﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    class ZTRoadGridArchiveQuery : QueryBase
    {
        public ZTRoadGridArchiveQuery()
            : base(MainModel.GetInstance())
        {
        }

        public override string Name
        {
            get { return "道路栅格档案库"; }
        }

        public override string IconName
        {
            get { return null; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 20000, 20055, this.Name);
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MainModel.NeedSearchType.None;
        }

        //汇总结果数据
        List<ZTRoadGridArchiveSumRes> roadSumInfoList;
        //数据核查表所有轮次表,天表,月表信息
        Dictionary<string, List<string>> dataVerifyDic;
        //查询条件
        readonly ZTRoadGridArchiveCondition settingCondition = new ZTRoadGridArchiveCondition();
        ZTRoadGridArchiveDlg settingDlg;
        //详细栅格路段数据
        List<ZTRoadGridArchiveRes> detailResList;

        //由于尝试动态添加字段感觉效果不好,暂时去掉加载配置字段
        ////问题事件配置列表
        //Dictionary<string, int> eventCountDic;
        ////网络原因配置列表
        //Dictionary<string, int> reasonCountDic;

        protected override bool isValidCondition()
        {
            return true;
        }

        protected virtual void clearDatas()
        {
            //
        }

        #region 设置条件
        protected virtual bool getCondition()
        {
            bool isValid = initCondition();
            if (!isValid)
            {
                return false;
            }

            Dictionary<string, int> roadInfoDic = getRoadInfoFromSumRes();
            if (settingDlg == null)
            {
                settingDlg = new ZTRoadGridArchiveDlg();
            }
            settingDlg.Init(settingCondition, roadInfoDic, dataVerifyDic);

            if (settingDlg.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                //根据所选道路重置sum
                resetRoadSumInfo();
                return true;
            }

            return false;
        }

        private bool initCondition()
        {
            try
            {
                //从汇总表查询出所有道路,先用于条件窗体加载查询条件
                DiyQueryZTRoadGridAreaSum querySum = new DiyQueryZTRoadGridAreaSum();
                querySum.Query();
                roadSumInfoList = querySum.ZTRoadGridArchiveSumResList;
                if (roadSumInfoList.Count == 0)
                {
                    System.Windows.Forms.MessageBox.Show("没有查询到有效的道路信息");
                    return false;
                }

                //查询已有的轮次表,天表,月表
                DiyQueryZTRoadGridDataVerify queryData = new DiyQueryZTRoadGridDataVerify();
                queryData.Query();
                queryData.SetConverageData();
                dataVerifyDic = queryData.ConverageDataVerifyDic;
                if (dataVerifyDic.Count == 0)
                {
                    System.Windows.Forms.MessageBox.Show("没有查询到有效的数据表信息");
                    return false;
                }
                
                //DiyQueryZTRoadGridProblem queryProblem = new DiyQueryZTRoadGridProblem();
                //queryProblem.Query();
                //if (queryProblem.ProblemList.Count == 0)
                //{
                //    System.Windows.Forms.MessageBox.Show("没有查询到有效的问题事件配置信息");
                //    return false;
                //}
                //List<string> problemList = queryProblem.ProblemList;
                //eventCountDic = initCountDic(problemList);

                //DiyQueryZTRoadGridNetReason queryNetReason = new DiyQueryZTRoadGridNetReason();
                //queryNetReason.Query();
                //if (queryNetReason.NetReasonList.Count == 0)
                //{
                //    System.Windows.Forms.MessageBox.Show("没有查询到有效的网络原因配置信息");
                //    return false;
                //}
                //List<string> netReasonList = queryNetReason.NetReasonList;
                //reasonCountDic = initCountDic(netReasonList);
            }
            catch (Exception e)
            {
                System.Windows.Forms.MessageBox.Show(string.Format("查询异常:{0}", e.StackTrace));
                return false;
            }
            return true;
        }

        //private Dictionary<string, int> initCountDic(List<string> dataList)
        //{
        //    Dictionary<string, int> countDic = new Dictionary<string, int>();
        //    foreach (var name in dataList)
        //    {
        //        if (!countDic.ContainsKey(name))
        //        {
        //            countDic.Add(name, 0);
        //        }
        //    }
        //    return countDic;
        //}

        private Dictionary<string, int> getRoadInfoFromSumRes()
        {
            Dictionary<string, int> roadInfoDic = new Dictionary<string, int>();
            foreach (var roadInfo in roadSumInfoList)
            {
                if (!roadInfoDic.ContainsKey(roadInfo.RoadName))
                {
                    roadInfoDic.Add(roadInfo.RoadName, roadInfo.RoadID);
                }
            }

            return roadInfoDic;
        }

        private void resetRoadSumInfo()
        {
            List<ZTRoadGridArchiveSumRes> roadSumTmp = new List<ZTRoadGridArchiveSumRes>();
            string[] roads = settingCondition.RoadConditionStr.Split(',');
            foreach (var road in roads)
            {
                foreach (var sumInfo in roadSumInfoList)
                {
                    if (sumInfo.RoadName == road.Trim())
                    {
                        roadSumTmp.Add(sumInfo);
                        break;
                    }
                }
            }
            roadSumInfoList = roadSumTmp;
        }
        #endregion

        protected override void query()
        {
            clearDatas();
            if (!getCondition())
            {
                return;
            }

            WaitBox.Show("正在查询道路栅格信息...", queryInThread);
            showResultForm();
        }

        private void queryInThread()
        {
            try
            {
                dealDetailRoadInfo();

                dealSumRoadInfo();
            }
            finally
            {
                WaitBox.Close();
            }
        }

        #region 详情数据
        private void dealDetailRoadInfo()
        {
            //查询道路栅格详情
            DiyQueryZTRoadGridAreaDetailList query = new DiyQueryZTRoadGridAreaDetailList();
            query.SetCondition(settingCondition);
            query.Query();
            detailResList = query.RoadGridArchiveResList;

            //查询采样点小区和事件
            List<StatCellRoadInfo> curStatCellRoadResList;
            List<StatCellRoadInfo> hisStatCellRoadResList;
            List<EventRoadInfo> curEventRoadResList;
            List<ReasonRoadInfo> curRoadReasonResList;
            if (settingCondition.TypeContiodion == ZTRoadGridArchiveCondition.Type.ByRound)
            {
                queryRoundData(out curStatCellRoadResList, out hisStatCellRoadResList, out curEventRoadResList, out curRoadReasonResList);
            }
            else
            {
                if (settingCondition.PeriodTypeContiodion == ZTRoadGridArchiveCondition.PeriodType.ByDay)
                {
                    queryDayData(out curStatCellRoadResList, out hisStatCellRoadResList, out curEventRoadResList, out curRoadReasonResList);
                }
                else
                {
                    queryMonthData(out curStatCellRoadResList, out hisStatCellRoadResList, out curEventRoadResList, out curRoadReasonResList);
                }
            }

            //查询路段对应的分公司
            DiyQueryZTRoadGridAreaInfo queryCountry = new DiyQueryZTRoadGridAreaInfo();
            queryCountry.Query();
            Dictionary<int, string> areaInfo = queryCountry.AreaInfo;

            //按路段汇聚结果
            Dictionary<int, List<StatCellRoadInfo>> curAreaStatCellDic = converageAreaStatCell(curStatCellRoadResList);
            Dictionary<int, List<StatCellRoadInfo>> hisAreaStatCellDic = converageAreaStatCell(hisStatCellRoadResList);
            Dictionary<int, EventConverageRes> curEventDic = converageEvent(curEventRoadResList);
            Dictionary<int, ReasonConverageRes> curReasonDic = converageReason(curRoadReasonResList);

            addConverageDetailInfo(areaInfo, curAreaStatCellDic, hisAreaStatCellDic, curEventDic, curReasonDic);
        }

        private void addConverageDetailInfo(Dictionary<int, string> areaInfo, 
            Dictionary<int, List<StatCellRoadInfo>> curAreaStatCellDic, Dictionary<int, List<StatCellRoadInfo>> hisAreaStatCellDic, 
            Dictionary<int, EventConverageRes> curEventDic, Dictionary<int, ReasonConverageRes> curReasonDic)
        {
            //将详情数据添加完整
            foreach (var res in detailResList)
            {
                if (curAreaStatCellDic.ContainsKey(res.AreaID))
                {
                    res.CurStatCellRoadInfo = curAreaStatCellDic[res.AreaID];
                    res.CurStatCellRoadInfo.Sort();
                }
                if (hisAreaStatCellDic.ContainsKey(res.AreaID))
                {
                    res.HisStatCellRoadInfo = hisAreaStatCellDic[res.AreaID];
                    res.HisStatCellRoadInfo.Sort();
                }
                res.SetSN();

                if (curEventDic.ContainsKey(res.AreaID))
                {
                    res.CurEventRoadInfo = curEventDic[res.AreaID];
                    res.CurEventRoadInfo.SetTotalEventNum();
                }

                if (curReasonDic.ContainsKey(res.AreaID))
                {
                    res.CurRoadReasonInfo = curReasonDic[res.AreaID];
                }

                if (areaInfo.ContainsKey(res.AreaID))
                {
                    res.CompanyName = areaInfo[res.AreaID];
                }
            }
        }

        private void queryRoundData(out List<StatCellRoadInfo> curStatCellRoadResList, out List<StatCellRoadInfo> hisStatCellRoadResList,
            out List<EventRoadInfo> curEventRoadResList, out List<ReasonRoadInfo> curRoadReasonResList)
        {
            DiyQueryStatCellRoadRound queryStat = new DiyQueryStatCellRoadRound();
            queryStat.SetCondition(settingCondition);
            queryStat.Query();
            curStatCellRoadResList = queryStat.CurStatCellRoadResList;
            hisStatCellRoadResList = queryStat.HisStatCellRoadResList;

            DiyQueryEventRoadRound queryEvt = new DiyQueryEventRoadRound();
            queryEvt.SetCondition(settingCondition);
            queryEvt.Query();
            curEventRoadResList = queryEvt.CurEventRoadResList;

            DiyQueryReasonRoadRound queryReason = new DiyQueryReasonRoadRound();
            queryReason.SetCondition(settingCondition);
            queryReason.Query();
            curRoadReasonResList = queryReason.CurRoadReasonResList;
        }

        private void queryDayData(out List<StatCellRoadInfo> curStatCellRoadResList, out List<StatCellRoadInfo> hisStatCellRoadResList,
            out List<EventRoadInfo> curEventRoadResList, out List<ReasonRoadInfo> curRoadReasonResList)
        {
            DiyQueryStatCellRoadDD queryStat = new DiyQueryStatCellRoadDD();
            queryStat.SetCondition(settingCondition);
            queryStat.Query();
            curStatCellRoadResList = queryStat.CurStatCellRoadResList;
            hisStatCellRoadResList = queryStat.HisStatCellRoadResList;

            DiyQueryEventRoadDD queryEvt = new DiyQueryEventRoadDD();
            queryEvt.SetCondition(settingCondition);
            queryEvt.Query();
            curEventRoadResList = queryEvt.CurEventRoadResList;

            DiyQueryReasonRoadDD queryReason = new DiyQueryReasonRoadDD();
            queryReason.SetCondition(settingCondition);
            queryReason.Query();
            curRoadReasonResList = queryReason.CurRoadReasonResList;
        }

        private void queryMonthData(out List<StatCellRoadInfo> curStatCellRoadResList, out List<StatCellRoadInfo> hisStatCellRoadResList,
            out List<EventRoadInfo> curEventRoadResList, out List<ReasonRoadInfo> curRoadReasonResList)
        {
            DiyQueryStatCellRoadMM queryStat = new DiyQueryStatCellRoadMM();
            queryStat.SetCondition(settingCondition);
            queryStat.Query();
            curStatCellRoadResList = queryStat.CurStatCellRoadResList;
            hisStatCellRoadResList = queryStat.HisStatCellRoadResList;

            DiyQueryEventRoadMM queryEvt = new DiyQueryEventRoadMM();
            queryEvt.SetCondition(settingCondition);
            queryEvt.Query();
            curEventRoadResList = queryEvt.CurEventRoadResList;

            DiyQueryReasonRoadMM queryReason = new DiyQueryReasonRoadMM();
            queryReason.SetCondition(settingCondition);
            queryReason.Query();
            curRoadReasonResList = queryReason.CurRoadReasonResList;
        }

        private Dictionary<int, List<StatCellRoadInfo>> converageAreaStatCell(List<StatCellRoadInfo> statCellRoadResList)
        {
            //路段,小区,信息
            Dictionary<int, Dictionary<string, StatCellRoadInfo>> areaStatCellDic = new Dictionary<int, Dictionary<string, StatCellRoadInfo>>();
            foreach (StatCellRoadInfo info in statCellRoadResList)
            {
                //添加路段
                Dictionary<string, StatCellRoadInfo> statCellDic;
                if (!areaStatCellDic.TryGetValue(info.AreaID, out statCellDic))
                {
                    statCellDic = new Dictionary<string, StatCellRoadInfo>();
                    areaStatCellDic.Add(info.AreaID, statCellDic);
                }
                //添加小区,信息
                StatCellRoadInfo tmpInfo;
                if (!statCellDic.TryGetValue(info.Token, out tmpInfo))
                {
                    tmpInfo = new StatCellRoadInfo();
                    statCellDic.Add(info.Token, info);
                }
                //累加小区占用的采样点
                tmpInfo.Times += info.Times;
            }

            Dictionary<int, List<StatCellRoadInfo>> res = new Dictionary<int, List<StatCellRoadInfo>>();
            foreach (var item in areaStatCellDic)
            {
                res.Add(item.Key, new List<StatCellRoadInfo>(item.Value.Values));
            }

            return res;
        }

        private Dictionary<int, EventConverageRes> converageEvent(List<EventRoadInfo> eventRoadResList)
        {
            Dictionary<int, EventConverageRes> eventInfoDic = new Dictionary<int, EventConverageRes>();
            foreach (EventRoadInfo info in eventRoadResList)
            {
                EventConverageRes res;
                if (!eventInfoDic.TryGetValue(info.AreaID, out res))
                {
                    res = new EventConverageRes(info);
                    eventInfoDic.Add(info.AreaID, res);
                }
                res.ConverageEvent(info);
            }

            return eventInfoDic;
        }

        private Dictionary<int, ReasonConverageRes> converageReason(List<ReasonRoadInfo> curRoadReasonResList)
        {
            Dictionary<int, ReasonConverageRes> reasonInfoDic = new Dictionary<int, ReasonConverageRes>();
            foreach (ReasonRoadInfo info in curRoadReasonResList)
            {
                ReasonConverageRes res;
                if (!reasonInfoDic.TryGetValue(info.AreaID, out res))
                {
                    res = new ReasonConverageRes();
                    reasonInfoDic.Add(info.AreaID, res);
                }
                res.ConverageReason(info);
            }

            return reasonInfoDic;
        }
        #endregion

        #region 汇总数据
        private void dealSumRoadInfo()
        {
            //累计道路占用小区数,异常事件数
            Dictionary<string, ZTRoadGridArchiveSumRes> tmpSumResDic = converageRoad();

            //设置汇总数据对应的轮次或时段
            foreach (var roadSumRes in roadSumInfoList)
            {
                converageSumInfo(tmpSumResDic, roadSumRes);

                setRoundOrDate(roadSumRes);
            }
        }

        private Dictionary<string, ZTRoadGridArchiveSumRes> converageRoad()
        {
            Dictionary<string, ZTRoadGridArchiveSumRes> tmpSumResDic = new Dictionary<string, ZTRoadGridArchiveSumRes>();
            foreach (var detailRes in detailResList)
            {
                //汇聚道路占用小区
                ZTRoadGridArchiveSumRes sumRes;
                if (!tmpSumResDic.TryGetValue(detailRes.RoadName, out sumRes))
                {
                    sumRes = new ZTRoadGridArchiveSumRes();
                    tmpSumResDic.Add(detailRes.RoadName, sumRes);
                }
                foreach (var road in detailRes.CurStatCellRoadInfo)
                {
                    //过滤重复的小区
                    if (!sumRes.StatCellRoadInfoDic.ContainsKey(road.Token))
                    {
                        sumRes.StatCellRoadInfoDic.Add(road.Token, road);
                    }
                }

                sumRes.EventRoadInfo.ConverageEventSum(detailRes.CurEventRoadInfo);
                sumRes.RoadReasonInfo.ConverageReasonSum(detailRes.CurRoadReasonInfo);
            }

            return tmpSumResDic;
        }

        private void converageSumInfo(Dictionary<string, ZTRoadGridArchiveSumRes> tmpSumResDic, ZTRoadGridArchiveSumRes roadSumRes)
        {
            if (tmpSumResDic.ContainsKey(roadSumRes.RoadName))
            {
                ZTRoadGridArchiveSumRes tmpRes = tmpSumResDic[roadSumRes.RoadName];
                roadSumRes.StatCellRoadInfoDic = tmpRes.StatCellRoadInfoDic;
                roadSumRes.EventRoadInfo = tmpRes.EventRoadInfo;
                roadSumRes.RoadReasonInfo = tmpRes.RoadReasonInfo;
            }
        }

        private void setRoundOrDate(ZTRoadGridArchiveSumRes roadSumRes)
        {
            if (settingCondition.TypeContiodion == ZTRoadGridArchiveCondition.Type.ByRound)
            {
                roadSumRes.Round = settingCondition.CurRoundConditionStr;
                roadSumRes.RoundCount = settingCondition.CurRoundCount;
            }
            else
            {
                if (settingCondition.PeriodTypeContiodion == ZTRoadGridArchiveCondition.PeriodType.ByDay)
                {
                    roadSumRes.StartDate = settingCondition.StartTimeByDay.ToString("yyyy年MM月dd日");
                    roadSumRes.EndDate = settingCondition.EndTimeByDay.ToString("yyyy年MM月dd日");
                }
                else
                {
                    roadSumRes.StartDate = settingCondition.StartTimeByMonth.ToString("yyyy年MM月");
                    roadSumRes.EndDate = settingCondition.EndTimeByMonth.ToString("yyyy年MM月");
                }
            }
        }
        #endregion

        protected virtual void showResultForm()
        {
            ZTRoadGridArchiveForm frm = mainModel.CreateResultForm(typeof(ZTRoadGridArchiveForm)) as ZTRoadGridArchiveForm;
            frm.FillData(settingCondition, detailResList, roadSumInfoList);
            frm.Visible = true;
            frm.BringToFront();
        }
    }
}
