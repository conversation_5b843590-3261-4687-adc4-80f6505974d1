﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Stat.Data;
using MasterCom.RAMS.Model.Interface;
using DBDataViewer;

namespace MasterCom.RAMS.CQT
{
    public class DIYQueryCQTKPIFine : DIYStatByLog
    {
        public static DIYCQTCellStatByFile DiyCQTCellStatByFile { get; set; }

        public List<CQTKPIFinePoint> cqtPoint2Stat { get; set; } = new List<CQTKPIFinePoint>();
        public static bool isStatByCell { get; set; } = true;
        public static bool isAutoExport { get; set; } = false;
        public static bool isStatImg { get; set; } = false;
        public static bool isStatCylindricity { get; set; } = false;
        public static string AutoExcelPath { get; set; } = "";
        public DIYQueryCQTKPIFine(MainModel mm)
            : base(mm)
        { }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 11000, 11050, this.Name);
        }
        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }
        public override string Description
        {
            get
            {
                return "全区域/框选区域 CQT地点文件KPI统计分析";
            }
        }
        public override string Name
        {
            get
            {
                return "CQT地点指标统计";
            }
        }
        public override MainModel.NeedSearchType needSearchType()
        {
            return MainModel.NeedSearchType.None;
        }

        private List<CQTKPIFinePoint> getFileCQTPoints()
        {
            Dictionary<string, List<CQTKPIFinePoint>> cqtFineDic = CQTKPIFineDataManager.GetInstance().AllPoints;
            if (cqtFineDic.Count == 0)
            {
                DIYSQLQueryCQTFinePoint cqtFinePointQuery = new DIYSQLQueryCQTFinePoint(MainModel.GetInstance());
                cqtFinePointQuery.Query();
            }
            DateTime sDtime = condition.Periods[0].BeginTime;
            DateTime eDtime = condition.Periods[0].EndTime;
            List<CQTKPIFinePoint> pnts = new List<CQTKPIFinePoint>();
            List<CQTKPIFinePoint> regionPnts = new List<CQTKPIFinePoint>();
            List<string> cqtFileList = new List<string>();
            List<string> logList = getLogFileNameList(sDtime, eDtime);
            List<int> selectCQTProjectList = condition.Projects;
            #region 获取有文件的主地点
#if GDProjectOrder
            List<int> cqtProjectList = new List<int>() { 4, 53, 54, 14, 57, 58, 59, 32, 301 };
            List<int> cqtProjectTmp = new List<int>();
            foreach (int iProject in selectCQTProjectList)
            {
                if (cqtProjectList.Contains(iProject))
                {
                    cqtProjectTmp.Add(iProject);
                }
            }
            condition.Projects = cqtProjectTmp;
#endif
            StringBuilder strbProjects = getStrbProjects();
            addCqtFileList(sDtime, eDtime, cqtFileList, logList, strbProjects);
            addPnts(cqtFineDic, pnts, cqtFileList);
            #endregion
            condition.Projects = selectCQTProjectList;
            if (condition.Geometorys != null && condition.Geometorys.IsSelectRegion())
            {
                foreach (CQTKPIFinePoint cqtpnt in pnts)
                {
                    if (condition.Geometorys.GeoOp.Contains(cqtpnt.Longitude, cqtpnt.Latitude))
                    {
                        regionPnts.Add(cqtpnt);
                    }
                }
                if (regionPnts.Count > 0)
                    return regionPnts;
            }
            return pnts;
        }

        private StringBuilder getStrbProjects()
        {
            StringBuilder strbProjects = new StringBuilder();
            for (int i = 0; i < condition.Projects.Count; i++)
            {
                if (i > 0)
                {
                    strbProjects.Append(",");
                }
                strbProjects.Append(condition.Projects[i]);
            }

            return strbProjects;
        }

        private void addCqtFileList(DateTime sDtime, DateTime eDtime, List<string> cqtFileList, List<string> logList, StringBuilder strbProjects)
        {
            foreach (string log in logList)
            {
                DIYQueryCQTFileInfo diyFileInfo = new DIYQueryCQTFileInfo(MainModel, log, -100
                    , strbProjects.ToString(), true, sDtime, eDtime);
                diyFileInfo.SetQueryCondition(CQTPointKPIFineData.qCondition);
                diyFileInfo.Query();//查询测试地点的文件列表
                foreach (FileInfoItem fileInfo in diyFileInfo.CQTFileDic.Values)
                {
                    string wyName = fileInfo.StrfileName.Split('_')[2];
                    if (!cqtFileList.Contains(wyName))
                    {
                        cqtFileList.Add(wyName);
                    }
                }
            }
        }

        private void addPnts(Dictionary<string, List<CQTKPIFinePoint>> cqtFineDic, List<CQTKPIFinePoint> pnts, List<string> cqtFileList)
        {
            foreach (string wyName in cqtFileList)
            {
                List<CQTKPIFinePoint> samePntFineList;
                if (cqtFineDic.TryGetValue(wyName, out samePntFineList) && samePntFineList.Count > 0)
                {
                    CQTKPIFinePoint pntFine = samePntFineList[0];
                    StringBuilder strbNetType = new StringBuilder();
                    foreach (CQTKPIFinePoint itemPntFine in samePntFineList)
                    {
                        strbNetType.Append(itemPntFine.NetType);
                    }
                    pntFine.NetType = strbNetType.ToString();
                    pnts.Add(pntFine);
                }
            }
        }

        /// <summary>
        /// 获取tb_log表名
        /// </summary>
        private List<string> getLogFileNameList(DateTime tmpDate, DateTime eDtime)
        {
            List<string> logList = new List<string>();
            while (tmpDate <= eDtime)
            {
                string strLogName = string.Format("tb_log_file_{0:yyyy}_{0:MM}", tmpDate);
                if (!logList.Contains(strLogName))
                {
                    logList.Add(strLogName);
                }
                tmpDate = tmpDate.AddDays(1);
            }
            return logList;
        }
        protected CQTKPIReport curSelReport = null;
        protected override void query()
        {
            CQTPointKPIFineData.mainModel = MainModel;
            CQTPointKPIFineData.qCondition = condition;

            CQTKPIFineDataManager.GetInstance().Clear();

            List<CQTKPIFinePoint> pnts = getFileCQTPoints();

            CQTKPIReportCfgManager rptMng = CQTKPIReportCfgManager.GetInstance();
            if (rptMng.Reports.Count == 0)
            {
                rptMng.LoadByDefault();
            }
            CQTKPIFineStatSettingDlg dlg = new CQTKPIFineStatSettingDlg();
            dlg.FillData(pnts, rptMng);
            dlg.ShowDialog();
            if (dlg.DialogResult != System.Windows.Forms.DialogResult.OK)
            {
                return;
            }

            cqtPoint2Stat = dlg.SelCQTPnts;
            curSelReport = dlg.SelReport;
            CQTKPIFineDataManager.GetInstance().CurStatPoints = cqtPoint2Stat;
            if (cqtPoint2Stat.Count == 0 || curSelReport == null)
            {
                return;
            }
            foreach (int DistrictID in condition.DistrictIDs)
            {
                ClientProxy clientProxy = new ClientProxy();
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    continue;
                }
                try
                {
                    WaitBox.Show("开始统计数据...", queryInThread, clientProxy);
                }
                finally
                {
                    clientProxy.Close();
                }
            }
            showResultForm();
            isAutoExport = false;
            CQTPointKPIFineData.mainModel = null;
        }

        private void replayFiles()
        {
            FileInfo[] filesArr = DTDataHeaderManager.GetInstance().GetFiles();
            List<FileInfo> files = new List<FileInfo>();
            foreach (FileInfo fi in filesArr)
            {
                foreach (CQTKPIFinePoint pnt in cqtPoint2Stat)
                {
                    if (fi.Name.IndexOf(pnt.Name) != -1)
                    {
                        files.Add(fi);
                        break;
                    }
                }
            }
            if (files.Count==0)
            {
                return;
            }
            QueryCondition condition = new QueryCondition();
            condition.QueryType = Condition.QueryType;
            condition.Geometorys = Condition.Geometorys;
            condition.FileInfos.AddRange(files);

            //WaitBox.Text = "";按文件统计
            if (isStatImg)
            {
                DIYReplayFileWithNoWaitBox query = new DIYReplayFileWithNoWaitBox(MainModel);
                query.FilterSampleByRegion = false;
                query.FilterEventByRegion = false;
                query.IncludeTestPoint = true;
                query.IncludeEvent = true;
                query.IncludeMessage = true;
                query.SetQueryCondition(condition);
                query.Query();
                isStatImg = false;
            }
        }

        protected virtual void showResultForm()
        {
            Dictionary<string, CQTKPIFinePoint> cqtPointStatDic = new Dictionary<string, CQTKPIFinePoint>();
            foreach (CQTKPIFinePoint point in cqtPoint2Stat)
            {
                if (!cqtPointStatDic.ContainsKey(point.Name))
                {
                    cqtPointStatDic[point.Name] = point;
                }
            }
            if (CQTKPIFineDataManager.GetInstance().DataCount==0)
            {
                System.Windows.Forms.MessageBox.Show("所选CQT地点无测试文件！");
                return;
            }
            CQTKPIFineReportForm frm = MainModel.GetObjectFromBlackboard(typeof(CQTKPIFineReportForm)) as CQTKPIFineReportForm;
            if (frm == null || frm.IsDisposed)
            {
                frm = new CQTKPIFineReportForm(MainModel);
            }
            frm.FillData(condition.Periods[0], CQTKPIFineDataManager.GetInstance(), curSelReport, cqtPointStatDic);
            frm.Owner = MainModel.MainForm;
            frm.Visible = true;
            frm.BringToFront();
            if (isAutoExport)
                frm.Export2Xls(AutoExcelPath);
        }

        private void queryInThread(object o)
        {
            ClientProxy clientProxy = (ClientProxy)o;
            Package package = clientProxy.Package;
            int idx = 1;
            WaitBox.CanCancel = true;
            WaitBox.Text = "开始查询...";
            foreach (TimePeriod period in condition.Periods)
            {
                int countTotal = cqtPoint2Stat.Count;
                foreach (CQTKPIFinePoint cqtPnt in cqtPoint2Stat)
                {
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                    WaitBox.Text = "统计(" + idx++ + "/" + countTotal + ") " + cqtPnt.Name + "数据...";
                    prepareStatPackage_ImgGrid(package, period, condition.IsByRound, cqtPnt.Name);
                    fillContentNeeded_ImgGrid(package);
                    clientProxy.Send();
                    recieveInfo_ImgGrid(clientProxy, period);
                    //event
                    prepareStatPackage_Event(package, period, condition.IsByRound, cqtPnt.Name);
                    fillContentNeeded_Event(package);
                    clientProxy.Send();
                    recieveInfo_Event(clientProxy, period);
                }
            }
            replayFiles();
            WaitBox.Close();
        }

        protected void prepareStatPackage_ImgGrid(Package package, TimePeriod period, bool byRound, string cqtPointName)
        {
            package.Command = Command.DIYSearch;
            package.SubCommand = SubCommand.Request;
            package.Content.Type = MasterCom.RAMS.Net.RequestType.REQTYPE_DIY_LOG_KPI;
            package.Content.PrepareAddParam();
            if (byRound)
            {
                AddDIYRound(package, condition.ByRoundYear, condition.ByRoundRound);
            }
            else
            {
                AddDIYPeriod(package, period);
            }
            AddDIYProject(package, condition.Projects);
            AddDIYService(package, condition.ServiceTypes);
            AddDIYCarrierType(package, condition.CarrierTypes);
            if (!condition.IsAllAgent)
            {
                AddDIYAgent(package, condition.AgentIds);
            }
            AddDIYFileName(package, cqtPointName);//按CQT地点模糊匹配文件
            AddDIYFileFilter(package, condition);
            
            AddDIYStatStatus(package);
            AddDIYAreaTypeAndID(package, condition.Areas);
            AddDIYEndOpFlag(package);
        }

        protected void AddDIYFileName(Package package, string fileNameLikeStr)
        {
            package.Content.AddParam((byte)OpOptionDef.StrLike);
            package.Content.AddParam("0,2,1");//filename
            package.Content.AddParam(fileNameLikeStr);
        }

        protected void AddDIYFileNameFilter(Package package, string totalfilterStr, int orNumCount, int nameFilterType, string cqtPointName)
        {
            if (totalfilterStr.Length == 0)
            {
                return;
            }
            package.Content.AddParam((byte)OpOptionDef.StrLike);
            StringBuilder sbFileTrid = new StringBuilder();
            for (int i = 0; i < orNumCount; i++)
            {
                if (nameFilterType == 1)
                {
                    sbFileTrid.Append("0,16,1");//filepath
                }
                else if (nameFilterType == 2)
                {
                    sbFileTrid.Append("0,33,1");//strdesc
                }
                else
                {
                    sbFileTrid.Append("0,2,1");//filename
                }
                if (i < orNumCount - 1)
                {
                    sbFileTrid.Append(",");
                }
            }
            package.Content.AddParam(sbFileTrid.ToString());
            package.Content.AddParam(totalfilterStr);
        }

        protected virtual void fillContentNeeded_ImgGrid(Package package)
        {
            package.Content.AddParam("-1,-1,-1");//查询所有指标
        }

        protected void prepareStatPackage_Event(Package package, TimePeriod period, bool byRound,string cqtPointName)
        {
            package.Command = Command.DIYSearch;
            package.SubCommand = SubCommand.Request;
            package.Content.Type = MasterCom.RAMS.Net.RequestType.REQTYPE_DIY_AREA_EVENT;
            package.Content.PrepareAddParam();
            if (byRound)
            {
                AddDIYRound(package, condition.ByRoundYear, condition.ByRoundRound);
            }
            else
            {
                AddDIYPeriod(package, period);
            }
            AddDIYProject(package, condition.Projects);
            AddDIYService(package, condition.ServiceTypes);
            if (!condition.IsAllAgent)
            {
                AddDIYAgent(package, condition.AgentIds);
            }
            AddDIYFileName(package, cqtPointName);
            AddDIYFileFilter(package, condition);
            AddDIYEndOpFlag(package);
            AddDIYEndOpFlag(package);
        }

        protected void recieveInfo_ImgGrid(ClientProxy clientProxy, TimePeriod period)
        {
            DTDataHeaderManager headerManager = DTDataHeaderManager.GetInstance();
            List<ColumnDefItem> fileHeaderColumnDef = new List<ColumnDefItem>();
            List<StatImgDefItem> curImgColumnDef = new List<StatImgDefItem>();
            int index = 0;
            Package package = clientProxy.Package;
            int progress = 25;
            WaitBox.ProgressPercent = progress;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                #region Read Stat Data
                bool isEnd = recievePackgeData(clientProxy, period, headerManager, fileHeaderColumnDef, curImgColumnDef, package);
                if (isEnd)
                {
                    break;
                }
                #endregion

                if (Math.Log(index++) * (100000 / 10000) > WaitBox.ProgressPercent)
                {
                    progress++;
                    if (progress > 95)
                    {
                        progress = 5;
                        index = 0;
                    }
                    if (progress % 5 == 0)
                    {
                        WaitBox.ProgressPercent = progress;
                    }
                }
            }
            WaitBox.ProgressPercent = 95;
        }

        private bool recievePackgeData(ClientProxy clientProxy, TimePeriod period, DTDataHeaderManager headerManager, List<ColumnDefItem> fileHeaderColumnDef, List<StatImgDefItem> curImgColumnDef, Package package)
        {
            if (isFileHeaderContentType(package.Content.Type))
            {
                recieveFileHeader(clientProxy.DbID, package.Content, fileHeaderColumnDef);
            }
            else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.COLUMN_DEFINE)
            {
                curImgColumnDef.Clear();
                string idpairs = package.Content.GetParamString();
                parseToCurImgColumnDef(idpairs, curImgColumnDef);
            }
            else if (setKPIStatData(period, headerManager, curImgColumnDef, package))
            {
                //setData
            }
            else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
            {
                return true;
            }
            else
            {
                log.Error("Unexpected type: " + package.Content.Type);
                return true;
            }

            return false;
        }

        private bool setKPIStatData(TimePeriod period, DTDataHeaderManager headerManager, List<StatImgDefItem> curImgColumnDef, Package package)
        {
            if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIY_LOG_KPI_GSM
                || package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIY_LOG_KPI_GPRS)
            {
                DataGSM_NewImg newImg = new DataGSM_NewImg();
                addKPIStatData(period, headerManager, curImgColumnDef, package, newImg);
            }
            else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIY_LOG_KPI_TDSCDMA_AMR
                || package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIY_LOG_KPI_TDSCDMA_PS
                || package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIY_LOG_KPI_TDSCDMA_VP)
            {
                DataTDSCDMA_NewImg newImg = new DataTDSCDMA_NewImg();
                addKPIStatData(period, headerManager, curImgColumnDef, package, newImg);
            }
            else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIY_LOG_KPI_WCDMA_AMR
                || package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIY_LOG_KPI_WCDMA_PS
                || package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIY_LOG_KPI_WCDMA_VP
                || package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIY_LOG_KPI_WCDMA_PSHS)
            {
                DataWCDMA_AMR newImg = new DataWCDMA_AMR();
                addKPIStatData(period, headerManager, curImgColumnDef, package, newImg);
            }
            else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIY_LOG_KPI_CDMA_V
                || package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIY_LOG_KPI_CDMA_D)
            {
                DataCDMA_Voice newImg = new DataCDMA_Voice();
                addKPIStatData(period, headerManager, curImgColumnDef, package, newImg);
            }
            else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIY_LOG_KPI_CDMA2000_D)
            {
                DataEVDO_Data newImg = new DataEVDO_Data();
                addKPIStatData(period, headerManager, curImgColumnDef, package, newImg);
            }
            else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIY_LOG_KPI_GSM_MTR)
            {
                DataMTR_GSM newImg = new DataMTR_GSM();
                addKPIStatData(period, headerManager, curImgColumnDef, package, newImg);
            }
            else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIY_LOG_KPI_SCAN_TD)
            {
                DataScan_TD newImg = new DataScan_TD();
                addKPIStatData(period, headerManager, curImgColumnDef, package, newImg);
            }
            else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIY_LOG_KPI_SCAN_GSM)
            {
                DataScan_GSM newImg = new DataScan_GSM();
                addKPIStatData(period, headerManager, curImgColumnDef, package, newImg);
            }
            else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIY_LOG_KPI_WLAN)
            {
                DataWLAN newImg = new DataWLAN();
                addKPIStatData(period, headerManager, curImgColumnDef, package, newImg);
            }
            else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIY_LOG_KPI_LTE_AMR)
            {
                DataLTE newImg = new DataLTE();
                addKPIStatData(period, headerManager, curImgColumnDef, package, newImg);
            }
            else
            {
                return false;
            }
            return true;
        }

        private void addKPIStatData(TimePeriod period, DTDataHeaderManager headerManager, List<StatImgDefItem> curImgColumnDef, Package package, PartialData newImg)
        {
            try
            {
                foreach (StatImgDefItem cdf in curImgColumnDef)
                {
                    byte[] imgBytes = package.Content.GetParamBytes();
                    Dictionary<string, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgBytes);
                    foreach (string str in cellStatInfoDic.Keys)
                    {
                        newImg.wInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                    }
                }
                DTDataHeader dataHeader = headerManager.GetHeaderByFileID((int)newImg.wInfoDic["0801"]);
                CQTKPIFineDataManager.GetInstance().AddKPIStatData(dataHeader, newImg, period);
            }
            catch
            {
                //continue
            }
        }

        protected virtual void recieveInfo_Event(ClientProxy clientProxy,TimePeriod period)
        {
            DTDataHeaderManager headerManager = DTDataHeaderManager.GetInstance();
            List<ColumnDefItem> fileHeaderColumnDef = new List<ColumnDefItem>();
            List<ColumnDefItem> curDefColumnDef = new List<ColumnDefItem>();
            int index = 0;
            Package package = clientProxy.Package;
            int progress = 0;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                #region Read Stat Data
                if (isFileHeaderContentType(package.Content.Type))
                {
                    DTDataHeader fileInfo = recieveFileHeader(clientProxy.DbID, package.Content, fileHeaderColumnDef);
                    if (fileInfo != null)
                    {
                        DTDataHeaderManager.GetInstance().AddDTDataHeader(fileInfo);
                    }
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.COLUMN_DEFINE)
                {
                    curDefColumnDef.Clear();
                    string idpairs = package.Content.GetParamString();
                    parseToCurColumnDef(idpairs, curDefColumnDef);
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIY_AREA_EVENT)
                {
                    DataEvent data = DataEvent.Create(package.Content, curDefColumnDef);
                    DTDataHeader dataHeader = headerManager.GetHeaderByFileID(data.filebase.fileId);
                    CQTKPIFineDataManager.GetInstance().AddKPIStatData(dataHeader, data, period);
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                #endregion
                setProgressPercent(ref index, ref progress);
            }
        }
    }
}
