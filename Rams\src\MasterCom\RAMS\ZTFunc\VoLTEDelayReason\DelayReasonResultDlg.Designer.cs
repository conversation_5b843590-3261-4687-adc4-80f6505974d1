﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class DelayReasonResultDlg
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            DevExpress.XtraCharts.Series series1 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.PieSeriesLabel pieSeriesLabel1 = new DevExpress.XtraCharts.PieSeriesLabel();
            DevExpress.XtraCharts.PiePointOptions piePointOptions1 = new DevExpress.XtraCharts.PiePointOptions();
            DevExpress.XtraCharts.PiePointOptions piePointOptions2 = new DevExpress.XtraCharts.PiePointOptions();
            DevExpress.XtraCharts.PieSeriesView pieSeriesView1 = new DevExpress.XtraCharts.PieSeriesView();
            DevExpress.XtraCharts.PieSeriesLabel pieSeriesLabel2 = new DevExpress.XtraCharts.PieSeriesLabel();
            DevExpress.XtraCharts.PieSeriesView pieSeriesView2 = new DevExpress.XtraCharts.PieSeriesView();
            DevExpress.XtraCharts.ChartTitle chartTitle1 = new DevExpress.XtraCharts.ChartTitle();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(DelayReasonResultDlg));
            DevExpress.XtraCharts.XYDiagram xyDiagram1 = new DevExpress.XtraCharts.XYDiagram();
            DevExpress.XtraCharts.Series series2 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel1 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.PointOptions pointOptions1 = new DevExpress.XtraCharts.PointOptions();
            DevExpress.XtraCharts.Series series3 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel2 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.PointOptions pointOptions2 = new DevExpress.XtraCharts.PointOptions();
            DevExpress.XtraCharts.Series series4 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel3 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.PointOptions pointOptions3 = new DevExpress.XtraCharts.PointOptions();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel4 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.ChartTitle chartTitle2 = new DevExpress.XtraCharts.ChartTitle();
            DevExpress.XtraCharts.Series series5 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.PieSeriesLabel pieSeriesLabel3 = new DevExpress.XtraCharts.PieSeriesLabel();
            DevExpress.XtraCharts.PiePointOptions piePointOptions3 = new DevExpress.XtraCharts.PiePointOptions();
            DevExpress.XtraCharts.PiePointOptions piePointOptions4 = new DevExpress.XtraCharts.PiePointOptions();
            DevExpress.XtraCharts.PieSeriesView pieSeriesView3 = new DevExpress.XtraCharts.PieSeriesView();
            DevExpress.XtraCharts.PieSeriesLabel pieSeriesLabel4 = new DevExpress.XtraCharts.PieSeriesLabel();
            DevExpress.XtraCharts.PieSeriesView pieSeriesView4 = new DevExpress.XtraCharts.PieSeriesView();
            DevExpress.XtraCharts.ChartTitle chartTitle3 = new DevExpress.XtraCharts.ChartTitle();
            DevExpress.XtraCharts.XYDiagram xyDiagram2 = new DevExpress.XtraCharts.XYDiagram();
            DevExpress.XtraCharts.Series series6 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel5 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.PointOptions pointOptions4 = new DevExpress.XtraCharts.PointOptions();
            DevExpress.XtraCharts.Series series7 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel6 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.PointOptions pointOptions5 = new DevExpress.XtraCharts.PointOptions();
            DevExpress.XtraCharts.Series series8 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel7 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.PointOptions pointOptions6 = new DevExpress.XtraCharts.PointOptions();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel8 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.ChartTitle chartTitle4 = new DevExpress.XtraCharts.ChartTitle();
            this.listViewMo = new BrightIdeasSoftware.TreeListView();
            this.errListViewMo = new BrightIdeasSoftware.TreeListView();
            this.olvColumnSN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.errOlvColumnSN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMoFileName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.errOlvColumnMoFileName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMtFileName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.errOlvColumnMtFileName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnDesc = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.errOlvColumnDesc = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBeginTime = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.errOlvColumnBeginTime = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnEndTime = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.errOlvColumnEndTime = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnSpanTime = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.errOlvColumnSpanTime = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNormal = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.errOlvColumnNormal = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnHandover = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.errOlvColumnHandover = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnTA = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.errOlvColumnTA = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCover = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.errOlvColumnCover = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnHeart = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.errOlvColumnHeart = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnOverNet = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.errOlvColumnOverNet = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnEnd = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.errOlvColumnEnd = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMtAnalyse = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.errOlvColumnMtAnalyse = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnConTime = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.errOlvColumnConTime = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.cStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.ExportItem = new System.Windows.Forms.ToolStripMenuItem();
            this.ReplayItem = new System.Windows.Forms.ToolStripMenuItem();
            this.ExpandItem = new System.Windows.Forms.ToolStripMenuItem();
            this.tabMo = new System.Windows.Forms.TabControl();
            this.tabPage3 = new System.Windows.Forms.TabPage();
            this.splitContainerControl1 = new DevExpress.XtraEditors.SplitContainerControl();
            this.splitContainerControl2 = new DevExpress.XtraEditors.SplitContainerControl();
            this.splitContainerControl4 = new DevExpress.XtraEditors.SplitContainerControl();
            this.chartControlReason = new DevExpress.XtraCharts.ChartControl();
            this.gCMo = new DevExpress.XtraGrid.GridControl();
            this.gVMo = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn9 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.pictureBox1 = new System.Windows.Forms.PictureBox();
            this.chartControlMo = new DevExpress.XtraCharts.ChartControl();
            this.splitContainerControl3 = new DevExpress.XtraEditors.SplitContainerControl();
            this.splitContainerControl5 = new DevExpress.XtraEditors.SplitContainerControl();
            this.chartControlMtReason = new DevExpress.XtraCharts.ChartControl();
            this.gCMt = new DevExpress.XtraGrid.GridControl();
            this.gVMt = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn10 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn6 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn7 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn8 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.pictureBox2 = new System.Windows.Forms.PictureBox();
            this.chartControlMt = new DevExpress.XtraCharts.ChartControl();
            this.tabPage1 = new System.Windows.Forms.TabPage();
            this.tabPage4 = new System.Windows.Forms.TabPage();
            this.tabPage2 = new System.Windows.Forms.TabPage();
            this.tabPage5 = new System.Windows.Forms.TabPage();
            this.listViewMt = new BrightIdeasSoftware.TreeListView();
            this.errListViewMt = new BrightIdeasSoftware.TreeListView();
            this.olvColumnMtSN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.errOlvColumnMtSN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMtMoFileName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.errOlvColumnMtMoFileName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMtMtFileName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.errOlvColumnMtMtFileName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMtDesc = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.errOlvColumnMtDesc = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMtBeginTIme = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.errOlvColumnMtBeginTIme = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMtEndTime = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.errOlvColumnMtEndTime = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMtSpanTime = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.errOlvColumnMtSpanTime = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMtNormal = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.errOlvColumnMtNormal = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMtHandover = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.errOlvColumnMtHandover = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMtTA = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.errOlvColumnMtTA = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMtCover = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.errOlvColumnMtCover = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMtHeart = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.errOlvColumnMtHeart = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMtEnd = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.errOlvColumnMtEnd = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMtMoAnalyse = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.errOlvColumnMtMoAnalyse = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMtConTime = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.errOlvColumnMtConTime = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            ((System.ComponentModel.ISupportInitialize)(this.listViewMo)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.errListViewMo)).BeginInit();
            this.cStrip.SuspendLayout();
            this.tabMo.SuspendLayout();
            this.tabPage3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).BeginInit();
            this.splitContainerControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl2)).BeginInit();
            this.splitContainerControl2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl4)).BeginInit();
            this.splitContainerControl4.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlReason)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesLabel1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesLabel2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesView2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gCMo)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gVMo)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlMo)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl3)).BeginInit();
            this.splitContainerControl3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl5)).BeginInit();
            this.splitContainerControl5.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlMtReason)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesLabel3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesView3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesLabel4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesView4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gCMt)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gVMt)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlMt)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series6)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series7)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel6)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series8)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel7)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel8)).BeginInit();
            this.tabPage1.SuspendLayout();
            this.tabPage4.SuspendLayout();
            this.tabPage2.SuspendLayout();
            this.tabPage5.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.listViewMt)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.errListViewMt)).BeginInit();
            this.SuspendLayout();
            // 
            // listViewMo
            // 
            this.listViewMo.AllColumns.Add(this.olvColumnSN);
            this.listViewMo.AllColumns.Add(this.olvColumnMoFileName);
            this.listViewMo.AllColumns.Add(this.olvColumnMtFileName);
            this.listViewMo.AllColumns.Add(this.olvColumnDesc);
            this.listViewMo.AllColumns.Add(this.olvColumnBeginTime);
            this.listViewMo.AllColumns.Add(this.olvColumnEndTime);
            this.listViewMo.AllColumns.Add(this.olvColumnSpanTime);
            this.listViewMo.AllColumns.Add(this.olvColumnConTime);
            this.listViewMo.AllColumns.Add(this.olvColumnNormal);
            this.listViewMo.AllColumns.Add(this.olvColumnHandover);
            this.listViewMo.AllColumns.Add(this.olvColumnTA);
            this.listViewMo.AllColumns.Add(this.olvColumnCover);
            this.listViewMo.AllColumns.Add(this.olvColumnHeart);
            this.listViewMo.AllColumns.Add(this.olvColumnOverNet);
            this.listViewMo.AllColumns.Add(this.olvColumnEnd);
            this.listViewMo.AllColumns.Add(this.olvColumnMtAnalyse);
            this.listViewMo.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnSN,
            this.olvColumnMoFileName,
            this.olvColumnMtFileName,
            this.olvColumnDesc,
            this.olvColumnBeginTime,
            this.olvColumnEndTime,
            this.olvColumnSpanTime,
            this.olvColumnConTime,
            this.olvColumnNormal,
            this.olvColumnHandover,
            this.olvColumnTA,
            this.olvColumnCover,
            this.olvColumnHeart,
            this.olvColumnOverNet,
            this.olvColumnEnd,
            this.olvColumnMtAnalyse});
            this.listViewMo.ContextMenuStrip = this.cStrip;
            this.listViewMo.Cursor = System.Windows.Forms.Cursors.Default;
            this.listViewMo.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listViewMo.FullRowSelect = true;
            this.listViewMo.GridLines = true;
            this.listViewMo.HeaderWordWrap = true;
            this.listViewMo.IsNeedShowOverlay = false;
            this.listViewMo.Location = new System.Drawing.Point(3, 3);
            this.listViewMo.Name = "listViewMo";
            this.listViewMo.OwnerDraw = true;
            this.listViewMo.ShowGroups = false;
            this.listViewMo.Size = new System.Drawing.Size(1400, 539);
            this.listViewMo.TabIndex = 12;
            this.listViewMo.UseCompatibleStateImageBehavior = false;
            this.listViewMo.View = System.Windows.Forms.View.Details;
            this.listViewMo.VirtualMode = true;
            // 
            // errListViewMo
            // 
            this.errListViewMo.AllColumns.Add(this.errOlvColumnSN);
            this.errListViewMo.AllColumns.Add(this.errOlvColumnMoFileName);
            this.errListViewMo.AllColumns.Add(this.errOlvColumnMtFileName);
            this.errListViewMo.AllColumns.Add(this.errOlvColumnDesc);
            this.errListViewMo.AllColumns.Add(this.errOlvColumnBeginTime);
            this.errListViewMo.AllColumns.Add(this.errOlvColumnEndTime);
            this.errListViewMo.AllColumns.Add(this.errOlvColumnSpanTime);
            this.errListViewMo.AllColumns.Add(this.errOlvColumnConTime);
            this.errListViewMo.AllColumns.Add(this.errOlvColumnNormal);
            this.errListViewMo.AllColumns.Add(this.errOlvColumnHandover);
            this.errListViewMo.AllColumns.Add(this.errOlvColumnTA);
            this.errListViewMo.AllColumns.Add(this.errOlvColumnCover);
            this.errListViewMo.AllColumns.Add(this.errOlvColumnHeart);
            this.errListViewMo.AllColumns.Add(this.errOlvColumnOverNet);
            this.errListViewMo.AllColumns.Add(this.errOlvColumnEnd);
            this.errListViewMo.AllColumns.Add(this.errOlvColumnMtAnalyse);
            this.errListViewMo.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.errOlvColumnSN,
            this.errOlvColumnMoFileName,
            this.errOlvColumnMtFileName,
            this.errOlvColumnDesc,
            this.errOlvColumnBeginTime,
            this.errOlvColumnEndTime,
            this.errOlvColumnSpanTime,
            this.errOlvColumnConTime,
            this.errOlvColumnNormal,
            this.errOlvColumnHandover,
            this.errOlvColumnTA,
            this.errOlvColumnCover,
            this.errOlvColumnHeart,
            this.errOlvColumnOverNet,
            this.errOlvColumnEnd,
            this.errOlvColumnMtAnalyse});
            this.errListViewMo.ContextMenuStrip = this.cStrip;
            this.errListViewMo.Cursor = System.Windows.Forms.Cursors.Default;
            this.errListViewMo.Dock = System.Windows.Forms.DockStyle.Fill;
            this.errListViewMo.FullRowSelect = true;
            this.errListViewMo.GridLines = true;
            this.errListViewMo.HeaderWordWrap = true;
            this.errListViewMo.IsNeedShowOverlay = false;
            this.errListViewMo.Location = new System.Drawing.Point(3, 3);
            this.errListViewMo.Name = "errListViewMo";
            this.errListViewMo.OwnerDraw = true;
            this.errListViewMo.ShowGroups = false;
            this.errListViewMo.Size = new System.Drawing.Size(1400, 539);
            this.errListViewMo.TabIndex = 12;
            this.errListViewMo.UseCompatibleStateImageBehavior = false;
            this.errListViewMo.View = System.Windows.Forms.View.Details;
            this.errListViewMo.VirtualMode = true;
            // 
            // olvColumnSN
            // 
            this.olvColumnSN.HeaderFont = null;
            this.olvColumnSN.Text = "序号";
            // 
            // errOlvColumnSN
            // 
            this.errOlvColumnSN.HeaderFont = null;
            this.errOlvColumnSN.Text = "序号";
            // 
            // olvColumnMoFileName
            // 
            this.olvColumnMoFileName.HeaderFont = null;
            this.olvColumnMoFileName.Text = "主叫文件";
            this.olvColumnMoFileName.Width = 180;
            // 
            // errOlvColumnMoFileName
            // 
            this.errOlvColumnMoFileName.HeaderFont = null;
            this.errOlvColumnMoFileName.Text = "主叫文件";
            this.errOlvColumnMoFileName.Width = 180;
            // 
            // olvColumnMtFileName
            // 
            this.olvColumnMtFileName.HeaderFont = null;
            this.olvColumnMtFileName.Text = "被叫文件";
            this.olvColumnMtFileName.Width = 180;
            // 
            // errOlvColumnMtFileName
            // 
            this.errOlvColumnMtFileName.HeaderFont = null;
            this.errOlvColumnMtFileName.Text = "被叫文件";
            this.errOlvColumnMtFileName.Width = 180;
            // 
            // olvColumnDesc
            // 
            this.olvColumnDesc.HeaderFont = null;
            this.olvColumnDesc.Text = "信令切换";
            this.olvColumnDesc.Width = 180;
            // 
            // errOlvColumnDesc
            // 
            this.errOlvColumnDesc.HeaderFont = null;
            this.errOlvColumnDesc.Text = "信令切换";
            this.errOlvColumnDesc.Width = 180;
            // 
            // olvColumnBeginTime
            // 
            this.olvColumnBeginTime.HeaderFont = null;
            this.olvColumnBeginTime.Text = "开始时间";
            this.olvColumnBeginTime.Width = 100;
            // 
            // errOlvColumnBeginTime
            // 
            this.errOlvColumnBeginTime.HeaderFont = null;
            this.errOlvColumnBeginTime.Text = "开始时间";
            this.errOlvColumnBeginTime.Width = 100;
            // 
            // olvColumnEndTime
            // 
            this.olvColumnEndTime.HeaderFont = null;
            this.olvColumnEndTime.Text = "结束时间";
            this.olvColumnEndTime.Width = 100;
            // 
            // errOlvColumnEndTime
            // 
            this.errOlvColumnEndTime.HeaderFont = null;
            this.errOlvColumnEndTime.Text = "结束时间";
            this.errOlvColumnEndTime.Width = 100;
            // 
            // olvColumnSpanTime
            // 
            this.olvColumnSpanTime.HeaderFont = null;
            this.olvColumnSpanTime.Text = "时长间隔（毫秒）";
            // 
            // errOlvColumnSpanTime
            // 
            this.errOlvColumnSpanTime.HeaderFont = null;
            this.errOlvColumnSpanTime.Text = "时长间隔（毫秒）";
            // 
            // olvColumnNormal
            // 
            this.olvColumnNormal.HeaderFont = null;
            this.olvColumnNormal.Text = "是否正常";
            // 
            // errOlvColumnNormal
            // 
            this.errOlvColumnNormal.HeaderFont = null;
            this.errOlvColumnNormal.Text = "是否正常";
            // 
            // olvColumnHandover
            // 
            this.olvColumnHandover.HeaderFont = null;
            this.olvColumnHandover.Text = "是否伴随切换";
            // 
            // errOlvColumnHandover
            // 
            this.errOlvColumnHandover.HeaderFont = null;
            this.errOlvColumnHandover.Text = "是否伴随切换";
            // 
            // olvColumnTA
            // 
            this.olvColumnTA.HeaderFont = null;
            this.olvColumnTA.Text = "是否伴随TA更新";
            // 
            // errOlvColumnTA
            // 
            this.errOlvColumnTA.HeaderFont = null;
            this.errOlvColumnTA.Text = "是否伴随TA更新";
            // 
            // olvColumnCover
            // 
            this.olvColumnCover.HeaderFont = null;
            this.olvColumnCover.Text = "是否伴随覆盖干扰";
            // 
            // errOlvColumnCover
            // 
            this.errOlvColumnCover.HeaderFont = null;
            this.errOlvColumnCover.Text = "是否伴随覆盖干扰";
            // 
            // olvColumnHeart
            // 
            this.olvColumnHeart.HeaderFont = null;
            this.olvColumnHeart.Text = "是否进行核心网排查";
            // 
            // errOlvColumnHeart
            // 
            this.errOlvColumnHeart.HeaderFont = null;
            this.errOlvColumnHeart.Text = "是否进行核心网排查";
            // 
            // olvColumnOverNet
            // 
            this.olvColumnOverNet.HeaderFont = null;
            this.olvColumnOverNet.Text = "是否跨网消息转发";
            // 
            // errOlvColumnOverNet
            // 
            this.errOlvColumnOverNet.HeaderFont = null;
            this.errOlvColumnOverNet.Text = "是否跨网消息转发";
            // 
            // olvColumnEnd
            // 
            this.olvColumnEnd.HeaderFont = null;
            this.olvColumnEnd.Text = "是否进行终端排查";
            // 
            // errOlvColumnEnd
            // 
            this.errOlvColumnEnd.HeaderFont = null;
            this.errOlvColumnEnd.Text = "是否进行终端排查";
            // 
            // olvColumnMtAnalyse
            // 
            this.olvColumnMtAnalyse.HeaderFont = null;
            this.olvColumnMtAnalyse.Text = "是否进行被叫测分析";
            // 
            // errOlvColumnMtAnalyse
            // 
            this.errOlvColumnMtAnalyse.HeaderFont = null;
            this.errOlvColumnMtAnalyse.Text = "是否进行被叫测分析";
            // 
            // cStrip
            // 
            this.cStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.ExportItem,
            this.ReplayItem,
            this.ExpandItem});
            this.cStrip.Name = "cStrip";
            this.cStrip.Size = new System.Drawing.Size(149, 70);
            // 
            // ExportItem
            // 
            this.ExportItem.Name = "ExportItem";
            this.ExportItem.Size = new System.Drawing.Size(148, 22);
            this.ExportItem.Text = "导出到Excel";
            this.ExportItem.Click += new System.EventHandler(this.ExportItem_Click);
            // 
            // ReplayItem
            // 
            this.ReplayItem.Name = "ReplayItem";
            this.ReplayItem.Size = new System.Drawing.Size(148, 22);
            this.ReplayItem.Text = "回放文件";
            this.ReplayItem.Click += new System.EventHandler(this.ReplayItem_Click);
            // 
            // ExpandItem
            // 
            this.ExpandItem.Name = "ExpandItem";
            this.ExpandItem.Size = new System.Drawing.Size(148, 22);
            this.ExpandItem.Text = "展开所有节点";
            this.ExpandItem.Click += new System.EventHandler(this.ExpandItem_Click);
            // 
            // tabMo
            // 
            this.tabMo.Controls.Add(this.tabPage3);
            this.tabMo.Controls.Add(this.tabPage1);
            this.tabMo.Controls.Add(this.tabPage2);
            this.tabMo.Controls.Add(this.tabPage4);
            this.tabMo.Controls.Add(this.tabPage5);
            this.tabMo.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabMo.Location = new System.Drawing.Point(0, 0);
            this.tabMo.Name = "tabMo";
            this.tabMo.SelectedIndex = 0;
            this.tabMo.Size = new System.Drawing.Size(1414, 572);
            this.tabMo.TabIndex = 13;
            // 
            // tabPage3
            // 
            this.tabPage3.Controls.Add(this.splitContainerControl1);
            this.tabPage3.Location = new System.Drawing.Point(4, 23);
            this.tabPage3.Name = "tabPage3";
            this.tabPage3.Size = new System.Drawing.Size(1406, 545);
            this.tabPage3.TabIndex = 2;
            this.tabPage3.Text = "汇总";
            this.tabPage3.UseVisualStyleBackColor = true;
            // 
            // splitContainerControl1
            // 
            this.splitContainerControl1.FixedPanel = DevExpress.XtraEditors.SplitFixedPanel.None;
            this.splitContainerControl1.Horizontal = false;
            this.splitContainerControl1.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl1.Name = "splitContainerControl1";
            this.splitContainerControl1.Panel1.Controls.Add(this.splitContainerControl2);
            this.splitContainerControl1.Panel1.Text = "Panel1";
            this.splitContainerControl1.Panel2.Controls.Add(this.splitContainerControl3);
            this.splitContainerControl1.Panel2.Text = "Panel2";
            this.splitContainerControl1.Size = new System.Drawing.Size(1403, 537);
            this.splitContainerControl1.SplitterPosition = 269;
            this.splitContainerControl1.TabIndex = 10;
            this.splitContainerControl1.Text = "splitContainerControl1";
            // 
            // splitContainerControl2
            // 
            this.splitContainerControl2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl2.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl2.Name = "splitContainerControl2";
            this.splitContainerControl2.Panel1.Controls.Add(this.splitContainerControl4);
            this.splitContainerControl2.Panel1.Text = "Panel1";
            this.splitContainerControl2.Panel2.Controls.Add(this.pictureBox1);
            this.splitContainerControl2.Panel2.Controls.Add(this.chartControlMo);
            this.splitContainerControl2.Panel2.Text = "Panel2";
            this.splitContainerControl2.Size = new System.Drawing.Size(1403, 269);
            this.splitContainerControl2.SplitterPosition = 653;
            this.splitContainerControl2.TabIndex = 0;
            this.splitContainerControl2.Text = "splitContainerControl2";
            // 
            // splitContainerControl4
            // 
            this.splitContainerControl4.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl4.FixedPanel = DevExpress.XtraEditors.SplitFixedPanel.None;
            this.splitContainerControl4.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl4.Name = "splitContainerControl4";
            this.splitContainerControl4.Panel1.Controls.Add(this.chartControlReason);
            this.splitContainerControl4.Panel1.Text = "Panel1";
            this.splitContainerControl4.Panel2.Controls.Add(this.gCMo);
            this.splitContainerControl4.Panel2.Text = "Panel2";
            this.splitContainerControl4.Size = new System.Drawing.Size(653, 269);
            this.splitContainerControl4.SplitterPosition = 326;
            this.splitContainerControl4.TabIndex = 2;
            this.splitContainerControl4.Text = "splitContainerControl4";
            // 
            // chartControlReason
            // 
            this.chartControlReason.Dock = System.Windows.Forms.DockStyle.Fill;
            this.chartControlReason.Location = new System.Drawing.Point(0, 0);
            this.chartControlReason.Name = "chartControlReason";
            this.chartControlReason.RuntimeSelection = true;
            this.chartControlReason.RuntimeSeriesSelectionMode = DevExpress.XtraCharts.SeriesSelectionMode.Point;
            pieSeriesLabel1.Border.Visible = false;
            pieSeriesLabel1.LineVisible = true;
            series1.Label = pieSeriesLabel1;
            piePointOptions1.PointView = DevExpress.XtraCharts.PointView.Argument;
            series1.LegendPointOptions = piePointOptions1;
            series1.Name = "Series1";
            piePointOptions2.PercentOptions.PercentageAccuracy = 4;
            piePointOptions2.ValueNumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
            series1.PointOptions = piePointOptions2;
            series1.SynchronizePointOptions = false;
            pieSeriesView1.RuntimeExploding = false;
            series1.View = pieSeriesView1;
            this.chartControlReason.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
            series1};
            pieSeriesLabel2.LineVisible = true;
            this.chartControlReason.SeriesTemplate.Label = pieSeriesLabel2;
            pieSeriesView2.RuntimeExploding = false;
            this.chartControlReason.SeriesTemplate.View = pieSeriesView2;
            this.chartControlReason.Size = new System.Drawing.Size(326, 269);
            this.chartControlReason.TabIndex = 1;
            chartTitle1.Text = "主叫原因占比";
            this.chartControlReason.Titles.AddRange(new DevExpress.XtraCharts.ChartTitle[] {
            chartTitle1});
            // 
            // gCMo
            // 
            this.gCMo.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gCMo.Location = new System.Drawing.Point(0, 0);
            this.gCMo.MainView = this.gVMo;
            this.gCMo.Name = "gCMo";
            this.gCMo.Size = new System.Drawing.Size(321, 269);
            this.gCMo.TabIndex = 0;
            this.gCMo.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gVMo});
            this.gCMo.DoubleClick += new System.EventHandler(this.gCMo_DoubleClick);
            // 
            // gVMo
            // 
            this.gVMo.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn9,
            this.gridColumn1,
            this.gridColumn2,
            this.gridColumn3,
            this.gridColumn4});
            this.gVMo.GridControl = this.gCMo;
            this.gVMo.Name = "gVMo";
            this.gVMo.OptionsBehavior.Editable = false;
            this.gVMo.OptionsView.ColumnAutoWidth = false;
            // 
            // gridColumn9
            // 
            this.gridColumn9.Caption = "节点";
            this.gridColumn9.FieldName = "ShortName";
            this.gridColumn9.Name = "gridColumn9";
            this.gridColumn9.Visible = true;
            this.gridColumn9.VisibleIndex = 0;
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "主叫信令切换";
            this.gridColumn1.FieldName = "Desc";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.Visible = true;
            this.gridColumn1.VisibleIndex = 1;
            // 
            // gridColumn2
            // 
            this.gridColumn2.Caption = "最大值";
            this.gridColumn2.FieldName = "Max";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.Visible = true;
            this.gridColumn2.VisibleIndex = 2;
            // 
            // gridColumn3
            // 
            this.gridColumn3.Caption = "平均值";
            this.gridColumn3.FieldName = "Avg";
            this.gridColumn3.Name = "gridColumn3";
            this.gridColumn3.Visible = true;
            this.gridColumn3.VisibleIndex = 3;
            // 
            // gridColumn4
            // 
            this.gridColumn4.Caption = "最小值";
            this.gridColumn4.FieldName = "Min";
            this.gridColumn4.Name = "gridColumn4";
            this.gridColumn4.Visible = true;
            this.gridColumn4.VisibleIndex = 4;
            // 
            // pictureBox1
            // 
            this.pictureBox1.Cursor = System.Windows.Forms.Cursors.Hand;
            this.pictureBox1.Image = ((System.Drawing.Image)(resources.GetObject("pictureBox1.Image")));
            this.pictureBox1.Location = new System.Drawing.Point(6, 3);
            this.pictureBox1.Name = "pictureBox1";
            this.pictureBox1.Size = new System.Drawing.Size(32, 29);
            this.pictureBox1.SizeMode = System.Windows.Forms.PictureBoxSizeMode.StretchImage;
            this.pictureBox1.TabIndex = 14;
            this.pictureBox1.TabStop = false;
            this.pictureBox1.Visible = false;
            this.pictureBox1.Click += new System.EventHandler(this.pictureBox1_Click);
            // 
            // chartControlMo
            // 
            xyDiagram1.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram1.AxisX.Range.SideMarginsEnabled = true;
            xyDiagram1.AxisX.VisibleInPanesSerializable = "-1";
            xyDiagram1.AxisY.AutoScaleBreaks.Enabled = true;
            xyDiagram1.AxisY.AutoScaleBreaks.MaxCount = 4;
            xyDiagram1.AxisY.NumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Number;
            xyDiagram1.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram1.AxisY.Range.SideMarginsEnabled = true;
            xyDiagram1.AxisY.ScaleBreakOptions.SizeInPixels = 1;
            xyDiagram1.AxisY.Title.Font = new System.Drawing.Font("Tahoma", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            xyDiagram1.AxisY.Title.Text = "单位：秒";
            xyDiagram1.AxisY.Title.Visible = true;
            xyDiagram1.AxisY.VisibleInPanesSerializable = "-1";
            this.chartControlMo.Diagram = xyDiagram1;
            this.chartControlMo.Dock = System.Windows.Forms.DockStyle.Fill;
            this.chartControlMo.Location = new System.Drawing.Point(0, 0);
            this.chartControlMo.Name = "chartControlMo";
            this.chartControlMo.PaletteName = "Module";
            sideBySideBarSeriesLabel1.LineVisible = true;
            series2.Label = sideBySideBarSeriesLabel1;
            series2.Name = "最大值";
            pointOptions1.ArgumentNumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
            pointOptions1.ValueNumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
            series2.PointOptions = pointOptions1;
            sideBySideBarSeriesLabel2.LineVisible = true;
            series3.Label = sideBySideBarSeriesLabel2;
            series3.Name = "平均值";
            pointOptions2.ValueNumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
            series3.PointOptions = pointOptions2;
            sideBySideBarSeriesLabel3.LineVisible = true;
            series4.Label = sideBySideBarSeriesLabel3;
            series4.Name = "最小值";
            pointOptions3.ValueNumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
            series4.PointOptions = pointOptions3;
            this.chartControlMo.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
            series2,series3,series4};
            sideBySideBarSeriesLabel4.LineVisible = true;
            this.chartControlMo.SeriesTemplate.Label = sideBySideBarSeriesLabel4;
            this.chartControlMo.Size = new System.Drawing.Size(744, 269);
            this.chartControlMo.TabIndex = 9;
            chartTitle2.Font = new System.Drawing.Font("Tahoma", 10F);
            chartTitle2.Text = "主叫信令切换时长统计";
            this.chartControlMo.Titles.AddRange(new DevExpress.XtraCharts.ChartTitle[] {
            chartTitle2});
            // 
            // splitContainerControl3
            // 
            this.splitContainerControl3.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl3.FixedPanel = DevExpress.XtraEditors.SplitFixedPanel.None;
            this.splitContainerControl3.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl3.Name = "splitContainerControl3";
            this.splitContainerControl3.Panel1.Controls.Add(this.splitContainerControl5);
            this.splitContainerControl3.Panel1.Text = "Panel1";
            this.splitContainerControl3.Panel2.Controls.Add(this.pictureBox2);
            this.splitContainerControl3.Panel2.Controls.Add(this.chartControlMt);
            this.splitContainerControl3.Panel2.Text = "Panel2";
            this.splitContainerControl3.Size = new System.Drawing.Size(1403, 262);
            this.splitContainerControl3.SplitterPosition = 653;
            this.splitContainerControl3.TabIndex = 0;
            this.splitContainerControl3.Text = "splitContainerControl3";
            // 
            // splitContainerControl5
            // 
            this.splitContainerControl5.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl5.FixedPanel = DevExpress.XtraEditors.SplitFixedPanel.None;
            this.splitContainerControl5.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl5.Name = "splitContainerControl5";
            this.splitContainerControl5.Panel1.Controls.Add(this.chartControlMtReason);
            this.splitContainerControl5.Panel1.Text = "Panel1";
            this.splitContainerControl5.Panel2.Controls.Add(this.gCMt);
            this.splitContainerControl5.Panel2.Text = "Panel2";
            this.splitContainerControl5.Size = new System.Drawing.Size(653, 262);
            this.splitContainerControl5.SplitterPosition = 326;
            this.splitContainerControl5.TabIndex = 0;
            this.splitContainerControl5.Text = "splitContainerControl5";
            // 
            // chartControlMtReason
            // 
            this.chartControlMtReason.Dock = System.Windows.Forms.DockStyle.Fill;
            this.chartControlMtReason.Location = new System.Drawing.Point(0, 0);
            this.chartControlMtReason.Name = "chartControlMtReason";
            this.chartControlMtReason.RuntimeSelection = true;
            this.chartControlMtReason.RuntimeSeriesSelectionMode = DevExpress.XtraCharts.SeriesSelectionMode.Point;
            pieSeriesLabel3.Border.Visible = false;
            pieSeriesLabel3.LineVisible = true;
            series5.Label = pieSeriesLabel3;
            piePointOptions3.PointView = DevExpress.XtraCharts.PointView.Argument;
            series5.LegendPointOptions = piePointOptions3;
            series5.Name = "Series 1";
            piePointOptions4.PercentOptions.PercentageAccuracy = 4;
            piePointOptions4.ValueNumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
            series5.PointOptions = piePointOptions4;
            series5.SynchronizePointOptions = false;
            pieSeriesView3.RuntimeExploding = false;
            series5.View = pieSeriesView3;
            this.chartControlMtReason.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
            series5};
            pieSeriesLabel4.LineVisible = true;
            this.chartControlMtReason.SeriesTemplate.Label = pieSeriesLabel4;
            pieSeriesView4.RuntimeExploding = false;
            this.chartControlMtReason.SeriesTemplate.View = pieSeriesView4;
            this.chartControlMtReason.Size = new System.Drawing.Size(326, 262);
            this.chartControlMtReason.TabIndex = 2;
            chartTitle3.Text = "被叫原因占比";
            this.chartControlMtReason.Titles.AddRange(new DevExpress.XtraCharts.ChartTitle[] {
            chartTitle3});
            // 
            // gCMt
            // 
            this.gCMt.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gCMt.Location = new System.Drawing.Point(0, 0);
            this.gCMt.MainView = this.gVMt;
            this.gCMt.Name = "gCMt";
            this.gCMt.Size = new System.Drawing.Size(321, 262);
            this.gCMt.TabIndex = 0;
            this.gCMt.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gVMt});
            this.gCMt.DoubleClick += new System.EventHandler(this.gCMt_DoubleClick);
            // 
            // gVMt
            // 
            this.gVMt.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn10,
            this.gridColumn5,
            this.gridColumn6,
            this.gridColumn7,
            this.gridColumn8});
            this.gVMt.GridControl = this.gCMt;
            this.gVMt.Name = "gVMt";
            this.gVMt.OptionsBehavior.Editable = false;
            this.gVMt.OptionsView.ColumnAutoWidth = false;
            // 
            // gridColumn10
            // 
            this.gridColumn10.Caption = "节点";
            this.gridColumn10.FieldName = "ShortName";
            this.gridColumn10.Name = "gridColumn10";
            this.gridColumn10.Visible = true;
            this.gridColumn10.VisibleIndex = 0;
            // 
            // gridColumn5
            // 
            this.gridColumn5.Caption = "被叫信令切换";
            this.gridColumn5.FieldName = "Desc";
            this.gridColumn5.Name = "gridColumn5";
            this.gridColumn5.Visible = true;
            this.gridColumn5.VisibleIndex = 1;
            // 
            // gridColumn6
            // 
            this.gridColumn6.Caption = "最大值";
            this.gridColumn6.FieldName = "Max";
            this.gridColumn6.Name = "gridColumn6";
            this.gridColumn6.Visible = true;
            this.gridColumn6.VisibleIndex = 2;
            // 
            // gridColumn7
            // 
            this.gridColumn7.Caption = "平均值";
            this.gridColumn7.FieldName = "Avg";
            this.gridColumn7.Name = "gridColumn7";
            this.gridColumn7.Visible = true;
            this.gridColumn7.VisibleIndex = 3;
            // 
            // gridColumn8
            // 
            this.gridColumn8.Caption = "最小值";
            this.gridColumn8.FieldName = "Min";
            this.gridColumn8.Name = "gridColumn8";
            this.gridColumn8.Visible = true;
            this.gridColumn8.VisibleIndex = 4;
            // 
            // pictureBox2
            // 
            this.pictureBox2.Cursor = System.Windows.Forms.Cursors.Hand;
            this.pictureBox2.Image = ((System.Drawing.Image)(resources.GetObject("pictureBox2.Image")));
            this.pictureBox2.Location = new System.Drawing.Point(6, 3);
            this.pictureBox2.Name = "pictureBox2";
            this.pictureBox2.Size = new System.Drawing.Size(32, 29);
            this.pictureBox2.SizeMode = System.Windows.Forms.PictureBoxSizeMode.StretchImage;
            this.pictureBox2.TabIndex = 15;
            this.pictureBox2.TabStop = false;
            this.pictureBox2.Visible = false;
            this.pictureBox2.Click += new System.EventHandler(this.pictureBox2_Click);
            // 
            // chartControlMt
            // 
            xyDiagram2.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram2.AxisX.Range.SideMarginsEnabled = true;
            xyDiagram2.AxisX.VisibleInPanesSerializable = "-1";
            xyDiagram2.AxisY.AutoScaleBreaks.Enabled = true;
            xyDiagram2.AxisY.AutoScaleBreaks.MaxCount = 4;
            xyDiagram2.AxisY.NumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Number;
            xyDiagram2.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram2.AxisY.Range.SideMarginsEnabled = true;
            xyDiagram2.AxisY.ScaleBreakOptions.SizeInPixels = 1;
            xyDiagram2.AxisY.Title.Font = new System.Drawing.Font("Tahoma", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            xyDiagram2.AxisY.Title.Text = "单位：秒";
            xyDiagram2.AxisY.Title.Visible = true;
            xyDiagram2.AxisY.VisibleInPanesSerializable = "-1";
            this.chartControlMt.Diagram = xyDiagram2;
            this.chartControlMt.Dock = System.Windows.Forms.DockStyle.Fill;
            this.chartControlMt.Location = new System.Drawing.Point(0, 0);
            this.chartControlMt.Name = "chartControlMt";
            this.chartControlMt.PaletteName = "Module";
            sideBySideBarSeriesLabel5.LineVisible = true;
            series6.Label = sideBySideBarSeriesLabel5;
            series6.Name = "最大值";
            pointOptions4.ArgumentNumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
            pointOptions4.ValueNumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
            series6.PointOptions = pointOptions4;
            sideBySideBarSeriesLabel6.LineVisible = true;
            series7.Label = sideBySideBarSeriesLabel6;
            series7.Name = "平均值";
            pointOptions5.ValueNumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
            series7.PointOptions = pointOptions5;
            sideBySideBarSeriesLabel7.LineVisible = true;
            series8.Label = sideBySideBarSeriesLabel7;
            series8.Name = "最小值";
            pointOptions6.ValueNumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
            series8.PointOptions = pointOptions6;
            this.chartControlMt.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
            series6,series7,series8};
            sideBySideBarSeriesLabel8.LineVisible = true;
            this.chartControlMt.SeriesTemplate.Label = sideBySideBarSeriesLabel8;
            this.chartControlMt.Size = new System.Drawing.Size(744, 262);
            this.chartControlMt.TabIndex = 8;
            chartTitle4.Font = new System.Drawing.Font("Tahoma", 10F);
            chartTitle4.Text = "被叫信令切换时长统计";
            this.chartControlMt.Titles.AddRange(new DevExpress.XtraCharts.ChartTitle[] {
            chartTitle4});
            // 
            // tabPage1
            // 
            this.tabPage1.Controls.Add(this.listViewMo);
            this.tabPage1.Location = new System.Drawing.Point(4, 23);
            this.tabPage1.Name = "tabPage1";
            this.tabPage1.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage1.Size = new System.Drawing.Size(1406, 545);
            this.tabPage1.TabIndex = 0;
            this.tabPage1.Text = "主叫时延分析";
            this.tabPage1.UseVisualStyleBackColor = true;
            // 
            // tabPage4
            // 
            this.tabPage4.Controls.Add(this.errListViewMo);
            this.tabPage4.Location = new System.Drawing.Point(4, 23);
            this.tabPage4.Name = "tabPage4";
            this.tabPage4.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage4.Size = new System.Drawing.Size(1406, 545);
            this.tabPage4.TabIndex = 3;
            this.tabPage4.Text = "主叫异常时延分析";
            this.tabPage4.UseVisualStyleBackColor = true;
            // 
            // tabPage2
            // 
            this.tabPage2.Controls.Add(this.listViewMt);
            this.tabPage2.Location = new System.Drawing.Point(4, 23);
            this.tabPage2.Name = "tabPage2";
            this.tabPage2.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage2.Size = new System.Drawing.Size(1406, 545);
            this.tabPage2.TabIndex = 1;
            this.tabPage2.Text = "被叫时延分析";
            this.tabPage2.UseVisualStyleBackColor = true;
            // 
            // tabPage5
            // 
            this.tabPage5.Controls.Add(this.errListViewMt);
            this.tabPage5.Location = new System.Drawing.Point(4, 23);
            this.tabPage5.Name = "tabPage5";
            this.tabPage5.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage5.Size = new System.Drawing.Size(1406, 545);
            this.tabPage5.TabIndex = 4;
            this.tabPage5.Text = "被叫异常时延分析";
            this.tabPage5.UseVisualStyleBackColor = true;
            // 
            // listViewMt
            // 
            this.listViewMt.AllColumns.Add(this.olvColumnMtSN);
            this.listViewMt.AllColumns.Add(this.olvColumnMtMoFileName);
            this.listViewMt.AllColumns.Add(this.olvColumnMtMtFileName);
            this.listViewMt.AllColumns.Add(this.olvColumnMtDesc);
            this.listViewMt.AllColumns.Add(this.olvColumnMtBeginTIme);
            this.listViewMt.AllColumns.Add(this.olvColumnMtEndTime);
            this.listViewMt.AllColumns.Add(this.olvColumnMtSpanTime);
            this.listViewMt.AllColumns.Add(this.olvColumnMtConTime);
            this.listViewMt.AllColumns.Add(this.olvColumnMtNormal);
            this.listViewMt.AllColumns.Add(this.olvColumnMtHandover);
            this.listViewMt.AllColumns.Add(this.olvColumnMtTA);
            this.listViewMt.AllColumns.Add(this.olvColumnMtCover);
            this.listViewMt.AllColumns.Add(this.olvColumnMtHeart);
            this.listViewMt.AllColumns.Add(this.olvColumnMtEnd);
            this.listViewMt.AllColumns.Add(this.olvColumnMtMoAnalyse);
            this.listViewMt.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnMtSN,
            this.olvColumnMtMoFileName,
            this.olvColumnMtMtFileName,
            this.olvColumnMtDesc,
            this.olvColumnMtBeginTIme,
            this.olvColumnMtEndTime,
            this.olvColumnMtSpanTime,
            this.olvColumnMtConTime,
            this.olvColumnMtNormal,
            this.olvColumnMtHandover,
            this.olvColumnMtTA,
            this.olvColumnMtCover,
            this.olvColumnMtHeart,
            this.olvColumnMtEnd,
            this.olvColumnMtMoAnalyse});
            this.listViewMt.ContextMenuStrip = this.cStrip;
            this.listViewMt.Cursor = System.Windows.Forms.Cursors.Default;
            this.listViewMt.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listViewMt.FullRowSelect = true;
            this.listViewMt.GridLines = true;
            this.listViewMt.HeaderWordWrap = true;
            this.listViewMt.IsNeedShowOverlay = false;
            this.listViewMt.Location = new System.Drawing.Point(3, 3);
            this.listViewMt.Name = "listViewMt";
            this.listViewMt.OwnerDraw = true;
            this.listViewMt.ShowGroups = false;
            this.listViewMt.Size = new System.Drawing.Size(1400, 539);
            this.listViewMt.TabIndex = 13;
            this.listViewMt.UseCompatibleStateImageBehavior = false;
            this.listViewMt.View = System.Windows.Forms.View.Details;
            this.listViewMt.VirtualMode = true;
            // 
            // errListViewMt
            // 
            this.errListViewMt.AllColumns.Add(this.errOlvColumnMtSN);
            this.errListViewMt.AllColumns.Add(this.errOlvColumnMtMoFileName);
            this.errListViewMt.AllColumns.Add(this.errOlvColumnMtMtFileName);
            this.errListViewMt.AllColumns.Add(this.errOlvColumnMtDesc);
            this.errListViewMt.AllColumns.Add(this.errOlvColumnMtBeginTIme);
            this.errListViewMt.AllColumns.Add(this.errOlvColumnMtEndTime);
            this.errListViewMt.AllColumns.Add(this.errOlvColumnMtSpanTime);
            this.errListViewMt.AllColumns.Add(this.errOlvColumnMtConTime);
            this.errListViewMt.AllColumns.Add(this.errOlvColumnMtNormal);
            this.errListViewMt.AllColumns.Add(this.errOlvColumnMtHandover);
            this.errListViewMt.AllColumns.Add(this.errOlvColumnMtTA);
            this.errListViewMt.AllColumns.Add(this.errOlvColumnMtCover);
            this.errListViewMt.AllColumns.Add(this.errOlvColumnMtHeart);
            this.errListViewMt.AllColumns.Add(this.errOlvColumnMtEnd);
            this.errListViewMt.AllColumns.Add(this.errOlvColumnMtMoAnalyse);
            this.errListViewMt.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.errOlvColumnMtSN,
            this.errOlvColumnMtMoFileName,
            this.errOlvColumnMtMtFileName,
            this.errOlvColumnMtDesc,
            this.errOlvColumnMtBeginTIme,
            this.errOlvColumnMtEndTime,
            this.errOlvColumnMtSpanTime,
            this.errOlvColumnMtConTime,
            this.errOlvColumnMtNormal,
            this.errOlvColumnMtHandover,
            this.errOlvColumnMtTA,
            this.errOlvColumnMtCover,
            this.errOlvColumnMtHeart,
            this.errOlvColumnMtEnd,
            this.errOlvColumnMtMoAnalyse});
            this.errListViewMt.ContextMenuStrip = this.cStrip;
            this.errListViewMt.Cursor = System.Windows.Forms.Cursors.Default;
            this.errListViewMt.Dock = System.Windows.Forms.DockStyle.Fill;
            this.errListViewMt.FullRowSelect = true;
            this.errListViewMt.GridLines = true;
            this.errListViewMt.HeaderWordWrap = true;
            this.errListViewMt.IsNeedShowOverlay = false;
            this.errListViewMt.Location = new System.Drawing.Point(3, 3);
            this.errListViewMt.Name = "errListViewMt";
            this.errListViewMt.OwnerDraw = true;
            this.errListViewMt.ShowGroups = false;
            this.errListViewMt.Size = new System.Drawing.Size(1400, 539);
            this.errListViewMt.TabIndex = 13;
            this.errListViewMt.UseCompatibleStateImageBehavior = false;
            this.errListViewMt.View = System.Windows.Forms.View.Details;
            this.errListViewMt.VirtualMode = true;
            // 
            // olvColumnMtSN
            // 
            this.olvColumnMtSN.HeaderFont = null;
            this.olvColumnMtSN.Text = "序号";
            // 
            // errOlvColumnMtSN
            // 
            this.errOlvColumnMtSN.HeaderFont = null;
            this.errOlvColumnMtSN.Text = "序号";
            // 
            // olvColumnMtMoFileName
            // 
            this.olvColumnMtMoFileName.HeaderFont = null;
            this.olvColumnMtMoFileName.Text = "主叫文件";
            this.olvColumnMtMoFileName.Width = 180;
            // 
            // errOlvColumnMtMoFileName
            // 
            this.errOlvColumnMtMoFileName.HeaderFont = null;
            this.errOlvColumnMtMoFileName.Text = "主叫文件";
            this.errOlvColumnMtMoFileName.Width = 180;
            // 
            // olvColumnMtMtFileName
            // 
            this.olvColumnMtMtFileName.HeaderFont = null;
            this.olvColumnMtMtFileName.Text = "被叫文件";
            this.olvColumnMtMtFileName.Width = 180;
            // 
            // errOlvColumnMtMtFileName
            // 
            this.errOlvColumnMtMtFileName.HeaderFont = null;
            this.errOlvColumnMtMtFileName.Text = "被叫文件";
            this.errOlvColumnMtMtFileName.Width = 180;
            // 
            // olvColumnMtDesc
            // 
            this.olvColumnMtDesc.HeaderFont = null;
            this.olvColumnMtDesc.Text = "信令切换";
            this.olvColumnMtDesc.Width = 180;
            // 
            // errOlvColumnMtDesc
            // 
            this.errOlvColumnMtDesc.HeaderFont = null;
            this.errOlvColumnMtDesc.Text = "信令切换";
            this.errOlvColumnMtDesc.Width = 180;
            // 
            // olvColumnMtBeginTIme
            // 
            this.olvColumnMtBeginTIme.HeaderFont = null;
            this.olvColumnMtBeginTIme.Text = "开始时间";
            this.olvColumnMtBeginTIme.Width = 100;
            // 
            // errOlvColumnMtBeginTIme
            // 
            this.errOlvColumnMtBeginTIme.HeaderFont = null;
            this.errOlvColumnMtBeginTIme.Text = "开始时间";
            this.errOlvColumnMtBeginTIme.Width = 100;
            // 
            // olvColumnMtEndTime
            // 
            this.olvColumnMtEndTime.HeaderFont = null;
            this.olvColumnMtEndTime.Text = "结束时间";
            this.olvColumnMtEndTime.Width = 100;
            // 
            // errOlvColumnMtEndTime
            // 
            this.errOlvColumnMtEndTime.HeaderFont = null;
            this.errOlvColumnMtEndTime.Text = "结束时间";
            this.errOlvColumnMtEndTime.Width = 100;
            // 
            // olvColumnMtSpanTime
            // 
            this.olvColumnMtSpanTime.HeaderFont = null;
            this.olvColumnMtSpanTime.Text = "时长间隔（毫秒）";
            // 
            // errOlvColumnMtSpanTime
            // 
            this.errOlvColumnMtSpanTime.HeaderFont = null;
            this.errOlvColumnMtSpanTime.Text = "时长间隔（毫秒）";
            // 
            // olvColumnMtNormal
            // 
            this.olvColumnMtNormal.HeaderFont = null;
            this.olvColumnMtNormal.Text = "是否正常";
            // 
            // errOlvColumnMtNormal
            // 
            this.errOlvColumnMtNormal.HeaderFont = null;
            this.errOlvColumnMtNormal.Text = "是否正常";
            // 
            // olvColumnMtHandover
            // 
            this.olvColumnMtHandover.HeaderFont = null;
            this.olvColumnMtHandover.Text = "是否伴随切换";
            // 
            // errOlvColumnMtHandover
            // 
            this.errOlvColumnMtHandover.HeaderFont = null;
            this.errOlvColumnMtHandover.Text = "是否伴随切换";
            // 
            // olvColumnMtTA
            // 
            this.olvColumnMtTA.HeaderFont = null;
            this.olvColumnMtTA.Text = "是否伴随TA更新";
            // 
            // errOlvColumnMtTA
            // 
            this.errOlvColumnMtTA.HeaderFont = null;
            this.errOlvColumnMtTA.Text = "是否伴随TA更新";
            // 
            // olvColumnMtCover
            // 
            this.olvColumnMtCover.HeaderFont = null;
            this.olvColumnMtCover.Text = "是否存在覆盖干扰";
            // 
            // errOlvColumnMtCover
            // 
            this.errOlvColumnMtCover.HeaderFont = null;
            this.errOlvColumnMtCover.Text = "是否存在覆盖干扰";
            // 
            // olvColumnMtHeart
            // 
            this.olvColumnMtHeart.HeaderFont = null;
            this.olvColumnMtHeart.Text = "是否进行核心网排查";
            // 
            // errOlvColumnMtHeart
            // 
            this.errOlvColumnMtHeart.HeaderFont = null;
            this.errOlvColumnMtHeart.Text = "是否进行核心网排查";
            // 
            // olvColumnMtEnd
            // 
            this.olvColumnMtEnd.HeaderFont = null;
            this.olvColumnMtEnd.Text = "是否进行终端排查";
            // 
            // errOlvColumnMtEnd
            // 
            this.errOlvColumnMtEnd.HeaderFont = null;
            this.errOlvColumnMtEnd.Text = "是否进行终端排查";
            // 
            // olvColumnMtMoAnalyse
            // 
            this.olvColumnMtMoAnalyse.HeaderFont = null;
            this.olvColumnMtMoAnalyse.Text = "是否进行主叫测分析";
            // 
            // errOlvColumnMtMoAnalyse
            // 
            this.errOlvColumnMtMoAnalyse.HeaderFont = null;
            this.errOlvColumnMtMoAnalyse.Text = "是否进行主叫测分析";
            // 
            // olvColumnConTime
            // 
            this.olvColumnConTime.HeaderFont = null;
            this.olvColumnConTime.Text = "阈值";
            // 
            // errOlvColumnConTime
            // 
            this.errOlvColumnConTime.HeaderFont = null;
            this.errOlvColumnConTime.Text = "阈值";
            // 
            // olvColumnMtConTime
            // 
            this.olvColumnMtConTime.HeaderFont = null;
            this.olvColumnMtConTime.Text = "阈值";
            // 
            // errOlvColumnMtConTime
            // 
            this.errOlvColumnMtConTime.HeaderFont = null;
            this.errOlvColumnMtConTime.Text = "阈值";
            // 
            // DelayReasonResultDlg
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(96F, 96F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Dpi;
            this.ClientSize = new System.Drawing.Size(1414, 572);
            this.Controls.Add(this.tabMo);
            this.Name = "DelayReasonResultDlg";
            this.Text = "时延超长原因分析";
            ((System.ComponentModel.ISupportInitialize)(this.listViewMo)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.errListViewMo)).EndInit();
            this.cStrip.ResumeLayout(false);
            this.tabMo.ResumeLayout(false);
            this.tabPage3.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).EndInit();
            this.splitContainerControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl2)).EndInit();
            this.splitContainerControl2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl4)).EndInit();
            this.splitContainerControl4.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(pieSeriesLabel1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesLabel2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesView2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlReason)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gCMo)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gVMo)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlMo)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl3)).EndInit();
            this.splitContainerControl3.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl5)).EndInit();
            this.splitContainerControl5.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(pieSeriesLabel3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesView3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesLabel4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesView4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlMtReason)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gCMt)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gVMt)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series6)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel6)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series7)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel7)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series8)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel8)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlMt)).EndInit();
            this.tabPage1.ResumeLayout(false);
            this.tabPage4.ResumeLayout(false);
            this.tabPage2.ResumeLayout(false);
            this.tabPage5.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.listViewMt)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.errListViewMt)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.TabControl tabMo;
        private System.Windows.Forms.TabPage tabPage1;
        private BrightIdeasSoftware.TreeListView listViewMo;
        private BrightIdeasSoftware.OLVColumn olvColumnSN;
        private BrightIdeasSoftware.OLVColumn olvColumnMoFileName;
        private BrightIdeasSoftware.OLVColumn olvColumnMtFileName;
        private BrightIdeasSoftware.OLVColumn olvColumnDesc;
        private BrightIdeasSoftware.OLVColumn olvColumnBeginTime;
        private BrightIdeasSoftware.OLVColumn olvColumnEndTime;
        private BrightIdeasSoftware.OLVColumn olvColumnSpanTime;
        private BrightIdeasSoftware.OLVColumn olvColumnConTime;
        private BrightIdeasSoftware.OLVColumn olvColumnNormal;
        private BrightIdeasSoftware.OLVColumn olvColumnHandover;
        private BrightIdeasSoftware.OLVColumn olvColumnTA;
        private BrightIdeasSoftware.OLVColumn olvColumnCover;
        private BrightIdeasSoftware.OLVColumn olvColumnHeart;
        private BrightIdeasSoftware.OLVColumn olvColumnOverNet;
        private BrightIdeasSoftware.OLVColumn olvColumnEnd;
        private BrightIdeasSoftware.OLVColumn olvColumnMtAnalyse;
        private System.Windows.Forms.TabPage tabPage4;
        private BrightIdeasSoftware.TreeListView errListViewMo;
        private BrightIdeasSoftware.OLVColumn errOlvColumnSN;
        private BrightIdeasSoftware.OLVColumn errOlvColumnMoFileName;
        private BrightIdeasSoftware.OLVColumn errOlvColumnMtFileName;
        private BrightIdeasSoftware.OLVColumn errOlvColumnDesc;
        private BrightIdeasSoftware.OLVColumn errOlvColumnBeginTime;
        private BrightIdeasSoftware.OLVColumn errOlvColumnEndTime;
        private BrightIdeasSoftware.OLVColumn errOlvColumnSpanTime;
        private BrightIdeasSoftware.OLVColumn errOlvColumnConTime;
        private BrightIdeasSoftware.OLVColumn errOlvColumnNormal;
        private BrightIdeasSoftware.OLVColumn errOlvColumnHandover;
        private BrightIdeasSoftware.OLVColumn errOlvColumnTA;
        private BrightIdeasSoftware.OLVColumn errOlvColumnCover;
        private BrightIdeasSoftware.OLVColumn errOlvColumnHeart;
        private BrightIdeasSoftware.OLVColumn errOlvColumnOverNet;
        private BrightIdeasSoftware.OLVColumn errOlvColumnEnd;
        private BrightIdeasSoftware.OLVColumn errOlvColumnMtAnalyse;
        private System.Windows.Forms.TabPage tabPage2;
        private BrightIdeasSoftware.TreeListView listViewMt;
        private BrightIdeasSoftware.OLVColumn olvColumnMtSN;
        private BrightIdeasSoftware.OLVColumn olvColumnMtMoFileName;
        private BrightIdeasSoftware.OLVColumn olvColumnMtMtFileName;
        private BrightIdeasSoftware.OLVColumn olvColumnMtDesc;
        private BrightIdeasSoftware.OLVColumn olvColumnMtBeginTIme;
        private BrightIdeasSoftware.OLVColumn olvColumnMtEndTime;
        private BrightIdeasSoftware.OLVColumn olvColumnMtSpanTime;
        private BrightIdeasSoftware.OLVColumn olvColumnMtConTime;
        private BrightIdeasSoftware.OLVColumn olvColumnMtNormal;
        private BrightIdeasSoftware.OLVColumn olvColumnMtHandover;
        private BrightIdeasSoftware.OLVColumn olvColumnMtTA;
        private BrightIdeasSoftware.OLVColumn olvColumnMtCover;
        private BrightIdeasSoftware.OLVColumn olvColumnMtHeart;
        private BrightIdeasSoftware.OLVColumn olvColumnMtEnd;
        private BrightIdeasSoftware.OLVColumn olvColumnMtMoAnalyse;
        private System.Windows.Forms.TabPage tabPage5;
        private BrightIdeasSoftware.TreeListView errListViewMt;
        private BrightIdeasSoftware.OLVColumn errOlvColumnMtSN;
        private BrightIdeasSoftware.OLVColumn errOlvColumnMtMoFileName;
        private BrightIdeasSoftware.OLVColumn errOlvColumnMtMtFileName;
        private BrightIdeasSoftware.OLVColumn errOlvColumnMtDesc;
        private BrightIdeasSoftware.OLVColumn errOlvColumnMtBeginTIme;
        private BrightIdeasSoftware.OLVColumn errOlvColumnMtEndTime;
        private BrightIdeasSoftware.OLVColumn errOlvColumnMtSpanTime;
        private BrightIdeasSoftware.OLVColumn errOlvColumnMtConTime;
        private BrightIdeasSoftware.OLVColumn errOlvColumnMtNormal;
        private BrightIdeasSoftware.OLVColumn errOlvColumnMtHandover;
        private BrightIdeasSoftware.OLVColumn errOlvColumnMtTA;
        private BrightIdeasSoftware.OLVColumn errOlvColumnMtCover;
        private BrightIdeasSoftware.OLVColumn errOlvColumnMtHeart;
        private BrightIdeasSoftware.OLVColumn errOlvColumnMtEnd;
        private BrightIdeasSoftware.OLVColumn errOlvColumnMtMoAnalyse;
        private System.Windows.Forms.TabPage tabPage3;
        private DevExpress.XtraCharts.ChartControl chartControlReason;
        private DevExpress.XtraCharts.ChartControl chartControlMtReason;
        private DevExpress.XtraCharts.ChartControl chartControlMo;
        private DevExpress.XtraCharts.ChartControl chartControlMt;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl1;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl2;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl3;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl4;
        private DevExpress.XtraGrid.GridControl gCMo;
        private DevExpress.XtraGrid.Views.Grid.GridView gVMo;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl5;
        private DevExpress.XtraGrid.GridControl gCMt;
        private DevExpress.XtraGrid.Views.Grid.GridView gVMt;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn6;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn7;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn8;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn9;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn10;
        private System.Windows.Forms.ContextMenuStrip cStrip;
        private System.Windows.Forms.ToolStripMenuItem ExportItem;
        private System.Windows.Forms.ToolStripMenuItem ReplayItem;
        private System.Windows.Forms.PictureBox pictureBox1;
        private System.Windows.Forms.PictureBox pictureBox2;
        private System.Windows.Forms.ToolStripMenuItem ExpandItem;
    }
}