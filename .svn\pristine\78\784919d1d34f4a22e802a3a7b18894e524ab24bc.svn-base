using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.Model
{
    public class City
    {
        public int CityID { get; set; }

        public string Name { get; set; }

        public string Description { get; set; }

        public override string ToString()
        {
            return Name;
        }

        public static City Fill(Content content)
        {
            City city = new City();
            city.CityID = content.GetParamInt();
            city.Name = content.GetParamString();
            city.Description = content.GetParamString();
            return city;
        }
    }
}
