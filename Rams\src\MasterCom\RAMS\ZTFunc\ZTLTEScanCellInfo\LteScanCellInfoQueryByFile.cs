﻿using System.Collections.Generic;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;

namespace MasterCom.RAMS.ZTFunc
{
    public class LteScanCellInfoQueryByFile : DIYReplayFileQuery
    {
        private LteScanCellInfoStater stater;
        public LteScanCellInfoQueryByFile(MainModel mainModel)
            : base(mainModel)
        {
            IsAddSampleToDTDataManager = false;
            IsAddMessageToDTDataManager = false;
            isAutoLoadCQTPicture = false;
        }

        public override string Name
        {
            get { return "LTE扫频小区集(按文件)"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 23000, 23025, this.Name);
        }

        protected override DIYReplayContentOption getDIYReplayContent()
        {
            stater = new LteScanCellInfoStater();
            DIYReplayContentOption option = new DIYReplayContentOption();
            option.MessageInclude = false;
            option.EventInclude = false;
            option.DefaultSerialThemeName = "LTESCAN_TopN_CELL_Specific_RSRP";
            setColumns(option, "isampleid");
            setColumns(option, "itime");
            setColumns(option, "ilongitude");
            setColumns(option, "ilatitude");
            setColumns(option, "LTESCAN_TopN_CELL_Specific_RSRP");
            setColumns(option, "LTESCAN_TopN_CELL_Specific_RSSINR");
            setColumns(option, "LTESCAN_TopN_EARFCN");
            setColumns(option, "LTESCAN_TopN_PCI");
            return option;
        }

        protected override void doWithDTData(TestPoint tp)
        {
            stater.StatTestPoint(tp);
        }

        protected override void doPostReplayAction()
        {
            List<LteScanCellInfo> cellInfoList = stater.GetStatResult();
            LteScanCellInfoResultForm frm = MainModel.GetInstance().CreateResultForm(typeof(LteScanCellInfoResultForm)) as LteScanCellInfoResultForm;
            frm.FillData(cellInfoList);
            frm.Visible = true;
            frm.BringToFront();
        }

        protected override bool isValidPoint(double jd, double wd)
        {
            if (MainModel.SearchGeometrys.Region != null)
            {
                return MainModel.SearchGeometrys.GeoOp.Contains(jd, wd);
            }
            return true;
        }
    }
}
