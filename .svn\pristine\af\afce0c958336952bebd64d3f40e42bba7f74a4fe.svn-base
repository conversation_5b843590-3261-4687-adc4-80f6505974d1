﻿using System;
using System.Collections.Generic;
using System.Text;
using System.IO;

using ICSharpCode.SharpZipLib.Checksums;
using ICSharpCode.SharpZipLib.Zip;
using ICSharpCode.SharpZipLib.GZip;

namespace MasterCom.Util
{
    public static class ZipClass
    {
        public static void ZipFile(string FileToZip, string ZipedFile, int CompressionLevel, int BlockSize)
        {
            //如果文件没有找到，则报错
            if (!System.IO.File.Exists(FileToZip))
            {
                throw new System.IO.FileNotFoundException("The specified file " + FileToZip + " could not be found. Zipping aborderd");
            }

            System.IO.FileStream StreamToZip = new System.IO.FileStream(FileToZip, System.IO.FileMode.Open, System.IO.FileAccess.Read);
            System.IO.FileStream ZipFile = System.IO.File.Create(ZipedFile);
            ZipOutputStream ZipStream = new ZipOutputStream(ZipFile);
            ZipEntry ZipEntry = new ZipEntry("ZippedFile");
            ZipStream.PutNextEntry(ZipEntry);
            ZipStream.SetLevel(CompressionLevel);
            byte[] buffer = new byte[BlockSize];
            System.Int32 size = StreamToZip.Read(buffer, 0, buffer.Length);
            ZipStream.Write(buffer, 0, size);

            while (size < StreamToZip.Length)
            {
                int sizeRead = StreamToZip.Read(buffer, 0, buffer.Length);
                ZipStream.Write(buffer, 0, sizeRead);
                size += sizeRead;
            }

            ZipStream.Finish();
            ZipStream.Close();
            StreamToZip.Close();
        }

        public static void ZipFileMain(string srcfolderDir, string tarZipFileFullName
            , string password = null)
        {
            string[] filenames = Directory.GetFiles(srcfolderDir,"*.*",SearchOption.AllDirectories);
            if (filenames == null || filenames.Length <= 0)
            {
                return;
            }

            Crc32 crc = new Crc32();
            ZipOutputStream s = new ZipOutputStream(File.Create(tarZipFileFullName));

            s.SetLevel(6); // 0 - store only to 9 - means best compression 压缩方式为：最好
            if (!string.IsNullOrEmpty(password))
            {
                s.Password = password;
            }

            foreach (string filefullname in filenames)
            {
                //打开压缩文件
                FileStream fs = File.OpenRead(filefullname);

                byte[] buffer = new byte[fs.Length];
                fs.Read(buffer, 0, buffer.Length);
                string relaName = filefullname.Substring(srcfolderDir.Length);
                ZipEntry entry = new ZipEntry(relaName);

                entry.DateTime = DateTime.Now;

                // set Size and the crc, because the information
                // about the size and crc should be stored in the header
                // if it is not set it is automatically written in the footer.
                // (in this case size == crc == -1 in the header)
                // Some ZIP programs have problems with zip files that don't store
                // the size and crc in the header.
                entry.Size = fs.Length;
                fs.Close();

                if (string.IsNullOrEmpty(password))//设置密码并设置Crc后，用WinRar解压时会有问题，所以这里判段下
                {
                    crc.Reset();
                    crc.Update(buffer);
                    entry.Crc = crc.Value;
                }

                s.PutNextEntry(entry);

                s.Write(buffer, 0, buffer.Length);

            }

            s.Finish();
            s.Close();
        }

        /// <summary>
        /// 文件加密压缩
        /// </summary>
        /// <param name="srcfileDir">需要压缩的文件路径</param>
        /// <param name="tarZipFileFullName">压缩包路径</param>
        /// <param name="password">加密密码</param>
        public static void ZipFileSecurity(string srcfileDir, string tarZipFileFullName, string password
            , bool isDeleteSrcfileAfterZip = true)
        {
            if (!File.Exists(srcfileDir))
            {
                return;
            }

            ZipOutputStream s = new ZipOutputStream(File.Create(tarZipFileFullName));

            s.SetLevel(6); // 0 - store only to 9 - means best compression
            s.Password = password;

            //打开待压缩文件 
            FileStream fs = File.OpenRead(srcfileDir);

            byte[] buffer = new byte[fs.Length];
            fs.Read(buffer, 0, buffer.Length);

            Array arr = srcfileDir.Split('\\');
            string le = arr.GetValue(arr.Length - 1).ToString();
            ZipEntry entry = new ZipEntry(le);
            entry.DateTime = DateTime.Now;
            entry.Size = fs.Length;
            fs.Close();
            if (isDeleteSrcfileAfterZip)
            {
                System.Threading.Thread.Sleep(10);
                File.Delete(srcfileDir);
            }
            s.PutNextEntry(entry);
            s.Write(buffer, 0, buffer.Length);
            s.Finish();
            s.Close();
        }
    }

    public class UnZipClass
    {
        public bool UnZip(string zipfileFullName, string tarFolderName)
        {
            ZipInputStream s = new ZipInputStream(File.OpenRead(zipfileFullName));
            try
            {
                ZipEntry theEntry;
                while ((theEntry = s.GetNextEntry()) != null)
                {
                    string directoryName = Path.GetDirectoryName(tarFolderName);
                    string fileName = Path.GetFileName(theEntry.Name);
                    //生成解压目录
                    if (!Directory.Exists(directoryName))
                    {
                        Directory.CreateDirectory(directoryName);
                    }
                    if (fileName != String.Empty)
                    {
                        FileInfo fiTarInfo = new FileInfo(Path.Combine(tarFolderName, theEntry.Name));
                        string makesureDir = fiTarInfo.Directory.FullName;
                        if (!Directory.Exists(makesureDir))
                        {
                            Directory.CreateDirectory(makesureDir);
                        }
                        //解压文件到指定的目录
                        unZipToFolder(tarFolderName, s, theEntry);
                    }
                }
                return true;
            }
            catch (Exception)
            {
                return false;
            }
            finally
            {
                s.Close();
                s.Dispose();
            }
        }

        private void unZipToFolder(string tarFolderName, ZipInputStream s, ZipEntry theEntry)
        {
            FileStream streamWriter = File.Create(Path.Combine(tarFolderName, theEntry.Name));

            int size;
            byte[] data = new byte[2048];
            while (true)
            {
                size = s.Read(data, 0, data.Length);
                if (size > 0)
                {
                    streamWriter.Write(data, 0, size);
                }
                else
                {
                    break;
                }
            }
            streamWriter.Close();
            streamWriter.Dispose();
        }
    }
}
