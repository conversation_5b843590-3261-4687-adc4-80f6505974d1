﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc.LteSignalImsi
{
    public partial class KpiRoadRenderSetForm : DIYInjectionGridQueryMultiMapSettingDlg
    {
        public KpiRoadRenderSetForm()
        {
            InitializeComponent();
            this.Text = "道路KPI图层设置";
            layerSettingErrMsg = "请选择道路图层";
        }

        public List<KpiRoadRenderShpFile> GetShpFiles()
        {
            List<KpiRoadRenderShpFile> retList = new List<KpiRoadRenderShpFile>();
            foreach (StreetInjectTableInfo tInfo in MainModel.GetInstance().StreetInjectTablesList)
            {
                KpiRoadRenderShpFile shpFile = new KpiRoadRenderShpFile();
                shpFile.FilePath = tInfo.FilePath;
                shpFile.FileName = tInfo.FileName;
                shpFile.ColumName = tInfo.ColumnName;
                retList.Add(shpFile);
            }
            return retList;
        }
    }

    public class KpiRoadRenderShpFile
    {
        public string FileName
        {
            get;
            set;
        }

        public string FilePath
        {
            get;
            set;
        }

        public string ColumName
        {
            get;
            set;
        }
    }
}
