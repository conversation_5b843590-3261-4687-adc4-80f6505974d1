﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Func.SystemSetting;
using MasterCom.RAMS.BackgroundFunc;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class LTEModRoadQueryByFile : ModRoadQueryByFile
    {
        public LTEModRoadQueryByFile(MainModel mainModel) 
            : base(mainModel, new LTEModRoadQuery(mainModel))
        {
        }
    }

    public class LTEModRoadQueryByRegion : ModRoadQueryByRegion
    {
        public LTEModRoadQueryByRegion(MainModel mainModel)
            : base(mainModel, new LTEModRoadQuery(mainModel))
        {
            queryer = new LTEModRoadQuery(mainModel);
            ServiceTypes.Clear();
            ServiceTypes.AddRange(ServiceTypeManager.GetLteAllServiceTypes());
        }

        public LTEModRoadQueryByRegion(ServiceName serviceName, MainModel mainModel)
            : this(mainModel)
        {
            ServiceTypes.Clear();
            ServiceTypes = ServiceTypeManager.GetServiceTypesByServiceName(serviceName);
        }

        protected static readonly object lockObj = new object();
        private static LTEModRoadQueryByRegion intance = null;
        public static LTEModRoadQueryByRegion GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new LTEModRoadQueryByRegion(MainModel.GetInstance());
                    }
                }
            }
            return intance;
        }

        public LTEModRoadCondition ModRoadCond
        {
            get
            {
                LTEModRoadQuery modQuery = queryer as LTEModRoadQuery;
                if (modQuery.cond == null)
                {
                    modQuery.cond = new LTEModRoadCondition();
                }
                return modQuery.cond;
            }
        }

        #region Background
        public override BackgroundFuncType FuncType
        {
            get { return BackgroundFuncType.LTE业务专题; }
        }

        public override BackgroundSubFuncType SubFuncType
        {
            get { return BackgroundSubFuncType.干扰; }
        }

        public override Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["BackgroundStat"] = BackgroundStat;
                param["RoadLength"] = ModRoadCond.RoadLength;
                param["SampleInterval"] = ModRoadCond.SampleInterval;
                param["MaxRxlev"] = ModRoadCond.MaxRxlev;

                param["InterfereRate"] = ModRoadCond.FilterCond.InterfereRate;
                param["RxlevDiff"] = ModRoadCond.FilterCond.RxlevDiff;
                param["Angle"] = ModRoadCond.FilterCond.Angle;
                param["Distance"] = ModRoadCond.FilterCond.Distance;
                param["Sids"] = ModRoadCond.FilterCond.Sids;
                param["ModX"] = ModRoadCond.FilterCond.ModX;
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                setParam(value);
            }
        }

        private void setParam(Dictionary<string, object> value)
        {
            Dictionary<string, object> param = value;
            if (param.ContainsKey("BackgroundStat"))
            {
                BackgroundStat = (bool)param["BackgroundStat"];
            }
            if (param.ContainsKey("RoadLength"))
            {
                ModRoadCond.RoadLength = (double)param["RoadLength"];
            }
            if (param.ContainsKey("SampleInterval"))
            {
                ModRoadCond.SampleInterval = (double)param["SampleInterval"];
            }
            if (param.ContainsKey("MaxRxlev"))
            {
                ModRoadCond.MaxRxlev = (double)param["MaxRxlev"];
            }

            if (param.ContainsKey("InterfereRate"))
            {
                ModRoadCond.FilterCond.InterfereRate = (double)param["InterfereRate"];
            }
            if (param.ContainsKey("RxlevDiff"))
            {
                ModRoadCond.FilterCond.RxlevDiff = (double)param["RxlevDiff"];
            }
            if (param.ContainsKey("Angle"))
            {
                ModRoadCond.FilterCond.Angle = (double)param["Angle"];
            }
            if (param.ContainsKey("Distance"))
            {
                ModRoadCond.FilterCond.Distance = (double)param["Distance"];
            }
            setSids(param);
            if (param.ContainsKey("ModX"))
            {
                ModRoadCond.FilterCond.ModX = (int)param["ModX"];
            }
        }

        private void setSids(Dictionary<string, object> param)
        {
            if (param.ContainsKey("Sids"))
            {
                object objSids = param["Sids"];
                if (objSids is List<object>)
                {
                    List<object> objList = objSids as List<object>;
                    ModRoadCond.FilterCond.Sids.Clear();
                    foreach (int id in objList)
                    {
                        ModRoadCond.FilterCond.Sids.Add(id);
                    }
                }
                else if (objSids is List<int>)
                {
                    ModRoadCond.FilterCond.Sids = param["Sids"] as List<int>;
                }
            }
        }

        public override PropertiesControl Properties
        {
            get
            {
                return new Mod3RoadProperties_LTE(this);
            }
        }

        List<LTEModRoadItem> resultList = new List<LTEModRoadItem>();
        protected override void saveBackgroundData()
        {
            LTEModRoadQuery modQuery = queryer as LTEModRoadQuery;
            object tmpResult = modQuery.stater.GetStatResult(ModRoadCond.FilterCond);
            if (tmpResult is List<LTEModRoadItem>)
            {
                resultList = tmpResult as List<LTEModRoadItem>;
            }

            List<BackgroundResult> bgResultList = new List<BackgroundResult>();
            foreach (LTEModRoadItem item in resultList)
            {
                foreach (LTEModCellItem tarCellItem in item.InterCellList)
                {
                    BackgroundResult result = tarCellItem.ConvertToBackgroundResult(curAnaFileInfo, item);
                    result.SubFuncID = GetSubFuncID();
                    result.ProjectString = BackgroundFuncBaseSetting.GetInstance().projectType;
                    bgResultList.Add(result);
                }
            }
            BackgroundFuncQueryManager.GetInstance().SaveResult_Cell(GetSubFuncID(), curAnaFileInfo, bgResultList);
            modQuery.stater.ClearRoadList();
            resultList.Clear();
        }
        protected override void initBackgroundImageDesc()
        {
            Dictionary<string, List<BackgroundResult>> bgResultDic = new Dictionary<string, List<BackgroundResult>>();
            foreach (BackgroundResult bgResult in BackgroundResultList)
            {
                string strKey = string.Format("{0}_{1}_{2}", bgResult.FileID, bgResult.ISTime, bgResult.IETime);
                List<BackgroundResult> bgResultList;
                if (!bgResultDic.TryGetValue(strKey, out bgResultList))
                {
                    bgResultList = new List<BackgroundResult>();
                    bgResultDic[strKey] = bgResultList;
                }
                bgResultList.Add(bgResult);
            }
            this.BackgroundNPOIRowResultDic.Clear();
            this.BackgroundNPOIRowResultDic["模三干扰路段"] = getResultNPOIRows(bgResultDic);
        }

        private List<NPOIRow> getResultNPOIRows(Dictionary<string, List<BackgroundResult>> bgResultDic)
        {
            List<NPOIRow> resultNPOIRowList = new List<NPOIRow>();

            #region 列名
            NPOIRow rowTitle = new NPOIRow();
            rowTitle.AddCellValue("序号");
            rowTitle.AddCellValue("道路名称");
            rowTitle.AddCellValue("道路长度");
            rowTitle.AddCellValue("总采样点数");
            rowTitle.AddCellValue("路段主服个数");
            rowTitle.AddCellValue("路段邻区个数");
            rowTitle.AddCellValue("路段受干扰主服个数");
            rowTitle.AddCellValue("路段邻区干扰个数");
            rowTitle.AddCellValue("路段干扰采样点个数");
            rowTitle.AddCellValue("路段干扰采样点比例（%）");
            rowTitle.AddCellValue("文件名");

            rowTitle.AddCellValue("主服小区序号");
            rowTitle.AddCellValue("受干扰主服名");
            rowTitle.AddCellValue("主服TAC");
            rowTitle.AddCellValue("主服ECI");
            rowTitle.AddCellValue("主服CellID");
            rowTitle.AddCellValue("主服EARFCN");
            rowTitle.AddCellValue("主服PCI");
            rowTitle.AddCellValue("邻区个数");
            rowTitle.AddCellValue("干扰邻区个数");
            rowTitle.AddCellValue("邻区干扰比例（%）");
            rowTitle.AddCellValue("主服采样点数");
            rowTitle.AddCellValue("主服干扰采样点数");
            rowTitle.AddCellValue("主服干扰采样点占比（%）");
            rowTitle.AddCellValue("RSRP平均值");
            rowTitle.AddCellValue("SINR平均值");
            rowTitle.AddCellValue("主服PDCP_UL平均值(Mbps)");
            rowTitle.AddCellValue("主服PDCP_DL平均值(Mbps)");

            rowTitle.AddCellValue("邻区序号");
            rowTitle.AddCellValue("干扰邻区名");
            rowTitle.AddCellValue("干扰邻区TAC");
            rowTitle.AddCellValue("干扰邻区ECI");
            rowTitle.AddCellValue("干扰邻区CellID");
            rowTitle.AddCellValue("干扰邻区EARFCN");
            rowTitle.AddCellValue("干扰邻区PCI");
            rowTitle.AddCellValue("干扰邻区采样点数");
            rowTitle.AddCellValue("干扰采样点数");
            rowTitle.AddCellValue("干扰采样点比例（%）");
            rowTitle.AddCellValue("干扰邻区RSRP平均值");
            rowTitle.AddCellValue("干扰邻区SINR平均值");
            rowTitle.AddCellValue("干扰邻区距离");
            resultNPOIRowList.Add(rowTitle);
            #endregion

            int roadIndex = 0;
            foreach (List<BackgroundResult> bgResultList in bgResultDic.Values)
            {
                roadIndex++;
                NPOIRow resultRow_1 = null;
                int handIndex = 0;
                foreach (BackgroundResult bgResult in bgResultList)
                {
                    handIndex++;
                    int roadMainCellCount = bgResult.GetImageValueInt();
                    int roadNbCellCount = bgResult.GetImageValueInt();
                    int roadInterMainCellCount = bgResult.GetImageValueInt();
                    int roadInterNbCellCount = bgResult.GetImageValueInt();
                    int roadInterSampleCount = bgResult.GetImageValueInt();
                    float roadInterSampleRate = bgResult.GetImageValueFloat();

                    if (resultRow_1 == null)
                    {
                        resultRow_1 = new NPOIRow();
                        resultNPOIRowList.Add(resultRow_1);

                        resultRow_1.AddCellValue(roadIndex);
                        resultRow_1.AddCellValue(bgResult.RoadDesc);
                        resultRow_1.AddCellValue(bgResult.DistanceLast);
                        resultRow_1.AddCellValue(bgResult.SampleCount);
                        resultRow_1.AddCellValue(roadMainCellCount);
                        resultRow_1.AddCellValue(roadNbCellCount);
                        resultRow_1.AddCellValue(roadInterMainCellCount);
                        resultRow_1.AddCellValue(roadInterNbCellCount);
                        resultRow_1.AddCellValue(roadInterSampleCount);
                        resultRow_1.AddCellValue(roadInterSampleRate);
                        resultRow_1.AddCellValue(bgResult.FileName);
                    }

                    string mainCellName = bgResult.GetImageValueString();
                    int mainCellID = bgResult.GetImageValueInt();
                    int nbCellCount = bgResult.GetImageValueInt();
                    int interNbCellCount = bgResult.GetImageValueInt();
                    float interNbCellRate = bgResult.GetImageValueFloat();
                    int sampleCount = bgResult.GetImageValueInt();
                    int interSampleCount = bgResult.GetImageValueInt();
                    float interSampleRate = bgResult.GetImageValueFloat();
                    string pdcpUlAvgStr = bgResult.GetImageValueString();
                    string pdcpDlAvgStr = bgResult.GetImageValueString();

                    NPOIRow resultRow_2 = new NPOIRow();
                    resultRow_1.AddSubRow(resultRow_2);

                    resultRow_2.AddCellValue(handIndex);
                    resultRow_2.AddCellValue(mainCellName);
                    resultRow_2.AddCellValue(bgResult.LAC);
                    resultRow_2.AddCellValue(bgResult.CI);
                    resultRow_2.AddCellValue(mainCellID);
                    resultRow_2.AddCellValue(bgResult.BCCH);
                    resultRow_2.AddCellValue(bgResult.BSIC);
                    resultRow_2.AddCellValue(nbCellCount);
                    resultRow_2.AddCellValue(interNbCellCount);
                    resultRow_2.AddCellValue(interNbCellRate);
                    resultRow_2.AddCellValue(sampleCount);
                    resultRow_2.AddCellValue(interSampleCount);
                    resultRow_2.AddCellValue(interSampleRate);
                    resultRow_2.AddCellValue(bgResult.RxLevMean);
                    resultRow_2.AddCellValue(bgResult.RxQualMean);
                    resultRow_2.AddCellValue(pdcpUlAvgStr);
                    resultRow_2.AddCellValue(pdcpDlAvgStr);

                    for (int i = 0; i < interNbCellCount; i++)
                    {
                        string nbCellName = bgResult.GetImageValueString();
                        int nbCellTac = bgResult.GetImageValueInt();
                        int nbCellEci = bgResult.GetImageValueInt();
                        int nbCellCellID = bgResult.GetImageValueInt();
                        int nbCellEarfcn = bgResult.GetImageValueInt();
                        int nbCellPci = bgResult.GetImageValueInt();
                        int nbCellSampleCount = bgResult.GetImageValueInt();
                        int nbInterSampleCount = bgResult.GetImageValueInt();
                        float nbInterSampleRate = bgResult.GetImageValueFloat();
                        string nbRsrpAvgStr = bgResult.GetImageValueString();
                        string nbSinrAvgStr = bgResult.GetImageValueString();
                        float nbDistance = bgResult.GetImageValueFloat();

                        NPOIRow resultRow_3 = new NPOIRow();
                        resultRow_2.AddSubRow(resultRow_3);

                        resultRow_3.AddCellValue(i + 1);
                        resultRow_3.AddCellValue(nbCellName);
                        resultRow_3.AddCellValue(nbCellTac);
                        resultRow_3.AddCellValue(nbCellEci);
                        resultRow_3.AddCellValue(nbCellCellID);
                        resultRow_3.AddCellValue(nbCellEarfcn);
                        resultRow_3.AddCellValue(nbCellPci);
                        resultRow_3.AddCellValue(nbCellSampleCount);
                        resultRow_3.AddCellValue(nbInterSampleCount);
                        resultRow_3.AddCellValue(nbInterSampleRate);
                        resultRow_3.AddCellValue(nbRsrpAvgStr);
                        resultRow_3.AddCellValue(nbSinrAvgStr);
                        resultRow_3.AddCellValue(nbDistance);
                    }
                }
            }
            return resultNPOIRowList;
        }
        #endregion
    }

    public class LTEModRoadQuery : ModRoadQueryBase
    {
        public LTEModRoadQuery(MainModel mainModel) : base(mainModel)
        {
        }

        public override string Name
        {
            get { return "LTE模三干扰道路"; }
        }

        public override List<string> Columns
        {
            get
            {
                return new List<string>()
                {
                    "isampleid",
                    "itime",
                    "ilongitude",
                    "ilatitude",
                    "lte_TAC",
                    "lte_ECI",
                    "lte_EARFCN",
                    "lte_PCI",
                    "lte_RSRQ",
                    "lte_RSRP",
                    "lte_RSSI",
                    "lte_SINR",
                    "lte_NCell_EARFCN",
                    "lte_NCell_PCI",
                    "lte_NCell_RSRP",
                    "lte_NCell_RSRQ",
                    "lte_NCell_RSSI",
                    "lte_NCell_SINR",
                    "lte_PDCP_DL",
                };
            }
        }

        public override ModRoadStaterBase Stater
        {
            get { return this.stater; }
        }

        public override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22027, "分析");//////
        }

        public override bool isValidCondition()
        {
            if (mainModel.IsBackground || mainModel.QueryFromBackground)
            {
                stater = new LTEModRoadStater(mainModel, cond);
                return true;
            }

            if (setForm == null || setForm.IsDisposed)
            {
                setForm = new LTEModRoadSettingForm();
            }
            if (setForm.ShowDialog() != System.Windows.Forms.DialogResult.OK)
            {
                return false;
            }

            cond = setForm.GetCondition();
            stater = new LTEModRoadStater(mainModel, cond);
            curRoad = null;
            return true;
        }
    
        public override void FireShowForm()
        {
            mainModel.FireSetDefaultMapSerialTheme("TD_LTE_RSRP", "LTE_FDD:RSRP");

            resultForm = mainModel.GetObjectFromBlackboard(typeof(LTEModRoadResultForm).FullName) as LTEModRoadResultForm;
            if (resultForm == null || resultForm.IsDisposed)
            {
                resultForm = new LTEModRoadResultForm(mainModel);
            }
            resultForm.FillData(stater);
            if (!resultForm.Visible)
            {
                resultForm.Show(mainModel.MainForm);
            }
        }

        protected override ModRoadItemBase CreateNewRoad(TestPoint firstPoint, string fileName)
        {
            return new LTEModRoadItem(firstPoint, fileName, cond);
        }

        public LTEModRoadStater stater { get; set; }
        public LTEModRoadCondition cond { get; set; }
        private LTEModRoadSettingForm setForm;
        private LTEModRoadResultForm resultForm { get; set; }
    }
    public class LTEModRoadQuery_FDD : LTEModRoadQuery
    {
        public LTEModRoadQuery_FDD(MainModel mainModel)
            : base(mainModel)
        {

        }

        public override string Name
        {
            get { return "LTE_FDD模三干扰道路"; }
        }

        public override List<string> Columns
        {
            get
            {
                return new List<string>()
                {
                    "isampleid",
                    "itime",
                    "ilongitude",
                    "ilatitude",
                    "lte_fdd_TAC",
                    "lte_fdd_ECI",
                    "lte_fdd_EARFCN",
                    "lte_fdd_PCI",
                    "lte_fdd_RSRQ",
                    "lte_fdd_RSRP",
                    "lte_fdd_RSSI",
                    "lte_fdd_SINR",
                    "lte_fdd_NCell_EARFCN",
                    "lte_fdd_NCell_PCI",
                    "lte_fdd_NCell_RSRP",
                    "lte_fdd_NCell_RSRQ",
                    "lte_fdd_NCell_RSSI",
                    "lte_fdd_NCell_SINR",
                    "lte_fdd_PDCP_DL",
                };
            }
        }
        public override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 26000, 26030, this.Name);//////
        }
    }

    public class LTE_FDDModRoadQueryByFile : ModRoadQueryByFile
    {
        public LTE_FDDModRoadQueryByFile(MainModel mainModel)
            : base(mainModel, new LTEModRoadQuery_FDD(mainModel))
        {

        }
    }

    public class LTE_FDDModRoadQueryByRegion : ModRoadQueryByRegion
    {
        public LTE_FDDModRoadQueryByRegion(MainModel mainModel)
            : base(mainModel, new LTEModRoadQuery_FDD(mainModel))
        {
            queryer = new LTEModRoadQuery(mainModel);
        }
    }
}
