﻿using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.ZTFunc;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.Model
{
    public static class NREventHelper
    {
        public static NRHandoverEventHelper HandoverHelper { get; } = new NRHandoverEventHelper();

        public static NRVoiceEventHelper VoiceHelper { get; } = new NRVoiceEventHelper();

        public static NRServiceEventHelper ServiceHelper { get; } = new NRServiceEventHelper();

        private static bool isSetLongCI = false;

        public static void SetLongCI(List<ColumnDefItem> colList)
        {
            if (!isSetLongCI)
            {
                List<ColumnDefItem> cdfList = colList.FindAll(x => x.showName == "CI" || x.showName == "TargetCI");
                if (cdfList != null)
                {
                    foreach (var cdf in cdfList)
                    {
                        cdf.vType = E_VType.E_Int64;
                    }
                }
                isSetLongCI = true;
            }
        }

        public static void ReSetIntCI(List<ColumnDefItem> colList)
        {
            if (isSetLongCI)
            {
                List<ColumnDefItem> cdfList = colList.FindAll(x => x.showName == "CI" || x.showName == "TargetCI");
                if (cdfList != null)
                {
                    foreach (var cdf in cdfList)
                    {
                        cdf.vType = E_VType.E_Int;
                    }
                }
                isSetLongCI = false;
            }
        }
    }

    #region NR切换事件Helper
    public enum NRHandOverType
    {
        SA,
        NSA,
        NSANR,
        NSALTE,
        NSANRLTE,
        LTE,
        UNKNOWN
    }

    public class NRHandoverEventHelper
    {
        #region init
        public NSANRHandOverEvent NSANR { get; set; } = new NSANRHandOverEvent();
        public NSALTEHandOverEvent NSALTE { get; set; } = new NSALTEHandOverEvent();
        public SAHandOverEvent SA { get; set; } = new SAHandOverEvent();
        public LTEHandOverEvent LTE { get; set; } = new LTEHandOverEvent();

        private readonly List<int> lteHandoverEvtList = initLteHandoverEvt();
        private readonly List<int> nsaHandoverEvtList = initNSAHandoverEvt();
        private readonly List<int> nsaLTEHandoverEvtList = initNSALTEHandoverEvt();
        private readonly List<int> nsaNRHandoverEvtList = initNSANRHandoverEvt();
        private readonly List<int> nsaNRLTEHandoverEvtList = initNSANRLTEHandoverEvt();
        private readonly List<int> saHandoverEvtList = initSAHandoverEvt();

        private static List<int> initLteHandoverEvt()
        {
            List<int> list = new List<int>();
            list.Add((int)NREventManager.LTEInterHandoverRequest);
            list.Add((int)NREventManager.LTEInterHandoverSuccess);
            list.Add((int)NREventManager.LTEInterHandoverFailure);
            list.Add((int)NREventManager.LTEIntraHandoverRequest);
            list.Add((int)NREventManager.LTEIntraHandoverSuccess);
            list.Add((int)NREventManager.LTEIntraHandoverFailure);
            return list;
        }

        private static List<int> initNSAHandoverEvt()
        {
            List<int> list = new List<int>();
            list.AddRange(initNSALTEHandoverEvt());
            list.AddRange(initNSANRHandoverEvt());
            list.AddRange(initNSANRLTEHandoverEvt());
            return list;
        }

        private static List<int> initNSALTEHandoverEvt()
        {
            List<int> list = new List<int>();
            list.Add((int)NREventManager.DiffLTESameNRHandoverRequest);
            list.Add((int)NREventManager.DiffLTESameNRHandoverSuccess);
            list.Add((int)NREventManager.DiffLTESameNRHandoverFailure);
            return list;
        }

        private static List<int> initNSANRHandoverEvt()
        {
            List<int> list = new List<int>();
            list.Add((int)NREventManager.SameLTEDiffNRHandoverRequest);
            list.Add((int)NREventManager.SameLTEDiffNRHandoverSuccess);
            list.Add((int)NREventManager.SameLTEDiffNRHandoverFailure);
            return list;
        }

        private static List<int> initNSANRLTEHandoverEvt()
        {
            List<int> list = new List<int>();
            list.Add((int)NREventManager.DiffLTEDiffNRHandoverRequest);
            list.Add((int)NREventManager.DiffLTEDiffNRHandoverSuccess);
            list.Add((int)NREventManager.DiffLTEDiffNRHandoverFailure);
            return list;
        }

        private static List<int> initSAHandoverEvt()
        {
            List<int> list = new List<int>();
            list.Add((int)NREventManager.NRInterHandoverRequest);
            list.Add((int)NREventManager.NRInterHandoverSuccess);
            list.Add((int)NREventManager.NRInterHandoverFailure);
            list.Add((int)NREventManager.NRIntraHandoverRequest);
            list.Add((int)NREventManager.NRIntraHandoverSuccess);
            list.Add((int)NREventManager.NRIntraHandoverFailure);
            return list;
        }
        #endregion

        /// <summary>
        /// 将和Diff LTE Same NR Handover事件相同时间点的 LTE Handover事件剔除
        /// </summary>
        /// <param name="evtList"></param>
        public void FilterHandoverEvents(List<Event> evtList)
        {
            List<int> diffLTESameNRHandoverEvtIDList = new List<int>()
            {
                (int)NREventManager.DiffLTESameNRHandoverRequest,
                (int)NREventManager.DiffLTESameNRHandoverSuccess,
                (int)NREventManager.DiffLTESameNRHandoverFailure,
            };
            List<Event> nsaLteEvtList = new List<Event>();

            List<int> lteHandoverEvtIDList = new List<int>()
            {
                (int)NREventManager.LTEInterHandoverRequest,
                (int)NREventManager.LTEInterHandoverSuccess,
                (int)NREventManager.LTEInterHandoverFailure,
                (int)NREventManager.LTEIntraHandoverRequest,
                (int)NREventManager.LTEIntraHandoverSuccess,
                (int)NREventManager.LTEIntraHandoverFailure,
            };
            List<Event> lteEvtList = new List<Event>();

            foreach (var evt in evtList)
            {
                if (diffLTESameNRHandoverEvtIDList.Contains(evt.ID))
                {
                    nsaLteEvtList.Add(evt);
                }
                else if (lteHandoverEvtIDList.Contains(evt.ID))
                {
                    lteEvtList.Add(evt);
                }
            }

            foreach (var nrEvt in nsaLteEvtList)
            {
                foreach (var lteEvt in lteEvtList)
                {
                    if (nrEvt.DateTime == lteEvt.DateTime)
                    {
                        evtList.Remove(lteEvt);
                    }
                }
            }
        }

        #region 判断切换事件
        public bool JudgeHandoverSuccess(int evtID, bool isAnaLte)
        {
            if (evtID == (int)NREventManager.DiffLTESameNRHandoverSuccess
                || evtID == (int)NREventManager.SameLTEDiffNRHandoverSuccess
                || evtID == (int)NREventManager.DiffLTEDiffNRHandoverSuccess)
            {
                return true;
            }
            else if (isAnaLte && (evtID == (int)NREventManager.LTEInterHandoverSuccess
                || evtID == (int)NREventManager.LTEIntraHandoverSuccess))
            {
                return true;
            }
            else if (evtID == (int)NREventManager.NRInterHandoverSuccess
                || evtID == (int)NREventManager.NRIntraHandoverSuccess)
            {
                return true;
            }
            return false;
        }

        public bool JudgeIsNRHandoverEvt(int evtID)
        {
            if (lteHandoverEvtList.Contains(evtID)
                || nsaNRLTEHandoverEvtList.Contains(evtID))
            {
                return false;
            }
            return true;
        }

        public bool JudgeIncludeLTEHandover(NRHandOverType type)
        {
            if (type == NRHandOverType.LTE
                || type == NRHandOverType.NSALTE
                || type == NRHandOverType.NSANRLTE)
            {
                return true;
            }
            return false;
        }

        public bool JudgeIncludeNRHandover(NRHandOverType type)
        {
            if (type == NRHandOverType.SA
                || type == NRHandOverType.NSANR
                || type == NRHandOverType.NSANRLTE)
            {
                return true;
            }
            return false;
        }
        #endregion

        #region 获取切换信息
        #region 初始化不同类型的切换事件
        public List<int> GetHandoverRequestEvt(bool isAnaLte)
        {
            List<int> list = new List<int>();
            if (isAnaLte)
            {
                list.Add((int)NREventManager.LTEInterHandoverRequest);
                list.Add((int)NREventManager.LTEIntraHandoverRequest);
            }

            list.Add((int)NREventManager.DiffLTESameNRHandoverRequest);
            list.Add((int)NREventManager.SameLTEDiffNRHandoverRequest);
            list.Add((int)NREventManager.DiffLTEDiffNRHandoverRequest);

            list.Add((int)NREventManager.NRInterHandoverRequest);
            list.Add((int)NREventManager.NRIntraHandoverRequest);
            return list;
        }

        public List<int> GetHandoverSuccessEvt(bool isAnaLte)
        {
            List<int> list = new List<int>();
            if (isAnaLte)
            {
                list.Add((int)NREventManager.LTEInterHandoverSuccess);
                list.Add((int)NREventManager.LTEIntraHandoverSuccess);
            }

            list.Add((int)NREventManager.DiffLTESameNRHandoverSuccess);
            list.Add((int)NREventManager.SameLTEDiffNRHandoverSuccess);
            list.Add((int)NREventManager.DiffLTEDiffNRHandoverSuccess);

            list.Add((int)NREventManager.NRInterHandoverSuccess);
            list.Add((int)NREventManager.NRIntraHandoverSuccess);
            return list;
        }

        public List<int> GetHandoverFailEvt(bool isAnaLte)
        {
            List<int> list = new List<int>();
            if (isAnaLte)
            {
                list.Add((int)NREventManager.LTEInterHandoverFailure);
                list.Add((int)NREventManager.LTEIntraHandoverFailure);
            }
            list.Add((int)NREventManager.DiffLTESameNRHandoverFailure);
            list.Add((int)NREventManager.SameLTEDiffNRHandoverFailure);
            list.Add((int)NREventManager.DiffLTEDiffNRHandoverFailure);

            list.Add((int)NREventManager.NRInterHandoverFailure);
            list.Add((int)NREventManager.NRIntraHandoverFailure);
            return list;
        }

        public List<int> GetIncludeNRHandover()
        {
            List<int> list = new List<int>();
            list.Add((int)NREventManager.SameLTEDiffNRHandoverSuccess);
            list.Add((int)NREventManager.DiffLTEDiffNRHandoverSuccess);
            list.Add((int)NREventManager.NRInterHandoverSuccess);
            list.Add((int)NREventManager.NRIntraHandoverSuccess);
            return list;
        }

        public List<int> GetIncludeLTEHandover()
        {
            List<int> list = new List<int>();
            list.Add((int)NREventManager.LTEInterHandoverSuccess);
            list.Add((int)NREventManager.LTEIntraHandoverSuccess);
            list.Add((int)NREventManager.DiffLTESameNRHandoverSuccess);
            list.Add((int)NREventManager.DiffLTEDiffNRHandoverSuccess);
            return list;
        }
        #endregion

        #region 获取事件切换前后小区信息
        public HandOverCellInfo GetHandOverCellInfo(Event evt)
        {
            HandOverCellInfo info = new HandOverCellInfo();
            NRHandOverType type = GetHandoverType(evt.ID, false);
            if (type == NRHandOverType.NSA)
            {
                info.NRSrcCell = NSANR.GetSrcCellInfo(evt);
                info.NRTarCell = NSANR.GetTarCellInfo(evt);

                info.LTESrcCell = NSALTE.GetSrcCellInfo(evt);
                info.LTETarCell = NSALTE.GetTarCellInfo(evt);
            }
            else if (type == NRHandOverType.SA)
            {
                info.NRSrcCell = SA.GetSrcCellInfo(evt);
                info.NRTarCell = SA.GetTarCellInfo(evt);
            }
            else if (type == NRHandOverType.LTE)
            {
                info.LTESrcCell = LTE.GetSrcCellInfo(evt);
                info.LTETarCell = LTE.GetTarCellInfo(evt);
            }

            return info;
        }

        public class HandOverCellInfo
        {
            public HandOverEventBase.CellInfo NRSrcCell { get; set; } = new HandOverEventBase.CellInfo();
            public HandOverEventBase.CellInfo NRTarCell { get; set; } = new HandOverEventBase.CellInfo();

            public HandOverEventBase.CellInfo LTESrcCell { get; set; } = new HandOverEventBase.CellInfo();
            public HandOverEventBase.CellInfo LTETarCell { get; set; } = new HandOverEventBase.CellInfo();
        }
        #endregion

        public NRHandOverType GetHandoverType(int evtID, bool diffNSA)
        {
            if (lteHandoverEvtList.Contains(evtID))
            {
                return NRHandOverType.LTE;
            }
            else if (nsaHandoverEvtList.Contains(evtID))
            {
                if (diffNSA)
                {
                    if (nsaNRLTEHandoverEvtList.Contains(evtID))
                    {
                        return NRHandOverType.NSANRLTE;
                    }
                    else if (nsaNRHandoverEvtList.Contains(evtID))
                    {
                        return NRHandOverType.NSANR;
                    }
                    else if (nsaLTEHandoverEvtList.Contains(evtID))
                    {
                        return NRHandOverType.NSALTE;
                    }
                }
                return NRHandOverType.NSA;
            }
            else if (saHandoverEvtList.Contains(evtID))
            {
                return NRHandOverType.SA;
            }

            return NRHandOverType.UNKNOWN;
        }

        public string GetHandoverTypeDesc(NRHandOverType type)
        {
            switch (type)
            {
                case NRHandOverType.LTE:
                    return "LTE";
                case NRHandOverType.NSA:
                    return "NSA";
                case NRHandOverType.NSALTE:
                    return "NSA-LTE";
                case NRHandOverType.NSANR:
                    return "NSA-NR";
                case NRHandOverType.NSANRLTE:
                    return "NSA-NR,NSA-LTE";
                case NRHandOverType.SA:
                    return "SA";
                default:
                    return "未知";
            }
        }
        #endregion
    }

    public abstract class HandOverEventBase
    {
        #region 切换前
        public virtual int? GetSrcArfcn(Event evt)
        {
            throw new NotImplementedException();
        }
        public virtual int? GetSrcPci(Event evt)
        {
            throw new NotImplementedException();
        }
        public virtual int? GetSrcTac(Event evt)
        {
            throw new NotImplementedException();
        }
        public virtual long? GetSrcNci(Event evt)
        {
            throw new NotImplementedException();
        }
        #endregion

        #region 切换后
        public virtual int? GetTarArfcn(Event evt)
        {
            throw new NotImplementedException();
        }
        public virtual int? GetTarPci(Event evt)
        {
            throw new NotImplementedException();
        }
        public virtual int? GetTarTac(Event evt)
        {
            throw new NotImplementedException();
        }
        public virtual long? GetTarNci(Event evt)
        {
            throw new NotImplementedException();
        }
        #endregion

        public virtual CellInfo GetSrcCellInfo(Event evt)
        {
            CellInfo info = new CellInfo();
            info.ARFCN = getValidData(GetSrcArfcn(evt));
            info.PCI = getValidData(GetSrcPci(evt));
            info.Cell = getHandoverCell(evt, info);
            return info;
        }

        public virtual CellInfo GetTarCellInfo(Event evt)
        {
            CellInfo info = new CellInfo();
            info.ARFCN = getValidData(GetTarArfcn(evt));
            info.PCI = getValidData(GetTarPci(evt));
            info.Cell = getHandoverCell(evt, info);
            return info;
        }

        protected int getValidData(int? data)
        {
            if (data == null)
            {
                return -1;
            }
            return (int)data;
        }

        protected virtual ICell getHandoverCell(Event evt, CellInfo info)
        {
            throw new NotImplementedException();
        }

        public int? GetEvtIntValue(Event evt, string value)
        {
            object obj = evt[value];
            if (obj == null)
            {
                return null;
            }
            else
            {
                int res;
                if (int.TryParse(obj.ToString(), out res))
                {
                    return res;
                }
                else
                {
                    return null;
                }
            }
        }

        public class CellInfo
        {
            public ICell Cell { get; set; }

            public int TAC { get; set; } = -1;
            public long NCI { get; set; } = -1;
            public int ARFCN { get; set; } = -1;
            public int PCI { get; set; } = -1;
        }
    }

    public class NSANRHandOverEvent : HandOverEventBase
    {
        public override int? GetSrcArfcn(Event evt)
        {
            int? arfcn = GetEvtIntValue(evt, "Value3");
            return arfcn;
        }
        public override int? GetSrcPci(Event evt)
        {
            int? pci = GetEvtIntValue(evt, "Value4");
            return pci;
        }
        public override int? GetTarArfcn(Event evt)
        {
            int? arfcn = GetEvtIntValue(evt, "Value8");
            return arfcn;
        }
        public override int? GetTarPci(Event evt)
        {
            int? pci = GetEvtIntValue(evt, "Value9");
            return pci;
        }

        protected override ICell getHandoverCell(Event evt, CellInfo info)
        {
            NRCell cell;
            CellManager mng = CellManager.GetInstance();
            cell = mng.GetNearestNRCellByARFCNPCI(evt.DateTime, info.ARFCN, info.PCI, evt.Longitude, evt.Latitude);
            if (cell != null)
            {
                info.TAC = cell.TAC;
                info.NCI = cell.NCI;
            }
            return cell;
        }
    }

    public class NSALTEHandOverEvent : HandOverEventBase
    {
        public override int? GetSrcArfcn(Event evt)
        {
            int? arfcn = GetEvtIntValue(evt, "Value1");
            return arfcn;
        }
        public override int? GetSrcPci(Event evt)
        {
            int? pci = GetEvtIntValue(evt, "Value2");
            return pci;
        }
        public override int? GetTarArfcn(Event evt)
        {
            int? arfcn = GetEvtIntValue(evt, "Value5");
            return arfcn;
        }
        public override int? GetTarPci(Event evt)
        {
            int? pci = GetEvtIntValue(evt, "Value6");
            return pci;
        }

        protected override ICell getHandoverCell(Event evt, CellInfo info)
        {
            LTECell cell;
            CellManager mng = CellManager.GetInstance();
            cell = mng.GetNearestLTECellByEARFCNPCI(evt.DateTime, info.ARFCN, info.PCI, evt.Longitude, evt.Latitude);
            if (cell != null)
            {
                info.TAC = cell.TAC;
                info.NCI = cell.ECI;
            }
            return cell;
        }
    }

    public class SAHandOverEvent : HandOverEventBase
    {
        public override int? GetSrcArfcn(Event evt)
        {
            int? arfcn = GetEvtIntValue(evt, "Value2");
            return arfcn;
        }
        public override int? GetSrcPci(Event evt)
        {
            int? pci = GetEvtIntValue(evt, "Value3");
            return pci;
        }
        public override int? GetTarArfcn(Event evt)
        {
            int? arfcn = GetEvtIntValue(evt, "Value4");
            return arfcn;
        }
        public override int? GetTarPci(Event evt)
        {
            int? pci = GetEvtIntValue(evt, "Value5");
            return pci;
        }

        protected override ICell getHandoverCell(Event evt, CellInfo info)
        {
            NRCell cell;
            CellManager mng = CellManager.GetInstance();
            cell = mng.GetNearestNRCellByARFCNPCI(evt.DateTime, info.ARFCN, info.PCI, evt.Longitude, evt.Latitude);
            if (cell != null)
            {
                info.TAC = cell.TAC;
                info.NCI = cell.NCI;
            }
            return cell;
        }
    }

    public class LTEHandOverEvent : HandOverEventBase
    {
        public override int? GetSrcArfcn(Event evt)
        {
            int? arfcn = GetEvtIntValue(evt, "Value2");
            return arfcn;
        }
        public override int? GetSrcPci(Event evt)
        {
            int? pci = GetEvtIntValue(evt, "Value3");
            return pci;
        }
        public override int? GetTarArfcn(Event evt)
        {
            int? arfcn = GetEvtIntValue(evt, "Value4");
            return arfcn;
        }
        public override int? GetTarPci(Event evt)
        {
            int? pci = GetEvtIntValue(evt, "Value5");
            return pci;
        }

        protected override ICell getHandoverCell(Event evt, CellInfo info)
        {
            LTECell cell;
            CellManager mng = CellManager.GetInstance();
            cell = mng.GetNearestLTECellByEARFCNPCI(evt.DateTime, info.ARFCN, info.PCI, evt.Longitude, evt.Latitude);
            if (cell != null)
            {
                info.TAC = cell.TAC;
                info.NCI = cell.ECI;
            }
            return cell;
        }
    }
    #endregion

    #region NR语音事件Helper
    public class NRVoiceEventHelper
    {
        #region CallAttempt
        public List<int> MoCallAttemptEventIds { get; } = new List<int>
        {
            (int)NREventManager.VoLTE_Audio_MO_Call_Attempt,
            (int)NREventManager.VoLTE_Video_MO_Call_Attempt,
            (int)NREventManager.CSFB_MO_Call_Attempt,
            (int)NREventManager.EPSFB_Audio_MO_Call_Attempt,
            (int)NREventManager.GSM_MO_Call_Attempt
        };

        public List<int> MtCallAttemptEventIds { get; } = new List<int>
        {
            (int)NREventManager.VoLTE_Audio_MT_Call_Attempt,
            (int)NREventManager.VoLTE_Video_MT_Call_Attempt,
            (int)NREventManager.CSFB_MT_Call_Attempt,
            (int)NREventManager.EPSFB_Audio_MT_Call_Attempt,
            (int)NREventManager.GSM_MT_Call_Attempt
        };
        #endregion

        #region CallOver
        public List<int> MoCallOverEventIds { get; } = new List<int>
        {
            (int)NREventManager.VoLTE_Audio_MO_Call_End,
            (int)NREventManager.VoLTE_Audio_MO_Drop_Call,
            (int)NREventManager.VoLTE_Audio_MO_Block_Call,
            (int)NREventManager.VoLTE_eSRVCC_Audio_MO_Call_End,
            (int)NREventManager.VoLTE_eSRVCC_Audio_MO_Drop_Call,
            (int)NREventManager.VoLTE_eSRVCC_Audio_MO_Block_Call,
            (int)NREventManager.VoLTE_Video_MO_Call_End,
            (int)NREventManager.VoLTE_Video_MO_Drop_Call,
            (int)NREventManager.VoLTE_Video_MO_Block_Call,
            (int)NREventManager.VoLTE_eSRVCC_Video_MO_Call_End,
            (int)NREventManager.VoLTE_eSRVCC_Video_MO_Drop_Call,
            (int)NREventManager.VoLTE_eSRVCC_Video_MO_Block_Call,
            (int)NREventManager.CSFB_MO_Drop_Call,
            (int)NREventManager.CSFB_MO_Block_Call,
            (int)NREventManager.CSFB_MO_Call_End,

            (int)NREventManager.EPSFB_Audio_MO_Call_End,
            (int)NREventManager.EPSFB_Audio_MO_Drop_Call,
            (int)NREventManager.EPSFB_Audio_MO_Block_Call,
            (int)NREventManager.EPSFB_Video_MO_Call_End,
            (int)NREventManager.EPSFB_Video_MO_Drop_Call,
            (int)NREventManager.EPSFB_Video_MO_Block_Call,
            (int)NREventManager.EPSFB_eSRVCC_Audio_MO_Call_End,
            (int)NREventManager.EPSFB_eSRVCC_Audio_MO_Drop_Call,
            (int)NREventManager.EPSFB_eSRVCC_Audio_MO_Block_Call,
            (int)NREventManager.EPSFB_eSRVCC_Video_MO_Call_End,
            (int)NREventManager.EPSFB_eSRVCC_Video_MO_Drop_Call,
            (int)NREventManager.EPSFB_eSRVCC_Video_MO_Block_Call,

            (int)NREventManager.GSM_MO_Drop_Call,
            (int)NREventManager.GSM_MO_Block_Call,
            (int)NREventManager.GSM_MO_Call_End,
        };

        public List<int> MtCallOverEventIds { get; } = new List<int>
        {
            (int)NREventManager.VoLTE_Audio_MT_Call_End,
            (int)NREventManager.VoLTE_Audio_MT_Drop_Call,
            (int)NREventManager.VoLTE_Audio_MT_Block_Call,
            (int)NREventManager.VoLTE_eSRVCC_Audio_MT_Call_End,
            (int)NREventManager.VoLTE_eSRVCC_Audio_MT_Drop_Call,
            (int)NREventManager.VoLTE_eSRVCC_Audio_MT_Block_Call,
            (int)NREventManager.VoLTE_Video_MT_Call_End,
            (int)NREventManager.VoLTE_Video_MT_Drop_Call,
            (int)NREventManager.VoLTE_Video_MT_Block_Call,
            (int)NREventManager.VoLTE_eSRVCC_Video_MT_Call_End,
            (int)NREventManager.VoLTE_eSRVCC_Video_MT_Drop_Call,
            (int)NREventManager.VoLTE_eSRVCC_Video_MT_Block_Call,
            (int)NREventManager.CSFB_MT_Drop_Call,
            (int)NREventManager.CSFB_MT_Block_Call,
            (int)NREventManager.CSFB_MT_Call_End,

            (int)NREventManager.EPSFB_Audio_MT_Call_End,
            (int)NREventManager.EPSFB_Audio_MT_Drop_Call,
            (int)NREventManager.EPSFB_Audio_MT_Block_Call,
            (int)NREventManager.EPSFB_Video_MT_Call_End,
            (int)NREventManager.EPSFB_Video_MT_Drop_Call,
            (int)NREventManager.EPSFB_Video_MT_Block_Call,
            (int)NREventManager.EPSFB_eSRVCC_Audio_MT_Call_End,
            (int)NREventManager.EPSFB_eSRVCC_Audio_MT_Drop_Call,
            (int)NREventManager.EPSFB_eSRVCC_Audio_MT_Block_Call,
            (int)NREventManager.EPSFB_eSRVCC_Video_MT_Call_End,
            (int)NREventManager.EPSFB_eSRVCC_Video_MT_Drop_Call,
            (int)NREventManager.EPSFB_eSRVCC_Video_MT_Block_Call,

            (int)NREventManager.GSM_MT_Drop_Call,
            (int)NREventManager.GSM_MT_Block_Call,
            (int)NREventManager.GSM_MT_Call_End,
        };
        #endregion

        #region CallBlock
        public List<int> MoCallBlockEventIds { get; } = new List<int>
        {
            (int)NREventManager.VoLTE_Audio_MO_Block_Call,
            (int)NREventManager.VoLTE_eSRVCC_Audio_MO_Block_Call,
            (int)NREventManager.VoLTE_Video_MO_Block_Call,
            (int)NREventManager.VoLTE_eSRVCC_Video_MO_Block_Call,
            (int)NREventManager.CSFB_MO_Block_Call,
            (int)NREventManager.EPSFB_Audio_MO_Block_Call,
            (int)NREventManager.EPSFB_Video_MO_Block_Call,
            (int)NREventManager.EPSFB_eSRVCC_Audio_MO_Block_Call,
            (int)NREventManager.EPSFB_eSRVCC_Video_MO_Block_Call,
            (int)NREventManager.GSM_MO_Block_Call,
        };

        public List<int> MtCallBlockEventIds { get; } = new List<int>
        {
            (int)NREventManager.VoLTE_Audio_MT_Block_Call,
            (int)NREventManager.VoLTE_eSRVCC_Audio_MT_Block_Call,
            (int)NREventManager.VoLTE_Video_MT_Block_Call,
            (int)NREventManager.VoLTE_eSRVCC_Video_MT_Block_Call,
            (int)NREventManager.CSFB_MT_Block_Call,
            (int)NREventManager.EPSFB_Audio_MT_Block_Call,
            (int)NREventManager.EPSFB_Video_MT_Block_Call,
            (int)NREventManager.EPSFB_eSRVCC_Audio_MT_Block_Call,
            (int)NREventManager.EPSFB_eSRVCC_Video_MT_Block_Call,
            (int)NREventManager.GSM_MT_Block_Call,
        };
        #endregion

        #region CallDrop
        public List<int> MoCallDropEventIds { get; } = new List<int>
        {
            (int)NREventManager.CSFB_MO_Drop_Call,
            (int)NREventManager.VoLTE_eSRVCC_Video_MO_Drop_Call,
            (int)NREventManager.VoLTE_Video_MO_Drop_Call,
            (int)NREventManager.VoLTE_eSRVCC_Audio_MO_Drop_Call,
            (int)NREventManager.VoLTE_Audio_MO_Drop_Call,
            (int)NREventManager.EPSFB_Audio_MO_Drop_Call,
            (int)NREventManager.EPSFB_Video_MO_Drop_Call,
            (int)NREventManager.EPSFB_eSRVCC_Audio_MO_Drop_Call,
            (int)NREventManager.EPSFB_eSRVCC_Video_MO_Drop_Call,
            (int)NREventManager.GSM_MO_Drop_Call,
        };

        public List<int> MtCallDropEventIds { get; } = new List<int>
        {
            (int)NREventManager.CSFB_MT_Drop_Call,
            (int)NREventManager.VoLTE_eSRVCC_Video_MT_Drop_Call,
            (int)NREventManager.VoLTE_Video_MT_Drop_Call,
            (int)NREventManager.VoLTE_eSRVCC_Audio_MT_Drop_Call,
            (int)NREventManager.VoLTE_Audio_MT_Drop_Call,
            (int)NREventManager.EPSFB_Audio_MT_Drop_Call,
            (int)NREventManager.EPSFB_Video_MT_Drop_Call,
            (int)NREventManager.EPSFB_eSRVCC_Audio_MT_Drop_Call,
            (int)NREventManager.EPSFB_eSRVCC_Video_MT_Drop_Call,
            (int)NREventManager.GSM_MT_Drop_Call,
        };
        #endregion
    }
    #endregion


    #region NR业务事件Helper
    public class NRServiceEventHelper
    {

    }
    #endregion

    public enum NREventManager
    {
        #region 切换
        LTEInterHandoverRequest = 9076,
        LTEInterHandoverSuccess = 9077,
        LTEInterHandoverFailure = 9078,
        LTEIntraHandoverRequest = 9079,
        LTEIntraHandoverSuccess = 9080,
        LTEIntraHandoverFailure = 9081,

        DiffLTESameNRHandoverRequest = 9091,
        SameLTEDiffNRHandoverRequest = 9092,
        DiffLTEDiffNRHandoverRequest = 9093,
        DiffLTESameNRHandoverSuccess = 9094,
        SameLTEDiffNRHandoverSuccess = 9095,
        DiffLTEDiffNRHandoverSuccess = 9096,
        DiffLTESameNRHandoverFailure = 9097,
        SameLTEDiffNRHandoverFailure = 9098,
        DiffLTEDiffNRHandoverFailure = 9099,

        NRInterHandoverRequest = 9295,
        NRInterHandoverSuccess = 9296,
        NRInterHandoverFailure = 9297,
        NRIntraHandoverRequest = 9298,
        NRIntraHandoverSuccess = 9299,
        NRIntraHandoverFailure = 9300,

        IRAT_NR_LTE_HO_Request = 9540,
        IRAT_NR_LTE_HO_Success = 9541,
        IRAT_NR_LTE_HO_Failure = 9542,
        IRAT_LTE_NR_HO_Request = 9549,
        IRAT_LTE_NR_HO_Success = 9550,
        IRAT_LTE_NR_HO_Failure = 9551,
        #endregion

        #region 语音
        VoLTE_Audio_MO_Call_Attempt = 9149,
        VoLTE_Audio_MT_Call_Attempt = 9150,
        VoLTE_Audio_MO_Call_Established = 9153,
        VoLTE_Audio_MT_Call_Established = 9154,
        VoLTE_Audio_MO_Call_End = 9155,
        VoLTE_Audio_MT_Call_End = 9156,
        VoLTE_Audio_MO_Drop_Call = 9157,
        VoLTE_Audio_MT_Drop_Call = 9158,
        VoLTE_Audio_MO_Block_Call = 9159,
        VoLTE_Audio_MT_Block_Call = 9160,

        VoLTE_eSRVCC_Audio_MO_Call_Established = 9161,
        VoLTE_eSRVCC_Audio_MT_Call_Established = 9162,
        VoLTE_eSRVCC_Audio_MO_Call_End = 9163,
        VoLTE_eSRVCC_Audio_MT_Call_End = 9164,
        VoLTE_eSRVCC_Audio_MO_Drop_Call = 9165,
        VoLTE_eSRVCC_Audio_MT_Drop_Call = 9166,
        VoLTE_eSRVCC_Audio_MO_Block_Call = 9167,
        VoLTE_eSRVCC_Audio_MT_Block_Call = 9168,

        VoLTE_eSRVCC_HandOver_Success = 9172,

        VoLTE_Video_MO_Call_Attempt = 9187,
        VoLTE_Video_MT_Call_Attempt = 9188,
        VoLTE_Video_MO_Call_Established = 9191,
        VoLTE_Video_MT_Call_Established = 9192,
        VoLTE_Video_MO_Call_End = 9193,
        VoLTE_Video_MT_Call_End = 9194,
        VoLTE_Video_MO_Drop_Call = 9195,
        VoLTE_Video_MT_Drop_Call = 9196,
        VoLTE_Video_MO_Block_Call = 9197,
        VoLTE_Video_MT_Block_Call = 9198,

        VoLTE_eSRVCC_Video_MO_Call_Established = 9199,
        VoLTE_eSRVCC_Video_MT_Call_Established = 9200,
        VoLTE_eSRVCC_Video_MO_Call_End = 9201,
        VoLTE_eSRVCC_Video_MT_Call_End = 9202,
        VoLTE_eSRVCC_Video_MO_Drop_Call = 9203,
        VoLTE_eSRVCC_Video_MT_Drop_Call = 9204,
        VoLTE_eSRVCC_Video_MO_Block_Call = 9205,
        VoLTE_eSRVCC_Video_MT_Block_Call = 9206,

        CSFB_MO_Call_Attempt = 9250,
        CSFB_MT_Call_Attempt = 9251,
        CSFB_MO_Call_Established = 9252,
        CSFB_MT_Call_Established = 9253,
        CSFB_MO_Drop_Call = 9254,
        CSFB_MT_Drop_Call = 9255,
        CSFB_MO_Block_Call = 9256,
        CSFB_MT_Block_Call = 9257,
        CSFB_MO_Call_End = 9258,
        CSFB_MT_Call_End = 9259,

        EPSFB_Audio_MO_Call_Attempt = 9334,
        EPSFB_Audio_MT_Call_Attempt = 9335,
        EPSFB_Audio_MO_Call_Setup = 9336,
        EPSFB_Audio_MT_Call_Setup = 9337,
        EPSFB_Audio_MO_Call_Established = 9338,
        EPSFB_Audio_MT_Call_Established = 9339,
        EPSFB_Audio_MO_Call_End = 9340,
        EPSFB_Audio_MT_Call_End = 9341,
        EPSFB_Audio_MO_Drop_Call = 9342,
        EPSFB_Audio_MT_Drop_Call = 9343,
        EPSFB_Audio_MO_Block_Call = 9344,
        EPSFB_Audio_MT_Block_Call = 9345,

        EPSFB_Video_MO_Call_Attempt = 9346,
        EPSFB_Video_MT_Call_Attempt = 9347,
        EPSFB_Video_MO_Call_Setup = 9348,
        EPSFB_Video_MT_Call_Setup = 9349,
        EPSFB_Video_MO_Call_Established = 9350,
        EPSFB_Video_MT_Call_Established = 9351,
        EPSFB_Video_MO_Call_End = 9352,
        EPSFB_Video_MT_Call_End = 9353,
        EPSFB_Video_MO_Drop_Call = 9354,
        EPSFB_Video_MT_Drop_Call = 9355,
        EPSFB_Video_MO_Block_Call = 9356,
        EPSFB_Video_MT_Block_Call = 9357,

        EPSFB_eSRVCC_Audio_MO_Call_Established = 9358,
        EPSFB_eSRVCC_Audio_MT_Call_Established = 9359,
        EPSFB_eSRVCC_Audio_MO_Call_End = 9360,
        EPSFB_eSRVCC_Audio_MT_Call_End = 9361,
        EPSFB_eSRVCC_Audio_MO_Drop_Call = 9362,
        EPSFB_eSRVCC_Audio_MT_Drop_Call = 9363,
        EPSFB_eSRVCC_Audio_MO_Block_Call = 9364,
        EPSFB_eSRVCC_Audio_MT_Block_Call = 9365,

        EPSFB_eSRVCC_Video_MO_Call_Established = 9366,
        EPSFB_eSRVCC_Video_MT_Call_Established = 9367,
        EPSFB_eSRVCC_Video_MO_Call_End = 9368,
        EPSFB_eSRVCC_Video_MT_Call_End = 9369,
        EPSFB_eSRVCC_Video_MO_Drop_Call = 9370,
        EPSFB_eSRVCC_Video_MT_Drop_Call = 9371,
        EPSFB_eSRVCC_Video_MO_Block_Call = 9372,
        EPSFB_eSRVCC_Video_MT_Block_Call = 9373,

        GSM_MO_Call_Attempt = 9400,
        GSM_MT_Call_Attempt = 9401,
        GSM_MO_Call_Established = 9402,
        GSM_MT_Call_Established = 9403,
        GSM_MO_Drop_Call = 9404,
        GSM_MT_Drop_Call = 9405,
        GSM_MO_Block_Call = 9406,
        GSM_MT_Block_Call = 9407,
        GSM_MO_Call_End = 9408,
        GSM_MT_Call_End = 9409,

        GSM_MO_Call_Alerting = 9410,
        GSM_MT_Call_Alerting = 9411,
        GSM_MO_Call_CM_ReEstablishment = 9412,
        GSM_MT_Call_CM_ReEstablishment = 9413,
        GSM_MO_Drop_Call_ReEstablish = 9414,
        GSM_MT_Drop_Call_ReEstablish = 9415,

        EPSFB_FR_Fail = 9557,
        EPSFB_FR_MoreThan2point5 = 9558,
        EPSFB_FR_LessThan2point5 = 9559,
        #endregion

        #region 业务
        FTPDownloadBegan = 9057,
        FTPDownloadSuccess = 9058,
        FTPDownloadDrop = 9059,
        FTPDownloadFirstData = 9073,
        FTPDownloadUnFinished = 9064,

        HttpRequest = 9620,
        HttpSuccess = 9621,
        HttpDisFail = 9622,
        HttpComplete = 9623,
        HttpIncomplete = 9624,
        HttpFail = 9628,

        DownRequest = 9625,
        DownSuccess = 9626,
        DownDrop = 9627,
        DownloadFail = 9629,

        FlvPlayFinished = 9608,
        VideoRequest = 9640,
        VideoFirstData = 9641,
        VideoRebufferStart = 9642,
        VideoRebufferEnd = 9643,
        VideoLastData = 9644,
        VideoFinish = 9645,
        VideoDrop = 9646,
        VideoReproductionStart = 9647,
        VideoFail = 9648,
        #endregion

        Attach_Request = 9001,
        Attach_Accept = 9002,
        NR_Cell_Add_Request = 9022,
        NR_Cell_Add_Success = 9023,
        Ping_Success = 9282,
        Ping_Fail = 9283,
        MosUnder3dot0Last2Pnt = 9235,
        NR_TAU_Request = 9315,
        NR_TAU_Success = 9316,

        NR_Registration_Request = 9301,
        NR_Registration_Accept = 9302,
        NR_Registration_Reject = 9303,

        #region VONR
        //尝试次数事件
        VoNR_Audio_MO_Call_Attempt = 9834,
        VoNR_Audio_MT_Call_Attempt = 9835,
        //成功次数事件
        VoNR_Audio_MT_Call_Established = 9839,
        VoNR_Audio_MO_Call_End = 9840,
        //失败次数事件
        VoNR_Audio_MT_Drop_Call = 9843,
        VoNR_Audio_MO_Block_Call = 9844,
        #endregion
    }
}
