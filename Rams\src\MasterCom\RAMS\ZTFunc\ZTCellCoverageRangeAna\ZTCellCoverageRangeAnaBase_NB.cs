﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    class ZTCellCoverageRangeAnaBase_NB : ZTCellCoverageRangeAnaBase
    {
        public ZTCellCoverageRangeAnaBase_NB(ServiceName serviceName)
            : base(MainModel.GetInstance())
        {
            ServiceTypes.Clear();
            ServiceTypes = ServiceTypeManager.GetServiceTypesByServiceName(serviceName);
        }

        public override string Name
        {
            get { return "小区覆盖带分析(按小区)"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 34000, 34016, this.Name);
        }
    }
}
