﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Data;
using System.Drawing;

using MasterCom.MTGis;
using MasterCom.MControls;
using MasterCom.Util;

using DevExpress.XtraCharts;
using System.Xml;

namespace MasterCom.RAMS.ZTFunc
{
    public class LteMgrsRsrpRangeStater : LteMgrsStaterBase
    {
        LteMgrsFuncItem tmpFuncItem = null;

        public override void DoStat(LteMgrsFuncItem curFuncItem)
        {
            tmpFuncItem = curFuncItem;
        }

        public override List<LteMgrsResultControlBase> GetResult()
        {
            LteMgrsRsrpRangeResult resultControl = new LteMgrsRsrpRangeResult();
            resultControl.FillData(tmpFuncItem);
            return new List<LteMgrsResultControlBase>() { resultControl };
        }

        public override void Clear()
        {
            tmpFuncItem = null;
        }

        public DataTable GetTable(LteMgrsCity city, LteMgrsRsrpBandType type)
        {
            DataTable table = new DataTable(city.CityName + EnumDescriptionAttribute.GetText(type) + "场强分布");
            table.Columns.AddRange(BuildColumns());

            foreach (LteMgrsRegion region in city.RegionDic.Values)
            {
                // do stat
                int invalidCnt = 0;
                float rsrpSum = 0;
                int[] rangesCnt = new int[Ranges.ColorRanges.Count];
                List<LteMgrsGrid> gridList = new List<LteMgrsGrid>(region.GridDic.Values);
                foreach (LteMgrsGrid grid in gridList)
                {
                    float value = LteMgrsGridHelper.GetTopRsrp(grid, type);
                    if (float.IsNaN(value))
                    {
                        ++invalidCnt;
                        continue;
                    }
                    int idx = Ranges.GetIndex(value);
                    ++rangesCnt[idx];
                    rsrpSum += value;
                }

                // add to table
                List<object> dataRow = new List<object>();
                dataRow.Add(region.RegionName);
                foreach (int cnt in rangesCnt)
                {
                    dataRow.Add(cnt);
                }
                dataRow.Add(invalidCnt);
                dataRow.Add(gridList.Count);
                dataRow.Add(gridList.Count - invalidCnt == 0 ? 0 : Math.Round(rsrpSum / (gridList.Count - invalidCnt), 2));
                table.Rows.Add(dataRow.ToArray());
            }

            // summary
            AddSummary( table);
            return table;
        }

        public ChartControl GetChart(DataTable dt)
        {
            ChartControl chart = new ChartControl();

            Series[] series = new Series[Ranges.ColorRanges.Count + 1];
            for (int i = 0; i < Ranges.ColorRanges.Count + 1; ++i) // BuildColumns
            {
                Series sis = null;
                if (i == Ranges.ColorRanges.Count)
                {
                    sis = new Series(Ranges.InvalidDesc, ViewType.FullStackedBar);
                    ((FullStackedBarSeriesView)sis.View).Color = Ranges.InvalidColor;
                }
                else
                {
                    sis = new Series(Ranges.ColorRanges[i].desInfo, ViewType.FullStackedBar);
                    ((FullStackedBarSeriesView)sis.View).Color = Ranges.ColorRanges[i].color;
                }
                ((FullStackedBarSeriesView)sis.View).FillStyle.FillMode = FillMode.Solid;
                ((FullStackedBarSeriesLabel)sis.Label).Visible = false;
                ((StackedBarSeriesView)sis.View).BarWidth = 0.4;
                series[i] = sis;
            }

            for (int i = 0; i < dt.Rows.Count; ++i)
            {
                object[] row = dt.Rows[i].ItemArray;

                string argument = string.Format("{0}", row[0].ToString());
                int total = Convert.ToInt32(row[row.Length - 2]);
                for (int j = 1, k = 0; j < dt.Columns.Count - 1 && k < series.Length; ++j, ++k)
                {
                    series[k].Points.Add(new SeriesPoint(argument, total == 0 ? 0 : 1d * Convert.ToInt32(row[j]) / total));
                }
            }

            ChartTitle title = new ChartTitle();
            title.Text = dt.TableName;
            chart.Titles.Add(title);
            chart.Series.Clear();
            chart.Series.AddRange(series);
            ((XYDiagram)chart.Diagram).Rotated = true;
            ((XYDiagram)chart.Diagram).AxisY.NumericOptions.Format = NumericFormat.Percent;
            ((XYDiagram)chart.Diagram).AxisY.NumericOptions.Precision = 0;
            ((XYDiagram)chart.Diagram).AxisX.Reverse = true;
            return chart;
        }

        public List<LteMgrsDrawItem> GetDrawList(LteMgrsCity city, LteMgrsRsrpBandType type)
        {
            List<LteMgrsDrawItem> retList = new List<LteMgrsDrawItem>();
            foreach (LteMgrsRegion region in city.RegionDic.Values)
            {
                foreach (LteMgrsGrid grid in region.GridDic.Values)
                {
                    LteMgrsDrawItem item = new LteMgrsDrawItem(new DbPoint(grid.TLLng, grid.BRLat), new DbPoint(grid.BRLng, grid.TLLat));
                    float value = LteMgrsGridHelper.GetTopRsrp(grid, type);
                    item.FillColor = Ranges.GetColor(value);
                    item.ToolInfoTitle = grid.MgrsString;
                    item.ToolInfoDetail = grid.DetailInfo;
                    retList.Add(item);
                }
            }
            return retList;
        }

        public LteMgrsLegendGroup GetLegend()
        {
            return Ranges.GetLegend();
        }

        private DataColumn[] BuildColumns()
        {
            List<DataColumn> columns = new List<DataColumn>();
            columns.Add(new DataColumn("网格", typeof(string)));
            for (int i = 0; i < Ranges.ColorRanges.Count; ++i)
            {
                columns.Add(new DataColumn(Ranges.ColorRanges[i].desInfo, typeof(string)));
            }
            columns.Add(new DataColumn("无效栅格", typeof(string))); // 无效栅格定义：栅格无有效频段或者所有频段都不是设定的频段
            columns.Add(new DataColumn("栅格总数", typeof(string))); // 栅格总数定义：区域内所有栅格，包括无效栅格
            columns.Add(new DataColumn("平均电平", typeof(string)));
            return columns.ToArray();
        }

        private void AddSummary(DataTable table)
        {
            DataRow summary = table.NewRow();
            for (int i = 0; i < table.Columns.Count; ++i)
            {
                // 不需要统计的列
                if (i == 0)
                {
                    summary[i] = sSummaryColumnName;
                    continue;
                }

                // 对需要的列逐行处理
                double sum = 0;
                for (int j = 1; j < table.Rows.Count; ++j)
                {
                    sum += Convert.ToDouble(table.Rows[j][i].ToString());
                }
                
                // 处理平均场强
                if (i == table.Columns.Count - 1)
                {
                    summary[i] = table.Rows.Count == 0 ? null : (object)(Math.Round(sum / (table.Rows.Count - 1), 2));
                }
                else // 栅格数
                {
                    summary[i] = Convert.ToInt32(sum);
                }
            }
            table.Rows.Add(summary);
        }

        public static LteMgrsColorRange Ranges { get; set; } = new LteMgrsRsrpColorRange();
        private static string sSummaryColumnName = "汇总(网格内)";
    }
}
