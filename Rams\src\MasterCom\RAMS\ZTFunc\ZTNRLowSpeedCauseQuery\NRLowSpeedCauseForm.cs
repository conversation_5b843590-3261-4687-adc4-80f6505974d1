﻿using DevExpress.XtraCharts;
using MasterCom.RAMS.Func;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NRLowSpeedCauseForm : MinCloseForm
    {
        public NRLowSpeedCauseForm() : base(MainModel.GetInstance())
        {
            InitializeComponent();
        }

        public void FillData(List<NRLowSpeedSeg> segs, NRLowSpeedCauseCondition cond)
        {
            makeSummary(segs, cond);
            gridDetail.DataSource = segs;
            gridDetail.RefreshDataSource();
            MainModel.FireSetDefaultMapSerialTheme("NR", "APP_Speed_Mb");
        }

        private void makeSummary(List<NRLowSpeedSeg> segs, NRLowSpeedCauseCondition cond)
        {
            Dictionary<string, NRLowSpeedCauseSummary> nameCause = new Dictionary<string, NRLowSpeedCauseSummary>();
            foreach (NRLowSpeedCauseBase item in cond.Causes)
            {
                nameCause.Add(item.Ancestor.Name, new NRLowSpeedCauseSummary(item));
            }
            NRLowSpeedUnknowReason unknowCause = new NRLowSpeedUnknowReason();
            nameCause.Add(unknowCause.Name, new NRLowSpeedCauseSummary(unknowCause));
            int lowPntCnt = 0;
            foreach (NRLowSpeedSeg seg in segs)
            {
                lowPntCnt += seg.SampleCount;
                foreach (NRLowSpeedPointDetail item in seg.PointDetails)
                {
                    nameCause[item.Reason.Ancestor.Name].AddCause(item.Reason);
                }
            }
            List<string> colDonotMerge = new List<string>();
            DataTable tb = new DataTable();
            tb.Columns.Add("原因类别", typeof(string));
            tb.Columns.Add("采样点个数", typeof(int)).Caption = "采样点个数";
            tb.Columns.Add("占比（%）", typeof(double)).Caption = "占比（%）";

            tb.Columns.Add("原因场景", typeof(string));
            tb.Columns.Add("采样点个数sub", typeof(int)).Caption = "采样点个数";
            tb.Columns.Add("类目占比sub（%）", typeof(double)).Caption = "类目占比（%）";
            tb.Columns.Add("总体占比sub（%）", typeof(double)).Caption = "总体占比（%）";

            tb.Columns.Add("详细原因", typeof(string));
            tb.Columns.Add("采样点个数detail", typeof(int)).Caption = "采样点个数";
            colDonotMerge.Add("采样点个数detail");
            tb.Columns.Add("类目占比detail（%）", typeof(double)).Caption = "类目占比（%）";
            colDonotMerge.Add("类目占比detail（%）");
            tb.Columns.Add("总体占比detail（%）", typeof(double)).Caption = "总体占比（%）";
            colDonotMerge.Add("类目占比detail（%）");
            addRowInfo(nameCause, lowPntCnt, tb);
            gridSummary.DataSource = tb;
            viewSummary.PopulateColumns(tb);
            foreach (string colName in colDonotMerge)
            {
                int idx = tb.Columns.IndexOf(colName);
                viewSummary.Columns[idx].OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            }
            gridSummary.RefreshDataSource();
            viewSummary.BestFitColumns();

            fillCharts(nameCause, lowPntCnt);
        }

        private void addRowInfo(Dictionary<string, NRLowSpeedCauseSummary> nameCause, int lowPntCnt, DataTable tb)
        {
            foreach (NRLowSpeedCauseSummary summary in nameCause.Values)
            {
                if (summary.SubCause == null)
                {
                    DataRow row = tb.NewRow();
                    row[0] = summary.CauseName;
                    row[1] = summary.Count;
                    row[2] = Math.Round(100.0 * summary.Count / lowPntCnt, 2);
                    tb.Rows.Add(row);
                }
                else
                {
                    addSubRow(lowPntCnt, tb, summary);
                }
            }
        }

        private void addSubRow(int lowPntCnt, DataTable tb, NRLowSpeedCauseSummary summary)
        {
            foreach (NRLowSpeedCauseSummary sub in summary.SubCause)
            {
                if (sub.SubCause == null)
                {
                    DataRow subRow = tb.NewRow();
                    subRow[0] = summary.CauseName;
                    subRow[1] = summary.Count;
                    subRow[2] = Math.Round(100.0 * summary.Count / lowPntCnt, 2);
                    subRow[3] = sub.CauseName;
                    subRow[4] = sub.Count;
                    subRow[5] = summary.Count == 0 ? 0 : Math.Round(100.0 * sub.Count / summary.Count, 2);
                    subRow[6] = Math.Round(100.0 * sub.Count / lowPntCnt, 2);
                    tb.Rows.Add(subRow);
                }
                else
                {
                    addDetailRow(lowPntCnt, tb, summary, sub);
                }
            }
        }

        private void addDetailRow(int lowPntCnt, DataTable tb, NRLowSpeedCauseSummary summary, NRLowSpeedCauseSummary sub)
        {
            foreach (NRLowSpeedCauseSummary detail in sub.SubCause)
            {
                DataRow detailRow = tb.NewRow();
                detailRow[0] = summary.CauseName;
                detailRow[1] = summary.Count;
                detailRow[2] = Math.Round(100.0 * summary.Count / lowPntCnt, 2);
                detailRow[3] = sub.CauseName;
                detailRow[4] = sub.Count;
                detailRow[5] = summary.Count == 0 ? 0 : Math.Round(100.0 * sub.Count / summary.Count, 2);
                detailRow[6] = Math.Round(100.0 * sub.Count / lowPntCnt, 2);
                detailRow[7] = detail.CauseName;
                detailRow[8] = detail.Count;
                detailRow[9] = sub.Count == 0 ? 0 : Math.Round(100.0 * detail.Count / sub.Count, 2);
                detailRow[10] = Math.Round(100.0 * detail.Count / lowPntCnt, 2);
                tb.Rows.Add(detailRow);
            }
        }

        private void fillCharts(Dictionary<string, NRLowSpeedCauseSummary> summaryDic, int totalPoorPntCnt)
        {
            Series mainSer = chartMain.Series[0];
            mainSer.Points.Clear();
            Series subSer = chartSub.Series[0];
            subSer.Points.Clear();
            foreach (NRLowSpeedCauseSummary sum in summaryDic.Values)
            {
                SeriesPoint pnt = new SeriesPoint(sum.CauseName
                    , Math.Round(100.0 * sum.Count / totalPoorPntCnt, 2));
                mainSer.Points.Add(pnt);

                if (sum.SubCause != null)
                {
                    foreach (NRLowSpeedCauseSummary sub in sum.SubCause)
                    {
                        subSer.Points.Add(new SeriesPoint(sub.CauseName
                            , Math.Round(100.0 * sub.Count / totalPoorPntCnt, 2)));
                    }
                }
                else
                {//未知原因
                    subSer.Points.Add(pnt);
                }
            }
        }

        private void viewDetail_DoubleClick(object sender, EventArgs e)
        {
            DevExpress.XtraGrid.Views.Grid.GridView view = this.gridDetail.FocusedView as DevExpress.XtraGrid.Views.Grid.GridView;
            if (view == null)
            {
                return;
            }
            DevExpress.Utils.DXMouseEventArgs dxEvt = e as DevExpress.Utils.DXMouseEventArgs;
            if (dxEvt == null)
            {
                return;
            }
            DevExpress.XtraGrid.Views.Grid.ViewInfo.GridHitInfo info = view.CalcHitInfo(dxEvt.Location);
            if (info == null || !info.InRow)
            {
                return;
            }
            NRLowSpeedPointDetail reason = view.GetRow(info.RowHandle) as NRLowSpeedPointDetail;
            MainModel.SelectedTestPoints.Clear();
            MainModel.SelectedTestPoints.Add(reason.TestPoint);
            MainModel.MainForm.GetMapForm().GoToView(reason.TestPoint.Longitude, reason.TestPoint.Latitude, 6000);
        }

        private void viewMain_DoubleClick(object sender, EventArgs e)
        {
            DevExpress.Utils.DXMouseEventArgs dxEvt = e as DevExpress.Utils.DXMouseEventArgs;
            if (dxEvt == null)
            {
                return;
            }
            DevExpress.XtraGrid.Views.Grid.ViewInfo.GridHitInfo info = viewMain.CalcHitInfo(dxEvt.Location);
            if (info == null || !info.InRow)
            {
                return;
            }
            NRLowSpeedSeg seg = viewMain.GetRow(info.RowHandle) as NRLowSpeedSeg;
            if (seg == null)
            {
                return;
            }
            MainModel.ClearDTData();
            foreach (TestPoint tp in seg.TestPoints)
            {
                MainModel.DTDataManager.Add(tp);
            }
            foreach (Event evt in seg.Events)
            {
                MainModel.DTDataManager.Add(evt);
            }
            MainModel.FireDTDataChanged(this);
        }

        private void miDetailExport_Click(object sender, EventArgs e)
        {
            SaveFileDialog dlg = new SaveFileDialog();
            dlg.RestoreDirectory = true;
            dlg.Filter = FilterHelper.ExcelX;
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                this.Cursor = System.Windows.Forms.Cursors.WaitCursor;
                DevExpress.XtraPrinting.XlsxExportOptions options = new DevExpress.XtraPrinting.XlsxExportOptions();
                options.TextExportMode = DevExpress.XtraPrinting.TextExportMode.Text;
                options.SheetName = "低速率原因";
                gridDetail.ExportToXlsx(dlg.FileName, options);
                this.Cursor = System.Windows.Forms.Cursors.Default;
            }
        }

        private void miSummaryExport_Click(object sender, EventArgs e)
        {
            SaveFileDialog dlg = new SaveFileDialog();
            dlg.RestoreDirectory = true;
            dlg.Filter = FilterHelper.ExcelX;
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                this.Cursor = System.Windows.Forms.Cursors.WaitCursor;
                DevExpress.XtraPrinting.XlsxExportOptions options = new DevExpress.XtraPrinting.XlsxExportOptions();
                options.TextExportMode = DevExpress.XtraPrinting.TextExportMode.Text;
                gridSummary.ExportToXlsx(dlg.FileName, options);
                this.Cursor = System.Windows.Forms.Cursors.Default;
            }
        }
    }
}
