﻿using MasterCom.RAMS.Func;
using System;
using System.Drawing;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class LeakOutAsNCellDlg_NR : BaseDialog
    {
        public LeakOutAsNCellDlg_NR(bool isNeedLte)
        {
            InitializeComponent();
            numRxLevDValue.Enabled = numRxLev.Enabled = chkNBCell.Checked;
            if (!isNeedLte)
            {
                chkLteCell.Visible = false;
                chkGetRoadDesc.Location = new Point(30, 130);
                label5.Location = new Point(147, 132);
            }
        }

        /// <summary>
        /// 邻区电平
        /// </summary>
        public int RxLevThreshold
        {
            get { return (int)numRxLev.Value; }
        }

        /// <summary>
        /// 邻区与主服电平差
        /// </summary>
        public int RxLevDValue
        {
            get { return (int)numRxLevDValue.Value; }
        }

        /// <summary>
        /// 只计算主服小区
        /// </summary>
        public bool LeakOutAsMainCell
        {
            get { return chkMainCell.Checked; }
        }

        /// <summary>
        /// 同时计算邻区小区
        /// </summary>
        public bool LeakOutAsNBCell
        {
            get { return chkNBCell.Checked; }
        }

        /// <summary>
        /// 获取覆盖道路信息
        /// </summary>
        public bool getRoadDesc
        {
            get { return chkGetRoadDesc.Checked; }
        }

        /// <summary>
        /// 是否锚点Lte小区
        /// </summary>
        public bool IsGetLteCell
        {
            get { return chkLteCell.Checked; }
        }

        private void chkNBCell_CheckedChanged(object sender, EventArgs e)
        {
            numRxLevDValue.Enabled = numRxLev.Enabled = chkNBCell.Checked;
        }
    }
}
