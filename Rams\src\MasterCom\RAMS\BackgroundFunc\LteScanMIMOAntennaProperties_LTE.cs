﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc;

namespace MasterCom.RAMS.Func.SystemSetting
{
    public partial class LteScanMIMOAntennaProperties_LTE : PropertiesControl
    {
        private DevExpress.XtraEditors.GroupControl groupControl;
        private DevExpress.XtraEditors.CheckEdit chkBackgroundStat;
        private DevExpress.XtraEditors.GroupControl groupControl1;
        private DevExpress.XtraEditors.GroupControl groupRSRP1;
        private NumericUpDown numRsrp1Min;
        private Label label4;
        private NumericUpDown numRsrp1Max;
        private CheckBox chkRsrp0;
        private CheckBox chkSampleN;
        private CheckBox chkChanel;
        private DevExpress.XtraEditors.GroupControl groupRSRP0;
        private NumericUpDown numRsrp0Min;
        private Label label3;
        private NumericUpDown numRsrp0Max;
        private DevExpress.XtraEditors.GroupControl groupSampleN;
        private Label label2;
        private NumericUpDown numSampleN;
        private DevExpress.XtraEditors.GroupControl groupChancel;
        private Label label1;
        private NumericUpDown numChanel;
        private CheckBox chkF;
        private CheckBox chkE;
        private CheckBox chkD;
        private CheckBox chkRsrp1;

        private readonly ZTLteMiMoAntenna queryFunc;

        public LteScanMIMOAntennaProperties_LTE(ZTLteMiMoAntenna queryFunc)
        {
            InitializeComponent();
            this.queryFunc = queryFunc;
        }

        public override string ParentName
        {
            get { return queryFunc.FuncType.ToString(); }
        }

        public override string ParentSubName
        {
            get { return queryFunc.SubFuncType.ToString(); }
        }

        public override string SelfName
        {
            get { return queryFunc.Name; }
        }

        public override string TabPageName
        {
            get { return queryFunc.Name; }
        }

        public override void Flush()
        {
            chkBackgroundStat.Checked = queryFunc.BackgroundStat;

            chkChanel.Checked = queryFunc.mimoCfgInfo.bChanelAna;
            chkD.Checked = queryFunc.mimoCfgInfo.bChanelD;
            chkE.Checked = queryFunc.mimoCfgInfo.bChanelE;
            chkF.Checked = queryFunc.mimoCfgInfo.bChanelF;
            numChanel.Value = queryFunc.mimoCfgInfo.iChanelNum;
            chkSampleN.Checked = queryFunc.mimoCfgInfo.bSampleN;
            numSampleN.Value = queryFunc.mimoCfgInfo.iSampleN;
            chkRsrp0.Checked = queryFunc.mimoCfgInfo.bRSRP0;
            numRsrp0Min.Value = int.Parse(queryFunc.mimoCfgInfo.fRsrp0Min.ToString());
            numRsrp0Max.Value = int.Parse(queryFunc.mimoCfgInfo.fRsrp0Max.ToString());
            chkRsrp1.Checked = queryFunc.mimoCfgInfo.bRSRP1;
            numRsrp1Min.Value = int.Parse(queryFunc.mimoCfgInfo.fRsrp1Min.ToString());
            numRsrp1Max.Value = int.Parse(queryFunc.mimoCfgInfo.fRsrp1Max.ToString());
        }

        public override void Apply()
        {
            queryFunc.BackgroundStat = chkBackgroundStat.Checked;

            queryFunc.mimoCfgInfo.bChanelAna = chkChanel.Checked;
            queryFunc.mimoCfgInfo.bChanelD = chkD.Checked;
            queryFunc.mimoCfgInfo.bChanelE = chkE.Checked;
            queryFunc.mimoCfgInfo.bChanelF = chkF.Checked;
            queryFunc.mimoCfgInfo.iChanelNum = int.Parse(numChanel.Value.ToString());
            queryFunc.mimoCfgInfo.bSampleN = chkSampleN.Checked;
            queryFunc.mimoCfgInfo.iSampleN = int.Parse(numSampleN.Value.ToString());
            queryFunc.mimoCfgInfo.bRSRP0 = chkRsrp0.Checked;
            queryFunc.mimoCfgInfo.fRsrp0Min = float.Parse(numRsrp0Min.Value.ToString());
            queryFunc.mimoCfgInfo.fRsrp0Max = float.Parse(numRsrp0Max.Value.ToString());
            queryFunc.mimoCfgInfo.bRSRP1 = chkRsrp1.Checked;
            queryFunc.mimoCfgInfo.fRsrp1Min = float.Parse(numRsrp1Min.Value.ToString());
            queryFunc.mimoCfgInfo.fRsrp1Max = float.Parse(numRsrp1Max.Value.ToString());
        }


        public override bool IsValid()
        {
            return true;
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.groupControl = new DevExpress.XtraEditors.GroupControl();
            this.chkBackgroundStat = new DevExpress.XtraEditors.CheckEdit();
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.groupRSRP1 = new DevExpress.XtraEditors.GroupControl();
            this.numRsrp1Min = new System.Windows.Forms.NumericUpDown();
            this.label4 = new System.Windows.Forms.Label();
            this.numRsrp1Max = new System.Windows.Forms.NumericUpDown();
            this.chkRsrp0 = new System.Windows.Forms.CheckBox();
            this.chkSampleN = new System.Windows.Forms.CheckBox();
            this.chkChanel = new System.Windows.Forms.CheckBox();
            this.groupRSRP0 = new DevExpress.XtraEditors.GroupControl();
            this.numRsrp0Min = new System.Windows.Forms.NumericUpDown();
            this.label3 = new System.Windows.Forms.Label();
            this.numRsrp0Max = new System.Windows.Forms.NumericUpDown();
            this.groupSampleN = new DevExpress.XtraEditors.GroupControl();
            this.label2 = new System.Windows.Forms.Label();
            this.numSampleN = new System.Windows.Forms.NumericUpDown();
            this.groupChancel = new DevExpress.XtraEditors.GroupControl();
            this.label1 = new System.Windows.Forms.Label();
            this.numChanel = new System.Windows.Forms.NumericUpDown();
            this.chkF = new System.Windows.Forms.CheckBox();
            this.chkE = new System.Windows.Forms.CheckBox();
            this.chkD = new System.Windows.Forms.CheckBox();
            this.chkRsrp1 = new System.Windows.Forms.CheckBox();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl)).BeginInit();
            this.groupControl.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chkBackgroundStat.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupRSRP1)).BeginInit();
            this.groupRSRP1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numRsrp1Min)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRsrp1Max)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupRSRP0)).BeginInit();
            this.groupRSRP0.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numRsrp0Min)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRsrp0Max)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupSampleN)).BeginInit();
            this.groupSampleN.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numSampleN)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupChancel)).BeginInit();
            this.groupChancel.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numChanel)).BeginInit();
            this.SuspendLayout();
            // 
            // groupControl
            // 
            this.groupControl.Controls.Add(this.chkBackgroundStat);
            this.groupControl.Location = new System.Drawing.Point(3, 3);
            this.groupControl.Name = "groupControl";
            this.groupControl.Size = new System.Drawing.Size(473, 53);
            this.groupControl.TabIndex = 4;
            this.groupControl.Text = "开关设置";
            // 
            // chkBackgroundStat
            // 
            this.chkBackgroundStat.Location = new System.Drawing.Point(15, 26);
            this.chkBackgroundStat.Name = "chkBackgroundStat";
            this.chkBackgroundStat.Properties.Caption = "启用";
            this.chkBackgroundStat.Size = new System.Drawing.Size(75, 19);
            this.chkBackgroundStat.TabIndex = 0;
            // 
            // groupControl1
            // 
            this.groupControl1.Controls.Add(this.groupRSRP1);
            this.groupControl1.Controls.Add(this.chkRsrp0);
            this.groupControl1.Controls.Add(this.chkSampleN);
            this.groupControl1.Controls.Add(this.chkChanel);
            this.groupControl1.Controls.Add(this.groupRSRP0);
            this.groupControl1.Controls.Add(this.groupSampleN);
            this.groupControl1.Controls.Add(this.groupChancel);
            this.groupControl1.Controls.Add(this.chkRsrp1);
            this.groupControl1.Location = new System.Drawing.Point(4, 62);
            this.groupControl1.Name = "groupControl1";
            this.groupControl1.Size = new System.Drawing.Size(472, 272);
            this.groupControl1.TabIndex = 5;
            this.groupControl1.Text = "MIMO双流分析设置";
            // 
            // groupRSRP1
            // 
            this.groupRSRP1.Controls.Add(this.numRsrp1Min);
            this.groupRSRP1.Controls.Add(this.label4);
            this.groupRSRP1.Controls.Add(this.numRsrp1Max);
            this.groupRSRP1.Location = new System.Drawing.Point(81, 195);
            this.groupRSRP1.Name = "groupRSRP1";
            this.groupRSRP1.Size = new System.Drawing.Size(383, 59);
            this.groupRSRP1.TabIndex = 25;
            this.groupRSRP1.Text = "按RSRP1";
            // 
            // numRsrp1Min
            // 
            this.numRsrp1Min.Enabled = false;
            this.numRsrp1Min.Location = new System.Drawing.Point(7, 26);
            this.numRsrp1Min.Minimum = new decimal(new int[] {
            140,
            0,
            0,
            -2147483648});
            this.numRsrp1Min.Name = "numRsrp1Min";
            this.numRsrp1Min.Size = new System.Drawing.Size(46, 22);
            this.numRsrp1Min.TabIndex = 6;
            this.numRsrp1Min.Value = new decimal(new int[] {
            110,
            0,
            0,
            -2147483648});
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(59, 32);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(78, 14);
            this.label4.TabIndex = 5;
            this.label4.Text = "<=RSRP1<=";
            // 
            // numRsrp1Max
            // 
            this.numRsrp1Max.Enabled = false;
            this.numRsrp1Max.Location = new System.Drawing.Point(143, 26);
            this.numRsrp1Max.Minimum = new decimal(new int[] {
            140,
            0,
            0,
            -2147483648});
            this.numRsrp1Max.Name = "numRsrp1Max";
            this.numRsrp1Max.Size = new System.Drawing.Size(46, 22);
            this.numRsrp1Max.TabIndex = 4;
            this.numRsrp1Max.Value = new decimal(new int[] {
            45,
            0,
            0,
            -2147483648});
            // 
            // chkRsrp0
            // 
            this.chkRsrp0.AutoSize = true;
            this.chkRsrp0.Location = new System.Drawing.Point(5, 159);
            this.chkRsrp0.Name = "chkRsrp0";
            this.chkRsrp0.Size = new System.Drawing.Size(73, 18);
            this.chkRsrp0.TabIndex = 24;
            this.chkRsrp0.Text = "按RSRP0";
            this.chkRsrp0.UseVisualStyleBackColor = true;
            this.chkRsrp0.CheckedChanged += new System.EventHandler(this.chkRsrp0_CheckedChanged);
            // 
            // chkSampleN
            // 
            this.chkSampleN.AutoSize = true;
            this.chkSampleN.Location = new System.Drawing.Point(5, 101);
            this.chkSampleN.Name = "chkSampleN";
            this.chkSampleN.Size = new System.Drawing.Size(70, 18);
            this.chkSampleN.TabIndex = 23;
            this.chkSampleN.Text = "按前N强";
            this.chkSampleN.UseVisualStyleBackColor = true;
            this.chkSampleN.CheckedChanged += new System.EventHandler(this.chkSampleN_CheckedChanged);
            // 
            // chkChanel
            // 
            this.chkChanel.AutoSize = true;
            this.chkChanel.Checked = true;
            this.chkChanel.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkChanel.Location = new System.Drawing.Point(9, 46);
            this.chkChanel.Name = "chkChanel";
            this.chkChanel.Size = new System.Drawing.Size(62, 18);
            this.chkChanel.TabIndex = 22;
            this.chkChanel.Text = "按频段";
            this.chkChanel.UseVisualStyleBackColor = true;
            this.chkChanel.CheckedChanged += new System.EventHandler(this.chkChanel_CheckedChanged);
            // 
            // groupRSRP0
            // 
            this.groupRSRP0.Controls.Add(this.numRsrp0Min);
            this.groupRSRP0.Controls.Add(this.label3);
            this.groupRSRP0.Controls.Add(this.numRsrp0Max);
            this.groupRSRP0.Location = new System.Drawing.Point(81, 134);
            this.groupRSRP0.Name = "groupRSRP0";
            this.groupRSRP0.Size = new System.Drawing.Size(383, 55);
            this.groupRSRP0.TabIndex = 21;
            this.groupRSRP0.Text = "按RSRP0";
            // 
            // numRsrp0Min
            // 
            this.numRsrp0Min.Enabled = false;
            this.numRsrp0Min.Location = new System.Drawing.Point(5, 26);
            this.numRsrp0Min.Minimum = new decimal(new int[] {
            140,
            0,
            0,
            -2147483648});
            this.numRsrp0Min.Name = "numRsrp0Min";
            this.numRsrp0Min.Size = new System.Drawing.Size(46, 22);
            this.numRsrp0Min.TabIndex = 6;
            this.numRsrp0Min.Value = new decimal(new int[] {
            110,
            0,
            0,
            -2147483648});
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(57, 32);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(78, 14);
            this.label3.TabIndex = 5;
            this.label3.Text = "<=RSRP0<=";
            // 
            // numRsrp0Max
            // 
            this.numRsrp0Max.Enabled = false;
            this.numRsrp0Max.Location = new System.Drawing.Point(141, 24);
            this.numRsrp0Max.Minimum = new decimal(new int[] {
            140,
            0,
            0,
            -2147483648});
            this.numRsrp0Max.Name = "numRsrp0Max";
            this.numRsrp0Max.Size = new System.Drawing.Size(46, 22);
            this.numRsrp0Max.TabIndex = 4;
            this.numRsrp0Max.Value = new decimal(new int[] {
            45,
            0,
            0,
            -2147483648});
            // 
            // groupSampleN
            // 
            this.groupSampleN.Controls.Add(this.label2);
            this.groupSampleN.Controls.Add(this.numSampleN);
            this.groupSampleN.Location = new System.Drawing.Point(80, 82);
            this.groupSampleN.Name = "groupSampleN";
            this.groupSampleN.Size = new System.Drawing.Size(384, 46);
            this.groupSampleN.TabIndex = 20;
            this.groupSampleN.Text = "按前N强";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(17, 25);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(99, 14);
            this.label2.TabIndex = 5;
            this.label2.Text = "每采样点前N强：";
            // 
            // numSampleN
            // 
            this.numSampleN.Enabled = false;
            this.numSampleN.Location = new System.Drawing.Point(116, 22);
            this.numSampleN.Name = "numSampleN";
            this.numSampleN.Size = new System.Drawing.Size(46, 22);
            this.numSampleN.TabIndex = 4;
            this.numSampleN.Value = new decimal(new int[] {
            9,
            0,
            0,
            0});
            // 
            // groupChancel
            // 
            this.groupChancel.Controls.Add(this.label1);
            this.groupChancel.Controls.Add(this.numChanel);
            this.groupChancel.Controls.Add(this.chkF);
            this.groupChancel.Controls.Add(this.chkE);
            this.groupChancel.Controls.Add(this.chkD);
            this.groupChancel.Location = new System.Drawing.Point(81, 22);
            this.groupChancel.Name = "groupChancel";
            this.groupChancel.Size = new System.Drawing.Size(383, 54);
            this.groupChancel.TabIndex = 19;
            this.groupChancel.Text = "按小区频段分析";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(204, 26);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(87, 14);
            this.label1.TabIndex = 5;
            this.label1.Text = "每频段前N强：";
            // 
            // numChanel
            // 
            this.numChanel.Location = new System.Drawing.Point(293, 21);
            this.numChanel.Name = "numChanel";
            this.numChanel.Size = new System.Drawing.Size(46, 22);
            this.numChanel.TabIndex = 4;
            this.numChanel.Value = new decimal(new int[] {
            3,
            0,
            0,
            0});
            // 
            // chkF
            // 
            this.chkF.AutoSize = true;
            this.chkF.Checked = true;
            this.chkF.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkF.Location = new System.Drawing.Point(133, 26);
            this.chkF.Name = "chkF";
            this.chkF.Size = new System.Drawing.Size(56, 18);
            this.chkF.TabIndex = 2;
            this.chkF.Text = "F频段";
            this.chkF.UseVisualStyleBackColor = true;
            // 
            // chkE
            // 
            this.chkE.AutoSize = true;
            this.chkE.Checked = true;
            this.chkE.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkE.Location = new System.Drawing.Point(73, 26);
            this.chkE.Name = "chkE";
            this.chkE.Size = new System.Drawing.Size(57, 18);
            this.chkE.TabIndex = 1;
            this.chkE.Text = "E频段";
            this.chkE.UseVisualStyleBackColor = true;
            // 
            // chkD
            // 
            this.chkD.AutoSize = true;
            this.chkD.Checked = true;
            this.chkD.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkD.Location = new System.Drawing.Point(13, 26);
            this.chkD.Name = "chkD";
            this.chkD.Size = new System.Drawing.Size(58, 18);
            this.chkD.TabIndex = 0;
            this.chkD.Text = "D频段";
            this.chkD.UseVisualStyleBackColor = true;
            // 
            // chkRsrp1
            // 
            this.chkRsrp1.AutoSize = true;
            this.chkRsrp1.Location = new System.Drawing.Point(5, 213);
            this.chkRsrp1.Name = "chkRsrp1";
            this.chkRsrp1.Size = new System.Drawing.Size(73, 18);
            this.chkRsrp1.TabIndex = 26;
            this.chkRsrp1.Text = "按RSRP1";
            this.chkRsrp1.UseVisualStyleBackColor = true;
            this.chkRsrp1.CheckedChanged += new System.EventHandler(this.chkRsrp1_CheckedChanged);
            // 
            // LteScanMIMOAntennaProperties_LTE
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.groupControl1);
            this.Controls.Add(this.groupControl);
            this.Name = "LteScanMIMOAntennaProperties_LTE";
            this.Size = new System.Drawing.Size(491, 354);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl)).EndInit();
            this.groupControl.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.chkBackgroundStat.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            this.groupControl1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupRSRP1)).EndInit();
            this.groupRSRP1.ResumeLayout(false);
            this.groupRSRP1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numRsrp1Min)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRsrp1Max)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupRSRP0)).EndInit();
            this.groupRSRP0.ResumeLayout(false);
            this.groupRSRP0.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numRsrp0Min)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRsrp0Max)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupSampleN)).EndInit();
            this.groupSampleN.ResumeLayout(false);
            this.groupSampleN.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numSampleN)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupChancel)).EndInit();
            this.groupChancel.ResumeLayout(false);
            this.groupChancel.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numChanel)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private void chkChanel_CheckedChanged(object sender, EventArgs e)
        {
            chkD.Enabled = chkE.Enabled = chkF.Enabled = numChanel.Enabled = chkChanel.Checked;
        }

        private void chkSampleN_CheckedChanged(object sender, EventArgs e)
        {
            numSampleN.Enabled = chkSampleN.Checked;
        }

        private void chkRsrp0_CheckedChanged(object sender, EventArgs e)
        {
            numRsrp0Min.Enabled = numRsrp0Max.Enabled = chkRsrp0.Checked;
        }

        private void chkRsrp1_CheckedChanged(object sender, EventArgs e)
        {
            numRsrp1Min.Enabled = numRsrp1Max.Enabled = chkRsrp1.Checked;
        }

    }
}
