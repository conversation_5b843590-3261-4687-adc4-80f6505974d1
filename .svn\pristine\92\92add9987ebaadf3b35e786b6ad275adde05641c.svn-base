﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTLowSpeedSettingDlg_TD
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.label1 = new System.Windows.Forms.Label();
            this.spinEditSpeed = new DevExpress.XtraEditors.SpinEdit();
            this.spinEditDistance = new DevExpress.XtraEditors.SpinEdit();
            this.label2 = new System.Windows.Forms.Label();
            this.simpleButtonOK = new DevExpress.XtraEditors.SimpleButton();
            this.simpleButtonCancel = new DevExpress.XtraEditors.SimpleButton();
            this.label3 = new System.Windows.Forms.Label();
            this.label5 = new System.Windows.Forms.Label();
            this.checkBoxSynthesis = new System.Windows.Forms.CheckBox();
            this.checkBoxLowSpeedTD = new System.Windows.Forms.CheckBox();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.spinEditSpeedMin = new DevExpress.XtraEditors.SpinEdit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditSpeed.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditDistance.Properties)).BeginInit();
            this.groupBox1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditSpeedMin.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label1.Location = new System.Drawing.Point(124, 25);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(65, 12);
            this.label1.TabIndex = 0;
            this.label1.Text = "≤ 速率 ≤";
            // 
            // spinEditSpeed
            // 
            this.spinEditSpeed.EditValue = new decimal(new int[] {
            20,
            0,
            0,
            0});
            this.spinEditSpeed.Location = new System.Drawing.Point(199, 21);
            this.spinEditSpeed.Name = "spinEditSpeed";
            this.spinEditSpeed.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditSpeed.Properties.Appearance.Options.UseFont = true;
            this.spinEditSpeed.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditSpeed.Properties.Mask.EditMask = "f0";
            this.spinEditSpeed.Size = new System.Drawing.Size(70, 20);
            this.spinEditSpeed.TabIndex = 1;
            // 
            // spinEditDistance
            // 
            this.spinEditDistance.EditValue = new decimal(new int[] {
            50,
            0,
            0,
            0});
            this.spinEditDistance.Location = new System.Drawing.Point(199, 62);
            this.spinEditDistance.Name = "spinEditDistance";
            this.spinEditDistance.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditDistance.Properties.Appearance.Options.UseFont = true;
            this.spinEditDistance.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditDistance.Properties.Mask.EditMask = "f0";
            this.spinEditDistance.Size = new System.Drawing.Size(70, 20);
            this.spinEditDistance.TabIndex = 2;
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label2.Location = new System.Drawing.Point(142, 65);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(47, 12);
            this.label2.TabIndex = 3;
            this.label2.Text = "距离 ≥";
            // 
            // simpleButtonOK
            // 
            this.simpleButtonOK.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.simpleButtonOK.Appearance.Options.UseFont = true;
            this.simpleButtonOK.Location = new System.Drawing.Point(43, 183);
            this.simpleButtonOK.Name = "simpleButtonOK";
            this.simpleButtonOK.Size = new System.Drawing.Size(75, 23);
            this.simpleButtonOK.TabIndex = 4;
            this.simpleButtonOK.Text = "确定";
            this.simpleButtonOK.Click += new System.EventHandler(this.simpleButtonOK_Click);
            // 
            // simpleButtonCancel
            // 
            this.simpleButtonCancel.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.simpleButtonCancel.Appearance.Options.UseFont = true;
            this.simpleButtonCancel.Location = new System.Drawing.Point(158, 183);
            this.simpleButtonCancel.Name = "simpleButtonCancel";
            this.simpleButtonCancel.Size = new System.Drawing.Size(75, 23);
            this.simpleButtonCancel.TabIndex = 5;
            this.simpleButtonCancel.Text = "取消";
            this.simpleButtonCancel.Click += new System.EventHandler(this.simpleButtonCancel_Click);
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label3.Location = new System.Drawing.Point(284, 25);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(29, 12);
            this.label3.TabIndex = 0;
            this.label3.Text = "Kbps";
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label5.Location = new System.Drawing.Point(296, 66);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(17, 12);
            this.label5.TabIndex = 3;
            this.label5.Text = "米";
            // 
            // checkBoxSynthesis
            // 
            this.checkBoxSynthesis.AutoSize = true;
            this.checkBoxSynthesis.Checked = true;
            this.checkBoxSynthesis.CheckState = System.Windows.Forms.CheckState.Checked;
            this.checkBoxSynthesis.Location = new System.Drawing.Point(7, 28);
            this.checkBoxSynthesis.Name = "checkBoxSynthesis";
            this.checkBoxSynthesis.Size = new System.Drawing.Size(108, 16);
            this.checkBoxSynthesis.TabIndex = 6;
            this.checkBoxSynthesis.Text = "综合低速率路段";
            this.checkBoxSynthesis.UseVisualStyleBackColor = true;
            // 
            // checkBoxLowSpeedTD
            // 
            this.checkBoxLowSpeedTD.AutoSize = true;
            this.checkBoxLowSpeedTD.Location = new System.Drawing.Point(126, 28);
            this.checkBoxLowSpeedTD.Name = "checkBoxLowSpeedTD";
            this.checkBoxLowSpeedTD.Size = new System.Drawing.Size(96, 16);
            this.checkBoxLowSpeedTD.TabIndex = 7;
            this.checkBoxLowSpeedTD.Text = "TD低速率路段";
            this.checkBoxLowSpeedTD.UseVisualStyleBackColor = true;
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.checkBoxLowSpeedTD);
            this.groupBox1.Controls.Add(this.checkBoxSynthesis);
            this.groupBox1.Location = new System.Drawing.Point(31, 99);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(230, 61);
            this.groupBox1.TabIndex = 8;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "网络";
            // 
            // spinEditSpeedMin
            // 
            this.spinEditSpeedMin.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.spinEditSpeedMin.Location = new System.Drawing.Point(46, 21);
            this.spinEditSpeedMin.Name = "spinEditSpeedMin";
            this.spinEditSpeedMin.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditSpeedMin.Properties.Appearance.Options.UseFont = true;
            this.spinEditSpeedMin.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditSpeedMin.Properties.Mask.EditMask = "f0";
            this.spinEditSpeedMin.Size = new System.Drawing.Size(70, 20);
            this.spinEditSpeedMin.TabIndex = 9;
            // 
            // ZTLowSpeedSettingDlg_TD
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(357, 240);
            this.Controls.Add(this.spinEditSpeedMin);
            this.Controls.Add(this.groupBox1);
            this.Controls.Add(this.simpleButtonCancel);
            this.Controls.Add(this.simpleButtonOK);
            this.Controls.Add(this.label5);
            this.Controls.Add(this.label2);
            this.Controls.Add(this.spinEditDistance);
            this.Controls.Add(this.spinEditSpeed);
            this.Controls.Add(this.label3);
            this.Controls.Add(this.label1);
            this.Name = "ZTLowSpeedSettingDlg_TD";
            this.Text = "低速率分析设置";
            ((System.ComponentModel.ISupportInitialize)(this.spinEditSpeed.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditDistance.Properties)).EndInit();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditSpeedMin.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.Label label1;
        private DevExpress.XtraEditors.SpinEdit spinEditSpeed;
        private DevExpress.XtraEditors.SpinEdit spinEditDistance;
        private System.Windows.Forms.Label label2;
        private DevExpress.XtraEditors.SimpleButton simpleButtonOK;
        private DevExpress.XtraEditors.SimpleButton simpleButtonCancel;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.CheckBox checkBoxSynthesis;
        private System.Windows.Forms.CheckBox checkBoxLowSpeedTD;
        private System.Windows.Forms.GroupBox groupBox1;
        private DevExpress.XtraEditors.SpinEdit spinEditSpeedMin;
    }
}