using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.ES.ColorManager
{
    public partial class SelectEventForm : BaseFormStyle
    {
        MainModel model;

        public SelectEventForm(MainModel model)
        {
            InitializeComponent();
            this.model = model;
            FillEventCategory(EsColorConfig.OneInstance.EventColorItems.Values);
        }

        public void ResetData()
        {
            FillEventCategory(EsColorConfig.OneInstance.EventColorItems.Values);
        }

        private EventCategory selectedCategory;

        List<EventCategory> categorys = new List<EventCategory>();

        private void FillEventCategory(IEnumerable<EventCategory> categorys)
        {
            this.categorys.Clear();
            this.categorys.AddRange(categorys);
            category_olv.SetObjects(this.categorys);
            category_olv.Sort(this.olvColumn1,SortOrder.Ascending);
            category_olv.Invalidate();
            this.ok_b.Enabled = false;
        }


        private void objectListView_SelectedIndexChanged(object sender, EventArgs e)
        {
            ok_b.Enabled = this.category_olv.SelectedObject != null;
            selectedCategory = this.category_olv.SelectedObject as EventCategory;
        }

        private void objectListView_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            if (this.category_olv.SelectedObject != null)
            {
                selectedCategory = this.category_olv.SelectedObject as EventCategory;
                this.ok_b.PerformClick();
            }
        }

        public List<int> SelectEvents
        {
            get
            {
                List<int> events = new List<int>();
                if (selectedCategory != null)
                {
                    foreach (EventColorItem eci in selectedCategory.CheckedColorItems)
                    {
                        foreach (EventInfo ei in EventInfoManager.GetInstance().EventInfos)
                        {
                            if (eci.Expression.Contains(ei.Expression) && !events.Contains(ei.ID))
                                events.Add(ei.ID);
                        }
                    }
                }
                return events;
            }
        }

        public void DoRefush()
        {
            DoRefush(selectedCategory.Name);
        }

        public void DoRefush(string str)
        {
            EventColorDialog.GetOneInstance(model).DoRefush(str);
            EsColorConfig.OneInstance.LastCategoryName = str;
        }

        private void showConfig_b_Click(object sender, EventArgs e)
        {
            EventColorDialog ecd = EventColorDialog.GetOneInstance(this.model);
            if (ecd.ShowDialog(this) == DialogResult.OK)
            {
                ecd.DoOk();
                ResetData();
            }
        }
        public bool FilterOffValue9
        {
            get
            {
                return cbxFilterOffV9.Checked;
            }
        }
    }
}