﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MapWinGIS;
using MasterCom.MTGis;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class LowSpeedInfoForm_LTE : MinCloseForm
    {
        public LowSpeedInfoForm_LTE(MainModel mainModel)
            : base(mainModel)
        {
            InitializeComponent();
        }
        public LowSpeedConditionLTE funcCond { get; set; }
        public void FillData(List<DIYLowSpeedInfo_LTE> lowSpeedInfoList_LTE)
        {
            gridControl.DataSource = lowSpeedInfoList_LTE;
            gridControl.RefreshDataSource();
        }

        private void gridControl_DoubleClick(object sender, EventArgs e)
        {
            if (gridView.SelectedRowsCount > 0)
            {
                DIYLowSpeedInfo_LTE lowSpeedItem = gridView.GetRow(gridView.GetSelectedRows()[0]) as DIYLowSpeedInfo_LTE;
                if (lowSpeedItem != null)
                {
                    mModel.DTDataManager.Clear();
                    foreach (TestPoint tp in lowSpeedItem.testPointList)
                    {
                        mModel.DTDataManager.Add(tp);
                    }
                    mModel.FireDTDataChanged(this);
                }
            }
        }

        private void ToolStripMenuItemExport_Click(object sender, EventArgs e)
        {
            try
            {
                ExcelNPOIManager.ExportToExcel(gridView);
            }
            catch
            {
                MessageBox.Show("导出Excel失败！");
            }
        }

        private void miExportShp_Click(object sender, EventArgs e)
        {
            List<DIYLowSpeedInfo_LTE> lowSet = gridControl.DataSource as List<DIYLowSpeedInfo_LTE>;
            if (lowSet == null || lowSet.Count == 0)
            {
                MessageBox.Show("没有数据可供导出!");
                return;
            }
            SaveFileDialog dlg = new SaveFileDialog();
            dlg.RestoreDirectory = true;
            dlg.Filter = FilterHelper.Shp;
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return;
            }
            if (makeShpFile(dlg.FileName, lowSet))
            {
                MessageBox.Show("导出完毕!");
            }
        }

        private bool makeShpFile(string filename, List<DIYLowSpeedInfo_LTE> lowSet)
        {
            Shapefile shpFile = new Shapefile();
            bool result = shpFile.CreateNewWithShapeID("", MapWinGIS.ShpfileType.SHP_POINT);
            if (!result)
            {
                MessageBox.Show(shpFile.get_ErrorMsg(shpFile.LastErrorCode));
                return false;
            }
            shpFile.DefaultDrawingOptions.SetDefaultPointSymbol(tkDefaultPointSymbol.dpsCircle);

            //---列
            int idIdx = 0;
            int fiFileName = idIdx++;
            int fiDateTime = idIdx++;
            int fiCellName = idIdx++;
            int fiLongitude = idIdx++;
            int fiLatitude = idIdx++;
            int fiRsrp = idIdx++;
            int fiAppSpeed = idIdx++;
            int fiDL_Mb = idIdx++;
            int fiUL_Mb = idIdx;
            ShapeHelper.InsertNewField(shpFile, "FileName", FieldType.STRING_FIELD, 10, 30, ref fiFileName);
            ShapeHelper.InsertNewField(shpFile, "DateTime", FieldType.STRING_FIELD, 10, 30, ref fiDateTime);
            ShapeHelper.InsertNewField(shpFile, "CellName", FieldType.STRING_FIELD, 10, 30, ref fiCellName);
            ShapeHelper.InsertNewField(shpFile, "Longitude", FieldType.DOUBLE_FIELD, 10, 30, ref fiLongitude);
            ShapeHelper.InsertNewField(shpFile, "Latitude", FieldType.DOUBLE_FIELD, 10, 30, ref fiLatitude);
            ShapeHelper.InsertNewField(shpFile, "RSRP", FieldType.DOUBLE_FIELD, 10, 30, ref fiRsrp);
            ShapeHelper.InsertNewField(shpFile, "Speed_Kbps", FieldType.DOUBLE_FIELD, 10, 30, ref fiAppSpeed);
            ShapeHelper.InsertNewField(shpFile, "DL_Mb", FieldType.DOUBLE_FIELD, 10, 30, ref fiDL_Mb);
            ShapeHelper.InsertNewField(shpFile, "UL_Mb", FieldType.DOUBLE_FIELD, 10, 30, ref fiUL_Mb);
            //---

            //---行              
            int shpIdx = 0;
            foreach (DIYLowSpeedInfo_LTE item in lowSet)
            {
                foreach (TestPoint tp in item.testPointList)
                {
                    MapWinGIS.Shape spBase = new MapWinGIS.Shape();
                    spBase.Create(ShpfileType.SHP_POINT);
                    MapWinGIS.Point pt = new MapWinGIS.Point();
                    pt.x = tp.Longitude;
                    pt.y = tp.Latitude;
                    int j = 0;
                    spBase.InsertPoint(pt, ref j);
                    shpFile.EditInsertShape(spBase, ref shpIdx);
                    shpFile.EditCellValue(fiFileName, shpIdx, tp.FileName);
                    shpFile.EditCellValue(fiDateTime, shpIdx, tp.DateTime.ToString());
                    ICell cell = tp.GetMainLTECell_TdOrFdd();
                    shpFile.EditCellValue(fiCellName, shpIdx, cell == null ? string.Empty : cell.Name);
                    shpFile.EditCellValue(fiLongitude, shpIdx, tp.Longitude);
                    shpFile.EditCellValue(fiLatitude, shpIdx, tp.Latitude);
                    if (tp is LTEFddTestPoint)
                    {
                        object value = tp["lte_fdd_RSRP"];
                        shpFile.EditCellValue(fiRsrp, shpIdx, getValidData(value));
                        value = tp["lte_fdd_APP_Speed_kb"];
                        shpFile.EditCellValue(fiAppSpeed, shpIdx, getValidData(value));
                        value = tp["lte_fdd_APP_ThroughputDL_Mb"];
                        shpFile.EditCellValue(fiDL_Mb, shpIdx, getValidData(value));
                        value = tp["lte_fdd_APP_ThroughputUL_Mb"];
                        shpFile.EditCellValue(fiUL_Mb, shpIdx, getValidData(value));
                    }
                    else
                    {
                        object value = tp["lte_RSRP"];
                        shpFile.EditCellValue(fiRsrp, shpIdx, getValidData(value));
                        value = tp["lte_APP_Speed_kb"];
                        shpFile.EditCellValue(fiAppSpeed, shpIdx, getValidData(value));
                        value = tp["lte_APP_ThroughputDL_Mb"];
                        shpFile.EditCellValue(fiDL_Mb, shpIdx, getValidData(value));
                        value = tp["lte_APP_ThroughputUL_Mb"];
                        shpFile.EditCellValue(fiUL_Mb, shpIdx, getValidData(value));
                    }
                }
            }

            ShapeHelper.DeleteShpFile(filename);
            try
            {
                bool saved = shpFile.SaveAs(filename, null);
                if (!saved)
                {
                    MessageBox.Show("保存文件失败！" + shpFile.get_ErrorMsg(shpFile.LastErrorCode));
                }
            }
            catch (System.Exception ex)
            {
                MessageBox.Show("保存文件失败！" + ex.Message);
                return false;
            }
            finally
            {
                shpFile.Close();
            }
            return true;
        }

        private double getValidData(object value)
        {
            if (value == null)
            {
                return double.NaN;
            }
            return double.Parse(value.ToString());
        }

        private void toolStripMenuItemShowAllPointMap_Click(object sender, EventArgs e)
        {
            mModel.DTDataManager.Clear();
            if (gridView.IsEmpty)
            {
                return;
            }
            List<DIYLowSpeedInfo_LTE> listLowSpeedItems = this.gridControl.DataSource as List<DIYLowSpeedInfo_LTE>;
            if (this.toolStripMenuItemOnlyShowSelectedPoint.Checked)
            {
                if (this.gridView.SelectedRowsCount <= 0) return;
                foreach (int index in gridView.GetSelectedRows())
                {
                    DIYLowSpeedInfo_LTE lowSpeedItem = gridView.GetRow(index) as DIYLowSpeedInfo_LTE;
                    foreach (TestPoint tp in lowSpeedItem.testPointList)
                    {
                        mModel.DTDataManager.Add(tp);
                    }
                }
                mModel.FireDTDataChanged(this);
            }
            else
            {
                foreach (DIYLowSpeedInfo_LTE lowSpeedItem in listLowSpeedItems)
                {
                    foreach (TestPoint tp in lowSpeedItem.testPointList)
                    {
                        mModel.DTDataManager.Add(tp);
                    }
                }
                mModel.FireDTDataChanged(this);
            }
        }

        
        private void toolStripMenuItemOnlyShowSelectedPoint_Click(object sender, EventArgs e)
        {
            this.toolStripMenuItemOnlyShowSelectedPoint.Checked = !this.toolStripMenuItemOnlyShowSelectedPoint.Checked;
        }
    }
}
