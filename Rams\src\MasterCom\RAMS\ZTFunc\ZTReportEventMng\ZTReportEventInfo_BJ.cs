﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTReportEventInfo_BJ : Event
    {
        public string NetType { get; set; }
        public string AreaName { get; set; }
        public string ProjName { get; set; }
        public string TestDate { get; set; }
        public string TestTime { get; set; }
        public string GridName { get; set; }
        public string CellName { get; set; }
        public new int LAC { get; set; }
        public new int CI { get; set; }
        public string Remark { get; set; }
        public string CauseDeal { get; set; }
        public string CauseProcess { get; set; }
        public string CauseDetailDeal { get; set; }
        public string CauseDetailProcess { get; set; }
        public string MethodDeal { get; set; }
        public string MethodProcess { get; set; }
        public string Solution { get; set; }
        public bool IsRectify { get; set; }
        public bool IsReTest { get; set; }
        public string OptEffect { get; set; }
        public string DwUserName { get; set; }
        public string DwTime { get; set; }
        public string PreprocessTime { get; set; }
        public string PreTypeDesc { get; set; }
        public string ReasonDesc { get; set; }
        public string RoadName { get; set; }
        //审核人
        public string Auditor { get; set; }
        //审核时间
        public int TimeAudit { get; set; }
        public string SavaPath { get; set; }
        public int SeqID { get; set; }
        public int EventTimeSec { get; set; }
        public int EventTimeMs { get; set; }
        public int Bms { get; set; }
        public bool IsKeyProb { get; set; } = false;
        public string EventType { get; set; }
        public string EventName { get; set; }
        public string GridGroup { get; set; }
        public string DeadLine { get; set; }

        public ZTReportEventInfo_BJ()
        {
        }

        public bool Fill(MasterCom.RAMS.Net.Content content, MainModel mainModel)
        {
            this.FileInfo = new Model.FileInfo();
            this.FileInfo.ID = content.GetParamInt();
            this.FileInfo.ProjectID = content.GetParamInt();
            CategoryEnumItem projectItem = ((CategoryEnum)mainModel.CategoryManager["Project"])[ProjectType];
            ProjName = projectItem == null ? null : projectItem.Name;
            SeqID = content.GetParamInt();
            EventTimeSec = content.GetParamInt();
            EventTimeMs = content.GetParamInt();
            //保存时间
            Time = EventTimeSec;
            Millisecond = (short)EventTimeMs;
            base["Value7"] = EventTimeSec - 180;
            this.FileInfo.DistrictID = MainModel.GetInstance().DistrictID;
            TestDate = JavaDate.GetDateTimeFromMilliseconds(EventTimeSec * 1000L).ToShortDateString();
            TestTime = JavaDate.GetDateTimeFromMilliseconds(EventTimeSec * 1000L).ToString("HH:mm:ss") + " " + EventTimeMs.ToString();
            DeadLine = getDeadLine(TestDate);
            Bms = content.GetParamInt();
            ID = content.GetParamInt(); //eventId
            EventName = EventInfo != null ? EventInfo.Name : "";
            if (EventInfo != null)
            {
                EventType = getEventType(EventInfo.ID);
            }
            Longitude = Convert.ToDouble(content.GetParamInt()) / 10000000;
            Latitude = Convert.ToDouble(content.GetParamInt()) / 10000000;
            LAC = content.GetParamInt();
            CI = content.GetParamInt();
            PreTypeDesc = content.GetParamString();
            ReasonDesc = content.GetParamString();
            CellName = content.GetParamString();

            string strAreaName = content.GetParamString();
            bool isSet = setGridAndArea(strAreaName);
            if (!isSet)
            {
                return false;
            }

            RoadName = content.GetParamString();
            this.FileInfo.Name = content.GetParamString();
            this.FileInfo.ServiceType = content.GetParamInt();
            this.FileInfo.SampleTbName = content.GetParamString();
            Remark = content.GetParamString();
            NetType = content.GetParamString();
            CauseDeal = content.GetParamString();
            CauseDetailDeal = content.GetParamString();
            MethodDeal = content.GetParamString();
            Solution = content.GetParamString();
            IsRectify = content.GetParamInt() != 0;
            IsReTest = content.GetParamInt() != 0;
            OptEffect = content.GetParamString();
            DwUserName = content.GetParamString();
            DwTime = content.GetParamString();
            IsKeyProb = content.GetParamInt() == 1;
            PreprocessTime = content.GetParamString();
            CauseProcess = content.GetParamString();
            CauseDetailProcess = content.GetParamString();
            MethodProcess = content.GetParamString();
            this.FileInfo.LogTable = "tb_log_file_" + JavaDate.GetDateTimeFromMilliseconds(EventTimeSec * 1000L).ToString("yyyy_MM");
            return true;
        }

        private bool setGridAndArea(string strAreaName)
        {
            //城二_西城|东南77|[1|2|3]
            string[] arr = strAreaName.Split('[');
            if (arr.Length == 2)
            {
                return setArrGridAndArea(arr);
            }
            else
            {
                return setOldArrGridAndArea(strAreaName);
            }
        }

        private bool setArrGridAndArea(string[] arr)
        {
            string[] s1 = arr[0].Split('|');
            string[] s2 = arr[1].Split('|');
            if (s1.Length >= 2)
            { //城一_二组|东北26|| 道路信息：北土城西路
                GridGroup = s1[0];
                if (!string.IsNullOrEmpty(GridGroup) && GridGroup.Contains("_"))
                {
                    AreaName = GridGroup.Substring(0, GridGroup.IndexOf('_'));
                }
                else
                { //后台匹配不到区域，过滤掉。
                    return false;
                }
                if (s2[1].Equals("2")
                    || s2[1].Equals("2]"))
                {
                    GridName = s1[1];
                }
            }
            else
            {
                return false;
            }
            return true;
        }

        private bool setOldArrGridAndArea(string strAreaName)
        {
            string[] arrOld = strAreaName.Split('|');
            if (arrOld.Length >= 2)
            { //城一_二组|东北26|| 道路信息：北土城西路
                GridGroup = arrOld[0];
                if (!string.IsNullOrEmpty(GridGroup) && GridGroup.Contains("_"))
                {
                    AreaName = GridGroup.Substring(0, GridGroup.IndexOf('_'));
                }
                else
                { //后台匹配不到区域，过滤掉。
                    return false;
                }
                GridName = arrOld[1];
            }
            else
            {
                return false;
            }
            return true;
        }

        private static string getEventType(int eventID)
        {
            string eventType = string.Empty;

            switch (eventID)
            {
                case 6:
                case 907:
                    eventType = "掉话"; // "GSM主叫掉话"
                    break;
                case 7:
                case 908:
                    eventType = "掉话"; // "GSM被叫掉话"
                    break;
                case 8:
                case 10:
                case 82:
                case 88:
                    eventType = "未接通"; //"GSM主叫接入失败"
                    break;

                case 106:
                case 112:
                case 199:
                    eventType = "掉话"; // "TD主叫掉话"
                    break;
                case 118:
                case 124:
                case 200:
                    eventType = "掉话"; // "TD被叫掉话"
                    break;
                case 105:
                case 111:
                case 189:
                case 201:
                    eventType = "未接通"; //"TD主叫接入失败"
                    break;
                case 2003:
                    eventType = "重叠覆盖";//LTE扫频高重叠覆盖道路
                    break;
                case 1006:
                case 1007:
                case 1016:
                case 1017:
                case 1026:
                case 1027:
                case 1036:
                case 1037:
                case 1046:
                case 1047:
                case 1056:
                case 1057:
                    eventType = "掉话";//LTECSFB主叫未接通
                    break;
                case 1008:
                case 1028:
                case 1048:
                case 1058:
                    eventType = "未接通";//LTECSFB主叫未接通
                    break;
                default:
                    break;
            }
            return eventType;
        }


        /// <summary>
        /// 获取事件的最迟反馈时间，时间为测试时间延后3天的17点
        /// </summary>
        /// <param name="eventTimeSec"></param>
        /// <returns></returns>
        private static string getDeadLine(string testDate)
        {
            DateTime testData = DateTime.Parse(testDate).Date;
            int restWorkDay = DayOfWeek.Friday - testData.DayOfWeek;
            int days2Add = 0;
            if (restWorkDay < 0)
            {
                days2Add = 4;
            }
            else if (restWorkDay >= 3)
            {
                days2Add = 3;
            }
            else
            {
                days2Add = 5;
            }
            return testData.AddDays(days2Add).AddHours(17).ToString("yyyy/MM/dd HH:mm:ss");
        }
    }
}