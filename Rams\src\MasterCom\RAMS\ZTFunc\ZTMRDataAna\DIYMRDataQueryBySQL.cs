﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Data;
using System.Data.SqlClient;
using System.Reflection;
using System.Windows.Forms;
using System.IO;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    /// <summary>
    /// MR参数查询主类
    /// </summary>
    public class DIYMRDataQueryBySQL
    {
        private MRDataCondition condition;
        private readonly List<MRDataResult> resultList;
        private static DIYMRDataSettingForm setForm { get; set; }
        private readonly MainModel mainModel;

        private readonly Dictionary<int, MRDataCell> iCellDict;
        private readonly Dictionary<int, MRDataRxLev> iRxLevDict;
        private readonly Dictionary<int, MRDataQual> iQualDict;
        private readonly Dictionary<int, MRDataTA> iTADict;
        private readonly Dictionary<int, MRDataPL> iPLDict;

        public DIYMRDataQueryBySQL(MainModel mainModel)
        {
            this.mainModel = mainModel;
            condition = new MRDataCondition();
            iCellDict = new Dictionary<int, MRDataCell>();
            iRxLevDict = new Dictionary<int, MRDataRxLev>();
            iQualDict = new Dictionary<int, MRDataQual>();
            iTADict = new Dictionary<int, MRDataTA>();
            iPLDict = new Dictionary<int, MRDataPL>();
            resultList = new List<MRDataResult>();
        }

        public bool GetCondition()
        {
            if (setForm == null)
            {
                setForm = new DIYMRDataSettingForm();
            }
            if (setForm.ShowDialog() != DialogResult.OK || !setForm.GetCondition(ref condition))
            {
                return false;
            }
            return true;
        }

        public void DoQuery()
        {
            WaitBox.Show("正在查询数据...", DoQueryInWaitBox);
        }

        public void FireShowResultForm()
        {
            if (isDBError)
            {
                isDBError = false;
                MessageBox.Show("获取MR参数失败，请检查配置文件:\nCellParamSetting.xml", "错误");
                return;
            }

            object obj = MainModel.GetInstance().GetObjectFromBlackboard(typeof(DIYMRDataResultForm).FullName);
            DIYMRDataResultForm form = obj == null ? null : obj as DIYMRDataResultForm;
            if (form == null || form.IsDisposed)
            {
                form = new DIYMRDataResultForm(mainModel, condition);
            }
            form.FillData(resultList);
            if (!form.Visible)
            {
                form.Show(mainModel.MainForm);
            }
        }

        private bool isDBError = false;
        private void DoQueryInWaitBox()
        {
            try
            {
                FillDataDict();
            }
            catch
            {
                System.Threading.Thread.Sleep(500);
                WaitBox.Close();
                isDBError = true;
                return;
            }

            int loop = 0;
            WaitBox.Text = "正在处理数据...";
            foreach (int iCellID in iCellDict.Keys)
            {
                WaitBox.ProgressPercent = ++loop * 100 / iCellDict.Count;
                if (!iRxLevDict.ContainsKey(iCellID) || !iQualDict.ContainsKey(iCellID)
                    || !iTADict.ContainsKey(iCellID) || !iPLDict.ContainsKey(iCellID))
                {
                    continue;
                }
                MRDataResult resultItem = new MRDataResult(iCellDict[iCellID].Lac, iCellDict[iCellID].Ci);
                CalculateRxLev(resultItem, iCellID);
                CalculateQual(resultItem, iCellID);
                CalculateTA(resultItem, iCellID);
                CalculatePL(resultItem, iCellID);
                resultItem.ID = resultList.Count + 1;
                resultList.Add(resultItem);
            }

            WaitBox.Close();
        }

        /// <summary>
        /// 查询各个数据表填充到相应字典
        /// </summary>
        private void FillDataDict()
        {
            MRDataSQLQuerier.ReloadConfig();

            addCellDict();

            addRxLevDict();

            addQualDict();

            addTADict();

            addPLDict();
        }

        private void addCellDict()
        {
            Dictionary<int, List<GSMCellSign>> signDict = CellSignManager.GetInstance().SignCellMap;
            foreach (int iCellID in signDict.Keys)
            {
                if (iCellDict.ContainsKey(iCellID))
                {
                    continue;
                }
                MRDataCell cell = new MRDataCell();
                cell.iCellID = iCellID;
                cell.Lac = signDict[iCellID][0].LAC;
                cell.Ci = signDict[iCellID][0].CI;
                iCellDict.Add(cell.iCellID, cell);
            }
        }

        private void addRxLevDict()
        {
            List<MRDataRxLev> rxLevList = MRDataRxLev.GetInstanceList<MRDataRxLev>(condition.StartTime, condition.EndTime);
            foreach (MRDataRxLev item in rxLevList)
            {
                int iCellID = (int)item["iCELLID"];
                if (!iRxLevDict.ContainsKey(iCellID))
                {
                    iRxLevDict.Add(iCellID, item);
                }
                else
                {
                    iRxLevDict[iCellID].Add(item.Datas);
                }
            }
        }

        private void addQualDict()
        {
            List<MRDataQual> qualList = MRDataQual.GetInstanceList<MRDataQual>(condition.StartTime, condition.EndTime);
            foreach (MRDataQual item in qualList)
            {
                int iCellID = (int)item["iCELLID"];
                if (!iQualDict.ContainsKey(iCellID))
                {
                    iQualDict.Add(iCellID, item);
                }
                else
                {
                    iQualDict[iCellID].Add(item.Datas);
                }
            }
        }

        private void addTADict()
        {
            List<MRDataTA> taList = MRDataTA.GetInstanceList<MRDataTA>(condition.StartTime, condition.EndTime);
            foreach (MRDataTA item in taList)
            {
                int iCellID = (int)item["iCELLID"];
                if (!iTADict.ContainsKey(iCellID))
                {
                    iTADict.Add(iCellID, item);
                }
                else
                {
                    iTADict[iCellID].Add(item.Datas);
                }
            }
        }

        private void addPLDict()
        {
            List<MRDataPL> plList = MRDataPL.GetInstanceList<MRDataPL>(condition.StartTime, condition.EndTime);
            foreach (MRDataPL item in plList)
            {
                int iCellID = (int)item["iCELLID"];
                if (!iPLDict.ContainsKey(iCellID))
                {
                    iPLDict.Add(iCellID, item);
                }
                else
                {
                    iPLDict[iCellID].Add(item.Datas);
                }
            }
        }

        /// <summary>
        /// CalculateXXXX为各种参数计算
        /// </summary>
        private int[] halfRxLev { get; set; } = { 53, 56, 63, 69, 75, 82, 88, 94, 100, 104 };
        private void CalculateRxLev(MRDataResult result, int iCellID)
        {
            result.UL90Cover = Math.Round(Convert.ToDouble(iRxLevDict[iCellID]["采样点数_UL_90覆盖率"])
                                / Convert.ToDouble(iRxLevDict[iCellID]["RXLEV_UL_CNT"]) * 100, 2);
            result.DL90Cover = Math.Round(Convert.ToDouble(iRxLevDict[iCellID]["采样点数_DL_90覆盖率"])
                                / Convert.ToDouble(iRxLevDict[iCellID]["RXLEV_DL_CNT"]) * 100, 2);

            double countUL = 0;
            double sumUL = 0;
            for (int i = 14, j = 0; i >= 5; --i, ++j)
            {
                double cnt = Convert.ToDouble(iRxLevDict[iCellID].Datas[i]);
                countUL += cnt;
                sumUL += cnt * halfRxLev[j];
            }

            double countDL = 0;
            double sumDL = 0;
            for (int i = 24, j = 0; i >= 15; --i, ++j)
            {
                double cnt = Convert.ToDouble(iRxLevDict[iCellID].Datas[i]);
                countDL += cnt;
                sumDL += cnt * halfRxLev[j];
            }

            result.CoverImbalance = Math.Round(Math.Abs(sumUL / countUL - sumDL / countDL), 2);
        }

        private void CalculateQual(MRDataResult result, int iCellID)
        {
            double count = 0;
            for (int i = 3; i < 8; ++i)
            {
                count += Convert.ToDouble(iQualDict[iCellID].Datas[i]);
            }
            result.ULQual = Math.Round(count / Convert.ToDouble(iQualDict[iCellID]["RXQUAL_UL_CNT"]) * 100, 2);

            count = 0;
            for (int i = 11; i < 16; ++i)
            {
                count += Convert.ToDouble(iQualDict[iCellID].Datas[i]);
            }
            result.DLQual = Math.Round(count / Convert.ToDouble(iQualDict[iCellID]["RXQUAL_DL_CNT"]) * 100, 2);
        }

        private void CalculateTA(MRDataResult result, int iCellID)
        {
            double count = 0;
            for (int i = 4; i <= 6; ++i)
            {
                count += Convert.ToDouble(iTADict[iCellID].Datas[i]);
            }
            result.TA = Math.Round(count / Convert.ToDouble(iTADict[iCellID]["TA_CNT"]) * 100, 2);
        }

        private void CalculatePL(MRDataResult result, int iCellID)
        {
            double count = 0;
            for (int i = 6; i <= 11; ++i)
            {
                count += Convert.ToDouble(iPLDict[iCellID].Datas[i]);
            }
            result.PathLossImbalance = Math.Round(count / Convert.ToDouble(iPLDict[iCellID]["PL_DIFF_CNT"]) * 100, 2);
        }
    }

    /// <summary>
    /// MR参数条件，除了时间以外，其他只用于报表着色
    /// </summary>
    public class MRDataCondition
    {
        public DateTime StartTime { get; set; } 
        public DateTime EndTime { get; set; } 
        public double UL90Cover { get; set; } 
        public double DL90Cover { get; set; } 
        public double CoverImbalance { get; set; } 
        public double ULQual { get; set; } 
        public double DLQual { get; set; } 
        public double TA { get; set; } 
        public double PathLossImbalance { get; set; } 

        public MRDataCondition()
        {
            StartTime = DateTime.Now.Date;
            EndTime = DateTime.Now.Date;
            UL90Cover = 0.95;
            DL90Cover = 0.95;
            CoverImbalance = 0.1;
            ULQual = 0.95;
            DLQual = 0.95;
            TA = 0.9;
            PathLossImbalance = 0.9;
        }
    }

    /// <summary>
    /// MR参数查询结果
    /// </summary>
    public class MRDataResult
    {
        public int ID { get; set; }
        public int Lac { get; set; }
        public int Ci { get; set; }
        public double UL90Cover { get; set; }
        public double DL90Cover { get; set; }
        public double CoverImbalance { get; set; }
        public double ULQual { get; set; }
        public double DLQual { get; set; }
        public double TA { get; set; }
        public double PathLossImbalance { get; set; }

        public MRDataResult(int lac, int ci)
        {
            Lac = lac;
            Ci = ci;
        }
    }

    /// <summary>
    /// 各种MR参数
    /// </summary>
    public class MRDataCell
    {
        public int iCellID { get; set; } 
        public int Lac { get; set; } 
        public int Ci { get; set; } 

        private static string tableName = "MTNOH_AAA_Resource2..TB_GSM_小区标识";
        public static List<MRDataCell> GetInstanceList()
        {
            List<MRDataCell> retList = new List<MRDataCell>();
            List<object[]> objList = MRDataSQLQuerier.DoQuery(tableName);
            foreach (object[] objs in objList)
            {
                MRDataCell mr = new MRDataCell();
                mr.iCellID = Convert.ToInt32(objs[0]);
                mr.Lac = Convert.ToInt32(objs[1]);
                mr.Ci = Convert.ToInt32(objs[2]);
                retList.Add(mr);
            }
            return retList;
        }
    }

    public class MRDataRxLev
    {
        public object[] Datas { get; set; }
        protected string[] FieldNames;
        protected Dictionary<string, int> NameIndexDict;

        public MRDataRxLev()
        {
            tableName = "[MTNOH_GSM_MR_AAA].[dbo].[MRR_小时_小区_RXLEV]";
            fieldNames = new string[]
            {
                "时间",
                "iCELLID",
                "厂商ID",
                "RXLEV_UL_CNT",
                "RXLEV_DL_CNT",
                "RXLEV_UL_104_LOWER",
                "RXLEV_UL_104_97",
                "RXLEV_UL_97_91",
                "RXLEV_UL_91_85",
                "RXLEV_UL_85_79",
                "RXLEV_UL_79_72",
                "RXLEV_UL_72_66",
                "RXLEV_UL_66_60",
                "RXLEV_UL_60_53",
                "RXLEV_UL_53_UPPER",
                "RXLEV_DL_104_LOWER",
                "RXLEV_DL_104_97",
                "RXLEV_DL_97_91",
                "RXLEV_DL_91_85",
                "RXLEV_DL_85_79",
                "RXLEV_DL_79_72",
                "RXLEV_DL_72_66",
                "RXLEV_DL_66_60",
                "RXLEV_DL_60_53",
                "RXLEV_DL_53_UPPER",
                "采样点数_UL_95覆盖率",
                "采样点数_UL_90覆盖率",
                "采样点数_UL_85覆盖率",
                "采样点数_DL_95覆盖率",
                "采样点数_DL_90覆盖率",
                "采样点数_DL_85覆盖率",
            };
            nameIndexDict = InitIndexDict(fieldNames);

            FieldNames = fieldNames;
            NameIndexDict = nameIndexDict;
        }

        public object this[string name]
        {
            get
            {
                return Datas[NameIndexDict[name]];
            }
            set
            {
                Datas[NameIndexDict[name]] = value;
            }
        }

        public void Add(object[] datas)
        {
            for (int i = 3; i < FieldNames.Length; ++i)
            {
                Datas[i] = Convert.ToDouble(Datas[i]) + Convert.ToDouble(datas[i]);
            }
        }

        public static List<T> GetInstanceList<T>(DateTime startTime, DateTime endTime)
        {
            return GetInstanceList<T>(tableName, fieldNames, startTime, endTime);
        }

        protected static List<T> GetInstanceList<T>(string tbName, string[] fdNames, DateTime startTime, DateTime endTime)
        {
            List<T> retList = new List<T>();
            List<object[]> objList = MRDataSQLQuerier.DoQuery(tbName, fdNames, startTime, endTime);
            FieldInfo info = typeof(T).GetField("Datas");
            foreach (object[] objs in objList)
            {
                T t = (T)Activator.CreateInstance(typeof(T));
                info.SetValue(t, objs);
                retList.Add(t);
            }
            return retList;
        }

        protected static Dictionary<string, int> InitIndexDict(string[] strs)
        {
            Dictionary<string, int> retDict = new Dictionary<string, int>();
            for (int i = 0; i < strs.Length; ++i)
            {
                retDict.Add(strs[i], i);
            }
            return retDict;
        }

        private static Dictionary<string, int> nameIndexDict { get; set; }
        private static string tableName { get; set; }
        private static string[] fieldNames { get; set; }
    }

    public class MRDataQual : MRDataRxLev
    {
        public MRDataQual()
        {
            tableName = "[MTNOH_GSM_MR_AAA].[dbo].[MRR_小时_小区_RXQUAL]";
            fieldNames = new string[]
            {
                "时间",
                "iCELLID",
                "厂商ID",
                "RXQUAL_UL_0",
                "RXQUAL_UL_1",
                "RXQUAL_UL_2",
                "RXQUAL_UL_3",
                "RXQUAL_UL_4",
                "RXQUAL_UL_5",
                "RXQUAL_UL_6",
                "RXQUAL_UL_7",
                "RXQUAL_DL_0",
                "RXQUAL_DL_1",
                "RXQUAL_DL_2",
                "RXQUAL_DL_3",
                "RXQUAL_DL_4",
                "RXQUAL_DL_5",
                "RXQUAL_DL_6",
                "RXQUAL_DL_7",
                "RXQUAL_UL_CNT",
                "RXQUAL_DL_CNT",
            };
            nameIndexDict = InitIndexDict(fieldNames);

            FieldNames = fieldNames;
            NameIndexDict = nameIndexDict;
        }

        public static new List<T> GetInstanceList<T>(DateTime startTime, DateTime endTime)
        {
            return GetInstanceList<T>(tableName, fieldNames, startTime, endTime);
        }

        private static Dictionary<string, int> nameIndexDict { get; set; }
        private static string tableName { get; set; }
        private static string[] fieldNames { get; set; }
    }

    public class MRDataTA : MRDataRxLev
    {
        public MRDataTA()
        {
            tableName = "[MTNOH_GSM_MR_AAA].[dbo].[MRR_小时_小区_TA]";
            fieldNames = new string[]
            {
                "时间",
                "iCELLID",
                "厂商ID",
                "TA_CNT",
                "TA_0",
                "TA_1",
                "TA_2",
                "TA_3",
                "TA_4",
                "TA_5",
                "TA_6",
                "TA_7_11",
                "TA_12_19",
                "TA_20_UPPER",
            };
            nameIndexDict = InitIndexDict(fieldNames);
            FieldNames = fieldNames;
            NameIndexDict = nameIndexDict;
        }

        public static new List<T> GetInstanceList<T>(DateTime startTime, DateTime endTime)
        {
            return GetInstanceList<T>(tableName, fieldNames, startTime, endTime);
        }

        private static Dictionary<string, int> nameIndexDict { get; set; }
        private static string tableName { get; set; }
        private static string[] fieldNames { get; set; }
    }

    public class MRDataPL : MRDataRxLev
    {
        public MRDataPL()
        {
            tableName = "[MTNOH_GSM_MR_AAA].[dbo].[MRR_小时_小区_PATHLOSS_DIFF]";
            fieldNames = new string[]
            {
                "时间",
                "iCELLID",
                "厂商ID",
                "PL_DIFF_CNT",
                "PL_DIFF_M20_LOWER",
                "PL_DIFF_M20_M12",
                "PL_DIFF_M12_M8",
                "PL_DIFF_M8_M4",
                "PL_DIFF_M4_0",
                "PL_DIFF_0_4",
                "PL_DIFF_4_8",
                "PL_DIFF_8_12",
                "PL_DIFF_12_20",
                "PL_DIFF_20_UPPER",
            };
            nameIndexDict = InitIndexDict(fieldNames);
            FieldNames = fieldNames;
            NameIndexDict = nameIndexDict;
        }

        public static new List<T> GetInstanceList<T>(DateTime startTime, DateTime endTime)
        {
            return GetInstanceList<T>(tableName, fieldNames, startTime, endTime);
        }

        private static Dictionary<string, int> nameIndexDict { get; set; }
        private static string tableName { get; set; }
        private static string[] fieldNames { get; set; }
    }

    /// <summary>
    /// 特定于MR参数的查询器
    /// </summary>
    public static class MRDataSQLQuerier
    {
        private static string connectString;

        public static void ReloadConfig()
        {
            connectString = CellParamCfgManager.GetInstance().DBConnectionStr;
        }

        public static List<object[]> DoQuery(string tableName, string[] fieldNames, DateTime startTime, DateTime endTime)
        {
            List<object[]> retList = new List<object[]>();
            string sql = BuildSQLString(tableName, fieldNames, startTime, endTime);
            using (SqlDataReader reader = SqlHelper.ExecuteReader(connectString, CommandType.Text, sql))
            {
                bool isDbNull = false;
                int counter = 0, curPercent = 11;
                while (reader.Read())
                {
                    SetWaitBoxProgress(ref counter, ref curPercent);

                    isDbNull = false;
                    object[] objs = new object[fieldNames.Length];
                    for (int i = 0; i < fieldNames.Length; ++i)
                    {
                        if (Convert.IsDBNull(reader[i]))
                        {
                            isDbNull = true;
                            break;
                        }
                        objs[i] = reader[i];
                    }
                    if (!isDbNull)
                    {
                        retList.Add(objs);
                    }
                }
            }
            return retList;
        }

        private static string BuildSQLString(string tableName, string[] fieldNames, DateTime startTime, DateTime endTime)
        {
            StringBuilder sb = new StringBuilder();
            sb.Append("SELECT ");
            for (int i = 0; i < fieldNames.Length - 1; ++i)
            {
                sb.Append("[" + fieldNames[i] + "], ");
            }
            sb.Append("[" + fieldNames[fieldNames.Length - 1] + "]");
            sb.Append("FROM " + tableName + " WHERE [iCELLID] IS NOT NULL AND [时间] BETWEEN '");
            sb.Append(startTime + "' AND '" + endTime + "' ORDER BY [iCELLID]");
            return sb.ToString();
        }

        private static void SetWaitBoxProgress(ref int counter, ref int curPercent)
        {
            int tmp = (int)(Math.Log(counter++) * (100000 / 10000));
            if (tmp < 95 && tmp > 0 && curPercent != tmp)
            {
                WaitBox.ProgressPercent = tmp;
                curPercent = tmp;
            }
            else if (tmp > 98)
            {
                curPercent = 5;
                counter = 0;
            }
        }

        /// <summary>
        /// 不再使用
        /// </summary>
        /// <param name="tableName"></param>
        /// <returns></returns>
        public static List<object[]> DoQuery(string tableName)
        {
            List<object[]> retList = new List<object[]>();
            string sql = "SELECT [自维护ID], [LAC], [CI] FROM " + tableName + " WHERE [自维护ID] IS NOT NULL ORDER BY [自维护ID]";
            using (SqlDataReader reader = SqlHelper.ExecuteReader(connectString, CommandType.Text, sql))
            {
                int counter = 0, curPercent = 11;
                bool isDbNull = false;
                while (reader.Read())
                {
                    SetWaitBoxProgress(ref counter, ref curPercent);

                    isDbNull = false;
                    object[] objs = new object[3];
                    for (int i = 0; i < 3; ++i)
                    {
                        if (Convert.IsDBNull(reader[i]))
                        {
                            isDbNull = true;
                            break;
                        }
                        objs[i] = (int)reader[i];
                    }
                    if (!isDbNull)
                    {
                        retList.Add(objs);
                    }
                }
            }
            return retList;
        }
    }
}
