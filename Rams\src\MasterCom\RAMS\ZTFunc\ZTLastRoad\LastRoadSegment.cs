﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class LastRoadSegment
    {
        private readonly List<TestPoint> testPoints = new List<TestPoint>();
        public List<TestPoint> TestPoints
        {
            get { return testPoints; }
        }
        public LastRoadSegment(TestPoint tp, List<TestPointDisplayColumn> cols)
        {
            AddTestPoint(tp, 0, cols);
        }

        public double this[TestPointDisplayColumn col]
        {
            get
            {
                double retValue = double.NaN;
                if (col.ValueType == ESummaryValueType.Average)
                {
                    double total;
                    int cnt = 0;
                    if (paramTotalValueDic.TryGetValue(col.ParamKey, out total) && paramCountDic.TryGetValue(col.ParamKey, out cnt))
                    {
                        return Math.Round(total / cnt, 2);
                    }
                    return double.NaN;
                }
                else if (col.ValueType == ESummaryValueType.Max)
                {
                    double max;
                    if (paramMaxValueDic.TryGetValue(col.ParamKey, out max))
                    {
                        return max;
                    }
                    return double.NaN;
                }
                else if (col.ValueType == ESummaryValueType.Min)
                {
                    double min;
                    if (paramMinValueDic.TryGetValue(col.ParamKey, out min))
                    {
                        return min;
                    }
                    return double.NaN;
                }
                return retValue;
            }
        }
        public string FileName
        {
            get { return this.LastTestPoint.FileName; }
        }
        /// <summary>
        /// 路段中，按时间次序的最后一采样点
        /// </summary>
        public TestPoint LastTestPoint
        {
            get { return testPoints[testPoints.Count - 1]; }
        }

        public double StaySecond
        {
            get
            {
                return (LastTestPoint.DateTime - testPoints[0].DateTime).TotalSeconds;
            }
        }

        public double CenterLatitude
        {
            get { return CenterTestPoint.Latitude; }
        }
        public double CenterLongitude
        {
            get { return CenterTestPoint.Longitude; }
        }
        public TestPoint CenterTestPoint
        {
            get { return testPoints[testPoints.Count / 2]; }
        }

        private ICell lastCell = null;
        internal void AddTestPoint(TestPoint testPoint, double distance2LastPoint, List<TestPointDisplayColumn> cols)
        {
            Distance += distance2LastPoint;
            testPoints.Add(testPoint);
            List<string> paramsPast = new List<string>();
            foreach (TestPointDisplayColumn col in cols)
            {
                string paramKey = col.ParamKey;
                if (paramsPast.Contains(paramKey))
                {//同一指标，可能有不同列（极值or平均值），只需要取一次指标即可
                    continue;
                }
                else
                {
                    paramsPast.Add(paramKey);
                }
                double dValue;
                object objValue = testPoint[col.DisplayParam.ParamInfo.Name, col.ParamArrayIndex];
                if (objValue != null && double.TryParse(objValue.ToString(), out dValue))
                {
                    saveTestPointParamValue(col, dValue);
                }
            }

            ICell cell = testPoint.GetMainCell();
            if (cell != null)
            {
                LastRoadSegmentCellItem curCellItem = CellItems.Find(delegate(LastRoadSegmentCellItem xItem)
                       { return xItem.Cell.Name == cell.Name; });
                if (curCellItem == null)
                {//新小区
                    curCellItem = new LastRoadSegmentCellItem(cell, testPoint, cols);
                    CellItems.Add(curCellItem);
                    if (lastCell != null)
                    {
                        LastRoadSegmentCellItem lastCellItem = CellItems.Find(
                            delegate(LastRoadSegmentCellItem lstItem)
                            { return lstItem.Cell.Name == lastCell.Name; });
                        lastCellItem.AddEndEdgedSegInfo(testPoint, distance2LastPoint);
                    }
                }
                else
                {
                    addTestPoint(testPoint, distance2LastPoint, cols, cell, curCellItem);
                }
            }
            lastCell = cell;
        }

        private void addTestPoint(TestPoint testPoint, double distance2LastPoint, List<TestPointDisplayColumn> cols, 
            ICell cell, LastRoadSegmentCellItem curCellItem)
        {
            if ((lastCell is UnknowCell && cell is UnknowCell) || lastCell == cell)
            {//同一小区持续
                curCellItem.AddTestPoint(false, testPoint, distance2LastPoint, cols);
            }
            else
            {//已有小区，出现断口，持续距离和时间累积到上一cell路段
                curCellItem.AddTestPoint(true, testPoint, 0, cols);
                if (lastCell != null)
                {
                    LastRoadSegmentCellItem lastCellItem = CellItems.Find(
                        delegate (LastRoadSegmentCellItem lstItem)
                        { return lstItem.Cell.Name == lastCell.Name; });
                    lastCellItem.AddEndEdgedSegInfo(testPoint, distance2LastPoint);
                }
            }
        }

        private readonly Dictionary<string, int> paramCountDic = new Dictionary<string, int>();
        private readonly Dictionary<string, double> paramMinValueDic = new Dictionary<string, double>();
        private readonly Dictionary<string, double> paramMaxValueDic = new Dictionary<string, double>();
        private readonly Dictionary<string, double> paramTotalValueDic = new Dictionary<string, double>();
        private void saveTestPointParamValue(TestPointDisplayColumn col, double value)
        {
            if (col.DisplayParam.ValueMin > value || col.DisplayParam.ValueMax < value)
            {//不在有效值域內，过滤掉
                return;
            }
            string nameKey = col.ParamKey;
            //采样点个数，指标总和值处理，为求平均做准备
            if (paramCountDic.ContainsKey(nameKey))
            {
                paramCountDic[nameKey]++;
                paramTotalValueDic[nameKey] += value;
            }
            else
            {
                paramCountDic.Add(nameKey, 1);
                paramTotalValueDic.Add(nameKey, value);
            }

            double min;
            if (paramMinValueDic.TryGetValue(nameKey, out min))
            {
                paramMinValueDic[nameKey] = Math.Min(min, value);
            }
            else
            {
                paramMinValueDic.Add(nameKey, value);
            }

            double max;
            if (paramMaxValueDic.TryGetValue(nameKey, out max))
            {
                paramMaxValueDic[nameKey] = Math.Max(max, value);
            }
            else
            {
                paramMaxValueDic.Add(nameKey, value);
            }
        }

        public double Distance { get; set; }

        private string roadNames;
        public string RoadNames
        {
            get { return roadNames; }
        }
        
        public List<LastRoadSegmentCellItem> CellItems { get; set; } = new List<LastRoadSegmentCellItem>();
        private string cellsToken=string.Empty;
        public string CellsToken
        {
            get { return cellsToken; }
        }
        internal void MakeSummary(LastRoadReport report)
        {
            List<double> lngs = new List<double>();
            List<double> lats = new List<double>();
            lngs.Add(testPoints[0].Longitude);
            lats.Add(testPoints[0].Latitude);
            lngs.Add(CenterLongitude);
            lats.Add(CenterLatitude);
            lngs.Add(this.LastTestPoint.Longitude);
            lats.Add(this.LastTestPoint.Latitude);
            roadNames = GISManager.GetInstance().GetRoadPlaceDesc(lngs, lats);
            StringBuilder sb = new StringBuilder();
            foreach (LastRoadSegmentCellItem cellItem in CellItems)
            {
                cellItem.MakeSummary();
                if (cellItem.Cell!=null&& !(cellItem.Cell is UnknowCell))
                {
                    sb.Append(cellItem.Cell.Token + "|");
                }
            }
            cellsToken = sb.ToString();
            if (cellsToken.Length>0)
            {
                cellsToken = cellsToken.Remove(cellsToken.Length - 1, 1);
            }
            lastCell = null;
        }

    }

    public class LastRoadSegmentCellItem
    {
        private readonly ICell cell;
        public ICell Cell
        {
            get { return cell; }
        }
        public LastRoadSegmentCellItem(ICell cell, TestPoint tp, List<TestPointDisplayColumn> cols)
        {
            this.cell = cell;
            AddTestPoint(false, tp, 0, cols);
        }
        private readonly List<TestPoint> testPoints = new List<TestPoint>();
        public List<TestPoint> TestPoints
        {
            get { return testPoints; }
        }

        public double this[TestPointDisplayColumn col]
        {
            get
            {
                double retValue = double.NaN;
                if (col.ValueType == ESummaryValueType.Average)
                {
                    double total;
                    int cnt = 0;
                    if (paramTotalValueDic.TryGetValue(col.ParamKey, out total) && paramCountDic.TryGetValue(col.ParamKey, out cnt))
                    {
                        return Math.Round(total / cnt, 2);
                    }
                    return double.NaN;
                }
                else if (col.ValueType == ESummaryValueType.Max)
                {
                    double max;
                    if (paramMaxValueDic.TryGetValue(col.ParamKey, out max))
                    {
                        return max;
                    }
                    return double.NaN;
                }
                else if (col.ValueType == ESummaryValueType.Min)
                {
                    double min;
                    if (paramMinValueDic.TryGetValue(col.ParamKey, out min))
                    {
                        return min;
                    }
                    return double.NaN;
                }
                return retValue;
            }
        }
        /// <summary>
        /// 路段中，按时间次序的最后一采样点
        /// </summary>
        public TestPoint LastTestPoint
        {
            get { return testPoints[testPoints.Count - 1]; }
        }

        private double staySecond = 0;
        public double StaySecond
        {
            get { return staySecond; }
        }

        public double CenterLatitude
        {
            get { return CenterTestPoint.Latitude; }
        }
        public double CenterLongitude
        {
            get { return CenterTestPoint.Longitude; }
        }
        public TestPoint CenterTestPoint
        {
            get { return testPoints[testPoints.Count / 2]; }
        }

        public void AddEndEdgedSegInfo(TestPoint testPoint, double distance2LastPoint)
        {
            Distance += distance2LastPoint;
            staySecond += (testPoint.DateTime - testPoints[testPoints.Count - 1].DateTime).TotalSeconds;
        }

        internal void AddTestPoint(bool isBroken,TestPoint testPoint, double distance2LastPoint, List<TestPointDisplayColumn> cols)
        {
            testPoints.Add(testPoint);
            if (!isBroken && testPoints.Count > 1)
            {
                staySecond += (testPoints[testPoints.Count - 1].DateTime - testPoints[testPoints.Count - 2].DateTime).TotalSeconds;
            }
            Distance += distance2LastPoint;
            List<string> paramsPast = new List<string>();
            foreach (TestPointDisplayColumn col in cols)
            {
                string paramKey = col.ParamKey;
                if (paramsPast.Contains(paramKey))
                {//同一指标，可能有不同列（极值or平均值），只需要取一次指标即可
                    continue;
                }
                else
                {
                    paramsPast.Add(paramKey);
                }
                double dValue;
                object objValue = testPoint[col.DisplayParam.ParamInfo.Name, col.ParamArrayIndex];
                if (objValue != null && double.TryParse(objValue.ToString(), out dValue))
                {
                    saveTestPointParamValue(col, dValue);
                }
            }
        }

        private readonly Dictionary<string, int> paramCountDic = new Dictionary<string, int>();
        private readonly Dictionary<string, double> paramMinValueDic = new Dictionary<string, double>();
        private readonly Dictionary<string, double> paramMaxValueDic = new Dictionary<string, double>();
        private readonly Dictionary<string, double> paramTotalValueDic = new Dictionary<string, double>();
        private void saveTestPointParamValue(TestPointDisplayColumn col, double value)
        {
            if (col.DisplayParam.ValueMin > value || col.DisplayParam.ValueMax < value)
            {//不在有效值域內，过滤掉
                return;
            }
            string nameKey = col.ParamKey;
            //采样点个数，指标总和值处理，为求平均做准备
            if (paramCountDic.ContainsKey(nameKey))
            {
                paramCountDic[nameKey]++;
                paramTotalValueDic[nameKey] += value;
            }
            else
            {
                paramCountDic.Add(nameKey, 1);
                paramTotalValueDic.Add(nameKey, value);
            }

            double min;
            if (paramMinValueDic.TryGetValue(nameKey, out min))
            {
                paramMinValueDic[nameKey] = Math.Min(min, value);
            }
            else
            {
                paramMinValueDic.Add(nameKey, value);
            }

            double max;
            if (paramMaxValueDic.TryGetValue(nameKey, out max))
            {
                paramMaxValueDic[nameKey] = Math.Max(max, value);
            }
            else
            {
                paramMaxValueDic.Add(nameKey, value);
            }
        }

        public double Distance { get; set; }

        public double AvgDistance2Points
        {
            get;
            set;
        }

        private string roadNames;
        public string RoadNames
        {
            get { return roadNames; }
        }

        internal void MakeSummary()
        {
            List<double> lngs = new List<double>();
            List<double> lats = new List<double>();
            lngs.Add(testPoints[0].Longitude);
            lats.Add(testPoints[0].Latitude);
            lngs.Add(CenterLongitude);
            lats.Add(CenterLatitude);
            lngs.Add(this.LastTestPoint.Longitude);
            lats.Add(this.LastTestPoint.Latitude);
            roadNames = GISManager.GetInstance().GetRoadPlaceDesc(lngs, lats);
            double dis = 0;
            if (cell != null && cell.Longitude != 0 && cell.Latitude != 0)
            {
                foreach (TestPoint tp in testPoints)
                {
                    dis += tp.Distance2(cell.Longitude, cell.Latitude);
                }
            }
            AvgDistance2Points = Math.Round(dis / testPoints.Count, 2);
        }

    }

}
