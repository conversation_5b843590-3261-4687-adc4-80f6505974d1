﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.Util.UiEx;
using MasterCom.RAMS.ZTFunc.ZTLTEClusterAnaEX;
using MasterCom.Util;
using MasterCom.RAMS.Net;
using System.Xml;

namespace MasterCom.RAMS.ZTFunc
{
    class ZTLTEClusterAnaQueryEx : DIYReplayFileQuery
    {

        protected Analyzer analyzer = null;
        public ZTLTEClusterAnaQueryEx(MainModel mModel)
            : base(mModel)
        {
            isAutoLoadCQTPicture = false;
            analyzer = new Analyzer();
        }
        protected override void query()
        {
            MainModel.FireDTDataChanged(MainModel.MainForm);
            base.query();
        }

        public override string Name
        {
            get { return "簇评估"; }
        }


        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22064, this.Name);
        }

        protected override bool isValidCondition()
        {
            return true;
        }
        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.File;
        }

        
        // 回放完一个文件后调用
        protected override void queryReplayInfo(ClientProxy clientProxy, Package package, FileInfo fileInfo)
        {
            base.queryReplayInfo(clientProxy, package, fileInfo);
            this.AnalyzeFile();
            MainModel.DTDataManager.Clear();
        }

        private void AnalyzeFile()
        {
            if (MainModel.DTDataManager.FileDataManagers.Count == 0)
            {
                return;
            }
            analyzer.Analyze(MainModel.DTDataManager.FileDataManagers[0]);
        }


        //protected override void fireShowForm()
        //{
        //    OpenFileDialog openFileDialog = new OpenFileDialog();
        //    openFileDialog.Filter = "Excel file (*.xlsx)|*.xlsx";
        //    openFileDialog.RestoreDirectory = true;
        //    if (openFileDialog.ShowDialog() == DialogResult.OK)
        //    {
        //        analyzer.Export(openFileDialog.FileName);
        //    }
        //}

        protected override void fireShowResult()
        {
            OpenFileDialog openFileDialog = new OpenFileDialog();
            openFileDialog.Filter = "Excel file (*.xlsx)|*.xlsx";
            openFileDialog.RestoreDirectory = true;
            if (openFileDialog.ShowDialog() == DialogResult.OK)
            {
                analyzer.Export(openFileDialog.FileName);
            }
        }

      
       
    }
}
