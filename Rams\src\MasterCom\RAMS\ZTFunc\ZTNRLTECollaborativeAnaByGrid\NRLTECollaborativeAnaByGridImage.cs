﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTNRLTECollaborativeAnaByGrid
{
    public abstract class NRLTECollaborativeAnaByGridImage
    {
        public abstract string RsrpAvg { get; }
        public abstract string RsrpCount { get; }
        public abstract string SinrAvg { get; }
        public abstract string SinrCount { get; }
    }

    public class NRGridImage : NRLTECollaborativeAnaByGridImage
    {
        public override string RsrpAvg { get { return "Nr_BA040002"; } }
        public override string RsrpCount { get { return "Nr_BA040001"; } }
        public override string SinrAvg { get { return "Nr_BA040009"; } }
        public override string SinrCount { get { return "Nr_BA040008"; } }
    }

    public class NRLTEGridImage : NRLTECollaborativeAnaByGridImage
    {
        public override string RsrpAvg { get { return "Nr_BA040017"; } }
        public override string RsrpCount { get { return "Nr_BA040016"; } }
        public override string SinrAvg { get { return "Nr_BA040019"; } }
        public override string SinrCount { get { return "Nr_BA040018"; } }
    }

    public class LTEGridImage : NRLTECollaborativeAnaByGridImage
    {
        public override string RsrpAvg { get { return "Lte_61210309"; } }
        public override string RsrpCount { get { return "Lte_61210301"; } }
        public override string SinrAvg { get { return "Lte_61210403"; } }
        public override string SinrCount { get { return "Lte_61210401"; } }
    }
}
