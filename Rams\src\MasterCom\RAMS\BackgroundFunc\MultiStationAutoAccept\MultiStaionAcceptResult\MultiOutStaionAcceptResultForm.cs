﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.Util;
using DevExpress.XtraGrid.Columns;
using DevExpress.XtraEditors;
using System.Windows.Forms;

namespace MasterCom.RAMS.BackgroundFunc
{
    public partial class MultiOutStaionAcceptResultForm : MinCloseForm
    {
        List<OutDoorBtsAcceptInfo> outDoorBtsAcceptInfoList;
        public MultiOutStaionAcceptResultForm()
            : base()
        {
            InitializeComponent();
            miExportSimpleExcel.Click += MiExportSimpleExcel_Click;
        }

        public void FillData(List<OutDoorBtsAcceptInfo> outDoorBtsAcceptInfos)
        {
            outDoorBtsAcceptInfoList = outDoorBtsAcceptInfos;
            gridControl1.DataSource = outDoorBtsAcceptInfos;
            gridControl1.RefreshDataSource();
        }

        public void MiExportSimpleExcel_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(GetNPOIRows(outDoorBtsAcceptInfoList));
        }

        private void tsMenuItemExpand_Click(object sender, EventArgs e)
        {
            for (int row = 0; row < gvResult.RowCount; row++)
            {
                gvResult.SetMasterRowExpanded(row, true);
            }
        }

        private void tsMenuItemCollapse_Click(object sender, EventArgs e)
        {
            gvResult.CollapseAllDetails();
        }

        public static List<NPOIRow> GetNPOIRows(List<OutDoorBtsAcceptInfo> btsAcceptInfoList)
        {
            List<NPOIRow> rows = new List<NPOIRow>();
            NPOIRow row = new NPOIRow();
            row.AddCellValue("序号");
            row.AddCellValue("日期");
            row.AddCellValue("基站名称");
            row.AddCellValue("基站ID");
            row.AddCellValue("经度");
            row.AddCellValue("纬度");
            row.AddCellValue("基站是否通过验收");
            row.AddCellValue("系统内切换");

            row.AddCellValue("小区Id");
            row.AddCellValue("小区是否通过验收");
            row.AddCellValue("FTP下载");
            row.AddCellValue("FTP上传");
            row.AddCellValue("RRC Setup Success Rate");
            row.AddCellValue("ERAB Setup Success Rate");
            row.AddCellValue("Access Success Rate");
            row.AddCellValue("3\\4G互操作（重选）");
            row.AddCellValue("2\\4G互操作（重选）");
            row.AddCellValue("CSFB呼叫成功率");
            rows.Add(row);

            foreach (OutDoorBtsAcceptInfo btsInfo in btsAcceptInfoList)
            {
                row = new NPOIRow();
                rows.Add(row);
                row.AddCellValue(btsInfo.SN);
                row.AddCellValue(btsInfo.Time);
                row.AddCellValue(btsInfo.BtsName);
                row.AddCellValue(btsInfo.BtsId);
                row.AddCellValue(btsInfo.Longitude);
                row.AddCellValue(btsInfo.Latitude);
                row.AddCellValue(btsInfo.IsAccordAcceptStr);
                row.AddCellValue(btsInfo.HandOverInfo);
                foreach (OutDoorCellAcceptInfo cellInfo in btsInfo.CellsAcceptDic.Values)
                {
                    NPOIRow subRow = new NPOIRow();
                    row.AddSubRow(subRow);
                    subRow.AddCellValue(cellInfo.CellId);
                    subRow.AddCellValue(cellInfo.IsAccordDes);
                    subRow.AddCellValue(cellInfo.FtpDlInfo);
                    subRow.AddCellValue(cellInfo.FtpUlInfo);
                    subRow.AddCellValue(cellInfo.RrcInfo);
                    subRow.AddCellValue(cellInfo.ErabInfo);
                    subRow.AddCellValue(cellInfo.AccessInfo);
                    subRow.AddCellValue(cellInfo.Reselect34Info);
                    subRow.AddCellValue(cellInfo.Reselect24Info);
                    subRow.AddCellValue(cellInfo.CsfbInfo);
                }
            }
            return rows;
        }
    }
}
