﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.BackgroundFunc
{
    public class DiyQueryFileInfoByTBAndFileName : DIYSQLBase
    {
        string tableName;
        string fileName;
        int iStime;
        int iEtime;
        List<TaskOrderRealFile> orderFile;
        readonly string splitStr = ",";

        public void SetQueryCondition(List<TaskOrderRealFile> orderFile, int iStime, int iEtime)
        {
            this.orderFile = orderFile;
            this.iStime = iStime;
            this.iEtime = iEtime;
            groupOrderFileByTableName();
        }

        Dictionary<string, List<string>> tableFileDic;
        //按log_file表汇聚对应文件
        private void groupOrderFileByTableName()
        {
            tableFileDic = new Dictionary<string, List<string>>();
            foreach (var file in orderFile)
            {
                List<string> fileList;
                if (!tableFileDic.TryGetValue(file.TableName, out fileList))
                {
                    fileList = new List<string>();
                    tableFileDic.Add(file.TableName, fileList);
                }
                fileList.Add(file.AnalyzeFileName);
            }
        }

        public DiyQueryFileInfoByTBAndFileName()
            : base()
        {
            
        }

        public override string Name
        {
            get
            {
                return "查询文件信息表";
            }
        }

        protected override string getSqlTextString()
        {
            StringBuilder selectSQL = new StringBuilder();
            selectSQL.AppendFormat("select [ifileid],[strfilename],[istime],[ietime],[idistance],[iservicetype],[icarriertype],[statstatus] from {0} where strfilename in ({1}) and istime >= {2} and ietime <= {3}", tableName, fileName, iStime, iEtime);
            return selectSQL.ToString();
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            int idx = 0;
            E_VType[] rType = new E_VType[8];
            rType[idx++] = E_VType.E_Int;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_Int;
            rType[idx++] = E_VType.E_Int;
            rType[idx++] = E_VType.E_Int;
            rType[idx++] = E_VType.E_Int;
            rType[idx++] = E_VType.E_Int;
            rType[idx] = E_VType.E_Int;
            return rType;
        }

        protected override void queryInThread(object o)
        {
            try
            {
                ClientProxy clientProxy = (ClientProxy)o;
                Package package = clientProxy.Package;
                foreach (var table in tableFileDic)
                {
                    int curIdx = 0;
                    while (curIdx < table.Value.Count)//分批次发送包
                    {
                        StringBuilder strb = getCurFiles(table.Value, ref curIdx);
                        fileName = strb.ToString().Substring(splitStr.Length);
                        tableName = table.Key;
                        getDataBySql(clientProxy, package);
                    }
                }
            }
            catch (Exception ex)
            {
                log.Error(ex.ToString());
            }
        }

        private StringBuilder getCurFiles(List<string> fileList, ref int curIdx)
        {
            StringBuilder strb = new StringBuilder();
            for (; curIdx < fileList.Count; curIdx++)
            {
                string curStr = fileList[curIdx];
                int bufferLength = Encoding.Default.GetBytes(strb + curStr).Length;
                if (bufferLength > 7000)
                {
                    break;
                }
                strb.Append(splitStr  + "'" + curStr + "'");
            }

            return strb;
        }

        private void getDataBySql(ClientProxy clientProxy, Package package)
        {
            string strsql = getSqlTextString();
            E_VType[] retArrDef = getSqlRetTypeArr();//获得枚举类型数组
            package.Command = Command.DIYSearch;//枚举类型：DIY接口
            package.SubCommand = SubCommand.Request;//枚举类型：请求
            if (MainDB)
            {
                package.Content.Type = RequestType.REQTYPE_DIY_MODEL_SQL_FORMAINDB;
            }
            else
            {
                package.Content.Type = RequestType.REQTYPE_DIY_MODEL_SQL;
            }
            package.Content.PrepareAddParam();
            package.Content.AddParam(strsql);
            StringBuilder sb = new StringBuilder();
            if (retArrDef != null)
            {
                for (int i = 0; i < retArrDef.Length; i++)
                {
                    sb.Append((int)retArrDef[i]);
                    sb.Append(",");
                }
            }
            package.Content.AddParam(sb.ToString().TrimEnd(','));
            clientProxy.Send();
            System.Threading.Thread.Sleep(300);
            receiveRetData(clientProxy);
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    dealReceiveData(package, clientProxy.DbID);
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }

        protected virtual void dealReceiveData(Package package, int districtID)
        {
            int fileID = package.Content.GetParamInt();
            string strFileName = package.Content.GetParamString();
            int istime = package.Content.GetParamInt();
            int ietime = package.Content.GetParamInt();
            int distance = package.Content.GetParamInt();
            int service = package.Content.GetParamInt();
            int carrier = package.Content.GetParamInt();
            int status = package.Content.GetParamInt();

            FileInfo fileInfo = new FileInfo();
            //DistrictID极其重要,如果不写会导致查询发送给服务端时鉴权失败,而且没有日志,直接得到255的返回错误
            fileInfo.DistrictID = districtID;
            fileInfo.ID = fileID;
            fileInfo.Name = strFileName;
            fileInfo.BeginTime = istime;
            fileInfo.EndTime = ietime;
            fileInfo.Distance = distance;
            fileInfo.LogTable = tableName;
            fileInfo.ServiceType = service;
            fileInfo.CarrierType = carrier;
            fileInfo.StatStatus = status;
            fileInfo.StrDesc = "";
            MainModel.FileInfos.Add(fileInfo);
        }
    }
}
