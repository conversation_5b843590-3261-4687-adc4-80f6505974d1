﻿using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.ZTFunc;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.Model
{
    public static class NREventHelper
    {
        public static NRHandoverEventHelper HandoverHelper { get; } = new NRHandoverEventHelper();

        public static NRVoiceEventHelper VoiceHelper { get; } = new NRVoiceEventHelper();

        public static NRServiceEventHelper ServiceHelper { get; } = new NRServiceEventHelper();

        private static bool isSetLongCI = false;

        public static void SetLongCI(List<ColumnDefItem> colList)
        {
            if (!isSetLongCI)
            {
                List<ColumnDefItem> cdfList = colList.FindAll(x => x.showName == "CI" || x.showName == "TargetCI");
                if (cdfList != null)
                {
                    foreach (var cdf in cdfList)
                    {
                        cdf.vType = E_VType.E_Int64;
                    }
                }
                isSetLongCI = true;
            }
        }

        public static void ReSetIntCI(List<ColumnDefItem> colList)
        {
            if (isSetLongCI)
            {
                List<ColumnDefItem> cdfList = colList.FindAll(x => x.showName == "CI" || x.showName == "TargetCI");
                if (cdfList != null)
                {
                    foreach (var cdf in cdfList)
                    {
                        cdf.vType = E_VType.E_Int;
                    }
                }
                isSetLongCI = false;
            }
        }
    }

    #region NR切换事件Helper
    public enum NRHandOverType
    {
        SA,
        NSA,
        NSANR,
        NSALTE,
        NSANRLTE,
        LTE,
        UNKNOWN
    }

    public class NRHandoverEventHelper
    {
        #region init
        public NSANRHandOverEvent NSANR { get; set; } = new NSANRHandOverEvent();
        public NSALTEHandOverEvent NSALTE { get; set; } = new NSALTEHandOverEvent();
        public SAHandOverEvent SA { get; set; } = new SAHandOverEvent();
        public LTEHandOverEvent LTE { get; set; } = new LTEHandOverEvent();

        private readonly List<int> lteHandoverEvtList = initLteHandoverEvt();
        private readonly List<int> nsaHandoverEvtList = initNSAHandoverEvt();
        private readonly List<int> nsaLTEHandoverEvtList = initNSALTEHandoverEvt();
        private readonly List<int> nsaNRHandoverEvtList = initNSANRHandoverEvt();
        private readonly List<int> nsaNRLTEHandoverEvtList = initNSANRLTEHandoverEvt();
        private readonly List<int> saHandoverEvtList = initSAHandoverEvt();

        private static List<int> initLteHandoverEvt()
        {
            List<int> list = new List<int>();
            list.Add((int)NREventManager.LTEInterHandoverRequest);
            list.Add((int)NREventManager.LTEInterHandoverSuccess);
            list.Add((int)NREventManager.LTEInterHandoverFailure);
            list.Add((int)NREventManager.LTEIntraHandoverRequest);
            list.Add((int)NREventManager.LTEIntraHandoverSuccess);
            list.Add((int)NREventManager.LTEIntraHandoverFailure);
            return list;
        }

        private static List<int> initNSAHandoverEvt()
        {
            List<int> list = new List<int>();
            list.AddRange(initNSALTEHandoverEvt());
            list.AddRange(initNSANRHandoverEvt());
            list.AddRange(initNSANRLTEHandoverEvt());
            return list;
        }

        private static List<int> initNSALTEHandoverEvt()
        {
            List<int> list = new List<int>();
            list.Add((int)NREventManager.DiffLTESameNRHandoverRequest);
            list.Add((int)NREventManager.DiffLTESameNRHandoverSuccess);
            list.Add((int)NREventManager.DiffLTESameNRHandoverFailure);
            return list;
        }

        private static List<int> initNSANRHandoverEvt()
        {
            List<int> list = new List<int>();
            list.Add((int)NREventManager.SameLTEDiffNRHandoverRequest);
            list.Add((int)NREventManager.SameLTEDiffNRHandoverSuccess);
            list.Add((int)NREventManager.SameLTEDiffNRHandoverFailure);
            return list;
        }

        private static List<int> initNSANRLTEHandoverEvt()
        {
            List<int> list = new List<int>();
            list.Add((int)NREventManager.DiffLTEDiffNRHandoverRequest);
            list.Add((int)NREventManager.DiffLTEDiffNRHandoverSuccess);
            list.Add((int)NREventManager.DiffLTEDiffNRHandoverFailure);
            return list;
        }

        private static List<int> initSAHandoverEvt()
        {
            List<int> list = new List<int>();
            list.Add((int)NREventManager.NRInterHandoverRequest);
            list.Add((int)NREventManager.NRInterHandoverSuccess);
            list.Add((int)NREventManager.NRInterHandoverFailure);
            list.Add((int)NREventManager.NRIntraHandoverRequest);
            list.Add((int)NREventManager.NRIntraHandoverSuccess);
            list.Add((int)NREventManager.NRIntraHandoverFailure);
            return list;
        }
        #endregion

        /// <summary>
        /// 将和Diff LTE Same NR Handover事件相同时间点的 LTE Handover事件剔除
        /// </summary>
        /// <param name="evtList"></param>
        public void FilterHandoverEvents(List<Event> evtList)
        {
            List<int> diffLTESameNRHandoverEvtIDList = new List<int>()
            {
                (int)NREventManager.DiffLTESameNRHandoverRequest,
                (int)NREventManager.DiffLTESameNRHandoverSuccess,
                (int)NREventManager.DiffLTESameNRHandoverFailure,
            };
            List<Event> nsaLteEvtList = new List<Event>();

            List<int> lteHandoverEvtIDList = new List<int>()
            {
                (int)NREventManager.LTEInterHandoverRequest,
                (int)NREventManager.LTEInterHandoverSuccess,
                (int)NREventManager.LTEInterHandoverFailure,
                (int)NREventManager.LTEIntraHandoverRequest,
                (int)NREventManager.LTEIntraHandoverSuccess,
                (int)NREventManager.LTEIntraHandoverFailure,
            };
            List<Event> lteEvtList = new List<Event>();

            foreach (var evt in evtList)
            {
                if (diffLTESameNRHandoverEvtIDList.Contains(evt.ID))
                {
                    nsaLteEvtList.Add(evt);
                }
                else if (lteHandoverEvtIDList.Contains(evt.ID))
                {
                    lteEvtList.Add(evt);
                }
            }

            foreach (var nrEvt in nsaLteEvtList)
            {
                foreach (var lteEvt in lteEvtList)
                {
                    if (nrEvt.DateTime == lteEvt.DateTime)
                    {
                        evtList.Remove(lteEvt);
                    }
                }
            }
        }

        #region 判断切换事件
        public bool JudgeHandoverSuccess(int evtID, bool isAnaLte)
        {
            if (evtID == (int)NREventManager.DiffLTESameNRHandoverSuccess
                || evtID == (int)NREventManager.SameLTEDiffNRHandoverSuccess
                || evtID == (int)NREventManager.DiffLTEDiffNRHandoverSuccess)
            {
                return true;
            }
            else if (isAnaLte && (evtID == (int)NREventManager.LTEInterHandoverSuccess
                || evtID == (int)NREventManager.LTEIntraHandoverSuccess))
            {
                return true;
            }
            else if (evtID == (int)NREventManager.NRInterHandoverSuccess
                || evtID == (int)NREventManager.NRIntraHandoverSuccess)
            {
                return true;
            }
            return false;
        }

        public bool JudgeIsNRHandoverEvt(int evtID)
        {
            if (lteHandoverEvtList.Contains(evtID)
                || nsaNRLTEHandoverEvtList.Contains(evtID))
            {
                return false;
            }
            return true;
        }

        public bool JudgeIncludeLTEHandover(NRHandOverType type)
        {
            if (type == NRHandOverType.LTE
                || type == NRHandOverType.NSALTE
                || type == NRHandOverType.NSANRLTE)
            {
                return true;
            }
            return false;
        }

        public bool JudgeIncludeNRHandover(NRHandOverType type)
        {
            if (type == NRHandOverType.SA
                || type == NRHandOverType.NSANR
                || type == NRHandOverType.NSANRLTE)
            {
                return true;
            }
            return false;
        }
        #endregion

        #region 获取切换信息
        #region 初始化不同类型的切换事件
        public List<int> GetHandoverRequestEvt(bool isAnaLte)
        {
            List<int> list = new List<int>();
            if (isAnaLte)
            {
                list.Add((int)NREventManager.LTEInterHandoverRequest);
                list.Add((int)NREventManager.LTEIntraHandoverRequest);
            }

            list.Add((int)NREventManager.DiffLTESameNRHandoverRequest);
            list.Add((int)NREventManager.SameLTEDiffNRHandoverRequest);
            list.Add((int)NREventManager.DiffLTEDiffNRHandoverRequest);

            list.Add((int)NREventManager.NRInterHandoverRequest);
            list.Add((int)NREventManager.NRIntraHandoverRequest);
            return list;
        }

        public List<int> GetHandoverSuccessEvt(bool isAnaLte)
        {
            List<int> list = new List<int>();
            if (isAnaLte)
            {
                list.Add((int)NREventManager.LTEInterHandoverSuccess);
                list.Add((int)NREventManager.LTEIntraHandoverSuccess);
            }

            list.Add((int)NREventManager.DiffLTESameNRHandoverSuccess);
            list.Add((int)NREventManager.SameLTEDiffNRHandoverSuccess);
            list.Add((int)NREventManager.DiffLTEDiffNRHandoverSuccess);

            list.Add((int)NREventManager.NRInterHandoverSuccess);
            list.Add((int)NREventManager.NRIntraHandoverSuccess);

            return list;
        }

        public List<int> GetHandoverFailEvt(bool isAnaLte)
        {
            List<int> list = new List<int>();
            if (isAnaLte)
            {
                list.Add((int)NREventManager.LTEInterHandoverFailure);
                list.Add((int)NREventManager.LTEIntraHandoverFailure);
            }

            list.Add((int)NREventManager.DiffLTESameNRHandoverFailure);
            list.Add((int)NREventManager.SameLTEDiffNRHandoverFailure);
            list.Add((int)NREventManager.DiffLTEDiffNRHandoverFailure);

            list.Add((int)NREventManager.NRInterHandoverFailure);
            list.Add((int)NREventManager.NRIntraHandoverFailure);

            return list;
        }

        public List<int> GetIncludeNRHandover()
        {
            List<int> list = new List<int>();

            list.Add((int)NREventManager.SameLTEDiffNRHandoverSuccess);
            list.Add((int)NREventManager.DiffLTEDiffNRHandoverSuccess);
            list.Add((int)NREventManager.NRInterHandoverSuccess);
            list.Add((int)NREventManager.NRIntraHandoverSuccess);

            return list;
        }

        public List<int> GetIncludeLTEHandover()
        {
            List<int> list = new List<int>();

            list.Add((int)NREventManager.LTEInterHandoverSuccess);
            list.Add((int)NREventManager.LTEIntraHandoverSuccess);
            list.Add((int)NREventManager.DiffLTESameNRHandoverSuccess);
            list.Add((int)NREventManager.DiffLTEDiffNRHandoverSuccess);

            return list;
        }
        #endregion

        #region 获取事件切换前后小区信息
        public HandOverCellInfo GetHandOverCellInfo(Event evt)
        {
            HandOverCellInfo info = new HandOverCellInfo();
            NRHandOverType type = GetHandoverType(evt.ID, false);
            if (type == NRHandOverType.NSA)
            {
                info.NRSrcCell = NSANR.GetSrcCellInfo(evt);
                info.NRTarCell = NSANR.GetTarCellInfo(evt);

                info.LTESrcCell = NSALTE.GetSrcCellInfo(evt);
                info.LTETarCell = NSALTE.GetTarCellInfo(evt);
            }
            else if (type == NRHandOverType.SA)
            {
                info.NRSrcCell = SA.GetSrcCellInfo(evt);
                info.NRTarCell = SA.GetTarCellInfo(evt);
            }
            else if (type == NRHandOverType.LTE)
            {
                info.LTESrcCell = LTE.GetSrcCellInfo(evt);
                info.LTETarCell = LTE.GetTarCellInfo(evt);
            }

            return info;
        }

        public class HandOverCellInfo
        {
            public HandOverEventBase.CellInfo NRSrcCell { get; set; } = new HandOverEventBase.CellInfo();
            public HandOverEventBase.CellInfo NRTarCell { get; set; } = new HandOverEventBase.CellInfo();

            public HandOverEventBase.CellInfo LTESrcCell { get; set; } = new HandOverEventBase.CellInfo();
            public HandOverEventBase.CellInfo LTETarCell { get; set; } = new HandOverEventBase.CellInfo();
        }
        #endregion

        public NRHandOverType GetHandoverType(int evtID, bool diffNSA)
        {
            if (lteHandoverEvtList.Contains(evtID))
            {
                return NRHandOverType.LTE;
            }
            else if (nsaHandoverEvtList.Contains(evtID))
            {
                if (diffNSA)
                {
                    if (nsaNRLTEHandoverEvtList.Contains(evtID))
                    {
                        return NRHandOverType.NSANRLTE;
                    }
                    else if (nsaNRHandoverEvtList.Contains(evtID))
                    {
                        return NRHandOverType.NSANR;
                    }
                    else if (nsaLTEHandoverEvtList.Contains(evtID))
                    {
                        return NRHandOverType.NSALTE;
                    }
                }
                return NRHandOverType.NSA;
            }
            else if (saHandoverEvtList.Contains(evtID))
            {
                return NRHandOverType.SA;
            }

            return NRHandOverType.UNKNOWN;
        }

        public string GetHandoverTypeDesc(NRHandOverType type)
        {
            switch (type)
            {
                case NRHandOverType.LTE:
                    return "LTE";
                case NRHandOverType.NSA:
                    return "NSA";
                case NRHandOverType.NSALTE:
                    return "NSA-LTE";
                case NRHandOverType.NSANR:
                    return "NSA-NR";
                case NRHandOverType.NSANRLTE:
                    return "NSA-NR,NSA-LTE";
                case NRHandOverType.SA:
                    return "SA";
                default:
                    return "未知";
            }
        }
        #endregion
    }

    public abstract class HandOverEventBase
    {
        #region 切换前
        public virtual int? GetSrcArfcn(Event evt)
        {
            throw new NotImplementedException();
        }
        public virtual int? GetSrcPci(Event evt)
        {
            throw new NotImplementedException();
        }
        public virtual int? GetSrcTac(Event evt)
        {
            throw new NotImplementedException();
        }
        public virtual long? GetSrcNci(Event evt)
        {
            throw new NotImplementedException();
        }
        #endregion

        #region 切换后
        public virtual int? GetTarArfcn(Event evt)
        {
            throw new NotImplementedException();
        }
        public virtual int? GetTarPci(Event evt)
        {
            throw new NotImplementedException();
        }
        public virtual int? GetTarTac(Event evt)
        {
            throw new NotImplementedException();
        }
        public virtual long? GetTarNci(Event evt)
        {
            throw new NotImplementedException();
        }
        #endregion

        public virtual CellInfo GetSrcCellInfo(Event evt)
        {
            CellInfo info = new CellInfo();
            info.ARFCN = getValidData(GetSrcArfcn(evt));
            info.PCI = getValidData(GetSrcPci(evt));
            info.Cell = getHandoverCell(evt, info);
            return info;
        }

        public virtual CellInfo GetTarCellInfo(Event evt)
        {
            CellInfo info = new CellInfo();
            info.ARFCN = getValidData(GetTarArfcn(evt));
            info.PCI = getValidData(GetTarPci(evt));
            info.Cell = getHandoverCell(evt, info);
            return info;
        }

        protected int getValidData(int? data)
        {
            if (data == null)
            {
                return -1;
            }
            return (int)data;
        }

        protected virtual ICell getHandoverCell(Event evt, CellInfo info)
        {
            throw new NotImplementedException();
        }

        public int? GetEvtIntValue(Event evt, string value)
        {
            object obj = evt[value];
            if (obj == null)
            {
                return null;
            }
            else
            {
                int res;
                if (int.TryParse(obj.ToString(), out res))
                {
                    return res;
                }
                else
                {
                    return null;
                }
            }
        }

        public class CellInfo
        {
            public ICell Cell { get; set; }

            public int TAC { get; set; } = -1;
            public long NCI { get; set; } = -1;
            public int ARFCN { get; set; } = -1;
            public int PCI { get; set; } = -1;
        }
    }

    public class NSANRHandOverEvent : HandOverEventBase
    {
        public override int? GetSrcArfcn(Event evt)
        {
            int? arfcn = GetEvtIntValue(evt, "Value3");
            return arfcn;
        }
        public override int? GetSrcPci(Event evt)
        {
            int? pci = GetEvtIntValue(evt, "Value4");
            return pci;
        }
        public override int? GetTarArfcn(Event evt)
        {
            int? arfcn = GetEvtIntValue(evt, "Value8");
            return arfcn;
        }
        public override int? GetTarPci(Event evt)
        {
            int? pci = GetEvtIntValue(evt, "Value9");
            return pci;
        }

        protected override ICell getHandoverCell(Event evt, CellInfo info)
        {
            NRCell cell;
            CellManager mng = CellManager.GetInstance();
            cell = mng.GetNearestNRCellByARFCNPCI(evt.DateTime, info.ARFCN, info.PCI, evt.Longitude, evt.Latitude);
            if (cell != null)
            {
                info.TAC = cell.TAC;
                info.NCI = cell.NCI;
            }
            return cell;
        }
    }

    public class NSALTEHandOverEvent : HandOverEventBase
    {
        public override int? GetSrcArfcn(Event evt)
        {
            int? arfcn = GetEvtIntValue(evt, "Value1");
            return arfcn;
        }
        public override int? GetSrcPci(Event evt)
        {
            int? pci = GetEvtIntValue(evt, "Value2");
            return pci;
        }
        public override int? GetTarArfcn(Event evt)
        {
            int? arfcn = GetEvtIntValue(evt, "Value5");
            return arfcn;
        }
        public override int? GetTarPci(Event evt)
        {
            int? pci = GetEvtIntValue(evt, "Value6");
            return pci;
        }

        protected override ICell getHandoverCell(Event evt, CellInfo info)
        {
            LTECell cell;
            CellManager mng = CellManager.GetInstance();
            cell = mng.GetNearestLTECellByEARFCNPCI(evt.DateTime, info.ARFCN, info.PCI, evt.Longitude, evt.Latitude);
            if (cell != null)
            {
                info.TAC = cell.TAC;
                info.NCI = cell.ECI;
            }
            return cell;
        }
    }

    public class SAHandOverEvent : HandOverEventBase
    {
        public override int? GetSrcArfcn(Event evt)
        {
            int? arfcn = GetEvtIntValue(evt, "Value2");
            return arfcn;
        }
        public override int? GetSrcPci(Event evt)
        {
            int? pci = GetEvtIntValue(evt, "Value3");
            return pci;
        }
        public override int? GetTarArfcn(Event evt)
        {
            int? arfcn = GetEvtIntValue(evt, "Value4");
            return arfcn;
        }
        public override int? GetTarPci(Event evt)
        {
            int? pci = GetEvtIntValue(evt, "Value5");
            return pci;
        }

        protected override ICell getHandoverCell(Event evt, CellInfo info)
        {
            NRCell cell;
            CellManager mng = CellManager.GetInstance();
            cell = mng.GetNearestNRCellByARFCNPCI(evt.DateTime, info.ARFCN, info.PCI, evt.Longitude, evt.Latitude);
            if (cell != null)
            {
                info.TAC = cell.TAC;
                info.NCI = cell.NCI;
            }
            return cell;
        }
    }

    public class LTEHandOverEvent : HandOverEventBase
    {
        public override int? GetSrcArfcn(Event evt)
        {
            int? arfcn = GetEvtIntValue(evt, "Value2");
            return arfcn;
        }
        public override int? GetSrcPci(Event evt)
        {
            int? pci = GetEvtIntValue(evt, "Value3");
            return pci;
        }
        public override int? GetTarArfcn(Event evt)
        {
            int? arfcn = GetEvtIntValue(evt, "Value4");
            return arfcn;
        }
        public override int? GetTarPci(Event evt)
        {
            int? pci = GetEvtIntValue(evt, "Value5");
            return pci;
        }

        protected override ICell getHandoverCell(Event evt, CellInfo info)
        {
            LTECell cell;
            CellManager mng = CellManager.GetInstance();
            cell = mng.GetNearestLTECellByEARFCNPCI(evt.DateTime, info.ARFCN, info.PCI, evt.Longitude, evt.Latitude);
            if (cell != null)
            {
                info.TAC = cell.TAC;
                info.NCI = cell.ECI;
            }
            return cell;
        }
    }
    #endregion

    #region NR语音事件Helper
    public class NRVoiceEventHelper
    {
        #region CallAttempt
        public List<int> MoCallAttemptEventIds { get; } = new List<int>
        {
            (int)NREventManager.VoLTE_Audio_MO_Call_Attempt,
            (int)NREventManager.VoLTE_Video_MO_Call_Attempt,

            (int)NREventManager.CSFB_MO_Call_Attempt,

            (int)NREventManager.EPSFB_Audio_MO_Call_Attempt,
            (int)NREventManager.EPSFB_Video_MO_Call_Attempt,

            (int)NREventManager.GSM_MO_Call_Attempt,

            (int)NREventManager.VoNR_Audio_MO_Call_Attempt,
            (int)NREventManager.VoNR_Video_MO_Call_Attempt
        };

        public List<int> MtCallAttemptEventIds { get; } = new List<int>
        {
            (int)NREventManager.VoLTE_Audio_MT_Call_Attempt,
            (int)NREventManager.VoLTE_Video_MT_Call_Attempt,

            (int)NREventManager.CSFB_MT_Call_Attempt,

            (int)NREventManager.EPSFB_Audio_MT_Call_Attempt,
            (int)NREventManager.EPSFB_Video_MT_Call_Attempt,

            (int)NREventManager.GSM_MT_Call_Attempt,

            (int)NREventManager.VoNR_Audio_MT_Call_Attempt,
            (int)NREventManager.VoNR_Video_MT_Call_Attempt
        };
        #endregion

        #region CallOver
        public List<int> MoCallOverEventIds { get; } = new List<int>
        {
            (int)NREventManager.VoLTE_Audio_MO_Call_End,
            (int)NREventManager.VoLTE_Audio_MO_Drop_Call,
            (int)NREventManager.VoLTE_Audio_MO_Block_Call,
            (int)NREventManager.VoLTE_Video_MO_Call_End,
            (int)NREventManager.VoLTE_Video_MO_Drop_Call,
            (int)NREventManager.VoLTE_Video_MO_Block_Call,
            (int)NREventManager.VoLTE_eSRVCC_Audio_MO_Call_End,
            (int)NREventManager.VoLTE_eSRVCC_Audio_MO_Drop_Call,
            (int)NREventManager.VoLTE_eSRVCC_Audio_MO_Block_Call,
            (int)NREventManager.VoLTE_eSRVCC_Video_MO_Call_End,
            (int)NREventManager.VoLTE_eSRVCC_Video_MO_Drop_Call,
            (int)NREventManager.VoLTE_eSRVCC_Video_MO_Block_Call,

            (int)NREventManager.CSFB_MO_Drop_Call,
            (int)NREventManager.CSFB_MO_Block_Call,
            (int)NREventManager.CSFB_MO_Call_End,

            (int)NREventManager.EPSFB_Audio_MO_Call_End,
            (int)NREventManager.EPSFB_Audio_MO_Drop_Call,
            (int)NREventManager.EPSFB_Audio_MO_Block_Call,
            (int)NREventManager.EPSFB_Video_MO_Call_End,
            (int)NREventManager.EPSFB_Video_MO_Drop_Call,
            (int)NREventManager.EPSFB_Video_MO_Block_Call,
            (int)NREventManager.EPSFB_eSRVCC_Audio_MO_Call_End,
            (int)NREventManager.EPSFB_eSRVCC_Audio_MO_Drop_Call,
            (int)NREventManager.EPSFB_eSRVCC_Audio_MO_Block_Call,
            (int)NREventManager.EPSFB_eSRVCC_Video_MO_Call_End,
            (int)NREventManager.EPSFB_eSRVCC_Video_MO_Drop_Call,
            (int)NREventManager.EPSFB_eSRVCC_Video_MO_Block_Call,

            (int)NREventManager.GSM_MO_Drop_Call,
            (int)NREventManager.GSM_MO_Block_Call,
            (int)NREventManager.GSM_MO_Call_End,

            (int)NREventManager.VoNR_Audio_MO_Call_End,
            (int)NREventManager.VoNR_Audio_MO_Drop_Call,
            (int)NREventManager.VoNR_Audio_MO_Block_Call,
            (int)NREventManager.VoNR_Video_MO_Call_End,
            (int)NREventManager.VoNR_Video_MO_Drop_Call,
            (int)NREventManager.VoNR_Video_MO_Block_Call
        };

        public List<int> MtCallOverEventIds { get; } = new List<int>
        {
            (int)NREventManager.VoLTE_Audio_MT_Call_End,
            (int)NREventManager.VoLTE_Audio_MT_Drop_Call,
            (int)NREventManager.VoLTE_Audio_MT_Block_Call,
            (int)NREventManager.VoLTE_Video_MT_Call_End,
            (int)NREventManager.VoLTE_Video_MT_Drop_Call,
            (int)NREventManager.VoLTE_Video_MT_Block_Call,
            (int)NREventManager.VoLTE_eSRVCC_Audio_MT_Call_End,
            (int)NREventManager.VoLTE_eSRVCC_Audio_MT_Drop_Call,
            (int)NREventManager.VoLTE_eSRVCC_Audio_MT_Block_Call,
            (int)NREventManager.VoLTE_eSRVCC_Video_MT_Call_End,
            (int)NREventManager.VoLTE_eSRVCC_Video_MT_Drop_Call,
            (int)NREventManager.VoLTE_eSRVCC_Video_MT_Block_Call,

            (int)NREventManager.CSFB_MT_Drop_Call,
            (int)NREventManager.CSFB_MT_Block_Call,
            (int)NREventManager.CSFB_MT_Call_End,

            (int)NREventManager.EPSFB_Audio_MT_Call_End,
            (int)NREventManager.EPSFB_Audio_MT_Drop_Call,
            (int)NREventManager.EPSFB_Audio_MT_Block_Call,
            (int)NREventManager.EPSFB_Video_MT_Call_End,
            (int)NREventManager.EPSFB_Video_MT_Drop_Call,
            (int)NREventManager.EPSFB_Video_MT_Block_Call,
            (int)NREventManager.EPSFB_eSRVCC_Audio_MT_Call_End,
            (int)NREventManager.EPSFB_eSRVCC_Audio_MT_Drop_Call,
            (int)NREventManager.EPSFB_eSRVCC_Audio_MT_Block_Call,
            (int)NREventManager.EPSFB_eSRVCC_Video_MT_Call_End,
            (int)NREventManager.EPSFB_eSRVCC_Video_MT_Drop_Call,
            (int)NREventManager.EPSFB_eSRVCC_Video_MT_Block_Call,

            (int)NREventManager.GSM_MT_Drop_Call,
            (int)NREventManager.GSM_MT_Block_Call,
            (int)NREventManager.GSM_MT_Call_End,

            (int)NREventManager.VoNR_Audio_MT_Call_End,
            (int)NREventManager.VoNR_Audio_MT_Drop_Call,
            (int)NREventManager.VoNR_Audio_MT_Block_Call,
            (int)NREventManager.VoNR_Video_MT_Call_End,
            (int)NREventManager.VoNR_Video_MT_Drop_Call,
            (int)NREventManager.VoNR_Video_MT_Block_Call
        };
        #endregion

        #region CallBlock
        public List<int> MoCallBlockEventIds { get; } = new List<int>
        {
            (int)NREventManager.VoLTE_Audio_MO_Block_Call,
            (int)NREventManager.VoLTE_Video_MO_Block_Call,
            (int)NREventManager.VoLTE_eSRVCC_Audio_MO_Block_Call,
            (int)NREventManager.VoLTE_eSRVCC_Video_MO_Block_Call,

            (int)NREventManager.CSFB_MO_Block_Call,

            (int)NREventManager.EPSFB_Audio_MO_Block_Call,
            (int)NREventManager.EPSFB_Video_MO_Block_Call,
            (int)NREventManager.EPSFB_eSRVCC_Audio_MO_Block_Call,
            (int)NREventManager.EPSFB_eSRVCC_Video_MO_Block_Call,

            (int)NREventManager.GSM_MO_Block_Call,

            (int)NREventManager.VoNR_Audio_MO_Block_Call,
            (int)NREventManager.VoNR_Video_MO_Block_Call
        };

        public List<int> MtCallBlockEventIds { get; } = new List<int>
        {
            (int)NREventManager.VoLTE_Audio_MT_Block_Call,
            (int)NREventManager.VoLTE_Video_MT_Block_Call,
            (int)NREventManager.VoLTE_eSRVCC_Audio_MT_Block_Call,
            (int)NREventManager.VoLTE_eSRVCC_Video_MT_Block_Call,

            (int)NREventManager.CSFB_MT_Block_Call,

            (int)NREventManager.EPSFB_Audio_MT_Block_Call,
            (int)NREventManager.EPSFB_Video_MT_Block_Call,
            (int)NREventManager.EPSFB_eSRVCC_Audio_MT_Block_Call,
            (int)NREventManager.EPSFB_eSRVCC_Video_MT_Block_Call,

            (int)NREventManager.GSM_MT_Block_Call,

            (int)NREventManager.VoNR_Audio_MT_Block_Call,
            (int)NREventManager.VoNR_Video_MT_Block_Call
        };
        #endregion

        #region CallDrop
        public List<int> MoCallDropEventIds { get; } = new List<int>
        {
            (int)NREventManager.CSFB_MO_Drop_Call,

            (int)NREventManager.VoLTE_Audio_MO_Drop_Call,
            (int)NREventManager.VoLTE_Video_MO_Drop_Call,
            (int)NREventManager.VoLTE_eSRVCC_Audio_MO_Drop_Call,
            (int)NREventManager.VoLTE_eSRVCC_Video_MO_Drop_Call,

            (int)NREventManager.EPSFB_Audio_MO_Drop_Call,
            (int)NREventManager.EPSFB_Video_MO_Drop_Call,
            (int)NREventManager.EPSFB_eSRVCC_Audio_MO_Drop_Call,
            (int)NREventManager.EPSFB_eSRVCC_Video_MO_Drop_Call,

            (int)NREventManager.GSM_MO_Drop_Call,

            (int)NREventManager.VoNR_Audio_MO_Drop_Call,
            (int)NREventManager.VoNR_Video_MO_Drop_Call
        };

        public List<int> MtCallDropEventIds { get; } = new List<int>
        {
            (int)NREventManager.CSFB_MT_Drop_Call,

            (int)NREventManager.VoLTE_Audio_MT_Drop_Call,
            (int)NREventManager.VoLTE_Video_MT_Drop_Call,
            (int)NREventManager.VoLTE_eSRVCC_Audio_MT_Drop_Call,
            (int)NREventManager.VoLTE_eSRVCC_Video_MT_Drop_Call,

            (int)NREventManager.EPSFB_Audio_MT_Drop_Call,
            (int)NREventManager.EPSFB_Video_MT_Drop_Call,
            (int)NREventManager.EPSFB_eSRVCC_Audio_MT_Drop_Call,
            (int)NREventManager.EPSFB_eSRVCC_Video_MT_Drop_Call,

            (int)NREventManager.GSM_MT_Drop_Call,

            (int)NREventManager.VoNR_Audio_MT_Drop_Call,
            (int)NREventManager.VoNR_Video_MT_Drop_Call
        };
        #endregion
    }
    #endregion


    #region NR业务事件Helper
    public class NRServiceEventHelper
    {

    }
    #endregion


    public enum LTEEventManager
    {
        #region 切换

        #endregion

    }

    public enum MoMtFile
    {
        Uncertain = -1,
        Unknow = 0,
        MoFlag = 1,
        MtFlag = 2
    }

    public enum NREventManager
    {
        #region 切换
        LTEInterHandoverRequest = 9076,
        LTEInterHandoverSuccess = 9077,
        LTEInterHandoverFailure = 9078,
        LTEIntraHandoverRequest = 9079,
        LTEIntraHandoverSuccess = 9080,
        LTEIntraHandoverFailure = 9081,

        DiffLTESameNRHandoverRequest = 9091,
        SameLTEDiffNRHandoverRequest = 9092,
        DiffLTEDiffNRHandoverRequest = 9093,
        DiffLTESameNRHandoverSuccess = 9094,
        SameLTEDiffNRHandoverSuccess = 9095,
        DiffLTEDiffNRHandoverSuccess = 9096,
        DiffLTESameNRHandoverFailure = 9097,
        SameLTEDiffNRHandoverFailure = 9098,
        DiffLTEDiffNRHandoverFailure = 9099,

        NRInterHandoverRequest = 9295,
        NRInterHandoverSuccess = 9296,
        NRInterHandoverFailure = 9297,
        NRIntraHandoverRequest = 9298,
        NRIntraHandoverSuccess = 9299,
        NRIntraHandoverFailure = 9300,

        IRAT_NR_LTE_HO_Request = 9540,
        IRAT_NR_LTE_HO_Success = 9541,
        IRAT_NR_LTE_HO_Failure = 9542,
        IRAT_L_NR_Redirect_Request = 9543,    // 重定向
        IRAT_L_NR_Redirect_Success = 9544,
        IRAT_L_NR_Redirect_Failure = 9545,
        IRAT_NR_L_Redirect_Request = 9546,
        IRAT_NR_L_Redirect_Success = 9547,
        IRAT_NR_L_Redirect_Failure = 9548,
        IRAT_LTE_NR_HO_Request = 9549,
        IRAT_LTE_NR_HO_Success = 9550,
        IRAT_LTE_NR_HO_Failure = 9551,
        #endregion

        #region 语音
        VoLTE_Audio_MO_Call_Attempt = 9149,
        VoLTE_Audio_MT_Call_Attempt = 9150,
        VoLTE_Audio_MO_Call_Established = 9153,
        VoLTE_Audio_MT_Call_Established = 9154,
        VoLTE_Audio_MO_Call_End = 9155,
        VoLTE_Audio_MT_Call_End = 9156,
        VoLTE_Audio_MO_Drop_Call = 9157,
        VoLTE_Audio_MT_Drop_Call = 9158,
        VoLTE_Audio_MO_Block_Call = 9159,
        VoLTE_Audio_MT_Block_Call = 9160,

        VoLTE_eSRVCC_Audio_MO_Call_Established = 9161,
        VoLTE_eSRVCC_Audio_MT_Call_Established = 9162,
        VoLTE_eSRVCC_Audio_MO_Call_End = 9163,
        VoLTE_eSRVCC_Audio_MT_Call_End = 9164,
        VoLTE_eSRVCC_Audio_MO_Drop_Call = 9165,
        VoLTE_eSRVCC_Audio_MT_Drop_Call = 9166,
        VoLTE_eSRVCC_Audio_MO_Block_Call = 9167,
        VoLTE_eSRVCC_Audio_MT_Block_Call = 9168,

        VoLTE_eSRVCC_HandOver_Success = 9172,

        VoLTE_Video_MO_Call_Attempt = 9187,
        VoLTE_Video_MT_Call_Attempt = 9188,
        VoLTE_Video_MO_Call_Established = 9191,
        VoLTE_Video_MT_Call_Established = 9192,
        VoLTE_Video_MO_Call_End = 9193,
        VoLTE_Video_MT_Call_End = 9194,
        VoLTE_Video_MO_Drop_Call = 9195,
        VoLTE_Video_MT_Drop_Call = 9196,
        VoLTE_Video_MO_Block_Call = 9197,
        VoLTE_Video_MT_Block_Call = 9198,

        VoLTE_eSRVCC_Video_MO_Call_Established = 9199,
        VoLTE_eSRVCC_Video_MT_Call_Established = 9200,
        VoLTE_eSRVCC_Video_MO_Call_End = 9201,
        VoLTE_eSRVCC_Video_MT_Call_End = 9202,
        VoLTE_eSRVCC_Video_MO_Drop_Call = 9203,
        VoLTE_eSRVCC_Video_MT_Drop_Call = 9204,
        VoLTE_eSRVCC_Video_MO_Block_Call = 9205,
        VoLTE_eSRVCC_Video_MT_Block_Call = 9206,

        CSFB_MO_Call_Attempt = 9250,
        CSFB_MT_Call_Attempt = 9251,
        CSFB_MO_Call_Established = 9252,
        CSFB_MT_Call_Established = 9253,
        CSFB_MO_Drop_Call = 9254,
        CSFB_MT_Drop_Call = 9255,
        CSFB_MO_Block_Call = 9256,
        CSFB_MT_Block_Call = 9257,
        CSFB_MO_Call_End = 9258,
        CSFB_MT_Call_End = 9259,

        EPSFB_Audio_MO_Call_Attempt = 9334,
        EPSFB_Audio_MT_Call_Attempt = 9335,
        EPSFB_Audio_MO_Call_Setup = 9336,
        EPSFB_Audio_MT_Call_Setup = 9337,
        EPSFB_Audio_MO_Call_Established = 9338,
        EPSFB_Audio_MT_Call_Established = 9339,
        EPSFB_Audio_MO_Call_End = 9340,
        EPSFB_Audio_MT_Call_End = 9341,
        EPSFB_Audio_MO_Drop_Call = 9342,
        EPSFB_Audio_MT_Drop_Call = 9343,
        EPSFB_Audio_MO_Block_Call = 9344,
        EPSFB_Audio_MT_Block_Call = 9345,

        EPSFB_Video_MO_Call_Attempt = 9346,
        EPSFB_Video_MT_Call_Attempt = 9347,
        EPSFB_Video_MO_Call_Setup = 9348,
        EPSFB_Video_MT_Call_Setup = 9349,
        EPSFB_Video_MO_Call_Established = 9350,
        EPSFB_Video_MT_Call_Established = 9351,
        EPSFB_Video_MO_Call_End = 9352,
        EPSFB_Video_MT_Call_End = 9353,
        EPSFB_Video_MO_Drop_Call = 9354,
        EPSFB_Video_MT_Drop_Call = 9355,
        EPSFB_Video_MO_Block_Call = 9356,
        EPSFB_Video_MT_Block_Call = 9357,

        EPSFB_eSRVCC_Audio_MO_Call_Established = 9358,
        EPSFB_eSRVCC_Audio_MT_Call_Established = 9359,
        EPSFB_eSRVCC_Audio_MO_Call_End = 9360,
        EPSFB_eSRVCC_Audio_MT_Call_End = 9361,
        EPSFB_eSRVCC_Audio_MO_Drop_Call = 9362,
        EPSFB_eSRVCC_Audio_MT_Drop_Call = 9363,
        EPSFB_eSRVCC_Audio_MO_Block_Call = 9364,
        EPSFB_eSRVCC_Audio_MT_Block_Call = 9365,

        EPSFB_eSRVCC_Video_MO_Call_Established = 9366,
        EPSFB_eSRVCC_Video_MT_Call_Established = 9367,
        EPSFB_eSRVCC_Video_MO_Call_End = 9368,
        EPSFB_eSRVCC_Video_MT_Call_End = 9369,
        EPSFB_eSRVCC_Video_MO_Drop_Call = 9370,
        EPSFB_eSRVCC_Video_MT_Drop_Call = 9371,
        EPSFB_eSRVCC_Video_MO_Block_Call = 9372,
        EPSFB_eSRVCC_Video_MT_Block_Call = 9373,

        GSM_MO_Call_Attempt = 9400,
        GSM_MT_Call_Attempt = 9401,
        GSM_MO_Call_Established = 9402,
        GSM_MT_Call_Established = 9403,
        GSM_MO_Drop_Call = 9404,
        GSM_MT_Drop_Call = 9405,
        GSM_MO_Block_Call = 9406,
        GSM_MT_Block_Call = 9407,
        GSM_MO_Call_End = 9408,
        GSM_MT_Call_End = 9409,

        GSM_MO_Call_Alerting = 9410,
        GSM_MT_Call_Alerting = 9411,
        GSM_MO_Call_CM_ReEstablishment = 9412,
        GSM_MT_Call_CM_ReEstablishment = 9413,
        GSM_MO_Drop_Call_ReEstablish = 9414,
        GSM_MT_Drop_Call_ReEstablish = 9415,

        EPSFB_FR_Fail = 9557,
        EPSFB_FR_MoreThan2point5 = 9558,
        EPSFB_FR_LessThan2point5 = 9559,
        #endregion

        #region 业务
        FTPDownloadBegan = 9057,
        FTPDownloadSuccess = 9058,
        FTPDownloadDrop = 9059,
        FTPDownloadFirstData = 9073,
        FTPDownloadUnFinished = 9064,

        HttpRequest = 9620,
        HttpSuccess = 9621,
        HttpDisFail = 9622,
        HttpComplete = 9623,
        HttpIncomplete = 9624,
        HttpFail = 9628,

        DownRequest = 9625,
        DownSuccess = 9626,
        DownDrop = 9627,
        DownloadFail = 9629,

        FlvPlayFinished = 9608,
        VideoRequest = 9640,
        VideoFirstData = 9641,
        VideoRebufferStart = 9642,
        VideoRebufferEnd = 9643,
        VideoLastData = 9644,
        VideoFinish = 9645,
        VideoDrop = 9646,
        VideoReproductionStart = 9647,
        VideoFail = 9648,
        #endregion

        Attach_Request = 9001,
        Attach_Accept = 9002,
        NR_Cell_Add_Request = 9022,
        NR_Cell_Add_Success = 9023,
        Ping_Success = 9282,
        Ping_Fail = 9283,
        MosUnder3dot0Last2Pnt = 9235,
        NR_TAU_Request = 9315,
        NR_TAU_Success = 9316,

        NR_Registration_Request = 9301,
        NR_Registration_Accept = 9302,
        NR_Registration_Reject = 9303,

        #region VONR

        VoNR_Audio_MO_Call_Attempt = 9834,
        VoNR_Audio_MT_Call_Attempt = 9835,
        VoNR_Audio_MO_Call_Setup = 9836,
        VoNR_Audio_MT_Call_Setup = 9837,
        VoNR_Audio_MO_Call_Established = 9838,
        VoNR_Audio_MT_Call_Established = 9839,
        VoNR_Audio_MO_Call_End = 9840,
        VoNR_Audio_MT_Call_End = 9841,
        VoNR_Audio_MO_Drop_Call = 9842,
        VoNR_Audio_MT_Drop_Call = 9843,
        VoNR_Audio_MO_Block_Call = 9844,
        VoNR_Audio_MT_Block_Call = 9845,

        VoNR_Video_MO_Call_Attempt = 9860,
        VoNR_Video_MT_Call_Attempt = 9861,
        VoNR_Video_MO_Call_Setup = 9862,
        VoNR_Video_MT_Call_Setup = 9863,
        VoNR_Video_MO_Call_Established = 9864,
        VoNR_Video_MT_Call_Established = 9865,
        VoNR_Video_MO_Call_End = 9866,
        VoNR_Video_MT_Call_End = 9867,
        VoNR_Video_MO_Drop_Call = 9868,
        VoNR_Video_MT_Drop_Call = 9869,
        VoNR_Video_MO_Block_Call = 9870,
        VoNR_Video_MT_Block_Call = 9871,

        #endregion
    }


    public enum MessageManager
    {
        #region SIP

        Msg_IMS_SIP_INVITE = 0x42060000,
        Msg_IMS_SIP_INVITE_Trying = 0x42064064,
        Msg_IMS_SIP_INVITE_Session_Progress = 0x420640B7,
        Msg_IMS_SIP_INVITE_Ringing = 0x420640B4,
        Msg_IMS_SIP_INVITE_OK = 0x420640C8,
        Msg_IMS_SIP_INVITE_Server_Internal_Error = 0x420641F4,
        Msg_IMS_SIP_INVITE_Forbidden = 0x42064193,

        Msg_IMS_SIP_PRACK = 0x420A0000,
        Msg_IMS_SIP_PRACK_OK = 0x420A40C8,

        Msg_IMS_SIP_UPDATE = 0x42100000,
        Msg_IMS_SIP_UPDATE_OK = 0x421040C8,

        Msg_IMS_SIP_ACK = 0x42010000,

        Msg_IMS_SIP_BYE = 0x42020000,
        Msg_IMS_SIP_BYE_OK = 0x420240C8,

        Msg_IMS_SIP_CANCEL = 0x42030000,
        Msg_IMS_SIP_CANCEL_OK = 0x420340C8,
        Msg_IMS_SIP_DO = 0x42040000,
        Msg_IMS_SIP_INFO = 0x42050000,
        Msg_IMS_SIP_MESSAGE = 0x42070000,
        Msg_IMS_SIP_NOTIFY = 0x42080000,
        Msg_IMS_SIP_OPTIONS = 0x42090000,
        Msg_IMS_SIP_QAUTH = 0x420B0000,
        Msg_IMS_SIP_REFER = 0x420C0000,
        Msg_IMS_SIP_REGISTER = 0x420D0000,
        Msg_IMS_SIP_REGISTER_OK = 0x420D40C8,
        Msg_IMS_SIP_REGISTER_Unauthorized = 0x420D4191,
        Msg_IMS_SIP_SPRACK = 0x420E0000,
        Msg_IMS_SIP_SUBSCRIBE = 0x420F0000,
        Msg_IMS_SIP_PUBLISH = 0x42110000,

        #endregion


        #region LTE_RRC

        //BCCH BCH Messages(0x64)
        LTE_RRC_Master_Information_Block = 0x412f6400,

        //UL DCCH Messages(0x6A)
        LTE_RRC_CSFB_Parameters_Request_CDMA2000 = 0x412f6a00,
        LTE_RRC_Measurement_Report = 0x412f6a01,
        LTE_RRC_RRC_Connection_Reconfiguration_Complete = 0x412f6a02,
        LTE_RRC_RRC_Connection_Reestablishment_Complete = 0x412f6a03,
        LTE_RRC_RRC_Connection_Setup_Complete = 0x412f6a04,
        LTE_RRC_Security_Mode_Complete = 0x412f6a05,
        LTE_RRC_Security_Mode_Failure = 0x412f6a06,
        LTE_RRC_UE_Capability_Information = 0x412f6a07,
        LTE_RRC_UL_Handover_Preparation_Transfer = 0x412f6a08,
        LTE_RRC_UL_Information_Transfer = 0x412f6a09,
        LTE_RRC_Counter_Check_Response = 0x412f6a0a,
        LTE_RRC_UEInformationResponse_r9 = 0x412f6a0b,
        LTE_RRC_ProximityIndication_r9 = 0x412f6a0c,
        LTE_RRC_RNReconfigurationComplete_r10 = 0x412f6a0d,
        LTE_RRC_MBMSCountingResponse_r10 = 0x412f6a0e,
        LTE_RRC_InterFreqRSTDMeasurementIndication_r10 = 0x412f6a0f,
        LTE_RRC_UEAssistanceInformation_r11 = 0x412f6a10,
        LTE_RRC_InDeviceCoexIndication_r11 = 0x412f6a11,
        LTE_RRC_MBMSInterestIndication_r11 = 0x412f6a12,
        LTE_RRC_SCGFailureInformation_r12 = 0x412f6a13,
        LTE_RRC_SidelinkUEInformation_r12 = 0x412f6a14,
        LTE_RRC_WLANConnectionStatusReport_r13 = 0x412f6a15,
        LTE_RRC_RRCConnectionResumeComplete_r13 = 0x412f6a16,
        LTE_RRC_ULInformationTransferMRDC_r15 = 0x412f6a17,
        LTE_RRC_SCGFailureInformationNR_r15 = 0x412f6a18,
        LTE_RRC_MeasReportAppLayer_r15 = 0x412f6a19,
        LTE_RRC_FailureInformation_r15 = 0x412f6a1a,
        LTE_RRC_UL_DCCH_Spare5 = 0x412f6a1b,
        LTE_RRC_UL_DCCH_Spare4 = 0x412f6a1c,
        LTE_RRC_UL_DCCH_Spare3 = 0x412f6a1d,
        LTE_RRC_UL_DCCH_Spare2 = 0x412f6a1e,
        LTE_RRC_UL_DCCH_Spare1 = 0x412f6a1f,

        //BCCH DL SCH Messages(0x65)
        LTE_RRC_System_Information_Blocks = 0x412f6500,
        LTE_RRC_System_Information_Block_Type_1 = 0x412f6501,
        LTE_RRC_System_Information_Block_Type_2 = 0x412f6502,
        LTE_RRC_System_Information_Block_Type_3 = 0x412f6503,
        LTE_RRC_System_Information_Block_Type_4 = 0x412f6504,
        LTE_RRC_System_Information_Block_Type_5 = 0x412f6505,
        LTE_RRC_System_Information_Block_Type_6 = 0x412f6506,
        LTE_RRC_System_Information_Block_Type_7 = 0x412f6507,
        LTE_RRC_System_Information_Block_Type_8 = 0x412f6508,
        LTE_RRC_System_Information_Block_Type_9 = 0x412f6509,
        LTE_RRC_System_Information_Block_Type_10 = 0x412f650a,
        LTE_RRC_System_Information_Block_Type_11 = 0x412f650b,
        LTE_RRC_System_Information_Block_Type_12 = 0x412f650c,

        //PCCH Messages(0x66)
        LTE_RRC_Paging = 0x412f6600,

        //DL CCCH Messages(0x67)
        LTE_RRC_RRC_Connection_Reestablishment = 0x412f6700,
        LTE_RRC_RRC_Connection_Reestablishment_Reject = 0x412f6701,
        LTE_RRC_RRC_Connection_Reject = 0x412f6702,
        LTE_RRC_RRC_Connection_Setup = 0x412f6703,
        LTE_RRC_RRC_Early_Data_Complete_r15 = 0x412f6710,
        LTE_RRC_DL_CCCH_Spare3 = 0x412f6711,
        LTE_RRC_DL_CCCH_Spare2 = 0x412f6712,
        LTE_RRC_DL_CCCH_Spare1 = 0x412f6713,

        //DL DCCH Messages(0x68)
        LTE_RRC_CSFB_Parameters_Response_CDMA2000 = 0x412f6800,
        LTE_RRC_DL_Information_Transfer = 0x412f6801,
        LTE_RRC_Handover_From_EUTRA_Preparation_Request = 0x412f6802,
        LTE_RRC_Mobility_From_EUTRA_Command = 0x412f6803,
        LTE_RRC_RRC_Connection_Reconfiguration = 0x412f6804,
        LTE_RRC_RRC_Connection_Release = 0x412f6805,
        LTE_RRC_Security_Mode_Command = 0x412f6806,
        LTE_RRC_UE_Capability_Enquiry = 0x412f6807,
        LTE_RRC_Counter_Check = 0x412f6808,
        LTE_RRC_UE_Information_Request_r9 = 0x412f6809,
        LTE_RRC_Logged_Measurement_Configuration_r10 = 0x412f6810,
        LTE_RRC_RN_Reconfiguration_r10 = 0x412f680a,
        LTE_RRC_RRC_Connection_Resume_r13 = 0x412f680b,
        LTE_RRC_DL_DCCH_Spare3 = 0x412f680c,
        LTE_RRC_DL_DCCH_Spare2 = 0x412f680d,
        LTE_RRC_DL_DCCH_Spare1 = 0x412f680e,

        //UL CCCH Messages(0x69)
        LTE_RRC_Connection_Reestablishment_Request = 0x412f6900,
        LTE_RRC_RRC_Connection_Request = 0x412f6901,
        LTE_RRC_RRC_Connection_Resume_Request_r13 = 0x412f6910,
        LTE_RRC_Early_Data_Request_r15 = 0x412f6920,
        LTE_RRC_UL_CCCH_Spare3 = 0x412f6921,
        LTE_RRC_UL_CCCH_Spare2 = 0x412f6922,
        LTE_RRC_UL_CCCH_Spare1 = 0x412f6923,

        #endregion


        #region LTE_NAS

        //EMMMessages(0x6B)
        LTE_NAS_Attach_request = 0x416b0741,
        LTE_NAS_Attach_accept = 0x416b0742,
        LTE_NAS_Attach_complete = 0x416b0743,
        LTE_NAS_Attach_reject = 0x416b0744,
        LTE_NAS_Detach_request = 0x416b0745,
        LTE_NAS_Detach_accept = 0x416b0746,
        LTE_NAS_Tracking_area_update_request = 0x416b0748,
        LTE_NAS_Tracking_area_update_accept = 0x416b0749,
        LTE_NAS_Tracking_area_update_complete = 0x416b074a,
        LTE_NAS_Tracking_area_update_reject = 0x416b074b,
        LTE_NAS_Extended_service_request = 0x416b074c,
        LTE_NAS_Control_plane_service_request = 0x416b074d,
        LTE_NAS_Service_reject = 0x416b074e,
        LTE_NAS_Service_Accept = 0x416b0707,
        LTE_NAS_Service_request = 0x416b0703,
        LTE_NAS_GUTI_reallocation_command = 0x416b0750,
        LTE_NAS_GUTI_reallocation_complete = 0x416b0751,
        LTE_NAS_Authentication_request = 0x416b0752,
        LTE_NAS_Authentication_response = 0x416b0753,
        LTE_NAS_Authentication_reject = 0x416b0754,
        LTE_NAS_Identity_request = 0x416b0755,
        LTE_NAS_Identity_response = 0x416b0756,
        LTE_NAS_Authentication_Fail = 0x416b075c,
        LTE_NAS_Security_mode_command = 0x416b075d,
        LTE_NAS_Security_mode_complete = 0x416b075e,
        LTE_NAS_Security_mode_reject = 0x416b075f,
        LTE_NAS_EMM_status = 0x416b0760,
        LTE_NAS_EMM_information = 0x416b0761,
        LTE_NAS_Downlink_NAS_transport = 0x416b0762,
        LTE_NAS_Uplink_NAS_transport = 0x416b0763,
        LTE_NAS_CS_Service_notification = 0x416b0764,
        LTE_NAS_Downlink_generic_NAS_transport = 0x416b0768,
        LTE_NAS_Uplink_generic_NAS_transport = 0x416b0769,

        //ESMMessages(0x6B)
        LTE_NAS_Activate_default_EPS_bearer_context_request = 0x416b02c1,
        LTE_NAS_Activate_default_EPS_bearer_context_accept = 0x416b02c2,
        LTE_NAS_Activate_default_EPS_bearer_context_reject = 0x416b02c3,
        LTE_NAS_Activate_dedicated_EPS_bearer_context_request = 0x416b02c5,
        LTE_NAS_Activate_dedicated_EPS_bearer_context_accept = 0x416b02c6,
        LTE_NAS_Activate_dedicated_EPS_bearer_context_reject = 0x416b02c7,
        LTE_NAS_Modify_EPS_bearer_context_request = 0x416b02c9,
        LTE_NAS_Modify_EPS_bearer_context_accept = 0x416b02ca,
        LTE_NAS_Modify_EPS_bearer_context_reject = 0x416b02cb,
        LTE_NAS_Deactivate_EPS_bearer_context_request = 0x416b02cd,
        LTE_NAS_Deactivate_EPS_bearer_context_accept = 0x416b02ce,
        LTE_NAS_PDN_connectivity_request = 0x416b02d0,
        LTE_NAS_PDN_connectivity_reject = 0x416b02d1,
        LTE_NAS_PDN_disconnect_request = 0x416b02d2,
        LTE_NAS_PDN_disconnect_reject = 0x416b02d3,
        LTE_NAS_Bearer_resource_allocation_request = 0x416b02d4,
        LTE_NAS_Bearer_resource_allocation_reject = 0x416b02d5,
        LTE_NAS_Bearer_resource_modification_request = 0x416b02d6,
        LTE_NAS_Bearer_resource_modification_reject = 0x416b02d7,
        LTE_NAS_ESM_information_request = 0x416b02d9,
        LTE_NAS_ESM_information_response = 0x416b02da,
        LTE_NAS_Notification = 0x416b02db,
        LTE_NAS_ESM_status = 0x416b02e8,

        #endregion


        #region LTE_NbIot

        //BCCH_BCH_NB(0x71)
        NBIOT_Master_Information_Block_NB = 0x412F7100,
        //BCCH_DL_SCH_NB(0x72)
        NBIOT_System_Information_Block_NB = 0x412F7200,
        NBIOT_System_Information_BlockType1_NB = 0x412F7201,
        //PCCH_NB(0x73)
        NBIOT_Paging_NB = 0x412F7300,
        //DL_CCCH_NB(0x74)
        NBIOT_RRC_Connection_Reestablishment_NB = 0x412F7400,
        NBIOT_RRC_Connection_Reestablishment_Reject = 0x412F7401,
        NBIOT_RRC_Connection_Reject_NB = 0x412F7402,
        NBIOT_RRC_Connection_Setup_NB = 0x412F7403,
        //DL_DCCH_NB(0x75)
        NBIOT_DL_Information_Transfer_NB = 0x412F7500,
        NBIOT_RRC_Connection_Reconfiguration_NB = 0x412F7501,
        NBIOT_RRC_Connection_Release_NB = 0x412F7502,
        NBIOT_Security_Mode_Command = 0x412F7503,
        NBIOT_UE_Capability_Enquiry_NB = 0x412F7504,
        NBIOT_RRC_Connection_Resume_NB = 0x412F7505,
        //UL_CCCH_NB(0x76)
        NBIOT_RRC_Connection_Reestablishment_Request_NB = 0x412F7600,
        NBIOT_RRC_Connection_Request_NB = 0x412F7601,
        NBIOT_RRC_Connection_Resume_Request_NB = 0x412F7602,
        //UL_DCCH_NB(0x77)
        NBIOT_RRC_Connection_Reconfiguration_Complete_NB = 0x412F7700,
        NBIOT_RRC_Connection_Reestablishment_Complete_NB = 0x412F7701,
        NBIOT_RRC_Connection_Setup_Complete_NB = 0x412F7702,
        NBIOT_Security_Mode_Complete = 0x412F7703,
        NBIOT_Security_Mode_Failure = 0x412F7704,
        NBIOT_UE_Capability_Information_NB = 0x412F7705,
        NBIOT_UL_Information_Transfer_NB = 0x412F7706,
        NBIOT_RRC_Connection_Resume_Complete_NB = 0x412F7707,

        #endregion


        #region NR_RRC

        //BCCH BCH Messages(0x80)  下行
        NR_RRC_MIB = 0x432F8000,

        //BCCH DL SCH Messages(0x81)  下行
        NR_RRC_SIBs = 0x432F8100,
        NR_RRC_SIBType1 = 0x432F8101,

        //DL CCCH Messages(0x82)  下行
        NR_RRC_RRCReject = 0x432F8200,
        NR_RRC_RRCSetup = 0x432F8201,
        NR_RRC_DL_CCCH_Spare2 = 0x432F8202,
        NR_RRC_DL_CCCH_Spare1 = 0x432F8203,

        //DL DCCH Messages(0x83)  下行
        NR_RRC_RRCReconfiguration = 0x432F8300,
        NR_RRC_RRCResume = 0x432F8301,
        NR_RRC_RRCRelease = 0x432F8302,
        NR_RRC_RRCReestablishment = 0x432F8303,
        NR_RRC_SecurityModeCommand = 0x432F8304,
        NR_RRC_DLInformationTransfer = 0x432F8305,
        NR_RRC_UECapabilityEnquiry = 0x432F8306,
        NR_RRC_CounterCheck = 0x432F8307,
        NR_RRC_MobilityFromNRCommand = 0x432F8308,
        NR_RRC_DL_DCCH_Spare7 = 0x432F8309,
        NR_RRC_DL_DCCH_Spare6 = 0x432F830A,
        NR_RRC_DL_DCCH_Spare5 = 0x432F830B,
        NR_RRC_DL_DCCH_Spare4 = 0x432F830C,
        NR_RRC_DL_DCCH_Spare3 = 0x432F830D,
        NR_RRC_DL_DCCH_Spare2 = 0x432F830E,
        NR_RRC_DL_DCCH_Spare1 = 0x432F830F,

        //PCCH Messages(0x84)  下行
        NR_RRC_Paging = 0x432F8400,
        NR_RRC_PCCH_Spare1 = 0x432F8401,

        //UL CCCH Messages(0x85)  上行
        NR_RRC_RRCSetupRequest = 0x432F8500,
        NR_RRC_RRCResumeRequest = 0x432F8501,
        NR_RRC_RRCReestablishmentRequest = 0x432F8502,
        NR_RRC_RRCSystemInfoRequest = 0x432F8503,

        //UL CCCH1 Messages(0x86)  上行
        NR_RRC_RRCResumeRequest1 = 0x432F8600,
        NR_RRC_UL_CCCH_Spare3 = 0x432F8601,
        NR_RRC_UL_CCCH_Spare2 = 0x432F8602,
        NR_RRC_UL_CCCH_Spare1 = 0x432F8603,

        //UL DCCH Messages(0x87)  上行
        NR_RRC_MeasurementReport = 0x432F8700,
        NR_RRC_RRCReconfigurationComplete = 0x432F8701,
        NR_RRC_RRCSetupComplete = 0x432F8702,
        NR_RRC_RRCReestablishmentComplete = 0x432F8703,
        NR_RRC_RRCResumeComplete = 0x432F8704,
        NR_RRC_SecurityModeComplete = 0x432F8705,
        NR_RRC_SecurityModeFailure = 0x432F8706,
        NR_RRC_ULInformationTransfer = 0x432F8707,
        NR_RRC_LocationMeasurementIndication = 0x432F8708,
        NR_RRC_UECapabilityInformation = 0x432F8709,
        NR_RRC_CounterCheckResponse = 0x432F870A,
        NR_RRC_UEAssistanceInformation = 0x432F870B,
        NR_RRC_FailureInformation = 0x432F870C,
        NR_RRC_UL_DCCH_Spare3 = 0x432F870D,
        NR_RRC_UL_DCCH_Spare2 = 0x432F870E,
        NR_RRC_UL_DCCH_Spare1 = 0x432F870F,

        //SIB Messages(0x88)  下行  BCCH
        NR_RRC_Other_SIB1 = 0x432F8800,
        NR_RRC_Other_SIB2 = 0x432F8801,
        NR_RRC_Other_SIB3 = 0x432F8802,
        NR_RRC_Other_SIB4 = 0x432F8803,
        NR_RRC_Other_SIB5 = 0x432F8804,
        NR_RRC_Other_SIB6 = 0x432F8805,
        NR_RRC_Other_SIB7 = 0x432F8806,
        NR_RRC_Other_SIB8 = 0x432F8807,
        NR_RRC_Other_SIB9 = 0x432F8808,

        //RS Control(0x88)
        NR_RRC_Other_RRCReconfiguration = 0x432F8809,
        NR_RRC_Other_CellGroupConfig = 0x432F880A,
        NR_RRC_Other_RadioBearerConfig = 0x432F880B,
        NR_RRC_Other_RRCReconfigurationComplete = 0x432F880C,
        NR_RRC_Other_MeasResultSCG_Failure = 0x432F880D,
        NR_RRC_Other_MeasurementReport = 0x432F880E,

        //UE Capability(0x88)
        NR_RRC_Other_Capability = 0x432F880F,
        NR_RRC_Other_MRDC_Capability = 0x432F8810,
        NR_RRC_Other_FreqBandList = 0x432F8811,
        NR_RRC_Other_MobilityFromNRCommand = 0x432F8812,

        #endregion


        #region NR_NAS

        //5GS EMM(0x89)
        NR_NAS_Registration_request = 0x432F8900,
        NR_NAS_Registration_accept = 0x432F8901,
        NR_NAS_Registration_complete = 0x432F8902,
        NR_NAS_Registration_reject = 0x432F8903,
        NR_NAS_UE_originating_Deregistration_request = 0x432F8904,
        NR_NAS_UE_originating_Deregistration_accept = 0x432F8905,
        NR_NAS_UE_terminated_Deregistration_request = 0x432F8906,
        NR_NAS_UE_terminated_Deregistration_accept = 0x432F8907,
        NR_NAS_Service_request = 0x432F8908,
        NR_NAS_Service_reject = 0x432F8909,
        NR_NAS_Service_accept = 0x432F890A,
        NR_NAS_Configuration_update_command = 0x432F890B,
        NR_NAS_Configuration_update_complete = 0x432F890C,
        NR_NAS_Authentication_request = 0x432F890D,
        NR_NAS_Authentication_response = 0x432F890E,
        NR_NAS_Authentication_reject = 0x432F890F,
        NR_NAS_Authentication_failure = 0x432F8910,
        NR_NAS_Authentication_result = 0x432F8911,
        NR_NAS_Identity_request = 0x432F8912,
        NR_NAS_Identity_response = 0x432F8913,
        NR_NAS_Security_mode_command = 0x432F8914,
        NR_NAS_Security_mode_complete = 0x432F8915,
        NR_NAS_Security_mode_reject = 0x432F8916,
        NR_NAS_5GMM_status = 0x432F8917,
        NR_NAS_Notification = 0x432F8918,
        NR_NAS_Notification_response = 0x432F8919,
        NR_NAS_UL_NAS_transport = 0x432F891A,
        NR_NAS_DL_NAS_transport = 0x432F891B,

        // 5GS ESM(0x89)
        NR_NAS_PDU_session_establishment_request = 0x432F891C,
        NR_NAS_PDU_session_establishment_accept = 0x432F891D,
        NR_NAS_PDU_session_establishment_reject = 0x432F891E,
        NR_NAS_PDU_session_authentication_command = 0x432F891F,
        NR_NAS_PDU_session_authentication_complete = 0x432F8920,
        NR_NAS_NPDU_session_authentication_result = 0x432F8921,
        NR_NAS_PDU_session_modification_request = 0x432F8922,
        NR_NAS_PDU_session_modification_reject = 0x432F8923,
        NR_NAS_PDU_session_modification_command = 0x432F8924,
        NR_NAS_PDU_session_modification_complete = 0x432F8925,
        NR_NAS_PDU_session_modification_command_reject = 0x432F8926,
        NR_NAS_PDU_session_release_request = 0x432F8927,
        NR_NAS_PDU_session_release_reject = 0x432F8928,
        NR_NAS_PDU_session_release_command = 0x432F8929,
        NR_NAS_PDU_session_release_complete = 0x432F892A,
        NR_NAS_5GSM_status = 0x432F892B

        #endregion
    }

}
