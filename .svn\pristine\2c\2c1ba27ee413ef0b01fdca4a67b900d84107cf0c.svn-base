﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Text;
using System.Windows.Forms;
using MasterCom.MTGis;
using MasterCom.RAMS.Func.SystemSetting;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.BackgroundFunc;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTLTEHighWayWeakSINRRoadQuery : ZTLTEHighWayWeakCoverRoadQuery
    {
        protected new WeakSINRRoadCondition_LTEHighRailWay weakCondition = new WeakSINRRoadCondition_LTEHighRailWay();
        private static ZTLTEHighWayWeakSINRRoadQuery intance = null;
        public static new ZTLTEHighWayWeakSINRRoadQuery GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new ZTLTEHighWayWeakSINRRoadQuery();
                    }
                }
            }
            return intance;
        }

        protected ZTLTEHighWayWeakSINRRoadQuery()
        {
            init();
        }

        private void init()
        {
            IsCanExportResultMapToWord = true;
            name = "弱质差路段_LTE";
            type = 2;
            funcId = 22000;
            subfuncId = 22013;
            desc = "分析";
            tpStr = "lte_NCell_RSRP";
            tpRSRP = "lte_RSRP";
            tpSINR = "lte_SINR";
            tpTac = "lte_TAC";
            tpSpeed = "lte_APP_Speed_Mb";
            themeName = "TD_LTE_SINR";
            tpNCell_EARFCN = "lte_NCell_EARFCN";
            tpNCell_PCI = "lte_NCell_PCI";
            tpAppType = "lte_APP_type";

            ServiceTypes.Clear();
            ServiceTypes.AddRange(ServiceTypeManager.GetLteAllServiceTypes());
        }

        public override string Name
        {
            get { return "弱质差路段(按采样点)"; }
        }

        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }
            LTEHighRailwayWeakSINRRoadDlg dlg = new LTEHighRailwayWeakSINRRoadDlg(weakCondition);
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                weakCondition = dlg.GetConditon();
                return true;
            }
            return false;
        }

        protected override void fireShowForm()
        {
            if (ZTWeakCovRoadInfoList.Count == 0)
            {
                MessageBox.Show("没有符合条件的信息！");
                return;
            }
            MainModel.FireSetDefaultMapSerialTheme(themeName);
            WeakSINRRoadLTEHighRailWayForm frm = MainModel.GetInstance().GetObjectFromBlackboard(typeof(WeakSINRRoadLTEHighRailWayForm)) as WeakSINRRoadLTEHighRailWayForm;
            if (frm == null || frm.IsDisposed)
            {
                frm = new WeakSINRRoadLTEHighRailWayForm(MainModel);
            }
            frm.FillData(ZTWeakCovRoadInfoList);
            frm.Owner = MainModel.MainForm;
            frm.Visible = true;
            frm.BringToFront();
            ZTWeakCovRoadInfoList = null;
        }
        
        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager fileDataManager in MainModel.DTDataManager.FileDataManagers)
            {
                //弱质差路段采样点集合
                List<TestPoint> weakRoadTPList = new List<TestPoint>();
                //保存连续的正常质差采样点集合
                List<TestPoint> continuousTPList = new List<TestPoint>();
                //保存弱质差采样点集合(不一定连续)
                List<TestPoint> weakTPList = new List<TestPoint>();
                foreach (TestPoint tp in fileDataManager.TestPoints)
                {
                    if (!isValidTestPoint(tp))
                    {
                        continue;
                    }
                    float? tpSINR = getSINR(tp);
                    if (tpSINR != null)
                    {
                        addWeakRoadTP(weakRoadTPList, continuousTPList, weakTPList, tp, tpSINR);
                    }
                }
            }
        }

        private void addWeakRoadTP(List<TestPoint> weakRoadTPList, List<TestPoint> continuousTPList, List<TestPoint> weakTPList, TestPoint tp, float? tpSINR)
        {
            if (tpSINR <= weakCondition.SINR)
            {
                //rsrp满足条件时添加弱质差采样点
                weakTPList.Add(tp);
                //添加2个弱质差采样点间的正常质差采样点到弱质差路段集合
                foreach (TestPoint normalTP in continuousTPList)
                {
                    weakRoadTPList.Add(normalTP);
                }
                weakRoadTPList.Add(tp);
                continuousTPList.Clear();
            }
            else if (weakTPList.Count > 0)
            {
                //当存在弱质差采样点时(弱质差路段首个采样点要为弱质差采样点),添加正常质差采样点
                continuousTPList.Add(tp);
                if (continuousTPList.Count >= weakCondition.NormalTestPoints)
                {
                    //正常质差采样点数大于NormalTestPoints,退出弱质差路段统计
                    if (weakTPList.Count >= weakCondition.WeakSINRRoadTestPoints)
                    {
                        //弱质差采样点数大于WeakCoverRoadTestPoints,添加弱质差路段
                        saveWeakCovRoadInfo(weakRoadTPList, weakTPList.Count);
                    }
                    //清空以保存的数据或不满足条件舍弃
                    weakRoadTPList.Clear();
                    continuousTPList.Clear();
                    weakTPList.Clear();
                }
            }
        }
    }

    public class ZTLTEFDHighWayDWeakSINRRoadQuery : ZTLTEHighWayWeakSINRRoadQuery
    {
        public ZTLTEFDHighWayDWeakSINRRoadQuery()
        {
            
        }

        public ZTLTEFDHighWayDWeakSINRRoadQuery(MainModel mainModel)
            : base()
        {
            init();
        }

        private void init()
        {
            name = "弱质差路段_LTE_FDD";
            type = 2;
            funcId = 26000;
            subfuncId = 26010;
            desc = "分析";
            tpStr = "lte_fdd_NCell_RSRP";
            tpRSRP = "lte_fdd_RSRP";
            tpSINR = "lte_fdd_SINR";
            tpTac = "lte_fdd_TAC";
            tpSpeed = "lte_fdd_APP_Speed_Mb";
            themeName = "LTE_FDD:SINR";
            tpNCell_EARFCN = "lte_fdd_NCell_EARFCN";
            tpNCell_PCI = "lte_fdd_NCell_PCI";
            tpAppType = "lte_fdd_APP_type";
        }
    }

}
