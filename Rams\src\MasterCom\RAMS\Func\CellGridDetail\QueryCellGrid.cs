﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Data;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.Func
{
    public class QueryCellGrid : QueryBase
    {
        private static readonly object lockObj = new object();
        private static QueryCellGrid Instance = null;
        public List<CellGridWithCell> ResultList { get; set; }
        private new CellGridDetailCondition condition = null;
        private List<CellGridDetailInfo> cgDetails = new List<CellGridDetailInfo>();

        public static QueryCellGrid GetInstance()
        {
            if (Instance == null)
            {
                lock (lockObj)
                {
                    if (Instance == null)
                    {
                        Instance = new QueryCellGrid();
                    }
                }
            }
            return Instance;
        }
        public override string Name
        {
            get { return "小区栅格统计"; }
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MainModel.NeedSearchType.Region;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            QueryCondition con = new QueryCondition();
            con.Geometorys = searchGeometrys;
            SetQueryCondition(con);
            return true;
        }
        public override bool IsNeedSetQueryCondition
        {
            get
            {
                return false;
            }
        }
        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 18000, 18039, this.Name);
        }
        CellGridDetailSetConditionForm setForm = null;
        protected override bool isValidCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }
            if (setForm == null)
            {
                setForm = new CellGridDetailSetConditionForm();
            }
            if (setForm.ShowDialog() == DialogResult.OK)
            {
                ResultList = new List<CellGridWithCell>();
                this.condition = setForm.GetCondition();
                return true;
            }
            return false;
        }
        protected QueryCellGrid()
            : base(MainModel.GetInstance())
        {

        }
        protected override void query()
        {
            WaitBox.Show(doStat);
            if (ResultList == null || ResultList.Count == 0)
            {
                MessageBox.Show("没有符合条件的数据");
                return;
            }

            showResultForm();
        }
        private void doStat()
        {
            try
            {
                List<LTECell> cells = new List<LTECell>();
                foreach (LTECell cel in CellManager.GetInstance().GetCurrentLTECells())
                {
                    if (this.Condition.Geometorys == null || this.Condition.Geometorys.GeoOp.Contains(cel.Longitude, cel.Latitude))
                    {
                        cells.Add(cel);
                    }
                }

                if (cells.Count <= 0)
                {
                    MessageBox.Show("没有获取到区域内的小区数据");
                    return;
                }

                DistrictManager mgr = DistrictManager.GetInstance();
                int iloop = 0;
                foreach (Stat.IDNamePair pair in mgr.GetAvailableDistrict())
                {
                    WaitBox.Text = "正在统计[" + pair.Name + "]的栅格问题点情况,统计进度(" + (++iloop) + "/" + mgr.GetAvailableDistrict().Count + ")...";
                    ClientProxy clientProxy = new ClientProxy();

                    if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, pair.id) != ConnectResult.Success)
                    {
                        ErrorInfo = "连接服务器失败";
                        continue;
                    }

                    statEachDB(cells);
                    break;
                }
                WaitBox.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.ToString());
                throw;
            }
        }
        private void statEachDB(List<LTECell> cells)
        {
            foreach (string name in this.condition.TableNames)
            {
                DIYSQLCellGridDetail queryDetail = new DIYSQLCellGridDetail(MainModel, name);

                Dictionary<LTECell, List<CellGridDetailInfo>> detailDic = new Dictionary<LTECell, List<CellGridDetailInfo>>();
                try
                {
                    if (cgDetails.Count <= 0)
                    {
                        queryDetail.Query();
                        cgDetails = queryDetail.CellGridDetails;
                    }
                    setDetailDic(cells, detailDic);
                    addResultList(detailDic);
                }
                catch (Exception ex)
                {
                    throw (new Exception("查询小区栅格数据失败：" + ex.ToString()));
                }
            }
        }

        private void setDetailDic(List<LTECell> cells, Dictionary<LTECell, List<CellGridDetailInfo>> detailDic)
        {
            foreach (CellGridDetailInfo detail in cgDetails)
            {
                if (detail.lteCell == null || !cells.Contains(detail.lteCell))
                {
                    continue;
                }
                if (!detailDic.ContainsKey(detail.lteCell))
                {
                    detailDic[detail.lteCell] = new List<CellGridDetailInfo>();
                }
                detailDic[detail.lteCell].Add(detail);
            }
        }

        private void addResultList(Dictionary<LTECell, List<CellGridDetailInfo>> detailDic)
        {
            foreach (KeyValuePair<LTECell, List<CellGridDetailInfo>> kv in detailDic)
            {
                CellGridWithCell cgw = new CellGridWithCell();
                cgw.CellGridDetails = kv.Value;
                cgw.LAC = kv.Key.TAC;
                cgw.CI = kv.Key.ECI;
                cgw.Cell = kv.Key.Name;
                cgw.isChecked = true;
                cgw.SN = ResultList.Count + 1;
                ResultList.Add(cgw);
            }
        }

        private void showResultForm()
        {
            CellGridDetailResultForm fm = MainModel.CreateResultForm(typeof(CellGridDetailResultForm)) as CellGridDetailResultForm;
            fm.FillData(ResultList);
            fm.Visible = true;
            fm.BringToFront();

            ResultList = null;
        }
    }

    public class CellGridDetailCondition
    {
        private readonly List<string> tableNames = new List<string>();
        public List<string> TableNames
        {
            get { return tableNames; }
        }
        public void AddTableName(string name)
        {
            tableNames.Add(name);
        }
    }
}
