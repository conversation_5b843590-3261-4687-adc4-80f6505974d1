﻿namespace MasterCom.RAMS.NOP
{
    partial class TaskOrderForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.lblDesc = new System.Windows.Forms.Label();
            this.pnlServerInfo = new System.Windows.Forms.Panel();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.btnModifySvrInfo = new System.Windows.Forms.Button();
            this.iPbox = new System.IPCtrl.IPbox();
            this.numPort = new System.Windows.Forms.NumericUpDown();
            this.label2 = new System.Windows.Forms.Label();
            this.label1 = new System.Windows.Forms.Label();
            this.pnlServerInfo.SuspendLayout();
            this.groupBox1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numPort)).BeginInit();
            this.SuspendLayout();
            // 
            // lblDesc
            // 
            this.lblDesc.Dock = System.Windows.Forms.DockStyle.Top;
            this.lblDesc.Location = new System.Drawing.Point(0, 0);
            this.lblDesc.Name = "lblDesc";
            this.lblDesc.Size = new System.Drawing.Size(824, 14);
            this.lblDesc.TabIndex = 1;
            this.lblDesc.Text = "NOP服务端信息配置错误，请联系管理员或手动输入信息";
            // 
            // pnlServerInfo
            // 
            this.pnlServerInfo.Controls.Add(this.groupBox1);
            this.pnlServerInfo.Dock = System.Windows.Forms.DockStyle.Top;
            this.pnlServerInfo.Location = new System.Drawing.Point(0, 14);
            this.pnlServerInfo.Name = "pnlServerInfo";
            this.pnlServerInfo.Size = new System.Drawing.Size(824, 105);
            this.pnlServerInfo.TabIndex = 2;
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.btnModifySvrInfo);
            this.groupBox1.Controls.Add(this.iPbox);
            this.groupBox1.Controls.Add(this.numPort);
            this.groupBox1.Controls.Add(this.label2);
            this.groupBox1.Controls.Add(this.label1);
            this.groupBox1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupBox1.Location = new System.Drawing.Point(0, 0);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(824, 105);
            this.groupBox1.TabIndex = 0;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "NOP服务端信息";
            // 
            // btnModifySvrInfo
            // 
            this.btnModifySvrInfo.Location = new System.Drawing.Point(254, 62);
            this.btnModifySvrInfo.Name = "btnModifySvrInfo";
            this.btnModifySvrInfo.Size = new System.Drawing.Size(75, 23);
            this.btnModifySvrInfo.TabIndex = 2;
            this.btnModifySvrInfo.Text = "确定";
            this.btnModifySvrInfo.UseVisualStyleBackColor = true;
            this.btnModifySvrInfo.Click += new System.EventHandler(this.btnModifySvrInfo_Click);
            // 
            // iPbox
            // 
            this.iPbox.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D;
            this.iPbox.Location = new System.Drawing.Point(108, 30);
            this.iPbox.Name = "iPbox";
            this.iPbox.Size = new System.Drawing.Size(124, 20);
            this.iPbox.TabIndex = 0;
            // 
            // numPort
            // 
            this.numPort.Location = new System.Drawing.Point(108, 64);
            this.numPort.Maximum = new decimal(new int[] {
            65535,
            0,
            0,
            0});
            this.numPort.Name = "numPort";
            this.numPort.Size = new System.Drawing.Size(120, 22);
            this.numPort.TabIndex = 1;
            this.numPort.Value = new decimal(new int[] {
            7100,
            0,
            0,
            0});
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(59, 66);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(43, 14);
            this.label2.TabIndex = 0;
            this.label2.Text = "端口：";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(48, 32);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(54, 14);
            this.label1.TabIndex = 0;
            this.label1.Text = "IP地址：";
            // 
            // TaskOrderForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(824, 418);
            this.Controls.Add(this.pnlServerInfo);
            this.Controls.Add(this.lblDesc);
            this.Name = "TaskOrderForm";
            this.Text = "工单列表";
            this.WindowState = System.Windows.Forms.FormWindowState.Maximized;
            this.pnlServerInfo.ResumeLayout(false);
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numPort)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.Label lblDesc;
        private System.Windows.Forms.Panel pnlServerInfo;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.NumericUpDown numPort;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label label1;
        private System.IPCtrl.IPbox iPbox;
        private System.Windows.Forms.Button btnModifySvrInfo;
    }
}