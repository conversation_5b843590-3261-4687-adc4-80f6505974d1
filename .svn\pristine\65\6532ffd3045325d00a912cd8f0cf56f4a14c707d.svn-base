﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTUrlAnalyzer
    {
        public static List<int> HttpIDList { get; } = new List<int>
        {
            (int)NREventManager.HttpRequest,
            (int)NREventManager.HttpSuccess,
            (int)NREventManager.HttpDisFail,
            (int)NREventManager.HttpComplete,
            (int)NREventManager.HttpIncomplete,
            (int)NREventManager.HttpFail
        };

        public static List<int> DownloadIDList { get; } = new List<int>
        {
            (int)NREventManager.DownRequest,
            (int)NREventManager.DownSuccess,
            (int)NREventManager.DownDrop,
            (int)NREventManager.DownloadFail
        };

        public static List<int> VideoIDList { get; } = new List<int>
        {
            (int)NREventManager.FlvPlayFinished,
            (int)NREventManager.VideoRequest,
            (int)NREventManager.VideoFirstData,
            (int)NREventManager.VideoRebufferStart,
            (int)NREventManager.VideoRebufferEnd,
            (int)NREventManager.VideoLastData,
            (int)NREventManager.VideoFinish,
            (int)NREventManager.VideoDrop,
            (int)NREventManager.VideoReproductionStart,
            (int)NREventManager.VideoFail,
        };

        protected void setSN(List<NRUrlAnaInfo> results)
        {
            int fileSN = 0;
            foreach (var logFile in results)
            {
                logFile.SN = ++fileSN;

                int BroSN = 0;
                foreach (var logBro in logFile.Bros)
                {
                    logBro.SN = ++BroSN;
                    setEvtSN(logBro.Events);
                }

                int DowSN = 0;
                foreach (var logDow in logFile.Downs)
                {
                    logDow.SN = ++DowSN;
                    setEvtSN(logDow.Events);
                }

                int VideoSN = 0;
                foreach (var logVideo in logFile.Videos)
                {
                    logVideo.SN = ++VideoSN;
                    setEvtSN(logVideo.Events);
                }
            }
        }

        protected void setEvtSN(List<NRUrlEvent> logEvts)
        {
            int evtSN = 0;
            foreach (var logEvt in logEvts)
            {
                logEvt.SN = ++evtSN;
            }
        }

        #region HttpAnalyze
        public static List<NRUrlBroUrl> HttpAnalyze(Dictionary<string, List<Event>> dic)
        {
            List<NRUrlBroUrl> dtlBroURLs = new List<NRUrlBroUrl>();
            foreach (var kvp in dic)
            {
                List<Event> CompleteTemp = new List<Event>();
                List<Event> DisSucTemp = new List<Event>();
                List<Event> DisFailTemp = new List<Event>();
                NRUrlBroUrl dtlBroURL = new NRUrlBroUrl(kvp.Key);
                Event evtBeforRequest = null;
                Event firstEvt = null;
                Event failEvt = null;
                if (kvp.Value.Count < 2)
                {
                    continue;
                }
                foreach (Event evt in kvp.Value)
                {
                    if (evt.ID == (int)NREventManager.HttpRequest)
                    {
                        if (evtBeforRequest != null)
                        {
                            dealHttpEndEvent(CompleteTemp, DisSucTemp, DisFailTemp, dtlBroURL, evtBeforRequest, firstEvt, failEvt);
                            failEvt = null;
                        }
                        firstEvt = evt;
                    }
                    else
                    {
                        evtBeforRequest = evt;
                        failEvt = addHttpEvt(CompleteTemp, DisSucTemp, DisFailTemp, failEvt, evt);
                    }
                }
                dealHttpEndEvent(CompleteTemp, DisSucTemp, DisFailTemp, dtlBroURL, evtBeforRequest, firstEvt, failEvt);
                setBroInfo(dtlBroURL);
                dtlBroURL.Calculate();
                dtlBroURLs.Add(dtlBroURL);
            }
            return dtlBroURLs;
        }

        private static void dealHttpEndEvent(List<Event> CompleteTemp, List<Event> DisSucTemp, List<Event> DisFailTemp, NRUrlBroUrl dtlBroURL, Event evtBeforRequest, Event firstEvt, Event failEvt)
        {
            if (isHttpEndEvent(evtBeforRequest))
            {
                dtlBroURL.EvtComple.AddRange(CompleteTemp);
                dtlBroURL.EvtDisFai.AddRange(DisFailTemp);
                dtlBroURL.EvtDisSuc.AddRange(DisSucTemp);

                NRUrlEvent dtlEvent1 = new NRUrlEvent(firstEvt, evtBeforRequest, failEvt, dtlBroURL.Url);
                dtlBroURL.Events.Add(dtlEvent1);
            }
            CompleteTemp.Clear();
            DisFailTemp.Clear();
            DisSucTemp.Clear();
        }

        private static Event addHttpEvt(List<Event> CompleteTemp, List<Event> DisSucTemp, List<Event> DisFailTemp, Event failEvt, Event evt)
        {
            if (evt.ID == (int)NREventManager.HttpFail)
            {
                failEvt = evt;
            }
            else if (evt.ID == (int)NREventManager.HttpDisFail)
            {
                DisFailTemp.Add(evt);
            }
            else if (evt.ID == (int)NREventManager.HttpComplete)
            {
                CompleteTemp.Add(evt);
            }
            else if (evt.ID == (int)NREventManager.HttpSuccess)
            {
                DisSucTemp.Add(evt);
            }

            return failEvt;
        }

        private static bool isHttpEndEvent(Event evt)
        {
            if (evt == null)
            {
                return false;
            }
            return evt.ID == (int)NREventManager.HttpComplete 
                || evt.ID == (int)NREventManager.HttpFail
                || evt.ID == (int)NREventManager.HttpIncomplete 
                || evt.ID == (int)NREventManager.HttpDisFail;
        }

        private static void setBroInfo(NRUrlBroUrl broURL)
        {
            if (broURL.EvtDisSuc.Count > 0)
            {
                foreach (Event evt in broURL.EvtDisSuc)
                {
                    broURL.Value2Sum += double.Parse(evt["Value2"].ToString());
                }
                broURL.DisDelay = Math.Round((double)broURL.Value2Sum / (double)broURL.DisSuc / 1000, 3);
            }
            if (broURL.EvtComple.Count > 0)
            {
                foreach (Event evt in broURL.EvtComple)
                {
                    broURL.Value1Sum += double.Parse(evt["Value1"].ToString());
                    broURL.Value2Sum1 += double.Parse(evt["Value2"].ToString());
                }
                broURL.Time = Math.Round((double)broURL.Value1Sum / (double)broURL.Complete / 1000, 3);
                broURL.Speed = Math.Round((((double)broURL.Value2Sum1 * 8000) / (double)broURL.Value1Sum) / 1024, 3);
            }
        }
        #endregion

        #region
        public static List<NRUrlDowUrl> DownAnalyze(Dictionary<string, List<Event>> dic)
        {
            List<NRUrlDowUrl> dtlDownURLs = new List<NRUrlDowUrl>();
            foreach (KeyValuePair<string, List<Event>> kvp in dic)
            {
                NRUrlDowUrl dtlDownURL = new NRUrlDowUrl(kvp.Key);
                Event evtBeforRequest = null;
                Event firstEvt = null;
                Event failEvt = null;
                if (kvp.Value.Count < 2)
                {
                    continue;
                }
                foreach (Event evt in kvp.Value)
                {
                    addDownEvt(kvp.Key, dtlDownURL, ref evtBeforRequest, ref firstEvt, ref failEvt, evt);
                }
                NRUrlEvent dtlEventTemp = new NRUrlEvent(firstEvt, evtBeforRequest, failEvt, kvp.Key);
                dtlDownURL.Events.Add(dtlEventTemp);

                setDowInfo(dtlDownURL);
                dtlDownURL.Calculate();
                dtlDownURLs.Add(dtlDownURL);
            }
            return dtlDownURLs;
        }

        private static void addDownEvt(string key, NRUrlDowUrl dtlDownURL, ref Event evtBeforRequest, ref Event firstEvt, ref Event failEvt, Event evt)
        {
            if (evt.ID == (int)NREventManager.DownRequest)
            {
                if (evtBeforRequest == null)
                {
                    firstEvt = evt;
                }
                else
                {
                    NRUrlEvent dtlEvent = new NRUrlEvent(firstEvt, evtBeforRequest, failEvt, key);
                    dtlDownURL.Events.Add(dtlEvent);
                    failEvt = null;
                    firstEvt = evt;
                }
            }
            else
            {
                evtBeforRequest = evt;
                if (evt.ID == (int)NREventManager.DownloadFail)
                {
                    failEvt = evt;
                    dtlDownURL.EvtFail.Add(evt);
                }
                else if (evt.ID == (int)NREventManager.DownDrop)
                {
                    dtlDownURL.EvtDowFai.Add(evt);
                }
                else if (evt.ID == (int)NREventManager.DownSuccess)
                {
                    dtlDownURL.EvtDowSuc.Add(evt);
                }
            }
        }

        private static void setDowInfo(NRUrlDowUrl dowURL)
        {
            if (dowURL.EvtDowSuc.Count > 0)
            {
                foreach (Event evt in dowURL.EvtDowSuc)
                {
                    dowURL.Value1Sum += double.Parse(evt["Value1"].ToString());
                    dowURL.Value2Sum += double.Parse(evt["Value2"].ToString());
                }
                dowURL.SucSpeed = Math.Round(((float)dowURL.Value2Sum / (float)dowURL.Value1Sum) * 8000 / 1024, 3);
                if (dowURL.EvtDowFai.Count > 0)
                {
                    foreach (Event evt in dowURL.EvtDowFai)
                    {
                        dowURL.Value1Sum1 += double.Parse(evt["Value1"].ToString());
                        dowURL.Value2Sum1 += double.Parse(evt["Value2"].ToString());
                    }
                }
                dowURL.Speed = Math.Round(((float)(dowURL.Value2Sum + dowURL.Value2Sum1) / (float)(dowURL.Value1Sum + dowURL.Value1Sum1)) * 8000 / 1024, 3);
            }
        }
        #endregion

        #region
        public static List<NRUrlVideoUrl> VideoAnalyze(Dictionary<string, List<Event>> dic)
        {
            List<NRUrlVideoUrl> dtlVideoURLs = new List<NRUrlVideoUrl>();
            foreach (KeyValuePair<string, List<Event>> kvp in dic)
            {
                NRUrlVideoUrl dtlVideoURL = new NRUrlVideoUrl(kvp.Key);
                Event evtBeforRequest = null;
                Event firstEvt = null;
                Event failEvt = null;
                if (kvp.Value.Count < 2)
                {
                    continue;
                }
                foreach (Event evt in kvp.Value)
                {
                    if (evt.ID == (int)NREventManager.VideoRequest)
                    {
                        if (evtBeforRequest == null)
                        {
                            firstEvt = evt;
                            dtlVideoURL.EvtReq.Add(evt);
                        }
                        else
                        {
                            dtlVideoURL.EvtReq.Add(evt);
                            NRUrlEvent dtlEvent = new NRUrlEvent(firstEvt, evtBeforRequest, failEvt, kvp.Key);
                            dtlVideoURL.Events.Add(dtlEvent);
                            failEvt = null;
                            firstEvt = evt;
                        }
                    }
                    evtBeforRequest = evt;
                    failEvt = addVideoEvt(dtlVideoURL, failEvt, evt);
                }
                NRUrlEvent dtlEventTemp = new NRUrlEvent(firstEvt, evtBeforRequest, failEvt, kvp.Key);
                dtlVideoURL.Events.Add(dtlEventTemp);

                setVideoInfo(dtlVideoURL);
                dtlVideoURL.Calculate();
                dtlVideoURLs.Add(dtlVideoURL);
            }
            return dtlVideoURLs;
        }

        private static Event addVideoEvt(NRUrlVideoUrl dtlVideoURL, Event failEvt, Event evt)
        {
            switch (evt.ID)
            {
                case (int)NREventManager.VideoFail:
                    failEvt = evt;
                    break;
                case (int)NREventManager.VideoFinish:
                    dtlVideoURL.EvtSuc.Add(evt);
                    break;
                case (int)NREventManager.VideoLastData:
                    dtlVideoURL.EvtLastData.Add(evt);
                    break;
                case (int)NREventManager.VideoRebufferStart:
                    dtlVideoURL.EvtRebuffer.Add(evt);
                    break;
                case (int)NREventManager.VideoReproductionStart:
                    dtlVideoURL.EvtPlayStart.Add(evt);
                    break;
                case (int)NREventManager.VideoRebufferEnd:
                    dtlVideoURL.EvtRebufferEnd.Add(evt);
                    break;
                case (int)NREventManager.FlvPlayFinished:
                    dtlVideoURL.EvtFlvPlayFinished.Add(evt);
                    break;
            }

            return failEvt;
        }

        private static void setVideoInfo(NRUrlVideoUrl videoURL)
        {
            if (videoURL.EvtLastData.Count > 0)
            {
                int count = videoURL.EvtLastData.Count;
                foreach (Event evt in videoURL.EvtLastData)
                {
                    videoURL.value7Sum35 += double.Parse(evt["Value7"].ToString());
                    videoURL.value6Sum35 += double.Parse(evt["Value6"].ToString());
                    videoURL.value5Sum35 += double.Parse(evt["Value5"].ToString());
                    videoURL.value4Sum35 += double.Parse(evt["Value4"].ToString());
                    videoURL.value2Sum35 += double.Parse(evt["Value2"].ToString());
                    videoURL.value1Sum35 += double.Parse(evt["Value1"].ToString());
                }
                videoURL.Time = Math.Round(((float)videoURL.value6Sum35 / (float)count) / 1000, 3);
                //videoURL.RebufferTime = Math.Round(((float)videoURL.value4Sum35 / (float)count) / 1000, 3);
                videoURL.PlayTime = Math.Round(((float)videoURL.value5Sum35 / (float)count) / 1000, 3);
                videoURL.TimeoutRate = Math.Round(((float)videoURL.value5Sum35 / (float)videoURL.value6Sum35 - 1), 4);
                videoURL.DownSpeed = Math.Round(((float)videoURL.value2Sum35 / (float)videoURL.value1Sum35) * 8000 / 1024, 3);
            }

            if (videoURL.EvtPlayStart.Count > 0)
            {
                int count = videoURL.EvtPlayStart.Count;
                foreach (Event evt in videoURL.EvtPlayStart)
                {
                    videoURL.value1Sum38 += double.Parse(evt["Value1"].ToString());
                }
                videoURL.Delay = Math.Round(((float)videoURL.value1Sum38 / (float)count) / 1000, 3);
                videoURL.LoadSpeed = Math.Round(((float)videoURL.value7Sum35 / (float)videoURL.value1Sum38) * 8000 / 1024, 3);
            }

            if (videoURL.EvtRebufferEnd.Count > 0 || videoURL.EvtFlvPlayFinished.Count > 0)
            {
                int count = videoURL.EvtRebufferEnd.Count + videoURL.EvtFlvPlayFinished.Count;
                foreach (Event evt in videoURL.EvtRebufferEnd)
                {
                    videoURL.value2Sum34 += double.Parse(evt["Value2"].ToString());
                }
                foreach (Event evt in videoURL.EvtFlvPlayFinished)
                {
                    videoURL.value2Sum29 += double.Parse(evt["Value2"].ToString());
                }
                double? sum = videoURL.value2Sum34 + videoURL.value2Sum29;
                videoURL.RebufferTime = Math.Round(((float)sum / (float)count) / 1000, 3);
            }
        }
        #endregion

        #region 解析URL
        public static string GetURL(int sn, List<Message> messages)
        {
            string url = "";
            for (int i = sn - 1; i >= 0; i--)
            {
                foreach (Message msg in messages)
                {
                    if (msg.SN == i)
                    {
                        byte[] source = ((MessageWithSource)msg).Source;
                        url = displaySrcCode(source);
                        return url;
                    }
                }
            }
            return url;
        }

        private static string displaySrcCode(byte[] srcCode)
        {
            string URLtext = "";
            if (srcCode != null)
            {
                int pos = findFistChar(srcCode);
                if (pos < srcCode.Length)
                {
                    int lastLen = srcCode.Length - pos;
                    URLtext = srcToString(pos, lastLen, srcCode);
                }
                else
                {
                    URLtext = ".";
                }
            }
            return URLtext;
        }

        private static int findFistChar(byte[] srcCode)
        {
            byte byt;
            for (int i = 0; i < srcCode.Length; i++)
            {
                byt = srcCode[i];
                if (Char.IsLetter((char)byt))
                {
                    string firstChar = Encoding.Default.GetString(srcCode, i, 1);
                    switch (firstChar)
                    {
                        case "B":
                            if (srcCode.Length > i + 14 && Encoding.Default.GetString(srcCode, i + 7, 4) == "http")
                            {
                                return i + 14;
                            }
                            //Browse www.baidu.com
                            return i + 7;
                        case "S":
                            //Stream https://tv.sohu.com/20180506/n600505668.shtml
                            return i + 7;
                        case "D":
                            //Download https://dldir1.qq.com/qqfile/qq/QQ9.0.4/23786/QQ9.0.4.exe
                            return i + 9;
                    }
                }
            }
            return srcCode.Length - 1;
        }

        private static string srcToString(int bgnPos, int len, byte[] srcCode)
        {
            StringBuilder dstStr = new StringBuilder();
            for (int pos = bgnPos; pos < (bgnPos + len); pos++)
            {
                String tmp = toAssicString(pos, srcCode);
                dstStr.Append(tmp);
            }
            return dstStr.ToString();
        }

        private static string toAssicString(int bgnPos, byte[] srcCode)
        {
            byte byt = srcCode[bgnPos];
            if (byt < 0 || byt > 0xa0)
                return ".";
            if (byt >= '!' && byt <= '~')
            {
                return Encoding.Default.GetString(srcCode, bgnPos, 1);
            }
            return ".";
        }
        #endregion

    }
}
