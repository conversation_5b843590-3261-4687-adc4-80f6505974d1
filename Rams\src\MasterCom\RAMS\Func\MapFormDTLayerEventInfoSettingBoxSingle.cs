﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.Func
{
    public partial class MapFormDTLayerEventInfoSettingBoxSingle : BaseDialog
    {
        public MapFormDTLayerEventInfoSettingBoxSingle(EventInfo eventInfo)
        {
            InitializeComponent();
            this.eventInfo = eventInfo;
        }

        private EventInfo eventInfo;

        private void MapFormDTLayerEventInfoSettingBoxSingle_Load(object sender, EventArgs e)
        {
            init();
        }

        private void init()
        {
            foreach (EventInfo e in EventInfoManager.GetInstance().EventInfos)
            {
                comboBoxSymbol.Items.Add(e.ImagePath);
            }
            txtID.Text = eventInfo.ID.ToString();
            textBoxName.Text = eventInfo.Name;
            textBoxNameZH.Text = eventInfo.NameZH;
            comboBoxSymbol.SelectedItem = eventInfo.ImagePath;
        }

        private void comboBoxSymbol_SelectedIndexChanged(object sender, EventArgs e)
        {
            labelSymbolPreview.Image = EventInfoManager.GetInstance().EventInfos[comboBoxSymbol.SelectedIndex].Image;
        }

        private void buttonOk_Click(object sender, EventArgs e)
        {
            eventInfo.Name = textBoxName.Text;
            eventInfo.NameZH = textBoxNameZH.Text;
            eventInfo.ImagePath = (string)comboBoxSymbol.SelectedItem;
        }

        private void buttonCancel_Click(object sender, EventArgs e)
        {
            this.Close();
        }
    }
}
