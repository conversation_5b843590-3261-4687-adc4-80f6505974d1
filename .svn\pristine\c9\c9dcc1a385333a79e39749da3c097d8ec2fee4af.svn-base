﻿using DevExpress.XtraTreeList.Nodes;
using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.Func
{
    public partial class EventChooserSimpleForm : BaseForm
    {
        private static EventChooserSimpleForm instance;
        private List<EventInfo> eventResultInfoList;

        public static EventChooserSimpleForm GetInstance(MainModel mainModel)
        {
            if (instance == null)
            {
                instance = new EventChooserSimpleForm(mainModel);
            }
            return instance;
        }

        public EventChooserSimpleForm(MainModel mainModel)
        {
            this.mainModel = mainModel;
            InitializeComponent();
        }

        private Dictionary<int, bool> checkedEventResultDic = new Dictionary<int, bool>();

        private void treeListEvent_AfterCheckNode(object sender, DevExpress.XtraTreeList.NodeEventArgs e)
        {
            EventInfo eventInfo = e.Node.Tag as EventInfo;

            if (!eventInfo.IsEventCollection())
            {
                checkedEventResultDic[eventInfo.ID] = e.Node.Checked;
            }
            else
            {
                foreach (TreeListNode node in e.Node.Nodes)
                {
                    EventInfo evtInfo = node.Tag as EventInfo;
                    checkedEventResultDic[evtInfo.ID] = e.Node.Checked;
                }
            }
        }

        public virtual List<int> SelectedEventIDs
        {
            get
            {
                List<int> selectedEventIDs = new List<int>();
                if (checkBoxSelectAll.Checked)
                {
                    selectedEventIDs.Add(-1);
                }
                else
                {
                    foreach (int id in checkedEventResultDic.Keys)
                    {
                        bool isChecked;
                        if (checkedEventResultDic.TryGetValue(id, out isChecked))
                        {
                            if (isChecked)
                            {
                                selectedEventIDs.Add(id);
                            }
                        }
                    }
                }
                return selectedEventIDs;
            }
        }

        public void SetEventList(List<int> eventResultIdList)
        {
            eventResultInfoList = new List<EventInfo>();

            var eventTree = EventInfoManager.GetInstance().EventTreeInfos;

            foreach (var evtList in eventTree)
            {
                foreach (var evt in evtList.eventInfo)
                {
                    if (eventResultIdList.Contains(evt.ID))
                    {
                        eventResultInfoList.Add(evt);
                    }
                }
            }

            SortEventResultInfoList();
            SetHasSelectEvent(eventResultIdList);
            loadEvent();
        }

        private void SortEventResultInfoList()
        {
            if (eventResultInfoList == null)
            {
                return;
            }

            var errCallEventId = new List<int>() { 9149, 9150, 9151, 9152, 9153, 9154, 9155, 9156, 9157, 9158, 9159, 9160, 
                9161, 9162, 9163, 9164, 9165, 9166, 9167, 9168, 9169, 9170, 9179, 9180, 9181, 9182, 9183, 9184, 9185, 9186, 
                9187, 9188, 9189, 9190, 9191, 9192, 9193, 9194, 9195, 9196, 9197, 9198, 9199, 9200, 9201, 9202, 9203, 9204, 
                9205, 9206, 9207, 9208, 9222, 9224, 9225, 9226, 9232, 9233, 9234, 9237, 9266, 9267, 9268, 9269, 9270, 9271, 
                9274, 9275, 9276, 9277, 9334, 9335, 9336, 9337, 9338, 9339, 9340, 9341, 9342, 9343, 9344, 9345, 9346, 9347, 
                9348, 9349, 9350, 9351, 9352, 9353, 9354, 9355, 9356, 9357, 9358, 9359, 9360, 9361, 9362, 9363, 9364, 9365, 
                9366, 9367, 9368, 9369, 9370, 9371, 9372, 9373, 9538, 9539, 9552, 9553, 9554, 9555, 9556, 9834, 9835, 9836, 
                9837, 9838, 9839, 9840, 9841, 9842, 9843, 9844, 9845, 9860, 9861, 9862, 9863, 9864, 9865, 9866, 9867, 9868, 
                9869, 9870, 9871, 9900, 9901, 9902 };

            var errHandOverId = new List<int>() { 9001, 9002, 9003, 9004, 9005, 9008, 9009, 9010, 9012, 9013, 9014, 9015, 
                9016, 9017, 9018, 9019, 9020, 9022, 9023, 9024, 9025, 9050, 9057, 9058, 9059, 9060, 9061, 9062, 9063, 9064, 
                9073, 9074, 9075, 9076, 9077, 9078, 9079, 9080, 9081, 9082, 9083, 9084, 9085, 9087, 9088, 9089, 9091, 9092, 
                9093, 9094, 9095, 9096, 9097, 9098, 9099, 9100, 9101, 9104, 9105, 9106, 9107, 9108, 9122, 9123, 9124, 9125, 
                9126, 9127, 9128, 9129, 9130, 9131, 9132, 9133, 9134, 9135, 9136, 9137, 9138, 9139, 9140, 9141, 9142, 9143, 
                9144, 9145, 9171, 9172, 9173, 9174, 9175, 9176, 9177, 9178, 9209, 9210, 9211, 9212, 9213, 9214, 9215, 9216, 
                9217, 9218, 9219, 9220, 9235, 9236, 9238, 9239, 9240, 9241, 9242, 9243, 9244, 9245, 9246, 9247, 9248, 9249, 
                9250, 9251, 9252, 9253, 9254, 9255, 9256, 9257, 9258, 9259, 9260, 9261, 9262, 9263, 9264, 9265, 9272, 9273, 
                9278, 9279, 9282, 9283, 9292, 9293, 9294, 9295, 9296, 9297, 9298, 9299, 9300, 9301, 9302, 9303, 9304, 9305, 
                9306, 9307, 9308, 9309, 9310, 9311, 9312, 9313, 9314, 9315, 9316, 9317, 9318, 9319, 9320, 9321, 9322, 9323, 
                9324, 9325, 9326, 9327, 9328, 9329, 9330, 9331, 9332, 9400, 9401, 9402, 9403, 9404, 9405, 9406, 9407, 9408, 
                9409, 9410, 9411, 9412, 9413, 9414, 9415, 9540, 9541, 9542, 9543, 9544, 9545, 9546, 9547, 9548, 9549, 9550, 
                9551, 9557, 9558, 9559, 9584, 9585, 9586, 9587, 9588, 9589, 9590, 9591, 9592, 9593, 9594, 9595, 9601, 9602, 
                9603, 9604, 9605, 9606, 9607, 9608, 9609, 9620, 9621, 9622, 9623, 9624, 9625, 9626, 9627, 9628, 9629, 9640, 
                9641, 9642, 9643, 9644, 9645, 9646, 9647, 9648 };

            List<EventInfo> sortEventResultInfoList = new List<EventInfo>();

            foreach (var evtResultInfo in eventResultInfoList)
            {
                if (errCallEventId.Contains(evtResultInfo.ID))
                {
                    sortEventResultInfoList.Add(evtResultInfo);
                }
            }

            foreach (var evtResultInfo in eventResultInfoList)
            {
                if (errHandOverId.Contains(evtResultInfo.ID))
                {
                    sortEventResultInfoList.Add(evtResultInfo);
                }
            }

            foreach (var evtResultInfo in eventResultInfoList)
            {
                if (!errCallEventId.Contains(evtResultInfo.ID) && !errHandOverId.Contains(evtResultInfo.ID))
                {
                    sortEventResultInfoList.Add(evtResultInfo);
                }
            }

            eventResultInfoList = sortEventResultInfoList;
        }

        private void SetHasSelectEvent(List<int> eventResultIdList)
        {
            if (eventResultIdList != null)
            {
                foreach (int id in eventResultIdList)
                {
                    checkedEventResultDic[id] = true;
                }
            }
        }

        protected virtual void loadEvent()
        {
            if (eventResultInfoList == null)
            {
                return;
            }

            treeListEvent.BeginUpdate();
            treeListEvent.Nodes.Clear();
            foreach (EventInfo eventInfo in eventResultInfoList)
            {
                if (eventInfo.eventInfo != null && eventInfo.eventInfo.Count > 0)
                {
                    TreeListNode rootNode = treeListEvent.AppendNode(new object[] { eventInfo.Name }, null);
                    rootNode.Tag = eventInfo;
                    createTreeListEvtNodes(eventInfo.eventInfo, rootNode);
                }
                else
                {
                    if (eventInfo != null)
                    {
                        TreeListNode pNode = treeListEvent.AppendNode(new object[] { eventInfo.Name + "#" + eventInfo.ID }, null);
                        pNode.Tag = eventInfo;
                        bool isChecked;
                        if (checkedEventResultDic.TryGetValue(eventInfo.ID, out isChecked))
                        {
                            pNode.Checked = isChecked;
                        }
                    }
                }
            }

            treeListEvent.ForceInitialize();
            treeListEvent.BestFitColumns();
            treeListEvent.EndUpdate();
            MasterCom.Util.DevControlManager.TreeListHelper.ThreeStateControl(treeListEvent);
            treeListEvent.ExpandAll();
        }

        private void createTreeListEvtNodes(List<MasterCom.RAMS.Model.EventInfo> eventInfos, TreeListNode node)
        {
            foreach (EventInfo eventInfo in eventInfos)
            {
                if (eventInfo.eventInfo != null && eventInfo.eventInfo.Count > 0)
                {
                    TreeListNode rootNode = treeListEvent.AppendNode(new object[] { eventInfo.Name }, null);
                    rootNode.Tag = eventInfo;
                    createTreeListEvtNodes(eventInfo.eventInfo, rootNode);
                }
                else
                {
                    if (eventInfo != null)
                    {
                        TreeListNode pNode = treeListEvent.AppendNode(new object[] { eventInfo.Name + "#" + eventInfo.ID }, node);
                        pNode.Tag = eventInfo;
                        bool isChecked;
                        if (checkedEventResultDic.TryGetValue(eventInfo.ID, out isChecked))
                        {
                            pNode.Checked = isChecked;
                        }
                    }
                }
            }
        }


        private void buttonOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

    }
}
