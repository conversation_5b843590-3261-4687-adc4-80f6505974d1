﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.ZTFunc
{
    public class GSMCellWrongDir : TDCellWrongDir
    {
        public GSMCellWrongDir(Cell cell)
            : base(cell)
        {
        }

        public new Cell Cell
        {
            get { return (Cell)cell; }
        }

        public override string CellName
        {
            get
            {
                return Cell.Name;
            }
        }

        public override double Latitude
        {
            get
            {
                return Cell.Latitude;
            }
        }

        public override double Longitude
        {
            get
            {
                return Cell.Longitude;
            }
        }

        public override int LAC
        {
            get
            {
                return Cell.LAC;
            }
        }

        public override int CI
        {
            get
            {
                return Cell.CI;
            }
        }

        public override int FREQ
        {
            get { return Cell.BCCH; }
        }

        public override int CPI
        {
            get { return Cell.BSIC; }
        }

        public override int Direction
        {
            get
            {
                return Cell.Direction;
            }
        }

        public override BackgroundResult ConvertToBackgroundResult()
        {
            BackgroundResult bgResult = new BackgroundResult();
            bgResult.CellType = BackgroundCellType.GSM;
            bgResult.LAC = LAC;
            bgResult.CI = CI;
            bgResult.BCCH = FREQ;
            bgResult.BSIC = CPI;
            bgResult.ISTime = resultShow.istime;
            bgResult.IETime = resultShow.ietime;
            bgResult.LongitudeMid = Longitude;
            bgResult.LatitudeMid = Latitude;
            bgResult.SampleCount = WrongTestPointCount;
            bgResult.AddImageValue(resultShow.GoodTestPointCount);
            bgResult.AddImageValue((float)WrongPercentage);
            return bgResult;
        }

        public override TDCellWrongDir Clone()
        {
            TDCellWrongDir cellWrong = new GSMCellWrongDir(Cell);
            cellWrong.cellWrongBatch = this.cellWrongBatch;
            cellWrong.resultFirstBatch = this.resultFirstBatch;
            cellWrong.resultSecondBatch = this.resultSecondBatch;

            return cellWrong;
        }
    }
}
