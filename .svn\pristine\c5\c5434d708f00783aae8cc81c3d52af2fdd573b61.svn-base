﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.ES.Data;
using MasterCom.Util;

namespace MasterCom.ES.UI
{
    public partial class SelectOwnFuncDlg : BaseFormStyle
    {
        public SelectOwnFuncDlg()
        {
            InitializeComponent();
        }
        internal void FillCommandersDic(Dictionary<string,ESOwnFuncCommander> cmdDic)
        {
            cbxOwnFunc.Items.Clear();
            foreach(ESOwnFuncCommander cmd in cmdDic.Values)
            {
                cbxOwnFunc.Items.Add(cmd);
            }
        }
        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            if(cbxOwnFunc.SelectedItem!=null)
            {
                this.DialogResult = DialogResult.OK;
            }
            else
            {
                //do nothing
            }
            
        }

        private void btnEditFunc_Click(object sender, EventArgs e)
        {
            ESCommanderEditorDlg dlgEditor = new ESCommanderEditorDlg();
            dlgEditor.InitShow(DTDataProvider.GetDTProviderInstance().CommanderDic);
            dlgEditor.ShowDialog(this);
            Dictionary<string, ESOwnFuncCommander> cmdDic = dlgEditor.GetCommanderDic();
            DTDataProvider.GetDTProviderInstance().CommanderDic = cmdDic;
            FillCommandersDic(cmdDic);
        }

        internal ESOwnFuncCommander GetSelectedOwnFuncCmd()
        {
            return cbxOwnFunc.SelectedItem as ESOwnFuncCommander;
        }
    }
}