﻿using MasterCom.MTGis;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    /// <summary>
    /// 重要路段基础信息
    /// </summary>
    public class RoadLabelBaseInfo
    {
        public int RoadLabelId { get; set; }

        private string roadLabel = "";//道路标签名称（路段名称）
        public string RoadLabel
        {
            get
            {
                if (!string.IsNullOrEmpty(roadLabel))
                {
                    return roadLabel.Trim();
                }
                return "";
            }
            set { roadLabel = value; }
        }
        public string RoadName { get; set; }//道路名称
        
        public List<string> MainLineNameList { get; set; } = new List<string>();//干线名称
        public string CountyName { get; set; }//区县名称
        public float Length_M { get; set; }
        public float Length_KM { get { return Length_M / 1000; } }
        
        public List<RoadPartAreaBaseInfo> RoadPartAreaInfoList { get; set; } = new List<RoadPartAreaBaseInfo>();
    }

    /// <summary>
    /// 路段分段区域信息
    /// </summary>
    public class RoadPartAreaBaseInfo
    {
        public int RoadPartAreaId { get; set; }
        public string RoadLabel { get; set; }
        public int RoadLabelId { get; set; }
        public string RoadName { get; set; }
        public double BrLongitude { get; set; }
        public double BrLatitude { get; set; }
        public double TlLongitude { get; set; }
        public double TlLatitude { get; set; }
        public double CenterLongitude { get; set; }
        public double CenterLatitude { get; set; }
        public float Length_M { get; set; }
        public float Length_KM { get { return Length_M / 1000; } }

        public List<DbPoint> DbPointList { get; set; } = new List<DbPoint>();
    }

    /// <summary>
    /// 测试异常信息（根据事件预处理信息）
    /// </summary>
    public class RoadLabelEvtAnaInfo
    {
        private string roadLabel = "";//道路标签名称（路段名称）
        public string RoadLabel
        {
            get
            {
                if (!string.IsNullOrEmpty(roadLabel))
                {
                    return roadLabel.Trim();
                }
                return "";
            }
            set { roadLabel = value; }
        }
        public string CountyName { get; set; }
        public string NetTypeStr { get; set; }
        public string ReasonType { get; set; }
        public float ProblemPer { get; set; }
        public float RoadLength_M { get; set; }
        public float ProblemEvtPer_2G { get; set; }
        public float ProblemEvtPer_4G { get; set; }
    }

    /// <summary>
    /// 小区退服信息
    /// </summary>
    public class CellExitInfo
    {
        private string cellToken = "";
        public string CellToken
        {
            get
            {
                if (!string.IsNullOrEmpty(cellToken))
                {
                    return cellToken.Trim();
                }
                return "";
            }
            set { cellToken = value; }
        }

        public string NetType { get; set; }
        public DateTime ExitTime { get; set; }
        public string ProblemType { get; set; }
        public string ProblemReason { get; set; }

        public override string ToString()
        {
            return string.Format("退服时间:{0},故障类型:{1},故障原因:{2}", ExitTime, ProblemType, ProblemReason);
        }
    }

}
