﻿using MasterCom.RAMS.Func;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NRCellOccupySameCarTestDlg : BaseDialog
    {
        public NRCellOccupySameCarTestDlg()
        {
            InitializeComponent();
        }

        public void SetCondition(NRCellOccupySameCarTestCondition cond)
        {
            if (cond.Type == NRCellOccupySameCarTestType.DlAndUl)
            {
                cbxVsType.SelectedIndex = 0;
                panelTimeSpan.Visible = false;
            }
            else
            {
                cbxVsType.SelectedIndex = 1;
                panelTimeSpan.Visible = true;
                numTimeSpan.Value = cond.TimeSpanVolteAndDl;
            }
        }

        public NRCellOccupySameCarTestCondition GetCondtion()
        {
            NRCellOccupySameCarTestCondition cond = new NRCellOccupySameCarTestCondition();
            cond.Type = (NRCellOccupySameCarTestType)cbxVsType.SelectedIndex;
            cond.TimeSpanVolteAndDl = (int)numTimeSpan.Value;
            return cond;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
        }

        private void cbxVsType_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (cbxVsType.SelectedIndex == 0)
            {
                panelTimeSpan.Visible = false;
            }
            else
            {
                panelTimeSpan.Visible = true;
            }
        }
    }

    public class NRCellOccupySameCarTestCondition
    {
        public NRCellOccupySameCarTestType Type { get; set; } = NRCellOccupySameCarTestType.DlAndUl;
        public int TimeSpanVolteAndDl { get; set; } = 60;
    }

    public enum NRCellOccupySameCarTestType
    {
        DlAndUl = 0,
        VolteAndDl = 1
    }
}
