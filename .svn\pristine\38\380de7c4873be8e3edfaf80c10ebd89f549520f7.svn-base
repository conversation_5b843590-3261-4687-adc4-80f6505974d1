﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.Util;
using MasterCom.RAMS.Stat;
using MasterCom.RAMS.Stat.Data;
using MasterCom.MControls;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class CellAssociationKPIResultForm : MinCloseForm
    {
        public CellAssociationKPIResultForm(MainModel mainModel) : base(mainModel)
        {
            InitializeComponent();
            miExportExcel.Click += MiExportExcel_Click;
            miSql.Click += MiSql_Click;
            gridView1.DoubleClick += GridView_DoubleClick;
            gridView1.CustomDrawCell += GridView_CustomDrawCell;
        }

        public void FillData(CellAssociationKPIView view, string sqlText)
        {
            this.sqlText = sqlText;
            this.view = view;
            this.gridControl1.DataSource = this.view.ShowTable;
            this.gridControl1.RefreshDataSource();

            foreach (DevExpress.XtraGrid.Columns.GridColumn gc in gridView1.Columns)
            {
                if (gc.ColumnType == typeof(DateTime))
                {
                    gc.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
                    gc.DisplayFormat.FormatString = "yyyy-MM-dd HH:mm";
                    continue;
                }
                else if (!view.ColumnFormatMap.ContainsKey(gc.FieldName))
                {
                    continue;
                }

                int lstIdx = view.ColumnFormatMap[gc.FieldName].LastIndexOf('$');
                string fmt = view.ColumnFormatMap[gc.FieldName].Substring(lstIdx + 1);
                if (fmt.StartsWith("F") || fmt.StartsWith("P"))
                {
                    gc.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
                    gc.DisplayFormat.FormatString = fmt.Replace(",", "");
                }
            }
        }

        private void MiExportExcel_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(this.gridView1);
        }

        private void MiSql_Click(object sender, EventArgs e)
        {
            TextOutputForm outForm = new TextOutputForm();
            outForm.Text = "指标查询语句";
            outForm.ShowText = this.sqlText;
            outForm.ShowDialog();
        }

        private void GridView_DoubleClick(object sender, EventArgs e)
        {
            DataRow dr = gridView1.GetDataRow(gridView1.GetSelectedRows()[0]);
            if (dr == null)
            {
                return;
            }
            string cellName = dr[0].ToString();

            CellAssociationData cellData = null;
            if (!view.CellDataDic.TryGetValue(cellName, out cellData))
            {
                return;
            }
            ICell cell = cellData.Cell;
            MainModel.MainForm.GetMapForm().GoToView(cell.Longitude, cell.Latitude);
        }

        private void GridView_CustomDrawCell(object sender, DevExpress.XtraGrid.Views.Base.RowCellCustomDrawEventArgs e)
        {
            if (!this.view.ColumnInfoMap.ContainsKey(e.Column.Caption))
            {
                return;
            }

            List<ColorRange> colorRanges = this.view.ColumnInfoMap[e.Column.Caption] as List<ColorRange>;
            if (colorRanges == null || colorRanges.Count == 0)
            {
                return;
            }

            double value;
            if (e.CellValue == null || !double.TryParse(e.CellValue.ToString(), out value))
            {
                return;
            }

            foreach (ColorRange cr in colorRanges)
            {
                if (cr.minValue <= value && value < cr.maxValue)
                {
                    e.Appearance.BackColor = cr.color;
                    break;
                }
            }
        }

        private CellAssociationKPIView view;

        private string sqlText;
    }

    public class CellAssociationKPIView
    {
        public DataTable ShowTable
        {
            get;
            set;
        }

        public Dictionary<string, object> ColumnInfoMap
        {
            get;
            set;
        }

        public Dictionary<string, string> ColumnFormatMap
        {
            get;
            set;
        }

        public Dictionary<string, CellAssociationData> CellDataDic
        {
            get;
            set;
        }
    }
}
