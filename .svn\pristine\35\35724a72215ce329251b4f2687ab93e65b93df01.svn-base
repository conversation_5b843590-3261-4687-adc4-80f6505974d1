﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.Util;

namespace MasterCom.RAMS.Model
{
    public interface ICell : IID
    {
        double Longitude
        {
            get;
        }

        double Latitude
        {
            get;
        }
        string Name
        {
            get;
            set;
        }
        short Direction
        {
            get;
        }
        string Token
        {
            get;
        }

        string Code
        {
            get;
        }

        ISite Site
        {
            get;
        }

        double EndPointLatitude { get; }

        double EndPointLongitude { get; }

        int Altitude { get; }
    }

    public class UnknowCell : ICell
    {
        public override string ToString()
        {
            return Name;
        }
        public override int GetHashCode()
        {
            return (Token + "@" + this.GetType().FullName).GetHashCode();
        }
        public override bool Equals(object obj)
        {
            UnknowCell other = obj as UnknowCell;
            if (other == null)
            {
                return false;
            }
            return other.Name == this.Name && other.Token == this.Token;
        }
        public UnknowCell(string token)
        {
            this.token = token;
        }
        public UnknowCell(int lac, long ci)
        {
            this.LAC = lac;
            this.CI = ci;
            this.token = string.Format("{0}_{1}", lac, ci);
        }
        public double Longitude
        {
            get { return double.NaN; }
        }

        public double Latitude
        {
            get { return double.NaN; }
        }

        public int LAC { get; set; }

        public long CI { get; set; }

        public int Altitude { get { return -1; } }


        private string name = null;
        public string Name
        {
            get
            {
                if (string.IsNullOrEmpty(name))
                {
                    name = string.Format("未知小区 {0}", token);
                }
                return name;
            }
            set
            {
                if (value!=null)
                {
                    name = value; 
                }
            }
        }

        public short Direction
        {
            get { return -1; }
        }

        protected string token;
        public string Token
        {
            get { return token; }
        }
        
        public int ID { get; set; } = -1;

        public double EndPointLatitude
        {
            get
            {
               return 0;
            }
        }

        public double EndPointLongitude
        {
            get
            {
                return 0;
            }
        }

        public string Code
        {
            get { return token; }
        }
        public ISite Site
        {
            get { return null; }
        }
    }
}
