﻿using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Text;
using System.Windows.Forms;
using System.Xml;
using System.Collections;
using MasterCom.RAMS.Model.Interface;
using MapWinGIS;
using Shape = MapWinGIS.Shape;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc.ZTWirelessNetTest
{
    #region 条件配置
    public class WirelessNetTestCond : ConfigDataInfo
    {
        public WirelessNetTestCond()
        {
            SetCondition(DateTime.Now.AddMonths(-1), DateTime.Now);
            SceneExcelPath = $@"{WirelessNetTestHelper.BasicPath}\场景.xlsx";
            ShapePath = $@"{WirelessNetTestHelper.BasicPath}\MAP";

            if (!Directory.Exists(ShapePath))
            {
                Directory.CreateDirectory(ShapePath);
            }

            //ProjectDic = new Dictionary<WirelessNetTestProjectType, Project>();
            //ProjectDic.Add(WirelessNetTestProjectType.城区, new Project(new List<int>() { 1 }));
            //ProjectDic.Add(WirelessNetTestProjectType.区县, new Project(new List<int>() { 2 }));
            //ProjectDic.Add(WirelessNetTestProjectType.高铁, new Project(new List<int>() { 3 }));
            //ProjectDic.Add(WirelessNetTestProjectType.地铁, new Project(new List<int>() { 4 }));
            //ProjectDic.Add(WirelessNetTestProjectType.高速, new Project(new List<int>() { 5 }));
            //ProjectDic.Add(WirelessNetTestProjectType.机场, new Project(new List<int>() { 6 }));

            ProjectDic = InitProjectTypeDic<Project>();
        }

        //测试时段
        public TimePeriod TestPeriod { get; set; }

        public string SceneExcelPath { get; set; }
        public string ShapePath { get; set; }

        //public string CityProject { get; set; }
        //public string CountyProject { get; set; }
        //public string RailwayProject { get; set; }
        //public string SubwayProject { get; set; }
        //public string SpeedwayProject { get; set; }
        //public string AirportProject { get; set; }

        public bool IsStatCU { get; private set; }
        public List<int> Carrier { get; private set; } = new List<int>();

        public List<int> DistrictIDs { get; set; } = new List<int>();
        //public Dictionary<WirelessNetTestProjectType, List<int>> ProjectDic { get; set; }

        public Dictionary<WirelessNetTestProjectType, Project> ProjectDic { get; set; }

        public void SetCondition(DateTime beginTime, DateTime endTime)
        {
            TestPeriod = new TimePeriod(beginTime, endTime);
        }

        public void Calculate(CategoryEnum projectCate)
        {
            foreach (var project in ProjectDic.Values)
            {
                project.Calculate(projectCate);
            }

            //CityProject = getProjectsDesc(WirelessNetTestProjectType.城区, projectCate);
            //CountyProject = getProjectsDesc(WirelessNetTestProjectType.区县, projectCate);
            //RailwayProject = getProjectsDesc(WirelessNetTestProjectType.高铁, projectCate);
            //SubwayProject = getProjectsDesc(WirelessNetTestProjectType.地铁, projectCate);
            //SpeedwayProject = getProjectsDesc(WirelessNetTestProjectType.高速, projectCate);
            //AirportProject = getProjectsDesc(WirelessNetTestProjectType.机场, projectCate);
        }

        public void SetCarrier(bool isStatCU)
        {
            IsStatCU = isStatCU;
            Carrier = new List<int>() { 1, 3 };
            if (IsStatCU)
            {
                Carrier.Add(2);
            }
        }

        //protected string getProjectsDesc(WirelessNetTestProjectType type, CategoryEnum projectCate)
        //{
        //    if (!ProjectDic.TryGetValue(type, out var projects))
        //    {
        //        return "";
        //    }

        //    StringBuilder desc = new StringBuilder();
        //    foreach (var project in projects)
        //    {
        //        var name = projectCate[project].Name;
        //        desc.Append($"{name},");
        //    }
        //    return desc.ToString().TrimEnd(',');
        //}

        //public string GetProjectsDesc(WirelessNetTestProjectType type)
        //{
        //    CategoryEnum projectCate = (CategoryEnum)CategoryManager.GetInstance()["Project"];
        //    return getProjectsDesc(type, projectCate);
        //}

        //public List<WirelessNetTestProjectType> GetProjectType(int projectID)
        //{
        //    var typeList = new List<WirelessNetTestProjectType>();
        //    foreach (var project in ProjectDic.Values)
        //    {
        //        if (project.ProjectList.Contains(projectID))
        //        {
        //            typeList.Add(project.Type);
        //        }
        //    }

        //    return typeList;
        //}

        #region 获取每个测试类的项目类型
        public WirelessNetTestProjectType GetProjectType(int projectID)
        {
            foreach (var project in ProjectDic.Values)
            {
                //如果多个测试类型的项目有重复,目前暂不处理,只返回第一个
                if (project.ProjectList.Contains(projectID))
                {
                    return project.Type;
                }
            }

            return WirelessNetTestProjectType.UNKNOWN;
        }

        public List<int> GetAllProjects()
        {
            Dictionary<int,int> allProjects = new Dictionary<int, int>();
            foreach (var projects in ProjectDic.Values)
            {
                foreach (var project in projects.ProjectList)
                {
                    allProjects[project] = project;
                }
            }
            return new List<int>(allProjects.Values);
        }

        public Dictionary<WirelessNetTestProjectType, T> InitProjectTypeDic<T>()
            where T : new() 
        {
            var dic = new Dictionary<WirelessNetTestProjectType, T>();
            dic.Add(WirelessNetTestProjectType.城区, new T());
            dic.Add(WirelessNetTestProjectType.区县, new T());
            dic.Add(WirelessNetTestProjectType.高铁, new T());
            dic.Add(WirelessNetTestProjectType.地铁, new T());
            dic.Add(WirelessNetTestProjectType.高速, new T());
            dic.Add(WirelessNetTestProjectType.机场, new T());
            return dic;
        }

        public Dictionary<WirelessNetTestProjectType, List<int>> GetProjects()
        {
            var dic = new Dictionary<WirelessNetTestProjectType, List<int>>();
            foreach (var project in ProjectDic)
            {
                dic.Add(project.Key, project.Value.ProjectList);
            }
            return dic;
        }

        public class Project
        {
            public Project()
            {
                ProjectList = new List<int>() { 1 };
            }

            public Project(WirelessNetTestProjectType type, List<int> projectList)
            {
                Type = type;
                ProjectList = projectList;
                GetProjectsDesc();
            }

            public WirelessNetTestProjectType Type { get; set; }
            public List<int> ProjectList { get; set; }
            public string ProjectDesc { get; set; }

            public void Calculate(CategoryEnum projectCate)
            {
                ProjectDesc = getProjectsDesc(projectCate);
            }

            public string GetProjectsDesc()
            {
                CategoryEnum projectCate = (CategoryEnum)CategoryManager.GetInstance()["Project"];
                ProjectDesc = getProjectsDesc(projectCate);
                return ProjectDesc;
            }

            protected string getProjectsDesc(CategoryEnum projectCate)
            {
                StringBuilder desc = new StringBuilder();
                foreach (var project in ProjectList)
                {
                    var name = projectCate[project].Name;
                    desc.Append($"{name},");
                }
                return desc.ToString().TrimEnd(',');
            }
        }
        #endregion

        #region 区分文件业务,暂时先写死
        public Dictionary<FormulaType, List<int>> FormulaTypeDic { get; set; } = new Dictionary<FormulaType, List<int>>()
        {
            { FormulaType.数据,new List<int>(){ 34, 41, 46, 47, 57, 58, 60, 61, 63, 64 } },
            { FormulaType.语音,new List<int>(){ 1, 6, 10, 33, 43, 45, 49, 51, 52, 59, 62, 65, 66, 67 } },
        };

        public FormulaType GetFileFormulaType(int service)
        {
            foreach (var type in FormulaTypeDic)
            {
                if (type.Value.Contains(service))
                {
                    return type.Key;
                }
            }
            return FormulaType.全部;
        }
        #endregion
    }

    /// <summary>
    /// 读取该功能的条件配置文件
    /// </summary>
    public class WirelessNetTestConfig : ConfigHelper<WirelessNetTestCond>
    {
        private static WirelessNetTestConfig instance = null;
        public static WirelessNetTestConfig Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new WirelessNetTestConfig();
                }
                return instance;
            }
        }

        public override string ConfigPath { get; } = $@"{WirelessNetTestHelper.BasicPath}\WirelessNetTestConfig.xml";
        public override string LogPath { get; } = @"\BackGroundLog\WirelessNetTest\";
        public override string LogName { get; } = "-无线网络测速.txt";

        protected override void loadConfig(XmlConfigFile xcfg, WirelessNetTestCond configInfo)
        {
            try
            {
                XmlElement config = xcfg.GetConfig("Configs");
                configInfo.SceneExcelPath = getValidPath(xcfg, config, "SceneExcelPath");
                configInfo.ShapePath = getValidDirectory(xcfg, config, "ShapePath");

                DateTime sTime = getValidData(xcfg, config, "StartTime", DateTime.Now.AddMonths(-1));
                DateTime eTime = getValidData(xcfg, config, "EndTime", DateTime.Now);
                configInfo.SetCondition(sTime, eTime);
                bool isStatCU = getValidData(xcfg, config, "IsStatCU", false);
                configInfo.SetCarrier(isStatCU);

                var districtIDs = xcfg.GetItemValue(config, "Districts") as List<object>;
                if (districtIDs != null)
                {
                    foreach (var o in districtIDs)
                    {
                        configInfo.DistrictIDs.Add((int)o);
                    }
                }

                loadProjects(xcfg, configInfo, config);
            }
            catch (Exception ex)
            {
                ErrMsg = $"加载配置出错:{ex.Message}";
            }
        }

        private void loadProjects(XmlConfigFile xcfg, WirelessNetTestCond configInfo, XmlElement config)
        {
            CategoryEnum projectCate = (CategoryEnum)CategoryManager.GetInstance()["Project"];
            var dic = xcfg.GetItemValue(config, "ProjectDic") as Dictionary<string, object>;
            //configInfo.ProjectDic = new Dictionary<WirelessNetTestProjectType, WirelessNetTestCond.Project>();
            configInfo.ProjectDic = configInfo.InitProjectTypeDic<WirelessNetTestCond.Project>();
            foreach (var item in dic)
            {
                var curProjects = (List<object>)item.Value;
                var type = (WirelessNetTestProjectType)Enum.Parse(typeof(WirelessNetTestProjectType), item.Key);

                var projectList = new List<int>();
                foreach (var projectID in curProjects)
                {
                    int iProjectID = (int)projectID;
                    if (projectCate[iProjectID] != null)
                    {
                        projectList.Add(iProjectID);
                    }
                }
                var project = new WirelessNetTestCond.Project(type, projectList);
                configInfo.ProjectDic[type] = project;
            }
            //configInfo.Calculate(projectCate);
        }

        public override void SaveConfig(WirelessNetTestCond configInfo)
        {
            try
            {
                var newConfig = new XmlConfigFile();
                XmlElement cfg = newConfig.AddConfig("Configs");
                newConfig.AddItem(cfg, "SceneExcelPath", configInfo.SceneExcelPath);
                newConfig.AddItem(cfg, "ShapePath", configInfo.ShapePath);
                newConfig.AddItem(cfg, "StartTime", configInfo.TestPeriod.BeginTime);
                newConfig.AddItem(cfg, "EndTime", configInfo.TestPeriod.EndTime);
                newConfig.AddItem(cfg, "IsStatCU", configInfo.IsStatCU);
                newConfig.AddItem(cfg, "Districts", configInfo.DistrictIDs);
                newConfig.AddItem(cfg, "ProjectDic", configInfo.GetProjects());
                newConfig.Save(ConfigPath);
            }
            catch (Exception ex)
            {
                ErrMsg = $"保存配置出错:{ex.Message}";
            }
        }

        public override void DoAfterFstSave(WirelessNetTestCond configInfo)
        {
            CategoryEnum projectCate = (CategoryEnum)CategoryManager.GetInstance()["Project"];
            configInfo.Calculate(projectCate);

            configInfo.SetCarrier(configInfo.IsStatCU);
        }
    }
    #endregion
}
