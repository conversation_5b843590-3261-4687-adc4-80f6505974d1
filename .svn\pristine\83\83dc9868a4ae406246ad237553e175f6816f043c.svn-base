﻿namespace MasterCom.ES.ColorManager
{
    partial class SelectEventForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.panel1 = new System.Windows.Forms.Panel();
            this.cbxFilterOffV9 = new System.Windows.Forms.CheckBox();
            this.showConfig_b = new Glass.GlassButton();
            this.ok_b = new System.Windows.Forms.Button();
            this.button1 = new System.Windows.Forms.Button();
            this.listItem2 = new MasterCom.Util.UiEx.ListItem();
            this.category_olv = new BrightIdeasSoftware.FastObjectListView();
            this.olvColumn1 = new BrightIdeasSoftware.OLVColumn();
            this.olvColumn2 = new BrightIdeasSoftware.OLVColumn();
            this.panel1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.category_olv)).BeginInit();
            this.SuspendLayout();
            // 
            // panel1
            // 
            this.panel1.Controls.Add(this.cbxFilterOffV9);
            this.panel1.Controls.Add(this.showConfig_b);
            this.panel1.Controls.Add(this.ok_b);
            this.panel1.Controls.Add(this.button1);
            this.panel1.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.panel1.Location = new System.Drawing.Point(0, 260);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(454, 23);
            this.panel1.TabIndex = 1;
            // 
            // cbxFilterOffV9
            // 
            this.cbxFilterOffV9.AutoSize = true;
            this.cbxFilterOffV9.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(192)))));
            this.cbxFilterOffV9.Checked = true;
            this.cbxFilterOffV9.CheckState = System.Windows.Forms.CheckState.Checked;
            this.cbxFilterOffV9.Location = new System.Drawing.Point(82, 4);
            this.cbxFilterOffV9.Name = "cbxFilterOffV9";
            this.cbxFilterOffV9.Size = new System.Drawing.Size(132, 16);
            this.cbxFilterOffV9.TabIndex = 10;
            this.cbxFilterOffV9.Text = "不显示过滤掉的事件";
            this.cbxFilterOffV9.UseVisualStyleBackColor = false;
            // 
            // showConfig_b
            // 
            this.showConfig_b.BackColor = System.Drawing.Color.WhiteSmoke;
            this.showConfig_b.ForeColor = System.Drawing.Color.Black;
            this.showConfig_b.Location = new System.Drawing.Point(3, 0);
            this.showConfig_b.Name = "showConfig_b";
            this.showConfig_b.Size = new System.Drawing.Size(75, 23);
            this.showConfig_b.TabIndex = 3;
            this.showConfig_b.Text = "查看配置";
            this.showConfig_b.Click += new System.EventHandler(this.showConfig_b_Click);
            // 
            // ok_b
            // 
            this.ok_b.DialogResult = System.Windows.Forms.DialogResult.OK;
            this.ok_b.Dock = System.Windows.Forms.DockStyle.Right;
            this.ok_b.Enabled = false;
            this.ok_b.Location = new System.Drawing.Point(304, 0);
            this.ok_b.Name = "ok_b";
            this.ok_b.Size = new System.Drawing.Size(75, 23);
            this.ok_b.TabIndex = 1;
            this.ok_b.Text = "确定";
            this.ok_b.UseVisualStyleBackColor = true;
            // 
            // button1
            // 
            this.button1.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.button1.Dock = System.Windows.Forms.DockStyle.Right;
            this.button1.Location = new System.Drawing.Point(379, 0);
            this.button1.Name = "button1";
            this.button1.Size = new System.Drawing.Size(75, 23);
            this.button1.TabIndex = 0;
            this.button1.Text = "取消";
            this.button1.UseVisualStyleBackColor = true;
            // 
            // listItem2
            // 
            this.listItem2.BackColor = System.Drawing.SystemColors.Window;
            this.listItem2.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.listItem2.ForeColor = System.Drawing.SystemColors.WindowText;
            this.listItem2.Text = "listItem2";
            // 
            // category_olv
            // 
            this.category_olv.AllColumns.Add(this.olvColumn1);
            this.category_olv.AllColumns.Add(this.olvColumn2);
            this.category_olv.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumn1,
            this.olvColumn2});
            this.category_olv.Cursor = System.Windows.Forms.Cursors.Default;
            this.category_olv.Dock = System.Windows.Forms.DockStyle.Fill;
            this.category_olv.FullRowSelect = true;
            this.category_olv.GridLines = true;
            this.category_olv.IsNeedShowOverlay = false;
            this.category_olv.Location = new System.Drawing.Point(0, 0);
            this.category_olv.Name = "category_olv";
            this.category_olv.OwnerDraw = true;
            this.category_olv.RowHeight = 25;
            this.category_olv.ShowGroups = false;
            this.category_olv.Size = new System.Drawing.Size(454, 260);
            this.category_olv.Sorting = System.Windows.Forms.SortOrder.Descending;
            this.category_olv.TabIndex = 2;
            this.category_olv.UseCompatibleStateImageBehavior = false;
            this.category_olv.UseHotItem = true;
            this.category_olv.UseTranslucentHotItem = true;
            this.category_olv.UseTranslucentSelection = true;
            this.category_olv.View = System.Windows.Forms.View.Details;
            this.category_olv.VirtualMode = true;
            this.category_olv.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.objectListView_MouseDoubleClick);
            this.category_olv.SelectedIndexChanged += new System.EventHandler(this.objectListView_SelectedIndexChanged);
            // 
            // olvColumn1
            // 
            this.olvColumn1.AspectName = "Name";
            this.olvColumn1.HeaderFont = null;
            this.olvColumn1.Text = "名称";
            this.olvColumn1.Width = 100;
            // 
            // olvColumn2
            // 
            this.olvColumn2.AspectName = "Des";
            this.olvColumn2.HeaderFont = null;
            this.olvColumn2.Text = "描述";
            this.olvColumn2.Width = 350;
            // 
            // SelectEventForm
            // 
            this.AcceptButton = this.ok_b;
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(454, 283);
            this.Controls.Add(this.category_olv);
            this.Controls.Add(this.panel1);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.SizableToolWindow;
            this.Name = "SelectEventForm";
            this.ShowIcon = false;
            this.ShowInTaskbar = false;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "选择网络问题";
            this.panel1.ResumeLayout(false);
            this.panel1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.category_olv)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.Panel panel1;
        private System.Windows.Forms.Button ok_b;
        private System.Windows.Forms.Button button1;
        private BrightIdeasSoftware.FastObjectListView category_olv;
        private BrightIdeasSoftware.OLVColumn olvColumn1;
        private BrightIdeasSoftware.OLVColumn olvColumn2;
        private MasterCom.Util.UiEx.ListItem listItem2;
        private Glass.GlassButton showConfig_b;
        private System.Windows.Forms.CheckBox cbxFilterOffV9;
    }
}