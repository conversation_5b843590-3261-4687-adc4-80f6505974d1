﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class WeakSINRRoadForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.gridControl = new DevExpress.XtraGrid.GridControl();
            this.contextMenuStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExport2Xls = new System.Windows.Forms.ToolStripMenuItem();
            this.gv = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnCity = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnGrid = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn18 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn26 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn15 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn16 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnRoad = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn25 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn14 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn6 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn7 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn8 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn9 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn10 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn11 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn17 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn19 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn20 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn21 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn22 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn23 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn24 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn12 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn13 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl)).BeginInit();
            this.contextMenuStrip.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gv)).BeginInit();
            this.SuspendLayout();
            // 
            // gridControl
            // 
            this.gridControl.ContextMenuStrip = this.contextMenuStrip;
            this.gridControl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControl.Location = new System.Drawing.Point(0, 0);
            this.gridControl.LookAndFeel.SkinName = "Office 2010 Blue";
            this.gridControl.MainView = this.gv;
            this.gridControl.Name = "gridControl";
            this.gridControl.Size = new System.Drawing.Size(1191, 562);
            this.gridControl.TabIndex = 3;
            this.gridControl.UseEmbeddedNavigator = true;
            this.gridControl.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gv});
            // 
            // contextMenuStrip
            // 
            this.contextMenuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExport2Xls});
            this.contextMenuStrip.Name = "contextMenuStrip";
            this.contextMenuStrip.Size = new System.Drawing.Size(139, 26);
            // 
            // miExport2Xls
            // 
            this.miExport2Xls.Name = "miExport2Xls";
            this.miExport2Xls.Size = new System.Drawing.Size(138, 22);
            this.miExport2Xls.Text = "导出Excel...";
            this.miExport2Xls.Click += new System.EventHandler(this.miExport2Xls_Click);
            // 
            // gv
            // 
            this.gv.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn1,
            this.gridColumnCity,
            this.gridColumnGrid,
            this.gridColumn18,
            this.gridColumn26,
            this.gridColumn15,
            this.gridColumn16,
            this.gridColumnRoad,
            this.gridColumn25,
            this.gridColumn3,
            this.gridColumn4,
            this.gridColumn5,
            this.gridColumn14,
            this.gridColumn6,
            this.gridColumn7,
            this.gridColumn8,
            this.gridColumn9,
            this.gridColumn10,
            this.gridColumn11,
            this.gridColumn17,
            this.gridColumn19,
            this.gridColumn20,
            this.gridColumn21,
            this.gridColumn22,
            this.gridColumn23,
            this.gridColumn24,
            this.gridColumn12,
            this.gridColumn13,
            this.gridColumn2});
            this.gv.GridControl = this.gridControl;
            this.gv.GroupSummary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridGroupSummaryItem(DevExpress.Data.SummaryItemType.Count, "Name", null, "")});
            this.gv.Name = "gv";
            this.gv.OptionsBehavior.Editable = false;
            this.gv.OptionsDetail.EnableMasterViewMode = false;
            this.gv.OptionsDetail.ShowDetailTabs = false;
            this.gv.OptionsView.ColumnAutoWidth = false;
            this.gv.DoubleClick += new System.EventHandler(this.gv_DoubleClick);
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "序号";
            this.gridColumn1.FieldName = "SN";
            this.gridColumn1.Fixed = DevExpress.XtraGrid.Columns.FixedStyle.Left;
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.Visible = true;
            this.gridColumn1.VisibleIndex = 0;
            // 
            // gridColumnCity
            // 
            this.gridColumnCity.Caption = "地市";
            this.gridColumnCity.FieldName = "CityName";
            this.gridColumnCity.Fixed = DevExpress.XtraGrid.Columns.FixedStyle.Left;
            this.gridColumnCity.Name = "gridColumnCity";
            this.gridColumnCity.Visible = true;
            this.gridColumnCity.VisibleIndex = 1;
            // 
            // gridColumnGrid
            // 
            this.gridColumnGrid.Caption = "网格名称";
            this.gridColumnGrid.FieldName = "GridName";
            this.gridColumnGrid.Fixed = DevExpress.XtraGrid.Columns.FixedStyle.Left;
            this.gridColumnGrid.Name = "gridColumnGrid";
            this.gridColumnGrid.Visible = true;
            this.gridColumnGrid.VisibleIndex = 2;
            // 
            // gridColumn18
            // 
            this.gridColumn18.Caption = "文件名";
            this.gridColumn18.FieldName = "FileName";
            this.gridColumn18.Fixed = DevExpress.XtraGrid.Columns.FixedStyle.Left;
            this.gridColumn18.Name = "gridColumn18";
            this.gridColumn18.Visible = true;
            this.gridColumn18.VisibleIndex = 3;
            this.gridColumn18.Width = 220;
            // 
            // gridColumn26
            // 
            this.gridColumn26.Caption = "开始时间";
            this.gridColumn26.FieldName = "StartTime";
            this.gridColumn26.Fixed = DevExpress.XtraGrid.Columns.FixedStyle.Left;
            this.gridColumn26.Name = "gridColumn26";
            this.gridColumn26.Visible = true;
            this.gridColumn26.VisibleIndex = 4;
            this.gridColumn26.Width = 135;
            // 
            // gridColumn15
            // 
            this.gridColumn15.Caption = "中心经度";
            this.gridColumn15.FieldName = "MidLng";
            this.gridColumn15.Fixed = DevExpress.XtraGrid.Columns.FixedStyle.Left;
            this.gridColumn15.Name = "gridColumn15";
            this.gridColumn15.Visible = true;
            this.gridColumn15.VisibleIndex = 5;
            this.gridColumn15.Width = 90;
            // 
            // gridColumn16
            // 
            this.gridColumn16.Caption = "中心纬度";
            this.gridColumn16.FieldName = "MidLat";
            this.gridColumn16.Fixed = DevExpress.XtraGrid.Columns.FixedStyle.Left;
            this.gridColumn16.Name = "gridColumn16";
            this.gridColumn16.Visible = true;
            this.gridColumn16.VisibleIndex = 6;
            this.gridColumn16.Width = 90;
            // 
            // gridColumnRoad
            // 
            this.gridColumnRoad.Caption = "道路";
            this.gridColumnRoad.FieldName = "RoadName";
            this.gridColumnRoad.Name = "gridColumnRoad";
            this.gridColumnRoad.Visible = true;
            this.gridColumnRoad.VisibleIndex = 7;
            // 
            // gridColumn25
            // 
            this.gridColumn25.Caption = "质差点占比(%)";
            this.gridColumn25.FieldName = "WeakPercent";
            this.gridColumn25.Name = "gridColumn25";
            this.gridColumn25.Visible = true;
            this.gridColumn25.VisibleIndex = 8;
            this.gridColumn25.Width = 100;
            // 
            // gridColumn3
            // 
            this.gridColumn3.Caption = "持续距离(米)";
            this.gridColumn3.FieldName = "Distance";
            this.gridColumn3.Name = "gridColumn3";
            this.gridColumn3.Visible = true;
            this.gridColumn3.VisibleIndex = 9;
            this.gridColumn3.Width = 90;
            // 
            // gridColumn4
            // 
            this.gridColumn4.Caption = "持续时间(秒)";
            this.gridColumn4.FieldName = "Second";
            this.gridColumn4.Name = "gridColumn4";
            this.gridColumn4.Visible = true;
            this.gridColumn4.VisibleIndex = 10;
            this.gridColumn4.Width = 90;
            // 
            // gridColumn5
            // 
            this.gridColumn5.Caption = "采样点个数";
            this.gridColumn5.FieldName = "TestPointCount";
            this.gridColumn5.Name = "gridColumn5";
            this.gridColumn5.Visible = true;
            this.gridColumn5.VisibleIndex = 11;
            this.gridColumn5.Width = 80;
            // 
            // gridColumn14
            // 
            this.gridColumn14.Caption = "FDD采样点占比(%)";
            this.gridColumn14.FieldName = "FDDPointPercent";
            this.gridColumn14.Name = "gridColumn14";
            this.gridColumn14.Visible = true;
            this.gridColumn14.VisibleIndex = 12;
            this.gridColumn14.Width = 130;
            // 
            // gridColumn6
            // 
            this.gridColumn6.Caption = "SINR最大值";
            this.gridColumn6.FieldName = "MaxSINR";
            this.gridColumn6.Name = "gridColumn6";
            this.gridColumn6.Visible = true;
            this.gridColumn6.VisibleIndex = 13;
            this.gridColumn6.Width = 70;
            // 
            // gridColumn7
            // 
            this.gridColumn7.Caption = "SINR最小值";
            this.gridColumn7.FieldName = "MinSINR";
            this.gridColumn7.Name = "gridColumn7";
            this.gridColumn7.Visible = true;
            this.gridColumn7.VisibleIndex = 14;
            this.gridColumn7.Width = 70;
            // 
            // gridColumn8
            // 
            this.gridColumn8.Caption = "SINR平均值";
            this.gridColumn8.FieldName = "AvgSINR";
            this.gridColumn8.Name = "gridColumn8";
            this.gridColumn8.Visible = true;
            this.gridColumn8.VisibleIndex = 15;
            this.gridColumn8.Width = 70;
            // 
            // gridColumn9
            // 
            this.gridColumn9.Caption = "RSRP最大值";
            this.gridColumn9.FieldName = "MaxRSRP";
            this.gridColumn9.Name = "gridColumn9";
            this.gridColumn9.Visible = true;
            this.gridColumn9.VisibleIndex = 16;
            this.gridColumn9.Width = 70;
            // 
            // gridColumn10
            // 
            this.gridColumn10.Caption = "RSRP最小值";
            this.gridColumn10.FieldName = "MinRSRP";
            this.gridColumn10.Name = "gridColumn10";
            this.gridColumn10.Visible = true;
            this.gridColumn10.VisibleIndex = 17;
            this.gridColumn10.Width = 70;
            // 
            // gridColumn11
            // 
            this.gridColumn11.Caption = "RSRP平均值";
            this.gridColumn11.FieldName = "AvgRSRP";
            this.gridColumn11.Name = "gridColumn11";
            this.gridColumn11.Visible = true;
            this.gridColumn11.VisibleIndex = 18;
            this.gridColumn11.Width = 70;
            // 
            // gridColumn17
            // 
            this.gridColumn17.Caption = "平均下载速率(Mbps)";
            this.gridColumn17.FieldName = "AvgSpeed";
            this.gridColumn17.Name = "gridColumn17";
            this.gridColumn17.Visible = true;
            this.gridColumn17.VisibleIndex = 19;
            this.gridColumn17.Width = 130;
            // 
            // gridColumn19
            // 
            this.gridColumn19.Caption = "下行码字0 64QAM占比（%）";
            this.gridColumn19.FieldName = "DlCode0Qam64Rate";
            this.gridColumn19.Name = "gridColumn19";
            this.gridColumn19.Visible = true;
            this.gridColumn19.VisibleIndex = 20;
            this.gridColumn19.Width = 180;
            // 
            // gridColumn20
            // 
            this.gridColumn20.Caption = "双流时长占比(%)";
            this.gridColumn20.FieldName = "Rank_Indicator";
            this.gridColumn20.Name = "gridColumn20";
            this.gridColumn20.Visible = true;
            this.gridColumn20.VisibleIndex = 21;
            this.gridColumn20.Width = 120;
            // 
            // gridColumn21
            // 
            this.gridColumn21.Caption = "误块率（%）";
            this.gridColumn21.FieldName = "PDSCH_BLER";
            this.gridColumn21.Name = "gridColumn21";
            this.gridColumn21.Visible = true;
            this.gridColumn21.VisibleIndex = 22;
            this.gridColumn21.Width = 90;
            // 
            // gridColumn22
            // 
            this.gridColumn22.Caption = "TM3比例（%）";
            this.gridColumn22.FieldName = "Transmission_Mode";
            this.gridColumn22.Name = "gridColumn22";
            this.gridColumn22.Visible = true;
            this.gridColumn22.VisibleIndex = 23;
            this.gridColumn22.Width = 120;
            // 
            // gridColumn23
            // 
            this.gridColumn23.Caption = "PRB调度数";
            this.gridColumn23.FieldName = "PDSCH_PRb_Num_s";
            this.gridColumn23.Name = "gridColumn23";
            this.gridColumn23.Visible = true;
            this.gridColumn23.VisibleIndex = 24;
            this.gridColumn23.Width = 90;
            // 
            // gridColumn24
            // 
            this.gridColumn24.Caption = "下行码字1 64QAM占比（%）";
            this.gridColumn24.FieldName = "DlCode1Qam64Rate";
            this.gridColumn24.Name = "gridColumn24";
            this.gridColumn24.Visible = true;
            this.gridColumn24.VisibleIndex = 25;
            this.gridColumn24.Width = 180;
            // 
            // gridColumn12
            // 
            this.gridColumn12.Caption = "关联小区名称";
            this.gridColumn12.FieldName = "CellName";
            this.gridColumn12.Name = "gridColumn12";
            this.gridColumn12.Visible = true;
            this.gridColumn12.VisibleIndex = 26;
            this.gridColumn12.Width = 180;
            // 
            // gridColumn13
            // 
            this.gridColumn13.Caption = "TAC-CI";
            this.gridColumn13.FieldName = "LACCIs";
            this.gridColumn13.Name = "gridColumn13";
            this.gridColumn13.Visible = true;
            this.gridColumn13.VisibleIndex = 27;
            this.gridColumn13.Width = 300;
            // 
            // gridColumn2
            // 
            this.gridColumn2.Caption = "区域";
            this.gridColumn2.FieldName = "MotorWay";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.Visible = true;
            this.gridColumn2.VisibleIndex = 28;
            // 
            // WeakSINRRoadForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1191, 562);
            this.Controls.Add(this.gridControl);
            this.Name = "WeakSINRRoadForm";
            this.Text = "SINR质差路段";
            ((System.ComponentModel.ISupportInitialize)(this.gridControl)).EndInit();
            this.contextMenuStrip.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gv)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraGrid.GridControl gridControl;
        private DevExpress.XtraGrid.Views.Grid.GridView gv;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnRoad;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn6;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn7;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn8;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn15;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn16;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn18;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip;
        private System.Windows.Forms.ToolStripMenuItem miExport2Xls;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn9;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn10;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn11;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn13;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn12;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn17;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn19;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn20;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn21;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn22;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn23;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn24;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnGrid;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn26;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn25;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnCity;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn14;
    }
}