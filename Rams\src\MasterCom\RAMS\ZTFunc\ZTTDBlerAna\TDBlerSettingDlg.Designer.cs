﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class TDBlerSettingDlg
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.groupBox7 = new System.Windows.Forms.GroupBox();
            this.tbxSuggest = new System.Windows.Forms.TextBox();
            this.groupBox16 = new System.Windows.Forms.GroupBox();
            this.tbxDescription = new System.Windows.Forms.TextBox();
            this.simpleButtonDown = new DevExpress.XtraEditors.SimpleButton();
            this.simpleButtonUp = new DevExpress.XtraEditors.SimpleButton();
            this.checkedListBoxControlReason = new DevExpress.XtraEditors.CheckedListBoxControl();
            this.simpleButtonCancel = new DevExpress.XtraEditors.SimpleButton();
            this.simpleButtonOK = new DevExpress.XtraEditors.SimpleButton();
            this.splitContainerControl1 = new DevExpress.XtraEditors.SplitContainerControl();
            this.xtraTabControl1 = new DevExpress.XtraTab.XtraTabControl();
            this.xtraTabPage1 = new DevExpress.XtraTab.XtraTabPage();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.labelControl17 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl16 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl15 = new DevExpress.XtraEditors.LabelControl();
            this.spinEditCornerAfter = new DevExpress.XtraEditors.SpinEdit();
            this.spinEditCornerBefore = new DevExpress.XtraEditors.SpinEdit();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.spinEditPccpch_Weak = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl14 = new DevExpress.XtraEditors.LabelControl();
            this.groupBox5 = new System.Windows.Forms.GroupBox();
            this.labelControl10 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl11 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl12 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl13 = new DevExpress.XtraEditors.LabelControl();
            this.spinEditNum_Pullotion = new DevExpress.XtraEditors.SpinEdit();
            this.spinEditPccpch_Pullotion = new DevExpress.XtraEditors.SpinEdit();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl5 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.spinEditRatio_Coverlap = new DevExpress.XtraEditors.SpinEdit();
            this.spinEditPccpch_Coverlap = new DevExpress.XtraEditors.SpinEdit();
            this.groupBox4 = new System.Windows.Forms.GroupBox();
            this.labelControl6 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl8 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl9 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl7 = new DevExpress.XtraEditors.LabelControl();
            this.spinEditAngle_Dorsad = new DevExpress.XtraEditors.SpinEdit();
            this.spinEditDistance_Dorsad = new DevExpress.XtraEditors.SpinEdit();
            this.xtraTabPage2 = new DevExpress.XtraTab.XtraTabPage();
            this.groupBox10 = new System.Windows.Forms.GroupBox();
            this.labelControl30 = new DevExpress.XtraEditors.LabelControl();
            this.spinEditPccpch_Sub_Dpch = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl31 = new DevExpress.XtraEditors.LabelControl();
            this.groupBox9 = new System.Windows.Forms.GroupBox();
            this.labelControl29 = new DevExpress.XtraEditors.LabelControl();
            this.spinEditCorrelation = new DevExpress.XtraEditors.SpinEdit();
            this.groupBox8 = new System.Windows.Forms.GroupBox();
            this.labelControl24 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl25 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl26 = new DevExpress.XtraEditors.LabelControl();
            this.spinEditTxPower_UpInterrupt = new DevExpress.XtraEditors.SpinEdit();
            this.spinEditPccpch_C_I_UpInterrupt = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl27 = new DevExpress.XtraEditors.LabelControl();
            this.spinEditPccpch_UpInterrupt = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl28 = new DevExpress.XtraEditors.LabelControl();
            this.groupBox6 = new System.Windows.Forms.GroupBox();
            this.labelControl23 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl22 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl19 = new DevExpress.XtraEditors.LabelControl();
            this.spinEditDpch_C_I_CI = new DevExpress.XtraEditors.SpinEdit();
            this.spinEditPccpch_C_I_CI = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl62 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl21 = new DevExpress.XtraEditors.LabelControl();
            this.spinEditPccpch_CI = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl20 = new DevExpress.XtraEditors.LabelControl();
            this.xtraTabPage3 = new DevExpress.XtraTab.XtraTabPage();
            this.groupBox17 = new System.Windows.Forms.GroupBox();
            this.spinEditHOFrequently = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl36 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl37 = new DevExpress.XtraEditors.LabelControl();
            this.groupBox13 = new System.Windows.Forms.GroupBox();
            this.labelControl34 = new DevExpress.XtraEditors.LabelControl();
            this.groupBox12 = new System.Windows.Forms.GroupBox();
            this.spinEditPccpch_sSubn_Unreasonable = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl38 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl39 = new DevExpress.XtraEditors.LabelControl();
            this.groupBox11 = new System.Windows.Forms.GroupBox();
            this.spinEditHOLAfter = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl33 = new DevExpress.XtraEditors.LabelControl();
            this.spinEditHOLBefore = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl32 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl18 = new DevExpress.XtraEditors.LabelControl();
            this.xtraTabPage4 = new DevExpress.XtraTab.XtraTabPage();
            this.groupBox15 = new System.Windows.Forms.GroupBox();
            this.labelControl63 = new DevExpress.XtraEditors.LabelControl();
            this.groupBox14 = new System.Windows.Forms.GroupBox();
            this.labelControl42 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl43 = new DevExpress.XtraEditors.LabelControl();
            this.spinEditDpch_Antenna = new DevExpress.XtraEditors.SpinEdit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            this.groupBox7.SuspendLayout();
            this.groupBox16.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.checkedListBoxControlReason)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).BeginInit();
            this.splitContainerControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).BeginInit();
            this.xtraTabControl1.SuspendLayout();
            this.xtraTabPage1.SuspendLayout();
            this.groupBox1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditCornerAfter.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditCornerBefore.Properties)).BeginInit();
            this.groupBox2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditPccpch_Weak.Properties)).BeginInit();
            this.groupBox5.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditNum_Pullotion.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditPccpch_Pullotion.Properties)).BeginInit();
            this.groupBox3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditRatio_Coverlap.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditPccpch_Coverlap.Properties)).BeginInit();
            this.groupBox4.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditAngle_Dorsad.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditDistance_Dorsad.Properties)).BeginInit();
            this.xtraTabPage2.SuspendLayout();
            this.groupBox10.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditPccpch_Sub_Dpch.Properties)).BeginInit();
            this.groupBox9.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditCorrelation.Properties)).BeginInit();
            this.groupBox8.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditTxPower_UpInterrupt.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditPccpch_C_I_UpInterrupt.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditPccpch_UpInterrupt.Properties)).BeginInit();
            this.groupBox6.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditDpch_C_I_CI.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditPccpch_C_I_CI.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditPccpch_CI.Properties)).BeginInit();
            this.xtraTabPage3.SuspendLayout();
            this.groupBox17.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditHOFrequently.Properties)).BeginInit();
            this.groupBox13.SuspendLayout();
            this.groupBox12.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditPccpch_sSubn_Unreasonable.Properties)).BeginInit();
            this.groupBox11.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditHOLAfter.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditHOLBefore.Properties)).BeginInit();
            this.xtraTabPage4.SuspendLayout();
            this.groupBox15.SuspendLayout();
            this.groupBox14.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditDpch_Antenna.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // groupControl1
            // 
            this.groupControl1.Controls.Add(this.groupBox7);
            this.groupControl1.Controls.Add(this.groupBox16);
            this.groupControl1.Controls.Add(this.simpleButtonDown);
            this.groupControl1.Controls.Add(this.simpleButtonUp);
            this.groupControl1.Controls.Add(this.checkedListBoxControlReason);
            this.groupControl1.Controls.Add(this.simpleButtonCancel);
            this.groupControl1.Controls.Add(this.simpleButtonOK);
            this.groupControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControl1.Location = new System.Drawing.Point(0, 0);
            this.groupControl1.Name = "groupControl1";
            this.groupControl1.Size = new System.Drawing.Size(1085, 362);
            this.groupControl1.TabIndex = 1;
            this.groupControl1.Text = "分析顺序";
            // 
            // groupBox7
            // 
            this.groupBox7.Controls.Add(this.tbxSuggest);
            this.groupBox7.Location = new System.Drawing.Point(370, 181);
            this.groupBox7.Name = "groupBox7";
            this.groupBox7.Size = new System.Drawing.Size(679, 127);
            this.groupBox7.TabIndex = 7;
            this.groupBox7.TabStop = false;
            this.groupBox7.Text = "优化建议";
            // 
            // tbxSuggest
            // 
            this.tbxSuggest.Location = new System.Drawing.Point(35, 23);
            this.tbxSuggest.Multiline = true;
            this.tbxSuggest.Name = "tbxSuggest";
            this.tbxSuggest.ReadOnly = true;
            this.tbxSuggest.Size = new System.Drawing.Size(612, 89);
            this.tbxSuggest.TabIndex = 5;
            // 
            // groupBox16
            // 
            this.groupBox16.Controls.Add(this.tbxDescription);
            this.groupBox16.Location = new System.Drawing.Point(370, 38);
            this.groupBox16.Name = "groupBox16";
            this.groupBox16.Size = new System.Drawing.Size(679, 127);
            this.groupBox16.TabIndex = 6;
            this.groupBox16.TabStop = false;
            this.groupBox16.Text = "分析场景说明";
            // 
            // tbxDescription
            // 
            this.tbxDescription.Location = new System.Drawing.Point(35, 23);
            this.tbxDescription.Multiline = true;
            this.tbxDescription.Name = "tbxDescription";
            this.tbxDescription.ReadOnly = true;
            this.tbxDescription.Size = new System.Drawing.Size(612, 89);
            this.tbxDescription.TabIndex = 5;
            // 
            // simpleButtonDown
            // 
            this.simpleButtonDown.Appearance.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.simpleButtonDown.Appearance.Options.UseFont = true;
            this.simpleButtonDown.Location = new System.Drawing.Point(278, 98);
            this.simpleButtonDown.Name = "simpleButtonDown";
            this.simpleButtonDown.Size = new System.Drawing.Size(64, 27);
            this.simpleButtonDown.TabIndex = 4;
            this.simpleButtonDown.Text = "↓";
            this.simpleButtonDown.Click += new System.EventHandler(this.simpleButtonDown_Click);
            // 
            // simpleButtonUp
            // 
            this.simpleButtonUp.Appearance.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.simpleButtonUp.Appearance.Options.UseFont = true;
            this.simpleButtonUp.Location = new System.Drawing.Point(278, 59);
            this.simpleButtonUp.Name = "simpleButtonUp";
            this.simpleButtonUp.Size = new System.Drawing.Size(64, 27);
            this.simpleButtonUp.TabIndex = 4;
            this.simpleButtonUp.Text = "↑";
            this.simpleButtonUp.Click += new System.EventHandler(this.simpleButtonUp_Click);
            // 
            // checkedListBoxControlReason
            // 
            this.checkedListBoxControlReason.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.checkedListBoxControlReason.Appearance.Options.UseFont = true;
            this.checkedListBoxControlReason.Location = new System.Drawing.Point(24, 38);
            this.checkedListBoxControlReason.Name = "checkedListBoxControlReason";
            this.checkedListBoxControlReason.Size = new System.Drawing.Size(241, 303);
            this.checkedListBoxControlReason.TabIndex = 3;
            this.checkedListBoxControlReason.SelectedIndexChanged += new System.EventHandler(this.checkedListBoxControlReason_SelectedIndexChanged);
            // 
            // simpleButtonCancel
            // 
            this.simpleButtonCancel.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.simpleButtonCancel.Appearance.Options.UseFont = true;
            this.simpleButtonCancel.Location = new System.Drawing.Point(961, 321);
            this.simpleButtonCancel.Name = "simpleButtonCancel";
            this.simpleButtonCancel.Size = new System.Drawing.Size(87, 27);
            this.simpleButtonCancel.TabIndex = 2;
            this.simpleButtonCancel.Text = "取消";
            this.simpleButtonCancel.Click += new System.EventHandler(this.simpleButtonCancel_Click);
            // 
            // simpleButtonOK
            // 
            this.simpleButtonOK.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.simpleButtonOK.Appearance.Options.UseFont = true;
            this.simpleButtonOK.Location = new System.Drawing.Point(850, 321);
            this.simpleButtonOK.Name = "simpleButtonOK";
            this.simpleButtonOK.Size = new System.Drawing.Size(87, 27);
            this.simpleButtonOK.TabIndex = 2;
            this.simpleButtonOK.Text = "确定";
            this.simpleButtonOK.Click += new System.EventHandler(this.simpleButtonOK_Click);
            // 
            // splitContainerControl1
            // 
            this.splitContainerControl1.CollapsePanel = DevExpress.XtraEditors.SplitCollapsePanel.Panel1;
            this.splitContainerControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl1.Horizontal = false;
            this.splitContainerControl1.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl1.Name = "splitContainerControl1";
            this.splitContainerControl1.Panel1.Controls.Add(this.xtraTabControl1);
            this.splitContainerControl1.Panel1.Text = "Panel1";
            this.splitContainerControl1.Panel2.Controls.Add(this.groupControl1);
            this.splitContainerControl1.Panel2.Text = "Panel2";
            this.splitContainerControl1.Size = new System.Drawing.Size(1085, 571);
            this.splitContainerControl1.SplitterPosition = 203;
            this.splitContainerControl1.TabIndex = 3;
            this.splitContainerControl1.Text = "splitContainerControl1";
            // 
            // xtraTabControl1
            // 
            this.xtraTabControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.xtraTabControl1.Location = new System.Drawing.Point(0, 0);
            this.xtraTabControl1.Name = "xtraTabControl1";
            this.xtraTabControl1.SelectedTabPage = this.xtraTabPage1;
            this.xtraTabControl1.Size = new System.Drawing.Size(1085, 203);
            this.xtraTabControl1.TabIndex = 0;
            this.xtraTabControl1.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.xtraTabPage1,
            this.xtraTabPage2,
            this.xtraTabPage3,
            this.xtraTabPage4});
            // 
            // xtraTabPage1
            // 
            this.xtraTabPage1.Controls.Add(this.groupBox1);
            this.xtraTabPage1.Controls.Add(this.groupBox2);
            this.xtraTabPage1.Controls.Add(this.groupBox5);
            this.xtraTabPage1.Controls.Add(this.groupBox3);
            this.xtraTabPage1.Controls.Add(this.groupBox4);
            this.xtraTabPage1.Name = "xtraTabPage1";
            this.xtraTabPage1.Size = new System.Drawing.Size(1078, 173);
            this.xtraTabPage1.Text = "覆盖类";
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.labelControl17);
            this.groupBox1.Controls.Add(this.labelControl16);
            this.groupBox1.Controls.Add(this.labelControl15);
            this.groupBox1.Controls.Add(this.spinEditCornerAfter);
            this.groupBox1.Controls.Add(this.spinEditCornerBefore);
            this.groupBox1.Location = new System.Drawing.Point(428, 8);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(507, 61);
            this.groupBox1.TabIndex = 6;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "拐角效应";
            // 
            // labelControl17
            // 
            this.labelControl17.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl17.Appearance.Options.UseFont = true;
            this.labelControl17.Location = new System.Drawing.Point(329, 26);
            this.labelControl17.Name = "labelControl17";
            this.labelControl17.Size = new System.Drawing.Size(126, 12);
            this.labelControl17.TabIndex = 1;
            this.labelControl17.Text = "秒内 出现拐角效应事件";
            // 
            // labelControl16
            // 
            this.labelControl16.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl16.Appearance.Options.UseFont = true;
            this.labelControl16.Location = new System.Drawing.Point(167, 26);
            this.labelControl16.Name = "labelControl16";
            this.labelControl16.Size = new System.Drawing.Size(42, 12);
            this.labelControl16.TabIndex = 1;
            this.labelControl16.Text = "秒 或后";
            // 
            // labelControl15
            // 
            this.labelControl15.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl15.Appearance.Options.UseFont = true;
            this.labelControl15.Location = new System.Drawing.Point(29, 26);
            this.labelControl15.Name = "labelControl15";
            this.labelControl15.Size = new System.Drawing.Size(24, 12);
            this.labelControl15.TabIndex = 1;
            this.labelControl15.Text = "在前";
            // 
            // spinEditCornerAfter
            // 
            this.spinEditCornerAfter.EditValue = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.spinEditCornerAfter.Location = new System.Drawing.Point(224, 22);
            this.spinEditCornerAfter.Name = "spinEditCornerAfter";
            this.spinEditCornerAfter.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditCornerAfter.Properties.Appearance.Options.UseFont = true;
            this.spinEditCornerAfter.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditCornerAfter.Properties.Mask.EditMask = "f0";
            this.spinEditCornerAfter.Size = new System.Drawing.Size(96, 20);
            this.spinEditCornerAfter.TabIndex = 0;
            // 
            // spinEditCornerBefore
            // 
            this.spinEditCornerBefore.EditValue = new decimal(new int[] {
            3,
            0,
            0,
            0});
            this.spinEditCornerBefore.Location = new System.Drawing.Point(64, 22);
            this.spinEditCornerBefore.Name = "spinEditCornerBefore";
            this.spinEditCornerBefore.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditCornerBefore.Properties.Appearance.Options.UseFont = true;
            this.spinEditCornerBefore.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditCornerBefore.Properties.Mask.EditMask = "f0";
            this.spinEditCornerBefore.Size = new System.Drawing.Size(96, 20);
            this.spinEditCornerBefore.TabIndex = 0;
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.labelControl1);
            this.groupBox2.Controls.Add(this.spinEditPccpch_Weak);
            this.groupBox2.Controls.Add(this.labelControl14);
            this.groupBox2.Location = new System.Drawing.Point(17, 8);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(392, 61);
            this.groupBox2.TabIndex = 2;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "弱覆盖";
            // 
            // labelControl1
            // 
            this.labelControl1.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl1.Appearance.Options.UseFont = true;
            this.labelControl1.Location = new System.Drawing.Point(80, 26);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(126, 12);
            this.labelControl1.TabIndex = 1;
            this.labelControl1.Text = "主服及第一邻区场强 ≤";
            // 
            // spinEditPccpch_Weak
            // 
            this.spinEditPccpch_Weak.EditValue = new decimal(new int[] {
            90,
            0,
            0,
            -2147483648});
            this.spinEditPccpch_Weak.Location = new System.Drawing.Point(212, 23);
            this.spinEditPccpch_Weak.Name = "spinEditPccpch_Weak";
            this.spinEditPccpch_Weak.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditPccpch_Weak.Properties.Appearance.Options.UseFont = true;
            this.spinEditPccpch_Weak.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditPccpch_Weak.Properties.Mask.EditMask = "f0";
            this.spinEditPccpch_Weak.Size = new System.Drawing.Size(96, 20);
            this.spinEditPccpch_Weak.TabIndex = 0;
            // 
            // labelControl14
            // 
            this.labelControl14.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl14.Appearance.Options.UseFont = true;
            this.labelControl14.Location = new System.Drawing.Point(314, 26);
            this.labelControl14.Name = "labelControl14";
            this.labelControl14.Size = new System.Drawing.Size(18, 12);
            this.labelControl14.TabIndex = 2;
            this.labelControl14.Text = "dBm";
            // 
            // groupBox5
            // 
            this.groupBox5.Controls.Add(this.labelControl10);
            this.groupBox5.Controls.Add(this.labelControl11);
            this.groupBox5.Controls.Add(this.labelControl12);
            this.groupBox5.Controls.Add(this.labelControl13);
            this.groupBox5.Controls.Add(this.spinEditNum_Pullotion);
            this.groupBox5.Controls.Add(this.spinEditPccpch_Pullotion);
            this.groupBox5.Location = new System.Drawing.Point(17, 75);
            this.groupBox5.Name = "groupBox5";
            this.groupBox5.Size = new System.Drawing.Size(390, 95);
            this.groupBox5.TabIndex = 5;
            this.groupBox5.TabStop = false;
            this.groupBox5.Text = "导频污染";
            // 
            // labelControl10
            // 
            this.labelControl10.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl10.Appearance.Options.UseFont = true;
            this.labelControl10.Location = new System.Drawing.Point(24, 30);
            this.labelControl10.Name = "labelControl10";
            this.labelControl10.Size = new System.Drawing.Size(180, 12);
            this.labelControl10.TabIndex = 3;
            this.labelControl10.Text = "与最大电平相差6dB内小区数量 ≥";
            // 
            // labelControl11
            // 
            this.labelControl11.Location = new System.Drawing.Point(312, 27);
            this.labelControl11.Name = "labelControl11";
            this.labelControl11.Size = new System.Drawing.Size(12, 14);
            this.labelControl11.TabIndex = 2;
            this.labelControl11.Text = "个";
            // 
            // labelControl12
            // 
            this.labelControl12.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl12.Appearance.Options.UseFont = true;
            this.labelControl12.Location = new System.Drawing.Point(312, 61);
            this.labelControl12.Name = "labelControl12";
            this.labelControl12.Size = new System.Drawing.Size(18, 12);
            this.labelControl12.TabIndex = 2;
            this.labelControl12.Text = "dBm";
            // 
            // labelControl13
            // 
            this.labelControl13.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl13.Appearance.Options.UseFont = true;
            this.labelControl13.Location = new System.Drawing.Point(84, 60);
            this.labelControl13.Name = "labelControl13";
            this.labelControl13.Size = new System.Drawing.Size(120, 12);
            this.labelControl13.TabIndex = 1;
            this.labelControl13.Text = "6dB内所有小区场强 ≥";
            // 
            // spinEditNum_Pullotion
            // 
            this.spinEditNum_Pullotion.EditValue = new decimal(new int[] {
            4,
            0,
            0,
            0});
            this.spinEditNum_Pullotion.Location = new System.Drawing.Point(210, 25);
            this.spinEditNum_Pullotion.Name = "spinEditNum_Pullotion";
            this.spinEditNum_Pullotion.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditNum_Pullotion.Properties.Appearance.Options.UseFont = true;
            this.spinEditNum_Pullotion.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditNum_Pullotion.Properties.Mask.EditMask = "f0";
            this.spinEditNum_Pullotion.Size = new System.Drawing.Size(96, 20);
            this.spinEditNum_Pullotion.TabIndex = 0;
            // 
            // spinEditPccpch_Pullotion
            // 
            this.spinEditPccpch_Pullotion.EditValue = new decimal(new int[] {
            85,
            0,
            0,
            -2147483648});
            this.spinEditPccpch_Pullotion.Location = new System.Drawing.Point(210, 57);
            this.spinEditPccpch_Pullotion.Name = "spinEditPccpch_Pullotion";
            this.spinEditPccpch_Pullotion.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditPccpch_Pullotion.Properties.Appearance.Options.UseFont = true;
            this.spinEditPccpch_Pullotion.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditPccpch_Pullotion.Properties.Mask.EditMask = "f0";
            this.spinEditPccpch_Pullotion.Size = new System.Drawing.Size(96, 20);
            this.spinEditPccpch_Pullotion.TabIndex = 0;
            // 
            // groupBox3
            // 
            this.groupBox3.Controls.Add(this.labelControl4);
            this.groupBox3.Controls.Add(this.labelControl5);
            this.groupBox3.Controls.Add(this.labelControl3);
            this.groupBox3.Controls.Add(this.labelControl2);
            this.groupBox3.Controls.Add(this.spinEditRatio_Coverlap);
            this.groupBox3.Controls.Add(this.spinEditPccpch_Coverlap);
            this.groupBox3.Location = new System.Drawing.Point(428, 75);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.Size = new System.Drawing.Size(310, 95);
            this.groupBox3.TabIndex = 4;
            this.groupBox3.TabStop = false;
            this.groupBox3.Text = "过覆盖";
            // 
            // labelControl4
            // 
            this.labelControl4.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl4.Appearance.Options.UseFont = true;
            this.labelControl4.Location = new System.Drawing.Point(40, 60);
            this.labelControl4.Name = "labelControl4";
            this.labelControl4.Size = new System.Drawing.Size(120, 12);
            this.labelControl4.TabIndex = 3;
            this.labelControl4.Text = "距离 ≥ 理想覆盖半径";
            // 
            // labelControl5
            // 
            this.labelControl5.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl5.Appearance.Options.UseFont = true;
            this.labelControl5.Location = new System.Drawing.Point(271, 62);
            this.labelControl5.Name = "labelControl5";
            this.labelControl5.Size = new System.Drawing.Size(12, 12);
            this.labelControl5.TabIndex = 2;
            this.labelControl5.Text = "倍";
            // 
            // labelControl3
            // 
            this.labelControl3.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl3.Appearance.Options.UseFont = true;
            this.labelControl3.Location = new System.Drawing.Point(271, 30);
            this.labelControl3.Name = "labelControl3";
            this.labelControl3.Size = new System.Drawing.Size(18, 12);
            this.labelControl3.TabIndex = 2;
            this.labelControl3.Text = "dBm";
            // 
            // labelControl2
            // 
            this.labelControl2.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl2.Appearance.Options.UseFont = true;
            this.labelControl2.Location = new System.Drawing.Point(118, 30);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(42, 12);
            this.labelControl2.TabIndex = 1;
            this.labelControl2.Text = "场强 ≥";
            // 
            // spinEditRatio_Coverlap
            // 
            this.spinEditRatio_Coverlap.EditValue = new decimal(new int[] {
            16,
            0,
            0,
            65536});
            this.spinEditRatio_Coverlap.Location = new System.Drawing.Point(167, 57);
            this.spinEditRatio_Coverlap.Name = "spinEditRatio_Coverlap";
            this.spinEditRatio_Coverlap.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditRatio_Coverlap.Properties.Appearance.Options.UseFont = true;
            this.spinEditRatio_Coverlap.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditRatio_Coverlap.Properties.Mask.EditMask = "f";
            this.spinEditRatio_Coverlap.Size = new System.Drawing.Size(96, 20);
            this.spinEditRatio_Coverlap.TabIndex = 0;
            // 
            // spinEditPccpch_Coverlap
            // 
            this.spinEditPccpch_Coverlap.EditValue = new decimal(new int[] {
            85,
            0,
            0,
            -2147483648});
            this.spinEditPccpch_Coverlap.Location = new System.Drawing.Point(167, 27);
            this.spinEditPccpch_Coverlap.Name = "spinEditPccpch_Coverlap";
            this.spinEditPccpch_Coverlap.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditPccpch_Coverlap.Properties.Appearance.Options.UseFont = true;
            this.spinEditPccpch_Coverlap.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditPccpch_Coverlap.Properties.Mask.EditMask = "f0";
            this.spinEditPccpch_Coverlap.Size = new System.Drawing.Size(96, 20);
            this.spinEditPccpch_Coverlap.TabIndex = 0;
            // 
            // groupBox4
            // 
            this.groupBox4.Controls.Add(this.labelControl6);
            this.groupBox4.Controls.Add(this.labelControl8);
            this.groupBox4.Controls.Add(this.labelControl9);
            this.groupBox4.Controls.Add(this.labelControl7);
            this.groupBox4.Controls.Add(this.spinEditAngle_Dorsad);
            this.groupBox4.Controls.Add(this.spinEditDistance_Dorsad);
            this.groupBox4.Location = new System.Drawing.Point(758, 75);
            this.groupBox4.Name = "groupBox4";
            this.groupBox4.Size = new System.Drawing.Size(289, 95);
            this.groupBox4.TabIndex = 3;
            this.groupBox4.TabStop = false;
            this.groupBox4.Text = "背向覆盖";
            // 
            // labelControl6
            // 
            this.labelControl6.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl6.Appearance.Options.UseFont = true;
            this.labelControl6.Location = new System.Drawing.Point(36, 62);
            this.labelControl6.Name = "labelControl6";
            this.labelControl6.Size = new System.Drawing.Size(114, 12);
            this.labelControl6.TabIndex = 3;
            this.labelControl6.Text = "小区与采样点距离 ≥";
            // 
            // labelControl8
            // 
            this.labelControl8.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl8.Appearance.Options.UseFont = true;
            this.labelControl8.Location = new System.Drawing.Point(259, 29);
            this.labelControl8.Name = "labelControl8";
            this.labelControl8.Size = new System.Drawing.Size(12, 12);
            this.labelControl8.TabIndex = 2;
            this.labelControl8.Text = "度";
            // 
            // labelControl9
            // 
            this.labelControl9.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl9.Appearance.Options.UseFont = true;
            this.labelControl9.Location = new System.Drawing.Point(42, 30);
            this.labelControl9.Name = "labelControl9";
            this.labelControl9.Size = new System.Drawing.Size(108, 12);
            this.labelControl9.TabIndex = 1;
            this.labelControl9.Text = "方位角 ≥ 主瓣正负";
            // 
            // labelControl7
            // 
            this.labelControl7.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl7.Appearance.Options.UseFont = true;
            this.labelControl7.Location = new System.Drawing.Point(259, 62);
            this.labelControl7.Name = "labelControl7";
            this.labelControl7.Size = new System.Drawing.Size(12, 12);
            this.labelControl7.TabIndex = 2;
            this.labelControl7.Text = "米";
            // 
            // spinEditAngle_Dorsad
            // 
            this.spinEditAngle_Dorsad.EditValue = new decimal(new int[] {
            90,
            0,
            0,
            0});
            this.spinEditAngle_Dorsad.Location = new System.Drawing.Point(156, 24);
            this.spinEditAngle_Dorsad.Name = "spinEditAngle_Dorsad";
            this.spinEditAngle_Dorsad.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditAngle_Dorsad.Properties.Appearance.Options.UseFont = true;
            this.spinEditAngle_Dorsad.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditAngle_Dorsad.Properties.Mask.EditMask = "f";
            this.spinEditAngle_Dorsad.Size = new System.Drawing.Size(96, 20);
            this.spinEditAngle_Dorsad.TabIndex = 0;
            // 
            // spinEditDistance_Dorsad
            // 
            this.spinEditDistance_Dorsad.EditValue = new decimal(new int[] {
            300,
            0,
            0,
            0});
            this.spinEditDistance_Dorsad.Location = new System.Drawing.Point(156, 58);
            this.spinEditDistance_Dorsad.Name = "spinEditDistance_Dorsad";
            this.spinEditDistance_Dorsad.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditDistance_Dorsad.Properties.Appearance.Options.UseFont = true;
            this.spinEditDistance_Dorsad.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditDistance_Dorsad.Properties.Mask.EditMask = "f0";
            this.spinEditDistance_Dorsad.Size = new System.Drawing.Size(96, 20);
            this.spinEditDistance_Dorsad.TabIndex = 0;
            // 
            // xtraTabPage2
            // 
            this.xtraTabPage2.Controls.Add(this.groupBox10);
            this.xtraTabPage2.Controls.Add(this.groupBox9);
            this.xtraTabPage2.Controls.Add(this.groupBox8);
            this.xtraTabPage2.Controls.Add(this.groupBox6);
            this.xtraTabPage2.Name = "xtraTabPage2";
            this.xtraTabPage2.Size = new System.Drawing.Size(1078, 173);
            this.xtraTabPage2.Text = "干扰类";
            // 
            // groupBox10
            // 
            this.groupBox10.Controls.Add(this.labelControl30);
            this.groupBox10.Controls.Add(this.spinEditPccpch_Sub_Dpch);
            this.groupBox10.Controls.Add(this.labelControl31);
            this.groupBox10.Location = new System.Drawing.Point(582, 8);
            this.groupBox10.Name = "groupBox10";
            this.groupBox10.Size = new System.Drawing.Size(412, 56);
            this.groupBox10.TabIndex = 0;
            this.groupBox10.TabStop = false;
            this.groupBox10.Text = "功控配置";
            // 
            // labelControl30
            // 
            this.labelControl30.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl30.Appearance.Options.UseFont = true;
            this.labelControl30.Location = new System.Drawing.Point(28, 26);
            this.labelControl30.Name = "labelControl30";
            this.labelControl30.Size = new System.Drawing.Size(204, 12);
            this.labelControl30.TabIndex = 4;
            this.labelControl30.Text = "主服小区PCCPCH_RSCP - DPCH RSCP ≥";
            // 
            // spinEditPccpch_Sub_Dpch
            // 
            this.spinEditPccpch_Sub_Dpch.EditValue = new decimal(new int[] {
            25,
            0,
            0,
            0});
            this.spinEditPccpch_Sub_Dpch.Location = new System.Drawing.Point(238, 21);
            this.spinEditPccpch_Sub_Dpch.Name = "spinEditPccpch_Sub_Dpch";
            this.spinEditPccpch_Sub_Dpch.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditPccpch_Sub_Dpch.Properties.Appearance.Options.UseFont = true;
            this.spinEditPccpch_Sub_Dpch.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditPccpch_Sub_Dpch.Properties.Mask.EditMask = "f0";
            this.spinEditPccpch_Sub_Dpch.Size = new System.Drawing.Size(96, 20);
            this.spinEditPccpch_Sub_Dpch.TabIndex = 3;
            // 
            // labelControl31
            // 
            this.labelControl31.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl31.Appearance.Options.UseFont = true;
            this.labelControl31.Location = new System.Drawing.Point(341, 24);
            this.labelControl31.Name = "labelControl31";
            this.labelControl31.Size = new System.Drawing.Size(12, 12);
            this.labelControl31.TabIndex = 5;
            this.labelControl31.Text = "dB";
            // 
            // groupBox9
            // 
            this.groupBox9.Controls.Add(this.labelControl29);
            this.groupBox9.Controls.Add(this.spinEditCorrelation);
            this.groupBox9.Location = new System.Drawing.Point(582, 78);
            this.groupBox9.Name = "groupBox9";
            this.groupBox9.Size = new System.Drawing.Size(412, 56);
            this.groupBox9.TabIndex = 0;
            this.groupBox9.TabStop = false;
            this.groupBox9.Text = "同频同码干扰";
            // 
            // labelControl29
            // 
            this.labelControl29.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl29.Appearance.Options.UseFont = true;
            this.labelControl29.Location = new System.Drawing.Point(130, 24);
            this.labelControl29.Name = "labelControl29";
            this.labelControl29.Size = new System.Drawing.Size(102, 12);
            this.labelControl29.TabIndex = 4;
            this.labelControl29.Text = "扰码自相关系数 ≥";
            // 
            // spinEditCorrelation
            // 
            this.spinEditCorrelation.EditValue = new decimal(new int[] {
            5,
            0,
            0,
            65536});
            this.spinEditCorrelation.Location = new System.Drawing.Point(238, 20);
            this.spinEditCorrelation.Name = "spinEditCorrelation";
            this.spinEditCorrelation.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditCorrelation.Properties.Appearance.Options.UseFont = true;
            this.spinEditCorrelation.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditCorrelation.Properties.Mask.EditMask = "f";
            this.spinEditCorrelation.Size = new System.Drawing.Size(96, 20);
            this.spinEditCorrelation.TabIndex = 3;
            // 
            // groupBox8
            // 
            this.groupBox8.Controls.Add(this.labelControl24);
            this.groupBox8.Controls.Add(this.labelControl25);
            this.groupBox8.Controls.Add(this.labelControl26);
            this.groupBox8.Controls.Add(this.spinEditTxPower_UpInterrupt);
            this.groupBox8.Controls.Add(this.spinEditPccpch_C_I_UpInterrupt);
            this.groupBox8.Controls.Add(this.labelControl27);
            this.groupBox8.Controls.Add(this.spinEditPccpch_UpInterrupt);
            this.groupBox8.Controls.Add(this.labelControl28);
            this.groupBox8.Location = new System.Drawing.Point(292, 8);
            this.groupBox8.Name = "groupBox8";
            this.groupBox8.Size = new System.Drawing.Size(266, 126);
            this.groupBox8.TabIndex = 0;
            this.groupBox8.TabStop = false;
            this.groupBox8.Text = "上行干扰";
            // 
            // labelControl24
            // 
            this.labelControl24.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl24.Appearance.Options.UseFont = true;
            this.labelControl24.Location = new System.Drawing.Point(40, 86);
            this.labelControl24.Name = "labelControl24";
            this.labelControl24.Size = new System.Drawing.Size(78, 12);
            this.labelControl24.TabIndex = 4;
            this.labelControl24.Text = "UE TxPower ≥";
            // 
            // labelControl25
            // 
            this.labelControl25.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl25.Appearance.Options.UseFont = true;
            this.labelControl25.Location = new System.Drawing.Point(40, 55);
            this.labelControl25.Name = "labelControl25";
            this.labelControl25.Size = new System.Drawing.Size(78, 12);
            this.labelControl25.TabIndex = 4;
            this.labelControl25.Text = "PCCPCH C/I ≥";
            // 
            // labelControl26
            // 
            this.labelControl26.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl26.Appearance.Options.UseFont = true;
            this.labelControl26.Location = new System.Drawing.Point(33, 24);
            this.labelControl26.Name = "labelControl26";
            this.labelControl26.Size = new System.Drawing.Size(84, 12);
            this.labelControl26.TabIndex = 4;
            this.labelControl26.Text = "PCCPCH_RSCP ≥";
            // 
            // spinEditTxPower_UpInterrupt
            // 
            this.spinEditTxPower_UpInterrupt.EditValue = new decimal(new int[] {
            20,
            0,
            0,
            0});
            this.spinEditTxPower_UpInterrupt.Location = new System.Drawing.Point(125, 83);
            this.spinEditTxPower_UpInterrupt.Name = "spinEditTxPower_UpInterrupt";
            this.spinEditTxPower_UpInterrupt.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditTxPower_UpInterrupt.Properties.Appearance.Options.UseFont = true;
            this.spinEditTxPower_UpInterrupt.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditTxPower_UpInterrupt.Properties.Mask.EditMask = "f0";
            this.spinEditTxPower_UpInterrupt.Size = new System.Drawing.Size(96, 20);
            this.spinEditTxPower_UpInterrupt.TabIndex = 3;
            // 
            // spinEditPccpch_C_I_UpInterrupt
            // 
            this.spinEditPccpch_C_I_UpInterrupt.EditValue = new decimal(new int[] {
            3,
            0,
            0,
            -2147483648});
            this.spinEditPccpch_C_I_UpInterrupt.Location = new System.Drawing.Point(124, 51);
            this.spinEditPccpch_C_I_UpInterrupt.Name = "spinEditPccpch_C_I_UpInterrupt";
            this.spinEditPccpch_C_I_UpInterrupt.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditPccpch_C_I_UpInterrupt.Properties.Appearance.Options.UseFont = true;
            this.spinEditPccpch_C_I_UpInterrupt.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditPccpch_C_I_UpInterrupt.Properties.Mask.EditMask = "f0";
            this.spinEditPccpch_C_I_UpInterrupt.Size = new System.Drawing.Size(96, 20);
            this.spinEditPccpch_C_I_UpInterrupt.TabIndex = 3;
            // 
            // labelControl27
            // 
            this.labelControl27.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl27.Appearance.Options.UseFont = true;
            this.labelControl27.Location = new System.Drawing.Point(222, 55);
            this.labelControl27.Name = "labelControl27";
            this.labelControl27.Size = new System.Drawing.Size(12, 12);
            this.labelControl27.TabIndex = 5;
            this.labelControl27.Text = "dB";
            // 
            // spinEditPccpch_UpInterrupt
            // 
            this.spinEditPccpch_UpInterrupt.EditValue = new decimal(new int[] {
            85,
            0,
            0,
            -2147483648});
            this.spinEditPccpch_UpInterrupt.Location = new System.Drawing.Point(124, 20);
            this.spinEditPccpch_UpInterrupt.Name = "spinEditPccpch_UpInterrupt";
            this.spinEditPccpch_UpInterrupt.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditPccpch_UpInterrupt.Properties.Appearance.Options.UseFont = true;
            this.spinEditPccpch_UpInterrupt.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditPccpch_UpInterrupt.Properties.Mask.EditMask = "f0";
            this.spinEditPccpch_UpInterrupt.Size = new System.Drawing.Size(96, 20);
            this.spinEditPccpch_UpInterrupt.TabIndex = 3;
            // 
            // labelControl28
            // 
            this.labelControl28.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl28.Appearance.Options.UseFont = true;
            this.labelControl28.Location = new System.Drawing.Point(224, 23);
            this.labelControl28.Name = "labelControl28";
            this.labelControl28.Size = new System.Drawing.Size(18, 12);
            this.labelControl28.TabIndex = 5;
            this.labelControl28.Text = "dBm";
            // 
            // groupBox6
            // 
            this.groupBox6.Controls.Add(this.labelControl23);
            this.groupBox6.Controls.Add(this.labelControl22);
            this.groupBox6.Controls.Add(this.labelControl19);
            this.groupBox6.Controls.Add(this.spinEditDpch_C_I_CI);
            this.groupBox6.Controls.Add(this.spinEditPccpch_C_I_CI);
            this.groupBox6.Controls.Add(this.labelControl62);
            this.groupBox6.Controls.Add(this.labelControl21);
            this.groupBox6.Controls.Add(this.spinEditPccpch_CI);
            this.groupBox6.Controls.Add(this.labelControl20);
            this.groupBox6.Location = new System.Drawing.Point(13, 8);
            this.groupBox6.Name = "groupBox6";
            this.groupBox6.Size = new System.Drawing.Size(254, 126);
            this.groupBox6.TabIndex = 0;
            this.groupBox6.TabStop = false;
            this.groupBox6.Text = "C/I差";
            // 
            // labelControl23
            // 
            this.labelControl23.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl23.Appearance.Options.UseFont = true;
            this.labelControl23.Location = new System.Drawing.Point(49, 86);
            this.labelControl23.Name = "labelControl23";
            this.labelControl23.Size = new System.Drawing.Size(66, 12);
            this.labelControl23.TabIndex = 4;
            this.labelControl23.Text = "DPCH_C/I ≤";
            // 
            // labelControl22
            // 
            this.labelControl22.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl22.Appearance.Options.UseFont = true;
            this.labelControl22.Location = new System.Drawing.Point(37, 55);
            this.labelControl22.Name = "labelControl22";
            this.labelControl22.Size = new System.Drawing.Size(78, 12);
            this.labelControl22.TabIndex = 4;
            this.labelControl22.Text = "PCCPCH_C/I ≤";
            // 
            // labelControl19
            // 
            this.labelControl19.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl19.Appearance.Options.UseFont = true;
            this.labelControl19.Location = new System.Drawing.Point(31, 23);
            this.labelControl19.Name = "labelControl19";
            this.labelControl19.Size = new System.Drawing.Size(84, 12);
            this.labelControl19.TabIndex = 4;
            this.labelControl19.Text = "PCCPCH_RSCP ≥";
            // 
            // spinEditDpch_C_I_CI
            // 
            this.spinEditDpch_C_I_CI.EditValue = new decimal(new int[] {
            3,
            0,
            0,
            -2147483648});
            this.spinEditDpch_C_I_CI.Location = new System.Drawing.Point(121, 83);
            this.spinEditDpch_C_I_CI.Name = "spinEditDpch_C_I_CI";
            this.spinEditDpch_C_I_CI.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditDpch_C_I_CI.Properties.Appearance.Options.UseFont = true;
            this.spinEditDpch_C_I_CI.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditDpch_C_I_CI.Properties.Mask.EditMask = "f0";
            this.spinEditDpch_C_I_CI.Size = new System.Drawing.Size(96, 20);
            this.spinEditDpch_C_I_CI.TabIndex = 3;
            // 
            // spinEditPccpch_C_I_CI
            // 
            this.spinEditPccpch_C_I_CI.EditValue = new decimal(new int[] {
            3,
            0,
            0,
            -2147483648});
            this.spinEditPccpch_C_I_CI.Location = new System.Drawing.Point(121, 51);
            this.spinEditPccpch_C_I_CI.Name = "spinEditPccpch_C_I_CI";
            this.spinEditPccpch_C_I_CI.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditPccpch_C_I_CI.Properties.Appearance.Options.UseFont = true;
            this.spinEditPccpch_C_I_CI.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditPccpch_C_I_CI.Properties.Mask.EditMask = "f0";
            this.spinEditPccpch_C_I_CI.Size = new System.Drawing.Size(96, 20);
            this.spinEditPccpch_C_I_CI.TabIndex = 3;
            // 
            // labelControl62
            // 
            this.labelControl62.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl62.Appearance.Options.UseFont = true;
            this.labelControl62.Location = new System.Drawing.Point(219, 86);
            this.labelControl62.Name = "labelControl62";
            this.labelControl62.Size = new System.Drawing.Size(12, 12);
            this.labelControl62.TabIndex = 5;
            this.labelControl62.Text = "dB";
            // 
            // labelControl21
            // 
            this.labelControl21.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl21.Appearance.Options.UseFont = true;
            this.labelControl21.Location = new System.Drawing.Point(219, 55);
            this.labelControl21.Name = "labelControl21";
            this.labelControl21.Size = new System.Drawing.Size(12, 12);
            this.labelControl21.TabIndex = 5;
            this.labelControl21.Text = "dB";
            // 
            // spinEditPccpch_CI
            // 
            this.spinEditPccpch_CI.EditValue = new decimal(new int[] {
            85,
            0,
            0,
            -2147483648});
            this.spinEditPccpch_CI.Location = new System.Drawing.Point(121, 20);
            this.spinEditPccpch_CI.Name = "spinEditPccpch_CI";
            this.spinEditPccpch_CI.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditPccpch_CI.Properties.Appearance.Options.UseFont = true;
            this.spinEditPccpch_CI.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditPccpch_CI.Properties.Mask.EditMask = "f0";
            this.spinEditPccpch_CI.Size = new System.Drawing.Size(96, 20);
            this.spinEditPccpch_CI.TabIndex = 3;
            // 
            // labelControl20
            // 
            this.labelControl20.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl20.Appearance.Options.UseFont = true;
            this.labelControl20.Location = new System.Drawing.Point(219, 23);
            this.labelControl20.Name = "labelControl20";
            this.labelControl20.Size = new System.Drawing.Size(18, 12);
            this.labelControl20.TabIndex = 5;
            this.labelControl20.Text = "dBm";
            // 
            // xtraTabPage3
            // 
            this.xtraTabPage3.Controls.Add(this.groupBox17);
            this.xtraTabPage3.Controls.Add(this.groupBox13);
            this.xtraTabPage3.Controls.Add(this.groupBox12);
            this.xtraTabPage3.Controls.Add(this.groupBox11);
            this.xtraTabPage3.Name = "xtraTabPage3";
            this.xtraTabPage3.Size = new System.Drawing.Size(1078, 173);
            this.xtraTabPage3.Text = "切换类";
            // 
            // groupBox17
            // 
            this.groupBox17.Controls.Add(this.spinEditHOFrequently);
            this.groupBox17.Controls.Add(this.labelControl36);
            this.groupBox17.Controls.Add(this.labelControl37);
            this.groupBox17.Location = new System.Drawing.Point(19, 98);
            this.groupBox17.Name = "groupBox17";
            this.groupBox17.Size = new System.Drawing.Size(516, 66);
            this.groupBox17.TabIndex = 5;
            this.groupBox17.TabStop = false;
            this.groupBox17.Text = "切换频繁";
            // 
            // spinEditHOFrequently
            // 
            this.spinEditHOFrequently.EditValue = new decimal(new int[] {
            20,
            0,
            0,
            0});
            this.spinEditHOFrequently.Location = new System.Drawing.Point(62, 27);
            this.spinEditHOFrequently.Name = "spinEditHOFrequently";
            this.spinEditHOFrequently.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditHOFrequently.Properties.Appearance.Options.UseFont = true;
            this.spinEditHOFrequently.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditHOFrequently.Properties.Mask.EditMask = "f0";
            this.spinEditHOFrequently.Size = new System.Drawing.Size(96, 20);
            this.spinEditHOFrequently.TabIndex = 4;
            // 
            // labelControl36
            // 
            this.labelControl36.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl36.Appearance.Options.UseFont = true;
            this.labelControl36.Location = new System.Drawing.Point(170, 30);
            this.labelControl36.Name = "labelControl36";
            this.labelControl36.Size = new System.Drawing.Size(120, 12);
            this.labelControl36.TabIndex = 0;
            this.labelControl36.Text = "秒内出现切换频繁事件";
            // 
            // labelControl37
            // 
            this.labelControl37.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl37.Appearance.Options.UseFont = true;
            this.labelControl37.Location = new System.Drawing.Point(37, 30);
            this.labelControl37.Name = "labelControl37";
            this.labelControl37.Size = new System.Drawing.Size(12, 12);
            this.labelControl37.TabIndex = 0;
            this.labelControl37.Text = "在";
            // 
            // groupBox13
            // 
            this.groupBox13.Controls.Add(this.labelControl34);
            this.groupBox13.Location = new System.Drawing.Point(556, 98);
            this.groupBox13.Name = "groupBox13";
            this.groupBox13.Size = new System.Drawing.Size(458, 66);
            this.groupBox13.TabIndex = 0;
            this.groupBox13.TabStop = false;
            this.groupBox13.Text = "孤岛效应";
            // 
            // labelControl34
            // 
            this.labelControl34.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl34.Appearance.Options.UseFont = true;
            this.labelControl34.Location = new System.Drawing.Point(28, 30);
            this.labelControl34.Name = "labelControl34";
            this.labelControl34.Size = new System.Drawing.Size(132, 12);
            this.labelControl34.TabIndex = 5;
            this.labelControl34.Text = "孤岛效应期间出现高BLER";
            // 
            // groupBox12
            // 
            this.groupBox12.Controls.Add(this.spinEditPccpch_sSubn_Unreasonable);
            this.groupBox12.Controls.Add(this.labelControl38);
            this.groupBox12.Controls.Add(this.labelControl39);
            this.groupBox12.Location = new System.Drawing.Point(556, 12);
            this.groupBox12.Name = "groupBox12";
            this.groupBox12.Size = new System.Drawing.Size(458, 66);
            this.groupBox12.TabIndex = 0;
            this.groupBox12.TabStop = false;
            this.groupBox12.Text = "切换不合理";
            // 
            // spinEditPccpch_sSubn_Unreasonable
            // 
            this.spinEditPccpch_sSubn_Unreasonable.EditValue = new decimal(new int[] {
            3,
            0,
            0,
            0});
            this.spinEditPccpch_sSubn_Unreasonable.Location = new System.Drawing.Point(276, 27);
            this.spinEditPccpch_sSubn_Unreasonable.Name = "spinEditPccpch_sSubn_Unreasonable";
            this.spinEditPccpch_sSubn_Unreasonable.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditPccpch_sSubn_Unreasonable.Properties.Appearance.Options.UseFont = true;
            this.spinEditPccpch_sSubn_Unreasonable.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditPccpch_sSubn_Unreasonable.Properties.Mask.EditMask = "f0";
            this.spinEditPccpch_sSubn_Unreasonable.Size = new System.Drawing.Size(96, 20);
            this.spinEditPccpch_sSubn_Unreasonable.TabIndex = 4;
            // 
            // labelControl38
            // 
            this.labelControl38.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl38.Appearance.Options.UseFont = true;
            this.labelControl38.Location = new System.Drawing.Point(30, 31);
            this.labelControl38.Name = "labelControl38";
            this.labelControl38.Size = new System.Drawing.Size(240, 12);
            this.labelControl38.TabIndex = 0;
            this.labelControl38.Text = "切换后3秒内曾出现 主服场强 - 邻区场强 ≤";
            // 
            // labelControl39
            // 
            this.labelControl39.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl39.Appearance.Options.UseFont = true;
            this.labelControl39.Location = new System.Drawing.Point(378, 31);
            this.labelControl39.Name = "labelControl39";
            this.labelControl39.Size = new System.Drawing.Size(18, 12);
            this.labelControl39.TabIndex = 6;
            this.labelControl39.Text = "dBm";
            // 
            // groupBox11
            // 
            this.groupBox11.Controls.Add(this.spinEditHOLAfter);
            this.groupBox11.Controls.Add(this.labelControl33);
            this.groupBox11.Controls.Add(this.spinEditHOLBefore);
            this.groupBox11.Controls.Add(this.labelControl32);
            this.groupBox11.Controls.Add(this.labelControl18);
            this.groupBox11.Location = new System.Drawing.Point(19, 12);
            this.groupBox11.Name = "groupBox11";
            this.groupBox11.Size = new System.Drawing.Size(516, 66);
            this.groupBox11.TabIndex = 0;
            this.groupBox11.TabStop = false;
            this.groupBox11.Text = "切换不及时";
            // 
            // spinEditHOLAfter
            // 
            this.spinEditHOLAfter.EditValue = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.spinEditHOLAfter.Location = new System.Drawing.Point(226, 27);
            this.spinEditHOLAfter.Name = "spinEditHOLAfter";
            this.spinEditHOLAfter.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditHOLAfter.Properties.Appearance.Options.UseFont = true;
            this.spinEditHOLAfter.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditHOLAfter.Properties.Mask.EditMask = "f0";
            this.spinEditHOLAfter.Size = new System.Drawing.Size(96, 20);
            this.spinEditHOLAfter.TabIndex = 4;
            // 
            // labelControl33
            // 
            this.labelControl33.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl33.Appearance.Options.UseFont = true;
            this.labelControl33.Location = new System.Drawing.Point(331, 30);
            this.labelControl33.Name = "labelControl33";
            this.labelControl33.Size = new System.Drawing.Size(138, 12);
            this.labelControl33.TabIndex = 0;
            this.labelControl33.Text = "秒 内出现切换不及时事件";
            // 
            // spinEditHOLBefore
            // 
            this.spinEditHOLBefore.EditValue = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.spinEditHOLBefore.Location = new System.Drawing.Point(62, 27);
            this.spinEditHOLBefore.Name = "spinEditHOLBefore";
            this.spinEditHOLBefore.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditHOLBefore.Properties.Appearance.Options.UseFont = true;
            this.spinEditHOLBefore.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditHOLBefore.Properties.Mask.EditMask = "f0";
            this.spinEditHOLBefore.Size = new System.Drawing.Size(96, 20);
            this.spinEditHOLBefore.TabIndex = 4;
            // 
            // labelControl32
            // 
            this.labelControl32.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl32.Appearance.Options.UseFont = true;
            this.labelControl32.Location = new System.Drawing.Point(170, 30);
            this.labelControl32.Name = "labelControl32";
            this.labelControl32.Size = new System.Drawing.Size(42, 12);
            this.labelControl32.TabIndex = 0;
            this.labelControl32.Text = "秒 或后";
            // 
            // labelControl18
            // 
            this.labelControl18.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl18.Appearance.Options.UseFont = true;
            this.labelControl18.Location = new System.Drawing.Point(24, 30);
            this.labelControl18.Name = "labelControl18";
            this.labelControl18.Size = new System.Drawing.Size(24, 12);
            this.labelControl18.TabIndex = 0;
            this.labelControl18.Text = "在前";
            // 
            // xtraTabPage4
            // 
            this.xtraTabPage4.Controls.Add(this.groupBox15);
            this.xtraTabPage4.Controls.Add(this.groupBox14);
            this.xtraTabPage4.Name = "xtraTabPage4";
            this.xtraTabPage4.Size = new System.Drawing.Size(1078, 173);
            this.xtraTabPage4.Text = "故障类";
            // 
            // groupBox15
            // 
            this.groupBox15.Controls.Add(this.labelControl63);
            this.groupBox15.Location = new System.Drawing.Point(22, 14);
            this.groupBox15.Name = "groupBox15";
            this.groupBox15.Size = new System.Drawing.Size(355, 66);
            this.groupBox15.TabIndex = 3;
            this.groupBox15.TabStop = false;
            this.groupBox15.Text = "终端故障";
            // 
            // labelControl63
            // 
            this.labelControl63.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl63.Appearance.Options.UseFont = true;
            this.labelControl63.Location = new System.Drawing.Point(26, 27);
            this.labelControl63.Name = "labelControl63";
            this.labelControl63.Size = new System.Drawing.Size(258, 12);
            this.labelControl63.TabIndex = 0;
            this.labelControl63.Text = "在起呼过程中或挂机（掉话）前3秒，出现高BLER";
            // 
            // groupBox14
            // 
            this.groupBox14.Controls.Add(this.labelControl42);
            this.groupBox14.Controls.Add(this.labelControl43);
            this.groupBox14.Controls.Add(this.spinEditDpch_Antenna);
            this.groupBox14.Location = new System.Drawing.Point(402, 14);
            this.groupBox14.Name = "groupBox14";
            this.groupBox14.Size = new System.Drawing.Size(355, 66);
            this.groupBox14.TabIndex = 3;
            this.groupBox14.TabStop = false;
            this.groupBox14.Text = "智能天线故障";
            // 
            // labelControl42
            // 
            this.labelControl42.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl42.Appearance.Options.UseFont = true;
            this.labelControl42.Location = new System.Drawing.Point(26, 29);
            this.labelControl42.Name = "labelControl42";
            this.labelControl42.Size = new System.Drawing.Size(96, 12);
            this.labelControl42.TabIndex = 0;
            this.labelControl42.Text = "DPCH_RSCP突降 ≥";
            // 
            // labelControl43
            // 
            this.labelControl43.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl43.Appearance.Options.UseFont = true;
            this.labelControl43.Location = new System.Drawing.Point(230, 29);
            this.labelControl43.Name = "labelControl43";
            this.labelControl43.Size = new System.Drawing.Size(12, 12);
            this.labelControl43.TabIndex = 2;
            this.labelControl43.Text = "dB";
            // 
            // spinEditDpch_Antenna
            // 
            this.spinEditDpch_Antenna.EditValue = new decimal(new int[] {
            30,
            0,
            0,
            0});
            this.spinEditDpch_Antenna.Location = new System.Drawing.Point(128, 24);
            this.spinEditDpch_Antenna.Name = "spinEditDpch_Antenna";
            this.spinEditDpch_Antenna.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditDpch_Antenna.Properties.Appearance.Options.UseFont = true;
            this.spinEditDpch_Antenna.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditDpch_Antenna.Size = new System.Drawing.Size(96, 20);
            this.spinEditDpch_Antenna.TabIndex = 1;
            // 
            // TDBlerSettingDlg
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1085, 571);
            this.Controls.Add(this.splitContainerControl1);
            this.Name = "TDBlerSettingDlg";
            this.Text = "BLER条件设置";
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            this.groupBox7.ResumeLayout(false);
            this.groupBox7.PerformLayout();
            this.groupBox16.ResumeLayout(false);
            this.groupBox16.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.checkedListBoxControlReason)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).EndInit();
            this.splitContainerControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).EndInit();
            this.xtraTabControl1.ResumeLayout(false);
            this.xtraTabPage1.ResumeLayout(false);
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditCornerAfter.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditCornerBefore.Properties)).EndInit();
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditPccpch_Weak.Properties)).EndInit();
            this.groupBox5.ResumeLayout(false);
            this.groupBox5.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditNum_Pullotion.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditPccpch_Pullotion.Properties)).EndInit();
            this.groupBox3.ResumeLayout(false);
            this.groupBox3.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditRatio_Coverlap.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditPccpch_Coverlap.Properties)).EndInit();
            this.groupBox4.ResumeLayout(false);
            this.groupBox4.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditAngle_Dorsad.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditDistance_Dorsad.Properties)).EndInit();
            this.xtraTabPage2.ResumeLayout(false);
            this.groupBox10.ResumeLayout(false);
            this.groupBox10.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditPccpch_Sub_Dpch.Properties)).EndInit();
            this.groupBox9.ResumeLayout(false);
            this.groupBox9.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditCorrelation.Properties)).EndInit();
            this.groupBox8.ResumeLayout(false);
            this.groupBox8.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditTxPower_UpInterrupt.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditPccpch_C_I_UpInterrupt.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditPccpch_UpInterrupt.Properties)).EndInit();
            this.groupBox6.ResumeLayout(false);
            this.groupBox6.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditDpch_C_I_CI.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditPccpch_C_I_CI.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditPccpch_CI.Properties)).EndInit();
            this.xtraTabPage3.ResumeLayout(false);
            this.groupBox17.ResumeLayout(false);
            this.groupBox17.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditHOFrequently.Properties)).EndInit();
            this.groupBox13.ResumeLayout(false);
            this.groupBox13.PerformLayout();
            this.groupBox12.ResumeLayout(false);
            this.groupBox12.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditPccpch_sSubn_Unreasonable.Properties)).EndInit();
            this.groupBox11.ResumeLayout(false);
            this.groupBox11.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditHOLAfter.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditHOLBefore.Properties)).EndInit();
            this.xtraTabPage4.ResumeLayout(false);
            this.groupBox15.ResumeLayout(false);
            this.groupBox15.PerformLayout();
            this.groupBox14.ResumeLayout(false);
            this.groupBox14.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditDpch_Antenna.Properties)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.GroupControl groupControl1;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl1;
        private DevExpress.XtraTab.XtraTabControl xtraTabControl1;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage1;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage2;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage3;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage4;
        private System.Windows.Forms.GroupBox groupBox2;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.SpinEdit spinEditPccpch_Weak;
        private System.Windows.Forms.GroupBox groupBox5;
        private DevExpress.XtraEditors.LabelControl labelControl10;
        private DevExpress.XtraEditors.LabelControl labelControl11;
        private DevExpress.XtraEditors.LabelControl labelControl13;
        private DevExpress.XtraEditors.SpinEdit spinEditNum_Pullotion;
        private DevExpress.XtraEditors.SpinEdit spinEditPccpch_Pullotion;
        private System.Windows.Forms.GroupBox groupBox3;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private DevExpress.XtraEditors.LabelControl labelControl5;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.SpinEdit spinEditRatio_Coverlap;
        private DevExpress.XtraEditors.SpinEdit spinEditPccpch_Coverlap;
        private System.Windows.Forms.GroupBox groupBox4;
        private DevExpress.XtraEditors.LabelControl labelControl6;
        private DevExpress.XtraEditors.LabelControl labelControl7;
        private DevExpress.XtraEditors.LabelControl labelControl8;
        private DevExpress.XtraEditors.LabelControl labelControl9;
        private DevExpress.XtraEditors.SpinEdit spinEditDistance_Dorsad;
        private DevExpress.XtraEditors.SpinEdit spinEditAngle_Dorsad;
        private DevExpress.XtraEditors.LabelControl labelControl14;
        private System.Windows.Forms.GroupBox groupBox1;
        private DevExpress.XtraEditors.LabelControl labelControl12;
        private System.Windows.Forms.GroupBox groupBox6;
        private DevExpress.XtraEditors.LabelControl labelControl22;
        private DevExpress.XtraEditors.LabelControl labelControl19;
        private DevExpress.XtraEditors.SpinEdit spinEditPccpch_C_I_CI;
        private DevExpress.XtraEditors.LabelControl labelControl21;
        private DevExpress.XtraEditors.SpinEdit spinEditPccpch_CI;
        private DevExpress.XtraEditors.LabelControl labelControl20;
        private System.Windows.Forms.GroupBox groupBox8;
        private DevExpress.XtraEditors.LabelControl labelControl24;
        private DevExpress.XtraEditors.LabelControl labelControl25;
        private DevExpress.XtraEditors.LabelControl labelControl26;
        private DevExpress.XtraEditors.SpinEdit spinEditTxPower_UpInterrupt;
        private DevExpress.XtraEditors.SpinEdit spinEditPccpch_C_I_UpInterrupt;
        private DevExpress.XtraEditors.LabelControl labelControl27;
        private DevExpress.XtraEditors.SpinEdit spinEditPccpch_UpInterrupt;
        private DevExpress.XtraEditors.LabelControl labelControl28;
        private DevExpress.XtraEditors.LabelControl labelControl23;
        private DevExpress.XtraEditors.SpinEdit spinEditDpch_C_I_CI;
        private System.Windows.Forms.GroupBox groupBox10;
        private DevExpress.XtraEditors.LabelControl labelControl30;
        private DevExpress.XtraEditors.SpinEdit spinEditPccpch_Sub_Dpch;
        private DevExpress.XtraEditors.LabelControl labelControl31;
        private System.Windows.Forms.GroupBox groupBox9;
        private DevExpress.XtraEditors.LabelControl labelControl29;
        private DevExpress.XtraEditors.SpinEdit spinEditCorrelation;
        private System.Windows.Forms.GroupBox groupBox13;
        private System.Windows.Forms.GroupBox groupBox12;
        private System.Windows.Forms.GroupBox groupBox11;
        private DevExpress.XtraEditors.SpinEdit spinEditPccpch_sSubn_Unreasonable;
        private DevExpress.XtraEditors.LabelControl labelControl38;
        private DevExpress.XtraEditors.LabelControl labelControl39;
        private DevExpress.XtraEditors.LabelControl labelControl43;
        private DevExpress.XtraEditors.SpinEdit spinEditDpch_Antenna;
        private DevExpress.XtraEditors.LabelControl labelControl42;
        private System.Windows.Forms.GroupBox groupBox14;
        private DevExpress.XtraEditors.SimpleButton simpleButtonCancel;
        private DevExpress.XtraEditors.SimpleButton simpleButtonOK;
        private DevExpress.XtraEditors.LabelControl labelControl62;
        private System.Windows.Forms.GroupBox groupBox15;
        private DevExpress.XtraEditors.LabelControl labelControl63;
        private DevExpress.XtraEditors.CheckedListBoxControl checkedListBoxControlReason;
        private DevExpress.XtraEditors.SimpleButton simpleButtonUp;
        private DevExpress.XtraEditors.SimpleButton simpleButtonDown;
        private System.Windows.Forms.TextBox tbxDescription;
        private DevExpress.XtraEditors.LabelControl labelControl16;
        private DevExpress.XtraEditors.LabelControl labelControl15;
        private DevExpress.XtraEditors.SpinEdit spinEditCornerBefore;
        private DevExpress.XtraEditors.LabelControl labelControl17;
        private DevExpress.XtraEditors.SpinEdit spinEditCornerAfter;
        private DevExpress.XtraEditors.SpinEdit spinEditHOLAfter;
        private DevExpress.XtraEditors.LabelControl labelControl33;
        private DevExpress.XtraEditors.SpinEdit spinEditHOLBefore;
        private DevExpress.XtraEditors.LabelControl labelControl32;
        private DevExpress.XtraEditors.LabelControl labelControl18;
        private System.Windows.Forms.GroupBox groupBox16;
        private System.Windows.Forms.GroupBox groupBox7;
        private System.Windows.Forms.TextBox tbxSuggest;
        private DevExpress.XtraEditors.LabelControl labelControl34;
        private System.Windows.Forms.GroupBox groupBox17;
        private DevExpress.XtraEditors.SpinEdit spinEditHOFrequently;
        private DevExpress.XtraEditors.LabelControl labelControl36;
        private DevExpress.XtraEditors.LabelControl labelControl37;

    }
}