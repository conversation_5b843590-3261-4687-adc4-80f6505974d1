﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc.ZTSCellNCellInfo;

namespace MasterCom.RAMS.ZTFunc
{
    public class QuerySCellNCellSignalLevDiff : DIYAnalyseByFileBackgroundBase
    {
        protected static readonly object lockObj = new object();
        private static QuerySCellNCellSignalLevDiff instance = null;
        public static QuerySCellNCellSignalLevDiff GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new QuerySCellNCellSignalLevDiff();
                    }
                }
            }
            return instance;
        }

        protected QuerySCellNCellSignalLevDiff()
            : base(MainModel.GetInstance())
        {
            this.IncludeMessage = false;
            this.IncludeEvent = false;
        }

        public override string Name
        {
            get
            {
                return "主服邻区场强信息查询(按区域)";
            }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 12000, 12083, this.Name);
        }

        protected override void fireShowForm()
        {
            SCellNCellSignalInfoForm frm = MainModel.GetObjectFromBlackboard(typeof(SCellNCellSignalInfoForm))
                 as SCellNCellSignalInfoForm;
            if (frm == null)
            {
                frm = new SCellNCellSignalInfoForm();
                frm.Owner = MainModel.MainForm;
            }
            frm.FillData(cells);
            frm.Visible = true;
            frm.BringToFront();
            cells = null;
        }

        protected bool saveTp = false;
        protected int levDiff = 3;
        protected override bool getCondition()
        {
            SLevNLevDiffDlg dlg = new SLevNLevDiffDlg();
            dlg.SetCondition(levDiff, saveTp);
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            dlg.GetCondition(out levDiff, out saveTp);
            cells = new List<SCellInfo>();
            return true;
        }

        protected List<SCellInfo> cells = null;
        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
            {
                Dictionary<ICell, SCellInfo> sCellDic = getSCellDic(file);

                foreach (SCellInfo sCell in sCellDic.Values)
                {
                    if (sCell.NCells.Count > 0)
                    {
                        this.cells.Add(sCell);
                    }
                }
            }
        }

        private Dictionary<ICell, SCellInfo> getSCellDic(DTFileDataManager file)
        {
            Dictionary<ICell, SCellInfo> sCellDic = new Dictionary<ICell, SCellInfo>();
            foreach (TestPoint tp in file.TestPoints)
            {
                if (!isValidTestPoint(tp))
                {
                    continue;
                }
                ICell sCell = tp.GetMainCell();
                if (sCell == null)
                {
                    continue;
                }
                float? rxLevSub = (float?)(short?)tp["RxLevSub"];
                if (rxLevSub == null || rxLevSub < -120 || rxLevSub > -10)
                {
                    continue;
                }

                SCellInfo sCellInfo;
                if (!sCellDic.TryGetValue(sCell, out sCellInfo))
                {
                    sCellInfo = new SCellInfo();
                    sCellInfo.Cell = sCell;
                    sCellInfo.FileName = file.FileName;
                    sCellDic[sCell] = sCellInfo;
                }
                dealNCell(tp, rxLevSub, sCellInfo);
            }

            return sCellDic;
        }

        private void dealNCell(TestPoint tp, float? rxLevSub, SCellInfo sCellInfo)
        {
            if (tp is TDTestPointDetail || tp is TDTestPointSummary
                || tp is LTETestPointDetail || tp is LTEUepTestPoint)
            {
                return;
            }

            for (int i = 0; i < 10; i++)
            {
                int? nRxLev = (int?)(short?)tp["N_RxLev", i];
                if (nRxLev == null)
                {
                    continue;
                }
                if (nRxLev > rxLevSub
                    || Math.Abs((float)(int)nRxLev - (float)rxLevSub) <= levDiff)
                {
                    ICell nCell = tp.GetNBCell_GSM(i);
                    if (nCell != null)
                    {
                        sCellInfo.AddNCell(saveTp ? tp : null, (float)rxLevSub, nCell, (float)(int)nRxLev);
                    }
                }

            }
        }
    }
}
