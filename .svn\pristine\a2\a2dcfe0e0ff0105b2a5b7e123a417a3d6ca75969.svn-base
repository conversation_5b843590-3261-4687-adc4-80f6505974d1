﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.ZTFunc.ZTPoorRSRQCell;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class QueryPoorRsrqCell_NR : QueryPoorRsrqCellBase
    {
        protected override string themeName { get { return "NR:SS_RSRQ"; } }
        protected override string rsrpName { get { return "NR_SS_RSRP"; } }
        protected override string sinrName { get { return "NR_SS_SINR"; } }
        protected override string rsrqName { get { return "NR_SS_RSRQ"; } }

        protected static readonly object lockObj = new object();
        private static QueryPoorRsrqCell_NR intance = null;
        public static QueryPoorRsrqCell_NR Instance
        {
            get
            {
                if (intance == null)
                {
                    lock (lockObj)
                    {
                        if (intance == null)
                        {
                            intance = new QueryPoorRsrqCell_NR();
                        }
                    }
                }
                return intance;
            }
        }

        public QueryPoorRsrqCell_NR()
            : base()
        {
            Columns = NRTpHelper.InitBaseReplayParamBackground(false, false);
            Columns.Add("NR_SS_RSRQ");
            Columns.Add("NR_lte_RSRP");
            Columns.Add("NR_lte_SINR");
            ServiceTypes = ServiceTypeManager.GetServiceTypesByServiceName(ServiceName.NR);
        }

        public override string Name
        {
            get { return "低RSRQ小区_NR"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 35000, 35011, this.Name);
        }

        protected override void addPoorCell(TestPoint testPoint)
        {
            NRCell cell = testPoint.GetMainCell_NR();
            if (cell != null)
            {
                float? rsrq = getRsrq(testPoint);
                if (rsrq != null)
                {
                    bool poorPt = poorCondition.IsPoorRsrq((float)rsrq);
                    PoorRsrqCell poorCell;
                    if (!poorCellDic.TryGetValue(cell.Token, out poorCell))
                    {
                        poorCell = new PoorRsrqCell_NR(cell);
                        poorCellDic[cell.Token] = poorCell;
                    }
                    poorCell.AddTestPoint(testPoint, (float)rsrq, poorPt);
                    poorCell.AddOtherTPInfo(testPoint);
                }
            }
        }

        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }
            SettingFormNR dlg = new SettingFormNR(poorCondition);
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                poorCondition = dlg.GetCondition();
                return true;
            }
            return false;
        }

        protected override void fireShowForm()
        {
            if (poorCellDic.Count == 0)
            {
                MessageBox.Show("没有符合条件的信息！");
                return;
            }
            ResultFormNR frm = MainModel.GetInstance().GetObjectFromBlackboard(typeof(ResultFormNR)) as ResultFormNR;
            if (frm == null || frm.IsDisposed)
            {
                frm = new ResultFormNR();
                frm.Owner = MainModel.MainForm;
            }

            List<PoorRsrqCell_NR> resList = new List<PoorRsrqCell_NR>();
            foreach (var item in poorCellDic.Values)
            {
                resList.Add(item as PoorRsrqCell_NR); 
            }

            frm.FillData(resList);
            frm.Visible = true;
            frm.BringToFront();
            poorCellDic = null;
        }

        protected override float? getRsrq(TestPoint tp)
        {
            return NRTpHelper.NrTpManager.GetSCellRsrq(tp);
        }
    }

    public class NRQueryPoorRsrqCellByFile : QueryPoorRsrqCell_NR
    {
        private static NRQueryPoorRsrqCellByFile instance = null;
        public static NRQueryPoorRsrqCellByFile GetInstance()
        {
            if (instance == null)
            {
                instance = new NRQueryPoorRsrqCellByFile();
            }
            return instance;
        }

        public override string Name
        {
            get { return "低RSRQ小区_NR(按文件)"; }
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MainModel.NeedSearchType.File;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            return true;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                return true;
            }
            return false;
        }
    }
}
