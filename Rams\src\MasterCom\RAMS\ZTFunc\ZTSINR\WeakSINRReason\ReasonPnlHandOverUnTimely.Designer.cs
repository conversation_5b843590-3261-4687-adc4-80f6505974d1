﻿namespace MasterCom.RAMS.ZTFunc.ZTSINR.WeakSINRReason
{
    partial class ReasonPnlHandOverUnTimely
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.label3 = new System.Windows.Forms.Label();
            this.label1 = new System.Windows.Forms.Label();
            this.numLastTime = new DevExpress.XtraEditors.SpinEdit();
            this.label2 = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.rsrpDiffer = new DevExpress.XtraEditors.SpinEdit();
            this.numBeforeTime = new DevExpress.XtraEditors.SpinEdit();
            ((System.ComponentModel.ISupportInitialize)(this.grp)).BeginInit();
            this.grp.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numLastTime.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rsrpDiffer.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numBeforeTime.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // grp
            // 
            this.grp.Controls.Add(this.numBeforeTime);
            this.grp.Controls.Add(this.rsrpDiffer);
            this.grp.Controls.Add(this.numLastTime);
            this.grp.Controls.Add(this.label4);
            this.grp.Controls.Add(this.label1);
            this.grp.Controls.Add(this.label2);
            this.grp.Controls.Add(this.label3);
            this.grp.Size = new System.Drawing.Size(606, 71);
            this.grp.Text = "切换不及时";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(36, 39);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(77, 12);
            this.label3.TabIndex = 0;
            this.label3.Text = "在质差点的前";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(311, 39);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(137, 12);
            this.label1.TabIndex = 1;
            this.label1.Text = "秒邻区信号强度超过主服";
            // 
            // numLastTime
            // 
            this.numLastTime.EditValue = new decimal(new int[] {
            3,
            0,
            0,
            0});
            this.numLastTime.Location = new System.Drawing.Point(251, 34);
            this.numLastTime.Name = "numLastTime";
            this.numLastTime.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numLastTime.Properties.MaxValue = new decimal(new int[] {
            20,
            0,
            0,
            0});
            this.numLastTime.Properties.MinValue = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numLastTime.Size = new System.Drawing.Size(54, 21);
            this.numLastTime.TabIndex = 2;
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(180, 39);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(65, 12);
            this.label2.TabIndex = 0;
            this.label2.Text = "秒内，存在";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(514, 39);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(53, 12);
            this.label4.TabIndex = 1;
            this.label4.Text = "dB的情况";
            // 
            // rsrpDiffer
            // 
            this.rsrpDiffer.EditValue = new decimal(new int[] {
            3,
            0,
            0,
            0});
            this.rsrpDiffer.Location = new System.Drawing.Point(454, 34);
            this.rsrpDiffer.Name = "rsrpDiffer";
            this.rsrpDiffer.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.rsrpDiffer.Properties.MaxValue = new decimal(new int[] {
            20,
            0,
            0,
            0});
            this.rsrpDiffer.Properties.MinValue = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.rsrpDiffer.Size = new System.Drawing.Size(54, 21);
            this.rsrpDiffer.TabIndex = 2;
            // 
            // numBeforeTime
            // 
            this.numBeforeTime.EditValue = new decimal(new int[] {
            3,
            0,
            0,
            0});
            this.numBeforeTime.Location = new System.Drawing.Point(120, 34);
            this.numBeforeTime.Name = "numBeforeTime";
            this.numBeforeTime.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numBeforeTime.Properties.MaxValue = new decimal(new int[] {
            20,
            0,
            0,
            0});
            this.numBeforeTime.Properties.MinValue = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numBeforeTime.Size = new System.Drawing.Size(54, 21);
            this.numBeforeTime.TabIndex = 3;
            // 
            // ReasonPnlHandOverUnTimely
            // 
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.Name = "ReasonPnlHandOverUnTimely";
            this.Size = new System.Drawing.Size(606, 71);
            ((System.ComponentModel.ISupportInitialize)(this.grp)).EndInit();
            this.grp.ResumeLayout(false);
            this.grp.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numLastTime.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rsrpDiffer.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numBeforeTime.Properties)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label1;
        private DevExpress.XtraEditors.SpinEdit rsrpDiffer;
        private DevExpress.XtraEditors.SpinEdit numLastTime;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.Label label2;
        private DevExpress.XtraEditors.SpinEdit numBeforeTime;
    }
}
