﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;
using System.Globalization;

namespace MasterCom.RAMS.ZTFunc
{
    /// <summary>
    /// 性能数据钻取
    /// </summary>
    class PerDetailInfQuery : DIYSQLBase
    {
        private readonly bool IsTDIn = false;
        /// <summary>
        /// 限制的指标名称
        /// </summary>
        public string EleName { get; set; }

        private DateTime timeStart;
        public DateTime TimeStart
        {
            get
            {
                return timeStart;
            }
            set
            {
                timeStart = new DateTime(value.Year, value.Month, value.Day);
            }
        }
        public DateTime TimeEnd { get; set; }
        public PerDetailInfQuery(MainModel mainModel, bool IsTDIn)
            : base(mainModel)
        {
            this.IsTDIn = IsTDIn;
            timeStart = DateTime.MaxValue;
            TimeEnd = DateTime.MaxValue;
        }

        protected override string getSqlTextString()
        {
            //this.SetQueryCondition(
            if (timeStart == DateTime.MaxValue && TimeEnd == DateTime.MaxValue)
            {
                TimePeriod timePeriod = condition.Periods[0];
                timeStart = timePeriod.BeginTime;
                TimeEnd = timePeriod.EndTime;
            }
            //根据时间求取表名和表时间限制
            GetTableNameAndDtRegion();
            if (allTableNameAndRegion == null || allTableNameAndRegion.Count == 0)
            {
                return "";
            }
            //MainModel.
            if (IsTDIn)
            {
                string eleConfigStr = getEleConfigStr(PerDetailInfSqlStatement.TDPerTreadHoldInfs);

                StringBuilder statement_1;
                StringBuilder statement_2;
                DateTime timeTemp = new DateTime(timeStart.Year, timeStart.Month, timeStart.Day);
                //将时间替换了
                statement_1 = new StringBuilder(PerDetailInfSqlStatement.TD_1.Replace("#1_#2", allTableNameAndRegion[0].TableName));
                statement_1 = statement_1.Replace("#3", eleConfigStr);
                setStatement_1(statement_1, 0, PerDetailInfSqlStatement.TDSumNotZero);

                statement_2 = new StringBuilder(PerDetailInfSqlStatement.TD_Count1.Replace("#1_#2", allTableNameAndRegion[0].TableName));
                setStatement_2(statement_2, 0, PerDetailInfSqlStatement.TDSumNotZero);
                for (int i = 1; i < allTableNameAndRegion.Count; i++)
                {
                    //将时间替换了
                    StringBuilder temp = new StringBuilder(PerDetailInfSqlStatement.TD_2.Replace("#1_#2", allTableNameAndRegion[i].TableName));
                    statement_1.Append("\r\n" + temp.Replace("#3", eleConfigStr));
                    setStatement_1(statement_1, i, PerDetailInfSqlStatement.TDSumNotZero);

                    temp = new StringBuilder(PerDetailInfSqlStatement.TD_Count2.Replace("#1_#2", allTableNameAndRegion[i].TableName));
                    statement_2.Append(temp.ToString());
                    setStatement_2(statement_2, i, PerDetailInfSqlStatement.TDSumNotZero);

                    timeTemp = timeTemp.AddDays(1);
                }
                StringBuilder statement = new StringBuilder(statement_1.ToString() + "\r\n" + statement_2.ToString());
                statement.Append("\r\n" + PerDetailInfSqlStatement.TDRes.ToString() + "\r\n" + PerDetailInfSqlStatement.DropTableTD.ToString());

                return statement.ToString();
            }
            else
            {
                string eleConfigStr = getEleConfigStr(PerDetailInfSqlStatement.GSMPerTreadHoldInfs);

                StringBuilder statement_1;
                StringBuilder statement_2;
                DateTime timeTemp = new DateTime(timeStart.Year, timeStart.Month, timeStart.Day);
                //将时间替换了
                statement_1 = new StringBuilder(PerDetailInfSqlStatement.GSM_1.Replace("#1_#2", allTableNameAndRegion[0].TableName));
                statement_1 = statement_1.Replace("#3", eleConfigStr);
                setStatement_1(statement_1, 0, PerDetailInfSqlStatement.GSMSumNotZero);

                statement_2 = new StringBuilder(PerDetailInfSqlStatement.GSM_Count1.Replace("#1_#2", allTableNameAndRegion[0].TableName));
                setStatement_2(statement_2, 0, PerDetailInfSqlStatement.GSMSumNotZero);
                for (int i = 1; i < allTableNameAndRegion.Count; i++)
                {
                    //将时间替换了
                    StringBuilder temp = new StringBuilder(PerDetailInfSqlStatement.GSM_2.Replace("#1_#2", allTableNameAndRegion[i].TableName));
                    statement_1.Append("\r\n" + temp.Replace("#3", eleConfigStr));
                    setStatement_1(statement_1, i, PerDetailInfSqlStatement.GSMSumNotZero);

                    temp = new StringBuilder(PerDetailInfSqlStatement.GSM_Count2.Replace("#1_#2", allTableNameAndRegion[i].TableName));
                    statement_2.Append(temp.ToString());
                    setStatement_2(statement_2, i, PerDetailInfSqlStatement.GSMSumNotZero);

                    timeTemp = timeTemp.AddDays(1);
                }
                StringBuilder statement = new StringBuilder(statement_1.ToString() + "\r\n" + statement_2.ToString());
                statement.Append("\r\n" + PerDetailInfSqlStatement.GSMRes.ToString() + "\r\n" + PerDetailInfSqlStatement.DropTableGSM.ToString());

                return statement.ToString();
            }
        }

        private void setStatement_2(StringBuilder statement_2, int index, string sumNotZero)
        {
            statement_2.Append("\r\n where tmdat >= " + allTableNameAndRegion[index].StartTmdat +
                "\r\n and tmdat < " + allTableNameAndRegion[index].EndTmdat);
            statement_2.Append(sumNotZero);
            statement_2.Append("\r\ngroup by LAC,CI");
        }

        private void setStatement_1(StringBuilder statement_1, int index, string sumNotZero)
        {
            statement_1.Append("\r\n and tmdat >= " + allTableNameAndRegion[index].StartTmdat +
                "\r\n and tmdat < " + allTableNameAndRegion[index].EndTmdat);
            statement_1.Append(sumNotZero);
            statement_1.Append("\r\ngroup by LAC,CI");
        }

        private string getEleConfigStr(string[] perTreadHoldInfs)
        {
            string eleConfigStr = "";
            for (int i = 0; i < perTreadHoldInfs.Length; i++)
            {
                if (perTreadHoldInfs[i].Contains(EleName))
                {
                    eleConfigStr = perTreadHoldInfs[i];
                    break;
                }
            }
            return eleConfigStr;
        }

        List<TableNameAndTimeRegion> allTableNameAndRegion;
        //根据时间获得对应的数据库表以及该表的时间范围
        private void GetTableNameAndDtRegion()
        {
            allTableNameAndRegion = new List<TableNameAndTimeRegion>();
            TableNameAndTimeRegion regionTemp = new TableNameAndTimeRegion();
            regionTemp.TableName = timeStart.Year.ToString("0000") + "_" + timeStart.Month.ToString("00");
            regionTemp.StartTmdat = "'" + timeStart.ToString("yyyy-MM-dd 00:00:00") + "'";
            if (timeStart.Year == TimeEnd.Year && TimeEnd.Month == timeStart.Month)
            {
                regionTemp.EndTmdat = "'" + TimeEnd.AddDays(1).ToString("yyyy-MM-dd 00:00:00") + "'";
                allTableNameAndRegion.Add(regionTemp);
                return;
            }
            regionTemp.EndTmdat = "'" + timeStart.AddMonths(1).ToString("yyyy-MM-01 00:00:00") + "'";
            allTableNameAndRegion.Add(regionTemp);
            DateTime tmdatTemp = timeStart;
            while (tmdatTemp.Year != TimeEnd.Year || tmdatTemp.Month != TimeEnd.Month)
            {
                if (tmdatTemp > TimeEnd)
                {
                    return;
                }
                tmdatTemp = tmdatTemp.AddMonths(1);
                regionTemp = new TableNameAndTimeRegion();
                regionTemp.TableName = tmdatTemp.Year.ToString("0000") + "_" + tmdatTemp.Month.ToString("00");
                regionTemp.StartTmdat = "'" + tmdatTemp.ToString("yyyy-MM-dd 00:00:00") + "'";
                if (tmdatTemp.Year == TimeEnd.Year && TimeEnd.Month == tmdatTemp.Month)
                {
                    regionTemp.EndTmdat = "'" + TimeEnd.AddDays(1).ToString("yyyy-MM-dd 00:00:00") + "'";
                    allTableNameAndRegion.Add(regionTemp);
                    return;
                }
                regionTemp.EndTmdat = "'" + tmdatTemp.AddMonths(1).ToString("yyyy-MM-01 00:00:00") + "'";
                allTableNameAndRegion.Add(regionTemp);
            }
        }
        
        public List<EleDetailInf> DetailInf { get; set; } = new List<EleDetailInf>();

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[3];
            rType[0] = E_VType.E_Int;
            rType[1] = E_VType.E_Int;
            rType[2] = E_VType.E_Float;
            return rType;
        }
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    fillData(package);
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                if (Math.Log(index++) * (100000 / 10000) > WaitBox.ProgressPercent)
                {
                    progress++;
                    if (progress > 95)
                    {
                        progress = 5;
                        index = 0;
                    }
                    if (progress % 5 == 0)
                    {
                        WaitBox.ProgressPercent = progress;
                    }
                }
            }
        }

        private void fillData(Package package)
        {
            try
            {
                EleDetailInf detInf = new EleDetailInf();
                detInf.LAC = package.Content.GetParamInt();
                detInf.CI = package.Content.GetParamInt();
                detInf.Radio = package.Content.GetParamFloat();
                DetailInf.Add(detInf);
            }
            catch
            {
                //continue
            }
        }

        public override string Name
        {
            get { return "PerDetailInfQuery"; }
        }
    }
    public class TableNameAndTimeRegion
    {
        public string TableName { get; set; }
        public string StartTmdat { get; set; }
        public string EndTmdat { get; set; }
        public TableNameAndTimeRegion()
        {
            TableName = "";
            StartTmdat = "";
            EndTmdat = "";
        }
    }
    public class EleDetailInf
    {
        public int LAC { get; set; }
        public int CI { get; set; }
        public double Radio { get; set; }
        public EleDetailInf()
        {
            LAC = 0;
            CI = 0;
            Radio = 0;
        }
    }

    public static class PerDetailInfSqlStatement
    {
        public const string GSM_1 = @"select LAC,CI,count(*) as countKCI into #TempDT_GSM from tb_para_cell_counter_detail_#1_#2  where #3";
        public const string GSM_2 = @"
        insert into #TempDT_GSM
        select LAC,CI, count(*) as countKCI from tb_para_cell_counter_detail_#1_#2
        where #3";
        public const string GSM_Count1 = @"
        select LAC,CI,count(*) as countSum into #TempDT_GSM_Count from tb_para_cell_counter_detail_#1_#2
        ";
        public const string GSM_Count2 = @"
        insert into #TempDT_GSM_Count
        select LAC,CI,count(*)  from tb_para_cell_counter_detail_#1_#2
        ";
        public const string GSMRes = @"        	
		select LAC,CI,sum(countKCI) as Sum_1 into #sum_1 from  #TempDT_GSM group by LAC,CI
		select LAC,CI,sum(countSum) as Sum_2 into #sum_2 from #TempDT_GSM_Count group by LAC,CI
		select top 200 #sum_1.LAC,#sum_1.CI,case  when Sum_2 = 0 then 0 else Sum_1/convert(float,Sum_2) end as '比率' from #sum_1,#sum_2 where #sum_1.LAC = #sum_2.LAC and #sum_1.CI = #sum_2.CI
        order by '比率' desc 
        ";


        public const string TD_1 = @"select LAC,CI,count(*) as countKCI into #TempDT_TD from tb_para_utrancell_counter_detail_#1_#2  where #3";
        public const string TD_2 = @"
        insert into #TempDT_TD
        select LAC,CI, count(*) as countKCI from tb_para_utrancell_counter_detail_#1_#2
        where #3";
        public const string TD_Count1 = @"
        select LAC,CI,count(*) as countSum into #TempDT_TD_Count from tb_para_utrancell_counter_detail_#1_#2
        ";
        public const string TD_Count2 = @"
        insert into #TempDT_TD_Count
        select LAC,CI,count(*)  from tb_para_utrancell_counter_detail_#1_#2
        ";
        public const string TDRes = @"       	
		select LAC,CI,sum(countKCI) as Sum_1 into #sum_1 from  #TempDT_TD group by LAC,CI
		select LAC,CI,sum(countSum) as Sum_2 into #sum_2 from #TempDT_TD_Count group by LAC,CI
		select top 200 #sum_1.LAC,#sum_1.CI,case  when Sum_2 = 0 then 0 else Sum_1/convert(float,Sum_2) end as '比率' from #sum_1,#sum_2 where #sum_1.LAC = #sum_2.LAC and #sum_1.CI = #sum_2.CI
        order by '比率' desc 
        ";

        public const string GroupStatement = "group by LAC,CI";

        public const string DropTableGSM = "\r\n drop table  #sum_1\r\n drop table  #sum_2\r\n drop table  #TempDT_GSM\r\ndrop table #TempDT_GSM_Count";
        public const string DropTableTD = "\r\n drop table  #sum_1\r\n drop table  #sum_2\r\n drop table  #TempDT_TD\r\ndrop table #TempDT_TD_Count";
        /// <summary>
        /// GSM性能指标范围
        /// </summary>
        public static string[] GSMPerTreadHoldInfs { get; } = new string[] {
            "([信令信道分配成功率] < 0.98)",
            "([话音信道分配成功率(不含切)] < 0.98 )",
            "([无线接通率]<0.96 )",
            "[信令信道拥塞率]>0.005",
            "[话音信道拥塞率(不含切)]>0.005",
            "[话音信道掉话率(不含切)]>0.02",
            "[非PBGT切换占比]>0.5",
            "([下行话音质量]<0.9 )",
            "[切换成功率]<0.95)",
            "([上行TBF建立成功率]<0.95)",
            "([下行TBF建立成功率]<0.98)",
            "[下行TBF掉线率]>0.05",
            "([PDCH分配成功率]<0.95)"
        };
        public const string GSMSumNotZero = @"
        and
        [信令信道分配成功率] +
        [话音信道分配成功率(不含切)] +
        [无线接通率]+
        [信令信道拥塞率]+
        [话音信道拥塞率(不含切)]+
        [话音信道掉话率(不含切)]+
        [非PBGT切换占比]+
        [下行话音质量]+
        [切换成功率]+ 
        [上行TBF建立成功率]+ 
        [下行TBF建立成功率]+
        [下行TBF掉线率]+
        [PDCH分配成功率]>0
        ";
        /// <summary>
        /// TD性能指标范围
        /// </summary>
        public static string[] TDPerTreadHoldInfs { get; } = new string[] {
            "[CS域RAB拥塞率]>0.002 ",
            "[CS域无线接通率]<0.99 ",
            "[CS域误块率] >0.01",
            "[语音业务无线掉话率] >0.005",
            "[PS域RAB拥塞率]>0.005",
            "[PS域RAB建立成功率] <0.995 ",
            "[PS域误块率] >0.05",
            "[PS域无线掉线率] >0.005",
            "[接力切换成功率] <0.98 ",
            "([码资源利用率] > 0.75 or ([码资源利用率] < 0.15))"
        };

        public const string TDSumNotZero = @"
        and
	    [CS域RAB拥塞率]+
        [CS域无线接通率]+
        [CS域误块率]+
        [语音业务无线掉话率] +
        [PS域RAB拥塞率]+
        [PS域RAB建立成功率]+
        [PS域误块率]+
        [PS域无线掉线率]+
        [接力切换成功率]+
        [码资源利用率]>0
        ";
    }
}
