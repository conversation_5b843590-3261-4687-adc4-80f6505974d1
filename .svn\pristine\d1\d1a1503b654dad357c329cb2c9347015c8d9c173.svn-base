﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ScanCellCoverInfoForm_W : MinCloseForm
    {
        private MainModel mainModel;
        private List<ScanCellCoverInfo_W> scanWCellCoverInfoList;

        public ScanCellCoverInfoForm_W(MainModel mainModel)
            : base(mainModel)
        {
            this.mainModel = mainModel;
            InitializeComponent();
        }

        public void FillData(List<ScanCellCoverInfo_W> scanWCellCoverInfoList)
        {
            this.scanWCellCoverInfoList = scanWCellCoverInfoList;
            this.gridControl1.DataSource = scanWCellCoverInfoList;
        }

        private void gridView1_DoubleClick(object sender, EventArgs e)
        {
            int[] hRows = gridView1.GetSelectedRows();
            if (hRows.Length <= 0) return;

            ScanCellCoverInfo_W cellInfo = gridView1.GetRow(hRows[0]) as ScanCellCoverInfo_W;
            if (cellInfo == null) return;
            mainModel.ScanCellCoverInfoList.Clear();
            mainModel.ScanCellCoverInfoList.Add(cellInfo);
            mainModel.SelectedWCell = cellInfo.CoverWCell;
            mainModel.ClearDTData();
            foreach (ScanCellCoverPointInfo_W tpInfo in cellInfo.TpInfoList)
            {
                mainModel.DTDataManager.Add(tpInfo.TP);
            }
            mainModel.FireDTDataChanged(this);
        }

        private void gridView2_DoubleClick(object sender, EventArgs e)
        {
            int[] hRows = ((DevExpress.XtraGrid.Views.Grid.GridView)sender).GetSelectedRows();
            if (hRows.Length <= 0) return;

            ScanCellCoverPointInfo_W ptInfo = ((DevExpress.XtraGrid.Views.Grid.GridView)sender).GetRow(hRows[0]) as ScanCellCoverPointInfo_W;
            if (ptInfo == null) return;

            mainModel.SelectedTestPoints.Clear();
            mainModel.SelectedTestPoints.Add(ptInfo.TP);
            mainModel.MainForm.GetMapForm().GoToView(ptInfo.TP.Longitude, ptInfo.TP.Latitude, 500);
        }

        private void ToolStripMenuItemExport_Click(object sender, EventArgs e)
        {
            try
            {
                DataTable table = new DataTable();
                table.Columns.Add("小区名称");
                table.Columns.Add("频点");
                table.Columns.Add("扰码");
                table.Columns.Add("经度");
                table.Columns.Add("纬度");
                table.Columns.Add("采样点数");
                table.Columns.Add("最大场强");
                table.Columns.Add("最小场强");
                table.Columns.Add("平均场强");
                table.Columns.Add("C/I最大值");
                table.Columns.Add("C/I最小值");
                table.Columns.Add("C/I平均值");
                table.Columns.Add("SIR最大值");
                table.Columns.Add("SIR最小值");
                table.Columns.Add("SIR平均值");
                table.Columns.Add("BLER最大值");
                table.Columns.Add("BLER最小值");
                table.Columns.Add("BLER平均值");
                table.Columns.Add("距离");
                foreach (ScanCellCoverInfo_W cellInfo in scanWCellCoverInfoList)
                {
                    int idx = 0;
                    DataRow row = table.NewRow();
                    row[idx++] = cellInfo.CellName;
                    row[idx++] = cellInfo.FREQ;
                    row[idx++] = cellInfo.CPI;
                    row[idx++] = cellInfo.Longitude;
                    row[idx++] = cellInfo.Latitude;
                    row[idx++] = cellInfo.TPCount;
                    row[idx++] = cellInfo.RscpMax;
                    row[idx++] = cellInfo.RscpMin;
                    row[idx++] = cellInfo.RscpMean;
                    row[idx++] = cellInfo.C2IMax;
                    row[idx++] = cellInfo.C2IMin;
                    row[idx++] = cellInfo.C2IMean;
                    row[idx++] = cellInfo.SirMax;
                    row[idx++] = cellInfo.SirMin;
                    row[idx++] = cellInfo.SirMean;
                    row[idx++] = cellInfo.BlerMax;
                    row[idx++] = cellInfo.BlerMin;
                    row[idx] = cellInfo.BlerMean;
                    table.Rows.Add(row);
                    foreach (ScanCellCoverPointInfo_W pointInfo in cellInfo.TpInfoList)
                    {
                        idx = 0;
                        row = table.NewRow();
                        row[idx++] = "";
                        row[idx++] = "";
                        row[idx++] = "";
                        row[idx++] = pointInfo.Longitude;
                        row[idx++] = pointInfo.Latitude;
                        row[idx++] = "";
                        row[idx++] = "";
                        row[idx++] = "";
                        row[idx++] = pointInfo.Rscp;
                        row[idx++] = "";
                        row[idx++] = "";
                        row[idx++] = pointInfo.C2I;
                        row[idx++] = "";
                        row[idx++] = "";
                        row[idx++] = pointInfo.Sir;
                        row[idx++] = "";
                        row[idx++] = "";
                        row[idx++] = pointInfo.Bler;
                        row[idx] = pointInfo.Distance;
                        table.Rows.Add(row);
                    }
                }
                MasterCom.Util.ExcelNPOIManager.ExportToExcel(table);
            }
            catch (System.Exception ex)
            {
                MessageBox.Show(ex.Message + Environment.NewLine + ex.StackTrace);
            }
        }
    }
}
