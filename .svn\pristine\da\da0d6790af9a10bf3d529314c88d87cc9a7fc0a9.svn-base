﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTRegionComplainSQLQuery : DIYSQLBase
    {
        public ZTRegionComplainSQLQuery(MainModel mm)
            : base(mm)
        {
            MainDB = false;
        }

        public override string Name
        {
            get { return "网格投诉统计"; }
        }

        public override bool IsNeedSetQueryCondition
        {
            get
            {
                return false;
            }
        }

        private int topn = 20;
        public void SetTopN(int topn)
        {
            this.topn = topn;
        }

        private DateTime begindate = DateTime.Now.AddDays(1 - DateTime.Now.Day);
        public string BeginDate
        {
            get { return begindate.ToString("yyyy-MM-dd"); }
            set { begindate = DateTime.Parse(value); }
        }

        private DateTime endDate = DateTime.Now.AddDays(1);
        public string EndDate
        {
            get { return endDate.ToString("yyyy-MM-dd"); }
            set { endDate = DateTime.Parse(value).AddDays(1); }
        }
        
        public string Network { get; set; } = "全部";
        
        public List<RegionComplainItem> RegionItems { get; set; } = new List<RegionComplainItem>();///统计结果集

        protected override string getSqlTextString()
        {
            string sql = "select b.id,b.gridName,b.region,b.sector,b.gridScene,a.ccount,c.GSM指标总得分,c.TD指标总得分,b.linkPeople,b.linkWay from (select top " + topn + " gridName as gridId,COUNT(*) as ccount from complaint_xj..tb_complain_item where gridName is not null and acceptTM between '" + BeginDate + "' and '" + EndDate + "' and ('" + Network + "' ='全部' or netType='" + Network + "') group by gridName order by COUNT(*) desc) a left join complaint_xj..tb_complainGrid b on a.gridId=b.id left join (select distinct keyname,max(case when reportName='GSM区域评估报表' then allScore else 0 end )as GSM指标总得分,max(case when reportName='TD区域评估报表' then allScore else 0 end) as TD指标总得分 from  Complain_Sys..tb_QoE_area_all_score where endTime=(select MAX(endTime) from Complain_Sys..tb_QoE_area_all_score) group by keyname) c on b.gridName=c.keyName order by a.ccount desc";
            return sql;
        }

        protected override Model.Interface.E_VType[] getSqlRetTypeArr()
        {
            E_VType[] vType = new E_VType[10];
            vType[0] = E_VType.E_String;
            vType[1] = E_VType.E_String;
            vType[2] = E_VType.E_String;
            vType[3] = E_VType.E_String;
            vType[4] = E_VType.E_String;
            vType[5] = E_VType.E_Int;
            vType[6] = E_VType.E_Float;
            vType[7] = E_VType.E_Float;
            vType[8] = E_VType.E_String;
            vType[9] = E_VType.E_String;
            return vType;
        }

        protected override void query()
        {
            WaitBox.Text = "正在查询...";
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, userName, password, dbid > 0 ? dbid : MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
                WaitBox.Show("开始统计数据...", queryInThread, clientProxy);
            }
            finally
            {
                clientProxy.Close();
                fireResultForm();
            }
        }

        protected override void queryInThread(object o)
        {
            base.queryInThread(o);
            WaitBox.Close();
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;
            RegionItems.Clear();
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    RegionComplainItem regionitem = new RegionComplainItem();
                    regionitem.ID = package.Content.GetParamString();
                    regionitem.GridName = package.Content.GetParamString();
                    regionitem.Region = package.Content.GetParamString();
                    regionitem.Town = package.Content.GetParamString();
                    regionitem.GridScene = package.Content.GetParamString();
                    regionitem.ItemCount = package.Content.GetParamInt();
                    regionitem.GSMAllScore = package.Content.GetParamFloat().ToString();
                    regionitem.TDAllScore = package.Content.GetParamFloat().ToString();
                    regionitem.LinkMan = package.Content.GetParamString();
                    regionitem.LinkPhone = package.Content.GetParamString();
                    this.RegionItems.Add(regionitem);
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_SUCCESS)
                {
                    break;
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_FAIL)
                {
                    break;
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                setProgressPercent(ref index, ref progress);
            }
        }
        protected void fireResultForm()
        {
            RegionComplaintReportForm frm = null;
            frm = MainModel.GetObjectFromBlackboard(typeof(RegionComplaintReportForm).FullName) as RegionComplaintReportForm;
            if (frm == null || frm.IsDisposed)
            {
                frm = new RegionComplaintReportForm(MainModel);
            }
            frm.FillData(RegionItems);
            if (!frm.Visible)
            {
                frm.Show(MainModel.MainForm);
            }
            else
            {
                frm.BringToFront();
            }
        }
    }
}
