﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc.Injection.TestPlanMultiLayer
{
    class AreaTypeOption
    {
        public override string ToString()
        {
            return this.Name;
        }
        public AreaTypeOption()
        {
            CityOptions = new List<CityOption>();
            foreach (Stat.IDNamePair item in DistrictManager.GetInstance().GetAvailableDistrict())
            {
                CityOption c = new CityOption();
                c.City = item;
                CityOptions.Add(c);
            }
            AreaType = AreaManager.GetInstance().AreaTypes.Items[0];
        }
        public string Name { get; set; }
        public CategoryEnumItem AreaType { get; set; }
        public List<CityOption> CityOptions { get; set; }

        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> dic = new Dictionary<string, object>();
                dic["Name"] = this.Name;
                dic["AreaTypeID"] = this.AreaType.ID;
                List<object> list = new List<object>();
                foreach (CityOption item in CityOptions)
                {
                    list.Add(item.Param);
                }
                dic["CityOptions"] = list;
                return dic;
            }
            set
            {
                if (value == null)
                {
                    return;
                }
                this.Name = value["Name"] as string;
                int id = (int)value["AreaTypeID"];
                this.AreaType = AreaManager.GetInstance().GetAreaType(id);
                this.CityOptions = new List<CityOption>();
                foreach (object item in (List<object>)value["CityOptions"])
                {
                    CityOption o = new CityOption();
                    o.Param = item as Dictionary<string, object>;
                    this.CityOptions.Add(o);
                }
            }
        }
    }
}
