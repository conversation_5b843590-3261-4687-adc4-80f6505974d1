﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using MasterCom.Util.UiEx;

namespace MasterCom.RAMS.Model.BaseInfo
{
    public class QueryUserPermission : QueryBase
    {
        public User user { get; set; }

        /// <summary>
        /// 查询所有用户权限信息
        /// </summary>
        /// <param name="mainModel"></param>
        public QueryUserPermission(MainModel mainModel)
            : base(mainModel)
        {
        }
        /// <summary>
        /// 查询指定用户权限信息
        /// </summary>
        /// <param name="mainModel"></param>
        /// <param name="user"></param>
        public QueryUserPermission(MainModel mainModel,User user)
            : base(mainModel)
        {
            this.user = user;
        }

        public override string Name
        {
            get { return "获取用户权限信息"; }
        }

        public override string IconName
        {
            get { return "Images/regionq.gif"; }
        }

        protected override bool isValidCondition()
        {
            return true;
        }

        protected override void query()
        {
            if (!MainModel.PermissionManager.HasQueriedRoleInfo)
            {
                try
                {
                    QueryPermissionRoleInfo queryBaseInfo = new QueryPermissionRoleInfo(MainModel, user == null);
                    queryBaseInfo.Query();
                }
                catch (Exception e)
                {
                    MessageBox.Show(e.ToString());
                }
            }
            WaitTextBox.Show("正在获取基础信息...", queryData);
        }

        private void queryData()
        {
            try
            {
                querUserRoleSetting();
            }
            catch (Exception ex)
            {
                MessageBox.Show("查询权限基础信息异常！" + Environment.NewLine + ex.ToString());
            }
            finally
            {
                System.Threading.Thread.Sleep(200);
                WaitTextBox.Close();
            }
        }

        private void querUserRoleSetting()
        {
            QueryUsers queryUser = new QueryUsers(MainModel, user);
            queryUser.Query();

            //user-roleList id
            DIYQueryUserFuncRole funcRoleQuery = new DIYQueryUserFuncRole(MainModel, user);
            funcRoleQuery.Query();

            DIYQueryUserDataSrcRole queryUserRole = new DIYQueryUserDataSrcRole(MainModel,user);
            queryUserRole.Query();

            Dictionary<int, Dictionary<int, List<int>>> usrDataSrcDic = queryUserRole.UserDistrictRoleDic;
            for (int i = 0; i < queryUser.UserList.Count; i++)
            {
                User usr = queryUser.UserList[i];
                Dictionary<int, bool> dic;
                if (funcRoleQuery.UserRolesDic.TryGetValue(usr.ID, out dic))
                {
                    usr.FunctionRoleIDList = new List<int>(dic.Keys);
                }

                Dictionary<int, List<int>> districtRoleDic = null;
                if (usrDataSrcDic.TryGetValue(usr.ID, out districtRoleDic))
                {
                    foreach (int districtID in districtRoleDic.Keys)
                    {
                        foreach (int roleID in districtRoleDic[districtID])
                        {
                            usr.UpdateDataSourceRole(districtID, roleID, true);
                        }
                    }
                }
            }
            MainModel.PermissionManager.Users = queryUser.UserList;
        }

    }
}
