﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Stat;
using MasterCom.RAMS.UserMng;
using MasterCom.MTGis;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Grid;
using System.Windows.Forms;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.KPI_Statistics;
using MasterCom.RAMS.ZTFunc;
using System.Drawing;

namespace MasterCom.RAMS.Net
{
    public class ZTGridCompareDownloadSpeed : ZTGridCompareCombineBase
    {
        public ZTGridCompareDownloadSpeed(MainModel mainModel)
            : base(mainModel)
        {
            this.mainModel = mainModel;
        }
        public override string Name
        {
            get { return "竞对低速率路段(按栅格>=5M,且<=25M)"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
           
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22073, "查询");
        }

        /// <summary>
        /// 设置查询指标
        /// </summary>
        /// <returns></returns>
        protected override bool getConditionBeforeQuery()
        {
            if (!condition.CarrierTypes.Contains(1) || condition.CarrierTypes.Count != 2)
            {
                MessageBox.Show("运营商选择有误，请选择移动联通或者移动电信");
                return false;
            }
            this.evtIDSvrIDDic = new Dictionary<int, Dictionary<int, bool>>();
            this.imgCodeSvrIDDic = new Dictionary<string, Dictionary<int, bool>>();
            List<string> formulaSet = new List<string>();
            formulaSet.Add(strTDDDownTime);
            formulaSet.Add(strTDDDownSize);
            formulaSet.Add(strFDDDownTime);
            formulaSet.Add(strFDDDownSize);
            statImgIDSet = getTriadIDIgnoreServiceType(formulaSet);
            isShowSetForm = true;
            return true;
        }
        /// <summary>
        /// 重写对比过程
        /// </summary>
        protected override void doCompare()
        {
            gridCountInfo = new GridCountInfo();
            //gridCountInfo.IAllGridCount = MainModel.CurGridColorUnitMatrix.Grids.Count;
            foreach (ColorUnit cu in MainModel.CurGridColorUnitMatrix)
            {
                MasterCom.RAMS.Grid.GridUnitBase grid = new Grid.GridUnitBase(cu.CenterLng, cu.CenterLat);
                GridTypeName gridName = strContainDbRect(grid.Bounds);
                if (gridName.strGridType == "" || gridName.strGridName == "")
                {
                    continue;
                }
                gridCountInfo.IAllGridCount++;
                int rAt, cAt;
                GridHelper.GetIndexOfDefaultSizeGrid(cu.CenterLng, cu.CenterLat, out rAt, out cAt);

                bool isNotValid = judgeValidStat(cu);
                if (isNotValid)
                {
                    continue;
                }

                double dHostDownTime = cu.DataHub.CalcValueByFormula(strTDDDownTime);
                double dHostDownSize = cu.DataHub.CalcValueByFormula(strTDDDownSize);

                double dGuestDownTime = cu.DataHub.CalcValueByFormula(strFDDDownTime);
                double dGuestDownSize = cu.DataHub.CalcValueByFormula(strFDDDownSize);

                if (dHostDownTime <= 0 || dGuestDownTime <= 0)
                {
                    continue;
                }
                double dHostDownSpeed = getHostDownSpeed(dHostDownTime, dHostDownSize);
                if (dHostDownSpeed < 5 || dHostDownSpeed > dSpeed)
                {
                    continue;
                }

                gridCountInfo.ICompareGridCount++;

                double dGuestDownSpeed = getGuestDownSpeed(dGuestDownTime, dGuestDownSize);

                if (dHostDownSpeed < dGuestDownSpeed * dPercent)
                {
                    GridColorUnit griCu = new GridColorUnit();
                    griCu.CuUnit = cu;
                    griCu.DHostSpeed = dHostDownSpeed;
                    griCu.DGuestSpeed = dGuestDownSpeed;
                    griCu.DHostMo = dHostDownSize;
                    griCu.DHostBase = dHostDownTime;
                    griCu.DGuestMo = dGuestDownSize;
                    griCu.DGuestBase = dGuestDownTime;
                    griCu.CuUnit.DataHub = null;//降低内存
                    setGridColorUnit(gridName, rAt, cAt, griCu);
                }
                cu.DataHub = null;
            }
        }

        private void setGridColorUnit(GridTypeName gridName, int rAt, int cAt, GridColorUnit griCu)
        {
            if (!gridColorUnit.ContainsKey(gridName))
            {
                gridColorUnit[gridName] = new GridMatrix<GridColorUnit>();
            }
            gridColorUnit[gridName][rAt, cAt] = griCu;
        }

        private bool judgeValidStat(ColorUnit cu)
        {
            StatDataLTE dataStatTDD = cu.DataHub.GetStatData(typeof(StatDataLTE)) as StatDataLTE;
            StatDataLTE_FDD dataStatFDD = cu.DataHub.GetStatData(typeof(StatDataLTE_FDD)) as StatDataLTE_FDD;
            if (dataStatTDD != null)
            {
                gridCountInfo.IHostGridCount++;
            }
            if (dataStatFDD != null)
            {
                gridCountInfo.IGuestGridCount++;
            }
            if (dataStatTDD == null || dataStatFDD == null)
            {
                cu.DataHub = null;
                return true;
            }
            return false;
        }

        private static double getHostDownSpeed(double dHostDownTime, double dHostDownSize)
        {
            double dHostDownSpeed = 0;
            if (dHostDownTime > 0 && dHostDownSize >= 0)
            {
                dHostDownSpeed = dHostDownSize / dHostDownTime;
            }

            return dHostDownSpeed;
        }

        private static double getGuestDownSpeed(double dGuestDownTime, double dGuestDownSize)
        {
            double dGuestDownSpeed = 0;
            if (dGuestDownTime > 0 && dGuestDownSize >= 0)
            {
                dGuestDownSpeed = dGuestDownSize / dGuestDownTime;
            }

            return dGuestDownSpeed;
        }

        /// <summary>
        /// 重写转化过程
        /// </summary>
        protected override GridCompareCombineInfo doChangGridCompareResult(GridCompareCombineBlock block)
        {
            if (block.Grids.Count == 0)
            {
                return null;
            }
            GridCompareCombineInfo gridItem = new GridCompareCombineInfo();
            double sumLng = 0;
            double sumLat = 0;
            double sumHostMo = 0;
            double sumHostBase = 0;
            double sumGuestMo = 0;
            double sumGuestBase = 0;
            StringBuilder sbLng = new StringBuilder();
            StringBuilder sbLat = new StringBuilder();
            foreach (GridColorUnit gridCU in block.Grids)
            {
                sumLng += gridCU.CuUnit.CenterLng;
                sumLat += gridCU.CuUnit.CenterLat;
                sbLng.Append(gridCU.CuUnit.CenterLng + ";");
                sbLat.Append(gridCU.CuUnit.CenterLat + ";");
                sumHostMo += gridCU.DHostMo;
                sumHostBase += gridCU.DHostBase;
                sumGuestMo += gridCU.DGuestMo;
                sumGuestBase += gridCU.DGuestBase;

                setStrFileName(gridItem, gridCU);
            }
            gridItem.StrLngList += sbLng.ToString();
            gridItem.StrLatList += sbLat.ToString();

            double dHostMeanVale = -999;
            double dGuestMeanVale = -999;
            if (sumHostBase != 0)
            {
                dHostMeanVale = sumHostMo / sumHostBase;
            }
            if (sumGuestBase != 0)
            {
                dGuestMeanVale = sumGuestMo / sumGuestBase;
            }
            if (condition.CarrierTypes.Contains(1) && condition.CarrierTypes.Contains(2))
            {
                gridItem.StrCompareInfo = "劣于联通";
                gridItem.StrProblemInfo = "移动下载速率：" + dHostMeanVale.ToString("0.00") + "M，联通下载速率：" 
                    + dGuestMeanVale.ToString("0.00") + "M，连续栅格个数：" + block.Grids.Count + "个";
            }
            else if (condition.CarrierTypes.Contains(1) && condition.CarrierTypes.Contains(3))
            {
                gridItem.StrCompareInfo = "劣于电信";
                gridItem.StrProblemInfo = "移动下载速率：" + dHostMeanVale.ToString("0.00") + "M，电信下载速率："
                    + dGuestMeanVale.ToString("0.00") + "M，连续栅格个数：" + block.Grids.Count + "个";
            }
            gridItem.DLng = sumLng / block.Grids.Count;
            gridItem.DLat = sumLat / block.Grids.Count;
            gridItem.StrProblemType = "速率问题";
            return gridItem;
        }

        private void setStrFileName(GridCompareCombineInfo gridItem, GridColorUnit gridCU)
        {
            CenterLongLat cll = new CenterLongLat(gridCU.CuUnit.CenterLng, gridCU.CuUnit.CenterLat);
            if (gridFileNameListDic.ContainsKey(cll))
            {
                StringBuilder sbName = new StringBuilder();
                foreach (string strFileName in gridFileNameListDic[cll])
                {
                    if (strFileName.Contains("上传"))
                    {
                        continue;
                    }
                    if (!sbName.ToString().Contains(strFileName))
                    {
                        sbName.Append(strFileName + ";");
                    }
                }
                gridItem.StrFileName = sbName.ToString();
            }
        }
    }
}
