﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using MasterCom.RAMS.ZTFunc.ZTCellSplit;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc.ZTCellSplit
{
    public partial class XtraCellSplitSetupForm : DevExpress.XtraEditors.XtraForm
    {
        MainModel mainModel;
        string network;
        public XtraCellSplitSetupForm(MainModel mainmodel,string mynetwork)
        {
            InitializeComponent();
            mainModel = mainmodel;
            network = mynetwork;
            //loadData();

            comboBox1.Items.Clear();

            if (network == "TD")
            {
                comboBox1.Items.Add("语音业务话务量");
                comboBox1.Items.Add("码资源利用率");
                comboBox1.Items.Add("PS域误块率");
                comboBox1.Items.Add("接力切换成功率");
                comboBox1.Items.Add("系统间切换占比");
                //comboBox2.Text = "3000";
                radioButton1.Visible = false;
                radioButton2.Visible = false;

                comboBox1.SelectedIndex = 0;
            }
            else
            {
                comboBox1.Items.Add("无线利用率");
                comboBox1.Items.Add("半速率话务比");
                comboBox1.Items.Add("上行话音质量");
                comboBox1.Items.Add("下行话音质量");
                comboBox1.Items.Add("SDCCH分配成功率");
                comboBox1.Items.Add("TCH分配成功率");
                comboBox1.Items.Add("无线接通率");

                radioButton1.Visible = true;
                radioButton2.Visible = true;

                comboBox1.SelectedIndex = 0;

            }
        }

        public void getSelect(out string name, out string table, out DateTime time, out string frequency)
        {
            //CellSplitSetup cellsetup = comboBox1.SelectedItem as CellSplitSetup;

            if (network == "TD")
            {
                
                table = "tb_para_utrancell_counter_detail_" + dateTimePicker1.Value.ToString("yyyy") + "_" + dateTimePicker1.Value.ToString("MM");//cellsetup.Table;
                frequency = "3000";
            }
            else
            {
               
                table = "tb_para_cell_counter_detail_" + dateTimePicker1.Value.ToString("yyyy") + "_" + dateTimePicker1.Value.ToString("MM");//cellsetup.Table;
                if (radioButton1.Checked)
                {
                    frequency = "900";
                }
                else
                {
                    frequency = "1800";
                }
            }
            name = comboBox1.Text;
            time = dateTimePicker1.Value;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void button2_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }
    }
}