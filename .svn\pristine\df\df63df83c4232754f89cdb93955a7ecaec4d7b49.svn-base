using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.Util;
using System.Xml;

namespace MasterCom.RAMS.Net
{
    public class CompareConfig
    {
        public Dictionary<string, CompareParamConfig> compareParamConfigDic { get; set; } = new Dictionary<string, CompareParamConfig>();
        
        public List<CompareParamConfig> CompareParamConfigList
        {
            get
            {
                List<CompareParamConfig> compareParamConfigList = new List<CompareParamConfig>();
                compareParamConfigList.AddRange(compareParamConfigDic.Values);
                compareParamConfigList.Sort(delegate(CompareParamConfig a, CompareParamConfig b) { return a.sn - b.sn; });
                return compareParamConfigList;
            }
        }
    
        public bool loadConfig(string path)
        {
            try
            {
                compareParamConfigDic.Clear();

                XmlConfigFile configFile = new XmlConfigFile(path);

                Dictionary<string, object> newDic = configFile.GetItemValue("Configs", "CompareConfig") as Dictionary<string, object>;
                if (newDic != null)
                {
                    this.Param = newDic;
                }
            }
            catch
            {
                return false;
            }
            return true;
        }

        public bool saveConfig(string path)
        {
            try
            {
                XmlConfigFile xmlconfigfile = new XmlConfigFile();
                XmlElement cfg = xmlconfigfile.AddConfig("Configs");
                xmlconfigfile.AddItem(cfg, "CompareConfig", this.Param);
                xmlconfigfile.Save(path);
            }
            catch
            {
                return false;
            }
            return true;
        }

        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();

                Dictionary<string, object> odic = new Dictionary<string, object>();
                foreach (string key in compareParamConfigDic.Keys)
                {
                    odic[key] = compareParamConfigDic[key].Param;
                }
                param["compareParamConfigDic"] = odic;

                return param;
            }
            set
            {
                Dictionary<string, CompareParamConfig> compareParamConfigDicTemp = new Dictionary<string, CompareParamConfig>();
                Dictionary<string, object> oDic = (Dictionary<string, object>)value["compareParamConfigDic"];
                foreach (string key in oDic.Keys)
                {
                    CompareParamConfig compareParamConfig = new CompareParamConfig();
                    compareParamConfig.Param = (Dictionary<string, object>)oDic[key];
                    compareParamConfigDicTemp[key] = compareParamConfig;
                }
                compareParamConfigDic = compareParamConfigDicTemp;
            }
        }
    }

    public class CompareParamConfig
    {
        public int sn { get; set; } = 1000;
        public string name { get; set; } = "";
        public int carrierID_A { get; set; }
        public int serviceID_A { get; set; }
        public List<int> paramID_A { get; set; } = new List<int>();

        public int carrierID_B { get; set; }
        public int serviceID_B { get; set; }
        public List<int> paramID_B { get; set; } = new List<int>();

        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["carrierID_A"] = carrierID_A;
                param["serviceID_A"] = serviceID_A;
                List<object> oList = new List<object>();
                foreach (int temp in paramID_A)
                {
                    oList.Add((object)temp);
                }
                param["paramID_A"] = oList;

                param["carrierID_B"] = carrierID_B;
                param["serviceID_B"] = serviceID_B;
                oList = new List<object>();
                foreach (int temp in paramID_B)
                {
                    oList.Add((object)temp);
                }
                param["paramID_B"] = oList;

                param["sn"] = sn;
                param["name"] = name;

                return param;
            }
            set
            {
                sn = (int)value["sn"];
                name = (string)value["name"];
                carrierID_A = (int)value["carrierID_A"];
                serviceID_A = (int)value["serviceID_A"];

                paramID_A.Clear();
                List<object> oList = (List<object>)value["paramID_A"];
                foreach (object o in oList)
                {
                    paramID_A.Add(Convert.ToInt32(o));
                }

                carrierID_B = (int)value["carrierID_B"];
                serviceID_B = (int)value["serviceID_B"];

                paramID_B.Clear();
                oList = (List<object>)value["paramID_B"];
                foreach (object o in oList)
                {
                    paramID_B.Add(Convert.ToInt32(o));
                }

            }
        }

        public CPDataType getCPDataTypeByID(int paramID, Dictionary<int, CPDataType> cpDataTypeDic)
        {
            if (cpDataTypeDic.ContainsKey(paramID))
            {
                return cpDataTypeDic[paramID];
            }
            return null;
        }
    }





}
