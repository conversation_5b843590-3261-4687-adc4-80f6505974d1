﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc.LteSignalImsi
{
    public partial class ReplayEventByImsiPeriodForm : BaseDialog
    {
        public ReplayEventByImsiPeriodForm()
        {
            InitializeComponent();
            btnOK.Click += BtnOK_Click;
            btnCancel.Click += BtnCancel_Click;
            cbxGisInterval.CheckedChanged += GisInterval_CheckedChanged;
            numberInterval.Enabled = cbxGisInterval.Checked;
        }

        public ReplayEventByImsiPeriodCond GetCondition(DateTime t)
        {
            ReplayEventByImsiPeriodCond cond = new ReplayEventByImsiPeriodCond();
            cond.IsGisIntervalLimit = cbxGisInterval.Checked;
            cond.GisIntervalMinutes = (int)numberInterval.Value;
            cond.Period = new MasterCom.Util.TimePeriod(
                t.AddMinutes(0 - (int)numberReplayPrev.Value), 
                t.AddMinutes((int)numberReplayNext.Value));
            return cond;
        }

        private void BtnOK_Click(object sender, EventArgs e)
        {
            DialogResult = System.Windows.Forms.DialogResult.OK;
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = System.Windows.Forms.DialogResult.Cancel;
        }

        private void GisInterval_CheckedChanged(object sender, EventArgs e)
        {
            numberInterval.Enabled = cbxGisInterval.Checked;
        }
    }

    public class ReplayEventByImsiPeriodCond
    {
        public MasterCom.Util.TimePeriod Period
        {
            get;
            set;
        }

        public int GisIntervalMinutes
        {
            get;
            set;
        }

        public bool IsGisIntervalLimit
        {
            get;
            set;
        }
    }
}
