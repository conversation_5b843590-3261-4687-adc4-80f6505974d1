﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTWeakCoverCDForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.contextMenuStrip1 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.ExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.ListViewRoad = new BrightIdeasSoftware.TreeListView();
            this.olvColumnSN = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnRoadName = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnDitance = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnDuration = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnSampleCount = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnTotalSampleCount = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnMaxRxPower = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnMinRxPower = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnMeanRxPower = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnMaxTxPower = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnMinTxPower = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnMeanTxPower = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnMaxC2I = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnMinC2I = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnMeanC2I = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnFileName = new BrightIdeasSoftware.OLVColumn();
            this.contextMenuStrip1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.ListViewRoad)).BeginInit();
            this.SuspendLayout();
            // 
            // contextMenuStrip1
            // 
            this.contextMenuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.ExportExcel});
            this.contextMenuStrip1.Name = "contextMenuStrip1";
            this.contextMenuStrip1.Size = new System.Drawing.Size(130, 26);
            // 
            // ExportExcel
            // 
            this.ExportExcel.Name = "ExportExcel";
            this.ExportExcel.Size = new System.Drawing.Size(129, 22);
            this.ExportExcel.Text = "导出Excel";
            this.ExportExcel.Click += new System.EventHandler(this.ToolStripMenuItemExport_Click);
            // 
            // ListViewRoad
            // 
            this.ListViewRoad.AllColumns.Add(this.olvColumnSN);
            this.ListViewRoad.AllColumns.Add(this.olvColumnRoadName);
            this.ListViewRoad.AllColumns.Add(this.olvColumnDitance);
            this.ListViewRoad.AllColumns.Add(this.olvColumnDuration);
            this.ListViewRoad.AllColumns.Add(this.olvColumnSampleCount);
            this.ListViewRoad.AllColumns.Add(this.olvColumnTotalSampleCount);
            this.ListViewRoad.AllColumns.Add(this.olvColumnMaxRxPower);
            this.ListViewRoad.AllColumns.Add(this.olvColumnMinRxPower);
            this.ListViewRoad.AllColumns.Add(this.olvColumnMeanRxPower);
            this.ListViewRoad.AllColumns.Add(this.olvColumnMaxTxPower);
            this.ListViewRoad.AllColumns.Add(this.olvColumnMinTxPower);
            this.ListViewRoad.AllColumns.Add(this.olvColumnMeanTxPower);
            this.ListViewRoad.AllColumns.Add(this.olvColumnMaxC2I);
            this.ListViewRoad.AllColumns.Add(this.olvColumnMinC2I);
            this.ListViewRoad.AllColumns.Add(this.olvColumnMeanC2I);
            this.ListViewRoad.AllColumns.Add(this.olvColumnFileName);
            this.ListViewRoad.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnSN,
            this.olvColumnRoadName,
            this.olvColumnDitance,
            this.olvColumnDuration,
            this.olvColumnSampleCount,
            this.olvColumnTotalSampleCount,
            this.olvColumnMaxRxPower,
            this.olvColumnMinRxPower,
            this.olvColumnMeanRxPower,
            this.olvColumnMaxTxPower,
            this.olvColumnMinTxPower,
            this.olvColumnMeanTxPower,
            this.olvColumnMaxC2I,
            this.olvColumnMinC2I,
            this.olvColumnMeanC2I,
            this.olvColumnFileName});
            this.ListViewRoad.ContextMenuStrip = this.contextMenuStrip1;
            this.ListViewRoad.Cursor = System.Windows.Forms.Cursors.Default;
            this.ListViewRoad.Dock = System.Windows.Forms.DockStyle.Fill;
            this.ListViewRoad.FullRowSelect = true;
            this.ListViewRoad.GridLines = true;
            this.ListViewRoad.HeaderWordWrap = true;
            this.ListViewRoad.IsNeedShowOverlay = false;
            this.ListViewRoad.Location = new System.Drawing.Point(0, 0);
            this.ListViewRoad.Name = "ListViewRoad";
            this.ListViewRoad.OwnerDraw = true;
            this.ListViewRoad.ShowGroups = false;
            this.ListViewRoad.Size = new System.Drawing.Size(1280, 540);
            this.ListViewRoad.TabIndex = 7;
            this.ListViewRoad.UseCompatibleStateImageBehavior = false;
            this.ListViewRoad.View = System.Windows.Forms.View.Details;
            this.ListViewRoad.VirtualMode = true;
            this.ListViewRoad.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.ListViewRoad_MouseDoubleClick);
            // 
            // olvColumnSN
            // 
            this.olvColumnSN.AspectName = "SN";
            this.olvColumnSN.HeaderFont = null;
            this.olvColumnSN.Text = "序号";
            // 
            // olvColumnRoadName
            // 
            this.olvColumnRoadName.AspectName = "RoadName";
            this.olvColumnRoadName.HeaderFont = null;
            this.olvColumnRoadName.Text = "道路名称";
            this.olvColumnRoadName.Width = 120;
            // 
            // olvColumnDitance
            // 
            this.olvColumnDitance.AspectName = "Distance";
            this.olvColumnDitance.HeaderFont = null;
            this.olvColumnDitance.Text = "弱覆盖长度(米)";
            this.olvColumnDitance.Width = 100;
            // 
            // olvColumnDuration
            // 
            this.olvColumnDuration.AspectName = "Duration";
            this.olvColumnDuration.HeaderFont = null;
            this.olvColumnDuration.Text = "持续时间(秒)";
            this.olvColumnDuration.Width = 80;
            // 
            // olvColumnSampleCount
            // 
            this.olvColumnSampleCount.AspectName = "SampleCount";
            this.olvColumnSampleCount.HeaderFont = null;
            this.olvColumnSampleCount.Text = "弱覆盖点数";
            this.olvColumnSampleCount.Width = 80;
            // 
            // olvColumnTotalSampleCount
            // 
            this.olvColumnTotalSampleCount.AspectName = "totalSampleCount";
            this.olvColumnTotalSampleCount.HeaderFont = null;
            this.olvColumnTotalSampleCount.Text = "总采样点数";
            this.olvColumnTotalSampleCount.Width = 80;
            // 
            // olvColumnMaxRxPower
            // 
            this.olvColumnMaxRxPower.AspectName = "MaxRxPower";
            this.olvColumnMaxRxPower.HeaderFont = null;
            this.olvColumnMaxRxPower.Text = "最大RxAGC";
            this.olvColumnMaxRxPower.Width = 80;
            // 
            // olvColumnMinRxPower
            // 
            this.olvColumnMinRxPower.AspectName = "MinRxPower";
            this.olvColumnMinRxPower.HeaderFont = null;
            this.olvColumnMinRxPower.Text = "最小RxAGC";
            this.olvColumnMinRxPower.Width = 80;
            // 
            // olvColumnMeanRxPower
            // 
            this.olvColumnMeanRxPower.AspectName = "MeanRxPower";
            this.olvColumnMeanRxPower.HeaderFont = null;
            this.olvColumnMeanRxPower.Text = "平均RxAGC";
            this.olvColumnMeanRxPower.Width = 80;
            // 
            // olvColumnMaxTxPower
            // 
            this.olvColumnMaxTxPower.AspectName = "MaxTxPower";
            this.olvColumnMaxTxPower.HeaderFont = null;
            this.olvColumnMaxTxPower.Text = "最大TxPower";
            this.olvColumnMaxTxPower.Width = 80;
            // 
            // olvColumnMinTxPower
            // 
            this.olvColumnMinTxPower.AspectName = "MinTxPower";
            this.olvColumnMinTxPower.HeaderFont = null;
            this.olvColumnMinTxPower.Text = "最小TxPower";
            this.olvColumnMinTxPower.Width = 80;
            // 
            // olvColumnMeanTxPower
            // 
            this.olvColumnMeanTxPower.AspectName = "MeanTxPower";
            this.olvColumnMeanTxPower.HeaderFont = null;
            this.olvColumnMeanTxPower.Text = "平均TxPower";
            this.olvColumnMeanTxPower.Width = 80;
            // 
            // olvColumnMaxC2I
            // 
            this.olvColumnMaxC2I.AspectName = "MaxC2I";
            this.olvColumnMaxC2I.HeaderFont = null;
            this.olvColumnMaxC2I.Text = "最大Ec/lo";
            this.olvColumnMaxC2I.Width = 70;
            // 
            // olvColumnMinC2I
            // 
            this.olvColumnMinC2I.AspectName = "MinC2I";
            this.olvColumnMinC2I.HeaderFont = null;
            this.olvColumnMinC2I.Text = "最小Ec/lo";
            this.olvColumnMinC2I.Width = 70;
            // 
            // olvColumnMeanC2I
            // 
            this.olvColumnMeanC2I.AspectName = "MeanC2I";
            this.olvColumnMeanC2I.HeaderFont = null;
            this.olvColumnMeanC2I.Text = "平均Ec/lo";
            this.olvColumnMeanC2I.Width = 70;
            // 
            // olvColumnFileName
            // 
            this.olvColumnFileName.AspectName = "FileName";
            this.olvColumnFileName.HeaderFont = null;
            this.olvColumnFileName.Text = "文件名";
            // 
            // ZTWeakCoverCDForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1280, 540);
            this.Controls.Add(this.ListViewRoad);
            this.Name = "ZTWeakCoverCDForm";
            this.Text = "CDMA弱覆盖路段";
            this.contextMenuStrip1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.ListViewRoad)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.ContextMenuStrip contextMenuStrip1;
        private System.Windows.Forms.ToolStripMenuItem ExportExcel;
        private BrightIdeasSoftware.TreeListView ListViewRoad;
        private BrightIdeasSoftware.OLVColumn olvColumnSN;
        private BrightIdeasSoftware.OLVColumn olvColumnRoadName;
        private BrightIdeasSoftware.OLVColumn olvColumnDitance;
        private BrightIdeasSoftware.OLVColumn olvColumnSampleCount;
        private BrightIdeasSoftware.OLVColumn olvColumnDuration;
        private BrightIdeasSoftware.OLVColumn olvColumnMaxRxPower;
        private BrightIdeasSoftware.OLVColumn olvColumnMinRxPower;
        private BrightIdeasSoftware.OLVColumn olvColumnMeanRxPower;
        private BrightIdeasSoftware.OLVColumn olvColumnMaxTxPower;
        private BrightIdeasSoftware.OLVColumn olvColumnMinTxPower;
        private BrightIdeasSoftware.OLVColumn olvColumnMeanTxPower;
        private BrightIdeasSoftware.OLVColumn olvColumnMaxC2I;
        private BrightIdeasSoftware.OLVColumn olvColumnMinC2I;
        private BrightIdeasSoftware.OLVColumn olvColumnMeanC2I;
        private BrightIdeasSoftware.OLVColumn olvColumnFileName;
        private BrightIdeasSoftware.OLVColumn olvColumnTotalSampleCount;

    }
}