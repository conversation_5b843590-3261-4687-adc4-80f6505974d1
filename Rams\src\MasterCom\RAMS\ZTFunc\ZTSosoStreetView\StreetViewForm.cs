﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using System.IO;
using AxMapWinGIS;
using MapWinGIS;
using MasterCom.Util;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.ExMap;
using GMap.NET;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class StreetViewForm : MinCloseForm
    {
        public static void Activate(object sender, EventArgs e)
        {
            MainModel model = MainModel.GetInstance();
            StreetViewForm form = model.GetObjectFromBlackboard(typeof(StreetViewForm).FullName) as StreetViewForm;
            if (form == null || form.IsDisposed)
            {
                form = new StreetViewForm(model);
            }
            if (!form.Visible)
            {
                form.Show(model.MainForm);
            }
        }

        public StreetViewForm(MainModel mm) : base(mm)
        {
            InitializeComponent();
            DisposeWhenClose = true;
            this.mainModel = mm;
            mapForm = mainModel.MainForm.GetMapForm();
            gMap = mapForm.ExMapPanel.getMainMap();
            axMap = mapForm.GetMapFormControl();
            InitWebBroswer();
        }

        protected override void MinCloseForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            axMap.MouseDownEvent -= AxMap_MouseDown;
            gMap.MouseDown -= new MouseEventHandler(this.GMap_MouseDown);
            webBrowser.Dispose();
            base.MinCloseForm_FormClosing(sender, e);
        }

        private void InitWebBroswer()
        {
            if (!File.Exists(htmlFilePath))
            {
                MessageBox.Show("未找到文件: " + htmlFilePath, "提示");
                return;
            }
            axMap.MouseDownEvent += AxMap_MouseDown;
            gMap.MouseDown += new MouseEventHandler(this.GMap_MouseDown);
            webBrowser.Navigate(htmlFilePath);
        }

        private void AxMap_MouseDown(object sender, _DMapEvents_MouseDownEvent e)
        {
            if (!this.Visible)
            {
                return;
            }

            double lng = 0, lat = 0, radius = 100;
            string type = "GIS";
            axMap.PixelToProj(e.x, e.y, ref lng, ref lat);
            webBrowser.Document.InvokeScript("setPano", new object[] { lng, lat, radius, type });
        }

        private void GMap_MouseDown(object sender, MouseEventArgs e)
        {
            if (!this.Visible)
            {
                return;
            }

            double radius = 100;
            PointLatLng pt = gMap.FromLocalToLatLng(e.X, e.Y);
            string type = "GOOGLE";
            webBrowser.Document.InvokeScript("setPano", new object[] { pt.Lng, pt.Lat, radius, type });
        }

        private MainModel mainModel;
        private MapForm mapForm;
        private AxMap axMap;
        private MTExGMap gMap;
        private static string htmlFilePath = Application.StartupPath + @"\config\SosoStreetView.html";
    }
}
