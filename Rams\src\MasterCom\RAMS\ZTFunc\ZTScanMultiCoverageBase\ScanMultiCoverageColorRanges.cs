﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.MControls;

namespace MasterCom.RAMS.ZTFunc
{
    /// <summary>
    /// 渲染图例
    /// </summary>
    public abstract class ScanMultiCoverageColorRanges
    {
        public List<ColorRange> ColorRanges { get; set; } = new List<ColorRange>();

        public abstract string RangeName { get; } //= "ScanMultiCoverageColorRanges";

        protected abstract void initialize();
        //{
        //    List<ColorRange> colorRanges = new List<ColorRange>();
        //    colorRanges.Add(new ColorRange(0, 2, Color.Cyan));
        //    colorRanges.Add(new ColorRange(2, 4, Color.Lime));
        //    colorRanges.Add(new ColorRange(4, 6, Color.Yellow));
        //    colorRanges.Add(new ColorRange(6, 10, Color.Orange));
        //    colorRanges.Add(new ColorRange(10, 50, Color.Red));
        //    return colorRanges;
        //}

        public virtual Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                List<object> colorParams = new List<object>();
                param[RangeName] = colorParams;
                foreach (ColorRange cr in ColorRanges)
                {
                    colorParams.Add(cr.Param);
                }
                return param;
            }
            set
            {
                if (value.Count == 0)
                {
                    return;
                }

                ColorRanges.Clear();
                List<object> colorParams = (List<object>)value[RangeName];
                foreach (object o in colorParams)
                {
                    Dictionary<string, object> colorParam = (Dictionary<string, object>)o;
                    ColorRange cr = new ColorRange();
                    cr.Param = colorParam;
                    ColorRanges.Add(cr);
                }
            }
        }
    }
}
