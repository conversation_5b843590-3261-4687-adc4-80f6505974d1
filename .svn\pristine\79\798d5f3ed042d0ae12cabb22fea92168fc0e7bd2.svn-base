﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using BrightIdeasSoftware;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc.JiLinFocusSet
{
    public partial class FocusSetListForm : MinCloseForm
    {
        public FocusSetListForm()
            : base()
        {
            InitializeComponent();
            init();
        }

        private void init()
        {
            lv.MouseDoubleClick += lv_MouseDoubleClick;
            lv.CanExpandGetter += delegate (object row)
            {
                return row is FocusSetMainItem;
            };

            lv.ChildrenGetter += delegate (object row)
            {
                if (row is FocusSetMainItem)
                {
                    FocusSetMainItem item = row as FocusSetMainItem;
                    List<object> objs = new List<object>();
                    foreach (EventItem evtItem in item.Items)
                    {
                        objs.Add(evtItem);
                    }
                    foreach (CheckLogItem checkItem in item.LogItems)
                    {
                        objs.Add(checkItem);
                    }
                    return objs;
                }
                return null;
            };

            colLng.AspectGetter += delegate (object row)
            {
                if (row is FocusSetMainItem)
                {
                    FocusSetMainItem item = row as FocusSetMainItem;
                    return item.Lng;
                }
                else if (row is EventItem)
                {
                    EventItem item = row as EventItem;
                    return item.Longitude;
                }
                return null;
            };

            colLat.AspectGetter += delegate (object row)
            {
                if (row is FocusSetMainItem)
                {
                    FocusSetMainItem item = row as FocusSetMainItem;
                    return item.Lat;
                }
                else if (row is EventItem)
                {
                    EventItem item = row as EventItem;
                    return item.Latitude;
                }
                return null;
            };

            setEventItem();

            setFocusSetMainItem();

            setCheckLogItem();
        }

        private void setFocusSetMainItem()
        {
            colSN.AspectGetter += delegate (object row)
            {
                if (row is FocusSetMainItem)
                {
                    FocusSetMainItem item = row as FocusSetMainItem;
                    return item.OrderID;
                }
                return null;
            };

            colRoadNames.AspectGetter += delegate (object row)
            {
                if (row is FocusSetMainItem)
                {
                    FocusSetMainItem item = row as FocusSetMainItem;
                    return item.RoadNames;
                }
                return null;
            };

            colOrderDate.AspectGetter += delegate (object row)
            {
                if (row is FocusSetMainItem)
                {
                    FocusSetMainItem item = row as FocusSetMainItem;
                    return item.CreateDate;
                }
                return null;
            };

            colAreaNames.AspectGetter += delegate (object row)
            {
                if (row is FocusSetMainItem)
                {
                    FocusSetMainItem item = row as FocusSetMainItem;
                    return item.AreaNames;
                }
                return null;
            };

            colItemCount.AspectGetter += delegate (object row)
            {
                if (row is FocusSetMainItem)
                {
                    FocusSetMainItem item = row as FocusSetMainItem;
                    return item.EventCount;
                }
                return null;
            };

            colStatusDesc.AspectGetter += delegate (object row)
            {
                if (row is FocusSetMainItem)
                {
                    FocusSetMainItem item = row as FocusSetMainItem;
                    return item.StatusDesc;
                }
                return null;
            };
        }

        private void setCheckLogItem()
        {
            colCheckTime.AspectGetter += delegate (object row)
            {
                if (row is CheckLogItem)
                {
                    CheckLogItem item = row as CheckLogItem;
                    return item.CheckTime;
                }
                return null;
            };

            colCheckFile.AspectGetter += delegate (object row)
            {
                if (row is CheckLogItem)
                {
                    CheckLogItem item = row as CheckLogItem;
                    return item.FileName;
                }
                return null;
            };

            colCheckStatus.AspectGetter += delegate (object row)
            {
                if (row is CheckLogItem)
                {
                    CheckLogItem item = row as CheckLogItem;
                    return item.Status;
                }
                return null;
            };

            colCheckCount.AspectGetter += delegate (object row)
            {
                if (row is CheckLogItem)
                {
                    CheckLogItem item = row as CheckLogItem;
                    return item.CheckCount;
                }
                return null;
            };
        }

        private void setEventItem()
        {
            colSolution.AspectGetter += delegate (object row)
            {
                if (row is EventItem)
                {
                    EventItem item = row as EventItem;
                    return item.SolutionDesc;
                }
                return null;
            };

            colStartTime.AspectGetter += delegate (object row)
            {
                if (row is EventItem)
                {
                    EventItem item = row as EventItem;
                    return item.BeginTime;
                }
                return null;
            };

            colReasonDesc.AspectGetter += delegate (object row)
            {
                if (row is EventItem)
                {
                    EventItem item = row as EventItem;
                    return item.ReasonDesc;
                }
                return null;
            };

            colPreType.AspectGetter += delegate (object row)
            {
                if (row is EventItem)
                {
                    EventItem item = row as EventItem;
                    return item.PreTypeDesc;
                }
                return null;
            };

            colEndTime.AspectGetter += delegate (object row)
            {
                if (row is EventItem)
                {
                    EventItem item = row as EventItem;
                    return item.EndTime;
                }
                return null;
            };

            colEventName.AspectGetter += delegate (object row)
            {
                if (row is EventItem)
                {
                    EventItem item = row as EventItem;
                    return item.Name;
                }
                return null;
            };

            colFileName.AspectGetter += delegate (object row)
            {
                if (row is EventItem)
                {
                    EventItem item = row as EventItem;
                    return item.FileName;
                }
                return null;
            };

            colItemID.AspectGetter += delegate (object row)
            {
                if (row is EventItem)
                {
                    EventItem item = row as EventItem;
                    return item.ItemID;
                }
                return null;
            };
        }

        void lv_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            OlvListViewHitTestInfo info = lv.OlvHitTest(e.X, e.Y);
            FocusSetMainItem mainItem = null;
            EventItem evtItem = null;
            if (info.RowObject is FocusSetMainItem)
            {
                mainItem = info.RowObject as FocusSetMainItem;
            }
            else if (info.RowObject is EventItem)
            {
                evtItem = info.RowObject as EventItem;
            }
            makeSureLayerVisible();
            if (mainItem!=null)
            {
                double minLng = double.MaxValue;
                double maxLng = double.MinValue;
                double minLat = double.MaxValue;
                double maxLat = double.MinValue;
                foreach (EventItem evt in mainItem.Items)
                {
                    minLng = Math.Min(minLng, evt.Longitude);
                    maxLng = Math.Max(maxLng, evt.Longitude);
                    minLat = Math.Min(minLat, evt.Latitude);
                    maxLat = Math.Max(maxLat, evt.Latitude);
                }
                MasterCom.MTGis.DbRect rect = new MTGis.DbRect(minLng - 0.002, minLat - 0.002, maxLng + 0.002, maxLat + 0.002);
                MainModel.MainForm.GetMapForm().GoToView(rect);
            }
            else if(evtItem!=null)
            {
                MainModel.SelectedEvents.Clear();
                MainModel.SelectedEvents.Add(evtItem);
                MainModel.MainForm.GetMapForm().GoToView(evtItem.Longitude, evtItem.Latitude, 4000);
            }
            layer.SelOrder = mainItem;
        }

        public List<FocusSetMainItem> Orders { get; set; } = new List<FocusSetMainItem>();
        internal void FillData(List<FocusSetMainItem> list, DateTime dtFrom, DateTime dtTo, string orderIDLower)
        {
            Orders = list;
            makeSureLayerVisible();
            lv.ClearObjects();
            lv.SetObjects(list);
            layer.Orders = list;
            lv.ExpandAll();
            this.dtFrom.Value = dtFrom;
            this.dtTo.Value = dtTo;
            this.txtOrderID.Text = orderIDLower;
        }

        FocusSetLayer layer;
        private void makeSureLayerVisible()
        {
            MapForm mf = MainModel.MainForm.GetMapForm();
            layer = mf.GetLayerBase(typeof(FocusSetLayer)) as FocusSetLayer;
        }

        private void miReplayEvt_Click(object sender, EventArgs e)
        {
            EventItem evt = lv.SelectedObject as EventItem;
            FileReplayer.ReplayOnePart(evt, new MasterCom.Util.TimePeriod(evt.BeginTime, evt.EndTime));
        }

        private void miExport2Xls_Click(object sender, EventArgs e)
        {
            List<NPOIRow> rows = new List<NPOIRow>();
            NPOIRow row = new NPOIRow();
            row.AddCellValue("工单ID");
            row.AddCellValue("状态");
            row.AddCellValue("汇聚事件个数");
            row.AddCellValue("工单首事件时间");
            row.AddCellValue("区域");
            row.AddCellValue("道路");
            row.AddCellValue("经度");
            row.AddCellValue("纬度");
            row.AddCellValue("序号");
            row.AddCellValue("经度");
            row.AddCellValue("纬度");
            row.AddCellValue("事件名称");
            row.AddCellValue("问题类型");
            row.AddCellValue("预判详情");
            row.AddCellValue("解决方案");
            row.AddCellValue("开始时间");
            row.AddCellValue("结束时间");
            row.AddCellValue("文件名");
            rows.Add(row);
            foreach (FocusSetMainItem mainItem in lv.Roots)
            {
                row = new NPOIRow();
                rows.Add(row);
                row.AddCellValue(mainItem.OrderID);
                row.AddCellValue(mainItem.StatusDesc);
                row.AddCellValue(mainItem.EventCount);
                row.AddCellValue(mainItem.CreateDate);
                row.AddCellValue(mainItem.AreaNames);
                row.AddCellValue(mainItem.RoadNames);
                row.AddCellValue(mainItem.Lng);
                row.AddCellValue(mainItem.Lat);
                foreach (EventItem evtItem in mainItem.Items)
                {
                    NPOIRow evtRow = new NPOIRow();
                    row.AddSubRow(evtRow);
                    evtRow.AddCellValue(evtItem.ItemID);
                    evtRow.AddCellValue(evtItem.Longitude);
                    evtRow.AddCellValue(evtItem.Latitude);
                    evtRow.AddCellValue(evtItem.Name);
                    evtRow.AddCellValue(evtItem.PreTypeDesc);
                    evtRow.AddCellValue(evtItem.ReasonDesc);
                    evtRow.AddCellValue(evtItem.SolutionDesc);
                    evtRow.AddCellValue(evtItem.BeginTime);
                    evtRow.AddCellValue(evtItem.EndTime);
                    evtRow.AddCellValue(evtItem.FileName);
                }
            }
            ExcelNPOIManager.ExportToExcel(rows);
        }

        private void ctxMenu_Opening(object sender, CancelEventArgs e)
        {
            miReplayEvt.Enabled = lv.SelectedObject is EventItem;
            miReplayFile.Enabled = lv.SelectedObject is CheckLogItem;
        }

        private DateTime dateFrom
        {
            get
            {
                return dtFrom.Value.Date;
            }
        }

        public DateTime dateTo
        {
            get { return dtTo.Value.Date.AddDays(1).AddMilliseconds(-1); }
        }

        public string orderIDLower
        {
            get { return txtOrderID.Text.Trim().ToLower(); }
        }

        private void btnFilter_Click(object sender, EventArgs e)
        {
            List<FocusSetMainItem> filteredItems = new List<FocusSetMainItem>();
            foreach (FocusSetMainItem item in Orders)
            {
                if (dateFrom <= item.CreateDate && item.CreateDate <= dateTo
                    && (orderIDLower.Length == 0
                    || item.OrderID.ToLower().Contains(orderIDLower)))
                {
                    filteredItems.Add(item);
                }
            }
            lv.ClearObjects();
            lv.SetObjects(filteredItems);
            makeSureLayerVisible();
            layer.Orders = filteredItems;
        }

        private void btnQuery_Click(object sender, EventArgs e)
        {
            QueryJiLinFocusSet query = new QueryJiLinFocusSet();
            query.IsShowConditonDlg = false;
            query.DateFrom = dateFrom;
            query.DateTo = dateTo;
            query.OrderIDLower = orderIDLower;
            query.Query();
        }

        private void miCollapseAll_Click(object sender, EventArgs e)
        {
            lv.CollapseAll();
        }

        private void miExpandAll_Click(object sender, EventArgs e)
        {
            lv.ExpandAll();
        }

        private void miReplayFile_Click(object sender, EventArgs e)
        {
            CheckLogItem log = lv.SelectedObject as CheckLogItem;
            FileReplayer.ReplayOnePart(log, new MasterCom.Util.TimePeriod(log.STime, log.ETime));
        }

       
    }
}
