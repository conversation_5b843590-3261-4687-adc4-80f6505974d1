﻿using MasterCom.MControls;
using MasterCom.MTGis;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRPilotFrequencyPolluteBlockLayer : LayerBase
    {
        readonly MapOperation opt;
        public Pen selPen { get; set; } = new Pen(Color.Cyan, 3);
        public Dictionary<NRPilotFrequencyPolluteBlock, NRCell> CurSelBlockWcellDic { get; set; } 
            = new Dictionary<NRPilotFrequencyPolluteBlock, NRCell>();
        public Dictionary<int, bool> CurSelTestPointBlockDic { get; set; } = new Dictionary<int, bool>();

        public NRPilotFrequencyPolluteBlockLayer()
           : base("NR导频污染图层")
        {
            MapForm mf = MainModel.MainForm.GetMapForm();
            opt = mf.GetMapOperation();
        }

        public Color getColorFrom(NRPilotFrequencyPolluteBlock tpb)
        {
            return getColorFromRange(MainModel.PilotFrequencyPolluteBlockColorRanges, (float)tpb.TestPointCount);
        }

        public Color getColorFromRange(List<ColorRange> ranges, float value) //根据value从ranges取颜色
        {
            int count = ranges.Count;
            if (count == 0)
            {
                return Color.Empty;
            }
            for (int i = 0; i < count; i++)
            {
                ColorRange cr = ranges[i];
                if (value >= cr.minValue && value < cr.maxValue)
                {
                    return cr.color;
                }
            }
            if (value == ranges[count - 1].maxValue)
            {
                return ranges[count - 1].color;
            }
            return Color.Black;
        }

        public override void Draw(System.Drawing.Rectangle clientRect, System.Drawing.Rectangle updateRect, Graphics graphics)
        {
            if (!IsVisible)
            {
                return;
            }
            updateRect.Inflate((int)(40 * 10000 / mapScale), (int)(40 * 10000 / mapScale));
            DbRect dRect;
            gisAdapter.FromDisplay(updateRect, out dRect);
            graphics.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.HighQuality;

            double temp_long = opt.GetCenter().x;
            double temp_lati = opt.GetCenter().y;
            DbPoint ptDSel = new DbPoint(temp_long, temp_lati);
            PointF scrPointSel;
            gisAdapter.ToDisplay(ptDSel, out scrPointSel);
            //底层20米的精度跨度大小 0.0001951;
            double llGap = (0.0001951 / 20) * ZTDIYPilotFrequencyPolluteByRegion_W.PilotFrequencyPolluteBlockRadius;
            DbPoint ptDSel2 = new DbPoint(temp_long + llGap, temp_lati);
            PointF scrPointSel2;
            gisAdapter.ToDisplay(ptDSel2, out scrPointSel2);
            int rGap = (int)((scrPointSel2.X - scrPointSel.X) / 2) + 1;
            DrawItem(MainModel.CurPilotFrequencyPolluteBlockList_NR, dRect, rGap, graphics, 0);
        }

        private void DrawItem(List<NRPilotFrequencyPolluteBlock> PilotFrequencyPolluteBlockList, DbRect dRect
            , int rGap, Graphics graphics, int blockType)
        {
            foreach (NRPilotFrequencyPolluteBlock block in PilotFrequencyPolluteBlockList)
            {
                SolidBrush brush = getBrush(blockType, block);
                if (block.Within(dRect.x1, dRect.y1, dRect.x2, dRect.y2))
                {
                    bool selected = false;
                    if (CurSelTestPointBlockDic != null && CurSelTestPointBlockDic.ContainsKey(block.ID))
                    {
                        selected = true;
                    }
                    Region blockReg = getRegion(rGap, block);
                    if (blockReg != null)
                    {
                        graphics.FillRegion(brush, blockReg);

                        drawSelected(graphics, block, brush, selected, blockReg);
                    }
                }
            }
        }

        private Region getRegion(int rGap, NRPilotFrequencyPolluteBlock block)
        {
            List<TestPoint> tpList = block.TestPoints;
            Region blockReg = null;
            foreach (TestPoint tp in tpList)
            {
                DbPoint dPoint = new DbPoint(tp.Longitude, tp.Latitude);
                PointF point;
                gisAdapter.ToDisplay(dPoint, out point);
                float radius = rGap > 4 ? rGap : 4;
                RectangleF rect = new RectangleF(point.X - radius, point.Y - radius, radius * 2, radius * 2);
                GraphicsPath gp = new GraphicsPath();
                gp.AddEllipse(rect);
                if (blockReg == null)
                {
                    blockReg = new Region(gp);
                }
                else
                {
                    blockReg.Union(gp);
                }
            }

            return blockReg;
        }

        private void drawSelected(Graphics graphics, NRPilotFrequencyPolluteBlock block, SolidBrush brush, bool selected, Region blockReg)
        {
            if (selected)
            {
                RectangleF rctf = blockReg.GetBounds(graphics);
                graphics.DrawRectangle(selPen, (int)rctf.Left, (int)rctf.Top, (int)rctf.Width, (int)rctf.Height);
                NRCell cell;
                CurSelBlockWcellDic.TryGetValue(block, out cell);
                if (cell != null)
                {
                    Pen pen = new Pen(brush, 2);
                    DbPoint dPointCell = new DbPoint(cell.Longitude, cell.Latitude);
                    PointF pointCell;
                    gisAdapter.ToDisplay(dPointCell, out pointCell);
                    graphics.DrawLine(pen, new PointF(rctf.X + rctf.Width / 2, rctf.Y + rctf.Height / 2), pointCell);
                }
            }
        }

        private SolidBrush getBrush(int blockType, NRPilotFrequencyPolluteBlock block)
        {
            SolidBrush brush = new SolidBrush(Color.Yellow);
            if (blockType == 0)
            {
                brush = new SolidBrush(getColorFrom(block));
            }
            else if (blockType == 1)
            {
                brush = new SolidBrush(Color.Yellow);
            }
            else if (blockType == 2)
            {
                brush = new SolidBrush(Color.Red);
            }
            else if (blockType == 3)
            {
                brush = new SolidBrush(Color.Black);
            }

            return brush;
        }
    }
}
