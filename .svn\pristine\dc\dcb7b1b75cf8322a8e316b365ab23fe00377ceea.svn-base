﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.KPI_Statistics;

namespace MasterCom.RAMS.ZTFunc.CQTSiteStat
{
    public class SiteStatInfo
    {
        public List<string> Keys
        {
            get;
            set;
        }
        public SiteStatInfo(bool isMerge)
            : this()
        {
            this.IsSum = isMerge;
        }
        public SiteStatInfo()
        {
            Keys = new List<string>();
            this.KPIData = new KPIDataGroup(null);
        }

        public KPIDataGroup KPIData
        {
            get;
            set;
        }
        
        public bool IsSum { get; set; } = false;

        public string TestDate { get; set; }
    }

}
