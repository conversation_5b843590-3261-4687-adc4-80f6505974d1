﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTHandoverBehindTimeCondition
    {
        public float MinValue { get; set; } = -140;
        public float MaxValue { get; set; } = -10;
        public float SvrPccpchMax { get; set; }
        public float NCellPccpch { get; set; }
        public float PccpchDiffMin { get; set; }
        public int StaySecondsMin { get; set; }
        public bool CheckType { get; set; }
        public bool CheckSameBand { get; set; }
        public ZTHandoverBehindTimeCondition(float svrPccpchMax, float nCellPccpchMin, float pccpchDiffMin, 
            int staySecondsMin, bool checkType)
        {
            this.SvrPccpchMax = svrPccpchMax;
            this.NCellPccpch = nCellPccpchMin;
            this.PccpchDiffMin = pccpchDiffMin;
            this.StaySecondsMin = staySecondsMin;
            this.CheckType = checkType;
        }

        public bool IsMatchMaxSvrPccpch(float? pccpch)
        {
            return (pccpch != null && MinValue <= pccpch && pccpch <= MaxValue && pccpch <= SvrPccpchMax);
        }

        public bool IsMatchMinNCellPccpch(float? pccpch)
        {
            bool matched = false;
            if (pccpch != null && MinValue <= pccpch && pccpch <= MaxValue)
            {
                matched = ((float)pccpch) >= NCellPccpch;
            }
            return matched;
        }

        public bool IsMatchMinPccpchDiff(float pccpchDiff)
        {
            return pccpchDiff >= PccpchDiffMin;
        }

        public bool IsMatchMinStaySeconds(float staySeconds)
        {
            return staySeconds >= StaySecondsMin;
        }

    }
}
