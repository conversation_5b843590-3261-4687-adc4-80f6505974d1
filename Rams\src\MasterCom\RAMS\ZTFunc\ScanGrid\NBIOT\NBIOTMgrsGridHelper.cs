﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public static class NbIotMgrsGridHelper
    {
        /// <summary>
        /// 过滤对应频段的小区
        /// </summary>
        /// <param name="cellList"></param>
        /// <param name="type"></param>
        /// <returns></returns>
        public static List<ScanGridInfo> FilterCells(List<ScanGridInfo> cellList, string type)
        {
            if (type == "不分段")
            {
                return cellList;
            }

            List<ScanGridInfo> retList = new List<ScanGridInfo>();
            for (int i = 0; i < cellList.Count; ++i)
            {
                ScanGridInfo cell = cellList[i];
                if (NbIotMgrsBaseSettingManager.Instance.JudgeInBand(type, cell.EARFCN))
                {
                    retList.Add(cell);
                }
            }
            return retList;
        }
    }
}
