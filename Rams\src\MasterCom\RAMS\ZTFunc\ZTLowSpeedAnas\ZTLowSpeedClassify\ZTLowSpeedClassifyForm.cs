﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MapWinGIS;
using MasterCom.MTGis;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using DevExpress.XtraGrid;
using DevExpress.XtraGrid.Columns;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTLowSpeedClassifyForm  : MinCloseForm
    {
        public ZTLowSpeedClassifyForm(MainModel mainModel)
            : base(mainModel)
        {
            InitializeComponent();
            initColumn();
        }

        Dictionary<string, DataTable> dicResult = new Dictionary<string, DataTable>();
        #region 列头初始化
        private void initColumn()
        {
            this.gridControl1.UseEmbeddedNavigator = true;
            this.gridControl1.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gridControl1.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gridControl1.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gridControl1.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gridControl1.EmbeddedNavigator.Buttons.Remove.Visible = false;

            this.gridControl2.UseEmbeddedNavigator = true;
            this.gridControl2.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gridControl2.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gridControl2.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gridControl2.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gridControl2.EmbeddedNavigator.Buttons.Remove.Visible = false;

            this.gridControl3.UseEmbeddedNavigator = true;
            this.gridControl3.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gridControl3.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gridControl3.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gridControl3.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gridControl3.EmbeddedNavigator.Buttons.Remove.Visible = false;

            this.gridControl4.UseEmbeddedNavigator = true;
            this.gridControl4.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gridControl4.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gridControl4.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gridControl4.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gridControl4.EmbeddedNavigator.Buttons.Remove.Visible = false;

            this.gvDown.Columns.Clear();
            this.gvDown.Columns.AddRange(GetHeaderColumns(0));
            this.gvDown.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gvDown.Appearance.HeaderPanel.TextOptions.Trimming = DevExpress.Utils.Trimming.None;
            this.gvDown.Appearance.HeaderPanel.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gvDown.ColumnPanelRowHeight = 50;
            this.gvDown.OptionsBehavior.Editable = false;
            this.gvDown.OptionsSelection.MultiSelect = true;
            this.gvDown.OptionsView.ColumnAutoWidth = false;
            this.gvDown.OptionsView.ShowDetailButtons = false;
            this.gvDown.OptionsView.ShowGroupPanel = false;

            this.gvUpload.Columns.Clear();
            this.gvUpload.Columns.AddRange(GetHeaderColumns(0));
            this.gvUpload.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gvUpload.Appearance.HeaderPanel.TextOptions.Trimming = DevExpress.Utils.Trimming.None;
            this.gvUpload.Appearance.HeaderPanel.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gvUpload.ColumnPanelRowHeight = 50;
            this.gvUpload.OptionsBehavior.Editable = false;
            this.gvUpload.OptionsSelection.MultiSelect = true;
            this.gvUpload.OptionsView.ColumnAutoWidth = false;
            this.gvUpload.OptionsView.ShowDetailButtons = false;
            this.gvUpload.OptionsView.ShowGroupPanel = false;

            this.gvGather.Columns.Clear();
            this.gvGather.Columns.AddRange(GetHeaderColumns(1));
            this.gvGather.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gvGather.Appearance.HeaderPanel.TextOptions.Trimming = DevExpress.Utils.Trimming.None;
            this.gvGather.Appearance.HeaderPanel.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gvGather.ColumnPanelRowHeight = 50;
            this.gvGather.OptionsBehavior.Editable = false;
            this.gvGather.OptionsSelection.MultiSelect = true;
            this.gvGather.OptionsView.ColumnAutoWidth = false;
            this.gvGather.OptionsView.ShowDetailButtons = false;
            this.gvGather.OptionsView.ShowGroupPanel = false;

            this.gvCode.Columns.Clear();
            this.gvCode.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gvCode.Appearance.HeaderPanel.TextOptions.Trimming = DevExpress.Utils.Trimming.None;
            this.gvCode.Appearance.HeaderPanel.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gvCode.ColumnPanelRowHeight = 50;
            this.gvCode.OptionsBehavior.Editable = false;
            this.gvCode.OptionsSelection.MultiSelect = true;
            this.gvCode.OptionsView.ColumnAutoWidth = false;
            this.gvCode.OptionsView.ShowDetailButtons = false;
            this.gvCode.OptionsView.ShowGroupPanel = false;
        }
        private List<string> ColumnsNames(int iType)
        {
            List<string> list = new List<string>();
            if (iType == 0)
            {
                #region 列头
                list.Add("序号");
                list.Add("地市");
                list.Add("文件名");
                list.Add("开始时间");
                list.Add("中心经度");
                list.Add("中心纬度");
                list.Add("干道名称");
                list.Add("网络类型");
                list.Add("业务类型");
                list.Add("持续距离(米)");
                list.Add("持续时间(秒)");
                list.Add("采样点总数");
                list.Add("低速率采样点个数");
                list.Add("低速率采样点占比(%)");
                list.Add("最高速率(M)");
                list.Add("最低速率(M)");
                list.Add("0速率时长（秒）");
                list.Add("0速率里程(米)");
                list.Add("0速率里程占比(%)");
                list.Add("平均速率(M)");
                list.Add("PDSCH_BLER平均值");
                list.Add("RSRP最大值");
                list.Add("RSRP最小值");
                list.Add("RSRP平均值");
                list.Add("邻区RSRP最大值");
                list.Add("邻区RSRP最小值");
                list.Add("邻区RSRP平均值");
                list.Add("SINR最大值");
                list.Add("SINR最小值");
                list.Add("SINR平均值");
                list.Add("连续SINR质差里程占比(%)");
                list.Add("重叠覆盖度≥3比例");
                list.Add("重叠覆盖里程占比");
                list.Add("下行码字0MCS平均值");
                list.Add("下行码字1MCS平均值");
                list.Add("下行码字0最高频率MCS(%)");
                list.Add("下行码字1最高频率MCS(%)");
                list.Add("码字0CQI平均值");
                list.Add("码字1CQI平均值");
                list.Add("下行码字0 64QAM占比");
                list.Add("下行码字1 64QAM占比");
                list.Add("下行码字0 16QAM占比");
                list.Add("下行码字1 16QAM占比");
                list.Add("Throughput_DL最大值");
                list.Add("Throughput_DL最小值");
                list.Add("Throughput_DL平均值");
                list.Add("Transmission_Mode");
                list.Add("TM3比例（%）");
                list.Add("rank_indicator");
                list.Add("双流时长占比（%）");
                list.Add("误块率（%）");
                list.Add("PDSCH_RB_Number");
                list.Add("PRB调度数");
                list.Add("PDCCH_DL_Grant_Count");
                list.Add("Ratio_DL_Code0_HARQ_ACK");
                list.Add("Ratio_DL_Code0_HARQ_NACK");
                list.Add("Ratio_DL_Code1_HARQ_ACK");
                list.Add("Ratio_DL_Code1_HARQ_NACK");
                list.Add("TAC-CellID");
                list.Add("关联小区名称");
                list.Add("频点");
                list.Add("片区名称");
                list.Add("代维区域");

                list.Add("无线环境好采样点占比");
                list.Add("限速情况");
                list.Add("主小区无线环境差但邻小区无线环境好时持续时长(S)");
                list.Add("分析结论"); 
	#endregion
            }
            else
            {
                #region 汇总列头

                list.Add("地市");
                list.Add("高速名称");
                list.Add("业务类型");
                list.Add("问题路段总数");
                list.Add("无线环境好且无限速路段数");
                list.Add("无线环境好且无限速路段占比");
                list.Add("无线环境好且有限速路段数");
                list.Add("无线环境好且有限速路段占比");
                list.Add("无线环境好且部分限速路段数");
                list.Add("无线环境好且部分限速路段占比");
                list.Add("无线环境好路段数占比");
                list.Add("无线环境差路段数");
                list.Add("无线环境差路段占比");

                list.Add("主小区无线环境差部分限速路段数");
                list.Add("主小区无线环境差部分限速路段占比");
                list.Add("主小区无线环境差无限速但邻小区无线环境好路段数");
                list.Add("主小区无线环境差无限速但邻小区无线环境好路段占比");
                list.Add("主小区无线环境差有限速但邻小区无线环境好路段数");
                list.Add("主小区无线环境差有限速但邻小区无线环境好路段占比");
                list.Add("主邻小区无线环境均差且无限速路段数");
                list.Add("主邻小区无线环境均差且无限速路段占比");
                list.Add("主邻小区无线环境均差且有限速路段数");
                list.Add("主邻小区无线环境均差且有限速路段占比");

                list.Add("含限速信令数");
                #endregion

            }
            return list;
        }

        private GridColumn[] GetHeaderColumns(int iType)
        {
            List<GridColumn> listCol = new List<GridColumn>();
            List<string> colNames = ColumnsNames(iType);
            foreach (string colName in colNames)
            {
                GridColumn col = new GridColumn();
                col.FieldName = colName;
                col.Caption = colName;
                col.Visible = true;
                col.VisibleIndex = listCol.Count;
                if (colName.Contains("好") || colName.Contains("差"))
                    col.Width = 120;
                if(colName.Contains("分析"))
                     col.Width = 200;
                listCol.Add(col);
            }
            return listCol.ToArray();
        }

        private DataColumn[] GetDataColumns(int iType)
        {
            List<DataColumn> listCol = new List<DataColumn>();
            List<string> colNames = ColumnsNames(iType);
            foreach (string colName in colNames)
            {
                DataColumn col = new DataColumn(colName);
                listCol.Add(col);
            }
            return listCol.ToArray();
        }
        #endregion

        #region 绑定数据
        public void FillData(List<DIYLowSpeedInfo_LTE> lowSpeedInfoList, DataTable dtCode)
        {
            dicResult.Clear();
            dicResult.Add("信令明细", dtCode);
            string cityName = DistrictManager.GetInstance().getDistrictName(MainModel.DistrictID);
            Dictionary<string, GatherTmp> dicGather = new Dictionary<string, GatherTmp>();
            foreach (DIYLowSpeedInfo_LTE info in lowSpeedInfoList)
            {
                if (!dicResult.ContainsKey(info.AppType))
                {
                    dicResult[info.AppType] = new DataTable(info.AppType);
                    dicResult[info.AppType].Columns.AddRange(GetDataColumns(0));
                }
                DataTable dt = dicResult[info.AppType];
                if (info is DIYLowSpeedInfo_LTE_MainRoad)
                {
                    DIYLowSpeedInfo_LTE_MainRoad mInfo = info as DIYLowSpeedInfo_LTE_MainRoad;
                    DataRow dr = dt.NewRow();
                    #region BingData
                    dr["序号"] = dt.Rows.Count + 1;
                    dr["地市"] = cityName;
                    dr["干道名称"] = mInfo.MotorWay;
                    dr["文件名"] = mInfo.FileName;
                    dr["开始时间"] = mInfo.DateTimeString;
                    dr["中心经度"] = mInfo.Longitude;
                    dr["中心纬度"] = mInfo.Latitude;
                    dr["网络类型"] = mInfo.NetType;
                    dr["业务类型"] = mInfo.AppType;
                    dr["持续距离(米)"] = mInfo.Distance;
                    dr["持续时间(秒)"] = mInfo.Duration;
                    dr["采样点总数"] = mInfo.TotalPointCount;
                    dr["低速率采样点个数"] = mInfo.LowPointCount;
                    dr["低速率采样点占比(%)"] = mInfo.LowPercent;
                    dr["最高速率(M)"] = mInfo.HighSpeed;
                    dr["最低速率(M)"] = mInfo.LowSpeed;
                    dr["0速率时长（秒）"] = mInfo.Duration_speed_0;
                    dr["0速率里程(米)"] = mInfo.Distance_0Speed;
                    dr["0速率里程占比(%)"] = mInfo.Rate_Distance_0Speed;
                    dr["平均速率(M)"] = mInfo.MeanSpeed;
                    dr["PDSCH_BLER平均值"] = mInfo.MeanPDSCHBLER;
                    dr["RSRP最大值"] = mInfo.RSRP_Max;
                    dr["RSRP最小值"] = mInfo.RSRP_Min;
                    dr["RSRP平均值"] = mInfo.RSRP_Mean;
                    dr["邻区RSRP最大值"] = mInfo.NRSRP_Max;
                    dr["邻区RSRP最小值"] = mInfo.NRSRP_Min;
                    dr["邻区RSRP平均值"] = mInfo.NRSRP_Mean;
                    dr["SINR最大值"] = mInfo.SINR_Max;
                    dr["SINR最小值"] = mInfo.SINR_Min;
                    dr["SINR平均值"] = mInfo.SINR_Mean;
                    dr["连续SINR质差里程占比(%)"] = mInfo.RateWeakSinr;
                    dr["重叠覆盖度≥3比例"] = mInfo.RateMultiCoverage;
                    dr["重叠覆盖里程占比"] = mInfo.RateDisMulticoverage;
                    dr["下行码字0MCS平均值"] = mInfo.Code0Mean;
                    dr["下行码字1MCS平均值"] = mInfo.Code1Mean;
                    dr["下行码字0最高频率MCS(%)"] = mInfo.Code0Max;
                    dr["下行码字1最高频率MCS(%)"] = mInfo.Code1Max;
                    dr["码字0CQI平均值"] = mInfo.CQI0Mean;
                    dr["码字1CQI平均值"] = mInfo.CQI1Mean;
                    dr["下行码字0 64QAM占比"] = mInfo.Code0_64QAMRate;
                    dr["下行码字1 64QAM占比"] = mInfo.Code1_64QAMRate;
                    dr["下行码字0 16QAM占比"] = mInfo.Code0_16QAMRate;
                    dr["下行码字1 16QAM占比"] = mInfo.Code1_16QAMRate;
                    dr["Throughput_DL最大值"] = mInfo.Throughput_DL_Max;
                    dr["Throughput_DL最小值"] = mInfo.Throughput_DL_Min;
                    dr["Throughput_DL平均值"] = mInfo.Throughput_DL_Mean;
                    dr["Transmission_Mode"] = mInfo.Transmission_Mode;
                    dr["TM3比例（%）"] = mInfo.Transmission_Mode3;
                    dr["rank_indicator"] = mInfo.Rank_Indicator;
                    dr["双流时长占比（%）"] = mInfo.Rank2_Indicator;
                    dr["误块率（%）"] = mInfo.PDSCH_BLER;
                    dr["PDSCH_RB_Number"] = mInfo.PDSCH_RB_Number;
                    dr["PRB调度数"] = mInfo.PDSCH_PRb_Num_s;
                    dr["PDCCH_DL_Grant_Count"] = mInfo.PDCCH_DL_Grant_Count;
                    dr["Ratio_DL_Code0_HARQ_ACK"] = mInfo.Ratio_DL_Code0_HARQ_ACK;
                    dr["Ratio_DL_Code0_HARQ_NACK"] = mInfo.Ratio_DL_Code0_HARQ_NACK;
                    dr["Ratio_DL_Code1_HARQ_ACK"] = mInfo.Ratio_DL_Code1_HARQ_ACK;
                    dr["Ratio_DL_Code1_HARQ_NACK"] = mInfo.Ratio_DL_Code1_HARQ_NACK;
                    dr["TAC-CellID"] = mInfo.LACCIs;
                    dr["关联小区名称"] = mInfo.CellName;
                    dr["频点"] = mInfo.BCCHs;
                    dr["片区名称"] = mInfo.AreaName;
                    dr["代维区域"] = mInfo.AreaAgentName;
                    #endregion

                    dr["无线环境好采样点占比"] = Math.Round(mInfo.iBestEvment * 1.0 / mInfo.TotalPointCount * 100, 2) + "%";
                    dr["限速情况"] = mInfo.strLimitInfo;
                    dr["主小区无线环境差但邻小区无线环境好时持续时长(S)"] = mInfo.iNCellDuration;
                    dr["分析结论"] = mInfo.WirelessInfo;
                    dt.Rows.Add(dr);

                    string key = string.Format("{0}-{1}", mInfo.MotorWay, mInfo.AppType);
                    if (!dicGather.ContainsKey(key))
                    {
                        dicGather[key] = new GatherTmp();
                        dicGather[key].cityName = cityName;
                    }
                    dicGather[key].FillData(mInfo);
                }
            }
            GatherData(dicGather);
        }

        /// <summary>
        /// 汇总页数据结构
        /// </summary>
        /// <param name="dicGather"></param>
        private void GatherData(Dictionary<string, GatherTmp> dicGather)
        {
            Dictionary<string, GatherTmp> roadGather = new Dictionary<string, GatherTmp>();
            GatherTmp cityGather = new GatherTmp();
            cityGather.AppType = "汇总";
            cityGather.MainRoad = "全部";
            foreach (GatherTmp gather in dicGather.Values)
            {
                if (!roadGather.ContainsKey(gather.MainRoad))
                {
                    roadGather[gather.MainRoad] = new GatherTmp();
                    roadGather[gather.MainRoad].AppType = "汇总";
                    roadGather[gather.MainRoad].cityName = gather.cityName;
                    roadGather[gather.MainRoad].MainRoad = gather.MainRoad;
                }
                GatherTmp tmp = roadGather[gather.MainRoad];
                tmp.Add(gather);
            }
            foreach (string key in roadGather.Keys)
            {
                dicGather[key] = roadGather[key];
            }
            dicResult["汇总页"] = new DataTable("汇总页");
            dicResult["汇总页"].Columns.AddRange(GetDataColumns(1));
            DataTable dtGather = dicResult["汇总页"];
            foreach (GatherTmp gather in dicGather.Values)
            {
                #region 
                if (gather.AppType == "汇总")
                {
                    cityGather.cityName = gather.cityName;
                    cityGather.Add(gather);
                } 
                #endregion
                BindDataGather(dtGather, gather);
            }
            // 地市汇总
            BindDataGather(dtGather, cityGather);
            BindDataToGV();
        }

        private void BindDataGather(DataTable dtGather, GatherTmp gather)
        {
            DataRow dr = dtGather.NewRow();
            dr["地市"] = gather.cityName;
            dr["高速名称"] = gather.MainRoad;
            dr["业务类型"] = gather.AppType;
            dr["问题路段总数"] = gather.SumRoad;
            dr["无线环境好且无限速路段数"] = gather.iWLBestRoadNum;
            dr["无线环境好且无限速路段占比"] = gather.rateWLBest;
            dr["无线环境好且有限速路段数"] = gather.iWLBestLimitRoadNum;
            dr["无线环境好且有限速路段占比"] = gather.rateWLBestLimit;
            dr["无线环境好路段数占比"] = gather.rateAllWLBest;
            dr["无线环境好且部分限速路段数"] = gather.iWLBestPartLimit;
            dr["无线环境好且部分限速路段占比"] = gather.rateWLBestPartLimit;
            dr["无线环境差路段数"] = gather.iWLBadRoadNum;
            dr["无线环境差路段占比"] = gather.rateWLBad;

            dr["主小区无线环境差部分限速路段数"] = gather.iWLBadPartLimit;
            dr["主小区无线环境差部分限速路段占比"] = gather.rateWLBadPartLimit;
            dr["主小区无线环境差无限速但邻小区无线环境好路段数"] = gather.iMainWLBadNBest;
            dr["主小区无线环境差无限速但邻小区无线环境好路段占比"] = gather.rateMainWLBadNBest;
            dr["主小区无线环境差有限速但邻小区无线环境好路段数"] = gather.iMainWLBadLimitNBest;
            dr["主小区无线环境差有限速但邻小区无线环境好路段占比"] = gather.rateMainWLBadLimitNBest;
            dr["主邻小区无线环境均差且无限速路段数"] = gather.iAllCellBad;
            dr["主邻小区无线环境均差且无限速路段占比"] = gather.rateAllCellBad;
            dr["主邻小区无线环境均差且有限速路段数"] = gather.iAllCellBadLimit;
            dr["主邻小区无线环境均差且有限速路段占比"] = gather.rateAllCellBadLimit;

            dr["含限速信令数"] = gather.iLimitNum;
            dtGather.Rows.Add(dr);
        }

        private void BindDataToGV()
        {
            if (dicResult.Count > 0)
            {
                foreach (string key in dicResult.Keys)
                {
                    if (key.Contains("汇总页"))
                        gridControl1.DataSource = dicResult[key];
                    if (key.Contains("信令"))
                        gridControl4.DataSource = dicResult[key];
                    else if (key.Contains(tabPageDown.Text))
                    {
                        bindData(key, gridControl2);
                    }
                    else if (key.Contains(tabPageUpload.Text))
                    {
                        bindData(key, gridControl3);
                    }
                }
            }
        }

        private void bindData(string key, GridControl gridControl)
        {
            if (gridControl.DataSource == null)
                gridControl.DataSource = dicResult[key];
            else
            {
                BindDataToGV(gridControl, dicResult[key]);
            }
        }

        private void BindDataToGV(GridControl gc, DataTable dtNext)
        {
            DataTable dt = (DataTable)gc.DataSource;
            DataTable newDataTable = dt.Copy();
            foreach (DataRow dr in dtNext.Rows)
            {
                newDataTable.ImportRow(dr);
            }
            gc.DataSource = newDataTable;
        }
        #endregion

        private void 导出ExcelToolStripMenuItem_Click(object sender, EventArgs e)
        {
            List<ExportToExcelModel> listData = new List<ExportToExcelModel>();
            ExportToExcelModel exModel = new ExportToExcelModel();
            exModel.Data = gvGather;
            exModel.SheetName = "汇总页";
            listData.Add(exModel);

            exModel = new ExportToExcelModel();
            exModel.Data = gvDown;
            exModel.SheetName = "下载";
            listData.Add(exModel);

            exModel = new ExportToExcelModel();
            exModel.Data = gvUpload;
            exModel.SheetName = "上传";
            listData.Add(exModel);

            exModel = new ExportToExcelModel();
            exModel.Data = gvCode;
            exModel.SheetName = "信令页";
            listData.Add(exModel);
            ExcelNPOIManager.ExportToExcelMore(listData);
        }

        private class GatherTmp
        {
            public string cityName = "";
            public string MainRoad = "";
            public string AppType = "";
            public int SumRoad = 0;

            /// <summary>
            /// 无线环境好且无限速路段
            /// </summary>
            public string rateWLBest
            {
                get
                {
                    if (SumRoad == 0)
                        return "0.00%";
                    return Math.Round(iWLBestRoadNum * 1.0 / SumRoad * 100, 2) + "%";
                }
            }
            public int iWLBestRoadNum = 0;

            /// <summary>
            /// 无线环境好且有限速路段
            /// </summary>
            public string rateWLBestLimit
            {
                get
                {
                    if (SumRoad == 0)
                        return "0.00%";
                    return Math.Round(iWLBestLimitRoadNum * 1.0 / SumRoad * 100, 2) + "%";
                }
            }
            public int iWLBestLimitRoadNum = 0;

            /// <summary>
            /// 无线环境好路段数占比
            /// </summary>
            public string rateAllWLBest
            {
                get
                {
                    if (SumRoad == 0)
                        return "0.00%";
                    return Math.Round((iWLBestRoadNum + iWLBestLimitRoadNum) * 1.0 / SumRoad * 100, 2) + "%";
                }
            }
            /// <summary>
            /// 无线环境差路段数
            /// </summary>
            public string rateWLBad
            {
                get
                {
                    if (SumRoad == 0)
                        return "0.00%";
                    return Math.Round(iWLBadRoadNum * 1.0 / SumRoad * 100, 2) + "%";
                }
            }
            public int iWLBadRoadNum = 0;

            /// <summary>
            /// 主小区无线环境差无限速但邻小区无线环境好路段数
            /// </summary>
            public string rateMainWLBadNBest
            {
                get
                {
                    if (SumRoad == 0)
                        return "0.00%";
                    return Math.Round(iMainWLBadNBest * 1.0 / SumRoad * 100, 2) + "%";
                }
            }
            public int iMainWLBadNBest = 0;

            /// <summary>
            /// 主小区无线环境差有限速但邻小区无线环境好路段数
            /// </summary>
            public string rateMainWLBadLimitNBest
            {
                get
                {
                    if (SumRoad == 0)
                        return "0.00%";
                    return Math.Round(iMainWLBadLimitNBest * 1.0 / SumRoad * 100, 2) + "%";
                }
            }
            public int iMainWLBadLimitNBest = 0;

            /// <summary>
            /// 主邻小区无线环境均差且无限速路段数
            /// </summary>
            public string rateAllCellBad
            {
                get
                {
                    if (SumRoad == 0)
                        return "0.00%";
                    return Math.Round(iAllCellBad * 1.0 / SumRoad * 100, 2) + "%";
                }
            }
            public int iAllCellBad = 0;

            /// <summary>
            /// 主邻小区无线环境均差且有限速路段数
            /// </summary>
            public string rateAllCellBadLimit
            {
                get
                {
                    if (SumRoad == 0)
                        return "0.00%";
                    return Math.Round(iAllCellBadLimit * 1.0 / SumRoad * 100, 2) + "%";
                }
            }
            public int iAllCellBadLimit = 0;
            /// <summary>
            /// 含限速信令数
            /// </summary>
            public int iLimitNum = 0;
            private readonly List<int> iFileList = new List<int>();

            /// <summary>
            /// 无线环境好且部分限速路段数
            /// </summary>
            public string rateWLBestPartLimit
            {
                get
                {
                    if (SumRoad == 0)
                        return "0.00%";
                    return Math.Round(iWLBestPartLimit * 1.0 / SumRoad * 100, 2) + "%";
                }
            }
            public int iWLBestPartLimit = 0;


            /// <summary>
            /// 主小区无线环境差部分限速路段数
            /// </summary>
            public string rateWLBadPartLimit
            {
                get
                {
                    if (SumRoad == 0)
                        return "0.00%";
                    return Math.Round(iWLBadPartLimit * 1.0 / SumRoad * 100, 2) + "%";
                }
            }
            public int iWLBadPartLimit = 0;
           
            public GatherTmp() { }

            public void FillData(DIYLowSpeedInfo_LTE_MainRoad info)
            {
                this.AppType = info.AppType;
                this.SumRoad++;
                this.MainRoad = info.MotorWay;
                if (info.bMainCellEvment)
                {
                    addBestData(info);
                }
                else
                {
                    addBadData(info);
                }

                if (!iFileList.Contains(info.FileID))
                {
                    this.iLimitNum += info.iLimitNum;
                    iFileList.Add(info.FileID);
                }
            }

            private void addBestData(DIYLowSpeedInfo_LTE_MainRoad info)
            {
                if (!info.IsValidLimit)
                {
                    this.iWLBestRoadNum++;
                }
                if (info.IsValidLimit)
                {
                    this.iWLBestLimitRoadNum++;
                }
                if (info.IsPartLimit)
                {
                    this.iWLBestPartLimit++;
                }
            }

            private void addBadData(DIYLowSpeedInfo_LTE_MainRoad info)
            {
                this.iWLBadRoadNum++;
                if (info.bNCellEvment)
                {
                    if (!info.IsValidLimit)
                    {
                        this.iMainWLBadNBest++;
                    }
                    if (info.IsValidLimit)
                    {
                        this.iMainWLBadLimitNBest++;
                    }
                }
                else
                {
                    if (!info.IsValidLimit)
                    {
                        this.iAllCellBad++;
                    }
                    if (info.IsValidLimit)
                    {
                        this.iAllCellBadLimit++;
                    }
                }
                if (info.IsPartLimit)
                {
                    this.iWLBadPartLimit++;
                }
            }

            public void Add(GatherTmp tmp)
            {
                this.SumRoad += tmp.SumRoad;
                this.iWLBestRoadNum += tmp.iWLBestRoadNum;
                this.iWLBestLimitRoadNum += tmp.iWLBestLimitRoadNum;
                this.iWLBadRoadNum += tmp.iWLBadRoadNum;

                this.iMainWLBadNBest += tmp.iMainWLBadNBest;
                this.iMainWLBadLimitNBest += tmp.iMainWLBadLimitNBest;
                this.iAllCellBad += tmp.iAllCellBad;
                this.iAllCellBadLimit += tmp.iAllCellBadLimit;
                this.iLimitNum += tmp.iLimitNum;

                this.iWLBestPartLimit += tmp.iWLBestPartLimit;
                this.iWLBadPartLimit += tmp.iWLBadPartLimit;
            }
        }
    }
}
