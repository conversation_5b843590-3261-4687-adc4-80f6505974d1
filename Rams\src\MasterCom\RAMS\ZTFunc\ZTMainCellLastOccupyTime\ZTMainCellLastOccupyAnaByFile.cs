﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTMainCellLastOccupyAnaByFile_GSM : ZTMainCellLastOccupyAnaBase
    {
        public ZTMainCellLastOccupyAnaByFile_GSM(MainModel mainModel)
            : base(mainModel)
        {
            baseParamName = new ParamName("RxLevSub", "RxQualSub");
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.GSM_VOICE);
            ServiceTypes.Add(ServiceType.EDGE_DATA);
            ServiceTypes.Add(ServiceType.GPRS_DATA);

            eventIDLst.Clear();
            foreach (EEventGSMID id in Enum.GetValues(typeof(EEventGSMID)))
            {
                eventIDLst.Add((int)id);
            }
        }

        public override string Name
        {
            get { return "GSM主服占用时长(按文件)"; }
        }
        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22026, this.Name);//////
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.File;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                return true;
            }
            return false;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        ZTMainCellLastOccupySetGSMConditionForm setForm = null;
        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }
            if (setForm == null)
            {
                setForm = new ZTMainCellLastOccupySetGSMConditionForm();
            }
            if (setForm.ShowDialog() == DialogResult.OK)
            {
                hoCondition = setForm.GetCondition();
                return true;
            }
            return false;
        }

        /// <summary>
        /// 判断是否在所选区域内
        /// </summary>
        /// <param name="testPoint"></param>
        /// <returns></returns>
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            if (testPoint is TestPointDetail)
            {
                return true;
            }
            else
                return false;
        }
    }

    public class ZTMainCellLastOccupyAnaByFile_TD : ZTMainCellLastOccupyAnaByFile_GSM
    {
        public ZTMainCellLastOccupyAnaByFile_TD(MainModel mainModel)
            : base(mainModel)
        {
            baseParamName = new ParamName("TD_PCCPCH_RSCP", "TD_PCCPCH_C2I");
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.TDSCDMA_VOICE);
            ServiceTypes.Add(ServiceType.TDSCDMA_DATA);
            ServiceTypes.Add(ServiceType.TDSCDMA_HSDPA);
            ServiceTypes.Add(ServiceType.TDSCDMA_HSUPA);

            eventIDLst.Clear();
            foreach (EEventTDID id in Enum.GetValues(typeof(EEventTDID)))
            {
                eventIDLst.Add((int)id);
            }
        }

        public override string Name
        {
            get { return "TD主服占用时长(按文件)"; }
        }

        protected override bool getCondition()
        {
            return true;
        }

        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            if (testPoint is TDTestPointDetail)
            {
                return true;
            }
            else
                return false;
        }
    }

    public class ZTMainCellLastOccupyAnaByFile_LTE : ZTMainCellLastOccupyAnaByFile_TD
    {
        public ZTMainCellLastOccupyAnaByFile_LTE(MainModel mainModel)
            : base(mainModel)
        {
            baseParamName = new ParamName("lte_RSRP", "lte_SINR");
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_TDD_VOICE);
            ServiceTypes.Add(ServiceType.LTE_TDD_DATA);
            ServiceTypes.Add(ServiceType.LTE_TDD_VOLTE);
            ServiceTypes.Add(ServiceType.SER_LTE_TDD_VIDEO_VOLTE);

            eventIDLst.Clear();
            foreach (EEventLTEID id in Enum.GetValues(typeof(EEventLTEID)))
            {
                eventIDLst.Add((int)id);
            }
        }

        public override string Name
        {
            get { return "LTE主服占用时长(按文件)"; }
        }

        ZTMainCellLastOccupySetLTEConditionForm setForm = null;
        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }
            if (setForm == null)
            {
                setForm = new ZTMainCellLastOccupySetLTEConditionForm();
            }
            if (setForm.ShowDialog() == DialogResult.OK)
            {
                hoCondition = setForm.GetCondition();
                return true;
            }
            return false;
        }

        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            if (testPoint is LTETestPointDetail)
            {
                return true;
            }
            else
                return false;
        }
    }
}
