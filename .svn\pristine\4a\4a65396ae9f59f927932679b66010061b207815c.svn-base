﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    /// <summary>
    /// 流媒体分析单元
    /// </summary>
    public class NRVideoPlayAnaItem : NRMobileServiceInfoBase
    {
        public NRVideoPlayAnaItem(string fileName)
        {
            this.FileName = fileName;
            this.TpsList = new List<TestPoint>();
            this.OtherTpsList = new List<TestPoint>();
            this.OtherMaxNBIndexList = new List<int>();
        }

        public string URL { get; set; }
        public double InterTime { get; set; }
        /// <summary>
        /// 非卡顿（缓冲）区间的采样点
        /// </summary>
        public List<TestPoint> OtherTpsList { get; set; }
        /// <summary>
        ///  非卡顿（缓冲)区间的最强邻区索引表
        /// </summary>
        public List<int> OtherMaxNBIndexList { get; set; }

        /// <summary>
        /// 根据新增加的非卡顿（缓冲）区间的采样点相应生成最强邻区索引表
        /// </summary>
        /// <param name="tpList"></param>
        /// <param name="nCellRsrpStr"></param>
        public void fillOtherMaxNBIndexList(List<TestPoint> tpList)
        {
            if (tpList == null || tpList.Count == 0)
            {
                return;
            }
            List<int> indexList = new List<int>();
            foreach (TestPoint tp in tpList)
            {
                findMaxNBCell(tp, indexList);
            }
            this.OtherMaxNBIndexList.AddRange(indexList);
        }
    }

    public class NRVideoPlayAnaCondition
    {
        /// <summary>
        /// 流媒体卡顿前几秒
        /// </summary>
        public int PreRebufferTime { get; set; } = 5;
        /// <summary>
        /// 流媒体缓冲前几秒
        /// </summary>
        public int PreLoadTime { get; set; } = 5;
    }
}
