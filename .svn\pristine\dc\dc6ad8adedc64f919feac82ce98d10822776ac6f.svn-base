﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause
{
    [Serializable]
    public class HoUpdateCause : CauseBase
    {
        public HoUpdateCause()
        {
            AddSubReason(new HoTooOftenCause());
            AddSubReason(new TAUpdateTooOftenCause());
            AddSubReason(new UnreasonableHoPoorMainRSRP());
            AddSubReason(new UnreasonableHoGoodNbRSRP());
            AddSubReason(new HoBehindTime());

            //AddSubReason(new CellErrorCause())
            AddSubReason(new IFHOChangedCause());
            AddSubReason(new PRBLowSchedulingCause());
            AddSubReason(new RRCReEstablishFailCause());
            AddSubReason(new RRCSetupFailCause());
            AddSubReason(new TrackAreaUpdateCause());
        }

        public override string Name
        {
            get { return "切换和TAC区域更新"; }
        }

        public override string Desc
        {
            get { return "质量引起的低速率"; }
        }

        public override string Suggestion
        {
            get { return "未知质量原因"; }
        }

        public override void Judge(LowSpeedSeg segItem, List<Model.Event> evts, List<TestPoint> allTP)
        {
            foreach (CauseBase r in SubCauses)
            {
                if (segItem.NeedJudge)
                {
                    r.Judge(segItem, evts, allTP);
                }
                else
                {
                    break;
                }
            }
        }


        public override Dictionary<string, object> CfgParam
        {
            get
            {
                Dictionary<string, object> paramDic = new Dictionary<string, object>();
                paramDic["TypeName"] = this.GetType().FullName;
                List<object> list = new List<object>();
                foreach (CauseBase cause in SubCauses)
                {
                    list.Add(cause.CfgParam);
                }
                paramDic["SubCauseSet"] = list;

                return paramDic;

            }
            set
            {
                if (value == null)
                {
                    return;
                }

                SubCauses = new List<CauseBase>();
                List<object> list = value["SubCauseSet"] as List<object>;
                foreach (object item in list)
                {
                    Dictionary<string, object> dic = item as Dictionary<string, object>;
                    string typeName = dic["TypeName"].ToString();
                    System.Reflection.Assembly assembly = System.Reflection.Assembly.GetExecutingAssembly();
                    CauseBase cause = (CauseBase)assembly.CreateInstance(typeName);
                    cause.CfgParam = dic;
                    AddSubReason(cause);
                }
            }
        }
    }

    [Serializable]
    public class HoTooOftenCause : CauseBase
    {
        public override string Name
        {
            get { return "频繁切换"; }
        }
        
        public int Second { get; set; } = 10;
        public int Times { get; set; } = 2;
        public override string Desc
        {
            get { return string.Format("在{0}秒内发生{1}次及以上切换", Second, Times); }
        }

        public override string Suggestion
        {
            get { return "核查小区切换参数"; }
        }

        public override void Judge(LowSpeedSeg segItem, List<Model.Event> evts, List<TestPoint> allTP)
        {
            foreach (TestPoint pnt in segItem.TestPoints)
            {
                if (!segItem.IsNeedJudge(pnt))
                {
                    continue;
                }
                List<Event> hoEvts = new List<Event>();
                int bTime = pnt.Time - Second / 2;
                int eTime = pnt.Time + Second / 2;
                foreach (Event evt in evts)
                {
                    if ((evt.ID == 1039 || evt.ID == 851 || evt.ID == 899
                        || evt.ID == 3138 || evt.ID == 3156 || evt.ID == 3159)
                        && bTime <= evt.Time && evt.Time <= eTime)
                    {
                        hoEvts.Add(evt);
                    }
                }
                if (hoEvts.Count >= Times)
                {
                    HoTooOftenCause cln = this.Clone() as HoTooOftenCause;
                    segItem.SetReason(new LowSpeedPointDetail(pnt, cln));
                }
            }
        }


        public override Dictionary<string, object> CfgParam
        {
            get
            {
                Dictionary<string, object> paramDic = new Dictionary<string, object>();
                paramDic["TypeName"] = this.GetType().FullName;
                paramDic["second"] = this.Second;
                paramDic["times"] = this.Times;

                return paramDic;
            }
            set
            {
                if (value == null)
                {
                    return;
                }

                this.Second = (int)value["second"];
                this.Times = (int)value["times"];
            }
        }
    }

    [Serializable]
    public class TAUpdateTooOftenCause : CauseBase
    {
        public override string Name
        {
            get { return "频繁更新"; }
        }

        public int Second { get; set; } = 10;
        public int Times { get; set; } = 2;
        public override string Desc
        {
            get { return string.Format("在{0}秒内发生{1}次及以上更新", Second, Times); }
        }

        public override string Suggestion
        {
            get { return "核查小区{0}区域边界"; }
        }

        public override void Judge(LowSpeedSeg segItem, List<Model.Event> evts, List<TestPoint> allTP)
        {
            foreach (TestPoint pnt in segItem.TestPoints)
            {
                if (!segItem.IsNeedJudge(pnt))
                {
                    continue;
                }
                List<Event> hoEvts = new List<Event>();
                int bTime = pnt.Time - Second / 2;
                int eTime = pnt.Time + Second / 2;
                foreach (Event evt in evts)
                {
                    if ((evt.ID == 853 || evt.ID == 3172) && bTime <= evt.Time && evt.Time <= eTime)
                    {
                        hoEvts.Add(evt);
                    }
                }
                if (hoEvts.Count >= Times)
                {
                    TAUpdateTooOftenCause cln = this.Clone() as TAUpdateTooOftenCause;
                    segItem.SetReason(new LowSpeedPointDetail(pnt, cln));
                }
            }
        }


        public override Dictionary<string, object> CfgParam
        {
            get
            {
                Dictionary<string, object> paramDic = new Dictionary<string, object>();
                paramDic["TypeName"] = this.GetType().FullName;
                paramDic["second"] = this.Second;
                paramDic["times"] = this.Times;

                return paramDic;
            }
            set
            {
                if (value == null)
                {
                    return;
                }

                this.Second = (int)value["second"];
                this.Times = (int)value["times"];
            }
        }
    }

    [Serializable]
    public class UnreasonableHoPoorMainRSRP : CauseBase
    {
        public override string Name
        {
            get { return "切换不合理(主服变差)"; }
        }

        public int Second { get; set; } = 5;
        public int BeforeSecond { get; set; } = 2;
        public int AfterSecond { get; set; } = 2;

        public override string Desc
        {
            get
            {
                return string.Format("切换前后{0}秒，切换后{1}秒的电平均值比前{2}秒均值差"
                    , Second, AfterSecond, BeforeSecond);
            }
        }

        public override string Suggestion
        {
            get { return "核查小区{0}切换参数"; }
        }

        public override void Judge(LowSpeedSeg segItem, List<Model.Event> evts, List<TestPoint> allTP)
        {
            List<Event> hoEvts = new List<Event>();
            foreach (Event evt in evts)
            {
                if (evt.ID == 1039 || evt.ID == 851 || evt.ID == 899
                    || evt.ID == 3138 || evt.ID == 3156 || evt.ID == 3159)
                {
                    hoEvts.Add(evt);
                }
            }

            foreach (Event e in hoEvts)
            {
                List<TestPoint> hoTpts, beforeTpts, afterTpts;
                getTps(allTP, e, out hoTpts, out beforeTpts, out afterTpts);

                float beforeRSRP = 0;
                int beforeNum = 0;
                getTpInfo(beforeTpts, ref beforeRSRP, ref beforeNum);

                float afterRSRP = 0;
                int afterNum = 0;
                getTpInfo(afterTpts, ref afterRSRP, ref afterNum);

                if (beforeNum != 0 && afterNum != 0
                    && beforeRSRP / beforeNum > afterRSRP / afterNum)
                {
                    foreach (TestPoint tp in hoTpts)
                    {
                        UnreasonableHoPoorMainRSRP cln = this.Clone() as UnreasonableHoPoorMainRSRP;
                        segItem.SetReason(new LowSpeedPointDetail(tp, cln));
                    }
                }
            }
        }

        private void getTps(List<TestPoint> allTP, Event e, out List<TestPoint> hoTpts, out List<TestPoint> beforeTpts, out List<TestPoint> afterTpts)
        {
            int bTime = e.Time - Second;
            int eTime = e.Time + Second;
            int beforeTime = e.Time - BeforeSecond;
            int afterTime = e.Time + AfterSecond;
            hoTpts = new List<TestPoint>();
            beforeTpts = new List<TestPoint>();
            afterTpts = new List<TestPoint>();
            foreach (TestPoint tp in allTP)
            {
                if (bTime <= tp.Time && tp.Time <= eTime)
                {
                    hoTpts.Add(tp);
                    if (beforeTime <= tp.Time && tp.Time <= e.Time)
                    {
                        beforeTpts.Add(tp);
                    }
                    else if (e.Time < tp.Time && tp.Time <= afterTime)
                    {
                        afterTpts.Add(tp);
                    }
                }
            }
        }

        private void getTpInfo(List<TestPoint> tps, ref float rsrp, ref int num)
        {
            foreach (TestPoint tp in tps)
            {
                float? crRsrp = (float?)GetRSRP(tp);
                if (crRsrp != null && -141 <= rsrp && rsrp <= 25)
                {
                    num++;
                    rsrp += (float)crRsrp;
                }
            }
        }

        protected object GetRSRP(TestPoint tp)
        {
            if (tp is LTEFddTestPoint)
            {
                return tp["lte_fdd_RSRP"];
            }
            return tp["lte_RSRP"];
        }

        public override Dictionary<string, object> CfgParam
        {
            get
            {
                Dictionary<string, object> paramDic = new Dictionary<string, object>();
                paramDic["TypeName"] = this.GetType().FullName;
                paramDic["lastHoSecondDiff"] = this.Second;
                paramDic["beforeSecond"] = this.BeforeSecond;
                paramDic["afterSecond"] = this.AfterSecond;


                return paramDic;
            }
            set
            {
                if (value == null)
                {
                    return;
                }

                this.Second = (int)value["lastHoSecondDiff"];
                this.BeforeSecond = (int)value["beforeSecond"];
                this.AfterSecond = (int)value["afterSecond"];
            }
        }
    }

    [Serializable]
    public class UnreasonableHoGoodNbRSRP : CauseBase
    {
        public override string Name
        {
            get { return "切换不合理(主服比邻区差)"; }
        }

        public int Second { get; set; } = 5;
        public int AfterSecond { get; set; } = 3;

        public override string Desc
        {
            get
            {
                return string.Format("切换前后{0}秒，切换后{1}秒主服小区电平均值小于邻小区电平均值"
                    , Second, AfterSecond);
            }
        }

        public override string Suggestion
        {
            get { return "核查小区{0}切换参数"; }
        }

        public override void Judge(LowSpeedSeg segItem, List<Model.Event> evts, List<TestPoint> allTP)
        {
            List<Event> hoEvts = new List<Event>();
            foreach (Event evt in evts)
            {
                if (evt.ID == 1039 || evt.ID == 851 || evt.ID == 899
                    || evt.ID == 3138 || evt.ID == 3156 || evt.ID == 3159)
                {
                    hoEvts.Add(evt);
                }
            }

            foreach (Event e in hoEvts)
            {
                int bTime = e.Time - Second;
                int eTime = e.Time + Second;
                int afterTime = e.Time + AfterSecond;
                List<TestPoint> hoTpts = new List<TestPoint>();
                List<TestPoint> afterTpts = getAfterTpts(allTP, e, bTime, eTime, afterTime, hoTpts);

                float mainRSRP = 0;
                float nbRsrp = 0;
                int afterNum = 0;
                getTpInfo(afterTpts, ref mainRSRP, ref nbRsrp, ref afterNum);

                if (afterNum != 0 && mainRSRP / afterNum < nbRsrp / afterNum)
                {
                    foreach (TestPoint tp in hoTpts)
                    {
                        UnreasonableHoGoodNbRSRP cln = this.Clone() as UnreasonableHoGoodNbRSRP;
                        segItem.SetReason(new LowSpeedPointDetail(tp, cln));
                    }
                }
            }
        }

        private static List<TestPoint> getAfterTpts(List<TestPoint> allTP, Event e, int bTime, int eTime, int afterTime, List<TestPoint> hoTpts)
        {
            List<TestPoint> afterTpts = new List<TestPoint>();
            foreach (TestPoint tp in allTP)
            {
                if (bTime <= tp.Time && tp.Time <= eTime)
                {
                    hoTpts.Add(tp);
                    if (e.Time < tp.Time && tp.Time <= afterTime)
                    {
                        afterTpts.Add(tp);
                    }
                }
            }

            return afterTpts;
        }

        private void getTpInfo(List<TestPoint> afterTpts, ref float mainRSRP, ref float nbRsrp, ref int afterNum)
        {
            foreach (TestPoint tp in afterTpts)
            {
                float? rsrp = (float?)GetRSRP(tp);
                if (rsrp != null && -141 <= rsrp && rsrp <= 25)
                {
                    afterNum++;
                    mainRSRP += (float)rsrp;
                }
                for (int i = 0; i < 10; i++)
                {
                    float? nRsrp = (float?)GetNRSRP(tp, i);
                    if (nRsrp != null && -141 <= nRsrp && nRsrp <= 25)
                    {
                        nbRsrp += (float)nRsrp;
                    }
                }
            }
        }

        protected object GetRSRP(TestPoint tp)
        {
            if (tp is LTEFddTestPoint)
            {
                return tp["lte_fdd_RSRP"];
            }
            return tp["lte_RSRP"];
        }
        protected object GetNRSRP(TestPoint tp,int index)
        {
            if (tp is LTEFddTestPoint)
            {
                return tp["lte_fdd_NCell_RSRP", index];
            }
            return tp["lte_NCell_RSRP", index];
        }

        public override Dictionary<string, object> CfgParam
        {
            get
            {
                Dictionary<string, object> paramDic = new Dictionary<string, object>();
                paramDic["TypeName"] = this.GetType().FullName;
                paramDic["lastHoSecondDiff"] = this.Second;
                paramDic["afterSecond"] = this.AfterSecond;

                return paramDic;
            }
            set
            {
                if (value == null)
                {
                    return;
                }

                this.Second = (int)value["lastHoSecondDiff"];
                this.AfterSecond = (int)value["afterSecond"];
            }
        }
    }

    [Serializable]
    public class HoBehindTime : CauseBase
    {
        public override string Name
        {
            get { return "切换不及时"; }
        }
        
        public float MainRSRPMin { get; set; } = -95;
        public int StaySecond { get; set; } = 5;
        public float RSRPDiff { get; set; } = 10;

        public override string Desc
        {
            get
            {
                return string.Format("主服信号＜{0}，邻区比主服电平＞{1}dB，持续{2}秒以上", MainRSRPMin, RSRPDiff, StaySecond);
            }
        }

        public override string Suggestion
        {
            get { return "检查主服小区{0}的切出参数、检查邻区{1}的切入参数"; }
        }

        public override void Judge(LowSpeedSeg segItem, List<Model.Event> evts, List<TestPoint> allTP)
        {
            List<TestPoint> winTps = new List<TestPoint>();
            foreach (TestPoint tp in allTP)
            {
                if (!segItem.IsNeedJudge(tp))
                {
                    setHoBehindTime(ref winTps, segItem);
                    continue;
                }

                float? rsrp = (float?)GetRSRP(tp);
                if (rsrp < MainRSRPMin)
                {
                    bool match = judgeMatch(winTps, tp, rsrp);
                    if (!match)
                    {
                        setHoBehindTime(ref winTps, segItem);
                    }
                }
                else
                {
                    setHoBehindTime(ref winTps, segItem);
                }
            }
            setHoBehindTime(ref winTps, segItem);//避免最后一段
        }

        private bool judgeMatch(List<TestPoint> winTps, TestPoint tp, float? rsrp)
        {
            bool match = false;
            for (int i = 0; i < 10; i++)
            {
                float? nRsrp = (float?)GetNRSRP(tp, i);
                if (nRsrp != null && (float)nRsrp - rsrp > RSRPDiff)
                {
                    match = true;
                    winTps.Add(tp);
                    break;
                }
            }

            return match;
        }

        protected object GetRSRP(TestPoint tp)
        {
            if (tp is LTEFddTestPoint)
            {
                return tp["lte_fdd_RSRP"];
            }
            return tp["lte_RSRP"];
        }
        protected object GetNRSRP(TestPoint tp, int index)
        {
            if (tp is LTEFddTestPoint)
            {
                return tp["lte_fdd_NCell_RSRP", index];
            }
            return tp["lte_NCell_RSRP", index];
        }

        private void setHoBehindTime(ref List<TestPoint> tps, LowSpeedSeg seg)
        {
            if (tps == null || tps.Count == 0)
            {
                return;
            }
            TestPoint beginTp = tps[0];
            TestPoint endTp = tps[tps.Count - 1];
            if (endTp.Time - beginTp.Time > StaySecond)
            {
                foreach (TestPoint tp in tps)
                {
                    HoBehindTime cln = this.Clone() as HoBehindTime;
                    seg.SetReason(new LowSpeedPointDetail(tp, cln));
                }
            }
            tps = new List<TestPoint>();
        }


        public override Dictionary<string, object> CfgParam
        {
            get
            {
                Dictionary<string, object> paramDic = new Dictionary<string, object>();
                paramDic["TypeName"] = this.GetType().FullName;
                paramDic["mainRSRPMin"] = this.MainRSRPMin;
                paramDic["staySecond"] = this.StaySecond;
                paramDic["rsrpDiff"] = this.RSRPDiff;

                return paramDic;
            }
            set
            {
                if (value == null)
                {
                    return;
                }

                this.MainRSRPMin = (float)value["mainRSRPMin"];
                this.StaySecond = (int)value["staySecond"];
                this.RSRPDiff = (float)value["rsrpDiff"];
            }
        }
    }
}
