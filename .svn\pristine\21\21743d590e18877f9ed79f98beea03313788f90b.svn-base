﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System.Drawing;
using MasterCom.MControls;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.Grid
{
    [Serializable()]
    public class GridColorModeItem
    {
        public string Type { get; set; } = "未分类";
        private Color EmptyColor = Color.Empty;
        private static ExpressionEx expParser = new ExpressionEx();
        public bool rangeMode { get; set; } = true;
        public string Name { get; set; }
        public string formula { get; set; }
        public float minR { get; set; }
        public float maxR { get; set; }
        public float minCompareR { get; set; }
        public float maxCompareR { get; set; }
        public List<ColorRange> colorRanges { get; set; } = new List<ColorRange>();//Range着色模式
        public Dictionary<int, Color> colorDic { get; set; } = new Dictionary<int, Color>();//字典着色模式
        public bool visible { get; set; }

        internal OwnFuncCommander ownFuncCommander = null;

        public bool checkVisible { get; set; } = true;

        public List<ColorRange> combinedColorRanges { get; set; } = new List<ColorRange>();//栅格汇聚边框Range着色模式
        public int combineValidCount { get; set; } = 1;  //栅格汇聚格数下限
        
        public void ResetDic()
        {
            colorDic.Clear();
        }
        public override string ToString()
        {
            return Name;
        }
        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                try
                {
                        param["Type"] = Type;
                }
                catch (Exception)
                {
                    param["Type"] ="未分类";
                }
                
                param["Name"] = Name;
                param["MinR"] = minR;
                param["MaxR"] = maxR;
                param["Formula"] = formula;
                List<object> colorParams = new List<object>();
                param["ColorRanges"] = colorParams;
                foreach (ColorRange cr in colorRanges)
                {
                    colorParams.Add(cr.Param);
                }
                param["RangeMode"] = rangeMode;
                param["MinCompareR"] = minCompareR;
                param["MaxCompareR"] = maxCompareR;
                if(ownFuncCommander!=null)
                {
                    param["OwnFuncCommander"] = ownFuncCommander.Param;
                }

                List<object> combinedColorParams = new List<object>();
                param["CombinedColorRanges"] = combinedColorParams;
                foreach (ColorRange cr in combinedColorRanges)
                {
                    combinedColorParams.Add(cr.Param);
                }
                param["CombineValidCount"] = combineValidCount;

                return param;
            }
            set
            {
                try
                {
                Type = (String)value["Type"];
                }
                catch (Exception)
                {
                    Type = "未分类";
                }
                Name = (String)value["Name"];
                minR = (float)value["MinR"];
                maxR = (float)value["MaxR"];
                formula = (String)value["Formula"];
                colorRanges.Clear();
                List<object> colorParams = (List<object>)value["ColorRanges"];
                foreach (object o in colorParams)
                {
                    Dictionary<string, object> colorParam = (Dictionary<string, object>)o;
                    ColorRange cr = new ColorRange();
                    cr.Param = colorParam;
                    colorRanges.Add(cr);
                }
                rangeMode = (bool)value["RangeMode"];
                try{
                    minCompareR = (float)value["MinCompareR"];
                    maxCompareR = (float)value["MaxCompareR"];
                }
                catch
                {
                    //continue
                }
                if(value.ContainsKey("OwnFuncCommander"))
                {
                    Dictionary<string, object> defParam = (Dictionary<string, object>)value["OwnFuncCommander"];
                    OwnFuncCommander commander = new OwnFuncCommander();
                    commander.Param = defParam;
                    ownFuncCommander = commander;
                }
                else
                {
                    ownFuncCommander = null;
                }

                combinedColorRanges.Clear();
                if (value.ContainsKey("CombinedColorRanges"))
                {
                    List<object> combinedColorParams = (List<object>)value["CombinedColorRanges"];
                    foreach (object o in combinedColorParams)
                    {
                        Dictionary<string, object> combinedColorParam = (Dictionary<string, object>)o;
                        ColorRange cr = new ColorRange();
                        cr.Param = combinedColorParam;
                        combinedColorRanges.Add(cr);
                    }
                }
                if (value.ContainsKey("CombineValidCount"))
                {
                    combineValidCount = (int)value["CombineValidCount"];
                }
            }
        }

        private static GridUnit gu = new GridUnit();//for check only
        public static int checkExpression(String formulaOrig)
        {
            if(formulaOrig.IndexOf("Inner_")==0)
            {
                return -100;
            }
            string curFormula = formulaOrig;
            int askPos = curFormula.IndexOf('?');
            if (askPos != -1)//有bool?1:2运算符
            {
                int spltPos = curFormula.IndexOf(':');
                if (spltPos != -1)//bool?1:2运算符完备
                {
                    string vformula1 = curFormula.Substring(askPos+1, spltPos - askPos-1);
                    curFormula = vformula1;
                }
            }
            try
            {
                if (curFormula.Trim().Length==0)
                {
                    return 0;
                }
                StringBuilder sb = new StringBuilder();
                int len = curFormula.Length;
                StringBuilder tokenSB = new StringBuilder();
                bool tokenStarted = false;
                StringBuilder arg = new StringBuilder();
                bool argStarted = false;
                int i = 0;
                while (i < len)
                {
                    char ch = curFormula[i];
                    if (argStarted)
                    {
                        if (ch >= '0' && ch <= '9')
                        {
                            arg.Append(ch);
                            i++;
                            continue;
                        }
                        if (ch == ']')
                        {
                            if (gu.HasValidField(tokenSB.ToString(), arg.ToString()))
                            {
                                i++;
                                tokenSB.Remove(0, tokenSB.Length);//clear it
                                tokenStarted = false;
                                sb.Append("(1)");
                                arg = new StringBuilder();//clear it
                                argStarted = false;
                                continue;
                            }
                            else
                            {
                                int effected = tokenSB.Length + arg.Length + 1;
                                return i - effected;
                            }
                        }

                        return i - arg.Length - 1;
                    }
                    if (ch == '(' )
                    {
                        if (tokenStarted)
                        {
                            int effected = tokenSB.Length + 1;
                            return i - effected;
                        }
                        sb.Append(ch);
                        i++;
                        continue;
                    }else if(ch == ')'){
                        if(tokenStarted)
                        {
                            if (gu.HasValidField(tokenSB.ToString(), ""))
                            {
                                i++;
                                tokenSB.Remove(0, tokenSB.Length);
                                sb.Append("(1)");
                                tokenStarted = false;
                                sb.Append(ch);
                                continue;
                            }
                            else
                            {
                                int effected = tokenSB.Length + 1;
                                return i - effected;
                            }
                        }
                        else
                        {
                            sb.Append(ch);
                            i++;
                            continue;
                        }
                    }

                    if (ch == '+' || ch == '-' || ch == '*' || ch == '/'||ch=='>'||ch=='<')
                    {
                        if (tokenStarted)
                        {
                            if (gu.HasValidField(tokenSB.ToString(), ""))
                            {
                                i++;
                                tokenSB.Remove(0, tokenSB.Length);
                                sb.Append("(1)");
                                tokenStarted = false;
                                sb.Append(ch);
                                continue;
                            }
                            else
                            {
                                int effected = tokenSB.Length + 1;
                                return i - effected;
                            }
                        }
                        else
                        {
                            sb.Append(ch);
                            i++;
                            continue;
                        }
                    }
                    if (ch == '[')
                    {
                        if (tokenStarted)
                        {
                            argStarted = true;
                            i++;
                            continue;
                        }
                        else
                        {
                            return i;
                        }
                    }
                    if (tokenStarted)
                    {
                        if (ch >= '0' && ch <= '9' || ch >= 'a' && ch <= 'z' || ch >= 'A' && ch <= 'Z'||ch=='_')
                        {
                            tokenSB.Append(ch);
                            i++;
                        }
                        else if (ch == ' ')
                        {
                            if (gu.HasValidField(tokenSB.ToString(), ""))
                            {
                                i++;
                                tokenSB.Remove(0, tokenSB.Length);
                                sb.Append("(1)");
                                tokenStarted = false;
                                sb.Append(ch);
                            }
                            else
                            {
                                int effected = tokenSB.Length + 1;
                                return i - effected;
                            }
                        }
                        else
                        {
                            return i;
                        }
                    }
                    else
                    {
                        if (ch >= 'a' && ch <= 'z' || ch >= 'A' && ch <= 'Z')
                        {
                            tokenStarted = true;
                            tokenSB.Append(ch);
                            i++;
                        }
                        else
                        {
                            sb.Append(ch);
                            i++;
                        }
                    }
                }
                if (tokenStarted)
                {
                    if (argStarted)
                    {
                        int effected = arg.Length + 1;
                        return curFormula.Length - effected;
                    }
                    if (gu.HasValidField(tokenSB.ToString(), ""))
                    {
                        tokenSB.Remove(0, tokenSB.Length);
                        sb.Append("(1)");
                    }
                    else
                    {
                        return curFormula.Length - 1;
                    }
                }
                string expv = sb.ToString();
                expv = expv.Replace(" ", "");
                String retstr = expParser.ParseCommand(expv);

                float fValue;
                if (float.TryParse(retstr, out fValue))
                {
                    return -100;
                }
                else
                {
                    return 0;
                }
            }
            catch
            {
                return 0;
            }
        }

        public float CalcValue(string formula, GridUnit data)
        {

            int askPos = formula.IndexOf('?');
            if (askPos != -1)//有bool?1:2运算符
            {
                int spltPos = formula.IndexOf(':');
                if (spltPos != -1)//bool?1:2运算符完备
                {
                    string askstrformula = formula.Substring(0, askPos);
                    float vAskRet = CalcValue(askstrformula, data);
                    if (vAskRet == 1)
                    {
                        string part1str = formula.Substring(askPos + 1, spltPos - askPos - 1);
                        return CalcValue(part1str, data);
                    }
                    else//part 2
                    {
                        string part2str = formula.Substring(spltPos + 1, formula.Length - spltPos - 1);
                        return CalcValue(part2str, data);
                    }
                }
            }

            StringBuilder sb = new StringBuilder();
            int len = formula.Length;
            StringBuilder tokenSB = new StringBuilder();
            bool tokenStarted = false;
            StringBuilder arg = new StringBuilder();
            bool argStarted = false;
            int i = 0;
            while (i < len)
            {
                char ch = formula[i];
                if (argStarted)
                {
                    if (ch >= '0' && ch <= '9')
                    {
                        arg.Append(ch);
                        i++;
                        continue;
                    }
                    if (ch == ']')
                    {
                        object v = data.GetFieldValue(tokenSB.ToString(), arg.ToString());
                        i++;
                        tokenSB.Remove(0, tokenSB.Length);//clear it
                        tokenStarted = false;
                        sb.Append("(" + v + ")");
                        arg = new StringBuilder();//clear it
                        argStarted = false;
                        continue;

                    }

                    throw new InvalidOperationException("公式有误");
                }
                if (ch == '(')
                {
                    if (tokenStarted)
                    {
                        int effected = tokenSB.Length + 1;
                        return i - effected;
                    }
                    sb.Append(ch);
                    i++;
                    continue;
                }
                else if (ch == ')')
                {
                    if (tokenStarted)
                    {
                        object v = data.GetFieldValue(tokenSB.ToString(), arg.ToString());
                        i++;
                        tokenSB.Remove(0, tokenSB.Length);
                        sb.Append("(" + v + ")");
                        tokenStarted = false;
                        sb.Append(ch);
                        continue;
                    }
                    else
                    {
                        sb.Append(ch);
                        i++;
                        continue;
                    }
                }
                if (ch == '+' || ch == '-' || ch == '*' || ch == '/' || ch == '>' || ch == '<')
                {
                    if (tokenStarted)
                    {
                        object v = data.GetFieldValue(tokenSB.ToString(), arg.ToString());
                        i++;
                        tokenSB.Remove(0, tokenSB.Length);
                        sb.Append("(" + v + ")");
                        tokenStarted = false;
                        sb.Append(ch);
                        continue;

                    }
                    else
                    {
                        sb.Append(ch);
                        i++;
                        continue;
                    }
                }
                if (ch == '[')
                {
                    if (tokenStarted)
                    {
                        argStarted = true;
                        i++;
                        continue;
                    }
                    else
                    {
                        throw new InvalidOperationException("公式有误");
                    }
                }
                if (tokenStarted)
                {
                    if (ch >= '0' && ch <= '9' || ch >= 'a' && ch <= 'z' || ch >= 'A' && ch <= 'Z' || ch == '_')
                    {
                        tokenSB.Append(ch);
                        i++;
                    }
                    else if (ch == ' ')
                    {
                        object v = data.GetFieldValue(tokenSB.ToString(), arg.ToString());
                        i++;
                        tokenSB.Remove(0, tokenSB.Length);
                        sb.Append("(" + v + ")");
                        tokenStarted = false;
                        sb.Append(ch);

                    }
                    else
                    {
                        throw new InvalidOperationException("公式有误");
                    }
                }
                else
                {
                    if (ch >= 'a' && ch <= 'z' || ch >= 'A' && ch <= 'Z')
                    {
                        tokenStarted = true;
                        tokenSB.Append(ch);
                        i++;
                    }
                    else
                    {
                        sb.Append(ch);
                        i++;
                    }
                }
            }
            if (tokenStarted)
            {
                if (argStarted)
                {
                    int effected = arg.Length + 1;
                    return formula.Length - effected;
                }
                object v = data.GetFieldValue(tokenSB.ToString(), arg.ToString());
                tokenSB.Remove(0, tokenSB.Length);
                sb.Append("(" + v + ")");
            }
            string expv = sb.ToString();
            expv = expv.Replace(" ", "");
            if (expv.Contains("+(False") || expv.Contains("False)+") || expv.Contains("-(False") || expv.Contains("False)-") || expv.Contains("*(False") ||
                expv.Contains("False)*") || expv.Contains("/(False") || expv.Contains("False)/") || expv.Contains("False"))
            {
                expv = expv.Replace("False", "0");
            }
            String retstr = expParser.ParseCommand(expv);
            float retvf = float.Parse(retstr);
            return retvf;
        }
        internal void CalcColor(ColorUnit cu)
        {
            try
            {
                if(ownFuncCommander==null)
                {
                    float retf = (float)cu.DataHub.CalcValueByFormula(formula);
                    cu.retv = retf;
                    if(!float.IsNaN(retf))
                    {
                        if(this.rangeMode)
                        {
                            cu.color = GetColor(retf);
                        }
                        else
                        {
                            cu.color = GetDicColor(retf);
                        }
                    }
                }
                else
                {
                    if (!ownFuncCommander._classReady && !ownFuncCommander._hasError)
                    {
                        ownFuncCommander.initFuncClass_GridUnit();
                    }
                    if (ownFuncCommander._classReady)
                    {
                        object[] paramObj = new object[2];
                        paramObj[0] = this;
                        paramObj[1] = cu;
                        object objClass = ownFuncCommander.clzzInst;
                        float fResult = (float)objClass.GetType().InvokeMember(
                                           "GetFuncResult",
                                           System.Reflection.BindingFlags.InvokeMethod, null, objClass,
                                           paramObj);
                        cu.color = GetColor(fResult);
                    }
                }
            }
            catch
            {
                //cu.Status = 0;//忽略其显示
                cu.color = Color.Black;
            }
        }
        
        public Color GetDicColor(float value)
        {
            int intv = (int)value;
            Color retcolor;
            if(colorDic.TryGetValue(intv, out retcolor))
            {
                return retcolor;
            }
            else{
                retcolor = ColorSequenceSupplier.getColor(colorDic.Count);
                colorDic[intv] = retcolor;
                return retcolor;
            }
        }
        public  ColorRange GetColorRange(float value)
        {
            int count = colorRanges.Count;
            if (count == 0)
            {
                return null;
            }
            for (int i = 0; i < count; i++)
            {
                ColorRange cr = colorRanges[i];
                if (value >= cr.minValue && value < cr.maxValue)
                {
                    return cr;
                }
            }
            if (value == colorRanges[count - 1].maxValue)
            {
                return colorRanges[count - 1];
            }
            return null;
        }
        public Color GetColor(float value)
        {
            int count = colorRanges.Count;
            if(count==0){
                return EmptyColor;
            }
            for (int i = 0; i < count; i++)
            {
                ColorRange cr = colorRanges[i];
                if (value >= cr.minValue && value < cr.maxValue)
                {
                    if (cr.visible)
                    {
                        return cr.color;
                    }
                    else
                    {
                        return EmptyColor;
                    }
                  
                }
            }
            if(value==colorRanges[count-1].maxValue)
            {
                return colorRanges[count - 1].color;
            }
            return EmptyColor;
        }

        internal void CalcCombinedColorRange(ColorUnit cu)
        {
            try
            {
                if (ownFuncCommander == null)
                {
                    float retf = (float)cu.DataHub.CalcValueByFormula(formula);
                    cu.retv = retf;
                    cu.combineColorRange = GetCombinedColorRange(retf);
                }
                else
                {
                    if (!ownFuncCommander._classReady && !ownFuncCommander._hasError)
                    {
                        ownFuncCommander.initFuncClass_GridUnit();
                    }
                    if (ownFuncCommander._classReady)
                    {
                        object[] paramObj = new object[2];
                        paramObj[0] = this;
                        paramObj[1] = cu;
                        object objClass = ownFuncCommander.clzzInst;
                        float fResult = (float)objClass.GetType().InvokeMember(
                                           "GetFuncResult",
                                           System.Reflection.BindingFlags.InvokeMethod, null, objClass,
                                           paramObj);
                        cu.combineColorRange = GetCombinedColorRange(fResult);
                    }
                }
            }
            catch
            {
                cu.Status = 0;//忽略其显示
                cu.combineColorRange = null;
            }
        }
        public ColorRange GetCombinedColorRange(float value)
        {
            int count = combinedColorRanges.Count;
            if (count == 0)
            {
                return null;
            }
            for (int i = 0; i < count; i++)
            {
                ColorRange cr = combinedColorRanges[i];
                if (value >= cr.minValue && value < cr.maxValue)
                {
                    return cr;
                }
            }
            if (value == combinedColorRanges[count - 1].maxValue)
            {
                return combinedColorRanges[count - 1];
            }
            return null;
        }
    }

    public class FieldTag
    {
        public String field { get; set; }
        public int arg { get; set; }
        public FieldTag(string field)
        {
            this.field = field;
            this.arg = -1;
        }
        public FieldTag(string field,int arg)
        {
            this.field = field;
            this.arg = arg;
        }
    }

    public class GridColorFixed
    {
        public string theme { get; set; }
        public List<GridColorFixedItem> items { get; set; }
    }
    public class GridColorFixedItem
    {
        public string desc { get; set; }
        public Color color { get; set; }

        public GridColorFixedItem()
        {

        }
        public GridColorFixedItem(Color color, string desc)
        {
            this.color = color;
            this.desc = desc;
        }
    }
}
