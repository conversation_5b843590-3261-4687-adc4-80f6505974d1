using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Stat;
using MasterCom.RAMS.Stat.Data;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model.Interface;
using DBDataViewer;
using System.Windows.Forms;
using MasterCom.MTGis;
using MasterCom.RAMS.ZTFunc;
using MasterCom.RAMS.Grid;
using MasterCom.RAMS.KPI_Statistics;

namespace MasterCom.RAMS.Net
{
    public class ZTNBCellMissByRegion : DIYQueryCoverScanGridByRegion
    {
        protected Dictionary<GridItem, Dictionary<string, Dictionary<int, GSMCellRxLev>>> cellGridDic = new Dictionary<GridItem, Dictionary<string, Dictionary<int, GSMCellRxLev>>>();

        public ZTNBCellMissByRegion(MainModel mainModel)
            : base(mainModel)
        {
        }
        public override string Name
        {
            get { return "邻区配置核查"; }
        }
        public override string IconName
        {
            get { return "Images/nbcell_miss.gif"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 15000, 15015, this.Name);
        }

        protected override bool getConditionBeforeQuery()
        {
            cellGridDic.Clear();
            CellManager.GetInstance().GetGSMNBCellInfo();
            return true;
        }

        protected override void fireShowResult()
        {
            ZTNBCellMissForm frm = MainModel.CreateResultForm(typeof(ZTNBCellMissForm)) as ZTNBCellMissForm;
            frm.FillDatas(cellGridDic, InspectType.GSM);
            frm.Visible = true;
            frm.BringToFront();
        }

        protected override void afterRecieveAllData(params object[] reservedParams)
        {
            getGridCells();
        }

        protected override string getStatImgNeededTriadID(params object[] paramSet)
        {
            List<string> formulaSet = new List<string>();
            formulaSet.Add(DataScan_GSM.RxlevMeanValueID);
            formulaSet.Add(DataScan_GSM.RxlevMaxID);
            formulaSet.Add(DataScan_GSM.RxlevSampleCountID);
            return getTriadIDIgnoreServiceType(formulaSet);
        }

        protected virtual void getGridCells()
        {
            foreach (GridDataUnit grid in CurScanGridUnitMatrix)
            {
                StatDataSCAN_GSM dataScam_GSM = grid.GetStatData(typeof(StatDataSCAN_GSM)) as StatDataSCAN_GSM;
                if (dataScam_GSM != null)
                {
                    GridItem gi = new GridItem(grid.LTLng, grid.LTLat);
                    foreach (int cellID in dataScam_GSM.CellInicatorDic.Keys)
                    {
                        setCellGridInfo(dataScam_GSM, gi, cellID);
                    }
                }
            }
        }

        private void setCellGridInfo(StatDataSCAN_GSM dataScam_GSM, GridItem gi, int cellID)
        {
            if (cellID != 0)
            {
                if (!cellGridDic.ContainsKey(gi))
                {
                    Dictionary<string, Dictionary<int, GSMCellRxLev>> serviceCellsInfo = new Dictionary<string, Dictionary<int, GSMCellRxLev>>();
                    serviceCellsInfo["GSM"] = new Dictionary<int, GSMCellRxLev>();
                    cellGridDic[gi] = serviceCellsInfo;
                }
                if (!cellGridDic[gi]["GSM"].ContainsKey(cellID))
                {
                    cellGridDic[gi]["GSM"][cellID] = new GSMCellRxLev();
                }
                cellGridDic[gi]["GSM"][cellID].cellID = cellID;

                calculateSampleRxlev(dataScam_GSM, gi, cellID);
            }
        }

        private void calculateSampleRxlev(StatDataSCAN_GSM dataScam_GSM, GridItem gi, int cellID)
        {
            /**
            cellGridDic[gi]["GSM"][cellID].rxlevAvg = dataScam_GSM[cellID, DataScan_GSM.RxlevMeanValueID];
            cellGridDic[gi]["GSM"][cellID].rxlevMax = dataScam_GSM[cellID, DataScan_GSM.RxlevMaxID];
            double num = dataScam_GSM[cellID, DataScan_GSM.RxlevSampleCountID];
            if (!double.IsNaN(num))
            {
                cellGridDic[gi]["GSM"][cellID].sampleNum = (int)num;
            }
            */

            //由于栅格程序中找不到对应5F0C0104系列的指标,故暂时改为从900,1800频点指标获取
            double sampleNum900 = dataScam_GSM[cellID, "5F0C070101"];
            double sampleNum1800 = dataScam_GSM[cellID, "5F0C070201"];
            if (!double.IsNaN(sampleNum900))
            {
                cellGridDic[gi]["GSM"][cellID].rxlevAvg = dataScam_GSM[cellID, "5F0C070102"];
                cellGridDic[gi]["GSM"][cellID].sampleNum = (int)sampleNum900;
            }
            else if (!double.IsNaN(sampleNum1800))
            {
                cellGridDic[gi]["GSM"][cellID].rxlevAvg = dataScam_GSM[cellID, "5F0C070202"];
                cellGridDic[gi]["GSM"][cellID].sampleNum = (int)sampleNum1800;
            }
        }

        protected void dealDataSCAN(GridDataUnit grid, ScanKPIData dataScam, string param, string avgParam, string maxParam, string numParam)
        {
            if (dataScam != null)
            {
                GridItem gi = new GridItem(grid.LTLng, grid.LTLat);
                foreach (int cellID in dataScam.CellInicatorDic.Keys)
                {
                    if (cellID == 0)
                    {
                        continue;
                    }
                    setCellInfo(gi, cellID, param);
                    cellGridDic[gi][param][cellID].rxlevAvg = dataScam[cellID, avgParam];
                    cellGridDic[gi][param][cellID].rxlevMax = dataScam[cellID, maxParam];
                    double num = dataScam[cellID, numParam];
                    if (!double.IsNaN(num))
                    {
                        cellGridDic[gi][param][cellID].sampleNum = (int)num;
                    }
                }
            }
        }

        protected void setCellInfo(GridItem gi, int cellID, string param)
        {
            if (!cellGridDic.ContainsKey(gi))
            {
                Dictionary<string, Dictionary<int, GSMCellRxLev>> serviceCellsInfo = new Dictionary<string, Dictionary<int, GSMCellRxLev>>();
                serviceCellsInfo[param] = new Dictionary<int, GSMCellRxLev>();
                cellGridDic[gi] = serviceCellsInfo;
            }
            if (!cellGridDic[gi].ContainsKey(param))
            {
                cellGridDic[gi][param] = new Dictionary<int, GSMCellRxLev>();
            }
            if (!cellGridDic[gi][param].ContainsKey(cellID))
            {
                cellGridDic[gi][param][cellID] = new GSMCellRxLev();
            }
            cellGridDic[gi][param][cellID].cellID = cellID;
        }
    }

    /// <summary>
    /// 小区过滤类型
    /// </summary>
    public enum CellBandTypeFilter
    {
        All,
        GSM900_GSM900,
        GSM900_DSC1800,
        DSC1800_DSC1800,
        DSC1800_GSM900,
        TD2TD,
        TD2GSM,
        W2W,
        LTE2GSM
    }

    /// <summary>
    /// 邻区核查类型
    /// </summary>
    public enum InspectType
    {
        GSM,
        T2T,
        T2G,
        W2W,
        LTE2GSM,
        LTE2TD,
        LTE2LTE,
        NR2LTE,
    }

    public class GSMCellRxLev
    {
        public int cellID { get; set; } = 0;
        public int sampleNum { get; set; } = 0;
        public double rxlevAvg { get; set; } = 0;
        public double rxlevMax { get; set; } = -999;
        public List<GridItem> grids { get; set; }  //同频干扰用，栅格合并后小区涉及到栅格
        public Dictionary<GridItem, GSMCellRxLev> gridCellDic { get; set; }

        public static IComparer<GSMCellRxLev> GetCompareByRxLevAvg()
        {
            if (compareByRxLevAvg == null)
            {
                compareByRxLevAvg = new CompareByRxLevAvg();
            }
            return compareByRxLevAvg;
        }
        private static IComparer<GSMCellRxLev> compareByRxLevAvg;
        private class CompareByRxLevAvg : IComparer<GSMCellRxLev>
        {
            public int Compare(GSMCellRxLev x, GSMCellRxLev y)
            {
                return y.rxlevAvg.CompareTo(x.rxlevAvg);
            }
        }
    }

    public class GridItem:GridUnitBase
    {
        public GridItem()
        {

        }

        public GridItem(double ltLng, double latLat)
        {
            this.LTLng = ltLng;
            this.LTLat = latLat;
        }

        public override bool Equals(object obj)
        {
            GridItem other = obj as GridItem;

            if (other == null)
                return false;

            if (!base.GetType().Equals(obj.GetType()))
                return false;

            return (this.CenterLng == other.CenterLng);
        }

        public override int GetHashCode()
        {
            return (this.RowIdx.ToString() + "_" + this.ColIdx.ToString()).GetHashCode();
        }
    }
}
