﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Drawing;
using MasterCom.MControls;

namespace MasterCom.Util
{
    public static class ColorSequenceSupplier
    {
        public static Color[] paintSequenceColors { get; set; } = createPaintColors();
        private static Color[] createPaintColors(){
            return new Color[]{
                 Color.Blue,
                 Color.Yellow,
                 Color.Green,
                 Color.Cyan,
                 Color.Maroon,
                 Color.Olive,
                 Color.Firebrick,
                 Color.SkyBlue,
              
                 Color.Black,
                 Color.BlueViolet,
                 Color.Brown,

                 Color.CadetBlue,
                 Color.Chartreuse,
                 Color.Chocolate,

                 Color.CornflowerBlue,

                 Color.Crimson,
                 
                 Color.Aquamarine,

                 Color.DarkBlue,
                 Color.DarkCyan,
                 Color.DarkGoldenrod,

                 Color.DarkGreen,

                 Color.DarkMagenta,
                 Color.DarkOliveGreen,
                 Color.DarkOrange,
                 Color.DarkOrchid,
                 Color.DarkRed,

                 Color.DarkSeaGreen,
                 Color.DarkSlateBlue,
                 Color.DarkSlateGray,
                 Color.DarkTurquoise,
                 Color.DarkViolet,
                 Color.DeepPink,

                 Color.DeepSkyBlue,

                 Color.DodgerBlue,
                 

                 Color.ForestGreen,
                 Color.Fuchsia,
                 
                 Color.Gold,
                 Color.Goldenrod,
                 
                 Color.GreenYellow,

                 Color.HotPink,
                 Color.IndianRed,
                 Color.Indigo,

                 Color.LawnGreen,

                 Color.LightBlue,
                 Color.LightCoral,

                 Color.LightGreen,
                 Color.LightPink,
                 Color.LightSalmon,
                 Color.LightSeaGreen,
                 Color.LightSkyBlue,
                 Color.LightSlateGray,
                 Color.LightSteelBlue,

                 Color.Lime,
                 Color.LimeGreen,

                 Color.Magenta,

                 Color.MediumAquamarine,
                 Color.MediumBlue,
                 Color.MediumOrchid,
                 Color.MediumPurple,
                 Color.MediumSeaGreen,
                 Color.MediumSlateBlue,
                 Color.MediumSpringGreen,
                 Color.MediumTurquoise,
                 Color.MediumVioletRed,
                 Color.MidnightBlue,

                 Color.Navy,
                 
                 Color.OliveDrab,
                 Color.Orange,
                 Color.OrangeRed,
                 Color.Orchid,

                 Color.PaleGreen,
                 Color.PaleTurquoise,
                 Color.PaleVioletRed,

                 Color.Peru,
                 Color.Plum,
                 Color.PowderBlue,
                 Color.Purple,
                 Color.Red,
                 Color.RosyBrown,
                 Color.RoyalBlue,
                 Color.SaddleBrown,
                 Color.Salmon,
                 Color.SandyBrown,
                 Color.SeaGreen,

                 Color.Sienna,

                 
                 Color.SlateBlue,
                 Color.SlateGray,
                 
                 Color.SpringGreen,
                 Color.SteelBlue,
                 Color.Tan,
                 Color.Teal,

                 Color.Tomato,
                 //Color.Transparent,
                 Color.Turquoise,
                 Color.Violet,

                 
                 Color.YellowGreen
            };
        }
        public static Color[] paintCellCovColors { get; set; } = createCellCovColors();
        private static Color[] createCellCovColors()
        {
            return new Color[]{
                 Color.FromArgb(0,0,219),
                 Color.FromArgb(255,0,128),
                 Color.FromArgb(20,226,206),
                 Color.FromArgb(216,206,27),
                 Color.LightCoral,
                 Color.Olive,
                 Color.YellowGreen
            };
        }
        public static Color getCellCovColor(int idx)
        {
            return paintCellCovColors[idx % paintCellCovColors.Length];
        }
        public static Color getColor(int idx)
        {
            return paintSequenceColors[idx % paintSequenceColors.Length];
        }

        private static Color[] vividColor = new Color[] { Color.Red, Color.Blue, Color.Purple, Color.Lime, Color.Orange };

        public static Color getVividColor(int idx)
        {
            return vividColor[idx % vividColor.Length];
        }

        public static List<ColorRange> makeColorRangeList(float min, float max, int intervals, Color fromColor,bool useVia, Color viaColor, Color toColor)
        {
            List<ColorRange> rglist = new List<ColorRange>();

            GradientDrawingSupplier gds = new GradientDrawingSupplier(intervals);
            gds.addColorSample(fromColor);
            if (useVia)
            {
                gds.addColorSample(viaColor);
            }
            gds.addColorSample(toColor);
            float step = (max - min) / intervals;
            for (int i = 0; i < intervals; i++)
            {
                float lower = min + step * i;
                float higher = min + step * (i + 1);
                Color color = gds.getColor(i);
                rglist.Add(new ColorRange(lower, higher, color));
            }
            return rglist;
        }
    }
    public class GradientDrawingSupplier
    {
        private int colorCount;//总共要生成颜色数量（决定了取样粒度）
        private readonly List<Color> colorSamples = new List<Color>();

        public GradientDrawingSupplier()
        {

        }

        public GradientDrawingSupplier(int colorcount)
        {
            this.colorCount = colorcount;
        }

        public void addColorSample(Color color)
        {
            colorSamples.Add(color);
        }
        public Color getColor(int i){
		    if(i>=colorCount){
			    return Color.Empty;
		    }
		    if(colorSamples.Count<2){//没有指定颜色采样端点，自动生成默认的
			    colorSamples.Clear();
			    colorSamples.Add(Color.Green);
			    colorSamples.Add(Color.Red);
		    }
		    return makeColor(i);
	    }
        private Color makeColor(int i)
        {

            int segCount = colorSamples.Count - 1;//共有多少个段
            float posPref = (float)segCount * i / colorCount;
            int posSeg = segCount * i / colorCount;

            int posEnd = posSeg + 1;
            if (posEnd > segCount)
                posEnd = segCount;
            Color start = colorSamples[posSeg];
            Color end = colorSamples[posEnd];

            float percentInSeg = posPref - posSeg;
            return makeColor(start, end, percentInSeg);
        }
        private Color makeColor(Color start, Color end, float percent)
        {
            int sR = start.R;
            int sG = start.G;
            int sB = start.B;

            int eR = end.R;
            int eG = end.G;
            int eB = end.B;

            int r = (int)(sR + percent * (eR - sR));
            int g = (int)(sG + percent * (eG - sG));
            int b = (int)(sB + percent * (eB - sB));
            return Color.FromArgb(r, g, b);
        }

        public int getColorCount()
        {
            return colorCount;
        }

        public void setColorCount(int colorCount)
        {
            this.colorCount = colorCount;
        }
    }
}
