﻿using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public abstract class ZTHightRSRPLowSINRBaseQuery<T> : DIYAnalyseByFileBackgroundBase where T : new()
    {
        protected abstract string themeName { get; }
        protected abstract string rsrpName { get; }
        protected abstract string sinrName { get; }

        protected PercentRoadBuilder roadBuilder = null;
        public HighRSRPLowSINRCondition WeakCond { get; set; } = new HighRSRPLowSINRCondition();
        protected List<HighRSRPLowSINR> resultList = new List<HighRSRPLowSINR>();

        private static T instance { get; set; }
        public static T GetInstance()
        {
            if (instance == null)
            {
                instance = new T();
            }
            return instance;
        }

        protected ZTHightRSRPLowSINRBaseQuery()
            : base(MainModel.GetInstance())
        {
            ServiceTypes.Clear();
        }

        public override string Name
        {
            get { return "强信号弱质量"; }
        }

        public void setRoadCond()
        {
            PercentRoadCondition roadCond = new PercentRoadCondition(WeakCond.Percentage / 100, OnOneRoadComplete);
            roadCond.IsCheckDuration = WeakCond.CheckTime;
            roadCond.IsCheckMinLength = WeakCond.CheckDistance;
            roadCond.IsCheckDistanceGap = WeakCond.Check2TPDistance;
            roadCond.MaxDistanceGap = WeakCond.Max2TPDistance;
            roadCond.MinDuration = WeakCond.MinStaySecond;
            roadCond.MinLength = WeakCond.MinStayDistance;
            this.roadBuilder = new PercentRoadBuilder(roadCond);
        }

        private void OnOneRoadComplete(object sender, PercentRoadItem roadItem)
        {
            double percent = Math.Round(roadItem.ValidPercent * 100, 2);
            if (percent < this.WeakCond.Percentage)
            {
                return;
            }
            double distance = roadItem.Length;

            if (!WeakCond.CheckStayDistance(distance)
                || !WeakCond.CheckStayTime(roadItem.Duration))
            {//不符合 最小持续距离 or 时间
                return;
            }
            addLowSinrInfo(roadItem.TestPoints, percent);
        }

        protected virtual void addLowSinrInfo(List<TestPoint> tps, double percent)
        {
            HighRSRPLowSINR info = new HighRSRPLowSINR();
            info.SN = resultList.Count + 1;
            info.Percent = percent;
            double dis = 0;
            TestPoint lastTp = null;
            foreach (TestPoint tp in tps)
            {
                if (lastTp != null)
                {
                    dis = tp.Distance2(lastTp);
                }
                float? sinr = getSinr(tp);
                float? rsrp = getRsrp(tp);
                info.AddTestPoint(tp, rsrp, sinr, dis);
                lastTp = tp;
            }
            info.FindRoadName();
            resultList.Add(info);
        }

        protected override void fireShowForm()
        {
            MainModel.FireSetDefaultMapSerialTheme(themeName);
            HighRSRPLowSINRForm frm = mainModel.CreateResultForm(typeof(HighRSRPLowSINRForm)) as HighRSRPLowSINRForm;
            frm.FillData(resultList);
            frm.Visible = true;
            frm.BringToFront();
        }

        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                setRoadCond();
                return true;
            }
            HighRSRPLowSINRSettingDlg dlg = new HighRSRPLowSINRSettingDlg(WeakCond);
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                WeakCond = dlg.GetCondition();
                setRoadCond();
                return true;
            }
            return false;
        }

        protected override void clearDataBeforeAnalyseFiles()
        {
            resultList.Clear();
        }

        protected virtual float? getRsrp(TestPoint tp)
        {
            return (float?)tp[rsrpName];
        }

        protected virtual float? getSinr(TestPoint tp)
        {
            return (float?)tp[sinrName];
        }

        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager fileDataManager in MainModel.DTDataManager.FileDataManagers)
            {
                foreach (TestPoint testPoint in fileDataManager.TestPoints)
                {
                    bool inRegion = isTPInRegion(testPoint);
                    if (inRegion)
                    {
                        float? sinr = getSinr(testPoint);
                        float? rsrp = getRsrp(testPoint);
                        if (rsrp >= WeakCond.MinRsrp)
                        {
                            roadBuilder.AddPoint(testPoint, isValidTestPoint(testPoint) && WeakCond.IsMatchIndicator(sinr));
                        }
                    }
                }
                roadBuilder.StopRoading();
            }
        }

        protected virtual bool isTPInRegion(TestPoint tp)
        {
            if (condition.Geometorys.GeoOp.Contains(tp.Longitude, tp.Latitude))
            {
                return true;
            }
            return false;
        }
    }
}
