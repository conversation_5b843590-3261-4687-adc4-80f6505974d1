<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="toolTip.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <metadata name="ColVisible.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="ColName.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="ColLabel.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="ColNote.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="imageList.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>110, 17</value>
  </metadata>
  <data name="imageList.ImageStream" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>
        AAEAAAD/////AQAAAAAAAAAMAgAAAFdTeXN0ZW0uV2luZG93cy5Gb3JtcywgVmVyc2lvbj0yLjAuMC4w
        LCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWI3N2E1YzU2MTkzNGUwODkFAQAAACZTeXN0
        ZW0uV2luZG93cy5Gb3Jtcy5JbWFnZUxpc3RTdHJlYW1lcgEAAAAERGF0YQcCAgAAAAkDAAAADwMAAAD6
        CQAAAk1TRnQBSQFMAgEBAgEAAfgBAAH4AQABEAEAARABAAT/AQkBAAj/AUIBTQE2AQQGAAE2AQQCAAEo
        AwABQAMAARADAAEBAQABCAYAAQQYAAGAAgABgAMAAoABAAGAAwABgAEAAYABAAKAAgADwAEAAcAB3AHA
        AQAB8AHKAaYBAAEzBQABMwEAATMBAAEzAQACMwIAAxYBAAMcAQADIgEAAykBAANVAQADTQEAA0IBAAM5
        AQABgAF8Af8BAAJQAf8BAAGTAQAB1gEAAf8B7AHMAQABxgHWAe8BAAHWAucBAAGQAakBrQIAAf8BMwMA
        AWYDAAGZAwABzAIAATMDAAIzAgABMwFmAgABMwGZAgABMwHMAgABMwH/AgABZgMAAWYBMwIAAmYCAAFm
        AZkCAAFmAcwCAAFmAf8CAAGZAwABmQEzAgABmQFmAgACmQIAAZkBzAIAAZkB/wIAAcwDAAHMATMCAAHM
        AWYCAAHMAZkCAALMAgABzAH/AgAB/wFmAgAB/wGZAgAB/wHMAQABMwH/AgAB/wEAATMBAAEzAQABZgEA
        ATMBAAGZAQABMwEAAcwBAAEzAQAB/wEAAf8BMwIAAzMBAAIzAWYBAAIzAZkBAAIzAcwBAAIzAf8BAAEz
        AWYCAAEzAWYBMwEAATMCZgEAATMBZgGZAQABMwFmAcwBAAEzAWYB/wEAATMBmQIAATMBmQEzAQABMwGZ
        AWYBAAEzApkBAAEzAZkBzAEAATMBmQH/AQABMwHMAgABMwHMATMBAAEzAcwBZgEAATMBzAGZAQABMwLM
        AQABMwHMAf8BAAEzAf8BMwEAATMB/wFmAQABMwH/AZkBAAEzAf8BzAEAATMC/wEAAWYDAAFmAQABMwEA
        AWYBAAFmAQABZgEAAZkBAAFmAQABzAEAAWYBAAH/AQABZgEzAgABZgIzAQABZgEzAWYBAAFmATMBmQEA
        AWYBMwHMAQABZgEzAf8BAAJmAgACZgEzAQADZgEAAmYBmQEAAmYBzAEAAWYBmQIAAWYBmQEzAQABZgGZ
        AWYBAAFmApkBAAFmAZkBzAEAAWYBmQH/AQABZgHMAgABZgHMATMBAAFmAcwBmQEAAWYCzAEAAWYBzAH/
        AQABZgH/AgABZgH/ATMBAAFmAf8BmQEAAWYB/wHMAQABzAEAAf8BAAH/AQABzAEAApkCAAGZATMBmQEA
        AZkBAAGZAQABmQEAAcwBAAGZAwABmQIzAQABmQEAAWYBAAGZATMBzAEAAZkBAAH/AQABmQFmAgABmQFm
        ATMBAAGZATMBZgEAAZkBZgGZAQABmQFmAcwBAAGZATMB/wEAApkBMwEAApkBZgEAA5kBAAKZAcwBAAKZ
        Af8BAAGZAcwCAAGZAcwBMwEAAWYBzAFmAQABmQHMAZkBAAGZAswBAAGZAcwB/wEAAZkB/wIAAZkB/wEz
        AQABmQHMAWYBAAGZAf8BmQEAAZkB/wHMAQABmQL/AQABzAMAAZkBAAEzAQABzAEAAWYBAAHMAQABmQEA
        AcwBAAHMAQABmQEzAgABzAIzAQABzAEzAWYBAAHMATMBmQEAAcwBMwHMAQABzAEzAf8BAAHMAWYCAAHM
        AWYBMwEAAZkCZgEAAcwBZgGZAQABzAFmAcwBAAGZAWYB/wEAAcwBmQIAAcwBmQEzAQABzAGZAWYBAAHM
        ApkBAAHMAZkBzAEAAcwBmQH/AQACzAIAAswBMwEAAswBZgEAAswBmQEAA8wBAALMAf8BAAHMAf8CAAHM
        Af8BMwEAAZkB/wFmAQABzAH/AZkBAAHMAf8BzAEAAcwC/wEAAcwBAAEzAQAB/wEAAWYBAAH/AQABmQEA
        AcwBMwIAAf8CMwEAAf8BMwFmAQAB/wEzAZkBAAH/ATMBzAEAAf8BMwH/AQAB/wFmAgAB/wFmATMBAAHM
        AmYBAAH/AWYBmQEAAf8BZgHMAQABzAFmAf8BAAH/AZkCAAH/AZkBMwEAAf8BmQFmAQAB/wKZAQAB/wGZ
        AcwBAAH/AZkB/wEAAf8BzAIAAf8BzAEzAQAB/wHMAWYBAAH/AcwBmQEAAf8CzAEAAf8BzAH/AQAC/wEz
        AQABzAH/AWYBAAL/AZkBAAL/AcwBAAJmAf8BAAFmAf8BZgEAAWYC/wEAAf8CZgEAAf8BZgH/AQAC/wFm
        AQABIQEAAaUBAANfAQADdwEAA4YBAAOWAQADywEAA7IBAAPXAQAD3QEAA+MBAAPqAQAD8QEAA/gBAAHw
        AfsB/wEAAaQCoAEAA4ADAAH/AgAB/wMAAv8BAAH/AwAB/wEAAf8BAAL/AgAD/wYAAfYBmgGZARsB/wsA
        Af8C8gHzAf8qAAH2AXkCWAF5ARoB/wkAAfQB8QPwAfMB/ygAAfYBeQFYAsMBWAF5ARoB/wcAAfQB8QHw
        AvQC8AHzAf8mAAH2AXkBWAHDAeUBoAHDAVgBeQEaAfQB/wQAAfQB8QHwAfQB8gHzAfQC8AHzAv8jAAHD
        AXkBWAKgAVkB5QGgAcMBWAF5ARoB9AH/AgAB9AHxAfAB9AHzAfEB8gHzAfQC8AHzAfQB/yEAAcMBeQFY
        AaAC5QF5AXoB5QGgAcMBWAF5ARoB9AEAAfQB8QHwAfQC8gLxAvIB9ALwAfMB9CEAAXkBWAKgAXoC5QJ5
        AuUBwwFYAXkBGgH/AvAC8wHxAvIB8ALxAfIB9ALwAfMB/yAAAVgBegGgAeUBeQF6AuUBeQFZAeUBoAHD
        AVgBeQH0AbwB8gHzAfIC8QLyAvEB8gHzAfQBvAHxAf8gAAHDAVgBegKgAnkC5QF5AeUDoAFSAvQBvAHy
        AvMC8QLyAfEB8gPzAQcB/yEAAcMBWAF6AcMBoAGZAZoFoAHDAVIB9AEAAfQBvAHyAfQB8wHxAfIF8wH0
        AQcB9CIAAcMBWAGaAsMBmQGgAcMDoAHDAVIB9AIAAfQBvAHyAvQB8QHzAfQC8gHzAfQBBwH/IwABwwFY
        AZoDwwGgAq0B5QHDAVIB/wMAAfQBvAHyA/QB8gLvAfIB9AEHAf8kAAHDAVgBmgHDAfYBoAGsAbkB5QF6
        AVYB9AQAAfQBvAHyAv8B8gHvAbwC8gHvAf8lAAEbAVgCoAGaAdQBnwF6AXkBzQH0BQAB9AG8A/MBvALx
        AfAB7wH/JgABwwN5AdwB2wFWAdwB1AH/BgAB9ATxAfABBwHyAQcB/yoAAfQC3AHbAQkLAAH/AfIC8QHz
        IQABQgFNAT4HAAE+AwABKAMAAUADAAEQAwABAQEAAQEFAAGAFwAD/wEAAfgBPwH4AT8EAAHwAR8B8AEf
        BAAB4AEPAeABDwQAAcABAwHAAQMEAAGAAQEBgAEBBQABAQEAAQEcAAGAAQABgAUAAcABAAHABQAB4AEA
        AeAFAAHwAQAB8AUAAfgBAAH4BQAB/AEAAfwFAAH/AcEB/wHBBAAL
</value>
  </data>
  <metadata name="toolStripSetting.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>218, 17</value>
  </metadata>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="tsBtnNewSpace.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAJFSURBVDhPpY5dSFNhAIYHY6CgdTG7cDcOCstESsgiSQRh
        dDFXmNsqEg2UIazQi1JEGrShjslo6UzPGLM510bgmmtrlprNqWU1rYtkc/40vWjd9eONbu3tnLM1PUQR
        9MDLd3G+5zkf6xc3mpqOt7e0ECqFgtB0dBB6rZYw6vWExWgk7BYL4bTbCc/ICDHudstSyi71tbUnO1Uq
        /AuroRDGXC5dSk1yqbp6kPrY/SiIbmcQ2tEg7rhC0D1extLmV0T9frxXq+kzkUjA43Bsp9QkIqHQSgW0
        o0mp1xNGn3cF/U9XsR37QctvWlvpk8JhsyGlJhFUVNCBu+5l9D0JgyBF48QaTJPrCH/6zngBhc1sZgbO
        lJbSAeqvhmdJ0Tz1ERZfBGOLUaxGt7BDvmTt8xYduG8wMAMniovpgGGclJ+vY+hFBMPTG3jgJzezAdvM
        ZnoUAz09zEBhQQEdqDXdw8DEB1j3iPZZ5ih0Gg0zcJDPt36LfcHRLikO3Zbg+mIDtPMPMTgdJEOR3wJd
        SiUzwOPx6BdQMrXTxgsoH65CoUaM884a3HqrhunlQjqgaGtjBg7k5DACe3ekU4yr74TofzWVDtxsbmYG
        9mdl/TFATeQSY2huKR241tjIDGRmZlp34jHyogQ1rytxZb4SEp+I3kVfFRpmZWgPKGGdW6ED9XV1zACH
        w5HJ5XLE43H6wt9YCARwWSr1ptRd2Gx2b0ZGRnxfdja4XC54ubng5+XhcH4+jhUV4VRJCcrLynBWIPCe
        I0lp/wuL9RNm6yN8FL7AtwAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="btnSaveIt.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAABQSURBVDhP3YxJCgAgDAP7dH+uhJIggkvVgzgQLGaxW+RN
        iZySf85gBi+KJDRQSYQGmEWR/DJQGV01OSFjVeh41WmNobYGCO43B4DCAZ1iVgBP6Iyo8QiMcwAAAABJ
        RU5ErkJggg==
</value>
  </data>
  <data name="tsBtnAddLayer.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAIFSURBVEhLYxgZoGdeeP7x6zPPnQDjuWA8eXF6PlSacjB3
        fWbX/HX1/5Dxkq0VXVBpysHM9aldr/7s/Q/Cs9fW/wPRizaXUM+C6WsTup792vIfhKevrv8HoudtLKCe
        BZNXRHVNXFH/DxnPBgYbVJpy0LcsuOvWl/n/Qbh7af0/ED11dQL1LOhY5Nt16f3k/yDcvKD+H4ieAPQV
        VJo4kNNu5lQ/x92vfo4LEnb0q5nm6DdpY/C6+tn1/5Dx1C2R62qmgdS4+zWBsTcQ+4FxcbejE9RYBJi9
        I+rmgcdV/0G4fkrVP2xsZD4uNSD23D0JN6HGIsCEjcE363tz/1EDT90WjGlB3lyxmw37hP6DcH290D9s
        bGQ+LjUgdu5cAUwLEqdx3czfxPgfhPPqGf5hYyPzcakBseOnMWNa4FfOvyiyk2d/RCfn/lAQ7uAAYtb9
        Ia2s+yOnsDxMX8n4H4yBBoDoyCkMDwNbGfYHtjKiYJB6jxK2RVBjiQMuRaxd8SCXIWGnIgbq5QOnPPau
        iFmM/0E4HGg4iLbLY6SeBXbZnF2Bkxn/gzHQAhBtmUlFC6zTubu8gAYjY/NUZupZYJnM1+XazvgfhF2A
        hoNo4wQqWmCeINBl38T4H4yBFoBowzhW6llgFi3a7FzC9dkRiB2A2LSE87NeOGczVJoqgAmImdEwSGyg
        AQMDALGgwAhg0QugAAAAAElFTkSuQmCC
</value>
  </data>
  <data name="tsBtnRemoveLayer.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADQSURBVEhLYxgFo2AUDBBYnpRUcqqtbdqpxsZpp+rrERjE
        B4l3dk471d2Ngtfl59dAtRMGR2trj/9cs+Y/CK+qr/8HYyPzkcVB7DOtrVeg2gmDHbGxx+dUVv4jBR/J
        yLgA1U4YzBUROX6IgeE/CNczMPyDsZH5yOIg9kpp6XNQ7YRBP9CCNUCNIAzSDGMj85HFQeyZ4uLEW5At
        I1NXLyW1CIZrJCUX1YiILKri41tUzsa2qISRcVEJAwOYBvGr+PkXFUpLN0G1j4JRMAroCxgYALctrNJ2
        NdykAAAAAElFTkSuQmCC
</value>
  </data>
  <data name="miBtnUp.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAJsSURBVEhLYyAF2Nvbs4AwlEtdEBoaynbwxNT9B45O3R8a
        qsUGFaYeWLS6omfzrt5/ILxgZUUPVJg6oGtCXOD+/fv/btzR/u/jj+P/QeyW3rhAqDRloKTaQ/nmszXv
        3n0/+H/d9sZ/IBqEQWK5JR7KUGXkgfh4e449p7pPr9pS/w8bPnBqwmmQGqhy0sG81ZnTnn3a9H/Zpvp/
        yDQMg/jzVxRMgyonDdR1+kbOX1v/7+H7Vf8XrEelYRjEn7+u/l91W0AkVBtxIKfSXuPM3Y6Ps1fX/yMG
        n7s75WNakb0GVDt+EBOjx73ucOHFKcvr/115Pu0/CMPYyGLo/E2Hqy66AvVCjcENeueFzTz7qOd/76L6
        f2cfdf8HYRgbWQyVD1HfOy96JtQY3CAmx0A/tdDCPrXQxD6lzNR97dGSLy1z6v8dudPwH0bD8NqLpV9S
        iizdU0uB6ktt7GNyzPShxhANmFYejn9dP63kHza84lD8a5AaiFLyANP8HdGv6ydk/Nt0IeM/jIbh+TuC
        KLdg0o7A1/Wd0f+w4UnbfCm3oHmVzuv6eot/U/ba/a+vd/o3fb/z/2n7HP9P2WP7HyQHUgNRSh5gKpkv
        /bqiXvhf5Wrh/zAahnPmCFFuQc4c4ddF9fz/sOHMSfyUW5A9Sfh1Tj3Pv5xFPP/hNBTHT2Kj3IJ0oAVp
        9Rz/0udy/IfRMBw/kQoWxAMtSKxn/YcNx/RTyYLYeuZ/sdOY/8NpKA5qpdwCxsQe+Vvp9eJ/0upF/6TW
        C/9Jrhf8k1TP/yehnvdPaAvXLZAaiFIygby9PIe+Pb8ADMvDsD6IZiBQmzEwAACNm/rdoZW4NAAAAABJ
        RU5ErkJggg==
</value>
  </data>
  <data name="miBtnDown.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAJvSURBVEhLrY1fSFNRHMeX1hhlrBrRQw/3wReJaD1UGFSH
        itAXIYUbiMtlOf/QgpQZM6Ff0fpjbrTGvCGtXfGh1LVFK2qEdrUlhWY06CXJh3ywhwuGIdTD7Zw87RzZ
        wyzd7hc+fH+/3/n+zs/wPwnIYBKs5k0CSmPNQECCicVy1hrRs37qFGzUToNZOwObNQdYtAbYqjXCNq3O
        K0zRTDqamwqqrhnVk1IhWQIKMXd7wKLSTDqamwpst41qHazD2dDlgP2OUW28byKcBjDhJdflQMCoOvuK
        yBJQhLmf1eNAc8CstoIZZ8MZ0uGAM7RFbY9YCMcNFszdJW/P/8DVwZ1qcOggkV4dJneVowTgCA4OH1r0
        UkzfaCYdzU0FgecVKnTW4GwEEpX5H5ATVWr8YxPhgL8Jc5cTNfkf6H9tV0Fy4WwMJO2rP2Bz7rM62g4g
        R1spqm/dXxZNtS28+XKZcDwhwNyjY66F+gt7yxwte5CjpRTZnLut7Jvl5QvX9Pj6AE/OeMnkTBdJ111/
        68w+c87zvvCJHvbN8jpm27UhnryY+vRNIpTgQ8C8zuwz57SOJVtStsVd9s2/1dCKSj5MB+fvRQCvhPfT
        N+ed7aiEra9MHdePV8sxwL2PAX/9Pkg4vOcuRwFf6qyoZmurk9x/XnoQBzz7I044vOcejjRLLL562e3I
        NDLunxh8BjgbQ+NdEzTD4rnpnKu8+PPso7m5n6OEEntxBVOnM1dHeTGL5SePr7ZSUZTf87/ekieJG5jW
        t/y1lexZH/UOuL1PX/owpS/i9rKxfhLFHcaRsW5l9F23IoqikY31FUJoLYW1K5DB8AfUtvrdIPoGngAA
        AABJRU5ErkJggg==
</value>
  </data>
  <metadata name="$this.TrayHeight" type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>33</value>
  </metadata>
</root>