﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.Util;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Func;
using MasterCom.MTGis;
using MasterCom.Util.UiEx;

namespace MasterCom.RAMS.ZTFunc
{
    public class LteMgrsTestDepthStater : LteMgrsStaterBase
    {
        private List<LteMgrsDrawItem> drawList;
        protected List<LteMgrsTestDepthView> resultViewList;

        public override void DoStat(LteMgrsFuncItem curFuncItem)
        {
            if (curFuncItem.BaseQueryCitys == null)
            {
                return;
            }

            resultViewList = new List<LteMgrsTestDepthView>();
            LteMgrsCity curCity = curFuncItem.CurQueryCitys[curFuncItem.SelectedCityIndex];
            LteMgrsCity baseCity = curFuncItem.BaseQueryCitys[curFuncItem.SelectedCityIndex];

            foreach (LteMgrsRegion curRegion in curCity.RegionDic.Values)
            {
                if (!baseCity.RegionDic.ContainsKey(curRegion.RegionName))
                {
                    continue;
                }
                LteMgrsRegion baseRegion = baseCity.RegionDic[curRegion.RegionName];

                LteMgrsTestDepthView view = new LteMgrsTestDepthView(curCity.CityName, curRegion.RegionName);
                view.DoStat(baseRegion, curRegion);
                resultViewList.Add(view);

                FillDrawList(curRegion, baseRegion);
            }

            // 地市汇总
            LteMgrsTestDepthView summary = new LteMgrsTestDepthView(curCity.CityName, "汇总(网格内)");
            int curGridCount = 0, baseGridCount = 0, newGridCount = 0;
            double testDepth = 0;
            foreach (LteMgrsTestDepthView view in resultViewList)
            {
                if (view.RegionName == LteMgrsCity.SOutsideRegion)
                {
                    continue;
                }
                curGridCount += view.CurGridCount;
                baseGridCount += view.BaseGridCount;
                newGridCount += view.NewGridCount;
            }
            testDepth = baseGridCount + newGridCount == 0 ? 0 : 1d * curGridCount / (baseGridCount + newGridCount);
            summary.SetSummary(baseGridCount, curGridCount, newGridCount, testDepth);
            resultViewList.Add(summary);
        }

        public override List<LteMgrsResultControlBase> GetResult()
        {
            LteMgrsTestDepthResult resultForm = new LteMgrsTestDepthResult();
            if (resultViewList != null)
            {
                resultForm.FillData(new List<LteMgrsTestDepthView>(resultViewList), new List<LteMgrsDrawItem>(drawList));
            }
            return new List<LteMgrsResultControlBase>() { resultForm };
        }

        public override void Clear()
        {
            if (resultViewList != null)
            {
                resultViewList.Clear();
                resultViewList = null;
            }
            if (drawList != null)
            {
                drawList.Clear();
                drawList = null;
            }
        }

        private void FillDrawList(LteMgrsRegion curRegion, LteMgrsRegion baseRegion)
        {
            if (drawList == null)
            {
                drawList = new List<LteMgrsDrawItem>();
            }

            foreach (string curKey in curRegion.GridDic.Keys)
            {
                LteMgrsGrid curGrid = curRegion.GridDic[curKey];
                LteMgrsDrawItem item = new LteMgrsDrawItem(new DbPoint(curGrid.TLLng, curGrid.BRLat), new DbPoint(curGrid.BRLng, curGrid.TLLat));
                if (!baseRegion.GridDic.ContainsKey(curKey))
                {
                    item.FillColor = Ranges.ColorRanges[0].color;
                }
                else
                {
                    item.FillColor = Ranges.ColorRanges[1].color;
                }
                item.ToolInfoTitle = curGrid.MgrsString;
                item.ToolInfoTitle = curGrid.DetailInfo;
                drawList.Add(item);
            }

            foreach (string baseKey in baseRegion.GridDic.Keys)
            {
                if (!curRegion.GridDic.ContainsKey(baseKey))
                {
                    LteMgrsGrid baseGrid = baseRegion.GridDic[baseKey];
                    LteMgrsDrawItem item = new LteMgrsDrawItem(new DbPoint(baseGrid.TLLng, baseGrid.BRLat), new DbPoint(baseGrid.BRLng, baseGrid.TLLat));
                    item.FillColor = Ranges.ColorRanges[2].color;
                    item.ToolInfoTitle = baseGrid.MgrsString;
                    item.ToolInfoTitle = baseGrid.DetailInfo;
                    drawList.Add(item);
                }
            }
        }

        public static LteMgrsColorRange Ranges { get; set; } = new LteMgrsTestDepthColorRange();
    }

    public class LteMgrsTestDepthView
    {
        public LteMgrsTestDepthView(string cityName, string regionName)
        {
            CityName = cityName;
            RegionName = regionName;
        }

        public void DoStat(LteMgrsRegion baseRegion, LteMgrsRegion curRegion)
        {
            BaseGridCount = baseRegion.GridDic.Count;
            CurGridCount = curRegion.GridDic.Count;
            foreach (string curKey in curRegion.GridDic.Keys)
            {
                if (!baseRegion.GridDic.ContainsKey(curKey))
                {
                    ++NewGridCount;
                }
            }
            TestDepth = BaseGridCount + NewGridCount == 0 ? 0 : 1d * CurGridCount / (BaseGridCount + NewGridCount);
        }

        public void SetSummary(int baseGridCount, int curGridCount, int newGridCount, double testDepth)
        {
            BaseGridCount = baseGridCount;
            CurGridCount = curGridCount;
            NewGridCount = newGridCount;
            TestDepth = testDepth;
        }

        public string CityName
        {
            get;
            private set;
        }

        public string RegionName
        {
            get;
            private set;
        }

        public int BaseGridCount
        {
            get;
            private set;
        }

        public int CurGridCount
        {
            get;
            private set;
        }

        public int NewGridCount
        {
            get;
            private set;
        }

        public double TestDepth
        {
            get;
            private set;
        }
    }
}
