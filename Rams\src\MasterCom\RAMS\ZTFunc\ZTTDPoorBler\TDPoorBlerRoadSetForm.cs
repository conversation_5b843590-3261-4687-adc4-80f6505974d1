﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class TDPoorBlerRoadSetForm : BaseDialog
    {
        public TDPoorBlerRoadSetForm()
        {
            InitializeComponent();
        }

        public void GetSettingFilterRet(out int minBler, out int roadDistance, out int sampleDistance)
        {
            minBler = (int)numBlerThreshold.Value;
            roadDistance = (int)numDistance.Value;
            sampleDistance = (int)numMaxDistance.Value;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }
    }
}
