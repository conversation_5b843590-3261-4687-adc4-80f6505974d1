﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using DevExpress.XtraCharts;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class LTEMIMOAntennaForm : MinCloseForm
    {
        public LTEMIMOAntennaForm(MainModel mainModel)
            : base(mainModel)
        {
            InitializeComponent();
        }

        public List<List<NPOIRow>> nrDatasList { get; set; }
        public List<string> sheetNames { get; set; }
        ZTLteMiMoAntenna.CellAngleData data = null;
        int iDataNum = 0;
        Dictionary<string, ZTLteMiMoAntenna.CellAngleData> DicUtranCellData;

        public void FillData(Dictionary<string, ZTLteMiMoAntenna.CellAngleData> dicUtranCellAngelData)
        {
            DicUtranCellData = new Dictionary<string, ZTLteMiMoAntenna.CellAngleData>();
            foreach (string cellName in dicUtranCellAngelData.Keys)
            {
                ZTLteMiMoAntenna.CellAngleData tmpData = dicUtranCellAngelData[cellName];
                this.DicUtranCellData.Add(cellName, tmpData);
            }

            try
            {
                iDataNum = dicUtranCellAngelData.Count;
                labNum.Text = iDataNum.ToString();
                int iPage = iDataNum % 200 > 0 ? iDataNum / 200 + 1 : iDataNum / 200;
                labPage.Text = iPage.ToString();

                dataGridViewCell.Rows.Clear();
                intDataViewColumn(dataGridViewAngle, nrDatasList[1][0].cellValues);

                int rowCellAt = 0;
                foreach (NPOIRow rowData in nrDatasList[0])
                {
                    if (rowCellAt == 0)
                    {
                        intDataViewColumn(dataGridViewCell, rowData.cellValues);
                        rowCellAt++;
                        continue;
                    }
                    if (rowCellAt > 200)
                        break;
                    initDataRow(dataGridViewCell, rowData);
                    rowCellAt++;
                }
                FillAngleData();
                txtPage.Text = "1";
            }
            catch
            {
                //continue
            }
        }

        /// <summary>
        /// 角度级数据赋值
        /// </summary>
        private void FillAngleData()
        {
            dataGridViewAngle.Rows.Clear();
            foreach (DataGridViewRow dgCell in dataGridViewCell.Rows)
            {
                if (dgCell == null || dgCell.Cells[3].Value == null)
                    continue;

                foreach (NPOIRow rowData in nrDatasList[1])
                {
                    if (rowData.cellValues[3].ToString() == dgCell.Cells[3].Value.ToString())
                    {
                        initDataRow(dataGridViewAngle, rowData);
                    }
                }
            }
        }

        /// <summary>
        /// 初始化列头
        /// </summary>
        private void intDataViewColumn(DataGridView dataGridView, List<object> objs)
        {
            dataGridView.Columns.Clear();
            dataGridView.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.AllCells;
            int idx = 1;
            foreach (object obj in objs)
            {
                dataGridView.Columns.Add(idx++.ToString(), obj.ToString());
            }
        }

        /// <summary>
        /// 初始化数据赋值
        /// </summary>
        private void initDataRow(DataGridView datatGridView, NPOIRow nop)
        {
            DataGridViewRow row = new DataGridViewRow();
            row.Tag = nop.cellValues[3];//小区名称
            foreach (object obj in nop.cellValues)
            {
                DataGridViewTextBoxCell boxcell = new DataGridViewTextBoxCell();
                boxcell.Value = obj.ToString();
                row.Cells.Add(boxcell);
            }
            datatGridView.Rows.Add(row);
        }

        /// <summary>
        /// 按小区模糊查找，前200个小区
        /// </summary>
        private void FillData(string strCellName)
        {
            dataGridViewCell.Rows.Clear();//小区级
            int rowCellAt = 0;
            foreach (NPOIRow rowData in nrDatasList[0])
            {
                if (nrDatasList[0][0].cellValues[0].ToString() == rowData.cellValues[0].ToString())
                    continue;

                if (strCellName != "" && rowData.cellValues[3].ToString().IndexOf(strCellName) < 0)
                    continue;

                if (rowCellAt >= 200)
                    break;
                initDataRow(dataGridViewCell, rowData);
                rowCellAt++;
            }

            FillAngleData();
        }

        /// <summary>
        /// 按页数查找
        /// </summary>
        private void FillData(int iPage)
        {
            dataGridViewCell.Rows.Clear();//小区级
            int iCount = -1;
            int rowCellAt = 0;
            foreach (NPOIRow rowData in nrDatasList[0])
            {
                if (nrDatasList[0][0].cellValues[0].ToString() == rowData.cellValues[0].ToString())
                    continue;

                iCount++;
                if (iCount / 200 != iPage)
                    continue;
                initDataRow(dataGridViewCell, rowData);
                rowCellAt++;
            }
            FillAngleData();
        }

        private void StripMenuDrawPhoto_Click(object sender, EventArgs e)
        {
            data = null;
            if (this.xtraTalSheet.SelectedTabPageIndex == 0)
            {
                string cellName = dataGridViewCell.SelectedRows[0].Tag as string;
                if (!DicUtranCellData.TryGetValue(cellName, out data))
                    return;
            }
            else if (this.xtraTalSheet.SelectedTabPageIndex == 1)
            {
                string cellName = dataGridViewAngle.SelectedRows[0].Tag as string;
                if (!DicUtranCellData.TryGetValue(cellName, out data))
                    return;
            }

            if (data != null)
            {
                drawAntRadarSeries();//俯视面雷达图
            }
        }

        private void StripMenuOutExcel_Click(object sender, EventArgs e)
        {
            try
            {
                ExcelNPOIManager.ExportToExcel(nrDatasList, sheetNames);
            }
            catch
            {
                MessageBox.Show("导出到Xls失败");
            }
        }

        /// <summary>
        /// 按平滑线绘雷达图
        /// </summary>
        private void drawAntRadarSeries()
        {
            double seriesMax = double.MinValue;
            double seriesMin = double.MaxValue;
            chartControlR0R1.Series.Clear();
            #region RSRP0数据
            Series seriesRSRP0 = new Series();
            seriesRSRP0.ShowInLegend = true;
            seriesRSRP0.LegendText = "RSRP0";
            seriesRSRP0.PointOptions.PointView = PointView.Values;
            RadarLineSeriesView lineSeriesView = new RadarLineSeriesView();
            lineSeriesView.Color = Color.Blue;
            lineSeriesView.LineMarkerOptions.Size = 2;
            seriesRSRP0.View = lineSeriesView;
            seriesRSRP0.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
            seriesRSRP0.Label.Visible = false;
            setSeriesInfo(ref seriesMax, ref seriesMin, seriesRSRP0, new Func(addSeriesRSRP0));
            chartControlR0R1.Series.Insert(0, seriesRSRP0);
            #endregion

            #region RSRP1数据
            Series seriesRSRP1 = new Series();
            seriesRSRP1.ShowInLegend = true;
            seriesRSRP1.LegendText = "RSRP1";
            seriesRSRP1.PointOptions.PointView = PointView.Values;
            RadarLineSeriesView lineSeriesView2 = new RadarLineSeriesView();
            lineSeriesView2.Color = Color.Orange;
            lineSeriesView2.LineMarkerOptions.Size = 2;
            seriesRSRP1.View = lineSeriesView2;
            seriesRSRP1.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
            seriesRSRP1.Label.Visible = false;
            setSeriesInfo(ref seriesMax, ref seriesMin, seriesRSRP1, new Func(addSeriesRSRP1));
            chartControlR0R1.Series.Insert(1, seriesRSRP1);

            ((RadarDiagram)chartControlR0R1.Diagram).AxisX.Range.SetInternalMinMaxValues(0, 340);
            ((RadarDiagram)chartControlR0R1.Diagram).AxisY.Range.MinValue = seriesMin - 1;
            ((RadarDiagram)chartControlR0R1.Diagram).AxisY.Range.MaxValue = seriesMax + 0.5;
            ((RadarDiagram)chartControlR0R1.Diagram).AxisX.GridSpacing = 20;
            ((RadarDiagram)chartControlR0R1.Diagram).RotationDirection = RadarDiagramRotationDirection.Clockwise;
            chartControlR0R1.Focus();
            #endregion

            chartControlRsrpDiff.Series.Clear();
            seriesMax = double.MinValue;
            seriesMin = double.MaxValue;

            #region △R数据
            Series seriesRSRPDiff = new Series();
            seriesRSRPDiff.ShowInLegend = true;
            seriesRSRPDiff.LegendText = "RSRP0-RSRP1";
            seriesRSRPDiff.PointOptions.PointView = PointView.Values;
            RadarLineSeriesView lineSeriesViewDiff = new RadarLineSeriesView();
            lineSeriesViewDiff.Color = Color.Blue;
            lineSeriesViewDiff.LineMarkerOptions.Size = 2;
            seriesRSRPDiff.View = lineSeriesView;
            seriesRSRPDiff.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
            seriesRSRPDiff.Label.Visible = false;
            setSeriesInfo(ref seriesMax, ref seriesMin, seriesRSRPDiff, new Func(addSeriesRSRPDiff));
            chartControlRsrpDiff.Series.Insert(0, seriesRSRPDiff);
            #endregion

            #region R稳定度数据
            Series seriesRSRP = new Series();
            seriesRSRP.ShowInLegend = true;
            seriesRSRP.LegendText = "RSRP标准差";
            seriesRSRP.PointOptions.PointView = PointView.Values;
            RadarLineSeriesView lineSeriesViewRsrp = new RadarLineSeriesView();
            lineSeriesViewRsrp.Color = Color.Orange;
            lineSeriesViewRsrp.LineMarkerOptions.Size = 2;
            seriesRSRP.View = lineSeriesView2;
            seriesRSRP.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
            seriesRSRP.Label.Visible = false;
            setSeriesInfo(ref seriesMax, ref seriesMin, seriesRSRP, new Func(addSeriesRSRP));
            chartControlRsrpDiff.Series.Insert(1, seriesRSRP);

            ((RadarDiagram)chartControlRsrpDiff.Diagram).AxisX.Range.SetInternalMinMaxValues(0, 340);
            ((RadarDiagram)chartControlRsrpDiff.Diagram).AxisY.Range.MinValue = seriesMin;
            ((RadarDiagram)chartControlRsrpDiff.Diagram).AxisY.Range.MaxValue = seriesMax + 1;
            ((RadarDiagram)chartControlRsrpDiff.Diagram).AxisX.GridSpacing = 20;
            ((RadarDiagram)chartControlRsrpDiff.Diagram).RotationDirection = RadarDiagramRotationDirection.Clockwise;
            chartControlRsrpDiff.Focus();
            #endregion
            this.xtraTalSheet.SelectedTabPageIndex = 2;
        }

        private double addSeriesRSRP0(int index)
        {
            return data.cExt.angleDatasExt[(index + 360) % 360].FRSRP0AVG;
        }

        private double addSeriesRSRP1(int index)
        {
            return data.cExt.angleDatasExt[(index + 360) % 360].FRSRP1AVG;
        }

        private double addSeriesRSRPDiff(int index)
        {
            return data.cExt.angleDatasExt[(index + 360) % 360].FRSRP0AVGDiffView;
        }

        private double addSeriesRSRP(int index)
        {
            return data.cExt.angleDatasExt[(index + 360) % 360].FRSRP;
        }

        private void setSeriesInfo(ref double seriesMax, ref double seriesMin, Series seriesRSRP, Func func)
        {
            for (int i = 359; i >= 0; i--)
            {
                double rsrpSum = 0;
                for (int j = i - 2; j <= i + 2; j++)
                {
                    rsrpSum += func(j);
                }
                double tmp = rsrpSum / 5;
                if (tmp > seriesMax)
                {
                    seriesMax = tmp;
                }
                if (tmp < seriesMin)
                {
                    seriesMin = tmp;
                }
                seriesRSRP.Points.Add(new SeriesPoint(i.ToString(), Math.Round(tmp, 2)));
            }
        }

        private delegate double Func(int data);

        /// <summary>
        /// 画Sinr0_CDF_PDF图
        /// </summary>
        private void drawAntSinr0CDF_PDFSeries()
        {
            int iMax = -40;
            double sumCdf = 0;
            //SINR0_PDF
            Series seriesSINR0PDF = new Series();
            seriesSINR0PDF.ShowInLegend = false;
            seriesSINR0PDF.LegendText = "SINR0_PDF";
            seriesSINR0PDF.PointOptions.PointView = PointView.Values;
            SplineSeriesView lineSeriesViewSinr0_PDF = new SplineSeriesView();
            lineSeriesViewSinr0_PDF.Color = Color.Blue;
            lineSeriesViewSinr0_PDF.LineMarkerOptions.Size = 2;
            seriesSINR0PDF.View = lineSeriesViewSinr0_PDF;
            seriesSINR0PDF.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
            seriesSINR0PDF.Label.Visible = false;
            //SINR0_CDF
            Series seriesSINR0CDF = new Series();
            seriesSINR0CDF.ShowInLegend = false;
            seriesSINR0CDF.LegendText = "SINR0_CDF";
            seriesSINR0CDF.PointOptions.PointView = PointView.Values;
            SplineAreaSeriesView lineSeriesViewSinr0_CDF = new SplineAreaSeriesView();
            lineSeriesViewSinr0_CDF.Color = Color.Orange;
            seriesSINR0CDF.View = lineSeriesViewSinr0_CDF;
            seriesSINR0CDF.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
            seriesSINR0CDF.Label.Visible = false;

            ((SplineSeriesView)seriesSINR0PDF.View).AxisY = ((XYDiagram)chartSINR0.Diagram).SecondaryAxesY[0];

            for (int i = -20; i < 31; i++)
            {
                int iCount = 0;
                foreach (string section in data.angleDatas.Keys)
                {
                    foreach (SampleMimoInfo info in data.angleDatas[section].SampleList)
                    {
                        if (i == (int)info.fSINR0)
                        {
                            iCount++;
                        }
                    }
                }
                double pdf = Math.Round(100.0 * iCount / data.sampleTotal, 2);
                seriesSINR0PDF.Points.Add(new SeriesPoint(i.ToString(), pdf));
                sumCdf += pdf;
                if (iMax < (int)pdf + 1)
                    iMax = (int)pdf + 1;

                double cdf = Math.Round(sumCdf, 2);
                seriesSINR0CDF.Points.Add(new SeriesPoint(i.ToString(), cdf));
            }

            chartSINR0.Series.Clear();
            chartSINR0.Series.Insert(0, seriesSINR0CDF);
            chartSINR0.Series.Insert(1, seriesSINR0PDF);
            ((XYDiagram)chartSINR0.Diagram).AxisX.GridSpacing = 3;
            ((XYDiagram)chartSINR0.Diagram).AxisY.GridSpacing = 10;
            ((XYDiagram)chartSINR0.Diagram).SecondaryAxesY[0].GridSpacing = iMax > 20 ? 5 : 2;
            ((SplineSeriesView)seriesSINR0PDF.View).AxisY.Range.MaxValue = iMax + 1 >= 100 ? 100 : iMax + 1;
            ((SplineSeriesView)seriesSINR0PDF.View).AxisY.Range.MinValue = 0;
            ((SplineSeriesView)seriesSINR0PDF.View).AxisX.Range.MaxValue = 30;
            ((SplineSeriesView)seriesSINR0PDF.View).AxisX.Range.MinValue = -20;
        }

        /// <summary>
        /// 画Sinr1_CDF_PDF图
        /// </summary>
        private void drawAntSinr1CDF_PDFSeries()
        {
            int iMax = -40;
            double sumCdf = 0;
            //SINR1_PDF
            Series seriesSINR1PDF = new Series();
            seriesSINR1PDF.ShowInLegend = false;
            seriesSINR1PDF.LegendText = "SINR1_PDF";
            seriesSINR1PDF.PointOptions.PointView = PointView.Values;
            SplineSeriesView lineSeriesViewSinr1_PDF = new SplineSeriesView();
            lineSeriesViewSinr1_PDF.Color = Color.Blue;
            lineSeriesViewSinr1_PDF.LineMarkerOptions.Size = 2;
            seriesSINR1PDF.View = lineSeriesViewSinr1_PDF;
            seriesSINR1PDF.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
            seriesSINR1PDF.Label.Visible = false;
            //SINR1_CDF
            Series seriesSINR1CDF = new Series();
            seriesSINR1CDF.ShowInLegend = false;
            seriesSINR1CDF.LegendText = "SINR1_CDF";
            seriesSINR1CDF.PointOptions.PointView = PointView.Values;
            SplineAreaSeriesView lineSeriesViewSinr1_CDF = new SplineAreaSeriesView();
            lineSeriesViewSinr1_CDF.Color = Color.Orange;
            seriesSINR1CDF.View = lineSeriesViewSinr1_CDF;
            seriesSINR1CDF.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
            seriesSINR1CDF.Label.Visible = false;

            ((SplineSeriesView)seriesSINR1PDF.View).AxisY = ((XYDiagram)chartSINR1.Diagram).SecondaryAxesY[0];
            for (int i = -20; i < 31; i++)
            {
                int iCount = 0;
                foreach (string section in data.angleDatas.Keys)
                {
                    foreach (SampleMimoInfo info in data.angleDatas[section].SampleList)
                    {
                        if (i == (int)info.fSINR1)
                        {
                            iCount++;
                        }
                    }
                }
                double pdf = Math.Round(100.0 * iCount / data.sampleTotal, 2);
                seriesSINR1PDF.Points.Add(new SeriesPoint(i.ToString(), pdf));
                sumCdf += pdf;
                if (iMax < (int)pdf + 1)
                    iMax = (int)pdf + 1;

                double cdf = Math.Round(sumCdf, 2);
                seriesSINR1CDF.Points.Add(new SeriesPoint(i.ToString(), cdf));
            }

            chartSINR1.Series.Clear();
            chartSINR1.Series.Insert(0, seriesSINR1CDF);
            chartSINR1.Series.Insert(1, seriesSINR1PDF);
            ((XYDiagram)chartSINR1.Diagram).AxisX.GridSpacing = 3;
            ((XYDiagram)chartSINR1.Diagram).AxisY.GridSpacing = 10;
            ((XYDiagram)chartSINR1.Diagram).SecondaryAxesY[0].GridSpacing = iMax > 20 ? 5 : 2;
            ((SplineSeriesView)seriesSINR1PDF.View).AxisY.Range.MaxValue = iMax + 1 >= 100 ? 100 : iMax + 1;
            ((SplineSeriesView)seriesSINR1PDF.View).AxisY.Range.MinValue = 0;
            ((SplineSeriesView)seriesSINR1PDF.View).AxisX.Range.MaxValue = 30;
            ((SplineSeriesView)seriesSINR1PDF.View).AxisX.Range.MinValue = -20;
        }

        /// <summary>
        /// 画Rsrp0_CDF_PDF图
        /// </summary>
        private void drawAntRsrp0CDF_PDFSeries()
        {
            int iMax = -1;
            double sumCdf = 0;
            //RSRP0 PDF
            Series seriesRSRP0PDF = new Series();
            seriesRSRP0PDF.ShowInLegend = false;
            seriesRSRP0PDF.LegendText = "RSRP0_PDF";
            seriesRSRP0PDF.PointOptions.PointView = PointView.Values;
            SplineSeriesView lineSeriesViewRsrp0_PDF = new SplineSeriesView();
            lineSeriesViewRsrp0_PDF.Color = Color.Blue;
            lineSeriesViewRsrp0_PDF.LineMarkerOptions.Size = 2;
            seriesRSRP0PDF.View = lineSeriesViewRsrp0_PDF;
            seriesRSRP0PDF.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
            seriesRSRP0PDF.Label.Visible = false;
            //RSRP0 CDF
            Series seriesRSRP0CDF = new Series();
            seriesRSRP0CDF.ShowInLegend = false;
            seriesRSRP0CDF.LegendText = "RSRP0_CDF";
            seriesRSRP0CDF.PointOptions.PointView = PointView.Values;
            SplineAreaSeriesView lineSeriesViewRsrp0_CDF = new SplineAreaSeriesView();
            lineSeriesViewRsrp0_CDF.Color = Color.Orange;
            seriesRSRP0CDF.View = lineSeriesViewRsrp0_CDF;
            seriesRSRP0CDF.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
            seriesRSRP0CDF.Label.Visible = false;

            ((SplineSeriesView)seriesRSRP0PDF.View).AxisY = ((XYDiagram)chartRSRP0.Diagram).SecondaryAxesY[0];
            for (int i = -140; i < -44; i++)
            {
                int iCount = 0;
                foreach (string section in data.angleDatas.Keys)
                {
                    foreach (SampleMimoInfo info in data.angleDatas[section].SampleList)
                    {
                        if (i == (int)info.fRSRP0)
                        {
                            iCount++;
                        }
                    }
                }

                double pdf = Math.Round(100.0 * iCount / data.sampleTotal, 2);
                seriesRSRP0PDF.Points.Add(new SeriesPoint(i.ToString(), pdf));
                sumCdf += pdf;
                if (iMax < (int)pdf+1)
                    iMax = (int)pdf + 1;

                double cdf = Math.Round(sumCdf, 2);
                seriesRSRP0CDF.Points.Add(new SeriesPoint(i.ToString(), cdf));
            }

            chartRSRP0.Series.Clear();
            chartRSRP0.Series.Insert(0, seriesRSRP0CDF);
            chartRSRP0.Series.Insert(1, seriesRSRP0PDF);
            ((XYDiagram)chartRSRP0.Diagram).AxisX.GridSpacing = 5;
            ((XYDiagram)chartRSRP0.Diagram).AxisY.GridSpacing = 10;
            ((SplineSeriesView)seriesRSRP0PDF.View).AxisY.Range.MaxValue = iMax + 1 >= 100 ? 100 : iMax + 1;
            ((SplineSeriesView)seriesRSRP0PDF.View).AxisY.Range.MinValue = 0;
            ((SplineSeriesView)seriesRSRP0PDF.View).AxisX.Range.MaxValue = -45;
            ((SplineSeriesView)seriesRSRP0PDF.View).AxisX.Range.MinValue = -140;
        }

        /// <summary>
        /// 画Rsrp1_CDF_PDF图
        /// </summary>
        private void drawAntRsrp1CDF_PDFSeries()
        {
            int iMax = -1;
            double sumCdf = 0;
            //RSRP1_PDF
            Series seriesRSRP1PDF = new Series();
            seriesRSRP1PDF.ShowInLegend = false;
            seriesRSRP1PDF.LegendText = "RSRP1_PDF";
            seriesRSRP1PDF.PointOptions.PointView = PointView.Values;
            SplineSeriesView lineSeriesViewRsrp1_PDF = new SplineSeriesView();
            lineSeriesViewRsrp1_PDF.Color = Color.Blue;
            lineSeriesViewRsrp1_PDF.LineMarkerOptions.Size = 2;
            seriesRSRP1PDF.View = lineSeriesViewRsrp1_PDF;
            seriesRSRP1PDF.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
            seriesRSRP1PDF.Label.Visible = false;
            //RSRP1_CDF
            Series seriesRSRP1CDF = new Series();
            seriesRSRP1CDF.ShowInLegend = false;
            seriesRSRP1CDF.LegendText = "RSRP1_CDF";
            seriesRSRP1CDF.PointOptions.PointView = PointView.Values;
            SplineAreaSeriesView lineSeriesViewRsrp1_CDF = new SplineAreaSeriesView();
            lineSeriesViewRsrp1_CDF.Color = Color.Orange;
            seriesRSRP1CDF.View = lineSeriesViewRsrp1_CDF;
            seriesRSRP1CDF.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
            seriesRSRP1CDF.Label.Visible = false;

            ((SplineSeriesView)seriesRSRP1PDF.View).AxisY = ((XYDiagram)chartRSRP1.Diagram).SecondaryAxesY[0];
            for (int i = -140; i < -44; i++)
            {
                int iCount = 0;
                foreach (string section in data.angleDatas.Keys)
                {
                    foreach (SampleMimoInfo info in data.angleDatas[section].SampleList)
                    {
                        if (i == (int)info.fRSRP1)
                        {
                            iCount++;
                        }
                    }
                }
                double pdf = Math.Round(100.0 * iCount / data.sampleTotal, 2);
                seriesRSRP1PDF.Points.Add(new SeriesPoint(i.ToString(), pdf));
                sumCdf += pdf;
                if (iMax < (int)pdf + 1)
                    iMax = (int)pdf + 1;

                double cdf = Math.Round(sumCdf, 2);
                seriesRSRP1CDF.Points.Add(new SeriesPoint(i.ToString(), cdf));
            }

            chartRSRP1.Series.Clear();
            chartRSRP1.Series.Insert(0, seriesRSRP1CDF);
            chartRSRP1.Series.Insert(1, seriesRSRP1PDF);
            ((XYDiagram)chartRSRP1.Diagram).AxisX.GridSpacing = 5;
            ((XYDiagram)chartRSRP1.Diagram).AxisY.GridSpacing = 10;
            ((SplineSeriesView)seriesRSRP1PDF.View).AxisY.Range.MaxValue = iMax + 1 >= 100 ? 100 : iMax + 1;
            ((SplineSeriesView)seriesRSRP1PDF.View).AxisY.Range.MinValue = 0;
            ((SplineSeriesView)seriesRSRP1PDF.View).AxisX.Range.MaxValue = -45;
            ((SplineSeriesView)seriesRSRP1PDF.View).AxisX.Range.MinValue = -140;
        }

        private void StripMenuOutCSV_Click(object sender, EventArgs e)
        {
            ZTAntFuncHelper.OutputCsvFile(nrDatasList, sheetNames);
        }

        private void antCDF_PDF_Click(object sender, EventArgs e)
        {
            data = null;
            if (this.xtraTalSheet.SelectedTabPageIndex == 0)
            {
                string cellName = dataGridViewCell.SelectedRows[0].Tag as string;
                if (!DicUtranCellData.TryGetValue(cellName, out data))
                    return;
            }
            else if (this.xtraTalSheet.SelectedTabPageIndex == 1)
            {
                string cellName = dataGridViewAngle.SelectedRows[0].Tag as string;
                if (!DicUtranCellData.TryGetValue(cellName, out data))
                    return;
            }
            if (data != null)
            {
                drawAntSinr0CDF_PDFSeries();//Sinr0_CDF_PDF图
                drawAntSinr1CDF_PDFSeries();//Sinr1_CDF_PDF图

                drawAntRsrp0CDF_PDFSeries();//Rsrp0_CDF_PDF图
                drawAntRsrp1CDF_PDFSeries();//Rsrp1_CDF_PDF图

                this.xtraTalSheet.SelectedTabPageIndex = 3;
            }
        }

        private void btnGo_Click(object sender, EventArgs e)
        {
            int iPage = 0;
            if (!int.TryParse(txtPage.Text.ToString(), out iPage))
            {
                iPage = 0;
            }
            iPage -= 1;
            int iCount = iDataNum;
            iCount = iCount % 200 > 0 ? iCount / 200 + 1 : iCount / 200;
            if (iPage < 0)
                iPage = 0;
            else if (iPage > iCount - 1)
                iPage = iCount - 1;
            txtPage.Text = (iPage + 1).ToString();
            FillData(iPage);
        }

        private void btnPrevpage_Click(object sender, EventArgs e)
        {
            int iPage = 0;
            if (!int.TryParse(txtPage.Text.ToString(), out iPage))
            {
                iPage = 0;
            }
            iPage -= 1;
            iPage = iPage - 1 >= 0 ? iPage - 1 : iPage;
            txtPage.Text = (iPage + 1).ToString();
            FillData(iPage);
        }

        private void btnNextpage_Click(object sender, EventArgs e)
        {
            int iPage = 0;
            if (!int.TryParse(txtPage.Text.ToString(), out iPage))
            {
                iPage = 0;
            }
            iPage -= 1;
            int iCount = iDataNum;
            iCount = iCount % 200 > 0 ? iCount / 200 + 1 : iCount / 200;

            iPage = iPage + 1 >= iCount ? iPage : iPage + 1;
            txtPage.Text = (iPage + 1).ToString();
            FillData(iPage);
        }

        private void btnSearch_Click(object sender, EventArgs e)
        {
            string strCellName = txtCellName.Text;
            FillData(strCellName);
        }

    }
}
