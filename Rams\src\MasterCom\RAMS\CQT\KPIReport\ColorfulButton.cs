﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.CQT
{
    public partial class ColorfulButton : UserControl
    {
        public ColorfulButton()
        {
            InitializeComponent();
        }

        public new string Text
        {
            get { return btn.Text; }
            set
            {
                if (value != null)
                {
                    btn.Text = value;
                } 
            }
        }

        private Color backColor;
        public Color BtnBackColor
        {
            get { return backColor; }
            set
            {
                backColor = value;
                btn.Appearance.BackColor = value;
            }
        }

        private void btn_MouseEnter(object sender, EventArgs e)
        {
            btn.Appearance.BackColor2 = Color.White;// Color.FromArgb(200, Color.White);
        }

        private void btn_MouseLeave(object sender, EventArgs e)
        {
            btn.Appearance.BackColor2 = backColor;
        }

        
    }
}
