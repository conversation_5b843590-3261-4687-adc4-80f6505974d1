﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class BlackBlock_PointDlg : BaseDialog
    {
        public BlackBlock_PointDlg()
        {
            InitializeComponent();
            cbxBlockType.Properties.Items.Clear();
            cbxBlockType.Properties.Items.Add("GSM语音");
            cbxBlockType.Properties.Items.Add("TD语音");
            cbxBlockType.SelectedIndex = 0;
            datEndDate.DateTime = DateTime.Now.Date;
            chkStatus.Items.Clear();
            chkStatus.Items.Add("已创建");
            chkStatus.Items.Add("已关闭");
            chkStatus.Items[0].CheckState = CheckState.Checked;
        }

        public BlackBlock_Point_QueryCond GetCondition()
        {
            BlackBlock_Point_QueryCond cond = new BlackBlock_Point_QueryCond();
            cond.blockType = cbxBlockType.SelectedIndex;
            cond.endDate = datEndDate.DateTime.Date;
            foreach (int chkIndex in chkStatus.CheckedIndices)
            {
                cond.blockStatusList.Add(chkIndex);
            }
            int blockID;
            if (!int.TryParse(edtBlockID.Text.Trim(), out blockID))
            {
                cond.blockID = -1;
            }
            else
            {
                cond.blockID = blockID;
            }
            return cond;
        }
    }

    public class BlackBlock_Point_QueryCond
    {
        /// <summary>
        /// 0:2G语音  1:TD语音
        /// </summary>
        public int blockType { get; set; } = 1;
        public DateTime endDate { get; set; } = DateTime.Now;
        public int EndTime
        {
            get { return (int)(JavaDate.GetMilliseconds(endDate) / 1000); }
        }
        public List<int> blockStatusList { get; set; } = new List<int>();
        public string name { get; set; } = "";
        public string reasonDesc { get; set; } = "";
        public string placeDesc { get; set; } = "";
        public string cellName { get; set; } = "";
        public int blockID { get; set; } = -1;//all
    }
}
