﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using MasterCom.RAMS.Stat;

namespace MasterCom.RAMS.KPI_Statistics
{
    public partial class ReportFilterControl : UserControl
    {
        /// <summary>
        /// 原始数据
        /// </summary>
        List<ReportStyle> listOriginalData = new List<ReportStyle>();
        public ReportFilterControl()
        {
            InitializeComponent();
        }

        /// <summary>
        /// 选择的报表项
        /// </summary>
        public ReportStyle SelectedReport
        {
            get 
            {
                return listBoxCtrData.SelectedItem as ReportStyle;
            }
        }

        /// <summary>
        /// 初始加载数据到listBoxCtrData
        /// </summary>
        public void initReportPicker()
        {
            List<ReportStyle> reports = KPIReportManager.Instance.Reports;
            listBoxCtrData.DataSource = null;
            AddWithSortReportData(ref reports);
            listBoxCtrData.DataSource = reports;

            if (listBoxCtrData.DataSource != null)
            {
                listBoxCtrData.SelectedIndex = 0;
            }
        }

        public void initReportPicker(List<ReportStyle> reports)
        {
            listBoxCtrData.DataSource = null;
            AddWithSortReportData(ref reports);
            listBoxCtrData.DataSource = reports;

            if (listBoxCtrData.DataSource != null)
            {
                listBoxCtrData.SelectedIndex = 0;
            }
        }

        /// <summary>
        /// 2个筛选用的控件宽度显示效果对齐
        /// </summary>
        private void popupContainerEdit_QueryPopUp(object sender, CancelEventArgs e)
        {
            PopupContainerEdit popupedit = (PopupContainerEdit)sender;
            popupContainerCtr.Width = popupedit.Width - 4;
        }

        /// <summary>
        /// 筛选条件改变时对应listbox数据改变
        /// </summary>
        private void txtFilter_TextChanged(object sender, EventArgs e)
        {
            List<ReportStyle> listRpt = new List<ReportStyle>();
            foreach (ReportStyle item in listOriginalData)
            {
                if (string.IsNullOrEmpty(txtFilter.Text) || item.name.ToUpper().IndexOf(txtFilter.Text.ToUpper()) >= 0)
                {
                    listRpt.Add(item);
                }
            }
            listBoxCtrData.DataSource = listRpt;
            if (listBoxCtrData.DataSource != null)
            {
                listBoxCtrData.SelectedIndex = 0;
            }
        }

        /// <summary>
        /// 选择改变时,对应选择框中的文本同时变化
        /// </summary>
        private void listBoxCtrData_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (listBoxCtrData.SelectedItem != null)
            {
                popupCntEdit.Text = listBoxCtrData.SelectedItem.ToString();
            }
            else
            {
                popupCntEdit.Text = "";
            }
        }

        /// <summary>
        /// 双击选中报表关闭选择框
        /// </summary>
        private void listBoxCtrData_DoubleClick(object sender, EventArgs e)
        {
            popupCntEdit.ClosePopup();
        }

        public void AddWithSortReportData(ref List<ReportStyle> listRtp)
        {
            int index = 0;
            listOriginalData.Clear();
            listRtp.Sort((a, b) => a.name.ToUpper().CompareTo(b.name.ToUpper()));
            for (int i = 0; i < listRtp.Count; i++)
            {
                //如果存在public则移到最前
                if (listRtp[i].name.ToUpper().IndexOf("PUBLIC") >= 0)
                {
                    listOriginalData.Insert(index, listRtp[i]);
                    listRtp.Insert(index, listRtp[i]);
                    listRtp.RemoveAt(i + 1);                   
                    index++;
                }
                else
                {
                    listOriginalData.Add(listRtp[i]);
                }
            }
        }
    }
}
