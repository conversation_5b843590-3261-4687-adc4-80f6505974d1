<?xml version="1.0"?>
<Configs>
  <Config name="ReportSetting">
    <Item name="styles" typeName="IDictionary">
      <Item typeName="String" key="Name">public_5G_NR_数据指标统计</Item>
      <Item typeName="IList" key="Cells">
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">测试总时间(s)</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">253</Item>
          <Item typeName="Int32" key="BkColorG">233</Item>
          <Item typeName="Int32" key="BkColorB">217</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">测试总里程(Km)</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">253</Item>
          <Item typeName="Int32" key="BkColorG">233</Item>
          <Item typeName="Int32" key="BkColorB">217</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">平均车速(Km/h)</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">253</Item>
          <Item typeName="Int32" key="BkColorG">233</Item>
          <Item typeName="Int32" key="BkColorB">217</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">综合覆盖率</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">3</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">5G 网络测试覆盖率(核心城区：SS-RSRP≥-88且SS-SINR≥-3)</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">7</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">5G 网络测试覆盖率(普通城区：SS-RSRP≥-91dBm&amp;SS-SINR≥-3)</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">8</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">LTE 锚点覆盖率
(RSRP≥-100dBm&amp;SINR≥-5)</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">9</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">LTE 锚点覆盖率
(RSRP≥-110dBm&amp;SINR≥-3)</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">10</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">4G占用小区数</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">11</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">NR占用小区数</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">12</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">SS-RSRP均值</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">13</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">SS-SINR均值</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">14</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RSRP均值</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">15</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">SINR均值</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">16</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">SS-RSRP≥-100dBm&amp;SS-SINR≥0dB采样点数</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">17</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">SS-RSRP≥-105dBm&amp;SS-SINR≥0dB采样点数</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">18</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">SS-RSRP≥-110dBm&amp;SS-SINR≥0dB采样点数</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">19</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">SS-RSRP≥-115dBm&amp;SS-SINR≥0dB采样点数</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">20</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">SS-RSRP≥-120dBm&amp;SS-SINR≥0dB采样点数</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">21</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">SS-RSRP≥-100dBm&amp;SS-SINR≥-5dB采样点数</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">22</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">SS-RSRP≥-105dBm&amp;SS-SINR≥-5dB采样点数</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">23</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">SS-RSRP≥-110dBm&amp;SS-SINR≥-5dB采样点数</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">24</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">SS-RSRP≥-115dBm&amp;SS-SINR≥-5dB采样点数</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">25</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">SS-RSRP≥-120dBm&amp;SS-SINR≥-5dB采样点数</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">26</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">总采样点数（SS-RSRP&amp;SS-SINR）</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">27</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">SS-RSRP≥-100dBm样点数</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">28</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">SS-RSRP≥-105dBm样点数</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">29</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">SS-RSRP≥-110dBm样点数</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">30</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">SS-RSRP≥-115dBm样点数</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">31</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">SS-RSRP≥-120dBm样点数</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">32</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">总采样点数（SS-RSRP）</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">33</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">SS-SINR≥0dB样点数</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">34</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">SS-SINR≥-5dB样点数</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">35</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">总采样点数（SS-SINR）</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">36</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">MAC层上行平均吞吐率(Mbps)</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">37</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">141</Item>
          <Item typeName="Int32" key="BkColorG">180</Item>
          <Item typeName="Int32" key="BkColorB">226</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">MAC层下行平均吞吐率(Mbps)</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">38</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">141</Item>
          <Item typeName="Int32" key="BkColorG">180</Item>
          <Item typeName="Int32" key="BkColorB">226</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">MAC层上行低速率占比(%)(≤2M)</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">39</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">141</Item>
          <Item typeName="Int32" key="BkColorG">180</Item>
          <Item typeName="Int32" key="BkColorB">226</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">MAC层下行低速率占比(%)(≤100M)</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">40</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">141</Item>
          <Item typeName="Int32" key="BkColorG">180</Item>
          <Item typeName="Int32" key="BkColorB">226</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">MAC层上行高速率占比(%)(≥80M)</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">41</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">141</Item>
          <Item typeName="Int32" key="BkColorG">180</Item>
          <Item typeName="Int32" key="BkColorB">226</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">MAC层下行高速率占比(%)(≥800M)</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">42</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">141</Item>
          <Item typeName="Int32" key="BkColorG">180</Item>
          <Item typeName="Int32" key="BkColorB">226</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">LTE RLC 下行平均吞吐率(Mbps)</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">43</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">141</Item>
          <Item typeName="Int32" key="BkColorG">180</Item>
          <Item typeName="Int32" key="BkColorB">226</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">LTE RLC 上行平均吞吐率(Mbps)</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">44</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">141</Item>
          <Item typeName="Int32" key="BkColorG">180</Item>
          <Item typeName="Int32" key="BkColorB">226</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">NR RLC 下行平均吞吐率(Mbps)</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">45</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">141</Item>
          <Item typeName="Int32" key="BkColorG">180</Item>
          <Item typeName="Int32" key="BkColorB">226</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">NR RLC 上行平均吞吐率(Mbps)</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">46</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">141</Item>
          <Item typeName="Int32" key="BkColorG">180</Item>
          <Item typeName="Int32" key="BkColorB">226</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">NR RLC层下行低速率占比(%)(≤50M)</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">47</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">141</Item>
          <Item typeName="Int32" key="BkColorG">180</Item>
          <Item typeName="Int32" key="BkColorB">226</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">NR RLC层下行低速率占比(%)(≤100M)</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">48</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">141</Item>
          <Item typeName="Int32" key="BkColorG">180</Item>
          <Item typeName="Int32" key="BkColorB">226</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">NR RLC层下行高速率占比(%)(≥450M)</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">49</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">141</Item>
          <Item typeName="Int32" key="BkColorG">180</Item>
          <Item typeName="Int32" key="BkColorB">226</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">NR RLC层下行高速率占比(%)(≥800M)</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">50</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">141</Item>
          <Item typeName="Int32" key="BkColorG">180</Item>
          <Item typeName="Int32" key="BkColorB">226</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">NR RLC层下行高速率占比(%)(≥1000M)</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">51</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">141</Item>
          <Item typeName="Int32" key="BkColorG">180</Item>
          <Item typeName="Int32" key="BkColorB">226</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">NR RLC层上行低速率占比(%)(≤1M)</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">52</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">141</Item>
          <Item typeName="Int32" key="BkColorG">180</Item>
          <Item typeName="Int32" key="BkColorB">226</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">NR RLC层上行低速率占比(%)(≤5M)</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">53</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">141</Item>
          <Item typeName="Int32" key="BkColorG">180</Item>
          <Item typeName="Int32" key="BkColorB">226</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">NR RLC层上行低速率占比(%)(≤10M)</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">54</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">141</Item>
          <Item typeName="Int32" key="BkColorG">180</Item>
          <Item typeName="Int32" key="BkColorB">226</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">NR RLC层上行高速率占比(≥60M)</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">55</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">141</Item>
          <Item typeName="Int32" key="BkColorG">180</Item>
          <Item typeName="Int32" key="BkColorB">226</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">NR RLC层上行高速率占比(≥80M)</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">56</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">141</Item>
          <Item typeName="Int32" key="BkColorG">180</Item>
          <Item typeName="Int32" key="BkColorB">226</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">NR RLC层上行高速率占比(≥100M)</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">57</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">141</Item>
          <Item typeName="Int32" key="BkColorG">180</Item>
          <Item typeName="Int32" key="BkColorB">226</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">FTP上行平均吞吐率(Mbps)</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">58</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">141</Item>
          <Item typeName="Int32" key="BkColorG">180</Item>
          <Item typeName="Int32" key="BkColorB">226</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">FTP下行平均吞吐率(Mbps)</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">59</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">141</Item>
          <Item typeName="Int32" key="BkColorG">180</Item>
          <Item typeName="Int32" key="BkColorB">226</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">NR_PDCCH DL Grant Count</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">60</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">141</Item>
          <Item typeName="Int32" key="BkColorG">180</Item>
          <Item typeName="Int32" key="BkColorB">226</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">NR_DL Initial BLER(%)</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">61</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">141</Item>
          <Item typeName="Int32" key="BkColorG">180</Item>
          <Item typeName="Int32" key="BkColorB">226</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">NR DL MCS均值</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">62</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">141</Item>
          <Item typeName="Int32" key="BkColorG">180</Item>
          <Item typeName="Int32" key="BkColorB">226</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">NR CQI均值</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">63</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">141</Item>
          <Item typeName="Int32" key="BkColorG">180</Item>
          <Item typeName="Int32" key="BkColorB">226</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">5G驻留时长（s）</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">64</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">228</Item>
          <Item typeName="Int32" key="BkColorG">223</Item>
          <Item typeName="Int32" key="BkColorB">236</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">5G时长驻留比（%）</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">65</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">228</Item>
          <Item typeName="Int32" key="BkColorG">223</Item>
          <Item typeName="Int32" key="BkColorB">236</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">SgNB添加成功率（%）</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">66</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">228</Item>
          <Item typeName="Int32" key="BkColorG">223</Item>
          <Item typeName="Int32" key="BkColorB">236</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">NR侧随机接入成功率（%）</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">67</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">228</Item>
          <Item typeName="Int32" key="BkColorG">223</Item>
          <Item typeName="Int32" key="BkColorB">236</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">NR侧随机接入成功次数</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">68</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">228</Item>
          <Item typeName="Int32" key="BkColorG">223</Item>
          <Item typeName="Int32" key="BkColorB">236</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">NR侧随机接入尝试次数</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">69</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">228</Item>
          <Item typeName="Int32" key="BkColorG">223</Item>
          <Item typeName="Int32" key="BkColorB">236</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">NR下行 256QAM比例(%)</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">70</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">216</Item>
          <Item typeName="Int32" key="BkColorG">228</Item>
          <Item typeName="Int32" key="BkColorB">188</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">NR下行 64QAM比例(%)</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">71</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">216</Item>
          <Item typeName="Int32" key="BkColorG">228</Item>
          <Item typeName="Int32" key="BkColorB">188</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">NR下行 16QAM比例(%)</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">72</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">216</Item>
          <Item typeName="Int32" key="BkColorG">228</Item>
          <Item typeName="Int32" key="BkColorB">188</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">NR下行 QPSK比例(%)</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">73</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">216</Item>
          <Item typeName="Int32" key="BkColorG">228</Item>
          <Item typeName="Int32" key="BkColorB">188</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">NR UL MCS均值</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">74</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">216</Item>
          <Item typeName="Int32" key="BkColorG">228</Item>
          <Item typeName="Int32" key="BkColorB">188</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">NR_UL Initial BLER(%)</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">75</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">216</Item>
          <Item typeName="Int32" key="BkColorG">228</Item>
          <Item typeName="Int32" key="BkColorB">188</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">NR上行256QAM比例(%)</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">76</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">216</Item>
          <Item typeName="Int32" key="BkColorG">228</Item>
          <Item typeName="Int32" key="BkColorB">188</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">NR上行 64QAM比例(%)</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">77</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">216</Item>
          <Item typeName="Int32" key="BkColorG">228</Item>
          <Item typeName="Int32" key="BkColorB">188</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">NR上行 16QAM比例(%)</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">78</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">216</Item>
          <Item typeName="Int32" key="BkColorG">228</Item>
          <Item typeName="Int32" key="BkColorB">188</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">NR上行 QPSK比例(%)</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">79</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">216</Item>
          <Item typeName="Int32" key="BkColorG">228</Item>
          <Item typeName="Int32" key="BkColorB">188</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">LTE_Path Loss(dB)</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">80</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">216</Item>
          <Item typeName="Int32" key="BkColorG">228</Item>
          <Item typeName="Int32" key="BkColorB">188</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">NR_Path Loss(dB)</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">81</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">216</Item>
          <Item typeName="Int32" key="BkColorG">228</Item>
          <Item typeName="Int32" key="BkColorB">188</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Nr_80040005/1000}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Nr_80040006/1000}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{(Nr_80040006/1000)/(Nr_80040005/1000/60/60) }</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*(Nr_8004001F/Nr_80040005)*(Nr_BA040051/Nr_BA040052)}%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">3</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*(Nr_BA040051/Nr_BA040052) }%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">7</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*Nr_BA040053/Nr_BA040052 }%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">8</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*Nr_BA040045/Nr_BA040050}%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">9</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{(100*Nr_BA040054/Nr_BA040050) }%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">10</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Nr_BA040043}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">11</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Nr_BA040044}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">12</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Nr_BA040002}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">13</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Nr_BA040009}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">14</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Nr_BA040017}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">15</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Nr_BA040019}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">16</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Nr_BA04000C}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">17</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Nr_BA04000D}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">18</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Nr_BA04000E}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">19</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Nr_BA04000F}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">20</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Nr_BA040010}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">21</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Nr_BA040011}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">22</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Nr_BA040012}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">23</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Nr_BA040013}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">24</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Nr_BA040014}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">25</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Nr_BA040015}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">26</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Nr_BA040052}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">27</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Nr_BA040003}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">28</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Nr_BA040004}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">29</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Nr_BA040005}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">30</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Nr_BA040006}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">31</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Nr_BA040007}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">32</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Nr_BA040001}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">33</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Nr_BA04000B}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">34</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Nr_BA04000A}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">35</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Nr_BA040008}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">36</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Nr_BA04003C/1024/1024}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">37</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Nr_BA04003E/1024/1024}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">38</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*Nr_BA040055/Nr_BA04003B }%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">39</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*Nr_BA040056/Nr_BA04003D }%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">40</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*Nr_BA040057/Nr_BA04003B }%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">41</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*Nr_BA040058/Nr_BA04003D }%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">42</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Nr_BA040037}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">43</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Nr_BA040039}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">44</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*Nr_BA04001E/Nr_BA04001A }%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">47</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*Nr_BA04001F/Nr_BA04001A }%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">48</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*Nr_BA040020/Nr_BA04001A }%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">49</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*Nr_BA040021/Nr_BA04001A }%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">50</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*Nr_BA040022/Nr_BA04001A }%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">51</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*Nr_BA040023/Nr_BA04001C }%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">52</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*Nr_BA040024/Nr_BA04001C }%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">53</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*Nr_BA040025/Nr_BA04001C }%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">54</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*Nr_BA040026/Nr_BA04001C }%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">55</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*Nr_BA040027/Nr_BA04001C }%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">56</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*Nr_BA040028/Nr_BA04001C }%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">57</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Nr_BA040046}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">60</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*Nr_BA04002A/Nr_BA040029 }%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">61</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Nr_BA040042}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">62</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Nr_BA040040}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">63</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Nr_8004001F/1000}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">64</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{(evtIdCount[9074])*100/(evtIdCount[9073])}%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">67</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{evtIdCount[9074]}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">68</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{evtIdCount[9073]}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">69</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*Nr_BA04002B/(Nr_BA04002B +Nr_BA04002C+Nr_BA04002D+Nr_BA04002E) }%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">70</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*Nr_BA04002C/(Nr_BA04002B +Nr_BA04002C+Nr_BA04002D+Nr_BA04002E) }</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">71</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*Nr_BA04002D/(Nr_BA04002B +Nr_BA04002C+Nr_BA04002D+Nr_BA04002E) }%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">72</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*Nr_BA04002E/(Nr_BA04002B +Nr_BA04002C+Nr_BA04002D+Nr_BA04002E) }%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">73</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Nr_BA040041}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">74</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*Nr_BA040030/Nr_BA04002F }%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">75</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*Nr_BA040031/(Nr_BA040031 +Nr_BA040032+Nr_BA040033+Nr_BA040034) }%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">76</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*Nr_BA040034/(Nr_BA040031 +Nr_BA040032+Nr_BA040033+Nr_BA040034) }%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">79</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Nr_BA04003A}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">80</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Nr_BA040035}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">81</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Nr_BA04001B/1024/1024}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">45</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Nr_BA04001D/1024/1024}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">46</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Nr_BA04003C/1024/1024}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">58</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Nr_BA04003E/1024/1024}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">59</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*Nr_8004001F/Nr_80040005 }%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">65</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*Nr_BA040032/(Nr_BA040031 +Nr_BA040032+Nr_BA040033+Nr_BA040034) }%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">77</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*Nr_BA040033/(Nr_BA040031 +Nr_BA040032+Nr_BA040033+Nr_BA040034) }%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">78</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Nr_8004001F/1000}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">4</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item typeName="String" key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">5G占网时长</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">4</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item typeName="String" key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Nr_BA040051}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">5</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item typeName="String" key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Nr_BA040052}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">6</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item typeName="String" key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RSRP_And_NR_SS_SINR_SampleNum</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">6</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item typeName="String" key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RSRP_[-88,)_And_NR_SS_SINR_[-3,)</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">5</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item typeName="String" key="FileNameFilter" />
        </Item>
      </Item>
      <Item typeName="IList" key="Graphs" />
      <Item typeName="IList" key="ColInfo" />
      <Item typeName="IList" key="ColWidth" />
    </Item>
  </Config>
</Configs>