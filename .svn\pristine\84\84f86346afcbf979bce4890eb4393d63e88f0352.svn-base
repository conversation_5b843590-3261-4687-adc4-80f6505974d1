﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func.SystemSetting;
using MasterCom.RAMS.ZTFunc;

namespace MasterCom.RAMS.Func.SystemSetting
{
    public partial class CellWrongProperties_GSM : PropertiesControl
    {
        public CellWrongProperties_GSM(ZTCellWrongDirQuery_GSM queryFunc)
        {
            InitializeComponent();
            this.queryFunc = queryFunc;
        }

        public override string ParentName
        {
            get { return queryFunc.FuncType.ToString(); }
        }

        public override string ParentSubName
        {
            get { return queryFunc.SubFuncType.ToString(); }
        }

        public override string SelfName
        {
            get { return queryFunc.Name; }
        }

        public override string TabPageName
        {
            get { return queryFunc.Name; }
        }

        public override bool IsValid()
        {
            return true;
        }

        public override void Flush()
        {
            chkBackgroundStat.Checked = queryFunc.BackgroundStat;
            spinEditRSCP.Value = (decimal)queryFunc.cellWrongCond.RxLevMin;
            spinEditDis.Value = (decimal)queryFunc.cellWrongCond.DistanceMin;
            spinEditAngle.Value = (decimal)queryFunc.cellWrongCond.AngleMin;
            spinEditPer.Value = (decimal)queryFunc.cellWrongCond.WrongRateMin;
        }

        public override void Apply()
        {
            queryFunc.BackgroundStat = chkBackgroundStat.Checked;
            queryFunc.cellWrongCond.RxLevMin = (int)spinEditRSCP.Value;
            queryFunc.cellWrongCond.DistanceMin = (int)spinEditDis.Value;
            queryFunc.cellWrongCond.AngleMin = (int)spinEditAngle.Value;
            queryFunc.cellWrongCond.WrongRateMin = (int)spinEditPer.Value;
        }

        private ZTCellWrongDirQuery_GSM queryFunc;
    }
}
