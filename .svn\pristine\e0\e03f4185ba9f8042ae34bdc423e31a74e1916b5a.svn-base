﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using MasterCom.RAMS.Func;
using DevExpress.XtraEditors.Controls;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class CSFBCauseSettingDlg : BaseForm
    {
        CCauseBase cause;

        CWeakCovCauseCond weakCovCond = null;
        COverCoverCauseCond overCovCond = null;
        CCovInConformityCauseCond covInConfCond = null;
        CCoverLapCauseCond covlapCond = null;
        CFastFailureCauseCond fastFailureCond = null;

        CBadQuaCauseCond badQuaCond = null;
        CStrongRxlBadQuaCauseCond strongRxlBQCond = null;

        CModThreeCauseCond modThreeCond = null;
        CIndoorLeakCauseCond indoorLeakCond = null;

        CFrequentHOCauseCond frequentHOCond = null;
        CDelayHOCauseCond delayHOCond = null;
        CUnReasonableHOCond unReasonHOCond = null;

        public CSFBCauseSettingDlg(CCauseBase cause)
        {
            InitializeComponent();
            this.cause = cause;
            refresh(cause);
        }

        private void refresh(CCauseBase cause)
        {
            checkedListBoxControlCauses.Items.Clear();

            CCauseBase cb = cause;

            while(cb != null)
            {
                CheckedListBoxItem item = new CheckedListBoxItem(cb);
                checkedListBoxControlCauses.Items.Add(item);
                item.CheckState = cb.Cond.BChecked ? System.Windows.Forms.CheckState.Checked : System.Windows.Forms.CheckState.Unchecked;
                getCond(cb);
                cb = cb.NextCause;
            }
            setDlgCond();
        }

        private void getCond(CCauseBase cause)
        {
            if (cause.Cond == null)
            {
                return;
            }
            if (cause.Cond is CWeakCovCauseCond)
            {
                weakCovCond = cause.Cond as CWeakCovCauseCond;
            }
            else if (cause.Cond is COverCoverCauseCond)
            {
                overCovCond = cause.Cond as COverCoverCauseCond;
            }
            else if (cause.Cond is CCovInConformityCauseCond)
            {
                covInConfCond = cause.Cond as CCovInConformityCauseCond;
            }
            else if (cause.Cond is CCoverLapCauseCond)
            {
                covlapCond = cause.Cond as CCoverLapCauseCond;
            }
            else if (cause.Cond is CFastFailureCauseCond)
            {
                fastFailureCond = cause.Cond as CFastFailureCauseCond;
            }
            else if (cause.Cond is CBadQuaCauseCond)
            {
                badQuaCond = cause.Cond as CBadQuaCauseCond;
            }
            else if (cause.Cond is CStrongRxlBadQuaCauseCond)
            {
                strongRxlBQCond = cause.Cond as CStrongRxlBadQuaCauseCond;
            }
            else if (cause.Cond is CModThreeCauseCond)
            {
                modThreeCond = cause.Cond as CModThreeCauseCond;
            }
            else if (cause.Cond is CIndoorLeakCauseCond)
            {
                indoorLeakCond = cause.Cond as CIndoorLeakCauseCond;
            }
            else if (cause.Cond is CFrequentHOCauseCond)
            {
                frequentHOCond = cause.Cond as CFrequentHOCauseCond;
            }
            else if (cause.Cond is CDelayHOCauseCond)
            {
                delayHOCond = cause.Cond as CDelayHOCauseCond;
            }
            else if (cause.Cond is CUnReasonableHOCond)
            {
                unReasonHOCond = cause.Cond as CUnReasonableHOCond;
            }
        }

        private void checkedListBoxControlCauses_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (checkedListBoxControlCauses.SelectedItem == null) return;

            CheckedListBoxItem item = checkedListBoxControlCauses.SelectedItem as CheckedListBoxItem;

            if (item == null) return;

            CCauseBase cb = item.Value as CCauseBase;

            if (cb == null || cb.Cond == null) return;

            textBoxDesc.Text = cb.Cond.GetCauseDesc();
            textBoxSuguest.Text = cb.Cond.GetCauseSuguest();
        }

        private void simpleButtonUp_Click(object sender, EventArgs e)
        {
            if (checkedListBoxControlCauses.SelectedItem == null) return;

            CheckedListBoxItem item = checkedListBoxControlCauses.SelectedItem as CheckedListBoxItem;

            if (item == null) return;

            CCauseBase cb = item.Value as CCauseBase;

            if (cb == null || cb.PrevCause == null) return;

            CCauseBase nextCause = cb.NextCause;
            CCauseBase prevCause = cb.PrevCause;
            CCauseBase prevPrevCause = prevCause.PrevCause;

            if (nextCause != null)
            {
                nextCause.PrevCause = prevCause;
            }
            prevCause.NextCause = nextCause;
            cb.PrevCause = prevPrevCause;

            if (prevPrevCause != null)
            {
                prevPrevCause.NextCause = cb;
            }
            cb.NextCause = prevCause;
            prevCause.PrevCause = cb;

            int idx = checkedListBoxControlCauses.Items.IndexOf(item);
            checkedListBoxControlCauses.Items.Remove(item);
            checkedListBoxControlCauses.Items.Insert(idx - 1, item);
            checkedListBoxControlCauses.SelectedItem = item;
        }

        private void simpleButtonDown_Click(object sender, EventArgs e)
        {
            if (checkedListBoxControlCauses.SelectedItem == null) return;

            CheckedListBoxItem item = checkedListBoxControlCauses.SelectedItem as CheckedListBoxItem;

            if (item == null) return;

            CCauseBase cb = item.Value as CCauseBase;

            if (cb == null || cb.NextCause == null) return;

            CCauseBase prevCause = cb.PrevCause;
            CCauseBase nextCause = cb.NextCause;
            CCauseBase nextNextCause = nextCause.NextCause;

            if (prevCause != null)
            {
                prevCause.NextCause = nextCause;
            }
            nextCause.PrevCause = prevCause;
            cb.NextCause = nextNextCause;

            if (nextNextCause != null)
            {
                nextNextCause.PrevCause = cb;
            }

            cb.PrevCause = nextCause;
            nextCause.NextCause = cb;

            int idx = checkedListBoxControlCauses.Items.IndexOf(item);
            checkedListBoxControlCauses.Items.Remove(item);
            checkedListBoxControlCauses.Items.Insert(idx + 1, item);
            checkedListBoxControlCauses.SelectedItem = item;
        }

        private void simpleButtonOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = System.Windows.Forms.DialogResult.OK;
        }

        private void simpleButtonCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = System.Windows.Forms.DialogResult.Cancel;
        }

        private void setDlgCond()
        {
            if (weakCovCond != null)
            {
                numericUpDownWeakCovMainRxlevMax.Value = (decimal)weakCovCond.MainRxlevMax;
                numericUpDownWeakCovNBMax.Value = (decimal)weakCovCond.NBRxlevMax;
            }
            if (overCovCond != null)
            {
                numericUpDownOverCovRxlev.Value = (decimal)overCovCond.MainRxlevMin;
                numericUpDownOverCovBtsNum.Value = overCovCond.BtsNum;
                numericUpDownOverCovRadius.Value = (decimal)overCovCond.Radius;
                numericUpDownOverCovDisMin.Value = (decimal)overCovCond.DistanceMin;
                numericUpDownOverCovDisMax.Value = (decimal)overCovCond.DistanceMax;
            }
            if (covInConfCond != null)
            {
                numericUpDownCovInConfDistanceMin.Value = (decimal)covInConfCond.DistanceMin;
                numericUpDownCovInConfAngle.Value = covInConfCond.AngleMin;
                numericUpDownCovInConfRxlevMin.Value = (decimal)covInConfCond.RxlevMin;
                numericUpDownCovInConfRxlevMax.Value = (decimal)covInConfCond.RxlevMax;
            }
            if (covlapCond != null)
            {
                numericUpDownCoverLapMainRxlev.Value = (decimal)covlapCond.RxlevMin;
                numericUpDownCoverLapDiff.Value = covlapCond.RxlevDiff;
                numericUpDownCoverLapCovNum.Value = covlapCond.CovNum;
            }
            if (fastFailureCond != null)
            {
                numericUpDownFastFailureRxlevMin.Value = (decimal)fastFailureCond.RxlevMin;
                numericUpDownFastFailureTimeSpan.Value = fastFailureCond.ITimeSpan;
                numericUpDownFastFailureRxlevMax.Value = (decimal)fastFailureCond.RxlevMax;
            }
            if (badQuaCond != null)
            {
                numericUpDownBadRxqualSinrMax.Value = (decimal)badQuaCond.SinrMax;
                numericUpDownBadRxqualQualMin.Value = badQuaCond.RxqualMin;
            }
            if (strongRxlBQCond != null)
            {
                numericUpDownStrongRxlevBadRxqualRxlevMin.Value = (decimal)strongRxlBQCond.RxlevMin;
                numericUpDownStrongRxlevBadRxqualSinrMax.Value = (decimal)strongRxlBQCond.SinrMax;
                numericUpDownStrongRxlevBadRxqualQualMin.Value = strongRxlBQCond.RxqualMin;
            }

            if (modThreeCond != null)
            {
                numericUpDownMod3MainRxlev.Value = (decimal)modThreeCond.MainRxlev;
                numericUpDownMod3Diff.Value = modThreeCond.RxlevDiff;
                numericUpDownMod3Percent.Value = modThreeCond.Percent;
            }
            if (frequentHOCond != null)
            {
                numericUpDownFrequentHOCnt.Value = frequentHOCond.HONum;
            }
            if (delayHOCond != null)
            {
                numericUpDownDelayHOMainRxlev.Value = (decimal)delayHOCond.MainRxlev;
                numericUpDownDelayHONBMax.Value = (decimal)delayHOCond.NBRxlev;
                numericUpDownDelayHOPercent.Value = delayHOCond.Percent;
            }
            if (unReasonHOCond != null)
            {
                numericUpDownUnReasonableDiff.Value = unReasonHOCond.RxlevDiff;
            }
        }
        public void RefreshData()
        {
            weakCovCond.Refresh((float)numericUpDownWeakCovMainRxlevMax.Value, (float)numericUpDownWeakCovNBMax.Value);
            overCovCond.Refresh((float)numericUpDownOverCovRxlev.Value, (int)numericUpDownOverCovBtsNum.Value,
                (float)numericUpDownOverCovRadius.Value, (double)numericUpDownOverCovDisMin.Value, (double)numericUpDownOverCovDisMax.Value);
            covInConfCond.Refresh((double)numericUpDownCovInConfDistanceMin.Value, (int)numericUpDownCovInConfAngle.Value,
                (float)numericUpDownCovInConfRxlevMin.Value, (float)numericUpDownCovInConfRxlevMax.Value);
            covlapCond.Refresh((float)numericUpDownCoverLapMainRxlev.Value, (int)numericUpDownCoverLapDiff.Value, 
                (int)numericUpDownCoverLapCovNum.Value);
            fastFailureCond.Refresh((float)numericUpDownFastFailureRxlevMin.Value, (int)numericUpDownFastFailureTimeSpan.Value,
                (float)numericUpDownFastFailureRxlevMax.Value);

            badQuaCond.Refresh((float)numericUpDownBadRxqualSinrMax.Value, (byte)numericUpDownBadRxqualQualMin.Value);
            strongRxlBQCond.Refresh((float)numericUpDownStrongRxlevBadRxqualRxlevMin.Value, (float)numericUpDownStrongRxlevBadRxqualSinrMax.Value,
                (byte)numericUpDownStrongRxlevBadRxqualQualMin.Value);

            modThreeCond.Refresh((float)numericUpDownMod3MainRxlev.Value, (int)numericUpDownMod3Diff.Value, (int)numericUpDownMod3Percent.Value);

            frequentHOCond.Refresh((int)numericUpDownFrequentHOCnt.Value);
            delayHOCond.Refresh((float)numericUpDownDelayHOMainRxlev.Value, (float)numericUpDownDelayHONBMax.Value, 
                (int)numericUpDownDelayHOPercent.Value);
            unReasonHOCond.Refresh((int)numericUpDownUnReasonableDiff.Value);

            foreach (CheckedListBoxItem item in checkedListBoxControlCauses.Items)
            {
                CCauseBase causeItem = item.Value as CCauseBase;
                causeItem.Cond.BChecked = item.CheckState == System.Windows.Forms.CheckState.Checked;
            }
            if (checkedListBoxControlCauses.Items.Count > 0)
            {
                CheckedListBoxItem checkItem = checkedListBoxControlCauses.Items[0];
                cause = checkItem.Value as CCauseBase;
            }
        }

        public CCauseBase GetCause()
        {
            return cause;
        }
    }

    public class CServiceRejectCauseCond : CCauseCond
    {
        private string dtDesc = "";
        private string cellDesc = "";

        public override bool IsValid(DTFileDataManager file, Event e, int msgIdxBegin, int msgIdxEnd)
        {
            for (int idx = msgIdxBegin; idx <= msgIdxEnd; idx++ )
            {
                DTData data = file.DTDatas[idx];
                if (data is Message)
                {
                    Message msg = data as Message;

                    if (msg.ID == (int)EMessage.Service_reject ||
                        msg.ID == (int)EMessage.Tracking_Area_Update_Reject)
                    {
                        dtDesc = msg.DateTime.ToString();
                        COutParam param;
                        if (getCellParam(file, data, out param))
                        {
                            cellDesc = param.CellName;
                        }
                        return true;
                    }
                }
            } return false;
        }

        public override string GetCauseDesc()
        {
            return string.Format("主叫在{0}收到CM Service Reject消息", dtDesc);
        }

        public override string GetCauseSuguest()
        {
            return string.Format("排查{0}小区基站故障", cellDesc);
        }

        public override string GetPhenomenon()
        {
            return "呼叫被拒绝";
        }
    }

    public class CFall2GCauseCond : CCauseCond
    {
        public override bool IsValid(DTFileDataManager file, Event e, int msgIdxBegin, int msgIdxEnd)
        {
            for (int idx = msgIdxBegin; idx <= msgIdxEnd;idx++ )
            {
                DTData data = file.DTDatas[idx];
                if (data is Message)
                {
                    Message msg = data as Message;
                    if (msg.ID == (int)ECSFBProceeding.MO_CSFB_Proceeding ||
                        msg.ID == (int)ECSFBProceeding.MT_CSFB_Proceeding)
                    {
                        return false;
                    }
                    else if (msg.ID == (int)ECSFBProceeding.MO_CSFB_回落失败 ||
                        msg.ID == (int)ECSFBProceeding.MT_CSFB_回落失败)
                    {
                        return true;
                    }
                }
            } return true;
        }

        public override string GetCauseDesc()
        {
            return "未正确回落到GSM网络";
        }

        public override string GetCauseSuguest()
        {
            return "检查终端或者是设备故障";
        }

        public override string GetPhenomenon()
        {
            return "";
        }
    }

    public class CSDBlockCauseCond : CCauseCond
    {
        private string cellDesc = "";
        private string rxlevDesc = "";

        public override bool IsValid(DTFileDataManager file, Event e, int msgIdxBegin, int msgIdxEnd)
        {
            for (int idx = msgIdxBegin; idx <= msgIdxEnd; idx++)
            {
                DTData data = file.DTDatas[idx];
                if (data is Message)
                {
                    Message msg = data as Message;

                    if (msg.ID == (int)EMessage.IMMEDIATE_ASSIGNMENT_REJECT)
                    {
                        COutParam param;
                        if (getCellParam(file, msg, out param))
                        {
                            cellDesc = param.CellName;
                            rxlevDesc = param.RxlevStr;
                        }
                        return true;
                    }
                }
            } return false;
        }

        public override string GetCauseDesc()
        {
            return string.Format("占用到{0}小区，场强为{1}dBm", cellDesc, rxlevDesc);
        }

        public override string GetCauseSuguest()
        {
            return string.Format("调整{0}小区SDCCH信道数目", cellDesc);
        }

        public override string GetPhenomenon()
        {
            return "SDCCH信道拥塞";
        }
    }

    public class CMtLocationUpdateCauseCond : CCauseCond
    {
        public override bool IsValid(DTFileDataManager file, Event e, int msgIdxBegin, int msgIdxEnd)
        {
            DTFileDataManager mtFile = getMtMoFile(file);

            if (mtFile == null) return false;

            DateTime beginTime = file.DTDatas[msgIdxBegin].DateTime;
            DateTime endTime = file.DTDatas[msgIdxEnd].DateTime;

            foreach (Event eMt in mtFile.Events)
            {
                if (eMt.DateTime >= beginTime)
                {
                    if (eMt.DateTime > endTime)
                    {
                        break;
                    }
                    else if (isLocationUpdate(eMt))
                    {
                        return true;
                    }
                }
            }

            return false;
        }

        private bool isLocationUpdate(Event e)
        {
            foreach (ELocationUpdateSuccess eLUS in Enum.GetValues(typeof(ELocationUpdateSuccess)))
            {
                if (e.ID == (int)eLUS)
                {
                    return true;
                }
            }
            return false;
        }

        public override string GetCauseDesc()
        {
            return "被叫在做位置更新";
        }

        public override string GetCauseSuguest()
        {
            return "未知";
        }

        public override string GetPhenomenon()
        {
            return "";
        }
    }

    //无out of service 事件 ,暂用 No Service消息代替  
    public class CMtOOSCauseCond : CCauseCond
    {
        private bool bMt = false;

        public override bool IsValid(DTFileDataManager file, Event e, int msgIdxBegin, int msgIdxEnd)
        {
            DTFileDataManager mtFile = file;
            if (file.MoMtFlag == (int)EMoMt.Mo)
            {
                return dealMoFile(file, msgIdxBegin, msgIdxEnd, out mtFile);
            }
            return dealMtFile(file, msgIdxBegin, mtFile);
        }

        private bool dealMtFile(DTFileDataManager file, int msgIdxBegin, DTFileDataManager mtFile)
        {
            bMt = true;
            DateTime startTime = file.DTDatas[msgIdxBegin].DateTime;
            DateTime endTime = file.DTDatas[msgIdxBegin].DateTime;

            foreach (Message msg in mtFile.Messages)
            {
                if (msg.DateTime < startTime)
                    continue;
                if (msg.DateTime > endTime)
                    break;
                else if (msg.ID == (int)EMessage.No_Service)
                {
                    return true;
                }
            }

            return false;
        }

        private bool dealMoFile(DTFileDataManager file, int msgIdxBegin, int msgIdxEnd, out DTFileDataManager mtFile)
        {
            mtFile = getMtMoFile(file);
            if (mtFile == null)
            {
                return false;
            }
            Message setupMsg = getSetupMsg(file, msgIdxBegin, msgIdxEnd);
            DateTime beginTime = file.DTDatas[msgIdxBegin].DateTime;
            if (setupMsg == null || setupMsg.DateTime < beginTime)
            {
                return false;
            }

            foreach (Message msg in mtFile.Messages)
            {
                if (msg.DateTime < beginTime)
                    continue;
                if (msg.DateTime > setupMsg.DateTime)
                    break;
                if (msg.ID == (int)EMessage.No_Service)
                    return true;
            }
            return false;
        }

        private static Message getSetupMsg(DTFileDataManager file, int msgIdxBegin, int msgIdxEnd)
        {
            Message setupMsg = null;
            for (int idx = msgIdxBegin; idx <= msgIdxEnd; idx++)
            {
                DTData data = file.DTDatas[idx];
                if (data is Message)
                {
                    Message msg = data as Message;
                    if (msg.ID == (int)EMessage.setupGSM)
                    {
                        setupMsg = msg;
                        break;
                    }
                }
            }

            return setupMsg;
        }

        public override string GetCauseDesc()
        {
            if (bMt)
            {
                return "被叫出现out of service";
            }
            return "被叫在主叫setup之前出现out of service";
        }

        public override string GetCauseSuguest()
        {
            return "未知";
        }

        public override string GetPhenomenon()
        {
            return "手机脱网";
        }
    }

    public class CMtNotReceiveCauseCond : CCauseCond
    {
        public override bool IsValid(DTFileDataManager file, Event e, int msgIdxBegin, int msgIdxEnd)
        {
            DTFileDataManager mtFile = getMtMoFile(file);

            if (mtFile == null) return true;

            DateTime beginTime = file.DTDatas[msgIdxBegin].DateTime;
            DateTime endTime = file.DTDatas[msgIdxEnd].DateTime;

            foreach (Message msg in mtFile.Messages)
            {
                if (msg.DateTime >= beginTime)
                {
                    if (msg.DateTime > endTime)
                    {
                        break;
                    }
                    else if (msg.ID == (int)EMessage.Paging)
                    {
                        return false;
                    }
                }
            }

            return true;
        }

        public override string GetCauseDesc()
        {
            return "被叫一直未收到寻呼消息";
        }

        public override string GetCauseSuguest()
        {
            return "未知";
        }

        public override string GetPhenomenon()
        {
            return "";
        }
    }

    public class CMoHangUpCauseCond : CCauseCond
    {
        public override bool IsValid(DTFileDataManager file, Event e, int msgIdxBegin, int msgIdxEnd)
        {
            return false;
        }

        public override string GetCauseDesc()
        {
            return "主叫手机主动挂断电话，setup 与connect间出现voice hangup事件，而网络没有主动释放被叫，被叫还在走流程导致未接通";
        }

        public override string GetCauseSuguest()
        {
            return "未知";
        }

        public override string GetPhenomenon()
        {
            return "";
        }
    }

    public class CMoLocationUpdateCauseCond : CCauseCond
    {
        public override bool IsValid(DTFileDataManager file, Event e, int msgIdxBegin, int msgIdxEnd)
        {
            DTFileDataManager mtFile = getMtMoFile(file);

            if (mtFile == null) return false;

            DateTime beginTime = file.DTDatas[msgIdxBegin].DateTime;
            DateTime endTime = file.DTDatas[msgIdxEnd].DateTime;

            foreach (Event eMt in mtFile.Events)
            {
                if (eMt.DateTime >= beginTime)
                {
                    if (eMt.DateTime > endTime)
                    {
                        break;
                    }
                    else if (isLocationUpdate(eMt))
                    {
                        return true;
                    }
                }
            }

            return false;
        }

        private bool isLocationUpdate(Event e)
        {
            foreach (ELocationUpdateSuccess eLUS in Enum.GetValues(typeof(ELocationUpdateSuccess)))
            {
                if (e.ID == (int)eLUS)
                {
                    return true;
                }
            }
            return false;
        }

        public override string GetCauseDesc()
        {
            return "主叫在此阶段位置更新";
        }

        public override string GetCauseSuguest()
        {
            return "未知";
        }

        public override string GetPhenomenon()
        {
            return "";
        }
    }

    public class CWeakCovCauseCond : CCauseCond
    {
        public float MainRxlevMax { get; set; } = -100;
        public float NBRxlevMax { get; set; } = -105;
        public override Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["BChecked"] = BChecked;
                param["MainRxlevMax"] = MainRxlevMax;
                param["NBRxlevMax"] = NBRxlevMax;
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                if (param.ContainsKey("BChecked"))
                {
                    BChecked = (bool)param["BChecked"];
                }
                if (param.ContainsKey("MainRxlevMax"))
                {
                    MainRxlevMax = (float)param["MainRxlevMax"];
                }
                if (param.ContainsKey("NBRxlevMax"))
                {
                    NBRxlevMax = (float)param["NBRxlevMax"];
                }
            }
        }
        public void Refresh(float mainRxlev, float nbRxlev)
        {
            this.MainRxlevMax = mainRxlev;
            this.NBRxlevMax = nbRxlev;
        }

        public override bool IsValid(DTFileDataManager file, Event e, int msgIdxBegin, int msgIdxEnd)
        {
            List<LTETestPointDetail> tpLst = getTestPoint(file, e);
            if (tpLst.Count > 0)
            {
                return anaTpList(tpLst);
            }
            else
            {
                List<LTEFddTestPoint> tpLstFdd = getTestPointFdd(file, e);
                return anaTpList(tpLstFdd);
            }
        }

        private bool anaTpList(List<LTETestPointDetail> tpLst)
        {
            int num = 0;
            float _rsrp = 0, _nRsrp = 0;
            foreach (LTETestPointDetail tp in tpLst)
            {
                object rsrp = MasterCom.RAMS.ZTFunc.AreaArchiveManage.TestPointFactory.CreateTestPointDeal(tp).GetRxlev(tp);
                object nRsrp = MasterCom.RAMS.ZTFunc.AreaArchiveManage.TestPointFactory.CreateTestPointDeal(tp).GetNbRxlev(tp, 0);

                if (rsrp == null || nRsrp == null) continue;

                _rsrp += Convert.ToSingle(rsrp);
                _nRsrp += Convert.ToSingle(nRsrp);
                num++;
            }
            return num > 0 && _rsrp / num < MainRxlevMax && _nRsrp / num < NBRxlevMax;
        }
        private bool anaTpList(List<LTEFddTestPoint> tpLstFdd)
        {
            int num = 0;
            float _rsrp = 0, _nRsrp = 0;
            foreach (LTEFddTestPoint tp in tpLstFdd)
            {
                object rsrp = MasterCom.RAMS.ZTFunc.AreaArchiveManage.TestPointFactory.CreateTestPointDeal(tp).GetRxlev(tp);
                object nRsrp = MasterCom.RAMS.ZTFunc.AreaArchiveManage.TestPointFactory.CreateTestPointDeal(tp).GetNbRxlev(tp, 0);

                if (rsrp == null || nRsrp == null) continue;

                _rsrp += Convert.ToSingle(rsrp);
                _nRsrp += Convert.ToSingle(nRsrp);
                num++;
            }
            return num > 0 && _rsrp / num < MainRxlevMax && _nRsrp / num < NBRxlevMax;
        }

        public override string GetCauseDesc()
        {
            return string.Format("场强<{0}dBm，最强邻区场强<{1}dBm（回看5秒均值）", MainRxlevMax, NBRxlevMax);
        }

        public override string GetCauseSuguest()
        {
            return "增加或者调整覆盖";
        }

        public override string GetPhenomenon()
        {
            return "主服小区与邻区电平均较低";
        }
    }

    public class COverCoverCauseCond : CCauseCond
    {
        public float MainRxlevMin { get; set; } = -90;
        public int BtsNum { get; set; } = 3;
        public float Radius { get; set; } = 1.6f;
        public double DistanceMin { get; set; } = 1000;
        public double DistanceMax { get; set; } = 3000;

        public override Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["BChecked"] = BChecked;
                param["MainRxlevMin"] = MainRxlevMin;
                param["BtsNum"] = BtsNum;
                param["Radius"] = Radius;
                param["DistanceMin"] = DistanceMin;
                param["DistanceMax"] = DistanceMax;
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                if (param.ContainsKey("BChecked"))
                {
                    BChecked = (bool)param["BChecked"];
                }
                if (param.ContainsKey("MainRxlevMin"))
                {
                    MainRxlevMin = (float)param["MainRxlevMin"];
                }
                if (param.ContainsKey("BtsNum"))
                {
                    BtsNum = (int)param["BtsNum"];
                }
                if (param.ContainsKey("Radius"))
                {
                    Radius = (float)param["Radius"];
                }
                if (param.ContainsKey("DistanceMin"))
                {
                    DistanceMin = (double)param["DistanceMin"];
                }
                if (param.ContainsKey("DistanceMax"))
                {
                    DistanceMax = (double)param["DistanceMax"];
                }
            }
        }

        private string cellDesc = "";

        public void Refresh(float mainRxlev, int btsN, float radi, double distanceMi, double distanceMa)
        {
            this.MainRxlevMin = mainRxlev;
            this.BtsNum = btsN;
            this.Radius = radi;
            this.DistanceMin = distanceMi;
            this.DistanceMax = distanceMa;
        }

        public override bool IsValid(DTFileDataManager file, Event e, int msgIdxBegin, int msgIdxEnd)
        {
            List<LTETestPointDetail> tpLst = getTestPoint(file, e);
            ICell cell = e.GetSrcCell();
            if (cell == null) return false;
            cellDesc = cell.Name;
            double distance = 0;
            if(cell is Cell)
                distance= MasterCom.ES.Data.CfgDataProvider.CalculateRadius(cell as Cell, BtsNum, false) * Radius;
            else if (cell is TDCell)
                distance = MasterCom.ES.Data.CfgDataProvider.CalculateRadius(cell as TDCell, BtsNum) * Radius;
            else if (cell is LTECell)
                distance = MasterCom.ES.Data.CfgDataProvider.CalculateRadius(cell as LTECell, BtsNum) * Radius;
            else if(cell is WCell)
                distance = MasterCom.ES.Data.CfgDataProvider.CalculateRadius(cell as WCell, BtsNum) * Radius;
            if (tpLst.Count > 0)
            {
                return anaTpLst(tpLst) && distance > DistanceMin && distance < DistanceMax;
            }
            else
            {
                List<LTEFddTestPoint> tpLstFdd = getTestPointFdd(file, e);
                return anaTpLst(tpLstFdd) && distance > DistanceMin && distance < DistanceMax;
            }
        }

        private bool anaTpLst(List<LTETestPointDetail> tpLst)
        {
            int num = 0;
            float _rsrp = 0;
            foreach (LTETestPointDetail tp in tpLst)
            {
                object rsrp = MasterCom.RAMS.ZTFunc.AreaArchiveManage.TestPointFactory.CreateTestPointDeal(tp).GetRxlev(tp);

                if (rsrp == null) continue;

                _rsrp += Convert.ToSingle(rsrp);
                num++;
            }
            return num > 0 && _rsrp / num > MainRxlevMin;
        }
        private bool anaTpLst(List<LTEFddTestPoint> tpLstFdd)
        {
            int num = 0;
            float _rsrp = 0;
            foreach (LTEFddTestPoint tp in tpLstFdd)
            {
                object rsrp = MasterCom.RAMS.ZTFunc.AreaArchiveManage.TestPointFactory.CreateTestPointDeal(tp).GetRxlev(tp);

                if (rsrp == null) continue;

                _rsrp += Convert.ToSingle(rsrp);
                num++;
            }
            return num > 0 && _rsrp / num > MainRxlevMin;
        }

        public override string GetCauseDesc()
        {
            return string.Format("采样点场强>{0}dBm，理想覆盖参考基站数={1}，理想覆盖半径系数={2}，{3}m<过覆盖距离<{4}m(判断当前占用小区)",
                MainRxlevMin, BtsNum, Radius, DistanceMin, DistanceMax);
        }

        public override string GetCauseSuguest()
        {
            return string.Format("控制{0}小区覆盖", cellDesc);
        }

        public override string GetPhenomenon()
        {
            return string.Format("覆盖距离超过理想覆盖系数{0}倍", Radius);
        }
    }

    public class CCovInConformityCauseCond : CCauseCond
    {
        public double DistanceMin { get; set; } = 200;
        public int AngleMin { get; set; } = 60;
        public float RxlevMin { get; set; } = -110;
        public float RxlevMax { get; set; } = -90;
        public override Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["BChecked"] = BChecked;
                param["DistanceMin"] = DistanceMin;
                param["AngleMin"] = AngleMin;
                param["RxlevMin"] = RxlevMin;
                param["RxlevMax"] = RxlevMax;
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                if (param.ContainsKey("BChecked"))
                {
                    BChecked = (bool)param["BChecked"];
                }
                if (param.ContainsKey("DistanceMin"))
                {
                    DistanceMin = (double)param["DistanceMin"];
                }
                if (param.ContainsKey("AngleMin"))
                {
                    AngleMin = (int)param["AngleMin"];
                }
                if (param.ContainsKey("RxlevMin"))
                {
                    RxlevMin = (float)param["RxlevMin"];
                }
                if (param.ContainsKey("RxlevMax"))
                {
                    RxlevMax = (float)param["RxlevMax"];
                }
            }
        }

        private string cellDesc = "";

        public void Refresh(double distance, int angle, float rxlev, float maxRxlev)
        {
            this.DistanceMin = distance;
            this.AngleMin = angle;
            this.RxlevMin = rxlev;
            this.RxlevMax = maxRxlev;
        }

        public override bool IsValid(DTFileDataManager file, Event e, int msgIdxBegin, int msgIdxEnd)
        {
            ICell cell = e.GetSrcCell();
            if(cell == null) return false;
            cellDesc = cell.Name;
            List<LTETestPointDetail> tpLst = getTestPoint(file, e);

            double distance = MasterCom.Util.MathFuncs.GetDistance(e.Longitude, e.Latitude, cell.Longitude, cell.Latitude);

            bool angleCheck = true;
            if(cell is Cell)
                angleCheck = !MasterCom.Util.MathFuncs.JudgePoint(e.Latitude, e.Latitude, cell.Longitude, cell.Latitude,
                (cell as Cell).Direction, AngleMin);
            else if(cell is TDCell)
                angleCheck = !MasterCom.Util.MathFuncs.JudgePoint(e.Latitude, e.Latitude, cell.Longitude, cell.Latitude,
                (cell as TDCell).Direction, AngleMin);
            else if (cell is LTECell)
                angleCheck = !MasterCom.Util.MathFuncs.JudgePoint(e.Latitude, e.Latitude, cell.Longitude, cell.Latitude,
                (cell as LTECell).Direction, AngleMin);
            else if(cell is WCell)
                angleCheck = !MasterCom.Util.MathFuncs.JudgePoint(e.Latitude, e.Latitude, cell.Longitude, cell.Latitude,
                (cell as WCell).Direction, AngleMin);
            if (tpLst.Count > 0)
            {
                return distance > DistanceMin && angleCheck && anaTpLst(tpLst);
            }
            else
            {
                List<LTEFddTestPoint> tpLstFdd = getTestPointFdd(file, e);
                return distance > DistanceMin && angleCheck && anaTpLst(tpLstFdd);
            }
        }

        private bool anaTpLst(List<LTETestPointDetail> tpLst)
        {
            int num = 0;
            float _rsrp = 0;
            foreach (LTETestPointDetail tp in tpLst)
            {
                object rsrp = MasterCom.RAMS.ZTFunc.AreaArchiveManage.TestPointFactory.CreateTestPointDeal(tp).GetRxlev(tp);
                if (rsrp == null) continue;

                _rsrp += Convert.ToSingle(rsrp);
                num++;
            }
            return num > 0 && _rsrp / num > RxlevMin && _rsrp / num < RxlevMax;
        }
        private bool anaTpLst(List<LTEFddTestPoint> tpLstFdd)
        {
            int num = 0;
            float _rsrp = 0;
            foreach (LTEFddTestPoint tp in tpLstFdd)
            {
                object rsrp = MasterCom.RAMS.ZTFunc.AreaArchiveManage.TestPointFactory.CreateTestPointDeal(tp).GetRxlev(tp);
                if (rsrp == null) continue;

                _rsrp += Convert.ToSingle(rsrp);
                num++;
            }
            return num > 0 && _rsrp / num > RxlevMin && _rsrp / num < RxlevMax;
        }

        public override string GetCauseDesc()
        {
            return string.Format("与主服务小区{0}距离>{1}m,夹角>{2}度，场强>{3}dBm",
                cellDesc, DistanceMin, AngleMin, RxlevMin);
        }

        public override string GetCauseSuguest()
        {
            return string.Format("控制{0}小区覆盖并完善邻区关系", cellDesc);
        }

        public override string GetPhenomenon()
        {
            return "采样点距离主服小区过远且不在其正向覆盖内";
        }
    }

    public class CCoverLapCauseCond : CCauseCond
    {
        public float RxlevMin { get; set; } = -100;
        public int RxlevDiff { get; set; } = 6;
        public int CovNum { get; set; } = 3;
        public override Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["BChecked"] = BChecked;
                param["RxlevMin"] = RxlevMin;
                param["RxlevDiff"] = RxlevDiff;
                param["CovNum"] = CovNum;
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                if (param.ContainsKey("BChecked"))
                {
                    BChecked = (bool)param["BChecked"];
                }
                if (param.ContainsKey("RxlevMin"))
                {
                    RxlevMin = (float)param["RxlevMin"];
                }
                if (param.ContainsKey("RxlevDiff"))
                {
                    RxlevDiff = (int)param["RxlevDiff"];
                }
                if (param.ContainsKey("CovNum"))
                {
                    CovNum = (int)param["CovNum"];
                }
            }
        }
        public void Refresh(float rxlev, int rxlevDif, int cov)
        {
            this.RxlevMin = rxlev;
            this.RxlevDiff = rxlevDif;
            this.CovNum = cov;
        }

        public override bool IsValid(DTFileDataManager file, Event e, int msgIdxBegin, int msgIdxEnd)
        {
            if (getTestPoint(file, e).Count > 0)
            {
                return anaTpLst(getTestPoint(file, e));
            }
            else
            {
                return anaTpLst(getTestPointFdd(file, e));
            }
        }

        private bool anaTpLst(List<LTETestPointDetail> tpLst)
        {
            foreach (LTETestPointDetail tp in tpLst)
            {
                int cov = 1;
                object rsrp = MasterCom.RAMS.ZTFunc.AreaArchiveManage.TestPointFactory.CreateTestPointDeal(tp).GetRxlev(tp);

                if (rsrp == null || Convert.ToSingle(rsrp) <= RxlevMin)
                    return false;
                for (int idx = 0; idx < 6; idx++ )
                {
                    object nrsrp = MasterCom.RAMS.ZTFunc.AreaArchiveManage.TestPointFactory.CreateTestPointDeal(tp).GetNbRxlev(tp, idx);

                    if (nrsrp != null && Math.Abs(Convert.ToSingle(rsrp) - Convert.ToSingle(nrsrp)) < RxlevDiff)
                        cov++;
                }
                if (cov < CovNum)
                    return false;
            }
            return tpLst.Count > 0;
        }
        private bool anaTpLst(List<LTEFddTestPoint> tpLstFdd)
        {
            foreach (LTEFddTestPoint tp in tpLstFdd)
            {
                int cov = 1;
                object rsrp = MasterCom.RAMS.ZTFunc.AreaArchiveManage.TestPointFactory.CreateTestPointDeal(tp).GetRxlev(tp);

                if (rsrp == null || Convert.ToSingle(rsrp) <= RxlevMin)
                    return false;
                for (int idx = 0; idx < 6; idx++)
                {
                    object nrsrp = MasterCom.RAMS.ZTFunc.AreaArchiveManage.TestPointFactory.CreateTestPointDeal(tp).GetNbRxlev(tp, idx);

                    if (nrsrp != null && Math.Abs(Convert.ToSingle(rsrp) - Convert.ToSingle(nrsrp)) < RxlevDiff)
                        cov++;
                }
                if (cov < CovNum)
                    return false;
            }
            return tpLstFdd.Count > 0;
        }

        public override string GetCauseDesc()
        {
            return string.Format("主服小区场强大于{0}dBm，邻区中信号场强与主服信号差异<{1}dB，（回看5秒采样点重叠覆盖度均值大于等于3）",
                RxlevMin, RxlevDiff);
        }

        public override string GetCauseSuguest()
        {
            return "控制覆盖";
        }

        public override string GetPhenomenon()
        {
            return string.Format("邻区场强与主服信号（大于{0}dBm）差异小于{1}dB", RxlevMin, RxlevDiff);
        }
    }

    public class CFastFailureCauseCond : CCauseCond
    {
        public float RxlevMin { get; set; } = -80;
        public int ITimeSpan { get; set; } = 3;
        public float RxlevMax { get; set; } = -95;
        public override Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["BChecked"] = BChecked;
                param["RxlevMin"] = RxlevMin;
                param["ITimeSpan"] = ITimeSpan;
                param["RxlevMax"] = RxlevMax;
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                if (param.ContainsKey("BChecked"))
                {
                    BChecked = (bool)param["BChecked"];
                }
                if (param.ContainsKey("RxlevMin"))
                {
                    RxlevMin = (float)param["RxlevMin"];
                }
                if (param.ContainsKey("ITimeSpan"))
                {
                    ITimeSpan = (int)param["ITimeSpan"];
                }
                if (param.ContainsKey("RxlevMax"))
                {
                    RxlevMax = (float)param["RxlevMax"];
                }
            }
        }

        private string cellDesc = "";

        public void Refresh(float rxlevMi, int itspan, float rxlevMa)
        {
            this.RxlevMin = rxlevMi;
            this.ITimeSpan = itspan;
            this.RxlevMax = rxlevMa;
        }

        public override bool IsValid(DTFileDataManager file, Event e, int msgIdxBegin, int msgIdxEnd)
        {
            if (getTestPoint(file, e).Count > 0)
            {
                return anaTpLst(getTestPoint(file, e));
            }
            else
            {
                return anaTpLst(getTestPointFdd(file, e));
            }
        }

        private bool anaTpLst(List<LTETestPointDetail> tpLst)
        {
            for (int ifront = 0; ifront < tpLst.Count - 1; ifront++ )
            {
                object rsrp = MasterCom.RAMS.ZTFunc.AreaArchiveManage.TestPointFactory.CreateTestPointDeal(tpLst[ifront]).GetRxlev(tpLst[ifront]);
                if (rsrp == null || Convert.ToSingle(rsrp) <= RxlevMin) continue;

                for (int ibehind = ifront + 1; ibehind < tpLst.Count; ibehind++)
                {
                    object bhRsrp = MasterCom.RAMS.ZTFunc.AreaArchiveManage.TestPointFactory.CreateTestPointDeal(tpLst[ibehind]).GetRxlev(tpLst[ibehind]);
                    if(bhRsrp == null || Convert.ToSingle(bhRsrp) >= RxlevMax)
                    {
                        continue;
                    }
                    if (tpLst[ibehind].DateTime.Subtract(tpLst[ifront].DateTime).TotalSeconds > ITimeSpan)
                    {
                        break;
                    }
                    getCellName(tpLst[ifront]);
                    return true;
                }
            }
            return false;
        }
        private bool anaTpLst(List<LTEFddTestPoint> tpLst)
        {
            for (int ifront = 0; ifront < tpLst.Count - 1; ifront++)
            {
                object rsrp = MasterCom.RAMS.ZTFunc.AreaArchiveManage.TestPointFactory.CreateTestPointDeal(tpLst[ifront]).GetRxlev(tpLst[ifront]);
                if (rsrp == null || Convert.ToSingle(rsrp) <= RxlevMin) continue;

                for (int ibehind = ifront + 1; ibehind < tpLst.Count; ibehind++)
                {
                    object bhRsrp = MasterCom.RAMS.ZTFunc.AreaArchiveManage.TestPointFactory.CreateTestPointDeal(tpLst[ibehind]).GetRxlev(tpLst[ibehind]);
                    if (bhRsrp == null || Convert.ToSingle(bhRsrp) >= RxlevMax)
                    {
                        continue;
                    }
                    if (tpLst[ibehind].DateTime.Subtract(tpLst[ifront].DateTime).TotalSeconds > ITimeSpan)
                    {
                        break;
                    }
                    getCellName(tpLst[ifront]);
                    return true;
                }
            }
            return false;
        }

        private void getCellName(LTETestPointDetail tp)
        {
            ICell cell = tp.GetMainCell();
            if (cell != null)
                cellDesc = cell.Name;
            else
            {
                int? lac = (int?)tp["lte_gsm_SC_LAC"];
                int? ci = (int?)tp["lte_gsm_SC_CI"];
                if (lac != null && ci != null)
                {
                    cellDesc = string.Format("{0}_{1}", lac, ci);
                    return;
                }

                lac = (int?)tp["lte_td_SC_LAC"];
                ci = (int?)tp["lte_td_SC_CellID"];
                if (lac != null && ci != null)
                {
                    cellDesc = string.Format("{0}_{1}", lac, ci);
                    return;
                }

                lac = (int?)(ushort?)tp["lte_TAC"];
                ci = (int?)tp["lte_ECI"];
                if (lac != null && ci != null)
                {
                    cellDesc = string.Format("{0}_{1}", lac, ci);
                }
            }
        }
        private void getCellName(LTEFddTestPoint tp)
        {
            ICell cell = tp.GetMainCell();
            if (cell != null)
                cellDesc = cell.Name;
            else
            {
                int? lac = (int?)tp["lte_fdd_gsm_SC_LAC"];
                int? ci = (int?)tp["lte_fdd_gsm_SC_CI"];
                if (lac != null && ci != null)
                {
                    cellDesc = string.Format("{0}_{1}", lac, ci);
                    return;
                }

                lac = (int?)tp["lte_fdd_wcdma_SysLAI"];
                ci = (int?)tp["lte_fdd_wcdma_SysCellID"];
                if (lac != null && ci != null)
                {
                    cellDesc = string.Format("{0}_{1}", lac, ci);
                    return;
                }

                lac = (int?)(ushort?)tp["lte_fdd_TAC"];
                ci = (int?)tp["lte_fdd_ECI"];
                if (lac != null && ci != null)
                {
                    cellDesc = string.Format("{0}_{1}", lac, ci);
                }
            }
        }

        public override string GetCauseDesc()
        {
            return string.Format("回看5秒，看发生问题的小区是否存在场强快衰，场强从大于{0}dBm，3秒钟以内衰减到小于{1}dBm",
                RxlevMin, RxlevMax);
        }

        public override string GetCauseSuguest()
        {
            return string.Format("检查{0}小区是否正常", cellDesc);
        }

        public override string GetPhenomenon()
        {
            return "采样点RSRP三秒钟内衰减15dB以上";
        }
    }

    public class CBadQuaCauseCond : CCauseCond
    {
        public float SinrMax { get; set; } = 3;
        public byte RxqualMin { get; set; } = 4;
        public override Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["BChecked"] = BChecked;
                param["SinrMax"] = SinrMax;
                param["RxqualMin"] = RxqualMin;
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                if (param.ContainsKey("BChecked"))
                {
                    BChecked = (bool)param["BChecked"];
                }
                if (param.ContainsKey("SinrMax"))
                {
                    SinrMax = (float)param["SinrMax"];
                }
                if (param.ContainsKey("RxqualMin"))
                {
                    RxqualMin = (byte)(int)param["RxqualMin"];
                }
            }
        }
        public void Refresh(float sinr, byte rxqual)
        {
            this.SinrMax = sinr;
            this.RxqualMin = rxqual;
        }

        public override bool IsValid(DTFileDataManager file, Event e, int msgIdxBegin, int msgIdxEnd)
        {
            if (getTestPoint(file, e).Count > 0)
            {
                return anaTpLst(getTestPoint(file, e));
            }
            else
            {
                return anaTpLst(getTestPointFdd(file, e));
            }
        }

        private bool anaTpLst(List<LTETestPointDetail> tpLst)
        {
            int num = 0;
            float _sinr = 0;
            int _rxqual = 0;
            foreach (LTETestPointDetail tp in tpLst)
            {
                float? sinr = (float?)tp["lte_SINR"];
                byte? rxqual = (byte?)tp["lte_gsm_DM_RxQualSub"];

                if (sinr == null || rxqual == null) continue;

                _sinr += (float)sinr;
                _rxqual += (int)rxqual;
                num++;
            }
            return num > 0 && _sinr / num < SinrMax && _rxqual / num > RxqualMin;
        }
        private bool anaTpLst(List<LTEFddTestPoint> tpLstFdd)
        {
            int num = 0;
            float _sinr = 0;
            int _rxqual = 0;
            foreach (LTEFddTestPoint tp in tpLstFdd)
            {
                float? sinr = (float?)tp["lte_fdd_SINR"];
                float? rxqual = (float?)tp["lte_fdd_wcdma_TotalEc_Io"];

                if (sinr == null || rxqual == null) continue;

                _sinr += (float)sinr;
                _rxqual += (int)rxqual;
                num++;
            }
            return num > 0 && _sinr / num < SinrMax && _rxqual / num > RxqualMin;
        }

        public override string GetCauseDesc()
        {
            return string.Format("SINR<{0}dB，RxQuality>{1}（回看5秒均值）", SinrMax, RxqualMin);
        }

        public override string GetCauseSuguest()
        {
            return "具体分析SINR质差原因";
        }

        public override string GetPhenomenon()
        {
            return string.Format("采样点SINR < {0}dB 或 RxQuality > {1}", SinrMax, RxqualMin);
        }
    }

    public class CStrongRxlBadQuaCauseCond : CCauseCond
    {
        public float RxlevMin { get; set; } = -85;
        public float SinrMax { get; set; } = 5;
        public byte RxqualMin { get; set; } = 4;
        public override Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["BChecked"] = BChecked;
                param["RxlevMin"] = RxlevMin;
                param["SinrMax"] = SinrMax;
                param["RxqualMin"] = RxqualMin;
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                if (param.ContainsKey("BChecked"))
                {
                    BChecked = (bool)param["BChecked"];
                }
                if (param.ContainsKey("RxlevMin"))
                {
                    RxlevMin = (float)param["RxlevMin"];
                }
                if (param.ContainsKey("SinrMax"))
                {
                    SinrMax = (float)param["SinrMax"];
                }
                if (param.ContainsKey("RxqualMin"))
                {
                    RxqualMin = (byte)(int)param["RxqualMin"];
                }
            }
        }
        public void Refresh(float rxlev, float sinr, byte rxqual)
        {
            this.RxlevMin = rxlev;
            this.SinrMax = sinr;
            this.RxqualMin = rxqual;
        }

        public override bool IsValid(DTFileDataManager file, Event e, int msgIdxBegin, int msgIdxEnd)
        {
            if (getTestPoint(file, e).Count > 0)
            {
                return anaTpLst(getTestPoint(file, e));
            }
            else
            {
                return anaTpLst(getTestPointFdd(file, e));
            }
        }

        private bool anaTpLst(List<LTETestPointDetail> tpLst)
        {
            int num = 0;
            float _rsrp = 0, _sinr = 0;
            int _rxqual = 0;
            foreach (LTETestPointDetail tp in tpLst)
            {
                object rsrp = MasterCom.RAMS.ZTFunc.AreaArchiveManage.TestPointFactory.CreateTestPointDeal(tp).GetRxlev(tp);
                float? sinr = (float?)tp["lte_SINR"];
                byte? rxqual = (byte?)tp["lte_gsm_DM_RxQualSub"];

                if (rsrp == null || sinr == null || rxqual == null) continue;

                _rsrp += Convert.ToSingle(rsrp);
                _sinr += (float)sinr;
                _rxqual += (int)rxqual;
                num++;
            }
            return num > 0 && _rsrp / num >RxlevMin && _sinr / num < SinrMax && _rxqual / num > RxqualMin;
        }
        private bool anaTpLst(List<LTEFddTestPoint> tpLstFdd)
        {
            int num = 0;
            float _rsrp = 0, _sinr = 0;
            int _rxqual = 0;
            foreach (LTEFddTestPoint tp in tpLstFdd)
            {
                object rsrp = MasterCom.RAMS.ZTFunc.AreaArchiveManage.TestPointFactory.CreateTestPointDeal(tp).GetRxlev(tp);
                float? sinr = (float?)tp["lte_fdd_SINR"];
                float? rxqual = (float?)tp["lte_fdd_wcdma_TotalEc_Io"];

                if (rsrp == null || sinr == null || rxqual == null) continue;

                _rsrp += Convert.ToSingle(rsrp);
                _sinr += (float)sinr;
                _rxqual += (int)rxqual;
                num++;
            }
            return num > 0 && _rsrp / num > RxlevMin && _sinr / num < SinrMax && _rxqual / num > RxqualMin;
        }

        public override string GetCauseDesc()
        {
            return string.Format("场强>{0}dBm,SINR<{1}dB，RxQuality>{2}，（回看5秒均值）",
                RxlevMin, SinrMax, RxqualMin);
        }

        public override string GetCauseSuguest()
        {
            return "查看是否有外部干扰或者是硬件故障";
        }

        public override string GetPhenomenon()
        {
            return string.Format("采样点场强 > {0}dBm,SINR < {1} 或 RxQuality > {2}", RxlevMin, SinrMax, RxqualMin);
        }
    }

    public class CModThreeCauseCond : CCauseCond
    {
        public float MainRxlev { get; set; } = -95;
        public int RxlevDiff { get; set; } = 6;
        public int Percent { get; set; } = 60;
        public override Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["BChecked"] = BChecked;
                param["MainRxlev"] = MainRxlev;
                param["RxlevDiff"] = RxlevDiff;
                param["Percent"] = Percent;
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                if (param.ContainsKey("BChecked"))
                {
                    BChecked = (bool)param["BChecked"];
                }
                if (param.ContainsKey("MainRxlev"))
                {
                    MainRxlev = (float)param["MainRxlev"];
                }
                if (param.ContainsKey("RxlevDiff"))
                {
                    RxlevDiff = (int)param["RxlevDiff"];
                }
                if (param.ContainsKey("Percent"))
                {
                    Percent = (int)param["Percent"];
                }
            }
        }
        public void Refresh(float rxlev, int rxlevDif, int perc)
        {
            this.MainRxlev = rxlev;
            this.RxlevDiff = rxlevDif;
            this.Percent = perc;
        }

        public override bool IsValid(DTFileDataManager file, Event e, int msgIdxBegin, int msgIdxEnd)
        {
            if (getTestPoint(file, e).Count > 0)
            {
                return anaTpLst(getTestPoint(file, e));
            }
            else
            {
                return anaTpLst(getTestPointFdd(file, e));
            }
        }

        private bool anaTpLst(List<LTETestPointDetail> tpLst)
        {
            int num = 0;
            foreach (LTETestPointDetail tp in tpLst)
            {
                num = getValidNum(num, tp, "lte_PCI", "lte_NCell_PCI");
            }
            return tpLst.Count > 0 && (float)num * 100 / tpLst.Count >= Percent;
        }

        private bool anaTpLst(List<LTEFddTestPoint> tpLstFdd)
        {
            int num = 0;
            foreach (LTEFddTestPoint tp in tpLstFdd)
            {
                num = getValidNum(num, tp, "lte_fdd_PCI", "lte_fdd_NCell_PCI");
            }
            return tpLstFdd.Count > 0 && (float)num * 100 / tpLstFdd.Count >= Percent;
        }

        private int getValidNum(int num, TestPoint tp, string pciName, string nCellPciName)
        {
            object rsrp = MasterCom.RAMS.ZTFunc.AreaArchiveManage.TestPointFactory.CreateTestPointDeal(tp).GetRxlev(tp);
            short? pci = (short?)tp[pciName];

            if (rsrp != null && pci != null && Convert.ToSingle(rsrp) > MainRxlev)
            {
                for (int idx = 0; idx < 6; idx++)
                {
                    object nRsrp = MasterCom.RAMS.ZTFunc.AreaArchiveManage.TestPointFactory.CreateTestPointDeal(tp).GetNbRxlev(tp, idx);
                    short? npci = (short?)tp[nCellPciName, idx];
                    if (nRsrp == null || npci == null) break;
                    if (Math.Abs(Convert.ToSingle(rsrp) - Convert.ToSingle(nRsrp)) > RxlevDiff) break;
                    if (pci % 3 == npci % 3)
                    {
                        num++;
                        break;
                    }
                }
            }

            return num;
        }

        public override string GetCauseDesc()
        {
            return string.Format("回看5秒，主服小区场强大于{0}dBm，邻区和主服小区电平相差{1}db以内且存在模三干扰，{2}%点满足这个条件",
                MainRxlev, RxlevDiff, Percent);
        }

        public override string GetCauseSuguest()
        {
            return "调整PCI值";
        }

        public override string GetPhenomenon()
        {
            return "主服小区与邻区小区存在模三干扰";
        }
    }

    public class CIndoorLeakCauseCond : CCauseCond
    {
        private string cellDesc = "";
        public override bool IsValid(DTFileDataManager file, Event e, int msgIdxBegin, int msgIdxEnd)
        {
            ICell cell = e.GetSrcCell();
            if (cell == null) return false;
            cellDesc = cell.Name;
            if (cell is Cell)
                return (cell as Cell).Type == BTSType.Indoor;
            else if (cell is TDCell)
                return (cell as TDCell).Type == TDNodeBType.Indoor;
            else if (cell is LTECell)
                return (cell as LTECell).Type == LTEBTSType.Indoor;
            else if (cell is WCell)
                return (cell as WCell).Type == WNodeBType.Indoor;
            return false;
        }

        public override string GetCauseDesc()
        {
            return "看问题发生时小区是否为室分小区";
        }

        public override string GetCauseSuguest()
        {
            return string.Format("控制{0}小区室分外泄", cellDesc);
        }

        public override string GetPhenomenon()
        {
            return "采样点占用室分小区";
        }
    }

    public class CFrequentHOCauseCond : CCauseCond
    {
        public int HONum { get; set; } = 3;
        public override Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["BChecked"] = BChecked;
                param["HONum"] = HONum;
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                if (param.ContainsKey("BChecked"))
                {
                    BChecked = (bool)param["BChecked"];
                }
                if (param.ContainsKey("HONum"))
                {
                    HONum = (int)param["HONum"];
                }
            }
        }
        public void Refresh(int hon)
        {
            this.HONum = hon;
        }

        public override bool IsValid(DTFileDataManager file, Event e, int msgIdxBegin, int msgIdxEnd)
        {
            return anaHandover(file, e);
        }

        private bool anaHandover(DTFileDataManager file, Event e)
        {
            int num = 0;
            int idxE = file.DTDatas.IndexOf(e);
            for (int idx = idxE - 1; idx >= 0; idx-- )
            {
                DTData data = file.DTDatas[idx];

                if (e.DateTime.Subtract(data.DateTime).TotalSeconds > 10) 
                    break;

                if (data is Event)
                {
                    Event eData = data as Event;
                    if (isHandOverSuccess(eData))
                        num++;
                }
            }
            return num >= HONum;
        }

        private bool isHandOverSuccess(Event e)
        {
            foreach (EHandOverSuccess ehos in Enum.GetValues(typeof(EHandOverSuccess)))
            {
                if (e.ID == (int)ehos)
                {
                    return true;
                }
            }
            return false;
        }

        public override string GetCauseDesc()
        {
            return string.Format("回看10秒，切换次数大于等于{0}次", HONum);
        }

        public override string GetCauseSuguest()
        {
            return "调整覆盖，控制切换";
        }

        public override string GetPhenomenon()
        {
            return string.Format("10秒内发生{0}次以上切换", HONum);
        }
    }

    public class CDelayHOCauseCond : CCauseCond
    {
        private string cellMain = "";
        private string cellNB = "";
        
        public float MainRxlev { get; set; } = -90;
        public float NBRxlev { get; set; } = -85;
        public int Percent { get; set; } = 60;
        public override Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["BChecked"] = BChecked;
                param["MainRxlev"] = MainRxlev;
                param["NBRxlev"] = NBRxlev;
                param["Percent"] = Percent;
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                if (param.ContainsKey("BChecked"))
                {
                    BChecked = (bool)param["BChecked"];
                }
                if (param.ContainsKey("MainRxlev"))
                {
                    MainRxlev = (float)param["MainRxlev"];
                }
                if (param.ContainsKey("NBRxlev"))
                {
                    NBRxlev = (float)param["NBRxlev"];
                }
                if (param.ContainsKey("Percent"))
                {
                    Percent = (int)param["Percent"];
                }
            }
        }
        public void Refresh(float rxlev, float nbrxlev, int perc)
        {
            this.MainRxlev = rxlev;
            this.NBRxlev = nbrxlev;
            this.Percent = perc;
        }

        public override bool IsValid(DTFileDataManager file, Event e, int msgIdxBegin, int msgIdxEnd)
        {
            if (getTestPoint(file, e).Count > 0)
            {
                return anaTpLst(getTestPoint(file, e));
            }
            else
            {
                return anaTpLst(getTestPointFdd(file, e));
            }
        }

        private bool anaTpLst(List<LTETestPointDetail> tpLst)
        {
            int num = 0;
            LTETestPointDetail lastPnt = null;
            foreach (LTETestPointDetail tp in tpLst)
            {
                object rsrp = MasterCom.RAMS.ZTFunc.AreaArchiveManage.TestPointFactory.CreateTestPointDeal(tp).GetRxlev(tp);
                object nrsrp = MasterCom.RAMS.ZTFunc.AreaArchiveManage.TestPointFactory.CreateTestPointDeal(tp).GetNbRxlev(tp, 0);

                if (rsrp == null || nrsrp == null) continue;

                if (Convert.ToSingle(rsrp) < MainRxlev && Convert.ToSingle(nrsrp) > NBRxlev)
                {
                    lastPnt = tp;
                    num++;
                }
            }
            bool brt = tpLst.Count > 0 && (float)num * 100 / tpLst.Count >= Percent;
            if (brt && lastPnt != null)
            {
                int? lac = null, ci = null, earfcn = null;
                short? pci = null;
                ICell cell = lastPnt.GetMainCell();
                if(cell == null)
                {
                    lac = (int?)lastPnt["lte_SCell_LAC"];
                    ci = (int?)lastPnt["lte_SCell_CI"];

                    lac = getValidData(lac, (int?)lastPnt["lte_td_SC_LAC"]);
                    ci = getValidData(ci, (int?)lastPnt["lte_td_SC_CellID"]);

                    lac = getValidData(lac, (int?)lastPnt["lte_gsm_SC_LAC"]);
                    ci = getValidData(ci, (int?)lastPnt["lte_gsm_SC_CI"]);
                }
                cellMain = getCellName(cell, string.Format("{0}_{1}", lac, ci));

                cell = lastPnt.GetNBCell(0);
                if (cell == null)
                {
                    earfcn = (int?)lastPnt["lte_NCell_EARFCN", 0];
                    pci = (short?)lastPnt["lte_NCell_PCI", 0];

                    earfcn = getValidData(earfcn, (int?)lastPnt["lte_td_NCell_UARFCN", 0]);
                    pci = getValidData(pci, (short?)(int?)lastPnt["lte_td_NCell_CPI", 0]);

                    earfcn = getValidData(earfcn, (int?)(short?)lastPnt["lte_gsm_NC_BCCH", 0]);
                    pci = getValidData(pci, (short?)(byte?)lastPnt["lte_gsm_NC_BSIC", 0]);
                }
                cellNB = getCellName(cell, string.Format("{0}_{1}", earfcn, pci));
            }
            return brt;
        }

        private bool anaTpLst(List<LTEFddTestPoint> tpLstFdd)
        {
            int num = 0;
            LTEFddTestPoint lastPnt = null;
            foreach (LTEFddTestPoint tp in tpLstFdd)
            {
                object rsrp = MasterCom.RAMS.ZTFunc.AreaArchiveManage.TestPointFactory.CreateTestPointDeal(tp).GetRxlev(tp);
                object nrsrp = MasterCom.RAMS.ZTFunc.AreaArchiveManage.TestPointFactory.CreateTestPointDeal(tp).GetNbRxlev(tp, 0);

                if (rsrp == null || nrsrp == null) continue;

                if (Convert.ToSingle(rsrp) < MainRxlev && Convert.ToSingle(nrsrp) > NBRxlev)
                {
                    lastPnt = tp;
                    num++;
                }
            }
            bool brt = tpLstFdd.Count > 0 && (float)num * 100 / tpLstFdd.Count >= Percent;
            if (brt && lastPnt != null)
            {
                int? lac = null, ci = null, earfcn = null;
                short? pci = null;
                ICell cell = lastPnt.GetMainCell();
                if (cell == null)
                {
                    lac = (int?)lastPnt["lte_fdd_SCell_LAC"];
                    ci = (int?)lastPnt["lte_fdd_SCell_CI"];

                    lac = getValidData(lac, (int?)lastPnt["lte_fdd_wcdma_SysLAI"]);
                    ci = getValidData(ci, (int?)lastPnt["lte_fdd_wcdma_SysCellID"]);

                    lac = getValidData(lac, (int?)lastPnt["lte_fdd_gsm_SC_LAC"]);
                    ci = getValidData(ci, (int?)lastPnt["lte_fdd_gsm_SC_CI"]);
                }
                cellMain = getCellName(cell, string.Format("{0}_{1}", lac, ci));

                cell = lastPnt.GetNBCell(0);
                if (cell == null)
                {
                    earfcn = (int?)lastPnt["lte_fdd_NCell_EARFCN", 0];
                    pci = (short?)lastPnt["lte_fdd_NCell_PCI", 0];

                    earfcn = getValidData(earfcn, (int?)lastPnt["lte_fdd_wcdma_SNeiFreq", 0]);
                    pci = getValidData(pci, (short?)(int?)lastPnt["lte_fdd_wcdma_SNeiPSC", 0]);

                    earfcn = getValidData(earfcn, (int?)(short?)lastPnt["lte_fdd_gsm_NC_BCCH", 0]);
                    pci = getValidData(pci, (short?)(byte?)lastPnt["lte_fdd_gsm_NC_BSIC", 0]);
                }
                cellNB = getCellName(cell, string.Format("{0}_{1}", earfcn, pci));
            }
            return brt;
        }

        private string getCellName(ICell cell, string token)
        {
            if (cell == null)
            {
                return token;
            }
            else
            {
                return cell.Name;
            }
        }

        private T getValidData<T>(T data, T tpData)
        {
            if (data == null)
            {
                return tpData;
            }
            return data;
        }

        public override string GetCauseDesc()
        {
            return string.Format("主服{0}小区电平<{1}dBm，最强邻区{2}小区电平>{3}dBm，回看5秒{4}%采样点满足这个条件",
                cellMain, MainRxlev, cellNB, NBRxlev, Percent);
        }

        public override string GetCauseSuguest()
        {
            return string.Format("调整{0}小区到{1}小区切换参数", cellMain, cellNB);
        }

        public override string GetPhenomenon()
        {
            return "邻区电平持续强比服务小区强";
        }
    }

    public class CUnReasonableHOCond : CCauseCond
    {
        private string cellSrc = "";
        private string cellTarget = "";
        public int RxlevDiff { get; set; } = 5;
        public override Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["BChecked"] = BChecked;
                param["RxlevDiff"] = RxlevDiff;
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                if (param.ContainsKey("BChecked"))
                {
                    BChecked = (bool)param["BChecked"];
                }
                if (param.ContainsKey("RxlevDiff"))
                {
                    RxlevDiff = (int)param["RxlevDiff"];
                }
            }
        }
        public void Refresh(int rxlev)
        {
            this.RxlevDiff = rxlev;
        }

        public override bool IsValid(DTFileDataManager file, Event e, int msgIdxBegin, int msgIdxEnd)
        {
            Event hoEvent = getEvent(file, e);
            if (hoEvent == null) return false;
            return anaTestPoint(file, e, hoEvent);
        }

        private object getRsrp(TestPoint tp)
        {
            if (tp == null)
            {
                return null;
            }

            object rsrp = AreaArchiveManage.TestPointFactory.CreateTestPointDeal(tp).GetRxlev(tp);
            return rsrp;
        }

        private bool anaTestPoint(DTFileDataManager file, Event e, Event hoEvent)
        {
            LTETestPointDetail eTp = getNearstTestPoint(file, e);
            LTETestPointDetail eHoTp = getNearstTestPoint(file, hoEvent);
            LTEFddTestPoint eTpFdd = getNearstTestPointFdd(file, e);
            LTEFddTestPoint eHoTpFdd = getNearstTestPointFdd(file, hoEvent);

            if ((eTp == null || eHoTp == null) && (eTpFdd == null || eHoTpFdd == null))
            {
                return false;
            }
            object rsrpE = getRsrp(eTp);
            object rsrpEHO = getRsrp(eHoTp);
            object rsrpEFdd = getRsrp(eTpFdd);
            object rsrpEHOFdd = getRsrp(eHoTpFdd);

            if ((rsrpE == null || rsrpEHO == null) && (rsrpEFdd == null || rsrpEHOFdd == null))
            {
                return false;
            }

            ICell cell = hoEvent.GetSrcCell();
            cellSrc = cell == null ? string.Format("{0}_{1}", hoEvent["LAC"], hoEvent["CI"]) : cell.Name;

            cell = hoEvent.GetTargetCell();
            cellTarget = cell == null ? string.Format("{0}_{1}", hoEvent["TargetLAC"], hoEvent["TargetCI"]) : cell.Name;

            if (rsrpE != null && rsrpEHO != null)
            {
                return Convert.ToSingle(rsrpEHO) > Convert.ToSingle(rsrpE) + RxlevDiff;
            }
            else
            {
                return Convert.ToSingle(rsrpEHOFdd) > Convert.ToSingle(rsrpEFdd) + RxlevDiff;
            }
        }

        private LTETestPointDetail getNearstTestPoint(DTFileDataManager file, Event e)
        {
            int idxE = file.DTDatas.IndexOf(e);
            for (int idx = idxE - 1; idx >= 0; idx--)
            {
                DTData data = file.DTDatas[idx];

                if (data is LTETestPointDetail)
                {
                    return data as LTETestPointDetail;
                }
            }
            return null;
        }
        private LTEFddTestPoint getNearstTestPointFdd(DTFileDataManager file, Event e)
        {
            int idxE = file.DTDatas.IndexOf(e);
            for (int idx = idxE - 1; idx >= 0; idx--)
            {
                DTData data = file.DTDatas[idx];

                if (data is LTEFddTestPoint)
                {
                    return data as LTEFddTestPoint;
                }
            }
            return null;
        }

        private Event getEvent(DTFileDataManager file, Event e)
        {
            int idxE = file.DTDatas.IndexOf(e);
            for (int idx = idxE - 1; idx >= 0; idx-- )
            {
                DTData data = file.DTDatas[idx];

                if (data is Event)
                {
                    Event eData = data as Event;
                    if (isHandOverSuccess(eData))
                        return eData;
                }
            }
            return null;
        }

        private bool isHandOverSuccess(Event e)
        {
            foreach (EHandOverSuccess ehos in Enum.GetValues(typeof(EHandOverSuccess)))
            {
                if (e.ID == (int)ehos)
                {
                    return true;
                }
            }
            return false;
        }

        public override string GetCauseDesc()
        {
            return string.Format("{0}小区切换后比切换前{1}小区场强低{2}dB以上", cellTarget, cellSrc, RxlevDiff);
        }

        public override string GetCauseSuguest()
        {
            return string.Format("调整{0}小区到{1}小区切换参数", cellSrc, cellTarget);
        }

        public override string GetPhenomenon()
        {
            return "切换后主服小区电平比邻区电平弱";
        }
    }

    public class COtherCauseCond : CCauseCond
    {
        public override bool IsValid(DTFileDataManager file, Event e, int msgIdxBegin, int msgIdxEnd)
        {
            return true;
        }

        public override string GetCauseDesc()
        {
            return "其他原因";
        }

        public override string GetCauseSuguest()
        {
            return "需人工详查";
        }

        public override string GetPhenomenon()
        {
            return "";
        }
    }

    public class CMoProceedingCauseCond : CCauseCond
    {
        public override bool IsValid(DTFileDataManager file, Event e, int msgIdxBegin, int msgIdxEnd)
        {
            DTFileDataManager mtFile = getMtMoFile(file);

            if (mtFile == null) return true;

            return false;
        }

        public override string GetCauseDesc()
        {
            return "主叫异常事件时间点无对应的被叫log";
        }

        public override string GetCauseSuguest()
        {
            return "复测验证";
        }

        public override string GetPhenomenon()
        {
            return "对端文件未找到";
        }
    }

    public class CLURCauseCond : CCauseCond
    {
        private string dtDesc = "";

        public override bool IsValid(DTFileDataManager file, Event e, int msgIdxBegin, int msgIdxEnd)
        {
            DateTime beginTime = file.DTDatas[msgIdxBegin].DateTime;
            DateTime endTime = file.DTDatas[msgIdxEnd].DateTime;

            bool bResponse = false;
            bool bLocation = false;
            for (int idx = msgIdxBegin; idx < msgIdxEnd;idx++ )
            {
                DTData data = file.DTDatas[idx];

                if (data.DateTime < beginTime)
                    continue;
                if (data.DateTime > endTime)
                    break;
                bool isRelease = dealMsgData(ref bResponse, ref bLocation, data);
                if (isRelease)
                {
                    return false;
                }
            }
            return bLocation;
        }

        private bool dealMsgData(ref bool bResponse, ref bool bLocation, DTData data)
        {
            if (data is Message)
            {
                Message msg = data as Message;
                if (msg.ID == (int)EMessage.Authentication_Response)
                    bResponse = true;
                if (msg.ID == (int)EMessage.Channel_Release && bResponse)
                    return true;
                if (msg.ID == (int)EMessage.Location_Updating_Request ||
                    msg.ID == (int)EMessage.Routing_area_update_Request)
                {
                    dtDesc = msg.DateTime.ToString();
                    bLocation = true;
                }
            }
            return false;
        }

        public override string GetCauseDesc()
        {
            return string.Format("主叫在Authentication Response之后，未出现channel release消息，在此阶段位置更新，时间为：{0}", dtDesc);
        }

        public override string GetCauseSuguest()
        {
            return "检查基站是否存在故障以及是否存在强干扰";
        }

        public override string GetPhenomenon()
        {
            return "业务状态出现位置更新";
        }
    }

    public class CMtTACauseCond : CCauseCond
    {
        private string dtDesc = "";
        private string cellDesc = "";

        public override bool IsValid(DTFileDataManager file, Event e, int msgIdxBegin, int msgIdxEnd)
        {
            Message proceedingMsg = GetMoProceedingMsg(file, msgIdxBegin, msgIdxEnd);
            
            if (proceedingMsg == null) return false;

            DTFileDataManager mtFile = getMtMoFile(file);

            if (hasUpdateRequest(mtFile, proceedingMsg.DateTime))
                return true;
            return false;
        }

        private Message GetMoProceedingMsg(DTFileDataManager file, int msgIdxBegin, int msgIdxEnd)
        {
            for (int idx = msgIdxBegin; idx <= msgIdxEnd;idx++ )
            {
                DTData data = file.DTDatas[idx];
                if (data is Message)
                {
                    Message msg = data as Message;
                    if (msg.ID == (int)EMessage.Call_Proceeding ||
                        msg.ID == (int)EMessage.Call_Proceeding_GSM)
                    {
                        return msg;
                    }
                }
            }
            return null;
        }

        private bool hasUpdateRequest(DTFileDataManager file, DateTime dtStart)
        {
            foreach (Message msg in file.Messages)
            {
                if (msg.DateTime < dtStart.AddSeconds(-1))
                    continue;
                if (msg.DateTime > dtStart.AddSeconds(5))
                    break;
                if (msg.ID == (int)EMessage.Tracking_area_update_request)
                {
                    dtDesc = msg.DateTime.ToString();
                    COutParam param;
                    if (getCellParam(file, msg, out param))
                    {
                        cellDesc = param.CellName;
                    }
                    return true;
                }
            }
            return false;
        }

        public override string GetCauseDesc()
        {
            return string.Format("被叫在{0}正在做TA更新", dtDesc);
        }

        public override string GetCauseSuguest()
        {
            return string.Format("合理规划{0}小区的位置区，或调整其覆盖范围", cellDesc);
        }

        public override string GetPhenomenon()
        {
            return "被叫正在做TA更新";
        }
    }

    public class CMtCallingCauseCond : CCauseCond
    {
        private string strCellDesc { get; set; } = "";
        private string strProceedingTime { get; set; } = "";
        private string numberDesc = "";

        public override bool IsValid(DTFileDataManager file, Event e, int msgIdxBegin, int msgIdxEnd)
        {
            DTFileDataManager mtFile = getMtMoFile(file);
            if (mtFile == null) return false;

            Message proceedingMsg = GetMoProceedingMsg(file, msgIdxBegin, msgIdxEnd);
            if (proceedingMsg == null) return false;

            Message paging, extend_service_request;
            if (bMtMsg(mtFile, proceedingMsg.DateTime.AddSeconds(-1), proceedingMsg.DateTime.AddSeconds(10),
                out paging, out extend_service_request))
                return false;

            Message previousMsg = GetMoPreviousDisconnect(file, proceedingMsg);
            if (previousMsg == null) return false;

            bool bMtHaveRequest = bMtServiceRequest(mtFile, previousMsg.DateTime, proceedingMsg.DateTime.AddSeconds(-1));

            if (bMtHaveRequest)
            {
                DateTime startTime = previousMsg.DateTime;
                DateTime endTime = e.DateTime;
                findNumber(mtFile, startTime, endTime);

                return true;
            }
            return false;
        }

        private bool bMtMsg(DTFileDataManager mtFile, DateTime startTime, DateTime endTime,
            out Message paging, out Message extend_service_request)
        {
            paging = extend_service_request = null;

            foreach (Message msg in mtFile.Messages)
            {
                if (msg.DateTime < startTime)
                    continue;
                else if(msg.DateTime > endTime)
                    break;
                if (msg.ID == (int)EMessage.Extended_Service_Request)
                {
                    extend_service_request = msg;
                }
                if (msg.ID == (int)EMessage.Paging)
                {
                    paging = msg;
                }
            }
            return extend_service_request != null;
        }

        private void findNumber(DTFileDataManager mtFile, DateTime startTime, DateTime endTime)
        {
            foreach (Message msg in mtFile.Messages)
            {
                if(msg.DateTime < startTime)
                    continue;
                if(msg.DateTime > endTime)
                    break;
                if(msg.ID == (int)EMessage.setupGSM ||
                    msg.ID == (int)EMessage.setupLTE)
                {
                    MessageWithSource setupMsg = msg as MessageWithSource;
                    numberDesc = PhoneInfoAnaByRegion.GetCsfbPhoneNumber(setupMsg);
                    return;
                }
            }
        }

        private bool bMtServiceRequest(DTFileDataManager mtFile, DateTime startTime, DateTime endTime)
        {
            Message serviceRequestMsg = null;
            foreach (Message msg in mtFile.Messages)
            {
                if (msg.DateTime < startTime)
                    continue;
                if(msg.DateTime > endTime)
                    break;
                if (msg.ID == (int)EMessage.Extended_Service_Request ||
                    msg.ID == (int)EMessage.GMM_Service_Request ||
                    msg.ID == (int)EMessage.GMM_Service_Request_LTE)
                {
                    serviceRequestMsg = msg;
                    break;
                }
            }
            return serviceRequestMsg != null;
        }

        private Message GetMoPreviousDisconnect(DTFileDataManager file, DTData proceeding)
        {
            int msgIdxEnd = file.DTDatas.IndexOf(proceeding);
            for (int idx = msgIdxEnd; idx >= 0; idx-- )
            {
                DTData data = file.DTDatas[idx];
                if (data is Message)
                {
                    Message msg = data as Message;
                    if (msg.ID == (int)EMessage.Disconnect ||
                        msg.ID == (int)EMessage.Disconnect_LTE ||
                        msg.ID == (int)EMessage.Release ||
                        msg.ID == (int)EMessage.Release_LTE)
                    {
                        return msg;
                    }
                }
            }
            return null;
        }

        private Message GetMoProceedingMsg(DTFileDataManager file, int msgIdxBegin, int msgIdxEnd)
        {
            for (int idx = msgIdxBegin; idx <= msgIdxEnd; idx++)
            {
                DTData data = file.DTDatas[idx];
                if (data is Message)
                {
                    Message msg = data as Message;
                    if (msg.ID == (int)EMessage.Call_Proceeding ||
                        msg.ID == (int)EMessage.Call_Proceeding_GSM)
                    {
                        strProceedingTime = msg.DateTime.ToString();
                        COutParam param;
                        if (getCellParam(file, msg, out param))
                        {
                            strCellDesc = param.CellName;
                        }
                        return msg;
                    }
                }
            }
            return null;
        }

        public override string GetCauseDesc()
        {
            return string.Format("被叫在主叫发出CM Service Request的时候已经在通话，通话号码为{0}", numberDesc);
        }

        public override string GetCauseSuguest()
        {
            return "尽量避免其他号码和测试号码通话";
        }

        public override string GetPhenomenon()
        {
            return "被叫正在通话";
        }
    }

    public class CMtCallingNoServiceRequestCauseCond : CCauseCond
    {
        private string strProceedingTime = "";
        private string strCellDesc = "";

        private enum PagingRecord { ps, cs };

        public override bool IsValid(DTFileDataManager file, Event e, int msgIdxBegin, int msgIdxEnd)
        {
            DateTime beginTime = file.DTDatas[msgIdxBegin].DateTime;

            DTFileDataManager mtFile = getMtMoFile(file);
            if (mtFile == null) return false;
            strProceedingTime = beginTime.ToString();

            Message paging, extend_service_request;
            if (bMtMsg(mtFile, beginTime.AddSeconds(-1), beginTime.AddSeconds(10),
                out paging, out extend_service_request))
                return false;

            List<int> msgIDs = new List<int>(new int[] { (int)EMessage.Disconnect, (int)EMessage.Disconnect_LTE, (int)EMessage.Release, (int)EMessage.Release_LTE });
            Message previousMsg = findPrevMessage(file, file.DTDatas[msgIdxBegin], msgIDs);
            if (previousMsg == null) return false;

            if (paging != null)
            {
                MessageWithSource pagingMsWS = paging as MessageWithSource;
                uint arrCnt = 0;
                if (MessageDecodeHelper.StartDissect(pagingMsWS.Direction, pagingMsWS.Source, pagingMsWS.Length, pagingMsWS.ID)
                    && MessageDecodeHelper.GetSingleUInt("lte-rrc.pagingRecordList", ref arrCnt) && arrCnt > 0)
                {
                    uint[] recordArr = new uint[arrCnt];
                    if (MessageDecodeHelper.GetMultiUInt("lte-rrc.cn_Domain", ref recordArr, (int)arrCnt))
                    {
                        return judgeValidRecord(file, msgIdxBegin, recordArr);
                    }
                }
            }
            return false;
        }

        private bool judgeValidRecord(DTFileDataManager file, int msgIdxBegin, uint[] recordArr)
        {
            foreach (uint ui in recordArr)
            {
                if (ui == (int)PagingRecord.cs)
                {
                    COutParam param;
                    if (getCellParam(file, file.DTDatas[msgIdxBegin], out param))
                    {
                        strCellDesc = param.CellName;
                    }
                    return true;
                }
            }
            return false;
        }

        private bool bMtMsg(DTFileDataManager mtFile, DateTime startTime, DateTime endTime,
            out Message paging, out Message extend_service_request)
        {
            paging = extend_service_request = null;

            foreach (Message msg in mtFile.Messages)
            {
                if (msg.DateTime < startTime)
                    continue;
                else if (msg.DateTime > endTime)
                    break;
                if (msg.ID == (int)EMessage.Extended_Service_Request)
                {
                    extend_service_request = msg;
                }
                if (msg.ID == (int)EMessage.Paging)
                {
                    paging = msg;
                }
            }
            return extend_service_request != null;
        }

        public override string GetCauseDesc()
        {
            return string.Format("主叫在{0}发出MO CSFB Proceeding请求，被叫在这个时间点前1秒后10秒以内没有有效paging消息", strProceedingTime);
        }

        public override string GetCauseSuguest()
        {
            return string.Format("跟踪{0}小区MME以及eNB是否下发寻呼消息", strCellDesc);
        }

        public override string GetPhenomenon()
        {
            return "被叫未收到寻呼消息";
        }
    }

    public class CMtCallingNoCallCauseCond : CCauseCond
    {
        private string strProceedingTime = "";

        public override bool IsValid(DTFileDataManager file, Event e, int msgIdxBegin, int msgIdxEnd)
        {
            DateTime beginTime = file.DTDatas[msgIdxBegin].DateTime;
            DateTime endTime = file.DTDatas[msgIdxEnd].DateTime;

            DTFileDataManager mtFile = getMtMoFile(file);
            if (mtFile == null) return false;

            List<int> msgIDs = new List<int>(new int[] { (int)EMessage.Call_Proceeding, (int)EMessage.Call_Proceeding_GSM });
            Message proceedingMsg = findMessage(file, beginTime, endTime, msgIDs);
            if (proceedingMsg == null) return false;
            strProceedingTime = proceedingMsg.DateTime.ToString();

            Message paging, extend_service_request;
            if (bMtMsg(mtFile, proceedingMsg.DateTime.AddSeconds(-1), proceedingMsg.DateTime.AddSeconds(10),
                out paging, out extend_service_request))
                return false;

            msgIDs = new List<int>(new int[] { (int)EMessage.Disconnect, (int)EMessage.Disconnect_LTE, (int)EMessage.Release, (int)EMessage.Release_LTE });
            Message previousMsg = findPrevMessage(file, proceedingMsg, msgIDs);
            if (previousMsg == null) return false;

            if (paging != null)
            {
                return true;
            }
            return false;
        }

        private bool bMtMsg(DTFileDataManager mtFile, DateTime startTime, DateTime endTime,
            out Message paging, out Message extend_service_request)
        {
            paging = extend_service_request = null;

            foreach (Message msg in mtFile.Messages)
            {
                if (msg.DateTime < startTime)
                    continue;
                else if (msg.DateTime > endTime)
                    break;
                if (msg.ID == (int)EMessage.Extended_Service_Request)
                {
                    extend_service_request = msg;
                }
                if (msg.ID == (int)EMessage.Paging)
                {
                    paging = msg;
                }
            }
            return extend_service_request != null;
        }

        public override string GetCauseDesc()
        {
            return string.Format("主叫在{0}发出MO CSFB Proceeding请求，被叫在这个时间点前1秒后10秒以内没有Extended service ", strProceedingTime);
        }

        public override string GetCauseSuguest()
        {
            return "检查终端以及基站是否正常";
        }

        public override string GetPhenomenon()
        {
            return "被叫未起呼";
        }
    }

    public class CMtExtendServiceRequestCond : CCauseCond
    {
        private string dtDesc = "";
        private string cellDesc = "";

        public override bool IsValid(DTFileDataManager file, Event e, int msgIdxBegin, int msgIdxEnd)
        {
            DTFileDataManager mtFile = getMtMoFile(file);
            if (mtFile == null) return false;

            Message proceedingMsg = GetMoProceedingMsg(mtFile, file, msgIdxBegin, msgIdxEnd);

            if (proceedingMsg == null) return false;

            if (hasUpdateRequest(mtFile, proceedingMsg.DateTime))
                return true;
            return false;
        }

        private Message GetMoProceedingMsg(DTFileDataManager mtFile, DTFileDataManager file, int msgIdxBegin, int msgIdxEnd)
        {
            for (int idx = msgIdxBegin; idx <= msgIdxEnd; idx++)
            {
                DTData data = file.DTDatas[idx];
                if (data is Message)
                {
                    Message msg = data as Message;
                    if (msg.ID == (int)EMessage.Call_Proceeding ||
                        msg.ID == (int)EMessage.Call_Proceeding_GSM)
                    {
                        dtDesc = msg.DateTime.ToString();
                        COutParam param;
                        if (getCellParam(mtFile, msg, out param))
                        {
                            cellDesc = param.CellName;
                        }
                        return msg;
                    }
                }
            }
            return null;
        }

        private bool hasUpdateRequest(DTFileDataManager file, DateTime dtStart)
        {
            bool bHasMsg = false;
            bool bFind = false;
            foreach (Message msg in file.Messages)
            {
                if (msg.DateTime < dtStart.AddSeconds(-1))
                    continue;
                if (msg.DateTime > dtStart.AddSeconds(5))
                    break;
                bHasMsg = true;
                if (msg.ID == (int)EMessage.Extended_Service_Request)
                {
                    bFind = true;
                    break;
                }
            }
            return !bHasMsg || !bFind;
        }

        public override string GetCauseDesc()
        {
            return string.Format("主叫在{0}发出Call Proceeding请求，被叫在这个时间点前1秒或后5秒以内没有Extended service request消息", dtDesc);
        }

        public override string GetCauseSuguest()
        {
            return string.Format("跟踪{0}小区MME以及eNB是否下发寻呼消息", cellDesc);
        }

        public override string GetPhenomenon()
        {
            return "";
        }
    }

    public class CMoProceedingNoRRCReleaseCauseCond : CCauseCond
    {
        private readonly string cellDesc = "";

        public override bool IsValid(DTFileDataManager file, Event e, int msgIdxBegin, int msgIdxEnd)
        {
            DateTime beginTime = file.DTDatas[msgIdxBegin].DateTime;
            DateTime endTime = file.DTDatas[msgIdxEnd].DateTime;

            DTFileDataManager mtFile = getMtMoFile(file);
            if (mtFile == null) return false;

            List<int> evtLst = new List<int>(new int[] { (int)EEvent.MO_CSFB_Proceeding, (int)EEvent.MT_CSFB_Proceeding,
                (int)EEvent.FDD_MO_CSFB_Proceeding,(int)EEvent.FDD_MT_CSFB_Proceeding });
            Event proceedingEvt = findEvent(file, beginTime, endTime, evtLst);
            if (proceedingEvt == null) return false;

            List<int> msgIDs = new List<int>(new int[]{(int)EMessage.Extended_Service_Request,
            (int)EMessage.GMM_Service_Request,(int)EMessage.GMM_Service_Request_LTE});
            Message serviceRequest = findMessage(mtFile, proceedingEvt.DateTime, endTime, msgIDs);
            if (serviceRequest == null) return false;

            msgIDs = new List<int>(new int[] { (int)EMessage.RRCConnectionRelease });
            Message rrcReleaseMsg = findMessage(mtFile, serviceRequest.DateTime, endTime, msgIDs);

            if (rrcReleaseMsg == null)
            {
                return true;
            }
            return false;
        }

        public override string GetCauseDesc()
        {
            return "被叫出现Extended service request 消息之后未出现RRC Connection Release消息";
        }

        public override string GetCauseSuguest()
        {
            return string.Format("排查{0}小区邻区参数设置是否正常", cellDesc);
        }

        public override string GetPhenomenon()
        {
            return "被叫未发生回落";
        }
    }

    public class CMoProceedingRRCReleaseWithEarfcnCauseCond : CCauseCond
    {
        private string strDesc = "";
        private string strSuggest = "";

        public string ReasonName { get; set; } = "回落失败";
        private string phenomenon = "异系统邻区配置不合理";

        public override bool IsValid(DTFileDataManager file, Event e, int msgIdxBegin, int msgIdxEnd)
        {
            bool bFindLac = false;
            DateTime beginTime = file.DTDatas[msgIdxBegin].DateTime;
            DateTime endTime = file.DTDatas[msgIdxEnd].DateTime;

            DTFileDataManager mtFile = getMtMoFile(file);
            if (mtFile == null) return false;

            List<int> evtLst = new List<int>(new int[] { (int)EEvent.MO_CSFB_Proceeding, (int)EEvent.MT_CSFB_Proceeding,
                (int)EEvent.FDD_MO_CSFB_Proceeding,(int)EEvent.FDD_MT_CSFB_Proceeding });
            Event proceedingEvt = findEvent(file, beginTime, endTime, evtLst);
            if (proceedingEvt == null) return false;

            List<int> msgIDs = new List<int>(new int[]{(int)EMessage.Extended_Service_Request,
            (int)EMessage.GMM_Service_Request,(int)EMessage.GMM_Service_Request_LTE});
            Message serviceRequest = findMessage(mtFile, proceedingEvt.DateTime, endTime, msgIDs);
            if (serviceRequest == null) return false;

            msgIDs = new List<int>(new int[] { (int)EMessage.RRCConnectionRelease });
            Message rrcReleaseMsg = findMessage(mtFile, serviceRequest.DateTime, endTime, msgIDs);

            if (rrcReleaseMsg != null)
            {
                MessageWithSource RRCReleaseMsg = rrcReleaseMsg as MessageWithSource;
                uint[] earfcns = new uint[20];
                if (MessageDecodeHelper.StartDissect(RRCReleaseMsg.Direction, RRCReleaseMsg.Source, RRCReleaseMsg.Length, RRCReleaseMsg.ID) &&
                    MessageDecodeHelper.GetMultiUInt("lte-rrc.explicitListOfARFCNs", ref earfcns, earfcns.Length) &&
                    earfcns.Length > 0)
                {
                    msgIDs = new List<int>(new int[] { (int)EMessage.Paging_Response });
                    Message pagingResponce = findMessage(mtFile, rrcReleaseMsg.DateTime, endTime, msgIDs);
                    if (pagingResponce == null)
                    {
                        setDesc(bFindLac, endTime, mtFile, rrcReleaseMsg);
                        return true;
                    }
                }
            }
            return false;
        }

        private void setDesc(bool bFindLac, DateTime endTime, DTFileDataManager mtFile, Message rrcReleaseMsg)
        {
            int lac = 0;
            COutParam param;
            if (getCellParam(mtFile, rrcReleaseMsg.DateTime, endTime, MasterCom.RAMS.Model.TestPoint.ECurrNetType.GSM, out param) &&
                int.TryParse(param.Lac, out lac))
            {
                bFindLac = CellManager.GetInstance().LacExists(lac) || CellManager.GetInstance().LacExists_TD(lac);
            }
            if (bFindLac)
            {
                strDesc = "异系统邻区配置不合理导致回落失败，从而产生未接通";
                strSuggest = "检查异系统邻区配置问题";
            }
            else
            {
                strDesc = string.Format("被叫回落占用上伪基站，LAC为：{0}，导致回落失败，从而发生未接通", lac);
                strSuggest = "排除伪基站干扰";
                ReasonName = "伪基站";
                phenomenon = "LAC/TAC未在本地工参中找到";
            }
        }

        public override string GetCauseDesc()
        {
            return strDesc;
        }

        public override string GetCauseSuguest()
        {
            return strSuggest;
        }

        public override string GetPhenomenon()
        {
            return phenomenon;
        }
    }

    public class CMoProceedingRRCReleaseCauseCond : CCauseCond
    {
        private string cellDesc = "";

        public override bool IsValid(DTFileDataManager file, Event e, int msgIdxBegin, int msgIdxEnd)
        {
            DateTime beginTime = file.DTDatas[msgIdxBegin].DateTime;
            DateTime endTime = file.DTDatas[msgIdxEnd].DateTime;

            DTFileDataManager mtFile = getMtMoFile(file);
            if (mtFile == null) return false;

            List<int> evtLst = new List<int>(new int[] { (int)EEvent.MO_CSFB_Proceeding, (int)EEvent.MT_CSFB_Proceeding,
                (int)EEvent.FDD_MO_CSFB_Proceeding,(int)EEvent.FDD_MT_CSFB_Proceeding });
            Event proceedingEvt = findEvent(file, beginTime, endTime, evtLst);
            if (proceedingEvt == null) return false;

            List<int> msgIDs = new List<int>(new int[]{(int)EMessage.Extended_Service_Request,
            (int)EMessage.GMM_Service_Request,(int)EMessage.GMM_Service_Request_LTE});
            Message serviceRequest = findMessage(mtFile, proceedingEvt.DateTime, endTime, msgIDs);
            if (serviceRequest == null) return false;

            msgIDs = new List<int>(new int[] { (int)EMessage.RRCConnectionRelease });
            Message rrcReleaseMsg = findMessage(mtFile, serviceRequest.DateTime, endTime, msgIDs);

            if (rrcReleaseMsg != null)
            {
                COutParam param;
                if (getCellParam(mtFile, rrcReleaseMsg, out param))
                {
                    cellDesc = param.CellName;
                }
                MessageWithSource RRCReleaseMsg = rrcReleaseMsg as MessageWithSource;
                uint[] earfcns = new uint[20];
                if (!MessageDecodeHelper.StartDissect(RRCReleaseMsg.Direction, RRCReleaseMsg.Source, RRCReleaseMsg.Length, RRCReleaseMsg.ID) ||
                    !MessageDecodeHelper.GetMultiUInt("lte-rrc.explicitListOfARFCNs", ref earfcns, earfcns.Length) ||
                    earfcns.Length <= 0)
                {
                    return true;
                }
            }
            return false;
        }

        public override string GetCauseDesc()
        {
            return "被叫出现Extended service request 消息之后未出现RRC Connection Release消息";
        }

        public override string GetCauseSuguest()
        {
            return string.Format("排查{0}小区邻区参数设置是否正常", cellDesc);
        }

        public override string GetPhenomenon()
        {
            return "被叫未发生回落";
        }
    }

    public class CMtLURCauseCond : CCauseCond
    {
        private string dtDesc = "";
        private string cellDesc = "";

        public override bool IsValid(DTFileDataManager file, Event e, int msgIdxBegin, int msgIdxEnd)
        {
            DTFileDataManager mtFile = getMtMoFile(file);
            if (mtFile == null) return false;

            DateTime beginTime = file.DTDatas[msgIdxBegin].DateTime;
            DateTime endTime = file.DTDatas[msgIdxEnd].DateTime;

            foreach (Message msg in mtFile.Messages)
            {
                if (msg.DateTime < beginTime)
                    continue;
                if(msg.DateTime > endTime)
                    break;
                if (msg.ID == (int)EMessage.Location_Updating_Request ||
                        msg.ID == (int)EMessage.Routing_area_update_Request)
                {
                    dtDesc = msg.DateTime.ToString();
                    COutParam param;
                    if (getCellParam(mtFile, msg, out param))
                    {
                        cellDesc = param.CellName;
                    }
                    return true;
                }
            } return false;
        }

        public override string GetCauseDesc()
        {
            return string.Format("被叫在{0}正在做位置更新", dtDesc);
        }

        public override string GetCauseSuguest()
        {
            return string.Format("合理规划{0}小区的位置区，或调整其覆盖范围", cellDesc);
        }

        public override string GetPhenomenon()
        {
            return "被叫位置更新";
        }
    }

    public class CTCHDisconnectCauseCond : CCauseCond
    {
        private bool bMt = false;
        private string cellDesc = "";

        private string strDesc = "";
        private string strSuggest = "";

        public string ResonName { get; set; } = "资源不足";
        private string phenomenon = "SDCCH信道拥塞";

        public override bool IsValid(DTFileDataManager file, Event e, int msgIdxBegin, int msgIdxEnd)
        {
            if (file.MoMtFlag == (int)EMoMt.Mt)
                bMt = true;
            Message msg = getDisconnectMsg(file, msgIdxBegin, msgIdxEnd);
            if (msg == null) return false;
            
            MessageWithSource disconnectMsg = msg as MessageWithSource;
            uint cause = 0;
            if(MessageDecodeHelper.StartDissect(msg.Direction,disconnectMsg.Source, disconnectMsg.Length, disconnectMsg.ID) &&
                MessageDecodeHelper.GetSingleUInt("gsm_a_dtap.cause", ref cause))
            {
                return setValue(cause);
            }
            return false;
        }

        private bool setValue(uint cause)
        {
            if ((cause == (int)ECauseValue.Value34 || cause == (int)ECauseValue.Value44))
            {
                strDesc = string.Format("{0}Disconnect 中Cause value 为Cause（34）或Cause（44），TCH拥塞导致主被叫都未接通", bMt ? "被叫" : "主叫");
                strSuggest = string.Format("对{0}小区进行扩容", cellDesc);
                ResonName = "资源不足";
                phenomenon = "SDCCH信道拥塞";
                return true;
            }
            else if (cause == (int)ECauseValue.Value38)
            {
                strDesc = "被叫Disconnect 中Cause value 为Cause（38），网络故障导致未接通";
                strSuggest = "检查当前网络问题";
                ResonName = "基站故障";
                phenomenon = "网络故障";
                return true;
            }
            else if (cause == (int)ECauseValue.Value19)
            {
                strDesc = "被叫Disconnect 中Cause value 为Cause（19），被叫无应答导致未接通";
                strSuggest = "检查被叫终端问题以及呼叫设置";
                ResonName = "人为原因";
                phenomenon = "用户振铃，无应答";
                return true;
            }
            else if (cause == (int)ECauseValue.Value21)
            {
                strDesc = "被叫Disconnect 中Cause value 为Cause（21），呼叫被被叫拒绝导致未接通";
                strSuggest = "检查被叫终端问题以及呼叫设置";
                ResonName = "人为原因";
                phenomenon = "呼叫被拒绝";
                return true;
            }
            else if (cause == (int)ECauseValue.Value17)
            {
                strDesc = "被叫Disconnect 中Cause value 为Cause（17），被叫正在通话导致未接通";
                strSuggest = "检查被叫终端问题以及呼叫设置";
                ResonName = "人为原因";
                phenomenon = "用户忙";
                return true;
            }
            else if (cause == (int)ECauseValue.Value108)
            {
                strDesc = "被叫Disconnect 中Cause value 为Cause（108），计时器到时";
                strSuggest = "检查被叫终端问题以及呼叫设置";
                ResonName = "人为原因";
                phenomenon = "计时器到时";
                return true;
            }
            else if (cause == (int)ECauseValue.Value127)
            {
                strDesc = "被叫Disconnect 中Cause value 为Cause（127）";
                strSuggest = "通过终端信令无法定位精确原因";
                ResonName = "其他";
                phenomenon = "通过终端信令无法定位精确原因";
                return true;
            }
            return false;
        }

        private Message getDisconnectMsg(DTFileDataManager file, int msgIdxBegin, int msgIdxEnd)
        {
            for (int idx = msgIdxBegin; idx <= msgIdxEnd; idx++ )
            {
                DTData data = file.DTDatas[idx];
                if (data is Message)
                {
                    Message msg = data as Message;
                    if (msg.ID == (int)EMessage.Disconnect || msg.ID == (int)EMessage.Disconnect_LTE)
                    {
                        COutParam param;
                        if (getCellParam(file, msg, out param))
                        {
                            cellDesc = param.CellName;
                        }
                        return msg;
                    }
                }
            }
            return null;
        }

        public override string GetCauseDesc()
        {
            return strDesc;
        }

        public override string GetCauseSuguest()
        {
            return strSuggest;
        }

        public override string GetPhenomenon()
        {
            return phenomenon;
        }

        private enum ECauseValue
        {
            Value17 = 17,
            Value19 = 19,
            Value21 = 21,
            Value34 = 34,
            Value38 = 38,
            Value44 = 44,
            Value108 = 108,
            Value127 = 127,
        }
    }

    public class CPersonDoesCauseCond : CCauseCond
    {
        private string timeDesc = "";

        public override bool IsValid(DTFileDataManager file, Event e, int msgIdxBegin, int msgIdxEnd)
        {
            DTFileDataManager mtmoFile = file;
            if (file.MoMtFlag == (int)EMoMt.Mo)
                mtmoFile = getMtMoFile(file);

            if (mtmoFile == null) return false;

            if (bDisconnect(mtmoFile, file.DTDatas[msgIdxBegin].DateTime, file.DTDatas[msgIdxEnd].DateTime, true))
                return true;
            return false;
        }

        private bool bDisconnect(DTFileDataManager file, DateTime beginTime, DateTime endTime, bool bAlert)
        {
            foreach (Message msg in file.Messages)
            {
                if (msg.DateTime < beginTime)
                    continue;
                if(msg.DateTime > endTime)
                    break;
                if (msg.ID == (int)EMessage.AlertingGSM || msg.ID == (int)EMessage.AlertingTD)
                    bAlert = true;
                if (bAlert &&
                    (msg.ID == (int)EMessage.Disconnect || msg.ID == (int)EMessage.Disconnect_LTE))
                {
                    timeDesc = msg.DateTime.ToString();
                    return msg.Direction == (int)EDirection.Up;
                }
            }
            return false;
        }

        public override string GetCauseDesc()
        {
            return string.Format("被叫在{0}主动挂断（出现上行Disconnect消息）", timeDesc);
        }

        public override string GetCauseSuguest()
        {
            return "测试过程请规范测试";
        }

        public override string GetPhenomenon()
        {
            return "被叫振铃后出现上行Disconnect消息";
        }

        private enum EDirection
        {
            Down = 1,
            Up = 2,
        }
    }

    public class CPersonDoes2CauseCond : CCauseCond
    {
        private string timeDesc = "";

        public override bool IsValid(DTFileDataManager file, Event e, int msgIdxBegin, int msgIdxEnd)
        {
            DTFileDataManager mtmoFile = getMtMoFile(file);
            if (mtmoFile == null) return false;
            if (file.MoMtFlag == (int)EMoMt.Mo)
            {
                if (bDisconnect(mtmoFile, file.DTDatas[msgIdxBegin].DateTime, file.DTDatas[msgIdxEnd].DateTime, false))
                    return true;
                return false;
            }
            if (bDisconnect(mtmoFile, file.DTDatas[msgIdxBegin].DateTime, file.DTDatas[msgIdxEnd].DateTime, true))
                return true;
            return false;
        }

        private bool bDisconnect(DTFileDataManager file, DateTime beginTime, DateTime endTime, bool bAlert)
        {
            foreach (Message msg in file.Messages)
            {
                if (msg.DateTime < beginTime)
                    continue;
                if (msg.DateTime > endTime)
                    break;
                if (msg.ID == (int)EMessage.AlertingGSM)
                    bAlert = true;
                if (bAlert &&
                    (msg.ID == (int)EMessage.Release_Complete || msg.ID == (int)EMessage.Release_Complete_LTE))
                {
                    timeDesc = msg.DateTime.ToString();
                    return msg.Direction == (int)EDirection.Down;
                }
            }
            return false;
        }

        public override string GetCauseDesc()
        {
            return string.Format("{0}Alerting之后,出现下行的Release Complete", timeDesc);
        }

        public override string GetCauseSuguest()
        {
            return "基站故障";
        }

        public override string GetPhenomenon()
        {
            return "被叫振铃后出现下行release complete";
        }

        private enum EDirection
        {
            Down = 1,
            Up = 2,
        }
    }

    public class CPretendCauseCond : CCauseCond
    {
        private string lacDesc = "";

        public override bool IsValid(DTFileDataManager file, Event e, int msgIdxBegin, int msgIdxEnd)
        {
            return isPretendStation(file, msgIdxBegin, msgIdxEnd);
        }

        private bool isPretendStation(DTFileDataManager file, int msgIdxBegin, int msgIdxEnd)
        {
            DateTime beginTime = file.DTDatas[msgIdxBegin].DateTime;
            DateTime endTime = file.DTDatas[msgIdxEnd].DateTime;

            bool bLocation = false;
            foreach (Message msg in file.Messages)
            {
                if (msg.DateTime < beginTime.AddSeconds(-3))
                    continue;
                if (msg.DateTime > endTime)
                    break;
                if (msg.ID == (int)EMessage.Location_Updating_Accept ||
                    msg.ID == (int)EMessage.Location_Updating_Reject ||
                    msg.ID == (int)EMessage.Location_Updating_Accept_LTE ||
                    msg.ID == (int)EMessage.Location_Updating_Reject_LTE)
                {
                    bLocation = true;
                    
                    MessageWithSource acceptMsg = msg as MessageWithSource;
                    uint lac = 0;
                    if (MessageDecodeHelper.StartDissect(msg.Direction,acceptMsg.Source, acceptMsg.Length, acceptMsg.ID) &&
                        MessageDecodeHelper.GetSingleUInt("gsm_a.lai_lac", ref lac))
                    {
                        lacDesc = Convert.ToString(lac);
                        return !(CellManager.GetInstance().LacExists(Convert.ToInt32(lac)) ||
                            CellManager.GetInstance().LacExists_TD(Convert.ToInt32(lac)));
                    }
                }
            }
            return bLocation;
        }

        public override string GetCauseDesc()
        {
            return string.Format("终端占用到伪基站小区，占用LAC为：{0}", lacDesc);
        }

        public override string GetCauseSuguest()
        {
            return "排除伪基站干扰";
        }

        public override string GetPhenomenon()
        {
            return "采样点LAC/TAC不在本地工参中";
        }
    }

    public class CServiceAbortCauseCond : CCauseCond
    {
        private string dtDesc = "";
        private string cellDesc = "";

        public override bool IsValid(DTFileDataManager file, Event e, int msgIdxBegin, int msgIdxEnd)
        {
            for (int idx = msgIdxBegin; idx <= msgIdxEnd; idx++)
            {
                DTData data = file.DTDatas[idx];
                if (data is Message)
                {
                    Message msg = data as Message;

                    if (msg.ID == (int)EMessage.CM_Service_Abort ||
                        msg.ID == (int)EMessage.CM_Service_Abort_LTE)
                    {
                        dtDesc = msg.DateTime.ToString();
                        COutParam param;
                        if (getCellParam(file, data, out param))
                        {
                            cellDesc = param.CellName;
                        }
                        return true;
                    }
                }
            } return false;
        }

        public override string GetCauseDesc()
        {
            return string.Format("主叫在{0}收到CM Service Abort消息", dtDesc);
        }

        public override string GetCauseSuguest()
        {
            return string.Format("排查{0}小区基站故障", cellDesc);
        }

        public override string GetPhenomenon()
        {
            return "呼叫被拒绝";
        }
    }

    public class CBlockCallCauseCond : CCauseCond
    {
        private bool isMo = true;

        public override bool IsValid(DTFileDataManager file, Event e, int msgIdxBegin, int msgIdxEnd)
        {
            DateTime startTime = file.DTDatas[msgIdxBegin].DateTime;
            DateTime endTime = file.DTDatas[msgIdxEnd].DateTime;

            return bBlockCall(file, startTime, endTime);
        }

        private bool bBlockCall(DTFileDataManager file, DateTime startTime, DateTime endTime)
        {
            isMo = file.MoMtFlag == (int)EMoMt.Mo;
            int iBlockCall = isMo ? (int)EEvent.CSFB_MT_Block_Call : (int)EEvent.CSFB_MO_Block_Call;
            int iBlockCallFdd = isMo ? (int)EEvent.FDD_CSFB_MT_Block_Call : (int)EEvent.FDD_CSFB_MO_Block_Call;
            foreach (Event e in file.Events)
            {
                if(e.DateTime < startTime)
                    continue;
                if(e.DateTime > endTime)
                    break;
                if (e.ID == iBlockCall || e.ID == iBlockCallFdd)
                    return true;
            }
            return false;
        }

        public override string GetCauseDesc()
        {
            return string.Format("{0}", isMo ? "在主叫Log出现CSFB MT BLOCK CALL" : "在被叫Log出现CSFB_MO_Block_Call");
        }

        public override string GetCauseSuguest()
        {
            return "测试过程请规范测试";
        }

        public override string GetPhenomenon()
        {
            return "主叫log中出现被叫事件";
        }
    }

    public abstract class CCauseCond : ICloneable
    {
        public bool BChecked { get; set; } = true;

        private bool fillTddData(COutParam param, List<LTETestPointDetail> testPntLst)
        {
            LTETestPointDetail tp = testPntLst[0];
            ICell cell;
            int? lac;
            int? ci;

            CellManager mng = CellManager.GetInstance();
            lac = (int?)tp["lte_gsm_SC_LAC"];
            ci = (int?)tp["lte_gsm_SC_CI"];
            cell = mng.GetNearestCell(tp.DateTime, (ushort?)lac, (ushort?)ci,
                (short?)tp["lte_gsm_SC_BCCH"], (byte?)tp["lte_gsm_SC_BSIC"], tp.Longitude, tp.Latitude);
            if (lac != null && ci != null)
            {
                param.Fill(cell, lac, ci, (float?)(short?)tp["lte_gsm_DM_RxLevSub"]);
                return true;
            }

            lac = (int?)tp["lte_td_SC_LAC"];
            ci = (int?)tp["lte_td_SC_CellID"];
            cell = mng.GetNearestTDCell(tp.DateTime, lac, ci,
                (int?)tp["lte_td_SC_UARFCN"], (int?)tp["lte_td_SC_CPI"], tp.Longitude, tp.Latitude);

            if (lac != null && ci != null)
            {
                param.Fill(cell, lac, ci, (float?)tp["lte_td_DM_PCCPCH_RSCP"]);
                return true;
            }

            lac = (int?)(ushort?)tp["lte_TAC"];
            ci = (int?)tp["lte_ECI"];
            cell = mng.GetNearestLTECell(tp.DateTime, lac, ci,
                (int?)tp["lte_EARFCN"], (int?)(short?)tp["lte_PCI"], tp.Longitude, tp.Latitude);

            if (lac != null && ci != null)
            {
                param.Fill(cell, lac, ci, (float?)tp["lte_RSRP"]);
                return true;
            }
            return false;
        }

        private bool fillFddData(COutParam param, List<LTEFddTestPoint> testPntLstFdd)
        {
            if (testPntLstFdd.Count > 0)
            {
                LTEFddTestPoint tp = testPntLstFdd[0];
                ICell cell;
                int? lac;
                int? ci;

                CellManager mng = CellManager.GetInstance();
                lac = (int?)tp["lte_fdd_wcdma_SysLAI"];
                ci = (int?)tp["lte_fdd_wcdma_SysCellID"];
                cell = mng.GetNearestWCell(tp.DateTime, lac, ci, (int?)tp["lte_fdd_wcdma_frequency"], (int?)tp["lte_fdd_wcdma_Reference_PSC"], tp.Longitude, tp.Latitude);
                if (lac != null && ci != null)
                {
                    param.Fill(cell, lac, ci, (float?)tp["lte_fdd_wcdma_TotalRSCP"]);
                    return true;
                }
            }
            return false;
        }

        public virtual bool getCellParam(DTFileDataManager file, DTData data, out COutParam param)
        {
            param = new COutParam();
            List<LTETestPointDetail> testPntLst = getTestPoint(file,
                data.DateTime.AddSeconds(0 - CSFBCauseQueryAna.CauseTimeSpan), data.DateTime);
            if (testPntLst.Count > 0)
            {
                return fillTddData(param, testPntLst);
            }
            else
            {
                List<LTEFddTestPoint> testPntLstFdd = getTestPointFdd(file,
                data.DateTime.AddSeconds(0 - CSFBCauseQueryAna.CauseTimeSpan), data.DateTime);
                return fillFddData(param, testPntLstFdd);
            }
        }

        public virtual bool getCellParam(DTFileDataManager file, DateTime startTime, DateTime endTime, out COutParam param)
        {
            param = new COutParam();
            List<LTETestPointDetail> testPntLst = getTestPoint(file, startTime, endTime);
            if (testPntLst.Count > 0)
            {
                return fillTddData(param, testPntLst);
            }
            else
            {
                List<LTEFddTestPoint> testPntLstFdd = getTestPointFdd(file, startTime, endTime);
                return fillFddData(param, testPntLstFdd);
            }
        }

        public virtual bool getCellParam(DTFileDataManager file, DateTime startTime, DateTime endTime,
            MasterCom.RAMS.Model.TestPoint.ECurrNetType modeType, out COutParam param)
        {
            param = new COutParam();
            List<LTETestPointDetail> testPntLst = getTestPoint(file, startTime, endTime, modeType);
            if (testPntLst.Count > 0)
            {
                return fillTddData(param, testPntLst);
            }
            else
            {
                List<LTEFddTestPoint> testPntLstFdd = getTestPointFdd(file, startTime, endTime, modeType);
                return fillFddData(param, testPntLstFdd);
            }
        }

        protected virtual List<LTETestPointDetail> getTestPoint(DTFileDataManager file, DTData data)
        {
            List<LTETestPointDetail> tpLst = new List<LTETestPointDetail>();
            DateTime startTime = data.DateTime.AddSeconds(0 - CSFBCauseQueryAna.CauseTimeSpan);
            foreach (TestPoint tp in file.TestPoints)
            {
                if (tp is LTETestPointDetail)
                {
                    if (tp.DateTime < startTime)
                        continue;
                    if (tp.DateTime > data.DateTime)
                        break;
                    tpLst.Add(tp as LTETestPointDetail);
                }
            }
            return tpLst;
        }

        protected virtual List<LTETestPointDetail> getTestPoint(DTFileDataManager file, DateTime startTime, DateTime endTime)
        {
            List<LTETestPointDetail> tpLst = new List<LTETestPointDetail>();
            foreach (TestPoint tp in file.TestPoints)
            {
                if (tp is LTETestPointDetail)
                {
                    if (tp.DateTime < startTime)
                        continue;
                    if (tp.DateTime > endTime)
                        break;
                    tpLst.Add(tp as LTETestPointDetail);
                }
            }
            return tpLst;
        }

        protected virtual List<LTETestPointDetail> getTestPoint(DTFileDataManager file, DateTime startTime, DateTime endTime,
            MasterCom.RAMS.Model.TestPoint.ECurrNetType modeType)
        {
            List<LTETestPointDetail> tpLst = new List<LTETestPointDetail>();
            foreach (TestPoint tp in file.TestPoints)
            {
                if (tp is LTETestPointDetail)
                {
                    if (tp.DateTime < startTime)
                        continue;
                    if (tp.DateTime > endTime)
                        break;
                    if (tp.NetworkType == modeType)
                        tpLst.Add(tp as LTETestPointDetail);
                }
            }
            return tpLst;
        }

        #region LTEFDD

        protected virtual List<LTEFddTestPoint> getTestPointFdd(DTFileDataManager file, DTData data)
        {
            List<LTEFddTestPoint> tpLst = new List<LTEFddTestPoint>();
            DateTime startTime = data.DateTime.AddSeconds(0 - CSFBCauseQueryAna.CauseTimeSpan);
            foreach (TestPoint tp in file.TestPoints)
            {
                if (tp is LTEFddTestPoint)
                {
                    if (tp.DateTime < startTime)
                        continue;
                    if (tp.DateTime > data.DateTime)
                        break;
                    tpLst.Add(tp as LTEFddTestPoint);
                }
            }
            return tpLst;
        }

        protected virtual List<LTEFddTestPoint> getTestPointFdd(DTFileDataManager file, DateTime startTime, DateTime endTime)
        {
            List<LTEFddTestPoint> tpLst = new List<LTEFddTestPoint>();
            foreach (TestPoint tp in file.TestPoints)
            {
                if (tp is LTEFddTestPoint)
                {
                    if (tp.DateTime < startTime)
                        continue;
                    if (tp.DateTime > endTime)
                        break;
                    tpLst.Add(tp as LTEFddTestPoint);
                }
            }
            return tpLst;
        }

        protected virtual List<LTEFddTestPoint> getTestPointFdd(DTFileDataManager file, DateTime startTime, DateTime endTime,
            MasterCom.RAMS.Model.TestPoint.ECurrNetType modeType)
        {
            List<LTEFddTestPoint> tpLst = new List<LTEFddTestPoint>();
            foreach (TestPoint tp in file.TestPoints)
            {
                if (tp is LTEFddTestPoint)
                {
                    if (tp.DateTime < startTime)
                        continue;
                    if (tp.DateTime > endTime)
                        break;
                    if (tp.NetworkType == modeType)
                        tpLst.Add(tp as LTEFddTestPoint);
                }
            }
            return tpLst;
        }

        #endregion

        protected virtual DTFileDataManager getMtMoFile(DTFileDataManager file)
        {
            foreach (DTFileDataManager fileMtMo in MainModel.GetInstance().DTDataManager.FileDataManagers)
            {
                foreach (FileInfo info in MainModel.GetInstance().FileInfos)
                {
                    if (info.ID == file.FileID && info.EventCount == fileMtMo.FileID && fileMtMo.MoMtFlag != file.MoMtFlag)
                    {
                        return fileMtMo;
                    }
                }
            }
            return null;
        }

        protected Event findEvent(DTFileDataManager file, DateTime startTime, DateTime endTime, List<int> evtLst)
        {
            Event rtEvt = null;
            foreach (Event evt in file.Events)
            {
                if (evt.DateTime < startTime)
                    continue;
                else if (evt.DateTime > endTime)
                    break;
                if (evtLst.Contains(evt.ID))
                {
                    rtEvt = evt;
                    break;
                }
            }
            return rtEvt;
        }

        protected Message findMessage(DTFileDataManager file, DateTime startTime, DateTime endTime, List<int> msgLst)
        {
            Message rtMsg = null;
            foreach (Message msg in file.Messages)
            {
                if (msg.DateTime < startTime)
                    continue;
                else if(msg.DateTime > endTime)
                    break;
                if (msgLst.Contains(msg.ID))
                {
                    rtMsg = msg;
                    break;
                }
            }
            return rtMsg;
        }

        protected Message findPrevMessage(DTFileDataManager file, DTData data, List<int> msgIDs)
        {
            int msgIdxEnd = file.DTDatas.IndexOf(data);
            for (int idx = msgIdxEnd; idx >= 0; idx--)
            {
                DTData tmp = file.DTDatas[idx];

                if (data is Message)
                {
                    Message msg = tmp as Message;
                    if (msg != null && msgIDs.Contains(msg.ID))
                    {
                        return msg;
                    }
                }
            }
            return null;
        }

        public abstract bool IsValid(DTFileDataManager file, Event e, int msgIdxBegin, int msgIdxEnd);

        public abstract string GetCauseDesc();

        public abstract string GetCauseSuguest();

        public abstract string GetPhenomenon();

        public virtual Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["BChecked"] = BChecked;
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                if (param.ContainsKey("BChecked"))
                {
                    BChecked = (bool)param["BChecked"];
                }
            }
        }

        #region ICloneable 成员

        public object Clone()
        {
            return this.MemberwiseClone();
        }

        #endregion
    }
}
