﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraBars;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Frame;

namespace MasterCom.RAMS.Func
{
    
    public partial class SQLConditionPanel : UserControl
    {
        private MainModel mainModel;
        private PopupControlContainer popupContainer;
        public string QueryBoxText
        {
            get
            {
                return this.sqlTextBox.Text;
            }
        }

        public string commitSqlStr { get; set; } = "";

        //过滤字段
        string strFilter = "|where|exec|insert|select|delete|update|chr|mid|master|truncate|char|declare|"
        + "iimporttime|istime|ietime|iduration|idistance|itllongitude|itllatitude|ibrlongitude|ibrlatitude|imsgnum|"
        + "isamplenum|ifilesize|strsavepath|strsampletbname|strsampletbname2|strmsgtbname|streventtbname|"
        + "iprojecttype|itesttype|iyear|ibatch|iareatype|iareaid|idevicetype|ifiletype|iservicetype|icarriertype|"
        + "iagentid|strdesc|idbvalue|isubtype1|isubtype2|suffix_week|suffix_day|logtbname|";

        string aKeyWordsFilter = "|ifileid|strfilename|ieventnum|istaffid|statstatus|";
        string bKeyWordsFilter = "istatus";

    
        public SQLConditionPanel()
        {
            InitializeComponent();
        }

        public SQLConditionPanel(PopupControlContainer popupContainer, MainModel mainModel)
        {
            InitializeComponent();
            this.mainModel = mainModel;
            this.popupContainer = popupContainer;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            if(sqlCheck())
            {
                mainModel.SubmitSqlTextBoxChanged(this);
                popupContainer.HidePopup();
            }
        }

        /* 判断是否出现不允许使用的字段或关键字 */
        private bool sqlCheck()
        {
            string sqlStr = this.sqlTextBox.Text.Trim().ToLower();
            commitSqlStr = sqlStr;

            foreach (string i in sqlStr.Split(new char[]{' ', '\'', '='}, StringSplitOptions.RemoveEmptyEntries))
            {
                if (strFilter.IndexOf(i + "|") > -1 && strFilter.IndexOf("|" + i) > -1)
                {
                    MessageBox.Show("不能输入列表中没有的字段或关键字！");
                    return false;
                }
                if (aKeyWordsFilter.IndexOf(i + "|") > -1 && aKeyWordsFilter.IndexOf("|" + i) > -1)
                {
                    commitSqlStr = commitSqlStr.Replace(i, "a." + i);
                }
                if(bKeyWordsFilter == i)
                {
                    commitSqlStr = commitSqlStr.Replace(i, "b." + i);
                }
                
            }
            return true;
        }


        /* 双击列表项时添加选中项到右边的textBox */
        private void sqlListBox_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            addToTextBox(this.sqlListBox, e);
        }

        private void fieldListBox_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            addToTextBox(this.fieldListBox, e);
        }

        private void addToTextBox(ListBox listBox, MouseEventArgs e)
        {
            int index = listBox.IndexFromPoint(e.Location);
            if (index != System.Windows.Forms.ListBox.NoMatches)
            {
                string insertItem = listBox.SelectedItem.ToString();
                this.sqlTextBox.Text = this.sqlTextBox.Text.Insert(this.sqlTextBox.SelectionStart, insertItem);
                this.sqlTextBox.Focus();
                this.sqlTextBox.Select(sqlTextBox.Text.Length, 0);
            }
        }

        private void btnClear_Click(object sender, EventArgs e)
        {
            this.sqlTextBox.Text = "";
        }

    }
}
