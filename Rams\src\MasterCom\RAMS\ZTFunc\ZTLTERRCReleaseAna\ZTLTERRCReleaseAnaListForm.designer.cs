﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTLTERRCReleaseAnaListForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(ZTLTERRCReleaseAnaListForm));
            this.ctxMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miReplay = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.ListViewRRCReleaseAna = new BrightIdeasSoftware.TreeListView();
            this.olvColumnStatSN = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnFileName = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnCellName = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnCellTAC = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnCellECI = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnCellCellID = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnCellEARFCN = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnCellPCI = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnTime = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnLongitude = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnLatitude = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnRSRP = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnSINR = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnEutranEARFCN = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnLastMRPCI = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnLastMRRSRP = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnReDirectCellName = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnCellDistance = new BrightIdeasSoftware.OLVColumn();
            this.ctxMenu.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.ListViewRRCReleaseAna)).BeginInit();
            this.SuspendLayout();
            // 
            // ctxMenu
            // 
            this.ctxMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miReplay,
            this.miExportExcel});
            this.ctxMenu.Name = "ctxMenu";
            this.ctxMenu.Size = new System.Drawing.Size(130, 48);
            this.ctxMenu.Opening += new System.ComponentModel.CancelEventHandler(this.ctxMenu_Opening);
            // 
            // miReplay
            // 
            this.miReplay.Name = "miReplay";
            this.miReplay.Size = new System.Drawing.Size(129, 22);
            this.miReplay.Text = "回放";
            this.miReplay.Click += new System.EventHandler(this.miReplay_Click);
            // 
            // miExportExcel
            // 
            this.miExportExcel.Name = "miExportExcel";
            this.miExportExcel.Size = new System.Drawing.Size(129, 22);
            this.miExportExcel.Text = "导出Excel";
            this.miExportExcel.Click += new System.EventHandler(this.miExportExcel_Click);
            // 
            // ListViewRRCReleaseAna
            // 
            this.ListViewRRCReleaseAna.AllColumns.Add(this.olvColumnStatSN);
            this.ListViewRRCReleaseAna.AllColumns.Add(this.olvColumnFileName);
            this.ListViewRRCReleaseAna.AllColumns.Add(this.olvColumnCellName);
            this.ListViewRRCReleaseAna.AllColumns.Add(this.olvColumnCellTAC);
            this.ListViewRRCReleaseAna.AllColumns.Add(this.olvColumnCellECI);
            this.ListViewRRCReleaseAna.AllColumns.Add(this.olvColumnCellCellID);
            this.ListViewRRCReleaseAna.AllColumns.Add(this.olvColumnCellEARFCN);
            this.ListViewRRCReleaseAna.AllColumns.Add(this.olvColumnCellPCI);
            this.ListViewRRCReleaseAna.AllColumns.Add(this.olvColumnTime);
            this.ListViewRRCReleaseAna.AllColumns.Add(this.olvColumnLongitude);
            this.ListViewRRCReleaseAna.AllColumns.Add(this.olvColumnLatitude);
            this.ListViewRRCReleaseAna.AllColumns.Add(this.olvColumnRSRP);
            this.ListViewRRCReleaseAna.AllColumns.Add(this.olvColumnSINR);
            this.ListViewRRCReleaseAna.AllColumns.Add(this.olvColumnEutranEARFCN);
            this.ListViewRRCReleaseAna.AllColumns.Add(this.olvColumnLastMRPCI);
            this.ListViewRRCReleaseAna.AllColumns.Add(this.olvColumnLastMRRSRP);
            this.ListViewRRCReleaseAna.AllColumns.Add(this.olvColumnReDirectCellName);
            this.ListViewRRCReleaseAna.AllColumns.Add(this.olvColumnCellDistance);
            this.ListViewRRCReleaseAna.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
                        | System.Windows.Forms.AnchorStyles.Left)
                        | System.Windows.Forms.AnchorStyles.Right)));
            this.ListViewRRCReleaseAna.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnStatSN,
            this.olvColumnFileName,
            this.olvColumnCellName,
            this.olvColumnCellTAC,
            this.olvColumnCellECI,
            this.olvColumnCellCellID,
            this.olvColumnCellEARFCN,
            this.olvColumnCellPCI,
            this.olvColumnTime,
            this.olvColumnLongitude,
            this.olvColumnLatitude,
            this.olvColumnRSRP,
            this.olvColumnSINR,
            this.olvColumnEutranEARFCN,
            this.olvColumnLastMRPCI,
            this.olvColumnLastMRRSRP,
            this.olvColumnReDirectCellName,
            this.olvColumnCellDistance});
            this.ListViewRRCReleaseAna.ContextMenuStrip = this.ctxMenu;
            this.ListViewRRCReleaseAna.Cursor = System.Windows.Forms.Cursors.Default;
            this.ListViewRRCReleaseAna.FullRowSelect = true;
            this.ListViewRRCReleaseAna.GridLines = true;
            this.ListViewRRCReleaseAna.HeaderWordWrap = true;
            this.ListViewRRCReleaseAna.IsNeedShowOverlay = false;
            this.ListViewRRCReleaseAna.Location = new System.Drawing.Point(1, 1);
            this.ListViewRRCReleaseAna.Name = "ListViewRRCReleaseAna";
            this.ListViewRRCReleaseAna.OwnerDraw = true;
            this.ListViewRRCReleaseAna.ShowGroups = false;
            this.ListViewRRCReleaseAna.Size = new System.Drawing.Size(1381, 501);
            this.ListViewRRCReleaseAna.TabIndex = 7;
            this.ListViewRRCReleaseAna.UseCompatibleStateImageBehavior = false;
            this.ListViewRRCReleaseAna.View = System.Windows.Forms.View.Details;
            this.ListViewRRCReleaseAna.VirtualMode = true;
            this.ListViewRRCReleaseAna.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.ListViewRRCReleaseAna_MouseDoubleClick);
            // 
            // olvColumnStatSN
            // 
            this.olvColumnStatSN.AspectName = "";
            this.olvColumnStatSN.HeaderFont = null;
            this.olvColumnStatSN.Text = "序号";
            this.olvColumnStatSN.Width = 40;
            // 
            // olvColumnFileName
            // 
            this.olvColumnFileName.HeaderFont = null;
            this.olvColumnFileName.Text = "文件名称";
            this.olvColumnFileName.Width = 120;
            // 
            // olvColumnCellName
            // 
            this.olvColumnCellName.HeaderFont = null;
            this.olvColumnCellName.Text = "小区名称";
            this.olvColumnCellName.Width = 100;
            // 
            // olvColumnCellTAC
            // 
            this.olvColumnCellTAC.HeaderFont = null;
            this.olvColumnCellTAC.Text = "TAC";
            // 
            // olvColumnCellECI
            // 
            this.olvColumnCellECI.HeaderFont = null;
            this.olvColumnCellECI.Text = "ECI";
            // 
            // olvColumnCellCellID
            // 
            this.olvColumnCellCellID.HeaderFont = null;
            this.olvColumnCellCellID.Text = "CellID";
            // 
            // olvColumnCellEARFCN
            // 
            this.olvColumnCellEARFCN.HeaderFont = null;
            this.olvColumnCellEARFCN.Text = "EARFCN";
            // 
            // olvColumnCellPCI
            // 
            this.olvColumnCellPCI.HeaderFont = null;
            this.olvColumnCellPCI.Text = "PCI";
            // 
            // olvColumnTime
            // 
            this.olvColumnTime.HeaderFont = null;
            this.olvColumnTime.Text = "时间";
            this.olvColumnTime.Width = 120;
            // 
            // olvColumnLongitude
            // 
            this.olvColumnLongitude.HeaderFont = null;
            this.olvColumnLongitude.Text = "经度";
            this.olvColumnLongitude.Width = 80;
            // 
            // olvColumnLatitude
            // 
            this.olvColumnLatitude.HeaderFont = null;
            this.olvColumnLatitude.Text = "纬度";
            this.olvColumnLatitude.Width = 80;
            // 
            // olvColumnRSRP
            // 
            this.olvColumnRSRP.HeaderFont = null;
            this.olvColumnRSRP.Text = "RSRP";
            this.olvColumnRSRP.Width = 50;
            // 
            // olvColumnSINR
            // 
            this.olvColumnSINR.HeaderFont = null;
            this.olvColumnSINR.Text = "SINR";
            this.olvColumnSINR.Width = 50;
            // 
            // olvColumnEutranEARFCN
            // 
            this.olvColumnEutranEARFCN.HeaderFont = null;
            this.olvColumnEutranEARFCN.Text = "重定向频点";
            this.olvColumnEutranEARFCN.Width = 70;
            // 
            // olvColumnLastMRPCI
            // 
            this.olvColumnLastMRPCI.HeaderFont = null;
            this.olvColumnLastMRPCI.Text = "MR上报PCI";
            this.olvColumnLastMRPCI.Width = 70;
            // 
            // olvColumnLastMRRSRP
            // 
            this.olvColumnLastMRRSRP.HeaderFont = null;
            this.olvColumnLastMRRSRP.Text = "MR上报RSRP";
            this.olvColumnLastMRRSRP.Width = 80;
            // 
            // olvColumnReDirectCellName
            // 
            this.olvColumnReDirectCellName.HeaderFont = null;
            this.olvColumnReDirectCellName.Text = "上报小区名称";
            this.olvColumnReDirectCellName.Width = 80;
            // 
            // olvColumnCellDistance
            // 
            this.olvColumnCellDistance.HeaderFont = null;
            this.olvColumnCellDistance.Text = "与主服小区距离(米)";
            this.olvColumnCellDistance.Width = 112;
            // 
            // ZTLTERRCReleaseAnaListForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1383, 502);
            this.Controls.Add(this.ListViewRRCReleaseAna);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Name = "ZTLTERRCReleaseAnaListForm";
            this.Text = "LTE重定向异常分析结果";
            this.ctxMenu.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.ListViewRRCReleaseAna)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.ContextMenuStrip ctxMenu;
        private System.Windows.Forms.ToolStripMenuItem miExportExcel;
        private System.Windows.Forms.ToolStripMenuItem miReplay;
        private BrightIdeasSoftware.TreeListView ListViewRRCReleaseAna;
        private BrightIdeasSoftware.OLVColumn olvColumnStatSN;
        private BrightIdeasSoftware.OLVColumn olvColumnCellName;
        private BrightIdeasSoftware.OLVColumn olvColumnCellTAC;
        private BrightIdeasSoftware.OLVColumn olvColumnCellCellID;
        private BrightIdeasSoftware.OLVColumn olvColumnCellEARFCN;
        private BrightIdeasSoftware.OLVColumn olvColumnCellPCI;
        private BrightIdeasSoftware.OLVColumn olvColumnTime;
        private BrightIdeasSoftware.OLVColumn olvColumnRSRP;
        private BrightIdeasSoftware.OLVColumn olvColumnLongitude;
        private BrightIdeasSoftware.OLVColumn olvColumnEutranEARFCN;
        private BrightIdeasSoftware.OLVColumn olvColumnLatitude;
        private BrightIdeasSoftware.OLVColumn olvColumnSINR;
        private BrightIdeasSoftware.OLVColumn olvColumnFileName;
        private BrightIdeasSoftware.OLVColumn olvColumnCellECI;
        private BrightIdeasSoftware.OLVColumn olvColumnLastMRPCI;
        private BrightIdeasSoftware.OLVColumn olvColumnLastMRRSRP;
        private BrightIdeasSoftware.OLVColumn olvColumnReDirectCellName;
        private BrightIdeasSoftware.OLVColumn olvColumnCellDistance;

    }
}