﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.BackgroundFunc
{
    public class NbIotStationAcceptManager : MultiStationAutoAcceptManager
    {
        public NBIotCellAcceptFileInfo NBIotAcceptFileInfo { get; set; }

        protected new virtual List<NbIotCellAcceptKpiAna> getAcceptAnaList(LTEBTSType btsType)
        {
            List<NbIotCellAcceptKpiAna> acceptAnaList = null;
            if (btsType == LTEBTSType.Outdoor)
            {
                acceptAnaList = new List<NbIotCellAcceptKpiAna>()
                {
                   new NBIotAcpAutoAttachRate(),
                   new NBIotAcpAutoRrcRate(),
                   new NBIotAcpAutoPingDelay(),
                   new NBIotAcpAutoULSpeed(),
                   new NBIotAcpAutoDLSpeed(),
                   new NBIotAcpAutoCoverPicture(),
                };
            }
            else
            {
                //移动白皮书暂未写NB室分站
            }
            return acceptAnaList;
        }

        /// <summary>
        /// 分析文件
        /// </summary>
        /// <param name="fileInfo"></param>
        /// <param name="fileManager"></param>
        protected override void anaWithOtherFile(FileInfo fileInfo, DTFileDataManager fileManager)
        {
            LTECell targetCell = GetFileTestCell(fileManager);
            if (targetCell == null)
            {
                reportInfo(string.Format("文件{0}未找到目标小区", fileInfo.Name));
                return;
            }

            List<NbIotCellAcceptKpiAna> acceptAnaList = getAcceptAnaList(targetCell.Type);//根据小区类型获取业务分析类
            foreach (NbIotCellAcceptKpiAna acp in acceptAnaList)
            {
                if (acp.IsValidFile(fileInfo))
                {
                    doStatWithData(acp, fileInfo, fileManager, targetCell);
                }
            }
        }

        protected void doStatWithData(NbIotCellAcceptKpiAna acp, FileInfo fileInfo, DTFileDataManager fileManager, LTECell targetCell)
        {
            if (NBIotAcceptFileInfo == null)
            {
                NBIotAcceptFileInfo = new NBIotCellAcceptFileInfo(fileInfo, targetCell);
            }
            Dictionary<NBIotKpiKey, object> kpiInfoDic = acp.GetFileKpiInfos(fileInfo, fileManager, targetCell);
            foreach (NBIotKpiKey key in kpiInfoDic.Keys)
            {
                object valueObj = kpiInfoDic[key];
                if (valueObj is double)
                {
                    double valueDouble = (double)valueObj;
                    if (valueDouble == double.MinValue)
                    {
                        valueObj = double.NaN;
                    }
                    else
                    {
                        valueObj = Math.Round(valueDouble, 4);
                    }
                }

                NBIotAcceptFileInfo.AcceptKpiDic.Add((uint)key, valueObj);
            }
        }

        #region 获取测试文件对应的主服小区
        /// <summary>
        /// 获取测试文件对应的小区
        /// </summary>
        /// <param name="fileManager"></param>
        /// <returns></returns>
        public static new LTECell GetFileTestCell(DTFileDataManager fileManager)
        {
            bool isHandoverFile = false;
            if (fileManager.FileName.Contains("重选"))
            {
                isHandoverFile = true;
            }

            LTECell targeCell = null;
            foreach (TestPoint tp in fileManager.TestPoints)
            {
                LTECell cell = GetTpSrcCell(tp);
                if (isSrcTestCell(cell, fileManager.FileName, isHandoverFile))
                {
                    targeCell = cell;
                    break;
                }
            }
            return targeCell;
        }

        /// <summary>
        /// 根据采样点匹配主服小区
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        public static new LTECell GetTpSrcCell(TestPoint tp)
        {
            LTECell cell = null;
            if (tp is LTETestPointDetail)
            {
                cell = StationAcceptCellHelper_XJ.Instance.GetLTECellByTacEci(tp);

                int? earfcn = (int?)tp["lte_EARFCN"];
                int? pci = (int?)(short?)tp["lte_PCI"];

                if (cell == null && earfcn != null && pci != null)
                {
                    var time = StationAcceptCellHelper_XJ.Instance.GetValidDate(tp.DateTime);
                    cell = getLTECellByEARFCNPCI(time, earfcn, pci, tp.Longitude, tp.Latitude, tp.FileName);
                }
            }
            return cell;
        }

        /// <summary>
        /// 根据频点PCI匹配小区
        /// </summary>
        /// <param name="time"></param>
        /// <param name="earfcn"></param>
        /// <param name="pci"></param>
        /// <param name="longitude"></param>
        /// <param name="latitude"></param>
        /// <param name="fileName"></param>
        /// <returns></returns>
        private static LTECell getLTECellByEARFCNPCI(DateTime time, int? earfcn, int? pci, double longitude
           , double latitude, string fileName)
        {
            if (earfcn == null || pci == null)
            {
                return null;
            }
            List<LTECell> cells = CellManager.GetInstance().GetLTECellListByEARFCNPCI(time, earfcn, pci);
            if (cells != null && cells.Count > 0)
            {
                if (!string.IsNullOrEmpty(fileName))
                {
                    LTECell cell = getInDistLimitCell(time, longitude, latitude, cells, fileName);
                    if (cell != null)
                    {
                        return cell;
                    }
                }

                return CellManager.GetInstance().GetNearestLTECellByEARFCNPCI(time, earfcn, pci, longitude, latitude);
            }
            return null;
        }

        private static LTECell getInDistLimitCell(DateTime time, double longitude, double latitude, List<LTECell> cells, string fileName)
        {
            foreach (LTECell cell in cells)
            {
                if (fileName.Contains(cell.Name) && cell.ValidPeriod.Contains(time)
                    && (longitude <= 0 || latitude <= 0
                    || !CellManager.GetInstance().SystemConfigInfo.distLimit
                    || cell.GetDistance(longitude, latitude) <= CD.MAX_COV_DISTANCE_LTE))
                {
                    return cell;
                }
            }
            return null;
        }

        /// <summary>
        /// 根据文件名判断是否为测试文件的主服小区(后续根据文件命名规范修改此判断)
        /// </summary>
        /// <param name="cell"></param>
        /// <param name="fileName"></param>
        /// <returns></returns>
        private static bool isSrcTestCell(LTECell cell, string fileName, bool isHandoverFile)
        {
            if (cell == null)
            {
                return false;
            }
            string keyStr = cell.Name.ToUpper();
            if (isHandoverFile && cell.BelongBTS != null)
            {
                keyStr = cell.BelongBTS.Name.ToUpper();
            }
            if (fileName.ToUpper().Contains(keyStr.Trim()))
            {
                return true;
            }
            fileName = fileName.Trim().ToUpper();
            string cellName = cell.Name.Trim().ToUpper();
            //判断文件是否包含小区名
            if (fileName.Contains(cellName))
            {
                return true;
            }
            return false;
        }
        #endregion

        protected override void anaWithHandoverFile(FileInfo fileInfo, DTFileDataManager fileManager)
        {
            //暂无此情形
        }

        protected override void anaWithLeakOut_ScanFile(FileInfo fileInfo, DTFileDataManager fileManager)
        {
            //暂无此情形
        }
    }

    public class NbIotStationAcceptAutoSet : StationAcceptAutoSetBase
    {
        public NbIotStationAcceptAutoSet()
        {
            FtpSetInfo = new FtpSettings();
        }

        //暂未使用
        public FtpSettings FtpSetInfo { get; set; }
    }

    public static class NbIotStaionAcceptResultHelper
    {
        public static NbIotOutDoorBtsAcceptInfo GetOutDoorBtsResultByBgData(List<BackgroundResult> bgResultList
            , int enodeBid)
        {
            NbIotOutDoorBtsAcceptInfo btsAcceptInfo = null;
            foreach (BackgroundResult bgResult in bgResultList)
            {
                if (bgResult.StrDesc == "室外")
                {
                    btsAcceptInfo = addOutdoorCellAcceptInfo(enodeBid, btsAcceptInfo, bgResult);
                }
            }
            if (btsAcceptInfo != null)
            {
                btsAcceptInfo.CheckBtsIsAccordAccept();
            }
            return btsAcceptInfo;
        }

        private static NbIotOutDoorBtsAcceptInfo addOutdoorCellAcceptInfo(int enodeBid, NbIotOutDoorBtsAcceptInfo btsAcceptInfo, BackgroundResult bgResult)
        {
            DateTime bgResultTime = JavaDate.GetDateTimeFromMilliseconds(bgResult.ISTime * 1000L);
            LTECell nbiotCell = StationAcceptCellHelper_XJ.Instance.GetLTECellByTacEci(bgResultTime, bgResult.LAC, bgResult.CI);
            if (nbiotCell == null || nbiotCell.BelongBTS.BTSID != enodeBid)
            {
                reportBackgroundInfo(string.Format("预处理文件{0}未关联到目标小区。", bgResult.FileName));
            }
            else
            {
                if (btsAcceptInfo == null)
                {
                    btsAcceptInfo = new NbIotOutDoorBtsAcceptInfo(nbiotCell.BelongBTS);
                    btsAcceptInfo.AccpetTimePeriod.SetBeginTime(bgResultTime);
                }
                btsAcceptInfo.AccpetTimePeriod.SetEndTime(bgResultTime);

                try
                {
                    byte[] bytes = bgResult.GetImageValueBytes();
                    Dictionary<uint, object> kpiDic = NewBlackBlock.KeyValueImageParser.FromImage(bytes);

                    NbIotOutDoorCellAcceptInfo cellAcceptInfo;
                    if (!btsAcceptInfo.CellsAcceptDic.TryGetValue(nbiotCell.CellID, out cellAcceptInfo))
                    {
                        cellAcceptInfo = new NbIotOutDoorCellAcceptInfo(nbiotCell);
                        btsAcceptInfo.CellsAcceptDic.Add(nbiotCell.CellID, cellAcceptInfo);
                    }
                    cellAcceptInfo.AddAcceptKpiInfo(kpiDic);
                }
                catch (Exception ex)
                {
                    reportBackgroundError(ex);
                }
            }

            return btsAcceptInfo;
        }

        public static NBIotInDoorBtsAcceptInfo GetInDoorBtsResultByBgData(List<BackgroundResult> bgResultList
            , int enodeBid)
        {
            NBIotInDoorBtsAcceptInfo btsAcceptInfo = null;
            foreach (BackgroundResult bgResult in bgResultList)
            {
                if (bgResult.StrDesc != "室外")
                {
                    btsAcceptInfo = addIndoorCellAcceptInfo(enodeBid, btsAcceptInfo, bgResult);
                }
            }
            if (btsAcceptInfo != null)
            {
                btsAcceptInfo.CheckBtsIsAccordAccept();
            }
            return btsAcceptInfo;
        }

        private static NBIotInDoorBtsAcceptInfo addIndoorCellAcceptInfo(int enodeBid, NBIotInDoorBtsAcceptInfo btsAcceptInfo, BackgroundResult bgResult)
        {
            DateTime bgResultTime = JavaDate.GetDateTimeFromMilliseconds(bgResult.ISTime * 1000L);
            LTECell nbiotCell = StationAcceptCellHelper_XJ.Instance.GetLTECellByTacEci(bgResultTime, bgResult.LAC, bgResult.CI);
            if (nbiotCell == null || nbiotCell.BelongBTS.BTSID != enodeBid)
            {
                reportBackgroundInfo(string.Format("预处理文件{0}未关联到目标小区。", bgResult.FileName));
            }
            else
            {
                if (btsAcceptInfo == null)
                {
                    btsAcceptInfo = new NBIotInDoorBtsAcceptInfo(nbiotCell.BelongBTS);
                    btsAcceptInfo.AccpetTimePeriod.SetBeginTime(bgResultTime);
                }
                btsAcceptInfo.AccpetTimePeriod.SetEndTime(bgResultTime);

                try
                {
                    byte[] bytes = bgResult.GetImageValueBytes();
                    Dictionary<uint, object> kpiDic = NewBlackBlock.KeyValueImageParser.FromImage(bytes);

                    NBIotInDoorCellAcceptInfo cellAcceptInfo;
                    if (!btsAcceptInfo.CellsAcceptDic.TryGetValue(nbiotCell.CellID, out cellAcceptInfo))
                    {
                        cellAcceptInfo = new NBIotInDoorCellAcceptInfo(nbiotCell);
                        btsAcceptInfo.CellsAcceptDic.Add(nbiotCell.CellID, cellAcceptInfo);
                    }
                    cellAcceptInfo.AddAcceptKpiInfo(bgResult.FileName, kpiDic);
                }
                catch (Exception ex)
                {
                    reportBackgroundError(ex);
                }
            }

            return btsAcceptInfo;
        }

        public static void reportBackgroundInfo(string str)
        {
            BackgroundFuncManager.GetInstance().ReportBackgroundInfo(str);
        }

        public static void reportBackgroundError(Exception ex)
        {
            BackgroundFuncManager.GetInstance().ReportBackgroundError(ex);
        }
    }
}
