﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    [Serializable]
    public class NRWeakCoverCause : NRLowSpeedCauseBase
    {
        public override string Name
        {
            get
            {
                return "弱覆盖";
            }
        }

        public float RSRPMax { get; set; } = -95;

        public override string Desc
        {
            get
            {
                return string.Format("主服和邻区信号强度都≤{0}", RSRPMax);
            }
        }

        public override string Suggestion
        {
            get { return null; }
        }

        public override void Judge(NRLowSpeedSeg segItem, List<Event> evts, List<TestPoint> allTP, NRTpManagerBase nRCond)
        {
            foreach (TestPoint pnt in segItem.TestPoints)
            {
                if (!segItem.IsNeedJudge(pnt))
                {
                    continue;
                }
                float? rsrp = nRCond.GetSCellRsrp(pnt);
                if (rsrp <= RSRPMax)
                {
                    bool nGreater = getGreater(pnt, ref rsrp, nRCond);
                    if (nGreater)
                    {
                        continue;
                    }
                    dealLowSpeedSeg(segItem, pnt, nRCond);
                    if (!segItem.NeedJudge)
                    {
                        return;
                    }
                }
            }
        }

        private bool getGreater(TestPoint pnt, ref float? rsrp, NRTpManagerBase nRCond)
        {
            bool nGreater = false;
            for (int i = 0; i < 10; i++)
            {
                float? objV = nRCond.GetNCellRsrp(pnt, i);
                if (objV == null)
                {
                    continue;
                }
                rsrp = float.Parse(objV.ToString());
                if (rsrp > RSRPMax)
                {
                    nGreater = true;
                    break;
                }
            }

            return nGreater;
        }

        private void dealLowSpeedSeg(NRLowSpeedSeg segItem, TestPoint pnt, NRTpManagerBase nRCond)
        {
            //foreach (NRLowSpeedCauseBase subReason in SubCauses)
            //{
            //    if (!segItem.IsNeedJudge(pnt))
            //    {
            //        break;
            //    }
            //    subReason.JudgeSinglePoint(segItem, pnt, nRCond);
            //}
            if (segItem.IsNeedJudge(pnt))
            {
                NRLowSpeedUnknowReason r = new NRLowSpeedUnknowReason();
                r.Parent = this;
                segItem.SetReason(new NRLowSpeedPointDetail(pnt, r, nRCond));
            }
        }

        public override Dictionary<string, object> CfgParam
        {
            get
            {
                Dictionary<string, object> paramDic = new Dictionary<string, object>();
                paramDic["TypeName"] = this.GetType().FullName;

                paramDic["rsrpMax"] = this.RSRPMax;

                //List<object> list = new List<object>();
                //foreach (NRLowSpeedCauseBase cause in SubCauses)
                //{
                //    list.Add(cause.CfgParam);
                //}
                //paramDic["SubCauseSet"] = list;

                return paramDic;

            }
            set
            {
                if (value == null)
                {
                    return;
                }

                this.RSRPMax = (float)value["rsrpMax"];

                //SubCauses = new List<NRLowSpeedCauseBase>();
                //List<object> list = value["SubCauseSet"] as List<object>;
                //foreach (object item in list)
                //{
                //    Dictionary<string, object> dic = item as Dictionary<string, object>;
                //    string typeName = dic["TypeName"].ToString();
                //    System.Reflection.Assembly assembly = System.Reflection.Assembly.GetExecutingAssembly();
                //    NRLowSpeedCauseBase cause = (NRLowSpeedCauseBase)assembly.CreateInstance(typeName);
                //    cause.CfgParam = dic;
                //    AddSubReason(cause);
                //}
            }
        }

    }
}
