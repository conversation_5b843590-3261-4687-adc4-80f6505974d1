﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraCharts;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc.TestDepth
{
    public partial class TestDepthResultForm : MinCloseForm
    {
        public TestDepthResultForm()
            : base(MainModel.GetInstance())
        {
            InitializeComponent();
        }
        List<TestDepthDetail> details = new List<TestDepthDetail>();
        public void FillData(List<TestDepthDetail> details, TestDepthDetail summaryDetail)
        {
            this.details = details;
            List<TestDepthDetail> temp = new List<TestDepthDetail>(details);
            if (summaryDetail != null)
            {
                temp.Add(summaryDetail);
            }
            gridControl.DataSource = temp;
            gridControl.RefreshDataSource();
            makeSureLayerVisible();
            layer.RegionGridDetails = details;
            layer.Invalidate();
        }


        private void makeSureLayerVisible()
        {
            MapForm mf = MainModel.MainForm.GetMapForm();
            if (mf != null)
            {
                layer = mf.GetCustomLayer(typeof(TestDepthGridLayer)) as TestDepthGridLayer;
                if (layer == null)
                {
                    layer = new TestDepthGridLayer(mf.GetMapOperation(), "栅格测试深度");
                    mf.AddTempCustomLayer(layer);
                }
            }
        }

        TestDepthGridLayer layer = null;

        private void chkHistory_CheckedChanged(object sender, EventArgs e)
        {
            colorHistory.Enabled = layer.DrawHistory = chkHistory.Checked;
            layer.Invalidate();
        }

        private void chkEstimate_CheckedChanged(object sender, EventArgs e)
        {
            colorEstimate.Enabled = layer.DrawEstimate = chkEstimate.Checked;
            layer.Invalidate();
        }

        private void colorHistory_EditValueChanged(object sender, EventArgs e)
        {
            layer.HistoryColor = colorHistory.Color;
            layer.Invalidate();
        }

        private void colorEstimate_EditValueChanged(object sender, EventArgs e)
        {
            layer.EstimateColor = colorEstimate.Color;
            layer.Invalidate();
        }

        private void miExportExcel_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(gridView);
        }

        private void gridView_DoubleClick(object sender, EventArgs e)
        {
            DevExpress.Utils.DXMouseEventArgs xe = e as DevExpress.Utils.DXMouseEventArgs;
            DevExpress.XtraGrid.Views.Grid.ViewInfo.GridHitInfo info = gridView.CalcHitInfo(xe.Location);
            if (info.InRow)
            {
                TestDepthDetail item = gridView.GetRow(info.RowHandle) as TestDepthDetail;
                layer.RegionGridDetails.Clear();
                layer.RegionGridDetails.Add(item);
                layer.Invalidate();
            }
           
        }

        private void miShowAll_Click(object sender, EventArgs e)
        {
            layer.RegionGridDetails = details;
            layer.Invalidate();
        }

        //private void fillProfileTabChart(List<object> profileList, DevExpress.XtraTab.XtraTabControl profileChart, string titleName)
        //{
        //    profileChart.TabPages.Clear();

        //    if (profileList.Count == 0)
        //    {
        //        return;
        //    }

        //    ChartControl chart = new ChartControl();
        //    chart.Legend.Visible = false;
        //    chart.Titles.Clear();
        //    chart.Series.Clear();
        //    ChartTitle title = new ChartTitle();
        //    Series series = null;

        //    title.Text = titleName;
        //    chart.Titles.Add(title);

        //    //foreach ( profileInfo in profileList)
        //    //{
        //    //    if (profileInfo.GroupName.IndexOf("网格") >= 0)
        //    //    {
        //    //        series = new Series(title.Text, DevExpress.XtraCharts.ViewType.Bar);
        //    //        series.Points.Add(new SeriesPoint(profileInfo.GroupName, profileInfo.EventFinishedPercent));
        //    //        chart.Series.Add(series);
        //    //    }
        //    //}

        //    if (chart.Series.Count > 0)
        //    {
        //        XYDiagram diagram = chart.Diagram as XYDiagram;
        //        diagram.EnableAxisXScrolling = true;
        //        diagram.EnableAxisXZooming = true;
        //        series.View.Color = Color.DodgerBlue;
        //        ((SideBySideBarSeriesView)series.View).FillStyle.FillMode = FillMode.Solid;
        //        chart.Dock = DockStyle.Fill;

        //        //XtraTabPage page = profileChart.TabPages.Add(titleName);
        //        //page.Controls.Add(chart);
        //    }
        //}
        

    }
}
