﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.NewBlackBlock;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.BackgroundFunc
{
    public class CellAcceptFileInfo_TianJin
    {
        public CellAcceptFileInfo_TianJin(FileInfo fileInfo, LTECell cell)
        {
            File = fileInfo;
            Cell = cell;
            AcceptKpiDic = new Dictionary<uint, object>();
        }

        public FileInfo File { get; set; }

        public int FileId
        {
            get
            {
                if (File != null)
                {
                    return File.ID;
                }
                return 0;
            }
        }

        public string FileName
        {
            get
            {
                if (File != null)
                {
                    return File.Name;
                }
                return "";
            }
        }

        public LTECell Cell { get; set; }

        public int CellId
        {
            get
            {
                if (Cell != null)
                {
                    return Cell.CellID;
                }
                return 0;
            }
        }

        public string CellName
        {
            get
            {
                if (Cell != null)
                {
                    return Cell.Name;
                }
                return "";
            }
        }

        public string BtsName
        {
            get
            {
                if (Cell != null)
                {
                    return Cell.BTSName;
                }
                return "";
            }
        }

        public int BtsId
        {
            get
            {
                if (Cell != null)
                {
                    return Cell.BelongBTS.BTSID;
                }
                return 0;
            }
        }

        public int TAC
        {
            get
            {
                if (Cell != null)
                {
                    return Cell.TAC;
                }
                return 0;
            }
        }

        public int ECI
        {
            get
            {
                if (Cell != null)
                {
                    return Cell.ECI;
                }
                return 0;
            }
        }

        public Dictionary<uint, object> AcceptKpiDic { get; set; }
    }
}
