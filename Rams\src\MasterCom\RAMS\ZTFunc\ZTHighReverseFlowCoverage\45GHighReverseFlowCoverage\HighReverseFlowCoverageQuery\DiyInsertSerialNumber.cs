﻿
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc.ZTHighReverseFlowCoverage._45GHighReverseFlowCoverage
{
    /// <summary>
    /// 只插入新增的编号
    /// </summary>
    public class DiyInsertSerialNumber : DiySqlMultiNonQuery
    {
        public string ErrMsg { get; protected set; }
        protected List<SerialNumber> addList = null;
        public DiyInsertSerialNumber(List<SerialNumber> addList)
        {
            MainDB = true;
            this.addList = addList;
        }

        protected override void query()
        {
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, userName, password, dbid > 0 ? dbid : MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrMsg = "连接服务器失败!";
                    return;
                }
                WaitBox.Text = $"正在向数据库导入{Name}数据......";
                queryInThread(clientProxy);
            }
            catch (Exception ee)
            {
                ErrMsg = ee.Message + ee.Source + ee.StackTrace;
                MessageBox.Show(ErrorInfo);
            }
            finally
            {
                clientProxy.Close();
            }
        }

        protected override void queryInThread(object o)
        {
            base.queryInThread(o);
            System.Threading.Thread.Sleep(200);
            WaitBox.Text = $"导入{Name}完毕.....";
        }

        protected override string getSqlTextString()
        {
            StringBuilder strb = new StringBuilder();
            string tableName = DiyQuerySerialNumber.TableName;

            foreach (SerialNumber info in addList)
            {
                //string sql = $"insert into [{tableName}]([id],[city],[cityid],[enodebid],[serialnumber]," +
                //$"[longitude],[latitude]) " +
                //$"values({info.ID},'{info.City}',{info.CityID},{info.Enodebid}," +
                //$"'{info.Number}',{info.ILongitude},{info.ILatitude});";
                string sql = $"insert into [{tableName}] " +
                    $"values({info.ID},'{info.City}',{info.CityID},{info.Enodebid}," +
                    $"'{info.Number}',{info.Bts.ILongitude},{info.Bts.ILatitude});";
                strb.Append(sql);
            }

            return strb.ToString();
        }
    }

}
