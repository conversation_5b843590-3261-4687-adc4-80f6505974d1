﻿using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTStationAcceptance_XJ
{
    public abstract class NRStationAcceptBase : StationAcceptBase
    {
        protected NRCellServiceInfo curCellServiceInfo;
        protected NRBtsServiceInfo curBtsServiceInfo;

        protected override void analyzeFile(FileInfo fileInfo, DTFileDataManager fileManager, BtsInfoBase bts, CellInfoBase cell, StationAcceptConditionBase condition)
        {
            NRBtsInfo nrBtsInfo = bts as NRBtsInfo;
            NRCellInfo nrCellInfo = cell as NRCellInfo;
            NRStationAcceptCondition nrCondition = condition as NRStationAcceptCondition;

            if (nrCondition.NRServiceType == NRServiceName.NSA)
            {
                curCellServiceInfo = nrCellInfo.NSAInfo;
                curBtsServiceInfo = nrBtsInfo.NSABtsInfo;
            }
            else if (nrCondition.NRServiceType == NRServiceName.SA)
            {
                curCellServiceInfo = nrCellInfo.SAInfo;
                curBtsServiceInfo = nrBtsInfo.SABtsInfo;
            }
            else
            {
                return;
            }

            analyzeNRFile(fileInfo, fileManager, nrBtsInfo, nrCellInfo, nrCondition);

            verifyfileResult(fileInfo, fileManager, nrBtsInfo, nrCellInfo, nrCondition);
        }

        protected virtual void analyzeNRFile(FileInfo fileInfo, DTFileDataManager fileManager, NRBtsInfo bts, NRCellInfo cell, NRStationAcceptCondition condition)
        {

        }

        protected virtual void verifyfileResult(FileInfo fileInfo, DTFileDataManager fileManager, NRBtsInfo bts, NRCellInfo cell, NRStationAcceptCondition condition)
        {

        }
    }

    public abstract class NRStationAcceptSample : NRStationAcceptBase
    {
        protected ICell getMainCell(TestPoint tp)
        {
            NRCell iCell = StationAcceptCellHelper_XJ.Instance.GetNRCell(tp);
            return iCell;
        }

        protected override void analyzeNRFile(FileInfo fileInfo, DTFileDataManager fileManager, NRBtsInfo bts, NRCellInfo cell, NRStationAcceptCondition condition)
        {
            foreach (TestPoint tp in fileManager.TestPoints)
            {
                ICell iCell = getMainCell(tp);
                if (iCell != null && iCell == cell.Cell)
                {
                    addData(tp, cell, condition);
                }
            }
        }

        protected abstract void addData(TestPoint tp, NRCellInfo cell, NRStationAcceptCondition condition);
    }


    /// <summary>
    /// 分析速率业务基类
    /// </summary>
    public abstract class NRStationAcceptThroughput : NRStationAcceptSample
    {
        protected override void verifyfileResult(FileInfo fileInfo, DTFileDataManager fileManager, NRBtsInfo bts, NRCellInfo cell, NRStationAcceptCondition condition)
        {
            ThroughputStandard standard = getThroughputStandard(fileInfo.Name, condition);
            if (standard == null)
            {
                return;
            }

            verifyThroughput(cell, condition, standard);
        }

        /// <summary>
        /// 根据文件名获取吞吐率判断标准
        /// </summary>
        protected ThroughputStandard getThroughputStandard(string fileName, NRStationAcceptCondition condition)
        {
            string token = NRStationAcceptFileNameHelper.GetThroughputFileToken(fileName, 7);
            if (string.IsNullOrEmpty(token))
            {
                log.Error($"{fileName}文件没有找到对应的速率标准,请检查文件命名");
                return null;
            }

            ThroughputStandard standard = condition.Standard.ThroughputStandardList.Find(x => x.Token == token.Trim());
            return standard;
        }

        /// <summary>
        /// 判断速率是否有效
        /// </summary>
        protected abstract void verifyThroughput(NRCellInfo cell, NRStationAcceptCondition condition, ThroughputStandard standard);

        protected void dealValidGoodThroughput(FtpPointInfo info, ThroughputStandard.DataInfo standard)
        {
            if (info.Rsrp.Divisor > 0 && info.Rsrp.Data > -80)
            {
                info.Rsrp.IsValid = true;
            }
            if (info.Sinr.Data > 18)
            {
                info.Sinr.IsValid = true;
            }
            dealValidThroughput(info, standard, ThroughputStandard.PointType.Good);
        }

        protected void dealValidMiddleThroughput(FtpPointInfo info, ThroughputStandard.DataInfo standard)
        {
            if (info.Rsrp.Divisor > 0 && info.Rsrp.Data >= -90 && info.Rsrp.Data < -80)
            {
                info.Rsrp.IsValid = true;
            }
            if (info.Sinr.Data >= 5 && info.Sinr.Data < 10)
            {
                info.Sinr.IsValid = true;
            }
            dealValidThroughput(info, standard, ThroughputStandard.PointType.Middle);
        }

        protected void dealValidBadThroughput(FtpPointInfo info, ThroughputStandard.DataInfo standard)
        {
            if ((info.Rsrp.Divisor > 0 && info.Rsrp.Data < -90) || info.Sinr.Data < 0)
            {
                info.Rsrp.IsValid = true;
                info.Sinr.IsValid = true;
            }
            dealValidThroughput(info, standard, ThroughputStandard.PointType.Bad);
        }

        private void dealValidThroughput(FtpPointInfo info, ThroughputStandard.DataInfo standard, ThroughputStandard.PointType pointType)
        {
            bool isValid = standard.JudgeThroughput(pointType, info.Throughput.Data);
            if (isValid)
            {
                info.Throughput.IsValid = true;
            }
            info.Judge();
        }
    }

    public abstract class NRStationAcceptEvent : NRStationAcceptBase
    {
        protected List<int> evtSuccList;
        protected List<int> evtRequList;

        protected override void analyzeNRFile(FileInfo fileInfo, DTFileDataManager fileManager, NRBtsInfo bts, NRCellInfo cell, NRStationAcceptCondition condition)
        {
            SuccessRateKpiInfo eventInfo = initEventKpiInfo(cell, condition);
            dealEvtDataList(eventInfo, fileManager.Events);
        }

        protected virtual void dealEvtDataList(SuccessRateKpiInfo eventInfo, List<Event> evtList)
        {
            foreach (Event evt in evtList)
            {
                if (evtRequList.Contains(evt.ID))
                {
                    eventInfo.RequestCnt++;
                }
                else if (evtSuccList.Contains(evt.ID))
                {
                    eventInfo.SucceedCnt++;
                }
            }
            eventInfo.CalculateFailEvt();
        }

        protected abstract SuccessRateKpiInfo initEventKpiInfo(NRCellInfo cell, NRStationAcceptCondition condition);
    }

    public abstract class NRStationAcceptCoverPic : StationAcceptCoverPic
    {
        protected NRStationAcceptCoverPic(string picPath)
            : base(picPath)
        {
        }

        protected override void analyzeFile(FileInfo fileInfo, DTFileDataManager fileManager, BtsInfoBase bts, CellInfoBase cell, StationAcceptConditionBase condition)
        {
            NRBtsInfo nrBtsInfo = bts as NRBtsInfo;
            NRCellInfo nrCellInfo = cell as NRCellInfo;
            NRStationAcceptCondition nrCondition = condition as NRStationAcceptCondition;
            NRCellServiceInfo cellTypeInfo = getNRCellServiceInfo(nrCellInfo, nrCondition);
            analyzeFile(fileInfo, fileManager, nrBtsInfo, cellTypeInfo, nrCondition);
        }

        protected virtual void analyzeFile(FileInfo fileInfo, DTFileDataManager fileManager, NRBtsInfo bts, NRCellServiceInfo cellTypeInfo, NRStationAcceptCondition condition)
        {

        }

        protected override string fireMapAndTakePic(MapForm mf, string paramName, string filePath)
        {
            //要让小区显示在采样点之上
            var nrLayer = mf.GetNRCellLayer();
            mf.MakeSureCustomLayerVisible(nrLayer, true);

            return base.fireMapAndTakePic(mf, paramName, filePath);
        }

        protected NRCellServiceInfo getNRCellServiceInfo(NRCellInfo nrCellInfo, NRStationAcceptCondition condition)
        {
            if (condition.NRServiceType == NRServiceName.NSA)
            {
                return nrCellInfo.NSAInfo;
            }
            else if (condition.NRServiceType == NRServiceName.SA)
            {
                return nrCellInfo.SAInfo;
            }
            return null;
        }
    }
}
