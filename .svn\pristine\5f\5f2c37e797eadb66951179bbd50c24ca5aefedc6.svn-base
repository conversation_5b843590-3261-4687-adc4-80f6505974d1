﻿using MasterCom.RAMS.Func;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NRWeakSinrRoadGridDlg : BaseDialog
    {
        public NRWeakSinrRoadGridDlg()
        {
            InitializeComponent();
        }

        public void SetCondition(NRWeakSinrRoadGridCondtion condition)
        {
            if (condition == null)
            {
                DateTime now = DateTime.Now.Date;
                beginTime2.Value = new DateTime(now.Year, now.Month, 1);
                endTime2.Value = now;

                beginTime1.Value = beginTime2.Value.AddMonths(-1);
                endTime1.Value = beginTime2.Value.AddDays(-1);
                return;
            }
            beginTime1.Value = condition.Period1.BeginTime;
            endTime1.Value = condition.Period1.EndTime.Date;
            beginTime2.Value = condition.Period2.BeginTime;
            endTime2.Value = condition.Period2.EndTime.Date;
            numMaxValue.Value = (decimal)condition.MaxSINR;
            numMinDistance.Value = (decimal)condition.MinCoverRoadDistance;
            numMaxTPDistance.Value = (decimal)condition.Max2TPDistance;
            numGridSpan.Value = (decimal)condition.GridSpanM;
        }

        public NRWeakSinrRoadGridCondtion GetCondition()
        {
            NRWeakSinrRoadGridCondtion condition = new NRWeakSinrRoadGridCondtion();
            condition.Period1 = p1;
            condition.Period2 = p2;
            condition.MaxSINR = (float)numMaxValue.Value;
            condition.MinCoverRoadDistance = (double)numMinDistance.Value;
            condition.Max2TPDistance = (double)numMaxTPDistance.Value;
            condition.GridSpanM = (double)numGridSpan.Value;
            return condition;
        }

        private TimePeriod p1 = null;
        private TimePeriod p2 = null;

        private void btnOK_Click(object sender, EventArgs e)
        {
            p1 = new TimePeriod();
            if (!p1.SetPeriod(beginTime1.Value.Date, endTime1.Value.Date.AddDays(1).AddMilliseconds(-1)))
            {
                MessageBox.Show("时间段1，开始时间不能大于结束时间！请重新设置条件！");
                return;
            }
            p2 = new TimePeriod();
            if (!p2.SetPeriod(beginTime2.Value.Date, endTime2.Value.Date.AddDays(1).AddMilliseconds(-1)))
            {
                MessageBox.Show("时间段2，开始时间不能大于结束时间！请重新设置条件！");
                return;
            }
            bool notIntersect = p1.BeginTime >= p2.EndTime || p1.EndTime <= p2.BeginTime;
            if (!notIntersect)
            {
                MessageBox.Show("时间段2与时间段1有交叉！请重新设置条件！");
                return;
            }
            DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
        }
    }

    public class NRWeakSinrRoadGridCondtion
    {
        public TimePeriod Period1 { get; set; } = new TimePeriod();
        public TimePeriod Period2 { get; set; } = new TimePeriod();

        public double GridSpanDegree
        {
            get
            {
                return GridSpanM * 0.00001;
            }
        }

        public double GridSpanM { get; set; }

        public float MaxSINR { get; set; } = -10;
        public double Max2TPDistance { get; set; } = 20;//最大采样点间隔
        public double MinCoverRoadDistance { get; set; } = 50;//最小持续距离
        public double MinDuration { get; set; } = 10;//最小持续时长

        public bool IsValidate(float? sinr)
        {
            return sinr <= MaxSINR && sinr >= -50;
        }

        public bool MatchMinWeakCoverDistance(double distance)
        {
            return distance >= MinCoverRoadDistance;
        }

        public bool Match2TestpointsMaxDistance(double distance)
        {
            return distance <= Max2TPDistance;
        }
    }
}
