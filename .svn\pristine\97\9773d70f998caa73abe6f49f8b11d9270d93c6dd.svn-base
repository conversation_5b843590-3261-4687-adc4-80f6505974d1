﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ScanOverlapSettingDlg : BaseFormStyle
    {
        public ScanOverlapSettingDlg(float rxlevDiff, double distanceDiff)
        {
            InitializeComponent();
            spinEditRxLevDiff.Value = (decimal)rxlevDiff;
            spinEditDistanceDiff.Value = (decimal)distanceDiff;
        }

        public void GetSettingValue(out float rxLevDiff, out double distanceDiff)
        {
            rxLevDiff = (float)spinEditRxLevDiff.Value;
            distanceDiff = (double)spinEditRxLevDiff.Value;
        }
    }
}
