﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using DevExpress.XtraCharts;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTAddIndexCfgForm : BaseDialog
    {
        public ZTAddIndexCfgForm()
        {
            InitializeComponent();
            cmbtype.Text = "路测指标";
        }

        public AddIndexItem addIndexItem { get; set; }
        private void btnOK_Click(object sender, EventArgs e)
        {
            try
            {
                if (cmbtype.Text == "")
                {
                    lberror.Text = "请选择指标类型！";
                    return;
                }
                if (tbName.Text == "")
                {
                    lberror.Text = "请填写指标名称！";
                    return;
                }
                addIndexItem = new AddIndexItem();
                addIndexItem.indextype = cmbtype.Text.Replace("指标","");
                addIndexItem.name = tbName.Text;
                addIndexItem.des = tbdes.Text;
            }
            catch (Exception)
            {
                lberror.Text = "输入字符格式不正确！";
                return;
            }
            lberror.Text = "";
            this.DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

    }

    public class AddIndexItem
    {
        public string indextype { get; set; } = "";//指标类型
        public string name { get; set; } = "";//指标名称
        public string des { get; set; } = "";//指标描述
    }
}
