﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTTDNBCellFreqCpiResultForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.treeListView = new BrightIdeasSoftware.TreeListView();
            this.olvColumnRegionName = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnArea = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnCount = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnLAC = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnCI = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnFreq = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnCPI = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnNBCellName = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnNBLAC = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnNBCI = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnNBFreq = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnNBCPI = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnCoEfficient = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnDistance = new BrightIdeasSoftware.OLVColumn();
            this.contextMenuStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExpandAll = new System.Windows.Forms.ToolStripMenuItem();
            this.miCollapseAll = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportToExcel = new System.Windows.Forms.ToolStripMenuItem();
            ((System.ComponentModel.ISupportInitialize)(this.treeListView)).BeginInit();
            this.contextMenuStrip.SuspendLayout();
            this.SuspendLayout();
            // 
            // treeListView
            // 
            this.treeListView.AllColumns.Add(this.olvColumnRegionName);
            this.treeListView.AllColumns.Add(this.olvColumnArea);
            this.treeListView.AllColumns.Add(this.olvColumnCount);
            this.treeListView.AllColumns.Add(this.olvColumnLAC);
            this.treeListView.AllColumns.Add(this.olvColumnCI);
            this.treeListView.AllColumns.Add(this.olvColumnFreq);
            this.treeListView.AllColumns.Add(this.olvColumnCPI);
            this.treeListView.AllColumns.Add(this.olvColumnNBCellName);
            this.treeListView.AllColumns.Add(this.olvColumnNBLAC);
            this.treeListView.AllColumns.Add(this.olvColumnNBCI);
            this.treeListView.AllColumns.Add(this.olvColumnNBFreq);
            this.treeListView.AllColumns.Add(this.olvColumnNBCPI);
            this.treeListView.AllColumns.Add(this.olvColumnCoEfficient);
            this.treeListView.AllColumns.Add(this.olvColumnDistance);
            this.treeListView.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnRegionName,
            this.olvColumnArea,
            this.olvColumnCount,
            this.olvColumnLAC,
            this.olvColumnCI,
            this.olvColumnFreq,
            this.olvColumnCPI,
            this.olvColumnNBCellName,
            this.olvColumnNBLAC,
            this.olvColumnNBCI,
            this.olvColumnNBFreq,
            this.olvColumnNBCPI,
            this.olvColumnCoEfficient,
            this.olvColumnDistance});
            this.treeListView.ContextMenuStrip = this.contextMenuStrip;
            this.treeListView.Cursor = System.Windows.Forms.Cursors.Default;
            this.treeListView.Dock = System.Windows.Forms.DockStyle.Fill;
            this.treeListView.FullRowSelect = true;
            this.treeListView.GridLines = true;
            this.treeListView.Location = new System.Drawing.Point(0, 0);
            this.treeListView.MultiSelect = false;
            this.treeListView.Name = "treeListView";
            this.treeListView.OwnerDraw = true;
            this.treeListView.ShowGroups = false;
            this.treeListView.Size = new System.Drawing.Size(1066, 371);
            this.treeListView.TabIndex = 1;
            this.treeListView.UseCompatibleStateImageBehavior = false;
            this.treeListView.View = System.Windows.Forms.View.Details;
            this.treeListView.VirtualMode = true;
            this.treeListView.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.treeListView_MouseDoubleClick);
            // 
            // olvColumnRegionName
            // 
            this.olvColumnRegionName.HeaderFont = null;
            this.olvColumnRegionName.Text = "区域名称";
            this.olvColumnRegionName.Width = 180;
            // 
            // olvColumnArea
            // 
            this.olvColumnArea.HeaderFont = null;
            this.olvColumnArea.Text = "面积(平方公里)";
            this.olvColumnArea.Width = 100;
            // 
            // olvColumnCount
            // 
            this.olvColumnCount.HeaderFont = null;
            this.olvColumnCount.Text = "数量";
            // 
            // olvColumnLAC
            // 
            this.olvColumnLAC.HeaderFont = null;
            this.olvColumnLAC.Text = "LAC";
            // 
            // olvColumnCI
            // 
            this.olvColumnCI.HeaderFont = null;
            this.olvColumnCI.Text = "CI";
            // 
            // olvColumnFreq
            // 
            this.olvColumnFreq.HeaderFont = null;
            this.olvColumnFreq.Text = "频点";
            // 
            // olvColumnCPI
            // 
            this.olvColumnCPI.HeaderFont = null;
            this.olvColumnCPI.Text = "扰码";
            // 
            // olvColumnNBCellName
            // 
            this.olvColumnNBCellName.HeaderFont = null;
            this.olvColumnNBCellName.Text = "邻区名称";
            this.olvColumnNBCellName.Width = 100;
            // 
            // olvColumnNBLAC
            // 
            this.olvColumnNBLAC.HeaderFont = null;
            this.olvColumnNBLAC.Text = "邻区LAC";
            // 
            // olvColumnNBCI
            // 
            this.olvColumnNBCI.HeaderFont = null;
            this.olvColumnNBCI.Text = "邻区CI";
            // 
            // olvColumnNBFreq
            // 
            this.olvColumnNBFreq.HeaderFont = null;
            this.olvColumnNBFreq.Text = "邻区频点";
            // 
            // olvColumnNBCPI
            // 
            this.olvColumnNBCPI.HeaderFont = null;
            this.olvColumnNBCPI.Text = "邻区CPI";
            // 
            // olvColumnCoEfficient
            // 
            this.olvColumnCoEfficient.HeaderFont = null;
            this.olvColumnCoEfficient.Text = "相关系数";
            // 
            // olvColumnDistance
            // 
            this.olvColumnDistance.HeaderFont = null;
            this.olvColumnDistance.Text = "站间距(米)";
            this.olvColumnDistance.Width = 80;
            // 
            // contextMenuStrip
            // 
            this.contextMenuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExpandAll,
            this.miCollapseAll,
            this.miExportToExcel});
            this.contextMenuStrip.Name = "contextMenuStrip";
            this.contextMenuStrip.Size = new System.Drawing.Size(130, 70);
            this.contextMenuStrip.Opening += new System.ComponentModel.CancelEventHandler(this.contextMenuStrip_Opening);
            // 
            // miExpandAll
            // 
            this.miExpandAll.Name = "miExpandAll";
            this.miExpandAll.Size = new System.Drawing.Size(129, 22);
            this.miExpandAll.Text = "全部展开";
            this.miExpandAll.Click += new System.EventHandler(this.miExpandAll_Click);
            // 
            // miCollapseAll
            // 
            this.miCollapseAll.Name = "miCollapseAll";
            this.miCollapseAll.Size = new System.Drawing.Size(129, 22);
            this.miCollapseAll.Text = "全部折叠";
            this.miCollapseAll.Click += new System.EventHandler(this.miCollapseAll_Click);
            // 
            // miExportToExcel
            // 
            this.miExportToExcel.Name = "miExportToExcel";
            this.miExportToExcel.Size = new System.Drawing.Size(129, 22);
            this.miExportToExcel.Text = "导出Excel";
            this.miExportToExcel.Click += new System.EventHandler(this.miExportToExcel_Click);
            // 
            // ZTTDNBCellFreqCpiResultForm
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.Appearance.Options.UseImage = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1066, 371);
            this.Controls.Add(this.treeListView);
            this.Name = "ZTTDNBCellFreqCpiResultForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "TD邻区频点扰码核查";
            ((System.ComponentModel.ISupportInitialize)(this.treeListView)).EndInit();
            this.contextMenuStrip.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private BrightIdeasSoftware.TreeListView treeListView;
        private BrightIdeasSoftware.OLVColumn olvColumnRegionName;
        private BrightIdeasSoftware.OLVColumn olvColumnLAC;
        private BrightIdeasSoftware.OLVColumn olvColumnCI;
        private BrightIdeasSoftware.OLVColumn olvColumnFreq;
        private BrightIdeasSoftware.OLVColumn olvColumnDistance;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip;
        private System.Windows.Forms.ToolStripMenuItem miExportToExcel;
        private System.Windows.Forms.ToolStripMenuItem miExpandAll;
        private System.Windows.Forms.ToolStripMenuItem miCollapseAll;
        private BrightIdeasSoftware.OLVColumn olvColumnCPI;
        private BrightIdeasSoftware.OLVColumn olvColumnCount;
        private BrightIdeasSoftware.OLVColumn olvColumnArea;
        private BrightIdeasSoftware.OLVColumn olvColumnNBCellName;
        private BrightIdeasSoftware.OLVColumn olvColumnNBLAC;
        private BrightIdeasSoftware.OLVColumn olvColumnNBCI;
        private BrightIdeasSoftware.OLVColumn olvColumnNBFreq;
        private BrightIdeasSoftware.OLVColumn olvColumnNBCPI;
        private BrightIdeasSoftware.OLVColumn olvColumnCoEfficient;
    }
}