﻿using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTDIYQueryScanAnalysisForm : MinCloseForm
    {
        MapForm mapForm;
        public ZTDIYQueryScanAnalysisForm(MainModel mainModel)
            : base(mainModel)
        {
            InitializeComponent();
            this.mapForm = mainModel.MainForm.GetMapForm();
            this.mainModel = mainModel;
        }
        MainModel mainModel = null;
        List<ScanAnalysisLTEInfo> eventResultListNew;
        List<ScanAnalysisLTEInfo> cellResultListNew;
        public void FillData(List<ScanAnalysisLTEInfo> eventResultList
            ,List<ScanAnalysisLTEInfo> cellResultList)
        {
            eventResultListNew = new List<ScanAnalysisLTEInfo>();
            eventResultListNew.AddRange(eventResultList);
            evtGridControl.DataSource = eventResultListNew;
            evtGridControl.RefreshDataSource();

            if (eventResultList.Count > 0)
            {
                List<CellSampleInfo> cellInfoList = new List<CellSampleInfo>();
                foreach (LTECell lteCell in eventResultList[0].cellInfoDic.Keys)
                {
                    cellInfoList.Add(eventResultList[0].cellInfoDic[lteCell]);
                }
                gdCellInfo.DataSource = cellInfoList;
                gdCellInfo.RefreshDataSource();
            }

            cellResultListNew = new List<ScanAnalysisLTEInfo>();
            cellResultListNew.AddRange(cellResultList);
            cellGridControl.DataSource = cellResultListNew;
            cellGridControl.RefreshDataSource();
            
            xtraTabControl1.SelectedTabPage = evtTP;
        }

        private void outPutExcelMenu_Click(object sender, EventArgs e)
        {
            outPutDataToExcel();
        }
        private void outPutDataToExcel()
        {
            List<NPOIRow> dataEvent = new List<NPOIRow>();
            List<NPOIRow> dataCell = new List<NPOIRow>();
            NPOIRow nrEvent = new NPOIRow();
            List<object> colsEvt = new List<object>();
            colsEvt.Add("序号");
            colsEvt.Add("事件名称");
            colsEvt.Add("网格类型");
            colsEvt.Add("网格名称");
            colsEvt.Add("道路名称");
            colsEvt.Add("事件LAC");
            colsEvt.Add("事件CI");
            colsEvt.Add("经度");
            colsEvt.Add("维度");
            colsEvt.Add("时间");
            colsEvt.Add("扫频采样点数");
            colsEvt.Add("覆盖率");
            colsEvt.Add("覆盖时长(s)");
            colsEvt.Add("覆盖小区数");
            colsEvt.Add("平均场强(dBm)");
            colsEvt.Add("最大场强(dBm)");
            colsEvt.Add("最小场强(dBm)");

            colsEvt.Add("小区名称");
            colsEvt.Add("小区TAC");
            colsEvt.Add("小区ECI");
            colsEvt.Add("小区采样点数(s)");
            colsEvt.Add("小区覆盖时长(s)");
            colsEvt.Add("小区平均场强(dBm)");
            colsEvt.Add("小区最大场强(dBm)");
            colsEvt.Add("小区最小场强(dBm)");

            nrEvent.cellValues = colsEvt;
            dataEvent.Add(nrEvent);

            NPOIRow nrCell = new NPOIRow();
            List<object> colsCell = new List<object>();
            colsCell.Add("小区名称");
            colsCell.Add("频点");
            colsCell.Add("PCI");
            colsCell.Add("扫频采样点数");
            colsCell.Add("覆盖率");
            colsCell.Add("覆盖时长(s)");
            colsCell.Add("平均场强(dBm)");
            colsCell.Add("最大场强(dBm)");
            colsCell.Add("最小场强(dBm)");

            nrCell.cellValues = colsCell;
            dataCell.Add(nrCell);

            foreach (ScanAnalysisLTEInfo sai in eventResultListNew)
            {
                foreach (LTECell cell in sai.cellInfoDic.Keys)
                { 
                    NPOIRow nr = new NPOIRow();
                    List<object> objs = new List<object>();
                    objs.Add(sai.IndexID.ToString());
                    objs.Add(sai.StrEventName);
                    objs.Add(sai.StrGridType);
                    objs.Add(sai.StrGridName);
                    objs.Add(sai.StrEventRoadName);
                    objs.Add(sai.StrLAC.ToString());
                    objs.Add(sai.StrCI.ToString());
                    objs.Add(sai.DEventLongitude.ToString());
                    objs.Add(sai.DEventLatitude.ToString());
                    objs.Add(sai.StrTime);
                    objs.Add(sai.ItpCount.ToString());
                    objs.Add(sai.StrCoverRate);
                    objs.Add(sai.DCoverTime.ToString("0.00"));
                    objs.Add(sai.ICoverCellCount.ToString());
                    objs.Add(sai.IR0_RPMean.ToString());
                    objs.Add(sai.IRO_RPMax.ToString());
                    objs.Add(sai.IRO_RPMin.ToString());

                    objs.Add(cell.Name.ToString());
                    objs.Add(cell.TAC.ToString());
                    objs.Add(cell.ECI.ToString());
                    objs.Add(sai.cellInfoDic[cell].ICellCountRO_RP.ToString());
                    objs.Add(sai.cellInfoDic[cell].DCellCoverTime.ToString("0.00"));
                    objs.Add(sai.cellInfoDic[cell].ICellMeanRO_RP.ToString());
                    objs.Add(sai.cellInfoDic[cell].ICellMaxRO_RP.ToString());
                    objs.Add(sai.cellInfoDic[cell].ICellMinRO_RP.ToString());

                    nr.cellValues = objs;
                    dataEvent.Add(nr);
                }
            }

            foreach (ScanAnalysisLTEInfo sai in cellResultListNew)
            {
                NPOIRow nr = new NPOIRow();
                List<object> objs = new List<object>();
                objs.Add(sai.StrCellName);
                objs.Add(sai.IEARFCN.ToString());
                objs.Add(sai.IPCI.ToString());
                objs.Add(sai.ItpCount.ToString());
                objs.Add(sai.StrCoverRate);
                objs.Add(sai.DCoverTime.ToString("0.00"));
                objs.Add(sai.IR0_RPMean.ToString());
                objs.Add(sai.IRO_RPMax.ToString());
                objs.Add(sai.IRO_RPMin.ToString());

                nr.cellValues = objs;
                dataCell.Add(nr);
            }

            List<List<NPOIRow>> nrDatasList = new List<List<NPOIRow>>();
            nrDatasList.Add(dataEvent);
            nrDatasList.Add(dataCell);
            List<string> sheetNames = new List<string>();
            sheetNames.Add("事件维度分析结果");
            sheetNames.Add("小区维度分析结果");

            ExcelNPOIManager.ExportToExcel(nrDatasList, sheetNames);
        }

        private void replayScanSample_Click(object sender, EventArgs e)
        {
            int[] row = gridView1.GetSelectedRows();
            if (row.Length == 0)
                return;
            object o = gridView1.GetRow(row[0]);
            ScanAnalysisLTEInfo replayFile = o as ScanAnalysisLTEInfo;
            FileInfo fileInfo = new FileInfo();
            fileInfo.Name = replayFile.ReplayFile.Name;
            fileInfo.ID = replayFile.ReplayFile.ID;
            fileInfo.ProjectID = replayFile.ReplayFile.ProjectID;
            fileInfo.ServiceType = replayFile.ReplayFile.ServiceType;
            fileInfo.SampleTbName = replayFile.ReplayFile.SampleTbName;
            fileInfo.LogTable = replayFile.ReplayFile.LogTable;

            DateTime timeStart = replayFile.Evt.DateTime.AddSeconds(-10);
            DateTime timeEnd = replayFile.Evt.DateTime.AddSeconds(10);
            MasterCom.RAMS.Model.Interface.FileReplayer.Replay(fileInfo, new TimePeriod(timeStart, timeEnd));

        }

        private void xtraTabControl1_SelectedPageChanged(object sender, DevExpress.XtraTab.TabPageChangedEventArgs e)
        {
            if (xtraTabControl1.SelectedTabPage == evtTP)
            {
                replayScanSample.Visible = true;
            }
            else if (xtraTabControl1.SelectedTabPage == cellTP)
            {
                replayScanSample.Visible = false;
            }
        }

        private void evtGridControl_Click(object sender, EventArgs e)
        {
            int[] row = gridView1.GetSelectedRows();
            if (row.Length == 0)
                return;
            object o = gridView1.GetRow(row[0]);
            ScanAnalysisLTEInfo eventCellInfo = o as ScanAnalysisLTEInfo;
            List<CellSampleInfo> cellInfoList = new List<CellSampleInfo>();
            foreach(LTECell lteCell in eventCellInfo.cellInfoDic.Keys)
            {
                cellInfoList.Add(eventCellInfo.cellInfoDic[lteCell]);
            }
            gdCellInfo.DataSource = cellInfoList;
            gdCellInfo.RefreshDataSource();
        }

        private void sampleMenu_Click(object sender, EventArgs e)
        {
            if (MapForm.specialSampleOplayer.IsVisible)
            {
                MapForm.specialSampleOplayer.IsVisible = false;
            }
            else
            {
                MapForm.specialSampleOplayer.IsVisible = true;
            }
            MapForm mf = new MapForm();
            mf.updateMap();
        }

        private void replayEvent_Click(object sender, EventArgs e)
        {
            int[] row = gridView1.GetSelectedRows();
            if (row.Length == 0)
                return;
            object o = gridView1.GetRow(row[0]);
            ScanAnalysisLTEInfo evt = o as ScanAnalysisLTEInfo;

            MasterCom.RAMS.Model.Interface.FileReplayer.Replay(evt.Evt, true);
        }

        private void outShapFile_Click(object sender, EventArgs e)
        {
            SaveFileDialog saveFileDlg = new SaveFileDialog();
            saveFileDlg.Filter = FilterHelper.Shp;
            saveFileDlg.FilterIndex = 1;
            saveFileDlg.RestoreDirectory = true;
            saveFileDlg.Title = "另存为";
            if (saveFileDlg.ShowDialog() == DialogResult.OK)
            {
                string fileName = saveFileDlg.FileName;
                int expRet = ZTDIYAnalysisSpecialSampleOpLayer.OutputShapeFile(fileName);
                if (expRet == 0)
                {
                    MessageBox.Show(this, "所选导出图层没有需要导出的数据！");
                }
                else if (expRet == 1)
                {
                    MessageBox.Show(this, "导出成功！");
                    this.DialogResult = DialogResult.OK;
                }
                else if (expRet == 2)
                {
                    //取消导出
                }
                else
                {
                    MessageBox.Show(this, "导出图层发生错误！");
                }
            }
        }

        private void outPutEventShpFile_Click(object sender, EventArgs e)
        {
            SaveFileDialog saveFileDlg = new SaveFileDialog();
            saveFileDlg.Filter = FilterHelper.Shp;
            saveFileDlg.FilterIndex = 1;
            saveFileDlg.RestoreDirectory = true;
            saveFileDlg.Title = "另存为";
            if (saveFileDlg.ShowDialog() == DialogResult.OK)
            {
                string fileName = saveFileDlg.FileName;
                int expRet = ZTDIYAnalysisSpecialSampleOpLayer.OutputEventShapeFile(fileName, eventResultListNew);
                if (expRet == 0)
                {
                    MessageBox.Show(this, "所选导出图层没有需要导出的数据！");
                }
                else if (expRet == 1)
                {
                    MessageBox.Show(this, "导出成功！");
                    this.DialogResult = DialogResult.OK;
                }
                else if (expRet == 2)
                {
                    //取消导出
                }
                else
                {
                    MessageBox.Show(this, "导出图层发生错误！");
                }
            }
        }

        private void miCustomFly_Click(object sender, EventArgs e)
        {
            if (miCustomFly.Text == "隐藏常用飞线")
            {
                ZTDIYAnalysisSpecialSampleOpLayer.isCustomFly = false;
                MainModel.DTDataManager.Clear();
                MainModel.FireDTDataChanged(this);
                MainModel.DrawFlyLines = false;
                miCustomFly.Text = "显示常用飞线";
            }
            else
            {
                MainModel.DTDataManager.Clear();
                foreach (TestPoint tpp in ZTDIYQueryScanAnalysis_LTE.ScanTestPointList)
                {
                    MainModel.DTDataManager.Add(tpp);
                }
                MainModel.FireDTDataChanged(this);
                foreach (MasterCom.RAMS.Func.MapSerialInfo serial in MainModel.MainForm.GetMapForm().GetDTLayer().SerialInfos)
                {
                    //if (serial.Name.Contains("TopN_PSS_RSSI") || serial.Name.Contains("RSRP")
                    //    || serial.Name.Contains("RSCP") || serial.Name.Contains("RxLev"))
                    {
                        serial.Visible = true;
                    }
                }
                MainModel.DrawFlyLines = true;
                MainModel.MainForm.GetMapForm().GetDTLayer().Invalidate();
                miCustomFly.Text = "隐藏常用飞线";
            }
            MainModel.MainForm.GetMapForm().updateMap();
        }

        private void miSpFly_Click(object sender, EventArgs e)
        {
            if (miSpFly.Text == "隐藏飞线(含邻区)")
            {
                ZTDIYAnalysisSpecialSampleOpLayer.isSpFfly = false;
                miSpFly.Text = "显示飞线(含邻区)";
            }
            else
            {
                ZTDIYAnalysisSpecialSampleOpLayer.isSpFfly = true;
                miSpFly.Text = "隐藏飞线(含邻区)";
            }
            MapForm.specialSampleOplayer.IsVisible = true;
            MainModel.MainForm.GetMapForm().updateMap();
        }
    }
}
