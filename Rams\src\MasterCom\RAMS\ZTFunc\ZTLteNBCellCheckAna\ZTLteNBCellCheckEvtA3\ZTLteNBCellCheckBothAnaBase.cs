﻿using System;
using System.Collections.Generic;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using System.Text;
using MasterCom.RAMS.Func.SystemSetting;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTLteNBCellCheckBothAnaBase : DIYAnalyseByCellBackgroundBaseByFile
    {
        public ZTLteNBCellCheckCondition nbCellCheckCondition { get; set; } = new ZTLteNBCellCheckCondition();   //查询条件
        //针对采样点
        public Dictionary<string, ZTLteNBCellCheckBothCellItem> cellSampleDic { get; set; } = new Dictionary<string, ZTLteNBCellCheckBothCellItem>();
        //针对信令
        public Dictionary<string, ZTLteNBCellCheckBothCellItem> cellMsgDic { get; set; } = new Dictionary<string, ZTLteNBCellCheckBothCellItem>();

        //记录下发的邻区配置（通过信令获取）
        public Dictionary<LTECell, Dictionary<LTECell, int>> nbCfgDic { get; set; } = new Dictionary<LTECell, Dictionary<LTECell, int>>();    //<主服, <邻区,COUNT>>

        public ZTLteNBCellCheckBothAnaBase(MainModel mainModel)
            : base(mainModel)
        {
            funcVersion = 1.1;
            this.IncludeEvent = false;
            this.IncludeMessage = true;

            ServiceTypes.Clear();
            ServiceTypes.AddRange(ServiceTypeManager.GetLteAllServiceTypes());
        }

        protected override void clearDataBeforeAnalyseFiles()
        {
            cellSampleDic = new Dictionary<string, ZTLteNBCellCheckBothCellItem>();
            cellMsgDic = new Dictionary<string, ZTLteNBCellCheckBothCellItem>();
            nbCfgDic = new Dictionary<LTECell, Dictionary<LTECell, int>>();
        }

        ZTLteNBCellCheckBothAnaSetForm setForm = null;
        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }
            if (setForm == null)
            {
                setForm = new ZTLteNBCellCheckBothAnaSetForm();
            }
            if (setForm.ShowDialog() == DialogResult.OK)
            {
                nbCellCheckCondition = setForm.GetCondition();
                return true;
            }
            return false;
        }

        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager fileMng in MainModel.DTDataManager.FileDataManagers)
            {
                List<DTData> dtDataList = new List<DTData>();

                foreach (TestPoint tp in fileMng.TestPoints)
                {
                    dtDataList.Add(tp);
                }

                foreach (MasterCom.RAMS.Model.Message msg in fileMng.Messages)
                {
                    if (msg.ID == (int)EnumLteNBCheckMsg.RRCConnectionReconfiguration ||
                         msg.ID == (int)EnumLteNBCheckMsg.SystemInformationBlockType1 ||
                         msg.ID == (int)EnumLteNBCheckMsg.MeasurementReport)
                    {
                        dtDataList.Add((DTData)msg);
                    }
                }
                dtDataList.Sort(comparer);

                dealWithDTData(dtDataList);
            }
        }

        private void dealWithDTData(List<DTData> dtDataList)
        {
            ZTLteNBCellCheckMsgItem curMsgItem = new ZTLteNBCellCheckMsgItem();
            for (int i = 0; i < dtDataList.Count; i++)
            {
                if (dtDataList[i] is TestPoint)
                {
                    doStatWithTestPoint(dtDataList[i] as TestPoint);

                    if (curMsgItem.IsGotHOMsg)
                    {
                        curMsgItem.Tp = dtDataList[i] as TestPoint; //用最近的采样点经纬度来填充
                    }
                }
                else if (dtDataList[i] is MasterCom.RAMS.Model.Message)
                {
                    MessageWithSource msg = dtDataList[i] as MessageWithSource;
                    if (msg.ID == (int)EnumLteNBCheckMsg.RRCConnectionReconfiguration)
                    {
                        processRRCConnReconfigMsg(msg, ref curMsgItem);
                    }
                    else if (msg.ID == (int)EnumLteNBCheckMsg.SystemInformationBlockType1)
                    {
                        processSIB1Msg(msg, ref curMsgItem);
                    }
                    else if (msg.ID == (int)EnumLteNBCheckMsg.MeasurementReport)
                    {
                        processMRMsg(msg, ref curMsgItem);
                    }
                }
            }
        }

        /// <summary>
        /// 对采样点进行处理，无法匹配到工参的，不进行处理
        /// </summary>
        private void doStatWithTestPoint(TestPoint tp)
        {
            LTECell servCell = tp.GetMainLTECell_TdOrFdd();

            if (servCell == null)    //无法匹配到工参，则无经纬度，剔除
            {
                return;
            }

            float? rsrp = tp.GetRxlev();
            if (rsrp == null || rsrp < -141)//主服不符合门限，剔除
            {
                return;
            }

            ZTLteNBCellCheckBothCellItem cellItem;
            if (cellSampleDic.ContainsKey(servCell.Name))
            {
                cellItem = cellSampleDic[servCell.Name];
                if (cellItem.ServCell.ValidPeriod.IEndTime < servCell.ValidPeriod.IEndTime)
                {//多份小区快照时，取最新快照小区作为漏配判断依据
                    cellItem.ServCell = servCell;
                }
                cellItem.MergeData((float)rsrp);
            }
            else
            {
                cellItem = new ZTLteNBCellCheckBothCellItem(servCell, (float)rsrp);
                cellSampleDic.Add(servCell.Name, cellItem);
            }

            for (int i = 0; i < 6; i++)
            {
                setNBDic(tp, cellItem, i);
            }
        }

        private void setNBDic(TestPoint tp, ZTLteNBCellCheckBothCellItem cellItem, int i)
        {
            float? nRsrp = tp.GetNbRxlev(i);
            if (nRsrp != null && nRsrp >= nbCellCheckCondition.RSRP)
            {
                LTECell nbCell = tp.GetNBLTECell_TdOrFdd(i);
                if (nbCell != null)
                {
                    if (cellItem.NBDic.ContainsKey(nbCell.Name))
                    {
                        cellItem.NBDic[nbCell.Name].MergeData((float)nRsrp, i);
                    }
                    else
                    {
                        ZTLteNBCellCheckBothNBItem nbItem = new ZTLteNBCellCheckBothNBItem(nbCell, (float)nRsrp, i);
                        cellItem.NBDic.Add(nbCell.Name, nbItem);
                    }
                }
            }
        }

        /// <summary>
        /// 将一个完整信令周期中的主服和对应的邻区配置记录下来
        /// </summary>
        /// <param name="curMsgItem"></param>
        private void addToNBCfgDic(ref ZTLteNBCellCheckMsgItem curMsgItem)
        {
            if (curMsgItem.NBCfgDic.Count == 0)    //没有邻区，不需要处理
            {
                return;
            }

            if (curMsgItem.Tp == null)    //没有采样点信息，无法获取经纬度，不需要处理
            {
                return;
            }

            LTECell servCell = null;
            servCell = CellManager.GetInstance().GetNearestLTECell(curMsgItem.Tp.DateTime, curMsgItem.TAC, curMsgItem.ECI, curMsgItem.EARFCN, curMsgItem.PCI,
                                                                  curMsgItem.Tp.Longitude, curMsgItem.Tp.Latitude);

            if (servCell == null)    //没有匹配到主服工参，不处理
            {
                return;
            }

            Dictionary<LTECell, int> nbDic = new Dictionary<LTECell, int>();
            if (nbCfgDic.ContainsKey(servCell))
            {
                nbDic = nbCfgDic[servCell];
            }
            else
            {
                nbCfgDic.Add(servCell, nbDic);
            }

            foreach (int earfcn in curMsgItem.NBCfgDic.Keys)
            {
                foreach (int pci in curMsgItem.NBCfgDic[earfcn].Keys)
                {
                    addNBCell(curMsgItem, nbDic, earfcn, pci);
                }
            }
        }

        private void addNBCell(ZTLteNBCellCheckMsgItem curMsgItem, Dictionary<LTECell, int> nbDic, int earfcn, int pci)
        {
            LTECell nbCell = CellManager.GetInstance().GetNearestLTECellByEARFCNPCI(curMsgItem.Tp.DateTime, earfcn, pci, curMsgItem.Tp.Longitude, curMsgItem.Tp.Latitude);
            if (nbCell != null)
            {
                if (nbDic.ContainsKey(nbCell))
                {
                    nbDic[nbCell]++;
                }
                else
                {
                    nbDic.Add(nbCell, 1);
                }
            }
        }

        /// <summary>
        /// 记录通过信令获取到的主服和邻区上报信息
        /// </summary>
        /// <param name="curMsgItem"></param>
        private void addToCellMsgDic(ref ZTLteNBCellCheckMsgItem curMsgItem)
        {
            if (curMsgItem.NBRptDic.Count == 0)    //没有收到上报的MR数据，不需要处理
            {
                return;
            }

            if (curMsgItem.Tp == null)    //没有采样点信息，无法获取经纬度，不需要处理
            {
                return;
            }

            LTECell servCell = null;
            servCell = CellManager.GetInstance().GetNearestLTECell(curMsgItem.Tp.DateTime, curMsgItem.TAC, curMsgItem.ECI, curMsgItem.EARFCN, curMsgItem.PCI,
                                                                  curMsgItem.Tp.Longitude, curMsgItem.Tp.Latitude);

            if (servCell == null)    //没有匹配到主服工参，不处理
            {
                return;
            }


            ZTLteNBCellCheckBothCellItem cellItem;
            if (cellMsgDic.ContainsKey(servCell.Name))
            {
                cellItem = cellMsgDic[servCell.Name];
                cellItem.MergeData(curMsgItem.RSRPTotal, curMsgItem.MRCount);
            }
            else
            {
                cellItem = new ZTLteNBCellCheckBothCellItem(servCell, curMsgItem.RSRPTotal, curMsgItem.MRCount);
                cellMsgDic.Add(servCell.Name, cellItem);
            }

            foreach (int earfcn in curMsgItem.NBRptDic.Keys)
            {
                foreach (int pci in curMsgItem.NBRptDic[earfcn].Keys)
                {
                    LTECell nbCell = CellManager.GetInstance().GetNearestLTECellByEARFCNPCI(curMsgItem.Tp.DateTime, earfcn, pci, curMsgItem.Tp.Longitude, curMsgItem.Tp.Latitude);
                    if (nbCell == null)
                    {
                        continue;
                    }

                    ZTLteNBCellCheckBothNBItem nbItem = curMsgItem.NBRptDic[earfcn][pci];
                    nbItem.NBCell = nbCell;
                    if (cellItem.NBDic.ContainsKey(nbCell.Name))
                    {
                        cellItem.NBDic[nbCell.Name].MergeData(nbItem);
                    }
                    else
                    {
                        cellItem.NBDic.Add(nbCell.Name, nbItem);
                    }
                }
            }
        }


        /// <summary>
        /// 分析 RRCConnectionReconfig层三信令，如果包含目标EARFCN和PCI，就认为是切换信令
        /// 如果不是，则需进一步判断是否是下发邻区的信令
        /// </summary>
        /// <param name="cellItem"></param>
        /// <param name="dtData"></param>
        /// <param name="fileItem"></param>
        private void processRRCConnReconfigMsg(MessageWithSource msg, ref ZTLteNBCellCheckMsgItem curMsgItem)
        {
            MessageDecodeHelper.StartDissect(msg.Direction, msg.Source, msg.Source.Length, msg.ID);

            // 如果RRC Connection Reconfiguration中包含mobilityControlInfo，那主要作用就是eNodeB发切换命令给UE执行切换
            // 如果RRC Connection Reconfiguration中包含measConfig，那其主要作用就是进行测量配置

            int earfcn = 0;
            int pci = 0;
            if (MessageDecodeHelper.GetSingleSInt("lte-rrc.targetPhysCellId", ref pci))
            {
                if (!MessageDecodeHelper.GetSingleSInt("lte-rrc.dl_CarrierFreq", ref earfcn))
                {//如果获取不到earfcn，沿用之前的
                    earfcn = curMsgItem.EARFCN;
                }

                if (curMsgItem.IsGotHOMsg 
                    && curMsgItem.IsGotNBConfigMsg
                    && curMsgItem.IsGotSIB1Msg)
                {//如果一次信息已经收集完整，则添加到邻区配置列表中
                    addToNBCfgDic(ref curMsgItem);

                    if (curMsgItem.IsGotMRMsg)
                    {//如果在一次信令过程中收到A3事件的测量报告，则加入到信令获取的测量结果中
                        addToCellMsgDic(ref curMsgItem);
                    }
                }

                curMsgItem = new ZTLteNBCellCheckMsgItem();

                curMsgItem.EARFCN = earfcn;
                curMsgItem.PCI = pci;
                curMsgItem.IsGotHOMsg = true;
            }
            else
            {
                processRRCConnReconfigMsg_CfgNBList(ref curMsgItem);
            }
        }

        /// <summary>
        /// 找出含有A3、A4、A5事件的RptObjectID
        /// </summary>
        /// <param name="rptObjectID_A3"></param>
        private void setRptObjectID_AxEvent(ref int rptObjectID_Ax)
        {
            int rptObjectCount = 0;
            if (MessageDecodeHelper.GetSingleSInt("lte-rrc.reportConfigToAddModList", ref rptObjectCount))  //包含报告对象
            {
                int[] arrEventIDs = new int[rptObjectCount];
                if (MessageDecodeHelper.GetMultiSInt("lte-rrc.eventId", ref arrEventIDs, rptObjectCount))   //是否有事件ID，如果没有，肯定没有A3事件，则终止
                {
                    int[] arrRptObjectIDs = new int[rptObjectCount];
                    int[] arrTrigTypes = new int[rptObjectCount];
                    int periodCount = 0;                        //记录周期性上报rpt次数，

                    if (MessageDecodeHelper.GetMultiSInt("lte-rrc.reportConfigId", ref arrRptObjectIDs, rptObjectCount)
                        && (MessageDecodeHelper.GetMultiSInt("lte-rrc.triggerType", ref arrTrigTypes, rptObjectCount)))
                    {
                        getObjectID(ref rptObjectID_Ax, rptObjectCount, arrEventIDs, arrRptObjectIDs, arrTrigTypes, ref periodCount);
                    }
                }
            }
        }

        private void getObjectID(ref int rptObjectID_Ax, int rptObjectCount, int[] arrEventIDs, int[] arrRptObjectIDs, 
            int[] arrTrigTypes, ref int periodCount)
        {
            for (int i = 0; i < rptObjectCount; i++)
            {
                if (arrTrigTypes[i] == 0)   //event
                {
                    int eventid = arrEventIDs[i - periodCount];

                    if (eventid == (int)EnumMRTrigEvent.Event_A3
                        || eventid == (int)EnumMRTrigEvent.Event_A4
                        || eventid == (int)EnumMRTrigEvent.Event_A5)
                    //A3、A4、A5事件（现在网络中有多层网，很多省份切换都开通了A4，A5事件，所以计算该邻区漏配时应该考虑A4，A5事件进去更为合理）
                    {
                        rptObjectID_Ax = arrRptObjectIDs[i];    //记录A3、A4、A5事件的rpt的ID号
                        return;
                    }
                }
                else
                {
                    periodCount++;
                }
            }
        }

        /// <summary>
        /// 通过RptObjectID来反查measID和measObjectID
        /// </summary>
        /// <param name="rptObjectID_A3"></param>
        /// <param name="measID"></param>
        /// <param name="measObjectID"></param>
        private void setMeasIDAndMeasObjectIDByRptObjectID(int rptObjectID_A3, ref int measID, ref int measObjectID)
        {
            int measIDCount = 0;
            if (MessageDecodeHelper.GetSingleSInt("lte-rrc.measIdToAddModList", ref measIDCount))
            {
                int[] arrMeasObjectIDs = new int[measIDCount];
                int[] arrMeasIDs = new int[measIDCount];
                int[] arrRptObjectIDs = new int[measIDCount];

                if (MessageDecodeHelper.GetMultiSInt("lte-rrc.measId", ref arrMeasIDs, measIDCount)
                    && MessageDecodeHelper.GetMultiSInt("lte-rrc.measObjectId", ref arrMeasObjectIDs, measIDCount)
                    && MessageDecodeHelper.GetMultiSInt("lte-rrc.reportConfigId", ref arrRptObjectIDs, measIDCount))
                {
                    for (int i = 0; i < measIDCount; i++)
                    {
                        if (arrRptObjectIDs[i] == rptObjectID_A3)
                        {
                            measObjectID = arrMeasObjectIDs[i];
                            measID = arrMeasIDs[i];
                            return;
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 通过MeasObjectID来找到需要测量的邻区列表，同时建立MeasID和频点的对应关系（由于MR报告中没有频点，需要用此关系找出频点）
        /// </summary>
        /// <param name="cellItem"></param>
        /// <param name="measID"></param>
        /// <param name="measObjectID"></param>
        private void setNBListCfgByMeasObjectID(ref ZTLteNBCellCheckMsgItem curMsgItem, int measObjectID)
        {
            int measObjectCount = 0;
            if (MessageDecodeHelper.GetSingleSInt("lte-rrc.measObjectToAddModList", ref measObjectCount))  //包含测量对象
            {
                int[] arrMeasObjectIDs = new int[measObjectCount];
                int[] arrCarrierFreqs = new int[measObjectCount];
                int[] arrPCICounts = new int[measObjectCount];
                int[] arrPCIHeadIndexs = new int[measObjectCount];

                int index = 0;  //需要获取的测量数据在数组中的位置
                int accuPCIIndex = 0;       //PCI累积的数量,用于确定数组大小，取回所有的PCI

                addNBMeasEarfcn(curMsgItem, measObjectCount, ref arrMeasObjectIDs, ref arrCarrierFreqs);

                //获取每个MeasID下发的PCI数量
                if (MessageDecodeHelper.GetMultiSInt("lte-rrc.cellsToAddModList", ref arrPCICounts, measObjectCount))
                {
                    dealMeasObject(measObjectID, measObjectCount, arrMeasObjectIDs, arrPCICounts, arrPCIHeadIndexs, ref index, ref accuPCIIndex);

                    if (arrPCICounts.Length == 0)  //没有获取到，return
                    {
                        return;
                    }

                }
                else //没有下发PCI，直接return
                {
                    return;
                }

                addNBCfg(curMsgItem, arrCarrierFreqs, arrPCICounts, arrPCIHeadIndexs, index, accuPCIIndex);
            }
        }

        private void dealMeasObject(int measObjectID, int measObjectCount, int[] arrMeasObjectIDs, int[] arrPCICounts, int[] arrPCIHeadIndexs, ref int index, ref int accuPCIIndex)
        {
            for (int i = 0; i < measObjectCount; i++)
            {
                if (i >= arrPCICounts.Length)   //存在下发X个measObjectId，但在某些measObjec的Item中，不下发PCI的情况，因此要判断数组的大小，常见于异频的情况，只下发测量的mbw，不下发具体的PCI
                {
                    break;
                }
                arrPCIHeadIndexs[i] = accuPCIIndex;
                accuPCIIndex += arrPCICounts[i];

                if (arrMeasObjectIDs[i] == measObjectID)
                {
                    index = i;
                }
            }
        }

        private void addNBMeasEarfcn(ZTLteNBCellCheckMsgItem curMsgItem, int measObjectCount, ref int[] arrMeasObjectIDs, ref int[] arrCarrierFreqs)
        {
            //建立MeasID和CarrierFreq的对应关系
            if (MessageDecodeHelper.GetMultiSInt("lte-rrc.measObjectId", ref arrMeasObjectIDs, measObjectCount)
                && MessageDecodeHelper.GetMultiSInt("lte-rrc.carrierFreq", ref arrCarrierFreqs, measObjectCount))
            {
                for (int i = 0; i < Math.Min(arrMeasObjectIDs.Length, arrCarrierFreqs.Length); i++)  //！！对比arrMeasObjectIDs和arrCarrierFreqs数组长度，取短的，原因是有些测量没有频点信息
                {
                    if (!curMsgItem.NBMeasEarfcnDic.ContainsKey(arrMeasObjectIDs[i]))
                    {
                        curMsgItem.NBMeasEarfcnDic.Add(arrMeasObjectIDs[i], arrCarrierFreqs[i]);
                    }
                    else
                    {
                        curMsgItem.NBMeasEarfcnDic[arrMeasObjectIDs[i]] = arrCarrierFreqs[i];
                    }
                }
            }
        }

        private void addNBCfg(ZTLteNBCellCheckMsgItem curMsgItem, int[] arrCarrierFreqs, int[] arrPCICounts, int[] arrPCIHeadIndexs, int index, int accuPCIIndex)
        {
            //记录需要测量的频点和PCI
            int[] arrPCIs = new int[accuPCIIndex];
            if (MessageDecodeHelper.GetMultiSInt("lte-rrc.physCellId", ref arrPCIs, accuPCIIndex))
            {
                int mrEarfcn = arrCarrierFreqs[index];
                Dictionary<int, int> pciDic = new Dictionary<int, int>();

                if (curMsgItem.NBCfgDic.ContainsKey(mrEarfcn))
                {
                    pciDic = curMsgItem.NBCfgDic[mrEarfcn];
                }
                else
                {
                    curMsgItem.NBCfgDic.Add(mrEarfcn, pciDic);
                }

                int startIndex = arrPCIHeadIndexs[index];
                for (int j = 0; j < arrPCICounts[index]; j++)
                {
                    if ((mrEarfcn == curMsgItem.EARFCN) && (arrPCIs[startIndex + j] == curMsgItem.PCI)) //列表中可能出现主服的EARFCN和PCI，剔除
                    {
                        continue;
                    }

                    if (pciDic.ContainsKey(arrPCIs[startIndex + j]))
                    {
                        pciDic[arrPCIs[startIndex + j]]++;
                    }
                    else
                    {
                        pciDic.Add(arrPCIs[startIndex + j], 1);
                    }
                }
            }
        }

        /// <summary>
        /// 解析出需要测量的邻区列表
        /// </summary>
        /// <param name="cellItem"></param>
        private void processRRCConnReconfigMsg_CfgNBList(ref ZTLteNBCellCheckMsgItem curMsgItem)
        {
            if (!curMsgItem.IsGotHOMsg)  //对于其它信令，如果还没有头信令，不处理
            {
                return;
            }

            //找出A3、A4、A5事件所在的RptObjectID
            int rptObjectID_Ax = 0;
            setRptObjectID_AxEvent(ref rptObjectID_Ax);
            if (rptObjectID_Ax == 0)  //没有A3、A4、A5事件
            {
                return;
            }

            //根据Ax事件的RptObjectID，找出对应的measObjectID和measID
            int measID = 0;
            int measObjectID = 0;
            setMeasIDAndMeasObjectIDByRptObjectID(rptObjectID_Ax, ref measID, ref measObjectID);
            if (measID == 0 || measObjectID == 0)   //没有找到或出错
            {
                return;
            }

            setNBListCfgByMeasObjectID(ref curMsgItem, measObjectID);
            curMsgItem.IsGotNBConfigMsg = true;
        }

        /// <summary>
        /// 解析SIB1,用户获取当前小区的TAC和ECI
        /// </summary>
        /// <param name="cellItem"></param>
        /// <param name="dtData"></param>
        private void processSIB1Msg(MessageWithSource msg, ref ZTLteNBCellCheckMsgItem curMsgItem)
        {
            if (!curMsgItem.IsGotHOMsg)  //对于其它信令，如果还没有头信令，不处理
            {
                return;
            }

            MessageDecodeHelper.StartDissect(msg.Direction, msg.Source, msg.Source.Length, msg.ID);

            uint uTac = 0;
            uint uEci = 0;
            if (MessageDecodeHelper.GetSingleUInt("lte-rrc.trackingAreaCode_TAC", ref uTac) 
                && MessageDecodeHelper.GetSingleUInt("lte-rrc.cellIdentity_ECI", ref uEci))
            {
                curMsgItem.TAC = (int)uTac;
                curMsgItem.ECI = (int)uEci;
                curMsgItem.IsGotSIB1Msg = true;
            }
            curMsgItem.IsGotSIB1Msg = true;
        }

        /// <summary>
        /// 解析MR测量报告，解析时需要判断MeasID是否是之前A3事件设置的ID
        /// </summary>
        /// <param name="cellItem"></param>
        /// <param name="dtData"></param>
        private void processMRMsg(MessageWithSource msg, ref ZTLteNBCellCheckMsgItem curMsgItem)
        {
            if (!curMsgItem.IsGotHOMsg)  //对于其它信令，如果还没有头信令，不处理
            {
                return;
            }

            MessageDecodeHelper.StartDissect(msg.Direction, msg.Source, msg.Source.Length, msg.ID);

            int measID = 0;
            bool isFound = false;
            int earfcn = 0;
            if (MessageDecodeHelper.GetSingleSInt("lte-rrc.measId", ref measID))
            {
                foreach (int nbMeasID in curMsgItem.NBMeasEarfcnDic.Keys)
                {
                    if (measID == nbMeasID)
                    {
                        isFound = true;
                        earfcn = curMsgItem.NBMeasEarfcnDic[nbMeasID];
                        break;
                    }
                }
                if (!isFound)  //不是需要的报告
                {
                    return;
                }
            }

            int nbCellsCount = 0;
            if (MessageDecodeHelper.GetSingleSInt("lte-rrc.measResultListEUTRA", ref nbCellsCount))
            {
                int[] arrRSRPs = new int[nbCellsCount + 1]; //包括主服的，所以+1
                int[] arrPCIs = new int[nbCellsCount];
                if (MessageDecodeHelper.GetMultiSInt(
                    "lte-rrc.rsrpResult", ref arrRSRPs
                    , nbCellsCount + 1) && MessageDecodeHelper.GetMultiSInt("lte-rrc.physCellId"
                    , ref arrPCIs, nbCellsCount))
                {
                    curMsgItem.MRCount++;
                    curMsgItem.RSRPTotal += arrRSRPs[0] - 141;

                    dealNBRptPci(curMsgItem, earfcn, nbCellsCount, arrRSRPs, arrPCIs);

                    curMsgItem.IsGotMRMsg = true; //信息收集完毕
                }
            }
        }

        private void dealNBRptPci(ZTLteNBCellCheckMsgItem curMsgItem, int earfcn, int nbCellsCount, int[] arrRSRPs, int[] arrPCIs)
        {
            Dictionary<int, ZTLteNBCellCheckBothNBItem> pciDic;
            if (curMsgItem.NBRptDic.ContainsKey(earfcn))
            {
                pciDic = curMsgItem.NBRptDic[earfcn];
            }
            else
            {
                pciDic = new Dictionary<int, ZTLteNBCellCheckBothNBItem>();
                curMsgItem.NBRptDic.Add(earfcn, pciDic);
            }

            for (int i = 0; i < nbCellsCount; i++)
            {
                if ((i + 1) >= arrRSRPs.Length)     //存在邻区不上报RSRP的情况，导致RSRP数组比邻区数量少
                {
                    break;
                }

                if (((float)arrRSRPs[i + 1] - 141) < -141)  //如果邻区的RSRP低于有效门限，则跳过
                {
                    continue;
                }

                if (pciDic.ContainsKey(arrPCIs[i]))
                {
                    pciDic[arrPCIs[i]].SampleCount++;
                    pciDic[arrPCIs[i]].AccuScore(i);
                    pciDic[arrPCIs[i]].RSRPTotal += ((float)arrRSRPs[i + 1] - 141);  //RSRPResult列表中主服在最前面，因此需要跳过主服
                }
                else
                {
                    ZTLteNBCellCheckBothNBItem nbItem = new ZTLteNBCellCheckBothNBItem(earfcn, arrPCIs[i], ((float)arrRSRPs[i + 1] - 141), i);
                    pciDic.Add(arrPCIs[i], nbItem);
                }
            }
        }

        /// <summary>
        /// 对最终结果按照条件进行过滤
        /// </summary>
        /// <returns></returns>
        public List<ZTLteNBCellCheckBothCellItem> filterResultByCondition()
        {
            if (cellSampleDic.Count > 0)
            {
                MainModel.CellManager.GetLTENBCellInfo();
            }

            List<ZTLteNBCellCheckBothCellItem> resultList = new List<ZTLteNBCellCheckBothCellItem>();

            foreach (string cellName in cellSampleDic.Keys)
            {
                ZTLteNBCellCheckBothCellItem cellItem = cellSampleDic[cellName];
                setNBCfg(ref cellItem);

                List<ZTLteNBCellCheckBothNBItem> nbList = new List<ZTLteNBCellCheckBothNBItem>();
                foreach (string nbName in cellItem.NBDic.Keys)
                {
                    ZTLteNBCellCheckBothNBItem nbItem = cellItem.NBDic[nbName];

                    if (nbItem.NBCell.Name == cellName)  //主服与邻区相等，剔除 （部分版本的解码程序存在问题，没有剔除主服，因此需要进行处理）
                    {
                        continue;
                    }

                    if (nbItem.SampleCount < nbCellCheckCondition.SampleCount) //采样点数低于门限，不添加
                    {
                        continue;
                    }

                    setDistance(ref nbItem, cellItem.ServCell);
                    if (nbItem.Distance > nbCellCheckCondition.Distance)    //超过距离
                    {
                        continue;
                    }

                    setStatusByMsg(ref nbItem, cellItem.ServCell);      //检测是否漏配（与信令下发的邻区列表对比）
                    setStatusByParam(ref nbItem, cellItem.ServCell);    //检测是否漏配（与工参的邻区列表对比）

                    setSource(ref nbItem, cellItem.ServCell);           //检测是否在信令中上报

                    nbList.Add(nbItem);
                }

                if (cellMsgDic.ContainsKey(cellName))
                {
                    dealMsg(cellName, cellItem, nbList);
                }

                addResult(resultList, cellItem, nbList);
            }

            return resultList;
        }

        private void dealMsg(string cellName, ZTLteNBCellCheckBothCellItem cellItem, List<ZTLteNBCellCheckBothNBItem> nbList)
        {
            foreach (string nbName in cellMsgDic[cellName].NBDic.Keys)
            {
                if (!cellItem.NBDic.ContainsKey(nbName))    //只在MR中出现，没有在采样点中出现
                {
                    ZTLteNBCellCheckBothNBItem nbItem = cellMsgDic[cellName].NBDic[nbName];
                    setDistance(ref nbItem, cellItem.ServCell);
                    if (nbItem.Distance > nbCellCheckCondition.Distance)    //超过距离
                    {
                        continue;
                    }

                    setStatusByMsg(ref nbItem, cellItem.ServCell);      //检测是否漏配（与信令下发的邻区列表对比）
                    setStatusByParam(ref nbItem, cellItem.ServCell);    //检测是否漏配（与工参的邻区列表对比）

                    setSource(ref nbItem, cellItem.ServCell);           //检测是否在信令中上报
                    nbItem.Source = "只来自MR";

                    nbList.Add(nbItem);
                }
            }
        }

        private static void addResult(List<ZTLteNBCellCheckBothCellItem> resultList, ZTLteNBCellCheckBothCellItem cellItem, List<ZTLteNBCellCheckBothNBItem> nbList)
        {
            if (nbList.Count > 0)
            {
                cellItem.NBDic.Clear();

                nbList.Sort(ZTLteNBCellCheckBothNBItem.GetCompareByScore());

                for (int i = 0; i < nbList.Count; i++)
                {
                    nbList[i].Order = i + 1;
                    cellItem.NBDic.Add(nbList[i].NBCell.Name, nbList[i]);
                }
                cellItem.SN = resultList.Count + 1;
                resultList.Add(cellItem);
            }
        }

        private void setDistance(ref ZTLteNBCellCheckBothNBItem nbItem, LTECell servCell)
        {
            nbItem.Distance = nbItem.NBCell.GetDistance(servCell.Longitude, servCell.Latitude);
        }

        private void setStatusByMsg(ref ZTLteNBCellCheckBothNBItem nbItem, LTECell servCell)
        {
            if (!nbCfgDic.ContainsKey(servCell)) //如果没有收到配置,不更改状态
            {
                return;
            }

            if (nbCfgDic[servCell].Count == 0)  //下了配置，但只下了频点，没有PCI列表，导致无配置的邻区，当做没有下配置
            {
                return;
            }

            if (nbCfgDic[servCell].ContainsKey(nbItem.NBCell))   //配置中存在
            {
                nbItem.StatusByMsg = "在配置中";
            }
            else
            {
                nbItem.StatusByMsg = "漏配";
            }
        }

        private void setStatusByParam(ref ZTLteNBCellCheckBothNBItem nbItem, LTECell servCell)
        {
            nbItem.StatusByParam = "漏配";  //先初始化为漏配

            foreach (LTECell nbCell in servCell.NeighbourCells)
            {
                if (nbCell.Name == nbItem.NBCell.Name)   //名称相同
                {
                    nbItem.StatusByParam = "在配置中";
                    break;
                }
            }
        }

        private void setSource(ref ZTLteNBCellCheckBothNBItem nbItem, LTECell servCell)
        {
            nbItem.Source = "采样点";

            if (cellMsgDic.ContainsKey(servCell.Name))
            {
                bool isContain = cellMsgDic[servCell.Name].NBDic.ContainsKey(nbItem.NBCell.Name);
                if (isContain)   //在message的邻区中出现
                {
                    nbItem.Source += "|MR";
                }
            }
        }

        private void setNBCfg(ref ZTLteNBCellCheckBothCellItem cellItem)
        {
            if (!nbCfgDic.ContainsKey(cellItem.ServCell))    //信令中没有下发配置，不做处理
            {
                return;
            }

            StringBuilder sb = new StringBuilder(cellItem.NBCfg);
            foreach (LTECell nbCell in nbCfgDic[cellItem.ServCell].Keys)
            {
                sb.Append(nbCell.Name + "|");
            }
            cellItem.NBCfg = sb.ToString();
            cellItem.NBCount = nbCfgDic[cellItem.ServCell].Count;
        }

        /// <summary>
        /// 显示结果
        /// </summary>
        protected override void fireShowForm()
        {
            List<ZTLteNBCellCheckBothCellItem> resultList = filterResultByCondition();

            if (resultList.Count == 0)
            {
                MessageBox.Show("没有符合条件的信息！");
                return;
            }

            ZTLteNBCellCheckBothAnaListForm form = mainModel.CreateResultForm(typeof(ZTLteNBCellCheckBothAnaListForm)) as ZTLteNBCellCheckBothAnaListForm;
            form.FillData(resultList);
            form.Visible = true;
            form.BringToFront();
        }

        protected override void releaseSource()
        {
            cellSampleDic = null;
            cellMsgDic = null;
            nbCfgDic = null;
        }

        private readonly Comparer comparer = new Comparer();
        private class Comparer : IComparer<DTData>
        {
            public int Compare(DTData x, DTData y)
            {
                return x.SN - y.SN;
            }
        }

        #region Background
        public override BackgroundFuncType FuncType
        {
            get { return BackgroundFuncType.LTE业务专题; }
        }

        public override BackgroundSubFuncType SubFuncType
        {
            get { return BackgroundSubFuncType.其他; }
        }

        public override Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["BackgroundStat"] = BackgroundStat;
                param["SampleCount"] = this.nbCellCheckCondition.SampleCount;
                param["RSRP"] = this.nbCellCheckCondition.RSRP;
                param["Distance"] = this.nbCellCheckCondition.Distance;
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                if (param.ContainsKey("BackgroundStat"))
                {
                    BackgroundStat = (bool)param["BackgroundStat"];
                }
                if (param.ContainsKey("SampleCount"))
                {
                    this.nbCellCheckCondition.SampleCount = (int)param["SampleCount"];
                }
                if (param.ContainsKey("RSRP"))
                {
                    this.nbCellCheckCondition.RSRP = (int)param["RSRP"];
                }
                if (param.ContainsKey("Distance"))
                {
                    this.nbCellCheckCondition.Distance = (int)param["Distance"];
                }
            }
        }

        public override PropertiesControl Properties
        {
            get
            {
                return new NBCellCheckMsgProperties_LTE(this);
            }
        }
        protected override void saveBackgroundData()
        {
            int subFuncId = this.GetSubFuncID();
            List<BackgroundResult> bgResultList = new List<BackgroundResult>();
            foreach (ZTLteNBCellCheckBothCellItem nBCellItem in cellSampleDic.Values)
            {
                bgResultList.Add(nBCellItem.ConverToBackgroundResult(curAnaFileInfo, subFuncId, "采样点"));
            }
            foreach (ZTLteNBCellCheckBothCellItem nBCellItem in cellMsgDic.Values)
            {
                bgResultList.Add(nBCellItem.ConverToBackgroundResult(curAnaFileInfo, subFuncId, "信令"));
            }
            foreach (var varLteCell in nbCfgDic)
            {
                LTECell lteCell = varLteCell.Key;
                Dictionary<LTECell, int> nbLteCellDic = varLteCell.Value;

                bgResultList.Add(lteCellInfoToBgResult(lteCell, nbLteCellDic, curAnaFileInfo, subFuncId));
            }

            BackgroundFuncQueryManager.GetInstance().SaveResult_Cell(GetSubFuncID(), curAnaFileInfo, bgResultList);

            cellSampleDic.Clear();
            cellMsgDic.Clear();
            nbCfgDic.Clear();
        }

        private BackgroundResult lteCellInfoToBgResult(LTECell servCell, Dictionary<LTECell, int> nbLteCellDic
            , FileInfo file, int subFuncId)
        {
            BackgroundResult bgResult = new BackgroundResult();
            bgResult.SubFuncID = subFuncId;
            bgResult.ProjectString = MasterCom.RAMS.BackgroundFunc.BackgroundFuncBaseSetting.GetInstance().projectType;

            bgResult.FileID = file.ID;
            bgResult.FileName = file.Name;
            bgResult.ISTime = file.BeginTime;
            bgResult.IETime = file.BeginTime;

            bgResult.CellType = BackgroundCellType.LTE;
            bgResult.LAC = servCell.TAC;
            bgResult.CI = servCell.ECI;
            bgResult.BCCH = servCell.EARFCN;
            bgResult.BSIC = servCell.PCI;
            bgResult.LongitudeMid = servCell.Longitude;
            bgResult.LatitudeMid = servCell.Latitude;
            bgResult.StrDesc = "主邻区";

            bgResult.AddImageValue(nbLteCellDic.Count);
            foreach (var varNbCell in nbLteCellDic)
            {
                bgResult.AddImageValue(varNbCell.Key.ECI);
                bgResult.AddImageValue(varNbCell.Value);
            }

            return bgResult;
        }

        protected void bgResultToCfgDic(BackgroundResult bgResult, out LTECell serverCell
            , out Dictionary<LTECell, int> nbLteCellDic)
        {
            DateTime beginTime = MasterCom.Util.JavaDate.GetDateTimeFromMilliseconds(bgResult.ISTime * 1000L);
            CellManager cellManager = CellManager.GetInstance();

            serverCell = cellManager.GetNearestLTECell(beginTime, bgResult.LAC, bgResult.CI, bgResult.BCCH
                , bgResult.BSIC, bgResult.LongitudeMid, bgResult.LatitudeMid);
            nbLteCellDic = new Dictionary<LTECell, int>();

            int nbCellCount = bgResult.GetImageValueInt();
            for (int i = 0; i < nbCellCount; i++)
            {
                int eci = bgResult.GetImageValueInt();
                int count = bgResult.GetImageValueInt();
                LTECell nbCell = cellManager.GetLTECellByECI(beginTime, eci);
                if (nbCell != null)
                {
                    nbLteCellDic[nbCell] = count;
                }
            }
        }

        protected override void initBackgroundImageDesc()
        {
            foreach (BackgroundResult bgResult in BackgroundResultList)
            {
                if (bgResult.StrDesc == "采样点")
                {
                    addCellItemToDic(cellSampleDic, ZTLteNBCellCheckBothCellItem.BgReslutToCellItem(bgResult));
                }
                else if (bgResult.StrDesc == "信令")
                {
                    addCellItemToDic(cellMsgDic, ZTLteNBCellCheckBothCellItem.BgReslutToCellItem(bgResult));
                }
                else if (bgResult.StrDesc == "主邻区")
                {
                    LTECell curCell;
                    Dictionary<LTECell, int> curNbCellDic;
                    bgResultToCfgDic(bgResult, out curCell, out curNbCellDic);

                    if (curCell != null)
                    {
                        addNBDic(curCell, curNbCellDic);
                    }
                }
            }
            List<ZTLteNBCellCheckBothCellItem> resultList = filterResultByCondition();
            this.BackgroundNPOIRowResultDic.Clear();
            List<MasterCom.Util.NPOIRow> rows = ZTLteNBCellCheckBothAnaListForm.ConvertToNPOIRows(resultList, true);
            if (rows != null)
            {
                this.BackgroundNPOIRowResultDic[""] = rows;
            }
            resultList.Clear();
        }

        private void addNBDic(LTECell curCell, Dictionary<LTECell, int> curNbCellDic)
        {
            Dictionary<LTECell, int> nbCellDicSum;
            if (nbCfgDic.TryGetValue(curCell, out nbCellDicSum))
            {
                foreach (var varNbCell in curNbCellDic)
                {
                    int count;
                    if (!nbCellDicSum.TryGetValue(varNbCell.Key, out count))
                    {
                        nbCellDicSum.Add(varNbCell.Key, varNbCell.Value);
                    }
                }
            }
            else
            {
                nbCfgDic.Add(curCell, curNbCellDic);
            }
        }

        protected void addCellItemToDic(Dictionary<string, ZTLteNBCellCheckBothCellItem> cellItemDic
            , ZTLteNBCellCheckBothCellItem cellItem)
        {
            if (cellItem == null || cellItem.ServCell == null)
            {
                return;
            }

            ZTLteNBCellCheckBothCellItem cellItemSum;
            if (cellItemDic.TryGetValue(cellItem.ServCell.Name, out cellItemSum))
            {
                cellItemSum.Merge(cellItem);
            }
            else
            {
                cellItemDic.Add(cellItem.ServCell.Name, cellItem);
            }
        }
        #endregion
    }

    public class ZTLteNBCellCheckBothCellItem
    {
        public int SN { get; set; }
        public LTECell ServCell { get; set; }
        public int SampleCount { get; set; }
        public float RSRPTotal { get; set; }
        public Dictionary<string, ZTLteNBCellCheckBothNBItem> NBDic { get; set; }   //Dictionary<cellName,ZTLteNBCellCheckBothNBItem> 

        //主服的邻区配置信息
        public string NBCfg { get; set; }
        public int NBCount { get; set; }

        public List<int> FileIds { get; set; } = new List<int>();//目前只在网络体检导出时记录
        public List<string> FileNames { get; set; } = new List<string>();
        public ZTLteNBCellCheckBothCellItem(LTECell lteCell, float rsrp)
        {
            ServCell = lteCell;
            SampleCount = 1;
            RSRPTotal = rsrp;
            NBDic = new Dictionary<string, ZTLteNBCellCheckBothNBItem>();

            NBCfg = "";
            NBCount = 0;
        }

        //用于针对MR的数据的初始化，MR上报的可能有多次
        public ZTLteNBCellCheckBothCellItem(LTECell lteCell, float rsrpTotal, int mrCount)
        {
            ServCell = lteCell;
            SampleCount = mrCount;
            RSRPTotal = rsrpTotal;
            NBDic = new Dictionary<string, ZTLteNBCellCheckBothNBItem>();

            NBCfg = "";
            NBCount = 0;
        }

        public void MergeData(float rsrp)
        {
            SampleCount++;
            RSRPTotal += rsrp;
        }

        //MR上报的可能有多次
        public void MergeData(float rsrpTotal, int mrCount)
        {
            SampleCount += mrCount;
            RSRPTotal += rsrpTotal;
        }

        public void Merge(ZTLteNBCellCheckBothCellItem otherCellItem)
        {
            this.SampleCount += otherCellItem.SampleCount;
            this.RSRPTotal += otherCellItem.RSRPTotal;

            foreach (int fileId in otherCellItem.FileIds)
            {
                if (!this.FileIds.Contains(fileId))
                {
                    this.FileIds.Add(fileId);
                }
            }
            foreach (string fileName in otherCellItem.FileNames)
            {
                if (!this.FileNames.Contains(fileName))
                {
                    this.FileNames.Add(fileName);
                }
            }

            foreach (var varNbCellItem in otherCellItem.NBDic)
            {
                ZTLteNBCellCheckBothNBItem cellItemSum;
                if (this.NBDic.TryGetValue(varNbCellItem.Key, out cellItemSum))
                {
                    cellItemSum.MergeData(varNbCellItem.Value);
                }
                else
                {
                    this.NBDic.Add(varNbCellItem.Key, varNbCellItem.Value);
                }
            }
        }

        #region 预处理
        public string AvgRSRP
        {
            get
            {
                if (SampleCount > 0)
                {
                    return Math.Round((RSRPTotal / (float)SampleCount), 2).ToString();
                }
                else
                {
                    return "";
                }
            }
        }
        #endregion

        public BackgroundResult ConverToBackgroundResult(FileInfo file, int subFuncId, string strDesc)
        {
            BackgroundResult bgResult = new BackgroundResult();
            bgResult.SubFuncID = subFuncId;
            bgResult.ProjectString = MasterCom.RAMS.BackgroundFunc.BackgroundFuncBaseSetting.GetInstance().projectType;

            bgResult.FileID = file.ID;
            bgResult.FileName = file.Name;
            bgResult.ISTime = file.BeginTime;
            bgResult.IETime = file.BeginTime;

            bgResult.CellType = BackgroundCellType.LTE;
            bgResult.LAC = ServCell.TAC;
            bgResult.CI = ServCell.ECI;
            bgResult.BCCH = ServCell.EARFCN;
            bgResult.BSIC = ServCell.PCI;
            bgResult.LongitudeMid = ServCell.Longitude;
            bgResult.LatitudeMid = ServCell.Latitude;

            bgResult.SampleCount = this.SampleCount;
            if (SampleCount > 0)
            {
                bgResult.RxLevMean = (float)Math.Round((RSRPTotal / (float)SampleCount), 2);
            }
            else
            {
                bgResult.RxLevMean = 255;
            }
            bgResult.StrDesc = strDesc;

            bgResult.AddImageValue(NBDic.Count);
            foreach (ZTLteNBCellCheckBothNBItem nbItem in NBDic.Values)
            {
                bgResult.AddImageValue(nbItem.NBCell.ECI);
                bgResult.AddImageValue(nbItem.SampleCount);
                bgResult.AddImageValue(nbItem.AvgRSRP);
                bgResult.AddImageValue(nbItem.Score);
                bgResult.AddImageValue(nbItem.Source);
            }

            return bgResult;
        }

        public static ZTLteNBCellCheckBothCellItem BgReslutToCellItem(BackgroundResult bgResult)
        {
            DateTime beginTime = MasterCom.Util.JavaDate.GetDateTimeFromMilliseconds(bgResult.ISTime * 1000L);
            CellManager cellManager = CellManager.GetInstance();

            float rsrpSum = bgResult.SampleCount * bgResult.RxLevMean;
            LTECell lteCell = cellManager.GetNearestLTECell(beginTime, bgResult.LAC, bgResult.CI, bgResult.BCCH
                , bgResult.BSIC, bgResult.LongitudeMid, bgResult.LatitudeMid);
            if (lteCell == null)
            {
                return null;
            }

            ZTLteNBCellCheckBothCellItem cellItem = new ZTLteNBCellCheckBothCellItem(lteCell, rsrpSum, bgResult.SampleCount);
            cellItem.FileIds.Add(bgResult.FileID);
            cellItem.FileNames.Add(bgResult.FileName);

            int nbCellCount = bgResult.GetImageValueInt();
            for (int i = 0; i < nbCellCount; i++)
            {
                int eci = bgResult.GetImageValueInt();
                int sampleCount = bgResult.GetImageValueInt();
                string avgRsrpStr = bgResult.GetImageValueString();
                int score = bgResult.GetImageValueInt();
                string sourceStr = bgResult.GetImageValueString();

                LTECell nbCell = cellManager.GetLTECellByECI(beginTime, eci);
                if (nbCell != null)
                {
                    ZTLteNBCellCheckBothNBItem nbItem = new ZTLteNBCellCheckBothNBItem(nbCell);
                    cellItem.NBDic.Add(nbCell.Name, nbItem);
                    nbItem.SampleCount = sampleCount;
                    nbItem.Score = score;
                    nbItem.Source = sourceStr;

                    float avgRsrp;
                    if (float.TryParse(avgRsrpStr, out avgRsrp))
                    {
                        nbItem.RSRPTotal = avgRsrp * nbItem.SampleCount;
                    }
                }
            }
            return cellItem;
        }
    }

    public class ZTLteNBCellCheckBothNBItem
    {
        public LTECell NBCell { get; set; }
        public int EARFCN { get; set; }
        public int PCI { get; set; }
        public int SampleCount { get; set; }
        public float RSRPTotal { get; set; }
        public int Score { get; set; }          //得分，按照在邻区中的顺序进行加权计算
        public int Order { get; set; }          //排名，根据得分，最高分排名为1
        public string Source { get; set; }      //数据来源
        public string StatusByMsg { get; set; } //是否漏配的判断(根据信令中的邻区列表判断)
        public string StatusByParam { get; set; } //是否漏配的判断(根据工参中的邻区列表判断)
        public double Distance { get; set; }

        public ZTLteNBCellCheckBothNBItem(LTECell nbCell, float RSRPTotal, int index)
        {
            this.NBCell = nbCell;
            this.SampleCount = 1;
            this.RSRPTotal = RSRPTotal;
            this.StatusByMsg = "未知";
            this.StatusByParam = "未知";
            this.Source = "来自采样点";
            this.Distance = -999;
            this.Score = getScoreByIndex(index);
            this.Order = 0;
        }

        //用于MR邻区的初始化
        public ZTLteNBCellCheckBothNBItem(int EARFCN, int PCI, float RSRPTotal, int index)
        {
            this.EARFCN = EARFCN;
            this.PCI = PCI;
            this.SampleCount = 1;
            this.RSRPTotal = RSRPTotal;
            this.StatusByMsg = "未知";
            this.StatusByParam = "未知";
            this.Source = "来自MR";
            this.Distance = -999;
            this.Score = getScoreByIndex(index);
            this.Order = 0;
        }

        //用于BackgroundResult转换
        public ZTLteNBCellCheckBothNBItem(LTECell nbCell)
        {
            this.NBCell = nbCell;
            this.EARFCN = nbCell.EARFCN;
            this.PCI = nbCell.PCI;
            this.StatusByMsg = "未知";
            this.StatusByParam = "未知";
            this.Distance = -999;
            this.Order = 0;
        }

        public void AccuScore(int index)
        {
            this.Score += getScoreByIndex(index);
        }

        public int getScoreByIndex(int index)
        {
            int score = 0;
            switch (index)
            {
                case 0:
                    score = 6;
                    break;
                case 1:
                    score = 5;
                    break;
                case 2:
                    score = 4;
                    break;
                case 3:
                    score = 3;
                    break;
                case 4:
                    score = 2;
                    break;
                case 5:
                    score = 1;
                    break;
                default:
                    break;
            }

            return score;
        }

        public void MergeData(float rsrp, int index)
        {
            SampleCount++;
            RSRPTotal += rsrp;
            Score += getScoreByIndex(index);
        }

        //用于MR数据的合并
        public void MergeData(ZTLteNBCellCheckBothNBItem nbItem)
        {
            SampleCount += nbItem.SampleCount;
            RSRPTotal += nbItem.RSRPTotal;
            Score += nbItem.Score;
        }
        public static IComparer<ZTLteNBCellCheckBothNBItem> GetCompareByScore()
        {
            if (comparerByScore == null)
            {
                comparerByScore = new ComparerByScore();
            }
            return comparerByScore;
        }
        public class ComparerByScore : IComparer<ZTLteNBCellCheckBothNBItem>
        {
            public int Compare(ZTLteNBCellCheckBothNBItem x, ZTLteNBCellCheckBothNBItem y)
            {
                return y.Score - x.Score;
            }
        }
        private static IComparer<ZTLteNBCellCheckBothNBItem> comparerByScore;

        #region 预处理
        public string AvgRSRP
        {
            get
            {
                if (SampleCount > 0)
                {
                    return Math.Round((RSRPTotal / (float)SampleCount), 2).ToString();
                }
                else
                {
                    return "";
                }
            }
        }

        public string DistanceStr
        {
            get
            {
                if (Distance != -999)
                {
                    return Math.Round(Distance, 2).ToString();
                }
                else
                {
                    return "";
                }
            }
        }

        public string Advice
        {
            get
            {
                if (Source == "采样点|MR")
                {
                    if ((StatusByMsg == "漏配" || StatusByMsg == "未知") && StatusByParam == "漏配")
                    {
                        return "必配";
                    }
                    else
                    {
                        return "";
                    }
                }
                else
                {
                    return "";
                }
            }
        }
        #endregion
    }


    //用来记录一次完整的信令过程，
    public class ZTLteNBCellCheckMsgItem
    {
        public int TAC { get; set; }
        public int ECI { get; set; }
        public int EARFCN { get; set; }
        public int PCI { get; set; }
        //通过信令下发的邻区频点和PCI列表
        public Dictionary<int, Dictionary<int, int>> NBCfgDic { get; set; }       //Dictionary<EARFCN,Dictionary<PCI,count>>
        //由于上报的MR中无频点信息，需要在下发配置的时候，记录MeasID和频点的对应关系
        public Dictionary<int, int> NBMeasEarfcnDic { get; set; }               //Dictionary<MeasID, EARFCN>>

        public bool IsGotHOMsg { get; set; }
        public bool IsGotNBConfigMsg { get; set; }
        public bool IsGotSIB1Msg { get; set; }
        public bool IsGotMRMsg { get; set; }

        public TestPoint Tp { get; set; }
        public int MRCount { get; set; }
        public float RSRPTotal { get; set; }

        public Dictionary<int, Dictionary<int, ZTLteNBCellCheckBothNBItem>> NBRptDic { get; set; }   //Dictionary<EARFCN, Dictionary<PCI,ZTLteNBCellBothNBItem>>

        public ZTLteNBCellCheckMsgItem()
        {
            TAC = 0;
            ECI = 0;
            EARFCN = 0;
            PCI = 0;

            NBCfgDic = new Dictionary<int, Dictionary<int, int>>();
            NBMeasEarfcnDic = new Dictionary<int, int>();

            IsGotHOMsg = false;
            IsGotNBConfigMsg = false;
            IsGotSIB1Msg = false;
            IsGotMRMsg = false;

            MRCount = 0;
            RSRPTotal = 0;
            NBRptDic = new Dictionary<int, Dictionary<int, ZTLteNBCellCheckBothNBItem>>();
        }
    }
}



