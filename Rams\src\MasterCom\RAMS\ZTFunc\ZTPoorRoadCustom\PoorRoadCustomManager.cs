﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class PoorRoadCustomManager
    {
        public string Iroad { get; set; }

        public List<PoorGridRoadInfo> MtGridRoadInfoList { get; set; } = new List<PoorGridRoadInfo>();
    }

    /// <summary>
    /// 道路详细信息列表(GSM窗体展示)
    /// </summary>
    public class PoorRoadCustomList
    {
        /// <summary>
        /// 道路名称
        /// </summary>
        public string RoadName { get; set; }
        /// <summary>
        /// 距离
        /// </summary>
        public double Distance { get; set; }
        /// <summary>
        /// 重复次数
        /// </summary>
        public int Num { get; set; }
        /// <summary>
        /// 平均场强
        /// </summary>
        public double Average { get; set; }
        public double F140f94 { get; set; }
        public double F94f90 { get; set; }
        public double F90f85 { get; set; }
        public double F85f80 { get; set; }
        public double F80f75 { get; set; }
        public double F75f10 { get; set; }
        public float Rxlmean { get; set; }
        public int Rxl75 { get; set; }
        public int Rxl76_80 { get; set; }
        public int Rxl81_85 { get; set; }
        public int Rxl86_90 { get; set; }
        public int Rxl91_94 { get; set; }
        public int Rxl94 { get; set; }
        public double Pesqmean { get; set; }
        public int Pesq28 { get; set; }
        public int Pesq28_30 { get; set; }
        public int Pesq30 { get; set; }
        public double Tamean { get; set; }
        public int Ta0_1 { get; set; }
        public int Ta2_4 { get; set; }
        public int Ta5_9 { get; set; }
        public int Ta10 { get; set; }
        public double Rxqmean { get; set; }
        public int Rxq0_3 { get; set; }
        public int Rxq4_5 { get; set; }
        public int Rxq6_7 { get; set; }
        public double Bmstxmean { get; set; }
        public int Bmst0_15 { get; set; }
        public int Bmst15_24 { get; set; }
        public int Bmst24_29 { get; set; }
        public int Bmst29_43 { get; set; }

        private List<PoorGridRoadInfo> grindRoadInfoList = new List<PoorGridRoadInfo>();
        public List<PoorGridRoadInfo> GrindRoadInfoList
        {
            get { return grindRoadInfoList; }
            set
            {
                List<PoorRoadCustomSample> samplelist = new List<PoorRoadCustomSample>();
                grindRoadInfoList = value;
                foreach (PoorGridRoadInfo item in grindRoadInfoList)
                {
                    if (item.MustTestList.Count > 0)
                    {
                        samplelist.AddRange(item.MustTestList);
                        Distance = Distance + item.Fdistance;
                    }
                }
                Distance = Math.Round(Distance, 2);
                List = samplelist;
            }
        }

        private List<PoorRoadCustomSample> list = new List<PoorRoadCustomSample>();
        public List<PoorRoadCustomSample> List
        {
            get { return list; }
            set
            {
                list = value;
                setValue();
            }
        }

        private void setValue()
        {
            List<string> fileidstr = new List<string>(); //文件集合,用来判断文件数目(重复次数)
            foreach (PoorRoadCustomSample item in list)
            {
                int subtemp = item.Wrxlevsub;
                int rxq = item.Brxqualsub;
                int bmstx = item.Bmstxpower;
                double mostemp = item.Pesq;
                int bta = item.Bta;

                //电平
                if (!fileidstr.Contains(item.Ifileid))
                {
                    fileidstr.Add(item.Ifileid);
                }

                addAverage(subtemp);

                addRxqmean(rxq);

                addBmstxmean(bmstx);

                addPesqmean(mostemp);

                addTamean(bta);
            }
            Average = Math.Round(Average / list.Count, 2);
            Rxqmean = Math.Round(Rxqmean / list.Count, 2);
            Bmstxmean = Math.Round(Bmstxmean / list.Count, 2);
            Pesqmean = Math.Round(Pesqmean / list.Count, 2);
            Tamean = Math.Round(Tamean / list.Count, 2);

            Num = fileidstr.Count;
        }

        private void addTamean(int bta)
        {
            //TA
            if (bta <= 1)
            {
                Ta0_1 = Ta0_1 + 1;
                Tamean = Tamean + Tamean;
            }
            if (bta >= 2 && bta <= 4)
            {
                Ta2_4 = Ta2_4 + 1;
                Tamean = Tamean + Tamean;
            }
            if (bta >= 5 && bta <= 9)
            {
                Ta5_9 = Ta5_9 + 1;
                Tamean = Tamean + Tamean;
            }
            if (bta >= 10)
            {
                Ta10 = Ta10 + 1;
                Tamean = Tamean + Tamean;
            }
        }

        private void addPesqmean(double mostemp)
        {
            //MOS
            if (mostemp < 2.8)
            {
                Pesq28 = Pesq28 + 1;
                Pesqmean = Pesqmean + Pesqmean;
            }
            if (mostemp >= 2.8 && mostemp < 3)
            {
                Pesq28_30 = Pesq28_30 + 1;
                Pesqmean = Pesqmean + Pesqmean;
            }
            if (mostemp >= 3 && mostemp <= 5)
            {
                Pesq30 = Pesq30 + 1;
                Pesqmean = Pesqmean + Pesqmean;
            }
        }

        private void addBmstxmean(int bmstx)
        {
            //手机发射功率
            if (bmstx <= 15)
            {
                Bmst0_15 = Bmst0_15 + 1;
                Bmstxmean = Bmstxmean + Bmstxmean;
            }
            if (bmstx > 15 && bmstx <= 24)
            {
                Bmst15_24 = Bmst15_24 + 1;
                Bmstxmean = Bmstxmean + Bmstxmean;
            }
            if (bmstx > 24 && bmstx <= 29)
            {
                Bmst24_29 = Bmst24_29 + 1;
                Bmstxmean = Bmstxmean + Bmstxmean;
            }
            if (bmstx > 29 && bmstx <= 43)
            {
                Bmst29_43 = Bmst29_43 + 1;
                Bmstxmean = Bmstxmean + Bmstxmean;
            }
        }

        private void addRxqmean(int rxq)
        {
            //质量
            if (rxq <= 3)
            {
                Rxq0_3 = Rxq0_3 + 1;
                Rxqmean = Rxqmean + Rxqmean;
            }
            if (rxq > 4 && rxq <= 5)
            {
                Rxq4_5 = Rxq4_5 + 1;
                Rxqmean = Rxqmean + Rxqmean;
            }
            if (rxq > 6 && rxq <= 7)
            {
                Rxq6_7 = Rxq6_7 + 1;
                Rxqmean = Rxqmean + Rxqmean;
            }
        }

        private void addAverage(int subtemp)
        {
            if (subtemp > -140 && subtemp <= -94)
            {
                F140f94 = F140f94 + 1;
                Average = Average + subtemp;
            }
            if (subtemp > -94 && subtemp <= -90)
            {
                F94f90 = F94f90 + 1;
                Average = Average + subtemp;
            }
            if (subtemp > -90 && subtemp <= -85)
            {
                F90f85 = F90f85 + 1;
                Average = Average + subtemp;
            }
            if (subtemp > -85 && subtemp <= -80)
            {
                F85f80 = F85f80 + 1;
                Average = Average + subtemp;
            }
            if (subtemp > -80 && subtemp <= -75)
            {
                F80f75 = F80f75 + 1;
                Average = Average + subtemp;
            }
            if (subtemp > -75 && subtemp <= -10)
            {
                F75f10 = F75f10 + 1;
                Average = Average + subtemp;
            }
        }
    }

    /// <summary>
    /// 道路详细信息列表(GSM窗体展示)
    /// </summary>
    public class PoorRoadCustomTDList
    {

        /// <summary>
        /// 道路名称
        /// </summary>
        public string RoadName { get; set; }
        /// <summary>
        /// 距离
        /// </summary>
        public double Distance { get; set; }
        /// <summary>
        /// 重复次数
        /// </summary>
        public int Num { get; set; }
        public double Pccpch_rscpmean { get; set; }
        public int Pccpch_rscpf75_10 { get; set; }
        public int Pccpch_rscpf80_75 { get; set; }
        public int Pccpch_rscpf85_80 { get; set; }
        public int Pccpch_rscpf90_85 { get; set; }
        public int Pccpch_rscpf94_90 { get; set; }
        public int Pccpch_rscpf140_94 { get; set; }
        public double Pccpch_c2imean { get; set; }
        public int Pccpch_c2i20_10 { get; set; }
        public int Pccpch_c2i10_3 { get; set; }
        public int Pccpch_c2i3_15 { get; set; }
        public int Pccpch_c2i15_25 { get; set; }
        public double Dpch_c2imean { get; set; }
        public int Dpch_c2i20_10 { get; set; }
        public int Dpch_c2i10_3 { get; set; }
        public int Dpch_c2i3_15 { get; set; }
        public int Dpch_c2i15_25 { get; set; }
        public double Txpowermean { get; set; }
        public int Txpower50_20 { get; set; }
        public int Txpower20_0 { get; set; }
        public int Txpower0_15 { get; set; }
        public int Txpower15_34 { get; set; }
        public double Mosmean { get; set; }
        public double Mos0_28 { get; set; }
        public double Mos28_3 { get; set; }
        public double Mos3_5 { get; set; }

        private List<PoorGridRoadInfo> grindRoadInfoList = new List<PoorGridRoadInfo>();
        public List<PoorGridRoadInfo> GrindRoadInfoList
        {
            get { return grindRoadInfoList; }
            set
            {
                List<PoorRoadCustomTDSample> samplelist = new List<PoorRoadCustomTDSample>();
                grindRoadInfoList = value;
                foreach (PoorGridRoadInfo item in grindRoadInfoList)
                {
                    if (item.MustTesttdList.Count > 0)
                    {
                        samplelist.AddRange(item.MustTesttdList);
                        Distance = Distance + item.Fdistance;
                    }
                }
                Distance = Math.Round(Distance, 2);
                List = samplelist;
            }
        }

        private List<PoorRoadCustomTDSample> list = new List<PoorRoadCustomTDSample>();
        public List<PoorRoadCustomTDSample> List
        {
            get { return list; }
            set
            {
                list = value;
                setValue();
            }
        }

        private void setValue()
        {
            List<string> fileidstr = new List<string>(); //文件集合,用来判断文件数目(重复次数)

            foreach (PoorRoadCustomTDSample item in list)
            {
                int rscp = item.PCCPCH_RSCP;
                int pccpchc2i = item.PCCPCH_C2I;
                int dpchc2i = item.DPCH_C2I;
                int txpowertemp = item.PCCPCHTxPower;
                double mostemp = item.MOS;

                //电平
                if (!fileidstr.Contains(item.Ifileid))
                {
                    fileidstr.Add(item.Ifileid);
                }

                addPccpch_rscpmean(rscp);

                addTxpowermean(txpowertemp);

                addPccpch_c2imean(pccpchc2i);

                addDpch_c2imean(dpchc2i);

                addMosmean(mostemp);
            }
            Pccpch_rscpmean = Math.Round(Pccpch_rscpmean / list.Count, 2);
            Txpowermean = Math.Round(Txpowermean / list.Count, 2);
            Pccpch_c2imean = Math.Round(Pccpch_c2imean / list.Count, 2);
            Dpch_c2imean = Math.Round(Dpch_c2imean / list.Count, 2);
            Mosmean = Math.Round(Mosmean / list.Count, 2);

            Num = fileidstr.Count;
        }

        private void addPccpch_rscpmean(int rscp)
        {
            if (rscp > -140 && rscp <= -94)
            {
                Pccpch_rscpf140_94 = Pccpch_rscpf140_94 + 1;
                Pccpch_rscpmean = Pccpch_rscpmean + rscp;
            }
            if (rscp > -94 && rscp <= -90)
            {
                Pccpch_rscpf94_90 = Pccpch_rscpf94_90 + 1;
                Pccpch_rscpmean = Pccpch_rscpmean + rscp;
            }
            if (rscp > -90 && rscp <= -85)
            {
                Pccpch_rscpf90_85 = Pccpch_rscpf90_85 + 1;
                Pccpch_rscpmean = Pccpch_rscpmean + rscp;
            }
            if (rscp > -85 && rscp <= -80)
            {
                Pccpch_rscpf85_80 = Pccpch_rscpf85_80 + 1;
                Pccpch_rscpmean = Pccpch_rscpmean + rscp;
            }
            if (rscp > -80 && rscp <= -75)
            {
                Pccpch_rscpf80_75 = Pccpch_rscpf80_75 + 1;
                Pccpch_rscpmean = Pccpch_rscpmean + rscp;
            }
            if (rscp > -75 && rscp <= -10)
            {
                Pccpch_rscpf75_10 = Pccpch_rscpf75_10 + 1;
                Pccpch_rscpmean = Pccpch_rscpmean + rscp;
            }
        }

        private void addTxpowermean(int txpowertemp)
        {
            //质量
            if (txpowertemp > -50 && txpowertemp <= 20)
            {
                Txpower50_20 = Txpower50_20 + 1;
                Txpowermean = Txpowermean + Txpowermean;
            }
            if (txpowertemp > -20 && txpowertemp <= 0)
            {
                Txpower20_0 = Txpower20_0 + 1;
                Txpowermean = Txpowermean + Txpowermean;
            }
            if (txpowertemp > 0 && txpowertemp <= 15)
            {
                Txpower0_15 = Txpower0_15 + 1;
                Txpowermean = Txpowermean + Txpowermean;
            }
            if (txpowertemp > 15 && txpowertemp <= 34)
            {
                Txpower15_34 = Txpower15_34 + 1;
                Txpowermean = Txpowermean + Txpowermean;
            }
        }

        private void addPccpch_c2imean(int pccpchc2i)
        {
            //pccpch_c2i
            if (pccpchc2i <= -10)
            {
                Pccpch_c2i20_10 = Pccpch_c2i20_10 + 1;
                Pccpch_c2imean = Pccpch_c2imean + Pccpch_c2imean;
            }
            if (pccpchc2i > -10 && pccpchc2i <= -3)
            {
                Pccpch_c2i10_3 = Pccpch_c2i10_3 + 1;
                Pccpch_c2imean = Pccpch_c2imean + Pccpch_c2imean;
            }
            if (pccpchc2i > 3 && pccpchc2i <= 15)
            {
                Pccpch_c2i3_15 = Pccpch_c2i3_15 + 1;
                Pccpch_c2imean = Pccpch_c2imean + Pccpch_c2imean;
            }
            if (pccpchc2i > 15 && pccpchc2i <= 25)
            {
                Pccpch_c2i15_25 = Pccpch_c2i15_25 + 1;
                Pccpch_c2imean = Pccpch_c2imean + Pccpch_c2imean;
            }
        }

        private void addDpch_c2imean(int dpchc2i)
        {
            //dpch_c2i
            if (dpchc2i <= -10)
            {
                Dpch_c2i20_10 = Dpch_c2i20_10 + 1;
                Dpch_c2imean = Dpch_c2imean + Dpch_c2imean;
            }
            if (dpchc2i > -10 && dpchc2i <= -3)
            {
                Dpch_c2i10_3 = Dpch_c2i10_3 + 1;
                Dpch_c2imean = Dpch_c2imean + Dpch_c2imean;
            }
            if (dpchc2i > 3 && dpchc2i <= 15)
            {
                Dpch_c2i3_15 = Dpch_c2i3_15 + 1;
                Dpch_c2imean = Dpch_c2imean + Dpch_c2imean;
            }
            if (dpchc2i > 15 && dpchc2i <= 25)
            {
                Dpch_c2i15_25 = Dpch_c2i15_25 + 1;
                Dpch_c2imean = Dpch_c2imean + Dpch_c2imean;
            }
        }

        private void addMosmean(double mostemp)
        {
            //MOS
            if (mostemp < 2.8)
            {
                Mos0_28 = Mos0_28 + 1;
                Mosmean = Mosmean + Mosmean;
            }
            if (mostemp >= 2.8 && mostemp < 3)
            {
                Mos28_3 = Mos28_3 + 1;
                Mosmean = Mosmean + Mosmean;
            }
            if (mostemp >= 3 && mostemp <= 5)
            {
                Mos3_5 = Mos3_5 + 1;
                Mosmean = Mosmean + Mosmean;
            }
        }
    }

    /// <summary>
    /// 道路栅格信息表
    /// </summary>
    public class PoorGridRoadInfo
    {
        public int Iroad { get; set; }
        public double Fdistance { get; set; }
        public int Itype { get; set; }
        //覆盖采样点集合
        public List<PoorRoadCustomSample> MustTestList { get; set; } = new List<PoorRoadCustomSample>();
        //覆盖采样点集合
        public List<PoorRoadCustomTDSample> MustTesttdList { get; set; } = new List<PoorRoadCustomTDSample>();
        public int Order { get; set; }
        public int Isampleid { get; set; }
        public string Strgrid { get; set; }
        public string Strroad { get; set; }
        public float Ilongitude { get; set; }
        public float Ilatitude { get; set; }
        public string Strdesc1 { get; set; }
        public string Strdesc2 { get; set; }
        public string Strdesc3 { get; set; }
    }

    /// <summary>
    /// GSM数据库表
    /// </summary>
    public class PoorRoadCustomSample
    {
        public string Strroad { get; set; }
        public int Isampleid { get; set; }
        public string Ifileid { get; set; }
        public DateTime Itime { get; set; }
        public int Wtimems { get; set; }
        public double Ilongitude { get; set; }
        public double Ilatitude { get; set; }
        public int Ilac { get; set; }
        public int Ici { get; set; }
        public int Wbcch { get; set; }
        public int Bbsic { get; set; }
        public int Wrxlevsub { get; set; }
        public int Brxqualsub { get; set; }
        public int Speechcodec { get; set; }
        public int Bmstxpower { get; set; }
        public double Pesq { get; set; }
        public int Bta { get; set; }
    }

    /// <summary>
    /// TD数据库表
    /// </summary>
    public class PoorRoadCustomTDSample
    {
        public string Strroad { get; set; }
        public int Isampleid { get; set; }
        public string Ifileid { get; set; }
        public DateTime Itime { get; set; }
        public int Wtimems { get; set; }
        public double Ilongitude { get; set; }
        public double Ilatitude { get; set; }
        public int Ilac { get; set; }
        public int Ici { get; set; }
        public int PCCPCH_RSCP { get; set; }
        public int PCCPCH_C2I { get; set; }
        public int DPCH_RSCP { get; set; }
        public int DPCH_C2I { get; set; }
        public int PCCPCHTxPower { get; set; }
        public double MOS { get; set; }
        public int GSMCarrierRSSI { get; set; }
        public int NbCell1_Rscp { get; set; }
        public int NbCell2_Rscp { get; set; }
        public int NbCell3_Rscp { get; set; }
    }
}
