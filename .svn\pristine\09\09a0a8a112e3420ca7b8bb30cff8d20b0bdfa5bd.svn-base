﻿using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTWeakCoverByEventResultForm : MinCloseForm
    {
        public ZTWeakCoverByEventResultForm(MainModel mainModel)
            : base(mainModel)
        {
            InitializeComponent();
            this.mainModel = mainModel;
        }
        MainModel mainModel = null;
        List<WeakCoverRoadStatInfo> weakCoverRoadInfoLists = null;
        List<WeakCoverRoadSummary> weakCoverRoadSummaryInfoList = null;
        public void FillData(List<WeakCoverRoadStatInfo> weakCoverRoadInfoLists
            , Dictionary<CityGridKey, WeakCoverRoadSummary> weakCoverRoadSummaryInfoDic)
        {
            this.weakCoverRoadInfoLists = new List<WeakCoverRoadStatInfo>();
            this.weakCoverRoadInfoLists = weakCoverRoadInfoLists;
            gridDataView.DataSource = this.weakCoverRoadInfoLists;
            gridDataView.RefreshDataSource();

            this.weakCoverRoadSummaryInfoList = new List<WeakCoverRoadSummary>();
            this.weakCoverRoadSummaryInfoList.AddRange(weakCoverRoadSummaryInfoDic.Values);
            gridDataSummary.DataSource = this.weakCoverRoadSummaryInfoList;
            gridDataSummary.RefreshDataSource();
        }

        private void replayEvent_Click(object sender, EventArgs e)
        {
            if (gridView1.SelectedRowsCount > 0)
            {
                int[] rows = this.gridView1.GetSelectedRows();
                WeakCoverRoadStatInfo weakCover = this.gridView1.GetRow(rows[0]) as WeakCoverRoadStatInfo;
                MasterCom.RAMS.Model.Interface.FileReplayer.Replay(weakCover.Evt,true);
            }
        }

        private void xtraTabData_SelectedPageChanged(object sender, DevExpress.XtraTab.TabPageChangedEventArgs e)
        {
            replayEvent.Visible = true;
            if (xtraTabData.SelectedTabPageIndex == 1)
            {
                replayEvent.Visible = false;
            }
        }

        private void outPutData_Click(object sender, System.EventArgs e)
        {
            List<NPOIRow> dataTotal = new List<NPOIRow>();
            List<NPOIRow> datasDetails = new List<NPOIRow>();
            List<List<NPOIRow>> nrDatasList = new List<List<NPOIRow>>();
            List<string> sheetNames = new List<string>();

            #region 导出详情
            NPOIRow nrDetails = new NPOIRow();
            List<object> colsDetails = new List<object>();
            colsDetails.Add("地市名称");
            colsDetails.Add("道路类型");
            colsDetails.Add("网格号");
            colsDetails.Add("事件经度");
            colsDetails.Add("事件纬度");
            colsDetails.Add("开始时间");
            colsDetails.Add("结束时间");
            colsDetails.Add("持续时长(s)");
            colsDetails.Add("持续距离(m)");

            nrDetails.cellValues = colsDetails;
            datasDetails.Add(nrDetails);

            foreach (WeakCoverRoadStatInfo gridInfo in weakCoverRoadInfoLists)
            {
                NPOIRow nr = new NPOIRow();
                List<object> objs = new List<object>();

                objs.Add(gridInfo.StrCityName);
                objs.Add(gridInfo.StrGridType);
                objs.Add(gridInfo.StrGridName);
                objs.Add(gridInfo.DLng.ToString());
                objs.Add(gridInfo.DLat.ToString());
                objs.Add(gridInfo.StrBeginTime);
                objs.Add(gridInfo.StrEndTime);
                objs.Add(gridInfo.StrLastTime);
                objs.Add(gridInfo.StrLastDistance);

                nr.cellValues = objs;
                datasDetails.Add(nr);
            }
            nrDatasList.Add(datasDetails);
            sheetNames.Add("弱覆盖详情列表");
            #endregion

            #region 导出汇总
            NPOIRow nrTotal = new NPOIRow();
            List<object> colsTotal = new List<object>();
            colsTotal.Add("地市名称");
            colsTotal.Add("道路类型");
            colsTotal.Add("网格号");
            colsTotal.Add("弱覆盖路段数");
            colsTotal.Add("持续总时长(s)");
            colsTotal.Add("持续总距离(m)");
            colsTotal.Add("平均持续时长(s)");
            colsTotal.Add("平均持续距离(m)");

            nrTotal.cellValues = colsTotal;
            dataTotal.Add(nrTotal);

            foreach (WeakCoverRoadSummary gridCountInfo in weakCoverRoadSummaryInfoList)
            {
                NPOIRow nr = new NPOIRow();
                List<object> objs = new List<object>();

                objs.Add(gridCountInfo.StrCityName);
                objs.Add(gridCountInfo.StrGridType);
                objs.Add(gridCountInfo.StrGridName);
                objs.Add(gridCountInfo.IRoadNum.ToString());
                objs.Add(gridCountInfo.DTimeSum.ToString());
                objs.Add(gridCountInfo.DDistanceSum.ToString());
                objs.Add(gridCountInfo.DTimeAvg.ToString());
                objs.Add(gridCountInfo.DDistanceAvg.ToString());
                
                nr.cellValues = objs;
                dataTotal.Add(nr);
            }
            nrDatasList.Add(dataTotal);
            sheetNames.Add("弱覆盖汇总");
            #endregion

            ExcelNPOIManager.ExportToExcel(nrDatasList, sheetNames);
        }
    }
}
