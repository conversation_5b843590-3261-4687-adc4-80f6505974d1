﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Util;
using MasterCom.RAMS.Compare;
using MasterCom.Util;
using MasterCom.RAMS.Frame;
using MasterCom.RAMS.Stat;
using MasterCom.RAMS.Model;
using MasterCom.MTGis;

namespace MasterCom.RAMS.Func
{
    public partial class CPReportForm : MinCloseForm
    {
        private MainForm mainForm = null;
        private MapFormCompareHisShowLayer hisLayer;
        public CPReportForm(MainForm mf,MainModel mModel,MapFormCompareHisShowLayer layer)
            :base(mModel)
        {
            this.mainForm = mf;
            this.hisLayer = layer;
            InitializeComponent();
            listView.ListViewItemSorter = new ListViewSorter(listView);
            listViewBetter.ListViewItemSorter = new ListViewSorter(listViewBetter);
            listViewWorse.ListViewItemSorter = new ListViewSorter(listViewWorse);
            initOption();
        }

        private void initOption()
        {
            string str = hisLayer.CarrierPair;
            if(str.Length > 0)
            {
                str = str.Substring(0,1);
            }

            if(str == "Y")  //当前运营商是移动
            {
                cbxCarrier.Items.Add("移动VS联通");
                cbxCarrier.Items.Add("移动VS电信");
                cbxCarrier.SelectedIndex = 0; 
            }
            else if (str == "L")
            {
                cbxCarrier.Items.Add("联通VS移动");
                cbxCarrier.Items.Add("联通VS电信");
                cbxCarrier.SelectedIndex = 0; 
            }

            if (hisLayer.CarrierPair == "Y2L")
            {
                cbxCarrier.SelectedIndex = 0;
            }
            if(hisLayer.ServPair == "G2G")
            {
                cbxService.SelectedIndex = 0;
            }
            cbxKpi.SelectedIndex = hisLayer.KpiPos;

            if(hisLayer.ByMethod == 2)
            {
                rdTime.Checked = true;
            }
            else
            {
                rdSum.Checked = true;
            }

            for(int w =1;w<20;w++)
            {
                int span = 80*(w*2+1);
                cbxWindowSize.Items.Add(new IDNamePair(w,span+"×"+span));
            }
            cbxWindowSize.SelectedIndex = 1;//default 2
            for(int a = 10;a<400;a++)
            {
                int area = 6400 * a;
                cbxAreaSize.Items.Add(new IDNamePair(a, "" + area));
            }
            cbxAreaSize.SelectedIndex = 50;
        }
        public void FillDatas(CPGridReportUnit rptUnit)
        {
            this.listView.Items.Clear();
            listView.Items.Add(makeLVI("优势点", rptUnit.betterGrid, rptUnit.totleGrid));
            listView.Items.Add(makeLVI("劣势点", rptUnit.worseGrid, rptUnit.totleGrid));
            listView.Items.Add(makeLVI("相当点", rptUnit.equalGrid, rptUnit.totleGrid));
            listView.Items.Add(makeLVI("同优点", rptUnit.allGoodGrid, rptUnit.totleGrid));
            listView.Items.Add(makeLVI("同差点", rptUnit.allBadGrid, rptUnit.totleGrid));

            int unknown = rptUnit.totleGrid - rptUnit.betterGrid - rptUnit.worseGrid
                          - rptUnit.equalGrid - rptUnit.allGoodGrid - rptUnit.allBadGrid;

            listView.Items.Add(makeLVI("未知点", unknown, rptUnit.totleGrid));
        }
        public void FillDatas(CPPeriodStatUnit rptUnit)
        {
            this.listView.Items.Clear();
            listView.Items.Add(makeLVI("很优点(近期3天以上优势)", rptUnit.veryBetter, rptUnit.totle));
            listView.Items.Add(makeLVI("较优点(最近测试为优势)", rptUnit.better, rptUnit.totle));
            listView.Items.Add(makeLVI("很劣点(近期3天以上劣势)", rptUnit.veryWorse, rptUnit.totle));
            listView.Items.Add(makeLVI("较劣点(最近测试为劣势)", rptUnit.worse, rptUnit.totle));
            listView.Items.Add(makeLVI("其它", rptUnit.other, rptUnit.totle));
        }
        public IComparer<List<CompUnit>> GetCompareComp()
        {
            if (theComparer == null)
            {
                theComparer = new CompListCompare();
            }
            return theComparer;
        }

        private IComparer<List<CompUnit>> theComparer;

        public class CompListCompare : IComparer<List<CompUnit>>
        {
            public int Compare(List<CompUnit> x, List<CompUnit> y)
            {
                if(x.Count>0 && y.Count>0)
                {
                    int xstatus = x[0].GoodBadStatus;
                    int ystatus = y[0].GoodBadStatus;
                    if(xstatus==ystatus)
                    {
                        return y.Count - x.Count;
                    }
                    else
                    {
                        return xstatus - ystatus;
                    }
                }
                else
                {
                    return 0;
                }
               
            }
        }
        internal void FillComRegions(List<List<CompUnit>> retcompList)
        {
            retcompList.Sort(GetCompareComp());
            this.listViewBetter.Items.Clear();
            this.listViewWorse.Items.Clear();
            foreach(List<CompUnit> comList in retcompList)
            {
                if(comList.Count>0)
                {
                    if(comList[0].GoodBadStatus==1)
                    {
                        this.listViewBetter.Items.Add(makeRegLVI(this.listViewBetter.Items.Count + 1, comList));
                    }
                    else if(comList[0].GoodBadStatus==2)
                    {
                        this.listViewWorse.Items.Add(makeRegLVI(this.listViewWorse.Items.Count + 1, comList));
                    }
                }
            }
        }

        private ListViewItem makeRegLVI(int idx, List<CompUnit> comList)
        {
            ListViewItem lvi = new ListViewItem();
            lvi.Text = ""+idx;
            lvi.Tag = comList;
            lvi.SubItems[0].Tag = idx;
            lvi.SubItems.Add("" + comList.Count*6400);
            lvi.SubItems[1].Tag = comList.Count;

            //添加街道显示
            List<double> lonList = new List<double>();
            List<double> latList = new List<double>();
            foreach (CompUnit cu in comList)
            {
                lonList.Add(cu.MidLongitude);
                latList.Add(cu.MidLatitude);
            }
            string roadItem = GISManager.GetInstance().GetRoadPlaceDesc(lonList, latList);     
            lvi.SubItems.Add(roadItem);
            lvi.SubItems[2].Tag = roadItem;

            return lvi;
        }

        private ListViewItem makeLVI(string title,int num,int totle)
        {
            ListViewItem lvi = new ListViewItem();
            lvi.Text = title;
            lvi.SubItems.Add("" + num);
            lvi.SubItems[1].Tag = num;
            lvi.SubItems.Add(getPercentDesc(num, totle));
            lvi.SubItems[2].Tag = getPercentFloat(num, totle);
            return lvi;
        }

        private string getPercentDesc(int sub, int totle)
        {
            if(totle==0)
            {
                return "-";
            }
            else
            {
                return string.Format("{0:F2}%", 100.0*sub / totle);
            }
        }
        private float getPercentFloat(int sub,int totle)
        {
            if(totle==0)
            {
                return 0;
            }
            else
            {
                return (float)sub / totle;
            }
        }

        protected override string ShowImage
        {
            get
            {
                return "images\\cellquery.gif";
            }
        }

        private void miExportExcel_Click(object sender, EventArgs e)
        {
            try
            {
                MasterCom.Util.ExcelNPOIManager.ExportToExcel(this.listView);
            }
            catch (Exception ex)
            {
                MessageBox.Show("导出Excel失败!" + ex.Message);
            }
        }

        private void listViewBetter_SelectedIndexChanged(object sender, EventArgs e)
        {
            //
        }

        private void fireGotoSelectionRegion(List<CompUnit> compList)
        {
            DbRect gotoRect = new DbRect();
            //数据范围
            bool first = true;
            foreach (CompUnit u in compList)
            {
                first = setRect(gotoRect, first, u);
            }
            MapForm mf = mainForm.GetMapForm();
            if (mf != null)
            {
                DbPoint center = gotoRect.Center();
                mf.GoToView(center.x,center.y,20000);
            }
        }

        private static bool setRect(DbRect gotoRect, bool first, CompUnit u)
        {
            if (first)
            {
                gotoRect.x1 = u.ltlongitude;
                gotoRect.x2 = u.brlongitude;
                gotoRect.y1 = u.brlatitude;
                gotoRect.y2 = u.ltlatitude;
                first = false;
            }
            else
            {
                if (gotoRect.x1 > u.ltlongitude)
                {
                    gotoRect.x1 = u.ltlongitude;
                }
                if (gotoRect.x2 < u.brlongitude)
                {
                    gotoRect.x2 = u.brlongitude;
                }
                if (gotoRect.y1 > u.brlatitude)
                {
                    gotoRect.y1 = u.brlatitude;
                }
                if (gotoRect.y2 < u.ltlatitude)
                {
                    gotoRect.y2 = u.ltlatitude;
                }
            }

            return first;
        }

        public void clearList()
        {
            listView.Items.Clear();
            listViewBetter.Items.Clear();
            listViewWorse.Items.Clear();
        }

        private void listViewBetter_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            if (listViewBetter.SelectedItems.Count > 0)
            {
                List<CompUnit> compList = listViewBetter.SelectedItems[0].Tag as List<CompUnit>;
                if (compList != null)
                {
                    fireGotoSelectionRegion(compList);
                }
            }
        }

        private void listViewWorse_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            if (listViewWorse.SelectedItems.Count > 0)
            {
                List<CompUnit> compList = listViewWorse.SelectedItems[0].Tag as List<CompUnit>;
                if (compList != null)
                {
                    fireGotoSelectionRegion(compList);
                }
            }
        }

        private void toolStripMenuItem1_Click(object sender, EventArgs e)
        {
            try
            {
                MasterCom.Util.ExcelNPOIManager.ExportToExcel(this.listViewBetter);
            }
            catch (Exception ex)
            {
                MessageBox.Show("导出Excel失败!" + ex.Message);
            }
        }

        private void toolStripMenuItem2_Click(object sender, EventArgs e)
        {
            try
            {
                MasterCom.Util.ExcelNPOIManager.ExportToExcel(this.listViewWorse);
            }
            catch (Exception ex)
            {
                MessageBox.Show("导出Excel失败!" + ex.Message);
            }
        }

        private void btnReCombine_Click(object sender, EventArgs e)
        {
            IDNamePair windsz = cbxWindowSize.SelectedItem as IDNamePair;
            IDNamePair areasz = cbxAreaSize.SelectedItem as IDNamePair;
            if(windsz!=null && areasz!=null)
            {
                this.hisLayer.NeedFreshFullImg = true;
                this.hisLayer.SetAutoCombinParam(windsz.id, areasz.id);
                this.hisLayer.doFireApplyAction();
            }
            
        }

        private void cbxVsType_SelectedIndexChanged(object sender, EventArgs e)
        {
            cbxService.Items.Clear();

            if(cbxCarrier.Text == "移动VS联通") 
            {
                cbxService.Items.Add("GSM VS GSM");
                cbxService.Items.Add("TDSCDMA VS WCDMA");
                cbxService.Items.Add("GSM VS WCDMA");
            }
            else if(cbxCarrier.Text == "移动VS电信")
            {
                cbxService.Items.Add("GSM VS CDMA");
            }
            else if (cbxCarrier.Text == "联通VS移动")
            {
                cbxService.Items.Add("GSM VS GSM");
                cbxService.Items.Add("WCDMA VS TDSCDMA");
            }
            else if (cbxCarrier.Text == "联通VS电信")
            {
                cbxService.Items.Add("GSM VS CDMA");
            }
            else if (cbxCarrier.Text == "电信VS移动")
            {
                cbxService.Items.Add("CDMA VS GSM");
            }
            else if (cbxCarrier.Text == "电信VS联通")
            {
                cbxService.Items.Add("CDMA VS GSM");
            }
            cbxService.SelectedIndex = 0;
        }

        private void cbxService_SelectedIndexChanged(object sender, EventArgs e)
        {
            cbxKpi.Items.Clear();

            if ((cbxService.Text == "GSM VS GSM") || (cbxService.Text == "GSM VS CDMA") || (cbxService.Text == "CDMA VS GSM") || (cbxService.Text == "GSM VS WCDMA"))
            {
                cbxKpi.Items.Add("信号强度");
                cbxKpi.Items.Add("信号质量");
                cbxKpi.Items.Add("PESQ");
            }
            else if ((cbxService.Text == "TDSCDMA VS WCDMA") || (cbxService.Text == "WCDMA VS TDSCDMA"))
            {
                cbxKpi.Items.Add("信号强度及干扰");
                cbxKpi.Items.Add("BLER");
                cbxKpi.Items.Add("PESQ");
            }
            cbxKpi.SelectedIndex = 0;
        }

        private void cbxKpi_SelectedIndexChanged(object sender, EventArgs e)
        {
            //
        }

        private void btnFreshVSType_Click(object sender, EventArgs e)
        {
            string str = cbxCarrier.Text;
            str = str.Replace("移动","Y");
            str = str.Replace("联通","L");
            str = str.Replace("电信", "D");
            str = str.Replace("VS","2");
            str = str.Trim();
            this.hisLayer.CarrierPair = str;

            str = cbxService.Text;
            str = str.Replace("TDSCDMA","T"); //先处理TD和W，避免CDMA识别错误
            str = str.Replace("WCDMA", "W");
            str = str.Replace("GSM", "G");
            str = str.Replace("CDMA", "C");
            str = str.Replace(" VS ", "2");
            str = str.Trim();
            this.hisLayer.ServPair = str;

            this.hisLayer.KpiPos = cbxKpi.SelectedIndex;

            if (rdTime.Checked)
            {
                this.hisLayer.ByMethod = 2;
            }
            else
            {
                this.hisLayer.ByMethod = 1;
            }

            this.hisLayer.NeedFreshFullImg = true;
            this.hisLayer.doFireApplyAction();
        }

        private void btnColorMng_Click(object sender, EventArgs e)
        {
            CPColorMngDlg dlg = new CPColorMngDlg();
            dlg.FillColorModeList(hisLayer.ColorModes,hisLayer.ByMethod);
            if (dlg.ShowDialog()==DialogResult.OK)
            {
                btnFreshVSType_Click(sender, e);
            } 
        }

    }
   
}