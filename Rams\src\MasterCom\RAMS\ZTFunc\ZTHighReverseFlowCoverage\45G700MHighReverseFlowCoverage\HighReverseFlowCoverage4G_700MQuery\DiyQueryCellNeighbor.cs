﻿using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTHighReverseFlowCoverage._45G700MHighReverseFlowCoverage
{
    public class DiyQueryCellNeighbor : DIYSQLBase
    {
        public Dictionary<long, List<CellNeighbor>> CellInfosDic { get; protected set; }

        protected string tableName { get; } = "tb_high_reverse_flow_coverage_700m_neighbor";

        public int TotalCount { get; protected set; }
        public int ErrTotalCount { get; protected set; }

        protected Dictionary<string, long> nrCgi_Nci = new Dictionary<string, long>();
        protected Dictionary<string, long> lteCgi_Eci = new Dictionary<string, long>();

        public DiyQueryCellNeighbor()
            : base()
        {
            MainDB = true;
        }

        protected override string getSqlTextString()
        {
            string sql = $"SELECT [nrcgi],[ltecgi] " +
                $"FROM {tableName}";
            return sql;
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            int idx = 0;
            E_VType[] rType = new E_VType[2];
            rType[idx++] = E_VType.E_String;
            rType[idx] = E_VType.E_String;
            return rType;
        }

        protected override bool isValidCondition()
        {
            CellInfosDic = new Dictionary<long, List<CellNeighbor>>();
            return true;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    dealReceiveData(package);
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                setProgressPercent(ref index, ref progress);
            }
        }

        protected override void setProgressPercent(ref int index, ref int progress)
        {
            //每10w条数据进度涨10%>90则从头开始
            index++;
            if (index > 100000)
            {
                index = 0;
                progress += 10;
                if (progress >= 95)
                {
                    progress = 5;
                }
                WaitBox.ProgressPercent = progress;
            }
        }

        protected virtual void dealReceiveData(Package package)
        {
            CellNeighbor info = new CellNeighbor();
            info.FillDataBySQL(package, nrCgi_Nci, lteCgi_Eci);
            if (info.Eci == 0 || info.Nci == 0)
            {
                ErrTotalCount++;
                return;
            }

            if (!CellInfosDic.TryGetValue(info.Eci, out var infoList))
            {
                infoList = new List<CellNeighbor>();
                CellInfosDic.Add(info.Eci, infoList);
            }
            infoList.Add(info);
            TotalCount++;
        }
    }
}
