<?xml version="1.0"?>
<Configs>
  <Config name="ReportSetting">
    <Item name="styles" typeName="IList">
      <Item typeName="IDictionary">
        <Item typeName="String" key="Name">小区区域统计报表</Item>
        <Item typeName="IList" key="Cells">
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">统计项目(RxQual sub)</Item>
            <Item typeName="Int32" key="RowAt">1</Item>
            <Item typeName="Int32" key="ColAt">0</Item>
            <Item typeName="Int32" key="BkColorR">128</Item>
            <Item typeName="Int32" key="BkColorG">128</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item key="Exp" />
            <Item typeName="Int32" key="RowAt">1</Item>
            <Item typeName="Int32" key="ColAt">1</Item>
            <Item typeName="Int32" key="BkColorR">128</Item>
            <Item typeName="Int32" key="BkColorG">128</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item key="Exp" />
            <Item typeName="Int32" key="RowAt">2</Item>
            <Item typeName="Int32" key="ColAt">0</Item>
            <Item typeName="Int32" key="BkColorR">128</Item>
            <Item typeName="Int32" key="BkColorG">128</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">MS1采样点</Item>
            <Item typeName="Int32" key="RowAt">2</Item>
            <Item typeName="Int32" key="ColAt">1</Item>
            <Item typeName="Int32" key="BkColorR">128</Item>
            <Item typeName="Int32" key="BkColorG">128</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">Rxqual=1</Item>
            <Item typeName="Int32" key="RowAt">4</Item>
            <Item typeName="Int32" key="ColAt">0</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(东区MS1){vdRxQual[1] }</Item>
            <Item typeName="Int32" key="RowAt">4</Item>
            <Item typeName="Int32" key="ColAt">1</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">Rxqual=0 </Item>
            <Item typeName="Int32" key="RowAt">3</Item>
            <Item typeName="Int32" key="ColAt">0</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">百分比</Item>
            <Item typeName="Int32" key="RowAt">2</Item>
            <Item typeName="Int32" key="ColAt">2</Item>
            <Item typeName="Int32" key="BkColorR">128</Item>
            <Item typeName="Int32" key="BkColorG">128</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">百分比</Item>
            <Item typeName="Int32" key="RowAt">2</Item>
            <Item typeName="Int32" key="ColAt">4</Item>
            <Item typeName="Int32" key="BkColorR">128</Item>
            <Item typeName="Int32" key="BkColorG">128</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">百分比</Item>
            <Item typeName="Int32" key="RowAt">2</Item>
            <Item typeName="Int32" key="ColAt">6</Item>
            <Item typeName="Int32" key="BkColorR">128</Item>
            <Item typeName="Int32" key="BkColorG">128</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">百分比</Item>
            <Item typeName="Int32" key="RowAt">2</Item>
            <Item typeName="Int32" key="ColAt">8</Item>
            <Item typeName="Int32" key="BkColorR">128</Item>
            <Item typeName="Int32" key="BkColorG">128</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">MS1采样点</Item>
            <Item typeName="Int32" key="RowAt">2</Item>
            <Item typeName="Int32" key="ColAt">5</Item>
            <Item typeName="Int32" key="BkColorR">128</Item>
            <Item typeName="Int32" key="BkColorG">128</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">MS1采样点</Item>
            <Item typeName="Int32" key="RowAt">2</Item>
            <Item typeName="Int32" key="ColAt">9</Item>
            <Item typeName="Int32" key="BkColorR">128</Item>
            <Item typeName="Int32" key="BkColorG">128</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">百分比</Item>
            <Item typeName="Int32" key="RowAt">2</Item>
            <Item typeName="Int32" key="ColAt">12</Item>
            <Item typeName="Int32" key="BkColorR">128</Item>
            <Item typeName="Int32" key="BkColorG">128</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">MS2采样点</Item>
            <Item typeName="Int32" key="RowAt">2</Item>
            <Item typeName="Int32" key="ColAt">3</Item>
            <Item typeName="Int32" key="BkColorR">128</Item>
            <Item typeName="Int32" key="BkColorG">128</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">MS2采样点</Item>
            <Item typeName="Int32" key="RowAt">2</Item>
            <Item typeName="Int32" key="ColAt">7</Item>
            <Item typeName="Int32" key="BkColorR">128</Item>
            <Item typeName="Int32" key="BkColorG">128</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">百分比</Item>
            <Item typeName="Int32" key="RowAt">2</Item>
            <Item typeName="Int32" key="ColAt">10</Item>
            <Item typeName="Int32" key="BkColorR">128</Item>
            <Item typeName="Int32" key="BkColorG">128</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">百分比</Item>
            <Item typeName="Int32" key="RowAt">2</Item>
            <Item typeName="Int32" key="ColAt">16</Item>
            <Item typeName="Int32" key="BkColorR">128</Item>
            <Item typeName="Int32" key="BkColorG">128</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">东区</Item>
            <Item typeName="Int32" key="RowAt">1</Item>
            <Item typeName="Int32" key="ColAt">3</Item>
            <Item typeName="Int32" key="BkColorR">128</Item>
            <Item typeName="Int32" key="BkColorG">128</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item key="Exp" />
            <Item typeName="Int32" key="RowAt">1</Item>
            <Item typeName="Int32" key="ColAt">2</Item>
            <Item typeName="Int32" key="BkColorR">128</Item>
            <Item typeName="Int32" key="BkColorG">128</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">南区</Item>
            <Item typeName="Int32" key="RowAt">1</Item>
            <Item typeName="Int32" key="ColAt">7</Item>
            <Item typeName="Int32" key="BkColorR">128</Item>
            <Item typeName="Int32" key="BkColorG">128</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">MS2采样点</Item>
            <Item typeName="Int32" key="RowAt">2</Item>
            <Item typeName="Int32" key="ColAt">11</Item>
            <Item typeName="Int32" key="BkColorR">128</Item>
            <Item typeName="Int32" key="BkColorG">128</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">西区</Item>
            <Item typeName="Int32" key="RowAt">1</Item>
            <Item typeName="Int32" key="ColAt">11</Item>
            <Item typeName="Int32" key="BkColorR">128</Item>
            <Item typeName="Int32" key="BkColorG">128</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">MS1采样点</Item>
            <Item typeName="Int32" key="RowAt">2</Item>
            <Item typeName="Int32" key="ColAt">13</Item>
            <Item typeName="Int32" key="BkColorR">128</Item>
            <Item typeName="Int32" key="BkColorG">128</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">百分比</Item>
            <Item typeName="Int32" key="RowAt">2</Item>
            <Item typeName="Int32" key="ColAt">14</Item>
            <Item typeName="Int32" key="BkColorR">128</Item>
            <Item typeName="Int32" key="BkColorG">128</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">MS2采样点</Item>
            <Item typeName="Int32" key="RowAt">2</Item>
            <Item typeName="Int32" key="ColAt">15</Item>
            <Item typeName="Int32" key="BkColorR">128</Item>
            <Item typeName="Int32" key="BkColorG">128</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">北区</Item>
            <Item typeName="Int32" key="RowAt">1</Item>
            <Item typeName="Int32" key="ColAt">15</Item>
            <Item typeName="Int32" key="BkColorR">128</Item>
            <Item typeName="Int32" key="BkColorG">128</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">Rxqual=2</Item>
            <Item typeName="Int32" key="RowAt">5</Item>
            <Item typeName="Int32" key="ColAt">0</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">Rxqual=3</Item>
            <Item typeName="Int32" key="RowAt">6</Item>
            <Item typeName="Int32" key="ColAt">0</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">Rxqual=4</Item>
            <Item typeName="Int32" key="RowAt">7</Item>
            <Item typeName="Int32" key="ColAt">0</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">Rxqual=5</Item>
            <Item typeName="Int32" key="RowAt">8</Item>
            <Item typeName="Int32" key="ColAt">0</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">Rxqual=6</Item>
            <Item typeName="Int32" key="RowAt">9</Item>
            <Item typeName="Int32" key="ColAt">0</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">Rxqual=7</Item>
            <Item typeName="Int32" key="RowAt">10</Item>
            <Item typeName="Int32" key="ColAt">0</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item key="Exp" />
            <Item typeName="Int32" key="RowAt">11</Item>
            <Item typeName="Int32" key="ColAt">0</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">话音质量</Item>
            <Item typeName="Int32" key="RowAt">12</Item>
            <Item typeName="Int32" key="ColAt">0</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item key="Exp" />
            <Item typeName="Int32" key="RowAt">13</Item>
            <Item typeName="Int32" key="ColAt">0</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">统计项目(Rxlev sub)</Item>
            <Item typeName="Int32" key="RowAt">14</Item>
            <Item typeName="Int32" key="ColAt">0</Item>
            <Item typeName="Int32" key="BkColorR">128</Item>
            <Item typeName="Int32" key="BkColorG">128</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">东区</Item>
            <Item typeName="Int32" key="RowAt">14</Item>
            <Item typeName="Int32" key="ColAt">3</Item>
            <Item typeName="Int32" key="BkColorR">128</Item>
            <Item typeName="Int32" key="BkColorG">128</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">南区</Item>
            <Item typeName="Int32" key="RowAt">14</Item>
            <Item typeName="Int32" key="ColAt">7</Item>
            <Item typeName="Int32" key="BkColorR">128</Item>
            <Item typeName="Int32" key="BkColorG">128</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">西区</Item>
            <Item typeName="Int32" key="RowAt">14</Item>
            <Item typeName="Int32" key="ColAt">11</Item>
            <Item typeName="Int32" key="BkColorR">128</Item>
            <Item typeName="Int32" key="BkColorG">128</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">北区</Item>
            <Item typeName="Int32" key="RowAt">14</Item>
            <Item typeName="Int32" key="ColAt">15</Item>
            <Item typeName="Int32" key="BkColorR">128</Item>
            <Item typeName="Int32" key="BkColorG">128</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">MS1采样点</Item>
            <Item typeName="Int32" key="RowAt">15</Item>
            <Item typeName="Int32" key="ColAt">1</Item>
            <Item typeName="Int32" key="BkColorR">128</Item>
            <Item typeName="Int32" key="BkColorG">128</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">百分比</Item>
            <Item typeName="Int32" key="RowAt">15</Item>
            <Item typeName="Int32" key="ColAt">2</Item>
            <Item typeName="Int32" key="BkColorR">128</Item>
            <Item typeName="Int32" key="BkColorG">128</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">MS2采样点</Item>
            <Item typeName="Int32" key="RowAt">15</Item>
            <Item typeName="Int32" key="ColAt">3</Item>
            <Item typeName="Int32" key="BkColorR">128</Item>
            <Item typeName="Int32" key="BkColorG">128</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">百分比</Item>
            <Item typeName="Int32" key="RowAt">15</Item>
            <Item typeName="Int32" key="ColAt">4</Item>
            <Item typeName="Int32" key="BkColorR">128</Item>
            <Item typeName="Int32" key="BkColorG">128</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">MS1采样点</Item>
            <Item typeName="Int32" key="RowAt">15</Item>
            <Item typeName="Int32" key="ColAt">5</Item>
            <Item typeName="Int32" key="BkColorR">128</Item>
            <Item typeName="Int32" key="BkColorG">128</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">百分比</Item>
            <Item typeName="Int32" key="RowAt">15</Item>
            <Item typeName="Int32" key="ColAt">6</Item>
            <Item typeName="Int32" key="BkColorR">128</Item>
            <Item typeName="Int32" key="BkColorG">128</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">MS2采样点</Item>
            <Item typeName="Int32" key="RowAt">15</Item>
            <Item typeName="Int32" key="ColAt">7</Item>
            <Item typeName="Int32" key="BkColorR">128</Item>
            <Item typeName="Int32" key="BkColorG">128</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">百分比</Item>
            <Item typeName="Int32" key="RowAt">15</Item>
            <Item typeName="Int32" key="ColAt">8</Item>
            <Item typeName="Int32" key="BkColorR">128</Item>
            <Item typeName="Int32" key="BkColorG">128</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">MS1采样点</Item>
            <Item typeName="Int32" key="RowAt">15</Item>
            <Item typeName="Int32" key="ColAt">9</Item>
            <Item typeName="Int32" key="BkColorR">128</Item>
            <Item typeName="Int32" key="BkColorG">128</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">百分比</Item>
            <Item typeName="Int32" key="RowAt">15</Item>
            <Item typeName="Int32" key="ColAt">10</Item>
            <Item typeName="Int32" key="BkColorR">128</Item>
            <Item typeName="Int32" key="BkColorG">128</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">MS2采样点</Item>
            <Item typeName="Int32" key="RowAt">15</Item>
            <Item typeName="Int32" key="ColAt">11</Item>
            <Item typeName="Int32" key="BkColorR">128</Item>
            <Item typeName="Int32" key="BkColorG">128</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">百分比</Item>
            <Item typeName="Int32" key="RowAt">15</Item>
            <Item typeName="Int32" key="ColAt">12</Item>
            <Item typeName="Int32" key="BkColorR">128</Item>
            <Item typeName="Int32" key="BkColorG">128</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">MS1采样点</Item>
            <Item typeName="Int32" key="RowAt">15</Item>
            <Item typeName="Int32" key="ColAt">13</Item>
            <Item typeName="Int32" key="BkColorR">128</Item>
            <Item typeName="Int32" key="BkColorG">128</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">百分比</Item>
            <Item typeName="Int32" key="RowAt">15</Item>
            <Item typeName="Int32" key="ColAt">14</Item>
            <Item typeName="Int32" key="BkColorR">128</Item>
            <Item typeName="Int32" key="BkColorG">128</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">MS2采样点</Item>
            <Item typeName="Int32" key="RowAt">15</Item>
            <Item typeName="Int32" key="ColAt">15</Item>
            <Item typeName="Int32" key="BkColorR">128</Item>
            <Item typeName="Int32" key="BkColorG">128</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">百分比</Item>
            <Item typeName="Int32" key="RowAt">15</Item>
            <Item typeName="Int32" key="ColAt">16</Item>
            <Item typeName="Int32" key="BkColorR">128</Item>
            <Item typeName="Int32" key="BkColorG">128</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">-110≤Rxlev&lt;-94</Item>
            <Item typeName="Int32" key="RowAt">16</Item>
            <Item typeName="Int32" key="ColAt">0</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">-94≤Rxlev&lt;-90</Item>
            <Item typeName="Int32" key="RowAt">17</Item>
            <Item typeName="Int32" key="ColAt">0</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">-90≤Rxlev&lt;-85</Item>
            <Item typeName="Int32" key="RowAt">18</Item>
            <Item typeName="Int32" key="ColAt">0</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">-85≤Rxlev&lt;-80</Item>
            <Item typeName="Int32" key="RowAt">19</Item>
            <Item typeName="Int32" key="ColAt">0</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">-80≤Rxlev&lt;-70</Item>
            <Item typeName="Int32" key="RowAt">20</Item>
            <Item typeName="Int32" key="ColAt">0</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">-70≤Rxlev&lt;0</Item>
            <Item typeName="Int32" key="RowAt">21</Item>
            <Item typeName="Int32" key="ColAt">0</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">采样点总数</Item>
            <Item typeName="Int32" key="RowAt">22</Item>
            <Item typeName="Int32" key="ColAt">0</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">覆盖率</Item>
            <Item typeName="Int32" key="RowAt">23</Item>
            <Item typeName="Int32" key="ColAt">0</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item key="Exp" />
            <Item typeName="Int32" key="RowAt">24</Item>
            <Item typeName="Int32" key="ColAt">0</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item key="Exp" />
            <Item typeName="Int32" key="RowAt">25</Item>
            <Item typeName="Int32" key="ColAt">0</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">片区</Item>
            <Item typeName="Int32" key="RowAt">26</Item>
            <Item typeName="Int32" key="ColAt">0</Item>
            <Item typeName="Int32" key="BkColorR">128</Item>
            <Item typeName="Int32" key="BkColorG">128</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">东区</Item>
            <Item typeName="Int32" key="RowAt">27</Item>
            <Item typeName="Int32" key="ColAt">0</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">南区</Item>
            <Item typeName="Int32" key="RowAt">28</Item>
            <Item typeName="Int32" key="ColAt">0</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">西区</Item>
            <Item typeName="Int32" key="RowAt">29</Item>
            <Item typeName="Int32" key="ColAt">0</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">北区</Item>
            <Item typeName="Int32" key="RowAt">30</Item>
            <Item typeName="Int32" key="ColAt">0</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">采样点总数</Item>
            <Item typeName="Int32" key="RowAt">31</Item>
            <Item typeName="Int32" key="ColAt">0</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">采样点</Item>
            <Item typeName="Int32" key="RowAt">26</Item>
            <Item typeName="Int32" key="ColAt">1</Item>
            <Item typeName="Int32" key="BkColorR">128</Item>
            <Item typeName="Int32" key="BkColorG">128</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">占比</Item>
            <Item typeName="Int32" key="RowAt">26</Item>
            <Item typeName="Int32" key="ColAt">2</Item>
            <Item typeName="Int32" key="BkColorR">128</Item>
            <Item typeName="Int32" key="BkColorG">128</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item key="Exp" />
            <Item typeName="Int32" key="RowAt">26</Item>
            <Item typeName="Int32" key="ColAt">3</Item>
            <Item typeName="Int32" key="BkColorR">128</Item>
            <Item typeName="Int32" key="BkColorG">128</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">正常拨打</Item>
            <Item typeName="Int32" key="RowAt">26</Item>
            <Item typeName="Int32" key="ColAt">4</Item>
            <Item typeName="Int32" key="BkColorR">128</Item>
            <Item typeName="Int32" key="BkColorG">128</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">未接通</Item>
            <Item typeName="Int32" key="RowAt">26</Item>
            <Item typeName="Int32" key="ColAt">5</Item>
            <Item typeName="Int32" key="BkColorR">128</Item>
            <Item typeName="Int32" key="BkColorG">128</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">掉话</Item>
            <Item typeName="Int32" key="RowAt">26</Item>
            <Item typeName="Int32" key="ColAt">6</Item>
            <Item typeName="Int32" key="BkColorR">128</Item>
            <Item typeName="Int32" key="BkColorG">128</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">拨打次数</Item>
            <Item typeName="Int32" key="RowAt">26</Item>
            <Item typeName="Int32" key="ColAt">7</Item>
            <Item typeName="Int32" key="BkColorR">128</Item>
            <Item typeName="Int32" key="BkColorG">128</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item key="Exp" />
            <Item typeName="Int32" key="RowAt">1</Item>
            <Item typeName="Int32" key="ColAt">4</Item>
            <Item typeName="Int32" key="BkColorR">128</Item>
            <Item typeName="Int32" key="BkColorG">128</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item key="Exp" />
            <Item typeName="Int32" key="RowAt">1</Item>
            <Item typeName="Int32" key="ColAt">5</Item>
            <Item typeName="Int32" key="BkColorR">128</Item>
            <Item typeName="Int32" key="BkColorG">128</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item key="Exp" />
            <Item typeName="Int32" key="RowAt">1</Item>
            <Item typeName="Int32" key="ColAt">6</Item>
            <Item typeName="Int32" key="BkColorR">128</Item>
            <Item typeName="Int32" key="BkColorG">128</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item key="Exp" />
            <Item typeName="Int32" key="RowAt">1</Item>
            <Item typeName="Int32" key="ColAt">8</Item>
            <Item typeName="Int32" key="BkColorR">128</Item>
            <Item typeName="Int32" key="BkColorG">128</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item key="Exp" />
            <Item typeName="Int32" key="RowAt">1</Item>
            <Item typeName="Int32" key="ColAt">9</Item>
            <Item typeName="Int32" key="BkColorR">128</Item>
            <Item typeName="Int32" key="BkColorG">128</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item key="Exp" />
            <Item typeName="Int32" key="RowAt">1</Item>
            <Item typeName="Int32" key="ColAt">10</Item>
            <Item typeName="Int32" key="BkColorR">128</Item>
            <Item typeName="Int32" key="BkColorG">128</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item key="Exp" />
            <Item typeName="Int32" key="RowAt">1</Item>
            <Item typeName="Int32" key="ColAt">12</Item>
            <Item typeName="Int32" key="BkColorR">128</Item>
            <Item typeName="Int32" key="BkColorG">128</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item key="Exp" />
            <Item typeName="Int32" key="RowAt">1</Item>
            <Item typeName="Int32" key="ColAt">13</Item>
            <Item typeName="Int32" key="BkColorR">128</Item>
            <Item typeName="Int32" key="BkColorG">128</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item key="Exp" />
            <Item typeName="Int32" key="RowAt">1</Item>
            <Item typeName="Int32" key="ColAt">14</Item>
            <Item typeName="Int32" key="BkColorR">128</Item>
            <Item typeName="Int32" key="BkColorG">128</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item key="Exp" />
            <Item typeName="Int32" key="RowAt">1</Item>
            <Item typeName="Int32" key="ColAt">16</Item>
            <Item typeName="Int32" key="BkColorR">128</Item>
            <Item typeName="Int32" key="BkColorG">128</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item key="Exp" />
            <Item typeName="Int32" key="RowAt">15</Item>
            <Item typeName="Int32" key="ColAt">0</Item>
            <Item typeName="Int32" key="BkColorR">128</Item>
            <Item typeName="Int32" key="BkColorG">128</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item key="Exp" />
            <Item typeName="Int32" key="RowAt">14</Item>
            <Item typeName="Int32" key="ColAt">1</Item>
            <Item typeName="Int32" key="BkColorR">128</Item>
            <Item typeName="Int32" key="BkColorG">128</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item key="Exp" />
            <Item typeName="Int32" key="RowAt">14</Item>
            <Item typeName="Int32" key="ColAt">2</Item>
            <Item typeName="Int32" key="BkColorR">128</Item>
            <Item typeName="Int32" key="BkColorG">128</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item key="Exp" />
            <Item typeName="Int32" key="RowAt">14</Item>
            <Item typeName="Int32" key="ColAt">4</Item>
            <Item typeName="Int32" key="BkColorR">128</Item>
            <Item typeName="Int32" key="BkColorG">128</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item key="Exp" />
            <Item typeName="Int32" key="RowAt">14</Item>
            <Item typeName="Int32" key="ColAt">5</Item>
            <Item typeName="Int32" key="BkColorR">128</Item>
            <Item typeName="Int32" key="BkColorG">128</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item key="Exp" />
            <Item typeName="Int32" key="RowAt">14</Item>
            <Item typeName="Int32" key="ColAt">6</Item>
            <Item typeName="Int32" key="BkColorR">128</Item>
            <Item typeName="Int32" key="BkColorG">128</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item key="Exp" />
            <Item typeName="Int32" key="RowAt">14</Item>
            <Item typeName="Int32" key="ColAt">8</Item>
            <Item typeName="Int32" key="BkColorR">128</Item>
            <Item typeName="Int32" key="BkColorG">128</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item key="Exp" />
            <Item typeName="Int32" key="RowAt">14</Item>
            <Item typeName="Int32" key="ColAt">9</Item>
            <Item typeName="Int32" key="BkColorR">128</Item>
            <Item typeName="Int32" key="BkColorG">128</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item key="Exp" />
            <Item typeName="Int32" key="RowAt">14</Item>
            <Item typeName="Int32" key="ColAt">10</Item>
            <Item typeName="Int32" key="BkColorR">128</Item>
            <Item typeName="Int32" key="BkColorG">128</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item key="Exp" />
            <Item typeName="Int32" key="RowAt">14</Item>
            <Item typeName="Int32" key="ColAt">12</Item>
            <Item typeName="Int32" key="BkColorR">128</Item>
            <Item typeName="Int32" key="BkColorG">128</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item key="Exp" />
            <Item typeName="Int32" key="RowAt">14</Item>
            <Item typeName="Int32" key="ColAt">13</Item>
            <Item typeName="Int32" key="BkColorR">128</Item>
            <Item typeName="Int32" key="BkColorG">128</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item key="Exp" />
            <Item typeName="Int32" key="RowAt">14</Item>
            <Item typeName="Int32" key="ColAt">14</Item>
            <Item typeName="Int32" key="BkColorR">128</Item>
            <Item typeName="Int32" key="BkColorG">128</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item key="Exp" />
            <Item typeName="Int32" key="RowAt">14</Item>
            <Item typeName="Int32" key="ColAt">16</Item>
            <Item typeName="Int32" key="BkColorR">128</Item>
            <Item typeName="Int32" key="BkColorG">128</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(东区MS1){vdRxQual[0] }</Item>
            <Item typeName="Int32" key="RowAt">3</Item>
            <Item typeName="Int32" key="ColAt">1</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(东区MS1){vdRxQual[2] }</Item>
            <Item typeName="Int32" key="RowAt">5</Item>
            <Item typeName="Int32" key="ColAt">1</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(东区MS1){vdRxQual[3]}</Item>
            <Item typeName="Int32" key="RowAt">6</Item>
            <Item typeName="Int32" key="ColAt">1</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(东区MS1){vdRxQual[4]}</Item>
            <Item typeName="Int32" key="RowAt">7</Item>
            <Item typeName="Int32" key="ColAt">1</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(东区MS1){vdRxQual[5]}</Item>
            <Item typeName="Int32" key="RowAt">8</Item>
            <Item typeName="Int32" key="ColAt">1</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(东区MS1){vdRxQual[6]}</Item>
            <Item typeName="Int32" key="RowAt">9</Item>
            <Item typeName="Int32" key="ColAt">1</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(东区MS1){vdRxQual[7]}</Item>
            <Item typeName="Int32" key="RowAt">10</Item>
            <Item typeName="Int32" key="ColAt">1</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(东区MS1){100*vdRxQual[0]/(vdRxQual[0]+vdRxQual[1]+vdRxQual[2]+vdRxQual[3]+vdRxQual[4]+vdRxQual[5]+vdRxQual[6]+vdRxQual[7]) }%</Item>
            <Item typeName="Int32" key="RowAt">3</Item>
            <Item typeName="Int32" key="ColAt">2</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(东区MS1){100*vdRxQual[1]/(vdRxQual[0]+vdRxQual[1]+vdRxQual[2]+vdRxQual[3]+vdRxQual[4]+vdRxQual[5]+vdRxQual[6]+vdRxQual[7]) }%</Item>
            <Item typeName="Int32" key="RowAt">4</Item>
            <Item typeName="Int32" key="ColAt">2</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(东区MS1){100*vdRxQual[2]/(vdRxQual[0]+vdRxQual[1]+vdRxQual[2]+vdRxQual[3]+vdRxQual[4]+vdRxQual[5]+vdRxQual[6]+vdRxQual[7]) }%</Item>
            <Item typeName="Int32" key="RowAt">5</Item>
            <Item typeName="Int32" key="ColAt">2</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(东区MS1){100*vdRxQual[3]/(vdRxQual[0]+vdRxQual[1]+vdRxQual[2]+vdRxQual[3]+vdRxQual[4]+vdRxQual[5]+vdRxQual[6]+vdRxQual[7]) }%</Item>
            <Item typeName="Int32" key="RowAt">6</Item>
            <Item typeName="Int32" key="ColAt">2</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(东区MS1){100*vdRxQual[4]/(vdRxQual[0]+vdRxQual[1]+vdRxQual[2]+vdRxQual[3]+vdRxQual[4]+vdRxQual[5]+vdRxQual[6]+vdRxQual[7]) }%</Item>
            <Item typeName="Int32" key="RowAt">7</Item>
            <Item typeName="Int32" key="ColAt">2</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(东区MS1){100*vdRxQual[5]/(vdRxQual[0]+vdRxQual[1]+vdRxQual[2]+vdRxQual[3]+vdRxQual[4]+vdRxQual[5]+vdRxQual[6]+vdRxQual[7]) }%</Item>
            <Item typeName="Int32" key="RowAt">8</Item>
            <Item typeName="Int32" key="ColAt">2</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(东区MS1){100*vdRxQual[6]/(vdRxQual[0]+vdRxQual[1]+vdRxQual[2]+vdRxQual[3]+vdRxQual[4]+vdRxQual[5]+vdRxQual[6]+vdRxQual[7]) }%</Item>
            <Item typeName="Int32" key="RowAt">9</Item>
            <Item typeName="Int32" key="ColAt">2</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(东区MS1){100*vdRxQual[7]/(vdRxQual[0]+vdRxQual[1]+vdRxQual[2]+vdRxQual[3]+vdRxQual[4]+vdRxQual[5]+vdRxQual[6]+vdRxQual[7]) }%</Item>
            <Item typeName="Int32" key="RowAt">10</Item>
            <Item typeName="Int32" key="ColAt">2</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(南区MS1){100*vdRxQual[0]/(vdRxQual[0]+vdRxQual[1]+vdRxQual[2]+vdRxQual[3]+vdRxQual[4]+vdRxQual[5]+vdRxQual[6]+vdRxQual[7]) }%</Item>
            <Item typeName="Int32" key="RowAt">3</Item>
            <Item typeName="Int32" key="ColAt">6</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(南区MS1){100*vdRxQual[1]/(vdRxQual[0]+vdRxQual[1]+vdRxQual[2]+vdRxQual[3]+vdRxQual[4]+vdRxQual[5]+vdRxQual[6]+vdRxQual[7]) }%</Item>
            <Item typeName="Int32" key="RowAt">4</Item>
            <Item typeName="Int32" key="ColAt">6</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(南区MS1){100*vdRxQual[2]/(vdRxQual[0]+vdRxQual[1]+vdRxQual[2]+vdRxQual[3]+vdRxQual[4]+vdRxQual[5]+vdRxQual[6]+vdRxQual[7]) }%</Item>
            <Item typeName="Int32" key="RowAt">5</Item>
            <Item typeName="Int32" key="ColAt">6</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(南区MS1){100*vdRxQual[3]/(vdRxQual[0]+vdRxQual[1]+vdRxQual[2]+vdRxQual[3]+vdRxQual[4]+vdRxQual[5]+vdRxQual[6]+vdRxQual[7]) }%</Item>
            <Item typeName="Int32" key="RowAt">6</Item>
            <Item typeName="Int32" key="ColAt">6</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(南区MS1){100*vdRxQual[4]/(vdRxQual[0]+vdRxQual[1]+vdRxQual[2]+vdRxQual[3]+vdRxQual[4]+vdRxQual[5]+vdRxQual[6]+vdRxQual[7]) }%</Item>
            <Item typeName="Int32" key="RowAt">7</Item>
            <Item typeName="Int32" key="ColAt">6</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(南区MS1){100*vdRxQual[5]/(vdRxQual[0]+vdRxQual[1]+vdRxQual[2]+vdRxQual[3]+vdRxQual[4]+vdRxQual[5]+vdRxQual[6]+vdRxQual[7]) }%</Item>
            <Item typeName="Int32" key="RowAt">8</Item>
            <Item typeName="Int32" key="ColAt">6</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(南区MS1){100*vdRxQual[6]/(vdRxQual[0]+vdRxQual[1]+vdRxQual[2]+vdRxQual[3]+vdRxQual[4]+vdRxQual[5]+vdRxQual[6]+vdRxQual[7]) }%</Item>
            <Item typeName="Int32" key="RowAt">9</Item>
            <Item typeName="Int32" key="ColAt">6</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(南区MS1){100*vdRxQual[7]/(vdRxQual[0]+vdRxQual[1]+vdRxQual[2]+vdRxQual[3]+vdRxQual[4]+vdRxQual[5]+vdRxQual[6]+vdRxQual[7]) }%</Item>
            <Item typeName="Int32" key="RowAt">10</Item>
            <Item typeName="Int32" key="ColAt">6</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(西区MS1){100*vdRxQual[0]/(vdRxQual[0]+vdRxQual[1]+vdRxQual[2]+vdRxQual[3]+vdRxQual[4]+vdRxQual[5]+vdRxQual[6]+vdRxQual[7])}%</Item>
            <Item typeName="Int32" key="RowAt">3</Item>
            <Item typeName="Int32" key="ColAt">10</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(西区MS1){100*vdRxQual[1]/(vdRxQual[0]+vdRxQual[1]+vdRxQual[2]+vdRxQual[3]+vdRxQual[4]+vdRxQual[5]+vdRxQual[6]+vdRxQual[7])}%</Item>
            <Item typeName="Int32" key="RowAt">4</Item>
            <Item typeName="Int32" key="ColAt">10</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(西区MS1){100*vdRxQual[2]/(vdRxQual[0]+vdRxQual[1]+vdRxQual[2]+vdRxQual[3]+vdRxQual[4]+vdRxQual[5]+vdRxQual[6]+vdRxQual[7])}%</Item>
            <Item typeName="Int32" key="RowAt">5</Item>
            <Item typeName="Int32" key="ColAt">10</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(西区MS1){100*vdRxQual[3]/(vdRxQual[0]+vdRxQual[1]+vdRxQual[2]+vdRxQual[3]+vdRxQual[4]+vdRxQual[5]+vdRxQual[6]+vdRxQual[7])}%</Item>
            <Item typeName="Int32" key="RowAt">6</Item>
            <Item typeName="Int32" key="ColAt">10</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(西区MS1){100*vdRxQual[4]/(vdRxQual[0]+vdRxQual[1]+vdRxQual[2]+vdRxQual[3]+vdRxQual[4]+vdRxQual[5]+vdRxQual[6]+vdRxQual[7])}%</Item>
            <Item typeName="Int32" key="RowAt">7</Item>
            <Item typeName="Int32" key="ColAt">10</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(西区MS1){100*vdRxQual[5]/(vdRxQual[0]+vdRxQual[1]+vdRxQual[2]+vdRxQual[3]+vdRxQual[4]+vdRxQual[5]+vdRxQual[6]+vdRxQual[7])}%</Item>
            <Item typeName="Int32" key="RowAt">8</Item>
            <Item typeName="Int32" key="ColAt">10</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(西区MS1){100*vdRxQual[6]/(vdRxQual[0]+vdRxQual[1]+vdRxQual[2]+vdRxQual[3]+vdRxQual[4]+vdRxQual[5]+vdRxQual[6]+vdRxQual[7])}%</Item>
            <Item typeName="Int32" key="RowAt">9</Item>
            <Item typeName="Int32" key="ColAt">10</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(西区MS1){100*vdRxQual[7]/(vdRxQual[0]+vdRxQual[1]+vdRxQual[2]+vdRxQual[3]+vdRxQual[4]+vdRxQual[5]+vdRxQual[6]+vdRxQual[7])}%</Item>
            <Item typeName="Int32" key="RowAt">10</Item>
            <Item typeName="Int32" key="ColAt">10</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(北区MS1){100*vdRxQual[0]/(vdRxQual[0]+vdRxQual[1]+vdRxQual[2]+vdRxQual[3]+vdRxQual[4]+vdRxQual[5]+vdRxQual[6]+vdRxQual[7]) }%</Item>
            <Item typeName="Int32" key="RowAt">3</Item>
            <Item typeName="Int32" key="ColAt">14</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(北区MS1){100*vdRxQual[1]/(vdRxQual[0]+vdRxQual[1]+vdRxQual[2]+vdRxQual[3]+vdRxQual[4]+vdRxQual[5]+vdRxQual[6]+vdRxQual[7]) }%</Item>
            <Item typeName="Int32" key="RowAt">4</Item>
            <Item typeName="Int32" key="ColAt">14</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(北区MS1){100*vdRxQual[2]/(vdRxQual[0]+vdRxQual[1]+vdRxQual[2]+vdRxQual[3]+vdRxQual[4]+vdRxQual[5]+vdRxQual[6]+vdRxQual[7]) }%</Item>
            <Item typeName="Int32" key="RowAt">5</Item>
            <Item typeName="Int32" key="ColAt">14</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(北区MS1){100*vdRxQual[3]/(vdRxQual[0]+vdRxQual[1]+vdRxQual[2]+vdRxQual[3]+vdRxQual[4]+vdRxQual[5]+vdRxQual[6]+vdRxQual[7]) }%</Item>
            <Item typeName="Int32" key="RowAt">6</Item>
            <Item typeName="Int32" key="ColAt">14</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(北区MS1){100*vdRxQual[4]/(vdRxQual[0]+vdRxQual[1]+vdRxQual[2]+vdRxQual[3]+vdRxQual[4]+vdRxQual[5]+vdRxQual[6]+vdRxQual[7]) }%</Item>
            <Item typeName="Int32" key="RowAt">7</Item>
            <Item typeName="Int32" key="ColAt">14</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(北区MS1){100*vdRxQual[5]/(vdRxQual[0]+vdRxQual[1]+vdRxQual[2]+vdRxQual[3]+vdRxQual[4]+vdRxQual[5]+vdRxQual[6]+vdRxQual[7]) }%</Item>
            <Item typeName="Int32" key="RowAt">8</Item>
            <Item typeName="Int32" key="ColAt">14</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(北区MS1){100*vdRxQual[6]/(vdRxQual[0]+vdRxQual[1]+vdRxQual[2]+vdRxQual[3]+vdRxQual[4]+vdRxQual[5]+vdRxQual[6]+vdRxQual[7]) }%</Item>
            <Item typeName="Int32" key="RowAt">9</Item>
            <Item typeName="Int32" key="ColAt">14</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(北区MS1){100*vdRxQual[7]/(vdRxQual[0]+vdRxQual[1]+vdRxQual[2]+vdRxQual[3]+vdRxQual[4]+vdRxQual[5]+vdRxQual[6]+vdRxQual[7]) }%</Item>
            <Item typeName="Int32" key="RowAt">10</Item>
            <Item typeName="Int32" key="ColAt">14</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item key="Exp" />
            <Item typeName="Int32" key="RowAt">11</Item>
            <Item typeName="Int32" key="ColAt">14</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(东区MS2){vdRxQual[0] }</Item>
            <Item typeName="Int32" key="RowAt">3</Item>
            <Item typeName="Int32" key="ColAt">3</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(东区MS2){vdRxQual[1] }</Item>
            <Item typeName="Int32" key="RowAt">4</Item>
            <Item typeName="Int32" key="ColAt">3</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(东区MS2){vdRxQual[2] }</Item>
            <Item typeName="Int32" key="RowAt">5</Item>
            <Item typeName="Int32" key="ColAt">3</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(东区MS2){vdRxQual[3] }</Item>
            <Item typeName="Int32" key="RowAt">6</Item>
            <Item typeName="Int32" key="ColAt">3</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(东区MS2){vdRxQual[4] }</Item>
            <Item typeName="Int32" key="RowAt">7</Item>
            <Item typeName="Int32" key="ColAt">3</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(东区MS2){vdRxQual[5] }</Item>
            <Item typeName="Int32" key="RowAt">8</Item>
            <Item typeName="Int32" key="ColAt">3</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(东区MS2){vdRxQual[6] }</Item>
            <Item typeName="Int32" key="RowAt">9</Item>
            <Item typeName="Int32" key="ColAt">3</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(东区MS2){vdRxQual[7] }</Item>
            <Item typeName="Int32" key="RowAt">10</Item>
            <Item typeName="Int32" key="ColAt">3</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(南区MS2){vdRxQual[0] }</Item>
            <Item typeName="Int32" key="RowAt">3</Item>
            <Item typeName="Int32" key="ColAt">7</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(南区MS2){vdRxQual[1]}</Item>
            <Item typeName="Int32" key="RowAt">4</Item>
            <Item typeName="Int32" key="ColAt">7</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(南区MS2){vdRxQual[2]}</Item>
            <Item typeName="Int32" key="RowAt">5</Item>
            <Item typeName="Int32" key="ColAt">7</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(南区MS2){vdRxQual[3]}</Item>
            <Item typeName="Int32" key="RowAt">6</Item>
            <Item typeName="Int32" key="ColAt">7</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(南区MS2){vdRxQual[4]}</Item>
            <Item typeName="Int32" key="RowAt">7</Item>
            <Item typeName="Int32" key="ColAt">7</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(南区MS2){vdRxQual[5]}</Item>
            <Item typeName="Int32" key="RowAt">8</Item>
            <Item typeName="Int32" key="ColAt">7</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(南区MS2){vdRxQual[6]}</Item>
            <Item typeName="Int32" key="RowAt">9</Item>
            <Item typeName="Int32" key="ColAt">7</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(南区MS2){vdRxQual[7]}</Item>
            <Item typeName="Int32" key="RowAt">10</Item>
            <Item typeName="Int32" key="ColAt">7</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(西区MS2){vdRxQual[0]}</Item>
            <Item typeName="Int32" key="RowAt">3</Item>
            <Item typeName="Int32" key="ColAt">11</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(西区MS2){vdRxQual[1]}</Item>
            <Item typeName="Int32" key="RowAt">4</Item>
            <Item typeName="Int32" key="ColAt">11</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(西区MS2){vdRxQual[2]}</Item>
            <Item typeName="Int32" key="RowAt">5</Item>
            <Item typeName="Int32" key="ColAt">11</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(西区MS2){vdRxQual[3]}</Item>
            <Item typeName="Int32" key="RowAt">6</Item>
            <Item typeName="Int32" key="ColAt">11</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(西区MS2){vdRxQual[4]}</Item>
            <Item typeName="Int32" key="RowAt">7</Item>
            <Item typeName="Int32" key="ColAt">11</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(西区MS2){vdRxQual[5]}</Item>
            <Item typeName="Int32" key="RowAt">8</Item>
            <Item typeName="Int32" key="ColAt">11</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(西区MS2){vdRxQual[6]}</Item>
            <Item typeName="Int32" key="RowAt">9</Item>
            <Item typeName="Int32" key="ColAt">11</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(西区MS2){vdRxQual[7]}</Item>
            <Item typeName="Int32" key="RowAt">10</Item>
            <Item typeName="Int32" key="ColAt">11</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(北区MS2){vdRxQual[0]}</Item>
            <Item typeName="Int32" key="RowAt">3</Item>
            <Item typeName="Int32" key="ColAt">15</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(北区MS2){vdRxQual[1]}</Item>
            <Item typeName="Int32" key="RowAt">4</Item>
            <Item typeName="Int32" key="ColAt">15</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(北区MS2){vdRxQual[2]}</Item>
            <Item typeName="Int32" key="RowAt">5</Item>
            <Item typeName="Int32" key="ColAt">15</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(北区MS2){vdRxQual[3]}</Item>
            <Item typeName="Int32" key="RowAt">6</Item>
            <Item typeName="Int32" key="ColAt">15</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(北区MS2){vdRxQual[4]}</Item>
            <Item typeName="Int32" key="RowAt">7</Item>
            <Item typeName="Int32" key="ColAt">15</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(北区MS2){vdRxQual[5]}</Item>
            <Item typeName="Int32" key="RowAt">8</Item>
            <Item typeName="Int32" key="ColAt">15</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(北区MS2){vdRxQual[6]}</Item>
            <Item typeName="Int32" key="RowAt">9</Item>
            <Item typeName="Int32" key="ColAt">15</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(北区MS2){vdRxQual[7]}</Item>
            <Item typeName="Int32" key="RowAt">10</Item>
            <Item typeName="Int32" key="ColAt">15</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(东区MS2){100*vdRxQual[0]/(vdRxQual[0]+vdRxQual[1]+vdRxQual[2]+vdRxQual[3]+vdRxQual[4]+vdRxQual[5]+vdRxQual[6]+vdRxQual[7])}%</Item>
            <Item typeName="Int32" key="RowAt">3</Item>
            <Item typeName="Int32" key="ColAt">4</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(东区MS2){100*vdRxQual[1]/(vdRxQual[0]+vdRxQual[1]+vdRxQual[2]+vdRxQual[3]+vdRxQual[4]+vdRxQual[5]+vdRxQual[6]+vdRxQual[7])}%</Item>
            <Item typeName="Int32" key="RowAt">4</Item>
            <Item typeName="Int32" key="ColAt">4</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(东区MS2){100*vdRxQual[2]/(vdRxQual[0]+vdRxQual[1]+vdRxQual[2]+vdRxQual[3]+vdRxQual[4]+vdRxQual[5]+vdRxQual[6]+vdRxQual[7])}%</Item>
            <Item typeName="Int32" key="RowAt">5</Item>
            <Item typeName="Int32" key="ColAt">4</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(东区MS2){100*vdRxQual[3]/(vdRxQual[0]+vdRxQual[1]+vdRxQual[2]+vdRxQual[3]+vdRxQual[4]+vdRxQual[5]+vdRxQual[6]+vdRxQual[7])}%</Item>
            <Item typeName="Int32" key="RowAt">6</Item>
            <Item typeName="Int32" key="ColAt">4</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(东区MS2){100*vdRxQual[4]/(vdRxQual[0]+vdRxQual[1]+vdRxQual[2]+vdRxQual[3]+vdRxQual[4]+vdRxQual[5]+vdRxQual[6]+vdRxQual[7])}%</Item>
            <Item typeName="Int32" key="RowAt">7</Item>
            <Item typeName="Int32" key="ColAt">4</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(东区MS2){100*vdRxQual[5]/(vdRxQual[0]+vdRxQual[1]+vdRxQual[2]+vdRxQual[3]+vdRxQual[4]+vdRxQual[5]+vdRxQual[6]+vdRxQual[7])}%</Item>
            <Item typeName="Int32" key="RowAt">8</Item>
            <Item typeName="Int32" key="ColAt">4</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(东区MS2){100*vdRxQual[6]/(vdRxQual[0]+vdRxQual[1]+vdRxQual[2]+vdRxQual[3]+vdRxQual[4]+vdRxQual[5]+vdRxQual[6]+vdRxQual[7])}%</Item>
            <Item typeName="Int32" key="RowAt">9</Item>
            <Item typeName="Int32" key="ColAt">4</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(东区MS2){100*vdRxQual[7]/(vdRxQual[0]+vdRxQual[1]+vdRxQual[2]+vdRxQual[3]+vdRxQual[4]+vdRxQual[5]+vdRxQual[6]+vdRxQual[7])}%</Item>
            <Item typeName="Int32" key="RowAt">10</Item>
            <Item typeName="Int32" key="ColAt">4</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(南区MS2){100*vdRxQual[0]/(vdRxQual[0]+vdRxQual[1]+vdRxQual[2]+vdRxQual[3]+vdRxQual[4]+vdRxQual[5]+vdRxQual[6]+vdRxQual[7])}%</Item>
            <Item typeName="Int32" key="RowAt">3</Item>
            <Item typeName="Int32" key="ColAt">8</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(南区MS2){100*vdRxQual[1]/(vdRxQual[0]+vdRxQual[1]+vdRxQual[2]+vdRxQual[3]+vdRxQual[4]+vdRxQual[5]+vdRxQual[6]+vdRxQual[7])}%</Item>
            <Item typeName="Int32" key="RowAt">4</Item>
            <Item typeName="Int32" key="ColAt">8</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(南区MS2){100*vdRxQual[2]/(vdRxQual[0]+vdRxQual[1]+vdRxQual[2]+vdRxQual[3]+vdRxQual[4]+vdRxQual[5]+vdRxQual[6]+vdRxQual[7])}%</Item>
            <Item typeName="Int32" key="RowAt">5</Item>
            <Item typeName="Int32" key="ColAt">8</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(南区MS2){100*vdRxQual[3]/(vdRxQual[0]+vdRxQual[1]+vdRxQual[2]+vdRxQual[3]+vdRxQual[4]+vdRxQual[5]+vdRxQual[6]+vdRxQual[7])}%</Item>
            <Item typeName="Int32" key="RowAt">6</Item>
            <Item typeName="Int32" key="ColAt">8</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(南区MS2){100*vdRxQual[4]/(vdRxQual[0]+vdRxQual[1]+vdRxQual[2]+vdRxQual[3]+vdRxQual[4]+vdRxQual[5]+vdRxQual[6]+vdRxQual[7])}%</Item>
            <Item typeName="Int32" key="RowAt">7</Item>
            <Item typeName="Int32" key="ColAt">8</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(南区MS2){100*vdRxQual[5]/(vdRxQual[0]+vdRxQual[1]+vdRxQual[2]+vdRxQual[3]+vdRxQual[4]+vdRxQual[5]+vdRxQual[6]+vdRxQual[7])}%</Item>
            <Item typeName="Int32" key="RowAt">8</Item>
            <Item typeName="Int32" key="ColAt">8</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(南区MS2){100*vdRxQual[6]/(vdRxQual[0]+vdRxQual[1]+vdRxQual[2]+vdRxQual[3]+vdRxQual[4]+vdRxQual[5]+vdRxQual[6]+vdRxQual[7])}%</Item>
            <Item typeName="Int32" key="RowAt">9</Item>
            <Item typeName="Int32" key="ColAt">8</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(南区MS2){100*vdRxQual[7]/(vdRxQual[0]+vdRxQual[1]+vdRxQual[2]+vdRxQual[3]+vdRxQual[4]+vdRxQual[5]+vdRxQual[6]+vdRxQual[7])}%</Item>
            <Item typeName="Int32" key="RowAt">10</Item>
            <Item typeName="Int32" key="ColAt">8</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(西区MS2){100*vdRxQual[0]/(vdRxQual[0]+vdRxQual[1]+vdRxQual[2]+vdRxQual[3]+vdRxQual[4]+vdRxQual[5]+vdRxQual[6]+vdRxQual[7])}%</Item>
            <Item typeName="Int32" key="RowAt">3</Item>
            <Item typeName="Int32" key="ColAt">12</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(西区MS2){100*vdRxQual[1]/(vdRxQual[0]+vdRxQual[1]+vdRxQual[2]+vdRxQual[3]+vdRxQual[4]+vdRxQual[5]+vdRxQual[6]+vdRxQual[7])}%</Item>
            <Item typeName="Int32" key="RowAt">4</Item>
            <Item typeName="Int32" key="ColAt">12</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(西区MS2){100*vdRxQual[2]/(vdRxQual[0]+vdRxQual[1]+vdRxQual[2]+vdRxQual[3]+vdRxQual[4]+vdRxQual[5]+vdRxQual[6]+vdRxQual[7])}%</Item>
            <Item typeName="Int32" key="RowAt">5</Item>
            <Item typeName="Int32" key="ColAt">12</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(西区MS2){100*vdRxQual[3]/(vdRxQual[0]+vdRxQual[1]+vdRxQual[2]+vdRxQual[3]+vdRxQual[4]+vdRxQual[5]+vdRxQual[6]+vdRxQual[7])}%</Item>
            <Item typeName="Int32" key="RowAt">6</Item>
            <Item typeName="Int32" key="ColAt">12</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(西区MS2){100*vdRxQual[4]/(vdRxQual[0]+vdRxQual[1]+vdRxQual[2]+vdRxQual[3]+vdRxQual[4]+vdRxQual[5]+vdRxQual[6]+vdRxQual[7])}%</Item>
            <Item typeName="Int32" key="RowAt">7</Item>
            <Item typeName="Int32" key="ColAt">12</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(西区MS2){100*vdRxQual[5]/(vdRxQual[0]+vdRxQual[1]+vdRxQual[2]+vdRxQual[3]+vdRxQual[4]+vdRxQual[5]+vdRxQual[6]+vdRxQual[7])}%</Item>
            <Item typeName="Int32" key="RowAt">8</Item>
            <Item typeName="Int32" key="ColAt">12</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(西区MS2){100*vdRxQual[6]/(vdRxQual[0]+vdRxQual[1]+vdRxQual[2]+vdRxQual[3]+vdRxQual[4]+vdRxQual[5]+vdRxQual[6]+vdRxQual[7])}%</Item>
            <Item typeName="Int32" key="RowAt">9</Item>
            <Item typeName="Int32" key="ColAt">12</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(西区MS2){100*vdRxQual[7]/(vdRxQual[0]+vdRxQual[1]+vdRxQual[2]+vdRxQual[3]+vdRxQual[4]+vdRxQual[5]+vdRxQual[6]+vdRxQual[7])}%</Item>
            <Item typeName="Int32" key="RowAt">10</Item>
            <Item typeName="Int32" key="ColAt">12</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(北区MS2){100*vdRxQual[0]/(vdRxQual[0]+vdRxQual[1]+vdRxQual[2]+vdRxQual[3]+vdRxQual[4]+vdRxQual[5]+vdRxQual[6]+vdRxQual[7])}%</Item>
            <Item typeName="Int32" key="RowAt">3</Item>
            <Item typeName="Int32" key="ColAt">16</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(北区MS2){100*vdRxQual[1]/(vdRxQual[0]+vdRxQual[1]+vdRxQual[2]+vdRxQual[3]+vdRxQual[4]+vdRxQual[5]+vdRxQual[6]+vdRxQual[7])}%</Item>
            <Item typeName="Int32" key="RowAt">4</Item>
            <Item typeName="Int32" key="ColAt">16</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(北区MS2){100*vdRxQual[2]/(vdRxQual[0]+vdRxQual[1]+vdRxQual[2]+vdRxQual[3]+vdRxQual[4]+vdRxQual[5]+vdRxQual[6]+vdRxQual[7])}%</Item>
            <Item typeName="Int32" key="RowAt">5</Item>
            <Item typeName="Int32" key="ColAt">16</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(北区MS2){100*vdRxQual[3]/(vdRxQual[0]+vdRxQual[1]+vdRxQual[2]+vdRxQual[3]+vdRxQual[4]+vdRxQual[5]+vdRxQual[6]+vdRxQual[7])}%</Item>
            <Item typeName="Int32" key="RowAt">6</Item>
            <Item typeName="Int32" key="ColAt">16</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(北区MS2){100*vdRxQual[4]/(vdRxQual[0]+vdRxQual[1]+vdRxQual[2]+vdRxQual[3]+vdRxQual[4]+vdRxQual[5]+vdRxQual[6]+vdRxQual[7])}%</Item>
            <Item typeName="Int32" key="RowAt">7</Item>
            <Item typeName="Int32" key="ColAt">16</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(北区MS2){100*vdRxQual[5]/(vdRxQual[0]+vdRxQual[1]+vdRxQual[2]+vdRxQual[3]+vdRxQual[4]+vdRxQual[5]+vdRxQual[6]+vdRxQual[7])}%</Item>
            <Item typeName="Int32" key="RowAt">8</Item>
            <Item typeName="Int32" key="ColAt">16</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(北区MS2){100*vdRxQual[6]/(vdRxQual[0]+vdRxQual[1]+vdRxQual[2]+vdRxQual[3]+vdRxQual[4]+vdRxQual[5]+vdRxQual[6]+vdRxQual[7])}%</Item>
            <Item typeName="Int32" key="RowAt">9</Item>
            <Item typeName="Int32" key="ColAt">16</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(北区MS2){100*vdRxQual[7]/(vdRxQual[0]+vdRxQual[1]+vdRxQual[2]+vdRxQual[3]+vdRxQual[4]+vdRxQual[5]+vdRxQual[6]+vdRxQual[7])}%</Item>
            <Item typeName="Int32" key="RowAt">10</Item>
            <Item typeName="Int32" key="ColAt">16</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(东区){100*((vdRxQual[0]+vdRxQual[1]+vdRxQual[2])+0.7*(vdRxQual[3]+vdRxQual[5]))/(vdRxQual[0]+vdRxQual[1]+vdRxQual[2]+vdRxQual[3]+vdRxQual[4]+vdRxQual[5]+vdRxQual[6]+vdRxQual[7])}%</Item>
            <Item typeName="Int32" key="RowAt">12</Item>
            <Item typeName="Int32" key="ColAt">1</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(南区){100*((vdRxQual[0]+vdRxQual[1]+vdRxQual[2])+0.7*(vdRxQual[3]+vdRxQual[5]))/(vdRxQual[0]+vdRxQual[1]+vdRxQual[2]+vdRxQual[3]+vdRxQual[4]+vdRxQual[5]+vdRxQual[6]+vdRxQual[7])}%</Item>
            <Item typeName="Int32" key="RowAt">12</Item>
            <Item typeName="Int32" key="ColAt">5</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(西区){100*((vdRxQual[0]+vdRxQual[1]+vdRxQual[2])+0.7*(vdRxQual[3]+vdRxQual[5]))/(vdRxQual[0]+vdRxQual[1]+vdRxQual[2]+vdRxQual[3]+vdRxQual[4]+vdRxQual[5]+vdRxQual[6]+vdRxQual[7])}%</Item>
            <Item typeName="Int32" key="RowAt">12</Item>
            <Item typeName="Int32" key="ColAt">9</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(北区){100*((vdRxQual[0]+vdRxQual[1]+vdRxQual[2])+0.7*(vdRxQual[3]+vdRxQual[5]))/(vdRxQual[0]+vdRxQual[1]+vdRxQual[2]+vdRxQual[3]+vdRxQual[4]+vdRxQual[5]+vdRxQual[6]+vdRxQual[7])}%</Item>
            <Item typeName="Int32" key="RowAt">12</Item>
            <Item typeName="Int32" key="ColAt">13</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(东区MS1){100*((vdRxQual[0]+vdRxQual[1]+vdRxQual[2])+0.7*(vdRxQual[3]+vdRxQual[5]))/(vdRxQual[0]+vdRxQual[1]+vdRxQual[2]+vdRxQual[3]+vdRxQual[4]+vdRxQual[5]+vdRxQual[6]+vdRxQual[7]) }%</Item>
            <Item typeName="Int32" key="RowAt">11</Item>
            <Item typeName="Int32" key="ColAt">1</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(南区MS1){100*((vdRxQual[0]+vdRxQual[1]+vdRxQual[2])+0.7*(vdRxQual[3]+vdRxQual[5]))/(vdRxQual[0]+vdRxQual[1]+vdRxQual[2]+vdRxQual[3]+vdRxQual[4]+vdRxQual[5]+vdRxQual[6]+vdRxQual[7]) }%</Item>
            <Item typeName="Int32" key="RowAt">11</Item>
            <Item typeName="Int32" key="ColAt">5</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(西区MS1){100*((vdRxQual[0]+vdRxQual[1]+vdRxQual[2])+0.7*(vdRxQual[3]+vdRxQual[5]))/(vdRxQual[0]+vdRxQual[1]+vdRxQual[2]+vdRxQual[3]+vdRxQual[4]+vdRxQual[5]+vdRxQual[6]+vdRxQual[7]) }%</Item>
            <Item typeName="Int32" key="RowAt">11</Item>
            <Item typeName="Int32" key="ColAt">9</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(北区MS1){100*((vdRxQual[0]+vdRxQual[1]+vdRxQual[2])+0.7*(vdRxQual[3]+vdRxQual[5]))/(vdRxQual[0]+vdRxQual[1]+vdRxQual[2]+vdRxQual[3]+vdRxQual[4]+vdRxQual[5]+vdRxQual[6]+vdRxQual[7]) }%</Item>
            <Item typeName="Int32" key="RowAt">11</Item>
            <Item typeName="Int32" key="ColAt">13</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(东区MS2){100*((vdRxQual[0]+vdRxQual[1]+vdRxQual[2])+0.7*(vdRxQual[3]+vdRxQual[5]))/(vdRxQual[0]+vdRxQual[1]+vdRxQual[2]+vdRxQual[3]+vdRxQual[4]+vdRxQual[5]+vdRxQual[6]+vdRxQual[7]) }%</Item>
            <Item typeName="Int32" key="RowAt">11</Item>
            <Item typeName="Int32" key="ColAt">3</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(南区MS2){100*(vdRxQual[0]+vdRxQual[1]+vdRxQual[2])+0.7*(vdRxQual[3]+vdRxQual[5]))/(vdRxQual[0]+vdRxQual[1]+vdRxQual[2]+vdRxQual[3]+vdRxQual[4]+vdRxQual[5]+vdRxQual[6]+vdRxQual[7]) }%</Item>
            <Item typeName="Int32" key="RowAt">11</Item>
            <Item typeName="Int32" key="ColAt">7</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(西区MS2){100*((vdRxQual[0]+vdRxQual[1]+vdRxQual[2])+0.7*(vdRxQual[3]+vdRxQual[5]))/(vdRxQual[0]+vdRxQual[1]+vdRxQual[2]+vdRxQual[3]+vdRxQual[4]+vdRxQual[5]+vdRxQual[6]+vdRxQual[7]) }%</Item>
            <Item typeName="Int32" key="RowAt">11</Item>
            <Item typeName="Int32" key="ColAt">11</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Exp">(北区MS2){100*((vdRxQual[0]+vdRxQual[1]+vdRxQual[2])+0.7*(vdRxQual[3]+vdRxQual[5]))/(vdRxQual[0]+vdRxQual[1]+vdRxQual[2]+vdRxQual[3]+vdRxQual[4]+vdRxQual[5]+vdRxQual[6]+vdRxQual[7]) }%</Item>
            <Item typeName="Int32" key="RowAt">11</Item>
            <Item typeName="Int32" key="ColAt">15</Item>
            <Item typeName="Int32" key="BkColorR">255</Item>
            <Item typeName="Int32" key="BkColorG">255</Item>
            <Item typeName="Int32" key="BkColorB">255</Item>
            <Item typeName="Int32" key="ForeColorR">0</Item>
            <Item typeName="Int32" key="ForeColorG">0</Item>
            <Item typeName="Int32" key="ForeColorB">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
        </Item>
        <Item typeName="IList" key="Graphs" />
        <Item typeName="IList" key="ColWidth">
          <Item typeName="Int32">146</Item>
          <Item typeName="Int32">63</Item>
          <Item typeName="Int32">58</Item>
          <Item typeName="Int32">68</Item>
          <Item typeName="Int32">64</Item>
          <Item typeName="Int32">65</Item>
          <Item typeName="Int32">53</Item>
          <Item typeName="Int32">62</Item>
          <Item typeName="Int32">53</Item>
          <Item typeName="Int32">62</Item>
          <Item typeName="Int32">46</Item>
          <Item typeName="Int32">65</Item>
          <Item typeName="Int32">50</Item>
          <Item typeName="Int32">64</Item>
          <Item typeName="Int32">51</Item>
          <Item typeName="Int32">64</Item>
          <Item typeName="Int32">100</Item>
          <Item typeName="Int32">100</Item>
        </Item>
      </Item>
    </Item>
  </Config>
</Configs>