﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.KPI_Statistics;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.Util.UiEx;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;
using System.Xml;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc.ZTLTEClusterAna;


namespace MasterCom.RAMS.ZTFunc
{
    class ZTLTEClusterAnaQuery : QueryKpiStatByFiles
    {

        protected override void fireShowResult()
        {
            ResultForm resultForm = MainModel.GetObjectFromBlackboard(
                typeof(ResultForm).FullName) as ResultForm;
            if (resultForm == null || resultForm.IsDisposed)
            {
                resultForm = new ResultForm(MainModel);
            }
            //簇内平均站间距怎么求现场那边还没给出，暂时直接赋值544
            resultForm.FillData(manager.GetResult(0, ClusterType));
            if (!resultForm.Visible)
            {
                resultForm.Show(MainModel.MainForm);
            }
        }

        protected override bool getConditionBeforeQuery()
        {
            isQueryAllParams = false;
            KpiDataManager = new KPIDataManager();
            
            return true;
        }
        
        protected override string getStatImgNeededTriadID(params object[] paramSet)
        {
            string triadIDSet;
            List<string> formulaSet = new List<string>();
            formulaSet.Add("{100*((Lte_61210380+Lte_61210381+Lte_61210382 +Lte_61210306+Lte_61210307+Lte_61210308)/Lte_61210301 }");//RSRP>-95
            formulaSet.Add("{Lte_61210403 }");//SINR平均值
            formulaSet.Add("{100*Lte_61210F09/Lte_61210F01 }");//道路重叠覆盖度
            formulaSet.Add("{((value4[871] / 1000) / Lte_0806) *100}");//LTE连续质差里程占比
            formulaSet.Add("{(evtIdCount[858]/(evtIdCount[858]+evtIdCount[896]))*((evtIdCount[855]+evtIdCount[891])"
                + "/(evtIdCount[855]+evtIdCount[856]+evtIdCount[891]+evtIdCount[892]))}");//连接建立成功率
            formulaSet.Add("{(value1[855]+value1[891])/((evtIdCount[855]+evtIdCount[891])*1000) + (value1[858])/(evtIdCount[858]*1000)}");//连接建立时延(ms)
            formulaSet.Add("{100*evtIdCount[896]/evtIdCount[858]}");//掉线率
            formulaSet.Add("{(evtIdCount[850]+evtIdCount[898])*100/(evtIdCount[849]+evtIdCount[897])}");//切换成功率
            formulaSet.Add("{value1[850]/evtIdCount[850]}");//控制面切换时延(ms)
            //formulaSet.Add("{Lte_61210403 }");//用户面切换时延(ms)
            formulaSet.Add("{Lte_61211D12/(1024*1024) }");//L2平均下行吞吐量(Mbps)
            formulaSet.Add("{Lte_61211E12/(1024*1024) }");//L2平均上行吞吐量(Mbps)
            formulaSet.Add("{100*(evtIdCount[879]+value9[879])/(evtIdCount[876]+value9[876])}");//CSFB回落成功率
            formulaSet.Add("{value1[1011]/(1000.0*evtIdCount[1011])}");//CSFB时延
            formulaSet.Add("{100*(Lte_61210434+Lte_61210411+Lte_61210408+Lte_61210409+Lte_6121040A+Lte_6121040B)/Lte_61210401 }");//SINR>8dB
            formulaSet.Add("{100*(Lte_61210433+Lte_6121040F+Lte_61210410 +Lte_61210411+Lte_61210412)/Lte_61210401 }");//SINR>1.5dB
            formulaSet.Add("{100*(Lte_6121040F+Lte_61210410 +Lte_61210411+Lte_61210408+Lte_61210409+Lte_6121040A+Lte_6121040B)/Lte_61210401 }");//SINR>3dB
            formulaSet.Add("{100*(Lte_61210410 +Lte_61210411+Lte_61210408+Lte_61210409+Lte_6121040A+Lte_6121040B)/Lte_61210401 }");//SINR>6dB
            formulaSet.Add("{100*(Lte_61210320+Lte_6121031F+Lte_6121031E+Lte_6121031D+Lte_6121031C+Lte_61210389)/Lte_61210301 }");//RSRP>-103dBm



            triadIDSet = this.getTriadIDIgnoreServiceType(formulaSet);
            return triadIDSet;
        }

        protected override void afterRecieveAllData(params object[] reservedParams)
        {
            try
            {
                List<KPIDataGroup> dataSet = KpiDataManager.GetReportGroupDataSet();
                FileInfo f = dataSet[0].GroupInfo as FileInfo;
                getClusterType(f);
                manager = new AnalyzerManager();
                manager.SetAcceptCond(ClusterType);
                foreach (KPIDataGroup item in dataSet)
                {
                    if (item != null)
                    {
                        item.FinalMtMoGroup();
                    }
                }
                foreach (KPIDataGroup item in dataSet)
                {

                    if (item.GroupInfo is FileInfo)
                    {
                        FileInfo fi = item.GroupInfo as FileInfo;
                        manager.AnalyzeFile(fi, item);
                    }
                }
                CSFBFile = null;
            }
            catch(Exception ex)
            {
                MessageBox.Show(ex.Message + ex.StackTrace);
            }
        }

        //根据簇内基站平均距离求得簇为密集城区还是特殊场景
        private void getClusterType(FileInfo fi)
        {
            //ClusterType在这里设置
            if (fi.Name.Contains("密集城区"))
            {
                ClusterType = true;
            }
            else
            {
                ClusterType = false;
            }
            

        }

        private bool ClusterType = false;//true 为密集城区,false为特殊场景


        AnalyzerManager manager;





        public override string Name
        {
            get { return "簇评估"; }
        }






        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22064, this.Name);
        }

        protected override bool isValidCondition()
        {
            return true;
        }
        FileInfo CSFBFile = null;
        protected override void recieveAndHandleSpecificStatData(Net.Package package, List<Model.Interface.StatImgDefItem> curImgColumnDef, KPIStatDataBase singleStatData)
        {
            try
            {
                if (curFile.Name.Contains("CSFB"))
                {
                    if (CSFBFile == null)
                    {
                        CSFBFile = curFile;
                    }
                    fillStatData(package, curImgColumnDef, singleStatData);
                    KpiDataManager.AddStatData(string.Empty, CSFBFile, curFile, singleStatData, false);
                }
                else
                {
                    fillStatData(package, curImgColumnDef, singleStatData);
                    KpiDataManager.AddStatData(string.Empty, curFile, curFile, singleStatData, false);
                }
            }
            catch(Exception ex)
            {
                MessageBox.Show(ex.Message + ex.StackTrace);
            }
        }

        protected override void handleStatEvent(Model.Event evt)
        {
            StatDataEvent eventData = new StatDataEvent(evt, this.curReportStyle != null && this.curReportStyle.IsSeparateEvtByServiceID, needSeparateByFileName(evt));
            if (curFile.Name.Contains("CSFB"))
            {
                if (CSFBFile == null)
                {
                    CSFBFile = curFile;
                }
                KpiDataManager.AddStatData(string.Empty, CSFBFile, curFile, eventData, false);
            }
            else
            {
                KpiDataManager.AddStatData(string.Empty, curFile, curFile, eventData, false);
            }
        }


    }
}
