﻿using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRHttpPageAnaBase : DIYAnalyseFilesOneByOneByRegion
    {
        public NRHttpPageAnaBase(MainModel mainModel)
            : base(mainModel)
        {
            this.FilterEventByRegion = false;
            this.IncludeMessage = true;
            this.IncludeTestPoint = true;
            this.IncludeEvent = true;

            ServiceTypes.Clear();
            ServiceTypes = ServiceTypeManager.GetServiceTypesByServiceName(ServiceName.NR);
        }

        protected string themeName = "";

        protected List<NRHttpPageAnaItem> httpPageAnaList = new List<NRHttpPageAnaItem>();
        protected NRHttpPageCondion httpPageCondition = new NRHttpPageCondion();

        protected override bool getCondition()
        {
            setParmAndServiceType();
            NRHttpPageSettingDlg dlg = new NRHttpPageSettingDlg(httpPageCondition);
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                httpPageCondition = dlg.GetCondition();
                return true;
            }
            return false;
        }

        /// <summary>
        /// 设置参数名和业务类型
        /// </summary>
        protected virtual void setParmAndServiceType()
        {
            Columns = NRTpHelper.InitBaseReplayParamBackground(false, true);
            Columns.Add("NR_APP_type");
            Columns.Add("NR_APP_Speed");
            Columns.Add("NR_APP_Status");

            themeName = "NR:SS_RSRP";
        }

        protected override void clearDataBeforeAnalyseFiles()
        {
            httpPageAnaList = new List<NRHttpPageAnaItem>();
        }

        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager fileMng in MainModel.DTDataManager.FileDataManagers)
            {
                List<Event> evtsList = fileMng.Events;
                NRHttpPageAnaItem curAnaItem = null;
                foreach (Event evt in evtsList)
                {
                    if (evt.ID == (int)NREventManager.HttpRequest)     //Http浏览发起
                    {
                        setStartInfo(evt, fileMng.Messages, ref curAnaItem);
                    }
                    else if (evt.ID == (int)NREventManager.HttpComplete ||
                         evt.ID == (int)NREventManager.HttpIncomplete)  //Http浏览加载完成或未完成
                    {
                        curAnaItem = setNRHttpPageAnaItem(fileMng, curAnaItem, evt);
                    }
                }
            }
        }

        private void setStartInfo(Event evt, List<Model.Message> msgList, ref NRHttpPageAnaItem anaItem)
        {
            anaItem = new NRHttpPageAnaItem(evt.FileName);
            anaItem.BeginTime = evt.DateTime;
            anaItem.BeginLatitude = evt.Latitude;
            anaItem.BeginLongitude = evt.Longitude;
            anaItem.URL = ZTUrlAnalyzer.GetURL(evt.SN, msgList);
        }

        private NRHttpPageAnaItem setNRHttpPageAnaItem(DTFileDataManager fileMng, NRHttpPageAnaItem curAnaItem, Event evt)
        {
            if (curAnaItem != null)
            {
                curAnaItem.EndTime = evt.DateTime;
                TimeSpan ts = curAnaItem.EndTime.Subtract(curAnaItem.BeginTime);
                curAnaItem.PagingTime = ts.TotalSeconds;
                if (curAnaItem.PagingTime > httpPageCondition.LessPageTime
                    && (curAnaItem.PagingTime <= httpPageCondition.GreatPageTime 
                        || httpPageCondition.IsNoGreat))
                //达到浏览时长要求
                {
                    TimePeriod period = new TimePeriod(curAnaItem.BeginTime.AddSeconds(-httpPageCondition.PreLoadTime), curAnaItem.EndTime);
                    curAnaItem.TpsList = getTestPoinsByPeriod(period, fileMng.TestPoints);
                    setEndInfo(evt, ref curAnaItem);
                }
            }

            return curAnaItem;
        }

        protected List<TestPoint> getTestPoinsByPeriod(TimePeriod period, List<TestPoint> tps)
        {
            List<TestPoint> ret = new List<TestPoint>();
            for (int index = 0; index < tps.Count; index++)
            {
                TestPoint tp = tps[index];
                if (tp.DateTime >= period.BeginTime && tp.DateTime <= period.EndTime)
                {
                    ret.Add(tp);
                }
                else if (tp.DateTime > period.EndTime)
                {
                    break;
                }
            }
            return ret;
        }

        /// <summary>
        /// 设置结束事件相关信息
        /// </summary>
        /// <param name="evt"></param>
        /// <param name="anaItem"></param>
        private void setEndInfo(Event evt, ref NRHttpPageAnaItem anaItem)
        {
            if (isValidPeriod(anaItem.TpsList))
            {
                anaItem.FillItem();
                anaItem.SN = httpPageAnaList.Count + 1;
                anaItem.EndLatitude = evt.Latitude;
                anaItem.EndLongitude = evt.Longitude;
                checkIsFailed(ref anaItem, evt.ID);
                anaItem.StatTpsList = getVaildTestPoints(anaItem.TpsList, anaItem.MaxNBIndexList);
                httpPageAnaList.Add(anaItem);
            }
            anaItem = null;
        }

        protected virtual bool isValidPeriod(List<TestPoint> tpsList)
        {
            foreach (TestPoint tp in tpsList)
            {
                if (isValidTestPoint(tp))
                {
                    return true;
                }
            }
            return false;
        }

        protected void checkIsFailed(ref NRHttpPageAnaItem anaItem, int evtID)
        {
            if (evtID == (int)NREventManager.HttpComplete)
            {
                anaItem.IsFailed = "成功";
            }
            else if (evtID == (int)NREventManager.HttpIncomplete)
            {
                anaItem.IsFailed = "失败";
            }
        }

        protected List<KeyValuePair<TestPoint, int>> getVaildTestPoints(List<TestPoint> tpList, List<int> maxNBIndexList)
        {
            List<KeyValuePair<TestPoint, int>> ret = new List<KeyValuePair<TestPoint, int>>();
            if (tpList.Count == maxNBIndexList.Count)
            {
                for (int i = 0; i < tpList.Count; i++)
                {
                    if (isValidTestPoint(tpList[i]))
                    {
                        ret.Add(new KeyValuePair<TestPoint, int>(tpList[i], maxNBIndexList[i]));
                    }
                }
            }
            return ret;
        }

        protected override void fireShowForm()
        {
            if (httpPageAnaList.Count == 0)
            {
                MessageBox.Show("没有符合条件的信息！");
                return;
            }
            MainModel.FireSetDefaultMapSerialTheme(themeName);
            NRHttpPageAnaForm frm = MainModel.CreateResultForm(typeof(NRHttpPageAnaForm)) as NRHttpPageAnaForm;
            frm.FillData(httpPageAnaList);
            frm.Owner = MainModel.MainForm;
            frm.Visible = true;
            frm.BringToFront();
        }
    }

    public class NRHttpPageAnaByFile : NRHttpPageAnaBase
    {
        public NRHttpPageAnaByFile(MainModel mm)
            : base(mm)
        {

        }
        private static NRHttpPageAnaByFile instance = null;
        protected static readonly object lockObj = new object();
        public static NRHttpPageAnaByFile GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new NRHttpPageAnaByFile(MainModel.GetInstance());
                    }
                }
            }
            return instance;
        }

        public override string Name
        {
            get { return "NR Http浏览分析(按文件)"; }
        }

        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22103, this.Name);
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.File;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }

        protected override bool isValidPeriod(List<TestPoint> tpsList)
        {
            return true;
        }
    }

    public class NRHttpPageAnaByRegion : NRHttpPageAnaBase
    {
        public NRHttpPageAnaByRegion(MainModel mm)
            : base(mm)
        {

        }
        private static NRHttpPageAnaByRegion instance = null;
        protected static readonly object lockObj = new object();
        public static NRHttpPageAnaByRegion GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new NRHttpPageAnaByRegion(MainModel.GetInstance());
                    }
                }
            }
            return instance;
        }

        public override string Name
        {
            get { return "Http浏览分析(按区域)"; }
        }

        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 35000, 35054, this.Name);
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.Region;
        }
    }
}
