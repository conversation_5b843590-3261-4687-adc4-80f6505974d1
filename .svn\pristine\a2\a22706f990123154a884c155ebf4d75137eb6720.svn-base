﻿using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTHighReverseFlowCoverage._45G700MHighReverseFlowCoverage
{ 
    public class CellNeighbor
    {
        public string NrCgi { get; set; }
        public long Nci { get; set; }
        public string LteCgi { get; set; }
        public long Eci { get; set; }

        public void FillDataBySQL(Package package, Dictionary<string, long> nrCgi_Nci
            , Dictionary<string, long> lteCgi_Eci)
        {
            NrCgi = package.Content.GetParamString().Trim('"');
            LteCgi = package.Content.GetParamString().Trim('"');

            if (!nrCgi_Nci.TryGetValue(NrCgi, out var nci))
            {
                nci = CGIHelper.GetNciCgiByCgi(NrCgi);
                nrCgi_Nci.Add(NrCgi, nci);
            }
            Nci = nci;

            if (!lteCgi_Eci.TryGetValue(LteCgi, out var eci))
            {
                eci = CGIHelper.GetEciCgiByCgi(LteCgi);
                lteCgi_Eci.Add(LteCgi, eci);
            }
            Eci = eci;
        }
    }
}
