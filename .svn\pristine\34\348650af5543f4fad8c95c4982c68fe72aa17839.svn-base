﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.Func.SampleFilter
{
    public partial class SampleFilterDlg : Form
    {
        private MapForm mapform = null;
        public SampleFilterDlg(MapForm form)
        {
            InitializeComponent();
            this.mapform = form;
        }

        internal void InitShow()
        {
            cbxSampleFilter.Items.Add("TD DPCH 过滤");
            cbxSampleFilter.Items.Add("按BCCH/BSIC（UARFCN/CPI）过滤");
            cbxSampleFilter.SelectedIndex = 0;
        }

        private void btnFresh_Click(object sender, EventArgs e)
        {
            bool useNeib = cbxUseNeib.Checked;
            if(cbxSampleFilter.SelectedIndex == 0)
            {
                Dictionary<int, bool> dpchDic = new Dictionary<int, bool>();
                MainModel mmodel = MainModel.GetInstance();
                foreach (DTFileDataManager fileDataManager in mmodel.DTDataManager.FileDataManagers)
                {
                    if (mmodel.VisibleOffsetManager.IsFileVisble(fileDataManager.FileID))
                    {
                        foreach (TestPoint testPoint in fileDataManager.TestPoints)
                        {
                            if (testPoint is TDTestPointDetail)
                            {
                                TDTestPointDetail tpDetail = testPoint as TDTestPointDetail;
                                int? bcch = (int?)tpDetail["TD_DPCH_UARFCN"];
                                if (bcch != null && bcch > 0)
                                {
                                    dpchDic[(int)bcch] = true;
                                }
                            }
                        }
                    }
                }
                treeView.Nodes.Clear();
                foreach (int bcch in dpchDic.Keys)
                {
                    TreeNode bcchNode = new TreeNode();
                    bcchNode.Text = "" + bcch;
                    bcchNode.Tag = bcch;
                    treeView.Nodes.Add(bcchNode);
                }
            }
            else if(cbxSampleFilter.SelectedIndex == 1)
            {
                Dictionary<short, Dictionary<byte, bool>> bcchBsicDic = new Dictionary<short, Dictionary<byte, bool>>();
                MainModel mmodel = MainModel.GetInstance();
                foreach (DTFileDataManager fileDataManager in mmodel.DTDataManager.FileDataManagers)
                {
                    if (mmodel.VisibleOffsetManager.IsFileVisble(fileDataManager.FileID))
                    {
                        foreach (TestPoint testPoint in fileDataManager.TestPoints)
                        {
                            if(testPoint is TestPointScan)
                            {
                                TestPointScan scanPoint = testPoint as TestPointScan;
                                int count = (int)scanPoint["SCAN_Count"];
                                for (int i = 0; i < count; i++)
                                {
                                    short? bcch = (short?)scanPoint["SCAN_BCCH", i];
                                    byte? bsic = (byte?)scanPoint["SCAN_BSIC", i];
                                    if (bcch != null && bsic != null && bsic < 64)
                                    {
                                        Dictionary<byte, bool> bsicDic = null;
                                        if (!bcchBsicDic.TryGetValue((short)bcch, out bsicDic))
                                        {
                                            bsicDic = new Dictionary<byte, bool>();
                                            bcchBsicDic[(short)bcch] = bsicDic;
                                        }
                                        if (!bsicDic.ContainsKey((byte)bsic))
                                        {
                                            bsicDic[(byte)bsic] = true;
                                        }
                                    }
                                }
                            }
                            else if (testPoint is ScanTestPoint_G)
                            {
                                for (int i = 0; i < 10; i++)
                                {
                                    short? bcch = (short?)(int?)testPoint["GSCAN_BCCH", i];
                                    byte? bsic = (byte?)(int?)testPoint["GSCAN_BSIC", i];
                                    if (bcch != null && bsic != null && bsic < 64)
                                    {
                                        Dictionary<byte, bool> bsicDic = null;
                                        if (!bcchBsicDic.TryGetValue((short)bcch, out bsicDic))
                                        {
                                            bsicDic = new Dictionary<byte, bool>();
                                            bcchBsicDic[(short)bcch] = bsicDic;
                                        }
                                        if (!bsicDic.ContainsKey((byte)bsic))
                                        {
                                            bsicDic[(byte)bsic] = true;
                                        }
                                    }
                                }
                            }
                            else if(testPoint is TestPointDetail)
                            {
                                TestPointDetail tpDetail = testPoint as TestPointDetail;
                                short? bcch = (short?)tpDetail["BCCH"];
                                byte? bsic = (byte?)tpDetail["BSIC"];
                                if (bcch != null && bsic != null && bsic < 64)
                                {
                                    Dictionary<byte, bool> bsicDic = null;
                                    if (!bcchBsicDic.TryGetValue((short)bcch, out bsicDic))
                                    {
                                        bsicDic = new Dictionary<byte, bool>();
                                        bcchBsicDic[(short)bcch] = bsicDic;
                                    }
                                    if (!bsicDic.ContainsKey((byte)bsic))
                                    {
                                        bsicDic[(byte)bsic] = true;
                                    }
                                }
                                if(useNeib)
                                {
                                    for (int i = 0; i < 6; i++)
                                    {
                                        bcch = (short?)testPoint["N_BCCH", i];
                                        bsic = (byte?)testPoint["N_BSIC", i];
                                        if (bcch != null && bsic != null && bsic < 64)
                                        {
                                            Dictionary<byte, bool> bsicDic = null;
                                            if (!bcchBsicDic.TryGetValue((short)bcch, out bsicDic))
                                            {
                                                bsicDic = new Dictionary<byte, bool>();
                                                bcchBsicDic[(short)bcch] = bsicDic;
                                            }
                                            if (!bsicDic.ContainsKey((byte)bsic))
                                            {
                                                bsicDic[(byte)bsic] = true;
                                            }
                                        }
                                    }
                                }
                            }
                            else if (testPoint is TDTestPointDetail)
                            {
                                TDTestPointDetail tpDetail = testPoint as TDTestPointDetail;
                                int? bcch = (int?)tpDetail[MainModel.TD_SCell_UARFCN];
                                int? bsic = (int?)tpDetail[MainModel.TD_SCell_CPI];
                                if (bcch != null && bsic != null && bcch > 0 && bsic < 255)
                                {
                                    Dictionary<byte, bool> bsicDic = null;
                                    if (!bcchBsicDic.TryGetValue((short)bcch, out bsicDic))
                                    {
                                        bsicDic = new Dictionary<byte, bool>();
                                        bcchBsicDic[(short)bcch] = bsicDic;
                                    }
                                    if (!bsicDic.ContainsKey((byte)bsic))
                                    {
                                        bsicDic[(byte)bsic] = true;
                                    }
                                }
                                if(useNeib)
                                {
                                    for (int i = 0; i < 6; i++)
                                    {
                                        bcch = (int?)testPoint["TD_NCell_UARFCN", i];
                                        bsic = (int?)testPoint["TD_NCell_CPI", i];
                                        if (bcch != null && bsic != null && bcch > 0 && bsic < 255)
                                        {
                                            Dictionary<byte, bool> bsicDic = null;
                                            if (!bcchBsicDic.TryGetValue((short)bcch, out bsicDic))
                                            {
                                                bsicDic = new Dictionary<byte, bool>();
                                                bcchBsicDic[(short)bcch] = bsicDic;
                                            }
                                            if (!bsicDic.ContainsKey((byte)bsic))
                                            {
                                                bsicDic[(byte)bsic] = true;
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                treeView.Nodes.Clear();
                foreach(short bcch in bcchBsicDic.Keys)
                {
                    Dictionary<byte, bool> bsicDic = bcchBsicDic[bcch];
                    TreeNode bcchNode = new TreeNode();
                    bcchNode.Text = "" + bcch;
                    bcchNode.Tag = bcch;
                    treeView.Nodes.Add(bcchNode);
                    foreach(byte bsic in bsicDic.Keys)
                    {
                        TreeNode bsicNode = new TreeNode();
                        bsicNode.Text = "" + bsic;
                        bsicNode.Tag = bsic;
                        bcchNode.Nodes.Add(bsicNode);
                    }
                }
            }
        }

        private void btnApply_Click(object sender, EventArgs e)
        {
            if(cbxSampleFilter.SelectedIndex == 0)
            {
                List<int> list = getDPCHDic();
                string param = getParamStr(list);
                MapDTLayer dtLayer = mapform.GetDTLayer();
                setIndex0Serial(param, dtLayer);
                dtLayer.Invalidate();
            }
            else if (cbxSampleFilter.SelectedIndex == 1)
            {
                short bcch = 0;
                byte bsic = 0;
                getBcchBsicSet(ref bcch, ref bsic);
                MapDTLayer dtLayer = mapform.GetDTLayer();
                setIndex1Serial(bcch, bsic, dtLayer);
                dtLayer.Invalidate();
            }
        }

        private void setIndex0Serial(string param, MapDTLayer dtLayer)
        {
            foreach (MapSerialInfo serialInfo in dtLayer.SerialInfos)
            {
                if (serialInfo.Name == "TD_显示指定DPCH")
                {
                    serialInfo.OwnFuncParam = param;
                    serialInfo.Visible = true;
                }
                else
                {
                    serialInfo.Visible = false;
                }
            }
        }

        private void setIndex1Serial(short bcch, byte bsic, MapDTLayer dtLayer)
        {
            foreach (MapSerialInfo serialInfo in dtLayer.SerialInfos)
            {
                if (serialInfo.Name == "ScanByBCCHBSIC")
                {
                    serialInfo.OwnFuncParam = (cbxUseNeib.Checked ? "1" : "0") + "," + bcch + "," + bsic;
                    break;
                }
            }
        }

        private static string getParamStr(List<int> list)
        {
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < list.Count; i++)
            {
                sb.Append(list[i]);
                if (i != list.Count - 1)
                {
                    sb.Append(",");
                }
            }
            string param = sb.ToString();
            return param;
        }

        private List<int> getDPCHDic()
        {
            List<int> dpchList = new List<int>();
            foreach (TreeNode bcchNode in treeView.Nodes)
            {
                if (bcchNode.Checked)
                {
                    int bcch = (int)bcchNode.Tag;
                    dpchList.Add(bcch);
                }
            }
            return dpchList;
        }
        private void getBcchBsicSet(ref short bcch,ref byte bsic)
        {
            foreach (TreeNode bcchNode in treeView.Nodes)
            {
                if (bcchNode.Checked)
                {
                    bcch = (short)bcchNode.Tag;
                    foreach (TreeNode bsicNode in bcchNode.Nodes)
                    {
                        if (bsicNode.Checked)
                        {
                            bsic = (byte)bsicNode.Tag;
                            break;
                        }
                    }
                    break;
                }
            }
        }

        private void SampleFilterDlg_FormClosing(object sender, FormClosingEventArgs e)
        {
            if(e.CloseReason == CloseReason.UserClosing)
            {
                this.Visible = false;
                e.Cancel = true;
            }
        }

        private void treeView_AfterCheck(object sender, TreeViewEventArgs e)
        {
            if(cbxSampleFilter.SelectedIndex==0)
            {
                lbCurSel.Text = "";
            }
            else if(cbxSampleFilter.SelectedIndex ==1)
            {
                short bcch = 0;
                byte bsic = 0;
                getBcchBsicSet(ref bcch, ref bsic);
                lbCurSel.Text = "当前选中" + bcch + "," + bsic;
            }
            
        }
    }
}