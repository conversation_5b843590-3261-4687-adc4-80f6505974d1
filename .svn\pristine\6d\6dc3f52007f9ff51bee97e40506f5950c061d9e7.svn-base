﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class TestDetailShow
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.gridControlRes = new DevExpress.XtraGrid.GridControl();
            this.gridViewForTestDetail = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumnFileName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnTmdat = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnLAC = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnCI = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnProbleInf = new DevExpress.XtraGrid.Columns.GridColumn();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlRes)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewForTestDetail)).BeginInit();
            this.SuspendLayout();
            // 
            // gridControlRes
            // 
            this.gridControlRes.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlRes.Location = new System.Drawing.Point(0, 0);
            this.gridControlRes.MainView = this.gridViewForTestDetail;
            this.gridControlRes.Name = "gridControlRes";
            this.gridControlRes.Size = new System.Drawing.Size(749, 427);
            this.gridControlRes.TabIndex = 3;
            this.gridControlRes.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridViewForTestDetail});
            // 
            // gridViewForTestDetail
            // 
            this.gridViewForTestDetail.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumnFileName,
            this.gridColumnTmdat,
            this.gridColumnLAC,
            this.gridColumnCI,
            this.gridColumnProbleInf});
            this.gridViewForTestDetail.GridControl = this.gridControlRes;
            this.gridViewForTestDetail.Name = "gridViewForTestDetail";
            this.gridViewForTestDetail.OptionsBehavior.Editable = false;
            this.gridViewForTestDetail.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridViewForTestDetail.OptionsView.ColumnAutoWidth = false;
            this.gridViewForTestDetail.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumnFileName
            // 
            this.gridColumnFileName.Caption = "文件名";
            this.gridColumnFileName.FieldName = "FileName";
            this.gridColumnFileName.Name = "gridColumnFileName";
            this.gridColumnFileName.Visible = true;
            this.gridColumnFileName.VisibleIndex = 0;
            this.gridColumnFileName.Width = 250;
            // 
            // gridColumnTmdat
            // 
            this.gridColumnTmdat.Caption = "时间";
            this.gridColumnTmdat.FieldName = "Tmdat";
            this.gridColumnTmdat.Name = "gridColumnTmdat";
            this.gridColumnTmdat.Visible = true;
            this.gridColumnTmdat.VisibleIndex = 1;
            this.gridColumnTmdat.Width = 175;
            // 
            // gridColumnLAC
            // 
            this.gridColumnLAC.Caption = "LAC";
            this.gridColumnLAC.FieldName = "LAC";
            this.gridColumnLAC.Name = "gridColumnLAC";
            this.gridColumnLAC.Visible = true;
            this.gridColumnLAC.VisibleIndex = 2;
            this.gridColumnLAC.Width = 70;
            // 
            // gridColumnCI
            // 
            this.gridColumnCI.Caption = "CI";
            this.gridColumnCI.FieldName = "CI";
            this.gridColumnCI.Name = "gridColumnCI";
            this.gridColumnCI.Visible = true;
            this.gridColumnCI.VisibleIndex = 3;
            this.gridColumnCI.Width = 70;
            // 
            // gridColumnProbleInf
            // 
            this.gridColumnProbleInf.Caption = "问题类型";
            this.gridColumnProbleInf.FieldName = "Proble";
            this.gridColumnProbleInf.Name = "gridColumnProbleInf";
            this.gridColumnProbleInf.Visible = true;
            this.gridColumnProbleInf.VisibleIndex = 4;
            this.gridColumnProbleInf.Width = 150;
            // 
            // TestDetailShow
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(749, 427);
            this.Controls.Add(this.gridControlRes);
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "TestDetailShow";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Load += new System.EventHandler(this.TestDetailShow_Load);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlRes)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewForTestDetail)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraGrid.GridControl gridControlRes;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewForTestDetail;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnFileName;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnTmdat;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnLAC;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnCI;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnProbleInf;
    }
}