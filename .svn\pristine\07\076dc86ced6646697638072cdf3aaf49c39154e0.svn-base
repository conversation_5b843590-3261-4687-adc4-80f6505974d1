﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Runtime.Serialization;
using System.Text;
using MasterCom.MControls;
using MasterCom.MTGis;
using MasterCom.RAMS.ZTFunc.ZTRegionGridFilter;
using MasterCom.RAMS.Grid;

namespace MasterCom.RAMS.ZTFunc.ZTCellCoverTooClose
{
    public class RadiusFilteredGridLayer : CustomDrawLayer
    {
        public RadiusFilteredGridLayer(MapOperation mp, string name)
            : base(mp, name)
        {
            ColorRange c = new ColorRange(0, 30, Color.FromArgb(200, 255, 0, 0));
            ColorRanges.Add(c);
            c = new ColorRange(30, 40, Color.FromArgb(200, 255, 0, 255));
            ColorRanges.Add(c);
            c = new ColorRange(40, 50, Color.FromArgb(200, 255, 255, 0));
            ColorRanges.Add(c);
            c = new ColorRange(50, 60, Color.FromArgb(200, 128, 255, 255));
            ColorRanges.Add(c);
            c = new ColorRange(60, 70, Color.FromArgb(200, 0, 0, 255));
            ColorRanges.Add(c);
            c = new ColorRange(70, 85, Color.FromArgb(200, 0, 255, 0));
            ColorRanges.Add(c);
            c = new ColorRange(85, 100, Color.FromArgb(200, 0, 128, 0));
            ColorRanges.Add(c);
        }

        public List<ColorRange> ColorRanges { get; set; } = new List<ColorRange>();

        public RadiusGridDataInfo RadiusDataSet { get; set; } = new RadiusGridDataInfo();
        public RadiusGridDataInfo CellRadiusDataSet { get; set; } = new RadiusGridDataInfo();
        public RadiusGridItem CurSelGrid { get; set; }

        public override void Draw(System.Drawing.Rectangle clientRect, System.Drawing.Rectangle updateRect, System.Drawing.Graphics graphics)
        {
            if (!IsVisible)
            {
                return;
            }
            Rectangle inflatedRect = new Rectangle(updateRect.X, updateRect.Y, updateRect.Width, updateRect.Height);
            inflatedRect.Inflate(50, 50);
            DbRect dRect;
            Map.FromDisplay(updateRect, out dRect);

            RectangleF? size = null;

            if (RadiusDataSet != null)
            {
                foreach (RadiusGridItem grid in RadiusDataSet.Grids)
                {
                    if (grid.Bounds.Within(dRect))
                    {
                        drawGrid(graphics, grid, ref size);
                    }
                }
            }

            if(CellRadiusDataSet != null)
            {
                foreach (RadiusGridItem grid in CellRadiusDataSet.Grids)
                {
                    if (grid.Bounds.Within(dRect))
                    {
                        drawGrid(graphics, grid, ref size);
                    }
                }
            }
                
        }

        private void drawGrid(Graphics graphics, GridUnitBase grid, ref RectangleF? size)
        {
            PointF pointLt;
            this.Map.ToDisplay(new DbPoint(grid.LTLng, grid.LTLat), out pointLt);
            if (size == null)
            {
                DbPoint brPoint = new DbPoint(grid.BRLng, grid.BRLat);
                PointF pointBr;
                this.Map.ToDisplay(brPoint, out pointBr);
                size = new RectangleF(pointLt.X, pointLt.Y, pointBr.X - pointLt.X, pointBr.Y - pointLt.Y);
            }
            RectangleF rect = new RectangleF(pointLt.X, pointLt.Y, ((RectangleF)size).Width, ((RectangleF)size).Height);
            graphics.FillRectangle(new SolidBrush(getGridColor(grid)), rect);
            if ((grid as RadiusGridItem).CellName != "")//属于小区的栅格显示红色边框
            {
                rect.Inflate(1.0f, 1.0f);
                graphics.DrawRectangle(new Pen(Color.Red), rect.X, rect.Y, rect.Width, rect.Height);
            }
        }

        private Color getGridColor(GridUnitBase grid)
        {
            for (int i = 0; i < ColorRanges.Count; i++)
            {
                ColorRange rng = ColorRanges[i];
                
                RadiusGridItem ragGrid = grid as RadiusGridItem;
                if (rng.minValue <= ragGrid.Coverage90
                && (i == ColorRanges.Count - 1
                ? ragGrid.Coverage90 <= rng.maxValue
                : ragGrid.Coverage90 < rng.maxValue))
                {
                    return rng.color;
                }

                if (rng.minValue <= ragGrid.Coverage94
                    && (i == ColorRanges.Count - 1
                    ? ragGrid.Coverage94 <= rng.maxValue
                    : ragGrid.Coverage94 < rng.maxValue))
                {
                    return rng.color;
                }
                
            }
            return Color.Empty;
        }


    }
}
