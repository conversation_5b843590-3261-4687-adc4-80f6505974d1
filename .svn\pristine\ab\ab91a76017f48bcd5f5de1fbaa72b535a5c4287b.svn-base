using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Util;

namespace MasterCom.RAMS.Func
{
    public partial class CellPerformanceInfoForm : Form
    {
        public CellPerformanceInfoForm(ICollection<CellPerformanceInfo> cellPerformanceInfos)
        {
            InitializeComponent();
            foreach (string columnName in CellPerformanceInfo.ColumnNames)
            {
                dataGridView.Columns.Add(columnName, columnName);
            }
            this.cellPerformanceInfos = new CellPerformanceInfo[cellPerformanceInfos.Count];
            cellPerformanceInfos.CopyTo(this.cellPerformanceInfos, 0);
            dataGridView.RowCount = this.cellPerformanceInfos.Length;
        }

        private void dataGridView_CellValueNeeded(object sender, DataGridViewCellValueEventArgs e)
        {
            if (e.RowIndex < 0 || e.RowIndex >= dataGridView.RowCount || e.ColumnIndex < 0 || e.ColumnIndex >= dataGridView.ColumnCount)
            {
                return;
            }
            e.Value = cellPerformanceInfos[e.RowIndex].ColumnInfo[e.ColumnIndex];
        }

        private void Export2ExcelToolStripMenuItem_Click(object sender, EventArgs e)
        {
            ExcelOperator.ByTxtExcel(dataGridView);
        }

        private void CellPerformanceInfoForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (e.CloseReason == CloseReason.UserClosing)
            {
                e.Cancel = true;
                this.Visible = false;
            }
        }

        private CellPerformanceInfo[] cellPerformanceInfos;
    }
}