﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.MTGis;
using MasterCom.RAMS.Func.SystemSetting;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using MasterCom.RAMS.UserMng;

namespace MasterCom.RAMS.ZTFunc
{
    public class LTEUnKnownDisturbAnaByRegion : DIYAnalyseByFileBackgroundBase
    {
        LTEUnknownDisturbCondition disturbenceCondition = null;
        protected bool saveTestPoints = true;
        protected static readonly object lockObj = new object();
        private static LTEUnKnownDisturbAnaByRegion intance = null;
        List<LTEUnknownDisturbInfo> disturbenceList = null;
        public static LTEUnKnownDisturbAnaByRegion GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new LTEUnKnownDisturbAnaByRegion();
                    }
                }
            }
            return intance;
        }
        protected LTEUnKnownDisturbAnaByRegion()
            : base(MainModel.GetInstance())
        {
            init();
        }
        protected void init()
        {
            if (intance != null)
            {
                return;
            }
            ServiceTypes.Clear();
#if LT
            carrierID = CarrierType.ChinaUnicom;
#else
            carrierID = CarrierType.ChinaMobile;
#endif
        }
        public override string Name
        {
            get { return "不明干扰(按区域)"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22087, "分析");
        }
        protected override void fireShowForm()
        {
            if (disturbenceList.Count == 0)
            {
                MessageBox.Show("没有符合条件的信息！");
                return;
            }
            LTEUnknownDisturbInfoForm frm = MainModel.GetInstance().GetObjectFromBlackboard(typeof(LTEUnknownDisturbInfoForm)) as LTEUnknownDisturbInfoForm;
            if (frm == null || frm.IsDisposed)
            {
                frm = new LTEUnknownDisturbInfoForm(MainModel);
            }
            frm.FillData(disturbenceList);
            frm.Owner = MainModel.MainForm;
            frm.Visible = true;
            frm.BringToFront();
        }
        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                saveTestPoints = false;
                return true;
            }
            LTEUnknownDisturbDlg dlg = new LTEUnknownDisturbDlg(disturbenceCondition);
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                disturbenceCondition = dlg.GetCondition();
                return true;
            }
            return false;
        }
        protected override void clearDataBeforeAnalyseFiles()
        {
            disturbenceList = new List<LTEUnknownDisturbInfo>();
        }
        protected virtual float? getRsrp(TestPoint tp)
        {
            return (float?)tp["lte_RSRP"];
        }
        protected virtual float? getSinr(TestPoint tp)
        {
            return (float?)tp["lte_SINR"];
        }
        protected virtual float? getNMaxRsrp(TestPoint tp)
        {
            return (float?)tp["lte_NCell_RSRP", 0];
        }
        protected override void doStatWithQuery()
        {
            List<TestPoint> testPointList = MainModel.DTDataManager.FileDataManagers[0].TestPoints;
            LTEUnknownDisturbInfo disturbenceCover = null;
            TestPoint prePoint = null;//前一点
            for (int i = 0; i < testPointList.Count; i++)
            {
                TestPoint testPoint = testPointList[i];
                if (isValidTestPoint(testPoint))
                {
                    disturbenceCover = getLTEUnknownDisturbInfo(testPointList, disturbenceCover, prePoint, i, testPoint);
                    prePoint = testPoint;
                }
                else//区域外的采样点
                {
                    saveWeakCoverInfo(disturbenceCover);
                    disturbenceCover = null;//重置
                }
            }
        }

        private LTEUnknownDisturbInfo getLTEUnknownDisturbInfo(List<TestPoint> testPointList, LTEUnknownDisturbInfo disturbenceCover, TestPoint prePoint, int i, TestPoint testPoint)
        {
            float? sinr = getSinr(testPoint);
            float? rsrp = getRsrp(testPoint);
            float? nMaxRsrp = getNMaxRsrp(testPoint);
            TPInfo tpInfo = new TPInfo(sinr, rsrp, nMaxRsrp);
            if (!disturbenceCondition.IsMatchIndicator(tpInfo.Rsrp, tpInfo.Sinr, tpInfo.NMaxRsrp))//先作指标的判断
            {
                saveWeakCoverInfo(disturbenceCover);
                disturbenceCover = null;
            }
            else
            {
                if (disturbenceCover == null)
                {
                    disturbenceCover = new LTEUnknownDisturbInfo();
                    addWeakCoverInfo(testPointList, disturbenceCover, i, testPoint, tpInfo, 0);
                }
                else
                {
                    double dis = prePoint.Distance2(testPoint.Longitude, testPoint.Latitude);
                    if (disturbenceCondition.IsMatchIndicator(tpInfo.Rsrp, tpInfo.Sinr, tpInfo.NMaxRsrp))
                    {
                        addWeakCoverInfo(testPointList, disturbenceCover, i, testPoint, tpInfo, dis);
                    }
                    else
                    {//两采样点距离不符合，该点开始新的路段
                        saveWeakCoverInfo(disturbenceCover);
                        disturbenceCover = new LTEUnknownDisturbInfo();
                        disturbenceCover.AddTestPoint(testPoint, tpInfo, 0);
                    }
                }
            }

            return disturbenceCover;
        }

        private void addWeakCoverInfo(List<TestPoint> testPointList, LTEUnknownDisturbInfo disturbenceCover, int i, TestPoint testPoint, TPInfo tpInfo, double dis)
        {
            //符合两采样点之间的距离门限
            disturbenceCover.AddTestPoint(testPoint, tpInfo, dis);
            if (i == testPointList.Count - 1)//最后一采样点
            {
                saveWeakCoverInfo(disturbenceCover);
            }
        }

        private void saveWeakCoverInfo(LTEUnknownDisturbInfo info)
        {
            if (info == null
                || !disturbenceCondition.checkStayDistance(info.StayDistance)
                || !disturbenceCondition.CheckStayTime(info.StaySecond))
            {//不符合 最小持续距离 or 时间
                return;
            }
            //save 2 list
            if (disturbenceList == null)
            {
                disturbenceList = new List<LTEUnknownDisturbInfo>();
            }
            info.SN = disturbenceList.Count + 1;
            info.FindRoadName();
 
            disturbenceList.Add(info);
        }

        public class TPInfo
        {
            public TPInfo(float? sinr, float? rsrp, float? nMaxRsrp)
            {
                Sinr = (float)sinr;
                Rsrp = (float)rsrp;
                NMaxRsrp = (float)nMaxRsrp;
            }

            public float Sinr { get; set; }
            public float Rsrp { get; set; }
            public float NMaxRsrp { get; set; }
        }
    }

    public class LTEUnKnownDisturbAnaByRegion_FDD : LTEUnKnownDisturbAnaByRegion
    {
        private static LTEUnKnownDisturbAnaByRegion_FDD instance = null;
        public static new LTEUnKnownDisturbAnaByRegion_FDD GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new LTEUnKnownDisturbAnaByRegion_FDD();
                    }
                }
            }
            return instance;
        }

        public override string Name
        {
            get { return "不明干扰LTE_FDD(按区域)"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 26000, 26031, "分析");
        }

        protected override float? getRsrp(TestPoint tp)
        {
            return (float?)tp["lte_fdd_RSRP"];
        }
        protected override float? getSinr(TestPoint tp)
        {
            return (float?)tp["lte_fdd_SINR"];
        }
        protected override float? getNMaxRsrp(TestPoint tp)
        {
            return (float?)tp["lte_fdd_NCell_RSRP", 0];
        }
    }
}
