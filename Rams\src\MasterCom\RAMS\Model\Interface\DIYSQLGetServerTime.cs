using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;

namespace MasterCom.RAMS.Model.Interface
{
    public class DIYSQLGetServerTime : DIYSQLBase
    {
        private int time;
        public int ServerTime
        {
            get { return time; }
        }
        public DateTime ServerDateTime
        {
            get { return JavaDate.GetDateTimeFromMilliseconds(time * 1000L); }
        }
        public int ServerDay
        {
            get { return ServerDateTime.Day; }
        }
        public int ServerMonth
        {
            get { return ServerDateTime.Month; }
        }
        public DIYSQLGetServerTime(MainModel mainModel)
            : base(mainModel)
        {
        }
        protected override string getSqlTextString()
        {
            return "select datediff(ss, '1970-01-01 08:00',getDate())";
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[1];
            rType[0] = E_VType.E_Int;
            return rType;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    time = package.Content.GetParamInt();
                    //do your code here
                }
                else if (package.Content.Type == Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                if (Math.Log(index++) * (100000 / 10000) > WaitBox.ProgressPercent)
                {
                    progress++;
                    if (progress > 95)
                    {
                        progress = 5;
                        index = 0;
                    }
                    if (progress % 5 == 0)
                    {
                        WaitBox.ProgressPercent = progress;
                    }
                }
            }
        }

        public override string Name
        {
            get { return "DIYSQLGetServerTime"; }
        }
    }
}
