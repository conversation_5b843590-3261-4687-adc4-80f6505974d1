﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using MasterCom.RAMS.Model.Interface;
using DevExpress.XtraCharts;
using System.IO;
using MasterCom.RAMS.Util;
using Excel = Microsoft.Office.Interop.Excel;
using DevExpress.XtraEditors;

namespace MasterCom.RAMS.Func.PopShow
{
    public partial class KPIInfoPanel_ng : UserControl, PopShowPanelInterface
    {
        //第一命令字：config
        public const byte REQTYPE_CONFIG_KPI_TABLE = 0x2b;  //REQUEST
        public const byte RESTYPE_CONFIG_KPI_TABLE = 0x2b;

        //第一命令字：查询    启动预读
        public const byte REQTYPE_TABLE_DATA_INFO = 0x83; //REQUEST 
        public const byte RESTYPE_TABLE_DATA_INFO = 0x83; 
        private MainModel MainModel;
        public Dictionary<string, Object> colorRangeDic { get; set; }
        public Dictionary<string, AlarmCfgItem> alarmCfgDic { get; set; }

        public static KPIInfoPanel_ng theKPIInfoPanel { get; set; }

        AxisRange defaultRangeX;
        AxisRange defaultRangeY;
        public KPIInfoPanel_ng()
        {
            InitializeComponent();
#if PopShow_KPI_Color
            btnColor.Visible = true;
#endif
            initShowInfo();
            theKPIInfoPanel = this;
            if (chartControl.Diagram != null)
            {
                defaultRangeX = (AxisRange)((XYDiagram)chartControl.Diagram).AxisX.Range.Clone();
                defaultRangeY = (AxisRange)((XYDiagram)chartControl.Diagram).AxisY.Range.Clone();
            }

            curRetDataDic = new Dictionary<string, KPIPopTable>();
        }

        private void initShowInfo()
        {
            cbxShowType.Items.Add("最近一周");
            cbxShowType.Items.Add("最近一月");
            cbxShowType.Items.Add("按周");
            cbxShowType.Items.Add("按月");
            cbxShowType.Items.Add("按天");
            cbxShowType.SelectedIndex = 0;

        }

        #region PopShowPanelInterface 成员

        public void RunQuery(BackgroundWorker worker, TaskInfo task)
        {
           // PrepareTable();
            isProvUser = MainModel.User.DBID == -1;
            curDrillLevel = 0;
            Dictionary<string, KPIPopTable> entryHeaderDic = queryPopEntry(worker);
            foreach (string tbkey in entryHeaderDic.Keys)
            {
                KPIPopTable hdUnit = entryHeaderDic[tbkey];
                hdUnit.Sort();
                hdUnit.initFinderDic();
                List<KPIResultInfo> resultList = queryResultFromHeader(worker, hdUnit, MainModel.User.DBID);
                hdUnit.cityDataResult = buildCityStruct(resultList);
            }
            task.retResultInfo = entryHeaderDic;
           
        }

        private Dictionary<string, List<KPIResultInfo>> buildCityStruct(List<KPIResultInfo> resultList)
        {
            Dictionary<string, List<KPIResultInfo>> cityDic = new Dictionary<string, List<KPIResultInfo>>();
            foreach(KPIResultInfo info in resultList)
            {
                string strcity = DistrictManager.GetInstance().getDistrictName(info.dbid);
                List<KPIResultInfo> list = null;
                if(!cityDic.TryGetValue(strcity,out list))
                {
                    list = new List<KPIResultInfo>();
                    cityDic[strcity] = list;
                }
                list.Add(info);
            }
            return cityDic;
        }

        private List<KPIResultInfo> queryResultFromHeader(BackgroundWorker worker, KPIPopTable hdUnit,int dbid)
        {
            List<KPIResultInfo> retList = new List<KPIResultInfo>();
            ClientProxy clientProxy = new ClientProxy();

            string username = MainModel.User.LoginName;
            string password = MainModel.User.Password;
            int districtID = MainModel.DistrictID;
#if Guangdong
            //if (districtID == 14 || districtID == 15 || districtID == 22)
            {
                username = MainModel.MainDbUser.LoginName;
                password = MainModel.MainDbUser.Password;
                districtID = 2;
            }
#endif
            if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, username, password, districtID) != ConnectResult.Success)
            {
                worker.ReportProgress(99, "连接服务器端出错！");
                return retList;
            }
            try
            {
                Package package = clientProxy.Package;
                package.Command = Command.InfoQuery;
                package.SubCommand = SubCommand.Request;
                package.Content.Type = REQTYPE_TABLE_DATA_INFO;
                package.Content.PrepareAddParam();
                package.Content.AddParam(dbid);

                package.Content.AddParam(hdUnit.tablename);
                clientProxy.Send();
                while (true)
                {
                    clientProxy.Recieve();
                    if (package.Content.Type == RESTYPE_TABLE_DATA_INFO)
                    {
                        package.Content.PrepareGetParam();
                        KPIResultInfo retItem = hdUnit.ReadResultItemFrom(package.Content);
                        retList.Add(retItem);
                    }
                    else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                    {
                        break;
                    }
                }
                return retList;
            }

            finally
            {
                clientProxy.Close();
            }

        }
        private Dictionary<string, KPIPopTable> queryPopEntry(BackgroundWorker worker)
        {
#if PopShow_KPI_Color
            DiySqlPopKpiColor kpiColorQeruyTask = new DiySqlPopKpiColor(MainModel);
            kpiColorQeruyTask.Query();
            colorRangeDic = kpiColorQeruyTask.colorRangeDic;

            DiySqlPopKpiAlarmCfg kpiAlarmCfgrQeruyTask = new DiySqlPopKpiAlarmCfg(MainModel);
            kpiAlarmCfgrQeruyTask.Query();
            alarmCfgDic = kpiAlarmCfgrQeruyTask.alarmCfgDic;
#endif

            Dictionary<string, KPIPopTable> entryDicList = new Dictionary<string, KPIPopTable>();
            ClientProxy clientProxy = new ClientProxy();
            string username = MainModel.User.LoginName;
            string password = MainModel.User.Password;
            int dbid = MainModel.DistrictID;
#if Guangdong
            //if (dbid == 14 || dbid == 15 || dbid == 22)
            {
                username = MainModel.MainDbUser.LoginName;
                password = MainModel.MainDbUser.Password;
                dbid = 2;
            }
#endif

            if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, username, password, dbid) != ConnectResult.Success)
            {
                worker.ReportProgress(99,"连接服务器端出错！");
                return entryDicList;
            }
            try
            {
                Package package = clientProxy.Package;
                // get cellDefine;
                package.Command = Command.CellConfigManage;
                package.SubCommand = SubCommand.Request;
                package.Content.Type = REQTYPE_CONFIG_KPI_TABLE;
                package.Content.PrepareAddParam();
                clientProxy.Send();
                while (true)
                {
                    clientProxy.Recieve();
                    if (package.Content.Type == RESTYPE_CONFIG_KPI_TABLE)
                    {
                        package.Content.PrepareGetParam();
                        PopKPIEntryItem entry = new PopKPIEntryItem();
                        entry.Fill(package.Content);
                        addEntryList(entryDicList, entry);
                    }
                    else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                    {
                        break;
                    }
                }
                return entryDicList;
            }
            finally
            {
                clientProxy.Close();
            }
            

        }

        private void addEntryList(Dictionary<string, KPIPopTable> entryDicList, PopKPIEntryItem entry)
        {
            if (entry.strtablename == "tb_popblackblock"
                || entry.strtablename == "tb_pop_esinsight"
                || entry.strtablename == "tb_pop_compbench_status"
                || entry.strtablename == "tb_pop_compbench_unit"/*|| entry.strtablename.IndexOf("_agent_")!=-1*/)//特殊的供其它程序使用的，不在KPI显示处显示
            {
                //
            }
            else
            {
                KPIPopTable headerUnit = null;
                if (!entryDicList.TryGetValue(entry.strtablename, out headerUnit))
                {
                    headerUnit = new KPIPopTable();
                    headerUnit.tablename = entry.strtablename;
                    entryDicList[headerUnit.tablename] = headerUnit;
                }
#if PopShow_KPI_Color
                            string key = entry.strtablename + entry.strcolname;
                            if (kpiColorQeruyTask.colorRangeDic.ContainsKey(key))
                            {
                                entry.colorLst = kpiColorQeruyTask.colorRangeDic[key] as List<DTParameterRangeColor>;
                            }
#endif
                headerUnit.entryList.Add(entry);
            }
        }
        #endregion

        #region PopShowPanelInterface 成员

        public Dictionary<string, KPIPopTable> curRetDataDic { get; set; }
        public void FireFreshShowData(TaskInfo task)
        {
            if (!(task.retResultInfo is Dictionary<string, KPIPopTable>))
            {
                curRetDataDic.Clear();
            }
            else
            {
                curRetDataDic = task.retResultInfo as Dictionary<string, KPIPopTable>;
            }
            cbxReportSel.Items.Clear();
            foreach(KPIPopTable popTable in curRetDataDic.Values)
            {
                if (popTable.tablename.Contains("_agent_"))
                {
                    continue;
                }
                cbxReportSel.Items.Add(popTable);
            }
            if(cbxReportSel.Items.Count>0)
            {
                cbxReportSel.SelectedIndex = 0;
            }
        }

        #endregion

        #region PopShowPanelInterface 成员


        public void SetMainModal(MasterCom.RAMS.Model.MainModel mm, WelcomForm welcomform)
        {
            this.MainModel = mm;
        }

        #endregion

        private void cbxReportSel_SelectedIndexChanged(object sender, EventArgs e)
        {
            if(isProvUser)
            {
                curDrillLevel = 0;
                cityName = "";
            }
            refreshShowReport(true);
        }

        private void cbxShowType_SelectedIndexChanged(object sender, EventArgs e)
        {
            string selShowType = cbxShowType.SelectedItem as string;
            if(selShowType==null)
            {
                return;
            }
            refreshShowReport(false);
        }
        private void refreshShowReport(bool resetContent)
        {
            btnLevel0.Visible = isProvUser;

            dataGridView.Columns.Clear();
            KPIPopTable kpiPopTable = cbxReportSel.SelectedItem as KPIPopTable;
            string selShowType = cbxShowType.SelectedItem as string;
            if(kpiPopTable!=null && selShowType!=null)
            {
                FinalShowResult showRet = parseShowFromTable(kpiPopTable,selShowType);
                if(resetContent)
                {
                    Dictionary<string, bool> namesDic = getNameList(showRet);
                    cbxContentType.Properties.Items.Clear();
                    cbxContentType.Properties.Items.Add("(全部)");
                    foreach (string nm in namesDic.Keys)
                    {
                        cbxContentType.Properties.Items.Add(nm);
                    }
                    cbxContentType.Text = "(全部)";
                }
                showInGrid(showRet);
            }
        }

        private Dictionary<string, bool> getNameList(FinalShowResult showRet)
        {
            Dictionary<string, bool> ret = new Dictionary<string, bool>();
            foreach (List<object> vList in showRet.dataRows)
            {
                ret[vList[1] as string] = true;
            }
            return ret;
        }
        /// <summary>
        /// 是否当前登录的是省用户/多地市用户
        /// </summary>
        private bool isProvUser = false;
        /// <summary>
        /// 当前所选地市
        /// </summary>
        private string cityName = "";
        private int curDrillLevel = 0;//0 全省汇总，1 全省  1地市片区

        private FinalShowResult parseShowFromTable(KPIPopTable kpiPopTable, string selShowType)
        {
            FinalShowResult sRet = new FinalShowResult();
            checkedCbxMonth.Visible = true;
            kpiPopTable.reloadDataResultByLevel(isProvUser, cityName,"");
            if (selShowType == "按天")
            {
                labelMonth.Visible = false;
                checkedCbxMonth.Visible = false;
                sRet = prepareShowByDay(kpiPopTable);
            }
            else if (selShowType == "按月")
            {
                checkedCbxMonth.Visible = true;
                sRet = prepareShowByMonth(kpiPopTable);
            }
            else if (selShowType == "按周")
            {
                labelMonth.Visible = false;
                checkedCbxMonth.Visible = false;
                sRet = prepareShowByWeek(kpiPopTable);
            }
            else if(selShowType == "最近一月")
            {
                labelMonth.Visible = false;
                checkedCbxMonth.Visible = false;
                KPIResultInfo notUsed = null;
                sRet = prepareShowByLastMonth(kpiPopTable, out notUsed);
            }
            else if (selShowType == "最近一周")
            {
                labelMonth.Visible = false;
                checkedCbxMonth.Visible = false;
                KPIResultInfo notUsed = null;
                sRet = prepareShowByLastWeek(kpiPopTable, out notUsed);
            }
            return sRet;
        }

        private FinalShowResult prepareShowByDay(KPIPopTable kpiPopTable)
        {
            FinalShowResult sRet = setResultBase(kpiPopTable);
            foreach (KPIResultInfo retInfo in kpiPopTable.dataResult)
            {
                List<object> objList = new List<object>();
                DateTime stimeDate = JavaDate.GetDateTimeFromMilliseconds(retInfo.stime * 1000L);
                objList.Add(stimeDate.ToString("yyyy.MM.dd"));
                objList.Add(retInfo.strname);
                for (int i = 0; i < retInfo.valueList.Count && i < sRet.columnNames.Count - 2; i++)
                {
                    object v = retInfo.valueList[i];
                    objList.Add(v);
                }
                sRet.dataRows.Add(objList);
            }
            return sRet;
        }

        private FinalShowResult setResultBase(KPIPopTable kpiPopTable)
        {
            FinalShowResult sRet = new FinalShowResult();
            sRet.columnNames.Add("时间");
            sRet.columnNames.Add("名称");
            for (int i = 4; i < kpiPopTable.entryList.Count; i++)
            {
                PopKPIEntryItem entry = kpiPopTable.entryList[i];
                if (entry.strcolname.IndexOf("_base") == -1)
                {
                    sRet.columnNames.Add(entry.strcoldesc);
                }
            }

            return sRet;
        }

        #region LastWeek
        private FinalShowResult prepareShowByLastWeek(KPIPopTable kpiPopTable, out KPIResultInfo totalResult)
        {
            totalResult = null;
            if (kpiPopTable.ToString() == "GSM语音业务报表_汇总")
            {
                return getGsmTotalResLastWeek(kpiPopTable);
            }
            else
            {
                return getTotalResLastWeek(kpiPopTable, out totalResult);
            }
        }

        private FinalShowResult getGsmTotalResLastWeek(KPIPopTable kpiPopTable)
        {
            FinalShowResult sRet = setResultBase(kpiPopTable);
            KPIResultInfo total = null;//abc***
            foreach (KPIPopTable tableData in curRetDataDic.Values)
            {
                if (tableData.ToString().IndexOf("GSM语音业务报表_") == 0 && tableData.ToString() != "GSM语音业务报表_汇总")
                {
                    //
                    tableData.reloadDataResultByLevel(isProvUser, cityName, "");
                    total = dealGsmTotalLastWeek(kpiPopTable, sRet, total, tableData);
                }
            }
            if (total != null)
            {
                List<object> objTotalList = new List<object>();
                objTotalList.Add(getWeekStr(total.stime));
                objTotalList.Add("汇总");
                for (int i = 0; i < total.valueList.Count && i < sRet.columnNames.Count - 2; i++)
                {
                    object v = total.valueList[i];
                    objTotalList.Add(v);
                }
                sRet.dataRows.Add(objTotalList);
            }

            return sRet;
        }

        private FinalShowResult getTotalResLastWeek(KPIPopTable kpiPopTable, out KPIResultInfo totalResult)
        {
            FinalShowResult sRet = setResultBase(kpiPopTable);
            Dictionary<string, KPIResultInfo> retDic = new Dictionary<string, KPIResultInfo>();
            foreach (KPIResultInfo rinfo in kpiPopTable.dataResult)
            {
                string key = rinfo.dbid + ":" + rinfo.strname + ":" + getWeekStr(rinfo.stime);
                KPIResultInfo ginfo = null;
                if (!retDic.TryGetValue(key, out ginfo))
                {
                    retDic[key] = rinfo.copyInstance();
                    retDic[key].stime = getWeekBeginTime(rinfo.stime);
                }
                else
                {
                    ginfo.Gather(rinfo, kpiPopTable);
                }
            }
            int maxstime = 0;
            foreach (KPIResultInfo ginfo in retDic.Values)
            {
                if (ginfo.stime > maxstime)
                {
                    maxstime = ginfo.stime;
                }
            }
            totalResult = dealTotalResultLastWeek(kpiPopTable, sRet, retDic, maxstime);

            //==========//abc***
            return sRet;
        }

        private KPIResultInfo dealGsmTotalLastWeek(KPIPopTable kpiPopTable, FinalShowResult sRet, KPIResultInfo total, KPIPopTable tableData)
        {
            KPIResultInfo kRi = null;
            FinalShowResult projResult = prepareShowByLastWeek(tableData, out kRi);
            if (projResult.dataRows.Count > 0)
            {
                string nameColValue = projResult.dataRows[projResult.dataRows.Count - 1][1].ToString();
                if (nameColValue == "汇总")
                {
                    List<object> retGatherOfProj = new List<object>();
                    retGatherOfProj.AddRange(projResult.dataRows[projResult.dataRows.Count - 1]);
                    retGatherOfProj[1] = tableData.ToString();
                    sRet.dataRows.Add(retGatherOfProj);
                    if (total == null)
                    {
                        total = kRi;
                    }
                    else
                    {
                        total.Gather(kRi, kpiPopTable);
                    }
                }
            }

            return total;
        }

        private KPIResultInfo dealTotalResultLastWeek(KPIPopTable kpiPopTable, FinalShowResult sRet, Dictionary<string, KPIResultInfo> retDic, int maxstime)
        {
            KPIResultInfo totalResult;
            KPIResultInfo total = null;//abc***
            foreach (KPIResultInfo ginfo in retDic.Values)
            {
                if (ginfo.stime == maxstime)
                {
                    List<object> objList = new List<object>();
                    objList.Add(getWeekStr(ginfo.stime));
                    objList.Add(ginfo.strname);
                    for (int i = 0; i < ginfo.valueList.Count && i < sRet.columnNames.Count - 2; i++)
                    {
                        object v = ginfo.valueList[i];
                        objList.Add(v);
                    }
                    sRet.dataRows.Add(objList);
                    if (total == null)
                    {
                        total = ginfo.copyInstance();
                    }
                    else
                    {
                        total.Gather(ginfo, kpiPopTable);//abc***
                    }
                }
            }
            totalResult = total;
            //=======//abc***
            if (total != null)
            {
                List<object> objTotalList = new List<object>();
                objTotalList.Add(getWeekStr(maxstime));
                objTotalList.Add("汇总");
                for (int i = 0; i < total.valueList.Count && i < sRet.columnNames.Count - 2; i++)
                {
                    object v = total.valueList[i];
                    objTotalList.Add(v);
                }
                sRet.dataRows.Add(objTotalList);
            }

            return totalResult;
        }
        #endregion

        private FinalShowResult prepareShowByWeek(KPIPopTable kpiPopTable)
        {
            FinalShowResult sRet = setResultBase(kpiPopTable);
            KPIPopTable gatheredTable = new KPIPopTable();
            gatheredTable.entryList.AddRange(kpiPopTable.entryList);
            Dictionary<string, KPIResultInfo> retDic = new Dictionary<string, KPIResultInfo>();
            foreach (KPIResultInfo rinfo in kpiPopTable.dataResult)
            {
                string key = rinfo.dbid + ":" + rinfo.strname + ":" + getWeekStr(rinfo.stime);
                KPIResultInfo ginfo = null;
                if (!retDic.TryGetValue(key, out ginfo))
                {
                    retDic[key] = rinfo.copyInstance();
                }
                else
                {
                    ginfo.Gather(rinfo, kpiPopTable);
                }
            }

            foreach (KPIResultInfo ginfo in retDic.Values)
            {
                List<object> objList = new List<object>();
                objList.Add(getWeekStr(ginfo.stime));
                objList.Add(ginfo.strname);
                for (int i = 0; i < ginfo.valueList.Count && i < sRet.columnNames.Count - 2; i++)
                {
                    object v = ginfo.valueList[i];
                    objList.Add(v);
                }
                sRet.dataRows.Add(objList);
            }
            return sRet;
        }

        #region LastMonth
        private FinalShowResult prepareShowByLastMonth(KPIPopTable kpiPopTable, out KPIResultInfo totalResult)
        {
            totalResult = null;
            if (kpiPopTable.ToString() == "GSM语音业务报表_汇总")
            {
                return getGsmTotalResLastMonth(kpiPopTable);
            }
            else
            {
                return getTotalResLastMonth(kpiPopTable, out totalResult);
            }
        }

        private FinalShowResult getGsmTotalResLastMonth(KPIPopTable kpiPopTable)
        {
            FinalShowResult sRet = setResultBase(kpiPopTable);
            KPIResultInfo total = null;//abc***
            foreach (KPIPopTable tableData in curRetDataDic.Values)
            {
                if (tableData.ToString().IndexOf("GSM语音业务报表_") == 0 && tableData.ToString() != "GSM语音业务报表_汇总")
                {
                    //
                    tableData.reloadDataResultByLevel(isProvUser, cityName, "");
                    total = dealGsmTotalLastMonth(kpiPopTable, sRet, total, tableData);
                }
            }
            if (total != null)
            {
                List<object> objTotalList = new List<object>();
                DateTime tstimeDate = JavaDate.GetDateTimeFromMilliseconds(1000L * total.stime);
                objTotalList.Add(tstimeDate.ToString("yyyy-MM"));
                objTotalList.Add("汇总");
                for (int i = 0; i < total.valueList.Count && i < sRet.columnNames.Count - 2; i++)
                {
                    object v = total.valueList[i];
                    objTotalList.Add(v);
                }
                sRet.dataRows.Add(objTotalList);
            }

            return sRet;
        }

        private KPIResultInfo dealGsmTotalLastMonth(KPIPopTable kpiPopTable, FinalShowResult sRet, KPIResultInfo total, KPIPopTable tableData)
        {
            KPIResultInfo kRi = null;
            FinalShowResult projResult = prepareShowByLastMonth(tableData, out kRi);
            if (projResult.dataRows.Count > 0)
            {
                string nameColValue = projResult.dataRows[projResult.dataRows.Count - 1][1].ToString();
                if (nameColValue == "汇总")
                {
                    List<object> retGatherOfProj = new List<object>();
                    retGatherOfProj.AddRange(projResult.dataRows[projResult.dataRows.Count - 1]);
                    retGatherOfProj[1] = tableData.ToString();
                    sRet.dataRows.Add(retGatherOfProj);
                    if (total == null)
                    {
                        total = kRi;
                    }
                    else
                    {
                        total.Gather(kRi, kpiPopTable);
                    }
                }
            }

            return total;
        }

        private FinalShowResult getTotalResLastMonth(KPIPopTable kpiPopTable, out KPIResultInfo totalResult)
        {
            FinalShowResult sRet = setResultBase(kpiPopTable);
            KPIPopTable gatheredTable = new KPIPopTable();
            gatheredTable.entryList.AddRange(kpiPopTable.entryList);
            Dictionary<string, KPIResultInfo> retDic = new Dictionary<string, KPIResultInfo>();
            foreach (KPIResultInfo rinfo in kpiPopTable.dataResult)
            {
                string key = rinfo.dbid + ":" + rinfo.strname + ":" + getMonthStr(rinfo.stime);
                KPIResultInfo ginfo = null;
                if (!retDic.TryGetValue(key, out ginfo))
                {
                    retDic[key] = rinfo.copyInstance();
                    retDic[key].stime = getMonthBeginTime(rinfo.stime);
                }
                else
                {
                    ginfo.Gather(rinfo, kpiPopTable);
                }
            }
            int maxstime = 0;
            foreach (KPIResultInfo ginfo in retDic.Values)
            {
                if (ginfo.stime > maxstime)
                {
                    maxstime = ginfo.stime;
                }
            }
            totalResult = dealTotalResultLastMonth(kpiPopTable, sRet, retDic, maxstime);
            //==========//abc***
            return sRet;
        }

        private static KPIResultInfo dealTotalResultLastMonth(KPIPopTable kpiPopTable, FinalShowResult sRet, Dictionary<string, KPIResultInfo> retDic, int maxstime)
        {
            KPIResultInfo totalResult;
            KPIResultInfo total = null;//abc***
            foreach (KPIResultInfo ginfo in retDic.Values)
            {
                if (ginfo.stime == maxstime)
                {
                    List<object> objList = new List<object>();
                    DateTime stimeDate = JavaDate.GetDateTimeFromMilliseconds(ginfo.stime * 1000L);
                    objList.Add(stimeDate.ToString("yyyy-MM"));
                    objList.Add(ginfo.strname);
                    for (int i = 0; i < ginfo.valueList.Count && i < sRet.columnNames.Count - 2; i++)
                    {
                        object v = ginfo.valueList[i];
                        objList.Add(v);
                    }
                    sRet.dataRows.Add(objList);
                    if (total == null)
                    {
                        total = ginfo.copyInstance();
                    }
                    else
                    {
                        total.Gather(ginfo, kpiPopTable);//abc***
                    }

                }
            }
            totalResult = total;
            //=======//abc***
            if (total != null)
            {
                List<object> objTotalList = new List<object>();
                DateTime tstimeDate = JavaDate.GetDateTimeFromMilliseconds(1000L * maxstime);
                objTotalList.Add(tstimeDate.ToString("yyyy-MM"));
                objTotalList.Add("汇总");
                for (int i = 0; i < total.valueList.Count && i < sRet.columnNames.Count - 2; i++)
                {
                    object v = total.valueList[i];
                    objTotalList.Add(v);
                }
                sRet.dataRows.Add(objTotalList);
            }

            return totalResult;
        }
        #endregion

        private FinalShowResult prepareShowByMonth(KPIPopTable kpiPopTable)
        {
            checkedCbxMonth.Properties.Items.Clear();
            labelMonth.Visible = true;
            checkedCbxMonth.Visible = true;
            checkMonthState = false;
            FinalShowResult sRet = setResultBase(kpiPopTable);
            KPIPopTable gatheredTable = new KPIPopTable();
            gatheredTable.entryList.AddRange(kpiPopTable.entryList);
            Dictionary<string, KPIResultInfo> retDic = new Dictionary<string, KPIResultInfo>();
            Dictionary<string, string> monthDic = new Dictionary<string, string>();
            foreach(KPIResultInfo rinfo in kpiPopTable.dataResult)
            {
                string monthKey = getMonthStr(rinfo.stime);
                string key = rinfo.dbid + ":" + rinfo.strname + ":" + monthKey;
                KPIResultInfo ginfo = null;
                string monthInfo;
                if (!monthDic.TryGetValue(monthKey,out monthInfo))
                {
                    monthDic[monthKey] = monthKey;
                }
                if(!retDic.TryGetValue(key,out ginfo))
                {
                    retDic[key] = rinfo.copyInstance();
                }
                else
                {
                    ginfo.Gather(rinfo, kpiPopTable);
                }
            }

            foreach (string month in monthDic.Values)
            {
                checkedCbxMonth.Properties.Items.Add(month, true);//默认选中所有月份
            }

            foreach(KPIResultInfo ginfo in retDic.Values)
            {
                List<object> objList = new List<object>();
                DateTime stimeDate = JavaDate.GetDateTimeFromMilliseconds(ginfo.stime *1000L);
                objList.Add(stimeDate.ToString("yyyy-MM"));
                objList.Add(ginfo.strname);
                for (int i = 0; i < ginfo.valueList.Count && i < sRet.columnNames.Count - 2; i++)
                {
                    object v = ginfo.valueList[i];
                    objList.Add(v);
                }
                sRet.dataRows.Add(objList);
            }
            checkedCbxMonth.Properties.Tag = sRet;
            checkMonthState = true;
            return sRet;
        }

        private int getWeekBeginTime(int time)
        {
            DateTime dt = JavaDate.GetDateTimeFromMilliseconds(time * 1000L);
            DayOfWeek dweek = dt.DayOfWeek;
            int dayToMondy = 0;
            switch (dweek)
            {
                case DayOfWeek.Monday:
                    dayToMondy = 0;
                    break;
                case DayOfWeek.Tuesday:
                    dayToMondy = 1;
                    break;
                case DayOfWeek.Wednesday:
                    dayToMondy = 2;
                    break;
                case DayOfWeek.Thursday:
                    dayToMondy = 3;
                    break;
                case DayOfWeek.Friday:
                    dayToMondy = 4;
                    break;
                case DayOfWeek.Saturday:
                    dayToMondy = 5;
                    break;
                case DayOfWeek.Sunday:
                    dayToMondy = 6;
                    break;

            }
            DateTime dtbegin = dt.AddDays(-dayToMondy);
            dtbegin = (new DateTime(dtbegin.Year, dtbegin.Month, dtbegin.Day)).ToLocalTime();
            long seconds = JavaDate.GetMilliseconds(dtbegin)/1000;
            return (int)seconds;
        }

        private string getWeekStr(int time)
        {
            DateTime dt = JavaDate.GetDateTimeFromMilliseconds(time * 1000L);
            DayOfWeek dweek = dt.DayOfWeek;
            int dayToMondy = 0;
            switch(dweek)
            {
                case DayOfWeek.Monday:
                    dayToMondy =0;
                    break;
                case DayOfWeek.Tuesday:
                    dayToMondy = 1;
                    break;
                case DayOfWeek.Wednesday:
                    dayToMondy = 2;
                    break;
                case DayOfWeek.Thursday:
                    dayToMondy = 3;
                    break;
                case DayOfWeek.Friday:
                    dayToMondy = 4;
                    break;
                case DayOfWeek.Saturday:
                    dayToMondy = 5;
                    break;
                case  DayOfWeek.Sunday:
                    dayToMondy = 6;
                    break;

            }
            return dt.AddDays(-dayToMondy).ToString("yyyy.MM.dd_") + dt.AddDays(6 - dayToMondy).ToString("MM.dd");
        }
        private int getMonthBeginTime(int time)
        {
            DateTime dt = JavaDate.GetDateTimeFromMilliseconds(time * 1000L);
            DateTime dtbegin = new DateTime(dt.Year, dt.Month, 1).ToLocalTime();
            return (int)(JavaDate.GetMilliseconds(dtbegin)/1000);
        }

        private string getMonthStr(int time)
        {
            DateTime dt = JavaDate.GetDateTimeFromMilliseconds(time * 1000L);
            return dt.ToString("yyyy-MM");
        }

        private void showInGrid(FinalShowResult showRet)
        {
            dataGridView.Rows.Clear();
            dataGridView.Columns.Clear();
            for (int i = 0; i < showRet.columnNames.Count; i++)
            {
                string columnname = showRet.columnNames[i];
                dataGridView.Columns.Add("Column" + i, columnname);
            }
            dataGridView.Columns[0].SortMode = DataGridViewColumnSortMode.Programmatic;
            if(showRet.dataRows.Count>0)
            {
                string selShowType = cbxShowType.SelectedItem as string;
                KPIPopTable kpiPopTable = cbxReportSel.SelectedItem as KPIPopTable;
                string rptName = kpiPopTable.ToString();
                if (curDrillLevel == 0 && (selShowType == "最近一周" || selShowType == "最近一月") && rptName != "GSM语音业务报表_汇总")
                {
                    int indexRowAt = 0;
                    List<object> dataRow = showRet.dataRows[showRet.dataRows.Count-1];
                    AddRowToGrid(indexRowAt, dataRow);
                }
                else
                {
                    int indexRowAt = 0;
                    for (int r = 0; r < showRet.dataRows.Count; r++)
                    {
                        List<object> dataRow = showRet.dataRows[r];
                        indexRowAt = AddRowToGrid(indexRowAt, dataRow);
                    }
                }
                
            }
            dataGridView.Sort(dataGridView.Columns[0], ListSortDirection.Ascending);
            dataGridView.Columns[0].Frozen = true;
            dataGridView.Columns[1].Frozen = true;
        }

        private int AddRowToGrid(int indexRowAt, List<object> dataRow)
        {
            bool isAdd = false;

            if (checkedCbxMonth.Visible)
            {
                isAdd = judgeMonthAdded(dataRow, isAdd);
            }
            else
            {
                isAdd = true;
            }

            if (((cbxContentType.Text).Contains("(全部)") || (cbxContentType.Text).Contains((string)dataRow[1])) && isAdd)
            {
                dataGridView.Rows.Add(1);
                for (int c = 0; c < dataRow.Count; c++)
                {
                    object dv = dataRow[c];
                    if (dv is DateTime)
                    {
                        dataGridView.Rows[indexRowAt].Cells[c].Value = ((DateTime)dv).ToString("yyyy-MM-dd");
                    }
                    else
                    {
                        dataGridView.Rows[indexRowAt].Cells[c].Value = dataRow[c];
#if PopShow_KPI_Color                                
                                string selShowType = cbxShowType.SelectedItem as string;
                                if (selShowType.Contains("周"))
                                {
                                    KPIPopTable kpiPopTable = cbxReportSel.SelectedItem as KPIPopTable;
                                    string key = kpiPopTable.tablename;
                                    foreach (PopKPIEntryItem item in kpiPopTable.entryList)
                                    {
                                        if (item.strcoldesc == showRet.columnNames[c])
                                        {
                                            key += item.strcolname;
                                            break;
                                        }
                                    }
                                    if (colorRangeDic!=null && colorRangeDic.ContainsKey(key))
                                    {
                                        List<DTParameterRangeColor> colorRangeLst = colorRangeDic[key] as List<DTParameterRangeColor>;
                                        foreach (DTParameterRangeColor colorRange in colorRangeLst)
                                        {
                                            if (colorRange.Within((float)dataRow[c]))
                                            {
                                                dataGridView.Rows[indexRowAt].Cells[c].Style.BackColor = colorRange.Value;
                                                dataGridView.Rows[indexRowAt].Cells[c].ToolTipText = colorRange.DesInfo;

                                                break;
                                            }
                                        }
                                    }
                                }
#endif
                    }
                }
                indexRowAt++;
            }
            return indexRowAt;
        }

        private bool judgeMonthAdded(List<object> dataRow, bool isAdd)
        {
            string[] selMonth = checkedCbxMonth.Text.Split(',');
            if (selMonth != null)
            {
                foreach (string month in selMonth)
                {
                    if (month.Trim().Equals(dataRow[0]))
                    {
                        isAdd = true;
                        break;
                    }
                }
            }

            return isAdd;
        }

        private void cbxContentType_SelectedIndexChanged(object sender, EventArgs e)
        {
            cbxValueChanged();
        }
        private void dataGridView_CellClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.ColumnIndex >= 2 && e.ColumnIndex<dataGridView.Columns.Count) 
            {
                KPIInfoPanelHelper.FreshShowChart_ng(e.ColumnIndex, new FreshShowChartControl(chartControl, dataGridView, cbxShowType, cbxContentType), defaultRangeX, defaultRangeY);
            }
        }

        internal void ReturnTChartControl(Steema.TeeChart.TChart tchart)
        {
            tchart.Parent = null;
            splitMain.Panel2.Controls.Add(tchart);
            splitMain.Panel2Collapsed = false;
            tchart.Dock = DockStyle.Fill;
        }

        private void btnColor_Click(object sender, EventArgs e)
        {
            //
#if PopShow_KPI_Color
            kpiColorCfgForm form = new kpiColorCfgForm(this);
            
            form.ShowDialog();
#endif
        }

        private void btnLevel0_Click(object sender, EventArgs e)
        {
            if(isProvUser)
            {
                curDrillLevel = 0;
                cityName = "";
                refreshShowReport(true);
            }
        }

        private void dataGridView_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.ColumnIndex == 1 && e.RowIndex < dataGridView.Rows.Count && e.RowIndex!=-1)//选择了地市名称
            {
                KPIPopTable kpiPopTable = cbxReportSel.SelectedItem as KPIPopTable;
                if (kpiPopTable.ToString() == "GSM语音业务报表_汇总")
                {
                    if(e.ColumnIndex ==1)
                    {
                        getReportSel(e);
                    }
                }
                else
                {
                    changeDrillLevelReport(e);
                }
            }
        }

        private void getReportSel(DataGridViewCellEventArgs e)
        {
            string rptName = dataGridView.Rows[e.RowIndex].Cells[e.ColumnIndex].Value as string;
            foreach (KPIPopTable ptable in cbxReportSel.Items)
            {
                if (ptable.ToString() == rptName)
                {
                    //find
                    cbxReportSel.SelectedItem = ptable;
                    break;
                }
            }
        }

        private void changeDrillLevelReport(DataGridViewCellEventArgs e)
        {
            if (curDrillLevel == 0)
            {
                curDrillLevel = 1;
                refreshShowReport(true);
            }
            else if (curDrillLevel == 1)
            {
                string cityname = dataGridView.Rows[e.RowIndex].Cells[e.ColumnIndex].Value as string;
                if (cityname != null)
                {
                    this.cityName = cityname;
                    this.curDrillLevel = 2;
                    refreshShowReport(true);
                }
            }
        }

        private void miExp2Word_Click(object sender, EventArgs e)
        {
            if (dataGridView.RowCount<=0)
            {
                MessageBox.Show("当前无数据可导出！");
                return;
            }
            SelectSavePath();
        }
        private void SelectSavePath()
        {
            SaveFileDialog saveFileDlg = new SaveFileDialog();
            saveFileDlg.Title = "选择要保存文档的路径";
            saveFileDlg.RestoreDirectory = true;
            saveFileDlg.Filter = FilterHelper.Excel;
            if (saveFileDlg.ShowDialog() == DialogResult.OK)
            {
                ExcelControl excel = new ExcelControl();
                try
                {
                    ExportInfo(excel);
                    excel.SaveFile(saveFileDlg.FileName);
                }
                catch
                {
                    MessageBox.Show("导出数据出错！");
                    return;
                }
                finally 
                {
                    excel.CloseExcel();
                }
                if (DialogResult.Yes == MessageBox.Show(this, "Excel文件保存完毕，是否现在打开?", "打开文件", MessageBoxButtons.YesNo))
                {
                    try
                    {
                        System.Diagnostics.Process.Start(saveFileDlg.FileName);
                    }
                    catch
                    {
                        MessageBox.Show("打开失败!\r\n文件名:" + saveFileDlg.FileName);
                    }
                }
            }
        }

        public void ExportInfo(ExcelControl excel)
        {
            string dirPath = Path.Combine(Application.StartupPath, "KPIPictTemp");
            Directory.CreateDirectory(dirPath);

            KPIExport2XlsParam excelParam = new KPIExport2XlsParam();
            excelParam.excelApp = excel;
            excelParam.dgv = dataGridView;
            excelParam.pictPath = dirPath;
            excelParam.title = cbxReportSel.SelectedItem.ToString() +"_"+ cbxShowType.SelectedItem.ToString();
            WaitBox.Show(this, printPict, excelParam);
        }

        #region
        #endregion

        private void printPict(object param)
        {
            WaitBox.Text = "正在生成图片...";
            KPIExport2XlsParam wordParam = param as KPIExport2XlsParam;
            for (int i = 2; i < dataGridView.ColumnCount; i++)
            {
                KPIInfoPanelHelper.FreshShowChart_ng(i, new FreshShowChartControl(chartControl, dataGridView, cbxShowType, cbxContentType), defaultRangeX, defaultRangeY);
                string pictPath = wordParam.pictPath + Path.DirectorySeparatorChar + i.ToString() + ".jpg";
                chartControl.ExportToImage(pictPath, System.Drawing.Imaging.ImageFormat.Jpeg);
                WaitBox.ProgressPercent = (int)(i * 100.0 / dataGridView.ColumnCount);
            }
            fireExp2Xls(wordParam);
        }

        private void fireExp2Xls(object param)
        {
            KPIExport2XlsParam excelParam = param as KPIExport2XlsParam;
            excelParam.excelApp.Sheet.Name = "KPI报表_" + excelParam.title;
            excelParam.excelApp.ExportExcel(excelParam.excelApp.Sheet, excelParam.dgv);
            object cellHeightPt = ((Microsoft.Office.Interop.Excel.Range)(excelParam.excelApp.Sheet.Cells[1, 1])).Height;
            float fCellHeightPt = float.Parse(cellHeightPt.ToString());//Excel单元格的高度（磅）
            float picWidth = chartControl.Width / 96f * 72;//图片的宽度（磅）
            float picHeight = chartControl.Height / 96f * 72f;//图片的高度（磅）
            int h = (int)(picHeight / fCellHeightPt + 1);
            for (int index = 2; index < excelParam.dgv.ColumnCount; index++)//插入图片
            {
                int rowIndex=excelParam.dgv.RowCount+2+h*(index -2);
                string pictPath = excelParam.pictPath + Path.DirectorySeparatorChar + index.ToString() + ".jpg";
                excelParam.excelApp.InsertPicture(rowIndex, pictPath, picWidth,picHeight);
            }
            WaitBox.Close();
        }

        private bool isChartFocused = false;
        public bool IsChartFocused
        {
            get { return isChartFocused; }
        }
        private void chartControl_MouseEnter(object sender, EventArgs e)
        {
            chartControl.Focus();
            isChartFocused = true;
        }
        
        private void chartControl_MouseLeave(object sender, EventArgs e)
        {
            isChartFocused = false;
            this.Focus();
        }

        private void button1_Click(object sender, EventArgs e)
        {
            WelcomForm welFrm = this.Parent.Parent.Parent as WelcomForm;
           if (welFrm!=null)
           {
               welFrm.ExpAll2Xls();
           }
        }

        private void exp2Xls_Click(object sender, EventArgs e)
        {
            SelectSavePath();
        }

        private void checkedCbxMonth_EditValueChanged(object sender, EventArgs e)
        {
            if (checkMonthState)
            {
                needFreshGrid = true;
            }
            else
            {
                needFreshGrid = false;
            }
        }
        private bool needFreshGrid = false;
        private bool checkMonthState = false;
        public class KPIExport2XlsParam
        {
            public ExcelControl excelApp { get; set; }
            public string pictPath { get; set; }
            public DataGridView dgv { get; set; }
            public string title { get; set; }
        }

        private void checkedComboBoxEdit1_Properties_Closed(object sender, DevExpress.XtraEditors.Controls.ClosedEventArgs e)
        {
            if (needFreshGrid)
            {
                FinalShowResult rSet = checkedCbxMonth.Properties.Tag as FinalShowResult;
                if (rSet != null)
                {
                    showInGrid(rSet);
                    KPIInfoPanelHelper.FreshShowChart_ng(2, new FreshShowChartControl(chartControl, dataGridView, cbxShowType, cbxContentType), defaultRangeX, defaultRangeY);
                }
            }
        }

        private void checkedCbxMonth_MouseDown(object sender, MouseEventArgs e)
        {
            checkedCbxMonth.Properties.DropDownRows = checkedCbxMonth.Properties.Items.Count + 1;
        }

        private void cbxContentType_EditValueChanged(object sender, EventArgs e)
        {
            cbxValueChanged();
        }

        private void cbxValueChanged()
        {
            refreshShowReport(false);
            KPIInfoPanelHelper.FreshShowChart_ng(2, new FreshShowChartControl(chartControl, dataGridView, cbxShowType, cbxContentType), defaultRangeX, defaultRangeY);
        }

        private void cbxContentType_MouseDown(object sender, MouseEventArgs e)
        {
            cbxContentType.Properties.DropDownRows = cbxContentType.Properties.Items.Count + 1;
        }
    }
}
