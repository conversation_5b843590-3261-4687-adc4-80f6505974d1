﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util.UiEx;

using MasterCom.RAMS.ZTFunc.ZTLteTestAcceptance;

namespace MasterCom.RAMS.ZTFunc
{
    public class LteTestAcceptQuery : DIYReplayFileQuery
    {
        public LteTestAcceptQuery(MainModel mainModel)
            : base(mainModel)
        {
            isAutoLoadCQTPicture = false;
        }

        public override string Name
        {
            get
            {
                return "LTE室分验收";
            }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22055, this.Name);
        }

        protected override DIYReplayContentOption getDIYReplayContent()
        {
            DIYReplayContentOption option = new DIYReplayContentOption();
            option.DefaultSerialThemeName = null;

            List<ColumnDefItem> items = null;
            foreach (string col in queryColumns)
            {
                items = InterfaceManager.GetInstance().GetColumnDefByShowName(col);
                option.SampleColumns.AddRange(items);
            }

            option.EventInclude = true;
            option.MessageInclude = true;

            return option;
        }

        LteDoorStationSettingFormConfigModel_XJ curCondition = null;
        protected override bool isValidCondition()
        {
            curCondition = LteDoorStationSettingFormConfig_XJ.Instance.LoadConfig();
            LteTestSettingForm setForm = new LteTestSettingForm();
            setForm.setDoorCondition(curCondition);
            if (setForm.ShowDialog() != System.Windows.Forms.DialogResult.OK)
            {
                return false;
            }

            manager = new LteTestAcceptManager();
            manager.SetAcceptCond(setForm.GetCondition());
            return true;
        }

        protected override void query()
        {
            MainModel.FireDTDataChanged(MainModel.MainForm);
            base.query();
        }

        protected override void fireShowResult()
        {
            if (errEx != null)
            {
                System.Windows.Forms.MessageBox.Show(errEx.Message + Environment.NewLine + errEx.StackTrace,
                    this.Name, System.Windows.Forms.MessageBoxButtons.OK);
            }
            else
            {
                System.Windows.Forms.MessageBox.Show(
                    "Excel导出完成!", this.Name, System.Windows.Forms.MessageBoxButtons.OK);
            }
            this.manager = null;
        }

        // 分析完所有文件后调用
        protected override void doPostReplayAction()
        {
            WaitTextBox.Show("正在导出Excel文件...", afterAnalyzeInThread);
        }

        // 回放完一个文件后调用
        protected override void queryReplayInfo(ClientProxy clientProxy, Package package, FileInfo fileInfo)
        {
            base.queryReplayInfo(clientProxy, package, fileInfo);
            this.AnalyzeFile(fileInfo);
            MainModel.DTDataManager.Clear();
        }

        private void AnalyzeFile(FileInfo fileInfo)
        {
            if (MainModel.DTDataManager.FileDataManagers.Count == 0)
            {
                return;
            }
            manager.AnalyzeFile(fileInfo, MainModel.DTDataManager.FileDataManagers[0]);
        }

        private void afterAnalyzeInThread()
        {
            try
            {
                manager.DoWorkAfterAnalyze();
                manager = null;
                this.errEx = null;
            }
            catch (Exception ex)
            {
                this.errEx = ex;
            }
            finally
            {
                System.Threading.Thread.Sleep(300);
                WaitTextBox.Close();
            }
        }

        private LteTestAcceptManager manager;

        private Exception errEx;

        #region queryColumns
        private readonly List<string> queryColumns = new List<string>()
        {
            "isampleid",
            "itime",
            "ilongitude",
            "ilatitude",
            "lte_TAC",
            "lte_ECI",
            "lte_RSRP",
            "lte_SINR",
            "lte_EARFCN",
            "lte_PCI",
            "lte_APP_ThroughputUL",
            "lte_APP_ThroughputDL",
            "lte_PDCP_UL",
            "lte_PDCP_DL",
            "lte_RSRP_Rx0",
            "lte_RSRP_Rx1",
            "lte_NCell_EARFCN",
            "lte_NCell_PCI",
            "lte_NCell_RSRP",
        };
        #endregion
    }
}
