﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.Util;

namespace MasterCom.RAMS.Model
{
    public class GSMUplinkData : TestPoint
    {
        public override void Fill(MasterCom.RAMS.Net.Content content)
        {
            FileID = content.GetParamInt();
            SN = content.GetParamInt();
            Time = content.GetParamInt();
            Millisecond = (short)content.GetParamInt();
            MS = (byte)content.GetParamInt();
            Longitude = content.GetParamDouble();
            Latitude = content.GetParamDouble();
            this["UL_Mode"] = content.GetParamShort();
            this["UL_MCC"] = content.GetParamShort();
            this["UL_MNC"] = content.GetParamByte();
            this["UL_LAC"] = content.GetParamInt();
            this["UL_RAC"] = content.GetParamShort();
            this["UL_CI"] = content.GetParamInt();
            this["UL_BCCH"] = content.GetParamShort();
            this["UL_BSIC"] = content.GetParamByte();
            this["UL_RxLevFull_DL"] = content.GetParamShort();
            this["UL_RxLevFull_UL"] = content.GetParamShort();
            this["UL_RxLevSub_DL"] = content.GetParamShort();
            this["UL_RxLevSub_UL"] = content.GetParamShort();
            this["UL_RxQualFull_DL"] = content.GetParamByte();
            this["UL_RxQualFull_UL"] = content.GetParamByte();
            this["UL_RxQualSub_DL"] = content.GetParamByte();
            this["UL_RxQualSub_UL"] = content.GetParamByte();
            this["UL_DTX_DL"] = content.GetParamByte();
            this["UL_DTX_UL"] = content.GetParamByte();
            this["UL_Pathloss_Full"] = content.GetParamByte();
            this["UL_Pathloss_Sub"] = content.GetParamByte();
            this["UL_Power_Level_BS"] = content.GetParamByte();
            this["UL_Power_Level_MS"] = content.GetParamByte();
            this["UL_SQI"] = content.GetParamShort();
            this["UL_TA"] = content.GetParamByte();
            this["UL_HP"] = content.GetParamByte();
            this["UL_ChannelType"] = content.GetParamByte();
            this["UL_MAIO"] = content.GetParamByte();
            this["UL_HSN"] = content.GetParamByte();
            //this["UP_ChannelType"] = content.GetParamByte();
            //this["UP_MAIO"] = content.GetParamByte();
            //this["UP_HSN"] = content.GetParamByte();
            //this["ImgGsmUlNBCell"] = content.GetParamBytes();
            //this["ImgGsmUlReserved"] = content.GetParamBytes();
            this["UL_N_ARFCN"] = content.GetParamShort();
            this["UL_N_BSIC"] = content.GetParamByte();
            this["UL_N_RxLev"] = content.GetParamShort();
            this["UL_N_LAC"] = content.GetParamInt();
            this["UL_N_CI"] = content.GetParamInt();
            this["UL_N_Ranking_Value"] = content.GetParamShort();
            this["UL_N_Ranking_Type"] = content.GetParamShort();
            this["UL_Reserved"] = content.GetParamByte();

        }
    }
}
