﻿using MasterCom.RAMS.Func;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class DelayReasonSettingDlg : BaseDialog
    {
        public DelayReasonSettingDlg()
        {
            InitializeComponent();
        }

        public void GetCondition(out ReasonCondition con)
        {
            con = new ReasonCondition();
            con.timeFind = (int)numSiteFind.Value;
            //con.timeResponse = (int)numSiteResponse.Value;
            con.timeInvite2tryingMo = (int)numSiteI2TMo.Value;
            con.timeTrying2progressMo = (int)numSiteT2PMo.Value;
            con.timeProgress2prackMo = (int)numSiteP2PMo.Value;
            con.timePrack2okMo = (int)numSiteP2POKMo.Value;
            con.timeOK2updateMo = (int)numSiteP2UMo.Value;
            con.timeUpdate2okMo = (int)numSiteU2UMo.Value;
            con.timeOK2ringingMo = (int)numSiteU2RMo.Value;

            con.timePaging2inviteMt = (int)numSiteP2iMt.Value;
            con.timeInvite2tryingMt = (int)numSiteI2TMt.Value;
            con.timeTrying2progressMt = (int)numSiteT2PMt.Value;
            con.timeProgress2prackMt = (int)numSiteP2PMt.Value;
            con.timePrack2okMt = (int)numSiteP2POKMt.Value;
            con.timeOK2updateMt = (int)numSiteP2UMt.Value;
            con.timeUpdate2okMt = (int)numSiteU2UMt.Value;
            con.timeOK2ringingMt = (int)numSiteU2RMt.Value;
        }
        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }
    }
}
