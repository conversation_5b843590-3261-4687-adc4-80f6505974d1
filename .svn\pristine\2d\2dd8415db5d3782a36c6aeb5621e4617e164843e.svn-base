﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.TableViewer
{
    public partial class ParamArrayIndexSelDlg : Form
    {
        public ParamArrayIndexSelDlg(int arrSize,string paramName)
        {
            InitializeComponent();
            cbxParaArrIndex.Items.Clear();
            for(int i=0;i<arrSize;i++)
            {
                cbxParaArrIndex.Items.Add(i);
            }
            cbxParaArrIndex.SelectedIndex = 0;
            this.Text = "添加参数选项 - " + paramName;
        }
        public int GetSelArrayIndex()
        {
            return (int)cbxParaArrIndex.SelectedItem;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }
    }
}