﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTNRDropCallAna
{
    public abstract class NRDropCallAnaCauseBase
    {
        public abstract bool IsSatisfy(NRDropCallAnaInfo call);
        //public abstract string Name { get;set;}
        public virtual bool ShowCaption
        {
            get
            {
                throw new NotImplementedException();
            }
            set
            {
                throw new NotImplementedException();
            }
        }
    }

    public class VoiceHangupCause : NRDropCallAnaCauseBase
    {
        public override bool IsSatisfy(NRDropCallAnaInfo call)
        {//addMessage(child, new MessageInfo(0x7FFF1C43, "Voice_Hangup"));
            for (int i = call.Messages.Count - 1; i >= 0; i--)
            {
                Message msg = call.Messages[i];
                if (msg.ID == 0x7FFF1C43)
                {
                    call.DropCause = ENRDropCallCause.VoiceHangup;
                    return true;
                }
            }
            return false;
        }
    }

    public class ReleasEPSBeforeOKCause : NRDropCallAnaCauseBase
    {
        public override bool IsSatisfy(NRDropCallAnaInfo call)
        {//0x416B02ce, "Deactivate EPS bearer context accept"
            for (int i = call.Messages.Count - 1; i >= 0; i--)
            {
                Message msg = call.Messages[i];
                if (msg.ID == 0x416B02ce)
                {
                    call.DropCause = ENRDropCallCause.提前释放EPS专用承载;
                    return true;
                }
            }
            return false;
        }
    }

    public class TwoBYECause : NRDropCallAnaCauseBase
    {
        public override bool IsSatisfy(NRDropCallAnaInfo call)
        {
            int byeNum = 0;
            for (int i = call.Messages.Count - 1; i >= 0; i--)
            {
                Message msg = call.Messages[i];
                if (msg.ID == 1107427328)
                {
                    byeNum++;
                }
                else if (msg.ID == 1107706056)
                {
                    break;
                }
            }
            if (byeNum > 1)
            {
                call.DropCause = ENRDropCallCause.双BYE;
                return true;
            }
            else
            {
                return false;
            }
        }
    }

    public class SIPBYERequestTerminated : NRDropCallAnaCauseBase
    {
        public override bool IsSatisfy(NRDropCallAnaInfo call)
        {
            for (int i = call.Messages.Count - 1; i >= 0; i--)
            {
                Message msg = call.Messages[i];
                if (msg.ID >= 0x42000000 && msg.ID < 0x43000000)
                {
                    int statusCode = (msg.ID & 0x00003fff);
                    if (statusCode == 487)
                    {
                        //eIMS_SIP_Response_Status_Code.Request_Terminated
                        call.DropCause = ENRDropCallCause.BYE_Request_Terminated;
                        return true;
                    }
                }
            }
            return false;
        }
    }

    public class WeakCoverCause : NRDropCallAnaCauseBase
    {
        public override bool IsSatisfy(NRDropCallAnaInfo call)
        {
            throw new NotImplementedException();
        }
    }

    public class PoorSINRCause : NRDropCallAnaCauseBase
    {
        public override bool IsSatisfy(NRDropCallAnaInfo call)
        {
            throw new NotImplementedException();
        }

    }

}
