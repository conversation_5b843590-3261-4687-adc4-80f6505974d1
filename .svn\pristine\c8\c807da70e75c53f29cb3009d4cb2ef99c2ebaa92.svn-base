﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.ZTFunc;
using MasterCom.RAMS.Func;
using MasterCom.Util;
using System.Windows.Forms;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc
{
    /// <summary>
    /// Written by WuJunHong 2012.7.27
    /// </summary>
    public class ZTDiyCoveredSampleQueryByRegion : DIYQueryFileInfoByRegion
    {
        public int setUnCoveredRxlev { get; set; } = 0;  //-95
        public int setWeakCoveredRxlev { get; set; } = 0;  //-80
        protected int serviceType = 19;
        SampleRangeInfo sampleRangeInfo = null;


        public ZTDiyCoveredSampleQueryByRegion(MainModel mainModel)
            :base(mainModel)
        {
        }

        public override string Name
        {
            get { return "无/弱覆盖占比分析"; }
        }

        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 16000, 16009, this.Name);
        }

        protected override void query()
        {
            if (!getCondition())
            {
                return;
            }
            MainModel.ClearDTData();
            IsShowFileInfoForm = false;
            MainModel.IsDrawEventResult = false;
            sampleRangeInfo = new SampleRangeInfo();
            base.query();

            WaitBox.CanCancel = true;
            WaitBox.Show("开始分析文件...", analyseFiles);
            

            if (sampleRangeInfo.totalCount == 0)
            {
                MessageBox.Show("查询结果不存在TD扫频数据！");
                return;
            }
            fireShowForm();
            
            MainModel.FireSetDefaultMapSerialTheme("TDSCAN_PCCPCH_RSCP");
            MainModel.FireDTDataChanged(this);
        }

        protected void analyseFiles()
        {
            try
            {
                List<FileInfo> files = new List<FileInfo>();
                foreach (FileInfo fileInfo in MainModel.FileInfos)
                {
                    if (fileInfo.ServiceType != serviceType) 
                    {
                        continue;
                    }
                    files.Add(fileInfo);  //只选用Scan_TD类型的文件
                }
                int iloop = 0;
                foreach (FileInfo fileInfo in files)
                {
                    WaitBox.Text = "正在分析文件(" + (++iloop) + "/" + files.Count + ")...";
                    WaitBox.ProgressPercent = (int)(iloop * 100.0 / files.Count);
                    Condition.FileInfos.Clear();
                    Condition.FileInfos.Add(fileInfo);
                    MainModel.IsFileReplayByCompareMode = Condition.isCompareMode;

                    replay();
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                }
            }
            finally
            {
                System.Threading.Thread.Sleep(20);
                WaitBox.Close();
            }
        }

        protected virtual void replay()
        {
            QueryCondition condition = new QueryCondition();
            condition.QueryType = Condition.QueryType;
            condition.FileInfos.AddRange(Condition.FileInfos);
            condition.Geometorys = Condition.Geometorys;
            DIYReplayFileWithNoWaitBox query = new DIYReplayFileWithNoWaitBox(MainModel);
            query.FilterSampleByRegion = true;
            query.IncludeEvent = false;
            query.SetQueryCondition(condition);
            query.Query();
            doStat();
        }

        protected virtual void doStat()
        {
            try
            {
                foreach (DTFileDataManager fileDataManager in MainModel.DTDataManager.FileDataManagers)
                {
                    List<TestPoint> testPointList = fileDataManager.TestPoints;
                    for (int i = 0; i < testPointList.Count; i++)
                    {
                        TestPoint testPoint = testPointList[i];
                        addValidTPInfo(testPoint);
                    }
                }
            }
            catch
            {
                //continue
            }
        }

        private void addValidTPInfo(TestPoint testPoint)
        {
            if (isValidTestPoint(testPoint))
            {
                float? pcchSub = (float?)testPoint["TDS_PCCPCH_RSCP"];
                sampleRangeInfo.totalCount++;
                if (pcchSub < setUnCoveredRxlev && pcchSub != null)  //统计无覆盖的采样点数
                {
                    sampleRangeInfo.noneCoveredCount++;
                }
                if (pcchSub < setWeakCoveredRxlev && pcchSub != null)  //统计弱覆盖的采样点数
                {
                    sampleRangeInfo.weakCoveredCount++;
                }
                if (pcchSub != null)
                {
                    addSampleRangeInfo(pcchSub);
                }
            }
        }

        private void addSampleRangeInfo(float? pcchSub)
        {
            float p = (float)pcchSub;
            sampleRangeInfo.sumRxlev += p;  //累加总电平
            if (p < -100)
                sampleRangeInfo.n_100++;
            else if (p < -95)
                sampleRangeInfo._100_95++;
            else if (p < -90)
                sampleRangeInfo._95_90++;
            else if (p < -85)
                sampleRangeInfo._90_85++;
            else if (p < -80)
                sampleRangeInfo._85_80++;
            else if (p < -75)
                sampleRangeInfo._80_75++;
            else if (p < -70)
                sampleRangeInfo._75_70++;
            else if (p < -65)
                sampleRangeInfo._70_65++;
            else if (p >= -65)
                sampleRangeInfo._65_p++;
        }

        private void fireShowForm()
        {
            MainModel.SampleRangeInfo = sampleRangeInfo;
            ScanUncoveredAndWeakCoveredSampleForm frm = MainModel.GetInstance().CreateResultForm(typeof(ScanUncoveredAndWeakCoveredSampleForm)) as ScanUncoveredAndWeakCoveredSampleForm;
            frm.FillData();
            frm.Visible = true;
            frm.BringToFront();
        }

        protected virtual bool getCondition()
        {
            SetThresholdForm setThresholdForm = new SetThresholdForm();
            if (setThresholdForm.ShowDialog() == DialogResult.OK)
            {
                int unCoveredRxlev;
                int weakCoveredRxlev;
                setThresholdForm.GetSettingRxlev(out unCoveredRxlev, out weakCoveredRxlev);
                setUnCoveredRxlev = unCoveredRxlev;
                setWeakCoveredRxlev = weakCoveredRxlev;
                return true;
            }
            return false;
        }

        protected virtual bool isValidTestPoint(TestPoint testPoint)
        {
            if (Condition.Geometorys != null && Condition.Geometorys.GeoOp.Contains(testPoint.Longitude, testPoint.Latitude))
            {
                return true;
            }
            return false;
        }

    }

    public class ZTDiyWcdmaCoveredSampleQueryByRegion : DIYQueryFileInfoByRegion
    {
        public int setUnCoveredRxlev { get; set; } = 0;  //-95
        public int setWeakCoveredRxlev { get; set; } = 0;  //-80
        protected int serviceType = 21;//ServiceType.WCDMA_SCAN
        SampleRangeInfo sampleRangeInfo = null;


        public ZTDiyWcdmaCoveredSampleQueryByRegion(MainModel mainModel)
            : base(mainModel)
        {
        }

        public override string Name
        {
            get { return "WCDMA无/弱覆盖占比分析"; }
        }

        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }

        protected override void query()
        {
            if (!getCondition())
            {
                return;
            }
            MainModel.ClearDTData();
            IsShowFileInfoForm = false;
            MainModel.IsDrawEventResult = false;
            sampleRangeInfo = new SampleRangeInfo();
            sampleRangeInfo.SampleRangeInfoType = SampleRangeInfo.SampleRangeType.WCDMA;
            base.query();

            WaitBox.CanCancel = true;
            WaitBox.Show("开始分析文件...", analyseFiles);

            fireShowForm();

            MainModel.FireSetDefaultMapSerialTheme("WS_CPICHTotalRSCP");
            MainModel.FireDTDataChanged(this);
        }

        protected void analyseFiles()
        {
            try
            {
                List<FileInfo> files = new List<FileInfo>();
                foreach (FileInfo fileInfo in MainModel.FileInfos)
                {
                    if (fileInfo.ServiceType != serviceType)
                    {
                        continue;
                    }
                    files.Add(fileInfo);  //只选用Scan_TD类型的文件
                }
                int iloop = 0;
                foreach (FileInfo fileInfo in files)
                {
                    WaitBox.Text = "正在分析文件(" + (++iloop) + "/" + files.Count + ")...";
                    WaitBox.ProgressPercent = (int)(iloop * 100.0 / files.Count);
                    Condition.FileInfos.Clear();
                    Condition.FileInfos.Add(fileInfo);
                    MainModel.IsFileReplayByCompareMode = Condition.isCompareMode;

                    replay();
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                }
            }
            finally
            {
                System.Threading.Thread.Sleep(20);
                WaitBox.Close();
            }
        }

        protected virtual void replay()
        {
            QueryCondition condition = new QueryCondition();
            condition.QueryType = Condition.QueryType;
            condition.FileInfos.AddRange(Condition.FileInfos);
            condition.Geometorys = Condition.Geometorys;
            DIYReplayFileWithNoWaitBox query = new DIYReplayFileWithNoWaitBox(MainModel);
            query.FilterSampleByRegion = true;
            query.IncludeEvent = false;
            query.SetQueryCondition(condition);
            query.Query();
            doStat();
        }

        protected virtual void doStat()
        {
            try
            {
                foreach (DTFileDataManager fileDataManager in MainModel.DTDataManager.FileDataManagers)
                {
                    List<TestPoint> testPointList = fileDataManager.TestPoints;
                    for (int i = 0; i < testPointList.Count; i++)
                    {
                        TestPoint testPoint = testPointList[i];
                        addValidTPInfo(testPoint);
                    }
                }
            }
            catch
            {
                //continue
            }
        }

        private void addValidTPInfo(TestPoint testPoint)
        {
            if (isValidTestPoint(testPoint))
            {
                float? pcchSub = (float?)testPoint["WS_CPICHTotalRSCP"];
                sampleRangeInfo.totalCount++;
                if (pcchSub < setUnCoveredRxlev && pcchSub != null)  //统计无覆盖的采样点数
                {
                    sampleRangeInfo.noneCoveredCount++;
                }
                if (pcchSub < setWeakCoveredRxlev && pcchSub != null)  //统计弱覆盖的采样点数
                {
                    sampleRangeInfo.weakCoveredCount++;
                }
                if (pcchSub != null)
                {
                    addSampleRangeInfo(pcchSub);
                }
            }
        }

        private void addSampleRangeInfo(float? pcchSub)
        {
            float p = (float)pcchSub;
            sampleRangeInfo.sumRxlev += p;  //累加总电平
            if (p < -100)
            {
                sampleRangeInfo.n_100++;
            }
            else if (p < -95)
            {
                sampleRangeInfo._100_95++;
            }
            else if (p < -90)
            {
                sampleRangeInfo._95_90++;
            }
            else if (p < -85)
            {
                sampleRangeInfo._90_85++;
            }
            else if (p < -80)
            {
                sampleRangeInfo._85_80++;
            }
            else if (p < -75)
            {
                sampleRangeInfo._80_75++;
            }
            else if (p < -70)
            {
                sampleRangeInfo._75_70++;
            }
            else if (p < -65)
            {
                sampleRangeInfo._70_65++;
            }
            else if (p >= -65)
            {
                sampleRangeInfo._65_p++;
            }
        }

        private void fireShowForm()
        {
            MainModel.SampleRangeInfo = sampleRangeInfo;
            ScanUncoveredAndWeakCoveredSampleForm frm = MainModel.GetInstance().CreateResultForm(typeof(ScanUncoveredAndWeakCoveredSampleForm)) as ScanUncoveredAndWeakCoveredSampleForm;
            frm.FillData();
            frm.Visible = true;
            frm.BringToFront();
        }

        protected virtual bool getCondition()
        {
            SetThresholdForm setThresholdForm = new SetThresholdForm();
            if (setThresholdForm.ShowDialog() == DialogResult.OK)
            {
                int unCoveredRxlev;
                int weakCoveredRxlev;
                setThresholdForm.GetSettingRxlev(out unCoveredRxlev, out weakCoveredRxlev);
                setUnCoveredRxlev = unCoveredRxlev;
                setWeakCoveredRxlev = weakCoveredRxlev;
                return true;
            }
            return false;
        }

        protected virtual bool isValidTestPoint(TestPoint testPoint)
        {
            if (Condition.Geometorys != null && Condition.Geometorys.GeoOp.Contains(testPoint.Longitude, testPoint.Latitude))
            {
                return true;
            }
            return false;
        }
    }

    public class SampleRangeInfo
    {
        public enum SampleRangeType
        { 
            TD,
            WCDMA
        }

        /// <summary>
        /// SampleRangeInfo类型
        /// </summary>
        public SampleRangeType SampleRangeInfoType { get; set; } = SampleRangeType.TD;

        /// <summary>
        /// 采样点总数
        /// </summary>
        public int totalCount { get; set; } = 0;

        /// <summary>
        /// 弱覆盖采样点数目
        /// </summary>
        public int weakCoveredCount { get; set; } = 0;

        /// <summary>
        /// 无覆盖采样点数目
        /// </summary>
        public int noneCoveredCount { get; set; } = 0;

        /// <summary>
        /// 平均电平
        /// </summary>
        public int averageRxlev { get; set; } = 0;

        /// <summary>
        /// 采样点总电平
        /// </summary>
        public float sumRxlev { get; set; } = 0;

        public string GetWeakCoveredSamplePercentage()
        {
            return ((double)(weakCoveredCount) / (double)(totalCount)).ToString("P");
        }

        public string GetNoneCoveredSamplePercentage()
        {
            return ((double)(noneCoveredCount) / (double)(totalCount)).ToString("P");
        }

        public double GetAverageRxlev()
        {
            return Math.Round(sumRxlev / totalCount, 2);
        }

        /// <summary>
        /// (–∞,-100)
        /// </summary>
        public int n_100 { get; set; } = 0;
        /// <summary>
        /// [–100,-95)
        /// </summary>
        public int _100_95 { get; set; } = 0;
        /// <summary>
        /// [–95,-90)
        /// </summary>
        public int _95_90 { get; set; } = 0;
        /// <summary>
        /// [–90,-85)
        /// </summary>
        public int _90_85 { get; set; } = 0;
        /// <summary>
        /// [–85,-80)
        /// </summary>
        public int _85_80 { get; set; } = 0;
        /// <summary>
        /// [–80,-75)
        /// </summary>
        public int _80_75 { get; set; } = 0;
        /// <summary>
        /// [–75,-70)
        /// </summary>
        public int _75_70 { get; set; } = 0;
        /// <summary>
        /// [–70, -65)
        /// </summary>
        public int _70_65 { get; set; } = 0;
        /// <summary>
        /// [–65, +∞)
        /// </summary>
        public int _65_p { get; set; } = 0;
    }
}
