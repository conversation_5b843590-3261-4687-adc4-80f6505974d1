﻿namespace MasterCom.RAMS.ZTFunc.ZTReportEventMng
{
    partial class VVipEventLogListForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.tabControl = new DevExpress.XtraTab.XtraTabControl();
            this.tabTD = new DevExpress.XtraTab.XtraTabPage();
            this.gridControlTD = new DevExpress.XtraGrid.GridControl();
            this.gridViewTD = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumnSN = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnNetType = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnTestDate = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn15 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnCause = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn6 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn7 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn8 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn9 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn10 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn11 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn12 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn13 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn14 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repositoryItemCbxClosedLoop = new DevExpress.XtraEditors.Repository.RepositoryItemComboBox();
            this.repositoryItemCbxHasTrafficLog = new DevExpress.XtraEditors.Repository.RepositoryItemComboBox();
            this.repositoryItemCbxMoMt = new DevExpress.XtraEditors.Repository.RepositoryItemComboBox();
            this.tabGSM = new DevExpress.XtraTab.XtraTabPage();
            this.gridControlGSM = new DevExpress.XtraGrid.GridControl();
            this.gridViewGSM = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn16 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn17 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn18 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn20 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn21 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn22 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn23 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn24 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn25 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn26 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn27 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn28 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn29 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn30 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn31 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn32 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn33 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn34 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repositoryItemComboBox2 = new DevExpress.XtraEditors.Repository.RepositoryItemComboBox();
            this.repositoryItemComboBox3 = new DevExpress.XtraEditors.Repository.RepositoryItemComboBox();
            this.repositoryItemComboBox1 = new DevExpress.XtraEditors.Repository.RepositoryItemComboBox();
            ((System.ComponentModel.ISupportInitialize)(this.tabControl)).BeginInit();
            this.tabControl.SuspendLayout();
            this.tabTD.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlTD)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewTD)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemCbxClosedLoop)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemCbxHasTrafficLog)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemCbxMoMt)).BeginInit();
            this.tabGSM.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlGSM)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewGSM)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemComboBox2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemComboBox3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemComboBox1)).BeginInit();
            this.SuspendLayout();
            // 
            // tabControl
            // 
            this.tabControl.Appearance.BackColor = System.Drawing.Color.Transparent;
            this.tabControl.Appearance.Options.UseBackColor = true;
            this.tabControl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabControl.Location = new System.Drawing.Point(0, 0);
            this.tabControl.Name = "tabControl";
            this.tabControl.SelectedTabPage = this.tabTD;
            this.tabControl.Size = new System.Drawing.Size(1008, 386);
            this.tabControl.TabIndex = 8;
            this.tabControl.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.tabTD,
            this.tabGSM});
            // 
            // tabTD
            // 
            this.tabTD.Controls.Add(this.gridControlTD);
            this.tabTD.Name = "tabTD";
            this.tabTD.Size = new System.Drawing.Size(1000, 356);
            this.tabTD.Text = "VVIP-TD";
            // 
            // gridControlTD
            // 
            this.gridControlTD.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlTD.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gridControlTD.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gridControlTD.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gridControlTD.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gridControlTD.EmbeddedNavigator.Buttons.Remove.Visible = false;
            this.gridControlTD.Location = new System.Drawing.Point(0, 0);
            this.gridControlTD.MainView = this.gridViewTD;
            this.gridControlTD.Name = "gridControlTD";
            this.gridControlTD.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repositoryItemCbxClosedLoop,
            this.repositoryItemCbxHasTrafficLog,
            this.repositoryItemCbxMoMt});
            this.gridControlTD.Size = new System.Drawing.Size(1000, 356);
            this.gridControlTD.TabIndex = 6;
            this.gridControlTD.UseEmbeddedNavigator = true;
            this.gridControlTD.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridViewTD});
            // 
            // gridViewTD
            // 
            this.gridViewTD.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumnSN,
            this.gridColumnNetType,
            this.gridColumnTestDate,
            this.gridColumn15,
            this.gridColumnCause,
            this.gridColumn1,
            this.gridColumn2,
            this.gridColumn3,
            this.gridColumn4,
            this.gridColumn5,
            this.gridColumn6,
            this.gridColumn7,
            this.gridColumn8,
            this.gridColumn9,
            this.gridColumn10,
            this.gridColumn11,
            this.gridColumn12,
            this.gridColumn13,
            this.gridColumn14});
            this.gridViewTD.GridControl = this.gridControlTD;
            this.gridViewTD.Name = "gridViewTD";
            this.gridViewTD.OptionsBehavior.Editable = false;
            this.gridViewTD.OptionsView.ColumnAutoWidth = false;
            this.gridViewTD.OptionsView.ShowGroupPanel = false;
            this.gridViewTD.ShowButtonMode = DevExpress.XtraGrid.Views.Base.ShowButtonModeEnum.Default;
            this.gridViewTD.DoubleClick += new System.EventHandler(this.gridView_DoubleClick);
            // 
            // gridColumnSN
            // 
            this.gridColumnSN.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(0)))));
            this.gridColumnSN.AppearanceHeader.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(0)))));
            this.gridColumnSN.AppearanceHeader.Options.UseBackColor = true;
            this.gridColumnSN.Caption = "事件编号";
            this.gridColumnSN.FieldName = "SN";
            this.gridColumnSN.Name = "gridColumnSN";
            this.gridColumnSN.Visible = true;
            this.gridColumnSN.VisibleIndex = 0;
            this.gridColumnSN.Width = 71;
            // 
            // gridColumnNetType
            // 
            this.gridColumnNetType.Caption = "基站类型";
            this.gridColumnNetType.FieldName = "BTSType";
            this.gridColumnNetType.Name = "gridColumnNetType";
            this.gridColumnNetType.Visible = true;
            this.gridColumnNetType.VisibleIndex = 1;
            this.gridColumnNetType.Width = 60;
            // 
            // gridColumnTestDate
            // 
            this.gridColumnTestDate.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(0)))));
            this.gridColumnTestDate.AppearanceHeader.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(0)))));
            this.gridColumnTestDate.AppearanceHeader.Options.UseBackColor = true;
            this.gridColumnTestDate.Caption = "测试时间";
            this.gridColumnTestDate.FieldName = "Date";
            this.gridColumnTestDate.Name = "gridColumnTestDate";
            this.gridColumnTestDate.Visible = true;
            this.gridColumnTestDate.VisibleIndex = 2;
            // 
            // gridColumn15
            // 
            this.gridColumn15.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(0)))));
            this.gridColumn15.AppearanceHeader.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(0)))));
            this.gridColumn15.AppearanceHeader.Options.UseBackColor = true;
            this.gridColumn15.Caption = "手机型号";
            this.gridColumn15.FieldName = "PhoneModelNumber";
            this.gridColumn15.Name = "gridColumn15";
            this.gridColumn15.Visible = true;
            this.gridColumn15.VisibleIndex = 3;
            // 
            // gridColumnCause
            // 
            this.gridColumnCause.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(0)))));
            this.gridColumnCause.AppearanceHeader.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(0)))));
            this.gridColumnCause.AppearanceHeader.Options.UseBackColor = true;
            this.gridColumnCause.Caption = "Log号";
            this.gridColumnCause.FieldName = "LogNumber";
            this.gridColumnCause.Name = "gridColumnCause";
            this.gridColumnCause.Visible = true;
            this.gridColumnCause.VisibleIndex = 4;
            // 
            // gridColumn1
            // 
            this.gridColumn1.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(0)))));
            this.gridColumn1.AppearanceHeader.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(0)))));
            this.gridColumn1.AppearanceHeader.Options.UseBackColor = true;
            this.gridColumn1.Caption = "CI";
            this.gridColumn1.FieldName = "CI";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.Visible = true;
            this.gridColumn1.VisibleIndex = 5;
            // 
            // gridColumn2
            // 
            this.gridColumn2.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(0)))));
            this.gridColumn2.AppearanceHeader.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(0)))));
            this.gridColumn2.AppearanceHeader.Options.UseBackColor = true;
            this.gridColumn2.Caption = "主/被叫";
            this.gridColumn2.FieldName = "MoMt";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.Visible = true;
            this.gridColumn2.VisibleIndex = 6;
            // 
            // gridColumn3
            // 
            this.gridColumn3.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(0)))));
            this.gridColumn3.AppearanceHeader.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(0)))));
            this.gridColumn3.AppearanceHeader.Options.UseBackColor = true;
            this.gridColumn3.Caption = "事件";
            this.gridColumn3.FieldName = "EventName";
            this.gridColumn3.Name = "gridColumn3";
            this.gridColumn3.Visible = true;
            this.gridColumn3.VisibleIndex = 7;
            // 
            // gridColumn4
            // 
            this.gridColumn4.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(0)))));
            this.gridColumn4.AppearanceHeader.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(0)))));
            this.gridColumn4.AppearanceHeader.Options.UseBackColor = true;
            this.gridColumn4.Caption = "事件产生时间";
            this.gridColumn4.FieldName = "Time";
            this.gridColumn4.Name = "gridColumn4";
            this.gridColumn4.Visible = true;
            this.gridColumn4.VisibleIndex = 8;
            // 
            // gridColumn5
            // 
            this.gridColumn5.Caption = "事件产生地点";
            this.gridColumn5.FieldName = "Place";
            this.gridColumn5.Name = "gridColumn5";
            this.gridColumn5.Visible = true;
            this.gridColumn5.VisibleIndex = 9;
            // 
            // gridColumn6
            // 
            this.gridColumn6.Caption = "经度";
            this.gridColumn6.FieldName = "Longitude";
            this.gridColumn6.Name = "gridColumn6";
            this.gridColumn6.Visible = true;
            this.gridColumn6.VisibleIndex = 10;
            // 
            // gridColumn7
            // 
            this.gridColumn7.Caption = "纬度";
            this.gridColumn7.FieldName = "Latitude";
            this.gridColumn7.Name = "gridColumn7";
            this.gridColumn7.Visible = true;
            this.gridColumn7.VisibleIndex = 11;
            // 
            // gridColumn8
            // 
            this.gridColumn8.Caption = "事件分析";
            this.gridColumn8.FieldName = "Analytics";
            this.gridColumn8.Name = "gridColumn8";
            this.gridColumn8.Visible = true;
            this.gridColumn8.VisibleIndex = 12;
            // 
            // gridColumn9
            // 
            this.gridColumn9.Caption = "解决建议";
            this.gridColumn9.FieldName = "Suggestion";
            this.gridColumn9.Name = "gridColumn9";
            this.gridColumn9.Visible = true;
            this.gridColumn9.VisibleIndex = 13;
            // 
            // gridColumn10
            // 
            this.gridColumn10.Caption = "分类原因";
            this.gridColumn10.FieldName = "ErrorType";
            this.gridColumn10.Name = "gridColumn10";
            this.gridColumn10.Visible = true;
            this.gridColumn10.VisibleIndex = 14;
            // 
            // gridColumn11
            // 
            this.gridColumn11.Caption = "所属片";
            this.gridColumn11.FieldName = "OwnRegion";
            this.gridColumn11.Name = "gridColumn11";
            this.gridColumn11.Visible = true;
            this.gridColumn11.VisibleIndex = 15;
            // 
            // gridColumn12
            // 
            this.gridColumn12.Caption = "是否闭环";
            this.gridColumn12.FieldName = "IsCloseLoop";
            this.gridColumn12.Name = "gridColumn12";
            this.gridColumn12.Visible = true;
            this.gridColumn12.VisibleIndex = 16;
            // 
            // gridColumn13
            // 
            this.gridColumn13.Caption = "是否有traffic记录";
            this.gridColumn13.FieldName = "HasTrafficLog";
            this.gridColumn13.Name = "gridColumn13";
            this.gridColumn13.Visible = true;
            this.gridColumn13.VisibleIndex = 17;
            // 
            // gridColumn14
            // 
            this.gridColumn14.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(0)))));
            this.gridColumn14.AppearanceHeader.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(0)))));
            this.gridColumn14.AppearanceHeader.Options.UseBackColor = true;
            this.gridColumn14.Caption = "手机号";
            this.gridColumn14.FieldName = "PhoneNumber";
            this.gridColumn14.Name = "gridColumn14";
            this.gridColumn14.Visible = true;
            this.gridColumn14.VisibleIndex = 18;
            // 
            // repositoryItemCbxClosedLoop
            // 
            this.repositoryItemCbxClosedLoop.AutoHeight = false;
            this.repositoryItemCbxClosedLoop.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repositoryItemCbxClosedLoop.DropDownRows = 2;
            this.repositoryItemCbxClosedLoop.Items.AddRange(new object[] {
            "是",
            "否"});
            this.repositoryItemCbxClosedLoop.Name = "repositoryItemCbxClosedLoop";
            this.repositoryItemCbxClosedLoop.NullText = "[编辑值为空]";
            this.repositoryItemCbxClosedLoop.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            // 
            // repositoryItemCbxHasTrafficLog
            // 
            this.repositoryItemCbxHasTrafficLog.AutoHeight = false;
            this.repositoryItemCbxHasTrafficLog.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repositoryItemCbxHasTrafficLog.Items.AddRange(new object[] {
            "是",
            "否"});
            this.repositoryItemCbxHasTrafficLog.Name = "repositoryItemCbxHasTrafficLog";
            this.repositoryItemCbxHasTrafficLog.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            // 
            // repositoryItemCbxMoMt
            // 
            this.repositoryItemCbxMoMt.AutoHeight = false;
            this.repositoryItemCbxMoMt.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repositoryItemCbxMoMt.Items.AddRange(new object[] {
            "主叫",
            "被叫"});
            this.repositoryItemCbxMoMt.Name = "repositoryItemCbxMoMt";
            this.repositoryItemCbxMoMt.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            // 
            // tabGSM
            // 
            this.tabGSM.Controls.Add(this.gridControlGSM);
            this.tabGSM.Name = "tabGSM";
            this.tabGSM.Size = new System.Drawing.Size(1000, 356);
            this.tabGSM.Text = "VVIP-GSM";
            // 
            // gridControlGSM
            // 
            this.gridControlGSM.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlGSM.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gridControlGSM.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gridControlGSM.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gridControlGSM.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gridControlGSM.EmbeddedNavigator.Buttons.Remove.Visible = false;
            this.gridControlGSM.Location = new System.Drawing.Point(0, 0);
            this.gridControlGSM.MainView = this.gridViewGSM;
            this.gridControlGSM.Name = "gridControlGSM";
            this.gridControlGSM.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repositoryItemComboBox2,
            this.repositoryItemComboBox3,
            this.repositoryItemComboBox1});
            this.gridControlGSM.Size = new System.Drawing.Size(1000, 356);
            this.gridControlGSM.TabIndex = 7;
            this.gridControlGSM.UseEmbeddedNavigator = true;
            this.gridControlGSM.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridViewGSM});
            // 
            // gridViewGSM
            // 
            this.gridViewGSM.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn16,
            this.gridColumn17,
            this.gridColumn18,
            this.gridColumn20,
            this.gridColumn21,
            this.gridColumn22,
            this.gridColumn23,
            this.gridColumn24,
            this.gridColumn25,
            this.gridColumn26,
            this.gridColumn27,
            this.gridColumn28,
            this.gridColumn29,
            this.gridColumn30,
            this.gridColumn31,
            this.gridColumn32,
            this.gridColumn33,
            this.gridColumn34});
            this.gridViewGSM.GridControl = this.gridControlGSM;
            this.gridViewGSM.IndicatorWidth = 30;
            this.gridViewGSM.Name = "gridViewGSM";
            this.gridViewGSM.OptionsBehavior.Editable = false;
            this.gridViewGSM.OptionsView.ColumnAutoWidth = false;
            this.gridViewGSM.OptionsView.ShowGroupPanel = false;
            this.gridViewGSM.ShowButtonMode = DevExpress.XtraGrid.Views.Base.ShowButtonModeEnum.ShowAlways;
            this.gridViewGSM.DoubleClick += new System.EventHandler(this.gridView_DoubleClick);
            // 
            // gridColumn16
            // 
            this.gridColumn16.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(0)))));
            this.gridColumn16.AppearanceHeader.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(0)))));
            this.gridColumn16.AppearanceHeader.Options.UseBackColor = true;
            this.gridColumn16.Caption = "事件编号";
            this.gridColumn16.FieldName = "SN";
            this.gridColumn16.Name = "gridColumn16";
            this.gridColumn16.Visible = true;
            this.gridColumn16.VisibleIndex = 0;
            this.gridColumn16.Width = 71;
            // 
            // gridColumn17
            // 
            this.gridColumn17.Caption = "基站类型";
            this.gridColumn17.FieldName = "BTSType";
            this.gridColumn17.Name = "gridColumn17";
            this.gridColumn17.Visible = true;
            this.gridColumn17.VisibleIndex = 1;
            this.gridColumn17.Width = 60;
            // 
            // gridColumn18
            // 
            this.gridColumn18.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(0)))));
            this.gridColumn18.AppearanceHeader.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(0)))));
            this.gridColumn18.AppearanceHeader.Options.UseBackColor = true;
            this.gridColumn18.Caption = "测试时间";
            this.gridColumn18.FieldName = "Date";
            this.gridColumn18.Name = "gridColumn18";
            this.gridColumn18.Visible = true;
            this.gridColumn18.VisibleIndex = 2;
            // 
            // gridColumn20
            // 
            this.gridColumn20.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(0)))));
            this.gridColumn20.AppearanceHeader.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(0)))));
            this.gridColumn20.AppearanceHeader.Options.UseBackColor = true;
            this.gridColumn20.Caption = "Log号";
            this.gridColumn20.FieldName = "LogNumber";
            this.gridColumn20.Name = "gridColumn20";
            this.gridColumn20.Visible = true;
            this.gridColumn20.VisibleIndex = 3;
            // 
            // gridColumn21
            // 
            this.gridColumn21.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(0)))));
            this.gridColumn21.AppearanceHeader.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(0)))));
            this.gridColumn21.AppearanceHeader.Options.UseBackColor = true;
            this.gridColumn21.Caption = "CI";
            this.gridColumn21.FieldName = "CI";
            this.gridColumn21.Name = "gridColumn21";
            this.gridColumn21.Visible = true;
            this.gridColumn21.VisibleIndex = 4;
            // 
            // gridColumn22
            // 
            this.gridColumn22.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(0)))));
            this.gridColumn22.AppearanceHeader.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(0)))));
            this.gridColumn22.AppearanceHeader.Options.UseBackColor = true;
            this.gridColumn22.Caption = "主/被叫";
            this.gridColumn22.FieldName = "MoMt";
            this.gridColumn22.Name = "gridColumn22";
            this.gridColumn22.Visible = true;
            this.gridColumn22.VisibleIndex = 5;
            // 
            // gridColumn23
            // 
            this.gridColumn23.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(0)))));
            this.gridColumn23.AppearanceHeader.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(0)))));
            this.gridColumn23.AppearanceHeader.Options.UseBackColor = true;
            this.gridColumn23.Caption = "事件";
            this.gridColumn23.FieldName = "EventName";
            this.gridColumn23.Name = "gridColumn23";
            this.gridColumn23.Visible = true;
            this.gridColumn23.VisibleIndex = 6;
            // 
            // gridColumn24
            // 
            this.gridColumn24.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(0)))));
            this.gridColumn24.AppearanceHeader.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(0)))));
            this.gridColumn24.AppearanceHeader.Options.UseBackColor = true;
            this.gridColumn24.Caption = "事件产生时间";
            this.gridColumn24.FieldName = "Time";
            this.gridColumn24.Name = "gridColumn24";
            this.gridColumn24.Visible = true;
            this.gridColumn24.VisibleIndex = 7;
            // 
            // gridColumn25
            // 
            this.gridColumn25.Caption = "事件产生地点";
            this.gridColumn25.FieldName = "Place";
            this.gridColumn25.Name = "gridColumn25";
            this.gridColumn25.Visible = true;
            this.gridColumn25.VisibleIndex = 8;
            // 
            // gridColumn26
            // 
            this.gridColumn26.Caption = "经度";
            this.gridColumn26.FieldName = "Longitude";
            this.gridColumn26.Name = "gridColumn26";
            this.gridColumn26.Visible = true;
            this.gridColumn26.VisibleIndex = 9;
            // 
            // gridColumn27
            // 
            this.gridColumn27.Caption = "纬度";
            this.gridColumn27.FieldName = "Latitude";
            this.gridColumn27.Name = "gridColumn27";
            this.gridColumn27.Visible = true;
            this.gridColumn27.VisibleIndex = 10;
            // 
            // gridColumn28
            // 
            this.gridColumn28.Caption = "事件分析";
            this.gridColumn28.FieldName = "Analytics";
            this.gridColumn28.Name = "gridColumn28";
            this.gridColumn28.Visible = true;
            this.gridColumn28.VisibleIndex = 11;
            // 
            // gridColumn29
            // 
            this.gridColumn29.Caption = "解决建议";
            this.gridColumn29.FieldName = "Suggestion";
            this.gridColumn29.Name = "gridColumn29";
            this.gridColumn29.Visible = true;
            this.gridColumn29.VisibleIndex = 12;
            // 
            // gridColumn30
            // 
            this.gridColumn30.Caption = "分类原因";
            this.gridColumn30.FieldName = "ErrorType";
            this.gridColumn30.Name = "gridColumn30";
            this.gridColumn30.Visible = true;
            this.gridColumn30.VisibleIndex = 13;
            // 
            // gridColumn31
            // 
            this.gridColumn31.Caption = "所属片";
            this.gridColumn31.FieldName = "OwnRegion";
            this.gridColumn31.Name = "gridColumn31";
            this.gridColumn31.Visible = true;
            this.gridColumn31.VisibleIndex = 14;
            // 
            // gridColumn32
            // 
            this.gridColumn32.Caption = "是否闭环";
            this.gridColumn32.FieldName = "IsCloseLoop";
            this.gridColumn32.Name = "gridColumn32";
            this.gridColumn32.Visible = true;
            this.gridColumn32.VisibleIndex = 15;
            // 
            // gridColumn33
            // 
            this.gridColumn33.Caption = "是否有traffic记录";
            this.gridColumn33.FieldName = "HasTrafficLog";
            this.gridColumn33.Name = "gridColumn33";
            this.gridColumn33.Visible = true;
            this.gridColumn33.VisibleIndex = 16;
            // 
            // gridColumn34
            // 
            this.gridColumn34.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(0)))));
            this.gridColumn34.AppearanceHeader.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(0)))));
            this.gridColumn34.AppearanceHeader.Options.UseBackColor = true;
            this.gridColumn34.Caption = "手机号";
            this.gridColumn34.FieldName = "PhoneNumber";
            this.gridColumn34.Name = "gridColumn34";
            this.gridColumn34.Visible = true;
            this.gridColumn34.VisibleIndex = 17;
            // 
            // repositoryItemComboBox2
            // 
            this.repositoryItemComboBox2.AutoHeight = false;
            this.repositoryItemComboBox2.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repositoryItemComboBox2.DropDownRows = 2;
            this.repositoryItemComboBox2.Items.AddRange(new object[] {
            "是",
            "否"});
            this.repositoryItemComboBox2.Name = "repositoryItemComboBox2";
            this.repositoryItemComboBox2.NullText = "[编辑值为空]";
            this.repositoryItemComboBox2.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            // 
            // repositoryItemComboBox3
            // 
            this.repositoryItemComboBox3.AutoHeight = false;
            this.repositoryItemComboBox3.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repositoryItemComboBox3.Items.AddRange(new object[] {
            "是",
            "否"});
            this.repositoryItemComboBox3.Name = "repositoryItemComboBox3";
            this.repositoryItemComboBox3.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            // 
            // repositoryItemComboBox1
            // 
            this.repositoryItemComboBox1.AutoHeight = false;
            this.repositoryItemComboBox1.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repositoryItemComboBox1.Items.AddRange(new object[] {
            "主叫",
            "被叫"});
            this.repositoryItemComboBox1.Name = "repositoryItemComboBox1";
            this.repositoryItemComboBox1.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            // 
            // VVipEventLogListForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1008, 386);
            this.Controls.Add(this.tabControl);
            this.Name = "VVipEventLogListForm";
            this.Text = "VVIP商务终端测试事件列表";
            ((System.ComponentModel.ISupportInitialize)(this.tabControl)).EndInit();
            this.tabControl.ResumeLayout(false);
            this.tabTD.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlTD)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewTD)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemCbxClosedLoop)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemCbxHasTrafficLog)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemCbxMoMt)).EndInit();
            this.tabGSM.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlGSM)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewGSM)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemComboBox2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemComboBox3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemComboBox1)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraTab.XtraTabControl tabControl;
        private DevExpress.XtraTab.XtraTabPage tabTD;
        private DevExpress.XtraGrid.GridControl gridControlTD;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewTD;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnSN;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnNetType;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnTestDate;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn15;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnCause;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraEditors.Repository.RepositoryItemComboBox repositoryItemCbxMoMt;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn6;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn7;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn8;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn9;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn10;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn11;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn12;
        private DevExpress.XtraEditors.Repository.RepositoryItemComboBox repositoryItemCbxClosedLoop;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn13;
        private DevExpress.XtraEditors.Repository.RepositoryItemComboBox repositoryItemCbxHasTrafficLog;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn14;
        private DevExpress.XtraTab.XtraTabPage tabGSM;
        private DevExpress.XtraGrid.GridControl gridControlGSM;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewGSM;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn16;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn17;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn18;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn20;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn21;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn22;
        private DevExpress.XtraEditors.Repository.RepositoryItemComboBox repositoryItemComboBox1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn23;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn24;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn25;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn26;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn27;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn28;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn29;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn30;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn31;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn32;
        private DevExpress.XtraEditors.Repository.RepositoryItemComboBox repositoryItemComboBox2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn33;
        private DevExpress.XtraEditors.Repository.RepositoryItemComboBox repositoryItemComboBox3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn34;
    }
}