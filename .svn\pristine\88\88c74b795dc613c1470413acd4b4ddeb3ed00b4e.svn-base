﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model.BaseInfo;
using MasterCom.Util.UiEx;

namespace MasterCom.RAMS.Model
{
    public partial class TestRoundListForm : BaseForm
    {
        public static int FuncId { get; set; } = 11056;
        bool isAdmin = false;
        private List<TestRound> curDisplayRounds = new List<TestRound>();
        private List<TestRound> roundsRemove = new List<TestRound>();
        /// <summary>
        /// 包括新增，修改的测试轮次
        /// </summary>
        private List<TestRound> roundsModify = new List<TestRound>();

        public TestRoundListForm(List<TestRound> testRoundList)
            : base()
        {
            InitializeComponent();
            refreashListView(testRoundList);
        }

        private void refreashListView(List<TestRound> testRounds)
        {
            if (testRounds == null)
            {
                testRounds = new List<TestRound>();
            }
            curDisplayRounds = testRounds;
            gridCtrlTestRound.DataSource = curDisplayRounds;
            gridCtrlTestRound.RefreshDataSource();
        }

        private void gvRound_DoubleClick(object sender, EventArgs e)
        {
            if (isAdmin)
            {
                DevExpress.Utils.DXMouseEventArgs dxE = e as DevExpress.Utils.DXMouseEventArgs;
                int rowHd = gvRound.CalcHitInfo(dxE.Location).RowHandle;
                if (rowHd >= 0)
                {
                    TestRound round = gvRound.GetRow(rowHd) as TestRound;
                    modifyRound(round);
                }
            }
        }

        private void btnAdd_Click(object sender, EventArgs e)
        {
            TestRoundInfoForm frm = new TestRoundInfoForm(curDisplayRounds, null, true);
            if (frm.ShowDialog(this) == DialogResult.OK)
            {
                frm.Round.SN = curDisplayRounds.Count + 1;
                curDisplayRounds.Add(frm.Round);
                roundsModify.Add(frm.Round);
                gridCtrlTestRound.RefreshDataSource();
            }
        }

        private void btnModify_Click(object sender, EventArgs e)
        {
            TestRound round = gvRound.GetFocusedRow() as TestRound;
            if (round == null)
            {
                MessageBox.Show("请选择测试时段！");
                return;
            }
            modifyRound(round);
        }

        private void modifyRound(TestRound round)
        {
            TestRoundInfoForm frm = new TestRoundInfoForm(curDisplayRounds, round, false);
            if (frm.ShowDialog(this) == DialogResult.OK)
            {
                if (!roundsModify.Contains(round))
                {
                    roundsModify.Add(round);
                }
                gridCtrlTestRound.RefreshDataSource();
            }
        }

        private void btnRemove_Click(object sender, EventArgs e)
        {
            int[] hdRows = gvRound.GetSelectedRows();
            if (hdRows.Length == 0)
            {
                MessageBox.Show("请选择测试时段！");
                return;
            }
            for (int i = 0; i < hdRows.Length; i++)
            {
                TestRound round = gvRound.GetRow(hdRows[i]) as TestRound;
                if (round != null)
                {
                    roundsRemove.Add(round);
                    curDisplayRounds.Remove(round);
                }
            }
            gridCtrlTestRound.RefreshDataSource();
        }

        private void btnSubmit_Click(object sender, EventArgs e)
        {
            if (roundsRemove.Count > 0 || roundsModify.Count > 0)
            {
                WaitTextBox.Show("正在提交数据...", updateInThread);
                roundsRemove.Clear();
                roundsModify.Clear();
            }
            else
            {
                MessageBox.Show("没有需要提交的数据！");
            }
        }

        private void updateInThread()
        {
            try
            {
                if (roundsRemove.Count > 0)
                {
                    TestRoundDBOperator op = new TestRoundDBOperator(roundsRemove, true, MainModel.User.ID);
                    op.Query();
                }
                if (roundsModify.Count > 0)
                {
                    TestRoundDBOperator op = new TestRoundDBOperator(roundsModify, false, MainModel.User.ID);
                    op.Query();
                }
            }
            finally
            {
                WaitTextBox.Close();
            }      
        }

        private void btnInputAdminPw_Click(object sender, System.EventArgs e)
        {
            MasterCom.Util.TextInputBox box = new MasterCom.Util.TextInputBox("请输入系统管理员口令", "口令", null);
            box.SetPwdMode(true);
            while (box.ShowDialog() == DialogResult.OK)
            {
                if (box.TextInput == "mastercom168")
                {
                    isAdmin = btnSubmit.Visible = btnModify.Visible = btnAdd.Visible = btnRemove.Visible = true;
                    btnInputAdminPw.Visible = false;
                    break;
                }
            }
        }
    }
}
