﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Windows.Forms;
using DevExpress.XtraTreeList;
using DevExpress.XtraTreeList.Columns;
using DevExpress.XtraTreeList.Nodes;
using MapWinGIS;
using MasterCom.MTGis;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.KPI_Statistics.PK;
using MasterCom.RAMS.ZTFunc;
using MasterCom.RAMS.ZTFunc.AreaArchiveManage;
using MasterCom.Util;

namespace MasterCom.RAMS.KPI_Statistics
{
    public partial class KpiPkResultForm : MinCloseForm
    {
        Dictionary<PkGridDataGroup, List<PkGridDataCombine>> gridCombinedDic = null;
        PKCondition condition = null;
        public KpiPkResultForm()
        {
            InitializeComponent();
        }

        private PkGridLayer layer;

        public void FillData(PKCondition pkCond, IEnumerable<PkGridDataGroup> regDataSet
            , Dictionary<PkGridDataGroup, List<PkGridDataCombine>> combinedDic)
        {
            gridChkItemDic = new Dictionary<PkGridDataHub, CheckAlthm>();
            this.condition = pkCond;
            this.gridCombinedDic = combinedDic;
            lbxLegend.BeginUpdate();
            lbxLegend.Items.Clear();
            Dictionary<Alghirithm, CheckAlthm> althmChkDic = new Dictionary<Alghirithm, CheckAlthm>();
            foreach (Alghirithm thm in pkCond.SelTemplate.AlghirithmVec)
            {
                CheckAlthm chkItem = new CheckAlthm(thm);
                lbxLegend.Items.Add(chkItem);
                althmChkDic[thm] = chkItem;
            }
            CheckAlthm temp = new CheckAlthm(pkCond.SelTemplate.OtherAlghirithm);
            althmChkDic[pkCond.SelTemplate.OtherAlghirithm] = temp;
            lbxLegend.Items.Add(temp);
            lbxLegend.EndUpdate();

            treeList.BeginUpdate();
            treeList.Nodes.Clear();
            makeReportColumn(treeList);
            foreach (PkGridDataGroup dataGrp in regDataSet)
            {
                List<PkGridDataCombine> combinedList = null;
                if (gridCombinedDic != null)
                {
                    gridCombinedDic.TryGetValue(dataGrp, out combinedList);
                }
                appendTreeNode(pkCond, dataGrp, althmChkDic, combinedList);
            }
            treeList.EndUpdate();

            layer = MainModel.MainForm.GetMapForm().GetLayerBase(typeof(PkGridLayer)) as PkGridLayer;
            if (layer == null)
            {
                layer = new PkGridLayer();
                MainModel.MainForm.GetMapForm().AddLayerBase(layer);
            }

            layer.GridDic = gridChkItemDic;
            layer.Invalidate();
        }

        private void makeReportColumn(TreeList treeList)
        {
            treeList.Columns.Clear();
            TreeListColumn colFixed = AddtreeListColumn("区域", 330);
            colFixed.Fixed = FixedStyle.Left;

            AddtreeListColumn("竞比结果");

            foreach (ECarrier car in Enum.GetValues(typeof(ECarrier)))
            {
                AddtreeListColumn(car.ToString());
            }
            
            AddtreeListColumn("道路名");
            AddtreeListColumn("LOG名");
        }

        private TreeListColumn AddtreeListColumn(string caption, int width = 120)
        {
            TreeListColumn col = treeList.Columns.Add();
            col.Caption = caption;
            col.UnboundType = DevExpress.XtraTreeList.Data.UnboundColumnType.Object;
            col.OptionsColumn.AllowEdit = false;
            col.OptionsColumn.AllowMoveToCustomizationForm = false;
            col.OptionsColumn.ReadOnly = true;
            col.Visible = true;
            col.Width = width;
            return col;
        }

        private Dictionary<PkGridDataHub, CheckAlthm> gridChkItemDic = new Dictionary<PkGridDataHub, CheckAlthm>();
        private void appendTreeNode(PKCondition pkCond, PkGridDataGroup dataGrp, Dictionary<Alghirithm, CheckAlthm> althmChkDic
            , List<PkGridDataCombine> combinedList)
        {
            double dcm = 0, dcu = 0, dct = 0;
            dcm = dataGrp.CalcFormula(Model.CarrierType.ChinaMobile, -1, pkCond.SelTemplate.CMHub.PkBase.FormulaExp);
            dcu = dataGrp.CalcFormula(Model.CarrierType.ChinaUnicom, -1, pkCond.SelTemplate.CUHub.PkBase.FormulaExp);
            dct = dataGrp.CalcFormula(Model.CarrierType.ChinaTelecom, -1, pkCond.SelTemplate.CTHub.PkBase.FormulaExp);
            Alghirithm althm = pkCond.GetAlghirithm(dcm, dcu, dct);
            TreeListNode pNode = treeList.AppendNode(new object[] { dataGrp.ToString(),
                 althm.Name, toText(dcm), toText(dcu), toText(dct) }, null);
            pNode.Tag = althm;

            foreach (PkGridDataHub grid in dataGrp.Matrix)
            {
                dcm = grid.CalcFormula(Model.CarrierType.ChinaMobile, -1, pkCond.SelTemplate.CMHub.PkBase.FormulaExp);
                dcu = grid.CalcFormula(Model.CarrierType.ChinaUnicom, -1, pkCond.SelTemplate.CUHub.PkBase.FormulaExp);
                dct = grid.CalcFormula(Model.CarrierType.ChinaTelecom, -1, pkCond.SelTemplate.CTHub.PkBase.FormulaExp);
                althm = pkCond.GetAlghirithm(dcm, dcu, dct);
                TreeListNode node = treeList.AppendNode(new object[] { grid.ToString(),
                 althm.Name, toText(dcm), toText(dcu), toText(dct), grid.RoadName, grid.LowestGridLogNames }, pNode);
                node.Tag = grid;
                gridChkItemDic[grid] = althmChkDic[althm];
                grid.RangName = althm.Name;
            }

            TreeListNode fNode = treeList.AppendNode(new object[] { dataGrp.ToString() + "-竞比结果连续汇聚", "", "", "", "" }, null);
            fNode.Tag = dataGrp;
            appendCombineTreeNode(pkCond, combinedList, fNode);
        }
        private void appendCombineTreeNode(PKCondition pkCond, List<PkGridDataCombine> combinedList, TreeListNode fNode)
        {
            if (combinedList == null || combinedList.Count <= 0)
            {
                return;
            }
            foreach (PkGridDataCombine pkGridDataCombine in combinedList)
            {
                if (pkGridDataCombine.Matrix.Grids.Count >= spEditSerialNum.Value)
                {
                    pkGridDataCombine.FinalMtMoGroup();
                    double dcm = 0, dcu = 0, dct = 0;
                    dcm = pkGridDataCombine.CalcFormula(Model.CarrierType.ChinaMobile, -1, pkCond.SelTemplate.CMHub.PkBase.FormulaExp);
                    dcu = pkGridDataCombine.CalcFormula(Model.CarrierType.ChinaUnicom, -1, pkCond.SelTemplate.CUHub.PkBase.FormulaExp);
                    dct = pkGridDataCombine.CalcFormula(Model.CarrierType.ChinaTelecom, -1, pkCond.SelTemplate.CTHub.PkBase.FormulaExp);
                    Alghirithm althm = pkCond.GetAlghirithm(dcm, dcu, dct);
                    TreeListNode cNode = treeList.AppendNode(new object[] { pkGridDataCombine.ToString(),
                        althm.Name,toText(dcm), toText(dcu), toText(dct),
                        pkGridDataCombine.RoadNames,pkGridDataCombine.LowestGridLogNames }, fNode);
                    cNode.Tag = pkGridDataCombine;
                }
            }
        }
        private string toText(double dValue)
        {
            if (double.IsNaN(dValue))
            {
                return "-";
            }
            return Convert.ToString(dValue);
        }

        private void miExportXls_Click(object sender, EventArgs e)
        {
            exportXls(true, true);
        }

        private void miExportSerialGridXls_Click(object sender, EventArgs e)
        {
            exportXls(false, true);
        }

        private void miExportEveryGridXls_Click(object sender, EventArgs e)
        {
            exportXls(true, false);
        }
        private void exportXls(bool isExportEveryGrid, bool isExportSerial)
        {
            List<NPOIRow> rows = new List<NPOIRow>();
            NPOIRow row = new NPOIRow();
            rows.Add(row);
            row.AddCellValue("区域");
            row.AddCellValue("竞比结果");
            row.AddCellValue("移动");
            row.AddCellValue("联通");
            row.AddCellValue("电信");
            row.AddCellValue("道路名");
            row.AddCellValue("Log名");
            foreach (TreeListNode node in treeList.Nodes)
            {
                if (node.Tag is Alghirithm && !isExportEveryGrid)
                {
                    continue;
                }
                if (node.Tag is PkGridDataGroup && !isExportSerial)
                {
                    continue;
                }

                row = getNpoiRow(node);
                rows.Add(row);
                foreach (TreeListNode subNode in node.Nodes)
                {
                    row = getNpoiRow(subNode);
                    rows.Add(row);
                }
            }
            ExcelNPOIManager.ExportToExcel(rows);
        }
        private NPOIRow getNpoiRow(TreeListNode node)
        {
            NPOIRow row = new NPOIRow();
            foreach (TreeListColumn col in treeList.Columns)
            {
                row.AddCellValue(node.GetDisplayText(col));
            }
            return row;
        }
        
        private void lbxLegend_DrawItem(object sender, DrawItemEventArgs e)
        {
            ListBox listBoxLegend = sender as ListBox;
            if (e.Index < 0)
            {
                return;
            }
            object item = listBoxLegend.Items[e.Index];
            string text = "";
            if (item is CheckAlthm)
            {
                CheckAlthm checkAlthm = item as CheckAlthm;
                text = checkAlthm.BCheck ? "[√]" : "[  ]";
                e.Graphics.DrawString(text, listBoxLegend.Font, Brushes.Black, e.Bounds.X, e.Bounds.Y);
                e.Graphics.FillRectangle(new SolidBrush(checkAlthm.Althm.Color), e.Bounds.X + 20, e.Bounds.Y, 16, 16);
                text = checkAlthm.Althm.Name;
                e.Graphics.DrawString(text, listBoxLegend.Font, Brushes.Black, e.Bounds.X + 40, e.Bounds.Y);
            }
            else if (item is string)
            {
                text = item.ToString();
                e.Graphics.DrawString(text, listBoxLegend.Font, Brushes.Red, e.Bounds.X, e.Bounds.Y);
            }
        }

        private void lbxLegend_MouseClick(object sender, MouseEventArgs e)
        {
            int idx = lbxLegend.IndexFromPoint(e.Location);
            if (lbxLegend.Items.Count <= idx || idx < 0)
            {
                return;
            }

            if (lbxLegend.Items[idx] is CheckAlthm)
            {
                CheckAlthm chkItem = lbxLegend.Items[idx] as CheckAlthm;
                chkItem.BCheck = !chkItem.BCheck;
                lbxLegend.Invalidate(false);
                layer.Invalidate();
            }
        }

        private void treeList_DoubleClick(object sender, EventArgs e)
        {
            MouseEventArgs me = e as MouseEventArgs;
            TreeListHitInfo info = treeList.CalcHitInfo(me.Location);
            if (info.Node == null)
            {
                return;
            }

            if (info.Node.Tag is PkGridDataHub)
            {
                PkGridDataHub grid = info.Node.Tag as PkGridDataHub;
                if (grid != null)
                {
                    layer.CurSelGridList = new List<PkGridDataHub> { grid };
                    MainModel.MainForm.GetMapForm().GoToView(grid.CenterLng, grid.CenterLat, 6000);
                }
            }
            else if (info.Node.Tag is PkGridDataCombine)
            {
                PkGridDataCombine gridCombine = info.Node.Tag as PkGridDataCombine;
                if (gridCombine != null)
                {
                    layer.CurSelGridList = new List<PkGridDataHub>();
                    foreach (PkGridDataHub grid in gridCombine.Matrix)
                    {
                        layer.CurSelGridList.Add(grid);
                    }
                    PkGridDataHub firstGrid = gridCombine.GroupInfo as PkGridDataHub;
                    if (firstGrid != null)
                    {
                        MainModel.MainForm.GetMapForm().GoToView(firstGrid.CenterLng, firstGrid.CenterLat, 6000);
                    }
                }
            }
        }

        private void miExportShp_Click(object sender, EventArgs e)
        {
            SaveFileDialog dlg = new SaveFileDialog();
            dlg.Filter = FilterHelper.Shp;
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return;
            }

            Shapefile shpFile = new Shapefile();
            try
            {
                bool result = shpFile.CreateNewWithShapeID("", ShpfileType.SHP_POLYGON);
                if (!result)
                {
                    MessageBox.Show(shpFile.get_ErrorMsg(shpFile.LastErrorCode));
                    return;
                }
                int idIdx = 0;
                int ltLng = idIdx++;
                int ltLat = idIdx++;
                int brLng = idIdx++;
                int brLat = idIdx++;
                int pkDesc = idIdx++;
                int cmValue = idIdx++;
                int cuValue = idIdx++;
                int ctValue = idIdx;
                ShapeHelper.InsertNewField(shpFile, "左上经度", FieldType.DOUBLE_FIELD, 10, 30, ref ltLng);
                ShapeHelper.InsertNewField(shpFile, "左上纬度", FieldType.DOUBLE_FIELD, 10, 30, ref ltLat);
                ShapeHelper.InsertNewField(shpFile, "右下经度", FieldType.DOUBLE_FIELD, 10, 30, ref brLng);
                ShapeHelper.InsertNewField(shpFile, "右下纬度", FieldType.DOUBLE_FIELD, 10, 30, ref brLat);
                ShapeHelper.InsertNewField(shpFile, "竞比结果", FieldType.STRING_FIELD, 10, 30, ref pkDesc);
                ShapeHelper.InsertNewField(shpFile, "移动指标值", FieldType.STRING_FIELD, 10, 30, ref cmValue);
                ShapeHelper.InsertNewField(shpFile, "联通指标值", FieldType.STRING_FIELD, 10, 30, ref cuValue);
                ShapeHelper.InsertNewField(shpFile, "电信指标值", FieldType.STRING_FIELD, 10, 30, ref ctValue);
                int numShp = 0;
                foreach (TreeListNode root in treeList.Nodes)
                {
                    if (!(root.Tag is Alghirithm))
                    {
                        continue;
                    }
                    foreach (TreeListNode node in root.Nodes)
                    {
                        PkGridDataHub grid = node.Tag as PkGridDataHub;
                        shpFile.EditInsertShape(ShapeHelper.CreateRectShape(grid.LTLng, grid.LTLat, grid.BRLng, grid.BRLat), ref numShp);
                        shpFile.EditCellValue(ltLng, numShp, grid.LTLng);
                        shpFile.EditCellValue(ltLat, numShp, grid.LTLat);
                        shpFile.EditCellValue(brLng, numShp, grid.BRLng);
                        shpFile.EditCellValue(brLat, numShp, grid.BRLat);

                        setShapefileCellValue(shpFile, pkDesc, numShp, node);
                    }
                }
                ShapeHelper.DeleteShpFile(dlg.FileName);
                if (!shpFile.SaveAs(dlg.FileName, null))
                {
                    MessageBox.Show("保存文件失败！" + shpFile.get_ErrorMsg(shpFile.LastErrorCode));
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.ToString());
            }
            finally
            {
                shpFile.Close();
            }
        }

        private void setShapefileCellValue(Shapefile shpFile, int pkDesc, int numShp, TreeListNode node)
        {
            int idx = pkDesc;
            foreach (TreeListColumn col in treeList.Columns)
            {
                if (col.Fixed == FixedStyle.None)
                {
                    shpFile.EditCellValue(idx, numShp, node.GetDisplayText(col));
                    idx++;
                }
            }
        }

        private void spEditSerialNum_EditValueChanged(object sender, EventArgs e)
        {
            foreach (TreeListNode root in treeList.Nodes)
            {
                if (root.Tag is PkGridDataGroup)
                {
                    root.Nodes.Clear();
                    PkGridDataGroup dataGroup = root.Tag as PkGridDataGroup;
                    List<PkGridDataCombine> combinedList;
                    if (gridCombinedDic != null && gridCombinedDic.TryGetValue(dataGroup, out combinedList))
                    {
                        appendCombineTreeNode(condition, combinedList, root);
                    }
                }
            }
        }
    }
}
