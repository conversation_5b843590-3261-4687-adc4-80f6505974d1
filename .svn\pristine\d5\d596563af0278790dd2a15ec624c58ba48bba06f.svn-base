﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class PerCellDetailShowGSM : DevExpress.XtraEditors.XtraForm
    {
        public List<GSMPerDetailEleInf> DeailInfGSM { get; set; }
        
        public string MainSelectedEleName { get; set; }

        public PerCellDetailShowGSM()
        {
            InitializeComponent();
            eleNameSelect1.Hide();
            IniEleGridColRelation();
            this.LostFocus += new EventHandler(LostFocusDo);
        }
        private void LostFocusDo(object sender, EventArgs e)
        {
            this.Close();
        }

        private void PerCellDetailShow_VisibleChanged(object sender, EventArgs e)
        {
            if (!this.Visible)
            {
                this.Close();
            }
        }

        private void PerCellDetailShow_Deactivate(object sender, EventArgs e)
        {
            this.Close();
        }

        private void simpleButton2_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void simpleButton1_Click(object sender, EventArgs e)
        {
            eleNameSelect1.GetSelectedEleNamesDo = new EleNameSelectGSM.GetSelectedEleNames(AddSelectedEleName);
            eleNameSelect1.Show();
        }

        private void eleNameSelect1_EnabledChanged(object sender, EventArgs e)
        {
            eleNameSelect1.Hide();
        }

        private void AddSelectedEleName(List<string> selectedEleNames)
        {
            SetSpareGCVisibleFalse();
            for (int i = 0; i < selectedEleNames.Count; i++)
            {
                if (EleGridColRelation.ContainsKey(selectedEleNames[i]))
                {
                    string[] gcAndColInf = EleGridColRelation[selectedEleNames[i]];
                    if (gcAndColInf == null)
                    {
                        continue;
                    }
                    object o = this.GetType().GetField(gcAndColInf[0],
                        System.Reflection.BindingFlags.NonPublic |
                        System.Reflection.BindingFlags.Instance |
                        System.Reflection.BindingFlags.IgnoreCase).GetValue(this);
                    DevExpress.XtraGrid.Columns.GridColumn gc = (DevExpress.XtraGrid.Columns.GridColumn)o;
                    gc.Visible = true;
                }
            }
        }
        private void SetSpareGCVisibleFalse()
        {
            object o;
            for (int i = 7; i <= 13; i++)
            {
                o = this.GetType().GetField("gc"+i.ToString(),
                                   System.Reflection.BindingFlags.NonPublic |
                                   System.Reflection.BindingFlags.Instance |
                                   System.Reflection.BindingFlags.IgnoreCase).GetValue(this);
                DevExpress.XtraGrid.Columns.GridColumn gc = (DevExpress.XtraGrid.Columns.GridColumn)o;
                gc.Visible = false;
            }
            if (EleGridColRelation.ContainsKey(MainSelectedEleName))
            {
                o = this.GetType().GetField(EleGridColRelation[MainSelectedEleName][0],
                                      System.Reflection.BindingFlags.NonPublic |
                                      System.Reflection.BindingFlags.Instance |
                                      System.Reflection.BindingFlags.IgnoreCase).GetValue(this);
                DevExpress.XtraGrid.Columns.GridColumn gc = (DevExpress.XtraGrid.Columns.GridColumn)o;
                gc.Visible = true;
            }
        }
        private Dictionary<string, string[]> EleGridColRelation = new Dictionary<string, string[]>();
        private void IniEleGridColRelation()
        {
            EleGridColRelation.Add("无线接通率", new string[] { "gc1", "col1" });
            EleGridColRelation.Add("话音信道拥塞率(不含切)", new string[] { "gc2", "col2" });
            EleGridColRelation.Add("话音信道掉话率(不含切)", new string[] { "gc3", "col3" });
            EleGridColRelation.Add("上行TBF建立成功率", new string[] { "gc4", "col4" });
            EleGridColRelation.Add("下行TBF建立成功率", new string[] { "gc5", "col5" });
            EleGridColRelation.Add("PDCH分配成功率", new string[] { "gc6", "col6" });

            EleGridColRelation.Add("信令信道分配成功率", new string[] { "gc7", "col7" });
            EleGridColRelation.Add("话音信道分配成功率(不含切)", new string[] { "gc8", "col8" });
            EleGridColRelation.Add("信令信道拥塞率", new string[] { "gc9", "col9" });
            EleGridColRelation.Add("非PBGT切换占比", new string[] { "gc10", "col10" });
            EleGridColRelation.Add("下行话音质量", new string[] { "gc11", "col11" });
            EleGridColRelation.Add("切换成功率", new string[] { "gc12", "col12" });
            EleGridColRelation.Add("下行TBF掉线率", new string[] { "gc13", "col13" });
        }

        private DataTable dtRes;
        private void PerCellDetailShow_Load(object sender, EventArgs e)
        {
            if (dtRes == null || dtRes.Columns.Count == 0)
            {
                dtRes = new DataTable();
                dtRes.Columns.Add("tmdat");
                dtRes.Columns.Add("col1");
                dtRes.Columns.Add("col2");
                dtRes.Columns.Add("col3");
                dtRes.Columns.Add("col4");
                dtRes.Columns.Add("col5");
                dtRes.Columns.Add("col6");
                dtRes.Columns.Add("col7");
                dtRes.Columns.Add("col8");
                dtRes.Columns.Add("col9");
                dtRes.Columns.Add("col10");
                dtRes.Columns.Add("col11");
                dtRes.Columns.Add("col12");
                dtRes.Columns.Add("col13");
            }
            if (DeailInfGSM != null)
            {
                for (int i = 0; i < DeailInfGSM.Count; i++)
                {
                    dtRes.Rows.Add(new object[] {
                        DeailInfGSM[i].Tmdat.Replace(":00:00.000",""),
                        (DeailInfGSM[i].data1*100).ToString("0.00")+"%",
                        (DeailInfGSM[i].data2*100).ToString("0.00")+"%",
                        (DeailInfGSM[i].data3*100).ToString("0.00")+"%",
                        (DeailInfGSM[i].data4*100).ToString("0.00")+"%",
                        (DeailInfGSM[i].data5*100).ToString("0.00")+"%",
                        (DeailInfGSM[i].data6*100).ToString("0.00")+"%",
                        (DeailInfGSM[i].data7*100).ToString("0.00")+"%",
                        (DeailInfGSM[i].data8*100).ToString("0.00")+"%",
                        (DeailInfGSM[i].data9*100).ToString("0.00")+"%",
                        (DeailInfGSM[i].data10*100).ToString("0.00")+"%",
                        (DeailInfGSM[i].data11*100).ToString("0.00")+"%",
                        (DeailInfGSM[i].data12*100).ToString("0.00")+"%",
                        (DeailInfGSM[i].data13*100).ToString("0.00")+"%",
                    });
                }
                this.gridControlDetail.DataSource = dtRes;
            }
            SetSpareGCVisibleFalse();
            //初始化数据范围
            IniRegionInfs();
        }

        private void gridViewDetail_RowCellStyle(object sender, DevExpress.XtraGrid.Views.Grid.RowCellStyleEventArgs e)
        {
            string colName = e.Column.Caption;
            if (regionInf.ContainsKey(colName))
            {
                List<EleRegion> eleReg = regionInf[colName];
                double cellValue = double.Parse(e.CellValue.ToString().Replace("%", "")) / 100.0;
                if (IsEleValueInRegion(cellValue, eleReg))
                {
                    e.Appearance.BackColor = Color.FromArgb(200, 100, 100);
                }
            }
        }
        private bool IsEleValueInRegion(double value,List<EleRegion> eleReg)
        {
            if (eleReg.Count == 0)
            {
                if (value >= eleReg[0].Min && value <= eleReg[0].Max)
                {
                    return true;
                }
                else
                {
                    return false;
                }
            }
            else
            {
                for (int i = 0; i < eleReg.Count; i++)
                {
                    if (value < eleReg[i].Min || value > eleReg[i].Max)
                    {
                        return false;
                    }
                }
                return true;
            }
        }

        private Dictionary<string, List<EleRegion>> regionInf;
        private void IniRegionInfs()
        {
            regionInf = new Dictionary<string, List<EleRegion>>();
            #region GSM性能
            List<EleRegion> eleReg = new List<EleRegion>();
            eleReg.Add(new EleRegion(0.98, 0));
            regionInf.Add("信令信道分配成功率", eleReg);
            eleReg = new List<EleRegion>();
            eleReg.Add(new EleRegion(0.98, 0));
            regionInf.Add("话音信道分配成功率(不含切)", eleReg);
            eleReg = new List<EleRegion>();
            eleReg.Add(new EleRegion(0.96, 0));
            regionInf.Add("无线接通率", eleReg);
            eleReg = new List<EleRegion>();
            eleReg.Add(new EleRegion(1, 0.005));
            regionInf.Add("信令信道拥塞率", eleReg);
            eleReg = new List<EleRegion>();
            eleReg.Add(new EleRegion(1, 0.005));
            regionInf.Add("话音信道拥塞率(不含切)", eleReg);
            eleReg = new List<EleRegion>();
            eleReg.Add(new EleRegion(1, 0.02));
            regionInf.Add("话音信道掉话率(不含切)", eleReg);
            eleReg = new List<EleRegion>();
            eleReg.Add(new EleRegion(1, 0.5));
            regionInf.Add("非PBGT切换占比", eleReg);
            eleReg = new List<EleRegion>();
            eleReg.Add(new EleRegion(0.9, 0));
            regionInf.Add("下行话音质量", eleReg);
            eleReg = new List<EleRegion>();
            eleReg.Add(new EleRegion(0.95, 0));
            regionInf.Add("切换成功率", eleReg);
            eleReg = new List<EleRegion>();
            eleReg.Add(new EleRegion(0.95, 0));
            regionInf.Add("上行TBF建立成功率", eleReg);
            eleReg = new List<EleRegion>();
            eleReg.Add(new EleRegion(0.98, 0));
            regionInf.Add("下行TBF建立成功率", eleReg);
            eleReg = new List<EleRegion>();
            eleReg.Add(new EleRegion(1, 0.05));
            regionInf.Add("下行TBF掉线率", eleReg);
            eleReg = new List<EleRegion>();
            eleReg.Add(new EleRegion(0.95, 0));
            regionInf.Add("PDCH分配成功率", eleReg);
            #endregion

            #region TD性能
            eleReg = new List<EleRegion>();
            eleReg.Add(new EleRegion(1, 0.002));
            regionInf.Add("CS域RAB拥塞率", eleReg);

            eleReg = new List<EleRegion>();
            eleReg.Add(new EleRegion(0.99, 0));
            regionInf.Add("CS域无线接通率", eleReg);

            eleReg = new List<EleRegion>();
            eleReg.Add(new EleRegion(1, 0.01));
            regionInf.Add("CS域误块率", eleReg);

            eleReg = new List<EleRegion>();
            eleReg.Add(new EleRegion(1, 0.005));
            regionInf.Add("语音业务无线掉话率", eleReg);

            eleReg = new List<EleRegion>();
            eleReg.Add(new EleRegion(1, 0.005));
            regionInf.Add("PS域RAB拥塞率", eleReg);

            eleReg = new List<EleRegion>();
            eleReg.Add(new EleRegion(0.995, 0));
            regionInf.Add("PS域RAB建立成功率", eleReg);

            eleReg = new List<EleRegion>();
            eleReg.Add(new EleRegion(1, 0.05));
            regionInf.Add("PS域误块率", eleReg);

            eleReg = new List<EleRegion>();
            eleReg.Add(new EleRegion(1, 0.005));
            regionInf.Add("PS域无线掉线率", eleReg);

            eleReg = new List<EleRegion>();
            eleReg.Add(new EleRegion(0.98, 0));
            regionInf.Add("接力切换成功率", eleReg);

            eleReg = new List<EleRegion>();
            eleReg.Add(new EleRegion(1, 0.75));
            eleReg.Add(new EleRegion(0.15, 0));
            regionInf.Add("码资源利用率", eleReg);
            #endregion
        }

        private void ToolStripMenuItemDesc_Click(object sender, EventArgs e)
        {
            //SaveFileDialog saveFileDialog = new SaveFileDialog();
            //saveFileDialog.Title = "导出Excel";
            //saveFileDialog.Filter = "Excel文件(*.xls)|*.xls";
            //if (saveFileDialog.ShowDialog() == DialogResult.OK)
            //{
            //    gridControlDetail.ExportToExcelOld(saveFileDialog.FileName);
            //    DevExpress.XtraEditors.XtraMessageBox.Show("保存成功！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            //}
            List<List<object>> exportList = GridViewTransfer.Transfer(this.gridControlDetail);
            ExcelNPOIManager.ExportToExcel(exportList);
        }
    }
    public class EleRegion
    {
        public double Min { get; set; }
        public double Max { get; set; }

        public EleRegion()
        {
            Min = double.MinValue;
            Max = 1000;
        }
        public EleRegion(double maxValue, double minValue)
        {
            this.Max = maxValue;
            this.Min = minValue;
        }
    }
}