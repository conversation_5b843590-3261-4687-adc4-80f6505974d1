﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ScanCellCoverInfoForm : MinCloseForm
    {
        List<ScanCellCoverInfo_GSM> scanCellCoverInfoList;

        public ScanCellCoverInfoForm(MainModel mainModel)
            : base(mainModel)
        {
            InitializeComponent();
        }

        public void FillData(List<ScanCellCoverInfo_GSM> scanCellCoverInfoList)
        {
            this.scanCellCoverInfoList = scanCellCoverInfoList;
            this.gridControl1.DataSource = scanCellCoverInfoList;
        }

        private void gridView1_DoubleClick(object sender, EventArgs e)
        {
            int[] hRows = gridView1.GetSelectedRows();
            if (hRows.Length <= 0) return;
            ScanCellCoverInfo_GSM cellInfo = gridView1.GetRow(hRows[0]) as ScanCellCoverInfo_GSM;
            if (cellInfo == null) return;
            MainModel.ScanCellCoverInfoList.Clear();
            MainModel.ScanCellCoverInfoList.Add(cellInfo);
            MainModel.SelectedCell = cellInfo.CoverCell;
            
            MainModel.ClearDTData();
            foreach (ScanCellCoverPointInfo_GSM tpInfo in cellInfo.TpInfoList)
            {
                MainModel.DTDataManager.Add(tpInfo.TP);
            }
            MainModel.FireDTDataChanged(this);
        }

        private void gridView2_DoubleClick(object sender, EventArgs e)
        {
            int[] hRows = ((DevExpress.XtraGrid.Views.Grid.GridView)sender).GetSelectedRows();
            if (hRows.Length <= 0) return;

            ScanCellCoverPointInfo_GSM ptInfo = ((DevExpress.XtraGrid.Views.Grid.GridView)sender).GetRow(hRows[0]) as ScanCellCoverPointInfo_GSM;
            if (ptInfo == null) return;
            MainModel.SelectedTestPoints.Clear();
            MainModel.SelectedTestPoints.Add(ptInfo.TP);
            MainModel.MainForm.GetMapForm().GoToView(ptInfo.TP.Longitude, ptInfo.TP.Latitude, 5000);
        }

        private void ToolStripMenuItemExport_Click(object sender, EventArgs e)
        {
            try
            {
                DataTable table = new DataTable();
                table.Columns.Add("小区名称");
                table.Columns.Add("频点");
                table.Columns.Add("扰码");
                table.Columns.Add("经度");
                table.Columns.Add("纬度");
                table.Columns.Add("采样点数");
                table.Columns.Add("最大场强");
                table.Columns.Add("最小场强");
                table.Columns.Add("平均场强");
                table.Columns.Add("C/I最大值");
                table.Columns.Add("C/I最小值");
                table.Columns.Add("C/I平均值");
                table.Columns.Add("最大质量");
                table.Columns.Add("最小质量");
                table.Columns.Add("平均质量");
                table.Columns.Add("距离");
                foreach (ScanCellCoverInfo_GSM cellInfo in scanCellCoverInfoList)
                {
                    int idx = 0;
                    DataRow row = table.NewRow();
                    row[idx++] = cellInfo.CellName;
                    row[idx++] = cellInfo.BCCH;
                    row[idx++] = cellInfo.BSIC;
                    row[idx++] = cellInfo.Longitude;
                    row[idx++] = cellInfo.Latitude;
                    row[idx++] = cellInfo.TPCount;
                    row[idx++] = cellInfo.RxlevMax;
                    row[idx++] = cellInfo.RxlevMin;
                    row[idx++] = cellInfo.RxlevMean;
                    row[idx++] = cellInfo.C2IMax;
                    row[idx++] = cellInfo.C2IMin;
                    row[idx++] = cellInfo.C2IMean;
                    row[idx++] = cellInfo.RxQualMax;
                    row[idx++] = cellInfo.RxQualMin;
                    row[idx] = cellInfo.RxQualMean;
                    table.Rows.Add(row);
                    foreach (ScanCellCoverPointInfo_GSM pointInfo in cellInfo.TpInfoList)
                    {
                        idx = 0;
                        row = table.NewRow();
                        row[idx++] = "";
                        row[idx++] = "";
                        row[idx++] = "";
                        row[idx++] = pointInfo.Longitude;
                        row[idx++] = pointInfo.Latitude;
                        row[idx++] = "";
                        row[idx++] = "";
                        row[idx++] = "";
                        row[idx++] = pointInfo.Rxlev;
                        row[idx++] = "";
                        row[idx++] = "";
                        row[idx++] = pointInfo.C2I;
                        row[idx++] = "";
                        row[idx++] = "";
                        row[idx++] = pointInfo.RxQual;
                        row[idx] = pointInfo.Distance;
                        table.Rows.Add(row);
                    }
                }
                MasterCom.Util.ExcelNPOIManager.ExportToExcel(table);
            }
            catch
            {
                //continue
            }
        }
    }
}
