﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc.FindNearestSiteByPoints
{
    public partial class ConditionDlg : BaseDialog
    {
        public ConditionDlg()
        {
            InitializeComponent();
            this.gridCtrlGIS.DataSource = gisPoints;
            this.gridCtrlXls.DataSource = xlsPoints;
        }

        private void btnEditPath_Properties_ButtonClick(object sender, DevExpress.XtraEditors.Controls.ButtonPressedEventArgs e)
        {
            loadXls();
        }

        private void btnEditPath_DoubleClick(object sender, EventArgs e)
        {
            loadXls();
        }

        private List<PointInfo> xlsPoints = new List<PointInfo>();
        private void loadXls()
        {
            OpenFileDialog dlg = new OpenFileDialog();
            dlg.Filter = "Excel 97-2003 工作簿(*.xls)|*.xls|Excel 工作簿(*.xlsx)|*.xlsx";
            dlg.FilterIndex = 2;
            dlg.Multiselect = false;
            dlg.FileName = this.btnEditPath.Text;

            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return;
            }
            this.btnEditPath.Text = dlg.FileName;
            DataSet ds = ExcelNPOIManager.ImportFromExcel(dlg.FileName);
            DataTable table = ds.Tables[0];
            foreach (DataRow row in table.Rows)
            {
                try
                {
                    string name = row.ItemArray[0].ToString();
                    double lng = 0, lat = 0;
                    if (double.TryParse(row.ItemArray[1].ToString(), out lng)
                      && double.TryParse(row.ItemArray[2].ToString(), out lat))
                    {
                        PointInfo pi = new PointInfo();
                        pi.Name = name;
                        pi.Longitude = lng;
                        pi.Latitude = lat;
                        xlsPoints.Add(pi);
                    }
                }
                catch
                {
                    //continue
                }
            }
            this.gridCtrlXls.RefreshDataSource();
        }

        private void btnBegin_Click(object sender, EventArgs e)
        {
            btnEnd.Enabled = true;
            mainModel.MainForm.GetMapForm().ActivateMapToolBar("Select");
            mainModel.MainForm.GetMapForm().MapFeatureSelecting -= mapFeatureSelecting;
            mainModel.MainForm.GetMapForm().MapFeatureSelecting += mapFeatureSelecting;
        }

        private int pointSN = 1;
        public List<PointInfo> gisPoints { get; set; } = new List<PointInfo>();
        private void mapFeatureSelecting(object sender, EventArgs e)
        {
            MasterCom.RAMS.Func.MapForm.MapEventArgs mapEvt = e as MasterCom.RAMS.Func.MapForm.MapEventArgs;
            PointInfo pi = new PointInfo();
            pi.Name = "点" + pointSN++;
            pi.Longitude = Math.Round(mapEvt.Longitude, 6);
            pi.Latitude = Math.Round(mapEvt.Latitude, 6);
            gisPoints.Add(pi);
            gridCtrlGIS.RefreshDataSource();
        }

        private void btnEnd_Click(object sender, EventArgs e)
        {
            btnBegin.Enabled = true;
            mainModel.MainForm.GetMapForm().MapFeatureSelecting -= mapFeatureSelecting;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            List<PointInfo> points = null;
            if (tabCtrl.SelectedTabPage == pageXls)
            {
                points = xlsPoints;
            }
            else
            {
                points = gisPoints;
            }
            if (points.Count == 0)
            {
                MessageBox.Show("请指定坐标点！");
                return;
            }
            mainModel.MainForm.GetMapForm().MapFeatureSelecting -= mapFeatureSelecting;
            if (this.Runner != null)
            {
                if (rdbNear.Checked)
                {
                    Runner.NearSearch(points, chkIndoorSite.Checked, (double)numRadius.Value);
                }
                else
                {
                    Runner.Search(points, chkIndoorSite.Checked);
                }
                
            }
            this.Close();
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void ConditionDlg_FormClosing(object sender, FormClosingEventArgs e)
        {
            mainModel.MainForm.GetMapForm().MapFeatureSelecting -= mapFeatureSelecting;
        }

        private void btnRemove_Click(object sender, EventArgs e)
        {
            foreach (int handle in gvGIS.GetSelectedRows())
            {
                PointInfo pi = gvGIS.GetRow(handle) as PointInfo;
                if (pi != null)
                {
                    gisPoints.Remove(pi);
                }
            }
            gridCtrlGIS.RefreshDataSource();
        }

        public QueryNearestSiteByPoints Runner { get; set; }

        private void radioSet()
        {
            if (rdbNear.Checked)
            {
                label2.Visible = true;
                label3.Visible = true;
                numRadius.Visible = true;
            }
            else if (rdbNearest.Checked)
            {
                label2.Visible = false;
                label3.Visible = false;
                numRadius.Visible = false;
            }
        }
        private void rdbNearest_CheckedChanged(object sender, EventArgs e)
        {
            radioSet();
        }

        private void rdbNear_CheckedChanged(object sender, EventArgs e)
        {
            radioSet();
        }
        
    }
}
