<?xml version="1.0"?>
<Configs>
  <Config name="Template">
    <Item name="Options" typeName="IDictionary">
      <Item typeName="String" key="Name">移动3G场强 vs 电信3G场强</Item>
      <Item typeName="IDictionary" key="HubContext">
        <Item typeName="IDictionary" key="移动">
          <Item typeName="String" key="Name">移动</Item>
          <Item typeName="IList" key="ServVec">
            <Item typeName="Int32">4</Item>
          </Item>
          <Item typeName="String" key="FormulaName" />
          <Item typeName="String" key="FormulaExp">{Tx_5C04030D}</Item>
          <Item typeName="IDictionary" key="ValueRange">
            <Item typeName="Double" key="Min">-140</Item>
            <Item typeName="Boolean" key="MinIncluded">True</Item>
            <Item typeName="Double" key="Max">-10</Item>
            <Item typeName="Boolean" key="MaxIncluded">True</Item>
          </Item>
        </Item>
        <Item typeName="IDictionary" key="联通">
          <Item typeName="String" key="Name">联通</Item>
          <Item typeName="IList" key="ServVec">
            <Item typeName="Int32">10</Item>
          </Item>
          <Item typeName="String" key="FormulaName" />
          <Item typeName="String" key="FormulaExp">{Wx_710A3F}</Item>
          <Item typeName="IDictionary" key="ValueRange">
            <Item typeName="Double" key="Min">-140</Item>
            <Item typeName="Boolean" key="MinIncluded">True</Item>
            <Item typeName="Double" key="Max">-10</Item>
            <Item typeName="Boolean" key="MaxIncluded">True</Item>
          </Item>
        </Item>
        <Item typeName="IDictionary" key="电信">
          <Item typeName="String" key="Name">电信</Item>
          <Item typeName="IList" key="ServVec">
            <Item typeName="Int32">8</Item>
          </Item>
          <Item typeName="String" key="FormulaName" />
          <Item typeName="String" key="FormulaExp">{Ex_5E09010B}</Item>
          <Item typeName="IDictionary" key="ValueRange">
            <Item typeName="Double" key="Min">-140</Item>
            <Item typeName="Boolean" key="MinIncluded">True</Item>
            <Item typeName="Double" key="Max">-10</Item>
            <Item typeName="Boolean" key="MaxIncluded">True</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="IList" key="AlghirithmVec">
        <Item typeName="IDictionary">
          <Item typeName="String" key="Name">移动比电信好</Item>
          <Item typeName="Boolean" key="IsSpecial">False</Item>
          <Item typeName="String" key="ENaNCM">无数据</Item>
          <Item typeName="String" key="ENaNCU">有数据</Item>
          <Item typeName="String" key="ENaNCT">有数据</Item>
          <Item typeName="Int32" key="Color">-16760832</Item>
          <Item typeName="String" key="ColorRGB">0,64,0</Item>
          <Item typeName="IList" key="ValueRangeVec">
            <Item typeName="IDictionary">
              <Item typeName="String" key="HostCarrier">移动</Item>
              <Item typeName="String" key="GuestCarrier">电信</Item>
              <Item typeName="IDictionary" key="Range">
                <Item typeName="Double" key="Min">5</Item>
                <Item typeName="Boolean" key="MinIncluded">True</Item>
                <Item typeName="Double" key="Max">100</Item>
                <Item typeName="Boolean" key="MaxIncluded">True</Item>
              </Item>
            </Item>
          </Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Name">移动与电信相当</Item>
          <Item typeName="Boolean" key="IsSpecial">False</Item>
          <Item typeName="String" key="ENaNCM">无数据</Item>
          <Item typeName="String" key="ENaNCU">有数据</Item>
          <Item typeName="String" key="ENaNCT">有数据</Item>
          <Item typeName="Int32" key="Color">-256</Item>
          <Item typeName="String" key="ColorRGB">255,255,0</Item>
          <Item typeName="IList" key="ValueRangeVec">
            <Item typeName="IDictionary">
              <Item typeName="String" key="HostCarrier">移动</Item>
              <Item typeName="String" key="GuestCarrier">电信</Item>
              <Item typeName="IDictionary" key="Range">
                <Item typeName="Double" key="Min">-5</Item>
                <Item typeName="Boolean" key="MinIncluded">True</Item>
                <Item typeName="Double" key="Max">5</Item>
                <Item typeName="Boolean" key="MaxIncluded">False</Item>
              </Item>
            </Item>
          </Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Name">移动比电信差</Item>
          <Item typeName="Boolean" key="IsSpecial">False</Item>
          <Item typeName="String" key="ENaNCM">无数据</Item>
          <Item typeName="String" key="ENaNCU">有数据</Item>
          <Item typeName="String" key="ENaNCT">有数据</Item>
          <Item typeName="Int32" key="Color">-65536</Item>
          <Item typeName="String" key="ColorRGB">255,0,0</Item>
          <Item typeName="IList" key="ValueRangeVec">
            <Item typeName="IDictionary">
              <Item typeName="String" key="HostCarrier">移动</Item>
              <Item typeName="String" key="GuestCarrier">电信</Item>
              <Item typeName="IDictionary" key="Range">
                <Item typeName="Double" key="Min">-100</Item>
                <Item typeName="Boolean" key="MinIncluded">True</Item>
                <Item typeName="Double" key="Max">-5</Item>
                <Item typeName="Boolean" key="MaxIncluded">False</Item>
              </Item>
            </Item>
          </Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Name">只有移动</Item>
          <Item typeName="Boolean" key="IsSpecial">True</Item>
          <Item typeName="String" key="ENaNCM">有数据</Item>
          <Item typeName="String" key="ENaNCU">忽略</Item>
          <Item typeName="String" key="ENaNCT">无数据</Item>
          <Item typeName="Int32" key="Color">-4144960</Item>
          <Item typeName="String" key="ColorRGB">192,192,192</Item>
          <Item typeName="IList" key="ValueRangeVec" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Name">只有电信</Item>
          <Item typeName="Boolean" key="IsSpecial">True</Item>
          <Item typeName="String" key="ENaNCM">无数据</Item>
          <Item typeName="String" key="ENaNCU">忽略</Item>
          <Item typeName="String" key="ENaNCT">有数据</Item>
          <Item typeName="Int32" key="Color">-32944</Item>
          <Item typeName="String" key="ColorRGB">255,127,80</Item>
          <Item typeName="IList" key="ValueRangeVec" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Name">双方都没数据</Item>
          <Item typeName="Boolean" key="IsSpecial">True</Item>
          <Item typeName="String" key="ENaNCM">无数据</Item>
          <Item typeName="String" key="ENaNCU">忽略</Item>
          <Item typeName="String" key="ENaNCT">无数据</Item>
          <Item typeName="Int32" key="Color">-1146130</Item>
          <Item typeName="String" key="ColorRGB">238,130,238</Item>
          <Item typeName="IList" key="ValueRangeVec" />
        </Item>
      </Item>
      <Item typeName="IDictionary" key="OtherAlghirithm">
        <Item typeName="String" key="Name">其他</Item>
        <Item typeName="Boolean" key="IsSpecial">False</Item>
        <Item typeName="String" key="ENaNCM">有数据</Item>
        <Item typeName="String" key="ENaNCU">有数据</Item>
        <Item typeName="String" key="ENaNCT">有数据</Item>
        <Item typeName="Int32" key="Color">-16711681</Item>
        <Item typeName="String" key="ColorRGB">0,255,255</Item>
        <Item typeName="IList" key="ValueRangeVec" />
      </Item>
    </Item>
  </Config>
</Configs>