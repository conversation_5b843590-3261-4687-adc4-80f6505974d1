﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class AttributePanel : UserControl
    {
        private string attribute;

        private string name;

        private ItemSelectionPanel item;

        private Label lbCnt;

        public AttributePanel()
        {
            InitializeComponent();
            lbCnt = new Label();
            lbCnt.TextChanged += new EventHandler(lbCnt_TextChanged);
        }

        public void SetAttribute(string attribute, string name)
        {
            this.attribute = attribute;
            this.name = name;
            listViewShow.Items.Clear();
            if (MainModel.GetInstance().CategoryManager[attribute] != null)
            {
                item = new ItemSelectionPanel(toolStripDropDownAttribute, listViewShow, lbCnt, MainModel.GetInstance().MainForm.ItemSelection, attribute, true);
                toolStripDropDownAttribute.Items.Clear();
                item.FreshItems();
                toolStripDropDownAttribute.Items.Add(new ToolStripControlHost(item));
            }

            groupBox.Text = string.Format("{0}[0]", name);
        }

        public void FillData(List<int> ids)
        {
            listViewShow.Items.Clear();
            CategoryEnum category = (CategoryEnum)CategoryManager.GetInstance()[attribute];
            foreach (int servID in ids)
            {
                CategoryEnumItem eItem = category[servID];
                if (eItem == null)
                {
                    continue;
                }
                ListViewItem lvi = new ListViewItem();
                lvi.Text = eItem.Description;
                lvi.Tag = eItem.ID;
                listViewShow.Items.Add(lvi);
            }
            lbCnt.Text = string.Format("[{0}]", listViewShow.Items.Count);
        }

        public List<int> GetAttribute()
        {
            List<int> attributeVec = new List<int>();
            foreach (ListViewItem lv in listViewShow.Items)
            {
                attributeVec.Add((int)lv.Tag);
            }
            return attributeVec;
        }

        private void buttonSel_Click(object sender, EventArgs e)
        {
            Point pt = new Point(buttonSel.Width, buttonSel.Height);
            toolStripDropDownAttribute.Show(buttonSel, pt, ToolStripDropDownDirection.BelowLeft);
        }

        private void lbCnt_TextChanged(object sender, EventArgs e)
        {
            groupBox.Text = string.Format("{0}{1}", name, lbCnt.Text);
        }
    }
}
