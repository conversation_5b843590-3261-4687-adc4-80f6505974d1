﻿using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NRVoiceAnaByFreqBandForm : MinCloseForm
    {
        public NRVoiceAnaByFreqBandForm()
        {
            InitializeComponent();
        }

        List<NRVoiceAnaByFreqBandFileResult> resList;
        public void FillData(List<NRVoiceAnaByFreqBandFileResult> resList)
        {
            this.resList = resList;
            gridControl.DataSource = resList;
            gridControl.RefreshDataSource();
        }

        private void miExportXlsSum_Click(object sender, EventArgs e)
        {
            List<NPOIRow> rows = new List<NPOIRow>();
            NPOIRow row = new NPOIRow();
            rows.Add(row);
            row.AddCellValue("文件名");
            row.AddCellValue("频段");
            row.AddCellValue("占用小区数");
            row.AddCellValue("主叫试呼次数");
            row.AddCellValue("主叫接通次数");
            row.AddCellValue("被叫接通次数");
            row.AddCellValue("主叫掉话次数");
            row.AddCellValue("被叫掉话次数");
            row.AddCellValue("采样点占比");
            row.AddCellValue("采样点占比-分子");
            row.AddCellValue("采样点占比-分母");
            row.AddCellValue("RSRP≥-110占比");
            row.AddCellValue("RSRP≥-110占比-分子");
            row.AddCellValue("RSRP≥-110占比-分母");
            row.AddCellValue("SINR≥-3占比");
            row.AddCellValue("SINR≥-3占比-分子");
            row.AddCellValue("SINR≥-3占比-分母");
            row.AddCellValue("SINR≥0占比");
            row.AddCellValue("SINR≥0占比-分子");
            row.AddCellValue("SINR≥0占比-分母");
            row.AddCellValue("MOS≥3占比");
            row.AddCellValue("MOS≥3占比-分子");
            row.AddCellValue("MOS≥3占比-分母");
            row.AddCellValue("RSRP均值");
            row.AddCellValue("SINR均值");
            row.AddCellValue("覆盖率(RSRP≥-110dBm & SINR≥-3)");
            row.AddCellValue("覆盖率(RSRP≥-110dBm & SINR≥-3)-分子");
            row.AddCellValue("覆盖率(RSRP≥-110dBm & SINR≥-3)-分母");
    
            foreach (NRVoiceAnaByFreqBandFileResult fileRes in resList)
            {
                row = new NPOIRow();
                rows.Add(row);
                row.AddCellValue(fileRes.FileName);
                foreach (NRVoiceAnaByFreqBandResult freq in fileRes.ResList)
                {
                    NPOIRow subRow = new NPOIRow();
                    row.AddSubRow(subRow);
                    subRow.AddCellValue(freq.FreqBand);
                    subRow.AddCellValue(freq.CellCount);
                    subRow.AddCellValue(freq.MoCallInfo.CallRequestCount);
                    subRow.AddCellValue(freq.MoCallInfo.CallEstablishedCount);
                    subRow.AddCellValue(freq.MtCallInfo.CallEstablishedCount);
                    subRow.AddCellValue(freq.MoCallInfo.DropCallCount);
                    subRow.AddCellValue(freq.MtCallInfo.DropCallCount);
                    subRow.AddCellValue(freq.SampleRate.RateDesc);
                    subRow.AddCellValue(freq.SampleRate.Count);
                    subRow.AddCellValue(freq.SampleRate.TotalCount);
                    subRow.AddCellValue(freq.RsrpMoreThan110Rate.RateDesc);
                    subRow.AddCellValue(freq.RsrpMoreThan110Rate.Count);
                    subRow.AddCellValue(freq.RsrpMoreThan110Rate.TotalCount);
                    subRow.AddCellValue(freq.SinrMoreThan3Rate.RateDesc);
                    subRow.AddCellValue(freq.SinrMoreThan3Rate.Count);
                    subRow.AddCellValue(freq.SinrMoreThan3Rate.TotalCount);
                    subRow.AddCellValue(freq.SinrMoreThan0Rate.RateDesc);
                    subRow.AddCellValue(freq.SinrMoreThan0Rate.Count);
                    subRow.AddCellValue(freq.SinrMoreThan0Rate.TotalCount);
                    subRow.AddCellValue(freq.MosMoreThan3Rate.RateDesc);
                    subRow.AddCellValue(freq.MosMoreThan3Rate.Count);
                    subRow.AddCellValue(freq.MosMoreThan3Rate.TotalCount);
                    subRow.AddCellValue(freq.RsrpData.AvgDesc);
                    subRow.AddCellValue(freq.SinrData.AvgDesc);
                    subRow.AddCellValue(freq.CoverRate.RateDesc);
                    subRow.AddCellValue(freq.CoverRate.Count);
                    subRow.AddCellValue(freq.CoverRate.TotalCount);
                }
            }
            ExcelNPOIManager.ExportToExcel(rows);
        }
    }
}
