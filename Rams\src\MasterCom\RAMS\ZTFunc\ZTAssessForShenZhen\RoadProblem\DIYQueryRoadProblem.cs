﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;
using MasterCom.RAMS.ZTFunc;

namespace MasterCom.RAMS.Net
{
    public class DIYQueryRoadProblem : QueryBase
    {
        /// <summary>
        /// 数据类型，0：GSM，1：TD
        /// </summary>
        int dataType = 0;
        /// <summary>
        /// 问题状态，0：全部，1：已创建，2：已关闭
        /// </summary>
        int status;
        public bool ShowConditionSetting { get; set; }
        private DIYQueryRoadProblem(MainModel mainModel)
            : base(mainModel)
        {
        }
        public override bool IsNeedSetQueryCondition
        {
            get
            {
                return false;
            }
        }
        private static DIYQueryRoadProblem queryRoadProblem = null;

        public static DIYQueryRoadProblem GetInstance()
        {
            if (queryRoadProblem == null)
            {
                queryRoadProblem = new DIYQueryRoadProblem(MainModel.GetInstance());
            }
            return queryRoadProblem;
        }

        public override string Name
        {
            get { return "道路问题点"; }
        }

        public override string IconName
        {
            get { return ""; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 18000, 18002, this.Name);
        }

        protected override bool isValidCondition()
        {
            return true;
        }

        RoadProblemDlg conditionDlg = null;
        protected override void query()
        {
            if (ShowConditionSetting)
            {
                if (conditionDlg == null)
                {
                    conditionDlg = new RoadProblemDlg();
                }
                if (conditionDlg.ShowDialog() == System.Windows.Forms.DialogResult.OK)
                {
                    conditionDlg.GetCondition(out dataType, out status);
                }
                else
                {
                    return;
                }
            }
            WaitBox.Show("开始查询道路问题点...", queryInThread);
            fireShowForm();
        }

        private void fireShowForm()
        {
            object obj = MainModel.GetInstance().GetObjectFromBlackboard(typeof(RoadProblemForm).FullName);
            RoadProblemForm roadProblemForm = obj == null ? null : obj as RoadProblemForm;
            if (roadProblemForm == null || roadProblemForm.IsDisposed)
            {
                roadProblemForm = new RoadProblemForm(MainModel);
            }
            roadProblemForm.FillData(dataType, status);
            if (!roadProblemForm.Visible)
            {
                roadProblemForm.Show(MainModel.MainForm);
            }
        }

        private void queryInThread()
        {
            try
            {
                MainModel.AllRoadProblemsDic.Clear();
                string tableName = "tb_sz_road_td_problem";
                if (dataType == 0)
                {
                    tableName = "tb_sz_road_gsm_problem";
                }
                else if (dataType == 1)
                {
                    tableName = "tb_sz_road_td_problem";
                }
                string sqlGridProblem = "select A.ID, A.areaname, A.level, A.roadname, B.roadDistance, A.status," +
                    " A.repeatlast, A.createdyear, A.createdbatch, A.beginyear, A.beginBatch, A.lastAbnormalYear," +
                    " A.lastAbnormalBatch, A.closedYear, A.closedBatch, A.lasttestyear, A.lasttestbatch," +
                    " A.gooddayscount, A.validatestatus, A.lastvalidateyear, A.lastvalidatebatch, A.gridrepeatcount" +
                    " from " + tableName + " A " +
                    " left join tb_sz_road_info B on A.areaname = B.areaname and A.level = B.level " +
                    " and A.roadname = B.roadname" +
                    " order by A.ID";
                WaitBox.Text = "开始获取道路问题点...";
                DIYSQLRoadProblem roadQuery = new DIYSQLRoadProblem(MainModel, sqlGridProblem);
                roadQuery.Query();
                if (MainModel.AllRoadProblemsDic.Count == 0)
                {
                    return;
                }
            }
            catch
            {
                //continue
            }
            finally
            {
                WaitBox.Close();
            }
        }

        private class DIYSQLRoadProblem : DIYSQLBase
        {
            readonly string sql;
            public DIYSQLRoadProblem(MainModel mainModel, string sql)
                : base(mainModel)
            {
                this.sql = sql;
            }
            protected override string getSqlTextString()
            {
                return sql;
            }

            protected override E_VType[] getSqlRetTypeArr()
            {
                E_VType[] rType = new E_VType[22];
                rType[0] = E_VType.E_Int;
                rType[1] = E_VType.E_String;
                rType[2] = E_VType.E_Int;
                rType[3] = E_VType.E_String;
                rType[4] = E_VType.E_Int;
                rType[5] = E_VType.E_Int;
                rType[6] = E_VType.E_Int;
                rType[7] = E_VType.E_Int;
                rType[8] = E_VType.E_Int;
                rType[9] = E_VType.E_Int;
                rType[10] = E_VType.E_Int;
                rType[11] = E_VType.E_Int;
                rType[12] = E_VType.E_Int;
                rType[13] = E_VType.E_Int;
                rType[14] = E_VType.E_Int;
                rType[15] = E_VType.E_Int;
                rType[16] = E_VType.E_Int;
                rType[17] = E_VType.E_Int;
                rType[18] = E_VType.E_Int;
                rType[19] = E_VType.E_Int;
                rType[20] = E_VType.E_Int;
                rType[21] = E_VType.E_Int;
                return rType;
            }

            protected override void receiveRetData(ClientProxy clientProxy)
            {
                Package package = clientProxy.Package;
                int index = 0;
                int progress = 0;
                while (true)
                {
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                    clientProxy.Recieve();
                    package.Content.PrepareGetParam();
                    if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                    {
                        RoadProblem road = new RoadProblem();
                        road.Fill(package.Content);
                        MainModel.AllRoadProblemsDic[road.ID] = road;
                        //do your code here
                    }
                    else if (package.Content.Type == ResponseType.END)
                    {
                        break;
                    }
                    else
                    {
                        log.Error("Unexpected type: " + package.Content.Type);
                        break;
                    }
                    if (Math.Log(index++) * (100000 / 10000) > WaitBox.ProgressPercent)
                    {
                        progress++;
                        if (progress > 95)
                        {
                            progress = 5;
                            index = 0;
                        }
                        if (progress % 5 == 0)
                        {
                            WaitBox.ProgressPercent = progress;
                        }
                    }
                }
            }

            public override string Name
            {
                get { return "DIYSQLRoadProblem"; }
            }
        }
    }
}
