﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class NRUnknownDisturbDlg
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(LTEUnknownDisturbDlg));
            this.button1 = new System.Windows.Forms.Button();
            this.button2 = new System.Windows.Forms.Button();
            this.label1 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.label5 = new System.Windows.Forms.Label();
            this.label6 = new System.Windows.Forms.Label();
            this.numMinDiffer = new System.Windows.Forms.NumericUpDown();
            this.numMaxSINR = new System.Windows.Forms.NumericUpDown();
            this.numMinDistance = new System.Windows.Forms.NumericUpDown();
            this.chkTime = new System.Windows.Forms.CheckBox();
            this.label7 = new System.Windows.Forms.Label();
            this.numTime = new System.Windows.Forms.NumericUpDown();
            ((System.ComponentModel.ISupportInitialize)(this.numMinDiffer)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxSINR)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMinDistance)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numTime)).BeginInit();
            this.SuspendLayout();
            // 
            // button1
            // 
            this.button1.DialogResult = System.Windows.Forms.DialogResult.OK;
            this.button1.Location = new System.Drawing.Point(117, 190);
            this.button1.Margin = new System.Windows.Forms.Padding(2);
            this.button1.Name = "button1";
            this.button1.Size = new System.Drawing.Size(65, 25);
            this.button1.TabIndex = 0;
            this.button1.Text = "确认";
            this.button1.UseVisualStyleBackColor = true;
            // 
            // button2
            // 
            this.button2.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.button2.Location = new System.Drawing.Point(197, 190);
            this.button2.Margin = new System.Windows.Forms.Padding(2);
            this.button2.Name = "button2";
            this.button2.Size = new System.Drawing.Size(65, 25);
            this.button2.TabIndex = 1;
            this.button2.Text = "取消";
            this.button2.UseVisualStyleBackColor = true;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(26, 27);
            this.label1.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(180, 14);
            this.label1.TabIndex = 2;
            this.label1.Text = "主服小区RSRP - 最强邻区RSRP≥";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(304, 21);
            this.label2.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(31, 14);
            this.label2.TabIndex = 3;
            this.label2.Text = "dBm";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(164, 67);
            this.label3.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(42, 14);
            this.label3.TabIndex = 4;
            this.label3.Text = "SINR<";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(304, 61);
            this.label4.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(21, 14);
            this.label4.TabIndex = 5;
            this.label4.Text = "dB";
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(142, 107);
            this.label5.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(64, 14);
            this.label5.TabIndex = 10;
            this.label5.Text = "持续距离≥";
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(304, 101);
            this.label6.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(19, 14);
            this.label6.TabIndex = 11;
            this.label6.Text = "米";
            // 
            // numMinDiffer
            // 
            this.numMinDiffer.Location = new System.Drawing.Point(219, 19);
            this.numMinDiffer.Margin = new System.Windows.Forms.Padding(2, 3, 2, 3);
            this.numMinDiffer.Maximum = new decimal(new int[] {
            166,
            0,
            0,
            0});
            this.numMinDiffer.Minimum = new decimal(new int[] {
            6,
            0,
            0,
            0});
            this.numMinDiffer.Name = "numMinDiffer";
            this.numMinDiffer.Size = new System.Drawing.Size(79, 22);
            this.numMinDiffer.TabIndex = 0;
            this.numMinDiffer.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numMinDiffer.Value = new decimal(new int[] {
            6,
            0,
            0,
            0});
            // 
            // numMaxSINR
            // 
            this.numMaxSINR.Location = new System.Drawing.Point(219, 59);
            this.numMaxSINR.Margin = new System.Windows.Forms.Padding(2);
            this.numMaxSINR.Maximum = new decimal(new int[] {
            50,
            0,
            0,
            0});
            this.numMaxSINR.Minimum = new decimal(new int[] {
            50,
            0,
            0,
            -2147483648});
            this.numMaxSINR.Name = "numMaxSINR";
            this.numMaxSINR.Size = new System.Drawing.Size(79, 22);
            this.numMaxSINR.TabIndex = 13;
            this.numMaxSINR.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            // 
            // numMinDistance
            // 
            this.numMinDistance.Location = new System.Drawing.Point(219, 99);
            this.numMinDistance.Margin = new System.Windows.Forms.Padding(2);
            this.numMinDistance.Name = "numMinDistance";
            this.numMinDistance.Size = new System.Drawing.Size(79, 22);
            this.numMinDistance.TabIndex = 14;
            this.numMinDistance.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numMinDistance.Value = new decimal(new int[] {
            50,
            0,
            0,
            0});
            // 
            // chkTime
            // 
            this.chkTime.AutoSize = true;
            this.chkTime.Location = new System.Drawing.Point(132, 143);
            this.chkTime.Margin = new System.Windows.Forms.Padding(2);
            this.chkTime.Name = "chkTime";
            this.chkTime.Size = new System.Drawing.Size(74, 18);
            this.chkTime.TabIndex = 15;
            this.chkTime.Text = "持续时间";
            this.chkTime.UseVisualStyleBackColor = true;
            this.chkTime.CheckedChanged += new System.EventHandler(this.chkTime_CheckedChanged);
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(304, 141);
            this.label7.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(19, 14);
            this.label7.TabIndex = 17;
            this.label7.Text = "秒";
            // 
            // numTime
            // 
            this.numTime.Location = new System.Drawing.Point(219, 139);
            this.numTime.Margin = new System.Windows.Forms.Padding(2, 3, 2, 3);
            this.numTime.Maximum = new decimal(new int[] {
            3600,
            0,
            0,
            0});
            this.numTime.Name = "numTime";
            this.numTime.Size = new System.Drawing.Size(79, 22);
            this.numTime.TabIndex = 18;
            this.numTime.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numTime.Value = new decimal(new int[] {
            10,
            0,
            0,
            0});
            // 
            // LTEUnknownDisturbDlg
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(361, 226);
            this.Controls.Add(this.numTime);
            this.Controls.Add(this.label7);
            this.Controls.Add(this.chkTime);
            this.Controls.Add(this.numMinDistance);
            this.Controls.Add(this.numMaxSINR);
            this.Controls.Add(this.numMinDiffer);
            this.Controls.Add(this.label6);
            this.Controls.Add(this.label5);
            this.Controls.Add(this.label4);
            this.Controls.Add(this.label3);
            this.Controls.Add(this.label2);
            this.Controls.Add(this.label1);
            this.Controls.Add(this.button2);
            this.Controls.Add(this.button1);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Margin = new System.Windows.Forms.Padding(2);
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "LTEUnknownDisturbDlg";
            this.Text = "不明干扰条件设置";
            ((System.ComponentModel.ISupportInitialize)(this.numMinDiffer)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxSINR)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMinDistance)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numTime)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.Button button1;
        private System.Windows.Forms.Button button2;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.NumericUpDown numMinDiffer;
        private System.Windows.Forms.NumericUpDown numMaxSINR;
        private System.Windows.Forms.NumericUpDown numMinDistance;
        private System.Windows.Forms.NumericUpDown numMaxTPDistance;
        private System.Windows.Forms.CheckBox chkDistance;
        private System.Windows.Forms.CheckBox chkTime;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.NumericUpDown numTime;
    }
}