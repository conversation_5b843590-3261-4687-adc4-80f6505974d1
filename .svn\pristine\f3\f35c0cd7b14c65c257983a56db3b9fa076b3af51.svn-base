﻿using DevExpress.XtraEditors;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc;
using MasterCom.RAMS.ZTFunc.ZTRegionGridFilter;
using MasterCom.Util;
using System.Collections.Generic;
using System.Windows.Forms;

namespace MasterCom.RAMS.KPI_Statistics
{
    /// <summary>
    /// 设定半径，以指定小区的半径范围内的总采样点数、该小区采样点数、采样点占比、90覆盖率、94覆盖率、该小区90覆盖率、94覆盖率
    /// </summary>
    public class ZTCellCoverTooCloseByRatius : QueryKPIStatBase
    {
        private static ZTCellCoverTooCloseByRatius intance = null;
        protected static readonly object lockObj = new object();
        public static ZTCellCoverTooCloseByRatius GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new ZTCellCoverTooCloseByRatius();
                    }
                }
            }
            return intance;
        }

        protected ZTCellCoverTooCloseByRatius()
            : base(MainModel.GetInstance())
        {
            
        }

        public override string Name
        {
            get { return "小区覆盖过近_GSM"; }
        }

        public override string IconName
        {
            get { return null; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 12000, 12086, this.Name);
        }

        protected Dictionary<string, CellCoverTooClose> cellTooCloseRetDic = new Dictionary<string, CellCoverTooClose>();
        public int radius { get; set; } = 0;
        public double lngCell { get; set; } = 0;
        public double latCell { get; set; } = 0;
        Cell selectedCell = null;

        RadiusGridDataInfo ragData = null;//指定半径内小区的
        RadiusGridDataInfo cellRagData = null;//属于小区的栅格
        RadiusGridDataInfo notCellRagData = null;//不属于小区的栅格
        CellCoverTooClose covTooClose = null;


        protected override StatTbToken getTableNameToken()
        {
            return StatTbToken.cell_grid;
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.Cell;
        }

        public override bool CanEnabled(Model.SearchGeometrys searchGeometrys)
        {
            if (searchGeometrys.SelectedCell == null)
            {
                return false;
            }

            selectedCell = searchGeometrys.SelectedCell;
            return true;
        }

        protected override string getStatImgNeededTriadID(params object[] paramSet)
        {
            Dictionary<string, bool> formulaSet = new Dictionary<string, bool>();
            formulaSet["Mx_640101"] = true;
            formulaSet["Mx_640117"] = true;
            formulaSet["Mx_64010A"] = true;
            formulaSet["Mx_640109"] = true;
            formulaSet["Mx_640108"] = true;
            formulaSet["Mx_640107"] = true;
            return getTriadIDIgnoreServiceType(formulaSet.Keys);
        }

        protected override void queryPeriodInfo(TimePeriod period, ClientProxy clientProxy, params object[] reservedParams)
        {
            //query kpi stat only
            isQueringEvent = false;
            preparePackageBasicContent(clientProxy.Package, period, reservedParams);
            preparePackageNeededInfo_StatImg(clientProxy.Package, reservedParams);
            clientProxy.Send();
            recieveInfo_ImgGrid(clientProxy, reservedParams);
            afterRecieveOnePeriodData(period);
        }


        protected override void AddExtraCondition(Package package, params object[] reservedParams)
        {
            lngCell = selectedCell.Longitude;
            latCell = selectedCell.Latitude;

            double longParam = radius * 0.000009;
            double latParam = radius * 0.00001;
            package.Content.AddParam((byte)OpOptionDef.AreaSelectIntersect);
            package.Content.AddParam(lngCell - longParam);
            package.Content.AddParam(latCell + latParam);
            package.Content.AddParam(lngCell + longParam);
            package.Content.AddParam(latCell - latParam);
            AddDIYEndOpFlag(package);
        }

        protected override void AddGeographicFilter(Package package)
        {
            if (isQueringEvent)
            {
                AddDIYEndOpFlag(package);
                this.AddDIYRegion_Sample(package);
            }
            else
            {
                this.AddDIYRegion_Intersect(package);
            }
        }

        protected override void preparePackageCommand(Package package)
        {
            if (isQueringEvent)
            {
                package.Command = Command.DIYSearch;
                package.SubCommand = SubCommand.Request;
                package.Content.Type = RequestType.REQTYPE_DIY_AREA_EVENT;
                package.Content.PrepareAddParam();
            }
            else
            {
                package.Command = Command.DIYSearch;
                package.SubCommand = SubCommand.Request;
                package.Content.Type = RequestType.KPI_CELL_GRID;
                package.Content.PrepareAddParam();
            }
        }

        /// <summary>
        /// 判断栅格中心点到小区的距离是否在半径范围内
        /// </summary>
        /// <param name="lng">栅格中心点经纬度</param>
        /// <param name="lat"></param>
        /// <param name="lng2">小区经纬度</param>
        /// <param name="lat2"></param>
        /// <param name="radius"></param>
        /// <returns></returns>
        protected bool isValidPoint(double lng, double lat, double lng2, double lat2, int radius)
        {
            return MathFuncs.GetDistance(lng, lat, lng2, lat2) < radius;
        }

        protected override void recieveAndHandleSpecificStatData(Package package, List<Model.Interface.StatImgDefItem> curImgColumnDef, KPIStatDataBase singleStatData)
        {
            int lac = package.Content.GetParamInt();
            int ci = package.Content.GetParamInt();
            double lng = package.Content.GetParamDouble();  //采样点栅格左上角经纬度
            double lat = package.Content.GetParamDouble();
            ColorUnit cu = new ColorUnit();
            cu.LTLng = lng;
            cu.LTLat = lat;
            StatDataGSM sd = singleStatData as StatDataGSM;

            double centerLng = cu.CenterLng;  //栅格中心点经纬度
            double centerLat = cu.CenterLat;
            
            lngCell = selectedCell.Longitude;   //选中小区的经纬度
            latCell = selectedCell.Latitude;
            if (isValidPoint(centerLng, centerLat, lngCell, latCell, radius))//比较栅格点与选中小区的距离
            {
                if (!cellTooCloseRetDic.TryGetValue(selectedCell.Name, out covTooClose))
                {
                    covTooClose = new CellCoverTooClose();
                    covTooClose.cell = selectedCell;
                    cellTooCloseRetDic[selectedCell.Name] = covTooClose;
                }
                fillStatData(package, curImgColumnDef, sd);

                if (selectedCell.LAC == lac && selectedCell.CI == ci)
                {
                    cellRagData.AddGridData(sd, selectedCell.Name);//属于小区的栅格

                }
                else
                {
                    notCellRagData.AddGridData(sd, "");
                }

                ragData.AddGridData(sd, "");//所有栅格

            }

        }

        ZTCellCoverTooCloseRatiusSetForm rDlg = null;
        protected override bool getConditionBeforeQuery()
        {
            cellTooCloseRetDic.Clear();
            ragData = null;
            cellRagData = null;
            notCellRagData = null;
            ragData = new RadiusGridDataInfo();
            cellRagData = new RadiusGridDataInfo();
            notCellRagData = new RadiusGridDataInfo();

            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }
            if (rDlg == null)
            {
                rDlg = new ZTCellCoverTooCloseRatiusSetForm(this.GetType());
            }
            if (rDlg.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            radius = rDlg.GetRatiusRegion();

            return true;
        }

        /// <summary>
        /// 分别计算区域内总的和属于小区的90、94覆盖率，采样点数量、采样点占比等信息
        /// </summary>
        protected void doBeforeShowResult()
        {
            float radiusCov90Sum = 0;
            float radiusCov94Sum = 0;
            float CellRadiusCov90Sum = 0;
            float CellRadiusCov94Sum = 0;
            ragData.MakeSummary();
            cellRagData.MakeSummary();
            notCellRagData.MakeSummary();
            foreach (RadiusGridItem item in ragData.Grids)
            {
                covTooClose.testPointTotal += item.TestPointCount;
                radiusCov90Sum += item.Coverage90;
                radiusCov94Sum += item.Coverage94;

            }
            if(ragData.GridNum != 0)
            {
                covTooClose.region90coverage = float.Parse((radiusCov90Sum / ragData.GridNum).ToString("0.00"));
                covTooClose.region94coverage = float.Parse((radiusCov94Sum / ragData.GridNum).ToString("0.00"));
            }


            foreach (RadiusGridItem item in cellRagData.Grids)
            {
                covTooClose.cellTestPointCount += item.TestPointCount;
                CellRadiusCov90Sum += item.Coverage90;
                CellRadiusCov94Sum += item.Coverage94;
            }
            if(cellRagData.GridNum != 0)
            {
                covTooClose.cell90coverage = float.Parse((CellRadiusCov90Sum / cellRagData.GridNum).ToString("0.00"));
                covTooClose.cell94coverage = float.Parse((CellRadiusCov94Sum / cellRagData.GridNum).ToString("0.00"));
            }

            
        }

        protected override void fireShowResult()
        {
            doBeforeShowResult();

            if (cellTooCloseRetDic.Count == 0)
            {
                XtraMessageBox.Show(MainModel.MainForm, "没有符合条件的数据。");
                return;
            }
            ZTCellCoverTooCloseListForm cellCoverTooCloseListForm = MainModel.GetInstance().GetObjectFromBlackboard(typeof(ZTCellCoverTooCloseListForm)) as ZTCellCoverTooCloseListForm;
            if (cellCoverTooCloseListForm == null || cellCoverTooCloseListForm.IsDisposed)
            {
                cellCoverTooCloseListForm = new ZTCellCoverTooCloseListForm(MainModel);
            }
            cellCoverTooCloseListForm.showCellSet(cellTooCloseRetDic, radius, ragData, cellRagData, notCellRagData);
            cellCoverTooCloseListForm.Owner = MainModel.MainForm;
            cellCoverTooCloseListForm.Visible = true;
            cellCoverTooCloseListForm.BringToFront();
        }


    }


    public class CellCoverTooClose
    {
        public int testPointTotal { get; set; } = 0;
        public int cellTestPointCount { get; set; } = 0;
        public float region90coverage { get; set; } = 0;
        public float region94coverage { get; set; } = 0;
        public float cell90coverage { get; set; } = 0;
        public float cell94coverage { get; set; } = 0;

        public Cell cell { get; set; }

        public double longitude { get; set; }
        public double latitude { get; set; }
    }
}
