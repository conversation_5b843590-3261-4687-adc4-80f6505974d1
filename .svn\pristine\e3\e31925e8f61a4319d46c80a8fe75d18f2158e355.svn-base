﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Data;
using CQTLibrary.PublicItem;

namespace MasterCom.RAMS
{
    static class CQTDataProcessorForCell
    {
        public static DataSet GetDataForShowCell(Dictionary<LaiKey, Dictionary<DateTime, List<ParaResult>>> dataGet)
        {
            DataSet ds = new DataSet();
            Dictionary<string, List<object[]>> infShow = new Dictionary<string, List<object[]>>();
            foreach (LaiKey laiKey in dataGet.Keys)
            {
                foreach (DateTime timeInf in dataGet[laiKey].Keys)
                {
                    foreach (ParaResult para in dataGet[laiKey][timeInf])
                    {
                        object[] rowTemp = new object[7];
                        rowTemp[0] = timeInf.ToString("yyyy-MM-dd hh:mm:ss") ;
                        rowTemp[1] = para.StrKpiName;
                        rowTemp[2] = laiKey.Ilac;
                        rowTemp[3] = laiKey.Ici;
                        rowTemp[4] = para.StrAlarmType;
                        rowTemp[5] = para.FValue;
                        rowTemp[6] = para.StrValue;
                        if (infShow.ContainsKey(para.StrKpiName))
                        {
                            infShow[para.StrKpiName].Add(rowTemp);
                        }
                        else
                        {
                            List<object[]> listTemp = new List<object[]>();
                            listTemp.Add(rowTemp);
                            infShow.Add(para.StrKpiName, listTemp);
                        }
                    }
                }
            }
            foreach (string tbName in infShow.Keys)
            {
                DataTable dt = new DataTable(tbName);
                dt.Columns.Add("c1");
                dt.Columns.Add("c2");
                dt.Columns.Add("c3");
                dt.Columns.Add("c4");
                dt.Columns.Add("c5");
                dt.Columns.Add("c6");
                dt.Columns.Add("c7");
                for (int i = 0; i < infShow[tbName].Count; i++)
                {
                    DataRow rowTemp = dt.NewRow();
                    rowTemp["c1"] = infShow[tbName][i][0];
                    rowTemp["c2"] = infShow[tbName][i][1];
                    rowTemp["c3"] = infShow[tbName][i][2];
                    rowTemp["c4"] = infShow[tbName][i][3];
                    rowTemp["c5"] = infShow[tbName][i][4];
                    rowTemp["c6"] = infShow[tbName][i][5];
                    rowTemp["c7"] = infShow[tbName][i][6];
                    dt.Rows.Add(rowTemp);
                }
                ds.Tables.Add(dt);
            }
            return ds;
        }
    }
}
