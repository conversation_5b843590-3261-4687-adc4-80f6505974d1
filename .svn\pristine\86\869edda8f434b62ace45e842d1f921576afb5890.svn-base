﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;

namespace MasterCom.RAMS.Func
{
    public abstract class CreateChildForm:QueryBase
    {
        protected CreateChildForm(MainModel mm)
            : base(mm)
        { }
        public override bool IsNeedSetQueryCondition
        {
            get
            {
                return false;
            }
        }
        public override string Name
        {
            get { return ""; }
        }

        public override string IconName
        {
            get { return string.Empty; }
        }

        protected override bool isValidCondition()
        {
            return true;
        }
        protected MtAction action = null;
        protected override void query()
        {
            if (action == null)
            {
                initAction();
            }
            action.OnAction();
        }

        protected abstract void initAction();

    }
}
