﻿using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class LowSpeedRoadAnaBase_NR : DIYAnalyseByFileBackgroundBase
    {
        protected static readonly object lockObj = new object();

        LowSpeedRoadCondition_NR funcCond = new LowSpeedRoadCondition_NR();
        protected PercentRoadBuilder roadBuilder;
        protected PercentRoadBuilder roadBuilder_NR;
        protected PercentRoadBuilder roadBuilder_LTE;

        protected List<LowSpeedRoadInfo_NR> resultList = new List<LowSpeedRoadInfo_NR>();

        protected LowSpeedRoadAnaBase_NR()
            : base(MainModel.GetInstance())
        {
            this.IncludeEvent = false;
            this.Columns = new List<string>();
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 35000, 35012, this.Name);
        }
        protected override void getReadyBeforeQuery()
        {
            resultList.Clear();

            Columns = NRTpHelper.InitBaseReplayParamBackground(true, false);
            Columns.Add("mode");
            Columns.Add("NR_SS_RSSI");
            Columns.Add("NR_NCell_RSRP");
            Columns.Add("NR_APP_type");
            Columns.Add("NR_APP_Status");
            Columns.Add("NR_APP_Speed");
            Columns.Add("NR_Throughput_PDCP_DL");
            Columns.Add("NR_Throughput_PDCP_UL");
            Columns.Add("NR_Throughput_MAC_DL");
            Columns.Add("NR_Throughput_MAC_UL");
            Columns.Add("NR_16QAM_Count_DL");
            Columns.Add("NR_64QAM_Count_DL");
            Columns.Add("NR_256QAM_Count_DL");
            Columns.Add("NR_BPSK_Count_DL");
            Columns.Add("NR_QPSK_Count_DL");
            Columns.Add("NR_PDSCH_BLER");
            Columns.Add("NR_PUSCH_BLER");
            Columns.Add("NR_Avg_CQI");

            ServiceTypes = ServiceTypeManager.GetServiceTypesByServiceName(ServiceName.NR);
        }

        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                setRoadCond();
                return true;
            }

            LowSpeedRoadDlg_NR dlg = new LowSpeedRoadDlg_NR();
            dlg.SetCondition(funcCond);
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            funcCond = dlg.GetCondition();
            setRoadCond();
            return true;
        }

        protected void setRoadCond()
        {
            PercentRoadCondition roadCond = new PercentRoadCondition(funcCond.LowPercent / 100, onOneRoadComplete);
            roadCond.IsCheckDuration = false;
            roadCond.IsCheckMinLength = true;
            roadCond.IsCheckDistanceGap = true;
            roadCond.MaxDistanceGap = funcCond.TestPointDistance;

            this.roadBuilder = new PercentRoadBuilder(roadCond);
            this.roadBuilder_NR = new PercentRoadBuilder(roadCond);
            this.roadBuilder_LTE = new PercentRoadBuilder(roadCond);
        }

        protected override void doStatWithQuery()
        {
            try
            {
                foreach (DTFileDataManager fileDataManager in MainModel.DTDataManager.FileDataManagers)
                {
                    short? curRoadAppType = 0;
                    TestPoint.ECurrNetType curRoadNetType = TestPoint.ECurrNetType.Unknow;

                    foreach (TestPoint tp in fileDataManager.TestPoints)
                    {
                        if (WaitBox.CancelRequest)
                        {
                            return;
                        }
                        checkStopAndCreateNewRoad(tp, ref curRoadAppType, ref curRoadNetType);

                        addPointToRoad(curRoadNetType, tp);
                    }

                    stopRoading();
                }
            }
            catch (Exception ee)
            {
                MessageBox.Show(ee.Message + Environment.NewLine + ee.Source + Environment.NewLine + ee.StackTrace);
            }
        }

        private void addPointToRoad(TestPoint.ECurrNetType curRoadNetType, TestPoint tp)
        {
            if (this.funcCond.CheckSynthesisLowSpeed)
            {
                roadBuilder.AddPoint(tp, isValidLowSpeedTp(tp), NetTypeString.Synthesis);
            }
            if (curRoadNetType == TestPoint.ECurrNetType.LTE && this.funcCond.CheckLTELowSpeed)
            {
                roadBuilder_LTE.AddPoint(tp, isValidLowSpeedTp(tp), NetTypeString.LTE);
            }
            else if (curRoadNetType == TestPoint.ECurrNetType.NR && this.funcCond.CheckNRLowSpeed)
            {
                roadBuilder_NR.AddPoint(tp, isValidLowSpeedTp(tp), NetTypeString.NR);
            }
        }

        private void checkStopAndCreateNewRoad(TestPoint tp, ref short? curRoadAppType
            , ref TestPoint.ECurrNetType curRoadNetType)
        {
            short? curTpAppType = NRTpHelper.NrTpManager.GetAppType(tp);

            bool isAppTypeChange = curTpAppType != curRoadAppType && curTpAppType != (int)AppType.NR_RACH;
            bool isNetTypeChange = curRoadNetType != tp.NetworkType;

            if (isAppTypeChange && funcCond.CheckSynthesisLowSpeed)
            {
                this.roadBuilder.StopRoading(NetTypeString.Synthesis);
                setNewRoadMinLength(this.roadBuilder.RoadCond, curTpAppType);
            }

            if (isAppTypeChange || isNetTypeChange)
            {
                if (funcCond.CheckLTELowSpeed)
                {
                    this.roadBuilder_LTE.StopRoading(NetTypeString.LTE);
                    setNewRoadMinLength(this.roadBuilder_LTE.RoadCond, curTpAppType);
                }
                if (funcCond.CheckNRLowSpeed)
                {
                    this.roadBuilder_NR.StopRoading(NetTypeString.NR);
                    setNewRoadMinLength(this.roadBuilder_NR.RoadCond, curTpAppType);
                }

            }

            if (isAppTypeChange)
            {
                curRoadAppType = curTpAppType;
            }
            if (isNetTypeChange)
            {
                curRoadNetType = tp.NetworkType;
            }
        }

        private void stopRoading()
        {
            if (this.funcCond.CheckSynthesisLowSpeed)
            {
                roadBuilder.StopRoading(NetTypeString.Synthesis);
            }
            if (funcCond.CheckLTELowSpeed)
            {
                this.roadBuilder_LTE.StopRoading(NetTypeString.LTE);
            }
            if (funcCond.CheckNRLowSpeed)
            {
                this.roadBuilder_NR.StopRoading(NetTypeString.NR);
            }
        }

        private void setNewRoadMinLength(PercentRoadCondition roadCond, short? appType)
        {
            if (funcCond.SpeedTypeSelected == LowSpeedRoadCondition_NR.ESpeedType.APP)
            {
                switch (appType)
                {
                    case (int)AppType.FTP_Download:
                        roadCond.MinLength = funcCond.DistanceFTPDLMin;
                        break;
                    case (int)AppType.FTP_Upload:
                        roadCond.MinLength = funcCond.DistanceFTPULMin;
                        break;
                    case (int)AppType.Http_Download:
                        roadCond.MinLength = funcCond.DistanceHTTPMin;
                        break;
                    case (int)AppType.Email_SMTP:
                        roadCond.MinLength = funcCond.DistanceEMailMin;
                        break;
                    case (int)AppType.Http_Video:
                        roadCond.MinLength = funcCond.DistanceVideoMin;
                        break;
                    default:
                        break;
                }
            }
            else if (appType == (int)AppType.FTP_Download)
            {
                roadCond.MinLength = funcCond.DistanceThroughputDlMin;
            }
            else if (appType == (int)AppType.FTP_Upload)
            {
                roadCond.MinLength = funcCond.DistanceThroughputUlMin;
            }
        }

        protected virtual bool isValidLowSpeedTp(TestPoint tp)
        {
            if (tp is TestPoint_NR && isValidTestPoint(tp))
            {
                bool ignore = funcCond.IsIgnoreByRsrp(tp) || funcCond.IsIgnoreBySinr(tp);
                if (!ignore)
                {
                    return funcCond.IsLowSpeed(tp);
                }
            }
            return false;
        }

        protected void onOneRoadComplete(object sender, PercentRoadItem roadItem)
        {
            PercentRoadBuilder curRoadBuilder = sender as PercentRoadBuilder;
            double curLowPercent = Math.Round(roadItem.ValidPercent * 100, 2);
            if (curLowPercent < funcCond.LowPercent || roadItem.Length < curRoadBuilder.RoadCond.MinLength)
            {
                return;
            }

            LowSpeedRoadInfo_NR info = new LowSpeedRoadInfo_NR();
            info.Distance = roadItem.Length;
            info.Duration = roadItem.Duration;
            info.TpCount_LowSpeed = roadItem.ValidCount;
            info.TpRate_LowSpeed = curLowPercent;
            info.NetType = roadItem.NetType;
            info.SetTestPoints(roadItem.TestPoints, funcCond.GetSpeed);

            info.SN = resultList.Count + 1;
            resultList.Add(info);
        }

        protected override void fireShowForm()
        {
            if (resultList.Count == 0)
            {
                System.Windows.Forms.MessageBox.Show("没有符合条件的信息！");
                return;
            }
            LowSpeedRoadInfoForm_NR frm = MainModel.CreateResultForm(typeof(LowSpeedRoadInfoForm_NR)) as LowSpeedRoadInfoForm_NR;
            frm.FillData(resultList);
            frm.Owner = MainModel.MainForm;
            frm.Visible = true;
            frm.BringToFront();

            if (funcCond.SpeedTypeSelected == LowSpeedRoadCondition_NR.ESpeedType.APP)
            {
                mainModel.FireSetDefaultMapSerialTheme("NR_APP_Speed_Mb");
            }
            else if (funcCond.SpeedTypeSelected == LowSpeedRoadCondition_NR.ESpeedType.MAC)
            {
                mainModel.FireSetDefaultMapSerialTheme("NR_Throughput_MAC_DL_Mb");
                mainModel.FireSetDefaultMapSerialTheme("NR_Throughput_MAC_UL_Mb");
            }
            else
            {
                mainModel.FireSetDefaultMapSerialTheme("NR_Throughput_PDCP_DL_Mb");
                mainModel.FireSetDefaultMapSerialTheme("NR_Throughput_PDCP_UL_Mb");
            }
        }

        struct NetTypeString
        {
            public readonly static string Synthesis = "综合";
            public readonly static string LTE = "LTE";
            public readonly static string NR = "NR";
        }
    }

    public class LowSpeedRoadAnaByRegion_NR : LowSpeedRoadAnaBase_NR
    {
        private static LowSpeedRoadAnaByRegion_NR instance = null;
        public static LowSpeedRoadAnaByRegion_NR GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new LowSpeedRoadAnaByRegion_NR();
                    }
                }
            }
            return instance;
        }

        protected LowSpeedRoadAnaByRegion_NR()
            : base()
        {
        }

        public override string Name
        {
            get { return "低速率路段分析(按区域)"; }
        }

    }

    public class LowSpeedRoadAnaByFile_NR : LowSpeedRoadAnaBase_NR
    {
        private static LowSpeedRoadAnaByFile_NR intance = null;
        public static LowSpeedRoadAnaByFile_NR GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new LowSpeedRoadAnaByFile_NR();
                    }
                }
            }
            return intance;
        }

        protected LowSpeedRoadAnaByFile_NR()
            : base()
        {
        }
        public override string Name
        {
            get { return "低速率路段分析(按文件)"; }
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.File;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                return true;
            }
            return false;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            return true;
        }
    }
}
