﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.BackgroundFunc
{
    public partial class WeakRoadAutoSettingDlg : BaseForm
    {
        WeakRoadReport curReport = null;

        public WeakRoadAutoSettingDlg()
            : base()
        {
            InitializeComponent();
            servPanel = new ItemSelectionPanel(toolStripDropDownService, listViewService, lbSvCount, new MapFormItemSelection(), "ServiceType", false);
            servPanel.FreshItems();
            toolStripDropDownService.Items.Add(new ToolStripControlHost(servPanel));
            cbxSaveType.SelectedIndex = 0;

            if (MainModel.GetInstance().User.DBID == -1)
            {
                for (int districtId = 1; districtId <= DistrictManager.GetInstance().getDistrictCount(); districtId++)
                {
                    ListViewItem listViewItem = new ListViewItem();
                    listViewItem.Text = DistrictManager.GetInstance().getDistrictName(districtId);
                    listViewItem.Tag = districtId;
                    listViewCity.Items.Add(listViewItem);
                }
            }
            else
            {
                ListViewItem listViewItem = new ListViewItem();
                listViewItem.Text = DistrictManager.GetInstance().getDistrictName(MainModel.GetInstance().DistrictID);
                listViewItem.Tag = MainModel.GetInstance().DistrictID;
                listViewCity.Items.Add(listViewItem);
            }
            fillReportList(null);
        }
        public WeakRoadReport SelectedReport
        {
            get { return listAllReport.SelectedItem as WeakRoadReport; }
        }
        private void fillReportList(WeakRoadReport selectRpt)
        {
            listAllReport.SelectedIndexChanged -= listAllReport_SelectedIndexChanged;
            listAllReport.Items.Clear();
            listAllReport.SelectedIndexChanged += listAllReport_SelectedIndexChanged;
            foreach (WeakRoadReport rpt in WeakRoadAutoCfgManager.GetInstance().Reports)
            {
                listAllReport.Items.Add(rpt);
            }

            if (listAllReport.Items.Count > 0)
            {
                if (selectRpt != null)
                {
                    listAllReport.SelectedItem = selectRpt;
                }
                else
                {
                    listAllReport.SelectedIndex = 0;
                }
                curReport = listAllReport.SelectedItem as WeakRoadReport;
            }
            btnRemoveReport.Enabled = curReport != null;
        }

        private void listAllReport_SelectedIndexChanged(object sender, EventArgs e)
        {
            curReport = listAllReport.SelectedItem as WeakRoadReport;
            btnRemoveReport.Enabled = curReport != null;

            if (curReport != null)
            {
                numWeakPerMin.Value = (decimal)curReport.WeakPerMin;
                numRsrpMax.Value = (decimal)curReport.RsrpMax;
                numLastDisMax.Value = (decimal)curReport.DisLastMax;
                numLastDisMin.Value = (decimal)curReport.DisLastMin;
                numRecentMonths.Value = (decimal)curReport.RecentMonths;
                txtProjects.Text = curReport.Projects;
                txtFilePath.Text = curReport.FilePath;
                txtFileName.Text = curReport.FileName;
                txtFuncId.Text = curReport.FuncId.ToString();
                chkCanUse.Checked = curReport.IsCanUse;
                numTpCellDisMax.Value = (decimal)curReport.TpCellDisMax;
                numTpCellAngleMax.Value = (decimal)curReport.TpCellAngleMax;
                cbxSaveType.SelectedIndex = curReport.FileFilterIndex;

                setDistrictChecked();

                this.listViewService.Items.Clear();
                CategoryEnumItem[] svItems = ((CategoryEnum)CategoryManager.GetInstance()["ServiceType"]).Items;
                addServiceType(svItems);
                this.lbSvCount.Text = string.Format("[{0}]", this.listViewService.Items.Count);
            }
        }

        private void setDistrictChecked()
        {
            foreach (ListViewItem view in listViewCity.Items)
            {
                view.Checked = false;
                foreach (int id in curReport.DistrictIDSet)
                {
                    if (id == (int)view.Tag)
                    {
                        view.Checked = true;
                        break;
                    }
                }
            }
        }

        private void addServiceType(CategoryEnumItem[] svItems)
        {
            foreach (int id in curReport.ServiceIDSet)
            {
                foreach (CategoryEnumItem item in svItems)
                {
                    if (item.ID == id)
                    {
                        ListViewItem lvItem = new ListViewItem(item.Description);
                        lvItem.Tag = item.ID;
                        this.listViewService.Items.Add(lvItem);
                        break;
                    }
                }
            }
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            curReport = listAllReport.SelectedItem as WeakRoadReport;
            if (curReport != null)
            {
                List<int> districtList = new List<int>();
                foreach (ListViewItem view in listViewCity.Items)
                {
                    if (view.Checked)
                    {
                        districtList.Add((int)view.Tag);
                    }
                }

                List<int> serviceIDList = new List<int>();
                foreach (ListViewItem item in this.listViewService.Items)
                {
                    serviceIDList.Add((int)item.Tag);
                }

                string strError = judgeError(districtList, serviceIDList);

                if (strError != "")
                {
                    XtraMessageBox.Show(strError, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }
                curReport.WeakPerMin = (float?)numWeakPerMin.Value;
                curReport.RsrpMax = (float?)numRsrpMax.Value;
                curReport.DisLastMax = (float?)numLastDisMax.Value;
                curReport.DisLastMin = (float?)numLastDisMin.Value;
                curReport.RecentMonths = (int)numRecentMonths.Value;
                curReport.Projects = txtProjects.Text.Trim();
                curReport.FilePath = txtFilePath.Text;
                curReport.FileName = txtFileName.Text;
                curReport.FuncId = int.Parse(txtFuncId.Text.Trim());
                curReport.IsCanUse = chkCanUse.Checked;
                curReport.DistrictIDSet = districtList;
                curReport.ServiceIDSet = serviceIDList;
                curReport.TpCellDisMax = (int?)numTpCellDisMax.Value;
                curReport.TpCellAngleMax = (int?)numTpCellAngleMax.Value;
                curReport.FileFilterIndex = cbxSaveType.SelectedIndex;
            }
            WeakRoadAutoCfgManager.GetInstance().Save();
        }

        private string judgeError(List<int> districtList, List<int> serviceIDList)
        {
            string strError = "";
            if (numLastDisMax.Value < numLastDisMin.Value)
            {
                strError = "最小持续距离不能大于最大持续距离！";
            }
            else if (string.IsNullOrEmpty(txtProjects.Text))
            {
                strError = "文件类型不能为空！";
            }
            else if (districtList.Count == 0)
            {
                strError = "需至少选择一个地市！";
            }
            else if (serviceIDList.Count == 0)
            {
                strError = "需至少选择一种业务类型！";
            }
            else
            {
                int id = 0;
                if (txtFuncId.Text == "")
                {
                    strError = "需设置功能ID！";
                }
                else if (!int.TryParse(txtFuncId.Text, out id))
                {
                    strError = "功能ID需为数字！";
                }
            }

            return strError;
        }

        private void listAllReport_DoubleClick(object sender, EventArgs e)
        {
            if (listAllReport.SelectedItem is WeakRoadReport)
            {
                WeakRoadReport rpt = listAllReport.SelectedItem as WeakRoadReport;
                TextInputBox box = new TextInputBox("更改报表名称", "名称", rpt.Name);
                if (box.ShowDialog() == DialogResult.OK)
                {
                    rpt.Name = box.TextInput;
                    listAllReport.Invalidate();
                }
            }
        }

        private void listAllReport_DrawItem(object sender, DrawItemEventArgs e)
        {
            e.DrawBackground();
            e.DrawFocusRectangle();
            if (listAllReport.Items.Count > 0)
            {
                e.Graphics.DrawString(listAllReport.Items[e.Index].ToString(), e.Font, new SolidBrush(e.ForeColor), e.Bounds);
            }
        }

        private void btnRemoveReport_Click(object sender, EventArgs e)
        {
            if (MessageBox.Show(this, "确定删除该报表？删除后将不能恢复！", "确认", MessageBoxButtons.OKCancel) == DialogResult.OK)
            {
                WeakRoadAutoCfgManager.GetInstance().Reports.Remove(curReport);
                curReport = null;
                fillReportList(null);
            }
        }

        private void btnNewReport_Click(object sender, EventArgs e)
        {
            TextInputBox box = new TextInputBox("新建报表", "报表名称", "未命名报表");
            if (box.ShowDialog() == DialogResult.OK)
            {
                WeakRoadReport rpt = new WeakRoadReport(box.TextInput);
                WeakRoadAutoCfgManager.GetInstance().Reports.Add(rpt);
                fillReportList(rpt);
            }
        }

        private void btnSelectFilePath_Click(object sender, EventArgs e)
        {
            if (folderBrowserDialog1.ShowDialog() == DialogResult.OK)
            {
                txtFilePath.Text = folderBrowserDialog1.SelectedPath;
            }
        }

        ItemSelectionPanel servPanel;
        private void btnPopupService_Click(object sender, EventArgs e)
        {
            List<int> idSet = new List<int>();
            foreach (ListViewItem item in this.listViewService.Items)
            {
                idSet.Add((int)item.Tag);
            }
            this.servPanel.UpdateNodeState(idSet);
            System.Drawing.Point pt = new System.Drawing.Point(btnPopupService.Width, btnPopupService.Height);
            toolStripDropDownService.Show(btnPopupService, pt, ToolStripDropDownDirection.AboveLeft);
        }

        private void checkAllCity_CheckedChanged(object sender, EventArgs e)
        {
            if (checkAllCity.Checked)
            {
                for (int i = 0; i < listViewCity.Items.Count; i++)
                {
                    listViewCity.Items[i].Checked = true;
                }
            }
            else
            {
                for (int i = 0; i < listViewCity.Items.Count; i++)
                {
                    listViewCity.Items[i].Checked = false;
                }
            }
            listViewCity.Enabled = true;
        }
    }
}
