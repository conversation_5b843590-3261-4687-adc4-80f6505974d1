﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class DriveSpeedKpiInfo
    {
        public int SN
        {
            get;
            set;
        }
        public string FileName
        {
            get
            {
                string name = string.Empty;
                if (testPoints.Count > 0)
                {
                    name = testPoints[0].FileName;
                }
                return name;
            }
        }
        public double DriveSpeed { get; set; }
        public string DriveSpeedType { get; set; }
        public string KpiType { get; set; }
        public double CommonTpPercent { get; set; }
        public double RoadLength { get; set; }
        public double Duration { get; set; }

        private readonly List<TestPoint> testPoints = new List<TestPoint>();
        public List<TestPoint> TestPoints
        {
            get { return testPoints; }
        }

        private float totalSINR = 0;
        private int sinrNum = 0;
        public float? AvgSINR
        {
            get
            {
                if (sinrNum > 0)
                {
                    return (float)Math.Round(totalSINR / sinrNum, 2);
                }
                return null;
            }
        }

        private float totalRSRP = 0;
        private int rsrpNum = 0;
        public float? AvgRSRP
        {
            get
            {
                if (rsrpNum > 0)
                {
                    return (float)Math.Round(totalRSRP / rsrpNum, 2); 
                }
                return null;
            }
        }

        private double totalSpeed = 0;
        private double speedNum = 0;
        public double? AvgSpeed
        {
            get
            {
                if (speedNum > 0)
                {
                    return Math.Round(totalSpeed / speedNum, 2);
                }
                return null;
            }
        }

        public double MidLng
        {
            get
            {
                double lng = double.NaN;
                if (testPoints.Count > 0)
                {
                    lng = testPoints[(testPoints.Count / 2)].Longitude;
                }
                return lng;
            }
        }
        public double MidLat
        {
            get
            {
                double lat = double.NaN;
                if (testPoints.Count > 0)
                {
                    lat = testPoints[(testPoints.Count / 2)].Latitude;
                }
                return lat;
            }
        }
        internal void Add(float? sinr, float? rsrp, double? downloadSpeed, TestPoint testPoint)
        {
            if (sinr != null && sinr >= -50 && sinr <= 50)
            {
                sinrNum++;
                totalSINR += (float)sinr;
            }

            if (rsrp != null && rsrp >= -141 && rsrp <= 25)
            {
                rsrpNum++;
                totalRSRP += (float)rsrp;
            }
            if (downloadSpeed != null && downloadSpeed >= 0)
            {
                speedNum++;
                totalSpeed += (double)downloadSpeed;
            }
            testPoints.Add(testPoint);
        }
    }
}
