﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Model.Interface;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using System.Collections.ObjectModel;
using System.Drawing.Drawing2D;
using System.Drawing;
using MasterCom.MTGis;
using MapWinGIS;
using MasterCom.RAMS.Func.SystemSetting;
using MasterCom.RAMS.BackgroundFunc;
using System.Data;
using System.Data.SqlClient;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public class LTEDownloadPerformanceAna : ZTAntennaBase
    {
        public LTEDownloadPerformanceAna()
            : base(MainModel.GetInstance())
        {
        }

        #region 功能基本信息
        private static LTEDownloadPerformanceAna instance = null;
        protected static readonly object lockObj = new object();

        public static LTEDownloadPerformanceAna GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new LTEDownloadPerformanceAna();
                    }
                }
            }
            return instance;
        }

        public override string Name
        {
            get { return "LTE速率性能评估"; }
        }

        public override string IconName
        {
            get { return "Images/regionq.gif"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 28000, 28009, this.Name);
        }
        #endregion

        #region 变量
        string strCityTypeName = "";
        Dictionary<int, LTEDownloadPerformanceItem> lteDownloadPerformanceItemDic = null;
        Dictionary<int, List<LTEDataDownloadPerInfo>> lteDownloadPerformanceTpDic = null;
        Dictionary<LTECell, List<LTEDataDownloadPerInfo>> lteScanDownloadPerformanceTpDic = null;
        Dictionary<string, LteSampleMainInfo> dicLteOverlapData = null;
        Dictionary<string, LteSampleMainInfo> dicLteOverlapMode3Data = null;
        Dictionary<int, Dictionary<int, List<long>>> cellFileTestTimeDic = null;
        #endregion

        protected override void query()
        {
            if (!MainModel.IsBackground && !MainModel.QueryFromBackground)
            {
                setVarBeforQuery();
                InitRegionMop2();
            }
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
                MainModel.ClearDTData();
                initData();
                if (!MainModel.IsBackground)
                {
                    if (!MainModel.QueryFromBackground)
                    {
                        WaitBox.CanCancel = true;
                        WaitBox.Text = "正在查询...";
                        strCityTypeName = DistrictManager.GetInstance().getDistrictName(MainModel.DistrictID);
                        WaitBox.Show("读取数据分析...", queryInThread, clientProxy);
                        FireShowResultForm();
                    }
                    else
                    {
                        getBackgroundData();
                        initBackgroundImageDesc();
                    }
                    MainModel.FireSetDefaultMapSerialTheme("LTE", "RSRP");
                }
                else
                {
                    strCityTypeName = DistrictManager.GetInstance().getDistrictName(MainModel.DistrictID);
                    doBackgroundStatByPeriod(clientProxy);
                }
            }
            catch
            {
                clientProxy.Close();
            }
            finally
            {
                MainModel.ClearDTData();
                initData();
            }
        }

        private void initData()
        {
            lteDownloadPerformanceItemDic = new Dictionary<int, LTEDownloadPerformanceItem>();
            lteDownloadPerformanceTpDic = new Dictionary<int, List<LTEDataDownloadPerInfo>>();
            lteScanDownloadPerformanceTpDic = new Dictionary<LTECell, List<LTEDataDownloadPerInfo>>();
            dicLteOverlapData = new Dictionary<string, LteSampleMainInfo>();
            dicLteOverlapMode3Data = new Dictionary<string, LteSampleMainInfo>();
            cellFileTestTimeDic = new Dictionary<int, Dictionary<int, List<long>>>();
        }

        protected override void queryInThread(object o)
        {
            try
            {
                doSomethingBeforeQueryInThread();
                ClientProxy clientProxy = (ClientProxy)o;
                Package package = clientProxy.Package;
                int periodCount = Condition.Periods.Count;
                int index = 0;
                if (condition.IsByRound)
                {
                    queryPeriodInfo(clientProxy, package, null, true);
                }
                else
                {
                    foreach (TimePeriod period in Condition.Periods)
                    {
                        if (WaitBox.CancelRequest)
                        {
                            break;
                        }
                        WaitBox.Text = "开始统计时段[" + period.GetShortString() + "]内的数据...";
                        WaitBox.ProgressPercent = index / periodCount + 10;
                        queryPeriodInfo(clientProxy, package, period, false);
                    }
                }
                getResultAfterQuery();
            }
            catch (Exception ex)
            {
                log.Error(ex.Message + Environment.NewLine + ex.StackTrace);
            }
            finally
            {
                WaitBox.Close();
            }
        }

        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            List<object> columnsDef = new List<object>();
            Dictionary<string, object> param;
            #region 定义所需字段
            List<string> strParamList = new List<string>() { "lte_SCell_LAC", "lte_SCell_CI" , "lte_TAC", "lte_ECI", "lte_EARFCN" , "lte_PCI"
                ,"lte_PDCP_DL" ,"lte_MAC_DL", "lte_PHY_DL","lte_RSRP" ,"lte_RSRQ", "lte_SINR","lte_Wideband_CQI" ,"lte_PDSCH_BLER", "lte_PDCCH_BLER"
                ,"lte_Current_Network_Type" ,"lte_Times_QPSK_DLCode0", "lte_Times_QPSK_DLCode1" ,"lte_Times_QAM16_DLCode0", "lte_Times_QAM16_DLCode1"
                ,"lte_Times_QAM64_DLCode0" ,"lte_Times_QAM64_DLCode1", "lte_Times_MCS_DLCode0","lte_Times_MCS_DLCode1" ,"lte_MCSCode0_DL"
                ,"lte_MCSCode1_DL" ,"lte_Transmission_Mode", "lte_Rank_Indicator","lte_PHY_Code0_DL" ,"lte_PDSCH_BLER", "lte_PDCCH_BLER"
                ,"lte_PDSCH_PRb_Num_s" ,"lte_PDSCH_Code0_BLER", "lte_Wideband_CQI_for_CW0" ,"lte_RANK2_SINR1", "lte_PHY_Code1_DL"
                ,"lte_PDSCH_Code1_BLER" ,"lte_Wideband_CQI_for_CW1", "lte_RANK2_SINR2","lte_PDSCH_PRb_Num_slot"
                ,"LTESCAN_TopN_CELL_Specific_RSRP" ,"LTESCAN_TopN_CELL_Specific_RSSINR", "LTESCAN_TopN_RS_RP_Rx1Tx1"
                ,"LTESCAN_TopN_RS_CINR_Rx1Tx1" ,"LTESCAN_TopN_RS_RP_Rx1Tx2", "LTESCAN_TopN_RS_CINR_Rx1Tx2" ,"LTESCAN_TopN_EARFCN", "LTESCAN_TopN_PCI"};
            #endregion
            foreach (string strParam in strParamList)
            {
                param = new Dictionary<string, object>();
                param["param_name"] = strParam;
                param["param_arg"] = 0;
                columnsDef.Add((object)param);
            }

            Dictionary<string, object> tmpDic = new Dictionary<string, object>();
            tmpDic.Add("name", (object)"LTE_DOWNLOAD_PER");
            tmpDic.Add("themeName", (object)"lte_RSRP");
            tmpDic.Add("columnsDef", (object)columnsDef);

            DIYSampleGroup group = new DIYSampleGroup();
            group.Param = tmpDic;
            return group;
        }

        protected override void statData(ClientProxy clientProxy)
        {
            InitRegionMop2();
            setVarBeforQuery();
            queryInThread(clientProxy);
        }

        #region 采样点数据处理

        protected override void doWithDTData(TestPoint tp)
        {
            try
            {
                if (mapOp2.CheckPointInRegion(tp.Longitude, tp.Latitude))
                {
                    if (tp is LTETestPointDetail)
                    {
                        doWithDTDataAna(tp);
                    }
                    else if (tp is ScanTestPoint_LTE)
                    {
                        doWithSCANDataAna(tp);
                    }
                }
            }
            catch (Exception ex)
            {
                log.Error(ex.StackTrace);
            }
        }

        private void doWithDTDataAna(TestPoint tp)
        {
            int? iTac = (int?)(ushort?)tp["lte_TAC"];
            int? iEci = (int?)tp["lte_ECI"];
            int? iEarfcn = (int?)tp["lte_EARFCN"];
            int? iPCI = (int?)(short?)tp["lte_PCI"];
            float? fRSRP = (float?)tp["lte_RSRP"];
            float? fSINR = (float?)tp["lte_SINR"];
            if (iTac == null || iEci == null || iEarfcn == null || iPCI == null || fRSRP == null || fSINR == null
                || fRSRP < -140 || fRSRP > 25 || fSINR < -50 || fSINR > 50)
            {
                return;
            }

            LTECell lteMainCell = tp.GetMainCell_LTE();
            if (lteMainCell == null)
            {
                lteMainCell = CellManager.GetInstance().GetNearestLTECellByTACCI(
                    JavaDate.GetDateTimeFromMilliseconds(tp.Time * 1000L), iTac, iEci, tp.Longitude, tp.Latitude);
            }
            if (lteMainCell == null)
            {
                lteMainCell = CellManager.GetInstance().GetNearestLTECellByEARFCNPCI(
                    JavaDate.GetDateTimeFromMilliseconds(tp.Time * 1000L), iEarfcn, iPCI, tp.Longitude, tp.Latitude);
            }
            if (lteMainCell == null)
            {
                lteMainCell = new LTECell();
                lteMainCell.Name = "" + iTac.ToString() + "_" + iEci.ToString();
                lteMainCell.ECI = (int)iEci;
                lteMainCell.SectorID = (int)iEci % 256;
                lteMainCell.EARFCN = (int)iEarfcn;
                lteMainCell.PCI = (int)iPCI;
            }
            if (!lteDownloadPerformanceTpDic.ContainsKey(lteMainCell.ECI))
            {
                lteDownloadPerformanceTpDic[lteMainCell.ECI] = new List<LTEDataDownloadPerInfo>();
                lteDownloadPerformanceItemDic[lteMainCell.ECI] = new LTEDownloadPerformanceItem();
                lteDownloadPerformanceItemDic[lteMainCell.ECI].LteCell = lteMainCell;
                lteDownloadPerformanceItemDic[lteMainCell.ECI].I序号 = lteDownloadPerformanceItemDic.Count;
                lteDownloadPerformanceItemDic[lteMainCell.ECI].Str小区名称 = lteMainCell.Name;
                lteDownloadPerformanceItemDic[lteMainCell.ECI].IEnodBID = lteMainCell.ECI / 256;
                lteDownloadPerformanceItemDic[lteMainCell.ECI].ISectorID = lteMainCell.SectorID;
                lteDownloadPerformanceItemDic[lteMainCell.ECI].IECI = lteMainCell.ECI;
                lteDownloadPerformanceItemDic[lteMainCell.ECI].DLongitude = 0;
                if (lteMainCell.BelongBTS == null)
                {
                    lteDownloadPerformanceItemDic[lteMainCell.ECI].DLongitude = tp.Longitude;
                    lteDownloadPerformanceItemDic[lteMainCell.ECI].DLatitude = tp.Latitude;
                }
            }
            if (lteMainCell.BelongBTS == null && lteDownloadPerformanceItemDic[lteMainCell.ECI].DLongitude < tp.Longitude)
            {
                lteDownloadPerformanceItemDic[lteMainCell.ECI].DLongitude = tp.Longitude;
                lteDownloadPerformanceItemDic[lteMainCell.ECI].DLatitude = tp.Latitude;
            }
            LTEDataDownloadPerInfo lteDataDownloadPerInfo = new LTEDataDownloadPerInfo(tp);
            lteDownloadPerformanceTpDic[lteMainCell.ECI].Add(lteDataDownloadPerInfo);
            if (!cellFileTestTimeDic.ContainsKey(lteMainCell.ECI))
            {
                Dictionary<int, List<long>> fileTestTime = new Dictionary<int, List<long>>();
                fileTestTime[tp.FileID] = new List<long>() { tp.lTimeWithMillsecond };
                cellFileTestTimeDic.Add(lteMainCell.ECI, fileTestTime);
            }
            else
            {
                if (!cellFileTestTimeDic[lteMainCell.ECI].ContainsKey(tp.FileID))
                {
                    cellFileTestTimeDic[lteMainCell.ECI][tp.FileID] = new List<long>() { tp.lTimeWithMillsecond };
                }
                else
                {
                    cellFileTestTimeDic[lteMainCell.ECI][tp.FileID].Add(tp.lTimeWithMillsecond);
                }
            }
        }

        private void doWithSCANDataAna(TestPoint tp)
        {
            doWithScanMOMISample(tp);
            //同频主瓣分析
            Dictionary<int, LteSampleSubInfo> maxEarfcnDic = new Dictionary<int, LteSampleSubInfo>();
            List<LteSampleSubInfo> cellList = ZTLteAntennaOverlapCoverage.GetInstance().doWithSCANData(tp, ref maxEarfcnDic, true,false);
            ZTLteAntennaOverlapCoverage.GetInstance().doWithAllSample(tp, cellList, maxEarfcnDic, ref dicLteOverlapData);
            //同频主瓣且Mode3分析
            maxEarfcnDic = new Dictionary<int, LteSampleSubInfo>();
            List<LteSampleSubInfo> cellListMode3 = ZTLteAntennaOverlapCoverage.GetInstance().doWithSCANData(tp, ref maxEarfcnDic, true, true);
            ZTLteAntennaOverlapCoverage.GetInstance().doWithAllSample(tp, cellListMode3, maxEarfcnDic, ref dicLteOverlapMode3Data);
        }

        private void doWithScanMOMISample(TestPoint tp)
        {
            for (int i = 0; i < 50; i++)
            {
                float rsrp0, sinr0, rsrp1, sinr1;
                bool isValid = judgeValidTP(tp, i, out rsrp0, out sinr0, out rsrp1, out sinr1);
                if (!isValid)
                {
                    break;
                }

                LTECell lteCell = tp.GetCell_LTEScan(i);
                if (lteCell == null || lteCell.Direction > 360)
                    continue;
                if (!lteScanDownloadPerformanceTpDic.ContainsKey(lteCell))
                {
                    lteScanDownloadPerformanceTpDic[lteCell] = new List<LTEDataDownloadPerInfo>();
                }
                LTEDataDownloadPerInfo lteDataDownloadPerInfo = new LTEDataDownloadPerInfo(tp, "");
                lteDataDownloadPerInfo.fRSRP0 = rsrp0;
                lteDataDownloadPerInfo.fRSRP1 = rsrp1;
                lteDataDownloadPerInfo.fSINR0 = sinr0;
                lteDataDownloadPerInfo.fSINR1 = sinr1;
                lteScanDownloadPerformanceTpDic[lteCell].Add(lteDataDownloadPerInfo);
            }
        }

        private bool judgeValidTP(TestPoint tp, int i, out float rsrp0, out float sinr0, out float rsrp1, out float sinr1)
        {
            sinr0 = 0;
            rsrp1 = 0;
            sinr1 = 0;

            bool isValid = getValidData(tp, i , "LTESCAN_TopN_RS_RP_Rx1Tx1", -141, 25, out rsrp0);
            if (!isValid)
            {
                return isValid;
            }

            isValid = getValidData(tp, i, "LTESCAN_TopN_RS_CINR_Rx1Tx1", -50, 50, out sinr0);
            if (!isValid)
            {
                return isValid;
            }

            isValid = getValidData(tp, i, "LTESCAN_TopN_RS_RP_Rx1Tx2", -141, 25, out rsrp1);
            if (!isValid)
            {
                return isValid;
            }

            isValid = getValidData(tp, i, "LTESCAN_TopN_RS_CINR_Rx1Tx2", -50, 50, out sinr1);
            if (!isValid)
            {
                return isValid;
            }
            return true;
        }

        private bool getValidData(TestPoint tp, int i, string name, int min, int max, out float data)
        {
            data = 0;
            object value = tp[name, i];
            if (value == null)
            {
                return false;
            }
            data = float.Parse(value.ToString());
            if (data < min || data > max)
            {
                return false;
            }
            return true;
        }

        protected override void getResultAfterQuery()
        {
            WaitBox.CanCancel = true;
            WaitBox.Text = "正在统计预设指标值........";
            WaitBox.ProgressPercent = 0;
            int iCount = lteDownloadPerformanceItemDic.Count;
            int iNum = 0;
            foreach (int iEci in lteDownloadPerformanceItemDic.Keys)
            {
                try
                {
                    WaitBox.ProgressPercent = (int)(100 * ((iNum * 1.0) / iCount));
                    if (WaitBox.CancelRequest)
                        break;
                    setDownloadPerformanceItem(iEci);
                    LTEDataDownloadStatInfo statInfo = new LTEDataDownloadStatInfo();
                    statDtAna(iEci, ref statInfo);
                    statScanMOMIAna(iEci, ref statInfo);
                    fillPerformanceAnaItem(iEci, statInfo);
                    statScanOverAna(iEci);
                }
                catch (Exception ex)
                {
                    log.Error(ex.StackTrace);
                }
            }
        }

        private void setDownloadPerformanceItem(int iEci)
        {
            lteDownloadPerformanceItemDic[iEci].Str地市 = strCityTypeName;
            string strGridTypeName = "";
            double Longitude = lteDownloadPerformanceItemDic[iEci].LteCell.BelongBTS == null ?
                lteDownloadPerformanceItemDic[iEci].DLongitude : lteDownloadPerformanceItemDic[iEci].LteCell.Longitude;
            double Latitude = lteDownloadPerformanceItemDic[iEci].LteCell.BelongBTS == null ?
                lteDownloadPerformanceItemDic[iEci].DLatitude : lteDownloadPerformanceItemDic[iEci].LteCell.Latitude;
            isContainPoint(Longitude, Latitude, ref strGridTypeName);
            if (strGridTypeName == "")
                strGridTypeName = "无网格号";
            lteDownloadPerformanceItemDic[iEci].Str覆盖网格 = strGridTypeName;
            lteDownloadPerformanceItemDic[iEci].F测试采样点数 = lteDownloadPerformanceTpDic[iEci].Count;
            foreach (int iFile in cellFileTestTimeDic[iEci].Keys)
            {
                List<long> testTime = cellFileTestTimeDic[iEci][iFile];
                testTime.Sort();
                long lPreTime = testTime.Count > 0 ? testTime[0] : 0;
                foreach (long lTime in testTime)
                {
                    double dTimeSpan = 1.0 * (lTime - lPreTime) / 1000;
                    if (dTimeSpan <= 5)
                    {
                        lteDownloadPerformanceItemDic[iEci].F测试占用时长 += dTimeSpan;
                    }
                    lPreTime = lTime;
                }
            }
            lteDownloadPerformanceItemDic[iEci].F测试占用时长 = Math.Round(lteDownloadPerformanceItemDic[iEci].F测试占用时长, 2);
        }
        #endregion

        #region 性能指标统计及问题归类分析

        private void fillPerformanceAnaItem(int iEci, LTEDataDownloadStatInfo statInfo)
        {
            LTEDownloadPerformanceItem lteDownloadItem = lteDownloadPerformanceItemDic[iEci];
            lteDownloadItem.FRSRP_80个数 = statInfo.fRSRP_80Num;
            lteDownloadItem.F平均RSRQ = Math.Round(1.0 * statInfo.fRSRQ_Sum / statInfo.fRSRQ_Num, 2);
            lteDownloadItem.FPRSQ_115_R = Math.Round(100.0 * statInfo.fRSRQ_115Num / statInfo.fRSRQ_Num, 2);
            lteDownloadItem.FRSRP_80_RSRQ个数 = statInfo.fRSRP_80_RQRQ_115Num;
            lteDownloadItem.FRSRP_80_RSRQ_R = Math.Round(100.0 * statInfo.fRSRP_80_RQRQ_115Num / statInfo.fRSRQ_Num, 2);
            lteDownloadItem.FPDCP_DL平均速率 = Math.Round(1.0 * statInfo.fFPDCP_DLSum / statInfo.fFPDCP_DLNum, 2);
            lteDownloadItem.FMAC_DL平均速率 = Math.Round(1.0 * statInfo.fFMAC_DLSum / statInfo.fFMAC_DLNum, 2);
            lteDownloadItem.FPHY_DL平均速率 = Math.Round(1.0 * statInfo.fFPHY_DLSum / statInfo.fFPHY_DLNum, 2);
            lteDownloadItem.F覆盖率100_3 = Math.Round(100.0 * statInfo.fRsrpSinr100_3 / lteDownloadItem.F测试采样点数, 2);
            lteDownloadItem.F覆盖率110_3 = Math.Round(100.0 * statInfo.fRsrpSinr110_3 / lteDownloadItem.F测试采样点数, 2);
            lteDownloadItem.F平均RSRP = Math.Round(1.0 * statInfo.fRsrpSum / lteDownloadItem.F测试采样点数, 2);
            lteDownloadItem.F平均SINR = Math.Round(1.0 * statInfo.fSinrSum / lteDownloadItem.F测试采样点数, 2);
            lteDownloadItem.FSINR_3_R = Math.Round(100.0 * statInfo.fSirn_3 / lteDownloadItem.F测试采样点数, 2);
            lteDownloadItem.FSINR10_R = Math.Round(100.0 * statInfo.fSinr10 / lteDownloadItem.F测试采样点数, 2);
            lteDownloadItem.F平均RI = calculate(statInfo.fRISum, statInfo.fRINum, 1);
            lteDownloadItem.FRI1_R = calculate(statInfo.fRI1Num, statInfo.fRINum, 100);
            lteDownloadItem.F平均CQI = calculate(statInfo.fCQISum, statInfo.fCQINum, 1);
            lteDownloadItem.FCQI10_R = calculate(statInfo.fCQI10Num, statInfo.fCQINum, 100);
            lteDownloadItem.F平均下行PDSCH_BLER = calculate(statInfo.fPDSCH_BLERSum, statInfo.fPDSCH_BLERNum, 1);
            lteDownloadItem.F下行PDSCH_BLER10_R = calculate(statInfo.fPDSCH_BLER10Num, statInfo.fPDSCH_BLERNum, 100);
            lteDownloadItem.F平均下行PDCCH_BLER = calculate(statInfo.fPDCCH_BLERSum, statInfo.fPDCCH_BLERNum, 1);
            float iQ0Sum = statInfo.fQPSK_DLCode0Sum + statInfo.fQAM16_DLCode0Sum + statInfo.fQAM64_DLCode0Sum;
            float iQ1Sum = statInfo.fQPSK_DLCode1Sum + statInfo.fQAM16_DLCode1Sum + statInfo.fQAM64_DLCode1Sum;
            lteDownloadItem.FQPSK调整_R = calculate((statInfo.fQPSK_DLCode0Sum + statInfo.fQPSK_DLCode1Sum), (iQ0Sum + iQ1Sum), 100);
            lteDownloadItem.F16QAM调制_R = calculate((statInfo.fQAM16_DLCode0Sum + statInfo.fQAM16_DLCode1Sum), (iQ0Sum + iQ1Sum), 100);
            lteDownloadItem.F64QAM调制_R = calculate((statInfo.fQAM64_DLCode0Sum + statInfo.fQAM64_DLCode1Sum), (iQ0Sum + iQ1Sum), 100);

            lteDownloadItem.F平均下行MCS = calculate((statInfo.fMCS0Sum + statInfo.fMCS1Sum), (statInfo.fMCS0Num + statInfo.fMCS1Num), 1);
            lteDownloadItem.F平均分配下行PRB数 = (int)calculate(statInfo.fPRBSum, statInfo.fPRBNum, 1);
            lteDownloadItem.F平均时隙分配下行PRB数 = (int)calculate(statInfo.fPRBSlotSum, statInfo.fPRBSlotNum, 1);

            int iTMSum = 0;
            foreach (int tm in statInfo.tmDic.Keys)
            {
                if (tm == 0) continue;
                iTMSum += statInfo.tmDic[tm];
            }
            lteDownloadItem.FTM1_R = lteDownloadItem.FTM2_R = lteDownloadItem.FTM3_R = lteDownloadItem.FTM7_R = lteDownloadItem.FTM8_R = 0;
            if (statInfo.tmDic.ContainsKey(1))
                lteDownloadItem.FTM1_R = Math.Round(100.0 * statInfo.tmDic[1] / iTMSum, 2);
            if (statInfo.tmDic.ContainsKey(2))
                lteDownloadItem.FTM2_R = Math.Round(100.0 * statInfo.tmDic[2] / iTMSum, 2);
            if (statInfo.tmDic.ContainsKey(3))
                lteDownloadItem.FTM3_R = Math.Round(100.0 * statInfo.tmDic[3] / iTMSum, 2);
            if (statInfo.tmDic.ContainsKey(7))
                lteDownloadItem.FTM7_R = Math.Round(100.0 * statInfo.tmDic[7] / iTMSum, 2);
            if (statInfo.tmDic.ContainsKey(8))
                lteDownloadItem.FTM8_R = Math.Round(100.0 * statInfo.tmDic[8] / iTMSum, 2);
            lteDownloadItem.FPHY_Code0_DL平均速率 = calculate(statInfo.fPHY_Code0_DLSum, statInfo.fPHY_Code0_DLNum, 1);
            lteDownloadItem.F平均PDSCH_Code0_BLER = calculate(statInfo.fPDSCH_Code0_BLERSum, statInfo.fPDSCH_Code0_BLERNum, 1);
            lteDownloadItem.FCode0_QPSK调整_R = calculate(statInfo.fQPSK_DLCode0Sum, iQ0Sum, 100);
            lteDownloadItem.FCode0_16QAM调制_R = calculate(statInfo.fQAM16_DLCode0Sum, iQ0Sum, 100);
            lteDownloadItem.FCode0_64QAM调制_R = calculate(statInfo.fQAM64_DLCode0Sum, iQ0Sum, 100);
            lteDownloadItem.FCode0_平均下行MCS = calculate(statInfo.fMCS0Sum, statInfo.fMCS0Num, 1);
            lteDownloadItem.F平均lte_Wideband_CQI_for_CW0 = calculate(statInfo.flte_Wideband_CQI_for_CW0Sum, statInfo.flte_Wideband_CQI_for_CW0Num, 1);
            lteDownloadItem.Flte_Wideband_CQI_for_CW0_10_R = calculate(statInfo.flte_Wideband_CQI_for_CW0_10Num, statInfo.flte_Wideband_CQI_for_CW0Num, 100);
            lteDownloadItem.FRANK2_SINR1 = calculate(statInfo.fRANK2_SINR1Sum, statInfo.fRANK2_SINR1Num, 1);
            lteDownloadItem.FPHY_Code1_DL平均速率 = calculate(statInfo.fPHY_Code1_DLSum, statInfo.fPHY_Code1_DLNum, 1);
            lteDownloadItem.F平均PDSCH_Code1_BLER = calculate(statInfo.fPDSCH_Code1_BLERSum, statInfo.fPDSCH_Code1_BLERNum, 1);

            lteDownloadItem.FCode1_QPSK调整_R = calculate(statInfo.fQPSK_DLCode1Sum, iQ1Sum, 100);
            lteDownloadItem.FCode1_16QAM调制_R = calculate(statInfo.fQAM16_DLCode1Sum, iQ1Sum, 100);
            lteDownloadItem.FCode1_64QAM调制_R = calculate(statInfo.fQAM64_DLCode1Sum, iQ1Sum, 100);
            lteDownloadItem.FCode1_平均下行MCS = calculate(statInfo.fMCS1Sum, statInfo.fMCS1Num, 1);
            lteDownloadItem.F平均lte_Wideband_CQI_for_CW1 = calculate(statInfo.flte_Wideband_CQI_for_CW1Sum, statInfo.flte_Wideband_CQI_for_CW1Num, 1);
            lteDownloadItem.Flte_Wideband_CQI_for_CW1_10_R = calculate(statInfo.flte_Wideband_CQI_for_CW1_10Num, statInfo.flte_Wideband_CQI_for_CW1Num, 100);
            lteDownloadItem.FRANK2_SINR2 =  calculate(statInfo.fRANK2_SINR2Sum, statInfo.fRANK2_SINR2Num, 1);

            lteDownloadItem.FCQI0_1_5_R = calculate(statInfo.fCQI0_1_5Num, statInfo.fCQI0_1Num, 100);
            lteDownloadItem.FSinr0_1_5_R = statInfo.fRANK2_SINR_Num > 0 ? Math.Round(100.0 * statInfo.fRANK2_SINR_5Num / statInfo.fRANK2_SINR_Num, 2) : 0;
            lteDownloadItem.F双流功率不平衡_RSRP0_RSRP1 = statInfo.fScanSampleNum > 0 ?
                Math.Abs(Math.Round(1.0 * statInfo.fRSRP0Sum / statInfo.fScanSampleNum - 1.0 * statInfo.fRSRP1Sum / statInfo.fScanSampleNum, 2)) : 0; 
            lteDownloadItem.FRSRP0_110_R =  calculate(statInfo.fScanSampleNum, statInfo.fRSRP0_110Num, 100);
            lteDownloadItem.FRSRP1_110_R =  calculate(statInfo.fScanSampleNum, statInfo.fRSRP1_110Num, 100);
            lteDownloadItem.FSINR0_3_R = calculate(statInfo.fScanSampleNum, statInfo.fSINR0_3Num, 100);
            lteDownloadItem.FSINR1_3_R = calculate(statInfo.fScanSampleNum, statInfo.fSINR1_3Num, 100);


            小区整体评估(ref lteDownloadItem);
            复用Code0和Code1评估(ref lteDownloadItem);
            扫频数据评估(ref lteDownloadItem);
            重叠覆盖干扰(ref lteDownloadItem);
        }

        private double calculate(float sum, float count, int per)
        {
            if (count > 0)
            {
                return Math.Round(1.0 * per * sum / count, 2);
            }
            return 0;
        }

        private void statDtAna(int iEci, ref LTEDataDownloadStatInfo statInfo)
        {
            foreach (LTEDataDownloadPerInfo ltePerInfo in lteDownloadPerformanceTpDic[iEci])
            {
                statInfo.fRSRP_80Num += ltePerInfo.flte_RSRP > -80 ? 1 : 0;
                statInfo.fRSRQ_Sum += ltePerInfo.flte_RSRQ > -999 ? (float)ltePerInfo.flte_RSRQ : 0;
                statInfo.fRSRQ_Num += ltePerInfo.flte_RSRQ > -999 ? 1 : 0;
                statInfo.fRSRQ_115Num += ltePerInfo.flte_RSRQ > -999 && ltePerInfo.flte_RSRQ < -11.5 ? 1 : 0;
                statInfo.fRSRP_80_RQRQ_115Num +=ltePerInfo.flte_RSRP > -80 && ltePerInfo.flte_RSRQ > -50 && ltePerInfo.flte_RSRQ < -11.5 ? 1 : 0;
                statInfo.fFPDCP_DLSum += ltePerInfo.flte_PDCP_DL > -999 ? (float)ltePerInfo.flte_PDCP_DL : 0;
                statInfo.fFPDCP_DLNum += ltePerInfo.flte_PDCP_DL > -999 ? 1 : 0;
                statInfo.fFMAC_DLSum += ltePerInfo.flte_MAC_DL > -999 ? (float)ltePerInfo.flte_MAC_DL : 0;
                statInfo.fFMAC_DLNum += ltePerInfo.flte_MAC_DL > -999 ? 1 : 0;
                statInfo.fFPHY_DLSum += ltePerInfo.flte_PHY_DL > -999 ? (float)ltePerInfo.flte_PHY_DL : 0;
                statInfo.fFPHY_DLNum += ltePerInfo.flte_PHY_DL > -999 ? 1 : 0;
                lteDownloadPerformanceItemDic[iEci].FMAC_DL平均速率 += (float)ltePerInfo.flte_MAC_DL;
                lteDownloadPerformanceItemDic[iEci].FPHY_DL平均速率 += (float)ltePerInfo.flte_PHY_DL;
                statInfo.fRsrpSum += (float)ltePerInfo.flte_RSRP;
                statInfo.fSinrSum += (float)ltePerInfo.flte_SINR;
                if (ltePerInfo.flte_RSRP >= -100 && ltePerInfo.flte_SINR >= -3)
                    statInfo.fRsrpSinr100_3++;
                if (ltePerInfo.flte_RSRP >= -110 && ltePerInfo.flte_SINR >= -3)
                    statInfo.fRsrpSinr110_3++;
                statInfo.fSirn_3 += ltePerInfo.flte_SINR > -3 ? 1 : 0;
                statInfo.fSinr10 += ltePerInfo.flte_SINR > 10 ? 1 : 0;
                if (ltePerInfo.fRI > -999)
                {
                    statInfo.fRISum += (float)ltePerInfo.fRI;
                    statInfo.fRINum++;
                }
                statInfo.fRI1Num += ltePerInfo.fRI > 1 ? 1 : 0;
                statInfo.fCQISum += ltePerInfo.fCQI > -999 ? (float)ltePerInfo.fCQI : 0;
                statInfo.fCQINum += ltePerInfo.fCQI > -999 ? 1 : 0;
                statInfo.fCQI10Num += ltePerInfo.fCQI > 10 ? 1 : 0;
                statInfo.fPDSCH_BLERSum += ltePerInfo.fPDSCH_BLER > 0 ? (float)ltePerInfo.fPDSCH_BLER : 0;
                statInfo.fPDSCH_BLERNum += ltePerInfo.fPDSCH_BLER > 0 ? 1 : 0;
                statInfo.fPDSCH_BLER10Num += ltePerInfo.fPDSCH_BLER > 10 ? 1 : 0;
                statInfo.fPDCCH_BLERSum += ltePerInfo.fPDCCH_BLER > 0 ? (float)ltePerInfo.fPDCCH_BLER : 0;
                statInfo.fPDCCH_BLERNum += ltePerInfo.fPDCCH_BLER > 0 ? 1 : 0;
                statInfo.fQPSK_DLCode0Sum += ltePerInfo.ilte_Times_QPSK_DLCode0 > 0 ? (float)ltePerInfo.ilte_Times_QPSK_DLCode0 : 0;
                statInfo.fQPSK_DLCode1Sum += ltePerInfo.ilte_Times_QPSK_DLCode1 > 0 ? (float)ltePerInfo.ilte_Times_QPSK_DLCode1 : 0;
                statInfo.fQAM16_DLCode0Sum += ltePerInfo.ilte_Times_QAM16_DLCode0 > 0 ? (float)ltePerInfo.ilte_Times_QAM16_DLCode0 : 0;
                statInfo.fQAM16_DLCode1Sum += ltePerInfo.ilte_Times_QAM16_DLCode1 > 0 ? (float)ltePerInfo.ilte_Times_QAM16_DLCode1 : 0;
                statInfo.fQAM64_DLCode0Sum += ltePerInfo.ilte_Times_QAM64_DLCode0 > 0 ? (float)ltePerInfo.ilte_Times_QAM64_DLCode0 : 0;
                statInfo.fQAM64_DLCode1Sum += ltePerInfo.ilte_Times_QAM64_DLCode1 > 0 ? (float)ltePerInfo.ilte_Times_QAM64_DLCode1 : 0;
                statInfo.fMCS0Sum += ltePerInfo.ilte_Times_MCS_DLCode0_Sum > 0 ? (float)ltePerInfo.ilte_Times_MCS_DLCode0_Sum : 0;
                statInfo.fMCS0Num += ltePerInfo.ilte_Times_MCS_DLCode0_Num;
                statInfo.fMCS1Sum += ltePerInfo.ilte_Times_MCS_DLCode1_Sum > 0 ? (float)ltePerInfo.ilte_Times_MCS_DLCode1_Sum : 0;
                statInfo.fMCS1Num += ltePerInfo.ilte_Times_MCS_DLCode1_Num;
                statInfo.fPRBSum += ltePerInfo.ilte_PDSCH_PRb_Num_s > 0 ? (float)ltePerInfo.ilte_PDSCH_PRb_Num_s : 0;
                statInfo.fPRBNum += ltePerInfo.ilte_PDSCH_PRb_Num_s > 0 ? 1 : 0;
                statInfo.fPRBSlotSum += ltePerInfo.ilte_PDSCH_PRb_Num_slot > 0 ? (float)ltePerInfo.ilte_PDSCH_PRb_Num_slot : 0;
                statInfo.fPRBSlotNum += ltePerInfo.ilte_PDSCH_PRb_Num_slot > 0 ? 1 : 0;
                if (!statInfo.tmDic.ContainsKey((int)ltePerInfo.ilte_Transmission_Mode))
                    statInfo.tmDic[(int)ltePerInfo.ilte_Transmission_Mode] = 0;
                statInfo.tmDic[(int)ltePerInfo.ilte_Transmission_Mode] += 1;
                statInfo.fPHY_Code0_DLSum += ltePerInfo.fPHY_Code0_DL > 0 ? (float)ltePerInfo.fPHY_Code0_DL : 0;
                statInfo.fPHY_Code0_DLNum += ltePerInfo.fPHY_Code0_DL > 0 ? 1 : 0;
                statInfo.fPDSCH_Code0_BLERSum += ltePerInfo.fPDSCH_Code0_BLER > -999 ? (float)ltePerInfo.fPDSCH_Code0_BLER : 0;
                statInfo.fPDSCH_Code0_BLERNum += ltePerInfo.fPDSCH_Code0_BLER > -999 ? 1 : 0;
                statInfo.flte_Wideband_CQI_for_CW0Sum += ltePerInfo.flte_Wideband_CQI_for_CW0 > 0 ? (float)ltePerInfo.flte_Wideband_CQI_for_CW0 : 0;
                statInfo.flte_Wideband_CQI_for_CW0Num += ltePerInfo.flte_Wideband_CQI_for_CW0 > 0 ? 1 : 0;
                statInfo.flte_Wideband_CQI_for_CW0_10Num += ltePerInfo.flte_Wideband_CQI_for_CW0 > 10 ? 1 : 0;
                statInfo.fRANK2_SINR1Sum += ltePerInfo.fRANK2_SINR1 > 0 ? (float)ltePerInfo.fRANK2_SINR1 : 0;
                statInfo.fRANK2_SINR1Num += ltePerInfo.fRANK2_SINR1 > 0 ? 1 : 0;
                statInfo.fPHY_Code1_DLSum += ltePerInfo.fPHY_Code1_DL > 0 ? (float)ltePerInfo.fPHY_Code1_DL : 0;
                statInfo.fPHY_Code1_DLNum += ltePerInfo.fPHY_Code1_DL > 0 ? 1 : 0;
                statInfo.fPDSCH_Code1_BLERSum += ltePerInfo.fPDSCH_Code1_BLER > -999 ? (float)ltePerInfo.fPDSCH_Code1_BLER : 0;
                statInfo.fPDSCH_Code1_BLERNum += ltePerInfo.fPDSCH_Code1_BLER > -999 ? 1 : 0;
                statInfo.flte_Wideband_CQI_for_CW1Sum += ltePerInfo.flte_Wideband_CQI_for_CW1 > 0 ? (float)ltePerInfo.flte_Wideband_CQI_for_CW1 : 0;
                statInfo.flte_Wideband_CQI_for_CW1Num += ltePerInfo.flte_Wideband_CQI_for_CW1 > 0 ? 1 : 0;
                statInfo.flte_Wideband_CQI_for_CW1_10Num += ltePerInfo.flte_Wideband_CQI_for_CW1 > 10 ? 1 : 0;
                statInfo.fRANK2_SINR2Sum += ltePerInfo.fRANK2_SINR2 > 0 ? (float)ltePerInfo.fRANK2_SINR2 : 0;
                statInfo.fRANK2_SINR2Num += ltePerInfo.fRANK2_SINR2 > 0 ? 1 : 0;
                if (ltePerInfo.flte_Wideband_CQI_for_CW0 > 0 && ltePerInfo.flte_Wideband_CQI_for_CW1 > 0)
                {
                    statInfo.fCQI0_1Num++;
                    float fCQIDiff = Math.Abs((float)(ltePerInfo.flte_Wideband_CQI_for_CW0 - ltePerInfo.flte_Wideband_CQI_for_CW1));
                    statInfo.fCQI0_1_5Num += fCQIDiff > 5 ? fCQIDiff : 0;
                }
                if (ltePerInfo.fRANK2_SINR1 > 0 && ltePerInfo.fRANK2_SINR2 > 0)
                {
                    statInfo.fRANK2_SINR_Num++;
                    float fRANK2_SINRDiff = Math.Abs((float)(ltePerInfo.fRANK2_SINR1 - ltePerInfo.fRANK2_SINR2));
                    statInfo.fRANK2_SINR_5Num += fRANK2_SINRDiff > 5 ? fRANK2_SINRDiff : 0;
                }
            }
        }

        private void statScanMOMIAna(int iEci, ref LTEDataDownloadStatInfo statInfo)
        {
            LTECell lteCell = lteDownloadPerformanceItemDic[iEci].LteCell;
            if (lteCell == null || !lteScanDownloadPerformanceTpDic.ContainsKey(lteCell))
                return;
            foreach (LTEDataDownloadPerInfo ltePerInfo in lteScanDownloadPerformanceTpDic[lteCell])
            {
                statInfo.fRSRP0Sum += ltePerInfo.fRSRP0;
                statInfo.fRSRP1Sum += ltePerInfo.fRSRP1;
                statInfo.fRSRP0_110Num += ltePerInfo.fRSRP0 > -110 ? 1 : 0;
                statInfo.fRSRP1_110Num += ltePerInfo.fRSRP1 > -110 ? 1 : 0;
                statInfo.fSINR0_3Num += ltePerInfo.fSINR0 > -3 ? 1 : 0;
                statInfo.fSINR1_3Num += ltePerInfo.fSINR1 > -3 ? 1 : 0;
            }
            statInfo.fScanSampleNum = lteScanDownloadPerformanceTpDic[lteCell].Count;
        }

        private void statScanOverAna(int iEci)
        {
            if (!dicLteOverlapData.ContainsKey(lteDownloadPerformanceItemDic[iEci].Str小区名称))
            {
                return;
            }
            string strCellName = lteDownloadPerformanceItemDic[iEci].Str小区名称;
            lteDownloadPerformanceItemDic[iEci].F同频主小区平均邻区数 = 0;
            lteDownloadPerformanceItemDic[iEci].F同频主小区平均邻区数 = dicLteOverlapData[strCellName].nbCellInfoDic.Count;
            foreach (string strCell in dicLteOverlapData[strCellName].nbCellInfoDic.Keys)
            {
                lteDownloadPerformanceItemDic[iEci].I同频主小区最强邻区E_UtranID = dicLteOverlapData[strCellName].nbCellInfoDic[strCell].nbCell.ECI;
                lteDownloadPerformanceItemDic[iEci].Str同频主小区最强邻区中文名 = dicLteOverlapData[strCellName].nbCellInfoDic[strCell].nbCell.Name;
                lteDownloadPerformanceItemDic[iEci].F同频主小区采样点_R =
                    Math.Round(100.0 * dicLteOverlapData[strCellName].nbCellInfoDic[strCell].iSampleNum / dicLteOverlapData[strCellName].LTECellInfo.isampleCount, 2);
            }
            if (!dicLteOverlapMode3Data.ContainsKey(strCellName))
            {
                return;
            }
            lteDownloadPerformanceItemDic[iEci].F同频主小区mod3的采样点_R = 0;
            lteDownloadPerformanceItemDic[iEci].F同频主小区mod3平均邻区数 = dicLteOverlapMode3Data[strCellName].nbCellInfoDic.Count;
            foreach (string strCell in dicLteOverlapMode3Data[strCellName].nbCellInfoDic.Keys)
            {
                lteDownloadPerformanceItemDic[iEci].I同频主小区mod3最强邻区E_UtranID = dicLteOverlapMode3Data[strCellName].nbCellInfoDic[strCell].nbCell.ECI;
                lteDownloadPerformanceItemDic[iEci].Str同频主小区mod3最强邻区中文名 = dicLteOverlapMode3Data[strCellName].nbCellInfoDic[strCell].nbCell.Name;
                lteDownloadPerformanceItemDic[iEci].F同频主小区mod3的采样点_R =
                    Math.Round(100.0 * dicLteOverlapMode3Data[strCellName].nbCellInfoDic[strCell].iSampleNum / dicLteOverlapMode3Data[strCellName].LTECellInfo.isampleCount, 2);
            }
        }

        private string getProblem(string problem, string name, string defaultValue, string problemValue)
        {
            if (problem.Contains(name))
            {
                return defaultValue;
            }
            return problemValue;
        }

        private void 小区整体评估(ref LTEDownloadPerformanceItem lteDownloadItem)
        {
            lteDownloadItem.Str问题归类 = "";
            lteDownloadItem.Str问题影响 = "";
            if (lteDownloadItem.F覆盖率110_3 < 90)
            {
                lteDownloadItem.Str问题归类 += "弱覆盖,";
                lteDownloadItem.Str问题影响 += getProblem(lteDownloadItem.Str问题影响, "感知恶化,", "", "感知恶化,");
            }
            if (lteDownloadItem.FPHY_DL平均速率 < 10)
            {
                lteDownloadItem.Str问题归类 += "低下载速率,";
                lteDownloadItem.Str问题影响 += getProblem(lteDownloadItem.Str问题影响, "感知恶化,", "", "感知恶化,");
            }
            if (lteDownloadItem.F平均下行PDSCH_BLER > 10)
            {
                lteDownloadItem.Str问题归类 += "高误码,";
                lteDownloadItem.Str问题影响 += getProblem(lteDownloadItem.Str问题影响, "感知恶化,", "", "感知恶化,");
            }
            if (lteDownloadItem.FRI1_R == 0)
            {
                lteDownloadItem.Str问题归类 += "单流,";
                lteDownloadItem.Str问题影响 += getProblem(lteDownloadItem.Str问题影响, "感知恶化,", "室分单流,", "感知恶化,室分单流,");
            }
            if (lteDownloadItem.FRI1_R < 50)
            {
                lteDownloadItem.Str问题归类 += "双流比不佳,";
                lteDownloadItem.Str问题影响 += getProblem(lteDownloadItem.Str问题影响, "效率下降,", "", "效率下降,");
            }
            if (lteDownloadItem.FSINR_3_R < 90)
            {
                lteDownloadItem.Str问题归类 += "低信噪比,";
                lteDownloadItem.Str问题影响 += getProblem(lteDownloadItem.Str问题影响, "感知恶化,", "", "感知恶化,");
            }
            if (lteDownloadItem.F64QAM调制_R < 50)
            {
                lteDownloadItem.Str问题归类 += "调制比不佳,";
                lteDownloadItem.Str问题影响 += getProblem(lteDownloadItem.Str问题影响, "效率下降,", "", "效率下降,");
            }
            if (lteDownloadItem.FCQI10_R < 50)
            {
                lteDownloadItem.Str问题归类 += "信道质量不佳,";
                lteDownloadItem.Str问题影响 += getProblem(lteDownloadItem.Str问题影响, "效率下降,", "", "效率下降,");
            }
            if (lteDownloadItem.FSINR10_R < 50)
            {
                lteDownloadItem.Str问题归类 += "信噪比不佳,";
                lteDownloadItem.Str问题影响 += getProblem(lteDownloadItem.Str问题影响, "效率下降,", "", "效率下降,");
            }
            if (lteDownloadItem.F平均时隙分配下行PRB数 < 80)
            {
                lteDownloadItem.Str问题归类 += "资源不足,";
                lteDownloadItem.Str问题影响 += getProblem(lteDownloadItem.Str问题影响, "效率下降,", "", "效率下降,");
            }
        }

        private void 复用Code0和Code1评估(ref LTEDownloadPerformanceItem lteDownloadItem)
        {
            if (lteDownloadItem.FPHY_Code0_DL平均速率 < 10 && lteDownloadItem.FPHY_Code1_DL平均速率 < 10)
            {
                lteDownloadItem.Str问题归类 += "Code0和Code1均出现低速率,";

                lteDownloadItem.Str问题影响 += getProblem(lteDownloadItem.Str问题影响, "感知恶化,", "", "感知恶化,");
            }
            else if (lteDownloadItem.FPHY_Code0_DL平均速率 < 10 || lteDownloadItem.FPHY_Code1_DL平均速率 < 10)
            {
                lteDownloadItem.Str问题归类 += "Code0或者Code1低速率,";
                lteDownloadItem.Str问题影响 += getProblem(lteDownloadItem.Str问题影响, "效率下降,", "", "效率下降,");
            }
            if (lteDownloadItem.F平均PDSCH_Code0_BLER > 10 && lteDownloadItem.F平均PDSCH_Code1_BLER > 10)
            {
                lteDownloadItem.Str问题归类 += "Code0和Code1均出现高误码,";
                lteDownloadItem.Str问题影响 += getProblem(lteDownloadItem.Str问题影响, "感知恶化,", "", "感知恶化,");
            }
            else if (lteDownloadItem.F平均PDSCH_Code0_BLER > 10 || lteDownloadItem.F平均PDSCH_Code1_BLER > 10)
            {
                lteDownloadItem.Str问题归类 += "Code0或Code1高误码,";
                lteDownloadItem.Str问题影响 += getProblem(lteDownloadItem.Str问题影响, "效率下降,", "", "效率下降,");
            }
            if (lteDownloadItem.FCode0_64QAM调制_R < 50 && lteDownloadItem.FCode1_64QAM调制_R < 50)
            {
                lteDownloadItem.Str问题归类 += "Code0和Code1均出现调制比不佳,";
                lteDownloadItem.Str问题影响 += getProblem(lteDownloadItem.Str问题影响, "效率下降,", "", "效率下降,");
            }
            else if (lteDownloadItem.FCode0_64QAM调制_R < 50 || lteDownloadItem.FCode1_64QAM调制_R < 50)
            {
                lteDownloadItem.Str问题归类 += "Code0或Code1调制比不平衡,";
                lteDownloadItem.Str问题影响 += getProblem(lteDownloadItem.Str问题影响, "效率下降,", "", "效率下降,");
            }
            if (lteDownloadItem.FCQI0_1_5_R > 80 || lteDownloadItem.FSinr0_1_5_R > 80)
            {
                lteDownloadItem.Str问题归类 += "Code0或Code1出现不平衡,";
                lteDownloadItem.Str问题影响 += getProblem(lteDownloadItem.Str问题影响, "效率下降,", "", "效率下降,");
            }
        }

        private void 扫频数据评估(ref LTEDownloadPerformanceItem lteDownloadItem)
        {
            if (lteDownloadItem.F双流功率不平衡_RSRP0_RSRP1 > 10)
            {
                lteDownloadItem.Str问题归类 += "双流功率严重不平衡,";
                lteDownloadItem.Str问题影响 += getProblem(lteDownloadItem.Str问题影响, "感知恶化,", "", "感知恶化,");
            }
            else if (lteDownloadItem.F双流功率不平衡_RSRP0_RSRP1 > 5)
            {
                lteDownloadItem.Str问题归类 += "双流功率不平衡,";
                lteDownloadItem.Str问题影响 += getProblem(lteDownloadItem.Str问题影响, "效率下降,", "", "效率下降,");
            }
            if (lteDownloadItem.FRSRP0_110_R < 90 && lteDownloadItem.FRSRP1_110_R < 90)
            {
                lteDownloadItem.Str问题归类 += "双流弱覆盖,";
                lteDownloadItem.Str问题影响 += getProblem(lteDownloadItem.Str问题影响, "感知恶化,", "", "感知恶化,");
            }
            else if (lteDownloadItem.FRSRP0_110_R < 90 || lteDownloadItem.FRSRP1_110_R < 90)
            {
                lteDownloadItem.Str问题归类 += "RSRP0或者RSRP1出现弱覆盖,";
                lteDownloadItem.Str问题影响 += getProblem(lteDownloadItem.Str问题影响, "效率下降,", "", "效率下降,");
            }
            if (lteDownloadItem.FSINR0_3_R < 90 && lteDownloadItem.FSINR1_3_R < 90)
            {
                lteDownloadItem.Str问题归类 += "双流低信噪比,";
                lteDownloadItem.Str问题影响 += getProblem(lteDownloadItem.Str问题影响, "感知恶化,", "", "感知恶化,");
            }
            else if (lteDownloadItem.FSINR0_3_R < 90 || lteDownloadItem.FSINR1_3_R < 90)
            {
                lteDownloadItem.Str问题归类 += "SINR0或SINR1低信噪比,";
                lteDownloadItem.Str问题影响 += getProblem(lteDownloadItem.Str问题影响, "效率下降,", "", "效率下降,");
            }
        }

        private void 重叠覆盖干扰(ref LTEDownloadPerformanceItem lteDownloadItem)
        {
            if (lteDownloadItem.F同频主小区mod3的采样点_R > 50)
            {
                lteDownloadItem.Str问题归类 += "高下行干扰,";
                lteDownloadItem.Str问题影响 += getProblem(lteDownloadItem.Str问题影响, "感知恶化,", "", "感知恶化,");
            }
            if (lteDownloadItem.F同频主小区平均邻区数 > 0)
            {
                lteDownloadItem.Str问题归类 += "存在下行干扰,";
                lteDownloadItem.Str问题影响 += getProblem(lteDownloadItem.Str问题影响, "效率下降,", "", "效率下降,");
            }
        }
        #endregion

        protected void FireShowResultForm()
        {
            object obj = MainModel.GetInstance().GetObjectFromBlackboard(typeof(LTEDownloadPerformanceForm).FullName);
            LTEDownloadPerformanceForm form = obj == null ? null : obj as LTEDownloadPerformanceForm;
            if (form == null || form.IsDisposed)
            {
                form = new LTEDownloadPerformanceForm(MainModel);
            }

            form.FillData(lteDownloadPerformanceItemDic);
            if (!form.Visible)
            {
                form.Show(MainModel.MainForm);
            }
        }
    }
    public class LTEDownloadPerformanceItem
    {
        public LTECell LteCell
        {
            get;
            set;
        }
        public int I序号
        {
            get;
            set;
        }
        public string Str地市
        {
            get;
            set;
        }
        public string Str小区名称
        {
            get;
            set;
        }
        public string Str覆盖网格
        {
            get;
            set;
        }
        public int IEnodBID
        {
            get;
            set;
        }
        public int ISectorID
        {
            get;
            set;
        }
        public int IECI
        {
            get;
            set;
        }
        public double DLongitude
        {
            get;
            set;
        }
        public double DLatitude
        {
            get;
            set;
        }
        public string Str问题归类
        {
            get;
            set;
        }
        public string Str问题影响
        {
            get;
            set;
        }
        public double F测试占用时长
        {
            get;
            set;
        }
        public float F测试采样点数
        {
            get;
            set;
        }     
        public double FPDCP_DL平均速率
        {
            get;
            set;
        }
        public double FMAC_DL平均速率
        {
            get;
            set;
        }
        public double FPHY_DL平均速率
        {
            get;
            set;
        }
        public double F覆盖率100_3
        {
            get;
            set;
        }
        public double F覆盖率110_3
        {
            get;
            set;
        }
        public double F平均RSRP
        {
            get;
            set;
        }
        public double FRSRP_80个数
        {
            get;
            set;
        }
        public double F平均SINR
        {
            get;
            set;
        }
        public double FSINR_3_R
        {
            get;
            set;
        }
        public double FSINR10_R
        {
            get;
            set;
        }
        public double F平均RSRQ
        {
            get;
            set;
        }
        public double FPRSQ_115_R
        {
            get;
            set;
        }
        public double FRSRP_80_RSRQ个数
        {
            get;
            set;
        }
        public double FRSRP_80_RSRQ_R
        {
            get;
            set;
        }
        public double F平均RI
        {
            get;
            set;
        }
        public double FRI1_R
        {
            get;
            set;
        }
        public double F平均CQI
        {
            get;
            set;
        }
        public double FCQI10_R
        {
            get;
            set;
        }
        public double F平均下行PDSCH_BLER
        {
            get;
            set;
        }
        public double F下行PDSCH_BLER10_R
        {
            get;
            set;
        }
        public double F平均下行PDCCH_BLER
        {
            get;
            set;
        }
        public double FQPSK调整_R
        {
            get;
            set;
        }
        public double F16QAM调制_R
        {
            get;
            set;
        }
        public double F64QAM调制_R
        {
            get;
            set;
        }
        public double F平均下行MCS
        {
            get;
            set;
        }
        public int F平均分配下行PRB数
        {
            get;
            set;
        }
        public int F平均时隙分配下行PRB数
        {
            get;
            set;
        }
        public double FTM1_R
        {
            get;
            set;
        }
        public double FTM2_R
        {
            get;
            set;
        }
        public double FTM3_R
        {
            get;
            set;
        }
        public double FTM7_R
        {
            get;
            set;
        }
        public double FTM8_R
        {
            get;
            set;
        }
        public double FPHY_Code0_DL平均速率
        {
            get;
            set;
        }
        public double F平均PDSCH_Code0_BLER
        {
            get;
            set;
        }
        public double FCode0_QPSK调整_R
        {
            get;
            set;
        }
        public double FCode0_16QAM调制_R
        {
            get;
            set;
        }
        public double FCode0_64QAM调制_R
        {
            get;
            set;
        }
        public double FCode0_平均下行MCS
        {
            get;
            set;
        }
        public double F平均lte_Wideband_CQI_for_CW0
        {
            get;
            set;
        }
        public double Flte_Wideband_CQI_for_CW0_10_R
        {
            get;
            set;
        }
        public double FRANK2_SINR1
        {
            get;
            set;
        }
        public double FPHY_Code1_DL平均速率
        {
            get;
            set;
        }
        public double F平均PDSCH_Code1_BLER
        {
            get;
            set;
        }
        public double FCode1_QPSK调整_R
        {
            get;
            set;
        }
        public double FCode1_16QAM调制_R
        {
            get;
            set;
        }
        public double FCode1_64QAM调制_R
        {
            get;
            set;
        }
        public double FCode1_平均下行MCS
        {
            get;
            set;
        }
        public double F平均lte_Wideband_CQI_for_CW1
        {
            get;
            set;
        }
        public double Flte_Wideband_CQI_for_CW1_10_R
        {
            get;
            set;
        }
        public double FRANK2_SINR2
        {
            get;
            set;
        }
        public double FCQI0_1_5_R
        {
            get;
            set;
        }
        public double FSinr0_1_5_R
        {
            get;
            set;
        }
        public double F双流功率不平衡_RSRP0_RSRP1
        {
            get;
            set;
        }
        public double FRSRP0_110_R
        {
            get;
            set;
        }
        public double FRSRP1_110_R
        {
            get;
            set;
        }
        public double FSINR0_3_R
        {
            get;
            set;
        }
        public double FSINR1_3_R
        {
            get;
            set;
        }
        public double F同频主小区平均邻区数
        {
            get;
            set;
        }
        public int I同频主小区最强邻区E_UtranID
        {
            get;
            set;
        }
        public string Str同频主小区最强邻区中文名
        {
            get;
            set;
        }
        public double F同频主小区采样点_R
        {
            get;
            set;
        }
        public double F同频主小区mod3平均邻区数
        {
            get;
            set;
        }
        public int I同频主小区mod3最强邻区E_UtranID
        {
            get;
            set;
        }
        public string Str同频主小区mod3最强邻区中文名
        {
            get;
            set;
        }
        public double F同频主小区mod3的采样点_R
        {
            get;
            set;
        }
    }

    public class LTEDataDownloadPerInfo
    {
        public float? flte_RSRP { get; set; }
        public float? flte_SINR { get; set; }
        public float? flte_RSRQ { get; set; }
        public float? flte_PDCP_DL { get; set; }
        public float? flte_MAC_DL { get; set; }
        public float? flte_PHY_DL { get; set; }
        public float? fRI { get; set; }
        public float? fCQI { get; set; }
        public float? fPDSCH_BLER { get; set; }
        public float? fPDCCH_BLER { get; set; }
        public int? ilte_PDSCH_PRb_Num_s { get; set; }
        public int? ilte_PDSCH_PRb_Num_slot { get; set; }
        public int? ilte_Transmission_Mode { get; set; }
        public float? fPHY_Code0_DL { get; set; }
        public float? fPDSCH_Code0_BLER { get; set; }
        public int? ilte_Times_QPSK_DLCode0 { get; set; }
        public int? ilte_Times_QAM16_DLCode0 { get; set; }
        public int? ilte_Times_QAM64_DLCode0 { get; set; }
        public int? ilte_Times_MCS_DLCode0_Sum { get; set; }
        public int ilte_Times_MCS_DLCode0_Num { get; set; }
        public float? flte_Wideband_CQI_for_CW0 { get; set; }
        public float? fRANK2_SINR1 { get; set; }
        public float? fPHY_Code1_DL { get; set; }
        public float? fPDSCH_Code1_BLER { get; set; }
        public int? ilte_Times_QPSK_DLCode1 { get; set; }
        public int? ilte_Times_QAM16_DLCode1 { get; set; }
        public int? ilte_Times_QAM64_DLCode1 { get; set; }
        public int? ilte_Times_MCS_DLCode1_Sum { get; set; }
        public int ilte_Times_MCS_DLCode1_Num { get; set; }
        public float? flte_Wideband_CQI_for_CW1 { get; set; }
        public float? fRANK2_SINR2 { get; set; }

        public float fRSRP0 { get; set; }
        public float fRSRP1 { get; set; }
        public float fSINR0 { get; set; }
        public float fSINR1 { get; set; }

        public LTEDataDownloadPerInfo()
        {
            flte_RSRP = 0;
            flte_SINR = 0;
            flte_RSRQ = 0;
            flte_PDCP_DL = 0;
            flte_MAC_DL = 0;
            flte_PHY_DL = 0;
            fRI = 0;
            fCQI = 0;
            fPDSCH_BLER = 0;
            fPDCCH_BLER = 0;
            ilte_PDSCH_PRb_Num_s = 0;
            ilte_PDSCH_PRb_Num_slot = 0;
            ilte_Transmission_Mode = 0;
            fPHY_Code0_DL = 0;
            fPDSCH_Code0_BLER = 0;
            ilte_Times_QPSK_DLCode0 = 0;
            ilte_Times_QAM16_DLCode0 = 0;
            ilte_Times_QAM64_DLCode0 = 0;
            ilte_Times_MCS_DLCode0_Sum = 0;
            ilte_Times_MCS_DLCode0_Num = 0;
            flte_Wideband_CQI_for_CW0 = 0;
            fRANK2_SINR1 = 0;
            fPHY_Code1_DL = 0;
            fPDSCH_Code1_BLER = 0;
            ilte_Times_QPSK_DLCode1 = 0;
            ilte_Times_QAM16_DLCode1 = 0;
            ilte_Times_QAM64_DLCode1 = 0;
            ilte_Times_MCS_DLCode1_Sum = 0;
            ilte_Times_MCS_DLCode1_Num = 0;
            flte_Wideband_CQI_for_CW1 = 0;
            fRANK2_SINR2 = 0;
        }

        public LTEDataDownloadPerInfo(TestPoint tp) : this()
        {
            flte_RSRP = (float?)tp["lte_RSRP"];
            flte_SINR = (float?)tp["lte_SINR"];
            flte_RSRQ = tp["lte_RSRQ"] == null ? -999 : (float?)tp["lte_RSRQ"];
            flte_PDCP_DL = tp["lte_PDCP_DL"] == null ? -999 : (float)(int?)tp["lte_PDCP_DL"] / 1000 / 1000;
            flte_MAC_DL = tp["lte_MAC_DL"] == null ? -999 : (float)(int?)tp["lte_MAC_DL"] / 1000 / 1000;
            flte_PHY_DL = tp["lte_PHY_DL"] == null ? -999 : (float)(int?)tp["lte_PHY_DL"] / 1000 / 1000;
            fRI = getValidDataFloatShort(tp["lte_Rank_Indicator"], -999);
            fCQI = getValidDataFloatShort(tp["lte_Wideband_CQI"], -999);
            fPDSCH_BLER = tp["lte_PDSCH_BLER"] == null ? -999 : (float?)tp["lte_PDSCH_BLER"];
            fPDCCH_BLER = getValidDataFloatShort(tp["lte_PDCCH_BLER"], -999);
            ilte_PDSCH_PRb_Num_s = getValidDataInt(tp["lte_PDSCH_PRb_Num_s"], -999);
            ilte_PDSCH_PRb_Num_slot = getValidDataInt(tp["lte_PDSCH_PRb_Num_slot"], -999);
            ilte_Transmission_Mode = tp["lte_Transmission_Mode"] == null ? -999 : (int?)(short)tp["lte_Transmission_Mode"];
            fPHY_Code0_DL = tp["lte_PHY_Code0_DL"] == null ? -999 : (float)(int?)tp["lte_PHY_Code0_DL"] / 1000 / 1000;
            fPDSCH_Code0_BLER = tp["lte_PDSCH_Code0_BLER"] == null ? -999 : (float?)tp["lte_PDSCH_Code0_BLER"];
            ilte_Times_QPSK_DLCode0 = getValidDataInt(tp["lte_Times_QPSK_DLCode0"], -999);
            ilte_Times_QAM16_DLCode0 = getValidDataInt(tp["lte_Times_QAM16_DLCode0"], -999);
            ilte_Times_QAM64_DLCode0 = getValidDataInt(tp["lte_Times_QAM64_DLCode0"], -999);
            flte_Wideband_CQI_for_CW0 = getValidDataFloatShort(tp["lte_Wideband_CQI_for_CW0"], -999);
            fRANK2_SINR1 = getValidDataFloatShort(tp["lte_RANK2_SINR1"], -999);
            fPHY_Code1_DL = tp["lte_PHY_Code1_DL"] == null ? -999 : (float)(int?)tp["lte_PHY_Code1_DL"] / 1000 / 1000;
            fPDSCH_Code1_BLER = tp["lte_PDSCH_Code1_BLER"] == null ? -999 : (float?)tp["lte_PDSCH_Code1_BLER"];
            ilte_Times_QPSK_DLCode1 = getValidDataInt(tp["lte_Times_QPSK_DLCode1"], -999);
            ilte_Times_QAM16_DLCode1 = getValidDataInt(tp["lte_Times_QAM16_DLCode1"], -999);
            ilte_Times_QAM64_DLCode1 = getValidDataInt(tp["lte_Times_QAM64_DLCode1"], -999);
            flte_Wideband_CQI_for_CW1 = getValidDataFloatShort(tp["lte_Wideband_CQI_for_CW1"], -999);
            fRANK2_SINR2 = getValidDataFloatShort(tp["lte_RANK2_SINR2"], -999);
            for (int i = 0; i < 32; i++) 
            {
                ilte_Times_MCS_DLCode0_Sum += getValidDataIntShort(tp["lte_MCSCode0_DL", i], 0) * i;
                ilte_Times_MCS_DLCode0_Num += getValidDataIntShort(tp["lte_MCSCode0_DL", i], 0);
                ilte_Times_MCS_DLCode1_Sum += getValidDataIntShort(tp["lte_MCSCode1_DL", i], 0) * i;
                ilte_Times_MCS_DLCode1_Num += getValidDataIntShort(tp["lte_MCSCode1_DL", i], 0);
            }
        }

        private int getValidDataInt(object data, int def)
        {
            if (data == null)
            {
                return def;
            }
            return (int)data;
        }

        private int getValidDataIntShort(object data, int def)
        {
            if (data == null)
            {
                return def;
            }
            return (int)(short?)data;
        }

        private float getValidDataFloatShort(object data, int def)
        {
            if (data == null)
            {
                return def;
            }
            return (float)(short?)data;
        }

        public LTEDataDownloadPerInfo(TestPoint tp, string strScan) : this()
        {
        }
    }

    public class LTEDataDownloadStatInfo
    {
        public LTEDataDownloadStatInfo()
        {
            tmDic = new Dictionary<int, int>();
        }

        public float fRSRP_80Num  { get; set; }
        public float fRSRQ_Sum  { get; set; }
        public float fRSRQ_Num  { get; set; }
        public float fRSRQ_115Num  { get; set; }
        public float fRSRP_80_RQRQ_115Num  { get; set; }
        public float fFPDCP_DLSum  { get; set; }
        public float fFPDCP_DLNum  { get; set; }
        public float fFMAC_DLSum  { get; set; }
        public float fFMAC_DLNum  { get; set; }
        public float fFPHY_DLSum  { get; set; }
        public float fFPHY_DLNum  { get; set; }
        public float fRsrpSinr100_3  { get; set; }
        public float fRsrpSinr110_3  { get; set; }
        public float fRsrpSum  { get; set; }
        public float fSinrSum  { get; set; }
        public float fSirn_3  { get; set; }
        public float fSinr10  { get; set; }
        public float fRISum  { get; set; }
        public float fRINum  { get; set; }
        public float fRI1Num  { get; set; }
        public float fCQISum  { get; set; }
        public float fCQINum  { get; set; }
        public float fCQI10Num  { get; set; }
        public float fPDSCH_BLERSum  { get; set; }
        public float fPDSCH_BLERNum  { get; set; }
        public float fPDSCH_BLER10Num  { get; set; }
        public float fPDCCH_BLERSum  { get; set; }
        public float fPDCCH_BLERNum  { get; set; }
        public float fQPSK_DLCode0Sum  { get; set; }
        public float fQPSK_DLCode1Sum  { get; set; }
        public float fQAM16_DLCode0Sum  { get; set; }
        public float fQAM16_DLCode1Sum  { get; set; }
        public float fQAM64_DLCode0Sum  { get; set; }
        public float fQAM64_DLCode1Sum  { get; set; }
        public float fMCS0Sum  { get; set; }
        public float fMCS0Num  { get; set; }
        public float fMCS1Sum  { get; set; }
        public float fMCS1Num  { get; set; }
        public float fPRBSum  { get; set; }
        public float fPRBNum  { get; set; }
        public float fPRBSlotSum  { get; set; }
        public float fPRBSlotNum  { get; set; }
        public Dictionary<int, int> tmDic { get; set; }
        public float fPHY_Code0_DLSum  { get; set; }
        public float fPHY_Code0_DLNum  { get; set; }
        public float fPDSCH_Code0_BLERSum  { get; set; }
        public float fPDSCH_Code0_BLERNum  { get; set; }
        public float fPHY_Code1_DLSum  { get; set; }
        public float fPHY_Code1_DLNum  { get; set; }
        public float fPDSCH_Code1_BLERSum  { get; set; }
        public float fPDSCH_Code1_BLERNum  { get; set; }
        public float flte_Wideband_CQI_for_CW0Sum  { get; set; }
        public float flte_Wideband_CQI_for_CW0Num  { get; set; }
        public float flte_Wideband_CQI_for_CW0_10Num  { get; set; }
        public float flte_Wideband_CQI_for_CW1Sum  { get; set; }
        public float flte_Wideband_CQI_for_CW1Num  { get; set; }
        public float flte_Wideband_CQI_for_CW1_10Num  { get; set; }
        public float fRANK2_SINR1Sum  { get; set; }
        public float fRANK2_SINR1Num  { get; set; }
        public float fRANK2_SINR2Sum  { get; set; }
        public float fRANK2_SINR2Num  { get; set; }
        public float fCQI0_1Num  { get; set; }
        public float fCQI0_1_5Num  { get; set; }
        public float fRANK2_SINR_Num  { get; set; }
        public float fRANK2_SINR_5Num  { get; set; }
        public float fRSRP0Sum  { get; set; }
        public float fRSRP1Sum  { get; set; }
        public float fRSRP0_110Num  { get; set; }
        public float fRSRP1_110Num  { get; set; }
        public float fSINR0_3Num  { get; set; }
        public float fSINR1_3Num  { get; set; }
        public float fScanSampleNum  { get; set; }
    }
}
