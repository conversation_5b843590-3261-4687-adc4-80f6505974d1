﻿using DevExpress.XtraEditors;
using MasterCom.RAMS.BackgroundFunc;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class VolteWeakMosReasonAnaBase : DIYAnalyseByFileBackgroundBase
    {
        VoLteWeakMosReasonAnaDlg condDlg;
        WeakMosReasonAnaCondtion cond;
        int mosTpCount = 0;
        readonly List<VoLteWeakMosReasonInfo> resultList = new List<VoLteWeakMosReasonInfo>();

        readonly VolteWeakMosReasonManager manager;
        readonly VolteWeakMosReasonHelper helper;

        /// <summary>
        /// 未匹配的对主端文件记录
        /// </summary>
        StringBuilder strbErr = null;

        protected static readonly object lockObj = new object();
        protected VolteWeakMosReasonAnaBase()
            : base(MainModel.GetInstance())
        {
            this.FilterSampleByRegion = true;
            this.IncludeMessage = true;
            ServiceTypes.Clear();
            ServiceTypes.AddRange(ServiceTypeManager.GetServiceTypesByServiceName(ServiceName.VoLTE));

            Columns = new List<string>();
            Columns.Add("lte_RSRP");
            Columns.Add("lte_SINR");
            Columns.Add("lte_PESQMos");
            Columns.Add("lte_POLQA_Score_SWB");
            Columns.Add("lte_volte_RTP_Packets_Lost_Num");

            helper = new VolteWeakMosReasonHelper(MainModel.GetInstance(), ServiceTypes);
            manager = new VolteWeakMosReasonManager(helper);
        }
        public override string Name
        {
            get { return "弱MOS原因分析"; }
        }
        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 27000, 27018, this.Name);
        }

        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }
            if (condDlg == null)
            {
                condDlg = new VoLteWeakMosReasonAnaDlg();
            }
            condDlg.SetCondition(cond);
            strbErr = new StringBuilder();
            if (condDlg.ShowDialog() == DialogResult.OK)
            {
                cond = condDlg.GetCondition();
                return true;
            }
            return false;
        }
        protected override void getReadyBeforeQuery()
        {
            mosTpCount = 0;
            resultList.Clear();
        }

        #region 分析主被叫文件
        /// <summary>
        /// 对主叫和被叫文件一起分析
        /// </summary>
        protected override void analyseFiles()
        {
            Dictionary<FileInfo, FileInfo> moMtPair = helper.GetMoMtPair();
            strbErr = helper.StrbErr;
            try
            {
                clearDataBeforeAnalyseFiles();

                replayFiles(moMtPair);
                MainModel.ClearDTData();
                doSomethingAfterAnalyseFiles();
            }
            finally
            {
                System.Threading.Thread.Sleep(10);
                WaitBox.Close();
            }
        }

        private void replayFiles(Dictionary<FileInfo, FileInfo> moMtPair)
        {
            int iloop = 0;
            foreach (KeyValuePair<FileInfo, FileInfo> pair in moMtPair)
            {
                WaitBox.Text = "正在分析文件( " + (++iloop) + "/" + moMtPair.Count + " )...";
                WaitBox.ProgressPercent = (int)(iloop * 100.0 / moMtPair.Count);

                condition.FileInfos.Clear();
                if (pair.Key != null)
                {
                    condition.FileInfos.Add(pair.Key);
                }
                if (pair.Value != null)
                {
                    condition.FileInfos.Add(pair.Value);
                }

                replay();

                if (WaitBox.CancelRequest)
                {
                    break;
                }
            }
        }

        ///// <summary>
        ///// 对主叫和被叫文件一起分析
        ///// </summary>
        //protected override void analyseFiles()
        //{
        //    Dictionary<FileInfo, FileInfo> moMtPair = helper.GetMoMtPair();
        //    strbErr = helper.StrbErr;

        //    try
        //    {
        //        clearDataBeforeAnalyseFiles();

        //        if (MainModel.IsBackground)
        //        {
        //            BackgroundFuncManager.GetInstance().ReportBackgroundInfo("共读取待分析文件" + moMtPair.Count + "个...");
        //        }
        //        replayFiles(moMtPair);
        //        MainModel.ClearDTData();
        //        doSomethingAfterAnalyseFiles();
        //    }
        //    finally
        //    {
        //        System.Threading.Thread.Sleep(10);
        //        WaitBox.Close();
        //    }
        //}

        //private void replayFiles(Dictionary<FileInfo, FileInfo> moMtPair)
        //{
        //    int iloop = 0;
        //    foreach (KeyValuePair<FileInfo, FileInfo> pair in moMtPair)
        //    {
        //        if (MainModel.IsBackground)
        //        {
        //            if (MainModel.BackgroundStopRequest)
        //            {
        //                break;
        //            }
        //            BackgroundFuncManager.GetInstance().ReportBackgroundInfo("正在分析 " + FuncType.ToString() +
        //                SubFuncType.ToString() + " 类 " + Name + "，当前文件 " + (++iloop) + "/" + moMtPair.Count +
        //                "个...文件名：" + pair.Key.Name + pair.Value.Name);
        //        }
        //        else
        //        {
        //            WaitBox.Text = "正在分析文件( " + (++iloop) + "/" + moMtPair.Count + " )...";
        //            WaitBox.ProgressPercent = (int)(iloop * 100.0 / moMtPair.Count);
        //        }

        //        condition.FileInfos.Clear();
        //        if (pair.Key != null)
        //        {
        //            condition.FileInfos.Add(pair.Key);
        //        }
        //        if (pair.Value != null)
        //        {
        //            condition.FileInfos.Add(pair.Value);
        //        }

        //        replay();
        //        condition.FileInfos.Clear();
        //        if (WaitBox.CancelRequest)
        //        {
        //            break;
        //        }
        //    }
        //}
        #endregion

        protected override void doStatWithQuery()
        {
            DTFileDataManager moFile = null;
            DTFileDataManager mtFile = null;
            foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
            {
                if (file.MoMtFlag == 1)
                {
                    moFile = file;
                }
                else
                {
                    mtFile = file;
                }
            }
            List<VoLteWeakMosReasonInfo> moResultList = dealSourceEndInfo(moFile);
            List<VoLteWeakMosReasonInfo> mtResultList = dealSourceEndInfo(mtFile);
            if (moResultList.Count == 0 && mtResultList.Count == 0)
            {
                return;
            }

            Dictionary<TimePeriod, VoLteWeakMosReasonInfo> resDic = new Dictionary<TimePeriod, VoLteWeakMosReasonInfo>();
            dealOppositeEndInfo(mtFile, ref resDic, moResultList);
            dealOppositeEndInfo(moFile, ref resDic, mtResultList);
            resDic = Sort.SortDic(resDic, sortByTimePeriod);
            foreach (KeyValuePair<TimePeriod, VoLteWeakMosReasonInfo> resultItem in resDic)
            {
                resultItem.Value.SN = resultList.Count + 1;
                resultList.Add(resultItem.Value);
            }
        }

        private int sortByTimePeriod(KeyValuePair<TimePeriod, VoLteWeakMosReasonInfo> s1, KeyValuePair<TimePeriod, VoLteWeakMosReasonInfo> s2)
        {
            return s1.Key.BeginTime.CompareTo(s2.Key.BeginTime);
        }

        #region 处理主端结果
        /// <summary>
        /// 查询文件中的弱Mos集合
        /// </summary>
        /// <param name="fileMng">查询的文件</param>
        /// <param name="resultLists">保存弱Mos集合</param>
        private List<VoLteWeakMosReasonInfo> dealSourceEndInfo(DTFileDataManager fileMng)
        {
            List<VoLteWeakMosReasonInfo> resultLists = new List<VoLteWeakMosReasonInfo>();
            if (fileMng == null)
            {
                return resultLists;
            }

            string mosParamName = "";
            List<DTData> dtDataList = fileMng.DTDatas;
            dtDataList.Sort(DTData.ComparerBySnAsc);

            Event curCycleCallBeginEvt = null;
            Event curCycleEsrvccEvt = null;
            try
            {
                VoLteWeakMosReasonInfo callInfo = new VoLteWeakMosReasonInfo(fileMng.FileName, null);
                for (int i = 0; i < dtDataList.Count; i++)
                {//先粗略地按mos点切割成若干周期，保存信息时再调用GetValidInfo获取各mos周期内的有效信息
                    DTData dt = dtDataList[i];
                    switch (dt)
                    {
                        case TestPoint tp:
                            addTP(fileMng.FileName, resultLists, ref mosParamName, curCycleCallBeginEvt, curCycleEsrvccEvt, ref callInfo, tp);
                            break;
                        case Event evt:
                            addEvt(evt, ref curCycleCallBeginEvt, ref curCycleEsrvccEvt, callInfo);
                            break;
                        case Model.Message msg:
                            addMsg(msg, callInfo);
                            break;
                    }
                }
            }
            catch (Exception ee)
            {
                MessageBox.Show(ee.Message + Environment.NewLine + ee.Source + Environment.NewLine + ee.StackTrace);
            }
            return resultLists;
        }

        /// <summary>
        /// 根据采样点判断是否是弱MOS点
        /// 是弱MOS点判断原因并添加到结果集
        /// </summary>
        /// <param name="fileName">文件名</param>
        /// <param name="resultLists">弱MOS结果集</param>
        /// <param name="mosParamName">弱MOS参数名</param>
        /// <param name="curCycleCallBeginEvt">起呼事件</param>
        /// <param name="curCycleEsrvccEvt">Esrvcc事件</param>
        /// <param name="callInfo">当前弱MOS结果</param>
        /// <param name="tp">采样点</param>
        private void addTP(string fileName, List<VoLteWeakMosReasonInfo> resultLists, ref string mosParamName
            , Event curCycleCallBeginEvt, Event curCycleEsrvccEvt, ref VoLteWeakMosReasonInfo callInfo, TestPoint tp)
        {
            callInfo.TestPoints.Add(tp);
            float? mos = GetMosInfo(tp, ref mosParamName);
            if (mos != null && mos > 0)
            {//采样点有Mos值时，结束本周期并保存弱Mos信息，然后开始新的周期
                mosTpCount++;
                if (mos <= cond.MosWeakGate)
                {
                    callInfo.SetMosValue(tp, (float)mos);
                    manager.GetWeakMosReasonInfo(callInfo, cond);
                    callInfo.SN = resultLists.Count + 1;
                    resultLists.Add(callInfo);
                }

                callInfo = new VoLteWeakMosReasonInfo(fileName, curCycleCallBeginEvt);
                callInfo.EsrvccEvt = curCycleEsrvccEvt;
            }
        }

        public float? GetMosInfo(TestPoint tp, ref string curMosParamName)
        {
            if (string.IsNullOrEmpty(curMosParamName))
            {
                float? pesq = (float?)tp["lte_PESQMos"];
                float? polqa = (float?)tp["lte_POLQA_Score_SWB"];
                if (NRTpHelper.JudgeValidData(pesq, 0, 5))
                {
                    curMosParamName = "lte_PESQMos";
                    return pesq;
                }
                else if (NRTpHelper.JudgeValidData(polqa, 0, 5))
                {
                    curMosParamName = "lte_POLQA_Score_SWB";
                    return polqa;
                }
            }
            else
            {
                float? mos = (float?)tp[curMosParamName];
                if (NRTpHelper.JudgeValidData(mos, 0, 5))
                {
                    return mos;
                }
            }
            return null;
        }

        private void addEvt(Event evt, ref Event curCycleCallBeginEvt, ref Event curCycleEsrvccEvt, VoLteWeakMosReasonInfo callInfo)
        {
            if (helper.CallBeginEvtIdList.Contains(evt.ID))
            {//呼叫刚建立时初始化信息
                curCycleCallBeginEvt = evt;
                curCycleEsrvccEvt = null;
            }
            else if (evt.ID == helper.P2CHandoverSuccess)
            {
                curCycleEsrvccEvt = evt;
            }
            else if (helper.CallEndEvtIdList.Contains(evt.ID))
            {//eSRVCC call end或者eSRVCC call Drop时清空curCycleEsrvccEvt
                curCycleEsrvccEvt = null;
            }
            else if (helper.HandOverEvtIdList.Contains(evt.ID))
            {
                callInfo.HandOverEvents.Add(evt);
            }
        }

        private void addMsg(Model.Message msg, VoLteWeakMosReasonInfo callInfo)
        {
            if (helper.ReestablishmentRequestMsg.Contains(msg.ID))//RRC重建 ConnectionReestablishmentRequest
            {
                //用于分析RRC重建问题
                callInfo.RRCReestablishMsg = msg;
            }
        }
        #endregion

        #region 处理对端结果
        /// <summary>
        /// 源文件去匹配对比文件中是否存在对应的弱Mos点
        /// </summary>
        /// <param name="destinationFile">对比文件</param>
        /// <param name="resDic">临时结果集</param>
        /// <param name="sourceFileList">源文件弱Mos结果集</param>
        private void dealOppositeEndInfo(DTFileDataManager destinationFile, ref Dictionary<TimePeriod, VoLteWeakMosReasonInfo> resDic,
            List<VoLteWeakMosReasonInfo> sourceFileList)
        {
            //记录每次找到采样点的index,提高性能,不用每次从0开始查找
            List<DTData> dtDataList = null;
            if (destinationFile != null)
            {
                dtDataList = destinationFile.DTDatas;
                dtDataList.Sort(DTData.ComparerBySnAsc);
            }

            int tpIndex = 0;
            foreach (VoLteWeakMosReasonInfo sourceEndRes in sourceFileList)
            {
                //循环源文件弱Mos结果集,对端是否在该弱Mos时段内有主端没有匹配到的弱Mos原因
                if (destinationFile != null)
                {
                    //根据源文件时段查询对比文件中是否在该时段产生弱Mos原因
                    VoLteWeakMosReasonInfo oppositeEndRes = checkReasonByPeriod(dtDataList, destinationFile.FileName, sourceEndRes.MosTimePeriod, ref tpIndex);
                    setReason(sourceEndRes, oppositeEndRes);
                    sourceEndRes.SetOppositeInfo(oppositeEndRes);
                }
                resDic[sourceEndRes.MosTimePeriod] = sourceEndRes;
            }
        }

        /// <summary>
        ///  判断对应时间段内是否有弱Mos产生的原因
        /// </summary>
        /// <param name="fileMng">查询的文件</param>
        /// <param name="timePeriod">查询的时段</param>
        /// <param name="tpIndex">记录查找到的采样点序号</param>
        /// <returns></returns>
        private VoLteWeakMosReasonInfo checkReasonByPeriod(List<DTData> dtDataList, string fileName, TimePeriod timePeriod, ref int tpIndex)
        {
            try
            {
                VoLteWeakMosReasonInfo callInfo = new VoLteWeakMosReasonInfo(fileName, null);

                getEvtAndDTDatas(dtDataList, timePeriod, callInfo, ref tpIndex);
                callInfo.RemoveInvalidInfo(timePeriod);
                callInfo.GetTPInfo();
                manager.GetWeakMosReasonInfo(callInfo, cond);

                return callInfo;
            }
            catch (Exception ee)
            {
                MessageBox.Show(ee.Message + Environment.NewLine + ee.Source + Environment.NewLine + ee.StackTrace);
                return null;
            }
        }

        /// <summary>
        /// 通过dtDataList中的数据按Mos时段查找采样点,起呼事件,Esrvcc事件,频繁切换,RRC
        /// </summary>
        /// <param name="dtDataList">DTFileDataManager中的DTDatas</param>
        /// <param name="timePeriod">弱Mos时段</param>
        /// <param name="callInfo">保存结果数据(采样点,起呼事件,Esrvcc事件,频繁切换,RRC)</param>
        /// <param name="tpIndex">记录查找到的采样点序号</param>
        private void getEvtAndDTDatas(List<DTData> dtDataList, TimePeriod timePeriod, VoLteWeakMosReasonInfo callInfo, ref int tpIndex)
        {
            //起呼事件
            Event curCycleCallBeginEvt = null;
            //Esrvcc事件
            Event curCycleEsrvccEvt = null;
            ////保存对应时段的采样点
            List<TestPoint> tps = new List<TestPoint>();

            bool hasFindCallBeginEvt = true;
            for (; tpIndex < dtDataList.Count; tpIndex++)
            {
                //是否在时间段内
                if (timePeriod.BeginTime <= dtDataList[tpIndex].DateTime && dtDataList[tpIndex].DateTime <= timePeriod.EndTime)
                {
                    addValueFromDtDatas(dtDataList, callInfo, tpIndex, tps);
                    if (hasFindCallBeginEvt)
                    {
                        pushBackEvt(dtDataList, tpIndex, ref curCycleCallBeginEvt, ref curCycleEsrvccEvt);
                        hasFindCallBeginEvt = false;
                    }
                }
                else if (dtDataList[tpIndex].DateTime > timePeriod.EndTime)
                {
                    break;
                }
            }

            callInfo.TestPoints = tps;
            callInfo.MosTestPoints = tps;
            callInfo.CallBeginEvt = curCycleCallBeginEvt;
            callInfo.EsrvccEvt = curCycleEsrvccEvt;
        }

        private void addValueFromDtDatas(List<DTData> dtDataList, VoLteWeakMosReasonInfo callInfo, int tpIndex, List<TestPoint> tps)
        {
            DTData dt = dtDataList[tpIndex];
            switch (dt)
            {
                case TestPoint tp:
                    tps.Add(tp);
                    break;
                case Event evt:
                    if (helper.HandOverEvtIdList.Contains(evt.ID))
                    {
                        //切换事件
                        callInfo.HandOverEvents.Add(evt);
                    }
                    break;
                case Model.Message msg:
                    if (helper.ReestablishmentRequestMsg.Contains(msg.ID))
                    {
                        //RRC重建 ConnectionReestablishmentRequest
                        callInfo.RRCReestablishMsg = msg;
                    }
                    break;
            }
        }

        private void pushBackEvt(List<DTData> dtDataList, int tpIndex, ref Event curCycleCallBeginEvt, ref Event curCycleEsrvccEvt)
        {
            //由于是倒推,避免Esrvcc事件被置为null,假定一开始就存在Esrvcc事件
            bool hasEsrvccEvt = true;
            //找到对端的弱Mos时段后倒推该时段所属的起呼事件(最近的一个起呼事件),并判断是否有Esrvcc事件
            for (int j = tpIndex; j > 0; j--)
            {
                if (dtDataList[j] is Event evt)
                {
                    if (helper.CallBeginEvtIdList.Contains(evt.ID))
                    {//呼叫刚建立时初始化信息
                        curCycleCallBeginEvt = evt;
                        if (!hasEsrvccEvt)
                        {
                            curCycleEsrvccEvt = null;
                        }
                        break;
                    }
                    else if (evt.ID == helper.P2CHandoverSuccess && hasEsrvccEvt)
                    {
                        curCycleEsrvccEvt = evt;
                    }
                    else if (helper.CallEndEvtIdList.Contains(evt.ID))
                    {//eSRVCC call end或者eSRVCC call Drop时清空curCycleEsrvccEvt
                        curCycleEsrvccEvt = null;
                        hasEsrvccEvt = false;
                    }
                }
            }
        }

        private void setReason(VoLteWeakMosReasonInfo sourceFile, VoLteWeakMosReasonInfo res)
        {
            if (res != null && res.ReasonList[0] != "其他")
            {
                if (sourceFile.ReasonList.Contains("其他"))
                {
                    sourceFile.ReasonList.Remove("其他");
                }
                foreach (string reason in res.ReasonList)
                {
                    if (!sourceFile.ReasonList.Contains(reason))
                    {
                        sourceFile.ReasonList.Add(reason);
                    }
                }
            }
        }
        #endregion

        protected override void doSomethingAfterAnalyseFiles()
        {
            foreach (var item in resultList)
            {
                item.Calculate();
            }
        }

        protected override void fireShowForm()
        {
            if (strbErr.Length > 0)
            {
                XtraMessageBox.Show(strbErr.ToString());
            }
            if (resultList.Count == 0)
            {
                System.Windows.Forms.MessageBox.Show("没有符合条件的信息！");
                return;
            }
            VoLteWeakMosReasonInfoForm frm = MainModel.CreateResultForm(typeof(VoLteWeakMosReasonInfoForm)) as VoLteWeakMosReasonInfoForm;
            frm.FillData(mosTpCount, resultList, cond.ReasonList);
            frm.Owner = MainModel.MainForm;
            frm.Visible = true;
            frm.BringToFront();
        }
    }

    public class VolteWeakMosReasonAnaByFile : VolteWeakMosReasonAnaBase
    {
        private VolteWeakMosReasonAnaByFile()
            : base()
        {
        }

        private static VolteWeakMosReasonAnaByFile instance = null;
        public static VolteWeakMosReasonAnaByFile GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new VolteWeakMosReasonAnaByFile();
                    }
                }
            }
            return instance;
        }

        public override string Name
        {
            get { return "弱MOS原因分析(按文件)"; }
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.File;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                return true;
            }
            return false;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        /// <summary>
        /// 判断是否在所选区域内
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            return true;
        }

    }
    public class VolteWeakMosReasonAnaByRegion : VolteWeakMosReasonAnaBase
    {
        protected VolteWeakMosReasonAnaByRegion()
            : base()
        {
        }

        private static VolteWeakMosReasonAnaByRegion instance = null;
        public static VolteWeakMosReasonAnaByRegion GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new VolteWeakMosReasonAnaByRegion();
                    }
                }
            }
            return instance;
        }

        public override string Name
        {
            get { return "弱MOS原因分析(按区域)"; }
        }
    }
}
