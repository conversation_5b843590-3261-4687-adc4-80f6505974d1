﻿using System;
using System.Collections.Generic;
using System.Text;

using System.Drawing;
using System.Drawing.Drawing2D;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc
{
    public class PointStatusColoringLayer : CustomDrawLayer
    {
        public PointStatusColoringLayer(MapOperation oper, String name) : base(oper, name)
        {
        }

        public static PointStatusColoringLayer Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new PointStatusColoringLayer(MainModel.GetInstance().MainForm.GetMapForm().GetMapOperation(), "点状态着色图层");
                }
                return instance;
            }
        }

        public void GetSelectedInfo(MapOperation2 mop2, out List<string> titles, out List<string> infos)
        {
            titles = new List<string>();
            infos = new List<string>();
            foreach (StatusPointItem pt in PointList)
            {
                DbPoint dPoint = new DbPoint(pt.Longitude, pt.Latitude);
                PointF point;
                Map.ToDisplay(dPoint, out point);
                RectangleF rect;
                rect = new RectangleF(point.X - pt.Style.PointSize / 2, point.Y - pt.Style.PointSize / 2, pt.Style.PointSize, pt.Style.PointSize);

                DbRect dRect;
                Map.FromDisplay(rect, out dRect);
                if (!mop2.CheckCenterInDRect(dRect))
                {
                    continue;
                }

                if (pt.ShowAttributes.Count > 0)
                {
                    titles.Add(pt.ShowAttributes[0]);
                    StringBuilder sb = new StringBuilder();
                    foreach (string row in pt.ShowAttributes)
                    {
                        sb.AppendLine(row);
                    }
                    infos.Add(sb.ToString());
                }
            }
        }

        public override void Draw(Rectangle clientRect, Rectangle updateRect, Graphics graphics)
        {
            if (PointList == null || PointList.Count == 0)
            {
                return;
            }

            float ratio = CustomDrawLayer.GetTestPointDisplayRatio(Map.Scale);
            updateRect.Inflate((int)(64 * ratio), (int)(64 * ratio));
            DbRect dRect;
            Map.FromDisplay(updateRect, out dRect);

            foreach (StatusPointItem sp in PointList)
            {
                if (dRect.IsPointInThisRect(sp.Longitude, sp.Latitude) && sp.Style.Visible)
                {
                    DbPoint dPoint = new DbPoint(sp.Longitude, sp.Latitude);
                    PointF point;
                    Map.ToDisplay(dPoint, out point);

                    Brush brush = new SolidBrush(sp.Style.FillColor);
                    float radius = ratio * sp.Style.PointSize;
                    graphics.TranslateTransform(point.X, point.Y);
                    graphics.ScaleTransform(radius, radius);
                    GraphicsPath path = SymbolManager.GetInstance().Paths[sp.Style.Symbol];
                    graphics.FillPath(brush, path);
                    graphics.ResetTransform();
                }
            }
        }

        public static List<StatusPointItem> PointList { get; set; } = new List<StatusPointItem>();

        private static PointStatusColoringLayer instance;
    }

    public class StatusPointItem
    {
        public double Longitude { get; set; }
        public double Latitude { get; set; }

        public StatusPointStyle Style { get; set; } = new StatusPointStyle();

        public List<string> ShowAttributes { get; set; } = new List<string>();
    }

    public class StatusPointStyle
    {
        public Color FillColor { get; set; }
        public int PointSize { get; set; }
        public int Symbol { get; set; }
        public bool Visible { get; set; }
        public string Name { get; set; }
    }
}
