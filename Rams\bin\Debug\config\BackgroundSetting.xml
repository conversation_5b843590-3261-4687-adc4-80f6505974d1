<?xml version="1.0"?>
<Configs>
  <Config name="BackgroundSetting">
    <Item name="BackgroundFunc" typeName="IDictionary">
      <Item typeName="IDictionary" key="BaseSetting">
        <Item typeName="Boolean" key="IsNeedLoadCurCityWorkParam">True</Item>
        <Item typeName="Boolean" key="IsNeedResetCurCityMap">True</Item>
        <Item typeName="Int32" key="ValidUnit">1</Item>
        <Item typeName="Boolean" key="IsCheckPrjId">True</Item>
        <Item typeName="String" key="ProjectType">1,2,3,4,5,6,7,8,9,10,11,12,13</Item>
        <Item typeName="IList" key="ProjectTypeList">
          <Item typeName="Int32">23</Item>
        </Item>
        <Item typeName="Int32" key="QueryTimeType">1</Item>
        <Item typeName="String" key="DSTimeSet">2016-12-01</Item>
        <Item typeName="String" key="DETimeSet">2016-12-02</Item>
        <Item typeName="String" key="StrCityNames">武汉</Item>
        <Item typeName="IDictionary" key="MapInfoRegion">
          <Item typeName="IDictionary" key="2">
            <Item typeName="String" key="MapPath">D:\江西路网通 - 自动导出版本\GEOGRAPHIC\赣州\赣州城区和县城-cj_region.shp</Item>
            <Item typeName="String" key="MapName">赣州城区和县城-cj_region</Item>
            <Item typeName="String" key="ColumnName">具体场景</Item>
          </Item>
          <Item typeName="IDictionary" key="3">
            <Item typeName="String" key="MapPath">D:\江西路网通 - 自动导出版本\GEOGRAPHIC\吉安\吉安城区和县城-cj_region.shp</Item>
            <Item typeName="String" key="MapName">吉安城区和县城-cj_region</Item>
            <Item typeName="String" key="ColumnName">具体场景</Item>
          </Item>
          <Item typeName="IDictionary" key="4">
            <Item typeName="String" key="MapPath">D:\江西路网通 - 自动导出版本\GEOGRAPHIC\景德镇\景德镇城区和县城-cj_region.shp</Item>
            <Item typeName="String" key="MapName">景德镇城区和县城-cj_region</Item>
            <Item typeName="String" key="ColumnName">具体场景</Item>
          </Item>
          <Item typeName="IDictionary" key="5">
            <Item typeName="String" key="MapPath">D:\江西路网通 - 自动导出版本\GEOGRAPHIC\九江\九江城区和县城-cj_region.shp</Item>
            <Item typeName="String" key="MapName">九江城区和县城-cj_region</Item>
            <Item typeName="String" key="ColumnName">具体场景</Item>
          </Item>
          <Item typeName="IDictionary" key="6">
            <Item typeName="String" key="MapPath">D:\江西路网通 - 自动导出版本\GEOGRAPHIC\南昌\南昌城区和县城-cj_region.shp</Item>
            <Item typeName="String" key="MapName">南昌城区和县城-cj_region</Item>
            <Item typeName="String" key="ColumnName">具体场景</Item>
          </Item>
          <Item typeName="IDictionary" key="1">
            <Item typeName="String" key="MapPath">E:\Project\Rams\bin\Debug\GEOGRAPHIC\武汉\市界_region.shp</Item>
            <Item typeName="String" key="MapName">市界_region</Item>
            <Item typeName="String" key="ColumnName">NAME</Item>
          </Item>
          <Item typeName="IDictionary" key="8">
            <Item typeName="String" key="MapPath">D:\江西路网通 - 自动导出版本\GEOGRAPHIC\上饶\上饶城区和县城-cj_region.shp</Item>
            <Item typeName="String" key="MapName">上饶城区和县城-cj_region</Item>
            <Item typeName="String" key="ColumnName">具体场景</Item>
          </Item>
          <Item typeName="IDictionary" key="9">
            <Item typeName="String" key="MapPath">D:\江西路网通 - 自动导出版本\GEOGRAPHIC\新余\新余城区和县城-cj_region.shp</Item>
            <Item typeName="String" key="MapName">新余城区和县城-cj_region</Item>
            <Item typeName="String" key="ColumnName">具体场景</Item>
          </Item>
          <Item typeName="IDictionary" key="7">
            <Item typeName="String" key="MapPath">D:\江西路网通 - 自动导出版本\GEOGRAPHIC\萍乡\萍乡城区和县城-cj_region.shp</Item>
            <Item typeName="String" key="MapName">萍乡城区和县城-cj_region</Item>
            <Item typeName="String" key="ColumnName">具体场景</Item>
          </Item>
          <Item typeName="IDictionary" key="10">
            <Item typeName="String" key="MapPath">D:\江西路网通 - 自动导出版本\GEOGRAPHIC\宜春\宜春城区和县城-cj_region.shp</Item>
            <Item typeName="String" key="MapName">宜春城区和县城-cj_region</Item>
            <Item typeName="String" key="ColumnName">具体场景</Item>
          </Item>
          <Item typeName="IDictionary" key="11">
            <Item typeName="String" key="MapPath">D:\江西路网通 - 自动导出版本\GEOGRAPHIC\鹰潭\鹰潭城区和县城-cj_region.shp</Item>
            <Item typeName="String" key="MapName">鹰潭城区和县城-cj_region</Item>
            <Item typeName="String" key="ColumnName">具体场景</Item>
          </Item>
          <Item typeName="IDictionary" key="12">
            <Item typeName="String" key="MapPath">D:\江西路网通 - 自动导出版本\GEOGRAPHIC\江西\县市界_region.shp</Item>
            <Item typeName="String" key="MapName">县市界_region</Item>
            <Item typeName="String" key="ColumnName">AREA</Item>
          </Item>
        </Item>
        <Item typeName="Boolean" key="IsDoByPeriod">False</Item>
        <Item typeName="Int32" key="DoByEveryDay_Hour">1</Item>
        <Item typeName="Single" key="DoByPeriod_Minutes">10</Item>
        <Item typeName="IDictionary" key="ExportSetting">
          <Item typeName="Boolean" key="AutoExportResult">True</Item>
          <Item typeName="Boolean" key="IsExportByDay">True</Item>
          <Item typeName="Boolean" key="IsExportByWeek">False</Item>
          <Item typeName="Boolean" key="IsExportByMonth">False</Item>
          <Item typeName="Boolean" key="IsExportByDiyPeriod">False</Item>
          <Item typeName="Boolean" key="IsExportByAll">False</Item>
          <Item typeName="Boolean" key="ExportNow">True</Item>
          <Item typeName="Boolean" key="ExportFirstDay">False</Item>
          <Item typeName="String" key="ExportResultFolderPath">E:\资料\网络体检</Item>
          <Item typeName="Boolean" key="IsExportResultToXls">True</Item>
          <Item typeName="Boolean" key="IsExportResultMapToWord">False</Item>
          <Item typeName="Boolean" key="IsSaveResultSumByDiffFolder">False</Item>
        </Item>
        <Item typeName="IDictionary" key="RoadSetting">
          <Item typeName="Int32" key="ValidDistance">50</Item>
        </Item>
        <Item typeName="IDictionary" key="AreaSetting">
          <Item typeName="Int32" key="AreaStatTimeMode">2</Item>
          <Item typeName="Int32" key="GatherRedius">200</Item>
          <Item typeName="Int32" key="StatTimeUnitCount">2</Item>
        </Item>
      </Item>
      <Item typeName="IDictionary" key="FuncSetting">
        <Item typeName="IDictionary" key="12001">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
          <Item typeName="Int32" key="MaxRxLev">-85</Item>
          <Item typeName="Int32" key="RoadDistance">100</Item>
          <Item typeName="Int32" key="SampleDistance">20</Item>
          <Item typeName="Int32" key="SampleCellDistance">300</Item>
          <Item typeName="Int32" key="SampleCellAngle">60</Item>
        </Item>
        <Item typeName="IDictionary" key="12002">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
          <Item typeName="Int32" key="FilterRxlev">-90</Item>
          <Item typeName="Int32" key="NearestCellCount">3</Item>
          <Item typeName="Single" key="DisFactor">1.6</Item>
          <Item typeName="Int32" key="MinSampleCount">0</Item>
          <Item typeName="Single" key="MinPercent">0</Item>
          <Item typeName="Int32" key="MinDistance">0</Item>
          <Item typeName="Int32" key="MaxDistance">1000000</Item>
        </Item>
        <Item typeName="IDictionary" key="12003">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
          <Item typeName="Boolean" key="LeakOutAsMainCell">True</Item>
          <Item typeName="Boolean" key="LeakOutAsNBCell">False</Item>
          <Item typeName="Int32" key="RxLevThreshold">-85</Item>
          <Item typeName="Int32" key="RxLevDValue">10</Item>
        </Item>
        <Item typeName="IDictionary" key="12004">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
          <Item typeName="Int32" key="RxLevMin">-85</Item>
          <Item typeName="Int32" key="RxLevMax">-10</Item>
          <Item typeName="Int32" key="SampleRxQuality">0</Item>
          <Item typeName="Int32" key="NoMainCellBlockRadius">70</Item>
          <Item typeName="Int32" key="SampleCountLimit">1</Item>
          <Item typeName="Int32" key="SampleCellDistance">800</Item>
          <Item typeName="Int32" key="CellCountLimit">4</Item>
          <Item typeName="Int32" key="RxLevDValue">6</Item>
          <Item typeName="Int32" key="HandOverTimeOffset">2</Item>
          <Item typeName="Single" key="RxqualValue">0</Item>
        </Item>
        <Item typeName="IDictionary" key="12024">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
          <Item typeName="Int32" key="RxQualThreshold">5</Item>
          <Item typeName="Int32" key="RxQualRadius">70</Item>
          <Item typeName="Int32" key="SampleCountLimit">1</Item>
        </Item>
        <Item typeName="IDictionary" key="12074">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
          <Item typeName="Int32" key="RxQualThreshold">5</Item>
          <Item typeName="Int32" key="RoadDistance">100</Item>
          <Item typeName="Int32" key="SampleDistance">20</Item>
        </Item>
        <Item typeName="IDictionary" key="12005">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
          <Item typeName="Single" key="RSRPMin">-80</Item>
          <Item typeName="Double" key="DisMin">100</Item>
          <Item typeName="Int32" key="AngleMin">50</Item>
          <Item typeName="Double" key="WrongPerMin">50</Item>
        </Item>
        <Item typeName="IDictionary" key="12060">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
        </Item>
        <Item typeName="IDictionary" key="12033">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
          <Item typeName="Int32" key="TimeThreshold">30</Item>
          <Item typeName="Int32" key="DistanceThreshold">150</Item>
        </Item>
        <Item typeName="IDictionary" key="12030">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
          <Item typeName="Int32" key="TimeLimit">10</Item>
          <Item typeName="Boolean" key="BLimitSpeed">False</Item>
          <Item typeName="Int32" key="SpeedLimitMin">5</Item>
          <Item typeName="Int32" key="SpeedLimitMax">10</Item>
        </Item>
        <Item typeName="IDictionary" key="12029">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
          <Item typeName="Int32" key="TimeLimit">15</Item>
          <Item typeName="Int32" key="DistanceLimit">150</Item>
          <Item typeName="Int32" key="HandoverCount">3</Item>
          <Item typeName="Boolean" key="IsBusiness">False</Item>
        </Item>
        <Item typeName="IDictionary" key="12009">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
          <Item typeName="Int32" key="RxQualMin">5</Item>
          <Item typeName="Int32" key="RxQualMax">7</Item>
          <Item typeName="Int32" key="NeighborCellCnt">3</Item>
        </Item>
        <Item typeName="IDictionary" key="12051">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
          <Item typeName="Int32" key="SpeedThreshold">20</Item>
          <Item typeName="Int32" key="DistanceThreshold">0</Item>
        </Item>
        <Item typeName="IDictionary" key="12055">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
          <Item typeName="Int32" key="MinRxLev">-85</Item>
          <Item typeName="Int32" key="MaxRxLev">-10</Item>
          <Item typeName="Int32" key="MinSpeed">0</Item>
          <Item typeName="Int32" key="MaxSpeed">500</Item>
        </Item>
        <Item typeName="IDictionary" key="12082">
          <Item typeName="String" key="RxlevMin">-95</Item>
          <Item typeName="String" key="BandType">GSM900</Item>
        </Item>
        <Item typeName="IDictionary" key="16001">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
          <Item typeName="Int32" key="RxLevThreshold">-95</Item>
          <Item typeName="Int32" key="DistanceLast">100</Item>
        </Item>
        <Item typeName="IDictionary" key="13001">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
          <Item typeName="Int32" key="RSCPThreshold">-95</Item>
          <Item typeName="Int32" key="PccpchC_IThreshold">-3</Item>
          <Item typeName="Int32" key="DpchC_IThreshold">-3</Item>
          <Item typeName="Int32" key="SampleDistance">20</Item>
          <Item typeName="Boolean" key="CheckPccpchC2I">False</Item>
          <Item typeName="Boolean" key="CheckDpchC2I">False</Item>
          <Item typeName="Int32" key="MinCovrDistance">100</Item>
          <Item typeName="Int32" key="SampleCellDistance">300</Item>
          <Item typeName="Int32" key="SampleCellAngle">60</Item>
          <Item typeName="Boolean" key="CheckNCellRscp">True</Item>
          <Item typeName="Int32" key="NcellRscpMax">-95</Item>
        </Item>
        <Item typeName="IDictionary" key="13003">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
          <Item typeName="Int32" key="FilterRxlev">-90</Item>
          <Item typeName="Int32" key="NearestCellCount">3</Item>
          <Item typeName="Single" key="DisFactor">1.6</Item>
          <Item typeName="Int32" key="MinSampleCount">0</Item>
          <Item typeName="Single" key="MinPercent">0</Item>
          <Item typeName="Int32" key="MinDistance">0</Item>
          <Item typeName="Int32" key="MaxDistance">1000000</Item>
        </Item>
        <Item typeName="IDictionary" key="13004">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
          <Item typeName="Boolean" key="LeakOutAsMainCell">True</Item>
          <Item typeName="Boolean" key="LeakOutAsNBCell">False</Item>
          <Item typeName="Int32" key="RxLevThreshold">-85</Item>
          <Item typeName="Int32" key="RxLevDValue">10</Item>
        </Item>
        <Item typeName="IDictionary" key="13005">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
          <Item typeName="Int32" key="FilterPCCPCH_RSCPMin">-85</Item>
          <Item typeName="Int32" key="FilterPCCPCH_RSCPMax">-10</Item>
          <Item typeName="Int32" key="PilotFrequencyPolluteBlockRadius">50</Item>
          <Item typeName="Int32" key="SampleCountLimit">10</Item>
          <Item typeName="Int32" key="CellCountThreshold">4</Item>
          <Item typeName="Int32" key="RxLevDValueThreshold">6</Item>
        </Item>
        <Item typeName="IDictionary" key="13029">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
          <Item typeName="Single" key="RSCPMin">-120</Item>
          <Item typeName="Double" key="DisMin">100</Item>
          <Item typeName="Int32" key="AngleMin">50</Item>
          <Item typeName="Double" key="WrongPerMin">50</Item>
        </Item>
        <Item typeName="IDictionary" key="13053">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
        </Item>
        <Item typeName="IDictionary" key="13016">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
          <Item typeName="Int32" key="TimeLimit">10</Item>
          <Item typeName="Boolean" key="BLimitSpeed">False</Item>
          <Item typeName="Int32" key="SpeedLimitMin">5</Item>
          <Item typeName="Int32" key="SpeedLimitMax">10</Item>
        </Item>
        <Item typeName="IDictionary" key="13015">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
          <Item typeName="Int32" key="TimeLimit">15</Item>
          <Item typeName="Int32" key="DistanceLimit">150</Item>
          <Item typeName="Int32" key="HandoverCount">3</Item>
          <Item typeName="Boolean" key="IsBusiness">False</Item>
        </Item>
        <Item typeName="IDictionary" key="13048">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
          <Item typeName="Int32" key="TimeThreshold">30</Item>
          <Item typeName="Int32" key="DistanceThreshold">150</Item>
          <Item typeName="Int32" key="SampleDistanceThreshold">20</Item>
        </Item>
        <Item typeName="IDictionary" key="13026">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
          <Item typeName="Int32" key="MinBler">6</Item>
          <Item typeName="Int32" key="RoadDistance">100</Item>
          <Item typeName="Int32" key="SampleDistance">20</Item>
        </Item>
        <Item typeName="IDictionary" key="13027">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
          <Item typeName="Double" key="BlerMin">6</Item>
          <Item typeName="Double" key="BlerMax">100</Item>
          <Item typeName="Double" key="PCCPCHRSCPMin">-140</Item>
          <Item typeName="Double" key="PCCPCHRSCPMax">-10</Item>
          <Item typeName="Double" key="PCCPCHC2IMin">-30</Item>
          <Item typeName="Double" key="PCCPCHC2IMax">30</Item>
          <Item typeName="Double" key="DPCHC2IMin">-30</Item>
          <Item typeName="Double" key="DPCHC2IMax">30</Item>
        </Item>
        <Item typeName="IDictionary" key="13020">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
          <Item typeName="Int32" key="SpeedThreshold">20</Item>
          <Item typeName="Int32" key="DistanceThreshold">0</Item>
        </Item>
        <Item typeName="IDictionary" key="13023">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
          <Item typeName="Int32" key="MinRxLev">-85</Item>
          <Item typeName="Int32" key="MaxRxLev">-10</Item>
          <Item typeName="Int32" key="MinSpeed">0</Item>
          <Item typeName="Int32" key="MaxSpeed">500</Item>
          <Item typeName="Int32" key="MinPCCPCH_C2I">3</Item>
          <Item typeName="Int32" key="MaxPCCPCH_C2I">25</Item>
          <Item typeName="Int32" key="MinDPCH_C2I">3</Item>
          <Item typeName="Int32" key="MaxDPCH_C2I">25</Item>
        </Item>
        <Item typeName="IDictionary" key="22009">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
          <Item typeName="Single" key="MaxRSRP">-105</Item>
          <Item typeName="Double" key="MinCoverRoadDistance">100</Item>
          <Item typeName="Double" key="MaxTPDistance">50</Item>
          <Item typeName="Boolean" key="CheckAndOr">True</Item>
          <Item typeName="Boolean" key="CheckSINR">True</Item>
          <Item typeName="Single" key="MaxSINR">5</Item>
          <Item typeName="Boolean" key="CheckNbMaxRSRP">False</Item>
          <Item typeName="String" key="compareSymble">&lt;</Item>
          <Item typeName="Single" key="NbMaxRSRP">-100</Item>
          <Item typeName="Int32" key="MaxSampleCellDistance">300</Item>
          <Item typeName="Int32" key="MaxSampleCellAngle">60</Item>
          <Item typeName="Double" key="MinWeakPointPercent">100</Item>
        </Item>
        <Item typeName="IDictionary" key="22010">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
          <Item typeName="Int32" key="FilterRxlev">-90</Item>
          <Item typeName="Int32" key="NearestCellCount">3</Item>
          <Item typeName="Single" key="DisFactor">1.6</Item>
          <Item typeName="Boolean" key="IsChkSampleCount">False</Item>
          <Item typeName="Int32" key="MinSampleCount">100</Item>
          <Item typeName="Boolean" key="IsChkOverPercent">False</Item>
          <Item typeName="Single" key="MinPercent">1</Item>
          <Item typeName="Boolean" key="IsChkDistance">False</Item>
          <Item typeName="Int32" key="MinDistance">0</Item>
          <Item typeName="Int32" key="MaxDistance">3000</Item>
          <Item typeName="Boolean" key="IsChkBand">False</Item>
          <Item typeName="String" key="BandType">D</Item>
        </Item>
        <Item typeName="IDictionary" key="22011">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
          <Item typeName="Int32" key="FilterPCCPCH_RSCPMin">-85</Item>
          <Item typeName="Int32" key="FilterPCCPCH_RSCPMax">-10</Item>
          <Item typeName="Int32" key="PilotFrequencyPolluteBlockRadius">50</Item>
          <Item typeName="Int32" key="SampleCountLimit">10</Item>
          <Item typeName="Int32" key="CellCountThreshold">4</Item>
          <Item typeName="Int32" key="RxLevDValueThreshold">6</Item>
        </Item>
        <Item typeName="IDictionary" key="22018">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
          <Item typeName="Boolean" key="IsAppSpeed">True</Item>
          <Item typeName="Double" key="EmailRateMax">2</Item>
          <Item typeName="Double" key="FTPDLRateMax">10</Item>
          <Item typeName="Double" key="FTPULRateMax">1</Item>
          <Item typeName="Double" key="HTTPRateMax">2</Item>
          <Item typeName="Double" key="PdcpRateMax">10</Item>
          <Item typeName="Double" key="DistanceMin">50</Item>
          <Item typeName="Double" key="TestPointDistance">50</Item>
          <Item typeName="Double" key="EmailRateMin">0</Item>
          <Item typeName="Double" key="FTPDLRateMin">0</Item>
          <Item typeName="Double" key="FTPULRateMin">0</Item>
          <Item typeName="Double" key="HTTPRateMin">0</Item>
          <Item typeName="Double" key="PdcpRateMin">0</Item>
          <Item typeName="Boolean" key="CheckHo">False</Item>
          <Item typeName="Int32" key="HoSecond">0</Item>
          <Item typeName="Boolean" key="CheckRsrp">False</Item>
          <Item typeName="Single" key="RsrpMax">0</Item>
          <Item typeName="Single" key="RsrpMin">0</Item>
          <Item typeName="Boolean" key="CheckSinr">False</Item>
          <Item typeName="Single" key="SinrMax">0</Item>
          <Item typeName="Single" key="SinrMin">0</Item>
          <Item typeName="Double" key="LowPercent">80</Item>
          <Item typeName="Boolean" key="CheckSynthesisLowSpeed">False</Item>
          <Item typeName="Boolean" key="CheckLTELowSpeed">True</Item>
          <Item typeName="Boolean" key="CheckLowSpeed_TDOrW">True</Item>
          <Item typeName="Boolean" key="CheckGSMLowSpeed">True</Item>
        </Item>
        <Item typeName="IDictionary" key="22012">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
          <Item typeName="Single" key="RSRPMin">-95</Item>
          <Item typeName="Double" key="DisMin">100</Item>
          <Item typeName="Int32" key="AngleMin">90</Item>
          <Item typeName="Double" key="WrongPerMin">50</Item>
        </Item>
        <Item typeName="IDictionary" key="23020">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
          <Item typeName="Boolean" key="BDtNbInfo">False</Item>
          <Item typeName="Boolean" key="BSecAna">False</Item>
          <Item typeName="Boolean" key="BSecDataExport">False</Item>
          <Item typeName="Boolean" key="BSampleShow">True</Item>
        </Item>
        <Item typeName="IDictionary" key="22021">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
          <Item typeName="Int32" key="RsrpThreshold">-100</Item>
          <Item typeName="Int32" key="SampleNum">10</Item>
          <Item typeName="Int32" key="DistanceMax">3000</Item>
          <Item typeName="Int32" key="DistanceMin">1000</Item>
          <Item typeName="Boolean" key="NBCellChecked">True</Item>
        </Item>
        <Item typeName="IDictionary" key="22042">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
          <Item typeName="Boolean" key="LeakOutAsMainCell">True</Item>
          <Item typeName="Boolean" key="LeakOutAsNCell">False</Item>
          <Item typeName="Boolean" key="IsGetRoadDesc">True</Item>
          <Item typeName="Boolean" key="IsGetGSMCell">False</Item>
          <Item typeName="Int32" key="MinNCellRxlev">-85</Item>
          <Item typeName="Int32" key="DiffRxlev">10</Item>
        </Item>
        <Item typeName="IDictionary" key="22013">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
          <Item typeName="Single" key="MaxSINR">0</Item>
          <Item typeName="Boolean" key="CheckRSRP">False</Item>
          <Item typeName="Single" key="MinRSRP">-110</Item>
          <Item typeName="Double" key="WeakSINRPercent">80</Item>
          <Item typeName="Double" key="Max2TPDistance">50</Item>
          <Item typeName="Boolean" key="CheckMinDistance">True</Item>
          <Item typeName="Double" key="MinCoverRoadDistance">100</Item>
          <Item typeName="Boolean" key="CheckMinDuration">False</Item>
          <Item typeName="Double" key="MinDuration">10</Item>
        </Item>
        <Item typeName="IDictionary" key="22014">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
          <Item typeName="Single" key="MaxSinr">0</Item>
          <Item typeName="Single" key="MinRsrp">-85</Item>
          <Item typeName="Double" key="Percentage">80</Item>
          <Item typeName="Boolean" key="Check2TPDistance">True</Item>
          <Item typeName="Double" key="Max2TPDistance">50</Item>
          <Item typeName="Boolean" key="CheckDistance">True</Item>
          <Item typeName="Double" key="MinStayDistance">100</Item>
          <Item typeName="Boolean" key="CheckTime">False</Item>
          <Item typeName="Double" key="MinStaySecond">10</Item>
        </Item>
        <Item typeName="IDictionary" key="22025">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
          <Item typeName="Single" key="MaxSINR">0</Item>
          <Item typeName="IDictionary" key="ReasonsParam">
            <Item typeName="IDictionary" key="弱覆盖">
              <Item typeName="Boolean" key="Enable">True</Item>
              <Item typeName="Single" key="MaxRSRP">-105</Item>
            </Item>
            <Item typeName="IDictionary" key="重叠覆盖">
              <Item typeName="Boolean" key="Enable">True</Item>
              <Item typeName="Int32" key="MultiNumMin">5</Item>
              <Item typeName="Single" key="RSRPDiffMax">6</Item>
              <Item typeName="Single" key="RSRSMin">-105</Item>
            </Item>
            <Item typeName="IDictionary" key="模3干扰">
              <Item typeName="Boolean" key="Enable">True</Item>
              <Item typeName="Single" key="RSRPDiffMax">6</Item>
            </Item>
            <Item typeName="IDictionary" key="切换频繁">
              <Item typeName="Boolean" key="Enable">True</Item>
            </Item>
            <Item typeName="IDictionary" key="切换不合理">
              <Item typeName="Boolean" key="Enable">True</Item>
            </Item>
            <Item typeName="IDictionary" key="切换不及时">
              <Item typeName="Boolean" key="Enable">True</Item>
            </Item>
            <Item typeName="IDictionary" key="过覆盖">
              <Item typeName="Boolean" key="Enable">True</Item>
              <Item typeName="Double" key="OverRsrpMin">-90</Item>
              <Item typeName="Double" key="CoverFactor">1.6</Item>
            </Item>
            <Item typeName="IDictionary" key="背向覆盖">
              <Item typeName="Boolean" key="Enable">True</Item>
              <Item typeName="Double" key="DirectionDif">60</Item>
              <Item typeName="Double" key="Cell2TpDis">100</Item>
            </Item>
            <Item typeName="IDictionary" key="室分泄露">
              <Item typeName="Boolean" key="Enable">True</Item>
            </Item>
            <Item typeName="IDictionary" key="质量毛刺">
              <Item typeName="Boolean" key="Enable">True</Item>
            </Item>
            <Item typeName="IDictionary" key="未知原因">
              <Item typeName="Boolean" key="Enable">True</Item>
            </Item>
          </Item>
          <Item typeName="Int32" key="timeLimit">15</Item>
          <Item typeName="Int32" key="distanceLimit">100</Item>
          <Item typeName="Int32" key="handoverCount">5</Item>
          <Item typeName="Int32" key="timePersist">3</Item>
          <Item typeName="Int32" key="timeBeforeWeakSinr">6</Item>
          <Item typeName="Int32" key="stayTime">3</Item>
          <Item typeName="Single" key="rsrpDiffer">3</Item>
          <Item typeName="Int32" key="beforeTime">5</Item>
          <Item typeName="Int32" key="suddenWeakTime">2</Item>
          <Item typeName="Single" key="suddenWeakAvg">8</Item>
        </Item>
        <Item typeName="IDictionary" key="22027">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
          <Item typeName="Double" key="RoadLength">100</Item>
          <Item typeName="Double" key="SampleInterval">50</Item>
          <Item typeName="Double" key="MaxRxlev">-95</Item>
          <Item typeName="Double" key="InterfereRate">0.1</Item>
          <Item typeName="Double" key="RxlevDiff">6</Item>
          <Item typeName="Double" key="Angle">180</Item>
          <Item typeName="Double" key="Distance">1000</Item>
          <Item typeName="IList" key="Sids">
            <Item typeName="Int32">0</Item>
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
          </Item>
          <Item typeName="Int32" key="ModX">3</Item>
        </Item>
        <Item typeName="IDictionary" key="22015">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
          <Item typeName="Single" key="SvrPccpchMax">-100</Item>
          <Item typeName="Single" key="NCellPccpch">-90</Item>
          <Item typeName="Single" key="PccpchDiffMin">10</Item>
          <Item typeName="Int32" key="StaySecondsMin">6</Item>
          <Item typeName="Boolean" key="CheckType">True</Item>
        </Item>
        <Item typeName="IDictionary" key="22017">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
        </Item>
        <Item typeName="IDictionary" key="22020">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
          <Item typeName="Int32" key="TimeLimit">15</Item>
          <Item typeName="Int32" key="DistanceLimit">100</Item>
          <Item typeName="Int32" key="HandoverCount">6</Item>
          <Item typeName="Boolean" key="IsBusiness">False</Item>
        </Item>
        <Item typeName="IDictionary" key="22058">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
          <Item typeName="Int32" key="TimeLimit">15</Item>
          <Item typeName="Boolean" key="IsLimitSpeed">True</Item>
          <Item typeName="Int32" key="SpeedLimitMin">5</Item>
          <Item typeName="Int32" key="SpeedLimitMax">100</Item>
        </Item>
        <Item typeName="IDictionary" key="22064">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
          <Item typeName="Int32" key="TimeLimit">15</Item>
          <Item typeName="Int32" key="DistanceLimit">100</Item>
          <Item typeName="Int32" key="HandoverCount">3</Item>
          <Item typeName="Boolean" key="IsBusiness">False</Item>
        </Item>
        <Item typeName="IDictionary" key="22094">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
          <Item typeName="Boolean" key="IsChkWeakSinr">True</Item>
          <Item typeName="Single" key="SinrWeak1Gate">5</Item>
          <Item typeName="Single" key="RsrpBefore1Gate">-105</Item>
          <Item typeName="Single" key="SinrBefore1Gate">5</Item>
          <Item typeName="Boolean" key="IsChkWeakRsrp">True</Item>
          <Item typeName="Single" key="RsrpWeak2Gate">-105</Item>
          <Item typeName="Single" key="RsrpBefore2Gate">-105</Item>
          <Item typeName="Single" key="SinrReduce2Gate">10</Item>
          <Item typeName="Boolean" key="IsChkWeakSinrRsrp">True</Item>
          <Item typeName="Single" key="RsrpWeak3Gate">-105</Item>
          <Item typeName="Single" key="SinrWeak3Gate">5</Item>
          <Item typeName="Single" key="SinrReduce3Gate">10</Item>
        </Item>
        <Item typeName="IDictionary" key="22095">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
          <Item typeName="Boolean" key="IsChkGoodRsrp">True</Item>
          <Item typeName="Single" key="RsrpWeakGate">-105</Item>
          <Item typeName="Single" key="SinrBefore1Gate">5</Item>
          <Item typeName="Single" key="Rsrp1Add1Min1">0</Item>
          <Item typeName="Single" key="Rsrp1Add1Max1">5</Item>
          <Item typeName="Single" key="Rsrp1Add1Max2">10</Item>
          <Item typeName="Single" key="Rsrp1Add2Max1">5</Item>
          <Item typeName="Single" key="Rsrp1Add2Max2">10</Item>
          <Item typeName="Boolean" key="IsChkWeakRsrp">True</Item>
          <Item typeName="Single" key="SinrBefore2Gate">5</Item>
          <Item typeName="Single" key="RsrpWeakGate2">-105</Item>
          <Item typeName="Single" key="Rsrp2Add1Min1">0</Item>
          <Item typeName="Single" key="Rsrp2Add1Max1">5</Item>
          <Item typeName="Single" key="Rsrp2Add2Max1">0</Item>
          <Item typeName="Single" key="Rsrp2Add2Max2">5</Item>
          <Item typeName="Single" key="Rsrp2Add2Max3">10</Item>
        </Item>
        <Item typeName="IDictionary" key="22052">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
        </Item>
        <Item typeName="IDictionary" key="22024">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
          <Item typeName="Int32" key="SampleCount">5</Item>
          <Item typeName="Int32" key="RSRP">-110</Item>
          <Item typeName="Int32" key="Distance">1000</Item>
        </Item>
        <Item typeName="IDictionary" key="22043">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
        </Item>
        <Item typeName="IDictionary" key="22049">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
          <Item typeName="IDictionary" key="SceneCauseCond">
            <Item typeName="IDictionary" key="弱覆盖">
              <Item typeName="Boolean" key="BChecked">True</Item>
              <Item typeName="Single" key="MainRxlevMax">-100</Item>
              <Item typeName="Single" key="NBRxlevMax">-105</Item>
            </Item>
            <Item typeName="IDictionary" key="过覆盖">
              <Item typeName="Boolean" key="BChecked">True</Item>
              <Item typeName="Single" key="MainRxlevMin">-90</Item>
              <Item typeName="Int32" key="BtsNum">3</Item>
              <Item typeName="Single" key="Radius">1.6</Item>
              <Item typeName="Double" key="DistanceMin">1000</Item>
              <Item typeName="Double" key="DistanceMax">3000</Item>
            </Item>
            <Item typeName="IDictionary" key="覆盖不符">
              <Item typeName="Boolean" key="BChecked">True</Item>
              <Item typeName="Double" key="DistanceMin">200</Item>
              <Item typeName="Int32" key="AngleMin">60</Item>
              <Item typeName="Single" key="RxlevMin">-110</Item>
              <Item typeName="Single" key="RxlevMax">-90</Item>
            </Item>
            <Item typeName="IDictionary" key="质差">
              <Item typeName="Boolean" key="BChecked">True</Item>
              <Item typeName="Single" key="SinrMax">3</Item>
              <Item typeName="Int32" key="RxqualMin">4</Item>
            </Item>
            <Item typeName="IDictionary" key="强信号弱质量（干扰&amp;硬件故障）">
              <Item typeName="Boolean" key="BChecked">True</Item>
              <Item typeName="Single" key="RxlevMin">-85</Item>
              <Item typeName="Single" key="SinrMax">5</Item>
              <Item typeName="Int32" key="RxqualMin">4</Item>
            </Item>
            <Item typeName="IDictionary" key="重叠覆盖">
              <Item typeName="Boolean" key="BChecked">True</Item>
              <Item typeName="Single" key="RxlevMin">-100</Item>
              <Item typeName="Int32" key="RxlevDiff">6</Item>
              <Item typeName="Int32" key="CovNum">3</Item>
            </Item>
            <Item typeName="IDictionary" key="模三干扰">
              <Item typeName="Boolean" key="BChecked">True</Item>
              <Item typeName="Single" key="MainRxlev">-95</Item>
              <Item typeName="Int32" key="RxlevDiff">6</Item>
              <Item typeName="Int32" key="Percent">60</Item>
            </Item>
            <Item typeName="IDictionary" key="频繁切换">
              <Item typeName="Boolean" key="BChecked">True</Item>
              <Item typeName="Int32" key="HONum">3</Item>
            </Item>
            <Item typeName="IDictionary" key="切换不及时">
              <Item typeName="Boolean" key="BChecked">True</Item>
              <Item typeName="Single" key="MainRxlev">-90</Item>
              <Item typeName="Single" key="NBRxlev">-85</Item>
              <Item typeName="Int32" key="Percent">60</Item>
            </Item>
            <Item typeName="IDictionary" key="切换不合理">
              <Item typeName="Boolean" key="BChecked">True</Item>
              <Item typeName="Int32" key="RxlevDiff">5</Item>
            </Item>
            <Item typeName="IDictionary" key="场强快衰">
              <Item typeName="Boolean" key="BChecked">True</Item>
              <Item typeName="Single" key="RxlevMin">-80</Item>
              <Item typeName="Int32" key="ITimeSpan">3</Item>
              <Item typeName="Single" key="RxlevMax">-95</Item>
            </Item>
            <Item typeName="IDictionary" key="室分外泄">
              <Item typeName="Boolean" key="BChecked">True</Item>
            </Item>
          </Item>
        </Item>
        <Item typeName="IDictionary" key="19038">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
          <Item typeName="IDictionary" key="FuncCond">
            <Item typeName="Double" key="NearDistanceMin">100</Item>
            <Item typeName="Double" key="DiffBandDistanceMin">50</Item>
            <Item typeName="Double" key="AltitudeMax">50</Item>
            <Item typeName="Double" key="AvgSiteDistanceMax">700</Item>
            <Item typeName="Int32" key="AvgSitesDistanceAngle">120</Item>
          </Item>
        </Item>
        <Item typeName="IDictionary" key="22070">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
          <Item typeName="IDictionary" key="OutDoorCallCheck">
            <Item typeName="Boolean" key="IsCheckHandOver">True</Item>
            <Item typeName="Boolean" key="IsCheckCsfb">False</Item>
            <Item typeName="Boolean" key="IsCheckVolteVoiceAllCall">False</Item>
            <Item typeName="Boolean" key="IsCheckVolteVoiceMoCall">False</Item>
            <Item typeName="Boolean" key="IsCheckVolteVideoAllCall">False</Item>
            <Item typeName="Boolean" key="IsCheckVolteVideoMoCall">False</Item>
            <Item typeName="Boolean" key="IsCheckSRVCCCall">False</Item>
            <Item typeName="Boolean" key="IsCheckLeakOutRatio">False</Item>
            <Item typeName="Boolean" key="IsCheckOutDoorOtherSet">False</Item>
            <Item typeName="Boolean" key="IsCheckInAndOutSrc">False</Item>
          </Item>
          <Item typeName="IDictionary" key="InDoorCallCheck">
            <Item typeName="Boolean" key="IsCheckHandOver">True</Item>
            <Item typeName="Boolean" key="IsCheckCsfb">False</Item>
            <Item typeName="Boolean" key="IsCheckVolteVoiceAllCall">False</Item>
            <Item typeName="Boolean" key="IsCheckVolteVoiceMoCall">False</Item>
            <Item typeName="Boolean" key="IsCheckVolteVideoAllCall">False</Item>
            <Item typeName="Boolean" key="IsCheckVolteVideoMoCall">False</Item>
            <Item typeName="Boolean" key="IsCheckSRVCCCall">False</Item>
            <Item typeName="Boolean" key="IsCheckLeakOutRatio">False</Item>
            <Item typeName="Boolean" key="IsCheckOutDoorOtherSet">False</Item>
            <Item typeName="Boolean" key="IsCheckInAndOutSrc">False</Item>
          </Item>
          <Item typeName="Int32" key="DiyStatsRecentDays">10</Item>
          <Item typeName="String" key="ExcelPath">E:\单站验收\湖北\工参\单站验收小区工参_20180819.xls</Item>
          <Item typeName="Boolean" key="IsAutoWorkParamFilePath">False</Item>
          <Item typeName="Int32" key="AutoWorkParamRecentDays">2</Item>
          <Item typeName="Int32" key="FileNameEnodType">0</Item>
          <Item typeName="String" key="FilePath">E:\单站验收\湖北</Item>
          <Item typeName="String" key="FileCountAutoULFolder" />
          <Item typeName="Boolean" key="FileCountAutoIsCheck">False</Item>
          <Item typeName="Int32" key="AutoAnaDurationMinutes">10</Item>
        </Item>
        <Item typeName="IDictionary" key="22104">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
          <Item typeName="IDictionary" key="OutDoorCallCheck">
            <Item typeName="Boolean" key="IsCheckHandOver">True</Item>
            <Item typeName="Boolean" key="IsCheckCsfb">False</Item>
            <Item typeName="Boolean" key="IsCheckVolteVoiceAllCall">False</Item>
            <Item typeName="Boolean" key="IsCheckVolteVoiceMoCall">False</Item>
            <Item typeName="Boolean" key="IsCheckVolteVideoAllCall">False</Item>
            <Item typeName="Boolean" key="IsCheckVolteVideoMoCall">False</Item>
            <Item typeName="Boolean" key="IsCheckSRVCCCall">False</Item>
            <Item typeName="Boolean" key="IsCheckLeakOutRatio">False</Item>
            <Item typeName="Boolean" key="IsCheckOutDoorOtherSet">False</Item>
            <Item typeName="Boolean" key="IsCheckInAndOutSrc">False</Item>
          </Item>
          <Item typeName="IDictionary" key="InDoorCallCheck">
            <Item typeName="Boolean" key="IsCheckHandOver">True</Item>
            <Item typeName="Boolean" key="IsCheckCsfb">False</Item>
            <Item typeName="Boolean" key="IsCheckVolteVoiceAllCall">False</Item>
            <Item typeName="Boolean" key="IsCheckVolteVoiceMoCall">False</Item>
            <Item typeName="Boolean" key="IsCheckVolteVideoAllCall">False</Item>
            <Item typeName="Boolean" key="IsCheckVolteVideoMoCall">False</Item>
            <Item typeName="Boolean" key="IsCheckSRVCCCall">False</Item>
            <Item typeName="Boolean" key="IsCheckLeakOutRatio">False</Item>
            <Item typeName="Boolean" key="IsCheckOutDoorOtherSet">False</Item>
            <Item typeName="Boolean" key="IsCheckInAndOutSrc">False</Item>
          </Item>
          <Item typeName="Int32" key="DiyStatsRecentDays">2</Item>
          <Item typeName="String" key="ExcelPath" />
          <Item typeName="Boolean" key="IsAutoWorkParamFilePath">False</Item>
          <Item typeName="Int32" key="AutoWorkParamRecentDays">2</Item>
          <Item typeName="Int32" key="FileNameEnodType">0</Item>
          <Item typeName="String" key="FilePath">C:\Users\<USER>\Desktop</Item>
          <Item typeName="String" key="FileCountAutoULFolder" />
          <Item typeName="Boolean" key="FileCountAutoIsCheck">False</Item>
          <Item typeName="Int32" key="AutoAnaDurationMinutes">10</Item>
        </Item>
        <Item typeName="IDictionary" key="22114">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
          <Item typeName="IDictionary" key="ExportReportSet">
            <Item typeName="String" key="ReportSavePath">E:\单站验收\新疆</Item>
            <Item typeName="Boolean" key="IsCheckWebservice">True</Item>
            <Item typeName="String" key="ResultWebservicePath">http://10.235.110.139:9081/services/UploadFileWebServiceIntf?wsdl</Item>
            <Item typeName="Int32" key="WorkParamBeforeDays_Begin">360</Item>
            <Item typeName="Int32" key="WorkParamBeforeDays_End">0</Item>
            <Item typeName="String" key="CellParamFolderPath">E:\单站验收\新疆\单验工参</Item>
            <Item typeName="String" key="AntennaInfoFolderPath">E:\单站验收\新疆\天资数据</Item>
            <Item typeName="String" key="OutdoorBtsPicFolderPath">E:\单站验收\新疆\站点图片</Item>
            <Item typeName="String" key="IndoorCoverPicFolderPath">E:\单站验收\新疆\站点图片</Item>
            <Item typeName="Boolean" key="IsAnaFusionDatas">True</Item>
            <Item typeName="Boolean" key="IsAnaFusionByRecentDays">True</Item>
            <Item typeName="Int32" key="FusionRecentDays">7</Item>
            <Item typeName="String" key="FusionByPeriodBeginDate">2017/11/16</Item>
            <Item typeName="String" key="FusionByPeriodEndDate">2017/11/16</Item>
            <Item typeName="Int32" key="FusionAccordLastDays">3</Item>
            <Item typeName="String" key="FusionCheckDistictNames">武汉</Item>
          </Item>
        </Item>
        <Item typeName="IDictionary" key="22088">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
        </Item>
        <Item typeName="IDictionary" key="18040">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
          <Item typeName="IDictionary" key="ExportReportSet">
            <Item typeName="String" key="ExcelPath" />
            <Item typeName="String" key="FilePath" />
            <Item typeName="Int32" key="AcceptType">0</Item>
            <Item typeName="String" key="FileNameFilters" />
            <Item typeName="Boolean" key="IsAnalysedNearest">True</Item>
            <Item typeName="Int32" key="NearestDay">30</Item>
          </Item>
        </Item>
        <Item typeName="IDictionary" key="11002">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
          <Item typeName="IDictionary" key="ExportReportSet">
            <Item typeName="String" key="ExcelPath" />
            <Item typeName="String" key="FilePath" />
            <Item typeName="Int32" key="AcceptType">0</Item>
            <Item typeName="String" key="FileNameFilters" />
            <Item typeName="Boolean" key="IsAnalysedNearest">False</Item>
            <Item typeName="Int32" key="NearestDay">0</Item>
            <Item typeName="String" key="DataSource" />
            <Item typeName="String" key="InitialCatalog" />
            <Item typeName="String" key="User" />
            <Item typeName="String" key="PW" />
          </Item>
        </Item>
        <Item typeName="IDictionary" key="22118">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
          <Item typeName="IDictionary" key="ExportReportSet">
            <Item typeName="String" key="ExcelPath" />
            <Item typeName="String" key="FilePath" />
            <Item typeName="Int32" key="AcceptType">0</Item>
            <Item typeName="String" key="FileNameFilters" />
            <Item typeName="Boolean" key="IsAnalysedNearest">False</Item>
            <Item typeName="Int32" key="NearestDay">30</Item>
          </Item>
        </Item>
        <Item typeName="IDictionary" key="11053">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
          <Item typeName="Boolean" key="IsCheckLteCell">True</Item>
          <Item typeName="Boolean" key="IsCheckTdCell">False</Item>
          <Item typeName="Boolean" key="IsCheckGsmCell">False</Item>
          <Item typeName="Boolean" key="IsCheckUnKownCell">False</Item>
          <Item typeName="Boolean" key="IsCheckRecentDay">False</Item>
          <Item typeName="Int32" key="RecentDays">2</Item>
          <Item typeName="Boolean" key="IsCheckCellBasicInfo">False</Item>
          <Item typeName="String" key="SelectedTmpName">24_联通_FDD LTE综合统计报表</Item>
          <Item typeName="String" key="ExcelSavePath" />
          <Item typeName="Int32" key="ResultSaveTypeIndex">0</Item>
        </Item>
        <Item typeName="IDictionary" key="22115">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
          <Item typeName="IDictionary" key="ExportReportSet">
            <Item typeName="String" key="CellParamFolderPath">E:\单站验收\贵州\工参</Item>
            <Item typeName="String" key="FtpServerPath">192.168.2.61</Item>
            <Item typeName="Boolean" key="IsAnaSpecifyBts">False</Item>
            <Item typeName="String" key="SpecifyBtsEnodeBids" />
            <Item typeName="Int32" key="WorkParamBeforeDays_Begin">100</Item>
            <Item typeName="Int32" key="WorkParamBeforeDays_End">0</Item>
            <Item typeName="String" key="FtpUserName">g/etzgUKIJw=</Item>
            <Item typeName="String" key="FtpUserPwd">Uteyy/YHQts=</Item>
          </Item>
        </Item>
        <Item typeName="IDictionary" key="22117">
          <Item typeName="Boolean" key="BackgroundStat">True</Item>
          <Item typeName="IDictionary" key="ExportReportSet">
            <Item typeName="String" key="ReportSavePath">E:\单站验收\青海</Item>
            <Item typeName="Boolean" key="IsCheckWebservice">False</Item>
            <Item typeName="String" key="ResultWebservicePath" />
            <Item typeName="Int32" key="WorkParamBeforeDays_Begin">25</Item>
            <Item typeName="Int32" key="WorkParamBeforeDays_End">0</Item>
            <Item typeName="String" key="CellParamFolderPath">E:\单站验收\青海\单验工参</Item>
            <Item typeName="String" key="AntennaInfoFolderPath" />
            <Item typeName="String" key="OutdoorBtsPicFolderPath">E:\单站验收\青海\图片</Item>
            <Item typeName="String" key="IndoorCoverPicFolderPath">E:\单站验收\青海\图片</Item>
            <Item typeName="Boolean" key="IsAnaFusionDatas">True</Item>
            <Item typeName="Boolean" key="IsAnaFusionByRecentDays">True</Item>
            <Item typeName="Int32" key="FusionRecentDays">7</Item>
            <Item typeName="String" key="FusionByPeriodBeginDate">2017/11/16</Item>
            <Item typeName="String" key="FusionByPeriodEndDate">2017/11/16</Item>
            <Item typeName="Int32" key="FusionAccordLastDays">3</Item>
            <Item typeName="String" key="FusionCheckDistictNames">武汉</Item>
          </Item>
          <Item typeName="IDictionary" key="Params_FTP">
            <Item typeName="String" key="FtpUserName">75bt54TYBJ8=</Item>
            <Item typeName="String" key="FtpUserPwd">75bt54TYBJ8=</Item>
            <Item typeName="String" key="FtpServerPath">192.168.2.61:21//JZZDDY</Item>
          </Item>
        </Item>
        <Item typeName="IDictionary" key="15001">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
          <Item typeName="Int32" key="RxLevThreshold">-95</Item>
          <Item typeName="Int32" key="DistanceLast">100</Item>
        </Item>
        <Item typeName="IDictionary" key="15031">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
          <Item typeName="Int32" key="RxLevMax">-85</Item>
          <Item typeName="Int32" key="CellCoverDistance">300</Item>
          <Item typeName="Int32" key="WeakGridCount">2</Item>
          <Item typeName="Boolean" key="JudgeByScale">False</Item>
          <Item typeName="Int32" key="JudgeScale">50</Item>
        </Item>
        <Item typeName="IDictionary" key="15003">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
          <Item typeName="Int32" key="RSCPThreshold">-85</Item>
          <Item typeName="Int32" key="RSCPDValue">10</Item>
          <Item typeName="Int32" key="NBSampleDistanceLimit">50</Item>
          <Item typeName="Int32" key="ValidDistance">50</Item>
        </Item>
        <Item typeName="IDictionary" key="15005">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
          <Item typeName="Int32" key="RxLevThreshold">-80</Item>
          <Item typeName="Int32" key="DistanceThreshold">100</Item>
          <Item typeName="Int32" key="BadSampleRate">5</Item>
        </Item>
        <Item typeName="IDictionary" key="15002">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
          <Item typeName="Int32" key="FilterRxlev">-140</Item>
          <Item typeName="Int32" key="NearestCellCount">3</Item>
          <Item typeName="Single" key="DisFactor">1.6</Item>
          <Item typeName="Int32" key="SameFreq">9</Item>
          <Item typeName="Int32" key="AdjFreq">-9</Item>
        </Item>
        <Item typeName="IDictionary" key="15042">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
        </Item>
        <Item typeName="IDictionary" key="15007">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
          <Item typeName="Int32" key="RxLevThreshold">-85</Item>
          <Item typeName="Int32" key="DistanceThreshold">300</Item>
        </Item>
        <Item typeName="IDictionary" key="15008">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
          <Item typeName="Int32" key="SetRxlev">-80</Item>
          <Item typeName="Int32" key="CoverDistance">200</Item>
          <Item typeName="Int32" key="CellDistance">2000</Item>
        </Item>
        <Item typeName="IDictionary" key="15021">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
          <Item typeName="Int32" key="SetRxlev">-80</Item>
          <Item typeName="Int32" key="SetRxlevDiff">12</Item>
          <Item typeName="Int32" key="InvalidPointRxLev">-95</Item>
        </Item>
        <Item typeName="IDictionary" key="15022">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
          <Item typeName="Int32" key="SetRxlev">-80</Item>
          <Item typeName="Int32" key="SetRxlevDiff">12</Item>
          <Item typeName="Int32" key="InvalidPointRxLev">-95</Item>
        </Item>
        <Item typeName="IDictionary" key="16007">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
          <Item typeName="Int32" key="RxLevMax">-85</Item>
          <Item typeName="Int32" key="CellCoverDistance">300</Item>
          <Item typeName="Int32" key="WeakGridCount">2</Item>
          <Item typeName="Boolean" key="JudgeByScale">False</Item>
          <Item typeName="Int32" key="JudgeScale">50</Item>
        </Item>
        <Item typeName="IDictionary" key="16008">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
          <Item typeName="Int32" key="FilterPCCPCH_RSCP">-95</Item>
          <Item typeName="Int32" key="SampleCountLimit">1</Item>
        </Item>
        <Item typeName="IDictionary" key="16003">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
          <Item typeName="Int32" key="FilterRxlev">-90</Item>
          <Item typeName="Int32" key="NearestCellCount">3</Item>
          <Item typeName="Single" key="DisFactor">1.6</Item>
          <Item typeName="Int32" key="MinSampleCount">0</Item>
          <Item typeName="Single" key="MinPercent">0</Item>
          <Item typeName="Int32" key="MinDistance">0</Item>
          <Item typeName="Int32" key="MaxDistance">1000000</Item>
        </Item>
        <Item typeName="IDictionary" key="16002">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
          <Item typeName="Int32" key="RSCPThreshold">-85</Item>
          <Item typeName="Int32" key="RSCPDValue">10</Item>
          <Item typeName="Int32" key="NBSampleDistanceLimit">50</Item>
          <Item typeName="Int32" key="ValidDistance">50</Item>
        </Item>
        <Item typeName="IDictionary" key="16004">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
          <Item typeName="Int32" key="FilterPCCPCH_RSCPMin">-85</Item>
          <Item typeName="Int32" key="FilterPCCPCH_RSCPMax">-10</Item>
          <Item typeName="Int32" key="PilotFrequencyPolluteBlockRadius">50</Item>
          <Item typeName="Int32" key="SampleCountLimit">10</Item>
          <Item typeName="Int32" key="CellCountThreshold">4</Item>
          <Item typeName="Int32" key="RxLevDValueThreshold">6</Item>
        </Item>
        <Item typeName="IDictionary" key="16012">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
          <Item typeName="Boolean" key="CheckCellRank">True</Item>
          <Item typeName="Int32" key="CellRank">5</Item>
          <Item typeName="Boolean" key="CheckPccpchRscpThreshold">True</Item>
          <Item typeName="Double" key="PccpchRscpThreshold">-85</Item>
          <Item typeName="Double" key="DirectionGapDegreeThreshold">50</Item>
          <Item typeName="Double" key="DistanceRangeThreshold">0.6</Item>
        </Item>
        <Item typeName="IDictionary" key="16027">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
        </Item>
        <Item typeName="IDictionary" key="16010">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
          <Item typeName="Int32" key="RxLevThreshold">-85</Item>
          <Item typeName="Int32" key="DistanceThreshold">300</Item>
        </Item>
        <Item typeName="IDictionary" key="16011">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
          <Item typeName="Int32" key="SetRxlev">-80</Item>
          <Item typeName="Int32" key="CoverDistance">200</Item>
          <Item typeName="Int32" key="CellDistance">2000</Item>
        </Item>
        <Item typeName="IDictionary" key="16020">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
          <Item typeName="Int32" key="C_IThreshold">-3</Item>
          <Item typeName="Int32" key="RxLevThreshold">-95</Item>
          <Item typeName="Single" key="DistanceLast">50</Item>
          <Item typeName="Single" key="DitanceTP">50</Item>
        </Item>
        <Item typeName="IDictionary" key="16017">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
          <Item typeName="Int32" key="SetRxlev">-80</Item>
          <Item typeName="Int32" key="SetRxlevDiff">6</Item>
          <Item typeName="Int32" key="InvalidPointRxLev">-95</Item>
        </Item>
        <Item typeName="IDictionary" key="16018">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
          <Item typeName="Int32" key="SetRxlev">-80</Item>
          <Item typeName="Int32" key="SetRxlevDiff">6</Item>
          <Item typeName="Int32" key="InvalidPointRxLev">-95</Item>
        </Item>
        <Item typeName="IDictionary" key="23005">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
          <Item typeName="Int32" key="SetRxlev">-80</Item>
          <Item typeName="Int32" key="SetRxlevDiff">6</Item>
          <Item typeName="Int32" key="InvalidPointRxLev">-95</Item>
        </Item>
        <Item typeName="IDictionary" key="23007">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
          <Item typeName="Int32" key="RSCPThreshold">-85</Item>
          <Item typeName="Int32" key="RSCPDValue">10</Item>
          <Item typeName="Int32" key="NBSampleDistanceLimit">50</Item>
          <Item typeName="Int32" key="ValidDistance">50</Item>
        </Item>
        <Item typeName="IDictionary" key="23006">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
          <Item typeName="Double" key="DistanceMin">0</Item>
          <Item typeName="Int32" key="AngleMin">0</Item>
          <Item typeName="Single" key="RxLevMin">0</Item>
          <Item typeName="Double" key="WrongRateMin">0</Item>
          <Item typeName="String" key="SaveFilePath" />
          <Item typeName="Boolean" key="IsExportNow">False</Item>
          <Item typeName="Int32" key="ExportDateDiy">1</Item>
        </Item>
        <Item typeName="IDictionary" key="23008">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
          <Item typeName="Int32" key="RxLevThreshold">-85</Item>
          <Item typeName="Int32" key="DistanceThreshold">300</Item>
        </Item>
        <Item typeName="IDictionary" key="23009">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
          <Item typeName="Int32" key="SetRxlev">-80</Item>
          <Item typeName="Int32" key="CoverDistance">200</Item>
          <Item typeName="Int32" key="CellDistance">2000</Item>
        </Item>
        <Item typeName="IDictionary" key="23018">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
          <Item typeName="Boolean" key="BDtNbInfo">False</Item>
          <Item typeName="Boolean" key="BSecAna">False</Item>
          <Item typeName="Boolean" key="BSecDataExport">False</Item>
          <Item typeName="Boolean" key="BSampleShow">True</Item>
        </Item>
        <Item typeName="IDictionary" key="23021">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
          <Item typeName="Boolean" key="bChanelAna">False</Item>
          <Item typeName="Boolean" key="bChanelD">False</Item>
          <Item typeName="Boolean" key="bChanelE">False</Item>
          <Item typeName="Boolean" key="bChanelF">False</Item>
          <Item typeName="Int32" key="iChanelNum">3</Item>
          <Item typeName="Boolean" key="bSampleN">False</Item>
          <Item typeName="Int32" key="iSampleN">9</Item>
          <Item typeName="Boolean" key="bRSRP0">False</Item>
          <Item typeName="Single" key="fRsrp0Min">-140</Item>
          <Item typeName="Single" key="fRsrp0Max">25</Item>
          <Item typeName="Boolean" key="bRSRP1">False</Item>
          <Item typeName="Single" key="fRsrp1Min">-140</Item>
          <Item typeName="Single" key="fRsrp1Max">25</Item>
        </Item>
        <Item typeName="IDictionary" key="23024">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
          <Item typeName="IDictionary" key="FuncCond">
            <Item typeName="Boolean" key="IsShieldProblem">False</Item>
            <Item typeName="Boolean" key="IsLeakOutCheck">False</Item>
            <Item typeName="Boolean" key="IsFarCoverCheck">False</Item>
            <Item typeName="Int32" key="NearestBtsCount">3</Item>
            <Item typeName="Double" key="RadiusFactor">1.6</Item>
            <Item typeName="Boolean" key="IsCloseSimuCheck">False</Item>
            <Item key="CloseSimuDic" />
            <Item typeName="Boolean" key="IsTwoEarfcn">False</Item>
            <Item typeName="Boolean" key="IsFreqBand">False</Item>
            <Item typeName="Int32" key="band">0</Item>
            <Item typeName="Int32" key="AbsValue">-110</Item>
            <Item typeName="Boolean" key="IsAbsCheck">False</Item>
            <Item typeName="Boolean" key="IsRelativeCheck">True</Item>
            <Item typeName="Int32" key="AbsCoverate">11</Item>
            <Item typeName="Int32" key="RxLevMaxDiff">6</Item>
            <Item typeName="Int32" key="RxLevMin">-95</Item>
            <Item typeName="Int32" key="RelCoverate">4</Item>
            <Item typeName="Int32" key="RoadDistance">50</Item>
            <Item typeName="Double" key="RoadMinPercent">100</Item>
            <Item typeName="Int32" key="SampleDistance">50</Item>
          </Item>
        </Item>
        <Item typeName="IDictionary" key="18049">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
          <Item typeName="String" key="ExcelPath" />
          <Item typeName="String" key="ImportedExcelPath" />
          <Item typeName="String" key="WebServicePath" />
          <Item typeName="String" key="PicPath" />
          <Item typeName="String" key="AttachPath" />
          <Item typeName="Int32" key="TimeDiff">0</Item>
          <Item typeName="Int32" key="ErrorTimeDiff">72</Item>
          <Item typeName="Int32" key="TimeOverlap">80</Item>
          <Item typeName="Int32" key="TPOverlap">80</Item>
          <Item typeName="Int32" key="TPTimeDiff">2</Item>
          <Item typeName="Int32" key="TPDistance">50</Item>
        </Item>
        <Item typeName="IDictionary" key="22121">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
          <Item typeName="IDictionary" key="ExportReportSet">
            <Item typeName="String" key="ReportSavePath" />
            <Item typeName="Boolean" key="IsCheckWebservice">False</Item>
            <Item typeName="String" key="ResultWebservicePath" />
            <Item typeName="Int32" key="WorkParamBeforeDays_Begin">2</Item>
            <Item typeName="Int32" key="WorkParamBeforeDays_End">0</Item>
            <Item typeName="String" key="CellParamFolderPath" />
            <Item typeName="String" key="AntennaInfoFolderPath" />
            <Item typeName="String" key="OutdoorBtsPicFolderPath" />
            <Item typeName="String" key="IndoorCoverPicFolderPath" />
            <Item typeName="Boolean" key="IsAnaFusionDatas">False</Item>
            <Item typeName="Boolean" key="IsAnaFusionByRecentDays">True</Item>
            <Item typeName="Int32" key="FusionRecentDays">7</Item>
            <Item typeName="String" key="FusionByPeriodBeginDate">2018/10/10</Item>
            <Item typeName="String" key="FusionByPeriodEndDate">2018/10/10</Item>
            <Item typeName="Int32" key="FusionAccordLastDays">3</Item>
            <Item typeName="String" key="FusionCheckDistictNames" />
          </Item>
          <Item typeName="IDictionary" key="Params_FTP">
            <Item key="FtpUserName" />
            <Item key="FtpUserPwd" />
            <Item key="FtpServerPath" />
          </Item>
        </Item>
        <Item typeName="IDictionary" key="22122">
          <Item typeName="Boolean" key="BackgroundStat">False</Item>
          <Item typeName="IDictionary" key="ExportReportSet">
            <Item typeName="String" key="ReportSavePath" />
            <Item typeName="Boolean" key="IsCheckWebservice">False</Item>
            <Item typeName="String" key="ResultWebservicePath" />
            <Item typeName="Int32" key="WorkParamBeforeDays_Begin">2</Item>
            <Item typeName="Int32" key="WorkParamBeforeDays_End">0</Item>
            <Item typeName="String" key="CellParamFolderPath" />
            <Item typeName="String" key="AntennaInfoFolderPath" />
            <Item typeName="String" key="OutdoorBtsPicFolderPath" />
            <Item typeName="String" key="IndoorCoverPicFolderPath" />
            <Item typeName="Boolean" key="IsAnaFusionDatas">False</Item>
            <Item typeName="Boolean" key="IsAnaFusionByRecentDays">True</Item>
            <Item typeName="Int32" key="FusionRecentDays">7</Item>
            <Item typeName="String" key="FusionByPeriodBeginDate">2018/11/20</Item>
            <Item typeName="String" key="FusionByPeriodEndDate">2018/11/20</Item>
            <Item typeName="Int32" key="FusionAccordLastDays">3</Item>
            <Item typeName="String" key="FusionCheckDistictNames" />
          </Item>
          <Item typeName="IDictionary" key="Params_FTP">
            <Item key="FtpUserName" />
            <Item key="FtpUserPwd" />
            <Item key="FtpServerPath" />
          </Item>
        </Item>
      </Item>
    </Item>
  </Config>
</Configs>