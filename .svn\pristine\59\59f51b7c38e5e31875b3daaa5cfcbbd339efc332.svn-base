﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc.GridOrderCommon
{
    public class OrderCellItem
    {
        public int SetID { get; set; }

        public int CityID { get; set; }

        public int ItemID { get; set; }

        public int CellID { get; set; }

        public int SetTokenID { get; set; }

        public string GridSN { get; set; }

        public int ProjectID { get; set; }

        public int ServiceID { get; set; }

        public int FileID { get; set; }

        public string LogTbName { get; set; }

        public int Lac { get; set; }

        public int Ci { get; set; }

        public string CellName { get; set; }

        private ICell cell;
        public ICell Cell
        {
            get
            {
                if (cell == null)
                {
                    cell = CellManager.GetInstance().GetCurrentLTECell(Lac, Ci);
                }
                return cell;
            }
        }

        public string PrimaryCause { get; set; }

        public string SpecifictCause { get; set; }

        public string Detail { get; set; }

        public string Suggest { get; set; }

        public int Status
        {
            get;
            set;
        }

        public string IsProbCell
        {
            get { return Status == 0 ? "否" : "是"; }
        }

        public OrderGridItem Grid
        {
            get;
            set;
        }

        public string OrderKey
        {
            get
            {
                return string.Format("{0}-{1}-{2}", CityID, SetTokenID, SetID);
            }
        }

        public Dictionary<string, double> KPIDic { get; set; } = new Dictionary<string, double>();


        public object FileName { get; set; }
    }
}
