﻿using MasterCom.RAMS.Func;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NRLastWeakMosAnaForm : MinCloseForm
    {
        public NRLastWeakMosAnaForm()
        {
            InitializeComponent();

            this.gridView1.OptionsView.ColumnAutoWidth = false;
            this.gridView1.OptionsBehavior.Editable = false;

            ToolStripMenuItem item = new ToolStripMenuItem("导出Excel");
            item.Click += item_Click;
            ContextMenuStrip menu = new ContextMenuStrip();
            menu.Items.Add(item);
            this.gridControl1.ContextMenuStrip = menu;

            this.gridView1.DoubleClick += gridView1_DoubleClick;
        }

        private List<NRLastWeakMosResult> listResult = new List<NRLastWeakMosResult>();

        void item_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(gridView1);
        }

        void gridView1_DoubleClick(object sender, EventArgs e)
        {
            try
            {
                int[] rows = gridView1.GetSelectedRows();
                if (rows == null || rows.Length < 1 || rows[0] < 0)
                {
                    return;
                }
                NRLastWeakMosResult lr = gridView1.GetRow(rows[0]) as NRLastWeakMosResult;
                if (lr == null)
                {
                    return;
                }
                MainModel.DTDataManager.Clear();
                MainModel.DTDataManager.Add(lr.Ev);
                MainModel.FireSetDefaultMapSerialThemes("NR:SS_RSRP");
                MainModel.MainForm.GetMapForm().GoToView(lr.Ev.Longitude, lr.Ev.Latitude);
                MainModel.FireDTDataChanged(this);
            }
            catch
            {
                //continue
            }
        }

        public void FillData(List<NRLastWeakMosResult> lr)
        {
            this.listResult = lr;
            this.gridControl1.DataSource = this.listResult;
            this.gridControl1.RefreshDataSource();
            this.gridControl1.Refresh();
        }
    }
}
