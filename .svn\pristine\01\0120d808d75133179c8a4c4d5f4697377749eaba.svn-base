﻿using MasterCom.MTGis;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NRHttpPageAnaForm : MinCloseForm
    {
        public NRHttpPageAnaForm() : base()
        {
            InitializeComponent();
        }

        public void FillData(List<NRHttpPageAnaItem> list)
        {
            gridControl.DataSource = list;
            gridControl.RefreshDataSource();
            MainModel.ClearDTData();
            foreach (NRHttpPageAnaItem item in list)
            {
                foreach (TestPoint tp in item.TpsList)
                {
                    MainModel.DTDataManager.Add(tp);
                }
            }
            MainModel.FireDTDataChanged(this);
        }

        private void gv_DoubleClick(object sender, EventArgs e)
        {
            NRHttpPageAnaItem httpPage = gv.GetFocusedRow() as NRHttpPageAnaItem;
            if (httpPage != null)
            {
                MainModel.ClearSelectedTestPoints();
                MainModel.DTDataManager.Clear();
                foreach (TestPoint tp in httpPage.TpsList)
                {
                    MainModel.DTDataManager.Add(tp);
                }
                MainModel.FireDTDataChanged(MainModel.MainForm);
                OutlineOfRoad outRoad = new OutlineOfRoad();
                outRoad.SetPoints(httpPage.TpsList);
                TempLayer.Instance.Draw(outRoad.Drawer);
            }
        }

        private void miExport2Xls_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(gv);
        }

        private void miReplay_Click(object sender, EventArgs e)
        {
            NRHttpPageAnaItem item = gv.GetRow(gv.FocusedRowHandle) as NRHttpPageAnaItem;
            if (item == null)
            {
                return;
            }
            MainModel.MainForm.NeedChangeWorkSpace(false);
            PreNextMinutesPeriodForm preNextMinutePeriodForm = new PreNextMinutesPeriodForm();
            preNextMinutePeriodForm.Pre = 0;
            preNextMinutePeriodForm.Next = 0;
            if (preNextMinutePeriodForm.ShowDialog() == DialogResult.OK)
            {
                int pre = preNextMinutePeriodForm.Pre;
                int next = preNextMinutePeriodForm.Next;
                DateTime timeStart = item.BeginTime.AddMinutes(-pre);
                DateTime timeEnd = item.EndTime.AddMinutes(next);
                FileReplayer.Replay(item.TpsList[0].FileInfo, new MasterCom.Util.TimePeriod(timeStart, timeEnd));
            }
            else
            {
                MainModel.MainForm.CancelChange = true;
            }
            MainModel.MainForm.ChangeWorkSpace();
        }
    }
}
