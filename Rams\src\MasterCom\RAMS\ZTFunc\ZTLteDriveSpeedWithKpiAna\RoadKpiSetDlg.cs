﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class RoadKpiSetDlg : BaseForm
    {
        public RoadKpiSetDlg(RoadKpiCondition condition)
        {
            InitializeComponent();
            setCondition(condition);
        }

        private void setCondition(RoadKpiCondition condition)
        {
            if (condition == null)
            {
                condition = new RoadKpiCondition();
            }

            numGoodRsrpGate.Value = (decimal)condition.GoodRsrpGate;
            numGoodSinrGate.Value = (decimal)condition.GoodSinrGate;
            numGoodSpeedGate.Value = (decimal)condition.GoodDownLoadSpeedGate;
            chkGoodDlSpeed.Checked = condition.IsCheckGoodDlSpeed;
            cmbGoodKpi.SelectedIndex = condition.IsGoodKpiAllAnd ? 0 : 1;

            numBadRsrpGate.Value = (decimal)condition.BadRsrpGate;
            numBadSinrGate.Value = (decimal)condition.BadSinrGate;
            numBadSpeedGate.Value = (decimal)condition.BadDownLoadSpeedGate;
            chkBadDlSpeed.Checked = condition.IsCheckBadDlSpeed;
            cmbBadKpi.SelectedIndex = condition.IsBadKpiAllAnd ? 0 : 1;

            chkMinDistance.Checked = condition.CheckMinDistance;
            numMinDistance.Value = (decimal)condition.MinCoverRoadDistance;
            chkMinDuration.Checked = condition.CheckMinDuration;
            numMinDuration.Value = (decimal)condition.MinDuration;
            numMaxTPDistance.Value = (decimal)condition.Max2TPDistance;
            numCommonTpPercent.Value = (decimal)condition.CommonTpPercentGate;

            numFastMinSpeed.Value = (decimal)condition.FastSpeedMinGate;
            numSlowMaxSpeed.Value = (decimal)condition.SlowSpeedMaxGate;
            numSlowMinSpeed.Value = (decimal)condition.SlowSpeedMinGate;
            numStopMaxSpeed.Value = (decimal)condition.StopSpeedMaxGate;
        }

        public RoadKpiCondition GetCondition()
        {
            RoadKpiCondition condition = new RoadKpiCondition();
            condition.GoodRsrpGate = (float)numGoodRsrpGate.Value;
            condition.GoodSinrGate = (float)numGoodSinrGate.Value;
            condition.GoodDownLoadSpeedGate = (float)numGoodSpeedGate.Value;
            condition.IsCheckGoodDlSpeed = chkGoodDlSpeed.Checked;
            condition.IsGoodKpiAllAnd = cmbGoodKpi.SelectedIndex == 0;

            condition.BadRsrpGate = (float)numBadRsrpGate.Value;
            condition.BadSinrGate = (float)numBadSinrGate.Value;
            condition.BadDownLoadSpeedGate = (float)numBadSpeedGate.Value;
            condition.IsCheckBadDlSpeed = chkBadDlSpeed.Checked;
            condition.IsBadKpiAllAnd = cmbBadKpi.SelectedIndex == 0;

            condition.CheckMinDistance = chkMinDistance.Checked;
            condition.MinCoverRoadDistance = (float)numMinDistance.Value;
            condition.CheckMinDuration = chkMinDuration.Checked;
            condition.MinDuration = (float)numMinDuration.Value;
            condition.Max2TPDistance = (float)numMaxTPDistance.Value;
            condition.CommonTpPercentGate = (float)numCommonTpPercent.Value;

            condition.FastSpeedMinGate = (float)numFastMinSpeed.Value;
            condition.SlowSpeedMaxGate = (float)numSlowMaxSpeed.Value;
            condition.SlowSpeedMinGate = (float)numSlowMinSpeed.Value;
            condition.StopSpeedMaxGate = (float)numStopMaxSpeed.Value;
            return condition;
        }

        private void chkMinDuration_CheckedChanged(object sender, EventArgs e)
        {
            numMinDuration.Enabled = chkMinDuration.Checked;
            if (!chkMinDuration.Checked)
            {
                chkMinDistance.Checked = true;
            }
        }

        private void chkMinDistance_CheckedChanged(object sender, EventArgs e)
        {
            numMinDistance.Enabled = chkMinDistance.Checked;
            if (!chkMinDistance.Checked)
            {
                chkMinDuration.Checked = true;
            }
        }

        private void chkGoodDlSpeed_CheckedChanged(object sender, EventArgs e)
        {
            numGoodSpeedGate.Enabled = chkGoodDlSpeed.Checked;
        }

        private void chkBadDlSpeed_CheckedChanged(object sender, EventArgs e)
        {
            numBadSpeedGate.Enabled = chkBadDlSpeed.Checked;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            if (numFastMinSpeed.Value < numSlowMaxSpeed.Value || numSlowMaxSpeed.Value < numSlowMinSpeed.Value
                || numSlowMinSpeed.Value < numStopMaxSpeed.Value)
            {
                DevExpress.XtraEditors.XtraMessageBox.Show("车速值范围不可交叉！");
                return;
            }
            this.DialogResult = DialogResult.OK;
        }

    }
}
