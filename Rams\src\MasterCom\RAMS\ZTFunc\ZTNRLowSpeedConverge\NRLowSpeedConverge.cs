﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRLowSpeedConverge : DIYAnalyseByFileBackgroundBase
    {
        List<NRLowSpeedConvergeBlock> lowSpeedBlockList;
        NRLowSpeedConvergeCondition curCondition;

        protected static readonly object lockObj = new object();
        protected NRLowSpeedConverge()
            : base(MainModel.GetInstance())
        {
            IncludeMessage = true;

            Columns = NRTpHelper.InitBaseReplayParamBackground(false, false);
            Columns.Add("NR_APP_type");
            Columns.Add("NR_APP_Speed_Mb");

            Columns.Add("NR_PDSCH_BLER");
            Columns.Add("NR_PDCCH_DL_Grant_Count");
            Columns.Add("NR_64QAM_Count_DL_TB0");
            Columns.Add("NR_QPSK_Count_DL_TB0");
            Columns.Add("NR_16QAM_Count_DL_TB0");
            Columns.Add("NR_64QAM_Count_DL_TB1");
            Columns.Add("NR_QPSK_Count_DL_TB1");
            Columns.Add("NR_16QAM_Count_DL_TB1");

            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.NR_SA_TDD_DATA);
            ServiceTypes.Add(ServiceType.NR_NSA_TDD_DATA);
            ServiceTypes.Add(ServiceType.NR_DM_TDD_DATA);
        }

        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 35000, 35047, this.Name);
        }

        protected override bool getCondition()
        {
            NRLowSpeedConvergeDlg dlg = new NRLowSpeedConvergeDlg();
            dlg.SetCondition(curCondition);
            if (dlg.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                curCondition = dlg.GetCondition();
                return true;
            }
            return false;
        }

        protected override void getReadyBeforeQuery()
        {
            lowSpeedBlockList = new List<NRLowSpeedConvergeBlock>();
        }

        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
            {
                foreach (var tp in file.TestPoints)
                {
                    if (validTestPoint(tp))
                    {
                        gatherBlock(tp);
                    }
                }
            }
        }

        protected virtual bool validTestPoint(TestPoint tp)
        {
            try
            {
                if (base.isValidTestPoint(tp))
                {
                    int? earfcn = (int?)NRTpHelper.NrTpManager.GetEARFCN(tp);
                    int? pci = (int?)NRTpHelper.NrTpManager.GetEARFCN(tp);
                    double? speed = NRTpHelper.NrTpManager.GetAppSpeedMb(tp);
                    if (earfcn != null && pci != null && speed != null && speed > 0 && speed < curCondition.AppSpeed)
                    {
                        return true;
                    }
                }
                return false;
            }
            catch
            {
                return false;
            }
        }

        private void gatherBlock(TestPoint tp)
        {
            //获取与采样点相交的block
            List<NRLowSpeedConvergeBlock> tmpList = new List<NRLowSpeedConvergeBlock>();
            foreach (NRLowSpeedConvergeBlock block in lowSpeedBlockList)
            {
                if (block.Intersect(tp.Longitude, tp.Latitude, curCondition.Radius))
                {
                    tmpList.Add(block);
                }
            }

            if (tmpList.Count == 0)
            {
                //没有相交的block则新增一个block
                NRLowSpeedConvergeBlock item = new NRLowSpeedConvergeBlock(tp);
                NRLowSpeedConvergeSample sample = new NRLowSpeedConvergeSample();
                sample.SetSampleInfo(tp);
                item.Samplelist.Add(sample);
                lowSpeedBlockList.Add(item);
            }
            else
            {
                //存在相交的block则汇聚
                NRLowSpeedConvergeBlock blockTmp = null;
                for (int i = 0; i < tmpList.Count; i++)
                {
                    NRLowSpeedConvergeBlock block = tmpList[i];
                    if (blockTmp == null)
                    {
                        blockTmp = block;
                        block.AddTestPoint(tp);
                        NRLowSpeedConvergeSample sample = new NRLowSpeedConvergeSample();
                        sample.SetSampleInfo(tp);
                        block.Samplelist.Add(sample);
                    }
                    else
                    {
                        blockTmp.Join(block);
                        lowSpeedBlockList.Remove(block);
                    }
                }
            }
        }

        protected override void getResultsAfterQuery()
        {
            List<NRLowSpeedConvergeBlock> resultList = new List<NRLowSpeedConvergeBlock>();
            MainModel.DTDataManager.Clear();
            int id = 1;
            foreach (NRLowSpeedConvergeBlock block in lowSpeedBlockList)
            {
                block.GetResult();
                if (block.Samplelist.Count >= curCondition.SampleCountLimit)
                {
                    block.ID = id++;
                    int sn = 1;
                    foreach (NRLowSpeedConvergeSample sample in block.Samplelist)
                    {
                        sample.SN = sn++;
                        MainModel.DTDataManager.Add(sample.TestPoint);
                    }
                    resultList.Add(block);
                }
            }
            lowSpeedBlockList = resultList;
        }

        protected override void fireShowForm()
        {
            NRLowSpeedConvergeForm frm = MainModel.CreateResultForm(typeof(NRLowSpeedConvergeForm)) as NRLowSpeedConvergeForm;
            frm.FillData(lowSpeedBlockList);
            frm.Visible = true;
            frm.BringToFront();
        }
    }

    public class NRLowSpeedConvergeByFile : NRLowSpeedConverge
    {
        private NRLowSpeedConvergeByFile()
            : base()
        {
        }

        private static NRLowSpeedConvergeByFile instance = null;
        public static NRLowSpeedConvergeByFile GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new NRLowSpeedConvergeByFile();
                    }
                }
            }
            return instance;
        }

        public override string Name
        {
            get { return "NR低速率点汇聚(按文件)"; }
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.File;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                return true;
            }
            return false;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        /// <summary>
        /// 判断是否在所选区域内
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            return true;
        }

    }

    public class NRLowSpeedConvergeByRegion : NRLowSpeedConverge
    {
        protected NRLowSpeedConvergeByRegion()
            : base()
        {
        }

        private static NRLowSpeedConvergeByRegion instance = null;
        public static NRLowSpeedConvergeByRegion GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new NRLowSpeedConvergeByRegion();
                    }
                }
            }
            return instance;
        }

        public override string Name
        {
            get { return "NR低速率点汇聚(按区域)"; }
        }
    }
}
