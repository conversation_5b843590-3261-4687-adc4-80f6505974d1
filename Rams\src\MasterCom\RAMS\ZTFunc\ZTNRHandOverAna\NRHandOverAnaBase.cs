﻿using System;
using System.Collections.Generic;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using EvtEngineLib;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRHandOverAnaBase : DIYAnalyseFilesOneByOneByRegion
    {
        public List<NRHandOverAnaItem> resultList { get; set; } = new List<NRHandOverAnaItem>();    //保存结果
        public NRHandOverAnaCondition hoCondition { get; set; } = new NRHandOverAnaCondition();   //查询条件
        protected List<int> valuedEvtId;

        public NRHandOverAnaBase(MainModel mainModel)
            : base(mainModel)
        {
            this.IncludeEvent = true;
            this.IncludeMessage = false;

            ServiceTypes = ServiceTypeManager.GetServiceTypesByServiceName(ServiceName.NR);
            Columns = NRTpHelper.InitBaseReplayParamBackground(true, true);
        }

        protected override void clearDataBeforeAnalyseFiles()
        {
            resultList = new List<NRHandOverAnaItem>();
        }

        NRHandOverAnaSetForm setForm = null;
        protected override bool getCondition()
        {
            if (setForm == null)
            {
                setForm = new NRHandOverAnaSetForm();
            }

            if (setForm.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                hoCondition = setForm.GetCondition();
                valuedEvtId = new List<int>();

                valuedEvtId = NREventHelper.HandoverHelper.GetHandoverSuccessEvt(hoCondition.IsAnaLte);
                return true;
            }
            return false;
        }

        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager fileMng in MainModel.DTDataManager.FileDataManagers)
            {
                List<TestPoint> testPointList = fileMng.TestPoints;
                List<Event> eventList = fileMng.Events;
                if (hoCondition.IsAnaLte)
                {
                    NREventHelper.HandoverHelper.FilterHandoverEvents(eventList);
                }

                int idx = 0;
                for (int eLoop = 0; eLoop < eventList.Count; ++eLoop)
                {
                    Event e = eventList[eLoop];
                    if (!valuedEvtId.Contains(e.ID))
                    {
                        continue;
                    }

                    int index = GetNearestTestPointIndex(e.SN, testPointList);
                    if (index  == -1)
                    {
                        continue;
                    }

                    idx = addResultList(testPointList, idx, e, index);
                }
            }
        }

        private int addResultList(List<TestPoint> testPointList, int idx, Event e, int index)
        {
            NRHandOverAnaItem item = new NRHandOverAnaItem(e.FileName, e);

            long tpTimeHead = (long)(e.Time * 1000L + e.Millisecond - hoCondition.BeforeSecond * 1000L);
            long tpTimeTail = (long)(e.Time * 1000L + e.Millisecond + hoCondition.AfterSecond * 1000L);

            for (int i = index; i >= 0; --i)
            {
                TestPoint tp = testPointList[i];
                if ((tp.Time * 1000L + tp.Millisecond) < tpTimeHead)
                {
                    break;
                }
                item.AddBeforeTp(tp);
            }
            for (int i = index + 1; i < testPointList.Count; ++i)
            {
                TestPoint tp = testPointList[i];
                if (tpTimeTail < (tp.Time * 1000L + tp.Millisecond))
                {
                    break;
                }
                item.AddAfterTp(tp);
            }

            item.SetSameSiteFlag(hoCondition.SiteDistance);
            item.SN = resultList.Count + 1;
            item.Calculate();
            item.SetHandOverReasonable();

            resultList.Add(item);
            return idx;
        }

        /// <summary>
        /// 显示结果
        /// </summary>
        protected override void fireShowForm()
        {
            if (resultList.Count == 0)
            {
                System.Windows.Forms.MessageBox.Show("没有切换事件！");
                return;
            }

            object obj = MainModel.GetInstance().GetObjectFromBlackboard(typeof(NRHandOverAnaListForm).FullName);
            NRHandOverAnaListForm cellReselectAnaListForm = obj == null ? null : obj as NRHandOverAnaListForm;
            if (cellReselectAnaListForm == null || cellReselectAnaListForm.IsDisposed)
            {
                cellReselectAnaListForm = new NRHandOverAnaListForm(MainModel);
            }

            cellReselectAnaListForm.FillData(resultList);
            if (!cellReselectAnaListForm.Visible)
            {
                cellReselectAnaListForm.Show(MainModel.MainForm);
            }
        }

        protected override void releaseSource()
        {
            resultList = null;
        }

        /// <summary>
        /// tpList升序，寻找最大的index使得tpList[index] <= eventSN
        /// </summary>
        /// <param name="eventSN"></param>
        /// <param name="tpList"></param>
        /// <returns></returns>
        protected int GetNearestTestPointIndex(int eventSN, List<TestPoint> tpList)
        {
            int index = -1;
            for (int i = 0; i < tpList.Count; i++)
            {
                if (tpList[i].SN > eventSN)
                {
                    index = i - 1;
                    break;
                }
            }
            return index;
        }

        private Comparer comparer { get; set; } = new Comparer();
        private class Comparer : IComparer<DTData>
        {
            public int Compare(DTData x, DTData y)
            {
                return x.SN - y.SN;
            }
        }
    }

    public class NRHandOverAnaItem
    {
        public int SN { get; set; }
        public string FileName { get; set; }

        public string DateTime { get; private set; } = "";
        public double Longitude { get; private set; } = 0;
        public double Latitude { get; private set; } = 0;

        //public string GridName { get; private set; }
        public string HandOverResult { get; private set; } = "";
        public string HnadOverDirection { get; private set; }
        public string HandOverType { get; private set; }
        public string HandOverReasonable { get; private set; }
        public string IsSameSite { get; set; }

        public NRHandOverAnaCellItem NrSrcCellItem { get; set; }
        public NRHandOverAnaCellItem NrDestCellItem { get; set; }
        public NRHandOverAnaCellItem LteSrcCellItem { get; set; }
        public NRHandOverAnaCellItem LteDestCellItem { get; set; }


        public Event HOEvt { get; set; }
        public List<TestPoint> TpList { get; set; }
        public NRHandOverType handoverType { get; set; }

        public NRHandOverAnaItem(string fileName, Event hoEvt)
        {
            FileName = fileName;
            HOEvt = hoEvt;
            NrSrcCellItem = new NRHandOverAnaCellItem();
            NrDestCellItem = new NRHandOverAnaCellItem();
            LteSrcCellItem = new NRHandOverAnaCellItem();
            LteDestCellItem = new NRHandOverAnaCellItem();

            TpList = new List<TestPoint>();

            handoverType = NREventHelper.HandoverHelper.GetHandoverType(hoEvt.ID, true);

            if (handoverType == NRHandOverType.LTE)
            {
                SetCellItem(LteSrcCellItem, hoEvt, true, NREventHelper.HandoverHelper.LTE);
                SetCellItem(LteDestCellItem, hoEvt, false, NREventHelper.HandoverHelper.LTE);
            }
            else if (handoverType == NRHandOverType.NSALTE)
            {
                SetCellItem(LteSrcCellItem, hoEvt, true, NREventHelper.HandoverHelper.NSALTE);
                SetCellItem(LteDestCellItem, hoEvt, false, NREventHelper.HandoverHelper.NSALTE);
            }
            else if (handoverType == NRHandOverType.NSANR)
            {
                SetCellItem(NrSrcCellItem, hoEvt, true, NREventHelper.HandoverHelper.NSANR);
                SetCellItem(NrDestCellItem, hoEvt, false, NREventHelper.HandoverHelper.NSANR);
            }
            else if (handoverType == NRHandOverType.NSANRLTE)
            {
                SetCellItem(NrSrcCellItem, hoEvt, true, NREventHelper.HandoverHelper.NSANR);
                SetCellItem(NrDestCellItem, hoEvt, false, NREventHelper.HandoverHelper.NSANR);

                SetCellItem(LteSrcCellItem, hoEvt, true, NREventHelper.HandoverHelper.NSALTE);
                SetCellItem(LteDestCellItem, hoEvt, false, NREventHelper.HandoverHelper.NSALTE);
            }
            else if (handoverType == NRHandOverType.SA)
            {
                SetCellItem(NrSrcCellItem, hoEvt, true, NREventHelper.HandoverHelper.SA);
                SetCellItem(NrDestCellItem, hoEvt, false, NREventHelper.HandoverHelper.SA);
            }
        }

        public void AddBeforeTp(TestPoint tp)
        {
            if (handoverType == NRHandOverType.NSALTE
                || handoverType == NRHandOverType.LTE)
            {
                AddTpInfo(this.LteSrcCellItem, tp, NRTpHelper.NrLteTpManager);
            }
            else if (handoverType == NRHandOverType.NSANR
                || handoverType == NRHandOverType.SA)
            {
                AddTpInfo(this.NrSrcCellItem, tp, NRTpHelper.NrTpManager);
            }
            else if (handoverType == NRHandOverType.NSANRLTE)
            {
                AddTpInfo(this.NrSrcCellItem, tp, NRTpHelper.NrTpManager);
                AddTpInfo(this.LteSrcCellItem, tp, NRTpHelper.NrLteTpManager);
            }
        }

        public void AddAfterTp(TestPoint tp)
        {
            if (handoverType == NRHandOverType.NSALTE
                || handoverType == NRHandOverType.LTE)
            {
                addLteAfterTp(tp, NRTpHelper.NrLteTpManager);
            }
            else if (handoverType == NRHandOverType.NSANR
                || handoverType == NRHandOverType.SA)
            {
                addNRAfterTp(tp, NRTpHelper.NrTpManager);
            }
            else if (handoverType == NRHandOverType.NSANRLTE)
            {
                addNRAfterTp(tp, NRTpHelper.NrTpManager);
                addLteAfterTp(tp, NRTpHelper.NrLteTpManager);
            }
        }

        private void addLteAfterTp(TestPoint tp, NRTpManagerBase nrCond)
        {
            //判断邻区是否包含源小区,包含则记录源小区
            for (int j = 0; j < 12; j++)
            {
                LTECell nCell = tp.GetNBCell_LTE(j);
                if (nCell != null && nCell.EARFCN == LteSrcCellItem.ARFCN && nCell.PCI == LteSrcCellItem.PCI)
                {
                    float? nRsrp = nrCond.GetNCellRsrp(tp, j);
                    if (nRsrp != null && -141 <= nRsrp && nRsrp <= 25)
                    {
                        LteDestCellItem.SrcRsrpInfo.Sum += (float)nRsrp;
                        LteDestCellItem.SrcRsrpInfo.Count++;
                    }
                }
            }
            AddTpInfo(LteDestCellItem, tp, nrCond);
        }

        private void addNRAfterTp(TestPoint tp, NRTpManagerBase nrCond)
        {
            for (int j = 0; j < 16; j++)
            {
                bool isNCell = NRTpHelper.NrTpManager.JudgeIsNCell(tp, j);
                if (!isNCell)
                {
                    continue;
                }
                NRCell nCell = tp.GetNBCell_NR(j);
                if (nCell != null && nCell.SSBARFCN == NrDestCellItem.ARFCN && nCell.PCI == NrDestCellItem.PCI)
                {
                    float? nRsrp = nrCond.GetNCellRsrp(tp, j);
                    if (nRsrp != null && -141 <= nRsrp && nRsrp <= 25)
                    {
                        NrDestCellItem.SrcRsrpInfo.Sum += (float)nRsrp;
                        NrDestCellItem.SrcRsrpInfo.Count++;
                    }
                }
            }
            AddTpInfo(NrDestCellItem, tp, nrCond);
        }

        public void AddTpInfo(NRHandOverAnaCellItem tpItem, TestPoint tp, NRTpManagerBase nrCond)
        {
            TpList.Add(tp);
            tpItem.TPCount++;
            float? rsrp = nrCond.GetSCellRsrp(tp);
            if (rsrp != null && rsrp >= -140 && rsrp <= -10)
            {
                tpItem.RsrpInfo.Sum += (float)rsrp;
                tpItem.RsrpInfo.Count++;
            }

            float? sinr = nrCond.GetSCellSinr(tp);
            if (sinr != null && sinr >= -50 && sinr <= 50)
            {
                tpItem.SinrInfo.Sum += (float)sinr;
                tpItem.SinrInfo.Count++;
            }
        }

        #region
        public void SetCellItem(NRHandOverAnaCellItem cellItem, Event reselEvt, bool isSrc, HandOverEventBase helper)
        {
            if (isSrc)
            {
                HandOverEventBase.CellInfo cell = helper.GetSrcCellInfo(reselEvt);
                setCellInfo(cellItem, reselEvt, cell);
            }
            else
            {
                HandOverEventBase.CellInfo cell = helper.GetTarCellInfo(reselEvt);
                setCellInfo(cellItem, reselEvt, cell);
            }
        }

        private void setCellInfo(NRHandOverAnaCellItem cellItem, Event reselEvt, HandOverEventBase.CellInfo info)
        {
            if (info.Cell != null)
            {
                cellItem.Cell = info.Cell;
                cellItem.CellName = info.Cell.Name;
                double dis = MathFuncs.GetDistance(info.Cell.Longitude, info.Cell.Latitude, reselEvt.Longitude, reselEvt.Latitude);
                cellItem.Distance = Math.Round(dis, 2).ToString();
            }
            cellItem.ARFCN = info.ARFCN;
            cellItem.PCI = info.PCI;
        }
        #endregion

        public void SetSameSiteFlag(int siteDistance)
        {
            if (NrSrcCellItem.Cell != null && NrDestCellItem.Cell != null)
            {
                if (MathFuncs.GetDistance(NrSrcCellItem.Cell.Longitude, NrSrcCellItem.Cell.Latitude,
                    NrDestCellItem.Cell.Longitude, NrDestCellItem.Cell.Latitude) <= siteDistance)
                {
                    IsSameSite = "是";
                }
                else
                {
                    IsSameSite = "否";
                }
            }
        }

        public void Calculate()
        {
            if (HOEvt != null)
            {
                DateTime = HOEvt.DateTime.ToString("yyyy-MM-dd HH:mm:ss");
                Longitude = HOEvt.Longitude;
                Latitude = HOEvt.Latitude;

                if (HOEvt.ID == 9296 || HOEvt.ID == 9299)
                {
                    HandOverResult = "成功";
                }
                else if (HOEvt.ID == 9297 || HOEvt.ID == 9300)
                {
                    HandOverResult = "失败";
                }
            }

            NrSrcCellItem.Calculate();
            NrDestCellItem.Calculate();
            LteSrcCellItem.Calculate();
            LteDestCellItem.Calculate();

            HnadOverDirection = NrSrcCellItem.FreqBand + "->" + NrDestCellItem.FreqBand;

            HandOverType = NREventHelper.HandoverHelper.GetHandoverTypeDesc(handoverType);
        }

        public void SetHandOverReasonable()
        {
            HandOverReasonable = "合理";

            /* 不合理原因：
             * 切换前弱覆盖、质差、无覆盖
             * 切换后弱覆盖、质差、无覆盖
             * 切换不合理、切换过慢
             */

            List<string> HandOverReason = new List<string>();

            double srcNrCount = NrSrcCellItem.RsrpInfo.Count;
            double srcNrAvgRsrp = NrSrcCellItem.RsrpInfo.Avg;
            double srcNrAvgSinr = NrSrcCellItem.SinrInfo.Avg;
            double destNrCount = NrDestCellItem.RsrpInfo.Count;
            double destNrAvgRsrp = NrDestCellItem.RsrpInfo.Avg;
            double destNrAvgSinr = NrDestCellItem.SinrInfo.Avg;

            double srcLteAvgRsrp = LteSrcCellItem.RsrpInfo.Avg;
            double srcLteCount = LteSrcCellItem.RsrpInfo.Count;
            double destLteAvgRsrp = LteDestCellItem.RsrpInfo.Avg;
            double destLteCount = LteDestCellItem.RsrpInfo.Count;

            if (srcNrCount <= 0 || destNrCount <= 0)
            {
                HandOverReasonable = "合理";
                return;
            }

            if (srcNrAvgRsrp > -105 && (destNrAvgSinr + 5 < srcNrAvgSinr))
            {
                HandOverReasonable = "切换不合理";
                return;
            }

            if (srcNrAvgRsrp < -103)
            {
                HandOverReasonable = "切换过慢";
                return;
            }
            if (srcNrAvgSinr < -3)
            {
                HandOverReasonable = "切换过慢";
                return;
            }

            if (destNrAvgSinr < 3)
            {
                HandOverReasonable = "切换不合理";
                return;
            }
            if (destNrAvgRsrp <= -93)
            {
                HandOverReasonable = "切换不合理";
                return;
            }

            HandOverReasonable = "合理";

            //if (srcLteCount <= 0 || destLteCount <= 0)
            //{
            //    return;
            //}


        }


        #region 预处理
        public int BeforeArfcn
        {
            get
            {
                return NrSrcCellItem.ARFCN;
            }
        }
        public int BeforePCI
        {
            get
            {
                return NrSrcCellItem.PCI;
            }
        }
        public string BeforeCellName
        {
            get
            {
                return NrSrcCellItem.CellName;
            }
        }
        public string BeforeCellDistance
        {
            get
            {
                return NrSrcCellItem.Distance;
            }
        }
        public string BeforeRsrpAvg
        {
            get
            {
                if (NrSrcCellItem.RsrpInfo.Count > 0)
                {
                    return NrSrcCellItem.RsrpInfo.AvgDesc;
                }
                else
                {
                    return "";
                }
            }
        }
        public string BeforeSinrAvg
        {
            get
            {
                if (NrSrcCellItem.SinrInfo.Count > 0)
                {
                    return NrSrcCellItem.SinrInfo.AvgDesc;
                }
                else
                {
                    return "";
                }
            }
        }
        public string BeforeAppSpeedAvg
        {
            get
            {
                //if (BeforeTpItem.AppSpeedCount > 0)
                //{
                //    return Math.Round(BeforeTpItem.AppSpeed / BeforeTpItem.AppSpeedCount, 2).ToString();
                //}
                //else
                {
                    return "";
                }
            }
        }
        public string BeforePdcpSpeedAvg
        {
            get
            {
                //if (BeforeTpItem.PdcpSpeedCount > 0)
                //{
                //    return Math.Round(BeforeTpItem.PdcpSpeed / BeforeTpItem.PdcpSpeedCount, 2).ToString();
                //}
                //else
                {
                    return "";
                }
            }
        }
        public string BeforeRsrqAvg
        {
            get
            {
                //if (BeforeTpItem.RsrqCount > 0)
                //{
                //    return Math.Round(BeforeTpItem.Rsrq / BeforeTpItem.RsrqCount, 2).ToString();
                //}
                //else
                {
                    return "";
                }
            }
        }
        public string BeforeRssiAvg
        {
            get
            {
                //if (BeforeTpItem.RssiCount > 0)
                //{
                //    return Math.Round(BeforeTpItem.Rssi / BeforeTpItem.RssiCount, 2).ToString();
                //}
                //else
                {
                    return "";
                }
            }
        }

        public int AfterArfcn
        {
            get
            {
                return NrDestCellItem.ARFCN;
            }
        }
        public int AfterPCI
        {
            get
            {
                return NrDestCellItem.PCI;
            }
        }
        public string AfterCellName
        {
            get
            {
                return NrDestCellItem.CellName;
            }
        }
        public string AfterCellDistance
        {
            get
            {
                return NrDestCellItem.Distance;
            }
        }
        public string AfterRsrpAvg
        {
            get
            {
                if (NrDestCellItem.RsrpInfo.Count > 0)
                {
                    return NrDestCellItem.RsrpInfo.AvgDesc;
                }
                else
                {
                    return "";
                }
            }
        }
        public string AfterSrcRsrpAvg
        {
            get
            {
                if (NrDestCellItem.SinrInfo.Count > 0)
                {
                    return NrDestCellItem.SinrInfo.AvgDesc;
                }
                else
                {
                    return "";
                }
            }
        }
        public string AfterSinrAvg
        {
            get
            {
                if (NrDestCellItem.SinrInfo.Count > 0)
                {
                    return NrDestCellItem.SinrInfo.AvgDesc;
                }
                else
                {
                    return "";
                }
            }
        }
        public string AfterAppSpeedAvg
        {
            get
            {
                //if (AfterTpItem.AppSpeedCount > 0)
                //{
                //    return Math.Round(AfterTpItem.AppSpeed / AfterTpItem.AppSpeedCount, 2).ToString();
                //}
                //else
                {
                    return "";
                }
            }
        }
        public string AfterPdcpSpeedAvg
        {
            get
            {
                //if (AfterTpItem.PdcpSpeedCount > 0)
                //{
                //    return Math.Round(AfterTpItem.PdcpSpeed / AfterTpItem.PdcpSpeedCount, 2).ToString();
                //}
                //else
                {
                    return "";
                }
            }
        }
        public string AfterRsrqAvg
        {
            get
            {
                //if (AfterTpItem.RsrqCount > 0)
                //{
                //    return Math.Round(AfterTpItem.Rsrq / AfterTpItem.RsrqCount, 2).ToString();
                //}
                //else
                {
                    return "";
                }
            }
        }
        public string AfterRssiAvg
        {
            get
            {
                //if (AfterTpItem.RssiCount > 0)
                //{
                //    return Math.Round(AfterTpItem.Rssi / AfterTpItem.RssiCount, 2).ToString();
                //}
                //else
                {
                    return "";
                }
            }
        }
        public string GridName
        {
            get
            {
                return GISManager.GetInstance().GetGridDesc(HOEvt.Longitude, HOEvt.Latitude);
            }
        }
        #endregion

        //private string getFreqBand(int arfcn)
        //{
        //    string res = "";
        //    if (IsNREvt)
        //    {
        //        //暂不明确,先不出
        //        //res = NRCell.GetBandTypeByArfcn(arfcn).ToString();
        //    }
        //    else
        //    {
        //        res = LTECell.GetBandTypeByEarfcn_BJ(arfcn).ToString();
        //    }
        //    return res;
        //}

        //private string getAvg(float count, double sum)
        //{
        //    string res = "";
        //    if (count > 0)
        //    {
        //        res = Math.Round(sum / count, 2).ToString();
        //    }
        //    return res;
        //}
    }

    public class NRHandOverAnaCellItem
    {
        public ICell Cell { get; set; }
        public string CellName { get; set; }
        public int ARFCN { get; set; }
        public int PCI { get; set; }
        public string Distance { get; set; }
        public string FreqBand { get; set; }

        public int TPCount { get; set; }
        public DataInfo RsrpInfo { get; set; } = new DataInfo();
        public DataInfo SinrInfo { get; set; } = new DataInfo();
        //只有切换后有这个数据
        public DataInfo SrcRsrpInfo { get; set; } = new DataInfo();

        public void Calculate()
        {
            RsrpInfo.Calculate();
            SinrInfo.Calculate();
            SrcRsrpInfo.Calculate();
            FreqBand = LteCellDataHelper.FreqBand_BeiJing.GetFreqBandByEarfcn(ARFCN);
        }

        public class DataInfo
        {
            public float Sum { get; set; }
            public int Count { get; set; }
            public double Avg { get; set; }
            public string AvgDesc { get; set; } = "";

            public void Calculate()
            {
                if (Count > 0)
                {
                    Avg = Math.Round(Sum / Count, 2);
                    AvgDesc = Avg.ToString();
                }
            }
        }
    }

    public class NRHandOverAnaTpItem
    {
        public float Rsrp { get; set; }
        public float RsrpCount { get; set; }
        public float Sinr { get; set; }
        public float SinrCount { get; set; }
        public float AppSpeed { get; set; }
        public float AppSpeedCount { get; set; }
        public float PdcpSpeed { get; set; }
        public float PdcpSpeedCount { get; set; }
        public float Rsrq { get; set; }
        public float RsrqCount { get; set; }
        public float Rssi { get; set; }
        public float RssiCount { get; set; }
        public float SrcRsrp { get; set; }
        public float SrcRsrpCount { get; set; }
    }

    public class NRHandOverAnaCondition
    {
        public double BeforeSecond { get; set; }           //切换前时长
        public double AfterSecond { get; set; }            //切换后时长
        public int SiteDistance { get; set; }           //同站距离
        public bool IsAnaLte { get; set; }
        //public bool IsBusiness { get; set; }            //只分析业务态 

        public NRHandOverAnaCondition()
        {
            BeforeSecond = 5;
            AfterSecond = 5;
            SiteDistance = 50;
            //IsBusiness = false;
        }
    }
}
