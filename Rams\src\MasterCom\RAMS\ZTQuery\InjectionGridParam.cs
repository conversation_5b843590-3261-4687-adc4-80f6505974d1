using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTQuery
{
    public class InjectionGridParam
    {
        public double ltLong { get; set; }
        public double ltLat { get; set; }
        public double brLong { get; set; }
        public double brLat { get; set; }

        public int timevalue { get; set; }
        public int distLen { get; set; }
        public DateTime DateTimeValue
        {
            get { return JavaDate.GetDateTimeFromMilliseconds(timevalue * 1000L); }
        }
        public int GetWeekOfYear()
        {
            int week = JavaDate.GetWeekIndex(DateTimeValue);
            return week;
        }
        internal static InjectionGridParam FillFrom(MasterCom.RAMS.Net.Content c)
        {
            InjectionGridParam d = new InjectionGridParam();
            d.timevalue = c.GetParamInt();
            d.distLen = c.GetParamInt();
            d.ltLong = c.GetParamDouble(); 
            d.ltLat = c.GetParamDouble();
            d.brLong = c.GetParamDouble();
            d.brLat = c.GetParamDouble();
            return d;
        }
    }
}
