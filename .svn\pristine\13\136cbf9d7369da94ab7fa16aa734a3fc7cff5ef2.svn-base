﻿using MasterCom.RAMS.Func;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NRScanCellWrongDirForm : MinCloseForm
    {
        public NRScanCellWrongDirForm()
            : base(MainModel.GetInstance())
        {
            InitializeComponent();
        }

        public void FillData(List<CellWrongDirItem_NRScan> list)
        {
            foreach (CellWrongDirItem_NRScan item in list)
            {
                item.CalcWrongDir();
            }
            gridControl.DataSource = list;
            gridControl.RefreshDataSource();
        }

        private void miExport_Click(object sender, EventArgs e)
        {
            //SaveFileDialog dlg = new SaveFileDialog();
            //dlg.RestoreDirectory = true;
            //dlg.Filter = FilterHelper.ExcelX;
            //if (dlg.ShowDialog(this) == DialogResult.OK)
            //{
            //    gridControl.ExportToXlsx(dlg.FileName);
            //}
            ExcelNPOIManager.ExportToExcel(gridView);
        }

        private void gridView_DoubleClick(object sender, EventArgs e)
        {
            DevExpress.Utils.DXMouseEventArgs dxEvt = e as DevExpress.Utils.DXMouseEventArgs;
            if (dxEvt == null)
            {
                return;
            }
            DevExpress.XtraGrid.Views.Grid.ViewInfo.GridHitInfo info = gridView.CalcHitInfo(dxEvt.Location);
            if (info == null || !info.InRow)
            {
                return;
            }

            var item = gridView.GetRow(info.RowHandle) as CellWrongDirItem_NRScan;
            MainModel.ClearDTData();
            foreach (TestPoint tp in item.WrongTestPoints)
            {
                MainModel.DTDataManager.Add(tp);
            }
            MainModel.SetSelectedNRCell(item.Cell as NRCell);
            MainModel.RefreshLegend();
            MainModel.FireDTDataChanged(this);
            MainModel.FireSetDefaultMapSerialTheme(NRTpHelper.NrScanTpManager.RsrpFullThemeName);
        }
    }

}
