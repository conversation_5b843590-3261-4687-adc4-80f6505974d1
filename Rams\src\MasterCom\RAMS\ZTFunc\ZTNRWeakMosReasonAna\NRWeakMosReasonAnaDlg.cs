﻿using MasterCom.RAMS.Func;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NRWeakMosReasonAnaDlg : BaseDialog
    {
        public NRWeakMosReasonAnaDlg()
        {
            InitializeComponent();
        }

        NRWeakMosReasonAnaCond cond;

        public void SetCondition(NRWeakMosReasonAnaCond cond)
        {
            if (cond == null)
            {
                cond = new NRWeakMosReasonAnaCond();
                cond.Init();
            }
            numMosWeakGate.Value = (decimal)cond.MosWeakGate;
            numHandoverCountGate.Value = (decimal)cond.HandoverCountGate;
            numWeakRsrpGate.Value = (decimal)cond.WeakRsrpGate;
            numWeakRsrpTpCount.Value = (decimal)cond.WeakRsrpTpCount;
            numWeakSinrGate.Value = (decimal)cond.WeakSinrGate;
            numWeakSinrTpCount.Value = (decimal)cond.WeakSinrTpCount;
            numRtpLostGate.Value = (decimal)cond.RtpLostNumGate;

            checkedListBoxControlReason.Items.Clear();
            foreach (string reason in cond.ReasonList)
            {
                this.checkedListBoxControlReason.Items.Add(reason);
            }
            this.cond = cond;
        }

        public NRWeakMosReasonAnaCond GetCondition()
        {
            cond.MosWeakGate = (float)numMosWeakGate.Value;
            cond.HandoverCountGate = (int)numHandoverCountGate.Value;
            cond.WeakRsrpGate = (float)numWeakRsrpGate.Value;
            cond.WeakRsrpTpCount = (int)numWeakRsrpTpCount.Value;
            cond.WeakSinrGate = (float)numWeakSinrGate.Value;
            cond.WeakSinrTpCount = (int)numWeakSinrTpCount.Value;
            cond.RtpLostNumGate = (int)numRtpLostGate.Value;

            return cond;
        }
        private void listBoxControlReason_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (checkedListBoxControlReason.SelectedItem == null)
            {
                return;
            }

            switch (checkedListBoxControlReason.SelectedItem.ToString())
            {
                case "回落(CSFB)":
                    tbxDescription.Text = "定义：低MOS时间段的呼叫起始事件为CSFB事件（不包含EPSFB）";
                    break;
                case "Esrvcc":
                    tbxDescription.Text = "定义：低MOS时间段前出现Esrvcc事件";
                    break;
                case "RRC重建":
                    tbxDescription.Text = "定义：低MOS时间段内出现RRC重建";
                    break;
                case "频繁切换":
                    tbxDescription.Text = "定义：低MOS时间段内切换事件次数> n";
                    break;
                case "弱覆盖":
                    tbxDescription.Text = "定义：低MOS时间段内连续 n 个采样点的RSRP<= X dBm";
                    break;
                case "质差":
                    tbxDescription.Text = "定义：低MOS时间段内连续 n 个采样点的SINR<= X dB";
                    break;
                case "丢包":
                    tbxDescription.Text = "定义：低MOS时间段内RTP packets Lost number > n \r\n(packets Lost number为低MOS时间段内最大丢包数减去最小丢包数)";
                    break;
                default:
                    tbxDescription.Text = "";
                    break;
            }
        }
    }

    public class NRWeakMosReasonAnaCond : WeakMosReasonAnaCondBase
    {

    }
}
