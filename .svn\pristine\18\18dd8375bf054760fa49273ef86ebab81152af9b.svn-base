﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.Net
{
    public class QueryTestPicInfo : DIYSQLBase
    {
        readonly string fileNameKey;
        readonly List<CqtPictureInfo> cqtPicList = new List<CqtPictureInfo>();
        public QueryTestPicInfo(string fileNameKey)
            : base(MainModel.GetInstance())
        {
            MainDB = true;
            this.fileNameKey = fileNameKey;
        }
        public override string Name
        {
            get { return "查询相关CQT测试照片"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 400, 413, this.Name);
        }
        protected override void query()
        {
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, userName, password, dbid > 0 ? dbid : MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }

                WaitBox.Show("开始查询测试报告信息...", queryInThread, clientProxy);

                showResultForm();
            }
            finally
            {
                clientProxy.Close();
            }
            
        }
        protected override void queryInThread(object o)
        {
            base.queryInThread(o);

            if (cqtPicList.Count > 0)
            {
                DownLoadPic downLoadPic = new DownLoadPic(cqtPicList);
                downLoadPic.Query();
            }

            System.Threading.Thread.Sleep(200);
            WaitBox.Close();
        }
        protected void showResultForm()
        {
            if (cqtPicList.Count == 0)
            {
                System.Windows.Forms.MessageBox.Show("没有符合条件的信息！");
                return;
            }
            TestPictureForm frm = MainModel.CreateResultForm(typeof(TestPictureForm)) as TestPictureForm;
            frm.FillData(cqtPicList);
            frm.Owner = MainModel.MainForm;
            frm.Visible = true;
            frm.BringToFront();
        }
        protected override string getSqlTextString()
        {
            return string.Format(@"select strPicName, picPath from tb_cfg_static_cqt_picture_BJ 
where strPicName like '%{0}%'", fileNameKey);
        }
        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[2];
            rType[0] = E_VType.E_String;
            rType[1] = E_VType.E_String;
            return rType;
        }
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    CqtPictureInfo picInfo = CqtPictureInfo.FillFrom(package.Content);
                    picInfo.SN = cqtPicList.Count + 1;
                    cqtPicList.Add(picInfo);
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL_SUCCESS)
                {
                    //
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL_FAIL)
                {
                    //
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }
    }
    public class DownLoadPic : QueryBase
    {
        readonly List<CqtPictureInfo> cqtPicList;
        public DownLoadPic(List<CqtPictureInfo> cqtPicList)
        {
            this.cqtPicList = cqtPicList;
        }
        public override string Name
        {
            get { return "下载CQT图片"; }
        }
        protected override bool isValidCondition()
        {
            return true;
        }
        protected void prepareSearchPackage(Package package, string filePath)
        {
            package.Command = Command.DataManage;
            package.SubCommand = SubCommand.Request;
            package.Content.Type = RequestType.REQTYPE_FILEDOWNLOADByPath;
            package.Content.PrepareAddParam();
            package.Content.AddParam(filePath);
        }
        protected override void query()
        {
            ClientProxy clientProxy = new ClientProxy();
            clientProxy.setTimeout(30000, 30000);
            if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
            {
                ErrorInfo = "连接服务器失败!";
                return;
            }

            for (int i = 0; i < cqtPicList.Count; i++)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                CqtPictureInfo cqtInfo = cqtPicList[i];

                prepareSearchPackage(clientProxy.Package, cqtInfo.PicFullName);
                clientProxy.Send();
                receiveOneFile(clientProxy, cqtInfo);

                WaitBox.ProgressPercent = ((i + 1) * 100 / cqtPicList.Count);
            }

            clientProxy.Close();
        }

        private void receiveOneFile(ClientProxy clientProxy, CqtPictureInfo cqtInfo)
        {
            string picFullName = cqtInfo.PicFullName;
            MemoryStream stream = null;
            Package package = clientProxy.Package;
            try
            {
                while (true)
                {
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }

                    clientProxy.Recieve();
                    package.Content.PrepareGetParam();
                    if (package.Content.Type == ResponseType.FileBegin || package.Content.Type == ResponseType.FileContinue
                        || package.Content.Type == ResponseType.FileEnd)
                    {
                        bool isEnd = fillData(cqtInfo, ref stream, package);
                        if (isEnd)
                        {
                            break;
                        }
                    }
                    else if (package.Content.Type == ResponseType.FileNotFound)
                    {
                        MessageBox.Show(string.Format("{0}文件下载异常:文件未找到", picFullName));
                        break;
                    }
                    else
                    {
                        MessageBox.Show(string.Format("{0}文件下载异常:下载失败", picFullName));
                        break;
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(string.Format("{0}文件下载异常:{1}", picFullName, ex.Message));
            }
            finally
            {
                if (stream != null)
                {
                    stream.Close();
                }
            }
        }

        private bool fillData(CqtPictureInfo cqtInfo, ref MemoryStream stream, Package package)
        {
            package.Content.GetParamString();//realFileName
            package.Content.GetParamInt();//pNum
            package.Content.GetParamInt();//length
            if (stream == null)
            {
                stream = new MemoryStream();
            }
            stream.Write(package.Content.Buff, package.Content.CurOffset
                , package.Content.Buff.Length - package.Content.CurOffset);
            if (package.Content.Type == ResponseType.FileEnd)
            {
                cqtInfo.PictureImage = System.Drawing.Image.FromStream(stream);
                return true;
            }

            return false;
        }
    }
    public class CqtPictureInfo
    {
        public int SN { get; set; }
        public string PictureName { get; set; }
        public string PicPath { get; set; }
        public string PicFullName 
        {
            get 
            {
                return System.IO.Path.Combine(PicPath, PictureName); 
            }
        }
        public Image PictureImage { get; set; }
        public static CqtPictureInfo FillFrom(MasterCom.RAMS.Net.Content content)
        {
            CqtPictureInfo info = new CqtPictureInfo();
            info.PictureName = content.GetParamString();
            info.PicPath = content.GetParamString();
            return info;
        }
    }
}
