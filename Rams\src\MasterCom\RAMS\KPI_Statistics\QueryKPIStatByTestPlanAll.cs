﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.KPI_Statistics
{
    public class QueryKPIStatByTestPlanAll : QueryKPIStatAll
    {
        List<string> list_TestPlan = null;
        public override string Name
        {
            get { return "按测试计划统计(全部)"; }
        }

        public QueryKPIStatByTestPlanAll(MainModel mainModel)
        {
            this.mainModel = mainModel;
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 11000, 11058, this.Name);
        }

        /// <summary>
        /// 报表统计前的条件设定（测试计划和报表类型）
        /// </summary>
        protected override bool getConditionBeforeQuery()
        {
            DIYQueryFileInfo diyeryFileInfo = new DIYQueryFileInfo(this.mainModel);
            diyeryFileInfo.IsShowFileInfoForm = false;
            diyeryFileInfo.SetQueryCondition(this.condition);
            diyeryFileInfo.Query();
            List<FileInfo> fileInfos = mainModel.FileInfos;
            List<string> testPlans = new List<string>();
            foreach (FileInfo file in fileInfos)
            {
                if (file.TestPlanName != null && !testPlans.Contains(file.TestPlanName))
                    testPlans.Add(file.TestPlanName);

            }
            mainModel.FileInfos.Clear();                                                      //移除fileinfos，避免文件列表加载数据
            if (testPlans.Count <= 0)
            {
                System.Windows.Forms.MessageBox.Show("无测试计划，请重新设定条件！");
                return false;
            }            
            TestPlanReportPickerDlg dlg = new TestPlanReportPickerDlg(testPlans);            
            if (dlg.ShowDialog() != System.Windows.Forms.DialogResult.OK)
            {
                return false;
            }
            curReportStyle = dlg.Report;
            isQueryAllParams = dlg.IsQueryAllParams;
            list_TestPlan = dlg.list_TestPlan;
            KpiDataManager = new KPIDataManager();
            return true;
        }

        protected override void recieveAndHandleSpecificStatData(Net.Package package, List<Model.Interface.StatImgDefItem> curImgColumnDef, KPIStatDataBase singleStatData)
        {
            fillStatData(package, curImgColumnDef, singleStatData);
            int fileID = (int)singleStatData[KPIStatDataBase.FileIDKey, KPIStatDataBase.NewGridFileIDKey, -1];
            MasterCom.RAMS.Model.FileInfo fi = DTDataHeaderManager.GetInstance().GetHeaderByFileID(fileID);

            if (!string.IsNullOrEmpty(fi.TestPlanName) && list_TestPlan.Contains(fi.TestPlanName))
            {
                KpiDataManager.AddStatData(string.Empty, fi.TestPlanName, fi, singleStatData, this.curReportStyle != null && this.curReportStyle.HasGridPerCell);
            }
        }

        protected override void handleStatEvent(Model.Event evt)
        {
            StatDataEvent eventData = new StatDataEvent(evt, needSeparateByServiceID(evt), needSeparateByFileName(evt));
            FileInfo fi = DTDataHeaderManager.GetInstance().GetHeaderByFileID(evt.FileID);

            if (!string.IsNullOrEmpty(fi.TestPlanName) && list_TestPlan.Contains(fi.TestPlanName))
            {
                KpiDataManager.AddStatData(string.Empty, fi.TestPlanName, fi, eventData
                    , this.curReportStyle != null && this.curReportStyle.HasGridPerCell);
            }
        }
    }
}
