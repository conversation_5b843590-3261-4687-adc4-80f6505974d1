﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using System.Collections;
using System.Data;
using MasterCom.Util;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc
{
    public class QueryCoverageByRegion : QueryBase
    {
        private readonly List<CoverageAreaInfo> CoverInfoList;
        protected string xlsFilePath = null;
        protected int ColumnCounts = 3;
        private readonly List<BtsLongitudeLatitude> btsLongLatList;
        protected bool ShowRadius = true;
        protected int Radius = 0;

        public QueryCoverageByRegion(MainModel mainModel)
            : base(mainModel)
        {
            btsLongLatList = new List<BtsLongitudeLatitude>();
            CoverInfoList = new List<CoverageAreaInfo>();
        }

        public override string Name
        {
            get { return "规划站普查"; }
        }

        public override string IconName
        {
            get { return ""; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 18000, 18017, this.Name);
        }

        protected override bool isValidCondition()
        {
            return true;
        }

        protected virtual void clearData()
        {
            btsLongLatList.Clear();
            CoverInfoList.Clear();
        }

        protected virtual bool ReadXLS(ref DataTable datatable)
        {
            ReadLongLatFromXlsPlatForm form = new ReadLongLatFromXlsPlatForm(ShowRadius);
            form.SetFilePath(xlsFilePath);
            if (form.ShowDialog() != System.Windows.Forms.DialogResult.OK) return false;
            xlsFilePath = form.FilePath;
            Radius = form.Radius;
            if (!System.IO.File.Exists(xlsFilePath))
            {
                System.Windows.Forms.MessageBox.Show("此xls文件不存在", "提示");
                return false;
            }

            ArrayList sheets = ExcelTools.GetSheetNameList(xlsFilePath);

            foreach (string item in sheets)
            {
                DataTable table = ExcelTools.ExcelToDataTable(xlsFilePath, item);
                if (table.Rows.Count > 0)
                {
                    if (table.Columns.Count < ColumnCounts)
                    {
                        System.Windows.Forms.MessageBox.Show("sheet  " + item + "缺少字段");
                    }
                    else if (table.Columns.Count > ColumnCounts)
                    {
                        System.Windows.Forms.MessageBox.Show("sheet  " + item + "过多字段");
                    }
                    else
                    {
                        datatable.Merge(table);
                    }
                }
            }
            return true;
        }

        protected override void query()
        {
            clearData();
            DataTable datatable = new DataTable();
            if (!ReadXLS(ref datatable)) return;
            
            if (datatable.Rows.Count > 0)
            {
                getData(datatable);
            }
            search();
            fireShowForm();
        }

        protected virtual void getData(DataTable datatable)
        {
            foreach (DataRow dr in datatable.Rows)
            {
                if (isValidLongitude(dr[1].ToString()) && isValidLatitude(dr[2].ToString()))
                {
                    btsLongLatList.Add(new BtsLongitudeLatitude(dr));
                }
            }
        }

        private bool isValidLongitude(string longitude)
        {
            double result;
            if (!double.TryParse(longitude, out result)
                || result < 10 || result > 200)
            {
                return false;
            }
            return true;
        }

        private bool isValidLatitude(string latitude)
        {
            double result;
            if (!double.TryParse(latitude, out result)
                || result < 10 || result > 60)
            {
                return false;
            }
            return true;
        }

        protected virtual void search()
        {
            foreach (BtsLongitudeLatitude btsLongLat in btsLongLatList)
            {
                MapWinGIS.Shape circle = MainModel.MainForm.GetMapForm().DrawCircle(
                    new DbPoint(btsLongLat.Longitude, btsLongLat.Latitude),
                    Radius * 0.00001);
                this.Condition.Geometorys.Region = circle;
                QueryCoverageDetailInfoByRegion covQuery = new QueryCoverageDetailInfoByRegion(
                    MainModel, btsLongLat.BTSName, btsLongLat.Longitude, btsLongLat.Latitude);
                covQuery.SetQueryCondition(this.Condition);
                covQuery.Query();
                CoverInfoList.Add(covQuery.CovAreaInfo);
            }
        }

        protected virtual void fireShowForm()
        {
            object obj = MainModel.GetObjectFromBlackboard(typeof(CoverageAreaInfoForm).FullName);
            CoverageAreaInfoForm form = obj == null ? new CoverageAreaInfoForm(MainModel, Radius) : obj as CoverageAreaInfoForm;
            if (!form.Visible)
            {
                form.Show(MainModel.MainForm);
            }
            form.FillData(CoverInfoList);
        }
    }

    public class BtsLongitudeLatitude
    {
        public string BTSName { get; set; }
        public double Longitude { get; set; }
        public double Latitude { get; set; }

        public BtsLongitudeLatitude(DataRow dr)
        {
            int idx = 0;
            BTSName = Convert.ToString(dr[idx++]);
            Longitude = Convert.ToDouble(dr[idx++]);
            Latitude = Convert.ToDouble(dr[idx]);
        }
    }
}
