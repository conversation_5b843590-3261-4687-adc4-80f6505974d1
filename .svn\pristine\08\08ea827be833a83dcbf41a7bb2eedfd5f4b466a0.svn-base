﻿using MasterCom.MTGis;
using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Text;

namespace MasterCom.RAMS.Func
{
    public class CellGridDetailLayer : LayerBase
    {
        public CellGridDetailLayer()
            : base("小区栅格图层")
        {

        }

        SolidBrush brush { get; set; } = new SolidBrush(Color.FromArgb(200, Color.Orange));
        public CellGridCondition conn { get; set; }
        public List<CellGridWithCell> cgw { get; set; } = new List<CellGridWithCell>();

        public override void Draw(Rectangle clientRect, Rectangle updateRect, Graphics graphics)
        {
            if (!IsVisible || cgw.Count == 0)
            {
                return;
            }

            Rectangle inflatedRect = new Rectangle(updateRect.X, updateRect.Y, updateRect.Width, updateRect.Height);
            DbRect dRect;
            this.gisAdapter.FromDisplay(inflatedRect, out dRect);

            foreach (CellGridWithCell cg in cgw)
            {
                foreach (CellGridDetailInfo detail in cg.CellGridDetails)
                {
                    Color cl = getColor(detail);
                    drawGridInRegion(graphics, dRect, detail, cl);
                }
            }
        }

        private Color getColor(CellGridDetailInfo detail)
        {
            Color cl = Color.Orange;
            setColorByRsrp(detail.AvgRSRP, -110, -141, ref cl, conn.RSRP141);
            setColorByRsrp(detail.AvgRSRP, -105, -110, ref cl, conn.RSRP110);
            setColorByRsrp(detail.AvgRSRP, -100, -105, ref cl, conn.RSRP105);
            setColorByRsrp(detail.AvgRSRP, -95, -100, ref cl, conn.RSRP100);
            setColorByRsrp(detail.AvgRSRP, -80, -95, ref cl, conn.RSRP95);
            setColorByRsrp(detail.AvgRSRP, -75, -80, ref cl, conn.RSRP80);
            setColorByRsrp(detail.AvgRSRP, -40, -75, ref cl, conn.RSRP75);
            setColorByRsrp(detail.AvgRSRP, 25, -40, ref cl, conn.RSRP40);
            return cl;
        }

        private void setColorByRsrp(double rsrp, int max, int min, ref Color color, Color conColor)
        {
            if (rsrp < max && rsrp >= min)
            {
                color = conColor;
            }
        }

        private void drawGridInRegion(Graphics graphics, DbRect dRect, CellGridDetailInfo detail, Color cl)
        {
            bool isnext = true;
            foreach (LabColorText lct in conn.LbList)
            {
                if (cl == lct.color && !lct.isCheck)
                {
                    isnext = false;
                    break;
                }
            }
            if (isnext)
            {
                if (detail.AvgRSRP == 0.0)
                {
                    cl = Color.Black;
                }
                brush = new SolidBrush(Color.FromArgb(200, cl));
                if (dRect.Within(detail.Bounds))
                {
                    RectangleF rect;
                    this.gisAdapter.ToDisplay(detail.Bounds, out rect);
                    graphics.FillRectangle(brush, rect);
                    PointF gridCenter = new PointF(rect.X + rect.Width / 2, rect.Y + rect.Height / 2);
                    DbPoint cellPt = new DbPoint(detail.lteCell.EndPointLongitude, detail.lteCell.EndPointLatitude);
                    PointF cellPf;
                    gisAdapter.ToDisplay(cellPt, out cellPf);
                    graphics.DrawLine(Pens.Orange, cellPf, gridCenter);
                    Rectangle re = Rectangle.Ceiling(rect);
                    graphics.DrawRectangle(Pens.Black, re);
                }
            }
        }
    }
}
