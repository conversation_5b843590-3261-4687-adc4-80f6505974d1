﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;

using MasterCom.NOP.RAMS.Extend.Command;
using MasterCom.NOP.Report;
using MasterCom.NOP.DataSet;
using MasterCom.NOP.WF;
using MasterCom.NOP.WF.Core;

namespace MasterCom.RAMS.NOP
{
    public class TaskOrderQuerier
    {
        public TaskOrderQuerier(MainModel mainModel)
        {
            this.mainModel = mainModel;
        }

        public ICollection<Task> Query()
        {
            return Query(null);
        }

        public ICollection<Task> Query(DateTime sTime, DateTime eTime)
        {
            Dictionary<int, object> conditionDic = new Dictionary<int, object>();
            conditionDic.Add(timePeriodFieldID, new global::Chris.Util.TimePeriod(sTime, eTime));
            return Query(conditionDic);
        }

        public Schema QuerySchema()
        {
            string hostname = NopCfgMngr.Instance.NopServer.Ip;
            int port = NopCfgMngr.Instance.NopServer.Port;
            string username = this.mainModel.User.LoginName;
            string password = NopCfgMngr.Instance.Password;

            DataSetNetAdapter adapter = new DataSetNetAdapter(hostname, port, username, password);
            return adapter.GetSchemaByID(schemaID);
        }

        private ICollection<Task> Query(Dictionary<int, object> conditionDic)
        {
            string hostname = NopCfgMngr.Instance.NopServer.Ip;
            int port = NopCfgMngr.Instance.NopServer.Port;
            string username = this.mainModel.User.LoginName;
            string password = NopCfgMngr.Instance.Password;

            WFNetAdapter adapter = new WFNetAdapter(hostname, port, username, password);
            WorkFlowVersion workFlowVersion = adapter.QueryWorkFlowVersion(workFlowVersionID);
            ICollection<Task> tasks = adapter.QueryTasks(workFlowVersion, conditionDic, 0, null, false);
            return tasks;
        }

        protected int schemaID = 1;

        protected int workFlowVersionID = 9;

        protected int timePeriodFieldID = 1030;

        protected MainModel mainModel;
    }
}