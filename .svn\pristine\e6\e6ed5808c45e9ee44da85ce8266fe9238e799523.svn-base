﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.Model
{
    public abstract class FreaBandBase
    {
        protected List<FreqBandRange> freqBandRangeList;
        public virtual void InitFreqBand()
        {
            freqBandRangeList = new List<FreqBandRange>();
        }

        public virtual string GetFreqBandByEarfcn(int earfcn)
        {
            if (earfcn == 0)
            {
                return "";
            }

            foreach (var freqBandRange in freqBandRangeList)
            {
                if (freqBandRange.Judge(earfcn))
                {
                    return freqBandRange.BandName;
                }
            }

            return "";
        }

        protected class FreqBandRange
        {
            public string BandName { get; set; }
            public List<Range> RangeList { get; set; } = new List<Range>();

            public bool Judge(int earfcn)
            {
                foreach (var range in RangeList)
                {
                    if (range.Min <= earfcn && earfcn <= range.Max)
                    {
                        return true;
                    }
                }
                return false;
            }
        }

        protected class Range
        {
            public int Max { get; set; }
            public int Min { get; set; }
        }
    }

    public class FreqBand_BJ : FreaBandBase
    {
        public FreqBand_BJ()
        {
            InitFreqBand();
        }

        /*
         * A频段（Band34）：
         * D频段（Band38）：
         * E频段（Band40）：
         * F频段（Band39）：
         * FDD900频段（Band）：
         * FDD1800频段（Band）：
         * 
         * 5G频段中，中国移动（2.6GHz+4.9GHz），中国联通（3.5GHz），中国电信（3.5GHz），中国广电（700MHz）
         * 中国移动：
         * n41频段（Band41，TDD）：（2515-2675MHz，160MHz）（部署：4G+5G）
         * n79频段（Band79，TDD）：（4800-4900MHz，100MHz）（部署：5G）
         * 
         * 中国联通：
         * n77频段（Band77，TDD）：（3300-3400MHz，100MHz）（部署：5G）（联通+电信+广电，室内覆盖）
         * n78频段（Band78，TDD）：（3500-3600MHz，100MHz）（部署：5G）
         * 
         * 中国电信：
         * n77频段（Band77，TDD）：（3300-3400MHz，100MHz）（部署：5G）（联通+电信+广电，室内覆盖）
         * n78频段（Band78，TDD）：（3400-3500MHz，100MHz）（部署：5G）
         * 
         * 中国广电：
         * n28频段（Band28，FDD）：（上行：703-733MHz，下行：758-788MHz，30MHz）（部署：4G+5G）
         * n77频段（Band77，TDD）：（3300-3400MHz，100MHz）（部署：5G）（联通+电信+广电，室内覆盖）
         * n79频段（Band79，TDD）：（4900-4960MHz，60MHz）（部署：5G）
         */

        public override void InitFreqBand()
        {
            freqBandRangeList = new List<FreqBandRange>
            {
                new FreqBandRange{
                    BandName = "A",
                    RangeList = new List<Range>()
                    {
                        new Range() {  Min = 36200, Max = 36349 }
                    }
                },
                new FreqBandRange{
                    BandName = "D",
                    RangeList = new List<Range>()
                    {
                        new Range() {  Min = 37750, Max = 38249 },
                        new Range() {  Min = 39650, Max = 41589 }
                    }
                },
                new FreqBandRange{
                    BandName = "E",
                    RangeList = new List<Range>()
                    {
                        new Range() {  Min = 38650, Max = 39649 }
                    }
                },
                new FreqBandRange{
                    BandName = "F",
                    RangeList = new List<Range>()
                    {
                        new Range() {  Min = 38250, Max = 38649 }
                    }
                },
                new FreqBandRange{
                    BandName = "FDD900",
                    RangeList = new List<Range>()
                    {
                        new Range() {  Min = 21450, Max = 21799 },
                        new Range() {  Min = 3450, Max = 3799 }
                    }
                },
                new FreqBandRange{
                    BandName = "FDD1800",
                    RangeList = new List<Range>()
                    {
                        new Range() {  Min = 19200, Max = 19949 },
                        new Range() {  Min = 1200, Max = 1949 }
                    }
                },
                new FreqBandRange{
                    BandName = "n28",
                    RangeList = new List<Range>()
                    {
                        new Range() {  Min = 140600, Max = 149600 },  //(703MHz~748MHz)
                        new Range() {  Min = 151600, Max = 160600 }   //(758MHz~803MHz)
                    }
                },
                new FreqBandRange{
                    BandName = "n41",
                    RangeList = new List<Range>()
                    {
                        new Range() {  Min = 499200, Max = 537999 }  //(2496MHz~2690MHz)
                    }
                },
                new FreqBandRange{
                    BandName = "n77",
                    RangeList = new List<Range>()
                    {
                        new Range() {  Min = 620000, Max = 626666 }  //(3300MHz~3400MHz)
                    }
                },
                new FreqBandRange{
                    BandName = "n78",
                    RangeList = new List<Range>()
                    {
                        new Range() {  Min = 626667, Max = 640000 }  //(3400MHz~3600MHz)
                    }
                },
                new FreqBandRange{
                    BandName = "n79",
                    RangeList = new List<Range>()
                    {
                        new Range() {  Min = 693333, Max = 733333 }  //(4400MHz~5000MHz)
                    }
                }
            };
        }
    }
}
