﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
   public class TDScore
    {
       //ID
        public int Id { get; set; }
        /// <summary>
        ///  类别
        /// </summary>
        public string Strtype { get; set; }
        /// <summary>
        /// 方式
        /// </summary>
        public string Strmode { get; set; }
        /// <summary>
        /// 项目
        /// </summary>
        public string Strproject { get; set; }
        /// <summary>
        /// 门限
        /// </summary>
        public decimal Fthreshold { get; set; }
        /// <summary>
        /// 满分指标
        /// </summary>
        public decimal Foutindicators { get; set; }
        /// <summary>
        /// 0分指标
        /// </summary>
        public decimal F0indicators { get; set; }
        /// <summary>
        /// 总分分值
        /// </summary>
        public int Itotalscore { get; set; }
        /// <summary>
        /// 门限指标分
        /// </summary>
        public int Ithresholdscore { get; set; }
        /// <summary>
        /// 实际指标
        /// </summary>
        public decimal Ftrueindicators { get; set; }
        /// <summary>
        /// 得分
        /// </summary>
        public decimal Score
        {
            get
            {
                return getScore();
            }
        }

        private decimal getScore()
        {
            if (F0indicators < Foutindicators)
            {
                return getHeigherScore();
            }
            else if (F0indicators > Foutindicators)
            {
                return getLowerScore();
            }
            else
            {
                return 0;
            }
        }

        private decimal getHeigherScore()
        {
            if (Ftrueindicators > 0 && Ftrueindicators > Fthreshold)//大于门限指标返回得分
            {

                return Ithresholdscore + (Ftrueindicators - Fthreshold) / (Foutindicators - Fthreshold) * (Itotalscore - Ithresholdscore);
            }
            else if (Ftrueindicators > 0 && Ftrueindicators > F0indicators && Ftrueindicators < Fthreshold)
            {
                return Ithresholdscore + (Ftrueindicators - Fthreshold) / (Fthreshold - F0indicators) * (Ithresholdscore - 0);
            }
            else if (Ftrueindicators > Foutindicators)//大于满分指标返回满分
            {
                return Foutindicators;
            }
            else
            {
                return 0;
            }
        }

        private decimal getLowerScore()
        {
            if (Ftrueindicators > 0 && Ftrueindicators > Fthreshold)//大于门限指标返回得分
            {

                return Ithresholdscore - (Ftrueindicators - Fthreshold) / (F0indicators - Fthreshold) * (Itotalscore - Ithresholdscore);
            }
            else if (Ftrueindicators > 0 && Ftrueindicators > F0indicators && Ftrueindicators < Fthreshold)
            {
                return Ithresholdscore - (Fthreshold - Ftrueindicators) / (Fthreshold - Foutindicators) * (Ithresholdscore - 0);
            }
            else if (Ftrueindicators > Foutindicators)//大于满分指标返回满分
            {
                return Foutindicators;
            }
            else
            {
                return 0;
            }
        }
    }
}
