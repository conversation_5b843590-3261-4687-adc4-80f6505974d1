﻿using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;
using System.Xml;

namespace MasterCom.RAMS.ZTFunc
{
    public class NR700StationSettingDlgConfig_XJ : ConfigHelper<NR700StationSettingDlgConfigModel_XJ>
    {
        private static NR700StationSettingDlgConfig_XJ instance = null;
        public static NR700StationSettingDlgConfig_XJ Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new NR700StationSettingDlgConfig_XJ();
                }
                return instance;
            }
        }

        public override string ConfigPath { get; } = $@"{AppDomain.CurrentDomain.BaseDirectory}\config\StationDlgConfig\NR700MStationDlg.xml";

        public override string LogPath { get; } = @"\BackGroundLog\StationDlgConfig\";
        public override string LogName { get; } = "-5G700M单验门限设置.txt";

        protected override void loadConfig(XmlConfigFile xcfg, NR700StationSettingDlgConfigModel_XJ configInfo)
        {
            try
            {
                XmlElement config = xcfg.GetConfig("Configs");
                configInfo.AccessSuccessRate = getValidData(xcfg, config, "AccessSuccessRate", "");
                configInfo.BigPingTimeDelay = getValidData(xcfg, config, "BigPingTimeDelay", "");
                configInfo.SmallPingTimeDelay = getValidData(xcfg, config, "SmallPingTimeDelay", "");
                configInfo.DownTestRSRP = getValidData(xcfg, config, "DownTestRSRP", "");
                configInfo.DownTestAvgSINR = getValidData(xcfg, config, "DownTestAvgSINR", "");
                configInfo.DownThroughput = getValidData(xcfg, config, "DownThroughput", "");
                configInfo.UploadTestRSRP = getValidData(xcfg, config, "UploadTestRSRP", "");
                configInfo.UploadAvgSINR = getValidData(xcfg, config, "UploadAvgSINR", "");
                configInfo.UploadThroughput = getValidData(xcfg, config, "UploadThroughput", "");
                configInfo.CallSuccessRate = getValidData(xcfg, config, "CallSuccessRate", "");
                configInfo.CallSuccessRate4G = getValidData(xcfg, config, "CallSuccessRate4G", "");
                configInfo.FRSuccessRate5G = getValidData(xcfg, config, "FRSuccessRate5G", "");
                configInfo.FRSuccessRate4G = getValidData(xcfg, config, "FRSuccessRate4G", "");
                configInfo.StationInSwitch = getValidData(xcfg, config, "StationInSwitch", "");
                configInfo.StationBtwSwitch = getValidData(xcfg, config, "StationBtwSwitch", "");
            }
            catch (Exception ex)
            {
                ErrMsg = $"加载配置出错:{ex.Message}";
            }
        }

        public override void SaveConfig(NR700StationSettingDlgConfigModel_XJ configInfo)
        {
            try
            {
                var newConfig = new XmlConfigFile();
                XmlElement cfg = newConfig.AddConfig("Configs");
                newConfig.AddItem(cfg, "AccessSuccessRate", configInfo.AccessSuccessRate);
                newConfig.AddItem(cfg, "BigPingTimeDelay", configInfo.BigPingTimeDelay);
                newConfig.AddItem(cfg, "SmallPingTimeDelay", configInfo.SmallPingTimeDelay);
                newConfig.AddItem(cfg, "DownTestRSRP", configInfo.DownTestRSRP);
                newConfig.AddItem(cfg, "DownTestAvgSINR", configInfo.DownTestAvgSINR);
                newConfig.AddItem(cfg, "DownThroughput", configInfo.DownThroughput);
                newConfig.AddItem(cfg, "UploadTestRSRP", configInfo.UploadTestRSRP);
                newConfig.AddItem(cfg, "UploadAvgSINR", configInfo.UploadAvgSINR);
                newConfig.AddItem(cfg, "UploadThroughput", configInfo.UploadThroughput);
                newConfig.AddItem(cfg, "CallSuccessRate", configInfo.CallSuccessRate);
                newConfig.AddItem(cfg, "CallSuccessRate4G", configInfo.CallSuccessRate4G);
                newConfig.AddItem(cfg, "FRSuccessRate5G", configInfo.FRSuccessRate5G);
                newConfig.AddItem(cfg, "FRSuccessRate4G", configInfo.FRSuccessRate4G);
                newConfig.AddItem(cfg, "StationInSwitch", configInfo.StationInSwitch);
                newConfig.AddItem(cfg, "StationBtwSwitch", configInfo.StationBtwSwitch);
                newConfig.Save(ConfigPath);
            }
            catch (Exception ex)
            {
                ErrMsg = $"保存配置出错:{ex.Message}";
            }
        }
    }

    public class NR700StationSettingDlgConfigModel_XJ : ConfigDataInfo
    {
        public string AccessSuccessRate { get; set; }

        public string BigPingTimeDelay { get; set; }

        public string SmallPingTimeDelay { get; set; }

        public string DownTestRSRP { get; set; }

        public string DownTestAvgSINR { get; set; }

        public string DownThroughput { get; set; }

        public string UploadTestRSRP { get; set; }

        public string UploadAvgSINR { get; set; }

        public string UploadThroughput { get; set; }

        public string CallSuccessRate { get; set; }

        public string CallSuccessRate4G { get; set; }

        public string FRSuccessRate5G { get; set; }

        public string FRSuccessRate4G { get; set; }

        public string StationInSwitch { get; set; }

        public string StationBtwSwitch { get; set; }
    }
}
