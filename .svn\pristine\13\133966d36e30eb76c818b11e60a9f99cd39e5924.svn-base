﻿using MasterCom.RAMS.Func.SystemSetting;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.IO;
using System.Text;
using System.Xml;

namespace MasterCom.RAMS.BackgroundFunc
{
    public class LowTaskFileManage : DIYAnalyseByCellBackgroundBaseByFile
    {
        public TaskOrderManageCondition OrderCondition { get; set; }//本功能的配置信息
        bool isValidSetting = false;//本功能的配置是否有效

        Dictionary<string, TaskOrderManageInfo> orderFileDic;//工单号对应的工单
        TaskOrderManageInfo curTaskOrder;//当前分析的工单
        TaskOrderManageResult curTaskOrderResult;//当前分析工单返回给亿阳的结果
        TaskOrderFailAnalyseFile curTaskOrderFailAnalyseFiles;//当前分析工单中解析异常的文件(文件 statstatus 为0)
        
        CarrierResultInfo orderCarrierResult;
        CarrierRenderInfo carrierRenderInfo;

        protected static readonly object lockObj = new object();
        private static LowTaskFileManage intance;
        public static LowTaskFileManage GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new LowTaskFileManage();
                    }
                }
            }
            return intance;
        }

        protected LowTaskFileManage()
            : base(MainModel.GetInstance())
        {
            if (intance != null)
            {
                return;
            }
            isIgnoreExport = true;
            OrderCondition = new TaskOrderManageCondition();
        }

        public override string Name
        {
            get
            {
                return "亿阳工单信息管理";
            }
        }

        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 18000, 18049, "查询");
        }

        public override void DealBeforeBackgroundQueryByCity()
        {
            //在进行功能前仅做一次的操作,读取配置和Excel
            isValidSetting = judgeValidCondition();

            BackgroundFuncManager.GetInstance().ReportBackgroundInfo("开始读取亿阳推送工单Excel");
            getOrderFromExcel();
        }

        #region 获取基础条件
        private bool judgeValidCondition()
        {
            bool isValid = true;
            List<string> pathList = new List<string>() { OrderCondition.ExcelPath, OrderCondition.ImportedExcelPath, OrderCondition.PicPath };
            foreach (string path in pathList)
            {
                isValid = judgeValidPath(path);
                if (!isValid)
                {
                    BackgroundFuncManager.GetInstance().ReportBackgroundInfo("条件路径有空值");
                    return false;
                }
            }

            List<string> urlList = new List<string>() { OrderCondition.WebServicePath, OrderCondition.AttachPath };
            foreach (string url in urlList)
            {
                if (string.IsNullOrEmpty(url))
                {
                    BackgroundFuncManager.GetInstance().ReportBackgroundInfo("条件地址有空值");
                    return false;
                }
            }
            return true;
        }

        private bool judgeValidPath(string path)
        {
            if (path != null && Directory.Exists(path))
            {
                return true;
            }
            return false;
        }

        protected override bool getCondition()
        {
            FilterSampleByRegion = false;
            IncludeEvent = false;
            orderFileDic = new Dictionary<string, TaskOrderManageInfo>();
            this.Columns = new List<string>();
            Columns.Add("RxLevSub");
            Columns.Add("RxQualSub");
            Columns.Add("GSCAN_RxLev");
            Columns.Add("GSCAN_RxQual");
            Columns.Add("lte_RSRP");
            Columns.Add("lte_SINR");
            Columns.Add("LTESCAN_TopN_CELL_Specific_RSRP");
            Columns.Add("LTESCAN_TopN_CELL_Specific_RSSINR");
            return isValidSetting;
        }
        #endregion

        #region 读取新工单Excel并入库
        private void getOrderFromExcel()
        {
            //查找可用的Excel
            List<string> validExcelFiles = getValidExcelFiles(OrderCondition.ExcelPath);
            if (validExcelFiles.Count <= 0)
            {
                return;
            }
            BackgroundFuncManager.GetInstance().ReportBackgroundInfo(string.Format("存在工单文件{0}个", validExcelFiles.Count));
            //对工单Excel进行处理,读取到内存并搬移文件
            List<string> readSuccessFileList;
            Dictionary<string, TaskOrderManageInfo> curOrderFileDic = DealWithExcelFiles(validExcelFiles, out readSuccessFileList);

            //工单信息插入到数据库
            BackgroundFuncManager.GetInstance().ReportBackgroundInfo($"存在有效的工单[{curOrderFileDic.Count}]个");
            bool isSuccess = insertToTB(curOrderFileDic);
            //DiyInsertTaskOrderInfo query = new DiyInsertTaskOrderInfo(curOrderFileDic);
            //query.Query();

            if (isSuccess)
            {
                BackgroundFuncManager.GetInstance().ReportBackgroundInfo("工单数据入库成功!开始搬移文件...");
                foreach (string excelFile in readSuccessFileList)
                {
                    MoveFile(excelFile, OrderCondition.ImportedExcelPath);
                }
            }
            else
            {
                //BackgroundFuncManager.GetInstance().ReportBackgroundInfo(query.ErrorInfo);
                foreach (string excelFile in readSuccessFileList)
                {
                    MoveFileWithCreate(excelFile, OrderCondition.ImportedExcelPath + "//UploadError");
                }
            }
        }

        #region
        private bool insertToTB(Dictionary<string, TaskOrderManageInfo> curOrderFileDic)
        {
            string strsql = getSqlTextString(curOrderFileDic);
            List<string> sqlList = getSqlList(strsql);

            try
            {
                DiySqlNonQueryMainDB queryNon;
                foreach (var sql in sqlList)
                {
                    queryNon = new DiySqlNonQueryMainDB(mainModel, sql);
                    queryNon.Query();
                    System.Threading.Thread.Sleep(MainModel.SystemConfigInfo.MultiSqlDelayMs);
                }
                return true;
            }
            catch (Exception e)
            {
                BackgroundFuncManager.GetInstance().ReportBackgroundInfo($"入库出错{e.Message}");
                return false;
            }
        }

        private string getSqlTextString(Dictionary<string, TaskOrderManageInfo> curOrderFileDic)
        {
            StringBuilder strb = new StringBuilder();
            foreach (TaskOrderManageInfo taskOrder in curOrderFileDic.Values)
            {
                strb.AppendFormat(@"IF EXISTS(SELECT 1 FROM [tb_lowtask_file] where [工单号] = '{0}') begin DELETE FROM [tb_lowtask_file] where [工单号] = '{0}' DELETE FROM [tb_lowtask_result] where [工单号] = '{0}' end ",
                    taskOrder.OrderID);
                foreach (var orderFile in taskOrder.OrderFileList)
                {
                    strb.AppendFormat(@"insert into [tb_lowtask_file] ([地市],[工单号],[开始时间],[结束时间],[文件名],[导入时间]) values ('{0}','{1}','{2}','{3}','{4}','{5}');",
                        orderFile.Area, taskOrder.OrderID, taskOrder.StartDateTime, taskOrder.EndDateTime, orderFile.FileName, taskOrder.ImportDataTime);
                }
            }
            return strb.ToString();
        }

        private List<string> getSqlList(string strsql)
        {
            List<string> sqlList = new List<string>();

            string[] strArr = strsql.Split(new char[] { ';' }, StringSplitOptions.RemoveEmptyEntries);
            StringBuilder strb = new StringBuilder();
            int curIdx = 0;
            for (; curIdx < strArr.Length; curIdx++)
            {
                string curStr = strArr[curIdx];
                int bufferLength = Encoding.Default.GetBytes(strb + curStr).Length;
                if (bufferLength > 7000)
                {
                    sqlList.Add(strb.ToString());
                    strb = new StringBuilder();
                    //strb.Append(curStr + ";");
                    //continue;
                }
                strb.Append(curStr + ";");
            }
            if (strb.Length > 0)
            {
                sqlList.Add(strb.ToString());
            }
            return sqlList;
        }
        #endregion


        private List<string> getValidExcelFiles(string directoryPath)
        {
            if (!Directory.Exists(directoryPath))
            {
                BackgroundFuncManager.GetInstance().ReportBackgroundInfo(directoryPath + "不存在！");
                return new List<string>();
            }

            List<string> vaildExcelFileList = new List<string>();
            string[] dirExcelFiles = Directory.GetFiles(directoryPath, "*.xls?");
            foreach (string fName in dirExcelFiles)
            {
                vaildExcelFileList.Add(fName);
            }
            return vaildExcelFileList;
        }

        private Dictionary<string, TaskOrderManageInfo> DealWithExcelFiles(List<string> validExcelFiles, out List<string> readSuccessFileList)
        {
            Dictionary<string, TaskOrderManageInfo> orderFileDicTmp = new Dictionary<string, TaskOrderManageInfo>();
            readSuccessFileList = new List<string>();
            foreach (string excelFile in validExcelFiles)
            {
                Dictionary<string, TaskOrderManageInfo> curOrderFileDic;
                bool readSuccess = readOneExcelFile(out curOrderFileDic, excelFile);
                if (readSuccess)
                {
                    foreach (var item in curOrderFileDic)
                    {
                        string orderID = item.Key;
                        if (!orderFileDicTmp.ContainsKey(orderID))
                        {
                            orderFileDicTmp.Add(orderID, item.Value);
                        }
                    }
                    //读取成功的文件先记录不搬移
                    readSuccessFileList.Add(excelFile);
                }
                else
                {
                    //搬移到对应路径下的Error目录
                    MoveFileWithCreate(excelFile, OrderCondition.ImportedExcelPath + "//Error");
                }
            }
            return orderFileDicTmp;
        }

        private void MoveFile(string sourceFileFullPath, string destFilePath)
        {
            string destFileFullPath = getDestFileFullPath(sourceFileFullPath, destFilePath);
            if (File.Exists(destFileFullPath))
            {
                File.Delete(destFileFullPath);
            }
            File.Move(sourceFileFullPath, destFileFullPath);
        }

        private string getDestFileFullPath(string sourceFileFullPath, string destFilePath)
        {
            string fileName = sourceFileFullPath.Substring(sourceFileFullPath.LastIndexOf('\\'));
            StringBuilder strBuilder = new StringBuilder();
            strBuilder.Append(destFilePath);
            strBuilder.Append(fileName);
            return strBuilder.ToString();
        }

        private void MoveFileWithCreate(string sourceFileFullPath, string destFilePath)
        {
            if (!Directory.Exists(destFilePath))
            {
                Directory.CreateDirectory(destFilePath);
            }
            MoveFile(sourceFileFullPath, destFilePath);
        }

        private bool readOneExcelFile(out Dictionary<string, TaskOrderManageInfo> curOrderFileDic, string fileName)
        {
            curOrderFileDic = new Dictionary<string, TaskOrderManageInfo>();
            try
            {
                DataSet ds = ExcelNPOIManager.ImportFromExcel(fileName);
                if (ds != null && ds.Tables != null)
                {
                    getTaskOrderInfo(out curOrderFileDic, ds);
                    if (curOrderFileDic.Count == 0)
                    {
                        BackgroundFuncManager.GetInstance().ReportBackgroundInfo(string.Format("{0}文件为空", fileName));
                        return false;
                    }
                }
            }
            catch (Exception ex)
            {
                BackgroundFuncManager.GetInstance().ReportBackgroundInfo(string.Format("{0}读取出错,{1}", fileName, ex.Message));
                return false;
            }
            BackgroundFuncManager.GetInstance().ReportBackgroundInfo(string.Format("{0}读取完毕,包含{1}个工单信息", fileName, curOrderFileDic.Count));
            foreach (var file in curOrderFileDic)
            {
                BackgroundFuncManager.GetInstance().ReportBackgroundInfo($"工单[{file.Key}]文件数[{file.Value.OrderFileList.Count}]");
            }
            return true;
        }

        private void getTaskOrderInfo(out Dictionary<string, TaskOrderManageInfo> curOrderFileDic, DataSet ds)
        {
            curOrderFileDic = new Dictionary<string, TaskOrderManageInfo>();
            foreach (DataTable dt in ds.Tables)
            {
                BackgroundFuncManager.GetInstance().ReportBackgroundInfo($"Excel行数{dt.Rows.Count}");
                int row = 1;
                foreach (DataRow dr in dt.Rows)
                {
                    addTaskOrderInfo(curOrderFileDic, dr, row);
                    row++;
                }
            }
        }

        private void addTaskOrderInfo(Dictionary<string, TaskOrderManageInfo> curOrderFileDic, DataRow dr, int row)
        {
            TaskOrderManageInfo taskOrderInfo;
            TaskOrderFileInfo orderFileInfo = new TaskOrderFileInfo();
            bool isValid = orderFileInfo.FillDataByExcel(dr, row);
            if (isValid)
            {
                string orderID = orderFileInfo.OrderID;
                if (!curOrderFileDic.TryGetValue(orderID, out taskOrderInfo))
                {
                    taskOrderInfo = new TaskOrderManageInfo(orderID, orderFileInfo.Area, orderFileInfo.StartDateTime, orderFileInfo.EndDateTime, DateTime.Now);
                    curOrderFileDic.Add(orderID, taskOrderInfo);
                }
                taskOrderInfo.OrderFileList.Add(orderFileInfo);
            }
        }
        #endregion

        protected override void getReadyBeforeQuery()
        {
            getOrderFromDB();
        }

        #region 从数据库读取需要分析的工单
        private void getOrderFromDB()
        {
            //读取tb_lowtask_result中未上传的工单
            DiyQueryTaskOrderInfo query = new DiyQueryTaskOrderInfo(mainModel);
            string districtName = DistrictManager.GetInstance().getDistrictName(mainModel.DistrictID);
            query.SetQueryCondition(districtName);
            query.Query();
            foreach (TaskOrderManageInfo orderFile in query.orderFileDic.Values)
            {
                if (!orderFileDic.ContainsKey(orderFile.OrderID))
                {
                    orderFileDic.Add(orderFile.OrderID, orderFile);
                }
            }
        }
        #endregion

        protected override void doBackgroundStatByFile(ClientProxy clientProxy)
        {
            if (orderFileDic.Count <= 0)
            {
                BackgroundFuncManager.GetInstance().ReportBackgroundError("暂无可以处理的工单文件");
                return;
            }

            BackgroundFuncManager.GetInstance().ReportBackgroundInfo(string.Format("有{0}个工单待分析", orderFileDic.Count));
            QueryCondition cond = new QueryCondition();
            cond.Geometorys = new SearchGeometrys();
            cond.Geometorys.Region = BackgroundFuncConfigManager.GetInstance().RegionBorder;
            SetQueryCondition(cond);

            //按工单循环
            foreach (TaskOrderManageInfo order in orderFileDic.Values)
            {
                bool needDealOrder = InitOrderInfo(order);
                if (needDealOrder)
                {
                    analyzeOneOrder(order);

                    bool needUpload = jdugeNeedUpload();
                    setResultAfterAnalyse();
                    if (needUpload)
                    {
                        UpLoadWebSerevice();
                    }
                    
                    saveResultToDB();
                }
                clearCurOrderData();
            }
            clearData();
        }

        private void analyzeOneOrder(TaskOrderManageInfo order)
        {
            List<TaskOrderRealFile> orderAnalysedFiles = getOrderAnalysedFiles(order);
            if (orderAnalysedFiles.Count == 0)
            {
                curTaskOrder.OrderState = OrderStateType.LogLoss;
                return;
            }
            judgeOrderState(order.OrderFileList.Count, orderAnalysedFiles);

            getFileInfoForAnalyse(orderAnalysedFiles, curTaskOrder.StartDateTime, curTaskOrder.EndDateTime);
            
            if (mainModel.FileInfos.Count > 0)
            {
                analyseFiles(orderAnalysedFiles);
            }
            else
            {
                curTaskOrder.OrderState = OrderStateType.LogLoss;
                BackgroundFuncManager.GetInstance().ReportBackgroundError(string.Format("工单时间段{0}-{1},不包含可以分析的文件", 
                    order.StartDateTime.ToString(), order.EndDateTime.ToString()));
            }
        }

        #region 分析工单前初始化
        private bool InitOrderInfo(TaskOrderManageInfo order)
        {
            if (order.OrderFileList.Count == 0)
            {
                BackgroundFuncManager.GetInstance().ReportBackgroundError("工单中没有文件");
                return false;
            }
            else if (order.OrderState == OrderStateType.AnalyseFail)
            {
                bool reachAnalyseFailTime = getReachDeadline(order, OrderCondition.ErrorTimeDiff);
                if (reachAnalyseFailTime)
                {
                    //设为初始状态重新分析后上传结果
                    order.OrderState = OrderStateType.Nomal;
                }
                else
                {
                    BackgroundFuncManager.GetInstance().ReportBackgroundError(string.Format("工单{0}中存在解析异常文件,请重新上传", order.OrderID));
                    return false;
                }
            }

            curTaskOrder = order;
            curTaskOrderResult = new TaskOrderManageResult(curTaskOrder.OrderID);
            bool reachOrderAnalyseDeadline = getReachDeadline(order, OrderCondition.TimeDiff);
            if (!reachOrderAnalyseDeadline)
            {
                //状态记为暂未解析
                curTaskOrder.OrderState = OrderStateType.LogDormant;
            }
            BackgroundFuncManager.GetInstance().ReportBackgroundInfo(string.Format("正在分析工单{0},包含文件数{1}个", order.OrderID, order.OrderFileList.Count));
            return true;
        }

        private bool getReachDeadline(TaskOrderManageInfo order, int deadline)
        {
            //***考虑是否用数据库时间作为判断
            double timespan = (DateTime.Now - order.ImportDataTime).TotalHours;
            if (timespan > deadline)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        #endregion

        #region 通过存储过程查询工单文件对应的实际解析后文件名,文件ID,log_file表(会过滤掉尚未入库的文件)
        private List<TaskOrderRealFile> getOrderAnalysedFiles(TaskOrderManageInfo order)
        {
            string splitStr = "^_^";
            string orderFileStr = getOrderFileStr(order, splitStr);
            List<TaskOrderRealFile> orderRealFile = getRealTaskOrderFile(orderFileStr, splitStr);
            BackgroundFuncManager.GetInstance().ReportBackgroundInfo(string.Format("从存储过程获取到解析后的文件数{0}个", orderRealFile.Count));
            return orderRealFile;
        }

        private void judgeOrderState(int orderFileCount, List<TaskOrderRealFile> orderAnalysedFiles)
        {
            int originalOrderFileCount = getOriginalOrderFileCount(orderAnalysedFiles);
            bool hasAnalyseInDB = getOrderState(orderFileCount, originalOrderFileCount);
            if (!hasAnalyseInDB || orderAnalysedFiles.Count == 0)
            {
                BackgroundFuncManager.GetInstance().ReportBackgroundError(string.Format("工单{0}中没有有效的文件或有文件尚未解析完成", curTaskOrder.OrderID));
            }
            else
            {
                BackgroundFuncManager.GetInstance().ReportBackgroundInfo(string.Format("实际工单对应文件数{0}个", originalOrderFileCount));
            }
        }

        private string getOrderFileStr(TaskOrderManageInfo orderInfo, string splitStr)
        {
            StringBuilder strBuilder = new StringBuilder();
            foreach (TaskOrderFileInfo orderFile in orderInfo.OrderFileList)
            {
                strBuilder.Append(orderFile.FileName);
                strBuilder.Append(splitStr);
            }
            string str = strBuilder.ToString();
            return str.Substring(0, str.LastIndexOf(splitStr));
        }

        private List<TaskOrderRealFile> getRealTaskOrderFile(string orderFileStr, string splitStr)
        {
            DiyQueryTaskOrderRealFile query = new DiyQueryTaskOrderRealFile(mainModel);
            query.SetQueryCondition(orderFileStr, splitStr);
            query.Query();
            List<TaskOrderRealFile> orderFile = query.GetValidFiles();
            return orderFile;
        }

        /// <summary>
        /// 根据存储过程查询出的结果合并拆分过后的文件,获取实际工单对应的文件个数
        /// </summary>
        /// <param name="orderRealFile"></param>
        /// <returns></returns>
        private int getOriginalOrderFileCount(List<TaskOrderRealFile> orderRealFile)
        {
            Dictionary<string, TaskOrderRealFile> originalOrderFile = new Dictionary<string, TaskOrderRealFile>();
            foreach (var file in orderRealFile)
            {
                if (!originalOrderFile.ContainsKey(file.FileName))
                {
                    originalOrderFile.Add(file.FileName, file);
                }
            }
            return originalOrderFile.Count;
        }

        /// <summary>
        /// 工单中的文件数量 大与 存储过程查询出的汇聚后实际已解析文件数量
        /// 判断是否在允许时间范围内,在则允许文件未解析,不在则为文件丢失
        /// </summary>
        /// <param name="orderFileCount">工单中的文件数量</param>
        /// <param name="orderRealFileCount">存储过程查询出的汇聚后实际已解析文件数量</param>
        private bool getOrderState(int orderFileCount, int orderRealFileCount)
        {
            bool hasAnalyseInDB = true;
            if (orderFileCount > orderRealFileCount)
            {
                hasAnalyseInDB = setOrderStateByImportTime(false);
            }
            return hasAnalyseInDB;
        }

        private bool setOrderStateByImportTime(bool isAnalyse)
        {
            if (curTaskOrder.OrderState == OrderStateType.LogDormant)
            {
                return false;
            }
            else
            {
                if (isAnalyse)
                {
                    //超出时间已解析,文件解析异常 或 文件不在工单时间内为虚假log
                    curTaskOrder.OrderState = OrderStateType.LogAnalyzeFail;
                }
                else
                {
                    //超出设定时间范围还未解析,状态记为log丢失
                    curTaskOrder.OrderState = OrderStateType.LogLoss;
                }
            }
            return true;
        }
        #endregion

        #region 通过文件ID,log_file表查询对应文件的详细信息(会过滤掉虚假log,log的时间不在工单时间范围内)
        private void getFileInfoForAnalyse(List<TaskOrderRealFile> orderFile, DateTime sTime, DateTime eTime)
        {
            MainModel.FileInfos.Clear();
            if (orderFile.Count > 0)
            {
                BackgroundFuncManager.GetInstance().ReportBackgroundInfo("开始从LogFile中查询文件");
                int iStime = (int)(JavaDate.GetMilliseconds(sTime) / 1000L);
                int iEtime = (int)(JavaDate.GetMilliseconds(eTime) / 1000L);
                MainModel.FileInfos.Clear();
                DiyQueryFileInfoByTBAndFileName query = new DiyQueryFileInfoByTBAndFileName();
                query.SetQueryCondition(orderFile, iStime, iEtime);
                query.Query();

                /** 文件查询时总出现sql语句大于8000个字符
                QueryCondition cond = new QueryCondition();
                StringBuilder sb = new StringBuilder();
                foreach (TaskOrderRealFile item in orderFile)
                {
                    sb.Append(item.FileName);
                    sb.Append(" or ");
                }
                string strFilter = sb.ToString().Remove(sb.Length - 4);
                int orNum = 1;
                cond.FileName = QueryCondition.MakeFileFilterString(strFilter, ref orNum).Replace("[_]", "_");
                cond.FileNameOrNum = orNum;
                cond.Areas = new Dictionary<int, List<int>>();
                TimePeriod period = new TimePeriod(sTime, eTime);
                cond.Periods.Add(period);
                cond.CarrierTypes.Add(1);
                MainModel model = MainModel.GetInstance();
                DIYQueryFileInfo queryCheckFile = new DIYQueryFileInfo(model);
                queryCheckFile.IsShowFileInfoForm = false;
                queryCheckFile.SetQueryCondition(cond);
                queryCheckFile.Query();
                */

                BackgroundFuncManager.GetInstance().ReportBackgroundInfo(string.Format("从LogFile中查询出的文件数{0}个", MainModel.FileInfos.Count));
            }
        }
        #endregion

        protected void analyseFiles(List<TaskOrderRealFile> orderAnalysedFiles)
        {
            try
            {
                clearDataBeforeAnalyseFiles();
                addFileCountLog();
                int iloop = 0;
                foreach (Model.FileInfo fileInfo in MainModel.FileInfos)
                {
                    BackgroundFuncManager.GetInstance().ReportBackgroundInfo("正在分析 " + FuncType.ToString() +
                        SubFuncType.ToString() + " 类 " + Name + "，当前文件 " + (++iloop) + "/" + MainModel.FileInfos.Count +
                        "个...文件名：" + fileInfo.Name);

                    curAnaFileInfo = fileInfo;
                    Condition.FileInfos.Clear();
                    Condition.FileInfos.Add(fileInfo);
                    MainModel.IsFileReplayByCompareMode = Condition.isCompareMode;

                    replay();
                    if (mainModel.BackgroundStopRequest)
                    {
                        break;
                    }
                }
                doSomethingAfterAnalyseFiles(orderAnalysedFiles);
            }
            catch (Exception e)
            {
                BackgroundFuncManager.GetInstance().ReportBackgroundError(e);
            }
            finally
            {
                System.Threading.Thread.Sleep(20);
            }
        }

        private void addFileCountLog()
        {
            int tdFileCount = 0;
            int diifNetFileCount = 0;
            foreach (Model.FileInfo fileInfo in MainModel.FileInfos)
            {
                if (fileInfo.CarrierType == 1)
                {
                    tdFileCount++;
                }
                else
                {
                    diifNetFileCount++;
                }
            }
            BackgroundFuncManager.GetInstance().ReportBackgroundInfo(string.Format("移动文件数{0}个,异网文件数{1}个", tdFileCount, diifNetFileCount));
        }

        protected override void clearDataBeforeAnalyseFiles()
        {
            orderCarrierResult = new CarrierResultInfo();
            carrierRenderInfo = new CarrierRenderInfo();
        }

        protected override void doStatWithQuery()
        {
            if (curAnaFileInfo == null || MainModel.DTDataManager.FileDataManagers.Count == 0)
            {
                return;
            }

            NetTypeResultInfo netTypeResultInfo;
            string carrierType = CarrierResultInfo.GetCarrierInfo(curAnaFileInfo.CarrierType);
            if (!orderCarrierResult.CarrierNetTypeDic.TryGetValue(carrierType, out netTypeResultInfo))
            {
                netTypeResultInfo = new NetTypeResultInfo();
                orderCarrierResult.CarrierNetTypeDic.Add(carrierType, netTypeResultInfo);
            }
            FileResultInfo fileResultInfo;
            string netType = NetTypeResultInfo.GetNetType(curAnaFileInfo.ServiceType, curAnaFileInfo.CarrierType);
            if (!netTypeResultInfo.NetTypeFileDic.TryGetValue(netType, out fileResultInfo))
            {
                fileResultInfo = new FileResultInfo();
                netTypeResultInfo.NetTypeFileDic.Add(netType, fileResultInfo);
                fileResultInfo.FileTPs.Add(curAnaFileInfo, MainModel.DTDataManager.FileDataManagers[0].TestPoints);
                BackgroundFuncManager.GetInstance().ReportBackgroundInfo(string.Format("{0} - 当前文件里程:{1},时长:{2}",
                carrierType, curAnaFileInfo.Distance, curAnaFileInfo.Duration));
            }
            else
            {
                judgeSameCarFile(carrierType, netType);
            }
        }

        #region 判断同车测试
        private void judgeSameCarFile(string carrierType, string netType)
        {
            NetTypeResultInfo carrierNetType = orderCarrierResult.CarrierNetTypeDic[carrierType];
            FileResultInfo netTypeFile = carrierNetType.NetTypeFileDic[netType];
            foreach (var testFile in netTypeFile.FileTPs)
            {
                bool isValidSameCarFile = judgeValidSameCarFile(netTypeFile.FileTPs, testFile, carrierType);
                if (isValidSameCarFile)
                {
                    return;
                }
            }
            //非同车测试文件
            netTypeFile.FileTPs.Add(curAnaFileInfo, MainModel.DTDataManager.FileDataManagers[0].TestPoints);
            BackgroundFuncManager.GetInstance().ReportBackgroundInfo(string.Format("{0} - 当前文件里程:{1},时长:{2}",
                carrierType, curAnaFileInfo.Distance, curAnaFileInfo.Duration));
        }

        private bool judgeValidSameCarFile(Dictionary<Model.FileInfo, List<TestPoint>> notSameCarTestFileDic, 
            KeyValuePair<Model.FileInfo, List<TestPoint>> testFile, string carrierType)
        {
            Model.FileInfo file = testFile.Key;
            bool timeOverlap = judgeTimeOverlap(file);
            if (timeOverlap)
            {
                //先判断采样点少得文件在采样点大的文件中的占比
                bool isSameCarFile;
                if (MainModel.DTDataManager.FileDataManagers[0].TestPoints.Count < testFile.Value.Count)
                {
                    isSameCarFile = judgeTestpointOverlap(MainModel.DTDataManager.FileDataManagers[0].TestPoints, testFile.Value);
                }
                else
                {
                    isSameCarFile = judgeTestpointOverlap(testFile.Value, MainModel.DTDataManager.FileDataManagers[0].TestPoints);
                }

                if (isSameCarFile)
                {
                    Model.FileInfo reservedFile;
                    if (curAnaFileInfo.Distance > file.Distance || (curAnaFileInfo.EndTime - curAnaFileInfo.BeginTime) > (file.EndTime - file.BeginTime))
                    {
                        //计算里程时长哪个文件更大,保留较大的那个文件
                        notSameCarTestFileDic.Add(curAnaFileInfo, MainModel.DTDataManager.FileDataManagers[0].TestPoints);
                        notSameCarTestFileDic.Remove(file);
                        reservedFile = curAnaFileInfo;
                    }
                    else
                    {
                        reservedFile = file;
                    }

                    BackgroundFuncManager.GetInstance().ReportBackgroundInfo(string.Format("{0} - 文件[{1}]与[{2}]为同车测试文件,保留文件里程:{3},时长:{4}",
                        carrierType, curAnaFileInfo.Name, file.Name, reservedFile.Distance, reservedFile.Duration));

                    return true;
                }
            }
            return false;
        }

        private bool judgeTimeOverlap(Model.FileInfo comparedFile)
        {
            //判断时段吻合度
            if (comparedFile.EndTime > curAnaFileInfo.BeginTime && comparedFile.BeginTime < curAnaFileInfo.EndTime)
            {
                int fileDuration = comparedFile.EndTime - comparedFile.BeginTime;
                int curAnaFileDuration = curAnaFileInfo.EndTime - curAnaFileInfo.BeginTime;

                double overlap = Math.Min(comparedFile.EndTime, curAnaFileInfo.EndTime) - Math.Max(comparedFile.BeginTime, curAnaFileInfo.BeginTime);
                double fileOverlap = getOverlap(overlap, fileDuration, 2);
                double curAnaFileOverlap = getOverlap(overlap, curAnaFileDuration, 2);
                if (fileOverlap >= OrderCondition.TimeOverlap || curAnaFileOverlap >= OrderCondition.TimeOverlap)
                {
                    return true;
                }
            }
            return false;
        }
        
        private bool judgeTestpointOverlap(List<TestPoint> curTestPoint, List<TestPoint> comparedTestPoint)
        {
            int lastTestPointTime = 0;
            int comparedTestPointTime = 0;
            int comparedTestPointIndex = 0;

            //按秒计算采样点个数(1秒只取1个点进行判断)
            int secondTestPoint = 0;
            //对比文件按秒计算采样点个数
            int secondComparedTestPoint = 0;
            //重叠采样点个数
            int sameTestPoint = 0;
            for (int i = 0; i < curTestPoint.Count; i++)
            {
                TestPoint curTP = curTestPoint[i];
                bool isValid = isValidTestPoint(lastTestPointTime, curTP);
                if (isValid)
                {
                    judgeComparedTestPoint(comparedTestPoint, curTP, ref comparedTestPointTime, ref comparedTestPointIndex, ref sameTestPoint);
                    secondTestPoint++;
                }
                lastTestPointTime = curTP.Time;
            }

            comparedTestPointTime = 0;
            for (int i = 0; i < comparedTestPoint.Count; i++)
            {
                TestPoint tp = comparedTestPoint[i];
                bool isValid = isValidTestPoint(comparedTestPointTime, tp);
                if (isValid)
                {
                    secondComparedTestPoint++;
                }
                comparedTestPointTime = tp.Time;
            }

            double curOverlap =  getOverlap(sameTestPoint, secondTestPoint, 2);
            double comparedOverlap = getOverlap(sameTestPoint, secondComparedTestPoint, 2);
            if (curOverlap >= OrderCondition.TPOverlap || comparedOverlap >= OrderCondition.TPOverlap)
            {
                return true;
            }

            return false;
        }

        private double getOverlap(double value1, double value2, int digits)
        {
            double res;
            if (value2 == 0)
            {
                res = 0;
            }
            else
            {
                res = Math.Round(value1 * 100 / value2, digits);
            }
            return res;
        }

        private void judgeComparedTestPoint(List<TestPoint> comparedTestPoint, TestPoint curTP, ref int comparedTestPointTime, 
            ref int comparedTestPointIndex, ref int sameTestPoint)
        {
            int lastTime = 0;
            for (; comparedTestPointIndex < comparedTestPoint.Count; comparedTestPointIndex++)
            {
                TestPoint comparedTP = comparedTestPoint[comparedTestPointIndex];
                lastTime = comparedTP.Time;
                //判断前后2秒的点距离是否满足条件
                if (comparedTP.Time >= curTP.Time - OrderCondition.TPTimeDiff && comparedTP.Time <= curTP.Time + OrderCondition.TPTimeDiff)
                {
                    bool isValid = isValidTestPoint(comparedTestPointTime - OrderCondition.TPTimeDiff, comparedTP);
                    if (isValid)
                    {
                        bool isSameCarTP = judgeDistance(curTP, comparedTP);
                        if (isSameCarTP)
                        {
                            sameTestPoint++;
                            comparedTestPointTime = curTP.Time;
                            break;
                        }
                    }
                }
                else if (comparedTP.Time > curTP.Time + OrderCondition.TPTimeDiff)
                {
                    break;
                }
            }
            comparedTestPointIndex = setTestPointIndex(curTP, comparedTestPointIndex, lastTime);
        }

        private int setTestPointIndex(TestPoint curTP, int comparedTestPointIndex, int lastTime)
        {
            if (lastTime >= curTP.Time + OrderCondition.TPTimeDiff)
            {
                //计算到n秒后的采样点则让index回退n*10个采样点
                if (comparedTestPointIndex > OrderCondition.TPTimeDiff * 10)
                {
                    comparedTestPointIndex -= OrderCondition.TPTimeDiff * 10;
                }
                else
                {
                    comparedTestPointIndex = 0;
                }
            }

            return comparedTestPointIndex;
        }

        private bool judgeDistance(TestPoint curTP, TestPoint comparedTP)
        {
            //判断距离
            double distance = comparedTP.Distance2(curTP);
            if (distance <= OrderCondition.TPDistance)
            {
                return true;
            }
            return false;
        }

        private bool isValidTestPoint(int Time, TestPoint tp)
        {
            //同一秒只取一个采样点进行判断,过滤经纬度为0的点(经纬度为0无法计算距离)
            if (tp.Time > Time && tp.Longitude > 0 && tp.Latitude > 0)
            {
                return true;
            }
            return false;
        }
        #endregion

        protected void doSomethingAfterAnalyseFiles(List<TaskOrderRealFile> orderAnalysedFiles)
        {
            BackgroundFuncManager.GetInstance().ReportBackgroundInfo("正在处理分析结果...");
            curTaskOrderFailAnalyseFiles = new TaskOrderFailAnalyseFile(curTaskOrder.OrderID);

            foreach (var carrierNetType in orderCarrierResult.CarrierNetTypeDic)
            {
                string carrierType = carrierNetType.Key;
                foreach (var netTypeFile in carrierNetType.Value.NetTypeFileDic)
                {
                    string netType = netTypeFile.Key;
                    int distance;
                    int duration;
                    setResultInfo(carrierType, netTypeFile.Value, orderAnalysedFiles, out distance, out duration);
                    BackgroundFuncManager.GetInstance().ReportBackgroundInfo(string.Format("{0} - {1} 非同车测试文件有{2}个",
                        carrierType, netType, netTypeFile.Value.FileTPs.Count));

                    curTaskOrderResult.SetCarrierNetTypeValue(distance, duration, carrierType, netType);
                }
            }

            takePic();
        }

        //统计网络制式对应的里程时长和采样点
        private void setResultInfo(string carrierType, FileResultInfo netTypeFile,
            List<TaskOrderRealFile> orderAnalysedFiles, out int distance, out int duration)
        {
            distance = 0;
            duration = 0;
            foreach (var fileDic in netTypeFile.FileTPs)
            {
                var file = fileDic.Key;
                if (file.StatStatus == 0)
                {
                    dealAnalyseFailFile(orderAnalysedFiles, file);
                }
                else
                {
                    distance += file.Distance;
                    duration += (file.EndTime - file.BeginTime);

                    //异网暂不截图,所以不添加渲染用的采样点
                    if (carrierType == CMCarrierType)
                    {
                        addRenderInfo(carrierType, fileDic, file);
                    }
                }
            }
        }

        private void addRenderInfo(string carrierType, KeyValuePair<Model.FileInfo, List<TestPoint>> fileDic, Model.FileInfo file)
        {
            ServiceTypeRenderInfo serviceTypeRenderInfo;
            if (!carrierRenderInfo.CarrierTPDic.TryGetValue(carrierType, out serviceTypeRenderInfo))
            {
                serviceTypeRenderInfo = new ServiceTypeRenderInfo();
                carrierRenderInfo.CarrierTPDic.Add(carrierType, serviceTypeRenderInfo);
            }

            string type = ServiceTypeRenderInfo.GetTypeByTypeID(file.ServiceType);
            List<TestPoint> netTypeAllTPList;
            if (!serviceTypeRenderInfo.ServiceTypeTPDic.TryGetValue(type, out netTypeAllTPList))
            {
                netTypeAllTPList = new List<TestPoint>();
                serviceTypeRenderInfo.ServiceTypeTPDic.Add(type, netTypeAllTPList);
            }
            netTypeAllTPList.AddRange(fileDic.Value);
        }

        private void dealAnalyseFailFile(List<TaskOrderRealFile> orderAnalysedFiles, Model.FileInfo file)
        {
            bool hasFindInOrder = false;
            foreach (var orderFile in orderAnalysedFiles)
            {
                if (orderFile.AnalyzeFileName == file.Name)
                {
                    curTaskOrderFailAnalyseFiles.FileName.Add(orderFile.FileName);
                    hasFindInOrder = true;
                    break;
                }
            }

            if (hasFindInOrder)
            {
                setOrderStateByImportTime(true);
                BackgroundFuncManager.GetInstance().ReportBackgroundError(string.Format("文件[{0}]解析异常", file.Name));
            }
            else
            {
                BackgroundFuncManager.GetInstance().ReportBackgroundError(string.Format("没有在工单中获取到对应[{0}]文件", file.Name));
            }
        }

        #region 截图,保存附件信息
        private void takePic()
        {
            BackgroundFuncManager.GetInstance().ReportBackgroundInfo("开始截图...");
            string rsrpThemeName = "";
            string sinrThemeName = "";
            int districtID = DistrictManager.GetInstance().GetDistrictID(curTaskOrder.Area);
            Func.MapForm mapForm = mainModel.MainForm.GetMapForm();
            //异网不截图,所以暂时就一个附件
            string lastCombinePic = "";

            foreach (var carrierInfo in carrierRenderInfo.CarrierTPDic)
            {
                string carrierType = carrierInfo.Key;
                foreach (var serviceTP in carrierInfo.Value.ServiceTypeTPDic)
                {
                    try
                    {
                        string serviceType = serviceTP.Key;
                        ServiceTypeRenderInfo.GetThemeNameByType(serviceType, out rsrpThemeName, out sinrThemeName);

                        string rsrpPicPath = getPicPath(districtID, "RSRP", serviceType, carrierType);
                        takePicByServiceType(rsrpPicPath, rsrpThemeName, serviceTP.Value, mapForm);
                        string sinrPicPath = getPicPath(districtID, "SINR", serviceType, carrierType);
                        takePicByServiceType(sinrPicPath, sinrThemeName, serviceTP.Value, mapForm);

                        //读取RSRP,SINR图片合成一张图
                        string combinePicPath = getPicPath(districtID, "RSRP_SINR", serviceType, carrierType);
                        combineTwoPic(rsrpPicPath, sinrPicPath, combinePicPath);

                        //合成不同业务类型的图片
                        string curCombinePicPath = getPicPath(districtID, "RSRP_SINR", "Result", carrierType);
                        combineTwoPic(lastCombinePic, combinePicPath, curCombinePicPath);
                        lastCombinePic = curCombinePicPath;

                        File.Delete(combinePicPath);
                        File.Delete(rsrpPicPath);
                        File.Delete(sinrPicPath);
                    }
                    catch (Exception e)
                    {
                        BackgroundFuncManager.GetInstance().ReportBackgroundError(string.Format("截图发生异常:{0}", e.StackTrace));
                    }
                }
            }

            string picName = lastCombinePic.Substring(lastCombinePic.LastIndexOf("\\") + 1);
            curTaskOrderResult.AttachName = picName;
            curTaskOrderResult.AttachUrl = getAttachUrl(districtID, picName);
            curTaskOrderResult.AttachSize = getFileSize(lastCombinePic);
        }

        private void takePicByServiceType(string picPath, string themeName, List<TestPoint> orderAllTPList, Func.MapForm mapForm)
        {
            MainModel.GetInstance().FireSetDefaultMapSerialTheme(themeName);
            mapForm.GoToView(null, orderAllTPList, this);
            mapForm.FireAndOutputCurMapToPic(themeName, false, picPath);
        }

        private string getAttachUrl(int districtID, string picName)
        {
            StringBuilder attachUrl = new StringBuilder();
            char[] cahrArray = new char[] { '/', '\\', ' ' };
            attachUrl.Append(OrderCondition.AttachPath.TrimEnd(cahrArray));
            attachUrl.Append('/');
            attachUrl.Append(districtID);
            attachUrl.Append('/');
            attachUrl.Append(picName);
            return attachUrl.ToString();
        }

        private string getPicPath(int districtID, string paramName, string serviceType, string carrierType)
        {
            string folderPath = getPicFolder(districtID);
            if (!Directory.Exists(folderPath))
            {
                Directory.CreateDirectory(folderPath);
            }
            StringBuilder strBuilder = new StringBuilder(folderPath);
            strBuilder.Append("\\");
            strBuilder.Append(curTaskOrder.OrderID);
            strBuilder.Append("_");
            strBuilder.Append(paramName);
            strBuilder.Append("_");
            strBuilder.Append(serviceType);
            strBuilder.Append("_");
            strBuilder.Append(carrierType);
            strBuilder.Append(".jpg");
            return strBuilder.ToString();
        }

        private string getPicFolder(int districtID)
        {
            StringBuilder strBuilder = new StringBuilder(OrderCondition.PicPath);
            strBuilder.Append("\\");
            strBuilder.Append(districtID);
            return strBuilder.ToString();
        }

        private void combineTwoPic(string picPath1, string picPath2, string combinePicPath)
        {
            if (string.IsNullOrEmpty(picPath1))
            {
                if (File.Exists(combinePicPath))
                {
                    File.Delete(combinePicPath);
                }
                File.Move(picPath2, combinePicPath);
                return;
            }

            Image img1 = Image.FromFile(picPath1);
            Bitmap map1 = new Bitmap(img1);
            Image img2 = Image.FromFile(picPath2);
            Bitmap map2 = new Bitmap(img2);

            var width = Math.Max(img1.Width, img2.Width);
            var height = img1.Height + img2.Height + 10;

            Bitmap bitMap = new Bitmap(width, height);
            Graphics g1 = Graphics.FromImage(bitMap);
            g1.FillRectangle(Brushes.White, new Rectangle(0, 0, width, height));
            g1.DrawImage(map1, 0, 0, img1.Width, img1.Height);
            g1.DrawImage(map2, 0, img1.Height + 10, img2.Width, img2.Height);
            map1.Dispose();
            map2.Dispose();
            img1.Dispose();
            img2.Dispose();

            if (File.Exists(combinePicPath))
            {
                File.Delete(combinePicPath);
            }

            Image img = bitMap;
            img.Save(combinePicPath);
            img.Dispose();
        }

        private long getFileSize(string filePath)
        {
            if (File.Exists(filePath))
            {
                System.IO.FileInfo file = new System.IO.FileInfo(filePath);
                return file.Length;
            }
            return 0;
        }
        #endregion

        private void setResultAfterAnalyse()
        {
            curTaskOrderResult.UpLoadState = Convert.ToInt32(curTaskOrder.OrderState);
            curTaskOrderResult.CalculateResult();
        }

        #region 上传webservice
        private bool jdugeNeedUpload()
        {
            if (curTaskOrder.OrderState == OrderStateType.LogDormant)
            {
                //没有到达文件解析截止时间不上传
                return false;
            }
            else if (curTaskOrder.OrderState == OrderStateType.LogAnalyzeFail)
            {
                bool reachAnalyseFailTime = getReachDeadline(curTaskOrder, OrderCondition.ErrorTimeDiff);
                if (!reachAnalyseFailTime)
                {
                    //没有到达文件解析异常处理时间不上传
                    curTaskOrder.OrderState = OrderStateType.AnalyseFail;
                    return false;
                }
            }
            return true;
        }

        private void UpLoadWebSerevice()
        {
            string attachRef = getAttachRef();
            string opDetail = getOpDetail();

            object[] args = new object[2];
            args[0] = attachRef;
            args[1] = opDetail;

            curTaskOrderResult.UpLoadDateTime = DateTime.Now;
            BackgroundFuncManager.GetInstance().ReportBackgroundInfo(string.Format("正在上传WebService\r\n opDetail:{0}\r\n attachRef:{1}", opDetail, attachRef));
            try
            {
                object result = WebServiceHelper.InvokeWebService(OrderCondition.WebServicePath, "returnMaterialResult", args);
                bool isSuccess = judgeWebServiceSuccess(result);
                if (!isSuccess)
                {
                    curTaskOrderResult.UpLoadState = -99;
                }
            }
            catch (Exception e)
            {
                BackgroundFuncManager.GetInstance().ReportBackgroundError("上传WebService发生异常" + e.Message);
                curTaskOrderResult.UpLoadState = -99;
            }
        }

        private string getAttachRef()
        {
            if (!string.IsNullOrEmpty(curTaskOrderResult.AttachName))
            {
                XmlDocument xmlDoc = new XmlDocument();
                //创建根节点    
                XmlNode root = xmlDoc.CreateElement("attachRef");
                xmlDoc.AppendChild(root);
                XmlNode nodeAttach = xmlDoc.CreateNode(XmlNodeType.Element, "attachInfo", null);
                CreateNode(xmlDoc, nodeAttach, "attachName", curTaskOrderResult.AttachName);
                CreateNode(xmlDoc, nodeAttach, "attachURL", curTaskOrderResult.AttachUrl);
                CreateNode(xmlDoc, nodeAttach, "attachLength", curTaskOrderResult.AttachSize.ToString());
                root.AppendChild(nodeAttach);

                string attachRef = xmlDoc.InnerXml;
                return attachRef;

            }
            else
            {
                string attachRef = "<attachRef></attachRef>";
                return attachRef;
            }
        }

        private string getOpDetail()
        {
            if (!string.IsNullOrEmpty(curTaskOrderResult.OrderID))
            {
                XmlDocument xmlDoc = new XmlDocument();
                //创建根节点    
                XmlNode root = xmlDoc.CreateElement("opDetail");
                xmlDoc.AppendChild(root);

                XmlNode nodeRecord = xmlDoc.CreateNode(XmlNodeType.Element, "recordInfo", null);
                root.AppendChild(nodeRecord);
                CreateOpDetailNode(xmlDoc, nodeRecord, "优化服务平台工单号", "sheetName", curTaskOrderResult.OrderID);
                CreateOpDetailNode(xmlDoc, nodeRecord, "GSM测试公里数", "GSM_Test_distance", curTaskOrderResult.CM2GDistance);
                CreateOpDetailNode(xmlDoc, nodeRecord, "LTE测试公里数", "LTE_Test_distance", curTaskOrderResult.CM4GDistance);
                CreateOpDetailNode(xmlDoc, nodeRecord, "异网测试公里数", "Spec_Test_Distance", curTaskOrderResult.DiffNetDistance);
                CreateOpDetailNode(xmlDoc, nodeRecord, "GSM测试时长", "GSM_Test_duration", curTaskOrderResult.CM2GDuration);
                CreateOpDetailNode(xmlDoc, nodeRecord, "LTE测试时长", "LTE_Test_duration", curTaskOrderResult.CM4GDuration);
                CreateOpDetailNode(xmlDoc, nodeRecord, "异网测试时长", "Spec_Test_Duration", curTaskOrderResult.DiffNetDuration);

                CreateOpDetailNode(xmlDoc, nodeRecord, "报错信息", "WrongInformation", curTaskOrderResult.UpLoadState.ToString());

                string opDetail = xmlDoc.InnerXml;
                return opDetail;
            }
            else
            {
                string opDetail = "<opDetail></opDetail>";
                return opDetail;
            }
        }



        protected void CreateOpDetailNode(XmlDocument xmlDoc, XmlNode parentNode, string fieldChName, string fieldEnName, string fieldContent)
        {
            XmlNode nodeField = xmlDoc.CreateNode(XmlNodeType.Element, "fieldInfo", null);
            CreateNode(xmlDoc, nodeField, "fieldChName", fieldChName);
            CreateNode(xmlDoc, nodeField, "fieldEnName", fieldEnName);
            CreateNode(xmlDoc, nodeField, "fieldContent", fieldContent);
            parentNode.AppendChild(nodeField);
        }

        public void CreateNode(XmlDocument xmlDoc, XmlNode parentNode, string name, string value)
        {
            XmlNode node = xmlDoc.CreateNode(XmlNodeType.Element, name, null);
            node.InnerText = value;
            parentNode.AppendChild(node);
        }

        private bool judgeWebServiceSuccess(object returnedValue)
        {
            string returnedValueStr = returnedValue.ToString();
            BackgroundFuncManager.GetInstance().ReportBackgroundInfo(string.Format("接收到WebService的返回信息:{0}", returnedValueStr));
            if (returnedValueStr.IndexOf("true") >= 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        #endregion

        #region 保存结果
        private void saveResultToDB()
        {
            BackgroundFuncManager.GetInstance().ReportBackgroundInfo("正在将结果保存数据库...");
            try
            {
                if (curTaskOrder.OrderState == OrderStateType.AnalyseFail || curTaskOrder.OrderState == OrderStateType.LogAnalyzeFail)
                {
                    //文件解析异常更改工单文件描述
                    DiyUpdateTaskOrderInfo updateQuery = new DiyUpdateTaskOrderInfo(curTaskOrderFailAnalyseFiles);
                    updateQuery.Query();
                }
                DiyInsertTaskOrderResult query = new DiyInsertTaskOrderResult(curTaskOrderResult);
                query.Query();
            }
            catch(Exception ex)
            {
                BackgroundFuncManager.GetInstance().ReportBackgroundError(string.Format("保存数据库出错.{0}", ex.StackTrace));
            }
        }
        #endregion

        private void clearCurOrderData()
        {
            curTaskOrder = null;
            curTaskOrderResult = null;
            orderCarrierResult = null;
            carrierRenderInfo = null;
            MainModel.FileInfos.Clear();
            MainModel.ClearDTData();
            MainModel.FireDTDataChanged(this);
        }

        private void clearData()
        {
            orderFileDic = null;
        }

        public override BackgroundStatType StatType
        {
            get { return BackgroundStatType.Cell_Region; }
        }

        public override BackgroundFuncType FuncType
        {
            get { return BackgroundFuncType.LTE业务专题; }
        }

        public override BackgroundSubFuncType SubFuncType
        {
            get { return BackgroundSubFuncType.其他; }
        }

        public override Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["BackgroundStat"] = BackgroundStat;
                param["ExcelPath"] = OrderCondition.ExcelPath;
                param["ImportedExcelPath"] = OrderCondition.ImportedExcelPath;
                param["WebServicePath"] = OrderCondition.WebServicePath;
                param["PicPath"] = OrderCondition.PicPath;
                param["AttachPath"] = OrderCondition.AttachPath;
                param["TimeDiff"] = OrderCondition.TimeDiff;
                param["ErrorTimeDiff"] = OrderCondition.ErrorTimeDiff;
                param["TimeOverlap"] = OrderCondition.TimeOverlap;
                param["TPOverlap"] = OrderCondition.TPOverlap;
                param["TPTimeDiff"] = OrderCondition.TPTimeDiff;
                param["TPDistance"] = OrderCondition.TPDistance;
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                try
                {
                    BackgroundStat = (bool)param["BackgroundStat"];
                    OrderCondition.ExcelPath = (string)param["ExcelPath"];
                    OrderCondition.ImportedExcelPath = (string)param["ImportedExcelPath"];
                    OrderCondition.WebServicePath = (string)param["WebServicePath"];
                    OrderCondition.PicPath = (string)param["PicPath"];
                    OrderCondition.AttachPath = (string)param["AttachPath"];
                    OrderCondition.TimeDiff = (int)param["TimeDiff"];
                    OrderCondition.ErrorTimeDiff = (int)param["ErrorTimeDiff"];
                    OrderCondition.TimeOverlap = (int)param["TimeOverlap"];
                    OrderCondition.TPOverlap = (int)param["TPOverlap"];
                    OrderCondition.TPTimeDiff = (int)param["TPTimeDiff"];
                    OrderCondition.TPDistance = (int)param["TPDistance"];
                }
                catch(Exception e)
                {
                    BackgroundFuncManager.GetInstance().ReportBackgroundError(string.Format("{0}参数加载异常 : {1}", Name, e.StackTrace));
                }
            }
        }

        public override PropertiesControl Properties
        {
            get
            {
                return new LowTaskFileManageProperties(this);
            }
        }

        public const string CMCarrierType = "移动";
        public const string DiffNetCarrierType = "异网";
        class CarrierResultInfo
        {
            //运营商对应的网络制式结果信息
            public Dictionary<string, NetTypeResultInfo> CarrierNetTypeDic { get; set; } = new Dictionary<string, NetTypeResultInfo>();

            //根据文件运营商类型返回运营商标识
            public static string GetCarrierInfo(int carrier)
            {
                switch (carrier)
                {
                    case 1:
                        return CMCarrierType;
                    default:
                        return DiffNetCarrierType;
                }
            }
        }

        public const string GsmNetType = "2G";
        public const string LteNetType = "4G";
        public const string DiffNetType = "DiffNet";
        public const string UnknownNetType = "unknown";
        class NetTypeResultInfo
        {
            //网络制式对应的文件结果信息
            public Dictionary<string, FileResultInfo> NetTypeFileDic { get; set; } = new Dictionary<string, FileResultInfo>();

            //根据文件业务类型返回网络制式标识
            public static string GetNetType(int serviceType, int carrierType)
            {
                if (carrierType != 1)
                {
                    //异网运营商暂不区分2G,4G,返回异网
                    return DiffNetType;
                }

                string netType = "";
                ServiceType typeEnum = (ServiceType)serviceType;
                switch (typeEnum)
                {
                    case ServiceType.GSM_VOICE:
                    case ServiceType.GPRS_DATA:
                    case ServiceType.EDGE_DATA:
                    case ServiceType.GSM_SCAN:
                    case ServiceType.GSM_IDLE:
                    case ServiceType.GSM_MOS:
                    case ServiceType.GSM_MTR:
                    case ServiceType.GSM_CALLTRACE:
                    case ServiceType.GSM扫频_频谱分析:
                    //CSFB文件当作2G
                    case ServiceType.LTE_TDD_VOICE:
                    case ServiceType.LTE_FDD_VOICE:
                        netType = GsmNetType;
                        break;
                    case ServiceType.LTE_TDD_DATA:
                    case ServiceType.LTE_TDD_IDLE:
                    case ServiceType.LTE_TDD_MULTI:
                    case ServiceType.LTE_TDD_VOLTE:
                    case ServiceType.LTE_TDD_UEP:
                    case ServiceType.LTE_SIGNAL:
                    case ServiceType.SER_LTE_TDD_VIDEO_VOLTE:
                    case ServiceType.LTE_FDD_DATA:
                    case ServiceType.LTE_FDD_IDLE:
                    case ServiceType.LTE_FDD_MULTI:
                    case ServiceType.LTE_FDD_VOLTE:
                    case ServiceType.SER_LTE_FDD_VIDEO_VOLTE:
                    case ServiceType.LTE_SCAN_TOPN:
                    case ServiceType.LTE_SCAN_CW:
                    case ServiceType.LTE扫频_频谱分析:
                    case ServiceType.SER_NBIOT_DATA:
                    case ServiceType.SCAN_NBIOT_TOPN:
                        netType = LteNetType;
                        break;
                    default:
                        netType = UnknownNetType;
                        break;
                }
                return netType;
            }
        }

        class FileResultInfo
        {
            //文件对应的采样点
            public Dictionary<Model.FileInfo, List<TestPoint>> FileTPs { get; set; } = new Dictionary<Model.FileInfo, List<TestPoint>>();
        }

        class CarrierRenderInfo
        {
            //运营商对应的业务类型渲染信息
            public Dictionary<string, ServiceTypeRenderInfo> CarrierTPDic { get; set; } = new Dictionary<string, ServiceTypeRenderInfo>();
        }

        class ServiceTypeRenderInfo
        {
            //不同业务类型的图例不同,指标不同,需要分开截图绘制
            //业务类型对应的采样点
            public Dictionary<string, List<TestPoint>> ServiceTypeTPDic { get; set; } = new Dictionary<string, List<TestPoint>>();

            /// <summary>
            /// 根据业务标识获取渲染指标名
            /// </summary>
            /// <param name="type"></param>
            /// <param name="rsrpThemeName"></param>
            /// <param name="sinrThemeName"></param>
            public static void GetThemeNameByType(string type, out string rsrpThemeName, out string sinrThemeName)
            {
                switch (type)
                {
                    case "Gsm":
                        rsrpThemeName = "RxLevSub";
                        sinrThemeName = "RxQualSub";
                        return;
                    case "GsmScan":
                        rsrpThemeName = "GSCAN_RxLev";
                        sinrThemeName = "GSCAN_RxQual";
                        return;
                    case "Lte":
                    case "Nbiot":
                    case "Csfb":
                        rsrpThemeName = "lte_RSRP";
                        sinrThemeName = "lte_SINR";
                        return;
                    case "Lte-Fdd":
                    case "Csfb-Fdd":
                        rsrpThemeName = "lte_fdd_RSRP";
                        sinrThemeName = "lte_fdd_SINR";
                        return;
                    case "LteScan":
                    case "NbiotScan":
                        rsrpThemeName = "LTESCAN_TopN_CELL_Specific_RSRP";
                        sinrThemeName = "LTESCAN_TopN_CELL_Specific_RSSINR";
                        return;
                    default:
                        rsrpThemeName = "";
                        sinrThemeName = "";
                        return;
                }
            }

            /// <summary>
            /// 根据文件业务类型获取渲染对应的业务标识
            /// </summary>
            /// <param name="serviceType"></param>
            /// <returns></returns>
            public static string GetTypeByTypeID(int serviceType)
            {
                ServiceType typeEnum = (ServiceType)serviceType;
                switch (typeEnum)
                {
                    case ServiceType.GSM_VOICE:
                    case ServiceType.GPRS_DATA:
                    case ServiceType.EDGE_DATA:
                    case ServiceType.GSM_IDLE:
                    case ServiceType.GSM_MOS:
                        return "Gsm";
                    case ServiceType.GSM_SCAN:
                        return "GsmScan";
                    case ServiceType.LTE_TDD_DATA:
                    case ServiceType.LTE_TDD_IDLE:
                    case ServiceType.LTE_TDD_MULTI:
                    case ServiceType.LTE_TDD_UEP:
                    case ServiceType.LTE_SIGNAL:
                    case ServiceType.LTE_TDD_VOLTE:
                    case ServiceType.SER_LTE_TDD_VIDEO_VOLTE:
                        return "Lte";
                    case ServiceType.LTE_FDD_DATA:
                    case ServiceType.LTE_FDD_IDLE:
                    case ServiceType.LTE_FDD_MULTI:
                    case ServiceType.LTE_FDD_VOLTE:
                    case ServiceType.SER_LTE_FDD_VIDEO_VOLTE:
                        return "Lte-Fdd";
                    //CSFB文件当作2G
                    case ServiceType.LTE_TDD_VOICE:
                        return "Csfb";
                    case ServiceType.LTE_FDD_VOICE:
                        return "Csfb-Fdd";
                    case ServiceType.SER_NBIOT_DATA:
                        return "Nbiot";
                    case ServiceType.LTE_SCAN_TOPN:
                    case ServiceType.LTE_SCAN_CW:
                        return "LteScan";
                    case ServiceType.SCAN_NBIOT_TOPN:
                        return "NbiotScan";
                    default:
                        return "unknown";
                }
            }
        }
    }
}
