using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.Util;

namespace MasterCom.RAMS.Model
{
    public class MSC
    {
        public MSC()
        {
        }

        public MSC(string name)
        {
            Name = name;
        }

        public string Name { get; set; }

        public int PoolID { get; set; } = -1;

        public List<BSC> BSCs
        {
            get { return bscs; }
        }

        public BSC AddBSC(string bscName)
        {
            if (!nameBSCMap.ContainsKey(bscName))
            {
                BSC bsc = new BSC(bscName);
                bscs.Add(bsc);
                nameBSCMap[bscName] = bsc;
                bsc.BelongMSC = this;
            }
            return nameBSCMap[bscName];
        }

        private readonly List<BSC> bscs = new List<BSC>();

        private readonly Dictionary<string, BSC> nameBSCMap = new Dictionary<string, BSC>();

        public static IComparer<MSC> GetCompareByName()
        {
            if (comparerByName == null)
            {
                comparerByName = new ComparerByName();
            }
            return comparerByName;
        }

        private static IComparer<MSC> comparerByName;

        public class ComparerByName : IComparer<MSC>
        {
            public int Compare(MSC x, MSC y)
            {
                return x.Name.CompareTo(y.Name);
            }
        }
    }
}