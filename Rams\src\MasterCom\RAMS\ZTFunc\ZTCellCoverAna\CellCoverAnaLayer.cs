﻿using System.Collections.Generic;
using MasterCom.MTGis;
using System.Drawing;
using MasterCom.RAMS.Func.CoverageCheck;
using MapWinGIS;
using System;
using System.Runtime.Serialization;
using System.Drawing.Drawing2D;
using MasterCom.MControls;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Grid;

namespace MasterCom.RAMS.ZTFunc
{
    [Serializable()]
    public class CellCoverAnaLayer : CustomDrawLayer
    {
        public CellCoverAnaLayer(MapOperation mp, string name)
            : base(mp, name)
        {
        }
        Dictionary<CellGridKey, int> cellGridRxLev = new Dictionary<CellGridKey,int>();

        public Pen selPen { get; set; } = new Pen(Color.Cyan, 3);
        public void FillData(Dictionary<CellGridKey, int> cellGridKeyRxLev)
        {
            this.cellGridRxLev.Clear();
            this.cellGridRxLev = cellGridKeyRxLev;
        }

        static CellCoverAnaLayer()
        {
            float radius = 12.5f;
            antennaPoints.Add(new PointF[] 
                {
                    new PointF(0, -0.25f), 
                    new PointF(radius - 2, -0.25f), 
                    new PointF(radius - 3, -1.5F), 
                    new PointF(radius+2, 0), 
                    new PointF(radius - 3, 1.5F), 
                    new PointF(radius - 2, 0.25f), 
                    new PointF(0, 0.25f), 
                });
            GraphicsPath path = new GraphicsPath();
            path.AddPolygon(antennaPoints[0]);
            antennaPaths.Add(path);
        }
        
        public Color getColorFrom(int value)
        {
            if (value >= -120 && value < -100)
                return Color.FromArgb(255, Color.FromArgb(255, 0, 0));
            else if (value >= -100 && value < -90)
                return Color.FromArgb(255, Color.FromArgb(255, 102, 0));
            else if (value >= -90 && value < -80)
                return Color.FromArgb(255, Color.FromArgb(255, 204, 0));
            else if (value >= -80 && value < -70)
                return Color.FromArgb(255, Color.FromArgb(0, 0, 255));
            else if (value >= -70 && value < -60)
                return Color.FromArgb(255, Color.FromArgb(101, 0, 255));
            else if (value >= -60 && value < -10)
                return Color.FromArgb(255, Color.FromArgb(0, 255, 255));
            else
                return Color.Green;
        }
        public override void Draw(System.Drawing.Rectangle clientRect, System.Drawing.Rectangle updateRect, Graphics graphics)
        {
            if(!IsVisible)
            {
                return;
            }
            updateRect.Inflate((int)(40 * 10000 / Map.Scale), (int)(40 * 10000 / Map.Scale));
            DbRect dRect;
            Map.FromDisplay(updateRect, out dRect);
            graphics.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.HighQuality;

            double temp_long = Map.GetCenter().x;
            double temp_lati = Map.GetCenter().y;
            DbPoint ptDSel = new DbPoint(temp_long, temp_lati);
            PointF scrPointSel;
            Map.ToDisplay(ptDSel, out scrPointSel);
            //底层20米的精度跨度大小 0.0001951
            double llGap = (0.0001951 / 20) * 100;
            DbPoint ptDSel2 = new DbPoint(temp_long + llGap, temp_lati);
            PointF scrPointSel2;
            Map.ToDisplay(ptDSel2, out scrPointSel2);

            double xDiaGrid = (0.0001951 / 20) * 80;
            DbPoint ptXDSel2Grid = new DbPoint(temp_long + xDiaGrid, temp_lati);
            PointF scrXPointSel2Grid;
            Map.ToDisplay(ptXDSel2Grid, out scrXPointSel2Grid);
            float xRadGrid = (scrXPointSel2Grid.X - scrPointSel.X) / 2 * 1.03f;

            //底层20米的精度跨度大小 0.00017986
            double yDiaGrid = (0.00017986 / 20) * 80;
            DbPoint ptYDSel2Grid = new DbPoint(temp_long, temp_lati - yDiaGrid);
            PointF scrYPointSel2Grid;
            Map.ToDisplay(ptYDSel2Grid, out scrYPointSel2Grid);
            float yRadGrid = (scrYPointSel2Grid.Y - scrPointSel.Y) / 2 * 1.0025f;

            DrawGrids(xRadGrid, yRadGrid, graphics);
        }
        private void DrawGrids(float xGapGrid, float yGapGrid, Graphics graphics)
        {
            float xGapGridNew = float.Parse((xGapGrid * 1.0).ToString());
            float yGapGridNew = float.Parse((yGapGrid * 1.0).ToString());
            foreach (CellGridKey grid in cellGridRxLev.Keys)
            {
                SolidBrush brush = new SolidBrush(getColorFrom(cellGridRxLev[grid]));
                DbPoint dPoint = new DbPoint(grid.ILongitude, grid.ILatitude);
                PointF point;
                this.Map.ToDisplay(dPoint, out point);

                RectangleF rect = new RectangleF(point.X - xGapGridNew, point.Y - yGapGridNew, xGapGridNew, yGapGridNew);
                graphics.FillRectangle(brush, rect);

                if (grid.ILongitude == ZTDIYQueryCellCoverAnaForm.gridKey.ILongitude
                    && grid.ILatitude == ZTDIYQueryCellCoverAnaForm.gridKey.ILatitude)
                {
                    graphics.DrawRectangle(new Pen(Color.Red, 4), (int)rect.Left, (int)rect.Top, (int)rect.Width, (int)rect.Height);
                }
            }
        }

        private static List<PointF[]> antennaPoints = new List<PointF[]>();
        private static List<GraphicsPath> antennaPaths = new List<GraphicsPath>();
    }
}
