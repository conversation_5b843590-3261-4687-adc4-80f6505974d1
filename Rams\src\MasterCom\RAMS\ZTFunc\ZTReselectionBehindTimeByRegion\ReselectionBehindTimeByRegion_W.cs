﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public class ReselectionBehindTimeByRegion_W : ReselectionBehindTimeByRegion_TD
    {
        public ReselectionBehindTimeByRegion_W(MainModel mainModel) : base(mainModel)
        {
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.WCDMA_DATA);
            ServiceTypes.Add(ServiceType.WCDMA_VOICE);
        }

        public override string Name
        {
            get { return "WCDMA重选不及时"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 14000, 14025, this.Name);
        }

        protected override ValidTestPoint NewPoint()
        {
            return new ValidTestPoint_W();
        }
    }

    public class ValidTestPoint_W : ValidTestPoint
    {
        public override ConvertResult Convert(TestPoint tp, int rxLevDiff)
        {
            int? mainLac = (int?)tp["W_SysLAI"];
            if (mainLac == null || (int)mainLac == -255)
            {
                MainLac = MainCi = -255;
                return ConvertResult.MainLacciInvalid;
            }
            int? mainCi = (int?)tp["W_SysCellID"];
            if (mainCi == null || (int)mainCi == -255)
            {
                MainLac = MainCi = -255;
                return ConvertResult.MainLacciInvalid;
            }
            float? mainRxLev = (float?)tp["W_TotalRSCP"];
            if (mainRxLev == null || (float)mainRxLev > -10 || (float)mainRxLev < -140)
            {
                return ConvertResult.MainRxLevInvalid;
            }
            MainLac = (int)mainLac;
            MainCi = (int)mainCi;
            MainRxLev = (float)mainRxLev;

            float? nRxLev;
            for (int i = 0; i < 6; ++i)
            {
                WCell wCell = tp.GetNBCell(i) as WCell;
                if (wCell == null)
                {
                    continue;
                }

                nRxLev = (float?)tp["W_SNeiRSCP", i];
                if (nRxLev == null || nRxLev > -10 || nRxLev < -140 || nRxLev - mainRxLev < rxLevDiff)
                {
                    continue;
                }

                NRxLev.Add((float)nRxLev);
                NLac.Add(wCell.LAC);
                NCi.Add(wCell.CI);
            }
            if (NLac.Count == 0)
            {
                return ConvertResult.NCellNotFound;
            }

            Time = tp.Time;
            DTime = tp.DateTime;
            return ConvertResult.Succeed;
        }
    }
}
