<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="toolTip.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="$this.Icon" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAABAAEAEBAAAAEAIABoBAAAFgAAACgAAAAQAAAAIAAAAAEAIAAAAAAAAAQAABILAAASCwAAAAAAAAAA
        AACmpqYAAAAAAQAAAAgAAAASAAAAEwAAABMAAAATAAAAEwAAABMAAAATAAAAEwAAABMAAAASAAAACAAA
        AAGmpqYAp6enAAAAAASbm5uip6en/6enp/+np6f/p6en/6enp/93mH7/CEwS/3eYfv+np6f/p6en/5ub
        m6IAAAAEp6enAKmpqQAAAAACqamp//////////////////////+11bz/CEwS/wmeLf8ITBL/q8yy/+/v
        7/+pqan/AAAAAqmpqQCqqqoAqqqqAKqqqv//////oMjv/6nM7/+jybb/CFYV/wyqO/8Mqjv/DKo7/whW
        Ff+nyK7/qqqq/wmeLQCqqqoArKysAKysrACsrKz//////3TU+v9bubf/CGQa/w65TP8OuUz/MN1r/w65
        TP8OuUz/CGQa/3ubgv8Jni0ArKysAK6urgCurq4Arq6u///////v9Pf/icjw/waJrP8JdSD/MN1r/wl1
        IP8w3Wv/EcVa/xHFWv8JdSD/CXUgTa6urgCxsbEAsbGxALGxsf//////Ap7w/wKe8P8CnvD/W7Cx/wmG
        Jf8Ggan/CYYl/zDda/8TzmT/MN1r/wmGJf+xsbEAs7OzALOzswCzs7P//////wKr9v8Wsfb/jdPy/+Tp
        7f85pen/ApDp/0+krP8JlCr/MN1r/wmUKv8JdSBNs7OzALW1tQC1tbUAtbW1///////x8fH/1eXv/6LS
        7f8rqe3/Apvu/xKg7f/N2+T/n8Cm/wmeLf+Booj/CZ4tALW1tQC3t7cAt7e3ALe3t///////AqXz/wKl
        8/8CpfP/AqXz/x2s8f+92OX/39/g/93d3v/b29v/t7e3/7e3twC3t7cAurq6ALq6ugC6urr//////wKt
        9/8Crff/IrX1/1/E7//H2+T/39/f/7W1tf+1tbX/u7u7/7W1tf+6uroAurq6ALy8vAC8vLwAvLy8////
        ///o6Oj/5ubm/+Pj5P/h4eH/3t7f/7W1tf///////////+Li4v+8vLz5vLy8ALy8vAC+vr4Avr6+AL6+
        vv//////5eXm/+Pj4//g4OH/3t7f/9zc3P+1tbX//////9bW1v/R0dL/vr6+4b6+vgC+vr4Av7+/AL+/
        vwC/v7///////+Li4//g4OD/3t7e/9vb3P/Z2dr/tbW1/+vr6//T09T/y8vM/7+/v6W/v78Av7+/AMHB
        wQDBwcEAwcHB/////////////////////////////////7W1tf/d3d7/ysrK/8LCwv/BwcE8wcHBAMHB
        wQDCwsIAwsLCAMLCwpbCwsL/wsLC/8LCwv/CwsL/wsLC/8LCwv/CwsL8wsLCw8LCworCwsIhwsLCAMLC
        wgDCwsIAgAEAAIABAACAAQAAwAMAAMADAADAAQAAwAEAAMABAADAAwAAwAMAAMADAADAAwAAwAMAAMAD
        AADAAwAAwAcAAA==
</value>
  </data>
</root>