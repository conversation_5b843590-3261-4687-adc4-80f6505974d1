﻿namespace MasterCom.RAMS.KPI_Statistics
{
    partial class ReportPickerDlg
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.lnkReloadRpt = new System.Windows.Forms.LinkLabel();
            this.label1 = new System.Windows.Forms.Label();
            this.btnCancel = new System.Windows.Forms.Button();
            this.btnOK = new System.Windows.Forms.Button();
            this.chkAllParam = new System.Windows.Forms.CheckBox();
            this.lbNoteAll = new System.Windows.Forms.Label();
            this.chkCbxRound = new DevExpress.XtraEditors.CheckedComboBoxEdit();
            this.label2 = new System.Windows.Forms.Label();
            this.pnlRound = new System.Windows.Forms.Panel();
            this.label3 = new System.Windows.Forms.Label();
            this.gpbCellFilter = new System.Windows.Forms.GroupBox();
            this.lbldes = new System.Windows.Forms.Label();
            this.btnFileSelect = new System.Windows.Forms.Button();
            this.txtFileNames = new System.Windows.Forms.TextBox();
            this.lblCellFileFilter = new System.Windows.Forms.Label();
            this.chkSelectFilter = new System.Windows.Forms.CheckBox();
            this.reportFilter = new MasterCom.RAMS.KPI_Statistics.ReportFilterControl();
            this.chkTime = new System.Windows.Forms.CheckBox();
            ((System.ComponentModel.ISupportInitialize)(this.chkCbxRound.Properties)).BeginInit();
            this.pnlRound.SuspendLayout();
            this.gpbCellFilter.SuspendLayout();
            this.SuspendLayout();
            // 
            // lnkReloadRpt
            // 
            this.lnkReloadRpt.AutoSize = true;
            this.lnkReloadRpt.Location = new System.Drawing.Point(489, 32);
            this.lnkReloadRpt.Name = "lnkReloadRpt";
            this.lnkReloadRpt.Size = new System.Drawing.Size(53, 12);
            this.lnkReloadRpt.TabIndex = 1;
            this.lnkReloadRpt.TabStop = true;
            this.lnkReloadRpt.Text = "重新加载";
            this.lnkReloadRpt.LinkClicked += new System.Windows.Forms.LinkLabelLinkClickedEventHandler(this.lnkReloadRpt_LinkClicked);
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.ForeColor = System.Drawing.Color.Black;
            this.label1.Location = new System.Drawing.Point(12, 31);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(65, 12);
            this.label1.TabIndex = 15;
            this.label1.Text = "统计报表：";
            // 
            // btnCancel
            // 
            this.btnCancel.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnCancel.Location = new System.Drawing.Point(475, 230);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(87, 27);
            this.btnCancel.TabIndex = 4;
            this.btnCancel.Text = "取消";
            this.btnCancel.UseVisualStyleBackColor = true;
            // 
            // btnOK
            // 
            this.btnOK.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnOK.Location = new System.Drawing.Point(382, 230);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(87, 27);
            this.btnOK.TabIndex = 3;
            this.btnOK.Text = "确定";
            this.btnOK.UseVisualStyleBackColor = true;
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // chkAllParam
            // 
            this.chkAllParam.AutoSize = true;
            this.chkAllParam.Location = new System.Drawing.Point(14, 63);
            this.chkAllParam.Name = "chkAllParam";
            this.chkAllParam.Size = new System.Drawing.Size(72, 16);
            this.chkAllParam.TabIndex = 2;
            this.chkAllParam.Text = "所有指标";
            this.chkAllParam.UseVisualStyleBackColor = true;
            // 
            // lbNoteAll
            // 
            this.lbNoteAll.AutoSize = true;
            this.lbNoteAll.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(128)))));
            this.lbNoteAll.Location = new System.Drawing.Point(92, 65);
            this.lbNoteAll.Name = "lbNoteAll";
            this.lbNoteAll.Size = new System.Drawing.Size(257, 12);
            this.lbNoteAll.TabIndex = 18;
            this.lbNoteAll.Text = "（勾选时，将会查询全部指标，资源占用较大）";
            // 
            // chkCbxRound
            // 
            this.chkCbxRound.Location = new System.Drawing.Point(80, 12);
            this.chkCbxRound.Name = "chkCbxRound";
            this.chkCbxRound.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.chkCbxRound.Properties.SelectAllItemCaption = "全选";
            this.chkCbxRound.Size = new System.Drawing.Size(200, 21);
            this.chkCbxRound.TabIndex = 1;
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.ForeColor = System.Drawing.Color.Black;
            this.label2.Location = new System.Drawing.Point(9, 17);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(65, 12);
            this.label2.TabIndex = 15;
            this.label2.Text = "测试轮次：";
            // 
            // pnlRound
            // 
            this.pnlRound.Controls.Add(this.chkCbxRound);
            this.pnlRound.Controls.Add(this.label3);
            this.pnlRound.Controls.Add(this.label2);
            this.pnlRound.Location = new System.Drawing.Point(3, 93);
            this.pnlRound.Name = "pnlRound";
            this.pnlRound.Size = new System.Drawing.Size(533, 40);
            this.pnlRound.TabIndex = 19;
            this.pnlRound.Visible = false;
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(399, 21);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(131, 12);
            this.label3.TabIndex = 0;
            this.label3.Text = "注：此panel默认不可见";
            this.label3.Visible = false;
            // 
            // gpbCellFilter
            // 
            this.gpbCellFilter.Controls.Add(this.lbldes);
            this.gpbCellFilter.Controls.Add(this.btnFileSelect);
            this.gpbCellFilter.Controls.Add(this.txtFileNames);
            this.gpbCellFilter.Controls.Add(this.lblCellFileFilter);
            this.gpbCellFilter.Controls.Add(this.chkSelectFilter);
            this.gpbCellFilter.Location = new System.Drawing.Point(3, 146);
            this.gpbCellFilter.Name = "gpbCellFilter";
            this.gpbCellFilter.Size = new System.Drawing.Size(559, 78);
            this.gpbCellFilter.TabIndex = 20;
            this.gpbCellFilter.TabStop = false;
            this.gpbCellFilter.Visible = false;
            // 
            // lbldes
            // 
            this.lbldes.AutoSize = true;
            this.lbldes.Location = new System.Drawing.Point(5, 55);
            this.lbldes.Name = "lbldes";
            this.lbldes.Size = new System.Drawing.Size(431, 12);
            this.lbldes.TabIndex = 4;
            this.lbldes.Text = "注：Excel文件前三列为TAC、ECI、CellName(文件不要被打开否则读取不到数据)";
            // 
            // btnFileSelect
            // 
            this.btnFileSelect.Enabled = false;
            this.btnFileSelect.Location = new System.Drawing.Point(465, 23);
            this.btnFileSelect.Name = "btnFileSelect";
            this.btnFileSelect.Size = new System.Drawing.Size(87, 27);
            this.btnFileSelect.TabIndex = 3;
            this.btnFileSelect.Text = "选择";
            this.btnFileSelect.UseVisualStyleBackColor = true;
            this.btnFileSelect.Click += new System.EventHandler(this.btnFileSelect_Click);
            // 
            // txtFileNames
            // 
            this.txtFileNames.Enabled = false;
            this.txtFileNames.Location = new System.Drawing.Point(91, 25);
            this.txtFileNames.Name = "txtFileNames";
            this.txtFileNames.Size = new System.Drawing.Size(364, 21);
            this.txtFileNames.TabIndex = 2;
            // 
            // lblCellFileFilter
            // 
            this.lblCellFileFilter.AutoSize = true;
            this.lblCellFileFilter.Location = new System.Drawing.Point(5, 29);
            this.lblCellFileFilter.Name = "lblCellFileFilter";
            this.lblCellFileFilter.Size = new System.Drawing.Size(89, 12);
            this.lblCellFileFilter.TabIndex = 1;
            this.lblCellFileFilter.Text = "小区文件筛选：";
            // 
            // chkSelectFilter
            // 
            this.chkSelectFilter.AutoSize = true;
            this.chkSelectFilter.Location = new System.Drawing.Point(5, 2);
            this.chkSelectFilter.Name = "chkSelectFilter";
            this.chkSelectFilter.Size = new System.Drawing.Size(72, 16);
            this.chkSelectFilter.TabIndex = 0;
            this.chkSelectFilter.Text = "小区筛选";
            this.chkSelectFilter.UseVisualStyleBackColor = true;
            this.chkSelectFilter.Visible = false;
            this.chkSelectFilter.Click += new System.EventHandler(this.chkSelectFilter_Click);
            // 
            // reportFilter
            // 
            this.reportFilter.Location = new System.Drawing.Point(83, 27);
            this.reportFilter.Name = "reportFilter";
            this.reportFilter.Size = new System.Drawing.Size(400, 30);
            this.reportFilter.TabIndex = 21;
            // 
            // chkTime
            // 
            this.chkTime.AutoSize = true;
            this.chkTime.Location = new System.Drawing.Point(393, 65);
            this.chkTime.Name = "chkTime";
            this.chkTime.Size = new System.Drawing.Size(96, 16);
            this.chkTime.TabIndex = 22;
            this.chkTime.Text = "分多时段汇总";
            this.chkTime.UseVisualStyleBackColor = true;
            this.chkTime.Visible = false;
            // 
            // ReportPickerDlg
            // 
            this.AcceptButton = this.btnOK;
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(574, 269);
            this.Controls.Add(this.chkTime);
            this.Controls.Add(this.reportFilter);
            this.Controls.Add(this.gpbCellFilter);
            this.Controls.Add(this.lbNoteAll);
            this.Controls.Add(this.chkAllParam);
            this.Controls.Add(this.lnkReloadRpt);
            this.Controls.Add(this.label1);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.Controls.Add(this.pnlRound);
            this.Name = "ReportPickerDlg";
            this.Text = "选择报表模板";
            ((System.ComponentModel.ISupportInitialize)(this.chkCbxRound.Properties)).EndInit();
            this.pnlRound.ResumeLayout(false);
            this.pnlRound.PerformLayout();
            this.gpbCellFilter.ResumeLayout(false);
            this.gpbCellFilter.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion
        #region
        /// <summary>
        /// 新初始化窗体选择带有”TAC“，”ECI“，“CellName”的Excel或CSV文件
        /// </summary>
        
        #endregion
        private System.Windows.Forms.LinkLabel lnkReloadRpt;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.Button btnOK;
        private System.Windows.Forms.CheckBox chkAllParam;
        private System.Windows.Forms.Label lbNoteAll;
        private DevExpress.XtraEditors.CheckedComboBoxEdit chkCbxRound;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Panel pnlRound;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.GroupBox gpbCellFilter;
        private System.Windows.Forms.CheckBox chkSelectFilter;
        private System.Windows.Forms.Label lblCellFileFilter;
        private System.Windows.Forms.Label lbldes;
        private System.Windows.Forms.Button btnFileSelect;
        private System.Windows.Forms.TextBox txtFileNames;
        private MasterCom.RAMS.KPI_Statistics.ReportFilterControl reportFilter;
        private System.Windows.Forms.CheckBox chkTime;
    }
}