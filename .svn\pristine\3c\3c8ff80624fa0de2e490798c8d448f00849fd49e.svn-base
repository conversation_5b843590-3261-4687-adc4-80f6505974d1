﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.Func
{
    public partial class CQTConnectDlg : Form
    {
        public CQTConnectDlg()
        {
            InitializeComponent();
        }

        internal void FillData(List<CQTAddressItem> list)
        {
            listCQTNames.Items.Clear();
            foreach(CQTAddressItem cqtItem in list)
            {
                listCQTNames.Items.Add(cqtItem);
            }
        }

        internal CQTAddressItem getSelectedCQTItem()
        {
            return listCQTNames.SelectedItem as CQTAddressItem;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            if(listCQTNames.SelectedItem!=null)
            {
                this.DialogResult = DialogResult.OK;
            }
        }
    }
}