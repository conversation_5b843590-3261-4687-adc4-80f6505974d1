﻿using System;
using System.Collections.Generic;
using System.Text;
using MapWinGIS;

namespace MasterCom.MTGis
{
    public static class ShapeHelper
    {
        /// <summary>
        ///  按partShapeIndex获取Shape里面对应Part Shape的点信息
        /// </summary>
        /// <param name="shape"></param>
        /// <param name="partShapeIndex"></param>
        /// <returns></returns>
        public static List<DbPoint> GetPartShapePoints(MapWinGIS.Shape shape, int partShapeIndex)
        {
            int partCnt = shape.NumParts;
            if (partShapeIndex >= partCnt)
            {
                return new List<DbPoint>();
            }
            List<DbPoint> pnts = new List<DbPoint>();
            int startIdx = shape.get_Part(partShapeIndex);
            int endIdx = 0;
            if (partShapeIndex == partCnt - 1)
            {//最后一个shape
                endIdx = shape.numPoints;
            }
            else
            {
                endIdx = shape.get_Part(partShapeIndex + 1);
            }
            for (; startIdx < endIdx; startIdx++)
            {
                double x = 0;
                double y = 0;
                if (shape.get_XY(startIdx, ref x, ref y))
                {
                    DbPoint pnt = new DbPoint(x, y);
                    pnts.Add(pnt);
                }
            }
            return pnts;
        }

        /// <summary>
        ///  按partShapeIndex获取Shape里面对应Part Shape的点信息
        /// </summary>
        /// <param name="shape"></param>
        /// <param name="partShapeIndex"></param>
        /// <returns></returns>
        public static List<GMap.NET.PointLatLng> GetPartShapePointsEx(MapWinGIS.Shape shape, int partShapeIndex)
        {
            int partCnt = shape.NumParts;
            if (partShapeIndex >= partCnt)
            {
                return new List<GMap.NET.PointLatLng>();
            }
            List<GMap.NET.PointLatLng> pnts = new List<GMap.NET.PointLatLng>();
            int startIdx = shape.get_Part(partShapeIndex);
            int endIdx = 0;
            if (partShapeIndex == partCnt - 1)
            {//最后一个shape
                endIdx = shape.numPoints;
            }
            else
            {
                endIdx = shape.get_Part(partShapeIndex + 1);
            }
            for (; startIdx < endIdx; startIdx++)
            {
                double x = 0;
                double y = 0;
                if (shape.get_XY(startIdx, ref x, ref y))
                {
                    GMap.NET.PointLatLng pnt = new GMap.NET.PointLatLng(y, x);
                    pnts.Add(pnt);
                }
            }
            return pnts;
        }

        /// <summary>
        /// 由于MapWinGis自带的Shape.get_PartAsShape会产生无法回收的垃圾，故如此实现
        /// </summary>
        /// <returns></returns>
        public static Shape GetPartAsShape(MapWinGIS.Shape shape, int partIdx)
        {
            List<DbPoint> pnts = GetPartShapePoints(shape, partIdx);
            if (pnts == null)
            {
                return null;
            }
            Shape partShp = new Shape();
            partShp.ShapeType = shape.ShapeType;
            int j = 0;
            for (int i = 0; i < pnts.Count; i++, j++)
            {
                Point pnt = new Point();
                pnt.x = pnts[i].x;
                pnt.y = pnts[i].y;
                partShp.InsertPoint(pnt, ref j);
            }
            return partShp;
        }

        /// <summary>
        /// 向shapefile插入新的字段
        /// </summary>
        /// <param name="file">需要插入字段的shapefile</param>
        /// <param name="fieldName">字段名</param>
        /// <param name="filedType">字段类型</param>
        /// <param name="filedPecision">字段精度</param>
        /// <param name="filedWidth">字段宽度</param>
        /// <param name="fieldId">字段对应的索引</param>
        /// <returns></returns>
        public static bool InsertNewField(Shapefile file, string fieldName, FieldType filedType, int filedPecision, int filedWidth, ref int fieldId)
        {
            Field field = new Field();
            field.Name = fieldName;
            field.Type = filedType;
            field.Precision = filedPecision;
            field.Width = filedWidth;
            return file.EditInsertField(field, ref fieldId, null);
        }

        /// <summary>
        /// 向shape插入一个圆
        /// </summary>
        /// <param name="shp">需要插入圆的shape</param>
        /// <param name="longitude">圆心经度</param>
        /// <param name="latitude">圆心纬度</param>
        /// <param name="radius">半径，经纬度对应米，如0.0004大概为40米</param>
        public static void AddCircleShape(MapWinGIS.Shape shp, double longitude, double latitude, double radius)
        {
            for (int i = 0; i < 37; i++)
            {
                MapWinGIS.Point pnt = new MapWinGIS.Point();
                pnt.x = longitude + radius * Math.Cos(i * Math.PI / 18);
                pnt.y = latitude - radius * Math.Sin(i * Math.PI / 18);
                shp.InsertPoint(pnt, ref i);
            }
        }

        /// <summary>
        /// 创建一个圆形
        /// </summary>
        /// <param name="longitude">圆心经度</param>
        /// <param name="latitude">圆心纬度</param>
        /// <param name="radius">半径，经纬度对应米，如0.0004大概为40米</param>
        /// <returns>圆形</returns>
        public static Shape CreateCircleShape(double longitude, double latitude, double radius)
        {
            Shape shp = new Shape();
            shp.Create(ShpfileType.SHP_POLYGON);
            for (int i = 0; i < 37; i++)//取圆上37个点
            {
                MapWinGIS.Point pnt = new MapWinGIS.Point();
                pnt.x = longitude + radius * Math.Cos(i * Math.PI / 18);
                pnt.y = latitude - radius * Math.Sin(i * Math.PI / 18);
                shp.InsertPoint(pnt, ref i);
            }
            return shp;
        }

        /// <summary>
        /// 生成一个矩形shape
        /// </summary>
        /// <param name="ltX">左上角经度</param>
        /// <param name="ltY">左上角纬度</param>
        /// <param name="brX">右下角经度</param>
        /// <param name="brY">右下角纬度</param>
        public static Shape CreateRectShape(double ltX, double ltY, double brX, double brY)
        {
            //左上角开始，顺时针InsertPoint
            Shape shp = new Shape();
            shp.Create(ShpfileType.SHP_POLYGON);
            int idx = 0;
            MapWinGIS.Point pnt = new MapWinGIS.Point();
            pnt.x = ltX;//左上角
            pnt.y = ltY;
            shp.InsertPoint(pnt, ref idx);
            MapWinGIS.Point pnt1 = new MapWinGIS.Point();
            pnt1.x = brX;//右上角
            pnt1.y = ltY;
            idx++;
            shp.InsertPoint(pnt1, ref idx);
            MapWinGIS.Point pnt2 = new MapWinGIS.Point();
            pnt2.x = brX;//右下角
            pnt2.y = brY;
            idx++;
            shp.InsertPoint(pnt2, ref idx);
            MapWinGIS.Point pnt3 = new MapWinGIS.Point();
            pnt3.x = ltX;//左下角
            pnt3.y = brY;
            idx++;
            shp.InsertPoint(pnt3, ref idx);
            idx++;
            shp.InsertPoint(pnt, ref idx);//左上角
            return shp;
        }

        public static List<Shape> GetShapesFromTable(string mapPathRegion, string columnNameRegion)
        {
            List<Shape> shapeList = new List<Shape>();
            if (!System.IO.File.Exists(mapPathRegion))
            {
                return shapeList;
            }

            MapWinGIS.Shapefile table = new MapWinGIS.Shapefile();
            try
            {
                if (!table.Open(mapPathRegion, null))
                {
                    return shapeList;
                }
            }
            catch
            {
                table.Close();
                return shapeList;
            }
            try
            {
                int nmFldIndex = MapOperation.GetColumnFieldIndex(table, columnNameRegion);
                return GetShapesFromTable(table, nmFldIndex);
            }
            catch 
            {
                //continue
            }
            finally
            {
                table.Close();
            }
            return shapeList;
        }

        public static List<Shape> GetShapesFromTable(MapWinGIS.Shapefile table, int nmFieldIdx)
        {
            List<Shape> shapeList = new List<Shape>();
            ShapeAndRegionNameDic = new Dictionary<string, List<Shape>>();
            try
            {
                for (int i = 0; i < table.NumShapes; i++)
                {
                    string strRionName = table.get_CellValue(nmFieldIdx,i) as string ;
                    MapWinGIS.Shape geome = table.get_Shape(i);
                    if (geome.ShapeType == MapWinGIS.ShpfileType.SHP_POLYGON)
                    {
                        shapeList.Add(geome);
                        if (!ShapeAndRegionNameDic.ContainsKey(strRionName))
                        {
                            List<Shape> shape = new List<Shape>();
                            shape.Add(geome);
                            ShapeAndRegionNameDic.Add(strRionName,shape);
                        }
                        else
                        {
                            ShapeAndRegionNameDic[strRionName].Add(geome);
                        }
                    }
                }
            }
            catch
            {
                //continue
            }
            return shapeList;
        }

        public static Dictionary<string, List<Shape>> ShapeAndRegionNameDic { get; set; } = new Dictionary<string, List<Shape>>();

        public static MapWinGIS.Shape CombineMultiRegions(List<MapWinGIS.Shape> shapeList)
        {
            if (shapeList.Count > 0)
            {
                MapWinGIS.Shape bshape = shapeList[0].Clone();
                for (int x = 1; x < shapeList.Count; x++)
                {
                    Shape shp = shapeList[x];
                    int numPart = bshape.NumParts;
                    int numPoint = bshape.numPoints;
                    bshape.InsertPart(numPoint, ref numPart);
                    for (int i = 0; i < shp.numPoints; i++)
                    {
                        bshape.InsertPoint(shp.get_Point(i), ref numPoint);
                        numPoint++;
                    }
                }
                return bshape;
            }
            return null;
        }

        /// <summary>
        /// 生成一个三角状的小区Shape
        /// </summary>
        /// <param name="centerX">中心经度</param>
        /// <param name="centerY">中心纬度</param>
        /// <param name="endX">方向角方法的末端经度</param>
        /// <param name="endY">方向角方法的末端纬度</param>
        /// <param name="dirAng">相对于正北方向的方向角度数</param>
        /// <returns></returns>
        public static MapWinGIS.Shape CreateOutdoorCellShape(double centerX, double centerY, double endX, double endY, double dirAng)
        {
            Shape shp = new Shape();
            shp.Create(ShpfileType.SHP_POLYGON);
            int i = 1;
            MapWinGIS.Point centerPnt = new MapWinGIS.Point();
            centerPnt.x = centerX;
            centerPnt.y = centerY;
            shp.InsertPoint(centerPnt, ref i);
            i++;
            double radius = Math.Sqrt(Math.Pow(centerX - endX, 2) + Math.Pow(centerY - endY, 2));
            MapWinGIS.Point pnt = new MapWinGIS.Point();
            pnt.x = centerX + radius * Math.Cos((dirAng - 90 - 15 )* Math.PI / 180);
            pnt.y = centerY - radius * Math.Sin((dirAng - 90 - 15) * Math.PI / 180);
            shp.InsertPoint(pnt, ref i);
            i++;
            pnt = new MapWinGIS.Point();
            pnt.x = endX;
            pnt.y = endY;
            shp.InsertPoint(pnt, ref i);
            i++;
            pnt = new MapWinGIS.Point();
            pnt.x = centerX + radius * Math.Cos((dirAng - 90 + 15) * Math.PI / 180);
            pnt.y = centerY - radius * Math.Sin((dirAng - 90 + 15) * Math.PI / 180);
            shp.InsertPoint(pnt, ref i);
            i++;
            shp.InsertPoint(centerPnt, ref i);
            return shp;
        }

        public static bool DeleteShpFile(string fullFileName)
        {
            bool deleteed = false;
            try
            {
                string delPath = System.IO.Path.GetDirectoryName(fullFileName);
                string delFile = System.IO.Path.GetFileNameWithoutExtension(fullFileName);
                System.IO.File.Delete(string.Format(delPath + @"\" + delFile + ".shp"));
                System.IO.File.Delete(string.Format(delPath + @"\" + delFile + ".dbf"));
                System.IO.File.Delete(string.Format(delPath + @"\" + delFile + ".prj"));
                System.IO.File.Delete(string.Format(delPath + @"\" + delFile + ".shx"));
                deleteed = true;
            }
            catch
            {
                //continue
            }
            return deleteed;
        }

        /// <summary>
        /// 获取指定文件的所有属性字段
        /// </summary>
        /// <param name="shpFileName"></param>
        /// <returns>打开文件失败返回null</returns>
        public static List<string> GetFieldNamesFromFile(string shpFileName)
        {
            Shapefile shp = new Shapefile();
            if (!shp.Open(shpFileName, null))
            {
                return new List<string>();
            }

            List<string> retList = new List<string>();
            for (int i = 0; i < shp.NumFields; ++i)
            {
                Field field = shp.get_Field(i);
                retList.Add(field.Name);
            }
            shp.Close();
            return retList;
        }
        internal static Shape CreatePolylineShape(List<DbPoint> list)
        {
            Shape shp = new Shape();
            shp.Create(ShpfileType.SHP_POLYLINE);
            int w = 1;
            MapWinGIS.Point firstPt = new Point();
            firstPt.x = list[0].x;
            firstPt.y = list[0].y;
            shp.InsertPoint(firstPt, ref w);
            w++;
            for (int i = 1; i < list.Count; i++)
            {
                DbPoint pt = list[i];
                MapWinGIS.Point centerPnt = new MapWinGIS.Point();
                centerPnt.x = pt.x;
                centerPnt.y = pt.y;
                shp.InsertPoint(centerPnt, ref w);
                w++;
            }
            return shp;
        }
        
        internal static Shape CreatePolygonShape(List<DbPoint> list)
        {
            Shape shp = new Shape();
            shp.Create(ShpfileType.SHP_POLYGON);
            int w = 1;
            MapWinGIS.Point firstPt = new Point();
            firstPt.x = list[0].x;
            firstPt.y = list[0].y;
            shp.InsertPoint(firstPt, ref w);
            w++;
            for(int i =1;i<list.Count;i++)
            {
                DbPoint pt = list[i];
                MapWinGIS.Point centerPnt = new MapWinGIS.Point();
                centerPnt.x = pt.x;
                centerPnt.y = pt.y;
                shp.InsertPoint(centerPnt, ref w);
                w++;
            }
            shp.InsertPoint(firstPt, ref w);
            return shp;
        }

        //导出图层,用于查看切割的图形是否正确
        public static int MakeShpFile(string filename, Dictionary<string, Shape> shpList, ShpfileType type)
        {
            try
            {
                Shapefile shpFile = new Shapefile();
                bool result = shpFile.CreateNewWithShapeID("", type);
                if (!result)
                {
                    return -1;
                }

                //列
                int iGrid = 0;
                ShapeHelper.InsertNewField(shpFile, "NAME", FieldType.STRING_FIELD, 7, 0, ref iGrid);

                int shpIdx = 0;
                foreach (var info in shpList)
                {
                    shpFile.EditInsertShape(info.Value, ref shpIdx);
                    shpFile.EditCellValue(iGrid, shpIdx, info.Key);
                    shpIdx++;
                }
                shpFile.SaveAs(filename, null);
                shpFile.Close();
                return 1;
            }
            catch (Exception)
            {
                return -1;
            }
        }
    }

    /// <summary>
    /// Shapefile的Field定义
    /// </summary>
    public class ShpField
    {
        public string FieldName { get; set; }
        public MapWinGIS.FieldType FieldType { get; set; }
        public int Precision { get; set; }
        public int Width { get; set; }
        public int Index { get; set; }

        public ShpField(string fieldName, MapWinGIS.FieldType fieldType, int precision, int width, int index)
        {
            this.FieldName = fieldName;
            this.FieldType = fieldType;
            this.Precision = precision;
            this.Width = width;
            this.Index = index;
        }
    }

    /// <summary>
    /// Shapefile的项定义，项由shape和字段值列表组成
    /// </summary>
    public class ShpItem
    {
        public int ShapeIndex { get; set; }
        public MapWinGIS.Shape Shape { get; set; }
        public List<object> FieldValueList { get; set; }

        public ShpItem(int shapeIndex, MapWinGIS.Shape shape)
        {
            this.ShapeIndex = shapeIndex;
            this.Shape = shape;
            this.FieldValueList = null;
        }

        public ShpItem(int shapeIndex, MapWinGIS.Shape shape, List<object> fieldValueList)
        {
            this.ShapeIndex = shapeIndex;
            this.Shape = shape;
            this.FieldValueList = fieldValueList;
        }
    }

    /// <summary>
    /// 用于保存一个简单的Shapefile
    /// 暂时主要用于LowSpeedInfoForm和LowSpeedInfoForm_TD
    /// </summary>
    public class ShpNewFile
    {
        private readonly MapWinGIS.Shapefile shp = new MapWinGIS.Shapefile();

        public bool CreateNewFile(string fileName, MapWinGIS.ShpfileType fileType)
        {
            return shp.CreateNew(fileName, fileType);
        }

        public void SaveClose()
        {
            shp.Save(null);
            shp.Close();
        }

        public bool CreateFields(List<ShpField> fieldList)
        {
            bool ret = false;
            //shp.StartEditingTable(null)
            foreach (ShpField field in fieldList)
            {
                int index = field.Index;
                ret = ShapeHelper.InsertNewField(shp, field.FieldName, field.FieldType, field.Precision,
                     field.Width, ref index);
                field.Index = index;
                if (!ret)
                {
                    break;
                }
            }
            //shp.StopEditingTable(ret, null) //对table改为非编辑状态，会影响其下的shape，导致锁死shape，无法编辑shape
            return ret;
        }

        public bool InsertItems(List<ShpItem> itemList)
        {
            bool ret = false;
            shp.StartEditingShapes(true, null);
            foreach (ShpItem item in itemList)
            {
                int index = item.ShapeIndex;
                ret = shp.EditInsertShape(item.Shape, ref index);
                item.ShapeIndex = index;
                if (!ret)
                {
                    break;
                }

                // 如果不包含字段值列表
                if (item.FieldValueList == null)
                {
                    continue;
                }
                int fieldIndex = 0;
                foreach (object o in item.FieldValueList)
                {
                    // 保存任一字段值失败即返回
                    if (!shp.EditCellValue(fieldIndex++, item.ShapeIndex, o))
                    {
                        shp.StopEditingShapes(false, true, null);
                        return false;
                    }
                }
            }
            shp.StopEditingShapes(ret, true, null);
            return ret;
        }
    }
}
