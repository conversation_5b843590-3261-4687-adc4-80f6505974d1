﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class LteWeakCellAnaBase : DIYAnalyseByFileBackgroundBase
    {
        protected static readonly object lockObj = new object();

        WeakLteCellCondition cond = new WeakLteCellCondition();
        protected Dictionary<string, LteWeakCellInfo> lteWeakCellInfoDic = new Dictionary<string, LteWeakCellInfo>();
        protected List<LteWeakCellInfo> resultList = new List<LteWeakCellInfo>();
        protected LteWeakCellAnaBase()
            : base(MainModel.GetInstance())
        {
            this.FilterSampleByRegion = true;
            this.IncludeEvent = false;
            this.Columns = new List<string>();
            Columns.Add("lte_RSRP");
            Columns.Add("lte_SINR");
            Columns.Add("lte_TAC");
            Columns.Add("lte_ECI");
            Columns.Add("lte_EARFCN");
            Columns.Add("lte_PCI");
            Columns.Add("lte_RSSI");
            Columns.Add("lte_Pathloss");
        }

        protected LteWeakCellAnaBase(bool isVoLTE)
            : this()
        {
            ServiceTypes.Clear();
            if (isVoLTE)
            {
                ServiceTypes.AddRange(ServiceTypeManager.GetServiceTypesByServiceName(ServiceName.VoLTE));
            }
            else
            {
                ServiceTypes.AddRange(ServiceTypeManager.GetServiceTypesByServiceName(ServiceName.LTE));
            }
        }
        public override string Name
        {
            get { return "弱覆盖小区分析"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22105, this.Name);
        }
        protected override void clearDataBeforeAnalyseFiles()
        {
            lteWeakCellInfoDic.Clear();
            resultList.Clear();
        }

        WeakCellConditionDlg dlg;
        protected override bool getCondition()
        {
            if (dlg == null)
            {
                dlg = new WeakCellConditionDlg();
            }
            dlg.SetCondition(cond);
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            cond = dlg.GetCondition();
            return true;
        }
        protected override void doStatWithQuery()
        {
            try
            {
                foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
                {
                    foreach (TestPoint tp in file.TestPoints)
                    {
                        if (WaitBox.CancelRequest)
                        {
                            return;
                        }
                        LTECell lteCell = tp.GetMainCell_LTE();
                        if (lteCell == null)
                        {
                            continue;
                        }

                        LteWeakCellInfo weakInfo;
                        if (!lteWeakCellInfoDic.TryGetValue(lteCell.Token, out weakInfo))
                        {
                            weakInfo = new LteWeakCellInfo(tp.DistrictID, lteCell);
                            lteWeakCellInfoDic.Add(lteCell.Token, weakInfo);
                        }
                        weakInfo.AddTestPoint(tp);
                    }
                }
            }
            catch (Exception ee)
            {
                MessageBox.Show(ee.Message + Environment.NewLine + ee.Source + Environment.NewLine + ee.StackTrace);
            }
        }
        protected override void getResultsAfterQuery()
        {
            foreach (LteWeakCellInfo info in lteWeakCellInfoDic.Values)
            {
                addValidRes(info);
            }
            lteWeakCellInfoDic.Clear();
        }

        private void addValidRes(LteWeakCellInfo info)
        {
            bool isWeakRsrp = info.RsrpAvg <= cond.RsrpMax;
            bool isWeakSinr = info.SinrAvg <= cond.SinrMax;
            if (cond.IsCheckSinr)
            {
                if (cond.SinrIsAnd)
                {
                    if (isWeakRsrp && isWeakSinr)
                    {
                        addToResult(info);
                    }
                }
                else
                {
                    if (isWeakRsrp || isWeakSinr)
                    {
                        addToResult(info);
                    }
                }
            }
            else if (isWeakRsrp)
            {
                addToResult(info);
            }
        }

        protected void addToResult(LteWeakCellInfo info)
        {
            info.SN = resultList.Count + 1;
            resultList.Add(info);
        }
        protected override void fireShowForm()
        {
            if (resultList.Count == 0)
            {
                System.Windows.Forms.MessageBox.Show("没有符合条件的信息！");
                return;
            }
            WeakCellInfoForm frm = MainModel.CreateResultForm(typeof(WeakCellInfoForm)) as WeakCellInfoForm;
            frm.FillData(resultList, this.Name);
            frm.Owner = MainModel.MainForm;
            frm.Visible = true;
            frm.BringToFront();
            mainModel.FireSetDefaultMapSerialTheme("lte_RSRP");
        }
    }

    public class LteWeakCellAnaByRegion : LteWeakCellAnaBase
    {
        private static LteWeakCellAnaByRegion instance = null;
        public static LteWeakCellAnaByRegion GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new LteWeakCellAnaByRegion();
                    }
                }
            }
            return instance;
        }

        protected LteWeakCellAnaByRegion()
            : base(false)
        {
        }
        public LteWeakCellAnaByRegion(bool isVoLTE)
            : base(isVoLTE)
        {
        }
        public override string Name
        {
            get { return "弱覆盖小区分析(按区域)"; }
        }
    }
    public class LteWeakCellAnaByFile : LteWeakCellAnaBase
    {
        private static LteWeakCellAnaByFile intance = null;
        public static LteWeakCellAnaByFile GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new LteWeakCellAnaByFile();
                    }
                }
            }
            return intance;
        }

        protected LteWeakCellAnaByFile()
            : base(false)
        {
        }
        public LteWeakCellAnaByFile(bool isVoLTE)
            : base(isVoLTE)
        {
        }
        public override string Name
        {
            get { return "弱覆盖小区分析(按文件)"; }
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.File;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                return true;
            }
            return false;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        /// <summary>
        /// 判断是否在所选区域内
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            return true;
        }
    }
}
