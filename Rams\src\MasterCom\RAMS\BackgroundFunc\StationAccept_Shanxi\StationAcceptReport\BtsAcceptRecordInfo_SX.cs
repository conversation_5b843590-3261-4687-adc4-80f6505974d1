﻿using MasterCom.RAMS.Net;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.BackgroundFunc.StationAccept_Shanxi
{
    public class BtsAcceptRecordInfo_SX<U>
    {
        public BtsAcceptRecordInfo_SX(StationAcceptReportInfo info)
        {
            DistrictName = info.DistrictName;
            BtsName = info.BtsName;
            ENodeBID = info.ENodeBID;
            CoverTypeDesc = info.CoverTypeDesc;
            IsBtsPassAccept = info.IsBtsPassAccept;
            BtsAcceptErrorInfo = info.BtsAcceptErrorInfo;
            ExcelPath = info.ExcelPath;
            UpdateTime = info.UpdateTime;
        }

        public BtsAcceptRecordInfo_SX(BtsAcceptWorkParamBase<U> btsInfo)
        {
            DistrictName = btsInfo.DistrictName;
            BtsName = btsInfo.BtsNameFull;
            ENodeBID = btsInfo.ENodeBID;
            CoverTypeDesc = btsInfo.CoverScene;
            foreach (var cellInfo in btsInfo.CellWorkParamDic)
            {
                CellAcceptRecordInfo_SX recordInfo = new CellAcceptRecordInfo_SX(cellInfo.Value);
                CellRecordInfoDic.Add(cellInfo.Key, recordInfo);
            }
            CellRecordInfoList = new List<CellAcceptRecordInfo_SX>(CellRecordInfoDic.Values);
        }

        public string DistrictName { get; set; }
        public string BtsName { get; set; }
        public int ENodeBID { get; set; }
        public string CoverTypeDesc { get; set; }

        public bool IsBtsPassAccept { get; set; } = false;
        public string BtsAcceptErrorInfo { get; set; }
        public string ExcelPath { get; set; }
        public DateTime UpdateTime { get; set; }

        public string IsBtsPassAcceptDesc { get { return IsBtsPassAccept ? "是" : "否"; } }

        public Dictionary<U, CellAcceptRecordInfo_SX> CellRecordInfoDic { get; set; } = new Dictionary<U, CellAcceptRecordInfo_SX>();
        public List<CellAcceptRecordInfo_SX> CellRecordInfoList { get; set; }

        public void Calculate()
        {
            IsBtsPassAccept = true;
            foreach (var cellInfo in CellRecordInfoDic.Values)
            {
                cellInfo.Calculate();
                if (!cellInfo.IsCellPassAccept)
                {
                    IsBtsPassAccept = false;
                }
            }

            if (!string.IsNullOrEmpty(BtsAcceptErrorInfo))
            {
                IsBtsPassAccept = false;
            }
        }

        //public void FillDataByDB(Package package)
        //{
        //    DistrictName = package.Content.GetParamString();
        //    BtsName = package.Content.GetParamString();
        //    ENodeBID = package.Content.GetParamInt();
        //    CoverTypeDesc = package.Content.GetParamString();

        //    string cellName = package.Content.GetParamString();
        //    int cellID = package.Content.GetParamInt();
        //    CellAcceptRecordInfo_SX cellInfo;
        //    if(!CellRecordInfoDic.TryGetValue(cellID, out cellInfo))
        //    {
        //        cellInfo = new CellAcceptRecordInfo_SX(cellID, cellName);
        //        CellRecordInfoDic.Add(cellID, cellInfo);

        //        cellInfo.IsCellPassAccept = Convert.ToBoolean(package.Content.GetParamInt());
        //        cellInfo.CellAcceptErrorInfo = package.Content.GetParamString();
        //    }

        //    IsBtsPassAccept = Convert.ToBoolean(package.Content.GetParamInt());
        //    BtsAcceptErrorInfo = package.Content.GetParamString();
        //    ExcelPath = package.Content.GetParamString();
        //    UpdateTime = Convert.ToDateTime(package.Content.GetParamString());
        //}
    }

    public class CellAcceptRecordInfo_SX
    {
        public CellAcceptRecordInfo_SX(StationAcceptReportInfo info)
        {
            CellID = info.CellID;
            CellName = info.CellName;
            IsCellPassAccept = info.IsCellPassAccept;
            CellAcceptErrorInfo = info.CellAcceptErrorInfo;
        }

        public CellAcceptRecordInfo_SX(CellAcceptWorkParamBase cellInfo)
        {
            CellID = cellInfo.CellID;
            CellName = cellInfo.CellNameFull;
        }

        public int CellID { get; set; }
        public string CellName { get; set; }

        public bool IsCellPassAccept { get; set; } = false;
        public string CellAcceptErrorInfo { get; set; }

        public string IsCellPassAcceptDesc { get { return IsCellPassAccept ? "是" : "否"; } }

        public void Calculate()
        {
            IsCellPassAccept = true;
            if (!string.IsNullOrEmpty(CellAcceptErrorInfo))
            {
                IsCellPassAccept = false;
            }
        }
    }

    public class UpLoadAcceptRecord_SX<U> : DiySqlMultiNonQuery
    {
        readonly BtsAcceptRecordInfo_SX<U> btsRecordInfo;
        readonly string tableName;
        public UpLoadAcceptRecord_SX(BtsAcceptRecordInfo_SX<U> recordInfo, NetType type)
            : base()
        {
            MainDB = true;
            this.btsRecordInfo = recordInfo;
            tableName = ParamsHelper.GetRecordTableName(type);
        }

        protected override string getSqlTextString()
        {
            StringBuilder strb = new StringBuilder();
            strb.Append($"delete from {tableName} where ENodeBID = {btsRecordInfo.ENodeBID};");
            foreach (var cellRecordInfo in btsRecordInfo.CellRecordInfoList)
            {
                strb.Append($"insert into [{tableName}] " +
                    $"([DistrictName],[BtsName],[ENodeBID],[CoverTypeDes],[CellName],[CellID]," +
                    $"[IsBtsPassAccept],[BtsErrorInfo],[IsCellPassAccept],[CellErrorInfo]," +
                    $"[ExcelPath],[UpdateTime]) values " +
                    $"('{btsRecordInfo.DistrictName}'," +
                    $"'{btsRecordInfo.BtsName}'," +
                    $"{btsRecordInfo.ENodeBID}," +
                    $"'{btsRecordInfo.CoverTypeDesc}'," +
                    $"'{cellRecordInfo.CellName}'," +
                    $"{cellRecordInfo.CellID}," +
                    $"{Convert.ToInt32(btsRecordInfo.IsBtsPassAccept)}," +
                    $"'{btsRecordInfo.BtsAcceptErrorInfo}'," +
                    $"{Convert.ToInt32(cellRecordInfo.IsCellPassAccept)},"+
                    $"'{cellRecordInfo.CellAcceptErrorInfo}'" +
                    $",'{btsRecordInfo.ExcelPath}'" +
                    $",'{DateTime.Now}');");
            }
            return strb.ToString();
        }
    }
}
