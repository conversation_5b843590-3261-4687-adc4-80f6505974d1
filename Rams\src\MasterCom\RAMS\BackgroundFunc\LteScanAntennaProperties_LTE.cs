﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc;

namespace MasterCom.RAMS.Func.SystemSetting
{
    public partial class LteScanAntennaProperties_LTE : PropertiesControl
    {
        private DevExpress.XtraEditors.GroupControl groupControl1;
        private DevExpress.XtraEditors.GroupControl groupControl;
        private DevExpress.XtraEditors.CheckEdit chkBackgroundStat;
        private GroupBox groupBox1;
        private CheckBox chbDtNb;
        private GroupBox groupBox2;
        private CheckBox chbSample;
        private CheckBox chbSecAna;
        private CheckBox chbCsvFile;
        private Label label1;
        private TextBox tbPath;
        private Button btnBrowse;

        private readonly ZTLteScanAntenna queryFunc;

        public LteScanAntennaProperties_LTE(ZTLteScanAntenna queryFunc)
        {
            InitializeComponent();
            this.queryFunc = queryFunc;
        }

        public override string ParentName
        {
            get { return queryFunc.FuncType.ToString(); }
        }

        public override string ParentSubName
        {
            get { return queryFunc.SubFuncType.ToString(); }
        }

        public override string SelfName
        {
            get { return queryFunc.Name; }
        }

        public override string TabPageName
        {
            get { return queryFunc.Name; }
        }

        public override void Flush()
        {
            chkBackgroundStat.Checked = queryFunc.BackgroundStat;
            chbDtNb.Enabled = true;
            tbPath.Text = Application.StartupPath + "\\userData";
            chbCsvFile.Enabled = false;
            tbPath.Enabled = false;
            btnBrowse.Enabled = false;

        }

        public override bool IsValid()
        {
            return true;
        }

        public override void Apply()
        {
            queryFunc.BackgroundStat = chkBackgroundStat.Checked;
            queryFunc.BDtNbInfo = chbDtNb.Checked;
            queryFunc.BSecAna = chbSecAna.Checked;
            queryFunc.BSecDataExport = chbCsvFile.Checked;
            queryFunc.BSampleShow = chbSample.Checked;
        }

        private void chbSecAna_CheckedChanged(object sender, EventArgs e)
        {
            chbCsvFile.Enabled = chbSecAna.Checked;
        }

        private void chbCsvFile_CheckedChanged(object sender, EventArgs e)
        {
            tbPath.Enabled = chbCsvFile.Checked;
            btnBrowse.Enabled = chbCsvFile.Checked;
        }
        private void btnBrowse_Click(object sender, EventArgs e)
        {
            FolderBrowserDialog fbd = new FolderBrowserDialog();
            if (fbd.ShowDialog() == DialogResult.OK)
            {
                tbPath.Text = fbd.SelectedPath;
            }
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.chbSample = new System.Windows.Forms.CheckBox();
            this.chbSecAna = new System.Windows.Forms.CheckBox();
            this.chbCsvFile = new System.Windows.Forms.CheckBox();
            this.label1 = new System.Windows.Forms.Label();
            this.tbPath = new System.Windows.Forms.TextBox();
            this.btnBrowse = new System.Windows.Forms.Button();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.chbDtNb = new System.Windows.Forms.CheckBox();
            this.groupControl = new DevExpress.XtraEditors.GroupControl();
            this.chkBackgroundStat = new DevExpress.XtraEditors.CheckEdit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            this.groupBox2.SuspendLayout();
            this.groupBox1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl)).BeginInit();
            this.groupControl.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chkBackgroundStat.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // groupControl1
            // 
            this.groupControl1.Controls.Add(this.groupBox2);
            this.groupControl1.Controls.Add(this.groupBox1);
            this.groupControl1.Location = new System.Drawing.Point(3, 62);
            this.groupControl1.Name = "groupControl1";
            this.groupControl1.Size = new System.Drawing.Size(448, 272);
            this.groupControl1.TabIndex = 5;
            this.groupControl1.Text = "条件设置";
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.chbSample);
            this.groupBox2.Controls.Add(this.chbSecAna);
            this.groupBox2.Controls.Add(this.chbCsvFile);
            this.groupBox2.Controls.Add(this.label1);
            this.groupBox2.Controls.Add(this.tbPath);
            this.groupBox2.Controls.Add(this.btnBrowse);
            this.groupBox2.Location = new System.Drawing.Point(17, 119);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(384, 125);
            this.groupBox2.TabIndex = 13;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "结果集设定";
            // 
            // chbSample
            // 
            this.chbSample.AutoSize = true;
            this.chbSample.Checked = true;
            this.chbSample.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chbSample.Location = new System.Drawing.Point(8, 24);
            this.chbSample.Name = "chbSample";
            this.chbSample.Size = new System.Drawing.Size(86, 18);
            this.chbSample.TabIndex = 12;
            this.chbSample.Text = "采样点回放";
            this.chbSample.UseVisualStyleBackColor = true;
            // 
            // chbSecAna
            // 
            this.chbSecAna.AutoSize = true;
            this.chbSecAna.Location = new System.Drawing.Point(8, 48);
            this.chbSecAna.Name = "chbSecAna";
            this.chbSecAna.Size = new System.Drawing.Size(98, 18);
            this.chbSecAna.TabIndex = 11;
            this.chbSecAna.Text = "二维数据分析";
            this.chbSecAna.UseVisualStyleBackColor = true;
            this.chbSecAna.CheckedChanged += new System.EventHandler(this.chbSecAna_CheckedChanged);
            // 
            // chbCsvFile
            // 
            this.chbCsvFile.AutoSize = true;
            this.chbCsvFile.Location = new System.Drawing.Point(110, 48);
            this.chbCsvFile.Name = "chbCsvFile";
            this.chbCsvFile.Size = new System.Drawing.Size(98, 18);
            this.chbCsvFile.TabIndex = 3;
            this.chbCsvFile.Text = "二维数据导出";
            this.chbCsvFile.UseVisualStyleBackColor = true;
            this.chbCsvFile.CheckedChanged += new System.EventHandler(this.chbCsvFile_CheckedChanged);
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(6, 73);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(67, 14);
            this.label1.TabIndex = 8;
            this.label1.Text = "文件路径：";
            // 
            // tbPath
            // 
            this.tbPath.Location = new System.Drawing.Point(71, 70);
            this.tbPath.Name = "tbPath";
            this.tbPath.Size = new System.Drawing.Size(214, 22);
            this.tbPath.TabIndex = 9;
            // 
            // btnBrowse
            // 
            this.btnBrowse.Anchor = (System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right);
            this.btnBrowse.Location = new System.Drawing.Point(314, 70);
            this.btnBrowse.Name = "btnBrowse";
            this.btnBrowse.Size = new System.Drawing.Size(58, 23);
            this.btnBrowse.TabIndex = 10;
            this.btnBrowse.Text = "浏览";
            this.btnBrowse.UseVisualStyleBackColor = true;
            this.btnBrowse.Click += new System.EventHandler(this.btnBrowse_Click);
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.chbDtNb);
            this.groupBox1.Location = new System.Drawing.Point(17, 35);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(384, 64);
            this.groupBox1.TabIndex = 12;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "数据源设定";
            // 
            // chbDtNb
            // 
            this.chbDtNb.AutoSize = true;
            this.chbDtNb.Location = new System.Drawing.Point(10, 29);
            this.chbDtNb.Name = "chbDtNb";
            this.chbDtNb.Size = new System.Drawing.Size(146, 18);
            this.chbDtNb.TabIndex = 12;
            this.chbDtNb.Text = "联合邻区数据（路测）";
            this.chbDtNb.UseVisualStyleBackColor = true;
            // 
            // groupControl
            // 
            this.groupControl.Controls.Add(this.chkBackgroundStat);
            this.groupControl.Location = new System.Drawing.Point(3, 3);
            this.groupControl.Name = "groupControl";
            this.groupControl.Size = new System.Drawing.Size(448, 53);
            this.groupControl.TabIndex = 4;
            this.groupControl.Text = "开关设置";
            // 
            // chkBackgroundStat
            // 
            this.chkBackgroundStat.Location = new System.Drawing.Point(15, 26);
            this.chkBackgroundStat.Name = "chkBackgroundStat";
            this.chkBackgroundStat.Properties.Caption = "启用";
            this.chkBackgroundStat.Size = new System.Drawing.Size(75, 19);
            this.chkBackgroundStat.TabIndex = 0;
            // 
            // LteScanAntennaProperties_LTE
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.groupControl1);
            this.Controls.Add(this.groupControl);
            this.Name = "LteScanAntennaProperties_LTE";
            this.Size = new System.Drawing.Size(454, 337);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl)).EndInit();
            this.groupControl.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.chkBackgroundStat.Properties)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

    }
}
