﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.Util;
using MasterCom.RAMS.NOP;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc.LteLowDLSpeedAna
{
    public partial class LowDLSpeedResultForm : MinCloseForm
    {
        public LowDLSpeedResultForm()
            : base()
        {
            InitializeComponent();
            this.gv.DoubleClick += gv_DoubleClick;
        }

        void gv_DoubleClick(object sender, EventArgs e)
        {
            if (this.gv.GetSelectedRows().Length <= 0)
            {
                return;
            }

            MainModel.FireSetDefaultMapSerialTheme("LTE_TDD:APP_Speed_Mb");
            CDLowSpeedESResult lowSpeedItem = gv.GetRow(gv.GetSelectedRows()[0]) as CDLowSpeedESResult;
            if (lowSpeedItem != null)
            {
                mModel.DTDataManager.Clear();
                foreach (TestPoint tp in lowSpeedItem.TestPoints)
                {
                    mModel.DTDataManager.Add(tp);
                }
                mModel.FireDTDataChanged(this);
            }


            ProcRoutineManager.Instance.Init();
            TaskFlowDiagramForm frm = MainModel.CreateResultForm(typeof(TaskFlowDiagramForm)) as TaskFlowDiagramForm;
            string eventName =  "FTP Download_连续低速率_50米_20M_CoverBest";
            int districtID = 1;

            if (lowSpeedItem != null)
            {
                TaskEventItem taskItem = new TaskEventItem(eventName, districtID, lowSpeedItem.FileID, lowSpeedItem.EventID, lowSpeedItem.SeqID, lowSpeedItem.EventTime);
                taskItem.ESResultInfo = lowSpeedItem;
                frm.TaskEventItem = taskItem;
            }
            frm.Visible = true;
            frm.BringToFront();
        }

        public void FillData(List<CDLowSpeedESResult> results)
        {
            gridCtrl.DataSource = results;
            gridCtrl.RefreshDataSource();
        }

        private void miExport2Xls_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(gv);
        }
    }
}
