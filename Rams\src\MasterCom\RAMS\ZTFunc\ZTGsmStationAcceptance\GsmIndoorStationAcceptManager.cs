﻿using System;
using System.Collections.Generic;
using System.Text;
using System.IO;

using MasterCom.Util.UiEx;
using MasterCom.RAMS.Model;
using Excel = Microsoft.Office.Interop.Excel;
using MasterCom.RAMS.Func;
using System.Drawing;

namespace MasterCom.RAMS.ZTFunc
{
    public class GsmIndoorStationAcceptManager : GsmStationAcceptManager
    {
        public override void SetAcceptCond(string saveFolder)
        {
            this.saveFolder = saveFolder;
            GsmStationAcceptBase.MaxDeviceID = 0;
            this.acceptorList = new List<GsmStationAcceptBase>()
            {
                new GsmIndoorAcpVoice(),
                new GsmIndoorAcpGPRSAttachRate(),
                new GsmIndoorAcpPingRate(),
                new GsmIndoorAcpFtpDownload(),
                new GsmIndoorAcpWithInstationHandOver(),
                new GsmIndoorAcpBetweenstationHandOver(),
                new GsmIndoorAcpHomePage(),
            };
        }
        
        public override string GetTargetFile(string btsName, int cellCount, string saveFolder)
        {
            if (cellCount > 6)
            {
                throw (new Exception(string.Format("基站{0}小区数超过6个，不支持报告导出", btsName)));
            }

            string templateFile = "新疆中移GSM搬迁工程优化单站验证(室分)-XXXX站-ZGM-模板.xlsx";
            templateFile = Path.Combine(workDir, templateFile);

            string targetFile = string.Format("GSM搬迁站验收_{0}.xlsx", btsName);
            targetFile = Path.Combine(saveFolder, targetFile);
            if (File.Exists(targetFile))
            {
                File.Delete(targetFile);
            }
            File.Copy(templateFile, targetFile);
            return targetFile;
        }

        private static readonly string workDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "userdata/GsmIndoorStationAcceptance");
    }
}
