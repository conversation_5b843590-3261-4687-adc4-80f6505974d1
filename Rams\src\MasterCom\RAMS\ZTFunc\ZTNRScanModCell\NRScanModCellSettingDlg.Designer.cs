﻿
namespace MasterCom.RAMS.ZTFunc.ZTNRScanModCell
{
    partial class NRScanModCellSettingDlg
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.btnOK = new System.Windows.Forms.Button();
            this.btnCancel = new System.Windows.Forms.Button();
            this.numMaxSinr = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.numSampleCount = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl8 = new DevExpress.XtraEditors.LabelControl();
            this.numDiffRxlev = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl7 = new DevExpress.XtraEditors.LabelControl();
            this.numMinRxlev = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl6 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl5 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl9 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl10 = new DevExpress.XtraEditors.LabelControl();
            this.cmbModType = new DevExpress.XtraEditors.ComboBoxEdit();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxSinr.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSampleCount.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numDiffRxlev.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMinRxlev.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cmbModType.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // btnOK
            // 
            this.btnOK.Location = new System.Drawing.Point(73, 235);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(87, 27);
            this.btnOK.TabIndex = 38;
            this.btnOK.Text = "确定";
            this.btnOK.UseVisualStyleBackColor = true;
            // 
            // btnCancel
            // 
            this.btnCancel.Location = new System.Drawing.Point(168, 235);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(87, 27);
            this.btnCancel.TabIndex = 37;
            this.btnCancel.Text = "取消";
            this.btnCancel.UseVisualStyleBackColor = true;
            // 
            // numMaxSinr
            // 
            this.numMaxSinr.EditValue = new decimal(new int[] {
            6,
            0,
            0,
            0});
            this.numMaxSinr.Location = new System.Drawing.Point(136, 70);
            this.numMaxSinr.Name = "numMaxSinr";
            this.numMaxSinr.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numMaxSinr.Properties.Appearance.Options.UseFont = true;
            this.numMaxSinr.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numMaxSinr.Properties.IsFloatValue = false;
            this.numMaxSinr.Properties.Mask.EditMask = "N00";
            this.numMaxSinr.Properties.MaxValue = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.numMaxSinr.Properties.MinValue = new decimal(new int[] {
            100,
            0,
            0,
            -2147483648});
            this.numMaxSinr.Size = new System.Drawing.Size(105, 20);
            this.numMaxSinr.TabIndex = 36;
            // 
            // labelControl2
            // 
            this.labelControl2.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl2.Appearance.Options.UseFont = true;
            this.labelControl2.Location = new System.Drawing.Point(82, 73);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(36, 12);
            this.labelControl2.TabIndex = 35;
            this.labelControl2.Text = "SINR≤";
            // 
            // numSampleCount
            // 
            this.numSampleCount.EditValue = new decimal(new int[] {
            50,
            0,
            0,
            0});
            this.numSampleCount.Location = new System.Drawing.Point(136, 153);
            this.numSampleCount.Name = "numSampleCount";
            this.numSampleCount.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numSampleCount.Properties.Appearance.Options.UseFont = true;
            this.numSampleCount.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numSampleCount.Properties.IsFloatValue = false;
            this.numSampleCount.Properties.Mask.EditMask = "N00";
            this.numSampleCount.Properties.MaxValue = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numSampleCount.Size = new System.Drawing.Size(105, 20);
            this.numSampleCount.TabIndex = 34;
            // 
            // labelControl1
            // 
            this.labelControl1.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl1.Appearance.Options.UseFont = true;
            this.labelControl1.Location = new System.Drawing.Point(40, 156);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(72, 12);
            this.labelControl1.TabIndex = 33;
            this.labelControl1.Text = "总采样点数≥";
            // 
            // labelControl8
            // 
            this.labelControl8.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl8.Appearance.Options.UseFont = true;
            this.labelControl8.Location = new System.Drawing.Point(248, 114);
            this.labelControl8.Name = "labelControl8";
            this.labelControl8.Size = new System.Drawing.Size(12, 12);
            this.labelControl8.TabIndex = 32;
            this.labelControl8.Text = "dB";
            // 
            // numDiffRxlev
            // 
            this.numDiffRxlev.EditValue = new decimal(new int[] {
            12,
            0,
            0,
            0});
            this.numDiffRxlev.Location = new System.Drawing.Point(136, 111);
            this.numDiffRxlev.Name = "numDiffRxlev";
            this.numDiffRxlev.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numDiffRxlev.Properties.Appearance.Options.UseFont = true;
            this.numDiffRxlev.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numDiffRxlev.Properties.IsFloatValue = false;
            this.numDiffRxlev.Properties.Mask.EditMask = "N00";
            this.numDiffRxlev.Properties.MaxValue = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.numDiffRxlev.Properties.MinValue = new decimal(new int[] {
            100,
            0,
            0,
            -2147483648});
            this.numDiffRxlev.Size = new System.Drawing.Size(105, 20);
            this.numDiffRxlev.TabIndex = 28;
            // 
            // labelControl7
            // 
            this.labelControl7.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl7.Appearance.Options.UseFont = true;
            this.labelControl7.Location = new System.Drawing.Point(54, 114);
            this.labelControl7.Name = "labelControl7";
            this.labelControl7.Size = new System.Drawing.Size(60, 12);
            this.labelControl7.TabIndex = 31;
            this.labelControl7.Text = "相对覆盖≤";
            // 
            // numMinRxlev
            // 
            this.numMinRxlev.EditValue = new decimal(new int[] {
            105,
            0,
            0,
            -2147483648});
            this.numMinRxlev.Location = new System.Drawing.Point(136, 29);
            this.numMinRxlev.Name = "numMinRxlev";
            this.numMinRxlev.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numMinRxlev.Properties.Appearance.Options.UseFont = true;
            this.numMinRxlev.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numMinRxlev.Properties.IsFloatValue = false;
            this.numMinRxlev.Properties.Mask.EditMask = "N00";
            this.numMinRxlev.Properties.MaxValue = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.numMinRxlev.Properties.MinValue = new decimal(new int[] {
            140,
            0,
            0,
            -2147483648});
            this.numMinRxlev.Size = new System.Drawing.Size(105, 20);
            this.numMinRxlev.TabIndex = 27;
            // 
            // labelControl6
            // 
            this.labelControl6.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl6.Appearance.Options.UseFont = true;
            this.labelControl6.Location = new System.Drawing.Point(248, 34);
            this.labelControl6.Name = "labelControl6";
            this.labelControl6.Size = new System.Drawing.Size(18, 12);
            this.labelControl6.TabIndex = 30;
            this.labelControl6.Text = "dBm";
            // 
            // labelControl5
            // 
            this.labelControl5.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl5.Appearance.Options.UseFont = true;
            this.labelControl5.Location = new System.Drawing.Point(54, 36);
            this.labelControl5.Name = "labelControl5";
            this.labelControl5.Size = new System.Drawing.Size(60, 12);
            this.labelControl5.TabIndex = 29;
            this.labelControl5.Text = "最强信号≥";
            // 
            // labelControl9
            // 
            this.labelControl9.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl9.Appearance.Options.UseFont = true;
            this.labelControl9.Location = new System.Drawing.Point(247, 194);
            this.labelControl9.Name = "labelControl9";
            this.labelControl9.Size = new System.Drawing.Size(24, 12);
            this.labelControl9.TabIndex = 41;
            this.labelControl9.Text = "干扰";
            // 
            // labelControl10
            // 
            this.labelControl10.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl10.Appearance.Options.UseFont = true;
            this.labelControl10.Location = new System.Drawing.Point(64, 194);
            this.labelControl10.Name = "labelControl10";
            this.labelControl10.Size = new System.Drawing.Size(48, 12);
            this.labelControl10.TabIndex = 40;
            this.labelControl10.Text = "自定义模";
            // 
            // cmbModType
            // 
            this.cmbModType.EditValue = "3";
            this.cmbModType.Location = new System.Drawing.Point(135, 190);
            this.cmbModType.Name = "cmbModType";
            this.cmbModType.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cmbModType.Properties.Items.AddRange(new object[] {
            "3",
            "4",
            "6"});
            this.cmbModType.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            this.cmbModType.Size = new System.Drawing.Size(105, 21);
            this.cmbModType.TabIndex = 39;
            // 
            // NRScanModCellSettingDlg
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(316, 287);
            this.Controls.Add(this.labelControl9);
            this.Controls.Add(this.labelControl10);
            this.Controls.Add(this.cmbModType);
            this.Controls.Add(this.btnOK);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.numMaxSinr);
            this.Controls.Add(this.labelControl2);
            this.Controls.Add(this.numSampleCount);
            this.Controls.Add(this.labelControl1);
            this.Controls.Add(this.labelControl8);
            this.Controls.Add(this.numDiffRxlev);
            this.Controls.Add(this.labelControl7);
            this.Controls.Add(this.numMinRxlev);
            this.Controls.Add(this.labelControl6);
            this.Controls.Add(this.labelControl5);
            this.Name = "NRScanModCellSettingDlg";
            this.Text = "NR小区模间干扰查询设置";
            ((System.ComponentModel.ISupportInitialize)(this.numMaxSinr.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSampleCount.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numDiffRxlev.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMinRxlev.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cmbModType.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.Button btnOK;
        private System.Windows.Forms.Button btnCancel;
        private DevExpress.XtraEditors.SpinEdit numMaxSinr;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.SpinEdit numSampleCount;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.LabelControl labelControl8;
        private DevExpress.XtraEditors.SpinEdit numDiffRxlev;
        private DevExpress.XtraEditors.LabelControl labelControl7;
        private DevExpress.XtraEditors.SpinEdit numMinRxlev;
        private DevExpress.XtraEditors.LabelControl labelControl6;
        private DevExpress.XtraEditors.LabelControl labelControl5;
        private DevExpress.XtraEditors.LabelControl labelControl9;
        private DevExpress.XtraEditors.LabelControl labelControl10;
        private DevExpress.XtraEditors.ComboBoxEdit cmbModType;
    }
}