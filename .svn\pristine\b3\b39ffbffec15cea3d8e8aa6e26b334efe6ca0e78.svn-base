﻿using System;
using System.Collections.Generic;
using System.Text;
using AxMapWinGIS;
using MapWinGIS;
using MasterCom.MTGis;

namespace MasterCom.RAMS.MapControlTool
{
    public class MapControlToolAddCircle
    {
        public event EventHandler CirCleCreated;
        private readonly AxMap mapControl;
        private Shapefile sf;
        private int hLayer = -1;
        private bool isActive = false;
        private Shape cirCle { get; set; }

        public MapControlToolAddCircle(AxMap mapControl)
        {
            this.mapControl = mapControl;
        }

        public void FireCireCreated()
        {
            if (CirCleCreated != null)
            {
                CirCleCreated(this, EventArgs.Empty);
            }
        }

        public void Activate()
        {
            if (!isActive)
            {
                mapControl.MouseUpEvent += new AxMapWinGIS._DMapEvents_MouseUpEventHandler(mapControl_MouseUpEvent);
                mapControl.SelectBoxFinal += new AxMapWinGIS._DMapEvents_SelectBoxFinalEventHandler(mapControl_SelectBoxFinnal);
            }
            isActive = true;
            mapControl.CursorMode = tkCursorMode.cmSelection;
            mapControl.MapCursor = tkCursor.crsrCross;
            addCircleLayer();
        }

        public void Deactivate()
        {
            if (!isActive) return;
            mapControl.SelectBoxFinal -= mapControl_SelectBoxFinnal;
            mapControl.MouseUpEvent -= mapControl_MouseUpEvent;
            isActive = false;
        }

        public void Clear()
        {
            sf.EditClear();
            cirCle = null;
        }

        private void mapControl_MouseUpEvent(object sender, _DMapEvents_MouseUpEvent e)
        {
            if (e.button == 2) return;
            sf.EditClear();
            cirCle = null;
            FireCireCreated();
        }

        private void mapControl_SelectBoxFinnal(object sender, _DMapEvents_SelectBoxFinalEvent e)
        {
            if (e.bottom == e.top || e.left == e.right) return;
            double ltLong = 0;
            double ltLat = 0;
            double brLong = 0;
            double brLat = 0;
            mapControl.PixelToProj(e.left, e.top, ref ltLong, ref ltLat);
            mapControl.PixelToProj(e.right, e.bottom, ref brLong, ref brLat);
            double radius = MasterCom.Util.MathFuncs.GetDistance(ltLong, ltLat, brLong, brLat);
            createCircle(new DbPoint(ltLong, ltLat), radius);
        }

        private void createCircle(DbPoint centerPoint, double radius)
        {
            sf.EditClear();
            int shpIdx = 0;
            cirCle = ShapeHelper.CreateCircleShape(centerPoint.x, centerPoint.y, radius);
            sf.EditInsertShape(cirCle, ref shpIdx);
            int pos = mapControl.get_LayerPosition(hLayer);
            mapControl.MoveLayerTop(pos);
            FireCireCreated();
        }

        public void CreateCircle(DbPoint centerPoint, double radius)
        {
            sf.EditClear();
            if (radius == 0)
            {
                cirCle = null;
                FireCireCreated();
            }
            else
            {
                createCircle(centerPoint, radius);
            }
        }

        private void addCircleLayer()
        {
            if (sf == null)
            {
                sf = new Shapefile();
                sf.CreateNew("", ShpfileType.SHP_POLYGON);
                sf.DefaultDrawingOptions.LineWidth = 3;
                sf.DefaultDrawingOptions.FillVisible = true;
                sf.DefaultDrawingOptions.LineColor = (uint)System.Drawing.ColorTranslator.ToOle(System.Drawing.Color.Green);
                sf.DefaultDrawingOptions.FillTransparency = 35;
            }
            Shapefile shpFile = mapControl.get_Shapefile(hLayer);
            if (shpFile != sf)
            {
                hLayer = mapControl.AddLayer(sf, true);
            }
        }
    }
}
