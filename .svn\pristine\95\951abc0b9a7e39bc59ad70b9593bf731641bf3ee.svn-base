﻿using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRMobileServiceAnaBase : DIYAnalyseFilesOneByOneByRegion
    {
        public NRMobileServiceEvent Events { get; set; } = new NRMobileServiceEvent();
        public List<NRMobileServiceAnaFileItem> resultList { get; set; } = new List<NRMobileServiceAnaFileItem>();    //保存结果

        public NRMobileServiceAnaBase(MainModel mainModel)
            : base(mainModel)
        {
            this.IncludeEvent = true;
            this.IncludeMessage = true;

            Columns = NRTpHelper.InitBaseReplayParamBackground(false, false);
            Columns.Add("NR_APP_Speed");
        }

        protected override void clearDataBeforeAnalyseFiles()
        {
            resultList = new List<NRMobileServiceAnaFileItem>();
        }

        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager fileMng in MainModel.DTDataManager.FileDataManagers)
            {
                NRMobileServiceAnaFileItem fileItem = new NRMobileServiceAnaFileItem(fileMng.FileName);

                List<DTData> dtDataList = new List<DTData>();

                foreach (TestPoint tp in fileMng.TestPoints)
                {
                    dtDataList.Add((DTData)tp);
                }

                addEvtToDtDataList(fileMng, dtDataList);
                dtDataList.Sort(comparer);

                //开始分析事件
                NRMobileServiceAnaItem anaItem = new NRMobileServiceAnaItem(Events);

                for (int i = 0; i < dtDataList.Count; i++)
                {
                    anaItem = analyseDtDataList(fileMng, fileItem, dtDataList, anaItem, i);
                }

                addToResultList(anaItem, fileItem); //最后一个

                if (fileItem.AnaList.Count > 1)
                {
                    fileItem.SN = resultList.Count + 1;
                    resultList.Add(fileItem);
                }
            }
        }

        private NRMobileServiceAnaItem analyseDtDataList(DTFileDataManager fileMng, NRMobileServiceAnaFileItem fileItem, List<DTData> dtDataList, NRMobileServiceAnaItem anaItem, int i)
        {
            if (dtDataList[i] is TestPoint)
            {
                if (anaItem.IsGotBegin && !anaItem.IsGotEnd)  //有开始，尚未结束，记录中间的采样点信息
                {
                    anaItem.AddTpInfo(dtDataList[i] as TestPoint);
                }
            }
            else if (dtDataList[i] is Event)
            {
                Event evt = dtDataList[i] as Event;
                if (evt.ID == Events.structEvents.FTPDownloadBegan           //开始事件
                    || evt.ID == Events.structEvents.HTTPPageRequest
                    || evt.ID == Events.structEvents.HTTPDownloadBegan
                    || evt.ID == Events.structEvents.VIDEOPlayRequest)
                {
                    if (anaItem.IsGotBegin)         //前一个结果，加入列表中
                    {
                        addToResultList(anaItem, fileItem);
                    }

                    anaItem = new NRMobileServiceAnaItem(Events);  //重新初始化
                    string URL = ZTUrlAnalyzer.GetURL(evt.SN, fileMng.Messages);
                    anaItem.AddFirstEvtInfo(evt, URL);
                }
                else  //其它事件
                {
                    anaItem.AddOtherEvtInfo(evt);
                }
            }

            return anaItem;
        }

        private void addEvtToDtDataList(DTFileDataManager fileMng, List<DTData> dtDataList)
        {
            foreach (Event evt in fileMng.Events)
            {
                if (evt.ID == Events.structEvents.FTPDownloadBegan
                    || evt.ID == Events.structEvents.FTPDownloadFirstData
                    || evt.ID == Events.structEvents.FTPDownloadSuccess
                    || evt.ID == Events.structEvents.FTPDownloadDrop
                    || evt.ID == Events.structEvents.FTPDownloadUnFinished
                    || evt.ID == Events.structEvents.HTTPPageRequest
                    || evt.ID == Events.structEvents.HTTPPageDisplaySuccess
                    || evt.ID == Events.structEvents.HTTPPageDisplayFailure
                    || evt.ID == Events.structEvents.HTTPPageComplete
                    || evt.ID == Events.structEvents.HTTPPageIncomplete
                    || evt.ID == Events.structEvents.HTTPPageFail
                    || evt.ID == Events.structEvents.VIDEOPlayRequest
                    || evt.ID == Events.structEvents.VIDEOPlayFirstData
                    || evt.ID == Events.structEvents.VIDEOPlayRebufferStart
                    || evt.ID == Events.structEvents.VIDEOPlayRebufferEnd
                    || evt.ID == Events.structEvents.VIDEOPlayLastData
                    || evt.ID == Events.structEvents.VIDEOPlayDrop
                    || evt.ID == Events.structEvents.VIDEOPlayReproductionStart
                    || evt.ID == Events.structEvents.VIDEOPlayReproductionStartFailure
                    || evt.ID == Events.structEvents.HTTPDownloadBegan
                    || evt.ID == Events.structEvents.HTTPDownloadSuccess
                    || evt.ID == Events.structEvents.HTTPDownloadDrop
                    || evt.ID == Events.structEvents.HTTPDownloadFail)
                {
                    dtDataList.Add(evt);
                }
            }
        }

        private void addToResultList(NRMobileServiceAnaItem anaItem, NRMobileServiceAnaFileItem fileItem)
        {
            //没有开始，不处理
            if (!anaItem.IsGotBegin)
            {
                return;
            }

            if (!anaItem.IsGotEnd)
            {
                anaItem.Result = "无结论";  //存在没有结束事件的情况
            }

            anaItem.SN = fileItem.AnaList.Count + 1;
            fileItem.AnaList.Add(anaItem);
        }

        /// <summary>
        /// 显示结果
        /// </summary>
        protected override void fireShowForm()
        {
            if (resultList.Count == 0)
            {
                System.Windows.Forms.MessageBox.Show("没有符合条件的信息！");
                return;
            }

            NRMobileServiceAnaForm frm = MainModel.CreateResultForm(typeof(NRMobileServiceAnaForm)) as NRMobileServiceAnaForm;
            frm.FillData(resultList);
            frm.Owner = MainModel.MainForm;
            frm.Visible = true;
            frm.BringToFront();
        }

        protected override void releaseSource()
        {
            resultList = null;
        }

        private Comparer comparer { get; set; } = new Comparer();
        private class Comparer : IComparer<DTData>
        {
            public int Compare(DTData x, DTData y)
            {
                return x.SN - y.SN;
            }
        }
    }

    class NRMobileServiceAnaByFile : NRMobileServiceAnaBase
    {
        public NRMobileServiceAnaByFile(MainModel mainModel)
            : base(mainModel)
        {

        }
        private static NRMobileServiceAnaByFile instance = null;
        protected static readonly object lockObj = new object();
        public static NRMobileServiceAnaByFile GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new NRMobileServiceAnaByFile(MainModel.GetInstance());
                    }
                }
            }
            return instance;
        }

        public override string Name
        {
            get { return "移动互联业务时长分析(按文件)"; }
        }
        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 35000, 35051, this.Name);
        }
        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.File;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }
    }

    class NRMobileServiceAnaByRegion : NRMobileServiceAnaBase
    {
        public NRMobileServiceAnaByRegion(MainModel mm)
            : base(mm)
        {

        }
        private static NRMobileServiceAnaByRegion instance = null;
        protected static readonly object lockObj = new object();
        public static NRMobileServiceAnaByRegion GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new NRMobileServiceAnaByRegion(MainModel.GetInstance());
                    }
                }
            }
            return instance;
        }

        public override string Name
        {
            get { return "移动互联业务时长分析(按区域)"; }
        }
        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22102, this.Name);
        }
        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.Region;
        }
    }
}
