﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class DiyQueryFddDBAntenna : DIYSQLBase
    {
        public List<FddIndoorDBAntenna> DataList { get; private set; }
        readonly int eci;
        readonly FddDatabaseSetting setting;
        public DiyQueryFddDBAntenna(int eci, FddDatabaseSetting setting)
            : base()
        {
            MainDB = true;
            this.eci = eci;
            this.setting = setting;
        }

        public override string Name
        {
            get
            {
                return "查询FDD单验天线下RSRP";
            }
        }

        /// <summary>
        /// 查询ECI对应最新的一个工单数据
        /// </summary>
        /// <returns></returns>
        protected override string getSqlTextString()
        {
            string tableName = string.Format(@"{0}.{1}.[dbo].[{2}]", setting.ServerIp, setting.DbName, setting.TableNameHead);
            StringBuilder selectSQL = new StringBuilder();
            selectSQL.AppendFormat(@"select [楼层],[天线],[RSRP] from {0} where [工单流水号] = (select top 1 [工单流水号] from {0} where CI = {1} order by [工单流水号] desc)",
            tableName, eci);
            return selectSQL.ToString();
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            int idx = 0;
            E_VType[] rType = new E_VType[3];
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx] = E_VType.E_Float;
            return rType;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            DataList = new List<FddIndoorDBAntenna>();
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    FddIndoorDBAntenna data = new FddIndoorDBAntenna();
                    data.FillData(package);
                    DataList.Add(data);
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }
    }

    public class FddIndoorDBAntenna
    {
        public string Floor { get; set; }
        public int Type { get; set; }
        public string AntennaType { get; set; }
        public double Rsrp { get; set; }

        public void FillData(Package package)
        {
            Floor = package.Content.GetParamString();
            AntennaType = package.Content.GetParamString();
            Rsrp = package.Content.GetParamFloat();

            string antennaType = AntennaType.Substring(AntennaType.IndexOf('-') + 1);
            int type;
            int.TryParse(antennaType, out type);
            Type = type;
        }
    }

    public class FddIndoorDBAntennaResult
    {
        public FddIndoorDBAntennaResult()
        {
            //初始化20个天线型号
            for (int i = 1; i <= 20; i++)
            {
                List<FddIndoorDBAntenna> typeList = new List<FddIndoorDBAntenna>();
                FloorData.Add(i, typeList);
            }
        }

        /// <summary>
        /// 天线型号,天线数据
        /// </summary>
        public Dictionary<int, List<FddIndoorDBAntenna>> FloorData { get; set; } = new Dictionary<int, List<FddIndoorDBAntenna>>();
        public string Floor { get; set; }
        public List<FddIndoorDBAntenna> AvgFloorData { get; set; } = new List<FddIndoorDBAntenna>();

        public void CalculateData()
        {
            foreach (List<FddIndoorDBAntenna> floorData in FloorData.Values)
            {
                for (int i = 0; i < floorData.Count; i++)
                {
                    if (floorData[i].Rsrp > -140 && floorData[i].Rsrp < -40)
                    {
                        AvgFloorData.Add(floorData[i]);
                        break;
                    }
                }
            }

            AvgFloorData.Sort((a, b) => a.Type.CompareTo(b.Type));
        }
    }
}
