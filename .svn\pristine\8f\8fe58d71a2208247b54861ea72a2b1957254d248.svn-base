using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using System.IO;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.Grid;
using MasterCom.MControls;
using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using MasterCom.RAMS.Net;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTScanLTEBestRxlevListForm : MinCloseForm
    {
        List<ScanLTEFreqRxlev> resultList = new List<ScanLTEFreqRxlev>();
        int sampleTotal = 0;

        public ZTScanLTEBestRxlevListForm(MainModel mainModel)
            : base(mainModel)
        {
            InitializeComponent();
            mapForm = mainModel.MainForm.GetMapForm();
            init();
        }
        private MapForm mapForm = null;

        private void init()
        {
            this.olvColumnFreq.AspectGetter = delegate(object row)
            {
                if (row is ScanLTEFreqRxlev)
                {
                    ScanLTEFreqRxlev item = row as ScanLTEFreqRxlev;
                    return item.freq;
                }
                return null;
            };

            this.olvColumnSample.AspectGetter = delegate(object row)
            {
                if (row is ScanLTEFreqRxlev)
                {
                    ScanLTEFreqRxlev item = row as ScanLTEFreqRxlev;
                    return item.sample;
                }
                return "";
            };

            this.olvColumnRate.AspectGetter = delegate(object row)
            {
                if (row is ScanLTEFreqRxlev)
                {
                    ScanLTEFreqRxlev item = row as ScanLTEFreqRxlev;
                    return Math.Round(100*item.sample /(float)this.sampleTotal, 2).ToString();
                }
                return "";
            };

            this.olvColumnMaxRxlev.AspectGetter = delegate(object row)
            {
                if (row is ScanLTEFreqRxlev)
                {
                    ScanLTEFreqRxlev item = row as ScanLTEFreqRxlev;
                    return item.maxRxlev;
                }
                return "";
            };

            this.olvColumnMinRxlev.AspectGetter = delegate(object row)
            {
                if (row is ScanLTEFreqRxlev)
                {
                    ScanLTEFreqRxlev item = row as ScanLTEFreqRxlev;
                    return item.minRxlev;
                }
                return "";
            };

            this.olvColumnAvgRxlev.AspectGetter = delegate(object row)
            {
                if (row is ScanLTEFreqRxlev)
                {
                    ScanLTEFreqRxlev item = row as ScanLTEFreqRxlev;
                    return Math.Round(item.avgTotal / item.sample, 2).ToString();
                }
                return "";
            };


            this.olvColumnAboveBenchRxlev.AspectGetter = delegate(object row)
            {
                if (row is ScanLTEFreqRxlev)
                {
                    ScanLTEFreqRxlev item = row as ScanLTEFreqRxlev;
                    return Math.Round(100 * item.aboveSample / item.sample, 2).ToString();
                }
                return "";
            };
        }

        public void FillData(Dictionary<int, ScanLTEFreqRxlev> dicFreqAll, int sampleTotal)
        {
            ListViewFreqRxlev.RebuildColumns();
            ListViewFreqRxlev.ClearObjects();

            resultList = new List<ScanLTEFreqRxlev>();
            this.sampleTotal = sampleTotal;

            foreach (int freq in dicFreqAll.Keys)
            {
                resultList.Add(dicFreqAll[freq]);
            }

            ListViewFreqRxlev.SetObjects(resultList);
            MainModel.RefreshLegend();
            MapForm mf = MainModel.MainForm.GetMapForm();
            if (mf != null)
            {
                mf.GetCellLayer().Invalidate();
            }
        }

        private void listViewTotal_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            if (ListViewFreqRxlev.SelectedObject is ScanLTEInterType)
            {
                ScanLTEInterType interType = ListViewFreqRxlev.SelectedObject as ScanLTEInterType;

                mModel.DTDataManager.Clear();
                foreach (ScanLTEInterference interInfo in interType.typeList)
                {
                    mModel.DTDataManager.Add(interInfo.tp);
                }
                mModel.FireDTDataChanged(this);
            }
            else if (ListViewFreqRxlev.SelectedObject is ScanLTEInterference)
            {
                ScanLTEInterference interInfo = ListViewFreqRxlev.SelectedObject as ScanLTEInterference;
                mModel.DTDataManager.Clear();
                mModel.DTDataManager.Add(interInfo.tp);
                mModel.FireDTDataChanged(this);
            }
        }

        //private void GoToView(ScanLTEInterference interInfo)
        //{
        //    mapForm.GoToView(interInfo.tp.Longitude, interInfo.tp.Latitude);
        //}

        //private void GoToView(List<ScanLTEInterference> typeList)
        //{
        //    double ltLong = 100000;
        //    double ltLat = -100000;
        //    double brLong = -100000;
        //    double brLat = 100000;

        //    foreach (ScanLTEInterference interInfo in typeList)
        //    {
        //        if (interInfo.tp.Longitude < ltLong)
        //        {
        //            ltLong = interInfo.tp.Longitude;
        //        }
        //        if (interInfo.tp.Longitude > brLong)
        //        {
        //            brLong = interInfo.tp.Longitude;
        //        }
        //        if (interInfo.tp.Latitude < brLat)
        //        {
        //            brLat = interInfo.tp.Latitude;
        //        }
        //        if (interInfo.tp.Latitude > ltLat)
        //        {
        //            ltLat = interInfo.tp.Latitude;
        //        }
        //    }
        //    mapForm.GoToView(new DbRect(ltLong, ltLat, brLong, brLat));
        //}

        private void miExpandAll_Click(object sender, EventArgs e)
        {
            ListViewFreqRxlev.ExpandAll();
        }

        private void miCallapsAll_Click(object sender, EventArgs e)
        {
            ListViewFreqRxlev.CollapseAll();
        }

        private void miExportExcel_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(ListViewFreqRxlev);
        }
    }
}