﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.Util;
using DevExpress.XtraGrid.Columns;
using DevExpress.XtraEditors;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NRHnadoverNCellInfoForm : MinCloseForm
    {
        List<NRHandoverNCellInfo> ListHandoverInfos;
        public NRHnadoverNCellInfoForm()
            : base()
        {
            InitializeComponent();
            miExportSimpleExcel.Click += MiExportSimpleExcel_Click;
            gridView1.DoubleClick += GridView_DoubleClick;
            gridView2.DoubleClick += GridView_DoubleClick;

        }

        public void FillData(List<NRHandoverNCellInfo> handoverInfos)
        {
            ListHandoverInfos = handoverInfos;
            gridControl1.DataSource = handoverInfos;
            gridControl1.RefreshDataSource();
            MainModel.DTDataManager.Clear();
            foreach (NRHandoverNCellInfo handoverInfo in handoverInfos)
            {
                FillDTData(handoverInfo);
            }
            MainModel.FireDTDataChanged(this);
        }

        public void GridView_DoubleClick(object sender, EventArgs e)
        {
            MainModel.DTDataManager.Clear();
            DevExpress.XtraGrid.Views.Grid.GridView gv = sender as DevExpress.XtraGrid.Views.Grid.GridView;
            if (gv.GetRow(gv.GetSelectedRows()[0]) is NRHandoverNCellInfo)
            {
                NRHandoverNCellInfo handoverInfo = gv.GetRow(gv.GetSelectedRows()[0]) as NRHandoverNCellInfo;
                FillDTData(handoverInfo);
                MainModel.MainForm.GetMapForm().GoToView(handoverInfo.Longitude, handoverInfo.Latitude, 5000);
            }
            else if (gv.GetRow(gv.GetSelectedRows()[0]) is NRNCellInfo)
            {
                NRNCellInfo ncell = gv.GetRow(gv.GetSelectedRows()[0]) as NRNCellInfo;
                MainModel.MainForm.GetMapForm().GoToView(ncell.Longitude, ncell.Latitude, 5000);
            }
            MainModel.FireDTDataChanged(this);
            MainModel.FireSelectedCellChanged(this);
        }

        private void FillDTData(NRHandoverNCellInfo handoverInfo)
        {
            MainModel.DTDataManager.Add(handoverInfo.Evt);
            foreach (TestPoint tp in handoverInfo.TestPonitList)
            {
                MainModel.DTDataManager.Add(tp);
            }
        }

        public void MiExportSimpleExcel_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(getNPOIRowsByGridView());
        }

        private void miExportSimpleCSV_Click(object sender, EventArgs e)
        {
            OutputCsvFile();
        }

        private List<NPOIRow> getNPOIRowsByGridView()
        {
            List<NPOIRow> rows = new List<NPOIRow>();
            NPOIRow row = new NPOIRow();
            foreach (GridColumn col in this.gridView1.Columns)
            {
                row.AddCellValue(col.Caption);
            }
            foreach (GridColumn col in this.gridView2.Columns)
            {
                row.AddCellValue(col.Caption);
            }
            rows.Add(row);
            for (int i = 0; i < ListHandoverInfos.Count; i++)
            {
                row = new NPOIRow();
                rows.Add(row);
                //row.AddCellValue(ListHandoverInfos[i].SN);
                row.AddCellValue(ListHandoverInfos[i].FileName);
                row.AddCellValue(ListHandoverInfos[i].Longitude);
                row.AddCellValue(ListHandoverInfos[i].Latitude);
                row.AddCellValue(ListHandoverInfos[i].Time);
                row.AddCellValue(ListHandoverInfos[i].CellName);
                row.AddCellValue(ListHandoverInfos[i].Earfcn);
                row.AddCellValue(ListHandoverInfos[i].Pci);
                row.AddCellValue(ListHandoverInfos[i].TAC);
                row.AddCellValue(ListHandoverInfos[i].NCI);
                row.AddCellValue(ListHandoverInfos[i].Rsrp);
                for (int j = 0; j < ListHandoverInfos[i].NcellList.Count; j++)
                {
                    NPOIRow subRow = new NPOIRow();
                    row.AddSubRow(subRow);
                    subRow.AddCellValue(ListHandoverInfos[i].NcellList[j].SN);
                    subRow.AddCellValue(ListHandoverInfos[i].NcellList[j].CellName);
                    subRow.AddCellValue(ListHandoverInfos[i].NcellList[j].Earfcn);
                    subRow.AddCellValue(ListHandoverInfos[i].NcellList[j].Pci);
                    subRow.AddCellValue(ListHandoverInfos[i].NcellList[j].TAC);
                    subRow.AddCellValue(ListHandoverInfos[i].NcellList[j].NCI);
                    subRow.AddCellValue(ListHandoverInfos[i].NcellList[j].Rsrp);
                }
            }
            return rows;
        }

        public void OutputCsvFile()
        {
            SaveFileDialog saveDialog = new SaveFileDialog();
            saveDialog.Filter = FilterHelper.Csv;
            saveDialog.RestoreDirectory = true;
            if (saveDialog.ShowDialog() != DialogResult.OK)
            {
                return;
            }
            string strFileSubName = saveDialog.FileName;
            try
            {
                if (System.IO.File.Exists(strFileSubName))
                    System.IO.File.Delete(strFileSubName);

                System.IO.FileStream fileStream = new System.IO.FileStream(strFileSubName, System.IO.FileMode.CreateNew, System.IO.FileAccess.Write, System.IO.FileShare.Read);
                System.IO.StreamWriter streamWriter = new System.IO.StreamWriter(fileStream, Encoding.Default);

                StringBuilder sb = new StringBuilder();
                foreach (GridColumn col in this.gridView1.Columns)
                {
                    sb.Append(col.Caption + ",");
                }
                foreach (GridColumn col in this.gridView2.Columns)
                {
                    sb.Append(col.Caption + ",");
                }
                streamWriter.WriteLine(sb.ToString());

                setCellInfo(streamWriter);

                streamWriter.Close();
                fileStream.Close();

                if (DialogResult.Yes == XtraMessageBox.Show("CSV文件保存完毕，是否现在打开?", "打开文件", MessageBoxButtons.YesNo))  //添加了自动打开文件的提示
                {
                    System.Diagnostics.Process.Start(strFileSubName);
                }
            }
            catch
            {
                XtraMessageBox.Show("打开失败!\r\n文件名:" + strFileSubName);
            }
        }

        private void setCellInfo(System.IO.StreamWriter streamWriter)
        {
            for (int iCount = 0; iCount < ListHandoverInfos.Count; iCount++)
            {
                StringBuilder sb1 = new StringBuilder();
                StringBuilder sb2 = new StringBuilder();
                sb2.Append(",,,,,,,,,");

                //sb1.Append(ListHandoverInfos[iCount].SN + ",");
                sb1.Append(ListHandoverInfos[iCount].FileName + ",");
                sb1.Append(ListHandoverInfos[iCount].Longitude + ",");
                sb1.Append(ListHandoverInfos[iCount].Latitude + ",");
                sb1.Append(ListHandoverInfos[iCount].Time + ",");
                sb1.Append(ListHandoverInfos[iCount].CellName + ",");
                sb1.Append(ListHandoverInfos[iCount].Earfcn + ",");
                sb1.Append(ListHandoverInfos[iCount].Pci + ",");
                sb1.Append(ListHandoverInfos[iCount].Rsrp + ",");

                if (ListHandoverInfos[iCount].NcellList.Count > 0)
                {
                    setNbCellInfo(streamWriter, iCount, sb1, sb2);
                }
                else
                {
                    streamWriter.WriteLine(sb1.ToString());
                }
            }
        }

        private void setNbCellInfo(System.IO.StreamWriter streamWriter, int iCount, StringBuilder sb1, StringBuilder sb2)
        {
            for (int j = 0; j < ListHandoverInfos[iCount].NcellList.Count; j++)
            {
                StringBuilder sb3 = new StringBuilder();
                sb3.Append(ListHandoverInfos[iCount].NcellList[j].SN + ",");
                sb3.Append(ListHandoverInfos[iCount].NcellList[j].CellName + ",");
                sb3.Append(ListHandoverInfos[iCount].NcellList[j].Earfcn + ",");
                sb3.Append(ListHandoverInfos[iCount].NcellList[j].Pci + ",");
                sb3.Append(ListHandoverInfos[iCount].NcellList[j].Rsrp + ",");
                if (j == 0)
                {
                    streamWriter.WriteLine(sb1.ToString() + sb3);
                }
                else
                {
                    streamWriter.WriteLine(sb2.ToString() + sb3);
                }
            }
        }
    }
}
