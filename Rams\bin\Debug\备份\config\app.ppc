<?xml version="1.0"?>
<!--PerformanceSystem：网络类型； SystemName：网络名称；-->
<!--PerformanceTable：数据库表； ShowName：显示名称； TableName：表名-->
<!--PerformanceField：数据库表里的字段； ShowName：显示名称； FiledName：字段名称； FiledType：字段类型（E_Float为Float类型，E_String为字符类型）-->
<!--For 小区性能参数 By V -->
<Configs>
	<Config name="Common">
		<Item name="PerformanceSystems" typeName="IList">
			<Item typeName="PerformanceSystem">
				<Item name="SystemName" typeName="String">GSM</Item>
				<Item name="PerformanceTables" typeName="IList">
					<Item typeName="PerformanceTable">
						<Item name="ShowName" typeName="String">CCCH性能数据</Item>
						<Item name="TableName" typeName="String">tb_para_Cell_CCCH</Item>
						<Item name="PerformanceFields" typeName="IList">
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">AGCH溢出次数</Item>
								<Item name="FieldName" typeName="String">CS_AGCH_OVERLOAD_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">EGPRS上行LLC层流量</Item>
								<Item name="FieldName" typeName="String">PS_EGPRS_UL_LLC_PL</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">EGPRS下行LLC层流量</Item>
								<Item name="FieldName" typeName="String">PS_EGPRS_DL_LLC_PL</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">GPRS上行LLC层流量</Item>
								<Item name="FieldName" typeName="String">PS_GPRS_UL_LLC_PL</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">GPRS下行LLC层流量</Item>
								<Item name="FieldName" typeName="String">PS_GPRS_DL_LLC_PL</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">PCH-AGCH平均队列长度</Item>
								<Item name="FieldName" typeName="String">CS_AVE_PCH_AGCH_STACK_LEN_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">PCH寻呼尝试次数</Item>
								<Item name="FieldName" typeName="String">CS_PCH_PAG_ATT_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">PDTCH的平均队列长度</Item>
								<Item name="FieldName" typeName="String">PS_AVE_PDTCH_STACK_LEN_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">从PCH队列中丢弃的寻呼消息数</Item>
								<Item name="FieldName" typeName="String">CS_PCH_STACK_LOSST_PAG_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">立即指配成功次数</Item>
								<Item name="FieldName" typeName="String">CS_IMM_ASS_SUCC_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">立即指配拒绝次数</Item>
								<Item name="FieldName" typeName="String">CS_IMM_ASS_REJ_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">随机接入尝试次数</Item>
								<Item name="FieldName" typeName="String">CS_RAACCE_ATT_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">寻呼消息发送尝试次数</Item>
								<Item name="FieldName" typeName="String">CS_PAG_SEND_ATT_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">寻呼消息无响应次数</Item>
								<Item name="FieldName" typeName="String">CS_PAG_NO_RESPONSE_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">寻呼溢出次数</Item>
								<Item name="FieldName" typeName="String">CS_PAG_OVERLOAD_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
						</Item>
					</Item>
					<Item typeName="PerformanceTable">
						<Item name="ShowName" typeName="String">PDCH性能数据</Item>
						<Item name="TableName" typeName="String">tb_para_Cell_PDCH</Item>
						<Item name="PerformanceFields" typeName="IList">
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">每小区所有TBF占用PDCH总和</Item>
								<Item name="FieldName" typeName="String">CS_PER_CELL_ALL_TBF_ACT_PDCH_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">EGPRS-PDCH占用的数量</Item>
								<Item name="FieldName" typeName="String">PS_EGPRS-PDCH_ACT_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">PDCH承载效率</Item>
								<Item name="FieldName" typeName="String">PS_PDCH_BUR_EFF</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">PDCH分配请求次数</Item>
								<Item name="FieldName" typeName="String">PS_PDCH_ASSIG_ASK_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">PDCH分配失败次数</Item>
								<Item name="FieldName" typeName="String">PS_PDCH_ASSIG_FAIL_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">PDCH平均可用数</Item>
								<Item name="FieldName" typeName="String">PS_AVE_PDCH_AVA_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">TCH争夺PDCH次数</Item>
								<Item name="FieldName" typeName="String">CS_TCH_SCRAMBLE_PDCH_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">占用的PDCH的平均数目</Item>
								<Item name="FieldName" typeName="String">PS_AVE_PDCH_ACT_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">占用的PDCH的最大数目</Item>
								<Item name="FieldName" typeName="String">PS_MAX_PDCH_ACT_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
						</Item>
					</Item>
					<Item typeName="PerformanceTable">
						<Item name="ShowName" typeName="String">RLC层性能数据</Item>
						<Item name="TableName" typeName="String">tb_para_Cell_RLC</Item>
						<Item name="PerformanceFields" typeName="IList">
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">确认模式下PS域上行RLC层传输块数</Item>
								<Item name="FieldName" typeName="String">PS_VER_UL_RLC_BLOCK_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">确认模式下PS域上行RLC层重传块数</Item>
								<Item name="FieldName" typeName="String">PS_VER_UL_RLC_BLOCK_RETRANS_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">确认模式下PS域下行RLC层传输块数</Item>
								<Item name="FieldName" typeName="String">PS_VER_DL_RLC_BLOCK_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">确认模式下PS域下行RLC层重传块数</Item>
								<Item name="FieldName" typeName="String">PS_VER_DL_RLC_BLOCK_RETRANS_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">上行RLC层流量</Item>
								<Item name="FieldName" typeName="String">PS_UL_RLC_PL</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">上行RLC层重传流量</Item>
								<Item name="FieldName" typeName="String">PS_RLC_UL_RETRANS_PL</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">下行RLC层流量</Item>
								<Item name="FieldName" typeName="String">PS_DL_RLC_PL</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">下行RLC层重传流量</Item>
								<Item name="FieldName" typeName="String">PS_DL_RETRANS_PL</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">下行RLC空数据块个数</Item>
								<Item name="FieldName" typeName="String">PS_RLC_DL_BLOCK_NULL_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">上行RLC层流量（CS1编码）</Item>
								<Item name="FieldName" typeName="String">PS_UL RLC_CS1_PL</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">上行RLC层流量（CS2编码）</Item>
								<Item name="FieldName" typeName="String">PS_UL RLC_CS2_PL</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">上行RLC层流量（CS3编码）</Item>
								<Item name="FieldName" typeName="String">PS_UL RLC_CS3_PL</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">上行RLC层流量（CS4编码）</Item>
								<Item name="FieldName" typeName="String">PS_UL RLC_CS4_PL</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">上行RLC层流量（MCS1编码）</Item>
								<Item name="FieldName" typeName="String">PS_UL RLC_MCS1_PL</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">上行RLC层流量（MCS2编码）</Item>
								<Item name="FieldName" typeName="String">PS_UL RLC_MCS2_PL</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">上行RLC层流量（MCS3编码）</Item>
								<Item name="FieldName" typeName="String">PS_UL RLC_MCS3_PL</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">上行RLC层流量（MCS4编码）</Item>
								<Item name="FieldName" typeName="String">PS_UL RLC_MCS4_PL</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">上行RLC层流量（MCS5编码）</Item>
								<Item name="FieldName" typeName="String">PS_UL RLC_MCS5_PL</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">上行RLC层流量（MCS6编码）</Item>
								<Item name="FieldName" typeName="String">PS_UL RLC_MCS6_PL</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">上行RLC层流量（MCS7编码）</Item>
								<Item name="FieldName" typeName="String">PS_UL RLC_MCS7_PL</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">上行RLC层流量（MCS8编码）</Item>
								<Item name="FieldName" typeName="String">PS_UL RLC_MCS8_PL</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">上行RLC层流量（MCS9编码）</Item>
								<Item name="FieldName" typeName="String">PS_UL RLC_MCS9_PL</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">上行RLC层数据块个数（CS1编码）</Item>
								<Item name="FieldName" typeName="String">PS_RLC_UL_BLOCK(CS1)_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">上行RLC层数据块个数（CS2编码）</Item>
								<Item name="FieldName" typeName="String">PS_RLC_UL_BLOCK(CS2)_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">上行RLC层数据块个数（CS3编码）</Item>
								<Item name="FieldName" typeName="String">PS_RLC_UL_BLOCK(CS3)_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">上行RLC层数据块个数（CS4编码）</Item>
								<Item name="FieldName" typeName="String">PS_RLC_UL_BLOCK(CS4)_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">上行RLC数据块个数(MCS1编码)</Item>
								<Item name="FieldName" typeName="String">PS_RLC_UL_BLOCK(MCS1)_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">上行RLC数据块个数(MCS2编码)</Item>
								<Item name="FieldName" typeName="String">PS_RLC_UL_BLOCK(MCS2)_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">上行RLC数据块个数(MCS3编码)</Item>
								<Item name="FieldName" typeName="String">PS_RLC_UL_BLOCK(MCS3)_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">上行RLC数据块个数(MCS4编码)</Item>
								<Item name="FieldName" typeName="String">PS_RLC_UL_BLOCK(MCS4)_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">上行RLC数据块个数(MCS5编码)</Item>
								<Item name="FieldName" typeName="String">PS_RLC_UL_BLOCK(MCS5)_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">上行RLC数据块个数(MCS6编码)</Item>
								<Item name="FieldName" typeName="String">PS_RLC_UL_BLOCK(MCS6)_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">上行RLC数据块个数(MCS7编码)</Item>
								<Item name="FieldName" typeName="String">PS_RLC_UL_BLOCK(MCS7)_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">上行RLC数据块个数(MCS8编码)</Item>
								<Item name="FieldName" typeName="String">PS_RLC_UL_BLOCK(MCS8)_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">上行RLC数据块个数(MCS9编码)</Item>
								<Item name="FieldName" typeName="String">PS_RLC_UL_BLOCK(MCS9)_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">上行RLC信令块个数</Item>
								<Item name="FieldName" typeName="String">PS_UL_RLC_SIGNALING_BLOCK_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">上行链路无线数据块重传数（EGPRS）</Item>
								<Item name="FieldName" typeName="String">PS_EGPRS_UL_RLC_BLOCK_RETRANS_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">上行链路无线数据块重传数（GPRS）</Item>
								<Item name="FieldName" typeName="String">PS_GPRS_UL_RLC_BLOCK_RETRANS_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">下行RLC层流量（CS1编码）</Item>
								<Item name="FieldName" typeName="String">PS_DL_RLC_CS1_PL</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">下行RLC层流量（CS2编码）</Item>
								<Item name="FieldName" typeName="String">PS_DL_RLC_CS2_PL</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">下行RLC层流量（CS3编码）</Item>
								<Item name="FieldName" typeName="String">PS_DL_RLC_CS3_PL</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">下行RLC层流量（CS4编码）</Item>
								<Item name="FieldName" typeName="String">PS_DL_RLC_CS4_PL</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">下行RLC层流量（MCS1编码）</Item>
								<Item name="FieldName" typeName="String">PS_DL_RLC_MCS1_PL</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">下行RLC层流量（MCS2编码）</Item>
								<Item name="FieldName" typeName="String">PS_DL_RLC_MCS2_PL</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">下行RLC层流量（MCS3编码）</Item>
								<Item name="FieldName" typeName="String">PS_DL_RLC_MCS3_PL</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">下行RLC层流量（MCS4编码）</Item>
								<Item name="FieldName" typeName="String">PS_DL_RLC_MCS4_PL</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">下行RLC层流量（MCS5编码）</Item>
								<Item name="FieldName" typeName="String">PS_DL_RLC_MCS5_PL</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">下行RLC层流量（MCS6编码）</Item>
								<Item name="FieldName" typeName="String">PS_DL_RLC_MCS6_PL</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">下行RLC层流量（MCS7编码）</Item>
								<Item name="FieldName" typeName="String">PS_DL_RLC_MCS7_PL</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">下行RLC层流量（MCS8编码）</Item>
								<Item name="FieldName" typeName="String">PS_DL_RLC_MCS8_PL</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">下行RLC层流量（MCS9编码）</Item>
								<Item name="FieldName" typeName="String">PS_DL_RLC_MCS9_PL</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">下行RLC层数据块个数（CS1编码）</Item>
								<Item name="FieldName" typeName="String">PS_RLC_DL_BLOCK(CS1)_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">下行RLC层数据块个数（CS2编码）</Item>
								<Item name="FieldName" typeName="String">PS_RLC_DL_BLOCK(CS2)_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">下行RLC层数据块个数（CS3编码）</Item>
								<Item name="FieldName" typeName="String">PS_RLC_DL_BLOCK(CS3)_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">下行RLC层数据块个数（CS4编码）</Item>
								<Item name="FieldName" typeName="String">PS_RLC_DL_BLOCK(CS4)_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">下行RLC数据块个数(MCS1编码)</Item>
								<Item name="FieldName" typeName="String">PS_RLC_DL_BLOCK(MCS1)_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">下行RLC数据块个数(MCS2编码)</Item>
								<Item name="FieldName" typeName="String">PS_RLC_DL_BLOCK(MCS2)_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">下行RLC数据块个数(MCS3编码)</Item>
								<Item name="FieldName" typeName="String">PS_RLC_DL_BLOCK(MCS3)_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">下行RLC数据块个数(MCS4编码)</Item>
								<Item name="FieldName" typeName="String">PS_RLC_DL_BLOCK(MCS4)_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">下行RLC数据块个数(MCS5编码)</Item>
								<Item name="FieldName" typeName="String">PS_RLC_DL_BLOCK(MCS5)_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">下行RLC数据块个数(MCS6编码)</Item>
								<Item name="FieldName" typeName="String">PS_RLC_DL_BLOCK(MCS6)_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">下行RLC数据块个数(MCS7编码)</Item>
								<Item name="FieldName" typeName="String">PS_RLC_DL_BLOCK(MCS7)_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">下行RLC数据块个数(MCS8编码)</Item>
								<Item name="FieldName" typeName="String">PS_RLC_DL_BLOCK(MCS8)_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">下行RLC数据块个数(MCS9编码)</Item>
								<Item name="FieldName" typeName="String">PS_RLC_DL_BLOCK(MCS9)_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">下行RLC信令块个数</Item>
								<Item name="FieldName" typeName="String">PS_DL_RLC_SIGNALING_BLOCK_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">下行链路无线数据块重传数(EGPRS)</Item>
								<Item name="FieldName" typeName="String">PS_EGPRS_DL_RAD_BLOCK_RETRANS_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
						</Item>
					</Item>
					<Item typeName="PerformanceTable">
						<Item name="ShowName" typeName="String">SDCCH性能数据</Item>
						<Item name="TableName" typeName="String">tb_para_Cell_SDCCH</Item>
						<Item name="PerformanceFields" typeName="IList">
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">SDCCH分配成功次数</Item>
								<Item name="FieldName" typeName="String">CS_SDCCH_ASSIG_SUCC_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">SDCCH配置数</Item>
								<Item name="FieldName" typeName="String">CS_SDCCH_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">SDCCH掉话次数</Item>
								<Item name="FieldName" typeName="String">CS_SDCCH_DROP_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">SDCCH分配失败次数</Item>
								<Item name="FieldName" typeName="String">CS_PDCH_ASSIG_FAIL_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">SDCCH话务量</Item>
								<Item name="FieldName" typeName="String">CS_SDCCH_ERL</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">SDCCH试呼次数</Item>
								<Item name="FieldName" typeName="String">CS_SDCCH_REQ_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">SDCCH溢出次数</Item>
								<Item name="FieldName" typeName="String">CS_SDCCH_OVERLOAD_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">SDCCH占用次数</Item>
								<Item name="FieldName" typeName="String">CS_SDCCH_ACT_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">动态SDCCH可用数</Item>
								<Item name="FieldName" typeName="String">CS_DYN_SDCCH_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">短信SDCCH话务量</Item>
								<Item name="FieldName" typeName="String">CS_SMS_SDCCH_ERL</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">短信SDCCH占用次数</Item>
								<Item name="FieldName" typeName="String">CS_SMS_SDCCH_ACT_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">短信对SDCCH试呼次数</Item>
								<Item name="FieldName" typeName="String">CS_SMS_SDCCH_REQ_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">话音业务SDCCH占用次数</Item>
								<Item name="FieldName" typeName="String">CS_VC_SDCCH_ACT_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">话音业务对SDCCH话务量</Item>
								<Item name="FieldName" typeName="String">CS_VC_SDCCH_ERL</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">话音业务对SDCCH试呼次数</Item>
								<Item name="FieldName" typeName="String">CS_VC_SDCCH_REQ_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">静态SDCCH可用数</Item>
								<Item name="FieldName" typeName="String">CS_STATIC_SDCCH_AVA_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
						</Item>
					</Item>
					<Item typeName="PerformanceTable">
						<Item name="ShowName" typeName="String">TBF性能数据</Item>
						<Item name="TableName" typeName="String">tb_para_Cell_TBF</Item>
						<Item name="PerformanceFields" typeName="IList">
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">TBF溢出次数(由于无线拥塞)</Item>
								<Item name="FieldName" typeName="String">PS_TBF_OVERLOAD_DUETO_RIADO_BLOCHING_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">上行TBF拥塞次数</Item>
								<Item name="FieldName" typeName="String">PS_UL_TBF_BLOCKING_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">下行TBF拥塞次数</Item>
								<Item name="FieldName" typeName="String">PS_DL_TBF_BLOCKING_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">上行EGPRS-TBF建立尝试次数</Item>
								<Item name="FieldName" typeName="String">PS_EGPRS-TBF_UL_EST_ATT_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">上行EGPRS-TBF建立成功次数</Item>
								<Item name="FieldName" typeName="String">PS_EGPRS-TBF_UL_EST_SUCC_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">上行EGPRS-TBF平均持续时长</Item>
								<Item name="FieldName" typeName="String">PS_UL_EGPRS-TBF_UL_EST_SUCC_TIME</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">上行EGPRS-TBF异常中断次数</Item>
								<Item name="FieldName" typeName="String">PS_UL_EGPRS-TBF_ABNORMAL_DISCONNECT_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">上行TBF成功建立次数</Item>
								<Item name="FieldName" typeName="String">PS_TBF_UL_EST_SUCC_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">上行TBF建立尝试次数</Item>
								<Item name="FieldName" typeName="String">PS_TBF_UL_EST_ATT_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">上行TBF平均持续时长</Item>
								<Item name="FieldName" typeName="String">PS_UL_TBF_UL_EST_SUCC_TIME</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">上行TBF异常中断次数</Item>
								<Item name="FieldName" typeName="String">PS_UL_TBF_ABNORMAL_DISCONNECT_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">上行平均并发EGPRS-TBF数</Item>
								<Item name="FieldName" typeName="String">PS_UL_AVE_COMP_EGPRS_TBF_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">上行平均并发TBF数</Item>
								<Item name="FieldName" typeName="String">PS_UL_AVE_COMP_TBF_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">下行EGPRS-TBF建立尝试次数</Item>
								<Item name="FieldName" typeName="String">PS_EGPRS-TBF_DL_EST_ATT_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">下行EGPRS-TBF建立成功次数</Item>
								<Item name="FieldName" typeName="String">PS_EGPRS-TBF_DL_EST_SUCC_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">下行EGPRS-TBF平均持续时长</Item>
								<Item name="FieldName" typeName="String">PS_DL_EGPRS-TBF_UL_EST_SUCC_TIME</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">下行EGPRS-TBF异常中断次数</Item>
								<Item name="FieldName" typeName="String">PS_DL_EGPRS-TBF_ABNORMAL_DISCONNECT_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">下行TBF成功建立次数</Item>
								<Item name="FieldName" typeName="String">PS_TBF_DL_EST_SUCC_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">下行TBF建立尝试次数</Item>
								<Item name="FieldName" typeName="String">PS_TBF_DL_EST_ATT_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">下行TBF平均持续时长</Item>
								<Item name="FieldName" typeName="String">PS_DL_TBF_UL_EST_SUCC_TIME</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">下行TBF异常中断次数</Item>
								<Item name="FieldName" typeName="String">PS_DL_TBF_ABNORMAL_DISCONNECT_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">下行平均并发EGPRS-TBF数</Item>
								<Item name="FieldName" typeName="String">PS_DL_AVE_COMP_EGPRS_TBF_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">下行平均并发TBF数</Item>
								<Item name="FieldName" typeName="String">PS_DL_AVE_COMP_TBF_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
						</Item>
					</Item>
					<Item typeName="PerformanceTable">
						<Item name="ShowName" typeName="String">TCH性能数据</Item>
						<Item name="TableName" typeName="String">tb_para_Cell_TCH</Item>
						<Item name="PerformanceFields" typeName="IList">
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">TCH信道可用总数</Item>
								<Item name="FieldName" typeName="String">CS_TCH_AVA_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">TCH信道配置数</Item>
								<Item name="FieldName" typeName="String">CS_TCH_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">TCH信道切换掉话次数</Item>
								<Item name="FieldName" typeName="String">CS_TCH_DROP_DUETO_HO_FAIL_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">TCH信道指配次数</Item>
								<Item name="FieldName" typeName="String">CS_TCH_ASSIG_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">半速率话音信道话务量</Item>
								<Item name="FieldName" typeName="String">CS_HR_CH_ERL</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">半速率话音信道可用数</Item>
								<Item name="FieldName" typeName="String">CS_HR_CH_AVA_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">话音信道掉话次数</Item>
								<Item name="FieldName" typeName="String">CS_VC_CH_DROP_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">话音信道分配失败次数（不含切换）</Item>
								<Item name="FieldName" typeName="String">CS_VC_ASSIG_FAIL(NON-HO)_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">话音信道分配失败次数（含切换）</Item>
								<Item name="FieldName" typeName="String">CS_VC_ASSIG_FAIL(HO)_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">话音信道切换试呼次数</Item>
								<Item name="FieldName" typeName="String">CS_VC_HO_REQ_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">话音信道切换占用次数</Item>
								<Item name="FieldName" typeName="String">CS_VC_HO_ACT_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">话音信道试呼次数（不含切换）</Item>
								<Item name="FieldName" typeName="String">CS_VC_HO_REQ(NON-HO)_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">话音信道试呼次数（含切换）</Item>
								<Item name="FieldName" typeName="String">CS_VC_HO_REQ(HO)_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">话音信道溢出次数（不含切换）</Item>
								<Item name="FieldName" typeName="String">CS_VC_CH_OVERLOAD(NON-HO)_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">话音信道溢出次数（含切换）</Item>
								<Item name="FieldName" typeName="String">PS_UL RLC_MCS2_PL</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">CS_VC_CH_OVERLOAD(HO)_NB</Item>
								<Item name="FieldName" typeName="String">PS_UL RLC_MCS3_PL</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">话音信道占用次数（不含切换）</Item>
								<Item name="FieldName" typeName="String">CS_VC_ACT(NON-HO)_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">话音信道占用次数（含切换）</Item>
								<Item name="FieldName" typeName="String">CS_VC_ACT(HO)_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">全速率话音信道话务量</Item>
								<Item name="FieldName" typeName="String">CS_FHR_VC_CH_ELR</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">全速率话音信道可用数</Item>
								<Item name="FieldName" typeName="String">CS_FHR_VC_CH_AVA_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">小区总话务量</Item>
								<Item name="FieldName" typeName="String">CS_CELL_TOTAL_ELR</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">最大话音信道占用数量</Item>
								<Item name="FieldName" typeName="String">CS_MAX_VC_CH_ACT_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
						</Item>
					</Item>
					<Item typeName="PerformanceTable">
						<Item name="ShowName" typeName="String">保持类</Item>
						<Item name="TableName" typeName="String">tb_para_Cell_HoldCall</Item>
						<Item name="PerformanceFields" typeName="IList">
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">BSC间切出成功率(%)</Item>
								<Item name="FieldName" typeName="String">CS_HO_INTER_BSC_SUCC_TO_ADJ_RATE</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">BSC间切入成功率(%)</Item>
								<Item name="FieldName" typeName="String">CS_HO_INTER_BSC_FROM_ADJ_SUCC_RATE</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">DL RLC中CS1流量比例(%)</Item>
								<Item name="FieldName" typeName="String">PS_DL_RLC_CS1_RATE</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">DL RLC中CS2流量比例(%)</Item>
								<Item name="FieldName" typeName="String">PS_DL_RLC_CS2_RATE</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">DL RLC中CS3流量比例(%)</Item>
								<Item name="FieldName" typeName="String">PS_DL_RLC_CS3_RATE</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">DL RLC中CS4流量比例(%)</Item>
								<Item name="FieldName" typeName="String">PS_DL_RLC_CS4_RATE</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">DL RLC中MCS1流量比例(%)</Item>
								<Item name="FieldName" typeName="String">PS_DL_RLC_MCS1_RATE</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">DL RLC中MCS2流量比例(%)</Item>
								<Item name="FieldName" typeName="String">PS_DL_RLC_MCS2_RATE</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">DL RLC中MCS3流量比例(%)</Item>
								<Item name="FieldName" typeName="String">PS_DL_RLC_MCS3_RATE</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">DL RLC中MCS4流量比例(%)</Item>
								<Item name="FieldName" typeName="String">PS_DL_RLC_MCS4_RATE</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">DL RLC中MCS5流量比例(%)</Item>
								<Item name="FieldName" typeName="String">PS_DL_RLC_MCS5_RATE</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">DL RLC中MCS6流量比例(%)</Item>
								<Item name="FieldName" typeName="String">PS_DL_RLC_MCS6_RATE</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">DL RLC中MCS7流量比例(%)</Item>
								<Item name="FieldName" typeName="String">PS_DL_RLC_MCS7_RATE</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">DL RLC中MCS8流量比例(%)</Item>
								<Item name="FieldName" typeName="String">PS_DL_RLC_MCS8_RATE</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">DL RLC中MCS9流量比例(%)</Item>
								<Item name="FieldName" typeName="String">PS_DL_RLC_MCS9_RATE</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">EGPRS流量</Item>
								<Item name="FieldName" typeName="String">PS_EGPRS_PL</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">UL RLC中CS1流量比例(%)</Item>
								<Item name="FieldName" typeName="String">PS_UL RLC_CS1_RATE</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">UL RLC中CS2流量比例(%)</Item>
								<Item name="FieldName" typeName="String">PS_UL RLC_CS2_RATE</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">UL RLC中CS3流量比例(%)</Item>
								<Item name="FieldName" typeName="String">PS_UL RLC_CS3_RATE</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">UL RLC中CS4流量比例(%)</Item>
								<Item name="FieldName" typeName="String">PS_UL RLC_CS4_RATE</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">UL RLC中MCS1流量比例(%)</Item>
								<Item name="FieldName" typeName="String">PS_UL RLC_MCS1_RATE</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">UL RLC中MCS2流量比例(%)</Item>
								<Item name="FieldName" typeName="String">PS_UL RLC_MCS2_RATE</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">UL RLC中MCS3流量比例(%)</Item>
								<Item name="FieldName" typeName="String">PS_UL RLC_MCS3_RATE</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">UL RLC中MCS4流量比例(%)</Item>
								<Item name="FieldName" typeName="String">PS_UL RLC_MCS4_RATE</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">UL RLC中MCS5流量比例(%)</Item>
								<Item name="FieldName" typeName="String">PS_UL RLC_MCS5_RATE</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">UL RLC中MCS6流量比例(%)</Item>
								<Item name="FieldName" typeName="String">PS_UL RLC_MCS6_RATE</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">UL RLC中MCS7流量比例(%)</Item>
								<Item name="FieldName" typeName="String">PS_UL RLC_MCS7_RATE</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">UL RLC中MCS8流量比例(%)</Item>
								<Item name="FieldName" typeName="String">PS_UL RLC_MCS8_RATE</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">UL RLC中MCS9流量比例(%)</Item>
								<Item name="FieldName" typeName="String">PS_UL RLC_MCS9_RATE</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">话务掉话比</Item>
								<Item name="FieldName" typeName="String">CS_ELR_DROP_RATE</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">上行TBF建立成功率(%)</Item>
								<Item name="FieldName" typeName="String">PS_TBF_UL_EST_SUCC_RATE</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">EGPRS RLC层单时隙吞吐率</Item>
								<Item name="FieldName" typeName="String">PS_EGPRS_RLC_SINGLE_TIMESLOT_RAT</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">EGPRS上行TBF建立成功率(%)</Item>
								<Item name="FieldName" typeName="String">PS_EGPRS_UL_TBF_EST_SUCC_RATE</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">EGPRS下行TBF掉线率</Item>
								<Item name="FieldName" typeName="String">PS_EGPRS_DL_TBF_DROP_RATE</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">EGPRS下行TBF建立成功率(%)</Item>
								<Item name="FieldName" typeName="String">PS_EGPRS_DL_TBF_EST_SUCC_RATE</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">EGPRS下行重传率(%)</Item>
								<Item name="FieldName" typeName="String">PS_EGPRS_DL_RETRANS_RATE</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">GPRS RLC层单时隙吞吐率</Item>
								<Item name="FieldName" typeName="String">PS_GPRS_RLC_SINGLE_TIMESLOT_RAT</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">GPRS上行TBF建立成功率(%)</Item>
								<Item name="FieldName" typeName="String">PS_GPRS_UL_TBF_EST_SUCC_RATE</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">GPRS下行TBF掉线率(%)</Item>
								<Item name="FieldName" typeName="String">PS_GPRS_DL_TBF_DROP_RATE</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">GPRS下行TBF建立成功率(%)</Item>
								<Item name="FieldName" typeName="String">PS_GPRS_DL_TBF_EST_SUCC_RATE</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">GPRS下行重传率(%)</Item>
								<Item name="FieldName" typeName="String">PS_GPRS_DL_RETRANS_RATE</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">GSM拥塞率(%)</Item>
								<Item name="FieldName" typeName="String">CS_GSM_BLOCKING_RATE</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">PDCH分配成功率(%)</Item>
								<Item name="FieldName" typeName="String">PS_PDCH_ASSIG_SUCC_RATE</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">PDCH复用度</Item>
								<Item name="FieldName" typeName="String">PS_PDCH_REU_REU</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">RLC层上行EGPRS流量(Mbit)</Item>
								<Item name="FieldName" typeName="String">PS_EGPRS_UL_RLC_PL</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">RLC层上行GPRS流量(Mbit)</Item>
								<Item name="FieldName" typeName="String">PS_GPRS_UL_RLC_PL</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">RLC层下行EGPRS流量(Mbit)</Item>
								<Item name="FieldName" typeName="String">PS_EGPRS_DL_RLC_PL</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">RLC层下行GPRS流量(Mbit)</Item>
								<Item name="FieldName" typeName="String">PS_GPRS_DL_RLC_PL</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">RLC层下行链路无线数据块重传数(EGPRS)</Item>
								<Item name="FieldName" typeName="String">PS_EGPRS_DL_RLC_BLOCK_RETRANS_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">RLC层总块数(EGPRS)</Item>
								<Item name="FieldName" typeName="String">PS_EGPRS_RLC_BLOCK_ALL_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">RLC层总块数(GPRS)</Item>
								<Item name="FieldName" typeName="String">PS_GPRS_RLC_BLOCK_ALL_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">SDCCH拥塞率(%)</Item>
								<Item name="FieldName" typeName="String">CS_SDCCH_BLOCKING_RATE</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">TCH拥塞率（不含切）(%)</Item>
								<Item name="FieldName" typeName="String">CS_TCH_BLOCKING(NON-HO)_RATE</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">TCH拥塞率（含切）(%)</Item>
								<Item name="FieldName" typeName="String">CS_TCH_BLOCKING(HO)_RATE</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">低编码比例(%)</Item>
								<Item name="FieldName" typeName="String">PS_LOW_MCS_RATE</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">干扰带比例(%)</Item>
								<Item name="FieldName" typeName="String">CS_BAND_RATE</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">高编码比例(%)</Item>
								<Item name="FieldName" typeName="String">PS_HIG_MCS_RATE</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">话音信道掉话率(%)(不含切)</Item>
								<Item name="FieldName" typeName="String">CS_VC_CH_DROP(NON_HO)_RATE</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">话音信道掉话率(%)(含切)</Item>
								<Item name="FieldName" typeName="String">CS_VC_CH_DROP(HO)_RATE</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">切换成功率(%)</Item>
								<Item name="FieldName" typeName="String">CS_HO_SUCC_RATE</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">上行EGPRS-TBF建立尝试比例(%)</Item>
								<Item name="FieldName" typeName="String">PS_EGPRS-TBF_UL_EST_ATT_RATE</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">下行EGPRS-TBF建立尝试比例(%)</Item>
								<Item name="FieldName" typeName="String">PS_EGPRS-TBF_DL_EST_ATT_RATE</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">下行TBF掉线率(%)</Item>
								<Item name="FieldName" typeName="String">PS_TBF_DL_EST_DROP_RATE</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">下行TBF建立成功率(%)</Item>
								<Item name="FieldName" typeName="String">PS_TBF_DL_EST_SUCC_RATE</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">下行链路无线数据块重传数(GPRS)</Item>
								<Item name="FieldName" typeName="String">PS_DGPRS_DL_RLC_BLOCK_RETRANS_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">中编码比例(%)</Item>
								<Item name="FieldName" typeName="String">PS_MID_MCS_RATE</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">下行链路无线数据块重传数(EGPRS)</Item>
								<Item name="FieldName" typeName="String">PS_EGPRS_DL_RLC_BLOCK_RETRANS_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
						</Item>
					</Item>
					<Item typeName="PerformanceTable">
						<Item name="ShowName" typeName="String">分切换原因的小区切换性能数据</Item>
						<Item name="TableName" typeName="String">tb_para_Cell_HandOverByCause</Item>
						<Item name="PerformanceFields" typeName="IList">
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">由MSC发出的切出该小区的切换请求 (找到更好的小区) 次数</Item>
								<Item name="FieldName" typeName="String">CS_HO_REQ_DUETO_MSC_TO_ADJ_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">由MSC发出的切入该小区的切换请求 (找到更好的小区) 次数</Item>
								<Item name="FieldName" typeName="String">CS_HO_REQ_DUETO_MSC_FROM_ADJ_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">由上行链路信号电平过低引起的切换请求次数</Item>
								<Item name="FieldName" typeName="String">CS_HO_ERQ_DEUTO_POOR_UL_LEV_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">由下行链路信号电平过低引起的切换请求次数</Item>
								<Item name="FieldName" typeName="String">CS_HO_ERQ_DEUTO_POOR_DL_LEV_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">由于功率算法选择更好的小区引起的切换请求次数</Item>
								<Item name="FieldName" typeName="String">CS_HO_REQ_DEUTO_PBGT_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">由于上行链路干扰而引起的小区内切换请求次数</Item>
								<Item name="FieldName" typeName="String">CS_HO_INTRA_CELL_REQ_DUETO_UL_INTERFERENCE_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">由于上行链路质量不足引起的切换请求次数</Item>
								<Item name="FieldName" typeName="String">CS_HO_REQ_DEUTO_POOR_UL_QUAL_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">由于手机距BTS过远而引起的切出该小区的切换请求次数</Item>
								<Item name="FieldName" typeName="String">CS_HO_TO_ADJ_DUETO_TA_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">由于手机距原BTS过远而引起的切入该小区的切换请求次数</Item>
								<Item name="FieldName" typeName="String">CS_HO_FROM_ADJ_DUETO_TA_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">由于手机未能占上新的信道而造成的切入MSC小区的失败次数</Item>
								<Item name="FieldName" typeName="String">CS_HO_FAIL_TO_MSC_CELL_DUETO_NEW_CH_FAIL_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">由于下行链路干扰而引起的小区内切换请求次数</Item>
								<Item name="FieldName" typeName="String">CS_HO_INTRA_CELL_REQ_DUETO_DL_INTERFERENCE_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">由于下行链路质量不足引起的切换请求次数</Item>
								<Item name="FieldName" typeName="String">CS_HO_REQ_DEUTO_POOR_DL_QUAL_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">由于小区内没有信道而引起的BSC内小区切入失败次数</Item>
								<Item name="FieldName" typeName="String">CS_HO_FROM_ADJ_FAIL_INTRA_BSC_DEUTO_NO_CH_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">占用新信道引起的小区内切换失败数</Item>
								<Item name="FieldName" typeName="String">CS_HO_FAIL_DUE_TO_ACT_NEW_CH_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
						</Item>
					</Item>
					<Item typeName="PerformanceTable">
						<Item name="ShowName" typeName="String">干扰频段</Item>
						<Item name="TableName" typeName="String">tb_para_Cell_Interference</Item>
						<Item name="PerformanceFields" typeName="IList">
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">干扰频带1</Item>
								<Item name="FieldName" typeName="String">CS_BAND1_RATE</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">干扰频带2</Item>
								<Item name="FieldName" typeName="String">CS_BAND2_RATE</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">干扰频带3</Item>
								<Item name="FieldName" typeName="String">CS_BAND3_RATE</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">干扰频带4</Item>
								<Item name="FieldName" typeName="String">CS_BAND4_RATE</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">干扰频带5</Item>
								<Item name="FieldName" typeName="String">CS_BAND5_RATE</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
						</Item>
					</Item>
					<Item typeName="PerformanceTable">
						<Item name="ShowName" typeName="String">接入类</Item>
						<Item name="TableName" typeName="String">tb_para_Cell_AccessCall</Item>
						<Item name="PerformanceFields" typeName="IList">
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">SDCCH分配成功率(%)</Item>
								<Item name="FieldName" typeName="String">CS_SDCCH_ASSIG_SUCC_RATE</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">TCH分配成功率(%)</Item>
								<Item name="FieldName" typeName="String">CS_TCH_ASSIG_SUCC_RATE</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">无线接通率(%)</Item>
								<Item name="FieldName" typeName="String">CS_REDIO_ACCE_SUCC_RATE</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">随机接入成功率（业务相关）</Item>
								<Item name="FieldName" typeName="String">CS_RAACCE_ACT_RATE</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">无线接入性</Item>
								<Item name="FieldName" typeName="String">CS_REDIO_ACCE_PRO</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
						</Item>
					</Item>
					<Item typeName="PerformanceTable">
						<Item name="ShowName" typeName="String">通话质量类</Item>
						<Item name="TableName" typeName="String">tb_para_Cell_ServiceCall</Item>
						<Item name="PerformanceFields" typeName="IList">
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">BSC内切换成功率(%)</Item>
								<Item name="FieldName" typeName="String">CS_HO_INTRA_BSC_SUCC_RATE</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">切换总尝试次数</Item>
								<Item name="FieldName" typeName="String">CS_HO_ATT(ALL)_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">由于上行话质问题而触发的切换尝试次数</Item>
								<Item name="FieldName" typeName="String">CS_HO_ATT_DUETO_POOR_UL_QUAL_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">由于下行话质问题而触发的切换尝试次数</Item>
								<Item name="FieldName" typeName="String">CS_HO_ATT_DUETO_POOR_DL_QUAL_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">半速率话务比例(%)</Item>
								<Item name="FieldName" typeName="String">CS_HR_ERL_RATE</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">每线话务量</Item>
								<Item name="FieldName" typeName="String">CS_PERLINE_ERL</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">平均每呼叫切换次数</Item>
								<Item name="FieldName" typeName="String">CS_AVE_HO_PER_CALL_ATT_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">上行话音质量(%)</Item>
								<Item name="FieldName" typeName="String">CS_UL_VC_QUAL_RATE</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">上行质差话务比例(%)</Item>
								<Item name="FieldName" typeName="String">CS_UL_POOR_QUAL_ERL_RATE</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">上行质差切换率(%)</Item>
								<Item name="FieldName" typeName="String">CS_UL_POOR_QUAL_HO_SUCC_RATE</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">无线利用率(%)</Item>
								<Item name="FieldName" typeName="String">CS_REDIO_ADV_APP_RATE</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">下行话音质量(%)</Item>
								<Item name="FieldName" typeName="String">CS_DL_VC_QUAL_RATE</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">下行质差话务比例(%)</Item>
								<Item name="FieldName" typeName="String">CS_DL_POOR_QUAL_ERL_RATE</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">下行质差切换率(%)</Item>
								<Item name="FieldName" typeName="String">CS_DL_POOR_QUAL_HO_SUCC_RATE</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
						</Item>
					</Item>
					<Item typeName="PerformanceTable">
						<Item name="ShowName" typeName="String">网络覆盖类</Item>
						<Item name="TableName" typeName="String">tb_para_Cell_Cover</Item>
						<Item name="PerformanceFields" typeName="IList">
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">测量报告上行覆盖率(%)</Item>
								<Item name="FieldName" typeName="String">CS_MREPORT_UL_COVER_RATE</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">测量报告下行覆盖率(%)</Item>
								<Item name="FieldName" typeName="String">CS_MREPORT_DL_COVER_RATE</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
						</Item>
					</Item>
					<Item typeName="PerformanceTable">
						<Item name="ShowName" typeName="String">小区切换性能数据</Item>
						<Item name="TableName" typeName="String">tb_para_Cell_HandOver</Item>
						<Item name="PerformanceFields" typeName="IList">
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">切出成功次数(汇总)</Item>
								<Item name="FieldName" typeName="String">CS_HO_TO_ADJ_SUCC(ALL)_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">切换成功总次数(汇总)</Item>
								<Item name="FieldName" typeName="String">CS_HO_OUT_SUCC(ALL)_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">切换请求总次数(汇总)</Item>
								<Item name="FieldName" typeName="String">CS_HO_REQ(ALL)_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">切入成功次数(汇总)</Item>
								<Item name="FieldName" typeName="String">CS_HO_FROM_ADJ_SUCC(ALL)_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">双频切换成功总次数(汇总)</Item>
								<Item name="FieldName" typeName="String">CS_INTER_FREQ_HO_SUCC(ALL)_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">双频切换试呼总次数(汇总)</Item>
								<Item name="FieldName" typeName="String">CS_INTER_FREQ_HO_REQ(ALL)_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">小区切出试呼次数(汇总)</Item>
								<Item name="FieldName" typeName="String">CS_HO_TO_ADJ_REQ(ALL)_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">小区切入试呼次数(汇总)</Item>
								<Item name="FieldName" typeName="String">CS_HO_FROM_ADJ_REQ(ALL)_NB</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">切换出成功率（汇总）(%)</Item>
								<Item name="FieldName" typeName="String">CS_HO_OUT_SUCC(ALL)_RATE</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">切换入成功率（汇总）(%)</Item>
								<Item name="FieldName" typeName="String">CS_HO_IN(ALL)_RATE</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
						</Item>
					</Item>
				</Item>
			</Item>
			<Item typeName="PerformanceSystem">
				<Item name="SystemName" typeName="String">TD</Item>
				<Item name="PerformanceTables" typeName="IList">
					<Item typeName="PerformanceTable">
						<Item name="ShowName" typeName="String">HSDPA建立相关统计数据</Item>
						<Item name="TableName" typeName="String">tb_para_UtranCell_HSDPA</Item>
						<Item name="PerformanceFields" typeName="IList">
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">CN发起正常释放HS-DSCH个数</Item>
								<Item name="FieldName" typeName="String">HSDPA.SuccCnInitHsdschRelease</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">HS-DSCH服务小区之间更改出尝试次数</Item>
								<Item name="FieldName" typeName="String">HSDPA.AttOutInterHsCellChange</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">HS-DSCH服务小区之间更改出成功次数</Item>
								<Item name="FieldName" typeName="String">HSDPA.SuccOutInterHsCellChange</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">HS-DSCH服务小区之间更改出失败次数</Item>
								<Item name="FieldName" typeName="String">HSDPA.FailOutInterHsCellChange</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">HS-DSCH服务小区之间更改入尝试次数</Item>
								<Item name="FieldName" typeName="String">HSDPA.AttInInterHsCellChange</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">HS-DSCH服务小区之间更改入成功次数</Item>
								<Item name="FieldName" typeName="String">HSDPA.SuccInInterHsCellChange</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">HS-DSCH服务小区之间更改入失败次数</Item>
								<Item name="FieldName" typeName="String">HSDPA.FailInInterHsCellChange</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">HS-DSCH异常释放个数</Item>
								<Item name="FieldName" typeName="String">HSDPA.failHsdschRelease</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">HSDPA MAC-d建立尝试个数</Item>
								<Item name="FieldName" typeName="String">HSDPA.AttMacdEstab</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">HSDPA MAC-d建立成功个数</Item>
								<Item name="FieldName" typeName="String">HSDPA.SuccMacdEstab</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">HSDPA MAC-d建立失败个数</Item>
								<Item name="FieldName" typeName="String">HSDPA.FailMacdEstab</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">HSDPA RB建立尝试个数</Item>
								<Item name="FieldName" typeName="String">HSDPA.AttRbEstab</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">HSDPA RB建立成功个数</Item>
								<Item name="FieldName" typeName="String">HSDPA.SuccRbEstab</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">HSDPA RB建立失败个数</Item>
								<Item name="FieldName" typeName="String">HSDPA.FailRbEstab</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">RNC请求释放分组域HSDPA RAB数</Item>
								<Item name="FieldName" typeName="String">HSDPA.RabRelByRnc</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">RNC释放的分组域HSDPA RAB数</Item>
								<Item name="FieldName" typeName="String">HSDPA.RabRel</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">尝试建立HSDPA分组RAB数</Item>
								<Item name="FieldName" typeName="String">HSDPA.AttRabEstab</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">成功建立HSDPA分组RAB数</Item>
								<Item name="FieldName" typeName="String">HSDPA.SuccRabEstab</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">小区间DCH到HS-DSCH的信道转换尝试次数</Item>
								<Item name="FieldName" typeName="String">HSDPA.AttDchToHsInterCell</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">小区间DCH到HS-DSCH的信道转换成功次数</Item>
								<Item name="FieldName" typeName="String">HSDPA.SuccDchToHsInterCell</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">小区间HS-DSCH到DCH的信道转换尝试次数</Item>
								<Item name="FieldName" typeName="String">HSDPA.AttHsToDchInterCell</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">小区间HS-DSCH到DCH的信道转换成功次数</Item>
								<Item name="FieldName" typeName="String">HSDPA.SuccHsToDchInterCell</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">小区内DCH到HS-DSCH的信道转换尝试次数</Item>
								<Item name="FieldName" typeName="String">HSDPA.AttDchToHsIntraCell</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">小区内DCH到HS-DSCH的信道转换成功次数</Item>
								<Item name="FieldName" typeName="String">HSDPA.SuccDchToHsIntraCell</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">小区内FACH到HS-DSCH的信道转换尝试次数</Item>
								<Item name="FieldName" typeName="String">HSDPA.AttFachToHsIntraCell</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">小区内FACH到HS-DSCH的信道转换成功次数</Item>
								<Item name="FieldName" typeName="String">HSDPA.SuccFachToHsIntraCell</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">小区内HS-DSCH到DCH的信道转换尝试次数</Item>
								<Item name="FieldName" typeName="String">HSDPA.AttHsToDchIntraCell</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">小区内HS-DSCH到DCH的信道转换成功次数</Item>
								<Item name="FieldName" typeName="String">HSDPA.SuccHsToDchIntraCell</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">小区内HS-DSCH到FACH的信道转换尝试次数</Item>
								<Item name="FieldName" typeName="String">HSDPA.AttHsToFachIntraCell</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">小区内HS-DSCH到FACH的信道转换成功次数</Item>
								<Item name="FieldName" typeName="String">HSDPA.SuccHsToFachIntraCell</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">小区内HS-DSCH到PCH的信道转换尝试次数</Item>
								<Item name="FieldName" typeName="String">HSDPA.AttHsToPchIntraCell</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">小区内HS-DSCH到PCH的信道转换成功次数</Item>
								<Item name="FieldName" typeName="String">HSDPA.SuccHsToPchIntraCell</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
						</Item>
					</Item>
					<Item typeName="PerformanceTable">
						<Item name="ShowName" typeName="String">HSUPA建立相关统计数据</Item>
						<Item name="TableName" typeName="String">tb_para_UtranCell_HSUPA</Item>
						<Item name="PerformanceFields" typeName="IList">
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">CN发起正常释放E-DCH次数</Item>
								<Item name="FieldName" typeName="String">HSUPA.SuccCnInitEdchRelease</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">E-DCH RNC间硬切换入尝试次数</Item>
								<Item name="FieldName" typeName="String">HSUPA.AttIncHhoInterRnc</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">E-DCH RNC间硬切换入成功次数</Item>
								<Item name="FieldName" typeName="String">HSUPA.SuccIncHhoInterRnc</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">E-DCH服务小区之间更改的出尝试次数</Item>
								<Item name="FieldName" typeName="String">HSUPA.AttOutInterHsCellChange</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">E-DCH服务小区之间更改的出成功次数</Item>
								<Item name="FieldName" typeName="String">HSUPA.SuccOutInterHsCellChange</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">E-DCH服务小区之间更改的出失败次数</Item>
								<Item name="FieldName" typeName="String">HSUPA.FailOutInterHsCellChange</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">E-DCH服务小区之间更改的入尝试次数</Item>
								<Item name="FieldName" typeName="String">HSUPA.AttInInterHsCellChange</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">E-DCH服务小区之间更改的入成功次数</Item>
								<Item name="FieldName" typeName="String">HSUPA.SuccInInterHsCellChange</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">E-DCH服务小区之间更改的入失败次数</Item>
								<Item name="FieldName" typeName="String">HSDPA.AttMacdEstab</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">E-DCH异常释放次数</Item>
								<Item name="FieldName" typeName="String">HSUPA.AbnormalEdchRelease</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">HSUPA MAC-d建立尝试个数</Item>
								<Item name="FieldName" typeName="String">HSUPA.AttMacdSetup</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">HSUPA MAC-d建立成功个数</Item>
								<Item name="FieldName" typeName="String">HSUPA.SuccMacdSetup</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">HSUPA MAC-d建立失败个数</Item>
								<Item name="FieldName" typeName="String">HSUPA.FailMacdSetup</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">HSUPA RB建立尝试个数</Item>
								<Item name="FieldName" typeName="String">HSUPA.AttRbSetup</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">HSUPA RB建立成功个数</Item>
								<Item name="FieldName" typeName="String">HSUPA.SuccRbSetup</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">HSUPA RB建立失败个数</Item>
								<Item name="FieldName" typeName="String">HSUPA.FailRbSetup</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">HSUPA到2G(GPRS)系统间切换尝试次数</Item>
								<Item name="FieldName" typeName="String">HSUPA.AttHsupaToGprs</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">HSUPA到2G(GPRS)系统间切换成功次数</Item>
								<Item name="FieldName" typeName="String">HSUPA.SuccHSupaToGprs</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">HSUPA分组域RAB建立尝试个数</Item>
								<Item name="FieldName" typeName="String">HSUPA.AttRabEstab</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">HSUPA分组域RAB建立成功个数</Item>
								<Item name="FieldName" typeName="String">HSUPA.SuccRabEstab</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">小区HSUPA平均用户数</Item>
								<Item name="FieldName" typeName="String">HSUPA.MeanNbrUser</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">小区HSUPA最大用户数</Item>
								<Item name="FieldName" typeName="String">HSUPA.MaxNbrUser</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">小区间DCH到E-DCH信道转换尝试次数</Item>
								<Item name="FieldName" typeName="String">HSUPA.AttDchToEdchInterCell</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">小区间DCH到E-DCH信道转换成功次数</Item>
								<Item name="FieldName" typeName="String">HSUPA.SuccDchToEdchInterCell</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">小区间E-DCH到DCH信道转换尝试次数</Item>
								<Item name="FieldName" typeName="String">HSUPA.AttEdchToDchInterCell</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">小区间E-DCH到DCH信道转换成功次数</Item>
								<Item name="FieldName" typeName="String">HSUPA.SuccEdchToDchInterCell</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">小区内DCH到E-DCH信道转换尝试次数</Item>
								<Item name="FieldName" typeName="String">HSUPA.AttDchToEdchIntraCell</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">小区内DCH到E-DCH信道转换成功次数</Item>
								<Item name="FieldName" typeName="String">HSUPA.SuccDchToEdchIntraCell</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">小区内E-DCH到DCH信道转换尝试次数</Item>
								<Item name="FieldName" typeName="String">HSUPA.AttEdchToDchIntraCell</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">小区内E-DCH到DCH信道转换成功次数</Item>
								<Item name="FieldName" typeName="String">HSUPA.SuccEdchToDchIntraCell</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">小区内E-DCH到RACH信道转换尝试次数</Item>
								<Item name="FieldName" typeName="String">HSUPA.AttEdchToRachIntraCell</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">小区内E-DCH到RACH信道转换成功次数</Item>
								<Item name="FieldName" typeName="String">HSUPA.SuccEdchToRachIntraCell</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">小区内RACH 到E-DCH信道转换成功次数</Item>
								<Item name="FieldName" typeName="String">HSUPA.SuccRachToEdchIntraCell</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">小区内RACH到E-DCH信道转换尝试次数</Item>
								<Item name="FieldName" typeName="String">HSUPA.AttRachToEdchIntraCell</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
						</Item>
					</Item>
					<Item typeName="PerformanceTable">
						<Item name="ShowName" typeName="String">Iub接口无线链路管理统计数据</Item>
						<Item name="TableName" typeName="String">tb_para_UtranCell_RLM</Item>
						<Item name="PerformanceFields" typeName="IList">
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">Iub接口无线链路建立尝试个数</Item>
								<Item name="FieldName" typeName="String">RLM.AttRlSetupIubUtranSide</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">Iub接口无线链路建立成功个数</Item>
								<Item name="FieldName" typeName="String">RLM.SuccRlSetupIubUtranSide</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">Iub接口无线链路建立失败总次数</Item>
								<Item name="FieldName" typeName="String">RLM.FailRlSetupIubUtranSide</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">Iub接口无线链路删除尝试个数</Item>
								<Item name="FieldName" typeName="String">RLM.AttRlDelIubUtranSide</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">Iub接口无线链路删除成功个数</Item>
								<Item name="FieldName" typeName="String">RLM.SuccRlDelIubUtranSide</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">Iub接口无线链路删除失败个数</Item>
								<Item name="FieldName" typeName="String">RLM.FailRlDelIubUtranSide</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">Iub接口无线链路增加尝试个数</Item>
								<Item name="FieldName" typeName="String">RLM.AttRlAddIubUtranSide</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">Iub接口无线链路增加成功个数</Item>
								<Item name="FieldName" typeName="String">RLM.SuccRlAddIubUtranSide</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">Iub接口无线链路增加失败总次数</Item>
								<Item name="FieldName" typeName="String">RLM.FailRlAddIubUtranSide</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
						</Item>
					</Item>
					<Item typeName="PerformanceTable">
						<Item name="ShowName" typeName="String">MBMS相关性能数据</Item>
						<Item name="TableName" typeName="String">tb_para_UtranCell_MBMS</Item>
						<Item name="PerformanceFields" typeName="IList">
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">PTM RB建立成功次数</Item>
								<Item name="FieldName" typeName="String">MBMS.SuccRbPtmEstab</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">PTM RB建立请求次数</Item>
								<Item name="FieldName" typeName="String">MBMS.AttRbPtmEstab</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">PTM平均数量</Item>
								<Item name="FieldName" typeName="String">MBMS.MeanNbrPtm</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">PTM最大数量</Item>
								<Item name="FieldName" typeName="String">MBMS.MaxNbrPtm</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
						</Item>
					</Item>
					<Item typeName="PerformanceTable">
						<Item name="ShowName" typeName="String">RRC连接建立统计数据</Item>
						<Item name="TableName" typeName="String">tb_para_UtranCell_RRC</Item>
						<Item name="PerformanceFields" typeName="IList">
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">CELL_DCH状态RRC连接最大建立时间</Item>
								<Item name="FieldName" typeName="String">RRC.AttConnEstabTimeMax.Dch</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">CELL_FACH状态RRC连接平均建立时间</Item>
								<Item name="FieldName" typeName="String">RRC.AttConnEstabTimeMean.Fach</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">CELL_FACH状态RRC连接最大建立时间</Item>
								<Item name="FieldName" typeName="String">RRC.AttConnEstabTimeMax.Fach</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">RRC连接平均建立时间</Item>
								<Item name="FieldName" typeName="String">RRC.AttConnEstabTimeMean</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">RRC连接最大建立时间</Item>
								<Item name="FieldName" typeName="String">RRC.AttConnEstabTimeMax</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">CELL_DCH状态RRC连接平均建立时间</Item>
								<Item name="FieldName" typeName="String">RRC.AttConnEstabTimeMean.Dch</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">RRC连接平均使用数</Item>
								<Item name="FieldName" typeName="String">RRC.MeanConn</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">RRC连接使用最大使用数</Item>
								<Item name="FieldName" typeName="String">RRC.MaxConn</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">RRC连接建立成功次数</Item>
								<Item name="FieldName" typeName="String">RRC.SuccConnEstab</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">RRC连接请求次数</Item>
								<Item name="FieldName" typeName="String">RRC.AttConnEstab</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">RRC连接失败次数</Item>
								<Item name="FieldName" typeName="String">RRC.FailConnEstab</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
						</Item>
					</Item>
					<Item typeName="PerformanceTable">
						<Item name="ShowName" typeName="String">保持类</Item>
						<Item name="TableName" typeName="String">tb_para_UtranCell_HoldCall</Item>
						<Item name="PerformanceFields" typeName="IList">
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">HSDPA信道异常释放率(%)</Item>
								<Item name="FieldName" typeName="String">HoldCallc1</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">PS域无线掉线率(cell)(%)</Item>
								<Item name="FieldName" typeName="String">HoldCallc2</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">CS域拥塞率(%)</Item>
								<Item name="FieldName" typeName="String">HoldCallc3</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">PS域拥塞率(%)</Item>
								<Item name="FieldName" typeName="String">HoldCallc4</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">RNC内同频接力切换成功率(cell)(%)</Item>
								<Item name="FieldName" typeName="String">HoldCallc5</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">RNC内同频硬切换成功率(%)</Item>
								<Item name="FieldName" typeName="String">HoldCallc6</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">RNC内异频接力切换成功率(cell)(%)</Item>
								<Item name="FieldName" typeName="String">HoldCallc7</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">RNC内异频硬切换成功率(%)</Item>
								<Item name="FieldName" typeName="String">HoldCallc8</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">TD网内小区间切换成功率(%)</Item>
								<Item name="FieldName" typeName="String">HoldCallc9</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">TD语音业务话务掉话比(cell)</Item>
								<Item name="FieldName" typeName="String">HoldCallc10</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">TD语音业务无线掉话率(小区级)(%)</Item>
								<Item name="FieldName" typeName="String">HoldCallc11</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">电路域系统间切换平均时长(ms)</Item>
								<Item name="FieldName" typeName="String">HoldCallc12</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">接力切换成功率(cell)(%)</Item>
								<Item name="FieldName" typeName="String">HoldCallc13</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">视频业务话务掉话比(cell)</Item>
								<Item name="FieldName" typeName="String">HoldCallc14</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">视频业务无线掉话率(cell)(%)</Item>
								<Item name="FieldName" typeName="String">HoldCallc15</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">同频硬切换成功率(%)</Item>
								<Item name="FieldName" typeName="String">HoldCallc16</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">RRC连接建立成功率(cell)(%)</Item>
								<Item name="FieldName" typeName="String">HoldCallc17</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">系统间CS域切换成功率(%)</Item>
								<Item name="FieldName" typeName="String">HoldCallc18</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">系统间PS域切换出成功率（TD-SCDMA-]GPRS）</Item>
								<Item name="FieldName" typeName="String">HoldCallc19</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">系统间PS域切换入成功率（GPRS-]TD-SCDMA）</Item>
								<Item name="FieldName" typeName="String">HoldCallc20</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">CS域系统间切换占比(%)</Item>
								<Item name="FieldName" typeName="String">HoldCallc21</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">CS域小区间切换出成功率(%)</Item>
								<Item name="FieldName" typeName="String">HoldCallc22</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">CS域小区间切换入成功率(%)</Item>
								<Item name="FieldName" typeName="String">HoldCallc23</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">异频硬切换成功率(%)</Item>
								<Item name="FieldName" typeName="String">HoldCallc24</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">硬切换成功率(%)</Item>
								<Item name="FieldName" typeName="String">HoldCallc25</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">小区间E-DCH到DCH信道转换成功次数</Item>
								<Item name="FieldName" typeName="String">HSUPA.SuccEdchToDchInterCell</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">小区内DCH到E-DCH信道转换尝试次数</Item>
								<Item name="FieldName" typeName="String">HSUPA.AttDchToEdchIntraCell</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">小区内DCH到E-DCH信道转换成功次数</Item>
								<Item name="FieldName" typeName="String">HSUPA.SuccDchToEdchIntraCell</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">小区内E-DCH到DCH信道转换尝试次数</Item>
								<Item name="FieldName" typeName="String">HSUPA.AttEdchToDchIntraCell</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">小区内E-DCH到DCH信道转换成功次数</Item>
								<Item name="FieldName" typeName="String">HSUPA.SuccEdchToDchIntraCell</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">小区内E-DCH到RACH信道转换尝试次数</Item>
								<Item name="FieldName" typeName="String">HSUPA.AttEdchToRachIntraCell</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">小区内E-DCH到RACH信道转换成功次数</Item>
								<Item name="FieldName" typeName="String">HSUPA.SuccEdchToRachIntraCell</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">小区内RACH 到E-DCH信道转换成功次数</Item>
								<Item name="FieldName" typeName="String">HSUPA.SuccRachToEdchIntraCell</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">小区内RACH到E-DCH信道转换尝试次数</Item>
								<Item name="FieldName" typeName="String">HSUPA.AttRachToEdchIntraCell</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
						</Item>
					</Item>
					<Item typeName="PerformanceTable">
						<Item name="ShowName" typeName="String">传输误块统计数据</Item>
						<Item name="TableName" typeName="String">tb_para_UtranCell_FP</Item>
						<Item name="PerformanceFields" typeName="IList">
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">电路域收到的上行传输块的总数</Item>
								<Item name="FieldName" typeName="String">FP.NbrBlocksReceivedCs</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">电路域收到的上行传输块中出现错块的个数</Item>
								<Item name="FieldName" typeName="String">FP.NbrErrBlocksReceivedCs</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">分组域收到的上行传输块的总数</Item>
								<Item name="FieldName" typeName="String">FP.NbrBlocksReceivedPs</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">分组域收到的上行传输块中出现错块的个数</Item>
								<Item name="FieldName" typeName="String">FP.NbrErrBlocksReceivedPs</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
						</Item>
					</Item>
					<Item typeName="PerformanceTable">
						<Item name="ShowName" typeName="String">接入类</Item>
						<Item name="TableName" typeName="String">tb_para_UtranCell_AccessCall</Item>
						<Item name="PerformanceFields" typeName="IList">
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">CS域无线接入成功率</Item>
								<Item name="FieldName" typeName="String">AccessCallC1</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">HSDPA信道建立成功率(%)</Item>
								<Item name="FieldName" typeName="String">AccessCallC2</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">PS域无线接通率(%)</Item>
								<Item name="FieldName" typeName="String">AccessCallC3</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">RAB建立成功率(%)</Item>
								<Item name="FieldName" typeName="String">AccessCallC4</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">RRC连接建立成功率(%)</Item>
								<Item name="FieldName" typeName="String">AccessCallC5</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">TD语音业务无线接通率(小区级)(%)</Item>
								<Item name="FieldName" typeName="String">AccessCallC6</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">电路域RAB建立成功率(%)</Item>
								<Item name="FieldName" typeName="String">AccessCallC7</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">电路域会话类RRC连接建立成功率(%)</Item>
								<Item name="FieldName" typeName="String">AccessCallC8</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">分组域RAB建立成功率(%)</Item>
								<Item name="FieldName" typeName="String">AccessCallC9</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">寻呼拥塞率(%)</Item>
								<Item name="FieldName" typeName="String">AccessCallC10</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
						</Item>
					</Item>
					<Item typeName="PerformanceTable">
						<Item name="ShowName" typeName="String">系统间小区间切换统计数据</Item>
						<Item name="TableName" typeName="String">tb_para_UtranCell_IRATHO</Item>
						<Item name="PerformanceFields" typeName="IList">
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">电路域系统间切换入RNC的成功时长（GSM-]TD-SCDMA)</Item>
								<Item name="FieldName" typeName="String">IRATHO.SuccRelocIncTimeMeanCs</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">电路域系统间小区间切换入RNC成功次数（GSM-]TD-SCDMA）</Item>
								<Item name="FieldName" typeName="String">IRATHO.SuccIncCsPerCell</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">电路域系统间小区间切换入RNC请求次数（GSM-]TD-SCDMA）</Item>
								<Item name="FieldName" typeName="String">IRATHO.AttIncCsPerCell</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">电路域系统间小区间切换入失败次数（GSM-]TD-SCDMA）</Item>
								<Item name="FieldName" typeName="String">IRATHO.FailIncCsPerCell</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">分组域系统间切换入RNC的成功时长（GPRS-]TD-SCDMA)</Item>
								<Item name="FieldName" typeName="String">IRATHO.SuccRelocIncTimeMeanPs</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">分组域系统间小区间切换入RNC成功次数（GPRS-]TD-SCDMA）</Item>
								<Item name="FieldName" typeName="String">IRATHO.SuccRelocIncInterSysPsPerCell</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">分组域系统间小区间切换入RNC请求次数（GPRS-]TD-SCDMA）</Item>
								<Item name="FieldName" typeName="String">IRATHO.AttRelocIncInterSysPsPerCell</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">电路域系统间切换出RNC成功时长（TD-SCDMA-]GSM)</Item>
								<Item name="FieldName" typeName="String">IRATHO.SuccRelocOutTimeMeanCs</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">电路域系统间小区间切换出成功次数（TD-SCDMA-]GSM）</Item>
								<Item name="FieldName" typeName="String">IRATHO.SuccOutCs</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">电路域系统间小区间切换出请求次数（TD-SCDMA-]GSM）</Item>
								<Item name="FieldName" typeName="String">IRATHO.AttOutCs</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">电路域系统间小区间切换出失败次数（TD-SCDMA-]GSM）</Item>
								<Item name="FieldName" typeName="String">IRATHO.FailOutCs</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">电路域系统间小区间切换出准备成功次数（TD-SCDMA-]GSM）</Item>
								<Item name="FieldName" typeName="String">IRATHO.SuccRelocPrepOutCs</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">电路域系统间小区间切换出准备请求次数（TD-SCDMA-]GSM）</Item>
								<Item name="FieldName" typeName="String">IRATHO.AttRelocPrepOutCs</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">电路域系统间小区间切换出准备失败次数（TD-SCDMA-]GSM）</Item>
								<Item name="FieldName" typeName="String">IRATHO.FailRelocPrepOutCs</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">分组域系统间切换出RNC成功时长（TD-SCDMA-]GPRS)</Item>
								<Item name="FieldName" typeName="String">IRATHO.SuccRelocOutTimeMeanPs</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">分组域系统间小区间切换出成功次数（TD-SCDMA-]GPRS）</Item>
								<Item name="FieldName" typeName="String">IRATHO.SuccOutPsUtran</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">分组域系统间小区间切换出请求次数（TD-SCDMA-]GPRS）</Item>
								<Item name="FieldName" typeName="String">IRATHO.AttOutPsUtran</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">分组域系统间小区间切换出失败次数（TD-SCDMA-]GPRS）</Item>
								<Item name="FieldName" typeName="String">IRATHO.FailOutPsUtran</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
						</Item>
					</Item>
					<Item typeName="PerformanceTable">
						<Item name="ShowName" typeName="String">系统内小区间硬切换统计数据</Item>
						<Item name="TableName" typeName="String">tb_para_UtranCell_HHO</Item>
						<Item name="PerformanceFields" typeName="IList">
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">系统内小区间同频硬切换出成功次数</Item>
								<Item name="FieldName" typeName="String">HHO.SuccOutInterCellIntraFreq</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">系统内小区间同频硬切换出请求次数</Item>
								<Item name="FieldName" typeName="String">HHO.AttOutInterCellIntraFreq</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">系统内小区间同频硬切换出失败次数</Item>
								<Item name="FieldName" typeName="String">HHO.FailOutInterCellIntraFreq</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">系统内小区间异频硬切换出成功次数</Item>
								<Item name="FieldName" typeName="String">HHO.SuccOutInterCellInterFreq</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">系统内小区间异频硬切换出请求次数</Item>
								<Item name="FieldName" typeName="String">HHO.AttOutInterCellInterFreq</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">系统内小区间异频硬切换出失败次数</Item>
								<Item name="FieldName" typeName="String">HHO.FailOutInterCellInterFreq</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">RNC间小区间同频硬切换出成功次数</Item>
								<Item name="FieldName" typeName="String">HHO.SuccOutInterRncCnIntraFreq</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">RNC间小区间同频硬切换出请求次数</Item>
								<Item name="FieldName" typeName="String">HHO.AttOutInterRncCnIntraFreq</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">RNC间小区间同频硬切换出失败次数</Item>
								<Item name="FieldName" typeName="String">HHO.FailOutInterRncCnIntraFreq</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">RNC间小区间异频硬切换出成功次数</Item>
								<Item name="FieldName" typeName="String">HHO.SuccOutInterRncCnInterFreq</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">RNC间小区间异频硬切换出请求次数</Item>
								<Item name="FieldName" typeName="String">HHO.AttOutInterRncCnInterFreq</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">RNC间小区间异频硬切换出失败次数</Item>
								<Item name="FieldName" typeName="String">HHO.FailOutInterRncCnInterFreq</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">RNC间硬切换出成功次数</Item>
								<Item name="FieldName" typeName="String">HHO.SuccOutInterRncPerCell</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">RNC间硬切换出准备请求次数</Item>
								<Item name="FieldName" typeName="String">HHO.AttOutInterRncPerCell</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">RNC间硬切换出准备失败次数</Item>
								<Item name="FieldName" typeName="String">HHO.FailOutInterRncPerCell</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">RNC间硬切换入成功次数</Item>
								<Item name="FieldName" typeName="String">HHO.SuccIncInterRncPerCell</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">RNC间硬切换入准备请求次数</Item>
								<Item name="FieldName" typeName="String">HHO.AttIncInterRncPerCell</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">RNC间硬切换入准备失败次数</Item>
								<Item name="FieldName" typeName="String">HHO.FailIncInterRncPerCell</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">RNC内小区间异频硬切换出成功次数</Item>
								<Item name="FieldName" typeName="String">HHO.SuccOutIntraRncInterFreq</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">RNC内小区间异频硬切换出请求次数</Item>
								<Item name="FieldName" typeName="String">HHO.AttOutIntraRncInterFreq</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">RNC内小区间异频硬切换出失败次数</Item>
								<Item name="FieldName" typeName="String">HHO.FailOutIntraRncInterFreq</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">RNC内小区间异频硬切换入成功次数</Item>
								<Item name="FieldName" typeName="String">HHO.SuccIncIntraRncInterFreq</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">RNC内小区间异频硬切换入请求次数</Item>
								<Item name="FieldName" typeName="String">HHO.AttIncIntraRncInterFreq</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">RNC内小区间异频硬切换入失败次数</Item>
								<Item name="FieldName" typeName="String">HHO.FailIncIntraRncInterFreq</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">RNC内小区间同频硬切换出成功次数</Item>
								<Item name="FieldName" typeName="String">HHO.SuccOutIntraRncIntraFreq</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">RNC内小区间同频硬切换出请求次数</Item>
								<Item name="FieldName" typeName="String">HHO.AttOutIntraRncIntraFreq</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">RNC内小区间同频硬切换出失败次数</Item>
								<Item name="FieldName" typeName="String">HHO.FailOutIntraRncIntraFreq</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">RNC内小区间同频硬切换入成功次数</Item>
								<Item name="FieldName" typeName="String">HHO.SuccIncIntraRncIntraFreq</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">RNC内小区间同频硬切换入请求次数</Item>
								<Item name="FieldName" typeName="String">HHO.AttIncIntraRncIntraFreq</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">RNC内小区间同频硬切换入失败次数</Item>
								<Item name="FieldName" typeName="String">HHO.FailIncIntraRncIntraFreq</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
						</Item>
					</Item>
					<Item typeName="PerformanceTable">
						<Item name="ShowName" typeName="String">RNC内小区间接力切换统计数据</Item>
						<Item name="TableName" typeName="String">tb_para_UtranCell_BHO</Item>
						<Item name="PerformanceFields" typeName="IList">
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">RNC内小区间同频接力切换出成功次数（小区级）</Item>
								<Item name="FieldName" typeName="String">BHO.SuccOutIntraRncIntraFreqPerCell</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">RNC内小区间同频接力切换出请求次数（小区级）</Item>
								<Item name="FieldName" typeName="String">BHO.AttOutIntraRncIntraFreqPerCell</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">RNC内小区间同频接力切换出失败次数（小区级）</Item>
								<Item name="FieldName" typeName="String">BHO.FailOutIntraRncIntraFreqPerCell</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">RNC内小区间同频接力切换入成功次数（小区级）</Item>
								<Item name="FieldName" typeName="String">BHO.SuccIncIntraRncIntraFreqPerCell</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">RNC内小区间同频接力切换入请求次数（小区级）</Item>
								<Item name="FieldName" typeName="String">BHO.AttIncIntraRncIntraFreqPerCell</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">RNC内小区间同频接力切换入失败次数（小区级）</Item>
								<Item name="FieldName" typeName="String">BHO.FailIncIntraRncIntraFreqPerCell</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">RNC内小区间异频接力切换出成功次数（小区级）</Item>
								<Item name="FieldName" typeName="String">BHO.SuccOutIntraRncInterFreqPerCell</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">RNC内小区间异频接力切换出请求次数（小区级）</Item>
								<Item name="FieldName" typeName="String">BHO.AttOutIntraRncInterFreqPerCell</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">RNC内小区间异频接力切换出失败次数（小区级）</Item>
								<Item name="FieldName" typeName="String">BHO.FailOutIntraRncInterFreqPerCell</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">RNC内小区间异频接力切换入成功次数（小区级）</Item>
								<Item name="FieldName" typeName="String">BHO.SuccIncIntraRncInterFreqPerCell</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">RNC内小区间异频接力切换入请求次数（小区级）</Item>
								<Item name="FieldName" typeName="String">BHO.AttIncIntraRncInterFreqPerCell</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">RNC内小区间异频接力切换入失败次数（小区级）</Item>
								<Item name="FieldName" typeName="String">BHO.FailIncIntraRncInterFreqPerCell</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
						</Item>
					</Item>
					<Item typeName="PerformanceTable">
						<Item name="ShowName" typeName="String">小区动态信道调整统计数据</Item>
						<Item name="TableName" typeName="String">tb_para_UtranCell_DCA</Item>
						<Item name="PerformanceFields" typeName="IList">
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">小区内同频动态信道调整尝试次数</Item>
								<Item name="FieldName" typeName="String">DCA.AttIntraCellIntraFreq</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">小区内同频动态信道调整成功次数</Item>
								<Item name="FieldName" typeName="String">DCA.SuccIntraCellIntraFreq</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">小区内同频动态信道调整失败次数</Item>
								<Item name="FieldName" typeName="String">DCA.FailIntraCellIntraFreq</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">小区内异频动态信道调整尝试次数</Item>
								<Item name="FieldName" typeName="String">DCA.AttIntraCellInterFreq</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">小区内异频动态信道调整成功次数</Item>
								<Item name="FieldName" typeName="String">DCA.SuccIntraCellInterFreq</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">小区内异频动态信道调整失败次数</Item>
								<Item name="FieldName" typeName="String">DCA.FailIntraCellInterFreq</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
						</Item>
					</Item>
					<Item typeName="PerformanceTable">
						<Item name="ShowName" typeName="String">小区RAB统计数据</Item>
						<Item name="TableName" typeName="String">tb_para_UtranCell_RAB</Item>
						<Item name="PerformanceFields" typeName="IList">
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">RNC请求释放的电路域的RAB数（小区级）</Item>
								<Item name="FieldName" typeName="String">RAB.RelReqCsPerCell</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">RNC请求释放的分组域RAB数（小区级）</Item>
								<Item name="FieldName" typeName="String">RAB.RelReqPs</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">RNC请求释放电路域Iu连接对应的RAB数（小区级）</Item>
								<Item name="FieldName" typeName="String">IU.NbrRabCsRelIuConnPerCell</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">RNC请求释放分组域Iu连接对应的RAB数（小区级）</Item>
								<Item name="FieldName" typeName="String">IU.NbrRabPsRelIuConnPerCell</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">成功建立的电路域的RAB数（小区级）</Item>
								<Item name="FieldName" typeName="String">RAB.SuccEstabCsPerCell</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">成功建立的分组域RAB数（小区级）</Item>
								<Item name="FieldName" typeName="String">RAB.SuccEstabPsPerCell</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">请求建立的电路域RAB数（小区级）</Item>
								<Item name="FieldName" typeName="String">RAB.AttEstabCsPerCell</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">请求建立的分组域RAB数（小区级）</Item>
								<Item name="FieldName" typeName="String">RAB.AttEstabPsPerCell</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">小区间切换入的电路域RAB数</Item>
								<Item name="FieldName" typeName="String">RAB.NbrRabHoiInterCellCs</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">小区间切换入的电路域会话类12.2K RAB数</Item>
								<Item name="FieldName" typeName="String">RAB.NbrRabHoiInterCellCs.Conv.[2][2]</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">小区间切换入的电路域会话类32K RAB数</Item>
								<Item name="FieldName" typeName="String">RAB.NbrRabHoiInterCellCs.Conv.[4][4]</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">小区间切换入的电路域会话类64K RAB数</Item>
								<Item name="FieldName" typeName="String">RAB.NbrRabHoiInterCellCs.Conv.[5][5]</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">小区间切换入的电路域会话类RAB数</Item>
								<Item name="FieldName" typeName="String">RAB.NbrRabHoiInterCellCs.Conv</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">小区间切换入的电路域会话类窄带AMR RAB数</Item>
								<Item name="FieldName" typeName="String">RAB.NbrRabHoiInterCellCs.Conv.[1][1]</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">小区间切换入的分组域RAB数</Item>
								<Item name="FieldName" typeName="String">RAB.NbrRabHhoInterCellPs</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">指配失败的电路域的RAB数（小区级）</Item>
								<Item name="FieldName" typeName="String">RAB.FailEstabCsPerCell</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">指配失败的分组域RAB数（小区级）</Item>
								<Item name="FieldName" typeName="String">RAB.FailEstabPsPerCell</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">低数据速率的RAB个数</Item>
								<Item name="FieldName" typeName="String">RAB.NbrLowDataRate</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">HSUPA分组域RAB建立尝试个数</Item>
								<Item name="FieldName" typeName="String">HSUPA.AttRabEstab</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">HSUPA分组域RAB建立成功个数</Item>
								<Item name="FieldName" typeName="String">HSUPA.SuccRabEstab</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">小区HSUPA平均用户数</Item>
								<Item name="FieldName" typeName="String">HSUPA.MeanNbrUser</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">小区HSUPA最大用户数</Item>
								<Item name="FieldName" typeName="String">HSUPA.MaxNbrUser</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">小区间DCH到E-DCH信道转换尝试次数</Item>
								<Item name="FieldName" typeName="String">HSUPA.AttDchToEdchInterCell</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">小区间DCH到E-DCH信道转换成功次数</Item>
								<Item name="FieldName" typeName="String">HSUPA.SuccDchToEdchInterCell</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">小区间E-DCH到DCH信道转换尝试次数</Item>
								<Item name="FieldName" typeName="String">HSUPA.AttEdchToDchInterCell</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">小区间E-DCH到DCH信道转换成功次数</Item>
								<Item name="FieldName" typeName="String">HSUPA.SuccEdchToDchInterCell</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">小区内DCH到E-DCH信道转换尝试次数</Item>
								<Item name="FieldName" typeName="String">HSUPA.AttDchToEdchIntraCell</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">小区内DCH到E-DCH信道转换成功次数</Item>
								<Item name="FieldName" typeName="String">HSUPA.SuccDchToEdchIntraCell</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">小区内E-DCH到DCH信道转换尝试次数</Item>
								<Item name="FieldName" typeName="String">HSUPA.AttEdchToDchIntraCell</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">小区内E-DCH到DCH信道转换成功次数</Item>
								<Item name="FieldName" typeName="String">HSUPA.SuccEdchToDchIntraCell</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">小区内E-DCH到RACH信道转换尝试次数</Item>
								<Item name="FieldName" typeName="String">HSUPA.AttEdchToRachIntraCell</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">小区内E-DCH到RACH信道转换成功次数</Item>
								<Item name="FieldName" typeName="String">HSUPA.SuccEdchToRachIntraCell</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">小区内RACH 到E-DCH信道转换成功次数</Item>
								<Item name="FieldName" typeName="String">HSUPA.SuccRachToEdchIntraCell</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">小区内RACH到E-DCH信道转换尝试次数</Item>
								<Item name="FieldName" typeName="String">HSUPA.AttRachToEdchIntraCell</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
						</Item>
					</Item>
					<Item typeName="PerformanceTable">
						<Item name="ShowName" typeName="String">小区RB统计数据</Item>
						<Item name="TableName" typeName="String">tb_para_UtranCell_RB</Item>
						<Item name="PerformanceFields" typeName="IList">
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">RB重配成功个数</Item>
								<Item name="FieldName" typeName="String">RB.NbrSuccReconfig</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">RB重配请求个数</Item>
								<Item name="FieldName" typeName="String">RB.NbrAttReconfig</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
						</Item>
					</Item>
					<Item typeName="PerformanceTable">
						<Item name="ShowName" typeName="String">小区流量统计数据</Item>
						<Item name="TableName" typeName="String">tb_para_UtranCell_RLC</Item>
						<Item name="PerformanceFields" typeName="IList">
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">RLC层电路域话务量</Item>
								<Item name="FieldName" typeName="String">RLC.CsTraffic</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">RLC层分组域下行流量</Item>
								<Item name="FieldName" typeName="String">RLC.PsDlOct</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">RLC层上行信道CTCH流量</Item>
								<Item name="FieldName" typeName="String">RLC.TrafficUl.Ctch</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">RLC层上行信道DTCH流量</Item>
								<Item name="FieldName" typeName="String">RLC.TrafficUl.Dtch</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">RLC层上行信道数据流量</Item>
								<Item name="FieldName" typeName="String">RLC.TrafficUl</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">RLC层下行信道CTCH数据流量</Item>
								<Item name="FieldName" typeName="String">RLC.TrafficDl.Ctch</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">RLC层下行信道DTCH数据流量</Item>
								<Item name="FieldName" typeName="String">RLC.TrafficDl.Dtch</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">RLC层下行信道数据流量</Item>
								<Item name="FieldName" typeName="String">RLC.TrafficDl</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">电路域确认模式下重传的RLC包的个数</Item>
								<Item name="FieldName" typeName="String">RLC.NbrRetransmittedBlocksToUeCsPerCell</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">发送RLC包的个数</Item>
								<Item name="FieldName" typeName="String">RLC.NbrBlockSentPerCell</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">发送RLC包透明传输模式的个数</Item>
								<Item name="FieldName" typeName="String">RLC.NbrBlockSentPerCell.Tm</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">发送RLC包无应答传输模式的个数</Item>
								<Item name="FieldName" typeName="String">RLC.NbrBlockSentPerCell.Um</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">发送RLC包应答传输模式的个数</Item>
								<Item name="FieldName" typeName="String">RLC.NbrBlockSentPerCell.Am</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">发送分组域RLC包的个数</Item>
								<Item name="FieldName" typeName="String">RLC.NbrPsBlockSentPerCell</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">发送分组域RLC包透明传输模式的个数</Item>
								<Item name="FieldName" typeName="String">RLC.NbrPsBlockSentPerCell.Tm</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">发送分组域RLC包无应答传输模式的个数</Item>
								<Item name="FieldName" typeName="String">RLC.NbrPsBlockSentPerCell.Um</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">发送分组域RLC包应答传输模式的个数</Item>
								<Item name="FieldName" typeName="String">RLC.NbrPsBlockSentPerCell.Am</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">分组域确认模式下重传的RLC包的个数</Item>
								<Item name="FieldName" typeName="String">RLC.NbrRetransmittedBlocksToUePsPerCell</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">接收RLC包的个数</Item>
								<Item name="FieldName" typeName="String">RLC.NbrBlockRecvPerCell</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">接收RLC包透明传输模式的个数</Item>
								<Item name="FieldName" typeName="String">RLC.NbrBlockRecvPerCell.Tm</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">接收RLC包无应答传输模式的个数</Item>
								<Item name="FieldName" typeName="String">RLC.NbrBlockRecvPerCell.Um</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">接收RLC包应答传输模式的个数</Item>
								<Item name="FieldName" typeName="String">RLC.NbrBlockRecvPerCell.Am</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">接收RLC错误包的个数</Item>
								<Item name="FieldName" typeName="String">RLC.NbrErrBlockRecvPerCell.Am</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">接收分组域RLC包的个数</Item>
								<Item name="FieldName" typeName="String">RLC.NbrPsBlockRecvPerCell</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">接收分组域RLC包透明传输模式的个数</Item>
								<Item name="FieldName" typeName="String">RLC.NbrPsBlockRecvPerCell.Tm</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">接收分组域RLC包无应答传输模式的个数</Item>
								<Item name="FieldName" typeName="String">RLC.NbrPsBlockRecvPerCell.Um</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">接收分组域RLC包应答传输模式的个数</Item>
								<Item name="FieldName" typeName="String">RLC.NbrPsBlockRecvPerCell.Am</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
						</Item>
					</Item>
					<Item typeName="PerformanceTable">
						<Item name="ShowName" typeName="String">小区寻呼统计数据</Item>
						<Item name="TableName" typeName="String">tb_para_UtranCell_PAGING</Item>
						<Item name="PerformanceFields" typeName="IList">
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">UTRAN发起寻呼类型1成功次数</Item>
								<Item name="FieldName" typeName="String">PAGING.SuccCellPagingType1</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">UTRAN发起寻呼类型1次数</Item>
								<Item name="FieldName" typeName="String">PAGING.AttInReCellPagingType1</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">UTRAN发起寻呼类型2次数</Item>
								<Item name="FieldName" typeName="String">PAGING.AttCellPagingType2</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">寻呼类型1发送不成功次数</Item>
								<Item name="FieldName" typeName="String">PAGING.FailTranCellPagingType1</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">寻呼类型1拥塞次数</Item>
								<Item name="FieldName" typeName="String">PAGING.CongCellPagingType1</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
						</Item>
					</Item>
					<Item typeName="PerformanceTable">
						<Item name="ShowName" typeName="String">小区资源性能统计数据</Item>
						<Item name="TableName" typeName="String">tb_para_UtranCell_RES</Item>
						<Item name="PerformanceFields" typeName="IList">
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">平均占用的上行BRU总数</Item>
								<Item name="FieldName" typeName="String">RES.MeanNbrBruUl</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">平均占用的下行BRU总数</Item>
								<Item name="FieldName" typeName="String">RES.MeanNbrBruDl</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">小区可用的上行BRU总数</Item>
								<Item name="FieldName" typeName="String">RES.NbrAvailBruUl</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">小区可用的下行BRU总数</Item>
								<Item name="FieldName" typeName="String">RES.NbrAvailBruDl</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">最大占用的上行BRU总数</Item>
								<Item name="FieldName" typeName="String">RES.MaxNbrBruUl</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">最大占用的下行BRU总数</Item>
								<Item name="FieldName" typeName="String">RES.MaxNbrBruDl</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">小区上行码资源利用率</Item>
								<Item name="FieldName" typeName="String">CfgBruUL</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">小区下行码资源利用率</Item>
								<Item name="FieldName" typeName="String">CfgBruDL</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">小区码资源利用率</Item>
								<Item name="FieldName" typeName="String">CfgBru</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
						</Item>
					</Item>
					<Item typeName="PerformanceTable">
						<Item name="ShowName" typeName="String">业务质量类</Item>
						<Item name="TableName" typeName="String">tb_para_UtranCell_ServiceCall</Item>
						<Item name="PerformanceFields" typeName="IList">
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">PS域误块率(%)</Item>
								<Item name="FieldName" typeName="String">ServiceCallC1</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">PS域下行重传率（RLC层）</Item>
								<Item name="FieldName" typeName="String">ServiceCallC2</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">电路域误块率(%)</Item>
								<Item name="FieldName" typeName="String">ServiceCallC3</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">语音业务误块率(%)</Item>
								<Item name="FieldName" typeName="String">ServiceCallC4</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">TD系统语音话务量(Cell）</Item>
								<Item name="FieldName" typeName="String">ServiceCallC5</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">电路域无线话务量</Item>
								<Item name="FieldName" typeName="String">ServiceCallC6</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">TD系统视频话务量（Cell）</Item>
								<Item name="FieldName" typeName="String">ServiceCallC7</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">HSDPA流量</Item>
								<Item name="FieldName" typeName="String">ServiceCallC8</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
							<Item typeName="PerformanceField">
								<Item name="ShowName" typeName="String">TD系统分组域业务流量（Cell）</Item>
								<Item name="FieldName" typeName="String">ServiceCallC9</Item>
								<Item name="FieldType" typeName="String">E_Float</Item>
							</Item>
						</Item>
					</Item>
				</Item>
			</Item>
		</Item>
	</Config>
</Configs>
