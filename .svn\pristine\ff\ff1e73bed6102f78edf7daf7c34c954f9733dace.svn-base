﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.ZTFunc
{
    public class QueryLastRoad : DIYAnalyseByFileBackgroundBase
    {
        public QueryLastRoad(MainModel mm) :
            base(mm)
        {
        }
        public override string Name
        {
            get { return "自定义持续道路分析（按区域）"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 20000, 20033, this.Name);
        }

        protected LastRoadReport report = null;
        protected override bool getCondition()
        {
            LastRoadReportSelectDlg conditionDlg = new LastRoadReportSelectDlg(report);
            if (conditionDlg.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            report = conditionDlg.SelectedReport;
            this.Columns = report.GetNeedParamNames();
            if (Columns.Count==0)
            {
                MessageBox.Show("所选报告无查询指标！");
                return false;
            }
            lastRoads = new List<LastRoadSegment>();
            return true;
        }

        List<LastRoadSegment> lastRoads = new List<LastRoadSegment>();
        LastRoadSegment lastSegment = null;
        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
            {
                foreach (TestPoint testPoint in file.TestPoints)
                {
                    if (!isValidTestPoint(testPoint))
                    {
                        //采样点不在区域内
                        saveOneLastRoad(ref lastSegment);
                    }
                    else
                    {
                        //在区域内
                        dealValidTP(testPoint);
                    }
                }
                saveOneLastRoad(ref lastSegment);//防止最后一段没有保存
            }
        }

        private void dealValidTP(TestPoint testPoint)
        {
            if (report.Condition.IsTPValueMatch(testPoint))
            {//指标值符合持续条件，新建一LastRoad
                bool isNewSegment = true;
                if (lastSegment != null)
                {
                    double distance2TP = lastSegment.LastTestPoint.Distance2(testPoint);
                    if (report.Condition.Is2TPDisMatch(distance2TP))
                    {//2采样点距离符合条件
                        isNewSegment = false;
                        lastSegment.AddTestPoint(testPoint, distance2TP, report.DisplayColumns);
                    }
                }
                if (isNewSegment)
                {
                    saveOneLastRoad(ref lastSegment);//保存上一段持续道路
                    lastSegment = new LastRoadSegment(testPoint, report.DisplayColumns);//该采样点开始新一段持续道路
                }
            }
            else
            {//不符合，保存前一持续路段
                saveOneLastRoad(ref lastSegment);
            }
        }

        /// <summary>
        /// 保存一段持续道路信息，然后lastRoad指向null
        /// </summary>
        /// <param name="lastRoad"></param>
        private void saveOneLastRoad(ref LastRoadSegment lastRoad)
        {
            if (lastRoad == null)
            {
                return;
            }
            if (lastRoads.Contains(lastRoad))
            {//文件最后一段可能会保存2次，在此过滤
                return;
            }
            if (report.Condition.IsLastDisMatch(lastRoad.Distance))
            {
                lastRoad.MakeSummary(report);
                lastRoads.Add(lastRoad);
            }
            lastRoad = null;
        }

        protected override void fireShowForm()
        {
            if (lastRoads.Count == 0)
            {
                MessageBox.Show("无符合条件的持续道路！");
                return;
            }
            LastRoadSegmentForm frm = MainModel.GetObjectFromBlackboard(typeof(LastRoadSegmentForm).FullName) as LastRoadSegmentForm;
            if (frm == null)
            {
                frm = new LastRoadSegmentForm(MainModel);
            }
            frm.FillData(report, lastRoads);
            frm.Owner = MainModel.MainForm;
            frm.Visible = true;
            frm.BringToFront();
            lastRoads = null;
        }

        #region Background
        /*To be perfect
        public override BackgroundFuncType FuncType
        {
            get { return BackgroundFuncType.TD扫频; }
        }

        public override BackgroundSubFuncType SubFuncType
        {
            get { return BackgroundSubFuncType.干扰; }
        }

        public override Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["BackgroundStat"] = backgroundStat;
                param["C_IThreshold"] = c_iThreshold;
                param["RxLevThreshold"] = rxLevThreshold;
                param["DistanceLast"] = distanceLast;
                param["DitanceTP"] = distanceTP;
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value as Dictionary<string, object>;
                if (param.ContainsKey("BackgroundStat"))
                {
                    backgroundStat = (bool)param["BackgroundStat"];
                }
                if (param.ContainsKey("C_IThreshold"))
                {
                    c_iThreshold = int.Parse(param["C_IThreshold"].ToString());
                }
                if (param.ContainsKey("RxLevThreshold"))
                {
                    rxLevThreshold = int.Parse(param["RxLevThreshold"].ToString());
                }
                if (param.ContainsKey("DistanceLast"))
                {
                    distanceLast = int.Parse(param["DistanceLast"].ToString());
                }
                if (param.ContainsKey("DitanceTP"))
                {
                    distanceTP = int.Parse(param["DitanceTP"].ToString());
                }
            }
        }

        public override PropertiesControl Properties
        {
            get
            {
                return new WeakC2IRoadProperties_TDSCAN(this);
            }
        }

        protected override void saveBackgroundData()
        {
            List<BackgroundResult> bgResultList = new List<BackgroundResult>();
            foreach (WeakC_IRoad block in weakC_IRoadList)
            {
                block.GetResult();
                BackgroundResult result = block.ConvertToBackgroundResult();
                result.SubFuncID = GetSubFuncID();
                bgResultList.Add(result);
            }
            BackgroundFuncQueryManager.GetInstance().SaveResult_Road(GetSubFuncID(), curAnaFileInfo, bgResultList);
            weakC_IRoadList.Clear();
        }

        protected override void initBackgroundImageDesc()
        {
            foreach (BackgroundResult bgResult in BackgroundResultList)
            {
                float c2iMean = bgResult.GetImageValueFloat();
                float c2iMin = bgResult.GetImageValueFloat();
                float c2iMax = bgResult.GetImageValueFloat();
                StringBuilder sb = new StringBuilder();
                sb.Append("C/I均值：");
                sb.Append(c2iMean);
                sb.Append("\r\n");
                sb.Append("C/I最小值：");
                sb.Append(c2iMin);
                sb.Append("\r\n");
                sb.Append("C/I最大值：");
                sb.Append(c2iMax);
                bgResult.ImageDesc = sb.ToString();
            }
        }*/
        #endregion
    }


   
     

}
