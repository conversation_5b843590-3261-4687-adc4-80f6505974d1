﻿using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model.CellParam;
using MasterCom.RAMS.Model.CellParam.QueryByRegion;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using MasterCom.Util.UiEx;

namespace MasterCom.RAMS.Model
{
    public class QueryCellProblemPoint:QueryBase
    {
        public QueryCellProblemPoint(MainModel mm)
            : base(mm)
        { }
        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 20000, 20031, this.Name);
        }
        public override bool IsNeedSetQueryCondition
        {
            get
            {
                return false;
            }
        }
        public override string Name
        {
            get { return "按区域查询小区问题点"; }
        }
        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return searchGeometrys.IsSelectRegion();
        }
        public override string IconName
        {
            get { return ""; }
        }

        protected override bool isValidCondition()
        {
            return true;
        }
        private List<CellProblemPoint> problemPoints = null;
        protected override void query()
        {
            problemPoints = new List<CellProblemPoint>();
            if (!getCondition())
            {
                return;
            }
            getCellSigns();
            if (cellSignCellDic.Count == 0)
            {
                MessageBox.Show("该区域未能关联到小区！");
                return;
            }
            WaitTextBox.Show("正在查询问题点...",queryProblemPoints);
            if (problemPoints.Count>0)
            {
                fireShowResultForm();
                problemPoints = null;
            }
            else
            {
                MessageBox.Show("未能查询到问题点！");
            }
        }

        private void fireShowResultForm()
        {
            CellParamProblemPointForm frm = MainModel.GetObjectFromBlackboard(typeof(CellParamProblemPointForm).FullName) as CellParamProblemPointForm;
            if (frm==null)
            {
                frm = new CellParamProblemPointForm(MainModel);
            }
            frm.FillData(problemPoints);
            if (!frm.Visible)
            {
                frm.Show(MainModel.MainForm);
            }
            frm.BringToFront();
        }

        private void queryProblemPoints()
        {
            //CellParamProblemPointRuleMng.GetInstance();//初始化规则
            StringBuilder cellSb = new StringBuilder();
            Dictionary<int, CellSign> cellSignDic = new Dictionary<int, CellSign>();
            foreach (CellSign cellSign in cellSignCellDic.Keys)
            {
                if (!cellSignDic.ContainsKey(cellSign.SignID))
                {
                    cellSignDic.Add(cellSign.SignID, cellSign);
                }
                cellSb.Append(cellSign.SignID);
                cellSb.Append(',');
            }
            cellSb.Remove(cellSb.Length - 1, 1);
            StringBuilder sqlSb = new StringBuilder();
            sqlSb.Append(@"select aa.i小区ID,aa.问题点ID,aa.时间,aa.小区名称,aa.问题点描述,bb.规则名称,bb.参数规则描述 from
[MTNOH_APP_PARAMS].[dbo].[TB_参数问题点_问题点] aa,[MTNOH_APP_PARAMS].[dbo].TB_参数问题点_规则定义 bb
where aa.规则ID = bb.规则ID and aa.[时间] between '");
            sqlSb.Append(dtBegin.ToString("yyyy-MM-dd HH:mm:ss"));
            sqlSb.Append("' and '");
            sqlSb.Append(dtEnd.ToString("yyyy-MM-dd HH:mm:ss"));
            sqlSb.Append("' and aa.[i小区ID] in (");
            sqlSb.Append(cellSb.ToString());
            sqlSb.Append(");");
            try
            {
                using (SqlConnection conn = new SqlConnection(CellParamCfgManager.GetInstance().DBConnectionStr))
                {
                    SqlCommand command = new SqlCommand(sqlSb.ToString(), conn);

                    conn.Open();
                    SqlDataReader reader = command.ExecuteReader();
                    while (reader.Read())
                    {
                        try
                        {
                            CellProblemPoint pnt = new CellProblemPoint();
                            pnt.ID = (int)reader["问题点ID"];
                            pnt.DateTime = DateTime.Parse(reader["时间"].ToString());
                            pnt.CellSignID = (int)reader["i小区ID"];
                            pnt.Cell = cellSignCellDic[cellSignDic[pnt.CellSignID]];
                            pnt.CellName = reader["小区名称"].ToString();
                            pnt.Description = reader["问题点描述"].ToString();
                            pnt.RuleName = reader["规则名称"].ToString();
                            pnt.RuleDescription = reader["参数规则描述"].ToString();
                            problemPoints.Add(pnt);
                        }
                        catch (Exception ex)
                        {
                            MessageBox.Show(ex.ToString());
                        }
                    }
                }
            }
            finally
            {
                WaitTextBox.Close();
            }
        }

        protected DateTime dtBegin = DateTime.Now.AddMonths(-1);
        protected DateTime dtEnd = DateTime.Now;
        private bool getCondition()
        {
            CellSignParamConditionDlg dlg = new CellSignParamConditionDlg(dtBegin, dtEnd);
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                dtBegin = dlg.BeginTime;
                dtEnd = dlg.EndTime;
            }
            else
            {
                return false;
            }
           
            return true;
        }

        protected Dictionary<CellSign, object> cellSignCellDic = new Dictionary<CellSign, object>();
        protected virtual void getCellSigns()
        {
            cellSignCellDic.Clear();
            List<Cell> cells = MainModel.MainForm.GetMapForm().GetCellLayer().CellsInCurrentView;
            if (cells == null||cells.Count==0)
            {
                cells = MainModel.MainForm.GetMapForm().GetCellLayer().GetCellsByRegion(MainModel.SearchGeometrys.GeoOp);
            }
            else
            {
                List<Cell> regionCells = new List<Cell>();
                foreach (Cell cell in cells)
                {
                    if (MainModel.SearchGeometrys.GeoOp.Contains(cell.Longitude, cell.Latitude))
                    {
                        regionCells.Add(cell);
                    }
                }
                cells = regionCells;
            }
            if (cells != null)
            {
                TimePeriod period = new TimePeriod(dtBegin, dtEnd);
                addValidSignCell(cells, period);
            }
        }

        private void addValidSignCell(List<Cell> cells, TimePeriod period)
        {
            foreach (Cell cell in cells)
            {
                List<GSMCellSign> signs = CellSignManager.GetInstance().GetCellSign(period, cell);
                if (signs != null && signs.Count > 0)
                {
                    foreach (CellSign sign in signs)
                    {
                        cellSignCellDic.Add(sign, cell);
                    }
                }
            }
        }
    }
}
