﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class NRLastWeakMosAnaByTPForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            DevExpress.XtraGrid.GridLevelNode gridLevelNode1 = new DevExpress.XtraGrid.GridLevelNode();
            this.gvWeakMos = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn6 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn7 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn8 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn9 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn10 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn11 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn20 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn27 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn12 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn18 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn28 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn29 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridControl1 = new DevExpress.XtraGrid.GridControl();
            this.gv = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn21 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn25 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn26 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnBeginTime = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnEndTime = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn19 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn22 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn14 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn15 = new DevExpress.XtraGrid.Columns.GridColumn();
            ((System.ComponentModel.ISupportInitialize)(this.gvWeakMos)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gv)).BeginInit();
            this.SuspendLayout();
            // 
            // gvWeakMos
            // 
            this.gvWeakMos.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn6,
            this.gridColumn7,
            this.gridColumn8,
            this.gridColumn9,
            this.gridColumn10,
            this.gridColumn11,
            this.gridColumn20,
            this.gridColumn27,
            this.gridColumn12,
            this.gridColumn18,
            this.gridColumn28,
            this.gridColumn29});
            this.gvWeakMos.GridControl = this.gridControl1;
            this.gvWeakMos.Name = "gvWeakMos";
            this.gvWeakMos.OptionsBehavior.Editable = false;
            this.gvWeakMos.OptionsDetail.ShowDetailTabs = false;
            this.gvWeakMos.OptionsView.ColumnAutoWidth = false;
            this.gvWeakMos.OptionsView.ShowGroupPanel = false;
            this.gvWeakMos.OptionsView.ShowIndicator = false;
            this.gvWeakMos.DoubleClick += new System.EventHandler(this.gvWeakMos_DoubleClick);
            // 
            // gridColumn6
            // 
            this.gridColumn6.Caption = "弱MOS点时间";
            this.gridColumn6.FieldName = "WeakMOSTP.DateTimeStringWithMillisecond";
            this.gridColumn6.Name = "gridColumn6";
            this.gridColumn6.Visible = true;
            this.gridColumn6.VisibleIndex = 0;
            this.gridColumn6.Width = 150;
            // 
            // gridColumn7
            // 
            this.gridColumn7.Caption = "弱MOS点经度";
            this.gridColumn7.FieldName = "WeakMOSTP.Longitude";
            this.gridColumn7.Name = "gridColumn7";
            this.gridColumn7.Visible = true;
            this.gridColumn7.VisibleIndex = 1;
            this.gridColumn7.Width = 101;
            // 
            // gridColumn8
            // 
            this.gridColumn8.Caption = "弱MOS点纬度";
            this.gridColumn8.FieldName = "WeakMOSTP.Latitude";
            this.gridColumn8.Name = "gridColumn8";
            this.gridColumn8.Visible = true;
            this.gridColumn8.VisibleIndex = 2;
            this.gridColumn8.Width = 100;
            // 
            // gridColumn9
            // 
            this.gridColumn9.Caption = "弱MOS值";
            this.gridColumn9.FieldName = "MOS";
            this.gridColumn9.Name = "gridColumn9";
            this.gridColumn9.Visible = true;
            this.gridColumn9.VisibleIndex = 3;
            // 
            // gridColumn10
            // 
            this.gridColumn10.Caption = "NR_RSRP";
            this.gridColumn10.FieldName = "NRInfo.Rsrp";
            this.gridColumn10.Name = "gridColumn10";
            this.gridColumn10.Visible = true;
            this.gridColumn10.VisibleIndex = 4;
            // 
            // gridColumn11
            // 
            this.gridColumn11.Caption = "NR_SINR";
            this.gridColumn11.FieldName = "NRInfo.Sinr";
            this.gridColumn11.Name = "gridColumn11";
            this.gridColumn11.Visible = true;
            this.gridColumn11.VisibleIndex = 5;
            // 
            // gridColumn20
            // 
            this.gridColumn20.Caption = "NR_EARFCN";
            this.gridColumn20.FieldName = "NRInfo.Earfcn";
            this.gridColumn20.Name = "gridColumn20";
            this.gridColumn20.Visible = true;
            this.gridColumn20.VisibleIndex = 8;
            this.gridColumn20.Width = 92;
            // 
            // gridColumn27
            // 
            this.gridColumn27.Caption = "NR_PCI";
            this.gridColumn27.FieldName = "NRInfo.Pci";
            this.gridColumn27.Name = "gridColumn27";
            this.gridColumn27.Visible = true;
            this.gridColumn27.VisibleIndex = 9;
            // 
            // gridColumn12
            // 
            this.gridColumn12.Caption = "LTE_RSRP";
            this.gridColumn12.FieldName = "LTEInfo.Rsrp";
            this.gridColumn12.Name = "gridColumn12";
            this.gridColumn12.Visible = true;
            this.gridColumn12.VisibleIndex = 6;
            this.gridColumn12.Width = 83;
            // 
            // gridColumn18
            // 
            this.gridColumn18.Caption = "LTE_SINR";
            this.gridColumn18.FieldName = "LTEInfo.Sinr";
            this.gridColumn18.Name = "gridColumn18";
            this.gridColumn18.Visible = true;
            this.gridColumn18.VisibleIndex = 7;
            this.gridColumn18.Width = 81;
            // 
            // gridColumn28
            // 
            this.gridColumn28.Caption = "LTE_EARFCN";
            this.gridColumn28.FieldName = "LTEInfo.Earfcn";
            this.gridColumn28.Name = "gridColumn28";
            this.gridColumn28.Visible = true;
            this.gridColumn28.VisibleIndex = 10;
            this.gridColumn28.Width = 99;
            // 
            // gridColumn29
            // 
            this.gridColumn29.Caption = "LTE_PCI";
            this.gridColumn29.FieldName = "LTEInfo.Pci";
            this.gridColumn29.Name = "gridColumn29";
            this.gridColumn29.Visible = true;
            this.gridColumn29.VisibleIndex = 11;
            // 
            // gridControl1
            // 
            this.gridControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            gridLevelNode1.LevelTemplate = this.gvWeakMos;
            gridLevelNode1.RelationName = "WeakMosTPs";
            this.gridControl1.LevelTree.Nodes.AddRange(new DevExpress.XtraGrid.GridLevelNode[] {
            gridLevelNode1});
            this.gridControl1.Location = new System.Drawing.Point(0, 0);
            this.gridControl1.MainView = this.gv;
            this.gridControl1.Name = "gridControl1";
            this.gridControl1.ShowOnlyPredefinedDetails = true;
            this.gridControl1.Size = new System.Drawing.Size(953, 550);
            this.gridControl1.TabIndex = 1;
            this.gridControl1.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gv,
            this.gvWeakMos});
            // 
            // gv
            // 
            this.gv.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn21,
            this.gridColumn25,
            this.gridColumn26,
            this.gridColumn3,
            this.gridColumnBeginTime,
            this.gridColumnEndTime,
            this.gridColumn2,
            this.gridColumn19,
            this.gridColumn22,
            this.gridColumn14,
            this.gridColumn5,
            this.gridColumn4,
            this.gridColumn15});
            this.gv.GridControl = this.gridControl1;
            this.gv.Name = "gv";
            this.gv.OptionsBehavior.Editable = false;
            this.gv.OptionsDetail.ShowDetailTabs = false;
            this.gv.OptionsView.ColumnAutoWidth = false;
            this.gv.OptionsView.ShowGroupPanel = false;
            this.gv.OptionsView.ShowIndicator = false;
            // 
            // gridColumn21
            // 
            this.gridColumn21.Caption = "文件名";
            this.gridColumn21.FieldName = "FileName";
            this.gridColumn21.Name = "gridColumn21";
            this.gridColumn21.Visible = true;
            this.gridColumn21.VisibleIndex = 0;
            this.gridColumn21.Width = 202;
            // 
            // gridColumn25
            // 
            this.gridColumn25.Caption = "持续弱MOS起始经度";
            this.gridColumn25.FieldName = "Longitude";
            this.gridColumn25.Name = "gridColumn25";
            this.gridColumn25.Visible = true;
            this.gridColumn25.VisibleIndex = 5;
            this.gridColumn25.Width = 137;
            // 
            // gridColumn26
            // 
            this.gridColumn26.Caption = "持续弱MOS起始纬度";
            this.gridColumn26.FieldName = "Latitude";
            this.gridColumn26.Name = "gridColumn26";
            this.gridColumn26.Visible = true;
            this.gridColumn26.VisibleIndex = 6;
            this.gridColumn26.Width = 137;
            // 
            // gridColumn3
            // 
            this.gridColumn3.Caption = "持续弱MOS起始时间";
            this.gridColumn3.FieldName = "DateTime";
            this.gridColumn3.Name = "gridColumn3";
            this.gridColumn3.Visible = true;
            this.gridColumn3.VisibleIndex = 4;
            this.gridColumn3.Width = 150;
            // 
            // gridColumnBeginTime
            // 
            this.gridColumnBeginTime.Caption = "通话起始时间";
            this.gridColumnBeginTime.FieldName = "StartCallTime";
            this.gridColumnBeginTime.Name = "gridColumnBeginTime";
            this.gridColumnBeginTime.Visible = true;
            this.gridColumnBeginTime.VisibleIndex = 7;
            this.gridColumnBeginTime.Width = 150;
            // 
            // gridColumnEndTime
            // 
            this.gridColumnEndTime.Caption = "通话结束时间";
            this.gridColumnEndTime.FieldName = "EndCallTime";
            this.gridColumnEndTime.Name = "gridColumnEndTime";
            this.gridColumnEndTime.Visible = true;
            this.gridColumnEndTime.VisibleIndex = 8;
            this.gridColumnEndTime.Width = 150;
            // 
            // gridColumn2
            // 
            this.gridColumn2.Caption = "弱MOS点数";
            this.gridColumn2.FieldName = "TpNum";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.Visible = true;
            this.gridColumn2.VisibleIndex = 1;
            this.gridColumn2.Width = 91;
            // 
            // gridColumn19
            // 
            this.gridColumn19.Caption = "MOS均值";
            this.gridColumn19.FieldName = "MOSInfo.Avg";
            this.gridColumn19.Name = "gridColumn19";
            this.gridColumn19.Visible = true;
            this.gridColumn19.VisibleIndex = 2;
            this.gridColumn19.Width = 90;
            // 
            // gridColumn22
            // 
            this.gridColumn22.Caption = "持续距离";
            this.gridColumn22.FieldName = "Distance";
            this.gridColumn22.Name = "gridColumn22";
            this.gridColumn22.Visible = true;
            this.gridColumn22.VisibleIndex = 3;
            this.gridColumn22.Width = 79;
            // 
            // gridColumn14
            // 
            this.gridColumn14.Caption = "平均NR_RSRP";
            this.gridColumn14.FieldName = "NRInfo.RsrpInfo.Avg";
            this.gridColumn14.Name = "gridColumn14";
            this.gridColumn14.Visible = true;
            this.gridColumn14.VisibleIndex = 9;
            this.gridColumn14.Width = 100;
            // 
            // gridColumn5
            // 
            this.gridColumn5.Caption = "平均NR_SINR";
            this.gridColumn5.FieldName = "NRInfo.SinrInfo.Avg";
            this.gridColumn5.Name = "gridColumn5";
            this.gridColumn5.Visible = true;
            this.gridColumn5.VisibleIndex = 10;
            this.gridColumn5.Width = 98;
            // 
            // gridColumn4
            // 
            this.gridColumn4.Caption = "平均LTE_RSRP";
            this.gridColumn4.FieldName = "LTEInfo.RsrpInfo.Avg";
            this.gridColumn4.Name = "gridColumn4";
            this.gridColumn4.Visible = true;
            this.gridColumn4.VisibleIndex = 11;
            this.gridColumn4.Width = 107;
            // 
            // gridColumn15
            // 
            this.gridColumn15.Caption = "平均LTE_SINR";
            this.gridColumn15.FieldName = "LTEInfo.SinrInfo.Avg";
            this.gridColumn15.Name = "gridColumn15";
            this.gridColumn15.Visible = true;
            this.gridColumn15.VisibleIndex = 12;
            this.gridColumn15.Width = 104;
            // 
            // NRLastWeakMosAnaByTPForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(953, 550);
            this.Controls.Add(this.gridControl1);
            this.Name = "NRLastWeakMosAnaByTPForm";
            this.Text = "持续弱MOS";
            ((System.ComponentModel.ISupportInitialize)(this.gvWeakMos)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gv)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraGrid.GridControl gridControl1;
        private DevExpress.XtraGrid.Views.Grid.GridView gv;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn14;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn19;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn15;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn21;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn22;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn25;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn26;
        private DevExpress.XtraGrid.Views.Grid.GridView gvWeakMos;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnBeginTime;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnEndTime;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn6;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn7;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn8;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn9;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn10;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn11;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn20;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn27;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn12;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn18;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn28;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn29;
    }
}