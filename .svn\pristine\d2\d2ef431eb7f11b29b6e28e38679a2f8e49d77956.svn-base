﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.Util;
using DevExpress.XtraEditors;
using System.IO;
using System.Collections;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.CQT
{
    public partial class CQTKPIReportEditForm : BaseDialog
    {
        private CQTKPIColumnPanel cqtkpiColumnPanel;
        private KPISummaryColumnPanel summaryPanel;
        public CQTKPIReportEditForm()
        {
            InitializeComponent();
            cqtkpiColumnPanel = new CQTKPIColumnPanel();
            cqtkpiColumnPanel.Dock = DockStyle.Fill;
            splitContainerControl1.Panel2.Controls.Add(cqtkpiColumnPanel);
            cqtkpiColumnPanel.Enabled = false;
            cqtkpiColumnPanel.Visible = false;

            summaryPanel = new KPISummaryColumnPanel();
            summaryPanel.Dock = DockStyle.Fill;
            splitContainerControl1.Panel2.Controls.Add(summaryPanel);
            summaryPanel.Enabled = false;
            summaryPanel.Visible = false;

            comboBoxReports.EditValueChanged += new EventHandler(comboBoxReports_EditValueChanged);
        }

        void comboBoxReports_EditValueChanged(object sender, EventArgs e)
        {
            if (curSelRpt != null && curSelRpt == barEditItemCurReport.EditValue && ((ComboBoxEdit)sender).SelectedIndex == -1)
            {
                curSelRpt.Name = ((ComboBoxEdit)sender).EditValue.ToString();
            }
        }

        private CQTKPIReportCfgManager rptMng = null;
        private CQTKPIReport curSelRpt = null;
        private object curSelCol = null;
        public void FillData(CQTKPIReportCfgManager rptMng, CQTKPIReport selRpt)
        {
            this.rptMng = rptMng;
            fillReportCombBox(rptMng.Reports, selRpt);
        }

        private void fillReportCombBox(List<CQTKPIReport> rpts, CQTKPIReport selRpt)
        {
            comboBoxReports.Items.Clear();
            foreach (CQTKPIReport rpt in rpts)
            {
                comboBoxReports.Items.Add(rpt);
            }
            barEditItemCurReport.EditValue = selRpt;
        }

        private void barEditItemCurReport_EditValueChanged(object sender, EventArgs e)
        {
            if (barEditItemCurReport.EditValue.GetType().Equals(typeof(string)))
            {
                return;
            }
            CQTKPIReport rpt = barEditItemCurReport.EditValue as CQTKPIReport;
            curSelRpt = rpt;
            if (curSelRpt != null && barEditItemCurReport.EditValue != null)
            {
                curSelRpt.Name = barEditItemCurReport.EditValue.ToString();
            }
            fillColumnNaviView();
            splitContainerControl1.Enabled = curSelRpt != null;
        }

        private void btnAddColumn_Click(object sender, EventArgs e)
        {
            if (curSelRpt == null)
            {
                return;
            }
            NewColumnBox nameBox = new NewColumnBox("新增列", "列名", "未命名");
            nameBox.ShowInTaskbar = false;
            if (nameBox.ShowDialog() == DialogResult.OK)
            {
                if (curSelRpt is CQTKPIReport_PK)
                {
                    CQTKPIReport_PK rpt = curSelRpt as CQTKPIReport_PK;
                    CQTKPIReportColumn_PK col = new CQTKPIReportColumn_PK();
                    col.Name = nameBox.TextInput;
                    col.CarreerID2 = rpt.CarreerID2;
                    rpt.Columns_PK.Add(col);
                }
                else
                {
                    if (nameBox.ColumnType == 0)
                    {
                        CQTKPIReportColumn col = new CQTKPIReportColumn(nameBox.TextInput, curSelRpt.CarreerID, 0, 100, 0, 100);
                        curSelRpt.Columns.Add(col);
                    }
                    else
                    {
                        CQTKPISummaryColumn col = new CQTKPISummaryColumn(curSelRpt, nameBox.TextInput);
                        curSelRpt.SummaryColumns.Add(col);
                    }

                }
                fillColumnNaviView();
                if (dataGridViewNavi.RowCount > 0)
                {
                    dataGridViewNavi.Rows[dataGridViewNavi.RowCount - 1].Selected = true;
                }
                selectColumnChanged();
            }
        }

        private void btnDelColumn_Click(object sender, EventArgs e)
        {
            if (curSelCol != null && curSelRpt != null)
            {
                if (curSelRpt is CQTKPIReport_PK)
                {
                    (curSelRpt as CQTKPIReport_PK).Columns_PK.Remove((CQTKPIReportColumn_PK)curSelCol);
                }
                else
                {
                    if (curSelCol is CQTKPISummaryColumn)
                    {
                        curSelRpt.SummaryColumns.Remove(curSelCol as CQTKPISummaryColumn);
                    }
                    else if (curSelCol is CQTKPIReportColumn)
                    {
                        curSelRpt.Columns.Remove(curSelCol as CQTKPIReportColumn);
                    }
                }
                curSelCol = null;
                fillColumnNaviView();
                if (curSelRpt.Columns.Count > 0)
                {
                    dataGridViewNavi.Rows[curSelRpt.Columns.Count - 1].Selected = true;
                }
                selectColumnChanged();
            }
        }

        private void fillColumnNaviView()
        {
            unbundingDataGirdView();
            dataGridViewNavi.Rows.Clear();
            if (curSelRpt is CQTKPIReport_PK)
            {
                CQTKPIReport_PK rpt = curSelRpt as CQTKPIReport_PK;
                foreach (CQTKPIReportColumn_PK col in rpt.Columns_PK)
                {
                    int idx = dataGridViewNavi.Rows.Add();
                    DataGridViewRow row = dataGridViewNavi.Rows[idx];
                    row.Cells[0].Value = col.Name;
                    row.Tag = col;
                }
            }
            else if (curSelRpt != null)
            {
                CQTKPIReport rpt = curSelRpt;
                foreach (CQTKPIReportColumn col in rpt.Columns)
                {
                    int idx = dataGridViewNavi.Rows.Add();
                    DataGridViewRow row = dataGridViewNavi.Rows[idx];
                    row.Cells[0].Value = col.Name;
                    row.Tag = col;
                }
                foreach (CQTKPISummaryColumn col in rpt.SummaryColumns)
                {
                    int idx = dataGridViewNavi.Rows.Add();
                    DataGridViewRow row = dataGridViewNavi.Rows[idx];
                    row.Cells[0].Value = col.Name;
                    row.Tag = col;
                }
            }
            bundingDataGridView();
            if (dataGridViewNavi.Rows.Count > 0)
            {
                splitContainerControl1.Panel2.Enabled = true;
            }
            else
            {
                splitContainerControl1.Panel2.Enabled = false;
            }
        }
        private void unbundingDataGirdView()
        {
            dataGridViewNavi.CellClick -= dataGridViewNavi_CellClick;
            dataGridViewNavi.SelectionChanged -= dataGridViewNavi_SelectionChanged;
        }
        private void bundingDataGridView()
        {
            dataGridViewNavi.CellClick += dataGridViewNavi_CellClick;
            dataGridViewNavi.SelectionChanged += dataGridViewNavi_SelectionChanged;
        }

        #region menubar
        private void biNew_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            NewReportDlg dlg = new NewReportDlg();
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                if (dlg.ReportType == 0)//单报表
                {
                    CQTKPIReport newRpt = new CQTKPIReport(dlg.ReportName);
                    newRpt.CarreerID = dlg.CarreeID1;
                    rptMng.Reports.Add(newRpt);
                    fillReportCombBox(rptMng.Reports, newRpt);
                }
                else//竞争对比报表
                {
                    CQTKPIReport_PK newRpt = new CQTKPIReport_PK(dlg.ReportName);
                    newRpt.CarreerID = dlg.CarreeID1;
                    newRpt.CarreerID2 = dlg.CarreeID2;
                    rptMng.Reports.Add(newRpt);
                    fillReportCombBox(rptMng.Reports, newRpt);
                }
            }
        }

        private void biOpen_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            OpenFileDialog dlg = new OpenFileDialog();
            dlg.Multiselect = true;
            dlg.RestoreDirectory = true;
            dlg.AddExtension = true;
            dlg.Filter = "指标报表 (*" + CQTKPIReport.ReportExtension + ",*" + CQTKPIReport_PK.ReportExtension + ")|*"
                + CQTKPIReport.ReportExtension + ";*" + CQTKPIReport_PK.ReportExtension;
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                foreach (string fileName in dlg.FileNames)
                {
                    addValidRpt(fileName);
                }
                fillReportCombBox(rptMng.Reports, rptMng.Reports.Count > 0 ? rptMng.Reports[0] : null);
            }
        }

        private void addValidRpt(string fileName)
        {
            if (Path.GetExtension(fileName) == CQTKPIReport.ReportExtension)
            {
                CQTKPIReport rpt = CQTKPIReport.Open(fileName);
                if (rpt != null)
                {
                    rptMng.Reports.Add(rpt);
                }
                else
                {
                    MessageBox.Show("文件:\r\n" + fileName + "\r\n不是有效的指标报表！");
                }
            }
            else if (Path.GetExtension(fileName) == CQTKPIReport_PK.ReportExtension)
            {
                CQTKPIReport_PK rpt = CQTKPIReport_PK.Open(fileName);
                if (rpt != null)
                {
                    rptMng.Reports.Add(rpt);
                }
                else
                {
                    MessageBox.Show("文件:\r\n" + fileName + "\r\n不是有效的指标报表！");
                }
            }
        }

        private void biSave_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (curSelRpt != null)
            {
                curSelRpt.Save();
            }
        }

        private void biSaveAs_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (curSelRpt == null)
            {
                return;
            }
            SaveFileDialog dlg = new SaveFileDialog();
            dlg.RestoreDirectory = true;
            dlg.InitialDirectory = CQTKPIReportCfgManager.FolderName;
            if (curSelRpt is CQTKPIReport_PK)
            {
                dlg.Filter = "指标报表 (*" + CQTKPIReport_PK.ReportExtension + ")|*" + CQTKPIReport_PK.ReportExtension;
            }
            else
            {
                dlg.Filter = "指标报表 (*" + CQTKPIReport.ReportExtension + ")|*" + CQTKPIReport.ReportExtension;
            }
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                curSelRpt.Save(dlg.FileName);
            }
        }

        private void biSaveAll_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (rptMng != null)
            {
                rptMng.SaveAllReport();
            }
        }

        private void biDelelte_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (curSelRpt != null)
            {
                DialogResult res = XtraMessageBox.Show(this, "确定删除当前报表？", "确认删除", MessageBoxButtons.OKCancel);
                if (res == DialogResult.OK)
                {
                    if (File.Exists(curSelRpt.FileName))
                    {
                        File.Delete(curSelRpt.FileName);
                    }
                    rptMng.Reports.Remove(curSelRpt);
                    curSelRpt = null;
                    fillReportCombBox(rptMng.Reports, rptMng.Reports.Count > 0 ? rptMng.Reports[0] : null);
                }
            }
        }
        #endregion

        private void selectColumnChanged()
        {
            object row = null;
            if (dataGridViewNavi.SelectedRows.Count > 0)
            {
                splitContainerControl1.Panel2.Enabled = true;
                row = dataGridViewNavi.SelectedRows[0].Tag;
            }
            else
            {
                cqtkpiColumnPanel_PK.SetColumn(null);
                cqtkpiColumnPanel.SetColumn(null);
                curSelCol = null;
                splitContainerControl1.Panel2.Enabled = false;
            }
            if (curSelCol is CQTKPIReportColumn)
            {
                (curSelCol as CQTKPIReportColumn).PropertyChanged -= col_PropertyChanged;
            }
            if (row is CQTKPIReportColumn_PK)
            {
                summaryPanel.Visible = false;
                cqtkpiColumnPanel.Visible = false;
                cqtkpiColumnPanel_PK.Dock = DockStyle.Fill;
                cqtkpiColumnPanel_PK.Visible = true;
                cqtkpiColumnPanel_PK.Enabled = true;

                CQTKPIReportColumn_PK col = row as CQTKPIReportColumn_PK;
                cqtkpiColumnPanel_PK.SetColumn(col);
                curSelCol = col;
                col.PropertyChanged += new EventHandler(col_PropertyChanged);
            }
            else if (row is CQTKPIReportColumn)
            {
                cqtkpiColumnPanel_PK.Visible = false;
                summaryPanel.Visible = false;
                cqtkpiColumnPanel.Dock = DockStyle.Fill;
                cqtkpiColumnPanel.Visible = true;
                cqtkpiColumnPanel.Enabled = true;

                CQTKPIReportColumn col = row as CQTKPIReportColumn;
                cqtkpiColumnPanel.SetColumn(col);
                curSelCol = col;
                col.PropertyChanged += new EventHandler(col_PropertyChanged);
            }
            else if (row is CQTKPISummaryColumn)
            {
                cqtkpiColumnPanel_PK.Visible = false;
                cqtkpiColumnPanel.Visible = false;

                summaryPanel.Dock = DockStyle.Fill;
                summaryPanel.Visible = true;
                summaryPanel.Enabled = true;

                CQTKPISummaryColumn col = row as CQTKPISummaryColumn;
                summaryPanel.SetColumn(col);
                curSelCol = col;
            }
        }

        void col_PropertyChanged(object sender, EventArgs e)
        {
            if (sender != null && sender.Equals(dataGridViewNavi.CurrentRow.Tag)
                && dataGridViewNavi.CurrentRow.Cells[0].Value.ToString() != dataGridViewNavi.CurrentRow.Tag.ToString())
            {
                dataGridViewNavi.CurrentRow.Cells[0].Value = dataGridViewNavi.CurrentRow.Tag.ToString();
            }
        }

        private void dataGridViewNavi_CellClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex > -1 && dataGridViewNavi.CurrentRow.Tag != curSelCol)
            {
                selectColumnChanged();
            }
        }

        private void dataGridViewNavi_SelectionChanged(object sender, EventArgs e)
        {
            selectColumnChanged();
        }

        private void dataGridViewNavi_CellEndEdit(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex < 0)
            {
                return;
            }
            object tag = dataGridViewNavi.Rows[e.RowIndex].Tag;
            if (tag is CQTKPIReportColumn)
            {
                (tag as CQTKPIReportColumn).Name = dataGridViewNavi[0, e.RowIndex].Value.ToString();
            }
            else if (tag is CQTKPIReportColumn_PK)
            {
                (tag as CQTKPIReportColumn_PK).Name = dataGridViewNavi[0, e.RowIndex].Value.ToString();
            }
            else if (tag is CQTKPISummaryColumn)
            {
                (tag as CQTKPISummaryColumn).Name = dataGridViewNavi[0, e.RowIndex].Value.ToString();
            }
            selectColumnChanged();
        }

    }
}
