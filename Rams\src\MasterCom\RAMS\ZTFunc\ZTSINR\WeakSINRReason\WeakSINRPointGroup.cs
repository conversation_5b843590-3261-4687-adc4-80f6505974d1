﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTSINR.WeakSINRReason
{
    public class WeakSINRPointGroup
    {
        public override bool Equals(object obj)
        {
            WeakSINRPointGroup other = obj as WeakSINRPointGroup;
            if (other==null)
            {
                return false;
            }
            return this.grpInfo.Equals(other.grpInfo);
        }
        public override string ToString()
        {
            return grpInfo.ToString();
        }
        public override int GetHashCode()
        {
            return grpInfo.GetHashCode();
        }
        private readonly object grpInfo = null;
        public object GroupInfo
        {
            get { return grpInfo; }
        }
        private int totalPntCnt = 0;
        public int TotalPntCnt
        {
            get { return totalPntCnt; }
        }
        private int weakPntCnt = 0;
        public int WeakPointCnt
        {
            get { return weakPntCnt; }
        }
        private readonly Dictionary<ReasonBase, List<WeakSINRPoint>> reasonPntDic = new Dictionary<ReasonBase, List<WeakSINRPoint>>();
        public Dictionary<ReasonBase, List<WeakSINRPoint>> ReasonPntDic
        {
            get { return reasonPntDic; }
        }
        public List<WeakSINRPoint> Points
        {
            get
            {
                List<WeakSINRPoint> list = new List<WeakSINRPoint>();
                foreach (ReasonBase item in reasonPntDic.Keys)
                {
                    list.AddRange(reasonPntDic[item]);
                }
                return list;
            }
        }
        public WeakSINRPointGroup(object grpItem, List<ReasonBase> reasons)
        {
            this.grpInfo = grpItem;
            foreach (ReasonBase item in reasons)
            {
                reasonPntDic[item] = new List<WeakSINRPoint>();
            }
        }

        public void AddWeakSINRPoint(WeakSINRPoint wp)
        {
            if (wp != null)
            {
                weakPntCnt++;
                reasonPntDic[wp.Reason].Add(wp);
            }
            else
            {
                totalPntCnt++;
            }
        }

        public void Gather(WeakSINRPointGroup otherGrp)
        {
            totalPntCnt += otherGrp.totalPntCnt;
            weakPntCnt += otherGrp.weakPntCnt;
            foreach (KeyValuePair<ReasonBase, List<WeakSINRPoint>> kvp in otherGrp.ReasonPntDic)
            {
                reasonPntDic[kvp.Key].AddRange(kvp.Value);
            }
        }


    }
}
