﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause
{
    [Serializable]
    public class OverCoverCause : CauseBase
    {
        public OverCoverCause()
        {
            AddSubReason(new OverCoverNoSite());
            AddSubReason(new OverCoverLackNbCell());
            AddSubReason(new OverCoverHandoverParam());
        }
        public override string Name
        {
            get
            {
                return "过覆盖";
            }
        }
        
        public float RSRPMin { get; set; } = -85;
        
        public float OverRatio { get; set; } = 1.6f;

        public override string Desc
        {
            get
            {
                return string.Format("采样点信号强度超过{0}，采样点与小区之间的距离超过小区理想覆盖半径的{1}倍"
                    , RSRPMin, OverRatio);
            }
        }

        public override string Suggestion
        {
            get { return null; }
        }

        public override void Judge(LowSpeedSeg segItem, List<Event> evts, List<TestPoint> allTP)
        {
            foreach (TestPoint pnt in segItem.TestPoints)
            {
                if (!segItem.IsNeedJudge(pnt))
                {
                    continue;
                }
                float? rsrp = (float?)GetRSRP(pnt);
                if (rsrp > RSRPMin)
                {
                    continue;
                }
                LTECell sCell = pnt.GetMainLTECell_TdOrFdd();
                if (sCell == null)
                {
                    continue;
                }
                double sDistance = pnt.Distance2(sCell.Longitude, sCell.Latitude);
                double radiusOfCell = MasterCom.ES.Data.CfgDataProvider.CalculateRadius(sCell, 3);
                if (sDistance <= radiusOfCell * 1.6)
                {
                    continue;
                }

                dealLowSpeedSeg(segItem, pnt);
                if (!segItem.NeedJudge)
                {
                    return;
                }
            }
        }

        private void dealLowSpeedSeg(LowSpeedSeg segItem, TestPoint pnt)
        {
            foreach (CauseBase subReason in SubCauses)
            {
                if (!segItem.IsNeedJudge(pnt))
                {
                    break;
                }
                subReason.JudgeSinglePoint(segItem, pnt);
            }

            if (segItem.IsNeedJudge(pnt))
            {
                UnknowReason r = new UnknowReason();
                r.Parent = this;
                segItem.SetReason(new LowSpeedPointDetail(pnt, r));
            }
        }

        protected object GetRSRP(TestPoint tp)
        {
            if (tp is LTEFddTestPoint)
            {
                return tp["lte_fdd_RSRP"];
            }
            return tp["lte_RSRP"];
        }

        public override Dictionary<string, object> CfgParam
        {
            get
            {
                Dictionary<string, object> paramDic = new Dictionary<string, object>();
                paramDic["TypeName"] = this.GetType().FullName;

                paramDic["rsrpMin"] = this.RSRPMin;

                List<object> list = new List<object>();
                foreach (CauseBase cause in SubCauses)
                {
                    list.Add(cause.CfgParam);
                }
                paramDic["SubCauseSet"] = list;

                return paramDic;

            }
            set
            {
                if (value == null)
                {
                    return;
                }

                this.RSRPMin = (float)value["rsrpMin"];

                SubCauses = new List<CauseBase>();
                List<object> list = value["SubCauseSet"] as List<object>;
                foreach (object item in list)
                {
                    Dictionary<string, object> dic = item as Dictionary<string, object>;
                    string typeName = dic["TypeName"].ToString();
                    System.Reflection.Assembly assembly = System.Reflection.Assembly.GetExecutingAssembly();
                    CauseBase cause = (CauseBase)assembly.CreateInstance(typeName);
                    cause.CfgParam = dic;
                    AddSubReason(cause);
                }
            }
        }
    }

    [Serializable]
    public class OverCoverNoSite : CauseBase
    {
        public override string Name
        {
            get { return "缺站"; }
        }
        //[NonSerialized]
        //private ICell serverCell = null;
        public float RadiusMin { get; set; } = 500;
        public override string Desc
        {
            get { return string.Format("主服和邻区距离都超过{0}米，并且周围{0}米内，没有其它LTE基站", RadiusMin); }
        }

        public override string Suggestion
        {
            get
            {
                return "合理规划站点";
            }
        }

        public override void JudgeSinglePoint(LowSpeedSeg segItem, TestPoint testPoint)
        {
            LTECell sCell = testPoint.GetMainLTECell_TdOrFdd();
            if (sCell == null)
            {
                return;
            }
            if (testPoint.Distance2(sCell.Longitude, sCell.Latitude) < RadiusMin)
            {//主服小区在x米内
                return;
            }
            for (int i = 0; i < 10; i++)
            {
                LTECell nCell = testPoint.GetNBLTECell_TdOrFdd(i);
                if (nCell == null)
                {
                    continue;
                }
                if (testPoint.Distance2(nCell.Longitude, nCell.Latitude) < RadiusMin)
                {//存在邻区小区在x米内
                    return;
                }
            }
            double distanceX = RadiusMin * 0.00001;
            List<LTECell> cells = CellManager.GetInstance().GetLTECells(testPoint.DateTime);
            foreach (LTECell cell in cells)
            {
                if (Math.Abs(cell.Longitude - testPoint.Longitude) > distanceX
                    || Math.Abs(cell.Latitude - testPoint.Latitude) > distanceX)
                {//粗略计算
                    continue;
                }
                if (testPoint.Distance2(cell.Longitude, cell.Latitude) <= RadiusMin)
                {
                    return;
                }
            }
            OverCoverNoSite cln = this.Clone() as OverCoverNoSite;
            segItem.SetReason(new LowSpeedPointDetail(testPoint, cln));
        }


        public override Dictionary<string, object> CfgParam
        {
            get
            {
                Dictionary<string, object> paramDic = new Dictionary<string, object>();
                paramDic["TypeName"] = this.GetType().FullName;
                paramDic["radiusMin"] = this.RadiusMin;

                return paramDic;
            }
            set
            {
                if (value == null)
                {
                    return;
                }

                this.RadiusMin = (float)value["radiusMin"];
            }
        }
    }

    [Serializable]
    public class OverCoverLackNbCell : CauseBase
    {
        public override string Name
        {
            get { return "漏配邻区关系"; }
        }
        [NonSerialized]
        private ICell serverCell;
        [NonSerialized]
        private ICell nbCell;
        public float RadiusMin { get; set; } = 500;
        public override string Desc
        {
            get
            {
                return "主服和邻区距离都超过X米，周围X米内有LTE基站，但基站中的小区都没有出现在邻区中";
            }
        }

        public override string Suggestion
        {
            get
            {
                return string.Format("核查邻区关系（核查主服{0}和{1}邻区关系）"
                    , serverCell != null ? serverCell.Name : "", nbCell != null ? nbCell.Name : "");
            }
        }

        public override void JudgeSinglePoint(LowSpeedSeg segItem, TestPoint testPoint)
        {
            LTECell sCell = testPoint.GetMainLTECell_TdOrFdd();
            if (sCell == null)
            {
                return;
            }
            if (testPoint.Distance2(sCell.Longitude, sCell.Latitude) < RadiusMin)
            {//主服小区在x米内
                return;
            }
            for (int i = 0; i < 10; i++)
            {
                LTECell nCell = testPoint.GetNBLTECell_TdOrFdd(i);
                if (nCell == null)
                {
                    continue;
                }
                if (testPoint.Distance2(nCell.Longitude, nCell.Latitude) < RadiusMin)
                {//存在邻区小区在x米内
                    return;
                }
            }
            double distanceX = RadiusMin * 0.00001;
            List<LTECell> cells = CellManager.GetInstance().GetLTECells(testPoint.DateTime);
            foreach (LTECell cell in cells)
            {
                if (Math.Abs(cell.Longitude - testPoint.Longitude) > distanceX
                    || Math.Abs(cell.Latitude - testPoint.Latitude) > distanceX)
                {//粗略计算
                    continue;
                }
                if (testPoint.Distance2(cell.Longitude, cell.Latitude) < RadiusMin)
                {
                    OverCoverLackNbCell cln = this.Clone() as OverCoverLackNbCell;
                    cln.serverCell = sCell;
                    cln.nbCell = cell;
                    segItem.SetReason(new LowSpeedPointDetail(testPoint, cln));
                    return;
                }
            }
        }

        public override Dictionary<string, object> CfgParam
        {
            get
            {
                Dictionary<string, object> paramDic = new Dictionary<string, object>();
                paramDic["TypeName"] = this.GetType().FullName;
                paramDic["radiusMin"] = this.RadiusMin;

                return paramDic;
            }
            set
            {
                if (value == null)
                {
                    return;
                }

                this.RadiusMin = (float)value["radiusMin"];
            }
        }
    }

    [Serializable]
    public class OverCoverHandoverParam : CauseBase
    {
        public override string Name
        {
            get { return "切换参数不合理"; }
        }
        [NonSerialized]
        private ICell serverCell;
        [NonSerialized]
        private ICell nbCell;
        public float RSRPDiff { get; set; } = 10;
        public override string Desc
        {
            get
            {
                return string.Format("邻区比主服信号超过{0}dB，并且距离更近", RSRPDiff);
            }
        }

        public override string Suggestion
        {
            get
            {
                return "核查主服的切出参数和邻区的切入参数";
            }
        }

        public override void JudgeSinglePoint(LowSpeedSeg segItem, TestPoint testPoint)
        {
            LTECell sCell = testPoint.GetMainLTECell_TdOrFdd();
            if (sCell == null)
            {
                return;
            }
            float? rsrp = (float?)GetRSRP(testPoint);
            double sDistance = testPoint.Distance2(sCell.Longitude, sCell.Latitude);
            for (int i = 0; i < 10; i++)
            {
                LTECell nCell = testPoint.GetNBLTECell_TdOrFdd(i);
                if (nCell == null)
                {
                    continue;
                }
                float? nRsrp = (float?)GetNRSRP(testPoint, i);
                if (nRsrp == null)
                {
                    continue;
                }
                double dis = testPoint.Distance2(nCell.Longitude, nCell.Latitude);
                if (nRsrp - rsrp >= RSRPDiff && dis < sDistance)
                {
                    OverCoverHandoverParam cln = this.Clone() as OverCoverHandoverParam;
                    cln.serverCell = sCell;
                    cln.nbCell = nCell;
                    segItem.SetReason(new LowSpeedPointDetail(testPoint, cln));
                    return;
                }
            }
        }

        protected object GetRSRP(TestPoint tp)
        {
            if (tp is LTEFddTestPoint)
            {
                return tp["lte_fdd_RSRP"];
            }
            return tp["lte_RSRP"];
        }
        protected object GetNRSRP(TestPoint tp, int index)
        {
            if (tp is LTEFddTestPoint)
            {
                return tp["lte_fdd_NCell_RSRP", index];
            }
            return tp["lte_NCell_RSRP", index];
        }

        public override Dictionary<string, object> CfgParam
        {
            get
            {
                Dictionary<string, object> paramDic = new Dictionary<string, object>();
                paramDic["TypeName"] = this.GetType().FullName;

                paramDic["rsrpDiff"] = this.RSRPDiff;

                return paramDic;
            }
            set
            {
                if (value == null)
                {
                    return;
                }

                this.RSRPDiff = (float)value["rsrpDiff"];
            }
        }
    }

}
