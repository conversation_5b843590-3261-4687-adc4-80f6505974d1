﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Util;
using MasterCom.RAMS.Frame;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.CQT
{
    public partial class ScoreRangeColorSettingPanel : UserControl
    {
        public ScoreRangeColorSettingPanel()
        {
            InitializeComponent();
        }
        public void SetScoreColumnCaption(string caption)
        {
            this.columnRange.HeaderText = caption;
        }
        public bool DescColumnsVisible
        {
            get { return columnDesc.Visible; }
            set { columnDesc.Visible = value; }
        }
        private double rangeMin;
        private double rangeMax;
        private List<DTParameterRangeColor> ranges;
        public List<DTParameterRangeColor> Ranges
        {
            get
            {
                if (ranges == null || ranges.Count == 0)
                {
                    return new List<DTParameterRangeColor>();
                }
                return ranges;
            }
        }
        public void SetScoreColorRanges(List<DTParameterRangeColor> ranges, double scoreMin, double scoreMax)
        {
            this.ranges = ranges;
            if (ranges==null)
            {
                this.ranges = new List<DTParameterRangeColor>();
            }
            rangeMin = scoreMin;
            rangeMax = scoreMax;
            checkButtonState();
            rowCountChanged();
        }

        private void addRangeValue(DTParameterRangeColor rangeValue)
        {
            if (ranges == null)
            {
                return;
            }
            for (int i = 0; i < ranges.Count; i++)
            {
                if (rangeValue.Min < ranges[i].Min)
                {
                    ranges.Insert(i, rangeValue);
                    return;
                }
            }
            ranges.Add(rangeValue);
        }

        private void checkButtonState()
        {
            buttonModify.Enabled = dataGridView.SelectedRows.Count == 1;
            buttonRemove.Enabled = dataGridView.SelectedRows.Count == 1;
        }
        public void UpdateRange(double rangeMin,double rangeMax)
        {
            this.rangeMin = rangeMin;
            this.rangeMax = rangeMax;
            checkRange();
        }
        public void checkRange()
        {
            if (ranges == null)
            {
                return;
            }
            bool changed = false;
            foreach (DTParameterRangeColor range in ranges)
            {
                if (range.Min < (float)rangeMin)
                {
                    range.Min = (float)rangeMin;
                    changed = true;
                }
                if (range.Max > (float)rangeMax)
                {
                    range.Max = (float)rangeMax;
                    changed = true;
                }
            }
            if (changed)
            {
                rowCountChanged();
            }
        }

        private void rowCountChanged()
        {
            if (ranges == null)
            {
                dataGridView.RowCount = 0;
                dataGridView.Invalidate();
                return;
            }
            dataGridView.RowCount = ranges.Count;
            foreach (DataGridViewRow row in dataGridView.Rows)
            {
                row.Height = 18;
            }
            dataGridView.Invalidate();
        }

        private void buttonAdd_Click(object sender, EventArgs e)
        {
            DTParameterRangeColor colorRange = new DTParameterRangeColor((float)rangeMin, (float)rangeMax, Color.Green);
            RangeColorSettingBox box = new RangeColorSettingBox(colorRange, (float)rangeMin, (float)rangeMax);
            if (box.ShowDialog() == DialogResult.OK)
            {
                addRangeValue((DTParameterRangeColor)box.RangeValue);
                rowCountChanged();
            }
        }

        private void buttonModify_Click(object sender, EventArgs e)
        {
            if (dataGridView.SelectedRows.Count > 0)
            {
                DTParameterRangeColor rangeValue = ranges[dataGridView.SelectedRows[0].Index];
                RangeColorSettingBox box = new RangeColorSettingBox(rangeValue, (float)rangeMin, (float)rangeMax);
                if (box.ShowDialog() == DialogResult.OK)
                {
                    dataGridView.Invalidate();
                }
            }
            else
            {
                checkButtonState();
            }
        }

        private void buttonRemove_Click(object sender, EventArgs e)
        {
            if (dataGridView.SelectedRows.Count==0)
            {
                checkButtonState();
                return;
            }
            ranges.RemoveAt(dataGridView.SelectedRows[0].Index);
            rowCountChanged();
        }

        private void buttonAutoSetting_Click(object sender, EventArgs e)
        {
            RangeColorAutoSettingBox box = new RangeColorAutoSettingBox((float)rangeMin, (float)rangeMax);
            if (box.ShowDialog() == DialogResult.OK)
            {
                ranges.Clear();
                
                foreach (DTParameterRange rangeValue in box.RangeValues)
                {
                    DTParameterRangeColor rangeColor = rangeValue as DTParameterRangeColor;
                    addRangeValue(rangeColor);
                }
                rowCountChanged();
            }
        }

        private void dataGridView_CellFormatting(object sender, DataGridViewCellFormattingEventArgs e)
        {
            if (ranges.Count > 0 && e.ColumnIndex == 1)
            {
                dataGridView[e.ColumnIndex, e.RowIndex].Style.BackColor = ranges[e.RowIndex].Value;
                dataGridView[e.ColumnIndex, e.RowIndex].Style.SelectionBackColor = ranges[e.RowIndex].Value;
            }
        }

        private void dataGridView_CellValueNeeded(object sender, DataGridViewCellValueEventArgs e)
        {
            if (ranges==null||ranges.Count==0)
            {
                return;
            }
            if (e.ColumnIndex == 0)
            {
                e.Value = ranges[e.RowIndex].RangeDescription;
            }
            else if (e.ColumnIndex == 2)
            {
                e.Value = ranges[e.RowIndex].DesInfo;
            }
        }

        private void dataGridView_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            buttonModify_Click(null, null);
        }

        private void dataGridView_SelectionChanged(object sender, EventArgs e)
        {
            checkButtonState();
        }

    }
}
