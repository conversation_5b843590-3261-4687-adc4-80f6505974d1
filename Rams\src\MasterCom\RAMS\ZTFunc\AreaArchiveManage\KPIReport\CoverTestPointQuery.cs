﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using DevExpress.XtraTreeList.Nodes;

namespace MasterCom.RAMS.ZTFunc.AreaArchiveManage.KPIReport
{
    public class CoverTestPointQuery : DIYSampleByRegion
    {
        protected List<AreaBase> areas;
        public CoverTestPointQuery(List<AreaBase> areas)
            : base(MainModel.GetInstance())
        {
            this.areas = areas;
        }

        protected override void AddDIYRegion_Intersect(Net.Package package)
        {//已用fileid过滤，可不用区域过滤
            if (areas.Count == 0)
                return;
            package.Content.AddParam((byte)MasterCom.RAMS.Net.OpOptionDef.AreaSelectIntersect);
            MasterCom.MTGis.DbRect rect = null;

            foreach (AreaBase item in areas)
            {
                if (rect == null)
                {
                    rect = item.Bounds.Clone();
                }
                else
                {
                    rect.MergeRects(item.Bounds);
                }
            }

            if (rect != null)
            {
                package.Content.AddParam(rect.x1);
                package.Content.AddParam(rect.y2);
                package.Content.AddParam(rect.x2);
                package.Content.AddParam(rect.y1);
            }
        }

        protected override void AddDIYRegion_Sample(Net.Package package)
        {
            package.Content.AddParam((byte)MasterCom.RAMS.Net.OpOptionDef.AreaSelectSample);
            MasterCom.MTGis.DbRect rect = null;

            foreach (AreaBase item in areas)
            {
                if (rect == null)
                {
                    rect = item.Bounds.Clone();
                }
                else
                {
                    rect.MergeRects(item.Bounds);
                }
            }

            if (rect != null)
            {
                package.Content.AddParam(rect.x1);
                package.Content.AddParam(rect.y2);
                package.Content.AddParam(rect.x2);
                package.Content.AddParam(rect.y1);
            }
        }

        protected override bool isValidTestPoint(TestPoint tp)
        {
            if (areas.Count == 0)
            {
                return false;
            }
            else
            {
                foreach (AreaBase item in areas)
                {
                    if (item.Bounds.IsPointInThisRect(tp.Longitude, tp.Latitude)
                        && item.MapOper.CheckPointInRegion(tp.Longitude, tp.Latitude))
                    {
                        return true;
                    }
                }
            }
            return false;
        }


    }
}
