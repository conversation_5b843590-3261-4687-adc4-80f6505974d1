﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.KPI_Statistics
{
    public class QueryKPIStatByRegionAsGrid : QueryKPIStatByRegion
    {
        public QueryKPIStatByRegionAsGrid()
            : base()
        {
            isStatAsGrid = true;
        }
        public override string Name
        {
            get { return "KPI统计(按区域内栅格)"; }
        }

        protected override void recieveAndHandleSpecificStatData(Net.Package package, List<Model.Interface.StatImgDefItem> curImgColumnDef, KPIStatDataBase singleStatData)
        {
            double lng = package.Content.GetParamDouble();
            double lat = package.Content.GetParamDouble();
            MasterCom.RAMS.Grid.GridUnitBase grid = new Grid.GridUnitBase(lng, lat);
            if (!condition.Geometorys.GeoOp.Contains(grid.CenterLng,grid.CenterLat))
            {
                return;
            }

            string gridKey = string.Format("{0}_{1}", lng, lat);

            fillStatData(package, curImgColumnDef, singleStatData);
            int fileID = (int)singleStatData[KPIStatDataBase.FileIDKey, KPIStatDataBase.NewGridFileIDKey, -1];
            MasterCom.RAMS.Model.FileInfo fi = DTDataHeaderManager.GetInstance().GetHeaderByFileID(fileID);
            KpiDataManager.AddStatData(string.Empty, gridKey, fi, singleStatData, false);

            List<ResvRegion> regs = getStatImgIntersectRegions(lng, lat);
            foreach (ResvRegion reg in regs)
            {
                KpiDataManager.AddStatData(reg.RegionName, reg, true, fi, singleStatData
                    , this.curReportStyle != null && this.curReportStyle.HasGridPerCell);
            }
            mergeRootNodeData(grid.CenterLng, grid.CenterLat, fi, singleStatData);
        }

        protected override void handleStatEvent(Model.Event evt)
        {
            if (!condition.Geometorys.GeoOp.Contains(evt.Longitude, evt.Latitude))
            {
                return;
            }
            double left = MasterCom.RAMS.Grid.GridHelper.RoundAsLeft(evt.Longitude);
            double top =  MasterCom.RAMS.Grid.GridHelper.RoundAsTop(evt.Latitude);
            string gridKey = string.Format("{0}_{1}", left, top);

            StatDataEvent eventData = new StatDataEvent(evt, this.needSeparateByServiceID(evt), needSeparateByFileName(evt));
            FileInfo fi = DTDataHeaderManager.GetInstance().GetHeaderByFileID(evt.FileID);
            KpiDataManager.AddStatData(string.Empty, gridKey, fi, eventData, false);

            List<ResvRegion> regs = getEventInRegions(evt);
            foreach (ResvRegion reg in regs)
            {
                KpiDataManager.AddStatData(reg.RegionName, reg, true, fi, eventData
                    , this.curReportStyle != null && this.curReportStyle.HasGridPerCell);
            }
            mergeRootNodeData(evt.Longitude, evt.Latitude, fi, eventData);
        }
    }
}
