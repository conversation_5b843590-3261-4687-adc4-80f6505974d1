﻿
using System;
using System.Collections.Generic;
using System.Data;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc.SiteCellInfo;
using MasterCom.Util;
using MasterCom.Util.UiEx;
using MasterCom.RAMS.ZTFunc.ImportCells;
using System.IO;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc
{
    public class ImportCell :QueryBase
    {
        public ImportCell()
            : base(MainModel.GetInstance())
        { }
        public override string Name
        {
            get { return "导入小区"; }
        }

        public override bool IsNeedSetQueryCondition
        {
            get
            {
                return false;
            }
        }

        protected override bool isValidCondition()
        {
            return true;
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 19000, 19046, this.Name);
        }
        private bool checkFormat(List<string> listCol)
        {
            try
            {
                if (listCol[0] != ColumnNames.CellName
                    ||listCol[1] != ColumnNames.Lon
                    ||listCol[2] != ColumnNames.Lat
                    ||listCol[3] != ColumnNames.Direction)
                {
                    return false;
                }
                return true;
            }
            catch
            {
                return false;
            }
        }

        private bool readFile(string fileName, out List<Cell_Import> listValue)
        {
            listValue = new List<Cell_Import>();
            ExcelNPOIReader reader = new ExcelNPOIReader(fileName);
            List<string> listCol = reader.GetColumns();
            if (!this.checkFormat(listCol))
            {
                MessageBox.Show("文件格式错误，文件的字段名和字段顺序请严格按照模板定制!");
                return false;
            }

            ExcelNPOITable table = reader.GetTable();
            foreach (object[] row in table.CellValues)
            {
                if (row.Length < 4) continue;
                if (row[0] == null
                    || row[1] == null
                    || row[2] == null
                    || row[3] == null) continue;

                Cell_Import cell = new Cell_Import();
                cell.CellName = row[0].ToString();
                double log;
                double lat;
                double dir;
                if (double.TryParse(row[1].ToString(), out log)
                    && double.TryParse(row[2].ToString(), out lat)
                    && double.TryParse(row[3].ToString(), out dir))
                {
                    cell.Lon = log;
                    cell.Lat = lat;
                    cell.Direction = dir;
                    listValue.Add(cell);
                }
            }
            return true;
        }
        private void addLayer(LayerBase layer)
        { 
            int i = 0;
            List<LayerBase> listTem = MainModel.GetInstance().MainForm.GetMapForm().TempLayerBaseVec;
            for (i = 0; i < listTem.Count; i++)
            {
                if (listTem[i].GetType().FullName == layer.GetType().FullName)
                {
                    listTem.RemoveAt(i);
                    i--;
                }
            }
            listTem.Add(layer);
        }
        protected override void query()
        {
            Func.MapForm mf = MainModel.MainForm.GetMapForm(); 
            try
            {
                ImportCellCondSetting cond = new ImportCellCondSetting();
                if (cond.ShowDialog() != DialogResult.OK) return;

                string fileName = cond.GetResult();
                List<Cell_Import> listValue = null;
                if (!this.readFile(fileName, out listValue)) return;

                ImportedCellLayer layer = new ImportedCellLayer();
                layer.FillCells(listValue);
                addLayer(layer);

                mf.GoToView(listValue[0].Lon, listValue[0].Lat);
                mf.SetRedrawBuffFlag();
                MessageBox.Show("导入小区成功!");
            }
            catch (Exception ex)
            {
                MessageBox.Show(string.Format("发生错误: \n{0}", ex.Message));
            }
        }

    }
}

