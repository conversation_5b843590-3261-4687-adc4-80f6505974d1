﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTStationAcceptance_XJ
{
    /// <summary>
    /// NR业务分析基类
    /// </summary>
    public abstract class NR700MStationAcceptBase : StationAcceptBase
    {
        protected NR700MCellServiceInfo curCellServiceInfo;
        protected NR700MBtsServiceInfo curBtsServiceInfo;

        protected override void analyzeFile(FileInfo fileInfo, DTFileDataManager fileManager, BtsInfoBase bts, CellInfoBase cell, StationAcceptConditionBase condition)
        {
            NR700MBtsInfo nrBtsInfo = bts as NR700MBtsInfo;
            NR700MCellInfo nrCellInfo = cell as NR700MCellInfo;
            NR700MStationAcceptCondition nrCondition = condition as NR700MStationAcceptCondition;

            if (nrCondition.NRServiceType == NRServiceName.SA)
            {
                curCellServiceInfo = nrCellInfo.SAInfo;
                curBtsServiceInfo = nrBtsInfo.SABtsInfo;
            }
            else
            {
                return;
            }

            bool needAna = judgeNeedAna(fileInfo, fileManager, nrBtsInfo, nrCellInfo, nrCondition);
            if (!needAna)
            {
                return;
            }

            analyzeNRFile(fileInfo, fileManager, nrBtsInfo, nrCellInfo, nrCondition);

            verifyFileResult(fileInfo, fileManager, nrBtsInfo, nrCellInfo, nrCondition);
        }

        protected virtual bool judgeNeedAna(FileInfo fileInfo, DTFileDataManager fileManager, NR700MBtsInfo bts, NR700MCellInfo cell, NR700MStationAcceptCondition condition)
        {
            return true;
        }

        protected virtual void analyzeNRFile(FileInfo fileInfo, DTFileDataManager fileManager, NR700MBtsInfo bts, NR700MCellInfo cell, NR700MStationAcceptCondition condition)
        {

        }

        protected virtual void verifyFileResult(FileInfo fileInfo, DTFileDataManager fileManager, NR700MBtsInfo bts, NR700MCellInfo cell, NR700MStationAcceptCondition condition)
        {

        }
    }

    /// <summary>
    /// 分析采样点业务基类
    /// </summary>
    public abstract class NR700MStationAcceptSample : NR700MStationAcceptBase
    {
        protected ICell getMainCell(TestPoint tp)
        {
            NRCell iCell = StationAcceptCellHelper_XJ.Instance.GetNRCell(tp);
            return iCell;
        }

        protected override void analyzeNRFile(FileInfo fileInfo, DTFileDataManager fileManager, NR700MBtsInfo bts, NR700MCellInfo cell, NR700MStationAcceptCondition condition)
        {
            foreach (TestPoint tp in fileManager.TestPoints)
            {
                ICell iCell = getMainCell(tp);
                if (iCell != null && cell.Cell.Name == iCell.Name)
                {
                    addData(tp, cell, condition);
                }
            }
        }

        /// <summary>
        /// 添加每个采样点的指标数据
        /// </summary>
        protected abstract void addData(TestPoint tp, NR700MCellInfo cell, NR700MStationAcceptCondition condition);
    }

    /// <summary>
    /// 分析速率业务基类
    /// </summary>
    public abstract class NR700MStationAcceptThroughput : NR700MStationAcceptSample
    {
        protected override void verifyFileResult(FileInfo fileInfo, DTFileDataManager fileManager, NR700MBtsInfo bts, NR700MCellInfo cell, NR700MStationAcceptCondition condition)
        {
            ThroughputStandard standard = getThroughputStandard(fileInfo.Name, condition);
            if (standard == null)
            {
                return;
            }

            verifyThroughput(cell, condition, standard);
        }

        /// <summary>
        /// 根据文件名获取吞吐率判断标准
        /// </summary>
        protected ThroughputStandard getThroughputStandard(string fileName, NR700MStationAcceptCondition condition)
        {
            string token = NRStationAcceptFileNameHelper.GetThroughputFileToken(fileName, 7);
            if (string.IsNullOrEmpty(token))
            {
                log.Error($"{fileName}文件没有找到对应的速率标准,请检查文件命名");
                return null;
            }

            ThroughputStandard standard = condition.Standard.ThroughputStandardList.Find(x => x.Token == token);
            return standard;
        }

        /// <summary>
        /// 判断速率是否有效
        /// </summary>
        protected abstract void verifyThroughput(NR700MCellInfo cell, NR700MStationAcceptCondition condition, ThroughputStandard standard);

        protected void dealValidGoodThroughput(FtpPointInfo info, ThroughputStandard.DataInfo standard)
        {
            if ((info.Rsrp.Divisor > 0 && info.Rsrp.Data >= -75) || info.Sinr.Data >= 15)
            {
                info.Rsrp.IsValid = true;
                info.Sinr.IsValid = true;
            }
            dealValidThroughput(info, standard, ThroughputStandard.PointType.Good);
        }

        protected void dealValidBadThroughput(FtpPointInfo info, ThroughputStandard.DataInfo standard)
        {
            if (info.Rsrp.Data < -90 || info.Sinr.Data < 0)
            {
                info.Rsrp.IsValid = true;
                info.Sinr.IsValid = true;
            }
            dealValidThroughput(info, standard, ThroughputStandard.PointType.Bad);
        }

        private void dealValidThroughput(FtpPointInfo info, ThroughputStandard.DataInfo standard, ThroughputStandard.PointType pointType)
        {
            bool isValid = standard.JudgeThroughput(pointType, info.Throughput.Data);
            if (isValid)
            {
                info.Throughput.IsValid = true;
            }
            info.Judge();
        }

        #region 另一种速率方案,暂时不用了,先保留代码,避免需求反复横挑2021.08.30
        protected int getBandByFileName(string fileName)
        {
            string[] strs = fileName.Split('_');
            if (strs.Length == 8)
            {
                string band = strs[strs.Length - 2];
                if (int.TryParse(band, out var iBand))
                {
                    return iBand;
                }
            }
            return -1;
        }

        protected void dealValidThroughput(FtpPointInfo info, Threshold threshold)
        {
            if (threshold.RsrpThreshold.Judge(info.Rsrp.Data, info.Rsrp.Divisor))
            {
                info.Rsrp.IsValid = true;
            }
            if (threshold.SinrThreshold.Judge(info.Sinr.Data))
            {
                info.Sinr.IsValid = true;
            }
            if (threshold.ThroughputThreshold.Judge(info.Throughput.Data))
            {
                info.Throughput.IsValid = true;
            }
        }

        protected class Threshold
        {
            public ThresholdData RsrpThreshold { get; set; }
            public ThresholdData SinrThreshold { get; set; }
            public ThresholdData ThroughputThreshold { get; set; }

            public Threshold(PointType type, bool isUpload, int band)
            {
                int threshold;
                double realThreshold;
                switch (type)
                {
                    case PointType.Good:
                        RsrpThreshold = new ThresholdData(Operator.GreatThanOrEqualTo, -75);
                        SinrThreshold = new ThresholdData(Operator.GreatThanOrEqualTo, 15);
                        if (isUpload) { threshold = 90; }
                        else { threshold = 200; }
                        realThreshold = Math.Round(threshold * (band / 30d), 2);
                        ThroughputThreshold = new ThresholdData(Operator.GreatThan, realThreshold);
                        break;
                    case PointType.Bad:
                        RsrpThreshold = new ThresholdData(Operator.LessThan, -90);
                        SinrThreshold = new ThresholdData(Operator.LessThan, 0);
                        if (isUpload) { threshold = 5; }
                        else { threshold = 80; }
                        realThreshold = Math.Round(threshold * (band / 30d), 2);
                        ThroughputThreshold = new ThresholdData(Operator.GreatThan, realThreshold);
                        break;
                }
            }
        }

        protected class ThresholdData
        {
            public double Threshold { get; set; }
            public Operator Operator { get; set; }

            public ThresholdData(Operator curOperator, double threshold)
            {
                Operator = curOperator;
                Threshold = threshold;
            }

            public bool Judge(double data)
            {
                bool isSatisfied = false;
                switch (Operator)
                {
                    case Operator.GreatThan:
                        isSatisfied = data > Threshold;
                        break;
                    case Operator.GreatThanOrEqualTo:
                        isSatisfied = data >= Threshold;
                        break;
                    case Operator.EqualTo:
                        isSatisfied = data == Threshold;
                        break;
                    case Operator.LessThanOrEqualTo:
                        isSatisfied = data <= Threshold;
                        break;
                    case Operator.LessThan:
                        isSatisfied = data < Threshold;
                        break;
                }
                return isSatisfied;
            }

            public bool Judge(double data, int count)
            {
                if (count > 0)
                {
                    return Judge(data);
                }
                return false;
            }
        }

        protected enum PointType
        { 
            Good,
            Middle,
            Bad
        }

        public enum Operator
        {
            GreatThan,
            GreatThanOrEqualTo,
            EqualTo,
            LessThanOrEqualTo,
            LessThan
        }
        #endregion
    }

    /// <summary>
    /// 分析事件业务基类
    /// </summary>
    public abstract class NR700MStationAcceptEvent : NR700MStationAcceptBase
    {
        protected List<int> evtSuccList = new List<int>();
        protected List<int> evtRequList = new List<int>();
        protected List<int> evtFailList = new List<int>();

        protected override void analyzeNRFile(FileInfo fileInfo, DTFileDataManager fileManager, NR700MBtsInfo bts, NR700MCellInfo cell, NR700MStationAcceptCondition condition)
        {
            SuccessRateKpiInfo eventInfo = initEventKpiInfo(fileInfo, cell, condition);
            if (eventInfo == null)
            {
                return;
            }
            dealEvtDataList(eventInfo, fileManager.Events);
        }

        protected virtual void dealEvtDataList(SuccessRateKpiInfo eventInfo, List<Event> evtList)
        {
            foreach (Event evt in evtList)
            {
                if (evtRequList.Contains(evt.ID))
                {
                    eventInfo.RequestCnt++;
                }
                else if (evtSuccList.Contains(evt.ID))
                {
                    eventInfo.SucceedCnt++;
                }
                else if (evtFailList.Contains(evt.ID))
                {
                    eventInfo.FailedCnt++;
                }
            }
        }

        protected abstract SuccessRateKpiInfo initEventKpiInfo(FileInfo fileInfo, NR700MCellInfo cell
            , NR700MStationAcceptCondition condition);
    }

    public abstract class NR700MStationAcceptCoverPic : StationAcceptCoverPic
    {
        protected NR700MStationAcceptCoverPic(string picPath)
            : base(picPath)
        {
        }

        protected override void analyzeFile(FileInfo fileInfo, DTFileDataManager fileManager
            , BtsInfoBase bts, CellInfoBase cell, StationAcceptConditionBase condition)
        {
            NR700MBtsInfo nrBtsInfo = bts as NR700MBtsInfo;
            NR700MCellInfo nrCellInfo = cell as NR700MCellInfo;
            NR700MStationAcceptCondition nrCondition = condition as NR700MStationAcceptCondition;
            NR700MCellServiceInfo cellTypeInfo;
            if (nrCondition.NRServiceType == NRServiceName.SA)
            {
                cellTypeInfo = nrCellInfo.SAInfo;
            }
            else
            {
                return;
            }

            analyzeFile(fileInfo, fileManager, nrBtsInfo, cellTypeInfo, nrCondition);
        }

        protected virtual void analyzeFile(FileInfo fileInfo, DTFileDataManager fileManager
            , NR700MBtsInfo bts, NR700MCellServiceInfo cellTypeInfo
            , NR700MStationAcceptCondition condition)
        {

        }

        protected override string fireMapAndTakePic(RAMS.Func.MapForm mf, string paramName, string filePath)
        {
            //要让小区显示在采样点之上
            var nrLayer = mf.GetNRCellLayer();
            mf.MakeSureCustomLayerVisible(nrLayer, true);

            return base.fireMapAndTakePic(mf, paramName, filePath);
        }
    }
}
