﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class ExportTDMOSAnaByRegion : TDMOSAnaByRegion
    {
        public ExportTDMOSAnaByRegion(MainModel model)
            : base(model)
        {
        }

        public override string Name
        {
            get { return "TD-MOS采样周期"; }
        }

        public override string IconName
        {
            get { return "Images/replay.gif"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 13000, 13041, this.Name);
        }

        protected override void doWithMOSResult(List<TDMOSParam> mosParamList)
        {
            List<List<object>> excelRow = new List<List<object>>();
            excelRow.Add(TDMOSParam.GetTitle());
            foreach (TDMOSParam param in mosParamList)
            {
                excelRow.Add(param.GetContent());
            }
            ExcelNPOIManager.ExportToExcel(excelRow);
        }
    }
}
