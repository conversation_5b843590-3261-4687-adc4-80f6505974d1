﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using DevExpress.XtraTreeList.Nodes;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using DevExpress.XtraCharts;
using DevExpress.XtraTab;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.CQT
{
    public partial class CQTKPIReportForm : MinCloseForm
    {
        public CQTKPIReportForm(MainModel mm)
            : base(mm)
        {
            InitializeComponent();
            helper = new TreeListSearchHelper(treeList);
        }
        CQTPointMapLayer layer = null;
        TimePeriod curStatPeriod = null;
        CQTKPIDataManager dataManager = null;
        CQTKPIReport curReport = null;
        public void FillData(TimePeriod statPeriod, CQTKPIDataManager dataMng, CQTKPIReport report)
        {
            curStatPeriod = statPeriod;
            dataManager = dataMng;
            curReport = report;
            refreshReport();
        }

        private void refreshReport()
        {
            dataManager.MakeReportData(treeList, curStatPeriod, curReport);
            fillCombBoxReport(curReport);
            if (treeList.VisibleColumns.Count > 0)
            {
                helper.FieldName = treeList.Columns[0].FieldName;
            }
            refreshLayer();
            cbeCurColumn_SelectedValueChanged(this, EventArgs.Empty);
        }

        private void refreshLayer()
        {
            MapForm mf = MainModel.MainForm.GetMapForm();
            if (mf == null)
            {
                return;
            }
            mModel.MainForm.RefreshLegend();
            MasterCom.MTGis.CustomDrawLayer cLayer = mf.GetCustomLayer(typeof(CQTPointMapLayer));
            if (cLayer == null)
            {
                layer = new CQTPointMapLayer(mf.GetMapOperation(), "CQT");
                mf.AddTempCustomLayer(layer);
            }
            else
            {
                layer = cLayer as CQTPointMapLayer;
            }
            layer.CQTPoints2Show = CQTPointManager.GetInstance().CQTPoints;
            layer.IsVisible = true;
            layer.Invalidate();
        }

        private void fillCombBoxReport(CQTKPIReport report)
        {
            cbeReport.Properties.Items.Clear();
            foreach (CQTKPIReport rpt in CQTKPIReportCfgManager.GetInstance().Reports)
            {
                cbeReport.Properties.Items.Add(rpt);
            }
            cbeReport.SelectedItem = report;
        }

        private void cbeReport_SelectedValueChanged(object sender, EventArgs e)
        {
            cbeCurColumn.Properties.Items.Clear();
            if (cbeReport.SelectedItem == null)
            {
                return;
            }
            curReport = cbeReport.SelectedItem as CQTKPIReport;
            if (curReport == null)
            {
                return;
            }
            treeList.NodeCellStyle -= this.treeListCQTPointNavi_NodeCellStyle;
            dataManager.MakeReportData(treeList, curStatPeriod, curReport);
            foreach (CQTKPIReportColumn col in curReport.Columns)
            {
                cbeCurColumn.Properties.Items.Add(col);
            }
            foreach (CQTKPISummaryColumn sCol in curReport.SummaryColumns)
            {
                cbeCurColumn.Properties.Items.Add(sCol);
            }
            if (cbeCurColumn.Properties.Items.Count > 0)
            {
                cbeCurColumn.SelectedIndex = 0;
            }
            treeList.NodeCellStyle += this.treeListCQTPointNavi_NodeCellStyle;
        }

        private void cbeCurColumn_SelectedValueChanged(object sender, EventArgs e)
        {
            MasterCom.RAMS.CQT.CQTKPIDataManager.ColumnShowEventArgs eA = new MasterCom.RAMS.CQT.CQTKPIDataManager.ColumnShowEventArgs();
            eA.column = cbeCurColumn.SelectedItem;
            dataManager.FireGisShowColumnChange(this, eA);
            treeList.Invalidate();
            if (treeList.FocusedNode != null && treeList.FocusedNode.Tag is CQTMainPointKPI)
            {
                CQTMainPointKPI mainPoint = (CQTMainPointKPI)treeList.FocusedNode.Tag;
                fillTabCharts(mainPoint);
                fillFloorLengend(mainPoint);
                selTabPageByColumn(treeList.FocusedColumn.Tag);
            }
            refreshLayer();
        }


        private void treeListCQTPointNavi_FocusedNodeChanged(object sender, DevExpress.XtraTreeList.FocusedNodeChangedEventArgs e)
        {
            if (e.Node==null)
            {
                return;
            }
            if (e.Node.Tag is CQTMainPointKPI)
            {
                CQTMainPointKPI mainPoint = e.Node.Tag as CQTMainPointKPI;
                fillTabCharts(mainPoint);
                fillFloorLengend(mainPoint);
                selTabPageByColumn(treeList.FocusedColumn.Tag);
            }
        }

        private void fillTabCharts(CQTMainPointKPI mainPoint)
        {
            grpPointInfo.Text = mainPoint.Name;
            tabControlChart.TabPages.Clear();
            foreach (CQTKPIReportColumn col in curReport.Columns)
            {
                XtraTabPage page = tabControlChart.TabPages.Add(col.Name);
                page.Tag = col;
                ChartControl chart = createChart(mainPoint, col);
                chart.Dock = DockStyle.Fill;
                page.Controls.Add(chart);
            }
            foreach (CQTKPISummaryColumn sCol in curReport.SummaryColumns)
            {
                XtraTabPage page = tabControlChart.TabPages.Add(sCol.Name);
                page.Tag = sCol;
                ChartControl chart = createChart(mainPoint, sCol);
                chart.Dock = DockStyle.Fill;
                page.Controls.Add(chart);
            }
        }

        private ChartControl createChart(CQTMainPointKPI mainPoint, object col)
        {
            ChartControl chart = new ChartControl();
            chart.Legend.Visible = false;
            chart.Titles.Clear();
            chart.Series.Clear();
            ChartTitle title = new ChartTitle();
            Series series = null;
            if (col is CQTKPIReportColumn)
            {
                title.Text = (col as CQTKPIReportColumn).Name;
                series = new Series(title.Text, ViewType.Bar);
                foreach (CQTSubPointKPI subPoint in mainPoint.SubPointNameDataDic.Values)
                {
                    double value = subPoint.ColumnKPIResultDic[(col as CQTKPIReportColumn)].KPIValue;
                    if (!double.IsNaN(value))
                    {
                        series.Points.Add(new SeriesPoint(subPoint.Name, value));
                    }
                }
            }
            else if (col is CQTKPISummaryColumn)
            {
                title.Text = (col as CQTKPISummaryColumn).Name;
                series = new Series(title.Text, ViewType.Bar);
                foreach (CQTSubPointKPI subPoint in mainPoint.SubPointNameDataDic.Values)
                {
                    double value = subPoint.SummaryResultDic[col as CQTKPISummaryColumn].Score;
                    if (!double.IsNaN(value))
                    {
                        series.Points.Add(new SeriesPoint(subPoint.Name, value));
                    }
                }
            }
            chart.Titles.Add(title);
            chart.Series.Add(series);
            XYDiagram diagram = chart.Diagram as XYDiagram;
            diagram.EnableAxisXScrolling = true;
            diagram.EnableAxisXZooming = true;
            diagram.AxisX.Label.Angle = mainPoint.SubPointNameDataDic.Count > 10 ? 90 : 0;
            if (series != null)
            {
                series.View.Color = Color.DodgerBlue;
                ((SideBySideBarSeriesView)series.View).FillStyle.FillMode = FillMode.Solid;
            }
            return chart;
        }

        TreeListSearchHelper helper;
        private void PerformSearch(bool forward)
        {
            helper.PerformSearch(forward);
            treeList.FocusedNode = helper.CurrentNode;
        }

        private void buttonEditSearch_ButtonClick(object sender, DevExpress.XtraEditors.Controls.ButtonPressedEventArgs e)
        {
            if (buttonEditSearch.Text.Trim().Length > 0)
            {
                PerformSearch(e.Button.Index == 1);
            }
        }

        private void buttonEditSearch_EditValueChanged(object sender, EventArgs e)
        {
            helper.Text = buttonEditSearch.Text;
        }

        private void treeListCQTPointNavi_NodeCellStyle(object sender, DevExpress.XtraTreeList.GetCustomNodeCellStyleEventArgs e)
        {
            if (e.Node == null || e.Node.Tag == null)
            {
                return;
            }
            try
            {
                if (e.Column.Tag is CQTKPIReportColumn)
                {
                    if (e.Node.Tag is CQTMainPointKPI)
                    {
                        e.Appearance.BackColor = (e.Node.Tag as CQTMainPointKPI).ColumnKPIResultDic[e.Column.Tag as CQTKPIReportColumn].Color;
                    }
                    else if (e.Node.Tag is CQTSubPointKPI)
                    {
                        e.Appearance.BackColor = (e.Node.Tag as CQTSubPointKPI).ColumnKPIResultDic[e.Column.Tag as CQTKPIReportColumn].Color;
                    }
                    else if (e.Node.Tag is CQTFileKPIData)
                    {
                        e.Appearance.BackColor = (e.Node.Tag as CQTFileKPIData).ColumnKPIResultDic[e.Column.Tag as CQTKPIReportColumn].Color;
                    }
                    e.Appearance.BorderColor = Color.Red;
                }
                else if (e.Column.Tag is CQTKPISummaryColumn)
                {
                    if (e.Node.Tag is CQTMainPointKPI)
                    {
                        e.Appearance.BackColor = (e.Node.Tag as CQTMainPointKPI).SummaryResultDic[e.Column.Tag as CQTKPISummaryColumn].Color;
                    }
                    else if (e.Node.Tag is CQTSubPointKPI)
                    {
                        e.Appearance.BackColor = (e.Node.Tag as CQTSubPointKPI).SummaryResultDic[e.Column.Tag as CQTKPISummaryColumn].Color;
                    }
                    else if (e.Node.Tag is CQTFileKPIData)
                    {
                        e.Appearance.BackColor = (e.Node.Tag as CQTFileKPIData).SummaryResultDic[e.Column.Tag as CQTKPISummaryColumn].Color;
                    }
                    e.Appearance.BorderColor = Color.Red;
                }
            }
            catch
            {
                //continue
            }
        }

        private void fillFloorLengend(CQTMainPointKPI mainPoint)
        {
            splitContainerControl2.Panel2.Controls.Clear();
            splitContainerControl2.Panel2.AutoScroll = true;

            string serialInfoName = "GSM RxLevSub";
            MapSerialInfo serial = DTLayerSerialManager.Instance.GetSerialByName(serialInfoName);

            List<DTFileDataManager> files = new List<DTFileDataManager>();
            foreach (FileInfo file in mainPoint.GetFileInfo(this.curReport.CarreerID))
            {
                foreach (DTFileDataManager fileMng in MainModel.DTDataManager.FileDataManagers)
                {
                    if (file.ID==fileMng.FileID)
                    {
                        files.Add(fileMng);
                        break;
                    }
                }
            }
            floorImgPanel.ShowImages(files, serial);
            floorImgPanel.Parent = splitContainerControl2.Panel2;
        }

        private void treeList_FocusedColumnChanged(object sender, DevExpress.XtraTreeList.FocusedColumnChangedEventArgs e)
        {
            if (e.Column==null)
            {
                return;
            }
            selTabPageByColumn(e.Column.Tag);
        }

        private void selTabPageByColumn(object col)
        {
            if (col==null)
            {
                return;
            }
            foreach (XtraTabPage page in tabControlChart.TabPages)
            {
                if (page.Tag == col)
                {
                    tabControlChart.SelectedTabPage = page;
                    return;
                }
            }
        }

        private void buttonEditSearch_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                e.Handled = true;
                PerformSearch(true);
            }
        }

        private void miReplayFile_Click(object sender, EventArgs e)
        {
            TreeListNode fNode = treeList.FocusedNode;
            if (fNode != null)
            {
                if (fNode.Tag is CQTMainPointKPI)
                {
                    CQTMainPointKPI mainPoint = fNode.Tag as CQTMainPointKPI;
                    DIYReplayFileQuery qry = new DIYReplayFileQuery(MainModel);
                    QueryCondition codition = new QueryCondition();
                    codition.DistrictID = MainModel.DistrictID;
                    codition.FileInfos = mainPoint.GetFileInfo((cbeReport.SelectedItem as CQTKPIReport).CarreerID);
                    qry.SetQueryCondition(codition);
                    qry.Query();
                }
                else if (fNode.Tag is CQTSubPointKPI)
                {
                    CQTSubPointKPI subPoint = fNode.Tag as CQTSubPointKPI;
                    DIYReplayFileQuery qry = new DIYReplayFileQuery(MainModel);
                    QueryCondition codition = new QueryCondition();
                    codition.DistrictID = MainModel.DistrictID;
                    codition.FileInfos = subPoint.GetFileInfo((cbeReport.SelectedItem as CQTKPIReport).CarreerID);
                    qry.SetQueryCondition(codition);
                    qry.Query();
                }
                else if (fNode.Tag is CQTFileKPIData)
                {
                    CQTFileKPIData fileData = fNode.Tag as CQTFileKPIData;
                    DIYReplayFileQuery qry = new DIYReplayFileQuery(MainModel);
                    QueryCondition codition = new QueryCondition();
                    codition.DistrictID = MainModel.DistrictID;
                    codition.FileInfos.Add(fileData.DataHeader);
                    qry.SetQueryCondition(codition);
                    qry.Query();
                }
            }
        }

        private void miExpandAll_Click(object sender, EventArgs e)
        {
            treeList.ExpandAll();
        }

        private void miCollapseAll_Click(object sender, EventArgs e)
        {
            treeList.CollapseAll();
        }

        private void miExport2Xls_Click(object sender, EventArgs e)
        {
            SaveFileDialog dlg = new SaveFileDialog();
            dlg.RestoreDirectory = true;
            dlg.Filter = "Excel2007文件(*.xlsx)|*.xlsx";
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                this.Cursor = System.Windows.Forms.Cursors.WaitCursor;
                treeList.ExpandAll();
                treeList.ExportToXlsx(dlg.FileName);
                this.Cursor = System.Windows.Forms.Cursors.Default;
            }
        }

        private void btnEditReport_Click(object sender, EventArgs e)
        {
            CQTKPIReportEditForm editFrm = new CQTKPIReportEditForm();
            editFrm.FillData(CQTKPIReportCfgManager.GetInstance(), curReport);
            editFrm.ShowDialog();
            if (!CQTKPIReportCfgManager.GetInstance().Reports.Contains(curReport))
            {
                curReport=CQTKPIReportCfgManager.GetInstance().Reports[0];
            }
            treeList.NodeCellStyle -= treeListCQTPointNavi_NodeCellStyle;
            refreshReport();
            treeList.NodeCellStyle += treeListCQTPointNavi_NodeCellStyle;
        }

        private void treeList_DoubleClick(object sender, EventArgs e)
        {
            TreeListNode fNode = treeList.FocusedNode;
            MainModel.SelCQTPoint = null;
            if (fNode != null && fNode.Tag is CQTMainPointKPI)
            {
                CQTMainPointKPI mainPoint = fNode.Tag as CQTMainPointKPI;
                MainModel.SelCQTPoint = mainPoint.CQTPoint;
                MainModel.MainForm.GetMapForm().GoToView(mainPoint.CQTPoint.Longitude, mainPoint.CQTPoint.Latitude, 5000);
            }
        }
    }
}
