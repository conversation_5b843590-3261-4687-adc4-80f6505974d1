﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class LowSpeedInfoForm_TD
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.gridControl = new DevExpress.XtraGrid.GridControl();
            this.gridView = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumnFileName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnTime = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnNetType = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnPointCount = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnTimeSlotUsedAvgString = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnLongitude = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnLatitude = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnDuration = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnDistance = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnHighSpeed = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnLowSpeed = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnMeanSpeed = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnLACCI = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnFreq = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnRoadDesc = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnPccpch_Rscp_Max = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnPccpch_Rscp_Min = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnPccpch_Rscp_Mean = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnPccpch_C2I_Max = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnPccpch_C2I_Min = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnPccpch_C2I_Mean = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnDpch_Rscp_Max = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnDpch_Rscp_Min = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnDpch_Rscp_Mean = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnDpch_C2I_Max = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnDpch_C2I_Min = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnDpch_C2I_Mean = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnBler_Max = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnBler_Min = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnBler_Mean = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnPdsch_rscp_Max = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnPdsch_rscp_Min = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnPdsch_rscp_Mean = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnPdsch_c2i_Max = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnPdsch_c2i_Min = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnPdsch_c2i_Mean = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnScch_rscp_Max = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnScch_rscp_Min = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnScch_rscp_Mean = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnScch_c2i_Max = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnScch_c2i_Min = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnScch_c2i_Mean = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnScch_ScheRate_Max = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnScch_ScheRate_Min = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnScch_ScheRate_Mean = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnBruNumDl_Max = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnBruNumDl_Min = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnBruNumDl_Mean = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnHandOver_count = new DevExpress.XtraGrid.Columns.GridColumn();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView)).BeginInit();
            this.SuspendLayout();
            // 
            // gridControl
            // 
            this.gridControl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControl.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gridControl.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gridControl.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gridControl.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gridControl.EmbeddedNavigator.Buttons.Remove.Visible = false;
            this.gridControl.Location = new System.Drawing.Point(0, 0);
            this.gridControl.MainView = this.gridView;
            this.gridControl.Name = "gridControl";
            this.gridControl.Size = new System.Drawing.Size(895, 407);
            this.gridControl.TabIndex = 2;
            this.gridControl.UseEmbeddedNavigator = true;
            this.gridControl.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView});
            this.gridControl.DoubleClick += new System.EventHandler(this.gridControl_DoubleClick);
            // 
            // gridView
            // 
            this.gridView.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumnFileName,
            this.gridColumnTime,
            this.gridColumnNetType,
            this.gridColumnPointCount,
            this.gridColumnTimeSlotUsedAvgString,
            this.gridColumnLongitude,
            this.gridColumnLatitude,
            this.gridColumnDuration,
            this.gridColumnDistance,
            this.gridColumnHighSpeed,
            this.gridColumnLowSpeed,
            this.gridColumnMeanSpeed,
            this.gridColumnLACCI,
            this.gridColumnFreq,
            this.gridColumnRoadDesc,
            this.gridColumn1,
            this.gridColumn2,
            this.gridColumn3,
            this.gridColumnPccpch_Rscp_Max,
            this.gridColumnPccpch_Rscp_Min,
            this.gridColumnPccpch_Rscp_Mean,
            this.gridColumnPccpch_C2I_Max,
            this.gridColumnPccpch_C2I_Min,
            this.gridColumnPccpch_C2I_Mean,
            this.gridColumnDpch_Rscp_Max,
            this.gridColumnDpch_Rscp_Min,
            this.gridColumnDpch_Rscp_Mean,
            this.gridColumnDpch_C2I_Max,
            this.gridColumnDpch_C2I_Min,
            this.gridColumnDpch_C2I_Mean,
            this.gridColumnBler_Max,
            this.gridColumnBler_Min,
            this.gridColumnBler_Mean,
            this.gridColumnPdsch_rscp_Max,
            this.gridColumnPdsch_rscp_Min,
            this.gridColumnPdsch_rscp_Mean,
            this.gridColumnPdsch_c2i_Max,
            this.gridColumnPdsch_c2i_Min,
            this.gridColumnPdsch_c2i_Mean,
            this.gridColumnScch_rscp_Max,
            this.gridColumnScch_rscp_Min,
            this.gridColumnScch_rscp_Mean,
            this.gridColumnScch_c2i_Max,
            this.gridColumnScch_c2i_Min,
            this.gridColumnScch_c2i_Mean,
            this.gridColumnScch_ScheRate_Max,
            this.gridColumnScch_ScheRate_Min,
            this.gridColumnScch_ScheRate_Mean,
            this.gridColumnBruNumDl_Max,
            this.gridColumnBruNumDl_Min,
            this.gridColumnBruNumDl_Mean,
            this.gridColumnHandOver_count});
            this.gridView.GridControl = this.gridControl;
            this.gridView.Name = "gridView";
            this.gridView.OptionsBehavior.Editable = false;
            this.gridView.OptionsSelection.MultiSelect = true;
            this.gridView.OptionsView.ColumnAutoWidth = false;
            this.gridView.OptionsView.ShowDetailButtons = false;
            this.gridView.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumnFileName
            // 
            this.gridColumnFileName.Caption = "文件名";
            this.gridColumnFileName.FieldName = "FileName";
            this.gridColumnFileName.Name = "gridColumnFileName";
            this.gridColumnFileName.Visible = true;
            this.gridColumnFileName.VisibleIndex = 0;
            this.gridColumnFileName.Width = 151;
            // 
            // gridColumnTime
            // 
            this.gridColumnTime.Caption = "时间";
            this.gridColumnTime.FieldName = "DateTimeString";
            this.gridColumnTime.Name = "gridColumnTime";
            this.gridColumnTime.Visible = true;
            this.gridColumnTime.VisibleIndex = 1;
            this.gridColumnTime.Width = 146;
            // 
            // gridColumnNetType
            // 
            this.gridColumnNetType.Caption = "网络类型";
            this.gridColumnNetType.FieldName = "NetType";
            this.gridColumnNetType.Name = "gridColumnNetType";
            this.gridColumnNetType.Visible = true;
            this.gridColumnNetType.VisibleIndex = 2;
            // 
            // gridColumnPointCount
            // 
            this.gridColumnPointCount.Caption = "采样点数";
            this.gridColumnPointCount.FieldName = "PointCount";
            this.gridColumnPointCount.Name = "gridColumnPointCount";
            this.gridColumnPointCount.Visible = true;
            this.gridColumnPointCount.VisibleIndex = 3;
            // 
            // gridColumnTimeSlotUsedAvgString
            // 
            this.gridColumnTimeSlotUsedAvgString.Caption = "HS占用的时隙平均值";
            this.gridColumnTimeSlotUsedAvgString.FieldName = "TimeSlotUsedAvgString";
            this.gridColumnTimeSlotUsedAvgString.Name = "gridColumnTimeSlotUsedAvgString";
            this.gridColumnTimeSlotUsedAvgString.Visible = true;
            this.gridColumnTimeSlotUsedAvgString.VisibleIndex = 4;
            // 
            // gridColumnLongitude
            // 
            this.gridColumnLongitude.Caption = "经度";
            this.gridColumnLongitude.FieldName = "Longitude";
            this.gridColumnLongitude.Name = "gridColumnLongitude";
            this.gridColumnLongitude.Visible = true;
            this.gridColumnLongitude.VisibleIndex = 5;
            // 
            // gridColumnLatitude
            // 
            this.gridColumnLatitude.Caption = "纬度";
            this.gridColumnLatitude.FieldName = "Latitude";
            this.gridColumnLatitude.Name = "gridColumnLatitude";
            this.gridColumnLatitude.Visible = true;
            this.gridColumnLatitude.VisibleIndex = 6;
            this.gridColumnLatitude.Width = 66;
            // 
            // gridColumnDuration
            // 
            this.gridColumnDuration.Caption = "时长(秒)";
            this.gridColumnDuration.FieldName = "Duration";
            this.gridColumnDuration.Name = "gridColumnDuration";
            this.gridColumnDuration.Visible = true;
            this.gridColumnDuration.VisibleIndex = 7;
            this.gridColumnDuration.Width = 66;
            // 
            // gridColumnDistance
            // 
            this.gridColumnDistance.Caption = "里程(米)";
            this.gridColumnDistance.FieldName = "Distance";
            this.gridColumnDistance.Name = "gridColumnDistance";
            this.gridColumnDistance.Visible = true;
            this.gridColumnDistance.VisibleIndex = 8;
            this.gridColumnDistance.Width = 73;
            // 
            // gridColumnHighSpeed
            // 
            this.gridColumnHighSpeed.Caption = "最高速率(Kbps)";
            this.gridColumnHighSpeed.FieldName = "HighSpeed";
            this.gridColumnHighSpeed.Name = "gridColumnHighSpeed";
            this.gridColumnHighSpeed.Visible = true;
            this.gridColumnHighSpeed.VisibleIndex = 9;
            this.gridColumnHighSpeed.Width = 98;
            // 
            // gridColumnLowSpeed
            // 
            this.gridColumnLowSpeed.Caption = "最低速率(Kbps)";
            this.gridColumnLowSpeed.FieldName = "LowSpeed";
            this.gridColumnLowSpeed.Name = "gridColumnLowSpeed";
            this.gridColumnLowSpeed.Visible = true;
            this.gridColumnLowSpeed.VisibleIndex = 10;
            this.gridColumnLowSpeed.Width = 99;
            // 
            // gridColumnMeanSpeed
            // 
            this.gridColumnMeanSpeed.Caption = "平均速率(Kbps)";
            this.gridColumnMeanSpeed.FieldName = "MeanSpeed";
            this.gridColumnMeanSpeed.Name = "gridColumnMeanSpeed";
            this.gridColumnMeanSpeed.Visible = true;
            this.gridColumnMeanSpeed.VisibleIndex = 11;
            this.gridColumnMeanSpeed.Width = 104;
            // 
            // gridColumnLACCI
            // 
            this.gridColumnLACCI.Caption = "LAC-CI";
            this.gridColumnLACCI.FieldName = "LACCIs";
            this.gridColumnLACCI.Name = "gridColumnLACCI";
            this.gridColumnLACCI.Visible = true;
            this.gridColumnLACCI.VisibleIndex = 12;
            this.gridColumnLACCI.Width = 177;
            // 
            // gridColumnFreq
            // 
            this.gridColumnFreq.Caption = "频点";
            this.gridColumnFreq.FieldName = "BCCHs";
            this.gridColumnFreq.Name = "gridColumnFreq";
            this.gridColumnFreq.Visible = true;
            this.gridColumnFreq.VisibleIndex = 13;
            this.gridColumnFreq.Width = 115;
            // 
            // gridColumnRoadDesc
            // 
            this.gridColumnRoadDesc.Caption = "道路";
            this.gridColumnRoadDesc.FieldName = "RoadDesc";
            this.gridColumnRoadDesc.Name = "gridColumnRoadDesc";
            this.gridColumnRoadDesc.Visible = true;
            this.gridColumnRoadDesc.VisibleIndex = 14;
            this.gridColumnRoadDesc.Width = 115;
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "片区";
            this.gridColumn1.FieldName = "AreaName";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.Visible = true;
            this.gridColumn1.VisibleIndex = 15;
            // 
            // gridColumn2
            // 
            this.gridColumn2.Caption = "网格";
            this.gridColumn2.FieldName = "GridName";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.Visible = true;
            this.gridColumn2.VisibleIndex = 16;
            // 
            // gridColumn3
            // 
            this.gridColumn3.Caption = "代维";
            this.gridColumn3.FieldName = "AreaAgentName";
            this.gridColumn3.Name = "gridColumn3";
            this.gridColumn3.Visible = true;
            this.gridColumn3.VisibleIndex = 17;
            // 
            // gridColumnPccpch_Rscp_Max
            // 
            this.gridColumnPccpch_Rscp_Max.Caption = "PCCPCH_RSCP最大值";
            this.gridColumnPccpch_Rscp_Max.FieldName = "Pccpch_Rscp_Max";
            this.gridColumnPccpch_Rscp_Max.Name = "gridColumnPccpch_Rscp_Max";
            this.gridColumnPccpch_Rscp_Max.Visible = true;
            this.gridColumnPccpch_Rscp_Max.VisibleIndex = 18;
            // 
            // gridColumnPccpch_Rscp_Min
            // 
            this.gridColumnPccpch_Rscp_Min.Caption = "PCCPCH_RSCP最小值";
            this.gridColumnPccpch_Rscp_Min.FieldName = "Pccpch_Rscp_Min";
            this.gridColumnPccpch_Rscp_Min.Name = "gridColumnPccpch_Rscp_Min";
            this.gridColumnPccpch_Rscp_Min.Visible = true;
            this.gridColumnPccpch_Rscp_Min.VisibleIndex = 19;
            // 
            // gridColumnPccpch_Rscp_Mean
            // 
            this.gridColumnPccpch_Rscp_Mean.Caption = "PCCPCH_RSCP平均值";
            this.gridColumnPccpch_Rscp_Mean.FieldName = "Pccpch_Rscp_Mean";
            this.gridColumnPccpch_Rscp_Mean.Name = "gridColumnPccpch_Rscp_Mean";
            this.gridColumnPccpch_Rscp_Mean.Visible = true;
            this.gridColumnPccpch_Rscp_Mean.VisibleIndex = 20;
            // 
            // gridColumnPccpch_C2I_Max
            // 
            this.gridColumnPccpch_C2I_Max.Caption = "PCCPCH_C/I最大值";
            this.gridColumnPccpch_C2I_Max.FieldName = "Pccpch_C2I_Max";
            this.gridColumnPccpch_C2I_Max.Name = "gridColumnPccpch_C2I_Max";
            this.gridColumnPccpch_C2I_Max.Visible = true;
            this.gridColumnPccpch_C2I_Max.VisibleIndex = 21;
            // 
            // gridColumnPccpch_C2I_Min
            // 
            this.gridColumnPccpch_C2I_Min.Caption = "PCCPCH_C/I最小值";
            this.gridColumnPccpch_C2I_Min.FieldName = "Pccpch_C2I_Min";
            this.gridColumnPccpch_C2I_Min.Name = "gridColumnPccpch_C2I_Min";
            this.gridColumnPccpch_C2I_Min.Visible = true;
            this.gridColumnPccpch_C2I_Min.VisibleIndex = 22;
            // 
            // gridColumnPccpch_C2I_Mean
            // 
            this.gridColumnPccpch_C2I_Mean.Caption = "PCCPCH_C/I平均值";
            this.gridColumnPccpch_C2I_Mean.FieldName = "Pccpch_C2I_Mean";
            this.gridColumnPccpch_C2I_Mean.Name = "gridColumnPccpch_C2I_Mean";
            this.gridColumnPccpch_C2I_Mean.Visible = true;
            this.gridColumnPccpch_C2I_Mean.VisibleIndex = 23;
            // 
            // gridColumnDpch_Rscp_Max
            // 
            this.gridColumnDpch_Rscp_Max.Caption = "DPCH_RSCP最大值";
            this.gridColumnDpch_Rscp_Max.FieldName = "Dpch_Rscp_Max";
            this.gridColumnDpch_Rscp_Max.Name = "gridColumnDpch_Rscp_Max";
            this.gridColumnDpch_Rscp_Max.Visible = true;
            this.gridColumnDpch_Rscp_Max.VisibleIndex = 24;
            // 
            // gridColumnDpch_Rscp_Min
            // 
            this.gridColumnDpch_Rscp_Min.Caption = "DPCH_RSCP最小值";
            this.gridColumnDpch_Rscp_Min.FieldName = "Dpch_Rscp_Min";
            this.gridColumnDpch_Rscp_Min.Name = "gridColumnDpch_Rscp_Min";
            this.gridColumnDpch_Rscp_Min.Visible = true;
            this.gridColumnDpch_Rscp_Min.VisibleIndex = 25;
            // 
            // gridColumnDpch_Rscp_Mean
            // 
            this.gridColumnDpch_Rscp_Mean.Caption = "DPCH_RSCP平均值";
            this.gridColumnDpch_Rscp_Mean.FieldName = "Dpch_Rscp_Mean";
            this.gridColumnDpch_Rscp_Mean.Name = "gridColumnDpch_Rscp_Mean";
            this.gridColumnDpch_Rscp_Mean.Visible = true;
            this.gridColumnDpch_Rscp_Mean.VisibleIndex = 26;
            // 
            // gridColumnDpch_C2I_Max
            // 
            this.gridColumnDpch_C2I_Max.Caption = "DPCH_C/I最大值";
            this.gridColumnDpch_C2I_Max.FieldName = "Dpch_C2I_Max";
            this.gridColumnDpch_C2I_Max.Name = "gridColumnDpch_C2I_Max";
            this.gridColumnDpch_C2I_Max.Visible = true;
            this.gridColumnDpch_C2I_Max.VisibleIndex = 27;
            // 
            // gridColumnDpch_C2I_Min
            // 
            this.gridColumnDpch_C2I_Min.Caption = "DPCH_C/I最小值";
            this.gridColumnDpch_C2I_Min.FieldName = "Dpch_C2I_Min";
            this.gridColumnDpch_C2I_Min.Name = "gridColumnDpch_C2I_Min";
            this.gridColumnDpch_C2I_Min.Visible = true;
            this.gridColumnDpch_C2I_Min.VisibleIndex = 28;
            // 
            // gridColumnDpch_C2I_Mean
            // 
            this.gridColumnDpch_C2I_Mean.Caption = "DPCH_C/I平均值";
            this.gridColumnDpch_C2I_Mean.FieldName = "Dpch_C2I_Mean";
            this.gridColumnDpch_C2I_Mean.Name = "gridColumnDpch_C2I_Mean";
            this.gridColumnDpch_C2I_Mean.Visible = true;
            this.gridColumnDpch_C2I_Mean.VisibleIndex = 29;
            // 
            // gridColumnBler_Max
            // 
            this.gridColumnBler_Max.Caption = "BLER最大值";
            this.gridColumnBler_Max.FieldName = "Bler_Max";
            this.gridColumnBler_Max.Name = "gridColumnBler_Max";
            this.gridColumnBler_Max.Visible = true;
            this.gridColumnBler_Max.VisibleIndex = 30;
            // 
            // gridColumnBler_Min
            // 
            this.gridColumnBler_Min.Caption = "BLER最小值";
            this.gridColumnBler_Min.FieldName = "Bler_Min";
            this.gridColumnBler_Min.Name = "gridColumnBler_Min";
            this.gridColumnBler_Min.Visible = true;
            this.gridColumnBler_Min.VisibleIndex = 31;
            // 
            // gridColumnBler_Mean
            // 
            this.gridColumnBler_Mean.Caption = "BLER平均值";
            this.gridColumnBler_Mean.FieldName = "Bler_Mean";
            this.gridColumnBler_Mean.Name = "gridColumnBler_Mean";
            this.gridColumnBler_Mean.Visible = true;
            this.gridColumnBler_Mean.VisibleIndex = 32;
            // 
            // gridColumnPdsch_rscp_Max
            // 
            this.gridColumnPdsch_rscp_Max.Caption = "PDSCH_RSCP_最大值";
            this.gridColumnPdsch_rscp_Max.FieldName = "Pdsch_Rscp_Max";
            this.gridColumnPdsch_rscp_Max.Name = "gridColumnPdsch_rscp_Max";
            this.gridColumnPdsch_rscp_Max.Visible = true;
            this.gridColumnPdsch_rscp_Max.VisibleIndex = 33;
            // 
            // gridColumnPdsch_rscp_Min
            // 
            this.gridColumnPdsch_rscp_Min.Caption = "PDSCH_RSCP_最小值";
            this.gridColumnPdsch_rscp_Min.FieldName = "Pdsch_Rscp_Min";
            this.gridColumnPdsch_rscp_Min.Name = "gridColumnPdsch_rscp_Min";
            this.gridColumnPdsch_rscp_Min.Visible = true;
            this.gridColumnPdsch_rscp_Min.VisibleIndex = 34;
            // 
            // gridColumnPdsch_rscp_Mean
            // 
            this.gridColumnPdsch_rscp_Mean.Caption = "PDSCH_RSCP_平均值";
            this.gridColumnPdsch_rscp_Mean.FieldName = "Pdsch_Rscp_Mean";
            this.gridColumnPdsch_rscp_Mean.Name = "gridColumnPdsch_rscp_Mean";
            this.gridColumnPdsch_rscp_Mean.Visible = true;
            this.gridColumnPdsch_rscp_Mean.VisibleIndex = 35;
            // 
            // gridColumnPdsch_c2i_Max
            // 
            this.gridColumnPdsch_c2i_Max.Caption = "PDSCH_C/I_最大值";
            this.gridColumnPdsch_c2i_Max.FieldName = "Pdsch_C2I_Max";
            this.gridColumnPdsch_c2i_Max.Name = "gridColumnPdsch_c2i_Max";
            this.gridColumnPdsch_c2i_Max.Visible = true;
            this.gridColumnPdsch_c2i_Max.VisibleIndex = 36;
            // 
            // gridColumnPdsch_c2i_Min
            // 
            this.gridColumnPdsch_c2i_Min.Caption = "PDSCH_C/I_最小值";
            this.gridColumnPdsch_c2i_Min.FieldName = "Pdsch_C2I_Min";
            this.gridColumnPdsch_c2i_Min.Name = "gridColumnPdsch_c2i_Min";
            this.gridColumnPdsch_c2i_Min.Visible = true;
            this.gridColumnPdsch_c2i_Min.VisibleIndex = 37;
            // 
            // gridColumnPdsch_c2i_Mean
            // 
            this.gridColumnPdsch_c2i_Mean.Caption = "PDSCH_C/I_平均值";
            this.gridColumnPdsch_c2i_Mean.FieldName = "Pdsch_C2I_Mean";
            this.gridColumnPdsch_c2i_Mean.Name = "gridColumnPdsch_c2i_Mean";
            this.gridColumnPdsch_c2i_Mean.Visible = true;
            this.gridColumnPdsch_c2i_Mean.VisibleIndex = 38;
            // 
            // gridColumnScch_rscp_Max
            // 
            this.gridColumnScch_rscp_Max.Caption = "SCCH_RSCP_最大值";
            this.gridColumnScch_rscp_Max.FieldName = "Scch_Rscp_Max";
            this.gridColumnScch_rscp_Max.Name = "gridColumnScch_rscp_Max";
            this.gridColumnScch_rscp_Max.Visible = true;
            this.gridColumnScch_rscp_Max.VisibleIndex = 39;
            // 
            // gridColumnScch_rscp_Min
            // 
            this.gridColumnScch_rscp_Min.Caption = "SCCH_RSCP_最小值";
            this.gridColumnScch_rscp_Min.FieldName = "Scch_Rscp_Min";
            this.gridColumnScch_rscp_Min.Name = "gridColumnScch_rscp_Min";
            this.gridColumnScch_rscp_Min.Visible = true;
            this.gridColumnScch_rscp_Min.VisibleIndex = 40;
            // 
            // gridColumnScch_rscp_Mean
            // 
            this.gridColumnScch_rscp_Mean.Caption = "SCCH_RSCP_平均值";
            this.gridColumnScch_rscp_Mean.FieldName = "Scch_Rscp_Mean";
            this.gridColumnScch_rscp_Mean.Name = "gridColumnScch_rscp_Mean";
            this.gridColumnScch_rscp_Mean.Visible = true;
            this.gridColumnScch_rscp_Mean.VisibleIndex = 41;
            // 
            // gridColumnScch_c2i_Max
            // 
            this.gridColumnScch_c2i_Max.Caption = "SCCH_C/I_最大值";
            this.gridColumnScch_c2i_Max.FieldName = "Scch_C2I_Max";
            this.gridColumnScch_c2i_Max.Name = "gridColumnScch_c2i_Max";
            this.gridColumnScch_c2i_Max.Visible = true;
            this.gridColumnScch_c2i_Max.VisibleIndex = 42;
            // 
            // gridColumnScch_c2i_Min
            // 
            this.gridColumnScch_c2i_Min.Caption = "SCCH_C/I_最小值";
            this.gridColumnScch_c2i_Min.FieldName = "Scch_C2I_Min";
            this.gridColumnScch_c2i_Min.Name = "gridColumnScch_c2i_Min";
            this.gridColumnScch_c2i_Min.Visible = true;
            this.gridColumnScch_c2i_Min.VisibleIndex = 43;
            // 
            // gridColumnScch_c2i_Mean
            // 
            this.gridColumnScch_c2i_Mean.Caption = "SCCH_C/I_平均值";
            this.gridColumnScch_c2i_Mean.FieldName = "Scch_C2I_Mean";
            this.gridColumnScch_c2i_Mean.Name = "gridColumnScch_c2i_Mean";
            this.gridColumnScch_c2i_Mean.Visible = true;
            this.gridColumnScch_c2i_Mean.VisibleIndex = 44;
            // 
            // gridColumnScch_ScheRate_Max
            // 
            this.gridColumnScch_ScheRate_Max.Caption = "调度率最大值";
            this.gridColumnScch_ScheRate_Max.FieldName = "Scch_scheRate_Max";
            this.gridColumnScch_ScheRate_Max.Name = "gridColumnScch_ScheRate_Max";
            this.gridColumnScch_ScheRate_Max.Visible = true;
            this.gridColumnScch_ScheRate_Max.VisibleIndex = 45;
            // 
            // gridColumnScch_ScheRate_Min
            // 
            this.gridColumnScch_ScheRate_Min.Caption = "调度率最小值";
            this.gridColumnScch_ScheRate_Min.FieldName = "Scch_scheRate_Min";
            this.gridColumnScch_ScheRate_Min.Name = "gridColumnScch_ScheRate_Min";
            this.gridColumnScch_ScheRate_Min.Visible = true;
            this.gridColumnScch_ScheRate_Min.VisibleIndex = 46;
            // 
            // gridColumnScch_ScheRate_Mean
            // 
            this.gridColumnScch_ScheRate_Mean.Caption = "调度率平均值";
            this.gridColumnScch_ScheRate_Mean.FieldName = "Scch_scheRate_Mean";
            this.gridColumnScch_ScheRate_Mean.Name = "gridColumnScch_ScheRate_Mean";
            this.gridColumnScch_ScheRate_Mean.Visible = true;
            this.gridColumnScch_ScheRate_Mean.VisibleIndex = 47;
            // 
            // gridColumnBruNumDl_Max
            // 
            this.gridColumnBruNumDl_Max.Caption = "码道数最大值";
            this.gridColumnBruNumDl_Max.FieldName = "BruNumDl_Max";
            this.gridColumnBruNumDl_Max.Name = "gridColumnBruNumDl_Max";
            this.gridColumnBruNumDl_Max.Visible = true;
            this.gridColumnBruNumDl_Max.VisibleIndex = 48;
            // 
            // gridColumnBruNumDl_Min
            // 
            this.gridColumnBruNumDl_Min.Caption = "码道数最小值";
            this.gridColumnBruNumDl_Min.FieldName = "BruNumDl_Min";
            this.gridColumnBruNumDl_Min.Name = "gridColumnBruNumDl_Min";
            this.gridColumnBruNumDl_Min.Visible = true;
            this.gridColumnBruNumDl_Min.VisibleIndex = 49;
            // 
            // gridColumnBruNumDl_Mean
            // 
            this.gridColumnBruNumDl_Mean.Caption = "码道数平均值";
            this.gridColumnBruNumDl_Mean.FieldName = "BruNumDl_Mean";
            this.gridColumnBruNumDl_Mean.Name = "gridColumnBruNumDl_Mean";
            this.gridColumnBruNumDl_Mean.Visible = true;
            this.gridColumnBruNumDl_Mean.VisibleIndex = 50;
            // 
            // gridColumnHandOver_count
            // 
            this.gridColumnHandOver_count.Caption = "切换次数";
            this.gridColumnHandOver_count.FieldName = "HandOver_count";
            this.gridColumnHandOver_count.Name = "gridColumnHandOver_count";
            this.gridColumnHandOver_count.Visible = true;
            this.gridColumnHandOver_count.VisibleIndex = 51;
            // 
            // LowSpeedInfoForm_TD
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(895, 407);
            this.Controls.Add(this.gridControl);
            this.Name = "LowSpeedInfoForm_TD";
            this.Text = "低速率里程分析";
            ((System.ComponentModel.ISupportInitialize)(this.gridControl)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraGrid.GridControl gridControl;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnFileName;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnTime;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnLongitude;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnLatitude;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnDuration;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnDistance;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnHighSpeed;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnLowSpeed;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnMeanSpeed;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnLACCI;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnFreq;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnRoadDesc;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnPccpch_Rscp_Max;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnPccpch_Rscp_Min;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnPccpch_Rscp_Mean;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnPccpch_C2I_Max;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnPccpch_C2I_Min;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnPccpch_C2I_Mean;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnDpch_Rscp_Max;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnDpch_Rscp_Min;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnDpch_Rscp_Mean;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnDpch_C2I_Max;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnDpch_C2I_Min;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnDpch_C2I_Mean;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnBler_Max;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnBler_Min;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnBler_Mean;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnPdsch_rscp_Max;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnPdsch_rscp_Min;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnPdsch_rscp_Mean;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnPdsch_c2i_Max;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnPdsch_c2i_Min;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnPdsch_c2i_Mean;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnScch_rscp_Max;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnScch_rscp_Min;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnScch_rscp_Mean;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnScch_c2i_Max;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnScch_c2i_Min;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnScch_c2i_Mean;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnHandOver_count;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnScch_ScheRate_Max;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnScch_ScheRate_Min;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnScch_ScheRate_Mean;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnBruNumDl_Max;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnBruNumDl_Min;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnBruNumDl_Mean;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnPointCount;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnTimeSlotUsedAvgString;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnNetType;
    }
}