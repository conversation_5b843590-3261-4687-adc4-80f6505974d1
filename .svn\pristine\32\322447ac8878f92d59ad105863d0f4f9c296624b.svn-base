﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class DiyQueryFddDBAlarm : DIYSQLBase
    {
        public List<FddAlarmInfo> DataList { get; private set; }
        readonly LTECell cell;
        readonly string testMonthStr;
        readonly FddDatabaseSetting setting;
        public DiyQueryFddDBAlarm(LTECell cell, string testMonthStr, FddDatabaseSetting setting)
            : base()
        {
            MainDB = true;
            this.cell = cell;
            this.testMonthStr = testMonthStr;
            this.setting = setting;
        }

        public override string Name
        {
            get
            {
                return "查询FDD单验告警信息";
            }
        }

        /// <summary>
        /// 查询ECI对应最新一个工单的所有数据
        /// </summary>
        /// <returns></returns>
        protected override string getSqlTextString()
        {
            string tableName = string.Format(@"{0}.{1}.[dbo].[{2}{3}]", setting.ServerIp, setting.DbName, setting.TableNameHead, testMonthStr);
            StringBuilder selectSQL = new StringBuilder();
            selectSQL.AppendFormat("select [stime],[name],[typename] from {0} where CI = ({1})", tableName, cell.ECI);
            return selectSQL.ToString();
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            int idx = 0;
            E_VType[] rType = new E_VType[3];
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx] = E_VType.E_String;
            return rType;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            DataList = new List<FddAlarmInfo>();
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    FddAlarmInfo data = new FddAlarmInfo();
                    data.FillData(package);
                    DataList.Add(data);
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }
    }

    public class FddAlarmInfo
    {
        public string CellName { get; set; }
        public string AlarmType { get; set; }
        public string Date { get; set; }

        public void FillData(Package package)
        {
            Date = package.Content.GetParamString();
            CellName = package.Content.GetParamString();
            AlarmType = package.Content.GetParamString();
        }
    }
}
