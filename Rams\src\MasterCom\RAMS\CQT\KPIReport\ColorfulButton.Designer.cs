﻿namespace MasterCom.RAMS.CQT
{
    partial class ColorfulButton
    {
        /// <summary> 
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Component Designer generated code

        /// <summary> 
        /// Required method for Designer support - do not modify 
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.btn = new DevExpress.XtraEditors.SimpleButton();
            this.SuspendLayout();
            // 
            // btn
            // 
            this.btn.Appearance.BackColor = System.Drawing.Color.GreenYellow;
            this.btn.Appearance.Font = new System.Drawing.Font("Tahoma", 9F, System.Drawing.FontStyle.Bold);
            this.btn.Appearance.Options.UseBackColor = true;
            this.btn.Appearance.Options.UseFont = true;
            this.btn.ButtonStyle = DevExpress.XtraEditors.Controls.BorderStyles.Style3D;
            this.btn.Dock = System.Windows.Forms.DockStyle.Fill;
            this.btn.Location = new System.Drawing.Point(0, 0);
            this.btn.LookAndFeel.Style = DevExpress.LookAndFeel.LookAndFeelStyle.Office2003;
            this.btn.LookAndFeel.UseDefaultLookAndFeel = false;
            this.btn.Name = "btn";
            this.btn.Size = new System.Drawing.Size(180, 40);
            this.btn.TabIndex = 0;
            this.btn.Text = "ColorfulButton";
            this.btn.MouseLeave += new System.EventHandler(this.btn_MouseLeave);
            this.btn.MouseEnter += new System.EventHandler(this.btn_MouseEnter);
            // 
            // ColorfulButton
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.btn);
            this.Name = "ColorfulButton";
            this.Size = new System.Drawing.Size(180, 40);
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.SimpleButton btn;
    }
}
