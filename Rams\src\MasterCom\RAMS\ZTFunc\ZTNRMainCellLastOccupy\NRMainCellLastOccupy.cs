﻿using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRMainCellLastOccupyBase : DIYAnalyseFilesOneByOneByRegion
    {
        List<NRMainCellLastOccupyInfo> resultList { get; set; } = new List<NRMainCellLastOccupyInfo>();
        NRMainCellLastOccupyCondition curCondition;

        protected static readonly object lockObj = new object();
        protected NRMainCellLastOccupyBase()
            : base(MainModel.GetInstance())
        {
            Columns = NRTpHelper.InitBaseReplayParamBackground(true, false);
            ServiceTypes = ServiceTypeManager.GetServiceTypesByServiceName(ServiceName.NR);
        }

        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 35000, 35045, this.Name);
        }
  
        protected override bool getCondition()
        {
            NRMainCellLastOccupyDlg dlg = new NRMainCellLastOccupyDlg();
            dlg.SetCondition(curCondition);
            if (dlg.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                curCondition = dlg.GetCondition();
                return true;
            }
            return false;
        }

        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
            {
                NRMainCellLastOccupyCellInfo cellInfo = null;
                NRMainCellLastOccupyInfo fileInfo = new NRMainCellLastOccupyInfo(file.GetFileInfo());

                for (int i = 0; i < file.TestPoints.Count - 1; i++)
                {
                    cellInfo = dealTP(file, cellInfo, fileInfo, i);
                }
                fileInfo.Sn = resultList.Count + 1;
                fileInfo.Calculate();
                resultList.Add(fileInfo);
            }
        }

        class TPCellInfo
        {
            public int CurIdx { get; set; }
            public TestPoint CurTP { get; set; }
            public TestPoint NextTP { get; set; }
            public object CurEarfcn { get; set; }
            public object NextEarfcn { get; set; }
            public object CurPci { get; set; }
            public object NextPci { get; set; }

            public TPCellInfo(int i, DTFileDataManager file)
            {
                CurIdx = i;
                CurTP = file.TestPoints[i];
                NextTP = file.TestPoints[i + 1];

                CurEarfcn = NRTpHelper.NrTpManager.GetEARFCN(CurTP);
                CurPci = NRTpHelper.NrTpManager.GetPCI(CurTP);
                NextEarfcn = NRTpHelper.NrTpManager.GetEARFCN(NextTP);
                NextPci = NRTpHelper.NrTpManager.GetPCI(NextTP);
            }
        }

        private NRMainCellLastOccupyCellInfo dealTP(DTFileDataManager file, NRMainCellLastOccupyCellInfo cellInfo, NRMainCellLastOccupyInfo fileInfo, int i)
        {
            TPCellInfo info = new TPCellInfo(i, file);
            if (info.CurEarfcn != null && info.CurPci != null)
            {
                if (info.NextEarfcn != null && info.NextPci != null)
                {
                    cellInfo = dealCurTPNextTPIsNotNull(file, cellInfo, fileInfo, info);
                }
                else
                {
                    cellInfo = dealCurTPIsNotNull(cellInfo, fileInfo, info);
                }
            }
            else if (info.NextEarfcn != null && info.NextPci != null && info.CurIdx == file.TestPoints.Count - 2)
            {
                cellInfo = addInfo(info.NextTP, info.NextEarfcn, info.NextPci, fileInfo);
            }

            return cellInfo;
        }

        private NRMainCellLastOccupyCellInfo dealCurTPNextTPIsNotNull(DTFileDataManager file, NRMainCellLastOccupyCellInfo cellInfo, NRMainCellLastOccupyInfo fileInfo, TPCellInfo info)
        {
            if ((int)info.CurEarfcn == (int)info.NextEarfcn && (int)info.CurPci == (int)info.NextPci)
            {
                if (cellInfo == null)
                {
                    cellInfo = initInfo(info.CurTP, info.CurEarfcn, info.CurPci);
                }
                else
                {
                    cellInfo.AddPnt(info.CurTP);
                    if (info.CurIdx == file.TestPoints.Count - 2)
                    {
                        cellInfo.AddPnt(info.NextTP);
                        cellInfo.Sn = fileInfo.CellInfoList.Count + 1;
                        fileInfo.CellInfoList.Add(cellInfo);
                        cellInfo = null;
                    }
                }
            }
            else
            {
                if (cellInfo != null)
                {
                    cellInfo.AddPnt(info.CurTP);
                    cellInfo.Sn = fileInfo.CellInfoList.Count + 1;
                    fileInfo.CellInfoList.Add(cellInfo);
                    cellInfo = null;
                }
                else
                {
                    cellInfo = addInfo(info.CurTP, info.CurEarfcn, info.CurPci, fileInfo);
                    if (info.CurIdx == file.TestPoints.Count - 2)
                    {
                        cellInfo = addInfo(info.NextTP, info.NextEarfcn, info.NextPci, fileInfo);
                    }
                }
            }

            return cellInfo;
        }

        private NRMainCellLastOccupyCellInfo dealCurTPIsNotNull(NRMainCellLastOccupyCellInfo cellInfo, NRMainCellLastOccupyInfo fileInfo, TPCellInfo info)
        {
            if (cellInfo != null)
            {
                cellInfo.AddPnt(info.CurTP);
                cellInfo.Sn = fileInfo.CellInfoList.Count + 1;
                fileInfo.CellInfoList.Add(cellInfo);
                cellInfo = null;
            }
            else
            {
                cellInfo = addInfo(info.CurTP, info.CurEarfcn, info.CurPci, fileInfo);
            }

            return cellInfo;
        }

        private NRMainCellLastOccupyCellInfo addInfo(TestPoint tp, object earfcn, object pci, NRMainCellLastOccupyInfo fileInfo)
        {
            NRMainCellLastOccupyCellInfo cellInfo = initInfo(tp, earfcn, pci);
            cellInfo.Sn = fileInfo.CellInfoList.Count + 1;
            fileInfo.CellInfoList.Add(cellInfo);
            cellInfo = null;
            return cellInfo;
        }

        private NRMainCellLastOccupyCellInfo initInfo(TestPoint tp, object earfcn, object pci)
        {
            NRMainCellLastOccupyCellInfo cellInfo = new NRMainCellLastOccupyCellInfo(curCondition);
            cellInfo.AddPnt(tp);
            NRCell cell = tp.GetMainCell_NR();
            if (cell != null)
            {
                cellInfo.CellName = cell.Name;
            }
            else
            {
                cellInfo.CellName = earfcn.ToString() + "_" + pci.ToString();
            }

            return cellInfo;
        }

        protected override void fireShowForm()
        {
            NRMainCellLastOccupyForm frm = MainModel.CreateResultForm(typeof(NRMainCellLastOccupyForm)) as NRMainCellLastOccupyForm;
            frm.FillData(resultList);
            frm.Visible = true;
            frm.BringToFront();
            resultList = new List<NRMainCellLastOccupyInfo>();
        }
    }

    public class NRMainCellLastOccupyByFile : NRMainCellLastOccupyBase
    {
        private NRMainCellLastOccupyByFile()
            : base()
        {
        }

        private static NRMainCellLastOccupyByFile instance = null;
        public static NRMainCellLastOccupyByFile GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new NRMainCellLastOccupyByFile();
                    }
                }
            }
            return instance;
        }

        public override string Name
        {
            get { return "主服占用(按文件)"; }
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.File;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                return true;
            }
            return false;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        /// <summary>
        /// 判断是否在所选区域内
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            return true;
        }

    }

    public class NRMainCellLastOccupyByRegion : NRMainCellLastOccupyBase
    {
        protected NRMainCellLastOccupyByRegion()
            : base()
        {
        }

        private static NRMainCellLastOccupyByRegion instance = null;
        public static NRMainCellLastOccupyByRegion GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new NRMainCellLastOccupyByRegion();
                    }
                }
            }
            return instance;
        }

        public override string Name
        {
            get { return "主服占用(按区域)"; }
        }
    }
}
