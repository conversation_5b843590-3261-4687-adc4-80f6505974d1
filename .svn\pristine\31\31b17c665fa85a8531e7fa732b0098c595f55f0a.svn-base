﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.Model.PerformanceParam.TD切换分析
{
    class DiySqlQueryJointHandOverAnalysisDataTD : DiySqlQueryJointHandOverAnalysisDate
    {
        public DiySqlQueryJointHandOverAnalysisDataTD(MainModel mainModel)
            : base(mainModel)
        {
        }

        protected override string getSqlTextString()
        {
            return "select iMon from tb_para_utrancell_handover_ana  group by iMon order by iMon";
        }
    }
}
