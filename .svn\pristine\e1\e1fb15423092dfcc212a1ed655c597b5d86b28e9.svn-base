﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraGrid;
using DevExpress.XtraTab;
using DevExpress.XtraGrid.Views.Grid;
using MasterCom.RAMS.Model.PerformanceParam;
using MasterCom.Util;
using MasterCom.RAMS.Model;
using DevExpress.XtraGrid.Columns;
using MasterCom.RAMS.Frame;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class PerformanceDataForm : MinCloseForm
    {
        public MainModel mainmodel { get; set; }
        public PerformanceDataForm(MainModel Mainmodel)
        {
            InitializeComponent();
            mainmodel = Mainmodel;
            TabPageId = 0;
        }
        private static int TabPageId { get; set; }
        public void FillDataTest(List<DataTable> tbs)
        {
            xtraTabControl.TabPages.Clear();
            foreach (DataTable tb in tbs)
            {
                GridView gv = new GridView();
                gv.OptionsView.ShowGroupPanel = false; //隐藏分组面板
                gv.OptionsView.ColumnAutoWidth = false; //自动列宽关闭,显示横向滚动条
                gv.Appearance.HeaderPanel.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;  //列头自动换行
                gv.Appearance.HeaderPanel.TextOptions.Trimming = DevExpress.Utils.Trimming.None;  //dev的bug  必须设置trimming为None
                gv.ColumnPanelRowHeight = 35;       //必须设置列头高度 否则不会换行
                gv.OptionsBehavior.Editable = false;

                for (int cid = 0; cid < tb.Columns.Count; cid++) //动态添加列，以便设置，（注：不进行这步，绑定时也会自动生成列，但，无法对列属性进行操作）
                {
                    GridColumn cellColt = new GridColumn();
                    cellColt.FieldName = tb.Columns[cid].ColumnName;
                    cellColt.Caption = tb.Columns[cid].Caption;
                    cellColt.VisibleIndex = gv.Columns.Count;
                    gv.Columns.Add(cellColt);
                }
                gv.Columns[0].Width = 140;
                gv.Columns[1].Width = 120;
                for (int fixId = 0; fixId < 4; fixId++) // 固定前四列
                {
                    gv.Columns[fixId].Fixed = DevExpress.XtraGrid.Columns.FixedStyle.Left;

                }

                GridControl gc = new GridControl();
                gc.Dock = DockStyle.Fill;
                gc.DataSource = tb;   // 绑定数据源
                gc.ContextMenuStrip = this.contextMenuStrip;
                gc.MainView = gv;
                setGridViewCondition(tb, gv);

                XtraTabPage page = new XtraTabPage();  //  把gridcontrol添加到XtraTabPage
                page.Text = tb.TableName;
                page.Controls.Add(gc);
                xtraTabControl.TabPages.Add(page);
            }
            xtraTabControl.SelectedTabPageIndex = TabPageId;
        }

        private void setGridViewCondition(DataTable tb, GridView gv)
        {
            List<string> cList = new List<string>();
            for (int grcl = 4; grcl < gv.Columns.Count; grcl++)
            {
                int columnNameLength = tb.Columns[grcl].ColumnName.Length / 2;
                if (columnNameLength > 3)
                    gv.Columns[grcl].Width = columnNameLength * 20;
                cList.Clear();
                for (int cellId = 0; cellId < tb.Rows.Count; cellId++)
                {
                    cList.Add(tb.Rows[cellId][grcl].ToString());
                }
                for (int cId = 0; cId < cList.Count; cId++)
                {
                    if (cList[cId].Contains(" "))
                    {
                        StyleFormatCondition sfc = new StyleFormatCondition();
                        sfc.Appearance.BackColor = System.Drawing.Color.White;
                        sfc.Appearance.ForeColor = System.Drawing.Color.Tomato;
                        sfc.Appearance.Options.UseBackColor = true;
                        sfc.Appearance.Options.UseFont = true;
                        sfc.Appearance.Options.UseForeColor = true;
                        sfc.Column = gv.Columns[grcl];
                        sfc.Condition = DevExpress.XtraGrid.FormatConditionEnum.Equal;
                        sfc.Value1 = cList[cId];
                        gv.FormatConditions.AddRange(new DevExpress.XtraGrid.StyleFormatCondition[] { sfc });
                    }
                }
            }
        }

        private void tsmiExport2Xls_Click(object sender, EventArgs e)
        {
            if (sender is ToolStripItem)
            {
                for(int i = 0;i< xtraTabControl.TabPages.Count;i++)
                {
                    xtraTabControl.SelectedTabPageIndex = i;
                }
            }

            ExcelHelper.XtraTabControlToExcel(xtraTabControl, "FieldName");
        }
        //隐藏窗口至右下角
        private void CellPerformanceDataForm_Deactivate(object sender, EventArgs e)
        {
            if (this.WindowState == FormWindowState.Minimized)
            {
                this.Visible = false;
                mainmodel.AddQuickWindowItem(this.GetType().Name, this.Text, "images\\cellquery.gif");
            }
        }

        private void xtraTabControl_Click(object sender, EventArgs e)
        {
            TabPageId = xtraTabControl.SelectedTabPageIndex;
        }
    }
}