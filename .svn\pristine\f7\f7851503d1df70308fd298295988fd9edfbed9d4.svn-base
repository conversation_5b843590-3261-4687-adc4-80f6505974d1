using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using System.Collections;

namespace MasterCom.Util
{
    public class ListViewSorter : IComparer
    {
        public ListViewSorter(ListView listView)
        {
            listView.ColumnClick += columnClicked;
        }

        private void columnClicked(Object sender, ColumnClickEventArgs e)
        {
            ListView listView = sender as ListView;
            if (listView != null)
            {
                listView.ListViewItemSorter = this;
                if (e.Column == sortColumn)
                {
                    if (sorting == SortOrder.Ascending)
                    {
                        sorting = SortOrder.Descending;
                    }
                    else
                    {
                        sorting = SortOrder.Ascending;
                    }
                }
                else
                {
                    sortColumn = e.Column;
                    sorting = SortOrder.Ascending;
                }
                listView.Sort();
            }
        }

        public int Compare(object x, object y)
        {
            ListViewItem listViewItemX = x as ListViewItem;
            ListViewItem listViewItemY = y as ListViewItem;
            if (listViewItemX != null && listViewItemY != null && listViewItemX.SubItems.Count > sortColumn && listViewItemY.SubItems.Count > sortColumn)
            {
                object tagX = listViewItemX.SubItems[sortColumn].Tag;
                object tagY = listViewItemY.SubItems[sortColumn].Tag;
                if (tagX == null || tagY == null || !(tagY is IComparable) || !(tagX is IComparable))
                {
                    tagX = listViewItemX.SubItems[sortColumn].Text;
                    tagY = listViewItemY.SubItems[sortColumn].Text;
                }

                return doSort(tagX, tagY);
            }
            return 0;
        }

        private int doSort(object tagX, object tagY)
        {
            double dX = 0;
            double dY = 0;
            bool isSucX = double.TryParse(tagX.ToString(), out dX);
            bool isSucY = double.TryParse(tagY.ToString(), out dY);

            if (isSucX && isSucY)
            {
                if (sorting == SortOrder.Ascending)
                {
                    return dX >= dY ? 1 : -1;
                }
                else if (sorting == SortOrder.Descending)
                {
                    return dX > dY ? -1 : 1;
                }
            }

            return judgeOrder(tagX, tagY);
        }

        private int judgeOrder(object tagX, object tagY)
        {
            if (tagY is string && !(tagX is string))
            {
                return (sorting == SortOrder.Ascending) ? 1 : -1;
            }
            else if (tagX is string && !(tagY is string))
            {
                return (sorting == SortOrder.Ascending) ? -1 : 1;
            }
            if (sorting == SortOrder.Ascending)
            {
                return ((IComparable)tagX).CompareTo(tagY);
            }
            else if (sorting == SortOrder.Descending)
            {
                return -((IComparable)tagX).CompareTo(tagY);
            }
            return 0;
        }

        private int sortColumn = 0;

        private SortOrder sorting = SortOrder.Ascending;
    }
}
