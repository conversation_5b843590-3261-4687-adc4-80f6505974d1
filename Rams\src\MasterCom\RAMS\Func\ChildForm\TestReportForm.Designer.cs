﻿namespace MasterCom.RAMS.Func
{
    partial class TestReportForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.Ctrlpanel = new System.Windows.Forms.Panel();
            this.btnDownLoad = new System.Windows.Forms.Button();
            this.listView = new System.Windows.Forms.ListView();
            this.columnHeaderSN = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderFileName = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderProjDes = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderYear = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderBatch = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderAreaType = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderDeviceType = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderFileType = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderServiceType = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderCarrierType = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderAgent = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderStaffid = new System.Windows.Forms.ColumnHeader();
            this.contextMenuStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miDownLoadFile = new System.Windows.Forms.ToolStripMenuItem();
            this.Ctrlpanel.SuspendLayout();
            this.contextMenuStrip.SuspendLayout();
            this.SuspendLayout();
            // 
            // Ctrlpanel
            // 
            this.Ctrlpanel.Controls.Add(this.btnDownLoad);
            this.Ctrlpanel.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.Ctrlpanel.Location = new System.Drawing.Point(0, 167);
            this.Ctrlpanel.Name = "Ctrlpanel";
            this.Ctrlpanel.Size = new System.Drawing.Size(867, 37);
            this.Ctrlpanel.TabIndex = 0;
            // 
            // btnDownLoad
            // 
            this.btnDownLoad.Image = global::MasterCom.RAMS.Properties.Resources.download;
            this.btnDownLoad.Location = new System.Drawing.Point(12, 6);
            this.btnDownLoad.Name = "btnDownLoad";
            this.btnDownLoad.Size = new System.Drawing.Size(75, 23);
            this.btnDownLoad.TabIndex = 0;
            this.btnDownLoad.Text = "下载";
            this.btnDownLoad.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btnDownLoad.UseVisualStyleBackColor = true;
            this.btnDownLoad.Click += new System.EventHandler(this.btnDownLoad_Click);
            // 
            // listView
            // 
            this.listView.AllowColumnReorder = true;
            this.listView.AutoArrange = false;
            this.listView.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.columnHeaderSN,
            this.columnHeaderFileName,
            this.columnHeaderProjDes,
            this.columnHeaderYear,
            this.columnHeaderBatch,
            this.columnHeaderAreaType,
            this.columnHeaderDeviceType,
            this.columnHeaderFileType,
            this.columnHeaderServiceType,
            this.columnHeaderCarrierType,
            this.columnHeaderAgent,
            this.columnHeaderStaffid});
            this.listView.ContextMenuStrip = this.contextMenuStrip;
            this.listView.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listView.FullRowSelect = true;
            this.listView.GridLines = true;
            this.listView.HideSelection = false;
            this.listView.LabelWrap = false;
            this.listView.Location = new System.Drawing.Point(0, 0);
            this.listView.Name = "listView";
            this.listView.ShowGroups = false;
            this.listView.Size = new System.Drawing.Size(867, 167);
            this.listView.TabIndex = 1;
            this.listView.UseCompatibleStateImageBehavior = false;
            this.listView.View = System.Windows.Forms.View.Details;
            this.listView.SelectedIndexChanged += new System.EventHandler(this.listView_SelectedIndexChanged);
            // 
            // columnHeaderSN
            // 
            this.columnHeaderSN.Text = "序号";
            // 
            // columnHeaderFileName
            // 
            this.columnHeaderFileName.Text = "文件名";
            // 
            // columnHeaderProjDes
            // 
            this.columnHeaderProjDes.Text = "项目类型";
            this.columnHeaderProjDes.Width = 111;
            // 
            // columnHeaderYear
            // 
            this.columnHeaderYear.Text = "年份";
            this.columnHeaderYear.Width = 80;
            // 
            // columnHeaderBatch
            // 
            this.columnHeaderBatch.Text = "轮次";
            this.columnHeaderBatch.Width = 70;
            // 
            // columnHeaderAreaType
            // 
            this.columnHeaderAreaType.Text = "区域类型";
            this.columnHeaderAreaType.Width = 87;
            // 
            // columnHeaderDeviceType
            // 
            this.columnHeaderDeviceType.Text = "设备类型";
            this.columnHeaderDeviceType.Width = 93;
            // 
            // columnHeaderFileType
            // 
            this.columnHeaderFileType.Text = "文件类型";
            this.columnHeaderFileType.Width = 92;
            // 
            // columnHeaderServiceType
            // 
            this.columnHeaderServiceType.Text = "服务类型";
            this.columnHeaderServiceType.Width = 96;
            // 
            // columnHeaderCarrierType
            // 
            this.columnHeaderCarrierType.Text = "运营商类型";
            this.columnHeaderCarrierType.Width = 85;
            // 
            // columnHeaderAgent
            // 
            this.columnHeaderAgent.Text = "代维公司";
            this.columnHeaderAgent.Width = 85;
            // 
            // columnHeaderStaffid
            // 
            this.columnHeaderStaffid.Text = "工作人员号";
            this.columnHeaderStaffid.Width = 90;
            // 
            // contextMenuStrip
            // 
            this.contextMenuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miDownLoadFile});
            this.contextMenuStrip.Name = "contextMenuStrip";
            this.contextMenuStrip.Size = new System.Drawing.Size(153, 48);
            // 
            // miDownLoadFile
            // 
            this.miDownLoadFile.Image = global::MasterCom.RAMS.Properties.Resources.download;
            this.miDownLoadFile.Name = "miDownLoadFile";
            this.miDownLoadFile.Size = new System.Drawing.Size(152, 22);
            this.miDownLoadFile.Text = "下载到本地...";
            this.miDownLoadFile.Click += new System.EventHandler(this.miDownLoadFile_Click);
            // 
            // TestReportForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(867, 204);
            this.Controls.Add(this.listView);
            this.Controls.Add(this.Ctrlpanel);
            this.Name = "TestReportForm";
            this.Text = "TestReportForm";
            this.Load += new System.EventHandler(this.TestReportForm_Load);
            this.Ctrlpanel.ResumeLayout(false);
            this.contextMenuStrip.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.Panel Ctrlpanel;
        private System.Windows.Forms.ListView listView;
        private System.Windows.Forms.Button btnDownLoad;
        private System.Windows.Forms.ColumnHeader columnHeaderSN;
        private System.Windows.Forms.ColumnHeader columnHeaderFileName;
        private System.Windows.Forms.ColumnHeader columnHeaderProjDes;
        private System.Windows.Forms.ColumnHeader columnHeaderYear;
        private System.Windows.Forms.ColumnHeader columnHeaderBatch;
        private System.Windows.Forms.ColumnHeader columnHeaderAreaType;
        private System.Windows.Forms.ColumnHeader columnHeaderDeviceType;
        private System.Windows.Forms.ColumnHeader columnHeaderFileType;
        private System.Windows.Forms.ColumnHeader columnHeaderServiceType;
        private System.Windows.Forms.ColumnHeader columnHeaderCarrierType;
        private System.Windows.Forms.ColumnHeader columnHeaderAgent;
        private System.Windows.Forms.ColumnHeader columnHeaderStaffid;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip;
        private System.Windows.Forms.ToolStripMenuItem miDownLoadFile;
    }
}