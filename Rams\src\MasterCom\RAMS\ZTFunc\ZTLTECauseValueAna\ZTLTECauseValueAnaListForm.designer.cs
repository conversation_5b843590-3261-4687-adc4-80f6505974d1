﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTLTECauseValueAnaListForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(ZTLTECauseValueAnaListForm));
            this.ctxMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miReplay = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.ListViewCauseValueAna = new BrightIdeasSoftware.TreeListView();
            this.olvColumnStatSN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnFileName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellTAC = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellECI = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellCellID = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellEARFCN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellPCI = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnTime = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLongitude = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLatitude = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnDownLostNum = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRSRP = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnSINR = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnPuschPower = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRSRPBef1s = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnSINRBef1s = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnPuschPowerBef1s = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNextCellName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNextCellTAC = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNextCellECI = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNextCellCellID = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNextCellEARFCN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNextCellPCI = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNextTime = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNextLongitude = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNextLatitude = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNextRSRP = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNextSINR = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNextPuschPower = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnResult = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCauseValue = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCauseReason = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnPCI = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.ctxMenu.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.ListViewCauseValueAna)).BeginInit();
            this.SuspendLayout();
            // 
            // ctxMenu
            // 
            this.ctxMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miReplay,
            this.miExportExcel});
            this.ctxMenu.Name = "ctxMenu";
            this.ctxMenu.Size = new System.Drawing.Size(130, 48);
            // 
            // miReplay
            // 
            this.miReplay.Name = "miReplay";
            this.miReplay.Size = new System.Drawing.Size(129, 22);
            this.miReplay.Text = "回放";
            this.miReplay.Click += new System.EventHandler(this.miReplay_Click);
            // 
            // miExportExcel
            // 
            this.miExportExcel.Name = "miExportExcel";
            this.miExportExcel.Size = new System.Drawing.Size(129, 22);
            this.miExportExcel.Text = "导出Excel";
            this.miExportExcel.Click += new System.EventHandler(this.miExportExcel_Click);
            // 
            // ListViewCauseValueAna
            // 
            this.ListViewCauseValueAna.AllColumns.Add(this.olvColumnStatSN);
            this.ListViewCauseValueAna.AllColumns.Add(this.olvColumnFileName);
            this.ListViewCauseValueAna.AllColumns.Add(this.olvColumnCellName);
            this.ListViewCauseValueAna.AllColumns.Add(this.olvColumnCellTAC);
            this.ListViewCauseValueAna.AllColumns.Add(this.olvColumnCellECI);
            this.ListViewCauseValueAna.AllColumns.Add(this.olvColumnCellCellID);
            this.ListViewCauseValueAna.AllColumns.Add(this.olvColumnCellEARFCN);
            this.ListViewCauseValueAna.AllColumns.Add(this.olvColumnCellPCI);
            this.ListViewCauseValueAna.AllColumns.Add(this.olvColumnTime);
            this.ListViewCauseValueAna.AllColumns.Add(this.olvColumnLongitude);
            this.ListViewCauseValueAna.AllColumns.Add(this.olvColumnLatitude);
            this.ListViewCauseValueAna.AllColumns.Add(this.olvColumnDownLostNum);
            this.ListViewCauseValueAna.AllColumns.Add(this.olvColumnRSRP);
            this.ListViewCauseValueAna.AllColumns.Add(this.olvColumnSINR);
            this.ListViewCauseValueAna.AllColumns.Add(this.olvColumnPuschPower);
            this.ListViewCauseValueAna.AllColumns.Add(this.olvColumnRSRPBef1s);
            this.ListViewCauseValueAna.AllColumns.Add(this.olvColumnSINRBef1s);
            this.ListViewCauseValueAna.AllColumns.Add(this.olvColumnPuschPowerBef1s);
            this.ListViewCauseValueAna.AllColumns.Add(this.olvColumnNextCellName);
            this.ListViewCauseValueAna.AllColumns.Add(this.olvColumnNextCellTAC);
            this.ListViewCauseValueAna.AllColumns.Add(this.olvColumnNextCellECI);
            this.ListViewCauseValueAna.AllColumns.Add(this.olvColumnNextCellCellID);
            this.ListViewCauseValueAna.AllColumns.Add(this.olvColumnNextCellEARFCN);
            this.ListViewCauseValueAna.AllColumns.Add(this.olvColumnNextCellPCI);
            this.ListViewCauseValueAna.AllColumns.Add(this.olvColumnNextTime);
            this.ListViewCauseValueAna.AllColumns.Add(this.olvColumnNextLongitude);
            this.ListViewCauseValueAna.AllColumns.Add(this.olvColumnNextLatitude);
            this.ListViewCauseValueAna.AllColumns.Add(this.olvColumnNextRSRP);
            this.ListViewCauseValueAna.AllColumns.Add(this.olvColumnNextSINR);
            this.ListViewCauseValueAna.AllColumns.Add(this.olvColumnNextPuschPower);
            this.ListViewCauseValueAna.AllColumns.Add(this.olvColumnResult);
            this.ListViewCauseValueAna.AllColumns.Add(this.olvColumnCauseValue);
            this.ListViewCauseValueAna.AllColumns.Add(this.olvColumnCauseReason);
            this.ListViewCauseValueAna.AllColumns.Add(this.olvColumnPCI);
            this.ListViewCauseValueAna.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.ListViewCauseValueAna.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnStatSN,
            this.olvColumnFileName,
            this.olvColumnCellName,
            this.olvColumnCellTAC,
            this.olvColumnCellECI,
            this.olvColumnCellCellID,
            this.olvColumnCellEARFCN,
            this.olvColumnCellPCI,
            this.olvColumnTime,
            this.olvColumnLongitude,
            this.olvColumnLatitude,
            this.olvColumnRSRP,
            this.olvColumnSINR,
            this.olvColumnPuschPower,
            this.olvColumnRSRPBef1s,
            this.olvColumnSINRBef1s,
            this.olvColumnPuschPowerBef1s,
            this.olvColumnNextCellName,
            this.olvColumnNextCellTAC,
            this.olvColumnNextCellECI,
            this.olvColumnNextCellCellID,
            this.olvColumnNextCellEARFCN,
            this.olvColumnNextCellPCI,
            this.olvColumnNextTime,
            this.olvColumnNextLongitude,
            this.olvColumnNextLatitude,
            this.olvColumnNextRSRP,
            this.olvColumnNextSINR,
            this.olvColumnNextPuschPower,
            this.olvColumnResult,
            this.olvColumnCauseValue,
            this.olvColumnCauseReason,
            this.olvColumnPCI});
            this.ListViewCauseValueAna.ContextMenuStrip = this.ctxMenu;
            this.ListViewCauseValueAna.Cursor = System.Windows.Forms.Cursors.Default;
            this.ListViewCauseValueAna.FullRowSelect = true;
            this.ListViewCauseValueAna.GridLines = true;
            this.ListViewCauseValueAna.HeaderWordWrap = true;
            this.ListViewCauseValueAna.IsNeedShowOverlay = false;
            this.ListViewCauseValueAna.Location = new System.Drawing.Point(1, 1);
            this.ListViewCauseValueAna.Name = "ListViewCauseValueAna";
            this.ListViewCauseValueAna.OwnerDraw = true;
            this.ListViewCauseValueAna.ShowGroups = false;
            this.ListViewCauseValueAna.Size = new System.Drawing.Size(1251, 501);
            this.ListViewCauseValueAna.TabIndex = 7;
            this.ListViewCauseValueAna.UseCompatibleStateImageBehavior = false;
            this.ListViewCauseValueAna.View = System.Windows.Forms.View.Details;
            this.ListViewCauseValueAna.VirtualMode = true;
            this.ListViewCauseValueAna.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.ListViewCauseValueAna_MouseDoubleClick);
            // 
            // olvColumnStatSN
            // 
            this.olvColumnStatSN.AspectName = "";
            this.olvColumnStatSN.HeaderFont = null;
            this.olvColumnStatSN.Text = "序号";
            this.olvColumnStatSN.Width = 40;
            // 
            // olvColumnFileName
            // 
            this.olvColumnFileName.HeaderFont = null;
            this.olvColumnFileName.Text = "文件名称";
            this.olvColumnFileName.Width = 120;
            // 
            // olvColumnCellName
            // 
            this.olvColumnCellName.HeaderFont = null;
            this.olvColumnCellName.Text = "重建前小区名称";
            this.olvColumnCellName.Width = 100;
            // 
            // olvColumnCellTAC
            // 
            this.olvColumnCellTAC.HeaderFont = null;
            this.olvColumnCellTAC.Text = "重建前TAC";
            // 
            // olvColumnCellECI
            // 
            this.olvColumnCellECI.HeaderFont = null;
            this.olvColumnCellECI.Text = "重建前ECI";
            // 
            // olvColumnCellCellID
            // 
            this.olvColumnCellCellID.HeaderFont = null;
            this.olvColumnCellCellID.Text = "重建前CellID";
            // 
            // olvColumnCellEARFCN
            // 
            this.olvColumnCellEARFCN.HeaderFont = null;
            this.olvColumnCellEARFCN.Text = "重建前EARFCN";
            // 
            // olvColumnCellPCI
            // 
            this.olvColumnCellPCI.HeaderFont = null;
            this.olvColumnCellPCI.Text = "重建前PCI";
            // 
            // olvColumnTime
            // 
            this.olvColumnTime.HeaderFont = null;
            this.olvColumnTime.Text = "重建前时间";
            this.olvColumnTime.Width = 120;
            // 
            // olvColumnLongitude
            // 
            this.olvColumnLongitude.HeaderFont = null;
            this.olvColumnLongitude.Text = "重建前小区经度";
            this.olvColumnLongitude.Width = 80;
            // 
            // olvColumnLatitude
            // 
            this.olvColumnLatitude.HeaderFont = null;
            this.olvColumnLatitude.Text = "重建前小区纬度";
            this.olvColumnLatitude.Width = 80;
            // 
            // olvColumnDownLostNum
            // 
            this.olvColumnDownLostNum.DisplayIndex = 13;
            this.olvColumnDownLostNum.HeaderFont = null;
            this.olvColumnDownLostNum.IsVisible = false;
            this.olvColumnDownLostNum.Text = "下行RRC重建过程中中RTP丢包数（handsettime）";
            // 
            // olvColumnRSRP
            // 
            this.olvColumnRSRP.HeaderFont = null;
            this.olvColumnRSRP.Text = "重建前小区RSRP";
            // 
            // olvColumnSINR
            // 
            this.olvColumnSINR.HeaderFont = null;
            this.olvColumnSINR.Text = "重建前小区SINR";
            // 
            // olvColumnPuschPower
            // 
            this.olvColumnPuschPower.HeaderFont = null;
            this.olvColumnPuschPower.Text = "重建前PUSCHPower";
            // 
            // olvColumnRSRPBef1s
            // 
            this.olvColumnRSRPBef1s.HeaderFont = null;
            this.olvColumnRSRPBef1s.Text = "重建前1秒时小区RSRP";
            // 
            // olvColumnSINRBef1s
            // 
            this.olvColumnSINRBef1s.HeaderFont = null;
            this.olvColumnSINRBef1s.Text = "重建前1秒时小区SINR";
            // 
            // olvColumnPuschPowerBef1s
            // 
            this.olvColumnPuschPowerBef1s.HeaderFont = null;
            this.olvColumnPuschPowerBef1s.Text = "重建前1秒时PUSCHPower";
            // 
            // olvColumnNextCellName
            // 
            this.olvColumnNextCellName.HeaderFont = null;
            this.olvColumnNextCellName.Text = "重建后小区名称";
            // 
            // olvColumnNextCellTAC
            // 
            this.olvColumnNextCellTAC.HeaderFont = null;
            this.olvColumnNextCellTAC.Text = "重建后TAC";
            // 
            // olvColumnNextCellECI
            // 
            this.olvColumnNextCellECI.HeaderFont = null;
            this.olvColumnNextCellECI.Text = "重建后ECI";
            // 
            // olvColumnNextCellCellID
            // 
            this.olvColumnNextCellCellID.HeaderFont = null;
            this.olvColumnNextCellCellID.Text = "重建后CellID";
            // 
            // olvColumnNextCellEARFCN
            // 
            this.olvColumnNextCellEARFCN.HeaderFont = null;
            this.olvColumnNextCellEARFCN.Text = "重建后EARFCN";
            // 
            // olvColumnNextCellPCI
            // 
            this.olvColumnNextCellPCI.HeaderFont = null;
            this.olvColumnNextCellPCI.Text = "重建后PCI";
            // 
            // olvColumnNextTime
            // 
            this.olvColumnNextTime.HeaderFont = null;
            this.olvColumnNextTime.Text = "重建后时间";
            // 
            // olvColumnNextLongitude
            // 
            this.olvColumnNextLongitude.HeaderFont = null;
            this.olvColumnNextLongitude.Text = "重建后小区经度";
            // 
            // olvColumnNextLatitude
            // 
            this.olvColumnNextLatitude.HeaderFont = null;
            this.olvColumnNextLatitude.Text = "重建后小区纬度";
            // 
            // olvColumnNextRSRP
            // 
            this.olvColumnNextRSRP.HeaderFont = null;
            this.olvColumnNextRSRP.Text = "重建后RSRP";
            // 
            // olvColumnNextSINR
            // 
            this.olvColumnNextSINR.HeaderFont = null;
            this.olvColumnNextSINR.Text = "重建后SINR";
            // 
            // olvColumnNextPuschPower
            // 
            this.olvColumnNextPuschPower.HeaderFont = null;
            this.olvColumnNextPuschPower.Text = "重建后PUSCHPower";
            // 
            // olvColumnResult
            // 
            this.olvColumnResult.HeaderFont = null;
            this.olvColumnResult.Text = "结果";
            // 
            // olvColumnCauseValue
            // 
            this.olvColumnCauseValue.HeaderFont = null;
            this.olvColumnCauseValue.Text = "Cause值";
            // 
            // olvColumnCauseReason
            // 
            this.olvColumnCauseReason.HeaderFont = null;
            this.olvColumnCauseReason.Text = "Cause原因";
            this.olvColumnCauseReason.Width = 69;
            // 
            // olvColumnPCI
            // 
            this.olvColumnPCI.HeaderFont = null;
            this.olvColumnPCI.Text = "重建信令中PCI";
            this.olvColumnPCI.Width = 91;
            // 
            // ZTLTECauseValueAnaListForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1253, 502);
            this.Controls.Add(this.ListViewCauseValueAna);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Name = "ZTLTECauseValueAnaListForm";
            this.Text = "RRC重建分析结果";
            this.ctxMenu.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.ListViewCauseValueAna)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.ContextMenuStrip ctxMenu;
        private System.Windows.Forms.ToolStripMenuItem miExportExcel;
        private System.Windows.Forms.ToolStripMenuItem miReplay;
        private BrightIdeasSoftware.TreeListView ListViewCauseValueAna;
        private BrightIdeasSoftware.OLVColumn olvColumnStatSN;
        private BrightIdeasSoftware.OLVColumn olvColumnCellName;
        private BrightIdeasSoftware.OLVColumn olvColumnCellTAC;
        private BrightIdeasSoftware.OLVColumn olvColumnCellCellID;
        private BrightIdeasSoftware.OLVColumn olvColumnCellEARFCN;
        private BrightIdeasSoftware.OLVColumn olvColumnCellPCI;
        private BrightIdeasSoftware.OLVColumn olvColumnTime;
        private BrightIdeasSoftware.OLVColumn olvColumnRSRP;
        private BrightIdeasSoftware.OLVColumn olvColumnLongitude;
        private BrightIdeasSoftware.OLVColumn olvColumnResult;
        private BrightIdeasSoftware.OLVColumn olvColumnLatitude;
        private BrightIdeasSoftware.OLVColumn olvColumnSINR;
        private BrightIdeasSoftware.OLVColumn olvColumnFileName;
        private BrightIdeasSoftware.OLVColumn olvColumnCellECI;
        private BrightIdeasSoftware.OLVColumn olvColumnCauseValue;
        private BrightIdeasSoftware.OLVColumn olvColumnCauseReason;
        private BrightIdeasSoftware.OLVColumn olvColumnPCI;
        private BrightIdeasSoftware.OLVColumn olvColumnNextCellName;
        private BrightIdeasSoftware.OLVColumn olvColumnNextCellTAC;
        private BrightIdeasSoftware.OLVColumn olvColumnNextCellECI;
        private BrightIdeasSoftware.OLVColumn olvColumnNextCellCellID;
        private BrightIdeasSoftware.OLVColumn olvColumnNextCellEARFCN;
        private BrightIdeasSoftware.OLVColumn olvColumnNextCellPCI;
        private BrightIdeasSoftware.OLVColumn olvColumnNextTime;
        private BrightIdeasSoftware.OLVColumn olvColumnNextLongitude;
        private BrightIdeasSoftware.OLVColumn olvColumnNextLatitude;
        private BrightIdeasSoftware.OLVColumn olvColumnNextRSRP;
        private BrightIdeasSoftware.OLVColumn olvColumnNextSINR;
        private BrightIdeasSoftware.OLVColumn olvColumnPuschPower;
        private BrightIdeasSoftware.OLVColumn olvColumnNextPuschPower;
        private BrightIdeasSoftware.OLVColumn olvColumnRSRPBef1s;
        private BrightIdeasSoftware.OLVColumn olvColumnSINRBef1s;
        private BrightIdeasSoftware.OLVColumn olvColumnPuschPowerBef1s;
        private BrightIdeasSoftware.OLVColumn olvColumnDownLostNum;

    }
}