﻿namespace MasterCom.RAMS.Func
{
    partial class ScanGridAnaSettingForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.groupBox8 = new System.Windows.Forms.GroupBox();
            this.cbxServiceType = new System.Windows.Forms.ComboBox();
            this.treeViewServiceType = new System.Windows.Forms.TreeView();
            this.anaFileFilter = new DevExpress.XtraEditors.GroupControl();
            this.anaTxtFileName = new System.Windows.Forms.TextBox();
            this.anaChkFileName = new System.Windows.Forms.CheckBox();
            this.anaRadioGroup = new DevExpress.XtraEditors.RadioGroup();
            this.label18 = new System.Windows.Forms.Label();
            this.labelAnaProject = new System.Windows.Forms.Label();
            this.btnAnaProjectType = new System.Windows.Forms.Button();
            this.listViewAna = new System.Windows.Forms.ListView();
            this.pickerAnaEndTime = new System.Windows.Forms.DateTimePicker();
            this.label3 = new System.Windows.Forms.Label();
            this.pickerAnaStartTime = new System.Windows.Forms.DateTimePicker();
            this.label2 = new System.Windows.Forms.Label();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.groupBox9 = new System.Windows.Forms.GroupBox();
            this.labelServiceType = new System.Windows.Forms.Label();
            this.treeViewServiceTypeCmp = new System.Windows.Forms.TreeView();
            this.cmpFileFilter = new DevExpress.XtraEditors.GroupControl();
            this.cmpTxtFileName = new System.Windows.Forms.TextBox();
            this.cmpChkFileName = new System.Windows.Forms.CheckBox();
            this.cmpRadioGroup = new DevExpress.XtraEditors.RadioGroup();
            this.label19 = new System.Windows.Forms.Label();
            this.labelCmpProject = new System.Windows.Forms.Label();
            this.chkUseCmp = new System.Windows.Forms.CheckBox();
            this.listViewCmp = new System.Windows.Forms.ListView();
            this.btnCmpProjectType = new System.Windows.Forms.Button();
            this.pickerCmpEndTime = new System.Windows.Forms.DateTimePicker();
            this.pickerCmpStartTime = new System.Windows.Forms.DateTimePicker();
            this.label6 = new System.Windows.Forms.Label();
            this.label7 = new System.Windows.Forms.Label();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.cbxNameField = new System.Windows.Forms.ComboBox();
            this.label20 = new System.Windows.Forms.Label();
            this.btnOpen = new System.Windows.Forms.Button();
            this.txtShpfile = new System.Windows.Forms.TextBox();
            this.label1 = new System.Windows.Forms.Label();
            this.groupBox4 = new System.Windows.Forms.GroupBox();
            this.label21 = new System.Windows.Forms.Label();
            this.numRelMin = new System.Windows.Forms.NumericUpDown();
            this.label25 = new System.Windows.Forms.Label();
            this.label17 = new System.Windows.Forms.Label();
            this.label16 = new System.Windows.Forms.Label();
            this.label15 = new System.Windows.Forms.Label();
            this.numValid = new System.Windows.Forms.NumericUpDown();
            this.label14 = new System.Windows.Forms.Label();
            this.label13 = new System.Windows.Forms.Label();
            this.numAbsolute = new System.Windows.Forms.NumericUpDown();
            this.label12 = new System.Windows.Forms.Label();
            this.label11 = new System.Windows.Forms.Label();
            this.numRelative = new System.Windows.Forms.NumericUpDown();
            this.label10 = new System.Windows.Forms.Label();
            this.label9 = new System.Windows.Forms.Label();
            this.label24 = new System.Windows.Forms.Label();
            this.numHighCoverage = new System.Windows.Forms.NumericUpDown();
            this.label23 = new System.Windows.Forms.Label();
            this.numWeakRxlev = new System.Windows.Forms.NumericUpDown();
            this.label22 = new System.Windows.Forms.Label();
            this.groupBox5 = new System.Windows.Forms.GroupBox();
            this.label27 = new System.Windows.Forms.Label();
            this.label26 = new System.Windows.Forms.Label();
            this.numGridCount = new System.Windows.Forms.NumericUpDown();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.numGridSize = new System.Windows.Forms.NumericUpDown();
            this.label5 = new System.Windows.Forms.Label();
            this.btnCancel = new System.Windows.Forms.Button();
            this.btnOK = new System.Windows.Forms.Button();
            this.dropDownAnaProject = new System.Windows.Forms.ToolStripDropDown();
            this.dropDownCmpProject = new System.Windows.Forms.ToolStripDropDown();
            this.groupBox6 = new System.Windows.Forms.GroupBox();
            this.groupBox10 = new System.Windows.Forms.GroupBox();
            this.checkBoxLianTong = new System.Windows.Forms.CheckBox();
            this.checkBoxDianXin = new System.Windows.Forms.CheckBox();
            this.checkBoxYiDong = new System.Windows.Forms.CheckBox();
            this.groupBox1.SuspendLayout();
            this.groupBox8.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.anaFileFilter)).BeginInit();
            this.anaFileFilter.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.anaRadioGroup.Properties)).BeginInit();
            this.groupBox2.SuspendLayout();
            this.groupBox9.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cmpFileFilter)).BeginInit();
            this.cmpFileFilter.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cmpRadioGroup.Properties)).BeginInit();
            this.groupBox3.SuspendLayout();
            this.groupBox4.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numRelMin)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numValid)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numAbsolute)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRelative)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numHighCoverage)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numWeakRxlev)).BeginInit();
            this.groupBox5.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numGridCount)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numGridSize)).BeginInit();
            this.groupBox6.SuspendLayout();
            this.groupBox10.SuspendLayout();
            this.SuspendLayout();
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.groupBox8);
            this.groupBox1.Controls.Add(this.anaFileFilter);
            this.groupBox1.Controls.Add(this.label18);
            this.groupBox1.Controls.Add(this.labelAnaProject);
            this.groupBox1.Controls.Add(this.btnAnaProjectType);
            this.groupBox1.Controls.Add(this.listViewAna);
            this.groupBox1.Controls.Add(this.pickerAnaEndTime);
            this.groupBox1.Controls.Add(this.label3);
            this.groupBox1.Controls.Add(this.pickerAnaStartTime);
            this.groupBox1.Controls.Add(this.label2);
            this.groupBox1.Location = new System.Drawing.Point(15, 14);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(277, 433);
            this.groupBox1.TabIndex = 0;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "分析数据源";
            // 
            // groupBox8
            // 
            this.groupBox8.Controls.Add(this.cbxServiceType);
            this.groupBox8.Controls.Add(this.treeViewServiceType);
            this.groupBox8.Location = new System.Drawing.Point(23, 191);
            this.groupBox8.Name = "groupBox8";
            this.groupBox8.Size = new System.Drawing.Size(234, 156);
            this.groupBox8.TabIndex = 11;
            this.groupBox8.TabStop = false;
            this.groupBox8.Text = "业务类型";
            // 
            // cbxServiceType
            // 
            this.cbxServiceType.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cbxServiceType.FormattingEnabled = true;
            this.cbxServiceType.Location = new System.Drawing.Point(8, 17);
            this.cbxServiceType.Name = "cbxServiceType";
            this.cbxServiceType.Size = new System.Drawing.Size(163, 22);
            this.cbxServiceType.TabIndex = 5;
            // 
            // treeViewServiceType
            // 
            this.treeViewServiceType.CheckBoxes = true;
            this.treeViewServiceType.Location = new System.Drawing.Point(8, 42);
            this.treeViewServiceType.Name = "treeViewServiceType";
            this.treeViewServiceType.Size = new System.Drawing.Size(220, 108);
            this.treeViewServiceType.TabIndex = 0;
            // 
            // anaFileFilter
            // 
            this.anaFileFilter.Controls.Add(this.anaTxtFileName);
            this.anaFileFilter.Controls.Add(this.anaChkFileName);
            this.anaFileFilter.Controls.Add(this.anaRadioGroup);
            this.anaFileFilter.Location = new System.Drawing.Point(23, 353);
            this.anaFileFilter.Name = "anaFileFilter";
            this.anaFileFilter.Size = new System.Drawing.Size(234, 74);
            this.anaFileFilter.TabIndex = 10;
            // 
            // anaTxtFileName
            // 
            this.anaTxtFileName.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.anaTxtFileName.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.anaTxtFileName.Enabled = false;
            this.anaTxtFileName.Location = new System.Drawing.Point(8, 41);
            this.anaTxtFileName.Name = "anaTxtFileName";
            this.anaTxtFileName.Size = new System.Drawing.Size(215, 21);
            this.anaTxtFileName.TabIndex = 4;
            this.toolTip.SetToolTip(this.anaTxtFileName, "支持按文件名模糊匹配文件，多个文件名时请用“ or ”隔开（不带双引号）");
            // 
            // anaChkFileName
            // 
            this.anaChkFileName.AutoSize = true;
            this.anaChkFileName.BackColor = System.Drawing.Color.Transparent;
            this.anaChkFileName.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.anaChkFileName.Location = new System.Drawing.Point(5, 0);
            this.anaChkFileName.Name = "anaChkFileName";
            this.anaChkFileName.Padding = new System.Windows.Forms.Padding(2, 0, 4, 0);
            this.anaChkFileName.Size = new System.Drawing.Size(78, 16);
            this.anaChkFileName.TabIndex = 7;
            this.anaChkFileName.Text = "文件筛选";
            this.anaChkFileName.UseVisualStyleBackColor = false;
            // 
            // anaRadioGroup
            // 
            this.anaRadioGroup.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.anaRadioGroup.EditValue = true;
            this.anaRadioGroup.Enabled = false;
            this.anaRadioGroup.Location = new System.Drawing.Point(5, 20);
            this.anaRadioGroup.Name = "anaRadioGroup";
            this.anaRadioGroup.Properties.Appearance.BackColor = System.Drawing.Color.Transparent;
            this.anaRadioGroup.Properties.Appearance.Options.UseBackColor = true;
            this.anaRadioGroup.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder;
            this.anaRadioGroup.Properties.Items.AddRange(new DevExpress.XtraEditors.Controls.RadioGroupItem[] {
            new DevExpress.XtraEditors.Controls.RadioGroupItem(true, "文件名"),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(false, "路径"),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(false, "备注"),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(false, "标记")});
            this.anaRadioGroup.Size = new System.Drawing.Size(215, 24);
            this.anaRadioGroup.TabIndex = 1;
            // 
            // label18
            // 
            this.label18.AutoSize = true;
            this.label18.Location = new System.Drawing.Point(21, 97);
            this.label18.Name = "label18";
            this.label18.Size = new System.Drawing.Size(53, 12);
            this.label18.TabIndex = 9;
            this.label18.Text = "数据来源";
            // 
            // labelAnaProject
            // 
            this.labelAnaProject.AutoSize = true;
            this.labelAnaProject.Location = new System.Drawing.Point(82, 97);
            this.labelAnaProject.Name = "labelAnaProject";
            this.labelAnaProject.Size = new System.Drawing.Size(23, 12);
            this.labelAnaProject.TabIndex = 3;
            this.labelAnaProject.Text = "[0]";
            // 
            // btnAnaProjectType
            // 
            this.btnAnaProjectType.Location = new System.Drawing.Point(182, 92);
            this.btnAnaProjectType.Name = "btnAnaProjectType";
            this.btnAnaProjectType.Size = new System.Drawing.Size(75, 23);
            this.btnAnaProjectType.TabIndex = 8;
            this.btnAnaProjectType.Text = "请选择↓";
            this.btnAnaProjectType.UseVisualStyleBackColor = true;
            // 
            // listViewAna
            // 
            this.listViewAna.Location = new System.Drawing.Point(23, 121);
            this.listViewAna.Name = "listViewAna";
            this.listViewAna.Size = new System.Drawing.Size(234, 69);
            this.listViewAna.TabIndex = 7;
            this.listViewAna.UseCompatibleStateImageBehavior = false;
            this.listViewAna.View = System.Windows.Forms.View.List;
            // 
            // pickerAnaEndTime
            // 
            this.pickerAnaEndTime.Location = new System.Drawing.Point(94, 58);
            this.pickerAnaEndTime.Name = "pickerAnaEndTime";
            this.pickerAnaEndTime.Size = new System.Drawing.Size(163, 21);
            this.pickerAnaEndTime.TabIndex = 3;
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(21, 65);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(65, 12);
            this.label3.TabIndex = 2;
            this.label3.Text = "结束日期: ";
            // 
            // pickerAnaStartTime
            // 
            this.pickerAnaStartTime.Location = new System.Drawing.Point(94, 24);
            this.pickerAnaStartTime.Name = "pickerAnaStartTime";
            this.pickerAnaStartTime.Size = new System.Drawing.Size(163, 21);
            this.pickerAnaStartTime.TabIndex = 1;
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(21, 31);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(65, 12);
            this.label2.TabIndex = 0;
            this.label2.Text = "开始日期: ";
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.groupBox9);
            this.groupBox2.Controls.Add(this.cmpFileFilter);
            this.groupBox2.Controls.Add(this.label19);
            this.groupBox2.Controls.Add(this.labelCmpProject);
            this.groupBox2.Controls.Add(this.chkUseCmp);
            this.groupBox2.Controls.Add(this.listViewCmp);
            this.groupBox2.Controls.Add(this.btnCmpProjectType);
            this.groupBox2.Controls.Add(this.pickerCmpEndTime);
            this.groupBox2.Controls.Add(this.pickerCmpStartTime);
            this.groupBox2.Controls.Add(this.label6);
            this.groupBox2.Controls.Add(this.label7);
            this.groupBox2.Location = new System.Drawing.Point(318, 14);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(284, 433);
            this.groupBox2.TabIndex = 1;
            this.groupBox2.TabStop = false;
            // 
            // groupBox9
            // 
            this.groupBox9.Controls.Add(this.labelServiceType);
            this.groupBox9.Controls.Add(this.treeViewServiceTypeCmp);
            this.groupBox9.Location = new System.Drawing.Point(20, 191);
            this.groupBox9.Name = "groupBox9";
            this.groupBox9.Size = new System.Drawing.Size(234, 156);
            this.groupBox9.TabIndex = 17;
            this.groupBox9.TabStop = false;
            this.groupBox9.Text = "业务类型";
            // 
            // labelServiceType
            // 
            this.labelServiceType.AutoSize = true;
            this.labelServiceType.Location = new System.Drawing.Point(8, 20);
            this.labelServiceType.Name = "labelServiceType";
            this.labelServiceType.Size = new System.Drawing.Size(77, 12);
            this.labelServiceType.TabIndex = 1;
            this.labelServiceType.Text = "当前业务类型";
            // 
            // treeViewServiceTypeCmp
            // 
            this.treeViewServiceTypeCmp.CheckBoxes = true;
            this.treeViewServiceTypeCmp.Location = new System.Drawing.Point(8, 42);
            this.treeViewServiceTypeCmp.Name = "treeViewServiceTypeCmp";
            this.treeViewServiceTypeCmp.Size = new System.Drawing.Size(220, 108);
            this.treeViewServiceTypeCmp.TabIndex = 0;
            // 
            // cmpFileFilter
            // 
            this.cmpFileFilter.Controls.Add(this.cmpTxtFileName);
            this.cmpFileFilter.Controls.Add(this.cmpChkFileName);
            this.cmpFileFilter.Controls.Add(this.cmpRadioGroup);
            this.cmpFileFilter.Enabled = false;
            this.cmpFileFilter.Location = new System.Drawing.Point(20, 356);
            this.cmpFileFilter.Name = "cmpFileFilter";
            this.cmpFileFilter.Size = new System.Drawing.Size(239, 71);
            this.cmpFileFilter.TabIndex = 16;
            // 
            // cmpTxtFileName
            // 
            this.cmpTxtFileName.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.cmpTxtFileName.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.cmpTxtFileName.Enabled = false;
            this.cmpTxtFileName.Location = new System.Drawing.Point(10, 41);
            this.cmpTxtFileName.Name = "cmpTxtFileName";
            this.cmpTxtFileName.Size = new System.Drawing.Size(220, 21);
            this.cmpTxtFileName.TabIndex = 4;
            this.toolTip.SetToolTip(this.cmpTxtFileName, "支持按文件名模糊匹配文件，多个文件名时请用“ or ”隔开（不带双引号）");
            // 
            // cmpChkFileName
            // 
            this.cmpChkFileName.AutoSize = true;
            this.cmpChkFileName.BackColor = System.Drawing.Color.Transparent;
            this.cmpChkFileName.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.cmpChkFileName.Location = new System.Drawing.Point(5, 0);
            this.cmpChkFileName.Name = "cmpChkFileName";
            this.cmpChkFileName.Padding = new System.Windows.Forms.Padding(2, 0, 4, 0);
            this.cmpChkFileName.Size = new System.Drawing.Size(78, 16);
            this.cmpChkFileName.TabIndex = 7;
            this.cmpChkFileName.Text = "文件筛选";
            this.cmpChkFileName.UseVisualStyleBackColor = false;
            // 
            // cmpRadioGroup
            // 
            this.cmpRadioGroup.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.cmpRadioGroup.EditValue = true;
            this.cmpRadioGroup.Enabled = false;
            this.cmpRadioGroup.Location = new System.Drawing.Point(5, 20);
            this.cmpRadioGroup.Name = "cmpRadioGroup";
            this.cmpRadioGroup.Properties.Appearance.BackColor = System.Drawing.Color.Transparent;
            this.cmpRadioGroup.Properties.Appearance.Options.UseBackColor = true;
            this.cmpRadioGroup.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder;
            this.cmpRadioGroup.Properties.Items.AddRange(new DevExpress.XtraEditors.Controls.RadioGroupItem[] {
            new DevExpress.XtraEditors.Controls.RadioGroupItem(true, "文件名"),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(false, "路径"),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(false, "备注"),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(false, "标记")});
            this.cmpRadioGroup.Size = new System.Drawing.Size(220, 24);
            this.cmpRadioGroup.TabIndex = 1;
            // 
            // label19
            // 
            this.label19.AutoSize = true;
            this.label19.Location = new System.Drawing.Point(22, 98);
            this.label19.Name = "label19";
            this.label19.Size = new System.Drawing.Size(53, 12);
            this.label19.TabIndex = 15;
            this.label19.Text = "数据来源";
            // 
            // labelCmpProject
            // 
            this.labelCmpProject.AutoSize = true;
            this.labelCmpProject.Location = new System.Drawing.Point(80, 98);
            this.labelCmpProject.Name = "labelCmpProject";
            this.labelCmpProject.Size = new System.Drawing.Size(23, 12);
            this.labelCmpProject.TabIndex = 14;
            this.labelCmpProject.Text = "[0]";
            // 
            // chkUseCmp
            // 
            this.chkUseCmp.AutoSize = true;
            this.chkUseCmp.Location = new System.Drawing.Point(9, 0);
            this.chkUseCmp.Name = "chkUseCmp";
            this.chkUseCmp.Size = new System.Drawing.Size(84, 16);
            this.chkUseCmp.TabIndex = 13;
            this.chkUseCmp.Text = "对比数据源";
            this.chkUseCmp.UseVisualStyleBackColor = true;
            // 
            // listViewCmp
            // 
            this.listViewCmp.Location = new System.Drawing.Point(20, 122);
            this.listViewCmp.Name = "listViewCmp";
            this.listViewCmp.Size = new System.Drawing.Size(238, 68);
            this.listViewCmp.TabIndex = 12;
            this.listViewCmp.UseCompatibleStateImageBehavior = false;
            this.listViewCmp.View = System.Windows.Forms.View.List;
            // 
            // btnCmpProjectType
            // 
            this.btnCmpProjectType.Location = new System.Drawing.Point(183, 93);
            this.btnCmpProjectType.Name = "btnCmpProjectType";
            this.btnCmpProjectType.Size = new System.Drawing.Size(75, 23);
            this.btnCmpProjectType.TabIndex = 11;
            this.btnCmpProjectType.Text = "请选择↓";
            this.btnCmpProjectType.UseVisualStyleBackColor = true;
            // 
            // pickerCmpEndTime
            // 
            this.pickerCmpEndTime.Location = new System.Drawing.Point(94, 58);
            this.pickerCmpEndTime.Name = "pickerCmpEndTime";
            this.pickerCmpEndTime.Size = new System.Drawing.Size(164, 21);
            this.pickerCmpEndTime.TabIndex = 9;
            // 
            // pickerCmpStartTime
            // 
            this.pickerCmpStartTime.Location = new System.Drawing.Point(96, 24);
            this.pickerCmpStartTime.Name = "pickerCmpStartTime";
            this.pickerCmpStartTime.Size = new System.Drawing.Size(163, 21);
            this.pickerCmpStartTime.TabIndex = 8;
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(22, 65);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(65, 12);
            this.label6.TabIndex = 6;
            this.label6.Text = "结束日期: ";
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(22, 31);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(65, 12);
            this.label7.TabIndex = 5;
            this.label7.Text = "开始日期: ";
            // 
            // groupBox3
            // 
            this.groupBox3.Controls.Add(this.cbxNameField);
            this.groupBox3.Controls.Add(this.label20);
            this.groupBox3.Controls.Add(this.btnOpen);
            this.groupBox3.Controls.Add(this.txtShpfile);
            this.groupBox3.Controls.Add(this.label1);
            this.groupBox3.Location = new System.Drawing.Point(15, 453);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.Size = new System.Drawing.Size(587, 96);
            this.groupBox3.TabIndex = 2;
            this.groupBox3.TabStop = false;
            this.groupBox3.Text = "网格图层设置";
            // 
            // cbxNameField
            // 
            this.cbxNameField.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cbxNameField.FormattingEnabled = true;
            this.cbxNameField.Location = new System.Drawing.Point(86, 59);
            this.cbxNameField.Name = "cbxNameField";
            this.cbxNameField.Size = new System.Drawing.Size(156, 22);
            this.cbxNameField.TabIndex = 4;
            // 
            // label20
            // 
            this.label20.AutoSize = true;
            this.label20.Location = new System.Drawing.Point(19, 65);
            this.label20.Name = "label20";
            this.label20.Size = new System.Drawing.Size(53, 12);
            this.label20.TabIndex = 3;
            this.label20.Text = "名称字段";
            // 
            // btnOpen
            // 
            this.btnOpen.Location = new System.Drawing.Point(487, 27);
            this.btnOpen.Name = "btnOpen";
            this.btnOpen.Size = new System.Drawing.Size(75, 23);
            this.btnOpen.TabIndex = 2;
            this.btnOpen.Text = "打开";
            this.btnOpen.UseVisualStyleBackColor = true;
            // 
            // txtShpfile
            // 
            this.txtShpfile.Location = new System.Drawing.Point(86, 27);
            this.txtShpfile.Name = "txtShpfile";
            this.txtShpfile.Size = new System.Drawing.Size(395, 21);
            this.txtShpfile.TabIndex = 1;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(20, 32);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(53, 12);
            this.label1.TabIndex = 0;
            this.label1.Text = "图层文件";
            // 
            // groupBox4
            // 
            this.groupBox4.Controls.Add(this.label21);
            this.groupBox4.Controls.Add(this.numRelMin);
            this.groupBox4.Controls.Add(this.label25);
            this.groupBox4.Controls.Add(this.label17);
            this.groupBox4.Controls.Add(this.label16);
            this.groupBox4.Controls.Add(this.label15);
            this.groupBox4.Controls.Add(this.numValid);
            this.groupBox4.Controls.Add(this.label14);
            this.groupBox4.Controls.Add(this.label13);
            this.groupBox4.Controls.Add(this.numAbsolute);
            this.groupBox4.Controls.Add(this.label12);
            this.groupBox4.Controls.Add(this.label11);
            this.groupBox4.Controls.Add(this.numRelative);
            this.groupBox4.Controls.Add(this.label10);
            this.groupBox4.Controls.Add(this.label9);
            this.groupBox4.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.groupBox4.Location = new System.Drawing.Point(624, 136);
            this.groupBox4.Name = "groupBox4";
            this.groupBox4.Size = new System.Drawing.Size(306, 191);
            this.groupBox4.TabIndex = 3;
            this.groupBox4.TabStop = false;
            this.groupBox4.Text = "覆盖带设置";
            // 
            // label21
            // 
            this.label21.AutoSize = true;
            this.label21.Location = new System.Drawing.Point(267, 72);
            this.label21.Name = "label21";
            this.label21.Size = new System.Drawing.Size(23, 12);
            this.label21.TabIndex = 18;
            this.label21.Text = "dBm";
            // 
            // numRelMin
            // 
            this.numRelMin.Location = new System.Drawing.Point(186, 68);
            this.numRelMin.Maximum = new decimal(new int[] {
            200,
            0,
            0,
            0});
            this.numRelMin.Minimum = new decimal(new int[] {
            200,
            0,
            0,
            -2147483648});
            this.numRelMin.Name = "numRelMin";
            this.numRelMin.Size = new System.Drawing.Size(73, 21);
            this.numRelMin.TabIndex = 17;
            this.numRelMin.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            // 
            // label25
            // 
            this.label25.AutoSize = true;
            this.label25.Location = new System.Drawing.Point(120, 72);
            this.label25.Name = "label25";
            this.label25.Size = new System.Drawing.Size(65, 12);
            this.label25.TabIndex = 16;
            this.label25.Text = "信号强度> ";
            // 
            // label17
            // 
            this.label17.AutoSize = true;
            this.label17.Location = new System.Drawing.Point(267, 156);
            this.label17.Name = "label17";
            this.label17.Size = new System.Drawing.Size(23, 12);
            this.label17.TabIndex = 10;
            this.label17.Text = "dBm";
            // 
            // label16
            // 
            this.label16.AutoSize = true;
            this.label16.Location = new System.Drawing.Point(267, 113);
            this.label16.Name = "label16";
            this.label16.Size = new System.Drawing.Size(23, 12);
            this.label16.TabIndex = 9;
            this.label16.Text = "dBm";
            // 
            // label15
            // 
            this.label15.AutoSize = true;
            this.label15.Location = new System.Drawing.Point(267, 31);
            this.label15.Name = "label15";
            this.label15.Size = new System.Drawing.Size(17, 12);
            this.label15.TabIndex = 3;
            this.label15.Text = "dB";
            // 
            // numValid
            // 
            this.numValid.Location = new System.Drawing.Point(186, 150);
            this.numValid.Maximum = new decimal(new int[] {
            200,
            0,
            0,
            0});
            this.numValid.Minimum = new decimal(new int[] {
            200,
            0,
            0,
            -2147483648});
            this.numValid.Name = "numValid";
            this.numValid.Size = new System.Drawing.Size(73, 21);
            this.numValid.TabIndex = 8;
            this.numValid.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            // 
            // label14
            // 
            this.label14.AutoSize = true;
            this.label14.Location = new System.Drawing.Point(96, 156);
            this.label14.Name = "label14";
            this.label14.Size = new System.Drawing.Size(89, 12);
            this.label14.TabIndex = 7;
            this.label14.Text = "最强信号强度> ";
            // 
            // label13
            // 
            this.label13.AutoSize = true;
            this.label13.Location = new System.Drawing.Point(12, 155);
            this.label13.Name = "label13";
            this.label13.Size = new System.Drawing.Size(65, 12);
            this.label13.TabIndex = 6;
            this.label13.Text = "计算有效性";
            // 
            // numAbsolute
            // 
            this.numAbsolute.Location = new System.Drawing.Point(186, 109);
            this.numAbsolute.Maximum = new decimal(new int[] {
            200,
            0,
            0,
            0});
            this.numAbsolute.Minimum = new decimal(new int[] {
            200,
            0,
            0,
            -2147483648});
            this.numAbsolute.Name = "numAbsolute";
            this.numAbsolute.Size = new System.Drawing.Size(73, 21);
            this.numAbsolute.TabIndex = 5;
            this.numAbsolute.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            // 
            // label12
            // 
            this.label12.AutoSize = true;
            this.label12.Location = new System.Drawing.Point(120, 113);
            this.label12.Name = "label12";
            this.label12.Size = new System.Drawing.Size(65, 12);
            this.label12.TabIndex = 4;
            this.label12.Text = "信号强度> ";
            // 
            // label11
            // 
            this.label11.AutoSize = true;
            this.label11.Location = new System.Drawing.Point(12, 113);
            this.label11.Name = "label11";
            this.label11.Size = new System.Drawing.Size(65, 12);
            this.label11.TabIndex = 3;
            this.label11.Text = "绝对覆盖带";
            // 
            // numRelative
            // 
            this.numRelative.Location = new System.Drawing.Point(186, 27);
            this.numRelative.Maximum = new decimal(new int[] {
            200,
            0,
            0,
            0});
            this.numRelative.Minimum = new decimal(new int[] {
            200,
            0,
            0,
            -2147483648});
            this.numRelative.Name = "numRelative";
            this.numRelative.Size = new System.Drawing.Size(73, 21);
            this.numRelative.TabIndex = 2;
            this.numRelative.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            // 
            // label10
            // 
            this.label10.AutoSize = true;
            this.label10.Location = new System.Drawing.Point(84, 33);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(101, 12);
            this.label10.TabIndex = 1;
            this.label10.Text = "与最强信号差异< ";
            // 
            // label9
            // 
            this.label9.AutoSize = true;
            this.label9.Location = new System.Drawing.Point(12, 33);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(65, 12);
            this.label9.TabIndex = 0;
            this.label9.Text = "相对覆盖带";
            // 
            // label24
            // 
            this.label24.AutoSize = true;
            this.label24.Location = new System.Drawing.Point(219, 23);
            this.label24.Name = "label24";
            this.label24.Size = new System.Drawing.Size(23, 12);
            this.label24.TabIndex = 15;
            this.label24.Text = "dBm";
            // 
            // numHighCoverage
            // 
            this.numHighCoverage.Location = new System.Drawing.Point(142, 58);
            this.numHighCoverage.Name = "numHighCoverage";
            this.numHighCoverage.Size = new System.Drawing.Size(73, 21);
            this.numHighCoverage.TabIndex = 14;
            this.numHighCoverage.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            // 
            // label23
            // 
            this.label23.AutoSize = true;
            this.label23.Location = new System.Drawing.Point(47, 62);
            this.label23.Name = "label23";
            this.label23.Size = new System.Drawing.Size(89, 12);
            this.label23.TabIndex = 13;
            this.label23.Text = "高重叠度阈值≥";
            // 
            // numWeakRxlev
            // 
            this.numWeakRxlev.Location = new System.Drawing.Point(142, 17);
            this.numWeakRxlev.Minimum = new decimal(new int[] {
            200,
            0,
            0,
            -2147483648});
            this.numWeakRxlev.Name = "numWeakRxlev";
            this.numWeakRxlev.Size = new System.Drawing.Size(73, 21);
            this.numWeakRxlev.TabIndex = 12;
            this.numWeakRxlev.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            // 
            // label22
            // 
            this.label22.AutoSize = true;
            this.label22.Location = new System.Drawing.Point(59, 25);
            this.label22.Name = "label22";
            this.label22.Size = new System.Drawing.Size(77, 12);
            this.label22.TabIndex = 11;
            this.label22.Text = "弱覆盖阈值< ";
            // 
            // groupBox5
            // 
            this.groupBox5.Controls.Add(this.label27);
            this.groupBox5.Controls.Add(this.label26);
            this.groupBox5.Controls.Add(this.numGridCount);
            this.groupBox5.Controls.Add(this.labelControl1);
            this.groupBox5.Controls.Add(this.numGridSize);
            this.groupBox5.Controls.Add(this.label5);
            this.groupBox5.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.groupBox5.Location = new System.Drawing.Point(624, 14);
            this.groupBox5.Name = "groupBox5";
            this.groupBox5.Size = new System.Drawing.Size(306, 109);
            this.groupBox5.TabIndex = 4;
            this.groupBox5.TabStop = false;
            this.groupBox5.Text = "栅格设置";
            // 
            // label27
            // 
            this.label27.AutoSize = true;
            this.label27.Location = new System.Drawing.Point(216, 67);
            this.label27.Name = "label27";
            this.label27.Size = new System.Drawing.Size(17, 12);
            this.label27.TabIndex = 6;
            this.label27.Text = "个";
            // 
            // label26
            // 
            this.label26.AutoSize = true;
            this.label26.Location = new System.Drawing.Point(215, 29);
            this.label26.Name = "label26";
            this.label26.Size = new System.Drawing.Size(17, 12);
            this.label26.TabIndex = 5;
            this.label26.Text = "米";
            // 
            // numGridCount
            // 
            this.numGridCount.Location = new System.Drawing.Point(136, 64);
            this.numGridCount.Name = "numGridCount";
            this.numGridCount.Size = new System.Drawing.Size(73, 21);
            this.numGridCount.TabIndex = 4;
            this.numGridCount.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numGridCount.Value = new decimal(new int[] {
            50,
            0,
            0,
            0});
            // 
            // labelControl1
            // 
            this.labelControl1.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl1.Appearance.Options.UseFont = true;
            this.labelControl1.Location = new System.Drawing.Point(34, 69);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(96, 12);
            this.labelControl1.TabIndex = 3;
            this.labelControl1.Text = "连续区域栅格数≥";
            // 
            // numGridSize
            // 
            this.numGridSize.Location = new System.Drawing.Point(136, 26);
            this.numGridSize.Name = "numGridSize";
            this.numGridSize.Size = new System.Drawing.Size(73, 21);
            this.numGridSize.TabIndex = 1;
            this.numGridSize.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numGridSize.Value = new decimal(new int[] {
            50,
            0,
            0,
            0});
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(53, 32);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(77, 12);
            this.label5.TabIndex = 0;
            this.label5.Text = "军事栅格精度";
            // 
            // btnCancel
            // 
            this.btnCancel.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnCancel.Location = new System.Drawing.Point(852, 563);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(78, 31);
            this.btnCancel.TabIndex = 5;
            this.btnCancel.Text = "取消";
            this.btnCancel.UseVisualStyleBackColor = true;
            // 
            // btnOK
            // 
            this.btnOK.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnOK.Location = new System.Drawing.Point(757, 563);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(78, 31);
            this.btnOK.TabIndex = 6;
            this.btnOK.Text = "确定";
            this.btnOK.UseVisualStyleBackColor = true;
            // 
            // dropDownAnaProject
            // 
            this.dropDownAnaProject.LayoutStyle = System.Windows.Forms.ToolStripLayoutStyle.Flow;
            this.dropDownAnaProject.Name = "toolStripDropDown1";
            this.dropDownAnaProject.Size = new System.Drawing.Size(2, 4);
            // 
            // dropDownCmpProject
            // 
            this.dropDownCmpProject.LayoutStyle = System.Windows.Forms.ToolStripLayoutStyle.Flow;
            this.dropDownCmpProject.Name = "toolStripDropDown1";
            this.dropDownCmpProject.Size = new System.Drawing.Size(2, 4);
            // 
            // groupBox6
            // 
            this.groupBox6.Controls.Add(this.label22);
            this.groupBox6.Controls.Add(this.numWeakRxlev);
            this.groupBox6.Controls.Add(this.label23);
            this.groupBox6.Controls.Add(this.label24);
            this.groupBox6.Controls.Add(this.numHighCoverage);
            this.groupBox6.Location = new System.Drawing.Point(624, 348);
            this.groupBox6.Name = "groupBox6";
            this.groupBox6.Size = new System.Drawing.Size(306, 99);
            this.groupBox6.TabIndex = 7;
            this.groupBox6.TabStop = false;
            this.groupBox6.Text = "其它";
            // 
            // groupBox10
            // 
            this.groupBox10.Controls.Add(this.checkBoxLianTong);
            this.groupBox10.Controls.Add(this.checkBoxDianXin);
            this.groupBox10.Controls.Add(this.checkBoxYiDong);
            this.groupBox10.Location = new System.Drawing.Point(624, 453);
            this.groupBox10.Name = "groupBox10";
            this.groupBox10.Size = new System.Drawing.Size(306, 96);
            this.groupBox10.TabIndex = 9;
            this.groupBox10.TabStop = false;
            this.groupBox10.Text = "运营商";
            // 
            // checkBoxLianTong
            // 
            this.checkBoxLianTong.AutoSize = true;
            this.checkBoxLianTong.Location = new System.Drawing.Point(210, 42);
            this.checkBoxLianTong.Name = "checkBoxLianTong";
            this.checkBoxLianTong.Size = new System.Drawing.Size(72, 16);
            this.checkBoxLianTong.TabIndex = 2;
            this.checkBoxLianTong.Text = "中国联通";
            this.checkBoxLianTong.UseVisualStyleBackColor = true;
            // 
            // checkBoxDianXin
            // 
            this.checkBoxDianXin.AutoSize = true;
            this.checkBoxDianXin.Location = new System.Drawing.Point(117, 42);
            this.checkBoxDianXin.Name = "checkBoxDianXin";
            this.checkBoxDianXin.Size = new System.Drawing.Size(72, 16);
            this.checkBoxDianXin.TabIndex = 1;
            this.checkBoxDianXin.Text = "中国电信";
            this.checkBoxDianXin.UseVisualStyleBackColor = true;
            // 
            // checkBoxYiDong
            // 
            this.checkBoxYiDong.AutoSize = true;
            this.checkBoxYiDong.Checked = true;
            this.checkBoxYiDong.CheckState = System.Windows.Forms.CheckState.Checked;
            this.checkBoxYiDong.Location = new System.Drawing.Point(21, 42);
            this.checkBoxYiDong.Name = "checkBoxYiDong";
            this.checkBoxYiDong.Size = new System.Drawing.Size(72, 16);
            this.checkBoxYiDong.TabIndex = 0;
            this.checkBoxYiDong.Text = "中国移动";
            this.checkBoxYiDong.UseVisualStyleBackColor = true;
            // 
            // ScanGridAnaSettingForm
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(950, 608);
            this.Controls.Add(this.groupBox10);
            this.Controls.Add(this.groupBox1);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.groupBox2);
            this.Controls.Add(this.groupBox4);
            this.Controls.Add(this.groupBox3);
            this.Controls.Add(this.btnOK);
            this.Controls.Add(this.groupBox6);
            this.Controls.Add(this.groupBox5);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "ScanGridAnaSettingForm";
            this.Text = "扫频栅格分析设置";
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.groupBox8.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.anaFileFilter)).EndInit();
            this.anaFileFilter.ResumeLayout(false);
            this.anaFileFilter.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.anaRadioGroup.Properties)).EndInit();
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            this.groupBox9.ResumeLayout(false);
            this.groupBox9.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cmpFileFilter)).EndInit();
            this.cmpFileFilter.ResumeLayout(false);
            this.cmpFileFilter.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cmpRadioGroup.Properties)).EndInit();
            this.groupBox3.ResumeLayout(false);
            this.groupBox3.PerformLayout();
            this.groupBox4.ResumeLayout(false);
            this.groupBox4.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numRelMin)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numValid)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numAbsolute)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRelative)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numHighCoverage)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numWeakRxlev)).EndInit();
            this.groupBox5.ResumeLayout(false);
            this.groupBox5.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numGridCount)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numGridSize)).EndInit();
            this.groupBox6.ResumeLayout(false);
            this.groupBox6.PerformLayout();
            this.groupBox10.ResumeLayout(false);
            this.groupBox10.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.GroupBox groupBox3;
        private System.Windows.Forms.Button btnOpen;
        private System.Windows.Forms.TextBox txtShpfile;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.GroupBox groupBox4;
        private System.Windows.Forms.GroupBox groupBox5;
        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.Button btnOK;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.DateTimePicker pickerAnaStartTime;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.ListView listViewAna;
        private System.Windows.Forms.ComboBox cbxServiceType;
        private System.Windows.Forms.DateTimePicker pickerAnaEndTime;
        private System.Windows.Forms.Button btnAnaProjectType;
        private System.Windows.Forms.ListView listViewCmp;
        private System.Windows.Forms.Button btnCmpProjectType;
        private System.Windows.Forms.DateTimePicker pickerCmpEndTime;
        private System.Windows.Forms.DateTimePicker pickerCmpStartTime;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.CheckBox chkUseCmp;
        private System.Windows.Forms.NumericUpDown numGridSize;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.NumericUpDown numRelative;
        private System.Windows.Forms.Label label10;
        private System.Windows.Forms.Label label12;
        private System.Windows.Forms.Label label11;
        private System.Windows.Forms.NumericUpDown numAbsolute;
        private System.Windows.Forms.Label label14;
        private System.Windows.Forms.Label label13;
        private System.Windows.Forms.Label label17;
        private System.Windows.Forms.Label label16;
        private System.Windows.Forms.Label label15;
        private System.Windows.Forms.NumericUpDown numValid;
        private System.Windows.Forms.ToolStripDropDown dropDownAnaProject;
        private System.Windows.Forms.ToolStripDropDown dropDownCmpProject;
        private System.Windows.Forms.Label labelAnaProject;
        private System.Windows.Forms.Label labelCmpProject;
        private System.Windows.Forms.Label label18;
        private System.Windows.Forms.Label label19;
        private System.Windows.Forms.ComboBox cbxNameField;
        private System.Windows.Forms.Label label20;
        private System.Windows.Forms.Label label9;
        private System.Windows.Forms.Label label22;
        private System.Windows.Forms.Label label23;
        private System.Windows.Forms.NumericUpDown numWeakRxlev;
        private System.Windows.Forms.Label label24;
        private System.Windows.Forms.NumericUpDown numHighCoverage;
        private System.Windows.Forms.NumericUpDown numGridCount;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private System.Windows.Forms.Label label21;
        private System.Windows.Forms.NumericUpDown numRelMin;
        private System.Windows.Forms.Label label25;
        private System.Windows.Forms.Label label27;
        private System.Windows.Forms.Label label26;
        private System.Windows.Forms.GroupBox groupBox6;
        private DevExpress.XtraEditors.GroupControl anaFileFilter;
        private System.Windows.Forms.TextBox anaTxtFileName;
        private System.Windows.Forms.CheckBox anaChkFileName;
        private DevExpress.XtraEditors.RadioGroup anaRadioGroup;
        private DevExpress.XtraEditors.GroupControl cmpFileFilter;
        private System.Windows.Forms.TextBox cmpTxtFileName;
        private System.Windows.Forms.CheckBox cmpChkFileName;
        private DevExpress.XtraEditors.RadioGroup cmpRadioGroup;
        private System.Windows.Forms.GroupBox groupBox8;
        private System.Windows.Forms.TreeView treeViewServiceType;
        private System.Windows.Forms.GroupBox groupBox9;
        private System.Windows.Forms.TreeView treeViewServiceTypeCmp;
        private System.Windows.Forms.GroupBox groupBox10;
        private System.Windows.Forms.CheckBox checkBoxLianTong;
        private System.Windows.Forms.CheckBox checkBoxDianXin;
        private System.Windows.Forms.CheckBox checkBoxYiDong;
        private System.Windows.Forms.Label labelServiceType;
    }
}