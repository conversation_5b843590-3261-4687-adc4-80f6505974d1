﻿namespace MasterCom.RAMS.ZTFunc.VoLTEBlockCallCause
{
    partial class BlockCallCauseForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            DevExpress.XtraCharts.Series series5 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.PieSeriesLabel pieSeriesLabel9 = new DevExpress.XtraCharts.PieSeriesLabel();
            DevExpress.XtraCharts.PiePointOptions piePointOptions9 = new DevExpress.XtraCharts.PiePointOptions();
            DevExpress.XtraCharts.PiePointOptions piePointOptions10 = new DevExpress.XtraCharts.PiePointOptions();
            DevExpress.XtraCharts.PieSeriesView pieSeriesView9 = new DevExpress.XtraCharts.PieSeriesView();
            DevExpress.XtraCharts.PieSeriesLabel pieSeriesLabel10 = new DevExpress.XtraCharts.PieSeriesLabel();
            DevExpress.XtraCharts.PieSeriesView pieSeriesView10 = new DevExpress.XtraCharts.PieSeriesView();
            DevExpress.XtraCharts.ChartTitle chartTitle5 = new DevExpress.XtraCharts.ChartTitle();
            this.tabCtrl = new DevExpress.XtraTab.XtraTabControl();
            this.pageSummary = new DevExpress.XtraTab.XtraTabPage();
            this.splitContainerControl1 = new DevExpress.XtraEditors.SplitContainerControl();
            this.gridSummary = new DevExpress.XtraGrid.GridControl();
            this.ctxMenuSum = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExportXlsSum = new System.Windows.Forms.ToolStripMenuItem();
            this.viewSummary = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.chartMain = new DevExpress.XtraCharts.ChartControl();
            this.pageDetail = new DevExpress.XtraTab.XtraTabPage();
            this.objLv = new BrightIdeasSoftware.TreeListView();
            this.colSN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colFileName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colMoMt = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colIsBlock = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colCause = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colLng = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colLat = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colTime = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colIMSErrorMsg = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colRsrp = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colSinr = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colMultiCvrPer = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colHoNum = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.ctxMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExpandAll = new System.Windows.Forms.ToolStripMenuItem();
            this.miCollapseAll = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator1 = new System.Windows.Forms.ToolStripSeparator();
            this.miExportXls = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator2 = new System.Windows.Forms.ToolStripSeparator();
            this.miReplayFile = new System.Windows.Forms.ToolStripMenuItem();
            this.miReplayEvent = new System.Windows.Forms.ToolStripMenuItem();
            this.miCompareReplayEvent = new System.Windows.Forms.ToolStripMenuItem();
            ((System.ComponentModel.ISupportInitialize)(this.tabCtrl)).BeginInit();
            this.tabCtrl.SuspendLayout();
            this.pageSummary.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).BeginInit();
            this.splitContainerControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridSummary)).BeginInit();
            this.ctxMenuSum.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.viewSummary)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartMain)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesLabel9)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesView9)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesLabel10)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesView10)).BeginInit();
            this.pageDetail.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.objLv)).BeginInit();
            this.ctxMenu.SuspendLayout();
            this.SuspendLayout();
            // 
            // tabCtrl
            // 
            this.tabCtrl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabCtrl.Location = new System.Drawing.Point(0, 0);
            this.tabCtrl.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.tabCtrl.Name = "tabCtrl";
            this.tabCtrl.SelectedTabPage = this.pageSummary;
            this.tabCtrl.Size = new System.Drawing.Size(1187, 478);
            this.tabCtrl.TabIndex = 3;
            this.tabCtrl.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.pageSummary,
            this.pageDetail});
            // 
            // pageSummary
            // 
            this.pageSummary.Controls.Add(this.splitContainerControl1);
            this.pageSummary.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.pageSummary.Name = "pageSummary";
            this.pageSummary.Size = new System.Drawing.Size(1180, 444);
            this.pageSummary.Text = "汇总信息";
            // 
            // splitContainerControl1
            // 
            this.splitContainerControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl1.FixedPanel = DevExpress.XtraEditors.SplitFixedPanel.None;
            this.splitContainerControl1.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl1.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.splitContainerControl1.Name = "splitContainerControl1";
            this.splitContainerControl1.Panel1.Controls.Add(this.gridSummary);
            this.splitContainerControl1.Panel1.Text = "Panel1";
            this.splitContainerControl1.Panel2.Controls.Add(this.chartMain);
            this.splitContainerControl1.Panel2.Text = "Panel2";
            this.splitContainerControl1.Size = new System.Drawing.Size(1180, 444);
            this.splitContainerControl1.SplitterPosition = 408;
            this.splitContainerControl1.TabIndex = 1;
            this.splitContainerControl1.Text = "splitContainerControl1";
            // 
            // gridSummary
            // 
            this.gridSummary.ContextMenuStrip = this.ctxMenuSum;
            this.gridSummary.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridSummary.EmbeddedNavigator.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.gridSummary.Location = new System.Drawing.Point(0, 0);
            this.gridSummary.MainView = this.viewSummary;
            this.gridSummary.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.gridSummary.Name = "gridSummary";
            this.gridSummary.Size = new System.Drawing.Size(408, 444);
            this.gridSummary.TabIndex = 0;
            this.gridSummary.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.viewSummary});
            // 
            // ctxMenuSum
            // 
            this.ctxMenuSum.ImageScalingSize = new System.Drawing.Size(20, 20);
            this.ctxMenuSum.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExportXlsSum});
            this.ctxMenuSum.Name = "ctxMenu";
            this.ctxMenuSum.Size = new System.Drawing.Size(164, 30);
            // 
            // miExportXlsSum
            // 
            this.miExportXlsSum.Name = "miExportXlsSum";
            this.miExportXlsSum.Size = new System.Drawing.Size(163, 26);
            this.miExportXlsSum.Text = "导出Excel...";
            this.miExportXlsSum.Click += new System.EventHandler(this.miExportXlsSum_Click);
            // 
            // viewSummary
            // 
            this.viewSummary.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.viewSummary.Appearance.HeaderPanel.TextOptions.Trimming = DevExpress.Utils.Trimming.None;
            this.viewSummary.Appearance.HeaderPanel.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.viewSummary.ColumnPanelRowHeight = 30;
            this.viewSummary.GridControl = this.gridSummary;
            this.viewSummary.Name = "viewSummary";
            this.viewSummary.OptionsBehavior.Editable = false;
            this.viewSummary.OptionsView.EnableAppearanceEvenRow = true;
            this.viewSummary.OptionsView.ShowGroupPanel = false;
            // 
            // chartMain
            // 
            this.chartMain.Dock = System.Windows.Forms.DockStyle.Fill;
            this.chartMain.EmptyChartText.Text = "无数据";
            this.chartMain.Location = new System.Drawing.Point(0, 0);
            this.chartMain.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.chartMain.Name = "chartMain";
            this.chartMain.PaletteName = "Metro";
            this.chartMain.RuntimeSelection = true;
            this.chartMain.RuntimeSeriesSelectionMode = DevExpress.XtraCharts.SeriesSelectionMode.Point;
            pieSeriesLabel9.Border.Visible = false;
            pieSeriesLabel9.LineVisible = true;
            series5.Label = pieSeriesLabel9;
            piePointOptions9.PercentOptions.PercentageAccuracy = 4;
            piePointOptions9.PointView = DevExpress.XtraCharts.PointView.Argument;
            piePointOptions9.ValueNumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
            piePointOptions9.ValueNumericOptions.Precision = 4;
            series5.LegendPointOptions = piePointOptions9;
            series5.Name = "Series 1";
            piePointOptions10.PercentOptions.PercentageAccuracy = 4;
            piePointOptions10.ValueNumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
            series5.PointOptions = piePointOptions10;
            series5.SeriesPointsSorting = DevExpress.XtraCharts.SortingMode.Descending;
            series5.SeriesPointsSortingKey = DevExpress.XtraCharts.SeriesPointKey.Value_1;
            series5.SynchronizePointOptions = false;
            pieSeriesView9.Border.Visible = false;
            pieSeriesView9.ExplodedDistancePercentage = 2D;
            pieSeriesView9.FillStyle.FillMode = DevExpress.XtraCharts.FillMode.Solid;
            pieSeriesView9.Rotation = 1;
            pieSeriesView9.RuntimeExploding = false;
            series5.View = pieSeriesView9;
            this.chartMain.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series5};
            pieSeriesLabel10.LineVisible = true;
            this.chartMain.SeriesTemplate.Label = pieSeriesLabel10;
            pieSeriesView10.RuntimeExploding = false;
            this.chartMain.SeriesTemplate.View = pieSeriesView10;
            this.chartMain.Size = new System.Drawing.Size(766, 444);
            this.chartMain.SmallChartText.Text = "请拉伸图表大小以便显示";
            this.chartMain.TabIndex = 1;
            chartTitle5.Alignment = System.Drawing.StringAlignment.Near;
            chartTitle5.Font = new System.Drawing.Font("宋体", 16F);
            chartTitle5.Text = "原因占比";
            this.chartMain.Titles.AddRange(new DevExpress.XtraCharts.ChartTitle[] {
            chartTitle5});
            // 
            // pageDetail
            // 
            this.pageDetail.Controls.Add(this.objLv);
            this.pageDetail.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.pageDetail.Name = "pageDetail";
            this.pageDetail.Size = new System.Drawing.Size(1180, 444);
            this.pageDetail.Text = "详细信息";
            // 
            // objLv
            // 
            this.objLv.AllColumns.Add(this.colSN);
            this.objLv.AllColumns.Add(this.colFileName);
            this.objLv.AllColumns.Add(this.colMoMt);
            this.objLv.AllColumns.Add(this.colIsBlock);
            this.objLv.AllColumns.Add(this.colCause);
            this.objLv.AllColumns.Add(this.colLng);
            this.objLv.AllColumns.Add(this.colLat);
            this.objLv.AllColumns.Add(this.colTime);
            this.objLv.AllColumns.Add(this.colIMSErrorMsg);
            this.objLv.AllColumns.Add(this.colRsrp);
            this.objLv.AllColumns.Add(this.colSinr);
            this.objLv.AllColumns.Add(this.colMultiCvrPer);
            this.objLv.AllColumns.Add(this.colHoNum);
            this.objLv.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.colSN,
            this.colFileName,
            this.colMoMt,
            this.colIsBlock,
            this.colCause,
            this.colLng,
            this.colLat,
            this.colTime,
            this.colIMSErrorMsg,
            this.colRsrp,
            this.colSinr,
            this.colMultiCvrPer,
            this.colHoNum});
            this.objLv.ContextMenuStrip = this.ctxMenu;
            this.objLv.Cursor = System.Windows.Forms.Cursors.Default;
            this.objLv.Dock = System.Windows.Forms.DockStyle.Fill;
            this.objLv.FullRowSelect = true;
            this.objLv.GridLines = true;
            this.objLv.HeaderWordWrap = true;
            this.objLv.Location = new System.Drawing.Point(0, 0);
            this.objLv.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.objLv.Name = "objLv";
            this.objLv.OwnerDraw = true;
            this.objLv.ShowGroups = false;
            this.objLv.Size = new System.Drawing.Size(1180, 444);
            this.objLv.TabIndex = 4;
            this.objLv.UseCompatibleStateImageBehavior = false;
            this.objLv.View = System.Windows.Forms.View.Details;
            this.objLv.VirtualMode = true;
            this.objLv.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.objLv_MouseDoubleClick);
            this.objLv.MouseDown += new System.Windows.Forms.MouseEventHandler(this.objLv_MouseDown);
            // 
            // colSN
            // 
            this.colSN.HeaderFont = null;
            this.colSN.Text = "序号";
            // 
            // colFileName
            // 
            this.colFileName.HeaderFont = null;
            this.colFileName.MinimumWidth = 300;
            this.colFileName.Text = "文件名";
            this.colFileName.Width = 300;
            // 
            // colMoMt
            // 
            this.colMoMt.HeaderFont = null;
            this.colMoMt.Text = "主叫/被叫";
            // 
            // colIsBlock
            // 
            this.colIsBlock.HeaderFont = null;
            this.colIsBlock.Text = "是否未接通";
            // 
            // colCause
            // 
            this.colCause.HeaderFont = null;
            this.colCause.Text = "未接通原因";
            // 
            // colLng
            // 
            this.colLng.HeaderFont = null;
            this.colLng.MinimumWidth = 80;
            this.colLng.Text = "未接通经度";
            this.colLng.Width = 80;
            // 
            // colLat
            // 
            this.colLat.HeaderFont = null;
            this.colLat.MinimumWidth = 80;
            this.colLat.Text = "未接通纬度";
            this.colLat.Width = 80;
            // 
            // colTime
            // 
            this.colTime.HeaderFont = null;
            this.colTime.MinimumWidth = 120;
            this.colTime.Text = "未接通时间";
            this.colTime.Width = 120;
            // 
            // colIMSErrorMsg
            // 
            this.colIMSErrorMsg.HeaderFont = null;
            this.colIMSErrorMsg.Text = "异常信令";
            // 
            // colRsrp
            // 
            this.colRsrp.HeaderFont = null;
            this.colRsrp.Text = "未接通前x秒平均RSRP";
            // 
            // colSinr
            // 
            this.colSinr.HeaderFont = null;
            this.colSinr.Text = "未接通前x秒平均SINR";
            // 
            // colMultiCvrPer
            // 
            this.colMultiCvrPer.HeaderFont = null;
            this.colMultiCvrPer.Text = "高重叠覆盖占比(%)";
            // 
            // colHoNum
            // 
            this.colHoNum.HeaderFont = null;
            this.colHoNum.Text = "未接通前x秒切换次数";
            // 
            // ctxMenu
            // 
            this.ctxMenu.ImageScalingSize = new System.Drawing.Size(20, 20);
            this.ctxMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExpandAll,
            this.miCollapseAll,
            this.toolStripSeparator1,
            this.miExportXls,
            this.toolStripSeparator2,
            this.miReplayFile,
            this.miReplayEvent,
            this.miCompareReplayEvent});
            this.ctxMenu.Name = "ctxMenu";
            this.ctxMenu.Size = new System.Drawing.Size(175, 172);
            this.ctxMenu.Closed += new System.Windows.Forms.ToolStripDropDownClosedEventHandler(this.ctxMenu_Closed);
            // 
            // miExpandAll
            // 
            this.miExpandAll.Name = "miExpandAll";
            this.miExpandAll.Size = new System.Drawing.Size(181, 26);
            this.miExpandAll.Text = "展开所有节点";
            this.miExpandAll.Click += new System.EventHandler(this.miExpandAll_Click);
            // 
            // miCollapseAll
            // 
            this.miCollapseAll.Name = "miCollapseAll";
            this.miCollapseAll.Size = new System.Drawing.Size(181, 26);
            this.miCollapseAll.Text = "收缩所有节点";
            this.miCollapseAll.Click += new System.EventHandler(this.miCollapseAll_Click);
            // 
            // toolStripSeparator1
            // 
            this.toolStripSeparator1.Name = "toolStripSeparator1";
            this.toolStripSeparator1.Size = new System.Drawing.Size(178, 6);
            // 
            // miExportXls
            // 
            this.miExportXls.Name = "miExportXls";
            this.miExportXls.Size = new System.Drawing.Size(181, 26);
            this.miExportXls.Text = "导出Excel...";
            this.miExportXls.Click += new System.EventHandler(this.miExportXls_Click);
            // 
            // toolStripSeparator2
            // 
            this.toolStripSeparator2.Name = "toolStripSeparator2";
            this.toolStripSeparator2.Size = new System.Drawing.Size(178, 6);
            // 
            // miReplayFile
            // 
            this.miReplayFile.Name = "miReplayFile";
            this.miReplayFile.Size = new System.Drawing.Size(181, 26);
            this.miReplayFile.Text = "回放所属文件";
            this.miReplayFile.Click += new System.EventHandler(this.miReplayFile_Click);
            // 
            // miReplayEvent
            // 
            this.miReplayEvent.Name = "miReplayEvent";
            this.miReplayEvent.Size = new System.Drawing.Size(181, 26);
            this.miReplayEvent.Text = "回放事件";
            this.miReplayEvent.Click += new System.EventHandler(this.miReplayEvent_Click);
            // 
            // miCompareReplayEvent
            // 
            this.miCompareReplayEvent.Name = "miCompareReplayEvent";
            this.miCompareReplayEvent.Size = new System.Drawing.Size(181, 26);
            this.miCompareReplayEvent.Text = "对比回放事件";
            this.miCompareReplayEvent.Click += new System.EventHandler(this.miCompareReplayEvent_Click);
            // 
            // BlockCallCauseForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(8F, 18F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1187, 478);
            this.Controls.Add(this.tabCtrl);
            this.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.Name = "BlockCallCauseForm";
            this.Text = "VoLTE未接通原因分析";
            ((System.ComponentModel.ISupportInitialize)(this.tabCtrl)).EndInit();
            this.tabCtrl.ResumeLayout(false);
            this.pageSummary.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).EndInit();
            this.splitContainerControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridSummary)).EndInit();
            this.ctxMenuSum.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.viewSummary)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesLabel9)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesView9)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesLabel10)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesView10)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartMain)).EndInit();
            this.pageDetail.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.objLv)).EndInit();
            this.ctxMenu.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraTab.XtraTabControl tabCtrl;
        private DevExpress.XtraTab.XtraTabPage pageSummary;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl1;
        private DevExpress.XtraGrid.GridControl gridSummary;
        private DevExpress.XtraGrid.Views.Grid.GridView viewSummary;
        private DevExpress.XtraCharts.ChartControl chartMain;
        private DevExpress.XtraTab.XtraTabPage pageDetail;
        private BrightIdeasSoftware.TreeListView objLv;
        private BrightIdeasSoftware.OLVColumn colSN;
        private BrightIdeasSoftware.OLVColumn colFileName;
        private BrightIdeasSoftware.OLVColumn colMoMt;
        private BrightIdeasSoftware.OLVColumn colIsBlock;
        private BrightIdeasSoftware.OLVColumn colLng;
        private BrightIdeasSoftware.OLVColumn colLat;
        private BrightIdeasSoftware.OLVColumn colTime;
        private BrightIdeasSoftware.OLVColumn colRsrp;
        private BrightIdeasSoftware.OLVColumn colSinr;
        private BrightIdeasSoftware.OLVColumn colHoNum;
        private BrightIdeasSoftware.OLVColumn colCause;
        private BrightIdeasSoftware.OLVColumn colMultiCvrPer;
        private System.Windows.Forms.ContextMenuStrip ctxMenu;
        private System.Windows.Forms.ToolStripMenuItem miExpandAll;
        private System.Windows.Forms.ToolStripMenuItem miCollapseAll;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator1;
        private System.Windows.Forms.ToolStripMenuItem miExportXls;
        private System.Windows.Forms.ContextMenuStrip ctxMenuSum;
        private System.Windows.Forms.ToolStripMenuItem miExportXlsSum;
        private BrightIdeasSoftware.OLVColumn colIMSErrorMsg;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator2;
        private System.Windows.Forms.ToolStripMenuItem miReplayFile;
        private System.Windows.Forms.ToolStripMenuItem miReplayEvent;
        private System.Windows.Forms.ToolStripMenuItem miCompareReplayEvent;
    }
}