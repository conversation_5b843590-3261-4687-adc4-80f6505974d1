﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.Func.PopShow
{
    public partial class StreetInjInfoPanel : UserControl, PopShowPanelInterface
    {
        private MainModel mm;
        public StreetInjInfoPanel()
        {
            InitializeComponent();
        }

        #region PopShowPanelInterface 成员

        public void RunQuery(BackgroundWorker worker,TaskInfo task)
        {
            System.Threading.Thread.Sleep(2000);
        }

        #endregion

        #region PopShowPanelInterface 成员


        public void FireFreshShowData(TaskInfo task)
        {
            //
        }

        #endregion

        #region PopShowPanelInterface 成员


        public void SetMainModal(MasterCom.RAMS.Model.MainModel mm)
        {
            this.mm = mm;
        }

        #endregion

        #region PopShowPanelInterface 成员


        public void SetMainModal(MainModel mm, WelcomForm welcomform)
        {
            //
        }

        #endregion
    }
}
