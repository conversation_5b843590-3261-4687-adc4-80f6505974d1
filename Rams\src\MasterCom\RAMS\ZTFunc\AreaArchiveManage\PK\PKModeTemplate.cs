﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.Util;
using MasterCom.RAMS.Chris.Util;
using System.Drawing;

namespace MasterCom.RAMS.ZTFunc.AreaArchiveManage
{
    public class PKModeTemplateMngr
    {
        public static readonly string PATH = System.Windows.Forms.Application.StartupPath + "\\config\\AreaArchive\\PKTemplate\\";
        public static readonly string PATH_NEW = System.Windows.Forms.Application.StartupPath + "\\config\\3PartPKTemplate\\";
        private static PKModeTemplateMngr instance = null;
        private PKModeTemplateMngr()
        {
            Init();
        }

        public void Init()
        {
            Templates = new List<PKModeTemplate>();
            if (System.IO.Directory.Exists(PATH))
            {
                if (!System.IO.Directory.Exists(PATH_NEW))
                {
                    System.IO.Directory.CreateDirectory(PATH_NEW);
                }
                foreach (string filePath in System.IO.Directory.GetFiles(PATH))
                {
                    System.IO.FileInfo fi = new System.IO.FileInfo(filePath);

                    System.IO.File.Move(filePath, PATH_NEW + fi.Name);
                }
                System.IO.Directory.Delete(PATH);
            }
            try
            {
                System.IO.DirectoryInfo directory = new System.IO.DirectoryInfo(PATH_NEW);
                if (!directory.Exists)
                {
                    return;
                }
                System.IO.FileInfo[] files = directory.GetFiles("*.xml", System.IO.SearchOption.TopDirectoryOnly);
                if (files.Length > 0)
                {
                    foreach (System.IO.FileInfo file in files)
                    {
                        XmlConfigFile configFile = new XmlConfigFile(file.FullName);
                        Dictionary<string, object> dic = configFile.GetItemValue("Template", "Options") as Dictionary<string, object>;
                        if (dic == null)
                        {
                            continue;
                        }
                        PKModeTemplate rptstyle = new PKModeTemplate();
                        rptstyle.Param = dic;
                        Templates.Add(rptstyle);
                    }
                }
            }
            catch (Exception e)
            {
                System.Windows.Forms.MessageBox.Show("加载档案模板异常。" + e.Message);
            }
        }

        public static PKModeTemplateMngr Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new PKModeTemplateMngr();
                }
                return instance;
            }
        }

        public List<PKModeTemplate> Templates
        {
            get;
            set;
        }

        internal void Save()
        {
            foreach (PKModeTemplate item in Templates)
            {
                item.Save();
            }
        }

        internal void Delete(PKModeTemplate curTemplate)
        {
            curTemplate.Delete();
            this.Templates.Remove(curTemplate);
        }
    }

    public class PKModeTemplate
    {
        public string Name { get; set; }

        public PKHub CMHub { get; set; }

        public PKHub CUHub { get; set; }

        public PKHub CTHub { get; set; }

        public List<Alghirithm> AlghirithmVec { get; set; }

        public Alghirithm OtherAlghirithm { get; set; }

        public PKModeTemplate()
        {
            CMHub = new PKHub(ECarrier.移动);
            CUHub = new PKHub(ECarrier.联通);
            CTHub = new PKHub(ECarrier.电信);

            AlghirithmVec = new List<Alghirithm>();
            OtherAlghirithm = new Alghirithm();
            OtherAlghirithm.Name = "其他";
            OtherAlghirithm.Color = Color.Cyan;

            addSpecial();
        }

        private void addSpecial()
        {
            /**
            Alghirithm special = new Alghirithm();
            special.Name = "三网都没测";
            special.SetAttribute(true, ESpecialDesc.无数据, ESpecialDesc.无数据, ESpecialDesc.无数据);
            special.Color = Color.Red;
            AlghirithmVec.Add(special);

            special = new Alghirithm();
            special.Name = "移动和联通未测，电信有测";
            special.SetAttribute(true, ESpecialDesc.无数据, ESpecialDesc.无数据, ESpecialDesc.有数据);
            special.Color = Color.Pink;
            AlghirithmVec.Add(special);

            special = new Alghirithm();
            special.Name = "移动和电信未测，联通有测";
            special.SetAttribute(true, ESpecialDesc.无数据, ESpecialDesc.有数据, ESpecialDesc.无数据);
            special.Color = Color.Orchid;
            AlghirithmVec.Add(special);

            special = new Alghirithm();
            special.Name = "移动未测，联通和电信有测";
            special.SetAttribute(true, ESpecialDesc.无数据, ESpecialDesc.有数据, ESpecialDesc.有数据);
            special.Color = Color.Orange;
            AlghirithmVec.Add(special);

            special = new Alghirithm();
            special.Name = "移动有测，联通和电信未测";
            special.SetAttribute(true, ESpecialDesc.有数据, ESpecialDesc.无数据, ESpecialDesc.无数据);
            special.Color = Color.Purple;
            AlghirithmVec.Add(special);

            special = new Alghirithm();
            special.Name = "移动和电信有测，联通未测";
            special.SetAttribute(true, ESpecialDesc.有数据, ESpecialDesc.无数据, ESpecialDesc.有数据);
            special.Color = Color.Gray;
            AlghirithmVec.Add(special);

            special = new Alghirithm();
            special.Name = "移动和联通有测，电信未测";
            special.SetAttribute(true, ESpecialDesc.有数据, ESpecialDesc.有数据, ESpecialDesc.无数据);
            special.Color = Color.Blue;
            AlghirithmVec.Add(special);
            *///
        }

        public double CalcFormula(AreaKPIDataGroup<AreaBase> kpiGroup, ECarrier car)
        {
            switch (car)
            {
                case ECarrier.移动:
                    return kpiGroup.CalcFormula((MasterCom.RAMS.Model.CarrierType)car, 0, CMHub.PkBase.FormulaExp);
                case ECarrier.联通:
                    return kpiGroup.CalcFormula((MasterCom.RAMS.Model.CarrierType)car, 0, CUHub.PkBase.FormulaExp);
                case ECarrier.电信:
                    return kpiGroup.CalcFormula((MasterCom.RAMS.Model.CarrierType)car, 0, CTHub.PkBase.FormulaExp);
                default:
                    break;
            }
            return double.NaN;
        }

        public Alghirithm GetAlghirithm(double dcm, double dcu, double dct)
        {
            if (!CMHub.Contain(dcm) || dcm == 0)
                dcm = double.NaN;
            if (!CUHub.Contain(dcu) || dcu == 0)
                dcu = double.NaN;
            if (!CTHub.Contain(dct) || dct == 0)
                dct = double.NaN;

            foreach (Alghirithm thm in AlghirithmVec)
            {
                if (!thm.IsSpecial && thm.IsBelong(dcm, dcu, dct))
                    return thm;
            }
            foreach (Alghirithm thm in AlghirithmVec)
            {
                if (thm.IsSpecial && thm.IsBelong(dcm, dcu, dct))
                    return thm;
            }
            return OtherAlghirithm;
        }

        public bool IsValid()
        {
            return CMHub.IsValid() && CUHub.IsValid() && CTHub.IsValid() && AlghirithmVec.Count > 0;
        }

        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> rtDic = new Dictionary<string, object>();
                Dictionary<string, object> dic = new Dictionary<string, object>();
                rtDic["Name"] = Name;
                dic[CMHub.Name] = CMHub.Param;
                dic[CUHub.Name] = CUHub.Param;
                dic[CTHub.Name] = CTHub.Param;

                rtDic["HubContext"] = dic;

                List<object> objSet = new List<object>();
                foreach (Alghirithm item in AlghirithmVec)
                {
                    objSet.Add(item.Param);
                }
                rtDic["AlghirithmVec"] = objSet;
                rtDic["OtherAlghirithm"] = OtherAlghirithm.Param;
                return rtDic;
            }
            set
            {
                Dictionary<string, object> dic = value;
                Name = Convert.ToString(dic["Name"]);

                Dictionary<string, object> hubContextDic = value["HubContext"] as Dictionary<string, object>;
                foreach (string hub in hubContextDic.Keys)
                {
                    ECarrier car = (ECarrier)Enum.Parse(typeof(ECarrier), hub);
                    
                    switch (car)
                    {
                        case ECarrier.移动:
                            CMHub.Param = hubContextDic[hub] as Dictionary<string, object>;
                            break;
                        case ECarrier.联通:
                            CUHub.Param = hubContextDic[hub] as Dictionary<string, object>;
                            break;
                        case ECarrier.电信:
                            CTHub.Param = hubContextDic[hub] as Dictionary<string, object>;
                            break;
                        default:
                            break;
                    }
                }

                List<object> oAlghirithm = value["AlghirithmVec"] as List<object>;
                AlghirithmVec.Clear();
                foreach (object oAl in oAlghirithm)
                {
                    Alghirithm athm = new Alghirithm();
                    athm.Param = oAl as Dictionary<string, object>;
                    AlghirithmVec.Add(athm);
                }

                if (value.ContainsKey("OtherAlghirithm"))
                    OtherAlghirithm.Param = value["OtherAlghirithm"] as Dictionary<string, object>;
            }
        }

        public void Save()
        {
            XmlConfigFile configFile = new XmlConfigFile();
            System.Xml.XmlElement cfg = configFile.AddConfig("Template");
            configFile.AddItem(cfg, "Options", this.Param);

            configFile.Save(PKModeTemplateMngr.PATH_NEW + Name + ".xml");
        }

        internal void Delete()
        {
            if (System.IO.File.Exists(PKModeTemplateMngr.PATH_NEW + Name + ".xml"))
            {
                System.IO.File.Delete(PKModeTemplateMngr.PATH_NEW + Name + ".xml");
            }
        }

        public override string ToString()
        {
            return Name;
        }
    }

    public class PKHub
    {
        public ECarrier Carrier { get; set; }

        public string Name { get { return Carrier.ToString(); } }

        public PkBaseKpi PkBase { get; set; }

        public PKHub(ECarrier car)
        {
            Carrier = car;
            PkBase = new PkBaseKpi();
        }

        public bool IsValid()
        {
            return PkBase.IsValid();
        }

        public bool Contain(double dValue)
        {
            return PkBase.Contain(dValue);
        }

        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> dic = new Dictionary<string, object>();
                dic["Name"] = Name;
                dic["ServVec"] = PkBase.ServVec;
                dic["FormulaName"] = PkBase.FormulaName;
                dic["FormulaExp"] = PkBase.FormulaExp;
                dic["ValueRange"] = PkBase.ValueRange.Param;
                return dic;
            }
            set
            {
                Dictionary<string, object> dic = value;
                PkBase.Param = dic;
            }
        }

        public override string ToString()
        {
            return Name;
        }
    }

    public class Alghirithm
    {
        public string Name { get; set; }

        public Color Color { get; set; }

        public List<ValueRange> ValueRangeVec { get; set; }

        public bool IsSpecial { get; set; }

        public ESpecialDesc ENaNCM { get; set; }

        public ESpecialDesc ENaNCU { get; set; }

        public ESpecialDesc ENaNCT { get; set; }

        public Alghirithm()
        {
            ValueRangeVec = new List<ValueRange>();
            IsSpecial = false;
            ENaNCM = ENaNCU = ENaNCT = ESpecialDesc.忽略;
        }

        public string StrDesc
        {
            get
            {
                StringBuilder sb = new StringBuilder();
                string and = " and ";
                if (IsSpecial)
                {
                    sb.Append(string.Format("移动{0} and 联通{1} and 电信{2}",
                        ENaNCM,
                        ENaNCU,
                        ENaNCT));
                }
                else
                {
                    foreach (ValueRange vr in ValueRangeVec)
                    {
                        sb.Append(vr.StrDesc);
                        sb.Append(and);
                    }
                }
                return sb.ToString().TrimEnd(and.ToCharArray());
            }
        }

        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> dic = new Dictionary<string, object>();
                dic["Name"] = Name;
                dic["IsSpecial"] = IsSpecial;
                dic["ENaNCM"] = ENaNCM.ToString();
                dic["ENaNCU"] = ENaNCU.ToString();
                dic["ENaNCT"] = ENaNCT.ToString();
                dic["Color"] = this.Color.ToArgb();
                List<object> valueVec = new List<object>();
                foreach (ValueRange vr in ValueRangeVec)
                {
                    valueVec.Add(vr.Param);
                }
                dic["ValueRangeVec"] = valueVec;
                return dic;
            }
            set
            {
                Dictionary<string, object> dic = value;
                Name = Convert.ToString(dic["Name"]);
                IsSpecial = Convert.ToBoolean(dic["IsSpecial"]);

                //模版改变，做兼容处理
                if (dic.ContainsKey("ENaNCM") && dic.ContainsKey("ENaNCU") && dic.ContainsKey("ENaNCT"))
                {
                    ENaNCM = (ESpecialDesc)Enum.Parse(typeof(ESpecialDesc), dic["ENaNCM"].ToString());
                    ENaNCU = (ESpecialDesc)Enum.Parse(typeof(ESpecialDesc), dic["ENaNCU"].ToString());
                    ENaNCT = (ESpecialDesc)Enum.Parse(typeof(ESpecialDesc), dic["ENaNCT"].ToString());
                }
                else if (dic.ContainsKey("IsNaNCM") && dic.ContainsKey("IsNaNCU") && dic.ContainsKey("IsNaNCT"))
                {
                    ENaNCM = Convert.ToBoolean(dic["IsNaNCM"]) ? ESpecialDesc.无数据 : ESpecialDesc.有数据;
                    ENaNCU = Convert.ToBoolean(dic["IsNaNCU"]) ? ESpecialDesc.无数据 : ESpecialDesc.有数据;
                    ENaNCT = Convert.ToBoolean(dic["IsNaNCT"]) ? ESpecialDesc.无数据 : ESpecialDesc.有数据;
                }

                Color = Color.FromArgb((int)value["Color"]);

                List<object> valueVec = dic["ValueRangeVec"] as List<object>;
                ValueRangeVec.Clear();
                foreach (object ov in valueVec)
                {
                    ValueRange vr = new ValueRange();
                    vr.Param = ov as Dictionary<string, object>;
                    ValueRangeVec.Add(vr);
                }
            }
        }

        public void SetAttribute(bool isSpecial, ESpecialDesc eNaNCM, ESpecialDesc eNaNCU, ESpecialDesc eNaNCT)
        {
            this.IsSpecial = isSpecial;
            this.ENaNCM = eNaNCM;
            this.ENaNCU = eNaNCU;
            this.ENaNCT = eNaNCT;
        }

        public bool IsBelong(double dcm, double dcu, double dct)
        {
            if (IsSpecial)
            {
                return isbelong(dcm, ENaNCM) && isbelong(dcu, ENaNCU) && isbelong(dct, ENaNCT);
            }
            else
            {
                Dictionary<ECarrier, double> carValueDic = new Dictionary<ECarrier, double>();
                carValueDic[ECarrier.移动] = dcm;
                carValueDic[ECarrier.联通] = dcu;
                carValueDic[ECarrier.电信] = dct;
                foreach (ValueRange vr in ValueRangeVec)
                {
                    if (!vr.Contain(carValueDic[vr.HostCarrier], carValueDic[vr.GuestCarrier]))
                        return false;
                }
            }
            return true;
        }

        private bool isbelong(double d, ESpecialDesc e)
        {
            return e == ESpecialDesc.忽略 || double.IsNaN(d) == (e == ESpecialDesc.无数据);
        }
    }

    public class ValueRange
    {
        public ECarrier HostCarrier { get; set; }

        public ECarrier GuestCarrier { get; set; }

        public Range Range { get; set; }

        public string StrDesc
        {
            get
            {
                return string.Format("{0} - {1} ∈ {2}", HostCarrier, GuestCarrier, Range.ToString());
            }
        }

        public bool Contain(double dHost, double dGuest)
        {
            if (double.IsNaN(dHost) ||
                double.IsNaN(dGuest))
            {
                return false;
            }
            return Range.Contains(dHost - dGuest);
        }

        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> dic = new Dictionary<string, object>();
                dic["HostCarrier"] = this.HostCarrier.ToString();
                dic["GuestCarrier"] = this.GuestCarrier.ToString();
                dic["Range"] = Range.Param;
                return dic;
            }
            set
            {
                if (value == null)
                {
                    return;
                }
                this.HostCarrier = (ECarrier)Enum.Parse(typeof(ECarrier), Convert.ToString(value["HostCarrier"]));
                this.GuestCarrier = (ECarrier)Enum.Parse(typeof(ECarrier), Convert.ToString(value["GuestCarrier"]));
                this.Range = new Range(0, false, 100, true);
                this.Range.Param = (Dictionary<string, object>)value["Range"];
            }
        }

        public override string ToString()
        {
            return StrDesc;
        }
    }
}
