﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.ZTFunc.ZTLTESINR;

namespace MasterCom.RAMS.ZTFunc
{
    public class LTESINRQueryByRegion :LTESINRQuery
    {
        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.Region;
        }
        private static LTESINRQueryByRegion instance;
        public static LTESINRQueryByRegion Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new LTESINRQueryByRegion(MainModel.GetInstance());
                }
                return instance;
            }
        }
        public LTESINRQueryByRegion(MainModel mModel)
            : base(mModel)
        {
            this.FilterEventByRegion = false;
            this.IncludeEvent = false;
            analyzer = new LTESINRAnalyzer();
        }
        public override string Name
        {
            get { return "SINR与RSRP关联(按区域)"; }
        }
        /// <summary>
        /// 判断是否在所选区域内
        /// </summary>
        /// <param name="testPoint"></param>
        /// <returns></returns>
        /// <summary>
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            try
            {
                if (testPoint is LTETestPointDetail)
                {
                    return Condition.Geometorys.GeoOp.CheckPointInRegion(testPoint.Longitude, testPoint.Latitude);
                }
                return false;
            }
            catch
            {
                return false;
            }
        }
    }

    public class LTESINRQueryByRegion_FDD : LTESINRQueryByRegion
    {
        private static LTESINRQueryByRegion_FDD instance = null;
        private static object obj = new object();
        public static LTESINRQueryByRegion_FDD GetInstance()
        {
            if (instance == null)
            {
                lock (obj)
                {
                    if (instance == null)
                    {
                        instance = new LTESINRQueryByRegion_FDD(MainModel.GetInstance());
                    }
                }
            }
            return instance;
        }

        public LTESINRQueryByRegion_FDD(MainModel mainModel)
            : base(mainModel)
        {

        }

        public override string Name
        {
            get { return "SINR与RSRP关联_LTE_FDD(按区域)"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 26000, 26026, this.Name);
        }
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            try
            {
                if (testPoint is LTEFddTestPoint)
                {
                    return Condition.Geometorys.GeoOp.CheckPointInRegion(testPoint.Longitude, testPoint.Latitude);
                }
                return false;
            }
            catch
            {
                return false;
            }
        }
    }
}
