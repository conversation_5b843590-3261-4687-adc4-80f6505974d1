﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using AxMapWinGIS;
using MapWinGIS;
using MasterCom.MTGis;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc.ImportCells
{
    public class ImportedCellLayer : LayerBase
    {
        private List<Cell_Import> listCell = null;
        public ImportedCellLayer()
            : base("导入小区图层")
        {
            this.listCell = new List<Cell_Import>();
            this.Fix = true;
        }

        public override void Draw(Rectangle clientRect, Rectangle updateRect, Graphics graphics)
        {
            SolidBrush bushSiteCircle = new SolidBrush(Color.Green);
            SolidBrush bushRec = new SolidBrush(Color.White);
            SolidBrush brushString = new SolidBrush(Color.Black);
            SolidBrush brush = new SolidBrush(Color.Blue);
            Pen penLine = new Pen(brush, 2);
            Font fontLabel = new Font(new Font("宋体", 9.0f), FontStyle.Bold);

            graphics.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.HighQuality;

            int arrowLength = 10;
            int arrowHalfWidth = 1;
            PointF[] pointsDir = new PointF[] 
                                                                {
                                                                    new PointF(0, -arrowHalfWidth), 
                                                                    new PointF(arrowLength - 3, -arrowHalfWidth), 
                                                                    new PointF(arrowLength - 4, -(arrowHalfWidth+2)), 
                                                                    new PointF(arrowLength+2, 0), 
                                                                    new PointF(arrowLength - 4, arrowHalfWidth+2), 
                                                                    new PointF(arrowLength - 3, arrowHalfWidth), 
                                                                    new PointF(0, arrowHalfWidth), 
                                                                };
            System.Drawing.Drawing2D.GraphicsPath gPath = new System.Drawing.Drawing2D.GraphicsPath();
            gPath.AddPolygon(pointsDir);
            Size sizeCell = new Size(4, 4);
            List<Cell_Import> listDrawCell = new List<Cell_Import>();
            DbRect dRect;
            this.gisAdapter.FromDisplay(updateRect, out dRect);
            foreach (Cell_Import c in this.listCell)
            {
                if (!dRect.IsPointInThisRect(c.Lon, c.Lat)) continue;
                listDrawCell.Add(c);
            }
            foreach (Cell_Import cell in listDrawCell)
            {
                DbPoint dPoint = new DbPoint(cell.Lon, cell.Lat);
                PointF point;
                this.gisAdapter.ToDisplay(dPoint, out point);
                graphics.TranslateTransform(point.X, point.Y);//把小区坐标当作坐标原点
                float scal = (float)(300.0 / this.gisAdapter.DistancePer50Pixel);
                float maxScal = 10.0f;
                scal = scal > maxScal ? maxScal : scal;
                graphics.ScaleTransform(scal, scal);

                graphics.DrawEllipse(penLine, new Rectangle(new System.Drawing.Point((-sizeCell.Width / 2), (-sizeCell.Height / 2)), sizeCell));
                
                graphics.RotateTransform((float)(cell.Direction - 90));//方向角的计算，以-90°为坐标0°，故要选中旋转坐标为方向角度-90

                graphics.FillPath(bushSiteCircle, gPath);

                graphics.ResetTransform();//还原坐标原点
                //draw label
                if (this.gisAdapter.DistancePer50Pixel <= 664.91)
                { 
                    graphics.TranslateTransform(point.X, point.Y);//把小区坐标当作坐标原点
                    int x = (int)(Math.Cos((cell.Direction - 90) * Math.PI / 180) * arrowLength * scal + 1);
                    int y = (int)(Math.Sin((cell.Direction - 90) * Math.PI / 180) * arrowLength * scal + 1);
                    SizeF sizeLabel = graphics.MeasureString(cell.CellName, fontLabel);
                    Rectangle rec = new Rectangle(x, y, (int)sizeLabel.Width + 2, (int)sizeLabel.Height + 2);
                    graphics.FillRectangle(bushRec, rec);
                    graphics.DrawString(cell.CellName, fontLabel, brushString, x+1, y+1);

                    graphics.ResetTransform();//还原坐标原点
                }
            }
        }

        public void FillCells(List<Cell_Import> listCell)
        {
            this.listCell = listCell;
        }
    }
}

