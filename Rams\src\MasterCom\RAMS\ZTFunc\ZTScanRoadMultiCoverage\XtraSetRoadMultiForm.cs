﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class XtraSetRoadMultiForm : BaseDialog
    {
        private CheckEdit ceFreq = new CheckEdit();
        private ComboBoxEdit cbeFreq = new ComboBoxEdit();

        public XtraSetRoadMultiForm(string type)
        {
            InitializeComponent();
            ceFreq.Visible = false;
            cbeFreq.Visible = false;
            cbxCoFreqType.Properties.Items.Clear();
            this.grpNoneMainPnt.Visible = false;
            this.Height = 264;
            if (type.Equals("GSM"))
            {
                numDiffMin.Enabled = true;
                checkBandEditDiff.Enabled = true;

                cbxCoFreqType.Properties.Items.Add("BCCH 或 TCH");
                cbxCoFreqType.Properties.Items.Add("BCCH Only");
                cbxCoFreqType.Properties.Items.Add("TCH Only");
                numRxLevDValue.Value = 12;
                numRxLevThreshold.Value = -80;
                this.Height = 400;
                this.grpNoneMainPnt.Visible = true;
            }
            else if (type.Equals("TD"))
            {
                grpNoneMainPnt.Enabled = false;
                numDiffMin.Enabled = false;
                cbxCoFreqType.Properties.Items.Add("ARFCN 或 ARFCNList");
                cbxCoFreqType.Properties.Items.Add("ARFCN Only");
                cbxCoFreqType.Properties.Items.Add("ARFCNList Only");
                numRxLevDValue.Value = 6;
                numRxLevThreshold.Value = -80;
            }
            else if (type.Equals("TDLTE"))
            {
                grpNoneMainPnt.Enabled = false;
                numDiffMin.Enabled = false;
                cbxCoFreqType.Properties.Items.Add("EARFCN 或 EARFCNListOnly");
                cbxCoFreqType.Properties.Items.Add("EARFCN Only");
                cbxCoFreqType.Properties.Items.Add("EARFCNList Only");
                numRxLevDValue.Value = 6;
                numRxLevThreshold.Value = -105;
                addFreq();
            }
            else if (type.Equals("WCDMA"))
            {
                grpNoneMainPnt.Enabled = false;
                numDiffMin.Enabled = false;
                cbxCoFreqType.Properties.Items.Add("UARFCN 或 UARFCNListOnly");
                cbxCoFreqType.Properties.Items.Add("UARFCN Only");
                cbxCoFreqType.Properties.Items.Add("UARFCNList Only");
                numRxLevDValue.Value = 6;
                numRxLevThreshold.Value = -80;
            }
            else if (type.Equals("NR"))
            {
                grpNoneMainPnt.Enabled = false;
                numDiffMin.Enabled = false;
                cbxCoFreqType.Properties.Items.Add("ARFCN 或 ARFCNListOnly");
                cbxCoFreqType.Properties.Items.Add("ARFCN Only");
                cbxCoFreqType.Properties.Items.Add("ARFCNList Only");
                numRxLevDValue.Value = 6;
                numRxLevThreshold.Value = -105;
            }

            if (cbxCoFreqType.Properties.Items.Count > 1)
            {
                cbxCoFreqType.SelectedIndex = 1;
            }
            chkCoFreq_CheckedChanged(null, null);
        }

        private void addFreq()
        {
            ceFreq.Parent = this;
            ceFreq.Visible = true;
            ceFreq.Checked = false;
            ceFreq.Location = new Point(31, 175);
            ceFreq.Size = new Size(57, 19);
            ceFreq.Text = "频段";
            ceFreq.CheckedChanged += new EventHandler(ceFreq_CheckedChanged);

            cbeFreq.Parent = this;
            cbeFreq.Visible = true;
            cbeFreq.Location = new Point(94, 175);
            cbeFreq.Size = new Size(102, 20);
            cbeFreq.Properties.Items.Clear();

            #region E
            FreqInfo freqE1 = new FreqInfo() { FreqBandName = "频点E1", Type = 0, Value = 38950 };
            FreqInfo freqE2 = new FreqInfo() { FreqBandName = "频点E2", Type = 0, Value = 39148 };
            FreqInfo freqE3 = new FreqInfo() { FreqBandName = "频点E3", Type = 0, Value = 39292 };

            FreqBandInfo freqBandE = new FreqBandInfo() { FreqBandName = "频段E" };
            freqBandE.RangeList.Add(freqE1);
            freqBandE.RangeList.Add(freqE2);
            freqBandE.RangeList.Add(freqE3);
            FreqBandInfo freqBandE1 = new FreqBandInfo() { FreqBandName = "频段E1" };
            freqBandE1.RangeList.Add(freqE1);
            FreqBandInfo freqBandE2 = new FreqBandInfo() { FreqBandName = "频段E2" };
            freqBandE2.RangeList.Add(freqE2);
            FreqBandInfo freqBandE3 = new FreqBandInfo() { FreqBandName = "频段E3" };
            freqBandE3.RangeList.Add(freqE3);
            #endregion

            #region A
            FreqInfo freqA = new FreqInfo() { FreqBandName = "频点A", Type = 0, Value = 36275 };

            FreqBandInfo freqBandA = new FreqBandInfo() { FreqBandName = "频段A" };
            freqBandA.RangeList.Add(freqA);
            #endregion

            #region D
            FreqInfo freqD1 = new FreqInfo() { FreqBandName = "频点D1", Type = 0, Value = 37900 };
            FreqInfo freqD2 = new FreqInfo() { FreqBandName = "频点D2", Type = 0, Value = 38098 };
            FreqInfo freqD3 = new FreqInfo() { FreqBandName = "频点D3", Type = 0, Value = 40936 };
            FreqInfo freqD7 = new FreqInfo() { FreqBandName = "频点D7", Type = 0, Value = 41134 };
            FreqInfo freqD8 = new FreqInfo() { FreqBandName = "频点D8", Type = 0, Value = 41332 };

            FreqBandInfo freqBandD = new FreqBandInfo() { FreqBandName = "频段D" };
            freqBandD.RangeList.Add(freqD1);
            freqBandD.RangeList.Add(freqD2);
            freqBandD.RangeList.Add(freqD3);
            freqBandD.RangeList.Add(freqD7);
            freqBandD.RangeList.Add(freqD8);
            FreqBandInfo freqBandD1 = new FreqBandInfo() { FreqBandName = "频段D1" };
            freqBandD1.RangeList.Add(freqD1);
            FreqBandInfo freqBandD2 = new FreqBandInfo() { FreqBandName = "频段D2" };
            freqBandD2.RangeList.Add(freqD2);
            FreqBandInfo freqBandD3 = new FreqBandInfo() { FreqBandName = "频段D3" };
            freqBandD3.RangeList.Add(freqD3);
            FreqBandInfo freqBandD7 = new FreqBandInfo() { FreqBandName = "频段D7" };
            freqBandD7.RangeList.Add(freqD7);
            FreqBandInfo freqBandD8 = new FreqBandInfo() { FreqBandName = "频段D8" };
            freqBandD8.RangeList.Add(freqD8);
            #endregion

            #region F
            FreqInfo freqF1 = new FreqInfo() { FreqBandName = "频点F1", Type = 0, Value = 38400 };
            FreqInfo freqF2 = new FreqInfo() { FreqBandName = "频点F2", Type = 0, Value = 38544 };
            FreqInfo freqF1Dot = new FreqInfo() { FreqBandName = "频点F1'", Type = 0, Value = 38496 };
            FreqInfo freqF2Dot = new FreqInfo() { FreqBandName = "频点F2'", Type = 0, Value = 38352 };

            FreqBandInfo freqBandF = new FreqBandInfo() { FreqBandName = "频段F" };
            freqBandF.RangeList.Add(freqF1);
            freqBandF.RangeList.Add(freqF2);
            freqBandF.RangeList.Add(freqF1Dot);
            freqBandF.RangeList.Add(freqF2Dot);
            FreqBandInfo freqBandF1 = new FreqBandInfo() { FreqBandName = "频段F1" };
            freqBandF1.RangeList.Add(freqF1);
            FreqBandInfo freqBandF2 = new FreqBandInfo() { FreqBandName = "频段F2" };
            freqBandF2.RangeList.Add(freqF2);
            FreqBandInfo freqBandF1Dot = new FreqBandInfo() { FreqBandName = "频段F1'" };
            freqBandF1Dot.RangeList.Add(freqF1Dot);
            FreqBandInfo freqBandF2Dot = new FreqBandInfo() { FreqBandName = "频段F2'" };
            freqBandF2Dot.RangeList.Add(freqF2Dot);
            #endregion

            #region FDD1800
            FreqInfo freqFDD1800 = new FreqInfo() { FreqBandName = "频段FDD1800", Type = 0, Value = 1300 };

            FreqBandInfo freqBandFDD1800 = new FreqBandInfo() { FreqBandName = "频段FDD1800" };
            freqBandFDD1800.RangeList.Add(freqFDD1800);
            #endregion

            #region FDD900
            FreqInfo freqFDD900 = new FreqInfo() { FreqBandName = "频段FDD900", Type = 0, Value = 3590 };

            FreqBandInfo freqBandFDD900 = new FreqBandInfo() { FreqBandName = "频段FDD900" };
            freqBandFDD900.RangeList.Add(freqFDD900);
            #endregion

            cbeFreq.Properties.Items.Add(freqBandE);
            cbeFreq.Properties.Items.Add(freqBandE1);
            cbeFreq.Properties.Items.Add(freqBandE2);
            cbeFreq.Properties.Items.Add(freqBandE3);

            cbeFreq.Properties.Items.Add(freqBandA);

            cbeFreq.Properties.Items.Add(freqBandD);
            cbeFreq.Properties.Items.Add(freqBandD1);
            cbeFreq.Properties.Items.Add(freqBandD2);
            cbeFreq.Properties.Items.Add(freqBandD3);
            cbeFreq.Properties.Items.Add(freqBandD7);
            cbeFreq.Properties.Items.Add(freqBandD8);

            cbeFreq.Properties.Items.Add(freqBandF);
            cbeFreq.Properties.Items.Add(freqBandF1);
            cbeFreq.Properties.Items.Add(freqBandF2);
            cbeFreq.Properties.Items.Add(freqBandF1Dot);
            cbeFreq.Properties.Items.Add(freqBandF2Dot);

            cbeFreq.Properties.Items.Add(freqBandFDD1800);
            cbeFreq.Properties.Items.Add(freqBandFDD900);

            cbeFreq.SelectedIndex = 0;
            cbeFreq.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            cbeFreq.Enabled = false;
        }

        void ceFreq_CheckedChanged(object sender, EventArgs e)
        {
            if (ceFreq.Checked)
            {
                cbeFreq.Enabled = true;
            }
            else
            {
                cbeFreq.Enabled = false;
            }
        }

        public bool BFreq
        {
            get { return ceFreq.Checked; }
        }

        public FreqBandInfo FRFreq
        {
            get { return cbeFreq.SelectedItem as FreqBandInfo; }
        }

        public void GetSettingFilterRet(out int setRxlev, out int setRxlevDiff, out bool coFreq, out MapForm.DisplayInterferenceType interferenceType,
            out bool saveTestPoint, out int invalidPointRxLev)
        {
            setRxlevDiff = (int)numRxLevDValue.Value;
            setRxlev = (int)numRxLevThreshold.Value;
            coFreq = chkCoFreq.Checked;
            interferenceType = (MapForm.DisplayInterferenceType)cbxCoFreqType.SelectedIndex;
            saveTestPoint = chkSaveTestPoint.Checked;
            invalidPointRxLev = (int)spinEditInvalidThresold.Value;
        }

        public int DSC1800DiffMin
        {
            get { return (int)numDiffMin.Value; }
            set { numDiffMin.Value = value; }
        }

        public bool CheckBandDiff
        {
            get { return checkBandEditDiff.Checked; }
            set { checkBandEditDiff.Checked = value; }
        }

        private void simpleButton1_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void simpleButton2_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        private void chkCoFreq_CheckedChanged(object sender, EventArgs e)
        {
            cbxCoFreqType.Enabled = chkCoFreq.Checked;
        }
    }
}