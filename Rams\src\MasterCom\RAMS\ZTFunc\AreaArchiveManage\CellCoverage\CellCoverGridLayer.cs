﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Runtime.Serialization;
using System.Text;
using MasterCom.MTGis;
using MasterCom.RAMS.Grid;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc.AreaArchiveManage.CellCoverage
{
    public class CellCoverGridLayer: LayerBase
    {
        public CellCoverGridLayer()
            : base("小区覆盖栅格图层")
        {
        }

        public ICell Cell { get; set; }
        public Dictionary<GridDataHub, Color> GridColorDic { get; set; }
        public override void Draw(System.Drawing.Rectangle clientRect, System.Drawing.Rectangle updateRect, System.Drawing.Graphics graphics)
        {
            if (!IsVisible)
            {
                return;
            }
            if (GridColorDic == null || GridColorDic.Count == 0)
            {
                return;
            }
            Rectangle inflatedRect = new Rectangle(updateRect.X, updateRect.Y, updateRect.Width, updateRect.Height);
            inflatedRect.Inflate(50, 50);
            DbRect dRect;
            this.gisAdapter.FromDisplay(updateRect, out dRect);

            RectangleF? size = null;
            DbPoint cellPt = new DbPoint(this.Cell.EndPointLongitude, this.Cell.EndPointLatitude);
            PointF cellPf;
            gisAdapter.ToDisplay(cellPt, out cellPf);
            foreach (GridDataHub grid in GridColorDic.Keys)
            {
                if (grid.Bounds.Within(dRect))
                {
                    drawGrid(graphics, cellPf, grid, GridColorDic[grid], ref size);
                }
            }
        }

        private void drawGrid(Graphics graphics,PointF cellPf, GridDataHub grid,Color color, ref RectangleF? size)
        {
            PointF pointLt;
            this.gisAdapter.ToDisplay(new DbPoint(grid.LTLng, grid.LTLat), out pointLt);
            if (size == null)
            {
                DbPoint brPoint = new DbPoint(grid.BRLng, grid.BRLat);
                PointF pointBr;
                this.gisAdapter.ToDisplay(brPoint, out pointBr);
                size = new RectangleF(pointLt.X, pointLt.Y, pointBr.X - pointLt.X, pointBr.Y - pointLt.Y);
            }
            RectangleF rect = new RectangleF(pointLt.X, pointLt.Y, ((RectangleF)size).Width, ((RectangleF)size).Height);
            graphics.FillRectangle(new SolidBrush(Color.FromArgb(200, color)), rect);
            PointF gridCenter = new PointF(rect.X + rect.Width / 2, rect.Y + rect.Height / 2);
            graphics.DrawLine(Pens.Red, cellPf, gridCenter);
        }


    }
}
