﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using System.Collections;
using System.IO;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Net;
using MasterCom.MTGis;
using DevExpress.XtraGrid.Columns;
using DevExpress.XtraTreeList.Nodes;
using DevExpress.XtraTreeList.Columns;
using DevExpress.XtraEditors;
using MasterCom.RAMS.NewBlackBlock;

namespace MasterCom.RAMS.BackgroundFunc
{
    public partial class BackgroundResultForm : MinCloseForm
    {
        Dictionary<BackgroundFuncType, Dictionary<BackgroundSubFuncType, Dictionary<string, List<BackgroundResult>>>> bgResultDic =
            new Dictionary<BackgroundFuncType, Dictionary<BackgroundSubFuncType, Dictionary<string, List<BackgroundResult>>>>();
        BackgroundStatType statTypeOld = BackgroundStatType.None;
        BackgroundCellType cellTypeOld = BackgroundCellType.None;
        bool initedSummary = false;
        bool initedDetail = false;
        public BackgroundResultForm(MainModel mainModel)
            : base(mainModel)
        {
            InitializeComponent();
            initTreeColumns();
            DisposeWhenClose = true;
        }

        private void initTreeColumns()
        {
            treeList.Columns.Clear();
            TreeListColumn column = treeList.Columns.Add();
            column.OptionsColumn.AllowEdit = false;
            column.Visible = true;
        }

        public void FillData(Dictionary<BackgroundFuncType, Dictionary<BackgroundSubFuncType, Dictionary<string, List<BackgroundResult>>>> bgResultDic)
        {
            this.bgResultDic = bgResultDic;
            initedSummary = false;
            initedDetail = false;
            initResult();
        }

        private void initResult() 
        {
            if (xtraTabControl.SelectedTabPageIndex == 0)
            {
                if (!initedSummary)
                {
                    initSummary();
                }
            }
            else
            {
                if (!initedDetail)
                {
                    initDetail();
                }
            }
        }

        private void initSummary()
        {
            treeListDetail.DataSource = new object();
        }

        private void initDetail()
        {
            initDetailTreeList();
            BindingSource source = new BindingSource();
            gridControl.DataSource = source;
            gridControl.RefreshDataSource();
            initedDetail = true;
        }

        private void initDetailTreeList()
        {
            treeList.BeginUnboundLoad();
            treeList.Nodes.Clear();
            foreach (BackgroundFuncType funcType in bgResultDic.Keys)
            {
                TreeListNode node = treeList.AppendNode(new object[] { funcType }, null);
                node.HasChildren = true;
                node.Tag = funcType;
                foreach (BackgroundSubFuncType subFuncType in bgResultDic[funcType].Keys)
                {
                    TreeListNode subNode = treeList.AppendNode(new object[] { subFuncType }, node);
                    subNode.HasChildren = true;
                    subNode.Tag = true;
                }
            }
            treeList.EndUnboundLoad();
        }

        private void initGridColumns(BackgroundStatType statType, BackgroundCellType cellType)
        {
            if (statType == statTypeOld && cellType == cellTypeOld)
            {
                return;
            }
            statTypeOld = statType;
            cellTypeOld = cellType;
            gridView.Columns.Clear();
            List<GridColumn> gridColumnList = getGridColumnList(statType, cellType);

            for (int i = 0; i < gridColumnList.Count; i++)
            {
                gridColumnList[i].VisibleIndex = i;
            }
            gridView.Columns.AddRange(gridColumnList.ToArray());
        }

        private static List<GridColumn> getGridColumnList(BackgroundStatType statType, BackgroundCellType cellType)
        {
            List<GridColumn> gridColumnList = new List<GridColumn>();
            GridColumn gridColumn;
            if (statType == BackgroundStatType.Road || statType == BackgroundStatType.Cell_Road)
            {
                gridColumn = new GridColumn();
                gridColumn.Caption = "文件名";
                gridColumn.FieldName = "FileName";
                gridColumn.Name = "gridColumnFileName";
                gridColumnList.Add(gridColumn);
            }
            if (statType == BackgroundStatType.Cell_Road || statType == BackgroundStatType.Cell_Region)
            {
                gridColumn = new GridColumn();
                gridColumn.Caption = "小区名称";
                gridColumn.FieldName = "CellName";
                gridColumn.Name = "gridColumnCellName";
                gridColumnList.Add(gridColumn);

                gridColumn = new GridColumn();
                gridColumn.Caption = cellType == BackgroundCellType.LTE ? "TAC" : "LAC";
                gridColumn.FieldName = "LAC";
                gridColumn.Name = "gridColumnLAC";
                gridColumnList.Add(gridColumn);

                gridColumn = new GridColumn();
                gridColumn.Caption = cellType == BackgroundCellType.LTE ? "ECI" : "CI";
                gridColumn.FieldName = "CI";
                gridColumn.Name = "gridColumnCI";
                gridColumnList.Add(gridColumn);

                gridColumn = new GridColumn();
                gridColumn.Caption = "频点";
                gridColumn.FieldName = "BCCH";
                gridColumn.Name = "gridColumnBCCH";
                gridColumnList.Add(gridColumn);

                gridColumn = new GridColumn();
                gridColumn.Caption = "色码(扰码)";
                gridColumn.FieldName = "BSIC";
                gridColumn.Name = "gridColumnBSIC";
                gridColumnList.Add(gridColumn);
            }
            if (statType == BackgroundStatType.Road || statType == BackgroundStatType.Cell_Road)
            {
                gridColumn = new GridColumn();
                gridColumn.Caption = "起始经度";
                gridColumn.FieldName = "LongitudeStart";
                gridColumn.Name = "gridColumnLongitudeStart";
                gridColumnList.Add(gridColumn);

                gridColumn = new GridColumn();
                gridColumn.Caption = "起始纬度";
                gridColumn.FieldName = "LatitudeStart";
                gridColumn.Name = "gridColumnLatitudeStart";
                gridColumnList.Add(gridColumn);
            }
            gridColumn = new GridColumn();
            gridColumn.Caption = "中心经度";
            gridColumn.FieldName = "LongitudeMid";
            gridColumn.Name = "gridColumnLongitudeMid";
            gridColumnList.Add(gridColumn);

            gridColumn = new GridColumn();
            gridColumn.Caption = "中心纬度";
            gridColumn.FieldName = "LatitudeMid";
            gridColumn.Name = "gridColumnLatitudeMid";
            gridColumnList.Add(gridColumn);
            if (statType == BackgroundStatType.Road || statType == BackgroundStatType.Cell_Road)
            {
                gridColumn = new GridColumn();
                gridColumn.Caption = "终止经度";
                gridColumn.FieldName = "LongitudeEnd";
                gridColumn.Name = "gridColumnLongitudeEnd";
                gridColumnList.Add(gridColumn);

                gridColumn = new GridColumn();
                gridColumn.Caption = "终止纬度";
                gridColumn.FieldName = "LatitudeEnd";
                gridColumn.Name = "gridColumnLatitudeEnd";
                gridColumnList.Add(gridColumn);
            }

            gridColumn = new GridColumn();
            gridColumn.Caption = "起始时间";
            gridColumn.FieldName = "DateTimeBeginString";
            gridColumn.Name = "gridColumnDateTimeBeginString";
            gridColumnList.Add(gridColumn);

            gridColumn = new GridColumn();
            gridColumn.Caption = "终止时间";
            gridColumn.FieldName = "DateTimeEndString";
            gridColumn.Name = "gridColumnDateTimeEndString";
            gridColumnList.Add(gridColumn);

            if (statType == BackgroundStatType.Road || statType == BackgroundStatType.Cell_Road)
            {
                gridColumn = new GridColumn();
                gridColumn.Caption = "持续时长(毫秒)";
                gridColumn.FieldName = "TimeLast";
                gridColumn.Name = "gridColumnTimeLast";
                gridColumnList.Add(gridColumn);

                gridColumn = new GridColumn();
                gridColumn.Caption = "持续距离(米)";
                gridColumn.FieldName = "DistanceLast";
                gridColumn.Name = "gridColumnDistanceLast";
                gridColumnList.Add(gridColumn);
            }

            gridColumn = new GridColumn();
            gridColumn.Caption = "采样点数";
            gridColumn.FieldName = "SampleCount";
            gridColumn.Name = "gridColumnSampleCount";
            gridColumnList.Add(gridColumn);

            gridColumn = new GridColumn();
            gridColumn.Caption = "平均场强";
            gridColumn.FieldName = "RxLevMeanString";
            gridColumn.Name = "gridColumnRxLevMean";
            gridColumnList.Add(gridColumn);

            gridColumn = new GridColumn();
            gridColumn.Caption = "最小场强";
            gridColumn.FieldName = "RxLevMinString";
            gridColumn.Name = "gridColumnRxLevMin";
            gridColumnList.Add(gridColumn);

            gridColumn = new GridColumn();
            gridColumn.Caption = "最大场强";
            gridColumn.FieldName = "RxLevMaxString";
            gridColumn.Name = "gridColumnRxLevMax";
            gridColumnList.Add(gridColumn);

            gridColumn = new GridColumn();
            gridColumn.Caption = "平均质量";
            gridColumn.FieldName = "RxQualMeanString";
            gridColumn.Name = "gridColumnRxQualMean";
            gridColumnList.Add(gridColumn);

            gridColumn = new GridColumn();
            gridColumn.Caption = "最小质量";
            gridColumn.FieldName = "RxQualMinString";
            gridColumn.Name = "gridColumnRxQualMin";
            gridColumnList.Add(gridColumn);

            gridColumn = new GridColumn();
            gridColumn.Caption = "最大质量";
            gridColumn.FieldName = "RxQualMaxString";
            gridColumn.Name = "gridColumnRxQualMax";
            gridColumnList.Add(gridColumn);

            gridColumn = new GridColumn();
            gridColumn.Caption = "道路";
            gridColumn.FieldName = "RoadDesc";
            gridColumn.Name = "gridColumnRoadDesc";
            gridColumnList.Add(gridColumn);

            gridColumn = new GridColumn();
            gridColumn.Caption = "片区";
            gridColumn.FieldName = "AreaDesc";
            gridColumn.Name = "gridColumnAreaDesc";
            gridColumnList.Add(gridColumn);

            gridColumn = new GridColumn();
            gridColumn.Caption = "网格";
            gridColumn.FieldName = "GridDesc";
            gridColumn.Name = "gridColumnGridDesc";
            gridColumnList.Add(gridColumn);

            gridColumn = new GridColumn();
            gridColumn.Caption = "代维片区";
            gridColumn.FieldName = "AreaAgentDesc";
            gridColumn.Name = "gridColumnAreaAgentDesc";
            gridColumnList.Add(gridColumn);

            gridColumn = new GridColumn();
            gridColumn.Caption = "涉及小区";
            gridColumn.FieldName = "CellIDDesc";
            gridColumn.Name = "gridColumnCellIDDesc";
            gridColumnList.Add(gridColumn);

            gridColumn = new GridColumn();
            gridColumn.Caption = "备注";
            gridColumn.FieldName = "StrDesc";
            gridColumn.Name = "gridColumnStrDesc";
            gridColumnList.Add(gridColumn);
            return gridColumnList;
        }

        private void miExportToExcel_Click(object sender, EventArgs e)
        {
            if (xtraTabControl.SelectedTabPageIndex == 0)
            {
                exportSummaryToExcel();
            }
            else
            {
                exportDetailToExcel(false);
            }
        }

        private void exportSummaryToExcel()
        {
            List<NPOIRow> rows = new List<NPOIRow>();
            NPOIRow row = new NPOIRow();
            row.AddCellValue("业务类型");
            row.AddCellValue("专题类型");
            row.AddCellValue("专题名称");
            row.AddCellValue("问题点个数");
            rows.Add(row);
            foreach (BackgroundFuncType funcType in bgResultDic.Keys)
            {
                row = new NPOIRow();
                row.AddCellValue(funcType.ToString());
                foreach (BackgroundSubFuncType subFuncType in bgResultDic[funcType].Keys)
                {
                    NPOIRow subRow = new NPOIRow();
                    subRow.AddCellValue(subFuncType.ToString());
                    foreach (string funcName in bgResultDic[funcType][subFuncType].Keys)
                    {
                        NPOIRow thirdRow = new NPOIRow();
                        thirdRow.AddCellValue(funcName);
                        thirdRow.AddCellValue(bgResultDic[funcType][subFuncType][funcName].Count);
                        subRow.AddSubRow(thirdRow);
                    }
                    row.AddSubRow(subRow);
                }
                rows.Add(row);
            }
            ExcelNPOIManager.ExportToExcel(rows);
        }

        private void exportDetailToExcel(bool isSplit)
        {
            List<List<NPOIRow>> rowsList = new List<List<NPOIRow>>();
            List<string> sheetNameList = new List<string>();

            foreach (BackgroundFuncType funcType in bgResultDic.Keys)
            {
                foreach (BackgroundSubFuncType subFuncType in bgResultDic[funcType].Keys)
                {
                    foreach (string funcName in bgResultDic[funcType][subFuncType].Keys)
                    {
                        List<NPOIRow> rows = new List<NPOIRow>();
                        rowsList.Add(rows);
                        sheetNameList.Add(funcName);
                        if (bgResultDic[funcType][subFuncType][funcName].Count <= 0)
                        {
                            continue;
                        }
                        BackgroundStatType statType = bgResultDic[funcType][subFuncType][funcName][0].StatType;
                        BackgroundCellType cellType = bgResultDic[funcType][subFuncType][funcName][0].CellType;
                        NPOIRow row = new NPOIRow();
                        if (statType == BackgroundStatType.Road || statType == BackgroundStatType.Cell_Road)
                        {
                            row.AddCellValue("文件名");
                        }
                        if (statType == BackgroundStatType.Cell_Road || statType == BackgroundStatType.Cell_Region)
                        {
                            row.AddCellValue("小区名称");
                            row.AddCellValue(cellType == BackgroundCellType.LTE ? "TAC" : "LAC");
                            row.AddCellValue(cellType == BackgroundCellType.LTE ? "ECI" : "CI");
                            row.AddCellValue("频点");
                            row.AddCellValue("色码(扰码)");
                        }
                        if (statType == BackgroundStatType.Road || statType == BackgroundStatType.Cell_Road)
                        {
                            row.AddCellValue("起始经度");
                            row.AddCellValue("起始纬度");
                        }
                        row.AddCellValue("中心经度");
                        row.AddCellValue("中心纬度");
                        if (statType == BackgroundStatType.Road || statType == BackgroundStatType.Cell_Road)
                        {
                            row.AddCellValue("终止经度");
                            row.AddCellValue("终止纬度");
                        }
                        row.AddCellValue("起始时间");
                        row.AddCellValue("终止时间");
                        if (statType == BackgroundStatType.Road || statType == BackgroundStatType.Cell_Road)
                        {
                            row.AddCellValue("持续时长(毫秒)");
                            row.AddCellValue("持续距离(米)");
                        }
                        row.AddCellValue("采样点数");
                        row.AddCellValue("平均场强");
                        row.AddCellValue("最小场强");
                        row.AddCellValue("最大场强");
                        row.AddCellValue("平均质量");
                        row.AddCellValue("最小质量");
                        row.AddCellValue("最大质量");
                        row.AddCellValue("道路");
                        row.AddCellValue("片区");
                        row.AddCellValue("网格");
                        row.AddCellValue("代维片区");
                        row.AddCellValue("涉及小区");
                        row.AddCellValue("备注");
                        if (!isSplit)
                        {
                            row.AddCellValue("个性化信息");
                        }
                        else
                        {
                            if (bgResultDic[funcType][subFuncType][funcName].Count > 0)
                            {
                                string strBgResult = bgResultDic[funcType][subFuncType][funcName][0].ImageDesc;
                                string[] strBgResultList = strBgResult.Replace("\r\n", "^").Split('^');
                                for (int i = 0; i < strBgResultList.Length; i++)
                                {
                                    row.AddCellValue(strBgResultList[i].Split('：')[0]);
                                }
                            }

                        }
                        rows.Add(row);
                        foreach (BackgroundResult bgResult in bgResultDic[funcType][subFuncType][funcName])
                        {
                            row = new NPOIRow();
                            if (statType == BackgroundStatType.Road || statType == BackgroundStatType.Cell_Road)
                            {
                                row.AddCellValue(bgResult.FileName);
                            }
                            if (statType == BackgroundStatType.Cell_Road || statType == BackgroundStatType.Cell_Region)
                            {
                                row.AddCellValue(bgResult.CellName);
                                row.AddCellValue(bgResult.LAC);
                                row.AddCellValue(bgResult.CI);
                                row.AddCellValue(bgResult.BCCH);
                                row.AddCellValue(bgResult.BSIC);
                            }
                            if (statType == BackgroundStatType.Road || statType == BackgroundStatType.Cell_Road)
                            {
                                row.AddCellValue(bgResult.LongitudeStart);
                                row.AddCellValue(bgResult.LatitudeStart);
                            }
                            row.AddCellValue(bgResult.LongitudeMid);
                            row.AddCellValue(bgResult.LatitudeMid);
                            if (statType == BackgroundStatType.Road || statType == BackgroundStatType.Cell_Road)
                            {
                                row.AddCellValue(bgResult.LongitudeEnd);
                                row.AddCellValue(bgResult.LatitudeEnd);
                            }
                            row.AddCellValue(bgResult.DateTimeBeginString);
                            row.AddCellValue(bgResult.DateTimeEndString);
                            if (statType == BackgroundStatType.Road || statType == BackgroundStatType.Cell_Road)
                            {
                                row.AddCellValue(bgResult.TimeLast);
                                row.AddCellValue(bgResult.DistanceLast);
                            }
                            row.AddCellValue(bgResult.SampleCount);
                            row.AddCellValue(bgResult.RxLevMean);
                            row.AddCellValue(bgResult.RxLevMin);
                            row.AddCellValue(bgResult.RxLevMax);
                            row.AddCellValue(bgResult.RxQualMean);
                            row.AddCellValue(bgResult.RxQualMin);
                            row.AddCellValue(bgResult.RxQualMax);
                            row.AddCellValue(bgResult.RoadDesc);
                            row.AddCellValue(bgResult.AreaDesc);
                            row.AddCellValue(bgResult.GridDesc);
                            row.AddCellValue(bgResult.AreaAgentDesc);
                            row.AddCellValue(bgResult.CellIDDesc);
                            row.AddCellValue(bgResult.StrDesc);
                            if (!isSplit)
                            {
                                row.AddCellValue(bgResult.ImageDesc);
                            }
                            else
                            {
                                string[] strBgResultList = bgResult.ImageDesc.Replace("\r\n", "^").Split('^');
                                if (strBgResultList[0] != "")
                                {
                                    for (int i = 0; i < strBgResultList.Length; i++)
                                    {
                                        row.AddCellValue(strBgResultList[i].Split('：')[1]);
                                    }
                                }
                                
                            }
                            rows.Add(row);
                        }
                    }
                }
            }
            ExcelNPOIManager.ExportToExcel(rowsList, sheetNameList);
        }

        private void gridControl_DoubleClick(object sender, EventArgs e)
        {
            if (gridView.SelectedRowsCount > 0)
            {
                BackgroundResult item = gridView.GetRow(gridView.GetSelectedRows()[0]) as BackgroundResult;
                MainModel.MainForm.GetMapForm().GoToView(item.LongitudeMid, item.LatitudeMid);
            }
        }

        private void treeList_FocusedNodeChanged(object sender, DevExpress.XtraTreeList.FocusedNodeChangedEventArgs e)
        {
            if (e.Node != null && e.Node.Tag is BackgroundSubFuncType)
            {
                BackgroundFuncType funcType = (BackgroundFuncType)e.Node.RootNode.Tag;
                BackgroundSubFuncType subFuncType = (BackgroundSubFuncType)e.Node.Tag;
                MainModel.CurBackgroundResultList.Clear();
                MainModel.CurBackgroundResultList.AddRange(bgResultDic[funcType][subFuncType][e.Node.GetValue(0).ToString()]);
                if (MainModel.CurBackgroundResultList.Count > 0)
                {
                    initGridColumns(MainModel.CurBackgroundResultList[0].StatType, MainModel.CurBackgroundResultList[0].CellType);
                }
            }
            else
            {
                MainModel.CurBackgroundResultList.Clear();
                memDIYInfo.Text = "";
            }
            BindingSource source = new BindingSource();
            source.DataSource = MainModel.CurBackgroundResultList;
            gridControl.DataSource = source;
            gridControl.RefreshDataSource();
        }

        private void treeList_BeforeExpand(object sender, DevExpress.XtraTreeList.BeforeExpandEventArgs e)
        {
            if (e.Node != null && e.Node.Tag is Boolean && Convert.ToBoolean(e.Node.Tag))
            {
                BackgroundFuncType funcType = (BackgroundFuncType)e.Node.ParentNode.Tag;
                BackgroundSubFuncType subFuncType = (BackgroundSubFuncType)e.Node.GetValue(0);
                Dictionary<string, List<BackgroundResult>> tmpDic = bgResultDic[funcType][subFuncType];
                TreeListNode node;
                treeList.BeginUnboundLoad();
                foreach (string key in tmpDic.Keys)
                {
                    node = treeList.AppendNode(new object[] { key }, e.Node);
                    node.HasChildren = false;
                    node.Tag = e.Node.GetValue(0);
                    e.Node.Tag = false;
                }
                treeList.EndUnboundLoad();
            }
        }

        private void gridView_SelectionChanged(object sender, DevExpress.Data.SelectionChangedEventArgs e)
        {
            if (gridView.SelectedRowsCount > 0)
            {
                MainModel.CurBackgroundResult = gridView.GetRow(gridView.GetSelectedRows()[0]) as BackgroundResult;
                memDIYInfo.Text = MainModel.CurBackgroundResult.ImageDesc;
            }
            else
            {
                memDIYInfo.Text = "";
            }
        }

        private void miExportToWord_Click(object sender, EventArgs e)
        {
            if (MainModel.CurBackgroundResultList.Count > 0)
            {
                BackgroundResultToWord.ExportToWord(MainModel.CurBackgroundResultList);
            }
        }

        private void miExportToWordAll_Click(object sender, EventArgs e)
        {
            List<BackgroundResult> bgResultList = new List<BackgroundResult>();
            foreach (BackgroundFuncType funcType in bgResultDic.Keys)
            {
                foreach (BackgroundSubFuncType subFuncType in bgResultDic[funcType].Keys)
                {
                    foreach (string funcName in bgResultDic[funcType][subFuncType].Keys)
                    {
                        bgResultList.AddRange(bgResultDic[funcType][subFuncType][funcName]);
                    }
                }
            }
            BackgroundResultToWord.ExportToWord(bgResultList);
        }

        private void treeListDetail_VirtualTreeGetCellValue(object sender, DevExpress.XtraTreeList.VirtualTreeGetCellValueInfo e)
        {
            if (e.Node is BackgroundResultSummary)
            {
                BackgroundResultSummary summary = e.Node as BackgroundResultSummary;
                if (e.Column == treeListColumnType)
                {
                    if (summary.DetailType == BackgroundResultSummaryType.FuncType || 
                        summary.DetailType == BackgroundResultSummaryType.SubFuncType)
                    {
                        e.CellData = summary.TypeName;
                    }
                    else
                    {
                        e.CellData = null;
                    }
                }
                else if (e.Column == treeListColumnFuncName)
                {
                    setData(e, summary.DetailType, summary.FuncName);
                }
                else if (e.Column == treeListColumnResultCount)
                {
                    setData(e, summary.DetailType, summary.ResultCount);
                }
            }
        }

        private static void setData(DevExpress.XtraTreeList.VirtualTreeGetCellValueInfo e, BackgroundResultSummaryType type, object data)
        {
            if (type == BackgroundResultSummaryType.Func)
            {
                e.CellData = data;
            }
            else
            {
                e.CellData = null;
            }
        }

        private void treeListDetail_VirtualTreeGetChildNodes(object sender, DevExpress.XtraTreeList.VirtualTreeGetChildNodesInfo e)
        {
            Cursor current = Cursor.Current;
            Cursor.Current = Cursors.WaitCursor;
            if (!initedSummary)
            {
                List<BackgroundResultSummary> list = getBackgroundDetailFuncType();
                e.Children = list;
                initedSummary = true;
            }
            else 
            {
                try 
                {
                    BackgroundResultSummary detail = (BackgroundResultSummary)e.Node;
                    if(detail.DetailType == BackgroundResultSummaryType.FuncType) 
                    {
                        e.Children = getBackgroundDetailSubFuncType(detail.FuncType);
                    }
                    else if (detail.DetailType == BackgroundResultSummaryType.SubFuncType)
                    {
                        e.Children = getBackgroundDetailFunc(detail.FuncType, detail.SubFuncType);
                    }
                }
                catch 
                { 
                    e.Children = new List<BackgroundResultSummary>(); 
                }
            }
            Cursor.Current = current;
        }

        private void xtraTabControl_SelectedPageChanged(object sender, DevExpress.XtraTab.TabPageChangedEventArgs e)
        {
            initResult();
        }

        private List<BackgroundResultSummary> getBackgroundDetailFuncType()
        {
            List<BackgroundResultSummary> detailList = new List<BackgroundResultSummary>();
            foreach (BackgroundFuncType funcType in bgResultDic.Keys)
            {
                detailList.Add(new BackgroundResultSummary(funcType));
            }
            return detailList;
        }

        private List<BackgroundResultSummary> getBackgroundDetailSubFuncType(BackgroundFuncType funcType)
        {
            List<BackgroundResultSummary> detailList = new List<BackgroundResultSummary>();
            foreach (BackgroundFuncType item in bgResultDic.Keys)
            {
                if (item == funcType)
                {
                    foreach (BackgroundSubFuncType subFuncType in bgResultDic[funcType].Keys)
                    {
                        detailList.Add(new BackgroundResultSummary(funcType, subFuncType));
                    }
                }
            }
            return detailList;
        }

        private List<BackgroundResultSummary> getBackgroundDetailFunc(BackgroundFuncType funcType, BackgroundSubFuncType subFuncType)
        {
            List<BackgroundResultSummary> detailList = new List<BackgroundResultSummary>();
            foreach (BackgroundFuncType item in bgResultDic.Keys)
            {
                if (item == funcType)
                {
                    foreach (BackgroundSubFuncType subItem in bgResultDic[funcType].Keys)
                    {
                        if (subItem == subFuncType)
                        {
                            foreach (string funcName in bgResultDic[funcType][subFuncType].Keys)
                            {
                                detailList.Add(new BackgroundResultSummary(funcName, bgResultDic[funcType][subFuncType][funcName].Count));
                            }
                        }
                    }
                }
            }
            return detailList;
        }

        private void contextMenuStrip_Opening(object sender, CancelEventArgs e)
        {
            if (xtraTabControl.SelectedTabPageIndex == 0)
            {
                miExpandAll.Visible = true;
                miCollapseAll.Visible = true;
                miSeparate.Visible = true;
                miExportToWord.Visible = false;
                miExportToWordAll.Visible = false;
            }
            else
            {
                miExpandAll.Visible = false;
                miCollapseAll.Visible = false;
                miSeparate.Visible = false;
                miExportToWord.Visible = true;
                miExportToWordAll.Visible = true;
            }
        }

        private void miExpandAll_Click(object sender, EventArgs e)
        {
            treeListDetail.ExpandAll();
        }

        private void miCollapseAll_Click(object sender, EventArgs e)
        {
            treeListDetail.CollapseAll();
        }

        class BackgroundResultSummary
        {
            public BackgroundResultSummary(BackgroundFuncType funcType)
            {
                this.funcType = funcType;
                this.detailType = BackgroundResultSummaryType.FuncType;
            }
            public BackgroundResultSummary(BackgroundFuncType funcType, BackgroundSubFuncType subFuncType)
            {
                this.funcType = funcType;
                this.subFuncType = subFuncType;
                this.detailType = BackgroundResultSummaryType.SubFuncType;
            }
            public BackgroundResultSummary(string funcName, int resultCount)
            {
                this.funcName = funcName;
                this.resultCount = resultCount;
                this.detailType = BackgroundResultSummaryType.Func;
            }
            private readonly BackgroundFuncType funcType;
            public BackgroundFuncType FuncType
            {
                get { return funcType; }
            }

            private readonly BackgroundSubFuncType subFuncType;
            public BackgroundSubFuncType SubFuncType
            {
                get { return subFuncType; }
            }

            public string TypeName
            {
                get 
                {
                    return detailType == BackgroundResultSummaryType.FuncType ? funcType.ToString() : subFuncType.ToString(); 
                }
            }

            private readonly string funcName;
            public string FuncName
            {
                get { return funcName; }
            }

            private readonly int resultCount;
            public int ResultCount
            {
                get { return resultCount; }
            }

            private readonly BackgroundResultSummaryType detailType;
            public BackgroundResultSummaryType DetailType
            {
                get { return detailType; }
            }
        }

        enum BackgroundResultSummaryType
        {
            FuncType,
            SubFuncType,
            Func
        }

        private void miMenuItem_Click(object sender, EventArgs e)
        {
            if (xtraTabControl.SelectedTabPageIndex == 0)
            {
                exportSummaryToExcel();
            }
            else
            {
                exportDetailToExcel(true);
            }
        }

    }
}
