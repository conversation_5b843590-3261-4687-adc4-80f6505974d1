<?xml version="1.0"?>
<Configs>
  <Config name="Quicks">
    <Item name="QuickSetSavers" typeName="IList">
      <Item typeName="SetSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="String" key="AuthorName">1</Item>
          <Item typeName="String" key="ReporterTemplateName">LTE综合统计报表</Item>
          <Item key="FileName" />
          <Item typeName="Int32" key="FormAreaType">1</Item>
          <Item typeName="String" key="SavePath">C:\MasterCom</Item>
          <Item key="dateTimeElapse" />
          <Item typeName="Int32" key="numElapseTo">0</Item>
          <Item typeName="IList" key="AgentIds">
            <Item typeName="Int32">0</Item>
          </Item>
          <Item typeName="IList" key="AreaList">
            <Item typeName="String">VIP路线</Item>
            <Item typeName="String">昌九城际</Item>
            <Item typeName="String">赣龙铁路</Item>
            <Item typeName="String">杭长高铁</Item>
            <Item typeName="String">京九铁路</Item>
            <Item typeName="String">铜九铁路</Item>
            <Item typeName="String">武九铁路</Item>
            <Item typeName="String">向莆高铁</Item>
            <Item typeName="String">浙赣铁路</Item>
          </Item>
          <Item typeName="IList" key="ProjList">
            <Item typeName="Int32">1</Item>
          </Item>
          <Item typeName="IList" key="ServiceList">
            <Item typeName="Int32">34</Item>
          </Item>
          <Item typeName="IList" key="CarrierTypes">
            <Item typeName="Int32">1</Item>
          </Item>
          <Item typeName="IList" key="CityList">
            <Item typeName="Int32">1</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="SetSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="String" key="AuthorName">2</Item>
          <Item typeName="String" key="ReporterTemplateName">LTE综合统计报表</Item>
          <Item key="FileName" />
          <Item typeName="Int32" key="FormAreaType">1</Item>
          <Item typeName="String" key="SavePath">C:\Users\<USER>\Desktop</Item>
          <Item key="dateTimeElapse" />
          <Item typeName="Int32" key="numElapseTo">0</Item>
          <Item typeName="IList" key="AgentIds">
            <Item typeName="Int32">0</Item>
          </Item>
          <Item typeName="IList" key="AreaList">
            <Item typeName="String">VIP路线</Item>
            <Item typeName="String">昌九城际</Item>
            <Item typeName="String">赣龙铁路</Item>
            <Item typeName="String">杭长高铁</Item>
            <Item typeName="String">京九铁路</Item>
            <Item typeName="String">铜九铁路</Item>
            <Item typeName="String">武九铁路</Item>
            <Item typeName="String">向莆高铁</Item>
            <Item typeName="String">浙赣铁路</Item>
          </Item>
          <Item typeName="IList" key="ProjList">
            <Item typeName="Int32">1</Item>
          </Item>
          <Item typeName="IList" key="ServiceList">
            <Item typeName="Int32">33</Item>
          </Item>
          <Item typeName="IList" key="CarrierTypes">
            <Item typeName="Int32">1</Item>
          </Item>
          <Item typeName="IList" key="CityList">
            <Item typeName="Int32">1</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="SetSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="String" key="AuthorName">测</Item>
          <Item typeName="String" key="ReporterTemplateName">3个指标</Item>
          <Item key="FileName" />
          <Item typeName="Int32" key="FormAreaType">4</Item>
          <Item typeName="String" key="SavePath">C:\MasterCom</Item>
          <Item key="dateTimeElapse" />
          <Item typeName="Int32" key="numElapseTo">0</Item>
          <Item typeName="IList" key="AgentIds">
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
          </Item>
          <Item typeName="IList" key="AreaList">
            <Item typeName="String">广佛肇城轨</Item>
          </Item>
          <Item typeName="IList" key="ProjList">
            <Item typeName="Int32">48</Item>
          </Item>
          <Item typeName="IList" key="ServiceList">
            <Item typeName="Int32">34</Item>
          </Item>
          <Item typeName="IList" key="CarrierTypes">
            <Item typeName="Int32">1</Item>
          </Item>
          <Item typeName="IList" key="CityList">
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">6</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="SetSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="String" key="AuthorName">3个指标</Item>
          <Item typeName="String" key="ReporterTemplateName">3个指标</Item>
          <Item key="FileName" />
          <Item typeName="Int32" key="FormAreaType">1</Item>
          <Item typeName="String" key="SavePath">C:\MasterCom</Item>
          <Item key="dateTimeElapse" />
          <Item typeName="Int32" key="numElapseTo">0</Item>
          <Item typeName="IList" key="AgentIds">
            <Item typeName="Int32">0</Item>
          </Item>
          <Item typeName="IList" key="AreaList">
            <Item typeName="String">VIP路线</Item>
            <Item typeName="String">昌九城际</Item>
            <Item typeName="String">赣龙铁路</Item>
            <Item typeName="String">杭长高铁</Item>
            <Item typeName="String">京九铁路</Item>
            <Item typeName="String">铜九铁路</Item>
            <Item typeName="String">武九铁路</Item>
            <Item typeName="String">向莆高铁</Item>
            <Item typeName="String">浙赣铁路</Item>
          </Item>
          <Item typeName="IList" key="ProjList">
            <Item typeName="Int32">1</Item>
          </Item>
          <Item typeName="IList" key="ServiceList">
            <Item typeName="Int32">34</Item>
          </Item>
          <Item typeName="IList" key="CarrierTypes">
            <Item typeName="Int32">1</Item>
          </Item>
          <Item typeName="IList" key="CityList">
            <Item typeName="Int32">1</Item>
          </Item>
        </Item>
      </Item>
    </Item>
  </Config>
</Configs>