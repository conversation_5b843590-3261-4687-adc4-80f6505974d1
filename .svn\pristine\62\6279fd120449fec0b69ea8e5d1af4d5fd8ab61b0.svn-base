﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class CityFileCompare
    {
        private string dateString = "";
        public string DateString
        {
            get { return dateString; }
        }
        private string netType = "";
        public string NetType
        {
            get { return netType; }
        }
        private string cityName = "";
        public string City
        {
            get { return cityName; }
        }
        public int ExistCount
        {
            get { return fileExist.Count; }
        }
        public string ExistPercent
        {
            get { return Math.Round(ExistCount * 1.0 / FileCount, 4).ToString("p2"); }
        }
        public int NotExistCount
        {
            get { return fileNotExist.Count; }
        }
        public double NotExistPercent
        {
            get { return Math.Round(NotExistCount * 1.0 / FileCount, 4); }
        }
        public int FileCount
        {
            get { return ExistCount + NotExistCount; }
        }
        public string Desc
        {
            get
            {
                int existCnt = fileExist.Count;
                int notExistCnt = fileNotExist.Count;
                int cnt = existCnt + notExistCnt;
                return "共" + cnt.ToString() + "个文件，已获取" + existCnt + "个，未获取" + notExistCnt + "个";
            }
        }
        private readonly List<FileState> fileExist = new List<FileState>();
        public List<FileState> FileExist
        {
            get { return fileExist; }
        }
        private readonly List<FileState> fileNotExist = new List<FileState>();
        public List<FileState> FileNotExist
        {
            get { return fileNotExist; }
        }
        public void Combine(CityFileCompare other)
        {
            if (other.FileNotExist.Count > 0)
            {
                fileNotExist.AddRange(other.FileNotExist);
            }
            if (other.FileExist.Count > 0)
            {
                fileExist.AddRange(other.FileExist);
            }
        }
        public static CityFileCompare FillFrom(MasterCom.RAMS.Net.Content content, out FileState fileState)
        {
            CityFileCompare ret = new CityFileCompare();
            fileState = new FileState();
            fileState.FileName = content.GetParamString();
            ret.cityName = content.GetParamString();
            fileState.CityName = ret.cityName;
            fileState.NetType = content.GetParamString();
            ret.netType = fileState.NetType;
            fileState.BeginTime = DateTime.Parse(content.GetParamString());
            ret.dateString = fileState.DateString;
            fileState.EndTime = DateTime.Parse(content.GetParamString());
            fileState.IsExist = content.GetParamInt() == 1;
            if (fileState.IsExist)
            {
                ret.FileExist.Add(fileState);
            }
            else
            {
                ret.FileNotExist.Add(fileState);
            }
            return ret;
        }
    }

    public class FileState
    {
        public string FileName { get; set; } = "";
        private string device =null;
        public string Device
        {
            get
            {
                if (device == null)
                {
                    device = FileName.Substring(0, 8);
                }
                return device;
            }
        }
        DateTime dtFile = DateTime.MinValue;
        public DateTime TimeFromFileName
        {
            get
            {
                if (dtFile == DateTime.MinValue
                    && FileName.Length >= 22)
                {
                    string date = FileName.Substring(8, 14);
                    date = string.Format("{0}-{1}-{2} {3}:{4}:{5}", date.Substring(0, 4)
                        , date.Substring(4, 2), date.Substring(6, 2), date.Substring(8, 2)
                        , date.Substring(10, 2), date.Substring(12, 2));
                    DateTime.TryParse(date, out dtFile);
                }
                return dtFile;
            }
        }
        
        public string CityName { get; set; } = "";
        public string DateString
        {
            get { return BeginTime.ToString("yyyy/MM/dd"); }
        }
        public string NetType { get; set; } = "";
        public DateTime BeginTime { get; set; }
        public string BeginTimeString
        {
            get 
            {
                return BeginTime.ToString("yyyy/MM/dd HH:mm:ss");
            }
        }
        public DateTime EndTime { get; set; }
        public string EndTimeString
        {
            get
            {
                return EndTime.ToString("yyyy/MM/dd HH:mm:ss");
            }
        }

        public bool IsExist { get; set; }
        public string Desc
        {
            get
            {
                return IsExist ? "已推送" : "未推送";
            }
        }

    }
}
