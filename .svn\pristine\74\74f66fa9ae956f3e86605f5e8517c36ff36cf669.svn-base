﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRLastWeakMosInfo
    {
        public DataInfo LossRateInfo { get; set; } = new DataInfo();
        public DataInfo MOSInfo { get; set; } = new DataInfo();
        public DataInfo HandOverInfo { get; set; } = new DataInfo();

        public CellInfo NRInfo { get; set; } = new CellInfo();
        public CellInfo LTEInfo { get; set; } = new CellInfo();
        public double Distance { get; set; }

        public class CellInfo
        {
            public int Earfcn { get; set; } = 0;
            public int PCI { get; set; } = 0;
            public DataInfo RsrpInfo { get; set; } = new DataInfo();
            public DataInfo SinrInfo { get; set; } = new DataInfo();
        }

        public class DataInfo
        { 
            public double Sum { get; set; }
            public int Count { get; set; }
        }
    }

    public class NRLastWeakMosResult
    {
        public int SN { get; set; }
        public string EventName { get; set; }
        public string FileName { get; set; }
        public string DateTime { get; set; }
        public int TpNum { get; set; }
        public double Distance { get; set; }
        public float AvgLossRate { get; set; }
        public float AvgMOS { get; set; }
        public int HandOverCount { get; set; }
        public Event Ev { get; set; }
        public double Longitude { get; set; }
        public double Latitude { get; set; }

        public CellResInfo NRInfo { get; set; } = new CellResInfo();
        public CellResInfo LTEInfo { get; set; } = new CellResInfo();

        public void SetResData(NRLastWeakMosInfo info)
        {
            Distance = Math.Round(info.Distance, 2);
            NRInfo.AvgRSRP = getValidData(info.NRInfo.RsrpInfo.Sum, info.NRInfo.RsrpInfo.Count);
            NRInfo.AvgSINR = getValidData(info.NRInfo.SinrInfo.Sum, info.NRInfo.SinrInfo.Count);
            LTEInfo.AvgRSRP = getValidData(info.LTEInfo.RsrpInfo.Sum, info.LTEInfo.RsrpInfo.Count);
            LTEInfo.AvgSINR = getValidData(info.LTEInfo.SinrInfo.Sum, info.LTEInfo.SinrInfo.Count);
            NRInfo.Earfcn = info.NRInfo.Earfcn;
            NRInfo.PCI = info.NRInfo.PCI;
            LTEInfo.Earfcn = info.LTEInfo.Earfcn;
            LTEInfo.PCI = info.LTEInfo.PCI;

            AvgLossRate = getValidData(info.LossRateInfo.Sum, info.LossRateInfo.Count);
            AvgMOS = getValidData(info.MOSInfo.Sum, info.MOSInfo.Count);

            HandOverCount = info.HandOverInfo.Count;
        }

        private float getValidData(double sum, int count)
        {
            if (count > 0)
            {
                float res = (float)Math.Round(sum / count, 2);
                return res;
            }
            else
            {
                return 0;
            }
        }

        public class CellResInfo
        {
            public int Earfcn { get; set; }
            public int PCI { get; set; }
            public float AvgRSRP { get; set; }
            public float AvgSINR { get; set; }
        }
    }

    public class NRLastWeakMosCondition
    {
        public int TpNum { get; set; } = 2;
        public ENRWeakMosEventType EventType { get; set; } = ENRWeakMosEventType.MosUnder3dot0Last2Pnt;

        public List<int> GetEvtList()
        {
            List<int> weakMosEventIds = new List<int>();
            if (EventType == ENRWeakMosEventType.MosUnder3dot0Last2Pnt)
            {
                weakMosEventIds = new List<int> { (int)NREventManager.MosUnder3dot0Last2Pnt };
            }
            return weakMosEventIds;
        }
    }

    public enum ENRWeakMosEventType
    {
        MosUnder3dot0Last2Pnt = 0
    }
}
