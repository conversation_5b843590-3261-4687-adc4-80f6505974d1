﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.BackgroundFunc;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Frame;

namespace MasterCom.RAMS.ZTFunc.LteLowDLSpeedAna
{
    public class QueryLowSpeedDataFromFileName : DIYAnalyseFilesOneByOneByRegion
    {
        /// <summary>
        /// 路段长度
        /// </summary>
        public int ValidDistance { get; set; } = 50;
        protected FileInfo curAnaFileInfo;
        public List<CDLowSpeedESResult> listResult_condition { get; set; }
        public QueryLowSpeedDataFromFileName(MainModel mainModel)
            : base(mainModel)
        {
        }
        private int sn;
        public void SetCondition(QueryCondition condi,List<CDLowSpeedESResult> results)
        {
            sn = 0;
            this.condition = condi;
            this.listResult_condition = results;
            this.lowSpeedInfoList_LTE = new List<CDLowSpeedESResult>();
        }
        public override BackgroundStatType StatType
        {
            get { return BackgroundStatType.Road; }
        }

        protected override void query()
        {
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
            }
            catch(Exception ex)
            {
                ErrorInfo = string.Format("连接服务器失败!\nmessage: \n{0}\n\nposition: \n{1}", ex.Message, ex.StackTrace);
                return;
            }
            IsShowFileInfoForm = false;
            MainModel.IsDrawEventResult = false;
            bool drawServer = MainModel.MainForm.GetMapForm().GetCellLayer().DrawServer;
            MainModel.MainForm.GetMapForm().GetCellLayer().DrawServer = false;

            WaitBox.CanCancel = true;
            Dictionary<string,int> dicFileName2Index = new Dictionary<string,int>();
            //去掉重复的文件名
            for (int i = 0; i < this.listResult_condition.Count; i++)
            { 
                string name = this.listResult_condition[i].FileName;
                if (dicFileName2Index.ContainsKey(name)) continue;
                dicFileName2Index[name] = i;
            }
            foreach (string key in dicFileName2Index.Keys)
            {
                QueryCondition cond = new QueryCondition();
                cond.FileName = key;
                cond.FileNameOrNum = 1;
                int index = dicFileName2Index[key];
                TimePeriod period = new TimePeriod(this.listResult_condition[index].BeginTime, this.listResult_condition[index].EndTime);
                cond.Periods.Add(period);
                cond.DistrictID = this.condition.DistrictID;
                cond.DistrictIDs.Add(this.condition.DistrictID);
                DIYQueryFileInfo queryCheckFile = new DIYQueryFileInfo(this.MainModel);
                queryCheckFile.IsShowFileInfoForm = false;
                queryCheckFile.SetQueryCondition(cond);
                queryCheckFile.Query();
                WaitBox.Show("开始分析文件...", analyseFiles);
            }
            DoWaitBoxAfterGetResults();

            MainModel.MainForm.GetMapForm().GetCellLayer().DrawServer = drawServer;
            if (!MainModel.QueryFromBackground)
            {
                MainModel.FireDTDataChanged(this);
                fireShowForm();
                fireSetDefaultMapSerialTheme();
            }
            else
            {
                initBackgroundImageDesc();
            }
        }
        protected override void statData(ClientProxy clientProxy)
        {
            getFilesForAnalyse();
            analyseFiles();
        }

        protected virtual void doBackgroundStatByFile(ClientProxy clientProxy)
        {
            QueryCondition cond = new QueryCondition();
            cond.Geometorys = new SearchGeometrys();
            cond.Geometorys.Region = BackgroundFuncConfigManager.GetInstance().RegionBorder;
            SetQueryCondition(cond);
            getFilesForAnalyse();
            analyseFiles();
        }

        protected virtual void fireSetDefaultMapSerialTheme()
        {
        }

        protected override void getBackgroundData()
        {
            BackgroundResultList = BackgroundFuncQueryManager.GetInstance().GetResult_Road(Condition.Periods[0].BeginTime,
                Condition.Periods[0].EndTime, GetSubFuncID(), Name, StatType, BackgroundFuncConfigManager.GetInstance().ProjectType);
        }

        protected virtual void getFilesForAnalyse()
        {
            BackgroundFuncQueryManager.GetInstance().GetFile_Road(GetSubFuncID(), ServiceTypeString, ((int)carrierID).ToString());
        }

        protected override void analyseFiles()
        {
            try
            {
                List<FileInfo> files = new List<FileInfo>();
                foreach (FileInfo fileInfo in MainModel.FileInfos)
                {
                    files.Add(fileInfo);
                }
                clearDataBeforeAnalyseFiles();
                int iloop = 0;
                if (MainModel.IsBackground)
                {
                    BackgroundFuncManager.GetInstance().ReportBackgroundInfo("共读取待分析文件" + files.Count + "个...");
                }
                foreach (FileInfo fileInfo in files)
                {
                    if (MainModel.IsBackground)
                    {
                        if (MainModel.BackgroundStopRequest)
                        {
                            break;
                        }
                        BackgroundFuncManager.GetInstance().ReportBackgroundInfo("正在分析 " + FuncType.ToString() + 
                            SubFuncType.ToString() + " 类 " + Name + "，当前文件 " + (++iloop) + "/" + files.Count + 
                            "个...文件名：" + fileInfo.Name);
                    }
                    else
                    {
                        WaitBox.Text = "正在分析文件( " + (++iloop) + "/" + files.Count + " )...";
                        WaitBox.ProgressPercent = (int)(iloop * 100.0 / files.Count);
                    }
                    if (filterFile(fileInfo))
                    {
                        continue;
                    }
                    curAnaFileInfo = fileInfo;
                    Condition.FileInfos.Clear();
                    Condition.FileInfos.Add(fileInfo);
                    MainModel.IsFileReplayByCompareMode = Condition.isCompareMode;

                    replay();
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                }
                MainModel.ClearDTData();
                doSomethingAfterAnalyseFiles();
            }
            finally
            {
                System.Threading.Thread.Sleep(20);
                WaitBox.Close();
            }
        }

        protected virtual void doSomethingAfterAnalyseFiles()
        {
            if (!MainModel.IsBackground)
            {
                getResultsAfterQuery();
            }
        }

        protected virtual bool filterFile(FileInfo fileInfo)
        {
            return false;
        }
        private bool isMultiCoverage(TestPoint tPoint)
        {
            List<float> rsrpLst = new List<float>();
            float? rsrpMain = (float?)tPoint["lte_RSRP"];
            if (rsrpMain != null && (float)rsrpMain >= -141 && (float)rsrpMain <= 25)
                rsrpLst.Add((float)rsrpMain);
            for (int i = 0; i < 6; i++)
            {
                float? nRsrp = (float?)tPoint["lte_NCell_RSRP", i];
                if (nRsrp != null && (float)nRsrp >= -141 && (float)nRsrp <= 25)
                    rsrpLst.Add((float)nRsrp);
            }
            rsrpLst.Sort();
            rsrpLst.Reverse();
            return rsrpLst.Count >= 6 && rsrpLst[0] > -105 && Math.Abs(rsrpLst[5] - rsrpLst[0]) <= 6;
        }

        /**
         * 事件           名称
         * 1275         FTP Download_连续低速率_50米_20M_CoverBest
         */
        protected override void doStatWithQuery()
        {
            this.testpointBandIdx.Clear();
            foreach (DTFileDataManager fileDataManager in MainModel.DTDataManager.FileDataManagers)
            {
                string fileName = fileDataManager.FileName;
                foreach (CDLowSpeedESResult ogrResult in this.listResult_condition)
                {
                    if (!ogrResult.FileName.Equals(fileName)) continue;
                    sn++;
                    this.testpointBandIdx.Add(fileDataManager.TestPoints.Count);
                    List<TestPoint> tps = new List<TestPoint>();
                    ZTLowSpeedAnaByRegion_GSM.TempData data = new ZTLowSpeedAnaByRegion_GSM.TempData();
                    List<string> lacciList = new List<string>();
                    List<int> freqList = new List<int>();
                    TestPoint tpPrev = null;
                    List<string> cellNames = new List<string>();
                    double distance = 0;
                    int lowSpeedCount = 0;
                    #region 查询一个时间区间
                    foreach (TestPoint tp in fileDataManager.TestPoints)
                    {
                        if (tp.DateTime < ogrResult.BeginTime) continue;
                        if (tp.DateTime > ogrResult.EndTime) break;

                        tps.Add(tp);
                        if (tpPrev != null)
                        {
                            distance += MathFuncs.GetDistance(tp.Longitude, tp.Latitude, tpPrev.Longitude, tpPrev.Latitude);
                        }
                        float? speed = null;
                        object obj = tp["lte_APP_Speed_Mb"];
                        if (obj != null)
                        {
                            speed = float.Parse(obj.ToString());
                        }
                        float? rsrp = (float?)tp["lte_RSRP"];
                        float? sinr = (float?)tp["lte_SINR"];
                        int? throughput_DL = (int?)tp["lte_APP_ThroughputDL"];
                        if (speed != null && speed >= 0)
                        {
                            data.speed_sampleNum++;
                            data.low_speed = speed > data.low_speed ? data.low_speed : (float)speed;
                            data.high_speed = speed > data.high_speed ? (float)speed : data.high_speed;
                            data.mean_speed += (float)speed;
                            lowSpeedCount += ((float)speed < 20) ? 1 : 0;
                            if (speed == 0)
                            {
                                data.distance_speed_0 += (tp == null || tpPrev == null) ? 0 :
                                                                    MathFuncs.GetDistance(tp.Longitude, tp.Latitude, tpPrev.Longitude, tpPrev.Latitude);
                            }
                        }
                        if (rsrp != null && rsrp >= -141 && rsrp <= 25)
                        {
                            data.rsrp_sampleNum++;
                            data.low_rsrp = (rsrp > data.low_rsrp) ? data.low_rsrp : (float)rsrp;
                            data.high_rsrp = (rsrp > data.high_rsrp) ? (float)rsrp : data.high_rsrp;
                            data.mean_rsrp += (float)rsrp;
                        }
                        if (sinr != null && sinr >= -20 && sinr <= 50)
                        {
                            data.sinr_sampleNum++;
                            data.low_sinr = (sinr > data.low_sinr) ? data.low_sinr : (int)sinr;
                            data.high_sinr = (sinr > data.high_sinr) ? (int)sinr : data.high_sinr;
                            data.mean_sinr += (int)sinr;
                        }
                        if (throughput_DL != null && throughput_DL >= 0)
                        {
                            data.throughput_DL_sampleNum++;
                            data.low_throughput_DL = (throughput_DL > data.low_throughput_DL) ? data.low_throughput_DL : (float)throughput_DL;
                            data.high_throughput_DL = (throughput_DL > data.high_throughput_DL) ? (float)throughput_DL : data.high_throughput_DL;
                            data.mean_throughput_DL += (float)throughput_DL;
                        }
                        data.multiCoverageSampleNum += isMultiCoverage(tp) ? 1 : 0;

                        int? lac = (int?)(ushort?)tp["lte_TAC"];
                        //int? ci = (int?)tPoint["lte_ECI"];
                        LTECell lteCell = tp.GetMainCell_LTE();
                        int? bcch = (int?)tp["lte_EARFCN"];
                        if (lteCell != null)
                        {
                            string lacci = lac.ToString() + "_" + lteCell.SCellID.ToString();
                            string cellName = lteCell.Name;
                            if (!lacciList.Contains(lacci))
                            {
                                lacciList.Add(lacci);
                            }
                            if (!cellNames.Contains(cellName))
                            {
                                cellNames.Add(cellName);
                            }
                        }
                        if (bcch != null && bcch > 0 && bcch < 65535 && !freqList.Contains((int)bcch))
                        {
                            freqList.Add((int)bcch);
                        }
                        //newManager.Add(tp);
                        tpPrev = tp;
                    }
                    #endregion

                    StringBuilder laccis = new StringBuilder();
                    foreach (string lacci in lacciList)
                    {
                        if (laccis.Length > 0)
                        {
                            laccis.Append(" | ");
                        }
                        laccis.Append(lacci);
                    }
                    StringBuilder cellNameStr = new StringBuilder();
                    foreach (string name in cellNames)
                    {
                        if (cellNameStr.Length > 0)
                        {
                            cellNameStr.Append(" | ");
                        }
                        cellNameStr.Append(name);
                    }
                    StringBuilder bcchs = new StringBuilder();
                    foreach (int bcch in freqList)
                    {
                        if (bcchs.Length > 0)
                        {
                            bcchs.Append(" | ");
                        }
                        bcchs.Append(bcch.ToString());
                    }
                    CDLowSpeedESResult result = new CDLowSpeedESResult(data, tps, distance, laccis.ToString(), bcchs.ToString(), cellNameStr.ToString());
                    searchWeakSinrRoad(tps, result);
                    calDisMultiCoverage(tps, result);
                    calOtherInfo(tps, result);
                    calOtherInfo2(tps, result);
                    result.GetResult();
                    result.LowPointCount = lowSpeedCount;
                    result.lowPercent = ((float)lowSpeedCount) / tps.Count * 100;
                    result.SN = sn;
                    //获取原来智能预判表的内容
                    result.SpecificType = ogrResult.SpecificType;
                    result.Detail = ogrResult.Detail;
                    result.Suggest = ogrResult.Suggest;
                    result.FileID = ogrResult.FileID;
                    result.EventID = ogrResult.EventID;
                    result.SeqID = ogrResult.SeqID;
                    result.EventTime = ogrResult.EventTime;
                    result.RelationID = ogrResult.RelationID;
                    result.Event = ogrResult.Event;
                    result.NodeResultSet = ogrResult.NodeResultSet;

                    lowSpeedInfoList_LTE.Add(result);
                }
            }
        }
        public List<CDLowSpeedESResult> GetResult()
        {
            return this.lowSpeedInfoList_LTE;
        }
        private List<CDLowSpeedESResult> lowSpeedInfoList_LTE = null;
        private readonly List<int> testpointBandIdx = new List<int>();
        private void calOtherInfo2(List<TestPoint> tps, CDLowSpeedESResult result)
        {
            int tra_ModeCount = 0;
            int tra_Mode3Count = 0;

            int time1Sum = 0;
            int time2Sum = 0;
            int lastRank = 0;
            int lastIdx = -1;

            //foreach (TestPoint tp in tps)
            for (int i = 0; i < tps.Count; i++)
            {
                TestPoint tp = tps[i];
                short? transMode = (short?)tp["lte_Transmission_Mode"];
                result.transmission_mode += transMode == null ? 0 : (short)transMode;

                if (transMode != null)
                {
                    tra_ModeCount++;
                    if (transMode == 3)
                    {
                        tra_Mode3Count++;
                    }
                }

                short? rank_indicator = (short?)tp["lte_Rank_Indicator"];
                result.rank_indicator += rank_indicator == null ? 0 : (short)rank_indicator;

                dealRank2_indicator(tps, ref time1Sum, ref time2Sum, ref lastRank, ref lastIdx, i, tp);

                addResultData(result, tp);
            }

            result.transmission_mode = result.transmission_mode / tps.Count;
            //roadPart.transmission_mode3 = Math.Round(tra_Mode3Count * 100.0 / tra_ModeCount, 2);
            result.transmission_mode3 = Math.Round(tra_Mode3Count * 100.0 / tps.Count, 2);
            result.rank_indicator = result.rank_indicator / tps.Count;
            result.rank2_indicator = Math.Round(time2Sum * 100.0 / (time1Sum + time2Sum), 2);

            result.pdsch_rb_number = result.pdsch_rb_number / tps.Count;
            result.pdsch_bler = (result.pdsch_bler / tps.Count);
            result.pdsch_prb_num_s = result.pdsch_prb_num_s / tps.Count;
            result.pdcch_dl_grant_count = result.pdcch_dl_grant_count / tps.Count;
            result.ratio_DL_Code0_HARQ_ACK = result.ratio_DL_Code0_HARQ_ACK / tps.Count;
            result.ratio_DL_Code0_HARQ_NACK = result.ratio_DL_Code0_HARQ_NACK / tps.Count;
            result.ratio_DL_Code1_HARQ_ACK = result.ratio_DL_Code1_HARQ_ACK / tps.Count;
            result.ratio_DL_Code1_HARQ_NACK = result.ratio_DL_Code1_HARQ_NACK / tps.Count;
            result.AppType = this.getAppType(tps[0]);
        }

        private void addResultData(CDLowSpeedESResult result, TestPoint tp)
        {
            int? pdsch_rb_num = (int?)tp["lte_PDSCH_RB_Number"];
            result.pdsch_rb_number += pdsch_rb_num == null ? 0 : (int)pdsch_rb_num;

            float? pdsch_bler = (float?)tp["lte_PDSCH_BLER"];
            result.pdsch_bler += pdsch_bler == null ? 0 : (float)pdsch_bler;

            int? pdsch_prb_num_s = (int?)tp["lte_PDSCH_PRb_Num_s"];
            result.pdsch_prb_num_s += pdsch_prb_num_s == null ? 0 : (int)pdsch_prb_num_s;

            short? pdcch_dl_grant_count = (short?)tp["lte_PDCCH_DL_Grant_Count"];
            result.pdcch_dl_grant_count += pdcch_dl_grant_count == null ? 0 : (short)pdcch_dl_grant_count;

            float? ratio_dl_code0_harq_ack = (float?)tp["lte_Ratio_DL_Code0_HARQ_ACK"];
            result.ratio_DL_Code0_HARQ_ACK += ratio_dl_code0_harq_ack == null ? 0 : (float)ratio_dl_code0_harq_ack;

            float? ratio_dl_code0_harq_nack = (float?)tp["lte_Ratio_DL_Code0_HARQ_NACK"];
            result.ratio_DL_Code0_HARQ_NACK += ratio_dl_code0_harq_nack == null ? 0 : (float)ratio_dl_code0_harq_nack;

            float? ratio_dl_code1_harq_ack = (float?)tp["lte_Ratio_DL_Code1_HARQ_ACK"];
            result.ratio_DL_Code1_HARQ_ACK += ratio_dl_code1_harq_ack == null ? 0 : (float)ratio_dl_code1_harq_ack;

            float? ratio_dl_code1_harq_nack = (float?)tp["lte_Ratio_DL_Code1_HARQ_NACK"];
            result.ratio_DL_Code1_HARQ_NACK += ratio_dl_code1_harq_nack == null ? 0 : (float)ratio_dl_code1_harq_nack;
        }

        private void dealRank2_indicator(List<TestPoint> tps, ref int time1Sum, ref int time2Sum, ref int lastRank, ref int lastIdx, int i, TestPoint tp)
        {
            #region lte_Rank_Indicator质差双流比例
            bool added = false;
            int rank = 0;
            object obj = tp["lte_Rank_Indicator"];
            if (obj != null)
            {
                int.TryParse(obj.ToString(), out rank);
            }

            if (lastRank == 0 && (rank == 1 || rank == 2))
            {
                lastIdx = i;
                lastRank = rank;
            }
            if (testpointBandIdx.Contains(i))
            {
                addTime(tps, ref time1Sum, ref time2Sum, lastRank, lastIdx, tps[i - 1], ref added);
                lastIdx = i;
                lastRank = rank;
            }

            if (rank != lastRank)
            {
                addTime(tps, ref time1Sum, ref time2Sum, lastRank, lastIdx, tp, ref added);
                lastIdx = i;
            }
            lastRank = rank;

            if (i == tps.Count - 1 && !added)
            {//避免漏掉最后一段
                if (lastRank == 1)
                {
                    time1Sum += tp.Time - tps[lastIdx].Time;
                }
                else if (lastRank == 2)
                {
                    time2Sum += tp.Time - tps[lastIdx].Time;
                }
            }
            #endregion
        }

        private void addTime(List<TestPoint> tps, ref int time1Sum, ref int time2Sum, int lastRank, int lastIdx, TestPoint tp, ref bool added)
        {
            if (lastRank == 1)
            {
                time1Sum += tp.Time - tps[lastIdx].Time;
                added = true;
            }
            else if (lastRank == 2)
            {
                time2Sum += tp.Time - tps[lastIdx].Time;
                added = true;
            }
        }

        private string getAppType(TestPoint tp)
        {
            short? lteType = (short?)tp["lte_APP_type"];
            if (lteType == 2)
            {
                return "FTP下载";
            }
            else if (lteType == 3)
            {
                return "FTP上传";
            }
            else if (lteType == 12)
            {
                return "HTTP下载";
            }
            else if (lteType == 15)
            {
                return "邮件";
            }
            else if (lteType == 25)
            {
                return "视频业务";
            }
            return "";
        }
        private void calOtherInfo(List<TestPoint> tPntLst, CDLowSpeedESResult res)
        {
            res.meanPDSCHBLER = res.code0Mean = res.code1Mean = res.code0Max = res.code1Max = res.cqi0Mean 
                = res.cqi1Mean = res.code0_64QAMRate = res.code0_16QAMRate = res.code1_64QAMRate = res.code1_16QAMRate = 0;
            int code0Sum = 0, code1Sum = 0;
            foreach (TestPoint tPnt in tPntLst)
            {
                float? bler = (float?)tPnt["lte_PDSCH_BLER"];
                if (bler != null && bler >= 0 && bler <= 100)
                    res.meanPDSCHBLER += (float)bler;

                setMCSCodeDL(res, ref code0Sum, ref code1Sum, tPnt);

                short? cqi_cw0 = (short?)tPnt["lte_Wideband_CQI_for_CW0"];
                res.cqi0Mean += getShortValue(cqi_cw0);
                short? cqi_cw1 = (short?)tPnt["lte_Wideband_CQI_for_CW1"];
                res.cqi1Mean += getShortValue(cqi_cw1);

                int? code0_QAM64 = (int?)tPnt["lte_Times_QAM64_DLCode0"];
                int code0_64 = getIntValue(code0_QAM64);
                int? code0_QAM16 = (int?)tPnt["lte_Times_QAM16_DLCode0"];
                int code0_16 = getIntValue(code0_QAM16);
                int? code0_QPSK = (int?)tPnt["lte_Times_QPSK_DLCode0"];
                int code0_qk = getIntValue(code0_QPSK);

                res.code0_64QAMRate += getIntValue(code0_64 + code0_16 + code0_qk, code0_64) * 1.0f;
                res.code0_16QAMRate += getIntValue(code0_64 + code0_16 + code0_qk, code0_16) * 1.0f;

                int? code1_QAM64 = (int?)tPnt["lte_Times_QAM64_DLCode1"];
                int code1_64 = getIntValue(code1_QAM64);
                int? code1_QAM16 = (int?)tPnt["lte_Times_QAM16_DLCode1"];
                int code1_16 = getIntValue(code1_QAM16);
                int? code1_QPSK = (int?)tPnt["lte_Times_QPSK_DLCode1"];
                int code1_qk = getIntValue(code1_QPSK);

                res.code1_64QAMRate += getIntValue(code1_64 + code1_16 + code1_qk, code1_64) * 1.0f;
                res.code1_16QAMRate += getIntValue(code1_64 + code1_16 + code1_qk, code1_16) * 1.0f;
            }

            res.meanPDSCHBLER /= tPntLst.Count;
            res.code0Mean = code0Sum == 0 ? double.NaN : res.code0Mean / code0Sum;
            res.code1Mean = code1Sum == 0 ? double.NaN : res.code1Mean / code1Sum;
            res.code0Max = code0Sum == 0 ? double.NaN : 100 * res.code0Max / code0Sum;
            res.code1Max = code1Sum == 0 ? double.NaN : 100 * res.code1Max / code1Sum;
            res.cqi0Mean /= tPntLst.Count;
            res.cqi1Mean /= tPntLst.Count;
            res.code0_64QAMRate /= tPntLst.Count;
            res.code0_16QAMRate /= tPntLst.Count;
            res.code1_64QAMRate /= tPntLst.Count;
            res.code1_16QAMRate /= tPntLst.Count;
        }

        private void setMCSCodeDL(CDLowSpeedESResult res, ref int code0Sum, ref int code1Sum, TestPoint tPnt)
        {
            for (int i = 0; i < 32; i++)
            {
                short? code0 = (short?)tPnt["lte_MCSCode0_DL", i];
                code0 = code0 == null ? 0 : code0;
                res.code0Mean += (short)code0 * i;
                code0Sum += (short)code0;
                res.code0Max = res.code0Max > (short)code0 ? res.code0Max : (short)code0;

                short? code1 = (short?)tPnt["lte_MCSCode1_DL", i];
                code1 = code1 == null ? 0 : code1;
                res.code1Mean += (short)code1 * i;
                code1Sum += (short)code1;
                res.code1Max = res.code1Max > (short)code1 ? res.code1Max : (short)code0;
            }
        }

        private int getIntValue(int? value, int defaultValue = 0)
        {
            if (value == null)
            {
                return defaultValue;
            }
            else
            {
                return (int)value;
            }
        }

        private int getShortValue(short? value)
        {
            if (value == null)
            {
                return 0;
            }
            else
            {
                return (short)value;
            }
        }


        protected void calDisMultiCoverage(List<TestPoint> tPntLst, CDLowSpeedESResult res)
        {
            res.distanceMulticoverage = 0;
            TestPoint pntPrev = null;
            foreach (TestPoint tPnt in tPntLst)
            {
                if (isMultiCoverage(tPnt) && pntPrev != null)
                {
                    double distance = MathFuncs.GetDistance(tPnt.Longitude, tPnt.Latitude, pntPrev.Longitude, pntPrev.Latitude);
                    res.distanceMulticoverage += distance;
                }
                pntPrev = tPnt;
            }
        }
        private void searchWeakSinrRoad(List<TestPoint> tPointLst, CDLowSpeedESResult res)
        {
            res.weakSinrLst = new List<TestPoint>();
            res.weakSinrDistance = 0;
            LinkedList<TestPoint> tmpPntLst = new LinkedList<TestPoint>();
            WeakSinrInfo info = new WeakSinrInfo();
            foreach (TestPoint tPoint in tPointLst)
            {
                if (calDuration(tmpPntLst, tPoint, info))
                {
                    if (1.0f * info.WeakCnt / tmpPntLst.Count >= 0.7)
                    {
                        res.weakSinrDistance += info.Distance;
                        res.weakSinrLst.AddRange(tmpPntLst);
                        tmpPntLst.Clear();
                        info.StepDurLst.Clear();
                        info.StepDisLst.Clear();
                        info.WeakCntLst.Clear();
                        info.WeakCnt = 0;
                        info.Duration = 0;
                        info.Distance = 0;
                    }
                    else
                    {
                        info.Duration -= info.StepDurLst.First.Value;
                        info.Distance -= info.StepDisLst.First.Value;
                        info.WeakCnt -= info.WeakCntLst.First.Value;
                        tmpPntLst.RemoveFirst();
                        info.StepDurLst.RemoveFirst();
                        info.StepDisLst.RemoveFirst();
                        info.WeakCntLst.RemoveFirst();
                    }
                }
            }
        }
        private bool calDuration(LinkedList<TestPoint> tmpPntLst, TestPoint curPnt, WeakSinrInfo info)
        {
            if (tmpPntLst.Count > 0)
            {
                double dur = Math.Abs(tmpPntLst.Last.Value.Time - curPnt.Time);
                double dis = MathFuncs.GetDistance(tmpPntLst.Last.Value.Longitude, tmpPntLst.Last.Value.Latitude, 
                                                                            curPnt.Longitude, curPnt.Latitude);
                info.Duration += dur;
                info.StepDurLst.AddLast(dur);
                info.Distance += dis;
                info.StepDisLst.AddLast(dis);
            }
            tmpPntLst.AddLast(curPnt);
            bool isWeak = isWeakSinr(curPnt);
            info.WeakCnt += isWeak ? 1 : 0;
            info.WeakCntLst.AddLast(isWeak ? 1 : 0);
            return info.Duration >= 10;
        }
        private bool isWeakSinr(TestPoint tPoint)
        {
            float? sinr = (float?)tPoint["lte_SINR"];
            return sinr != null && (float)sinr < -1;
        }

        class WeakSinrInfo
        {
            public double Duration { get; set; }
            public double Distance { get; set; }
            public int WeakCnt { get; set; }
            public LinkedList<int> WeakCntLst { get; set; } = new LinkedList<int>();
            public LinkedList<double> StepDurLst { get; set; } = new LinkedList<double>();
            public LinkedList<double> StepDisLst { get; set; } = new LinkedList<double>();
        }
    }
}
