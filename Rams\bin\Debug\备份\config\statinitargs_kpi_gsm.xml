<?xml version="1.0" encoding="UTF-8"?>
<Configs>
	<Config name="StatParamCfg">
		<Item name="configs" typeName="IList">
			<Item typeName="IDictionary">
				<Item typeName="String" key="Name">GSM参数</Item>
				<Item typeName="String" key="FName"/>
				<Item typeName="IList" key="children">
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">GSM语音Idle模式参数</Item>
						<Item typeName="String" key="FName"/>
						<Item typeName="IList" key="children">
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">测试时长 viDuration</Item>
								<Item typeName="String" key="FName">viDuration</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">测试距离 viDistance</Item>
								<Item typeName="String" key="FName">viDistance</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">采样点总数 viSampleTotle</Item>
								<Item typeName="String" key="FName">viSampleTotle</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">信号强度</Item>
								<Item typeName="String" key="FName"/>
								<Item typeName="IList" key="children">
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">[-10,-45]的数目 viRxLev[0]</Item>
										<Item typeName="String" key="FName">viRxLev</Item>
										<Item typeName="Int32" key="FTag">0</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">[-46,-50]的数目 viRxLev[1]</Item>
										<Item typeName="String" key="FName">viRxLev</Item>
										<Item typeName="Int32" key="FTag">1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">[-51,-55]的数目 viRxLev[2]</Item>
										<Item typeName="String" key="FName">viRxLev</Item>
										<Item typeName="Int32" key="FTag">2</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">[-56,-60]的数目 viRxLev[3]</Item>
										<Item typeName="String" key="FName">viRxLev</Item>
										<Item typeName="Int32" key="FTag">3</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">[-61,-65]的数目 viRxLev[4]</Item>
										<Item typeName="String" key="FName">viRxLev</Item>
										<Item typeName="Int32" key="FTag">4</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">[-66,-70]的数目 viRxLev[5]</Item>
										<Item typeName="String" key="FName">viRxLev</Item>
										<Item typeName="Int32" key="FTag">5</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">[-71,-75]的数目 viRxLev[6]</Item>
										<Item typeName="String" key="FName">viRxLev</Item>
										<Item typeName="Int32" key="FTag">6</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">[-76,-80]的数目 viRxLev[7]</Item>
										<Item typeName="String" key="FName">viRxLev</Item>
										<Item typeName="Int32" key="FTag">7</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">-81db的数目 viRxLev[8]</Item>
										<Item typeName="String" key="FName">viRxLev</Item>
										<Item typeName="Int32" key="FTag">8</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">-82db的数目 viRxLev[9]</Item>
										<Item typeName="String" key="FName">viRxLev</Item>
										<Item typeName="Int32" key="FTag">9</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">-83db的数目 viRxLev[10]</Item>
										<Item typeName="String" key="FName">viRxLev</Item>
										<Item typeName="Int32" key="FTag">10</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">-84db的数目 viRxLev[11]</Item>
										<Item typeName="String" key="FName">viRxLev</Item>
										<Item typeName="Int32" key="FTag">11</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">-85db的数目 viRxLev[12]</Item>
										<Item typeName="String" key="FName">viRxLev</Item>
										<Item typeName="Int32" key="FTag">12</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">-86db的数目 viRxLev[13]</Item>
										<Item typeName="String" key="FName">viRxLev</Item>
										<Item typeName="Int32" key="FTag">13</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">-87db的数目 viRxLev[14]</Item>
										<Item typeName="String" key="FName">viRxLev</Item>
										<Item typeName="Int32" key="FTag">14</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">-88db的数目 viRxLev[15]</Item>
										<Item typeName="String" key="FName">viRxLev</Item>
										<Item typeName="Int32" key="FTag">15</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">-89db的数目 viRxLev[16]</Item>
										<Item typeName="String" key="FName">viRxLev</Item>
										<Item typeName="Int32" key="FTag">16</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">-90db的数目 viRxLev[17]</Item>
										<Item typeName="String" key="FName">viRxLev</Item>
										<Item typeName="Int32" key="FTag">17</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">-91db的数目 viRxLev[18]</Item>
										<Item typeName="String" key="FName">viRxLev</Item>
										<Item typeName="Int32" key="FTag">18</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">-92db的数目 viRxLev[19]</Item>
										<Item typeName="String" key="FName">viRxLev</Item>
										<Item typeName="Int32" key="FTag">19</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">-93db的数目 viRxLev[20]</Item>
										<Item typeName="String" key="FName">viRxLev</Item>
										<Item typeName="Int32" key="FTag">20</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">-94db的数目 viRxLev[21]</Item>
										<Item typeName="String" key="FName">viRxLev</Item>
										<Item typeName="Int32" key="FTag">21</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">-95db的数目 viRxLev[22]</Item>
										<Item typeName="String" key="FName">viRxLev</Item>
										<Item typeName="Int32" key="FTag">22</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">-96db的数目 viRxLev[23]</Item>
										<Item typeName="String" key="FName">viRxLev</Item>
										<Item typeName="Int32" key="FTag">23</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">-97db的数目 viRxLev[24]</Item>
										<Item typeName="String" key="FName">viRxLev</Item>
										<Item typeName="Int32" key="FTag">24</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">-98db的数目 viRxLev[25]</Item>
										<Item typeName="String" key="FName">viRxLev</Item>
										<Item typeName="Int32" key="FTag">25</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">[-99,-120]的数目 viRxLev[26]</Item>
										<Item typeName="String" key="FName">viRxLev</Item>
										<Item typeName="Int32" key="FTag">26</Item>
										<Item typeName="IList" key="children"/>
									</Item>
								</Item>
							</Item>
						</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">GSM语音Dedicated模式参数</Item>
						<Item typeName="String" key="FName"/>
						<Item typeName="IList" key="children">
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">测试时长 vdDuration</Item>
								<Item typeName="String" key="FName">vdDuration</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">测试距离 vdDistance</Item>
								<Item typeName="String" key="FName">vdDistance</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">采样点总数 vdSampleTotle</Item>
								<Item typeName="String" key="FName">vdSampleTotle</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">信号强度</Item>
								<Item typeName="String" key="FName"/>
								<Item typeName="IList" key="children">
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">[-10,-45]的数目 vdRxLev[0]</Item>
										<Item typeName="String" key="FName">vdRxLev</Item>
										<Item typeName="Int32" key="FTag">0</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">[-46,-50]的数目 vdRxLev[1]</Item>
										<Item typeName="String" key="FName">vdRxLev</Item>
										<Item typeName="Int32" key="FTag">1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">[-51,-55]的数目 vdRxLev[2]</Item>
										<Item typeName="String" key="FName">vdRxLev</Item>
										<Item typeName="Int32" key="FTag">2</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">[-56,-60]的数目 vdRxLev[3]</Item>
										<Item typeName="String" key="FName">vdRxLev</Item>
										<Item typeName="Int32" key="FTag">3</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">[-61,-65]的数目 vdRxLev[4]</Item>
										<Item typeName="String" key="FName">vdRxLev</Item>
										<Item typeName="Int32" key="FTag">4</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">[-66,-70]的数目 vdRxLev[5]</Item>
										<Item typeName="String" key="FName">vdRxLev</Item>
										<Item typeName="Int32" key="FTag">5</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">[-71,-75]的数目 vdRxLev[6]</Item>
										<Item typeName="String" key="FName">vdRxLev</Item>
										<Item typeName="Int32" key="FTag">6</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">[-76,-80]的数目 vdRxLev[7]</Item>
										<Item typeName="String" key="FName">vdRxLev</Item>
										<Item typeName="Int32" key="FTag">7</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">-81db的数目 vdRxLev[8]</Item>
										<Item typeName="String" key="FName">vdRxLev</Item>
										<Item typeName="Int32" key="FTag">8</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">-82db的数目 vdRxLev[9]</Item>
										<Item typeName="String" key="FName">vdRxLev</Item>
										<Item typeName="Int32" key="FTag">9</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">-83db的数目 vdRxLev[10]</Item>
										<Item typeName="String" key="FName">vdRxLev</Item>
										<Item typeName="Int32" key="FTag">10</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">-84db的数目 vdRxLev[11]</Item>
										<Item typeName="String" key="FName">vdRxLev</Item>
										<Item typeName="Int32" key="FTag">11</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">-85db的数目 vdRxLev[12]</Item>
										<Item typeName="String" key="FName">vdRxLev</Item>
										<Item typeName="Int32" key="FTag">12</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">-86db的数目 vdRxLev[13]</Item>
										<Item typeName="String" key="FName">vdRxLev</Item>
										<Item typeName="Int32" key="FTag">13</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">-87db的数目 vdRxLev[14]</Item>
										<Item typeName="String" key="FName">vdRxLev</Item>
										<Item typeName="Int32" key="FTag">14</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">-88db的数目 vdRxLev[15]</Item>
										<Item typeName="String" key="FName">vdRxLev</Item>
										<Item typeName="Int32" key="FTag">15</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">-89db的数目 vdRxLev[16]</Item>
										<Item typeName="String" key="FName">vdRxLev</Item>
										<Item typeName="Int32" key="FTag">16</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">-90db的数目 vdRxLev[17]</Item>
										<Item typeName="String" key="FName">vdRxLev</Item>
										<Item typeName="Int32" key="FTag">17</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">-91db的数目 vdRxLev[18]</Item>
										<Item typeName="String" key="FName">vdRxLev</Item>
										<Item typeName="Int32" key="FTag">18</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">-92db的数目 vdRxLev[19]</Item>
										<Item typeName="String" key="FName">vdRxLev</Item>
										<Item typeName="Int32" key="FTag">19</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">-93db的数目 vdRxLev[20]</Item>
										<Item typeName="String" key="FName">vdRxLev</Item>
										<Item typeName="Int32" key="FTag">20</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">-94db的数目 vdRxLev[21]</Item>
										<Item typeName="String" key="FName">vdRxLev</Item>
										<Item typeName="Int32" key="FTag">21</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">-95db的数目 vdRxLev[22]</Item>
										<Item typeName="String" key="FName">vdRxLev</Item>
										<Item typeName="Int32" key="FTag">22</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">-96db的数目 vdRxLev[23]</Item>
										<Item typeName="String" key="FName">vdRxLev</Item>
										<Item typeName="Int32" key="FTag">23</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">-97db的数目 vdRxLev[24]</Item>
										<Item typeName="String" key="FName">vdRxLev</Item>
										<Item typeName="Int32" key="FTag">24</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">-98db的数目 vdRxLev[25]</Item>
										<Item typeName="String" key="FName">vdRxLev</Item>
										<Item typeName="Int32" key="FTag">25</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">[-99,-120]的数目 vdRxLev[26]</Item>
										<Item typeName="String" key="FName">vdRxLev</Item>
										<Item typeName="Int32" key="FTag">26</Item>
										<Item typeName="IList" key="children"/>
									</Item>
								</Item>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">信号质量</Item>
								<Item typeName="String" key="FName"/>
								<Item typeName="IList" key="children">
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">信号质量0数量 vdRxQual[0]</Item>
										<Item typeName="String" key="FName">vdRxQual</Item>
										<Item typeName="Int32" key="FTag">0</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">信号质量1数量 vdRxQual[1]</Item>
										<Item typeName="String" key="FName">vdRxQual</Item>
										<Item typeName="Int32" key="FTag">1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">信号质量2数量 vdRxQual[2]</Item>
										<Item typeName="String" key="FName">vdRxQual</Item>
										<Item typeName="Int32" key="FTag">2</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">信号质量3数量 vdRxQual[3]</Item>
										<Item typeName="String" key="FName">vdRxQual</Item>
										<Item typeName="Int32" key="FTag">3</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">信号质量4数量 vdRxQual[4]</Item>
										<Item typeName="String" key="FName">vdRxQual</Item>
										<Item typeName="Int32" key="FTag">4</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">信号质量5数量 vdRxQual[5]</Item>
										<Item typeName="String" key="FName">vdRxQual</Item>
										<Item typeName="Int32" key="FTag">5</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">信号质量6数量 vdRxQual[6]</Item>
										<Item typeName="String" key="FName">vdRxQual</Item>
										<Item typeName="Int32" key="FTag">6</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">信号质量7数量 vdRxQual[7]</Item>
										<Item typeName="String" key="FName">vdRxQual</Item>
										<Item typeName="Int32" key="FTag">7</Item>
										<Item typeName="IList" key="children"/>
									</Item>
								</Item>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">PESQ</Item>
								<Item typeName="String" key="FName"/>
								<Item typeName="IList" key="children">
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">PESQ[0~1]数量 vdPesq[0]</Item>
										<Item typeName="String" key="FName">vdPesq</Item>
										<Item typeName="Int32" key="FTag">0</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">PESQ(1~2]数量 vdPesq[1]</Item>
										<Item typeName="String" key="FName">vdPesq</Item>
										<Item typeName="Int32" key="FTag">1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">PESQ(2~2.5]数量 vdPesq[2]</Item>
										<Item typeName="String" key="FName">vdPesq</Item>
										<Item typeName="Int32" key="FTag">2</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">PESQ(2.5~2.8]数量 vdPesq[3]</Item>
										<Item typeName="String" key="FName">vdPesq</Item>
										<Item typeName="Int32" key="FTag">3</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">PESQ(2.8~3]数量 vdPesq[4]</Item>
										<Item typeName="String" key="FName">vdPesq</Item>
										<Item typeName="Int32" key="FTag">4</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">PESQ(3~3.3]数量 vdPesq[5]</Item>
										<Item typeName="String" key="FName">vdPesq</Item>
										<Item typeName="Int32" key="FTag">5</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">PESQ(3.3~4]数量 vdPesq[6]</Item>
										<Item typeName="String" key="FName">vdPesq</Item>
										<Item typeName="Int32" key="FTag">6</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">PESQ(4.5]数量 vdPesq[7]</Item>
										<Item typeName="String" key="FName">vdPesq</Item>
										<Item typeName="Int32" key="FTag">7</Item>
										<Item typeName="IList" key="children"/>
									</Item>
								</Item>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">PESQ Value</Item>
								<Item typeName="String" key="FName"/>
								<Item typeName="IList" key="children">
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">PESQ[0~1]值之和 vdPesqValue[0]</Item>
										<Item typeName="String" key="FName">vdPesqValue</Item>
										<Item typeName="Int32" key="FTag">0</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">PESQ(1~2]值之和 vdPesqValue[1]</Item>
										<Item typeName="String" key="FName">vdPesqValue</Item>
										<Item typeName="Int32" key="FTag">1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">PESQ(2~2.5]值之和 vdPesqValue[2]</Item>
										<Item typeName="String" key="FName">vdPesqValue</Item>
										<Item typeName="Int32" key="FTag">2</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">PESQ(2.5~2.8]值之和 vdPesqValue[3]</Item>
										<Item typeName="String" key="FName">vdPesqValue</Item>
										<Item typeName="Int32" key="FTag">3</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">PESQ(2.8~3]值之和 vdPesqValue[4]</Item>
										<Item typeName="String" key="FName">vdPesqValue</Item>
										<Item typeName="Int32" key="FTag">4</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">PESQ(3~3.3]值之和 vdPesqValue[5]</Item>
										<Item typeName="String" key="FName">vdPesqValue</Item>
										<Item typeName="Int32" key="FTag">5</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">PESQ(3.3~4]值之和 vdPesqValue[6]</Item>
										<Item typeName="String" key="FName">vdPesqValue</Item>
										<Item typeName="Int32" key="FTag">6</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">PESQ(4.5]值之和 vdPesqValue[7]</Item>
										<Item typeName="String" key="FName">vdPesqValue</Item>
										<Item typeName="Int32" key="FTag">7</Item>
										<Item typeName="IList" key="children"/>
									</Item>
								</Item>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TA</Item>
								<Item typeName="String" key="FName"/>
								<Item typeName="IList" key="children">
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TA值为0的数目 vdTA[0]</Item>
										<Item typeName="String" key="FName">vdTA</Item>
										<Item typeName="Int32" key="FTag">0</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TA值为1的数目 vdTA[1]</Item>
										<Item typeName="String" key="FName">vdTA</Item>
										<Item typeName="Int32" key="FTag">1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TA值为2的数目 vdTA[2]</Item>
										<Item typeName="String" key="FName">vdTA</Item>
										<Item typeName="Int32" key="FTag">2</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TA值为3的数目 vdTA[3]</Item>
										<Item typeName="String" key="FName">vdTA</Item>
										<Item typeName="Int32" key="FTag">3</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TA值为4的数目 vdTA[4]</Item>
										<Item typeName="String" key="FName">vdTA</Item>
										<Item typeName="Int32" key="FTag">4</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TA值为5的数目 vdTA[5]</Item>
										<Item typeName="String" key="FName">vdTA</Item>
										<Item typeName="Int32" key="FTag">5</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TA值为6的数目 vdTA[6]</Item>
										<Item typeName="String" key="FName">vdTA</Item>
										<Item typeName="Int32" key="FTag">6</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TA值为7的数目 vdTA[7]</Item>
										<Item typeName="String" key="FName">vdTA</Item>
										<Item typeName="Int32" key="FTag">7</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TA值为8的数目 vdTA[8]</Item>
										<Item typeName="String" key="FName">vdTA</Item>
										<Item typeName="Int32" key="FTag">8</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TA值为9的数目 vdTA[9]</Item>
										<Item typeName="String" key="FName">vdTA</Item>
										<Item typeName="Int32" key="FTag">9</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TA值为10的数目 vdTA[10]</Item>
										<Item typeName="String" key="FName">vdTA</Item>
										<Item typeName="Int32" key="FTag">10</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TA值为11到15的采样点数目 vdTA[11]</Item>
										<Item typeName="String" key="FName">vdTA</Item>
										<Item typeName="Int32" key="FTag">11</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TA值为16到20的采样点数目 vdTA[12]</Item>
										<Item typeName="String" key="FName">vdTA</Item>
										<Item typeName="Int32" key="FTag">12</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TA值为21到30的采样点数目 vdTA[13]</Item>
										<Item typeName="String" key="FName">vdTA</Item>
										<Item typeName="Int32" key="FTag">13</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TA值为31到40的采样点数目 vdTA[14]</Item>
										<Item typeName="String" key="FName">vdTA</Item>
										<Item typeName="Int32" key="FTag">14</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TA值为41到64的采样点数目 vdTA[15]</Item>
										<Item typeName="String" key="FName">vdTA</Item>
										<Item typeName="Int32" key="FTag">15</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TA值为11到15的采样点值之和 vdTAValue[11]</Item>
										<Item typeName="String" key="FName">vdTAValue</Item>
										<Item typeName="Int32" key="FTag">0</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TA值为16到20的采样点值之和 vdTAValue[12]</Item>
										<Item typeName="String" key="FName">vdTAValue</Item>
										<Item typeName="Int32" key="FTag">1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TA值为21到30的采样点值之和 vdTAValue[13]</Item>
										<Item typeName="String" key="FName">vdTAValue</Item>
										<Item typeName="Int32" key="FTag">2</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TA值为31到40的采样点值之和 vdTAValue[14]</Item>
										<Item typeName="String" key="FName">vdTAValue</Item>
										<Item typeName="Int32" key="FTag">3</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TA值为41到64的采样点值之和 vdTAValue[15]</Item>
										<Item typeName="String" key="FName">vdTAValue</Item>
										<Item typeName="Int32" key="FTag">4</Item>
										<Item typeName="IList" key="children"/>
									</Item>
								</Item>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">语音编码</Item>
								<Item typeName="String" key="FName"/>
								<Item typeName="IList" key="children">
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">编码个数 vdSpeechCodec_FR</Item>
										<Item typeName="String" key="FName">vdSpeechCodec_FR</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">编码个数 vdSpeechCodec_HR</Item>
										<Item typeName="String" key="FName">vdSpeechCodec_HR</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">编码个数 vdSpeechCodec_EFR</Item>
										<Item typeName="String" key="FName">vdSpeechCodec_EFR</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">编码个数 vdSpeechCodec_AMR</Item>
										<Item typeName="String" key="FName">vdSpeechCodec_AMR</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">编码时长 vdSpeechCodecTime_FR</Item>
										<Item typeName="String" key="FName">vdSpeechCodecTime_FR</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">编码时长 vdSpeechCodecTime_HR</Item>
										<Item typeName="String" key="FName">vdSpeechCodecTime_HR</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">编码时长 vdSpeechCodecTime_EFR</Item>
										<Item typeName="String" key="FName">vdSpeechCodecTime_EFR</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">编码时长 vdSpeechCodecTime_AMR</Item>
										<Item typeName="String" key="FName">vdSpeechCodecTime_AMR</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
								</Item>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">C/I指标</Item>
								<Item typeName="String" key="FName"/>
								<Item typeName="IList" key="children">
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">C/I[-5,9)个数 vdCIWorst</Item>
										<Item typeName="String" key="FName">vdCIWorst</Item>
										<Item typeName="Int32" key="FTag">0</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">C/I[9,12)个数 vdCIWorst</Item>
										<Item typeName="String" key="FName">vdCIWorst</Item>
										<Item typeName="Int32" key="FTag">1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">C/I[12,15)个数 vdCIWorst</Item>
										<Item typeName="String" key="FName">vdCIWorst</Item>
										<Item typeName="Int32" key="FTag">2</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">C/I[15,20)个数 vdCIWorst</Item>
										<Item typeName="String" key="FName">vdCIWorst</Item>
										<Item typeName="Int32" key="FTag">3</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">C/I[20,35]个数 vdCIWorst</Item>
										<Item typeName="String" key="FName">vdCIWorst</Item>
										<Item typeName="Int32" key="FTag">4</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">C/I[-5,9)值之和 vdCIWorstValue</Item>
										<Item typeName="String" key="FName">vdCIWorstValue</Item>
										<Item typeName="Int32" key="FTag">0</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">C/I[9,12)值之和 vdCIWorstValue</Item>
										<Item typeName="String" key="FName">vdCIWorstValue</Item>
										<Item typeName="Int32" key="FTag">1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">C/I[12,15)值之和 vdCIWorstValue</Item>
										<Item typeName="String" key="FName">vdCIWorstValue</Item>
										<Item typeName="Int32" key="FTag">2</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">C/I[15,20)值之和 vdCIWorstValue</Item>
										<Item typeName="String" key="FName">vdCIWorstValue</Item>
										<Item typeName="Int32" key="FTag">3</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">C/I[20,35]值之和 vdCIWorstValue</Item>
										<Item typeName="String" key="FName">vdCIWorstValue</Item>
										<Item typeName="Int32" key="FTag">4</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">C/I[-5,9)个数 vdCIAvg</Item>
										<Item typeName="String" key="FName">vdCIAvg</Item>
										<Item typeName="Int32" key="FTag">0</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">C/I[9,12)个数 vdCIAvg</Item>
										<Item typeName="String" key="FName">vdCIAvg</Item>
										<Item typeName="Int32" key="FTag">1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">C/I[12,15)个数 vdCIAvg</Item>
										<Item typeName="String" key="FName">vdCIAvg</Item>
										<Item typeName="Int32" key="FTag">2</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">C/I[15,20)个数 vdCIAvg</Item>
										<Item typeName="String" key="FName">vdCIAvg</Item>
										<Item typeName="Int32" key="FTag">3</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">C/I[20,35]个数 vdCIAvg</Item>
										<Item typeName="String" key="FName">vdCIAvg</Item>
										<Item typeName="Int32" key="FTag">4</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">C/I[-5,9)值之和 vdCIAvgValue</Item>
										<Item typeName="String" key="FName">vdCIAvgValue</Item>
										<Item typeName="Int32" key="FTag">0</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">C/I[9,12)值之和 vdCIAvgValue</Item>
										<Item typeName="String" key="FName">vdCIAvgValue</Item>
										<Item typeName="Int32" key="FTag">1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">C/I[12,15)值之和 vdCIAvgValue</Item>
										<Item typeName="String" key="FName">vdCIAvgValue</Item>
										<Item typeName="Int32" key="FTag">2</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">C/I[15,20)值之和 vdCIAvgValue</Item>
										<Item typeName="String" key="FName">vdCIAvgValue</Item>
										<Item typeName="Int32" key="FTag">3</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">C/I[20,35]值之和 vdCIAvgValue</Item>
										<Item typeName="String" key="FName">vdCIAvgValue</Item>
										<Item typeName="Int32" key="FTag">4</Item>
										<Item typeName="IList" key="children"/>
									</Item>
								</Item>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">SQI指标</Item>
								<Item typeName="String" key="FName"/>
								<Item typeName="IList" key="children">
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">SQI[-20~-10)个数 vdSQI</Item>
										<Item typeName="String" key="FName">vdSQI</Item>
										<Item typeName="Int32" key="FTag">0</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">SQI[-10~-0)个数 vdSQI</Item>
										<Item typeName="String" key="FName">vdSQI</Item>
										<Item typeName="Int32" key="FTag">1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">SQI[0~10)个数 vdSQI</Item>
										<Item typeName="String" key="FName">vdSQI</Item>
										<Item typeName="Int32" key="FTag">2</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">SQI[10~20)个数 vdSQI</Item>
										<Item typeName="String" key="FName">vdSQI</Item>
										<Item typeName="Int32" key="FTag">3</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">SQI[20~30)个数 vdSQI</Item>
										<Item typeName="String" key="FName">vdSQI</Item>
										<Item typeName="Int32" key="FTag">4</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">SQIValue[-20~-10)值之和 vdSQIValue</Item>
										<Item typeName="String" key="FName">vdSQIValue</Item>
										<Item typeName="Int32" key="FTag">0</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">SQIValue[-10~-0)值之和 vdSQIValue</Item>
										<Item typeName="String" key="FName">vdSQIValue</Item>
										<Item typeName="Int32" key="FTag">1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">SQIValue[0~10)值之和 vdSQIValue</Item>
										<Item typeName="String" key="FName">vdSQIValue</Item>
										<Item typeName="Int32" key="FTag">2</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">SQIValue[10~20)值之和 vdSQIValue</Item>
										<Item typeName="String" key="FName">vdSQIValue</Item>
										<Item typeName="Int32" key="FTag">3</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">SQIValue[20~30)值之和 vdSQIValue</Item>
										<Item typeName="String" key="FName">vdSQIValue</Item>
										<Item typeName="Int32" key="FTag">4</Item>
										<Item typeName="IList" key="children"/>
									</Item>
								</Item>
							</Item>
						</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">GSM数据业务参数</Item>
						<Item typeName="String" key="FName"/>
						<Item typeName="IList" key="children">
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">GSM测试采样点数</Item>
								<Item typeName="String" key="FName">gSampleTotleGSM</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">总测试时长</Item>
								<Item typeName="String" key="FName">gDuration</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">GPRS测试时长</Item>
								<Item typeName="String" key="FName">gDuration_GPRS</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">EDGE测试时长</Item>
								<Item typeName="String" key="FName">gDuration_EDGE</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">总测试距离</Item>
								<Item typeName="String" key="FName">gDistance</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">GPRS测试距离</Item>
								<Item typeName="String" key="FName">gDistance_GPRS</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">EDGE测试距离</Item>
								<Item typeName="String" key="FName">gDistance_EDGE</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">Rxlev</Item>
								<Item typeName="String" key="FName"/>
								<Item typeName="IList" key="children">
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_GSM_RSCP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_02010101</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_GSM_RSCP_10_45</Item>
										<Item typeName="String" key="FName">Gx_02010102</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_GSM_RSCP_46_50</Item>
										<Item typeName="String" key="FName">Gx_02010103</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_GSM_RSCP_51_55</Item>
										<Item typeName="String" key="FName">Gx_02010104</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_GSM_RSCP_56_60</Item>
										<Item typeName="String" key="FName">Gx_02010105</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_GSM_RSCP_61_65</Item>
										<Item typeName="String" key="FName">Gx_02010106</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_GSM_RSCP_66_70</Item>
										<Item typeName="String" key="FName">Gx_02010107</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_GSM_RSCP_71_75</Item>
										<Item typeName="String" key="FName">Gx_02010108</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_GSM_RSCP_76_80</Item>
										<Item typeName="String" key="FName">Gx_02010109</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_GSM_RSCP_81</Item>
										<Item typeName="String" key="FName">Gx_0201010A</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_GSM_RSCP_82</Item>
										<Item typeName="String" key="FName">Gx_0201010B</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_GSM_RSCP_83</Item>
										<Item typeName="String" key="FName">Gx_0201010C</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_GSM_RSCP_84</Item>
										<Item typeName="String" key="FName">Gx_0201010D</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_GSM_RSCP_85</Item>
										<Item typeName="String" key="FName">Gx_0201010E</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_GSM_RSCP_86</Item>
										<Item typeName="String" key="FName">Gx_0201010F</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_GSM_RSCP_87</Item>
										<Item typeName="String" key="FName">Gx_02010110</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_GSM_RSCP_88</Item>
										<Item typeName="String" key="FName">Gx_02010111</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_GSM_RSCP_89</Item>
										<Item typeName="String" key="FName">Gx_02010112</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_GSM_RSCP_90</Item>
										<Item typeName="String" key="FName">Gx_02010113</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_GSM_RSCP_91</Item>
										<Item typeName="String" key="FName">Gx_02010114</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_GSM_RSCP_92</Item>
										<Item typeName="String" key="FName">Gx_02010115</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_GSM_RSCP_93</Item>
										<Item typeName="String" key="FName">Gx_02010116</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_GSM_RSCP_94</Item>
										<Item typeName="String" key="FName">Gx_02010117</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_GSM_RSCP_95</Item>
										<Item typeName="String" key="FName">Gx_02010118</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_GSM_RSCP_96</Item>
										<Item typeName="String" key="FName">Gx_02010119</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_GSM_RSCP_97</Item>
										<Item typeName="String" key="FName">Gx_0201011A</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_GSM_RSCP_98</Item>
										<Item typeName="String" key="FName">Gx_0201011B</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_GSM_RSCP_MAX</Item>
										<Item typeName="String" key="FName">Gx_0201011C</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_GSM_RSCP_MIN</Item>
										<Item typeName="String" key="FName">Gx_0201011D</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
								</Item>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">RLC</Item>
								<Item typeName="String" key="FName"/>
								<Item typeName="IList" key="children">
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_GPRS_RLC_DL_THR_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_03010401</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_GPRS_RLC_DL_THR_SUM</Item>
										<Item typeName="String" key="FName">Gx_03010402</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_GPRS_RLC_UL_THR_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_03010403</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_GPRS_RLC_UL_THR_SUM</Item>
										<Item typeName="String" key="FName">Gx_03010404</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_EDGE_RLC_DL_THR_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_03010801</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_EDGE_RLC_DL_THR_SUM</Item>
										<Item typeName="String" key="FName">Gx_03010802</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_EDGE_RLC_UL_THR_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_03010803</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_EDGE_RLC_UL_THR_SUM</Item>
										<Item typeName="String" key="FName">Gx_03010804</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
								</Item>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">BLER</Item>
								<Item typeName="String" key="FName"/>
								<Item typeName="IList" key="children">
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_GPRS_RLC_BLER_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_04010401</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_GPRS_RLC_BLER_SUM</Item>
										<Item typeName="String" key="FName">Gx_04010402</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_GPRS_RLC_BLER_MAX</Item>
										<Item typeName="String" key="FName">Gx_04010403</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_GPRS_RLC_BLER_MIN</Item>
										<Item typeName="String" key="FName">Gx_04010404</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_EDGE_RLC_BLER_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_04010801</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_EDGE_RLC_BLER_SUM</Item>
										<Item typeName="String" key="FName">Gx_04010802</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_EDGE_RLC_BLER_MAX</Item>
										<Item typeName="String" key="FName">Gx_04010803</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_EDGE_RLC_BLER_MIN</Item>
										<Item typeName="String" key="FName">Gx_04010804</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
								</Item>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">APP</Item>
								<Item typeName="String" key="FName"/>
								<Item typeName="IList" key="children">
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_GPRS_FTP_Download_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050101040201</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_GPRS_FTP_Download_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050101040202</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_GPRS_FTP_Download_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050101040203</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_GPRS_FTP_Download_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050101040204</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_GPRS_WAP_Download_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050101040601</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_GPRS_WAP_Download_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050101040602</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_GPRS_WAP_Download_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050101040603</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_GPRS_WAP_Download_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050101040604</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_GPRS_Http_Download_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050101040C01</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_GPRS_Http_Download_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050101040C02</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_GPRS_Http_Download_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050101040C03</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_GPRS_Http_Download_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050101040C04</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_EDGE_FTP_Download_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050101080201</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_EDGE_FTP_Download_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050101080202</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_EDGE_FTP_Download_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050101080203</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_EDGE_FTP_Download_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050101080204</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_EDGE_WAP_Download_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050101080601</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_EDGE_WAP_Download_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050101080602</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_EDGE_WAP_Download_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050101080603</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_EDGE_WAP_Download_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050101080604</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_EDGE_Http_Download_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050101080C01</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_EDGE_Http_Download_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050101080C02</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_EDGE_Http_Download_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050101080C03</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_EDGE_Http_Download_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050101080C04</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_GPRS_FTP_Upload_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050102040301</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_GPRS_FTP_Upload_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050102040302</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_GPRS_FTP_Upload_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050102040303</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_GPRS_FTP_Upload_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050102040304</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_GPRS_WAP_Upload_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050102040701</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_GPRS_WAP_Upload_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050102040702</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_GPRS_WAP_Upload_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050102040703</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_GPRS_WAP_Upload_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050102040704</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_GPRS_Http_Upload_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050102040D01</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_GPRS_Http_Upload_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050102040D02</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_GPRS_Http_Upload_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050102040D03</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_GPRS_Http_Upload_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050102040D04</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_EDGE_FTP_Upload_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050102080301</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_EDGE_FTP_Upload_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050102080302</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_EDGE_FTP_Upload_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050102080303</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_EDGE_FTP_Upload_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050102080304</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_EDGE_WAP_Upload_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050102080701</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_EDGE_WAP_Upload_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050102080702</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_EDGE_WAP_Upload_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050102080703</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_EDGE_WAP_Upload_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050102080704</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_EDGE_Http_Upload_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050102080D01</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_EDGE_Http_Upload_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050102080D02</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_EDGE_Http_Upload_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050102080D03</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_EDGE_Http_Upload_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050102080D04</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
								</Item>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">MCS</Item>
								<Item typeName="String" key="FName"/>
								<Item typeName="IList" key="children">
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_EDGE_FTP_Download_MCS1包数量</Item>
										<Item typeName="String" key="FName">Gx_0701080201</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_EDGE_FTP_Download_MCS2包数量</Item>
										<Item typeName="String" key="FName">Gx_0701080202</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_EDGE_FTP_Download_MCS3包数量</Item>
										<Item typeName="String" key="FName">Gx_0701080203</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_EDGE_FTP_Download_MCS4包数量</Item>
										<Item typeName="String" key="FName">Gx_0701080204</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_EDGE_FTP_Download_MCS5包数量</Item>
										<Item typeName="String" key="FName">Gx_0701080205</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_EDGE_FTP_Download_MCS6包数量</Item>
										<Item typeName="String" key="FName">Gx_0701080206</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_EDGE_FTP_Download_MCS7包数量</Item>
										<Item typeName="String" key="FName">Gx_0701080207</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_EDGE_FTP_Download_MCS8包数量</Item>
										<Item typeName="String" key="FName">Gx_0701080208</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_EDGE_FTP_Download_MCS9包数量</Item>
										<Item typeName="String" key="FName">Gx_0701080209</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_EDGE_FTP_Download_MCS总包数量</Item>
										<Item typeName="String" key="FName">Gx_070108020A</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GSM_DT_EDGE_FTP_Download_MCS总采样点数</Item>
										<Item typeName="String" key="FName">Gx_070108020B</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
								</Item>
							</Item>
						</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">事件参数</Item>
						<Item typeName="String" key="FName"/>
						<Item typeName="IList" key="children">
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">MO_Call_Attempt 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">0</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">MT_Call_Attempt 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">Call_Attempt_Retry 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">2</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">MO Call CM_ReEstablishment 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">904</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">MT Call CM_ReEstablishment 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">905</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">MO_Call_Established 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">3</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">MT_Call_Established 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">4</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">MO_Drop_Call 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">5</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">MO Drop Call_NotNormal 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">902</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">MO Drop Call_ReEstablish 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">906</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">MT_Drop_Call 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">6</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">MT Drop Call_NotNormal 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">903</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">MT Drop Call_ReEstablish 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">907</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">MO_Call_Fail 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">7</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">MT_Call_Fail 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">8</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">MO_Block_Call 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">9</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">MT_Block_Call 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">10</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">MO_Call_End 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">11</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">MT_Call_End 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">12</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">MO_Call_Setup 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">13</Item>
								<Item typeName="IList" key="children">
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">cm service request时差</Item>
										<Item typeName="String" key="FName">value1</Item>
										<Item typeName="Int32" key="FTag">13</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">channelrequest时差</Item>
										<Item typeName="String" key="FName">value2</Item>
										<Item typeName="Int32" key="FTag">13</Item>
										<Item typeName="IList" key="children"/>
									</Item>
								</Item>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">MT_Call_Setup 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">14</Item>
								<Item typeName="IList" key="children">
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">Paging Response时差</Item>
										<Item typeName="String" key="FName">value1</Item>
										<Item typeName="Int32" key="FTag">14</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">channelrequest时差</Item>
										<Item typeName="String" key="FName">value2</Item>
										<Item typeName="Int32" key="FTag">14</Item>
										<Item typeName="IList" key="children"/>
									</Item>
								</Item>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">Handover_Command 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">15</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">Handover_Success 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">16</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">Handover_Failure 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">17</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">Location_Area_Update_Request 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">18</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">Location_Area_Update_Success 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">19</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">Location_Area_Update_Failure 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">20</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">GPRS_Attach_Request 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">21</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">GPRS_Attach_Accept 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">22</Item>
								<Item typeName="IList" key="children">
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">GPRS Attach 时延</Item>
										<Item typeName="String" key="FName">value1</Item>
										<Item typeName="Int32" key="FTag">22</Item>
										<Item typeName="IList" key="children"/>
									</Item>
								</Item>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">GPRS_Attach_Failure 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">23</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">GPRS Detach Request 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">24</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">GPRS Detach Accept 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">25</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">Routing_Area_Update_Request 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">26</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">Routing_Area_Update_Accept 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">27</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">Routing_Area_Update_Reject 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">28</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">Active_PDP_Context_Request 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">29</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">Active_PDP_Context_Accept 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">30</Item>
								<Item typeName="IList" key="children">
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">PDP激活时间</Item>
										<Item typeName="String" key="FName">value1</Item>
										<Item typeName="Int32" key="FTag">30</Item>
										<Item typeName="IList" key="children"/>
									</Item>
								</Item>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">Active_PDP_Context_Reject 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">31</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">Request_PDP_Context_Activation 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">32</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">Request_PDP_Context_Activation_Reject 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">33</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">Deactive_PDP_Context_Request 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">34</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">Deactive_PDP_Context_Accept 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">35</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">Modify_PDP_Context_Request 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">36</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">Modify_PDP_Context_Accept 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">37</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">Modify_PDP_Context_Reject 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">38</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">Cell_Reselection 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">39</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">Weak Coverage 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">40</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">MoMtDrop 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">41</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">Weak Quality 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">42</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">GPRSDrop 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">43</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WAP_Logon_Success 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">44</Item>
								<Item typeName="IList" key="children">
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WAP登陆时间</Item>
										<Item typeName="String" key="FName">value1</Item>
										<Item typeName="Int32" key="FTag">44</Item>
										<Item typeName="IList" key="children"/>
									</Item>
								</Item>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WAP_Logon_Failure 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">45</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WAP_Page_Refresh_Success 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">46</Item>
								<Item typeName="IList" key="children">
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WAP刷新时间</Item>
										<Item typeName="String" key="FName">value1</Item>
										<Item typeName="Int32" key="FTag">46</Item>
										<Item typeName="IList" key="children"/>
									</Item>
								</Item>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WAP_Page_Refresh_Failure 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">47</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WAP_Download_Success 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">48</Item>
								<Item typeName="IList" key="children">
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WAP下载时间</Item>
										<Item typeName="String" key="FName">value1</Item>
										<Item typeName="Int32" key="FTag">48</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WAP下载字节数</Item>
										<Item typeName="String" key="FName">value2</Item>
										<Item typeName="Int32" key="FTag">48</Item>
										<Item typeName="IList" key="children"/>
									</Item>
								</Item>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WAP_Download_Failure 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">49</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WAP_Upload_Success 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">50</Item>
								<Item typeName="IList" key="children">
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WAP上传时间</Item>
										<Item typeName="String" key="FName">value1</Item>
										<Item typeName="Int32" key="FTag">50</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WAP上传字节数</Item>
										<Item typeName="String" key="FName">value2</Item>
										<Item typeName="Int32" key="FTag">50</Item>
										<Item typeName="IList" key="children"/>
									</Item>
								</Item>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WAP_Upload_Failure 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">51</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WAP_Kjava_Download_Success 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">52</Item>
								<Item typeName="IList" key="children">
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WAP下载时间</Item>
										<Item typeName="String" key="FName">value1</Item>
										<Item typeName="Int32" key="FTag">52</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WAP下载字节数</Item>
										<Item typeName="String" key="FName">value2</Item>
										<Item typeName="Int32" key="FTag">53</Item>
										<Item typeName="IList" key="children"/>
									</Item>
								</Item>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WAP_Kjava_Download_Failure 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">53</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">Ping_success 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">54</Item>
								<Item typeName="IList" key="children">
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">ping时延</Item>
										<Item typeName="String" key="FName">value1</Item>
										<Item typeName="Int32" key="FTag">54</Item>
										<Item typeName="IList" key="children"/>
									</Item>
								</Item>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">Ping_fail 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">55</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">FTP_Download_Begin 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">56</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">FTP_Download_Success 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">57</Item>
								<Item typeName="IList" key="children">
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">FTP下载时间</Item>
										<Item typeName="String" key="FName">value1</Item>
										<Item typeName="Int32" key="FTag">57</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">FTP下载字节数</Item>
										<Item typeName="String" key="FName">value2</Item>
										<Item typeName="Int32" key="FTag">57</Item>
										<Item typeName="IList" key="children"/>
									</Item>
								</Item>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">FTP_Download_Fail 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">58</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">FTP_Upload_Begin 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">59</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">FTP_Upload_Success 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">60</Item>
								<Item typeName="IList" key="children">
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">FTP上传时间</Item>
										<Item typeName="String" key="FName">value1</Item>
										<Item typeName="Int32" key="FTag">60</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">FTP上传字节数</Item>
										<Item typeName="String" key="FName">value2</Item>
										<Item typeName="Int32" key="FTag">60</Item>
										<Item typeName="IList" key="children"/>
									</Item>
								</Item>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">FTP_Upload_Fail 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">61</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">MMS_Send_Success 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">62</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">MMS_Send_Fail 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">63</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">MMS_Push 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">64</Item>
								<Item typeName="IList" key="children">
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">PUSH时延</Item>
										<Item typeName="String" key="FName">value1</Item>
										<Item typeName="Int32" key="FTag">64</Item>
										<Item typeName="IList" key="children"/>
									</Item>
								</Item>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">MMS_Retrieve_Success 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">65</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">MMS_Retrieve_Fail 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">66</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">MMS_P2P_Success 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">67</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">MMS_P2P_Fail 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">68</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">SMS_Send_Out 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">69</Item>
								<Item typeName="IList" key="children">
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">发送时延</Item>
										<Item typeName="String" key="FName">value1</Item>
										<Item typeName="Int32" key="FTag">69</Item>
										<Item typeName="IList" key="children"/>
									</Item>
								</Item>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">SMS_Send_Timeout 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">70</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">SMS_Receieved 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">71</Item>
								<Item typeName="IList" key="children"/>
							</Item>
						</Item>
					</Item>
				</Item>
			</Item>
		</Item>
	</Config>
</Configs>
