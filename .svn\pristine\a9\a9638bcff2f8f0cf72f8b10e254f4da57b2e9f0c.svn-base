﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class XtraSetRedundantForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.labelControlBand = new DevExpress.XtraEditors.LabelControl();
            this.numRxLevDValue = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.label4 = new System.Windows.Forms.Label();
            this.numRxLevThreshold = new DevExpress.XtraEditors.SpinEdit();
            this.simpleButton1 = new DevExpress.XtraEditors.SimpleButton();
            this.simpleButton2 = new DevExpress.XtraEditors.SimpleButton();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.cbxCoFreqType = new DevExpress.XtraEditors.ComboBoxEdit();
            this.chkCoFreq = new DevExpress.XtraEditors.CheckEdit();
            this.labelControl5 = new DevExpress.XtraEditors.LabelControl();
            this.spinEditInvalidThresold = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            ((System.ComponentModel.ISupportInitialize)(this.numRxLevDValue.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRxLevThreshold.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbxCoFreqType.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkCoFreq.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditInvalidThresold.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // labelControlBand
            // 
            this.labelControlBand.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControlBand.Appearance.Options.UseFont = true;
            this.labelControlBand.Location = new System.Drawing.Point(21, 25);
            this.labelControlBand.Name = "labelControlBand";
            this.labelControlBand.Size = new System.Drawing.Size(96, 12);
            this.labelControlBand.TabIndex = 3;
            this.labelControlBand.Text = "与最强信号差异 <";
            // 
            // numRxLevDValue
            // 
            this.numRxLevDValue.EditValue = new decimal(new int[] {
            6,
            0,
            0,
            0});
            this.numRxLevDValue.Location = new System.Drawing.Point(128, 22);
            this.numRxLevDValue.Name = "numRxLevDValue";
            this.numRxLevDValue.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numRxLevDValue.Properties.Appearance.Options.UseFont = true;
            this.numRxLevDValue.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numRxLevDValue.Properties.IsFloatValue = false;
            this.numRxLevDValue.Properties.Mask.AutoComplete = DevExpress.XtraEditors.Mask.AutoCompleteType.None;
            this.numRxLevDValue.Properties.Mask.EditMask = "N00";
            this.numRxLevDValue.Size = new System.Drawing.Size(82, 20);
            this.numRxLevDValue.TabIndex = 4;
            // 
            // labelControl1
            // 
            this.labelControl1.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl1.Appearance.Options.UseFont = true;
            this.labelControl1.Location = new System.Drawing.Point(216, 25);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(12, 12);
            this.labelControl1.TabIndex = 5;
            this.labelControl1.Text = "dB";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label4.Location = new System.Drawing.Point(52, 57);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(65, 12);
            this.label4.TabIndex = 8;
            this.label4.Text = "信号强度 >";
            // 
            // numRxLevThreshold
            // 
            this.numRxLevThreshold.EditValue = new decimal(new int[] {
            80,
            0,
            0,
            -2147483648});
            this.numRxLevThreshold.Location = new System.Drawing.Point(128, 54);
            this.numRxLevThreshold.Name = "numRxLevThreshold";
            this.numRxLevThreshold.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numRxLevThreshold.Properties.Appearance.Options.UseFont = true;
            this.numRxLevThreshold.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numRxLevThreshold.Properties.IsFloatValue = false;
            this.numRxLevThreshold.Properties.Mask.EditMask = "N00";
            this.numRxLevThreshold.Size = new System.Drawing.Size(81, 20);
            this.numRxLevThreshold.TabIndex = 9;
            // 
            // simpleButton1
            // 
            this.simpleButton1.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.simpleButton1.Appearance.Options.UseFont = true;
            this.simpleButton1.Location = new System.Drawing.Point(68, 171);
            this.simpleButton1.Name = "simpleButton1";
            this.simpleButton1.Size = new System.Drawing.Size(75, 23);
            this.simpleButton1.TabIndex = 10;
            this.simpleButton1.Text = "确定";
            this.simpleButton1.Click += new System.EventHandler(this.simpleButton1_Click);
            // 
            // simpleButton2
            // 
            this.simpleButton2.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.simpleButton2.Appearance.Options.UseFont = true;
            this.simpleButton2.Location = new System.Drawing.Point(159, 171);
            this.simpleButton2.Name = "simpleButton2";
            this.simpleButton2.Size = new System.Drawing.Size(75, 23);
            this.simpleButton2.TabIndex = 11;
            this.simpleButton2.Text = "取消";
            this.simpleButton2.Click += new System.EventHandler(this.simpleButton2_Click);
            // 
            // labelControl2
            // 
            this.labelControl2.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl2.Appearance.Options.UseFont = true;
            this.labelControl2.Location = new System.Drawing.Point(216, 57);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(18, 12);
            this.labelControl2.TabIndex = 12;
            this.labelControl2.Text = "dBm";
            // 
            // cbxCoFreqType
            // 
            this.cbxCoFreqType.Location = new System.Drawing.Point(128, 118);
            this.cbxCoFreqType.Name = "cbxCoFreqType";
            this.cbxCoFreqType.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.cbxCoFreqType.Properties.Appearance.Options.UseFont = true;
            this.cbxCoFreqType.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cbxCoFreqType.Properties.Items.AddRange(new object[] {
            "BCCH&TCH",
            "BCCH Only",
            "TCH Only"});
            this.cbxCoFreqType.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            this.cbxCoFreqType.Size = new System.Drawing.Size(106, 20);
            this.cbxCoFreqType.TabIndex = 16;
            // 
            // chkCoFreq
            // 
            this.chkCoFreq.Location = new System.Drawing.Point(66, 118);
            this.chkCoFreq.Name = "chkCoFreq";
            this.chkCoFreq.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.chkCoFreq.Properties.Appearance.Options.UseFont = true;
            this.chkCoFreq.Properties.Caption = "同频";
            this.chkCoFreq.Size = new System.Drawing.Size(57, 19);
            this.chkCoFreq.TabIndex = 15;
            this.chkCoFreq.CheckedChanged += new System.EventHandler(this.chkCoFreq_CheckedChanged);
            // 
            // labelControl5
            // 
            this.labelControl5.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl5.Appearance.Options.UseFont = true;
            this.labelControl5.Location = new System.Drawing.Point(216, 89);
            this.labelControl5.Name = "labelControl5";
            this.labelControl5.Size = new System.Drawing.Size(18, 12);
            this.labelControl5.TabIndex = 20;
            this.labelControl5.Text = "dBm";
            // 
            // spinEditInvalidThresold
            // 
            this.spinEditInvalidThresold.EditValue = new decimal(new int[] {
            95,
            0,
            0,
            -2147483648});
            this.spinEditInvalidThresold.Location = new System.Drawing.Point(128, 86);
            this.spinEditInvalidThresold.Name = "spinEditInvalidThresold";
            this.spinEditInvalidThresold.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditInvalidThresold.Properties.Appearance.Options.UseFont = true;
            this.spinEditInvalidThresold.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditInvalidThresold.Properties.IsFloatValue = false;
            this.spinEditInvalidThresold.Properties.Mask.EditMask = "N00";
            this.spinEditInvalidThresold.Properties.MaxValue = new decimal(new int[] {
            10,
            0,
            0,
            -2147483648});
            this.spinEditInvalidThresold.Properties.MinValue = new decimal(new int[] {
            120,
            0,
            0,
            -2147483648});
            this.spinEditInvalidThresold.Size = new System.Drawing.Size(82, 20);
            this.spinEditInvalidThresold.TabIndex = 18;
            // 
            // labelControl4
            // 
            this.labelControl4.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl4.Appearance.Options.UseFont = true;
            this.labelControl4.Location = new System.Drawing.Point(45, 89);
            this.labelControl4.Name = "labelControl4";
            this.labelControl4.Size = new System.Drawing.Size(72, 12);
            this.labelControl4.TabIndex = 19;
            this.labelControl4.Text = "无效点场强 <";
            // 
            // XtraSetRedundantForm
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(268, 218);
            this.Controls.Add(this.labelControl5);
            this.Controls.Add(this.spinEditInvalidThresold);
            this.Controls.Add(this.labelControl4);
            this.Controls.Add(this.cbxCoFreqType);
            this.Controls.Add(this.chkCoFreq);
            this.Controls.Add(this.labelControl2);
            this.Controls.Add(this.simpleButton2);
            this.Controls.Add(this.simpleButton1);
            this.Controls.Add(this.numRxLevThreshold);
            this.Controls.Add(this.label4);
            this.Controls.Add(this.labelControl1);
            this.Controls.Add(this.numRxLevDValue);
            this.Controls.Add(this.labelControlBand);
            this.Name = "XtraSetRedundantForm";
            this.Text = "小区冗余覆盖度分析条件设置";
            ((System.ComponentModel.ISupportInitialize)(this.numRxLevDValue.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRxLevThreshold.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbxCoFreqType.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkCoFreq.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditInvalidThresold.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraEditors.LabelControl labelControlBand;
        private DevExpress.XtraEditors.SpinEdit numRxLevDValue;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private System.Windows.Forms.Label label4;
        private DevExpress.XtraEditors.SpinEdit numRxLevThreshold;
        private DevExpress.XtraEditors.SimpleButton simpleButton1;
        private DevExpress.XtraEditors.SimpleButton simpleButton2;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.ComboBoxEdit cbxCoFreqType;
        private DevExpress.XtraEditors.CheckEdit chkCoFreq;
        private DevExpress.XtraEditors.LabelControl labelControl5;
        private DevExpress.XtraEditors.SpinEdit spinEditInvalidThresold;
        private DevExpress.XtraEditors.LabelControl labelControl4;
    }
}