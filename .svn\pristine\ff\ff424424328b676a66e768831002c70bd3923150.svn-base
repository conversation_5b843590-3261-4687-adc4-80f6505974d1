﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    /// <summary>
    /// 重叠覆盖结果
    /// </summary>
    public class RoadMultiCoverageInfoBase
    {
        public bool IsNoneMainPoint { get; set; } = false;
        public bool InvalidatePoint { get; set; } = false;
        public string MainCellName { get; set; }
        public TestPoint TestPoint { get; set; } = null;
        //public MultiCoverBandType BandType;
        public string FileName { get; set; }
        public DateTime DateTime { get; set; }
        public double Longitude { get; set; }
        public double Latitude { get; set; }
        public float RsrpMax { get; set; }
        public int RelativeLevel { get; set; }
        public int AbsoluteLevel { get; set; }
        public int RelANDAbsLevel { get; set; }

        public bool Within(double x1, double y1, double x2, double y2)
        {
            if (Longitude < x1 || Longitude > x2 || Latitude < y1 || Latitude > y2)
            {
                return false;
            }
            return true;
        }

        protected string getNameStr(List<string> nameList)
        {
            StringBuilder names = new StringBuilder();
            foreach (string name in nameList)
            {
                names.Append(name + ";");
            }
            return names.ToString();
        }
    }

    /// <summary>
    /// 重叠覆盖栅格化结果
    /// </summary>
    public class GridRoadMultiCoverageInfoBase : GridBase
    {
        public GridRoadMultiCoverageInfoBase(double ltLongitude, double ltLatitude)
            : base(ltLongitude, ltLatitude)
        {
        }

        double RelLevelSum = 0;
        double AbsLevelSum = 0;
        double RelAndAbsLevelSum = 0;
        int testPointCount = 0;
        double rsrpSum = 0;

        public int RelativeLevel { get; protected set; }
        public int AbsoluteLevel { get; protected set; }
        public int RelANDAbsLevel { get; protected set; }
        public double RsrpAvg { get; protected set; }

        public void Add(RoadMultiCoverageInfoBase info)
        {
            RelLevelSum += info.RelativeLevel;
            AbsLevelSum += info.AbsoluteLevel;
            RelAndAbsLevelSum += info.RelANDAbsLevel;
            rsrpSum += info.RsrpMax;
            testPointCount++;
        }

        public void Calculate()
        {
            RelativeLevel = (int)(Math.Round(RelLevelSum / testPointCount, 0));
            AbsoluteLevel = (int)(Math.Round(AbsLevelSum / testPointCount, 0));
            RelANDAbsLevel = (int)(Math.Round(RelAndAbsLevelSum / testPointCount, 0));
            RsrpAvg = Math.Round(rsrpSum / testPointCount, 2);
        }
    }

    public class CellRsrpInfoBase<T> where T : ICell
    {
        public float Rsrp { get; set; }

        public int Earfcn { get; set; }

        public int Pci { get; set; }

        public T Cell { get; set; }

        public string StrName
        {
            get
            {
                if (Cell != null)
                {
                    return Cell.Name;
                }
                return string.Format("{0}_{1}", Earfcn, Pci);
            }
        }

        public CellRsrpInfoBase(float rsrp, int earfcn, int pci, T cell)
        {
            this.Rsrp = rsrp;
            this.Earfcn = earfcn;
            this.Pci = pci;
            this.Cell = cell;
        }
    }
}
