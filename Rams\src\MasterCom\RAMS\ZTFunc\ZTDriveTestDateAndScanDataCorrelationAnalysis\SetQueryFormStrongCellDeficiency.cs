﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.Util;
using MasterCom.RAMS.Grid;
using MasterCom.MTGis;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class SetQueryFormStrongCellDeficiency : BaseDialog
    {
        protected ItemSelectionPanel projPanelDT;
        protected ItemSelectionPanel projPanelScan;
        protected ItemSelectionPanel servPanelDT;
        protected ItemSelectionPanel servPanelScan;
        protected QueryCondition CurCondition;
        protected MapFormItemSelection ItemSelection;
        public bool isFirstTimeFireShowForm { get; set; } = true;
        Dictionary<TestPoint, tpGridposition> tpGridDicDT = null;
        Dictionary<TestPoint, tpGridposition> tpGridDicScan = null;
        List<markGrid> markGirds = null; 
        List<TestPoint> tpInconnectionDTList = null;
        List<TestPoint> tpInconnectionScanList = null;

        public double GRID_SPAN_LONG { get; set; } = CD.ATOM_SPAN_LONG;//当前运算中使用的值
        public double GRID_SPAN_LAT { get; set; } = CD.ATOM_SPAN_LAT;//当前运算中使用的值
        public DbRect bounds { get; set; }

        /// <summary>
        /// 栅格内采样点的最强信号小区字典
        /// </summary>
        Dictionary<string, Cell> gridStrongestCellDic = null;
        /// <summary>
        /// 栅格对应其最强小区电平字典
        /// </summary>
        Dictionary<string, int> gridStrongestRxlevDic = null;
        /// <summary>
        /// 栅格对应的异常采样点数目字典
        /// </summary>
        Dictionary<string, int> gridAbnormalSampleCountDic = null;
        /// <summary>
        /// 栅格对应的采样点数目字典
        /// </summary>
        Dictionary<string, int> gridSampleCountDic = null;

        public SetQueryFormStrongCellDeficiency(MainModel mModel, MapFormItemSelection itemSelection, QueryCondition condition)
        {
            InitializeComponent();

            mainModel = mModel;
            CurCondition = condition;
            ItemSelection = itemSelection;

            dateTimePickerBeginTimeDT.Value = DateTime.Now.AddDays(-1);
            dateTimePickerBeginTimeScan.Value = DateTime.Now.AddDays(-1);
            dateTimePickerEndTimeDT.Value = DateTime.Now;
            dateTimePickerEndTimeScan.Value = DateTime.Now;

            listViewProjectDT.Items.Clear();
            listViewProjectScan.Items.Clear();
            if (mainModel.CategoryManager["Project"] != null)
            {
                projPanelDT = new ItemSelectionPanel(toolStripDropDownProjectDT, listViewProjectDT, lbProjCountDT, itemSelection, "Project", true);
                projPanelScan = new ItemSelectionPanel(toolStripDropDownProjectScan, listViewProjectScan, lbProjCountScan, itemSelection, "Project", true);
                toolStripDropDownProjectDT.Items.Clear();
                toolStripDropDownProjectScan.Items.Clear();
                projPanelDT.FreshItems();
                projPanelScan.FreshItems();
                toolStripDropDownProjectDT.Items.Add(new ToolStripControlHost(projPanelDT));
                toolStripDropDownProjectScan.Items.Add(new ToolStripControlHost(projPanelScan));
            }

            listViewServiceDT.Items.Clear();
            listViewServiceScan.Items.Clear();
            if (mainModel.CategoryManager["ServiceType"] != null)
            {
                servPanelDT = new ItemSelectionPanel(toolStripDropDownServiceDT, listViewServiceDT, lbSvCountDT, itemSelection, "ServiceType", true);
                servPanelScan = new ItemSelectionPanel(toolStripDropDownServiceScan, listViewServiceScan, lbSvCountScan, itemSelection, "ServiceType", true);
                toolStripDropDownServiceDT.Items.Clear();
                toolStripDropDownServiceScan.Items.Clear();
                servPanelDT.FreshItems();
                servPanelScan.FreshItems();
                toolStripDropDownServiceDT.Items.Add(new ToolStripControlHost(servPanelDT));
                toolStripDropDownServiceScan.Items.Add(new ToolStripControlHost(servPanelScan));
            }

            setDefaultService();

            if (mainModel.ConditionRecorderSCellDeficiencyByRegion != null && mainModel.ConditionRecorderSCellDeficiencyByRegion.kind == conditionRecorder.Kind.StrongCellDeficiencyByRegion)
            {
                conditionRecorder recorder = mainModel.ConditionRecorderSCellDeficiencyByRegion;
                this.dateTimePickerBeginTimeDT.Value = recorder.dateTimePickerBeginTimeDTValue;
                this.dateTimePickerBeginTimeScan.Value = recorder.dateTimePickerBeginTimeScanValue;
                this.dateTimePickerEndTimeDT.Value = recorder.dateTimePickerEndTimeDTValue;
                this.dateTimePickerEndTimeScan.Value = recorder.dateTimePickerEndTimeScanValue;
                this.listViewProjectDT.Items.Clear();
                this.listViewProjectScan.Items.Clear();
                this.listViewServiceDT.Items.Clear();
                this.listViewServiceScan.Items.Clear();
                foreach (saveItem item in recorder.listViewProjectDTItems)
                {
                    ListViewItem lvi = new ListViewItem();
                    lvi.Text = item.text;
                    lvi.Tag = item.tag;
                    this.listViewProjectDT.Items.Add(lvi);
                }
                foreach (saveItem item in recorder.listViewProjectScanItems)
                {
                    ListViewItem lvi = new ListViewItem();
                    lvi.Text = item.text;
                    lvi.Tag = item.tag;
                    this.listViewProjectScan.Items.Add(lvi);
                }
                foreach (saveItem item in recorder.listViewServiceDTItems)
                {
                    ListViewItem lvi = new ListViewItem();
                    lvi.Text = item.text;
                    lvi.Tag = item.tag;
                    this.listViewServiceDT.Items.Add(lvi);
                }
                foreach (saveItem item in recorder.listViewServiceScanItems)
                {
                    ListViewItem lvi = new ListViewItem();
                    lvi.Text = item.text;
                    lvi.Tag = item.tag;
                    this.listViewServiceScan.Items.Add(lvi);
                }
                this.trackBarChannel.Value = recorder.trackBarChannelValue;
                this.trackBarChannel_Scroll(null, null);
            }
        }

        private void setDefaultService()
        {
            ListViewItem lviDt = new ListViewItem();
            lviDt.Text = "GSM语音业务";
            lviDt.Tag = 1;
            this.listViewServiceDT.Items.Add(lviDt);

            ListViewItem lviScan = new ListViewItem();
            lviScan.Text = "扫频业务";
            lviScan.Tag = 12;
            this.listViewServiceScan.Items.Add(lviScan);
        }

        private void buttonProjDT_Click(object sender, EventArgs e)
        {
            System.Drawing.Point pt = new System.Drawing.Point(buttonProjDT.Width, buttonProjDT.Height);
            toolStripDropDownProjectDT.Show(buttonProjDT, pt, ToolStripDropDownDirection.BelowLeft);
        }

        private void buttonProjScan_Click(object sender, EventArgs e)
        {
            System.Drawing.Point pt = new System.Drawing.Point(buttonProjScan.Width, buttonProjScan.Height);
            toolStripDropDownProjectScan.Show(buttonProjScan, pt, ToolStripDropDownDirection.BelowLeft);
        }

        private void buttonServDT_Click(object sender, EventArgs e)
        {
            System.Drawing.Point pt = new System.Drawing.Point(buttonServDT.Width, buttonServDT.Height);
            toolStripDropDownServiceDT.Show(buttonServDT, pt, ToolStripDropDownDirection.BelowLeft);
        }

        private void buttonServScan_Click(object sender, EventArgs e)
        {
            System.Drawing.Point pt = new System.Drawing.Point(buttonServScan.Width, buttonServScan.Height);
            toolStripDropDownServiceScan.Show(buttonServScan, pt, ToolStripDropDownDirection.BelowLeft);
        }

        private void trackBarChannel_Scroll(object sender, EventArgs e)
        {
            if (trackBarChannel.Value == 1)
            {
                label900.ForeColor = Color.Red;
                label1800.ForeColor = Color.Black;
                label900and1800.ForeColor = Color.Black;
            }
            else if (trackBarChannel.Value == 2)
            {
                label900.ForeColor = Color.Black;
                label1800.ForeColor = Color.Red;
                label900and1800.ForeColor = Color.Black;
            }
            else if (trackBarChannel.Value == 3)
            {
                label900.ForeColor = Color.Black;
                label1800.ForeColor = Color.Black;
                label900and1800.ForeColor = Color.Red;
            }
        }

        private bool isValidCondition()
        {
            if (dateTimePickerBeginTimeDT.Value > dateTimePickerEndTimeDT.Value)
            {
                MessageBox.Show("查询路测数据，结束时间必须大于开始时间。");
            }
            if (dateTimePickerBeginTimeScan.Value > dateTimePickerEndTimeScan.Value)
            {
                MessageBox.Show("查询扫频数据，结束时间必须大于开始时间。");
            }
            else if (listViewProjectDT.Items.Count <= 0)
            {
                MessageBox.Show("查询路测数据，至少需要选择一个项目。");
            }
            else if (listViewServiceDT.Items.Count <= 0)
            {
                MessageBox.Show("查询路测数据，至少需要选择一个业务。");
            }
            else if (listViewProjectScan.Items.Count <= 0)
            {
                MessageBox.Show("查询扫频数据，至少需要选择一个项目。");
            }
            else if (listViewServiceScan.Items.Count <= 0)
            {
                MessageBox.Show("查询扫频数据，至少需要选择一个业务。");
            }
            else
                return true;
            return false;
        }

        private void buttonQuery_Click(object sender, EventArgs e)
        {
            conditionRecorder recorder = new conditionRecorder();
            recorder.fill(conditionRecorder.Kind.StrongCellDeficiencyByRegion, this.dateTimePickerBeginTimeDT.Value, this.dateTimePickerBeginTimeScan.Value, this.dateTimePickerEndTimeDT.Value
                , this.dateTimePickerEndTimeScan.Value, this.trackBarChannel.Value);
            recorder.fillCollection(this.listViewProjectDT.Items, this.listViewProjectScan.Items, this.listViewServiceDT.Items, this.listViewServiceScan.Items);
            mainModel.ConditionRecorderSCellDeficiencyByRegion = recorder;

            mainModel.IsPrepareWithoutGridPartParam = true;
            mainModel.MainForm.GetMapForm().GetGridShowLayer().CurUsingGridColorFixed = null;

            tpGridDicDT = queryDTData();           
            if (tpGridDicDT == null)
                return;
            if (tpGridDicDT.Count == 0)
                return;
            tpGridDicScan = queryScanData();
            if (tpGridDicScan == null)
                return;
            if (tpGridDicScan.Count == 0)
                return;

            MapGridLayer gridShowLayer = mainModel.MainForm.GetMapForm().GetGridShowLayer();
            GRID_SPAN_LONG = gridShowLayer.GRID_SPAN_LONG;
            GRID_SPAN_LAT = gridShowLayer.GRID_SPAN_LAT;

            DIYQueryCoverGridByRegion GridQuery = new DIYQueryCoverGridByRegion(mainModel);
            GridQuery.SetQueryCondition(CurCondition);
            GridQuery.Query();
            WaitBox.Show(compareTpGridDicDTToTpGridDicScan);//保留DT和Scan都在同一栅格内的采样点集合
            WaitBox.Show(calAbnormalSamplePct);  //计算各个栅格内异常采样点的比例
            WaitBox.Show(showAbSamplePctGis);  //Gis显示异常采样点占比
            MapGridLayer.NeedFreshFullImg = true;
            mainModel.FireGridCoverQueried(this);
            mainModel.RefreshLegend();
            mainModel.IsPrepareWithoutGridPartParam = false; //查询结束，回复原始的布尔值，不影响其他栅格绘图
            clearTempObjects();
            this.Close();
        }

        private Dictionary<TestPoint, tpGridposition> queryDTData()
        {
            if (isValidCondition())
            {
                DIYSampleToGridQueryByRegion sampleToGridQueryForDT = new DIYSampleToGridQueryByRegion(mainModel);
                CurCondition.Periods.Clear();
                CurCondition.Periods.Add(new TimePeriod(dateTimePickerBeginTimeDT.Value.Date, dateTimePickerEndTimeDT.Value.Date.AddDays(1).AddMilliseconds(-1)));
                foreach (ListViewItem item in listViewProjectDT.Items)
                {
                    CurCondition.Projects.Add((byte)(int)item.Tag);
                }
                foreach (ListViewItem item in listViewServiceDT.Items)
                {
                    CurCondition.ServiceTypes.Add((byte)(int)item.Tag);
                }
                CurCondition.DistrictIDs.Clear();
                CurCondition.DistrictIDs.Add(mainModel.DistrictID);
                sampleToGridQueryForDT.CurCondition = CurCondition;
                sampleToGridQueryForDT.Query();

                return sampleToGridQueryForDT.tpGridDic;
            }
            return new Dictionary<TestPoint, tpGridposition>();
        }

        private Dictionary<TestPoint, tpGridposition> queryScanData()
        {
            DIYSampleToGridQueryByRegion sampleToGridQueryForScan = new DIYSampleToGridQueryByRegion(mainModel);
            CurCondition.Periods.Clear();
            CurCondition.Periods.Add(new TimePeriod(dateTimePickerBeginTimeScan.Value.Date, dateTimePickerEndTimeScan.Value.Date.AddDays(1).AddMilliseconds(-1)));
            CurCondition.Projects.Clear();
            CurCondition.ServiceTypes.Clear();
            foreach (ListViewItem item in listViewProjectScan.Items)
            {
                CurCondition.Projects.Add((byte)(int)item.Tag);
            }
            foreach (ListViewItem item in listViewServiceScan.Items)
            {
                CurCondition.ServiceTypes.Add((byte)(int)item.Tag);
            }
            CurCondition.DistrictIDs.Clear();
            CurCondition.DistrictIDs.Add(mainModel.DistrictID);
            sampleToGridQueryForScan.CurCondition = CurCondition;
            sampleToGridQueryForScan.Query();

            return sampleToGridQueryForScan.tpGridDic;
        }

        public void compareTpGridDicDTToTpGridDicScan()
        {
            try
            {
                WaitBox.Text = "正在调整频段……";
                WaitBox.ProgressPercent = 30;

                markGirds = new List<markGrid>();
                tpInconnectionDTList = new List<TestPoint>();
                tpInconnectionScanList = new List<TestPoint>();

                List<TestPoint> curBandTestpointDT = new List<TestPoint>();
                List<TestPoint> curBandTestpointScan = new List<TestPoint>();
                addCurBandTestpointDT(curBandTestpointDT);
                addCurBandTestpointScan(curBandTestpointScan);

                WaitBox.ProgressPercent = 50;

                markGrid markGrid = null;
                bool needAdd = false;
                addTP(curBandTestpointDT, ref markGrid, ref needAdd);

                addScanTP(curBandTestpointScan, ref markGrid, ref needAdd);

                foreach (markGrid mg in markGirds)
                {
                    tpInconnectionDTList.AddRange(mg.testPointDtList);
                    tpInconnectionScanList.AddRange(mg.testPointScanList);
                }

                WaitBox.ProgressPercent = 90;
            }
            catch
            {
                //continue
            }
            finally
            {
                //---释放内存--------
                tpGridDicDT = null;
                tpGridDicScan = null;
                //--------------------

                WaitBox.Close();
            }
        }

        private void addCurBandTestpointDT(List<TestPoint> curBandTestpointDT)
        {
            foreach (TestPoint tpDT in tpGridDicDT.Keys)
            {
                if (this.trackBarChannel.Value == 1)   //选择900频段
                {
                    short? bcch = (short?)tpDT["BCCH"];
                    if (bcch != null && bcch < 512)
                    {
                        curBandTestpointDT.Add(tpDT);
                    }
                }
                else if (this.trackBarChannel.Value == 2)  //选择1800频段
                {
                    short? bcch = (short?)tpDT["BCCH"];
                    if (bcch != null && bcch >= 512)
                    {
                        curBandTestpointDT.Add(tpDT);
                    }
                }
                else  //选择900和1800频段
                {
                    curBandTestpointDT.Add(tpDT);
                }
            }
        }

        private void addCurBandTestpointScan(List<TestPoint> curBandTestpointScan)
        {
            foreach (TestPoint tpScan in tpGridDicScan.Keys)
            {
                if (this.trackBarChannel.Value == 1)   //选择900频段
                {
                    short? bcch = (short?)(int?)tpScan["GSCAN_BCCH", 0];
                    if (bcch != null && bcch < 512)
                    {
                        curBandTestpointScan.Add(tpScan);
                    }
                }
                else if (this.trackBarChannel.Value == 2)  //选择1800频段
                {
                    short? bcch = (short?)(int?)tpScan["GSCAN_BCCH", 0];
                    if (bcch != null && bcch >= 512)
                    {
                        curBandTestpointScan.Add(tpScan);
                    }
                }
                else  //选择900和1800频段
                {
                    curBandTestpointScan.Add(tpScan);
                }
            }
        }

        private void addTP(List<TestPoint> curBandTestpointDT, ref markGrid markGrid, ref bool needAdd)
        {
            foreach (TestPoint tpDT in curBandTestpointDT)
            {
                tpGridposition posDT = tpGridDicDT[tpDT];
                if (markGirds.Count == 0)
                {
                    markGrid = new markGrid(posDT.row, posDT.col);
                    markGrid.hasDtSample = true;
                    markGrid.testPointDtList.Add(tpDT);
                    markGirds.Add(markGrid);
                }
                else
                {
                    dealTPMarkGirds(ref markGrid, ref needAdd, tpDT, posDT);
                }
            }
        }

        private void dealTPMarkGirds(ref markGrid markGrid, ref bool needAdd, TestPoint tpDT, tpGridposition posDT)
        {
            int num = markGirds.Count;
            int count = 0;
            foreach (markGrid mg in markGirds)
            {
                if (mg.row == posDT.row && mg.col == posDT.col)
                {
                    mg.testPointDtList.Add(tpDT);  //往栅格加入该采样点
                    needAdd = false;
                }
                else
                {
                    count++;
                    if (count == num)
                    {
                        needAdd = true;
                        markGrid = new markGrid(posDT.row, posDT.col);
                        markGrid.testPointDtList.Add(tpDT);
                        markGrid.hasDtSample = true;
                    }
                }
            }
            if (needAdd)    //将含有路测采样点的栅格收集标记起来
                markGirds.Add(markGrid);
        }

        private void addScanTP(List<TestPoint> curBandTestpointScan, ref markGrid markGrid, ref bool needAdd)
        {
            foreach (TestPoint tpScan in curBandTestpointScan)
            {
                tpGridposition posScan = tpGridDicScan[tpScan];
                dealScanTPMarkGirds(ref markGrid, ref needAdd, tpScan, posScan);
            }
        }

        private void dealScanTPMarkGirds(ref markGrid markGrid, ref bool needAdd, TestPoint tpScan, tpGridposition posScan)
        {
            int num = markGirds.Count;
            int count = 0;
            foreach (markGrid mg in markGirds)
            {
                if (mg.row == posScan.row && mg.col == posScan.col)
                {
                    mg.testPointScanList.Add(tpScan);  //往栅格加入该采样点
                    mg.hasScanSample = true;
                    needAdd = false;
                }
                else
                {
                    count++;
                    if (count == num)
                    {
                        markGrid = new markGrid(posScan.row, posScan.col);
                        markGrid.hasScanSample = true;
                        markGrid.testPointScanList.Add(tpScan);
                        needAdd = true;
                    }
                }
            }
            if (needAdd)    //将含有扫频采样点的栅格收集标记起来
                markGirds.Add(markGrid);
        }

        private void calAbnormalSamplePct()
        {
            try
            {
                WaitBox.Text = "计算各个栅格内异常采样点的比例";
                WaitBox.ProgressPercent = 20;
                gridStrongestCellDic = new Dictionary<string, Cell>();
                gridStrongestRxlevDic = new Dictionary<string, int>();         
                dealScanGridSample();
                WaitBox.ProgressPercent = 50;
                dealGridSample();
                WaitBox.ProgressPercent = 90;
            }
            catch
            {
                //continue
            }
            finally
            {
                //释放内存--------------------
                tpInconnectionScanList = null;
                tpInconnectionDTList = null;
                gridStrongestCellDic = null;
                //-----------------------------

                WaitBox.Close();
            }
        }

        private void dealScanGridSample()
        {
            foreach (TestPoint testpointScan in tpInconnectionScanList)  //统计扫频数据在栅格内的最强小区
            {
                bool hasStrongCell = false;
                float? rxLevSub = (float?)testpointScan["GSCAN_RxLev", 0];
                short? bcch = (short?)(int?)testpointScan["GSCAN_BCCH", 0];
                byte? bsic = (byte?)(int?)testpointScan["GSCAN_BSIC", 0];
                if (rxLevSub != null && bcch != null && bsic != null)
                {
                    Cell cell = CellManager.GetInstance().GetNearestCell(testpointScan.DateTime, (short)bcch, (byte)bsic, testpointScan.Longitude, testpointScan.Latitude);
                    hasStrongCell = getHasStrongCell(testpointScan, hasStrongCell, cell);
                    addGridStrongestCell(testpointScan, hasStrongCell, rxLevSub, cell);
                }
            }
        }

        private bool getHasStrongCell(TestPoint testpoint, bool hasStrongCell, Cell cell)
        {
            if (cell != null)
            {
                if (MainModel.GetInstance().SystemConfigInfo.distLimit)//距离限制设置
                {
                    if (cell.GetDistance(testpoint.Longitude, testpoint.Latitude) < CD.MAX_COV_DISTANCE_GSM)
                    {
                        hasStrongCell = true;
                    }
                }
                else
                {
                    hasStrongCell = true;
                }
            }

            return hasStrongCell;
        }

        private void addGridStrongestCell(TestPoint testpointScan, bool hasStrongCell, float? rxLevSub, Cell cell)
        {
            if (hasStrongCell)
            {
                int pGridUnitRow = (int)(Math.Round((bounds.y2 - testpointScan.Latitude) / GRID_SPAN_LAT));
                int pGridUnitCol = (int)(Math.Round((testpointScan.Longitude - bounds.x1) / GRID_SPAN_LONG));

                if (pGridUnitRow >= mainModel.CurGridColorUnitMatrix.MaxRowIdx)
                {
                    pGridUnitRow = mainModel.CurGridColorUnitMatrix.MaxRowIdx - 1;
                }
                else if (pGridUnitCol >= mainModel.CurGridColorUnitMatrix.MaxColIdx)
                {
                    pGridUnitCol = mainModel.CurGridColorUnitMatrix.MaxColIdx - 1;
                }

                string rowCol = pGridUnitRow + "_" + pGridUnitCol;
                if (!gridStrongestCellDic.ContainsKey(rowCol))
                {
                    gridStrongestRxlevDic.Add(rowCol, (int)rxLevSub);
                    gridStrongestCellDic.Add(rowCol, cell);
                }
                else
                {
                    if (rxLevSub > gridStrongestRxlevDic[rowCol])
                    {
                        gridStrongestRxlevDic[rowCol] = (int)rxLevSub;
                        gridStrongestCellDic[rowCol] = cell;
                    }
                }
            }
        }

        private void dealGridSample()
        {
            gridAbnormalSampleCountDic = new Dictionary<string, int>();
            gridSampleCountDic = new Dictionary<string, int>();
            foreach (TestPoint testpointDt in tpInconnectionDTList)    //用路测数据查找采样点对应的小区，
            {
                bool hasStrongCell = false;
                bool hasStrongNbcell = false;
                float? rxLevSub = (float?)(short?)testpointDt["RxLevSub"];
                Cell cell = CellManager.GetInstance().GetNearestCell(testpointDt.DateTime, (ushort?)(int?)testpointDt["LAC"], (ushort?)(int?)testpointDt["CI"], (short?)testpointDt["BCCH"], (byte?)testpointDt["BSIC"], testpointDt.Longitude, testpointDt.Latitude);
                if (rxLevSub != null)
                {
                    hasStrongCell = getHasStrongCell(testpointDt, hasStrongCell, cell);
                }

                List<Cell> nbcellList = getNbcellList(testpointDt, ref hasStrongNbcell);

                string rowCol = getRowCol(testpointDt);

                addValidGridSample(hasStrongCell, hasStrongNbcell, cell, nbcellList, rowCol);
            }
        }

        private static List<Cell> getNbcellList(TestPoint testpointDt, ref bool hasStrongNbcell)
        {
            List<Cell> nbcellList = new List<Cell>();
            for (int i = 0; i < 6; i++)
            {
                short? nbcch = (short?)testpointDt["N_BCCH", i];
                byte? nbsic = (byte?)testpointDt["N_BSIC", i];
                float? nRxLev = (float?)(short?)testpointDt["N_RxLev", i];

                if (nbcch != null && nbsic != null && nRxLev != null)
                {
                    Cell nbCell = CellManager.GetInstance().GetNearestCell(testpointDt.DateTime, (short)nbcch, (byte)nbsic, testpointDt.Longitude, testpointDt.Latitude);
                    if (nbCell != null
                        && MainModel.GetInstance().SystemConfigInfo.distLimit
                        && nbCell.GetDistance(testpointDt.Longitude, testpointDt.Latitude) < CD.MAX_COV_DISTANCE_GSM)//距离限制设置
                    {
                        nbcellList.Add(nbCell);
                        hasStrongNbcell = true;
                    }
                }
            }

            return nbcellList;
        }

        private string getRowCol(TestPoint testpointDt)
        {
            int pGridUnitRow = (int)(Math.Round((bounds.y2 - testpointDt.Latitude) / GRID_SPAN_LAT));
            int pGridUnitCol = (int)(Math.Round((testpointDt.Longitude - bounds.x1) / GRID_SPAN_LONG));
            if (pGridUnitRow >= mainModel.CurGridColorUnitMatrix.MaxRowIdx)
            {
                pGridUnitRow = mainModel.CurGridColorUnitMatrix.MaxRowIdx - 1;
            }
            else if (pGridUnitCol >= mainModel.CurGridColorUnitMatrix.MinColIdx)
            {
                pGridUnitCol = mainModel.CurGridColorUnitMatrix.MinColIdx - 1;
            }
            string rowCol = pGridUnitRow + "_" + pGridUnitCol;
            return rowCol;
        }

        private void addValidGridSample(bool hasStrongCell, bool hasStrongNbcell, Cell cell, List<Cell> nbcellList, string rowCol)
        {
            bool isMainCellExist = true; //主小区与同栅格的扫频小区符合
            bool isNbCellExist = true; //邻区与栅格的扫频小区符合
            if (hasStrongCell || hasStrongNbcell)
            {
                if (gridStrongestCellDic.ContainsKey(rowCol))
                {
                    judgeIsCellExist(cell, nbcellList, rowCol, ref isMainCellExist, ref isNbCellExist);

                    if (!isMainCellExist && !isNbCellExist)  //在同一栅格内，路测采样点的主小区及邻区与扫频的小区不同，或不存在小区，记为异常采样点
                    {
                        addGridSample(gridAbnormalSampleCountDic, rowCol);
                    }
                }
            }
            else  //路测采样点无强小区，记为异常采样点
            {
                addGridSample(gridAbnormalSampleCountDic, rowCol);
            }

            addGridSample(gridSampleCountDic, rowCol);
        }

        private void judgeIsCellExist(Cell cell, List<Cell> nbcellList, string rowCol, ref bool isMainCellExist, ref bool isNbCellExist)
        {
            if (cell != null && gridStrongestCellDic[rowCol] != cell)
            {
                isMainCellExist = false;
            }

            bool isfound = isFoundInNbCell(nbcellList, gridStrongestCellDic[rowCol]);
            if (!isfound)
            {
                isNbCellExist = false;
            }
        }

        private void addGridSample(Dictionary<string, int> gridSample, string rowCol)
        {
            if (!gridSample.ContainsKey(rowCol)) //记录栅格内的路测采样点数目
                gridSample.Add(rowCol, 1);
            else
                gridSample[rowCol]++;
        }

        private bool isFoundInNbCell(List<Cell> nbCellList, Cell cell)
        {
            if (nbCellList.Count == 0)
            {
                return true;
            }

            List<Cell> retCells = new List<Cell>();
            foreach (Cell c in nbCellList)
            {
                if (cell != c)
                {
                    retCells.Add(c);
                }
            }
            if (retCells.Count != 0)
            {
                return true;
            }
            else
            {
                return false;
            }

        }

        private void showAbSamplePctGis()
        {
            try
            {
                WaitBox.Text = "在地图上用栅格显示异常采样点占比";
                WaitBox.ProgressPercent = 20;
                foreach (string rowcol in gridSampleCountDic.Keys)
                {
                    ColorUnit cu = null;
                    int i = rowcol.IndexOf("_");
                    int row = int.Parse(rowcol.Substring(0, i));
                    int col = int.Parse(rowcol.Substring(i + 1));
                    if (mainModel.CurGridColorUnitMatrix[row, col] == null)
                    {
                        cu = new ColorUnit();
                        mainModel.CurGridColorUnitMatrix[row, col] = cu;
                        cu.Status = 0;
                    }
                    else
                    {
                        cu = mainModel.CurGridColorUnitMatrix[row, col];
                    }

                    double abSamplePct = 0;
                    if (gridAbnormalSampleCountDic.ContainsKey(rowcol))
                    {
                        abSamplePct = (double)gridAbnormalSampleCountDic[rowcol] / (double)gridSampleCountDic[rowcol];  //此栅格内的异常采样点比例
                    }

                    setColorUnit(cu, abSamplePct);
                }
                WaitBox.ProgressPercent = 70;

                setLegendType();

            }
            catch 
            { 
               //continue
            }
            finally
            {
                //---释放内存-----------------------
                gridAbnormalSampleCountDic=null;
                gridSampleCountDic = null;
                //----------------------------------

                WaitBox.Close();
            }
        }

        private void setColorUnit(ColorUnit cu, double abSamplePct)
        {
            if (abSamplePct == 0)
                cu.color = Color.Green;
            else if (0 < abSamplePct && abSamplePct <= 0.2)
                cu.color = Color.FromArgb(255, 192, 192);
            else if (0.2 < abSamplePct && abSamplePct <= 0.4)
                cu.color = Color.FromArgb(255, 128, 128);
            else if (0.4 < abSamplePct && abSamplePct <= 0.6)
                cu.color = Color.Red;
            else if (0.6 < abSamplePct && abSamplePct <= 0.8)
                cu.color = Color.FromArgb(192, 0, 0);
            else if (0.8 < abSamplePct)
                cu.color = Color.Maroon;
        }

        /// <summary>
        /// 清除之前用于计算的对象，减少其对内存的占用
        /// </summary>
        private void clearTempObjects()
        {
            this.markGirds.Clear();
            this.Dispose();
            //GC.Collect();
        }

        private void setLegendType()
        {
            GridColorFixed gridColorFixed = new GridColorFixed();
            gridColorFixed.items = new List<GridColorFixedItem>();
            gridColorFixed.theme = "异常采样点比例";
            GridColorFixedItem item = new GridColorFixedItem();
            item.desc = "x > 80%";
            item.color = Color.Maroon;
            gridColorFixed.items.Add(item);
            item = new GridColorFixedItem();
            item.desc = "60% < x <= -80%";
            item.color = Color.FromArgb(192, 0, 0);
            gridColorFixed.items.Add(item);
            item = new GridColorFixedItem();
            item.desc = "40% < x <= 60%";
            item.color = Color.Red;
            gridColorFixed.items.Add(item);
            item = new GridColorFixedItem();
            item.desc = "20% < x < 40%";
            item.color = Color.FromArgb(255, 128, 128);
            gridColorFixed.items.Add(item);
            item = new GridColorFixedItem();
            item.desc = "0 < x < 20%";
            item.color = Color.FromArgb(255, 192, 192);
            gridColorFixed.items.Add(item);
            item = new GridColorFixedItem();
            item.desc = "无异常采样点";
            item.color = Color.Green;
            gridColorFixed.items.Add(item);

            mainModel.MainForm.GetMapForm().GetGridShowLayer().CurUsingGridColorFixed = gridColorFixed;
        }
    }
}
