﻿namespace MasterCom.RAMS.NewBlackBlock
{
    partial class ReappearResultForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.treeListView = new BrightIdeasSoftware.TreeListView();
            this.colID = new BrightIdeasSoftware.OLVColumn();
            this.colName = new BrightIdeasSoftware.OLVColumn();
            this.colCreateDate = new BrightIdeasSoftware.OLVColumn();
            this.colCloseDate = new BrightIdeasSoftware.OLVColumn();
            this.colStatus = new BrightIdeasSoftware.OLVColumn();
            this.colCentLng = new BrightIdeasSoftware.OLVColumn();
            this.colCentLat = new BrightIdeasSoftware.OLVColumn();
            this.colDistance = new BrightIdeasSoftware.OLVColumn();
            this.colReappearCount = new BrightIdeasSoftware.OLVColumn();
            this.contextMenuStrip1 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExpandAll = new System.Windows.Forms.ToolStripMenuItem();
            this.miCollapseAll = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            ((System.ComponentModel.ISupportInitialize)(this.treeListView)).BeginInit();
            this.contextMenuStrip1.SuspendLayout();
            this.SuspendLayout();
            // 
            // treeListView
            // 
            this.treeListView.AllColumns.Add(this.colID);
            this.treeListView.AllColumns.Add(this.colName);
            this.treeListView.AllColumns.Add(this.colCreateDate);
            this.treeListView.AllColumns.Add(this.colCloseDate);
            this.treeListView.AllColumns.Add(this.colStatus);
            this.treeListView.AllColumns.Add(this.colCentLng);
            this.treeListView.AllColumns.Add(this.colCentLat);
            this.treeListView.AllColumns.Add(this.colDistance);
            this.treeListView.AllColumns.Add(this.colReappearCount);
            this.treeListView.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.colID,
            this.colName,
            this.colCreateDate,
            this.colCloseDate,
            this.colStatus,
            this.colCentLng,
            this.colCentLat,
            this.colDistance,
            this.colReappearCount});
            this.treeListView.ContextMenuStrip = this.contextMenuStrip1;
            this.treeListView.Cursor = System.Windows.Forms.Cursors.Default;
            this.treeListView.Dock = System.Windows.Forms.DockStyle.Fill;
            this.treeListView.FullRowSelect = true;
            this.treeListView.GridLines = true;
            this.treeListView.Location = new System.Drawing.Point(0, 0);
            this.treeListView.Name = "treeListView";
            this.treeListView.OwnerDraw = true;
            this.treeListView.ShowGroups = false;
            this.treeListView.Size = new System.Drawing.Size(882, 450);
            this.treeListView.TabIndex = 0;
            this.treeListView.UseCompatibleStateImageBehavior = false;
            this.treeListView.View = System.Windows.Forms.View.Details;
            this.treeListView.VirtualMode = true;
            // 
            // colID
            // 
            this.colID.HeaderFont = null;
            this.colID.Text = "黑点ID";
            this.colID.Width = 80;
            // 
            // colName
            // 
            this.colName.HeaderFont = null;
            this.colName.Text = "名称";
            this.colName.Width = 120;
            // 
            // colCreateDate
            // 
            this.colCreateDate.HeaderFont = null;
            this.colCreateDate.Text = "创建时间";
            this.colCreateDate.Width = 100;
            // 
            // colCloseDate
            // 
            this.colCloseDate.HeaderFont = null;
            this.colCloseDate.Text = "关闭时间";
            this.colCloseDate.Width = 100;
            // 
            // colStatus
            // 
            this.colStatus.HeaderFont = null;
            this.colStatus.Text = "状态";
            // 
            // colCentLng
            // 
            this.colCentLng.HeaderFont = null;
            this.colCentLng.Text = "中心经度";
            this.colCentLng.Width = 120;
            // 
            // colCentLat
            // 
            this.colCentLat.HeaderFont = null;
            this.colCentLat.Text = "中心纬度";
            this.colCentLat.Width = 120;
            // 
            // colDistance
            // 
            this.colDistance.HeaderFont = null;
            this.colDistance.Text = "重现距离";
            // 
            // colReappearCount
            // 
            this.colReappearCount.HeaderFont = null;
            this.colReappearCount.Text = "重现次数";
            // 
            // contextMenuStrip1
            // 
            this.contextMenuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExpandAll,
            this.miCollapseAll,
            this.miExportExcel});
            this.contextMenuStrip1.Name = "contextMenuStrip1";
            this.contextMenuStrip1.Size = new System.Drawing.Size(153, 92);
            // 
            // miExpandAll
            // 
            this.miExpandAll.Name = "miExpandAll";
            this.miExpandAll.Size = new System.Drawing.Size(152, 22);
            this.miExpandAll.Text = "全部展开...";
            // 
            // miCollapseAll
            // 
            this.miCollapseAll.Name = "miCollapseAll";
            this.miCollapseAll.Size = new System.Drawing.Size(152, 22);
            this.miCollapseAll.Text = "全部折叠...";
            // 
            // miExportExcel
            // 
            this.miExportExcel.Name = "miExportExcel";
            this.miExportExcel.Size = new System.Drawing.Size(152, 22);
            this.miExportExcel.Text = "导出Excel...";
            // 
            // ReappearResultForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(882, 450);
            this.Controls.Add(this.treeListView);
            this.Name = "ReappearResultForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "黑点重现分析";
            ((System.ComponentModel.ISupportInitialize)(this.treeListView)).EndInit();
            this.contextMenuStrip1.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private BrightIdeasSoftware.TreeListView treeListView;
        private BrightIdeasSoftware.OLVColumn colID;
        private BrightIdeasSoftware.OLVColumn colName;
        private BrightIdeasSoftware.OLVColumn colCreateDate;
        private BrightIdeasSoftware.OLVColumn colCloseDate;
        private BrightIdeasSoftware.OLVColumn colStatus;
        private BrightIdeasSoftware.OLVColumn colCentLng;
        private BrightIdeasSoftware.OLVColumn colCentLat;
        private BrightIdeasSoftware.OLVColumn colDistance;
        private BrightIdeasSoftware.OLVColumn colReappearCount;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip1;
        private System.Windows.Forms.ToolStripMenuItem miExpandAll;
        private System.Windows.Forms.ToolStripMenuItem miCollapseAll;
        private System.Windows.Forms.ToolStripMenuItem miExportExcel;
    }
}