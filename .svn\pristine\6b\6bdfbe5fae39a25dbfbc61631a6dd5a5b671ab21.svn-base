﻿using System;
using System.Collections.Generic;
using System.Text;
using AxMapWinGIS;
using MapWinGIS;
using System.Windows.Forms;

namespace MasterCom.RAMS.MapControlTool
{
    /// <summary>
    /// 地图移动缩放
    /// </summary>
    class MapAction
    {
        /// <summary>
        /// 默认移动大小，左右移动50像素，上下移动30像素
        /// </summary>
        /// <param name="map"></param>
        public MapAction(AxMap map)
        {
            this.map = map;
            double x1 = 0, x2 = 0, y1 = 0, y2 = 0;

            this.map.PixelToProj(0, 0, ref x1, ref y1);
            this.map.PixelToProj(50, 30, ref x2, ref y2);
            this.unitProjX = Math.Abs(x2 - x1);
            this.unitProjY = Math.Abs(y2 - y1);
        }

        /// <summary>
        /// 移动指定像素
        /// </summary>
        /// <param name="map"></param>
        /// <param name="pixelX">左右移动的像素值</param>
        /// <param name="pixelY">上下移动的像素值</param>
        public MapAction(AxMap map, int pixelX, int pixelY)
        {
            this.map = map;
            double x1 = 0, x2 = 0, y1 = 0, y2 = 0;

            this.map.PixelToProj(0, 0, ref x1, ref y1);
            this.map.PixelToProj(pixelX, pixelY, ref x2, ref y2);
            this.unitProjX = Math.Abs(x2 - x1);
            this.unitProjY = Math.Abs(y2 - y1);
        }

        /// <summary>
        /// 方向键移动地图
        /// </summary>
        /// <param name="e">按键事件</param>
        public void KeyboardMoveMap(PreviewKeyDownEventArgs e)
        {
            switch (e.KeyCode)
            {
                case Keys.Left:
                    MoveMapPerUnit(Edirection.Left);
                    break;
                case Keys.Right:
                    MoveMapPerUnit(Edirection.Right);
                    break;
                case Keys.Up:
                    MoveMapPerUnit(Edirection.Down);
                    break;
                case Keys.Down:
                    MoveMapPerUnit(Edirection.Up);
                    break;
                default:
                    return;
            } // end switch
        } // end function KeyboardMoveMap

        /// <summary>
        /// 光标接近地图边缘时候移动地图
        /// </summary>
        /// <param name="pixelX">基于地图像素的光标X值</param>
        /// <param name="pixelY">基于地图像素的光标Y值</param>
        /// <param name="factor">边缘范围与移动像素值的比例因子</param>
        public void CursorAccessMapBorder(int pixelX, int pixelY, double factor)
        {
            double projX = 0, projY = 0;
            double xMin, yMin, zMin, xMax, yMax, zMax;
            Extents ext = map.Extents as Extents;

            ext.GetBounds(out xMin, out yMin, out zMin, out xMax, out yMax, out zMax);
            map.PixelToProj(pixelX, pixelY, ref projX, ref projY);

            if (Math.Abs(xMax - projX) < unitProjX * factor)
            {
                MoveMapPerUnit(Edirection.Right);
            }
            else if (Math.Abs(xMin - projX) < unitProjX * factor)
            {
                MoveMapPerUnit((Edirection.Left));
            }

            if (Math.Abs(yMax - projY) < unitProjY * factor)
            {
                MoveMapPerUnit(Edirection.Down);
            }
            else if (Math.Abs(yMin - projY) < unitProjY * factor)
            {
                MoveMapPerUnit(Edirection.Up);
            }
        } // end funtion CursorAccessMapBorder

        /// <summary>
        /// 移动地图
        /// </summary>
        /// <param name="dir"></param>
        private void MoveMapPerUnit(Edirection dir)
        {
            double xMin, yMin, zMin, xMax, yMax, zMax;
            Extents ext = map.Extents as Extents;

            ext.GetBounds(out xMin, out yMin, out zMin, out xMax, out yMax, out zMax);
            switch (dir)
            {
                case Edirection.Left:
                    xMin -= unitProjX;
                    xMax -= unitProjX;
                    break;
                case Edirection.Right:
                    xMin += unitProjX;
                    xMax += unitProjX;
                    break;
                case Edirection.Up:
                    yMin -= unitProjY;
                    yMax -= unitProjY;
                    break;
                case Edirection.Down:
                    yMin += unitProjY;
                    yMax += unitProjY;
                    break;
            }

            ext.SetBounds(xMin, yMin, zMin, xMax, yMax, zMax);
            map.Extents = ext;
        }

        private readonly AxMap map;
        private readonly double unitProjX; // 左右移动的经度距离
        private readonly double unitProjY; // 上下移动的纬度距离

        private enum Edirection
        {
            Left,
            Up,
            Right,
            Down,
        };
    }
}
