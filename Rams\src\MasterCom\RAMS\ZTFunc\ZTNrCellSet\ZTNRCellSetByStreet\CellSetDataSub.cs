﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class CellSetDataSub
    {
        public CellSetDataSub(double rangeMax, double rangeMin)
        {
            this.rangeMax = rangeMax;
            this.rangeMin = rangeMin;
        }

        public CellSetDataSub()
        {
        }

        private readonly double rangeMax;
        private readonly double rangeMin;

        public int Count { get; set; }
        public double Total { get; set; }
        public double Max { get; set; } = -10000;
        public double Min { get; set; } = 10000;
        public double Avg { get; set; }

        public void Calculate()
        {
            if (Count > 0)
            {
                Avg = Math.Round(Total * 1.0 / Count, 2);
            }
            else
            {
                Avg = 0;
            }
        }

        public void AddDataSub(float? dataSub, int testPointCount)
        {
            if (dataSub != null)
            {
                double data = Math.Round((float)dataSub, 2);
                if (data < rangeMin || data > rangeMax)
                {
                    return;
                }
                if (testPointCount == 0 || Min > data)
                {
                    Min = data;
                }
                if (testPointCount == 0 || Max < data)
                {
                    Max = data;
                }
                Total += data;
                Count++;
            }
        }

        public int AddDataSub(double data, int testPointCount)
        {
            data = Math.Round(data, 2);
            if (Min > data)
            {
                Min = data;
            }
            if (Max < data)
            {
                Max = data;
            }
            Total += data;
            Count++;
            return ++testPointCount;
        }
    }
}
