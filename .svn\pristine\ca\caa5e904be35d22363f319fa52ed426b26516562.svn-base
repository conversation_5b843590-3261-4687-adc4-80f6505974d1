﻿using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTNRScanLowSinrRoad
{
    public class NRLowSinrRoad
    {
        public NRLowSinrRoad(string fileName, double distance, double duration)
        {
            FileName = fileName;
            Distance = Math.Round(distance, 2);
            Duration = duration;
        }

        public List<TestPoint> TestPntLst { get; protected set; } = new List<TestPoint>();
        public string FileName { get; protected set; }
        public double Distance { get; protected set; }
        public double Duration { get; protected set; }
        public int TestPointCnt { get; protected set; }
        public string LongMid { get; protected set; } = "-";
        public string LatMid { get; protected set; } = "-";
        public string RoadName { get; protected set; } = "";
        public string GridName { get; protected set; } = "";

        public void AddRange(List<TestPoint> tPntLst)
        {
            TestPntLst.AddRange(tPntLst);
        }

        public void Calculate()
        {
            TestPointCnt = TestPntLst.Count;
            if (TestPointCnt > 0)
            {
                LongMid = TestPntLst[TestPointCnt / 2].Longitude.ToString();
                LatMid = TestPntLst[TestPointCnt / 2].Latitude.ToString();
            }

            double lng;
            double lat;
            if (double.TryParse(LongMid, out lng)
                && double.TryParse(LatMid, out lat))
            {
                RoadName = GISManager.GetInstance().GetRoadPlaceDesc(lng, lat);
                GridName = GISManager.GetInstance().GetGridDesc(lng, lat);
            }
        }
    }

    public class NRScanLowSinrRoadCond
    {
        public int SINRMax { get; set; } = -3;

        public int DistanceMin { get; set; } = 20;
    }
}
