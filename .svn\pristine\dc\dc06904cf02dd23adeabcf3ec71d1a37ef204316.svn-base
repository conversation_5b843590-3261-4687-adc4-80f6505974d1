﻿namespace MasterCom.RAMS.ZTFunc.LteLowDLSpeedAna
{
    partial class LowDLSpeedResultForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.gridCtrl = new DevExpress.XtraGrid.GridControl();
            this.ctxMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExport2Xls = new System.Windows.Forms.ToolStripMenuItem();
            this.gv = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn7 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn25 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn24 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn23 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn22 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn21 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn20 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn19 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn18 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn17 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn16 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn15 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn14 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn13 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn12 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn11 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn10 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn9 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn8 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn38 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn37 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn36 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn35 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn34 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn33 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn32 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn31 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn30 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn29 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn28 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn27 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn26 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn53 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn52 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn51 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn50 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn49 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn48 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn47 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn46 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn45 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn44 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn43 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn42 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn41 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn40 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn39 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn58 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn57 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn56 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn55 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn54 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn63 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn62 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn61 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn60 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn6 = new DevExpress.XtraGrid.Columns.GridColumn();
            ((System.ComponentModel.ISupportInitialize)(this.gridCtrl)).BeginInit();
            this.ctxMenu.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gv)).BeginInit();
            this.SuspendLayout();
            // 
            // gridCtrl
            // 
            this.gridCtrl.ContextMenuStrip = this.ctxMenu;
            this.gridCtrl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridCtrl.Location = new System.Drawing.Point(0, 0);
            this.gridCtrl.MainView = this.gv;
            this.gridCtrl.Name = "gridCtrl";
            this.gridCtrl.ShowOnlyPredefinedDetails = true;
            this.gridCtrl.Size = new System.Drawing.Size(980, 374);
            this.gridCtrl.TabIndex = 0;
            this.gridCtrl.UseEmbeddedNavigator = true;
            this.gridCtrl.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gv});
            // 
            // ctxMenu
            // 
            this.ctxMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExport2Xls});
            this.ctxMenu.Name = "ctxMenu";
            this.ctxMenu.Size = new System.Drawing.Size(139, 26);
            // 
            // miExport2Xls
            // 
            this.miExport2Xls.Name = "miExport2Xls";
            this.miExport2Xls.Size = new System.Drawing.Size(138, 22);
            this.miExport2Xls.Text = "导出Excel...";
            this.miExport2Xls.Click += new System.EventHandler(this.miExport2Xls_Click);
            // 
            // gv
            // 
            this.gv.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn7,
            this.gridColumn1,
            this.gridColumn2,
            this.gridColumn3,
            this.gridColumn4,
            this.gridColumn25,
            this.gridColumn24,
            this.gridColumn23,
            this.gridColumn22,
            this.gridColumn21,
            this.gridColumn20,
            this.gridColumn19,
            this.gridColumn18,
            this.gridColumn17,
            this.gridColumn16,
            this.gridColumn15,
            this.gridColumn14,
            this.gridColumn13,
            this.gridColumn12,
            this.gridColumn11,
            this.gridColumn10,
            this.gridColumn9,
            this.gridColumn8,
            this.gridColumn38,
            this.gridColumn37,
            this.gridColumn36,
            this.gridColumn35,
            this.gridColumn34,
            this.gridColumn33,
            this.gridColumn32,
            this.gridColumn31,
            this.gridColumn30,
            this.gridColumn29,
            this.gridColumn28,
            this.gridColumn27,
            this.gridColumn26,
            this.gridColumn53,
            this.gridColumn52,
            this.gridColumn51,
            this.gridColumn50,
            this.gridColumn49,
            this.gridColumn48,
            this.gridColumn47,
            this.gridColumn46,
            this.gridColumn45,
            this.gridColumn44,
            this.gridColumn43,
            this.gridColumn42,
            this.gridColumn41,
            this.gridColumn40,
            this.gridColumn39,
            this.gridColumn58,
            this.gridColumn57,
            this.gridColumn56,
            this.gridColumn55,
            this.gridColumn54,
            this.gridColumn63,
            this.gridColumn62,
            this.gridColumn61,
            this.gridColumn60,
            this.gridColumn5,
            this.gridColumn6});
            this.gv.GridControl = this.gridCtrl;
            this.gv.Name = "gv";
            this.gv.OptionsBehavior.Editable = false;
            this.gv.OptionsView.ColumnAutoWidth = false;
            this.gv.OptionsView.EnableAppearanceEvenRow = true;
            this.gv.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn7
            // 
            this.gridColumn7.Caption = "序号";
            this.gridColumn7.FieldName = "SN";
            this.gridColumn7.Name = "gridColumn7";
            this.gridColumn7.Visible = true;
            this.gridColumn7.VisibleIndex = 0;
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "文件名";
            this.gridColumn1.FieldName = "FileName";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.Visible = true;
            this.gridColumn1.VisibleIndex = 1;
            this.gridColumn1.Width = 174;
            // 
            // gridColumn2
            // 
            this.gridColumn2.Caption = "开始时间";
            this.gridColumn2.DisplayFormat.FormatString = "yyyy-MM-dd hh:mm:ss";
            this.gridColumn2.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.gridColumn2.FieldName = "BeginTime";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.Visible = true;
            this.gridColumn2.VisibleIndex = 2;
            this.gridColumn2.Width = 138;
            // 
            // gridColumn3
            // 
            this.gridColumn3.Caption = "结束时间";
            this.gridColumn3.DisplayFormat.FormatString = "yyyy-MM-dd hh:mm:ss";
            this.gridColumn3.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.gridColumn3.FieldName = "EndTime";
            this.gridColumn3.Name = "gridColumn3";
            this.gridColumn3.Visible = true;
            this.gridColumn3.VisibleIndex = 3;
            this.gridColumn3.Width = 138;
            // 
            // gridColumn4
            // 
            this.gridColumn4.Caption = "问题类型";
            this.gridColumn4.FieldName = "SpecificType";
            this.gridColumn4.Name = "gridColumn4";
            this.gridColumn4.Visible = true;
            this.gridColumn4.VisibleIndex = 4;
            this.gridColumn4.Width = 133;
            // 
            // gridColumn25
            // 
            this.gridColumn25.Caption = "网格名称";
            this.gridColumn25.FieldName = "GridName";
            this.gridColumn25.Name = "gridColumn25";
            this.gridColumn25.Visible = true;
            this.gridColumn25.VisibleIndex = 5;
            // 
            // gridColumn24
            // 
            this.gridColumn24.Caption = "业务类型";
            this.gridColumn24.FieldName = "AppType";
            this.gridColumn24.Name = "gridColumn24";
            this.gridColumn24.Visible = true;
            this.gridColumn24.VisibleIndex = 6;
            // 
            // gridColumn23
            // 
            this.gridColumn23.Caption = "道路";
            this.gridColumn23.FieldName = "RoadDesc";
            this.gridColumn23.Name = "gridColumn23";
            this.gridColumn23.Visible = true;
            this.gridColumn23.VisibleIndex = 7;
            // 
            // gridColumn22
            // 
            this.gridColumn22.Caption = "里程(米)";
            this.gridColumn22.DisplayFormat.FormatString = "F2";
            this.gridColumn22.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn22.FieldName = "Distance";
            this.gridColumn22.Name = "gridColumn22";
            this.gridColumn22.Visible = true;
            this.gridColumn22.VisibleIndex = 8;
            // 
            // gridColumn21
            // 
            this.gridColumn21.Caption = "经度";
            this.gridColumn21.FieldName = "Longitude";
            this.gridColumn21.Name = "gridColumn21";
            this.gridColumn21.Visible = true;
            this.gridColumn21.VisibleIndex = 9;
            // 
            // gridColumn20
            // 
            this.gridColumn20.Caption = "纬度";
            this.gridColumn20.FieldName = "Latitude";
            this.gridColumn20.Name = "gridColumn20";
            this.gridColumn20.Visible = true;
            this.gridColumn20.VisibleIndex = 10;
            // 
            // gridColumn19
            // 
            this.gridColumn19.Caption = "时长(秒)";
            this.gridColumn19.FieldName = "Duration";
            this.gridColumn19.Name = "gridColumn19";
            this.gridColumn19.Visible = true;
            this.gridColumn19.VisibleIndex = 11;
            // 
            // gridColumn18
            // 
            this.gridColumn18.Caption = "采样点总数";
            this.gridColumn18.FieldName = "TotalPointCount";
            this.gridColumn18.Name = "gridColumn18";
            this.gridColumn18.Visible = true;
            this.gridColumn18.VisibleIndex = 12;
            this.gridColumn18.Width = 138;
            // 
            // gridColumn17
            // 
            this.gridColumn17.Caption = "低速率采样点个数";
            this.gridColumn17.FieldName = "LowPointCount";
            this.gridColumn17.Name = "gridColumn17";
            this.gridColumn17.Visible = true;
            this.gridColumn17.VisibleIndex = 13;
            this.gridColumn17.Width = 138;
            // 
            // gridColumn16
            // 
            this.gridColumn16.Caption = "低速率采样点占比(%)";
            this.gridColumn16.DisplayFormat.FormatString = "F2";
            this.gridColumn16.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn16.FieldName = "LowPercent";
            this.gridColumn16.Name = "gridColumn16";
            this.gridColumn16.Visible = true;
            this.gridColumn16.VisibleIndex = 14;
            this.gridColumn16.Width = 138;
            // 
            // gridColumn15
            // 
            this.gridColumn15.Caption = "最高速率(M)";
            this.gridColumn15.DisplayFormat.FormatString = "F2";
            this.gridColumn15.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn15.FieldName = "HighSpeed";
            this.gridColumn15.Name = "gridColumn15";
            this.gridColumn15.Visible = true;
            this.gridColumn15.VisibleIndex = 15;
            this.gridColumn15.Width = 138;
            // 
            // gridColumn14
            // 
            this.gridColumn14.Caption = "最低速率(M)";
            this.gridColumn14.DisplayFormat.FormatString = "F2";
            this.gridColumn14.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn14.FieldName = "LowSpeed";
            this.gridColumn14.Name = "gridColumn14";
            this.gridColumn14.Visible = true;
            this.gridColumn14.VisibleIndex = 16;
            this.gridColumn14.Width = 138;
            // 
            // gridColumn13
            // 
            this.gridColumn13.Caption = "0速率里程";
            this.gridColumn13.DisplayFormat.FormatString = "F2";
            this.gridColumn13.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn13.FieldName = "Distance_0Speed";
            this.gridColumn13.Name = "gridColumn13";
            this.gridColumn13.Visible = true;
            this.gridColumn13.VisibleIndex = 17;
            this.gridColumn13.Width = 138;
            // 
            // gridColumn12
            // 
            this.gridColumn12.Caption = "0速率里程占比(%)";
            this.gridColumn12.DisplayFormat.FormatString = "F2";
            this.gridColumn12.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn12.FieldName = "Rate_Distance_0Speed";
            this.gridColumn12.Name = "gridColumn12";
            this.gridColumn12.Visible = true;
            this.gridColumn12.VisibleIndex = 18;
            this.gridColumn12.Width = 138;
            // 
            // gridColumn11
            // 
            this.gridColumn11.Caption = "平均速率(M)";
            this.gridColumn11.DisplayFormat.FormatString = "F2";
            this.gridColumn11.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn11.FieldName = "MeanSpeed";
            this.gridColumn11.Name = "gridColumn11";
            this.gridColumn11.Visible = true;
            this.gridColumn11.VisibleIndex = 19;
            this.gridColumn11.Width = 138;
            // 
            // gridColumn10
            // 
            this.gridColumn10.Caption = "PDSCH_BLER平均值";
            this.gridColumn10.DisplayFormat.FormatString = "F2";
            this.gridColumn10.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn10.FieldName = "MeanPDSCHBLER";
            this.gridColumn10.Name = "gridColumn10";
            this.gridColumn10.Visible = true;
            this.gridColumn10.VisibleIndex = 20;
            this.gridColumn10.Width = 138;
            // 
            // gridColumn9
            // 
            this.gridColumn9.Caption = "TAC-CellID";
            this.gridColumn9.DisplayFormat.FormatString = "F2";
            this.gridColumn9.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn9.FieldName = "LACCIs";
            this.gridColumn9.Name = "gridColumn9";
            this.gridColumn9.Visible = true;
            this.gridColumn9.VisibleIndex = 21;
            this.gridColumn9.Width = 138;
            // 
            // gridColumn8
            // 
            this.gridColumn8.Caption = "关联小区名称";
            this.gridColumn8.FieldName = "CellName";
            this.gridColumn8.Name = "gridColumn8";
            this.gridColumn8.Visible = true;
            this.gridColumn8.VisibleIndex = 22;
            this.gridColumn8.Width = 138;
            // 
            // gridColumn38
            // 
            this.gridColumn38.Caption = "频点";
            this.gridColumn38.FieldName = "BCCHs";
            this.gridColumn38.Name = "gridColumn38";
            this.gridColumn38.Visible = true;
            this.gridColumn38.VisibleIndex = 23;
            this.gridColumn38.Width = 138;
            // 
            // gridColumn37
            // 
            this.gridColumn37.Caption = "片区名称";
            this.gridColumn37.FieldName = "AreaName";
            this.gridColumn37.Name = "gridColumn37";
            this.gridColumn37.Visible = true;
            this.gridColumn37.VisibleIndex = 24;
            this.gridColumn37.Width = 138;
            // 
            // gridColumn36
            // 
            this.gridColumn36.Caption = "代维区域";
            this.gridColumn36.FieldName = "AreaAgentName";
            this.gridColumn36.Name = "gridColumn36";
            this.gridColumn36.Visible = true;
            this.gridColumn36.VisibleIndex = 25;
            this.gridColumn36.Width = 138;
            // 
            // gridColumn35
            // 
            this.gridColumn35.Caption = "RSRP最大值";
            this.gridColumn35.FieldName = "RSRP_Max";
            this.gridColumn35.Name = "gridColumn35";
            this.gridColumn35.Visible = true;
            this.gridColumn35.VisibleIndex = 26;
            this.gridColumn35.Width = 138;
            // 
            // gridColumn34
            // 
            this.gridColumn34.Caption = "RSRP最小值";
            this.gridColumn34.FieldName = "RSRP_Min";
            this.gridColumn34.Name = "gridColumn34";
            this.gridColumn34.Visible = true;
            this.gridColumn34.VisibleIndex = 27;
            this.gridColumn34.Width = 138;
            // 
            // gridColumn33
            // 
            this.gridColumn33.Caption = "RSRP平均值";
            this.gridColumn33.DisplayFormat.FormatString = "F2";
            this.gridColumn33.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn33.FieldName = "RSRP_Mean";
            this.gridColumn33.Name = "gridColumn33";
            this.gridColumn33.Visible = true;
            this.gridColumn33.VisibleIndex = 28;
            this.gridColumn33.Width = 138;
            // 
            // gridColumn32
            // 
            this.gridColumn32.Caption = "SINR最大值";
            this.gridColumn32.FieldName = "SINR_Max";
            this.gridColumn32.Name = "gridColumn32";
            this.gridColumn32.Visible = true;
            this.gridColumn32.VisibleIndex = 29;
            this.gridColumn32.Width = 138;
            // 
            // gridColumn31
            // 
            this.gridColumn31.Caption = "SINR最小值";
            this.gridColumn31.FieldName = "SINR_Min";
            this.gridColumn31.Name = "gridColumn31";
            this.gridColumn31.Visible = true;
            this.gridColumn31.VisibleIndex = 30;
            this.gridColumn31.Width = 138;
            // 
            // gridColumn30
            // 
            this.gridColumn30.Caption = "SINR平均值";
            this.gridColumn30.DisplayFormat.FormatString = "F2";
            this.gridColumn30.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn30.FieldName = "SINR_Mean";
            this.gridColumn30.Name = "gridColumn30";
            this.gridColumn30.Visible = true;
            this.gridColumn30.VisibleIndex = 31;
            this.gridColumn30.Width = 138;
            // 
            // gridColumn29
            // 
            this.gridColumn29.Caption = "连续SINR质差里程占比(%)";
            this.gridColumn29.DisplayFormat.FormatString = "F2";
            this.gridColumn29.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn29.FieldName = "RateWeakSinr";
            this.gridColumn29.Name = "gridColumn29";
            this.gridColumn29.Visible = true;
            this.gridColumn29.VisibleIndex = 32;
            this.gridColumn29.Width = 138;
            // 
            // gridColumn28
            // 
            this.gridColumn28.Caption = "重叠覆盖度≥3比例";
            this.gridColumn28.DisplayFormat.FormatString = "F2";
            this.gridColumn28.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn28.FieldName = "RateMultiCoverage";
            this.gridColumn28.Name = "gridColumn28";
            this.gridColumn28.Visible = true;
            this.gridColumn28.VisibleIndex = 33;
            this.gridColumn28.Width = 138;
            // 
            // gridColumn27
            // 
            this.gridColumn27.Caption = "重叠覆盖里程占比";
            this.gridColumn27.DisplayFormat.FormatString = "F2";
            this.gridColumn27.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn27.FieldName = "RateDisMulticoverage";
            this.gridColumn27.Name = "gridColumn27";
            this.gridColumn27.Visible = true;
            this.gridColumn27.VisibleIndex = 34;
            this.gridColumn27.Width = 138;
            // 
            // gridColumn26
            // 
            this.gridColumn26.Caption = "下行码字0MCS平均值";
            this.gridColumn26.DisplayFormat.FormatString = "F2";
            this.gridColumn26.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn26.FieldName = "Code0Mean";
            this.gridColumn26.Name = "gridColumn26";
            this.gridColumn26.Visible = true;
            this.gridColumn26.VisibleIndex = 35;
            this.gridColumn26.Width = 138;
            // 
            // gridColumn53
            // 
            this.gridColumn53.Caption = "下行码字1MCS平均值";
            this.gridColumn53.DisplayFormat.FormatString = "F2";
            this.gridColumn53.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn53.FieldName = "Code1Mean";
            this.gridColumn53.Name = "gridColumn53";
            this.gridColumn53.Visible = true;
            this.gridColumn53.VisibleIndex = 36;
            this.gridColumn53.Width = 138;
            // 
            // gridColumn52
            // 
            this.gridColumn52.Caption = "下行码字0最高频率MCS(%)";
            this.gridColumn52.DisplayFormat.FormatString = "F2";
            this.gridColumn52.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn52.FieldName = "Code0Max";
            this.gridColumn52.Name = "gridColumn52";
            this.gridColumn52.Visible = true;
            this.gridColumn52.VisibleIndex = 37;
            this.gridColumn52.Width = 138;
            // 
            // gridColumn51
            // 
            this.gridColumn51.Caption = "下行码字1最高频率MCS(%)";
            this.gridColumn51.DisplayFormat.FormatString = "F2";
            this.gridColumn51.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn51.FieldName = "Code1Max";
            this.gridColumn51.Name = "gridColumn51";
            this.gridColumn51.Visible = true;
            this.gridColumn51.VisibleIndex = 38;
            this.gridColumn51.Width = 138;
            // 
            // gridColumn50
            // 
            this.gridColumn50.Caption = "码字0CQI平均值";
            this.gridColumn50.DisplayFormat.FormatString = "F2";
            this.gridColumn50.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn50.FieldName = "CQI0Mean";
            this.gridColumn50.Name = "gridColumn50";
            this.gridColumn50.Visible = true;
            this.gridColumn50.VisibleIndex = 39;
            this.gridColumn50.Width = 138;
            // 
            // gridColumn49
            // 
            this.gridColumn49.Caption = "码字1CQI平均值";
            this.gridColumn49.DisplayFormat.FormatString = "F2";
            this.gridColumn49.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn49.FieldName = "CQI1Mean";
            this.gridColumn49.Name = "gridColumn49";
            this.gridColumn49.Visible = true;
            this.gridColumn49.VisibleIndex = 40;
            this.gridColumn49.Width = 138;
            // 
            // gridColumn48
            // 
            this.gridColumn48.Caption = "下行码字0 64QAM占比";
            this.gridColumn48.DisplayFormat.FormatString = "F2";
            this.gridColumn48.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn48.FieldName = "Code0_64QAMRate";
            this.gridColumn48.Name = "gridColumn48";
            this.gridColumn48.Visible = true;
            this.gridColumn48.VisibleIndex = 41;
            this.gridColumn48.Width = 138;
            // 
            // gridColumn47
            // 
            this.gridColumn47.Caption = "下行码字1 64QAM占比";
            this.gridColumn47.DisplayFormat.FormatString = "F2";
            this.gridColumn47.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn47.FieldName = "Code1_64QAMRate";
            this.gridColumn47.Name = "gridColumn47";
            this.gridColumn47.Visible = true;
            this.gridColumn47.VisibleIndex = 42;
            this.gridColumn47.Width = 138;
            // 
            // gridColumn46
            // 
            this.gridColumn46.Caption = "下行码字0 16QAM占比";
            this.gridColumn46.DisplayFormat.FormatString = "F2";
            this.gridColumn46.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn46.FieldName = "Code0_16QAMRate";
            this.gridColumn46.Name = "gridColumn46";
            this.gridColumn46.Visible = true;
            this.gridColumn46.VisibleIndex = 43;
            this.gridColumn46.Width = 138;
            // 
            // gridColumn45
            // 
            this.gridColumn45.Caption = "下行码字1 16QAM占比";
            this.gridColumn45.DisplayFormat.FormatString = "F2";
            this.gridColumn45.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn45.FieldName = "Code1_16QAMRate";
            this.gridColumn45.Name = "gridColumn45";
            this.gridColumn45.Visible = true;
            this.gridColumn45.VisibleIndex = 44;
            this.gridColumn45.Width = 138;
            // 
            // gridColumn44
            // 
            this.gridColumn44.Caption = "Throughput_DL最大值";
            this.gridColumn44.DisplayFormat.FormatString = "F2";
            this.gridColumn44.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn44.FieldName = "Throughput_DL_Max";
            this.gridColumn44.Name = "gridColumn44";
            this.gridColumn44.Visible = true;
            this.gridColumn44.VisibleIndex = 45;
            this.gridColumn44.Width = 138;
            // 
            // gridColumn43
            // 
            this.gridColumn43.Caption = "Throughput_DL最小值";
            this.gridColumn43.DisplayFormat.FormatString = "F2";
            this.gridColumn43.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn43.FieldName = "Throughput_DL_Min";
            this.gridColumn43.Name = "gridColumn43";
            this.gridColumn43.Visible = true;
            this.gridColumn43.VisibleIndex = 46;
            this.gridColumn43.Width = 138;
            // 
            // gridColumn42
            // 
            this.gridColumn42.Caption = "Throughput_DL平均值";
            this.gridColumn42.DisplayFormat.FormatString = "F2";
            this.gridColumn42.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn42.FieldName = "Throughput_DL_Mean";
            this.gridColumn42.Name = "gridColumn42";
            this.gridColumn42.Visible = true;
            this.gridColumn42.VisibleIndex = 47;
            this.gridColumn42.Width = 138;
            // 
            // gridColumn41
            // 
            this.gridColumn41.Caption = "Transmission_Mode";
            this.gridColumn41.DisplayFormat.FormatString = "F2";
            this.gridColumn41.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn41.FieldName = "Transmission_Mode";
            this.gridColumn41.Name = "gridColumn41";
            this.gridColumn41.Visible = true;
            this.gridColumn41.VisibleIndex = 48;
            this.gridColumn41.Width = 138;
            // 
            // gridColumn40
            // 
            this.gridColumn40.Caption = "TM3比例（%）";
            this.gridColumn40.DisplayFormat.FormatString = "F2";
            this.gridColumn40.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn40.FieldName = "Transmission_Mode3";
            this.gridColumn40.Name = "gridColumn40";
            this.gridColumn40.Visible = true;
            this.gridColumn40.VisibleIndex = 49;
            this.gridColumn40.Width = 138;
            // 
            // gridColumn39
            // 
            this.gridColumn39.Caption = "rank_indicator";
            this.gridColumn39.DisplayFormat.FormatString = "F2";
            this.gridColumn39.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn39.FieldName = "Rank_Indicator";
            this.gridColumn39.Name = "gridColumn39";
            this.gridColumn39.Visible = true;
            this.gridColumn39.VisibleIndex = 50;
            this.gridColumn39.Width = 138;
            // 
            // gridColumn58
            // 
            this.gridColumn58.Caption = "双流时长占比（%）";
            this.gridColumn58.DisplayFormat.FormatString = "F2";
            this.gridColumn58.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn58.FieldName = "Rank2_Indicator";
            this.gridColumn58.Name = "gridColumn58";
            this.gridColumn58.Visible = true;
            this.gridColumn58.VisibleIndex = 51;
            this.gridColumn58.Width = 138;
            // 
            // gridColumn57
            // 
            this.gridColumn57.Caption = "误块率（%）";
            this.gridColumn57.DisplayFormat.FormatString = "F2";
            this.gridColumn57.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn57.FieldName = "PDSCH_BLER";
            this.gridColumn57.Name = "gridColumn57";
            this.gridColumn57.Visible = true;
            this.gridColumn57.VisibleIndex = 52;
            this.gridColumn57.Width = 138;
            // 
            // gridColumn56
            // 
            this.gridColumn56.Caption = "PDSCH_RB_Number";
            this.gridColumn56.DisplayFormat.FormatString = "F2";
            this.gridColumn56.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn56.FieldName = "PDSCH_RB_Number";
            this.gridColumn56.Name = "gridColumn56";
            this.gridColumn56.Visible = true;
            this.gridColumn56.VisibleIndex = 53;
            this.gridColumn56.Width = 138;
            // 
            // gridColumn55
            // 
            this.gridColumn55.Caption = "PRB调度数";
            this.gridColumn55.DisplayFormat.FormatString = "F2";
            this.gridColumn55.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn55.FieldName = "PDSCH_PRb_Num_s";
            this.gridColumn55.Name = "gridColumn55";
            this.gridColumn55.Visible = true;
            this.gridColumn55.VisibleIndex = 54;
            this.gridColumn55.Width = 138;
            // 
            // gridColumn54
            // 
            this.gridColumn54.Caption = "PDCCH_DL_Grant_Count";
            this.gridColumn54.DisplayFormat.FormatString = "F2";
            this.gridColumn54.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn54.FieldName = "PDCCH_DL_Grant_Count";
            this.gridColumn54.Name = "gridColumn54";
            this.gridColumn54.Visible = true;
            this.gridColumn54.VisibleIndex = 55;
            this.gridColumn54.Width = 138;
            // 
            // gridColumn63
            // 
            this.gridColumn63.Caption = "Ratio_DL_Code0_HARQ_ACK";
            this.gridColumn63.DisplayFormat.FormatString = "F2";
            this.gridColumn63.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn63.FieldName = "Ratio_DL_Code0_HARQ_ACK";
            this.gridColumn63.Name = "gridColumn63";
            this.gridColumn63.Visible = true;
            this.gridColumn63.VisibleIndex = 56;
            this.gridColumn63.Width = 138;
            // 
            // gridColumn62
            // 
            this.gridColumn62.Caption = "Ratio_DL_Code0_HARQ_NACK";
            this.gridColumn62.DisplayFormat.FormatString = "F2";
            this.gridColumn62.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn62.FieldName = "Ratio_DL_Code0_HARQ_NACK";
            this.gridColumn62.Name = "gridColumn62";
            this.gridColumn62.Visible = true;
            this.gridColumn62.VisibleIndex = 57;
            this.gridColumn62.Width = 138;
            // 
            // gridColumn61
            // 
            this.gridColumn61.Caption = "Ratio_DL_Code1_HARQ_ACK";
            this.gridColumn61.DisplayFormat.FormatString = "F2";
            this.gridColumn61.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn61.FieldName = "Ratio_DL_Code1_HARQ_ACK";
            this.gridColumn61.Name = "gridColumn61";
            this.gridColumn61.Visible = true;
            this.gridColumn61.VisibleIndex = 58;
            this.gridColumn61.Width = 138;
            // 
            // gridColumn60
            // 
            this.gridColumn60.Caption = "Ratio_DL_Code1_HARQ_NACK";
            this.gridColumn60.DisplayFormat.FormatString = "F2";
            this.gridColumn60.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn60.FieldName = "Ratio_DL_Code1_HARQ_NACK";
            this.gridColumn60.Name = "gridColumn60";
            this.gridColumn60.Visible = true;
            this.gridColumn60.VisibleIndex = 59;
            this.gridColumn60.Width = 138;
            // 
            // gridColumn5
            // 
            this.gridColumn5.Caption = "详情";
            this.gridColumn5.FieldName = "Detail";
            this.gridColumn5.Name = "gridColumn5";
            this.gridColumn5.Visible = true;
            this.gridColumn5.VisibleIndex = 60;
            this.gridColumn5.Width = 127;
            // 
            // gridColumn6
            // 
            this.gridColumn6.Caption = "建议";
            this.gridColumn6.FieldName = "Suggest";
            this.gridColumn6.Name = "gridColumn6";
            this.gridColumn6.Visible = true;
            this.gridColumn6.VisibleIndex = 61;
            this.gridColumn6.Width = 253;
            // 
            // LowDLSpeedResultForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(980, 374);
            this.Controls.Add(this.gridCtrl);
            this.Name = "LowDLSpeedResultForm";
            this.Text = "低速率路段原因分析";
            ((System.ComponentModel.ISupportInitialize)(this.gridCtrl)).EndInit();
            this.ctxMenu.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gv)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraGrid.GridControl gridCtrl;
        private DevExpress.XtraGrid.Views.Grid.GridView gv;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn6;
        private System.Windows.Forms.ContextMenuStrip ctxMenu;
        private System.Windows.Forms.ToolStripMenuItem miExport2Xls;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn7;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn25;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn24;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn23;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn22;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn21;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn20;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn19;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn18;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn17;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn16;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn15;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn14;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn13;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn12;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn11;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn10;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn9;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn8;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn38;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn37;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn36;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn35;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn34;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn33;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn32;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn31;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn30;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn29;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn28;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn27;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn26;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn53;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn52;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn51;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn50;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn49;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn48;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn47;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn46;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn45;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn44;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn43;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn42;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn41;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn40;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn39;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn58;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn57;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn56;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn55;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn54;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn63;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn62;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn61;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn60;
    }
}