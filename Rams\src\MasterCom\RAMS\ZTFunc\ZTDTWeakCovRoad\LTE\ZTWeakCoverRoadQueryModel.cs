﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Text;
using System.Windows.Forms;
using MasterCom.MTGis;
using MasterCom.RAMS.Func.SystemSetting;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTWeakCoverRoadQueryModel : DIYAnalyseByFileBackgroundBase
    {
        public string name { get; set; } = "";
        public int type { get; set; } = 0;
        public int funcId { get; set; } = 0;
        public int subfuncId { get; set; } = 0;
        public string desc { get; set; } = "";
        public string tpStr { get; set; } = "";
        public string tpRSRP { get; set; } = "";
        public string tpSINR { get; set; } = "";
        public string tpTac { get; set; } = "";
        public string tpSpeed { get; set; } = "";
        public string themeName { get; set; } = "";
        public string tpNCell_EARFCN { get; set; } = "";
        public string tpNCell_PCI { get; set; } = "";
        public string tpAppType { get; set; } = "";

        protected WeakCoverRoadCondition_LTE weakCondition = new WeakCoverRoadCondition_LTE();
        protected bool saveTestPoints = true;

        ////存放弱覆盖采样点的矩形区域，用于对区域内的小区进行筛选，避免遍历全部小区
        protected DbRect sampleRect = new DbRect(-999, -999, -999, -999);

        protected static readonly object lockObj = new object();
        private static ZTWeakCoverRoadQueryModel intance = null;
        public static ZTWeakCoverRoadQueryModel GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new ZTWeakCoverRoadQueryModel();
                    }
                }
            }
            return intance;
        }

        protected ZTWeakCoverRoadQueryModel()
            : base(MainModel.GetInstance())
        {
            if (intance != null)
            {
                return;
            }
            ServiceTypes.Clear();
#if LT
            carrierID = CarrierType.ChinaUnicom;
#else
            carrierID = CarrierType.ChinaMobile;
#endif
        }

        public override string Name
        {
            get { return name; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(type, funcId, subfuncId, this.Name);//////
        }

        private LTECell getNBCell(TestPoint testPoint, int index)
        {
            if(this.themeName == "LTE_FDD:RSRP")
            {
                return testPoint.GetNBCell_LTE_FDD(index);
            }
            else
            {
                return testPoint.GetNBCell_LTE(index);
            }
        }

        protected LTECell getMainCell(TestPoint testPoint)
        {
            if (this.themeName == "LTE_FDD:RSRP")
            {
                return testPoint.GetMainCell_LTE_FDD();
            }
            else
            {
                return testPoint.GetMainCell_LTE();
            }
        }

        protected override void fireShowForm()
        {
            if (weakCoverList.Count == 0)
            {
                MessageBox.Show("没有符合条件的信息！");
                return;
            }
            MainModel.FireSetDefaultMapSerialTheme(this.themeName);
            WeakCoverRoadLTEForm frm = MainModel.GetInstance().GetObjectFromBlackboard(typeof(WeakCoverRoadLTEForm)) as WeakCoverRoadLTEForm;
            if (frm == null || frm.IsDisposed)
            {
                frm = new WeakCoverRoadLTEForm(MainModel);
            }
            frm.weakCondition = this.weakCondition;
            frm.FillData(weakCoverList);
            frm.Owner = MainModel.MainForm;
            frm.Visible = true;
            frm.BringToFront();
            weakCoverList = null;
        }

        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                setRoadCond();
                saveTestPoints = false;
                return true;
            }
            LTEWeakCoverRoadSettingDlg dlg = new LTEWeakCoverRoadSettingDlg(weakCondition);
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                weakCondition = dlg.GetConditon();
                setRoadCond();
                return true;
            }
            return false;
        }

        protected PercentRoadBuilder roadBuilder;
        public void setRoadCond()
        {
            PercentRoadCondition roadCond = new PercentRoadCondition(weakCondition.MinWeakPointPercent / 100, OnOneRoadComplete);
            roadCond.IsCheckDuration = weakCondition.CheckMinDuration;
            roadCond.MinDuration = weakCondition.MinDuration;
            roadCond.IsCheckMinLength = weakCondition.CheckMinDistance;
            roadCond.MinLength = weakCondition.MinCoverRoadDistance;
            roadCond.IsCheckMaxLength = weakCondition.CheckMaxDistance;
            roadCond.MaxLength = weakCondition.MaxCoverRoadDistance;
            roadCond.IsCheckDistanceGap = weakCondition.CheckMaxTPDistance;
            roadCond.MaxDistanceGap = weakCondition.MaxTPDistance;

            this.roadBuilder = new PercentRoadBuilder(roadCond);
        }

        private double curWeakPercent = 0;
        private double duration = 0;
        public List<TestPoint> tps { get; set; } = new List<TestPoint>();

        protected void OnOneRoadComplete(object sender, PercentRoadItem roadItem)
        {
            curWeakPercent = Math.Round(roadItem.ValidPercent * 100, 2);
            duration = roadItem.Duration;
            if (curWeakPercent < this.weakCondition.MinWeakPointPercent)
            {
                return;
            }
            tps = roadItem.TestPoints;
            addToReportInfo(tps);
        }

        public virtual void addToReportInfo(List<TestPoint> testPointList)
        {
            if (testPointList.Count == 0)
            {
                return;
            }
            WeakCoverRoadLTE weakCover = null;
            TestPoint prePoint = null;//前一点
            for (int i = 0; i < testPointList.Count; i++)
            {
                TestPoint testPoint = testPointList[i];
                int inNbCellIndex = -1;
                Dictionary<float, LTECell> nbCell = getNbMaxRSRPCellDic(testPoint, ref inNbCellIndex);

                double dis = 0;
                if (weakCover == null) //弱覆盖开始
                {
                    weakCover = new WeakCoverRoadLTE();
                }
                else
                {//上一点为弱覆盖点
                    Debug.Assert(prePoint != null, "prePoint != null");
                    dis = prePoint.Distance2(testPoint);
                }

                float? nbRSRP = getNbMaxRSRP(testPoint);
                float? sinr = getSINR(testPoint);
                float? rsrp = getRsrp(testPoint);
                short? appType = getAppType(testPoint);
                double? speed = getSpeed(testPoint);
                int? tac = getTac(testPoint);
                LTECell lteCell = getMainCell(testPoint);
                weakCover.Add(rsrp, nbRSRP, sinr, tac, dis, testPoint, lteCell);
                weakCover.AddSpeed(appType, speed);
                weakCover.JudeNbMaxRsrpCelll(nbCell, testPoint, tpNCell_EARFCN, tpNCell_PCI, inNbCellIndex);
                prePoint = testPoint;
            }

            if (weakCover != null)
            {
                weakCover.CityName = DistrictManager.GetInstance().getDistrictName(MainModel.DistrictID);
                weakCover.Duration = duration;
                saveWeakCoverInfo(ref weakCover);
            }
        }

        protected override void clearDataBeforeAnalyseFiles()
        {
            weakCoverList = new List<WeakCoverRoadLTE>();
        }

        private int areaID = -1;
        private int areaTypeID = -1;
        protected override void doStatWithQuery()
        {
            try
            {
                foreach (DTFileDataManager fileDataManager in MainModel.DTDataManager.FileDataManagers)
                {
                    tps = new List<TestPoint>();
                    areaID = fileDataManager.GetFileInfo().AreaID;
                    areaTypeID = fileDataManager.GetFileInfo().AreaTypeID;

                    foreach (TestPoint tp in fileDataManager.TestPoints)
                    {
                        roadBuilder.AddPoint(tp, this.isValidTestPoint(tp));
                    }
                    this.roadBuilder.StopRoading();
                }
            }
            catch (Exception ee)
            {
                MessageBox.Show(ee.Message + Environment.NewLine + ee.Source + Environment.NewLine + ee.StackTrace);
            }
        }
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            bool ret = false;
            try
            {
                bool ignore = isIgnoreTestPoint(testPoint);
                if (!ignore)
                {
                    ret = condition.Geometorys.GeoOp.Contains(testPoint.Longitude, testPoint.Latitude);
                }
            }
            catch
            {
                //continue
            }
            return ret;
        }

        protected bool isIgnoreTestPoint(TestPoint tp)
        {
            bool isWeakRsrp = weakCondition.IsValidateRSRP(getRsrp(tp));
            bool isWeakSinr = weakCondition.IsValidateSINR(getSINR(tp));
            bool isWeakNbMaxRsrp = weakCondition.IsValidNbMaxRSRP(getNbMaxRSRP(tp));
            if (weakCondition.CheckCondAnd)
            {
                bool isIgnore = !isWeakRsrp
                    || (weakCondition.CheckSINR && !isWeakSinr) 
                    || (weakCondition.CheckNbMaxRSRP && !isWeakNbMaxRsrp);
                return isIgnore;
            } 
            else
            {
                bool isIgnore = !(isWeakRsrp || (weakCondition.CheckSINR && isWeakSinr))
                  || (weakCondition.CheckNbMaxRSRP && !isWeakNbMaxRsrp);
                return isIgnore;
            }
        }

        protected virtual float? getNbMaxRSRP(TestPoint testPoint)
        {
            float? max = null;
            for (int i = 0; i < 10; i++)
            {
                float? n = (float?) testPoint[tpStr, i];
                if (n == null || n < -141 || n > 25)
                {
                    continue;
                }
                max = max == null ? n : Math.Max((float) max, (float) n);
            }
            return max;
        }

        protected Dictionary<float,LTECell> getNbMaxRSRPCellDic(TestPoint testPoint,ref int index)
        {
            Dictionary<float, LTECell> nbCellDic = new Dictionary<float, LTECell>();
            LTECell nbCell;
            float? max = -999;
            for (int i = 0; i < 10; i++)
            {
                float? n = (float?)testPoint[tpStr, i];
                if (n == null || n < -141 || n > 25)
                {
                    continue;
                }
                if ((float) n > (float) max)
                {
                    max = n;
                    index = i;
                } 
            }
            if (max > -999)
            {
                nbCell = getNBCell(testPoint, index);// 原testPoint.GetNBCell_LTE(index);2015/1/23
                if (nbCell == null)
                {
                    nbCell = new LTECell();
                }
                nbCellDic.Add((float)max, nbCell);
            }           
            return nbCellDic;
        }


        protected virtual float? getRsrp(TestPoint tp)
        {
            return (float?)tp[tpRSRP];
        }

        protected virtual float? getSINR(TestPoint tp)
        {
            return (float?) tp[tpSINR];
        }

        protected virtual short? getAppType(TestPoint tp)
        {
            return (short?)tp[tpAppType];
        }

        protected virtual double? getSpeed(TestPoint tp)
        {
            return (double?)tp[tpSpeed];
        }

        protected virtual int? getTac(TestPoint tp)
        {
            return (int?)(ushort?)tp[tpTac];
        }

        protected List<WeakCoverRoadLTE> weakCoverList = null;

        protected void saveWeakCoverInfo(ref WeakCoverRoadLTE info)
        {
            if (info == null || !weakCondition.MatchMinWeakCoverDuration(info.Duration) || !weakCondition.MatchMinWeakCoverDistance(info.Distance))
            {
                info = null;
                return;
            }
            if (weakCoverList == null)
            {
                weakCoverList = new List<WeakCoverRoadLTE>();
            }
            if (!weakCoverList.Contains(info))
            {
                info.SN = weakCoverList.Count + 1;
                info.WeakPointPercent = curWeakPercent;
                info.FindRoadName();
                info.FindAreaName();
                info.FindGridName();
                info.FindAgentName();
                info.SetMotorWay(areaID, areaTypeID);
                weakCoverList.Add(info);
            }

            expandSampleRect(info.TestPoints);

            info = null;
        }

        protected void expandSampleRect(List<TestPoint> sampleList)
        {
            foreach (TestPoint tp in sampleList)
            {
                if (sampleRect.x2 == -999 || tp.Longitude > sampleRect.x2)
                {
                    sampleRect.x2 = tp.Longitude;
                }
                if (sampleRect.x1 == -999 || tp.Longitude < sampleRect.x1)
                {
                    sampleRect.x1 = tp.Longitude;
                }
                if (sampleRect.y2 == -999 || tp.Latitude > sampleRect.y2)
                {
                    sampleRect.y2 = tp.Latitude;
                }
                if (sampleRect.y1 == -999 || tp.Latitude < sampleRect.y1)
                {
                    sampleRect.y1 = tp.Latitude;
                }
            }
        }

        protected List<LTECell> getLTECellInGeometry()
        {
            ////外扩sampleRect
            double offset = (double)weakCondition.MaxSampleCellDistance / 100000;
            sampleRect.x1 -= offset;
            sampleRect.x2 += offset;
            sampleRect.y1 -= offset;
            sampleRect.y2 += offset;

            List<LTECell> cells = this.MainModel.CellManager.GetCurrentLTECells();
            List<LTECell> cellInLst = new List<LTECell>();

            foreach (LTECell cell in cells)
            {
                if (cell.BelongBTS.Type == LTEBTSType.Indoor)
                {
                    continue;
                }

                if (sampleRect.IsPointInThisRect(cell.Longitude, cell.Latitude))
                {
                    cellInLst.Add(cell);
                }
            }

            return cellInLst;
        }

        public void doSomethingAfterAna()
        {
             List<LTECell> cellInLst = getLTECellInGeometry();

            foreach (WeakCoverRoadLTE info in weakCoverList)
            {
                foreach (TestPoint testPoint in info.TestPoints)
                {
                    bool isPlanProblem = true;   //判断是优化问题，还是规划问题
                    foreach (LTECell cell in cellInLst)
                    {
                        if (cell.GetDistance(testPoint.Longitude, testPoint.Latitude) <= weakCondition.MaxSampleCellDistance
                            && MathFuncs.JudgePoint(cell.Longitude, cell.Latitude, testPoint.Longitude, testPoint.Latitude, (int)cell.Direction, weakCondition.MaxSampleCellAngle))
                        {
                            isPlanProblem = false;  //采样点在小区的方向角内，是优化问题
                            break;
                        }
                    }

                    int? lac = (int?)(ushort?)testPoint[tpTac];
                    LTECell lteCell = getMainCell(testPoint);//testPoint.GetMainCell_LTE();2015/1/23
                    addWeakCoverRoadLTEValue(info, isPlanProblem, lac, lteCell);

                    if (saveTestPoints)
                    {
                        MainModel.DTDataManager.Add(testPoint);
                    }
                }
            }

        }

        private static void addWeakCoverRoadLTEValue(WeakCoverRoadLTE info, bool isPlanProblem, int? lac, LTECell lteCell)
        {
            if (isPlanProblem)
            {
                info.TestPointCount_Plan++;
            }
            else
            {
                info.TestPointCount_Opt++;

                if (lteCell != null)
                {
                    string lacci = lac.ToString() + "_" + lteCell.SCellID.ToString();
                    if (!info.lacciList_opt.Contains(lacci))
                    {
                        info.lacciList_opt.Add(lacci);
                    }
                    if (!info.cellNames_opt.Contains(lteCell.Name))
                    {
                        info.cellNames_opt.Add(lteCell.Name);
                    }
                }
            }
        }

        protected override void getResultsAfterQuery()
        {
            doSomethingAfterAna();
        }

        #region Background
        public override BackgroundFuncType FuncType
        {
            get { return BackgroundFuncType.LTE业务专题; }
        }

        public override BackgroundSubFuncType SubFuncType
        {
            get { return BackgroundSubFuncType.覆盖; }
        }

        public override Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                //param["BackgroundStat"] = backgroundStat;
                //param["MaxRxLev"] = weakCovRoadCond.maxRxlev;
                //param["SampleDistance"] = weakCovRoadCond.sampleDistance;
                return param;
            }
            //set
            //{
            //    if (value == null || value.Count <= 0)
            //    {
            //        return;
            //    }
            //    Dictionary<string, object> param = value;
            //    if (param.ContainsKey("BackgroundStat"))
            //    {
            //        backgroundStat = (bool)param["BackgroundStat"];
            //    }
            //    if (param.ContainsKey("MaxRxLev"))
            //    {
            //        weakCovRoadCond.maxRxlev = int.Parse(param["MaxRxLev"].ToString());
            //    }
            //    if (param.ContainsKey("SampleDistance"))
            //    {
            //        weakCovRoadCond.sampleDistance = int.Parse(param["SampleDistance"].ToString());
            //    }
            //}
        }

        public override PropertiesControl Properties
        {
            get
            {
                return null;
                //return new WeakCovRoadProperties_GSM(this);
            }
        }

        protected override void saveBackgroundData()
        {
            //List<BackgroundResult> bgResultList = new List<BackgroundResult>();
            //foreach (ZTWeakCovRoadInfo item in ZTWeakCovRoadInfoList)
            //{
            //    BackgroundResult result = item.ConvertToBackgroundResult();
            //    result.SubFuncID = GetSubFuncID();
            //    bgResultList.Add(result);
            //}
            //BackgroundFuncQueryManager.GetInstance().SaveResult_Road(GetSubFuncID(), curAnaFileInfo, bgResultList);
            //ZTWeakCovRoadInfoList.Clear();
        }
        #endregion
    }

    public class WeakCoverRoadCondition_LTE
    {
        public float MaxRSRP { get; set; } = -105;
        public double MinWeakPointPercent { get; set; } = 100;
        public double MinCoverRoadDistance { get; set; } = 50;//最小持续距离
        public double MaxCoverRoadDistance { get; set; } = 5000;//最大持续距离
        public double MinDuration { get; set; } = 10;//最小持续时长
        public double MaxTPDistance { get; set; } = 50;
        public int MaxSampleCellDistance { get; set; } = 300;
        public int MaxSampleCellAngle { get; set; } = 60;
        public bool IsAssocAnalysis { get; set; } = false;

        public bool IsValidateRSRP(float? rxLev)
        {
            return rxLev <= MaxRSRP && rxLev >= -141;
        }

        public bool IsValidateSINR(float? sinr)
        {
            return sinr < MaxSINR && sinr >= -50;
        }
        public bool MatchMinWeakCoverDistance(double distance)
        {
            if (CheckMinDistance)
            {
                return distance >= MinCoverRoadDistance;
            }
            else
            {
                return true;
            }
        }
        public bool MatchMinWeakCoverDuration(double duration)
        {
            if (CheckMinDuration)
            {
                return duration >= MinDuration;
            }
            else
            {
                return true;
            }
        }
        public bool Match2TestpointsMaxDistance(double distance)
        {
            return distance <= MaxTPDistance;
        }
        public bool CheckCondAnd { get; set; }

        public bool CheckSINR { get; set; }

        public bool CheckNbMaxRSRP { get; set; }
        
        public bool CheckMinDistance { get; set; }
        
        public bool CheckMaxDistance { get; set; } = false;

        public bool CheckMaxTPDistance { get; set; } = true;

        public bool CheckMinDuration { get; set; }
        
        public float NbMaxRSRP { get; set; } = -100;
        
        public float MaxSINR { get; set; } = -3;

        internal bool IsValidNbMaxRSRP(float? nbRSRP)
        {
            if (compareSymble == CompareSymble.LessThan)
            {
                return nbRSRP < NbMaxRSRP && nbRSRP >= -141;
            }
            return nbRSRP > NbMaxRSRP && nbRSRP >= -141;
        }

        public CompareSymble compareSymble { get; set; } = CompareSymble.LessThan;
    }

    public enum CompareSymble
    {
        LessThan = 0,
        GreaterThan,
    }
}

