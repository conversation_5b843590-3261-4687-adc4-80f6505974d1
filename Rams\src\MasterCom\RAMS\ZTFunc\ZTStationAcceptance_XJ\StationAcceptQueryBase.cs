﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc;
using MasterCom.Util.UiEx;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTStationAcceptance_XJ
{
    public class StationAcceptQueryBase : DIYReplayFileQuery
    {
        /// <summary>
        /// 单验入口 - 回放并分析文件
        /// </summary>
        /// <param name="mainModel"></param>
        public StationAcceptQueryBase(MainModel mainModel)
            : base(mainModel)
        {
        }

        public override string Name { get { return "单站验收"; } }
        protected string exportReportFiles = "";
        protected StationAcceptManagerBase manager;
        protected string errMsg;

        protected override bool isValidCondition()
        {
            StationAcceptConditionBase cond = new StationAcceptConditionBase();
            System.Windows.Forms.FolderBrowserDialog fbd = new System.Windows.Forms.FolderBrowserDialog();
            if (fbd.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                cond.SaveFolder = fbd.SelectedPath;
                initManager(cond);
                return true;
            }
            return false;
        }

        /// <summary>
        /// 子类必须重写
        /// </summary>
        protected virtual void initManager(StationAcceptConditionBase cond)
        {
            //manager = new StationAcceptManagerBase();
            manager.SetAcceptCond(cond);
        }

        protected override DIYReplayContentOption getDIYReplayContent()
        {
            DIYReplayContentOption option = new DIYReplayContentOption();
            option.DefaultSerialThemeName = null;

            foreach (string col in queryColumns)
            {
                List<ColumnDefItem> items = InterfaceManager.GetInstance().GetColumnDefByShowName(col);
                option.SampleColumns.AddRange(items);
            }

            option.EventInclude = true;
            option.MessageInclude = true;

            return option;
        }

        protected override void query()
        {
            MainModel.FireDTDataChanged(MainModel.MainForm);
            errMsg = null;
            base.query();
        }

        protected override void fireShowResult()
        {
            if (!string.IsNullOrEmpty(errMsg))
            {
                System.Windows.Forms.MessageBox.Show(errMsg, Name, System.Windows.Forms.MessageBoxButtons.OK);
            }
            else if (string.IsNullOrEmpty(exportReportFiles))
            {
                System.Windows.Forms.MessageBox.Show("没有结果可以导出,请检查文件命名和工参信息", Name,
                    System.Windows.Forms.MessageBoxButtons.OK);
            }
            else
            {
                System.Windows.Forms.MessageBox.Show(string.Format("({0})站点的报告导出完成!"
                    , exportReportFiles.TrimEnd(',')), Name, System.Windows.Forms.MessageBoxButtons.OK);
            }
        }

        protected override void doPostReplayAction()
        {
            WaitTextBox.Show("正在导出Excel文件...", afterAnalyzeInThread);
        }

        protected override void queryReplayInfo(ClientProxy clientProxy, Package package, FileInfo fileInfo)
        {
            base.queryReplayInfo(clientProxy, package, fileInfo);
            if (judgeValidFile(fileInfo))
            {
                analyzeFile(fileInfo);
                MainModel.DTDataManager.Clear();
            }
        }

        protected virtual bool judgeValidFile(FileInfo fileInfo)
        {
            if (MainModel.DTDataManager.FileDataManagers.Count == 0)
            {
                return false;
            }
            return true;
        }

        protected virtual void analyzeFile(FileInfo fileInfo)
        {
            manager.AnalyzeFile(fileInfo, MainModel.DTDataManager.FileDataManagers[0]);
        }

        protected virtual void afterAnalyzeInThread()
        {
            try
            {
                exportReportFiles = "";
                if (string.IsNullOrEmpty(errMsg))
                {
                    manager.DoWorkAfterAnalyze();
                    exportReportFiles = manager.HasExportedFiles;
                    errMsg = manager.ErrMsg.ToString();
                    manager = null;
                }
            }
            catch (Exception ex)
            {
                errMsg = ex.Message + Environment.NewLine + ex.StackTrace;
            }
            finally
            {
                System.Threading.Thread.Sleep(300);
                WaitTextBox.Close();
            }
        }

        /// <summary>
        /// 子类必须重写
        /// </summary>
        protected virtual List<string> queryColumns
        {
            get
            {
                return new List<string>()
                {
                     "isampleid",
                     "itime",
                     "ilongitude",
                     "ilatitude"
                };
            }
        }
    }

    public class StationAcceptConditionBase
    {
        public string SaveFolder
        {
            get;
            set;
        }
    }
}
