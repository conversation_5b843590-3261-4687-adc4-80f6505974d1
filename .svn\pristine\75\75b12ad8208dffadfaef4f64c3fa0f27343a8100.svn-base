﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.CQT
{
    public partial class SetHoCondForm : BaseDialog
    {
        public SetHoCondForm()
        {
            InitializeComponent();
        }

        /// <summary>
        /// 获取设置主服电平
        /// </summary>
        public int MainCellRxlev
        {
            get
            {
                int iMainCellRxlev = 0;
                if (int.TryParse(this.mainCellTb.Text, out iMainCellRxlev))
                {
                    if (iMainCellRxlev >= -110 && iMainCellRxlev <= -10)
                        return iMainCellRxlev;
                    else
                        return 0;
                }
                return 0;
            }
        }

        /// <summary>
        /// 获取设置邻区电平
        /// </summary>
        public int NbCellRxlev
        {
            get
            {
                int iNbCellRxlev = 0;
                if (int.TryParse(this.nbCellTb.Text, out iNbCellRxlev))
                {
                    if (iNbCellRxlev >= -110 && iNbCellRxlev <= 110)
                        return iNbCellRxlev;
                    else
                        return 0;
                }
                return 0;
            }
        }

        /// <summary>
        /// 获取设置持续时长
        /// </summary>
        public int LastTime
        {
            get
            {
                int iLastTime = 0;
                if (int.TryParse(this.lastTimeTb.Text, out iLastTime))
                {
                    if (iLastTime >= 0)
                        return iLastTime;
                    else
                        return 0;
                }
                return 0;
            }
        }

        /// <summary>
        /// 是否只分析占用模式采样点
        /// </summary>
        public bool IsDeticateOnly
        {
            get
            {
                return deticateOnlyCb.Checked;
            }
        }
    }
}
