﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class DelayReasonSettingDlg
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.labelControl13 = new DevExpress.XtraEditors.LabelControl();
            this.numSiteP2iMt = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl14 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl15 = new DevExpress.XtraEditors.LabelControl();
            this.numSiteU2RMt = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl16 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl17 = new DevExpress.XtraEditors.LabelControl();
            this.numSiteU2UMt = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl18 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl19 = new DevExpress.XtraEditors.LabelControl();
            this.numSiteP2UMt = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl20 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl7 = new DevExpress.XtraEditors.LabelControl();
            this.numSiteP2POKMt = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl8 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl11 = new DevExpress.XtraEditors.LabelControl();
            this.numSiteP2PMt = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl12 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.numSiteT2PMt = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl5 = new DevExpress.XtraEditors.LabelControl();
            this.numSiteI2TMt = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl6 = new DevExpress.XtraEditors.LabelControl();
            this.btnCancel = new DevExpress.XtraEditors.SimpleButton();
            this.btnOK = new DevExpress.XtraEditors.SimpleButton();
            this.labelControl23 = new DevExpress.XtraEditors.LabelControl();
            this.numSiteU2RMo = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl24 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl25 = new DevExpress.XtraEditors.LabelControl();
            this.numSiteU2UMo = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl26 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl27 = new DevExpress.XtraEditors.LabelControl();
            this.numSiteP2UMo = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl28 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl29 = new DevExpress.XtraEditors.LabelControl();
            this.numSiteP2POKMo = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl30 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl31 = new DevExpress.XtraEditors.LabelControl();
            this.numSiteP2PMo = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl32 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl33 = new DevExpress.XtraEditors.LabelControl();
            this.numSiteT2PMo = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl34 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl35 = new DevExpress.XtraEditors.LabelControl();
            this.numSiteI2TMo = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl36 = new DevExpress.XtraEditors.LabelControl();
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.groupControl2 = new DevExpress.XtraEditors.GroupControl();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.numSiteFind = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            ((System.ComponentModel.ISupportInitialize)(this.numSiteP2iMt.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSiteU2RMt.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSiteU2UMt.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSiteP2UMt.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSiteP2POKMt.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSiteP2PMt.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSiteT2PMt.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSiteI2TMt.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSiteU2RMo.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSiteU2UMo.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSiteP2UMo.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSiteP2POKMo.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSiteP2PMo.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSiteT2PMo.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSiteI2TMo.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).BeginInit();
            this.groupControl2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numSiteFind.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // labelControl13
            // 
            this.labelControl13.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl13.Appearance.Options.UseFont = true;
            this.labelControl13.Location = new System.Drawing.Point(106, 37);
            this.labelControl13.Name = "labelControl13";
            this.labelControl13.Size = new System.Drawing.Size(102, 12);
            this.labelControl13.TabIndex = 81;
            this.labelControl13.Text = "Paging->Invite ≥";
            // 
            // numSiteP2iMt
            // 
            this.numSiteP2iMt.EditValue = new decimal(new int[] {
            600,
            0,
            0,
            0});
            this.numSiteP2iMt.Location = new System.Drawing.Point(220, 33);
            this.numSiteP2iMt.Name = "numSiteP2iMt";
            this.numSiteP2iMt.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numSiteP2iMt.Properties.Appearance.Options.UseFont = true;
            this.numSiteP2iMt.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numSiteP2iMt.Properties.IsFloatValue = false;
            this.numSiteP2iMt.Properties.Mask.EditMask = "N00";
            this.numSiteP2iMt.Properties.MaxValue = new decimal(new int[] {
            5000,
            0,
            0,
            0});
            this.numSiteP2iMt.Properties.MinValue = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numSiteP2iMt.Size = new System.Drawing.Size(81, 20);
            this.numSiteP2iMt.TabIndex = 78;
            // 
            // labelControl14
            // 
            this.labelControl14.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl14.Appearance.Options.UseFont = true;
            this.labelControl14.Location = new System.Drawing.Point(311, 36);
            this.labelControl14.Name = "labelControl14";
            this.labelControl14.Size = new System.Drawing.Size(24, 12);
            this.labelControl14.TabIndex = 82;
            this.labelControl14.Text = "毫秒";
            // 
            // labelControl15
            // 
            this.labelControl15.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl15.Appearance.Options.UseFont = true;
            this.labelControl15.Location = new System.Drawing.Point(28, 232);
            this.labelControl15.Name = "labelControl15";
            this.labelControl15.Size = new System.Drawing.Size(180, 12);
            this.labelControl15.TabIndex = 79;
            this.labelControl15.Text = "UPDATE(200)OK->Ringing(180) ≥";
            // 
            // numSiteU2RMt
            // 
            this.numSiteU2RMt.EditValue = new decimal(new int[] {
            20,
            0,
            0,
            0});
            this.numSiteU2RMt.Location = new System.Drawing.Point(220, 228);
            this.numSiteU2RMt.Name = "numSiteU2RMt";
            this.numSiteU2RMt.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numSiteU2RMt.Properties.Appearance.Options.UseFont = true;
            this.numSiteU2RMt.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numSiteU2RMt.Properties.IsFloatValue = false;
            this.numSiteU2RMt.Properties.Mask.EditMask = "N00";
            this.numSiteU2RMt.Properties.MaxValue = new decimal(new int[] {
            5000,
            0,
            0,
            0});
            this.numSiteU2RMt.Properties.MinValue = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numSiteU2RMt.Size = new System.Drawing.Size(81, 20);
            this.numSiteU2RMt.TabIndex = 77;
            // 
            // labelControl16
            // 
            this.labelControl16.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl16.Appearance.Options.UseFont = true;
            this.labelControl16.Location = new System.Drawing.Point(312, 231);
            this.labelControl16.Name = "labelControl16";
            this.labelControl16.Size = new System.Drawing.Size(24, 12);
            this.labelControl16.TabIndex = 80;
            this.labelControl16.Text = "毫秒";
            // 
            // labelControl17
            // 
            this.labelControl17.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl17.Appearance.Options.UseFont = true;
            this.labelControl17.Location = new System.Drawing.Point(64, 205);
            this.labelControl17.Name = "labelControl17";
            this.labelControl17.Size = new System.Drawing.Size(144, 12);
            this.labelControl17.TabIndex = 75;
            this.labelControl17.Text = "UPDATE->UPDATE(200)OK ≥";
            // 
            // numSiteU2UMt
            // 
            this.numSiteU2UMt.EditValue = new decimal(new int[] {
            30,
            0,
            0,
            0});
            this.numSiteU2UMt.Location = new System.Drawing.Point(220, 200);
            this.numSiteU2UMt.Name = "numSiteU2UMt";
            this.numSiteU2UMt.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numSiteU2UMt.Properties.Appearance.Options.UseFont = true;
            this.numSiteU2UMt.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numSiteU2UMt.Properties.IsFloatValue = false;
            this.numSiteU2UMt.Properties.Mask.EditMask = "N00";
            this.numSiteU2UMt.Properties.MaxValue = new decimal(new int[] {
            5000,
            0,
            0,
            0});
            this.numSiteU2UMt.Properties.MinValue = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numSiteU2UMt.Size = new System.Drawing.Size(81, 20);
            this.numSiteU2UMt.TabIndex = 72;
            // 
            // labelControl18
            // 
            this.labelControl18.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl18.Appearance.Options.UseFont = true;
            this.labelControl18.Location = new System.Drawing.Point(311, 203);
            this.labelControl18.Name = "labelControl18";
            this.labelControl18.Size = new System.Drawing.Size(24, 12);
            this.labelControl18.TabIndex = 76;
            this.labelControl18.Text = "毫秒";
            // 
            // labelControl19
            // 
            this.labelControl19.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl19.Appearance.Options.UseFont = true;
            this.labelControl19.Location = new System.Drawing.Point(70, 176);
            this.labelControl19.Name = "labelControl19";
            this.labelControl19.Size = new System.Drawing.Size(138, 12);
            this.labelControl19.TabIndex = 73;
            this.labelControl19.Text = "PRACK(200)OK->UPDATE ≥";
            // 
            // numSiteP2UMt
            // 
            this.numSiteP2UMt.EditValue = new decimal(new int[] {
            500,
            0,
            0,
            0});
            this.numSiteP2UMt.Location = new System.Drawing.Point(220, 172);
            this.numSiteP2UMt.Name = "numSiteP2UMt";
            this.numSiteP2UMt.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numSiteP2UMt.Properties.Appearance.Options.UseFont = true;
            this.numSiteP2UMt.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numSiteP2UMt.Properties.IsFloatValue = false;
            this.numSiteP2UMt.Properties.Mask.EditMask = "N00";
            this.numSiteP2UMt.Properties.MaxValue = new decimal(new int[] {
            5000,
            0,
            0,
            0});
            this.numSiteP2UMt.Properties.MinValue = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numSiteP2UMt.Size = new System.Drawing.Size(81, 20);
            this.numSiteP2UMt.TabIndex = 71;
            // 
            // labelControl20
            // 
            this.labelControl20.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl20.Appearance.Options.UseFont = true;
            this.labelControl20.Location = new System.Drawing.Point(312, 175);
            this.labelControl20.Name = "labelControl20";
            this.labelControl20.Size = new System.Drawing.Size(24, 12);
            this.labelControl20.TabIndex = 74;
            this.labelControl20.Text = "毫秒";
            // 
            // labelControl7
            // 
            this.labelControl7.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl7.Appearance.Options.UseFont = true;
            this.labelControl7.Location = new System.Drawing.Point(76, 149);
            this.labelControl7.Name = "labelControl7";
            this.labelControl7.Size = new System.Drawing.Size(132, 12);
            this.labelControl7.TabIndex = 69;
            this.labelControl7.Text = "PRACK->PRACK(200)OK ≥";
            // 
            // numSiteP2POKMt
            // 
            this.numSiteP2POKMt.EditValue = new decimal(new int[] {
            20,
            0,
            0,
            0});
            this.numSiteP2POKMt.Location = new System.Drawing.Point(220, 145);
            this.numSiteP2POKMt.Name = "numSiteP2POKMt";
            this.numSiteP2POKMt.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numSiteP2POKMt.Properties.Appearance.Options.UseFont = true;
            this.numSiteP2POKMt.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numSiteP2POKMt.Properties.IsFloatValue = false;
            this.numSiteP2POKMt.Properties.Mask.EditMask = "N00";
            this.numSiteP2POKMt.Properties.MaxValue = new decimal(new int[] {
            5000,
            0,
            0,
            0});
            this.numSiteP2POKMt.Properties.MinValue = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numSiteP2POKMt.Size = new System.Drawing.Size(81, 20);
            this.numSiteP2POKMt.TabIndex = 66;
            // 
            // labelControl8
            // 
            this.labelControl8.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl8.Appearance.Options.UseFont = true;
            this.labelControl8.Location = new System.Drawing.Point(311, 148);
            this.labelControl8.Name = "labelControl8";
            this.labelControl8.Size = new System.Drawing.Size(24, 12);
            this.labelControl8.TabIndex = 70;
            this.labelControl8.Text = "毫秒";
            // 
            // labelControl11
            // 
            this.labelControl11.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl11.Appearance.Options.UseFont = true;
            this.labelControl11.Location = new System.Drawing.Point(70, 122);
            this.labelControl11.Name = "labelControl11";
            this.labelControl11.Size = new System.Drawing.Size(138, 12);
            this.labelControl11.TabIndex = 67;
            this.labelControl11.Text = "Progress(183)->PRACK ≥";
            // 
            // numSiteP2PMt
            // 
            this.numSiteP2PMt.EditValue = new decimal(new int[] {
            1000,
            0,
            0,
            0});
            this.numSiteP2PMt.Location = new System.Drawing.Point(220, 118);
            this.numSiteP2PMt.Name = "numSiteP2PMt";
            this.numSiteP2PMt.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numSiteP2PMt.Properties.Appearance.Options.UseFont = true;
            this.numSiteP2PMt.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numSiteP2PMt.Properties.IsFloatValue = false;
            this.numSiteP2PMt.Properties.Mask.EditMask = "N00";
            this.numSiteP2PMt.Properties.MaxValue = new decimal(new int[] {
            5000,
            0,
            0,
            0});
            this.numSiteP2PMt.Properties.MinValue = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numSiteP2PMt.Size = new System.Drawing.Size(81, 20);
            this.numSiteP2PMt.TabIndex = 65;
            // 
            // labelControl12
            // 
            this.labelControl12.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl12.Appearance.Options.UseFont = true;
            this.labelControl12.Location = new System.Drawing.Point(312, 121);
            this.labelControl12.Name = "labelControl12";
            this.labelControl12.Size = new System.Drawing.Size(24, 12);
            this.labelControl12.TabIndex = 68;
            this.labelControl12.Text = "毫秒";
            // 
            // labelControl1
            // 
            this.labelControl1.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl1.Appearance.Options.UseFont = true;
            this.labelControl1.Location = new System.Drawing.Point(51, 92);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(156, 12);
            this.labelControl1.TabIndex = 63;
            this.labelControl1.Text = "Try(100)->Progress(183) ≥";
            // 
            // numSiteT2PMt
            // 
            this.numSiteT2PMt.EditValue = new decimal(new int[] {
            30,
            0,
            0,
            0});
            this.numSiteT2PMt.Location = new System.Drawing.Point(220, 88);
            this.numSiteT2PMt.Name = "numSiteT2PMt";
            this.numSiteT2PMt.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numSiteT2PMt.Properties.Appearance.Options.UseFont = true;
            this.numSiteT2PMt.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numSiteT2PMt.Properties.IsFloatValue = false;
            this.numSiteT2PMt.Properties.Mask.EditMask = "N00";
            this.numSiteT2PMt.Properties.MaxValue = new decimal(new int[] {
            5000,
            0,
            0,
            0});
            this.numSiteT2PMt.Properties.MinValue = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numSiteT2PMt.Size = new System.Drawing.Size(81, 20);
            this.numSiteT2PMt.TabIndex = 60;
            // 
            // labelControl4
            // 
            this.labelControl4.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl4.Appearance.Options.UseFont = true;
            this.labelControl4.Location = new System.Drawing.Point(311, 91);
            this.labelControl4.Name = "labelControl4";
            this.labelControl4.Size = new System.Drawing.Size(24, 12);
            this.labelControl4.TabIndex = 64;
            this.labelControl4.Text = "毫秒";
            // 
            // labelControl5
            // 
            this.labelControl5.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl5.Appearance.Options.UseFont = true;
            this.labelControl5.Location = new System.Drawing.Point(94, 65);
            this.labelControl5.Name = "labelControl5";
            this.labelControl5.Size = new System.Drawing.Size(114, 12);
            this.labelControl5.TabIndex = 61;
            this.labelControl5.Text = "Invite->Try(100) ≥";
            // 
            // numSiteI2TMt
            // 
            this.numSiteI2TMt.EditValue = new decimal(new int[] {
            20,
            0,
            0,
            0});
            this.numSiteI2TMt.Location = new System.Drawing.Point(220, 61);
            this.numSiteI2TMt.Name = "numSiteI2TMt";
            this.numSiteI2TMt.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numSiteI2TMt.Properties.Appearance.Options.UseFont = true;
            this.numSiteI2TMt.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numSiteI2TMt.Properties.IsFloatValue = false;
            this.numSiteI2TMt.Properties.Mask.EditMask = "N00";
            this.numSiteI2TMt.Properties.MaxValue = new decimal(new int[] {
            5000,
            0,
            0,
            0});
            this.numSiteI2TMt.Properties.MinValue = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numSiteI2TMt.Size = new System.Drawing.Size(81, 20);
            this.numSiteI2TMt.TabIndex = 59;
            // 
            // labelControl6
            // 
            this.labelControl6.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl6.Appearance.Options.UseFont = true;
            this.labelControl6.Location = new System.Drawing.Point(312, 64);
            this.labelControl6.Name = "labelControl6";
            this.labelControl6.Size = new System.Drawing.Size(24, 12);
            this.labelControl6.TabIndex = 62;
            this.labelControl6.Text = "毫秒";
            // 
            // btnCancel
            // 
            this.btnCancel.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnCancel.Appearance.Options.UseFont = true;
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnCancel.Location = new System.Drawing.Point(297, 545);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 71;
            this.btnCancel.Text = "取消";
            this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
            // 
            // btnOK
            // 
            this.btnOK.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnOK.Appearance.Options.UseFont = true;
            this.btnOK.Location = new System.Drawing.Point(213, 545);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 70;
            this.btnOK.Text = "确定";
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // labelControl23
            // 
            this.labelControl23.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl23.Appearance.Options.UseFont = true;
            this.labelControl23.Location = new System.Drawing.Point(30, 203);
            this.labelControl23.Name = "labelControl23";
            this.labelControl23.Size = new System.Drawing.Size(180, 12);
            this.labelControl23.TabIndex = 79;
            this.labelControl23.Text = "UPDATE(200)OK->Ringing(180) ≥";
            // 
            // numSiteU2RMo
            // 
            this.numSiteU2RMo.EditValue = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.numSiteU2RMo.Location = new System.Drawing.Point(219, 199);
            this.numSiteU2RMo.Name = "numSiteU2RMo";
            this.numSiteU2RMo.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numSiteU2RMo.Properties.Appearance.Options.UseFont = true;
            this.numSiteU2RMo.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numSiteU2RMo.Properties.IsFloatValue = false;
            this.numSiteU2RMo.Properties.Mask.EditMask = "N00";
            this.numSiteU2RMo.Properties.MaxValue = new decimal(new int[] {
            5000,
            0,
            0,
            0});
            this.numSiteU2RMo.Properties.MinValue = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numSiteU2RMo.Size = new System.Drawing.Size(81, 20);
            this.numSiteU2RMo.TabIndex = 77;
            // 
            // labelControl24
            // 
            this.labelControl24.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl24.Appearance.Options.UseFont = true;
            this.labelControl24.Location = new System.Drawing.Point(311, 202);
            this.labelControl24.Name = "labelControl24";
            this.labelControl24.Size = new System.Drawing.Size(24, 12);
            this.labelControl24.TabIndex = 80;
            this.labelControl24.Text = "毫秒";
            // 
            // labelControl25
            // 
            this.labelControl25.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl25.Appearance.Options.UseFont = true;
            this.labelControl25.Location = new System.Drawing.Point(65, 176);
            this.labelControl25.Name = "labelControl25";
            this.labelControl25.Size = new System.Drawing.Size(144, 12);
            this.labelControl25.TabIndex = 75;
            this.labelControl25.Text = "UPDATE->UPDATE(200)OK ≥";
            // 
            // numSiteU2UMo
            // 
            this.numSiteU2UMo.EditValue = new decimal(new int[] {
            800,
            0,
            0,
            0});
            this.numSiteU2UMo.Location = new System.Drawing.Point(219, 171);
            this.numSiteU2UMo.Name = "numSiteU2UMo";
            this.numSiteU2UMo.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numSiteU2UMo.Properties.Appearance.Options.UseFont = true;
            this.numSiteU2UMo.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numSiteU2UMo.Properties.IsFloatValue = false;
            this.numSiteU2UMo.Properties.Mask.EditMask = "N00";
            this.numSiteU2UMo.Properties.MaxValue = new decimal(new int[] {
            5000,
            0,
            0,
            0});
            this.numSiteU2UMo.Properties.MinValue = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numSiteU2UMo.Size = new System.Drawing.Size(81, 20);
            this.numSiteU2UMo.TabIndex = 72;
            // 
            // labelControl26
            // 
            this.labelControl26.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl26.Appearance.Options.UseFont = true;
            this.labelControl26.Location = new System.Drawing.Point(310, 174);
            this.labelControl26.Name = "labelControl26";
            this.labelControl26.Size = new System.Drawing.Size(24, 12);
            this.labelControl26.TabIndex = 76;
            this.labelControl26.Text = "毫秒";
            // 
            // labelControl27
            // 
            this.labelControl27.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl27.Appearance.Options.UseFont = true;
            this.labelControl27.Location = new System.Drawing.Point(70, 147);
            this.labelControl27.Name = "labelControl27";
            this.labelControl27.Size = new System.Drawing.Size(138, 12);
            this.labelControl27.TabIndex = 73;
            this.labelControl27.Text = "PRACK(200)OK->UPDATE ≥";
            // 
            // numSiteP2UMo
            // 
            this.numSiteP2UMo.EditValue = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.numSiteP2UMo.Location = new System.Drawing.Point(219, 143);
            this.numSiteP2UMo.Name = "numSiteP2UMo";
            this.numSiteP2UMo.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numSiteP2UMo.Properties.Appearance.Options.UseFont = true;
            this.numSiteP2UMo.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numSiteP2UMo.Properties.IsFloatValue = false;
            this.numSiteP2UMo.Properties.Mask.EditMask = "N00";
            this.numSiteP2UMo.Properties.MaxValue = new decimal(new int[] {
            5000,
            0,
            0,
            0});
            this.numSiteP2UMo.Properties.MinValue = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numSiteP2UMo.Size = new System.Drawing.Size(81, 20);
            this.numSiteP2UMo.TabIndex = 71;
            // 
            // labelControl28
            // 
            this.labelControl28.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl28.Appearance.Options.UseFont = true;
            this.labelControl28.Location = new System.Drawing.Point(311, 146);
            this.labelControl28.Name = "labelControl28";
            this.labelControl28.Size = new System.Drawing.Size(24, 12);
            this.labelControl28.TabIndex = 74;
            this.labelControl28.Text = "毫秒";
            // 
            // labelControl29
            // 
            this.labelControl29.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl29.Appearance.Options.UseFont = true;
            this.labelControl29.Location = new System.Drawing.Point(76, 120);
            this.labelControl29.Name = "labelControl29";
            this.labelControl29.Size = new System.Drawing.Size(132, 12);
            this.labelControl29.TabIndex = 69;
            this.labelControl29.Text = "PRACK->PRACK(200)OK ≥";
            // 
            // numSiteP2POKMo
            // 
            this.numSiteP2POKMo.EditValue = new decimal(new int[] {
            500,
            0,
            0,
            0});
            this.numSiteP2POKMo.Location = new System.Drawing.Point(219, 116);
            this.numSiteP2POKMo.Name = "numSiteP2POKMo";
            this.numSiteP2POKMo.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numSiteP2POKMo.Properties.Appearance.Options.UseFont = true;
            this.numSiteP2POKMo.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numSiteP2POKMo.Properties.IsFloatValue = false;
            this.numSiteP2POKMo.Properties.Mask.EditMask = "N00";
            this.numSiteP2POKMo.Properties.MaxValue = new decimal(new int[] {
            5000,
            0,
            0,
            0});
            this.numSiteP2POKMo.Properties.MinValue = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numSiteP2POKMo.Size = new System.Drawing.Size(81, 20);
            this.numSiteP2POKMo.TabIndex = 66;
            // 
            // labelControl30
            // 
            this.labelControl30.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl30.Appearance.Options.UseFont = true;
            this.labelControl30.Location = new System.Drawing.Point(310, 119);
            this.labelControl30.Name = "labelControl30";
            this.labelControl30.Size = new System.Drawing.Size(24, 12);
            this.labelControl30.TabIndex = 70;
            this.labelControl30.Text = "毫秒";
            // 
            // labelControl31
            // 
            this.labelControl31.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl31.Appearance.Options.UseFont = true;
            this.labelControl31.Location = new System.Drawing.Point(70, 93);
            this.labelControl31.Name = "labelControl31";
            this.labelControl31.Size = new System.Drawing.Size(138, 12);
            this.labelControl31.TabIndex = 67;
            this.labelControl31.Text = "Progress(183)->PRACK ≥";
            // 
            // numSiteP2PMo
            // 
            this.numSiteP2PMo.EditValue = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.numSiteP2PMo.Location = new System.Drawing.Point(219, 89);
            this.numSiteP2PMo.Name = "numSiteP2PMo";
            this.numSiteP2PMo.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numSiteP2PMo.Properties.Appearance.Options.UseFont = true;
            this.numSiteP2PMo.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numSiteP2PMo.Properties.IsFloatValue = false;
            this.numSiteP2PMo.Properties.Mask.EditMask = "N00";
            this.numSiteP2PMo.Properties.MaxValue = new decimal(new int[] {
            5000,
            0,
            0,
            0});
            this.numSiteP2PMo.Properties.MinValue = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numSiteP2PMo.Size = new System.Drawing.Size(81, 20);
            this.numSiteP2PMo.TabIndex = 65;
            // 
            // labelControl32
            // 
            this.labelControl32.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl32.Appearance.Options.UseFont = true;
            this.labelControl32.Location = new System.Drawing.Point(311, 92);
            this.labelControl32.Name = "labelControl32";
            this.labelControl32.Size = new System.Drawing.Size(24, 12);
            this.labelControl32.TabIndex = 68;
            this.labelControl32.Text = "毫秒";
            // 
            // labelControl33
            // 
            this.labelControl33.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl33.Appearance.Options.UseFont = true;
            this.labelControl33.Location = new System.Drawing.Point(51, 63);
            this.labelControl33.Name = "labelControl33";
            this.labelControl33.Size = new System.Drawing.Size(156, 12);
            this.labelControl33.TabIndex = 63;
            this.labelControl33.Text = "Try(100)->Progress(183) ≥";
            // 
            // numSiteT2PMo
            // 
            this.numSiteT2PMo.EditValue = new decimal(new int[] {
            1800,
            0,
            0,
            0});
            this.numSiteT2PMo.Location = new System.Drawing.Point(219, 59);
            this.numSiteT2PMo.Name = "numSiteT2PMo";
            this.numSiteT2PMo.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numSiteT2PMo.Properties.Appearance.Options.UseFont = true;
            this.numSiteT2PMo.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numSiteT2PMo.Properties.IsFloatValue = false;
            this.numSiteT2PMo.Properties.Mask.EditMask = "N00";
            this.numSiteT2PMo.Properties.MaxValue = new decimal(new int[] {
            5000,
            0,
            0,
            0});
            this.numSiteT2PMo.Properties.MinValue = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numSiteT2PMo.Size = new System.Drawing.Size(81, 20);
            this.numSiteT2PMo.TabIndex = 60;
            // 
            // labelControl34
            // 
            this.labelControl34.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl34.Appearance.Options.UseFont = true;
            this.labelControl34.Location = new System.Drawing.Point(310, 62);
            this.labelControl34.Name = "labelControl34";
            this.labelControl34.Size = new System.Drawing.Size(24, 12);
            this.labelControl34.TabIndex = 64;
            this.labelControl34.Text = "毫秒";
            // 
            // labelControl35
            // 
            this.labelControl35.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl35.Appearance.Options.UseFont = true;
            this.labelControl35.Location = new System.Drawing.Point(94, 36);
            this.labelControl35.Name = "labelControl35";
            this.labelControl35.Size = new System.Drawing.Size(114, 12);
            this.labelControl35.TabIndex = 61;
            this.labelControl35.Text = "Invite->Try(100) ≥";
            // 
            // numSiteI2TMo
            // 
            this.numSiteI2TMo.EditValue = new decimal(new int[] {
            600,
            0,
            0,
            0});
            this.numSiteI2TMo.Location = new System.Drawing.Point(219, 32);
            this.numSiteI2TMo.Name = "numSiteI2TMo";
            this.numSiteI2TMo.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numSiteI2TMo.Properties.Appearance.Options.UseFont = true;
            this.numSiteI2TMo.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numSiteI2TMo.Properties.IsFloatValue = false;
            this.numSiteI2TMo.Properties.Mask.EditMask = "N00";
            this.numSiteI2TMo.Properties.MaxValue = new decimal(new int[] {
            5000,
            0,
            0,
            0});
            this.numSiteI2TMo.Properties.MinValue = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numSiteI2TMo.Size = new System.Drawing.Size(81, 20);
            this.numSiteI2TMo.TabIndex = 59;
            // 
            // labelControl36
            // 
            this.labelControl36.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl36.Appearance.Options.UseFont = true;
            this.labelControl36.Location = new System.Drawing.Point(311, 35);
            this.labelControl36.Name = "labelControl36";
            this.labelControl36.Size = new System.Drawing.Size(24, 12);
            this.labelControl36.TabIndex = 62;
            this.labelControl36.Text = "毫秒";
            // 
            // groupControl1
            // 
            this.groupControl1.Controls.Add(this.labelControl3);
            this.groupControl1.Controls.Add(this.numSiteFind);
            this.groupControl1.Controls.Add(this.labelControl2);
            this.groupControl1.Controls.Add(this.labelControl23);
            this.groupControl1.Controls.Add(this.labelControl31);
            this.groupControl1.Controls.Add(this.numSiteU2RMo);
            this.groupControl1.Controls.Add(this.labelControl36);
            this.groupControl1.Controls.Add(this.labelControl24);
            this.groupControl1.Controls.Add(this.numSiteI2TMo);
            this.groupControl1.Controls.Add(this.labelControl25);
            this.groupControl1.Controls.Add(this.labelControl35);
            this.groupControl1.Controls.Add(this.numSiteU2UMo);
            this.groupControl1.Controls.Add(this.labelControl34);
            this.groupControl1.Controls.Add(this.labelControl26);
            this.groupControl1.Controls.Add(this.numSiteT2PMo);
            this.groupControl1.Controls.Add(this.labelControl27);
            this.groupControl1.Controls.Add(this.labelControl33);
            this.groupControl1.Controls.Add(this.numSiteP2UMo);
            this.groupControl1.Controls.Add(this.labelControl32);
            this.groupControl1.Controls.Add(this.labelControl28);
            this.groupControl1.Controls.Add(this.numSiteP2PMo);
            this.groupControl1.Controls.Add(this.labelControl29);
            this.groupControl1.Controls.Add(this.labelControl30);
            this.groupControl1.Controls.Add(this.numSiteP2POKMo);
            this.groupControl1.Location = new System.Drawing.Point(24, 12);
            this.groupControl1.Name = "groupControl1";
            this.groupControl1.Size = new System.Drawing.Size(354, 256);
            this.groupControl1.TabIndex = 73;
            this.groupControl1.Text = "主叫告警条件设置";
            // 
            // groupControl2
            // 
            this.groupControl2.Controls.Add(this.labelControl13);
            this.groupControl2.Controls.Add(this.numSiteP2iMt);
            this.groupControl2.Controls.Add(this.labelControl14);
            this.groupControl2.Controls.Add(this.labelControl15);
            this.groupControl2.Controls.Add(this.numSiteU2RMt);
            this.groupControl2.Controls.Add(this.labelControl16);
            this.groupControl2.Controls.Add(this.labelControl17);
            this.groupControl2.Controls.Add(this.numSiteU2UMt);
            this.groupControl2.Controls.Add(this.labelControl6);
            this.groupControl2.Controls.Add(this.labelControl18);
            this.groupControl2.Controls.Add(this.numSiteI2TMt);
            this.groupControl2.Controls.Add(this.labelControl19);
            this.groupControl2.Controls.Add(this.labelControl5);
            this.groupControl2.Controls.Add(this.numSiteP2UMt);
            this.groupControl2.Controls.Add(this.labelControl4);
            this.groupControl2.Controls.Add(this.labelControl20);
            this.groupControl2.Controls.Add(this.numSiteT2PMt);
            this.groupControl2.Controls.Add(this.labelControl7);
            this.groupControl2.Controls.Add(this.labelControl1);
            this.groupControl2.Controls.Add(this.numSiteP2POKMt);
            this.groupControl2.Controls.Add(this.labelControl12);
            this.groupControl2.Controls.Add(this.labelControl8);
            this.groupControl2.Controls.Add(this.numSiteP2PMt);
            this.groupControl2.Controls.Add(this.labelControl11);
            this.groupControl2.Location = new System.Drawing.Point(24, 274);
            this.groupControl2.Name = "groupControl2";
            this.groupControl2.Size = new System.Drawing.Size(354, 261);
            this.groupControl2.TabIndex = 74;
            this.groupControl2.Text = "被叫告警条件设置";
            // 
            // labelControl3
            // 
            this.labelControl3.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl3.Appearance.Options.UseFont = true;
            this.labelControl3.Location = new System.Drawing.Point(311, 230);
            this.labelControl3.Name = "labelControl3";
            this.labelControl3.Size = new System.Drawing.Size(24, 12);
            this.labelControl3.TabIndex = 83;
            this.labelControl3.Text = "毫秒";
            // 
            // numSiteFind
            // 
            this.numSiteFind.EditValue = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.numSiteFind.Location = new System.Drawing.Point(219, 227);
            this.numSiteFind.Name = "numSiteFind";
            this.numSiteFind.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numSiteFind.Properties.Appearance.Options.UseFont = true;
            this.numSiteFind.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numSiteFind.Properties.IsFloatValue = false;
            this.numSiteFind.Properties.Mask.EditMask = "N00";
            this.numSiteFind.Properties.MaxValue = new decimal(new int[] {
            2000,
            0,
            0,
            0});
            this.numSiteFind.Properties.MinValue = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numSiteFind.Size = new System.Drawing.Size(81, 20);
            this.numSiteFind.TabIndex = 81;
            // 
            // labelControl2
            // 
            this.labelControl2.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl2.Appearance.Options.UseFont = true;
            this.labelControl2.Location = new System.Drawing.Point(89, 230);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(120, 12);
            this.labelControl2.TabIndex = 82;
            this.labelControl2.Text = "MtInvite-MoInvite ≥";
            // 
            // DelayReasonSettingDlg
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(406, 576);
            this.Controls.Add(this.groupControl2);
            this.Controls.Add(this.groupControl1);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.Name = "DelayReasonSettingDlg";
            this.Text = "时延分析条件设置";
            ((System.ComponentModel.ISupportInitialize)(this.numSiteP2iMt.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSiteU2RMt.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSiteU2UMt.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSiteP2UMt.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSiteP2POKMt.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSiteP2PMt.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSiteT2PMt.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSiteI2TMt.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSiteU2RMo.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSiteU2UMo.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSiteP2UMo.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSiteP2POKMo.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSiteP2PMo.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSiteT2PMo.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSiteI2TMo.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            this.groupControl1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).EndInit();
            this.groupControl2.ResumeLayout(false);
            this.groupControl2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numSiteFind.Properties)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.SimpleButton btnCancel;
        private DevExpress.XtraEditors.SimpleButton btnOK;
        private DevExpress.XtraEditors.LabelControl labelControl7;
        private DevExpress.XtraEditors.SpinEdit numSiteP2POKMt;
        private DevExpress.XtraEditors.LabelControl labelControl8;
        private DevExpress.XtraEditors.LabelControl labelControl11;
        private DevExpress.XtraEditors.SpinEdit numSiteP2PMt;
        private DevExpress.XtraEditors.LabelControl labelControl12;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.SpinEdit numSiteT2PMt;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private DevExpress.XtraEditors.LabelControl labelControl5;
        private DevExpress.XtraEditors.SpinEdit numSiteI2TMt;
        private DevExpress.XtraEditors.LabelControl labelControl6;
        private DevExpress.XtraEditors.LabelControl labelControl13;
        private DevExpress.XtraEditors.SpinEdit numSiteP2iMt;
        private DevExpress.XtraEditors.LabelControl labelControl14;
        private DevExpress.XtraEditors.LabelControl labelControl15;
        private DevExpress.XtraEditors.SpinEdit numSiteU2RMt;
        private DevExpress.XtraEditors.LabelControl labelControl16;
        private DevExpress.XtraEditors.LabelControl labelControl17;
        private DevExpress.XtraEditors.SpinEdit numSiteU2UMt;
        private DevExpress.XtraEditors.LabelControl labelControl18;
        private DevExpress.XtraEditors.LabelControl labelControl19;
        private DevExpress.XtraEditors.SpinEdit numSiteP2UMt;
        private DevExpress.XtraEditors.LabelControl labelControl20;
        private DevExpress.XtraEditors.LabelControl labelControl23;
        private DevExpress.XtraEditors.SpinEdit numSiteU2RMo;
        private DevExpress.XtraEditors.LabelControl labelControl24;
        private DevExpress.XtraEditors.LabelControl labelControl25;
        private DevExpress.XtraEditors.SpinEdit numSiteU2UMo;
        private DevExpress.XtraEditors.LabelControl labelControl26;
        private DevExpress.XtraEditors.LabelControl labelControl27;
        private DevExpress.XtraEditors.SpinEdit numSiteP2UMo;
        private DevExpress.XtraEditors.LabelControl labelControl28;
        private DevExpress.XtraEditors.LabelControl labelControl29;
        private DevExpress.XtraEditors.SpinEdit numSiteP2POKMo;
        private DevExpress.XtraEditors.LabelControl labelControl30;
        private DevExpress.XtraEditors.LabelControl labelControl31;
        private DevExpress.XtraEditors.SpinEdit numSiteP2PMo;
        private DevExpress.XtraEditors.LabelControl labelControl32;
        private DevExpress.XtraEditors.LabelControl labelControl33;
        private DevExpress.XtraEditors.SpinEdit numSiteT2PMo;
        private DevExpress.XtraEditors.LabelControl labelControl34;
        private DevExpress.XtraEditors.LabelControl labelControl35;
        private DevExpress.XtraEditors.SpinEdit numSiteI2TMo;
        private DevExpress.XtraEditors.LabelControl labelControl36;
        private DevExpress.XtraEditors.GroupControl groupControl1;
        private DevExpress.XtraEditors.GroupControl groupControl2;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.SpinEdit numSiteFind;
        private DevExpress.XtraEditors.LabelControl labelControl2;
    }
}