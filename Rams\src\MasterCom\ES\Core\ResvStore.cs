﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Xml;
using MasterCom.Util;

namespace MasterCom.ES.Core
{
    public class ResvStore
    {
        readonly Dictionary<string, double> rsvValueDic = new Dictionary<string, double>();
        readonly Dictionary<string, string> rsvStringDic = new Dictionary<string, string>();

        public Dictionary<string, double> ResvValueDic
        {
            get 
            {
                return rsvValueDic;
            }
        }
        public Dictionary<string, string> ResvStringDic
        {
            get
            {
                return rsvStringDic;
            }
        }
        /// <summary>
        /// 预存池中是否存在，1 存在为double的，2 存在为string的 0 不存在
        /// </summary>
        /// <param name="name"></param>
        /// <returns></returns>
        public int ContainName(string name)
        {
            if(rsvValueDic.ContainsKey(name))
            {
                return 1;
            }
            if(rsvStringDic.ContainsKey(name))
            {
                return 2;
            }
            return 0;
        }
        public void SetResvValue(string name,double value)
        {
            rsvValueDic[name] = value;
        }
        public bool TryGetResvValue(string name, out double value)
        {
            return rsvValueDic.TryGetValue(name, out value);
        }
        public void SetResvValue(string name, string value)
        {
            rsvStringDic[name] = value;
        }
        public bool TryGetResvValue(string name, out string value)
        {
            return rsvStringDic.TryGetValue(name, out value);
        }
        internal void Reset()
        {
            List<string> dicList = new List<string>();
            foreach (string k in rsvValueDic.Keys)
            {
                dicList.Add(k);
            }
            rsvValueDic.Clear();
            foreach(string k in dicList)
            {
                rsvValueDic[k] = -99999;
            }

            dicList.Clear();
            foreach (string k in rsvStringDic.Keys)
            {
                dicList.Add(k);
            }
            rsvStringDic.Clear();
            foreach (string k in dicList)
            {
                rsvStringDic[k] = "";
            }
        }

        public static XmlElement AddItem(XmlConfigFile configFile, XmlElement config, string name, object value)
        {
            return null;
        }
        public static object GetItemValue(XmlConfigFile configFile, XmlElement item, string typeName)
        {
            return null;
        }
        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                List<object> paramList = new List<object>();
                param["resvList"] = paramList;
                foreach (string str in rsvValueDic.Keys)
                {
                    paramList.Add(str);
                }
                return param;
            }
            set
            {
                if (value == null || value.Count == 0)
                {
                    return;
                }
                rsvValueDic.Clear();
                Dictionary<string, object> pardic = value;
                foreach (string k in pardic.Keys)
                {
                    rsvValueDic[k] = (double)pardic[k];
                }
            }
        }
        public Dictionary<string, object> ParamStr
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                List<object> paramList = new List<object>();
                param["resvList"] = paramList;
                foreach (string str in rsvStringDic.Keys)
                {
                    paramList.Add(str);
                }
                return param;
            }
            set
            {
                if (value == null || value.Count == 0)
                {
                    return;
                }
                rsvStringDic.Clear();
                Dictionary<string, object> pardic = value;
                foreach (string k in pardic.Keys)
                {
                    rsvStringDic[k] = (string)pardic[k];
                }
            }
        }
    }
}
