﻿namespace MasterCom.RAMS.Model.Interface
{
    partial class SelectReportDlg
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.btnOK = new System.Windows.Forms.Button();
            this.btnCancel = new System.Windows.Forms.Button();
            this.cbxAllParam = new System.Windows.Forms.CheckBox();
            this.lbNoteAll = new System.Windows.Forms.Label();
            this.label1 = new System.Windows.Forms.Label();
            this.cbxAdvEvtFilter = new System.Windows.Forms.CheckBox();
            this.cbxEventFilterSelList = new System.Windows.Forms.ComboBox();
            this.lblNoteGridPercent = new System.Windows.Forms.Label();
            this.chkCaleGridPercent = new System.Windows.Forms.CheckBox();
            this.cbxMomt = new System.Windows.Forms.CheckBox();
            this.label2 = new System.Windows.Forms.Label();
            this.chkBlanceCale = new System.Windows.Forms.CheckBox();
            this.label3 = new System.Windows.Forms.Label();
            this.toolTip1 = new System.Windows.Forms.ToolTip(this.components);
            this.lnkReloadRpt = new System.Windows.Forms.LinkLabel();
            this.reportFilter = new MasterCom.RAMS.KPI_Statistics.ReportFilterControl();
            this.SuspendLayout();
            // 
            // btnOK
            // 
            this.btnOK.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnOK.Location = new System.Drawing.Point(241, 184);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(87, 27);
            this.btnOK.TabIndex = 0;
            this.btnOK.Text = "确定";
            this.btnOK.UseVisualStyleBackColor = true;
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // btnCancel
            // 
            this.btnCancel.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnCancel.Location = new System.Drawing.Point(348, 184);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(87, 27);
            this.btnCancel.TabIndex = 1;
            this.btnCancel.Text = "取消";
            this.btnCancel.UseVisualStyleBackColor = true;
            // 
            // cbxAllParam
            // 
            this.cbxAllParam.AutoSize = true;
            this.cbxAllParam.Location = new System.Drawing.Point(48, 54);
            this.cbxAllParam.Name = "cbxAllParam";
            this.cbxAllParam.Size = new System.Drawing.Size(72, 16);
            this.cbxAllParam.TabIndex = 3;
            this.cbxAllParam.Text = "所有指标";
            this.cbxAllParam.UseVisualStyleBackColor = true;
            this.cbxAllParam.CheckedChanged += new System.EventHandler(this.cbxAllParam_CheckedChanged);
            // 
            // lbNoteAll
            // 
            this.lbNoteAll.AutoSize = true;
            this.lbNoteAll.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(128)))));
            this.lbNoteAll.Location = new System.Drawing.Point(162, 55);
            this.lbNoteAll.Name = "lbNoteAll";
            this.lbNoteAll.Size = new System.Drawing.Size(221, 12);
            this.lbNoteAll.TabIndex = 4;
            this.lbNoteAll.Text = "（选择所有指标，查询速度会有所降低）";
            this.lbNoteAll.Visible = false;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.ForeColor = System.Drawing.Color.Black;
            this.label1.Location = new System.Drawing.Point(44, 10);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(137, 12);
            this.label1.TabIndex = 4;
            this.label1.Text = "请选择所要查询的报表：";
            // 
            // cbxAdvEvtFilter
            // 
            this.cbxAdvEvtFilter.AutoSize = true;
            this.cbxAdvEvtFilter.Location = new System.Drawing.Point(48, 100);
            this.cbxAdvEvtFilter.Name = "cbxAdvEvtFilter";
            this.cbxAdvEvtFilter.Size = new System.Drawing.Size(96, 16);
            this.cbxAdvEvtFilter.TabIndex = 3;
            this.cbxAdvEvtFilter.Text = "启用事件过滤";
            this.cbxAdvEvtFilter.UseVisualStyleBackColor = true;
            this.cbxAdvEvtFilter.CheckedChanged += new System.EventHandler(this.cbxAdvEvtFilter_CheckedChanged);
            // 
            // cbxEventFilterSelList
            // 
            this.cbxEventFilterSelList.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cbxEventFilterSelList.FormattingEnabled = true;
            this.cbxEventFilterSelList.Location = new System.Drawing.Point(165, 97);
            this.cbxEventFilterSelList.Name = "cbxEventFilterSelList";
            this.cbxEventFilterSelList.Size = new System.Drawing.Size(269, 22);
            this.cbxEventFilterSelList.TabIndex = 2;
            // 
            // lblNoteGridPercent
            // 
            this.lblNoteGridPercent.AutoSize = true;
            this.lblNoteGridPercent.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(128)))));
            this.lblNoteGridPercent.Location = new System.Drawing.Point(162, 78);
            this.lblNoteGridPercent.Name = "lblNoteGridPercent";
            this.lblNoteGridPercent.Size = new System.Drawing.Size(245, 12);
            this.lblNoteGridPercent.TabIndex = 6;
            this.lblNoteGridPercent.Text = "（选择栅格比例指标，查询速度会有所降低）";
            this.lblNoteGridPercent.Visible = false;
            // 
            // chkCaleGridPercent
            // 
            this.chkCaleGridPercent.AutoSize = true;
            this.chkCaleGridPercent.Location = new System.Drawing.Point(48, 77);
            this.chkCaleGridPercent.Name = "chkCaleGridPercent";
            this.chkCaleGridPercent.Size = new System.Drawing.Size(96, 16);
            this.chkCaleGridPercent.TabIndex = 5;
            this.chkCaleGridPercent.Text = "栅格比例指标";
            this.chkCaleGridPercent.UseVisualStyleBackColor = true;
            this.chkCaleGridPercent.CheckedChanged += new System.EventHandler(this.chkCaleGridPercent_CheckedChanged);
            // 
            // cbxMomt
            // 
            this.cbxMomt.AutoSize = true;
            this.cbxMomt.Location = new System.Drawing.Point(48, 124);
            this.cbxMomt.Name = "cbxMomt";
            this.cbxMomt.Size = new System.Drawing.Size(84, 16);
            this.cbxMomt.TabIndex = 7;
            this.cbxMomt.Text = "区分主被叫";
            this.cbxMomt.UseVisualStyleBackColor = true;
            this.cbxMomt.Visible = false;
            this.cbxMomt.CheckedChanged += new System.EventHandler(this.cbxMomt_CheckedChanged);
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(128)))));
            this.label2.Location = new System.Drawing.Point(163, 126);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(233, 12);
            this.label2.TabIndex = 8;
            this.label2.Text = "（选择区分主被叫，查询速度会有所降低）";
            this.label2.Visible = false;
            // 
            // chkBlanceCale
            // 
            this.chkBlanceCale.AutoSize = true;
            this.chkBlanceCale.Location = new System.Drawing.Point(48, 148);
            this.chkBlanceCale.Name = "chkBlanceCale";
            this.chkBlanceCale.Size = new System.Drawing.Size(96, 16);
            this.chkBlanceCale.TabIndex = 9;
            this.chkBlanceCale.Text = "匀化模式计算";
            this.chkBlanceCale.UseVisualStyleBackColor = true;
            this.chkBlanceCale.CheckedChanged += new System.EventHandler(this.chkBlanceCale_CheckedChanged);
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(128)))));
            this.label3.Location = new System.Drawing.Point(163, 149);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(221, 12);
            this.label3.TabIndex = 10;
            this.label3.Text = "（选择匀化模式，查询速度会有所降低）";
            this.label3.Visible = false;
            // 
            // toolTip1
            // 
            this.toolTip1.AutoPopDelay = 5000000;
            this.toolTip1.InitialDelay = 500;
            this.toolTip1.IsBalloon = true;
            this.toolTip1.ReshowDelay = 100;
            // 
            // lnkReloadRpt
            // 
            this.lnkReloadRpt.AutoSize = true;
            this.lnkReloadRpt.Location = new System.Drawing.Point(345, 10);
            this.lnkReloadRpt.Name = "lnkReloadRpt";
            this.lnkReloadRpt.Size = new System.Drawing.Size(77, 12);
            this.lnkReloadRpt.TabIndex = 11;
            this.lnkReloadRpt.TabStop = true;
            this.lnkReloadRpt.Text = "重新加载报表";
            this.lnkReloadRpt.LinkClicked += new System.Windows.Forms.LinkLabelLinkClickedEventHandler(this.lnkReloadRpt_LinkClicked);
            // 
            // reportFilter
            // 
            this.reportFilter.Location = new System.Drawing.Point(46, 26);
            this.reportFilter.Name = "reportFilter";
            this.reportFilter.Size = new System.Drawing.Size(380, 24);
            this.reportFilter.TabIndex = 12;
            // 
            // SelectReportDlg
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(482, 225);
            this.Controls.Add(this.reportFilter);
            this.Controls.Add(this.lnkReloadRpt);
            this.Controls.Add(this.label3);
            this.Controls.Add(this.chkBlanceCale);
            this.Controls.Add(this.label2);
            this.Controls.Add(this.cbxMomt);
            this.Controls.Add(this.lblNoteGridPercent);
            this.Controls.Add(this.chkCaleGridPercent);
            this.Controls.Add(this.label1);
            this.Controls.Add(this.lbNoteAll);
            this.Controls.Add(this.cbxAdvEvtFilter);
            this.Controls.Add(this.cbxAllParam);
            this.Controls.Add(this.cbxEventFilterSelList);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedToolWindow;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "SelectReportDlg";
            this.Text = "选择查询报表";
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.Button btnOK;
        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.CheckBox cbxAllParam;
        private System.Windows.Forms.Label lbNoteAll;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.CheckBox cbxAdvEvtFilter;
        private System.Windows.Forms.ComboBox cbxEventFilterSelList;
        private System.Windows.Forms.Label lblNoteGridPercent;
        private System.Windows.Forms.CheckBox chkCaleGridPercent;
        private System.Windows.Forms.CheckBox cbxMomt;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.CheckBox chkBlanceCale;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.ToolTip toolTip1;
        private System.Windows.Forms.LinkLabel lnkReloadRpt;
        private MasterCom.RAMS.KPI_Statistics.ReportFilterControl reportFilter;
    }
}