﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;
using MasterCom.Util.UiEx;

namespace MasterCom.RAMS.Net
{
    public class QueryLTECellNeighbourInfo : DIYSQLBase
    {
        public QueryLTECellNeighbourInfo(MainModel mainModel, int lteCellID) : base(mainModel)
        {
            this.lteCellID = lteCellID;
        }

        protected override string getSqlTextString()
        {
#if LT
            return @"SELECT [icellid],[inbcellid],[itype],[istime],[ietime] FROM [dbo].[tb_culte_cfg_nbcell] where icellid = " + lteCellID;
#else 
            return @"SELECT [icellid],[inbcellid],[itype],[istime],[ietime] FROM [dbo].[tb_tdlte_cfg_nbcell] where icellid = " + lteCellID;
#endif
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[5];
            rType[0] = E_VType.E_Int;
            rType[1] = E_VType.E_Int;
            rType[2] = E_VType.E_Int;
            rType[3] = E_VType.E_Int;
            rType[4] = E_VType.E_Int;
            return rType;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    fillData(package);
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }

        private void fillData(Package package)
        {
            package.Content.GetParamInt();//tdcellID
            int nbCellID = package.Content.GetParamInt();
            int nbCellType = package.Content.GetParamInt();
            int beginTime = package.Content.GetParamInt();
            int endTime = package.Content.GetParamInt();
            TimePeriod validPeriod = new TimePeriod(beginTime == 0 ? DateTime.MinValue : JavaDate.GetDateTimeFromMilliseconds(beginTime * 1000L), endTime == 0x7fffffff ? DateTime.MaxValue : JavaDate.GetDateTimeFromMilliseconds(endTime * 1000L));
            List<LTECell> cells = MainModel.CellManager.GetLTECells(lteCellID, validPeriod);
            List<Cell> nbCells = new List<Cell>();
            List<TDCell> nbTDCells = new List<TDCell>();
            List<WCell> nbWCells = new List<WCell>();
            List<LTECell> nbLTECells = new List<LTECell>();
            if (nbCellType == 0)
            {//gsm
                nbCells = MainModel.CellManager.GetCells(nbCellID, validPeriod);
            }
            else if (nbCellType == 1)
            {//td
                nbTDCells = MainModel.CellManager.GetTDCells(nbCellID, validPeriod);
                nbWCells = MainModel.CellManager.GetWCells(nbCellID, validPeriod);
            }
            else if (nbCellType == 2)
            {//lte
                nbLTECells = MainModel.CellManager.GetLTECells(nbCellID, validPeriod);
            }

            foreach (LTECell cell in cells)
            {
                foreach (Cell nbCell in nbCells)
                {
                    cell.AddNeighbourCell(nbCell);
                }
                foreach (TDCell nbCell in nbTDCells)
                {
                    cell.AddNeighbourCell(nbCell);
                }
                foreach (WCell nbCell in nbWCells)
                {
                    cell.AddNeighbourCell(nbCell);
                }
                foreach (LTECell nbCell in nbLTECells)
                {
                    cell.AddNeighbourCell(nbCell);

                }
            }
        }

        private readonly int lteCellID;
    }

    public class DIYSQLLTENBCellInfo : DIYSQLBase
    {
        public DIYSQLLTENBCellInfo()
            : base(MainModel.GetInstance())
        {
        }

        protected override string getSqlTextString()
        {
#if LT
            return @"SELECT [icellid],[inbcellid],[itype],[istime],[ietime] FROM [dbo].[tb_culte_cfg_nbcell]";
#else
            return @"SELECT [icellid],[inbcellid],[itype],[istime],[ietime] FROM [dbo].[tb_tdlte_cfg_nbcell]";
#endif
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[5];
            rType[0] = E_VType.E_Int;
            rType[1] = E_VType.E_Int;
            rType[2] = E_VType.E_Int;
            rType[3] = E_VType.E_Int;
            rType[4] = E_VType.E_Int;
            return rType;
        }

        protected override void query()
        {
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
                WaitTextBox.Show("正在获取LTE邻区信息...", queryInThread, clientProxy);
            }
            finally
            {
                clientProxy.Close();
            }
        }

        protected override void queryInThread(object o)
        {
            try
            {
                base.queryInThread(o);
            }
            catch
            {
                //continue
            }
            finally
            {
                WaitTextBox.Close();
            }
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    fillData(package);
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }

        private void fillData(Package package)
        {
            int cellID = package.Content.GetParamInt();
            int nbCellID = package.Content.GetParamInt();
            int nbCellType = package.Content.GetParamInt();
            int beginTime = package.Content.GetParamInt();
            int endTime = package.Content.GetParamInt();
            TimePeriod validPeriod = new TimePeriod(beginTime == 0 ? DateTime.MinValue : JavaDate.GetDateTimeFromMilliseconds(beginTime * 1000L), endTime == 0x7fffffff ? DateTime.MaxValue : JavaDate.GetDateTimeFromMilliseconds(endTime * 1000L));
            List<LTECell> cells = MainModel.CellManager.GetLTECells(cellID, validPeriod);
            List<Cell> nbCells = new List<Cell>();
            List<TDCell> nbTDCells = new List<TDCell>();
            List<LTECell> nbLTECells = new List<LTECell>();
            List<WCell> nbWCells = new List<WCell>();
            if (nbCellType == 0)
            {//gsm
                nbCells = MainModel.CellManager.GetCells(nbCellID, validPeriod);
            }
            else if (nbCellType == 1)
            {//td
                nbTDCells = MainModel.CellManager.GetTDCells(nbCellID, validPeriod);
                nbWCells = MainModel.CellManager.GetWCells(nbCellID, validPeriod);
            }
            else if (nbCellType == 2)
            {//lte
                nbLTECells = MainModel.CellManager.GetLTECells(nbCellID, validPeriod);
            }
            foreach (LTECell cell in cells)
            {
                addNeighbourCell(nbCells, cell);
                addTDNeighbourCell(nbTDCells, cell);
                addWNeighbourCell(nbWCells, cell);
                addLTENeighbourCell(nbLTECells, cell);
            }
        }

        private void addNeighbourCell(List<Cell> nbCells, LTECell cell)
        {
            foreach (Cell nbCell in nbCells)
            {
                if (cell.ValidPeriod.IsIntersect(nbCell.ValidPeriod))
                {
                    cell.AddNeighbourCell(nbCell);
                }
            }
        }

        private void addTDNeighbourCell(List<TDCell> nbTDCells, LTECell cell)
        {
            foreach (TDCell nbCell in nbTDCells)
            {
                if (cell.ValidPeriod.IsIntersect(nbCell.ValidPeriod))
                {
                    cell.AddNeighbourCell(nbCell);
                }
            }
        }

        private void addWNeighbourCell(List<WCell> nbWCells, LTECell cell)
        {
            foreach (WCell nbCell in nbWCells)
            {
                if (cell.ValidPeriod.IsIntersect(nbCell.ValidPeriod))
                {
                    cell.AddNeighbourCell(nbCell);
                }
            }
        }

        private void addLTENeighbourCell(List<LTECell> nbLTECells, LTECell cell)
        {
            foreach (LTECell nbCell in nbLTECells)
            {
                if (cell.ValidPeriod.IsIntersect(nbCell.ValidPeriod))
                {
                    cell.AddNeighbourCell(nbCell);
                }
            }
        }



        public override string Name
        {
            get { return "获取LTE邻区信息"; }
        }
    }
}
