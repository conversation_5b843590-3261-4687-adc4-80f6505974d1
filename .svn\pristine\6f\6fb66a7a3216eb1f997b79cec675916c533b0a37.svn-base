﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.Util;


namespace MasterCom.RAMS.ZTFunc
{
    public class VoNRMosParam
    {
        public VoNRMosParam(DateTime startTime)
        {
            ownParam = new VoNRParam(startTime);
            otherParam = new VoNRParam(startTime);
        }

        public string strFileName;
        public int SN;
        public float MOS;
        public float? PESQLQ;
        public float? PESQMOS;

        public short? PDCCH_UL_Grant_Count;
        public short? PDCCH_DL_Grant_Count;
        public float? MCSCode0_DL;
        public float? MCSCode1_DL;
        public float? MCS_UL;
        public float? PDSCH_Code0_BLER;
        public float? PDSCH_Code1_BLER;
        public float? PUSCH_BLER;
        public short? PDCCH_CCE_Start;
        public short? PDCCH_CCEs_Number;

        public double MOSContinuousDistance;

        public double longitude;
        public double latitude;
        public DateTime dtStartTime;
        public DateTime dtEndTime;

        public DateTime dtStartHandSetTime;
        public DateTime dtEndHandSetTime;

        public VoNRParam ownParam;
        public VoNRParam otherParam;

        public int PackageLostNum = 0;
        public int PackageNum = 0;

        /// <summary>
        /// 总切换次数
        /// </summary>
        public int AllHORequestCount
        {
            get
            {
                return ownParam.HORequest + otherParam.HORequest;
            }
        }

        public int SameBtsSameEarfcnHOCount
        {
            get
            {
                return ownParam.SameBtsSameEarfcnHOCount + otherParam.SameBtsSameEarfcnHOCount;
            }
        }
        public int SameBtsDiffEarfcnHOCount
        {
            get
            {
                return ownParam.SameBtsDiffEarfcnHOCount + otherParam.SameBtsDiffEarfcnHOCount;
            }
        }
        public int DiffBtsSameEarfcnHOCount
        {
            get
            {
                return ownParam.DiffBtsSameEarfcnHOCount + otherParam.DiffBtsSameEarfcnHOCount;
            }
        }
        public int DiffBtsDiffEarfcnHOCount
        {
            get
            {
                return ownParam.DiffBtsDiffEarfcnHOCount + otherParam.DiffBtsDiffEarfcnHOCount;
            }
        }

        public double AllJitterAvg
        {
            get
            {
                if ((ownParam.JitterTpCount + otherParam.JitterTpCount) <= 0)
                {
                    return 0;
                }
                return Math.Round((double)(ownParam.JitterTotal + otherParam.JitterTotal) / (ownParam.JitterTpCount + otherParam.JitterTpCount), 2);
            }
        }

        public string GridDesc
        {
            get
            {
                return GISManager.GetInstance().GetGridDesc(longitude, latitude);
            }
        }

        private double? rtpLossRate;
        public string RTPLossRate
        {
            get
            {
                if (rtpLossRate == null)
                {
                    return "-";
                }
                else
                {
                    return rtpLossRate.ToString();
                }
            }
        }

        public void FillRTPInfo(List<TestPoint> tpList)
        {
            int? preLossNum = null, aftLossNum = null, prePacketsNum = null, aftPacketsNum = null;
            int? ssrc = null;
            rtpLossRate = null;
            MOSContinuousDistance = 0;
            TestPoint prevTp = null;
            for (int i = 0; i < tpList.Count; i++)
            {
                //添加路程距离
                TestPoint tp = tpList[i];
                if (prevTp != null)
                {
                    MOSContinuousDistance += prevTp.Distance2(tpList[i]);
                }
                prevTp = tpList[i];

                int? lossNum, packetsNum;
                lossNum = (int?)tp["NR_VONR_RTP_Packets_Lost_Num"];
                packetsNum = (int?)tp["NR_VONR_RTP_Sequence_Number"];
                if (ssrc == null)
                {
                    //NR_VONR_RTP_Source_SSRC为标识,判断是否为同一次通话,同一次通话才记录丢包和总包数来计算丢包率
                    ssrc = (int?)tp["NR_VONR_RTP_Source_SSRC"];
                }
                else
                {
                    int? curSsrc = (int?)tp["NR_VONR_RTP_Source_SSRC"];
                    if (curSsrc == null || ssrc != curSsrc)
                    {
                        //不为同一次通话时,丢包数和总包数会清0,导致丢包率计算错误,故舍弃
                        return;
                    }
                }

                if (lossNum != null)
                {
                    if (preLossNum == null)
                    {
                        preLossNum = lossNum;
                    }
                    aftLossNum = lossNum;
                }

                if (packetsNum != null)
                {
                    if (prePacketsNum == null)
                    {
                        prePacketsNum = packetsNum;
                    }
                    aftPacketsNum = packetsNum;
                }
            }

            if (preLossNum != null && prePacketsNum != null && aftLossNum != null && aftPacketsNum != null)
            {
                int mosLossNum = (int)aftLossNum - (int)preLossNum;
                int mosPacketsNum = (int)aftPacketsNum - (int)prePacketsNum;

                PackageLostNum = mosLossNum;
                PackageNum = mosPacketsNum;
                if (mosLossNum >= 0 && mosPacketsNum > 0)
                {
                    rtpLossRate = Math.Round((double)PackageLostNum * 100 / PackageNum, 3);
                }
            }
        }


        public class VoNRParam : NRSumInfo
        {
            public VoNRParam(DateTime startTme)
            {
                StartTime = startTme;
                TpCount = 0;
            }
            public int TpCount { get; set; }

            #region 上行
            public int UlTimesSum
            {
                get
                {
                    return QAM16UlTimesTotal + QAM64UlTimesTotal + QPSKUlTimesTotal;
                }
            }
            public int QAM16UlTimesTotal = 0;
            public double QAM16UlTimesAvg
            {
                get
                {
                    if (TpCount > 0)
                    {
                        return Math.Round((double)QAM16UlTimesTotal / TpCount, 2);
                    }
                    return 0.0;
                }
            }
            public double QAM16UlPerAvg
            {
                get
                {
                    if (UlTimesSum == 0)
                    {
                        return 0.00;
                    }
                    else
                    {
                        return Math.Round((double)100 * QAM16UlTimesTotal / UlTimesSum, 2);
                    }
                }
            }


            public int QAM64UlTimesTotal = 0;
            public double QAM64UlTimesAvg
            {
                get
                {
                    if (TpCount > 0)
                    {
                        return Math.Round((double)QAM64UlTimesTotal / TpCount, 2);
                    }
                    return 0.0;
                }
            }
            public double QAM64UlPerAvg
            {
                get
                {
                    if (UlTimesSum == 0)
                    {
                        return 0.00;
                    }
                    else
                    {
                        return Math.Round((double)100 * QAM64UlTimesTotal / UlTimesSum, 2);
                    }
                }
            }


            public int QPSKUlTimesTotal = 0;
            public double QPSKUlTimesAvg
            {
                get
                {
                    if (TpCount > 0)
                    {
                        return Math.Round((double)QPSKUlTimesTotal / TpCount, 2);
                    }
                    return 0.0;
                }
            }
            public double QPSKUlPerAvg
            {
                get
                {
                    if (UlTimesSum == 0)
                    {
                        return 0.00;
                    }
                    else
                    {
                        return Math.Round((double)100 * QPSKUlTimesTotal / UlTimesSum, 2);
                    }
                }
            }

            protected List<int> mcs_UlList = new List<int>();
            protected int mcs_UlSum = 0;
            public string StrMCSUl
            {
                get
                {
                    return gatherIntListToString(mcs_UlList);
                }
            }
            public double? MCSUlAvg
            {
                get
                {
                    double? avg = null;
                    avg = Math.Round(mcs_UlSum / (double)mcs_UlList.Count, 2);
                    return avg;
                }
            }

            protected List<int> ulPRbNumsList = new List<int>();
            protected int ulPRbNumsSum = 0;
            public string StrUlPRbNums
            {
                get
                {
                    return gatherIntListToString(ulPRbNumsList);
                }
            }
            public double? UlPRbNumsAvg
            {
                get
                {
                    double? avg = null;
                    avg = Math.Round(ulPRbNumsSum / (double)ulPRbNumsList.Count, 2);
                    return avg;
                }
            }

            #endregion

            #region 下行码0

            public int DlCode0TimesSum
            {
                get
                {
                    return QAM16DlCode0TimesTotal + QAM64DlCode0TimesTotal + QPSKDlCode0TimesTotal;
                }
            }

            public int QAM16DlCode0TimesTotal = 0;
            public double QAM16DlCode0TimesAvg
            {
                get
                {
                    if (TpCount > 0)
                    {
                        return Math.Round((double)QAM16DlCode0TimesTotal / TpCount, 2);
                    }
                    return 0.0;
                }
            }
            public double QAM16DlCode0PerAvg
            {
                get
                {
                    if (DlCode0TimesSum == 0)
                    {
                        return 0.00;
                    }
                    else
                    {
                        return Math.Round((double)100 * QAM16DlCode0TimesTotal / DlCode0TimesSum, 2);
                    }
                }
            }


            public int QAM64DlCode0TimesTotal = 0;
            public double QAM64DlCode0TimesAvg
            {
                get
                {
                    if (TpCount > 0)
                    {
                        return Math.Round((double)QAM64DlCode0TimesTotal / TpCount, 2);
                    }
                    return 0.0;
                }
            }
            public double QAM64DlCode0PerAvg
            {
                get
                {
                    if (DlCode0TimesSum == 0)
                    {
                        return 0.00;
                    }
                    else
                    {
                        return Math.Round((double)100 * QAM64DlCode0TimesTotal / DlCode0TimesSum, 2);
                    }
                }
            }


            public int QPSKDlCode0TimesTotal = 0;
            public double QPSKDlCode0TimesAvg
            {
                get
                {
                    if (TpCount > 0)
                    {
                        return Math.Round((double)QPSKDlCode0TimesTotal / TpCount, 2);
                    }
                    return 0.0;
                }
            }
            public double QPSKDlCode0PerAvg
            {
                get
                {
                    if (DlCode0TimesSum == 0)
                    {
                        return 0.00;
                    }
                    else
                    {
                        return Math.Round((double)100 * QPSKDlCode0TimesTotal / DlCode0TimesSum, 2);
                    }
                }
            }
            protected int mcsDlCode0Sum = 0;
            protected List<int> mcsDlCode0List = new List<int>();
            public string StrMCSDlCode0
            {
                get
                {
                    return gatherIntListToString(mcsDlCode0List);
                }
            }
            #endregion

            #region 下行码1
            public int DlCode1TimesSum
            {
                get
                {
                    return QAM16DlCode1TimesTotal + QAM64DlCode1TimesTotal + QPSKDlCode1TimesTotal;
                }
            }

            public int QAM16DlCode1TimesTotal = 0;
            public double QAM16DlCode1TimesAvg
            {
                get
                {
                    if (TpCount > 0)
                    {
                        return Math.Round((double)QAM16DlCode1TimesTotal / TpCount, 2);
                    }
                    return 0.0;
                }
            }
            public double QAM16DlCode1PerAvg
            {
                get
                {
                    if (DlCode1TimesSum == 0)
                    {
                        return 0.00;
                    }
                    else
                    {
                        return Math.Round((double)100 * QAM16DlCode1TimesTotal / DlCode1TimesSum, 2);
                    }
                }
            }


            public int QAM64DlCode1TimesTotal = 0;
            public double QAM64DlCode1TimesAvg
            {
                get
                {
                    if (TpCount > 0)
                    {
                        return Math.Round((double)QAM64DlCode1TimesTotal / TpCount, 2);
                    }
                    return 0.0;
                }
            }
            public double QAM64DlCode1PerAvg
            {
                get
                {
                    if (DlCode1TimesSum == 0)
                    {
                        return 0.00;
                    }
                    else
                    {
                        return Math.Round((double)100 * QAM64DlCode1TimesTotal / DlCode1TimesSum, 2);
                    }
                }
            }


            public int QPSKDlCode1TimesTotal = 0;
            public double QPSKDlCode1TimesAvg
            {
                get
                {
                    if (TpCount > 0)
                    {
                        return Math.Round((double)QPSKDlCode1TimesTotal / TpCount, 2);
                    }
                    return 0.0;
                }
            }
            public double QPSKDlCode1PerAvg
            {
                get
                {
                    if (DlCode1TimesSum == 0)
                    {
                        return 0.00;
                    }
                    else
                    {
                        return Math.Round((double)100 * QPSKDlCode1TimesTotal / DlCode1TimesSum, 2);
                    }
                }
            }
            protected int mcsDlCode1Sum = 0;
            protected List<int> mcsDlCode1List = new List<int>();
            public string StrMCSDlCode1
            {
                get
                {
                    return gatherIntListToString(mcsDlCode1List);
                }
            }
            #endregion

            #region 下行（汇总）
            public int DlTimesSum
            {
                get
                {
                    return QAM16DlTimesTotal + QAM64DlTimesTotal + QPSKDlTimesTotal;
                }
            }

            public int QAM16DlTimesTotal
            {
                get
                {
                    return QAM16DlCode0TimesTotal + QAM16DlCode1TimesTotal;
                }
            }
            public double QAM16DlTimesAvg
            {
                get
                {
                    if (TpCount > 0)
                    {
                        return Math.Round((double)QAM16DlTimesTotal / TpCount, 2);
                    }
                    return 0.0;
                }
            }
            public double QAM16DlPerAvg
            {
                get
                {
                    if (DlTimesSum == 0)
                    {
                        return 0.00;
                    }
                    else
                    {
                        return Math.Round((double)100 * QAM16DlTimesTotal / DlTimesSum, 2);
                    }
                }
            }


            public int QAM64DlTimesTotal
            {
                get
                {
                    return QAM64DlCode0TimesTotal + QAM64DlCode1TimesTotal;
                }
            }
            public double QAM64DlTimesAvg
            {
                get
                {
                    if (TpCount > 0)
                    {
                        return Math.Round((double)QAM64DlTimesTotal / TpCount, 2);
                    }
                    return 0.0;
                }
            }
            public double QAM64DlPerAvg
            {
                get
                {
                    if (DlTimesSum == 0)
                    {
                        return 0.00;
                    }
                    else
                    {
                        return Math.Round((double)100 * QAM64DlTimesTotal / DlTimesSum, 2);
                    }
                }
            }


            public int QPSKDlTimesTotal
            {
                get
                {
                    return QPSKDlCode0TimesTotal + QPSKDlCode1TimesTotal;
                }
            }
            public double QPSKDlTimesAvg
            {
                get
                {
                    if (TpCount > 0)
                    {
                        return Math.Round((double)QPSKDlTimesTotal / TpCount, 2);
                    }
                    return 0.0;
                }
            }
            public double QPSKDlPerAvg
            {
                get
                {
                    if (DlTimesSum == 0)
                    {
                        return 0.00;
                    }
                    else
                    {
                        return Math.Round((double)100 * QPSKDlTimesTotal / DlTimesSum, 2);
                    }
                }
            }

            public double? MCSDlAvg
            {
                get
                {
                    double? avg = null;
                    int mcsDlSum = mcsDlCode0Sum + mcsDlCode1Sum;
                    int mcsDlCount = mcsDlCode0List.Count + mcsDlCode1List.Count;
                    avg = Math.Round(mcsDlSum / (double)mcsDlCount, 2);
                    return avg;
                }
            }

            protected List<int> dtxList = new List<int>();
            public string StrDtx
            {
                get
                {
                    return gatherIntListToString(dtxList);
                }
            }
            protected List<short> rankIndicatorList = new List<short>();
            public string StrRank
            {
                get
                {
                    return gatherShortListToString(rankIndicatorList);
                }
            }
            protected int? dlFrequency = null;
            public int? DlFrequency
            {
                get
                {
                    return dlFrequency;
                }
            }
            protected int dlPRbNumsSum = 0;
            protected List<int> dlPRbNumsList = new List<int>();
            public string StrDlPRbNums
            {
                get
                {
                    return gatherIntListToString(dlPRbNumsList);
                }
            }
            public double? DlPRbNumsAvg
            {
                get
                {
                    double? avg = null;
                    avg = Math.Round(dlPRbNumsSum / (double)dlPRbNumsList.Count, 2);
                    return avg;
                }
            }
            #endregion


            public Dictionary<string, int> dicLacCi = new Dictionary<string, int>();

            public List<Event> lstHOEvent = new List<Event>();
            public List<double> lstHODelayMedia = new List<double>();

            public List<string> lstHOCell = new List<string>();

            public List<string> lstBSC = new List<string>();

            public int Band_CM41 = 0;
            public int Band_CM79 = 0;
            public int Band_CU78 = 0;
            public int Band_CT78 = 0;

            public int txPowerACount = 0;
            public int txPowerAFull = 0;

            public int txPowerDCount = 0;
            public int txPowerDFull = 0;

            public int txPowerECount = 0;
            public int txPowerEFull = 0;

            public int txPowerFCount = 0;
            public int txPowerFFull = 0;

            public int HORequest = 0;
            public int HOSucc = 0;
            public int HOFail = 0;
            public int RRCReEstablish = 0;
            public DateTime StartTime;

            public int SameBtsSameEarfcnHOCount = 0;
            public int SameBtsDiffEarfcnHOCount = 0;
            public int DiffBtsSameEarfcnHOCount = 0;
            public int DiffBtsDiffEarfcnHOCount = 0;

            public int JitterTpCount = 0;
            public int JitterTotal = 0;

            //RTP单通(单向数据传输)
            public int RTPUETransfer = 0;
            public int RTPUETransferCount = 0;
            //RTP断续
            public int RTPIntermittently = 0;
            public int RTPIntermittentlyCount = 0;
            //RTP吞字(丢包导致吞字)
            public int RTPPacketLost = 0;
            public int RTPPacketLostCount = 0;

            public double JitterAvg  //抖动率
            {
                get
                {
                    if (JitterTpCount > 0)
                    {
                        return Math.Round((double)JitterTotal / JitterTpCount, 2);
                    }
                    return 0.0;

                }
            }
            public double? HO1TimeSpan
            {
                get
                {
                    double? ret = null;
                    if (lstHOEvent.Count > 0)
                    {
                        ret = Math.Round((lstHOEvent[0].DateTime - StartTime).TotalSeconds, 2);
                    }
                    return ret;
                }
            }
            public double? HO2TimeSpan
            {
                get
                {
                    double? ret = null;
                    if (lstHOEvent.Count > 1)
                    {
                        ret = Math.Round((lstHOEvent[1].DateTime - StartTime).TotalSeconds, 2);
                    }
                    return ret;
                }
            }
            public double? HO1Delay
            {
                get
                {
                    double? ret = null;
                    if (lstHOEvent.Count > 0)
                    {
                        ret = Math.Round(double.Parse(lstHOEvent[0]["Value5"].ToString()) / 1000f, 2);
                    }
                    return ret;
                }
            }
            public double? HO2Delay
            {
                get
                {
                    double? ret = null;
                    if (lstHOEvent.Count > 1)
                    {
                        ret = Math.Round(double.Parse(lstHOEvent[1]["Value5"].ToString()) / 1000f, 2);
                    }
                    return ret;
                }
            }

            //切换时延(媒体面)
            public double? HO1DelayMedia
            {
                get
                {
                    double? ret = null;
                    if (lstHODelayMedia.Count > 0)
                    {
                        ret = lstHODelayMedia[0];
                    }
                    return ret;
                }
            }
            public double? HO2DelayMedia
            {
                get
                {
                    double? ret = null;
                    if (lstHODelayMedia.Count > 1)
                    {
                        ret = lstHODelayMedia[1];
                    }
                    return ret;
                }
            }
            public double? DelayMediaAverage
            {
                get
                {
                    double? ret = null;
                    if (lstHODelayMedia.Count > 0)
                    {
                        double temp = 0;
                        foreach (double delay in lstHODelayMedia)
                        {
                            temp += delay;
                        }
                        temp = Math.Round(temp / lstHODelayMedia.Count, 2);
                        ret = temp;
                    }
                    return ret;
                }
            }

            public string Handover1Arfcns;
            public string Handover2Arfcns;
            public string Handover3Arfcns;

            public string HOCellCGI
            {
                get
                {
                    bool bOnce = false;
                    string lacci = "";
                    if (lstHOCell.Count > 0)   //如果有切换
                    {
                        foreach (string hocell in lstHOCell)
                        {
                            if (bOnce == true)
                            {
                                lacci += "|";
                            }

                            lacci += hocell;

                            if (bOnce == false)
                            {
                                bOnce = true;
                            }
                        }
                    }
                    else
                    {
                        if (dicLacCi.Count > 0) //如果没有切换，取小区采样点
                        {
                            foreach (string cellKey in dicLacCi.Keys)
                            {
                                if (bOnce == true)
                                {
                                    lacci += "|";
                                }

                                lacci += cellKey;

                                if (bOnce == false)
                                {
                                    bOnce = true;
                                }
                            }
                        }
                    }
                    return lacci;
                }
            }
            public string LacCiCount
            {
                get
                {
                    bool bOnce = false;
                    string lacciCount = "";
                    foreach (string cellKey in dicLacCi.Keys)
                    {
                        if (bOnce == true)
                        {
                            lacciCount += "|";
                        }
                        lacciCount += dicLacCi[cellKey].ToString();
                        if (bOnce == false)
                        {
                            bOnce = true;
                        }
                    }
                    return lacciCount;
                }
            }
            public string TestPointCGI
            {
                get
                {
                    bool bOnce = false;
                    string lacci = "";
                    foreach (string cellKey in dicLacCi.Keys)
                    {
                        if (bOnce == true)
                        {
                            lacci += "|";
                        }
                        lacci += cellKey;
                        if (bOnce == false)
                        {
                            bOnce = true;
                        }
                    }
                    return lacci;
                }
            }
            public string BSC
            {
                get
                {
                    bool bOnce = false;
                    string strBSC = "";
                    foreach (string bsc in lstBSC)
                    {
                        if (bOnce == true)
                        {
                            strBSC += "|";
                        }

                        strBSC += bsc;

                        if (bOnce == false)
                        {
                            bOnce = true;
                        }
                    }
                    return strBSC;
                }
            }

            private List<string> bandTypeList = new List<string>();
            public string BandType
            {
                get
                {
                    return gatherStringListToString(bandTypeList);
                }
            }

            //A是否满频率发射
            public bool IsTxPowerAFull
            {
                get
                {
                    bool ret = false;
                    if (txPowerACount != 0)
                    {
                        ret = (txPowerAFull * 1.0 / txPowerACount) > 0.9;
                    }
                    return ret;
                }
            }

            //D是否满频率发射
            public bool IsTxPowerDFull
            {
                get
                {
                    bool ret = false;
                    if (txPowerDCount != 0)
                    {
                        ret = (txPowerDFull * 1.0 / txPowerDCount) > 0.9;
                    }
                    return ret;
                }
            }
            public bool IsTxPowerEFull
            {
                get
                {
                    bool ret = false;
                    if (txPowerECount != 0)
                    {
                        ret = (txPowerEFull * 1.0 / txPowerECount) > 0.9;
                    }
                    return ret;
                }
            }
            public bool IsTxPowerFFull
            {
                get
                {
                    bool ret = false;
                    if (txPowerFCount != 0)
                    {
                        ret = (txPowerFFull * 1.0 / txPowerFCount) > 0.9;
                    }
                    return ret;
                }
            }


            protected int txPowerSum = 0;
            protected List<int> txPowerList = new List<int>();

            public double? TxPowerAvg
            {
                get
                {
                    double? avg = null;
                    avg = Math.Round(txPowerSum * 1.0 / txPowerList.Count, 2);
                    return avg;
                }
            }

            public string StrTxPower
            {
                get
                {
                    return gatherIntListToString(txPowerList);
                }
            }

            public double? TxPowerMoreThanNum(List<int> powerlist, short num)
            {
                int count = 0;
                foreach (var power in powerlist)
                {
                    if (power >= num)
                        count++;
                }
                return Math.Round(count * 1.0 / powerlist.Count, 2);
            }
            public double? TxPowerMoreThan15
            {
                get
                {
                    return 100 * TxPowerMoreThanNum(txPowerList, 15);
                }
            }

            public List<float> rsrpDIF = new List<float>();
            public List<float> rxLevDIF = new List<float>();
            public string RsrpDIF
            {
                get
                {
                    return gatherMultiFloatListToString(rsrpDIF, rxLevDIF);
                }
            }

            private List<string> netWorkTypeList = new List<string>();
            public string NetWorkTypeDes
            {
                get
                {
                    return gatherStringListToString(netWorkTypeList);
                }
            }

            private List<short> transModeList = new List<short>();
            public string TransMode
            {
                get
                {
                    return gatherShortListToString(transModeList);
                }
            }

            public void Fill(List<TestPoint> tps, List<Event> evts, DateTime endTime)
            {
                fillTestPoint(tps);
                fillEvent(evts, endTime, null);
            }

            public void Fill(List<TestPoint> tps, List<Event> evts, List<Message> msgs, DateTime endTime)
            {
                fillTestPoint(tps);
                fillEvent(evts, endTime, msgs);
            }

            public void Fill(List<TestPoint> moTps, List<TestPoint> mtTps, List<Event> evts, List<Message> msgs, DateTime endTime)
            {
#if AllRtpMsg
                if (lossPackageSecondCondition > 0)
                {
                    fillMsg(moTps, mtTps, msgs);
                }
#endif
                fillTestPoint(moTps);
                fillEvent(evts, endTime, msgs);
            }

            private double lossPackageSecondCondition;
            public void SetCondition(double lossPackageSecondCondition)
            {
                this.lossPackageSecondCondition = lossPackageSecondCondition;
            }

            private double lossPackageSeconds;
            public double LossPackageSeconds
            {
                get
                {
                    return lossPackageSeconds;
                }
            }

            private string lossMultiPackageSecond = "";
            public string LossMultiPackageSecond
            {
                get
                {
                    return lossMultiPackageSecond.TrimEnd(',');
                }
            }

            private string lossPackageTime = "";
            public string LossPackageTime
            {
                get
                {
                    return lossPackageTime.TrimEnd(',');
                }
            }

            private List<float> moLastSINRTestPoint = new List<float>();
            private List<float> mtLastSINRTestPoint = new List<float>();
            public string MoLastSINRTestPoint
            {
                get
                {
                    string result = "";
                    foreach (var item in moLastSINRTestPoint)
                    {
                        if (float.IsNaN(item))
                        {
                            result += "-,";
                            continue;
                        }
                        result += item.ToString() + ",";
                    }
                    return result.TrimEnd(',');
                }
            }

            public string MtLastSINRTestPoint
            {
                get
                {
                    string result = "";
                    foreach (float item in mtLastSINRTestPoint)
                    {
                        if (float.IsNaN(item))
                        {
                            result += "-,";
                            continue;
                        }
                        result += item.ToString() + ",";
                    }
                    return result.TrimEnd(',');
                }
            }

            private void fillMsg(List<TestPoint> moTps, List<TestPoint> mtTps, List<Message> msgs)
            {
                //丢包时间点对应的datetime时间,用于判断丢包前采样点
                List<DateTime> lossPackageDateTime = new List<DateTime>();
                Message prevMsg = null;
                lossPackageSeconds = 0;
                lossMultiPackageSecond = "";
                lossPackageTime = "";
                for (int i = 0; i < msgs.Count; i++)
                {
                    //rtp下行丢包时间累加
                    if (msgs[i].ID == 2147426825 && msgs[i].Direction == 1)
                    {
                        if (prevMsg != null)
                        {
                            TimeSpan timespan = msgs[i].HandsetTime - prevMsg.HandsetTime;
                            if (timespan.TotalSeconds > lossPackageSecondCondition)
                            {
                                lossPackageDateTime.Add(prevMsg.DateTime);
                                lossPackageTime += prevMsg.HandsetTime.ToString("yyyy-MM-dd HH:mm:ss.fff") + ",";
                                lossMultiPackageSecond += timespan.TotalSeconds.ToString() + " ,";
                                lossPackageSeconds += timespan.TotalSeconds;
                            }
                        }
                        prevMsg = msgs[i];
                    }
                }

                //根据丢包信令点的datetime时间去匹配丢包前最后一个SINR采样点
                int moIndex = moTps.Count - 1;
                int mtIndex = mtTps.Count - 1;
                for (int i = lossPackageDateTime.Count - 1; i >= 0; i--)
                {
                    DateTime prevTime = i == 0 ? DateTime.MinValue : lossPackageDateTime[i - 1];
                    addLastSINRTestPoint(moTps, ref moIndex, lossPackageDateTime[i], prevTime, moLastSINRTestPoint);
                    addLastSINRTestPoint(mtTps, ref mtIndex, lossPackageDateTime[i], prevTime, mtLastSINRTestPoint);
                }
                moLastSINRTestPoint.Reverse();
                mtLastSINRTestPoint.Reverse();
            }

            private void addLastSINRTestPoint(List<TestPoint> tps, ref int index, DateTime time, DateTime prevTime, List<float> lastSINRTestPoint)
            {
                for (; index >= 0; index--)
                {
                    if (tps[index].DateTime <= time && tps[index].DateTime > prevTime)
                    {
                        float? sinr = (float?)tps[index]["NR_SS_SINR"];
                        if (sinr != null)
                        {
                            lastSINRTestPoint.Add((float)sinr);
                            break;
                        }
                    }
                    if (index <= 0 || tps[index - 1].DateTime <= prevTime)
                    {
                        lastSINRTestPoint.Add(float.NaN);
                        break;
                    }
                }
            }

            private void fillTestPoint(List<TestPoint> tps)
            {
                if (tps != null && tps.Count > 0)
                {
                    this.TpCount = tps.Count;
                    foreach (TestPoint tp in tps)
                    {
                        if (!netWorkTypeList.Contains(tp.NetworkType.ToString()))
                        {
                            this.netWorkTypeList.Add(tp.NetworkType.ToString());
                        }

                        short? transMode = (short?)tp["lte_Transmission_Mode"];
                        if (transMode != null)
                        {
                            transModeList.Add((short)transMode);
                        }

                        int? jitter = (int?)tp["NR_VONR_RTP_Jitter"];
                        if (jitter != null)
                        {
                            JitterTpCount++;
                            JitterTotal += (int)jitter;
                        }

                        //========================== rsrp and sinr ==========================
                        fillRsrpAndSinr(tp);

                        //========================== speechcodec ==========================
                        fillSpeechCodec(tp);

                        //==========================  Band and PUSCH_Power ==========================
                        fillBand(tp);

                        //=========================== Bler and RB =====================================
                        fillBlerAndRB(tp);

                        //========================== CellInfo ==========================
                        ICell icell = tp.GetMainCell();

                        if (icell != null && icell.Site != null)
                        {
                            string bsc = icell.Site.Name;
                            if (!string.IsNullOrEmpty(bsc) && !lstBSC.Contains(bsc))
                            {
                                lstBSC.Add(bsc);
                            }
                        }

                        //========================== Cells ==========================
                        int? tac = (int?)tp["NR_TAC"];
                        long? nci = (long?)tp["NR_NCI"];
                        if (tac == null || nci == null)
                        {
                            tac = (ushort?)tp["NR_lte_TAC"];
                            nci = (int?)tp["NR_lte_ECI"];
                        }

                        if (tac != null && nci != null && tac > 0 && nci > 0)
                        {
                            string sKey = tac + "_" + nci;
                            if (dicLacCi.ContainsKey(sKey))
                            {
                                dicLacCi[sKey]++;
                            }
                            else
                            {
                                dicLacCi.Add(sKey, 1);
                            }
                        }

                        //========================= RSRP DIF ============================
                        float? rsrp = (float?)tp["NR_SS_RSRP"];
                        if (!fillRsrpDif(tp, rsrp, true))
                        {
                            rsrp = (float?)tp["NR_lte_RSRP"];
                            fillRsrpDif(tp, rsrp, false);
                        }

                        //========================= DTX =======================================
                        int? dtx = (int?)tp["NR_PDSCH_Retransmission_BLER"];  // lte_PDSCH_RB_Number
                        if (dtx != null)
                        {
                            dtxList.Add((int)dtx);
                        }

                        //====================== Rank Indicator ================================
                        //short? rank = (short?)tp[""];  // lte_Rank_Indicator
                        //if (rank != null)
                        //{
                        //    rankIndicatorList.Add((short)rank);
                        //}

                        //======================= DL Frequency =================================
                        //if (dlFrequency == null)
                        //{
                        //    dlFrequency = (int?)tp[""];  // lte_DL_Frequency
                        //}
                    }
                }
            }

            private bool fillRsrpDif(TestPoint tp, float? rsrp, bool isNR)
            {
                bool hasFill = false;
                if (rsrp != null)
                {
                    float? rsrpMax = null;
                    for (int i = 0; i < 30; i++)
                    {
                        float? nRsrp;
                        if (isNR)
                        {
                            nRsrp = (float?)tp["NR_NCell_RSRP", i];
                        }
                        else
                        {
                            nRsrp = (float?)tp["NR_lte_NCell_RSRP", i];
                        }

                        if (nRsrp != null)
                        {
                            if (rsrpMax == null)
                            {
                                rsrpMax = nRsrp;
                            }
                            rsrpMax = (float)nRsrp > (float)rsrpMax ? nRsrp : rsrpMax;
                        }
                    }

                    if (rsrpMax != null)
                    {
                        if (isNR)
                        {
                            rsrpDIF.Add((float)(rsrp - rsrpMax));
                        }
                        else
                        {
                            rxLevDIF.Add((float)(rsrp - rsrpMax));
                        }
                        hasFill = true;
                    }
                }
                return hasFill;
            }

            private void fillEvent(List<Event> evts, DateTime endTime, List<Message> msgs)
            {
                DateTime endTimeExcptHoSucc = endTime.AddSeconds(-1);
                foreach (Event evt in evts)
                {
                    if (evt.DateTime > endTimeExcptHoSucc && 
                        !(evt.ID == 9077 || evt.ID == 9080 || evt.ID == 9296 || evt.ID == 9299)) //only hosuccess +1s
                    {
                        continue;
                    }

                    if (evt.ID == 9076 || evt.ID == 9079 || evt.ID == 9295 || evt.ID == 9298)
                    {
                        HORequest++;
                    }
                    else if (evt.ID == 9087)
                    {
                        RRCReEstablish++;
                    }
                    else if (evt.ID == 9078 || evt.ID == 9081 || evt.ID == 9297 || evt.ID == 9300)
                    {
                        HOFail++;
                    }
                    else if (evt.ID == 9077 || evt.ID == 9080 || evt.ID == 9296 || evt.ID == 9299)
                    {
                        if (evt.DateTime <= endTime)
                        {
                            lstHOEvent.Add(evt);
                            HOSucc++;
#if AllRtpMsg
                            getHODelayMedia(msgs, evt);
#endif
                        }

                        if (evt["CI"] != null && evt["TargetCI"] != null)
                        {
                            string sKeySrc = evt["LAC"].ToString() + "_" + evt["CI"].ToString();
                            string sKeyDest = evt["TargetLAC"].ToString() + "_" + evt["TargetCI"].ToString();

                            NRCell srcCell = evt.GetSrcCell() as NRCell;
                            NRCell targetCell = evt.GetTargetCell() as NRCell;
                            if (srcCell != null || targetCell != null)
                            {
                                string srcArfcn = srcCell == null ? "" : srcCell.ARFCN.ToString();
                                string targetArfcn = targetCell == null ? "" : targetCell.ARFCN.ToString();
                                string handoverArfcns = srcArfcn + "->" + targetArfcn;
                                switch (lstHOEvent.Count)
                                {
                                    case 1:
                                        this.Handover1Arfcns = handoverArfcns;
                                        break;
                                    case 2:
                                        this.Handover2Arfcns = handoverArfcns;
                                        break;
                                    case 3:
                                        this.Handover3Arfcns = handoverArfcns;
                                        break;
                                }

                                if (srcCell != null && targetCell != null)
                                {
                                    int srcBtsid = srcCell.BelongBTS.BTSID;
                                    int targetBtsid = targetCell.BelongBTS.BTSID;

                                    if (srcBtsid == targetBtsid && srcArfcn == targetArfcn)
                                    {
                                        SameBtsSameEarfcnHOCount++;
                                    }
                                    else if (srcBtsid == targetBtsid && srcArfcn != targetArfcn)
                                    {
                                        SameBtsDiffEarfcnHOCount++;
                                    }
                                    else if (srcBtsid != targetBtsid && srcArfcn == targetArfcn)
                                    {
                                        DiffBtsSameEarfcnHOCount++;
                                    }
                                    else if (srcBtsid != targetBtsid && srcArfcn != targetArfcn)
                                    {
                                        DiffBtsDiffEarfcnHOCount++;
                                    }
                                }
                            }

                            if (lstHOCell.Count == 0) //第一次保存，将源和目的小区都放入
                            {
                                if (sKeySrc != "0_0")
                                {
                                    lstHOCell.Add(sKeySrc);
                                }
                            }
                            if (sKeyDest != "0_0")
                            {
                                lstHOCell.Add(sKeyDest);
                            }
                        }
                    }
                    else if (evt.ID == 10313)
                    {
                        //RTP单通
                        if (evt["Value3"] != null)
                        {
                            string value3 = evt["Value3"].ToString();
                            RTPUETransfer += int.Parse(value3);
                            RTPUETransferCount++;
                        }
                    }
                    else if (evt.ID == 10314)
                    {
                        //RTP断续
                        if (evt["Value3"] != null)
                        {
                            string value3 = evt["Value3"].ToString();
                            RTPIntermittently += int.Parse(value3);
                            RTPIntermittentlyCount++;
                        }
                    }
                    else if (evt.ID == 10315)
                    {
                        //RTP吞字
                        if (evt["Value3"] != null)
                        {
                            string value3 = evt["Value3"].ToString();
                            RTPPacketLost += int.Parse(value3);
                            RTPPacketLostCount++;
                        }
                    }
                }
            }

            /// <summary>
            /// 根据切换事件的时间查询对应信令中的前一个RTP包和后一个RTP包
            /// </summary>
            /// <param name="msgs"></param>
            /// <param name="msgIndex"></param>
            /// <param name="evt"></param>
            /// <returns></returns>
            private void getHODelayMedia(List<Message> msgs, Event evt)
            {
                if (msgs != null)
                {
                    for (int i = 0; i < msgs.Count; i++)
                    {
                        Message preRTPMsg = new Message();
                        Message nextRTPMsg = new Message();
                        //事件与信令的时间相同,且信令为切换的信令
                        //if (evt.DateTime == msgs[i].DateTime && msgs[i].ID == 1093626370)
                        if (DateTime.Compare(evt.DateTime, msgs[i].DateTime) == 0 && msgs[i].ID == 0x432F8701)  // NR->RRC Reconfiguration Complete
                        {
                            //向前找到第一个RTP包信令
                            for (int pre = i - 1; pre >= 0; pre--)
                            {
                                if (msgs[pre].ID == 2147426825 && msgs[pre].Direction == 1)
                                {
                                    preRTPMsg = msgs[pre];
                                    break;
                                }
                            }

                            //向后找到第一个RTP包信令
                            for (int next = i + 1; next <= msgs.Count - 1; next++)
                            {
                                if (msgs[next].ID == 2147426825 && msgs[next].Direction == 1)
                                {
                                    nextRTPMsg = msgs[next];
                                    break;
                                }
                            }

                            double ret = Math.Round((nextRTPMsg.HandsetTime - preRTPMsg.HandsetTime).TotalMilliseconds);
                            if (ret > 0 && ret < 10000)
                            {
                                lstHODelayMedia.Add(ret);
                            }
                        }
                    }
                }
            }

            private void fillSpeechCodec(TestPoint tp)
            {
                #region 上行
                int? timesQAM16Ul = (int?)tp["NR_16QAM_Count_UL"];
                if (timesQAM16Ul != null)
                {
                    QAM16UlTimesTotal += (int)timesQAM16Ul;
                }

                int? timesQAM64Ul = (int?)tp["NR_64QAM_Count_UL"];
                if (timesQAM64Ul != null)
                {
                    QAM64UlTimesTotal += (int)timesQAM64Ul;
                }

                int? timesQPSKUl = (int?)tp["NR_QPSK_Count_UL"];
                if (timesQPSKUl != null)
                {
                    QPSKUlTimesTotal += (int)timesQPSKUl;
                }

                for (int i = 0; i < 32; i++)
                {
                    int? mcsUl = (int?)tp["NR_MCS_UL_Info", i];  // lte_MCS_UL
                    if (mcsUl != null)
                    {
                        mcs_UlSum += (int)mcsUl;
                        mcs_UlList.Add((int)mcsUl);
                    }
                }

                int? ulPathLoss = (int?)tp["NR_PUSCH_PathLoss"];
                if (ulPathLoss != null)
                {
                    ulPathLossSum += (int)ulPathLoss;
                    ulPathLossList.Add((int)ulPathLoss);
                }
                #endregion

                #region 下行码0
                int? timesQAM16DlC0 = (int?)tp["NR_16QAM_Count_DL_TB0"];
                if (timesQAM16DlC0 != null)
                {
                    QAM16DlCode0TimesTotal += (int)timesQAM16DlC0;
                }
                int? timesQAM64DlC0 = (int?)tp["NR_64QAM_Count_DL_TB0"];
                if (timesQAM64DlC0 != null)
                {
                    QAM64DlCode0TimesTotal += (int)timesQAM64DlC0;
                }
                int? timesQPSKDlC0 = (int?)tp["NR_QPSK_Count_DL_TB0"];
                if (timesQPSKDlC0 != null)
                {
                    QPSKDlCode0TimesTotal += (int)timesQPSKDlC0;
                }

                for (int i = 0; i < 32; i++)
                {
                    int? mcsDlCode0 = (int?)tp["NR_MCS_DL_Code0", i];
                    if (mcsDlCode0 != null)
                    {
                        mcsDlCode0Sum += (int)mcsDlCode0;
                        mcsDlCode0List.Add((int)mcsDlCode0);
                    }
                }
                #endregion

                #region 下行码1
                int? timesQAM16DlC1 = (int?)tp["NR_16QAM_Count_DL_TB1"];
                if (timesQAM16DlC1 != null)
                {
                    QAM16DlCode1TimesTotal += (int)timesQAM16DlC1;
                }

                int? timesQAM64DlC1 = (int?)tp["NR_64QAM_Count_DL_TB1"];
                if (timesQAM64DlC1 != null)
                {
                    QAM64DlCode1TimesTotal += (int)timesQAM64DlC1;
                }

                int? timesQPSKDlC1 = (int?)tp["NR_QPSK_Count_DL_TB1"];
                if (timesQPSKDlC1 != null)
                {
                    QPSKDlCode1TimesTotal += (int)timesQPSKDlC1;
                }

                for (int i = 0; i < 32; i++)
                {
                    int? mcsDlCode1 = (int?)tp["NR_MCS_DL_Code1", i];
                    if (mcsDlCode1 != null)
                    {
                        mcsDlCode1Sum += (int)mcsDlCode1;
                        mcsDlCode1List.Add((int)mcsDlCode1);
                    }
                }
                #endregion
            }

            private void fillBand(TestPoint tp)
            {
                string strBandType = "";
                int? arfcn = (int?)tp["NR_SSB_ARFCN"];
                if (arfcn != null)
                {
                    float? power = (float?)tp["NR_PUSCH_TxPower"];
                    NRBandType nrBandType = NRCell.GetBandTypeByArfcn((int)arfcn);
                    strBandType = nrBandType.ToString();
                    if (power != null)
                    {
                        txPowerSum += (int)power;
                        txPowerList.Add((int)power);
                    }

                    if (nrBandType == NRBandType.移动Band41)
                    {
                        Band_CM41++;

                        if (power != null)
                        {
                            txPowerACount++;

                            if (power >= 40)  //满功率暂定为40
                            {
                                txPowerAFull++;
                            }
                        }
                    }
                    else if (nrBandType == NRBandType.移动Band79)
                    {
                        Band_CM79++;

                        if (power != null)
                        {
                            txPowerDCount++;
                            if (power >= 40)  //满功率暂定为40
                            {
                                txPowerDFull++;
                            }
                        }
                    }
                    else if (nrBandType == NRBandType.联通Band78)
                    {
                        Band_CU78++;

                        if (power != null)
                        {
                            txPowerECount++;
                            if (power >= 40)  //满功率暂定为40
                            {
                                txPowerEFull++;
                            }
                        }
                    }
                    else if (nrBandType == NRBandType.电信Band78)
                    {
                        Band_CT78++;

                        if (power != null)
                        {
                            txPowerFCount++;
                            if (power >= 40)  //满功率暂定为40
                            {
                                txPowerFFull++;
                            }
                        }
                    }
                }
                else
                {
                    arfcn = (int?)tp["NR_lte_EARFCN"];
                    if (arfcn != null)
                    {
                        strBandType = LTECell.GetBandTypeByEarfcn((int)arfcn).ToString();
                    }
                }

                if (!string.IsNullOrEmpty(strBandType) && !bandTypeList.Contains(strBandType))
                {
                    bandTypeList.Add(strBandType);
                }
            }

            protected int dsInitBlerCount = 0;
            protected float dsInitBlerSum = 0;
            protected int usInitBlerCount = 0;
            protected float usInitBlerSum = 0;
            private void fillBlerAndRB(TestPoint tp)
            {
                //==================================下行初传BLER=========================
                float? dsInitBler = (float?)tp["NR_PDSCH_Initial_BLER"];
                if (dsInitBler != null)
                {
                    dsInitBlerCount++;
                    dsInitBlerSum += (float)dsInitBler;
                }

                //float? dsInitBler0 = (float?)tp["lte_PDSCH_Init_BLER_Code0"];
                //if (dsInitBler0 != null)
                //{
                //    dsInitBlerCount++;
                //    dsInitBlerSum += (float)dsInitBler0;
                //}

                //float? dsInitBler1 = (float?)tp["lte_PDSCH_Init_BLER_Code1"];
                //if (dsInitBler1 != null)
                //{
                //    dsInitBlerCount++;
                //    dsInitBlerSum += (float)dsInitBler1;
                //}

                //================================上行初传BLER==============================
                float? usInitBler = (float?)tp["NR_PUSCH_Initial_BLER"];
                if (usInitBler != null)
                {
                    usInitBlerCount++;
                    usInitBlerSum += (float)usInitBler;
                }
                //========================DL PRB=============================
                float? dlPRbNums = (float?)tp["NR_PDSCH_Residual_BLER"];
                if (dlPRbNums != null)
                {
                    dlPRbNumsSum += (int)dlPRbNums;
                    dlPRbNumsList.Add((int)dlPRbNums);
                }
                //========================UL PRB=============================
                float? ulPRbNums = (float?)tp["NR_PUSCH_Residual_BLER"];
                if (ulPRbNums != null)
                {
                    ulPRbNumsSum += (int)ulPRbNums;
                    ulPRbNumsList.Add((int)ulPRbNums);
                }
            }

            public double? dsInitBlerAvg
            {
                get
                {
                    double? avg = null;
                    avg = Math.Round(dsInitBlerSum / (double)dsInitBlerCount, 2);
                    return avg;
                }
            }
            public double? usInitBlerAvg
            {
                get
                {
                    double? avg = null;
                    avg = Math.Round(usInitBlerSum / (double)usInitBlerCount, 2);
                    return avg;
                }
            }

            protected int ulPathLossSum = 0;
            protected List<int> ulPathLossList = new List<int>();
            public double? UlPathLossAvg
            {
                get
                {
                    double? avg = null;
                    avg = Math.Round(ulPathLossSum / (double)ulPathLossList.Count, 2);
                    return avg;
                }
            }
            public string StrUlPathLoss
            {
                get
                {
                    return gatherIntListToString(ulPathLossList);
                }
            }
        }

    }

    public class NRSumInfo
    {
        public string StrRsrp
        {
            get
            {
                return gatherMultiFloatListToString(rsrpList, rxLevList);
            }
        }

        public string StrRsrpAvg
        {
            get
            {
                if (RsrpAvg != null && RxLevAvg != null)
                {
                    return "LTE:" + RsrpAvg + " ； GSM:" + RxLevAvg;
                }
                else if (RsrpAvg != null)
                {
                    return RsrpAvg.ToString();
                }
                else if (RxLevAvg != null)
                {
                    return "GSM:" + RxLevAvg.ToString();
                }
                return null;
            }
        }

        protected List<float> rsrpList = new List<float>();

        protected float rsrpSum = 0;
        public double? RsrpAvg
        {
            get
            {
                double? avg = null;
                if (rsrpList.Count != 0)
                {
                    avg = Math.Round(rsrpSum / (double)rsrpList.Count, 2);
                }
                return avg;
            }
        }

        protected List<float> rxLevList = new List<float>();
        protected float rxLevSum = 0;
        public double? RxLevAvg
        {
            get
            {
                double? avg = null;
                if (rxLevList.Count != 0)
                {
                    avg = Math.Round(rxLevSum / (double)rxLevList.Count, 2);
                }
                return avg;
            }
        }

        public string StrSinr
        {
            get
            {
                return gatherMultiFloatListToString(sinrList, rxQualList);
            }
        }
        public string StrSinrAvg
        {
            get
            {
                if (SinrAvg != null && rxQualAvg != null)
                {
                    return "LTE:" + SinrAvg + " ； GSM:" + rxQualAvg;
                }
                else if (SinrAvg != null)
                {
                    return SinrAvg.ToString();
                }
                else if (rxQualAvg != null)
                {
                    return "GSM:" + rxQualAvg.ToString();
                }
                return null;
            }
        }
        protected List<float> sinrList = new List<float>();

        protected float sinrSum = 0;
        public double? SinrAvg
        {
            get
            {
                double? avg = null;
                if (sinrList.Count != 0)
                {
                    avg = Math.Round(sinrSum / (double)sinrList.Count, 2);
                }
                return avg;
            }
        }

        protected List<float> rxQualList = new List<float>();

        protected float rxQualSum = 0;
        protected double? rxQualAvg
        {
            get
            {
                double? avg = null;
                if (rxQualList.Count != 0)
                {
                    avg = Math.Round(rxQualSum / (double)rxQualList.Count, 2);
                }
                return avg;
            }
        }

        protected string gatherMultiFloatListToString(List<float> lteList, List<float> gsmList)
        {
            if (lteList.Count > 0 && gsmList.Count > 0)
            {
                return "LTE: " + gatherFloatListToString(lteList) + " ； GSM: " + gatherFloatListToString(gsmList);
            }
            else if (lteList.Count > 0)
            {
                return gatherFloatListToString(lteList);
            }
            else if (gsmList.Count > 0)
            {
                return "GSM: " + gatherFloatListToString(gsmList);
            }
            return null;
        }
        protected string gatherFloatListToString(List<float> floatList)
        {
            StringBuilder strbDes = new StringBuilder();
            foreach (float value in floatList)
            {
                strbDes.Append(Math.Round(value, 2) + "|");
            }
            if (strbDes.Length > 0)
            {
                strbDes.Remove(strbDes.Length - 1, 1);
            }
            return strbDes.ToString();
        }

        protected string gatherIntListToString(List<int> intList)
        {
            StringBuilder strbDes = new StringBuilder();
            foreach (short value in intList)
            {
                strbDes.Append(value + "|");
            }
            if (strbDes.Length > 0)
            {
                strbDes.Remove(strbDes.Length - 1, 1);
            }
            return strbDes.ToString();
        }
        protected string gatherShortListToString(List<short> shortList)
        {
            StringBuilder strbDes = new StringBuilder();
            foreach (short value in shortList)
            {
                strbDes.Append(value + "|");
            }
            if (strbDes.Length > 0)
            {
                strbDes.Remove(strbDes.Length - 1, 1);
            }
            return strbDes.ToString();
        }
        protected string gatherStringListToString(List<string> stringList)
        {
            StringBuilder strbDes = new StringBuilder();
            foreach (string des in stringList)
            {
                strbDes.Append(des + "|");
            }
            if (strbDes.Length > 0)
            {
                strbDes.Remove(strbDes.Length - 1, 1);
            }
            return strbDes.ToString();
        }

        protected void fillRsrpAndSinr(TestPoint tp)
        {
            //==========================  rsrp ==========================
            float? rsrp = (float?)tp["NR_SS_RSRP"];
            if (rsrp != null)
            {
                rsrpList.Add((float)rsrp);
                rsrpSum += (float)rsrp;
            }
            else
            {
                float? ltersrp = (float?)tp["NR_lte_RSRP"];
                if (ltersrp != null)
                {
                    rxLevList.Add((float)ltersrp);
                    rxLevSum += (float)ltersrp;
                }
            }

            //==========================  sinr ==========================
            float? sinr = (float?)tp["NR_SS_SINR"];
            if (sinr != null)
            {
                sinrList.Add((float)sinr);
                sinrSum += (float)sinr;
            }
            else
            {
                float? ltesinr = (float?)tp["NR_lte_SINR"];
                if (ltesinr != null)
                {
                    rxQualList.Add((float)ltesinr);
                    rxQualSum += (float)ltesinr;
                }
            }
        }
    }



}
