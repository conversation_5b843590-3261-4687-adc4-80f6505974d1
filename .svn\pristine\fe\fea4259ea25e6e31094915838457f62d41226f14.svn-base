﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTRtpPacketsLostByFile : ZTRtpPacketsLostBase
    {
        public ZTRtpPacketsLostByFile(MainModel mainModel)
            : base(mainModel)
        {

        }

        protected static readonly object lockObj = new object();
        private static ZTRtpPacketsLostByFile instance = null;
        public static ZTRtpPacketsLostByFile GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new ZTRtpPacketsLostByFile(MainModel.GetInstance());
                    }
                }
            }
            return instance;
        }

        public override string Name
        {
            get
            {
                return "RTP丢包率分析";
            }
        }

        public override string IconName
        {
            get
            {
                return "Images/regionstat.gif";
            }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22096, this.Name);
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.File;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                return true;
            }
            return false;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        /// <summary>
        /// 判断是否在所选区域内
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            return true;
        }
    }

    public class ZTRtpPacketsLostByFile_FDD : ZTRtpPacketsLostByFile
    {
        private static ZTRtpPacketsLostByFile_FDD instance = null;
        public static new ZTRtpPacketsLostByFile_FDD GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new ZTRtpPacketsLostByFile_FDD(MainModel.GetInstance());
                    }
                }
            }
            return instance;
        }
        public ZTRtpPacketsLostByFile_FDD(MainModel mainModel)
            : base(mainModel)
        {
            this.Columns = new List<string>();
            this.IncludeMessage = true;
            this.Columns.Add("lte_fdd_volte_RTP_Packets_Lost_Num");
            this.Columns.Add("lte_fdd_volte_Source_SSRC");
            this.Columns.Add("lte_fdd_volte_RTP_Loss_Rate");
            this.Columns.Add("lte_fdd_TAC");
            this.Columns.Add("lte_fdd_ECI");
            this.Columns.Add("lte_fdd_volte_RTP_Packets_Num");
            this.Columns.Add("lte_fdd_volte_RTP_Direction");
            this.Columns.Add("lte_fdd_EARFCN");
            this.Columns.Add("lte_fdd_PCI");
            this.Columns.Add("lte_fdd_RSRP");
            this.Columns.Add("lte_fdd_SINR");
            this.Columns.Add("lte_fdd_volte_RTP_Media_Type");
        }
        protected override void doStatWithQuery()
        {
            List<ZTRtpPacketsLost> lostList = new List<ZTRtpPacketsLost>();
            foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
            {
                ZTRtpPacketsLost pLost = null;
                for (int i = 0; i < file.TestPoints.Count - 1; i++)
                {
                    TestPoint tpi = file.TestPoints[i];
                    if (tpi["lte_fdd_volte_RTP_Direction"] != null && tpi["lte_fdd_volte_RTP_Direction"].ToString() == "1")
                    {
                        int j = i + 1;
                        TestPoint tpj = file.TestPoints[j];
                        if (tpj["lte_fdd_volte_RTP_Direction"] != null
                            && tpj["lte_fdd_volte_RTP_Direction"].ToString() == "1"
                            && Convert.ToDouble(tpi["lte_fdd_volte_RTP_Packets_Lost_Num"].ToString()) < Convert.ToDouble(tpj["lte_fdd_volte_RTP_Packets_Lost_Num"].ToString())
                            && Convert.ToDouble(tpi["lte_fdd_volte_Source_SSRC"].ToString()) == Convert.ToDouble(tpj["lte_fdd_volte_Source_SSRC"].ToString())
                            && Convert.ToDouble(tpj["lte_fdd_volte_RTP_Loss_Rate"].ToString()) >= hoCondition.RtpLossRate)
                        {
                            pLost = new ZTRtpPacketsLost();
                            pLost.Longitude = tpj.Longitude;
                            pLost.Latitude = tpj.Latitude;
                            pLost.BeginTime = tpj.DateTime;
                            if (file.TestPoints[j]["lte_fdd_EARFCN"] != null)
                            {
                                pLost.EARFCN = Convert.ToInt32(file.TestPoints[j]["lte_fdd_EARFCN"].ToString());
                            }
                            if (file.TestPoints[j]["lte_fdd_PCI"] != null)
                            {
                                pLost.PCI = Convert.ToInt32(file.TestPoints[j]["lte_fdd_PCI"].ToString());
                            }
                            if (file.TestPoints[j]["lte_fdd_volte_RTP_Loss_Rate"] != null)
                            {
                                pLost.LossRate = Convert.ToDouble(file.TestPoints[j]["lte_fdd_volte_RTP_Loss_Rate"].ToString());
                            }
                            if (file.TestPoints[j]["lte_fdd_volte_RTP_Packets_Lost_Num"] != null)
                            {
                                pLost.PacketsLostNum = Convert.ToInt32(file.TestPoints[j]["lte_fdd_volte_RTP_Packets_Lost_Num"].ToString());
                            }
                            if (file.TestPoints[j]["lte_fdd_volte_RTP_Packets_Num"] != null)
                            {
                                pLost.PacketsNum = Convert.ToInt32(file.TestPoints[j]["lte_fdd_volte_RTP_Packets_Num"].ToString());
                            }
                            if (file.TestPoints[j]["lte_fdd_volte_RTP_Direction"] != null)
                            {
                                pLost.Direction = Convert.ToInt32(file.TestPoints[j]["lte_fdd_volte_RTP_Direction"].ToString());
                            }
                            if (file.TestPoints[j]["lte_fdd_volte_Source_SSRC"] != null)
                            {
                                pLost.SourceSSRC = Convert.ToDouble(file.TestPoints[j]["lte_fdd_volte_Source_SSRC"].ToString());
                            }
                            if (file.TestPoints[j]["lte_fdd_volte_RTP_Media_Type"] != null)
                            {
                                pLost.MediaType = Convert.ToInt32(file.TestPoints[j]["lte_fdd_volte_RTP_Media_Type"].ToString());
                            }
                            pLost.file = file;
                            pLost.AddTestPoint(tpj);
                            lostList.Add(pLost);
                        }
                    }
                }

                float? sumSINRBefore = 0;
                float? sumSINRAfter = 0;
                float? sumRSRPBefore = 0;
                float? sumRSRPAfter = 0;
                float? maxSINRBefore = float.MinValue;
                float? maxSINRAfter = float.MinValue;
                float? maxRSRPBefore = float.MinValue;
                float? maxRSRPAfter = float.MinValue;
                int numBefore = 0;
                int numAfter = 0;
                foreach (ZTRtpPacketsLost lost1 in lostList)
                {
                    foreach (TestPoint tp in file.TestPoints)
                    {
                        if ((lost1.BeginTime - tp.DateTime).TotalSeconds >= hoCondition.TimeSpan)
                        {
                            addTPInfo(ref sumSINRBefore, ref sumRSRPBefore, ref maxSINRBefore, ref maxRSRPBefore, ref numBefore, tp);
                        }
                        if ((tp.DateTime - lost1.BeginTime).TotalSeconds >= hoCondition.TimeSpan)
                        {
                            addTPInfo(ref sumSINRAfter, ref sumRSRPAfter, ref maxSINRAfter, ref maxRSRPAfter, ref numAfter, tp);
                        }
                    }
                    if (numBefore != 0)
                    {
                        lost1.SINRBefore = (float)Math.Round((float)sumSINRBefore / numBefore, 2);
                        lost1.RSRPBefore = (float)Math.Round((float)sumRSRPBefore / numBefore, 2);
                        lost1.MaxSINRBefore = (float)maxSINRBefore;
                        lost1.MaxRSRPBefore = (float)maxRSRPBefore;
                    }
                    if (numAfter != 0)
                    {
                        lost1.SINRAfter = (float)Math.Round((float)sumSINRAfter / numAfter, 2);
                        lost1.RSRPAfter = (float)Math.Round((float)sumRSRPAfter / numAfter, 2);
                        lost1.MaxSINRAfter = (float)maxSINRAfter;
                        lost1.MaxRSRPAfter = (float)maxRSRPAfter;
                    }
                }
                double? ssrc = 0.0;

                foreach (ZTRtpPacketsLost lost2 in lostList)
                {
                    if (lost2.SourceSSRC == ssrc)
                    {
                        continue;
                    }
                    ssrc = lost2.SourceSSRC;
                    ZTRtpPacketsLostList listLost = new ZTRtpPacketsLostList(lostList, lost2.SourceSSRC, resultList.Count + 1);
                    resultList.Add(listLost);
                }
                foreach (ZTRtpPacketsLostList list in resultList)
                {
                    foreach (ZTRtpPacketsLost lost in list.LostList)
                    {
                        list.TestPoints.AddRange(lost.TestPoints);
                    }
                }
            }
        }

        private void addTPInfo(ref float? sumSINR, ref float? sumRSRP, ref float? maxSINR, ref float? maxRSRP, ref int num, TestPoint tp)
        {
            if (tp["lte_fdd_SINR"] != null && tp["lte_fdd_RSRP"] != null)
            {
                if (maxRSRP < (float?)tp["lte_fdd_RSRP"])
                {
                    maxRSRP = (float?)tp["lte_fdd_RSRP"];
                }
                if (maxSINR < (float?)tp["lte_fdd_SINR"])
                {
                    maxSINR = (float?)tp["lte_fdd_SINR"];
                }
                sumSINR += (float?)tp["lte_fdd_SINR"];
                sumRSRP += (float?)tp["lte_fdd_RSRP"];
                num++;
            }
        }

        public override string Name
        {
            get
            {
                return "VOLTE_FDD RTP丢包率分析";
            }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 30000, 30019, this.Name);
        }
    }
}
