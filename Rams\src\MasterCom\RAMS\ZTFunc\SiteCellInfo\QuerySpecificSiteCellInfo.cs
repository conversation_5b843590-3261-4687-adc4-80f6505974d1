﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc.SiteCellInfo;
using MasterCom.Util;
using MasterCom.Util.UiEx;

namespace MasterCom.RAMS.ZTFunc
{
    public class QuerySpecificSiteCellInfo :QueryBase
    {
        public QuerySpecificSiteCellInfo()
            : base(MainModel.GetInstance())
        { }
        public override string Name
        {
            get { return "基站乡镇农村信息查询"; }
        }

        public override bool IsNeedSetQueryCondition
        {
            get
            {
                return false;
            }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 19000, 19045, this.Name);
        }
        protected override bool isValidCondition()
        {
            SettingDlg dlg = new SettingDlg();
            dlg.FileName = this.fileName;
            dlg.CellDistance = this.cellDistance;
            dlg.SiteDistance = this.siteDistance;
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            this.fileName = dlg.FileName;
            this.cellDistance = dlg.CellDistance;
            this.siteDistance = dlg.SiteDistance;
            siteInfos = loadSiteInfo(this.fileName);
            return true;
        }

        private List<SiteInfo> loadSiteInfo(string fileName)
        {
            List<SiteInfo> list = new List<SiteInfo>();
            DataSet dataSet = ExcelNPOIManager.ImportFromExcel(fileName);
            DataTable table = dataSet.Tables[0];
            bool firstRow = true;
            foreach (DataRow row in table.Rows)
            {
                if (firstRow)
                {
                    firstRow = false;
                    continue;
                }
                SiteInfo si = new SiteInfo(row);
                list.Add(si);
            }
            return list;
        }

        protected override void query()
        {
            WaitTextBox.Show("正在分析...", anaInThread);
            fireShowResultForm();
        }

        private void fireShowResultForm()
        {
            SiteCellResultForm frm = MainModel.CreateResultForm(typeof(SiteCellResultForm)) as SiteCellResultForm;
            frm.FillData(cellSiteInfos);
            frm.Visible = true;
            frm.BringToFront();
            cellSiteInfos = null;
        }

        private List<CellSiteInfo> cellSiteInfos = null;
        public void anaInThread()
        {
            List<LTEBTS> btsSet = new List<LTEBTS>();
            foreach (LTEBTS bts in CellManager.GetInstance().GetCurrentLTEBTSs())
            {
                if (bts.Type == LTEBTSType.Indoor)
                {
                    continue;
                }
                if (MainModel.SearchGeometrys.Region != null)
                {
                    if (MainModel.SearchGeometrys.GeoOp.Contains(bts.Longitude, bts.Latitude))
                    {
                        btsSet.Add(bts);
                    }
                }
                else
                {
                    btsSet.Add(bts);
                }
            }

            Dictionary<int, BTSSiteInfo> btsIDDic = getBtsIDDic(btsSet);

            foreach (SiteInfo site in siteInfos)
            {
                site.MakeSummary();
            }
            cellSiteInfos = new List<CellSiteInfo>();
            foreach (BTSSiteInfo bts in btsIDDic.Values)
            {
                cellSiteInfos.AddRange(bts.MakeSummary());
            }
            WaitTextBox.Close();
        }

        private Dictionary<int, BTSSiteInfo> getBtsIDDic(List<LTEBTS> btsSet)
        {
            Dictionary<int, BTSSiteInfo> btsIDDic = new Dictionary<int, BTSSiteInfo>();
            double roughSiteDis = siteDistance / 100000;
            double roughCellDis = cellDistance / 100000;
            for (int i = 0; i < btsSet.Count; i++)
            {
                LTEBTS bts = btsSet[i];
                BTSSiteInfo cellInfo = null;
                if (i == 0)
                {
                    cellInfo = new BTSSiteInfo(bts);
                    btsIDDic[i] = cellInfo;
                }
                else
                {
                    cellInfo = btsIDDic[i];
                }
                calcCell2SiteDis(cellInfo, roughSiteDis, siteDistance);
                for (int j = i + 1; j < btsSet.Count; j++)
                {
                    BTSSiteInfo cellInfoOther = null;
                    if (!btsIDDic.TryGetValue(j, out cellInfoOther))
                    {
                        LTEBTS btsOther = btsSet[j];
                        cellInfoOther = new BTSSiteInfo(btsOther);
                        btsIDDic[j] = cellInfoOther;
                    }
                    calcCell2CellDis(cellInfo, cellInfoOther, roughCellDis, cellDistance);
                }
            }

            return btsIDDic;
        }

        private void calcCell2SiteDis(BTSSiteInfo cellInfo, double roughDis, double exactDis)
        {
            foreach (SiteInfo site in siteInfos)
            {
                if (Math.Abs(cellInfo.BTS.Longitude - site.CenterLng) < roughDis
                    && Math.Abs(cellInfo.BTS.Latitude - site.CenterLat) < roughDis)
                {
                    double dis = Math.Round(MathFuncs.GetDistance(cellInfo.BTS.Longitude, cellInfo.BTS.Latitude
                        , site.CenterLng, site.CenterLat), 2);
                    if (dis <= exactDis)
                    {
                        site.AddBTS(cellInfo, dis);
                        cellInfo.AddSite(site, dis);
                    }
                }
            }
        }

        private void calcCell2CellDis(BTSSiteInfo cellInfo, BTSSiteInfo cellInfoOther,double roughDis, double exactDis)
        {
            if (Math.Abs(cellInfo.BTS.Longitude - cellInfoOther.BTS.Longitude) < roughDis
                && Math.Abs(cellInfo.BTS.Latitude - cellInfoOther.BTS.Latitude) < roughDis)
            {
                double dis = Math.Round(MathFuncs.GetDistance(cellInfo.BTS.Longitude, cellInfo.BTS.Latitude
                        , cellInfoOther.BTS.Longitude, cellInfoOther.BTS.Latitude), 2);
                if (dis <= exactDis)
                {
                    cellInfo.AddOther(cellInfoOther, dis);
                    cellInfoOther.AddOther(cellInfo, dis);
                }
            }
        }

        List<SiteInfo> siteInfos = null;

        public string fileName { get; set; }

        public double cellDistance { get; set; } = 1500;

        public double siteDistance { get; set; } = 1500;

    }
}
