using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.Util;
using System.Xml;

namespace MasterCom.RAMS.Model
{
    public class EventInfoSet
    {
        public static object GetItemValue(XmlConfigFile configFile, XmlElement item, string typeName)
        {
            if (typeName.Equals(typeof(EventInfoSet).Name))
            {
                EventInfoSet eventInfoSet = new EventInfoSet();
                eventInfoSet.Name = configFile.GetItemValue(item, "Name") as string;
                List<object> list = null;
                list = configFile.GetItemValue(item, "EventIDs") as List<object>;
                if (list != null)
                {
                    foreach (object value in list)
                    {
                        eventInfoSet.eventIDs.Add((int)value);
                    }
                }
                return eventInfoSet;
            }
            return null;
        }

        public static XmlElement AddItem(XmlConfigFile configFile, XmlElement config, string name, object value)
        {
            if (value is EventInfoSet)
            {
                XmlElement item = configFile.AddItem(config, name, value.GetType());
                configFile.AddItem(item, "Name", (value as EventInfoSet).Name);
                configFile.AddItem(item, "EventIDs", (value as EventInfoSet).eventIDs);
                return item;
            }
            return null;
        }

        public EventInfoSet(string name)
        {
            this.Name = name;
        }

        private EventInfoSet()
        {
        }

        public string Name { get; set; }

        public List<int> EventIDs
        {
            get { return eventIDs; }
        }

        private readonly List<int> eventIDs = new List<int>();
    }
}
