﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.Func.SystemSetting
{
    public partial class CellSetAnaProperties_GSM : PropertiesControl
    {
        private DevExpress.XtraEditors.GroupControl groupControl1;
        private DevExpress.XtraEditors.SpinEdit numNeighborCell;
        private DevExpress.XtraEditors.SpinEdit numMaxRxQual;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.SpinEdit numMinRxQual;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.GroupControl groupControl;
        private DevExpress.XtraEditors.CheckEdit chkBackgroundStat;

        private readonly ZTDIYCellSetByRegion queryFunc;

        public CellSetAnaProperties_GSM(ZTDIYCellSetByRegion queryFunc)
        {
            InitializeComponent();
            this.queryFunc = queryFunc;
        }

        public override string ParentName
        {
            get { return queryFunc.FuncType.ToString(); }
        }

        public override string ParentSubName
        {
            get { return queryFunc.SubFuncType.ToString(); }
        }

        public override string SelfName
        {
            get { return queryFunc.Name; }
        }

        public override string TabPageName
        {
            get { return queryFunc.Name; }
        }

        public override void Flush()
        {
            chkBackgroundStat.Checked = queryFunc.BackgroundStat;
            numMinRxQual.Value = queryFunc.RxQualMin;
            numMaxRxQual.Value = queryFunc.RxQualMax;
            numNeighborCell.Value = queryFunc.NeighborCellCnt;
            
        }

        public override bool IsValid()
        {
            return true;
        }

        public override void Apply()
        {
            queryFunc.BackgroundStat = chkBackgroundStat.Checked;
            queryFunc.RxQualMin = (int)numMinRxQual.Value;
            queryFunc.RxQualMax = (int)numMaxRxQual.Value;
            queryFunc.NeighborCellCnt = (int)numNeighborCell.Value;
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.numNeighborCell = new DevExpress.XtraEditors.SpinEdit();
            this.numMaxRxQual = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.numMinRxQual = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.groupControl = new DevExpress.XtraEditors.GroupControl();
            this.chkBackgroundStat = new DevExpress.XtraEditors.CheckEdit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numNeighborCell.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxRxQual.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMinRxQual.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl)).BeginInit();
            this.groupControl.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chkBackgroundStat.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // groupControl1
            // 
            this.groupControl1.Controls.Add(this.numNeighborCell);
            this.groupControl1.Controls.Add(this.numMaxRxQual);
            this.groupControl1.Controls.Add(this.labelControl3);
            this.groupControl1.Controls.Add(this.labelControl2);
            this.groupControl1.Controls.Add(this.numMinRxQual);
            this.groupControl1.Controls.Add(this.labelControl1);
            this.groupControl1.Location = new System.Drawing.Point(3, 62);
            this.groupControl1.Name = "groupControl1";
            this.groupControl1.Size = new System.Drawing.Size(448, 102);
            this.groupControl1.TabIndex = 5;
            this.groupControl1.Text = "条件设置";
            // 
            // numNeighborCell
            // 
            this.numNeighborCell.EditValue = new decimal(new int[] {
            3,
            0,
            0,
            0});
            this.numNeighborCell.Location = new System.Drawing.Point(124, 67);
            this.numNeighborCell.Name = "numNeighborCell";
            this.numNeighborCell.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numNeighborCell.Properties.IsFloatValue = false;
            this.numNeighborCell.Properties.Mask.EditMask = "f0";
            this.numNeighborCell.Properties.MaxValue = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.numNeighborCell.Size = new System.Drawing.Size(63, 21);
            this.numNeighborCell.TabIndex = 28;
            // 
            // numMaxRxQual
            // 
            this.numMaxRxQual.EditValue = new decimal(new int[] {
            7,
            0,
            0,
            0});
            this.numMaxRxQual.Location = new System.Drawing.Point(190, 33);
            this.numMaxRxQual.Name = "numMaxRxQual";
            this.numMaxRxQual.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numMaxRxQual.Properties.IsFloatValue = false;
            this.numMaxRxQual.Properties.Mask.EditMask = "f0";
            this.numMaxRxQual.Properties.MaxValue = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.numMaxRxQual.Size = new System.Drawing.Size(63, 21);
            this.numMaxRxQual.TabIndex = 27;
            // 
            // labelControl3
            // 
            this.labelControl3.Location = new System.Drawing.Point(269, 36);
            this.labelControl3.Name = "labelControl3";
            this.labelControl3.Size = new System.Drawing.Size(24, 14);
            this.labelControl3.TabIndex = 25;
            this.labelControl3.Text = "dBm";
            // 
            // labelControl2
            // 
            this.labelControl2.Location = new System.Drawing.Point(40, 70);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(84, 14);
            this.labelControl2.TabIndex = 24;
            this.labelControl2.Text = "最强邻区个数：";
            // 
            // numMinRxQual
            // 
            this.numMinRxQual.EditValue = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.numMinRxQual.Location = new System.Drawing.Point(40, 33);
            this.numMinRxQual.Name = "numMinRxQual";
            this.numMinRxQual.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numMinRxQual.Properties.IsFloatValue = false;
            this.numMinRxQual.Properties.Mask.EditMask = "f0";
            this.numMinRxQual.Properties.MaxValue = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.numMinRxQual.Size = new System.Drawing.Size(63, 21);
            this.numMinRxQual.TabIndex = 21;
            // 
            // labelControl1
            // 
            this.labelControl1.Location = new System.Drawing.Point(124, 36);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(50, 14);
            this.labelControl1.TabIndex = 22;
            this.labelControl1.Text = "≤ 质量 ≤";
            // 
            // groupControl
            // 
            this.groupControl.Controls.Add(this.chkBackgroundStat);
            this.groupControl.Location = new System.Drawing.Point(3, 3);
            this.groupControl.Name = "groupControl";
            this.groupControl.Size = new System.Drawing.Size(448, 53);
            this.groupControl.TabIndex = 4;
            this.groupControl.Text = "开关设置";
            // 
            // chkBackgroundStat
            // 
            this.chkBackgroundStat.Location = new System.Drawing.Point(15, 26);
            this.chkBackgroundStat.Name = "chkBackgroundStat";
            this.chkBackgroundStat.Properties.Caption = "启用";
            this.chkBackgroundStat.Size = new System.Drawing.Size(75, 19);
            this.chkBackgroundStat.TabIndex = 0;
            // 
            // CellSetAnaProperties_GSM
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.groupControl1);
            this.Controls.Add(this.groupControl);
            this.Name = "CellSetAnaProperties_GSM";
            this.Size = new System.Drawing.Size(454, 280);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            this.groupControl1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numNeighborCell.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxRxQual.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMinRxQual.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl)).EndInit();
            this.groupControl.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.chkBackgroundStat.Properties)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion
    }
}
