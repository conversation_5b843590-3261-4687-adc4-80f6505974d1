﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.ZTFunc.ZTLTESINR;

namespace MasterCom.RAMS.ZTFunc
{
    public class LTESINRQueryByFile : LTESINRQuery
    {
        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.File;
        }
        private static LTESINRQueryByFile instance;
        public static LTESINRQueryByFile Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new LTESINRQueryByFile(MainModel.GetInstance());
                }
                return instance;
            }
        }
        public LTESINRQueryByFile(MainModel mModel)
            : base(mModel)
        {
            this.IncludeEvent = false;
            analyzer = new LTESINRAnalyzer();
        }
        public override string Name
        {
            get { return "SINR与RSRP关联(按文件)"; }
        }
        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }
    }

    public class LTESINRQueryByFile_FDD : LTESINRQueryByFile
    {
        private static LTESINRQueryByFile_FDD instance = null;
        private static object obj = new object();
        public static LTESINRQueryByFile_FDD GetInstance()
        {
            if (instance == null)
            {
                lock (obj)
                {
                    if (instance == null)
                    {
                        instance = new LTESINRQueryByFile_FDD(MainModel.GetInstance());
                    }
                }
            }
            return instance;
        }

        public LTESINRQueryByFile_FDD(MainModel mainModel)
            : base(mainModel)
        {

        }
        public override string Name
        {
            get { return "SINR与RSRP关联_LTE_FDD(按文件)"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 26000, 26026, this.Name);
        }
    }
}
