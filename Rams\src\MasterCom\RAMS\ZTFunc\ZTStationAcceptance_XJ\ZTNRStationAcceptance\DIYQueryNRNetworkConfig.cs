﻿using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTStationAcceptance_XJ
{
    public class DIYQueryNRNetworkConfig : DiyQueryDataBase
    {
        public string tableName { get; set; } = "tb_xinjiang_NRNetworkConfig";
        public List<NRNetworkConfigDBInfo> NRNetworkConfigDBInfoList { get; private set; }

        public DIYQueryNRNetworkConfig()
            : base()
        { }

        public override string Name { get { return "查询NR网管配置"; } }

        protected override string getSqlTextString()
        {
            string name = $"{tableName}_" + DateTime.Now.ToString("yyyyMMdd");
            StringBuilder selectSQL = new StringBuilder();
            selectSQL.AppendFormat(@"SELECT [基站名称],[小区名称],[NODEBID],[TAC],[CellCount],[CellID],[PCI],[根序列]
FROM {0} where [基站名称]='{1}'", name, btsName);

            return selectSQL.ToString();
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            int idx = 0;
            E_VType[] rType = new E_VType[8];
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_Int;
            rType[idx++] = E_VType.E_Int;
            rType[idx++] = E_VType.E_Int;
            rType[idx++] = E_VType.E_Int;
            rType[idx++] = E_VType.E_Int;
            rType[idx] = E_VType.E_String;
            return rType;
        }

        protected override void initData()
        {
            NRNetworkConfigDBInfoList = new List<NRNetworkConfigDBInfo>();
        }

        protected override void dealReceiveData(Package package)
        {
            NRNetworkConfigDBInfo info = new NRNetworkConfigDBInfo();
            info.FillData(package);
            NRNetworkConfigDBInfoList.Add(info);
        }
    }

    public class NRNetworkConfigDBInfo
    {
        public string BtsName { get; set; }
        public string CellName { get; set; }
        public int NodeBID { get; set; }
        public int TAC { get; set; }
        public int CellCount { get; set; }
        public int CellID { get; set; }
        public int PCI { get; set; }
        public string PRACH { get; set; }

        public int BBU { get; set; }
        public int RRU { get; set; }

        public void FillData(Package package)
        {
            BtsName = package.Content.GetParamString();
            CellName = package.Content.GetParamString();
            NodeBID = package.Content.GetParamInt();
            TAC = package.Content.GetParamInt();
            CellCount = package.Content.GetParamInt();
            CellID = package.Content.GetParamInt();
            PCI = package.Content.GetParamInt();
            PRACH = package.Content.GetParamString();

            //BBU = package.Content.GetParamInt();
            //RRU = package.Content.GetParamInt();
        }
    }
}
