﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class RegionComplaintReportForm : MinCloseForm
    {
        public RegionComplaintReportForm()
        {
            InitializeComponent();
            init();
        }

        public RegionComplaintReportForm(MainModel mainModel)
            :base(mainModel)
        {
            InitializeComponent();
            init();
        }

        private void init()
        {
            dtpBegin.Value = DateTime.Now.AddDays(1 - DateTime.Now.Day);
            ZTRegionComplainInitSQLQuery initquery = new ZTRegionComplainInitSQLQuery(MainModel);
            initquery.Query();
            cbxNetwork.Items.Clear();
            foreach (string network in initquery.Networks)
            {
                cbxNetwork.Items.Add(network);
            }
            cbxNetwork.Items.Add("全部");
            cbxNetwork.Text = "全部";
            tmenuitemGSMKPI.DropDownItems.Clear();
            foreach (string dropdownitem in initquery.GSMMenuItems)
            {
                ToolStripMenuItem menuitem = new ToolStripMenuItem(dropdownitem);
                menuitem.Tag = "GSM区域评估报表";
                menuitem.Click += new EventHandler(toolStripMenuItem_Click);
                tmenuitemGSMKPI.DropDownItems.Add(menuitem);
            }
            tmenuitemTDKPI.DropDownItems.Clear();
            foreach (string dropdownitem in initquery.TDMenuList)
            {
                ToolStripMenuItem menuitem = new ToolStripMenuItem(dropdownitem);
                menuitem.Tag = "TD区域评估报表";
                menuitem.Click += new EventHandler(toolStripMenuItem_Click);
                tmenuitemTDKPI.DropDownItems.Add(menuitem);
            }
        }
        public void FillData(List<RegionComplainItem> datasources)
        {
            BindingSource bindingSource = new BindingSource();
            bindingSource.DataSource = typeof(RegionComplainItem);
            foreach (RegionComplainItem regionitem in datasources)
            {
                bindingSource.Add(regionitem);
            }
            this.gvGridResult.DataSource = bindingSource;
        }

        private void toolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (gvGridResult.SelectedRows.Count > 0)
            {
                string gridName = gvGridResult.SelectedRows[0].Cells["gvcGridName"].Value as string;
                ToolStripMenuItem menuitem = sender as ToolStripMenuItem;
                string datepick = menuitem.Text;
                this.labelGridName.Text = gridName;
                this.labelDatePick.Text = datepick;
                tabResult.SelectedTab = tabpageKPIResult;
                string[] splits = datepick.Split(new string[] { "至" }, StringSplitOptions.RemoveEmptyEntries);
                if (splits.Length == 2)
                {
                    ZTRegionKPISQLQuery kpiquery = new ZTRegionKPISQLQuery(this.MainModel);
                    kpiquery.ReportName = menuitem.Tag.ToString();
                    kpiquery.GridName = gridName;
                    kpiquery.BeginDate = splits[0];
                    kpiquery.EndDate = splits[1];
                    kpiquery.Query();
                    ZTRegionKQISQLQuery kqiquery = new ZTRegionKQISQLQuery(this.MainModel);
                    kqiquery.ReportName = menuitem.Tag.ToString();
                    kqiquery.GridName = gridName;
                    kqiquery.BeginDate = splits[0];
                    kqiquery.EndDate = splits[1];
                    kqiquery.Query();
                    BindingSource kpisource = new BindingSource();
                    kpisource.DataSource = typeof(RegionKPIItem);
                    foreach(RegionKPIItem kpiitem in kpiquery.RegionKPIs)
                    {
                        kpisource.Add(kpiitem);
                    }
                    this.gvGridKPI.DataSource = kpisource;
                    BindingSource kqisource = new BindingSource();
                    kqisource.DataSource = typeof(RegionKQIItem);
                    foreach (RegionKQIItem kqiitem in kqiquery.RegionKQIs)
                    {
                        kqisource.Add(kqiitem);
                    }
                    this.gvGridKQI.DataSource = kqisource;
                }
            }
        }

        private void tbTOPN_KeyPress(object sender, KeyPressEventArgs e)
        {
            int ch = e.KeyChar;
            if (!((ch >= 48) && (ch <= 57) || ch == 8 || ch == 13))
            {
                e.Handled = true;
            }
        }

        private void btnQuery_Click(object sender, EventArgs e)
        {
            tabResult.SelectedTab = tabpageGridResult;
            ZTRegionComplainSQLQuery regionquery = new ZTRegionComplainSQLQuery(this.MainModel);
            regionquery.Network = this.cbxNetwork.Text;
            regionquery.BeginDate = this.dtpBegin.Value.ToString("yyyy-MM-dd");
            regionquery.EndDate = this.dtpEnd.Value.ToString("yyyy-MM-dd");
            int topn;
            if (!int.TryParse(tbTOPN.Text, out topn))
            {
                topn = 20;
            }
            regionquery.SetTopN(topn);
            regionquery.Query();
        }
    }
}
