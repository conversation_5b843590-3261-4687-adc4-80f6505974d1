﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NR700StationSettingDlg_XJ : BaseDialog
    {
        public NR700StationSettingDlg_XJ()
        {
            InitializeComponent();
        }
        public NR700StationSettingDlg_XJ(NR700StationSettingDlgConfigModel_XJ condition)
        {
            InitializeComponent();
            setCondition(condition);
        }

        public void setCondition(NR700StationSettingDlgConfigModel_XJ condition)
        {
            if (condition == null)
            {
                return;
            }

            txtAccessRate.Value = Convert.ToDecimal(condition.AccessSuccessRate);
            txtBigPing.Value = Convert.ToDecimal(condition.BigPingTimeDelay);
            txtSmallPing.Value = Convert.ToDecimal(condition.SmallPingTimeDelay);
            txtDownRsrp.Value = Convert.ToDecimal(condition.DownTestRSRP);
            txtDownAvgSINR.Value = Convert.ToDecimal(condition.DownTestAvgSINR);
            txtDownThrought.Value = Convert.ToDecimal(condition.DownThroughput);
            txtUploadRSRP.Value = Convert.ToDecimal(condition.UploadTestRSRP);
            txtUploadAvgSINR.Value = Convert.ToDecimal(condition.UploadAvgSINR);
            txtUploadThrought.Value = Convert.ToDecimal(condition.UploadThroughput);
            txtCallSuccessRate.Value = Convert.ToDecimal(condition.CallSuccessRate);
            txtCallRate4G.Value = Convert.ToDecimal(condition.CallSuccessRate4G);
            txtFRSuccessRate.Value = Convert.ToDecimal(condition.FRSuccessRate5G);
            txtFRSuccessRate4G.Value = Convert.ToDecimal(condition.FRSuccessRate4G);
            txtStationInSwitch.Value = Convert.ToDecimal(condition.StationInSwitch);
            txtStationBtwSwitchRate.Value = Convert.ToDecimal(condition.StationBtwSwitch);
        }

        public NR700StationSettingDlgConfigModel_XJ GetCondition()
        {
            NR700StationSettingDlgConfigModel_XJ condition = new NR700StationSettingDlgConfigModel_XJ();
            condition.AccessSuccessRate = txtAccessRate.Value.ToString();
            condition.BigPingTimeDelay = txtBigPing.Value.ToString();
            condition.SmallPingTimeDelay = txtSmallPing.Value.ToString();
            condition.DownTestRSRP = txtDownAvgSINR.Value.ToString();
            condition.DownTestAvgSINR = txtDownAvgSINR.Value.ToString();
            condition.DownThroughput = txtDownThrought.Value.ToString();
            condition.UploadTestRSRP = txtUploadRSRP.Value.ToString();
            condition.UploadAvgSINR = txtAccessRate.Value.ToString();
            condition.UploadThroughput = txtUploadThrought.Value.ToString();
            condition.CallSuccessRate = txtCallSuccessRate.Value.ToString();
            condition.CallSuccessRate4G = txtCallRate4G.Value.ToString();
            condition.FRSuccessRate5G = txtFRSuccessRate.Value.ToString();
            condition.FRSuccessRate4G = txtFRSuccessRate4G.Value.ToString();
            condition.StationInSwitch = txtStationInSwitch.Value.ToString();
            condition.StationBtwSwitch = txtStationBtwSwitchRate.Value.ToString();
            return condition;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            var cond = GetCondition();
            if (cond != null)
            {
                NR700StationSettingDlgConfig_XJ.Instance.SaveConfig(cond);
                DialogResult = DialogResult.OK;
            }
            else
            {
                MessageBox.Show("主库连接设置不能为空");
            }
        }

        private void groupBox3_Enter(object sender, EventArgs e)
        {

        }
    }
}
