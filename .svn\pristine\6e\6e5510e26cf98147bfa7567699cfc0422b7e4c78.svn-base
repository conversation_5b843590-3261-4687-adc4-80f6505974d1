﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public class LTEHnadoverNcellQueryAnaByFile : LTEHnadoverNcellQueryAnaByRegion
    {
        private LTEHnadoverNcellQueryAnaByFile()
            : base()
        {
        }

        private static LTEHnadoverNcellQueryAnaByFile intance = null;
        public static new LTEHnadoverNcellQueryAnaByFile GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new LTEHnadoverNcellQueryAnaByFile();
                    }
                }
            }
            return intance;
        }

        public override string Name
        {
            get { return "LTE切换主邻区信息(按文件)"; }
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.File;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                return true;
            }
            return false;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        /// <summary>
        /// 判断是否在所选区域内
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            return true;
        }

    }

    public class LTEHnadoverNcellQueryAnaByFile_FDD : LTEHnadoverNcellQueryAnaByRegion_FDD
    {
        private static LTEHnadoverNcellQueryAnaByFile_FDD instance = null;
        public static new LTEHnadoverNcellQueryAnaByFile_FDD GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new LTEHnadoverNcellQueryAnaByFile_FDD();
                    }
                }
            }
            return instance;
        }

        public override string Name
        {
            get { return "LTE_FDD切换主邻区信息(按文件)"; }
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.File;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                return true;
            }
            return false;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        /// <summary>
        /// 判断是否在所选区域内
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            return true;
        }
    }
}
