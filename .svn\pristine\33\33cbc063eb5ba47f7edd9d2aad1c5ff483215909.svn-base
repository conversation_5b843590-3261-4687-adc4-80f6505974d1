﻿namespace MasterCom.RAMS.NewBlackBlock
{
    partial class BlackBlockInfoDlg
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(BlackBlockInfoDlg));
            this.label1 = new System.Windows.Forms.Label();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.edtHandleUser = new System.Windows.Forms.TextBox();
            this.lblHandleUser = new System.Windows.Forms.Label();
            this.tbxReason = new System.Windows.Forms.TextBox();
            this.btnModify = new System.Windows.Forms.Button();
            this.tbxName = new System.Windows.Forms.TextBox();
            this.label2 = new System.Windows.Forms.Label();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.tbxEventDes = new System.Windows.Forms.TextBox();
            this.label15 = new System.Windows.Forms.Label();
            this.tbxLastTest = new System.Windows.Forms.TextBox();
            this.label14 = new System.Windows.Forms.Label();
            this.label13 = new System.Windows.Forms.Label();
            this.label11 = new System.Windows.Forms.Label();
            this.tbxCellDesc = new System.Windows.Forms.TextBox();
            this.tbxPlaceDesc = new System.Windows.Forms.TextBox();
            this.tbxFirstAbDate = new System.Windows.Forms.TextBox();
            this.label10 = new System.Windows.Forms.Label();
            this.tbxLastAbEvent = new System.Windows.Forms.TextBox();
            this.label9 = new System.Windows.Forms.Label();
            this.label8 = new System.Windows.Forms.Label();
            this.label5 = new System.Windows.Forms.Label();
            this.tbxCloseDate = new System.Windows.Forms.TextBox();
            this.tbxCreateDate = new System.Windows.Forms.TextBox();
            this.label7 = new System.Windows.Forms.Label();
            this.label12 = new System.Windows.Forms.Label();
            this.label6 = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.tbxAbEventCount = new System.Windows.Forms.TextBox();
            this.tbxStatus = new System.Windows.Forms.TextBox();
            this.tbxNormalDays = new System.Windows.Forms.TextBox();
            this.tbxAbDays = new System.Windows.Forms.TextBox();
            this.label3 = new System.Windows.Forms.Label();
            this.tbxBlockID = new System.Windows.Forms.TextBox();
            this.gbxBlockDate = new System.Windows.Forms.GroupBox();
            this.tabTestResult = new System.Windows.Forms.TabControl();
            this.tabPageDate = new System.Windows.Forms.TabPage();
            this.lvBlockDate = new BrightIdeasSoftware.ObjectListView();
            this.olvColumn1 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumn2 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.ctxMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.tabPageFile = new System.Windows.Forms.TabPage();
            this.lvFileResult = new BrightIdeasSoftware.ObjectListView();
            this.olvColumn3 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumn5 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumn4 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumn6 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.ctxMenuFile = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miReplayFile = new System.Windows.Forms.ToolStripMenuItem();
            this.tabPageGrid = new System.Windows.Forms.TabPage();
            this.lvGridsDate = new BrightIdeasSoftware.ObjectListView();
            this.olvColumnDate = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnTestResult = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.lvGrids = new BrightIdeasSoftware.ObjectListView();
            this.olvColumnXH = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnStatus = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.tabPageCell = new System.Windows.Forms.TabPage();
            this.viewCells = new BrightIdeasSoftware.ObjectListView();
            this.colCellSn = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colCellName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colCellNum = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colCellLevel = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.ctxMenuCell = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExportCells = new System.Windows.Forms.ToolStripMenuItem();
            this.tabPageReason = new System.Windows.Forms.TabPage();
            this.viewReason = new BrightIdeasSoftware.ObjectListView();
            this.olvColSnReason = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumn8 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumn9 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumn10 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.tabPageRelate = new System.Windows.Forms.TabPage();
            this.viewRelateBlock = new BrightIdeasSoftware.ObjectListView();
            this.olvColSnRelate = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumn12 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumn13 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumn14 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.groupBox1.SuspendLayout();
            this.groupBox3.SuspendLayout();
            this.gbxBlockDate.SuspendLayout();
            this.tabTestResult.SuspendLayout();
            this.tabPageDate.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.lvBlockDate)).BeginInit();
            this.ctxMenu.SuspendLayout();
            this.tabPageFile.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.lvFileResult)).BeginInit();
            this.ctxMenuFile.SuspendLayout();
            this.tabPageGrid.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.lvGridsDate)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lvGrids)).BeginInit();
            this.tabPageCell.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.viewCells)).BeginInit();
            this.ctxMenuCell.SuspendLayout();
            this.tabPageReason.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.viewReason)).BeginInit();
            this.tabPageRelate.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.viewRelateBlock)).BeginInit();
            this.SuspendLayout();
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(20, 27);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(59, 14);
            this.label1.TabIndex = 0;
            this.label1.Text = "名    称：";
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.edtHandleUser);
            this.groupBox1.Controls.Add(this.lblHandleUser);
            this.groupBox1.Controls.Add(this.tbxReason);
            this.groupBox1.Controls.Add(this.btnModify);
            this.groupBox1.Controls.Add(this.tbxName);
            this.groupBox1.Controls.Add(this.label2);
            this.groupBox1.Controls.Add(this.label1);
            this.groupBox1.Location = new System.Drawing.Point(14, 14);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(787, 157);
            this.groupBox1.TabIndex = 1;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "信息内容";
            // 
            // edtHandleUser
            // 
            this.edtHandleUser.Location = new System.Drawing.Point(356, 22);
            this.edtHandleUser.Name = "edtHandleUser";
            this.edtHandleUser.ReadOnly = true;
            this.edtHandleUser.Size = new System.Drawing.Size(153, 22);
            this.edtHandleUser.TabIndex = 5;
            // 
            // lblHandleUser
            // 
            this.lblHandleUser.AutoSize = true;
            this.lblHandleUser.Location = new System.Drawing.Point(287, 27);
            this.lblHandleUser.Name = "lblHandleUser";
            this.lblHandleUser.Size = new System.Drawing.Size(55, 14);
            this.lblHandleUser.TabIndex = 4;
            this.lblHandleUser.Text = "处理人：";
            // 
            // tbxReason
            // 
            this.tbxReason.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.tbxReason.Location = new System.Drawing.Point(103, 55);
            this.tbxReason.Multiline = true;
            this.tbxReason.Name = "tbxReason";
            this.tbxReason.ReadOnly = true;
            this.tbxReason.Size = new System.Drawing.Size(674, 61);
            this.tbxReason.TabIndex = 1;
            // 
            // btnModify
            // 
            this.btnModify.Anchor = System.Windows.Forms.AnchorStyles.Bottom;
            this.btnModify.Location = new System.Drawing.Point(690, 122);
            this.btnModify.Name = "btnModify";
            this.btnModify.Size = new System.Drawing.Size(87, 27);
            this.btnModify.TabIndex = 3;
            this.btnModify.Text = "应用修改";
            this.btnModify.UseVisualStyleBackColor = true;
            this.btnModify.Visible = false;
            this.btnModify.Click += new System.EventHandler(this.btnModify_Click);
            // 
            // tbxName
            // 
            this.tbxName.Location = new System.Drawing.Point(103, 22);
            this.tbxName.Name = "tbxName";
            this.tbxName.ReadOnly = true;
            this.tbxName.Size = new System.Drawing.Size(153, 22);
            this.tbxName.TabIndex = 1;
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(20, 58);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(67, 14);
            this.label2.TabIndex = 0;
            this.label2.Text = "原因描述：";
            // 
            // groupBox3
            // 
            this.groupBox3.Controls.Add(this.tbxEventDes);
            this.groupBox3.Controls.Add(this.label15);
            this.groupBox3.Controls.Add(this.tbxLastTest);
            this.groupBox3.Controls.Add(this.label14);
            this.groupBox3.Controls.Add(this.label13);
            this.groupBox3.Controls.Add(this.label11);
            this.groupBox3.Controls.Add(this.tbxCellDesc);
            this.groupBox3.Controls.Add(this.tbxPlaceDesc);
            this.groupBox3.Controls.Add(this.tbxFirstAbDate);
            this.groupBox3.Controls.Add(this.label10);
            this.groupBox3.Controls.Add(this.tbxLastAbEvent);
            this.groupBox3.Controls.Add(this.label9);
            this.groupBox3.Controls.Add(this.label8);
            this.groupBox3.Controls.Add(this.label5);
            this.groupBox3.Controls.Add(this.tbxCloseDate);
            this.groupBox3.Controls.Add(this.tbxCreateDate);
            this.groupBox3.Controls.Add(this.label7);
            this.groupBox3.Controls.Add(this.label12);
            this.groupBox3.Controls.Add(this.label6);
            this.groupBox3.Controls.Add(this.label4);
            this.groupBox3.Controls.Add(this.tbxAbEventCount);
            this.groupBox3.Controls.Add(this.tbxStatus);
            this.groupBox3.Controls.Add(this.tbxNormalDays);
            this.groupBox3.Controls.Add(this.tbxAbDays);
            this.groupBox3.Controls.Add(this.label3);
            this.groupBox3.Controls.Add(this.tbxBlockID);
            this.groupBox3.Location = new System.Drawing.Point(14, 180);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.Size = new System.Drawing.Size(787, 324);
            this.groupBox3.TabIndex = 3;
            this.groupBox3.TabStop = false;
            this.groupBox3.Text = "黑点情况";
            // 
            // tbxEventDes
            // 
            this.tbxEventDes.Location = new System.Drawing.Point(103, 196);
            this.tbxEventDes.Name = "tbxEventDes";
            this.tbxEventDes.ReadOnly = true;
            this.tbxEventDes.Size = new System.Drawing.Size(675, 22);
            this.tbxEventDes.TabIndex = 11;
            // 
            // label15
            // 
            this.label15.AutoSize = true;
            this.label15.Location = new System.Drawing.Point(20, 206);
            this.label15.Name = "label15";
            this.label15.Size = new System.Drawing.Size(67, 14);
            this.label15.TabIndex = 10;
            this.label15.Text = "事件详情：";
            // 
            // tbxLastTest
            // 
            this.tbxLastTest.Location = new System.Drawing.Point(624, 111);
            this.tbxLastTest.Name = "tbxLastTest";
            this.tbxLastTest.ReadOnly = true;
            this.tbxLastTest.Size = new System.Drawing.Size(153, 22);
            this.tbxLastTest.TabIndex = 5;
            // 
            // label14
            // 
            this.label14.AutoSize = true;
            this.label14.Location = new System.Drawing.Point(20, 290);
            this.label14.Name = "label14";
            this.label14.Size = new System.Drawing.Size(67, 14);
            this.label14.TabIndex = 8;
            this.label14.Text = "相关小区：";
            // 
            // label13
            // 
            this.label13.AutoSize = true;
            this.label13.Location = new System.Drawing.Point(20, 248);
            this.label13.Name = "label13";
            this.label13.Size = new System.Drawing.Size(67, 14);
            this.label13.TabIndex = 8;
            this.label13.Text = "位置描述：";
            // 
            // label11
            // 
            this.label11.AutoSize = true;
            this.label11.Location = new System.Drawing.Point(20, 164);
            this.label11.Name = "label11";
            this.label11.Size = new System.Drawing.Size(67, 14);
            this.label11.TabIndex = 8;
            this.label11.Text = "第一异常：";
            // 
            // tbxCellDesc
            // 
            this.tbxCellDesc.Location = new System.Drawing.Point(103, 280);
            this.tbxCellDesc.Name = "tbxCellDesc";
            this.tbxCellDesc.ReadOnly = true;
            this.tbxCellDesc.Size = new System.Drawing.Size(675, 22);
            this.tbxCellDesc.TabIndex = 9;
            // 
            // tbxPlaceDesc
            // 
            this.tbxPlaceDesc.Location = new System.Drawing.Point(103, 238);
            this.tbxPlaceDesc.Name = "tbxPlaceDesc";
            this.tbxPlaceDesc.ReadOnly = true;
            this.tbxPlaceDesc.Size = new System.Drawing.Size(675, 22);
            this.tbxPlaceDesc.TabIndex = 9;
            // 
            // tbxFirstAbDate
            // 
            this.tbxFirstAbDate.Location = new System.Drawing.Point(103, 157);
            this.tbxFirstAbDate.Name = "tbxFirstAbDate";
            this.tbxFirstAbDate.ReadOnly = true;
            this.tbxFirstAbDate.Size = new System.Drawing.Size(153, 22);
            this.tbxFirstAbDate.TabIndex = 9;
            // 
            // label10
            // 
            this.label10.AutoSize = true;
            this.label10.Location = new System.Drawing.Point(273, 164);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(67, 14);
            this.label10.TabIndex = 6;
            this.label10.Text = "最后异常：";
            // 
            // tbxLastAbEvent
            // 
            this.tbxLastAbEvent.Location = new System.Drawing.Point(356, 157);
            this.tbxLastAbEvent.Name = "tbxLastAbEvent";
            this.tbxLastAbEvent.ReadOnly = true;
            this.tbxLastAbEvent.Size = new System.Drawing.Size(153, 22);
            this.tbxLastAbEvent.TabIndex = 7;
            // 
            // label9
            // 
            this.label9.AutoSize = true;
            this.label9.Location = new System.Drawing.Point(527, 118);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(91, 14);
            this.label9.TabIndex = 4;
            this.label9.Text = "最后测试时间：";
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(273, 118);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(67, 14);
            this.label8.TabIndex = 4;
            this.label8.Text = "关闭时间：";
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(20, 118);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(67, 14);
            this.label5.TabIndex = 4;
            this.label5.Text = "创建时间：";
            // 
            // tbxCloseDate
            // 
            this.tbxCloseDate.Location = new System.Drawing.Point(356, 111);
            this.tbxCloseDate.Name = "tbxCloseDate";
            this.tbxCloseDate.ReadOnly = true;
            this.tbxCloseDate.Size = new System.Drawing.Size(153, 22);
            this.tbxCloseDate.TabIndex = 5;
            // 
            // tbxCreateDate
            // 
            this.tbxCreateDate.Location = new System.Drawing.Point(103, 111);
            this.tbxCreateDate.Name = "tbxCreateDate";
            this.tbxCreateDate.ReadOnly = true;
            this.tbxCreateDate.Size = new System.Drawing.Size(153, 22);
            this.tbxCreateDate.TabIndex = 5;
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(527, 72);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(79, 14);
            this.label7.TabIndex = 2;
            this.label7.Text = "异常事件数：";
            // 
            // label12
            // 
            this.label12.AutoSize = true;
            this.label12.Location = new System.Drawing.Point(273, 30);
            this.label12.Name = "label12";
            this.label12.Size = new System.Drawing.Size(67, 14);
            this.label12.TabIndex = 2;
            this.label12.Text = "当前状态：";
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(273, 72);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(67, 14);
            this.label6.TabIndex = 2;
            this.label6.Text = "正常天数：";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(20, 72);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(67, 14);
            this.label4.TabIndex = 2;
            this.label4.Text = "问题天数：";
            // 
            // tbxAbEventCount
            // 
            this.tbxAbEventCount.Location = new System.Drawing.Point(624, 65);
            this.tbxAbEventCount.Name = "tbxAbEventCount";
            this.tbxAbEventCount.ReadOnly = true;
            this.tbxAbEventCount.Size = new System.Drawing.Size(153, 22);
            this.tbxAbEventCount.TabIndex = 3;
            // 
            // tbxStatus
            // 
            this.tbxStatus.Location = new System.Drawing.Point(356, 23);
            this.tbxStatus.Name = "tbxStatus";
            this.tbxStatus.ReadOnly = true;
            this.tbxStatus.Size = new System.Drawing.Size(153, 22);
            this.tbxStatus.TabIndex = 3;
            // 
            // tbxNormalDays
            // 
            this.tbxNormalDays.Location = new System.Drawing.Point(356, 65);
            this.tbxNormalDays.Name = "tbxNormalDays";
            this.tbxNormalDays.ReadOnly = true;
            this.tbxNormalDays.Size = new System.Drawing.Size(153, 22);
            this.tbxNormalDays.TabIndex = 3;
            // 
            // tbxAbDays
            // 
            this.tbxAbDays.Location = new System.Drawing.Point(103, 65);
            this.tbxAbDays.Name = "tbxAbDays";
            this.tbxAbDays.ReadOnly = true;
            this.tbxAbDays.Size = new System.Drawing.Size(153, 22);
            this.tbxAbDays.TabIndex = 3;
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(20, 30);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(67, 14);
            this.label3.TabIndex = 0;
            this.label3.Text = "黑点编号：";
            // 
            // tbxBlockID
            // 
            this.tbxBlockID.Location = new System.Drawing.Point(103, 23);
            this.tbxBlockID.Name = "tbxBlockID";
            this.tbxBlockID.ReadOnly = true;
            this.tbxBlockID.Size = new System.Drawing.Size(153, 22);
            this.tbxBlockID.TabIndex = 1;
            // 
            // gbxBlockDate
            // 
            this.gbxBlockDate.Controls.Add(this.tabTestResult);
            this.gbxBlockDate.Location = new System.Drawing.Point(809, 15);
            this.gbxBlockDate.Name = "gbxBlockDate";
            this.gbxBlockDate.Size = new System.Drawing.Size(369, 490);
            this.gbxBlockDate.TabIndex = 9;
            this.gbxBlockDate.TabStop = false;
            this.gbxBlockDate.Text = "黑点测试情况";
            // 
            // tabTestResult
            // 
            this.tabTestResult.ContextMenuStrip = this.ctxMenu;
            this.tabTestResult.Controls.Add(this.tabPageDate);
            this.tabTestResult.Controls.Add(this.tabPageFile);
            this.tabTestResult.Controls.Add(this.tabPageGrid);
            this.tabTestResult.Controls.Add(this.tabPageCell);
            this.tabTestResult.Controls.Add(this.tabPageReason);
            this.tabTestResult.Controls.Add(this.tabPageRelate);
            this.tabTestResult.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabTestResult.Location = new System.Drawing.Point(3, 18);
            this.tabTestResult.Name = "tabTestResult";
            this.tabTestResult.SelectedIndex = 0;
            this.tabTestResult.Size = new System.Drawing.Size(363, 469);
            this.tabTestResult.TabIndex = 10;
            // 
            // tabPageDate
            // 
            this.tabPageDate.Controls.Add(this.lvBlockDate);
            this.tabPageDate.Location = new System.Drawing.Point(4, 23);
            this.tabPageDate.Name = "tabPageDate";
            this.tabPageDate.Padding = new System.Windows.Forms.Padding(3);
            this.tabPageDate.Size = new System.Drawing.Size(355, 442);
            this.tabPageDate.TabIndex = 0;
            this.tabPageDate.Text = "测试天";
            this.tabPageDate.UseVisualStyleBackColor = true;
            // 
            // lvBlockDate
            // 
            this.lvBlockDate.AllColumns.Add(this.olvColumn1);
            this.lvBlockDate.AllColumns.Add(this.olvColumn2);
            this.lvBlockDate.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumn1,
            this.olvColumn2});
            this.lvBlockDate.ContextMenuStrip = this.ctxMenu;
            this.lvBlockDate.Cursor = System.Windows.Forms.Cursors.Default;
            this.lvBlockDate.Dock = System.Windows.Forms.DockStyle.Fill;
            this.lvBlockDate.FullRowSelect = true;
            this.lvBlockDate.GridLines = true;
            this.lvBlockDate.Location = new System.Drawing.Point(3, 3);
            this.lvBlockDate.MultiSelect = false;
            this.lvBlockDate.Name = "lvBlockDate";
            this.lvBlockDate.ShowGroups = false;
            this.lvBlockDate.Size = new System.Drawing.Size(349, 436);
            this.lvBlockDate.TabIndex = 9;
            this.lvBlockDate.UseCompatibleStateImageBehavior = false;
            this.lvBlockDate.View = System.Windows.Forms.View.Details;
            // 
            // olvColumn1
            // 
            this.olvColumn1.AspectName = "Date";
            this.olvColumn1.HeaderFont = null;
            this.olvColumn1.Text = "测试时间";
            this.olvColumn1.Width = 171;
            // 
            // olvColumn2
            // 
            this.olvColumn2.AspectName = "TestResult";
            this.olvColumn2.HeaderFont = null;
            this.olvColumn2.Text = "结果";
            this.olvColumn2.Width = 144;
            // 
            // ctxMenu
            // 
            this.ctxMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExportExcel});
            this.ctxMenu.Name = "ctxMenu";
            this.ctxMenu.Size = new System.Drawing.Size(130, 26);
            // 
            // miExportExcel
            // 
            this.miExportExcel.Name = "miExportExcel";
            this.miExportExcel.Size = new System.Drawing.Size(129, 22);
            this.miExportExcel.Text = "导出Excel";
            this.miExportExcel.Click += new System.EventHandler(this.miExportExcel_Click);
            // 
            // tabPageFile
            // 
            this.tabPageFile.Controls.Add(this.lvFileResult);
            this.tabPageFile.Location = new System.Drawing.Point(4, 23);
            this.tabPageFile.Name = "tabPageFile";
            this.tabPageFile.Padding = new System.Windows.Forms.Padding(3);
            this.tabPageFile.Size = new System.Drawing.Size(355, 442);
            this.tabPageFile.TabIndex = 1;
            this.tabPageFile.Text = "测试文件";
            this.tabPageFile.UseVisualStyleBackColor = true;
            // 
            // lvFileResult
            // 
            this.lvFileResult.AllColumns.Add(this.olvColumn3);
            this.lvFileResult.AllColumns.Add(this.olvColumn5);
            this.lvFileResult.AllColumns.Add(this.olvColumn4);
            this.lvFileResult.AllColumns.Add(this.olvColumn6);
            this.lvFileResult.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumn3,
            this.olvColumn5,
            this.olvColumn4,
            this.olvColumn6});
            this.lvFileResult.ContextMenuStrip = this.ctxMenuFile;
            this.lvFileResult.Cursor = System.Windows.Forms.Cursors.Default;
            this.lvFileResult.Dock = System.Windows.Forms.DockStyle.Fill;
            this.lvFileResult.FullRowSelect = true;
            this.lvFileResult.GridLines = true;
            this.lvFileResult.Location = new System.Drawing.Point(3, 3);
            this.lvFileResult.MultiSelect = false;
            this.lvFileResult.Name = "lvFileResult";
            this.lvFileResult.ShowGroups = false;
            this.lvFileResult.Size = new System.Drawing.Size(349, 436);
            this.lvFileResult.TabIndex = 10;
            this.lvFileResult.UseCompatibleStateImageBehavior = false;
            this.lvFileResult.View = System.Windows.Forms.View.Details;
            // 
            // olvColumn3
            // 
            this.olvColumn3.AspectName = "Date";
            this.olvColumn3.HeaderFont = null;
            this.olvColumn3.Text = "测试时间";
            this.olvColumn3.Width = 89;
            // 
            // olvColumn5
            // 
            this.olvColumn5.AspectName = "Name";
            this.olvColumn5.HeaderFont = null;
            this.olvColumn5.Text = "文件";
            this.olvColumn5.Width = 73;
            // 
            // olvColumn4
            // 
            this.olvColumn4.AspectName = "ResultDesc";
            this.olvColumn4.HeaderFont = null;
            this.olvColumn4.Text = "结果";
            this.olvColumn4.Width = 79;
            // 
            // olvColumn6
            // 
            this.olvColumn6.AspectName = "NormalCount";
            this.olvColumn6.HeaderFont = null;
            this.olvColumn6.Text = "正常次数";
            this.olvColumn6.Width = 101;
            // 
            // ctxMenuFile
            // 
            this.ctxMenuFile.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miReplayFile});
            this.ctxMenuFile.Name = "ctxMenuFile";
            this.ctxMenuFile.Size = new System.Drawing.Size(134, 26);
            // 
            // miReplayFile
            // 
            this.miReplayFile.Name = "miReplayFile";
            this.miReplayFile.Size = new System.Drawing.Size(133, 22);
            this.miReplayFile.Text = "回放文件...";
            this.miReplayFile.Click += new System.EventHandler(this.miReplayFile_Click);
            // 
            // tabPageGrid
            // 
            this.tabPageGrid.Controls.Add(this.lvGridsDate);
            this.tabPageGrid.Controls.Add(this.lvGrids);
            this.tabPageGrid.Location = new System.Drawing.Point(4, 23);
            this.tabPageGrid.Name = "tabPageGrid";
            this.tabPageGrid.Padding = new System.Windows.Forms.Padding(3);
            this.tabPageGrid.Size = new System.Drawing.Size(355, 442);
            this.tabPageGrid.TabIndex = 2;
            this.tabPageGrid.Text = "测试栅格";
            this.tabPageGrid.UseVisualStyleBackColor = true;
            // 
            // lvGridsDate
            // 
            this.lvGridsDate.AllColumns.Add(this.olvColumnDate);
            this.lvGridsDate.AllColumns.Add(this.olvColumnTestResult);
            this.lvGridsDate.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnDate,
            this.olvColumnTestResult});
            this.lvGridsDate.Cursor = System.Windows.Forms.Cursors.Default;
            this.lvGridsDate.FullRowSelect = true;
            this.lvGridsDate.GridLines = true;
            this.lvGridsDate.Location = new System.Drawing.Point(156, 3);
            this.lvGridsDate.MultiSelect = false;
            this.lvGridsDate.Name = "lvGridsDate";
            this.lvGridsDate.ShowGroups = false;
            this.lvGridsDate.Size = new System.Drawing.Size(193, 447);
            this.lvGridsDate.TabIndex = 12;
            this.lvGridsDate.UseCompatibleStateImageBehavior = false;
            this.lvGridsDate.View = System.Windows.Forms.View.Details;
            // 
            // olvColumnDate
            // 
            this.olvColumnDate.AspectName = "Date";
            this.olvColumnDate.HeaderFont = null;
            this.olvColumnDate.Text = "测试时间";
            this.olvColumnDate.Width = 100;
            // 
            // olvColumnTestResult
            // 
            this.olvColumnTestResult.AspectName = "TestResult";
            this.olvColumnTestResult.HeaderFont = null;
            this.olvColumnTestResult.Text = "结果";
            // 
            // lvGrids
            // 
            this.lvGrids.AllColumns.Add(this.olvColumnXH);
            this.lvGrids.AllColumns.Add(this.olvColumnStatus);
            this.lvGrids.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnXH,
            this.olvColumnStatus});
            this.lvGrids.Cursor = System.Windows.Forms.Cursors.Default;
            this.lvGrids.FullRowSelect = true;
            this.lvGrids.GridLines = true;
            this.lvGrids.Location = new System.Drawing.Point(6, 3);
            this.lvGrids.MultiSelect = false;
            this.lvGrids.Name = "lvGrids";
            this.lvGrids.ShowGroups = false;
            this.lvGrids.Size = new System.Drawing.Size(144, 447);
            this.lvGrids.TabIndex = 11;
            this.lvGrids.UseCompatibleStateImageBehavior = false;
            this.lvGrids.View = System.Windows.Forms.View.Details;
            this.lvGrids.SelectedIndexChanged += new System.EventHandler(this.lvGrids_SelectedIndexChanged);
            this.lvGrids.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.lvGrids_MouseDoubleClick);
            // 
            // olvColumnXH
            // 
            this.olvColumnXH.HeaderFont = null;
            this.olvColumnXH.Text = "序号";
            // 
            // olvColumnStatus
            // 
            this.olvColumnStatus.AspectName = "Status";
            this.olvColumnStatus.HeaderFont = null;
            this.olvColumnStatus.Text = "状态";
            // 
            // tabPageCell
            // 
            this.tabPageCell.Controls.Add(this.viewCells);
            this.tabPageCell.Location = new System.Drawing.Point(4, 23);
            this.tabPageCell.Name = "tabPageCell";
            this.tabPageCell.Padding = new System.Windows.Forms.Padding(3);
            this.tabPageCell.Size = new System.Drawing.Size(355, 442);
            this.tabPageCell.TabIndex = 3;
            this.tabPageCell.Text = "需调整小区";
            this.tabPageCell.UseVisualStyleBackColor = true;
            // 
            // viewCells
            // 
            this.viewCells.AllColumns.Add(this.colCellSn);
            this.viewCells.AllColumns.Add(this.colCellName);
            this.viewCells.AllColumns.Add(this.colCellNum);
            this.viewCells.AllColumns.Add(this.colCellLevel);
            this.viewCells.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.colCellSn,
            this.colCellName,
            this.colCellNum,
            this.colCellLevel});
            this.viewCells.ContextMenuStrip = this.ctxMenuCell;
            this.viewCells.Cursor = System.Windows.Forms.Cursors.Default;
            this.viewCells.Dock = System.Windows.Forms.DockStyle.Fill;
            this.viewCells.FullRowSelect = true;
            this.viewCells.GridLines = true;
            this.viewCells.Location = new System.Drawing.Point(3, 3);
            this.viewCells.MultiSelect = false;
            this.viewCells.Name = "viewCells";
            this.viewCells.ShowGroups = false;
            this.viewCells.Size = new System.Drawing.Size(349, 436);
            this.viewCells.TabIndex = 11;
            this.viewCells.UseCompatibleStateImageBehavior = false;
            this.viewCells.View = System.Windows.Forms.View.Details;
            this.viewCells.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.viewCells_MouseDoubleClick);
            // 
            // colCellSn
            // 
            this.colCellSn.HeaderFont = null;
            this.colCellSn.IsEditable = false;
            this.colCellSn.Text = "序号";
            // 
            // colCellName
            // 
            this.colCellName.AspectName = "Name";
            this.colCellName.HeaderFont = null;
            this.colCellName.IsEditable = false;
            this.colCellName.Text = "小区";
            this.colCellName.Width = 100;
            // 
            // colCellNum
            // 
            this.colCellNum.AspectName = "Count";
            this.colCellNum.HeaderFont = null;
            this.colCellNum.IsEditable = false;
            this.colCellNum.Text = "涉及次数";
            // 
            // colCellLevel
            // 
            this.colCellLevel.AspectName = "SignalLevel";
            this.colCellLevel.HeaderFont = null;
            this.colCellLevel.IsEditable = false;
            this.colCellLevel.Text = "平均场强(dBm)";
            // 
            // ctxMenuCell
            // 
            this.ctxMenuCell.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExportCells});
            this.ctxMenuCell.Name = "ctxMenu";
            this.ctxMenuCell.Size = new System.Drawing.Size(130, 26);
            // 
            // miExportCells
            // 
            this.miExportCells.Name = "miExportCells";
            this.miExportCells.Size = new System.Drawing.Size(129, 22);
            this.miExportCells.Text = "导出Excel";
            this.miExportCells.Click += new System.EventHandler(this.miExportCells_Click);
            // 
            // tabPageReason
            // 
            this.tabPageReason.Controls.Add(this.viewReason);
            this.tabPageReason.Location = new System.Drawing.Point(4, 23);
            this.tabPageReason.Name = "tabPageReason";
            this.tabPageReason.Padding = new System.Windows.Forms.Padding(3);
            this.tabPageReason.Size = new System.Drawing.Size(355, 442);
            this.tabPageReason.TabIndex = 4;
            this.tabPageReason.Text = "原因及比例";
            this.tabPageReason.UseVisualStyleBackColor = true;
            // 
            // viewReason
            // 
            this.viewReason.AllColumns.Add(this.olvColSnReason);
            this.viewReason.AllColumns.Add(this.olvColumn8);
            this.viewReason.AllColumns.Add(this.olvColumn9);
            this.viewReason.AllColumns.Add(this.olvColumn10);
            this.viewReason.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColSnReason,
            this.olvColumn8,
            this.olvColumn9,
            this.olvColumn10});
            this.viewReason.ContextMenuStrip = this.ctxMenu;
            this.viewReason.Cursor = System.Windows.Forms.Cursors.Default;
            this.viewReason.Dock = System.Windows.Forms.DockStyle.Fill;
            this.viewReason.FullRowSelect = true;
            this.viewReason.GridLines = true;
            this.viewReason.Location = new System.Drawing.Point(3, 3);
            this.viewReason.MultiSelect = false;
            this.viewReason.Name = "viewReason";
            this.viewReason.ShowGroups = false;
            this.viewReason.Size = new System.Drawing.Size(349, 436);
            this.viewReason.TabIndex = 12;
            this.viewReason.UseCompatibleStateImageBehavior = false;
            this.viewReason.View = System.Windows.Forms.View.Details;
            // 
            // olvColSnReason
            // 
            this.olvColSnReason.HeaderFont = null;
            this.olvColSnReason.IsEditable = false;
            this.olvColSnReason.Text = "序号";
            // 
            // olvColumn8
            // 
            this.olvColumn8.AspectName = "Reason";
            this.olvColumn8.HeaderFont = null;
            this.olvColumn8.IsEditable = false;
            this.olvColumn8.Text = "原因分类";
            this.olvColumn8.Width = 100;
            // 
            // olvColumn9
            // 
            this.olvColumn9.AspectName = "Count";
            this.olvColumn9.HeaderFont = null;
            this.olvColumn9.IsEditable = false;
            this.olvColumn9.Text = "质差采样点数";
            this.olvColumn9.Width = 100;
            // 
            // olvColumn10
            // 
            this.olvColumn10.AspectName = "Rate";
            this.olvColumn10.HeaderFont = null;
            this.olvColumn10.IsEditable = false;
            this.olvColumn10.Text = "百分比（%）";
            // 
            // tabPageRelate
            // 
            this.tabPageRelate.Controls.Add(this.viewRelateBlock);
            this.tabPageRelate.Location = new System.Drawing.Point(4, 23);
            this.tabPageRelate.Name = "tabPageRelate";
            this.tabPageRelate.Padding = new System.Windows.Forms.Padding(3);
            this.tabPageRelate.Size = new System.Drawing.Size(355, 442);
            this.tabPageRelate.TabIndex = 5;
            this.tabPageRelate.Text = "关联黑点";
            this.tabPageRelate.UseVisualStyleBackColor = true;
            // 
            // viewRelateBlock
            // 
            this.viewRelateBlock.AllColumns.Add(this.olvColSnRelate);
            this.viewRelateBlock.AllColumns.Add(this.olvColumn12);
            this.viewRelateBlock.AllColumns.Add(this.olvColumn13);
            this.viewRelateBlock.AllColumns.Add(this.olvColumn14);
            this.viewRelateBlock.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColSnRelate,
            this.olvColumn12,
            this.olvColumn13,
            this.olvColumn14});
            this.viewRelateBlock.Cursor = System.Windows.Forms.Cursors.Default;
            this.viewRelateBlock.Dock = System.Windows.Forms.DockStyle.Fill;
            this.viewRelateBlock.FullRowSelect = true;
            this.viewRelateBlock.GridLines = true;
            this.viewRelateBlock.Location = new System.Drawing.Point(3, 3);
            this.viewRelateBlock.MultiSelect = false;
            this.viewRelateBlock.Name = "viewRelateBlock";
            this.viewRelateBlock.ShowGroups = false;
            this.viewRelateBlock.Size = new System.Drawing.Size(349, 436);
            this.viewRelateBlock.TabIndex = 13;
            this.viewRelateBlock.UseCompatibleStateImageBehavior = false;
            this.viewRelateBlock.View = System.Windows.Forms.View.Details;
            // 
            // olvColSnRelate
            // 
            this.olvColSnRelate.HeaderFont = null;
            this.olvColSnRelate.IsEditable = false;
            this.olvColSnRelate.Text = "序号";
            // 
            // olvColumn12
            // 
            this.olvColumn12.AspectName = "BlockID";
            this.olvColumn12.HeaderFont = null;
            this.olvColumn12.IsEditable = false;
            this.olvColumn12.Text = "黑点ID";
            // 
            // olvColumn13
            // 
            this.olvColumn13.AspectName = "Distance";
            this.olvColumn13.HeaderFont = null;
            this.olvColumn13.IsEditable = false;
            this.olvColumn13.Text = "距离（米）";
            this.olvColumn13.Width = 100;
            // 
            // olvColumn14
            // 
            this.olvColumn14.AspectName = "RelateBlockType";
            this.olvColumn14.HeaderFont = null;
            this.olvColumn14.IsEditable = false;
            this.olvColumn14.Text = "黑点类别";
            this.olvColumn14.Width = 100;
            // 
            // BlackBlockInfoDlg
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1190, 517);
            this.Controls.Add(this.gbxBlockDate);
            this.Controls.Add(this.groupBox3);
            this.Controls.Add(this.groupBox1);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "BlackBlockInfoDlg";
            this.Text = "问题黑点信息";
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.groupBox3.ResumeLayout(false);
            this.groupBox3.PerformLayout();
            this.gbxBlockDate.ResumeLayout(false);
            this.tabTestResult.ResumeLayout(false);
            this.tabPageDate.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.lvBlockDate)).EndInit();
            this.ctxMenu.ResumeLayout(false);
            this.tabPageFile.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.lvFileResult)).EndInit();
            this.ctxMenuFile.ResumeLayout(false);
            this.tabPageGrid.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.lvGridsDate)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lvGrids)).EndInit();
            this.tabPageCell.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.viewCells)).EndInit();
            this.ctxMenuCell.ResumeLayout(false);
            this.tabPageReason.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.viewReason)).EndInit();
            this.tabPageRelate.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.viewRelateBlock)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.TextBox tbxName;
        private System.Windows.Forms.TextBox tbxReason;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Button btnModify;
        private System.Windows.Forms.GroupBox groupBox3;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.TextBox tbxBlockID;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.TextBox tbxAbDays;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.TextBox tbxCreateDate;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.TextBox tbxAbEventCount;
        private System.Windows.Forms.TextBox tbxNormalDays;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.TextBox tbxCloseDate;
        private System.Windows.Forms.Label label11;
        private System.Windows.Forms.TextBox tbxFirstAbDate;
        private System.Windows.Forms.Label label10;
        private System.Windows.Forms.TextBox tbxLastAbEvent;
        private System.Windows.Forms.Label label9;
        private System.Windows.Forms.TextBox tbxLastTest;
        private System.Windows.Forms.Label label12;
        private System.Windows.Forms.TextBox tbxStatus;
        private System.Windows.Forms.Label label14;
        private System.Windows.Forms.Label label13;
        private System.Windows.Forms.TextBox tbxCellDesc;
        private System.Windows.Forms.TextBox tbxPlaceDesc;
        private System.Windows.Forms.TextBox tbxEventDes;
        private System.Windows.Forms.Label label15;
        private System.Windows.Forms.GroupBox gbxBlockDate;
        private BrightIdeasSoftware.ObjectListView lvBlockDate;
        private BrightIdeasSoftware.OLVColumn olvColumn1;
        private BrightIdeasSoftware.OLVColumn olvColumn2;
        private BrightIdeasSoftware.ObjectListView lvGridsDate;
        private BrightIdeasSoftware.OLVColumn olvColumnDate;
        private BrightIdeasSoftware.OLVColumn olvColumnTestResult;
        private BrightIdeasSoftware.ObjectListView lvGrids;
        private BrightIdeasSoftware.OLVColumn olvColumnXH;
        private BrightIdeasSoftware.OLVColumn olvColumnStatus;
        private System.Windows.Forms.ContextMenuStrip ctxMenu;
        private System.Windows.Forms.ToolStripMenuItem miExportExcel;
        private System.Windows.Forms.TextBox edtHandleUser;
        private System.Windows.Forms.Label lblHandleUser;
        private System.Windows.Forms.TabControl tabTestResult;
        private System.Windows.Forms.TabPage tabPageDate;
        private System.Windows.Forms.TabPage tabPageFile;
        private System.Windows.Forms.TabPage tabPageGrid;
        private BrightIdeasSoftware.ObjectListView lvFileResult;
        private BrightIdeasSoftware.OLVColumn olvColumn3;
        private BrightIdeasSoftware.OLVColumn olvColumn5;
        private BrightIdeasSoftware.OLVColumn olvColumn4;
        private System.Windows.Forms.ContextMenuStrip ctxMenuFile;
        private System.Windows.Forms.ToolStripMenuItem miReplayFile;
        private BrightIdeasSoftware.OLVColumn olvColumn6;
        private System.Windows.Forms.TabPage tabPageCell;
        private BrightIdeasSoftware.ObjectListView viewCells;
        private BrightIdeasSoftware.OLVColumn colCellSn;
        private BrightIdeasSoftware.OLVColumn colCellName;
        private BrightIdeasSoftware.OLVColumn colCellNum;
        private BrightIdeasSoftware.OLVColumn colCellLevel;
        private System.Windows.Forms.ContextMenuStrip ctxMenuCell;
        private System.Windows.Forms.ToolStripMenuItem miExportCells;
        private System.Windows.Forms.TabPage tabPageReason;
        private BrightIdeasSoftware.ObjectListView viewReason;
        private BrightIdeasSoftware.OLVColumn olvColSnReason;
        private BrightIdeasSoftware.OLVColumn olvColumn8;
        private BrightIdeasSoftware.OLVColumn olvColumn9;
        private BrightIdeasSoftware.OLVColumn olvColumn10;
        private System.Windows.Forms.TabPage tabPageRelate;
        private BrightIdeasSoftware.ObjectListView viewRelateBlock;
        private BrightIdeasSoftware.OLVColumn olvColSnRelate;
        private BrightIdeasSoftware.OLVColumn olvColumn12;
        private BrightIdeasSoftware.OLVColumn olvColumn13;
        private BrightIdeasSoftware.OLVColumn olvColumn14;
    }
}