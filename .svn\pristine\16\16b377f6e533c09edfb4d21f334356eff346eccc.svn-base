﻿namespace MasterCom.RAMS.Func
{
    partial class ZTHandoverTimesStatForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.toolStrip1 = new System.Windows.Forms.ToolStrip();
            this.toolStripLabel2 = new System.Windows.Forms.ToolStripLabel();
            this.lblTotTimes = new System.Windows.Forms.ToolStripLabel();
            this.objectListView = new BrightIdeasSoftware.ObjectListView();
            this.olvColumnTimeRange = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnHandoverTimes = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnHandoverPercent = new BrightIdeasSoftware.OLVColumn();
            this.contextMenuStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miOutputToExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripLabel1 = new System.Windows.Forms.ToolStripLabel();
            this.toolStrip1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.objectListView)).BeginInit();
            this.contextMenuStrip.SuspendLayout();
            this.SuspendLayout();
            // 
            // toolStrip1
            // 
            this.toolStrip1.BackColor = System.Drawing.SystemColors.Control;
            this.toolStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.toolStripLabel1,
            this.toolStripLabel2,
            this.lblTotTimes});
            this.toolStrip1.Location = new System.Drawing.Point(0, 0);
            this.toolStrip1.Name = "toolStrip1";
            this.toolStrip1.Size = new System.Drawing.Size(480, 25);
            this.toolStrip1.TabIndex = 6;
            this.toolStrip1.Text = "toolStrip1";
            // 
            // toolStripLabel2
            // 
            this.toolStripLabel2.Name = "toolStripLabel2";
            this.toolStripLabel2.Size = new System.Drawing.Size(107, 22);
            this.toolStripLabel2.Text = "      总切换次数:";
            // 
            // lblTotTimes
            // 
            this.lblTotTimes.ForeColor = System.Drawing.Color.Red;
            this.lblTotTimes.Name = "lblTotTimes";
            this.lblTotTimes.Size = new System.Drawing.Size(0, 22);
            // 
            // objectListView
            // 
            this.objectListView.AllColumns.Add(this.olvColumnTimeRange);
            this.objectListView.AllColumns.Add(this.olvColumnHandoverTimes);
            this.objectListView.AllColumns.Add(this.olvColumnHandoverPercent);
            this.objectListView.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnTimeRange,
            this.olvColumnHandoverTimes,
            this.olvColumnHandoverPercent});
            this.objectListView.ContextMenuStrip = this.contextMenuStrip;
            this.objectListView.Cursor = System.Windows.Forms.Cursors.Default;
            this.objectListView.Dock = System.Windows.Forms.DockStyle.Fill;
            this.objectListView.FullRowSelect = true;
            this.objectListView.GridLines = true;
            this.objectListView.Location = new System.Drawing.Point(0, 25);
            this.objectListView.MultiSelect = false;
            this.objectListView.Name = "objectListView";
            this.objectListView.ShowGroups = false;
            this.objectListView.Size = new System.Drawing.Size(480, 262);
            this.objectListView.TabIndex = 7;
            this.objectListView.UseCompatibleStateImageBehavior = false;
            this.objectListView.View = System.Windows.Forms.View.Details;
            // 
            // olvColumnTimeRange
            // 
            this.olvColumnTimeRange.AspectName = "timeRange";
            this.olvColumnTimeRange.HeaderFont = null;
            this.olvColumnTimeRange.Text = "切换间隔";
            this.olvColumnTimeRange.Width = 90;
            // 
            // olvColumnHandoverTimes
            // 
            this.olvColumnHandoverTimes.AspectName = "handoverTimes";
            this.olvColumnHandoverTimes.HeaderFont = null;
            this.olvColumnHandoverTimes.Text = "切换次数";
            this.olvColumnHandoverTimes.Width = 90;
            // 
            // olvColumnHandoverPercent
            // 
            this.olvColumnHandoverPercent.AspectName = "HandoverPercent";
            this.olvColumnHandoverPercent.HeaderFont = null;
            this.olvColumnHandoverPercent.Text = "切换数占比";
            this.olvColumnHandoverPercent.Width = 90;
            // 
            // contextMenuStrip
            // 
            this.contextMenuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miOutputToExcel});
            this.contextMenuStrip.Name = "contextMenuStrip";
            this.contextMenuStrip.Size = new System.Drawing.Size(125, 26);
            // 
            // miOutputToExcel
            // 
            this.miOutputToExcel.Name = "miOutputToExcel";
            this.miOutputToExcel.Size = new System.Drawing.Size(124, 22);
            this.miOutputToExcel.Text = "导出Excel";
            this.miOutputToExcel.Click += new System.EventHandler(this.miOutputToExcel_Click);
            // 
            // toolStripLabel1
            // 
            this.toolStripLabel1.Name = "toolStripLabel1";
            this.toolStripLabel1.Size = new System.Drawing.Size(83, 22);
            this.toolStripLabel1.Text = "间隔计算门限:";
            // 
            // ZTHandoverTimesStatForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(480, 287);
            this.Controls.Add(this.objectListView);
            this.Controls.Add(this.toolStrip1);
            this.Name = "ZTHandoverTimesStatForm";
            this.Text = "切换间隔统计";
            this.toolStrip1.ResumeLayout(false);
            this.toolStrip1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.objectListView)).EndInit();
            this.contextMenuStrip.ResumeLayout(false);
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.ToolStrip toolStrip1;
        private BrightIdeasSoftware.ObjectListView objectListView;
        private BrightIdeasSoftware.OLVColumn olvColumnHandoverTimes;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip;
        private System.Windows.Forms.ToolStripMenuItem miOutputToExcel;
        private System.Windows.Forms.ToolStripLabel toolStripLabel2;
        private System.Windows.Forms.ToolStripLabel lblTotTimes;
        private BrightIdeasSoftware.OLVColumn olvColumnTimeRange;
        private BrightIdeasSoftware.OLVColumn olvColumnHandoverPercent;
        private System.Windows.Forms.ToolStripLabel toolStripLabel1;
    }
}