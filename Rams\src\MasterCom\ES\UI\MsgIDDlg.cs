using System;
using System.Collections.Generic;
using System.Windows.Forms;

using MasterCom.Util;

namespace MasterCom.ES.UI
{
    public partial class MsgIDDlg : BaseFormStyle
    {
        public MsgIDDlg()
        {
            InitializeComponent();
            initFillMsg();
            lstViewMsg.ListViewItemSorter = new ListViewSorter(lstViewMsg);
        }

        private void initFillMsg()
        {
            lstViewMsg.Items.Clear();
            foreach (int id in MsgID2NameDic.Keys)
            {
                string name = MsgID2NameDic[id];
                ListViewItem lvi = new ListViewItem();
                lvi.Text = id.ToString();
                lvi.Tag = id;
                lvi.SubItems.Add(name);
                lvi.SubItems[lvi.SubItems.Count - 1].Tag = name;
                lstViewMsg.Items.Add(lvi);
            }
        }
        private Dictionary<int, string> msgID2NameDic = null;
        public Dictionary<int, string> MsgID2NameDic
        {
            get
            {
                if (msgID2NameDic == null)
                {
                    FillMsgDic("gsmtd");
                }
                return msgID2NameDic;
            }
        }
        public void FillMsgDic(string stack)
        {
            #region init all msg names
            msgID2NameDic = new Dictionary<int, string>();
            if ("cdma".Equals(stack) || "all".Equals(stack))
            {
                //cdma add begin
                msgID2NameDic[0x10040100] = "Registration Message";
                msgID2NameDic[0x10040208] = "AC Order->Base Station Challenge";
                msgID2NameDic[0x1004020C] = "AC Order->SSD Update Confirmation";
                msgID2NameDic[0x1004020D] = "AC Order->SSD Update Rejection";
                msgID2NameDic[0x10040240] = "AC Order->Mobile Station Acknowledgement";
                msgID2NameDic[0x10040254] = "AC Order->Release";
                msgID2NameDic[0x10040278] = "AC Order->Local Control Response";
                msgID2NameDic[0x1004027C] = "AC Order->Mobile Station Reject";
                msgID2NameDic[0x10040400] = "Origination Message";
                msgID2NameDic[0x10040500] = "Page Response Message";
                msgID2NameDic[0x10040900] = "PACA Cancel Message";
                msgID2NameDic[0x10040A00] = "Extended Status Response Message";
                
                msgID2NameDic[0x10050108] = "RTC Order->Base Station Challenge";
                msgID2NameDic[0x1005010C] = "RTC Order->SSD Update Confirmation";
                msgID2NameDic[0x1005010D] = "RTC Order->SSD Update Rejection";
                msgID2NameDic[0x10050114] = "RTC Order->Parameter Update Confirmation";
                msgID2NameDic[0x1005012C] = "RTC Order->Request Wide Analog Service";
                msgID2NameDic[0x1005012D] = "RTC Order->Request Narrow Analog Service";
                msgID2NameDic[0x1005012E] = "RTC Order->Request Analog Service";
                msgID2NameDic[0x10050140] = "RTC Order->Mobile Station Acknowledgement";
                msgID2NameDic[0x1005014C] = "RTC Order->Service Option Request";
                msgID2NameDic[0x10050150] = "RTC Order->Service Option Response";
                msgID2NameDic[0x10050154] = "RTC Order->Release";
                msgID2NameDic[0x1005015C] = "RTC Order->Long Code Transition Request";
                msgID2NameDic[0x1005015E] = "RTC Order->Long Code Transition Response";
                msgID2NameDic[0x10050160] = "RTC Order->Connect";
                msgID2NameDic[0x10050164] = "RTC Order->Continuous DTMF Tone";
                msgID2NameDic[0x10050165] = "RTC Order->Stop Continuous DTMF Tone";
                msgID2NameDic[0x10050174] = "RTC Order->Service Option Control";
                msgID2NameDic[0x10050178] = "RTC Order->Local Control Response";
                msgID2NameDic[0x1005017C] = "RTC Order->Mobile Station Reject";
                msgID2NameDic[0x10050200] = "Authentication Challenge Response Message";
                msgID2NameDic[0x10050400] = "Data Burst Message";
                msgID2NameDic[0x10050500] = "Pilot Strength Measurement Message";
                msgID2NameDic[0x10050600] = "Power Measurement Report Message";
                msgID2NameDic[0x10050800] = "Status Message";
                msgID2NameDic[0x10050900] = "Origination Continuation Message";
                msgID2NameDic[0x10050A00] = "Handoff Completion Message";
                msgID2NameDic[0x10050B00] = "Parameters Response message";
                msgID2NameDic[0x10050E00] = "Service Connect Completion Message";
                msgID2NameDic[0x10051000] = "Status Response Message";
                msgID2NameDic[0x10051100] = "TMSI Assignment Completion Message";
                msgID2NameDic[0x10051200] = "Supplemental Channel Request Message";
                msgID2NameDic[0x10051300] = "Candidate Frequency Search Response Message";
                msgID2NameDic[0x10051400] = "Candidate Frequency Search Report Message";
                msgID2NameDic[0x10051500] = "Periodic Pilot Strength Measurement Message";
                msgID2NameDic[0x10051600] = "Outer Loop Report Message";
                msgID2NameDic[0x10051700] = "Resource Request Message";
                msgID2NameDic[0x10051800] = "Extended Release Response Message";
                msgID2NameDic[0x10051A00] = "Enhanced Origination Message";
                msgID2NameDic[0x10051C00] = "Extended Pilot Strength Measurement Message";
                msgID2NameDic[0x10051D00] = "Extended Handoff Completion Message";
                msgID2NameDic[0x10051E00] = "Resource Release Request Messag";
                msgID2NameDic[0x10051F00] = "Security Mode Request Message";
                msgID2NameDic[0x10052000] = "Data Burst Response Message";
                msgID2NameDic[0x10052200] = "User Zone Update Request Message";
                msgID2NameDic[0x10052300] = "Call Cancel Message";
                msgID2NameDic[0x10052400] = "Device Information Message";
                msgID2NameDic[0x10052500] = "MC-MAP Initial L3 Message";
                msgID2NameDic[0x10050001] = "Pilot Strength Measurement Mini Message";
                msgID2NameDic[0x10050101] = "Supplemental Channel Request Mini Message";
                msgID2NameDic[0x10053A01] = "Resource Request Mini Message";
                msgID2NameDic[0x10053B01] = "Extended Release Response Mini Message";
                msgID2NameDic[0x10053C01] = "Resource Release Request Mini Message";

                msgID2NameDic[0x10060100] = "Sync channel message";

                msgID2NameDic[0x10070100] = "System Parameters Message";
                msgID2NameDic[0x10070200] = "Access Parameters Message";
                msgID2NameDic[0x10070300] = "Neighbor List Message";
                msgID2NameDic[0x10070400] = "CDMA Channel List Message";
                msgID2NameDic[0x10070500] = "Slotted Page Message";
                msgID2NameDic[0x10070600] = "Page Message";
                msgID2NameDic[0x10070704] = "PCH Order->Abbreviated Alert";
                msgID2NameDic[0x10070708] = "PCH Order->Base Station Challenge Confirmation";
                msgID2NameDic[0x10070710] = "PCH Order->Reorder";
                msgID2NameDic[0x10070718] = "PCH Order->Audit";
                msgID2NameDic[0x10070724] = "PCH Order->Intercept";
                msgID2NameDic[0x10070740] = "PCH Order->Base Station Acknowledgement";
                msgID2NameDic[0x10070749] = "PCH Order->Lock Until Power-Cycled";
                msgID2NameDic[0x1007074A] = "PCH Order->Maintenance Required";
                msgID2NameDic[0x1007074B] = "PCH Order->Unlock";
                msgID2NameDic[0x10070754] = "PCH Order->Release";
                msgID2NameDic[0x1007076C] = "PCH Order->Registration Accepted";
                msgID2NameDic[0x1007076D] = "PCH Order->Registration Request";
                msgID2NameDic[0x1007076E] = "PCH Order->Registration Rejected";
                msgID2NameDic[0x10070778] = "PCH Order->Local Control Response";
                msgID2NameDic[0x1007077C] = "PCH Order->Slotted Mode";
                msgID2NameDic[0x10070780] = "PCH Order->Retry";
                msgID2NameDic[0x10070800] = "Channel Assigment Message";
                msgID2NameDic[0x10070900] = "Data Burst Message";                
                msgID2NameDic[0x10070C00] = "Feature Notification Message";
                msgID2NameDic[0x10070D00] = "Extended System Parameters Message";
                msgID2NameDic[0x10070E00] = "Extended Neighbor List Message";            
                msgID2NameDic[0x10071100] = "General Page Message";
                msgID2NameDic[0x10071200] = "Global Service Redirection Message";     
                msgID2NameDic[0x10071400] = "PACA Message";
                msgID2NameDic[0x10071500] = "Extended Channel Assignment Message";
                msgID2NameDic[0x10071600] = "General Neighbor List Message";
                msgID2NameDic[0x10071700] = "User Zone Identification Message";
                msgID2NameDic[0x10071800] = "Private Neighbor List Message";
                msgID2NameDic[0x10071900] = "Service Release Message";
                msgID2NameDic[0x10071A00] = "Extended Global Service Redirection Message";
                msgID2NameDic[0x10071B00] = "Extended CDMA Channel List Message";     
                msgID2NameDic[0x10071D00] = "ANSI-41 System Parameters Message";
                msgID2NameDic[0x10071E00] = "MC-RR Paramters Message";
                msgID2NameDic[0x10071F00] = "ANSI-41 RAND Message";
                msgID2NameDic[0x10072000] = "Enhanced Access Parameters Message";
                msgID2NameDic[0x10072100] = "Universal Neighbor List Message";      
                msgID2NameDic[0x10072300] = "Universal Page Message";

                msgID2NameDic[0x10080108] = "FTC Order->Base Station Challenge Confirmation";
                msgID2NameDic[0x1008010C] = "FTC Order->Message Encryption Mode";
                msgID2NameDic[0x10080114] = "FTC Order->Parameter Update";
                msgID2NameDic[0x10080118] = "FTC Order->Audit";
                msgID2NameDic[0x10080128] = "FTC Order->Maintenance";
                msgID2NameDic[0x10080140] = "FTC Order->Base Station Acknowledgement";
                msgID2NameDic[0x10080144] = "FTC Order->Pilot Measurement Request";
                msgID2NameDic[0x10080145] = "FTC Order->Periodic Pilot Measurement Request";
                msgID2NameDic[0x10080149] = "FTC Order->Lock Until Power-Cycled";
                msgID2NameDic[0x1008014A] = "FTC Order->Maintenance Required";
                msgID2NameDic[0x1008014C] = "FTC Order->Service Option Request";
                msgID2NameDic[0x10080150] = "FTC Order->Service Option Response";
                msgID2NameDic[0x10080154] = "FTC Order->Release";
                msgID2NameDic[0x10080158] = "FTC Order->Outer Loop Report";
                msgID2NameDic[0x1008015C] = "FTC Order->Long Code Transition Request";
                msgID2NameDic[0x10080164] = "FTC Order->Continuous DTMF Tone";
                msgID2NameDic[0x10080165] = "FTC Order->Stop Continuous DTMF Tone";
                msgID2NameDic[0x10080168] = "FTC Order->Status Request";
                msgID2NameDic[0x10080174] = "FTC Order->Service Option Control";
                msgID2NameDic[0x10080178] = "FTC Order->Local Control Response";
                msgID2NameDic[0x10080180] = "FTC Order->Retry";
                msgID2NameDic[0x10080200] = "Authentication Challenge Message";
                msgID2NameDic[0x10080300] = "Alert with Information Message";
                msgID2NameDic[0x10080400] = "Data Burst Message";
                msgID2NameDic[0x10080500] = "Handoff Direction Message";
                msgID2NameDic[0x10080600] = "Analog Handoff Direction Message";
                msgID2NameDic[0x10080700] = "In-Traffic System Parameter Message";
                msgID2NameDic[0x10080800] = "Neighbor List Update Message";
                msgID2NameDic[0x10080900] = "Send Burst DTMF Message";
                msgID2NameDic[0x10080A00] = "Power Control Parameters Message";
                msgID2NameDic[0x10080B00] = "Retrieve Parameters Message";
                msgID2NameDic[0x10080C00] = "Set Parameters Message";
                msgID2NameDic[0x10080D00] = "SSD Update Message";
                msgID2NameDic[0x10080E00] = "Flash with Information Message";
                msgID2NameDic[0x10080F00] = "Mobile Station Registered Message";
                msgID2NameDic[0x10081000] = "Status Request Message";
                msgID2NameDic[0x10081100] = "Extended Handoff Direction Message";
                msgID2NameDic[0x10081200] = "Service Request Message";
                msgID2NameDic[0x10081300] = "Service Response Message";
                msgID2NameDic[0x10081400] = "Service Connect Message";
                msgID2NameDic[0x10081500] = "Service Option Control Message";
                msgID2NameDic[0x10081600] = "TMSI Assignment Message";
                msgID2NameDic[0x10081700] = "Service Redirection Message";
                msgID2NameDic[0x10081800] = "Supplemental Channel Assignment Message";
                msgID2NameDic[0x10081900] = "Power Control Message";
                msgID2NameDic[0x10081A00] = "Extended Neighbor List Update Message";
                msgID2NameDic[0x10081B00] = "Candidate Frequency Search Request Message";
                msgID2NameDic[0x10081C00] = "Candidate Frequency Search Control Message";
                msgID2NameDic[0x10081D00] = "Power Up Function Message";
                msgID2NameDic[0x10081E00] = "Power Up Function Completion Message";
                msgID2NameDic[0x10081F00] = "General Handoff Direction Message";
                msgID2NameDic[0x10082000] = "Resource Allocation  Message";
                msgID2NameDic[0x10082100] = "Extended Release Message";
                msgID2NameDic[0x10082200] = "Universal Handoff Direction Message";
                msgID2NameDic[0x10082300] = "Extended Supplemental Channel Assignment Message";
                msgID2NameDic[0x10082400] = "Mobile Assisted Burst Operation Parameters Messages";
                msgID2NameDic[0x10082500] = "User Zone Reject Message";
                msgID2NameDic[0x10082600] = "User Zone Update Message";
                msgID2NameDic[0x10082700] = "Call Assignment Message";
                msgID2NameDic[0x10082800] = "Extended Alert With Information Message";
                msgID2NameDic[0x10082900] = "DS-41 Inter-system Transfer Message";
                msgID2NameDic[0x10082A00] = "Extended Flash With Information Message";
                msgID2NameDic[0x10082B00] = "Security Mode Command Message";
                msgID2NameDic[0x10082C00] = "MC-MAP L3 Message";
                msgID2NameDic[0x10082D00] = "MC-MAP Inter-Sys Handover Command Message";
                msgID2NameDic[0x10082E00] = "MC-MAP Dedicated Mode Paging Message";
                msgID2NameDic[0x10082F00] = "R-TMSI Assignment Completion Message";
                msgID2NameDic[0x10083000] = "MC-MAP Flow Release Message";
                msgID2NameDic[0x10083100] = "Base Station Status Request Message";
                msgID2NameDic[0x10080010] = "Resource Allocation Mini Message";
                msgID2NameDic[0x10080110] = "Extended Release Mini Message";
                msgID2NameDic[0x10080210] = "Forward Supplemental Channel Assignment Mini Message";
                msgID2NameDic[0x10080310] = "Reverse Supplemental Channel Assignment Mini Message";
                msgID2NameDic[0x10083810] = "Acknowledgment PDU";
                msgID2NameDic[0x10083910] = "Reset PDU";

                msgID2NameDic[0x10900000] = "CDMA Forward Dedicated Control Channel Message";
                msgID2NameDic[0x10910000] = "CDMA Reverse Dedicated Control Channel Message";
                msgID2NameDic[0x10300000] = "CDMA Quick Paging Channel";
                msgID2NameDic[0x10BC0000] = "CDMA Quick Paging Channel, Ver 2";
                msgID2NameDic[0x10D60000] = "CDMA BroadCast Control Channel";
                msgID2NameDic[0x10D70000] = "CDMA Reversed Enhanced Access Channel";
                msgID2NameDic[0x10D80000] = "CDMA Forward Common Control Channel";
            }
            if ("gsmtd".Equals(stack) || "all".Equals(stack))
            {
                msgID2NameDic[769] = "Alerting";
                msgID2NameDic[776] = "Call Confirmed";
                msgID2NameDic[770] = "Call Proceeding";
                msgID2NameDic[775] = "Connect";
                msgID2NameDic[783] = "Connect Acknowledge";
                msgID2NameDic[782] = "Emergency Setup";
                msgID2NameDic[771] = "Progess";
                msgID2NameDic[779] = "Recall";
                msgID2NameDic[777] = "Start CC";
                msgID2NameDic[773] = "Setup";
                msgID2NameDic[791] = "Modify";
                msgID2NameDic[799] = "Modify Complete";
                msgID2NameDic[787] = "Modify Reject";
                msgID2NameDic[784] = "User Information";
                msgID2NameDic[792] = "Hold";
                msgID2NameDic[793] = "Hold Acknowledge";
                msgID2NameDic[794] = "Hold Reject";
                msgID2NameDic[796] = "Retrieve";
                msgID2NameDic[797] = "Retrieve Acknowledge";
                msgID2NameDic[798] = "Retrieve Reject";
                msgID2NameDic[805] = "Disconnect";
                msgID2NameDic[813] = "Release";
                msgID2NameDic[810] = "Release Complete";
                msgID2NameDic[825] = "Congestion Control";
                msgID2NameDic[831] = "Notify";
                msgID2NameDic[830] = "Status";
                msgID2NameDic[820] = "Status Enquiry";
                msgID2NameDic[821] = "Start DTMF";
                msgID2NameDic[817] = "Stop DTMF";
                msgID2NameDic[818] = "Stop DTMF Acknowledge";
                msgID2NameDic[822] = "Start DTMF Acknowledge";
                msgID2NameDic[823] = "Start DTMF Reject";
                msgID2NameDic[826] = "Facility";

                msgID2NameDic[1596] = "RR Initialisation Request";
                msgID2NameDic[1595] = "Additional Assignment";
                msgID2NameDic[1599] = "Immediate Assignment";
                msgID2NameDic[1593] = "Immediate Assignment Extended";
                msgID2NameDic[1594] = "Immediate Assignment Reject";
                msgID2NameDic[1589] = "Ciphering Mode Command";
                msgID2NameDic[1586] = "Ciphering Mode Complete";
                msgID2NameDic[1584] = "Configuration Change Command";
                msgID2NameDic[1585] = "Configuration Change Ack.";
                msgID2NameDic[1587] = "Configuration Change Reject";
                msgID2NameDic[1582] = "Assignment Command";
                msgID2NameDic[1577] = "Assignment Complete";
                msgID2NameDic[1583] = "Assignment Failure";
                msgID2NameDic[1579] = "Handover Command";
                msgID2NameDic[1580] = "Handover Complete";
                msgID2NameDic[1576] = "Handover Failure";
                msgID2NameDic[1581] = "Physical Information";
                msgID2NameDic[1544] = "RR-CELL Change ORDER";
                msgID2NameDic[1571] = "PDCH Assignment Command";
                msgID2NameDic[1549] = "Channel Release";
                msgID2NameDic[1546] = "PARTIAL Release";
                msgID2NameDic[1551] = "PARTIAL Release Complete";
                msgID2NameDic[1569] = "Paging Request Type 1";
                msgID2NameDic[1570] = "Paging Request Type 2";
                msgID2NameDic[1572] = "Paging Request Type 3";
                msgID2NameDic[1575] = "Paging Response";
                msgID2NameDic[1568] = "Notificatoin/NCH";
                msgID2NameDic[1573] = "Notificatoin/FACCH";
                msgID2NameDic[1574] = "Reserved";
                msgID2NameDic[1547] = "Reserved";
                msgID2NameDic[1560] = "System Information Type 8";
                msgID2NameDic[1561] = "System Information Type 1";
                msgID2NameDic[1562] = "System Information Type 2";
                msgID2NameDic[1563] = "System Information Type 3";
                msgID2NameDic[1564] = "System Information Type 4";
                msgID2NameDic[1565] = "System Information Type 5";
                msgID2NameDic[1566] = "System Information Type 6";
                msgID2NameDic[1567] = "System Information Type 7";
                msgID2NameDic[1538] = "System Information Type 2bis";
                msgID2NameDic[1539] = "System Information Type 2ter";
                msgID2NameDic[1541] = "System Information Type 5bis";
                msgID2NameDic[1542] = "System Information Type 5ter";
                msgID2NameDic[1540] = "System Information Type 9";
                msgID2NameDic[1536] = "System Information Type 13";
                msgID2NameDic[1552] = "Channel Mode Modify";
                msgID2NameDic[1554] = "RR Status";
                msgID2NameDic[1559] = "Channel Mode Modify Acknowledge";
                msgID2NameDic[1556] = "Frequency Redefinition";
                msgID2NameDic[1557] = "Measurement Report";
                msgID2NameDic[1558] = "Classmark Change";
                msgID2NameDic[1555] = "Classmark Enquiry";
                msgID2NameDic[1590] = "Extended Measurement Report";
                msgID2NameDic[1591] = "Extended Measurement Order";
                msgID2NameDic[1588] = "GPRS Suspension Request";
                msgID2NameDic[1545] = "VGCS Uplink Grant";
                msgID2NameDic[1550] = "Uplink Release";
                msgID2NameDic[1548] = "Uplink FREE";
                msgID2NameDic[1578] = "Uplink Busy";
                msgID2NameDic[1553] = "Talker Indication";

                //added by wj 
                msgID2NameDic[1608] = "DTM Assignment Failure";
                msgID2NameDic[1609] = "DTM Reject";
                msgID2NameDic[1610] = "DTM Request";
                msgID2NameDic[1611] = "Packet Assignment";
                msgID2NameDic[1612] = "DTM Assignment Command";
                msgID2NameDic[1613] = "DTM Information";
                msgID2NameDic[1614] = "Packet Notification";
                //added by wj end

                msgID2NameDic[1791] = "Channel Request";
                msgID2NameDic[1789] = "Handover access";

                msgID2NameDic[1281] = "IMSI Detach Indication";
                msgID2NameDic[1282] = "Location Updating Accept";
                msgID2NameDic[1284] = "Location Updating Reject";
                msgID2NameDic[1288] = "Location Updating Request";
                msgID2NameDic[1297] = "Authentication Reject";
                msgID2NameDic[1298] = "Authentication Request";
                msgID2NameDic[1300] = "Authentication Response";
                msgID2NameDic[1304] = "Identity Request";
                msgID2NameDic[1305] = "Identity Response";
                msgID2NameDic[1306] = "TMSI Reallocation Command";
                msgID2NameDic[1307] = "TMSI Reallocation Complete";
                msgID2NameDic[1313] = "CM Service Accept";
                msgID2NameDic[1314] = "CM Service Reject";
                msgID2NameDic[1315] = "CM Service Abort";
                msgID2NameDic[1316] = "CM Service Request";
                msgID2NameDic[1317] = "CM Service Prompt";
                msgID2NameDic[1318] = "Notificatoin Response";
                msgID2NameDic[1320] = "CM RE-Establishment Request";
                msgID2NameDic[1321] = "ABORT";
                msgID2NameDic[1328] = "MM NULL";
                msgID2NameDic[1329] = "MM Status";
                msgID2NameDic[1330] = "MM Information";

                msgID2NameDic[2049] = "Attach Request";
                msgID2NameDic[2050] = "Attach Accept";
                msgID2NameDic[2051] = "Attach Complete";
                msgID2NameDic[2052] = "Attach Reject";
                msgID2NameDic[2053] = "Detach Request";
                msgID2NameDic[2054] = "Detach Accept";
                msgID2NameDic[2056] = "Routing area update Request";
                msgID2NameDic[2057] = "Routing area update Accept";
                msgID2NameDic[2058] = "Routing area update Complete";
                msgID2NameDic[2059] = "Routing area update Reject";
                msgID2NameDic[2064] = "P-TMSI realLocation Command";
                msgID2NameDic[2065] = "P-TMSI realLocation Complete";
                msgID2NameDic[2066] = "Authentication and Ciphering req";
                msgID2NameDic[2067] = "Authentication and Ciphering resp";
                msgID2NameDic[2068] = "Authentication and Ciphering rej";
                msgID2NameDic[2069] = "Identity Request";
                msgID2NameDic[2070] = "Identity Response";
                msgID2NameDic[2080] = "GMM Status";
                msgID2NameDic[2081] = "GMM Information";

                msgID2NameDic[2305] = "SMS CP Data";
                msgID2NameDic[2308] = "SMS CP Ack";
                msgID2NameDic[2320] = "SMS CP Error";

                msgID2NameDic[2625] = "Activate PDP context Request";
                msgID2NameDic[2626] = "Activate PDP context Accept";
                msgID2NameDic[2627] = "Activate PDP context Reject";
                msgID2NameDic[2628] = "Request PDP context activation";
                msgID2NameDic[2629] = "Request PDP context activation rej.";
                msgID2NameDic[2630] = "Deactivate PDP context Request";
                msgID2NameDic[2631] = "Deactivate PDP context Accept";
                msgID2NameDic[2632] = "Modify PDP context Request";
                msgID2NameDic[2633] = "Modify PDP context Accept";
                msgID2NameDic[2640] = "Activate AA PDP context Request";
                msgID2NameDic[2641] = "Activate AA PDP context Accept";
                msgID2NameDic[2642] = "Activate AA PDP context Reject";
                msgID2NameDic[2643] = "Deactivate AA PDP context Request";
                msgID2NameDic[2644] = "Deactivate AA PDP context Accept";
                msgID2NameDic[2645] = "SM Status";

                msgID2NameDic[4352] = "Packet Cell Change Failure";
                msgID2NameDic[4353] = "Packet Control Acknowledgement";
                msgID2NameDic[4354] = "Packet Downlink Ack/Nack";
                msgID2NameDic[4355] = "Packet Uplink Dummy Control Block";
                msgID2NameDic[4356] = "Packet Measurement Report";
                msgID2NameDic[4362] = "Packet Enhanced Measurement Report";
                msgID2NameDic[4357] = "Packet Resource Request";
                msgID2NameDic[4358] = "Packet Mobile TBF Status";
                msgID2NameDic[4359] = "Packet PSI Status";
                msgID2NameDic[4360] = "EGPRS Packet Downlink Ack/Nack";
                msgID2NameDic[4361] = "Packet Pause";
                msgID2NameDic[4129] = "Packet Access Reject";
                msgID2NameDic[4097] = "Packet Cell Change Order";
                msgID2NameDic[4098] = "Packet Downlink Assignment";
                msgID2NameDic[4099] = "Packet Measurement Order";
                msgID2NameDic[4130] = "Packet Paging Request";
                msgID2NameDic[4131] = "Packet PDCH Release";
                msgID2NameDic[4100] = "Packet Polling Request";
                msgID2NameDic[4101] = "Packet Power Control/Timing Advance";
                msgID2NameDic[4132] = "Packet PRACH Parameters";
                msgID2NameDic[4102] = "Packet Queueing Notificatoin";
                msgID2NameDic[4103] = "Packet Timeslot Reconfigure";
                msgID2NameDic[4104] = "Packet TBF Release";
                msgID2NameDic[4105] = "Packet Uplink Ack/Nack";
                msgID2NameDic[4106] = "Packet Uplink Assignment";
                msgID2NameDic[4133] = "Packet Downlink Dummy Control Block";
                msgID2NameDic[4145] = "PSI1";
                msgID2NameDic[4146] = "PSI2";
                msgID2NameDic[4147] = "PSI3";
                msgID2NameDic[4148] = "PSI3 bis";
                msgID2NameDic[4149] = "PSI4";
                msgID2NameDic[4150] = "PSI5";
                msgID2NameDic[4144] = "PSI6";
                msgID2NameDic[4152] = "PSI7";
                msgID2NameDic[4153] = "PSI8";
                msgID2NameDic[4151] = "PSI13";

                msgID2NameDic[0x713A0301] = "Alerting";
                msgID2NameDic[0x713A0308] = "Call Confirmed";
                msgID2NameDic[0x713A0302] = "Call Proceeding";
                msgID2NameDic[0x713A0307] = "Connect";
                msgID2NameDic[0x713A030F] = "Connect Acknowledge";
                msgID2NameDic[0x713A030E] = "Emergency Setup";
                msgID2NameDic[0x713A0303] = "Progess";
                msgID2NameDic[0x713A0304] = "CC Establishment";
                msgID2NameDic[0x713A0306] = "CC Establishment Confirmed";
                msgID2NameDic[0x713A030B] = "Recall";
                msgID2NameDic[0x713A0309] = "Start CC";
                msgID2NameDic[0x713A0305] = "Setup";
                msgID2NameDic[0x713A0317] = "Modify";
                msgID2NameDic[0x713A031F] = "Modify Complete";
                msgID2NameDic[0x713A0313] = "Modify Reject";
                msgID2NameDic[0x713A0310] = "User Information";
                msgID2NameDic[0x713A0318] = "Hold";
                msgID2NameDic[0x713A0319] = "Hold Acknowledge";
                msgID2NameDic[0x713A031A] = "Hold Reject";
                msgID2NameDic[0x713A031C] = "Retrieve";
                msgID2NameDic[0x713A031D] = "Retrieve Acknowledge";
                msgID2NameDic[0x713A031E] = "Retrieve Reject";
                msgID2NameDic[0x713A0325] = "Disconnect";
                msgID2NameDic[0x713A032D] = "Release";
                msgID2NameDic[0x713A032A] = "Release Complete";
                msgID2NameDic[0x713A0339] = "Congestion Control";
                msgID2NameDic[0x713A033E] = "Notify";
                msgID2NameDic[0x713A033D] = "Status";
                msgID2NameDic[0x713A0334] = "Status Enquiry";
                msgID2NameDic[0x713A0335] = "Start DTMF";
                msgID2NameDic[0x713A0331] = "Stop DTMF";
                msgID2NameDic[0x713A0332] = "Stop DTMF Acknowledge";
                msgID2NameDic[0x713A0336] = "Start DTMF Acknowledge";
                msgID2NameDic[0x713A0337] = "Start DTMF Reject";
                msgID2NameDic[0x713A033A] = "Facility";       

                msgID2NameDic[0x713A0501] = "IMSI Detach Indication";
                msgID2NameDic[0x713A0502] = "Location Updating Accept";
                msgID2NameDic[0x713A0504] = "Location Updating Reject";
                msgID2NameDic[0x713A0508] = "Location Updating Request";
                msgID2NameDic[0x713A0511] = "Authentication Reject";
                msgID2NameDic[0x713A0512] = "Authentication Request";
                msgID2NameDic[0x713A0514] = "Authentication Response";
                msgID2NameDic[0x713A051B] = "TMSI Reallocation Complete";
                msgID2NameDic[0x713A0518] = "Identity Request";
                msgID2NameDic[0x713A0519] = "Identity Response";
                msgID2NameDic[0x713A051A] = "TMSI Reallocation Command";
                msgID2NameDic[0x713A0521] = "CM Service Accept";
                msgID2NameDic[0x713A0522] = "CM Service Reject";
                msgID2NameDic[0x713A0523] = "CM Service Abort";
                msgID2NameDic[0x713A0524] = "CM Service Request";
                msgID2NameDic[0x713A0525] = "CM Service Prompt";
                msgID2NameDic[0x713A0526] = "Notificatoin Response";
                msgID2NameDic[0x713A0528] = "CM RE-Establishment Request";
                msgID2NameDic[0x713A0529] = "ABORT";
                msgID2NameDic[0x713A0530] = "MM NULL";
                msgID2NameDic[0x713A0531] = "MM Status";
                msgID2NameDic[0x713A0532] = "MM Information";

                msgID2NameDic[0x713A0801] = "Attach Request";
                msgID2NameDic[0x713A0802] = "Attach Accept";
                msgID2NameDic[0x713A0803] = "Attach Complete";
                msgID2NameDic[0x713A0804] = "Attach Reject";
                msgID2NameDic[0x713A0805] = "Detach Request";
                msgID2NameDic[0x713A0806] = "Detach Accept";
                msgID2NameDic[0x713A0808] = "Routing area update Request";
                msgID2NameDic[0x713A0809] = "Routing area update Accept";
                msgID2NameDic[0x713A080A] = "Routing area update Complete";
                msgID2NameDic[0x713A080B] = "Routing area update Reject";
                msgID2NameDic[0x713A080C] = "GMM Service Request";
                msgID2NameDic[0x713A080D] = "GMM Service Accept";
                msgID2NameDic[0x713A080E] = "GMM Service Reject";
                msgID2NameDic[0x713A0810] = "P-TMSI realLocation Command";
                msgID2NameDic[0x713A0811] = "P-TMSI realLocation Complete";
                msgID2NameDic[0x713A0812] = "Authentication Ciphering Request";
                msgID2NameDic[0x713A0813] = "Authentication Ciphering Response";
                msgID2NameDic[0x713A0814] = "Authentication Ciphering Reject";
                msgID2NameDic[0x713A081C] = "Authentication Ciphering Failure";
                msgID2NameDic[0x713A0815] = "GMM Identity Request";
                msgID2NameDic[0x713A0816] = "GMM Identity Response";
                msgID2NameDic[0x713A0820] = "GMM Status";
                msgID2NameDic[0x713A0821] = "GMM Information";

                msgID2NameDic[0x713A0A41] = "Activate PDP context Request";
                msgID2NameDic[0x713A0A42] = "Activate PDP context Accept";
                msgID2NameDic[0x713A0A43] = "Activate PDP context Reject";
                msgID2NameDic[0x713A0A44] = "Request PDP context activation";
                msgID2NameDic[0x713A0A45] = "Request PDP context activation reject";
                msgID2NameDic[0x713A0A46] = "Deactivate PDP context Request";
                msgID2NameDic[0x713A0A47] = "Deactivate PDP context Accept";
                msgID2NameDic[0x713A0A48] = "Modify PDP Context Request Downlink";
                msgID2NameDic[0x713A0A49] = "Modify PDP Context Accept  UpLink";
                msgID2NameDic[0x713A0A4A] = "Modify PDP Context Request UpLink";
                msgID2NameDic[0x713A0A4B] = "Modify PDP Context Accept  Downlink";
                msgID2NameDic[0x713A0A4C] = "Modify PDP Context Reject";
                msgID2NameDic[0x713A0A4D] = "Activate secondary PDP context request";
                msgID2NameDic[0x713A0A4E] = "Activate secondary PDP context accept";
                msgID2NameDic[0x713A0A4F] = "Activate secondary PDP context reject";
                msgID2NameDic[0x713A0A50] = "Activate AA PDP context Request";
                msgID2NameDic[0x713A0A51] = "Activate AA PDP context Accept";
                msgID2NameDic[0x713A0A52] = "Activate AA PDP context Reject";
                msgID2NameDic[0x713A0A53] = "Deactivate AA PDP context Request";
                msgID2NameDic[0x713A0A54] = "Deactivate AA PDP context Accept";
                msgID2NameDic[0x713A0A55] = "SM Status";

                msgID2NameDic[0x52260100] = "Packet Cell Change Failure";
                msgID2NameDic[0x52260101] = "Packet Control Acknowledgement";
                msgID2NameDic[0x52260102] = "Packet Downlink Ack/Nack";
                msgID2NameDic[0x52260103] = "Packet Uplink Dummy Control Block";
                msgID2NameDic[0x52260104] = "Packet Measurement Report";
                msgID2NameDic[0x52260105] = "Packet Resource Request";
                msgID2NameDic[0x52260106] = "Packet Mobile TBF Status";
                msgID2NameDic[0x52260107] = "Packet PSI Status";
                msgID2NameDic[0x52260108] = "EGPRS Packet Downlink Ack/Nack";
                msgID2NameDic[0x52260109] = "Packet Pause";
                msgID2NameDic[0x5226010A] = "Additional MS Radio Access";
                msgID2NameDic[0x5226010B] = "Packet Enhanced Measurement Report";
                msgID2NameDic[0x52260001] = "Packet Cell Change Order";
                msgID2NameDic[0x52260002] = "Packet Downlink Assignment";
                msgID2NameDic[0x52260003] = "Packet Measurement Order";
                msgID2NameDic[0x52260004] = "Packet Polling Request";
                msgID2NameDic[0x52260005] = "Packet Power Control Timing Advance";
                msgID2NameDic[0x52260006] = "Packet Queueing Notification";
                msgID2NameDic[0x52260007] = "Packet Timeslot Reconfigure";
                msgID2NameDic[0x52260008] = "Packet TBF Release";
                msgID2NameDic[0x52260009] = "Packet Uplink Ack Nack";
                msgID2NameDic[0x5226000A] = "Packet Uplink Assignment";
                msgID2NameDic[0x52260021] = "Packet Access Reject";
                msgID2NameDic[0x52260022] = "Packet Paging Request";
                msgID2NameDic[0x52260023] = "Packet PDCH Release";
                msgID2NameDic[0x52260024] = "Packet PRACH Parameters";
                msgID2NameDic[0x52260025] = "Packet Downlink Dummy Control Block";
                msgID2NameDic[0x52260031] = "Packet System Information Type 1";
                msgID2NameDic[0x52260032] = "Packet System Information Type 2";
                msgID2NameDic[0x52260033] = "Packet System Information Type 3";
                msgID2NameDic[0x52260034] = "Packet System Information Type 3bis";
                msgID2NameDic[0x52260035] = "Packet System Information Type 4";
                msgID2NameDic[0x52260036] = "Packet System Information Type 5";
                msgID2NameDic[0x52260020] = "Packet System Information Type 6";
                msgID2NameDic[0x52260038] = "Packet System Information Type 7";
                msgID2NameDic[0x52260039] = "Packet System Information Type 8";
                msgID2NameDic[0x52260037] = "Packet System Information Type 13";

                msgID2NameDic[0x412F0001] = "CellUpdate";
                msgID2NameDic[0x412F0002] = "rrcConnectionRequest";
                msgID2NameDic[0x412F0003] = "uraUpdate";

                msgID2NameDic[0x412F0101] = "activeSetUpdateComplete";
                msgID2NameDic[0x412F0102] = "activeSetUpdateFailure";
                msgID2NameDic[0x412F0103] = "cellChangeOrderFromUTRANFailure";
                msgID2NameDic[0x412F0104] = "counterCheckResponse";
                msgID2NameDic[0x412F0105] = "handoverToUTRANComplete";
                msgID2NameDic[0x412F0106] = "initialDirectTransfer";
                msgID2NameDic[0x412F0107] = "handoverFromUTRANFailure";
                msgID2NameDic[0x412F0108] = "measurementControlFailure";
                msgID2NameDic[0x412F0109] = "measurementReport";
                msgID2NameDic[0x412F010A] = "physicalChannelReconfigurationComplete";
                msgID2NameDic[0x412F010B] = "physicalChannelReconfigurationFailure";
                msgID2NameDic[0x412F010C] = "radioBearerReconfigurationComplete";
                msgID2NameDic[0x412F010D] = "radioBearerReconfigurationFailure";
                msgID2NameDic[0x412F010E] = "radioBearerReleaseComplete";
                msgID2NameDic[0x412F010F] = "radioBearerReleaseFailure";
                msgID2NameDic[0x412F0110] = "radioBearerSetupComplete";
                msgID2NameDic[0x412F0111] = "radioBearerSetupFailure";
                msgID2NameDic[0x412F0112] = "rrcConnectionReleaseComplete";
                msgID2NameDic[0x412F0113] = "rrcConnectionSetupComplete";
                msgID2NameDic[0x412F0114] = "rrcStatus";
                msgID2NameDic[0x412F0115] = "securityModeComplete";
                msgID2NameDic[0x412F0116] = "securityModeFailure";
                msgID2NameDic[0x412F0117] = "signallingConnectionReleaseIndication";
                msgID2NameDic[0x412F0118] = "transportChannelReconfigurationComplete";
                msgID2NameDic[0x412F0119] = "transportChannelReconfigurationFailure";
                msgID2NameDic[0x412F011A] = "transportFormatCombinationControlFailure";
                msgID2NameDic[0x412F011B] = "ueCapabilityInformation";
                msgID2NameDic[0x412F011C] = "uplinkDirectTransfer";
                msgID2NameDic[0x412F011D] = "utranMobilityInformationConfirm";
                msgID2NameDic[0x412F011E] = "utranMobilityInformationFailure";

                msgID2NameDic[0x412F0201] = "cellUpdateConfirm";
                msgID2NameDic[0x412F0202] = "rrcConnectionReject";
                msgID2NameDic[0x412F0203] = "rrcConnectionRelease";
                msgID2NameDic[0x412F0204] = "rrcConnectionSetup";
                msgID2NameDic[0x412F0205] = "uraUpdateConfirm";

                msgID2NameDic[0x412F0301] = "activeSetUpdate";
                msgID2NameDic[0x412F0302] = "assistanceDataDelivery";
                msgID2NameDic[0x412F0303] = "cellChangeOrderFromUTRAN";
                msgID2NameDic[0x412F0304] = "cellUpdateConfirm";
                msgID2NameDic[0x412F0305] = "counterCheck";
                msgID2NameDic[0x412F0306] = "downlinkDirectTransfer";
                msgID2NameDic[0x412F0307] = "handoverFromUTRANCommand-GSM";
                msgID2NameDic[0x412F0308] = "handoverFromUTRANCommand-CDMA2000";
                msgID2NameDic[0x412F0309] = "measurementControl";
                msgID2NameDic[0x412F030A] = "pagingType2";
                msgID2NameDic[0x412F030B] = "physicalChannelReconfiguration";
                msgID2NameDic[0x412F030C] = "physicalSharedChannelAllocation";
                msgID2NameDic[0x412F030D] = "radioBearerReconfiguration";
                msgID2NameDic[0x412F030E] = "radioBearerRelease";
                msgID2NameDic[0x412F030F] = "radioBearerSetup";
                msgID2NameDic[0x412F0310] = "rrcConnectionRelease";
                msgID2NameDic[0x412F0311] = "securityModeCommand";
                msgID2NameDic[0x412F0312] = "signallingConnectionRelease";
                msgID2NameDic[0x412F0313] = "transportChannelReconfiguration";
                msgID2NameDic[0x412F0314] = "transportFormatCombinationControl";
                msgID2NameDic[0x412F0315] = "ueCapabilityEnquiry";
                msgID2NameDic[0x412F0316] = "ueCapabilityInformationConfirm";
                msgID2NameDic[0x412F0317] = "uplinkPhysicalChannelControl";
                msgID2NameDic[0x412F0318] = "uraUpdateConfirm";
                msgID2NameDic[0x412F0319] = "utranMobilityInformation";
                msgID2NameDic[0x412F0320] = "extension";

                msgID2NameDic[0x412F0401] = "masterInformationBlock";
                msgID2NameDic[0x412F0402] = "systemInformationBlockType1";
                msgID2NameDic[0x412F0403] = "systemInformationBlockType2";
                msgID2NameDic[0x412F0404] = "systemInformationBlockType3";
                msgID2NameDic[0x412F0405] = "systemInformationBlockType4";
                msgID2NameDic[0x412F0406] = "systemInformationBlockType5";
                msgID2NameDic[0x412F0407] = "systemInformationBlockType6";
                msgID2NameDic[0x412F0408] = "systemInformationBlockType7";
                msgID2NameDic[0x412F0409] = "systemInformationBlockType8";
                msgID2NameDic[0x412F040A] = "systemInformationBlockType9";
                msgID2NameDic[0x412F040B] = "systemInformationBlockType10";
                msgID2NameDic[0x412F040C] = "systemInformationBlockType11";
                msgID2NameDic[0x412F040D] = "systemInformationBlockType12";
                msgID2NameDic[0x412F040E] = "systemInformationBlockType13";
                msgID2NameDic[0x412F040F] = "systemInformationBlockType13_1";
                msgID2NameDic[0x412F0410] = "systemInformationBlockType13_2";
                msgID2NameDic[0x412F0411] = "systemInformationBlockType13_3";
                msgID2NameDic[0x412F0412] = "systemInformationBlockType13_4";
                msgID2NameDic[0x412F0413] = "systemInformationBlockType14";
                msgID2NameDic[0x412F0414] = "systemInformationBlockType15";
                msgID2NameDic[0x412F0415] = "systemInformationBlockType15_1";
                msgID2NameDic[0x412F0416] = "systemInformationBlockType15_2";
                msgID2NameDic[0x412F0417] = "systemInformationBlockType15_3";
                msgID2NameDic[0x412F0418] = "systemInformationBlockType16";
                msgID2NameDic[0x412F0419] = "systemInformationBlockType17";
                msgID2NameDic[0x412F041A] = "systemInformationBlockType15_4";
                msgID2NameDic[0x412F041B] = "systemInformationBlockType18";
                msgID2NameDic[0x412F041C] = "schedulingBlock1";
                msgID2NameDic[0x412F041D] = "schedulingBlock2";
                msgID2NameDic[0x412F041E] = "systemInformationBlockType15_5";

                msgID2NameDic[0x412F0501] = "systemInformation";
                msgID2NameDic[0x412F0502] = "systemInformationChangeIndication";

                msgID2NameDic[0x412F0601] = "pagingType1";

                msgID2NameDic[0x412F0701] = "puschCapacityRequest";

                msgID2NameDic[0x412F0801] = "physicalSharedChannelAllocation";

            }
            if ("gsmUplink".Equals(stack) || "all".Equals(stack))
            {
                msgID2NameDic[0x0A0B0100] = "Assignment command";
                msgID2NameDic[0x0A0B0101] = "Assignment complete";
                msgID2NameDic[0x0A0B0102] = "Assignment failure";
                msgID2NameDic[0x0A0B0103] = "Intra BSC, inter cell hand over";
                msgID2NameDic[0x0A0B0104] = "Intra BSC, inter cell hand over complete";
                msgID2NameDic[0x0A0B0105] = "Intra BSC, inter cell hand over failure";
                msgID2NameDic[0x0A0B0106] = "Inter BSC hand over required";
                msgID2NameDic[0x0A0B0107] = "Inter BSC hand over command";
                msgID2NameDic[0x0A0B0108] = "Inter BSC hand over complete";
                msgID2NameDic[0x0A0B0109] = "Inter BSC hand over failure";
                msgID2NameDic[0x0A0B010A] = "DTAP message received down link";
                msgID2NameDic[0x0A0B010B] = "DTAP message sent up link";
                msgID2NameDic[0x0A0B010C] = "Connection release, reset";
                msgID2NameDic[0x0A0B010D] = "Connection release, reset circuit";
                msgID2NameDic[0x0A0B010E] = "Connection release, sccp fault";
                msgID2NameDic[0x0A0B010F] = "Connection release, terrestrial resource fault";
                msgID2NameDic[0x0A0B0110] = "Connection release, rrfault, error indication";
                msgID2NameDic[0x0A0B0111] = "Connection release, rrfault, connection failure indication";
                msgID2NameDic[0x0A0B0112] = "Connection release, rrfault, blocking request";
                msgID2NameDic[0x0A0B0113] = "Connection release, disconnection request";
                msgID2NameDic[0x0A0B0114] = "Connection release, traffic function failure ";
                msgID2NameDic[0x0A0B0115] = "Clear command";
                msgID2NameDic[0x0A0B0116] = "Channel release";
                msgID2NameDic[0x0A0B0117] = "Release indication";
                msgID2NameDic[0x0A0B0118] = "Release indication not received, timer T3109 expired";
                msgID2NameDic[0x0A0B011B] = "Assignment failure to MSC";
                msgID2NameDic[0x0A0B011C] = "Hand over candidate list";
                msgID2NameDic[0x0A0B011D] = "Hand over candidate list";
                msgID2NameDic[0x0A0B011E] = "Hand over candidate list";
                msgID2NameDic[0x0A0B011F] = "Hand over candidate list";
                msgID2NameDic[0x0A0B0120] = "Hand over candidate list";
                msgID2NameDic[0x0A0B0121] = "Hand over candidate list";
                msgID2NameDic[0x0A0B0122] = "Sapi n reject";
                msgID2NameDic[0x0A0B0123] = "Sapi 3, error indication";
                msgID2NameDic[0x0A0B0124] = "Sapi 3, establish indication";
                msgID2NameDic[0x0A0B0125] = "Sapi 3, establish request";
                msgID2NameDic[0x0A0B0126] = "Sapi 3, establish confirm";
                msgID2NameDic[0x0A0B0127] = "Sapi 3, release indication";
                msgID2NameDic[0x0A0B0128] = "Sapi 3, release request";
                msgID2NameDic[0x0A0B0129] = "Sapi 3, release confirm";
                msgID2NameDic[0x0A0B012B] = "Intra BSC, inter cell hand over command, during assignment";
                msgID2NameDic[0x0A0B012C] = "Intra BSC, inter cell hand over complete, during assignment";
                msgID2NameDic[0x0A0B012D] = "Intra BSC, inter cell hand over failure, during assignment";
                msgID2NameDic[0x0A0B012E] = "Inter BSC hand over required, during assignment";
                msgID2NameDic[0x0A0B012F] = "Inter BSC hand over command, during assignment";
                msgID2NameDic[0x0A0B0130] = "Inter BSC hand over complete, during assignment";
                msgID2NameDic[0x0A0B0131] = "Inter BSC hand over failure, during assignment";
                msgID2NameDic[0x0A0B0132] = "Intra BSC, intra cell hand over assignment command";
                msgID2NameDic[0x0A0B0133] = "Intra BSC, intra cell hand over assignment";
                msgID2NameDic[0x0A0B0134] = "Intra BSC, intra cell hand over assignment failur";
                msgID2NameDic[0x0A0B0135] = "Hand over candidate list";
                msgID2NameDic[0x0A0B0136] = "Hand over candidate list";
                msgID2NameDic[0x0A0B0137] = "Connection release, transcoder resource failure";
                msgID2NameDic[0x0A0B0138] = "Configuration change command";
                msgID2NameDic[0x0A0B0139] = "Configuration change acknowledge";
                msgID2NameDic[0x0A0B013A] = "Configuration change reject";
                msgID2NameDic[0x0A0B013B] = "Resource level change, assignment command";
                msgID2NameDic[0x0A0B013C] = "Resource level change, assignment complete";
                msgID2NameDic[0x0A0B013D] = "Resource level change, assignment failure";
                msgID2NameDic[0x0A0B013E] = "Resource level change, configuration change command";
                msgID2NameDic[0x0A0B013F] = "Resource level change, configuration change acknowledge";
                msgID2NameDic[0x0A0B0140] = "Resource level change, configuration change reject";
                msgID2NameDic[0x0A0B0141] = "Configuration change, downgrade, rrfault, error indication";
                msgID2NameDic[0x0A0B0142] = "Configuration change, downgrade, rrfault,connection failure indication";
                msgID2NameDic[0x0A0B0143] = "Configuration change, downgrade, rrfault,blocking request";
                msgID2NameDic[0x0A0B0144] = "Configuration change, downgrade, disconnection";
                msgID2NameDic[0x0A0B0145] = "Connection release, Ater reset circuit";
                msgID2NameDic[0x0A0B0146] = "Connection release, Ater sccp fault";
                msgID2NameDic[0x0A0B0147] = "Connection release, Ater terrestrial resources fault";
                msgID2NameDic[0x0A0B0148] = "Configuration change, downgrade, Ater terrestrial resource fault";
                msgID2NameDic[0x0A0B0149] = "Connection release, Ater reset";
                msgID2NameDic[0x0A0B014A] = "Configuration change, downgrade, transcoder resource failure";
                msgID2NameDic[0x0A0B014B] = "Inter system hand over required";
                msgID2NameDic[0x0A0B014C] = "Inter system hand over command";
                msgID2NameDic[0x0A0B014D] = "Inter system hand over failure";
                msgID2NameDic[0x0A0B0000] = "MTR File Header";
                msgID2NameDic[0x0A0B0001] = "MTR End of File";
                msgID2NameDic[0x0A0B0200] = "Measurement Record";
                msgID2NameDic[0x0A0B0400] = "Frequency Hopping Record";
                msgID2NameDic[0x0A0B0500] = "Record ADMINISTRATIVE TYPE 2";
                msgID2NameDic[0x0A0B0600] = "Record EVENT DATA, ADDITIONAL";

            }
            #endregion
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            if(lstViewMsg.SelectedItems.Count>0)
            {
                this.DialogResult = DialogResult.OK;
            }
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }


        internal int GetSelectedMsgID()
        {
            if (lstViewMsg.SelectedItems.Count > 0)
            {
                int msgid = (int)lstViewMsg.SelectedItems[0].Tag;
                return msgid;
            }
            return 0;
        }

        internal void SetCurrentSelMsgId(int msgid)
        {
            foreach (ListViewItem lvi in lstViewMsg.Items)
            {
                if((int)lvi.Tag == msgid)
                {
                    lvi.Selected = true;
                    lvi.EnsureVisible();
                    lstViewMsg.Invalidate();
                    break;
                }
            }
        }

        private void lstViewMsg_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            if (lstViewMsg.SelectedItems.Count > 0)
            {
                this.DialogResult = DialogResult.OK;
            }
        }

        private void rbGSMTD_CheckedChanged(object sender, EventArgs e)
        {
            FillMsgDic("gsmtd");
            initFillMsg();
        }

        private void rbCDMA_CheckedChanged(object sender, EventArgs e)
        {
            FillMsgDic("cdma");
            initFillMsg();
        }

        private void rbGsmUplink_CheckedChanged(object sender, EventArgs e)
        {
            FillMsgDic("gsmUplink");
            initFillMsg();
        }
    }
}