﻿namespace MasterCom.RAMS.Func
{
    partial class ZTCellBaStatForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.objectListView = new BrightIdeasSoftware.ObjectListView();
            this.olvColumnCell = new BrightIdeasSoftware.OLVColumn();
            this.olvColumn900Freqs = new BrightIdeasSoftware.OLVColumn();
            this.olvColumn900FreqsCount = new BrightIdeasSoftware.OLVColumn();
            this.olvColumn1800Freqs = new BrightIdeasSoftware.OLVColumn();
            this.olvColumn1800FreqsCount = new BrightIdeasSoftware.OLVColumn();
            this.olvColumn900FreqsIdle = new BrightIdeasSoftware.OLVColumn();
            this.olvColumn900FreqsIdleCount = new BrightIdeasSoftware.OLVColumn();
            this.olvColumn1800FreqsIdle = new BrightIdeasSoftware.OLVColumn();
            this.olvColumn1800FreqsIdleCount = new BrightIdeasSoftware.OLVColumn();
            this.contextMenuStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miOutputToExcel = new System.Windows.Forms.ToolStripMenuItem();
            ((System.ComponentModel.ISupportInitialize)(this.objectListView)).BeginInit();
            this.contextMenuStrip.SuspendLayout();
            this.SuspendLayout();
            // 
            // objectListView
            // 
            this.objectListView.AllColumns.Add(this.olvColumnCell);
            this.objectListView.AllColumns.Add(this.olvColumn900Freqs);
            this.objectListView.AllColumns.Add(this.olvColumn900FreqsCount);
            this.objectListView.AllColumns.Add(this.olvColumn1800Freqs);
            this.objectListView.AllColumns.Add(this.olvColumn1800FreqsCount);
            this.objectListView.AllColumns.Add(this.olvColumn900FreqsIdle);
            this.objectListView.AllColumns.Add(this.olvColumn900FreqsIdleCount);
            this.objectListView.AllColumns.Add(this.olvColumn1800FreqsIdle);
            this.objectListView.AllColumns.Add(this.olvColumn1800FreqsIdleCount);
            this.objectListView.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnCell,
            this.olvColumn900Freqs,
            this.olvColumn900FreqsCount,
            this.olvColumn1800Freqs,
            this.olvColumn1800FreqsCount,
            this.olvColumn900FreqsIdle,
            this.olvColumn900FreqsIdleCount,
            this.olvColumn1800FreqsIdle,
            this.olvColumn1800FreqsIdleCount});
            this.objectListView.ContextMenuStrip = this.contextMenuStrip;
            this.objectListView.Cursor = System.Windows.Forms.Cursors.Default;
            this.objectListView.Dock = System.Windows.Forms.DockStyle.Fill;
            this.objectListView.FullRowSelect = true;
            this.objectListView.GridLines = true;
            this.objectListView.Location = new System.Drawing.Point(0, 0);
            this.objectListView.MultiSelect = false;
            this.objectListView.Name = "objectListView";
            this.objectListView.ShowGroups = false;
            this.objectListView.Size = new System.Drawing.Size(977, 364);
            this.objectListView.TabIndex = 7;
            this.objectListView.UseCompatibleStateImageBehavior = false;
            this.objectListView.View = System.Windows.Forms.View.Details;
            // 
            // olvColumnCell
            // 
            this.olvColumnCell.AspectName = "cellInfo";
            this.olvColumnCell.HeaderFont = null;
            this.olvColumnCell.Text = "小区信息";
            this.olvColumnCell.Width = 111;
            // 
            // olvColumn900Freqs
            // 
            this.olvColumn900Freqs.AspectName = "";
            this.olvColumn900Freqs.HeaderFont = null;
            this.olvColumn900Freqs.Text = "GSM900频点号";
            this.olvColumn900Freqs.Width = 120;
            // 
            // olvColumn900FreqsCount
            // 
            this.olvColumn900FreqsCount.AspectName = "";
            this.olvColumn900FreqsCount.HeaderFont = null;
            this.olvColumn900FreqsCount.Text = "GSM900频点个数";
            this.olvColumn900FreqsCount.Width = 120;
            // 
            // olvColumn1800Freqs
            // 
            this.olvColumn1800Freqs.AspectName = "";
            this.olvColumn1800Freqs.HeaderFont = null;
            this.olvColumn1800Freqs.Text = "GSM1800频点号";
            this.olvColumn1800Freqs.Width = 120;
            // 
            // olvColumn1800FreqsCount
            // 
            this.olvColumn1800FreqsCount.HeaderFont = null;
            this.olvColumn1800FreqsCount.Text = "GSM1800频点个数";
            this.olvColumn1800FreqsCount.Width = 120;
            // 
            // olvColumn900FreqsIdle
            // 
            this.olvColumn900FreqsIdle.HeaderFont = null;
            this.olvColumn900FreqsIdle.Text = "GSM900频点(IDLE)";
            this.olvColumn900FreqsIdle.Width = 120;
            // 
            // olvColumn900FreqsIdleCount
            // 
            this.olvColumn900FreqsIdleCount.HeaderFont = null;
            this.olvColumn900FreqsIdleCount.Text = "GSM900频点(IDLE)个数";
            this.olvColumn900FreqsIdleCount.Width = 120;
            // 
            // olvColumn1800FreqsIdle
            // 
            this.olvColumn1800FreqsIdle.HeaderFont = null;
            this.olvColumn1800FreqsIdle.Text = "GSM1800频点号(IDLE)";
            this.olvColumn1800FreqsIdle.Width = 120;
            // 
            // olvColumn1800FreqsIdleCount
            // 
            this.olvColumn1800FreqsIdleCount.HeaderFont = null;
            this.olvColumn1800FreqsIdleCount.Text = "GSM1800频点(IDLE)个数";
            this.olvColumn1800FreqsIdleCount.Width = 120;
            // 
            // contextMenuStrip
            // 
            this.contextMenuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miOutputToExcel});
            this.contextMenuStrip.Name = "contextMenuStrip";
            this.contextMenuStrip.Size = new System.Drawing.Size(130, 26);
            // 
            // miOutputToExcel
            // 
            this.miOutputToExcel.Name = "miOutputToExcel";
            this.miOutputToExcel.Size = new System.Drawing.Size(129, 22);
            this.miOutputToExcel.Text = "导出Excel";
            this.miOutputToExcel.Click += new System.EventHandler(this.miOutputToExcel_Click);
            // 
            // ZTCellBaStatForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(977, 364);
            this.Controls.Add(this.objectListView);
            this.Name = "ZTCellBaStatForm";
            this.Text = "小区BA表统计";
            ((System.ComponentModel.ISupportInitialize)(this.objectListView)).EndInit();
            this.contextMenuStrip.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private BrightIdeasSoftware.ObjectListView objectListView;
        private BrightIdeasSoftware.OLVColumn olvColumn900FreqsCount;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip;
        private System.Windows.Forms.ToolStripMenuItem miOutputToExcel;
        private BrightIdeasSoftware.OLVColumn olvColumn900Freqs;
        private BrightIdeasSoftware.OLVColumn olvColumn1800Freqs;
        private BrightIdeasSoftware.OLVColumn olvColumnCell;
        private BrightIdeasSoftware.OLVColumn olvColumn1800FreqsCount;
        private BrightIdeasSoftware.OLVColumn olvColumn900FreqsIdle;
        private BrightIdeasSoftware.OLVColumn olvColumn1800FreqsIdle;
        private BrightIdeasSoftware.OLVColumn olvColumn900FreqsIdleCount;
        private BrightIdeasSoftware.OLVColumn olvColumn1800FreqsIdleCount;
    }
}