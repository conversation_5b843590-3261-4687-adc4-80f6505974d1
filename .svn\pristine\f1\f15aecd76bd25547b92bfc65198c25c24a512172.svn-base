﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    class ZTPilotFreqPolluteByRegion_NB : ZTPilotFreqPolluteByRegion_LTE
    {
        public ZTPilotFreqPolluteByRegion_NB(ServiceName serviceName)
            : base(serviceName)
        {
        }

        public override string Name
        {
            get { return "导频污染分析_NB"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 34000, 34007, this.Name);
        }
    }
}
