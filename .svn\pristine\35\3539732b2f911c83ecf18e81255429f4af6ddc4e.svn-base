﻿namespace MasterCom.RAMS.Func
{
    partial class WlanChannelApInfoForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(WlanChannelApInfoForm));
            this.listViewChannel = new BrightIdeasSoftware.TreeListView();
            this.columnHeaderChannel_Channel = new BrightIdeasSoftware.OLVColumn();
            this.columnHeaderSSID_Channel = new BrightIdeasSoftware.OLVColumn();
            this.columnHeaderBSSID_Channel = new BrightIdeasSoftware.OLVColumn();
            this.columnHeaderFreq_Channel = new BrightIdeasSoftware.OLVColumn();
            this.columnHeaderRSSI_Channel = new BrightIdeasSoftware.OLVColumn();
            this.columnHeaderCI_Channel = new BrightIdeasSoftware.OLVColumn();
            this.columnHeaderSFI_Channel = new BrightIdeasSoftware.OLVColumn();
            this.columnHeaderNFI_Channel = new BrightIdeasSoftware.OLVColumn();
            this.columnHeaderFirstSeen_Channel = new BrightIdeasSoftware.OLVColumn();
            this.columnHeaderLastSeen_Channel = new BrightIdeasSoftware.OLVColumn();
            this.columnHeaderActiveTimes_Channel = new BrightIdeasSoftware.OLVColumn();
            this.columnHeaderRx_Channel = new BrightIdeasSoftware.OLVColumn();
            this.columnHeaderTx_Channel = new BrightIdeasSoftware.OLVColumn();
            this.columnHeaderBand_Channel = new BrightIdeasSoftware.OLVColumn();
            this.columnHeaderABGN_Channel = new BrightIdeasSoftware.OLVColumn();
            this.columnHeaderType_Channel = new BrightIdeasSoftware.OLVColumn();
            this.columnHeaderEncrytion_Channel = new BrightIdeasSoftware.OLVColumn();
            this.columnHeaderSecurity_Channel = new BrightIdeasSoftware.OLVColumn();
            this.columnHeaderBridge_Channel = new BrightIdeasSoftware.OLVColumn();
            this.columnHeaderDCFPCF_Channel = new BrightIdeasSoftware.OLVColumn();
            this.columnHeaderBeaconInterval_Channel = new BrightIdeasSoftware.OLVColumn();
            this.columnHeaderSupportRate_Channel = new BrightIdeasSoftware.OLVColumn();
            this.columnHeaderMaxRate_Channel = new BrightIdeasSoftware.OLVColumn();
            this.columnHeaderPreamble_Channel = new BrightIdeasSoftware.OLVColumn();
            this.columnHeaderNoise_Channel = new BrightIdeasSoftware.OLVColumn();
            ((System.ComponentModel.ISupportInitialize)(this.listViewChannel)).BeginInit();
            this.SuspendLayout();
            // 
            // listViewChannel
            // 
            this.listViewChannel.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.columnHeaderChannel_Channel,
            this.columnHeaderSSID_Channel,
            this.columnHeaderBSSID_Channel,
            this.columnHeaderFreq_Channel,
            this.columnHeaderRSSI_Channel,
            this.columnHeaderCI_Channel,
            this.columnHeaderSFI_Channel,
            this.columnHeaderNFI_Channel,
            this.columnHeaderFirstSeen_Channel,
            this.columnHeaderLastSeen_Channel,
            this.columnHeaderActiveTimes_Channel,
            this.columnHeaderRx_Channel,
            this.columnHeaderTx_Channel,
            this.columnHeaderBand_Channel,
            this.columnHeaderABGN_Channel,
            this.columnHeaderType_Channel,
            this.columnHeaderEncrytion_Channel,
            this.columnHeaderSecurity_Channel,
            this.columnHeaderBridge_Channel,
            this.columnHeaderDCFPCF_Channel,
            this.columnHeaderBeaconInterval_Channel,
            this.columnHeaderSupportRate_Channel,
            this.columnHeaderMaxRate_Channel,
            this.columnHeaderPreamble_Channel,
            this.columnHeaderNoise_Channel});
            this.listViewChannel.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listViewChannel.FullRowSelect = true;
            this.listViewChannel.GridLines = true;
            this.listViewChannel.Location = new System.Drawing.Point(0, 0);
            this.listViewChannel.Name = "listViewChannel";
            this.listViewChannel.OwnerDraw = true;
            this.listViewChannel.ShowGroups = false;
            this.listViewChannel.Size = new System.Drawing.Size(545, 195);
            this.listViewChannel.TabIndex = 4;
            this.listViewChannel.UseCompatibleStateImageBehavior = false;
            this.listViewChannel.View = System.Windows.Forms.View.Details;
            this.listViewChannel.VirtualMode = true;
            this.listViewChannel.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.listViewChannel_MouseDoubleClick);
            // 
            // columnHeaderChannel_Channel
            // 
            this.columnHeaderChannel_Channel.HeaderFont = null;
            this.columnHeaderChannel_Channel.Text = "Channel";
            // 
            // columnHeaderSSID_Channel
            // 
            this.columnHeaderSSID_Channel.HeaderFont = null;
            this.columnHeaderSSID_Channel.Text = "SSID";
            // 
            // columnHeaderBSSID_Channel
            // 
            this.columnHeaderBSSID_Channel.HeaderFont = null;
            this.columnHeaderBSSID_Channel.Text = "BSSID";
            // 
            // columnHeaderFreq_Channel
            // 
            this.columnHeaderFreq_Channel.HeaderFont = null;
            this.columnHeaderFreq_Channel.Text = "Freqecncy";
            this.columnHeaderFreq_Channel.Width = 70;
            // 
            // columnHeaderRSSI_Channel
            // 
            this.columnHeaderRSSI_Channel.HeaderFont = null;
            this.columnHeaderRSSI_Channel.Text = "RSSI";
            // 
            // columnHeaderCI_Channel
            // 
            this.columnHeaderCI_Channel.HeaderFont = null;
            this.columnHeaderCI_Channel.Text = "C/I";
            // 
            // columnHeaderSFI_Channel
            // 
            this.columnHeaderSFI_Channel.HeaderFont = null;
            this.columnHeaderSFI_Channel.Text = "SFI";
            // 
            // columnHeaderNFI_Channel
            // 
            this.columnHeaderNFI_Channel.HeaderFont = null;
            this.columnHeaderNFI_Channel.Text = "NFI";
            // 
            // columnHeaderFirstSeen_Channel
            // 
            this.columnHeaderFirstSeen_Channel.HeaderFont = null;
            this.columnHeaderFirstSeen_Channel.Text = "First Seen";
            this.columnHeaderFirstSeen_Channel.Width = 76;
            // 
            // columnHeaderLastSeen_Channel
            // 
            this.columnHeaderLastSeen_Channel.HeaderFont = null;
            this.columnHeaderLastSeen_Channel.Text = "Last Seen";
            this.columnHeaderLastSeen_Channel.Width = 69;
            // 
            // columnHeaderActiveTimes_Channel
            // 
            this.columnHeaderActiveTimes_Channel.HeaderFont = null;
            this.columnHeaderActiveTimes_Channel.Text = "Active Times(s)";
            this.columnHeaderActiveTimes_Channel.Width = 108;
            // 
            // columnHeaderRx_Channel
            // 
            this.columnHeaderRx_Channel.HeaderFont = null;
            this.columnHeaderRx_Channel.Text = "Rx(kb/s)";
            this.columnHeaderRx_Channel.Width = 63;
            // 
            // columnHeaderTx_Channel
            // 
            this.columnHeaderTx_Channel.HeaderFont = null;
            this.columnHeaderTx_Channel.Text = "Tx(kb/s)";
            this.columnHeaderTx_Channel.Width = 72;
            // 
            // columnHeaderBand_Channel
            // 
            this.columnHeaderBand_Channel.HeaderFont = null;
            this.columnHeaderBand_Channel.Text = "Band(%)";
            // 
            // columnHeaderABGN_Channel
            // 
            this.columnHeaderABGN_Channel.HeaderFont = null;
            this.columnHeaderABGN_Channel.Text = "A\\B\\G\\N";
            // 
            // columnHeaderType_Channel
            // 
            this.columnHeaderType_Channel.HeaderFont = null;
            this.columnHeaderType_Channel.Text = "Type";
            // 
            // columnHeaderEncrytion_Channel
            // 
            this.columnHeaderEncrytion_Channel.HeaderFont = null;
            this.columnHeaderEncrytion_Channel.Text = "Encrytion";
            this.columnHeaderEncrytion_Channel.Width = 69;
            // 
            // columnHeaderSecurity_Channel
            // 
            this.columnHeaderSecurity_Channel.HeaderFont = null;
            this.columnHeaderSecurity_Channel.Text = "Security";
            this.columnHeaderSecurity_Channel.Width = 65;
            // 
            // columnHeaderBridge_Channel
            // 
            this.columnHeaderBridge_Channel.HeaderFont = null;
            this.columnHeaderBridge_Channel.Text = "Bridge";
            // 
            // columnHeaderDCFPCF_Channel
            // 
            this.columnHeaderDCFPCF_Channel.HeaderFont = null;
            this.columnHeaderDCFPCF_Channel.Text = "DCF\\PCF";
            // 
            // columnHeaderBeaconInterval_Channel
            // 
            this.columnHeaderBeaconInterval_Channel.HeaderFont = null;
            this.columnHeaderBeaconInterval_Channel.Text = "Beacon Interval(ms)";
            this.columnHeaderBeaconInterval_Channel.Width = 130;
            // 
            // columnHeaderSupportRate_Channel
            // 
            this.columnHeaderSupportRate_Channel.HeaderFont = null;
            this.columnHeaderSupportRate_Channel.Text = "Support Rate";
            this.columnHeaderSupportRate_Channel.Width = 89;
            // 
            // columnHeaderMaxRate_Channel
            // 
            this.columnHeaderMaxRate_Channel.HeaderFont = null;
            this.columnHeaderMaxRate_Channel.Text = "MaxRate(Mb)";
            this.columnHeaderMaxRate_Channel.Width = 83;
            // 
            // columnHeaderPreamble_Channel
            // 
            this.columnHeaderPreamble_Channel.HeaderFont = null;
            this.columnHeaderPreamble_Channel.Text = "Preamble";
            this.columnHeaderPreamble_Channel.Width = 66;
            // 
            // columnHeaderNoise_Channel
            // 
            this.columnHeaderNoise_Channel.HeaderFont = null;
            this.columnHeaderNoise_Channel.Text = "Noise";
            // 
            // WlanChannelApInfoForm
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("WlanChannelApInfoForm.Appearance.Image")));
            this.Appearance.Options.UseFont = true;
            this.Appearance.Options.UseImage = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(545, 195);
            this.Controls.Add(this.listViewChannel);
            this.Name = "WlanChannelApInfoForm";
            this.Text = "ChannelApInfo";
            ((System.ComponentModel.ISupportInitialize)(this.listViewChannel)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private BrightIdeasSoftware.TreeListView listViewChannel;
        private BrightIdeasSoftware.OLVColumn columnHeaderChannel_Channel;
        private BrightIdeasSoftware.OLVColumn columnHeaderSSID_Channel;
        private BrightIdeasSoftware.OLVColumn columnHeaderBSSID_Channel;
        private BrightIdeasSoftware.OLVColumn columnHeaderFreq_Channel;
        private BrightIdeasSoftware.OLVColumn columnHeaderRSSI_Channel;
        private BrightIdeasSoftware.OLVColumn columnHeaderCI_Channel;
        private BrightIdeasSoftware.OLVColumn columnHeaderSFI_Channel;
        private BrightIdeasSoftware.OLVColumn columnHeaderNFI_Channel;
        private BrightIdeasSoftware.OLVColumn columnHeaderFirstSeen_Channel;
        private BrightIdeasSoftware.OLVColumn columnHeaderLastSeen_Channel;
        private BrightIdeasSoftware.OLVColumn columnHeaderActiveTimes_Channel;
        private BrightIdeasSoftware.OLVColumn columnHeaderRx_Channel;
        private BrightIdeasSoftware.OLVColumn columnHeaderTx_Channel;
        private BrightIdeasSoftware.OLVColumn columnHeaderBand_Channel;
        private BrightIdeasSoftware.OLVColumn columnHeaderABGN_Channel;
        private BrightIdeasSoftware.OLVColumn columnHeaderType_Channel;
        private BrightIdeasSoftware.OLVColumn columnHeaderEncrytion_Channel;
        private BrightIdeasSoftware.OLVColumn columnHeaderSecurity_Channel;
        private BrightIdeasSoftware.OLVColumn columnHeaderBridge_Channel;
        private BrightIdeasSoftware.OLVColumn columnHeaderDCFPCF_Channel;
        private BrightIdeasSoftware.OLVColumn columnHeaderBeaconInterval_Channel;
        private BrightIdeasSoftware.OLVColumn columnHeaderSupportRate_Channel;
        private BrightIdeasSoftware.OLVColumn columnHeaderMaxRate_Channel;
        private BrightIdeasSoftware.OLVColumn columnHeaderPreamble_Channel;
        private BrightIdeasSoftware.OLVColumn columnHeaderNoise_Channel;
    }
}