﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Func;
using MasterCom.Util;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.Func
{
    public class DIYSimpleRoadQueryByRegionBase : DIYAnalyseByFileBackgroundBase
    {
        protected double maxSampleGap = 20;     // 允许的最大采样点间隔
        protected double minRoadLength = 50;    // 路段最小长度
        protected List<RoadInfoViewBase> resultRoadList = new List<RoadInfoViewBase>();

        public DIYSimpleRoadQueryByRegionBase(MainModel mainModel) : base(mainModel)
        {
        }

        public override string Name
        {
            get { return "道路分析"; }
        }

        protected override bool getCondition()
        {
            return true;
        }

        protected override void clearDataBeforeAnalyseFiles()
        {
            resultRoadList.Clear();
        }

        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager fileDataManager in MainModel.DTDataManager.FileDataManagers)
            {
                DoStatOneFile(fileDataManager);
            }
        }

        protected override void getResultsAfterQuery()
        {
            //
        }

        protected override void fireShowForm()
        {
            if (resultRoadList.Count == 0)
            {
                MessageBox.Show("没有符合条件的分析结果，请尝试放宽设置条件", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }
            RoadInfoForm roadForm = MainModel.GetObjectFromBlackboard(typeof(RoadInfoForm).FullName) as RoadInfoForm;
            if (roadForm == null || roadForm.IsDisposed)
            {
                roadForm = new RoadInfoForm(MainModel);
            }
            roadForm.FormCaption = this.Name;
            roadForm.RebuildColumns(resultRoadList[0].GridColumns);
            roadForm.FillData(GetResultBindingObject(resultRoadList));

            if (!roadForm.Visible)
            {
                roadForm.Show(MainModel.MainForm);
            }
            resultRoadList.Clear();
        }

        protected override void fireSetDefaultMapSerialTheme()
        {
            //
        }

        // 该函数需判断路段长度的合法性
        protected virtual void fireRoadCompleted(List<TestPoint> roadPoints, double roadLength, DTFileDataManager fileManager)
        {
            if (roadLength < minRoadLength)
            {
                return;
            }
            RoadInfoViewBase roadView = new RoadInfoViewBase(roadPoints, roadLength);
            resultRoadList.Add(roadView);
        }

        protected virtual SimpleRoadPointStatus GetPointStatus(TestPoint tp)
        {
            return SimpleRoadPointStatus.Valid;
        }

        protected virtual object GetResultBindingObject(List<RoadInfoViewBase> resultList)
        {
            if (resultList.Count == 0)
            {
                return null;
            }
            return new List<RoadInfoViewBase>(resultList);
        }

        private void SaveAndReset(ref List<TestPoint> roadPoints, ref double roadLength, TestPoint newPoint, DTFileDataManager fileManager)
        {
            fireRoadCompleted(roadPoints, roadLength, fileManager);
            roadPoints.Clear();
            roadLength = 0;
            if (newPoint != null)
            {
                roadPoints.Add(newPoint);
            }
        }

        private void DoStatOneFile(DTFileDataManager file)
        {
            List<TestPoint> testPoints = file.TestPoints;
            List<TestPoint> roadPoints = new List<TestPoint>();
            double roadLength = 0;

            foreach (TestPoint tp in testPoints)
            {
                if (!isValidTestPoint(tp))  // 不在区域内的点
                {
                    SaveAndReset(ref roadPoints, ref roadLength, null, file);
                    continue;
                }

                SimpleRoadPointStatus status = GetPointStatus(tp);
                if (status == SimpleRoadPointStatus.Ignore)         // 忽略的点，如参数不存在
                {
                    continue;
                }
                else if (status == SimpleRoadPointStatus.Invalid)   // 不满足特定条件
                {
                    SaveAndReset(ref roadPoints, ref roadLength, null, file);
                    continue;
                }

                // 构成路段的第一个点
                if (roadPoints.Count == 0)
                {
                    roadPoints.Add(tp);
                    continue;
                }

                // 路段的续接点，判断点间隔
                TestPoint lastPoint = roadPoints[roadPoints.Count - 1];
                double dis = MathFuncs.GetDistance(tp.Longitude, tp.Latitude, lastPoint.Longitude, lastPoint.Latitude);
                if (dis > maxSampleGap)
                {
                    SaveAndReset(ref roadPoints, ref roadLength, tp, file);
                    continue;
                }

                roadLength += dis;
                roadPoints.Add(tp);
            }

            // 文件结束
            SaveAndReset(ref roadPoints, ref roadLength, null, file);
        }
    }

    public enum SimpleRoadPointStatus
    {
        Valid,      // 满足条件的采样点
        Ignore,     // 该点当作不存在
        Invalid,    // 不满足条件的采样
    }
}
