<?xml version="1.0"?>
<Configs>
  <Config name="ConditionCfg">
    <Item name="Condition" typeName="IDictionary">
      <Item typeName="Int32" key="sampleCntMin">5</Item>
      <Item typeName="Double" key="distanceMin">50</Item>
      <Item typeName="Double" key="secondMin">10</Item>
      <Item typeName="Boolean" key="checkFTP">True</Item>
      <Item typeName="Double" key="ftpRateMax">5</Item>
      <Item typeName="Boolean" key="checkHTTP">True</Item>
      <Item typeName="Double" key="httpRateMax">5</Item>
      <Item typeName="Boolean" key="checkEmail">True</Item>
      <Item typeName="Double" key="emailRateMax">2</Item>
      <Item typeName="IList" key="CauseSet">
        <Item typeName="IDictionary">
          <Item typeName="String" key="TypeName">MasterCom.RAMS.ZTFunc.NRCoverCause</Item>
          <Item typeName="IList" key="SubCauseSet">
            <Item typeName="IDictionary">
              <Item typeName="String" key="TypeName">MasterCom.RAMS.ZTFunc.NRWrongCoverCause</Item>
              <Item typeName="Int32" key="angle">60</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="TypeName">MasterCom.RAMS.ZTFunc.NROverCoverCause</Item>
              <Item typeName="Single" key="rsrpMin">-85</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="TypeName">MasterCom.RAMS.ZTFunc.NRWeakCoverCause</Item>
              <Item typeName="Single" key="rsrpMax">-95</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="TypeName">MasterCom.RAMS.ZTFunc.NRRepeatCoverCause</Item>
              <Item typeName="Single" key="rsrpDiff">10</Item>
              <Item typeName="Int32" key="second">5</Item>
            </Item>
          </Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="TypeName">MasterCom.RAMS.ZTFunc.NRQualCause</Item>
          <Item typeName="IList" key="SubCauseSet">
            <Item typeName="IDictionary">
              <Item typeName="String" key="TypeName">MasterCom.RAMS.ZTFunc.NRPoorSINRCause</Item>
              <Item typeName="Single" key="sinrMax">6</Item>
              <Item typeName="IList" key="SubCauseSet">
                <Item typeName="IDictionary">
                  <Item typeName="String" key="TypeName">MasterCom.RAMS.ZTFunc.NRPoorQualMod3Interf</Item>
                  <Item typeName="Single" key="rsrpDiffMax">6</Item>
                </Item>
                <Item typeName="IDictionary">
                  <Item typeName="String" key="TypeName">MasterCom.RAMS.ZTFunc.NRPoorQualWeakCover</Item>
                  <Item typeName="Single" key="rsrpMax">-85</Item>
                </Item>
              </Item>
            </Item>
          </Item>
        </Item>
      </Item>
    </Item>
  </Config>
</Configs>