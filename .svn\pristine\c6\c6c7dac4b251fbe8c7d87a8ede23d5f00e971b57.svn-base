﻿using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;
using System.Xml;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRIndoorStationSettingDlgConfig_XJ : ConfigHelper<NRIndoorStationSettingDlgConfigModel_XJ>
    {
        private static NRIndoorStationSettingDlgConfig_XJ instance = null;
        public static NRIndoorStationSettingDlgConfig_XJ Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new NRIndoorStationSettingDlgConfig_XJ();
                }
                return instance;
            }
        }

        public override string ConfigPath { get; } = $@"{AppDomain.CurrentDomain.BaseDirectory}\config\StationDlgConfig\NRIndoorStationDlg.xml";

        public override string LogPath { get; } = @"\BackGroundLog\StationDlgConfig\";
        public override string LogName { get; } = "-5G室分单验门限设置.txt";

        protected override void loadConfig(XmlConfigFile xcfg, NRIndoorStationSettingDlgConfigModel_XJ configInfo)
        {
            try
            {
                XmlElement config = xcfg.GetConfig("Configs");
                configInfo.AccessSuccessRate = getValidData(xcfg, config, "AccessSuccessRate", "");
                configInfo.PingTimeDelay = getValidData(xcfg, config, "PingTimeDelay", "");
                configInfo.DownTestRSRP = getValidData(xcfg, config, "DownTestRSRP", "");
                configInfo.DownTestAvgSINR = getValidData(xcfg, config, "DownTestAvgSINR", "");
                configInfo.DownThroughput = getValidData(xcfg, config, "DownThroughput", "");
                configInfo.UploadTestRSRP = getValidData(xcfg, config, "UploadTestRSRP", "");
                configInfo.UploadAvgSINR = getValidData(xcfg, config, "UploadAvgSINR", "");
                configInfo.UploadThroughput = getValidData(xcfg, config, "UploadThroughput", "");
                configInfo.CallSuccessRate = getValidData(xcfg, config, "CallSuccessRate", "");
                configInfo.CallTimeDelay = getValidData(xcfg, config, "CallTimeDelay", "");
                configInfo.ConnectionSuccessRate = getValidData(xcfg, config, "ConnectionSuccessRate", "");
                configInfo.SwitchSuccessRate = getValidData(xcfg, config, "SwitchSuccessRate", "");
            }
            catch (Exception ex)
            {
                ErrMsg = $"加载配置出错:{ex.Message}";
            }
        }

        public override void SaveConfig(NRIndoorStationSettingDlgConfigModel_XJ configInfo)
        {
            try
            {
                var newConfig = new XmlConfigFile();
                XmlElement cfg = newConfig.AddConfig("Configs");
                newConfig.AddItem(cfg, "AccessSuccessRate", configInfo.AccessSuccessRate);
                newConfig.AddItem(cfg, "PingTimeDelay", configInfo.PingTimeDelay);
                newConfig.AddItem(cfg, "DownTestRSRP", configInfo.DownTestRSRP);
                newConfig.AddItem(cfg, "DownTestAvgSINR", configInfo.DownTestAvgSINR);
                newConfig.AddItem(cfg, "DownThroughput", configInfo.DownThroughput);
                newConfig.AddItem(cfg, "UploadTestRSRP", configInfo.UploadTestRSRP);
                newConfig.AddItem(cfg, "UploadAvgSINR", configInfo.UploadAvgSINR);
                newConfig.AddItem(cfg, "UploadThroughput", configInfo.UploadThroughput);
                newConfig.AddItem(cfg, "CallTimeDelay", configInfo.CallTimeDelay);
                newConfig.AddItem(cfg, "CallSuccessRate", configInfo.CallSuccessRate);
                newConfig.AddItem(cfg, "ConnectionSuccessRate", configInfo.ConnectionSuccessRate);
                newConfig.AddItem(cfg, "SwitchSuccessRate", configInfo.SwitchSuccessRate);
                newConfig.Save(ConfigPath);
            }
            catch (Exception ex)
            {
                ErrMsg = $"保存配置出错:{ex.Message}";
            }
        }
    }

    public class NRIndoorStationSettingDlgConfigModel_XJ : ConfigDataInfo
    {
        public string AccessSuccessRate { get; set; }

        public string PingTimeDelay { get; set; }

        public string DownTestRSRP { get; set; }

        public string DownTestAvgSINR { get; set; }

        public string DownThroughput { get; set; }

        public string UploadTestRSRP { get; set; }

        public string UploadAvgSINR { get; set; }

        public string UploadThroughput { get; set; }

        public string CallSuccessRate { get; set; }

        public string CallTimeDelay { get; set; }

        public string ConnectionSuccessRate { get; set; }

        public string SwitchSuccessRate { get; set; }
    }
}
