﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;

namespace MasterCom.RAMS.Stat
{
    public class QueryOrderCheckFileInfo : DIYSQLBase
    {
        /// <summary>
        /// 山西工单质检文件查询
        /// </summary>
        public QueryOrderCheckFileInfo()
            : base(MainModel.GetInstance())
        {
            MainDB = true;
        }

        protected override string getSqlTextString()
        {
#if DEBUG
            return @"exec [192.168.2.12].[KPIMNG_DB_SHANXIJIN]..proc_check_file_query";
#else
            return @"exec [KPIMNG_DB_SHANXIJIN]..proc_check_file_query";
#endif
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[7];
            rType[0] = E_VType.E_String;
            rType[1] = E_VType.E_Int;
            rType[2] = E_VType.E_String;
            rType[3] = E_VType.E_String;
            rType[4] = E_VType.E_Int;
            rType[5] = E_VType.E_Int;
            rType[6] = E_VType.E_Int;
            return rType;
        }

        private List<OrderCheckFileItem> listDistrictData = new List<OrderCheckFileItem>();
        public List<OrderCheckFileItem> OrderCheckFiles
        {
            get { return listDistrictData; }
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            this.listDistrictData.Clear();
            Package package = clientProxy.Package; 
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    string flag = package.Content.GetParamString();
                    int sn = package.Content.GetParamInt();
                    string fileName = package.Content.GetParamString();
                    string projectType = package.Content.GetParamString();
                    int cityID = package.Content.GetParamInt();
                    string areaTypeName = package.Content.GetParamString();
                    string areaName = package.Content.GetParamString();

                    this.listDistrictData.Add(new OrderCheckFileItem(flag, sn, fileName, projectType, cityID, areaTypeName, areaName));
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }

        public Dictionary<int, List<OrderCheckFileItem>> GetResult()
        {
            if (this.listDistrictData == null) 
                this.listDistrictData = new List<OrderCheckFileItem>();
            Dictionary<int, List<OrderCheckFileItem>> dicID2District = new Dictionary<int, List<OrderCheckFileItem>>();
            this.reformatData(this.listDistrictData, ref dicID2District);
            return dicID2District;
        }
        private void reformatData(List<OrderCheckFileItem> listDistrictData, ref Dictionary<int, List<OrderCheckFileItem>> dicID2Data)
        {
            dicID2Data.Clear();
            foreach (OrderCheckFileItem dd in listDistrictData)
            {
                if (!dicID2Data.ContainsKey(dd.cityID))
                {
                    dicID2Data[dd.cityID] = new List<OrderCheckFileItem>();
                }
                dicID2Data[dd.cityID].Add(dd);
            }
        }
        public override string Name
        {
            get { return "查找地市ID信息"; }
        }

    }

    public class OrderCheckFileItem
    {
        public string OrderFlag { get; set; }
        public int Round { get; set; }
        public string FileName { get; set; }
        public string projectType { get; set; }
        public int cityID { get; set; }
        public string CityName
        {
            get;
            private set;
        }
        public string AreaTypeName { get; set; }
        public string AreaName { get; set; }

        public OrderCheckFileItem(string orderFlag, int round
            , string fileName, string projName, int cityID,string areaTypeName,string areaName)
        {
            this.OrderFlag = orderFlag;
            this.Round = round;
            this.FileName = fileName;
            this.projectType = projName;
            this.cityID = cityID;
            CityName = DistrictManager.GetInstance().getDistrictName(cityID);
            this.AreaTypeName = areaTypeName;
            this.AreaName = areaName;
        }
    }
}
