﻿using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class GDWeakRoadInfoForm : MinCloseForm
    {
        public GDWeakRoadInfoForm(MainModel mainModel)
            : base(mainModel)
        {
            InitializeComponent();
        }

        public void FillData(List<WeakRoadInfo> limitInfoList)
        {
            BindingSource source = new BindingSource();
            source.DataSource = limitInfoList;
            gridData.DataSource = source;
            gridData.RefreshDataSource();
        }

        private void miRePlay_Click(object sender, EventArgs e)
        {
            int[] row = gv.GetSelectedRows();
            if (row.Length == 0)
                return;
            object o = gv.GetRow(row[0]);

            WeakRoadInfo weakInfo = o as WeakRoadInfo;
            DateTime dtime = weakInfo.DEndeTime;
            int iDistrictID = MainModel.DistrictID;
            try
            {
                MainModel.DistrictID = weakInfo.FileInfoMsg.DistrictID;
                MasterCom.RAMS.Model.Interface.FileReplayer.ReplayOnePart(weakInfo.FileInfoMsg, dtime);
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.ToString());
            }
            finally
            {
                MainModel.DistrictID = iDistrictID;
            }
        }

        private void outPutExcel_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(gv);
        }
    }
}
