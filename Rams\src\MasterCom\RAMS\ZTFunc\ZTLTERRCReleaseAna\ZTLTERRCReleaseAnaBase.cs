﻿using System;
using System.Collections.Generic;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTLTERRCReleaseAnaBase : DIYAnalyseFilesOneByOneByRegion
    {
        public List<ZTLTERRCReleaseAnaItem> resultList { get; set; } = new List<ZTLTERRCReleaseAnaItem>();    //保存结果
        protected bool isVoLTE = false;
        public ZTLTERRCReleaseAnaBase(MainModel mainModel)
            : base(mainModel)
        {
            this.IncludeEvent = false;
            this.IncludeMessage = true;
        }

        protected override void getReadyBeforeQuery()
        {
            ServiceTypes.Clear();
            if (isVoLTE)
            {
                ServiceTypes.Add(ServiceType.LTE_TDD_VOLTE);
                ServiceTypes.Add(ServiceType.LTE_FDD_VOLTE);
                ServiceTypes.Add(ServiceType.SER_LTE_TDD_VIDEO_VOLTE);
                ServiceTypes.Add(ServiceType.SER_LTE_FDD_VIDEO_VOLTE);
            }
            else
            {
                ServiceTypes.Add(ServiceType.LTE_FDD_DATA);
                ServiceTypes.Add(ServiceType.LTE_FDD_IDLE);
                ServiceTypes.Add(ServiceType.LTE_FDD_MULTI);
                ServiceTypes.Add(ServiceType.LTE_FDD_VOICE);
                ServiceTypes.Add(ServiceType.LTE_TDD_DATA);
                ServiceTypes.Add(ServiceType.LTE_TDD_UEP);
                ServiceTypes.Add(ServiceType.LTE_TDD_IDLE);
                ServiceTypes.Add(ServiceType.LTE_TDD_MULTI);
                ServiceTypes.Add(ServiceType.LTE_TDD_VOICE);
            }
        }

        protected override void clearDataBeforeAnalyseFiles()
        {
            resultList = new List<ZTLTERRCReleaseAnaItem>();
        }

        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager fileMng in MainModel.DTDataManager.FileDataManagers)
            {
                List<DTData> dtDataList = new List<DTData>();

                foreach (TestPoint tp in fileMng.TestPoints)
                {
                    dtDataList.Add((DTData)tp);
                }

                foreach (MasterCom.RAMS.Model.Message msg in fileMng.Messages)
                {
                    if (msg.ID == (int)EnumLteNBCheckMsg.RRCConnectionRelease
                        || msg.ID == (int)EnumLteNBCheckMsg.MeasurementReport)
                    {
                        dtDataList.Add((DTData)msg);
                    }
                }

                dtDataList.Sort(comparer);

                TestPoint lastTp = null;
                DTData lastMR = null;

                for (int i = 0; i < dtDataList.Count; i++)
                {
                    if (dtDataList[i] is TestPoint)
                    {
                        lastTp = dtDataList[i] as TestPoint; //用最近的采样点经纬度来填充
                    }
                    else if (dtDataList[i] is MasterCom.RAMS.Model.Message)
                    {
                        lastMR = dealMsg(dtDataList, lastTp, lastMR, i);
                    }
                }
            }
        }

        private DTData dealMsg(List<DTData> dtDataList, TestPoint lastTp, DTData lastMR, int i)
        {
            if ((dtDataList[i] as Message).ID == (int)EnumLteNBCheckMsg.RRCConnectionRelease)
            {
                int eutranEARFCN = getEutranEARFCNFromRRCConnRelease(dtDataList[i]);

                if (eutranEARFCN > 0)
                {
                    int lastMRRsrp = 0;
                    int lastMRPCI = 0;

                    if (getEutranRSRPAndPCIFromMR(lastMR, dtDataList[i], ref lastMRRsrp, ref lastMRPCI))
                    {
                        ZTLTERRCReleaseAnaItem item = new ZTLTERRCReleaseAnaItem(lastTp, eutranEARFCN, lastMRRsrp, lastMRPCI);
                        item.Analyse();
                        item.SN = resultList.Count + 1;
                        resultList.Add(item);
                    }
                }
            }
            else if ((dtDataList[i] as Message).ID == (int)EnumLteNBCheckMsg.MeasurementReport)
            {
                lastMR = dtDataList[i];
            }

            return lastMR;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="dtData"></param>
        /// <returns></returns>
        private bool getEutranRSRPAndPCIFromMR(DTData lastMR, DTData RRCConnRelease, ref int rsrp, ref int pci)
        {
            if (lastMR == null)  //没找到最近的，返回0
            {
                return false;
            }

            TimeSpan timeSpan = RRCConnRelease.DateTime - lastMR.DateTime;
            if(timeSpan.Milliseconds > 3000) //如果两个信令时间距离超过3秒
            {
                return false;
            }

            MessageWithSource msg = ((MessageWithSource)lastMR);
            MessageDecodeHelper.StartDissect(msg.Direction,msg.Source, msg.Source.Length, msg.ID);

            int nbCellsCount = 0;
            if (MessageDecodeHelper.GetSingleSInt("lte-rrc.measResultListEUTRA", ref nbCellsCount))
            {
                int[] arrRSRPs = new int[nbCellsCount + 1]; //包括主服的，所以+1
                int[] arrPCIs = new int[nbCellsCount];

                if (MessageDecodeHelper.GetMultiSInt("lte-rrc.rsrpResult", ref arrRSRPs, nbCellsCount + 1)
                    && MessageDecodeHelper.GetMultiSInt("lte-rrc.physCellId", ref arrPCIs, nbCellsCount)
                    && (arrRSRPs.Length > 1) && (arrPCIs.Length > 0))
                {
                    rsrp = arrRSRPs[1] - 141; //跳过主服
                    pci = arrPCIs[0];

                    return true;
                }
            }

            return false;
        }


        /// <summary>
        /// 分析原理，当RRC Connection Release中出现eutran的频段，就认为重定向异常
        /// </summary>
        /// <param name="dtData"></param>
        /// <returns></returns>
        private int getEutranEARFCNFromRRCConnRelease(DTData dtData)
        {
            MessageWithSource msg = ((MessageWithSource)dtData);
            MessageDecodeHelper.StartDissect(msg.Direction,msg.Source, msg.Source.Length, msg.ID);

            uint carrier = 0;
            uint earfcn = 0; 

            if(!MessageDecodeHelper.GetSingleUInt("lte-rrc.redirectedCarrierInfo", ref carrier))
            {
                return 0;
            }

            if (carrier != (int)ECarrierInfo.eutra)
            {
                return 0;
            }

            if (!MessageDecodeHelper.GetSingleUInt("lte-rrc.eutra", ref earfcn))
            {
                return 0;
            }

            return (int)earfcn;
        }

         /// <summary>
        /// 显示结果
        /// </summary>
        protected override void fireShowForm()
        {
            if (resultList.Count == 0)
            {
                System.Windows.Forms.MessageBox.Show("没有符合条件的信息！");
                return;
            }

            ZTLTERRCReleaseAnaListForm frm = mainModel.CreateResultForm(
                typeof(ZTLTERRCReleaseAnaListForm)) as ZTLTERRCReleaseAnaListForm;
            frm.FillData(resultList);
            frm.Visible = true;
            frm.BringToFront();
        }

        protected override void releaseSource()
        {
            resultList = null;
        }

        private Comparer comparer { get; set; } = new Comparer();
        private class Comparer : IComparer<DTData>
        {
            public int Compare(DTData x, DTData y)
            {
                return x.SN - y.SN;
            }
        }
    }

    public class ZTLTERRCReleaseAnaItem
    {
        public int SN { get; set; }
        public string FileName { get; set; }

        public LTECell ServCell { get; set; }
        public string CellName { get; set; }
        public int TAC { get; set; }
        public int ECI { get; set; }
        public int CellID { get; set; }
        public int EARFCN { get; set; }
        public int PCI { get; set; }

        public TestPoint Tp { get; set; }
        public string TpTime { get; set; }
        public double TpLongitude { get; set; }
        public double TpLatitude { get; set; }
        public float TpRSRP { get; set; }
        public float TpSINR { get; set; }

        public int EutranEARFCN { get; set; }  //RRC Conn Release信令中记录的频点
        public int LastMRPCI { get; set; }  // 距离RRC Conn Release信令时间最近的MR报告中的PCI信息，MR在RRCConnectionRelease之前
        public int LastMRRSRP { get; set; }  // 距离RRC Conn Release信令时间最近的MR报告中的邻区的RSRP信息，MR在RRCConnectionRelease之前

        public string ReDirectCellName { get; set; } //重定向小区名，根据EutranEARFCN 和 LastMRPCI 进行工参匹配
        public string CellDistance { get; set; }     //重定向小区与之前小区的距离

        public ZTLTERRCReleaseAnaItem(TestPoint tp, int eutranEARFCN, int lastMRRsrp, int lastMRPCI)
        {
            Tp = tp;
            EutranEARFCN = eutranEARFCN;
            LastMRRSRP = lastMRRsrp;
            LastMRPCI = lastMRPCI;
        }

        public virtual void Analyse()
        {
            //Cell Info
            int? tac = GetTAC(Tp);
            int? eci = GetECI(Tp);
            int? earfcn = GetEARFCN(Tp);
            int? pci = GetPCI(Tp);

            LTECell servCell = CellManager.GetInstance().GetNearestLTECell(Tp.DateTime, tac, eci, earfcn, pci, Tp.Longitude, Tp.Latitude);

            if (servCell != null)    //没有匹配到主服工参
            {
                ServCell = servCell;
                CellName = servCell.Name;
                TAC = servCell.TAC;
                ECI = servCell.ECI;
                EARFCN = servCell.EARFCN;
                PCI = servCell.PCI;
                CellID = servCell.CellID;
            }
            else
            {
                CellName = "";
                if (tac != null)
                {
                    TAC = (int)tac;
                    ECI = (int)eci;
                    EARFCN = (int)earfcn;
                    PCI = (int)pci;
                }
            }

            //重定向小区
            LTECell redirectCell = CellManager.GetInstance().GetNearestLTECell(Tp.DateTime, null, null, EutranEARFCN, LastMRPCI, Tp.Longitude, Tp.Latitude);
            if (redirectCell != null)
            {
                ReDirectCellName = redirectCell.Name;

                if (servCell != null)
                {
                    CellDistance = Math.Round(redirectCell.GetDistance(servCell.Longitude, servCell.Latitude), 2).ToString();
                }
            }

            //TestPoint Info
            FileName = Tp.FileName;
            TpTime = Tp.DateTimeStringWithMillisecond;
            TpLongitude = Tp.Longitude;
            TpLatitude = Tp.Latitude;

            float? rsrp = GetRSRP(Tp);
            if (rsrp != null)
            {
                TpRSRP = (float)rsrp;
            }

            float? sinr = GetSINR(Tp);
            if (sinr != null)
            {
                TpSINR = (float)sinr;
            }
        }

        protected virtual int? GetTAC(TestPoint tp)
        {
            if (tp is LTEFddTestPoint)
            {
                return (int?)(ushort)tp["lte_fdd_TAC"];
            }
            return (int?)(ushort)tp["lte_TAC"];
        }
        protected virtual int? GetECI(TestPoint tp)
        {
            if (tp is LTEFddTestPoint)
            {
                return (int?)tp["lte_fdd_ECI"];
            }
            return (int?)tp["lte_ECI"];
        }
        protected virtual int? GetEARFCN(TestPoint tp)
        {
            if (tp is LTEFddTestPoint)
            {
                return (int?)tp["lte_fdd_EARFCN"];
            }
            return (int?)tp["lte_EARFCN"];
        }
        protected virtual int? GetPCI(TestPoint tp)
        {
            if (tp is LTEFddTestPoint)
            {
                return (int?)(short?)tp["lte_fdd_PCI"];
            }
            return (int?)(short?)tp["lte_PCI"];
        }
        protected virtual float? GetRSRP(TestPoint tp)
        {
            if (tp is LTEFddTestPoint)
            {
                return (float?)tp["lte_fdd_RSRP"];
            }
            return (float?)tp["lte_RSRP"];
        }
        protected virtual float? GetSINR(TestPoint tp)
        {
            if (tp is LTEFddTestPoint)
            {
                return (float?)tp["lte_fdd_SINR"];
            }
            return (float?)tp["lte_SINR"];
        }
    } 
}
