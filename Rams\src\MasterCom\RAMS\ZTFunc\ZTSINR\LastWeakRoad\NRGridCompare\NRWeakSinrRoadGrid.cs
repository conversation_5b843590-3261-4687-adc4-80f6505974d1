﻿using MasterCom.MTGis;
using MasterCom.RAMS.Grid;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRWeakSinrRoadGrid
    {
        public NRWeakSinrRoadGrid(FileInfo file)
        {
            FileInfo = file;
            FileName = FileInfo.Name;
        }

        public int SN { get; set; }
        public FileInfo FileInfo { get; private set; }
        public string FileName { get; private set; }
        public double Distance { get; set; }
        public List<TestPoint> TestPoints { get; private set; } = new List<TestPoint>();
        public int TestPointCount { get; set; }

        private float totalSINR { get; set; } = 0;
        private float totalRSRP { get; set; } = 0;
        private int rsrpNum { get; set; } = 0;

        public float MinSINR { get; set; } = float.MaxValue;
        public float MaxSINR { get; set; } = float.MinValue;
        public float MinRSRP { get; set; } = float.MaxValue;
        public float MaxRSRP { get; set; } = float.MinValue;
        public float AvgSINR { get; private set; }
        public float AvgRSRP { get; private set; }

        public List<NRWeakSinrRoadGrid> IntersectSegs { get; private set; }
        public List<DbRect> Grids { get; private set; } = null;
        public int IntersectSegNum { get; private set; } = 0;
        public string IntersectSn { get; private set; } = "";

        public string RoadName { get; set; } = string.Empty;
        public double MidLng { get; private set; }
        public double MidLat { get; private set; }
        public double Second { get; private set; }
    
        public void Calculate()
        {
            TestPointCount = TestPoints.Count;
            Distance = Math.Round(Distance, 2);
        
            if (TestPointCount > 0)
            {
                MidLng = TestPoints[(TestPointCount / 2)].Longitude;
                MidLat = TestPoints[(TestPointCount / 2)].Latitude;
                AvgSINR = (float)Math.Round(totalSINR / TestPointCount, 2);
            }
            if (rsrpNum > 0)
            {
                AvgRSRP = (float)Math.Round(totalRSRP / rsrpNum, 2);
            }
            if (TestPointCount > 1)
            {
                Second = (TestPoints[TestPointCount - 1].DateTime - TestPoints[0].DateTime).TotalSeconds;
            }
            RoadName = GISManager.GetInstance().GetRoadPlaceDesc(MidLng, MidLat);

            if (IntersectSegs != null)
            {
                IntersectSegNum = IntersectSegs.Count;
                StringBuilder sns = new StringBuilder();
                foreach (NRWeakSinrRoadGrid other in IntersectSegs)
                {
                    sns.Append(other.SN + "；");
                }
                IntersectSn = sns.ToString().TrimEnd('；');
            }
        }

        public void Add(float sinr, float? rsrp, double distance, TestPoint testPoint)
        {
            totalSINR += sinr;
            MinSINR = Math.Min(MinSINR, sinr);
            MaxSINR = Math.Max(MaxSINR, sinr);
            if (rsrp != null && rsrp >= -141 && rsrp <= -10)
            {
                rsrpNum++;
                totalRSRP += (float)rsrp;
                MinRSRP = Math.Min(MinRSRP, (float)rsrp);
                MaxRSRP = Math.Max(MaxRSRP, (float)rsrp);
            }
            this.Distance += distance;
            TestPoints.Add(testPoint);
        }

        public void MakeGrid(double gridSpanDegree)
        {
            Grids = new List<DbRect>();
            foreach (TestPoint tp in TestPoints)
            {
                DbRect rect = GridHelper.GetCustomSizeBounds(tp.Longitude, tp.Latitude, gridSpanDegree);
                int idx = Grids.FindIndex(x =>
                {
                    return x.x1 == rect.x1 && x.y1 == rect.y1;
                });
                if (idx == -1)
                {
                    Grids.Add(rect);
                }
            }
        }

        public bool Intersect(NRWeakSinrRoadGrid grid2)
        {
            foreach (DbRect rect in grid2.Grids)
            {
                foreach (DbRect grid in Grids)
                {
                    if (grid.IsPointInThisRect(rect.Center()))
                    {
                        return true;
                    }
                }
            }
            return false;
        }

        public void AddIntersectSeg(NRWeakSinrRoadGrid grid1)
        {
            if (IntersectSegs == null)
            {
                IntersectSegs = new List<NRWeakSinrRoadGrid>();
            }
            IntersectSegs.Add(grid1);
        }
    }
}
