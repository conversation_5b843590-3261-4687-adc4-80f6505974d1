﻿namespace MasterCom.RAMS.ZTFunc.ZTSINR.LastWeakRoad.GridCompare
{
    partial class CompareResultForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.splitContainerControl1 = new DevExpress.XtraEditors.SplitContainerControl();
            this.splitContainerControl2 = new DevExpress.XtraEditors.SplitContainerControl();
            this.groupControl2 = new DevExpress.XtraEditors.GroupControl();
            this.gridControlP1Grid = new DevExpress.XtraGrid.GridControl();
            this.contextMenuStrip1 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExp2Xls = new System.Windows.Forms.ToolStripMenuItem();
            this.gridView1 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn9 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn10 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn11 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn6 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn7 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn8 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn15 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn16 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn18 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn19 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn20 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn21 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.groupControl3 = new DevExpress.XtraEditors.GroupControl();
            this.gridControlP2Grid = new DevExpress.XtraGrid.GridControl();
            this.contextMenuStripNew = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.toolStripMenuItem1 = new System.Windows.Forms.ToolStripMenuItem();
            this.gridView3 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn12 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn13 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn14 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn17 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn30 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn31 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn32 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn33 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn34 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn35 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn43 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn44 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn45 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn46 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn47 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn48 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn49 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.groupControl4 = new DevExpress.XtraEditors.GroupControl();
            this.gridControlRepeatGrid = new DevExpress.XtraGrid.GridControl();
            this.contextMenuStripRepeat = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.toolStripMenuItem2 = new System.Windows.Forms.ToolStripMenuItem();
            this.gridView2 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn22 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn23 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn53 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn52 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn24 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn25 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn26 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn27 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn28 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn29 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn36 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn37 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn38 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn39 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn40 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn41 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn42 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn50 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn51 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.splitContainerControl3 = new DevExpress.XtraEditors.SplitContainerControl();
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.checkRepeat = new DevExpress.XtraEditors.CheckEdit();
            this.checkNew = new DevExpress.XtraEditors.CheckEdit();
            this.checkPeriod1 = new DevExpress.XtraEditors.CheckEdit();
            this.colorRepeat = new DevExpress.XtraEditors.ColorEdit();
            this.colorNew = new DevExpress.XtraEditors.ColorEdit();
            this.colorPeriod1 = new DevExpress.XtraEditors.ColorEdit();
            this.label3 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.label1 = new System.Windows.Forms.Label();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).BeginInit();
            this.splitContainerControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl2)).BeginInit();
            this.splitContainerControl2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).BeginInit();
            this.groupControl2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlP1Grid)).BeginInit();
            this.contextMenuStrip1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl3)).BeginInit();
            this.groupControl3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlP2Grid)).BeginInit();
            this.contextMenuStripNew.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridView3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl4)).BeginInit();
            this.groupControl4.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlRepeatGrid)).BeginInit();
            this.contextMenuStripRepeat.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl3)).BeginInit();
            this.splitContainerControl3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.checkRepeat.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.checkNew.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.checkPeriod1.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.colorRepeat.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.colorNew.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.colorPeriod1.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // splitContainerControl1
            // 
            this.splitContainerControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl1.FixedPanel = DevExpress.XtraEditors.SplitFixedPanel.None;
            this.splitContainerControl1.Horizontal = false;
            this.splitContainerControl1.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl1.LookAndFeel.SkinName = "Office 2007 Blue";
            this.splitContainerControl1.LookAndFeel.UseDefaultLookAndFeel = false;
            this.splitContainerControl1.Name = "splitContainerControl1";
            this.splitContainerControl1.Panel1.Controls.Add(this.splitContainerControl2);
            this.splitContainerControl1.Panel1.Text = "Panel1";
            this.splitContainerControl1.Panel2.Controls.Add(this.groupControl4);
            this.splitContainerControl1.Panel2.Text = "Panel2";
            this.splitContainerControl1.Size = new System.Drawing.Size(1109, 479);
            this.splitContainerControl1.SplitterPosition = 208;
            this.splitContainerControl1.TabIndex = 0;
            this.splitContainerControl1.Text = "splitContainerControl1";
            // 
            // splitContainerControl2
            // 
            this.splitContainerControl2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl2.FixedPanel = DevExpress.XtraEditors.SplitFixedPanel.None;
            this.splitContainerControl2.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl2.LookAndFeel.SkinName = "Office 2007 Blue";
            this.splitContainerControl2.LookAndFeel.UseDefaultLookAndFeel = false;
            this.splitContainerControl2.Name = "splitContainerControl2";
            this.splitContainerControl2.Panel1.Controls.Add(this.groupControl2);
            this.splitContainerControl2.Panel1.Text = "Panel1";
            this.splitContainerControl2.Panel2.Controls.Add(this.groupControl3);
            this.splitContainerControl2.Panel2.Text = "Panel2";
            this.splitContainerControl2.Size = new System.Drawing.Size(1109, 208);
            this.splitContainerControl2.SplitterPosition = 549;
            this.splitContainerControl2.TabIndex = 0;
            this.splitContainerControl2.Text = "splitContainerControl2";
            // 
            // groupControl2
            // 
            this.groupControl2.Controls.Add(this.gridControlP1Grid);
            this.groupControl2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControl2.Location = new System.Drawing.Point(0, 0);
            this.groupControl2.Name = "groupControl2";
            this.groupControl2.Size = new System.Drawing.Size(549, 208);
            this.groupControl2.TabIndex = 2;
            this.groupControl2.Text = "时间段1质差列表";
            // 
            // gridControlP1Grid
            // 
            this.gridControlP1Grid.ContextMenuStrip = this.contextMenuStrip1;
            this.gridControlP1Grid.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlP1Grid.Location = new System.Drawing.Point(2, 23);
            this.gridControlP1Grid.LookAndFeel.SkinName = "Office 2010 Blue";
            this.gridControlP1Grid.MainView = this.gridView1;
            this.gridControlP1Grid.Name = "gridControlP1Grid";
            this.gridControlP1Grid.Size = new System.Drawing.Size(545, 183);
            this.gridControlP1Grid.TabIndex = 1;
            this.gridControlP1Grid.UseEmbeddedNavigator = true;
            this.gridControlP1Grid.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView1});
            // 
            // contextMenuStrip1
            // 
            this.contextMenuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExp2Xls});
            this.contextMenuStrip1.Name = "contextMenuStrip1";
            this.contextMenuStrip1.Size = new System.Drawing.Size(139, 26);
            // 
            // miExp2Xls
            // 
            this.miExp2Xls.Name = "miExp2Xls";
            this.miExp2Xls.Size = new System.Drawing.Size(138, 22);
            this.miExp2Xls.Text = "导出Excel...";
            this.miExp2Xls.Click += new System.EventHandler(this.miExp2Xls_Click);
            // 
            // gridView1
            // 
            this.gridView1.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn1,
            this.gridColumn2,
            this.gridColumn3,
            this.gridColumn4,
            this.gridColumn5,
            this.gridColumn9,
            this.gridColumn10,
            this.gridColumn11,
            this.gridColumn6,
            this.gridColumn7,
            this.gridColumn8,
            this.gridColumn15,
            this.gridColumn16,
            this.gridColumn18,
            this.gridColumn19,
            this.gridColumn20,
            this.gridColumn21});
            this.gridView1.GridControl = this.gridControlP1Grid;
            this.gridView1.GroupSummary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridGroupSummaryItem(DevExpress.Data.SummaryItemType.Count, "Name", null, "")});
            this.gridView1.Name = "gridView1";
            this.gridView1.OptionsBehavior.Editable = false;
            this.gridView1.OptionsDetail.EnableMasterViewMode = false;
            this.gridView1.OptionsDetail.ShowDetailTabs = false;
            this.gridView1.OptionsView.ColumnAutoWidth = false;
            this.gridView1.DoubleClick += new System.EventHandler(this.gridView_DoubleClick);
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "序号";
            this.gridColumn1.FieldName = "SN";
            this.gridColumn1.Fixed = DevExpress.XtraGrid.Columns.FixedStyle.Left;
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.Visible = true;
            this.gridColumn1.VisibleIndex = 0;
            // 
            // gridColumn2
            // 
            this.gridColumn2.Caption = "道路";
            this.gridColumn2.FieldName = "RoadName";
            this.gridColumn2.Fixed = DevExpress.XtraGrid.Columns.FixedStyle.Left;
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.Visible = true;
            this.gridColumn2.VisibleIndex = 1;
            this.gridColumn2.Width = 110;
            // 
            // gridColumn3
            // 
            this.gridColumn3.Caption = "持续距离(米)";
            this.gridColumn3.FieldName = "Distance";
            this.gridColumn3.Name = "gridColumn3";
            this.gridColumn3.Visible = true;
            this.gridColumn3.VisibleIndex = 2;
            this.gridColumn3.Width = 81;
            // 
            // gridColumn4
            // 
            this.gridColumn4.Caption = "持续时间(秒)";
            this.gridColumn4.FieldName = "Second";
            this.gridColumn4.Name = "gridColumn4";
            this.gridColumn4.Visible = true;
            this.gridColumn4.VisibleIndex = 3;
            this.gridColumn4.Width = 99;
            // 
            // gridColumn5
            // 
            this.gridColumn5.Caption = "采样点个数";
            this.gridColumn5.FieldName = "TestPointCount";
            this.gridColumn5.Name = "gridColumn5";
            this.gridColumn5.Visible = true;
            this.gridColumn5.VisibleIndex = 4;
            // 
            // gridColumn9
            // 
            this.gridColumn9.Caption = "平均SINR";
            this.gridColumn9.FieldName = "AvgSINR";
            this.gridColumn9.Name = "gridColumn9";
            this.gridColumn9.Visible = true;
            this.gridColumn9.VisibleIndex = 5;
            // 
            // gridColumn10
            // 
            this.gridColumn10.Caption = "最大SINR";
            this.gridColumn10.FieldName = "MaxSINR";
            this.gridColumn10.Name = "gridColumn10";
            this.gridColumn10.Visible = true;
            this.gridColumn10.VisibleIndex = 6;
            // 
            // gridColumn11
            // 
            this.gridColumn11.Caption = "最小SINR";
            this.gridColumn11.FieldName = "MinSINR";
            this.gridColumn11.Name = "gridColumn11";
            this.gridColumn11.Visible = true;
            this.gridColumn11.VisibleIndex = 7;
            // 
            // gridColumn6
            // 
            this.gridColumn6.Caption = "最大场强";
            this.gridColumn6.FieldName = "MaxRSRP";
            this.gridColumn6.Name = "gridColumn6";
            this.gridColumn6.Visible = true;
            this.gridColumn6.VisibleIndex = 8;
            // 
            // gridColumn7
            // 
            this.gridColumn7.Caption = "最小场强";
            this.gridColumn7.FieldName = "MinRSRP";
            this.gridColumn7.Name = "gridColumn7";
            this.gridColumn7.Visible = true;
            this.gridColumn7.VisibleIndex = 9;
            // 
            // gridColumn8
            // 
            this.gridColumn8.Caption = "平均场强";
            this.gridColumn8.FieldName = "AvgRSRP";
            this.gridColumn8.Name = "gridColumn8";
            this.gridColumn8.Visible = true;
            this.gridColumn8.VisibleIndex = 10;
            // 
            // gridColumn15
            // 
            this.gridColumn15.Caption = "中心经度";
            this.gridColumn15.FieldName = "MidLng";
            this.gridColumn15.Name = "gridColumn15";
            this.gridColumn15.Visible = true;
            this.gridColumn15.VisibleIndex = 11;
            // 
            // gridColumn16
            // 
            this.gridColumn16.Caption = "中心纬度";
            this.gridColumn16.FieldName = "MidLat";
            this.gridColumn16.Name = "gridColumn16";
            this.gridColumn16.Visible = true;
            this.gridColumn16.VisibleIndex = 12;
            // 
            // gridColumn18
            // 
            this.gridColumn18.Caption = "文件名";
            this.gridColumn18.FieldName = "FileName";
            this.gridColumn18.Name = "gridColumn18";
            this.gridColumn18.Visible = true;
            this.gridColumn18.VisibleIndex = 13;
            // 
            // gridColumn19
            // 
            this.gridColumn19.Caption = "片区";
            this.gridColumn19.FieldName = "AreaName";
            this.gridColumn19.Name = "gridColumn19";
            // 
            // gridColumn20
            // 
            this.gridColumn20.Caption = "网格";
            this.gridColumn20.FieldName = "GridName";
            this.gridColumn20.Name = "gridColumn20";
            // 
            // gridColumn21
            // 
            this.gridColumn21.Caption = "代维分区";
            this.gridColumn21.FieldName = "AgentName";
            this.gridColumn21.Name = "gridColumn21";
            // 
            // groupControl3
            // 
            this.groupControl3.Controls.Add(this.gridControlP2Grid);
            this.groupControl3.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControl3.Location = new System.Drawing.Point(0, 0);
            this.groupControl3.Name = "groupControl3";
            this.groupControl3.Size = new System.Drawing.Size(556, 208);
            this.groupControl3.TabIndex = 5;
            this.groupControl3.Text = "时间段2质差列表";
            // 
            // gridControlP2Grid
            // 
            this.gridControlP2Grid.ContextMenuStrip = this.contextMenuStripNew;
            this.gridControlP2Grid.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlP2Grid.Location = new System.Drawing.Point(2, 23);
            this.gridControlP2Grid.LookAndFeel.SkinName = "Office 2010 Blue";
            this.gridControlP2Grid.MainView = this.gridView3;
            this.gridControlP2Grid.Name = "gridControlP2Grid";
            this.gridControlP2Grid.Size = new System.Drawing.Size(552, 183);
            this.gridControlP2Grid.TabIndex = 2;
            this.gridControlP2Grid.UseEmbeddedNavigator = true;
            this.gridControlP2Grid.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView3});
            // 
            // contextMenuStripNew
            // 
            this.contextMenuStripNew.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.toolStripMenuItem1});
            this.contextMenuStripNew.Name = "contextMenuStrip1";
            this.contextMenuStripNew.Size = new System.Drawing.Size(139, 26);
            // 
            // toolStripMenuItem1
            // 
            this.toolStripMenuItem1.Name = "toolStripMenuItem1";
            this.toolStripMenuItem1.Size = new System.Drawing.Size(138, 22);
            this.toolStripMenuItem1.Text = "导出Excel...";
            this.toolStripMenuItem1.Click += new System.EventHandler(this.toolStripMenuItem1_Click);
            // 
            // gridView3
            // 
            this.gridView3.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn12,
            this.gridColumn13,
            this.gridColumn14,
            this.gridColumn17,
            this.gridColumn30,
            this.gridColumn31,
            this.gridColumn32,
            this.gridColumn33,
            this.gridColumn34,
            this.gridColumn35,
            this.gridColumn43,
            this.gridColumn44,
            this.gridColumn45,
            this.gridColumn46,
            this.gridColumn47,
            this.gridColumn48,
            this.gridColumn49});
            this.gridView3.GridControl = this.gridControlP2Grid;
            this.gridView3.GroupSummary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridGroupSummaryItem(DevExpress.Data.SummaryItemType.Count, "Name", null, "")});
            this.gridView3.Name = "gridView3";
            this.gridView3.OptionsBehavior.Editable = false;
            this.gridView3.OptionsDetail.EnableMasterViewMode = false;
            this.gridView3.OptionsDetail.ShowDetailTabs = false;
            this.gridView3.OptionsView.ColumnAutoWidth = false;
            this.gridView3.DoubleClick += new System.EventHandler(this.gridView_DoubleClick);
            // 
            // gridColumn12
            // 
            this.gridColumn12.Caption = "序号";
            this.gridColumn12.FieldName = "SN";
            this.gridColumn12.Fixed = DevExpress.XtraGrid.Columns.FixedStyle.Left;
            this.gridColumn12.Name = "gridColumn12";
            this.gridColumn12.Visible = true;
            this.gridColumn12.VisibleIndex = 0;
            // 
            // gridColumn13
            // 
            this.gridColumn13.Caption = "道路";
            this.gridColumn13.FieldName = "RoadName";
            this.gridColumn13.Fixed = DevExpress.XtraGrid.Columns.FixedStyle.Left;
            this.gridColumn13.Name = "gridColumn13";
            this.gridColumn13.Visible = true;
            this.gridColumn13.VisibleIndex = 1;
            this.gridColumn13.Width = 110;
            // 
            // gridColumn14
            // 
            this.gridColumn14.Caption = "持续距离(米)";
            this.gridColumn14.FieldName = "Distance";
            this.gridColumn14.Name = "gridColumn14";
            this.gridColumn14.Visible = true;
            this.gridColumn14.VisibleIndex = 2;
            this.gridColumn14.Width = 81;
            // 
            // gridColumn17
            // 
            this.gridColumn17.Caption = "持续时间(秒)";
            this.gridColumn17.FieldName = "Second";
            this.gridColumn17.Name = "gridColumn17";
            this.gridColumn17.Visible = true;
            this.gridColumn17.VisibleIndex = 3;
            this.gridColumn17.Width = 99;
            // 
            // gridColumn30
            // 
            this.gridColumn30.Caption = "采样点个数";
            this.gridColumn30.FieldName = "TestPointCount";
            this.gridColumn30.Name = "gridColumn30";
            this.gridColumn30.Visible = true;
            this.gridColumn30.VisibleIndex = 4;
            // 
            // gridColumn31
            // 
            this.gridColumn31.Caption = "平均SINR";
            this.gridColumn31.FieldName = "AvgSINR";
            this.gridColumn31.Name = "gridColumn31";
            this.gridColumn31.Visible = true;
            this.gridColumn31.VisibleIndex = 5;
            // 
            // gridColumn32
            // 
            this.gridColumn32.Caption = "最大SINR";
            this.gridColumn32.FieldName = "MaxSINR";
            this.gridColumn32.Name = "gridColumn32";
            this.gridColumn32.Visible = true;
            this.gridColumn32.VisibleIndex = 6;
            // 
            // gridColumn33
            // 
            this.gridColumn33.Caption = "最小SINR";
            this.gridColumn33.FieldName = "MinSINR";
            this.gridColumn33.Name = "gridColumn33";
            this.gridColumn33.Visible = true;
            this.gridColumn33.VisibleIndex = 7;
            // 
            // gridColumn34
            // 
            this.gridColumn34.Caption = "最大场强";
            this.gridColumn34.FieldName = "MaxRSRP";
            this.gridColumn34.Name = "gridColumn34";
            this.gridColumn34.Visible = true;
            this.gridColumn34.VisibleIndex = 8;
            // 
            // gridColumn35
            // 
            this.gridColumn35.Caption = "最小场强";
            this.gridColumn35.FieldName = "MinRSRP";
            this.gridColumn35.Name = "gridColumn35";
            this.gridColumn35.Visible = true;
            this.gridColumn35.VisibleIndex = 9;
            // 
            // gridColumn43
            // 
            this.gridColumn43.Caption = "平均场强";
            this.gridColumn43.FieldName = "AvgRSRP";
            this.gridColumn43.Name = "gridColumn43";
            this.gridColumn43.Visible = true;
            this.gridColumn43.VisibleIndex = 10;
            // 
            // gridColumn44
            // 
            this.gridColumn44.Caption = "中心经度";
            this.gridColumn44.FieldName = "MidLng";
            this.gridColumn44.Name = "gridColumn44";
            this.gridColumn44.Visible = true;
            this.gridColumn44.VisibleIndex = 11;
            // 
            // gridColumn45
            // 
            this.gridColumn45.Caption = "中心纬度";
            this.gridColumn45.FieldName = "MidLat";
            this.gridColumn45.Name = "gridColumn45";
            this.gridColumn45.Visible = true;
            this.gridColumn45.VisibleIndex = 12;
            // 
            // gridColumn46
            // 
            this.gridColumn46.Caption = "文件名";
            this.gridColumn46.FieldName = "FileName";
            this.gridColumn46.Name = "gridColumn46";
            this.gridColumn46.Visible = true;
            this.gridColumn46.VisibleIndex = 13;
            // 
            // gridColumn47
            // 
            this.gridColumn47.Caption = "片区";
            this.gridColumn47.FieldName = "AreaName";
            this.gridColumn47.Name = "gridColumn47";
            // 
            // gridColumn48
            // 
            this.gridColumn48.Caption = "网格";
            this.gridColumn48.FieldName = "GridName";
            this.gridColumn48.Name = "gridColumn48";
            // 
            // gridColumn49
            // 
            this.gridColumn49.Caption = "代维分区";
            this.gridColumn49.FieldName = "AgentName";
            this.gridColumn49.Name = "gridColumn49";
            // 
            // groupControl4
            // 
            this.groupControl4.Controls.Add(this.gridControlRepeatGrid);
            this.groupControl4.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControl4.Location = new System.Drawing.Point(0, 0);
            this.groupControl4.Name = "groupControl4";
            this.groupControl4.Size = new System.Drawing.Size(1109, 267);
            this.groupControl4.TabIndex = 3;
            this.groupControl4.Text = "时间段2相对于时间段1重叠的质差列表";
            // 
            // gridControlRepeatGrid
            // 
            this.gridControlRepeatGrid.ContextMenuStrip = this.contextMenuStripRepeat;
            this.gridControlRepeatGrid.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlRepeatGrid.Location = new System.Drawing.Point(2, 23);
            this.gridControlRepeatGrid.LookAndFeel.SkinName = "Office 2010 Blue";
            this.gridControlRepeatGrid.MainView = this.gridView2;
            this.gridControlRepeatGrid.Name = "gridControlRepeatGrid";
            this.gridControlRepeatGrid.Size = new System.Drawing.Size(1105, 242);
            this.gridControlRepeatGrid.TabIndex = 3;
            this.gridControlRepeatGrid.UseEmbeddedNavigator = true;
            this.gridControlRepeatGrid.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView2});
            // 
            // contextMenuStripRepeat
            // 
            this.contextMenuStripRepeat.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.toolStripMenuItem2});
            this.contextMenuStripRepeat.Name = "contextMenuStrip1";
            this.contextMenuStripRepeat.Size = new System.Drawing.Size(139, 26);
            // 
            // toolStripMenuItem2
            // 
            this.toolStripMenuItem2.Name = "toolStripMenuItem2";
            this.toolStripMenuItem2.Size = new System.Drawing.Size(138, 22);
            this.toolStripMenuItem2.Text = "导出Excel...";
            this.toolStripMenuItem2.Click += new System.EventHandler(this.toolStripMenuItem2_Click);
            // 
            // gridView2
            // 
            this.gridView2.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn22,
            this.gridColumn23,
            this.gridColumn53,
            this.gridColumn52,
            this.gridColumn24,
            this.gridColumn25,
            this.gridColumn26,
            this.gridColumn27,
            this.gridColumn28,
            this.gridColumn29,
            this.gridColumn36,
            this.gridColumn37,
            this.gridColumn38,
            this.gridColumn39,
            this.gridColumn40,
            this.gridColumn41,
            this.gridColumn42,
            this.gridColumn50,
            this.gridColumn51});
            this.gridView2.GridControl = this.gridControlRepeatGrid;
            this.gridView2.GroupSummary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridGroupSummaryItem(DevExpress.Data.SummaryItemType.Count, "Name", null, "")});
            this.gridView2.Name = "gridView2";
            this.gridView2.OptionsBehavior.Editable = false;
            this.gridView2.OptionsDetail.EnableMasterViewMode = false;
            this.gridView2.OptionsDetail.ShowDetailTabs = false;
            this.gridView2.OptionsView.ColumnAutoWidth = false;
            this.gridView2.DoubleClick += new System.EventHandler(this.gridView_DoubleClick);
            // 
            // gridColumn22
            // 
            this.gridColumn22.Caption = "时间段2序号";
            this.gridColumn22.FieldName = "SN";
            this.gridColumn22.Fixed = DevExpress.XtraGrid.Columns.FixedStyle.Left;
            this.gridColumn22.Name = "gridColumn22";
            this.gridColumn22.Visible = true;
            this.gridColumn22.VisibleIndex = 0;
            // 
            // gridColumn23
            // 
            this.gridColumn23.Caption = "道路";
            this.gridColumn23.FieldName = "RoadName";
            this.gridColumn23.Fixed = DevExpress.XtraGrid.Columns.FixedStyle.Left;
            this.gridColumn23.Name = "gridColumn23";
            this.gridColumn23.Visible = true;
            this.gridColumn23.VisibleIndex = 1;
            this.gridColumn23.Width = 110;
            // 
            // gridColumn53
            // 
            this.gridColumn53.Caption = "时间段1重叠的序号";
            this.gridColumn53.FieldName = "IntersectSn";
            this.gridColumn53.Name = "gridColumn53";
            this.gridColumn53.Visible = true;
            this.gridColumn53.VisibleIndex = 3;
            // 
            // gridColumn52
            // 
            this.gridColumn52.Caption = "重叠段数";
            this.gridColumn52.FieldName = "IntersectSegNum";
            this.gridColumn52.Name = "gridColumn52";
            this.gridColumn52.Visible = true;
            this.gridColumn52.VisibleIndex = 4;
            // 
            // gridColumn24
            // 
            this.gridColumn24.Caption = "持续距离(米)";
            this.gridColumn24.FieldName = "Distance";
            this.gridColumn24.Name = "gridColumn24";
            this.gridColumn24.Visible = true;
            this.gridColumn24.VisibleIndex = 2;
            this.gridColumn24.Width = 81;
            // 
            // gridColumn25
            // 
            this.gridColumn25.Caption = "持续时间(秒)";
            this.gridColumn25.FieldName = "Second";
            this.gridColumn25.Name = "gridColumn25";
            this.gridColumn25.Visible = true;
            this.gridColumn25.VisibleIndex = 5;
            this.gridColumn25.Width = 99;
            // 
            // gridColumn26
            // 
            this.gridColumn26.Caption = "采样点个数";
            this.gridColumn26.FieldName = "TestPointCount";
            this.gridColumn26.Name = "gridColumn26";
            this.gridColumn26.Visible = true;
            this.gridColumn26.VisibleIndex = 6;
            // 
            // gridColumn27
            // 
            this.gridColumn27.Caption = "平均SINR";
            this.gridColumn27.FieldName = "AvgSINR";
            this.gridColumn27.Name = "gridColumn27";
            this.gridColumn27.Visible = true;
            this.gridColumn27.VisibleIndex = 7;
            // 
            // gridColumn28
            // 
            this.gridColumn28.Caption = "最大SINR";
            this.gridColumn28.FieldName = "MaxSINR";
            this.gridColumn28.Name = "gridColumn28";
            this.gridColumn28.Visible = true;
            this.gridColumn28.VisibleIndex = 8;
            // 
            // gridColumn29
            // 
            this.gridColumn29.Caption = "最小SINR";
            this.gridColumn29.FieldName = "MinSINR";
            this.gridColumn29.Name = "gridColumn29";
            this.gridColumn29.Visible = true;
            this.gridColumn29.VisibleIndex = 9;
            // 
            // gridColumn36
            // 
            this.gridColumn36.Caption = "最大场强";
            this.gridColumn36.FieldName = "MaxRSRP";
            this.gridColumn36.Name = "gridColumn36";
            this.gridColumn36.Visible = true;
            this.gridColumn36.VisibleIndex = 10;
            // 
            // gridColumn37
            // 
            this.gridColumn37.Caption = "最小场强";
            this.gridColumn37.FieldName = "MinRSRP";
            this.gridColumn37.Name = "gridColumn37";
            this.gridColumn37.Visible = true;
            this.gridColumn37.VisibleIndex = 11;
            // 
            // gridColumn38
            // 
            this.gridColumn38.Caption = "平均场强";
            this.gridColumn38.FieldName = "AvgRSRP";
            this.gridColumn38.Name = "gridColumn38";
            this.gridColumn38.Visible = true;
            this.gridColumn38.VisibleIndex = 12;
            // 
            // gridColumn39
            // 
            this.gridColumn39.Caption = "中心经度";
            this.gridColumn39.FieldName = "MidLng";
            this.gridColumn39.Name = "gridColumn39";
            this.gridColumn39.Visible = true;
            this.gridColumn39.VisibleIndex = 13;
            // 
            // gridColumn40
            // 
            this.gridColumn40.Caption = "中心纬度";
            this.gridColumn40.FieldName = "MidLat";
            this.gridColumn40.Name = "gridColumn40";
            this.gridColumn40.Visible = true;
            this.gridColumn40.VisibleIndex = 14;
            // 
            // gridColumn41
            // 
            this.gridColumn41.Caption = "文件名";
            this.gridColumn41.FieldName = "FileName";
            this.gridColumn41.Name = "gridColumn41";
            this.gridColumn41.Visible = true;
            this.gridColumn41.VisibleIndex = 15;
            // 
            // gridColumn42
            // 
            this.gridColumn42.Caption = "片区";
            this.gridColumn42.FieldName = "AreaName";
            this.gridColumn42.Name = "gridColumn42";
            // 
            // gridColumn50
            // 
            this.gridColumn50.Caption = "网格";
            this.gridColumn50.FieldName = "GridName";
            this.gridColumn50.Name = "gridColumn50";
            // 
            // gridColumn51
            // 
            this.gridColumn51.Caption = "代维分区";
            this.gridColumn51.FieldName = "AgentName";
            this.gridColumn51.Name = "gridColumn51";
            // 
            // splitContainerControl3
            // 
            this.splitContainerControl3.CollapsePanel = DevExpress.XtraEditors.SplitCollapsePanel.Panel1;
            this.splitContainerControl3.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl3.Horizontal = false;
            this.splitContainerControl3.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl3.LookAndFeel.SkinName = "Office 2007 Blue";
            this.splitContainerControl3.LookAndFeel.UseDefaultLookAndFeel = false;
            this.splitContainerControl3.Name = "splitContainerControl3";
            this.splitContainerControl3.Panel1.Controls.Add(this.groupControl1);
            this.splitContainerControl3.Panel1.Text = "Panel1";
            this.splitContainerControl3.Panel2.Controls.Add(this.splitContainerControl1);
            this.splitContainerControl3.Panel2.Text = "Panel2";
            this.splitContainerControl3.Size = new System.Drawing.Size(1109, 548);
            this.splitContainerControl3.SplitterPosition = 65;
            this.splitContainerControl3.TabIndex = 1;
            this.splitContainerControl3.Text = "splitContainerControl3";
            // 
            // groupControl1
            // 
            this.groupControl1.Controls.Add(this.checkRepeat);
            this.groupControl1.Controls.Add(this.checkNew);
            this.groupControl1.Controls.Add(this.checkPeriod1);
            this.groupControl1.Controls.Add(this.colorRepeat);
            this.groupControl1.Controls.Add(this.colorNew);
            this.groupControl1.Controls.Add(this.colorPeriod1);
            this.groupControl1.Controls.Add(this.label3);
            this.groupControl1.Controls.Add(this.label2);
            this.groupControl1.Controls.Add(this.label1);
            this.groupControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControl1.Location = new System.Drawing.Point(0, 0);
            this.groupControl1.Name = "groupControl1";
            this.groupControl1.Size = new System.Drawing.Size(1109, 65);
            this.groupControl1.TabIndex = 0;
            this.groupControl1.Text = "图层控制";
            // 
            // checkRepeat
            // 
            this.checkRepeat.EditValue = true;
            this.checkRepeat.Location = new System.Drawing.Point(607, 34);
            this.checkRepeat.Name = "checkRepeat";
            this.checkRepeat.Properties.Caption = "显示";
            this.checkRepeat.Size = new System.Drawing.Size(47, 19);
            this.checkRepeat.TabIndex = 2;
            this.checkRepeat.CheckedChanged += new System.EventHandler(this.checkRepeat_CheckedChanged);
            // 
            // checkNew
            // 
            this.checkNew.EditValue = true;
            this.checkNew.Location = new System.Drawing.Point(296, 33);
            this.checkNew.Name = "checkNew";
            this.checkNew.Properties.Caption = "显示";
            this.checkNew.Size = new System.Drawing.Size(47, 19);
            this.checkNew.TabIndex = 2;
            this.checkNew.CheckedChanged += new System.EventHandler(this.checkNew_CheckedChanged);
            // 
            // checkPeriod1
            // 
            this.checkPeriod1.EditValue = true;
            this.checkPeriod1.Location = new System.Drawing.Point(12, 33);
            this.checkPeriod1.Name = "checkPeriod1";
            this.checkPeriod1.Properties.Caption = "显示";
            this.checkPeriod1.Size = new System.Drawing.Size(47, 19);
            this.checkPeriod1.TabIndex = 2;
            this.checkPeriod1.CheckedChanged += new System.EventHandler(this.checkPeriod1_CheckedChanged);
            // 
            // colorRepeat
            // 
            this.colorRepeat.EditValue = System.Drawing.Color.Red;
            this.colorRepeat.Location = new System.Drawing.Point(821, 32);
            this.colorRepeat.Name = "colorRepeat";
            this.colorRepeat.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.colorRepeat.Size = new System.Drawing.Size(48, 21);
            this.colorRepeat.TabIndex = 1;
            this.colorRepeat.EditValueChanged += new System.EventHandler(this.colorRepeat_EditValueChanged);
            // 
            // colorNew
            // 
            this.colorNew.EditValue = System.Drawing.Color.Blue;
            this.colorNew.Location = new System.Drawing.Point(510, 32);
            this.colorNew.Name = "colorNew";
            this.colorNew.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.colorNew.Size = new System.Drawing.Size(48, 21);
            this.colorNew.TabIndex = 1;
            this.colorNew.EditValueChanged += new System.EventHandler(this.colorNew_EditValueChanged);
            // 
            // colorPeriod1
            // 
            this.colorPeriod1.EditValue = System.Drawing.Color.Yellow;
            this.colorPeriod1.Location = new System.Drawing.Point(204, 32);
            this.colorPeriod1.Name = "colorPeriod1";
            this.colorPeriod1.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.colorPeriod1.Size = new System.Drawing.Size(48, 21);
            this.colorPeriod1.TabIndex = 1;
            this.colorPeriod1.EditValueChanged += new System.EventHandler(this.colorPeriod1_EditValueChanged);
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(660, 36);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(158, 14);
            this.label3.TabIndex = 0;
            this.label3.Text = "时间段2重叠质差栅格颜色：";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(349, 36);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(158, 14);
            this.label2.TabIndex = 0;
            this.label2.Text = "时间段2新增质差栅格颜色：";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(65, 35);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(134, 14);
            this.label1.TabIndex = 0;
            this.label1.Text = "时间段1质差栅格颜色：";
            // 
            // CompareResultForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1109, 548);
            this.Controls.Add(this.splitContainerControl3);
            this.Name = "CompareResultForm";
            this.Text = "质差路段对比分析列表";
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).EndInit();
            this.splitContainerControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl2)).EndInit();
            this.splitContainerControl2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).EndInit();
            this.groupControl2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlP1Grid)).EndInit();
            this.contextMenuStrip1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl3)).EndInit();
            this.groupControl3.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlP2Grid)).EndInit();
            this.contextMenuStripNew.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridView3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl4)).EndInit();
            this.groupControl4.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlRepeatGrid)).EndInit();
            this.contextMenuStripRepeat.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl3)).EndInit();
            this.splitContainerControl3.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            this.groupControl1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.checkRepeat.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.checkNew.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.checkPeriod1.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.colorRepeat.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.colorNew.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.colorPeriod1.Properties)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl1;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl2;
        private DevExpress.XtraGrid.GridControl gridControlP1Grid;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn6;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn7;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn8;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn15;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn16;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn18;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn19;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn20;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn21;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl3;
        private DevExpress.XtraEditors.GroupControl groupControl1;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label label1;
        private DevExpress.XtraEditors.CheckEdit checkRepeat;
        private DevExpress.XtraEditors.CheckEdit checkNew;
        private DevExpress.XtraEditors.CheckEdit checkPeriod1;
        private DevExpress.XtraEditors.ColorEdit colorRepeat;
        private DevExpress.XtraEditors.ColorEdit colorNew;
        private DevExpress.XtraEditors.ColorEdit colorPeriod1;
        private System.Windows.Forms.Label label3;
        private DevExpress.XtraEditors.GroupControl groupControl2;
        private DevExpress.XtraEditors.GroupControl groupControl3;
        private DevExpress.XtraEditors.GroupControl groupControl4;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip1;
        private System.Windows.Forms.ToolStripMenuItem miExp2Xls;
        private System.Windows.Forms.ContextMenuStrip contextMenuStripNew;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem1;
        private System.Windows.Forms.ContextMenuStrip contextMenuStripRepeat;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn9;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn10;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn11;
        private DevExpress.XtraGrid.GridControl gridControlP2Grid;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn12;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn13;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn14;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn17;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn30;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn31;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn32;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn33;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn34;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn35;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn43;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn44;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn45;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn46;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn47;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn48;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn49;
        private DevExpress.XtraGrid.GridControl gridControlRepeatGrid;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn22;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn23;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn24;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn25;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn26;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn27;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn28;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn29;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn36;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn37;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn38;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn39;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn40;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn41;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn42;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn50;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn51;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn52;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn53;
    }
}