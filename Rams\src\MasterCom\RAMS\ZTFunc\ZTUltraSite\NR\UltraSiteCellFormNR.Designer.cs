﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class UltraSiteCellFormNR
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.gridControlFar = new DevExpress.XtraGrid.GridControl();
            this.ctxMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExportXlsx = new System.Windows.Forms.ToolStripMenuItem();
            this.gvFar = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn6 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn7 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn8 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn9 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn10 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn11 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn12 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn13 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn14 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.xtraTabControl1 = new DevExpress.XtraTab.XtraTabControl();
            this.xtraTabPage1 = new DevExpress.XtraTab.XtraTabPage();
            this.xtraTabPage2 = new DevExpress.XtraTab.XtraTabPage();
            this.gridControlHigh = new DevExpress.XtraGrid.GridControl();
            this.gvHigh = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn15 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn16 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn17 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn18 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn19 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn20 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn21 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn24 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn25 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn26 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.xtraTabPage3 = new DevExpress.XtraTab.XtraTabPage();
            this.gridControlNear = new DevExpress.XtraGrid.GridControl();
            this.gvNear = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn29 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn30 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn31 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn32 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn33 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn34 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn35 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn36 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn37 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn38 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn39 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn40 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn41 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn42 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn22 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn23 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn27 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn28 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn43 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn44 = new DevExpress.XtraGrid.Columns.GridColumn();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlFar)).BeginInit();
            this.ctxMenu.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gvFar)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).BeginInit();
            this.xtraTabControl1.SuspendLayout();
            this.xtraTabPage1.SuspendLayout();
            this.xtraTabPage2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlHigh)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvHigh)).BeginInit();
            this.xtraTabPage3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlNear)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvNear)).BeginInit();
            this.SuspendLayout();
            // 
            // gridControlFar
            // 
            this.gridControlFar.ContextMenuStrip = this.ctxMenu;
            this.gridControlFar.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlFar.Location = new System.Drawing.Point(0, 0);
            this.gridControlFar.LookAndFeel.SkinName = "Office 2010 Blue";
            this.gridControlFar.MainView = this.gvFar;
            this.gridControlFar.Name = "gridControlFar";
            this.gridControlFar.Size = new System.Drawing.Size(1137, 495);
            this.gridControlFar.TabIndex = 6;
            this.gridControlFar.UseEmbeddedNavigator = true;
            this.gridControlFar.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gvFar});
            // 
            // ctxMenu
            // 
            this.ctxMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExportXlsx});
            this.ctxMenu.Name = "ctxMenu";
            this.ctxMenu.Size = new System.Drawing.Size(139, 26);
            // 
            // miExportXlsx
            // 
            this.miExportXlsx.Name = "miExportXlsx";
            this.miExportXlsx.Size = new System.Drawing.Size(138, 22);
            this.miExportXlsx.Text = "导出Excel...";
            this.miExportXlsx.Click += new System.EventHandler(this.miExportXlsx_Click);
            // 
            // gvFar
            // 
            this.gvFar.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn1,
            this.gridColumn2,
            this.gridColumn3,
            this.gridColumn4,
            this.gridColumn5,
            this.gridColumn6,
            this.gridColumn7,
            this.gridColumn8,
            this.gridColumn9,
            this.gridColumn10,
            this.gridColumn11,
            this.gridColumn12,
            this.gridColumn13,
            this.gridColumn14,
            this.gridColumn22,
            this.gridColumn23});
            this.gvFar.GridControl = this.gridControlFar;
            this.gvFar.GroupSummary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridGroupSummaryItem(DevExpress.Data.SummaryItemType.Count, "Name", null, "")});
            this.gvFar.Name = "gvFar";
            this.gvFar.OptionsBehavior.Editable = false;
            this.gvFar.OptionsDetail.EnableMasterViewMode = false;
            this.gvFar.OptionsDetail.ShowDetailTabs = false;
            this.gvFar.OptionsView.ColumnAutoWidth = false;
            this.gvFar.OptionsView.ShowGroupPanel = false;
            this.gvFar.DoubleClick += new System.EventHandler(this.gvFar_DoubleClick);
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "类别";
            this.gridColumn1.FieldName = "TypeName";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.Visible = true;
            this.gridColumn1.VisibleIndex = 0;
            this.gridColumn1.Width = 77;
            // 
            // gridColumn2
            // 
            this.gridColumn2.Caption = "网格";
            this.gridColumn2.FieldName = "GridName";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.Visible = true;
            this.gridColumn2.VisibleIndex = 1;
            // 
            // gridColumn3
            // 
            this.gridColumn3.Caption = "站点";
            this.gridColumn3.FieldName = "SiteName";
            this.gridColumn3.Name = "gridColumn3";
            this.gridColumn3.Visible = true;
            this.gridColumn3.VisibleIndex = 2;
            this.gridColumn3.Width = 103;
            // 
            // gridColumn4
            // 
            this.gridColumn4.Caption = "小区";
            this.gridColumn4.FieldName = "CellName";
            this.gridColumn4.Name = "gridColumn4";
            this.gridColumn4.Visible = true;
            this.gridColumn4.VisibleIndex = 3;
            this.gridColumn4.Width = 112;
            // 
            // gridColumn5
            // 
            this.gridColumn5.Caption = "ARFCN";
            this.gridColumn5.FieldName = "Arfcn";
            this.gridColumn5.Name = "gridColumn5";
            this.gridColumn5.Visible = true;
            this.gridColumn5.VisibleIndex = 4;
            // 
            // gridColumn6
            // 
            this.gridColumn6.Caption = "PCI";
            this.gridColumn6.FieldName = "Pci";
            this.gridColumn6.Name = "gridColumn6";
            this.gridColumn6.Visible = true;
            this.gridColumn6.VisibleIndex = 5;
            // 
            // gridColumn7
            // 
            this.gridColumn7.Caption = "片区";
            this.gridColumn7.FieldName = "AreaName";
            this.gridColumn7.Name = "gridColumn7";
            this.gridColumn7.Visible = true;
            this.gridColumn7.VisibleIndex = 8;
            this.gridColumn7.Width = 82;
            // 
            // gridColumn8
            // 
            this.gridColumn8.Caption = "Delaunay站间距";
            this.gridColumn8.FieldName = "DistanceByDelaunay";
            this.gridColumn8.Name = "gridColumn8";
            this.gridColumn8.Visible = true;
            this.gridColumn8.VisibleIndex = 9;
            this.gridColumn8.Width = 107;
            // 
            // gridColumn9
            // 
            this.gridColumn9.Caption = "站间距(方向角)";
            this.gridColumn9.FieldName = "DistanceByDir";
            this.gridColumn9.Name = "gridColumn9";
            this.gridColumn9.Visible = true;
            this.gridColumn9.VisibleIndex = 10;
            this.gridColumn9.Width = 100;
            // 
            // gridColumn10
            // 
            this.gridColumn10.Caption = "最小超远间距";
            this.gridColumn10.FieldName = "ProbInfo";
            this.gridColumn10.Name = "gridColumn10";
            this.gridColumn10.Visible = true;
            this.gridColumn10.VisibleIndex = 11;
            this.gridColumn10.Width = 89;
            // 
            // gridColumn11
            // 
            this.gridColumn11.Caption = "经度";
            this.gridColumn11.FieldName = "Longitude";
            this.gridColumn11.Name = "gridColumn11";
            this.gridColumn11.Visible = true;
            this.gridColumn11.VisibleIndex = 12;
            this.gridColumn11.Width = 86;
            // 
            // gridColumn12
            // 
            this.gridColumn12.Caption = "纬度";
            this.gridColumn12.FieldName = "Latitude";
            this.gridColumn12.Name = "gridColumn12";
            this.gridColumn12.Visible = true;
            this.gridColumn12.VisibleIndex = 13;
            this.gridColumn12.Width = 91;
            // 
            // gridColumn13
            // 
            this.gridColumn13.Caption = "相关站点";
            this.gridColumn13.FieldName = "OtherSites";
            this.gridColumn13.Name = "gridColumn13";
            this.gridColumn13.Visible = true;
            this.gridColumn13.VisibleIndex = 14;
            this.gridColumn13.Width = 144;
            // 
            // gridColumn14
            // 
            this.gridColumn14.Caption = "相关小区";
            this.gridColumn14.FieldName = "OtherCells";
            this.gridColumn14.Name = "gridColumn14";
            this.gridColumn14.Visible = true;
            this.gridColumn14.VisibleIndex = 15;
            this.gridColumn14.Width = 147;
            // 
            // xtraTabControl1
            // 
            this.xtraTabControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.xtraTabControl1.Location = new System.Drawing.Point(0, 0);
            this.xtraTabControl1.Name = "xtraTabControl1";
            this.xtraTabControl1.SelectedTabPage = this.xtraTabPage1;
            this.xtraTabControl1.Size = new System.Drawing.Size(1144, 525);
            this.xtraTabControl1.TabIndex = 7;
            this.xtraTabControl1.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.xtraTabPage1,
            this.xtraTabPage2,
            this.xtraTabPage3});
            // 
            // xtraTabPage1
            // 
            this.xtraTabPage1.Controls.Add(this.gridControlFar);
            this.xtraTabPage1.Name = "xtraTabPage1";
            this.xtraTabPage1.Size = new System.Drawing.Size(1137, 495);
            this.xtraTabPage1.Text = "超远站";
            // 
            // xtraTabPage2
            // 
            this.xtraTabPage2.Controls.Add(this.gridControlHigh);
            this.xtraTabPage2.Name = "xtraTabPage2";
            this.xtraTabPage2.Size = new System.Drawing.Size(1137, 495);
            this.xtraTabPage2.Text = "超高站";
            // 
            // gridControlHigh
            // 
            this.gridControlHigh.ContextMenuStrip = this.ctxMenu;
            this.gridControlHigh.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlHigh.Location = new System.Drawing.Point(0, 0);
            this.gridControlHigh.LookAndFeel.SkinName = "Office 2010 Blue";
            this.gridControlHigh.MainView = this.gvHigh;
            this.gridControlHigh.Name = "gridControlHigh";
            this.gridControlHigh.Size = new System.Drawing.Size(1137, 495);
            this.gridControlHigh.TabIndex = 7;
            this.gridControlHigh.UseEmbeddedNavigator = true;
            this.gridControlHigh.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gvHigh});
            // 
            // gvHigh
            // 
            this.gvHigh.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn15,
            this.gridColumn16,
            this.gridColumn17,
            this.gridColumn18,
            this.gridColumn19,
            this.gridColumn20,
            this.gridColumn21,
            this.gridColumn24,
            this.gridColumn25,
            this.gridColumn26,
            this.gridColumn27,
            this.gridColumn28});
            this.gvHigh.GridControl = this.gridControlHigh;
            this.gvHigh.GroupSummary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridGroupSummaryItem(DevExpress.Data.SummaryItemType.Count, "Name", null, "")});
            this.gvHigh.Name = "gvHigh";
            this.gvHigh.OptionsBehavior.Editable = false;
            this.gvHigh.OptionsDetail.EnableMasterViewMode = false;
            this.gvHigh.OptionsDetail.ShowDetailTabs = false;
            this.gvHigh.OptionsView.ColumnAutoWidth = false;
            this.gvHigh.OptionsView.ShowGroupPanel = false;
            this.gvHigh.DoubleClick += new System.EventHandler(this.gvHigh_DoubleClick);
            // 
            // gridColumn15
            // 
            this.gridColumn15.Caption = "类别";
            this.gridColumn15.FieldName = "TypeName";
            this.gridColumn15.Name = "gridColumn15";
            this.gridColumn15.Visible = true;
            this.gridColumn15.VisibleIndex = 0;
            this.gridColumn15.Width = 77;
            // 
            // gridColumn16
            // 
            this.gridColumn16.Caption = "网格";
            this.gridColumn16.FieldName = "GridName";
            this.gridColumn16.Name = "gridColumn16";
            this.gridColumn16.Visible = true;
            this.gridColumn16.VisibleIndex = 1;
            // 
            // gridColumn17
            // 
            this.gridColumn17.Caption = "站点";
            this.gridColumn17.FieldName = "SiteName";
            this.gridColumn17.Name = "gridColumn17";
            this.gridColumn17.Visible = true;
            this.gridColumn17.VisibleIndex = 2;
            this.gridColumn17.Width = 103;
            // 
            // gridColumn18
            // 
            this.gridColumn18.Caption = "小区";
            this.gridColumn18.FieldName = "CellName";
            this.gridColumn18.Name = "gridColumn18";
            this.gridColumn18.Visible = true;
            this.gridColumn18.VisibleIndex = 3;
            this.gridColumn18.Width = 112;
            // 
            // gridColumn19
            // 
            this.gridColumn19.Caption = "ARFCN";
            this.gridColumn19.FieldName = "Arfcn";
            this.gridColumn19.Name = "gridColumn19";
            this.gridColumn19.Visible = true;
            this.gridColumn19.VisibleIndex = 4;
            // 
            // gridColumn20
            // 
            this.gridColumn20.Caption = "PCI";
            this.gridColumn20.FieldName = "Pci";
            this.gridColumn20.Name = "gridColumn20";
            this.gridColumn20.Visible = true;
            this.gridColumn20.VisibleIndex = 5;
            // 
            // gridColumn21
            // 
            this.gridColumn21.Caption = "片区";
            this.gridColumn21.FieldName = "AreaName";
            this.gridColumn21.Name = "gridColumn21";
            this.gridColumn21.Visible = true;
            this.gridColumn21.VisibleIndex = 8;
            this.gridColumn21.Width = 82;
            // 
            // gridColumn24
            // 
            this.gridColumn24.Caption = "天线挂高";
            this.gridColumn24.FieldName = "ProbInfo";
            this.gridColumn24.Name = "gridColumn24";
            this.gridColumn24.Visible = true;
            this.gridColumn24.VisibleIndex = 9;
            this.gridColumn24.Width = 89;
            // 
            // gridColumn25
            // 
            this.gridColumn25.Caption = "经度";
            this.gridColumn25.FieldName = "Longitude";
            this.gridColumn25.Name = "gridColumn25";
            this.gridColumn25.Visible = true;
            this.gridColumn25.VisibleIndex = 10;
            this.gridColumn25.Width = 86;
            // 
            // gridColumn26
            // 
            this.gridColumn26.Caption = "纬度";
            this.gridColumn26.FieldName = "Latitude";
            this.gridColumn26.Name = "gridColumn26";
            this.gridColumn26.Visible = true;
            this.gridColumn26.VisibleIndex = 11;
            this.gridColumn26.Width = 91;
            // 
            // xtraTabPage3
            // 
            this.xtraTabPage3.Controls.Add(this.gridControlNear);
            this.xtraTabPage3.Name = "xtraTabPage3";
            this.xtraTabPage3.Size = new System.Drawing.Size(1137, 495);
            this.xtraTabPage3.Text = "超近站";
            // 
            // gridControlNear
            // 
            this.gridControlNear.ContextMenuStrip = this.ctxMenu;
            this.gridControlNear.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlNear.Location = new System.Drawing.Point(0, 0);
            this.gridControlNear.LookAndFeel.SkinName = "Office 2010 Blue";
            this.gridControlNear.MainView = this.gvNear;
            this.gridControlNear.Name = "gridControlNear";
            this.gridControlNear.Size = new System.Drawing.Size(1137, 495);
            this.gridControlNear.TabIndex = 7;
            this.gridControlNear.UseEmbeddedNavigator = true;
            this.gridControlNear.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gvNear});
            // 
            // gvNear
            // 
            this.gvNear.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn29,
            this.gridColumn30,
            this.gridColumn31,
            this.gridColumn32,
            this.gridColumn33,
            this.gridColumn34,
            this.gridColumn35,
            this.gridColumn36,
            this.gridColumn37,
            this.gridColumn38,
            this.gridColumn39,
            this.gridColumn40,
            this.gridColumn41,
            this.gridColumn42,
            this.gridColumn43,
            this.gridColumn44});
            this.gvNear.GridControl = this.gridControlNear;
            this.gvNear.GroupSummary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridGroupSummaryItem(DevExpress.Data.SummaryItemType.Count, "Name", null, "")});
            this.gvNear.Name = "gvNear";
            this.gvNear.OptionsBehavior.Editable = false;
            this.gvNear.OptionsDetail.EnableMasterViewMode = false;
            this.gvNear.OptionsDetail.ShowDetailTabs = false;
            this.gvNear.OptionsView.ColumnAutoWidth = false;
            this.gvNear.OptionsView.ShowGroupPanel = false;
            this.gvNear.DoubleClick += new System.EventHandler(this.gvNear_DoubleClick);
            // 
            // gridColumn29
            // 
            this.gridColumn29.Caption = "类别";
            this.gridColumn29.FieldName = "TypeName";
            this.gridColumn29.Name = "gridColumn29";
            this.gridColumn29.Visible = true;
            this.gridColumn29.VisibleIndex = 0;
            this.gridColumn29.Width = 77;
            // 
            // gridColumn30
            // 
            this.gridColumn30.Caption = "网格";
            this.gridColumn30.FieldName = "GridName";
            this.gridColumn30.Name = "gridColumn30";
            this.gridColumn30.Visible = true;
            this.gridColumn30.VisibleIndex = 1;
            // 
            // gridColumn31
            // 
            this.gridColumn31.Caption = "站点";
            this.gridColumn31.FieldName = "SiteName";
            this.gridColumn31.Name = "gridColumn31";
            this.gridColumn31.Visible = true;
            this.gridColumn31.VisibleIndex = 2;
            this.gridColumn31.Width = 103;
            // 
            // gridColumn32
            // 
            this.gridColumn32.Caption = "小区";
            this.gridColumn32.FieldName = "CellName";
            this.gridColumn32.Name = "gridColumn32";
            this.gridColumn32.Visible = true;
            this.gridColumn32.VisibleIndex = 3;
            this.gridColumn32.Width = 112;
            // 
            // gridColumn33
            // 
            this.gridColumn33.Caption = "ARFCN";
            this.gridColumn33.FieldName = "Arfcn";
            this.gridColumn33.Name = "gridColumn33";
            this.gridColumn33.Visible = true;
            this.gridColumn33.VisibleIndex = 4;
            // 
            // gridColumn34
            // 
            this.gridColumn34.Caption = "PCI";
            this.gridColumn34.FieldName = "Pci";
            this.gridColumn34.Name = "gridColumn34";
            this.gridColumn34.Visible = true;
            this.gridColumn34.VisibleIndex = 5;
            // 
            // gridColumn35
            // 
            this.gridColumn35.Caption = "片区";
            this.gridColumn35.FieldName = "AreaName";
            this.gridColumn35.Name = "gridColumn35";
            this.gridColumn35.Visible = true;
            this.gridColumn35.VisibleIndex = 8;
            this.gridColumn35.Width = 82;
            // 
            // gridColumn36
            // 
            this.gridColumn36.Caption = "Delaunay站间距";
            this.gridColumn36.FieldName = "DistanceByDelaunay";
            this.gridColumn36.Name = "gridColumn36";
            this.gridColumn36.Visible = true;
            this.gridColumn36.VisibleIndex = 9;
            this.gridColumn36.Width = 107;
            // 
            // gridColumn37
            // 
            this.gridColumn37.Caption = "站间距(方向角)";
            this.gridColumn37.FieldName = "DistanceByDir";
            this.gridColumn37.Name = "gridColumn37";
            this.gridColumn37.Visible = true;
            this.gridColumn37.VisibleIndex = 10;
            this.gridColumn37.Width = 100;
            // 
            // gridColumn38
            // 
            this.gridColumn38.Caption = "超近小区间距";
            this.gridColumn38.FieldName = "ProbInfo";
            this.gridColumn38.Name = "gridColumn38";
            this.gridColumn38.Visible = true;
            this.gridColumn38.VisibleIndex = 11;
            this.gridColumn38.Width = 89;
            // 
            // gridColumn39
            // 
            this.gridColumn39.Caption = "经度";
            this.gridColumn39.FieldName = "Longitude";
            this.gridColumn39.Name = "gridColumn39";
            this.gridColumn39.Visible = true;
            this.gridColumn39.VisibleIndex = 12;
            this.gridColumn39.Width = 86;
            // 
            // gridColumn40
            // 
            this.gridColumn40.Caption = "纬度";
            this.gridColumn40.FieldName = "Latitude";
            this.gridColumn40.Name = "gridColumn40";
            this.gridColumn40.Visible = true;
            this.gridColumn40.VisibleIndex = 13;
            this.gridColumn40.Width = 91;
            // 
            // gridColumn41
            // 
            this.gridColumn41.Caption = "相关站点";
            this.gridColumn41.FieldName = "OtherSites";
            this.gridColumn41.Name = "gridColumn41";
            this.gridColumn41.Visible = true;
            this.gridColumn41.VisibleIndex = 14;
            this.gridColumn41.Width = 144;
            // 
            // gridColumn42
            // 
            this.gridColumn42.Caption = "相关小区";
            this.gridColumn42.FieldName = "OtherCells";
            this.gridColumn42.Name = "gridColumn42";
            this.gridColumn42.Visible = true;
            this.gridColumn42.VisibleIndex = 15;
            this.gridColumn42.Width = 147;
            // 
            // gridColumn22
            // 
            this.gridColumn22.Caption = "TAC";
            this.gridColumn22.FieldName = "TAC";
            this.gridColumn22.Name = "gridColumn22";
            this.gridColumn22.Visible = true;
            this.gridColumn22.VisibleIndex = 6;
            // 
            // gridColumn23
            // 
            this.gridColumn23.Caption = "NCI";
            this.gridColumn23.FieldName = "NCI";
            this.gridColumn23.Name = "gridColumn23";
            this.gridColumn23.Visible = true;
            this.gridColumn23.VisibleIndex = 7;
            // 
            // gridColumn27
            // 
            this.gridColumn27.Caption = "TAC";
            this.gridColumn27.FieldName = "TAC";
            this.gridColumn27.Name = "gridColumn27";
            this.gridColumn27.Visible = true;
            this.gridColumn27.VisibleIndex = 6;
            // 
            // gridColumn28
            // 
            this.gridColumn28.Caption = "NCI";
            this.gridColumn28.FieldName = "NCI";
            this.gridColumn28.Name = "gridColumn28";
            this.gridColumn28.Visible = true;
            this.gridColumn28.VisibleIndex = 7;
            // 
            // gridColumn43
            // 
            this.gridColumn43.Caption = "TAC";
            this.gridColumn43.FieldName = "TAC";
            this.gridColumn43.Name = "gridColumn43";
            this.gridColumn43.Visible = true;
            this.gridColumn43.VisibleIndex = 6;
            // 
            // gridColumn44
            // 
            this.gridColumn44.Caption = "NCI";
            this.gridColumn44.FieldName = "NCI";
            this.gridColumn44.Name = "gridColumn44";
            this.gridColumn44.Visible = true;
            this.gridColumn44.VisibleIndex = 7;
            // 
            // UltraSiteCellFormNR
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1144, 525);
            this.Controls.Add(this.xtraTabControl1);
            this.Name = "UltraSiteCellFormNR";
            this.Text = "三超站点";
            ((System.ComponentModel.ISupportInitialize)(this.gridControlFar)).EndInit();
            this.ctxMenu.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gvFar)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).EndInit();
            this.xtraTabControl1.ResumeLayout(false);
            this.xtraTabPage1.ResumeLayout(false);
            this.xtraTabPage2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlHigh)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvHigh)).EndInit();
            this.xtraTabPage3.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlNear)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvNear)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraGrid.GridControl gridControlFar;
        private DevExpress.XtraGrid.Views.Grid.GridView gvFar;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn6;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn7;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn8;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn9;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn10;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn11;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn12;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn13;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn14;
        private System.Windows.Forms.ContextMenuStrip ctxMenu;
        private System.Windows.Forms.ToolStripMenuItem miExportXlsx;
        private DevExpress.XtraTab.XtraTabControl xtraTabControl1;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage1;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage2;
        private DevExpress.XtraGrid.GridControl gridControlHigh;
        private DevExpress.XtraGrid.Views.Grid.GridView gvHigh;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn15;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn16;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn17;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn18;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn19;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn20;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn21;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn24;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn25;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn26;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage3;
        private DevExpress.XtraGrid.GridControl gridControlNear;
        private DevExpress.XtraGrid.Views.Grid.GridView gvNear;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn29;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn30;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn31;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn32;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn33;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn34;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn35;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn36;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn37;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn38;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn39;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn40;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn41;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn42;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn22;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn23;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn27;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn28;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn43;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn44;
    }
}