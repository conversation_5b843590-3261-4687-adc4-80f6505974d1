﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTMultiGridCoverAnaForm : MinCloseForm
    {
        public ZTMultiGridCoverAnaForm(MainModel mainModel, string strStartTime,bool isShowSample)
            : base(mainModel)
        {
            InitializeComponent();
            this.Text += "   开始时间:" + strStartTime + " _ 结束时间:" + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            this.isShowGridSample = isShowSample;
            if (isShowSample)
            {
                xtraTabPage1.PageVisible = false;
            } 
            else
            {
                xtraTabPage2.PageVisible = false;
            }
        }
        private bool isShowGridSample = false;
        public void FillData(List<MultiGridCoverAnaInfo> lteComprehensiveCoverageRateInfoList
            , List<GridSampleInfo> gridSampleListTmp)
        {
            BindingSource source = new BindingSource();
            source.DataSource = lteComprehensiveCoverageRateInfoList;
            dataGrid.DataSource = source;
            dataGrid.RefreshDataSource();

            BindingSource sourceGrid = new BindingSource();
            sourceGrid.DataSource = gridSampleListTmp;
            gridSample.DataSource = sourceGrid;
            gridSample.RefreshDataSource();
        }

        private void outPutExcel_Click(object sender, EventArgs e)
        {
            if (!isShowGridSample)
            {
                ExcelNPOIManager.ExportToExcel(this.gridView1);
            } 
            else
            {
                ExcelNPOIManager.ExportToExcel(this.gridView2);
            }
        }
    }
}
