using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.Util
{
    public enum CategoryType
    {
        None,
        Enum,
        Bound,
        DateTime,
        Date,
        Year,
        Month,
        Day,
        Time,
        Hour,
        Minute,
        Second,
        Period
    }

    public class Category
    {
        public static Category CreateCategory(int id, string name, string description, CategoryType type)
        {
            if (type == CategoryType.Enum)
            {
                return new CategoryEnum(id, name, description);
            }
            else if (type == CategoryType.Bound)
            {
                return new CategoryBound(id, name, description);
            }
            else
            {
                return new Category(id, name, description, type);
            }
        }

        public static CategoryEnum CreateCategoryEnum(int id, string name, string description)
        {
            return new CategoryEnum(id, name, description);
        }

        protected Category(int id, string name, string description)
        {
            ID = id;
            Name = name;
            Description = description;
        }

        private Category(int id, string name, string description, CategoryType type)
            : this(id, name, description)
        {
            Type = type;
        }

        public int ID { get; set; }

        public string Name { get; set; }

        public string Description { get; set; }

        public virtual CategoryType Type { get; set; }
    }

    public class CategoryEnum : Category
    {
        public CategoryEnum(int id, string name, string description)
            : base(id, name, description)
        {
        }

        public CategoryEnum(int id, string name, string description, CategoryEnumItem item)
            : this(id, name, description)
        {
            Add(item);
        }

        public CategoryEnum(int id, string name, string description, IEnumerable<CategoryEnumItem> items)
            : this(id, name, description)
        {
            Add(items);
        }

        public override CategoryType Type
        {
            get { return CategoryType.Enum; }
        }
        public CategoryEnumItem[] Items
        {
            get {
                items.Sort(delegate(CategoryEnumItem t1, CategoryEnumItem t2) { return t1.Name.CompareTo(t2.Name); });
                return items.ToArray(); }
        }

        public CategoryEnumItem this[int id]
        {
            get { return map.ContainsKey(id) ? map[id] : null; }
        }

        public string GetDescription(int id)
        {
            return map.ContainsKey(id) ? map[id].Description : "ĩ֪" + Description;
        }

        public void Clear()
        {
            items.Clear();
            map.Clear();
        }

        public void Add(CategoryEnumItem item)
        {
            items.Add(item);
            map[item.ID] = item;
        }

        public void Add(IEnumerable<CategoryEnumItem> items)
        {
            this.items.AddRange(items);
        }

        private readonly List<CategoryEnumItem> items = new List<CategoryEnumItem>();

        private readonly Dictionary<int, CategoryEnumItem> map = new Dictionary<int, CategoryEnumItem>();
    }

    public class CategoryBound : Category
    {
        public CategoryBound(int id, string name, string description)
            : base(id, name, description)
        {
        }
        
        public override CategoryType Type
        {
            get { return CategoryType.Bound; }
        }

        public decimal Minimum { get; set; }

        public bool MinimumIncluded { get; set; }

        public decimal Maximum { get; set; }

        public bool MaximumIncluded { get; set; }

        public decimal Increment { get; set; }

        public void SetMinimum(decimal min, bool included)
        {
            Minimum = min;
            MinimumIncluded = included;
        }

        public void SetMaximum(decimal max, bool included)
        {
            Maximum = max;
            MaximumIncluded = included;
        }
    }

    public class CategoryEnumItem
    {
        private CategoryEnumItem parentsItem = null;
        public CategoryEnumItem ParentsItem
        {
            get { return parentsItem; }
        }
        private List<CategoryEnumItem> childrents = null;
        public List<CategoryEnumItem> Childrents
        {
            get { return childrents; }
        }
        public void AddChild(CategoryEnumItem child)
        {
            child.parentsItem = this;
            if (childrents == null)
            {
                childrents = new List<CategoryEnumItem>();
            }
            childrents.Add(child);
        }
        public CategoryEnumItem()
        { 
        
        }

        public CategoryEnumItem(int id, string name, string description)
        {
            ID = id;
            Name = name;
            Description = description;
        }

        public int ID { get; set; }

        public string Name { get; set; }

        public string Description { get; set; }

        public override string ToString()
        {
            return Description;
        }

        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["ID"] = ID;
                param["Name"] = Name;
                param["Description"] = Description;
                return param;
            }

            set
            {
                ID = (int)value["ID"];
                Name = (string)value["Name"];
                Description = (string)value["Description"];
            }
        }
    }

    public class CategoryManager
    {
        public static CategoryManager GetInstance()
        {
            return instance;
        }

        public Category[] Categorys
        {
            get { return categorys.ToArray(); }
        }

        public Category this[int id]
        {
            get { return idMap.ContainsKey(id) ? idMap[id] : null; }
        }

        public Category this[string name]
        {
            get { return nameMap.ContainsKey(name) ? nameMap[name] : null; }
        }

        public void Clear()
        {
            categorys.Clear();
            idMap.Clear();
            nameMap.Clear();
        }

        public void Add(Category category)
        {
            categorys.Add(category);
            idMap[category.ID] = category;
            nameMap[category.Name] = category;
        }

        private static CategoryManager instance = new CategoryManager();

        private readonly List<Category> categorys = new List<Category>();

        private readonly Dictionary<int, Category> idMap = new Dictionary<int, Category>();

        private readonly Dictionary<string, Category> nameMap = new Dictionary<string, Category>();
    }
}
