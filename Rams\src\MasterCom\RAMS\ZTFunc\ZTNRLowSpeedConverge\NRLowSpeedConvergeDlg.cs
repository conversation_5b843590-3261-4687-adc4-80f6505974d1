﻿using MasterCom.RAMS.Func;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NRLowSpeedConvergeDlg : BaseDialog
    {
        public NRLowSpeedConvergeDlg()
        {
            InitializeComponent();
        }

        public void SetCondition(NRLowSpeedConvergeCondition cond)
        {
            if (cond == null)
            {
                cond = new NRLowSpeedConvergeCondition();
            }
            spinEditRxQual.Value = cond.AppSpeed;
            spinEditRadius.Value = cond.Radius;
            spinEditSampleNum.Value = cond.SampleCountLimit;
        }

        public NRLowSpeedConvergeCondition GetCondition()
        {
            NRLowSpeedConvergeCondition cond = new NRLowSpeedConvergeCondition();
            cond.AppSpeed = (int)spinEditRxQual.Value;
            cond.Radius = (int)spinEditRadius.Value;
            cond.SampleCountLimit = (int)spinEditSampleNum.Value;
            return cond;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }
    }

    public class NRLowSpeedConvergeCondition
    {
        public int AppSpeed { get; set; } = 10;
        public int Radius { get; set; } = 50;
        public int SampleCountLimit { get; set; } = 10;
    }
}
