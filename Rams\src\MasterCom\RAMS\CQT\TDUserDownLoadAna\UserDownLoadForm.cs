﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.CQT
{
    public partial class UserDownLoadForm : MinCloseForm
    {
        public UserDownLoadForm(MainModel mainModel)
            :base(mainModel)
        {
            InitializeComponent();

        }

        List<List<NPOIRow>> nrDatasList;
        List<string> sheetNames;

        public void FillData(List<ZTDIYUserDownLoadAna.DownloadEevetInfo> multiDeList, List<ZTDIYUserDownLoadAna.DownloadEevetInfo> onlyAndCompDeList
            , List<List<NPOIRow>> nrDatasList, List<string> sheetNames)
        {
            try
            {
                BindingSource bindingSource1 = new BindingSource();
                bindingSource1.DataSource = multiDeList;
                gridControl1.DataSource = bindingSource1;
                gridControl1.RefreshDataSource();

                BindingSource bindingSource2 = new BindingSource();
                bindingSource2.DataSource = onlyAndCompDeList;
                gridControl2.DataSource = bindingSource2;
                gridControl2.RefreshDataSource();
            }
            catch
            {
                //continue
            }

            this.nrDatasList = nrDatasList;
            this.sheetNames = sheetNames;
        }

        private void miExportExcel_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(nrDatasList, sheetNames);
        }
    }
}
