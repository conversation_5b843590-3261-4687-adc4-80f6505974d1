<?xml version="1.0"?>
<Configs>
  <Config name="StatReports">
    <Item name="reports" typeName="IList">
      <Item typeName="ReporterTemplate">
        <Item name="Param" typeName="IDictionary">
          <Item typeName="String" key="Name">04_天津2G对呼指标统计</Item>
          <Item typeName="Int32" key="KeyCount">2</Item>
          <Item typeName="Int32" key="TimeShowType">-1</Item>
          <Item typeName="IList" key="Columns">
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">测试日期</Item>
              <Item typeName="String" key="Exp">{kTimeValue}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">测试网格</Item>
              <Item typeName="String" key="Exp">{kAreaId}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">网格场景（本地）</Item>
              <Item key="Exp" />
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">维护区域</Item>
              <Item key="Exp" />
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">测试时长（小时)</Item>
              <Item typeName="String" key="Exp">{Mx_0805/(60*60*1000) }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet">
                <Item typeName="Int32">3</Item>
                <Item typeName="Int32">2</Item>
                <Item typeName="Int32">29</Item>
                <Item typeName="Int32">22</Item>
                <Item typeName="Int32">24</Item>
                <Item typeName="Int32">12</Item>
                <Item typeName="Int32">1</Item>
                <Item typeName="Int32">39</Item>
              </Item>
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">测试里程（公里）</Item>
              <Item typeName="String" key="Exp">{Mx_0806/1000 }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet">
                <Item typeName="Int32">3</Item>
                <Item typeName="Int32">2</Item>
                <Item typeName="Int32">29</Item>
                <Item typeName="Int32">22</Item>
                <Item typeName="Int32">24</Item>
                <Item typeName="Int32">12</Item>
                <Item typeName="Int32">1</Item>
                <Item typeName="Int32">39</Item>
              </Item>
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">厂家</Item>
              <Item key="Exp" />
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">LOG名称</Item>
              <Item key="Exp" />
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">覆盖采样点</Item>
              <Item typeName="String" key="Exp">{Mx_5A010217+Mx_5A01020A+Mx_5A010209}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet">
                <Item typeName="Int32">3</Item>
                <Item typeName="Int32">2</Item>
                <Item typeName="Int32">29</Item>
                <Item typeName="Int32">22</Item>
                <Item typeName="Int32">24</Item>
                <Item typeName="Int32">12</Item>
                <Item typeName="Int32">1</Item>
                <Item typeName="Int32">39</Item>
              </Item>
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">总采样点</Item>
              <Item typeName="String" key="Exp">{Mx_5A010201 }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet">
                <Item typeName="Int32">3</Item>
                <Item typeName="Int32">2</Item>
                <Item typeName="Int32">29</Item>
                <Item typeName="Int32">22</Item>
                <Item typeName="Int32">24</Item>
                <Item typeName="Int32">12</Item>
                <Item typeName="Int32">1</Item>
                <Item typeName="Int32">39</Item>
              </Item>
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">RxLevSub</Item>
              <Item typeName="String" key="Exp">{Mx_5A010202}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet">
                <Item typeName="Int32">3</Item>
                <Item typeName="Int32">2</Item>
                <Item typeName="Int32">29</Item>
                <Item typeName="Int32">22</Item>
                <Item typeName="Int32">24</Item>
                <Item typeName="Int32">12</Item>
                <Item typeName="Int32">1</Item>
                <Item typeName="Int32">39</Item>
              </Item>
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">RxlevSub采样点比例（RxlevSub≥-85dBm的采样点数/RxlevSub总采样点数×100%）</Item>
              <Item typeName="String" key="Exp">{100*(Mx_5A010217+Mx_5A01020A+Mx_5A010209)/Mx_5A010201 }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet">
                <Item typeName="Int32">3</Item>
                <Item typeName="Int32">2</Item>
                <Item typeName="Int32">29</Item>
                <Item typeName="Int32">22</Item>
                <Item typeName="Int32">24</Item>
                <Item typeName="Int32">12</Item>
                <Item typeName="Int32">1</Item>
                <Item typeName="Int32">39</Item>
              </Item>
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">RxQualSub</Item>
              <Item typeName="String" key="Exp">{Mx_5A01050B}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet">
                <Item typeName="Int32">3</Item>
                <Item typeName="Int32">2</Item>
                <Item typeName="Int32">29</Item>
                <Item typeName="Int32">22</Item>
                <Item typeName="Int32">24</Item>
                <Item typeName="Int32">12</Item>
                <Item typeName="Int32">1</Item>
                <Item typeName="Int32">39</Item>
              </Item>
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">RxQualSub采样点比例（RxQual0~5级采样点数/总样本点数×100% ）</Item>
              <Item typeName="String" key="Exp">{100*(Mx_5A010501+Mx_5A010502+Mx_5A010503+Mx_5A010504+Mx_5A010505+Mx_5A010506)/Mx_5A01050C }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet">
                <Item typeName="Int32">3</Item>
                <Item typeName="Int32">2</Item>
                <Item typeName="Int32">29</Item>
                <Item typeName="Int32">22</Item>
                <Item typeName="Int32">24</Item>
                <Item typeName="Int32">12</Item>
                <Item typeName="Int32">1</Item>
                <Item typeName="Int32">39</Item>
              </Item>
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">试呼次数</Item>
              <Item typeName="String" key="Exp">{evtIdCount[0]+value9[0]}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet">
                <Item typeName="Int32">3</Item>
                <Item typeName="Int32">2</Item>
                <Item typeName="Int32">29</Item>
                <Item typeName="Int32">22</Item>
                <Item typeName="Int32">24</Item>
                <Item typeName="Int32">12</Item>
                <Item typeName="Int32">1</Item>
                <Item typeName="Int32">39</Item>
              </Item>
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">接通次数</Item>
              <Item typeName="String" key="Exp">{(evtIdCount[0]+value9[0]) - (evtIdCount[7]+evtIdCount[9]+evtIdCount[81]+evtIdCount[87]+value9[7]+value9[9]+value9[81])}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet">
                <Item typeName="Int32">3</Item>
                <Item typeName="Int32">2</Item>
                <Item typeName="Int32">29</Item>
                <Item typeName="Int32">22</Item>
                <Item typeName="Int32">24</Item>
                <Item typeName="Int32">12</Item>
                <Item typeName="Int32">1</Item>
                <Item typeName="Int32">39</Item>
              </Item>
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">掉话次数</Item>
              <Item typeName="String" key="Exp">{evtIdCount[5]+evtIdCount[6]+value9[5]+value9[6]+evtIdCount[906]+evtIdCount[907]+value9[906]+value9[907]}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet">
                <Item typeName="Int32">3</Item>
                <Item typeName="Int32">2</Item>
                <Item typeName="Int32">29</Item>
                <Item typeName="Int32">22</Item>
                <Item typeName="Int32">24</Item>
                <Item typeName="Int32">12</Item>
                <Item typeName="Int32">1</Item>
                <Item typeName="Int32">39</Item>
              </Item>
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">接通率</Item>
              <Item typeName="String" key="Exp">{100.0*((evtIdCount[0]+value9[0]) - (evtIdCount[7]+evtIdCount[9]+evtIdCount[81]+evtIdCount[87]+value9[7]+value9[9]+value9[81]))/(evtIdCount[0]+value9[0])}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet">
                <Item typeName="Int32">3</Item>
                <Item typeName="Int32">2</Item>
                <Item typeName="Int32">29</Item>
                <Item typeName="Int32">22</Item>
                <Item typeName="Int32">24</Item>
                <Item typeName="Int32">12</Item>
                <Item typeName="Int32">1</Item>
                <Item typeName="Int32">39</Item>
              </Item>
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">掉话率</Item>
              <Item typeName="String" key="Exp">{100*(evtIdCount[5]+value9[5]+evtIdCount[6]+value9[6]+evtIdCount[906]+value9[906]+evtIdCount[907]+value9[907])/((evtIdCount[0]+evtIdCount[1]+value9[0]+value9[1])-(evtIdCount[7]+evtIdCount[9]+evtIdCount[81]+evtIdCount[87]+evtIdCount[8]+evtIdCount[10]+evtIdCount[82]+value9[7]+value9[9]+value9[81]+value9[8]+value9[10]+value9[82]))}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet">
                <Item typeName="Int32">3</Item>
                <Item typeName="Int32">2</Item>
                <Item typeName="Int32">29</Item>
                <Item typeName="Int32">22</Item>
                <Item typeName="Int32">24</Item>
                <Item typeName="Int32">12</Item>
                <Item typeName="Int32">1</Item>
                <Item typeName="Int32">39</Item>
              </Item>
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">测试设备</Item>
              <Item key="Exp" />
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
          </Item>
        </Item>
      </Item>
    </Item>
  </Config>
</Configs>