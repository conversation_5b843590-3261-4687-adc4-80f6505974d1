﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class SameEarfcnPciSetConditionDlg
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.simpleButtonOK = new DevExpress.XtraEditors.SimpleButton();
            this.simpleButtonCancel = new DevExpress.XtraEditors.SimpleButton();
            this.chbDisAngle = new System.Windows.Forms.CheckBox();
            this.label1 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.chbFirstNBCell = new System.Windows.Forms.CheckBox();
            this.chbSecondNBCell = new System.Windows.Forms.CheckBox();
            this.label4 = new System.Windows.Forms.Label();
            this.label6 = new System.Windows.Forms.Label();
            this.txbDistance = new DevExpress.XtraEditors.SpinEdit();
            this.txbMinAngle = new DevExpress.XtraEditors.SpinEdit();
            this.txbMaxAngle = new DevExpress.XtraEditors.SpinEdit();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.chbRejectIndoorCell = new System.Windows.Forms.CheckBox();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.xlsFileName = new System.Windows.Forms.TextBox();
            this.btnImport = new System.Windows.Forms.Button();
            this.radioNew = new System.Windows.Forms.RadioButton();
            this.radioOld = new System.Windows.Forms.RadioButton();
            this.btnDownload = new System.Windows.Forms.Button();
            ((System.ComponentModel.ISupportInitialize)(this.txbDistance.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txbMinAngle.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txbMaxAngle.Properties)).BeginInit();
            this.groupBox1.SuspendLayout();
            this.groupBox2.SuspendLayout();
            this.groupBox3.SuspendLayout();
            this.SuspendLayout();
            // 
            // simpleButtonOK
            // 
            this.simpleButtonOK.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.simpleButtonOK.Appearance.Options.UseFont = true;
            this.simpleButtonOK.Location = new System.Drawing.Point(337, 278);
            this.simpleButtonOK.Name = "simpleButtonOK";
            this.simpleButtonOK.Size = new System.Drawing.Size(75, 23);
            this.simpleButtonOK.TabIndex = 6;
            this.simpleButtonOK.Text = "确定";
            this.simpleButtonOK.Click += new System.EventHandler(this.simpleButtonOK_Click);
            // 
            // simpleButtonCancel
            // 
            this.simpleButtonCancel.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.simpleButtonCancel.Appearance.Options.UseFont = true;
            this.simpleButtonCancel.Location = new System.Drawing.Point(433, 278);
            this.simpleButtonCancel.Name = "simpleButtonCancel";
            this.simpleButtonCancel.Size = new System.Drawing.Size(75, 23);
            this.simpleButtonCancel.TabIndex = 7;
            this.simpleButtonCancel.Text = "取消";
            this.simpleButtonCancel.Click += new System.EventHandler(this.simpleButtonCancel_Click);
            // 
            // chbDisAngle
            // 
            this.chbDisAngle.AutoSize = true;
            this.chbDisAngle.Location = new System.Drawing.Point(20, 29);
            this.chbDisAngle.Name = "chbDisAngle";
            this.chbDisAngle.Size = new System.Drawing.Size(72, 16);
            this.chbDisAngle.TabIndex = 8;
            this.chbDisAngle.Text = "距离角度";
            this.chbDisAngle.UseVisualStyleBackColor = true;
            this.chbDisAngle.CheckedChanged += new System.EventHandler(this.chbDisAngle_CheckedChanged);
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(174, 64);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(17, 12);
            this.label1.TabIndex = 9;
            this.label1.Text = "米";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(43, 64);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(47, 12);
            this.label2.TabIndex = 11;
            this.label2.Text = "距离 ≤";
            // 
            // chbFirstNBCell
            // 
            this.chbFirstNBCell.AutoSize = true;
            this.chbFirstNBCell.Location = new System.Drawing.Point(34, 28);
            this.chbFirstNBCell.Name = "chbFirstNBCell";
            this.chbFirstNBCell.Size = new System.Drawing.Size(72, 16);
            this.chbFirstNBCell.TabIndex = 12;
            this.chbFirstNBCell.Text = "一层邻区";
            this.chbFirstNBCell.UseVisualStyleBackColor = true;
            // 
            // chbSecondNBCell
            // 
            this.chbSecondNBCell.AutoSize = true;
            this.chbSecondNBCell.Location = new System.Drawing.Point(34, 57);
            this.chbSecondNBCell.Name = "chbSecondNBCell";
            this.chbSecondNBCell.Size = new System.Drawing.Size(72, 16);
            this.chbSecondNBCell.TabIndex = 13;
            this.chbSecondNBCell.Text = "二层邻区";
            this.chbSecondNBCell.UseVisualStyleBackColor = true;
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(128, 101);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(53, 12);
            this.label4.TabIndex = 11;
            this.label4.Text = "≤夹角≤";
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(265, 101);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(17, 12);
            this.label6.TabIndex = 9;
            this.label6.Text = "度";
            // 
            // txbDistance
            // 
            this.txbDistance.EditValue = new decimal(new int[] {
            85,
            0,
            0,
            -2147483648});
            this.txbDistance.Location = new System.Drawing.Point(91, 59);
            this.txbDistance.Name = "txbDistance";
            this.txbDistance.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.txbDistance.Properties.IsFloatValue = false;
            this.txbDistance.Properties.Mask.EditMask = "N00";
            this.txbDistance.Size = new System.Drawing.Size(77, 21);
            this.txbDistance.TabIndex = 28;
            // 
            // txbMinAngle
            // 
            this.txbMinAngle.EditValue = new decimal(new int[] {
            85,
            0,
            0,
            -2147483648});
            this.txbMinAngle.Location = new System.Drawing.Point(45, 96);
            this.txbMinAngle.Name = "txbMinAngle";
            this.txbMinAngle.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.txbMinAngle.Properties.IsFloatValue = false;
            this.txbMinAngle.Properties.Mask.EditMask = "N00";
            this.txbMinAngle.Size = new System.Drawing.Size(77, 21);
            this.txbMinAngle.TabIndex = 28;
            // 
            // txbMaxAngle
            // 
            this.txbMaxAngle.EditValue = new decimal(new int[] {
            85,
            0,
            0,
            -2147483648});
            this.txbMaxAngle.Location = new System.Drawing.Point(184, 96);
            this.txbMaxAngle.Name = "txbMaxAngle";
            this.txbMaxAngle.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.txbMaxAngle.Properties.IsFloatValue = false;
            this.txbMaxAngle.Properties.Mask.EditMask = "N00";
            this.txbMaxAngle.Size = new System.Drawing.Size(77, 21);
            this.txbMaxAngle.TabIndex = 28;
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.label4);
            this.groupBox1.Controls.Add(this.txbMinAngle);
            this.groupBox1.Controls.Add(this.chbDisAngle);
            this.groupBox1.Controls.Add(this.txbMaxAngle);
            this.groupBox1.Controls.Add(this.label1);
            this.groupBox1.Controls.Add(this.txbDistance);
            this.groupBox1.Controls.Add(this.label6);
            this.groupBox1.Controls.Add(this.label2);
            this.groupBox1.Location = new System.Drawing.Point(26, 133);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(305, 139);
            this.groupBox1.TabIndex = 29;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "距离角度限制";
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.chbFirstNBCell);
            this.groupBox2.Controls.Add(this.chbSecondNBCell);
            this.groupBox2.Location = new System.Drawing.Point(348, 136);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(160, 88);
            this.groupBox2.TabIndex = 30;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "邻区限制";
            // 
            // chbRejectIndoorCell
            // 
            this.chbRejectIndoorCell.AutoSize = true;
            this.chbRejectIndoorCell.Location = new System.Drawing.Point(348, 246);
            this.chbRejectIndoorCell.Name = "chbRejectIndoorCell";
            this.chbRejectIndoorCell.Size = new System.Drawing.Size(96, 16);
            this.chbRejectIndoorCell.TabIndex = 31;
            this.chbRejectIndoorCell.Text = "剔除室内小区";
            this.chbRejectIndoorCell.UseVisualStyleBackColor = true;
            // 
            // groupBox3
            // 
            this.groupBox3.Controls.Add(this.xlsFileName);
            this.groupBox3.Controls.Add(this.btnDownload);
            this.groupBox3.Controls.Add(this.btnImport);
            this.groupBox3.Controls.Add(this.radioNew);
            this.groupBox3.Controls.Add(this.radioOld);
            this.groupBox3.Location = new System.Drawing.Point(26, 12);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.Size = new System.Drawing.Size(482, 98);
            this.groupBox3.TabIndex = 32;
            this.groupBox3.TabStop = false;
            this.groupBox3.Text = "核查模式";
            // 
            // xlsFileName
            // 
            this.xlsFileName.Location = new System.Drawing.Point(137, 65);
            this.xlsFileName.Name = "xlsFileName";
            this.xlsFileName.ReadOnly = true;
            this.xlsFileName.Size = new System.Drawing.Size(168, 21);
            this.xlsFileName.TabIndex = 8;
            // 
            // btnImport
            // 
            this.btnImport.Location = new System.Drawing.Point(311, 62);
            this.btnImport.Name = "btnImport";
            this.btnImport.Size = new System.Drawing.Size(64, 23);
            this.btnImport.TabIndex = 7;
            this.btnImport.Text = "导入文件";
            this.btnImport.UseVisualStyleBackColor = true;
            this.btnImport.Click += new System.EventHandler(this.btnImport_Click);
            // 
            // radioNew
            // 
            this.radioNew.AutoSize = true;
            this.radioNew.Location = new System.Drawing.Point(20, 65);
            this.radioNew.Name = "radioNew";
            this.radioNew.Size = new System.Drawing.Size(95, 16);
            this.radioNew.TabIndex = 6;
            this.radioNew.Text = "新开小区匹配";
            this.radioNew.UseVisualStyleBackColor = true;
            this.radioNew.CheckedChanged += new System.EventHandler(this.radioNew_CheckedChanged);
            // 
            // radioOld
            // 
            this.radioOld.AutoSize = true;
            this.radioOld.Checked = true;
            this.radioOld.Location = new System.Drawing.Point(20, 31);
            this.radioOld.Name = "radioOld";
            this.radioOld.Size = new System.Drawing.Size(83, 16);
            this.radioOld.TabIndex = 1;
            this.radioOld.TabStop = true;
            this.radioOld.Text = "工参自匹配";
            this.radioOld.UseVisualStyleBackColor = true;
            // 
            // btnDownload
            // 
            this.btnDownload.Location = new System.Drawing.Point(381, 62);
            this.btnDownload.Name = "btnDownload";
            this.btnDownload.Size = new System.Drawing.Size(95, 23);
            this.btnDownload.TabIndex = 7;
            this.btnDownload.Text = "下载文件模板";
            this.btnDownload.UseVisualStyleBackColor = true;
            this.btnDownload.Click += new System.EventHandler(this.btnDownload_Click);
            // 
            // SameEarfcnPciSetConditionDlg
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(533, 313);
            this.Controls.Add(this.groupBox3);
            this.Controls.Add(this.chbRejectIndoorCell);
            this.Controls.Add(this.groupBox2);
            this.Controls.Add(this.groupBox1);
            this.Controls.Add(this.simpleButtonCancel);
            this.Controls.Add(this.simpleButtonOK);
            this.Name = "SameEarfcnPciSetConditionDlg";
            this.Text = "LTE工参核查条件设置";
            this.Load += new System.EventHandler(this.SameEarfcnPciSetConditionDlg_Load);
            ((System.ComponentModel.ISupportInitialize)(this.txbDistance.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txbMinAngle.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txbMaxAngle.Properties)).EndInit();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            this.groupBox3.ResumeLayout(false);
            this.groupBox3.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraEditors.SimpleButton simpleButtonOK;
        private DevExpress.XtraEditors.SimpleButton simpleButtonCancel;
        private System.Windows.Forms.CheckBox chbDisAngle;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.CheckBox chbFirstNBCell;
        private System.Windows.Forms.CheckBox chbSecondNBCell;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.Label label6;
        private DevExpress.XtraEditors.SpinEdit txbDistance;
        private DevExpress.XtraEditors.SpinEdit txbMinAngle;
        private DevExpress.XtraEditors.SpinEdit txbMaxAngle;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.CheckBox chbRejectIndoorCell;
        private System.Windows.Forms.GroupBox groupBox3;
        private System.Windows.Forms.TextBox xlsFileName;
        private System.Windows.Forms.Button btnImport;
        private System.Windows.Forms.RadioButton radioNew;
        private System.Windows.Forms.RadioButton radioOld;
        private System.Windows.Forms.Button btnDownload;
    }
}