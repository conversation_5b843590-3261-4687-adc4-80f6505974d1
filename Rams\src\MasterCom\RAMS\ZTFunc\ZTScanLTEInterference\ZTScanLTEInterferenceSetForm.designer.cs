﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTScanLTEInterferenceSetForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.btnCancel = new System.Windows.Forms.Button();
            this.btnOK = new System.Windows.Forms.Button();
            this.numTop1Rxlev = new System.Windows.Forms.NumericUpDown();
            this.label2 = new System.Windows.Forms.Label();
            this.labelUnit = new System.Windows.Forms.Label();
            this.labelDesc = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.spinEditBandEnd = new DevExpress.XtraEditors.SpinEdit();
            this.spinEditBandStart = new DevExpress.XtraEditors.SpinEdit();
            this.labelFreqBand = new System.Windows.Forms.Label();
            this.labelBlock = new System.Windows.Forms.Label();
            this.grpInterferenceType = new System.Windows.Forms.GroupBox();
            this.label11 = new System.Windows.Forms.Label();
            this.numAllRxlev = new System.Windows.Forms.NumericUpDown();
            this.label10 = new System.Windows.Forms.Label();
            this.label9 = new System.Windows.Forms.Label();
            this.label8 = new System.Windows.Forms.Label();
            this.numTopNRxlev = new System.Windows.Forms.NumericUpDown();
            this.label5 = new System.Windows.Forms.Label();
            this.numTopNCount = new System.Windows.Forms.NumericUpDown();
            this.label1 = new System.Windows.Forms.Label();
            this.top1Rxlev = new System.Windows.Forms.Label();
            this.cbxFreqBandType = new DevExpress.XtraEditors.ComboBoxEdit();
            this.label4 = new System.Windows.Forms.Label();
            ((System.ComponentModel.ISupportInitialize)(this.numTop1Rxlev)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditBandEnd.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditBandStart.Properties)).BeginInit();
            this.grpInterferenceType.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numAllRxlev)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numTopNRxlev)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numTopNCount)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbxFreqBandType.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // btnCancel
            // 
            this.btnCancel.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnCancel.Location = new System.Drawing.Point(407, 228);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 16;
            this.btnCancel.Text = "取消";
            this.btnCancel.UseVisualStyleBackColor = true;
            this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
            // 
            // btnOK
            // 
            this.btnOK.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnOK.Location = new System.Drawing.Point(319, 228);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 17;
            this.btnOK.Text = "确定";
            this.btnOK.UseVisualStyleBackColor = true;
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // numTop1Rxlev
            // 
            this.numTop1Rxlev.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numTop1Rxlev.Location = new System.Drawing.Point(249, 26);
            this.numTop1Rxlev.Maximum = new decimal(new int[] {
            10,
            0,
            0,
            -2147483648});
            this.numTop1Rxlev.Minimum = new decimal(new int[] {
            150,
            0,
            0,
            -2147483648});
            this.numTop1Rxlev.Name = "numTop1Rxlev";
            this.numTop1Rxlev.Size = new System.Drawing.Size(82, 21);
            this.numTop1Rxlev.TabIndex = 14;
            this.numTop1Rxlev.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numTop1Rxlev.Value = new decimal(new int[] {
            56,
            0,
            0,
            -2147483648});
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label2.Location = new System.Drawing.Point(28, 63);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(107, 12);
            this.label2.TabIndex = 11;
            this.label2.Text = "下行容量损失(1)：";
            // 
            // labelUnit
            // 
            this.labelUnit.AutoSize = true;
            this.labelUnit.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelUnit.Location = new System.Drawing.Point(336, 30);
            this.labelUnit.Name = "labelUnit";
            this.labelUnit.Size = new System.Drawing.Size(23, 12);
            this.labelUnit.TabIndex = 10;
            this.labelUnit.Text = "dBm";
            // 
            // labelDesc
            // 
            this.labelDesc.AutoSize = true;
            this.labelDesc.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelDesc.Location = new System.Drawing.Point(329, 23);
            this.labelDesc.Name = "labelDesc";
            this.labelDesc.Size = new System.Drawing.Size(65, 12);
            this.labelDesc.TabIndex = 33;
            this.labelDesc.Text = "频段选择：";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label3.Location = new System.Drawing.Point(185, 23);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(11, 12);
            this.label3.TabIndex = 32;
            this.label3.Text = "-";
            // 
            // spinEditBandEnd
            // 
            this.spinEditBandEnd.EditValue = new decimal(new int[] {
            1900,
            0,
            0,
            0});
            this.spinEditBandEnd.Location = new System.Drawing.Point(200, 19);
            this.spinEditBandEnd.Name = "spinEditBandEnd";
            this.spinEditBandEnd.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditBandEnd.Properties.Appearance.Options.UseFont = true;
            this.spinEditBandEnd.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditBandEnd.Properties.Increment = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.spinEditBandEnd.Properties.IsFloatValue = false;
            this.spinEditBandEnd.Properties.Mask.EditMask = "N00";
            this.spinEditBandEnd.Size = new System.Drawing.Size(82, 20);
            this.spinEditBandEnd.TabIndex = 31;
            // 
            // spinEditBandStart
            // 
            this.spinEditBandStart.EditValue = new decimal(new int[] {
            1880,
            0,
            0,
            0});
            this.spinEditBandStart.Location = new System.Drawing.Point(97, 19);
            this.spinEditBandStart.Name = "spinEditBandStart";
            this.spinEditBandStart.Properties.Appearance.BackColor = System.Drawing.Color.White;
            this.spinEditBandStart.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditBandStart.Properties.Appearance.Options.UseBackColor = true;
            this.spinEditBandStart.Properties.Appearance.Options.UseFont = true;
            this.spinEditBandStart.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditBandStart.Properties.Increment = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.spinEditBandStart.Properties.IsFloatValue = false;
            this.spinEditBandStart.Properties.Mask.EditMask = "N00";
            this.spinEditBandStart.Size = new System.Drawing.Size(82, 20);
            this.spinEditBandStart.TabIndex = 30;
            // 
            // labelFreqBand
            // 
            this.labelFreqBand.AutoSize = true;
            this.labelFreqBand.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelFreqBand.Location = new System.Drawing.Point(26, 23);
            this.labelFreqBand.Name = "labelFreqBand";
            this.labelFreqBand.Size = new System.Drawing.Size(65, 12);
            this.labelFreqBand.TabIndex = 29;
            this.labelFreqBand.Text = "频段设置：";
            // 
            // labelBlock
            // 
            this.labelBlock.AutoSize = true;
            this.labelBlock.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelBlock.Location = new System.Drawing.Point(34, 30);
            this.labelBlock.Name = "labelBlock";
            this.labelBlock.Size = new System.Drawing.Size(101, 12);
            this.labelBlock.TabIndex = 34;
            this.labelBlock.Text = "终端接收机阻塞：";
            // 
            // grpInterferenceType
            // 
            this.grpInterferenceType.Controls.Add(this.label11);
            this.grpInterferenceType.Controls.Add(this.numAllRxlev);
            this.grpInterferenceType.Controls.Add(this.label10);
            this.grpInterferenceType.Controls.Add(this.label9);
            this.grpInterferenceType.Controls.Add(this.label8);
            this.grpInterferenceType.Controls.Add(this.numTopNRxlev);
            this.grpInterferenceType.Controls.Add(this.label5);
            this.grpInterferenceType.Controls.Add(this.numTopNCount);
            this.grpInterferenceType.Controls.Add(this.label1);
            this.grpInterferenceType.Controls.Add(this.top1Rxlev);
            this.grpInterferenceType.Controls.Add(this.labelBlock);
            this.grpInterferenceType.Controls.Add(this.numTop1Rxlev);
            this.grpInterferenceType.Controls.Add(this.labelUnit);
            this.grpInterferenceType.Controls.Add(this.label2);
            this.grpInterferenceType.Location = new System.Drawing.Point(14, 58);
            this.grpInterferenceType.Name = "grpInterferenceType";
            this.grpInterferenceType.Size = new System.Drawing.Size(468, 146);
            this.grpInterferenceType.TabIndex = 35;
            this.grpInterferenceType.TabStop = false;
            this.grpInterferenceType.Text = "干扰条件设置";
            // 
            // label11
            // 
            this.label11.AutoSize = true;
            this.label11.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label11.Location = new System.Drawing.Point(361, 99);
            this.label11.Name = "label11";
            this.label11.Size = new System.Drawing.Size(23, 12);
            this.label11.TabIndex = 44;
            this.label11.Text = "dBm";
            // 
            // numAllRxlev
            // 
            this.numAllRxlev.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numAllRxlev.Location = new System.Drawing.Point(272, 95);
            this.numAllRxlev.Maximum = new decimal(new int[] {
            10,
            0,
            0,
            -2147483648});
            this.numAllRxlev.Minimum = new decimal(new int[] {
            150,
            0,
            0,
            -2147483648});
            this.numAllRxlev.Name = "numAllRxlev";
            this.numAllRxlev.Size = new System.Drawing.Size(82, 21);
            this.numAllRxlev.TabIndex = 43;
            this.numAllRxlev.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numAllRxlev.Value = new decimal(new int[] {
            105,
            0,
            0,
            -2147483648});
            // 
            // label10
            // 
            this.label10.AutoSize = true;
            this.label10.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label10.Location = new System.Drawing.Point(141, 99);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(131, 12);
            this.label10.TabIndex = 42;
            this.label10.Text = "频带内所有信号均值 ≥";
            // 
            // label9
            // 
            this.label9.AutoSize = true;
            this.label9.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label9.Location = new System.Drawing.Point(28, 99);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(107, 12);
            this.label9.TabIndex = 41;
            this.label9.Text = "下行容量损失(2)：";
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label8.Location = new System.Drawing.Point(426, 63);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(23, 12);
            this.label8.TabIndex = 40;
            this.label8.Text = "dBm";
            // 
            // numTopNRxlev
            // 
            this.numTopNRxlev.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numTopNRxlev.Location = new System.Drawing.Point(337, 59);
            this.numTopNRxlev.Maximum = new decimal(new int[] {
            10,
            0,
            0,
            -2147483648});
            this.numTopNRxlev.Minimum = new decimal(new int[] {
            150,
            0,
            0,
            -2147483648});
            this.numTopNRxlev.Name = "numTopNRxlev";
            this.numTopNRxlev.Size = new System.Drawing.Size(82, 21);
            this.numTopNRxlev.TabIndex = 39;
            this.numTopNRxlev.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numTopNRxlev.Value = new decimal(new int[] {
            95,
            0,
            0,
            -2147483648});
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label5.Location = new System.Drawing.Point(249, 63);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(83, 12);
            this.label5.TabIndex = 38;
            this.label5.Text = "强信号全部 ≥";
            // 
            // numTopNCount
            // 
            this.numTopNCount.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numTopNCount.Location = new System.Drawing.Point(197, 59);
            this.numTopNCount.Maximum = new decimal(new int[] {
            30,
            0,
            0,
            0});
            this.numTopNCount.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numTopNCount.Name = "numTopNCount";
            this.numTopNCount.Size = new System.Drawing.Size(45, 21);
            this.numTopNCount.TabIndex = 37;
            this.numTopNCount.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numTopNCount.Value = new decimal(new int[] {
            10,
            0,
            0,
            0});
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label1.Location = new System.Drawing.Point(141, 63);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(53, 12);
            this.label1.TabIndex = 36;
            this.label1.Text = "频带内前";
            // 
            // top1Rxlev
            // 
            this.top1Rxlev.AutoSize = true;
            this.top1Rxlev.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.top1Rxlev.Location = new System.Drawing.Point(141, 30);
            this.top1Rxlev.Name = "top1Rxlev";
            this.top1Rxlev.Size = new System.Drawing.Size(107, 12);
            this.top1Rxlev.TabIndex = 35;
            this.top1Rxlev.Text = "频带内最强信号 ≥";
            // 
            // cbxFreqBandType
            // 
            this.cbxFreqBandType.Location = new System.Drawing.Point(393, 19);
            this.cbxFreqBandType.Name = "cbxFreqBandType";
            this.cbxFreqBandType.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cbxFreqBandType.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            this.cbxFreqBandType.Size = new System.Drawing.Size(70, 21);
            this.cbxFreqBandType.TabIndex = 36;
            this.cbxFreqBandType.SelectedIndexChanged += new System.EventHandler(this.cbxFreqBandType_SelectedIndexChanged);
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label4.Location = new System.Drawing.Point(288, 23);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(23, 12);
            this.label4.TabIndex = 37;
            this.label4.Text = "MHz";
            // 
            // ZTScanLTEInterferenceSetForm
            // 
            this.Appearance.BackColor = System.Drawing.SystemColors.Control;
            this.Appearance.Options.UseBackColor = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(506, 274);
            this.Controls.Add(this.label4);
            this.Controls.Add(this.cbxFreqBandType);
            this.Controls.Add(this.grpInterferenceType);
            this.Controls.Add(this.labelDesc);
            this.Controls.Add(this.label3);
            this.Controls.Add(this.spinEditBandEnd);
            this.Controls.Add(this.spinEditBandStart);
            this.Controls.Add(this.labelFreqBand);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "ZTScanLTEInterferenceSetForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "LTE干扰分析";
            ((System.ComponentModel.ISupportInitialize)(this.numTop1Rxlev)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditBandEnd.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditBandStart.Properties)).EndInit();
            this.grpInterferenceType.ResumeLayout(false);
            this.grpInterferenceType.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numAllRxlev)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numTopNRxlev)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numTopNCount)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbxFreqBandType.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.Button btnOK;
        private System.Windows.Forms.NumericUpDown numTop1Rxlev;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label labelUnit;
        private System.Windows.Forms.Label labelDesc;
        private System.Windows.Forms.Label label3;
        private DevExpress.XtraEditors.SpinEdit spinEditBandEnd;
        private DevExpress.XtraEditors.SpinEdit spinEditBandStart;
        private System.Windows.Forms.Label labelFreqBand;
        private System.Windows.Forms.Label labelBlock;
        private System.Windows.Forms.GroupBox grpInterferenceType;
        private System.Windows.Forms.Label top1Rxlev;
        private System.Windows.Forms.Label label11;
        private System.Windows.Forms.NumericUpDown numAllRxlev;
        private System.Windows.Forms.Label label10;
        private System.Windows.Forms.Label label9;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.NumericUpDown numTopNRxlev;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.NumericUpDown numTopNCount;
        private System.Windows.Forms.Label label1;
        private DevExpress.XtraEditors.ComboBoxEdit cbxFreqBandType;
        private System.Windows.Forms.Label label4;

    }
}