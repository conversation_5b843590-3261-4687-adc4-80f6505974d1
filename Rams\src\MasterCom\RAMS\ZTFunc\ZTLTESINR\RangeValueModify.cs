﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc.ZTLTESINR
{
    public partial class RangeValueModify : BaseDialog
    {
        public RangeValueModify(float min, float max, int oper, string kpiname)
        {
            InitializeComponent();
            numMin.Value = (decimal)min;
            numMax.Value = (decimal)max;
            Oper = oper;           
            if (oper == (int)EnumOperatorsType.GreaterThan)
            {
                labelRange.Text = "<" + kpiname ;
                numMax.Visible = false;
            }
            else if (oper == (int)EnumOperatorsType.EqualOrLessThan)
            {
                labelRange.Text = kpiname + "≤";
                numMax.Value = (decimal)min;
                numMin.Visible = false;
            }
            else
            {
                labelRange.Text = "<" + kpiname + "≤";
                
            }            
        }
        public int Oper { get; set; }
        public float min { get; set; } = 0;
        public float max { get; set; } = 0;
        private void buttonOK_Click(object sender, EventArgs e)
        {
            if ((int)numMax.Value <= (int)numMin.Value && numMin.Visible && numMax.Visible)
            {
                MessageBox.Show("最大值应大于最小值", "错误");
                return;
            }
            min = (float)numMin.Value;
            max = (float)numMax.Value;
            DialogResult = DialogResult.OK;
        }

        private void buttonCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
        }

     
    }
}
