﻿using MasterCom.RAMS.Func;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.CQT
{
    public partial class CQTProblemType : BaseDialog
    {
        public CQTProblemType(List<string> dBName)
        {
            InitializeComponent();
            foreach (string db in dBName)
            {
                cbCQTType.Properties.Items.Add(db);
            }
            if (cbCQTType.Properties.Items.Count > 0)
                cbCQTType.SelectedIndex = 0;
        }

        public string strCQTProblemType
        {
            get
            {
                if (cbCQTType.Properties.Items.Count == 0)
                    return "";
                else
                    return cbCQTType.SelectedItem.ToString();
            }
        }
        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = System.Windows.Forms.DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = System.Windows.Forms.DialogResult.Cancel;
        }
    }
}
