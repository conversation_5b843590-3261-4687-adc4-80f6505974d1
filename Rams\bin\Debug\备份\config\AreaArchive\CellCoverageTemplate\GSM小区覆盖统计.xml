<?xml version="1.0"?>
<Configs>
  <Config name="Template">
    <Item name="Options" typeName="IDictionary">
      <Item typeName="String" key="Name">GSM小区覆盖统计</Item>
      <Item typeName="IList" key="Columns">
        <Item typeName="IDictionary">
          <Item typeName="String" key="Caption">采样点个数</Item>
          <Item typeName="String" key="Expression">{Mx_0807}</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="MoMtFlag">0</Item>
          <Item typeName="Boolean" key="CalcProportion">True</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="Int32" key="StaticColor">-16711936</Item>
          <Item typeName="IList" key="ColorParam" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Caption">RxLevSub</Item>
          <Item typeName="String" key="Expression">{Mx_5A010202}</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="MoMtFlag">0</Item>
          <Item typeName="Boolean" key="CalcProportion">False</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="Int32" key="StaticColor">-16711936</Item>
          <Item typeName="IList" key="ColorParam" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Caption">RxQual</Item>
          <Item typeName="String" key="Expression">{100*(Mx_5A010501+Mx_5A010502+Mx_5A010503+Mx_5A010504+Mx_5A010505+Mx_5A010506)/Mx_5A01050C }%</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="MoMtFlag">0</Item>
          <Item typeName="Boolean" key="CalcProportion">False</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">True</Item>
          <Item typeName="Int32" key="StaticColor">-16711936</Item>
          <Item typeName="IList" key="ColorParam">
            <Item typeName="IDictionary">
              <Item typeName="Single" key="Min">0</Item>
              <Item typeName="Single" key="Max">98</Item>
              <Item typeName="Boolean" key="MinIncluded">True</Item>
              <Item typeName="Boolean" key="MaxIncluded">False</Item>
              <Item typeName="String" key="DescInfo" />
              <Item typeName="Int32" key="Value">-65536</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="Single" key="Min">98</Item>
              <Item typeName="Single" key="Max">100</Item>
              <Item typeName="Boolean" key="MinIncluded">True</Item>
              <Item typeName="Boolean" key="MaxIncluded">True</Item>
              <Item typeName="String" key="DescInfo" />
              <Item typeName="Int32" key="Value">-16744448</Item>
            </Item>
          </Item>
        </Item>
      </Item>
    </Item>
  </Config>
</Configs>