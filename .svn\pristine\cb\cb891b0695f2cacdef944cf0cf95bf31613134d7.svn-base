﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using Chris.Util;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using WorkFlowUI;
using MFrame.Interface;
using MasterCom.RAMS.Frame;
#if NopTask
using MasterCom.NOP.RAMS.Extend.Command;
using MasterCom.NOP.Report;
using MasterCom.NOP.DataSet;
#endif

namespace MasterCom.RAMS.NOP
{
    public partial class TaskOrderForm : MinCloseForm
    {
        public TaskOrderForm()
            : base()
        {
            InitializeComponent();
            VisibleChanged += TaskOrderForm_VisibleChanged;
        }

        public TaskOrderForm(int workFlowVersionID) : this()
        {
            this.workFlowVersionID = workFlowVersionID;
        }

        void TaskOrderForm_VisibleChanged(object sender, EventArgs e)
        {
            VisibleChanged -= TaskOrderForm_VisibleChanged;
            Application.Idle += Application_Idle;
        }

        void Application_Idle(object sender, EventArgs e)
        {
            Application.Idle -= Application_Idle;
            if (NopCfgMngr.Instance.NopServer != null)
            {
                this.iPbox.Text = NopCfgMngr.Instance.NopServer.Ip;
                this.numPort.Value = NopCfgMngr.Instance.NopServer.Port;
                if (!string.IsNullOrEmpty(NopCfgMngr.Instance.NopServer.Ip))
                {
                    init();
                }
            }
        }
#if NopTask
        MasterCom.NOP.DataSet.Schema schema = null;
        private void initSchema()
        {
            if (schema==null)
            {
                MasterCom.NOP.DataSet.DataSetNetAdapter adapter = new MasterCom.NOP.DataSet.DataSetNetAdapter(server.Hostname, server.Port, server.Password, "mastercom");
                schema = adapter.GetSchemaByID(1);
            }
        }
#endif
        MFrame.Interface.Server server;
        private void init()
        {
            lblDesc.Text = "正在初始化工单页面，请稍候...";
            pnlServerInfo.Enabled = false;

            Dictionary<string, object> param = new Dictionary<string, object>();
            MasterCom.NOP.Frame.MainModel mainModel = new MasterCom.NOP.Frame.MainModel();


            MFrame.Interface.Server serverInstance = Singleton<MFrame.Interface.Server>.GetInstance();
            if (NopCfgMngr.Instance.NopServer != null)
            {
                serverInstance.Hostname = NopCfgMngr.Instance.NopServer.Ip;//NOP服务端IP地址
                serverInstance.Port = NopCfgMngr.Instance.NopServer.Port;//NOP服务端默认端口
                serverInstance.UserName = MainModel.User.LoginName;
                serverInstance.Password = NopCfgMngr.Instance.Password;
            }
            server = serverInstance;
            mainModel.Server = serverInstance;
            MasterCom.NOP.UM.UserManagerQuery um = new MasterCom.NOP.UM.UserManagerQuery(server.Hostname, server.Port, server.UserName, server.Password);
            um.UserName = server.UserName;
            if (um.Execute())
            {
                MasterCom.NOP.UM.User user = um.UserManager.GetUserByName(server.UserName);
                mainModel.User = user;
            }
            param["MainModel"] = mainModel;

            Config moduleConfig = new Config();
            moduleConfig["WorkFlowVersionID"] = workFlowVersionID;//要查询的版本ID
            param["ModuleParam"] = moduleConfig;

            ConditionTaskListGrid grid = null;
            try
            {
                grid = new ConditionTaskListGrid();
                grid.Param = param;
                if (!grid.IsDisposed)
                {
                    pnlServerInfo.Visible = false;
                    lblDesc.Visible = false;
                    grid.Dock = DockStyle.Fill;
                    this.Controls.Add(grid);
#if NopTask
                    CmdGetTaskFileInfo.FileInfoGot += CmdGetTaskFileInfo_FileInfoGot;
                    CmdShowTaskESResultInfo.QueryTaskNodeInfo += CmdShowTaskESResultInfo_QueryTaskNodeInfo;
                    CmdDiyGetTaskInfo.TaskDetailInfoGot += CmdDiyGetTaskInfo_TaskDetailInfoGot;
#endif
                }
                else
                {
                    pnlServerInfo.Enabled = true;
                    lblDesc.Text = "无法查看工单，请联系管理员";
                    lblDesc.ForeColor = Color.Red;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message + Environment.NewLine + ex.ToString());
            }
        }

        protected override void Dispose(bool disposing)
        {
#if NopTask
            CmdGetTaskFileInfo.FileInfoGot -= CmdGetTaskFileInfo_FileInfoGot;
            CmdShowTaskESResultInfo.QueryTaskNodeInfo -= CmdShowTaskESResultInfo_QueryTaskNodeInfo;
            CmdDiyGetTaskInfo.TaskDetailInfoGot -= CmdDiyGetTaskInfo_TaskDetailInfoGot;
#endif
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

#if NopTask
        void CmdGetTaskFileInfo_FileInfoGot(object sender, TaskFileInfoGotEventArgs e)
        {
            if (!e.Error)
            {
                initSchema();
                FileInfo fi = new FileInfo();
                DateTime beginTime = DateTime.MinValue;
                DateTime endTime = DateTime.MinValue;
                ResultSet resultSet = createResultSet((byte[])e.Param["EventTable"]);
                foreach (MasterCom.NOP.DataSet.Row row in resultSet.Rows)
                {
                    fi.ID = (int)row.GetValue("fileID");
                    fi.LogTable = (string)row.GetValue("logTable");
                    fi.DistrictID = (int)row.GetValue("districtID");
                    beginTime = ((DateTime)row.GetValue("beginTime"));
                    endTime = ((DateTime)row.GetValue("endTime"));
                    fi.ServiceType = (int)row.GetValue("serviceType");
                }
                MasterCom.RAMS.Model.Interface.FileReplayer.ReplayOnePart(
                    fi, new MasterCom.Util.TimePeriod(beginTime, endTime));
            }
            else
            {
                MessageBox.Show("获取工单的文件信息失败！");
            }
        }

        private ResultSet createResultSet(byte[] byteArr)
        {
            initSchema();
            MasterCom.NOP.Report.TemplateInfo info = new MasterCom.NOP.Report.TemplateInfo(schema);
            return ReportNetHelper.BytesToResultSet(byteArr, ref schema, ref info);
        }

        void CmdDiyGetTaskInfo_TaskDetailInfoGot(object sender, TaskInfoGotEventArgs e)
        {
            Dictionary<string, object> param = e.Param;
            if (!param.ContainsKey("OperCode"))
            {
                return;
            }
            int operCode = (int)param["OperCode"];
            switch (operCode)
            {
                case 1://对比回放
                    replayFiles(param);
                    break;
                default:
                    break;
            }
        }

        enum OrderSvcType
        {
            LTEDATA,
            LTECSFB,
            VoLTE,
            LTESCAN
        }

        private void replayFiles(Dictionary<string, object> param)
        {
            FileInfo fi = new FileInfo();
            DateTime beginTime = DateTime.MinValue;
            DateTime endTime = DateTime.MinValue;
            ResultSet resultSet = createResultSet((byte[])param["EventTable"]);
            string taskName = string.Empty;
            OrderSvcType orderType = OrderSvcType.LTEDATA;

            foreach (MasterCom.NOP.DataSet.Row row in resultSet.Rows)
            {
                fi.ID = (int)row.GetValue("fileID");
                fi.LogTable = (string)row.GetValue("logTable");
                fi.DistrictID = (int)row.GetValue("districtID");
                beginTime = ((DateTime)row.GetValue("beginTime"));
                endTime = ((DateTime)row.GetValue("endTime"));
                fi.ServiceType = (int)row.GetValue("serviceType");
                string orgName = (string)param["OrderName"];
                if (orgName.IndexOf("task_") != -1)
                {
                    taskName = orgName.Substring(5);
                }
                else
                {
                    taskName = orgName;
                }

                if (orgName.Contains("CSFB"))
                {
                    orderType = OrderSvcType.LTECSFB;
                }
                else if (orgName.Contains("VOLTE"))
                {
                    orderType = OrderSvcType.VoLTE;
                }
                else if (orgName.Contains("SCAN"))
                {
                    orderType = OrderSvcType.LTESCAN;
                }
            }
            MasterCom.Util.TimePeriod period = new MasterCom.Util.TimePeriod(beginTime, endTime);
            TaskFileCompareReplay replay = new TaskFileCompareReplay(fi, period, taskName);
            replay.Query();
            QueryCondition cond = replay.GetQueryCondition();
            if (cond.FileInfos.Count >= 2)
            {
                if (cond.FileInfos.Count > 2)
                {
                    foreach (FileInfo f in cond.FileInfos)
                    {
                        if (f.ID != fi.ID)
                        {//多个验证文件时，只呈现主叫文件
                            if ((f.Momt == (int)MoMtFile.MtFlag || f.Name.Contains("被叫")))
                            {
                                MainModel.VisibleOffsetManager.SetFileVisible(f.ID, false);
                            }
                            else
                            {
                                MainModel.VisibleOffsetManager.SetFileOffset(f.ID, 20, 20);
                            }
                        }
                    }
                }
                else
                {
                    MainModel.VisibleOffsetManager.SetFileOffset(cond.FileInfos[1].ID, 20, 20);
                }

                DataTable table = null;
                switch (orderType)
                {
                    case OrderSvcType.LTEDATA:
                        table = KPIStater.StatLTEData(MainModel.DTDataManager.FileDataManagers);
                        break;
                    case OrderSvcType.LTECSFB:
                        table = KPIStater.StatCSFB(MainModel.DTDataManager.FileDataManagers);
                        break;
                    case OrderSvcType.VoLTE:
                        table = KPIStater.StatVoLTE(MainModel.DTDataManager.FileDataManagers);
                        break;
                    case OrderSvcType.LTESCAN:
                        table = KPIStater.StatLTEScan(MainModel.DTDataManager.FileDataManagers);
                        break;
                    default:
                        break;
                }
                KPIForm form = MainModel.CreateResultForm(typeof(KPIForm)) as KPIForm;
                form.FillData(table);
                form.Visible = true;
                form.BringToFront();
                MainModel.MainForm.GetMapForm().updateMap();
            }
        }

        void CmdShowTaskESResultInfo_QueryTaskNodeInfo(object sender, TaskNodeInfoEventArgs e)
        {
            if (e.Param != null)
            {
                ProcRoutineManager.Instance.Init();//初始化
                string eventName = string.Empty;
                int districtID = 0;
                int fileID = 0;
                int eventID = 0;
                int eventSN = 0;
                DateTime date = DateTime.MinValue;
                ResultSet resultSet = createResultSet((byte[])e.Param["EventTable"]);
                foreach (MasterCom.NOP.DataSet.Row row in resultSet.Rows)
                {
                    districtID = (int)row.GetValue("districtID");
                    eventName = (string)row.GetValue("问题点名称");
                    fileID = (int)row.GetValue("fileID");
                    eventID = (int)row.GetValue("eventID");
                    eventSN = (int)row.GetValue("eventSN");
                    date = (DateTime)row.GetValue("问题点时间");
                }
                TaskEventItem taskItem = new TaskEventItem(eventName, districtID, fileID, eventID, eventSN, date);
                ShowTaskESResult showResult = new ShowTaskESResult(taskItem);
                showResult.Query();
            }
            else
            {
                MessageBox.Show("获取工单的预判详情失败！");
            }
        }
#endif

        private void TaskOrderForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            //
        }

        private void btnModifySvrInfo_Click(object sender, EventArgs e)
        {
            if (!iPbox.IsInputValid)
            {
                MessageBox.Show("请输入正确的IP地址！");
                return;
            }
            NopServer nopServer = new NopServer();
            nopServer.Ip = iPbox.Text;
            nopServer.Port = (int)numPort.Value;
            NopCfgMngr.Instance.NopServer = nopServer;
            init();
        }

        int workFlowVersionID = 9;
    }
}
