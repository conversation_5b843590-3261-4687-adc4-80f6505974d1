﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.Util;
using MasterCom.RAMS.NewBlackBlock;

namespace MasterCom.RAMS.Func
{
    public partial class BlackBlockSetConditionForm : BaseDialog
    {
        private Dictionary<string, BlackBlockToken> tokenDic;
        public BlackBlockSetConditionForm()
        {
            InitializeComponent();
            dtStatBegin.Value = Convert.ToDateTime(DateTime.Now.AddMonths(-1).AddDays(1-DateTime.Now.Day).ToString("yyyy-MM-dd"));
            dtStatEnd.Value = Convert.ToDateTime(DateTime.Now.AddMonths(0).AddDays(-DateTime.Now.Day).ToString("yyyy-MM-dd"));

            tokenDic = QueryNewBlackBlock.GetTokenDic();
            chkBlackBlockType.Items.Clear();
            foreach (string token in tokenDic.Keys)
            {
                chkBlackBlockType.Items.Add(tokenDic[token]);
            }

            for (int i = 0; i < chkBlackBlockType.Items.Count; i++)
            {
                chkBlackBlockType.SetItemChecked(i, true);
            }
            cbxAllNone.Checked = true;
        }

        public BlackBlockTimeSetCondition GetCondition()
        {
            BlackBlockTimeSetCondition condition = new BlackBlockTimeSetCondition();
            condition.beginTime = (int)(JavaDate.GetMilliseconds(Convert.ToDateTime(dtStatBegin.Value.ToString("yyyy-MM-dd 00:00:00"))) / 1000L);
            condition.endTime = (int)(JavaDate.GetMilliseconds(Convert.ToDateTime(dtStatEnd.Value.AddDays(1).ToString("yyyy-MM-dd 00:00:00"))) / 1000L);

            for (int i = 0; i < chkBlackBlockType.Items.Count; i++)
            {
                if (chkBlackBlockType.CheckedItems.Contains(chkBlackBlockType.Items[i]))
                {
                    BlackBlockToken token = chkBlackBlockType.Items[i] as BlackBlockToken;
                    condition.AddToken(token);
                }
            }

            return condition;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            if (dtStatBegin.Value > dtStatEnd.Value)
            {
                MessageBox.Show("时间设置有误，请重新设置");
                return;
            }

            if (chkBlackBlockType.CheckedItems.Count == 0)
            {
                MessageBox.Show("请至少设置一个黑点类型");
                return;
            }

            DialogResult = System.Windows.Forms.DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = System.Windows.Forms.DialogResult.Cancel;
        }

        private void cbxAllNone_CheckedChanged(object sender, EventArgs e)
        {
            bool checkAll = cbxAllNone.Checked;

            for (int i = 0; i < chkBlackBlockType.Items.Count; i++)
            {
                chkBlackBlockType.SetItemChecked(i, checkAll);
            }
        }
    }
}
