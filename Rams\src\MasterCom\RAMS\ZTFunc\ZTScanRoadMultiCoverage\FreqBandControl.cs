﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class FreqBandControl : UserControl
    {
        private FreqBandSelectionPanel freqBandPanel;
        private List<CarrierFreqBand> listCarrierFreqBand = new List<CarrierFreqBand>();
        private List<FreqPoint> listFreqPoint;

        //委托事件
        public delegate void ChkChangeDelegate(object sender, EventArgs e);//建立委托
        public ChkChangeDelegate ChkFreqBandChange_click { get; set; }

        public FreqBandControl()
        {
            InitializeComponent();
            //绑定委托事件
            chkFreqBand.CheckedChanged += new EventHandler(chkFreqBand_CheckedChanged);
            getFreqBandCondition();
        }

        private void chkFreqBand_CheckedChanged(object sender, EventArgs e)
        {
            lvFreqBand.Enabled = chkFreqBand.Checked;
            btnFreqBand.Enabled = chkFreqBand.Checked;

            if (ChkFreqBandChange_click != null)
            {
                ChkFreqBandChange_click(sender, e);
            }
        }
        /// <summary>
        /// 得到频段信息
        /// </summary>
        private void getFreqBandCondition()
        {
            lvFreqBand.Items.Clear();
            freqBandPanel = new FreqBandSelectionPanel(toolStripDropDownFreq, lvFreqBand);
            toolStripDropDownFreq.Items.Clear();
            freqBandPanel.FreshItems();
            toolStripDropDownFreq.Items.Add(new ToolStripControlHost(freqBandPanel));
            setDefaultItem();
        }

        /// <summary>
        /// 设置默认选中
        /// </summary>
        private void setDefaultItem()
        {
            listCarrierFreqBand = freqBandPanel.ListCarrierFreqBand;
            //默认选中一个频点
            if (listCarrierFreqBand.Count > 0 && listCarrierFreqBand[0].ListFreqBand.Count > 0 && listCarrierFreqBand[0].ListFreqBand[0].ListFreqPoint.Count > 0)
            {
                FreqPoint fp = listCarrierFreqBand[0].ListFreqBand[0].ListFreqPoint[0];
                ListViewItem lvi = new ListViewItem();
                lvi.Text = fp.Carrier + "_"+ fp.FreqPointName;
                lvi.Tag = fp;
                lvFreqBand.Items.Add(lvi);
            }
        }

        private void btnFreqBand_Click(object sender, EventArgs e)
        {
            Point pt = new Point(btnFreqBand.Width, btnFreqBand.Height);
            toolStripDropDownFreq.Show(btnFreqBand, pt, ToolStripDropDownDirection.BelowLeft);
            //设置对应节点选中
            listFreqPoint = GetListViewItems();
            freqBandPanel.SetNodeChecked(listFreqPoint);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        public List<FreqPoint> GetListViewItems()
        {
            List<FreqPoint> list = new List<FreqPoint>();
            if (lvFreqBand.Items.Count > 0)
            {
                foreach (ListViewItem lv in lvFreqBand.Items)
                {
                    list.Add(lv.Tag as FreqPoint);
                }
            }
            return list;
        }
    }
}
