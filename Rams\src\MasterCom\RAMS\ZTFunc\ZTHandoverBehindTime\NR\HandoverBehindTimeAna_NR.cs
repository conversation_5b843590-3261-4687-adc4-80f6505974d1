﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.ZTFunc
{
    public class HandoverBehindTimeAnaByRegion_NR : HandoverBehindTimeAnaBase_NR
    {
        private static HandoverBehindTimeAnaByRegion_NR instance = null;
        public static HandoverBehindTimeAnaByRegion_NR GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new HandoverBehindTimeAnaByRegion_NR();
                    }
                }
            }
            return instance;
        }

        public override string Name
        {
            get { return "切换不及时(按区域)_NR"; }
        }

    }
    public class HandoverBehindTimeAnaByFile_NR : HandoverBehindTimeAnaBase_NR
    {
        private static HandoverBehindTimeAnaByFile_NR intance = null;
        public static HandoverBehindTimeAnaByFile_NR GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new HandoverBehindTimeAnaByFile_NR();
                    }
                }
            }
            return intance;
        }

        public override string Name
        {
            get { return "切换不及时(按文件)_NR"; }
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.File;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                return true;
            }
            return false;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        /// <summary>
        /// 判断是否在所选区域内
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            return true;
        }
    }
    public class HandoverBehindTimeAnaBase_NR : DIYAnalyseByFileBackgroundBase
    {
        protected static readonly object lockObj = new object();

        List<HandoverBehindTimeInfo_NR> results = null;
        HandoverBehindTimeCondition_NR handoverCond { get; set; } = new HandoverBehindTimeCondition_NR();
        public HandoverBehindTimeAnaBase_NR()
            : base(MainModel.GetInstance())
        {
            ServiceTypes = ServiceTypeManager.GetServiceTypesByServiceName(ServiceName.NR);
            FilterSampleByRegion = false;

            Columns = NRTpHelper.InitBaseReplayParamBackground(true, true);
            Columns.Add("NR_APP_type");
            Columns.Add("NR_lte_Work_Mode");
        }
        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }

            HandoverBehindTimeSettingDlg_NR conditionDlg = new HandoverBehindTimeSettingDlg_NR(handoverCond);
            if (conditionDlg.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                handoverCond = conditionDlg.GetCondition();
                return true;
            }
            return false;
        }
        public override string Name
        {
            get { return "切换不及时分析"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 35000, 35020, this.Name);
        }

        protected override void getReadyBeforeQuery()
        {
            results = new List<HandoverBehindTimeInfo_NR>();
        }
        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager dtFile in MainModel.DTDataManager.FileDataManagers)
            {
                anaNrHandoverInfo(dtFile, handoverCond.HandoverCondition_NR);

                if (handoverCond.IsAnaLteHandover)
                {
                    anaNrHandoverInfo(dtFile, handoverCond.HandoverCondition_LTE);
                }
            }
        }
        private void anaNrHandoverInfo(DTFileDataManager dtFile, NrHandoverBehindTimeCondBase handoverCond)
        {
            HandoverBehindTimeInfo_NR info = null;
            foreach (TestPoint tp in dtFile.TestPoints)
            {
                if (handoverCond.CheckType && handoverCond.GetAppType(tp) == null)
                {
                    continue;
                }
                if (!isValidTestPoint(tp))//非区域内点
                {
                    saveAndResetOneResult(handoverCond, ref info);
                }
                else
                {
                    info = saveValidHandoverBehindTime(dtFile, handoverCond, info, tp);
                }
            }
        }
        private HandoverBehindTimeInfo_NR saveValidHandoverBehindTime(DTFileDataManager dtFile
            , NrHandoverBehindTimeCondBase handoverCond, HandoverBehindTimeInfo_NR info, TestPoint tp)
        {
            float? rsrp = handoverCond.GetSCellRsrp(tp);
            float? maxNCellRsrp = handoverCond.GetMaxNCellRsrp(tp);

            if (handoverCond.IsMatchMaxSvrPccpch(rsrp)
                && handoverCond.IsMatchMinNCellPccpch(maxNCellRsrp)
                && handoverCond.IsMatchMinPccpchDiff(((float)maxNCellRsrp - (float)rsrp)))
            {
                if (info == null)
                {
                    info = new HandoverBehindTimeInfo_NR(handoverCond.Netype);
                }
                info.AddTestPoint(tp, (float)rsrp, (float)maxNCellRsrp);
                if (tp.Equals(dtFile.TestPoints[dtFile.TestPoints.Count - 1]))
                {//文件最后一点，需要把前面的信息保存起来
                    saveAndResetOneResult(handoverCond, ref info);
                }
            }
            else
            {
                saveAndResetOneResult(handoverCond, ref info);
            }

            return info;
        }

        private void saveAndResetOneResult(NrHandoverBehindTimeCondBase handoverCond
            , ref HandoverBehindTimeInfo_NR info)
        {
            if (info == null)
            {
                return;
            }
            if (handoverCond.IsMatchMinStaySeconds(info.StaySeconds))//持续时间判断
            {
                results.Add(info);
                info.SN = results.Count;
                info.FindRoadName();
            }
            info = null;//重置
        }

        protected override void fireShowForm()
        {
            if (results.Count > 0)
            {
                HandoverBehindTimeListForm_NR frm = null;
                frm = MainModel.CreateResultForm(typeof(HandoverBehindTimeListForm_NR)) as HandoverBehindTimeListForm_NR;
                frm.FillData(results);
                frm.Visible = true;
                frm.BringToFront();
                results = new List<HandoverBehindTimeInfo_NR>();
                MainModel.FireSetDefaultMapSerialTheme("NR:SS_RSRP");
            }
            else
            {
                DevExpress.XtraEditors.XtraMessageBox.Show("在设置的条件下，没有符合的数据。请尝试放宽条件。");
            }
        }

        protected override void fireSetDefaultMapSerialTheme()
        {
            MainModel.FireSetDefaultMapSerialTheme("NR", "SS_RSRP");
        }
    }
}
