﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class GSMWeakCoverReasonCondtion
    {
        public GSMWeakCoverReasonCondtion()
        {
            InventoryDic = new Dictionary<string, bool>();
            InventoryDic.Add("缺少基站", true);
            InventoryDic.Add("邻区漏配", true);
            InventoryDic.Add("主服小区覆盖不足", true);
            InventoryDic.Add("邻小区覆盖不足", true);
            InventoryDic.Add("覆盖不稳定", true);
            InventoryDic.Add("室分泄漏", true);
        }

        public float PoorBtsDisGate { get; set; } = 800;
        public float LackNcellDisGate { get; set; } = 700;
        public float RxLevWeakGate { get; set; } = -90;
        public int LastSecondsBeforeWeak { get; set; } = 5;
        public float RxLevUnstabitilyGate { get; set; } = -70;
        public float RxLevMainLessNCellGate { get; set; } = 2.0F;
        public int NCellWeakCountGate { get; set; } = 3;
        public Dictionary<string, bool> InventoryDic { get; set; }
    }
}
