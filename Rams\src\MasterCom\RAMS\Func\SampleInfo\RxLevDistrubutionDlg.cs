﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.Func
{
    public partial class RxLevDistrubutionDlg : BaseDialog
    {
        public RxLevDistrubutionDlg()
        {
            InitializeComponent();
        }

        public void GetCondition(ref int distanceMin, ref int distanceMax)
        {
            distanceMin = (int)edtDistanceMin.Value;
            distanceMax = (int)edtDistanceMax.Value;
        }
    }
}
