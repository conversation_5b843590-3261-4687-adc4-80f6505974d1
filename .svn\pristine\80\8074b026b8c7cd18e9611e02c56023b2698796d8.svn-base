﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTLTECSFBSignalAnaByFile : QueryCoverInfoBase
    {
        protected int evtId_MO_CSFB_LTE_Release = 878;
        protected int evtId_MT_CSFB_LTE_Release = 886;
        protected int evtId_Return_Back_to_LTE_Complete = 884;
        protected int evtId_CSFB_Handover_Success = 1039;//TD-LTE的为GSM Handover Success，FDD-LTE的为WCDMA_HandoverSuccess_Soft
        public ZTLTECSFBSignalAnaByFile(MainModel mainModel)
            : base(mainModel)
        {
        }
        public override MainModel.NeedSearchType needSearchType()
        {
            return MainModel.NeedSearchType.File;
        }

        public virtual int ServiceType
        {
            get { return 33; }
        }

        public override string Name
        {
            get { return "CSFB信令分析"; }
        }

        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22039, this.Name);
        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                return true;
            }
            return false;
        }

        bool error = false;

        private List<CSFBFileInfo> resultList = new List<CSFBFileInfo>();

        protected override void query()
        {
            ClientProxy clientProxy = new ClientProxy();
            List<int> listAlreadyAnyFieldID = new List<int>();
            try
            {
                MainModel.IsDrawEventResult = false;
                MainModel.IsFileReplayByCompareMode = Condition.isCompareMode;

                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }

                resultList = new List<CSFBFileInfo>();

                List<FileInfo> fileInfoSelecteds = new List<FileInfo>();
                fileInfoSelecteds.AddRange(Condition.FileInfos);
                foreach (FileInfo fileInfoSelected in fileInfoSelecteds)
                {
                    if (fileInfoSelected.ServiceType != this.ServiceType)
                    {
                        continue;
                    }
                    FileInfo fileInfo = new FileInfo();
                    fileInfo.ID = fileInfoSelected.ID;
                    fileInfo.LogTable = fileInfoSelected.LogTable;
                    fileInfo.Name = fileInfoSelected.Name;
                    Condition.FileInfos.Clear();
                    Condition.FileInfos.Add(fileInfo);

                    MainModel.DTDataManager.Clear();
                    MainModel.SelectedTestPoints.Clear();
                    MainModel.SelectedEvents.Clear();
                    MainModel.SelectedMessage = null;

                    WaitBox.Show("开始查询数据..", queryInThread, clientProxy);
                    if (!error)
                    {
                        replay();
                        dealMoMtFileRes(listAlreadyAnyFieldID);
                    }
                }
            }
            finally
            {
                clientProxy.Close();
            }

            if (resultList.Count > 0)
            {
                fireShowForm();
            }
            else
            {
                ErrorInfo = "没有找到相关的记录!";
            }
        }

        private void dealMoMtFileRes(List<int> listAlreadyAnyFieldID)
        {
            //主叫或被叫分析过的不再分析
            if (MainModel.DTDataManager.FileDataManagers.Count == 2
                && !listAlreadyAnyFieldID.Contains(MainModel.DTDataManager.FileDataManagers[0].FileID)
                && !listAlreadyAnyFieldID.Contains(MainModel.DTDataManager.FileDataManagers[1].FileID))
            {
                listAlreadyAnyFieldID.Add(MainModel.DTDataManager.FileDataManagers[0].FileID);
                listAlreadyAnyFieldID.Add(MainModel.DTDataManager.FileDataManagers[1].FileID);

                int moFileID = -1;
                moFileID = getMOMTFlag(MainModel.DTDataManager.FileDataManagers[0].Events, MainModel.DTDataManager.FileDataManagers[1].Events);

                //按主被叫的顺序传入

                CSFBFileInfo CSFBFileInfo = new CSFBFileInfo();
                if (moFileID == 0)
                {
                    processEachFile(CSFBFileInfo, MainModel.DTDataManager.FileDataManagers[0], MainModel.DTDataManager.FileDataManagers[1]);
                }
                else if (moFileID == 1)
                {
                    processEachFile(CSFBFileInfo, MainModel.DTDataManager.FileDataManagers[1], MainModel.DTDataManager.FileDataManagers[0]);
                }
                CSFBFileInfo.SN = resultList.Count + 1;
                resultList.Add(CSFBFileInfo);
            }
        }

        private int getMOMTFlag(List<Event> eventList0, List<Event> eventList1)
        {
            int moFileID = -1;
            moFileID = getMoFileID(eventList0, eventList1, moFileID, 0);
            if (moFileID == -1)
            {
                moFileID = getMoFileID(eventList1, eventList0, moFileID, 1);
            }
            return moFileID;
        }

        private int getMoFileID(List<Event> evtList, List<Event> peerEvtList, int moFileID, int id)
        {
            foreach (Event e in evtList)
            {
                if (e.ID == evtId_MO_CSFB_LTE_Release)
                {
                    foreach (Event e2 in peerEvtList)
                    {
                        if (e2.ID == evtId_MT_CSFB_LTE_Release)
                        {
                            moFileID = id;
                            break;
                        }
                    }
                }
            }

            return moFileID;
        }

        protected void fireShowForm()
        {
            ZTLTECSFBSignalAnaListForm listForm = MainModel.GetInstance().GetObjectFromBlackboard(typeof(ZTLTECSFBSignalAnaListForm).FullName) as ZTLTECSFBSignalAnaListForm;
            if (listForm == null || listForm.IsDisposed)
            {
                listForm = new ZTLTECSFBSignalAnaListForm(MainModel);
            }
            listForm.FillData(resultList);
            listForm.Owner = MainModel.MainForm;
            listForm.Visible = true;
        }

        private void replay()
        {
            QueryCondition condition = new QueryCondition();
            condition.isCompareMode = Condition.isCompareMode;
            condition.QueryType = Condition.QueryType;
            condition.FileInfos.AddRange(Condition.FileInfos);
            ZTLTECSFBSignalAnaByFileReplay query = new ZTLTECSFBSignalAnaByFileReplay(MainModel);
            query.SetQueryCondition(condition);
            query.Query();
        }

        protected override void queryInThread(object o)
        {
            try
            {
                System.Threading.Thread.Sleep(20);
                ClientProxy clientProxy = (ClientProxy)o;
                Package package = clientProxy.Package;
                prepareInfoQueryPackage(package, Condition.FileInfos[0]);
                clientProxy.Send();

                FileInfo fi = reciveInfo(clientProxy);
                if (fi == null) throw (new Exception("未能找到当前文件，已忽略文件：" + Condition.FileInfos[0].Name));
                if (fi.EventCount == fi.ID) throw (new Exception("主被叫是同一个文件，已忽略文件：" + Condition.FileInfos[0].Name));

                FileInfo fi2 = new FileInfo();
                fi2.ID = fi.EventCount;
                fi2.ProjectID = fi.ProjectID;
                fi2.SampleTbName = fi.SampleTbName;
                fi2.LogTable = fi.LogTable;
                prepareInfoQueryPackage(package, fi2);
                clientProxy.Send();
                fi = reciveInfo(clientProxy);
                if (fi == null) throw (new Exception("未能找到对端文件，已忽略文件：" + Condition.FileInfos[0].Name));
                fi2.Name = fi.Name;
                log.Info("回放文件ID1：" + fi.ID + ".Id2:" + fi.EventCount);
                Condition.FileInfos.Add(fi2);
                error = false;

            }
            catch (Exception e)
            {
                log.Error(e.Message + "\n" + e.StackTrace);
                ErrorInfo += e.Message;
                error = true;
            }
            finally
            {
                WaitBox.Close();
            }
        }

        protected void prepareInfoQueryPackage(Package package, FileInfo fileInfo)
        {
            package.Command = Command.InfoQuery;
            package.SubCommand = SubCommand.Request;
            package.Content.Type = RequestType.REQTYPE_LOG_SEARCH_BYFILEID;
            package.Content.PrepareAddParam();
            package.Content.AddParam(fileInfo.ID);
            package.Content.AddParam(fileInfo.LogTable);
        }

        protected FileInfo reciveInfo(ClientProxy clientProxy)
        {
            FileInfo fileInfo = null;
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_LOG_SEARCH_BYFILEID)
                {
                    fileInfo = new FileInfo();
                    fileInfo.Fill(package.Content);
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
            }
            return fileInfo;
        }

        private class ZTLTECSFBSignalAnaByFileReplay : DIYReplayFileQuery
        {
            public ZTLTECSFBSignalAnaByFileReplay(MainModel mainModel)
                : base(mainModel)
            {
            }

            protected override void query()
            {
                WaitBox.CanCancel = true;
                WaitBox.Text = "正在查询..";
                ClientProxy clientProxy = new ClientProxy();
                try
                {
                    if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
                    {
                        ErrorInfo = "连接服务器失败!";
                        return;
                    }
                    WaitBox.Text = "正在查询..";
                    MainModel.IsDrawEventResult = false;
                    MainModel.IsFileReplayByCompareMode = Condition.isCompareMode;
                    MainModel.IsFileReplayByMTRMode = condition.isMTRMode;
                    MainModel.IsFileReplayByMTRToLogMode = condition.isMTRToLogMode;
                    WaitBox.Show("开始统计数据..", queryInThread, clientProxy);

                }
                finally
                {
                    clientProxy.Close();
                }
            }

            //int fileIndex = 0;
            protected override void queryInThread(object o)
            {
                try
                {
                    ClientProxy clientProxy = (ClientProxy)o;
                    Package package = clientProxy.Package;
                    int index = 0;
                    foreach (MasterCom.RAMS.Model.FileInfo fileInfo in Condition.FileInfos)
                    {
                        index++;
                        //fileIndex++;
                        if (WaitBox.CancelRequest)
                        {
                            break;
                        }
                        WaitBox.Text = "开始获取[" + fileInfo.Name + "]数据..";
                        queryReplayInfo(clientProxy, package, fileInfo);
                    }
                }
                catch (DebugAssertException dbgE)
                {
                    ErrorInfo += "Debug Assert 失败:" + dbgE.ToString();
                }
                finally
                {
                    WaitBox.Close();
                }
            }

            protected override void queryReplayInfo(ClientProxy clientProxy, Package package, FileInfo fileInfo)
            {
                //event
                prepareStatPackage_Event_FileFilter(package, fileInfo);
                prepareStatPackage_Event_EventFilter(package);
                fillContentNeeded_Event(package);
                clientProxy.Send();
                recieveInfo_Event(clientProxy);

                //message
                prepareStatPackage_Message_FileFilter(package, fileInfo);
                prepareStatPackage_Message_MessageFilter(package);
                fillContentNeeded_Message(package, true);
                clientProxy.Send();
                recieveInfo_Message(clientProxy, true);
            }
        }

        //整个分析过程
        private void processEachFile(CSFBFileInfo CSFBFileInfo, DTFileDataManager moFile, DTFileDataManager mtFile)
        {
            CSFBFileInfo.moFileName = moFile.FileName;
            CSFBFileInfo.mtFileName = mtFile.FileName;

            foreach (Event e in moFile.Events)
            {
                if (e.ID == evtId_MO_CSFB_LTE_Release)    //找到开始
                {
                    long callTail = getTailByReleaseEvent(e, moFile.Events);

                    if (callTail != 0)   //找到了结尾
                    {
                        CSFBCallInfo callInfo = new CSFBCallInfo(e);
                        getAllInfos(callInfo, e, callTail, moFile, mtFile);

                        callInfo.SN = CSFBFileInfo.callList.Count + 1;
                        CSFBFileInfo.callList.Add(callInfo);
                    }
                }
            }
        }

        private long getTailByReleaseEvent(Event releaseEvent, List<Event> moEventList)
        {
            long seqNo = 0;
            if (releaseEvent["Value7"] != null && long.TryParse(releaseEvent["Value7"].ToString(), out seqNo))
            {
                //
            }
            else
            {
                return 0;
            }

            foreach (Event e in moEventList)
            {
                if (e.ID == evtId_Return_Back_to_LTE_Complete
                    && (releaseEvent.Time * 1000L + releaseEvent.Millisecond) <= (e.Time * 1000L + e.Millisecond))
                {
                    long curSn = 0;
                    object obj = e["Value7"];
                    if (obj != null && long.TryParse(obj.ToString(), out curSn)
                        && curSn == seqNo && (e.Time - releaseEvent.Time) < 300)
                    {
                        //限制从回落到LTE到返回LTE时长不超过5分钟
                        return e.Time * 1000L + e.Millisecond;
                    }
                }
            }

            return 0;
        }

        private void getAllInfos(CSFBCallInfo callInfo, Event releaseEvent, long callTail, DTFileDataManager moFile, DTFileDataManager mtFile)
        {
            long callHead = releaseEvent.Time * 1000L + releaseEvent.Millisecond;   //release时间为开始时间

            setMsgInfos(callInfo, callHead, callTail, moFile, mtFile);
            setEvtInfos(callInfo, callHead, callTail, moFile, mtFile);
        }

        protected virtual void setMsgInfos(CSFBCallInfo callInfo, long callHead, long callTail, DTFileDataManager moFile, DTFileDataManager mtFile)
        {
            foreach (MasterCom.RAMS.Model.Message msg in moFile.Messages) //主叫文件
            {
                if ((msg.Time * 1000L + msg.Millisecond) >= callHead && (msg.Time * 1000L + msg.Millisecond) <= callTail)
                {
                    setMoMsgInfo(callInfo, msg);
                }
            }

            foreach (MasterCom.RAMS.Model.Message msg in mtFile.Messages) //被叫文件
            {
                if ((msg.Time * 1000L + msg.Millisecond) >= callHead && (msg.Time * 1000L + msg.Millisecond) <= callTail)
                {
                    setMtMsgInfo(callInfo, msg);
                }
            }
        }

        private static void setMoMsgInfo(CSFBCallInfo callInfo, Model.Message msg)
        {
            if (msg.ID == (int)CSFBMsg.Setup)
            {
                callInfo.moSetupTime = msg.Time * 1000L + msg.Millisecond;
            }
            else if (msg.ID == (int)CSFBMsg.AssignmentComplete)
            {
                callInfo.moAssignmentCompleteTime = msg.Time * 1000L + msg.Millisecond;
            }
        }

        private static void setMtMsgInfo(CSFBCallInfo callInfo, Model.Message msg)
        {
            if (msg.ID == (int)CSFBMsg.RRCConnectionRelease)
            {
                callInfo.mtRrcConnectionReleaseTime = msg.Time * 1000L + msg.Millisecond;
            }
            else if (msg.ID == (int)CSFBMsg.Setup)
            {
                callInfo.mtSetupTime = msg.Time * 1000L + msg.Millisecond;
            }
            else if (msg.ID == (int)CSFBMsg.AssignmentComplete)
            {
                callInfo.mtAssignmentCompleteTime = msg.Time * 1000L + msg.Millisecond;
            }
        }

        private void setEvtInfos(CSFBCallInfo callInfo, long callHead, long callTail, DTFileDataManager moFile, DTFileDataManager mtFile)
        {
            long timeSpan = 100000000;

            foreach (Event e in moFile.Events) //主叫文件
            {
                timeSpan = dealMoEvt(callInfo, callHead, callTail, timeSpan, e);
            }

            timeSpan = 100000000;
            foreach (Event e in mtFile.Events) //被叫文件
            {
                timeSpan = dealMtEvt(callInfo, callHead, callTail, timeSpan, e);
            }
        }

        private long dealMoEvt(CSFBCallInfo callInfo, long callHead, long callTail, long timeSpan, Event e)
        {
            if ((e.Time * 1000L + e.Millisecond) >= callHead && (e.Time * 1000L + e.Millisecond) <= callTail)
            {
                if (e.ID == evtId_MO_CSFB_LTE_Release)
                {
                    callInfo.moLTECellInfo = e.GetSrcCell();
                    callInfo.moLTECell = e["LAC"] + "|" + e["CI"];

                }
                else if (e.ID == evtId_CSFB_Handover_Success)   //切换时的小区
                {
                    if (callInfo.moHOSuccessTime != 0)   //已经取到一次切换，不再取
                    {
                        //do nothing
                    }
                    else
                    {
                        callInfo.moHOSuccessTime = e.Time * 1000L + e.Millisecond;
                        callInfo.moCellHO = e["TargetLAC"] + "|" + e["TargetCI"];   //取切换成功后的小区
                    }
                }
                else  //取距离Setup信令最近的事件中的LAC,CI
                {
                    if (callInfo.moSetupTime != 0
                        && (e.Time * 1000L + e.Millisecond) <= callInfo.moSetupTime
                        && callInfo.moSetupTime - (e.Time * 1000L + e.Millisecond) < timeSpan)
                    {
                        callInfo.moCellInfo = e.GetSrcCell();
                        callInfo.moCell = e["LAC"] + "|" + e["CI"];
                        timeSpan = callInfo.moSetupTime - (e.Time * 1000L + e.Millisecond);
                    }
                }
            }

            return timeSpan;
        }

        private long dealMtEvt(CSFBCallInfo callInfo, long callHead, long callTail, long timeSpan, Event e)
        {
            if ((e.Time * 1000L + e.Millisecond) >= callHead && (e.Time * 1000L + e.Millisecond) <= callTail)
            {
                if (e.ID == evtId_MT_CSFB_LTE_Release)
                {
                    callInfo.mtLTECellInfo = e.GetSrcCell();
                    callInfo.mtLTECell = e["LAC"] + "|" + e["CI"];
                    callInfo.mtReleaseEvent = e;
                }
                else if (e.ID == evtId_CSFB_Handover_Success)   //切换时的小区
                {
                    if (callInfo.mtHOSuccessTime != 0)   //已经取到一次切换，不再取
                    {
                        //do nothing
                    }
                    else
                    {
                        callInfo.mtHOSuccessTime = e.Time * 1000L + e.Millisecond;
                        callInfo.mtCellHO = e["TargetLAC"] + "|" + e["TargetCI"];
                    }
                }
                else  //取距离Setup信令最近的事件中的LAC,CI
                {
                    if (callInfo.mtSetupTime != 0
                        && (e.Time * 1000L + e.Millisecond) <= callInfo.mtSetupTime
                        && callInfo.mtSetupTime - (e.Time * 1000L + e.Millisecond) < timeSpan)
                    {
                        callInfo.mtCellInfo = e.GetSrcCell();
                        callInfo.mtCell = e["LAC"] + "|" + e["CI"];
                        timeSpan = callInfo.mtSetupTime - (e.Time * 1000L + e.Millisecond);
                    }
                }
            }

            return timeSpan;
        }
    }

    public class CSFBFileInfo
    {
        public int SN { get; set; } = 0;
        public string moFileName { get; set; } = "";
        public string mtFileName { get; set; } = "";

        public List<CSFBCallInfo> callList { get; set; } = new List<CSFBCallInfo>();
    }

    public class CSFBCallInfo
    {
        public int SN { get; set; }
        public long moRrcConnectionReleaseTime { get; set; } //主叫RRCConnectionRelease时间
        public long mtRrcConnectionReleaseTime { get; set; } //被叫RRCConnectionRelease时间
        public long moSetupTime { get; set; }                //主叫Setup时间
        public long mtSetupTime { get; set; }                //被叫Setup时间
        public long moAssignmentCompleteTime { get; set; }   //主叫AssignmentComplete时间
        public long mtAssignmentCompleteTime { get; set; }   //被叫AssignmentComplete时间
        public long mtrrcConnectionSetupCompleteTime { get; set; }  //被叫rrcConnectionSetupComplete时间
        public long moHOSuccessTime { get; set; }            //主叫第一次切换时间
        public long mtHOSuccessTime { get; set; }            //被叫第一次切换时间
        public Event moReleaseEvent { get; set; }            //主叫回落事件
        public Event mtReleaseEvent { get; set; }            //被叫回落事件

        public bool isFdd { get; set; } = false;
        public ICell moLTECellInfo { get; set; } //主叫回落Release时的LTE小区信息
        public ICell moCellInfo { get; set; }    //2G 主叫Setup时小区信息
        public ICell mtLTECellInfo { get; set; } //被叫回落Release时的LTE小区信息
        public ICell mtCellInfo { get; set; }    //2G 被叫Setup时小区信息
        public string moLTECell { get; set; }    //主叫回落Release时的LTE小区
        public string mtLTECell { get; set; }    //被叫回落Release时的LTE小区
        public string moCell { get; set; }       //2G 主叫Setup时小区
        public string mtCell { get; set; }       //2G 被叫Setup时小区
        public string moCellHO { get; set; }     //2G 主叫第一个发生切换的小区
        public string mtCellHO { get; set; }     //2G 被叫第一个发生切换的小区

        public CSFBCallInfo(Event moReleaseEvent)
        {
            this.moRrcConnectionReleaseTime = moReleaseEvent.Time * 1000L + moReleaseEvent.Millisecond;
            this.moReleaseEvent = moReleaseEvent;
            this.mtReleaseEvent = null;

            this.SN = 0;
            this.mtRrcConnectionReleaseTime = 0;
            this.moSetupTime = 0;
            this.mtSetupTime = 0;
            this.mtAssignmentCompleteTime = 0;
            this.mtAssignmentCompleteTime = 0;
            this.mtrrcConnectionSetupCompleteTime = 0;
            this.moHOSuccessTime = 0;
            this.mtHOSuccessTime = 0;
            this.moLTECellInfo = null;
            this.moCellInfo = null;
            this.mtLTECellInfo = null;
            this.mtCellInfo = null;
            this.moLTECell = "";
            this.mtLTECell = "";
            this.moCell = "";
            this.mtCell = "";
            this.moCellHO = "";
            this.mtCellHO = "";
        }

        private void splitCellInfo(string total, ref int lac, ref int ci)
        {
            int pos = total.IndexOf("|");
            if (pos > 0)
            {
                lac = Convert.ToInt32(total.Substring(0, pos));
                ci = Convert.ToInt32(total.Substring(pos + 1, total.Length - pos - 1));
            }
        }

        private string getLTECellInfo(string cellInfo)
        {
            int tac = 0;
            int eci = 0;
            splitCellInfo(cellInfo, ref tac, ref eci);

            LTECell cell = CellManager.GetInstance().GetLTECell(moReleaseEvent.DateTime, tac, eci);

            if (cell != null)
            {
                return cell.Name + "__" + cellInfo;
            }
            else
            {
                return cellInfo;
            }
        }

        private string getGSMCellInfo(string cellInfo)
        {
            int lac = 0;
            int ci = 0;
            splitCellInfo(cellInfo, ref lac, ref ci);

            Cell cell = CellManager.GetInstance().GetCell(moReleaseEvent.DateTime, (ushort)lac, (ushort)ci);

            if (cell != null)
            {
                return cell.Name + "__" + cellInfo;
            }
            else
            {
                return cellInfo;
            }
        }


        #region 预处理 
        public string moLTECellProcessed
        {
            get
            {
                return getLTECellInfo(moLTECell);
            }
        }

        public string mtLTECellProcessed
        {
            get
            {
                return getLTECellInfo(mtLTECell);
            }
        }

        public string moCellProcessed
        {
            get
            {
                return getGSMCellInfo(moCell);
            }
        }

        public string mtCellProcessed
        {
            get
            {
                return getGSMCellInfo(mtCell);
            }
        }

        public string moCellHOProcessed
        {
            get
            {
                if (moHOSuccessTime > 0)
                {
                    return getGSMCellInfo(moCellHO);
                }
                else
                {
                    return "";
                }
            }
        }

        public string mtCellHOProcessed
        {
            get
            {
                if (mtHOSuccessTime > 0)
                {
                    return getGSMCellInfo(mtCellHO);
                }
                else
                {
                    return "";
                }
            }
        }

        public string moHODelayProcessed
        {
            get
            {
                if (moHOSuccessTime > 0)
                {
                    if (moAssignmentCompleteTime > 0)
                    {
                        return (moHOSuccessTime - moAssignmentCompleteTime).ToString();
                    }
                    else
                    {
                        return "无AssignmentComplete信令";
                    }
                }
                else
                {
                    return "";
                }
            }
        }

        public string mtHODelayProcessed
        {
            get
            {
                if (mtHOSuccessTime > 0)
                {
                    if (mtAssignmentCompleteTime > 0)
                    {
                        return (mtHOSuccessTime - mtAssignmentCompleteTime).ToString();
                    }
                    else
                    {
                        return "无AssignmentComplete信令";
                    }
                }
                else
                {
                    return "";
                }
            }
        }
        public string mtHODelayProcessedFdd
        {
            get
            {
                if (mtHOSuccessTime > 0)
                {
                    if (mtrrcConnectionSetupCompleteTime > 0)
                    {
                        return ((mtHOSuccessTime - mtrrcConnectionSetupCompleteTime)/1000).ToString();
                    }
                    else
                    {
                        return "无rrcConnectionSetupComplete信令";
                    }
                }
                else
                {
                    return "";
                }
            }
        }

        public string mtReleaseTime
        {
            get
            {
                if (mtRrcConnectionReleaseTime != 0)
                {
                    string milliSecond = mtRrcConnectionReleaseTime.ToString();
                    milliSecond = milliSecond.Substring(10, milliSecond.Length - 10);

                    return JavaDate.GetDateTimeFromMilliseconds(mtRrcConnectionReleaseTime).ToString("yyyy-MM-dd HH:mm:ss") + " " + milliSecond;
                }
                else
                {
                    return "";
                }
            }
        }
        #endregion
    }
    public enum CSFBMsg
    {
        RRCConnectionRelease = MessageManager.LTE_RRC_RRC_Connection_Release,
        Setup = 773 ,
        AssignmentComplete = 1577 ,
        ExtendedServiceRequest = 0x416B074c,
        CmServiceAbort = 1315,
        Alterting = 769 ,
        Disconnect = 805,
        CmServiceRequest=1316,
        PagingReponse=1575
    }
}