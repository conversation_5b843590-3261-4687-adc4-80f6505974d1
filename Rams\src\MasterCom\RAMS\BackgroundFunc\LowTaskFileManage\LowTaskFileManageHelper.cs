﻿using MasterCom.RAMS.Net;
using System;
using System.Collections.Generic;
using System.Data;

namespace MasterCom.RAMS.BackgroundFunc
{
    public class TaskOrderManageCondition
    {
        public string ExcelPath { get; set; }
        public string ImportedExcelPath { get; set; }
        public string WebServicePath { get; set; }
        public string PicPath { get; set; }
        public string AttachPath { get; set; }
        public int TimeDiff { get; set; }
        public int ErrorTimeDiff { get; set; } = 72;
        public int TimeOverlap { get; set; } = 80;
        public int TPOverlap { get; set; } = 80;
        public int TPTimeDiff { get; set; } = 2;
        public int TPDistance { get; set; } = 50;
    }

    public class TaskOrderManageInfo
    {
        public TaskOrderManageInfo(string orderID, string area, DateTime startTime, DateTime endTime, DateTime importTime)
        {
            OrderState = OrderStateType.Nomal;
            OrderFileList = new List<TaskOrderFileInfo>();
            OrderID = orderID;
            Area = area;
            StartDateTime = startTime;
            EndDateTime = endTime;
            ImportDataTime = importTime;
        }

        public OrderStateType OrderState { get; set; }
        public string OrderID { get; private set; }
        public string Area { get; private set; }
        public DateTime StartDateTime { get; private set; }
        public DateTime EndDateTime { get; private set; }
        public DateTime ImportDataTime { get; set; }

        public List<TaskOrderFileInfo> OrderFileList { get; set; }
    }

    public class TaskOrderFileInfo
    {
        public string Area { get; private set; }
        public string OrderID { get; private set; }
        public DateTime StartDateTime { get; private set; }
        public DateTime EndDateTime { get; private set; }
        public DateTime ImportDataTime { get; set; }
        public string FileName { get; set; }
        public string Remarks { get; set; }

        public bool FillDataByExcel(DataRow dr, int row)
        {
            try
            {
                Area = dr[0].ToString();
                if (!Area.Contains("市"))
                {
                    Area += "市";
                }

                OrderID = dr[1].ToString();
                if (string.IsNullOrEmpty(OrderID))
                {
                    return false;
                }

                DateTime dt;
                bool convertResult;
                convertResult = ConvertDateTime(dr[2].ToString(), out dt);
                if (!convertResult)
                {
                    return false;
                }
                StartDateTime = dt;

                convertResult = ConvertDateTime(dr[3].ToString(), out dt);
                if (!convertResult)
                {
                    return false;
                }
                EndDateTime = dt.AddDays(1);

                FileName = dr[4].ToString();
                if (string.IsNullOrEmpty(FileName))
                {
                    return false;
                }
            }
            catch(Exception ex)
            {
                BackgroundFuncManager.GetInstance().ReportBackgroundError($"{row}:{ex.Message}");
                return false;
            }
            return true;
        }

        private bool ConvertDateTime(string str, out DateTime dt)
        {
            string[] dateFormates = { "yyyy-MM-dd", "MM/yyyy/dd", "yyyyMMdd", "yyyy/MM/dd", "yyyy/M/dd", "yyyy/M/d", "M/d/yyyy h/mm/ss", "M/d/yyyy h:mm:ss tt", "M/d/yyyy", "yyyy/m/d h:mm", "yyyy!/m!/d h:mm" };
            bool convertSuccess = true;
            if (DateTime.TryParse(str, out dt))
            {
                dt = dt.Date;
            }
            else if (DateTime.TryParseExact(str, dateFormates, System.Globalization.CultureInfo.InvariantCulture, System.Globalization.DateTimeStyles.None, out dt))//格式比较特殊,尝试自行转换时间
            {
                dt = dt.Date;
            }
            else
            {
                convertSuccess = false;
            }

            return convertSuccess;
        }

        public void FillDataByDB(Package package)
        {
            Area = package.Content.GetParamString();
            OrderID = package.Content.GetParamString();
            StartDateTime = Convert.ToDateTime(package.Content.GetParamString());
            EndDateTime = Convert.ToDateTime(package.Content.GetParamString());
            FileName = package.Content.GetParamString();
            ImportDataTime = Convert.ToDateTime(package.Content.GetParamString());
            Remarks = package.Content.GetParamString();
        }
    }

    public enum OrderStateType
    {
        //工单正常
        Nomal = 1,
        //文件缺失
        LogLoss = -1,
        //文件解析异常
        LogAnalyzeFail = -2,

        //-98是一个自定义类型,文件解析异常,未到达异常文件处理时间,不上传webservice
        AnalyseFail = -98,
        //-99是一个自定义类型,上传webservice失败
        UpLoadFail = -99,
        //-100是一个自定义类型,未到达文件解析时间,不上传webservice
        LogDormant = -100
    }

    public class TaskOrderManageResult
    {
        public TaskOrderManageResult(string orderID)
        {
            OrderID = orderID;
            UpLoadDateTime = System.Data.SqlTypes.SqlDateTime.MinValue.Value;
            Remark = "";
        }

        //工单号
        public string OrderID { get; set; }

        //不同运营商,网络制式对应的里程时长
        public Dictionary<string, Dictionary<string, double>> CarrierNetTypeDistance { get; set; } = new Dictionary<string, Dictionary<string, double>>();
        public Dictionary<string, Dictionary<string, double>> CarrierNetTypeDuration { get; set; } = new Dictionary<string, Dictionary<string, double>>();

        public string CM2GDistance { get; private set; }
        public string CM2GDuration { get; private set; }
        public string CM4GDistance { get; private set; }
        public string CM4GDuration { get; private set; }
        public string DiffNetDistance { get; private set; }
        public string DiffNetDuration { get; private set; }

        //附件名
        public string AttachName { get; set; }
        //附件地址
        public string AttachUrl { get; set; }
        //附件大小
        public long AttachSize { get; set; }
        //上传状态
        public int UpLoadState { get; set; }
        //上传时间
        public DateTime UpLoadDateTime { get; set; }
        //备注
        public string Remark { get; set; }
        
        /// <summary>
        /// 设置对应 运营商-网络制式 的里程时长
        /// </summary>
        /// <param name="distance"></param>
        /// <param name="duration"></param>
        /// <param name="carrierType"></param>
        /// <param name="netType"></param>
        public void SetCarrierNetTypeValue(int distance, int duration, string carrierType, string netType)
        {
            setCarrierNetTypeValue(distance, carrierType, netType, CarrierNetTypeDistance);
            setCarrierNetTypeValue(duration, carrierType, netType, CarrierNetTypeDuration);
        }

        private void setCarrierNetTypeValue(int value, string carrierType, string netType, Dictionary<string, Dictionary<string, double>> dic)
        {
            Dictionary<string, double> netTypeData;
            if (!dic.TryGetValue(carrierType, out netTypeData))
            {
                netTypeData = new Dictionary<string, double>();
                dic.Add(carrierType, netTypeData);
            }

            netTypeData[netType] = value;
        }

        /// <summary>
        /// 将里程时长转为最终输出的结果
        /// </summary>
        public void CalculateResult()
        {
            //里程转为公里
            foreach (var carrieerInfo in CarrierNetTypeDistance)
            {
                List<string> distancList = new List<string>(carrieerInfo.Value.Keys);
                for (int i = 0; i < distancList.Count; i++)
                {
                    double distance = CarrierNetTypeDistance[carrieerInfo.Key][distancList[i]];
                    CarrierNetTypeDistance[carrieerInfo.Key][distancList[i]] = Math.Round(distance / 1000, 2);
                }
            }
            
            //时长转为小时
            foreach (var carrieerInfo in CarrierNetTypeDuration)
            {
                List<string> distancList = new List<string>(carrieerInfo.Value.Keys);
                for (int i = 0; i < distancList.Count; i++)
                {
                    double duration = CarrierNetTypeDuration[carrieerInfo.Key][distancList[i]];
                    CarrierNetTypeDuration[carrieerInfo.Key][distancList[i]] = Math.Round(duration / 3600, 2);
                }
            }

            setValidDistanceDuration();
        }

        private void setValidDistanceDuration()
        {
            CM2GDistance = GetValidData(CarrierNetTypeDistance, LowTaskFileManage.CMCarrierType, LowTaskFileManage.GsmNetType);
            CM4GDistance = GetValidData(CarrierNetTypeDistance, LowTaskFileManage.CMCarrierType, LowTaskFileManage.LteNetType);
            DiffNetDistance = GetValidData(CarrierNetTypeDistance, LowTaskFileManage.DiffNetCarrierType, LowTaskFileManage.DiffNetType);
            CM2GDuration = GetValidData(CarrierNetTypeDuration, LowTaskFileManage.CMCarrierType, LowTaskFileManage.GsmNetType);
            CM4GDuration = GetValidData(CarrierNetTypeDuration, LowTaskFileManage.CMCarrierType, LowTaskFileManage.LteNetType);
            DiffNetDuration = GetValidData(CarrierNetTypeDuration, LowTaskFileManage.DiffNetCarrierType, LowTaskFileManage.DiffNetType);
        }

        /// <summary>
        /// 上传webservice和入库时获取有效的数据
        /// </summary>
        /// <param name="dataDic">里程或时长</param>
        /// <param name="carrierType">运营商类型</param>
        /// <param name="netType">网络制式</param>
        /// <returns></returns>
        public static string GetValidData(Dictionary<string, Dictionary<string, double>> dataDic, string carrierType, string netType)
        {
            if (dataDic.ContainsKey(carrierType))
            {
                Dictionary<string, double> data = dataDic[carrierType];
                if (data.ContainsKey(netType))
                {
                    return data[netType].ToString();
                }
            }

            return "0";
        }
    }

    public class TaskOrderFailAnalyseFile
    {
        public TaskOrderFailAnalyseFile(string orderID)
        {
            OrderID = orderID;
        }

        public string OrderID { get; set; }
        public List<string> FileName { get; set; } = new List<string>();
    }
}
