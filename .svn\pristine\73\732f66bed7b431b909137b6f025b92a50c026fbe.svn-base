﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.Model.RoadProtection
{
    public class DIYQuerydbSetting : DIYSQLBase
    {
        readonly List<int> gsmIDList = new List<int>();
        public List<int> GsmIDList
        {
            get { return gsmIDList; }
        }

        readonly List<DBSetting> dbList = new List<DBSetting>();
        public List<DBSetting> DBList
        {
            get { return dbList; }
        }

        readonly Dictionary<int, GSMData> gsmDic = new Dictionary<int, GSMData>();
        public Dictionary<int, GSMData> GsmDic
        {
            get { return gsmDic; }
        }

        public DIYQuerydbSetting(MainModel mainModel)
            : base(mainModel)
        {
        }
        protected override string getSqlTextString()
        {
            return "SELECT [dbname],[cityname] FROM [DTASYSTEM].[dbo].[tb_cfg_static_dbsetting]";
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[2];
            rType[0] = E_VType.E_String;
            rType[1] = E_VType.E_String;
            return rType;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            dbList.Clear();
            Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    DBSetting db = new DBSetting();
                    db.Dbname = package.Content.GetParamString();
                    db.Cityname = package.Content.GetParamString();

                    dbList.Add(db);
                    //do your code here
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                if (Math.Log(index++) * (100000 / 10000) > WaitBox.ProgressPercent)
                {
                    progress++;
                    if (progress > 95)
                    {
                        progress = 5;
                        index = 0;
                    }
                    if (progress % 5 == 0)
                    {
                        WaitBox.ProgressPercent = progress;
                    }
                }
            }
        }

        public override string Name
        {
            get { return "DIYQueryGSMData"; }
        }
    }
}
