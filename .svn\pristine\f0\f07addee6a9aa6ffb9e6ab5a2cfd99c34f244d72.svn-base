﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Drawing;
using System.Drawing.Imaging;
using MasterCom.RAMS.Model;
using Excel = Microsoft.Office.Interop.Excel;
using MasterCom.Util;
using System.Text.RegularExpressions;
using MasterCom.RAMS.ZTFunc;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.BackgroundFunc
{
    public enum KpiKey
    {
        FtpRsrpAvg = 0x04020000,
        FtpSinrAvg = 0x04020001,

        FtpDlRsrpAvg = 0x04020103,
        FtpDlSinrAvg = 0x04020104,
        FtpDlSpeedAvg = 0x04020105,
        FtpDlPntCount_Rsrp = 0x04020106,
        FtpDlPntCount_Sinr = 0x04020107,
        FtpDlPntCount_Speed = 0x04020108,

        FtpUlRsrpAvg = 0x04020113,
        FtpUlSinrAvg = 0x04020114,
        FtpUlSpeedAvg = 0x04020115,
        FtpUlPntCount_Rsrp = 0x04020116,
        FtpUlPntCount_Sinr = 0x04020117,
        FtpUlPntCount_Speed = 0x04020118,

        FtpDlRsrpAvg_Bad = 0x04020123,
        FtpDlSinrAvg_Bad = 0x04020124,
        FtpDlSpeedAvg_Bad = 0x04020125,
        FtpDlPntCount_Rsrp_Bad = 0x04020126,
        FtpDlPntCount_Sinr_Bad = 0x04020127,
        FtpDlPntCount_Speed_Bad = 0x04020128,

        FtpUlRsrpAvg_Bad = 0x04020133,
        FtpUlSinrAvg_Bad = 0x04020134,
        FtpUlSpeedAvg_Bad = 0x04020135,
        FtpUlPntCount_Rsrp_Bad = 0x04020136,
        FtpUlPntCount_Sinr_Bad = 0x04020137,
        FtpUlPntCount_Speed_Bad = 0x04020138,
        FtpUlCoverPntCount_Valid = 0x04020139,
        FtpUlCoverPntCount_Sum = 0x04020140,

        InHandoverRequestCnt = 0x04040100,
        InHandoverSucceedCnt = 0x04040101,
        RrcRequestCnt = 0x04040102,
        RrcSucceedCnt = 0x04040103,
        ErabRequestCnt = 0x04040104,
        ErabSucceedCnt = 0x04040105,
        AccRequestCnt = 0x04040106,
        AccSucceedCnt = 0x04040107,
        Reselect34RequestCnt = 0x04040108,
        Reselect34SucceedCnt = 0x04040109,
        Reselect24RequestCnt = 0x0404010a,
        Reselect24SucceedCnt = 0x0404010b,
        CsfbRequestCnt = 0x0404010c,
        CsfbSucceedCnt = 0x0404010d,

        FtpUlSpeedMax = 0x0404010e,
        FtpDlSpeedMax = 0x0404010f,
        IndoorCoveredFloor = 0x04040110,
        FtpDlCoverPntCount_Valid = 0x04040111,
        FtpDlCoverPntCount_Sum = 0x04040112,
        WeakLeakoutRate_LockEarfcn = 0x04040113,
        WeakLeakoutRate_Scan = 0x04040114,
        FtpDlCoverPntCount_Valid_Bad = 0x04040115,
        FtpDlCoverPntCount_Sum_Bad = 0x04040116,

        DoubleBalanceRate = 0x04041100,
        SinrRateHigherThan6 = 0x04041101,
        SinrRateHigherThan9 = 0x04041102,
        RsrpB105Sinr6Rate = 0x04041103,
        RsrpB95Sinr9Rate = 0x04041104,
        RsrpRateHigerThanB85 = 0x04041105,
        CsfbSucessRatioAllCall = 0x04041106,//CSFB全程呼叫成功率
        CsfbSucessRatioMoCall = 0x04041107,//CSFB呼叫接通率

        SrvccCallRequestCnt = 0x04041108,
        SrvccCallSucceedCnt = 0x04041109,
        VolteAudioCallRequestCnt = 0x04041110,
        VolteAudioCallSucceedCnt = 0x04041111,
        VolteVideoCallRequestCnt = 0x04041112,
        VolteVideoCallSucceedCnt = 0x04041113,
        CsfbReturnToLteRequestCnt = 0x04041114,
        CsfbReturnToLteCompleteCnt = 0x04041115,
        CsfbReturnToLteTimeDelay = 0x04041116,//单位为秒
        VolteAudioInOutHandTotalCnt = 0x04041117,
        VolteAudioInOutHandSucceedCnt = 0x04041118,
        VolteAudioInHandTotalCnt = 0x04041119,
        VolteAudioInHandSucceedCnt = 0x04041120,
    }
    public abstract class AcpAutoKpiBase
    {
        protected static readonly log4net.ILog log = log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        public virtual Dictionary<KpiKey, object> GetFileKpiInfos(FileInfo fileInfo
            , DTFileDataManager fileManager, LTECell targetCell)
        {
            return new Dictionary<KpiKey, object>();
        }

        public virtual bool IsValidFile(FileInfo fileInfo)
        {
            return false;
        }
        protected LTECell getTpSrcCell(TestPoint tp)
        {
            return MultiStationAutoAcceptManager.GetTpSrcCell(tp);
        }
        protected void reportInfo(string strInfo)
        {
            if (MainModel.GetInstance().BackgroundStarted)
            {
                BackgroundFuncManager.GetInstance().ReportBackgroundInfo(strInfo);
            }
            else
            {
                log.Info(strInfo);
            }
        }
        protected void reportInfo(Exception ex)
        {
            if (MainModel.GetInstance().BackgroundStarted)
            {
                BackgroundFuncManager.GetInstance().ReportBackgroundError(ex);
            }
            else
            {
                System.Windows.Forms.MessageBox.Show(ex.Message + Environment.NewLine + ex.Source + Environment.NewLine + ex.StackTrace);
            }
        }

        protected int? getIntValue(object objValue)
        {
            int? retValue = null;
            if (objValue != null)
            {
                int iValue;
                if (int.TryParse(objValue.ToString(), out iValue))
                {
                    retValue = iValue;
                }
            }
            return retValue;
        }
        protected long? getLongValue(object objValue)
        {
            long? retValue = null;
            if (objValue != null)
            {
                long lValue;
                if (long.TryParse(objValue.ToString(), out lValue))
                {
                    retValue = lValue;
                }
            }
            return retValue;
        }
    }

    #region 室外站
    // 从极好点文件中分析
    class AcpAutoFtpDownload : AcpAutoKpiBase
    {
        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("极好点下载");
        }

        public override Dictionary<KpiKey, object> GetFileKpiInfos(FileInfo fileInfo
            , DTFileDataManager fileManager, LTECell targetCell)
        {
            CellKPI kpiCell = anaFile(fileInfo, fileManager, targetCell);
            if (kpiCell == null)
            {
                reportInfo(string.Format("文件{0}获取目标小区信息失败", fileInfo.Name));
                return new Dictionary<KpiKey, object>();
            }
            return getKpiInfos(kpiCell);
        }
        protected virtual Dictionary<KpiKey, object> getKpiInfos(CellKPI kpiCell)
        {
            Dictionary<KpiKey, object> kpiInfos = new Dictionary<KpiKey, object>();
            kpiInfos.Add(KpiKey.FtpDlRsrpAvg, kpiCell.AvgRsrp);
            kpiInfos.Add(KpiKey.FtpDlSinrAvg, kpiCell.AvgSinr);
            kpiInfos.Add(KpiKey.FtpDlSpeedAvg, kpiCell.AvgDLSpeed);
            kpiInfos.Add(KpiKey.FtpDlCoverPntCount_Valid, kpiCell.PointCount_RsrpB105sinr6);
            kpiInfos.Add(KpiKey.FtpDlCoverPntCount_Sum, kpiCell.PointCount_RsrpAndSinr);
            return kpiInfos;
        }
        protected CellKPI anaFile(FileInfo fileInfo, DTFileDataManager fileManager, LTECell targetCell)
        {
            CellKPI targetKpiCell = null;
            foreach (TestPoint tp in fileManager.TestPoints)
            {
                LTECell cell = getTpSrcCell(tp);
                if (cell == null || cell.Token != targetCell.Token)
                {
                    continue;
                }

                if (targetKpiCell == null)
                {
                    targetKpiCell = new CellKPI(targetCell.Name);
                }
                targetKpiCell.AddPoint(tp);
            }
            return targetKpiCell;
        }

        public class CellKPI
        {
            public CellKPI(string cellName)
            {
                this.CellName = cellName;
                MaxULSpeed = double.MinValue;
            }
            public string CellName { get; set; }
            public int PointCount{ get; private set; }
            public int PointCount_RsrpB105sinr6 { get; private set; }
            public int PointCount_RsrpAndSinr { get; private set; }
            public double AvgSinr
            {
                get { return cntSinr == 0 ? double.MinValue : Math.Round(sumSinr / cntSinr, 2); }
            }
            public double AvgRsrp
            {
                get { return cntRsrp == 0 ? double.MinValue : Math.Round(sumRsrp / cntRsrp, 2); }
            }
            public double MaxULSpeed { get; set; }
            public double AvgULSpeed
            {
                get { return cntULSpeed == 0 ? double.MinValue : Math.Round(sumULSpeed / cntULSpeed, 2); }
            }
            public double AvgDLSpeed
            {
                get { return cntDLSpeed == 0 ? double.MinValue : Math.Round(sumDLSpeed / cntDLSpeed, 2); }
            }
            public double CoverRate
            {
                get 
                {
                    return PointCount_RsrpB105sinr6 == 0 ? double.MinValue : Math.Round(100 * PointCount_RsrpB105sinr6 / (double)PointCount, 2);
                }
            }
            public void AddPoint(TestPoint tp)
            {
                ++PointCount;

                float? sinr = (float?)tp["lte_SINR"];
                if (sinr != null && sinr != -10000000)
                {
                    ++cntSinr;
                    sumSinr += (float)sinr;
                }

                float? rsrp = (float?)tp["lte_RSRP"];
                if (rsrp != null && rsrp != -10000000)
                {
                    ++cntRsrp;
                    sumRsrp += (float)rsrp;
                }

                double? ulSpeed = (double?)tp["lte_PDCP_UL_Mb"];
                if (ulSpeed != null)
                {
                    ++cntULSpeed;
                    sumULSpeed += (double)ulSpeed;
                    MaxULSpeed = MaxULSpeed > (double)ulSpeed ? MaxULSpeed : (double)ulSpeed;
                }

                double? dlSpeed = (double?)tp["lte_PDCP_DL_Mb"];
                if (dlSpeed != null)
                {
                    ++cntDLSpeed;
                    sumDLSpeed += (double)dlSpeed;
                }

                if (sinr != null && rsrp != null)
                {
                    ++PointCount_RsrpAndSinr;
                    if (sinr >= 6 && rsrp >= -105)
                    {
                        ++PointCount_RsrpB105sinr6;
                    }
                }
            }

            private double sumSinr;
            private int cntSinr;

            private double sumRsrp;
            private int cntRsrp;

            private double sumULSpeed;
            private int cntULSpeed;

            private double sumDLSpeed;
            private int cntDLSpeed;
        }
    }

    class AcpAutoFtpUpload : AcpAutoFtpDownload
    {
        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("极好点上传");
        }
        protected override Dictionary<KpiKey, object> getKpiInfos(CellKPI kpiCell)
        {
            Dictionary<KpiKey, object> kpiInfos = new Dictionary<KpiKey, object>();
            kpiInfos.Add(KpiKey.FtpUlRsrpAvg, kpiCell.AvgRsrp);
            kpiInfos.Add(KpiKey.FtpUlSinrAvg, kpiCell.AvgSinr);
            kpiInfos.Add(KpiKey.FtpUlSpeedAvg, kpiCell.AvgULSpeed);
            return kpiInfos;
        }
    }

    class AcpAutoInnerHandover : AcpAutoKpiBase
    {
        public AcpAutoInnerHandover()
        {
            evtRequList = new List<int> { 850, 898 };
            evtSuccList = new List<int> { 851, 899 };
            evtFailList = new List<int> { 870, 1100 };
        }

        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("系统内切换");
        }

        public override Dictionary<KpiKey, object> GetFileKpiInfos(FileInfo fileInfo
            , DTFileDataManager fileManager, LTECell targetCell)
        {
            CellKPI kpiCell = anaFile(fileInfo, fileManager, targetCell);
            if (kpiCell == null)
            {
                reportInfo(string.Format("文件{0}获取目标小区信息失败", fileInfo.Name));
                return new Dictionary<KpiKey, object>();
            }

            return getKpiInfos(kpiCell);
        }
        protected virtual Dictionary<KpiKey, object> getKpiInfos(CellKPI kpiCell)
        {
            Dictionary<KpiKey, object> kpiInfos = new Dictionary<KpiKey, object>();
            kpiInfos.Add(KpiKey.InHandoverRequestCnt, kpiCell.RequestCnt);
            kpiInfos.Add(KpiKey.InHandoverSucceedCnt, kpiCell.SucceedCnt);
            return kpiInfos;
        }
        protected virtual CellKPI anaFile(FileInfo fileInfo, DTFileDataManager fileManager, LTECell targetCell)
        {
            CellKPI kpiCell = new CellKPI(targetCell.Name);
            foreach (Event evt in fileManager.Events)
            {
                if (evtRequList.Contains(evt.ID))
                {
                    ++kpiCell.RequestCnt;
                }
                if (evtSuccList.Contains(evt.ID))
                {
                    ++kpiCell.SucceedCnt;
                }
            }
            return kpiCell;
        }

        protected class CellKPI
        {
            public CellKPI(string cellName)
            {
                this.CellName = cellName;
            }
            public string CellName { get; set; }
            public int RequestCnt { get; set; }
            public int SucceedCnt { get; set; }
        }

        protected List<int> evtSuccList;
        protected List<int> evtFailList;
        protected List<int> evtRequList;
    }

    class AcpAutoRrcRate : AcpAutoInnerHandover
    {
        public AcpAutoRrcRate()
        {
            evtRequList = new List<int> { 855 };
            evtSuccList = new List<int> { 856 };
            evtFailList = new List<int> { 857 };
        }

        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("接入");
        }
        protected override Dictionary<KpiKey, object> getKpiInfos(CellKPI kpiCell)
        {
            Dictionary<KpiKey, object> kpiInfos = new Dictionary<KpiKey, object>();
            kpiInfos.Add(KpiKey.RrcRequestCnt, kpiCell.RequestCnt);
            kpiInfos.Add(KpiKey.RrcSucceedCnt, kpiCell.SucceedCnt);
            return kpiInfos;
        }
    }

    class AcpAutoErabRate : AcpAutoRrcRate
    {
        public AcpAutoErabRate()
        {
            evtRequList = new List<int> { 858 };
            evtSuccList = new List<int> { 859 };
            evtFailList = new List<int> { 897 };
        }
        protected override Dictionary<KpiKey, object> getKpiInfos(CellKPI kpiCell)
        {
            Dictionary<KpiKey, object> kpiInfos = new Dictionary<KpiKey, object>();
            kpiInfos.Add(KpiKey.ErabRequestCnt, kpiCell.RequestCnt);
            kpiInfos.Add(KpiKey.ErabSucceedCnt, kpiCell.SucceedCnt);
            return kpiInfos;
        }
    }

    class AcpAutoAccRate : AcpAutoRrcRate
    {
        public AcpAutoAccRate()
        {
            evtRequList = new List<int> { 22 };
            evtSuccList = new List<int> { 23 };
            evtFailList = new List<int> { 24 };
        }
        protected override Dictionary<KpiKey, object> getKpiInfos(CellKPI kpiCell)
        {
            Dictionary<KpiKey, object> kpiInfos = new Dictionary<KpiKey, object>();
            kpiInfos.Add(KpiKey.AccRequestCnt, kpiCell.RequestCnt);
            kpiInfos.Add(KpiKey.AccSucceedCnt, kpiCell.SucceedCnt);
            return kpiInfos;
        }
    }

    class AcpAuto34ReselectRate : AcpAutoRrcRate
    {
        public AcpAuto34ReselectRate()
        {
            evtFailList = new List<int>();
        }

        public override bool IsValidFile(FileInfo fileInfo)
        {
            if (fileInfo.Name.Contains("34G重选"))
            {
                if (fileInfo.DeviceType == 21 || fileInfo.DeviceType == 8) // 中兴
                {
                    evtRequList = new List<int> { 852 };
                    evtSuccList = new List<int> { 853 };
                }
                else
                {
                    evtRequList = new List<int> { 1308 };
                    evtSuccList = new List<int> { 1308 };
                }
                return true;
            }
            return false;
        }
        protected override Dictionary<KpiKey, object> getKpiInfos(CellKPI kpiCell)
        {
            Dictionary<KpiKey, object> kpiInfos = new Dictionary<KpiKey, object>();
            kpiInfos.Add(KpiKey.Reselect34RequestCnt, kpiCell.RequestCnt);
            kpiInfos.Add(KpiKey.Reselect34SucceedCnt, kpiCell.SucceedCnt);
            return kpiInfos;
        }
    }

    class AcpAuto24ReselectRate : AcpAutoRrcRate
    {
        public AcpAuto24ReselectRate()
        {
            evtFailList = new List<int>();
        }

        public override bool IsValidFile(FileInfo fileInfo)
        {
            if (fileInfo.Name.ToUpper().Contains("24G重选"))
            {
                if (fileInfo.DeviceType == 21 || fileInfo.DeviceType == 8) // 中兴
                {
                    evtRequList = new List<int> { 852 };
                    evtSuccList = new List<int> { 853 };
                }
                else
                {
                    evtRequList = new List<int> { 1306 };
                    evtSuccList = new List<int> { 1306 };
                }
                return true;
            }
            return false;
        }
        protected override Dictionary<KpiKey, object> getKpiInfos(CellKPI kpiCell)
        {
            Dictionary<KpiKey, object> kpiInfos = new Dictionary<KpiKey, object>();
            kpiInfos.Add(KpiKey.Reselect24RequestCnt, kpiCell.RequestCnt);
            kpiInfos.Add(KpiKey.Reselect24SucceedCnt, kpiCell.SucceedCnt);
            return kpiInfos;
        }
    }
    class AcpAutoCsfbRate : AcpAutoInnerHandover
    {
        public AcpAutoCsfbRate()
        {
        }

        //该csfb呼叫成功率算法是新疆现场同其他厂家商定的算法，仅适用于新疆
        public static void GetCsfbEventIdsByDevice(int deviceType, out List<int> evtRequList, out List<int> evtSuccList)
        {
            if (deviceType == 21) // 中兴
            {
                evtRequList = new List<int> { 852 };
                evtSuccList = new List<int> { 853 };
            }
            else
            {
                evtRequList = new List<int> { 877 };
                evtSuccList = new List<int> { 878 };
            }
        }

        public override bool IsValidFile(FileInfo fileInfo)
        {
            if (fileInfo.Momt != (int)MoMtFile.MoFlag || fileInfo.Name.IndexOf("csfb", StringComparison.CurrentCultureIgnoreCase) == -1)
            {
                return false;
            }
            GetCsfbEventIdsByDevice(fileInfo.DeviceType, out evtRequList, out evtSuccList);
            return true;
        }
        protected override Dictionary<KpiKey, object> getKpiInfos(CellKPI kpiCell)
        {
            Dictionary<KpiKey, object> kpiInfos = new Dictionary<KpiKey, object>();
            kpiInfos.Add(KpiKey.CsfbRequestCnt, kpiCell.RequestCnt);
            kpiInfos.Add(KpiKey.CsfbSucceedCnt, kpiCell.SucceedCnt);
            return kpiInfos;
        }
    }

    class AcpAutoCoverPicture : AcpAutoKpiBase
    {
        protected string picFolderPath = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "userdata\\LteStationAcceptance");

        protected static readonly object lockObj = new object();
        private static AcpAutoCoverPicture intance = null;
        public static AcpAutoCoverPicture Instance
        {
            get
            {
                if (intance == null)
                {
                    lock (lockObj)
                    {
                        if (intance == null)
                        {
                            intance = new AcpAutoCoverPicture();
                        }
                    }
                }
                return intance;
            }
        }

        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.ToUpper().Contains("DT上传") || fileInfo.Name.ToUpper().Contains("DT下载");
        }
        public override Dictionary<KpiKey, object> GetFileKpiInfos(FileInfo fileInfo
            , DTFileDataManager fileManager, LTECell targetCell)
        {
            try
            {
                MasterCom.MTGis.DbRect bounds = GetCoverBounds(fileManager, targetCell);
                double nearestDistance;
                TestPoint nearestTp = GetNearestTp(fileManager.TestPoints, targetCell, out nearestDistance);

                if (fileInfo.Name.ToUpper().Contains("DT上传"))
                {
                    FireMapAndTakePic("PDCP_UL_Mb", bounds, nearestTp, targetCell);
                    saveTpDesToTxt(nearestTp, nearestDistance, targetCell, "DT上传");
                }
                else
                {
                    saveTpDesToTxt(nearestTp, nearestDistance, targetCell, "DT下载");
                    FireMapAndTakePic("RSRP", bounds, nearestTp, targetCell);
                    FireMapAndTakePic("SINR", bounds, nearestTp, targetCell);
                    FireMapAndTakePic("PDCP_DL_Mb", bounds, nearestTp, targetCell);
                }
            }
            catch(Exception ex)
            {
                reportInfo(ex);
            }
            return new Dictionary<KpiKey, object>();
        }
        public MasterCom.MTGis.DbRect GetCoverBounds(DTFileDataManager fileManager, LTECell lteCell)
        {
            double lngMin = lteCell.Longitude;
            double lngMax = lteCell.Longitude;
            double latMin = lteCell.Latitude;
            double latMax = lteCell.Latitude;

            foreach (TestPoint tp in fileManager.TestPoints)
            {
                if (tp.Longitude > 70 && tp.Latitude > 3
                    && tp.GetMainCell() == lteCell)
                {
                    lngMin = Math.Min(lngMin, tp.Longitude);
                    lngMax = Math.Max(lngMax, tp.Longitude);
                    latMin = Math.Min(latMin, tp.Latitude);
                    latMax = Math.Max(latMax, tp.Latitude);
                }
            }
            MasterCom.MTGis.DbRect bounds = new MTGis.DbRect(lngMin - 0.001, latMin - 0.001
                , lngMax + 0.001, latMax + 0.001);

            return bounds;
        }
        public MTGis.DbRect GetCoverBounds(DTFileDataManager fileManager)
        {
            double lngMin = 1000;
            double lngMax = -1000;
            double latMin = 1000;
            double latMax = -1000;

            foreach (TestPoint tp in fileManager.TestPoints)
            {
                if (tp.Longitude > 70 && tp.Latitude > 3)
                {
                    lngMin = Math.Min(lngMin, tp.Longitude);
                    lngMax = Math.Max(lngMax, tp.Longitude);
                    latMin = Math.Min(latMin, tp.Latitude);
                    latMax = Math.Max(latMax, tp.Latitude);
                }
            }
            MTGis.DbRect bounds = new MTGis.DbRect(lngMin - 0.001, latMin - 0.001
                , lngMax + 0.001, latMax + 0.001);

            return bounds;
        }

        public string FireMapAndTakePic(string paramName, MasterCom.MTGis.DbRect bounds, TestPoint nearestTp, LTECell srcLteCell)
        {
            MainModel mModel = MainModel.GetInstance();
            mModel.FireSetDefaultMapSerialTheme("LTE_TDD", paramName);
            mModel.DrawFlyLines = false;

            if (srcLteCell == null)
            {
                return "";
            }

            MapForm mf = mModel.MainForm.GetMapForm();
            MapDTLayer layer = mf.GetDTLayer();
            foreach (MapSerialInfo serialInfo in layer.SerialInfos)
            {
                if (serialInfo.Name.Equals(paramName))
                {
                    layer.CurFlyLinesSerialInfo = serialInfo;
                    break;
                }
            }

            mModel.DrawLinesPntToCells = true;
            mModel.PntToCellsDic.Clear();
            List<LongLat> longlatList = new List<LongLat>();
            mModel.PntToCellsDic.Add(nearestTp, longlatList);
            longlatList.Add(new LongLat((float)srcLteCell.EndPointLongitude, (float)srcLteCell.EndPointLatitude));

            mModel.FireDTDataChanged(mModel.MainForm);

            //要让小区显示在采样点之上
            MapLTECellLayer lteLayer = mf.GetLTECellLayer();
            mf.MakeSureCustomLayerVisible(lteLayer, true);
            mf.GoToView(bounds);

            return takePicture(srcLteCell.BTSName, srcLteCell.Name, paramName, "");
        }

        public string FireMapAndTakePicByFunc(string funcName, string paramName, MTGis.DbRect bounds, string btsName, string cellName)
        {
            MainModel mModel = MainModel.GetInstance();
            var mf = mModel.MainForm.GetMapForm();
            mModel.FireSetDefaultMapSerialTheme("LTE_TDD", paramName);
            mModel.DrawDifferentServerColor = true;
            mModel.DrawFlyLines = false;
            mModel.FireDTDataChanged(mModel.MainForm);

            //要让小区显示在采样点之上
            MapLTECellLayer lteLayer = mf.GetLTECellLayer();
            mf.MakeSureCustomLayerVisible(lteLayer, true);
            mf.GoToView(bounds);

            return takePicture(btsName, cellName, paramName, funcName);
        }

        #region 覆盖截图
        private string takePicture(string btsName, string cellName, string paramName, string funcName)
        {
            string filePath = GetCoverPicPath(btsName, cellName, paramName, funcName);

            Bitmap bitMap = MainModel.GetInstance().MainForm.GetMapForm().DrawToBitmapDIY();
            bitMap.Save(filePath, ImageFormat.Png);
            bitMap.Dispose();
            return filePath;
        }

        /// <summary>
        /// 获取小区某种覆盖截图的保存路径
        /// </summary>
        /// <param name="btsName"></param>
        /// <param name="cellName"></param>
        /// <param name="postfix"></param>
        /// <returns></returns>
        public string GetCoverPicPath(string btsName, string cellName, string paramName, string funcName = "")
        {
            string folderPath = GetBtsPicFolder(btsName);
            if (!System.IO.Directory.Exists(folderPath))
            {
                System.IO.Directory.CreateDirectory(folderPath);
            }
            if (string.IsNullOrEmpty(funcName))
            {
                return System.IO.Path.Combine(folderPath, cellName + "_" + paramName + ".png");
            }
            else
            {
                return System.IO.Path.Combine(folderPath, cellName + "_" + paramName + "_" + funcName + ".png");
            }
        }
        /// <summary>
        /// 获取站点覆盖截图保存文件夹地址
        /// </summary>
        /// <param name="btsName"></param>
        /// <returns></returns>
        public string GetBtsPicFolder(string btsName)
        {
            return System.IO.Path.Combine(picFolderPath, btsName.Trim());
        }
        #endregion

        #region 最近的采样点
        public TestPoint GetNearestTp(List<TestPoint> testPoints, LTECell lteCell, out double nearestDistance)
        {
            nearestDistance = double.MaxValue;
            TestPoint nearestTp = null;
            foreach (TestPoint tp in testPoints)
            {
                double curDistance = tp.Distance2(lteCell.Longitude, lteCell.Latitude);
                if (curDistance < nearestDistance)
                {
                    nearestDistance = curDistance;
                    nearestTp = tp;
                }
            }
            return nearestTp;
        }
        public string GetTpDes(TestPoint nearestTp, double minDistance)
        {
            StringBuilder strbTpDes = new StringBuilder();
            strbTpDes.AppendLine("离基站最近的采样点信息:");
            strbTpDes.AppendLine("测试时间:" + nearestTp.DateTimeStringWithMillisecond);
            strbTpDes.AppendLine("经度:" + nearestTp.Longitude);
            strbTpDes.AppendLine("纬度:" + nearestTp.Latitude);
            strbTpDes.AppendLine("采样点到基站的距离(米):" + minDistance);

            ICell cell = nearestTp.GetMainCell();
            if (cell == null)
            {
                strbTpDes.AppendLine(string.Format("主服小区:未知小区{0}_{1}"
                    , (int?)(ushort?)nearestTp["lte_TAC"], (int?)nearestTp["lte_ECI"]));
            }
            else
            {
                strbTpDes.AppendLine("主服小区:" + cell.Name);
            }

            strbTpDes.Append("邻服小区：");
            for (int i = 0; i < 6; i++)
            {
                ICell nCell = nearestTp.GetNBCell(i);
                if (nCell != null)
                {
                    strbTpDes.Append(nCell.Name + ";");
                }
            }
            return strbTpDes.ToString();
        }
        public string GetTpDesFdd(TestPoint nearestTp, double minDistance, LTECell cell)
        {
            StringBuilder strbTpDes = new StringBuilder();
            strbTpDes.AppendLine("离基站最近的采样点信息:");
            strbTpDes.AppendLine("测试时间:" + nearestTp.DateTimeStringWithMillisecond);
            strbTpDes.AppendLine("经度:" + nearestTp.Longitude);
            strbTpDes.AppendLine("纬度:" + nearestTp.Latitude);
            strbTpDes.AppendLine("采样点到基站的距离(米):" + minDistance);
            strbTpDes.AppendLine("主服小区:" + cell.Name);
            strbTpDes.Append("邻服小区：");
            for (int i = 0; i < 6; i++)
            {
                ICell nCell = nearestTp.GetNBCell(i);
                if (nCell != null)
                {
                    strbTpDes.Append(nCell.Name + ";");
                }
            }
            return strbTpDes.ToString();
        }
        protected void saveTpDesToTxt(TestPoint nearestTp, double nearestDistance, LTECell targetCell, string strDtType)
        {
            try
            {
                string tpDesSavePath = getTpDesTxtPath(targetCell, strDtType);
                if (!System.IO.File.Exists(tpDesSavePath))
                {
                    System.IO.File.Create(tpDesSavePath).Close();
                }
                string tpDes = GetTpDes(nearestTp, nearestDistance);
                using (System.IO.StreamWriter sw = System.IO.File.AppendText(tpDesSavePath))
                {
                    sw.Write(tpDes);
                    sw.Flush();
                    sw.Close();
                }
            }
            catch
            { 
                //continue
            }
        }

        public string ReadTpdesFromTxt(LTECell targetCell, string strDtType)
        {
            string tpDesSavePath = getTpDesTxtPath(targetCell, strDtType);
            if (System.IO.File.Exists(tpDesSavePath))
            {
                return System.IO.File.ReadAllText(tpDesSavePath);
            }
            return null;
        }
        protected string getTpDesTxtPath(LTECell targetCell, string strDtType)
        {
            string folderPath = GetBtsPicFolder(targetCell.BTSName);
            if (!System.IO.Directory.Exists(folderPath))
            {
                System.IO.Directory.CreateDirectory(folderPath);
            }
            return System.IO.Path.Combine(folderPath, targetCell.Name + "_" + strDtType + ".txt");
        }
        #endregion
        public static void InsertExcelPicture(Excel.Workbook eBook, string startCell, string picPath)
        {
            Excel.Worksheet eSheet = (Excel.Worksheet)eBook.Sheets[3];
            Excel.Range rng = eSheet.get_Range(startCell, Type.Missing);

            double width = eBook.Application.CentimetersToPoints(17.36);
            double height = eBook.Application.CentimetersToPoints(10.11);
            eSheet.Shapes.AddPicture(picPath,
                Microsoft.Office.Core.MsoTriState.msoFalse,
                Microsoft.Office.Core.MsoTriState.msoCTrue,
                (float)(double)rng.Left, (float)(double)rng.Top,
                (float)width, (float)height);
        }

        public static void InsertExcelFDDPicture(Excel.Workbook eBook, string startCell, string picPath, int sheet, double widthCM, double heightCM)
        {
            Excel.Worksheet eSheet = (Excel.Worksheet)eBook.Sheets[sheet];
            Excel.Range rng = eSheet.get_Range(startCell, Type.Missing);

            double width = eBook.Application.CentimetersToPoints(widthCM);
            double height = eBook.Application.CentimetersToPoints(heightCM);
            eSheet.Shapes.AddPicture(picPath,
                Microsoft.Office.Core.MsoTriState.msoFalse,
                Microsoft.Office.Core.MsoTriState.msoCTrue,
                (float)(double)rng.Left, (float)(double)rng.Top,
                (float)width, (float)height);
        }
    }
    #endregion

    #region 室分站
    class AcpAutoIndoorRrcRate : AcpAutoRrcRate
    {
        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("附着");
        }

    }
    class AcpAutoIndoorAccRate : AcpAutoAccRate
    {
        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("附着");
        }
    }
    class AcpAutoIndoorErabRate : AcpAutoErabRate
    {
        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("附着");
        }
    }
    class AcpAutoIndoorCsfbRate : AcpAutoCsfbRate
    {
        public AcpAutoIndoorCsfbRate()
        {
        }
        public override bool IsValidFile(FileInfo fileInfo)
        {
            if (fileInfo.Momt != (int)MoMtFile.MoFlag || fileInfo.Name.IndexOf("语音", StringComparison.CurrentCultureIgnoreCase) == -1)
            {
                return false;
            }
            GetCsfbEventIdsByDevice(fileInfo.DeviceType, out evtRequList, out evtSuccList);
            return true;
        }
        protected override Dictionary<KpiKey, object> getKpiInfos(CellKPI kpiCell)
        {
            Dictionary<KpiKey, object> kpiInfos = new Dictionary<KpiKey, object>();
            kpiInfos.Add(KpiKey.CsfbRequestCnt, kpiCell.RequestCnt);
            kpiInfos.Add(KpiKey.CsfbSucceedCnt, kpiCell.SucceedCnt);
            return kpiInfos;
        }
    }
    class AcpAutoIndoorLeakOut_LockEarfcn : AcpAutoKpiBase
    {
        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("锁频");
        }
        public override Dictionary<KpiKey, object> GetFileKpiInfos(FileInfo fileInfo
            , DTFileDataManager fileManager, LTECell targetCell)
        {
            int colIndex = getColumnIndex(fileInfo);
            if (colIndex == -1)
            {
                reportInfo(string.Format("文件名中{0}未发现PCI关键字", fileInfo.Name));
                return new Dictionary<KpiKey, object>();
            }

            RateKpiInfo kpiCell = anaFile(fileInfo, fileManager, targetCell);
            if (kpiCell == null)
            {
                reportInfo(string.Format("文件{0}获取目标小区信息失败", fileInfo.Name));
                return new Dictionary<KpiKey, object>();
            }
            return getKpiInfos(kpiCell);
        }
        protected virtual RateKpiInfo anaFile(FileInfo fileInfo, DTFileDataManager fileManager, LTECell targetCell)
        {
            RateKpiInfo weakLeakOutRateInfo = null;
            foreach (TestPoint tp in fileManager.TestPoints)
            {
                LTECell cell = getTpSrcCell(tp);
                if (cell == null || cell.Token != targetCell.Token)
                {
                    continue;
                }

                if (weakLeakOutRateInfo == null)
                {
                    weakLeakOutRateInfo = new RateKpiInfo("锁频外泄", 0, 0);
                }
                float? rsrp = (float?)tp["lte_RSRP"];
                if (rsrp != null && rsrp != -10000000)
                {
                    ++weakLeakOutRateInfo.TotalCount;
                    if (rsrp <= -115)
                    {
                        ++weakLeakOutRateInfo.ValidCount;
                    }
                }
            }
            return weakLeakOutRateInfo;
        }
        protected virtual Dictionary<KpiKey, object> getKpiInfos(RateKpiInfo kpiCell)
        {
            Dictionary<KpiKey, object> kpiInfos = new Dictionary<KpiKey, object>();
            kpiInfos.Add(KpiKey.WeakLeakoutRate_LockEarfcn, kpiCell.Rate);
            return kpiInfos;
        }
        protected int getColumnIndex(FileInfo fileInfo)
        {
            if (fileInfo.Name.ToUpper().Contains("PCI"))
            {
                return 0;
            }
            return -1;
        }
        //protected class CellKPI
        //{
        //    public string CellName { get; private set; }

        //    private int cntScanTP;
        //    private int cntValidScanTP;

        //    public void AddPoint(TestPoint tp)
        //    {

        //        #region 扫频模式
        //        double? MaxRSRP = (double?)tp["LTESCAN_TopN_CELL_Specific_RSRP", 0];
        //        if (MaxRSRP == null)
        //        {
        //            return;
        //        }
        //        LTECell MainCell = tp.GetCell_LTEScan(0);
        //        if (MainCell == null)
        //        {
        //            return;
        //        }
        //        cntScanTP++;

        //        if (MainCell.Type != LTEBTSType.Outdoor)
        //        {
        //            return;
        //        }
        //        for (int i = 0; i < 20; i++)
        //        {
        //            LTECell cell = tp.GetCell_LTEScan(i);
        //            double? RSRP = (double?)tp["LTESCAN_TopN_CELL_Specific_RSRP", i];
        //            if (RSRP == null || MaxRSRP - RSRP < 10)
        //            {
        //                break;
        //            }
        //            if (cell != null && cell.Name.Trim().Equals(this.CellName.Trim()))
        //            {
        //                cntValidScanTP++;
        //                break;
        //            }
        //        }
        //        #endregion
        //    }
        //}
    }
    class AcpAutoIndoorLeakOut_Scan : AcpAutoIndoorLeakOut_LockEarfcn
    {
        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("扫频");
        }

        protected override RateKpiInfo anaFile(FileInfo fileInfo, DTFileDataManager fileManager, LTECell targetCell)
        {
            RateKpiInfo weakLeakOutRateInfo = new RateKpiInfo("扫频外泄", 0, 0);
            foreach (TestPoint tp in fileManager.TestPoints)
            {
                getLeakOutKpi(weakLeakOutRateInfo, tp, targetCell.Token);
            }
            return weakLeakOutRateInfo;
        }

        private void getLeakOutKpi(RateKpiInfo weakLeakOutRateInfo, TestPoint tp, string token)
        {
            //获取宏站主控小区
            LTECell cell = StationAcceptCellHelper_XJ.Instance.GetLTECell(tp);
            if (cell == null)
            {
                return;
            }

            float? rsrp = (float?)tp["lte_RSRP"];
            if (rsrp != null && rsrp != -10000000)
            {
                ++weakLeakOutRateInfo.TotalCount;
                if (cell.Type == LTEBTSType.Outdoor && cell.Token != token)
                {
                    bool hasValidTP = hasValidTestPoint(tp, token, rsrp);
                    if (hasValidTP)
                    {
                        ++weakLeakOutRateInfo.ValidCount;
                    }
                }
            }
        }

        private bool hasValidTestPoint(TestPoint tp, string token, float? rsrp)
        {
            for (int i = 0; i < 10; i++)
            {
                //获取邻区为目标室分小区的采样点
                LTECell nCell = MultiStationAutoAcceptManager.GetLeakOutScanTpNCell(tp, i);
                if (nCell != null && nCell.Token == token)
                {
                    float? nRsrp = (float?)tp["lte_NCell_RSRP", i];
                    //宏站主控小区与目标室分小区场强在10db以内则为泄漏点
                    if (nRsrp != null && rsrp - nRsrp <= 10)
                    {
                        return false;
                    }
                }
            }
            return true;
        }


        protected override Dictionary<KpiKey, object> getKpiInfos(RateKpiInfo kpiCell)
        {
            Dictionary<KpiKey, object> kpiInfos = new Dictionary<KpiKey, object>();
            kpiInfos.Add(KpiKey.WeakLeakoutRate_Scan, kpiCell.Rate);
            return kpiInfos;
        }
    }

    /// <summary>
    /// 平层测试指标
    /// </summary>
    class AcpAutoIndoorLevelTestKpi : AcpAutoKpiBase
    {
        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("上传") || fileInfo.Name.Contains("下载")
                || fileInfo.Name.Contains("切换");
        }
        public override Dictionary<KpiKey, object> GetFileKpiInfos(FileInfo fileInfo
           , DTFileDataManager fileManager, LTECell targetCell)
        {
            CellKPI targetKpiCell = null;
            foreach (TestPoint tp in fileManager.TestPoints)
            {
                LTECell cell = getTpSrcCell(tp);
                if (cell == null || cell.Token != targetCell.Token)
                {
                    continue;
                }

                if (targetKpiCell == null)
                {
                    targetKpiCell = new CellKPI(fileInfo.Name);
                }
                targetKpiCell.AddPoint(tp);
            }

            if (targetKpiCell == null)
            {
                reportInfo(string.Format("文件{0}获取目标小区信息失败", fileInfo.Name));
                return new Dictionary<KpiKey, object>();
            }
            return getKpiInfos(targetKpiCell, fileInfo.Name);
        }
        private Dictionary<KpiKey, object> getKpiInfos(CellKPI kpiCell,string fileName)
        {
            Dictionary<KpiKey, object> kpiInfos = new Dictionary<KpiKey, object>();
            kpiInfos.Add(KpiKey.DoubleBalanceRate, kpiCell.BalanceRate);
            kpiInfos.Add(KpiKey.FtpRsrpAvg, kpiCell.AvgRsrp);
            kpiInfos.Add(KpiKey.FtpSinrAvg, kpiCell.AvgSinr);
            kpiInfos.Add(KpiKey.SinrRateHigherThan6, kpiCell.SinrRate_HigherThan6);
            kpiInfos.Add(KpiKey.SinrRateHigherThan9, kpiCell.SinrRate_HigherThan9);
            kpiInfos.Add(KpiKey.RsrpB105Sinr6Rate, kpiCell.RsrpB105Sinr6Rate);
            kpiInfos.Add(KpiKey.RsrpB95Sinr9Rate, kpiCell.RsrpB95Sinr9Rate);
            kpiInfos.Add(KpiKey.RsrpRateHigerThanB85, kpiCell.RsrpRate_HigerThanB85);

            if (fileName.Contains("上传"))
            {
                kpiInfos.Add(KpiKey.FtpUlCoverPntCount_Sum, kpiCell.PointCount_RsrpAndSinr);
                kpiInfos.Add(KpiKey.FtpUlCoverPntCount_Valid, kpiCell.PointCount_RsrpB105sinr6);
                kpiInfos.Add(KpiKey.FtpUlPntCount_Rsrp, kpiCell.PointCount_Rsrp);
                kpiInfos.Add(KpiKey.FtpUlPntCount_Sinr, kpiCell.PointCount_Sinr);
                kpiInfos.Add(KpiKey.FtpUlSpeedAvg, kpiCell.AvgULSpeed);
                kpiInfos.Add(KpiKey.FtpUlSpeedMax, kpiCell.MaxULSpeed);
                kpiInfos.Add(KpiKey.FtpUlPntCount_Speed, kpiCell.PointCount_UlSpeed);
            }
            else if (fileName.Contains("下载"))
            {
                kpiInfos.Add(KpiKey.FtpDlCoverPntCount_Sum, kpiCell.PointCount_RsrpAndSinr);
                kpiInfos.Add(KpiKey.FtpDlCoverPntCount_Valid, kpiCell.PointCount_RsrpB105sinr6);
                kpiInfos.Add(KpiKey.FtpDlPntCount_Rsrp, kpiCell.PointCount_Rsrp);
                kpiInfos.Add(KpiKey.FtpDlPntCount_Sinr, kpiCell.PointCount_Sinr);
                kpiInfos.Add(KpiKey.FtpDlSpeedMax, kpiCell.MaxDLSpeed);
                kpiInfos.Add(KpiKey.FtpDlPntCount_Speed, kpiCell.PointCount_DlSpeed);
                kpiInfos.Add(KpiKey.FtpDlSpeedAvg, kpiCell.AvgDLSpeed);
            }
            return kpiInfos;
        }

        protected class CellKPI : LevelTestKpiBase
        {
            public CellKPI(string fileName)
                : base(fileName)
            {
            }
            public override double BalanceRate
            {
                get { return cntTotalBalance == 0 ? double.MinValue : Math.Round(100 * cntBalance / (double)cntTotalBalance, 2); }
            }
            public override double AvgRsrp
            {
                get { return PointCount_Rsrp == 0 ? double.MinValue : Math.Round(sumRsrp / PointCount_Rsrp, 2); }
            }
            public override double AvgSinr
            {
                get { return PointCount_Sinr == 0 ? double.MinValue : Math.Round(sumSinr / PointCount_Sinr, 2); }
            }
            public override double AvgULSpeed
            {
                get { return PointCount_UlSpeed == 0 ? double.MinValue : Math.Round(sumULSpeed / PointCount_UlSpeed, 2); }
            }
            public override double AvgDLSpeed
            {
                get { return PointCount_DlSpeed == 0 ? double.MinValue : Math.Round(sumDLSpeed / PointCount_DlSpeed, 2); }
            }
            public override double SinrRate_HigherThan6
            {
                get { return PointCount_Sinr == 0 ? double.MinValue : Math.Round(100 * sinr6count / (double)PointCount_Sinr, 2); }
            }
            public override double SinrRate_HigherThan9
            {
                get { return PointCount_Sinr == 0 ? double.MinValue : Math.Round(100 * sinr9count / (double)PointCount_Sinr, 2); }
            }
            public override double RsrpB105Sinr6Rate
            {
                get { return PointCount_RsrpAndSinr == 0 ? double.MinValue : Math.Round(100 * PointCount_RsrpB105sinr6 / (double)PointCount_RsrpAndSinr, 2); }
            }
            public override double RsrpB95Sinr9Rate
            {
                get { return PointCount_RsrpAndSinr == 0 ? double.MinValue : Math.Round(100 * rsrpB95sinr9cnt / (double)PointCount_RsrpAndSinr, 2); }
            }
            public override double RsrpRate_HigerThanB85
            {
                get { return PointCount_Rsrp == 0 ? double.MinValue : Math.Round(100 * rsrpB85count / (double)PointCount_Rsrp, 2); }
            }
            public void AddPoint(TestPoint tp)
            {
                setRsrp0AndRsrp1(tp); float? rsrp = setRsrp(tp);
                float? sinr = setSinr(tp);
                setRsrpAndSinr(rsrp, sinr);
                setSpeed(tp);
            }

            private void setRsrp0AndRsrp1(TestPoint tp)
            {
                #region 双通道功率平衡率
                float? rsrp0 = (float?)tp["lte_RSRP_Rx0"];
                float? rsrp1 = (float?)tp["lte_RSRP_Rx1"];
                if (rsrp0 != null && rsrp1 != null)
                {
                    ++cntTotalBalance;
                    float rsrpAbs = (float)rsrp1 - (float)rsrp0;
                    if (Math.Abs(rsrpAbs) <= 6)
                    {
                        cntBalance++;
                    }
                }
                #endregion
            }

            private float? setRsrp(TestPoint tp)
            {

                #region 平均RSRP 及 RSRP≥-85dBm占比
                float? rsrp = (float?)tp["lte_RSRP"];
                if (rsrp != null && rsrp != -10000000)
                {
                    ++PointCount_Rsrp;
                    sumRsrp += (float)rsrp;

                    if (rsrp >= -85)
                    {
                        rsrpB85count++;
                    }
                }
                #endregion
                return rsrp;
            }

            private float? setSinr(TestPoint tp)
            {

                #region 平均SINR 及 SINR占比(>=6dB)、SINR占比(>=9dB)
                float? sinr = (float?)tp["lte_SINR"];
                if (sinr != null && sinr != -10000000)
                {
                    ++PointCount_Sinr;
                    sumSinr += (float)sinr;

                    if (sinr >= 6)
                    {
                        sinr6count++;
                    }
                    if (sinr >= 9)
                    {
                        sinr9count++;
                    }
                }
                #endregion
                return sinr;
            }

            private void setRsrpAndSinr(float? rsrp, float? sinr)
            {
                #region RSRP≥-105dBm & SINR≥6dB占比 及 RSRP≥-95dBm & SINR≥9dB占比
                if (rsrp != null && sinr != null)
                {
                    PointCount_RsrpAndSinr++;
                    if (rsrp >= -105 && sinr >= 6)
                    {
                        PointCount_RsrpB105sinr6++;
                    }

                    if (rsrp >= -95 && sinr >= 9)
                    {
                        rsrpB95sinr9cnt++;
                    }
                }
                #endregion
            }

            private void setSpeed(TestPoint tp)
            {
                #region 上行吞吐量 及 上行吞吐峰值速率
                double? ulSpeed = (double?)tp["lte_PDCP_UL_Mb"];
                if (ulSpeed != null)
                {
                    ++PointCount_UlSpeed;
                    sumULSpeed += (double)ulSpeed;

                    if (ulSpeed > MaxULSpeed)
                    {
                        MaxULSpeed = (float)ulSpeed;
                    }
                }
                #endregion

                #region 下行吞吐量 及 下行吞吐峰值速率
                double? dlSpeed = (double?)tp["lte_PDCP_DL_Mb"];
                if (dlSpeed != null)
                {
                    ++PointCount_DlSpeed;
                    sumDLSpeed += (double)dlSpeed;
                    if (dlSpeed > MaxDLSpeed)
                    {
                        MaxDLSpeed = (float)dlSpeed;
                    }
                }
                #endregion
            }

            #region 私有变量
            protected double sumRsrp;
            protected double sumSinr;
            protected double sumULSpeed;
            protected double sumDLSpeed;

            protected int cntBalance;
            protected int cntTotalBalance;

            protected int sinr6count;
            protected int sinr9count;

            protected int rsrpB85count;
            protected int rsrpB95sinr9cnt;
            #endregion
        }
    }
    class AcpAutoIndoorInnerHandover : AcpAutoInnerHandover
    {
        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("切换");
        }
    }
    class AcpAutoIndoorCoverFloor : AcpAutoKpiBase
    {
        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("下载") || fileInfo.Name.Contains("上传");
        }

        public override Dictionary<KpiKey, object> GetFileKpiInfos(FileInfo fileInfo
            , DTFileDataManager fileManager, LTECell targetCell)
        {
            Dictionary<KpiKey, object> kpiInfos = new Dictionary<KpiKey, object>();

            string[] names = fileInfo.Name.Split('_');
            foreach (string item in names)
            {
                string strItem = item.Replace("负", "-").Replace("B", "-");

                // @"-?\d+楼" 用正则表达式判断是否包含"n楼"字段,n为整数
                string strFloorNumber = Regex.Match(strItem, @"-?\d+楼", RegexOptions.None).Value.Replace("楼", "");
                if (string.IsNullOrEmpty(strFloorNumber))
                {
                    // @"-?\d+F$" 用正则表达式判断是否以"nF"字段为结尾,n为整数
                    strFloorNumber = Regex.Match(strItem, @"-?\d+F$", RegexOptions.None).Value.Replace("F", "");
                }

                int floorNumber;
                if (int.TryParse(strFloorNumber, out floorNumber))
                {
                    kpiInfos.Add(KpiKey.IndoorCoveredFloor, floorNumber);
                    break;
                }
            }
            return kpiInfos;
        }
    }
    #endregion
}
