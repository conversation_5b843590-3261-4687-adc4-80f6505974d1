﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause
{
    public partial class HoUpdatePnl : UserControl
    {
        public HoUpdatePnl()
        {
            InitializeComponent();
        }

        HoUpdateCause mainReason = null;
        HoTooOftenCause overHo = null;
        TAUpdateTooOftenCause overUpdate = null;
        UnreasonableHoPoorMainRSRP poorSC = null;
        UnreasonableHoGoodNbRSRP goodNb = null;
        HoBehindTime behindTime = null;
        public void LinkCondition(FunctionCondition cond)
        {
            foreach (CauseBase cause in cond.Causes)
            {
                if (cause is HoUpdateCause)
                {
                    mainReason = cause as HoUpdateCause;
                    break;
                }
            }
            foreach (CauseBase item in mainReason.SubCauses)
            {
                setCondition(item);
            }

            numGoodNbAfterHoAfterSec.Value = (decimal)goodNb.AfterSecond;
            numGoodNbAfterHoAfterSec.ValueChanged += numGoodNbAfterHoAfterSec_ValueChanged;
            numGoodNbAfterHoSec.Value = (decimal)goodNb.Second;
            numGoodNbAfterHoSec.ValueChanged += numGoodNbAfterHoSec_ValueChanged;

            numOverHoNum.Value = (decimal)overHo.Times;
            numOverHoNum.ValueChanged += numOverHoNum_ValueChanged;
            numOverHoSecond.Value = (decimal)overHo.Second;
            numOverHoSecond.ValueChanged += numOverHoSecond_ValueChanged;

            numOverUpdateNum.Value = (decimal)overUpdate.Times;
            numOverUpdateNum.ValueChanged += numOverUpdateNum_ValueChanged;
            numOverUpdateSecond.Value = (decimal)overUpdate.Second;
            numOverUpdateSecond.ValueChanged += numOverUpdateSecond_ValueChanged;

            numPoorAfterHoAfterSec.Value = (decimal)poorSC.AfterSecond;
            numPoorAfterHoAfterSec.ValueChanged += numPoorAfterHoAfterSec_ValueChanged;
            numPoorAfterHoBeforeSec.Value = (decimal)poorSC.BeforeSecond;
            numPoorAfterHoBeforeSec.ValueChanged += numPoorAfterHoBeforeSec_ValueChanged;
            numPoorAfterHoScond.Value = (decimal)poorSC.Second;
            numPoorAfterHoScond.ValueChanged += numPoorAfterHoScond_ValueChanged;

            numBehindDiff.Value = (decimal)behindTime.RSRPDiff;
            numBehindDiff.ValueChanged += numBehindDiff_ValueChanged;
            numBehindRSRP.Value = (decimal)behindTime.MainRSRPMin;
            numBehindRSRP.ValueChanged += numBehindRSRP_ValueChanged;
            numBehindSecond.Value = (decimal)behindTime.StaySecond;
            numBehindSecond.ValueChanged += numBehindSecond_ValueChanged;

        }

        private void setCondition(CauseBase item)
        {
            if (item is HoTooOftenCause)
            {
                overHo = item as HoTooOftenCause;
            }
            else if (item is TAUpdateTooOftenCause)
            {
                overUpdate = item as TAUpdateTooOftenCause;
            }
            else if (item is UnreasonableHoPoorMainRSRP)
            {
                poorSC = item as UnreasonableHoPoorMainRSRP;
            }
            else if (item is UnreasonableHoGoodNbRSRP)
            {
                goodNb = item as UnreasonableHoGoodNbRSRP;
            }
            else if (item is HoBehindTime)
            {
                behindTime = item as HoBehindTime;
            }
            else if (item is CellErrorCause)
            {
                cellErrorPnl1.LinkCondition(item as CellErrorCause);
            }
            else if (item is IFHOChangedCause)
            {
                ifhoChangedPnl1.LinkCondition(item as IFHOChangedCause);
            }
            else if (item is PRBLowSchedulingCause)
            {
                prbLowSchedulingPnl1.LinkCondition(item as PRBLowSchedulingCause);
            }
            else if (item is RRCReEstablishFailCause)
            {
                rrcReEstablishFailPnl1.LinkCondition(item as RRCReEstablishFailCause);
            }
            else if (item is RRCSetupFailCause)
            {
                rrcSetupFailPnl1.LinkCondition(item as RRCSetupFailCause);
            }
            else if (item is TrackAreaUpdateCause)
            {
                trackAreaUpdatePnl1.LinkCondition(item as TrackAreaUpdateCause);
            }
        }

        void numBehindSecond_ValueChanged(object sender, EventArgs e)
        {
            behindTime.StaySecond = (int)numBehindSecond.Value;
        }

        void numBehindRSRP_ValueChanged(object sender, EventArgs e)
        {
            behindTime.MainRSRPMin = (float)numBehindRSRP.Value;
        }

        void numBehindDiff_ValueChanged(object sender, EventArgs e)
        {
            behindTime.RSRPDiff = (float)numBehindDiff.Value;
        }

        void numPoorAfterHoScond_ValueChanged(object sender, EventArgs e)
        {
            poorSC.Second = (int)numPoorAfterHoScond.Value;
        }

        void numPoorAfterHoBeforeSec_ValueChanged(object sender, EventArgs e)
        {
            poorSC.BeforeSecond = (int)numPoorAfterHoBeforeSec.Value;
        }

        void numPoorAfterHoAfterSec_ValueChanged(object sender, EventArgs e)
        {
            poorSC.AfterSecond = (int)numPoorAfterHoAfterSec.Value;
        }

        void numOverUpdateSecond_ValueChanged(object sender, EventArgs e)
        {
            overUpdate.Second = (int)numOverUpdateSecond.Value;
        }

        void numOverUpdateNum_ValueChanged(object sender, EventArgs e)
        {
            overUpdate.Times = (int)numOverUpdateNum.Value;
        }

        void numOverHoSecond_ValueChanged(object sender, EventArgs e)
        {
            overHo.Second = (int)numOverHoSecond.Value;
        }

        void numOverHoNum_ValueChanged(object sender, EventArgs e)
        {
            overHo.Times = (int)numOverHoNum.Value;
        }

        void numGoodNbAfterHoSec_ValueChanged(object sender, EventArgs e)
        {
            goodNb.Second = (int)numGoodNbAfterHoSec.Value;
        }

        void numGoodNbAfterHoAfterSec_ValueChanged(object sender, EventArgs e)
        {
            goodNb.AfterSecond = (int)numGoodNbAfterHoAfterSec.Value;
        }

    }
}
