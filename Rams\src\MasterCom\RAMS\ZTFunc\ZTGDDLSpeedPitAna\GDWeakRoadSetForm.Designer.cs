﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class GDWeakRoadSetForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.gpSet = new System.Windows.Forms.GroupBox();
            this.btnCancel = new System.Windows.Forms.Button();
            this.btnOK = new System.Windows.Forms.Button();
            this.label4 = new System.Windows.Forms.Label();
            this.numThreshold = new System.Windows.Forms.NumericUpDown();
            this.label3 = new System.Windows.Forms.Label();
            this.numPercent = new System.Windows.Forms.NumericUpDown();
            this.numSample = new System.Windows.Forms.NumericUpDown();
            this.label2 = new System.Windows.Forms.Label();
            this.label1 = new System.Windows.Forms.Label();
            this.cmbRoadType = new System.Windows.Forms.ComboBox();
            this.gpSet.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numThreshold)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numPercent)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSample)).BeginInit();
            this.SuspendLayout();
            // 
            // gpSet
            // 
            this.gpSet.Controls.Add(this.btnCancel);
            this.gpSet.Controls.Add(this.btnOK);
            this.gpSet.Controls.Add(this.label4);
            this.gpSet.Controls.Add(this.numThreshold);
            this.gpSet.Controls.Add(this.label3);
            this.gpSet.Controls.Add(this.numPercent);
            this.gpSet.Controls.Add(this.numSample);
            this.gpSet.Controls.Add(this.label2);
            this.gpSet.Controls.Add(this.label1);
            this.gpSet.Controls.Add(this.cmbRoadType);
            this.gpSet.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gpSet.Location = new System.Drawing.Point(0, 0);
            this.gpSet.Name = "gpSet";
            this.gpSet.Size = new System.Drawing.Size(411, 158);
            this.gpSet.TabIndex = 0;
            this.gpSet.TabStop = false;
            // 
            // btnCancel
            // 
            this.btnCancel.Location = new System.Drawing.Point(296, 107);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 32;
            this.btnCancel.Text = "取消";
            this.btnCancel.UseVisualStyleBackColor = true;
            this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
            // 
            // btnOK
            // 
            this.btnOK.Location = new System.Drawing.Point(206, 107);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 31;
            this.btnOK.Text = "确定";
            this.btnOK.UseVisualStyleBackColor = true;
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(214, 67);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(89, 12);
            this.label4.TabIndex = 26;
            this.label4.Text = "弱采样点门限＜";
            // 
            // numThreshold
            // 
            this.numThreshold.DecimalPlaces = 2;
            this.numThreshold.Location = new System.Drawing.Point(309, 61);
            this.numThreshold.Maximum = new decimal(new int[] {
            50,
            0,
            0,
            0});
            this.numThreshold.Minimum = new decimal(new int[] {
            150,
            0,
            0,
            -2147483648});
            this.numThreshold.Name = "numThreshold";
            this.numThreshold.Size = new System.Drawing.Size(66, 21);
            this.numThreshold.TabIndex = 25;
            this.numThreshold.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numThreshold.Value = new decimal(new int[] {
            110,
            0,
            0,
            -2147483648});
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(10, 72);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(89, 12);
            this.label3.TabIndex = 24;
            this.label3.Text = "弱采样点占比≥";
            // 
            // numPercent
            // 
            this.numPercent.DecimalPlaces = 2;
            this.numPercent.Location = new System.Drawing.Point(105, 66);
            this.numPercent.Name = "numPercent";
            this.numPercent.Size = new System.Drawing.Size(74, 21);
            this.numPercent.TabIndex = 23;
            this.numPercent.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numPercent.Value = new decimal(new int[] {
            70,
            0,
            0,
            0});
            // 
            // numSample
            // 
            this.numSample.Location = new System.Drawing.Point(309, 21);
            this.numSample.Name = "numSample";
            this.numSample.Size = new System.Drawing.Size(68, 21);
            this.numSample.TabIndex = 3;
            this.numSample.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numSample.Value = new decimal(new int[] {
            10,
            0,
            0,
            0});
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(216, 26);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(89, 12);
            this.label2.TabIndex = 2;
            this.label2.Text = "连续采样点数：";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(33, 26);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(65, 12);
            this.label1.TabIndex = 1;
            this.label1.Text = "路段类型：";
            // 
            // cmbRoadType
            // 
            this.cmbRoadType.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbRoadType.FormattingEnabled = true;
            this.cmbRoadType.Items.AddRange(new object[] {
            "弱覆盖",
            "弱质差"});
            this.cmbRoadType.Location = new System.Drawing.Point(105, 21);
            this.cmbRoadType.Name = "cmbRoadType";
            this.cmbRoadType.Size = new System.Drawing.Size(74, 22);
            this.cmbRoadType.TabIndex = 0;
            this.cmbRoadType.SelectedIndexChanged += new System.EventHandler(this.cmbRoadType_SelectedIndexChanged);
            // 
            // GDWeakRoadSetForm
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(411, 158);
            this.Controls.Add(this.gpSet);
            this.Name = "GDWeakRoadSetForm";
            this.Text = "弱路段条件设置";
            this.gpSet.ResumeLayout(false);
            this.gpSet.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numThreshold)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numPercent)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSample)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.GroupBox gpSet;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.ComboBox cmbRoadType;
        private System.Windows.Forms.NumericUpDown numSample;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.NumericUpDown numPercent;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.NumericUpDown numThreshold;
        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.Button btnOK;
    }
}