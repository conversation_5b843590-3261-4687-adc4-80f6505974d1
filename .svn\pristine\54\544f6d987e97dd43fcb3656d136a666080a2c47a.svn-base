﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc.ZTPoorRSRQCell
{
    public partial class SettingForm : BaseForm
    {
        public SettingForm()
            : base()
        {
            InitializeComponent();
        }

        public SettingForm(PoorRsrqCellCondition condition)
        {
            InitializeComponent();
            setCondition(condition);
        }

        private void setCondition(PoorRsrqCellCondition condition)
        {
            if (condition==null)
            {
                return;
            }
            numMaxValue.Value = (decimal)condition.MaxRsrq;
        }

        internal PoorRsrqCellCondition GetCondition()
        {
            PoorRsrqCellCondition cond = new PoorRsrqCellCondition();
            cond.MaxRsrq = (float)numMaxValue.Value;
            return cond;
        }
    }
}
