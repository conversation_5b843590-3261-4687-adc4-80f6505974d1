﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.NOP
{
    class GroupStatAnalyzer
    {
        public List<GroupStatResult> Results = null;
        public List<TaskStatResult> TaskResults = null;

        public GroupStatAnalyzer()
        {
            TaskResults = new List<TaskStatResult>();
        }

        public void ClearResult()
        {
            TaskResults = new List<TaskStatResult>();
            Results = new List<GroupStatResult>();
        }

        public void Analyze(Dictionary<int,List<GroupStatModel>> modelDic)
        {
            if (modelDic == null)
            {
                return;
            }
            Results = new List<GroupStatResult>()
            {
                new GroupStatResult("T1"),
                new GroupStatResult("T2_1"),
                new GroupStatResult("T2_2"),
                new GroupStatResult("T2_3"),
                new GroupStatResult("T2_4"),
                new GroupStatResult("T2_5"),
                new GroupStatResult("T3"),
                new GroupStatResult("地市")
            };

            foreach (var item in modelDic)
            {
                if (item.Value.Count < 1)
                {
                    continue;
                }
                int T0Count = 0;
                int beforIndex = 0;
                int countIndex = getIndex(item);
                string descripe = "";
                TaskStatResult taskResult = getTaskStatResult(item, ref T0Count, ref beforIndex, countIndex, ref descripe);
                if (T0Count > 1)
                {
                    Results[7].Counts[countIndex]++;
                    taskResult.Counts[7]++;
                }
                int strIndex = descripe.IndexOf("用户组别:");
                if (beforIndex >= 0 && strIndex > 0 && strIndex + 13 < descripe.Length)
                {

                    string str = descripe.Substring(strIndex + 5, 8);
                    int index = getGroupIndex(str);
                    if (index > beforIndex && taskResult.Counts[beforIndex] == 0)
                    {
                        taskResult.Counts[beforIndex]++;
                        Results[beforIndex].Counts[countIndex]++;
                    }
                }
                TaskResults.Add(taskResult);
            }
        }

        private TaskStatResult getTaskStatResult(KeyValuePair<int, List<GroupStatModel>> item, ref int T0Count, ref int beforIndex, int countIndex, ref string descripe)
        {
            TaskStatResult taskResult = new TaskStatResult(item.Key, item.Value[0].District, item.Value[0].TaskName);
            foreach (GroupStatModel model in item.Value)
            {
                descripe = model.Description;
                int groupIndex = getGroupIndex(model.GroupName);
                if (groupIndex < 0)
                {
                    continue;
                }
                if (groupIndex == 7)
                {
                    T0Count++;
                }
                if (beforIndex >= 0)
                {
                    bool a = setIndexCount(ref beforIndex, countIndex, taskResult, model, groupIndex);
                    if (a)
                    {
                        continue;
                    }
                }
                beforIndex = groupIndex;
            }

            return taskResult;
        }

        private bool setIndexCount(ref int beforIndex, int countIndex, TaskStatResult taskResult, GroupStatModel model, int groupIndex)
        {
            if (groupIndex >= 0 && groupIndex < 7 && model.Description.Contains("[预处理]"))
            {
                if (groupIndex > beforIndex)
                {
                    if (taskResult.Counts[beforIndex] > 0)
                    {
                        beforIndex = groupIndex;
                        return true;
                    }
                    Results[beforIndex].Counts[countIndex]++;
                    taskResult.Counts[beforIndex]++;
                }
                if (model.Description.Contains("变为[已派单]"))
                {
                    if (taskResult.Counts[groupIndex] > 0)
                    {
                        beforIndex = groupIndex;
                        return true;
                    }
                    Results[groupIndex].Counts[countIndex]++;
                    taskResult.Counts[groupIndex]++;
                    beforIndex = -1;
                    return true;
                }
            }
            else if (groupIndex == 7 && groupIndex > beforIndex && taskResult.Counts[beforIndex] < 1)
            {
                Results[beforIndex].Counts[countIndex]++;
                taskResult.Counts[beforIndex]++;
            }

            return false;
        }

        public void Analyze1(Dictionary<int, List<GroupStatModel>> modelDic)
        {

            if (modelDic == null)
            {
                return;
            }
            Results = new List<GroupStatResult>()
            {
                new GroupStatResult("T1"),
                new GroupStatResult("T2_1"),
                new GroupStatResult("T2_2"),
                new GroupStatResult("T2_3"),
                new GroupStatResult("T2_4"),
                new GroupStatResult("T2_5"),
                new GroupStatResult("T3"),
                new GroupStatResult("地市")
            };

            foreach (var item in modelDic)
            {
                if (item.Value.Count < 1)
                {
                    continue;
                }

                int countIndex = getIndex(item);

                int T0Count = 0;
                TaskStatResult taskResult = getTaskStatResult(item, countIndex, ref T0Count);

                if (T0Count > 1)
                {
                    Results[7].Counts[countIndex]++;
                    taskResult.Counts[7]++;
                }

                TaskResults.Add(taskResult);
            }
        }

        private int getIndex(KeyValuePair<int, List<GroupStatModel>> item)
        {
            int countIndex = validDistrict(item.Value[0].District);
            if (countIndex == 6)
            {
                if (item.Value[0].AreaName.Contains("天门市"))
                {
                    countIndex = 7;
                }
                else if (item.Value[0].AreaName.Contains("潜江市"))
                {
                    countIndex = 8;
                }
            }

            return countIndex;
        }

        private TaskStatResult getTaskStatResult(KeyValuePair<int, List<GroupStatModel>> item, int countIndex, ref int T0Count)
        {
            TaskStatResult taskResult = new TaskStatResult(item.Key, item.Value[0].District, item.Value[0].TaskName);
            foreach (GroupStatModel model in item.Value)
            {
                int groupIndex = getGroupIndex(model.GroupName);
                if (groupIndex < 0 || taskResult.Counts[groupIndex] > 0)
                {
                    continue;
                }
                if (groupIndex == 7)
                {
                    T0Count++;
                    continue;
                }

                if (groupIndex >= 0 && groupIndex < 7)
                {
                    bool isValid = (model.Description.Contains("提交了工单") && model.Description.Contains("工单状态由[预处理]变为[已派单]"))
                        || (model.Description.Contains("保存了工单") && model.Description.Contains("用户组别"));
                    if (isValid)
                    {
                        Results[groupIndex].Counts[countIndex]++;
                        taskResult.Counts[groupIndex]++;
                    }
                }
            }

            return taskResult;
        }

        private int getGroupIndex(string str)
        {
            int index = -1;
            List<TaskName> groupList = new List<TaskName>();
            groupList.Add(new TaskName("T1", "测试数据管理", ++index));
            groupList.Add(new TaskName("T2_1", "片区优化组", ++index));
            groupList.Add(new TaskName("T2_2", "参数维护组", ++index));
            groupList.Add(new TaskName("T2_3", "室分维护组", ++index));
            groupList.Add(new TaskName("T2_4", "无线规划组", ++index));
            groupList.Add(new TaskName("T2_5", "干线优化组", ++index));
            groupList.Add(new TaskName("T3", "疑难问题处理组", ++index));

            index = -1;
            foreach (var group in groupList)
            {
                if (str.Contains(group.Name) || str.Contains(group.NameCH))
                {
                    index = group.Index;
                    return index;
                }
            }
            if (validDistrict(str) >= 0)
            {
                index = 7;
            }
            return index;
        }

        private int validDistrict(string str)
        {
            int index = -1;
            List<TaskName> districtList = new List<TaskName>();
            districtList.Add(new TaskName("ezhou", "鄂州", ++index));
            districtList.Add(new TaskName("enshi", "恩施", ++index));
            districtList.Add(new TaskName("huangshi", "黄石", ++index));
            districtList.Add(new TaskName("huanggang", "黄冈", ++index));
            districtList.Add(new TaskName("jingmen", "荆门", ++index));
            districtList.Add(new TaskName("jingzhou", "荆州", ++index));
            districtList.Add(new TaskName("jianghan", "江汉", ++index));
            districtList.Add(new TaskName("tianmen", "天门", ++index));
            districtList.Add(new TaskName("qianjiang", "潜江", ++index));
            districtList.Add(new TaskName("shiyan", "十堰", ++index));
            districtList.Add(new TaskName("suizhou", "随州", ++index));
            districtList.Add(new TaskName("wuhan", "武汉", ++index));
            districtList.Add(new TaskName("xiangyang", "襄阳", ++index));
            districtList.Add(new TaskName("xianning", "咸宁", ++index));
            districtList.Add(new TaskName("xiaogan", "孝感", ++index));
            districtList.Add(new TaskName("yichang", "宜昌", ++index));

            index = -1;
            foreach (var district in districtList)
            {
                if (str.Contains(district.Name) || str.Contains(district.NameCH))
                {
                    index = district.Index;
                }
            }
            return index;
        }

        class TaskName
        {
            public TaskName(string name, string nameCH, int index)
            {
                Index = index;
                Name = name;
                NameCH = nameCH;
            }
            public int Index { get; set; }
            public string Name { get; set; }
            public string NameCH { get; set; }
        }
    }
}
