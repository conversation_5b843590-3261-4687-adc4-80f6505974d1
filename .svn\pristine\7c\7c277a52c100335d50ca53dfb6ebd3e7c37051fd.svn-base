﻿using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.ZTFunc;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTNRReSelectBase : DIYAnalyseFilesOneByOneByRegion
    {
        protected static readonly object lockObj = new object();
        public List<ZTNRReSelectItem> resultList { get; set; } = new List<ZTNRReSelectItem>();    //保存结果
        public ZTNRReSelectCondition hoCondition { get; set; } = new ZTNRReSelectCondition();   //查询条件
        protected List<int> valuedEvtId;
        public ZTNRReSelectBase(MainModel mainModel)
            : base(mainModel)
        {
            this.IncludeEvent = true;
            this.IncludeMessage = false;
            valuedEvtId = new List<int> { (int)NREventManager.IRAT_L_NR_Redirect_Success, (int)NREventManager.IRAT_NR_L_Redirect_Success };
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.NR_SA_TDD_IDLE);
            ServiceTypes.Add(ServiceType.NR_SA_TDD_DATA);
            ServiceTypes.Add(ServiceType.NR_SA_TDD_VOLTE);
            ServiceTypes.Add(ServiceType.NR_SA_TDD_VONR);
        }

        protected override void clearDataBeforeAnalyseFiles()
        {
            resultList = new List<ZTNRReSelectItem>();
        }

        ZTNRReSelectSetForm setForm = null;
        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }

            if (setForm == null)
            {
                setForm = new ZTNRReSelectSetForm();
            }

            if (setForm.ShowDialog() == DialogResult.OK)
            {
                hoCondition = setForm.GetCondition();
                return true;
            }

            return false;
        }

        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager fileMng in MainModel.DTDataManager.FileDataManagers)
            {
                List<TestPoint> testPointList = fileMng.TestPoints;
                List<Event> eventList = fileMng.Events;

                for (int eLoop = 0; eLoop < eventList.Count; ++eLoop)
                {
                    Event e = eventList[eLoop];
                    if (!valuedEvtId.Contains(e.ID))
                    {
                        continue;
                    }

                    int index = -1;
                    if ((index = GetNearestTestPointIndex(e.SN, testPointList)) == -1)
                    {
                        continue;
                    }

                    addResultList(testPointList, e, index);
                }
            }
        }

        private void addResultList(List<TestPoint> testPointList, Event e, int index)
        {
            ZTNRReSelectItem item = new ZTNRReSelectItem(e.FileName, e);

            long tpTimeHead = e.Time * 1000L + e.Millisecond - hoCondition.BeforeSecond * 1000L;
            long tpTimeTail = e.Time * 1000L + e.Millisecond + hoCondition.AfterSecond * 1000L;

            for (int i = index; i >= 0; --i)
            {
                TestPoint tp = testPointList[i];
                if ((tp.Time + hoCondition.BeforeSecond) * 1000L + tp.Millisecond < tpTimeHead)
                {
                    break;
                }
                item.AddBeforeTp(tp);
            }
            for (int i = index + 1; i < testPointList.Count; ++i)
            {
                TestPoint tp = testPointList[i];
                if (tpTimeTail < tp.Time * 1000L + tp.Millisecond)
                {
                    break;
                }
                item.AddAfterTp(tp);
            }

            item.SetSameSiteFlag(hoCondition.SiteDistance);

            item.SN = resultList.Count + 1;
            resultList.Add(item);
        }

        /// <summary>
        /// 显示结果
        /// </summary>
        protected override void fireShowForm()
        {
            if (resultList.Count == 0)
            {
                System.Windows.Forms.MessageBox.Show("没有符合条件的信息！");
                return;
            }

            object obj = MainModel.GetInstance().GetObjectFromBlackboard(typeof(ZTNRReSelectListForm).FullName);
            ZTNRReSelectListForm cellReselectAnaListForm = obj == null ? null : obj as ZTNRReSelectListForm;
            if (cellReselectAnaListForm == null || cellReselectAnaListForm.IsDisposed)
            {
                cellReselectAnaListForm = new ZTNRReSelectListForm(MainModel);
            }

            cellReselectAnaListForm.FillData(resultList);
            if (!cellReselectAnaListForm.Visible)
            {
                cellReselectAnaListForm.Show(MainModel.MainForm);
            }
        }

        protected override void releaseSource()
        {
            resultList = null;
        }

        /// <summary>
        /// tpList升序，寻找最大的index使得tpList[index] <= eventSN
        /// </summary>
        /// <param name="eventSN"></param>
        /// <param name="tpList"></param>
        /// <returns></returns>
        protected int GetNearestTestPointIndex(int eventSN, List<TestPoint> tpList)
        {
            int index = -1;
            for (int i = 0; i < tpList.Count; i++)
            {
                if (tpList[i].SN > eventSN)
                {
                    index = i - 1;
                    break;
                }
            }
            return index;
        }

        private Comparer comparer { get; set; } = new Comparer();
        private class Comparer : IComparer<DTData>
        {
            public int Compare(DTData x, DTData y)
            {
                return x.SN - y.SN;
            }
        }
    }


    public class ZTNRReSelectItem
    {
        private readonly List<int> valueEvtID = new List<int> { 
            (int)NREventManager.IRAT_L_NR_Redirect_Success, (int)NREventManager.IRAT_NR_L_Redirect_Success };
        public int SN { get; set; }
        public string FileName { get; set; }

        public string DateTime
        {
            get
            {
                if (HOEvt != null)
                {
                    return HOEvt.DateTime.ToString("yyyy-MM-dd HH:mm:ss");
                }
                return "";
            }
        }

        public double Longitude
        {
            get
            {
                if (HOEvt != null)
                {
                    return HOEvt.Longitude;
                }
                return 0;
            }
        }
        public double Latitude
        {
            get
            {
                if (HOEvt != null)
                {
                    return HOEvt.Latitude;
                }
                return 0;
            }
        }

        public string HandOverResult
        {
            get
            {
                if (HOEvt != null && valueEvtID.Contains(HOEvt.ID))
                {
                    return "成功";
                }
                return "";
            }
        }
        public string HnadOverDirection
        {
            get
            {
                return NRCell.GetBandTypeByArfcn(SrcCellItem.ARFCN) + "->" + NRCell.GetBandTypeByArfcn(DestCellItem.ARFCN);
            }
        }

        public ZTNRReSelectCellItem SrcCellItem { get; set; }
        public ZTNRReSelectCellItem DestCellItem { get; set; }

        public ZTNRReSelectTpItem BeforeTpItem { get; set; }
        public ZTNRReSelectTpItem AfterTpItem { get; set; }

        public Event HOEvt { get; set; }
        public List<TestPoint> TpList { get; set; }
        public string IsSameSite { get; set; }

        public ZTNRReSelectItem(string fileName, Event hoEvt)
        {
            FileName = fileName;
            HOEvt = hoEvt;
            SrcCellItem = new ZTNRReSelectCellItem();
            DestCellItem = new ZTNRReSelectCellItem();
            BeforeTpItem = new ZTNRReSelectTpItem();
            AfterTpItem = new ZTNRReSelectTpItem();
            TpList = new List<TestPoint>();

            SetCellItem(SrcCellItem, (int)hoEvt["LAC"], (long)hoEvt["CI"], 0, 0, hoEvt);
            SetCellItem(DestCellItem, (int)hoEvt["TargetLAC"], (long)hoEvt["TargetCI"], 0, 0, hoEvt);
        }

        public void AddBeforeTp(TestPoint tp)
        {
            AddTpInfo(this.BeforeTpItem, tp);
        }

        public void AddAfterTp(TestPoint tp)
        {
            AddTpInfo(this.AfterTpItem, tp);
        }

        private void AddTpInfo(ZTNRReSelectTpItem tpItem, TestPoint tp)
        {
            TpList.Add(tp);
            float? rsrp = GetRSRP(tp);
            if (rsrp != null && rsrp >= -140 && rsrp <= -10)
            {
                tpItem.Rsrp += (float)rsrp;
                tpItem.RsrpCount++;
            }

            float? sinr = GetSINR(tp);
            if (sinr != null && sinr >= -50 && sinr <= 50)
            {
                tpItem.Sinr += (float)sinr;
                tpItem.SinrCount++;
            }

            float? appSpeed = GetAppSpeed(tp);
            if (appSpeed != null && appSpeed >= 0)
            {
                tpItem.AppSpeed += (float)appSpeed / 1000 / 1000;
                tpItem.AppSpeedCount++;
            }

            int? status = GetStatus(tp);
            float? pdcpSpeed = GetPDCP(tp);
            if (pdcpSpeed != null && pdcpSpeed >= 0 && status != null && status == 12)
            {
                tpItem.PdcpSpeed += (float)pdcpSpeed / 1000 / 1000;
                tpItem.PdcpSpeedCount++;
            }

            float? rsrq = GetRSRQ(tp);
            if (rsrq != null && rsrq >= -40 && rsrq <= 40)
            {
                tpItem.Rsrq += (float)rsrq;
                tpItem.RsrqCount++;
            }

            float? rssi = GetRSSI(tp);
            if (rssi != null && rssi >= -125 && rssi <= -25)
            {
                tpItem.Rssi += (float)rssi;
                tpItem.RssiCount++;
            }
        }

        protected float? GetRSRP(TestPoint tp)
        {
            float? rsrp = (float?)tp["NR_SS_RSRP"];
            if (rsrp == null)
            {
                rsrp = (float?)tp["NR_lte_RSRP"];
            }

            return rsrp;
        }
        protected float? GetSINR(TestPoint tp)
        {
            float? sinr = (float?)tp["NR_SS_SINR"];
            if (sinr == null)
            {
                sinr = (float?)tp["NR_lte_SINR"];
            }

            return sinr;
        }
        protected float? GetAppSpeed(TestPoint tp)
        {
            return (int?)tp["NR_APP_ThroughputDL"];
        }
        protected int? GetStatus(TestPoint tp)
        {
            short? appStatus = (short?)tp["NR_APP_Status"];

            return appStatus;
        }
        protected float? GetPDCP(TestPoint tp)
        {
            return (int?)tp["NR_Throughput_PDCP_DL"];
        }
        protected float? GetRSRQ(TestPoint tp)
        {
            float? rsrq = (float?)tp["NR_SS_RSRQ"];
            if (rsrq == null)
            {
                rsrq = (float?)tp["NR_lte_RSRQ"];
            }

            return rsrq;
        }
        protected float? GetRSSI(TestPoint tp)
        {
            float? rssi = (float?)tp["NR_SS_RSSI"];
            if (rssi == null)
            {
                rssi = (float?)tp["NR_lte_RSSI"];
            }

            return rssi;
        }

        public void SetCellItem(ZTNRReSelectCellItem cellItem, int tac, long nci, int arfrn, int pci, Event reselEvt)
        {
            NRCell cell = CellManager.GetInstance().GetNRCell(reselEvt.DateTime, tac, nci);

            if (cell != null)
            {
                cellItem.NrCell = cell;
                cellItem.CellName = cell.Name;
                cellItem.CellID = cell.CellID.ToString();
                cellItem.Distance = Math.Round(cell.GetDistance(reselEvt.Longitude, reselEvt.Latitude), 2).ToString();
            }

            cellItem.TAC = tac.ToString();
            cellItem.NCI = nci.ToString();
            cellItem.ARFCN = arfrn;
            cellItem.PCI = pci;
        }

        public void SetSameSiteFlag(int siteDistance)
        {
            if (SrcCellItem.NrCell != null && DestCellItem.NrCell != null)
            {
                if (SrcCellItem.NrCell.GetDistance(DestCellItem.NrCell.Longitude, DestCellItem.NrCell.Latitude) <= siteDistance)
                {
                    IsSameSite = "是";
                }
                else
                {
                    IsSameSite = "否";
                }
            }
        }

        #region 预处理
        public int BeforeArfcn
        {
            get
            {
                return SrcCellItem.ARFCN;
            }
        }
        public int BeforePCI
        {
            get
            {
                return SrcCellItem.PCI;
            }
        }
        public string BeforeCellName
        {
            get
            {
                return SrcCellItem.CellName;
            }
        }
        public string BeforeCellDistance
        {
            get
            {
                return SrcCellItem.Distance;
            }
        }
        public string BeforeRsrpAvg
        {
            get
            {
                if (BeforeTpItem.RsrpCount > 0)
                {
                    return Math.Round(BeforeTpItem.Rsrp / BeforeTpItem.RsrpCount, 2).ToString();
                }
                else
                {
                    return "";
                }
            }
        }
        public string BeforeSinrAvg
        {
            get
            {
                if (BeforeTpItem.SinrCount > 0)
                {
                    return Math.Round(BeforeTpItem.Sinr / BeforeTpItem.SinrCount, 2).ToString();
                }
                else
                {
                    return "";
                }
            }
        }
        public string BeforeAppSpeedAvg
        {
            get
            {
                if (BeforeTpItem.AppSpeedCount > 0)
                {
                    return Math.Round(BeforeTpItem.AppSpeed / BeforeTpItem.AppSpeedCount, 2).ToString();
                }
                else
                {
                    return "";
                }
            }
        }
        public string BeforePdcpSpeedAvg
        {
            get
            {
                if (BeforeTpItem.PdcpSpeedCount > 0)
                {
                    return Math.Round(BeforeTpItem.PdcpSpeed / BeforeTpItem.PdcpSpeedCount, 2).ToString();
                }
                else
                {
                    return "";
                }
            }
        }
        public string BeforeRsrqAvg
        {
            get
            {
                if (BeforeTpItem.RsrqCount > 0)
                {
                    return Math.Round(BeforeTpItem.Rsrq / BeforeTpItem.RsrqCount, 2).ToString();
                }
                else
                {
                    return "";
                }
            }
        }
        public string BeforeRssiAvg
        {
            get
            {
                if (BeforeTpItem.RssiCount > 0)
                {
                    return Math.Round(BeforeTpItem.Rssi / BeforeTpItem.RssiCount, 2).ToString();
                }
                else
                {
                    return "";
                }
            }
        }

        public int AfterArfcn
        {
            get
            {
                return DestCellItem.ARFCN;
            }
        }
        public int AfterPCI
        {
            get
            {
                return DestCellItem.PCI;
            }
        }
        public string AfterCellName
        {
            get
            {
                return DestCellItem.CellName;
            }
        }
        public string AfterCellDistance
        {
            get
            {
                return DestCellItem.Distance;
            }
        }
        public string AfterRsrpAvg
        {
            get
            {
                if (AfterTpItem.RsrpCount > 0)
                {
                    return Math.Round(AfterTpItem.Rsrp / AfterTpItem.RsrpCount, 2).ToString();
                }
                else
                {
                    return "";
                }
            }
        }
        public string AfterSinrAvg
        {
            get
            {
                if (AfterTpItem.SinrCount > 0)
                {
                    return Math.Round(AfterTpItem.Sinr / AfterTpItem.SinrCount, 2).ToString();
                }
                else
                {
                    return "";
                }
            }
        }
        public string AfterAppSpeedAvg
        {
            get
            {
                if (AfterTpItem.AppSpeedCount > 0)
                {
                    return Math.Round(AfterTpItem.AppSpeed / AfterTpItem.AppSpeedCount, 2).ToString();
                }
                else
                {
                    return "";
                }
            }
        }
        public string AfterPdcpSpeedAvg
        {
            get
            {
                if (AfterTpItem.PdcpSpeedCount > 0)
                {
                    return Math.Round(AfterTpItem.PdcpSpeed / AfterTpItem.PdcpSpeedCount, 2).ToString();
                }
                else
                {
                    return "";
                }
            }
        }
        public string AfterRsrqAvg
        {
            get
            {
                if (AfterTpItem.RsrqCount > 0)
                {
                    return Math.Round(AfterTpItem.Rsrq / AfterTpItem.RsrqCount, 2).ToString();
                }
                else
                {
                    return "";
                }
            }
        }
        public string AfterRssiAvg
        {
            get
            {
                if (AfterTpItem.RssiCount > 0)
                {
                    return Math.Round(AfterTpItem.Rssi / AfterTpItem.RssiCount, 2).ToString();
                }
                else
                {
                    return "";
                }
            }
        }
        public string GridName
        {
            get
            {
                var gridDesc = GISManager.GetInstance().GetGridDesc(HOEvt.Longitude, HOEvt.Latitude);

                if (gridDesc != "")
                {
                    return gridDesc;
                }

                if (HOEvt.ID == (int)NREventManager.IRAT_L_NR_Redirect_Success)
                {
                    gridDesc = "LTE->NR";
                }
                else if (HOEvt.ID == (int)NREventManager.IRAT_NR_L_Redirect_Success)
                {
                    gridDesc = "NR->LTE";
                }
                
                return gridDesc;
            }
        }
        #endregion
    }

    public class ZTNRReSelectCellItem
    {
        public NRCell NrCell { get; set; }
        public string CellName { get; set; }
        public string TAC { get; set; }
        public string NCI { get; set; }
        public int ARFCN { get; set; }
        public int PCI { get; set; }
        public string CellID { get; set; }
        public string Distance { get; set; }
    }

    public class ZTNRReSelectTpItem
    {
        public float Rsrp { get; set; }
        public float RsrpCount { get; set; }
        public float Sinr { get; set; }
        public float SinrCount { get; set; }
        public float AppSpeed { get; set; }
        public float AppSpeedCount { get; set; }
        public float PdcpSpeed { get; set; }
        public float PdcpSpeedCount { get; set; }
        public float Rsrq { get; set; }
        public float RsrqCount { get; set; }
        public float Rssi { get; set; }
        public float RssiCount { get; set; }
    }

    public class ZTNRReSelectCondition
    {
        public int BeforeSecond { get; set; }           //切换前时长
        public int AfterSecond { get; set; }            //切换后时长
        public int SiteDistance { get; set; }            //同站距离

        public ZTNRReSelectCondition()
        {
            BeforeSecond = 5;
            AfterSecond = 5;
            SiteDistance = 50;
        }
    }
}
