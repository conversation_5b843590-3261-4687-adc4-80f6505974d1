﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTScanInterfereCoefficientStatForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(ZTScanInterfereCoefficientStatForm));
            this.contextMenuStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.tsmiExp2Xls = new System.Windows.Forms.ToolStripMenuItem();
            this.contextMenuStripSample = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.tsmiSample2Xls = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportToShp = new System.Windows.Forms.ToolStripMenuItem();
            this.tabControl1 = new System.Windows.Forms.TabControl();
            this.tabPage1 = new System.Windows.Forms.TabPage();
            this.tabControl = new DevExpress.XtraTab.XtraTabControl();
            this.xtraTabPage4 = new DevExpress.XtraTab.XtraTabPage();
            this.gridControlCellInfo900 = new DevExpress.XtraGrid.GridControl();
            this.gridViewCellInfo900 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumnCellName_900 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnCellFreqCount_900 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnInterfereFactor_900 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnInterfereFreqs_900 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnTestPointCount_900 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.xtraTabPage5 = new DevExpress.XtraTab.XtraTabPage();
            this.gridControlCellInfo1800 = new DevExpress.XtraGrid.GridControl();
            this.gridViewCellInfo1800 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.tabPage2 = new System.Windows.Forms.TabPage();
            this.tabControlSample = new DevExpress.XtraTab.XtraTabControl();
            this.xtraTabPage1 = new DevExpress.XtraTab.XtraTabPage();
            this.gridControlSampleTotal = new DevExpress.XtraGrid.GridControl();
            this.gridViewSampleTotal = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumnBandType = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnSampleCount_0_40 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnSampleCount_40_60 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnSampleCount_60_80 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnSampleCount_80 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnSampleCount_Invalid = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnHighInterfereFactor = new DevExpress.XtraGrid.Columns.GridColumn();
            this.xtraTabPage2 = new DevExpress.XtraTab.XtraTabPage();
            this.gridControlSample900 = new DevExpress.XtraGrid.GridControl();
            this.gridViewSample900 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumnLongitude = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnLatitude = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnCellName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnMainBCCHString = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnInterfereBCCHString = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnInterfereFactorShow = new DevExpress.XtraGrid.Columns.GridColumn();
            this.xtraTabPage3 = new DevExpress.XtraTab.XtraTabPage();
            this.gridControlSample1800 = new DevExpress.XtraGrid.GridControl();
            this.gridViewSample1800 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn6 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn7 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn8 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn9 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn10 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn11 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.panelControl1 = new DevExpress.XtraEditors.PanelControl();
            this.btnColorSetting = new DevExpress.XtraEditors.SimpleButton();
            this.gridColumn12 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn13 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.contextMenuStrip.SuspendLayout();
            this.contextMenuStripSample.SuspendLayout();
            this.tabControl1.SuspendLayout();
            this.tabPage1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.tabControl)).BeginInit();
            this.tabControl.SuspendLayout();
            this.xtraTabPage4.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlCellInfo900)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewCellInfo900)).BeginInit();
            this.xtraTabPage5.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlCellInfo1800)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewCellInfo1800)).BeginInit();
            this.tabPage2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.tabControlSample)).BeginInit();
            this.tabControlSample.SuspendLayout();
            this.xtraTabPage1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlSampleTotal)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewSampleTotal)).BeginInit();
            this.xtraTabPage2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlSample900)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewSample900)).BeginInit();
            this.xtraTabPage3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlSample1800)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewSample1800)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl1)).BeginInit();
            this.panelControl1.SuspendLayout();
            this.SuspendLayout();
            // 
            // contextMenuStrip
            // 
            this.contextMenuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.tsmiExp2Xls});
            this.contextMenuStrip.Name = "contextMenuStrip";
            this.contextMenuStrip.Size = new System.Drawing.Size(151, 26);
            // 
            // tsmiExp2Xls
            // 
            this.tsmiExp2Xls.Name = "tsmiExp2Xls";
            this.tsmiExp2Xls.Size = new System.Drawing.Size(150, 22);
            this.tsmiExp2Xls.Text = "导出到Excel...";
            this.tsmiExp2Xls.Click += new System.EventHandler(this.tsmiExp2Xls_Click);
            // 
            // contextMenuStripSample
            // 
            this.contextMenuStripSample.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.tsmiSample2Xls,
            this.miExportToShp});
            this.contextMenuStripSample.Name = "contextMenuStripSample";
            this.contextMenuStripSample.Size = new System.Drawing.Size(151, 48);
            // 
            // tsmiSample2Xls
            // 
            this.tsmiSample2Xls.Name = "tsmiSample2Xls";
            this.tsmiSample2Xls.Size = new System.Drawing.Size(150, 22);
            this.tsmiSample2Xls.Text = "导出到Excel...";
            this.tsmiSample2Xls.Click += new System.EventHandler(this.tsmiSample2Xls_Click);
            // 
            // miExportToShp
            // 
            this.miExportToShp.Name = "miExportToShp";
            this.miExportToShp.Size = new System.Drawing.Size(150, 22);
            this.miExportToShp.Text = "导出到Shp...";
            this.miExportToShp.Click += new System.EventHandler(this.miExportToShp_Click);
            // 
            // tabControl1
            // 
            this.tabControl1.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
                        | System.Windows.Forms.AnchorStyles.Left)
                        | System.Windows.Forms.AnchorStyles.Right)));
            this.tabControl1.Controls.Add(this.tabPage1);
            this.tabControl1.Controls.Add(this.tabPage2);
            this.tabControl1.Location = new System.Drawing.Point(0, 0);
            this.tabControl1.Name = "tabControl1";
            this.tabControl1.SelectedIndex = 0;
            this.tabControl1.Size = new System.Drawing.Size(867, 413);
            this.tabControl1.TabIndex = 1;
            // 
            // tabPage1
            // 
            this.tabPage1.Controls.Add(this.tabControl);
            this.tabPage1.Location = new System.Drawing.Point(4, 23);
            this.tabPage1.Name = "tabPage1";
            this.tabPage1.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage1.Size = new System.Drawing.Size(859, 386);
            this.tabPage1.TabIndex = 0;
            this.tabPage1.Text = "小区干扰指数";
            this.tabPage1.UseVisualStyleBackColor = true;
            // 
            // tabControl
            // 
            this.tabControl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabControl.Location = new System.Drawing.Point(3, 3);
            this.tabControl.Name = "tabControl";
            this.tabControl.SelectedTabPage = this.xtraTabPage4;
            this.tabControl.Size = new System.Drawing.Size(853, 380);
            this.tabControl.TabIndex = 1;
            this.tabControl.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.xtraTabPage4,
            this.xtraTabPage5});
            // 
            // xtraTabPage4
            // 
            this.xtraTabPage4.Controls.Add(this.gridControlCellInfo900);
            this.xtraTabPage4.Name = "xtraTabPage4";
            this.xtraTabPage4.Size = new System.Drawing.Size(846, 350);
            this.xtraTabPage4.Text = "900";
            // 
            // gridControlCellInfo900
            // 
            this.gridControlCellInfo900.ContextMenuStrip = this.contextMenuStrip;
            this.gridControlCellInfo900.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlCellInfo900.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gridControlCellInfo900.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gridControlCellInfo900.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gridControlCellInfo900.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gridControlCellInfo900.EmbeddedNavigator.Buttons.Remove.Visible = false;
            this.gridControlCellInfo900.Location = new System.Drawing.Point(0, 0);
            this.gridControlCellInfo900.MainView = this.gridViewCellInfo900;
            this.gridControlCellInfo900.Name = "gridControlCellInfo900";
            this.gridControlCellInfo900.Size = new System.Drawing.Size(846, 350);
            this.gridControlCellInfo900.TabIndex = 3;
            this.gridControlCellInfo900.UseEmbeddedNavigator = true;
            this.gridControlCellInfo900.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridViewCellInfo900});
            // 
            // gridViewCellInfo900
            // 
            this.gridViewCellInfo900.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumnCellName_900,
            this.gridColumnCellFreqCount_900,
            this.gridColumnInterfereFactor_900,
            this.gridColumnInterfereFreqs_900,
            this.gridColumn12,
            this.gridColumnTestPointCount_900});
            this.gridViewCellInfo900.GridControl = this.gridControlCellInfo900;
            this.gridViewCellInfo900.Name = "gridViewCellInfo900";
            this.gridViewCellInfo900.OptionsBehavior.Editable = false;
            this.gridViewCellInfo900.OptionsDetail.EnableMasterViewMode = false;
            this.gridViewCellInfo900.OptionsSelection.MultiSelect = true;
            this.gridViewCellInfo900.OptionsView.ColumnAutoWidth = false;
            this.gridViewCellInfo900.OptionsView.ShowGroupPanel = false;
            this.gridViewCellInfo900.DoubleClick += new System.EventHandler(this.gridViewCellInfo900_DoubleClick);
            // 
            // gridColumnCellName_900
            // 
            this.gridColumnCellName_900.Caption = "小区";
            this.gridColumnCellName_900.FieldName = "CellName";
            this.gridColumnCellName_900.Name = "gridColumnCellName_900";
            this.gridColumnCellName_900.Visible = true;
            this.gridColumnCellName_900.VisibleIndex = 0;
            this.gridColumnCellName_900.Width = 127;
            // 
            // gridColumnCellFreqCount_900
            // 
            this.gridColumnCellFreqCount_900.Caption = "频点数";
            this.gridColumnCellFreqCount_900.FieldName = "CellFreqCount";
            this.gridColumnCellFreqCount_900.Name = "gridColumnCellFreqCount_900";
            this.gridColumnCellFreqCount_900.Visible = true;
            this.gridColumnCellFreqCount_900.VisibleIndex = 1;
            // 
            // gridColumnInterfereFactor_900
            // 
            this.gridColumnInterfereFactor_900.Caption = "干扰系数";
            this.gridColumnInterfereFactor_900.FieldName = "InterfereFactor";
            this.gridColumnInterfereFactor_900.Name = "gridColumnInterfereFactor_900";
            this.gridColumnInterfereFactor_900.Visible = true;
            this.gridColumnInterfereFactor_900.VisibleIndex = 2;
            // 
            // gridColumnInterfereFreqs_900
            // 
            this.gridColumnInterfereFreqs_900.Caption = "受干扰频点及贡献";
            this.gridColumnInterfereFreqs_900.FieldName = "InterfereFreqs";
            this.gridColumnInterfereFreqs_900.Name = "gridColumnInterfereFreqs_900";
            this.gridColumnInterfereFreqs_900.Visible = true;
            this.gridColumnInterfereFreqs_900.VisibleIndex = 3;
            this.gridColumnInterfereFreqs_900.Width = 228;
            // 
            // gridColumnTestPointCount_900
            // 
            this.gridColumnTestPointCount_900.Caption = "采样点数";
            this.gridColumnTestPointCount_900.FieldName = "TestPointCount";
            this.gridColumnTestPointCount_900.Name = "gridColumnTestPointCount_900";
            this.gridColumnTestPointCount_900.Visible = true;
            this.gridColumnTestPointCount_900.VisibleIndex = 5;
            this.gridColumnTestPointCount_900.Width = 72;
            // 
            // xtraTabPage5
            // 
            this.xtraTabPage5.Controls.Add(this.gridControlCellInfo1800);
            this.xtraTabPage5.Name = "xtraTabPage5";
            this.xtraTabPage5.Size = new System.Drawing.Size(846, 350);
            this.xtraTabPage5.Text = "1800";
            // 
            // gridControlCellInfo1800
            // 
            this.gridControlCellInfo1800.ContextMenuStrip = this.contextMenuStrip;
            this.gridControlCellInfo1800.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlCellInfo1800.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gridControlCellInfo1800.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gridControlCellInfo1800.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gridControlCellInfo1800.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gridControlCellInfo1800.EmbeddedNavigator.Buttons.Remove.Visible = false;
            this.gridControlCellInfo1800.Location = new System.Drawing.Point(0, 0);
            this.gridControlCellInfo1800.MainView = this.gridViewCellInfo1800;
            this.gridControlCellInfo1800.Name = "gridControlCellInfo1800";
            this.gridControlCellInfo1800.Size = new System.Drawing.Size(846, 350);
            this.gridControlCellInfo1800.TabIndex = 4;
            this.gridControlCellInfo1800.UseEmbeddedNavigator = true;
            this.gridControlCellInfo1800.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridViewCellInfo1800});
            // 
            // gridViewCellInfo1800
            // 
            this.gridViewCellInfo1800.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn1,
            this.gridColumn2,
            this.gridColumn3,
            this.gridColumn4,
            this.gridColumn13,
            this.gridColumn5});
            this.gridViewCellInfo1800.GridControl = this.gridControlCellInfo1800;
            this.gridViewCellInfo1800.Name = "gridViewCellInfo1800";
            this.gridViewCellInfo1800.OptionsBehavior.Editable = false;
            this.gridViewCellInfo1800.OptionsDetail.EnableMasterViewMode = false;
            this.gridViewCellInfo1800.OptionsSelection.MultiSelect = true;
            this.gridViewCellInfo1800.OptionsView.ColumnAutoWidth = false;
            this.gridViewCellInfo1800.OptionsView.ShowGroupPanel = false;
            this.gridViewCellInfo1800.DoubleClick += new System.EventHandler(this.gridViewCellInfo1800_DoubleClick);
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "小区";
            this.gridColumn1.FieldName = "CellName";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.Visible = true;
            this.gridColumn1.VisibleIndex = 0;
            this.gridColumn1.Width = 127;
            // 
            // gridColumn2
            // 
            this.gridColumn2.Caption = "频点数";
            this.gridColumn2.FieldName = "CellFreqCount";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.Visible = true;
            this.gridColumn2.VisibleIndex = 1;
            // 
            // gridColumn3
            // 
            this.gridColumn3.Caption = "干扰系数";
            this.gridColumn3.FieldName = "InterfereFactor";
            this.gridColumn3.Name = "gridColumn3";
            this.gridColumn3.Visible = true;
            this.gridColumn3.VisibleIndex = 2;
            // 
            // gridColumn4
            // 
            this.gridColumn4.Caption = "受干扰频点及贡献";
            this.gridColumn4.FieldName = "InterfereFreqs";
            this.gridColumn4.Name = "gridColumn4";
            this.gridColumn4.Visible = true;
            this.gridColumn4.VisibleIndex = 3;
            this.gridColumn4.Width = 228;
            // 
            // gridColumn5
            // 
            this.gridColumn5.Caption = "采样点数";
            this.gridColumn5.FieldName = "TestPointCount";
            this.gridColumn5.Name = "gridColumn5";
            this.gridColumn5.Visible = true;
            this.gridColumn5.VisibleIndex = 5;
            this.gridColumn5.Width = 72;
            // 
            // tabPage2
            // 
            this.tabPage2.Controls.Add(this.tabControlSample);
            this.tabPage2.Controls.Add(this.panelControl1);
            this.tabPage2.Location = new System.Drawing.Point(4, 23);
            this.tabPage2.Name = "tabPage2";
            this.tabPage2.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage2.Size = new System.Drawing.Size(859, 386);
            this.tabPage2.TabIndex = 1;
            this.tabPage2.Text = "采样点干扰指数";
            this.tabPage2.UseVisualStyleBackColor = true;
            // 
            // tabControlSample
            // 
            this.tabControlSample.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabControlSample.Location = new System.Drawing.Point(3, 36);
            this.tabControlSample.Name = "tabControlSample";
            this.tabControlSample.SelectedTabPage = this.xtraTabPage1;
            this.tabControlSample.Size = new System.Drawing.Size(853, 347);
            this.tabControlSample.TabIndex = 2;
            this.tabControlSample.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.xtraTabPage1,
            this.xtraTabPage2,
            this.xtraTabPage3});
            this.tabControlSample.SelectedPageChanged += new DevExpress.XtraTab.TabPageChangedEventHandler(this.tabControlSample_SelectedPageChanged);
            // 
            // xtraTabPage1
            // 
            this.xtraTabPage1.Controls.Add(this.gridControlSampleTotal);
            this.xtraTabPage1.Name = "xtraTabPage1";
            this.xtraTabPage1.Size = new System.Drawing.Size(846, 317);
            this.xtraTabPage1.Text = "汇总";
            // 
            // gridControlSampleTotal
            // 
            this.gridControlSampleTotal.ContextMenuStrip = this.contextMenuStripSample;
            this.gridControlSampleTotal.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlSampleTotal.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gridControlSampleTotal.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gridControlSampleTotal.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gridControlSampleTotal.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gridControlSampleTotal.EmbeddedNavigator.Buttons.Remove.Visible = false;
            this.gridControlSampleTotal.Location = new System.Drawing.Point(0, 0);
            this.gridControlSampleTotal.MainView = this.gridViewSampleTotal;
            this.gridControlSampleTotal.Name = "gridControlSampleTotal";
            this.gridControlSampleTotal.Size = new System.Drawing.Size(846, 317);
            this.gridControlSampleTotal.TabIndex = 2;
            this.gridControlSampleTotal.UseEmbeddedNavigator = true;
            this.gridControlSampleTotal.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridViewSampleTotal});
            // 
            // gridViewSampleTotal
            // 
            this.gridViewSampleTotal.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumnBandType,
            this.gridColumnSampleCount_0_40,
            this.gridColumnSampleCount_40_60,
            this.gridColumnSampleCount_60_80,
            this.gridColumnSampleCount_80,
            this.gridColumnSampleCount_Invalid,
            this.gridColumnHighInterfereFactor});
            this.gridViewSampleTotal.GridControl = this.gridControlSampleTotal;
            this.gridViewSampleTotal.Name = "gridViewSampleTotal";
            this.gridViewSampleTotal.OptionsBehavior.Editable = false;
            this.gridViewSampleTotal.OptionsDetail.EnableMasterViewMode = false;
            this.gridViewSampleTotal.OptionsSelection.MultiSelect = true;
            this.gridViewSampleTotal.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumnBandType
            // 
            this.gridColumnBandType.Caption = "频段";
            this.gridColumnBandType.FieldName = "BandTypeString";
            this.gridColumnBandType.Name = "gridColumnBandType";
            this.gridColumnBandType.Visible = true;
            this.gridColumnBandType.VisibleIndex = 0;
            // 
            // gridColumnSampleCount_0_40
            // 
            this.gridColumnSampleCount_0_40.Caption = "[0, 40)";
            this.gridColumnSampleCount_0_40.FieldName = "SampleCount_0_40";
            this.gridColumnSampleCount_0_40.Name = "gridColumnSampleCount_0_40";
            this.gridColumnSampleCount_0_40.Visible = true;
            this.gridColumnSampleCount_0_40.VisibleIndex = 1;
            // 
            // gridColumnSampleCount_40_60
            // 
            this.gridColumnSampleCount_40_60.Caption = "[40, 60)";
            this.gridColumnSampleCount_40_60.FieldName = "SampleCount_40_60";
            this.gridColumnSampleCount_40_60.Name = "gridColumnSampleCount_40_60";
            this.gridColumnSampleCount_40_60.Visible = true;
            this.gridColumnSampleCount_40_60.VisibleIndex = 2;
            // 
            // gridColumnSampleCount_60_80
            // 
            this.gridColumnSampleCount_60_80.Caption = "[60, 80)";
            this.gridColumnSampleCount_60_80.FieldName = "SampleCount_60_80";
            this.gridColumnSampleCount_60_80.Name = "gridColumnSampleCount_60_80";
            this.gridColumnSampleCount_60_80.Visible = true;
            this.gridColumnSampleCount_60_80.VisibleIndex = 3;
            // 
            // gridColumnSampleCount_80
            // 
            this.gridColumnSampleCount_80.Caption = "[80, +∞）";
            this.gridColumnSampleCount_80.FieldName = "SampleCount_80";
            this.gridColumnSampleCount_80.Name = "gridColumnSampleCount_80";
            this.gridColumnSampleCount_80.Visible = true;
            this.gridColumnSampleCount_80.VisibleIndex = 4;
            // 
            // gridColumnSampleCount_Invalid
            // 
            this.gridColumnSampleCount_Invalid.Caption = "无效点数";
            this.gridColumnSampleCount_Invalid.FieldName = "SampleCount_Invalid";
            this.gridColumnSampleCount_Invalid.Name = "gridColumnSampleCount_Invalid";
            this.gridColumnSampleCount_Invalid.Visible = true;
            this.gridColumnSampleCount_Invalid.VisibleIndex = 5;
            // 
            // gridColumnHighInterfereFactor
            // 
            this.gridColumnHighInterfereFactor.Caption = "高危点比例(%)";
            this.gridColumnHighInterfereFactor.FieldName = "HighInterfereFactor";
            this.gridColumnHighInterfereFactor.Name = "gridColumnHighInterfereFactor";
            this.gridColumnHighInterfereFactor.Visible = true;
            this.gridColumnHighInterfereFactor.VisibleIndex = 6;
            // 
            // xtraTabPage2
            // 
            this.xtraTabPage2.Controls.Add(this.gridControlSample900);
            this.xtraTabPage2.Name = "xtraTabPage2";
            this.xtraTabPage2.Size = new System.Drawing.Size(846, 317);
            this.xtraTabPage2.Text = "900采样点";
            // 
            // gridControlSample900
            // 
            this.gridControlSample900.ContextMenuStrip = this.contextMenuStripSample;
            this.gridControlSample900.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlSample900.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gridControlSample900.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gridControlSample900.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gridControlSample900.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gridControlSample900.EmbeddedNavigator.Buttons.Remove.Visible = false;
            this.gridControlSample900.Location = new System.Drawing.Point(0, 0);
            this.gridControlSample900.MainView = this.gridViewSample900;
            this.gridControlSample900.Name = "gridControlSample900";
            this.gridControlSample900.Size = new System.Drawing.Size(846, 317);
            this.gridControlSample900.TabIndex = 5;
            this.gridControlSample900.UseEmbeddedNavigator = true;
            this.gridControlSample900.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridViewSample900});
            // 
            // gridViewSample900
            // 
            this.gridViewSample900.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumnLongitude,
            this.gridColumnLatitude,
            this.gridColumnCellName,
            this.gridColumnMainBCCHString,
            this.gridColumnInterfereBCCHString,
            this.gridColumnInterfereFactorShow});
            this.gridViewSample900.GridControl = this.gridControlSample900;
            this.gridViewSample900.Name = "gridViewSample900";
            this.gridViewSample900.OptionsBehavior.Editable = false;
            this.gridViewSample900.OptionsSelection.MultiSelect = true;
            this.gridViewSample900.OptionsView.ColumnAutoWidth = false;
            this.gridViewSample900.OptionsView.ShowGroupPanel = false;
            this.gridViewSample900.DoubleClick += new System.EventHandler(this.gridViewSample900_DoubleClick);
            // 
            // gridColumnLongitude
            // 
            this.gridColumnLongitude.Caption = "经度";
            this.gridColumnLongitude.FieldName = "Longitude";
            this.gridColumnLongitude.Name = "gridColumnLongitude";
            this.gridColumnLongitude.Visible = true;
            this.gridColumnLongitude.VisibleIndex = 0;
            this.gridColumnLongitude.Width = 84;
            // 
            // gridColumnLatitude
            // 
            this.gridColumnLatitude.Caption = "纬度";
            this.gridColumnLatitude.FieldName = "Latitude";
            this.gridColumnLatitude.Name = "gridColumnLatitude";
            this.gridColumnLatitude.Visible = true;
            this.gridColumnLatitude.VisibleIndex = 1;
            this.gridColumnLatitude.Width = 82;
            // 
            // gridColumnCellName
            // 
            this.gridColumnCellName.Caption = "小区";
            this.gridColumnCellName.FieldName = "CellName";
            this.gridColumnCellName.Name = "gridColumnCellName";
            this.gridColumnCellName.Visible = true;
            this.gridColumnCellName.VisibleIndex = 2;
            this.gridColumnCellName.Width = 108;
            // 
            // gridColumnMainBCCHString
            // 
            this.gridColumnMainBCCHString.Caption = "一强频点";
            this.gridColumnMainBCCHString.FieldName = "MainBCCHString";
            this.gridColumnMainBCCHString.Name = "gridColumnMainBCCHString";
            this.gridColumnMainBCCHString.Visible = true;
            this.gridColumnMainBCCHString.VisibleIndex = 3;
            this.gridColumnMainBCCHString.Width = 225;
            // 
            // gridColumnInterfereBCCHString
            // 
            this.gridColumnInterfereBCCHString.Caption = "受干扰频点";
            this.gridColumnInterfereBCCHString.FieldName = "InterfereBCCHString";
            this.gridColumnInterfereBCCHString.Name = "gridColumnInterfereBCCHString";
            this.gridColumnInterfereBCCHString.Visible = true;
            this.gridColumnInterfereBCCHString.VisibleIndex = 4;
            this.gridColumnInterfereBCCHString.Width = 207;
            // 
            // gridColumnInterfereFactorShow
            // 
            this.gridColumnInterfereFactorShow.Caption = "干扰系数(%)";
            this.gridColumnInterfereFactorShow.FieldName = "InterfereFactorShow";
            this.gridColumnInterfereFactorShow.Name = "gridColumnInterfereFactorShow";
            this.gridColumnInterfereFactorShow.Visible = true;
            this.gridColumnInterfereFactorShow.VisibleIndex = 5;
            this.gridColumnInterfereFactorShow.Width = 98;
            // 
            // xtraTabPage3
            // 
            this.xtraTabPage3.Controls.Add(this.gridControlSample1800);
            this.xtraTabPage3.Name = "xtraTabPage3";
            this.xtraTabPage3.Size = new System.Drawing.Size(846, 317);
            this.xtraTabPage3.Text = "1800采样点";
            // 
            // gridControlSample1800
            // 
            this.gridControlSample1800.ContextMenuStrip = this.contextMenuStripSample;
            this.gridControlSample1800.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlSample1800.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gridControlSample1800.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gridControlSample1800.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gridControlSample1800.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gridControlSample1800.EmbeddedNavigator.Buttons.Remove.Visible = false;
            this.gridControlSample1800.Location = new System.Drawing.Point(0, 0);
            this.gridControlSample1800.MainView = this.gridViewSample1800;
            this.gridControlSample1800.Name = "gridControlSample1800";
            this.gridControlSample1800.Size = new System.Drawing.Size(846, 317);
            this.gridControlSample1800.TabIndex = 5;
            this.gridControlSample1800.UseEmbeddedNavigator = true;
            this.gridControlSample1800.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridViewSample1800});
            // 
            // gridViewSample1800
            // 
            this.gridViewSample1800.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn6,
            this.gridColumn7,
            this.gridColumn8,
            this.gridColumn9,
            this.gridColumn10,
            this.gridColumn11});
            this.gridViewSample1800.GridControl = this.gridControlSample1800;
            this.gridViewSample1800.Name = "gridViewSample1800";
            this.gridViewSample1800.OptionsBehavior.Editable = false;
            this.gridViewSample1800.OptionsSelection.MultiSelect = true;
            this.gridViewSample1800.OptionsView.ColumnAutoWidth = false;
            this.gridViewSample1800.OptionsView.ShowGroupPanel = false;
            this.gridViewSample1800.DoubleClick += new System.EventHandler(this.gridViewSample1800_DoubleClick);
            // 
            // gridColumn6
            // 
            this.gridColumn6.Caption = "经度";
            this.gridColumn6.FieldName = "Longitude";
            this.gridColumn6.Name = "gridColumn6";
            this.gridColumn6.Visible = true;
            this.gridColumn6.VisibleIndex = 0;
            this.gridColumn6.Width = 84;
            // 
            // gridColumn7
            // 
            this.gridColumn7.Caption = "纬度";
            this.gridColumn7.FieldName = "Latitude";
            this.gridColumn7.Name = "gridColumn7";
            this.gridColumn7.Visible = true;
            this.gridColumn7.VisibleIndex = 1;
            this.gridColumn7.Width = 82;
            // 
            // gridColumn8
            // 
            this.gridColumn8.Caption = "小区";
            this.gridColumn8.FieldName = "CellName";
            this.gridColumn8.Name = "gridColumn8";
            this.gridColumn8.Visible = true;
            this.gridColumn8.VisibleIndex = 2;
            this.gridColumn8.Width = 108;
            // 
            // gridColumn9
            // 
            this.gridColumn9.Caption = "一强频点";
            this.gridColumn9.FieldName = "MainBCCHString";
            this.gridColumn9.Name = "gridColumn9";
            this.gridColumn9.Visible = true;
            this.gridColumn9.VisibleIndex = 3;
            this.gridColumn9.Width = 225;
            // 
            // gridColumn10
            // 
            this.gridColumn10.Caption = "受干扰频点";
            this.gridColumn10.FieldName = "InterfereBCCHString";
            this.gridColumn10.Name = "gridColumn10";
            this.gridColumn10.Visible = true;
            this.gridColumn10.VisibleIndex = 4;
            this.gridColumn10.Width = 207;
            // 
            // gridColumn11
            // 
            this.gridColumn11.Caption = "干扰系数(%)";
            this.gridColumn11.FieldName = "InterfereFactorShow";
            this.gridColumn11.Name = "gridColumn11";
            this.gridColumn11.Visible = true;
            this.gridColumn11.VisibleIndex = 5;
            this.gridColumn11.Width = 98;
            // 
            // panelControl1
            // 
            this.panelControl1.Controls.Add(this.btnColorSetting);
            this.panelControl1.Dock = System.Windows.Forms.DockStyle.Top;
            this.panelControl1.Location = new System.Drawing.Point(3, 3);
            this.panelControl1.Name = "panelControl1";
            this.panelControl1.Size = new System.Drawing.Size(853, 33);
            this.panelControl1.TabIndex = 1;
            // 
            // btnColorSetting
            // 
            this.btnColorSetting.Location = new System.Drawing.Point(754, 5);
            this.btnColorSetting.Name = "btnColorSetting";
            this.btnColorSetting.Size = new System.Drawing.Size(75, 23);
            this.btnColorSetting.TabIndex = 0;
            this.btnColorSetting.Text = "着色设置";
            this.btnColorSetting.Click += new System.EventHandler(this.btnColorSetting_Click);
            // 
            // gridColumn12
            // 
            this.gridColumn12.Caption = "干扰频点及小区(CI)";
            this.gridColumn12.FieldName = "FreqCellNames";
            this.gridColumn12.Name = "gridColumn12";
            this.gridColumn12.Visible = true;
            this.gridColumn12.VisibleIndex = 4;
            this.gridColumn12.Width = 243;
            // 
            // gridColumn13
            // 
            this.gridColumn13.Caption = "干扰频点及小区(CI)";
            this.gridColumn13.FieldName = "FreqCellNames";
            this.gridColumn13.Name = "gridColumn13";
            this.gridColumn13.Visible = true;
            this.gridColumn13.VisibleIndex = 4;
            this.gridColumn13.Width = 243;
            // 
            // ZTScanInterfereCoefficientStatForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(866, 413);
            this.Controls.Add(this.tabControl1);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Name = "ZTScanInterfereCoefficientStatForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "扫频干扰指数";
            this.contextMenuStrip.ResumeLayout(false);
            this.contextMenuStripSample.ResumeLayout(false);
            this.tabControl1.ResumeLayout(false);
            this.tabPage1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.tabControl)).EndInit();
            this.tabControl.ResumeLayout(false);
            this.xtraTabPage4.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlCellInfo900)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewCellInfo900)).EndInit();
            this.xtraTabPage5.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlCellInfo1800)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewCellInfo1800)).EndInit();
            this.tabPage2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.tabControlSample)).EndInit();
            this.tabControlSample.ResumeLayout(false);
            this.xtraTabPage1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlSampleTotal)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewSampleTotal)).EndInit();
            this.xtraTabPage2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlSample900)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewSample900)).EndInit();
            this.xtraTabPage3.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlSample1800)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewSample1800)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl1)).EndInit();
            this.panelControl1.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.ContextMenuStrip contextMenuStrip;
        private System.Windows.Forms.ToolStripMenuItem tsmiExp2Xls;
        private System.Windows.Forms.TabControl tabControl1;
        private System.Windows.Forms.TabPage tabPage1;
        private System.Windows.Forms.TabPage tabPage2;
        private System.Windows.Forms.ContextMenuStrip contextMenuStripSample;
        private System.Windows.Forms.ToolStripMenuItem tsmiSample2Xls;
        private DevExpress.XtraTab.XtraTabControl tabControlSample;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage1;
        private DevExpress.XtraGrid.GridControl gridControlSampleTotal;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewSampleTotal;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnBandType;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnSampleCount_0_40;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnSampleCount_40_60;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnSampleCount_60_80;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnSampleCount_80;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnSampleCount_Invalid;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnHighInterfereFactor;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage2;
        private DevExpress.XtraGrid.GridControl gridControlSample900;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewSample900;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnLongitude;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnLatitude;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnCellName;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnMainBCCHString;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnInterfereBCCHString;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnInterfereFactorShow;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage3;
        private DevExpress.XtraGrid.GridControl gridControlSample1800;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewSample1800;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn6;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn7;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn8;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn9;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn10;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn11;
        private DevExpress.XtraEditors.PanelControl panelControl1;
        private DevExpress.XtraTab.XtraTabControl tabControl;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage4;
        private DevExpress.XtraGrid.GridControl gridControlCellInfo900;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewCellInfo900;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnCellName_900;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnCellFreqCount_900;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnInterfereFactor_900;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnInterfereFreqs_900;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnTestPointCount_900;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage5;
        private DevExpress.XtraGrid.GridControl gridControlCellInfo1800;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewCellInfo1800;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn5;
        private DevExpress.XtraEditors.SimpleButton btnColorSetting;
        private System.Windows.Forms.ToolStripMenuItem miExportToShp;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn12;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn13;
    }
}