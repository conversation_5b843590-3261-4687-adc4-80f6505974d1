using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using System.IO;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.Grid;
using MasterCom.MControls;
using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using MasterCom.RAMS.Net;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class TDPoorC2IRoadListForm : MinCloseForm
    {
        public TDPoorC2IRoadListForm(MainModel mainModel)
            : base(mainModel)
        {
            InitializeComponent();
            mapForm = mainModel.MainForm.GetMapForm();
            init();
            DisposeWhenClose = true;
        }
        private MapForm mapForm = null;

        private void init()
        {
            this.olvColumnSN.AspectGetter = delegate (object row)
            {
                if (row is TDPoorC2IRoadInfo)
                {
                    TDPoorC2IRoadInfo item = row as TDPoorC2IRoadInfo;
                    return item.SN;
                }
                return null;
            };

            this.olvColumnRoadName.AspectGetter = delegate (object row)
            {
                if (row is TDPoorC2IRoadInfo)
                {
                    TDPoorC2IRoadInfo item = row as TDPoorC2IRoadInfo;
                    return item.roadName;
                }
                return "";
            };

            this.olvColumnDitance.AspectGetter = delegate (object row)
            {
                if (row is TDPoorC2IRoadInfo)
                {
                    TDPoorC2IRoadInfo item = row as TDPoorC2IRoadInfo;
                    return item.Distance_Show;
                }
                return "";
            };


            this.olvColumnSample.AspectGetter = delegate (object row)
            {
                if (row is TDPoorC2IRoadInfo)
                {
                    TDPoorC2IRoadInfo item = row as TDPoorC2IRoadInfo;
                    return item.sampleLst.Count;
                }
                return "";
            };

            this.olvColumnMaxBler.AspectGetter = delegate (object row)
            {
                if (row is TDPoorC2IRoadInfo)
                {
                    TDPoorC2IRoadInfo item = row as TDPoorC2IRoadInfo;
                    return item.blerMax;
                }
                return "";
            };

            this.olvColumnMinBler.AspectGetter = delegate (object row)
            {
                if (row is TDPoorC2IRoadInfo)
                {
                    TDPoorC2IRoadInfo item = row as TDPoorC2IRoadInfo;
                    return item.blerMin;
                }
                return "";
            };

            this.olvColumnAvgBler.AspectGetter = delegate (object row)
            {
                if (row is TDPoorC2IRoadInfo)
                {
                    TDPoorC2IRoadInfo item = row as TDPoorC2IRoadInfo;
                    return item.blerAvg;
                }
                return "";
            };

            initData();

            this.olvColumnLongitudeMid.AspectGetter = delegate (object row)
            {
                if (row is TDPoorC2IRoadInfo)
                {
                    TDPoorC2IRoadInfo item = row as TDPoorC2IRoadInfo;
                    return item.LongitudeMid;
                }
                return "";
            };

            this.olvColumnLatitudeMid.AspectGetter = delegate (object row)
            {
                if (row is TDPoorC2IRoadInfo)
                {
                    TDPoorC2IRoadInfo item = row as TDPoorC2IRoadInfo;
                    return item.LatitudeMid;
                }
                return "";
            };
        }

        private void initData()
        {
            this.olvColumnMaxRscp.AspectGetter = delegate (object row)
            {
                if (row is TDPoorC2IRoadInfo)
                {
                    TDPoorC2IRoadInfo item = row as TDPoorC2IRoadInfo;
                    return getValidData(item.pccpchRscpSample, item.pccpchRscpMax);
                }
                return "";
            };

            this.olvColumnMinRscp.AspectGetter = delegate (object row)
            {
                if (row is TDPoorC2IRoadInfo)
                {
                    TDPoorC2IRoadInfo item = row as TDPoorC2IRoadInfo;
                    return getValidData(item.pccpchRscpSample, item.pccpchRscpMin);
                }
                return "";
            };

            this.olvColumnAvgRscp.AspectGetter = delegate (object row)
            {
                if (row is TDPoorC2IRoadInfo)
                {
                    TDPoorC2IRoadInfo item = row as TDPoorC2IRoadInfo;
                    return getValidData(item.pccpchRscpSample, item.pccpchRscpAvg);
                }
                return "";
            };

            this.olvColumnMaxPCCPCHC2I.AspectGetter = delegate (object row)
            {
                if (row is TDPoorC2IRoadInfo)
                {
                    TDPoorC2IRoadInfo item = row as TDPoorC2IRoadInfo;
                    return getValidData(item.pccpchC2ISample, item.pccpchC2IMax);
                }
                return "";
            };

            this.olvColumnMinPCCPCHC2I.AspectGetter = delegate (object row)
            {
                if (row is TDPoorC2IRoadInfo)
                {
                    TDPoorC2IRoadInfo item = row as TDPoorC2IRoadInfo;
                    return getValidData(item.pccpchC2ISample, item.pccpchC2IMin);
                }
                return "";
            };

            this.olvColumnAvgPCCPCHC2I.AspectGetter = delegate (object row)
            {
                if (row is TDPoorC2IRoadInfo)
                {
                    TDPoorC2IRoadInfo item = row as TDPoorC2IRoadInfo;
                    return getValidData(item.pccpchC2ISample, item.pccpchC2IAvg);
                }
                return "";
            };

            this.olvColumnMaxDPCHC2I.AspectGetter = delegate (object row)
            {
                if (row is TDPoorC2IRoadInfo)
                {
                    TDPoorC2IRoadInfo item = row as TDPoorC2IRoadInfo;
                    return getValidData(item.dpchC2ISample, item.dpchC2IMax);
                }
                return "";
            };

            this.olvColumnMinDPCHC2I.AspectGetter = delegate (object row)
            {
                if (row is TDPoorC2IRoadInfo)
                {
                    TDPoorC2IRoadInfo item = row as TDPoorC2IRoadInfo;
                    return getValidData(item.dpchC2ISample, item.dpchC2IMin);
                }
                return "";
            };

            this.olvColumnAvgDPCHC2I.AspectGetter = delegate (object row)
            {
                if (row is TDPoorC2IRoadInfo)
                {
                    TDPoorC2IRoadInfo item = row as TDPoorC2IRoadInfo;
                    return getValidData(item.dpchC2ISample, item.dpchC2IAvg);
                }
                return "";
            };
        }

        private object getValidData(float count, float data)
        {
            if (count > 0)
            {
                return data;
            }
            else
            {
                return "";
            }
        }

        public void FillData(List<TDPoorC2IRoadInfo> roadC2IList)
        {
            ListViewRoad.RebuildColumns();
            ListViewRoad.ClearObjects();
            ListViewRoad.SetObjects(roadC2IList);//(MainModel.TdPoorBlerRoadCovLst);
            MainModel.RefreshLegend();
            MapForm mf = MainModel.MainForm.GetMapForm();
            if (mf != null)
            {
                mf.GetCellLayer().Invalidate();
            }
        }

        private void listViewTotal_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            if (ListViewRoad.SelectedObject is TDPoorC2IRoadInfo)
            {
                TDPoorC2IRoadInfo info = ListViewRoad.SelectedObject as TDPoorC2IRoadInfo;

                mapForm.MainModel.SelectedTestPoints.Clear();
                mapForm.MainModel.SelectedTestPoints.AddRange(info.sampleLst);
                GoToView(info.sampleLst);
                mapForm.GetDTLayer().Invalidate();
            }
        }

        private void GoToView(List<TestPoint> tpList)
        {
            double ltLong = 100000;
            double ltLat = -100000;
            double brLong = -100000;
            double brLat = 100000;

            foreach (TestPoint tp in tpList)
            {
                if (tp.Longitude < ltLong)
                {
                    ltLong = tp.Longitude;
                }
                if (tp.Longitude > brLong)
                {
                    brLong = tp.Longitude;
                }
                if (tp.Latitude < brLat)
                {
                    brLat = tp.Latitude;
                }
                if (tp.Latitude > ltLat)
                {
                    ltLat = tp.Latitude;
                }
            }
            mapForm.GoToView(new DbRect(ltLong, ltLat, brLong, brLat));
        }

        private void miExpandAll_Click(object sender, EventArgs e)
        {
            ListViewRoad.ExpandAll();
        }

        private void miCallapsAll_Click(object sender, EventArgs e)
        {
            ListViewRoad.CollapseAll();
        }

        private void miExportExcel_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(ListViewRoad);
        }

        private void ctxMenu_Opening(object sender, CancelEventArgs e)
        {
            //
        }

        private void ListViewRoad_SelectedIndexChanged(object sender, EventArgs e)
        {
            //
        }

        private void TDPoorBlerRoadListForm_Load(object sender, EventArgs e)
        {
            //
        }
    }
}