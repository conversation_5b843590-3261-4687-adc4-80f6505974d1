﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;
namespace MasterCom.RAMS.ZTFunc
{
    public partial class LastRoadCalculate : MinCloseForm
    {
        public LastRoadCalculate(MainModel mainModel)
            :base(mainModel)
        {
            InitializeComponent();
        }

        public bool isMultiCells { get; set; }
        List<List<NPOIRow>> nrDatasList = new List<List<NPOIRow>>();
        List<string> sheetNames = new List<string>();

        internal void FillData()
        {
            BindingSource bindingSource = new BindingSource();
            bindingSource.DataSource = ZTDIYLastRoadCalculate.resultList;
            gridControl1.DataSource = bindingSource;
            gridControl1.RefreshDataSource();
        }

        public void FillData(List<List<NPOIRow>> nrDatasList, List<string> sheetNames)
        {
            BindingSource bindingSource = new BindingSource();
            bindingSource.DataSource = ZTDIYLastRoadCalculate.resultList;
            gridControl1.DataSource = bindingSource;
            gridControl1.RefreshDataSource();
            this.nrDatasList = nrDatasList;
            this.sheetNames = sheetNames;
        }

        private void miExportExcel_Click_1(object sender, EventArgs e)
        {
            //SaveFileDialog dlg = new SaveFileDialog();
            //dlg.Title = "导出后台预处理表";
            //dlg.RestoreDirectory = true;
            //dlg.Filter = "Excel文件(*.xls)|*.xls";
            //if (dlg.ShowDialog() == DialogResult.OK)
            //{
            //    string filename = dlg.FileName;
            //    this.gridControl1.ExportToExcelOld(filename);
            //    DevExpress.XtraEditors.XtraMessageBox.Show("导出成功！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            //}
            List<List<object>> exportList = GridViewTransfer.Transfer(this.gridControl1);
            ExcelNPOIManager.ExportToExcel(exportList);
        }

        private void 导出平面里程模板ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(nrDatasList, sheetNames);
        }
    }
}
