﻿using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Data;
using System.Text;
using System.Collections;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc.ZTWirelessNetTest
{
    #region 场景小区配置
    #region 从Excel读取场景小区信息
    public class WirelessNetTestSceneCell
    {
        private static WirelessNetTestSceneCell instance = null;
        public static WirelessNetTestSceneCell Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new WirelessNetTestSceneCell();
                }
                return instance;
            }
        }

        public Dictionary<string, WirelessNetTestDistrictInfo> DistrictDic { get; set; }

        #region 读取配置文件数据
        public void ReadExcel(string fileName)
        {
            DistrictDic = new Dictionary<string, WirelessNetTestDistrictInfo>();
            if (string.IsNullOrEmpty(fileName) || !System.IO.File.Exists(fileName))
            {
                return;
            }

            var districtScene = new List<Scene>();
            var districtNrCell = new List<Cell>();
            var districtLteCell = new List<Cell>();

            WaitBox.ProgressPercent = 10;
            try
            {
                using (DataSet ds = ExcelNPOIManager.ImportFromExcel(fileName))
                {
                    if (ds == null || ds.Tables == null || ds.Tables.Count < 3)
                    {
                        throw (new Exception("Excel中缺少数据或读取失败！格式请参见模板"));
                    }

                    DataTable senceTB = ds.Tables[0];
                    loadConfig(senceTB, "场景", districtScene, loadSenceConfig);
                    WaitBox.ProgressPercent += 20;

                    DataTable nrCellTB = ds.Tables[1];
                    loadConfig(nrCellTB, "NR小区", districtNrCell, loadCellConfig, NetType.NR);
                    WaitBox.ProgressPercent += 20;

                    DataTable LteCellTB = ds.Tables[2];
                    loadConfig(LteCellTB, "LTE小区", districtLteCell, loadCellConfig, NetType.LTE);
                    WaitBox.ProgressPercent += 20;
                }
            }
            catch (Exception e)
            {
                //MessageBox.Show(e.Message);
            }

            dealScene(districtScene, DistrictDic);

            dealCell(districtNrCell, DistrictDic);

            dealCell(districtLteCell, DistrictDic);

            foreach (var district in DistrictDic.Values)
            {
                district.Calculate();
            }
        }

        #region 读取数据
        private void loadSenceConfig(DataRow dr, ICollection collection, params object[] objs)
        {
            var res = collection as List<Scene>;

            string districtName = dr["地市"]?.ToString();
            string sceneName = dr["场景名称"]?.ToString();
            string subSceneName = dr["子场景名称"]?.ToString();
            string type = dr["类别"]?.ToString();
            var projectType = (WirelessNetTestProjectType)Enum.Parse(typeof(WirelessNetTestProjectType), type);

            Scene scene = new Scene()
            {
                DistrictName = districtName,
                SceneName = sceneName,
                SubSceneName = subSceneName,
                Type = projectType
            };
            res.Add(scene);
        }

        private void loadCellConfig(DataRow dr, ICollection collection, params object[] objs)
        {
            var netType = (NetType)objs[0];
            var res = collection as List<Cell>;

            string districtName = dr["地市"]?.ToString();
            string sceneName = dr["场景名称"]?.ToString();
            string subSceneName = dr["子场景名称"]?.ToString();
            string nodeBID = dr["基站标识"]?.ToString();
            string ci = dr["CI"]?.ToString();
            string type = dr["备注"]?.ToString();
            var projectType = (WirelessNetTestProjectType)Enum.Parse(typeof(WirelessNetTestProjectType), type);

            Cell cell = new Cell()
            {
                DistrictName = districtName,
                SceneName = sceneName,
                SubSceneName = subSceneName,
                NodeBID = nodeBID,
                Ci = ci,
                Type = projectType,
                NetType = netType
            };
            res.Add(cell);
        }

        private delegate void GetTableData(DataRow dr, ICollection collection, params object[] objs);

        private void loadConfig(DataTable dt, string desc, ICollection collection, GetTableData func, params object[] objs)
        {
            try
            {
                foreach (DataRow dr in dt.Rows)
                {
                    func(dr, collection, objs);
                }

                //if (collection.Count == 0)
                //{
                //    throw (new Exception($"[{desc}]sheet无数据"));
                //}
            }
            catch (Exception ex)
            {
                throw (new Exception($"加载[{desc}]sheet出错:{ex.Message}{ex.StackTrace}"));
            }
        }
        #endregion

        #region 处理数据
        private void dealScene(List<Scene> districtScene, Dictionary<string, WirelessNetTestDistrictInfo> districtDic)
        {
            foreach (var scene in districtScene)
            {
                if (!districtDic.TryGetValue(scene.DistrictName, out var districtInfo))
                {
                    districtInfo = new WirelessNetTestDistrictInfo();
                    districtDic.Add(scene.DistrictName, districtInfo);
                }

                districtInfo.AddDistrict(scene);
            }
        }

        private void dealCell(List<Cell> districtCell, Dictionary<string, WirelessNetTestDistrictInfo> districtDic)
        {
            foreach (var cell in districtCell)
            {
                if (districtDic.TryGetValue(cell.DistrictName, out var districtInfo))
                {
                    districtInfo.AddCell(cell);
                }
            }
        }
        #endregion
        #endregion

        #region 获取有效的<文件ID, 场景信息> 集合
        public Dictionary<int, WirelessNetTestScene> GetVliadFilesScene(List<FileInfo> files, string districtName
            , WirelessNetTestProjectType type)
        {
            var fileSceneDic = new Dictionary<int, WirelessNetTestScene>();

            //获取对应地市-类别的场景列表
            Dictionary<string, WirelessNetTestScene> sceneDic;
            if (!DistrictDic.TryGetValue(districtName, out var districtScene)
                || !districtScene.SceneTypeDic.TryGetValue(type, out sceneDic))
            {
                WirelessNetTestConfig.Instance.WriteLog($"不存在[{districtName}{type}]的场景信息", "Error");
                return fileSceneDic;
            }

            foreach (var file in files)
            {
                string sceneName = getSceneByFileName(file.Name);
                if (sceneDic.TryGetValue(sceneName, out var sceneInfo))
                {
                    fileSceneDic[file.ID] = sceneInfo;
                }
            }

            return fileSceneDic;
        }

        public Dictionary<int, WirelessNetTestScene> FileSceneDic { get; set; }
        public Dictionary<int, WirelessNetTestSubScene> FileSubSceneDic { get; set; }

        public bool SetValidFileScene(FileInfo file, Dictionary<string, WirelessNetTestScene> sceneDic)
        {
            //获取对应地市-类别的场景列表
            if (sceneDic == null)
            {
                return false;
            }

            string sceneName = getSceneByFileName(file.Name);
            if (sceneDic.TryGetValue(sceneName, out var sceneInfo))
            {
                FileSceneDic[file.ID] = sceneInfo;
                sceneInfo.Files.Add(file);

                string subSceneName = getSubSceneByFileName(file.Name);
                if (sceneInfo.SubSceneDic.TryGetValue(subSceneName, out var subSceneInfo))
                {
                    FileSubSceneDic[file.ID] = subSceneInfo;
                    subSceneInfo.Files.Add(file);
                }
                return true;
            }

            return false;
        }

        protected string getSceneByFileName(string fileName)
        {
            //TODO : 从文件名获取场景字段 - 暂不知道规则,瞎编一个
            string sceneName = fileName;
            var str = fileName.Split('_');
            if (str.Length > 1)
            {
                sceneName = str[0];
            }

            return sceneName;
        }

        protected string getSubSceneByFileName(string fileName)
        {
            //TODO : 从文件名获取场景字段 - 暂不知道规则,瞎编一个
            string sceneName = fileName;
            var str = fileName.Split('_');
            if (str.Length > 2)
            {
                sceneName = $"{str[0]}_{str[1]}";
            }

            return sceneName;
        }

        #endregion

        public void Clear() 
        {
            FileSceneDic = null;
            FileSubSceneDic = null;
        }

        public class Scene
        {
            public string DistrictName { get; set; }
            public string SceneName { get; set; }
            public string SubSceneName { get; set; }
            public WirelessNetTestProjectType Type { get; set; }
        }

        public class Cell
        {
            public string DistrictName { get; set; }
            public string SceneName { get; set; }
            public string SubSceneName { get; set; }
            public string NodeBID { get; set; }
            public string Ci { get; set; }
            public WirelessNetTestProjectType Type { get; set; }
            public NetType NetType { get; set; } = NetType.UNKNOWN;
        }
    }
    #endregion

    //类别-场景-子场景-基站-小区

    /*
     * 类别-场景-基站
     * 类别-场景-小区
     * 类别-子场景-基站
     * 类别-子场景-小区
     */

    public class WirelessNetTestDistrictInfo
    {
        public WirelessNetTestDistrictInfo()
        {
            SceneTypeDic = new Dictionary<WirelessNetTestProjectType, Dictionary<string, WirelessNetTestScene>>();
            //SubSceneTypeDic = new Dictionary<WirelessNetTestProjectType, Dictionary<string, WirelessNetTestSubScene>>();
        }

        //<类别,<场景Key,场景>>
        public Dictionary<WirelessNetTestProjectType, Dictionary<string, WirelessNetTestScene>> SceneTypeDic { get; set; }

        //public Dictionary<WirelessNetTestProjectType, Dictionary<string, WirelessNetTestSubScene>> SubSceneTypeDic { get; set; }



        public void AddDistrict(WirelessNetTestSceneCell.Scene district)
        {
            if (!SceneTypeDic.TryGetValue(district.Type, out var sceneDic))
            {
                sceneDic = new Dictionary<string, WirelessNetTestScene>();
                SceneTypeDic.Add(district.Type, sceneDic);
            }

            if (!sceneDic.TryGetValue(district.SceneName, out var scene))
            {
                scene = new WirelessNetTestScene(district.SceneName, district.Type);
                sceneDic.Add(district.SceneName, scene);
            }

            if (!string.IsNullOrEmpty(district.SubSceneName))
            {
                scene.AddSubScene(district.SceneName, district.SubSceneName, district.Type);
            }
        }

        public void AddCell(WirelessNetTestSceneCell.Cell cell)
        {
            if (!SceneTypeDic.TryGetValue(cell.Type, out var sceneDic))
            {
                return;
            }
            if (!sceneDic.TryGetValue(cell.SceneName, out var scene))
            {
                return;
            }

            scene.AddCell(cell.SubSceneName, cell.NodeBID, cell.Ci, cell.NetType);
        }

        public void Calculate()
        {
            foreach (var sceneType in SceneTypeDic.Values)
            {
                foreach (var scene in sceneType.Values)
                {
                    scene.CalCulate();
                }
            }
        }
    }

    public class WirelessNetTestScene
    {
        public WirelessNetTestScene(string sceneName, WirelessNetTestProjectType type)
        {
            SceneName = sceneName;
            Type = type;

            SubSceneDic = new Dictionary<string, WirelessNetTestSubScene>();
            LteBtsDic = new Dictionary<string, WirelessNetTestBts>();
            NrBtsDic = new Dictionary<string, WirelessNetTestBts>();
            Files = new List<FileInfo>();
        }

        public string SceneName { get; set; }
        public WirelessNetTestProjectType Type { get; set; }
        //子场景Key,子场景>
        public Dictionary<string, WirelessNetTestSubScene> SubSceneDic { get; set; }

        public Dictionary<string, WirelessNetTestBts> LteBtsDic { get; set; }
        public Dictionary<string, WirelessNetTestBts> NrBtsDic { get; set; }

        public int LteCellCount { get; protected set; }
        public int LteBtsCount { get; protected set; }
        public int NrCellCount { get; protected set; }
        public int NrBtsCount { get; protected set; }

        //统计时,每个场景包含的文件
        public List<FileInfo> Files { get; set; }

        public void AddSubScene(string sceneName, string subSceneName, WirelessNetTestProjectType type)
        {
            if (!SubSceneDic.TryGetValue(subSceneName, out var _))
            {
                var subScene = new WirelessNetTestSubScene(sceneName, subSceneName, type);
                SubSceneDic.Add(subSceneName, subScene);
            }
        }

        public void AddCell(string subSceneName, string eNodeBID, string ci, NetType netType)
        {
            if (!string.IsNullOrEmpty(subSceneName)
                && SubSceneDic.TryGetValue(subSceneName, out var subScene))
            {
                //存在子场景则添加到[场景小区]及[子场景小区]中
                addCell(eNodeBID, ci, netType, subScene.NrBtsDic, subScene.LteBtsDic);
            }

            //不存在子场景则直接添加到[场景小区]中
            addCell(eNodeBID, ci, netType, NrBtsDic, LteBtsDic);
        }

        private void addCell(string eNodeBID, string ci, NetType netType
            , Dictionary<string, WirelessNetTestBts> nrDic, Dictionary<string, WirelessNetTestBts> lteDic)
        {
            Dictionary<string, WirelessNetTestBts> dic;
            if (netType == NetType.NR)
            {
                dic = nrDic;
            }
            else if (netType == NetType.LTE)
            {
                dic = lteDic;
            }
            else
            {
                return;
            }

            if (!dic.TryGetValue(eNodeBID, out var cell))
            {
                cell = new WirelessNetTestBts(eNodeBID, netType);
                dic.Add(eNodeBID, cell);
            }
            cell.AddCI(ci);
        }

        public void CalCulate()
        {
            LteBtsCount = LteBtsDic.Count;
            NrBtsCount = NrBtsDic.Count;

            foreach (var bts in LteBtsDic.Values)
            {
                LteCellCount += bts.CIDic.Count;
            }

            foreach (var bts in NrBtsDic.Values)
            {
                NrCellCount += bts.CIDic.Count;
            }

            foreach (var subScene in SubSceneDic.Values)
            {
                subScene.CalCulate();
            }
        }
    }

    public class WirelessNetTestSubScene : WirelessNetTestScene
    {
        public WirelessNetTestSubScene(string sceneName, string subSceneName, WirelessNetTestProjectType type)
          : base(sceneName, type)
        {
            SubSceneName = subSceneName;
        }

        public string SubSceneName { get; set; }

        //public WirelessNetTestSubScene(string sceneName, string subSceneName, WirelessNetTestProjectType type)
        //{
        //    SceneName = sceneName;
        //    SubSceneName = subSceneName;
        //    Type = type;

        //    LteBtsDic = new Dictionary<string, WirelessNetTestBts>();
        //    NrBtsDic = new Dictionary<string, WirelessNetTestBts>();
        //    Files = new List<FileInfo>();
        //}

        //统计时,每个子场景包含的文件
        //public List<FileInfo> Files { get; set; }

        //public string SceneName { get; set; }
        //public string SubSceneName { get; set; }
        //public WirelessNetTestProjectType Type { get; set; }

        //public Dictionary<string, WirelessNetTestBts> LteBtsDic { get; set; }
        //public Dictionary<string, WirelessNetTestBts> NrBtsDic { get; set; }

        //public int LteCellCount { get; private set; }
        //public int LteBtsCount { get; private set; }
        //public int NrCellCount { get; private set; }
        //public int NrBtsCount { get; private set; }

        //public void CalCulate()
        //{
        //    LteBtsCount = LteBtsDic.Count;
        //    NrBtsCount = NrBtsDic.Count;

        //    foreach (var bts in LteBtsDic.Values)
        //    {
        //        LteCellCount += bts.CIDic.Count;
        //    }

        //    foreach (var bts in NrBtsDic.Values)
        //    {
        //        NrCellCount += bts.CIDic.Count;
        //    }
        //}
    }

    public class WirelessNetTestBts
    {
        public WirelessNetTestBts(string eNodeBID, NetType netType)
        {
            NetType = netType;
            ENodeBID = eNodeBID;
            CIDic = new Dictionary<string, string>();
        }

        public NetType NetType { get; set; }
        public string ENodeBID { get; set; }
        public Dictionary<string, string> CIDic { get; set; }

        public void AddCI(string ci)
        {
            if (!CIDic.TryGetValue(ci, out var _))
            {
                CIDic.Add(ci, "");
            }
        }
    }
    #endregion
}
