﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTDIYLastRoadSetTimeForm : BaseDialog
    {
        public ZTDIYLastRoadSetTimeForm()
        {
            InitializeComponent();
        }
        Dictionary<int, List<RoundCfg>> roundCfgDic;
        Dictionary<string, MapCfg> mapCfgDic;

        public ZTDIYLastRoadSetTimeForm(Dictionary<string, MapCfg> mapCfgDicTem, List<string> rejectMapList,
            Dictionary<int, List<RoundCfg>> roundCfgDicTem, bool isShow)
        {
            InitializeComponent();
            this.mapCfgDic = new Dictionary<string, MapCfg>();
            this.mapCfgDic = mapCfgDicTem;
            this.roundCfgDic = new Dictionary<int, List<RoundCfg>>();
            this.roundCfgDic = roundCfgDicTem;
            foreach (string strMap in mapCfgDicTem.Keys)
            {
                mapCb.Items.Add(strMap);
            }
            if (mapCb.Items.Count > 0)
                mapCb.SelectedIndex = 0;

            if (isShow)
            {
                foreach (string strMap in rejectMapList)
                {
                    delMapCb.Items.Add(strMap);
                }
            }
            else
            {
                label4.Text = "地域类型";
                delMapCb.Items.Add("本地网");
                delMapCb.Items.Add("高速");
                delMapCb.Items.Add("国道");
            }
            if (delMapCb.Items.Count > 0)
            {
                delMapCb.SelectedIndex = 0;
            }
            this.chbOnlyFour.Visible = isShow;
        }

        public String MapName
        {
            get { return this.mapCb.Text; }
        }

        public String rejMapName
        {
            get { return this.delMapCb.Text; }
        }

        public int iAreaType
        {
            get { return delMapCb.SelectedIndex + 1; }
        }

        public bool isOnlyCalByFour
        {
            get { return this.chbOnlyFour.Checked; }
        }

        public string StrSRound
        {
            get { return cbSYearRound.SelectedItem.ToString(); } 
        }

        public string StrERound
        {
            get { return cbEYearRound.SelectedItem.ToString(); }
        }

        private void mapCb_SelectedIndexChanged(object sender, EventArgs e)
        {
            string strMapName = "";
            if (mapCb.SelectedItem == null)
                return;
            strMapName = mapCb.SelectedItem.ToString();
            cbSYearRound.SelectedItem = null;
            cbEYearRound.SelectedItem = null;
            cbSYearRound.Items.Clear();
            cbEYearRound.Items.Clear();
            if (this.roundCfgDic.ContainsKey(this.mapCfgDic[strMapName].IMapId))
            {
                foreach (RoundCfg rCfg in this.roundCfgDic[this.mapCfgDic[strMapName].IMapId])
                {
                    cbSYearRound.Items.Add(rCfg.StrRound);
                    cbEYearRound.Items.Add(rCfg.StrRound);
                }
                if (cbSYearRound.Items.Count > 0)
                {
                    cbSYearRound.SelectedIndex = 0;
                    cbEYearRound.SelectedIndex = 0;
                }
            }
        }

        private void btCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }
    }
}
