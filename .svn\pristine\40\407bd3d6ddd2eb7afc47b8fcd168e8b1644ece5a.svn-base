﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTDIYCellAbnormalAngleSettingDlg : Form
    {
        public ZTDIYCellAbnormalAngleSettingDlg()
        {
            InitializeComponent();
        }

        public int RxlevMin
        {
            get { return (int)spinEditRxlev.Value; }
        }

        public int DistanceMin
        {
            get { return (int)spinEditDistance.Value; }
        }

        public int AngleMin
        {
            get { return (int)spinEditAngle.Value; }
        }

        private void simpleButtonOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void simpleButtonCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }
    }
}
