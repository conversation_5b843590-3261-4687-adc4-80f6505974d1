﻿using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class LastRoadFileStatusForm : MinCloseForm
    {
        public LastRoadFileStatusForm(MainModel mainModel)
            :base(mainModel)
        {
            InitializeComponent();
        }

        public void FillData(List<FileIDStatusInfo> cityFileStatusList)
        {
            BindingSource bindingSource = new BindingSource();
            bindingSource.DataSource = cityFileStatusList;
            gridData.DataSource = bindingSource;
            gridData.RefreshDataSource();
        }

        private void outPutExcel_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(gridView1);
        }

    }
}
