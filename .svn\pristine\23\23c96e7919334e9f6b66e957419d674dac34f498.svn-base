﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.Util;

namespace MasterCom.RAMS.Model
{
    public partial class TestRoundInfoForm : BaseForm
    {
        List<TestRound> testRounds = null;
        TestRound curTestRound = null;
        bool isAdd = false;
        public TestRoundInfoForm(List<TestRound> existRounds, TestRound roud2Modify, bool isAdd)
        {
            InitializeComponent();
            this.isAdd = isAdd;
            this.testRounds = existRounds;
            init(roud2Modify);
        }

        private void init(TestRound round2Modify)
        {
            if (!isAdd)
            {
                cbxYear.Enabled = false;
                cbxMonth.Enabled = false;
            }
            this.curTestRound = round2Modify;
            if (curTestRound != null)
            {
                cbxYear.SelectedItem = curTestRound.Year.ToString();
                cbxMonth.SelectedItem = curTestRound.Month.ToString();
                dateEditBegin.EditValue = curTestRound.BeginTime;
                dateEditEnd.EditValue = curTestRound.EndTime;
                txtDesc.Text = curTestRound.StrDesc;
            }
            else
            {
                curTestRound = new TestRound();
                cbxYear.SelectedItem = "2016";
                cbxMonth.SelectedItem = "1";
            }
        }

        public TestRound Round
        {
            get
            {
                return curTestRound;
            }
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            if (isAdd)
            {
                bool isExists = testRounds.Exists(delegate(TestRound u)
                { return (u.Year.ToString() == cbxYear.Text && u.Month.ToString() == cbxMonth.Text); });

                if (isExists)
                {
                    MessageBox.Show("已存在相同的测试时段，请修改！");
                    return;
                }
            }
            curTestRound.Year = int.Parse(cbxYear.SelectedItem.ToString());
            curTestRound.Month = int.Parse(cbxMonth.SelectedItem.ToString());
            curTestRound.BeginTime = (DateTime)dateEditBegin.EditValue;
            DateTime endtime = (DateTime)dateEditEnd.EditValue;
            curTestRound.EndTime = endtime.Date.AddDays(1).AddMilliseconds(-1);
            curTestRound.StrDesc = txtDesc.Text;

            curTestRound.BeginTimeInt = (int)(JavaDate.GetMilliseconds(curTestRound.BeginTime) / 1000);
            curTestRound.EndTimeInt = (int)(JavaDate.GetMilliseconds(curTestRound.EndTime) / 1000);
            DialogResult = DialogResult.OK;
        }
    }
}
