﻿using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    //public class VolteCallCycleInfo
    //{
    //    public VolteCallCycleInfo(string fileName, Event callBeginEvt)
    //    {
    //        this.FileName = fileName;
    //        this.CallBeginEvt = callBeginEvt;
    //    }

    //    public int SN { get; set; }

    //    public string ReasonsDes
    //    {
    //        get
    //        {
    //            StringBuilder strb = new StringBuilder();
    //            foreach (string str in ReasonList)
    //            {
    //                strb.Append(str + ";");
    //            }
    //            if (strb.Length > 0)
    //            {
    //                strb.Remove(strb.Length - 1, 1);
    //            }
    //            return strb.ToString();
    //        }
    //    }
        
    //    public List<string> ReasonList { get; set; } = new List<string>();

    //    public string GridName { get; set; }
    //    public string RoadName { get; set; }

    //    public string FileName { get; set; }

    //    public float? MosValue { get; private set; }
    //    public TestPoint MosTestPoint { get; private set; }
    //    public string MosTime
    //    {
    //        get
    //        {
    //            if (MosTestPoint != null)
    //            {
    //                return MosTestPoint.DateTimeStringWithMillisecond;
    //            }
    //            return "";
    //        }
    //    }

    //    public string MosLongitude
    //    {
    //        get
    //        {
    //            if (MosTestPoint != null)
    //            {
    //                return MosTestPoint.Longitude.ToString();
    //            }
    //            return "";
    //        }
    //    }

    //    public string MosLatitude
    //    {
    //        get
    //        {
    //            if (MosTestPoint != null)
    //            {
    //                return MosTestPoint.Latitude.ToString();
    //            }
    //            return "";
    //        }
    //    }

    //    public Event CallBeginEvt { get; set; }
    //    public string CallBeginEvtName
    //    {
    //        get
    //        {
    //            if (CallBeginEvt != null)
    //            {
    //                return CallBeginEvt.Name;
    //            }
    //            return "";
    //        }
    //    }

    //    public Event EsrvccEvt { get; set; }
        
    //    public List<TestPoint> TestPoints { get; set; } = new List<TestPoint>();

    //    #region Mos时段内信息

    //    private TimePeriod mosTimePeriod = new TimePeriod();
    //    /// <summary>
    //    /// //一个MOS区间(设x为MOS值时间点，采样点分析的时间段为【x-10,x-2】)
    //    /// </summary>
    //    public TimePeriod MosTimePeriod
    //    {
    //        get { return mosTimePeriod; }
    //    }
    //    /// <summary>
    //    /// 一个MOS区间的采样点信息
    //    /// </summary>
    //    public List<TestPoint> MosTestPoints { get; set; } = new List<TestPoint>();

    //    public Message RRCReestablishMsg { get; set; }
        
    //    public List<Event> HandOverEvents { get; set; } = new List<Event>();
    //    public int HandOverEventsCount
    //    {
    //        get
    //        {
    //            return HandOverEvents.Count;
    //        }
    //    }
    //    public float? RsrpAvg_MosPeriod { get; set; }
    //    public float? SinrAvg_MosPeriod { get; set; }

    //    #endregion

    //    public void SetMosValue(TestPoint mosTestPoint, float mosValue)
    //    {
    //        this.MosTestPoint = mosTestPoint;
    //        this.MosValue = (float)Math.Round(mosValue, 2);
    //        this.mosTimePeriod = new TimePeriod(mosTestPoint.DateTime.AddSeconds(-10), mosTestPoint.DateTime.AddSeconds(-2));
    //        this.GridName = GISManager.GetInstance().GetGridDesc(mosTestPoint.Longitude, mosTestPoint.Latitude);
    //        this.RoadName = GISManager.GetInstance().GetRoadPlaceDesc(mosTestPoint.Longitude, mosTestPoint.Latitude);
    //        getValidInfo();
    //    }
    //    protected void getValidInfo()//获取到mos信息后核查事件、信令是否在有效时间内
    //    {
    //        int tpIdx1 = 0;
    //        this.MosTestPoints = MOSAnaManager.getTestPoinsByPeriod(this.mosTimePeriod, this.TestPoints, ref tpIdx1);

    //        //呼叫起始事件时间点<= esrvcc事件时间点<= Mos时间段
    //        if (this.CallBeginEvt != null && this.CallBeginEvt.DateTime > this.mosTimePeriod.BeginTime)
    //        {
    //            this.CallBeginEvt = null;
    //        }

    //        if (this.EsrvccEvt != null)
    //        {
    //            if (this.CallBeginEvt != null && this.CallBeginEvt.DateTime > this.EsrvccEvt.DateTime)
    //            {
    //                this.EsrvccEvt = null;
    //            }
    //            if (this.EsrvccEvt.DateTime > this.mosTimePeriod.BeginTime)
    //            {
    //                this.EsrvccEvt = null;
    //            }
    //        }

    //        if (this.RRCReestablishMsg != null && !this.mosTimePeriod.Contains(this.RRCReestablishMsg.DateTime))
    //        {
    //            this.RRCReestablishMsg = null;
    //        }

    //        for (int i = 0; i < this.HandOverEvents.Count; i++)
    //        {
    //            Event evt = this.HandOverEvents[i];
    //            if (!this.mosTimePeriod.Contains(evt.DateTime))
    //            {
    //                this.HandOverEvents.Remove(evt);
    //                i--;
    //            }
    //        }

    //        getAvgRsrpSinr();
    //    }

    //    private void getAvgRsrpSinr()
    //    {
    //        int rsrpTpCount = 0;
    //        float rsrpSum = 0;
    //        int sinrTpCount = 0;
    //        float sinrSum = 0;
    //        foreach (TestPoint tp in this.MosTestPoints)
    //        {
    //            float? rsrp = (float?)tp["lte_RSRP"];
    //            if (rsrp != null)
    //            {
    //                rsrpTpCount++;
    //                rsrpSum += (float)rsrp;
    //            }

    //            float? sinr = (float?)tp["lte_SINR"];
    //            if (sinr != null)
    //            {
    //                sinrTpCount++;
    //                sinrSum += (float)sinr;
    //            }
    //        }
    //        this.RsrpAvg_MosPeriod = rsrpTpCount > 0 ? (float?)Math.Round(rsrpSum / rsrpTpCount, 2) : null;
    //        this.SinrAvg_MosPeriod = sinrTpCount > 0 ? (float?)Math.Round(sinrSum / sinrTpCount, 2) : null;
    //    }
    //}

    //public class VoLteWeakMosReasonInfo
    //{
    //    public VoLteWeakMosReasonInfo(string reasonName)
    //    {
    //        this.Reason = reasonName;
    //    }
    //    public int MosTpCount { get; set; }
    //    public int WeakMosTpCount { get; set; }
    //    public double WeakMosTpPer
    //    {
    //        get
    //        {
    //            if (MosTpCount != 0)
    //            {
    //                return Math.Round((double)100 * WeakMosTpCount / MosTpCount, 2);
    //            }
    //            return 0;
    //        }
    //    }

    //    public string Reason { get; set; }
    //    public int ReasonMosTpCount { get; set; }
    //    public double ReasonMosTpRate
    //    {
    //        get
    //        {
    //            if (WeakMosTpCount != 0)
    //            {
    //                return Math.Round((double)100 * ReasonMosTpCount / WeakMosTpCount, 2);
    //            }
    //            return 0;
    //        }
    //    }
    //}


    public class VoLteWeakMosReasonInfo : WeakMosReasonInfoBase
    {
        public VoLteWeakMosReasonInfo(string fileName, Event callBeginEvt)
            : base(fileName, callBeginEvt)
        {
        }

        public DataInfo OppositeRsrpInfo { get; private set; } = new DataInfo();
        public DataInfo OppositeSinrInfo { get; private set; } = new DataInfo();

        protected override float? getRsrp(TestPoint tp)
        {
            return (float?)tp["lte_RSRP"];
        }

        protected override float? getSinr(TestPoint tp)
        {
            return (float?)tp["lte_SINR"];
        }

        public void SetOppositeInfo(VoLteWeakMosReasonInfo info)
        {
            OppositeRsrpInfo = info.RsrpInfo;
            OppositeSinrInfo = info.SinrInfo;
        }
    }
}
