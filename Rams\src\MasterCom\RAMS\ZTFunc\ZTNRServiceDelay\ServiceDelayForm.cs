﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc.ZTNRServiceDelay
{
    public partial class ServiceDelayForm : MinCloseForm
    {
        public ServiceDelayForm()
            : base(MainModel.GetInstance())
        {
            InitializeComponent();
            DisposeWhenClose = true;
        }

        public void FillData(List<SingleServiceDelayInfo> calls)
        {
            gridControl.DataSource = calls;
            gridControl.RefreshDataSource();
        }

        private void miReplay_Click(object sender, EventArgs e)
        {
            int[] rows = bandedGridView.GetSelectedRows();
            if (rows.Length == 0)
            {
                MessageBox.Show("请选择要回放的接入文件！");
                return;
            }
            SingleServiceDelayInfo call = bandedGridView.GetRow(rows[0]) as SingleServiceDelayInfo;
            if (call == null)
            {
                return;
            }

            MainModel.MainForm.NeedChangeWorkSpace(true);
            PreNextMinutesForm preNextMinutesForm = new PreNextMinutesForm(false);
            preNextMinutesForm.Pre = 2;
            preNextMinutesForm.Next = 2;
            if (preNextMinutesForm.ShowDialog() == DialogResult.OK)
            {
                QueryCondition condition = new QueryCondition();
                condition.isCompareMode = true;

                MasterCom.RAMS.Model.FileInfo fileInfo = new FileInfo();

                fileInfo.ID = call.OneEvent.FileID;
                fileInfo.ProjectID = call.OneEvent.ProjectType;
                fileInfo.LogTable = call.OneEvent.LogTable;
                fileInfo.ServiceType = call.OneEvent.ServiceType;
                fileInfo.SampleTbName = call.OneEvent.SampleTbName;
                condition.FileInfos.Add(fileInfo);


                int pre = preNextMinutesForm.Pre;
                int next = preNextMinutesForm.Next;
                DateTime timeStart = call.OneEvent.DateTime.AddMinutes(-pre);
                DateTime timeEnd = call.OneEvent.DateTime.AddMinutes(next);

                condition.Periods.Add(new TimePeriod(timeStart, timeEnd));
                try
                {
                    ReplayFileWithinCompare query = new ReplayFileWithinCompare(MainModel);

                    condition.DistrictID = call.OneEvent.DistrictID;
                    query.SetQueryCondition(condition);
                    query.Query();
                }
                catch
                {
                    MainModel.MainForm.CancelChange = true;
                }
            }
            else
            {
                MainModel.MainForm.CancelChange = true;
            }
            MainModel.MainForm.ChangeWorkSpace();
        }

        private void miExport2Xls_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(bandedGridView);
        }

    }
}
