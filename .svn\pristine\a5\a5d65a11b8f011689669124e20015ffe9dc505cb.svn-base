using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.Func
{
    partial class NeighboursForm : Form
    {
        public NeighboursForm(MainModel mainModel, Cell cell)
        {
            InitializeComponent();
            this.mainModel = mainModel;
            this.cell = cell;
            textBoxID.Text = cell.ID.ToString();
            textBoxName.Text = cell.Name;
            cell.NeighbourCellIds.Clear();
            if (cell.NeighbourCellIds.Count == 0)
            {
                QueryCellNeighbourInfo queryNeighbour = new QueryCellNeighbourInfo(mainModel, cell.ID);
                queryNeighbour.Query();
            }
            cells = cell.GetNeighbourCells();
            listBoxCell.DataSource = cells;
            listBoxCell.DisplayMember = "Name";
            checkButtonState();
        }

        private void listBoxCell_SelectedIndexChanged(object sender, EventArgs e)
        {
            checkButtonState();
        }

        private void buttonLocationCell_Click(object sender, EventArgs e)
        {
            mainModel.SelectedCell = cell;
            mainModel.FireSelectedCellChanged(this);
        }

        private void buttonCellInfo_Click(object sender, EventArgs e)
        {
            new CellInfoForm(mainModel, cell).Show(Owner);
        }

        private void buttonShow_Click(object sender, EventArgs e)
        {
            mainModel.NeighbourCells.Clear();
            mainModel.NeighbourCells.AddRange(cells);
            mainModel.FireCellDrawInfoChanged(this);
        }

        private void buttonInfo_Click(object sender, EventArgs e)
        {
            new CellInfoForm(mainModel, (Cell)listBoxCell.SelectedItem).Show(Owner);
        }

        private void buttonNeighbours_Click(object sender, EventArgs e)
        {
            new NeighboursForm(mainModel, (Cell)listBoxCell.SelectedItem).Show(Owner);
        }

        private void buttonInterference_Click(object sender, EventArgs e)
        {
            int mode = 0;
            if (radioButtonBCCHTCH.Checked)
            {
                mode = 0;
            }
            else if (radioButtonBCCH.Checked)
            {
                mode = 1;
            }
            else if (radioButtonTCH.Checked)
            {
                mode = 2;
            }
            new InterferenceForm(mainModel, (Cell)listBoxCell.SelectedItem, mode).Show(Owner);
        }

        private void buttonCoBSIC_Click(object sender, EventArgs e)
        {
            new CoBSICForm(mainModel, (Cell)listBoxCell.SelectedItem).Show(Owner);
        }

        private void buttonLocation_Click(object sender, EventArgs e)
        {
            mainModel.SelectedCell = (Cell)listBoxCell.SelectedItem;
            mainModel.FireSelectedCellChanged(this);
        }

        private void buttonOK_Click(object sender, EventArgs e)
        {
            Close();
        }

        private void checkButtonState()
        {
            bool cellSelected = listBoxCell.SelectedItem != null;
            buttonInfo.Enabled = cellSelected;
            buttonNeighbours.Enabled = cellSelected;
            radioButtonBCCHTCH.Enabled = cellSelected;
            radioButtonBCCH.Enabled = cellSelected;
            radioButtonTCH.Enabled = cellSelected;
            buttonInterference.Enabled = cellSelected;
            buttonCoBSIC.Enabled = cellSelected;
            buttonLocation.Enabled = cellSelected;
        }

        private void InitializeComponent()
        {
            System.Windows.Forms.Label label8;
            System.Windows.Forms.Label labelID;
            System.Windows.Forms.GroupBox groupBox2;
            System.Windows.Forms.GroupBox groupBox3;
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(NeighboursForm));
            this.buttonShow = new System.Windows.Forms.Button();
            this.buttonCellInfo = new System.Windows.Forms.Button();
            this.buttonLocationCell = new System.Windows.Forms.Button();
            this.radioButtonTCH = new System.Windows.Forms.RadioButton();
            this.radioButtonBCCH = new System.Windows.Forms.RadioButton();
            this.radioButtonBCCHTCH = new System.Windows.Forms.RadioButton();
            this.buttonCoBSIC = new System.Windows.Forms.Button();
            this.buttonInterference = new System.Windows.Forms.Button();
            this.buttonNeighbours = new System.Windows.Forms.Button();
            this.buttonInfo = new System.Windows.Forms.Button();
            this.buttonLocation = new System.Windows.Forms.Button();
            this.listBoxCell = new System.Windows.Forms.ListBox();
            this.buttonOK = new System.Windows.Forms.Button();
            this.textBoxName = new System.Windows.Forms.TextBox();
            this.textBoxID = new System.Windows.Forms.TextBox();
            label8 = new System.Windows.Forms.Label();
            labelID = new System.Windows.Forms.Label();
            groupBox2 = new System.Windows.Forms.GroupBox();
            groupBox3 = new System.Windows.Forms.GroupBox();
            groupBox2.SuspendLayout();
            groupBox3.SuspendLayout();
            this.SuspendLayout();
            // 
            // label8
            // 
            label8.AutoSize = true;
            label8.Location = new System.Drawing.Point(154, 15);
            label8.Name = "label8";
            label8.Size = new System.Drawing.Size(35, 12);
            label8.TabIndex = 2;
            label8.Text = "&Name:";
            // 
            // labelID
            // 
            labelID.AutoSize = true;
            labelID.Location = new System.Drawing.Point(9, 15);
            labelID.Name = "labelID";
            labelID.Size = new System.Drawing.Size(23, 12);
            labelID.TabIndex = 0;
            labelID.Text = "I&D:";
            // 
            // groupBox2
            // 
            groupBox2.Controls.Add(this.buttonShow);
            groupBox2.Controls.Add(this.buttonCellInfo);
            groupBox2.Controls.Add(this.buttonLocationCell);
            groupBox2.Controls.Add(this.radioButtonTCH);
            groupBox2.Controls.Add(this.radioButtonBCCH);
            groupBox2.Controls.Add(this.radioButtonBCCHTCH);
            groupBox2.Controls.Add(this.buttonCoBSIC);
            groupBox2.Controls.Add(this.buttonInterference);
            groupBox2.Controls.Add(this.buttonNeighbours);
            groupBox2.Controls.Add(this.buttonInfo);
            groupBox2.Controls.Add(this.buttonLocation);
            groupBox2.Location = new System.Drawing.Point(193, 38);
            groupBox2.Name = "groupBox2";
            groupBox2.Size = new System.Drawing.Size(121, 320);
            groupBox2.TabIndex = 5;
            groupBox2.TabStop = false;
            groupBox2.Text = "Operation";
            // 
            // buttonShow
            // 
            this.buttonShow.Location = new System.Drawing.Point(6, 77);
            this.buttonShow.Name = "buttonShow";
            this.buttonShow.Size = new System.Drawing.Size(109, 23);
            this.buttonShow.TabIndex = 2;
            this.buttonShow.Text = "&Show Neighbours";
            this.buttonShow.UseVisualStyleBackColor = true;
            this.buttonShow.Click += new System.EventHandler(this.buttonShow_Click);
            // 
            // buttonCellInfo
            // 
            this.buttonCellInfo.Location = new System.Drawing.Point(6, 48);
            this.buttonCellInfo.Name = "buttonCellInfo";
            this.buttonCellInfo.Size = new System.Drawing.Size(109, 23);
            this.buttonCellInfo.TabIndex = 1;
            this.buttonCellInfo.Text = "&Cell Info...";
            this.buttonCellInfo.UseVisualStyleBackColor = true;
            this.buttonCellInfo.Click += new System.EventHandler(this.buttonCellInfo_Click);
            // 
            // buttonLocationCell
            // 
            this.buttonLocationCell.Location = new System.Drawing.Point(6, 19);
            this.buttonLocationCell.Name = "buttonLocationCell";
            this.buttonLocationCell.Size = new System.Drawing.Size(109, 23);
            this.buttonLocationCell.TabIndex = 0;
            this.buttonLocationCell.Text = "&Location Cell";
            this.buttonLocationCell.UseVisualStyleBackColor = true;
            this.buttonLocationCell.Click += new System.EventHandler(this.buttonLocationCell_Click);
            // 
            // radioButtonTCH
            // 
            this.radioButtonTCH.AutoSize = true;
            this.radioButtonTCH.Location = new System.Drawing.Point(9, 210);
            this.radioButtonTCH.Name = "radioButtonTCH";
            this.radioButtonTCH.Size = new System.Drawing.Size(71, 16);
            this.radioButtonTCH.TabIndex = 7;
            this.radioButtonTCH.Text = "&TCH Only";
            this.radioButtonTCH.UseVisualStyleBackColor = true;
            // 
            // radioButtonBCCH
            // 
            this.radioButtonBCCH.AutoSize = true;
            this.radioButtonBCCH.Location = new System.Drawing.Point(9, 187);
            this.radioButtonBCCH.Name = "radioButtonBCCH";
            this.radioButtonBCCH.Size = new System.Drawing.Size(77, 16);
            this.radioButtonBCCH.TabIndex = 6;
            this.radioButtonBCCH.Text = "&BCCH Only";
            this.radioButtonBCCH.UseVisualStyleBackColor = true;
            // 
            // radioButtonBCCHTCH
            // 
            this.radioButtonBCCHTCH.AutoSize = true;
            this.radioButtonBCCHTCH.Checked = true;
            this.radioButtonBCCHTCH.Location = new System.Drawing.Point(9, 164);
            this.radioButtonBCCHTCH.Name = "radioButtonBCCHTCH";
            this.radioButtonBCCHTCH.Size = new System.Drawing.Size(83, 16);
            this.radioButtonBCCHTCH.TabIndex = 5;
            this.radioButtonBCCHTCH.TabStop = true;
            this.radioButtonBCCHTCH.Text = "&BCCH && TCH";
            this.radioButtonBCCHTCH.UseVisualStyleBackColor = true;
            // 
            // buttonCoBSIC
            // 
            this.buttonCoBSIC.Location = new System.Drawing.Point(6, 262);
            this.buttonCoBSIC.Name = "buttonCoBSIC";
            this.buttonCoBSIC.Size = new System.Drawing.Size(109, 23);
            this.buttonCoBSIC.TabIndex = 9;
            this.buttonCoBSIC.Text = "&Co-BSIC...";
            this.buttonCoBSIC.UseVisualStyleBackColor = true;
            this.buttonCoBSIC.Click += new System.EventHandler(this.buttonCoBSIC_Click);
            // 
            // buttonInterference
            // 
            this.buttonInterference.Location = new System.Drawing.Point(6, 233);
            this.buttonInterference.Name = "buttonInterference";
            this.buttonInterference.Size = new System.Drawing.Size(109, 23);
            this.buttonInterference.TabIndex = 8;
            this.buttonInterference.Text = "&Interference...";
            this.buttonInterference.UseVisualStyleBackColor = true;
            this.buttonInterference.Click += new System.EventHandler(this.buttonInterference_Click);
            // 
            // buttonNeighbours
            // 
            this.buttonNeighbours.Location = new System.Drawing.Point(6, 135);
            this.buttonNeighbours.Name = "buttonNeighbours";
            this.buttonNeighbours.Size = new System.Drawing.Size(109, 23);
            this.buttonNeighbours.TabIndex = 4;
            this.buttonNeighbours.Text = "&Neighbours...";
            this.buttonNeighbours.UseVisualStyleBackColor = true;
            this.buttonNeighbours.Click += new System.EventHandler(this.buttonNeighbours_Click);
            // 
            // buttonInfo
            // 
            this.buttonInfo.Location = new System.Drawing.Point(6, 106);
            this.buttonInfo.Name = "buttonInfo";
            this.buttonInfo.Size = new System.Drawing.Size(109, 23);
            this.buttonInfo.TabIndex = 3;
            this.buttonInfo.Text = "&Neighbour Info...";
            this.buttonInfo.UseVisualStyleBackColor = true;
            this.buttonInfo.Click += new System.EventHandler(this.buttonInfo_Click);
            // 
            // buttonLocation
            // 
            this.buttonLocation.Location = new System.Drawing.Point(6, 291);
            this.buttonLocation.Name = "buttonLocation";
            this.buttonLocation.Size = new System.Drawing.Size(109, 23);
            this.buttonLocation.TabIndex = 10;
            this.buttonLocation.Text = "&Location Neighbour";
            this.buttonLocation.UseVisualStyleBackColor = true;
            this.buttonLocation.Click += new System.EventHandler(this.buttonLocation_Click);
            // 
            // groupBox3
            // 
            groupBox3.Controls.Add(this.listBoxCell);
            groupBox3.Location = new System.Drawing.Point(12, 38);
            groupBox3.Name = "groupBox3";
            groupBox3.Size = new System.Drawing.Size(175, 320);
            groupBox3.TabIndex = 4;
            groupBox3.TabStop = false;
            groupBox3.Text = "&Neighbour Cell";
            // 
            // listBoxCell
            // 
            this.listBoxCell.Anchor = (((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
                        | System.Windows.Forms.AnchorStyles.Left)
                        | System.Windows.Forms.AnchorStyles.Right);
            this.listBoxCell.FormattingEnabled = true;
            this.listBoxCell.HorizontalScrollbar = true;
            this.listBoxCell.ItemHeight = 12;
            this.listBoxCell.Location = new System.Drawing.Point(6, 19);
            this.listBoxCell.Name = "listBoxCell";
            this.listBoxCell.Size = new System.Drawing.Size(163, 280);
            this.listBoxCell.TabIndex = 0;
            this.listBoxCell.SelectedIndexChanged += new System.EventHandler(this.listBoxCell_SelectedIndexChanged);
            this.listBoxCell.DoubleClick += new System.EventHandler(this.buttonLocation_Click);
            // 
            // buttonOK
            // 
            this.buttonOK.DialogResult = System.Windows.Forms.DialogResult.OK;
            this.buttonOK.Location = new System.Drawing.Point(227, 364);
            this.buttonOK.Name = "buttonOK";
            this.buttonOK.Size = new System.Drawing.Size(87, 23);
            this.buttonOK.TabIndex = 6;
            this.buttonOK.Text = "&OK";
            this.buttonOK.UseVisualStyleBackColor = true;
            this.buttonOK.Click += new System.EventHandler(this.buttonOK_Click);
            // 
            // textBoxName
            // 
            this.textBoxName.BackColor = System.Drawing.SystemColors.Window;
            this.textBoxName.Location = new System.Drawing.Point(199, 12);
            this.textBoxName.Name = "textBoxName";
            this.textBoxName.ReadOnly = true;
            this.textBoxName.Size = new System.Drawing.Size(115, 21);
            this.textBoxName.TabIndex = 3;
            // 
            // textBoxID
            // 
            this.textBoxID.BackColor = System.Drawing.SystemColors.Window;
            this.textBoxID.Enabled = false;
            this.textBoxID.Location = new System.Drawing.Point(49, 12);
            this.textBoxID.Name = "textBoxID";
            this.textBoxID.ReadOnly = true;
            this.textBoxID.Size = new System.Drawing.Size(99, 21);
            this.textBoxID.TabIndex = 1;
            // 
            // NeighboursForm
            // 
            this.AcceptButton = this.buttonOK;
            this.CancelButton = this.buttonOK;
            this.ClientSize = new System.Drawing.Size(326, 399);
            this.Controls.Add(groupBox2);
            this.Controls.Add(groupBox3);
            this.Controls.Add(label8);
            this.Controls.Add(this.textBoxName);
            this.Controls.Add(this.textBoxID);
            this.Controls.Add(labelID);
            this.Controls.Add(this.buttonOK);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle;
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "NeighboursForm";
            this.ShowInTaskbar = false;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "Cell Neighbours";
            groupBox2.ResumeLayout(false);
            groupBox2.PerformLayout();
            groupBox3.ResumeLayout(false);
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        private readonly MainModel mainModel;

        private readonly Cell cell;

        private readonly List<Cell> cells;

        private TextBox textBoxID;

        private TextBox textBoxName;

        private ListBox listBoxCell;

        private Button buttonLocationCell;

        private Button buttonCellInfo;

        private Button buttonShow;

        private Button buttonInfo;

        private Button buttonNeighbours;

        private RadioButton radioButtonBCCHTCH;

        private RadioButton radioButtonBCCH;

        private RadioButton radioButtonTCH;

        private Button buttonInterference;

        private Button buttonCoBSIC;

        private Button buttonLocation;

        private Button buttonOK;
    }
}
