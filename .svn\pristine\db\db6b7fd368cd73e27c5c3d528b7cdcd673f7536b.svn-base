﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NRReasonPnlWeakCover : NRReasonPanelBase
    {
        public NRReasonPnlWeakCover()
        {
            InitializeComponent();
        }

        public override void AttachReason(NRReasonBase reason)
        {
            base.AttachReason(reason);
            numMaxRSRP.ValueChanged -= numMaxRSRP_ValueChanged;
            numMaxRSRP.Value = (decimal)((NRReasonWeakCover)reason).MaxRSRP;
            numMaxRSRP.ValueChanged += numMaxRSRP_ValueChanged;
        }

        void numMaxRSRP_ValueChanged(object sender, EventArgs e)
        {
            ((NRReasonWeakCover)reason).MaxRSRP = (float)numMaxRSRP.Value;
        }

    }
}
