﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTLowSpeedConvergeSampleForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.listViewTotal = new BrightIdeasSoftware.TreeListView();
            this.olvColumnSN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnSampleCount = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnTAC = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnECI = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnSpeed = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLongitude = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLatitude = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnTime = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBand = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnDistance = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnSINR = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCode0Qam64Rate = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCode1Qam64Rate = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnDoubleRankRate1 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnDoubleRankRate2 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnPDSCH_BLER_Avg = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnPDCCH_DL_Grant_Count_Avg = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRsrp = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.contextMenuStripBadRxQualConverge = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.ToolStripMenuItemExport = new System.Windows.Forms.ToolStripMenuItem();
            this.ToolStripMenuItemExpand = new System.Windows.Forms.ToolStripMenuItem();
            this.ToolStripMenuItemFold = new System.Windows.Forms.ToolStripMenuItem();
            this.xtraTabControl1 = new DevExpress.XtraTab.XtraTabControl();
            this.xtraTabPage1 = new DevExpress.XtraTab.XtraTabPage();
            ((System.ComponentModel.ISupportInitialize)(this.listViewTotal)).BeginInit();
            this.contextMenuStripBadRxQualConverge.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).BeginInit();
            this.xtraTabControl1.SuspendLayout();
            this.xtraTabPage1.SuspendLayout();
            this.SuspendLayout();
            // 
            // listViewTotal
            // 
            this.listViewTotal.AllColumns.Add(this.olvColumnSN);
            this.listViewTotal.AllColumns.Add(this.olvColumnSampleCount);
            this.listViewTotal.AllColumns.Add(this.olvColumnCellName);
            this.listViewTotal.AllColumns.Add(this.olvColumnTAC);
            this.listViewTotal.AllColumns.Add(this.olvColumnECI);
            this.listViewTotal.AllColumns.Add(this.olvColumnSpeed);
            this.listViewTotal.AllColumns.Add(this.olvColumnRsrp);
            this.listViewTotal.AllColumns.Add(this.olvColumnSINR);
            this.listViewTotal.AllColumns.Add(this.olvColumnCode0Qam64Rate);
            this.listViewTotal.AllColumns.Add(this.olvColumnCode1Qam64Rate);
            this.listViewTotal.AllColumns.Add(this.olvColumnDoubleRankRate1);
            this.listViewTotal.AllColumns.Add(this.olvColumnDoubleRankRate2);
            this.listViewTotal.AllColumns.Add(this.olvColumnPDSCH_BLER_Avg);
            this.listViewTotal.AllColumns.Add(this.olvColumnPDCCH_DL_Grant_Count_Avg);
            this.listViewTotal.AllColumns.Add(this.olvColumnLongitude);
            this.listViewTotal.AllColumns.Add(this.olvColumnLatitude);
            this.listViewTotal.AllColumns.Add(this.olvColumnTime);
            this.listViewTotal.AllColumns.Add(this.olvColumnBand);
            this.listViewTotal.AllColumns.Add(this.olvColumnDistance);
            this.listViewTotal.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnSN,
            this.olvColumnSampleCount,
            this.olvColumnCellName,
            this.olvColumnTAC,
            this.olvColumnECI,
            this.olvColumnSpeed,
            this.olvColumnRsrp,
            this.olvColumnSINR,
            this.olvColumnCode0Qam64Rate,
            this.olvColumnCode1Qam64Rate,
            this.olvColumnDoubleRankRate1,
            this.olvColumnDoubleRankRate2,
            this.olvColumnPDSCH_BLER_Avg,
            this.olvColumnPDCCH_DL_Grant_Count_Avg,
            this.olvColumnLongitude,
            this.olvColumnLatitude,
            this.olvColumnTime,
            this.olvColumnBand,
            this.olvColumnDistance});
            this.listViewTotal.ContextMenuStrip = this.contextMenuStripBadRxQualConverge;
            this.listViewTotal.Cursor = System.Windows.Forms.Cursors.Default;
            this.listViewTotal.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listViewTotal.FullRowSelect = true;
            this.listViewTotal.GridLines = true;
            this.listViewTotal.HeaderWordWrap = true;
            this.listViewTotal.IsNeedShowOverlay = false;
            this.listViewTotal.Location = new System.Drawing.Point(0, 0);
            this.listViewTotal.Name = "listViewTotal";
            this.listViewTotal.OwnerDraw = true;
            this.listViewTotal.ShowGroups = false;
            this.listViewTotal.Size = new System.Drawing.Size(874, 322);
            this.listViewTotal.TabIndex = 4;
            this.listViewTotal.UseCompatibleStateImageBehavior = false;
            this.listViewTotal.View = System.Windows.Forms.View.Details;
            this.listViewTotal.VirtualMode = true;
            this.listViewTotal.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.listViewTotal_MouseDoubleClick);
            // 
            // olvColumnSN
            // 
            this.olvColumnSN.AspectName = "Index";
            this.olvColumnSN.HeaderFont = null;
            this.olvColumnSN.Text = "序号";
            // 
            // olvColumnSampleCount
            // 
            this.olvColumnSampleCount.HeaderFont = null;
            this.olvColumnSampleCount.Text = "采样点数";
            // 
            // olvColumnCellName
            // 
            this.olvColumnCellName.HeaderFont = null;
            this.olvColumnCellName.Text = "小区名";
            this.olvColumnCellName.Width = 105;
            // 
            // olvColumnTAC
            // 
            this.olvColumnTAC.HeaderFont = null;
            this.olvColumnTAC.Text = "TAC";
            this.olvColumnTAC.Width = 81;
            // 
            // olvColumnECI
            // 
            this.olvColumnECI.HeaderFont = null;
            this.olvColumnECI.Text = "ECI";
            this.olvColumnECI.Width = 69;
            // 
            // olvColumnSpeed
            // 
            this.olvColumnSpeed.HeaderFont = null;
            this.olvColumnSpeed.Text = "速率";
            this.olvColumnSpeed.Width = 68;
            // 
            // olvColumnLongitude
            // 
            this.olvColumnLongitude.HeaderFont = null;
            this.olvColumnLongitude.Text = "经度";
            this.olvColumnLongitude.Width = 85;
            // 
            // olvColumnLatitude
            // 
            this.olvColumnLatitude.HeaderFont = null;
            this.olvColumnLatitude.Text = "纬度";
            this.olvColumnLatitude.Width = 82;
            // 
            // olvColumnTime
            // 
            this.olvColumnTime.HeaderFont = null;
            this.olvColumnTime.Text = "时间";
            this.olvColumnTime.Width = 135;
            // 
            // olvColumnBand
            // 
            this.olvColumnBand.HeaderFont = null;
            this.olvColumnBand.Text = "占用频点";
            this.olvColumnBand.Width = 100;
            // 
            // olvColumnDistance
            // 
            this.olvColumnDistance.HeaderFont = null;
            this.olvColumnDistance.Text = "与小区距离";
            // 
            // olvColumnSINR
            // 
            this.olvColumnSINR.HeaderFont = null;
            this.olvColumnSINR.Text = "SINR";
            // 
            // olvColumnCode0Qam64Rate
            // 
            this.olvColumnCode0Qam64Rate.HeaderFont = null;
            this.olvColumnCode0Qam64Rate.Text = "下行码字0 64QAM占比(%)";
            // 
            // olvColumnCode1Qam64Rate
            // 
            this.olvColumnCode1Qam64Rate.HeaderFont = null;
            this.olvColumnCode1Qam64Rate.Text = "下行码字1 64QAM占比(%)";
            // 
            // olvColumnDoubleRankRate1
            // 
            this.olvColumnDoubleRankRate1.HeaderFont = null;
            this.olvColumnDoubleRankRate1.Text = "单流占比(%)";
            // 
            // olvColumnDoubleRankRate2
            // 
            this.olvColumnDoubleRankRate2.HeaderFont = null;
            this.olvColumnDoubleRankRate2.Text = "双流占比(%)";
            // 
            // olvColumnPDSCH_BLER_Avg
            // 
            this.olvColumnPDSCH_BLER_Avg.HeaderFont = null;
            this.olvColumnPDSCH_BLER_Avg.Text = "平均PDSCH BLER";
            // 
            // olvColumnPDCCH_DL_Grant_Count_Avg
            // 
            this.olvColumnPDCCH_DL_Grant_Count_Avg.HeaderFont = null;
            this.olvColumnPDCCH_DL_Grant_Count_Avg.Text = "PDCCH DL Grant Count";
            // 
            // olvColumnRsrp
            // 
            this.olvColumnRsrp.HeaderFont = null;
            this.olvColumnRsrp.Text = "RSRP";
            // 
            // contextMenuStripBadRxQualConverge
            // 
            this.contextMenuStripBadRxQualConverge.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.ToolStripMenuItemExport,
            this.ToolStripMenuItemExpand,
            this.ToolStripMenuItemFold});
            this.contextMenuStripBadRxQualConverge.Name = "contextMenuStripBadRxQualConverge";
            this.contextMenuStripBadRxQualConverge.Size = new System.Drawing.Size(149, 70);
            // 
            // ToolStripMenuItemExport
            // 
            this.ToolStripMenuItemExport.Name = "ToolStripMenuItemExport";
            this.ToolStripMenuItemExport.Size = new System.Drawing.Size(148, 22);
            this.ToolStripMenuItemExport.Text = "导出到Excel";
            this.ToolStripMenuItemExport.Click += new System.EventHandler(this.ToolStripMenuItemExport_Click);
            // 
            // ToolStripMenuItemExpand
            // 
            this.ToolStripMenuItemExpand.Name = "ToolStripMenuItemExpand";
            this.ToolStripMenuItemExpand.Size = new System.Drawing.Size(148, 22);
            this.ToolStripMenuItemExpand.Text = "展开所有节点";
            this.ToolStripMenuItemExpand.Click += new System.EventHandler(this.ToolStripMenuItemExpand_Click);
            // 
            // ToolStripMenuItemFold
            // 
            this.ToolStripMenuItemFold.Name = "ToolStripMenuItemFold";
            this.ToolStripMenuItemFold.Size = new System.Drawing.Size(148, 22);
            this.ToolStripMenuItemFold.Text = "折叠所有节点";
            this.ToolStripMenuItemFold.Click += new System.EventHandler(this.ToolStripMenuItemFold_Click);
            // 
            // xtraTabControl1
            // 
            this.xtraTabControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.xtraTabControl1.Location = new System.Drawing.Point(0, 0);
            this.xtraTabControl1.Name = "xtraTabControl1";
            this.xtraTabControl1.SelectedTabPage = this.xtraTabPage1;
            this.xtraTabControl1.Size = new System.Drawing.Size(881, 352);
            this.xtraTabControl1.TabIndex = 5;
            this.xtraTabControl1.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.xtraTabPage1});
            // 
            // xtraTabPage1
            // 
            this.xtraTabPage1.Controls.Add(this.listViewTotal);
            this.xtraTabPage1.Name = "xtraTabPage1";
            this.xtraTabPage1.Size = new System.Drawing.Size(874, 322);
            this.xtraTabPage1.Text = "汇聚列表";
            // 
            // ZTLowSpeedConvergeSampleForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(881, 352);
            this.Controls.Add(this.xtraTabControl1);
            this.Name = "ZTLowSpeedConvergeSampleForm";
            this.Text = "低速率点汇聚列表";
            ((System.ComponentModel.ISupportInitialize)(this.listViewTotal)).EndInit();
            this.contextMenuStripBadRxQualConverge.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).EndInit();
            this.xtraTabControl1.ResumeLayout(false);
            this.xtraTabPage1.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private BrightIdeasSoftware.TreeListView listViewTotal;
        private BrightIdeasSoftware.OLVColumn olvColumnSN;
        private BrightIdeasSoftware.OLVColumn olvColumnSampleCount;
        private BrightIdeasSoftware.OLVColumn olvColumnCellName;
        private BrightIdeasSoftware.OLVColumn olvColumnTAC;
        private BrightIdeasSoftware.OLVColumn olvColumnECI;
        private BrightIdeasSoftware.OLVColumn olvColumnSpeed;
        private BrightIdeasSoftware.OLVColumn olvColumnLongitude;
        private BrightIdeasSoftware.OLVColumn olvColumnLatitude;
        private BrightIdeasSoftware.OLVColumn olvColumnTime;
        private System.Windows.Forms.ContextMenuStrip contextMenuStripBadRxQualConverge;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemExport;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemExpand;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemFold;
        private BrightIdeasSoftware.OLVColumn olvColumnBand;
        private BrightIdeasSoftware.OLVColumn olvColumnDistance;
        private BrightIdeasSoftware.OLVColumn olvColumnRsrp;
        private DevExpress.XtraTab.XtraTabControl xtraTabControl1;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage1;
        private BrightIdeasSoftware.OLVColumn olvColumnSINR;
        private BrightIdeasSoftware.OLVColumn olvColumnCode0Qam64Rate;
        private BrightIdeasSoftware.OLVColumn olvColumnCode1Qam64Rate;
        private BrightIdeasSoftware.OLVColumn olvColumnDoubleRankRate1;
        private BrightIdeasSoftware.OLVColumn olvColumnDoubleRankRate2;
        private BrightIdeasSoftware.OLVColumn olvColumnPDSCH_BLER_Avg;
        private BrightIdeasSoftware.OLVColumn olvColumnPDCCH_DL_Grant_Count_Avg;
    }
}