﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class GSMWeakCovRoadSetForm : BaseForm
    {
        public GSMWeakCovRoadSetForm()
        {
            InitializeComponent();
        }

        public void GetSetCondition(out ZTWeakCovRoadCondition weakCovRoadCond)
        {
            weakCovRoadCond = new ZTWeakCovRoadCondition();

            weakCovRoadCond.maxRxlev = (int)numRxlevThreshold.Value;
            weakCovRoadCond.roadDistance = (int)numDistance.Value;
            weakCovRoadCond.sampleDistance = (int)numMaxDistance.Value;

            weakCovRoadCond.sampleCellDistance = (int)numSampleCellDistance.Value;
            weakCovRoadCond.sampleCellAngle = (int)numSampleCellAngle.Value;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }
    }
}