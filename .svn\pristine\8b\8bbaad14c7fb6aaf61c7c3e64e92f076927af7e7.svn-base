﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class LtePlanningRoadSettingForm : BaseDialog
    {
        public LtePlanningRoadSettingForm()
        {
            InitializeComponent();
            chkLastTime.Checked = false;
            chkMinLength.Checked = true;
            chkWeakCover.Checked = false;
            chkMultiCover.Checked = false;

            btnExcel.Click += BtnExcel_Click;
            btnCancel.Click += BtnCancel_Click;
            btnOK.Click += BtnOK_Click;
            btnReset.Click += BtnReset_Click;
            chkLastTime.CheckedChanged += ChkLastTime_CheckedChanged;
            chkMinLength.CheckedChanged += ChkMinLength_CheckedChanged;
            chkWeakCover.CheckedChanged += ChkWeakCover_CheckedChanged;
            chkMultiCover.CheckedChanged += ChkMultiCover_CheckedChanged;
            BtnReset_Click(btnReset, new EventArgs());
        }

        public LtePlanningRoadCondition GetCondition()
        {
            LtePlanningRoadCondition cond = new LtePlanningRoadCondition();
            cond.XlsFileName = txtExcel.Text;
            cond.RoadMinInterval = (int)numInterval.Value;
            cond.IsSavePoint = chkSavePoint.Checked;

            cond.IsRoadByTime = chkLastTime.Checked;
            cond.RoadLastTime = (int)numLastTime.Value;
            cond.IsRoadByLength = chkMinLength.Checked;
            cond.RoadMinLength = (int)numMinLength.Value;

            cond.WeakCoverageEnable = chkWeakCover.Checked;
            cond.WeakCoverageRadius = (int)numWeakCoverRadius.Value;
            cond.WeakCoverageRate = (double)numWeakCoverRate.Value / 100;
            cond.WeakCoverageRsrp = (double)numWeakCoverRsrp.Value;

            cond.MultiCoverageEnable = chkMultiCover.Checked;
            cond.MultiCoverageDiff = (int)numMultiCoverDiff.Value;
            cond.MultiCoverageRate = (double)numMultiCoverRate.Value / 100;
            cond.MultiCoverageRsrp = (double)numMultiCoverRsrp.Value;
            cond.MultiCoverageValue = (int)numMultiCoverValue.Value;

            return cond;
        }

        private void BtnReset_Click(object sender, EventArgs e)
        {
            LtePlanningRoadCondition cond = new LtePlanningRoadCondition();

            numInterval.Value = (decimal)cond.RoadMinInterval;
            chkSavePoint.Checked = cond.IsSavePoint;

            numLastTime.Value = (decimal)cond.RoadLastTime;
            chkLastTime.Checked = cond.IsRoadByTime;

            numMinLength.Value = (decimal)cond.RoadMinLength;
            chkMinLength.Checked = cond.IsRoadByLength;

            numWeakCoverRsrp.Value = (decimal)cond.WeakCoverageRsrp;
            numWeakCoverRate.Value = (decimal)cond.WeakCoverageRate * 100;
            numWeakCoverRadius.Value = (decimal)cond.WeakCoverageRadius;
            chkWeakCover.Checked = cond.WeakCoverageEnable;

            numMultiCoverRsrp.Value = (decimal)cond.MultiCoverageRsrp;
            numMultiCoverRate.Value = (decimal)cond.MultiCoverageRate * 100;
            numMultiCoverDiff.Value = (decimal)cond.MultiCoverageDiff;
            numMultiCoverValue.Value = (decimal)cond.MultiCoverageValue;
            chkMultiCover.Checked = cond.MultiCoverageEnable;
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
        }

        private void BtnOK_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(txtExcel.Text))
            {
                MessageBox.Show("请选择Excel文件", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                DialogResult = DialogResult.None;
                return;
            }

            DialogResult = DialogResult.OK;
        }

        private void BtnExcel_Click(object sender, EventArgs e)
        {
            OpenFileDialog dlg = new OpenFileDialog();
            dlg.Filter = FilterHelper.Excel;
            dlg.FilterIndex = 2;
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return;
            }
            txtExcel.Text = dlg.FileName;
        }

        private void ChkLastTime_CheckedChanged(object sender, EventArgs e)
        {
            if (!chkLastTime.Checked && !chkMinLength.Checked)
            {
                chkLastTime.Checked = true;
                return;
            }
            numLastTime.Enabled = chkLastTime.Checked;
        }

        private void ChkMinLength_CheckedChanged(object sender, EventArgs e)
        {
            if (!chkMinLength.Checked && !chkLastTime.Checked)
            {
                chkMinLength.Checked = true;
                return;
            }
            numMinLength.Enabled = chkMinLength.Checked;
        }

        private void ChkWeakCover_CheckedChanged(object sender, EventArgs e)
        {
            numWeakCoverRadius.Enabled = chkWeakCover.Checked;
            numWeakCoverRate.Enabled = chkWeakCover.Checked;
            numWeakCoverRsrp.Enabled = chkWeakCover.Checked;
        }

        private void ChkMultiCover_CheckedChanged(object sender, EventArgs e)
        {
            numMultiCoverDiff.Enabled = chkMultiCover.Checked;
            numMultiCoverRate.Enabled = chkMultiCover.Checked;
            numMultiCoverRsrp.Enabled = chkMultiCover.Checked;
            numMultiCoverValue.Enabled = chkMultiCover.Checked;
        }
    }
}
