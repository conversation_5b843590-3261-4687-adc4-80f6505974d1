﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.Util;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class TAAnalyseForm : MinCloseForm
    {
        List<TAIntervalInfo> taInfos;
        public TAAnalyseForm(MainModel mainModel)
            : base(mainModel)
        {
            InitializeComponent();
            DisposeWhenClose = true;
        }

        public void FillData(List<TAIntervalInfo> taInfos)
        {
            this.taInfos = taInfos;
            BindingSource source = new BindingSource();
            source.DataSource = taInfos;
            gridControl.DataSource = source;
            gridControl.RefreshDataSource();
        }

        private void miExportToExcel_Click(object sender, EventArgs e)
        {
            List<NPOIRow> rowList = new List<NPOIRow>();
            NPOIRow row = new NPOIRow();
            row.cellValues.Add("TA区间");
            row.cellValues.Add("采样点数");
            row.cellValues.Add("平均值");
            row.cellValues.Add("采样点与主服平均距离");
            row.cellValues.Add("异常采样点数");
            row.cellValues.Add("异常采样点比例(%)");
            rowList.Add(row);
            foreach (TAIntervalInfo taInfo in taInfos)
            {
                row = new NPOIRow();
                row.cellValues.Add(taInfo.Range);
                row.cellValues.Add(taInfo.SampleCount);
                row.cellValues.Add(taInfo.TAAvg);
                row.cellValues.Add(taInfo.DistanceAvg);
                row.cellValues.Add(taInfo.BadSampleCount);
                row.cellValues.Add(taInfo.BadSampleScale);
                rowList.Add(row);
            }
            ExcelNPOIManager.ExportToExcel(rowList);
        }

        private void gridControl_DoubleClick(object sender, EventArgs e)
        {
            if (gridView.SelectedRowsCount > 0)
            {
                TAIntervalInfo taInfo = gridView.GetRow(gridView.GetSelectedRows()[0]) as TAIntervalInfo;
                MainModel.ClearDTData();
                foreach (TestPoint tp in taInfo.BadTestPoints)
                {
                    MainModel.DTDataManager.Add(tp);
                }
                MainModel.FireDTDataChanged(this);
            }
        }
    }
}
