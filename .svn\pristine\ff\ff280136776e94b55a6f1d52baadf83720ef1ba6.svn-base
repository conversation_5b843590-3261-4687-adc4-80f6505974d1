﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using BrightIdeasSoftware;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc.AreaArchiveManage.CellCoverage
{
    public partial class CellCoverResultForm : MinCloseForm
    {
        public CellCoverResultForm()
            : base()
        {
            InitializeComponent();
            DisposeWhenClose = true;
            initFixCol();
            cbxCol.SelectedIndexChanged += cbxCol_SelectedIndexChanged;
        }

        void cbxCol_SelectedIndexChanged(object sender, EventArgs e)
        {
            refreshLegend();
            refreshGis(true, curCellItem, null);
        }

        private void refreshGis(bool isRenderChanged, CellCoverMainItem cellItem, CellCoverAreaItem areaItem)
        {
            bool refreshGrid = false;
            if (cellItem == null)
            {
                cellItem = cellCoverItems[0];
                refreshGrid = true;
                curCellItem = cellItem;
            }
            else if (cellItem != curCellItem)
            {
                refreshGrid = true;
                curCellItem = cellItem;
            }

            if (isRenderChanged || refreshGrid)
            {
                gridLayer.GridColorDic = null;
                gridLayer.Cell = curCellItem.Cell;
                TemplateColumn col = cbxCol.SelectedItem as TemplateColumn;
                if (col == null)
                {
                    return;
                }
                Dictionary<GridDataHub, Color> gridColorDic = new Dictionary<GridDataHub, Color>();
                foreach (GridDataHub grid in curCellItem.MatrixData)
                {
                    double value = grid.DataGroup.CalcFormula((CarrierType)col.CarrierID, col.MoMtFlag, col.Expression);
                    gridColorDic[grid] = col.GetBKColorByValue(value);
                }
                gridLayer.GridColorDic = gridColorDic;
            }

            List<AreaBase> areas = new List<AreaBase>(curCellItem.CoverAreaItems.Count);
            foreach (CellCoverAreaItem item in curCellItem.CoverAreaItems)
            {
                areas.Add(item.Area);
            }
            areaLayer.Areas = areas;
            List<AreaBase> selAreas = new List<AreaBase>();
            MasterCom.MTGis.DbRect bounds = new MTGis.DbRect(curCellItem.Cell.Longitude, curCellItem.Cell.Latitude
                , curCellItem.Cell.Longitude, curCellItem.Cell.Latitude);

            if (areaItem == null)
            {
                foreach (CellCoverAreaItem ai in curCellItem.CoverAreaItems)
                {
                    selAreas.Add(ai.Area);
                    bounds.MergeRects(ai.Area.Bounds);
                }
            }
            else
            {
                selAreas.Add(areaItem.Area);
                bounds.MergeRects(areaItem.Area.Bounds);
            }
            areaLayer.SelectedAreas = selAreas;

            MapForm mf = MainModel.MainForm.GetMapForm();
            mf.GoToView(bounds);
        }

        private void refreshLegend()
        {
            lbxLegend.Items.Clear();
            TemplateColumn tCol = cbxCol.SelectedItem as TemplateColumn;
            if (tCol != null)
            {
                lbxLegend.Items.Add("");
                if (tCol.IsDynamicBKColor)
                {
                    foreach (DTParameterRangeColor rng in tCol.DynamicBKColorRanges)
                    {
                        lbxLegend.Items.Add(rng);
                    }
                }
                else
                {
                    lbxLegend.Items.Add(tCol.StaticColor);
                   
                }
            }
            lbxLegend.Invalidate();
        }

        private void initFixCol()
        {
            lv.CanExpandGetter += delegate(object row)
            {
                return row is CellCoverMainItem;
            };
            lv.ChildrenGetter += delegate(object row)
            {
                if (row is CellCoverMainItem)
                {
                    CellCoverMainItem s = row as CellCoverMainItem;
                    return s.CoverAreaItems;
                }
                return null;
            };

            colSN.AspectGetter += delegate(object row)
            {
                if (cellCoverItems != null && row is CellCoverMainItem)
                {
                    CellCoverMainItem s = row as CellCoverMainItem;
                    return cellCoverItems.IndexOf(s) + 1;
                }
                return null;
            };

            colItemName.AspectGetter += delegate(object row)
            {
                if ( row is CellCoverMainItem)
                {
                    CellCoverMainItem s = row as CellCoverMainItem;
                    return s.Cell.Name;
                }
                else if (row is CellCoverAreaItem)
                {
                    CellCoverAreaItem s = row as CellCoverAreaItem;
                    return s.Area.Name;
                }
                return null;
            };

            colDistance.AspectGetter += delegate(object row)
            {
                if (row is CellCoverAreaItem)
                {
                    CellCoverAreaItem s = row as CellCoverAreaItem;
                    return s.CoverCellDistance;
                }
                return null;
            };

            colIsNearestCell.AspectGetter += delegate(object row)
            {
                if (row is CellCoverAreaItem)
                {
                    CellCoverAreaItem s = row as CellCoverAreaItem;
                    return s.IsNearestCellDesc;
                }
                return null;
            };
            colNearestDis.AspectGetter += delegate(object row)
            {
                if (row is CellCoverAreaItem)
                {
                    CellCoverAreaItem s = row as CellCoverAreaItem;
                    return s.NearestSiteDistance;
                }
                return null;
            };
            colNearestSite.AspectGetter += delegate(object row)
            {
                if (row is CellCoverAreaItem)
                {
                    CellCoverAreaItem s = row as CellCoverAreaItem;
                    return s.NearestSiteNames;
                }
                return null;
            };
        }

        CellCoverGridLayer gridLayer = null;
        ZTAreaArchiveLayer areaLayer = null;
        private void makeSureLayerVisible()
        {
            MapForm mf = MainModel.MainForm.GetMapForm();
            areaLayer = mf.GetLayerBase(typeof(ZTAreaArchiveLayer)) as ZTAreaArchiveLayer;
            gridLayer = mf.GetLayerBase(typeof(CellCoverGridLayer)) as CellCoverGridLayer;
        }


        private List<CellCoverMainItem> cellCoverItems = null;
        public void FillData(CellCoverRptTemplate template,List<CellCoverMainItem> items)
        {
            curCellItem = null;
            this.cellCoverItems = items;
            makeSureLayerVisible();
            lv.ClearObjects();
            addCol2ListView(template.Columns);
            lv.SetObjects(cellCoverItems);
            if (cbxCol.Items.Count > 0)
            {
                cbxCol.SelectedIndex = 0;
            }
        }

        List<OLVColumn> addtionalDisCols = new List<OLVColumn>();
        private void addCol2ListView(List<TemplateColumn> cols)
        {
            foreach (OLVColumn oldCol in addtionalDisCols)
            {
                lv.AllColumns.Remove(oldCol);
                lv.Columns.Remove(oldCol);
                oldCol.Dispose();
            }
            addtionalDisCols.Clear();
            cbxCol.Items.Clear();
            foreach (TemplateColumn colInfo in cols)
            {
                cbxCol.Items.Add(colInfo);
                createCol(colInfo, false);
                if (colInfo.IsCalcProportion)
                {
                    createCol(colInfo, true);
                }
            }
            lv.RebuildColumns();
        }

        private void createCol(TemplateColumn tCol,bool isProportion)
        {
            OLVColumn col = new OLVColumn();
            col.Text = isProportion ? tCol.CaptionProportion : tCol.Caption;
            col.Tag = isProportion ? tCol.ExpProportion : tCol.Expression;
            lv.AllColumns.Add(col);
            lv.Columns.AddRange(new ColumnHeader[] { col });
            addtionalDisCols.Add(col);
            col.AspectGetter += delegate(object row)
            {
                string exp = col.Tag as string;
                if (row is CellCoverMainItem)
                {
                    double d = (row as CellCoverMainItem)[exp];
                    if (double.IsNaN(d))
                    {
                        return "-";
                    }
                    else
                    {
                        return d;
                    }
                }
                else if (row is CellCoverAreaItem)
                {
                    double d = (row as CellCoverAreaItem)[exp];
                    if (double.IsNaN(d))
                    {
                        return "-";
                    }
                    else
                    {
                        return d;
                    }
                }
                return null;
            };
        }

        CellCoverMainItem curCellItem = null;
        private void lv_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            OlvListViewHitTestInfo info = lv.OlvHitTest(e.X, e.Y);
            CellCoverAreaItem areaItem = null;
            CellCoverMainItem cellItem = null;
            if (info.RowObject is CellCoverMainItem)
            {
                cellItem = info.RowObject as CellCoverMainItem;
            }
            else if (info.RowObject is CellCoverAreaItem)
            {
                areaItem = info.RowObject as CellCoverAreaItem;
                cellItem = areaItem.CellCoverItem;
            }
            makeSureLayerVisible();
            refreshGis(false, cellItem, areaItem);
        }

        private void miExport2Xls_Click(object sender, EventArgs e)
        {
            lv.ExpandAll();
            List<NPOIRow> rows = new List<NPOIRow>();
            NPOIRow title = new NPOIRow();
            rows.Add(title);
            title.AddCellValue("序号");
            title.AddCellValue("小区");
            title.AddCellValue("区域");
            title.AddCellValue("是否为最近小区");
            title.AddCellValue("区域与覆盖小区距离(m)");
            title.AddCellValue("区域与最近基站距离(m)");
            title.AddCellValue("区域最近基站");
            foreach (OLVColumn col in addtionalDisCols)
            {
                title.AddCellValue(col.Text);
            }
            for (int i = 0; i < cellCoverItems.Count; i++)
            {
                NPOIRow cellItemRow = new NPOIRow();
                rows.Add(cellItemRow);
                CellCoverMainItem cellItem = cellCoverItems[i];
                cellItemRow.AddCellValue(i + 1);
                cellItemRow.AddCellValue(cellItem.Cell.Name);

                NPOIRow summaryRow =new NPOIRow();
                cellItemRow.AddSubRow(summaryRow);

                summaryRow.AddCellValue("汇总");
                summaryRow.AddCellValue(null);
                summaryRow.AddCellValue(null);
                summaryRow.AddCellValue(null);
                summaryRow.AddCellValue(null);
        
                foreach (OLVColumn col in addtionalDisCols)
                {
                    summaryRow.AddCellValue(col.GetValue(cellItem));
                }
                foreach (CellCoverAreaItem areaItem in cellItem.CoverAreaItems)
                {
                    NPOIRow areaRow = new NPOIRow();
                    cellItemRow.AddSubRow(areaRow);
                    areaRow.AddCellValue(areaItem.Area.Name);
                    areaRow.AddCellValue(areaItem.IsNearestCellDesc);
                    areaRow.AddCellValue(areaItem.CoverCellDistance);
                    areaRow.AddCellValue(areaItem.NearestSiteDistance);
                    areaRow.AddCellValue(areaItem.NearestSiteNames);
                    foreach (OLVColumn col in addtionalDisCols)
                    {
                        areaRow.AddCellValue(col.GetValue(areaItem));
                    }
                }
            }
            ExcelNPOIManager.ExportToExcel(rows);
        }

        private void lbxLegend_DrawItem(object sender, DrawItemEventArgs e)
        {
            System.Windows.Forms.ListBox listBoxLegend = sender as System.Windows.Forms.ListBox;
            if (e.Index < 0)
            {
                return;
            }
            object item = listBoxLegend.Items[e.Index];
            string text = "";
            if (item is DTParameterRangeColor)
            {
                e.Graphics.FillRectangle(new SolidBrush((item as DTParameterRangeColor).Value), e.Bounds.X, e.Bounds.Y, 16, 16);
                text = ((DTParameterRange)item).RangeDescription + "  " + ((DTParameterRange)item).DesInfo;
            }
            else if (item is Color)
            {
                e.Graphics.FillRectangle(new SolidBrush((Color)item), e.Bounds.X, e.Bounds.Y, 16, 16);
            }
            else if (item is string)
            {
                text = item.ToString();
            }
            e.Graphics.DrawString(text, listBoxLegend.Font, Brushes.Black, e.Bounds.X + 20, e.Bounds.Y);
        }

    }
}
