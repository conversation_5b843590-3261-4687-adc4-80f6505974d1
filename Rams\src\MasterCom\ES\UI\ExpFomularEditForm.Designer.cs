﻿namespace MasterCom.ES.UI
{
    partial class ExpFomularEditForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.btnOK = new System.Windows.Forms.Button();
            this.btnCancel = new System.Windows.Forms.Button();
            this.rboxFormula = new System.Windows.Forms.RichTextBox();
            this.cbxDataSrc = new System.Windows.Forms.ComboBox();
            this.label1 = new System.Windows.Forms.Label();
            this.cbxMethod = new System.Windows.Forms.ComboBox();
            this.label2 = new System.Windows.Forms.Label();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.btnFunc6 = new System.Windows.Forms.Button();
            this.lbParamTxt6 = new System.Windows.Forms.Label();
            this.lbParamIdx6 = new System.Windows.Forms.Label();
            this.tbxParam6 = new System.Windows.Forms.TextBox();
            this.btnFunc5 = new System.Windows.Forms.Button();
            this.btnFunc4 = new System.Windows.Forms.Button();
            this.btnFunc3 = new System.Windows.Forms.Button();
            this.btnFunc2 = new System.Windows.Forms.Button();
            this.btnFunc1 = new System.Windows.Forms.Button();
            this.lbParamTxt5 = new System.Windows.Forms.Label();
            this.lbParamIdx5 = new System.Windows.Forms.Label();
            this.lbParamTxt4 = new System.Windows.Forms.Label();
            this.lbParamIdx4 = new System.Windows.Forms.Label();
            this.lbParamTxt3 = new System.Windows.Forms.Label();
            this.lbParamIdx3 = new System.Windows.Forms.Label();
            this.tbxParam5 = new System.Windows.Forms.TextBox();
            this.lbParamTxt2 = new System.Windows.Forms.Label();
            this.tbxParam4 = new System.Windows.Forms.TextBox();
            this.lbParamIdx2 = new System.Windows.Forms.Label();
            this.tbxParam3 = new System.Windows.Forms.TextBox();
            this.lbParamTxt1 = new System.Windows.Forms.Label();
            this.tbxParam2 = new System.Windows.Forms.TextBox();
            this.lbParamIdx1 = new System.Windows.Forms.Label();
            this.tbxParam1 = new System.Windows.Forms.TextBox();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.cbxVirtual = new System.Windows.Forms.ComboBox();
            this.lbFuncDesc = new System.Windows.Forms.Label();
            this.btnCheckExp = new System.Windows.Forms.Button();
            this.mainTab = new System.Windows.Forms.TabControl();
            this.tabPage1 = new System.Windows.Forms.TabPage();
            this.tbxFuncExp = new System.Windows.Forms.TextBox();
            this.btnInsertFuncExp = new System.Windows.Forms.Button();
            this.label3 = new System.Windows.Forms.Label();
            this.tabPage2 = new System.Windows.Forms.TabPage();
            this.label6 = new System.Windows.Forms.Label();
            this.label5 = new System.Windows.Forms.Label();
            this.treeViewResvValue = new System.Windows.Forms.TreeView();
            this.tbxResvExp = new System.Windows.Forms.TextBox();
            this.label4 = new System.Windows.Forms.Label();
            this.btnInputRsvExp = new System.Windows.Forms.Button();
            this.tabPage3 = new System.Windows.Forms.TabPage();
            this.cbxInProcModule = new System.Windows.Forms.ListBox();
            this.btnKuohao = new System.Windows.Forms.Button();
            this.btnClear = new System.Windows.Forms.Button();
            this.btnBackspace = new System.Windows.Forms.Button();
            this.btnChu = new System.Windows.Forms.Button();
            this.btnCheng = new System.Windows.Forms.Button();
            this.btnJian = new System.Windows.Forms.Button();
            this.btn0 = new System.Windows.Forms.Button();
            this.btn7 = new System.Windows.Forms.Button();
            this.btn4 = new System.Windows.Forms.Button();
            this.btn1 = new System.Windows.Forms.Button();
            this.btn9 = new System.Windows.Forms.Button();
            this.btn6 = new System.Windows.Forms.Button();
            this.btn8 = new System.Windows.Forms.Button();
            this.btnDot = new System.Windows.Forms.Button();
            this.btn3 = new System.Windows.Forms.Button();
            this.btn5 = new System.Windows.Forms.Button();
            this.btn2 = new System.Windows.Forms.Button();
            this.btnJia = new System.Windows.Forms.Button();
            this.btnEqual = new System.Windows.Forms.Button();
            this.btnNotEqual = new System.Windows.Forms.Button();
            this.btnBigThan = new System.Windows.Forms.Button();
            this.btnSmallThan = new System.Windows.Forms.Button();
            this.ctxInputFromMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miResvValue = new System.Windows.Forms.ToolStripMenuItem();
            this.miParamValue = new System.Windows.Forms.ToolStripMenuItem();
            this.miMsgId = new System.Windows.Forms.ToolStripMenuItem();
            this.miEventId = new System.Windows.Forms.ToolStripMenuItem();
            this.miCellParam = new System.Windows.Forms.ToolStripMenuItem();
            this.miMSCName = new System.Windows.Forms.ToolStripMenuItem();
            this.miBSCName = new System.Windows.Forms.ToolStripMenuItem();
            this.miCellType = new System.Windows.Forms.ToolStripMenuItem();
            this.miBandType = new System.Windows.Forms.ToolStripMenuItem();
            this.miCellDesc = new System.Windows.Forms.ToolStripMenuItem();
            this.miCellCode = new System.Windows.Forms.ToolStripMenuItem();
            this.miHop = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem1 = new System.Windows.Forms.ToolStripSeparator();
            this.miTCH = new System.Windows.Forms.ToolStripMenuItem();
            this.miLAC = new System.Windows.Forms.ToolStripMenuItem();
            this.miCI = new System.Windows.Forms.ToolStripMenuItem();
            this.miBCCH = new System.Windows.Forms.ToolStripMenuItem();
            this.miBSIC = new System.Windows.Forms.ToolStripMenuItem();
            this.btnOpIn = new System.Windows.Forms.Button();
            this.label7 = new System.Windows.Forms.Label();
            this.toolTip = new System.Windows.Forms.ToolTip(this.components);
            this.miOwnFunc = new System.Windows.Forms.ToolStripMenuItem();
            this.groupBox1.SuspendLayout();
            this.groupBox2.SuspendLayout();
            this.mainTab.SuspendLayout();
            this.tabPage1.SuspendLayout();
            this.tabPage2.SuspendLayout();
            this.tabPage3.SuspendLayout();
            this.ctxInputFromMenu.SuspendLayout();
            this.SuspendLayout();
            // 
            // btnOK
            // 
            this.btnOK.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnOK.Location = new System.Drawing.Point(605, 420);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 0;
            this.btnOK.Text = "确定";
            this.btnOK.UseVisualStyleBackColor = true;
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // btnCancel
            // 
            this.btnCancel.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnCancel.Location = new System.Drawing.Point(720, 420);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 0;
            this.btnCancel.Text = "取消";
            this.btnCancel.UseVisualStyleBackColor = true;
            this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
            // 
            // rboxFormula
            // 
            this.rboxFormula.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left)
                        | System.Windows.Forms.AnchorStyles.Right)));
            this.rboxFormula.Location = new System.Drawing.Point(12, 12);
            this.rboxFormula.Name = "rboxFormula";
            this.rboxFormula.Size = new System.Drawing.Size(759, 61);
            this.rboxFormula.TabIndex = 1;
            this.rboxFormula.Text = "";
            // 
            // cbxDataSrc
            // 
            this.cbxDataSrc.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cbxDataSrc.FormattingEnabled = true;
            this.cbxDataSrc.Location = new System.Drawing.Point(56, 20);
            this.cbxDataSrc.Name = "cbxDataSrc";
            this.cbxDataSrc.Size = new System.Drawing.Size(148, 20);
            this.cbxDataSrc.TabIndex = 2;
            this.cbxDataSrc.SelectedIndexChanged += new System.EventHandler(this.cbxDataSrc_SelectedIndexChanged);
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(6, 24);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(53, 12);
            this.label1.TabIndex = 3;
            this.label1.Text = "数据源：";
            // 
            // cbxMethod
            // 
            this.cbxMethod.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left)
                        | System.Windows.Forms.AnchorStyles.Right)));
            this.cbxMethod.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cbxMethod.FormattingEnabled = true;
            this.cbxMethod.Location = new System.Drawing.Point(336, 21);
            this.cbxMethod.Name = "cbxMethod";
            this.cbxMethod.Size = new System.Drawing.Size(182, 20);
            this.cbxMethod.TabIndex = 2;
            this.cbxMethod.SelectedIndexChanged += new System.EventHandler(this.cbxMethod_SelectedIndexChanged);
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(294, 25);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(41, 12);
            this.label2.TabIndex = 3;
            this.label2.Text = "方法：";
            // 
            // groupBox1
            // 
            this.groupBox1.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
                        | System.Windows.Forms.AnchorStyles.Left)
                        | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBox1.Controls.Add(this.btnFunc6);
            this.groupBox1.Controls.Add(this.lbParamTxt6);
            this.groupBox1.Controls.Add(this.lbParamIdx6);
            this.groupBox1.Controls.Add(this.tbxParam6);
            this.groupBox1.Controls.Add(this.btnFunc5);
            this.groupBox1.Controls.Add(this.btnFunc4);
            this.groupBox1.Controls.Add(this.btnFunc3);
            this.groupBox1.Controls.Add(this.btnFunc2);
            this.groupBox1.Controls.Add(this.btnFunc1);
            this.groupBox1.Controls.Add(this.lbParamTxt5);
            this.groupBox1.Controls.Add(this.lbParamIdx5);
            this.groupBox1.Controls.Add(this.lbParamTxt4);
            this.groupBox1.Controls.Add(this.lbParamIdx4);
            this.groupBox1.Controls.Add(this.lbParamTxt3);
            this.groupBox1.Controls.Add(this.lbParamIdx3);
            this.groupBox1.Controls.Add(this.tbxParam5);
            this.groupBox1.Controls.Add(this.lbParamTxt2);
            this.groupBox1.Controls.Add(this.tbxParam4);
            this.groupBox1.Controls.Add(this.lbParamIdx2);
            this.groupBox1.Controls.Add(this.tbxParam3);
            this.groupBox1.Controls.Add(this.lbParamTxt1);
            this.groupBox1.Controls.Add(this.tbxParam2);
            this.groupBox1.Controls.Add(this.lbParamIdx1);
            this.groupBox1.Controls.Add(this.tbxParam1);
            this.groupBox1.Location = new System.Drawing.Point(10, 116);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(603, 176);
            this.groupBox1.TabIndex = 4;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "输入参数";
            // 
            // btnFunc6
            // 
            this.btnFunc6.FlatStyle = System.Windows.Forms.FlatStyle.Popup;
            this.btnFunc6.Location = new System.Drawing.Point(220, 152);
            this.btnFunc6.Name = "btnFunc6";
            this.btnFunc6.Size = new System.Drawing.Size(18, 18);
            this.btnFunc6.TabIndex = 11;
            this.btnFunc6.Text = ">";
            this.btnFunc6.UseVisualStyleBackColor = true;
            this.btnFunc6.Click += new System.EventHandler(this.btnFunc6_Click);
            // 
            // lbParamTxt6
            // 
            this.lbParamTxt6.AutoSize = true;
            this.lbParamTxt6.Location = new System.Drawing.Point(241, 155);
            this.lbParamTxt6.Name = "lbParamTxt6";
            this.lbParamTxt6.Size = new System.Drawing.Size(59, 12);
            this.lbParamTxt6.TabIndex = 10;
            this.lbParamTxt6.Text = "参数6描述";
            // 
            // lbParamIdx6
            // 
            this.lbParamIdx6.AutoSize = true;
            this.lbParamIdx6.Location = new System.Drawing.Point(25, 155);
            this.lbParamIdx6.Name = "lbParamIdx6";
            this.lbParamIdx6.Size = new System.Drawing.Size(17, 12);
            this.lbParamIdx6.TabIndex = 9;
            this.lbParamIdx6.Text = "6.";
            // 
            // tbxParam6
            // 
            this.tbxParam6.Location = new System.Drawing.Point(48, 151);
            this.tbxParam6.Name = "tbxParam6";
            this.tbxParam6.Size = new System.Drawing.Size(166, 21);
            this.tbxParam6.TabIndex = 8;
            this.tbxParam6.TextChanged += new System.EventHandler(this.tbxParam6_TextChanged);
            // 
            // btnFunc5
            // 
            this.btnFunc5.FlatStyle = System.Windows.Forms.FlatStyle.Popup;
            this.btnFunc5.Location = new System.Drawing.Point(220, 125);
            this.btnFunc5.Name = "btnFunc5";
            this.btnFunc5.Size = new System.Drawing.Size(18, 18);
            this.btnFunc5.TabIndex = 3;
            this.btnFunc5.Text = ">";
            this.btnFunc5.UseVisualStyleBackColor = true;
            this.btnFunc5.Click += new System.EventHandler(this.btnFunc5_Click);
            // 
            // btnFunc4
            // 
            this.btnFunc4.FlatStyle = System.Windows.Forms.FlatStyle.Popup;
            this.btnFunc4.Location = new System.Drawing.Point(220, 98);
            this.btnFunc4.Name = "btnFunc4";
            this.btnFunc4.Size = new System.Drawing.Size(18, 18);
            this.btnFunc4.TabIndex = 3;
            this.btnFunc4.Text = ">";
            this.btnFunc4.UseVisualStyleBackColor = true;
            this.btnFunc4.Click += new System.EventHandler(this.btnFunc4_Click);
            // 
            // btnFunc3
            // 
            this.btnFunc3.FlatStyle = System.Windows.Forms.FlatStyle.Popup;
            this.btnFunc3.Location = new System.Drawing.Point(220, 72);
            this.btnFunc3.Name = "btnFunc3";
            this.btnFunc3.Size = new System.Drawing.Size(18, 18);
            this.btnFunc3.TabIndex = 3;
            this.btnFunc3.Text = ">";
            this.btnFunc3.UseVisualStyleBackColor = true;
            this.btnFunc3.Click += new System.EventHandler(this.btnFunc3_Click);
            // 
            // btnFunc2
            // 
            this.btnFunc2.FlatStyle = System.Windows.Forms.FlatStyle.Popup;
            this.btnFunc2.Location = new System.Drawing.Point(220, 45);
            this.btnFunc2.Name = "btnFunc2";
            this.btnFunc2.Size = new System.Drawing.Size(18, 18);
            this.btnFunc2.TabIndex = 3;
            this.btnFunc2.Text = ">";
            this.btnFunc2.UseVisualStyleBackColor = true;
            this.btnFunc2.Click += new System.EventHandler(this.btnFunc2_Click);
            // 
            // btnFunc1
            // 
            this.btnFunc1.FlatStyle = System.Windows.Forms.FlatStyle.Popup;
            this.btnFunc1.Location = new System.Drawing.Point(220, 21);
            this.btnFunc1.Name = "btnFunc1";
            this.btnFunc1.Size = new System.Drawing.Size(18, 18);
            this.btnFunc1.TabIndex = 3;
            this.btnFunc1.Text = ">";
            this.btnFunc1.UseVisualStyleBackColor = true;
            this.btnFunc1.Click += new System.EventHandler(this.btnFunc1_Click);
            // 
            // lbParamTxt5
            // 
            this.lbParamTxt5.AutoSize = true;
            this.lbParamTxt5.Location = new System.Drawing.Point(241, 128);
            this.lbParamTxt5.Name = "lbParamTxt5";
            this.lbParamTxt5.Size = new System.Drawing.Size(59, 12);
            this.lbParamTxt5.TabIndex = 2;
            this.lbParamTxt5.Text = "参数5描述";
            // 
            // lbParamIdx5
            // 
            this.lbParamIdx5.AutoSize = true;
            this.lbParamIdx5.Location = new System.Drawing.Point(25, 128);
            this.lbParamIdx5.Name = "lbParamIdx5";
            this.lbParamIdx5.Size = new System.Drawing.Size(17, 12);
            this.lbParamIdx5.TabIndex = 1;
            this.lbParamIdx5.Text = "5.";
            // 
            // lbParamTxt4
            // 
            this.lbParamTxt4.AutoSize = true;
            this.lbParamTxt4.Location = new System.Drawing.Point(241, 101);
            this.lbParamTxt4.Name = "lbParamTxt4";
            this.lbParamTxt4.Size = new System.Drawing.Size(59, 12);
            this.lbParamTxt4.TabIndex = 2;
            this.lbParamTxt4.Text = "参数4描述";
            // 
            // lbParamIdx4
            // 
            this.lbParamIdx4.AutoSize = true;
            this.lbParamIdx4.Location = new System.Drawing.Point(25, 101);
            this.lbParamIdx4.Name = "lbParamIdx4";
            this.lbParamIdx4.Size = new System.Drawing.Size(17, 12);
            this.lbParamIdx4.TabIndex = 1;
            this.lbParamIdx4.Text = "4.";
            // 
            // lbParamTxt3
            // 
            this.lbParamTxt3.AutoSize = true;
            this.lbParamTxt3.Location = new System.Drawing.Point(241, 74);
            this.lbParamTxt3.Name = "lbParamTxt3";
            this.lbParamTxt3.Size = new System.Drawing.Size(59, 12);
            this.lbParamTxt3.TabIndex = 2;
            this.lbParamTxt3.Text = "参数3描述";
            // 
            // lbParamIdx3
            // 
            this.lbParamIdx3.AutoSize = true;
            this.lbParamIdx3.Location = new System.Drawing.Point(25, 74);
            this.lbParamIdx3.Name = "lbParamIdx3";
            this.lbParamIdx3.Size = new System.Drawing.Size(17, 12);
            this.lbParamIdx3.TabIndex = 1;
            this.lbParamIdx3.Text = "3.";
            // 
            // tbxParam5
            // 
            this.tbxParam5.Location = new System.Drawing.Point(48, 124);
            this.tbxParam5.Name = "tbxParam5";
            this.tbxParam5.Size = new System.Drawing.Size(166, 21);
            this.tbxParam5.TabIndex = 0;
            this.tbxParam5.TextChanged += new System.EventHandler(this.tbxParam5_TextChanged);
            // 
            // lbParamTxt2
            // 
            this.lbParamTxt2.AutoSize = true;
            this.lbParamTxt2.Location = new System.Drawing.Point(241, 48);
            this.lbParamTxt2.Name = "lbParamTxt2";
            this.lbParamTxt2.Size = new System.Drawing.Size(59, 12);
            this.lbParamTxt2.TabIndex = 2;
            this.lbParamTxt2.Text = "参数2描述";
            // 
            // tbxParam4
            // 
            this.tbxParam4.Location = new System.Drawing.Point(48, 97);
            this.tbxParam4.Name = "tbxParam4";
            this.tbxParam4.Size = new System.Drawing.Size(166, 21);
            this.tbxParam4.TabIndex = 0;
            this.tbxParam4.TextChanged += new System.EventHandler(this.tbxParam4_TextChanged);
            // 
            // lbParamIdx2
            // 
            this.lbParamIdx2.AutoSize = true;
            this.lbParamIdx2.Location = new System.Drawing.Point(25, 48);
            this.lbParamIdx2.Name = "lbParamIdx2";
            this.lbParamIdx2.Size = new System.Drawing.Size(17, 12);
            this.lbParamIdx2.TabIndex = 1;
            this.lbParamIdx2.Text = "2.";
            // 
            // tbxParam3
            // 
            this.tbxParam3.Location = new System.Drawing.Point(48, 70);
            this.tbxParam3.Name = "tbxParam3";
            this.tbxParam3.Size = new System.Drawing.Size(166, 21);
            this.tbxParam3.TabIndex = 0;
            this.tbxParam3.TextChanged += new System.EventHandler(this.tbxParam3_TextChanged);
            // 
            // lbParamTxt1
            // 
            this.lbParamTxt1.AutoSize = true;
            this.lbParamTxt1.Location = new System.Drawing.Point(241, 23);
            this.lbParamTxt1.Name = "lbParamTxt1";
            this.lbParamTxt1.Size = new System.Drawing.Size(59, 12);
            this.lbParamTxt1.TabIndex = 2;
            this.lbParamTxt1.Text = "参数1描述";
            // 
            // tbxParam2
            // 
            this.tbxParam2.Location = new System.Drawing.Point(48, 44);
            this.tbxParam2.Name = "tbxParam2";
            this.tbxParam2.Size = new System.Drawing.Size(166, 21);
            this.tbxParam2.TabIndex = 0;
            this.tbxParam2.TextChanged += new System.EventHandler(this.tbxParam2_TextChanged);
            // 
            // lbParamIdx1
            // 
            this.lbParamIdx1.AutoSize = true;
            this.lbParamIdx1.Location = new System.Drawing.Point(25, 23);
            this.lbParamIdx1.Name = "lbParamIdx1";
            this.lbParamIdx1.Size = new System.Drawing.Size(17, 12);
            this.lbParamIdx1.TabIndex = 1;
            this.lbParamIdx1.Text = "1.";
            // 
            // tbxParam1
            // 
            this.tbxParam1.Location = new System.Drawing.Point(48, 19);
            this.tbxParam1.Name = "tbxParam1";
            this.tbxParam1.Size = new System.Drawing.Size(166, 21);
            this.tbxParam1.TabIndex = 0;
            this.tbxParam1.TextChanged += new System.EventHandler(this.tbxParam1_TextChanged);
            // 
            // groupBox2
            // 
            this.groupBox2.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left)
                        | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBox2.Controls.Add(this.cbxDataSrc);
            this.groupBox2.Controls.Add(this.label1);
            this.groupBox2.Controls.Add(this.label2);
            this.groupBox2.Controls.Add(this.cbxVirtual);
            this.groupBox2.Controls.Add(this.cbxMethod);
            this.groupBox2.Controls.Add(this.lbFuncDesc);
            this.groupBox2.Location = new System.Drawing.Point(10, 33);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(537, 77);
            this.groupBox2.TabIndex = 4;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "方法选择";
            // 
            // cbxVirtual
            // 
            this.cbxVirtual.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cbxVirtual.FormattingEnabled = true;
            this.cbxVirtual.Location = new System.Drawing.Point(210, 20);
            this.cbxVirtual.Name = "cbxVirtual";
            this.cbxVirtual.Size = new System.Drawing.Size(79, 20);
            this.cbxVirtual.TabIndex = 2;
            this.cbxVirtual.SelectedIndexChanged += new System.EventHandler(this.cbxVirtual_SelectedIndexChanged);
            // 
            // lbFuncDesc
            // 
            this.lbFuncDesc.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left)
                        | System.Windows.Forms.AnchorStyles.Right)));
            this.lbFuncDesc.Location = new System.Drawing.Point(13, 41);
            this.lbFuncDesc.Name = "lbFuncDesc";
            this.lbFuncDesc.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.lbFuncDesc.Size = new System.Drawing.Size(518, 34);
            this.lbFuncDesc.TabIndex = 2;
            // 
            // btnCheckExp
            // 
            this.btnCheckExp.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnCheckExp.Location = new System.Drawing.Point(777, 27);
            this.btnCheckExp.Name = "btnCheckExp";
            this.btnCheckExp.Size = new System.Drawing.Size(49, 33);
            this.btnCheckExp.TabIndex = 0;
            this.btnCheckExp.Text = "检测";
            this.btnCheckExp.UseVisualStyleBackColor = true;
            this.btnCheckExp.Click += new System.EventHandler(this.btnCheckExp_Click);
            // 
            // mainTab
            // 
            this.mainTab.Alignment = System.Windows.Forms.TabAlignment.Bottom;
            this.mainTab.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
                        | System.Windows.Forms.AnchorStyles.Left)
                        | System.Windows.Forms.AnchorStyles.Right)));
            this.mainTab.Controls.Add(this.tabPage1);
            this.mainTab.Controls.Add(this.tabPage2);
            this.mainTab.Controls.Add(this.tabPage3);
            this.mainTab.Location = new System.Drawing.Point(13, 84);
            this.mainTab.Name = "mainTab";
            this.mainTab.SelectedIndex = 0;
            this.mainTab.Size = new System.Drawing.Size(633, 322);
            this.mainTab.TabIndex = 6;
            // 
            // tabPage1
            // 
            this.tabPage1.Controls.Add(this.tbxFuncExp);
            this.tabPage1.Controls.Add(this.groupBox1);
            this.tabPage1.Controls.Add(this.groupBox2);
            this.tabPage1.Controls.Add(this.btnInsertFuncExp);
            this.tabPage1.Controls.Add(this.label3);
            this.tabPage1.Location = new System.Drawing.Point(4, 4);
            this.tabPage1.Name = "tabPage1";
            this.tabPage1.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage1.Size = new System.Drawing.Size(625, 297);
            this.tabPage1.TabIndex = 0;
            this.tabPage1.Text = "预定义函数操作";
            this.tabPage1.UseVisualStyleBackColor = true;
            // 
            // tbxFuncExp
            // 
            this.tbxFuncExp.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left)
                        | System.Windows.Forms.AnchorStyles.Right)));
            this.tbxFuncExp.Location = new System.Drawing.Point(67, 7);
            this.tbxFuncExp.Name = "tbxFuncExp";
            this.tbxFuncExp.ReadOnly = true;
            this.tbxFuncExp.Size = new System.Drawing.Size(480, 21);
            this.tbxFuncExp.TabIndex = 5;
            // 
            // btnInsertFuncExp
            // 
            this.btnInsertFuncExp.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnInsertFuncExp.Location = new System.Drawing.Point(552, 5);
            this.btnInsertFuncExp.Name = "btnInsertFuncExp";
            this.btnInsertFuncExp.Size = new System.Drawing.Size(61, 25);
            this.btnInsertFuncExp.TabIndex = 0;
            this.btnInsertFuncExp.Text = "输入↑";
            this.btnInsertFuncExp.UseVisualStyleBackColor = true;
            this.btnInsertFuncExp.Click += new System.EventHandler(this.btnInsertFuncExp_Click);
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(8, 13);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(41, 12);
            this.label3.TabIndex = 3;
            this.label3.Text = "公式：";
            // 
            // tabPage2
            // 
            this.tabPage2.Controls.Add(this.label6);
            this.tabPage2.Controls.Add(this.label5);
            this.tabPage2.Controls.Add(this.treeViewResvValue);
            this.tabPage2.Controls.Add(this.tbxResvExp);
            this.tabPage2.Controls.Add(this.label4);
            this.tabPage2.Controls.Add(this.btnInputRsvExp);
            this.tabPage2.Location = new System.Drawing.Point(4, 4);
            this.tabPage2.Name = "tabPage2";
            this.tabPage2.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage2.Size = new System.Drawing.Size(625, 297);
            this.tabPage2.TabIndex = 1;
            this.tabPage2.Text = "预存值操作";
            this.tabPage2.UseVisualStyleBackColor = true;
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(380, 79);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(233, 12);
            this.label6.TabIndex = 39;
            this.label6.Text = "（请双击左侧树，选择需要获取的预存值）";
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(8, 50);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(53, 12);
            this.label5.TabIndex = 39;
            this.label5.Text = "预存值：";
            // 
            // treeViewResvValue
            // 
            this.treeViewResvValue.Location = new System.Drawing.Point(67, 45);
            this.treeViewResvValue.Name = "treeViewResvValue";
            this.treeViewResvValue.Size = new System.Drawing.Size(296, 207);
            this.treeViewResvValue.TabIndex = 38;
            this.treeViewResvValue.NodeMouseDoubleClick += new System.Windows.Forms.TreeNodeMouseClickEventHandler(this.treeViewResvValue_NodeMouseDoubleClick);
            // 
            // tbxResvExp
            // 
            this.tbxResvExp.Location = new System.Drawing.Point(67, 7);
            this.tbxResvExp.Name = "tbxResvExp";
            this.tbxResvExp.ReadOnly = true;
            this.tbxResvExp.Size = new System.Drawing.Size(480, 21);
            this.tbxResvExp.TabIndex = 37;
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(8, 13);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(41, 12);
            this.label4.TabIndex = 36;
            this.label4.Text = "公式：";
            // 
            // btnInputRsvExp
            // 
            this.btnInputRsvExp.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnInputRsvExp.Location = new System.Drawing.Point(552, 5);
            this.btnInputRsvExp.Name = "btnInputRsvExp";
            this.btnInputRsvExp.Size = new System.Drawing.Size(61, 25);
            this.btnInputRsvExp.TabIndex = 35;
            this.btnInputRsvExp.Text = "输入↑";
            this.btnInputRsvExp.UseVisualStyleBackColor = true;
            this.btnInputRsvExp.Click += new System.EventHandler(this.btnInputRsvExp_Click);
            // 
            // tabPage3
            // 
            this.tabPage3.Controls.Add(this.cbxInProcModule);
            this.tabPage3.Location = new System.Drawing.Point(4, 4);
            this.tabPage3.Name = "tabPage3";
            this.tabPage3.Size = new System.Drawing.Size(625, 297);
            this.tabPage3.TabIndex = 2;
            this.tabPage3.Text = "内部调用模块";
            this.tabPage3.UseVisualStyleBackColor = true;
            // 
            // cbxInProcModule
            // 
            this.cbxInProcModule.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
                        | System.Windows.Forms.AnchorStyles.Left)
                        | System.Windows.Forms.AnchorStyles.Right)));
            this.cbxInProcModule.FormattingEnabled = true;
            this.cbxInProcModule.ItemHeight = 12;
            this.cbxInProcModule.Location = new System.Drawing.Point(14, 19);
            this.cbxInProcModule.Name = "cbxInProcModule";
            this.cbxInProcModule.Size = new System.Drawing.Size(322, 232);
            this.cbxInProcModule.TabIndex = 1;
            this.cbxInProcModule.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.cbxInProcModule_MouseDoubleClick);
            // 
            // btnKuohao
            // 
            this.btnKuohao.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnKuohao.Location = new System.Drawing.Point(701, 220);
            this.btnKuohao.Name = "btnKuohao";
            this.btnKuohao.Size = new System.Drawing.Size(32, 23);
            this.btnKuohao.TabIndex = 34;
            this.btnKuohao.TabStop = false;
            this.btnKuohao.Text = "( )";
            this.btnKuohao.UseVisualStyleBackColor = true;
            this.btnKuohao.Click += new System.EventHandler(this.btnKuohao_Click);
            // 
            // btnClear
            // 
            this.btnClear.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnClear.Location = new System.Drawing.Point(663, 88);
            this.btnClear.Name = "btnClear";
            this.btnClear.Size = new System.Drawing.Size(70, 34);
            this.btnClear.TabIndex = 31;
            this.btnClear.TabStop = false;
            this.btnClear.Text = "清空";
            this.btnClear.UseVisualStyleBackColor = true;
            this.btnClear.Click += new System.EventHandler(this.btnClear_Click);
            // 
            // btnBackspace
            // 
            this.btnBackspace.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnBackspace.Location = new System.Drawing.Point(739, 94);
            this.btnBackspace.Name = "btnBackspace";
            this.btnBackspace.Size = new System.Drawing.Size(70, 23);
            this.btnBackspace.TabIndex = 33;
            this.btnBackspace.TabStop = false;
            this.btnBackspace.Text = "Backspace";
            this.btnBackspace.UseVisualStyleBackColor = true;
            this.btnBackspace.Click += new System.EventHandler(this.btnBackspace_Click);
            // 
            // btnChu
            // 
            this.btnChu.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnChu.Location = new System.Drawing.Point(777, 133);
            this.btnChu.Name = "btnChu";
            this.btnChu.Size = new System.Drawing.Size(32, 23);
            this.btnChu.TabIndex = 32;
            this.btnChu.TabStop = false;
            this.btnChu.Text = "/";
            this.btnChu.UseVisualStyleBackColor = true;
            this.btnChu.Click += new System.EventHandler(this.btnChu_Click);
            // 
            // btnCheng
            // 
            this.btnCheng.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnCheng.Location = new System.Drawing.Point(777, 162);
            this.btnCheng.Name = "btnCheng";
            this.btnCheng.Size = new System.Drawing.Size(32, 23);
            this.btnCheng.TabIndex = 30;
            this.btnCheng.TabStop = false;
            this.btnCheng.Text = "*";
            this.btnCheng.UseVisualStyleBackColor = true;
            this.btnCheng.Click += new System.EventHandler(this.btnCheng_Click);
            // 
            // btnJian
            // 
            this.btnJian.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnJian.Location = new System.Drawing.Point(777, 191);
            this.btnJian.Name = "btnJian";
            this.btnJian.Size = new System.Drawing.Size(32, 23);
            this.btnJian.TabIndex = 29;
            this.btnJian.TabStop = false;
            this.btnJian.Text = "-";
            this.btnJian.UseVisualStyleBackColor = true;
            this.btnJian.Click += new System.EventHandler(this.btnJian_Click);
            // 
            // btn0
            // 
            this.btn0.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btn0.Location = new System.Drawing.Point(663, 220);
            this.btn0.Name = "btn0";
            this.btn0.Size = new System.Drawing.Size(32, 23);
            this.btn0.TabIndex = 27;
            this.btn0.TabStop = false;
            this.btn0.Text = "0";
            this.btn0.UseVisualStyleBackColor = true;
            this.btn0.Click += new System.EventHandler(this.btn0_Click);
            // 
            // btn7
            // 
            this.btn7.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btn7.Location = new System.Drawing.Point(663, 133);
            this.btn7.Name = "btn7";
            this.btn7.Size = new System.Drawing.Size(32, 23);
            this.btn7.TabIndex = 28;
            this.btn7.TabStop = false;
            this.btn7.Text = "7";
            this.btn7.UseVisualStyleBackColor = true;
            this.btn7.Click += new System.EventHandler(this.btn7_Click);
            // 
            // btn4
            // 
            this.btn4.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btn4.Location = new System.Drawing.Point(663, 162);
            this.btn4.Name = "btn4";
            this.btn4.Size = new System.Drawing.Size(32, 23);
            this.btn4.TabIndex = 26;
            this.btn4.TabStop = false;
            this.btn4.Text = "4";
            this.btn4.UseVisualStyleBackColor = true;
            this.btn4.Click += new System.EventHandler(this.btn4_Click);
            // 
            // btn1
            // 
            this.btn1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btn1.Location = new System.Drawing.Point(663, 191);
            this.btn1.Name = "btn1";
            this.btn1.Size = new System.Drawing.Size(32, 23);
            this.btn1.TabIndex = 19;
            this.btn1.TabStop = false;
            this.btn1.Text = "1";
            this.btn1.UseVisualStyleBackColor = true;
            this.btn1.Click += new System.EventHandler(this.btn1_Click);
            // 
            // btn9
            // 
            this.btn9.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btn9.Location = new System.Drawing.Point(739, 133);
            this.btn9.Name = "btn9";
            this.btn9.Size = new System.Drawing.Size(32, 23);
            this.btn9.TabIndex = 20;
            this.btn9.TabStop = false;
            this.btn9.Text = "9";
            this.btn9.UseVisualStyleBackColor = true;
            this.btn9.Click += new System.EventHandler(this.btn9_Click);
            // 
            // btn6
            // 
            this.btn6.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btn6.Location = new System.Drawing.Point(739, 162);
            this.btn6.Name = "btn6";
            this.btn6.Size = new System.Drawing.Size(32, 23);
            this.btn6.TabIndex = 17;
            this.btn6.TabStop = false;
            this.btn6.Text = "6";
            this.btn6.UseVisualStyleBackColor = true;
            this.btn6.Click += new System.EventHandler(this.btn6_Click);
            // 
            // btn8
            // 
            this.btn8.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btn8.Location = new System.Drawing.Point(701, 133);
            this.btn8.Name = "btn8";
            this.btn8.Size = new System.Drawing.Size(32, 23);
            this.btn8.TabIndex = 18;
            this.btn8.TabStop = false;
            this.btn8.Text = "8";
            this.btn8.UseVisualStyleBackColor = true;
            this.btn8.Click += new System.EventHandler(this.btn8_Click);
            // 
            // btnDot
            // 
            this.btnDot.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnDot.Location = new System.Drawing.Point(739, 220);
            this.btnDot.Name = "btnDot";
            this.btnDot.Size = new System.Drawing.Size(32, 23);
            this.btnDot.TabIndex = 23;
            this.btnDot.TabStop = false;
            this.btnDot.Text = ".";
            this.btnDot.UseVisualStyleBackColor = true;
            this.btnDot.Click += new System.EventHandler(this.btnDot_Click);
            // 
            // btn3
            // 
            this.btn3.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btn3.Location = new System.Drawing.Point(739, 191);
            this.btn3.Name = "btn3";
            this.btn3.Size = new System.Drawing.Size(32, 23);
            this.btn3.TabIndex = 24;
            this.btn3.TabStop = false;
            this.btn3.Text = "3";
            this.btn3.UseVisualStyleBackColor = true;
            this.btn3.Click += new System.EventHandler(this.btn3_Click);
            // 
            // btn5
            // 
            this.btn5.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btn5.Location = new System.Drawing.Point(701, 162);
            this.btn5.Name = "btn5";
            this.btn5.Size = new System.Drawing.Size(32, 23);
            this.btn5.TabIndex = 21;
            this.btn5.TabStop = false;
            this.btn5.Text = "5";
            this.btn5.UseVisualStyleBackColor = true;
            this.btn5.Click += new System.EventHandler(this.btn5_Click);
            // 
            // btn2
            // 
            this.btn2.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btn2.Location = new System.Drawing.Point(701, 191);
            this.btn2.Name = "btn2";
            this.btn2.Size = new System.Drawing.Size(32, 23);
            this.btn2.TabIndex = 22;
            this.btn2.TabStop = false;
            this.btn2.Text = "2";
            this.btn2.UseVisualStyleBackColor = true;
            this.btn2.Click += new System.EventHandler(this.btn2_Click);
            // 
            // btnJia
            // 
            this.btnJia.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnJia.Location = new System.Drawing.Point(777, 220);
            this.btnJia.Name = "btnJia";
            this.btnJia.Size = new System.Drawing.Size(32, 23);
            this.btnJia.TabIndex = 25;
            this.btnJia.TabStop = false;
            this.btnJia.Text = "+";
            this.btnJia.UseVisualStyleBackColor = true;
            this.btnJia.Click += new System.EventHandler(this.btnJia_Click);
            // 
            // btnEqual
            // 
            this.btnEqual.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnEqual.Location = new System.Drawing.Point(777, 249);
            this.btnEqual.Name = "btnEqual";
            this.btnEqual.Size = new System.Drawing.Size(32, 23);
            this.btnEqual.TabIndex = 25;
            this.btnEqual.TabStop = false;
            this.btnEqual.Text = "=";
            this.btnEqual.UseVisualStyleBackColor = true;
            this.btnEqual.Click += new System.EventHandler(this.btnEqual_Click);
            // 
            // btnNotEqual
            // 
            this.btnNotEqual.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnNotEqual.Location = new System.Drawing.Point(739, 249);
            this.btnNotEqual.Name = "btnNotEqual";
            this.btnNotEqual.Size = new System.Drawing.Size(32, 23);
            this.btnNotEqual.TabIndex = 25;
            this.btnNotEqual.TabStop = false;
            this.btnNotEqual.Text = "#";
            this.btnNotEqual.UseVisualStyleBackColor = true;
            this.btnNotEqual.Click += new System.EventHandler(this.btnNotEqual_Click);
            // 
            // btnBigThan
            // 
            this.btnBigThan.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnBigThan.Location = new System.Drawing.Point(701, 249);
            this.btnBigThan.Name = "btnBigThan";
            this.btnBigThan.Size = new System.Drawing.Size(32, 23);
            this.btnBigThan.TabIndex = 25;
            this.btnBigThan.TabStop = false;
            this.btnBigThan.Text = ">";
            this.btnBigThan.UseVisualStyleBackColor = true;
            this.btnBigThan.Click += new System.EventHandler(this.btnBigThan_Click);
            // 
            // btnSmallThan
            // 
            this.btnSmallThan.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnSmallThan.Location = new System.Drawing.Point(663, 249);
            this.btnSmallThan.Name = "btnSmallThan";
            this.btnSmallThan.Size = new System.Drawing.Size(32, 23);
            this.btnSmallThan.TabIndex = 25;
            this.btnSmallThan.TabStop = false;
            this.btnSmallThan.Text = "<";
            this.btnSmallThan.UseVisualStyleBackColor = true;
            this.btnSmallThan.Click += new System.EventHandler(this.btnSmallThan_Click);
            // 
            // ctxInputFromMenu
            // 
            this.ctxInputFromMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miResvValue,
            this.miParamValue,
            this.miMsgId,
            this.miEventId,
            this.miCellParam,
            this.miOwnFunc});
            this.ctxInputFromMenu.Name = "ctxInputFromMenu";
            this.ctxInputFromMenu.Size = new System.Drawing.Size(153, 158);
            // 
            // miResvValue
            // 
            this.miResvValue.Name = "miResvValue";
            this.miResvValue.Size = new System.Drawing.Size(152, 22);
            this.miResvValue.Text = "预存值...";
            this.miResvValue.Click += new System.EventHandler(this.miResvValue_Click);
            // 
            // miParamValue
            // 
            this.miParamValue.Name = "miParamValue";
            this.miParamValue.Size = new System.Drawing.Size(152, 22);
            this.miParamValue.Text = "参数...";
            this.miParamValue.Click += new System.EventHandler(this.miParamValue_Click);
            // 
            // miMsgId
            // 
            this.miMsgId.Name = "miMsgId";
            this.miMsgId.Size = new System.Drawing.Size(152, 22);
            this.miMsgId.Text = "消息ID";
            this.miMsgId.Click += new System.EventHandler(this.miMsgId_Click);
            // 
            // miEventId
            // 
            this.miEventId.Name = "miEventId";
            this.miEventId.Size = new System.Drawing.Size(152, 22);
            this.miEventId.Text = "事件ID";
            this.miEventId.Click += new System.EventHandler(this.miEventId_Click);
            // 
            // miCellParam
            // 
            this.miCellParam.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miMSCName,
            this.miBSCName,
            this.miCellType,
            this.miBandType,
            this.miCellDesc,
            this.miCellCode,
            this.miHop,
            this.toolStripMenuItem1,
            this.miTCH,
            this.miLAC,
            this.miCI,
            this.miBCCH,
            this.miBSIC});
            this.miCellParam.Name = "miCellParam";
            this.miCellParam.Size = new System.Drawing.Size(152, 22);
            this.miCellParam.Text = "小区参数";
            // 
            // miMSCName
            // 
            this.miMSCName.Name = "miMSCName";
            this.miMSCName.Size = new System.Drawing.Size(118, 22);
            this.miMSCName.Text = "MSC名称";
            this.miMSCName.Click += new System.EventHandler(this.miMSCName_Click);
            // 
            // miBSCName
            // 
            this.miBSCName.Name = "miBSCName";
            this.miBSCName.Size = new System.Drawing.Size(118, 22);
            this.miBSCName.Text = "BSC名称";
            this.miBSCName.Click += new System.EventHandler(this.miBSCName_Click);
            // 
            // miCellType
            // 
            this.miCellType.Name = "miCellType";
            this.miCellType.Size = new System.Drawing.Size(118, 22);
            this.miCellType.Text = "小区类型";
            this.miCellType.Click += new System.EventHandler(this.miCellType_Click);
            // 
            // miBandType
            // 
            this.miBandType.Name = "miBandType";
            this.miBandType.Size = new System.Drawing.Size(118, 22);
            this.miBandType.Text = "BANDTYPE";
            this.miBandType.Click += new System.EventHandler(this.miBandType_Click);
            // 
            // miCellDesc
            // 
            this.miCellDesc.Name = "miCellDesc";
            this.miCellDesc.Size = new System.Drawing.Size(118, 22);
            this.miCellDesc.Text = "小区描述";
            this.miCellDesc.Click += new System.EventHandler(this.miCellDesc_Click);
            // 
            // miCellCode
            // 
            this.miCellCode.Name = "miCellCode";
            this.miCellCode.Size = new System.Drawing.Size(118, 22);
            this.miCellCode.Text = "小区编码";
            this.miCellCode.Click += new System.EventHandler(this.miCellCode_Click);
            // 
            // miHop
            // 
            this.miHop.Name = "miHop";
            this.miHop.Size = new System.Drawing.Size(118, 22);
            this.miHop.Text = "HOP";
            this.miHop.Click += new System.EventHandler(this.miHop_Click);
            // 
            // toolStripMenuItem1
            // 
            this.toolStripMenuItem1.Name = "toolStripMenuItem1";
            this.toolStripMenuItem1.Size = new System.Drawing.Size(115, 6);
            // 
            // miTCH
            // 
            this.miTCH.Name = "miTCH";
            this.miTCH.Size = new System.Drawing.Size(118, 22);
            this.miTCH.Text = "TCH";
            this.miTCH.Click += new System.EventHandler(this.miTCH_Click);
            // 
            // miLAC
            // 
            this.miLAC.Name = "miLAC";
            this.miLAC.Size = new System.Drawing.Size(118, 22);
            this.miLAC.Text = "LAC";
            this.miLAC.Click += new System.EventHandler(this.miLAC_Click);
            // 
            // miCI
            // 
            this.miCI.Name = "miCI";
            this.miCI.Size = new System.Drawing.Size(118, 22);
            this.miCI.Text = "CI";
            this.miCI.Click += new System.EventHandler(this.miCI_Click);
            // 
            // miBCCH
            // 
            this.miBCCH.Name = "miBCCH";
            this.miBCCH.Size = new System.Drawing.Size(118, 22);
            this.miBCCH.Text = "BCCH";
            this.miBCCH.Click += new System.EventHandler(this.miBCCH_Click);
            // 
            // miBSIC
            // 
            this.miBSIC.Name = "miBSIC";
            this.miBSIC.Size = new System.Drawing.Size(118, 22);
            this.miBSIC.Text = "BSIC";
            this.miBSIC.Click += new System.EventHandler(this.miBSIC_Click);
            // 
            // btnOpIn
            // 
            this.btnOpIn.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnOpIn.Location = new System.Drawing.Point(701, 294);
            this.btnOpIn.Name = "btnOpIn";
            this.btnOpIn.Size = new System.Drawing.Size(32, 23);
            this.btnOpIn.TabIndex = 25;
            this.btnOpIn.TabStop = false;
            this.btnOpIn.Text = "@";
            this.toolTip.SetToolTip(this.btnOpIn, "包含于 例如：  9@6,7,9,11 返回1，否则返回0");
            this.btnOpIn.UseVisualStyleBackColor = true;
            this.btnOpIn.Click += new System.EventHandler(this.btnOpIn_Click);
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(658, 299);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(41, 12);
            this.label7.TabIndex = 35;
            this.label7.Text = "其它：";
            // 
            // miOwnFunc
            // 
            this.miOwnFunc.Name = "miOwnFunc";
            this.miOwnFunc.Size = new System.Drawing.Size(152, 22);
            this.miOwnFunc.Text = "自定义函数...";
            this.miOwnFunc.Click += new System.EventHandler(this.miOwnFunc_Click);
            // 
            // ExpFomularEditForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(830, 455);
            this.Controls.Add(this.label7);
            this.Controls.Add(this.btnKuohao);
            this.Controls.Add(this.btnClear);
            this.Controls.Add(this.btnBackspace);
            this.Controls.Add(this.btnChu);
            this.Controls.Add(this.btnCheng);
            this.Controls.Add(this.btnJian);
            this.Controls.Add(this.btn0);
            this.Controls.Add(this.btn7);
            this.Controls.Add(this.btn4);
            this.Controls.Add(this.btn1);
            this.Controls.Add(this.btn9);
            this.Controls.Add(this.btn6);
            this.Controls.Add(this.btn8);
            this.Controls.Add(this.btnDot);
            this.Controls.Add(this.btn3);
            this.Controls.Add(this.btn5);
            this.Controls.Add(this.btn2);
            this.Controls.Add(this.btnSmallThan);
            this.Controls.Add(this.btnBigThan);
            this.Controls.Add(this.btnNotEqual);
            this.Controls.Add(this.btnOpIn);
            this.Controls.Add(this.btnEqual);
            this.Controls.Add(this.btnJia);
            this.Controls.Add(this.mainTab);
            this.Controls.Add(this.rboxFormula);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnCheckExp);
            this.Controls.Add(this.btnOK);
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "ExpFomularEditForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "判定节点条件编辑";
            this.Load += new System.EventHandler(this.ExpFomularEditForm_Load);
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            this.mainTab.ResumeLayout(false);
            this.tabPage1.ResumeLayout(false);
            this.tabPage1.PerformLayout();
            this.tabPage2.ResumeLayout(false);
            this.tabPage2.PerformLayout();
            this.tabPage3.ResumeLayout(false);
            this.ctxInputFromMenu.ResumeLayout(false);
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.Button btnOK;
        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.RichTextBox rboxFormula;
        private System.Windows.Forms.ComboBox cbxDataSrc;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.ComboBox cbxMethod;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.Label lbParamTxt5;
        private System.Windows.Forms.Label lbParamIdx5;
        private System.Windows.Forms.Label lbParamTxt4;
        private System.Windows.Forms.Label lbParamIdx4;
        private System.Windows.Forms.Label lbParamTxt3;
        private System.Windows.Forms.Label lbParamIdx3;
        private System.Windows.Forms.TextBox tbxParam5;
        private System.Windows.Forms.Label lbParamTxt2;
        private System.Windows.Forms.TextBox tbxParam4;
        private System.Windows.Forms.Label lbParamIdx2;
        private System.Windows.Forms.TextBox tbxParam3;
        private System.Windows.Forms.Label lbParamTxt1;
        private System.Windows.Forms.TextBox tbxParam2;
        private System.Windows.Forms.Label lbParamIdx1;
        private System.Windows.Forms.TextBox tbxParam1;
        private System.Windows.Forms.Button btnCheckExp;
        private System.Windows.Forms.TabControl mainTab;
        private System.Windows.Forms.TabPage tabPage1;
        private System.Windows.Forms.TextBox tbxFuncExp;
        private System.Windows.Forms.Button btnInsertFuncExp;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.TabPage tabPage2;
        private System.Windows.Forms.Button btnKuohao;
        private System.Windows.Forms.Button btnClear;
        private System.Windows.Forms.Button btnBackspace;
        private System.Windows.Forms.Button btnChu;
        private System.Windows.Forms.Button btnCheng;
        private System.Windows.Forms.Button btnJian;
        private System.Windows.Forms.Button btn0;
        private System.Windows.Forms.Button btn7;
        private System.Windows.Forms.Button btn4;
        private System.Windows.Forms.Button btn1;
        private System.Windows.Forms.Button btn9;
        private System.Windows.Forms.Button btn6;
        private System.Windows.Forms.Button btn8;
        private System.Windows.Forms.Button btnDot;
        private System.Windows.Forms.Button btn3;
        private System.Windows.Forms.Button btn5;
        private System.Windows.Forms.Button btn2;
        private System.Windows.Forms.Button btnJia;
        private System.Windows.Forms.Button btnEqual;
        private System.Windows.Forms.Button btnNotEqual;
        private System.Windows.Forms.Button btnBigThan;
        private System.Windows.Forms.Button btnSmallThan;
        private System.Windows.Forms.Button btnInputRsvExp;
        private System.Windows.Forms.TextBox tbxResvExp;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.TreeView treeViewResvValue;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.Label lbFuncDesc;
        private System.Windows.Forms.ContextMenuStrip ctxInputFromMenu;
        private System.Windows.Forms.ToolStripMenuItem miResvValue;
        private System.Windows.Forms.ToolStripMenuItem miParamValue;
        private System.Windows.Forms.ComboBox cbxVirtual;
        private System.Windows.Forms.Button btnFunc5;
        private System.Windows.Forms.Button btnFunc4;
        private System.Windows.Forms.Button btnFunc3;
        private System.Windows.Forms.Button btnFunc2;
        private System.Windows.Forms.Button btnFunc1;
        private System.Windows.Forms.ToolStripMenuItem miMsgId;
        private System.Windows.Forms.ToolStripMenuItem miEventId;
        private System.Windows.Forms.TabPage tabPage3;
        private System.Windows.Forms.ListBox cbxInProcModule;
        private System.Windows.Forms.Button btnFunc6;
        private System.Windows.Forms.Label lbParamTxt6;
        private System.Windows.Forms.Label lbParamIdx6;
        private System.Windows.Forms.TextBox tbxParam6;
        private System.Windows.Forms.ToolStripMenuItem miCellParam;
        private System.Windows.Forms.ToolStripMenuItem miMSCName;
        private System.Windows.Forms.ToolStripMenuItem miBSCName;
        private System.Windows.Forms.ToolStripMenuItem miCellType;
        private System.Windows.Forms.ToolStripMenuItem miBandType;
        private System.Windows.Forms.ToolStripMenuItem miCellDesc;
        private System.Windows.Forms.ToolStripMenuItem miCellCode;
        private System.Windows.Forms.ToolStripMenuItem miHop;
        private System.Windows.Forms.ToolStripSeparator toolStripMenuItem1;
        private System.Windows.Forms.ToolStripMenuItem miTCH;
        private System.Windows.Forms.ToolStripMenuItem miLAC;
        private System.Windows.Forms.ToolStripMenuItem miCI;
        private System.Windows.Forms.ToolStripMenuItem miBCCH;
        private System.Windows.Forms.ToolStripMenuItem miBSIC;
        private System.Windows.Forms.Button btnOpIn;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.ToolTip toolTip;
        private System.Windows.Forms.ToolStripMenuItem miOwnFunc;
    }
}