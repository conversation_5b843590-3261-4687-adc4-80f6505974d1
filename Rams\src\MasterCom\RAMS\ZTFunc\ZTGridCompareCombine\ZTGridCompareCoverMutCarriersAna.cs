﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Stat;
using MasterCom.RAMS.UserMng;
using MasterCom.MTGis;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Grid;
using System.Windows.Forms;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.KPI_Statistics;
using MasterCom.RAMS.ZTFunc;
using System.Drawing;


namespace MasterCom.RAMS.Net
{
    public class ZTGridCompareCoverMutCarriersAna : ZTGridCompareCombineBase
    {
        public ZTGridCompareCoverMutCarriersAna(MainModel mainModel)
            : base(mainModel)
        {
            this.mainModel = mainModel;
        }
        public override string Name
        {
            get { return "覆盖同时劣于联通电信路段"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22081, this.Name);
        }

        public GridMatrix<ColorUnit> ydCurGridColorUnitMatrix { get; set; }
        public GridMatrix<ColorUnit> ltCurGridColorUnitMatrix { get; set; }
        public GridMatrix<ColorUnit> dxCurGridColorUnitMatrix { get; set; }

        /// <summary>
        /// 设置查询指标
        /// </summary>
        /// <returns></returns>
        protected override bool getConditionBeforeQuery()
        {
            if (condition.CarrierTypes.Count < 3)
            {
                MessageBox.Show("运营商选择有误，需勾选全部运营商");
                return false;
            }

            this.evtIDSvrIDDic = new Dictionary<int, Dictionary<int, bool>>();
            this.imgCodeSvrIDDic = new Dictionary<string, Dictionary<int, bool>>();
            List<string> formulaSet = new List<string>();
            formulaSet.Add("Lte_61210309");
            formulaSet.Add("Lte_61210403");
            formulaSet.Add("Lte_61210301");
            formulaSet.Add("Lte_61210401");

            formulaSet.Add("Lf_612D0309");
            formulaSet.Add("Lf_612D0403");
            formulaSet.Add("Lf_612D0301");
            formulaSet.Add("Lf_612D0401");

            statImgIDSet = getTriadIDIgnoreServiceType(formulaSet);

            return true;
        }

        /// <summary>
        /// 重写三个运营商的数据获取
        /// </summary>
        /// <param name="o"></param>
        protected override void queryInThread(object o)
        {
            try
            {
                ClientProxy clientProxy = (ClientProxy)o;

                MainModel.DTDataManager.Clear();
                MainModel.SelectedTestPoints.Clear();
                MainModel.SelectedEvents.Clear();
                MainModel.SelectedMessage = null;
                MainModel.CurGridCoverData = null;
                MainModel.CurGridColorUnitMatrix = new GridMatrix<ColorUnit>();
                WaitBox.Text = strCityName + " 开始统计查询栅格数据...";
                WaitBox.CanCancel = true;
                MainModel.CurGridColorUnitMatrix = new GridMatrix<ColorUnit>();


                ydCurGridColorUnitMatrix = new GridMatrix<ColorUnit>();
                ltCurGridColorUnitMatrix = new GridMatrix<ColorUnit>();
                dxCurGridColorUnitMatrix = new GridMatrix<ColorUnit>();
                foreach (TimePeriod period in condition.Periods)
                {
                    int[] iCarrList = { 1, 2, 3 };
                    condition.CarrierTypes.Clear();
                    foreach (int iCarr in iCarrList)
                    {
                        condition.CarrierTypes.Add(iCarr);
                        if (iCarr == 1)
                        {
                            strCarrName = " 移动 ";
                            queryPeriodInfo(period, clientProxy, statImgIDSet, MainModel.CurGridColorUnitMatrix);
                            ydCurGridColorUnitMatrix = MainModel.CurGridColorUnitMatrix;
                        }
                        else if (iCarr == 2)
                        {
                            strCarrName = " 联通 ";
                            queryPeriodInfo(period, clientProxy, statImgIDSet, MainModel.CurGridColorUnitMatrix);
                            ltCurGridColorUnitMatrix = MainModel.CurGridColorUnitMatrix;
                        }
                        else if (iCarr == 3)
                        {
                            strCarrName = " 电信 ";
                            queryPeriodInfo(period, clientProxy, statImgIDSet, MainModel.CurGridColorUnitMatrix);
                            dxCurGridColorUnitMatrix = MainModel.CurGridColorUnitMatrix;
                        }
                        condition.CarrierTypes.Clear();
                        MainModel.CurGridColorUnitMatrix = new GridMatrix<ColorUnit>();
                    }
                    condition.CarrierTypes.AddRange(iCarrList);
                }
                MainModel.DTDataManager.Clear();
                MainModel.SelectedTestPoints.Clear();
                MainModel.SelectedEvents.Clear();
                MainModel.SelectedMessage = null;
                MainModel.CurGridCoverData = null;

                WaitBox.Text = strCityName + " 数据获取完毕，进行对比处理...";
                doCompare();
                WaitBox.Text = strCityName + " 对比完毕，进行栅格汇聚处理...";
                doCombine();
                gridFileNameListDic.Clear();

                if (MainModel.CurGridCoverData != null)
                {
                    MainModel.CurGridCoverData.Clear();
                }
            }
            catch (Exception e)
            {
                MessageBox.Show("Error" + e.StackTrace + e.ToString());
            }
            finally
            {
                WaitBox.Close();
            }
            MapGridLayer.NeedFreshFullImg = true;
        }
        /// <summary>
        /// 重写对比过程
        /// </summary>
        protected override void doCompare()
        {
            gridCountInfo = new GridCountInfo();
            List<CenterLongLat> gridCenterLongLat = new List<CenterLongLat>();
            addValidGrid(gridCenterLongLat, ydCurGridColorUnitMatrix, addYDGridCount);
            addValidGrid(gridCenterLongLat, ltCurGridColorUnitMatrix, addLTGridCount);
            addValidGrid(gridCenterLongLat, dxCurGridColorUnitMatrix, addDXGridCount);
            gridCountInfo.IAllGridCount = gridCenterLongLat.Count;
            gridCenterLongLat.Clear();
            foreach (ColorUnit cuYD in ydCurGridColorUnitMatrix)
            {
                GridTypeName gridName;
                int rAt, cAt;
                GridFormalValeInfo gridFormalValeInfo;
                bool isValid = getValidGridFormalValeInfo(cuYD, out gridName, out rAt, out cAt, out gridFormalValeInfo);
                if (isValid)
                {
                    GridColorUnit griCu = new GridColorUnit();
                    griCu.CuUnit = cuYD;
                    griCu.FHostRsrp = gridFormalValeInfo.fHostRsrp;
                    griCu.IHostRsrpSample = gridFormalValeInfo.iHostRsrpSample;
                    griCu.FHostSinr = gridFormalValeInfo.fHostSinr;
                    griCu.IHostSinrSample = gridFormalValeInfo.iHostSinrSample;

                    griCu.FGuestRsrp = gridFormalValeInfo.fGuestRsrp;
                    griCu.IGuestRsrpSample = gridFormalValeInfo.iGuestRsrpSample;
                    griCu.FGuestSinr = gridFormalValeInfo.fGuestSinr;
                    griCu.IGuestSinrSample = gridFormalValeInfo.iGuestSinrSample;

                    griCu.FGuestRsrp_dx = gridFormalValeInfo.fGuestRsrp_dx;
                    griCu.IGuestRsrpSample_dx = gridFormalValeInfo.iGuestRsrpSample_dx;
                    griCu.FGuestSinr_dx = gridFormalValeInfo.fGuestSinr_dx;
                    griCu.IGuestSinrSample_dx = gridFormalValeInfo.iGuestSinrSample_dx;

                    griCu.CuUnit.DataHub = null;//降低内存
                    if (!gridColorUnit.ContainsKey(gridName))
                    {
                        gridColorUnit[gridName] = new GridMatrix<GridColorUnit>();
                    }
                    gridColorUnit[gridName][rAt, cAt] = griCu;
                    cuYD.DataHub = null;
                }
            }
        }

        private bool getValidGridFormalValeInfo(ColorUnit cuYD, out GridTypeName gridName, out int rAt, out int cAt, out GridFormalValeInfo gridFormalValeInfo)
        {
            gridName = null; 
            rAt = 0; 
            cAt = 0; 
            gridFormalValeInfo = null;

            if (cuYD == null)
            {
                return false;
            }
            MasterCom.RAMS.Grid.GridUnitBase grid = new Grid.GridUnitBase(cuYD.CenterLng, cuYD.CenterLat);
            gridName = strContainDbRect(grid.Bounds);
            if (gridName.strGridType == "" || gridName.strGridName == "")
            {
                return false;
            }
            GridHelper.GetIndexOfDefaultSizeGrid(cuYD.CenterLng, cuYD.CenterLat, out rAt, out cAt);
            ColorUnit cuLT = ltCurGridColorUnitMatrix[rAt, cAt];
            ColorUnit cuDX = dxCurGridColorUnitMatrix[rAt, cAt];
            if (cuLT == null && cuDX == null)
            {
                return false;
            }
            StatDataLTE dataStatTDD = cuYD.DataHub.GetStatData(typeof(StatDataLTE)) as StatDataLTE;
            StatDataLTE_FDD dataStatFDD_LT = null;
            if (cuLT != null)
            {
                dataStatFDD_LT = cuLT.DataHub.GetStatData(typeof(StatDataLTE_FDD)) as StatDataLTE_FDD;
            }
            StatDataLTE_FDD dataStatFDD_DX = null;
            if (cuDX != null)
            {
                dataStatFDD_DX = cuDX.DataHub.GetStatData(typeof(StatDataLTE_FDD)) as StatDataLTE_FDD;
            }
            if (dataStatTDD == null || (dataStatFDD_LT == null && dataStatFDD_DX == null))
            {
                return false;
            }
            gridFormalValeInfo = initGridFormalValeInfo(cuYD, cuLT, cuDX, dataStatFDD_LT, dataStatFDD_DX);
            if (gridFormalValeInfo == null)
            {
                return false;
            }
            return true;
        }

        private GridFormalValeInfo initGridFormalValeInfo(ColorUnit cuYD, ColorUnit cuLT, ColorUnit cuDX, StatDataLTE_FDD dataStatFDD_LT, StatDataLTE_FDD dataStatFDD_DX)
        {
            GridFormalValeInfo gridFormalValeInfo = new GridFormalValeInfo();
            gridFormalValeInfo.fHostRsrp = (float)cuYD.DataHub.CalcValueByFormula("Lte_61210309");
            gridFormalValeInfo.fHostSinr = (float)cuYD.DataHub.CalcValueByFormula("Lte_61210403");
            gridFormalValeInfo.iHostRsrpSample = (int)cuYD.DataHub.CalcValueByFormula("Lte_61210301");
            gridFormalValeInfo.iHostSinrSample = (int)cuYD.DataHub.CalcValueByFormula("Lte_61210401");
            if (dataStatFDD_LT != null)
            {
                gridFormalValeInfo.fGuestRsrp = (float)cuLT.DataHub.CalcValueByFormula("Lf_612D0309");
                gridFormalValeInfo.fGuestSinr = (float)cuLT.DataHub.CalcValueByFormula("Lf_612D0403");
                gridFormalValeInfo.iGuestRsrpSample = (int)cuLT.DataHub.CalcValueByFormula("Lf_612D0301");
                gridFormalValeInfo.iGuestSinrSample = (int)cuLT.DataHub.CalcValueByFormula("Lf_612D0401");
            }
            if (dataStatFDD_DX != null)
            {
                gridFormalValeInfo.fGuestRsrp_dx = (float)cuDX.DataHub.CalcValueByFormula("Lf_612D0309");
                gridFormalValeInfo.fGuestSinr_dx = (float)cuDX.DataHub.CalcValueByFormula("Lf_612D0403");
                gridFormalValeInfo.iGuestRsrpSample_dx = (int)cuDX.DataHub.CalcValueByFormula("Lf_612D0301");
                gridFormalValeInfo.iGuestSinrSample_dx = (int)cuDX.DataHub.CalcValueByFormula("Lf_612D0401");
            }
            if ((gridFormalValeInfo.iHostRsrpSample > 0 && gridFormalValeInfo.iHostSinrSample > 0)
                && ((gridFormalValeInfo.iGuestRsrpSample > 0 && gridFormalValeInfo.iGuestSinrSample > 0)
                || (gridFormalValeInfo.iGuestRsrpSample_dx > 0 && gridFormalValeInfo.iGuestSinrSample_dx > 0)))
            {
                gridCountInfo.ICompareGridCount++;
            }
            if (gridFormalValeInfo.iHostRsrpSample <= 0 || gridFormalValeInfo.iHostSinrSample <= 0
                || gridFormalValeInfo.iGuestRsrpSample <= 0 || gridFormalValeInfo.iGuestSinrSample <= 0
                || gridFormalValeInfo.iGuestRsrpSample_dx <= 0 || gridFormalValeInfo.iGuestSinrSample_dx <= 0)
            {
                return null;
            }
            if ((gridFormalValeInfo.fHostRsrp >= -110 && gridFormalValeInfo.fHostSinr >= -3)
                || gridFormalValeInfo.fGuestRsrp < -110 || gridFormalValeInfo.fGuestSinr < -3
                || gridFormalValeInfo.fGuestRsrp_dx < -110 || gridFormalValeInfo.fGuestSinr_dx < -3)
            {
                return null;
            }

            return gridFormalValeInfo;
        }

        delegate void Func();

        private void addYDGridCount()
        {
            gridCountInfo.IHostGridCount++;
        }

        private void addLTGridCount()
        {
            gridCountInfo.IGuestGridCount++;
        }

        private void addDXGridCount()
        {
            gridCountInfo.IGuestDXGridCount++;
        }

        private void addValidGrid(List<CenterLongLat> gridCenterLongLat, GridMatrix<ColorUnit> gridColorUnitMatrix, Func func)
        {
            foreach (ColorUnit cu in gridColorUnitMatrix)
            {
                GridUnitBase grid = new GridUnitBase(cu.CenterLng, cu.CenterLat);
                GridTypeName gridName = strContainDbRect(grid.Bounds);
                if (gridName.strGridType != "" && gridName.strGridName != "")
                {
                    func();
                    CenterLongLat ccl = new CenterLongLat(cu.CenterLng, cu.CenterLat);
                    if (!gridCenterLongLat.Contains(ccl))
                    {
                        gridCenterLongLat.Add(ccl);
                    }
                }
            }
        }

        /// <summary>
        /// 重写转化过程
        /// </summary>
        protected override GridCompareCombineInfo doChangGridCompareResult(GridCompareCombineBlock block)
        {
            if (block.Grids.Count == 0)
            {
                return null;
            }
            GridCompareCombineInfo gridItem = new GridCompareCombineInfo();
            double sumLng = 0;
            double sumLat = 0;
            double sumHostRsrpMo = 0;
            int sumHostRsrpBase = 0;
            double sumHostSinrMo = 0;
            int sumHostSinrBase = 0;

            double sumGuestRsrpMo = 0;
            int sumGuestRsrpBase = 0;
            double sumGuestSinrMo = 0;
            int sumGuestSinrBase = 0;

            double sumGuestRsrpMo_dx = 0;
            int sumGuestRsrpBase_dx = 0;
            double sumGuestSinrMo_dx = 0;
            int sumGuestSinrBase_dx = 0;

            StringBuilder sbLng = new StringBuilder();
            StringBuilder sbLat = new StringBuilder();
            foreach (GridColorUnit gridCU in block.Grids)
            {
                sumLng += gridCU.CuUnit.CenterLng;
                sumLat += gridCU.CuUnit.CenterLat;
                sbLng.Append(gridCU.CuUnit.CenterLng + ";");
                sbLat.Append(gridCU.CuUnit.CenterLat + ";");

                sumHostRsrpMo += gridCU.FHostRsrp * gridCU.IHostRsrpSample;
                sumHostRsrpBase += gridCU.IHostRsrpSample;
                sumHostSinrMo += gridCU.FHostSinr * gridCU.IHostSinrSample;
                sumHostSinrBase += gridCU.IHostSinrSample;

                sumGuestRsrpMo += gridCU.FGuestRsrp * gridCU.IGuestRsrpSample;
                sumGuestRsrpBase += gridCU.IGuestRsrpSample;
                sumGuestSinrMo += gridCU.FGuestSinr * gridCU.IGuestSinrSample;
                sumGuestSinrBase += gridCU.IGuestSinrSample;

                sumGuestRsrpMo_dx += gridCU.FGuestRsrp * gridCU.IGuestRsrpSample_dx;
                sumGuestRsrpBase_dx += gridCU.IGuestRsrpSample_dx;
                sumGuestSinrMo_dx += gridCU.FGuestSinr * gridCU.IGuestSinrSample_dx;
                sumGuestSinrBase_dx += gridCU.IGuestSinrSample_dx;

                setGridStrFileName(gridItem, gridCU);
            }
            gridItem.StrLngList = sbLng.ToString();
            gridItem.StrLatList = sbLat.ToString();
            double dHostRsrpMeanVale = -999;
            double dHostSinrMeanVale = -999;

            double dGuestRsrpMeanVale = -999;
            double dGuestSinrMeanVale = -999;

            double dGuestRsrpMeanVale_dx = -999;
            double dGuestSinrMeanVale_dx = -999;

            if (sumHostRsrpBase != 0)
            {
                dHostRsrpMeanVale = sumHostRsrpMo / sumHostRsrpBase;
            }
            if (sumHostSinrBase != 0)
            {
                dHostSinrMeanVale = sumHostSinrMo / sumHostSinrBase;
            }

            if (sumGuestRsrpBase != 0)
            {
                dGuestRsrpMeanVale = sumGuestRsrpMo / sumGuestRsrpBase;
            }
            if (sumGuestSinrBase != 0)
            {
                dGuestSinrMeanVale = sumGuestSinrMo / sumGuestSinrBase;
            }

            if (sumGuestRsrpBase_dx != 0)
            {
                dGuestRsrpMeanVale_dx = sumGuestRsrpMo_dx / sumGuestRsrpBase_dx;
            }
            if (sumGuestSinrBase_dx != 0)
            {
                dGuestSinrMeanVale_dx = sumGuestSinrMo_dx / sumGuestSinrBase_dx;
            }

            gridItem.StrCompareInfo = "劣于联通,同时劣于电信";
            gridItem.StrProblemInfo = "移动RSRP：" + dHostRsrpMeanVale.ToString("0.00") + "dBm，移动SINR：" + dHostSinrMeanVale.ToString("0.00") 
                + "；联通RSRP："+ dGuestRsrpMeanVale.ToString("0.00") + "dBm，联通SINR：" + dGuestSinrMeanVale.ToString("0.00")
                + "；电信RSRP：" + dGuestRsrpMeanVale_dx.ToString("0.00") + "dBm，电信SINR：" + dGuestSinrMeanVale_dx.ToString("0.00") 
                + "，连续栅格个数：" + block.Grids.Count + "个";
            gridItem.DLng = sumLng / block.Grids.Count;
            gridItem.DLat = sumLat / block.Grids.Count;
            gridItem.StrProblemType = "覆盖问题";
            return gridItem;
        }

        private void setGridStrFileName(GridCompareCombineInfo gridItem, GridColorUnit gridCU)
        {
            CenterLongLat cll = new CenterLongLat(gridCU.CuUnit.CenterLng, gridCU.CuUnit.CenterLat);
            if (gridFileNameListDic.ContainsKey(cll))
            {
                gridItem.StrFileName = "";
                StringBuilder sb = new StringBuilder();
                foreach (string strFileName in gridFileNameListDic[cll])
                {
                    if (!gridItem.StrFileName.Contains(strFileName))
                    {
                        sb.Append(strFileName + ";");
                    }
                    gridItem.StrFileName = sb.ToString();
                }
            }
        }

        /// <summary>
        /// 显示结果集
        /// </summary>
        protected override void fireShowResult()
        {
            if (cityGridCompareInfoDic.Count == 0)
            {
                MessageBox.Show("没有结果");
                return;
            }
            GridCompareCombineMutForm showForm = null;
            object obj = MainModel.GetObjectFromBlackboard(typeof(GridCompareCombineMutForm).FullName);
            showForm = obj == null ? null : obj as GridCompareCombineMutForm;
            if (showForm == null || showForm.IsDisposed)
            {
                showForm = new GridCompareCombineMutForm(MainModel);
            }
            List<GridCompareCombineInfo> gridInfoList = new List<GridCompareCombineInfo>();
            foreach (string strCity in cityGridCompareInfoDic.Keys)
            {
                gridInfoList.AddRange(cityGridCompareInfoDic[strCity]);
            }
            cityGridCompareInfoDic.Clear();
            showForm.FillData(gridInfoList);
            showForm.Show(MainModel.MainForm);
        }
    }
}
