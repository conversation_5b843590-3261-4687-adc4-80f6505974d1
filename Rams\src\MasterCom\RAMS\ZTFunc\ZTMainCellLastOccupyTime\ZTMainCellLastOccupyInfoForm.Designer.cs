﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTMainCellLastOccupyInfoForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.treeViewCellOccupy = new BrightIdeasSoftware.TreeListView();
            this.olvColumnStatSN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnFileName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnEventName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnStatCellName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLACSrc = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCISrc = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLACTar = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCITar = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnDuration = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLongitude = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLatitude = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnTime = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnAvgRSRP = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnAvgSINR = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRSRPNum = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnSINRNum = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnTestPointNum = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.contextMenuStrip1 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.ToolStripMenuItemExpand = new System.Windows.Forms.ToolStripMenuItem();
            this.ToolStripMenuItemCollapse = new System.Windows.Forms.ToolStripMenuItem();
            this.ToolStripMenuItemExport = new System.Windows.Forms.ToolStripMenuItem();
            this.ToolStripMenuItemReplay = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStrip1 = new System.Windows.Forms.ToolStrip();
            this.toolStripButtonSection = new System.Windows.Forms.ToolStripButton();
            ((System.ComponentModel.ISupportInitialize)(this.treeViewCellOccupy)).BeginInit();
            this.contextMenuStrip1.SuspendLayout();
            this.toolStrip1.SuspendLayout();
            this.SuspendLayout();
            // 
            // treeViewCellOccupy
            // 
            this.treeViewCellOccupy.AllColumns.Add(this.olvColumnStatSN);
            this.treeViewCellOccupy.AllColumns.Add(this.olvColumnFileName);
            this.treeViewCellOccupy.AllColumns.Add(this.olvColumnEventName);
            this.treeViewCellOccupy.AllColumns.Add(this.olvColumnStatCellName);
            this.treeViewCellOccupy.AllColumns.Add(this.olvColumnLACSrc);
            this.treeViewCellOccupy.AllColumns.Add(this.olvColumnCISrc);
            this.treeViewCellOccupy.AllColumns.Add(this.olvColumnLACTar);
            this.treeViewCellOccupy.AllColumns.Add(this.olvColumnCITar);
            this.treeViewCellOccupy.AllColumns.Add(this.olvColumnDuration);
            this.treeViewCellOccupy.AllColumns.Add(this.olvColumnLongitude);
            this.treeViewCellOccupy.AllColumns.Add(this.olvColumnLatitude);
            this.treeViewCellOccupy.AllColumns.Add(this.olvColumnTime);
            this.treeViewCellOccupy.AllColumns.Add(this.olvColumnAvgRSRP);
            this.treeViewCellOccupy.AllColumns.Add(this.olvColumnAvgSINR);
            this.treeViewCellOccupy.AllColumns.Add(this.olvColumnRSRPNum);
            this.treeViewCellOccupy.AllColumns.Add(this.olvColumnSINRNum);
            this.treeViewCellOccupy.AllColumns.Add(this.olvColumnTestPointNum);
            this.treeViewCellOccupy.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.treeViewCellOccupy.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnStatSN,
            this.olvColumnFileName,
            this.olvColumnEventName,
            this.olvColumnStatCellName,
            this.olvColumnLACSrc,
            this.olvColumnCISrc,
            this.olvColumnLACTar,
            this.olvColumnCITar,
            this.olvColumnDuration,
            this.olvColumnLongitude,
            this.olvColumnLatitude,
            this.olvColumnTime,
            this.olvColumnAvgRSRP,
            this.olvColumnAvgSINR,
            this.olvColumnRSRPNum,
            this.olvColumnSINRNum,
            this.olvColumnTestPointNum});
            this.treeViewCellOccupy.ContextMenuStrip = this.contextMenuStrip1;
            this.treeViewCellOccupy.Cursor = System.Windows.Forms.Cursors.Default;
            this.treeViewCellOccupy.FullRowSelect = true;
            this.treeViewCellOccupy.GridLines = true;
            this.treeViewCellOccupy.HeaderWordWrap = true;
            this.treeViewCellOccupy.IsNeedShowOverlay = false;
            this.treeViewCellOccupy.Location = new System.Drawing.Point(0, 26);
            this.treeViewCellOccupy.Name = "treeViewCellOccupy";
            this.treeViewCellOccupy.OwnerDraw = true;
            this.treeViewCellOccupy.ShowGroups = false;
            this.treeViewCellOccupy.Size = new System.Drawing.Size(917, 373);
            this.treeViewCellOccupy.TabIndex = 8;
            this.treeViewCellOccupy.UseCompatibleStateImageBehavior = false;
            this.treeViewCellOccupy.View = System.Windows.Forms.View.Details;
            this.treeViewCellOccupy.VirtualMode = true;
            this.treeViewCellOccupy.DoubleClick += new System.EventHandler(this.treeViewCellOccupy_DoubleClick);
            // 
            // olvColumnStatSN
            // 
            this.olvColumnStatSN.AspectName = "";
            this.olvColumnStatSN.HeaderFont = null;
            this.olvColumnStatSN.Text = "序号";
            this.olvColumnStatSN.Width = 80;
            // 
            // olvColumnFileName
            // 
            this.olvColumnFileName.HeaderFont = null;
            this.olvColumnFileName.Text = "文件名称";
            this.olvColumnFileName.Width = 150;
            // 
            // olvColumnEventName
            // 
            this.olvColumnEventName.HeaderFont = null;
            this.olvColumnEventName.Text = "事件名称";
            this.olvColumnEventName.Width = 100;
            // 
            // olvColumnStatCellName
            // 
            this.olvColumnStatCellName.HeaderFont = null;
            this.olvColumnStatCellName.Text = "小区名称";
            this.olvColumnStatCellName.Width = 120;
            // 
            // olvColumnLACSrc
            // 
            this.olvColumnLACSrc.HeaderFont = null;
            this.olvColumnLACSrc.Text = "源LAC/TAC";
            this.olvColumnLACSrc.Width = 90;
            // 
            // olvColumnCISrc
            // 
            this.olvColumnCISrc.HeaderFont = null;
            this.olvColumnCISrc.Text = "源CI/ECI";
            this.olvColumnCISrc.Width = 90;
            // 
            // olvColumnLACTar
            // 
            this.olvColumnLACTar.HeaderFont = null;
            this.olvColumnLACTar.Text = "目的LAC/TAC";
            this.olvColumnLACTar.Width = 90;
            // 
            // olvColumnCITar
            // 
            this.olvColumnCITar.HeaderFont = null;
            this.olvColumnCITar.Text = "目的CI/ECI";
            this.olvColumnCITar.Width = 90;
            // 
            // olvColumnDuration
            // 
            this.olvColumnDuration.HeaderFont = null;
            this.olvColumnDuration.Text = "占用时长(秒)";
            this.olvColumnDuration.Width = 90;
            // 
            // olvColumnLongitude
            // 
            this.olvColumnLongitude.HeaderFont = null;
            this.olvColumnLongitude.Text = "经度";
            this.olvColumnLongitude.Width = 90;
            // 
            // olvColumnLatitude
            // 
            this.olvColumnLatitude.HeaderFont = null;
            this.olvColumnLatitude.Text = "纬度";
            this.olvColumnLatitude.Width = 90;
            // 
            // olvColumnTime
            // 
            this.olvColumnTime.HeaderFont = null;
            this.olvColumnTime.Text = "时间";
            this.olvColumnTime.Width = 120;
            // 
            // olvColumnAvgRSRP
            // 
            this.olvColumnAvgRSRP.HeaderFont = null;
            this.olvColumnAvgRSRP.Text = "平均电平";
            // 
            // olvColumnAvgSINR
            // 
            this.olvColumnAvgSINR.HeaderFont = null;
            this.olvColumnAvgSINR.Text = "平均质量";
            // 
            // olvColumnRSRPNum
            // 
            this.olvColumnRSRPNum.HeaderFont = null;
            this.olvColumnRSRPNum.Text = "低电平采样点数量";
            // 
            // olvColumnSINRNum
            // 
            this.olvColumnSINRNum.HeaderFont = null;
            this.olvColumnSINRNum.Text = "质差采样点数量";
            // 
            // olvColumnTestPointNum
            // 
            this.olvColumnTestPointNum.HeaderFont = null;
            this.olvColumnTestPointNum.Text = "总采样点个数";
            // 
            // contextMenuStrip1
            // 
            this.contextMenuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.ToolStripMenuItemExpand,
            this.ToolStripMenuItemCollapse,
            this.ToolStripMenuItemExport,
            this.ToolStripMenuItemReplay});
            this.contextMenuStrip1.Name = "contextMenuStrip1";
            this.contextMenuStrip1.Size = new System.Drawing.Size(137, 92);
            this.contextMenuStrip1.Opening += new System.ComponentModel.CancelEventHandler(this.contextMenuStrip1_Opening);
            // 
            // ToolStripMenuItemExpand
            // 
            this.ToolStripMenuItemExpand.Name = "ToolStripMenuItemExpand";
            this.ToolStripMenuItemExpand.Size = new System.Drawing.Size(136, 22);
            this.ToolStripMenuItemExpand.Text = "全部展开";
            this.ToolStripMenuItemExpand.Click += new System.EventHandler(this.ToolStripMenuItemExpand_Click);
            // 
            // ToolStripMenuItemCollapse
            // 
            this.ToolStripMenuItemCollapse.Name = "ToolStripMenuItemCollapse";
            this.ToolStripMenuItemCollapse.Size = new System.Drawing.Size(136, 22);
            this.ToolStripMenuItemCollapse.Text = "全部收缩";
            this.ToolStripMenuItemCollapse.Click += new System.EventHandler(this.ToolStripMenuItemCollapse_Click);
            // 
            // ToolStripMenuItemExport
            // 
            this.ToolStripMenuItemExport.Name = "ToolStripMenuItemExport";
            this.ToolStripMenuItemExport.Size = new System.Drawing.Size(136, 22);
            this.ToolStripMenuItemExport.Text = "导出到xls...";
            this.ToolStripMenuItemExport.Click += new System.EventHandler(this.ToolStripMenuItemExport_Click);
            // 
            // ToolStripMenuItemReplay
            // 
            this.ToolStripMenuItemReplay.Name = "ToolStripMenuItemReplay";
            this.ToolStripMenuItemReplay.Size = new System.Drawing.Size(136, 22);
            this.ToolStripMenuItemReplay.Text = "回放文件";
            this.ToolStripMenuItemReplay.Click += new System.EventHandler(this.ToolStripMenuItemReplay_Click);
            // 
            // toolStrip1
            // 
            this.toolStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.toolStripButtonSection});
            this.toolStrip1.Location = new System.Drawing.Point(0, 0);
            this.toolStrip1.Name = "toolStrip1";
            this.toolStrip1.Size = new System.Drawing.Size(919, 25);
            this.toolStrip1.TabIndex = 9;
            this.toolStrip1.Text = "toolStrip1";
            // 
            // toolStripButtonSection
            // 
            this.toolStripButtonSection.Image = global::MasterCom.RAMS.Properties.Resources.otheroptions;
            this.toolStripButtonSection.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.toolStripButtonSection.Name = "toolStripButtonSection";
            this.toolStripButtonSection.Size = new System.Drawing.Size(76, 22);
            this.toolStripButtonSection.Text = "分段区间";
            this.toolStripButtonSection.Click += new System.EventHandler(this.toolStripButtonSection_Click);
            // 
            // ZTMainCellLastOccupyInfoForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(919, 403);
            this.Controls.Add(this.toolStrip1);
            this.Controls.Add(this.treeViewCellOccupy);
            this.Name = "ZTMainCellLastOccupyInfoForm";
            this.Text = "主服小区持续占用";
            ((System.ComponentModel.ISupportInitialize)(this.treeViewCellOccupy)).EndInit();
            this.contextMenuStrip1.ResumeLayout(false);
            this.toolStrip1.ResumeLayout(false);
            this.toolStrip1.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private BrightIdeasSoftware.TreeListView treeViewCellOccupy;
        private BrightIdeasSoftware.OLVColumn olvColumnStatSN;
        private BrightIdeasSoftware.OLVColumn olvColumnStatCellName;
        private BrightIdeasSoftware.OLVColumn olvColumnFileName;
        private BrightIdeasSoftware.OLVColumn olvColumnLACSrc;
        private BrightIdeasSoftware.OLVColumn olvColumnCISrc;
        private BrightIdeasSoftware.OLVColumn olvColumnDuration;
        private System.Windows.Forms.ToolStrip toolStrip1;
        private System.Windows.Forms.ToolStripButton toolStripButtonSection;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip1;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemExpand;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemCollapse;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemExport;
        private BrightIdeasSoftware.OLVColumn olvColumnEventName;
        private BrightIdeasSoftware.OLVColumn olvColumnLACTar;
        private BrightIdeasSoftware.OLVColumn olvColumnCITar;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemReplay;
        private BrightIdeasSoftware.OLVColumn olvColumnLongitude;
        private BrightIdeasSoftware.OLVColumn olvColumnLatitude;
        private BrightIdeasSoftware.OLVColumn olvColumnTime;
        private BrightIdeasSoftware.OLVColumn olvColumnAvgRSRP;
        private BrightIdeasSoftware.OLVColumn olvColumnAvgSINR;
        private BrightIdeasSoftware.OLVColumn olvColumnRSRPNum;
        private BrightIdeasSoftware.OLVColumn olvColumnSINRNum;
        private BrightIdeasSoftware.OLVColumn olvColumnTestPointNum;
    }
}