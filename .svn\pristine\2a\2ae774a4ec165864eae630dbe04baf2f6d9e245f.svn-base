﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRWeakMosReasonManager : WeakMosReasonManagerBase
    {
        public NRWeakMosReasonManager(NRWeakMosReasonHelper helper)
        {
            this.helper = helper;
        }

        //NR解析器
        readonly NRWeakMosReasonHelper helper;

        //protected override float? getRsrp(TestPoint tp)
        //{
        //    return NRTpHelper.NrTpManager.GetSCellRsrp(tp);
        //}

        //protected override float? getSinr(TestPoint tp)
        //{
        //    return NRTpHelper.NrTpManager.GetSCellSinr(tp);
        //}

        protected override WeakMosReasonCallType judgeCallType(int? callBeginEvtID)
        {
            if (callBeginEvtID == null)
            {
                return WeakMosReasonCallType.Unknown;
            }
            return helper.JudgeCallType((int)callBeginEvtID);
        }

        protected override int? getRtpPacketsLostNum(TestPoint tp)
        {
            return helper.GetRtpPacketsLostNum(tp);
        }

        protected override bool judgeCsfb(int evtID)
        {
            return helper.JudgeCsfb(evtID);
        }

        protected override bool isWeakRsrpReason(TestPoint tp, WeakMosReasonAnaCondBase cond, ref int weakRsrpCount, WeakMosReasonCallType callType)
        {
            float? rsrp;
            if (callType == WeakMosReasonCallType.NR)
            {
                rsrp = NRTpHelper.NrTpManager.GetSCellRsrp(tp);
            }
            else if (callType == WeakMosReasonCallType.LTE)
            {
                rsrp = NRTpHelper.NrLteTpManager.GetSCellRsrp(tp);
            }
            else
            {
                return false;
            }

            bool isWeak = judgeWeakData(rsrp, -141, cond.WeakRsrpGate, cond.WeakRsrpTpCount, ref weakRsrpCount);
            return isWeak;
        }

        protected override bool isWeakSinrReason(TestPoint tp, WeakMosReasonAnaCondBase cond, ref int weakSinrCount, WeakMosReasonCallType callType)
        {
            float? sinr;
            if (callType == WeakMosReasonCallType.NR)
            {
                sinr = NRTpHelper.NrTpManager.GetSCellSinr(tp);
            }
            else if (callType == WeakMosReasonCallType.LTE)
            {
                sinr = NRTpHelper.NrLteTpManager.GetSCellSinr(tp);
            }
            else
            {
                return false;
            }

            bool isWeak = judgeWeakData(sinr, -50, cond.WeakSinrGate, cond.WeakSinrTpCount, ref weakSinrCount);
            return isWeak;
        }
    }
}
