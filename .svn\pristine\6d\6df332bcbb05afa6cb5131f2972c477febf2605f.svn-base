﻿namespace MasterCom.RAMS.Func.PopShow
{
    partial class CQTInfoPanel
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            DevExpress.XtraCharts.XYDiagram xyDiagram2 = new DevExpress.XtraCharts.XYDiagram();
            DevExpress.XtraCharts.Series series3 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel4 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.Series series4 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel5 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel6 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            this.panel1 = new System.Windows.Forms.Panel();
            this.label1 = new System.Windows.Forms.Label();
            this.splitContainer = new System.Windows.Forms.SplitContainer();
            this.gridControl = new DevExpress.XtraGrid.GridControl();
            this.gridView = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumnCQTType = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnProjectType = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnDateTime = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnAreaName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnTestCount = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnProblemCount = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnPassRate = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnTestDuration = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnTotalCount = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnTestRate = new DevExpress.XtraGrid.Columns.GridColumn();
            this.chartControl = new DevExpress.XtraCharts.ChartControl();
            this.cxtMsKPI_ng = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExp2Word = new System.Windows.Forms.ToolStripMenuItem();
            this.panel1.SuspendLayout();
            this.splitContainer.Panel1.SuspendLayout();
            this.splitContainer.Panel2.SuspendLayout();
            this.splitContainer.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControl)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel6)).BeginInit();
            this.cxtMsKPI_ng.SuspendLayout();
            this.SuspendLayout();
            // 
            // panel1
            // 
            this.panel1.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left)
                        | System.Windows.Forms.AnchorStyles.Right)));
            this.panel1.BackColor = System.Drawing.Color.Silver;
            this.panel1.Controls.Add(this.label1);
            this.panel1.Location = new System.Drawing.Point(3, 3);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(901, 30);
            this.panel1.TabIndex = 1;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.label1.ForeColor = System.Drawing.Color.White;
            this.label1.Location = new System.Drawing.Point(19, 9);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(91, 14);
            this.label1.TabIndex = 0;
            this.label1.Text = "CQT统计报表";
            // 
            // splitContainer
            // 
            this.splitContainer.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
                        | System.Windows.Forms.AnchorStyles.Left)
                        | System.Windows.Forms.AnchorStyles.Right)));
            this.splitContainer.Location = new System.Drawing.Point(3, 39);
            this.splitContainer.Name = "splitContainer";
            // 
            // splitContainer.Panel1
            // 
            this.splitContainer.Panel1.Controls.Add(this.gridControl);
            // 
            // splitContainer.Panel2
            // 
            this.splitContainer.Panel2.Controls.Add(this.chartControl);
            this.splitContainer.Size = new System.Drawing.Size(901, 411);
            this.splitContainer.SplitterDistance = 518;
            this.splitContainer.TabIndex = 2;
            // 
            // gridControl
            // 
            this.gridControl.ContextMenuStrip = this.cxtMsKPI_ng;
            this.gridControl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControl.Location = new System.Drawing.Point(0, 0);
            this.gridControl.MainView = this.gridView;
            this.gridControl.Name = "gridControl";
            this.gridControl.Size = new System.Drawing.Size(518, 411);
            this.gridControl.TabIndex = 0;
            this.gridControl.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView});
            // 
            // gridView
            // 
            this.gridView.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumnCQTType,
            this.gridColumnProjectType,
            this.gridColumnDateTime,
            this.gridColumnAreaName,
            this.gridColumnTestCount,
            this.gridColumnProblemCount,
            this.gridColumnPassRate,
            this.gridColumnTestDuration,
            this.gridColumnTotalCount,
            this.gridColumnTestRate});
            this.gridView.GridControl = this.gridControl;
            this.gridView.Name = "gridView";
            this.gridView.OptionsBehavior.Editable = false;
            this.gridView.OptionsBehavior.ReadOnly = true;
            this.gridView.RowCellClick += new DevExpress.XtraGrid.Views.Grid.RowCellClickEventHandler(this.gridView_RowCellClick);
            // 
            // gridColumnCQTType
            // 
            this.gridColumnCQTType.Caption = "CQT类型";
            this.gridColumnCQTType.FieldName = "CQTType";
            this.gridColumnCQTType.Name = "gridColumnCQTType";
            this.gridColumnCQTType.OptionsColumn.AllowSize = false;
            this.gridColumnCQTType.Visible = true;
            this.gridColumnCQTType.VisibleIndex = 0;
            this.gridColumnCQTType.Width = 68;
            // 
            // gridColumnProjectType
            // 
            this.gridColumnProjectType.Caption = "项目名称";
            this.gridColumnProjectType.FieldName = "ProjectType";
            this.gridColumnProjectType.Name = "gridColumnProjectType";
            this.gridColumnProjectType.OptionsColumn.AllowSize = false;
            this.gridColumnProjectType.Visible = true;
            this.gridColumnProjectType.VisibleIndex = 1;
            this.gridColumnProjectType.Width = 62;
            // 
            // gridColumnDateTime
            // 
            this.gridColumnDateTime.Caption = "时间";
            this.gridColumnDateTime.FieldName = "Period";
            this.gridColumnDateTime.Name = "gridColumnDateTime";
            this.gridColumnDateTime.OptionsColumn.AllowSize = false;
            this.gridColumnDateTime.Visible = true;
            this.gridColumnDateTime.VisibleIndex = 2;
            this.gridColumnDateTime.Width = 43;
            // 
            // gridColumnAreaName
            // 
            this.gridColumnAreaName.Caption = "区域名称";
            this.gridColumnAreaName.FieldName = "Area";
            this.gridColumnAreaName.Name = "gridColumnAreaName";
            this.gridColumnAreaName.OptionsColumn.AllowSize = false;
            this.gridColumnAreaName.Visible = true;
            this.gridColumnAreaName.VisibleIndex = 3;
            this.gridColumnAreaName.Width = 43;
            // 
            // gridColumnTestCount
            // 
            this.gridColumnTestCount.Caption = "测试点数";
            this.gridColumnTestCount.FieldName = "TestCount";
            this.gridColumnTestCount.Name = "gridColumnTestCount";
            this.gridColumnTestCount.OptionsColumn.AllowSize = false;
            this.gridColumnTestCount.Visible = true;
            this.gridColumnTestCount.VisibleIndex = 4;
            this.gridColumnTestCount.Width = 43;
            // 
            // gridColumnProblemCount
            // 
            this.gridColumnProblemCount.Caption = "问题点数";
            this.gridColumnProblemCount.FieldName = "ProblemCount";
            this.gridColumnProblemCount.Name = "gridColumnProblemCount";
            this.gridColumnProblemCount.OptionsColumn.AllowSize = false;
            this.gridColumnProblemCount.Visible = true;
            this.gridColumnProblemCount.VisibleIndex = 5;
            this.gridColumnProblemCount.Width = 43;
            // 
            // gridColumnPassRate
            // 
            this.gridColumnPassRate.Caption = "通过率(%)";
            this.gridColumnPassRate.FieldName = "PassRate";
            this.gridColumnPassRate.Name = "gridColumnPassRate";
            this.gridColumnPassRate.OptionsColumn.AllowSize = false;
            this.gridColumnPassRate.Visible = true;
            this.gridColumnPassRate.VisibleIndex = 6;
            this.gridColumnPassRate.Width = 43;
            // 
            // gridColumnTestDuration
            // 
            this.gridColumnTestDuration.Caption = "工作时长(分钟)";
            this.gridColumnTestDuration.FieldName = "TestDuration";
            this.gridColumnTestDuration.Name = "gridColumnTestDuration";
            this.gridColumnTestDuration.OptionsColumn.AllowSize = false;
            this.gridColumnTestDuration.Visible = true;
            this.gridColumnTestDuration.VisibleIndex = 7;
            this.gridColumnTestDuration.Width = 44;
            // 
            // gridColumnTotalCount
            // 
            this.gridColumnTotalCount.Caption = "CQT总点数";
            this.gridColumnTotalCount.FieldName = "TotalCount";
            this.gridColumnTotalCount.Name = "gridColumnTotalCount";
            this.gridColumnTotalCount.OptionsColumn.AllowSize = false;
            this.gridColumnTotalCount.Visible = true;
            this.gridColumnTotalCount.VisibleIndex = 8;
            this.gridColumnTotalCount.Width = 59;
            // 
            // gridColumnTestRate
            // 
            this.gridColumnTestRate.Caption = "完成率(%)";
            this.gridColumnTestRate.FieldName = "TestRate";
            this.gridColumnTestRate.Name = "gridColumnTestRate";
            this.gridColumnTestRate.OptionsColumn.AllowSize = false;
            this.gridColumnTestRate.Visible = true;
            this.gridColumnTestRate.VisibleIndex = 9;
            this.gridColumnTestRate.Width = 49;
            // 
            // chartControl
            // 
            xyDiagram2.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram2.AxisX.Range.SideMarginsEnabled = true;
            xyDiagram2.AxisX.VisibleInPanesSerializable = "-1";
            xyDiagram2.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram2.AxisY.Range.SideMarginsEnabled = true;
            xyDiagram2.AxisY.VisibleInPanesSerializable = "-1";
            this.chartControl.Diagram = xyDiagram2;
            this.chartControl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.chartControl.Location = new System.Drawing.Point(0, 0);
            this.chartControl.Name = "chartControl";
            sideBySideBarSeriesLabel4.LineVisible = true;
            series3.Label = sideBySideBarSeriesLabel4;
            series3.Name = "Series 1";
            sideBySideBarSeriesLabel5.LineVisible = true;
            series4.Label = sideBySideBarSeriesLabel5;
            series4.Name = "Series 2";
            this.chartControl.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series3,
        series4};
            sideBySideBarSeriesLabel6.LineVisible = true;
            this.chartControl.SeriesTemplate.Label = sideBySideBarSeriesLabel6;
            this.chartControl.Size = new System.Drawing.Size(379, 411);
            this.chartControl.TabIndex = 0;
            // 
            // cxtMsKPI_ng
            // 
            this.cxtMsKPI_ng.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExp2Word});
            this.cxtMsKPI_ng.Name = "cxtMsKPI_ng";
            this.cxtMsKPI_ng.Size = new System.Drawing.Size(175, 48);
            // 
            // miExp2Word
            // 
            this.miExp2Word.Name = "miExp2Word";
            this.miExp2Word.Size = new System.Drawing.Size(174, 22);
            this.miExp2Word.Text = "导出数据到Excel...";
            this.miExp2Word.Click += new System.EventHandler(this.miExp2Word_Click);
            // 
            // CQTInfoPanel
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.BackColor = System.Drawing.Color.Transparent;
            this.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.Controls.Add(this.splitContainer);
            this.Controls.Add(this.panel1);
            this.Name = "CQTInfoPanel";
            this.Size = new System.Drawing.Size(907, 453);
            this.panel1.ResumeLayout(false);
            this.panel1.PerformLayout();
            this.splitContainer.Panel1.ResumeLayout(false);
            this.splitContainer.Panel2.ResumeLayout(false);
            this.splitContainer.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControl)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel6)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControl)).EndInit();
            this.cxtMsKPI_ng.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.Panel panel1;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.SplitContainer splitContainer;
        private DevExpress.XtraGrid.GridControl gridControl;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnCQTType;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnDateTime;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnAreaName;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnTestCount;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnProblemCount;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnPassRate;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnTestDuration;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnTotalCount;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnTestRate;
        private DevExpress.XtraCharts.ChartControl chartControl;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnProjectType;
        private System.Windows.Forms.ContextMenuStrip cxtMsKPI_ng;
        private System.Windows.Forms.ToolStripMenuItem miExp2Word;
    }
}
