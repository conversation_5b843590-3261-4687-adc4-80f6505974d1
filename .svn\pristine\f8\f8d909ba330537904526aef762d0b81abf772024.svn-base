﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.KPI_Statistics;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using MasterCom.RAMS.ZTFunc;

namespace MasterCom.RAMS.Stat
{
    public class QueryDiyKpiByRegion : ReportStatQueryBase
    {
        public static int TitleBaseCount { get; set; } = 2;
        public Dictionary<string, StatInfoBase> FileKeyDataDic { get; set; }
        readonly TimePeriod period = null;
        public QueryDiyKpiByRegion(ReporterTemplate template, TimePeriod period)
            : base()
        {
            this.rptTemplate = template;
            this.IsShowResultForm = false;
            this.IsShowWaitBox = true;
            this.period = period;
            FileKeyDataDic = new Dictionary<string, StatInfoBase>();
        }
        protected override Model.Interface.StatTbToken getTableNameToken()
        {
            return Model.Interface.StatTbToken.grid;
        }
        protected override void AddGeographicFilter(Package package)
        {
            if (isQueringEvent)
            {
                AddDIYEndOpFlag(package);
                this.AddDIYRegion_Sample(package);
            }
            else
            {
                this.AddDIYRegion_Intersect(package);
            }
        }
        protected override void preparePackageCommand(Package package)
        {
            if (isQueringEvent)
            {
                package.Command = Command.DIYSearch;
                package.SubCommand = SubCommand.Request;
                package.Content.Type = RequestType.REQTYPE_DIY_AREA_EVENT;
                package.Content.PrepareAddParam();
            }
            else
            {
                package.Command = Command.DIYSearch;
                package.SubCommand = SubCommand.Request;
                package.Content.Type = RequestType.REQTYPE_DIY_AREA_COVER_GRID;
                package.Content.PrepareAddParam();
            }
        }

        protected override bool getConditionBeforeQuery()
        {
            return true;
        }
        protected override List<int> getNeedEventIdList()
        {
            List<int> evtIdList = new List<int>();
            if (evtIDSvrIDDic != null && evtIDSvrIDDic.Count > 0)
            {
                evtIdList = new List<int>(evtIDSvrIDDic.Keys);
            }
            if(condition.EventIDs!=null && condition.EventIDs.Count>0)
            {
                evtIdList.AddRange(condition.EventIDs);
            }
            return evtIdList;
        }
        protected override void afterRecieveAllData(params object[] reservedParams)
        {
            foreach (StatInfoBase item in FileKeyDataDic.Values)
            {
                item.KPIData.FinalMtMoGroup();
            }
        }
        protected override void recieveAndHandleSpecificStatData(Package package
            , List<StatImgDefItem> curImgColumnDef
            , KPIStatDataBase singleStatData)
        {
            double lng = package.Content.GetParamDouble();
            double lat = package.Content.GetParamDouble();
            if (!isValidPosition(lng, lat))
            {
                return;
            }

            fillStatData(package, curImgColumnDef, singleStatData);

            DTDataHeader fi = DTDataHeaderManager.GetInstance().GetHeaderByFileID(singleStatData.FileID);
            StatInfoBase tmp = new StatInfoBase();
            tmp.DistrictID = condition.DistrictID;
            tmp.FileHeader = fi;
            tmp.KPIData.AddStatData(fi, singleStatData, false);
            string flieKey = GetKeyUnionString();
            StatInfoBase fsi = null;
            if (this.FileKeyDataDic.TryGetValue(flieKey, out fsi))
            {
                fsi.KPIData.AddStatData(fi, singleStatData, false);
            }
            else
            {
                fsi = tmp;
                this.FileKeyDataDic[flieKey] = fsi;
            }
        }

        protected override void handleStatEvent(Event evt)
        {
            if (!isValidPosition(evt.Longitude, evt.Latitude))
            {
                return;
            }
            StatDataEvent singleStatData = new StatDataEvent(evt, needSeparateByServiceID(evt), needSeparateByFileName(evt));
            DTDataHeader fi = DTDataHeaderManager.GetInstance().GetHeaderByFileID(evt.FileID);
            StatInfoBase tmp = new StatInfoBase();
            tmp.DistrictID = condition.DistrictID;
            tmp.FileHeader = fi;
            tmp.KPIData.AddStatData(fi, singleStatData, false);
            string flieKey = GetKeyUnionString();
            StatInfoBase fsi = null;
            if (this.FileKeyDataDic.TryGetValue(flieKey, out fsi))
            {
                fsi.KPIData.AddStatData(fi, singleStatData, false);
            }
            else
            {
                fsi = tmp;
                this.FileKeyDataDic[flieKey] = fsi;
            }
        }

        protected string GetKeyUnionString()
        {
            return period.ToString();
        }
        protected override string GetKeyValue(string keyFieldRet, ReporterTemplate tpl, StatInfoBase statInfo)
        {
            string val = "";
            if (statInfo == null || statInfo.FileHeader == null)
            {
                val = "-";
            }
            else
            {
                val = GetKeyValueBase(keyFieldRet, tpl
                    , statInfo.FileHeader.AreaTypeID, statInfo.FileHeader.AreaID, statInfo.FileHeader.DistrictID, statInfo);
            }

            return val;
        }
        protected bool isValidPosition(double longitude, double latitude)
        {
            if (Condition.Geometorys != null && Condition.Geometorys.GeoOp.Contains(longitude, latitude))
            {
                return true;
            }
            return false;
        }

        public List<NPOIRow> CreateReportNPOI(List<MultiCompareRoadInfo> retdatas, bool isAnaMessageCellInfo)
        {
            List<NPOIRow> outputRows = new List<NPOIRow>();
            try
            {
                NPOIRow row = new NPOIRow();
                outputRows.Add(row);

                row.AddCellValue("轮次");
                row.AddCellValue("测试日期");
                if (isAnaMessageCellInfo)
                {
                    TitleBaseCount = 3;
                    row.AddCellValue("发现不符合参数数量");
                }
                else
                {
                    TitleBaseCount = 2;
                }
                //标题
                for (int i = 0; i < rptTemplate.Columns.Count; i++)
                {
                    ColumnSet column = rptTemplate.Columns[i];
                    row.AddCellValue(column.Title);
                }
                if (retdatas == null)
                    return outputRows;

                for (int r = 0; r < retdatas.Count; r++)
                {
                    MultiCompareRoadInfo cellInfo = retdatas[r];
                    row = new NPOIRow();
                    outputRows.Add(row);
                    row.cellValues = new List<object>(rptTemplate.Columns.Count + TitleBaseCount);
                    row.cellValues.Add(cellInfo.PeriodIndex);
                    row.cellValues.Add(cellInfo.Period.ToString());
                    if (isAnaMessageCellInfo)
                    {
                        row.cellValues.Add(cellInfo.UnfitParamCount);
                    }
                    for (int i = TitleBaseCount; i < rptTemplate.Columns.Count + TitleBaseCount; i++)
                    {
                        row.cellValues.Add(null);
                        row.cellValues[i] = getColumnSetValue(cellInfo.StatInfo, rptTemplate, i - TitleBaseCount);
                    }
                }
            }
            catch
            {
                //continue
            }
            return outputRows;
        }
    }
}
