﻿using MasterCom.RAMS.Model;
using System.Collections.Generic;
using MasterCom.RAMS.Func;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class LteURLAnaQueryByRegion : DIYAnalyseFilesOneByOneByRegion
    {
        public LteURLAnaQueryByRegion(MainModel mainModel)
            : base(mainModel)
        {
            this.FilterEventByRegion = false;
            this.IncludeMessage = true;
            this.IncludeTestPoint = false;

            analyzer = new LteURLAnalyzerByRegion();
        }
        protected List<DTFileDataManager> fileManagers = null;
        private readonly LteURLAnalyzerByRegion analyzer;
        protected Dictionary<string, List<ResvRegion>> resvRegionsDic = null;

        private static LteURLAnaQueryByRegion instance;
        public static LteURLAnaQueryByRegion GetInstance()
        {
            if (instance == null)
            {
                instance = new LteURLAnaQueryByRegion(MainModel.GetInstance());
            }
            return instance;
        }

        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager fileMng in MainModel.DTDataManager.FileDataManagers)
            {
                DTFileDataManager file = new DTFileDataManager(fileMng.FileID, fileMng.FileName, fileMng.ProjectType, fileMng.TestType,
                    fileMng.CarrierType, fileMng.LogTable, fileMng.SampleTableName, fileMng.ServiceType, fileMng.MoMtFlag);
                foreach (Event evt in fileMng.Events)
                {
                    if (analyzer.IsInHttpID(evt.ID)
                        || analyzer.IsInVideoID(evt.ID)
                        || analyzer.IsInDownloadID(evt.ID)
                        )
                    {
                        file.Add(evt);
                    }
                }
                foreach (MasterCom.RAMS.Model.Message msg in fileMng.Messages)
                {
                    if (msg.ID == (int)LteURLCheckMsg.HttpPageRequest
                        || msg.ID == (int)LteURLCheckMsg.HttpDownloadBegin
                        || msg.ID == (int)LteURLCheckMsg.VideoPlayRequest)
                    {
                        file.Add(msg);
                    }
                }
                fileManagers.Add(file);
            }
        }

        protected override void getResultsAfterQuery()
        {
            analyzer.Analyze(fileManagers);
        }

        protected override void fireShowForm()
        {
            LteURLAnaForm resultForm = MainModel.CreateResultForm(typeof(LteURLAnaForm)) as LteURLAnaForm;
            resultForm.FillData(analyzer.GetResult(), true);
            resultForm.Visible = true;
            resultForm.BringToFront();
        }

        protected override void getReadyBeforeQuery()
        {
            if (fileManagers == null)
            {
                fileManagers = new List<DTFileDataManager>();
            }
            else
            {
                fileManagers.Clear();
            }
           
        }

        protected override void clearDataBeforeAnalyseFiles()
        {
            resvRegionsDic = condition.Geometorys.SelectedResvRegionDic;
            if (resvRegionsDic != null && resvRegionsDic.Count > 0)
            {
                analyzer.SetResvRegion(resvRegionsDic);
            }
            else
            {
                List<ResvRegion> ress= new List<ResvRegion>();
                condition.Geometorys.RegionInfo.Shape = Condition.Geometorys.RegionInfo.Shape;
                ResvRegion resTemp = condition.Geometorys.RegionInfo;
                ress.Add(resTemp);
                resvRegionsDic = new Dictionary<string, List<ResvRegion>>();
                resvRegionsDic.Add("自选区域", ress);
                analyzer.SetResvRegion(resvRegionsDic);
            }
            
        }
        public override string Name
        {
            get { return "URL统计分析(按区域)"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22059, this.Name);
        }
        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.Region;
        }
        
    }

    public class LteURLAnaQueryByRegion_FDD : LteURLAnaQueryByRegion
    {
        private static LteURLAnaQueryByRegion_FDD instance = null;
        protected static readonly object lockObj = new object();
        public static new LteURLAnaQueryByRegion_FDD GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new LteURLAnaQueryByRegion_FDD(MainModel.GetInstance());
                    }
                }
            }
            return instance;
        }
        public LteURLAnaQueryByRegion_FDD(MainModel mainModel)
            : base(mainModel)
        {
            
        }
        public override string Name
        {
            get { return "URL统计分析LTE_FDD(按区域)"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 26000, 26052, this.Name);
        }
    }
}
