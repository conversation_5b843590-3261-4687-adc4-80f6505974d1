﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class LTEMIMOAntennaForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            DevExpress.XtraCharts.RadarDiagram radarDiagram7 = new DevExpress.XtraCharts.RadarDiagram();
            DevExpress.XtraCharts.Series series37 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.RadarPointSeriesLabel radarPointSeriesLabel19 = new DevExpress.XtraCharts.RadarPointSeriesLabel();
            DevExpress.XtraCharts.SeriesPoint seriesPoint31 = new DevExpress.XtraCharts.SeriesPoint("360");
            DevExpress.XtraCharts.SeriesPoint seriesPoint32 = new DevExpress.XtraCharts.SeriesPoint("350");
            DevExpress.XtraCharts.SeriesPoint seriesPoint33 = new DevExpress.XtraCharts.SeriesPoint("340");
            DevExpress.XtraCharts.SeriesPoint seriesPoint34 = new DevExpress.XtraCharts.SeriesPoint("330");
            DevExpress.XtraCharts.SeriesPoint seriesPoint35 = new DevExpress.XtraCharts.SeriesPoint("320");
            DevExpress.XtraCharts.RadarLineSeriesView radarLineSeriesView19 = new DevExpress.XtraCharts.RadarLineSeriesView();
            DevExpress.XtraCharts.Series series38 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.RadarPointSeriesLabel radarPointSeriesLabel20 = new DevExpress.XtraCharts.RadarPointSeriesLabel();
            DevExpress.XtraCharts.RadarLineSeriesView radarLineSeriesView20 = new DevExpress.XtraCharts.RadarLineSeriesView();
            DevExpress.XtraCharts.RadarPointSeriesLabel radarPointSeriesLabel21 = new DevExpress.XtraCharts.RadarPointSeriesLabel();
            DevExpress.XtraCharts.RadarLineSeriesView radarLineSeriesView21 = new DevExpress.XtraCharts.RadarLineSeriesView();
            DevExpress.XtraCharts.RadarDiagram radarDiagram8 = new DevExpress.XtraCharts.RadarDiagram();
            DevExpress.XtraCharts.Series series39 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.RadarPointSeriesLabel radarPointSeriesLabel22 = new DevExpress.XtraCharts.RadarPointSeriesLabel();
            DevExpress.XtraCharts.SeriesPoint seriesPoint36 = new DevExpress.XtraCharts.SeriesPoint("360");
            DevExpress.XtraCharts.SeriesPoint seriesPoint37 = new DevExpress.XtraCharts.SeriesPoint("350");
            DevExpress.XtraCharts.SeriesPoint seriesPoint38 = new DevExpress.XtraCharts.SeriesPoint("340");
            DevExpress.XtraCharts.SeriesPoint seriesPoint39 = new DevExpress.XtraCharts.SeriesPoint("330");
            DevExpress.XtraCharts.SeriesPoint seriesPoint40 = new DevExpress.XtraCharts.SeriesPoint("320");
            DevExpress.XtraCharts.RadarLineSeriesView radarLineSeriesView22 = new DevExpress.XtraCharts.RadarLineSeriesView();
            DevExpress.XtraCharts.Series series40 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.RadarPointSeriesLabel radarPointSeriesLabel23 = new DevExpress.XtraCharts.RadarPointSeriesLabel();
            DevExpress.XtraCharts.RadarLineSeriesView radarLineSeriesView23 = new DevExpress.XtraCharts.RadarLineSeriesView();
            DevExpress.XtraCharts.RadarPointSeriesLabel radarPointSeriesLabel24 = new DevExpress.XtraCharts.RadarPointSeriesLabel();
            DevExpress.XtraCharts.RadarLineSeriesView radarLineSeriesView24 = new DevExpress.XtraCharts.RadarLineSeriesView();
            DevExpress.XtraCharts.XYDiagram xyDiagram13 = new DevExpress.XtraCharts.XYDiagram();
            DevExpress.XtraCharts.SecondaryAxisY secondaryAxisY13 = new DevExpress.XtraCharts.SecondaryAxisY();
            DevExpress.XtraCharts.Series series41 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.PointSeriesLabel pointSeriesLabel37 = new DevExpress.XtraCharts.PointSeriesLabel();
            DevExpress.XtraCharts.SplineAreaSeriesView splineAreaSeriesView25 = new DevExpress.XtraCharts.SplineAreaSeriesView();
            DevExpress.XtraCharts.Series series42 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.PointSeriesLabel pointSeriesLabel38 = new DevExpress.XtraCharts.PointSeriesLabel();
            DevExpress.XtraCharts.SplineSeriesView splineSeriesView13 = new DevExpress.XtraCharts.SplineSeriesView();
            DevExpress.XtraCharts.PointSeriesLabel pointSeriesLabel39 = new DevExpress.XtraCharts.PointSeriesLabel();
            DevExpress.XtraCharts.SplineAreaSeriesView splineAreaSeriesView26 = new DevExpress.XtraCharts.SplineAreaSeriesView();
            DevExpress.XtraCharts.XYDiagram xyDiagram14 = new DevExpress.XtraCharts.XYDiagram();
            DevExpress.XtraCharts.SecondaryAxisY secondaryAxisY14 = new DevExpress.XtraCharts.SecondaryAxisY();
            DevExpress.XtraCharts.Series series43 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.PointSeriesLabel pointSeriesLabel40 = new DevExpress.XtraCharts.PointSeriesLabel();
            DevExpress.XtraCharts.SplineAreaSeriesView splineAreaSeriesView27 = new DevExpress.XtraCharts.SplineAreaSeriesView();
            DevExpress.XtraCharts.Series series44 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.PointSeriesLabel pointSeriesLabel41 = new DevExpress.XtraCharts.PointSeriesLabel();
            DevExpress.XtraCharts.SplineSeriesView splineSeriesView14 = new DevExpress.XtraCharts.SplineSeriesView();
            DevExpress.XtraCharts.PointSeriesLabel pointSeriesLabel42 = new DevExpress.XtraCharts.PointSeriesLabel();
            DevExpress.XtraCharts.SplineAreaSeriesView splineAreaSeriesView28 = new DevExpress.XtraCharts.SplineAreaSeriesView();
            DevExpress.XtraCharts.XYDiagram xyDiagram15 = new DevExpress.XtraCharts.XYDiagram();
            DevExpress.XtraCharts.SecondaryAxisY secondaryAxisY15 = new DevExpress.XtraCharts.SecondaryAxisY();
            DevExpress.XtraCharts.Series series45 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.PointSeriesLabel pointSeriesLabel43 = new DevExpress.XtraCharts.PointSeriesLabel();
            DevExpress.XtraCharts.SplineAreaSeriesView splineAreaSeriesView29 = new DevExpress.XtraCharts.SplineAreaSeriesView();
            DevExpress.XtraCharts.Series series46 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.PointSeriesLabel pointSeriesLabel44 = new DevExpress.XtraCharts.PointSeriesLabel();
            DevExpress.XtraCharts.SplineSeriesView splineSeriesView15 = new DevExpress.XtraCharts.SplineSeriesView();
            DevExpress.XtraCharts.PointSeriesLabel pointSeriesLabel45 = new DevExpress.XtraCharts.PointSeriesLabel();
            DevExpress.XtraCharts.SplineAreaSeriesView splineAreaSeriesView30 = new DevExpress.XtraCharts.SplineAreaSeriesView();
            DevExpress.XtraCharts.XYDiagram xyDiagram16 = new DevExpress.XtraCharts.XYDiagram();
            DevExpress.XtraCharts.SecondaryAxisY secondaryAxisY16 = new DevExpress.XtraCharts.SecondaryAxisY();
            DevExpress.XtraCharts.Series series47 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.PointSeriesLabel pointSeriesLabel46 = new DevExpress.XtraCharts.PointSeriesLabel();
            DevExpress.XtraCharts.SplineAreaSeriesView splineAreaSeriesView31 = new DevExpress.XtraCharts.SplineAreaSeriesView();
            DevExpress.XtraCharts.Series series48 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.PointSeriesLabel pointSeriesLabel47 = new DevExpress.XtraCharts.PointSeriesLabel();
            DevExpress.XtraCharts.SplineSeriesView splineSeriesView16 = new DevExpress.XtraCharts.SplineSeriesView();
            DevExpress.XtraCharts.PointSeriesLabel pointSeriesLabel48 = new DevExpress.XtraCharts.PointSeriesLabel();
            DevExpress.XtraCharts.SplineAreaSeriesView splineAreaSeriesView32 = new DevExpress.XtraCharts.SplineAreaSeriesView();
            this.xtraTalSheet = new DevExpress.XtraTab.XtraTabControl();
            this.xtraTabPageCell = new DevExpress.XtraTab.XtraTabPage();
            this.dataGridViewCell = new System.Windows.Forms.DataGridView();
            this.SN = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.city = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.grid = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.cellname = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.cellcgi = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.celltac = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.celleci = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.cellearfcn = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.cellpci = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.cellband = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.cellInOutDoor = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.cellDir = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.cellob = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.cellalt = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.cellsamplenum = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.celldist = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.reslut = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.warnstat = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.celldiff10Angle = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.celldiff5Angle = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.celldiff3Angle = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.maincelldifff9Angle = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.maincelldifff5Angle = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.maincelldiff3Angle = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.cellrsrpdiffmax = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.cellrsrpangelmax = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.cellmainrsrpdiffmax = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.cellrsrpanglemax = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.cellcoverrate = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.r0coverrate = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.r1coverrate = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.r0avg = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.r1avg = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.rsrpdiff = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.rsrpstability = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.s0avg = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.s1avg = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.sinrdiff = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.sinrstability = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.contextMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.StripMenuDrawPhoto = new System.Windows.Forms.ToolStripMenuItem();
            this.antCDF_PDF = new System.Windows.Forms.ToolStripMenuItem();
            this.StripMenuOutExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.StripMenuOutCSV = new System.Windows.Forms.ToolStripMenuItem();
            this.xtraTabPageAngle = new DevExpress.XtraTab.XtraTabPage();
            this.dataGridViewAngle = new System.Windows.Forms.DataGridView();
            this.dataGridViewTextBoxColumn1 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn2 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn3 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn4 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn5 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn6 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn7 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn8 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn9 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.band = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.cellDoor = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dir = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.ob = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.alt = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.anle = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.samplenum = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.celldistance = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.cellDiff5Ang = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.cellDiff3Ang = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.rsrpdiffmax = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.rsrpdiffangle = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn12 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn13 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn14 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn15 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn16 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn17 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn18 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn19 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn20 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn21 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn22 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.xtraTabPagePh = new DevExpress.XtraTab.XtraTabPage();
            this.groupControl = new DevExpress.XtraEditors.GroupControl();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.chartControlRsrpDiff = new DevExpress.XtraCharts.ChartControl();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.chartControlR0R1 = new DevExpress.XtraCharts.ChartControl();
            this.tabCDF_PDF = new DevExpress.XtraTab.XtraTabPage();
            this.groupCDF_PDF = new DevExpress.XtraEditors.GroupControl();
            this.groupCDF_PDF_RSRP1 = new DevExpress.XtraEditors.GroupControl();
            this.chartRSRP1 = new DevExpress.XtraCharts.ChartControl();
            this.groupCDF_PDF_SINR1 = new DevExpress.XtraEditors.GroupControl();
            this.chartSINR1 = new DevExpress.XtraCharts.ChartControl();
            this.groupCDF_PDF_SINR0 = new DevExpress.XtraEditors.GroupControl();
            this.chartSINR0 = new DevExpress.XtraCharts.ChartControl();
            this.groupCDF_PDF_RSRP0 = new DevExpress.XtraEditors.GroupControl();
            this.chartRSRP0 = new DevExpress.XtraCharts.ChartControl();
            this.btnNextpage = new System.Windows.Forms.Button();
            this.btnPrevpage = new System.Windows.Forms.Button();
            this.label5 = new System.Windows.Forms.Label();
            this.txtPage = new System.Windows.Forms.TextBox();
            this.labPage = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.btnGo = new System.Windows.Forms.Button();
            this.labNum = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.btnSearch = new System.Windows.Forms.Button();
            this.txtCellName = new System.Windows.Forms.TextBox();
            this.label1 = new System.Windows.Forms.Label();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTalSheet)).BeginInit();
            this.xtraTalSheet.SuspendLayout();
            this.xtraTabPageCell.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewCell)).BeginInit();
            this.contextMenu.SuspendLayout();
            this.xtraTabPageAngle.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewAngle)).BeginInit();
            this.xtraTabPagePh.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl)).BeginInit();
            this.groupControl.SuspendLayout();
            this.groupBox1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlRsrpDiff)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarDiagram7)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series37)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel19)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarLineSeriesView19)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series38)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel20)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarLineSeriesView20)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel21)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarLineSeriesView21)).BeginInit();
            this.groupBox3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlR0R1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarDiagram8)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series39)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel22)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarLineSeriesView22)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series40)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel23)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarLineSeriesView23)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel24)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarLineSeriesView24)).BeginInit();
            this.tabCDF_PDF.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupCDF_PDF)).BeginInit();
            this.groupCDF_PDF.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupCDF_PDF_RSRP1)).BeginInit();
            this.groupCDF_PDF_RSRP1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chartRSRP1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram13)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(secondaryAxisY13)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series41)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pointSeriesLabel37)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(splineAreaSeriesView25)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series42)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pointSeriesLabel38)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(splineSeriesView13)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pointSeriesLabel39)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(splineAreaSeriesView26)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupCDF_PDF_SINR1)).BeginInit();
            this.groupCDF_PDF_SINR1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chartSINR1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram14)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(secondaryAxisY14)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series43)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pointSeriesLabel40)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(splineAreaSeriesView27)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series44)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pointSeriesLabel41)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(splineSeriesView14)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pointSeriesLabel42)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(splineAreaSeriesView28)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupCDF_PDF_SINR0)).BeginInit();
            this.groupCDF_PDF_SINR0.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chartSINR0)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram15)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(secondaryAxisY15)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series45)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pointSeriesLabel43)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(splineAreaSeriesView29)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series46)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pointSeriesLabel44)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(splineSeriesView15)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pointSeriesLabel45)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(splineAreaSeriesView30)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupCDF_PDF_RSRP0)).BeginInit();
            this.groupCDF_PDF_RSRP0.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chartRSRP0)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram16)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(secondaryAxisY16)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series47)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pointSeriesLabel46)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(splineAreaSeriesView31)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series48)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pointSeriesLabel47)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(splineSeriesView16)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pointSeriesLabel48)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(splineAreaSeriesView32)).BeginInit();
            this.SuspendLayout();
            // 
            // xtraTalSheet
            // 
            this.xtraTalSheet.Dock = System.Windows.Forms.DockStyle.Fill;
            this.xtraTalSheet.Location = new System.Drawing.Point(0, 0);
            this.xtraTalSheet.Name = "xtraTalSheet";
            this.xtraTalSheet.SelectedTabPage = this.xtraTabPageCell;
            this.xtraTalSheet.Size = new System.Drawing.Size(1148, 558);
            this.xtraTalSheet.TabIndex = 0;
            this.xtraTalSheet.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.xtraTabPageCell,
            this.xtraTabPageAngle,
            this.xtraTabPagePh,
            this.tabCDF_PDF});
            // 
            // xtraTabPageCell
            // 
            this.xtraTabPageCell.Controls.Add(this.btnNextpage);
            this.xtraTabPageCell.Controls.Add(this.btnPrevpage);
            this.xtraTabPageCell.Controls.Add(this.label5);
            this.xtraTabPageCell.Controls.Add(this.txtPage);
            this.xtraTabPageCell.Controls.Add(this.labPage);
            this.xtraTabPageCell.Controls.Add(this.label4);
            this.xtraTabPageCell.Controls.Add(this.btnGo);
            this.xtraTabPageCell.Controls.Add(this.labNum);
            this.xtraTabPageCell.Controls.Add(this.label3);
            this.xtraTabPageCell.Controls.Add(this.label2);
            this.xtraTabPageCell.Controls.Add(this.btnSearch);
            this.xtraTabPageCell.Controls.Add(this.txtCellName);
            this.xtraTabPageCell.Controls.Add(this.label1);
            this.xtraTabPageCell.Controls.Add(this.dataGridViewCell);
            this.xtraTabPageCell.Name = "xtraTabPageCell";
            this.xtraTabPageCell.Size = new System.Drawing.Size(1141, 528);
            this.xtraTabPageCell.Text = "天线小区级统计";
            // 
            // dataGridViewCell
            // 
            this.dataGridViewCell.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.dataGridViewCell.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dataGridViewCell.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.SN,
            this.city,
            this.grid,
            this.cellname,
            this.cellcgi,
            this.celltac,
            this.celleci,
            this.cellearfcn,
            this.cellpci,
            this.cellband,
            this.cellInOutDoor,
            this.cellDir,
            this.cellob,
            this.cellalt,
            this.cellsamplenum,
            this.celldist,
            this.reslut,
            this.warnstat,
            this.celldiff10Angle,
            this.celldiff5Angle,
            this.celldiff3Angle,
            this.maincelldifff9Angle,
            this.maincelldifff5Angle,
            this.maincelldiff3Angle,
            this.cellrsrpdiffmax,
            this.cellrsrpangelmax,
            this.cellmainrsrpdiffmax,
            this.cellrsrpanglemax,
            this.cellcoverrate,
            this.r0coverrate,
            this.r1coverrate,
            this.r0avg,
            this.r1avg,
            this.rsrpdiff,
            this.rsrpstability,
            this.s0avg,
            this.s1avg,
            this.sinrdiff,
            this.sinrstability});
            this.dataGridViewCell.ContextMenuStrip = this.contextMenu;
            this.dataGridViewCell.Location = new System.Drawing.Point(0, 0);
            this.dataGridViewCell.Name = "dataGridViewCell";
            this.dataGridViewCell.RowTemplate.Height = 23;
            this.dataGridViewCell.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dataGridViewCell.Size = new System.Drawing.Size(1141, 496);
            this.dataGridViewCell.TabIndex = 0;
            // 
            // SN
            // 
            this.SN.HeaderText = "序号";
            this.SN.Name = "SN";
            // 
            // city
            // 
            this.city.HeaderText = "地市";
            this.city.Name = "city";
            // 
            // grid
            // 
            this.grid.HeaderText = "网格";
            this.grid.Name = "grid";
            // 
            // cellname
            // 
            this.cellname.HeaderText = "小区名称";
            this.cellname.Name = "cellname";
            // 
            // cellcgi
            // 
            this.cellcgi.HeaderText = "小区CGI";
            this.cellcgi.Name = "cellcgi";
            // 
            // celltac
            // 
            this.celltac.HeaderText = "小区TAC";
            this.celltac.Name = "celltac";
            // 
            // celleci
            // 
            this.celleci.HeaderText = "小区ECI";
            this.celleci.Name = "celleci";
            // 
            // cellearfcn
            // 
            this.cellearfcn.HeaderText = "小区EARFCN";
            this.cellearfcn.Name = "cellearfcn";
            // 
            // cellpci
            // 
            this.cellpci.HeaderText = "小区PCI";
            this.cellpci.Name = "cellpci";
            // 
            // cellband
            // 
            this.cellband.HeaderText = "小区频段";
            this.cellband.Name = "cellband";
            // 
            // cellInOutDoor
            // 
            this.cellInOutDoor.HeaderText = "小区室分类型";
            this.cellInOutDoor.Name = "cellInOutDoor";
            // 
            // cellDir
            // 
            this.cellDir.HeaderText = "小区方向角";
            this.cellDir.Name = "cellDir";
            // 
            // cellob
            // 
            this.cellob.HeaderText = "小区下倾角";
            this.cellob.Name = "cellob";
            // 
            // cellalt
            // 
            this.cellalt.HeaderText = "小区挂高";
            this.cellalt.Name = "cellalt";
            // 
            // cellsamplenum
            // 
            this.cellsamplenum.HeaderText = "小区采样点数";
            this.cellsamplenum.Name = "cellsamplenum";
            // 
            // celldist
            // 
            this.celldist.HeaderText = "小区通讯距离";
            this.celldist.Name = "celldist";
            // 
            // reslut
            // 
            this.reslut.HeaderText = "分析结果";
            this.reslut.Name = "reslut";
            // 
            // warnstat
            // 
            this.warnstat.HeaderText = "告警状态";
            this.warnstat.Name = "warnstat";
            // 
            // celldiff10Angle
            // 
            this.celldiff10Angle.HeaderText = "360双流功率差>9dB扫描角";
            this.celldiff10Angle.Name = "celldiff10Angle";
            // 
            // celldiff5Angle
            // 
            this.celldiff5Angle.HeaderText = "360双流功率差>5dB扫描角";
            this.celldiff5Angle.Name = "celldiff5Angle";
            // 
            // celldiff3Angle
            // 
            this.celldiff3Angle.HeaderText = "360双流功率差>3dB扫描角";
            this.celldiff3Angle.Name = "celldiff3Angle";
            // 
            // maincelldifff9Angle
            // 
            this.maincelldifff9Angle.HeaderText = "主瓣双流功率差>9dB扫描角";
            this.maincelldifff9Angle.Name = "maincelldifff9Angle";
            // 
            // maincelldifff5Angle
            // 
            this.maincelldifff5Angle.HeaderText = "主瓣双流功率差>5dB扫描角";
            this.maincelldifff5Angle.Name = "maincelldifff5Angle";
            // 
            // maincelldiff3Angle
            // 
            this.maincelldiff3Angle.HeaderText = "主瓣双流功率差>3dB扫描角";
            this.maincelldiff3Angle.Name = "maincelldiff3Angle";
            // 
            // cellrsrpdiffmax
            // 
            this.cellrsrpdiffmax.HeaderText = "RSRP最大差值";
            this.cellrsrpdiffmax.Name = "cellrsrpdiffmax";
            // 
            // cellrsrpangelmax
            // 
            this.cellrsrpangelmax.HeaderText = "RSRP最大差值角度";
            this.cellrsrpangelmax.Name = "cellrsrpangelmax";
            // 
            // cellmainrsrpdiffmax
            // 
            this.cellmainrsrpdiffmax.HeaderText = "主瓣RSRP最大差值";
            this.cellmainrsrpdiffmax.Name = "cellmainrsrpdiffmax";
            // 
            // cellrsrpanglemax
            // 
            this.cellrsrpanglemax.HeaderText = "主瓣RSRP最大差值角度";
            this.cellrsrpanglemax.Name = "cellrsrpanglemax";
            // 
            // cellcoverrate
            // 
            this.cellcoverrate.HeaderText = "综合覆盖率";
            this.cellcoverrate.Name = "cellcoverrate";
            // 
            // r0coverrate
            // 
            this.r0coverrate.HeaderText = "R0覆盖率";
            this.r0coverrate.Name = "r0coverrate";
            // 
            // r1coverrate
            // 
            this.r1coverrate.HeaderText = "R1覆盖率";
            this.r1coverrate.Name = "r1coverrate";
            // 
            // r0avg
            // 
            this.r0avg.HeaderText = "RSRP0均值";
            this.r0avg.Name = "r0avg";
            // 
            // r1avg
            // 
            this.r1avg.HeaderText = "RSRP1均值";
            this.r1avg.Name = "r1avg";
            // 
            // rsrpdiff
            // 
            this.rsrpdiff.HeaderText = "△RSRP";
            this.rsrpdiff.Name = "rsrpdiff";
            // 
            // rsrpstability
            // 
            this.rsrpstability.HeaderText = "RSRP稳定度";
            this.rsrpstability.Name = "rsrpstability";
            // 
            // s0avg
            // 
            this.s0avg.HeaderText = "SINR0均值";
            this.s0avg.Name = "s0avg";
            // 
            // s1avg
            // 
            this.s1avg.HeaderText = "SINR1均值";
            this.s1avg.Name = "s1avg";
            // 
            // sinrdiff
            // 
            this.sinrdiff.HeaderText = "△SINR";
            this.sinrdiff.Name = "sinrdiff";
            // 
            // sinrstability
            // 
            this.sinrstability.HeaderText = "SINR稳定度";
            this.sinrstability.Name = "sinrstability";
            // 
            // contextMenu
            // 
            this.contextMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.StripMenuDrawPhoto,
            this.antCDF_PDF,
            this.StripMenuOutExcel,
            this.StripMenuOutCSV});
            this.contextMenu.Name = "contextMenu";
            this.contextMenu.Size = new System.Drawing.Size(151, 92);
            // 
            // StripMenuDrawPhoto
            // 
            this.StripMenuDrawPhoto.Name = "StripMenuDrawPhoto";
            this.StripMenuDrawPhoto.Size = new System.Drawing.Size(150, 22);
            this.StripMenuDrawPhoto.Text = "天线波形重建";
            this.StripMenuDrawPhoto.Click += new System.EventHandler(this.StripMenuDrawPhoto_Click);
            // 
            // antCDF_PDF
            // 
            this.antCDF_PDF.Name = "antCDF_PDF";
            this.antCDF_PDF.Size = new System.Drawing.Size(150, 22);
            this.antCDF_PDF.Text = "天线CDF/PDF";
            this.antCDF_PDF.Click += new System.EventHandler(this.antCDF_PDF_Click);
            // 
            // StripMenuOutExcel
            // 
            this.StripMenuOutExcel.Name = "StripMenuOutExcel";
            this.StripMenuOutExcel.Size = new System.Drawing.Size(150, 22);
            this.StripMenuOutExcel.Text = "导出Excel";
            this.StripMenuOutExcel.Click += new System.EventHandler(this.StripMenuOutExcel_Click);
            // 
            // StripMenuOutCSV
            // 
            this.StripMenuOutCSV.Name = "StripMenuOutCSV";
            this.StripMenuOutCSV.Size = new System.Drawing.Size(150, 22);
            this.StripMenuOutCSV.Text = "导出CSV";
            this.StripMenuOutCSV.Click += new System.EventHandler(this.StripMenuOutCSV_Click);
            // 
            // xtraTabPageAngle
            // 
            this.xtraTabPageAngle.Controls.Add(this.dataGridViewAngle);
            this.xtraTabPageAngle.Name = "xtraTabPageAngle";
            this.xtraTabPageAngle.Size = new System.Drawing.Size(1141, 528);
            this.xtraTabPageAngle.Text = "天线角度区间级统计";
            // 
            // dataGridViewAngle
            // 
            this.dataGridViewAngle.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dataGridViewAngle.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.dataGridViewTextBoxColumn1,
            this.dataGridViewTextBoxColumn2,
            this.dataGridViewTextBoxColumn3,
            this.dataGridViewTextBoxColumn4,
            this.dataGridViewTextBoxColumn5,
            this.dataGridViewTextBoxColumn6,
            this.dataGridViewTextBoxColumn7,
            this.dataGridViewTextBoxColumn8,
            this.dataGridViewTextBoxColumn9,
            this.band,
            this.cellDoor,
            this.dir,
            this.ob,
            this.alt,
            this.anle,
            this.samplenum,
            this.celldistance,
            this.cellDiff5Ang,
            this.cellDiff3Ang,
            this.rsrpdiffmax,
            this.rsrpdiffangle,
            this.dataGridViewTextBoxColumn12,
            this.dataGridViewTextBoxColumn13,
            this.dataGridViewTextBoxColumn14,
            this.dataGridViewTextBoxColumn15,
            this.dataGridViewTextBoxColumn16,
            this.dataGridViewTextBoxColumn17,
            this.dataGridViewTextBoxColumn18,
            this.dataGridViewTextBoxColumn19,
            this.dataGridViewTextBoxColumn20,
            this.dataGridViewTextBoxColumn21,
            this.dataGridViewTextBoxColumn22});
            this.dataGridViewAngle.ContextMenuStrip = this.contextMenu;
            this.dataGridViewAngle.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dataGridViewAngle.Location = new System.Drawing.Point(0, 0);
            this.dataGridViewAngle.Name = "dataGridViewAngle";
            this.dataGridViewAngle.RowTemplate.Height = 23;
            this.dataGridViewAngle.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dataGridViewAngle.Size = new System.Drawing.Size(1141, 528);
            this.dataGridViewAngle.TabIndex = 1;
            // 
            // dataGridViewTextBoxColumn1
            // 
            this.dataGridViewTextBoxColumn1.HeaderText = "序号";
            this.dataGridViewTextBoxColumn1.Name = "dataGridViewTextBoxColumn1";
            // 
            // dataGridViewTextBoxColumn2
            // 
            this.dataGridViewTextBoxColumn2.HeaderText = "地市";
            this.dataGridViewTextBoxColumn2.Name = "dataGridViewTextBoxColumn2";
            // 
            // dataGridViewTextBoxColumn3
            // 
            this.dataGridViewTextBoxColumn3.HeaderText = "网格";
            this.dataGridViewTextBoxColumn3.Name = "dataGridViewTextBoxColumn3";
            // 
            // dataGridViewTextBoxColumn4
            // 
            this.dataGridViewTextBoxColumn4.HeaderText = "小区名称";
            this.dataGridViewTextBoxColumn4.Name = "dataGridViewTextBoxColumn4";
            // 
            // dataGridViewTextBoxColumn5
            // 
            this.dataGridViewTextBoxColumn5.HeaderText = "小区CGI";
            this.dataGridViewTextBoxColumn5.Name = "dataGridViewTextBoxColumn5";
            // 
            // dataGridViewTextBoxColumn6
            // 
            this.dataGridViewTextBoxColumn6.HeaderText = "小区TAC";
            this.dataGridViewTextBoxColumn6.Name = "dataGridViewTextBoxColumn6";
            // 
            // dataGridViewTextBoxColumn7
            // 
            this.dataGridViewTextBoxColumn7.HeaderText = "小区ECI";
            this.dataGridViewTextBoxColumn7.Name = "dataGridViewTextBoxColumn7";
            // 
            // dataGridViewTextBoxColumn8
            // 
            this.dataGridViewTextBoxColumn8.HeaderText = "小区EARFCN";
            this.dataGridViewTextBoxColumn8.Name = "dataGridViewTextBoxColumn8";
            // 
            // dataGridViewTextBoxColumn9
            // 
            this.dataGridViewTextBoxColumn9.HeaderText = "小区PCI";
            this.dataGridViewTextBoxColumn9.Name = "dataGridViewTextBoxColumn9";
            // 
            // band
            // 
            this.band.HeaderText = "小区频段";
            this.band.Name = "band";
            // 
            // cellDoor
            // 
            this.cellDoor.HeaderText = "小区室分类型";
            this.cellDoor.Name = "cellDoor";
            // 
            // dir
            // 
            this.dir.HeaderText = "小区方向角";
            this.dir.Name = "dir";
            // 
            // ob
            // 
            this.ob.HeaderText = "小区下倾角";
            this.ob.Name = "ob";
            // 
            // alt
            // 
            this.alt.HeaderText = "小区挂高";
            this.alt.Name = "alt";
            // 
            // anle
            // 
            this.anle.HeaderText = "角度区间类型";
            this.anle.Name = "anle";
            // 
            // samplenum
            // 
            this.samplenum.HeaderText = "采样点数";
            this.samplenum.Name = "samplenum";
            // 
            // celldistance
            // 
            this.celldistance.HeaderText = "平均通讯距离";
            this.celldistance.Name = "celldistance";
            // 
            // cellDiff5Ang
            // 
            this.cellDiff5Ang.HeaderText = "双流功率差>5dB扫描角";
            this.cellDiff5Ang.Name = "cellDiff5Ang";
            // 
            // cellDiff3Ang
            // 
            this.cellDiff3Ang.HeaderText = "双流功率差>3dB扫描角";
            this.cellDiff3Ang.Name = "cellDiff3Ang";
            // 
            // rsrpdiffmax
            // 
            this.rsrpdiffmax.HeaderText = "RSRP最大差值";
            this.rsrpdiffmax.Name = "rsrpdiffmax";
            // 
            // rsrpdiffangle
            // 
            this.rsrpdiffangle.HeaderText = "RSRP最大差值角度";
            this.rsrpdiffangle.Name = "rsrpdiffangle";
            // 
            // dataGridViewTextBoxColumn12
            // 
            this.dataGridViewTextBoxColumn12.HeaderText = "综合覆盖率";
            this.dataGridViewTextBoxColumn12.Name = "dataGridViewTextBoxColumn12";
            // 
            // dataGridViewTextBoxColumn13
            // 
            this.dataGridViewTextBoxColumn13.HeaderText = "R0覆盖率";
            this.dataGridViewTextBoxColumn13.Name = "dataGridViewTextBoxColumn13";
            // 
            // dataGridViewTextBoxColumn14
            // 
            this.dataGridViewTextBoxColumn14.HeaderText = "R1覆盖率";
            this.dataGridViewTextBoxColumn14.Name = "dataGridViewTextBoxColumn14";
            // 
            // dataGridViewTextBoxColumn15
            // 
            this.dataGridViewTextBoxColumn15.HeaderText = "RSRP0均值";
            this.dataGridViewTextBoxColumn15.Name = "dataGridViewTextBoxColumn15";
            // 
            // dataGridViewTextBoxColumn16
            // 
            this.dataGridViewTextBoxColumn16.HeaderText = "RSRP1均值";
            this.dataGridViewTextBoxColumn16.Name = "dataGridViewTextBoxColumn16";
            // 
            // dataGridViewTextBoxColumn17
            // 
            this.dataGridViewTextBoxColumn17.HeaderText = "△RSRP";
            this.dataGridViewTextBoxColumn17.Name = "dataGridViewTextBoxColumn17";
            // 
            // dataGridViewTextBoxColumn18
            // 
            this.dataGridViewTextBoxColumn18.HeaderText = "RSRP稳定度";
            this.dataGridViewTextBoxColumn18.Name = "dataGridViewTextBoxColumn18";
            // 
            // dataGridViewTextBoxColumn19
            // 
            this.dataGridViewTextBoxColumn19.HeaderText = "SINR0均值";
            this.dataGridViewTextBoxColumn19.Name = "dataGridViewTextBoxColumn19";
            // 
            // dataGridViewTextBoxColumn20
            // 
            this.dataGridViewTextBoxColumn20.HeaderText = "SINR1均值";
            this.dataGridViewTextBoxColumn20.Name = "dataGridViewTextBoxColumn20";
            // 
            // dataGridViewTextBoxColumn21
            // 
            this.dataGridViewTextBoxColumn21.HeaderText = "△SINR";
            this.dataGridViewTextBoxColumn21.Name = "dataGridViewTextBoxColumn21";
            // 
            // dataGridViewTextBoxColumn22
            // 
            this.dataGridViewTextBoxColumn22.HeaderText = "SINR稳定度";
            this.dataGridViewTextBoxColumn22.Name = "dataGridViewTextBoxColumn22";
            // 
            // xtraTabPagePh
            // 
            this.xtraTabPagePh.Controls.Add(this.groupControl);
            this.xtraTabPagePh.Name = "xtraTabPagePh";
            this.xtraTabPagePh.Size = new System.Drawing.Size(1141, 528);
            this.xtraTabPagePh.Text = "天线波形重建雷达图";
            // 
            // groupControl
            // 
            this.groupControl.Controls.Add(this.groupBox1);
            this.groupControl.Controls.Add(this.groupBox3);
            this.groupControl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControl.Location = new System.Drawing.Point(0, 0);
            this.groupControl.Name = "groupControl";
            this.groupControl.Size = new System.Drawing.Size(1141, 528);
            this.groupControl.TabIndex = 0;
            this.groupControl.Text = "雷达图分析";
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.chartControlRsrpDiff);
            this.groupBox1.Location = new System.Drawing.Point(585, 26);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(549, 495);
            this.groupBox1.TabIndex = 2;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "△R_R稳定度雷达图";
            // 
            // chartControlRsrpDiff
            // 
            this.chartControlRsrpDiff.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.chartControlRsrpDiff.BorderOptions.Color = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(255)))));
            radarDiagram7.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            radarDiagram7.AxisY.Range.Auto = false;
            radarDiagram7.AxisY.Range.MaxValueSerializable = "50";
            radarDiagram7.AxisY.Range.MinValueSerializable = "-50";
            radarDiagram7.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            radarDiagram7.AxisY.Range.SideMarginsEnabled = true;
            radarDiagram7.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(192)))), ((int)(((byte)(255)))), ((int)(((byte)(192)))));
            this.chartControlRsrpDiff.Diagram = radarDiagram7;
            this.chartControlRsrpDiff.Location = new System.Drawing.Point(7, 15);
            this.chartControlRsrpDiff.Name = "chartControlRsrpDiff";
            radarPointSeriesLabel19.LineVisible = true;
            series37.Label = radarPointSeriesLabel19;
            series37.Name = "AntSeries";
            series37.Points.AddRange(new DevExpress.XtraCharts.SeriesPoint[] {
            seriesPoint31,
            seriesPoint32,
            seriesPoint33,
            seriesPoint34,
            seriesPoint35});
            series37.ShowInLegend = false;
            series37.View = radarLineSeriesView19;
            radarPointSeriesLabel20.LineVisible = true;
            series38.Label = radarPointSeriesLabel20;
            series38.Name = "StandardSeries";
            series38.ShowInLegend = false;
            series38.View = radarLineSeriesView20;
            this.chartControlRsrpDiff.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series37,
        series38};
            radarPointSeriesLabel21.LineVisible = true;
            this.chartControlRsrpDiff.SeriesTemplate.Label = radarPointSeriesLabel21;
            this.chartControlRsrpDiff.SeriesTemplate.View = radarLineSeriesView21;
            this.chartControlRsrpDiff.Size = new System.Drawing.Size(536, 474);
            this.chartControlRsrpDiff.TabIndex = 0;
            // 
            // groupBox3
            // 
            this.groupBox3.Controls.Add(this.chartControlR0R1);
            this.groupBox3.Location = new System.Drawing.Point(5, 26);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.Size = new System.Drawing.Size(574, 495);
            this.groupBox3.TabIndex = 1;
            this.groupBox3.TabStop = false;
            this.groupBox3.Text = "R0_R1雷达图";
            // 
            // chartControlR0R1
            // 
            this.chartControlR0R1.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left)));
            this.chartControlR0R1.BorderOptions.Color = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(255)))));
            radarDiagram8.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            radarDiagram8.AxisY.Range.Auto = false;
            radarDiagram8.AxisY.Range.MaxValueSerializable = "0";
            radarDiagram8.AxisY.Range.MinValueSerializable = "-140";
            radarDiagram8.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            radarDiagram8.AxisY.Range.SideMarginsEnabled = true;
            radarDiagram8.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(192)))), ((int)(((byte)(255)))), ((int)(((byte)(192)))));
            this.chartControlR0R1.Diagram = radarDiagram8;
            this.chartControlR0R1.Location = new System.Drawing.Point(7, 16);
            this.chartControlR0R1.Name = "chartControlR0R1";
            radarPointSeriesLabel22.LineVisible = true;
            series39.Label = radarPointSeriesLabel22;
            series39.Name = "AntSeries";
            series39.Points.AddRange(new DevExpress.XtraCharts.SeriesPoint[] {
            seriesPoint36,
            seriesPoint37,
            seriesPoint38,
            seriesPoint39,
            seriesPoint40});
            series39.ShowInLegend = false;
            series39.View = radarLineSeriesView22;
            radarPointSeriesLabel23.LineVisible = true;
            series40.Label = radarPointSeriesLabel23;
            series40.Name = "StandardSeries";
            series40.ShowInLegend = false;
            series40.View = radarLineSeriesView23;
            this.chartControlR0R1.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series39,
        series40};
            radarPointSeriesLabel24.LineVisible = true;
            this.chartControlR0R1.SeriesTemplate.Label = radarPointSeriesLabel24;
            this.chartControlR0R1.SeriesTemplate.View = radarLineSeriesView24;
            this.chartControlR0R1.Size = new System.Drawing.Size(561, 473);
            this.chartControlR0R1.TabIndex = 0;
            // 
            // tabCDF_PDF
            // 
            this.tabCDF_PDF.Controls.Add(this.groupCDF_PDF);
            this.tabCDF_PDF.Name = "tabCDF_PDF";
            this.tabCDF_PDF.Size = new System.Drawing.Size(1141, 528);
            this.tabCDF_PDF.Text = "小区CDF/PDF图";
            // 
            // groupCDF_PDF
            // 
            this.groupCDF_PDF.Controls.Add(this.groupCDF_PDF_RSRP1);
            this.groupCDF_PDF.Controls.Add(this.groupCDF_PDF_SINR1);
            this.groupCDF_PDF.Controls.Add(this.groupCDF_PDF_SINR0);
            this.groupCDF_PDF.Controls.Add(this.groupCDF_PDF_RSRP0);
            this.groupCDF_PDF.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupCDF_PDF.Location = new System.Drawing.Point(0, 0);
            this.groupCDF_PDF.Name = "groupCDF_PDF";
            this.groupCDF_PDF.Size = new System.Drawing.Size(1141, 528);
            this.groupCDF_PDF.TabIndex = 0;
            this.groupCDF_PDF.Text = "CDF_PDF图";
            // 
            // groupCDF_PDF_RSRP1
            // 
            this.groupCDF_PDF_RSRP1.Controls.Add(this.chartRSRP1);
            this.groupCDF_PDF_RSRP1.Location = new System.Drawing.Point(5, 271);
            this.groupCDF_PDF_RSRP1.Name = "groupCDF_PDF_RSRP1";
            this.groupCDF_PDF_RSRP1.Size = new System.Drawing.Size(571, 240);
            this.groupCDF_PDF_RSRP1.TabIndex = 3;
            this.groupCDF_PDF_RSRP1.Text = "CDF_PDF_RSRP1";
            // 
            // chartRSRP1
            // 
            xyDiagram13.AxisX.Range.Auto = false;
            xyDiagram13.AxisX.Range.MaxValueSerializable = "-75";
            xyDiagram13.AxisX.Range.MinValueSerializable = "-140";
            xyDiagram13.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram13.AxisX.Range.SideMarginsEnabled = true;
            xyDiagram13.AxisX.VisibleInPanesSerializable = "-1";
            xyDiagram13.AxisY.GridSpacing = 10D;
            xyDiagram13.AxisY.GridSpacingAuto = false;
            xyDiagram13.AxisY.Range.Auto = false;
            xyDiagram13.AxisY.Range.MaxValueSerializable = "100";
            xyDiagram13.AxisY.Range.MinValueSerializable = "0";
            xyDiagram13.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram13.AxisY.Range.SideMarginsEnabled = true;
            xyDiagram13.AxisY.VisibleInPanesSerializable = "-1";
            secondaryAxisY13.AxisID = 0;
            secondaryAxisY13.GridSpacing = 2D;
            secondaryAxisY13.GridSpacingAuto = false;
            secondaryAxisY13.Name = "Rsrp1_PDF_Y";
            secondaryAxisY13.Range.Auto = false;
            secondaryAxisY13.Range.MaxValueSerializable = "25";
            secondaryAxisY13.Range.MinValueSerializable = "0";
            secondaryAxisY13.Range.ScrollingRange.SideMarginsEnabled = true;
            secondaryAxisY13.Range.SideMarginsEnabled = true;
            secondaryAxisY13.VisibleInPanesSerializable = "-1";
            xyDiagram13.SecondaryAxesY.AddRange(new DevExpress.XtraCharts.SecondaryAxisY[] {
            secondaryAxisY13});
            this.chartRSRP1.Diagram = xyDiagram13;
            this.chartRSRP1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.chartRSRP1.Location = new System.Drawing.Point(2, 23);
            this.chartRSRP1.Name = "chartRSRP1";
            pointSeriesLabel37.LineVisible = true;
            series41.Label = pointSeriesLabel37;
            series41.Name = "Rsrp0CDF";
            series41.View = splineAreaSeriesView25;
            pointSeriesLabel38.LineVisible = true;
            series42.Label = pointSeriesLabel38;
            series42.Name = "Rsrp1PDF";
            series42.View = splineSeriesView13;
            this.chartRSRP1.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series41,
        series42};
            pointSeriesLabel39.LineVisible = true;
            this.chartRSRP1.SeriesTemplate.Label = pointSeriesLabel39;
            splineAreaSeriesView26.Transparency = ((byte)(0));
            this.chartRSRP1.SeriesTemplate.View = splineAreaSeriesView26;
            this.chartRSRP1.Size = new System.Drawing.Size(567, 215);
            this.chartRSRP1.TabIndex = 0;
            // 
            // groupCDF_PDF_SINR1
            // 
            this.groupCDF_PDF_SINR1.Controls.Add(this.chartSINR1);
            this.groupCDF_PDF_SINR1.Location = new System.Drawing.Point(574, 271);
            this.groupCDF_PDF_SINR1.Name = "groupCDF_PDF_SINR1";
            this.groupCDF_PDF_SINR1.Size = new System.Drawing.Size(562, 240);
            this.groupCDF_PDF_SINR1.TabIndex = 2;
            this.groupCDF_PDF_SINR1.Text = "CDF_PDF_SINR1";
            // 
            // chartSINR1
            // 
            xyDiagram14.AxisX.Range.Auto = false;
            xyDiagram14.AxisX.Range.MaxValueSerializable = "25";
            xyDiagram14.AxisX.Range.MinValueSerializable = "-10";
            xyDiagram14.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram14.AxisX.Range.SideMarginsEnabled = true;
            xyDiagram14.AxisX.VisibleInPanesSerializable = "-1";
            xyDiagram14.AxisY.GridSpacing = 10D;
            xyDiagram14.AxisY.GridSpacingAuto = false;
            xyDiagram14.AxisY.Range.Auto = false;
            xyDiagram14.AxisY.Range.MaxValueSerializable = "100";
            xyDiagram14.AxisY.Range.MinValueSerializable = "0";
            xyDiagram14.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram14.AxisY.Range.SideMarginsEnabled = true;
            xyDiagram14.AxisY.VisibleInPanesSerializable = "-1";
            secondaryAxisY14.AxisID = 0;
            secondaryAxisY14.GridSpacing = 2D;
            secondaryAxisY14.GridSpacingAuto = false;
            secondaryAxisY14.Name = "Sinr1_PDF_Y";
            secondaryAxisY14.Range.Auto = false;
            secondaryAxisY14.Range.MaxValueSerializable = "25";
            secondaryAxisY14.Range.MinValueSerializable = "0";
            secondaryAxisY14.Range.ScrollingRange.SideMarginsEnabled = true;
            secondaryAxisY14.Range.SideMarginsEnabled = true;
            secondaryAxisY14.VisibleInPanesSerializable = "-1";
            xyDiagram14.SecondaryAxesY.AddRange(new DevExpress.XtraCharts.SecondaryAxisY[] {
            secondaryAxisY14});
            this.chartSINR1.Diagram = xyDiagram14;
            this.chartSINR1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.chartSINR1.Location = new System.Drawing.Point(2, 23);
            this.chartSINR1.Name = "chartSINR1";
            pointSeriesLabel40.LineVisible = true;
            series43.Label = pointSeriesLabel40;
            series43.Name = "Sinr1CDF";
            splineAreaSeriesView27.MarkerOptions.Size = 2;
            series43.View = splineAreaSeriesView27;
            pointSeriesLabel41.LineVisible = true;
            series44.Label = pointSeriesLabel41;
            series44.Name = "Sinr1PDF";
            series44.View = splineSeriesView14;
            this.chartSINR1.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series43,
        series44};
            pointSeriesLabel42.LineVisible = true;
            this.chartSINR1.SeriesTemplate.Label = pointSeriesLabel42;
            splineAreaSeriesView28.Transparency = ((byte)(0));
            this.chartSINR1.SeriesTemplate.View = splineAreaSeriesView28;
            this.chartSINR1.Size = new System.Drawing.Size(558, 215);
            this.chartSINR1.TabIndex = 1;
            // 
            // groupCDF_PDF_SINR0
            // 
            this.groupCDF_PDF_SINR0.Controls.Add(this.chartSINR0);
            this.groupCDF_PDF_SINR0.Location = new System.Drawing.Point(572, 27);
            this.groupCDF_PDF_SINR0.Name = "groupCDF_PDF_SINR0";
            this.groupCDF_PDF_SINR0.Size = new System.Drawing.Size(566, 240);
            this.groupCDF_PDF_SINR0.TabIndex = 1;
            this.groupCDF_PDF_SINR0.Text = "CDF_PDF_SINR0";
            // 
            // chartSINR0
            // 
            xyDiagram15.AxisX.Range.Auto = false;
            xyDiagram15.AxisX.Range.MaxValueSerializable = "25";
            xyDiagram15.AxisX.Range.MinValueSerializable = "-10";
            xyDiagram15.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram15.AxisX.Range.SideMarginsEnabled = true;
            xyDiagram15.AxisX.VisibleInPanesSerializable = "-1";
            xyDiagram15.AxisY.GridSpacing = 10D;
            xyDiagram15.AxisY.GridSpacingAuto = false;
            xyDiagram15.AxisY.Range.Auto = false;
            xyDiagram15.AxisY.Range.MaxValueSerializable = "100";
            xyDiagram15.AxisY.Range.MinValueSerializable = "0";
            xyDiagram15.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram15.AxisY.Range.SideMarginsEnabled = true;
            xyDiagram15.AxisY.VisibleInPanesSerializable = "-1";
            secondaryAxisY15.AxisID = 0;
            secondaryAxisY15.GridSpacing = 2D;
            secondaryAxisY15.GridSpacingAuto = false;
            secondaryAxisY15.Name = "Sinr0_PDF_Y";
            secondaryAxisY15.Range.Auto = false;
            secondaryAxisY15.Range.MaxValueSerializable = "25";
            secondaryAxisY15.Range.MinValueSerializable = "0";
            secondaryAxisY15.Range.ScrollingRange.SideMarginsEnabled = true;
            secondaryAxisY15.Range.SideMarginsEnabled = true;
            secondaryAxisY15.VisibleInPanesSerializable = "-1";
            xyDiagram15.SecondaryAxesY.AddRange(new DevExpress.XtraCharts.SecondaryAxisY[] {
            secondaryAxisY15});
            this.chartSINR0.Diagram = xyDiagram15;
            this.chartSINR0.Dock = System.Windows.Forms.DockStyle.Fill;
            this.chartSINR0.Location = new System.Drawing.Point(2, 23);
            this.chartSINR0.Name = "chartSINR0";
            pointSeriesLabel43.LineVisible = true;
            series45.Label = pointSeriesLabel43;
            series45.Name = "Sinr0CDF";
            splineAreaSeriesView29.MarkerOptions.Size = 2;
            series45.View = splineAreaSeriesView29;
            pointSeriesLabel44.LineVisible = true;
            series46.Label = pointSeriesLabel44;
            series46.Name = "Sinr0PDF";
            series46.View = splineSeriesView15;
            this.chartSINR0.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series45,
        series46};
            pointSeriesLabel45.LineVisible = true;
            this.chartSINR0.SeriesTemplate.Label = pointSeriesLabel45;
            splineAreaSeriesView30.Transparency = ((byte)(0));
            this.chartSINR0.SeriesTemplate.View = splineAreaSeriesView30;
            this.chartSINR0.Size = new System.Drawing.Size(562, 215);
            this.chartSINR0.TabIndex = 1;
            // 
            // groupCDF_PDF_RSRP0
            // 
            this.groupCDF_PDF_RSRP0.Controls.Add(this.chartRSRP0);
            this.groupCDF_PDF_RSRP0.Location = new System.Drawing.Point(6, 27);
            this.groupCDF_PDF_RSRP0.Name = "groupCDF_PDF_RSRP0";
            this.groupCDF_PDF_RSRP0.Size = new System.Drawing.Size(570, 240);
            this.groupCDF_PDF_RSRP0.TabIndex = 0;
            this.groupCDF_PDF_RSRP0.Text = "CDF_PDF_RSRP0";
            // 
            // chartRSRP0
            // 
            xyDiagram16.AxisX.Range.Auto = false;
            xyDiagram16.AxisX.Range.MaxValueSerializable = "-75";
            xyDiagram16.AxisX.Range.MinValueSerializable = "-140";
            xyDiagram16.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram16.AxisX.Range.SideMarginsEnabled = true;
            xyDiagram16.AxisX.VisibleInPanesSerializable = "-1";
            xyDiagram16.AxisY.GridSpacing = 10D;
            xyDiagram16.AxisY.GridSpacingAuto = false;
            xyDiagram16.AxisY.Range.Auto = false;
            xyDiagram16.AxisY.Range.MaxValueSerializable = "100";
            xyDiagram16.AxisY.Range.MinValueSerializable = "0";
            xyDiagram16.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram16.AxisY.Range.SideMarginsEnabled = true;
            xyDiagram16.AxisY.VisibleInPanesSerializable = "-1";
            secondaryAxisY16.AxisID = 0;
            secondaryAxisY16.GridSpacing = 2D;
            secondaryAxisY16.GridSpacingAuto = false;
            secondaryAxisY16.Name = "Rsrp0_PDF_Y";
            secondaryAxisY16.Range.Auto = false;
            secondaryAxisY16.Range.MaxValueSerializable = "25";
            secondaryAxisY16.Range.MinValueSerializable = "0";
            secondaryAxisY16.Range.ScrollingRange.SideMarginsEnabled = true;
            secondaryAxisY16.Range.SideMarginsEnabled = true;
            secondaryAxisY16.VisibleInPanesSerializable = "-1";
            xyDiagram16.SecondaryAxesY.AddRange(new DevExpress.XtraCharts.SecondaryAxisY[] {
            secondaryAxisY16});
            this.chartRSRP0.Diagram = xyDiagram16;
            this.chartRSRP0.Dock = System.Windows.Forms.DockStyle.Fill;
            this.chartRSRP0.Location = new System.Drawing.Point(2, 23);
            this.chartRSRP0.Name = "chartRSRP0";
            pointSeriesLabel46.LineVisible = true;
            series47.Label = pointSeriesLabel46;
            series47.Name = "Rsrp0CDF";
            series47.View = splineAreaSeriesView31;
            pointSeriesLabel47.LineVisible = true;
            series48.Label = pointSeriesLabel47;
            series48.Name = "Rsrp0PDF";
            series48.View = splineSeriesView16;
            this.chartRSRP0.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series47,
        series48};
            pointSeriesLabel48.LineVisible = true;
            this.chartRSRP0.SeriesTemplate.Label = pointSeriesLabel48;
            splineAreaSeriesView32.Transparency = ((byte)(0));
            this.chartRSRP0.SeriesTemplate.View = splineAreaSeriesView32;
            this.chartRSRP0.Size = new System.Drawing.Size(566, 215);
            this.chartRSRP0.TabIndex = 0;
            // 
            // btnNextpage
            // 
            this.btnNextpage.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnNextpage.Location = new System.Drawing.Point(823, 503);
            this.btnNextpage.Name = "btnNextpage";
            this.btnNextpage.Size = new System.Drawing.Size(33, 23);
            this.btnNextpage.TabIndex = 29;
            this.btnNextpage.Text = ">>";
            this.btnNextpage.UseVisualStyleBackColor = true;
            this.btnNextpage.Click += new System.EventHandler(this.btnNextpage_Click);
            // 
            // btnPrevpage
            // 
            this.btnPrevpage.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnPrevpage.Location = new System.Drawing.Point(784, 503);
            this.btnPrevpage.Name = "btnPrevpage";
            this.btnPrevpage.Size = new System.Drawing.Size(33, 23);
            this.btnPrevpage.TabIndex = 28;
            this.btnPrevpage.Text = "<<";
            this.btnPrevpage.UseVisualStyleBackColor = true;
            this.btnPrevpage.Click += new System.EventHandler(this.btnPrevpage_Click);
            // 
            // label5
            // 
            this.label5.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(726, 505);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(19, 14);
            this.label5.TabIndex = 27;
            this.label5.Text = "页";
            // 
            // txtPage
            // 
            this.txtPage.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.txtPage.Location = new System.Drawing.Point(661, 502);
            this.txtPage.Name = "txtPage";
            this.txtPage.Size = new System.Drawing.Size(63, 22);
            this.txtPage.TabIndex = 26;
            // 
            // labPage
            // 
            this.labPage.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.labPage.AutoSize = true;
            this.labPage.Location = new System.Drawing.Point(561, 507);
            this.labPage.Name = "labPage";
            this.labPage.Size = new System.Drawing.Size(14, 14);
            this.labPage.TabIndex = 25;
            this.labPage.Text = "0";
            // 
            // label4
            // 
            this.label4.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(518, 506);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(43, 14);
            this.label4.TabIndex = 24;
            this.label4.Text = "个，共";
            // 
            // btnGo
            // 
            this.btnGo.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnGo.Location = new System.Drawing.Point(745, 502);
            this.btnGo.Name = "btnGo";
            this.btnGo.Size = new System.Drawing.Size(33, 23);
            this.btnGo.TabIndex = 23;
            this.btnGo.Text = "GO";
            this.btnGo.UseVisualStyleBackColor = true;
            this.btnGo.Click += new System.EventHandler(this.btnGo_Click);
            // 
            // labNum
            // 
            this.labNum.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.labNum.AutoSize = true;
            this.labNum.Location = new System.Drawing.Point(473, 507);
            this.labNum.Name = "labNum";
            this.labNum.Size = new System.Drawing.Size(14, 14);
            this.labNum.TabIndex = 22;
            this.labNum.Text = "0";
            // 
            // label3
            // 
            this.label3.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(595, 506);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(67, 14);
            this.label3.TabIndex = 21;
            this.label3.Text = "页，跳转至";
            // 
            // label2
            // 
            this.label2.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(397, 506);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(79, 14);
            this.label2.TabIndex = 20;
            this.label2.Text = "总计小区共：";
            // 
            // btnSearch
            // 
            this.btnSearch.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnSearch.Location = new System.Drawing.Point(1091, 502);
            this.btnSearch.Name = "btnSearch";
            this.btnSearch.Size = new System.Drawing.Size(42, 23);
            this.btnSearch.TabIndex = 19;
            this.btnSearch.Text = "查找";
            this.btnSearch.UseVisualStyleBackColor = true;
            this.btnSearch.Click += new System.EventHandler(this.btnSearch_Click);
            // 
            // txtCellName
            // 
            this.txtCellName.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.txtCellName.Location = new System.Drawing.Point(929, 503);
            this.txtCellName.Name = "txtCellName";
            this.txtCellName.Size = new System.Drawing.Size(157, 22);
            this.txtCellName.TabIndex = 18;
            // 
            // label1
            // 
            this.label1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(868, 507);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(67, 14);
            this.label1.TabIndex = 17;
            this.label1.Text = "小区名称：";
            // 
            // LTEScanMIMOAntennaForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1148, 558);
            this.Controls.Add(this.xtraTalSheet);
            this.Name = "LTEScanMIMOAntennaForm";
            this.Text = "LTE天线双流分析结果";
            ((System.ComponentModel.ISupportInitialize)(this.xtraTalSheet)).EndInit();
            this.xtraTalSheet.ResumeLayout(false);
            this.xtraTabPageCell.ResumeLayout(false);
            this.xtraTabPageCell.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewCell)).EndInit();
            this.contextMenu.ResumeLayout(false);
            this.xtraTabPageAngle.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewAngle)).EndInit();
            this.xtraTabPagePh.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl)).EndInit();
            this.groupControl.ResumeLayout(false);
            this.groupBox1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(radarDiagram7)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel19)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarLineSeriesView19)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series37)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel20)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarLineSeriesView20)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series38)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel21)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarLineSeriesView21)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlRsrpDiff)).EndInit();
            this.groupBox3.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(radarDiagram8)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel22)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarLineSeriesView22)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series39)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel23)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarLineSeriesView23)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series40)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel24)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarLineSeriesView24)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlR0R1)).EndInit();
            this.tabCDF_PDF.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupCDF_PDF)).EndInit();
            this.groupCDF_PDF.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupCDF_PDF_RSRP1)).EndInit();
            this.groupCDF_PDF_RSRP1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(secondaryAxisY13)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram13)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(pointSeriesLabel37)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(splineAreaSeriesView25)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series41)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(pointSeriesLabel38)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(splineSeriesView13)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series42)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(pointSeriesLabel39)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(splineAreaSeriesView26)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartRSRP1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupCDF_PDF_SINR1)).EndInit();
            this.groupCDF_PDF_SINR1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(secondaryAxisY14)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram14)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(pointSeriesLabel40)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(splineAreaSeriesView27)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series43)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(pointSeriesLabel41)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(splineSeriesView14)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series44)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(pointSeriesLabel42)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(splineAreaSeriesView28)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartSINR1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupCDF_PDF_SINR0)).EndInit();
            this.groupCDF_PDF_SINR0.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(secondaryAxisY15)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram15)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(pointSeriesLabel43)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(splineAreaSeriesView29)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series45)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(pointSeriesLabel44)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(splineSeriesView15)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series46)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(pointSeriesLabel45)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(splineAreaSeriesView30)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartSINR0)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupCDF_PDF_RSRP0)).EndInit();
            this.groupCDF_PDF_RSRP0.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(secondaryAxisY16)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram16)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(pointSeriesLabel46)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(splineAreaSeriesView31)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series47)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(pointSeriesLabel47)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(splineSeriesView16)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series48)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(pointSeriesLabel48)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(splineAreaSeriesView32)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartRSRP0)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraTab.XtraTabControl xtraTalSheet;
        private DevExpress.XtraTab.XtraTabPage xtraTabPageCell;
        private DevExpress.XtraTab.XtraTabPage xtraTabPageAngle;
        private System.Windows.Forms.DataGridView dataGridViewCell;
        private System.Windows.Forms.DataGridView dataGridViewAngle;
        private DevExpress.XtraTab.XtraTabPage xtraTabPagePh;
        private System.Windows.Forms.ContextMenuStrip contextMenu;
        private System.Windows.Forms.ToolStripMenuItem StripMenuDrawPhoto;
        private System.Windows.Forms.ToolStripMenuItem StripMenuOutExcel;
        private DevExpress.XtraEditors.GroupControl groupControl;
        private System.Windows.Forms.GroupBox groupBox3;
        private DevExpress.XtraCharts.ChartControl chartControlR0R1;
        private System.Windows.Forms.GroupBox groupBox1;
        private DevExpress.XtraCharts.ChartControl chartControlRsrpDiff;
        private System.Windows.Forms.ToolStripMenuItem StripMenuOutCSV;
        private DevExpress.XtraTab.XtraTabPage tabCDF_PDF;
        private DevExpress.XtraEditors.GroupControl groupCDF_PDF;
        private DevExpress.XtraEditors.GroupControl groupCDF_PDF_RSRP0;
        private DevExpress.XtraEditors.GroupControl groupCDF_PDF_SINR0;
        //private DevExpress.XtraReports.UserDesigner.RecentlyUsedItemsComboBox recentlyUsedItemsComboBox1;
        //private DevExpress.XtraReports.UserDesigner.DesignRepositoryItemComboBox designRepositoryItemComboBox1;
        private DevExpress.XtraCharts.ChartControl chartSINR0;
        private DevExpress.XtraCharts.ChartControl chartRSRP0;
        private System.Windows.Forms.ToolStripMenuItem antCDF_PDF;
        private DevExpress.XtraEditors.GroupControl groupCDF_PDF_SINR1;
        private DevExpress.XtraCharts.ChartControl chartSINR1;
        private DevExpress.XtraEditors.GroupControl groupCDF_PDF_RSRP1;
        private DevExpress.XtraCharts.ChartControl chartRSRP1;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn1;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn2;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn3;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn4;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn5;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn6;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn7;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn8;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn9;
        private System.Windows.Forms.DataGridViewTextBoxColumn band;
        private System.Windows.Forms.DataGridViewTextBoxColumn cellDoor;
        private System.Windows.Forms.DataGridViewTextBoxColumn dir;
        private System.Windows.Forms.DataGridViewTextBoxColumn ob;
        private System.Windows.Forms.DataGridViewTextBoxColumn alt;
        private System.Windows.Forms.DataGridViewTextBoxColumn anle;
        private System.Windows.Forms.DataGridViewTextBoxColumn samplenum;
        private System.Windows.Forms.DataGridViewTextBoxColumn celldistance;
        private System.Windows.Forms.DataGridViewTextBoxColumn cellDiff5Ang;
        private System.Windows.Forms.DataGridViewTextBoxColumn cellDiff3Ang;
        private System.Windows.Forms.DataGridViewTextBoxColumn rsrpdiffmax;
        private System.Windows.Forms.DataGridViewTextBoxColumn rsrpdiffangle;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn12;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn13;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn14;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn15;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn16;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn17;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn18;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn19;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn20;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn21;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn22;
        private System.Windows.Forms.DataGridViewTextBoxColumn SN;
        private System.Windows.Forms.DataGridViewTextBoxColumn city;
        private System.Windows.Forms.DataGridViewTextBoxColumn grid;
        private System.Windows.Forms.DataGridViewTextBoxColumn cellname;
        private System.Windows.Forms.DataGridViewTextBoxColumn cellcgi;
        private System.Windows.Forms.DataGridViewTextBoxColumn celltac;
        private System.Windows.Forms.DataGridViewTextBoxColumn celleci;
        private System.Windows.Forms.DataGridViewTextBoxColumn cellearfcn;
        private System.Windows.Forms.DataGridViewTextBoxColumn cellpci;
        private System.Windows.Forms.DataGridViewTextBoxColumn cellband;
        private System.Windows.Forms.DataGridViewTextBoxColumn cellInOutDoor;
        private System.Windows.Forms.DataGridViewTextBoxColumn cellDir;
        private System.Windows.Forms.DataGridViewTextBoxColumn cellob;
        private System.Windows.Forms.DataGridViewTextBoxColumn cellalt;
        private System.Windows.Forms.DataGridViewTextBoxColumn cellsamplenum;
        private System.Windows.Forms.DataGridViewTextBoxColumn celldist;
        private System.Windows.Forms.DataGridViewTextBoxColumn reslut;
        private System.Windows.Forms.DataGridViewTextBoxColumn warnstat;
        private System.Windows.Forms.DataGridViewTextBoxColumn celldiff10Angle;
        private System.Windows.Forms.DataGridViewTextBoxColumn celldiff5Angle;
        private System.Windows.Forms.DataGridViewTextBoxColumn celldiff3Angle;
        private System.Windows.Forms.DataGridViewTextBoxColumn maincelldifff9Angle;
        private System.Windows.Forms.DataGridViewTextBoxColumn maincelldifff5Angle;
        private System.Windows.Forms.DataGridViewTextBoxColumn maincelldiff3Angle;
        private System.Windows.Forms.DataGridViewTextBoxColumn cellrsrpdiffmax;
        private System.Windows.Forms.DataGridViewTextBoxColumn cellrsrpangelmax;
        private System.Windows.Forms.DataGridViewTextBoxColumn cellmainrsrpdiffmax;
        private System.Windows.Forms.DataGridViewTextBoxColumn cellrsrpanglemax;
        private System.Windows.Forms.DataGridViewTextBoxColumn cellcoverrate;
        private System.Windows.Forms.DataGridViewTextBoxColumn r0coverrate;
        private System.Windows.Forms.DataGridViewTextBoxColumn r1coverrate;
        private System.Windows.Forms.DataGridViewTextBoxColumn r0avg;
        private System.Windows.Forms.DataGridViewTextBoxColumn r1avg;
        private System.Windows.Forms.DataGridViewTextBoxColumn rsrpdiff;
        private System.Windows.Forms.DataGridViewTextBoxColumn rsrpstability;
        private System.Windows.Forms.DataGridViewTextBoxColumn s0avg;
        private System.Windows.Forms.DataGridViewTextBoxColumn s1avg;
        private System.Windows.Forms.DataGridViewTextBoxColumn sinrdiff;
        private System.Windows.Forms.DataGridViewTextBoxColumn sinrstability;
        private System.Windows.Forms.Button btnNextpage;
        private System.Windows.Forms.Button btnPrevpage;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.TextBox txtPage;
        private System.Windows.Forms.Label labPage;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.Button btnGo;
        private System.Windows.Forms.Label labNum;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Button btnSearch;
        private System.Windows.Forms.TextBox txtCellName;
        private System.Windows.Forms.Label label1;
    }
}