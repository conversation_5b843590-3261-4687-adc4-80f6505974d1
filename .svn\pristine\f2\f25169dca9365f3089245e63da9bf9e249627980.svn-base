﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class TestPointLacGrid
    {
        public double Longitude
        {
            get;
            set;
        }
        public double Latitude
        {
            get;
            set;
        }
        public int SN
        {
            get;
            set;
        }
        public TestPointLacGrid()
        {
 
        }
      
        private int testPointCount = 0;
        public int TestPointCount
        {
            get { return testPointCount; }
        }
        private readonly List<LacInfo> lacInfos = new List<LacInfo>();
        public void AddTestPointLacValue(int lac)
        {
            if (0 <= lac && lac <= 65535)
            {
                testPointCount++;
                bool isContains = false;
                foreach (LacInfo item in lacInfos)
                {
                    if (item.Lac==lac)
                    {
                        item.TestPointCount++;
                        isContains = true;
                        break;
                    }
                }
                if (!isContains)
                {
                    LacInfo lacInfo = new LacInfo(lac);
                    lacInfos.Add(lacInfo);
                }
            }
        }
    }

    public class LacInfo
    {
        private readonly int lac;
        public int Lac
        {
            get { return lac; }
        }
        public int TestPointCount
        {
            get;
            set;
        }
        public LacInfo(int lac)
        {
            this.lac = lac;
            TestPointCount++;
        }
    }
}
