﻿namespace MasterCom.RAMS.Func
{
    partial class GridColorModeItemSelDlg_Diff
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.btnOK = new System.Windows.Forms.Button();
            this.btnCancel = new System.Windows.Forms.Button();
            this.gbxStatPeriod = new System.Windows.Forms.GroupBox();
            this.label2 = new System.Windows.Forms.Label();
            this.dateTimePickerEndTime = new System.Windows.Forms.DateTimePicker();
            this.dateTimePickerBeginTime = new System.Windows.Forms.DateTimePicker();
            this.gbxCompTime = new System.Windows.Forms.GroupBox();
            this.label1 = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.dateTimePickerCEndTime = new System.Windows.Forms.DateTimePicker();
            this.dateTimePickerCBeginTime = new System.Windows.Forms.DateTimePicker();
            this.chbContrast = new System.Windows.Forms.CheckBox();
            this.gbxParamSel = new System.Windows.Forms.GroupBox();
            this.lvGridColorModes = new System.Windows.Forms.ListView();
            this.lbProjCount = new System.Windows.Forms.Label();
            this.btnGridColorModes = new System.Windows.Forms.Button();
            this.dpdGridColorModes = new System.Windows.Forms.ToolStripDropDown();
            this.btnModeEdit = new System.Windows.Forms.Button();
            this.gbxStatPeriod.SuspendLayout();
            this.gbxCompTime.SuspendLayout();
            this.gbxParamSel.SuspendLayout();
            this.SuspendLayout();
            // 
            // btnOK
            // 
            this.btnOK.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.btnOK.Location = new System.Drawing.Point(220, 242);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 0;
            this.btnOK.Text = "确定";
            this.btnOK.UseVisualStyleBackColor = true;
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // btnCancel
            // 
            this.btnCancel.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnCancel.Location = new System.Drawing.Point(308, 242);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 1;
            this.btnCancel.Text = "取消";
            this.btnCancel.UseVisualStyleBackColor = true;
            this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
            // 
            // gbxStatPeriod
            // 
            this.gbxStatPeriod.Controls.Add(this.label2);
            this.gbxStatPeriod.Controls.Add(this.dateTimePickerEndTime);
            this.gbxStatPeriod.Controls.Add(this.dateTimePickerBeginTime);
            this.gbxStatPeriod.Location = new System.Drawing.Point(12, 8);
            this.gbxStatPeriod.Name = "gbxStatPeriod";
            this.gbxStatPeriod.Size = new System.Drawing.Size(362, 53);
            this.gbxStatPeriod.TabIndex = 3;
            this.gbxStatPeriod.TabStop = false;
            this.gbxStatPeriod.Text = "统计时段";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(173, 24);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(17, 12);
            this.label2.TabIndex = 1;
            this.label2.Text = "至";
            // 
            // dateTimePickerEndTime
            // 
            this.dateTimePickerEndTime.Location = new System.Drawing.Point(196, 20);
            this.dateTimePickerEndTime.Name = "dateTimePickerEndTime";
            this.dateTimePickerEndTime.Size = new System.Drawing.Size(143, 21);
            this.dateTimePickerEndTime.TabIndex = 1;
            // 
            // dateTimePickerBeginTime
            // 
            this.dateTimePickerBeginTime.Location = new System.Drawing.Point(24, 20);
            this.dateTimePickerBeginTime.Name = "dateTimePickerBeginTime";
            this.dateTimePickerBeginTime.Size = new System.Drawing.Size(143, 21);
            this.dateTimePickerBeginTime.TabIndex = 0;
            // 
            // gbxCompTime
            // 
            this.gbxCompTime.Controls.Add(this.label1);
            this.gbxCompTime.Controls.Add(this.label3);
            this.gbxCompTime.Controls.Add(this.dateTimePickerCEndTime);
            this.gbxCompTime.Controls.Add(this.dateTimePickerCBeginTime);
            this.gbxCompTime.Location = new System.Drawing.Point(12, 67);
            this.gbxCompTime.Name = "gbxCompTime";
            this.gbxCompTime.Size = new System.Drawing.Size(362, 56);
            this.gbxCompTime.TabIndex = 4;
            this.gbxCompTime.TabStop = false;
            this.gbxCompTime.Text = "对比时段";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.ForeColor = System.Drawing.SystemColors.ActiveCaption;
            this.label1.Location = new System.Drawing.Point(183, 0);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(173, 12);
            this.label1.TabIndex = 2;
            this.label1.Text = "对比算法：统计时段减对比时段";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(173, 24);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(17, 12);
            this.label3.TabIndex = 1;
            this.label3.Text = "至";
            // 
            // dateTimePickerCEndTime
            // 
            this.dateTimePickerCEndTime.Enabled = false;
            this.dateTimePickerCEndTime.Location = new System.Drawing.Point(196, 20);
            this.dateTimePickerCEndTime.Name = "dateTimePickerCEndTime";
            this.dateTimePickerCEndTime.Size = new System.Drawing.Size(143, 21);
            this.dateTimePickerCEndTime.TabIndex = 1;
            // 
            // dateTimePickerCBeginTime
            // 
            this.dateTimePickerCBeginTime.Enabled = false;
            this.dateTimePickerCBeginTime.Location = new System.Drawing.Point(24, 20);
            this.dateTimePickerCBeginTime.Name = "dateTimePickerCBeginTime";
            this.dateTimePickerCBeginTime.Size = new System.Drawing.Size(143, 21);
            this.dateTimePickerCBeginTime.TabIndex = 0;
            // 
            // chbContrast
            // 
            this.chbContrast.AutoSize = true;
            this.chbContrast.ForeColor = System.Drawing.SystemColors.ActiveCaption;
            this.chbContrast.Location = new System.Drawing.Point(259, 5);
            this.chbContrast.Name = "chbContrast";
            this.chbContrast.Size = new System.Drawing.Size(96, 16);
            this.chbContrast.TabIndex = 2;
            this.chbContrast.Text = "显示指标变化";
            this.chbContrast.UseVisualStyleBackColor = true;
            this.chbContrast.Visible = false;
            this.chbContrast.CheckedChanged += new System.EventHandler(this.chbContrast_CheckedChanged);
            // 
            // gbxParamSel
            // 
            this.gbxParamSel.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.gbxParamSel.Controls.Add(this.lvGridColorModes);
            this.gbxParamSel.Controls.Add(this.lbProjCount);
            this.gbxParamSel.Controls.Add(this.btnGridColorModes);
            this.gbxParamSel.Location = new System.Drawing.Point(21, 136);
            this.gbxParamSel.Name = "gbxParamSel";
            this.gbxParamSel.Size = new System.Drawing.Size(362, 100);
            this.gbxParamSel.TabIndex = 5;
            this.gbxParamSel.TabStop = false;
            this.gbxParamSel.Text = "请选择栅格指标";
            // 
            // lvGridColorModes
            // 
            this.lvGridColorModes.Dock = System.Windows.Forms.DockStyle.Fill;
            this.lvGridColorModes.HeaderStyle = System.Windows.Forms.ColumnHeaderStyle.None;
            this.lvGridColorModes.Location = new System.Drawing.Point(3, 17);
            this.lvGridColorModes.Name = "lvGridColorModes";
            this.lvGridColorModes.Size = new System.Drawing.Size(356, 80);
            this.lvGridColorModes.TabIndex = 1;
            this.lvGridColorModes.UseCompatibleStateImageBehavior = false;
            this.lvGridColorModes.View = System.Windows.Forms.View.List;
            // 
            // lbProjCount
            // 
            this.lbProjCount.AutoSize = true;
            this.lbProjCount.Font = new System.Drawing.Font("宋体", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.lbProjCount.ForeColor = System.Drawing.SystemColors.ActiveBorder;
            this.lbProjCount.Location = new System.Drawing.Point(93, 1);
            this.lbProjCount.Name = "lbProjCount";
            this.lbProjCount.Size = new System.Drawing.Size(23, 11);
            this.lbProjCount.TabIndex = 7;
            this.lbProjCount.Text = "[0]";
            // 
            // btnGridColorModes
            // 
            this.btnGridColorModes.BackColor = System.Drawing.SystemColors.Control;
            this.btnGridColorModes.FlatAppearance.BorderSize = 0;
            this.btnGridColorModes.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnGridColorModes.Location = new System.Drawing.Point(268, -3);
            this.btnGridColorModes.Name = "btnGridColorModes";
            this.btnGridColorModes.Size = new System.Drawing.Size(71, 20);
            this.btnGridColorModes.TabIndex = 0;
            this.btnGridColorModes.Text = "请选择↓";
            this.btnGridColorModes.UseVisualStyleBackColor = true;
            this.btnGridColorModes.Click += new System.EventHandler(this.btnGridColorModes_Click);
            // 
            // dpdGridColorModes
            // 
            this.dpdGridColorModes.LayoutStyle = System.Windows.Forms.ToolStripLayoutStyle.Flow;
            this.dpdGridColorModes.Name = "dpdGridColorModes";
            this.dpdGridColorModes.Size = new System.Drawing.Size(2, 4);
            // 
            // btnModeEdit
            // 
            this.btnModeEdit.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.btnModeEdit.Location = new System.Drawing.Point(120, 242);
            this.btnModeEdit.Name = "btnModeEdit";
            this.btnModeEdit.Size = new System.Drawing.Size(84, 23);
            this.btnModeEdit.TabIndex = 4;
            this.btnModeEdit.Text = "栅格KPI管理";
            this.btnModeEdit.UseVisualStyleBackColor = true;
            this.btnModeEdit.Click += new System.EventHandler(this.btnModeEdit_Click);
            // 
            // GridColorModeItemSelDlg_Diff
            // 
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.ClientSize = new System.Drawing.Size(396, 277);
            this.Controls.Add(this.chbContrast);
            this.Controls.Add(this.gbxParamSel);
            this.Controls.Add(this.gbxCompTime);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.Controls.Add(this.btnModeEdit);
            this.Controls.Add(this.gbxStatPeriod);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedToolWindow;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "GridColorModeItemSelDlg_Diff";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "栅格显示指标选择";
            this.gbxStatPeriod.ResumeLayout(false);
            this.gbxStatPeriod.PerformLayout();
            this.gbxCompTime.ResumeLayout(false);
            this.gbxCompTime.PerformLayout();
            this.gbxParamSel.ResumeLayout(false);
            this.gbxParamSel.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.Button btnOK;
        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.GroupBox gbxStatPeriod;
        private System.Windows.Forms.GroupBox gbxCompTime;
        private System.Windows.Forms.CheckBox chbContrast;
        private System.Windows.Forms.DateTimePicker dateTimePickerBeginTime;
        private System.Windows.Forms.DateTimePicker dateTimePickerEndTime;
        private System.Windows.Forms.DateTimePicker dateTimePickerCEndTime;
        private System.Windows.Forms.DateTimePicker dateTimePickerCBeginTime;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.GroupBox gbxParamSel;
        private System.Windows.Forms.Button btnGridColorModes;
        private System.Windows.Forms.ToolStripDropDown dpdGridColorModes;
        private System.Windows.Forms.Label lbProjCount;
        private System.Windows.Forms.ListView lvGridColorModes;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Button btnModeEdit;
    }
}