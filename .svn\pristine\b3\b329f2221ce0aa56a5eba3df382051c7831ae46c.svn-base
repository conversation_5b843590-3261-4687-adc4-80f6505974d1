using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.Util;
using System.Collections.ObjectModel;

namespace MasterCom.RAMS.Model
{
    public class WMGW 
    {
        public WMGW(string name)
        {
            Name = name;
        }

        public string Name { get; set; }

        public ReadOnlyCollection<WRNC> RNCs
        {
            get { return rncs.AsReadOnly(); }
        }

        public List<WRNC> RncsForConfig
        {
            get { return rncs; }
            set
            {
                rncs = value;
                if (rncs != null)
                {
                    foreach (WRNC wrnc in rncs)
                    {
                        nameRNCMap.Add(wrnc.Name, wrnc);
                    }
                }
            }
        }

        public WRNC AddRNC(string rncName)
        {
            if (!nameRNCMap.ContainsKey(rncName))
            {
                WRNC rnc = new WRNC(rncName);
                rncs.Add(rnc);
                nameRNCMap[rncName] = rnc;
                rnc.BelongMGW = this;
                return rnc;
            }
            return nameRNCMap[rncName];
        }

        private List<WRNC> rncs = new List<WRNC>();

        private readonly Dictionary<string, WRNC> nameRNCMap = new Dictionary<string, WRNC>();

        public static IComparer<WMGW> GetCompareByName()
        {
            if (comparerByName == null)
            {
                comparerByName = new ComparerByName();
            }
            return comparerByName;
        }

        private static IComparer<WMGW> comparerByName;

        public class ComparerByName : IComparer<WMGW>
        {
            public int Compare(WMGW x, WMGW y)
            {
                return x.Name.CompareTo(y.Name);
            }
        }
    }
}