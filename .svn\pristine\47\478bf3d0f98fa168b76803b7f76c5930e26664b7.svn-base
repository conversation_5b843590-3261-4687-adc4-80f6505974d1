﻿using MasterCom.MTGis;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.NOP
{
    public class TaskGisInfoQuery : DIYSQLBase
    {
        private readonly DbGisId gisIdInfo;
        
        public List<DbPoint> DbPointList { get; set; } = new List<DbPoint>();

        public TaskGisInfoQuery(DbGisId gisIdInfo)
            : base(MainModel.GetInstance())
        {
            this.gisIdInfo = gisIdInfo;
        }
        public override string Name
        {
            get { return "TaskGisInfoQuery"; }
        }
        protected override string getSqlTextString()
        {
            NopDBSetting nopDBSetting = NopDbManager.Instance.NopDbSetting;
            if (nopDBSetting != null && !string.IsNullOrEmpty(nopDBSetting.DbIpWithPort))
            {
                return string.Format(@"select 图形 from [{0}].MTNOH_GIS.dbo.TB_OVERLAY_图层图形 
 where 类型ID = {1} and id = {2}", nopDBSetting.DbIpWithPort, gisIdInfo.Type, gisIdInfo.Id);
            }
            else
            {
                return string.Format(@"select 图形 from MTNOH_GIS.dbo.TB_OVERLAY_图层图形 
 where 类型ID = {0} and id = {1}", gisIdInfo.Type, gisIdInfo.Id);
            }
        }
        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] vType = new E_VType[1];
            vType[0] = E_VType.E_ImgString;
            return vType;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            int index = 0;
            int progress = 0;
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    fillData(package);
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }

                setProgressPercent(ref index, ref progress);
            }
        }

        private void fillData(Package package)
        {
            byte[] bytes = package.Content.GetParamBytes();
            if (bytes != null)
            {
                int count = bytes.Length / 8;
                for (int i = 0; i < count; i++)
                {
                    double lng = BitConverter.ToInt32(bytes, i * 8) / 10000000.0;
                    double lat = BitConverter.ToInt32(bytes, i * 8 + 4) / 10000000.0;

                    DbPoint pnt = new DbPoint(lng, lat);
                    DbPointList.Add(pnt);
                }
            }
        }
    }

    public class DbGisId
    {
        public int Id { get; set; }
        public int Type { get; set; }

        public static List<DbGisId> GetGisInfosByJsonData(string jsonData)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<List<DbGisId>>(jsonData); 
        }
    }
}
