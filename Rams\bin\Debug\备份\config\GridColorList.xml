<?xml version="1.0"?>
<Configs>
  <Config name="GridColorItems">
    <Item name="GridColorModeItem" typeName="IList">
      <Item typeName="IDictionary">
        <Item typeName="String" key="Type">GSM栅格指标</Item>
        <Item typeName="String" key="Name">GSM语音场强最大</Item>
        <Item typeName="Single" key="MinR">-120</Item>
        <Item typeName="Single" key="MaxR">-10</Item>
        <Item typeName="String" key="Formula">Mx_640103</Item>
        <Item typeName="IList" key="ColorRanges">
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-120</Item>
            <Item typeName="Single" key="MaxV">-100</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">0</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item typeName="String" key="desInfo">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-100</Item>
            <Item typeName="Single" key="MaxV">-90</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">102</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-90</Item>
            <Item typeName="Single" key="MaxV">-80</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">204</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-80</Item>
            <Item typeName="Single" key="MaxV">-70</Item>
            <Item typeName="Int32" key="ColorR">203</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-70</Item>
            <Item typeName="Single" key="MaxV">-60</Item>
            <Item typeName="Int32" key="ColorR">101</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-60</Item>
            <Item typeName="Single" key="MaxV">-10</Item>
            <Item typeName="Int32" key="ColorR">0</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">255</Item>
            <Item key="desInfo" />
          </Item>
        </Item>
        <Item typeName="Boolean" key="RangeMode">True</Item>
        <Item typeName="Single" key="MinCompareR">0</Item>
        <Item typeName="Single" key="MaxCompareR">0</Item>
        <Item typeName="IList" key="CombinedColorRanges" />
        <Item typeName="Int32" key="CombineValidCount">1</Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Type">GSM栅格指标</Item>
        <Item typeName="String" key="Name">GSM语音场强最小</Item>
        <Item typeName="Single" key="MinR">-120</Item>
        <Item typeName="Single" key="MaxR">-10</Item>
        <Item typeName="String" key="Formula">Mx_640104</Item>
        <Item typeName="IList" key="ColorRanges">
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-120</Item>
            <Item typeName="Single" key="MaxV">-100</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">0</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-100</Item>
            <Item typeName="Single" key="MaxV">-90</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">102</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-90</Item>
            <Item typeName="Single" key="MaxV">-80</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">204</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-80</Item>
            <Item typeName="Single" key="MaxV">-70</Item>
            <Item typeName="Int32" key="ColorR">203</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-70</Item>
            <Item typeName="Single" key="MaxV">-60</Item>
            <Item typeName="Int32" key="ColorR">101</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-60</Item>
            <Item typeName="Single" key="MaxV">-10</Item>
            <Item typeName="Int32" key="ColorR">0</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">255</Item>
            <Item key="desInfo" />
          </Item>
        </Item>
        <Item typeName="Boolean" key="RangeMode">True</Item>
        <Item typeName="Single" key="MinCompareR">0</Item>
        <Item typeName="Single" key="MaxCompareR">0</Item>
        <Item typeName="IList" key="CombinedColorRanges" />
        <Item typeName="Int32" key="CombineValidCount">1</Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Type">GSM栅格指标</Item>
        <Item typeName="String" key="Name">GSM语音场强平均</Item>
        <Item typeName="Single" key="MinR">-120</Item>
        <Item typeName="Single" key="MaxR">-10</Item>
        <Item typeName="String" key="Formula">Mx_640102</Item>
        <Item typeName="IList" key="ColorRanges">
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-120</Item>
            <Item typeName="Single" key="MaxV">-100</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">0</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-100</Item>
            <Item typeName="Single" key="MaxV">-90</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">102</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-90</Item>
            <Item typeName="Single" key="MaxV">-80</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">204</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-80</Item>
            <Item typeName="Single" key="MaxV">-70</Item>
            <Item typeName="Int32" key="ColorR">0</Item>
            <Item typeName="Int32" key="ColorG">0</Item>
            <Item typeName="Int32" key="ColorB">255</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-70</Item>
            <Item typeName="Single" key="MaxV">-60</Item>
            <Item typeName="Int32" key="ColorR">101</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-60</Item>
            <Item typeName="Single" key="MaxV">-10</Item>
            <Item typeName="Int32" key="ColorR">0</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">255</Item>
            <Item key="desInfo" />
          </Item>
        </Item>
        <Item typeName="Boolean" key="RangeMode">True</Item>
        <Item typeName="Single" key="MinCompareR">0</Item>
        <Item typeName="Single" key="MaxCompareR">0</Item>
        <Item typeName="IList" key="CombinedColorRanges">
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-120</Item>
            <Item typeName="Single" key="MaxV">-70</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">0</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item typeName="String" key="desInfo">差</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-70</Item>
            <Item typeName="Single" key="MaxV">-50</Item>
            <Item typeName="Int32" key="ColorR">169</Item>
            <Item typeName="Int32" key="ColorG">85</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item typeName="String" key="desInfo">不错</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-50</Item>
            <Item typeName="Single" key="MaxV">-10</Item>
            <Item typeName="Int32" key="ColorR">0</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item typeName="String" key="desInfo">好</Item>
          </Item>
        </Item>
        <Item typeName="Int32" key="CombineValidCount">3</Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Type">GSM栅格指标</Item>
        <Item typeName="String" key="Name">GSM信号质量最大值</Item>
        <Item typeName="Single" key="MinR">0</Item>
        <Item typeName="Single" key="MaxR">7</Item>
        <Item typeName="String" key="Formula">Mx_5A010509</Item>
        <Item typeName="IList" key="ColorRanges">
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">0</Item>
            <Item typeName="Single" key="MaxV">3</Item>
            <Item typeName="Int32" key="ColorR">0</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">3</Item>
            <Item typeName="Single" key="MaxV">5</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">5</Item>
            <Item typeName="Single" key="MaxV">7</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">0</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
        </Item>
        <Item typeName="Boolean" key="RangeMode">True</Item>
        <Item typeName="Single" key="MinCompareR">0</Item>
        <Item typeName="Single" key="MaxCompareR">0</Item>
        <Item typeName="IList" key="CombinedColorRanges" />
        <Item typeName="Int32" key="CombineValidCount">1</Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Type">GSM栅格指标</Item>
        <Item typeName="String" key="Name">GSM信号质量最小值</Item>
        <Item typeName="Single" key="MinR">0</Item>
        <Item typeName="Single" key="MaxR">7</Item>
        <Item typeName="String" key="Formula">Mx_5A01050A</Item>
        <Item typeName="IList" key="ColorRanges">
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">0</Item>
            <Item typeName="Single" key="MaxV">3</Item>
            <Item typeName="Int32" key="ColorR">0</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">3</Item>
            <Item typeName="Single" key="MaxV">5</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">5</Item>
            <Item typeName="Single" key="MaxV">7</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">0</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
        </Item>
        <Item typeName="Boolean" key="RangeMode">True</Item>
        <Item typeName="Single" key="MinCompareR">0</Item>
        <Item typeName="Single" key="MaxCompareR">0</Item>
        <Item typeName="IList" key="CombinedColorRanges" />
        <Item typeName="Int32" key="CombineValidCount">1</Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Type">GSM栅格指标</Item>
        <Item typeName="String" key="Name">GSM信号质量平均值</Item>
        <Item typeName="Single" key="MinR">0</Item>
        <Item typeName="Single" key="MaxR">7</Item>
        <Item typeName="String" key="Formula">Mx_5A01050B</Item>
        <Item typeName="IList" key="ColorRanges">
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">0</Item>
            <Item typeName="Single" key="MaxV">3</Item>
            <Item typeName="Int32" key="ColorR">0</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">3</Item>
            <Item typeName="Single" key="MaxV">5</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">5</Item>
            <Item typeName="Single" key="MaxV">7</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">0</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
        </Item>
        <Item typeName="Boolean" key="RangeMode">True</Item>
        <Item typeName="Single" key="MinCompareR">0</Item>
        <Item typeName="Single" key="MaxCompareR">0</Item>
        <Item typeName="IList" key="CombinedColorRanges" />
        <Item typeName="Int32" key="CombineValidCount">1</Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Type">GSM栅格指标</Item>
        <Item typeName="String" key="Name">GSM_PESQ最大</Item>
        <Item typeName="Single" key="MinR">1</Item>
        <Item typeName="Single" key="MaxR">5</Item>
        <Item typeName="String" key="Formula">Mx_5A010B51</Item>
        <Item typeName="IList" key="ColorRanges">
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">1</Item>
            <Item typeName="Single" key="MaxV">1.8</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">0</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">1.8</Item>
            <Item typeName="Single" key="MaxV">2.6</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">128</Item>
            <Item typeName="Int32" key="ColorB">64</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">2.6</Item>
            <Item typeName="Single" key="MaxV">3.4</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">3.4</Item>
            <Item typeName="Single" key="MaxV">4.2</Item>
            <Item typeName="Int32" key="ColorR">0</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">4.2</Item>
            <Item typeName="Single" key="MaxV">5</Item>
            <Item typeName="Int32" key="ColorR">0</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">255</Item>
            <Item key="desInfo" />
          </Item>
        </Item>
        <Item typeName="Boolean" key="RangeMode">True</Item>
        <Item typeName="Single" key="MinCompareR">0</Item>
        <Item typeName="Single" key="MaxCompareR">0</Item>
        <Item typeName="IList" key="CombinedColorRanges" />
        <Item typeName="Int32" key="CombineValidCount">1</Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Type">GSM栅格指标</Item>
        <Item typeName="String" key="Name">GSM_PESQ最小</Item>
        <Item typeName="Single" key="MinR">1</Item>
        <Item typeName="Single" key="MaxR">5</Item>
        <Item typeName="String" key="Formula">Mx_5A010B52</Item>
        <Item typeName="IList" key="ColorRanges">
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">1</Item>
            <Item typeName="Single" key="MaxV">1.8</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">0</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">1.8</Item>
            <Item typeName="Single" key="MaxV">2.6</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">128</Item>
            <Item typeName="Int32" key="ColorB">64</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">2.6</Item>
            <Item typeName="Single" key="MaxV">3.4</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">3.4</Item>
            <Item typeName="Single" key="MaxV">4.2</Item>
            <Item typeName="Int32" key="ColorR">0</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">4.2</Item>
            <Item typeName="Single" key="MaxV">5</Item>
            <Item typeName="Int32" key="ColorR">0</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">255</Item>
            <Item key="desInfo" />
          </Item>
        </Item>
        <Item typeName="Boolean" key="RangeMode">True</Item>
        <Item typeName="Single" key="MinCompareR">0</Item>
        <Item typeName="Single" key="MaxCompareR">0</Item>
        <Item typeName="IList" key="CombinedColorRanges" />
        <Item typeName="Int32" key="CombineValidCount">1</Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Type">GSM栅格指标</Item>
        <Item typeName="String" key="Name">GSM_PESQ平均</Item>
        <Item typeName="Single" key="MinR">1</Item>
        <Item typeName="Single" key="MaxR">5</Item>
        <Item typeName="String" key="Formula">Mx_5A010B53</Item>
        <Item typeName="IList" key="ColorRanges">
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">1</Item>
            <Item typeName="Single" key="MaxV">1.8</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">0</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">1.8</Item>
            <Item typeName="Single" key="MaxV">2.6</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">128</Item>
            <Item typeName="Int32" key="ColorB">64</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">2.6</Item>
            <Item typeName="Single" key="MaxV">3.4</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">3.4</Item>
            <Item typeName="Single" key="MaxV">4.2</Item>
            <Item typeName="Int32" key="ColorR">0</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">4.2</Item>
            <Item typeName="Single" key="MaxV">5</Item>
            <Item typeName="Int32" key="ColorR">0</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">255</Item>
            <Item key="desInfo" />
          </Item>
        </Item>
        <Item typeName="Boolean" key="RangeMode">True</Item>
        <Item typeName="Single" key="MinCompareR">0</Item>
        <Item typeName="Single" key="MaxCompareR">0</Item>
        <Item typeName="IList" key="CombinedColorRanges" />
        <Item typeName="Int32" key="CombineValidCount">1</Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Type">GSM扫频栅格指标</Item>
        <Item typeName="String" key="Name">GSM扫频场强平均</Item>
        <Item typeName="Single" key="MinR">-120</Item>
        <Item typeName="Single" key="MaxR">-10</Item>
        <Item typeName="String" key="Formula">Gc_5F0C0102</Item>
        <Item typeName="IList" key="ColorRanges">
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-120</Item>
            <Item typeName="Single" key="MaxV">-100</Item>
            <Item typeName="Int32" key="ColorR">0</Item>
            <Item typeName="Int32" key="ColorG">0</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-100</Item>
            <Item typeName="Single" key="MaxV">-90</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">0</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-90</Item>
            <Item typeName="Single" key="MaxV">-80</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-80</Item>
            <Item typeName="Single" key="MaxV">-70</Item>
            <Item typeName="Int32" key="ColorR">0</Item>
            <Item typeName="Int32" key="ColorG">0</Item>
            <Item typeName="Int32" key="ColorB">255</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-70</Item>
            <Item typeName="Single" key="MaxV">-60</Item>
            <Item typeName="Int32" key="ColorR">101</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-60</Item>
            <Item typeName="Single" key="MaxV">-10</Item>
            <Item typeName="Int32" key="ColorR">0</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">255</Item>
            <Item key="desInfo" />
          </Item>
        </Item>
        <Item typeName="Boolean" key="RangeMode">True</Item>
        <Item typeName="Single" key="MinCompareR">0</Item>
        <Item typeName="Single" key="MaxCompareR">0</Item>
        <Item typeName="IList" key="CombinedColorRanges" />
        <Item typeName="Int32" key="CombineValidCount">1</Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Type">GSM栅格指标</Item>
        <Item typeName="String" key="Name">GSM_TA平均</Item>
        <Item typeName="Single" key="MinR">0</Item>
        <Item typeName="Single" key="MaxR">63</Item>
        <Item typeName="String" key="Formula">Mx_67010B</Item>
        <Item typeName="IList" key="ColorRanges">
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">0</Item>
            <Item typeName="Single" key="MaxV">2</Item>
            <Item typeName="Int32" key="ColorR">0</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">2</Item>
            <Item typeName="Single" key="MaxV">5</Item>
            <Item typeName="Int32" key="ColorR">128</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">128</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">5</Item>
            <Item typeName="Single" key="MaxV">10</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">10</Item>
            <Item typeName="Single" key="MaxV">20</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">128</Item>
            <Item typeName="Int32" key="ColorB">64</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">20</Item>
            <Item typeName="Single" key="MaxV">63</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">0</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
        </Item>
        <Item typeName="Boolean" key="RangeMode">True</Item>
        <Item typeName="Single" key="MinCompareR">0</Item>
        <Item typeName="Single" key="MaxCompareR">0</Item>
        <Item typeName="IList" key="CombinedColorRanges" />
        <Item typeName="Int32" key="CombineValidCount">1</Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Type">GSM栅格指标</Item>
        <Item typeName="String" key="Name">GPRSEDGE_下载平均时隙数</Item>
        <Item typeName="Single" key="MinR">1</Item>
        <Item typeName="Single" key="MaxR">40</Item>
        <Item typeName="String" key="Formula">10*(Mx_06020401+Mx_06020801+2*(Mx_06020402+Mx_06020802)+3*(Mx_06020403+Mx_06020803)+4*(Mx_06020404+Mx_06020804))/(Mx_06020401+Mx_06020402+Mx_06020403+Mx_06020404+Mx_06020801+Mx_06020802+Mx_06020803+Mx_06020804)</Item>
        <Item typeName="IList" key="ColorRanges">
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">1</Item>
            <Item typeName="Single" key="MaxV">10</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">0</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">10</Item>
            <Item typeName="Single" key="MaxV">20</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">127</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">20</Item>
            <Item typeName="Single" key="MaxV">30</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">30</Item>
            <Item typeName="Single" key="MaxV">40</Item>
            <Item typeName="Int32" key="ColorR">127</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
        </Item>
        <Item typeName="Boolean" key="RangeMode">True</Item>
        <Item typeName="Single" key="MinCompareR">0</Item>
        <Item typeName="Single" key="MaxCompareR">0</Item>
        <Item typeName="IList" key="CombinedColorRanges" />
        <Item typeName="Int32" key="CombineValidCount">1</Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Type">TD栅格指标</Item>
        <Item typeName="String" key="Name">TD_PCCPCH_RSCP平均值</Item>
        <Item typeName="Single" key="MinR">-140</Item>
        <Item typeName="Single" key="MaxR">-10</Item>
        <Item typeName="String" key="Formula">Tx_5C04030D</Item>
        <Item typeName="IList" key="ColorRanges">
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-140</Item>
            <Item typeName="Single" key="MaxV">-105</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">0</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-105</Item>
            <Item typeName="Single" key="MaxV">-94</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">102</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-94</Item>
            <Item typeName="Single" key="MaxV">-85</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">204</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-85</Item>
            <Item typeName="Single" key="MaxV">-75</Item>
            <Item typeName="Int32" key="ColorR">203</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-75</Item>
            <Item typeName="Single" key="MaxV">-60</Item>
            <Item typeName="Int32" key="ColorR">101</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-60</Item>
            <Item typeName="Single" key="MaxV">-10</Item>
            <Item typeName="Int32" key="ColorR">0</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">255</Item>
            <Item key="desInfo" />
          </Item>
        </Item>
        <Item typeName="Boolean" key="RangeMode">True</Item>
        <Item typeName="Single" key="MinCompareR">0</Item>
        <Item typeName="Single" key="MaxCompareR">0</Item>
        <Item typeName="IList" key="CombinedColorRanges" />
        <Item typeName="Int32" key="CombineValidCount">1</Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Type">TD栅格指标</Item>
        <Item typeName="String" key="Name">TD-VP90覆盖</Item>
        <Item typeName="Single" key="MinR">0</Item>
        <Item typeName="Single" key="MaxR">100</Item>
        <Item typeName="String" key="Formula">100.0*(Tx_5C0D0301+Tx_5C0D0302+Tx_5C0D0303+Tx_5C0D0304+Tx_5C0D0305)/Tx_5C0D030A</Item>
        <Item typeName="IList" key="ColorRanges">
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">0</Item>
            <Item typeName="Single" key="MaxV">80</Item>
            <Item typeName="Int32" key="ColorR">0</Item>
            <Item typeName="Int32" key="ColorG">0</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">80</Item>
            <Item typeName="Single" key="MaxV">85</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">0</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">85</Item>
            <Item typeName="Single" key="MaxV">90</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">90</Item>
            <Item typeName="Single" key="MaxV">95</Item>
            <Item typeName="Int32" key="ColorR">0</Item>
            <Item typeName="Int32" key="ColorG">0</Item>
            <Item typeName="Int32" key="ColorB">255</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">95</Item>
            <Item typeName="Single" key="MaxV">100</Item>
            <Item typeName="Int32" key="ColorR">50</Item>
            <Item typeName="Int32" key="ColorG">204</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
        </Item>
        <Item typeName="Boolean" key="RangeMode">True</Item>
        <Item typeName="Single" key="MinCompareR">0</Item>
        <Item typeName="Single" key="MaxCompareR">0</Item>
        <Item typeName="IList" key="CombinedColorRanges" />
        <Item typeName="Int32" key="CombineValidCount">1</Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Type">TD栅格指标</Item>
        <Item typeName="String" key="Name">TD-VP95覆盖</Item>
        <Item typeName="Single" key="MinR">0</Item>
        <Item typeName="Single" key="MaxR">100</Item>
        <Item typeName="String" key="Formula">100.0*(Tx_5C0D0301+Tx_5C0D0302+Tx_5C0D0303+Tx_5C0D0304+Tx_5C0D0305+Tx_5C0D0306)/Tx_5C0D030A</Item>
        <Item typeName="IList" key="ColorRanges">
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">0</Item>
            <Item typeName="Single" key="MaxV">80</Item>
            <Item typeName="Int32" key="ColorR">0</Item>
            <Item typeName="Int32" key="ColorG">0</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">80</Item>
            <Item typeName="Single" key="MaxV">85</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">0</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">85</Item>
            <Item typeName="Single" key="MaxV">90</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">90</Item>
            <Item typeName="Single" key="MaxV">95</Item>
            <Item typeName="Int32" key="ColorR">0</Item>
            <Item typeName="Int32" key="ColorG">0</Item>
            <Item typeName="Int32" key="ColorB">255</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">95</Item>
            <Item typeName="Single" key="MaxV">100</Item>
            <Item typeName="Int32" key="ColorR">50</Item>
            <Item typeName="Int32" key="ColorG">204</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
        </Item>
        <Item typeName="Boolean" key="RangeMode">True</Item>
        <Item typeName="Single" key="MinCompareR">0</Item>
        <Item typeName="Single" key="MaxCompareR">0</Item>
        <Item typeName="IList" key="CombinedColorRanges" />
        <Item typeName="Int32" key="CombineValidCount">1</Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Type">TD栅格指标</Item>
        <Item typeName="String" key="Name">TD-AMR90覆盖</Item>
        <Item typeName="Single" key="MinR">0</Item>
        <Item typeName="Single" key="MaxR">100</Item>
        <Item typeName="String" key="Formula">100.0*(Tx_5C040301+Tx_5C040302+Tx_5C040303+Tx_5C040304+Tx_5C040305)/Tx_5C04030A</Item>
        <Item typeName="IList" key="ColorRanges">
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">0</Item>
            <Item typeName="Single" key="MaxV">80</Item>
            <Item typeName="Int32" key="ColorR">0</Item>
            <Item typeName="Int32" key="ColorG">0</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">80</Item>
            <Item typeName="Single" key="MaxV">85</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">0</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">85</Item>
            <Item typeName="Single" key="MaxV">90</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">90</Item>
            <Item typeName="Single" key="MaxV">95</Item>
            <Item typeName="Int32" key="ColorR">0</Item>
            <Item typeName="Int32" key="ColorG">0</Item>
            <Item typeName="Int32" key="ColorB">255</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">95</Item>
            <Item typeName="Single" key="MaxV">100</Item>
            <Item typeName="Int32" key="ColorR">50</Item>
            <Item typeName="Int32" key="ColorG">204</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
        </Item>
        <Item typeName="Boolean" key="RangeMode">True</Item>
        <Item typeName="Single" key="MinCompareR">0</Item>
        <Item typeName="Single" key="MaxCompareR">0</Item>
        <Item typeName="IList" key="CombinedColorRanges" />
        <Item typeName="Int32" key="CombineValidCount">1</Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Type">TD栅格指标</Item>
        <Item typeName="String" key="Name">TD-AMR95覆盖</Item>
        <Item typeName="Single" key="MinR">0</Item>
        <Item typeName="Single" key="MaxR">100</Item>
        <Item typeName="String" key="Formula">100.0*(Tx_5C040301+Tx_5C040302+Tx_5C040303+Tx_5C040304+Tx_5C040305+Tx_5C040306)/Tx_5C04030A</Item>
        <Item typeName="IList" key="ColorRanges">
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">0</Item>
            <Item typeName="Single" key="MaxV">80</Item>
            <Item typeName="Int32" key="ColorR">0</Item>
            <Item typeName="Int32" key="ColorG">0</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">80</Item>
            <Item typeName="Single" key="MaxV">85</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">0</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">85</Item>
            <Item typeName="Single" key="MaxV">90</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">90</Item>
            <Item typeName="Single" key="MaxV">95</Item>
            <Item typeName="Int32" key="ColorR">0</Item>
            <Item typeName="Int32" key="ColorG">0</Item>
            <Item typeName="Int32" key="ColorB">255</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">95</Item>
            <Item typeName="Single" key="MaxV">100</Item>
            <Item typeName="Int32" key="ColorR">50</Item>
            <Item typeName="Int32" key="ColorG">204</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
        </Item>
        <Item typeName="Boolean" key="RangeMode">True</Item>
        <Item typeName="Single" key="MinCompareR">0</Item>
        <Item typeName="Single" key="MaxCompareR">0</Item>
        <Item typeName="IList" key="CombinedColorRanges" />
        <Item typeName="Int32" key="CombineValidCount">1</Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Type">TD栅格指标</Item>
        <Item typeName="String" key="Name">TD_PCCPCH_RSCP最大值</Item>
        <Item typeName="Single" key="MinR">-140</Item>
        <Item typeName="Single" key="MaxR">-10</Item>
        <Item typeName="String" key="Formula">Tx_5C04030B</Item>
        <Item typeName="IList" key="ColorRanges">
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-140</Item>
            <Item typeName="Single" key="MaxV">-105</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">0</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-105</Item>
            <Item typeName="Single" key="MaxV">-94</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">102</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-94</Item>
            <Item typeName="Single" key="MaxV">-85</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">204</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-85</Item>
            <Item typeName="Single" key="MaxV">-75</Item>
            <Item typeName="Int32" key="ColorR">203</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-75</Item>
            <Item typeName="Single" key="MaxV">-60</Item>
            <Item typeName="Int32" key="ColorR">101</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-60</Item>
            <Item typeName="Single" key="MaxV">-10</Item>
            <Item typeName="Int32" key="ColorR">0</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">255</Item>
            <Item key="desInfo" />
          </Item>
        </Item>
        <Item typeName="Boolean" key="RangeMode">True</Item>
        <Item typeName="Single" key="MinCompareR">0</Item>
        <Item typeName="Single" key="MaxCompareR">0</Item>
        <Item typeName="IList" key="CombinedColorRanges" />
        <Item typeName="Int32" key="CombineValidCount">1</Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Type">TD栅格指标</Item>
        <Item typeName="String" key="Name">TD_PCCPCH_RSCP最小值</Item>
        <Item typeName="Single" key="MinR">-140</Item>
        <Item typeName="Single" key="MaxR">-10</Item>
        <Item typeName="String" key="Formula">Tx_5C04030C</Item>
        <Item typeName="IList" key="ColorRanges">
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-140</Item>
            <Item typeName="Single" key="MaxV">-105</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">0</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-105</Item>
            <Item typeName="Single" key="MaxV">-94</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">102</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-94</Item>
            <Item typeName="Single" key="MaxV">-85</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">204</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-85</Item>
            <Item typeName="Single" key="MaxV">-75</Item>
            <Item typeName="Int32" key="ColorR">203</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-75</Item>
            <Item typeName="Single" key="MaxV">-60</Item>
            <Item typeName="Int32" key="ColorR">101</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-60</Item>
            <Item typeName="Single" key="MaxV">-10</Item>
            <Item typeName="Int32" key="ColorR">0</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">255</Item>
            <Item key="desInfo" />
          </Item>
        </Item>
        <Item typeName="Boolean" key="RangeMode">True</Item>
        <Item typeName="Single" key="MinCompareR">0</Item>
        <Item typeName="Single" key="MaxCompareR">0</Item>
        <Item typeName="IList" key="CombinedColorRanges" />
        <Item typeName="Int32" key="CombineValidCount">1</Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Type">TD栅格指标</Item>
        <Item typeName="String" key="Name">TD_PCCPCH_C2I平均值</Item>
        <Item typeName="Single" key="MinR">-20</Item>
        <Item typeName="Single" key="MaxR">25</Item>
        <Item typeName="String" key="Formula">Tx_5C040316</Item>
        <Item typeName="IList" key="ColorRanges">
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-20</Item>
            <Item typeName="Single" key="MaxV">-11</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">0</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-11</Item>
            <Item typeName="Single" key="MaxV">-2</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">102</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-2</Item>
            <Item typeName="Single" key="MaxV">7</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">204</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">7</Item>
            <Item typeName="Single" key="MaxV">16</Item>
            <Item typeName="Int32" key="ColorR">203</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">16</Item>
            <Item typeName="Single" key="MaxV">25</Item>
            <Item typeName="Int32" key="ColorR">0</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">255</Item>
            <Item key="desInfo" />
          </Item>
        </Item>
        <Item typeName="Boolean" key="RangeMode">True</Item>
        <Item typeName="Single" key="MinCompareR">0</Item>
        <Item typeName="Single" key="MaxCompareR">0</Item>
        <Item typeName="IList" key="CombinedColorRanges" />
        <Item typeName="Int32" key="CombineValidCount">1</Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Type">TD栅格指标</Item>
        <Item typeName="String" key="Name">TD_PCCPCH_C2I最大值</Item>
        <Item typeName="Single" key="MinR">-20</Item>
        <Item typeName="Single" key="MaxR">25</Item>
        <Item typeName="String" key="Formula">Tx_5C040314</Item>
        <Item typeName="IList" key="ColorRanges">
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-20</Item>
            <Item typeName="Single" key="MaxV">-11</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">0</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-11</Item>
            <Item typeName="Single" key="MaxV">-2</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">102</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-2</Item>
            <Item typeName="Single" key="MaxV">7</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">204</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">7</Item>
            <Item typeName="Single" key="MaxV">16</Item>
            <Item typeName="Int32" key="ColorR">203</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">16</Item>
            <Item typeName="Single" key="MaxV">25</Item>
            <Item typeName="Int32" key="ColorR">0</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">255</Item>
            <Item key="desInfo" />
          </Item>
        </Item>
        <Item typeName="Boolean" key="RangeMode">True</Item>
        <Item typeName="Single" key="MinCompareR">0</Item>
        <Item typeName="Single" key="MaxCompareR">0</Item>
        <Item typeName="IList" key="CombinedColorRanges" />
        <Item typeName="Int32" key="CombineValidCount">1</Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Type">TD栅格指标</Item>
        <Item typeName="String" key="Name">TD_PCCPCH_C2I最小值</Item>
        <Item typeName="Single" key="MinR">-20</Item>
        <Item typeName="Single" key="MaxR">25</Item>
        <Item typeName="String" key="Formula">Tx_5C040315</Item>
        <Item typeName="IList" key="ColorRanges">
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-20</Item>
            <Item typeName="Single" key="MaxV">-11</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">0</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-11</Item>
            <Item typeName="Single" key="MaxV">-2</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">102</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-2</Item>
            <Item typeName="Single" key="MaxV">7</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">204</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">7</Item>
            <Item typeName="Single" key="MaxV">16</Item>
            <Item typeName="Int32" key="ColorR">203</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">16</Item>
            <Item typeName="Single" key="MaxV">25</Item>
            <Item typeName="Int32" key="ColorR">0</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">255</Item>
            <Item key="desInfo" />
          </Item>
        </Item>
        <Item typeName="Boolean" key="RangeMode">True</Item>
        <Item typeName="Single" key="MinCompareR">0</Item>
        <Item typeName="Single" key="MaxCompareR">0</Item>
        <Item typeName="IList" key="CombinedColorRanges" />
        <Item typeName="Int32" key="CombineValidCount">1</Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Type">WCDMA栅格指标</Item>
        <Item typeName="String" key="Name">WCDMA90覆盖</Item>
        <Item typeName="Single" key="MinR">0</Item>
        <Item typeName="Single" key="MaxR">100</Item>
        <Item typeName="String" key="Formula">100*(Wx_710A37+Wx_710A36+Wx_710A35+Wx_710A34+Wx_710A33)/Wx_710A3C</Item>
        <Item typeName="IList" key="ColorRanges">
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">0</Item>
            <Item typeName="Single" key="MaxV">80</Item>
            <Item typeName="Int32" key="ColorR">0</Item>
            <Item typeName="Int32" key="ColorG">0</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">80</Item>
            <Item typeName="Single" key="MaxV">85</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">0</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">85</Item>
            <Item typeName="Single" key="MaxV">90</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">90</Item>
            <Item typeName="Single" key="MaxV">95</Item>
            <Item typeName="Int32" key="ColorR">0</Item>
            <Item typeName="Int32" key="ColorG">0</Item>
            <Item typeName="Int32" key="ColorB">255</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">95</Item>
            <Item typeName="Single" key="MaxV">100</Item>
            <Item typeName="Int32" key="ColorR">50</Item>
            <Item typeName="Int32" key="ColorG">204</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
        </Item>
        <Item typeName="Boolean" key="RangeMode">True</Item>
        <Item typeName="Single" key="MinCompareR">0</Item>
        <Item typeName="Single" key="MaxCompareR">0</Item>
        <Item typeName="IList" key="CombinedColorRanges" />
        <Item typeName="Int32" key="CombineValidCount">1</Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Type">WCDMA栅格指标</Item>
        <Item typeName="String" key="Name">WCDMA95覆盖</Item>
        <Item typeName="Single" key="MinR">0</Item>
        <Item typeName="Single" key="MaxR">100</Item>
        <Item typeName="String" key="Formula">100*(Wx_710A38+Wx_710A37+Wx_710A36+Wx_710A35+Wx_710A34+Wx_710A33)/Wx_710A3C</Item>
        <Item typeName="IList" key="ColorRanges">
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">0</Item>
            <Item typeName="Single" key="MaxV">80</Item>
            <Item typeName="Int32" key="ColorR">0</Item>
            <Item typeName="Int32" key="ColorG">0</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">80</Item>
            <Item typeName="Single" key="MaxV">85</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">0</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">85</Item>
            <Item typeName="Single" key="MaxV">90</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">90</Item>
            <Item typeName="Single" key="MaxV">95</Item>
            <Item typeName="Int32" key="ColorR">0</Item>
            <Item typeName="Int32" key="ColorG">0</Item>
            <Item typeName="Int32" key="ColorB">255</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">95</Item>
            <Item typeName="Single" key="MaxV">100</Item>
            <Item typeName="Int32" key="ColorR">50</Item>
            <Item typeName="Int32" key="ColorG">204</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
        </Item>
        <Item typeName="Boolean" key="RangeMode">True</Item>
        <Item typeName="Single" key="MinCompareR">0</Item>
        <Item typeName="Single" key="MaxCompareR">0</Item>
        <Item typeName="IList" key="CombinedColorRanges" />
        <Item typeName="Int32" key="CombineValidCount">1</Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Type">WCDMA栅格指标</Item>
        <Item typeName="String" key="Name">WCDMA语音RSCP平均值</Item>
        <Item typeName="Single" key="MinR">-140</Item>
        <Item typeName="Single" key="MaxR">10</Item>
        <Item typeName="String" key="Formula">Wx_740A3F</Item>
        <Item typeName="IList" key="ColorRanges">
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-140</Item>
            <Item typeName="Single" key="MaxV">-90</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">0</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-90</Item>
            <Item typeName="Single" key="MaxV">-85</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">102</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-85</Item>
            <Item typeName="Single" key="MaxV">-80</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">204</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-80</Item>
            <Item typeName="Single" key="MaxV">-75</Item>
            <Item typeName="Int32" key="ColorR">101</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-75</Item>
            <Item typeName="Single" key="MaxV">10</Item>
            <Item typeName="Int32" key="ColorR">0</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">255</Item>
            <Item key="desInfo" />
          </Item>
        </Item>
        <Item typeName="Boolean" key="RangeMode">True</Item>
        <Item typeName="Single" key="MinCompareR">0</Item>
        <Item typeName="Single" key="MaxCompareR">0</Item>
        <Item typeName="IList" key="CombinedColorRanges" />
        <Item typeName="Int32" key="CombineValidCount">1</Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Type">WCDMA栅格指标</Item>
        <Item typeName="String" key="Name">WCDMA语音RSCP最大值</Item>
        <Item typeName="Single" key="MinR">-140</Item>
        <Item typeName="Single" key="MaxR">10</Item>
        <Item typeName="String" key="Formula">Wx_740A3D</Item>
        <Item typeName="IList" key="ColorRanges">
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-140</Item>
            <Item typeName="Single" key="MaxV">-90</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">0</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-90</Item>
            <Item typeName="Single" key="MaxV">-85</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">102</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-85</Item>
            <Item typeName="Single" key="MaxV">-80</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">204</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-80</Item>
            <Item typeName="Single" key="MaxV">-75</Item>
            <Item typeName="Int32" key="ColorR">101</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-75</Item>
            <Item typeName="Single" key="MaxV">10</Item>
            <Item typeName="Int32" key="ColorR">0</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">255</Item>
            <Item key="desInfo" />
          </Item>
        </Item>
        <Item typeName="Boolean" key="RangeMode">True</Item>
        <Item typeName="Single" key="MinCompareR">0</Item>
        <Item typeName="Single" key="MaxCompareR">0</Item>
        <Item typeName="IList" key="CombinedColorRanges" />
        <Item typeName="Int32" key="CombineValidCount">1</Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Type">WCDMA栅格指标</Item>
        <Item typeName="String" key="Name">WCDMA语音RSCP最小值</Item>
        <Item typeName="Single" key="MinR">-140</Item>
        <Item typeName="Single" key="MaxR">10</Item>
        <Item typeName="String" key="Formula">Wx_740A3E</Item>
        <Item typeName="IList" key="ColorRanges">
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-140</Item>
            <Item typeName="Single" key="MaxV">-90</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">0</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-90</Item>
            <Item typeName="Single" key="MaxV">-85</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">102</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-85</Item>
            <Item typeName="Single" key="MaxV">-80</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">204</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-80</Item>
            <Item typeName="Single" key="MaxV">-75</Item>
            <Item typeName="Int32" key="ColorR">101</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-75</Item>
            <Item typeName="Single" key="MaxV">10</Item>
            <Item typeName="Int32" key="ColorR">0</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">255</Item>
            <Item key="desInfo" />
          </Item>
        </Item>
        <Item typeName="Boolean" key="RangeMode">True</Item>
        <Item typeName="Single" key="MinCompareR">0</Item>
        <Item typeName="Single" key="MaxCompareR">0</Item>
        <Item typeName="IList" key="CombinedColorRanges" />
        <Item typeName="Int32" key="CombineValidCount">1</Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Type">WCDMA栅格指标</Item>
        <Item typeName="String" key="Name">WCDMA语音TotalEcIo平均值</Item>
        <Item typeName="Single" key="MinR">-40</Item>
        <Item typeName="Single" key="MaxR">0</Item>
        <Item typeName="String" key="Formula">Wx_6A0A0A</Item>
        <Item typeName="IList" key="ColorRanges">
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-40</Item>
            <Item typeName="Single" key="MaxV">-14</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">0</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-14</Item>
            <Item typeName="Single" key="MaxV">-12</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">102</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-12</Item>
            <Item typeName="Single" key="MaxV">-10</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">204</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-10</Item>
            <Item typeName="Single" key="MaxV">-8</Item>
            <Item typeName="Int32" key="ColorR">101</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-8</Item>
            <Item typeName="Single" key="MaxV">0</Item>
            <Item typeName="Int32" key="ColorR">0</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">255</Item>
            <Item key="desInfo" />
          </Item>
        </Item>
        <Item typeName="Boolean" key="RangeMode">True</Item>
        <Item typeName="Single" key="MinCompareR">0</Item>
        <Item typeName="Single" key="MaxCompareR">0</Item>
        <Item typeName="IList" key="CombinedColorRanges" />
        <Item typeName="Int32" key="CombineValidCount">1</Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Type">WCDMA栅格指标</Item>
        <Item typeName="String" key="Name">WCDMA语音TotalEcIo最大值</Item>
        <Item typeName="Single" key="MinR">-40</Item>
        <Item typeName="Single" key="MaxR">0</Item>
        <Item typeName="String" key="Formula">Wx_6A0A07</Item>
        <Item typeName="IList" key="ColorRanges">
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-40</Item>
            <Item typeName="Single" key="MaxV">-14</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">0</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-14</Item>
            <Item typeName="Single" key="MaxV">-12</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">102</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-12</Item>
            <Item typeName="Single" key="MaxV">-10</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">204</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-10</Item>
            <Item typeName="Single" key="MaxV">-8</Item>
            <Item typeName="Int32" key="ColorR">101</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-8</Item>
            <Item typeName="Single" key="MaxV">0</Item>
            <Item typeName="Int32" key="ColorR">0</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">255</Item>
            <Item key="desInfo" />
          </Item>
        </Item>
        <Item typeName="Boolean" key="RangeMode">True</Item>
        <Item typeName="Single" key="MinCompareR">0</Item>
        <Item typeName="Single" key="MaxCompareR">0</Item>
        <Item typeName="IList" key="CombinedColorRanges" />
        <Item typeName="Int32" key="CombineValidCount">1</Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Type">WCDMA栅格指标</Item>
        <Item typeName="String" key="Name">WCDMA语音TotalEcIo最小值</Item>
        <Item typeName="Single" key="MinR">-40</Item>
        <Item typeName="Single" key="MaxR">0</Item>
        <Item typeName="String" key="Formula">Wx_6A0A08</Item>
        <Item typeName="IList" key="ColorRanges">
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-40</Item>
            <Item typeName="Single" key="MaxV">-14</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">0</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-14</Item>
            <Item typeName="Single" key="MaxV">-12</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">102</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-12</Item>
            <Item typeName="Single" key="MaxV">-10</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">204</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-10</Item>
            <Item typeName="Single" key="MaxV">-8</Item>
            <Item typeName="Int32" key="ColorR">101</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-8</Item>
            <Item typeName="Single" key="MaxV">0</Item>
            <Item typeName="Int32" key="ColorR">0</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">255</Item>
            <Item key="desInfo" />
          </Item>
        </Item>
        <Item typeName="Boolean" key="RangeMode">True</Item>
        <Item typeName="Single" key="MinCompareR">0</Item>
        <Item typeName="Single" key="MaxCompareR">0</Item>
        <Item typeName="IList" key="CombinedColorRanges" />
        <Item typeName="Int32" key="CombineValidCount">1</Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Type">WCDMA栅格指标</Item>
        <Item typeName="String" key="Name">WCDMA语音SIR平均值</Item>
        <Item typeName="Single" key="MinR">-20</Item>
        <Item typeName="Single" key="MaxR">30</Item>
        <Item typeName="String" key="Formula">Wx_5D0A0309</Item>
        <Item typeName="IList" key="ColorRanges">
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-20</Item>
            <Item typeName="Single" key="MaxV">10</Item>
            <Item typeName="Int32" key="ColorR">0</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">10</Item>
            <Item typeName="Single" key="MaxV">15</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">15</Item>
            <Item typeName="Single" key="MaxV">30</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">0</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
        </Item>
        <Item typeName="Boolean" key="RangeMode">True</Item>
        <Item typeName="Single" key="MinCompareR">0</Item>
        <Item typeName="Single" key="MaxCompareR">0</Item>
        <Item typeName="IList" key="CombinedColorRanges" />
        <Item typeName="Int32" key="CombineValidCount">1</Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Type">WCDMA栅格指标</Item>
        <Item typeName="String" key="Name">WCDMA语音SIR最大值</Item>
        <Item typeName="Single" key="MinR">-20</Item>
        <Item typeName="Single" key="MaxR">30</Item>
        <Item typeName="String" key="Formula">Wx_5D0A0306</Item>
        <Item typeName="IList" key="ColorRanges">
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-20</Item>
            <Item typeName="Single" key="MaxV">10</Item>
            <Item typeName="Int32" key="ColorR">0</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">10</Item>
            <Item typeName="Single" key="MaxV">15</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">15</Item>
            <Item typeName="Single" key="MaxV">30</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">0</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
        </Item>
        <Item typeName="Boolean" key="RangeMode">True</Item>
        <Item typeName="Single" key="MinCompareR">0</Item>
        <Item typeName="Single" key="MaxCompareR">0</Item>
        <Item typeName="IList" key="CombinedColorRanges" />
        <Item typeName="Int32" key="CombineValidCount">1</Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Type">WCDMA栅格指标</Item>
        <Item typeName="String" key="Name">WCDMA语音SIR最小值</Item>
        <Item typeName="Single" key="MinR">-20</Item>
        <Item typeName="Single" key="MaxR">30</Item>
        <Item typeName="String" key="Formula">Wx_5D0A0307</Item>
        <Item typeName="IList" key="ColorRanges">
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-20</Item>
            <Item typeName="Single" key="MaxV">10</Item>
            <Item typeName="Int32" key="ColorR">0</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">10</Item>
            <Item typeName="Single" key="MaxV">15</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">15</Item>
            <Item typeName="Single" key="MaxV">30</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">0</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
        </Item>
        <Item typeName="Boolean" key="RangeMode">True</Item>
        <Item typeName="Single" key="MinCompareR">0</Item>
        <Item typeName="Single" key="MaxCompareR">0</Item>
        <Item typeName="IList" key="CombinedColorRanges" />
        <Item typeName="Int32" key="CombineValidCount">1</Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Type">CDMA栅格指标</Item>
        <Item typeName="String" key="Name">CDMA90覆盖</Item>
        <Item typeName="Single" key="MinR">0</Item>
        <Item typeName="Single" key="MaxR">100</Item>
        <Item typeName="String" key="Formula">100*Cx_6E061F/Cx_6E0621</Item>
        <Item typeName="IList" key="ColorRanges">
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">0</Item>
            <Item typeName="Single" key="MaxV">80</Item>
            <Item typeName="Int32" key="ColorR">0</Item>
            <Item typeName="Int32" key="ColorG">0</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">80</Item>
            <Item typeName="Single" key="MaxV">85</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">0</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">85</Item>
            <Item typeName="Single" key="MaxV">90</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">90</Item>
            <Item typeName="Single" key="MaxV">95</Item>
            <Item typeName="Int32" key="ColorR">0</Item>
            <Item typeName="Int32" key="ColorG">0</Item>
            <Item typeName="Int32" key="ColorB">255</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">95</Item>
            <Item typeName="Single" key="MaxV">100</Item>
            <Item typeName="Int32" key="ColorR">50</Item>
            <Item typeName="Int32" key="ColorG">204</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
        </Item>
        <Item typeName="Boolean" key="RangeMode">True</Item>
        <Item typeName="Single" key="MinCompareR">0</Item>
        <Item typeName="Single" key="MaxCompareR">0</Item>
        <Item typeName="IList" key="CombinedColorRanges" />
        <Item typeName="Int32" key="CombineValidCount">1</Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Type">CDMA栅格指标</Item>
        <Item typeName="String" key="Name">CDMA95覆盖</Item>
        <Item typeName="Single" key="MinR">0</Item>
        <Item typeName="Single" key="MaxR">100</Item>
        <Item typeName="String" key="Formula">100*Cx_6E0620/Cx_6E0621</Item>
        <Item typeName="IList" key="ColorRanges">
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">0</Item>
            <Item typeName="Single" key="MaxV">80</Item>
            <Item typeName="Int32" key="ColorR">0</Item>
            <Item typeName="Int32" key="ColorG">0</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">80</Item>
            <Item typeName="Single" key="MaxV">85</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">0</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">85</Item>
            <Item typeName="Single" key="MaxV">90</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">90</Item>
            <Item typeName="Single" key="MaxV">95</Item>
            <Item typeName="Int32" key="ColorR">0</Item>
            <Item typeName="Int32" key="ColorG">0</Item>
            <Item typeName="Int32" key="ColorB">255</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">95</Item>
            <Item typeName="Single" key="MaxV">100</Item>
            <Item typeName="Int32" key="ColorR">50</Item>
            <Item typeName="Int32" key="ColorG">204</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
        </Item>
        <Item typeName="Boolean" key="RangeMode">True</Item>
        <Item typeName="Single" key="MinCompareR">0</Item>
        <Item typeName="Single" key="MaxCompareR">0</Item>
        <Item typeName="IList" key="CombinedColorRanges" />
        <Item typeName="Int32" key="CombineValidCount">1</Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Type">CDMA栅格指标</Item>
        <Item typeName="String" key="Name">CDMA语音TotalEcIo平均值</Item>
        <Item typeName="Single" key="MinR">-40</Item>
        <Item typeName="Single" key="MaxR">0</Item>
        <Item typeName="String" key="Formula">Cx_5B06012C</Item>
        <Item typeName="IList" key="ColorRanges">
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-40</Item>
            <Item typeName="Single" key="MaxV">-15</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">0</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-15</Item>
            <Item typeName="Single" key="MaxV">-13</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-13</Item>
            <Item typeName="Single" key="MaxV">-9</Item>
            <Item typeName="Int32" key="ColorR">0</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-9</Item>
            <Item typeName="Single" key="MaxV">0</Item>
            <Item typeName="Int32" key="ColorR">0</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">255</Item>
            <Item key="desInfo" />
          </Item>
        </Item>
        <Item typeName="Boolean" key="RangeMode">True</Item>
        <Item typeName="Single" key="MinCompareR">0</Item>
        <Item typeName="Single" key="MaxCompareR">0</Item>
        <Item typeName="IList" key="CombinedColorRanges" />
        <Item typeName="Int32" key="CombineValidCount">1</Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Type">CDMA栅格指标</Item>
        <Item typeName="String" key="Name">CDMA语音TotalEcIo最大值</Item>
        <Item typeName="Single" key="MinR">-40</Item>
        <Item typeName="Single" key="MaxR">0</Item>
        <Item typeName="String" key="Formula">Cx_5B06012A</Item>
        <Item typeName="IList" key="ColorRanges">
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-40</Item>
            <Item typeName="Single" key="MaxV">-15</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">0</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-15</Item>
            <Item typeName="Single" key="MaxV">-13</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-13</Item>
            <Item typeName="Single" key="MaxV">-9</Item>
            <Item typeName="Int32" key="ColorR">0</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-9</Item>
            <Item typeName="Single" key="MaxV">0</Item>
            <Item typeName="Int32" key="ColorR">0</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">255</Item>
            <Item key="desInfo" />
          </Item>
        </Item>
        <Item typeName="Boolean" key="RangeMode">True</Item>
        <Item typeName="Single" key="MinCompareR">0</Item>
        <Item typeName="Single" key="MaxCompareR">0</Item>
        <Item typeName="IList" key="CombinedColorRanges" />
        <Item typeName="Int32" key="CombineValidCount">1</Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Type">CDMA栅格指标</Item>
        <Item typeName="String" key="Name">CDMA语音TotalEcIo最小值</Item>
        <Item typeName="Single" key="MinR">-40</Item>
        <Item typeName="Single" key="MaxR">0</Item>
        <Item typeName="String" key="Formula">Cx_5B06012B</Item>
        <Item typeName="IList" key="ColorRanges">
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-40</Item>
            <Item typeName="Single" key="MaxV">-15</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">0</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-15</Item>
            <Item typeName="Single" key="MaxV">-13</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-13</Item>
            <Item typeName="Single" key="MaxV">-9</Item>
            <Item typeName="Int32" key="ColorR">0</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-9</Item>
            <Item typeName="Single" key="MaxV">0</Item>
            <Item typeName="Int32" key="ColorR">0</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">255</Item>
            <Item key="desInfo" />
          </Item>
        </Item>
        <Item typeName="Boolean" key="RangeMode">True</Item>
        <Item typeName="Single" key="MinCompareR">0</Item>
        <Item typeName="Single" key="MaxCompareR">0</Item>
        <Item typeName="IList" key="CombinedColorRanges" />
        <Item typeName="Int32" key="CombineValidCount">1</Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Type">CDMA栅格指标</Item>
        <Item typeName="String" key="Name">CDMA语音TxPower平均值</Item>
        <Item typeName="Single" key="MinR">-127</Item>
        <Item typeName="Single" key="MaxR">44</Item>
        <Item typeName="String" key="Formula">Cx_6C062C</Item>
        <Item typeName="IList" key="ColorRanges">
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-127</Item>
            <Item typeName="Single" key="MaxV">0</Item>
            <Item typeName="Int32" key="ColorR">0</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">0</Item>
            <Item typeName="Single" key="MaxV">10</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">10</Item>
            <Item typeName="Single" key="MaxV">44</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">0</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
        </Item>
        <Item typeName="Boolean" key="RangeMode">True</Item>
        <Item typeName="Single" key="MinCompareR">0</Item>
        <Item typeName="Single" key="MaxCompareR">0</Item>
        <Item typeName="IList" key="CombinedColorRanges" />
        <Item typeName="Int32" key="CombineValidCount">1</Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Type">CDMA栅格指标</Item>
        <Item typeName="String" key="Name">CDMA语音TxPower最大值</Item>
        <Item typeName="Single" key="MinR">-127</Item>
        <Item typeName="Single" key="MaxR">44</Item>
        <Item typeName="String" key="Formula">Cx_6C062A</Item>
        <Item typeName="IList" key="ColorRanges">
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-127</Item>
            <Item typeName="Single" key="MaxV">0</Item>
            <Item typeName="Int32" key="ColorR">0</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">0</Item>
            <Item typeName="Single" key="MaxV">10</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">10</Item>
            <Item typeName="Single" key="MaxV">44</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">0</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
        </Item>
        <Item typeName="Boolean" key="RangeMode">True</Item>
        <Item typeName="Single" key="MinCompareR">0</Item>
        <Item typeName="Single" key="MaxCompareR">0</Item>
        <Item typeName="IList" key="CombinedColorRanges" />
        <Item typeName="Int32" key="CombineValidCount">1</Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Type">CDMA栅格指标</Item>
        <Item typeName="String" key="Name">CDMA语音TxPower最小值</Item>
        <Item typeName="Single" key="MinR">-127</Item>
        <Item typeName="Single" key="MaxR">44</Item>
        <Item typeName="String" key="Formula">Cx_6C062B</Item>
        <Item typeName="IList" key="ColorRanges">
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-127</Item>
            <Item typeName="Single" key="MaxV">0</Item>
            <Item typeName="Int32" key="ColorR">0</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">0</Item>
            <Item typeName="Single" key="MaxV">10</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">10</Item>
            <Item typeName="Single" key="MaxV">44</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">0</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
        </Item>
        <Item typeName="Boolean" key="RangeMode">True</Item>
        <Item typeName="Single" key="MinCompareR">0</Item>
        <Item typeName="Single" key="MaxCompareR">0</Item>
        <Item typeName="IList" key="CombinedColorRanges" />
        <Item typeName="Int32" key="CombineValidCount">1</Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Type">CDMA栅格指标</Item>
        <Item typeName="String" key="Name">CDMA语音RxPower平均值</Item>
        <Item typeName="Single" key="MinR">-140</Item>
        <Item typeName="Single" key="MaxR">-1</Item>
        <Item typeName="String" key="Formula">Cx_6B0626</Item>
        <Item typeName="IList" key="ColorRanges">
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-140</Item>
            <Item typeName="Single" key="MaxV">-90</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">0</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-90</Item>
            <Item typeName="Single" key="MaxV">-80</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-80</Item>
            <Item typeName="Single" key="MaxV">-70</Item>
            <Item typeName="Int32" key="ColorR">0</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-70</Item>
            <Item typeName="Single" key="MaxV">-1</Item>
            <Item typeName="Int32" key="ColorR">0</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">255</Item>
            <Item key="desInfo" />
          </Item>
        </Item>
        <Item typeName="Boolean" key="RangeMode">True</Item>
        <Item typeName="Single" key="MinCompareR">0</Item>
        <Item typeName="Single" key="MaxCompareR">0</Item>
        <Item typeName="IList" key="CombinedColorRanges" />
        <Item typeName="Int32" key="CombineValidCount">1</Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Type">CDMA栅格指标</Item>
        <Item typeName="String" key="Name">CDMA语音RxPower最大值</Item>
        <Item typeName="Single" key="MinR">-140</Item>
        <Item typeName="Single" key="MaxR">-1</Item>
        <Item typeName="String" key="Formula">Cx_6B0627</Item>
        <Item typeName="IList" key="ColorRanges">
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-140</Item>
            <Item typeName="Single" key="MaxV">-90</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">0</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-90</Item>
            <Item typeName="Single" key="MaxV">-80</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-80</Item>
            <Item typeName="Single" key="MaxV">-70</Item>
            <Item typeName="Int32" key="ColorR">0</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-70</Item>
            <Item typeName="Single" key="MaxV">-1</Item>
            <Item typeName="Int32" key="ColorR">0</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">255</Item>
            <Item key="desInfo" />
          </Item>
        </Item>
        <Item typeName="Boolean" key="RangeMode">True</Item>
        <Item typeName="Single" key="MinCompareR">0</Item>
        <Item typeName="Single" key="MaxCompareR">0</Item>
        <Item typeName="IList" key="CombinedColorRanges" />
        <Item typeName="Int32" key="CombineValidCount">1</Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Type">CDMA栅格指标</Item>
        <Item typeName="String" key="Name">CDMA语音RxPower最小值</Item>
        <Item typeName="Single" key="MinR">-140</Item>
        <Item typeName="Single" key="MaxR">-1</Item>
        <Item typeName="String" key="Formula">Cx_6B0628</Item>
        <Item typeName="IList" key="ColorRanges">
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-140</Item>
            <Item typeName="Single" key="MaxV">-90</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">0</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-90</Item>
            <Item typeName="Single" key="MaxV">-80</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-80</Item>
            <Item typeName="Single" key="MaxV">-70</Item>
            <Item typeName="Int32" key="ColorR">0</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-70</Item>
            <Item typeName="Single" key="MaxV">-1</Item>
            <Item typeName="Int32" key="ColorR">0</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">255</Item>
            <Item key="desInfo" />
          </Item>
        </Item>
        <Item typeName="Boolean" key="RangeMode">True</Item>
        <Item typeName="Single" key="MinCompareR">0</Item>
        <Item typeName="Single" key="MaxCompareR">0</Item>
        <Item typeName="IList" key="CombinedColorRanges" />
        <Item typeName="Int32" key="CombineValidCount">1</Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Type">TD栅格指标</Item>
        <Item typeName="String" key="Name">TD_DPCH_RSCP平均值</Item>
        <Item typeName="Single" key="MinR">-140</Item>
        <Item typeName="Single" key="MaxR">-10</Item>
        <Item typeName="String" key="Formula">Tx_5C04040D</Item>
        <Item typeName="IList" key="ColorRanges">
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-140</Item>
            <Item typeName="Single" key="MaxV">-105</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">0</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-105</Item>
            <Item typeName="Single" key="MaxV">-94</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">102</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-94</Item>
            <Item typeName="Single" key="MaxV">-85</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">204</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-85</Item>
            <Item typeName="Single" key="MaxV">-75</Item>
            <Item typeName="Int32" key="ColorR">203</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-75</Item>
            <Item typeName="Single" key="MaxV">-60</Item>
            <Item typeName="Int32" key="ColorR">101</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-60</Item>
            <Item typeName="Single" key="MaxV">-10</Item>
            <Item typeName="Int32" key="ColorR">0</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">255</Item>
            <Item key="desInfo" />
          </Item>
        </Item>
        <Item typeName="Boolean" key="RangeMode">True</Item>
        <Item typeName="Single" key="MinCompareR">0</Item>
        <Item typeName="Single" key="MaxCompareR">0</Item>
        <Item typeName="IList" key="CombinedColorRanges" />
        <Item typeName="Int32" key="CombineValidCount">1</Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Type">TD栅格指标</Item>
        <Item typeName="String" key="Name">TD_DPCH_RSCP最大值</Item>
        <Item typeName="Single" key="MinR">-140</Item>
        <Item typeName="Single" key="MaxR">-10</Item>
        <Item typeName="String" key="Formula">Tx_5C04040B</Item>
        <Item typeName="IList" key="ColorRanges">
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-140</Item>
            <Item typeName="Single" key="MaxV">-105</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">0</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-105</Item>
            <Item typeName="Single" key="MaxV">-94</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">102</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-94</Item>
            <Item typeName="Single" key="MaxV">-85</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">204</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-85</Item>
            <Item typeName="Single" key="MaxV">-75</Item>
            <Item typeName="Int32" key="ColorR">203</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-75</Item>
            <Item typeName="Single" key="MaxV">-60</Item>
            <Item typeName="Int32" key="ColorR">101</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-60</Item>
            <Item typeName="Single" key="MaxV">-10</Item>
            <Item typeName="Int32" key="ColorR">0</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">255</Item>
            <Item key="desInfo" />
          </Item>
        </Item>
        <Item typeName="Boolean" key="RangeMode">True</Item>
        <Item typeName="Single" key="MinCompareR">0</Item>
        <Item typeName="Single" key="MaxCompareR">0</Item>
        <Item typeName="IList" key="CombinedColorRanges" />
        <Item typeName="Int32" key="CombineValidCount">1</Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Type">TD栅格指标</Item>
        <Item typeName="String" key="Name">TD_DPCH_RSCP最小值</Item>
        <Item typeName="Single" key="MinR">-140</Item>
        <Item typeName="Single" key="MaxR">-10</Item>
        <Item typeName="String" key="Formula">Tx_5C04040C</Item>
        <Item typeName="IList" key="ColorRanges">
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-140</Item>
            <Item typeName="Single" key="MaxV">-105</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">0</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-105</Item>
            <Item typeName="Single" key="MaxV">-94</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">102</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-94</Item>
            <Item typeName="Single" key="MaxV">-85</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">204</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-85</Item>
            <Item typeName="Single" key="MaxV">-75</Item>
            <Item typeName="Int32" key="ColorR">203</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-75</Item>
            <Item typeName="Single" key="MaxV">-60</Item>
            <Item typeName="Int32" key="ColorR">101</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-60</Item>
            <Item typeName="Single" key="MaxV">-10</Item>
            <Item typeName="Int32" key="ColorR">0</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">255</Item>
            <Item key="desInfo" />
          </Item>
        </Item>
        <Item typeName="Boolean" key="RangeMode">True</Item>
        <Item typeName="Single" key="MinCompareR">0</Item>
        <Item typeName="Single" key="MaxCompareR">0</Item>
        <Item typeName="IList" key="CombinedColorRanges" />
        <Item typeName="Int32" key="CombineValidCount">1</Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Type">TD栅格指标</Item>
        <Item typeName="String" key="Name">TD_DATA_PCCPCH_RSCP平均值</Item>
        <Item typeName="Single" key="MinR">-140</Item>
        <Item typeName="Single" key="MaxR">-10</Item>
        <Item typeName="String" key="Formula">Tx_5C05030D</Item>
        <Item typeName="IList" key="ColorRanges">
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-140</Item>
            <Item typeName="Single" key="MaxV">-105</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">0</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-105</Item>
            <Item typeName="Single" key="MaxV">-94</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">102</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-94</Item>
            <Item typeName="Single" key="MaxV">-85</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">204</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-85</Item>
            <Item typeName="Single" key="MaxV">-75</Item>
            <Item typeName="Int32" key="ColorR">203</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-75</Item>
            <Item typeName="Single" key="MaxV">-60</Item>
            <Item typeName="Int32" key="ColorR">101</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-60</Item>
            <Item typeName="Single" key="MaxV">-10</Item>
            <Item typeName="Int32" key="ColorR">0</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">255</Item>
            <Item key="desInfo" />
          </Item>
        </Item>
        <Item typeName="Boolean" key="RangeMode">True</Item>
        <Item typeName="Single" key="MinCompareR">0</Item>
        <Item typeName="Single" key="MaxCompareR">0</Item>
        <Item typeName="IList" key="CombinedColorRanges" />
        <Item typeName="Int32" key="CombineValidCount">1</Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Type">GSM栅格指标</Item>
        <Item typeName="String" key="Name">LTE_RSRP</Item>
        <Item typeName="Single" key="MinR">-141</Item>
        <Item typeName="Single" key="MaxR">25</Item>
        <Item typeName="String" key="Formula">Lte_61210309</Item>
        <Item typeName="IList" key="ColorRanges">
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-141</Item>
            <Item typeName="Single" key="MaxV">-107.8</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">0</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-107.8</Item>
            <Item typeName="Single" key="MaxV">-74.6</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">102</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-74.6</Item>
            <Item typeName="Single" key="MaxV">-41.4</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">204</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-41.4</Item>
            <Item typeName="Single" key="MaxV">-8.199997</Item>
            <Item typeName="Int32" key="ColorR">203</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-8.199997</Item>
            <Item typeName="Single" key="MaxV">25</Item>
            <Item typeName="Int32" key="ColorR">101</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
        </Item>
        <Item typeName="Boolean" key="RangeMode">True</Item>
        <Item typeName="Single" key="MinCompareR">0</Item>
        <Item typeName="Single" key="MaxCompareR">0</Item>
        <Item typeName="IList" key="CombinedColorRanges" />
        <Item typeName="Int32" key="CombineValidCount">1</Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Type">TD栅格指标</Item>
        <Item typeName="String" key="Name">未命名KPI</Item>
        <Item typeName="Single" key="MinR">0</Item>
        <Item typeName="Single" key="MaxR">500</Item>
        <Item typeName="String" key="Formula">(8*1000*Lte_052164020101)/(Lte_052164020102*1024)*1024</Item>
        <Item typeName="IList" key="ColorRanges">
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">0</Item>
            <Item typeName="Single" key="MaxV">100</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">0</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">100</Item>
            <Item typeName="Single" key="MaxV">200</Item>
            <Item typeName="Int32" key="ColorR">203</Item>
            <Item typeName="Int32" key="ColorG">51</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">200</Item>
            <Item typeName="Single" key="MaxV">300</Item>
            <Item typeName="Int32" key="ColorR">152</Item>
            <Item typeName="Int32" key="ColorG">102</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">300</Item>
            <Item typeName="Single" key="MaxV">400</Item>
            <Item typeName="Int32" key="ColorR">101</Item>
            <Item typeName="Int32" key="ColorG">153</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">400</Item>
            <Item typeName="Single" key="MaxV">500</Item>
            <Item typeName="Int32" key="ColorR">50</Item>
            <Item typeName="Int32" key="ColorG">204</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
        </Item>
        <Item typeName="Boolean" key="RangeMode">True</Item>
        <Item typeName="Single" key="MinCompareR">0</Item>
        <Item typeName="Single" key="MaxCompareR">0</Item>
        <Item typeName="IList" key="CombinedColorRanges" />
        <Item typeName="Int32" key="CombineValidCount">1</Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Type">未分类</Item>
        <Item typeName="String" key="Name">TD扫频</Item>
        <Item typeName="Single" key="MinR">-141</Item>
        <Item typeName="Single" key="MaxR">0</Item>
        <Item typeName="String" key="Formula">Tc_5F13080D</Item>
        <Item typeName="IList" key="ColorRanges">
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-141</Item>
            <Item typeName="Single" key="MaxV">-112.8</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">0</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-112.8</Item>
            <Item typeName="Single" key="MaxV">-84.6</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">102</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-84.6</Item>
            <Item typeName="Single" key="MaxV">-56.4</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">204</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-56.4</Item>
            <Item typeName="Single" key="MaxV">-28.2</Item>
            <Item typeName="Int32" key="ColorR">203</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-28.2</Item>
            <Item typeName="Single" key="MaxV">3.814697E-06</Item>
            <Item typeName="Int32" key="ColorR">101</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
        </Item>
        <Item typeName="Boolean" key="RangeMode">True</Item>
        <Item typeName="Single" key="MinCompareR">0</Item>
        <Item typeName="Single" key="MaxCompareR">0</Item>
        <Item typeName="IList" key="CombinedColorRanges" />
        <Item typeName="Int32" key="CombineValidCount">1</Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Type">未分类</Item>
        <Item typeName="String" key="Name">未命名KPI1</Item>
        <Item typeName="Single" key="MinR">0</Item>
        <Item typeName="Single" key="MaxR">0</Item>
        <Item key="Formula" />
        <Item typeName="IList" key="ColorRanges" />
        <Item typeName="Boolean" key="RangeMode">True</Item>
        <Item typeName="Single" key="MinCompareR">0</Item>
        <Item typeName="Single" key="MaxCompareR">0</Item>
        <Item typeName="IList" key="CombinedColorRanges" />
        <Item typeName="Int32" key="CombineValidCount">1</Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Type">未分类</Item>
        <Item typeName="String" key="Name">LTE下载</Item>
        <Item typeName="Single" key="MinR">0</Item>
        <Item typeName="Single" key="MaxR">1000</Item>
        <Item typeName="String" key="Formula">Lte_61210512</Item>
        <Item typeName="IList" key="ColorRanges">
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">0</Item>
            <Item typeName="Single" key="MaxV">100</Item>
            <Item typeName="Int32" key="ColorR">192</Item>
            <Item typeName="Int32" key="ColorG">192</Item>
            <Item typeName="Int32" key="ColorB">192</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">100</Item>
            <Item typeName="Single" key="MaxV">200</Item>
            <Item typeName="Int32" key="ColorR">198</Item>
            <Item typeName="Int32" key="ColorG">172</Item>
            <Item typeName="Int32" key="ColorB">172</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">200</Item>
            <Item typeName="Single" key="MaxV">300</Item>
            <Item typeName="Int32" key="ColorR">204</Item>
            <Item typeName="Int32" key="ColorG">153</Item>
            <Item typeName="Int32" key="ColorB">153</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">300</Item>
            <Item typeName="Single" key="MaxV">400</Item>
            <Item typeName="Int32" key="ColorR">210</Item>
            <Item typeName="Int32" key="ColorG">134</Item>
            <Item typeName="Int32" key="ColorB">134</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">400</Item>
            <Item typeName="Single" key="MaxV">500</Item>
            <Item typeName="Int32" key="ColorR">217</Item>
            <Item typeName="Int32" key="ColorG">115</Item>
            <Item typeName="Int32" key="ColorB">115</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">500</Item>
            <Item typeName="Single" key="MaxV">600</Item>
            <Item typeName="Int32" key="ColorR">223</Item>
            <Item typeName="Int32" key="ColorG">96</Item>
            <Item typeName="Int32" key="ColorB">96</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">600</Item>
            <Item typeName="Single" key="MaxV">700</Item>
            <Item typeName="Int32" key="ColorR">229</Item>
            <Item typeName="Int32" key="ColorG">76</Item>
            <Item typeName="Int32" key="ColorB">76</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">700</Item>
            <Item typeName="Single" key="MaxV">800</Item>
            <Item typeName="Int32" key="ColorR">236</Item>
            <Item typeName="Int32" key="ColorG">57</Item>
            <Item typeName="Int32" key="ColorB">57</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">800</Item>
            <Item typeName="Single" key="MaxV">900</Item>
            <Item typeName="Int32" key="ColorR">242</Item>
            <Item typeName="Int32" key="ColorG">38</Item>
            <Item typeName="Int32" key="ColorB">38</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">900</Item>
            <Item typeName="Single" key="MaxV">1000</Item>
            <Item typeName="Int32" key="ColorR">248</Item>
            <Item typeName="Int32" key="ColorG">19</Item>
            <Item typeName="Int32" key="ColorB">19</Item>
            <Item key="desInfo" />
          </Item>
        </Item>
        <Item typeName="Boolean" key="RangeMode">True</Item>
        <Item typeName="Single" key="MinCompareR">0</Item>
        <Item typeName="Single" key="MaxCompareR">0</Item>
        <Item typeName="IList" key="CombinedColorRanges" />
        <Item typeName="Int32" key="CombineValidCount">1</Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Type">未分类</Item>
        <Item typeName="String" key="Name">topn</Item>
        <Item typeName="Single" key="MinR">-100</Item>
        <Item typeName="Single" key="MaxR">200</Item>
        <Item typeName="String" key="Formula">Lc_5F230102</Item>
        <Item typeName="IList" key="ColorRanges">
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-100</Item>
            <Item typeName="Single" key="MaxV">-40</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">0</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-40</Item>
            <Item typeName="Single" key="MaxV">20</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">102</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">20</Item>
            <Item typeName="Single" key="MaxV">80</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">204</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">80</Item>
            <Item typeName="Single" key="MaxV">140</Item>
            <Item typeName="Int32" key="ColorR">203</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">140</Item>
            <Item typeName="Single" key="MaxV">200</Item>
            <Item typeName="Int32" key="ColorR">101</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
        </Item>
        <Item typeName="Boolean" key="RangeMode">True</Item>
        <Item typeName="Single" key="MinCompareR">0</Item>
        <Item typeName="Single" key="MaxCompareR">0</Item>
        <Item typeName="IList" key="CombinedColorRanges" />
        <Item typeName="Int32" key="CombineValidCount">1</Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Type">未分类</Item>
        <Item typeName="String" key="Name">LTE_FDD_RSRP</Item>
        <Item typeName="Single" key="MinR">-150</Item>
        <Item typeName="Single" key="MaxR">0</Item>
        <Item typeName="String" key="Formula">Lf_612D0309</Item>
        <Item typeName="IList" key="ColorRanges">
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-150</Item>
            <Item typeName="Single" key="MaxV">-120</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">0</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-120</Item>
            <Item typeName="Single" key="MaxV">-90</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">102</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-90</Item>
            <Item typeName="Single" key="MaxV">-60</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">204</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-60</Item>
            <Item typeName="Single" key="MaxV">-30</Item>
            <Item typeName="Int32" key="ColorR">203</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-30</Item>
            <Item typeName="Single" key="MaxV">0</Item>
            <Item typeName="Int32" key="ColorR">101</Item>
            <Item typeName="Int32" key="ColorG">255</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
        </Item>
        <Item typeName="Boolean" key="RangeMode">True</Item>
        <Item typeName="Single" key="MinCompareR">0</Item>
        <Item typeName="Single" key="MaxCompareR">0</Item>
        <Item typeName="IList" key="CombinedColorRanges" />
        <Item typeName="Int32" key="CombineValidCount">1</Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Type">未分类</Item>
        <Item typeName="String" key="Name">NR_RSRP</Item>
        <Item typeName="Single" key="MinR">-140</Item>
        <Item typeName="Single" key="MaxR">20</Item>
        <Item typeName="String" key="Formula">Nr_BA040002</Item>
        <Item typeName="IList" key="ColorRanges">
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-140</Item>
            <Item typeName="Single" key="MaxV">-120</Item>
            <Item typeName="Int32" key="ColorR">255</Item>
            <Item typeName="Int32" key="ColorG">0</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-120</Item>
            <Item typeName="Single" key="MaxV">-100</Item>
            <Item typeName="Int32" key="ColorR">223</Item>
            <Item typeName="Int32" key="ColorG">31</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-100</Item>
            <Item typeName="Single" key="MaxV">-80</Item>
            <Item typeName="Int32" key="ColorR">191</Item>
            <Item typeName="Int32" key="ColorG">63</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-80</Item>
            <Item typeName="Single" key="MaxV">-60</Item>
            <Item typeName="Int32" key="ColorR">159</Item>
            <Item typeName="Int32" key="ColorG">95</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-60</Item>
            <Item typeName="Single" key="MaxV">-40</Item>
            <Item typeName="Int32" key="ColorR">127</Item>
            <Item typeName="Int32" key="ColorG">127</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-40</Item>
            <Item typeName="Single" key="MaxV">-20</Item>
            <Item typeName="Int32" key="ColorR">95</Item>
            <Item typeName="Int32" key="ColorG">159</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">-20</Item>
            <Item typeName="Single" key="MaxV">0</Item>
            <Item typeName="Int32" key="ColorR">63</Item>
            <Item typeName="Int32" key="ColorG">191</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="MinV">0</Item>
            <Item typeName="Single" key="MaxV">20</Item>
            <Item typeName="Int32" key="ColorR">31</Item>
            <Item typeName="Int32" key="ColorG">223</Item>
            <Item typeName="Int32" key="ColorB">0</Item>
            <Item key="desInfo" />
          </Item>
        </Item>
        <Item typeName="Boolean" key="RangeMode">True</Item>
        <Item typeName="Single" key="MinCompareR">0</Item>
        <Item typeName="Single" key="MaxCompareR">0</Item>
        <Item typeName="IList" key="CombinedColorRanges" />
        <Item typeName="Int32" key="CombineValidCount">1</Item>
      </Item>
    </Item>
  </Config>
</Configs>