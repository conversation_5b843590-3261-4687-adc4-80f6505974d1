﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.ZTFunc
{
    public class TDDownLoadAnaByRegion : TDDownLoadQueryAnaBase
    {
        public TDDownLoadAnaByRegion(MainModel mm)
            : base(mm)
        {
            NeedJudgeTestPointByRegion = true;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            if (searchGeometrys.Region == null)
            {
                return false;
            }
            return searchGeometrys.IsValidRegion;
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MainModel.NeedSearchType.Region;
        }

        public override string Name
        {
            get { return "按文件下载速率分析"; }
        }

        protected override void query()
        {
            queryFiles();
            Condition.FileInfos.Clear();
            Condition.FileInfos.AddRange(MainModel.FileInfos);
            TDDownLoadAnaByFile downLoadAnaByFile = new TDDownLoadAnaByFile(MainModel);
            downLoadAnaByFile.NeedJudgeTestPointByRegion = true;
            downLoadAnaByFile.SetQueryCondition(condition);
            downLoadAnaByFile.Query();
        }

        protected void queryFiles()
        {
            DIYQueryFileInfoByRegion queryFileByReg = new DIYQueryFileInfoByRegion(MainModel);
            queryFileByReg.IsShowFileInfoForm = false;
            queryFileByReg.SetQueryCondition(condition);
            queryFileByReg.Query();
        }
    }
}
