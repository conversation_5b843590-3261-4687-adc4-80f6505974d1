﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class SettingDlg_LTE
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.chkSaveTestPoint = new DevExpress.XtraEditors.CheckEdit();
            this.groupBoxAbsolute = new System.Windows.Forms.GroupBox();
            this.label4 = new System.Windows.Forms.Label();
            this.numAbsValue = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.groupBoxRelative = new System.Windows.Forms.GroupBox();
            this.numBandValue = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.label2 = new System.Windows.Forms.Label();
            this.groupBoxVaild = new System.Windows.Forms.GroupBox();
            this.chkAllRsrp = new DevExpress.XtraEditors.CheckEdit();
            this.label1 = new System.Windows.Forms.Label();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl6 = new DevExpress.XtraEditors.LabelControl();
            this.numAllRsrp = new DevExpress.XtraEditors.SpinEdit();
            this.numMaxValidValue = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl7 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl5 = new DevExpress.XtraEditors.LabelControl();
            this.groupBoxRemove = new System.Windows.Forms.GroupBox();
            this.freqBandControl1 = new MasterCom.RAMS.ZTFunc.FreqBandControl();
            this.checkEditMultiBandDiffFreq = new DevExpress.XtraEditors.CheckEdit();
            this.checkEditDualBandJT = new DevExpress.XtraEditors.CheckEdit();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.btnCancel = new DevExpress.XtraEditors.SimpleButton();
            this.btnOK = new DevExpress.XtraEditors.SimpleButton();
            this.toolStripDropDownFreq = new System.Windows.Forms.ToolStripDropDown();
            ((System.ComponentModel.ISupportInitialize)(this.chkSaveTestPoint.Properties)).BeginInit();
            this.groupBoxAbsolute.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numAbsValue.Properties)).BeginInit();
            this.groupBoxRelative.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numBandValue.Properties)).BeginInit();
            this.groupBoxVaild.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chkAllRsrp.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numAllRsrp.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxValidValue.Properties)).BeginInit();
            this.groupBoxRemove.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.checkEditMultiBandDiffFreq.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.checkEditDualBandJT.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // chkSaveTestPoint
            // 
            this.chkSaveTestPoint.Location = new System.Drawing.Point(55, 368);
            this.chkSaveTestPoint.Name = "chkSaveTestPoint";
            this.chkSaveTestPoint.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.chkSaveTestPoint.Properties.Appearance.Options.UseFont = true;
            this.chkSaveTestPoint.Properties.Caption = "保留采样点信息";
            this.chkSaveTestPoint.Size = new System.Drawing.Size(122, 19);
            this.chkSaveTestPoint.TabIndex = 40;
            // 
            // groupBoxAbsolute
            // 
            this.groupBoxAbsolute.Controls.Add(this.label4);
            this.groupBoxAbsolute.Controls.Add(this.numAbsValue);
            this.groupBoxAbsolute.Controls.Add(this.labelControl2);
            this.groupBoxAbsolute.Location = new System.Drawing.Point(263, 292);
            this.groupBoxAbsolute.Name = "groupBoxAbsolute";
            this.groupBoxAbsolute.Size = new System.Drawing.Size(210, 70);
            this.groupBoxAbsolute.TabIndex = 39;
            this.groupBoxAbsolute.TabStop = false;
            this.groupBoxAbsolute.Text = "绝对覆盖度";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label4.Location = new System.Drawing.Point(11, 17);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(65, 12);
            this.label4.TabIndex = 32;
            this.label4.Text = "信号强度≥";
            // 
            // numAbsValue
            // 
            this.numAbsValue.EditValue = new decimal(new int[] {
            105,
            0,
            0,
            -2147483648});
            this.numAbsValue.Location = new System.Drawing.Point(80, 13);
            this.numAbsValue.Name = "numAbsValue";
            this.numAbsValue.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numAbsValue.Properties.Appearance.Options.UseFont = true;
            this.numAbsValue.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numAbsValue.Properties.IsFloatValue = false;
            this.numAbsValue.Properties.Mask.EditMask = "N00";
            this.numAbsValue.Size = new System.Drawing.Size(96, 20);
            this.numAbsValue.TabIndex = 0;
            // 
            // labelControl2
            // 
            this.labelControl2.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl2.Appearance.Options.UseFont = true;
            this.labelControl2.Location = new System.Drawing.Point(183, 17);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(18, 12);
            this.labelControl2.TabIndex = 34;
            this.labelControl2.Text = "dBm";
            // 
            // groupBoxRelative
            // 
            this.groupBoxRelative.Controls.Add(this.numBandValue);
            this.groupBoxRelative.Controls.Add(this.labelControl1);
            this.groupBoxRelative.Controls.Add(this.label2);
            this.groupBoxRelative.Location = new System.Drawing.Point(17, 292);
            this.groupBoxRelative.Name = "groupBoxRelative";
            this.groupBoxRelative.Size = new System.Drawing.Size(240, 70);
            this.groupBoxRelative.TabIndex = 38;
            this.groupBoxRelative.TabStop = false;
            this.groupBoxRelative.Text = "相对覆盖带";
            // 
            // numBandValue
            // 
            this.numBandValue.EditValue = new decimal(new int[] {
            6,
            0,
            0,
            0});
            this.numBandValue.Location = new System.Drawing.Point(119, 12);
            this.numBandValue.Name = "numBandValue";
            this.numBandValue.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numBandValue.Properties.Appearance.Options.UseFont = true;
            this.numBandValue.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numBandValue.Properties.IsFloatValue = false;
            this.numBandValue.Properties.Mask.AutoComplete = DevExpress.XtraEditors.Mask.AutoCompleteType.None;
            this.numBandValue.Properties.Mask.EditMask = "N00";
            this.numBandValue.Size = new System.Drawing.Size(96, 20);
            this.numBandValue.TabIndex = 0;
            // 
            // labelControl1
            // 
            this.labelControl1.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl1.Appearance.Options.UseFont = true;
            this.labelControl1.Location = new System.Drawing.Point(219, 16);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(12, 12);
            this.labelControl1.TabIndex = 29;
            this.labelControl1.Text = "dB";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label2.Location = new System.Drawing.Point(15, 16);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(101, 12);
            this.label2.TabIndex = 32;
            this.label2.Text = "与最强信号差异≤";
            // 
            // groupBoxVaild
            // 
            this.groupBoxVaild.Controls.Add(this.chkAllRsrp);
            this.groupBoxVaild.Controls.Add(this.label1);
            this.groupBoxVaild.Controls.Add(this.labelControl4);
            this.groupBoxVaild.Controls.Add(this.labelControl6);
            this.groupBoxVaild.Controls.Add(this.numAllRsrp);
            this.groupBoxVaild.Controls.Add(this.numMaxValidValue);
            this.groupBoxVaild.Controls.Add(this.labelControl7);
            this.groupBoxVaild.Controls.Add(this.labelControl5);
            this.groupBoxVaild.Location = new System.Drawing.Point(17, 169);
            this.groupBoxVaild.Name = "groupBoxVaild";
            this.groupBoxVaild.Size = new System.Drawing.Size(456, 115);
            this.groupBoxVaild.TabIndex = 37;
            this.groupBoxVaild.TabStop = false;
            this.groupBoxVaild.Text = "计算有效性";
            // 
            // chkAllRsrp
            // 
            this.chkAllRsrp.Location = new System.Drawing.Point(22, 55);
            this.chkAllRsrp.Name = "chkAllRsrp";
            this.chkAllRsrp.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.chkAllRsrp.Properties.Appearance.Options.UseFont = true;
            this.chkAllRsrp.Properties.Caption = "信号强度≥";
            this.chkAllRsrp.Size = new System.Drawing.Size(97, 19);
            this.chkAllRsrp.TabIndex = 4;
            this.chkAllRsrp.CheckedChanged += new System.EventHandler(this.chkAllRsrp_CheckedChanged);
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label1.Location = new System.Drawing.Point(15, 17);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(89, 12);
            this.label1.TabIndex = 0;
            this.label1.Text = "最强信号强度≥";
            // 
            // labelControl4
            // 
            this.labelControl4.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl4.Appearance.ForeColor = System.Drawing.Color.Red;
            this.labelControl4.Appearance.Options.UseFont = true;
            this.labelControl4.Appearance.Options.UseForeColor = true;
            this.labelControl4.Location = new System.Drawing.Point(244, 59);
            this.labelControl4.Name = "labelControl4";
            this.labelControl4.Size = new System.Drawing.Size(204, 12);
            this.labelControl4.TabIndex = 25;
            this.labelControl4.Text = "（参与运算的小区场强需满足此条件）";
            // 
            // labelControl6
            // 
            this.labelControl6.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl6.Appearance.ForeColor = System.Drawing.Color.Red;
            this.labelControl6.Appearance.Options.UseFont = true;
            this.labelControl6.Appearance.Options.UseForeColor = true;
            this.labelControl6.Location = new System.Drawing.Point(244, 17);
            this.labelControl6.Name = "labelControl6";
            this.labelControl6.Size = new System.Drawing.Size(216, 12);
            this.labelControl6.TabIndex = 25;
            this.labelControl6.Text = "（不符合该条件的采样点，视为无效点）";
            // 
            // numAllRsrp
            // 
            this.numAllRsrp.EditValue = new decimal(new int[] {
            105,
            0,
            0,
            -2147483648});
            this.numAllRsrp.Location = new System.Drawing.Point(119, 54);
            this.numAllRsrp.Name = "numAllRsrp";
            this.numAllRsrp.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numAllRsrp.Properties.Appearance.Options.UseFont = true;
            this.numAllRsrp.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numAllRsrp.Properties.IsFloatValue = false;
            this.numAllRsrp.Properties.Mask.EditMask = "N00";
            this.numAllRsrp.Properties.MaxValue = new decimal(new int[] {
            10,
            0,
            0,
            -2147483648});
            this.numAllRsrp.Properties.MinValue = new decimal(new int[] {
            141,
            0,
            0,
            -2147483648});
            this.numAllRsrp.Size = new System.Drawing.Size(96, 20);
            this.numAllRsrp.TabIndex = 1;
            // 
            // numMaxValidValue
            // 
            this.numMaxValidValue.EditValue = new decimal(new int[] {
            105,
            0,
            0,
            -2147483648});
            this.numMaxValidValue.Location = new System.Drawing.Point(119, 13);
            this.numMaxValidValue.Name = "numMaxValidValue";
            this.numMaxValidValue.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numMaxValidValue.Properties.Appearance.Options.UseFont = true;
            this.numMaxValidValue.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numMaxValidValue.Properties.IsFloatValue = false;
            this.numMaxValidValue.Properties.Mask.EditMask = "N00";
            this.numMaxValidValue.Properties.MaxValue = new decimal(new int[] {
            10,
            0,
            0,
            -2147483648});
            this.numMaxValidValue.Properties.MinValue = new decimal(new int[] {
            141,
            0,
            0,
            -2147483648});
            this.numMaxValidValue.Size = new System.Drawing.Size(96, 20);
            this.numMaxValidValue.TabIndex = 1;
            // 
            // labelControl7
            // 
            this.labelControl7.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl7.Appearance.Options.UseFont = true;
            this.labelControl7.Location = new System.Drawing.Point(222, 59);
            this.labelControl7.Name = "labelControl7";
            this.labelControl7.Size = new System.Drawing.Size(18, 12);
            this.labelControl7.TabIndex = 33;
            this.labelControl7.Text = "dBm";
            // 
            // labelControl5
            // 
            this.labelControl5.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl5.Appearance.Options.UseFont = true;
            this.labelControl5.Location = new System.Drawing.Point(222, 17);
            this.labelControl5.Name = "labelControl5";
            this.labelControl5.Size = new System.Drawing.Size(18, 12);
            this.labelControl5.TabIndex = 33;
            this.labelControl5.Text = "dBm";
            // 
            // groupBoxRemove
            // 
            this.groupBoxRemove.BackColor = System.Drawing.Color.Transparent;
            this.groupBoxRemove.Controls.Add(this.freqBandControl1);
            this.groupBoxRemove.Controls.Add(this.checkEditMultiBandDiffFreq);
            this.groupBoxRemove.Controls.Add(this.checkEditDualBandJT);
            this.groupBoxRemove.Location = new System.Drawing.Point(17, 16);
            this.groupBoxRemove.Name = "groupBoxRemove";
            this.groupBoxRemove.Size = new System.Drawing.Size(456, 137);
            this.groupBoxRemove.TabIndex = 36;
            this.groupBoxRemove.TabStop = false;
            this.groupBoxRemove.Text = "剔除规则";
            // 
            // freqBandControl1
            // 
            this.freqBandControl1.ChkFreqBandChange_click = null;
            this.freqBandControl1.Location = new System.Drawing.Point(184, 10);
            this.freqBandControl1.Name = "freqBandControl1";
            this.freqBandControl1.Size = new System.Drawing.Size(204, 124);
            this.freqBandControl1.TabIndex = 2;
            // 
            // checkEditMultiBandDiffFreq
            // 
            this.checkEditMultiBandDiffFreq.Location = new System.Drawing.Point(15, 97);
            this.checkEditMultiBandDiffFreq.Name = "checkEditMultiBandDiffFreq";
            this.checkEditMultiBandDiffFreq.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.checkEditMultiBandDiffFreq.Properties.Appearance.Options.UseFont = true;
            this.checkEditMultiBandDiffFreq.Properties.Caption = "多层网剔除异频点";
            this.checkEditMultiBandDiffFreq.Size = new System.Drawing.Size(138, 19);
            this.checkEditMultiBandDiffFreq.TabIndex = 1;
            this.checkEditMultiBandDiffFreq.CheckedChanged += new System.EventHandler(this.checkEditMultiBandDiffFreq_CheckedChanged);
            // 
            // checkEditDualBandJT
            // 
            this.checkEditDualBandJT.Location = new System.Drawing.Point(15, 20);
            this.checkEditDualBandJT.Name = "checkEditDualBandJT";
            this.checkEditDualBandJT.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.checkEditDualBandJT.Properties.Appearance.Options.UseFont = true;
            this.checkEditDualBandJT.Properties.Caption = "多层网剔除(集团)";
            this.checkEditDualBandJT.Size = new System.Drawing.Size(138, 19);
            this.checkEditDualBandJT.TabIndex = 0;
            this.checkEditDualBandJT.CheckedChanged += new System.EventHandler(this.checkEditTwoEarfcn_CheckedChanged);
            // 
            // labelControl3
            // 
            this.labelControl3.Appearance.ForeColor = System.Drawing.Color.Red;
            this.labelControl3.Appearance.Options.UseForeColor = true;
            this.labelControl3.Location = new System.Drawing.Point(173, 370);
            this.labelControl3.Name = "labelControl3";
            this.labelControl3.Size = new System.Drawing.Size(168, 14);
            this.labelControl3.TabIndex = 43;
            this.labelControl3.Text = "（可导出采样点，内存占用大）";
            // 
            // btnCancel
            // 
            this.btnCancel.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnCancel.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnCancel.Appearance.Options.UseFont = true;
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnCancel.Location = new System.Drawing.Point(379, 400);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(87, 27);
            this.btnCancel.TabIndex = 42;
            this.btnCancel.Text = "取消";
            // 
            // btnOK
            // 
            this.btnOK.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnOK.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnOK.Appearance.Options.UseFont = true;
            this.btnOK.Location = new System.Drawing.Point(272, 400);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(87, 27);
            this.btnOK.TabIndex = 41;
            this.btnOK.Text = "确定";
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // toolStripDropDownFreq
            // 
            this.toolStripDropDownFreq.LayoutStyle = System.Windows.Forms.ToolStripLayoutStyle.Flow;
            this.toolStripDropDownFreq.Name = "toolStripDropDown1";
            this.toolStripDropDownFreq.Size = new System.Drawing.Size(2, 4);
            // 
            // SettingDlg_LTE
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(487, 437);
            this.Controls.Add(this.chkSaveTestPoint);
            this.Controls.Add(this.groupBoxAbsolute);
            this.Controls.Add(this.groupBoxRelative);
            this.Controls.Add(this.groupBoxVaild);
            this.Controls.Add(this.groupBoxRemove);
            this.Controls.Add(this.labelControl3);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.Name = "SettingDlg_LTE";
            this.Text = "重叠覆盖度计算设置";
            this.Load += new System.EventHandler(this.SettingDlg_LTENew_Load);
            ((System.ComponentModel.ISupportInitialize)(this.chkSaveTestPoint.Properties)).EndInit();
            this.groupBoxAbsolute.ResumeLayout(false);
            this.groupBoxAbsolute.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numAbsValue.Properties)).EndInit();
            this.groupBoxRelative.ResumeLayout(false);
            this.groupBoxRelative.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numBandValue.Properties)).EndInit();
            this.groupBoxVaild.ResumeLayout(false);
            this.groupBoxVaild.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chkAllRsrp.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numAllRsrp.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxValidValue.Properties)).EndInit();
            this.groupBoxRemove.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.checkEditMultiBandDiffFreq.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.checkEditDualBandJT.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraEditors.CheckEdit chkSaveTestPoint;
        private System.Windows.Forms.GroupBox groupBoxAbsolute;
        private System.Windows.Forms.Label label4;
        private DevExpress.XtraEditors.SpinEdit numAbsValue;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private System.Windows.Forms.GroupBox groupBoxRelative;
        private DevExpress.XtraEditors.SpinEdit numBandValue;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.GroupBox groupBoxVaild;
        private DevExpress.XtraEditors.CheckEdit chkAllRsrp;
        private System.Windows.Forms.Label label1;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private DevExpress.XtraEditors.LabelControl labelControl6;
        private DevExpress.XtraEditors.SpinEdit numAllRsrp;
        private DevExpress.XtraEditors.SpinEdit numMaxValidValue;
        private DevExpress.XtraEditors.LabelControl labelControl7;
        private DevExpress.XtraEditors.LabelControl labelControl5;
        private System.Windows.Forms.GroupBox groupBoxRemove;
        private DevExpress.XtraEditors.CheckEdit checkEditMultiBandDiffFreq;
        private DevExpress.XtraEditors.CheckEdit checkEditDualBandJT;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.SimpleButton btnCancel;
        private DevExpress.XtraEditors.SimpleButton btnOK;
        private System.Windows.Forms.ToolStripDropDown toolStripDropDownFreq;
        private FreqBandControl freqBandControl1;
    }
}