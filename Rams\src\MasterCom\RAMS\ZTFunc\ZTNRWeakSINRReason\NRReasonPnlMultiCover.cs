﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NRReasonPnlMultiCover : NRReasonPanelBase
    {
        public NRReasonPnlMultiCover()
        {
            InitializeComponent();
        }

        public override void AttachReason(NRReasonBase reason)
        {
            base.AttachReason(reason);
            numDiffMax.ValueChanged -= numDiffMax_ValueChanged;
            numMinNum.ValueChanged -= numMinNum_ValueChanged;
            numRSRPMin.ValueChanged -= numRSRPMin_ValueChanged;
            numDiffMax.Value = (decimal)((NRReasonMultiCover)reason).RSRPDiffMax;
            numMinNum.Value = (decimal)((NRReasonMultiCover)reason).MultiNumMin;
            numRSRPMin.Value = (decimal)((NRReasonMultiCover)reason).RSRSMin;
            numDiffMax.ValueChanged += numDiffMax_ValueChanged;
            numMinNum.ValueChanged += numMinNum_ValueChanged;
            numRSRPMin.ValueChanged += numRSRPMin_ValueChanged;
        }

        void numRSRPMin_ValueChanged(object sender, EventArgs e)
        {
            ((NRReasonMultiCover)reason).RSRSMin = (float)numRSRPMin.Value;
        }

        void numMinNum_ValueChanged(object sender, EventArgs e)
        {
            ((NRReasonMultiCover)reason).MultiNumMin = (int)numMinNum.Value;
        }

        void numDiffMax_ValueChanged(object sender, EventArgs e)
        {
            ((NRReasonMultiCover)reason).RSRPDiffMax = (float)numDiffMax.Value;
        }
    }
}
