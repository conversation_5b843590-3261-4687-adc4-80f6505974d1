﻿using CQTLibrary.RAMS.NET;
using MasterCom.RAMS.BackgroundFunc;
using MasterCom.RAMS.Frame;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc.ZTStationAcceptance_XJ;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Net;
using System.Text;
using System.Windows.Forms;
using System.Xml;
using ClientProxy = MasterCom.RAMS.Net.ClientProxy;
using ColumnDefItem = MasterCom.RAMS.Model.Interface.ColumnDefItem;
using Command = MasterCom.RAMS.Net.Command;
using ConnectResult = MasterCom.RAMS.Net.ConnectResult;
using E_VType = MasterCom.RAMS.Model.Interface.E_VType;
using FileInfo = MasterCom.RAMS.Model.FileInfo;
using MainModel = MasterCom.RAMS.Model.MainModel;
using Package = MasterCom.RAMS.Net.Package;
using RequestType = MasterCom.RAMS.Net.RequestType;
using ResponseType = MasterCom.RAMS.Net.ResponseType;
using SubCommand = MasterCom.RAMS.Net.SubCommand;
using WaitBox = CQTLibrary.RAMS.NET.WaitBox;

namespace MasterCom.RAMS.ZTFunc
{
    public class NROptimizationcCusters_700M : NROptimizationcCustersBase
    {
        public NROptimizationcCusters_700M(MainModel mainModel)
        {
        }

        public override string Name
        {
            get
            {
                return "700M 5G簇优化统计";
            }
        }

        protected override bool isValidCondition()
        {
            List<int> stimeList = new List<int>();
            List<int> etimeList = new List<int>();
            List<TimePeriod> tpList = new List<TimePeriod>();
            List<string> names = new List<string>();
            List<FileInfo> nrFiles = new List<FileInfo>();
            foreach (var file in Condition.FileInfos)
            {
                ServiceName name = ServiceTypeManager.getServiceNameFromTypeID(file.ServiceType);
                if (name == ServiceName.NR && judgeFiles(file, names))
                {
                    nrFiles.Add(file);
                }
                else
                {
                    return false;
                }

                stimeList.Add(file.BeginTime);
                etimeList.Add(file.EndTime);
                tpList.Add(new TimePeriod(GetDateTime(file.BeginTime), GetDateTime(file.EndTime)));
                condition.Periods = tpList;
                condition.ServiceTypes.Add(file.ServiceType);
                condition.Projects.Add(file.ProjectID);
                condition.CarrierTypes.Add(file.CarrierType);
                Condition.FileInfos = nrFiles;
            }

            if (nrFiles.Count == 0)
            {
                MessageBox.Show("请选择5G文件进行簇优化统计");
                return false;
            }

            FolderBrowserDialog dlg = new FolderBrowserDialog();
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                this.SaveFolder = dlg.SelectedPath;
                this.custersName = Condition.FileInfos[0].Name.Split(new char[] { '_' })[0];
                NRDistributionPic nRPicture = new NRDistributionPic(mainModel, condition);
                nRPicture.CreatePicture(names[0]);
                return true;
            }
            return false;
        }

        /// <summary>  
        /// 时间戳Timestamp转换成日期  
        /// </summary>  
        /// <param name="timeStamp"></param>  
        /// <returns></returns>  
        private DateTime GetDateTime(int timeStamp)
        {
            DateTime dtStart = TimeZone.CurrentTimeZone.ToLocalTime(new DateTime(1970, 1, 1));
            long lTime = ((long)timeStamp * 10000000);
            TimeSpan toNow = new TimeSpan(lTime);
            DateTime targetDt = dtStart.Add(toNow);

            if (string.IsNullOrEmpty(targetDt.ToString()))
            {
                targetDt = DateTime.Now;
            }

            return targetDt;
        }

        /// <summary>
        /// 判断所选文件是否是同一个簇的文件
        /// </summary>
        /// <param name="file"></param>
        /// <param name="names"></param>
        /// <returns></returns>
        public bool judgeFiles(FileInfo file, List<string> names)
        {
            string[] clusters = file.Name.Split(new char[] { '_' });
            if (clusters.Length > 0)
            {
                if (names.Count == 0)
                {
                    names.Add(clusters[0]);
                }
                else
                {
                    if (names.Contains(clusters[0]))
                    {
                        names.Add(clusters[0]);
                    }
                    else
                    {
                        MessageBox.Show("请选择相同簇的5G文件进行簇优化统计");
                        return false;
                    }
                }
            }
            else
            {
                MessageBox.Show("请选择簇文件进行簇优化统计");
                return false;
            }

            return true;
        }
    }

    public class NRDistributionPic : NR700MOptimizationcCustersBaseInfo
    {
        NRStationAcceptCondition nRStationAcceptCondition = new NRStationAcceptCondition();
        public NRDistributionPic(MainModel mainModel)
: base(mainModel)
        {
        }

        public NRDistributionPic(MainModel mainModel, QueryCondition queryCondition)
: base(mainModel)
        {
            condition = queryCondition;
        }

        public void CreatePicture(string clusterName)
        {
            isValidCondition();
        }

        protected override bool isValidCondition()
        {
            //回放所有文件文件
            initManager(null);
            NR700MOptimizationcCustersBaseInfo nR700MOptimizationcCustersBaseInfo = new NR700MOptimizationcCustersBaseInfo(mainModel);
            nR700MOptimizationcCustersBaseInfo.FindSampleQuery(condition);

            //根据事件查询
            NR700MOptimizationcCustersEvent nR700MOptimizationcCustersEvent = new NR700MOptimizationcCustersEvent(mainModel);
            nR700MOptimizationcCustersEvent.FindEventQuery(condition);
            return true;
        }

        protected override void initManager(StationAcceptConditionBase cond)
        {
            string workDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "userdata/NROptimizationcCusters");
            string picPath = Path.Combine(workDir, "Pictures");
            List<StationAcceptBase> acceptorList = new List<StationAcceptBase>()
            {
                 new NRAcpCoverPic(picPath),
                 new NRAcpHandoverPic(picPath)
            };
        }

        protected override List<string> queryColumns
        {
            get
            {
                return new List<string>()
                {
                     "isampleid",
                     "itime",
                     "ilongitude",
                     "ilatitude",
                     "NR_SSB_ARFCN",
                     "NR_PCI",
                     "NR_SS_RSRP",
                     "NR_SS_SINR",
                     "NR_Throughput_MAC_DL",
                     "NR_Throughput_MAC_UL",
                     "NR_lte_TAC",
                     "NR_lte_ECI",
                     "NR_lte_EARFCN",
                     "NR_lte_PCI",
                     "NR_lte_RSRP",
                     "NR_lte_SINR",
                };
            }
        }

        protected override DIYReplayContentOption getDIYReplayContent()
        {
            DIYReplayContentOption option = new DIYReplayContentOption();
            option.DefaultSerialThemeName = null;

            foreach (string col in queryColumns)
            {
                List<ColumnDefItem> items = InterfaceManager.GetInstance().GetColumnDefByShowName(col);
                option.SampleColumns.AddRange(items);
            }

            option.EventInclude = true;
            option.MessageInclude = true;

            return option;
        }

        protected override void queryReplayInfo(ClientProxy clientProxy, Package package, FileInfo fileInfo)
        {
            base.queryReplayInfo(clientProxy, package, fileInfo);

        }

        protected string picFolderPath { get; set; }
        protected string curBtsPicFolderPath { get; set; }

        class NRAcpCoverPic : NRStationAcceptCoverPic
        {
            public NRAcpCoverPic(string picPath)
                : base(picPath)
            {
                reSetMapView("NR_SS_RSRP", new Func(getRsrpRanges), "");
                reSetMapView("NR_SS_SINR", new Func(getSinrRanges), "");
                reSetMapView("NR_lte_RSRP", new Func(getRsrpRanges), "");
                reSetMapView("NR_lte_SINR", new Func(getSinrRanges), "");
                reSetMapView("NR_Throughput_MAC_DL_Mb", new Func(getDLRanges), "");
                reSetMapView("NR_Throughput_MAC_UL_Mb", new Func(getULRanges), "");
            }

            protected List<RangeInfo> getRsrpRanges(string band)
            {
                List<RangeInfo> ranges = new List<RangeInfo>();
                ranges.Add(new RangeInfo(false, false, -156, -110, Color.Red));
                ranges.Add(new RangeInfo(true, false, -110, -100, Color.Magenta));
                ranges.Add(new RangeInfo(true, false, -100, -93, Color.Yellow));
                ranges.Add(new RangeInfo(true, false, -93, -85, Color.Cyan));
                ranges.Add(new RangeInfo(true, false, -85, -70, Color.Green));
                ranges.Add(new RangeInfo(true, false, -70, -31, Color.Blue));
                return ranges;
            }

            protected List<RangeInfo> getSinrRanges(string band)
            {
                List<RangeInfo> ranges = new List<RangeInfo>();
                ranges.Add(new RangeInfo(false, false, -23, 15, Color.Red));
                ranges.Add(new RangeInfo(true, false, 15, 3, Color.Magenta));
                ranges.Add(new RangeInfo(true, false, -3, 3, Color.Yellow));
                ranges.Add(new RangeInfo(true, false, 3, 15, Color.Cyan));
                ranges.Add(new RangeInfo(true, false, 15, 25, Color.Green));
                ranges.Add(new RangeInfo(true, false, 25, 50, Color.Blue));
                return ranges;
            }

            protected List<RangeInfo> getDLRanges(string band)
            {
                List<RangeInfo> ranges = new List<RangeInfo>();
                switch (band)
                {
                    default:
                        ranges.Add(new RangeInfo(false, false, 0, 100, Color.Red));
                        ranges.Add(new RangeInfo(true, false, 100, 250, Color.Magenta));
                        ranges.Add(new RangeInfo(true, false, 250, 550, Color.Yellow));
                        ranges.Add(new RangeInfo(true, false, 550, 750, Color.Cyan));
                        ranges.Add(new RangeInfo(true, false, 750, 1000, Color.Green));
                        ranges.Add(new RangeInfo(true, false, 1000, 2100, Color.Blue));
                        break;
                }
                return ranges;
            }

            protected List<RangeInfo> getULRanges(string band)
            {
                List<RangeInfo> ranges = new List<RangeInfo>();
                switch (band)
                {
                    default:
                        ranges.Add(new RangeInfo(false, false, 0, 10, Color.Red));
                        ranges.Add(new RangeInfo(true, false, 10, 25, Color.Magenta));
                        ranges.Add(new RangeInfo(true, false, 25, 45, Color.Yellow));
                        ranges.Add(new RangeInfo(true, false, 45, 70, Color.Cyan));
                        ranges.Add(new RangeInfo(true, false, 70, 100, Color.Green));
                        ranges.Add(new RangeInfo(true, false, 100, 11000, Color.Blue));
                        break;
                }
                return ranges;
            }

            protected override bool isValidFile(FileInfo fileInfo)
            {
                return fileInfo.Name.Contains("DT上传") || fileInfo.Name.Contains("DT下载");
            }

            protected override void analyzeFile(FileInfo fileInfo, DTFileDataManager fileManager, NRBtsInfo bts, NRCellServiceInfo cellTypeInfo, NRStationAcceptCondition condition)
            {

            }
        }

        class NRAcpHandoverPic : StationAcceptHanOverPic
        {
            public NRAcpHandoverPic(string picPath)
                : base(picPath)
            {

            }

            private const string pciParamName = "NR_PCI";

            protected override bool isValidFile(FileInfo fileInfo)
            {
                return fileInfo.Name.Contains("切换");
            }

            protected override void analyzeFile(FileInfo fileInfo, DTFileDataManager fileManager, BtsInfoBase bts, CellInfoBase cell, StationAcceptConditionBase condition)
            {
                NRBtsInfo nrBts = bts as NRBtsInfo;
                NRStationAcceptCondition nrCondition = condition as NRStationAcceptCondition;
                reSetPCIMapView(nrBts.Bts, nrBts.CurFileBtsName, pciParamName);

                MTGis.DbRect bounds;
                if (nrBts.CurFileBtsName == nrBts.BtsName)
                {
                    bounds = getCoverBounds(fileManager, nrBts.Bts.Longitude, nrBts.Bts.Latitude);
                }
                else
                {
                    bounds = getCoverBounds(fileManager);
                }

                MapForm mf = initMap(bounds);
                MainModel.GetInstance().DrawDifferentServerColor = true;
                if (nrCondition.NRServiceType == NRServiceName.NSA)
                {
                    if (!nrBts.NSABtsInfo.FileBtsHandOverInfoDic.TryGetValue(nrBts.CurFileBtsName, out BtsHandoverInfo info))
                    {
                        info = new BtsHandoverInfo(nrBts.CurFileBtsName);
                        nrBts.NSABtsInfo.FileBtsHandOverInfoDic.Add(nrBts.CurFileBtsName, info);
                    }

                    string path = getCoverPicPath(nrBts.BtsName, "NSA_" + nrBts.CurFileBtsName, pciParamName);
                    info.PCIPicInfo.PicPath = fireMapAndTakePic(mf, pciParamName, path);
                }
                else if (nrCondition.NRServiceType == NRServiceName.SA)
                {
                    if (!nrBts.SABtsInfo.FileBtsHandOverInfoDic.TryGetValue(nrBts.CurFileBtsName, out BtsHandoverInfo info))
                    {
                        info = new BtsHandoverInfo(nrBts.CurFileBtsName);
                        nrBts.SABtsInfo.FileBtsHandOverInfoDic.Add(nrBts.CurFileBtsName, info);
                    }

                    string path = getCoverPicPath(nrBts.BtsName, "SA_" + nrBts.CurFileBtsName, pciParamName);
                    info.PCIPicInfo.PicPath = fireMapAndTakePic(mf, pciParamName, path);
                }

                MainModel.GetInstance().DrawDifferentServerColor = false;
            }

            protected override string fireMapAndTakePic(MapForm mf, string paramName, string filePath)
            {
                //要让小区显示在采样点之上
                var nrLayer = mf.GetNRCellLayer();
                mf.MakeSureCustomLayerVisible(nrLayer, true);

                return base.fireMapAndTakePic(mf, paramName, filePath);
            }

            protected override List<ICell> getValidCells(ISite bts, string btsNamePostfix)
            {
                List<ICell> cellList = new List<ICell>();
                if (bts is NRBTS)
                {
                    NRBTS nrBts = bts as NRBTS;
                    foreach (NRCell cell in nrBts.Cells)
                    {
                        if (cell.Name.Contains(btsNamePostfix))
                        {
                            cellList.Add(cell);
                        }
                    }
                }
                return cellList;
            }

            protected override List<int> getPCIList(List<ICell> cellList)
            {
                List<int> pciList = new List<int>();
                foreach (ICell cell in cellList)
                {
                    NRCell nrCell = cell as NRCell;
                    pciList.Add(nrCell.PCI);
                }
                return pciList;
            }

            protected override void setServerCellColorByPCI(List<ICell> cellList, List<RangeInfo> ranges)
            {
                foreach (ICell cell in cellList)
                {
                    NRCell nrCell = cell as NRCell;
                    foreach (RangeInfo range in ranges)
                    {
                        if (nrCell.PCI >= range.Min && nrCell.PCI < range.Max)
                        {
                            nrCell.ServerCellColor = range.RangeColor;
                            break;
                        }
                    }
                }
            }
        }
    }

    public class NR700MOptimizationcCustersBaseInfo : DIYReplayFileQuery
    {
        protected StationAcceptManagerBase manager;

        public NR700MOptimizationcCustersBaseInfo(MainModel mainModel)
: base(mainModel)
        {

        }

        public void FindSampleQuery(QueryCondition Condition)
        {
            this.condition = Condition;
            query();
            //截图
            CutPic("NR_lte_RSRP", Condition.FileInfos[0].Name.Split(new char[] { '_' })[0]);
            CutPic("NR_lte_SINR", Condition.FileInfos[0].Name.Split(new char[] { '_' })[0]);
            CutPic("NR_SS_RSRP", Condition.FileInfos[0].Name.Split(new char[] { '_' })[0]);
            CutPic("NR_SS_SINR", Condition.FileInfos[0].Name.Split(new char[] { '_' })[0]);
            CutPic("NR_Throughput_MAC_DL_Mb", Condition.FileInfos[0].Name.Split(new char[] { '_' })[0]);
            CutPic("NR_Throughput_MAC_UL_Mb", Condition.FileInfos[0].Name.Split(new char[] { '_' })[0]);
        }

        protected string dirPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, @"userdata\NROptimizationcCusters\Pictures");
        public void CutPic(string themname, string clustername)
        {
            string path = Path.Combine(dirPath, clustername);
            if (!System.IO.Directory.Exists(path))
            {
                System.IO.Directory.CreateDirectory(path);
            }

            string picPath = System.IO.Path.Combine(path, themname + ".jpg");

            MapForm mapForm = mainModel.MainForm.GetMapForm();
            mapForm.FireAndOutputCurMapToPic(themname, false, picPath);
        }

        DIYReplayOptionDlg replayOptionDlg = null;
        protected new DIYReplayContentOption replayContentOption = null;
        protected override DIYReplayContentOption getDIYReplayContent()
        {
            if (replayOptionDlg == null)
            {
                replayOptionDlg = new DIYReplayOptionDlg();
                replayOptionDlg.InitLoadInfo("", "隐藏");
            }
            if (Condition.FileInfos.Count > 0)
            {
                int svtype = Condition.FileInfos[0].ServiceType;
                replayOptionDlg.FillCurrentServiceType(svtype);
            }

            isAutoLoadCQTPicture = replayOptionDlg.IsAutoLoadCQTPicture;
            return replayOptionDlg.GetSelectedReplayOption();
        }

        protected override void query()//回放查询入口
        {
            replayContentOption = getDIYReplayContent();//获取查询指标（内容）
            if (replayContentOption == null)
            {
                return;
            }
            WaitBox.CanCancel = true;
            WaitBox.Text = "正在查询...";
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, condition.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
                WaitBox.Text = "正在查询...";
                MainModel.DTDataManager.Clear();
                MainModel.SelectedTestPoints.Clear();
                MainModel.SelectedEvents.Clear();
                MainModel.SelectedMessage = null;
                MainModel.IsDrawEventResult = false;
                MainModel.IsFileReplayByCompareMode = Condition.isCompareMode;
                MainModel.IsFileReplayByMTRMode = condition.isMTRMode;
                MainModel.IsFileReplayByMTRToLogMode = condition.isMTRToLogMode;
                WaitBox.Show("开始统计数据...", queryInThread, clientProxy);
                doPostReplayAction();
                if (replayContentOption.DefaultSerialThemeName != null)
                {
                    MainModel.FireSetDefaultMapSerialTheme(replayContentOption.DefaultSerialThemeName);
                }
            }
            finally
            {
                MainModel.FireDTDataChanged(this);
                MainModel.RefreshLegend();
                clientProxy.Close();
            }
        }

        protected override void fillContentNeeded_Sample(Package package)
        {
            if (replayContentOption == null)
            {
                throw (new Exception("没有选择的参数指标！"));
            }
            List<ColumnDefItem> colDefList = getNeededColumnDefList(replayContentOption);
            StringBuilder sbuilder = new StringBuilder();
            Dictionary<string, bool> tempDic = new Dictionary<string, bool>();
            for (int i = 0; i < colDefList.Count; i++)
            {
                ColumnDefItem sid = colDefList[i];
                string id = sid.GetTriIdStrIgnoreTable();
                if (tempDic.ContainsKey(id))
                {
                    continue;
                }
                tempDic[id] = true;
                sbuilder.Append(id);
                sbuilder.Append(",");
            }
            package.Content.AddParam(sbuilder.ToString().TrimEnd(','));
        }

        private List<ColumnDefItem> getNeededColumnDefList(DIYReplayContentOption replayOption)
        {
            List<ColumnDefItem> retList = new List<ColumnDefItem>();
            Dictionary<string, ColumnDefItem> triIdTofixColumnsDic = new Dictionary<string, ColumnDefItem>();
            Dictionary<string, ColumnDefItem> tbIdAndImgIdToNonFixColumnsDic = new Dictionary<string, ColumnDefItem>();
            foreach (ColumnDefItem columnDef in replayOption.SampleColumns)
            {
                if (columnDef == null)
                {
                    continue;
                }
                if (columnDef.fix)
                {
                    triIdTofixColumnsDic[columnDef.GetTriIdStr()] = columnDef;
                }
                else
                {
                    string colDicKey = columnDef.tableName + "," + columnDef.imgID;
                    tbIdAndImgIdToNonFixColumnsDic[colDicKey] = columnDef;
                }
            }
            foreach (ColumnDefItem col in triIdTofixColumnsDic.Values)
            {
                retList.Add(col);
            }
            foreach (ColumnDefItem col in tbIdAndImgIdToNonFixColumnsDic.Values)
            {
                retList.Add(col);
            }
            return retList;
        }

        protected override void queryReplayInfo(ClientProxy clientProxy, Package package, FileInfo fileInfo)//选择好条件后查询回放信息
        {
            //sample
            prepareStatPackage_Sample_FileFilter(package, fileInfo);
            prepareStatPackage_Sample_SampleFilter(package);
            fillContentNeeded_Sample(package);
            clientProxy.Send();
            recieveInfo_Sample(clientProxy);
        }

        protected override void queryInThread(object o)
        {
            try
            {
                ClientProxy clientProxy = (ClientProxy)o;
                Package package = clientProxy.Package;
                int index = 0;
                MainModel.SelectedFileInfo = Condition.FileInfos[0];
                foreach (MasterCom.RAMS.Model.FileInfo fileInfo in Condition.FileInfos)
                {
                    if (IsMutCitys)//支持同时回放多地市的文件
                    {
                        clientProxy.Close();
                        clientProxy = new ClientProxy();
                        if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName
                            , MainModel.User.Password, fileInfo.DistrictID) != ConnectResult.Success)
                        {
                            ErrorInfo = fileInfo.Name + " 回放失败，其余文件正在继续!!!";
                            continue;
                        }
                        package = clientProxy.Package;
                    }
                    setOriginTypeByFileType(fileInfo);
                    setMTRMode(index, fileInfo);
                    index++;
                    fileIndex++;
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                    WaitBox.Text = "开始获取[" + fileInfo.Name + "]数据...";
                    queryReplayInfo(clientProxy, package, fileInfo);
                }
            }
            catch (DebugAssertException dbgE)
            {
                MessageBox.Show("Debug Assert 失败:" + dbgE.ToString());
            }
            catch (Exception ex)
            {
                log.Error(ex.StackTrace);
            }
            finally
            {
                WaitBox.Close();
            }
        }

        private void setOriginTypeByFileType(FileInfo fileInfo)
        {
            if (fileInfo.FileTypeDescription.Contains("鼎利"))
            {//鼎利测试文件以左下角为坐标原点
                MainModel.CQTPlanImgOrigin = MainModel.OriginType.LeftBottom;
            }
            else if (fileInfo.FileTypeDescription.Contains("烽火"))
            {//烽火测试文件以左上角为坐标原点
                MainModel.CQTPlanImgOrigin = MainModel.OriginType.LeftTop;
            }
        }

        private void setMTRMode(int index, FileInfo fileInfo)
        {
            if (MainModel.IsFileReplayByMTRMode)
            {
                if (index == 1)
                {
                    fileIdMTR = fileInfo.ID;
                    canGetHeaderMTR = true;
                }
                if (index > 0)
                {
                    fileOffsetTimeMS = fileInfo.OffsetTimeMS;
                }
            }
            if (MainModel.IsFileReplayByMTRToLogMode && index == 0)
            {
                fileOffsetTimeMS = fileInfo.OffsetTimeMS;
            }
        }

        protected override void recieveInfo_Sample(ClientProxy clientProxy)
        {
            DTDataHeaderManager headerManager = DTDataHeaderManager.GetInstance();
            List<ColumnDefItem> fileHeaderColumnDef = new List<ColumnDefItem>();
            List<ColumnDefItem> curSampleColumnDef = new List<ColumnDefItem>();
            int index = 0;
            Package package = clientProxy.Package;
            int progress = 0;

            Dictionary<string, string> uniqueColumnDic = getUniqueColumnDefDic(replayContentOption);
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                #region Read Stat Data
                if (isFileHeaderContentType(package.Content.Type))
                {
                    dealHeaderInfo(clientProxy, headerManager, fileHeaderColumnDef, package);
                }
                else if (judgeSampleColumnDefType(package))
                {
                    curSampleColumnDef.Clear();
                    string idpairs = package.Content.GetParamString();
                    parseToCurColumnDef(idpairs, curSampleColumnDef);
                }
                else if (judgeSampleResponseType(package))
                {
                    fillParams(headerManager, curSampleColumnDef, package, uniqueColumnDic);
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                #endregion

                setProgressPercent(ref index, ref progress);
            }
        }

        private void dealHeaderInfo(ClientProxy clientProxy, DTDataHeaderManager headerManager,
    List<ColumnDefItem> fileHeaderColumnDef, Package package)
        {
            DTDataHeader fileInfo = recieveFileHeader(clientProxy.DbID, package.Content, fileHeaderColumnDef);
            if (fileInfo != null)
            {
                DTDataHeaderManager.GetInstance().AddDTDataHeader(fileInfo);
                if (MainModel.IsFileReplayByMTRMode)
                {
                    if (canGetHeaderMTR)
                    {
                        headerMTR = fileInfo;
                        canGetHeaderMTR = false;
                    }
                    if (fileIndex > 1)
                    {
                        headerManager.AddDTDataHeader(headerMTR);
                    }
                }
            }
        }

        private bool judgeSampleColumnDefType(Package package)
        {
            return package.Content.Type == ResponseType.RESTYPE_COLUMN_SAMPLE_GSM_V ||
                   package.Content.Type == ResponseType.RESTYPE_COLUMN_SAMPLE_GSM_D ||
                   package.Content.Type == ResponseType.RESTYPE_COLUMN_SAMPLE_CDMA_V ||
                   package.Content.Type == ResponseType.RESTYPE_COLUMN_SAMPLE_CDMA_D ||
                   package.Content.Type == ResponseType.RESTYPE_COLUMN_SAMPLE_TDSCDMA_V ||
                   package.Content.Type == ResponseType.RESTYPE_COLUMN_SAMPLE_TDSCDMA_D ||
                   package.Content.Type == ResponseType.RESTYPE_COLUMN_SAMPLE_WCDMA_V ||
                   package.Content.Type == ResponseType.RESTYPE_COLUMN_SAMPLE_WCDMA_D ||
                   package.Content.Type == ResponseType.RESTYPE_COLUMN_SAMPLE_CDMA2000_D ||
                   package.Content.Type == ResponseType.RESTYPE_COLUMN_SAMPLE_TDSCDMA_SCAN ||
                   package.Content.Type == ResponseType.RESTYPE_COLUMN_SAMPLE_CDMA2000_V ||
                   package.Content.Type == ResponseType.RESTYPE_COLUMN_SAMPLE_GSM_SCAN ||
                   package.Content.Type == ResponseType.RESTYPE_COLUMN_SAMPLE_WCDMA_SCAN ||
                   package.Content.Type == ResponseType.RESTYPE_COLUMN_SAMPLE_GSM_MTR ||
                   package.Content.Type == ResponseType.RESTYPE_COLUMN_SAMPLE_WLAN ||
                   package.Content.Type == ResponseType.RESTYPE_COLUMN_SAMPLE_LTE ||
                   package.Content.Type == ResponseType.RESTYPE_COLUMN_SAMPLE_LTE_SCAN ||
                   package.Content.Type == ResponseType.RESTYPE_COLUMN_SAMPLE_CW_SCAN ||
                   package.Content.Type == ResponseType.RESTYPE_COLUMN_SAMPLE_FREQSPECTURM_SCAN ||
                   package.Content.Type == ResponseType.RESTYPE_COLUMN_SAMPLE_NBIOT_SCAN ||
                   package.Content.Type == ResponseType.RESTYPE_COLUMN_SAMPLE_NR ||
                   package.Content.Type == ResponseType.RESTYPE_COLUMN_SAMPLE_NR_SCAN ||
                   package.Content.Type == ResponseType.COLUMN_DEFINE;
        }

        private bool judgeSampleResponseType(Package package)
        {
            return package.Content.Type == ResponseType.RESTYPE_DIY_SAMPLE_GSM_V ||
                   package.Content.Type == ResponseType.RESTYPE_DIY_SAMPLE_GSM_D ||
                   package.Content.Type == ResponseType.RESTYPE_DIY_SAMPLE_CDMA_V ||
                   package.Content.Type == ResponseType.RESTYPE_DIY_SAMPLE_CDMA_D ||
                   package.Content.Type == ResponseType.RESTYPE_DIY_SAMPLE_TDSCDMA_V ||
                   package.Content.Type == ResponseType.RESTYPE_DIY_SAMPLE_TDSCDMA_D ||
                   package.Content.Type == ResponseType.RESTYPE_DIY_SAMPLE_WCDMA_V ||
                   package.Content.Type == ResponseType.RESTYPE_DIY_SAMPLE_WCDMA_D ||
                   package.Content.Type == ResponseType.RESTYPE_DIY_SAMPLE_CDMA2000_D ||
                   package.Content.Type == ResponseType.RESTYPE_DIY_SAMPLE_TDSCDMA_SCAN ||
                   package.Content.Type == ResponseType.RESTYPE_DIY_SAMPLE_CDMA2000_V ||
                   package.Content.Type == ResponseType.RESTYPE_DIY_SAMPLE_GSM_SCAN ||
                   package.Content.Type == ResponseType.RESTYPE_DIY_SAMPLE_WCDMA_SCAN ||
                   package.Content.Type == ResponseType.RESTYPE_DIY_SAMPLE_GSM_MTR ||
                   package.Content.Type == ResponseType.RESTYPE_DIY_SAMPLE_WLAN ||
                   package.Content.Type == ResponseType.RESTYPE_DIY_SAMPLE_LTE ||
                   package.Content.Type == ResponseType.RESTYPE_DIY_SAMPLE_LTE_SCAN ||
                   package.Content.Type == ResponseType.RESTYPE_DIY_SAMPLE_CW_SCAN ||
                   package.Content.Type == ResponseType.RESTYPE_DIY_SAMPLE_FREQSPECTURM_SCAN ||
                   package.Content.Type == ResponseType.FILE_SAMPLE_LTE_UEP ||
                   package.Content.Type == ResponseType.RESTYPE_DIY_SAMPLE_LTE_FDD ||
                   package.Content.Type == ResponseType.RESTYPE_DIY_SAMPLE_NBIOT_SCAN ||
                   package.Content.Type == ResponseType.RESTYPE_DIY_SAMPLE_NR ||
                   package.Content.Type == ResponseType.RESTYPE_DIY_SAMPLE_NR_SCAN ||
                   package.Content.Type == ResponseType.RESTYPE_DIY_SAMPLE_SIGNAL;
        }

        #region fillParams
        private void fillParams(DTDataHeaderManager headerManager, List<ColumnDefItem> curSampleColumnDef,
           Package package, Dictionary<string, string> uniqueColumnDic)
        {
            TestPoint tpPoint = getTestPoint(package);

            foreach (ColumnDefItem cdf in curSampleColumnDef)//回放指标名
            {
                switch (cdf.showName)
                {
                    case "ifileid":
                        fillIfileid(package, tpPoint);
                        break;
                    case "ilongitude":
                        fillIlongitude(package, tpPoint, cdf);
                        break;
                    case "ilatitude":
                        fillIlatitude(package, tpPoint, cdf);
                        break;
                    case "itime":
                        fillItime(package, tpPoint, cdf);
                        break;
                    case "isampleid":
                        fillIsampleid(package, tpPoint);
                        break;
                    case "bms":
                        fillBms(package, tpPoint, cdf);
                        break;
                    case "wtimems":
                        fillWtimems(package, tpPoint, cdf);
                        break;
                    default:
                        fillImageParams(package, uniqueColumnDic, tpPoint, cdf);
                        break;
                }
            }

            doAfterFillParams(headerManager, tpPoint);
        }

        private TestPoint getTestPoint(Package package)
        {
            TestPoint tpPoint = null;
            switch (package.Content.Type)
            {
                case ResponseType.RESTYPE_DIY_SAMPLE_TDSCDMA_V:
                case ResponseType.RESTYPE_DIY_SAMPLE_TDSCDMA_D:
                    tpPoint = new TDTestPointDetail();
                    break;
                case ResponseType.RESTYPE_DIY_SAMPLE_GSM_V:
                case ResponseType.RESTYPE_DIY_SAMPLE_GSM_D:
                    tpPoint = new TestPointDetail();
                    break;
                case ResponseType.RESTYPE_DIY_SAMPLE_WCDMA_V:
                case ResponseType.RESTYPE_DIY_SAMPLE_WCDMA_D:
                    tpPoint = new WCDMATestPointDetail();
                    break;
                case ResponseType.RESTYPE_DIY_SAMPLE_CDMA_V:
                case ResponseType.RESTYPE_DIY_SAMPLE_CDMA_D:
                    tpPoint = new CDMATestPointDetail();
                    break;
                case ResponseType.RESTYPE_DIY_SAMPLE_CDMA2000_V:
                case ResponseType.RESTYPE_DIY_SAMPLE_CDMA2000_D:
                    tpPoint = new CDMATestPointDetail();
                    break;
                case ResponseType.RESTYPE_DIY_SAMPLE_GSM_SCAN:
                    tpPoint = new ScanTestPoint_G();
                    break;
                case ResponseType.RESTYPE_DIY_SAMPLE_GSM_MTR:
                    tpPoint = new GSMUplinkData();
                    break;
                case ResponseType.RESTYPE_DIY_SAMPLE_TDSCDMA_SCAN:
                    tpPoint = new ScanTestPoint_TD();
                    break;
                case ResponseType.RESTYPE_DIY_SAMPLE_WCDMA_SCAN:
                    tpPoint = new ScanTestPoint_W();
                    break;
                case ResponseType.RESTYPE_DIY_SAMPLE_WLAN:
                    tpPoint = new WLANTestPoint();
                    break;
                case ResponseType.RESTYPE_DIY_SAMPLE_LTE:
                    tpPoint = new LTETestPointDetail();
                    break;
                case ResponseType.RESTYPE_DIY_SAMPLE_LTE_SCAN:
                    tpPoint = new ScanTestPoint_LTE();
                    break;
                case ResponseType.RESTYPE_DIY_SAMPLE_CW_SCAN:
                    tpPoint = new ScanTestPoint_CW();
                    break;
                case ResponseType.RESTYPE_DIY_SAMPLE_FREQSPECTURM_SCAN:
                    tpPoint = new ScanTestPoint_FreqSpecturm();
                    break;
                case ResponseType.FILE_SAMPLE_LTE_UEP:
                    tpPoint = new LTEUepTestPoint();
                    break;
                case ResponseType.RESTYPE_DIY_SAMPLE_LTE_FDD:
                    tpPoint = new LTEFddTestPoint();
                    break;
                case ResponseType.RESTYPE_DIY_SAMPLE_SIGNAL:
                    tpPoint = new SignalTestPoint();
                    break;
                case ResponseType.RESTYPE_DIY_SAMPLE_NBIOT_SCAN:
                    tpPoint = new ScanTestPoint_NBIOT();
                    break;
                case ResponseType.RESTYPE_DIY_SAMPLE_NR:
                    tpPoint = new TestPoint_NR();
                    break;
                case ResponseType.RESTYPE_DIY_SAMPLE_NR_SCAN:
                    tpPoint = new ScanTestPoint_NR();
                    break;
            }

            return tpPoint;
        }

        private void fillIfileid(Package package, TestPoint tpPoint)
        {
            tpPoint.FileID = package.Content.GetParamInt();
            if (MainModel.IsFileReplayByMTRMode && package.Content.Type == ResponseType.RESTYPE_DIY_SAMPLE_GSM_MTR)
            {
                tpPoint.FileID = fileIdMTR;
            }
        }

        private void fillIlongitude(Package package, TestPoint tpPoint, ColumnDefItem cdf)
        {
            Double curtmp = package.Content.GetParamDouble();
            if (curtmp.ToString() != cdf.nullValue)
            {
                tpPoint.Longitude = curtmp;
            }
        }

        private void fillIlatitude(Package package, TestPoint tpPoint, ColumnDefItem cdf)
        {
            Double curtmp = package.Content.GetParamDouble();
            if (curtmp.ToString() != cdf.nullValue)
            {
                tpPoint.Latitude = curtmp;
            }
        }

        private void fillItime(Package package, TestPoint tpPoint, ColumnDefItem cdf)
        {
            int curtmp = package.Content.GetParamInt();
            if (curtmp.ToString() != cdf.nullValue)
            {
                tpPoint.Time = curtmp;
            }
        }

        private void fillIsampleid(Package package, TestPoint tpPoint)
        {
            tpPoint.SN = package.Content.GetParamInt();
            if (MainModel.IsFileReplayByMTRMode && package.Content.Type == ResponseType.RESTYPE_DIY_SAMPLE_GSM_MTR)
            {
                if (MainModel.DTDataManager.FileDataManagers.Count > 1)
                {
                    tpPoint.SN = MainModel.DTDataManager.FileDataManagers[1].TestPoints.Count + 1;
                }
                else
                {
                    tpPoint.SN = 1;
                }
            }
        }

        private void fillBms(Package package, TestPoint tpPoint, ColumnDefItem cdf)
        {
            if (cdf.vType == E_VType.E_Int)
            {
                tpPoint.MS = (byte)package.Content.GetParamInt();
            }
            else if (cdf.vType == E_VType.E_Short)
            {
                tpPoint.MS = (byte)package.Content.GetParamShort();
            }
            else if (cdf.vType == E_VType.E_Byte)
            {
                tpPoint.MS = package.Content.GetParamByte();
            }
            else
            {
                throw (new Exception("wtimems 类型配置异常！"));
            }
        }

        private void fillWtimems(Package package, TestPoint tpPoint, ColumnDefItem cdf)
        {
            if (cdf.vType == E_VType.E_Int)
            {
                tpPoint.Millisecond = (short)package.Content.GetParamInt();
            }
            else if (cdf.vType == E_VType.E_Short)
            {
                tpPoint.Millisecond = package.Content.GetParamShort();
            }
            else
            {
                throw (new Exception("wtimems 类型配置异常！"));
            }
            if (MainModel.IsFileReplayByMTRMode && package.Content.Type == ResponseType.RESTYPE_DIY_SAMPLE_GSM_MTR)
            {
                tpPoint.Millisecond += (short)fileOffsetTimeMS;
            }
            if (MainModel.IsFileReplayByMTRToLogMode && package.Content.Type == ResponseType.RESTYPE_DIY_SAMPLE_GSM_V)
            {
                tpPoint.Millisecond -= (short)fileOffsetTimeMS;
            }
        }

        #region fillImageParams
        private void fillImageParams(Package package, Dictionary<string, string> uniqueColumnDic,
            TestPoint tpPoint, ColumnDefItem cdf)
        {
            if (cdf.fix)
            {
                fillFixParam(package, tpPoint, cdf);
            }
            else// non fix
            {
                fillNonFixParam(package, uniqueColumnDic, tpPoint, cdf);
            }
        }

        private void fillFixParam(Package package, TestPoint tpPoint, ColumnDefItem cdf)
        {
            switch (cdf.vType)
            {
                case E_VType.E_Int:
                    {
                        int datav = package.Content.GetParamInt();
                        setValidTpParam(tpPoint, cdf, datav.ToString(), datav);
                    }
                    break;
                case E_VType.E_Short:
                    {
                        short datav = package.Content.GetParamShort();
                        setValidTpParam(tpPoint, cdf, datav.ToString(), datav);
                    }
                    break;
                case E_VType.E_UShort:
                    {
                        ushort datav = package.Content.GetParamUShort();
                        setValidTpParam(tpPoint, cdf, datav.ToString(), datav);
                    }
                    break;
                case E_VType.E_Byte:
                    {
                        byte datav = package.Content.GetParamByte();
                        setValidTpParam(tpPoint, cdf, datav.ToString(), datav);
                    }
                    break;
                case E_VType.E_Float:
                case E_VType.E_IntFloat:
                    {
                        int datav = package.Content.GetParamInt();
                        setValidTpParam(tpPoint, cdf, datav.ToString(), datav / 1000.0f);
                    }
                    break;
                case E_VType.E_String:
                    {
                        string datav = package.Content.GetParamString();
                        if (datav != null && datav != "")
                        {
                            setValidTpParam(tpPoint, cdf, datav, datav);
                        }
                    }
                    break;
                case E_VType.E_UInt64:
                    {
                        UInt64 datav = package.Content.GetParamUInt64();
                        setValidTpParam(tpPoint, cdf, datav.ToString(), datav);
                    }
                    break;
                case E_VType.E_Int64:
                    {
                        Int64 datav = package.Content.GetParamInt64();
                        setValidTpParam(tpPoint, cdf, datav.ToString(), datav);
                    }
                    break;
                default:
                    package.Content.GetParamByte();
                    break;
            }
        }

        private void setValidTpParam<T>(TestPoint tpPoint, ColumnDefItem cdf, string datav, T defaultData)
        {
            if (datav != cdf.nullValue)
            {
                if (cdf.ShowArrayIndex == -1)
                {
                    tpPoint[cdf.showName] = defaultData;
                }
                else
                {
                    tpPoint[cdf.ShowArrayName, cdf.ShowArrayIndex] = defaultData;
                }
            }
        }

        private void fillNonFixParam(Package package, Dictionary<string, string> uniqueColumnDic,
            TestPoint tpPoint, ColumnDefItem cdf)
        {
            byte[] imgBytes = package.Content.GetParamBytes();
            if (cdf.vType != E_VType.E_ImgString)
            {
                fillImgStringParam(uniqueColumnDic, tpPoint, cdf, imgBytes);
            }
            else//string类型的image
            {
                fillStringParam(tpPoint, cdf, imgBytes);
            }
        }

        private void fillImgStringParam(Dictionary<string, string> uniqueColumnDic, TestPoint tpPoint,
            ColumnDefItem cdf, byte[] imgBytes)
        {
            List<ColumnDefItem> nonFixInColumns = InterfaceManager.GetInstance().GetOtherDefInSameColumn(cdf);
            foreach (ColumnDefItem nfColDef in nonFixInColumns)
            {
                if (!isAddAllOtherParameter && !uniqueColumnDic.ContainsKey(nfColDef.showName))
                {
                    //不查询所有其他参数时,如果参数名不是所选指标直接跳过,避免指标数据量过大导致内存增长过快
                    continue;
                }

                byte grpCount = imgBytes[0];
                byte grpAt = (byte)(nfColDef.paraID / 1000);
                if (grpAt > grpCount)
                {
                    continue;
                }
                int offsetx = initOffsetx(imgBytes, grpCount, grpAt);
                setImgStringParam(tpPoint, imgBytes, nfColDef, offsetx);
            }
        }

        private void setImgStringParam(TestPoint tpPoint, byte[] imgBytes, ColumnDefItem nfColDef, int offsetx)
        {
            byte ctOfNeib = imgBytes[offsetx++];
            byte szOfEachNeib = imgBytes[offsetx++];
            if (ctOfNeib > 0 && ctOfNeib <= nfColDef.maxArrCount)
            {
                for (int i = 0; i < ctOfNeib; i++)
                {
                    int startIdx = offsetx + i * szOfEachNeib + nfColDef.posFrom;
                    if (startIdx == imgBytes.Length)
                    {//一种可能情况：新加的参数，已入库的文件没有该参数。以新配置来解析image时，数组越界
                        break;
                    }
                    object arrObj = getArrObj(imgBytes, nfColDef, startIdx);
                    if (arrObj != null && arrObj.ToString() != nfColDef.nullValue)
                    {//有效值
                        tpPoint[nfColDef.showName, i] = arrObj;
                    }
                }
            }
        }

        private int initOffsetx(byte[] imgBytes, byte grpCount, byte grpAt)
        {
            int offsetx = 1;
            for (byte c = 0; c < grpCount && c < grpAt - 1; c++)
            {
                byte countOfArr = imgBytes[offsetx++];
                byte eachArrSize = imgBytes[offsetx++];
                offsetx += eachArrSize * countOfArr;
            }

            return offsetx;
        }

        private object getArrObj(byte[] imgBytes, ColumnDefItem nfColDef, int startIdx)
        {
            object arrObj = null;
            switch (nfColDef.vType)
            {
                case E_VType.E_Byte:
                    if (imgBytes.Length - startIdx >= 1)
                    {
                        arrObj = imgBytes[startIdx];
                    }
                    break;
                case E_VType.E_Int:
                    if (imgBytes.Length - startIdx >= 4)
                    {
                        int valueInt = BitConverter.ToInt32(imgBytes, startIdx);
                        arrObj = IPAddress.NetworkToHostOrder(valueInt);
                    }
                    break;
                case E_VType.E_Short:
                    if (imgBytes.Length - startIdx >= 2)
                    {
                        short valueShort = BitConverter.ToInt16(imgBytes, startIdx);
                        arrObj = IPAddress.NetworkToHostOrder(valueShort);
                    }
                    break;
                case E_VType.E_UShort:
                    if (imgBytes.Length - startIdx >= 2)
                    {
                        short valShort = BitConverter.ToInt16(imgBytes, startIdx);
                        arrObj = (ushort)IPAddress.NetworkToHostOrder(valShort);
                    }
                    break;
                case E_VType.E_Float:
                case E_VType.E_IntFloat:
                    if (imgBytes.Length - startIdx >= 4)
                    {
                        int valueFloatInt = BitConverter.ToInt32(imgBytes, startIdx);
                        if (valueFloatInt.ToString() != nfColDef.nullValue)
                        {
                            arrObj = IPAddress.NetworkToHostOrder(valueFloatInt) / 1000.0f;
                        }
                    }
                    break;
                case E_VType.E_String:
                    break;
                default:
                    break;
            }

            return arrObj;
        }

        private void fillStringParam(TestPoint tpPoint, ColumnDefItem cdf, byte[] imgBytes)
        {
            List<ColumnDefItem> nonFixInColumns = InterfaceManager.GetInstance().GetOtherDefInSameColumn(cdf);
            foreach (ColumnDefItem nfColDef in nonFixInColumns)
            {
                try
                {
                    byte grpCount = imgBytes[0];
                    if (grpCount == 0)
                    {
                        continue;
                    }
                    byte grpAt = (byte)(nfColDef.paraID / 1000);
                    if (grpAt > grpCount)
                    {
                        continue;
                    }
                    int offsetx = initOffsetx(imgBytes, grpCount, grpAt);
                    setStringParam(tpPoint, imgBytes, nfColDef, offsetx);
                }
                catch
                {
                    MessageBox.Show("解析数组出错！请核对配置或找入库人员处理！" + nfColDef.GetTriIdStr());
                }
            }
        }

        private void setStringParam(TestPoint tpPoint, byte[] imgBytes, ColumnDefItem nfColDef, int offsetx)
        {
            byte ctOfNeib = imgBytes[offsetx++];
            offsetx++;//byte countOfEachNeib  imgBytes[offsetx+]+
            byte szOfEachNeib;
            if (ctOfNeib > 0 && ctOfNeib <= nfColDef.maxArrCount)
            {
                for (int i = 0; i < ctOfNeib; i++)
                {
                    object arrObj = null;
                    szOfEachNeib = imgBytes[offsetx++];
                    if (szOfEachNeib == 0)
                    {
                        tpPoint[nfColDef.showName, i] = arrObj;
                        continue;
                    }
                    string sTmp = BitConverter.ToString(imgBytes, offsetx, szOfEachNeib);
                    offsetx += szOfEachNeib;
                    string[] sArr = sTmp.Split('-');
                    StringBuilder sb = new StringBuilder();
                    foreach (string s in sArr)
                    {
                        int numb = Convert.ToInt32(s, 16);
                        sb.Append((char)numb);
                    }
                    arrObj = sb.ToString();
                    tpPoint[nfColDef.showName, i] = arrObj;
                }
            }
        }
        #endregion
        #endregion

        private void doAfterFillParams(DTDataHeaderManager headerManager, TestPoint tpPoint)
        {
            DTDataHeader header = headerManager.GetHeaderByFileID(tpPoint.FileID);
            if (header != null && isValidTestPoint(tpPoint))
            {
                doForAttenuation(tpPoint);
                if (doForLTEScanWorkMode(tpPoint))
                {
                    tpPoint.ApplyHeader(header);
                    if (IsAddSampleToDTDataManager)
                    {
                        MainModel.DTDataManager.Add(tpPoint);
                    }
                    else
                    {
                        doWithDTData(tpPoint);
                    }
                    FireDoWithDTData(tpPoint);
                }
            }
        }

        /// <summary>
        /// 获取唯一的查询指标列名
        /// </summary>
        /// <param name="replayOption"></param>
        /// <returns></returns>
        private Dictionary<string, string> getUniqueColumnDefDic(DIYReplayContentOption replayOption)
        {
            Dictionary<string, string> columnsDic = new Dictionary<string, string>();
            foreach (ColumnDefItem columnDef in replayOption.SampleColumns)
            {
                if (columnDef == null)
                {
                    continue;
                }
                //相同名称指标只保留一个,避免循环次数过多
                if (!columnsDic.ContainsKey(columnDef.showName))
                {
                    columnsDic[columnDef.showName] = columnDef.showName;
                }
            }
            return columnsDic;
        }

        /// <summary>
        /// 子类必须重写
        /// </summary>
        protected virtual void initManager(StationAcceptConditionBase cond)
        {
            query();
        }

        /// <summary>
        /// 子类必须重写
        /// </summary>
        protected virtual List<string> queryColumns
        {
            get
            {
                return new List<string>()
                {
                     "isampleid",
                     "itime",
                     "ilongitude",
                     "ilatitude"
                };
            }
        }

        protected virtual bool judgeValidFile(FileInfo fileInfo)
        {
            if (MainModel.DTDataManager.FileDataManagers.Count == 0)
            {
                return false;
            }
            return true;
        }
    }

    public class NR700MOptimizationcCustersEvent : DIYEventQuery
    {
        public string ConfigPath { get; } = $@"{AppDomain.CurrentDomain.BaseDirectory}\config\NROptimizationcCusters\NROptimizationcCusters.xml";
        Dictionary<string, string> dic = new Dictionary<string, string>();

        public NR700MOptimizationcCustersEvent(MainModel mainModel)
              : base(mainModel)
        {

        }

        public void FindEventQuery(QueryCondition queryCondition)
        {
            this.condition = queryCondition;
            getEventInfoDic();
            foreach (KeyValuePair<string, string> item in dic)
            {
                Condition.EventIDs = new List<int>();
                Condition.EventIDs.Add(item.Value == "" ? 0 : Convert.ToInt32(item.Value));
                query();

                CutPic(item.Key, Condition.FileInfos[0].Name.Split(new char[] { '_' })[0], item.Value);
            }
        }

        protected override void query()
        {
            if (!MainModel.IsBackground && !MainModel.QueryFromBackground)
            {
                WaitBox.CanCancel = true;
                WaitBox.Text = "正在查询...";
            }
            ClientProxy clientProxy = null;

            MainModel.ClearDTData();
            MainModel.IsDrawEventResult = false;
            MainModel.IsFileReplayByCompareMode = Condition.isCompareMode;

            foreach (int DistrictID in condition.DistrictIDs)
            {
                clientProxy = new ClientProxy();
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }

                if (!MainModel.IsBackground && !MainModel.QueryFromBackground)
                {
                    WaitBox.Show("开始统计数据...", queryInThread, clientProxy);
                }
                else
                {
                    queryInThread(clientProxy);
                }
                clientProxy.Close();
            }

            MainModel.FireDTDataChanged(this);
            MainModel.RefreshLegend();
        }

        int index = 0;
        protected override void queryInThread(object o)
        {
            try
            {
                ClientProxy clientProxy = (ClientProxy)o;
                Package package = clientProxy.Package;
                int fileCount = Condition.FileInfos.Count;
                index = 0;
                foreach (FileInfo fileInfo in Condition.FileInfos)
                {
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                    WaitBox.Text = "开始统计文件[" + fileInfo.Name + "]的数据...";
                    WaitBox.ProgressPercent = index / fileCount + 10;
                    //根据文件id过滤
                    condition.NameFilterType = FileFilterType.ByMark_ID;
                    condition.FileName = fileInfo.ID.ToString();
                    queryPeriodInfo(clientProxy, package, condition.Periods[index], false);
                    index++;
                }
            }
            catch (Exception ex)
            {
                log.Error(ex.StackTrace);
            }
            finally
            {
                if (!MainModel.IsBackground && !MainModel.QueryFromBackground)
                {
                    WaitBox.Close();
                }
            }
        }

        protected string dirPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, @"userdata\NROptimizationcCusters\Pictures");
        public void CutPic(string themname, string clustername, string eventID)
        {
            string path = Path.Combine(dirPath, clustername);
            if (!System.IO.Directory.Exists(path))
            {
                System.IO.Directory.CreateDirectory(path);
            }

            string picPath = System.IO.Path.Combine(path, themname + ".jpg");

            MapForm mapForm = mainModel.MainForm.GetMapForm();
            List<MapEventInfo> mapEventInfos = mapForm.GetDTLayer().EventInfos;
            foreach (var eventInfo in mapEventInfos)
            {
                if (eventInfo.EventID.ToString() == eventID)
                {
                    mapForm.FireAndOutputCurMapToEventPic(themname, false, picPath, eventInfo);
                    break;
                }
            }
        }

        private void queryByPeriods(ClientProxy clientProxy, Package package, int periodCount, int index)
        {
            foreach (TimePeriod period in Condition.Periods)
            {
                if (!MainModel.IsBackground && !MainModel.QueryFromBackground)
                {
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                    WaitBox.Text = "开始统计时段[" + period.GetShortString() + "]内的数据...";
                    WaitBox.ProgressPercent = index / periodCount + 10;
                }
                else if (MainModel.BackgroundStopRequest)
                {
                    break;
                }
                queryPeriodInfo(clientProxy, package, period, false);
            }
        }

        protected override void queryPeriodInfo(ClientProxy clientProxy, Package package, TimePeriod period, bool byround)
        {
            MainModel.IsFusionInclude = false;
            prepareStatPackage_Event_FileFilter(package, period, byround);
            prepareStatPackage_Event_EventFilter(package, period);
            fillContentNeeded_Event(package);
            clientProxy.Send();
            recieveInfo_Event(clientProxy);
        }

        protected override void prepareStatPackage_Event_FileFilter(Package package, TimePeriod period, bool byRound)
        {
            package.Command = Command.DIYSearch;
            package.SubCommand = SubCommand.Request;
            package.Content.Type = RequestType.REQTYPE_DIY_AREA_EVENT;
            package.Content.PrepareAddParam();
            if (byRound)
            {
                AddDIYRound(package, condition.ByRoundYear, condition.ByRoundRound);
            }
            else
            {
                AddDIYPeriod(package, period);
            }
            AddDIYRegion_Intersect(package);
            AddDIYProject(package, condition.Projects);
            AddDIYService(package, condition.ServiceTypes);
            if (!condition.IsAllAgent)
            {
                AddDIYAgent(package, condition.AgentIds);
            }
            AddDIYCarrierType(package, condition.CarrierTypes);

            if (condition.DeviceTypes.Count != 0)
            {
                AddDIYDeviceType(package, condition.DeviceTypes);
            }
            AddDIYStatStatus(package);
            AddDIYAreaTypeAndID(package, condition.Areas);
            AddDIYMomt(package, condition.Momt);

            AddDIYEndOpFlag(package);
        }

        public void getEventInfoDic()
        {
            if (File.Exists(ConfigPath))
            {
                var xcfg = new XmlConfigFile(ConfigPath);
                if (xcfg.Load())
                {
                    XmlElement config = xcfg.GetConfig("Configs");
                    dic.Add("NRWeakCover", getValidData(xcfg, config, "NRWeakCover", ""));
                    dic.Add("NRSinr", getValidData(xcfg, config, "NRSinr", ""));
                    dic.Add("NRLowDown", getValidData(xcfg, config, "NRLowDown", ""));
                    dic.Add("NRLowUpload", getValidData(xcfg, config, "NRLowUpload", ""));
                    dic.Add("NRSwtich", getValidData(xcfg, config, "NRSwtich", ""));
                    dic.Add("NRDrop", getValidData(xcfg, config, "NRDrop", ""));
                    dic.Add("NRConnect", getValidData(xcfg, config, "NRConnect", ""));
                    dic.Add("NRTaskOfNet", getValidData(xcfg, config, "NRTaskOfNet", ""));
                    dic.Add("NRLteSwitchFail", getValidData(xcfg, config, "NRLteSwitchFail", ""));
                    dic.Add("NRLteDrop", getValidData(xcfg, config, "NRLteDrop", ""));
                    dic.Add("NRLteConnect", getValidData(xcfg, config, "NRLteConnect", ""));
                }
            }
        }


        #region 获取有效数据方法
        protected T getValidData<T>(XmlConfigFile xcfg, XmlElement config, string name, T defaultData)
        {
            object obj = xcfg.GetItemValue(config, name);
            if (obj != null)
            {
                return (T)obj;
            }
            return defaultData;
        }

        protected string getValidPath(XmlConfigFile xcfg, XmlElement config, string name)
        {
            object obj = xcfg.GetItemValue(config, name);
            if (obj != null)
            {
                string path = obj.ToString();
                if (File.Exists(path))
                {
                    return path;
                }
            }
            return "";
        }

        protected string getValidDirectory(XmlConfigFile xcfg, XmlElement config, string name)
        {
            object obj = xcfg.GetItemValue(config, name);
            if (obj != null)
            {
                string path = obj.ToString();
                if (Directory.Exists(path))
                {
                    return path;
                }
            }
            return "";
        }
        #endregion
    }
}
