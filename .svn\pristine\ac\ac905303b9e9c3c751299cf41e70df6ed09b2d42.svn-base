﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Drawing;
using MapWinGIS;
using MasterCom.RAMS.Model;

namespace MasterCom.MTGis
{
    public class MapOperation
    {
        private readonly AxMapWinGIS.AxMap axMap = null;
        public AxMapWinGIS.AxMap MapControl
        {
            get { return axMap; }
        }
        public MapOperation(AxMapWinGIS.AxMap axMap)
        {
            this.axMap = axMap;
            axMap_ExtentsChanged(null,EventArgs.Empty);
            axMap.ExtentsChanged += axMap_ExtentsChanged;
        }

        double pixelPerProjX = 0;
        double pixelPerProjY = 0;
        DbRect bounds = new DbRect();
        void axMap_ExtentsChanged(object sender, EventArgs e)
        {
            Extents ext = axMap.Extents as Extents;
            pixelPerProjX = axMap.Width / (ext.xMax - ext.xMin);
            pixelPerProjY = axMap.Height / (ext.yMax - ext.yMin);
            bounds = new DbRect(ext.xMin, ext.yMin, ext.xMax, ext.yMax);
        }

        public MainModel MainModel { get; set; }
        public double Scale
        {
            get
            {
                return this.axMap.CurrentScale;
            }
            set
            {
                this.axMap.CurrentScale = value;
            }
        }
        public void ToDisplay(DbPoint pntSrc, out PointF pntDest)
        {
            double pixX = 0;
            double pixY = 0;
            pixX = (pntSrc.x - bounds.x1) * pixelPerProjX;
            pixY = (bounds.y2 - pntSrc.y) * pixelPerProjY;
            pntDest = new PointF((float)pixX, (float)pixY);
        }
        public void ToDisplay(DbPoint[] pntSrc, out PointF[] pntDest)
        {
            int count = pntSrc.Length;
            pntDest = new PointF[count];
            for (int i = 0; i < count; i++)
            {
                double pixX = 0;
                double pixY = 0;
                pixX = (pntSrc[i].x - bounds.x1) * pixelPerProjX;
                pixY = (bounds.y2 - pntSrc[i].y) * pixelPerProjY;
                pntDest[i] = new PointF((float)pixX, (float)pixY);
            }

            
        }
        public void FromDisplay(PointF pntSrc, out DbPoint pntDest)
        {
            double projX = 0;
            double projY = 0;
            projX = bounds.x1 + pntSrc.X / pixelPerProjX;
            projY = bounds.y2 - pntSrc.Y / pixelPerProjY;
            pntDest = new DbPoint(projX, projY);
        }

        public void FromDisplay(PointF[] pntSrc, out DbPoint[] pntDest)
        {
            int count = pntSrc.Length;
            pntDest = new DbPoint[count];
            for (int i = 0; i < count; i++)
            {
                double projX = 0;
                double projY = 0;
                projX = bounds.x1 + pntSrc[i].X / pixelPerProjX;
                projY = bounds.y2 - pntSrc[i].Y / pixelPerProjY;
                pntDest[i] = new DbPoint(projX, projY);
            }
        }
        public void FromDisplay(RectangleF srcRect, out DbRect destRect)
        {
            double x1 = 0;
            double x2 = 0;
            double y1 = 0;
            double y2 = 0;
            x1 = bounds.x1 + srcRect.Left / pixelPerProjX;
            y1 = bounds.y2 - srcRect.Top / pixelPerProjY;
            x2 = bounds.x1 + srcRect.Right / pixelPerProjX;
            y2 = bounds.y2 - srcRect.Bottom / pixelPerProjY;
            destRect = new DbRect(x1, y1, x2, y2);
        }

        public void GoToView(double jd, double wd)
        {
            Extents ext = axMap.Extents as Extents;
            double xGap = ext.xMax - ext.xMin;
            double yGap = ext.yMax - ext.yMin;
            Extents extNew = new Extents();
            extNew.SetBounds(jd - xGap / 2, wd - yGap / 2, 0, jd + xGap / 2, wd + yGap / 2, 0);
            axMap.Extents = extNew;
        }

        public void GoToView(double jd, double wd, float zoom)
        {
            GoToView(jd, wd);
            axMap.CurrentScale = zoom;
        }
        public void GoToView(DbRect rect)
        {
            Extents extNew = new Extents();
            extNew.SetBounds(rect.x1, rect.y1, 0, rect.x2, rect.y2, 0);
            axMap.Extents = extNew;
        }
        public void GoToView(double xmin, double ymin, double xmax, double ymax)
        {
            if (xmax - xmin < 0.01 || ymax - ymin < 0.01)
            {
                double centerX = (xmin + xmax) / 2;
                double centerY = (ymin + ymax) / 2;
                GoToView(centerX, centerY, 8000);
            }
            else
            {
                Extents extNew = new Extents();
                extNew.SetBounds(xmin, ymin, 0, xmax, ymax, 0);
                axMap.Extents = extNew;
            }
        }

        internal DbRect GetBounds()
        {
            Extents ext = axMap.Extents as Extents;
            return new DbRect(ext.xMin, ext.yMin, ext.xMax, ext.yMax);
        }

        internal DbPoint GetCenter()
        {
            Extents ext = axMap.Extents as Extents;
            double xmin = ext.xMin;
            double xmax = ext.xMax;
            double ymin = ext.yMin;
            double ymax = ext.yMax;
            double centerX = (xmin + xmax) / 2;
            double centerY = (ymin + ymax) / 2;
            return new DbPoint(centerX, centerY);
        }
        internal static DbRect GetShapeBounds(MapWinGIS.Shape geometry)
        {
            if (geometry != null)
            {
                Extents ext = geometry.Extents;
                return new DbRect(ext.xMin, ext.yMin, ext.xMax, ext.yMax);
            }
            else
            {
                return new DbRect();
            }
        }

        public static bool CheckIntersectRegion(Shape geometry, DbRect rect)//TOCHECK
        {
            if (geometry == null)
            {
                return false;
            }
            Shape rectShape = new Shape();
            rectShape.Create(ShpfileType.SHP_POLYGON);
            int partIndex = rectShape.NumParts;
            rectShape.InsertPart(0, ref partIndex);
            int numPoint = 0;
            MapWinGIS.Point pt1 = new MapWinGIS.PointClass();
            MapWinGIS.Point pt2 = new MapWinGIS.PointClass();
            MapWinGIS.Point pt3 = new MapWinGIS.PointClass();
            MapWinGIS.Point pt4 = new MapWinGIS.PointClass();

            pt1.x = rect.x1; pt1.y = rect.y1;
            pt2.x = rect.x1; pt2.y = rect.y2;
            pt3.x = rect.x2; pt3.y = rect.y2;
            pt4.x = rect.x2; pt4.y = rect.y1;
            rectShape.InsertPoint(pt1, ref numPoint);
            rectShape.InsertPoint(pt2, ref numPoint);
            rectShape.InsertPoint(pt3, ref numPoint);
            rectShape.InsertPoint(pt4, ref numPoint);
            rectShape.InsertPoint(pt1, ref numPoint);
            return geometry.Distance(rectShape) == 0;
        }
        private static MapWinGIS.Utils util = new Utils();
        public static DbRect IntersectRect(DbRect rect1, DbRect rect2)
        {
            Shape shp1 = CreateRectShape(rect1);
            Shape shp2 = CreateRectShape(rect2);
            Shape shp = util.ClipPolygon(PolygonOperation.INTERSECTION_OPERATION, shp1, shp2);
            return MapOperation.GetShapeBounds(shp);
        }
        public static Shape CreateRectShape(DbRect rect)
        {
            Shape rectShape = new Shape();
            rectShape.Create(ShpfileType.SHP_POLYGON);
            int partIndex = rectShape.NumParts;
            rectShape.InsertPart(0, ref partIndex);
            int numPoint = 0;
            MapWinGIS.Point pt1 = new MapWinGIS.PointClass();
            MapWinGIS.Point pt2 = new MapWinGIS.PointClass();
            MapWinGIS.Point pt3 = new MapWinGIS.PointClass();
            MapWinGIS.Point pt4 = new MapWinGIS.PointClass();

            pt1.x = rect.x1; pt1.y = rect.y1;
            pt2.x = rect.x1; pt2.y = rect.y2;
            pt3.x = rect.x2; pt3.y = rect.y2;
            pt4.x = rect.x2; pt4.y = rect.y1;
            rectShape.InsertPoint(pt1, ref numPoint);
            rectShape.InsertPoint(pt2, ref numPoint);
            rectShape.InsertPoint(pt3, ref numPoint);
            rectShape.InsertPoint(pt4, ref numPoint);
            rectShape.InsertPoint(pt1, ref numPoint);
            return rectShape;
        }
        
        internal static bool CheckIntersectPolygon(Shape geometry, DbPoint[] dpoints)//TOCHECK
        {
            if (geometry == null)
            {
                return false;
            }
            Shape rectShape = new Shape();
            rectShape.Create(ShpfileType.SHP_POLYGON);
            int partIndex = rectShape.NumParts;
            rectShape.InsertPart(0, ref partIndex);
            int numPoint = 0;
            for (int i = 0; i < dpoints.Length; i++)
            {
                DbPoint dpt = dpoints[i];
                MapWinGIS.Point pt = new MapWinGIS.PointClass();
                pt.x = dpt.x;
                pt.y = dpt.y;
                rectShape.InsertPoint(pt, ref numPoint);
                if (i == dpoints.Length - 1)//LAST
                {
                    rectShape.InsertPoint(pt, ref numPoint);
                }
            }
            return geometry.Distance(rectShape) == 0;
        }
        internal static Shape CreateShapeByPoints(DbPoint[] dpoints)
        {
            Shape rectShape = new Shape();
            rectShape.Create(ShpfileType.SHP_POLYGON);
            int partIndex = rectShape.NumParts;
            rectShape.InsertPart(0, ref partIndex);
            int numPoint = 0;
            for (int i = 0; i < dpoints.Length; i++)
            {
                DbPoint dpt = dpoints[i];
                MapWinGIS.Point pt = new MapWinGIS.PointClass();
                pt.x = dpt.x;
                pt.y = dpt.y;
                rectShape.InsertPoint(pt, ref numPoint);
                if (i == dpoints.Length - 1)//LAST
                {
                    DbPoint p0 = dpoints[0];
                    if (p0.x != dpt.x || p0.y != dpt.y)
                    {
                        rectShape.InsertPoint(pt, ref numPoint);
                    }
                }
            }
            return rectShape;
        }

        internal static int GetColumnFieldIndex(Shapefile sfile, string column)
        {
            int fdIndex = -1;
            for (int fd = 0; fd < sfile.NumFields; fd++)
            {
                MapWinGIS.Field field = sfile.get_Field(fd);
                if (field.Name == column)
                {
                    fdIndex = fd;
                    break;
                }
            }
            return fdIndex;
        }
        public static Color ParseColorFrom(string rgb)
        {
            string[] colorArr = rgb.Split(',');
            if (colorArr.Length == 3)
            {
                int r;
                int g;
                int b;
                int.TryParse(colorArr[0], out r);
                int.TryParse(colorArr[1], out g);
                int.TryParse(colorArr[2], out b);
                return Color.FromArgb(int.Parse(colorArr[0]), int.Parse(colorArr[1]), int.Parse(colorArr[2]));
            }
            return Color.White;
        }
    }
    
}
