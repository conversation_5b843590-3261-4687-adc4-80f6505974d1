﻿using MasterCom.RAMS.Func;
using MasterCom.RAMS.Func.SystemSetting;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Text;

namespace MasterCom.RAMS.BackgroundFunc
{
    public class StationAcceptAna_QH : DIYAnalyseByCellBackgroundBaseByFile
    {
        int subFuncId;
        protected StationAcceptManager_QH manager;
        public StationAcceptAutoSet_QH FuncSet { get; set; } = new StationAcceptAutoSet_QH();
        Dictionary<string, Dictionary<int, BtsWorkParam_QH>> workParamSumDic = null;//Dictionary<地市, Dictionary<基站编号, BtsWorkParam_QH>> 
        string fileNameKeyStr = "";
        string curDistrictName = "";
        public List<string> ReportFilePaths { get; set; } = new List<string>();

        protected static readonly object lockObj = new object();
        private static StationAcceptAna_QH instance = null;
        public static StationAcceptAna_QH GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new StationAcceptAna_QH();
                    }
                }
            }
            return instance;
        }

        protected StationAcceptAna_QH()
            : base(MainModel.GetInstance())
        {
            if (instance != null)
            {
                return;
            }

            this.isIgnoreExport = true;
            FilterSampleByRegion = false;
            FilterEventByRegion = false;

            ServiceTypes.Clear();
            ServiceTypes.AddRange(ServiceTypeManager.GetServiceTypesByServiceName(ServiceName.LTE));
            ServiceTypes.AddRange(ServiceTypeManager.GetServiceTypesByServiceName(ServiceName.VoLTE));

            this.Columns = new List<string>();
            Columns.Add("lte_TAC");
            Columns.Add("lte_ECI");
            Columns.Add("lte_EARFCN");
            Columns.Add("lte_PCI");
            Columns.Add("lte_RSRQ");
            Columns.Add("lte_RSSI");
            Columns.Add("lte_SCell_LAC");
            Columns.Add("lte_SCell_CI");

            Columns.Add("lte_RSRP");
            Columns.Add("lte_SINR");
            Columns.Add("lte_PDCP_UL_Mb");
            Columns.Add("lte_PDCP_DL_Mb");
        }
        public override string Name
        {
            get { return "青海单站验收"; }
        }
        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 22000, 22117, "查询");
        }
        public override void DealBeforeBackgroundQueryByCity()
        {
            subFuncId = GetSubFuncID();
            QueryPhoneTestDbInfo queryPhoneDb = new QueryPhoneTestDbInfo();
            queryPhoneDb.Query();
            workParamSumDic = GetWorkParamsHelper_QH.GetWorkParamsInfo(FuncSet);
            ReportFilePaths.Clear();
        }
        protected override bool getCondition()
        {
            curDistrictName = DistrictManager.GetInstance().getDistrictName(MainModel.DistrictID).Replace("市", "");
            if (workParamSumDic == null || workParamSumDic.Count <= 0
                || !workParamSumDic.ContainsKey(curDistrictName))
            {
                reportBackgroundInfo("未读取到" + curDistrictName + "的待评估对象数据");
                return false;
            }
            MainModel.MainForm.GetMapForm().updateMap();
            return true;
        }
        protected override void query()
        {
            if (!getCondition())
            {
                return;
            }
            ClientProxy clientProxy = new ClientProxy();

            if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
            {
                ErrorInfo = "连接服务器失败!";
                return;
            }
            clientProxy.Close();

            Dictionary<int, BtsWorkParam_QH> curDistrictWorkParam = workParamSumDic[curDistrictName.Replace("市", "")];
            if (curDistrictWorkParam != null)
            {
                reportBackgroundInfo("读取到" + curDistrictWorkParam.Count + "个站点的工参信息");
                foreach (BtsWorkParam_QH btsInfo in curDistrictWorkParam.Values)
                {
                    if (MainModel.BackgroundStopRequest)
                    {
                        return;
                    }

                    dealBtsInfo(clientProxy, btsInfo);
                }
            }
        }

        private void dealBtsInfo(ClientProxy clientProxy, BtsWorkParam_QH btsInfo)
        {
            bool? isAllCellPassed = null;
            StringBuilder strbFilter = new StringBuilder();
            strbFilter.Append(btsInfo.BtsName);//部分站点的小区名并不包含基站名

            foreach (CellWorkParamBase info in btsInfo.CellWorkParamDic.Values)
            {
                CellWorkParam_QH cellInfo = info as CellWorkParam_QH;
                bool isCurCellPassed = cellInfo.StrDes.Contains("已通过");
                if (isAllCellPassed != false)
                {
                    isAllCellPassed = isCurCellPassed;
                }
                strbFilter.Append(string.Format(" or {0}", cellInfo.CellName));
            }
            if (isAllCellPassed == true)
            {
                reportBackgroundInfo(string.Format("基站 {0} 已通过验收！", btsInfo.BtsName));

            }
            else
            {
                fileNameKeyStr = strbFilter.ToString();

                LTEBTS bts = null;
                bool isNewAdd = addCellInfoToCellManager(btsInfo, ref bts);

                string folderPath = GetBtsPicFolder(btsInfo.BtsName);
                if (System.IO.Directory.Exists(folderPath))//删除本站之前的覆盖截图
                {
                    System.IO.Directory.Delete(folderPath, true);
                }

                reportBackgroundInfo(string.Format("开始读取基站 {0} 的待分析文件...", btsInfo.BtsName));
                doBackgroundStatByFile(clientProxy);

                reportBackgroundInfo(string.Format("开始读取基站 {0} 的预处理信息...", btsInfo.BtsName));
                exportReportByBgData(btsInfo);

                if (System.IO.Directory.Exists(folderPath))//删除本站新生成的覆盖截图
                {
                    System.IO.Directory.Delete(folderPath, true);
                }

                if (isNewAdd)
                {
                    removeCell(bts);
                }
            }
        }

        private void removeCell(LTEBTS bts)
        {
            foreach (LTECell cell in bts.Cells)
            {
                MainModel.CellManager.Remove(cell);
                foreach (LTEAntenna ant in cell.Antennas)
                {
                    MainModel.CellManager.Remove(ant);
                }
            }
            MainModel.CellManager.Remove(bts);
        }

        private bool addCellInfoToCellManager(BtsWorkParam_QH btsParam, ref LTEBTS bts)
        {
            bool isNewAdd = true;
            foreach (CellWorkParamBase info in btsParam.CellWorkParams)
            {
                CellWorkParam_QH cellParamInfo = info as CellWorkParam_QH;
                LTECell lteCell = CellManager.GetInstance().GetLTECellByECI(DateTime.Now, cellParamInfo.Eci);
                if (lteCell != null)
                {
                    bts = lteCell.BelongBTS;
                    removeCell(bts);
                    isNewAdd = false;
                    break;
                }
            }

            bts = new LTEBTS();
            int snapShotId = -1;

            #region 添加工参到CellManager

            bts.Fill(snapShotId, 0, 2147483647);
            bts.Name = btsParam.BtsName;
            bts.BTSID = btsParam.ENodeBID;
            bts.Type = btsParam.IsOutDoorBts ? LTEBTSType.Outdoor : LTEBTSType.Indoor;

            foreach (CellWorkParamBase info in btsParam.CellWorkParams)
            {
                CellWorkParam_QH cellParamInfo = info as CellWorkParam_QH;
                bts.Longitude = cellParamInfo.Longitude;
                bts.Latitude = cellParamInfo.Latitude;

                snapShotId--;
                LTECell cell = new LTECell();
                cell.Fill(snapShotId, 0, 2147483647);
                cell.BelongBTS = bts;
                cell.Name = cellParamInfo.CellName;
                cell.TAC = cellParamInfo.Tac;
                cell.ECI = cellParamInfo.Eci;
                cell.CellID = cellParamInfo.CellID;
                cell.PCI = cellParamInfo.Pci;
                cell.EARFCN = cellParamInfo.Earfcn;
                bts.AddCell(cell);
                MainModel.CellManager.Add(cell);

                LTEAntenna antenna = new LTEAntenna();
                snapShotId--;
                antenna.Fill(snapShotId, 0, 2147483647);
                antenna.Cell = cell;
                antenna.Longitude = cellParamInfo.Longitude;
                antenna.Latitude = cellParamInfo.Latitude;
                antenna.Direction = (short)cellParamInfo.Direction;
                antenna.Downward = (short)cellParamInfo.Downward;
                antenna.Altitude = cellParamInfo.Altitude;
            }
            MainModel.CellManager.Add(bts);
            #endregion

            return isNewAdd;
        }
        protected override void getFilesForAnalyse()
        {
            BackgroundFuncQueryManager.GetInstance().GetFilterFile_CellAccept(subFuncId, ServiceTypeString
                            , ((int)carrierID).ToString(), "strfilename", fileNameKeyStr);
        }
        protected override bool filterFile(FileInfo fileInfo)
        {
            if (fileInfo.Name.Contains("被叫") || fileInfo.Momt == (int)MoMtFile.MtFlag  //暂不分析被叫文件
                || fileInfo.Name.Contains("DT上传") || fileInfo.Name.Contains("DT下载"))//DT上传下载文件只用来截取覆盖图
            {
                return true;
            }
            return false;
        }
        protected override void doStatWithQuery()
        {
            if (curAnaFileInfo == null || MainModel.DTDataManager.FileDataManagers.Count == 0)
            {
                return;
            }
            manager = new StationAcceptManager_QH();
            manager.AnalyzeFile(curAnaFileInfo, MainModel.DTDataManager.FileDataManagers[0]);
            MainModel.DTDataManager.Clear();
        }
        protected override void saveBackgroundData()
        {
            List<BackgroundResult> resultList = new List<BackgroundResult>();
            if (manager.AcceptFileInfo != null && manager.AcceptFileInfo.AcceptKpiDic.Count > 0)
            {
                BackgroundResult result = manager.AcceptFileInfo.ConvertToBackgroundResult(subFuncId, BackgroundFuncConfigManager.GetInstance().ProjectType);
                resultList.Add(result);
                manager.AcceptFileInfo = null;

                //未匹配到目标小区或未获取到指标信息的文件信息暂不保留
                //（有可能是未更新工参信息导致的,或者DT上传下载文件是截取覆盖图用的，每次出报告都要重新查询回放）
                BackgroundFuncQueryManager.GetInstance().SaveResult_Cell(subFuncId, curAnaFileInfo, resultList);
            }
        }
        protected void exportReportByBgData(BtsWorkParam_QH btsWorkParamInfo)//查询预处理信息，然后导出报告
        {
            if (MainModel.BackgroundStopRequest)
            {
                return;
            }

            try
            {
                getBackgroundData();
                reportBackgroundInfo(string.Format("共读取到{0}条预处理信息", BackgroundResultList.Count));
                if (BackgroundResultList.Count <= 0)
                {
                    return;
                }
                BackgroundResultList.Sort(BackgroundResult.ComparerByISTimeDesc);

                if (btsWorkParamInfo.IsOutDoorBts)
                {
                    exportOutdoorBtsReport(BackgroundResultList, btsWorkParamInfo);
                }
                else
                {
                    exportIndoorBtsReport(BackgroundResultList, btsWorkParamInfo);
                }
            }
            catch(Exception ex)
            {
                reportBackgroundError(ex);
            }
        }
        private void exportOutdoorBtsReport(List<BackgroundResult> bgResultList, BtsWorkParam_QH btsWorkParamInfo)
        {
            OutDoorBtsAcceptInfo_QH curBtsAcceptInfo = StaionAcceptResultHelper.GetOutDoorBtsResultByBgData(
                bgResultList, btsWorkParamInfo.ENodeBID);

            if (curBtsAcceptInfo != null)
            {
                getBtsHandoverPic(curBtsAcceptInfo);
                getCellDtCoverPic(curBtsAcceptInfo);
                getFusionInfo(curBtsAcceptInfo, btsWorkParamInfo);
                GetPhoneTestInfoHelper.GetOutBtsPhoneTestInfo(curBtsAcceptInfo);
                bool hasExportReport = ExportOutdoorBtsReportHelper_QH.ExportReports(curBtsAcceptInfo, btsWorkParamInfo);
                updateBtsAcceptDes(curBtsAcceptInfo.IsAccordAccept, hasExportReport, btsWorkParamInfo);
            }
            else
            {
                reportBackgroundInfo("未匹配到目标基站信息，请核查路网通工参和待评估工参信息是否一致");
            }
        }
        private void exportIndoorBtsReport(List<BackgroundResult> bgResultList, BtsWorkParam_QH btsWorkParamInfo)
        {
            InDoorBtsAcceptInfo_QH curBtsAcceptInfo = StaionAcceptResultHelper.GetInDoorBtsResultByBgData(
              bgResultList, btsWorkParamInfo.ENodeBID);

            if (curBtsAcceptInfo != null)
            {
                getFusionInfo(curBtsAcceptInfo, btsWorkParamInfo);
                GetPhoneTestInfoHelper.GetInBtsPhoneTestInfo(curBtsAcceptInfo);
                bool hasExportReport = ExportIndoorBtsReportHelper_QH.ExportReports(curBtsAcceptInfo, btsWorkParamInfo);
                updateBtsAcceptDes(curBtsAcceptInfo.IsAccordAccept, hasExportReport, btsWorkParamInfo);
            }
            else
            {
                reportBackgroundInfo("未匹配到目标基站信息，请核查路网通工参和待评估工参信息是否一致");
            }
        }
        private void updateBtsAcceptDes(bool hasPassedAccept, bool hasExportReport, BtsWorkParam_QH btsParam)
        {
            if (hasPassedAccept && hasExportReport)
            {
                foreach (CellWorkParamBase info in btsParam.CellWorkParamDic.Values)
                {
                    CellWorkParam_QH cellParamInfo = info as CellWorkParam_QH;
                    cellParamInfo.StrDes = "已通过";
                }

                UpdateWorkParamDes upFunc = new UpdateWorkParamDes(btsParam);
                upFunc.Query();
            }
        }

        private QueryCondition getBgQueryCond()
        {
            BackgroundFuncConfigManager backgroundConfigManager = BackgroundFuncConfigManager.GetInstance();
            QueryCondition queryCond = new QueryCondition();
            DateTime startTime = backgroundConfigManager.StartTime;
            DateTime endTime = backgroundConfigManager.EndTime;
            queryCond.Periods.Add(new TimePeriod(startTime, endTime));
            queryCond.Projects = backgroundConfigManager.ProjectTypeList;
            foreach (ServiceType serviceType in this.ServiceTypes)
            {
                queryCond.ServiceTypes.Add((int)serviceType);
            }
            return queryCond;
        }
        private List<DTFileDataManager> getDtFilesByFileNameKey(string strFilter)
        {
            QueryCondition queryCond = getBgQueryCond();
            queryCond.NameFilterType = FileFilterType.ByFileName;
            int orNum = 1;
            queryCond.FileName = QueryCondition.MakeFileFilterString(strFilter, ref orNum).Replace("[_]", "_");
            queryCond.FileNameOrNum = orNum;

            QueryAndReplayFileInfo tpQuery = new QueryAndReplayFileInfo(1);
            tpQuery.SetQueryCondition(queryCond);
            tpQuery.Columns = this.Columns;
            tpQuery.Query();
            return tpQuery.DTFiles;
        }
        public string GetCoverPicPath(string btsName, string cellName, string paramName)//获取某种覆盖截图的保存路径
        {
            string folderPath = GetBtsPicFolder(btsName);
            if (!System.IO.Directory.Exists(folderPath))
            {
                System.IO.Directory.CreateDirectory(folderPath);
            }
            return System.IO.Path.Combine(folderPath, cellName + "_" + paramName + ".png");
        }
        public string GetBtsPicFolder(string btsName)//获取站点覆盖截图保存文件夹地址
        {
            return System.IO.Path.Combine(ExportOutdoorBtsReportHelper_QH.WorkDir, btsName.Trim());
        }

        #region 查询宏站的系统切换文件和DT测试文件，用于覆盖截图
        protected void getBtsHandoverPic(OutDoorBtsAcceptInfo_QH outBtsAcceptInfo)
        {
            string btsName = outBtsAcceptInfo.BtsName;
            reportBackgroundInfo("正在查询站点" + btsName + "的系统内切换文件...");
            string strFilter = string.Format("{0} and 系统内切换", btsName);
            List<DTFileDataManager> dtFiles = getDtFilesByFileNameKey(strFilter);
            if (dtFiles == null || dtFiles.Count <= 0)
            {
                strFilter = string.Format("{0} and 切换", btsName);
                dtFiles = getDtFilesByFileNameKey(strFilter);
            }

            if (dtFiles != null && dtFiles.Count > 0)
            {
                reportBackgroundInfo("查询到站点" + btsName + "的系统内切换文件共有" + dtFiles.Count + "个。");

                reSetPciMapView(outBtsAcceptInfo.LteBts.Cells);
                MapForm mapForm = mainModel.MainForm.GetMapForm();
                mapForm.GoToView(outBtsAcceptInfo.LteBts.Cells, dtFiles[0].TestPoints, this);

                outBtsAcceptInfo.CoverPicPath_Handover = GetCoverPicPath(btsName, btsName, "pci");
                mapForm.FireAndOutputCurMapToPic("lte_PCI", true, outBtsAcceptInfo.CoverPicPath_Handover);
            }
        }
        private void reSetPciMapView(List<LTECell> cellList)//重置pci图例
        {
            MapSerialInfo msi = DTLayerSerialManager.Instance.GetSerialByName("lte_PCI");
            if (msi != null)
            {
                List<Color> colorList = new List<Color> { Color.Red, Color.Green, Color.Blue, Color.GreenYellow, Color.Gray, Color.DarkOrange, Color.SkyBlue, Color.Silver, Color.LightGreen };
                if (colorList.Count >= cellList.Count)
                {
                    msi.ColorDisplayParam.Info.RangeColors = new List<DTParameterRangeColor>();
                    int i = 0;
                    foreach (LTECell cell in cellList)
                    {
                        DTParameterRangeColor paramColor = new DTParameterRangeColor(cell.PCI, cell.PCI, colorList[i]);
                        paramColor.MaxIncluded = true;
                        msi.ColorDisplayParam.Info.RangeColors.Add(paramColor);
                        i++;
                    }
                }
            }
        }

        protected void getCellDtCoverPic(OutDoorBtsAcceptInfo_QH outBtsAcceptInfo)
        {
            foreach (OutDoorCellAcceptInfo_QH cellInfo in outBtsAcceptInfo.CellsAcceptDic.Values)
            {
                string cellName = cellInfo.CellName;
                MapForm mapForm = mainModel.MainForm.GetMapForm();

                reportBackgroundInfo("正在查询小区" + cellName + "的DT下载文件...");
                string strFilter = string.Format("{0} and DT下载", cellName);
                List<DTFileDataManager> dtFiles = getDtFilesByFileNameKey(strFilter);
                if (dtFiles == null || dtFiles.Count <= 0)
                {
                    strFilter = string.Format("{0} and DL", cellName);
                    dtFiles = getDtFilesByFileNameKey(strFilter);
                }

                if (dtFiles != null && dtFiles.Count > 0)
                {
                    reportBackgroundInfo("查询到小区" + cellName + "的DT下载文件共有" + dtFiles.Count + "个。");

                    mapForm.GoToView(outBtsAcceptInfo.LteBts.Cells, dtFiles[0].TestPoints, this);

                    cellInfo.CoverPicPath_Rsrp = GetCoverPicPath(outBtsAcceptInfo.BtsName, cellName, "Rsrp");
                    cellInfo.CoverPicPath_Sinr = GetCoverPicPath(outBtsAcceptInfo.BtsName, cellName, "Sinr");
                    cellInfo.CoverPicPath_PdcpDL = GetCoverPicPath(outBtsAcceptInfo.BtsName, cellName, "PdcpDL");

                    mapForm.FireAndOutputCurMapToPic("lte_RSRP", true, cellInfo.CoverPicPath_Rsrp);
                    mapForm.FireAndOutputCurMapToPic("lte_SINR", true, cellInfo.CoverPicPath_Sinr);
                    mapForm.FireAndOutputCurMapToPic("lte_PDCP_DL_Mb", true, cellInfo.CoverPicPath_PdcpDL);
                }

                reportBackgroundInfo("正在查询" + cellName + "的DT上传文件...");
                strFilter = string.Format("{0} and DT上传", cellName);
                dtFiles = getDtFilesByFileNameKey(strFilter);
                if (dtFiles == null || dtFiles.Count <= 0)
                {
                    strFilter = string.Format("{0} and UL", cellName);
                    dtFiles = getDtFilesByFileNameKey(strFilter);
                }

                if (dtFiles != null && dtFiles.Count > 0)
                {
                    reportBackgroundInfo("查询到小区" + cellName + "的DT上传文件共有" + dtFiles.Count + "个。");

                    mapForm.GoToView(outBtsAcceptInfo.LteBts.Cells, dtFiles[0].TestPoints, this);

                    cellInfo.CoverPicPath_PdcpUL = GetCoverPicPath(outBtsAcceptInfo.BtsName, cellName, "PdcpUL");
                    mapForm.FireAndOutputCurMapToPic("lte_PDCP_UL_Mb", true, cellInfo.CoverPicPath_PdcpUL);
                }
            }
        }
        #endregion

        protected void getFusionInfo(BtsAcceptInfoBase btsInfo, BtsWorkParam_QH btsWorkParamInfo)
        {
            btsInfo.FusionInfo = null;
            if (FuncSet.IsAnaFusionDatas)
            {
                reportBackgroundInfo(string.Format("开始关联 {0} 站点的性能、告警、MR数据信息...", btsInfo.BtsName));
                btsInfo.FusionInfo = BtsFusionDataQuery_QH.GetFusionInfos(btsWorkParamInfo, FuncSet.FusionBeginTime
                    , FuncSet.FusionEndTime);

                btsInfo.FusionInfo.CheckIsAccordAccept(FuncSet);
                if (!btsInfo.FusionInfo.IsAccord)
                {
                    btsInfo.IsAccordAccept = false;
                    btsInfo.NotAccordKpiDes += btsInfo.FusionInfo.NotAccordKpiDes;//汇总不达标的指标信息
                }
            }
        }
        protected override void getBackgroundData()
        {
            BackgroundFuncConfigManager bgConfigManager = BackgroundFuncConfigManager.GetInstance();
            BackgroundResultList = BackgroundFuncQueryManager.GetInstance().GetFilterResult_CellAccept(new BackgroundFuncQueryManager.CellAcceptCondition(bgConfigManager.ISTime, bgConfigManager.IETime, subFuncId, bgConfigManager.ProjectType, "fileName", fileNameKeyStr), Name, StatType);
        }
        public override Dictionary<string, object> Param
        {
            get
            {
                ignoreParamKeys.Clear();
                ignoreParamKeys.Add("ExportReportSet");
                ignoreParamKeys.Add("Params_FTP");

                Dictionary<string, object> param = new Dictionary<string, object>();
                param["BackgroundStat"] = BackgroundStat;
                param["ExportReportSet"] = FuncSet.Params;
                param["Params_FTP"] = FuncSet.FtpSetInfo.Params;
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                if (param.ContainsKey("BackgroundStat"))
                {
                    BackgroundStat = (bool)param["BackgroundStat"];
                }
                if (param.ContainsKey("ExportReportSet"))
                {
                    FuncSet.Params = param["ExportReportSet"] as Dictionary<string, object>;
                }
                if (param.ContainsKey("Params_FTP"))
                {
                    FuncSet.FtpSetInfo.Params = param["Params_FTP"] as Dictionary<string, object>;
                }
            }
        }
        public override PropertiesControl Properties
        {
            get
            {
                return new StationAcceptPropertiesXJ_LTE(this, FuncSet);
            }
        }
        public override void DealAfterBackgroundQueryByCity()
        {
            if (workParamSumDic != null)
            {
                workParamSumDic.Clear();
            }

            if (!mainModel.BackgroundStopRequest && !string.IsNullOrEmpty(FuncSet.FtpSetInfo.FtpServerPath) 
                && ReportFilePaths.Count > 0)
            {
                reportBackgroundInfo("正在通过FTP上传单验报告...");

                string ftpFolderName = DateTime.Now.ToString("yyyyMMddHHmm");
                FtpHelper ftpHelper = new FtpHelper(FuncSet.FtpSetInfo);
                if (ftpHelper.CreatNewFtpFolder(ftpFolderName))
                {
                    foreach (string fileName in ReportFilePaths)
                    {
                        try
                        {
                            ftpHelper.UpLoadFile(fileName, ftpFolderName);
                        }
                        catch (Exception ex)
                        {
                            reportBackgroundInfo(string.Format("{0}FTP上传出错：{1}", fileName, ex.Message));
                        }
                    }
                    reportBackgroundInfo("上传单验报告完毕。");
                }
            }

            ReportFilePaths.Clear();
        }
        public override BackgroundStatType StatType
        {
            get { return BackgroundStatType.Cell_Region; }
        }
        public override BackgroundFuncType FuncType
        {
            get { return BackgroundFuncType.LTE业务专题; }
        }
        public override BackgroundSubFuncType SubFuncType
        {
            get { return BackgroundSubFuncType.单站验收; }
        }
    }
}
