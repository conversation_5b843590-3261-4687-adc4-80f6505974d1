using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.Util;
using System.Xml;

namespace MasterCom.RAMS.Model
{
    public class DTDisplayParameterSystem
    {
        public static object GetItemValue(XmlConfigFile configFile, XmlElement item, string typeName)
        {
            if (typeName.Equals(typeof(DTDisplayParameterSystem).Name))
            {
                DTDisplayParameterSystem info = new DTDisplayParameterSystem();
                info.FillItemValue(configFile, item);
                return info;
            }
            return null;
        }

        public static XmlElement AddItem(XmlConfigFile configFile, XmlElement config, string name, object value)
        {
            if (value is DTDisplayParameterSystem)
            {
                XmlElement item = configFile.AddItem(config, name, value.GetType());
                (value as DTDisplayParameterSystem).FillItem(configFile, item);
                return item;
            }
            return null;
        }
        public override string ToString()
        {
            return Name;
        }
        public DTDisplayParameterSystem()
        {
        }

        public DTDisplayParameterSystem(String name)
        {
            Name = name;
        }

        public string Name { get; set; }

        public IEnumerable<DTDisplayParameterInfo> DisplayParamInfos
        {
            get { return nameDTDisplayParameterInfoMap.Values; }
        }

        public void Add(DTDisplayParameterInfo displayParamInfo)
        {
            displayParamInfo.System = this;
            nameDTDisplayParameterInfoMap[displayParamInfo.Name] = displayParamInfo;
        }

        public void Remove(DTDisplayParameterInfo displayParamInfo)     //add
        {
            nameDTDisplayParameterInfoMap.Remove(displayParamInfo.Name);
        }

        public void Edit(DTDisplayParameterInfo displayParamInfo, string newName)     //add
        {
            nameDTDisplayParameterInfoMap.Remove(displayParamInfo.Name);
            displayParamInfo.Name = newName;
            nameDTDisplayParameterInfoMap.Add(newName, displayParamInfo);
        }

        public bool isValid(string displayName)     //add
        {
            if (nameDTDisplayParameterInfoMap.ContainsKey(displayName))
            {
                return false;
            }
            return true;
        }

        public DTDisplayParameterInfo this[String displayParamName]
        {
            get
            {
                if (nameDTDisplayParameterInfoMap.ContainsKey(displayParamName))
                {
                    return nameDTDisplayParameterInfoMap[displayParamName];
                }
                return null;
            }
        }

        private void FillItemValue(XmlConfigFile configFile, XmlElement item)
        {
            if (item != null)
            {
                Name = configFile.GetItemValue(item, "Name") as string;
                List<object> list = null;
                list = configFile.GetItemValue(item, "DTDisplayParameterInfos", DTDisplayParameterInfo.GetItemValue) as List<object>;
                if (list != null)
                {
                    foreach (object value in list)
                    {
                        if (((DTDisplayParameterInfo)value).ParamInfo != null)
                        {
                            Add((DTDisplayParameterInfo)value);
                        }
                    }
                }
            }
        }

        private void FillItem(XmlConfigFile configFile, XmlElement item)
        {
            configFile.AddItem(item, "Name", Name);
            configFile.AddItem(item, "DTDisplayParameterInfos", new List<DTDisplayParameterInfo>(nameDTDisplayParameterInfoMap.Values), DTDisplayParameterInfo.AddItem);
        }

        public bool Contains(string fullParamName)
        {
            foreach (DTDisplayParameterInfo item in nameDTDisplayParameterInfoMap.Values)
            {
                if (item.ParamInfo.Name==fullParamName)
                {
                    return true;
                }
            }
            return false;
        }
        private readonly SortedDictionary<String, DTDisplayParameterInfo> nameDTDisplayParameterInfoMap = new SortedDictionary<String, DTDisplayParameterInfo>();
    }
}
