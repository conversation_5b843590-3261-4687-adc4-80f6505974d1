﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTCellCoverTooCloseRatiusSetForm : BaseForm
    {
        public ZTCellCoverTooCloseRatiusSetForm(Type ztcellCoverLapByRegionLTE)
        {
            InitializeComponent();
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        internal int GetRatiusRegion()
        {
            int radius;
            int.TryParse(ratiusDistance.Value.ToString(), out radius);
            return radius;
        }

    }
}