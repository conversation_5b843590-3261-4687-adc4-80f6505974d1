﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ScanFartherCoverSettingDlg
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.spinEditRSRP = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.spinEditSampleNum = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.spinEditDistanceMin = new DevExpress.XtraEditors.SpinEdit();
            this.spinEditDistanceMax = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl5 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl6 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.simpleBtnOK = new DevExpress.XtraEditors.SimpleButton();
            this.simpleBtnCancel = new DevExpress.XtraEditors.SimpleButton();
            this.chbMaxRsrpCell = new System.Windows.Forms.CheckBox();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditRSRP.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditSampleNum.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditDistanceMin.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditDistanceMax.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // labelControl1
            // 
            this.labelControl1.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl1.Appearance.Options.UseFont = true;
            this.labelControl1.Location = new System.Drawing.Point(24, 14);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(78, 12);
            this.labelControl1.TabIndex = 0;
            this.labelControl1.Text = "采样点场强 ≥";
            // 
            // spinEditRSRP
            // 
            this.spinEditRSRP.EditValue = new decimal(new int[] {
            110,
            0,
            0,
            -2147483648});
            this.spinEditRSRP.Location = new System.Drawing.Point(108, 9);
            this.spinEditRSRP.Name = "spinEditRSRP";
            this.spinEditRSRP.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditRSRP.Properties.Appearance.Options.UseFont = true;
            this.spinEditRSRP.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditRSRP.Properties.Mask.EditMask = "f0";
            this.spinEditRSRP.Size = new System.Drawing.Size(75, 20);
            this.spinEditRSRP.TabIndex = 1;
            // 
            // labelControl2
            // 
            this.labelControl2.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl2.Appearance.Options.UseFont = true;
            this.labelControl2.Location = new System.Drawing.Point(24, 42);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(78, 12);
            this.labelControl2.TabIndex = 0;
            this.labelControl2.Text = "采样点数量 ≥";
            // 
            // spinEditSampleNum
            // 
            this.spinEditSampleNum.EditValue = new decimal(new int[] {
            15,
            0,
            0,
            0});
            this.spinEditSampleNum.Location = new System.Drawing.Point(108, 38);
            this.spinEditSampleNum.Name = "spinEditSampleNum";
            this.spinEditSampleNum.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditSampleNum.Properties.Appearance.Options.UseFont = true;
            this.spinEditSampleNum.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditSampleNum.Properties.Mask.EditMask = "f0";
            this.spinEditSampleNum.Size = new System.Drawing.Size(75, 20);
            this.spinEditSampleNum.TabIndex = 1;
            // 
            // labelControl3
            // 
            this.labelControl3.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl3.Appearance.Options.UseFont = true;
            this.labelControl3.Location = new System.Drawing.Point(106, 79);
            this.labelControl3.Name = "labelControl3";
            this.labelControl3.Size = new System.Drawing.Size(150, 12);
            this.labelControl3.TabIndex = 0;
            this.labelControl3.Text = "米 ≤ 采样点到小区距离 ≤";
            // 
            // spinEditDistanceMin
            // 
            this.spinEditDistanceMin.EditValue = new decimal(new int[] {
            1500,
            0,
            0,
            0});
            this.spinEditDistanceMin.Location = new System.Drawing.Point(24, 76);
            this.spinEditDistanceMin.Name = "spinEditDistanceMin";
            this.spinEditDistanceMin.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditDistanceMin.Properties.Appearance.Options.UseFont = true;
            this.spinEditDistanceMin.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditDistanceMin.Properties.Mask.EditMask = "f0";
            this.spinEditDistanceMin.Size = new System.Drawing.Size(75, 20);
            this.spinEditDistanceMin.TabIndex = 1;
            // 
            // spinEditDistanceMax
            // 
            this.spinEditDistanceMax.EditValue = new decimal(new int[] {
            6000,
            0,
            0,
            0});
            this.spinEditDistanceMax.Location = new System.Drawing.Point(262, 75);
            this.spinEditDistanceMax.Name = "spinEditDistanceMax";
            this.spinEditDistanceMax.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditDistanceMax.Properties.Appearance.Options.UseFont = true;
            this.spinEditDistanceMax.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditDistanceMax.Properties.Mask.EditMask = "f0";
            this.spinEditDistanceMax.Size = new System.Drawing.Size(75, 20);
            this.spinEditDistanceMax.TabIndex = 1;
            // 
            // labelControl5
            // 
            this.labelControl5.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl5.Appearance.Options.UseFont = true;
            this.labelControl5.Location = new System.Drawing.Point(185, 13);
            this.labelControl5.Name = "labelControl5";
            this.labelControl5.Size = new System.Drawing.Size(18, 12);
            this.labelControl5.TabIndex = 0;
            this.labelControl5.Text = "dBm";
            // 
            // labelControl6
            // 
            this.labelControl6.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl6.Appearance.Options.UseFont = true;
            this.labelControl6.Location = new System.Drawing.Point(188, 41);
            this.labelControl6.Name = "labelControl6";
            this.labelControl6.Size = new System.Drawing.Size(12, 12);
            this.labelControl6.TabIndex = 0;
            this.labelControl6.Text = "个";
            // 
            // labelControl4
            // 
            this.labelControl4.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl4.Appearance.Options.UseFont = true;
            this.labelControl4.Location = new System.Drawing.Point(346, 79);
            this.labelControl4.Name = "labelControl4";
            this.labelControl4.Size = new System.Drawing.Size(12, 12);
            this.labelControl4.TabIndex = 0;
            this.labelControl4.Text = "米";
            // 
            // simpleBtnOK
            // 
            this.simpleBtnOK.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.simpleBtnOK.Appearance.Options.UseFont = true;
            this.simpleBtnOK.Location = new System.Drawing.Point(188, 146);
            this.simpleBtnOK.Name = "simpleBtnOK";
            this.simpleBtnOK.Size = new System.Drawing.Size(75, 23);
            this.simpleBtnOK.TabIndex = 2;
            this.simpleBtnOK.Text = "确定";
            this.simpleBtnOK.Click += new System.EventHandler(this.simpleBtnOK_Click);
            // 
            // simpleBtnCancel
            // 
            this.simpleBtnCancel.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.simpleBtnCancel.Appearance.Options.UseFont = true;
            this.simpleBtnCancel.Location = new System.Drawing.Point(283, 146);
            this.simpleBtnCancel.Name = "simpleBtnCancel";
            this.simpleBtnCancel.Size = new System.Drawing.Size(75, 23);
            this.simpleBtnCancel.TabIndex = 2;
            this.simpleBtnCancel.Text = "取消";
            this.simpleBtnCancel.Click += new System.EventHandler(this.simpleBtnCancel_Click);
            // 
            // chbMaxRsrpCell
            // 
            this.chbMaxRsrpCell.AutoSize = true;
            this.chbMaxRsrpCell.Checked = true;
            this.chbMaxRsrpCell.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chbMaxRsrpCell.Location = new System.Drawing.Point(24, 113);
            this.chbMaxRsrpCell.Name = "chbMaxRsrpCell";
            this.chbMaxRsrpCell.Size = new System.Drawing.Size(108, 16);
            this.chbMaxRsrpCell.TabIndex = 3;
            this.chbMaxRsrpCell.Text = "只查第一强小区";
            this.chbMaxRsrpCell.UseVisualStyleBackColor = true;
            // 
            // ScanFartherCoverSettingDlg
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(384, 193);
            this.Controls.Add(this.chbMaxRsrpCell);
            this.Controls.Add(this.simpleBtnCancel);
            this.Controls.Add(this.simpleBtnOK);
            this.Controls.Add(this.spinEditDistanceMax);
            this.Controls.Add(this.spinEditDistanceMin);
            this.Controls.Add(this.labelControl4);
            this.Controls.Add(this.labelControl6);
            this.Controls.Add(this.labelControl3);
            this.Controls.Add(this.spinEditSampleNum);
            this.Controls.Add(this.labelControl5);
            this.Controls.Add(this.labelControl2);
            this.Controls.Add(this.spinEditRSRP);
            this.Controls.Add(this.labelControl1);
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "ScanFartherCoverSettingDlg";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "超远覆盖设置";
            ((System.ComponentModel.ISupportInitialize)(this.spinEditRSRP.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditSampleNum.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditDistanceMin.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditDistanceMax.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.SpinEdit spinEditRSRP;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.SpinEdit spinEditSampleNum;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.SpinEdit spinEditDistanceMin;
        private DevExpress.XtraEditors.SpinEdit spinEditDistanceMax;
        private DevExpress.XtraEditors.LabelControl labelControl5;
        private DevExpress.XtraEditors.LabelControl labelControl6;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private DevExpress.XtraEditors.SimpleButton simpleBtnOK;
        private DevExpress.XtraEditors.SimpleButton simpleBtnCancel;
        private System.Windows.Forms.CheckBox chbMaxRsrpCell;
    }
}