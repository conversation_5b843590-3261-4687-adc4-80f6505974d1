﻿using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.ZTFunc;
using MasterCom.Util.UiEx;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.Func
{
    /// <summary>
    /// 测试继承ZTHandoverToolMuchFormBase
    /// </summary>
    public partial class ZTTAUHandoverTooMuchNewForm : ZTHandoverToolMuchBaseForm
    {
        public override string FormName
        {
            get { return "跟踪区频繁更新"; }
        }

        public ZTTAUHandoverTooMuchNewForm()
            : base()
        {
            InitializeComponent();
            DisposeWhenClose = true;
        }

        protected override string ShowImage
        {
            get
            {
                return "images\\dbmng.gif";
            }
        }
    }
}
