﻿using MasterCom.RAMS.Net;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.BackgroundFunc.StationAccept_Shanxi
{
    public class WorkParamsImportQuery : DiySqlMultiNonQuery
    {
        readonly Dictionary<string, Dictionary<string, CellAcceptWorkParam_SX>> workParams;
        readonly string tableName;
        readonly NetType type;
        public WorkParamsImportQuery(Dictionary<string, Dictionary<string, CellAcceptWorkParam_SX>> workParams, NetType type)
            : base()
        {
            MainDB = true;
            this.workParams = workParams;
            this.type = type;
            tableName = ParamsHelper.GetWorkParamsTableName(type);
        }

        protected override string getSqlTextString()
        {
            StringBuilder strb = new StringBuilder();
            foreach (var btsInfo in workParams)
            {
                strb.Append($"delete from {tableName} where BtsName = '{btsInfo.Key}';");
                foreach (var cellInfo in btsInfo.Value.Values)
                {
                    if (type == NetType.LTE)
                    {
                        addLteParam(strb, cellInfo);
                    }
                    else if (type == NetType.NR)
                    {
                        addNrParam(strb, cellInfo);
                    }
                }
            }
            return strb.ToString();
        }

        private void addLteParam(StringBuilder strb, CellAcceptWorkParam_SX cellInfo)
        {
            strb.Append($"insert into [{tableName}] ([DistrictName],[BtsName],[ENodeBID]," +
                $"[CellName],[CellID],[SectorID],[Tac],[Eci],[Earfcn],[Pci],[Longitude]," +
                $"[Latitude],[CoverTypeDes],[Altitude],[Direction],[Downward],[AnalysedType]," +
                $"[UpdateTime],[Remark],[ImportTime]) values " +
                $"('{cellInfo.DistrictName}','{cellInfo.BtsNameFull}'," +
                $"{cellInfo.ENodeBID},'{cellInfo.CellNameFull}',{cellInfo.CellID}," +
                $"{cellInfo.SectorID},{cellInfo.Tac},{cellInfo.Eci},{cellInfo.Earfcn}," +
                $"{cellInfo.Pci},{cellInfo.Longitude * 10000000},{cellInfo.Latitude * 10000000}," +
                $"'{cellInfo.CoverTypeDes}',{cellInfo.Altitude},{cellInfo.Direction}," +
                $"{cellInfo.Downward},{(int)AnalyseType.NeedAnalyse},'{cellInfo.UpdateTime}'," +
                $"'{cellInfo.Remark}','{cellInfo.ImportTime}');");
        }

        private void addNrParam(StringBuilder strb, CellAcceptWorkParam_SX cellInfo)
        {
            CellAcceptWorkParam_SX_NR nrCellInfo = cellInfo as CellAcceptWorkParam_SX_NR;
            strb.Append($"insert into [{tableName}] ([DistrictName],[BtsName],[ENodeBID]," +
               $"[CellName],[CellID],[Tac],[Nci],[Earfcn],[Pci],[SsbArfcn],[Longitude]," +
               $"[Latitude],[CoverTypeDes],[Altitude],[Direction],[Downward],[AnalysedType]," +
               $"[UpdateTime],[Remark],[ImportTime]) values " +
               $"('{nrCellInfo.DistrictName}','{nrCellInfo.BtsNameFull}'," +
               $"{nrCellInfo.ENodeBID},'{nrCellInfo.CellNameFull}',{nrCellInfo.CellID}," +
               $"{nrCellInfo.Tac},{nrCellInfo.Nci},{nrCellInfo.Earfcn},{nrCellInfo.Pci}," +
               $"{nrCellInfo.SsbArfcn},{nrCellInfo.Longitude * 10000000},{nrCellInfo.Latitude * 10000000}," +
               $"'{nrCellInfo.CoverTypeDes}',{nrCellInfo.Altitude},{nrCellInfo.Direction}," +
               $"{nrCellInfo.Downward},{(int)AnalyseType.NeedAnalyse},'{nrCellInfo.UpdateTime}'," +
               $"'{nrCellInfo.Remark}','{nrCellInfo.ImportTime}');");
        }
    }

    public class WorkParamsUpdateQuery<T> : DiySqlMultiNonQuery
        where T : CellAcceptWorkParam_SX
    {
        readonly Dictionary<string, T> selectedData;
        readonly string tableName;
        readonly NetType type;

        public WorkParamsUpdateQuery(Dictionary<string, T> selectedRow, NetType type)
        {
            MainDB = true;
            selectedData = selectedRow;
            this.type = type;
            tableName = ParamsHelper.GetWorkParamsTableName(type);
        }

        protected override string getSqlTextString()
        {
            StringBuilder sql = new StringBuilder();
            foreach (var data in selectedData)
            {
                string[] token = data.Key.Split('_');
                string eNodeBID = token[0];
                string cellName = token[1];
                if (type == NetType.LTE)
                {
                    dealLteParam(sql, data, eNodeBID, cellName);
                }
                else if (type == NetType.NR)
                {
                    dealNrParam(sql, data, eNodeBID, cellName);
                }
            }

            return sql.ToString();
        }

        private void dealLteParam(StringBuilder sql, KeyValuePair<string, T> data, string eNodeBID, string cellName)
        {
            CellAcceptWorkParam_SX info = data.Value;
            sql.Append($"update {tableName} set DistrictName='{info.DistrictName}'," +
                $"BtsName='{info.BtsNameFull}',ENodeBID={info.ENodeBID},CellName='{info.CellNameFull}'," +
                $"CellID={info.CellID},SectorID={info.SectorID},Tac={info.Tac},Eci={info.Eci}," +
                $"Earfcn={info.Earfcn},Pci={info.Pci},Longitude={info.Longitude * 10000000}," +
                $"Latitude={info.Latitude * 10000000},CoverTypeDes='{info.CoverTypeDes}'," +
                $"Altitude={info.Altitude},Direction={info.Direction},Downward={info.Downward}," +
                $"AnalysedType={info.AnalysedType},UpdateTime='{DateTime.Now}' " +
                $"where ENodeBID = {eNodeBID} and CellID = {cellName};");
        }

        private void dealNrParam(StringBuilder sql, KeyValuePair<string, T> data, string eNodeBID, string cellName)
        {
            CellAcceptWorkParam_SX_NR info = data.Value as CellAcceptWorkParam_SX_NR;
            sql.Append($"update {tableName} set DistrictName='{info.DistrictName}'," +
                $"BtsName='{info.BtsNameFull}',ENodeBID={info.ENodeBID},CellName='{info.CellNameFull}'," +
                $"CellID={info.CellID},Tac={info.Tac},Eci={info.Eci},Earfcn={info.Earfcn}," +
                $"Pci={info.Pci},SsbArfcn={info.SsbArfcn},Longitude={info.Longitude * 10000000}," +
                $"Latitude={info.Latitude * 10000000},CoverTypeDes='{info.CoverTypeDes}'," +
                $"Altitude={info.Altitude},Direction={info.Direction},Downward={info.Downward}," +
                $"AnalysedType={info.AnalysedType},UpdateTime='{DateTime.Now}' " +
                $"where ENodeBID = {eNodeBID} and CellID = {cellName};");
        }
    }
}
