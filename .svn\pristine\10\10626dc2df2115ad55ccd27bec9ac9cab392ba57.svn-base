﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTGSMRxQualAnaSetForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.groupBox7 = new System.Windows.Forms.GroupBox();
            this.tbxSuggest = new System.Windows.Forms.TextBox();
            this.groupBox16 = new System.Windows.Forms.GroupBox();
            this.tbxDescription = new System.Windows.Forms.TextBox();
            this.simpleButtonDown = new DevExpress.XtraEditors.SimpleButton();
            this.simpleButtonUp = new DevExpress.XtraEditors.SimpleButton();
            this.checkedListBoxControlReason = new DevExpress.XtraEditors.CheckedListBoxControl();
            this.simpleButtonCancel = new DevExpress.XtraEditors.SimpleButton();
            this.simpleButtonOK = new DevExpress.XtraEditors.SimpleButton();
            this.splitContainerControl1 = new DevExpress.XtraEditors.SplitContainerControl();
            this.xtraTabControl1 = new DevExpress.XtraTab.XtraTabControl();
            this.xtraTabCover = new DevExpress.XtraTab.XtraTabPage();
            this.groupBox6 = new System.Windows.Forms.GroupBox();
            this.labelControl20 = new DevExpress.XtraEditors.LabelControl();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.spinEditWeakCover_NBCell_PccpchRscp = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl16 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl15 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.spinEditWeakCover_ServCell_PccpchRscp = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl14 = new DevExpress.XtraEditors.LabelControl();
            this.groupBox5 = new System.Windows.Forms.GroupBox();
            this.labelControl13 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl10 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl11 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl12 = new DevExpress.XtraEditors.LabelControl();
            this.spinEditNoMainCover_CellCount = new DevExpress.XtraEditors.SpinEdit();
            this.spinEditNoMainCover_PccpchRscp_Span = new DevExpress.XtraEditors.SpinEdit();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl5 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.spinEditLapCover_Ratio = new DevExpress.XtraEditors.SpinEdit();
            this.spinEditLapCover_PccpchRscp = new DevExpress.XtraEditors.SpinEdit();
            this.groupBox4 = new System.Windows.Forms.GroupBox();
            this.labelControl6 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl8 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl9 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl7 = new DevExpress.XtraEditors.LabelControl();
            this.spinEditBackCover_Angle = new DevExpress.XtraEditors.SpinEdit();
            this.spinEditBackCover_Distance = new DevExpress.XtraEditors.SpinEdit();
            this.xtraTabHO = new DevExpress.XtraTab.XtraTabPage();
            this.groupBox9 = new System.Windows.Forms.GroupBox();
            this.labelControl22 = new DevExpress.XtraEditors.LabelControl();
            this.spinEditReSelectionProb_Span = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl23 = new DevExpress.XtraEditors.LabelControl();
            this.groupBox12 = new System.Windows.Forms.GroupBox();
            this.labelControl18 = new DevExpress.XtraEditors.LabelControl();
            this.spinEditHOAbnormal_Span = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl38 = new DevExpress.XtraEditors.LabelControl();
            this.groupBox11 = new System.Windows.Forms.GroupBox();
            this.labelControl33 = new DevExpress.XtraEditors.LabelControl();
            this.xtraTabOther = new DevExpress.XtraTab.XtraTabPage();
            this.groupBox8 = new System.Windows.Forms.GroupBox();
            this.spinEditSpur_Span_Behind = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl17 = new DevExpress.XtraEditors.LabelControl();
            this.spinEditSpur_Span_Ahead = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl19 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl21 = new DevExpress.XtraEditors.LabelControl();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.label1 = new System.Windows.Forms.Label();
            this.spinEditFreqInterference_C2I = new DevExpress.XtraEditors.SpinEdit();
            this.label11 = new System.Windows.Forms.Label();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            this.groupBox7.SuspendLayout();
            this.groupBox16.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.checkedListBoxControlReason)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).BeginInit();
            this.splitContainerControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).BeginInit();
            this.xtraTabControl1.SuspendLayout();
            this.xtraTabCover.SuspendLayout();
            this.groupBox6.SuspendLayout();
            this.groupBox2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditWeakCover_NBCell_PccpchRscp.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditWeakCover_ServCell_PccpchRscp.Properties)).BeginInit();
            this.groupBox5.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditNoMainCover_CellCount.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditNoMainCover_PccpchRscp_Span.Properties)).BeginInit();
            this.groupBox3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditLapCover_Ratio.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditLapCover_PccpchRscp.Properties)).BeginInit();
            this.groupBox4.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditBackCover_Angle.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditBackCover_Distance.Properties)).BeginInit();
            this.xtraTabHO.SuspendLayout();
            this.groupBox9.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditReSelectionProb_Span.Properties)).BeginInit();
            this.groupBox12.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditHOAbnormal_Span.Properties)).BeginInit();
            this.groupBox11.SuspendLayout();
            this.xtraTabOther.SuspendLayout();
            this.groupBox8.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditSpur_Span_Behind.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditSpur_Span_Ahead.Properties)).BeginInit();
            this.groupBox1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditFreqInterference_C2I.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // groupControl1
            // 
            this.groupControl1.Controls.Add(this.groupBox7);
            this.groupControl1.Controls.Add(this.groupBox16);
            this.groupControl1.Controls.Add(this.simpleButtonDown);
            this.groupControl1.Controls.Add(this.simpleButtonUp);
            this.groupControl1.Controls.Add(this.checkedListBoxControlReason);
            this.groupControl1.Controls.Add(this.simpleButtonCancel);
            this.groupControl1.Controls.Add(this.simpleButtonOK);
            this.groupControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControl1.Location = new System.Drawing.Point(0, 0);
            this.groupControl1.Name = "groupControl1";
            this.groupControl1.Size = new System.Drawing.Size(1053, 392);
            this.groupControl1.TabIndex = 1;
            this.groupControl1.Text = "分析顺序";
            // 
            // groupBox7
            // 
            this.groupBox7.Controls.Add(this.tbxSuggest);
            this.groupBox7.Location = new System.Drawing.Point(370, 181);
            this.groupBox7.Name = "groupBox7";
            this.groupBox7.Size = new System.Drawing.Size(654, 127);
            this.groupBox7.TabIndex = 7;
            this.groupBox7.TabStop = false;
            this.groupBox7.Text = "优化建议";
            // 
            // tbxSuggest
            // 
            this.tbxSuggest.Location = new System.Drawing.Point(35, 23);
            this.tbxSuggest.Multiline = true;
            this.tbxSuggest.Name = "tbxSuggest";
            this.tbxSuggest.ReadOnly = true;
            this.tbxSuggest.Size = new System.Drawing.Size(592, 89);
            this.tbxSuggest.TabIndex = 5;
            // 
            // groupBox16
            // 
            this.groupBox16.Controls.Add(this.tbxDescription);
            this.groupBox16.Location = new System.Drawing.Point(370, 38);
            this.groupBox16.Name = "groupBox16";
            this.groupBox16.Size = new System.Drawing.Size(654, 127);
            this.groupBox16.TabIndex = 6;
            this.groupBox16.TabStop = false;
            this.groupBox16.Text = "分析场景说明";
            // 
            // tbxDescription
            // 
            this.tbxDescription.Location = new System.Drawing.Point(26, 21);
            this.tbxDescription.Multiline = true;
            this.tbxDescription.Name = "tbxDescription";
            this.tbxDescription.ReadOnly = true;
            this.tbxDescription.Size = new System.Drawing.Size(601, 89);
            this.tbxDescription.TabIndex = 5;
            // 
            // simpleButtonDown
            // 
            this.simpleButtonDown.Appearance.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.simpleButtonDown.Appearance.Options.UseFont = true;
            this.simpleButtonDown.Location = new System.Drawing.Point(278, 98);
            this.simpleButtonDown.Name = "simpleButtonDown";
            this.simpleButtonDown.Size = new System.Drawing.Size(64, 27);
            this.simpleButtonDown.TabIndex = 4;
            this.simpleButtonDown.Text = "↓";
            this.simpleButtonDown.Click += new System.EventHandler(this.simpleButtonDown_Click);
            // 
            // simpleButtonUp
            // 
            this.simpleButtonUp.Appearance.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.simpleButtonUp.Appearance.Options.UseFont = true;
            this.simpleButtonUp.Location = new System.Drawing.Point(278, 59);
            this.simpleButtonUp.Name = "simpleButtonUp";
            this.simpleButtonUp.Size = new System.Drawing.Size(64, 27);
            this.simpleButtonUp.TabIndex = 4;
            this.simpleButtonUp.Text = "↑";
            this.simpleButtonUp.Click += new System.EventHandler(this.simpleButtonUp_Click);
            // 
            // checkedListBoxControlReason
            // 
            this.checkedListBoxControlReason.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.checkedListBoxControlReason.Appearance.Options.UseFont = true;
            this.checkedListBoxControlReason.Location = new System.Drawing.Point(24, 38);
            this.checkedListBoxControlReason.Name = "checkedListBoxControlReason";
            this.checkedListBoxControlReason.Size = new System.Drawing.Size(241, 316);
            this.checkedListBoxControlReason.TabIndex = 3;
            this.checkedListBoxControlReason.SelectedIndexChanged += new System.EventHandler(this.checkedListBoxControlReason_SelectedIndexChanged);
            // 
            // simpleButtonCancel
            // 
            this.simpleButtonCancel.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.simpleButtonCancel.Appearance.Options.UseFont = true;
            this.simpleButtonCancel.Location = new System.Drawing.Point(940, 327);
            this.simpleButtonCancel.Name = "simpleButtonCancel";
            this.simpleButtonCancel.Size = new System.Drawing.Size(87, 27);
            this.simpleButtonCancel.TabIndex = 2;
            this.simpleButtonCancel.Text = "取消";
            this.simpleButtonCancel.Click += new System.EventHandler(this.simpleButtonCancel_Click);
            // 
            // simpleButtonOK
            // 
            this.simpleButtonOK.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.simpleButtonOK.Appearance.Options.UseFont = true;
            this.simpleButtonOK.Location = new System.Drawing.Point(829, 327);
            this.simpleButtonOK.Name = "simpleButtonOK";
            this.simpleButtonOK.Size = new System.Drawing.Size(87, 27);
            this.simpleButtonOK.TabIndex = 2;
            this.simpleButtonOK.Text = "确定";
            this.simpleButtonOK.Click += new System.EventHandler(this.simpleButtonOK_Click);
            // 
            // splitContainerControl1
            // 
            this.splitContainerControl1.CollapsePanel = DevExpress.XtraEditors.SplitCollapsePanel.Panel1;
            this.splitContainerControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl1.Horizontal = false;
            this.splitContainerControl1.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl1.Name = "splitContainerControl1";
            this.splitContainerControl1.Panel1.Controls.Add(this.xtraTabControl1);
            this.splitContainerControl1.Panel1.Text = "Panel1";
            this.splitContainerControl1.Panel2.Controls.Add(this.groupControl1);
            this.splitContainerControl1.Panel2.Text = "Panel2";
            this.splitContainerControl1.Size = new System.Drawing.Size(1053, 632);
            this.splitContainerControl1.SplitterPosition = 234;
            this.splitContainerControl1.TabIndex = 3;
            this.splitContainerControl1.Text = "splitContainerControl1";
            // 
            // xtraTabControl1
            // 
            this.xtraTabControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.xtraTabControl1.Location = new System.Drawing.Point(0, 0);
            this.xtraTabControl1.Name = "xtraTabControl1";
            this.xtraTabControl1.SelectedTabPage = this.xtraTabCover;
            this.xtraTabControl1.Size = new System.Drawing.Size(1053, 234);
            this.xtraTabControl1.TabIndex = 0;
            this.xtraTabControl1.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.xtraTabCover,
            this.xtraTabHO,
            this.xtraTabOther});
            // 
            // xtraTabCover
            // 
            this.xtraTabCover.Controls.Add(this.groupBox6);
            this.xtraTabCover.Controls.Add(this.groupBox2);
            this.xtraTabCover.Controls.Add(this.groupBox5);
            this.xtraTabCover.Controls.Add(this.groupBox3);
            this.xtraTabCover.Controls.Add(this.groupBox4);
            this.xtraTabCover.Name = "xtraTabCover";
            this.xtraTabCover.Size = new System.Drawing.Size(1046, 204);
            this.xtraTabCover.Text = "覆盖类";
            // 
            // groupBox6
            // 
            this.groupBox6.Controls.Add(this.labelControl20);
            this.groupBox6.Location = new System.Drawing.Point(368, 110);
            this.groupBox6.Name = "groupBox6";
            this.groupBox6.Size = new System.Drawing.Size(183, 86);
            this.groupBox6.TabIndex = 4;
            this.groupBox6.TabStop = false;
            this.groupBox6.Text = "室分泄露";
            // 
            // labelControl20
            // 
            this.labelControl20.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl20.Appearance.Options.UseFont = true;
            this.labelControl20.Location = new System.Drawing.Point(45, 41);
            this.labelControl20.Name = "labelControl20";
            this.labelControl20.Size = new System.Drawing.Size(84, 12);
            this.labelControl20.TabIndex = 1;
            this.labelControl20.Text = "占用到室分小区";
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.spinEditWeakCover_NBCell_PccpchRscp);
            this.groupBox2.Controls.Add(this.labelControl16);
            this.groupBox2.Controls.Add(this.labelControl15);
            this.groupBox2.Controls.Add(this.labelControl1);
            this.groupBox2.Controls.Add(this.spinEditWeakCover_ServCell_PccpchRscp);
            this.groupBox2.Controls.Add(this.labelControl14);
            this.groupBox2.Location = new System.Drawing.Point(26, 8);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(314, 91);
            this.groupBox2.TabIndex = 2;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "弱覆盖";
            // 
            // spinEditWeakCover_NBCell_PccpchRscp
            // 
            this.spinEditWeakCover_NBCell_PccpchRscp.EditValue = new decimal(new int[] {
            85,
            0,
            0,
            -2147483648});
            this.spinEditWeakCover_NBCell_PccpchRscp.Location = new System.Drawing.Point(156, 54);
            this.spinEditWeakCover_NBCell_PccpchRscp.Name = "spinEditWeakCover_NBCell_PccpchRscp";
            this.spinEditWeakCover_NBCell_PccpchRscp.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditWeakCover_NBCell_PccpchRscp.Properties.Appearance.Options.UseFont = true;
            this.spinEditWeakCover_NBCell_PccpchRscp.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditWeakCover_NBCell_PccpchRscp.Properties.Mask.EditMask = "f0";
            this.spinEditWeakCover_NBCell_PccpchRscp.Size = new System.Drawing.Size(96, 20);
            this.spinEditWeakCover_NBCell_PccpchRscp.TabIndex = 4;
            // 
            // labelControl16
            // 
            this.labelControl16.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl16.Appearance.Options.UseFont = true;
            this.labelControl16.Location = new System.Drawing.Point(260, 58);
            this.labelControl16.Name = "labelControl16";
            this.labelControl16.Size = new System.Drawing.Size(18, 12);
            this.labelControl16.TabIndex = 5;
            this.labelControl16.Text = "dBm";
            // 
            // labelControl15
            // 
            this.labelControl15.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl15.Appearance.Options.UseFont = true;
            this.labelControl15.Location = new System.Drawing.Point(47, 58);
            this.labelControl15.Name = "labelControl15";
            this.labelControl15.Size = new System.Drawing.Size(102, 12);
            this.labelControl15.TabIndex = 3;
            this.labelControl15.Text = "第一强邻区场强 ≤";
            // 
            // labelControl1
            // 
            this.labelControl1.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl1.Appearance.Options.UseFont = true;
            this.labelControl1.Location = new System.Drawing.Point(83, 27);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(66, 12);
            this.labelControl1.TabIndex = 1;
            this.labelControl1.Text = "主服场强 ≤";
            // 
            // spinEditWeakCover_ServCell_PccpchRscp
            // 
            this.spinEditWeakCover_ServCell_PccpchRscp.EditValue = new decimal(new int[] {
            85,
            0,
            0,
            -2147483648});
            this.spinEditWeakCover_ServCell_PccpchRscp.Location = new System.Drawing.Point(156, 22);
            this.spinEditWeakCover_ServCell_PccpchRscp.Name = "spinEditWeakCover_ServCell_PccpchRscp";
            this.spinEditWeakCover_ServCell_PccpchRscp.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditWeakCover_ServCell_PccpchRscp.Properties.Appearance.Options.UseFont = true;
            this.spinEditWeakCover_ServCell_PccpchRscp.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditWeakCover_ServCell_PccpchRscp.Properties.Mask.EditMask = "f0";
            this.spinEditWeakCover_ServCell_PccpchRscp.Size = new System.Drawing.Size(96, 20);
            this.spinEditWeakCover_ServCell_PccpchRscp.TabIndex = 0;
            // 
            // labelControl14
            // 
            this.labelControl14.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl14.Appearance.Options.UseFont = true;
            this.labelControl14.Location = new System.Drawing.Point(260, 27);
            this.labelControl14.Name = "labelControl14";
            this.labelControl14.Size = new System.Drawing.Size(18, 12);
            this.labelControl14.TabIndex = 2;
            this.labelControl14.Text = "dBm";
            // 
            // groupBox5
            // 
            this.groupBox5.Controls.Add(this.labelControl13);
            this.groupBox5.Controls.Add(this.labelControl10);
            this.groupBox5.Controls.Add(this.labelControl11);
            this.groupBox5.Controls.Add(this.labelControl12);
            this.groupBox5.Controls.Add(this.spinEditNoMainCover_CellCount);
            this.groupBox5.Controls.Add(this.spinEditNoMainCover_PccpchRscp_Span);
            this.groupBox5.Location = new System.Drawing.Point(26, 106);
            this.groupBox5.Name = "groupBox5";
            this.groupBox5.Size = new System.Drawing.Size(314, 90);
            this.groupBox5.TabIndex = 5;
            this.groupBox5.TabStop = false;
            this.groupBox5.Text = "覆盖杂乱";
            // 
            // labelControl13
            // 
            this.labelControl13.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl13.Appearance.Options.UseFont = true;
            this.labelControl13.Location = new System.Drawing.Point(84, 58);
            this.labelControl13.Name = "labelControl13";
            this.labelControl13.Size = new System.Drawing.Size(66, 12);
            this.labelControl13.TabIndex = 4;
            this.labelControl13.Text = "小区数量 ≥";
            // 
            // labelControl10
            // 
            this.labelControl10.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl10.Appearance.Options.UseFont = true;
            this.labelControl10.Location = new System.Drawing.Point(48, 27);
            this.labelControl10.Name = "labelControl10";
            this.labelControl10.Size = new System.Drawing.Size(102, 12);
            this.labelControl10.TabIndex = 3;
            this.labelControl10.Text = "与最大电平相差 ≤";
            // 
            // labelControl11
            // 
            this.labelControl11.Location = new System.Drawing.Point(261, 56);
            this.labelControl11.Name = "labelControl11";
            this.labelControl11.Size = new System.Drawing.Size(12, 14);
            this.labelControl11.TabIndex = 2;
            this.labelControl11.Text = "个";
            // 
            // labelControl12
            // 
            this.labelControl12.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl12.Appearance.Options.UseFont = true;
            this.labelControl12.Location = new System.Drawing.Point(261, 27);
            this.labelControl12.Name = "labelControl12";
            this.labelControl12.Size = new System.Drawing.Size(12, 12);
            this.labelControl12.TabIndex = 2;
            this.labelControl12.Text = "dB";
            // 
            // spinEditNoMainCover_CellCount
            // 
            this.spinEditNoMainCover_CellCount.EditValue = new decimal(new int[] {
            3,
            0,
            0,
            0});
            this.spinEditNoMainCover_CellCount.Location = new System.Drawing.Point(156, 54);
            this.spinEditNoMainCover_CellCount.Name = "spinEditNoMainCover_CellCount";
            this.spinEditNoMainCover_CellCount.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditNoMainCover_CellCount.Properties.Appearance.Options.UseFont = true;
            this.spinEditNoMainCover_CellCount.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditNoMainCover_CellCount.Properties.Mask.EditMask = "f0";
            this.spinEditNoMainCover_CellCount.Size = new System.Drawing.Size(96, 20);
            this.spinEditNoMainCover_CellCount.TabIndex = 0;
            // 
            // spinEditNoMainCover_PccpchRscp_Span
            // 
            this.spinEditNoMainCover_PccpchRscp_Span.EditValue = new decimal(new int[] {
            6,
            0,
            0,
            0});
            this.spinEditNoMainCover_PccpchRscp_Span.Location = new System.Drawing.Point(157, 22);
            this.spinEditNoMainCover_PccpchRscp_Span.Name = "spinEditNoMainCover_PccpchRscp_Span";
            this.spinEditNoMainCover_PccpchRscp_Span.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditNoMainCover_PccpchRscp_Span.Properties.Appearance.Options.UseFont = true;
            this.spinEditNoMainCover_PccpchRscp_Span.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditNoMainCover_PccpchRscp_Span.Properties.Mask.EditMask = "f0";
            this.spinEditNoMainCover_PccpchRscp_Span.Size = new System.Drawing.Size(96, 20);
            this.spinEditNoMainCover_PccpchRscp_Span.TabIndex = 0;
            // 
            // groupBox3
            // 
            this.groupBox3.Controls.Add(this.labelControl4);
            this.groupBox3.Controls.Add(this.labelControl5);
            this.groupBox3.Controls.Add(this.labelControl3);
            this.groupBox3.Controls.Add(this.labelControl2);
            this.groupBox3.Controls.Add(this.spinEditLapCover_Ratio);
            this.groupBox3.Controls.Add(this.spinEditLapCover_PccpchRscp);
            this.groupBox3.Location = new System.Drawing.Point(708, 8);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.Size = new System.Drawing.Size(314, 91);
            this.groupBox3.TabIndex = 4;
            this.groupBox3.TabStop = false;
            this.groupBox3.Text = "占用不合理";
            // 
            // labelControl4
            // 
            this.labelControl4.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl4.Appearance.Options.UseFont = true;
            this.labelControl4.Location = new System.Drawing.Point(38, 58);
            this.labelControl4.Name = "labelControl4";
            this.labelControl4.Size = new System.Drawing.Size(120, 12);
            this.labelControl4.TabIndex = 3;
            this.labelControl4.Text = "距离 ≥ 理想覆盖半径";
            // 
            // labelControl5
            // 
            this.labelControl5.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl5.Appearance.Options.UseFont = true;
            this.labelControl5.Location = new System.Drawing.Point(269, 58);
            this.labelControl5.Name = "labelControl5";
            this.labelControl5.Size = new System.Drawing.Size(12, 12);
            this.labelControl5.TabIndex = 2;
            this.labelControl5.Text = "倍";
            // 
            // labelControl3
            // 
            this.labelControl3.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl3.Appearance.Options.UseFont = true;
            this.labelControl3.Location = new System.Drawing.Point(269, 27);
            this.labelControl3.Name = "labelControl3";
            this.labelControl3.Size = new System.Drawing.Size(18, 12);
            this.labelControl3.TabIndex = 2;
            this.labelControl3.Text = "dBm";
            // 
            // labelControl2
            // 
            this.labelControl2.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl2.Appearance.Options.UseFont = true;
            this.labelControl2.Location = new System.Drawing.Point(116, 27);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(42, 12);
            this.labelControl2.TabIndex = 1;
            this.labelControl2.Text = "场强 ≥";
            // 
            // spinEditLapCover_Ratio
            // 
            this.spinEditLapCover_Ratio.EditValue = new decimal(new int[] {
            16,
            0,
            0,
            65536});
            this.spinEditLapCover_Ratio.Location = new System.Drawing.Point(165, 54);
            this.spinEditLapCover_Ratio.Name = "spinEditLapCover_Ratio";
            this.spinEditLapCover_Ratio.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditLapCover_Ratio.Properties.Appearance.Options.UseFont = true;
            this.spinEditLapCover_Ratio.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditLapCover_Ratio.Properties.Mask.EditMask = "f";
            this.spinEditLapCover_Ratio.Size = new System.Drawing.Size(96, 20);
            this.spinEditLapCover_Ratio.TabIndex = 0;
            // 
            // spinEditLapCover_PccpchRscp
            // 
            this.spinEditLapCover_PccpchRscp.EditValue = new decimal(new int[] {
            90,
            0,
            0,
            -2147483648});
            this.spinEditLapCover_PccpchRscp.Location = new System.Drawing.Point(165, 22);
            this.spinEditLapCover_PccpchRscp.Name = "spinEditLapCover_PccpchRscp";
            this.spinEditLapCover_PccpchRscp.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditLapCover_PccpchRscp.Properties.Appearance.Options.UseFont = true;
            this.spinEditLapCover_PccpchRscp.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditLapCover_PccpchRscp.Properties.Mask.EditMask = "f0";
            this.spinEditLapCover_PccpchRscp.Size = new System.Drawing.Size(96, 20);
            this.spinEditLapCover_PccpchRscp.TabIndex = 0;
            // 
            // groupBox4
            // 
            this.groupBox4.Controls.Add(this.labelControl6);
            this.groupBox4.Controls.Add(this.labelControl8);
            this.groupBox4.Controls.Add(this.labelControl9);
            this.groupBox4.Controls.Add(this.labelControl7);
            this.groupBox4.Controls.Add(this.spinEditBackCover_Angle);
            this.groupBox4.Controls.Add(this.spinEditBackCover_Distance);
            this.groupBox4.Location = new System.Drawing.Point(368, 8);
            this.groupBox4.Name = "groupBox4";
            this.groupBox4.Size = new System.Drawing.Size(314, 91);
            this.groupBox4.TabIndex = 3;
            this.groupBox4.TabStop = false;
            this.groupBox4.Text = "背向覆盖";
            // 
            // labelControl6
            // 
            this.labelControl6.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl6.Appearance.Options.UseFont = true;
            this.labelControl6.Location = new System.Drawing.Point(45, 58);
            this.labelControl6.Name = "labelControl6";
            this.labelControl6.Size = new System.Drawing.Size(114, 12);
            this.labelControl6.TabIndex = 3;
            this.labelControl6.Text = "小区与采样点距离 ≥";
            // 
            // labelControl8
            // 
            this.labelControl8.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl8.Appearance.Options.UseFont = true;
            this.labelControl8.Location = new System.Drawing.Point(268, 27);
            this.labelControl8.Name = "labelControl8";
            this.labelControl8.Size = new System.Drawing.Size(12, 12);
            this.labelControl8.TabIndex = 2;
            this.labelControl8.Text = "度";
            // 
            // labelControl9
            // 
            this.labelControl9.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl9.Appearance.Options.UseFont = true;
            this.labelControl9.Location = new System.Drawing.Point(51, 27);
            this.labelControl9.Name = "labelControl9";
            this.labelControl9.Size = new System.Drawing.Size(108, 12);
            this.labelControl9.TabIndex = 1;
            this.labelControl9.Text = "方位角 ≥ 主瓣正负";
            // 
            // labelControl7
            // 
            this.labelControl7.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl7.Appearance.Options.UseFont = true;
            this.labelControl7.Location = new System.Drawing.Point(268, 60);
            this.labelControl7.Name = "labelControl7";
            this.labelControl7.Size = new System.Drawing.Size(12, 12);
            this.labelControl7.TabIndex = 2;
            this.labelControl7.Text = "米";
            // 
            // spinEditBackCover_Angle
            // 
            this.spinEditBackCover_Angle.EditValue = new decimal(new int[] {
            90,
            0,
            0,
            0});
            this.spinEditBackCover_Angle.Location = new System.Drawing.Point(165, 22);
            this.spinEditBackCover_Angle.Name = "spinEditBackCover_Angle";
            this.spinEditBackCover_Angle.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditBackCover_Angle.Properties.Appearance.Options.UseFont = true;
            this.spinEditBackCover_Angle.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditBackCover_Angle.Properties.Mask.EditMask = "f";
            this.spinEditBackCover_Angle.Size = new System.Drawing.Size(96, 20);
            this.spinEditBackCover_Angle.TabIndex = 0;
            // 
            // spinEditBackCover_Distance
            // 
            this.spinEditBackCover_Distance.EditValue = new decimal(new int[] {
            500,
            0,
            0,
            0});
            this.spinEditBackCover_Distance.Location = new System.Drawing.Point(165, 54);
            this.spinEditBackCover_Distance.Name = "spinEditBackCover_Distance";
            this.spinEditBackCover_Distance.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditBackCover_Distance.Properties.Appearance.Options.UseFont = true;
            this.spinEditBackCover_Distance.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditBackCover_Distance.Properties.Mask.EditMask = "f0";
            this.spinEditBackCover_Distance.Size = new System.Drawing.Size(96, 20);
            this.spinEditBackCover_Distance.TabIndex = 0;
            // 
            // xtraTabHO
            // 
            this.xtraTabHO.Controls.Add(this.groupBox9);
            this.xtraTabHO.Controls.Add(this.groupBox12);
            this.xtraTabHO.Controls.Add(this.groupBox11);
            this.xtraTabHO.Name = "xtraTabHO";
            this.xtraTabHO.Size = new System.Drawing.Size(1046, 204);
            this.xtraTabHO.Text = "切换重选类";
            // 
            // groupBox9
            // 
            this.groupBox9.Controls.Add(this.labelControl22);
            this.groupBox9.Controls.Add(this.spinEditReSelectionProb_Span);
            this.groupBox9.Controls.Add(this.labelControl23);
            this.groupBox9.Location = new System.Drawing.Point(557, 12);
            this.groupBox9.Name = "groupBox9";
            this.groupBox9.Size = new System.Drawing.Size(468, 77);
            this.groupBox9.TabIndex = 7;
            this.groupBox9.TabStop = false;
            this.groupBox9.Text = "重选问题";
            // 
            // labelControl22
            // 
            this.labelControl22.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl22.Appearance.Options.UseFont = true;
            this.labelControl22.Location = new System.Drawing.Point(187, 32);
            this.labelControl22.Name = "labelControl22";
            this.labelControl22.Size = new System.Drawing.Size(108, 12);
            this.labelControl22.TabIndex = 8;
            this.labelControl22.Text = "秒内，出现重选问题";
            // 
            // spinEditReSelectionProb_Span
            // 
            this.spinEditReSelectionProb_Span.EditValue = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.spinEditReSelectionProb_Span.Location = new System.Drawing.Point(84, 29);
            this.spinEditReSelectionProb_Span.Name = "spinEditReSelectionProb_Span";
            this.spinEditReSelectionProb_Span.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditReSelectionProb_Span.Properties.Appearance.Options.UseFont = true;
            this.spinEditReSelectionProb_Span.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditReSelectionProb_Span.Properties.Mask.EditMask = "f0";
            this.spinEditReSelectionProb_Span.Size = new System.Drawing.Size(96, 20);
            this.spinEditReSelectionProb_Span.TabIndex = 7;
            // 
            // labelControl23
            // 
            this.labelControl23.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl23.Appearance.Options.UseFont = true;
            this.labelControl23.Location = new System.Drawing.Point(40, 33);
            this.labelControl23.Name = "labelControl23";
            this.labelControl23.Size = new System.Drawing.Size(42, 12);
            this.labelControl23.TabIndex = 6;
            this.labelControl23.Text = "质差前 ";
            // 
            // groupBox12
            // 
            this.groupBox12.Controls.Add(this.labelControl18);
            this.groupBox12.Controls.Add(this.spinEditHOAbnormal_Span);
            this.groupBox12.Controls.Add(this.labelControl38);
            this.groupBox12.Location = new System.Drawing.Point(19, 106);
            this.groupBox12.Name = "groupBox12";
            this.groupBox12.Size = new System.Drawing.Size(516, 81);
            this.groupBox12.TabIndex = 0;
            this.groupBox12.TabStop = false;
            this.groupBox12.Text = "切换不合理";
            // 
            // labelControl18
            // 
            this.labelControl18.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl18.Appearance.Options.UseFont = true;
            this.labelControl18.Location = new System.Drawing.Point(181, 38);
            this.labelControl18.Name = "labelControl18";
            this.labelControl18.Size = new System.Drawing.Size(144, 12);
            this.labelControl18.TabIndex = 5;
            this.labelControl18.Text = "秒内，出现切换不合理事件";
            // 
            // spinEditHOAbnormal_Span
            // 
            this.spinEditHOAbnormal_Span.EditValue = new decimal(new int[] {
            3,
            0,
            0,
            0});
            this.spinEditHOAbnormal_Span.Location = new System.Drawing.Point(78, 35);
            this.spinEditHOAbnormal_Span.Name = "spinEditHOAbnormal_Span";
            this.spinEditHOAbnormal_Span.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditHOAbnormal_Span.Properties.Appearance.Options.UseFont = true;
            this.spinEditHOAbnormal_Span.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditHOAbnormal_Span.Properties.Mask.EditMask = "f0";
            this.spinEditHOAbnormal_Span.Size = new System.Drawing.Size(96, 20);
            this.spinEditHOAbnormal_Span.TabIndex = 4;
            // 
            // labelControl38
            // 
            this.labelControl38.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl38.Appearance.Options.UseFont = true;
            this.labelControl38.Location = new System.Drawing.Point(34, 39);
            this.labelControl38.Name = "labelControl38";
            this.labelControl38.Size = new System.Drawing.Size(42, 12);
            this.labelControl38.TabIndex = 0;
            this.labelControl38.Text = "质差前 ";
            // 
            // groupBox11
            // 
            this.groupBox11.Controls.Add(this.labelControl33);
            this.groupBox11.Location = new System.Drawing.Point(19, 12);
            this.groupBox11.Name = "groupBox11";
            this.groupBox11.Size = new System.Drawing.Size(516, 77);
            this.groupBox11.TabIndex = 0;
            this.groupBox11.TabStop = false;
            this.groupBox11.Text = "切换不及时";
            // 
            // labelControl33
            // 
            this.labelControl33.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl33.Appearance.Options.UseFont = true;
            this.labelControl33.Location = new System.Drawing.Point(34, 31);
            this.labelControl33.Name = "labelControl33";
            this.labelControl33.Size = new System.Drawing.Size(288, 12);
            this.labelControl33.TabIndex = 0;
            this.labelControl33.Text = "出现切换不及时事件（切换不及时时间内出现质差点）";
            // 
            // xtraTabOther
            // 
            this.xtraTabOther.Controls.Add(this.groupBox8);
            this.xtraTabOther.Controls.Add(this.groupBox1);
            this.xtraTabOther.Name = "xtraTabOther";
            this.xtraTabOther.Size = new System.Drawing.Size(1046, 204);
            this.xtraTabOther.Text = "其它类";
            // 
            // groupBox8
            // 
            this.groupBox8.Controls.Add(this.spinEditSpur_Span_Behind);
            this.groupBox8.Controls.Add(this.labelControl17);
            this.groupBox8.Controls.Add(this.spinEditSpur_Span_Ahead);
            this.groupBox8.Controls.Add(this.labelControl19);
            this.groupBox8.Controls.Add(this.labelControl21);
            this.groupBox8.Location = new System.Drawing.Point(276, 16);
            this.groupBox8.Name = "groupBox8";
            this.groupBox8.Size = new System.Drawing.Size(546, 75);
            this.groupBox8.TabIndex = 31;
            this.groupBox8.TabStop = false;
            this.groupBox8.Text = "质量毛刺";
            // 
            // spinEditSpur_Span_Behind
            // 
            this.spinEditSpur_Span_Behind.EditValue = new decimal(new int[] {
            3,
            0,
            0,
            0});
            this.spinEditSpur_Span_Behind.Location = new System.Drawing.Point(231, 32);
            this.spinEditSpur_Span_Behind.Name = "spinEditSpur_Span_Behind";
            this.spinEditSpur_Span_Behind.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditSpur_Span_Behind.Properties.Appearance.Options.UseFont = true;
            this.spinEditSpur_Span_Behind.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditSpur_Span_Behind.Properties.Mask.EditMask = "f0";
            this.spinEditSpur_Span_Behind.Size = new System.Drawing.Size(96, 20);
            this.spinEditSpur_Span_Behind.TabIndex = 8;
            // 
            // labelControl17
            // 
            this.labelControl17.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl17.Appearance.Options.UseFont = true;
            this.labelControl17.Location = new System.Drawing.Point(336, 35);
            this.labelControl17.Name = "labelControl17";
            this.labelControl17.Size = new System.Drawing.Size(138, 12);
            this.labelControl17.TabIndex = 6;
            this.labelControl17.Text = "秒 内出现突然高质差事件";
            // 
            // spinEditSpur_Span_Ahead
            // 
            this.spinEditSpur_Span_Ahead.EditValue = new decimal(new int[] {
            3,
            0,
            0,
            0});
            this.spinEditSpur_Span_Ahead.Location = new System.Drawing.Point(67, 32);
            this.spinEditSpur_Span_Ahead.Name = "spinEditSpur_Span_Ahead";
            this.spinEditSpur_Span_Ahead.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditSpur_Span_Ahead.Properties.Appearance.Options.UseFont = true;
            this.spinEditSpur_Span_Ahead.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditSpur_Span_Ahead.Properties.Mask.EditMask = "f0";
            this.spinEditSpur_Span_Ahead.Size = new System.Drawing.Size(96, 20);
            this.spinEditSpur_Span_Ahead.TabIndex = 9;
            // 
            // labelControl19
            // 
            this.labelControl19.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl19.Appearance.Options.UseFont = true;
            this.labelControl19.Location = new System.Drawing.Point(175, 35);
            this.labelControl19.Name = "labelControl19";
            this.labelControl19.Size = new System.Drawing.Size(42, 12);
            this.labelControl19.TabIndex = 5;
            this.labelControl19.Text = "秒 或后";
            // 
            // labelControl21
            // 
            this.labelControl21.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl21.Appearance.Options.UseFont = true;
            this.labelControl21.Location = new System.Drawing.Point(29, 35);
            this.labelControl21.Name = "labelControl21";
            this.labelControl21.Size = new System.Drawing.Size(24, 12);
            this.labelControl21.TabIndex = 7;
            this.labelControl21.Text = "在前";
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.label1);
            this.groupBox1.Controls.Add(this.spinEditFreqInterference_C2I);
            this.groupBox1.Controls.Add(this.label11);
            this.groupBox1.Location = new System.Drawing.Point(22, 16);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(241, 75);
            this.groupBox1.TabIndex = 30;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "频率干扰或故障";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(182, 38);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(17, 12);
            this.label1.TabIndex = 27;
            this.label1.Text = "dB";
            // 
            // spinEditFreqInterference_C2I
            // 
            this.spinEditFreqInterference_C2I.EditValue = new decimal(new int[] {
            12,
            0,
            0,
            0});
            this.spinEditFreqInterference_C2I.Location = new System.Drawing.Point(80, 33);
            this.spinEditFreqInterference_C2I.Name = "spinEditFreqInterference_C2I";
            this.spinEditFreqInterference_C2I.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditFreqInterference_C2I.Properties.Appearance.Options.UseFont = true;
            this.spinEditFreqInterference_C2I.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditFreqInterference_C2I.Properties.Mask.EditMask = "f0";
            this.spinEditFreqInterference_C2I.Size = new System.Drawing.Size(96, 20);
            this.spinEditFreqInterference_C2I.TabIndex = 26;
            // 
            // label11
            // 
            this.label11.AutoSize = true;
            this.label11.Location = new System.Drawing.Point(35, 38);
            this.label11.Name = "label11";
            this.label11.Size = new System.Drawing.Size(41, 12);
            this.label11.TabIndex = 25;
            this.label11.Text = "C/I ≤";
            // 
            // ZTGSMRxQualAnaSetForm
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1053, 632);
            this.Controls.Add(this.splitContainerControl1);
            this.Name = "ZTGSMRxQualAnaSetForm";
            this.Text = "质差原因条件设置";
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            this.groupBox7.ResumeLayout(false);
            this.groupBox7.PerformLayout();
            this.groupBox16.ResumeLayout(false);
            this.groupBox16.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.checkedListBoxControlReason)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).EndInit();
            this.splitContainerControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).EndInit();
            this.xtraTabControl1.ResumeLayout(false);
            this.xtraTabCover.ResumeLayout(false);
            this.groupBox6.ResumeLayout(false);
            this.groupBox6.PerformLayout();
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditWeakCover_NBCell_PccpchRscp.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditWeakCover_ServCell_PccpchRscp.Properties)).EndInit();
            this.groupBox5.ResumeLayout(false);
            this.groupBox5.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditNoMainCover_CellCount.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditNoMainCover_PccpchRscp_Span.Properties)).EndInit();
            this.groupBox3.ResumeLayout(false);
            this.groupBox3.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditLapCover_Ratio.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditLapCover_PccpchRscp.Properties)).EndInit();
            this.groupBox4.ResumeLayout(false);
            this.groupBox4.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditBackCover_Angle.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditBackCover_Distance.Properties)).EndInit();
            this.xtraTabHO.ResumeLayout(false);
            this.groupBox9.ResumeLayout(false);
            this.groupBox9.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditReSelectionProb_Span.Properties)).EndInit();
            this.groupBox12.ResumeLayout(false);
            this.groupBox12.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditHOAbnormal_Span.Properties)).EndInit();
            this.groupBox11.ResumeLayout(false);
            this.groupBox11.PerformLayout();
            this.xtraTabOther.ResumeLayout(false);
            this.groupBox8.ResumeLayout(false);
            this.groupBox8.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditSpur_Span_Behind.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditSpur_Span_Ahead.Properties)).EndInit();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditFreqInterference_C2I.Properties)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.GroupControl groupControl1;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl1;
        private DevExpress.XtraTab.XtraTabControl xtraTabControl1;
        private DevExpress.XtraTab.XtraTabPage xtraTabCover;
        private DevExpress.XtraTab.XtraTabPage xtraTabOther;
        private DevExpress.XtraTab.XtraTabPage xtraTabHO;
        private System.Windows.Forms.GroupBox groupBox2;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.SpinEdit spinEditWeakCover_ServCell_PccpchRscp;
        private System.Windows.Forms.GroupBox groupBox5;
        private DevExpress.XtraEditors.LabelControl labelControl10;
        private DevExpress.XtraEditors.LabelControl labelControl11;
        private DevExpress.XtraEditors.SpinEdit spinEditNoMainCover_CellCount;
        private DevExpress.XtraEditors.SpinEdit spinEditNoMainCover_PccpchRscp_Span;
        private System.Windows.Forms.GroupBox groupBox3;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private DevExpress.XtraEditors.LabelControl labelControl5;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.SpinEdit spinEditLapCover_Ratio;
        private DevExpress.XtraEditors.SpinEdit spinEditLapCover_PccpchRscp;
        private System.Windows.Forms.GroupBox groupBox4;
        private DevExpress.XtraEditors.LabelControl labelControl6;
        private DevExpress.XtraEditors.LabelControl labelControl7;
        private DevExpress.XtraEditors.LabelControl labelControl8;
        private DevExpress.XtraEditors.LabelControl labelControl9;
        private DevExpress.XtraEditors.SpinEdit spinEditBackCover_Distance;
        private DevExpress.XtraEditors.SpinEdit spinEditBackCover_Angle;
        private DevExpress.XtraEditors.LabelControl labelControl14;
        private DevExpress.XtraEditors.LabelControl labelControl12;
        private System.Windows.Forms.GroupBox groupBox12;
        private System.Windows.Forms.GroupBox groupBox11;
        private DevExpress.XtraEditors.SpinEdit spinEditHOAbnormal_Span;
        private DevExpress.XtraEditors.LabelControl labelControl38;
        private DevExpress.XtraEditors.SimpleButton simpleButtonCancel;
        private DevExpress.XtraEditors.SimpleButton simpleButtonOK;
        private DevExpress.XtraEditors.CheckedListBoxControl checkedListBoxControlReason;
        private DevExpress.XtraEditors.SimpleButton simpleButtonUp;
        private DevExpress.XtraEditors.SimpleButton simpleButtonDown;
        private System.Windows.Forms.TextBox tbxDescription;
        private DevExpress.XtraEditors.LabelControl labelControl33;
        private System.Windows.Forms.GroupBox groupBox16;
        private System.Windows.Forms.GroupBox groupBox7;
        private System.Windows.Forms.TextBox tbxSuggest;
        private DevExpress.XtraEditors.LabelControl labelControl13;
        private DevExpress.XtraEditors.SpinEdit spinEditWeakCover_NBCell_PccpchRscp;
        private DevExpress.XtraEditors.LabelControl labelControl16;
        private DevExpress.XtraEditors.LabelControl labelControl15;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.Label label11;
        private System.Windows.Forms.Label label1;
        private DevExpress.XtraEditors.SpinEdit spinEditFreqInterference_C2I;
        private System.Windows.Forms.GroupBox groupBox6;
        private DevExpress.XtraEditors.LabelControl labelControl20;
        private System.Windows.Forms.GroupBox groupBox8;
        private System.Windows.Forms.GroupBox groupBox9;
        private DevExpress.XtraEditors.SpinEdit spinEditSpur_Span_Behind;
        private DevExpress.XtraEditors.LabelControl labelControl17;
        private DevExpress.XtraEditors.SpinEdit spinEditSpur_Span_Ahead;
        private DevExpress.XtraEditors.LabelControl labelControl19;
        private DevExpress.XtraEditors.LabelControl labelControl21;
        private DevExpress.XtraEditors.LabelControl labelControl18;
        private DevExpress.XtraEditors.LabelControl labelControl22;
        private DevExpress.XtraEditors.SpinEdit spinEditReSelectionProb_Span;
        private DevExpress.XtraEditors.LabelControl labelControl23;

    }
}