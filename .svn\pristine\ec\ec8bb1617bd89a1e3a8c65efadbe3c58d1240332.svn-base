﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTDIYCellAbnormalAngleInfoForm : MinCloseForm
    {
        private MainModel mainModel;
        private List<AbnormalAngleCellInfo> abnormalangleCellInfoList;
        public ZTDIYCellAbnormalAngleInfoForm(MainModel mainModel)
            :base(mainModel)
        {
            this.mainModel = mainModel;
            abnormalangleCellInfoList = new List<AbnormalAngleCellInfo>();
            InitializeComponent();

            this.gridView2.DoubleClick += gridView2_DoubleClick;
        }


        public void FillData(List<AbnormalAngleCellInfo> angleCellInfoList)
        {
            abnormalangleCellInfoList = angleCellInfoList;
            gridControl1.DataSource = abnormalangleCellInfoList;
            if (angleCellInfoList.Count != 0)
            {
                if (angleCellInfoList[0].tdCell != null)
                {
                    colMeanVar.Caption = "平均BLER";
                    colMaxVar.Caption = "最大BLER";
                    colMinVar.Caption = "最小BLER";
                    colVar.Caption = "BLER";
                }
                else if (angleCellInfoList[0].wCell != null)
                {
                    colMeanVar.Visible = false;
                    colMaxVar.Visible = false;
                    colMinVar.Visible = false;
                }
            }
            gridControl1.RefreshDataSource();
        }

        private void gridView1_DoubleClick(object sender, EventArgs e)
        {
            int[] rows = gridView1.GetSelectedRows();
            object obj = gridView1.GetRow(rows[0]);

            if (obj is AbnormalAngleCellInfo)
            {
                AbnormalAngleCellInfo cellInfo = obj as AbnormalAngleCellInfo;
                if (cellInfo.cell != null)
                {
                    mainModel.SelectedCells.Clear();
                    mainModel.SelectedCell = cellInfo.cell;
                    mainModel.MainForm.GetMapForm().GoToView(cellInfo.cell.Longitude, cellInfo.cell.Latitude);
                }
                else if (cellInfo.tdCell != null)
                {
                    mainModel.SelectedTDCells.Clear();
                    mainModel.SelectedTDCell = cellInfo.tdCell;
                    mainModel.MainForm.GetMapForm().GoToView(cellInfo.tdCell.Longitude, cellInfo.tdCell.Latitude);
                }
                else if (cellInfo.wCell != null)
                {
                    mainModel.SelectedWCells.Clear();
                    mainModel.SelectedWCell = cellInfo.wCell;
                    mainModel.MainForm.GetMapForm().GoToView(cellInfo.wCell.Longitude, cellInfo.wCell.Latitude);
                }
                else if (cellInfo.lteCell != null)
                {
                    mainModel.SelectedLTECells.Clear();
                    mainModel.SelectedLTECell = cellInfo.lteCell;
                    mainModel.MainForm.GetMapForm().GoToView(cellInfo.lteCell.Longitude, cellInfo.lteCell.Latitude);
                }
                else if (cellInfo.NRCell != null)
                {
                    mainModel.SelectedNRCells.Clear();
                    mainModel.SetSelectedNRCell(cellInfo.NRCell);
                    mainModel.MainForm.GetMapForm().GoToView(cellInfo.NRCell.Longitude, cellInfo.NRCell.Latitude);
                }
                if (cellInfo.AbnormalAnglePointInfoList != null && cellInfo.AbnormalAnglePointInfoList.Count > 0)
                {
                    MainModel.GetInstance().ClearDTData();
                    foreach (AbnormalAnglePointInfo info in cellInfo.AbnormalAnglePointInfoList)
                    {
                        MainModel.GetInstance().DTDataManager.Add(info.testPoint);
                    }
                    MainModel.GetInstance().DrawFlyLines = true;
                }
            }
            else if (obj is AbnormalAnglePointInfo)
            {
                AbnormalAnglePointInfo pointInfo = obj as AbnormalAnglePointInfo;
                List<AbnormalAnglePointInfo> pointInfoList = new List<AbnormalAnglePointInfo>();
                pointInfoList.Add(pointInfo);
                mainModel.MainForm.GetMapForm().FireCellAbnormalAngleQueried(pointInfoList);
                mainModel.MainForm.GetMapForm().GoToView(pointInfoList[0].testPoint.Longitude, pointInfoList[0].testPoint.Latitude);
            }
        }

        void gridView2_DoubleClick(object sender, EventArgs e)
        {
            DevExpress.XtraGrid.Views.Grid.GridView gv = sender as DevExpress.XtraGrid.Views.Grid.GridView;
            if (gv == null) return;

            int[] rows = gv.GetSelectedRows();
            if (rows.Length <= 0) return;

            object obj = gv.GetRow(rows[0]);
            if (obj is AbnormalAnglePointInfo)
            {
                MainModel.GetInstance().ClearDTData();
                AbnormalAnglePointInfo pointInfo = obj as AbnormalAnglePointInfo;
                MainModel.GetInstance().DTDataManager.Add(pointInfo.testPoint);
                mainModel.MainForm.GetMapForm().GoToView(pointInfo.testPoint.Longitude, pointInfo.testPoint.Latitude);

                MainModel.GetInstance().DrawFlyLines = true;
                MainModel.GetInstance().FireDTDataChanged(this);
            }
        }
        private void ToolStripMenuItemExport_Click(object sender, EventArgs e)
        {
            try
            {
                DataTable table = new DataTable();
                addColumn(table);
                setRowInfo(table);
                MasterCom.Util.ExcelNPOIManager.ExportToExcel(table);
            }
            catch
            {
                //continue
            }
        }

        private void addColumn(DataTable table)
        {
            table.Columns.Add("小区名");
            table.Columns.Add("LAC");
            table.Columns.Add("CI");
            table.Columns.Add("经度");
            table.Columns.Add("纬度");
            table.Columns.Add("场强");
            table.Columns.Add("角度");
            table.Columns.Add("距离");
            if (abnormalangleCellInfoList.Count != 0 && abnormalangleCellInfoList[0].cell != null)
            {
                table.Columns.Add("平均质量");
                table.Columns.Add("最大质量");
                table.Columns.Add("最小质量");
            }
            else if (abnormalangleCellInfoList.Count != 0 && abnormalangleCellInfoList[0].tdCell != null)
            {
                table.Columns.Add("平均BLER");
                table.Columns.Add("最大BLER");
                table.Columns.Add("最小BLER");
            }
            else if (abnormalangleCellInfoList.Count != 0 && abnormalangleCellInfoList[0].lteCell != null)
            {
                table.Columns.Add("平均SINR");
                table.Columns.Add("最大SINR");
                table.Columns.Add("最小SINR");
            }
            table.Columns.Add("异常采样点数量");
            table.Columns.Add("正常采样点数量");
            table.Columns.Add("异常采样点占比");
            table.Columns.Add("文件名");
        }

        private void setRowInfo(DataTable table)
        {
            foreach (AbnormalAngleCellInfo cellInfo in abnormalangleCellInfoList)
            {
                DataRow row = table.NewRow();
                row[0] = cellInfo.CellName;
                row[1] = cellInfo.LAC;
                row[2] = cellInfo.CI;
                row[5] = cellInfo.MeanRxlev;
                row[6] = cellInfo.MeanAngle;
                row[7] = cellInfo.MeanDistance;
                if (cellInfo.cell != null || cellInfo.tdCell != null || cellInfo.lteCell != null)
                {
                    row[8] = cellInfo.MeanVar;
                    row[9] = cellInfo.MaxVar;
                    row[10] = cellInfo.MinVar;
                    row[11] = cellInfo.PointNum;
                    row[12] = cellInfo.NormalPointNum;
                    row[13] = cellInfo.AbnormalRate;
                    row[14] = cellInfo.FileName;
                }
                else
                {
                    row[8] = cellInfo.PointNum;
                    row[9] = cellInfo.NormalPointNum;
                    row[10] = cellInfo.AbnormalRate;
                    row[11] = cellInfo.FileName;
                }
                table.Rows.Add(row);
                foreach (AbnormalAnglePointInfo pointInfo in cellInfo.AbnormalAnglePointInfoList)
                {
                    row = table.NewRow();
                    row[0] = "";
                    row[1] = "";
                    row[2] = "";
                    row[3] = pointInfo.Longitude;
                    row[4] = pointInfo.Latitude;
                    row[5] = pointInfo.Rxlev;
                    row[6] = pointInfo.Angle;
                    row[7] = pointInfo.Distance;
                    row[8] = pointInfo.Var == null ? "" : Math.Round((double)pointInfo.Var, 2).ToString();
                    table.Rows.Add(row);
                }
            }
        }
    }
}
