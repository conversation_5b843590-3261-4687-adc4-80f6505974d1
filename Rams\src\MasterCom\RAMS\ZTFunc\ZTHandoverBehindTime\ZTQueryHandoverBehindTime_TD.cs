﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTQueryHandoverBehindTime_TD : DIYAnalyseFilesOneByOneByRegion
    {
        public ZTQueryHandoverBehindTime_TD(MainModel mm)
            : base(mm)
        {
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.TDSCDMA_VOICE);
            ServiceTypes.Add(ServiceType.TDSCDMA_DATA);
            ServiceTypes.Add(ServiceType.TDSCDMA_HSDPA);
            ServiceTypes.Add(ServiceType.TDSCDMA_HSUPA);
            FilterSampleByRegion = false;
        }

        private ZTHandoverBehindTimeCondition handoverCondition = null;
        protected override bool getCondition()
        {
            HandoverBehindTimeSettingForm conditionDlg = new HandoverBehindTimeSettingForm(handoverCondition);
            if (conditionDlg.ShowDialog()==System.Windows.Forms.DialogResult.OK)
            {
                handoverCondition = conditionDlg.GetCondition();
                return true;
            }
            return false;
        }

        public override string Name
        {
            get
            {
                return "TD切换不及时分析";
            }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 13000, 13035, this.Name);
        }

        List<HandoverBehindTimeInfo> results = null;
        protected override void getReadyBeforeQuery()
        {
            results = new List<HandoverBehindTimeInfo>();
        }

        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager fileDataManager in MainModel.DTDataManager.FileDataManagers)
            {
                HandoverBehindTimeInfo info = null;
                foreach (TestPoint tp in fileDataManager.TestPoints)
                {
                    if (!isValidTestPoint(tp))//非区域内点
                    {
                        saveAndResetOneResult(ref info);
                    }
                    else
                    {
                        info = dealTP(fileDataManager, info, tp);
                    }
                }
            }
        }

        private HandoverBehindTimeInfo dealTP(DTFileDataManager fileDataManager, HandoverBehindTimeInfo info, TestPoint tp)
        {
            float? pccpchValue = (float?)tp["TD_PCCPCH_RSCP"];
            int maxNCellPccpch = int.MinValue;
            for (int i = 0; i < 20; i++)
            {
                int? nCellPccpch = (int?)tp["TD_NCell_PCCPCH_RSCP"];
                if (nCellPccpch != null)
                {
                    maxNCellPccpch = Math.Max((int)nCellPccpch, maxNCellPccpch);
                }
            }
            if (handoverCondition.IsMatchMaxSvrPccpch(pccpchValue)
           && handoverCondition.IsMatchMinNCellPccpch(maxNCellPccpch)
           && handoverCondition.IsMatchMinPccpchDiff((float)(maxNCellPccpch - pccpchValue)))
            {
                if (info == null)
                {
                    info = new HandoverBehindTimeInfo();
                }
                info.AddTestPoint(tp, (float)pccpchValue);
                if (tp.Equals(fileDataManager.TestPoints[fileDataManager.TestPoints.Count - 1]))
                {//文件最后一点，需要把前面的信息保存起来
                    saveAndResetOneResult(ref info);
                }
            }
            else
            {
                saveAndResetOneResult(ref info);
            }

            return info;
        }

        private void saveAndResetOneResult(ref HandoverBehindTimeInfo info)
        {
            if (info == null)
            {
                return;
            }
            if (handoverCondition.IsMatchMinStaySeconds(info.StaySeconds))//持续时间判断
            {
                results.Add(info);
                info.SN = results.Count;
                info.FindRoadName();
            }
            info = null;//重置
        }

        protected override void fireShowForm()
        {
            if (results.Count > 0)
            {
                HandoverBehindTimeListForm_TD frm = null;
                frm = MainModel.GetObjectFromBlackboard(typeof(HandoverBehindTimeListForm_TD).FullName) as HandoverBehindTimeListForm_TD;
                if (frm == null || frm.IsDisposed)
                {
                    frm = new HandoverBehindTimeListForm_TD(MainModel);
                }
                frm.FillData(results);
                if (!frm.Visible)
                {
                    frm.Show(MainModel.MainForm);
                }
                results = new List<HandoverBehindTimeInfo>();
            }
            else
            {
                System.Windows.Forms.MessageBox.Show("在设置的条件下，没有符合的数据。请尝试放宽条件。");
            }
        }


    }
}
