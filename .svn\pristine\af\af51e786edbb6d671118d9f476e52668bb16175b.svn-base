﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

using MasterCom.RAMS.Net;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util.UiEx;
using MasterCom.Util;

using DevExpress.XtraGrid.Columns;

using MasterCom.NOP.WF.Core;

namespace MasterCom.RAMS.NOP
{
    public partial class ReasonStatForm : MinCloseForm
    {
        public ReasonStatForm(object caller)
        {
            InitializeComponent();
            this.dtPickerBegin.Value = DateTime.Now.AddMonths(-1);
            miExportExcel.Click += MiExportExcel_Click;
            btnQuery.Click += BtnQuery_Click;

            this.caller = caller;
            this.Text = (caller as QueryBase).Name;
        }

        private void MiExportExcel_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(gridView1);
        }

        private void BtnQuery_Click(object sender, EventArgs e)
        {
            DateTime sTime = dtPickerBegin.Value.Date;
            DateTime eTime = dtPickerEnd.Value.Date;
            ReasonStater stater = GetStater();

            WaitTextBox.Show("正在查询工单...", DoStatInThread, new object[] { sTime, eTime, stater });

            gridView1.OptionsView.ColumnAutoWidth = !(caller is ShowTaskEventStatForm);
            gridControl1.DataSource = statResult;
            gridControl1.RefreshDataSource();
        }

        private void DoStatInThread(object o)
        {
            try
            {
                object[] args = o as object[];
                DateTime sTime = (DateTime)args[0];
                DateTime eTime = (DateTime)args[1];
                ReasonStater stater = (ReasonStater)args[2];
                statResult = stater.GetResult(sTime, eTime);
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message + Environment.NewLine + ex.StackTrace, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                System.Threading.Thread.Sleep(300);
                WaitTextBox.Close();
            }
        }

        private ReasonStater GetStater()
        {
            if (caller is ShowPredealReasonStatForm)
            {
                return new PredealReasonStater();
            }
            else if (caller is ShowTaskEventStatForm)
            {
                return new TaskEventStater();
            }
            else
            {
                return new ReasonStater();
            }
        }

        private object caller;

        private object statResult;
    }
}
