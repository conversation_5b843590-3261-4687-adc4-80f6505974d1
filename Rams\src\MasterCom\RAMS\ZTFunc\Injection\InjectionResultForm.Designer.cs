﻿namespace MasterCom.RAMS.ZTFunc.Injection
{
    partial class InjectionResultForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.splitContainerControl1 = new DevExpress.XtraEditors.SplitContainerControl();
            this.colorUncovered = new DevExpress.XtraEditors.ColorEdit();
            this.colorCovered = new DevExpress.XtraEditors.ColorEdit();
            this.numMinPersent = new System.Windows.Forms.NumericUpDown();
            this.label3 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.label1 = new System.Windows.Forms.Label();
            this.btnCombineSameStreetName = new System.Windows.Forms.Button();
            this.btnSetKeyStreets = new System.Windows.Forms.Button();
            this.ckbShowNonameRoad = new System.Windows.Forms.CheckBox();
            this.cbxShowOnlyKey = new System.Windows.Forms.CheckBox();
            this.splitContainerControl2 = new DevExpress.XtraEditors.SplitContainerControl();
            this.gridControlStreet = new DevExpress.XtraGrid.GridControl();
            this.contextMenuStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.gridViewStreet = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumnRegionName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnStreetTableName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnStreetName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnCoverPercent = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnDistTotal = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnDistCovered = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnToStandard = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnRepeatTimes = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnTestType = new DevExpress.XtraGrid.Columns.GridColumn();
            this.xtraTabControl1 = new DevExpress.XtraTab.XtraTabControl();
            this.xtraTabPage2 = new DevExpress.XtraTab.XtraTabPage();
            this.gridControlRegion = new DevExpress.XtraGrid.GridControl();
            this.gridViewRegion = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridView();
            this.bandReg = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridColumn1 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn3 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandStreet = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridColumn2 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn3 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn9 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn4 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn8 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn7 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn6 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn5 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandTestMileage = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.colRegGridMileage = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn1 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn10 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn2 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn11 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.xtraTabPage1 = new DevExpress.XtraTab.XtraTabPage();
            this.gridControlTable = new DevExpress.XtraGrid.GridControl();
            this.gridViewTableTotal = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridView();
            this.bandLayer = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridColumnStreetTableName_Table = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn12 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandStreetLayer = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridColumnDistTotal_Table = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumnDistCovered_Table = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumnCoverPercent_Table = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumnDistUnCoveredKM_Table = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumnUnCoveredPercent_Table = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumnDistRepeatedCoveredKM_Table = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumnRepeatePercent_Table = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumnDiverseFactor_Table = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandMileageLayer = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridColumn13 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.ctxMenuRegion = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miRegionExport = new System.Windows.Forms.ToolStripMenuItem();
            this.ctxMenuRoad = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miRoadExport = new System.Windows.Forms.ToolStripMenuItem();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).BeginInit();
            this.splitContainerControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.colorUncovered.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.colorCovered.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMinPersent)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl2)).BeginInit();
            this.splitContainerControl2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlStreet)).BeginInit();
            this.contextMenuStrip.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewStreet)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).BeginInit();
            this.xtraTabControl1.SuspendLayout();
            this.xtraTabPage2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlRegion)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewRegion)).BeginInit();
            this.xtraTabPage1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlTable)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewTableTotal)).BeginInit();
            this.ctxMenuRegion.SuspendLayout();
            this.ctxMenuRoad.SuspendLayout();
            this.SuspendLayout();
            // 
            // splitContainerControl1
            // 
            this.splitContainerControl1.Collapsed = true;
            this.splitContainerControl1.CollapsePanel = DevExpress.XtraEditors.SplitCollapsePanel.Panel1;
            this.splitContainerControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl1.Horizontal = false;
            this.splitContainerControl1.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl1.LookAndFeel.Style = DevExpress.LookAndFeel.LookAndFeelStyle.Office2003;
            this.splitContainerControl1.LookAndFeel.UseDefaultLookAndFeel = false;
            this.splitContainerControl1.Name = "splitContainerControl1";
            this.splitContainerControl1.Panel1.Controls.Add(this.colorUncovered);
            this.splitContainerControl1.Panel1.Controls.Add(this.colorCovered);
            this.splitContainerControl1.Panel1.Controls.Add(this.numMinPersent);
            this.splitContainerControl1.Panel1.Controls.Add(this.label3);
            this.splitContainerControl1.Panel1.Controls.Add(this.label2);
            this.splitContainerControl1.Panel1.Controls.Add(this.label1);
            this.splitContainerControl1.Panel1.Controls.Add(this.btnCombineSameStreetName);
            this.splitContainerControl1.Panel1.Controls.Add(this.btnSetKeyStreets);
            this.splitContainerControl1.Panel1.Controls.Add(this.ckbShowNonameRoad);
            this.splitContainerControl1.Panel1.Controls.Add(this.cbxShowOnlyKey);
            this.splitContainerControl1.Panel1.Text = "Panel1";
            this.splitContainerControl1.Panel2.Controls.Add(this.splitContainerControl2);
            this.splitContainerControl1.Panel2.Text = "Panel2";
            this.splitContainerControl1.Size = new System.Drawing.Size(1092, 451);
            this.splitContainerControl1.SplitterPosition = 39;
            this.splitContainerControl1.TabIndex = 8;
            this.splitContainerControl1.Text = "splitContainerControl1";
            // 
            // colorUncovered
            // 
            this.colorUncovered.EditValue = System.Drawing.Color.Red;
            this.colorUncovered.Location = new System.Drawing.Point(928, 9);
            this.colorUncovered.Name = "colorUncovered";
            this.colorUncovered.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.colorUncovered.Size = new System.Drawing.Size(47, 21);
            this.colorUncovered.TabIndex = 5;
            this.colorUncovered.Visible = false;
            // 
            // colorCovered
            // 
            this.colorCovered.EditValue = System.Drawing.Color.Green;
            this.colorCovered.Location = new System.Drawing.Point(751, 9);
            this.colorCovered.Name = "colorCovered";
            this.colorCovered.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.colorCovered.Size = new System.Drawing.Size(47, 21);
            this.colorCovered.TabIndex = 5;
            this.colorCovered.Visible = false;
            // 
            // numMinPersent
            // 
            this.numMinPersent.Increment = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.numMinPersent.Location = new System.Drawing.Point(450, 8);
            this.numMinPersent.Name = "numMinPersent";
            this.numMinPersent.Size = new System.Drawing.Size(58, 22);
            this.numMinPersent.TabIndex = 3;
            this.numMinPersent.Value = new decimal(new int[] {
            90,
            0,
            0,
            0});
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(819, 12);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(103, 14);
            this.label3.TabIndex = 2;
            this.label3.Text = "无覆盖道路颜色：";
            this.label3.Visible = false;
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(654, 11);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(91, 14);
            this.label2.TabIndex = 2;
            this.label2.Text = "覆盖道路颜色：";
            this.label2.Visible = false;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(368, 12);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(88, 14);
            this.label1.TabIndex = 2;
            this.label1.Text = "覆盖达标值≥：";
            // 
            // btnCombineSameStreetName
            // 
            this.btnCombineSameStreetName.Location = new System.Drawing.Point(12, 9);
            this.btnCombineSameStreetName.Name = "btnCombineSameStreetName";
            this.btnCombineSameStreetName.Size = new System.Drawing.Size(87, 23);
            this.btnCombineSameStreetName.TabIndex = 4;
            this.btnCombineSameStreetName.Text = "合并同名路段";
            this.btnCombineSameStreetName.UseVisualStyleBackColor = true;
            // 
            // btnSetKeyStreets
            // 
            this.btnSetKeyStreets.Location = new System.Drawing.Point(514, 7);
            this.btnSetKeyStreets.Name = "btnSetKeyStreets";
            this.btnSetKeyStreets.Size = new System.Drawing.Size(113, 23);
            this.btnSetKeyStreets.TabIndex = 1;
            this.btnSetKeyStreets.Text = "设置道路达标门限";
            this.btnSetKeyStreets.UseVisualStyleBackColor = true;
            this.btnSetKeyStreets.Visible = false;
            // 
            // ckbShowNonameRoad
            // 
            this.ckbShowNonameRoad.AutoSize = true;
            this.ckbShowNonameRoad.Checked = true;
            this.ckbShowNonameRoad.CheckState = System.Windows.Forms.CheckState.Checked;
            this.ckbShowNonameRoad.Location = new System.Drawing.Point(105, 11);
            this.ckbShowNonameRoad.Name = "ckbShowNonameRoad";
            this.ckbShowNonameRoad.Size = new System.Drawing.Size(110, 18);
            this.ckbShowNonameRoad.TabIndex = 0;
            this.ckbShowNonameRoad.Text = "显示未命名道路";
            this.ckbShowNonameRoad.UseVisualStyleBackColor = true;
            this.ckbShowNonameRoad.Visible = false;
            // 
            // cbxShowOnlyKey
            // 
            this.cbxShowOnlyKey.AutoSize = true;
            this.cbxShowOnlyKey.Location = new System.Drawing.Point(236, 11);
            this.cbxShowOnlyKey.Name = "cbxShowOnlyKey";
            this.cbxShowOnlyKey.Size = new System.Drawing.Size(110, 18);
            this.cbxShowOnlyKey.TabIndex = 0;
            this.cbxShowOnlyKey.Text = "只显示重点道路";
            this.cbxShowOnlyKey.UseVisualStyleBackColor = true;
            // 
            // splitContainerControl2
            // 
            this.splitContainerControl2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl2.FixedPanel = DevExpress.XtraEditors.SplitFixedPanel.Panel2;
            this.splitContainerControl2.Horizontal = false;
            this.splitContainerControl2.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl2.Name = "splitContainerControl2";
            this.splitContainerControl2.Panel1.Controls.Add(this.gridControlStreet);
            this.splitContainerControl2.Panel1.Text = "Panel1";
            this.splitContainerControl2.Panel2.Controls.Add(this.xtraTabControl1);
            this.splitContainerControl2.Panel2.Text = "Panel2";
            this.splitContainerControl2.Size = new System.Drawing.Size(1092, 447);
            this.splitContainerControl2.SplitterPosition = 182;
            this.splitContainerControl2.TabIndex = 6;
            this.splitContainerControl2.Text = "splitContainerControl2";
            // 
            // gridControlStreet
            // 
            this.gridControlStreet.ContextMenuStrip = this.contextMenuStrip;
            this.gridControlStreet.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlStreet.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gridControlStreet.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gridControlStreet.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gridControlStreet.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gridControlStreet.EmbeddedNavigator.Buttons.Remove.Visible = false;
            this.gridControlStreet.Location = new System.Drawing.Point(0, 0);
            this.gridControlStreet.MainView = this.gridViewStreet;
            this.gridControlStreet.Name = "gridControlStreet";
            this.gridControlStreet.Size = new System.Drawing.Size(1092, 259);
            this.gridControlStreet.TabIndex = 2;
            this.gridControlStreet.UseEmbeddedNavigator = true;
            this.gridControlStreet.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridViewStreet});
            // 
            // contextMenuStrip
            // 
            this.contextMenuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExportExcel});
            this.contextMenuStrip.Name = "contextMenuStrip";
            this.contextMenuStrip.Size = new System.Drawing.Size(130, 26);
            // 
            // miExportExcel
            // 
            this.miExportExcel.Name = "miExportExcel";
            this.miExportExcel.Size = new System.Drawing.Size(129, 22);
            this.miExportExcel.Text = "导出Excel";
            this.miExportExcel.Click += new System.EventHandler(this.miExportExcel_Click);
            // 
            // gridViewStreet
            // 
            this.gridViewStreet.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumnRegionName,
            this.gridColumnStreetTableName,
            this.gridColumnStreetName,
            this.gridColumnCoverPercent,
            this.gridColumnDistTotal,
            this.gridColumnDistCovered,
            this.gridColumnToStandard,
            this.gridColumnRepeatTimes,
            this.gridColumnTestType});
            this.gridViewStreet.GridControl = this.gridControlStreet;
            this.gridViewStreet.Name = "gridViewStreet";
            this.gridViewStreet.OptionsBehavior.Editable = false;
            this.gridViewStreet.OptionsView.ShowDetailButtons = false;
            this.gridViewStreet.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumnRegionName
            // 
            this.gridColumnRegionName.Caption = "区域";
            this.gridColumnRegionName.FieldName = "AreaName";
            this.gridColumnRegionName.Name = "gridColumnRegionName";
            this.gridColumnRegionName.Visible = true;
            this.gridColumnRegionName.VisibleIndex = 0;
            this.gridColumnRegionName.Width = 91;
            // 
            // gridColumnStreetTableName
            // 
            this.gridColumnStreetTableName.Caption = "道路图层";
            this.gridColumnStreetTableName.FieldName = "StreetTableName";
            this.gridColumnStreetTableName.Name = "gridColumnStreetTableName";
            this.gridColumnStreetTableName.Visible = true;
            this.gridColumnStreetTableName.VisibleIndex = 1;
            this.gridColumnStreetTableName.Width = 134;
            // 
            // gridColumnStreetName
            // 
            this.gridColumnStreetName.Caption = "道路名称";
            this.gridColumnStreetName.FieldName = "StreetName";
            this.gridColumnStreetName.Name = "gridColumnStreetName";
            this.gridColumnStreetName.Visible = true;
            this.gridColumnStreetName.VisibleIndex = 2;
            this.gridColumnStreetName.Width = 150;
            // 
            // gridColumnCoverPercent
            // 
            this.gridColumnCoverPercent.Caption = "渗透率(%)";
            this.gridColumnCoverPercent.FieldName = "CoverPercentForShow";
            this.gridColumnCoverPercent.Name = "gridColumnCoverPercent";
            this.gridColumnCoverPercent.Visible = true;
            this.gridColumnCoverPercent.VisibleIndex = 3;
            this.gridColumnCoverPercent.Width = 82;
            // 
            // gridColumnDistTotal
            // 
            this.gridColumnDistTotal.Caption = "道路总距离(米)";
            this.gridColumnDistTotal.FieldName = "DistTotalForShow";
            this.gridColumnDistTotal.Name = "gridColumnDistTotal";
            this.gridColumnDistTotal.Visible = true;
            this.gridColumnDistTotal.VisibleIndex = 4;
            this.gridColumnDistTotal.Width = 95;
            // 
            // gridColumnDistCovered
            // 
            this.gridColumnDistCovered.Caption = "渗透距离(米)";
            this.gridColumnDistCovered.FieldName = "DistCovered";
            this.gridColumnDistCovered.Name = "gridColumnDistCovered";
            this.gridColumnDistCovered.Visible = true;
            this.gridColumnDistCovered.VisibleIndex = 5;
            this.gridColumnDistCovered.Width = 84;
            // 
            // gridColumnToStandard
            // 
            this.gridColumnToStandard.Caption = "是否达标";
            this.gridColumnToStandard.FieldName = "ToStandardString";
            this.gridColumnToStandard.Name = "gridColumnToStandard";
            this.gridColumnToStandard.Visible = true;
            this.gridColumnToStandard.VisibleIndex = 6;
            this.gridColumnToStandard.Width = 80;
            // 
            // gridColumnRepeatTimes
            // 
            this.gridColumnRepeatTimes.Caption = "测试频次";
            this.gridColumnRepeatTimes.FieldName = "RepeatTimesForShow";
            this.gridColumnRepeatTimes.Name = "gridColumnRepeatTimes";
            this.gridColumnRepeatTimes.Visible = true;
            this.gridColumnRepeatTimes.VisibleIndex = 7;
            this.gridColumnRepeatTimes.Width = 80;
            // 
            // gridColumnTestType
            // 
            this.gridColumnTestType.Caption = "测试属性";
            this.gridColumnTestType.FieldName = "TestType";
            this.gridColumnTestType.Name = "gridColumnTestType";
            this.gridColumnTestType.Width = 80;
            // 
            // xtraTabControl1
            // 
            this.xtraTabControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.xtraTabControl1.Location = new System.Drawing.Point(0, 0);
            this.xtraTabControl1.Name = "xtraTabControl1";
            this.xtraTabControl1.SelectedTabPage = this.xtraTabPage2;
            this.xtraTabControl1.Size = new System.Drawing.Size(1092, 182);
            this.xtraTabControl1.TabIndex = 0;
            this.xtraTabControl1.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.xtraTabPage2,
            this.xtraTabPage1});
            // 
            // xtraTabPage2
            // 
            this.xtraTabPage2.Controls.Add(this.gridControlRegion);
            this.xtraTabPage2.Name = "xtraTabPage2";
            this.xtraTabPage2.Size = new System.Drawing.Size(1085, 152);
            this.xtraTabPage2.Text = "区域汇总";
            // 
            // gridControlRegion
            // 
            this.gridControlRegion.ContextMenuStrip = this.ctxMenuRegion;
            this.gridControlRegion.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlRegion.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gridControlRegion.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gridControlRegion.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gridControlRegion.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gridControlRegion.EmbeddedNavigator.Buttons.Remove.Visible = false;
            this.gridControlRegion.Location = new System.Drawing.Point(0, 0);
            this.gridControlRegion.MainView = this.gridViewRegion;
            this.gridControlRegion.Name = "gridControlRegion";
            this.gridControlRegion.Size = new System.Drawing.Size(1085, 152);
            this.gridControlRegion.TabIndex = 4;
            this.gridControlRegion.UseEmbeddedNavigator = true;
            this.gridControlRegion.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridViewRegion});
            // 
            // gridViewRegion
            // 
            this.gridViewRegion.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gridViewRegion.Appearance.HeaderPanel.TextOptions.Trimming = DevExpress.Utils.Trimming.None;
            this.gridViewRegion.Appearance.HeaderPanel.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridViewRegion.Bands.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.GridBand[] {
            this.bandReg,
            this.bandStreet,
            this.bandTestMileage});
            this.gridViewRegion.ColumnPanelRowHeight = 50;
            this.gridViewRegion.Columns.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn[] {
            this.gridColumn1,
            this.bandedGridColumn3,
            this.gridColumn2,
            this.gridColumn3,
            this.gridColumn4,
            this.gridColumn5,
            this.gridColumn7,
            this.gridColumn6,
            this.gridColumn9,
            this.gridColumn8,
            this.colRegGridMileage,
            this.bandedGridColumn1,
            this.gridColumn10,
            this.bandedGridColumn2,
            this.gridColumn11});
            this.gridViewRegion.GridControl = this.gridControlRegion;
            this.gridViewRegion.Name = "gridViewRegion";
            this.gridViewRegion.OptionsBehavior.Editable = false;
            this.gridViewRegion.OptionsView.ColumnAutoWidth = false;
            this.gridViewRegion.OptionsView.ShowGroupPanel = false;
            this.gridViewRegion.CustomDrawBandHeader += new DevExpress.XtraGrid.Views.BandedGrid.BandHeaderCustomDrawEventHandler(this.gridViewRegion_CustomDrawBandHeader);
            // 
            // bandReg
            // 
            this.bandReg.Caption = "区域";
            this.bandReg.Columns.Add(this.gridColumn1);
            this.bandReg.Columns.Add(this.bandedGridColumn3);
            this.bandReg.Name = "bandReg";
            this.bandReg.Width = 138;
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "区域";
            this.gridColumn1.FieldName = "RegionName";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.Visible = true;
            this.gridColumn1.Width = 63;
            // 
            // bandedGridColumn3
            // 
            this.bandedGridColumn3.Caption = "面积(km²)";
            this.bandedGridColumn3.FieldName = "Area";
            this.bandedGridColumn3.Name = "bandedGridColumn3";
            this.bandedGridColumn3.Visible = true;
            // 
            // bandStreet
            // 
            this.bandStreet.AppearanceHeader.Options.UseTextOptions = true;
            this.bandStreet.AppearanceHeader.TextOptions.Trimming = DevExpress.Utils.Trimming.None;
            this.bandStreet.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.bandStreet.Caption = "道路渗透";
            this.bandStreet.Columns.Add(this.gridColumn2);
            this.bandStreet.Columns.Add(this.gridColumn3);
            this.bandStreet.Columns.Add(this.gridColumn9);
            this.bandStreet.Columns.Add(this.gridColumn4);
            this.bandStreet.Columns.Add(this.gridColumn8);
            this.bandStreet.Columns.Add(this.gridColumn7);
            this.bandStreet.Columns.Add(this.gridColumn6);
            this.bandStreet.Columns.Add(this.gridColumn5);
            this.bandStreet.Name = "bandStreet";
            this.bandStreet.Width = 538;
            // 
            // gridColumn2
            // 
            this.gridColumn2.Caption = "道路总距离(公里)";
            this.gridColumn2.FieldName = "DistTotalKM";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.Visible = true;
            this.gridColumn2.Width = 72;
            // 
            // gridColumn3
            // 
            this.gridColumn3.Caption = "渗透距离(公里)";
            this.gridColumn3.FieldName = "DistCoveredKM";
            this.gridColumn3.Name = "gridColumn3";
            this.gridColumn3.Visible = true;
            this.gridColumn3.Width = 62;
            // 
            // gridColumn9
            // 
            this.gridColumn9.Caption = "未测试距离(公里)";
            this.gridColumn9.FieldName = "DistUnCoveredKM";
            this.gridColumn9.Name = "gridColumn9";
            this.gridColumn9.Visible = true;
            this.gridColumn9.Width = 70;
            // 
            // gridColumn4
            // 
            this.gridColumn4.Caption = "渗透率(%)";
            this.gridColumn4.FieldName = "CoverPercent";
            this.gridColumn4.Name = "gridColumn4";
            this.gridColumn4.Visible = true;
            this.gridColumn4.Width = 50;
            // 
            // gridColumn8
            // 
            this.gridColumn8.Caption = "未测试率(%)";
            this.gridColumn8.FieldName = "UnCoveredPercent";
            this.gridColumn8.Name = "gridColumn8";
            this.gridColumn8.Visible = true;
            this.gridColumn8.Width = 58;
            // 
            // gridColumn7
            // 
            this.gridColumn7.Caption = "重复测试距离(公里)";
            this.gridColumn7.FieldName = "DistRepeatedCoveredKM";
            this.gridColumn7.Name = "gridColumn7";
            this.gridColumn7.Visible = true;
            this.gridColumn7.Width = 69;
            // 
            // gridColumn6
            // 
            this.gridColumn6.Caption = "重复率(%)";
            this.gridColumn6.FieldName = "RepeatePercent";
            this.gridColumn6.Name = "gridColumn6";
            this.gridColumn6.Visible = true;
            this.gridColumn6.Width = 48;
            // 
            // gridColumn5
            // 
            this.gridColumn5.Caption = "变异系数";
            this.gridColumn5.FieldName = "DiverseFactor";
            this.gridColumn5.Name = "gridColumn5";
            this.gridColumn5.Visible = true;
            this.gridColumn5.Width = 109;
            // 
            // bandTestMileage
            // 
            this.bandTestMileage.Caption = "测试里程";
            this.bandTestMileage.Columns.Add(this.colRegGridMileage);
            this.bandTestMileage.Columns.Add(this.bandedGridColumn1);
            this.bandTestMileage.Columns.Add(this.gridColumn10);
            this.bandTestMileage.Columns.Add(this.bandedGridColumn2);
            this.bandTestMileage.Columns.Add(this.gridColumn11);
            this.bandTestMileage.Name = "bandTestMileage";
            this.bandTestMileage.Width = 469;
            // 
            // colRegGridMileage
            // 
            this.colRegGridMileage.Caption = "栅格总里程（公里）";
            this.colRegGridMileage.FieldName = "TestMileageKM";
            this.colRegGridMileage.Name = "colRegGridMileage";
            this.colRegGridMileage.Visible = true;
            this.colRegGridMileage.Width = 72;
            // 
            // bandedGridColumn1
            // 
            this.bandedGridColumn1.Caption = "渗透在道路上的栅格里程(公里)";
            this.bandedGridColumn1.FieldName = "CoveredMileageKM";
            this.bandedGridColumn1.Name = "bandedGridColumn1";
            this.bandedGridColumn1.Visible = true;
            this.bandedGridColumn1.Width = 70;
            // 
            // gridColumn10
            // 
            this.gridColumn10.AppearanceCell.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(192)))), ((int)(((byte)(255)))), ((int)(((byte)(255)))));
            this.gridColumn10.AppearanceCell.Options.UseBackColor = true;
            this.gridColumn10.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(128)))));
            this.gridColumn10.AppearanceHeader.Options.UseBackColor = true;
            this.gridColumn10.Caption = "未渗透在道路上的栅格里程(公里)";
            this.gridColumn10.FieldName = "NoneMustTestKM";
            this.gridColumn10.Name = "gridColumn10";
            this.gridColumn10.Visible = true;
            this.gridColumn10.Width = 71;
            // 
            // bandedGridColumn2
            // 
            this.bandedGridColumn2.Caption = "里程渗透率(%)";
            this.bandedGridColumn2.FieldName = "MileageCoveredRate";
            this.bandedGridColumn2.Name = "bandedGridColumn2";
            this.bandedGridColumn2.Visible = true;
            // 
            // gridColumn11
            // 
            this.gridColumn11.AppearanceCell.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(192)))), ((int)(((byte)(255)))), ((int)(((byte)(255)))));
            this.gridColumn11.AppearanceCell.Options.UseBackColor = true;
            this.gridColumn11.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(128)))));
            this.gridColumn11.AppearanceHeader.Options.UseBackColor = true;
            this.gridColumn11.Caption = "(道路渗透距离+非渗透里程)/(道路总距离+非渗透里程)(%)";
            this.gridColumn11.FieldName = "TestPercent";
            this.gridColumn11.Name = "gridColumn11";
            this.gridColumn11.Visible = true;
            this.gridColumn11.Width = 181;
            // 
            // xtraTabPage1
            // 
            this.xtraTabPage1.Controls.Add(this.gridControlTable);
            this.xtraTabPage1.Name = "xtraTabPage1";
            this.xtraTabPage1.Size = new System.Drawing.Size(1085, 152);
            this.xtraTabPage1.Text = "道路汇总";
            // 
            // gridControlTable
            // 
            this.gridControlTable.ContextMenuStrip = this.ctxMenuRoad;
            this.gridControlTable.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlTable.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gridControlTable.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gridControlTable.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gridControlTable.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gridControlTable.EmbeddedNavigator.Buttons.Remove.Visible = false;
            this.gridControlTable.Location = new System.Drawing.Point(0, 0);
            this.gridControlTable.MainView = this.gridViewTableTotal;
            this.gridControlTable.Name = "gridControlTable";
            this.gridControlTable.Size = new System.Drawing.Size(1085, 152);
            this.gridControlTable.TabIndex = 3;
            this.gridControlTable.UseEmbeddedNavigator = true;
            this.gridControlTable.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridViewTableTotal});
            // 
            // gridViewTableTotal
            // 
            this.gridViewTableTotal.Bands.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.GridBand[] {
            this.bandLayer,
            this.bandStreetLayer,
            this.bandMileageLayer});
            this.gridViewTableTotal.Columns.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn[] {
            this.gridColumnStreetTableName_Table,
            this.gridColumnDistTotal_Table,
            this.gridColumnDistCovered_Table,
            this.gridColumnCoverPercent_Table,
            this.gridColumnDiverseFactor_Table,
            this.gridColumnRepeatePercent_Table,
            this.gridColumnDistRepeatedCoveredKM_Table,
            this.gridColumnUnCoveredPercent_Table,
            this.gridColumnDistUnCoveredKM_Table,
            this.gridColumn12,
            this.gridColumn13});
            this.gridViewTableTotal.GridControl = this.gridControlTable;
            this.gridViewTableTotal.Name = "gridViewTableTotal";
            this.gridViewTableTotal.OptionsBehavior.Editable = false;
            this.gridViewTableTotal.OptionsView.ColumnAutoWidth = false;
            this.gridViewTableTotal.OptionsView.ShowDetailButtons = false;
            this.gridViewTableTotal.OptionsView.ShowGroupPanel = false;
            this.gridViewTableTotal.CustomDrawBandHeader += new DevExpress.XtraGrid.Views.BandedGrid.BandHeaderCustomDrawEventHandler(this.gridViewRegion_CustomDrawBandHeader);
            // 
            // bandLayer
            // 
            this.bandLayer.Caption = "图层";
            this.bandLayer.Columns.Add(this.gridColumnStreetTableName_Table);
            this.bandLayer.Columns.Add(this.gridColumn12);
            this.bandLayer.Name = "bandLayer";
            this.bandLayer.Width = 75;
            // 
            // gridColumnStreetTableName_Table
            // 
            this.gridColumnStreetTableName_Table.Caption = "道路图层";
            this.gridColumnStreetTableName_Table.FieldName = "StreetTableName";
            this.gridColumnStreetTableName_Table.Name = "gridColumnStreetTableName_Table";
            this.gridColumnStreetTableName_Table.Visible = true;
            // 
            // gridColumn12
            // 
            this.gridColumn12.Caption = "非必测试里程(公里)";
            this.gridColumn12.FieldName = "NoneMustTestKM";
            this.gridColumn12.Name = "gridColumn12";
            // 
            // bandStreetLayer
            // 
            this.bandStreetLayer.Caption = "道路渗透";
            this.bandStreetLayer.Columns.Add(this.gridColumnDistTotal_Table);
            this.bandStreetLayer.Columns.Add(this.gridColumnDistCovered_Table);
            this.bandStreetLayer.Columns.Add(this.gridColumnCoverPercent_Table);
            this.bandStreetLayer.Columns.Add(this.gridColumnDistUnCoveredKM_Table);
            this.bandStreetLayer.Columns.Add(this.gridColumnUnCoveredPercent_Table);
            this.bandStreetLayer.Columns.Add(this.gridColumnDistRepeatedCoveredKM_Table);
            this.bandStreetLayer.Columns.Add(this.gridColumnRepeatePercent_Table);
            this.bandStreetLayer.Columns.Add(this.gridColumnDiverseFactor_Table);
            this.bandStreetLayer.Name = "bandStreetLayer";
            this.bandStreetLayer.Width = 673;
            // 
            // gridColumnDistTotal_Table
            // 
            this.gridColumnDistTotal_Table.Caption = "道路总公里数";
            this.gridColumnDistTotal_Table.FieldName = "DistTotalKM";
            this.gridColumnDistTotal_Table.Name = "gridColumnDistTotal_Table";
            this.gridColumnDistTotal_Table.Visible = true;
            this.gridColumnDistTotal_Table.Width = 86;
            // 
            // gridColumnDistCovered_Table
            // 
            this.gridColumnDistCovered_Table.Caption = "渗透公里数";
            this.gridColumnDistCovered_Table.FieldName = "DistCoveredKM";
            this.gridColumnDistCovered_Table.Name = "gridColumnDistCovered_Table";
            this.gridColumnDistCovered_Table.Visible = true;
            this.gridColumnDistCovered_Table.Width = 79;
            // 
            // gridColumnCoverPercent_Table
            // 
            this.gridColumnCoverPercent_Table.Caption = "渗透率(%)";
            this.gridColumnCoverPercent_Table.FieldName = "CoverPercent";
            this.gridColumnCoverPercent_Table.Name = "gridColumnCoverPercent_Table";
            this.gridColumnCoverPercent_Table.Visible = true;
            this.gridColumnCoverPercent_Table.Width = 78;
            // 
            // gridColumnDistUnCoveredKM_Table
            // 
            this.gridColumnDistUnCoveredKM_Table.Caption = "未测试公里数";
            this.gridColumnDistUnCoveredKM_Table.FieldName = "DistUnCoveredKM";
            this.gridColumnDistUnCoveredKM_Table.Name = "gridColumnDistUnCoveredKM_Table";
            this.gridColumnDistUnCoveredKM_Table.Visible = true;
            this.gridColumnDistUnCoveredKM_Table.Width = 82;
            // 
            // gridColumnUnCoveredPercent_Table
            // 
            this.gridColumnUnCoveredPercent_Table.Caption = "未测试率(%)";
            this.gridColumnUnCoveredPercent_Table.FieldName = "UnCoveredPercent";
            this.gridColumnUnCoveredPercent_Table.Name = "gridColumnUnCoveredPercent_Table";
            this.gridColumnUnCoveredPercent_Table.Visible = true;
            this.gridColumnUnCoveredPercent_Table.Width = 91;
            // 
            // gridColumnDistRepeatedCoveredKM_Table
            // 
            this.gridColumnDistRepeatedCoveredKM_Table.Caption = "重复测试公里数";
            this.gridColumnDistRepeatedCoveredKM_Table.FieldName = "DistRepeatedCoveredKM";
            this.gridColumnDistRepeatedCoveredKM_Table.Name = "gridColumnDistRepeatedCoveredKM_Table";
            this.gridColumnDistRepeatedCoveredKM_Table.Visible = true;
            this.gridColumnDistRepeatedCoveredKM_Table.Width = 104;
            // 
            // gridColumnRepeatePercent_Table
            // 
            this.gridColumnRepeatePercent_Table.Caption = "重复率(%)";
            this.gridColumnRepeatePercent_Table.FieldName = "RepeatePercent";
            this.gridColumnRepeatePercent_Table.Name = "gridColumnRepeatePercent_Table";
            this.gridColumnRepeatePercent_Table.Visible = true;
            this.gridColumnRepeatePercent_Table.Width = 78;
            // 
            // gridColumnDiverseFactor_Table
            // 
            this.gridColumnDiverseFactor_Table.Caption = "变异系数";
            this.gridColumnDiverseFactor_Table.FieldName = "DiverseFactor";
            this.gridColumnDiverseFactor_Table.Name = "gridColumnDiverseFactor_Table";
            this.gridColumnDiverseFactor_Table.Visible = true;
            // 
            // bandMileageLayer
            // 
            this.bandMileageLayer.Caption = "栅格里程";
            this.bandMileageLayer.Columns.Add(this.gridColumn13);
            this.bandMileageLayer.Name = "bandMileageLayer";
            this.bandMileageLayer.Width = 295;
            // 
            // gridColumn13
            // 
            this.gridColumn13.Caption = "道路测试率(总测试里程/总道路公里数)(%)";
            this.gridColumn13.FieldName = "TestPercent";
            this.gridColumn13.Name = "gridColumn13";
            this.gridColumn13.Visible = true;
            this.gridColumn13.Width = 295;
            // 
            // ctxMenuRegion
            // 
            this.ctxMenuRegion.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miRegionExport});
            this.ctxMenuRegion.Name = "contextMenuStrip";
            this.ctxMenuRegion.Size = new System.Drawing.Size(130, 26);
            // 
            // miRegionExport
            // 
            this.miRegionExport.Name = "miRegionExport";
            this.miRegionExport.Size = new System.Drawing.Size(129, 22);
            this.miRegionExport.Text = "导出Excel";
            this.miRegionExport.Click += new System.EventHandler(this.miRegionExport_Click);
            // 
            // ctxMenuRoad
            // 
            this.ctxMenuRoad.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miRoadExport});
            this.ctxMenuRoad.Name = "contextMenuStrip";
            this.ctxMenuRoad.Size = new System.Drawing.Size(130, 26);
            // 
            // miRoadExport
            // 
            this.miRoadExport.Name = "miRoadExport";
            this.miRoadExport.Size = new System.Drawing.Size(152, 22);
            this.miRoadExport.Text = "导出Excel";
            this.miRoadExport.Click += new System.EventHandler(this.miRoadExport_Click);
            // 
            // InjectionResultForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1092, 451);
            this.Controls.Add(this.splitContainerControl1);
            this.Name = "InjectionResultForm";
            this.Text = "渗透率";
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).EndInit();
            this.splitContainerControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.colorUncovered.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.colorCovered.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMinPersent)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl2)).EndInit();
            this.splitContainerControl2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlStreet)).EndInit();
            this.contextMenuStrip.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridViewStreet)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).EndInit();
            this.xtraTabControl1.ResumeLayout(false);
            this.xtraTabPage2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlRegion)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewRegion)).EndInit();
            this.xtraTabPage1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlTable)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewTableTotal)).EndInit();
            this.ctxMenuRegion.ResumeLayout(false);
            this.ctxMenuRoad.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl1;
        private DevExpress.XtraEditors.ColorEdit colorUncovered;
        private DevExpress.XtraEditors.ColorEdit colorCovered;
        private System.Windows.Forms.NumericUpDown numMinPersent;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Button btnCombineSameStreetName;
        private System.Windows.Forms.Button btnSetKeyStreets;
        private System.Windows.Forms.CheckBox ckbShowNonameRoad;
        private System.Windows.Forms.CheckBox cbxShowOnlyKey;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl2;
        private DevExpress.XtraGrid.GridControl gridControlStreet;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewStreet;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnRegionName;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnStreetTableName;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnStreetName;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnCoverPercent;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnDistTotal;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnDistCovered;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnToStandard;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnRepeatTimes;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnTestType;
        private DevExpress.XtraTab.XtraTabControl xtraTabControl1;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage2;
        private DevExpress.XtraGrid.GridControl gridControlRegion;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridView gridViewRegion;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand bandReg;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn1;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn3;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand bandStreet;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn2;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn3;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn9;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn4;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn8;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn7;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn6;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn5;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand bandTestMileage;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn colRegGridMileage;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn1;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn10;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn2;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn11;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage1;
        private DevExpress.XtraGrid.GridControl gridControlTable;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridView gridViewTableTotal;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand bandLayer;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumnStreetTableName_Table;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn12;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand bandStreetLayer;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumnDistTotal_Table;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumnDistCovered_Table;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumnCoverPercent_Table;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumnDistUnCoveredKM_Table;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumnUnCoveredPercent_Table;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumnDistRepeatedCoveredKM_Table;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumnRepeatePercent_Table;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumnDiverseFactor_Table;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand bandMileageLayer;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn13;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip;
        private System.Windows.Forms.ToolStripMenuItem miExportExcel;
        private System.Windows.Forms.ContextMenuStrip ctxMenuRegion;
        private System.Windows.Forms.ToolStripMenuItem miRegionExport;
        private System.Windows.Forms.ContextMenuStrip ctxMenuRoad;
        private System.Windows.Forms.ToolStripMenuItem miRoadExport;

    }
}