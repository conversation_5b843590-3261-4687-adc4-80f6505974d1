﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTCellSetBriefSetForm : BaseDialog
    {
        public ZTCellSetBriefSetForm()
        {
            InitializeComponent();
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            if (!radBtnMaiCell.Checked && !radBtnNearCell.Checked)
            {
                MessageBox.Show("请至少选择一种查询类型！");
                return;
            }
            this.DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        public void getSelect(ref bool isMainCell, ref bool isNearCell, ref bool isContainNotGrid)
        {
            isMainCell = radBtnMaiCell.Checked;
            isNearCell = radBtnNearCell.Checked;
            isContainNotGrid = cbContainNotGrid.Checked;
        }
    }
}
