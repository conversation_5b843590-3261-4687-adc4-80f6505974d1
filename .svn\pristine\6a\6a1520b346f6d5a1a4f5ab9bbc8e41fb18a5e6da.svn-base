﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class LogDownloadSettingForm : BaseDialog
    {
        public LogDownloadSettingForm()
        {
            InitializeComponent();
            btnOK.Click += BtnOK_Click;
            btnCancel.Click += BtnCancel_Click;
        }

        public TimePeriod GetCondition()
        {
            return new TimePeriod(pickerStart.Value.Date, pickerEnd.Value.Date.AddDays(1).AddSeconds(-1));
        }

        private void BtnOK_Click(object sender, EventArgs e)
        {
            if (pickerStart.Value.Date > pickerEnd.Value.Date)
            {
                MessageBox.Show("开始时间不能大于结束事件", "设置错误", MessageBoxButtons.OK, MessageBoxIcon.Information);
                DialogResult = DialogResult.None;
                return;
            }
            DialogResult = DialogResult.OK;
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
        }
    }
}
