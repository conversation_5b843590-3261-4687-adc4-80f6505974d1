﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Xml;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class LastRoadReportManager
    {
        private static LastRoadReportManager instance = null;
        private LastRoadReportManager()
        {
            cfgFileName = System.Windows.Forms.Application.StartupPath + "/config/LastRoad.xml";
            LoadCfg();
        }
        public static LastRoadReportManager GetInstance()
        {
            if (instance==null)
            {
                instance = new LastRoadReportManager();
            }
            return instance;
        }
        
        public List<LastRoadReport> Reports { get; set; } = new List<LastRoadReport>();
        private List<object> cfgParam
        {
            get
            {
                List<object> rpts = new List<object>();
                foreach (LastRoadReport rpt in Reports)
                {
                    rpts.Add(rpt.CfgParam);
                }
                return rpts;
            }
            set
            {
                if (value == null)
                {
                    return;
                }
                Reports.Clear();
                foreach (object obj in value)
                {
                    LastRoadReport rpt = new LastRoadReport();
                    rpt.CfgParam = obj as Dictionary<string, object>;
                    Reports.Add(rpt);
                }
            }
        }

        private readonly string cfgFileName;
        public void LoadCfg()
        {
            if (File.Exists(cfgFileName))
            {
                XmlConfigFile configFile = new XmlConfigFile(cfgFileName);
                cfgParam = configFile.GetItemValue("LastRoadCfg", "Reports") as List<object>;
            }
        }
        public void Save()
        {
            MasterCom.Util.XmlConfigFile xmlFile = new MasterCom.Util.XmlConfigFile();
            XmlElement cfgE = xmlFile.AddConfig("LastRoadCfg");
            xmlFile.AddItem(cfgE, "Reports", this.cfgParam);
            xmlFile.Save(cfgFileName);
        }
    }
}
