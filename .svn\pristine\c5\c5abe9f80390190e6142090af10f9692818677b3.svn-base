﻿namespace MasterCom.RAMS.Frame
{
    partial class MainForm
    {
        public bool IsDisposing
        {
            get { return disposing; }
        }

        /// <summary>
        /// Required designer variable
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            this.disposing = true;
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.Windows.Forms.ColumnHeader columnHeader1;
            System.Windows.Forms.ColumnHeader columnHeader2;
            System.Windows.Forms.ColumnHeader columnHeader3;
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(MainForm));
            DevExpress.Utils.SuperToolTip superToolTip4 = new DevExpress.Utils.SuperToolTip();
            DevExpress.Utils.ToolTipTitleItem toolTipTitleItem4 = new DevExpress.Utils.ToolTipTitleItem();
            DevExpress.Utils.ToolTipItem toolTipItem4 = new DevExpress.Utils.ToolTipItem();
            DevExpress.Utils.SuperToolTip superToolTip5 = new DevExpress.Utils.SuperToolTip();
            DevExpress.Utils.ToolTipTitleItem toolTipTitleItem5 = new DevExpress.Utils.ToolTipTitleItem();
            DevExpress.Utils.ToolTipItem toolTipItem5 = new DevExpress.Utils.ToolTipItem();
            DevExpress.Utils.SuperToolTip superToolTip6 = new DevExpress.Utils.SuperToolTip();
            DevExpress.Utils.ToolTipTitleItem toolTipTitleItem6 = new DevExpress.Utils.ToolTipTitleItem();
            DevExpress.Utils.ToolTipItem toolTipItem6 = new DevExpress.Utils.ToolTipItem();
            DevExpress.XtraGrid.GridLevelNode gridLevelNode2 = new DevExpress.XtraGrid.GridLevelNode();
            this.ribbon = new DevExpress.XtraBars.Ribbon.RibbonControl();
            this.applicationMenu1 = new DevExpress.XtraBars.Ribbon.ApplicationMenu(this.components);
            this.barButtonItemExport2Tab = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItemSysSetting = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItemUpdateInterface = new DevExpress.XtraBars.BarButtonItem();
            this.barSubItemPermissionSetting = new DevExpress.XtraBars.BarSubItem();
            this.barBtnUserFuncPermission = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnUserDataSrcPermission = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnUserCityRight = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnShowUserForm = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItemExit = new DevExpress.XtraBars.BarButtonItem();
            this.barAndDockingController = new DevExpress.XtraBars.BarAndDockingController(this.components);
            this.barButtonItemOpenWorkSheet = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItemNew = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItemSave = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItemNavigator = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItemPreviousData = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItemNextData = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItemNewWorkSheet = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItemRenameWorkSheet = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItemRemoveWorkSheet = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItemPreviousWorkSheet = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItemNextWorkSheet = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItemCascade = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItemTileVertical = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItemTileHorizontal = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItemCloseAll = new DevExpress.XtraBars.BarButtonItem();
            this.barEditItemWKSList = new DevExpress.XtraBars.BarEditItem();
            this.repositoryItemComboBox1 = new DevExpress.XtraEditors.Repository.RepositoryItemComboBox();
            this.barButtonItemPlayLog = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItemStopPlay = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItemSlower = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItemFaster = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItemFullScreen = new DevExpress.XtraBars.BarButtonItem();
            this.barSubItemMS = new DevExpress.XtraBars.BarSubItem();
            this.barButtonItemMiMS_All = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItemMiMS_1 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItemMiMS_2 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItemMiMS_3 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItemMiMS_4 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItemMiMS_5 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItemMiMS_6 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItemMiMS_7 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItemMiMS_8 = new DevExpress.XtraBars.BarButtonItem();
            this.barSubItemOpen = new DevExpress.XtraBars.BarSubItem();
            this.barButtonItemTopFrontMi = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItemMiAllTopFront = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItemOpacitySetMi = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItemMiAllOpacitySet = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItemArrangeIcons = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItemLoadCellConfig_tsmi = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItemQueryParam = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItemPlanningInfoImport = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItemOwnDataUpload = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItemContents = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItemViewHistory = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItemAbout = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItemSystemSetting = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItemLink2SZMap = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItemSaveWorkSheet = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItemSaveAs = new DevExpress.XtraBars.BarButtonItem();
            this.barSubItemOpenWorkSheet = new DevExpress.XtraBars.BarSubItem();
            this.barButtonItemFunctionNav = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItemNewWS = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItemFullScr = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItemWKSManage = new DevExpress.XtraBars.BarButtonItem();
            this.barStaticItemCellInfo = new DevExpress.XtraBars.BarStaticItem();
            this.barButtonItemExportKML = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItemShowMap = new DevExpress.XtraBars.BarButtonItem();
            this.barSubItemCutScreen = new DevExpress.XtraBars.BarSubItem();
            this.barButtonItemCutScreen = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItemHDCut = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItemHDCutAndPrint = new DevExpress.XtraBars.BarButtonItem();
            this.barSubItemChooseCity = new DevExpress.XtraBars.BarButtonItem();
            this.barChkItemDEBUG = new DevExpress.XtraBars.BarCheckItem();
            this.barBtnCellCfgHistorySet = new DevExpress.XtraBars.BarButtonItem();
            this.barStaticUpdate = new DevExpress.XtraBars.BarStaticItem();
            this.lblCurServerIp = new DevExpress.XtraBars.BarStaticItem();
            this.barBtnOpenProgramLocation = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnProgramRestart = new DevExpress.XtraBars.BarButtonItem();
            this.ribbonPageStart = new DevExpress.XtraBars.Ribbon.RibbonPage();
            this.ribbonPageGroupFile = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageGroupChange = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageGroupWorkSheet = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageGroupPlay = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageGroupScreen = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageGroupWindowStyle = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageGroupMobile = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageGroupChooseCity = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageGroupMap = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageGroupProgram = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageHelp = new DevExpress.XtraBars.Ribbon.RibbonPage();
            this.ribbonPageGroupOpen = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageGroupCheck = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageGroupAbout = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonStatusBar = new DevExpress.XtraBars.Ribbon.RibbonStatusBar();
            this.imageListMenuItem = new System.Windows.Forms.ImageList(this.components);
            this.contextMenuStripTreeView = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.minAddChildNode = new System.Windows.Forms.ToolStripMenuItem();
            this.minEditCurrentNode = new System.Windows.Forms.ToolStripMenuItem();
            this.minDelCurrentNode = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator17 = new System.Windows.Forms.ToolStripSeparator();
            this.minAddRootNode = new System.Windows.Forms.ToolStripMenuItem();
            this.imageListVisibleParam = new System.Windows.Forms.ImageList(this.components);
            this.imageListWorkSpace = new System.Windows.Forms.ImageList(this.components);
            this.LeftToolStripPanel = new System.Windows.Forms.ToolStripPanel();
            this.RightToolStripPanel = new System.Windows.Forms.ToolStripPanel();
            this.contextMenuStripTabControlWorkSpace = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.toolStripMenuItemNewWorkSpace1 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItemRemoveWorkSpace1 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItemRenameWorkSpace1 = new System.Windows.Forms.ToolStripMenuItem();
            this.miSaveAsWSTemplate = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator3 = new System.Windows.Forms.ToolStripSeparator();
            this.toolStripMenuItemPreviousWorkSpace = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItemNextWorkSpace = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItemCloseAll = new System.Windows.Forms.ToolStripMenuItem();
            this.contextMenuStripVisibleParam = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.ToolStripMenuItemModifyVisibleParam = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItemCloseWindow = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItemRenameWorkSpace = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItemNewWorkSpace = new System.Windows.Forms.ToolStripMenuItem();
            this.contextMenuStripMenuItem = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.ToolStripMenuItemOpenWindow = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItemRemoveWorkSpace = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripDropDownLocal = new System.Windows.Forms.ToolStripDropDown();
            this.ctxQuickRestoreWindows = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.ToolStripMenuItemActive = new System.Windows.Forms.ToolStripMenuItem();
            this.contextMenuStripTreeViewWorkSpace = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.toolStripSeparator1 = new System.Windows.Forms.ToolStripSeparator();
            this.toolStripSeparator2 = new System.Windows.Forms.ToolStripSeparator();
            this.logTimer = new System.Windows.Forms.Timer(this.components);
            this.bgWorkerLog = new System.ComponentModel.BackgroundWorker();
            this.playTimer = new System.Windows.Forms.Timer(this.components);
            this.imageListWnd = new System.Windows.Forms.ImageList(this.components);
            this.defaultLookAndFeel1 = new DevExpress.LookAndFeel.DefaultLookAndFeel(this.components);
            this.imageListVisibleEvent = new System.Windows.Forms.ImageList(this.components);
            this.panelControlWorkSheet = new DevExpress.XtraEditors.PanelControl();
            this.btnAddNewTemplateWS = new DevExpress.XtraEditors.SimpleButton();
            this.btnQuickRestoreWindow = new DevExpress.XtraEditors.SimpleButton();
            this.xtraTabControlWorkSheet = new DevExpress.XtraTab.XtraTabControl();
            this.bgWorkerFire = new System.ComponentModel.BackgroundWorker();
            this.trackBarReplay = new DevExpress.XtraEditors.TrackBarControl();
            this.toolTip = new DevExpress.Utils.ToolTipController(this.components);
            this.lbSvCount = new System.Windows.Forms.Label();
            this.lbProjCount = new System.Windows.Forms.Label();
            this.lblCityCount = new System.Windows.Forms.Label();
            this.labelDeviceCount = new System.Windows.Forms.Label();
            this.textBoxFileName = new System.Windows.Forms.TextBox();
            this.dockManager = new DevExpress.XtraBars.Docking.DockManager(this.components);
            this.hideContainerLeft = new DevExpress.XtraBars.Docking.AutoHideContainer();
            this.dockPanelNavi = new DevExpress.XtraBars.Docking.DockPanel();
            this.ctrlContainerNavi = new DevExpress.XtraBars.Docking.ControlContainer();
            this.xtraTabControlNavigator = new DevExpress.XtraTab.XtraTabControl();
            this.tabPageSpace = new DevExpress.XtraTab.XtraTabPage();
            this.xtraTabControlWks = new DevExpress.XtraTab.XtraTabControl();
            this.xtraTabPageWksNavi = new DevExpress.XtraTab.XtraTabPage();
            this.treeViewWorkSpace = new System.Windows.Forms.TreeView();
            this.xtraTabPageWksTemplates = new DevExpress.XtraTab.XtraTabPage();
            this.treeViewTemplateWS = new System.Windows.Forms.TreeView();
            this.tabPageMenu = new DevExpress.XtraTab.XtraTabPage();
            this.treeViewMenuItem = new System.Windows.Forms.TreeView();
            this.tabPageParam = new DevExpress.XtraTab.XtraTabPage();
            this.xtraTabControlChildVisibleParam = new DevExpress.XtraTab.XtraTabControl();
            this.xtraTabPageSignalParam = new DevExpress.XtraTab.XtraTabPage();
            this.treeViewVisibleParam = new System.Windows.Forms.TreeView();
            this.pnlParam = new System.Windows.Forms.Panel();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.btnVisibleParamNext = new DevExpress.XtraEditors.SimpleButton();
            this.txtVisibleParamSearch = new DevExpress.XtraEditors.TextEdit();
            this.xtraTabPageEventParam = new DevExpress.XtraTab.XtraTabPage();
            this.treeViewVisibleEvent = new System.Windows.Forms.TreeView();
            this.pnlEventParam = new System.Windows.Forms.Panel();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.btnSearchEvent = new DevExpress.XtraEditors.SimpleButton();
            this.textEditSearchEvent = new DevExpress.XtraEditors.TextEdit();
            this.pnlCondAndLegend = new DevExpress.XtraBars.Docking.DockPanel();
            this.dockPanelQueryCond = new DevExpress.XtraBars.Docking.DockPanel();
            this.dockPanel1_Container = new DevExpress.XtraBars.Docking.ControlContainer();
            this.scrollCtrlCondition = new DevExpress.XtraEditors.XtraScrollableControl();
            this.grpCtrlCarrier = new DevExpress.XtraEditors.GroupControl();
            this.listViewCarrier = new System.Windows.Forms.ListView();
            this.columnHeader4 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.grpCtrlDaiwei = new DevExpress.XtraEditors.GroupControl();
            this.lbagCount = new System.Windows.Forms.Label();
            this.btnPopupAgent = new DevExpress.XtraEditors.SimpleButton();
            this.listViewAgent = new System.Windows.Forms.ListView();
            this.grpCtrlRegion = new DevExpress.XtraEditors.GroupControl();
            this.treeListArea = new DevExpress.XtraTreeList.TreeList();
            this.colKey = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.grpCtrlDevice = new DevExpress.XtraEditors.GroupControl();
            this.btnPopupDevice = new DevExpress.XtraEditors.SimpleButton();
            this.grpCtrlFileFilter = new DevExpress.XtraEditors.GroupControl();
            this.popupContainer = new DevExpress.XtraBars.PopupControlContainer(this.components);
            this.popupContainerSQL = new DevExpress.XtraBars.PopupControlContainer(this.components);
            this.checkBoxFileName = new System.Windows.Forms.CheckBox();
            this.radioGroupFileFilter = new DevExpress.XtraEditors.RadioGroup();
            this.grpCtrlPrjType = new DevExpress.XtraEditors.GroupControl();
            this.listViewService = new System.Windows.Forms.ListView();
            this.btnPopupService = new DevExpress.XtraEditors.SimpleButton();
            this.grpCtrlDataSrc = new DevExpress.XtraEditors.GroupControl();
            this.listViewProject = new System.Windows.Forms.ListView();
            this.btnPopupProject = new DevExpress.XtraEditors.SimpleButton();
            this.grpCtlCitys = new DevExpress.XtraEditors.GroupControl();
            this.btnPopupCitys = new DevExpress.XtraEditors.SimpleButton();
            this.grpCtrlPeriod = new DevExpress.XtraEditors.GroupControl();
            this.panelControl3 = new DevExpress.XtraEditors.PanelControl();
            this.cbxByRound = new System.Windows.Forms.CheckBox();
            this.checkBoxPeriodAdvance = new DevExpress.XtraEditors.CheckEdit();
            this.chkMultiTime = new DevExpress.XtraEditors.CheckEdit();
            this.dateTimePickerEndDate = new System.Windows.Forms.DateTimePicker();
            this.labelBeginDate = new DevExpress.XtraEditors.LabelControl();
            this.dateTimePickerBeginDate = new System.Windows.Forms.DateTimePicker();
            this.labelByRoundBegin = new DevExpress.XtraEditors.LabelControl();
            this.labelEndDate = new DevExpress.XtraEditors.LabelControl();
            this.buttonPeriodClear = new System.Windows.Forms.Button();
            this.buttonPeriodRemove = new System.Windows.Forms.Button();
            this.cbxByRoundFromYear = new System.Windows.Forms.ComboBox();
            this.buttonPeriodAdd = new System.Windows.Forms.Button();
            this.cbxByRoundFromRound = new System.Windows.Forms.ComboBox();
            this.listBoxPeriod = new System.Windows.Forms.ListBox();
            this.grpCtrlDiyCond = new DevExpress.XtraEditors.GroupControl();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.btnQuickCond = new DevExpress.XtraEditors.SimpleButton();
            this.btnResvRegion = new DevExpress.XtraEditors.SimpleButton();
            this.dockPanelLegend = new DevExpress.XtraBars.Docking.DockPanel();
            this.ctrlContainerLegend = new DevExpress.XtraBars.Docking.ControlContainer();
            this.legendPanel = new MasterCom.RAMS.Frame.LegendPanel();
            this.dockPanelConditonBatch = new DevExpress.XtraBars.Docking.DockPanel();
            this.controlContainer1 = new DevExpress.XtraBars.Docking.ControlContainer();
            this.splitContainerConditionBatch = new System.Windows.Forms.SplitContainer();
            this.gridControlAll = new DevExpress.XtraGrid.GridControl();
            this.gridViewTotal = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn21 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridView2 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.checkBoxAdvance = new System.Windows.Forms.CheckBox();
            this.toolStripDropDownRegion = new System.Windows.Forms.ToolStripDropDown();
            this.toolStripDropDownProject = new System.Windows.Forms.ToolStripDropDown();
            this.toolStripDropDownService = new System.Windows.Forms.ToolStripDropDown();
            this.ctxQuickCond = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.toolStripMenuItem1 = new System.Windows.Forms.ToolStripSeparator();
            this.miSaveCurCond = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripDropDownMarkFile = new System.Windows.Forms.ToolStripDropDown();
            this.toolStripDropDownAgent = new System.Windows.Forms.ToolStripDropDown();
            this.barManager = new DevExpress.XtraBars.BarManager(this.components);
            this.barDockControlTop = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlBottom = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlLeft = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlRight = new DevExpress.XtraBars.BarDockControl();
            this.toolStripDropDownCity = new System.Windows.Forms.ToolStripDropDown();
            this.bgWorkerBackground = new System.ComponentModel.BackgroundWorker();
            this.timerBackground = new System.Windows.Forms.Timer(this.components);
            this.toolStripDropDownDevice = new System.Windows.Forms.ToolStripDropDown();
            this.timerWeakRoadAutoStats = new System.Windows.Forms.Timer(this.components);
            this.bgWeakRoadAutoStats = new System.ComponentModel.BackgroundWorker();
            this.ctxMenuResvRegTv = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miLoadExcelChkNode = new System.Windows.Forms.ToolStripMenuItem();
            columnHeader1 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            columnHeader2 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            columnHeader3 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            ((System.ComponentModel.ISupportInitialize)(this.ribbon)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.applicationMenu1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemComboBox1)).BeginInit();
            this.contextMenuStripTreeView.SuspendLayout();
            this.contextMenuStripTabControlWorkSpace.SuspendLayout();
            this.contextMenuStripVisibleParam.SuspendLayout();
            this.contextMenuStripMenuItem.SuspendLayout();
            this.contextMenuStripTreeViewWorkSpace.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.panelControlWorkSheet)).BeginInit();
            this.panelControlWorkSheet.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControlWorkSheet)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.trackBarReplay)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.trackBarReplay.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dockManager)).BeginInit();
            this.hideContainerLeft.SuspendLayout();
            this.dockPanelNavi.SuspendLayout();
            this.ctrlContainerNavi.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControlNavigator)).BeginInit();
            this.xtraTabControlNavigator.SuspendLayout();
            this.tabPageSpace.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControlWks)).BeginInit();
            this.xtraTabControlWks.SuspendLayout();
            this.xtraTabPageWksNavi.SuspendLayout();
            this.xtraTabPageWksTemplates.SuspendLayout();
            this.tabPageMenu.SuspendLayout();
            this.tabPageParam.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControlChildVisibleParam)).BeginInit();
            this.xtraTabControlChildVisibleParam.SuspendLayout();
            this.xtraTabPageSignalParam.SuspendLayout();
            this.pnlParam.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtVisibleParamSearch.Properties)).BeginInit();
            this.xtraTabPageEventParam.SuspendLayout();
            this.pnlEventParam.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.textEditSearchEvent.Properties)).BeginInit();
            this.pnlCondAndLegend.SuspendLayout();
            this.dockPanelQueryCond.SuspendLayout();
            this.dockPanel1_Container.SuspendLayout();
            this.scrollCtrlCondition.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.grpCtrlCarrier)).BeginInit();
            this.grpCtrlCarrier.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.grpCtrlDaiwei)).BeginInit();
            this.grpCtrlDaiwei.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.grpCtrlRegion)).BeginInit();
            this.grpCtrlRegion.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.treeListArea)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.grpCtrlDevice)).BeginInit();
            this.grpCtrlDevice.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.grpCtrlFileFilter)).BeginInit();
            this.grpCtrlFileFilter.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.popupContainer)).BeginInit();
            this.popupContainer.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.popupContainerSQL)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.radioGroupFileFilter.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.grpCtrlPrjType)).BeginInit();
            this.grpCtrlPrjType.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.grpCtrlDataSrc)).BeginInit();
            this.grpCtrlDataSrc.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.grpCtlCitys)).BeginInit();
            this.grpCtlCitys.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.grpCtrlPeriod)).BeginInit();
            this.grpCtrlPeriod.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl3)).BeginInit();
            this.panelControl3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.checkBoxPeriodAdvance.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkMultiTime.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.grpCtrlDiyCond)).BeginInit();
            this.grpCtrlDiyCond.SuspendLayout();
            this.dockPanelLegend.SuspendLayout();
            this.ctrlContainerLegend.SuspendLayout();
            this.dockPanelConditonBatch.SuspendLayout();
            this.controlContainer1.SuspendLayout();
            this.splitContainerConditionBatch.Panel2.SuspendLayout();
            this.splitContainerConditionBatch.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlAll)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewTotal)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).BeginInit();
            this.ctxQuickCond.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.barManager)).BeginInit();
            this.ctxMenuResvRegTv.SuspendLayout();
            this.SuspendLayout();
            // 
            // columnHeader1
            // 
            columnHeader1.Width = 200;
            // 
            // columnHeader2
            // 
            columnHeader2.Width = 150;
            // 
            // columnHeader3
            // 
            columnHeader3.Width = 150;
            // 
            // ribbon
            // 
            this.ribbon.ApplicationButtonDropDownControl = this.applicationMenu1;
            this.ribbon.ApplicationButtonText = null;
            this.ribbon.Controller = this.barAndDockingController;
            // 
            // 
            // 
            this.ribbon.ExpandCollapseItem.Id = 0;
            this.ribbon.ExpandCollapseItem.Name = "";
            this.ribbon.ExpandCollapseItem.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonItemStyles.SmallWithoutText;
            this.ribbon.Font = new System.Drawing.Font("Tahoma", 9F);
            this.ribbon.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.ribbon.ExpandCollapseItem,
            this.barButtonItemOpenWorkSheet,
            this.barButtonItemNew,
            this.barButtonItemSave,
            this.barButtonItemNavigator,
            this.barButtonItemPreviousData,
            this.barButtonItemNextData,
            this.barButtonItemNewWorkSheet,
            this.barButtonItemRenameWorkSheet,
            this.barButtonItemRemoveWorkSheet,
            this.barButtonItemPreviousWorkSheet,
            this.barButtonItemNextWorkSheet,
            this.barButtonItemCascade,
            this.barButtonItemTileVertical,
            this.barButtonItemTileHorizontal,
            this.barButtonItemCloseAll,
            this.barEditItemWKSList,
            this.barButtonItemPlayLog,
            this.barButtonItemStopPlay,
            this.barButtonItemSlower,
            this.barButtonItemFaster,
            this.barButtonItemFullScreen,
            this.barSubItemMS,
            this.barButtonItemMiMS_All,
            this.barButtonItemMiMS_1,
            this.barButtonItemMiMS_2,
            this.barButtonItemMiMS_3,
            this.barButtonItemMiMS_4,
            this.barButtonItemMiMS_5,
            this.barButtonItemMiMS_6,
            this.barButtonItemMiMS_7,
            this.barButtonItemMiMS_8,
            this.barSubItemOpen,
            this.barButtonItemTopFrontMi,
            this.barButtonItemMiAllTopFront,
            this.barButtonItemOpacitySetMi,
            this.barButtonItemMiAllOpacitySet,
            this.barButtonItemArrangeIcons,
            this.barButtonItemLoadCellConfig_tsmi,
            this.barButtonItemQueryParam,
            this.barButtonItemPlanningInfoImport,
            this.barButtonItemOwnDataUpload,
            this.barButtonItemContents,
            this.barButtonItemViewHistory,
            this.barButtonItemAbout,
            this.barButtonItemSystemSetting,
            this.barButtonItemLink2SZMap,
            this.barButtonItemExit,
            this.barButtonItemSaveWorkSheet,
            this.barButtonItemSaveAs,
            this.barButtonItemExport2Tab,
            this.barButtonItemSysSetting,
            this.barSubItemOpenWorkSheet,
            this.barButtonItemFunctionNav,
            this.barButtonItemNewWS,
            this.barButtonItemFullScr,
            this.barButtonItemWKSManage,
            this.barStaticItemCellInfo,
            this.barButtonItemExportKML,
            this.barButtonItemShowMap,
            this.barSubItemCutScreen,
            this.barButtonItemCutScreen,
            this.barButtonItemHDCut,
            this.barSubItemChooseCity,
            this.barBtnUserFuncPermission,
            this.barSubItemPermissionSetting,
            this.barBtnUserDataSrcPermission,
            this.barBtnShowUserForm,
            this.barButtonItemUpdateInterface,
            this.barBtnUserCityRight,
            this.barChkItemDEBUG,
            this.barBtnCellCfgHistorySet,
            this.barButtonItemHDCutAndPrint,
            this.barStaticUpdate,
            this.lblCurServerIp,
            this.barBtnOpenProgramLocation,
            this.barBtnProgramRestart});
            this.ribbon.Location = new System.Drawing.Point(0, 0);
            this.ribbon.MaxItemId = 191;
            this.ribbon.MdiMergeStyle = DevExpress.XtraBars.Ribbon.RibbonMdiMergeStyle.Never;
            this.ribbon.Name = "ribbon";
            this.ribbon.PageHeaderItemLinks.Add(this.barEditItemWKSList);
            this.ribbon.PageHeaderItemLinks.Add(this.barButtonItemWKSManage, "KEYTIP");
            this.ribbon.Pages.AddRange(new DevExpress.XtraBars.Ribbon.RibbonPage[] {
            this.ribbonPageStart,
            this.ribbonPageHelp});
            this.ribbon.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repositoryItemComboBox1});
            this.ribbon.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonControlStyle.Office2010;
            this.ribbon.SelectedPage = this.ribbonPageStart;
            this.ribbon.ShowToolbarCustomizeItem = false;
            this.ribbon.Size = new System.Drawing.Size(1020, 148);
            this.ribbon.StatusBar = this.ribbonStatusBar;
            this.ribbon.Toolbar.ShowCustomizeItem = false;
            this.ribbon.ToolbarLocation = DevExpress.XtraBars.Ribbon.RibbonQuickAccessToolbarLocation.Hidden;
            this.ribbon.TransparentEditors = true;
            // 
            // applicationMenu1
            // 
            this.applicationMenu1.BottomPaneControlContainer = null;
            this.applicationMenu1.ItemLinks.Add(this.barButtonItemExport2Tab, true);
            this.applicationMenu1.ItemLinks.Add(this.barButtonItemSysSetting);
            this.applicationMenu1.ItemLinks.Add(this.barButtonItemUpdateInterface);
            this.applicationMenu1.ItemLinks.Add(this.barSubItemPermissionSetting);
            this.applicationMenu1.ItemLinks.Add(this.barBtnUserCityRight);
            this.applicationMenu1.ItemLinks.Add(this.barBtnShowUserForm);
            this.applicationMenu1.ItemLinks.Add(this.barButtonItemExit);
            this.applicationMenu1.Name = "applicationMenu1";
            this.applicationMenu1.Ribbon = this.ribbon;
            this.applicationMenu1.RightPaneControlContainer = null;
            // 
            // barButtonItemExport2Tab
            // 
            this.barButtonItemExport2Tab.Caption = "导出图层";
            this.barButtonItemExport2Tab.Glyph = ((System.Drawing.Image)(resources.GetObject("barButtonItemExport2Tab.Glyph")));
            this.barButtonItemExport2Tab.Id = 109;
            this.barButtonItemExport2Tab.Name = "barButtonItemExport2Tab";
            this.barButtonItemExport2Tab.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItemExport2Shp_ItemClick);
            // 
            // barButtonItemSysSetting
            // 
            this.barButtonItemSysSetting.Caption = "系统设置";
            this.barButtonItemSysSetting.Glyph = global::MasterCom.RAMS.Properties.Resources.otheroptions;
            this.barButtonItemSysSetting.Id = 110;
            this.barButtonItemSysSetting.Name = "barButtonItemSysSetting";
            this.barButtonItemSysSetting.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItemSystemSetting_ItemClick);
            // 
            // barButtonItemUpdateInterface
            // 
            this.barButtonItemUpdateInterface.Caption = "更新接口";
            this.barButtonItemUpdateInterface.Id = 151;
            this.barButtonItemUpdateInterface.LargeGlyph = global::MasterCom.RAMS.Properties.Resources.数据列表;
            this.barButtonItemUpdateInterface.Name = "barButtonItemUpdateInterface";
            this.barButtonItemUpdateInterface.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItemUpdateInterface_ItemClick);
            // 
            // barSubItemPermissionSetting
            // 
            this.barSubItemPermissionSetting.Caption = "权限设置";
            this.barSubItemPermissionSetting.Id = 147;
            this.barSubItemPermissionSetting.LargeGlyph = global::MasterCom.RAMS.Properties.Resources.SiteMap;
            this.barSubItemPermissionSetting.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnUserFuncPermission),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnUserDataSrcPermission)});
            this.barSubItemPermissionSetting.Name = "barSubItemPermissionSetting";
            this.barSubItemPermissionSetting.Visibility = DevExpress.XtraBars.BarItemVisibility.Never;
            // 
            // barBtnUserFuncPermission
            // 
            this.barBtnUserFuncPermission.Caption = "功能点权限设置";
            this.barBtnUserFuncPermission.Glyph = global::MasterCom.RAMS.Properties.Resources.SiteMap;
            this.barBtnUserFuncPermission.Id = 144;
            this.barBtnUserFuncPermission.Name = "barBtnUserFuncPermission";
            this.barBtnUserFuncPermission.Visibility = DevExpress.XtraBars.BarItemVisibility.Never;
            this.barBtnUserFuncPermission.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnUserFuncPermission_ItemClick);
            // 
            // barBtnUserDataSrcPermission
            // 
            this.barBtnUserDataSrcPermission.Caption = "数据源权限设置";
            this.barBtnUserDataSrcPermission.Glyph = global::MasterCom.RAMS.Properties.Resources.otherFunc;
            this.barBtnUserDataSrcPermission.Id = 148;
            this.barBtnUserDataSrcPermission.Name = "barBtnUserDataSrcPermission";
            this.barBtnUserDataSrcPermission.Visibility = DevExpress.XtraBars.BarItemVisibility.Never;
            this.barBtnUserDataSrcPermission.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnUserDataSrcPermission_ItemClick);
            // 
            // barBtnUserCityRight
            // 
            this.barBtnUserCityRight.Caption = "账号地市权限设置";
            this.barBtnUserCityRight.Id = 152;
            this.barBtnUserCityRight.LargeGlyph = global::MasterCom.RAMS.Properties.Resources.SiteMap;
            this.barBtnUserCityRight.Name = "barBtnUserCityRight";
            this.barBtnUserCityRight.Visibility = DevExpress.XtraBars.BarItemVisibility.Never;
            this.barBtnUserCityRight.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnUserCityRight_ItemClick);
            // 
            // barBtnShowUserForm
            // 
            this.barBtnShowUserForm.Caption = "账号管理";
            this.barBtnShowUserForm.Id = 149;
            this.barBtnShowUserForm.LargeGlyph = ((System.Drawing.Image)(resources.GetObject("barBtnShowUserForm.LargeGlyph")));
            this.barBtnShowUserForm.Name = "barBtnShowUserForm";
            this.barBtnShowUserForm.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnShowUserForm_ItemClick);
            // 
            // barButtonItemExit
            // 
            this.barButtonItemExit.Caption = "退出";
            this.barButtonItemExit.Glyph = ((System.Drawing.Image)(resources.GetObject("barButtonItemExit.Glyph")));
            this.barButtonItemExit.Id = 92;
            this.barButtonItemExit.Name = "barButtonItemExit";
            this.barButtonItemExit.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItemExit_ItemClick);
            // 
            // barAndDockingController
            // 
            this.barAndDockingController.AppearancesRibbon.PageGroupCaption.Font = new System.Drawing.Font("Tahoma", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.barAndDockingController.AppearancesRibbon.PageGroupCaption.Options.UseFont = true;
            this.barAndDockingController.AppearancesRibbon.PageHeader.Font = new System.Drawing.Font("Tahoma", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.barAndDockingController.AppearancesRibbon.PageHeader.Options.UseFont = true;
            this.barAndDockingController.PropertiesBar.AllowLinkLighting = false;
            // 
            // barButtonItemOpenWorkSheet
            // 
            this.barButtonItemOpenWorkSheet.Caption = "新建";
            this.barButtonItemOpenWorkSheet.Glyph = ((System.Drawing.Image)(resources.GetObject("barButtonItemOpenWorkSheet.Glyph")));
            this.barButtonItemOpenWorkSheet.Id = 4;
            this.barButtonItemOpenWorkSheet.ImageIndex = 6;
            this.barButtonItemOpenWorkSheet.LargeImageIndex = 0;
            this.barButtonItemOpenWorkSheet.Name = "barButtonItemOpenWorkSheet";
            this.barButtonItemOpenWorkSheet.Visibility = DevExpress.XtraBars.BarItemVisibility.Never;
            this.barButtonItemOpenWorkSheet.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItemNew_ItemClick);
            // 
            // barButtonItemNew
            // 
            this.barButtonItemNew.Caption = "新建";
            this.barButtonItemNew.Glyph = global::MasterCom.RAMS.Properties.Resources.newDoc;
            this.barButtonItemNew.Id = 7;
            this.barButtonItemNew.Name = "barButtonItemNew";
            this.barButtonItemNew.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItemNew_ItemClick);
            // 
            // barButtonItemSave
            // 
            this.barButtonItemSave.Caption = "保存";
            this.barButtonItemSave.Glyph = ((System.Drawing.Image)(resources.GetObject("barButtonItemSave.Glyph")));
            this.barButtonItemSave.Id = 9;
            this.barButtonItemSave.Name = "barButtonItemSave";
            this.barButtonItemSave.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItemSave_ItemClick);
            // 
            // barButtonItemNavigator
            // 
            this.barButtonItemNavigator.Caption = "隐藏/显示";
            this.barButtonItemNavigator.Id = 10;
            this.barButtonItemNavigator.LargeGlyph = ((System.Drawing.Image)(resources.GetObject("barButtonItemNavigator.LargeGlyph")));
            this.barButtonItemNavigator.Name = "barButtonItemNavigator";
            // 
            // barButtonItemPreviousData
            // 
            this.barButtonItemPreviousData.Caption = "上一次";
            this.barButtonItemPreviousData.Glyph = global::MasterCom.RAMS.Properties.Resources.toolStripButtonPreviousData;
            this.barButtonItemPreviousData.Hint = "上一次查询";
            this.barButtonItemPreviousData.Id = 11;
            this.barButtonItemPreviousData.Name = "barButtonItemPreviousData";
            this.barButtonItemPreviousData.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItemPreviousData_ItemClick);
            // 
            // barButtonItemNextData
            // 
            this.barButtonItemNextData.Caption = "下一次";
            this.barButtonItemNextData.Glyph = global::MasterCom.RAMS.Properties.Resources.toolStripButtonNextData;
            this.barButtonItemNextData.Hint = "下一次查询";
            this.barButtonItemNextData.Id = 12;
            this.barButtonItemNextData.Name = "barButtonItemNextData";
            this.barButtonItemNextData.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItemNextData_ItemClick);
            // 
            // barButtonItemNewWorkSheet
            // 
            this.barButtonItemNewWorkSheet.Caption = "新建";
            this.barButtonItemNewWorkSheet.Glyph = ((System.Drawing.Image)(resources.GetObject("barButtonItemNewWorkSheet.Glyph")));
            this.barButtonItemNewWorkSheet.Id = 13;
            this.barButtonItemNewWorkSheet.LargeGlyph = ((System.Drawing.Image)(resources.GetObject("barButtonItemNewWorkSheet.LargeGlyph")));
            this.barButtonItemNewWorkSheet.Name = "barButtonItemNewWorkSheet";
            this.barButtonItemNewWorkSheet.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItemNewWorkSheet_ItemClick);
            // 
            // barButtonItemRenameWorkSheet
            // 
            this.barButtonItemRenameWorkSheet.Caption = "重命名";
            this.barButtonItemRenameWorkSheet.Glyph = ((System.Drawing.Image)(resources.GetObject("barButtonItemRenameWorkSheet.Glyph")));
            this.barButtonItemRenameWorkSheet.Id = 14;
            this.barButtonItemRenameWorkSheet.Name = "barButtonItemRenameWorkSheet";
            this.barButtonItemRenameWorkSheet.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.renameWorkSheet_Click);
            // 
            // barButtonItemRemoveWorkSheet
            // 
            this.barButtonItemRemoveWorkSheet.Caption = "删除";
            this.barButtonItemRemoveWorkSheet.Glyph = global::MasterCom.RAMS.Properties.Resources.clear_cell_planning;
            this.barButtonItemRemoveWorkSheet.Id = 15;
            this.barButtonItemRemoveWorkSheet.Name = "barButtonItemRemoveWorkSheet";
            this.barButtonItemRemoveWorkSheet.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.removeWorkSheet_Click);
            // 
            // barButtonItemPreviousWorkSheet
            // 
            this.barButtonItemPreviousWorkSheet.Caption = "前一个";
            this.barButtonItemPreviousWorkSheet.Glyph = global::MasterCom.RAMS.Properties.Resources.previousWorkSheetToolStripButton;
            this.barButtonItemPreviousWorkSheet.Id = 16;
            this.barButtonItemPreviousWorkSheet.Name = "barButtonItemPreviousWorkSheet";
            this.barButtonItemPreviousWorkSheet.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.previousWorkSheet_Click);
            // 
            // barButtonItemNextWorkSheet
            // 
            this.barButtonItemNextWorkSheet.Caption = "后一个";
            this.barButtonItemNextWorkSheet.Glyph = global::MasterCom.RAMS.Properties.Resources.nextWorkSheetToolStripMenuItem;
            this.barButtonItemNextWorkSheet.Id = 17;
            this.barButtonItemNextWorkSheet.Name = "barButtonItemNextWorkSheet";
            this.barButtonItemNextWorkSheet.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.nextWorkSheet_Click);
            // 
            // barButtonItemCascade
            // 
            this.barButtonItemCascade.Caption = "层叠窗口";
            this.barButtonItemCascade.Glyph = ((System.Drawing.Image)(resources.GetObject("barButtonItemCascade.Glyph")));
            this.barButtonItemCascade.Id = 18;
            this.barButtonItemCascade.Name = "barButtonItemCascade";
            this.barButtonItemCascade.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItemCascade_ItemClick);
            // 
            // barButtonItemTileVertical
            // 
            this.barButtonItemTileVertical.Caption = "垂直平铺窗口";
            this.barButtonItemTileVertical.Glyph = global::MasterCom.RAMS.Properties.Resources.tileVerticalToolStripButton;
            this.barButtonItemTileVertical.Id = 19;
            this.barButtonItemTileVertical.Name = "barButtonItemTileVertical";
            this.barButtonItemTileVertical.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItemTileVertical_ItemClick);
            // 
            // barButtonItemTileHorizontal
            // 
            this.barButtonItemTileHorizontal.Caption = "水平平铺窗口";
            this.barButtonItemTileHorizontal.Glyph = global::MasterCom.RAMS.Properties.Resources.tileHorizontalToolStripButton;
            this.barButtonItemTileHorizontal.Id = 20;
            this.barButtonItemTileHorizontal.Name = "barButtonItemTileHorizontal";
            this.barButtonItemTileHorizontal.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItemTileHorizontal_ItemClick);
            // 
            // barButtonItemCloseAll
            // 
            this.barButtonItemCloseAll.Caption = "关闭所有窗口";
            this.barButtonItemCloseAll.Glyph = ((System.Drawing.Image)(resources.GetObject("barButtonItemCloseAll.Glyph")));
            this.barButtonItemCloseAll.Id = 21;
            this.barButtonItemCloseAll.Name = "barButtonItemCloseAll";
            this.barButtonItemCloseAll.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItemCloseAll_ItemClick);
            // 
            // barEditItemWKSList
            // 
            this.barEditItemWKSList.Caption = "当前工作空间：";
            this.barEditItemWKSList.Edit = this.repositoryItemComboBox1;
            this.barEditItemWKSList.Id = 23;
            this.barEditItemWKSList.Name = "barEditItemWKSList";
            this.barEditItemWKSList.Width = 100;
            // 
            // repositoryItemComboBox1
            // 
            this.repositoryItemComboBox1.AutoHeight = false;
            this.repositoryItemComboBox1.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repositoryItemComboBox1.Name = "repositoryItemComboBox1";
            this.repositoryItemComboBox1.UseParentBackground = true;
            // 
            // barButtonItemPlayLog
            // 
            this.barButtonItemPlayLog.Caption = "开始";
            this.barButtonItemPlayLog.Id = 24;
            this.barButtonItemPlayLog.LargeGlyph = ((System.Drawing.Image)(resources.GetObject("barButtonItemPlayLog.LargeGlyph")));
            this.barButtonItemPlayLog.Name = "barButtonItemPlayLog";
            this.barButtonItemPlayLog.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItemPlayLog_ItemClick);
            // 
            // barButtonItemStopPlay
            // 
            this.barButtonItemStopPlay.Caption = "结束";
            this.barButtonItemStopPlay.Enabled = false;
            this.barButtonItemStopPlay.Id = 29;
            this.barButtonItemStopPlay.LargeGlyph = ((System.Drawing.Image)(resources.GetObject("barButtonItemStopPlay.LargeGlyph")));
            this.barButtonItemStopPlay.Name = "barButtonItemStopPlay";
            this.barButtonItemStopPlay.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItemStopPlay_ItemClick);
            // 
            // barButtonItemSlower
            // 
            this.barButtonItemSlower.Caption = "减慢";
            this.barButtonItemSlower.Enabled = false;
            this.barButtonItemSlower.Id = 33;
            this.barButtonItemSlower.LargeGlyph = ((System.Drawing.Image)(resources.GetObject("barButtonItemSlower.LargeGlyph")));
            this.barButtonItemSlower.Name = "barButtonItemSlower";
            this.barButtonItemSlower.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItemSlower_ItemClick);
            // 
            // barButtonItemFaster
            // 
            this.barButtonItemFaster.Caption = "加快";
            this.barButtonItemFaster.Enabled = false;
            this.barButtonItemFaster.Id = 34;
            this.barButtonItemFaster.LargeGlyph = ((System.Drawing.Image)(resources.GetObject("barButtonItemFaster.LargeGlyph")));
            this.barButtonItemFaster.Name = "barButtonItemFaster";
            this.barButtonItemFaster.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItemFaster_ItemClick);
            // 
            // barButtonItemFullScreen
            // 
            this.barButtonItemFullScreen.Caption = "全屏显示";
            this.barButtonItemFullScreen.Hint = "全屏显示地图，按ESC键恢复";
            this.barButtonItemFullScreen.Id = 37;
            this.barButtonItemFullScreen.LargeGlyph = ((System.Drawing.Image)(resources.GetObject("barButtonItemFullScreen.LargeGlyph")));
            this.barButtonItemFullScreen.Name = "barButtonItemFullScreen";
            this.barButtonItemFullScreen.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItemFullScreen_ItemClick);
            // 
            // barSubItemMS
            // 
            this.barSubItemMS.Caption = "MS";
            this.barSubItemMS.Glyph = global::MasterCom.RAMS.Properties.Resources.mobilePhone;
            this.barSubItemMS.Id = 48;
            this.barSubItemMS.LargeGlyph = global::MasterCom.RAMS.Properties.Resources.mobilePhone;
            this.barSubItemMS.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.barButtonItemMiMS_All),
            new DevExpress.XtraBars.LinkPersistInfo(this.barButtonItemMiMS_1),
            new DevExpress.XtraBars.LinkPersistInfo(this.barButtonItemMiMS_2),
            new DevExpress.XtraBars.LinkPersistInfo(this.barButtonItemMiMS_3),
            new DevExpress.XtraBars.LinkPersistInfo(this.barButtonItemMiMS_4),
            new DevExpress.XtraBars.LinkPersistInfo(this.barButtonItemMiMS_5),
            new DevExpress.XtraBars.LinkPersistInfo(this.barButtonItemMiMS_6),
            new DevExpress.XtraBars.LinkPersistInfo(this.barButtonItemMiMS_7),
            new DevExpress.XtraBars.LinkPersistInfo(this.barButtonItemMiMS_8)});
            this.barSubItemMS.Name = "barSubItemMS";
            // 
            // barButtonItemMiMS_All
            // 
            this.barButtonItemMiMS_All.Caption = "ALL";
            this.barButtonItemMiMS_All.Id = 49;
            this.barButtonItemMiMS_All.Name = "barButtonItemMiMS_All";
            this.barButtonItemMiMS_All.Tag = "0";
            this.barButtonItemMiMS_All.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItemMiMS_X_ItemClick);
            // 
            // barButtonItemMiMS_1
            // 
            this.barButtonItemMiMS_1.Caption = "MS1";
            this.barButtonItemMiMS_1.Id = 50;
            this.barButtonItemMiMS_1.Name = "barButtonItemMiMS_1";
            this.barButtonItemMiMS_1.Tag = "1";
            this.barButtonItemMiMS_1.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItemMiMS_X_ItemClick);
            // 
            // barButtonItemMiMS_2
            // 
            this.barButtonItemMiMS_2.Caption = "MS2";
            this.barButtonItemMiMS_2.Id = 51;
            this.barButtonItemMiMS_2.Name = "barButtonItemMiMS_2";
            this.barButtonItemMiMS_2.Tag = "2";
            this.barButtonItemMiMS_2.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItemMiMS_X_ItemClick);
            // 
            // barButtonItemMiMS_3
            // 
            this.barButtonItemMiMS_3.Caption = "MS3";
            this.barButtonItemMiMS_3.Id = 52;
            this.barButtonItemMiMS_3.Name = "barButtonItemMiMS_3";
            this.barButtonItemMiMS_3.Tag = "3";
            this.barButtonItemMiMS_3.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItemMiMS_X_ItemClick);
            // 
            // barButtonItemMiMS_4
            // 
            this.barButtonItemMiMS_4.Caption = "MS4";
            this.barButtonItemMiMS_4.Id = 53;
            this.barButtonItemMiMS_4.Name = "barButtonItemMiMS_4";
            this.barButtonItemMiMS_4.Tag = "4";
            this.barButtonItemMiMS_4.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItemMiMS_X_ItemClick);
            // 
            // barButtonItemMiMS_5
            // 
            this.barButtonItemMiMS_5.Caption = "MS5";
            this.barButtonItemMiMS_5.Id = 54;
            this.barButtonItemMiMS_5.Name = "barButtonItemMiMS_5";
            this.barButtonItemMiMS_5.Tag = "5";
            this.barButtonItemMiMS_5.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItemMiMS_X_ItemClick);
            // 
            // barButtonItemMiMS_6
            // 
            this.barButtonItemMiMS_6.Caption = "MS6";
            this.barButtonItemMiMS_6.Id = 55;
            this.barButtonItemMiMS_6.Name = "barButtonItemMiMS_6";
            this.barButtonItemMiMS_6.Tag = "6";
            this.barButtonItemMiMS_6.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItemMiMS_X_ItemClick);
            // 
            // barButtonItemMiMS_7
            // 
            this.barButtonItemMiMS_7.Caption = "MS7";
            this.barButtonItemMiMS_7.Id = 56;
            this.barButtonItemMiMS_7.Name = "barButtonItemMiMS_7";
            this.barButtonItemMiMS_7.Tag = "7";
            this.barButtonItemMiMS_7.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItemMiMS_X_ItemClick);
            // 
            // barButtonItemMiMS_8
            // 
            this.barButtonItemMiMS_8.Caption = "MS8";
            this.barButtonItemMiMS_8.Id = 57;
            this.barButtonItemMiMS_8.Name = "barButtonItemMiMS_8";
            this.barButtonItemMiMS_8.Tag = "8";
            this.barButtonItemMiMS_8.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItemMiMS_X_ItemClick);
            // 
            // barSubItemOpen
            // 
            this.barSubItemOpen.Caption = "打开";
            this.barSubItemOpen.Glyph = ((System.Drawing.Image)(resources.GetObject("barSubItemOpen.Glyph")));
            this.barSubItemOpen.Id = 59;
            this.barSubItemOpen.LargeGlyph = ((System.Drawing.Image)(resources.GetObject("barSubItemOpen.LargeGlyph")));
            this.barSubItemOpen.Name = "barSubItemOpen";
            this.barSubItemOpen.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barSubItemOpen_ItemClick);
            // 
            // barButtonItemTopFrontMi
            // 
            this.barButtonItemTopFrontMi.Caption = "弹出最前";
            this.barButtonItemTopFrontMi.Glyph = ((System.Drawing.Image)(resources.GetObject("barButtonItemTopFrontMi.Glyph")));
            this.barButtonItemTopFrontMi.Id = 60;
            this.barButtonItemTopFrontMi.Name = "barButtonItemTopFrontMi";
            this.barButtonItemTopFrontMi.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItemTopFrontMi_ItemClick);
            // 
            // barButtonItemMiAllTopFront
            // 
            this.barButtonItemMiAllTopFront.Caption = "同数据源窗口弹出";
            this.barButtonItemMiAllTopFront.Glyph = ((System.Drawing.Image)(resources.GetObject("barButtonItemMiAllTopFront.Glyph")));
            this.barButtonItemMiAllTopFront.Id = 61;
            this.barButtonItemMiAllTopFront.Name = "barButtonItemMiAllTopFront";
            this.barButtonItemMiAllTopFront.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItemMiAllTopFront_ItemClick);
            // 
            // barButtonItemOpacitySetMi
            // 
            this.barButtonItemOpacitySetMi.Caption = "设置透明度并弹出";
            this.barButtonItemOpacitySetMi.Glyph = ((System.Drawing.Image)(resources.GetObject("barButtonItemOpacitySetMi.Glyph")));
            this.barButtonItemOpacitySetMi.Id = 62;
            this.barButtonItemOpacitySetMi.Name = "barButtonItemOpacitySetMi";
            this.barButtonItemOpacitySetMi.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItemOpacitySetMi_ItemClick);
            // 
            // barButtonItemMiAllOpacitySet
            // 
            this.barButtonItemMiAllOpacitySet.Caption = "同数据源窗口透明弹出";
            this.barButtonItemMiAllOpacitySet.Glyph = ((System.Drawing.Image)(resources.GetObject("barButtonItemMiAllOpacitySet.Glyph")));
            this.barButtonItemMiAllOpacitySet.Id = 63;
            this.barButtonItemMiAllOpacitySet.Name = "barButtonItemMiAllOpacitySet";
            this.barButtonItemMiAllOpacitySet.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItemMiAllOpacitySet_ItemClick);
            // 
            // barButtonItemArrangeIcons
            // 
            this.barButtonItemArrangeIcons.Caption = "排列图标";
            this.barButtonItemArrangeIcons.Glyph = ((System.Drawing.Image)(resources.GetObject("barButtonItemArrangeIcons.Glyph")));
            this.barButtonItemArrangeIcons.Id = 64;
            this.barButtonItemArrangeIcons.Name = "barButtonItemArrangeIcons";
            this.barButtonItemArrangeIcons.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItemArrangeIcons_ItemClick);
            // 
            // barButtonItemLoadCellConfig_tsmi
            // 
            this.barButtonItemLoadCellConfig_tsmi.Id = 141;
            this.barButtonItemLoadCellConfig_tsmi.Name = "barButtonItemLoadCellConfig_tsmi";
            // 
            // barButtonItemQueryParam
            // 
            this.barButtonItemQueryParam.Caption = "查找性能参数";
            this.barButtonItemQueryParam.Id = 79;
            this.barButtonItemQueryParam.LargeGlyph = ((System.Drawing.Image)(resources.GetObject("barButtonItemQueryParam.LargeGlyph")));
            this.barButtonItemQueryParam.Name = "barButtonItemQueryParam";
            this.barButtonItemQueryParam.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItemQueryParam_ItemClick);
            // 
            // barButtonItemPlanningInfoImport
            // 
            this.barButtonItemPlanningInfoImport.Caption = "导入规划信息";
            this.barButtonItemPlanningInfoImport.Id = 80;
            this.barButtonItemPlanningInfoImport.LargeGlyph = ((System.Drawing.Image)(resources.GetObject("barButtonItemPlanningInfoImport.LargeGlyph")));
            this.barButtonItemPlanningInfoImport.Name = "barButtonItemPlanningInfoImport";
            this.barButtonItemPlanningInfoImport.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItemPlanningInfoImport_ItemClick);
            // 
            // barButtonItemOwnDataUpload
            // 
            this.barButtonItemOwnDataUpload.Caption = "个人项目数据";
            this.barButtonItemOwnDataUpload.Id = 83;
            this.barButtonItemOwnDataUpload.LargeGlyph = ((System.Drawing.Image)(resources.GetObject("barButtonItemOwnDataUpload.LargeGlyph")));
            this.barButtonItemOwnDataUpload.Name = "barButtonItemOwnDataUpload";
            // 
            // barButtonItemContents
            // 
            this.barButtonItemContents.Caption = "帮助手册";
            this.barButtonItemContents.Id = 84;
            this.barButtonItemContents.LargeGlyph = ((System.Drawing.Image)(resources.GetObject("barButtonItemContents.LargeGlyph")));
            this.barButtonItemContents.Name = "barButtonItemContents";
            this.barButtonItemContents.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItemContents_ItemClick);
            // 
            // barButtonItemViewHistory
            // 
            this.barButtonItemViewHistory.Caption = "历史更新情况";
            this.barButtonItemViewHistory.Id = 85;
            this.barButtonItemViewHistory.LargeGlyph = ((System.Drawing.Image)(resources.GetObject("barButtonItemViewHistory.LargeGlyph")));
            this.barButtonItemViewHistory.Name = "barButtonItemViewHistory";
            this.barButtonItemViewHistory.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItemViewHistory_ItemClick);
            // 
            // barButtonItemAbout
            // 
            this.barButtonItemAbout.Caption = "关于";
            this.barButtonItemAbout.Id = 86;
            this.barButtonItemAbout.LargeGlyph = ((System.Drawing.Image)(resources.GetObject("barButtonItemAbout.LargeGlyph")));
            this.barButtonItemAbout.Name = "barButtonItemAbout";
            this.barButtonItemAbout.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItemAbout_ItemClick);
            // 
            // barButtonItemSystemSetting
            // 
            this.barButtonItemSystemSetting.Caption = "系统设置";
            this.barButtonItemSystemSetting.Id = 87;
            this.barButtonItemSystemSetting.LargeGlyph = global::MasterCom.RAMS.Properties.Resources.otheroptions;
            this.barButtonItemSystemSetting.Name = "barButtonItemSystemSetting";
            this.barButtonItemSystemSetting.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItemSystemSetting_ItemClick);
            // 
            // barButtonItemLink2SZMap
            // 
            this.barButtonItemLink2SZMap.Caption = "连接到深圳地图";
            this.barButtonItemLink2SZMap.Id = 91;
            this.barButtonItemLink2SZMap.LargeGlyph = ((System.Drawing.Image)(resources.GetObject("barButtonItemLink2SZMap.LargeGlyph")));
            this.barButtonItemLink2SZMap.Name = "barButtonItemLink2SZMap";
            this.barButtonItemLink2SZMap.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItemLink2SZMap_ItemClick);
            // 
            // barButtonItemSaveWorkSheet
            // 
            this.barButtonItemSaveWorkSheet.Caption = "保存工作空间";
            this.barButtonItemSaveWorkSheet.Glyph = ((System.Drawing.Image)(resources.GetObject("barButtonItemSaveWorkSheet.Glyph")));
            this.barButtonItemSaveWorkSheet.Id = 99;
            this.barButtonItemSaveWorkSheet.Name = "barButtonItemSaveWorkSheet";
            this.barButtonItemSaveWorkSheet.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItemSave_ItemClick);
            // 
            // barButtonItemSaveAs
            // 
            this.barButtonItemSaveAs.Caption = "另存...";
            this.barButtonItemSaveAs.Glyph = ((System.Drawing.Image)(resources.GetObject("barButtonItemSaveAs.Glyph")));
            this.barButtonItemSaveAs.Id = 108;
            this.barButtonItemSaveAs.Name = "barButtonItemSaveAs";
            this.barButtonItemSaveAs.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItemSaveAs_ItemClick);
            // 
            // barSubItemOpenWorkSheet
            // 
            this.barSubItemOpenWorkSheet.Caption = "打开工作空间";
            this.barSubItemOpenWorkSheet.Glyph = ((System.Drawing.Image)(resources.GetObject("barSubItemOpenWorkSheet.Glyph")));
            this.barSubItemOpenWorkSheet.Id = 111;
            this.barSubItemOpenWorkSheet.Name = "barSubItemOpenWorkSheet";
            this.barSubItemOpenWorkSheet.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barSubItemOpenWorkSheet_ItemClick);
            // 
            // barButtonItemFunctionNav
            // 
            this.barButtonItemFunctionNav.Caption = "功能导航";
            this.barButtonItemFunctionNav.Glyph = global::MasterCom.RAMS.Properties.Resources.SiteMap;
            this.barButtonItemFunctionNav.Id = 113;
            this.barButtonItemFunctionNav.Name = "barButtonItemFunctionNav";
            this.barButtonItemFunctionNav.Visibility = DevExpress.XtraBars.BarItemVisibility.Never;
            // 
            // barButtonItemNewWS
            // 
            this.barButtonItemNewWS.Caption = "barButtonItem12";
            this.barButtonItemNewWS.Description = "新建工作台";
            this.barButtonItemNewWS.Glyph = ((System.Drawing.Image)(resources.GetObject("barButtonItemNewWS.Glyph")));
            this.barButtonItemNewWS.Id = 119;
            this.barButtonItemNewWS.Name = "barButtonItemNewWS";
            this.barButtonItemNewWS.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItemNewWorkSheet_ItemClick);
            // 
            // barButtonItemFullScr
            // 
            this.barButtonItemFullScr.Caption = "barButtonItem13";
            this.barButtonItemFullScr.Description = "全屏显示";
            this.barButtonItemFullScr.Glyph = ((System.Drawing.Image)(resources.GetObject("barButtonItemFullScr.Glyph")));
            this.barButtonItemFullScr.Id = 120;
            this.barButtonItemFullScr.Name = "barButtonItemFullScr";
            this.barButtonItemFullScr.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItemFullScreen_ItemClick);
            // 
            // barButtonItemWKSManage
            // 
            this.barButtonItemWKSManage.Glyph = global::MasterCom.RAMS.Properties.Resources.delrpt;
            this.barButtonItemWKSManage.Hint = "删除工作空间";
            this.barButtonItemWKSManage.Id = 121;
            this.barButtonItemWKSManage.Name = "barButtonItemWKSManage";
            this.barButtonItemWKSManage.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItemWKSManage_ItemClick);
            // 
            // barStaticItemCellInfo
            // 
            this.barStaticItemCellInfo.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barStaticItemCellInfo.Appearance.ForeColor = System.Drawing.Color.Black;
            this.barStaticItemCellInfo.Appearance.Options.UseForeColor = true;
            this.barStaticItemCellInfo.Id = 122;
            this.barStaticItemCellInfo.Name = "barStaticItemCellInfo";
            toolTipTitleItem4.Text = "更新工参";
            toolTipItem4.LeftIndent = 6;
            toolTipItem4.Text = "点击以更新最新的工参信息";
            superToolTip4.Items.Add(toolTipTitleItem4);
            superToolTip4.Items.Add(toolTipItem4);
            this.barStaticItemCellInfo.SuperTip = superToolTip4;
            this.barStaticItemCellInfo.TextAlignment = System.Drawing.StringAlignment.Near;
            this.barStaticItemCellInfo.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barStaticItemlblCellInfo_ItemClick);
            // 
            // barButtonItemExportKML
            // 
            this.barButtonItemExportKML.Caption = "保存数据为KML格式";
            this.barButtonItemExportKML.Glyph = ((System.Drawing.Image)(resources.GetObject("barButtonItemExportKML.Glyph")));
            this.barButtonItemExportKML.Id = 126;
            this.barButtonItemExportKML.Name = "barButtonItemExportKML";
            this.barButtonItemExportKML.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItemExportKML_ItemClick);
            // 
            // barButtonItemShowMap
            // 
            this.barButtonItemShowMap.Caption = "地图";
            this.barButtonItemShowMap.Hint = "在当前工作台显示地图窗口";
            this.barButtonItemShowMap.Id = 129;
            this.barButtonItemShowMap.LargeGlyph = ((System.Drawing.Image)(resources.GetObject("barButtonItemShowMap.LargeGlyph")));
            this.barButtonItemShowMap.Name = "barButtonItemShowMap";
            this.barButtonItemShowMap.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItemShowMap_ItemClick);
            // 
            // barSubItemCutScreen
            // 
            this.barSubItemCutScreen.Caption = "截图";
            this.barSubItemCutScreen.Id = 138;
            this.barSubItemCutScreen.LargeGlyph = ((System.Drawing.Image)(resources.GetObject("barSubItemCutScreen.LargeGlyph")));
            this.barSubItemCutScreen.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.barButtonItemCutScreen),
            new DevExpress.XtraBars.LinkPersistInfo(this.barButtonItemHDCut),
            new DevExpress.XtraBars.LinkPersistInfo(this.barButtonItemHDCutAndPrint)});
            this.barSubItemCutScreen.Name = "barSubItemCutScreen";
            // 
            // barButtonItemCutScreen
            // 
            this.barButtonItemCutScreen.Caption = "屏幕截图";
            this.barButtonItemCutScreen.Id = 139;
            this.barButtonItemCutScreen.LargeGlyph = ((System.Drawing.Image)(resources.GetObject("barButtonItemCutScreen.LargeGlyph")));
            this.barButtonItemCutScreen.Name = "barButtonItemCutScreen";
            this.barButtonItemCutScreen.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItemCutScreen_ItemClick);
            // 
            // barButtonItemHDCut
            // 
            this.barButtonItemHDCut.Caption = "高清截图";
            this.barButtonItemHDCut.Id = 140;
            this.barButtonItemHDCut.Name = "barButtonItemHDCut";
            this.barButtonItemHDCut.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItemHDCut_ItemClick);
            // 
            // barButtonItemHDCutAndPrint
            // 
            this.barButtonItemHDCutAndPrint.Caption = "高清截图并打印...";
            this.barButtonItemHDCutAndPrint.Id = 156;
            this.barButtonItemHDCutAndPrint.Name = "barButtonItemHDCutAndPrint";
            this.barButtonItemHDCutAndPrint.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItemHDCutAndPrint_ItemClick);
            // 
            // barSubItemChooseCity
            // 
            this.barSubItemChooseCity.Caption = "城市";
            this.barSubItemChooseCity.Id = 143;
            this.barSubItemChooseCity.LargeGlyph = ((System.Drawing.Image)(resources.GetObject("barSubItemChooseCity.LargeGlyph")));
            this.barSubItemChooseCity.Name = "barSubItemChooseCity";
            this.barSubItemChooseCity.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barSubItemChooseCity_ItemClick);
            // 
            // barChkItemDEBUG
            // 
            this.barChkItemDEBUG.Caption = "DEBUG";
            this.barChkItemDEBUG.Id = 154;
            this.barChkItemDEBUG.LargeGlyph = global::MasterCom.RAMS.Properties.Resources.SiteMap;
            this.barChkItemDEBUG.Name = "barChkItemDEBUG";
            this.barChkItemDEBUG.Visibility = DevExpress.XtraBars.BarItemVisibility.Never;
            // 
            // barBtnCellCfgHistorySet
            // 
            this.barBtnCellCfgHistorySet.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnCellCfgHistorySet.Caption = "(最近)";
            this.barBtnCellCfgHistorySet.Id = 155;
            this.barBtnCellCfgHistorySet.Name = "barBtnCellCfgHistorySet";
            toolTipTitleItem5.Text = "设置工参时间";
            toolTipItem5.LeftIndent = 6;
            toolTipItem5.Text = "设置地图上显示的工参时间";
            superToolTip5.Items.Add(toolTipTitleItem5);
            superToolTip5.Items.Add(toolTipItem5);
            this.barBtnCellCfgHistorySet.SuperTip = superToolTip5;
            this.barBtnCellCfgHistorySet.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnCellCfgHistorySet_ItemClick);
            // 
            // barStaticUpdate
            // 
            this.barStaticUpdate.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barStaticUpdate.Id = 158;
            this.barStaticUpdate.Name = "barStaticUpdate";
            toolTipTitleItem6.Text = "更新客户端";
            toolTipItem6.LeftIndent = 6;
            toolTipItem6.Text = "点击以更新最新的客户端";
            superToolTip6.Items.Add(toolTipTitleItem6);
            superToolTip6.Items.Add(toolTipItem6);
            this.barStaticUpdate.SuperTip = superToolTip6;
            this.barStaticUpdate.TextAlignment = System.Drawing.StringAlignment.Near;
            // 
            // lblCurServerIp
            // 
            this.lblCurServerIp.Caption = "***********";
            this.lblCurServerIp.Id = 157;
            this.lblCurServerIp.Name = "lblCurServerIp";
            this.lblCurServerIp.TextAlignment = System.Drawing.StringAlignment.Near;
            // 
            // barBtnOpenProgramLocation
            // 
            this.barBtnOpenProgramLocation.Caption = "目录";
            this.barBtnOpenProgramLocation.Hint = "打开客户端所在目录";
            this.barBtnOpenProgramLocation.Id = 160;
            this.barBtnOpenProgramLocation.LargeGlyph = global::MasterCom.RAMS.Properties.Resources.barSubItemOpen;
            this.barBtnOpenProgramLocation.Name = "barBtnOpenProgramLocation";
            this.barBtnOpenProgramLocation.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnOpenProgramLocation_ItemClick);
            // 
            // barBtnProgramRestart
            // 
            this.barBtnProgramRestart.Caption = "重启";
            this.barBtnProgramRestart.Hint = "重启客户端";
            this.barBtnProgramRestart.Id = 171;
            this.barBtnProgramRestart.LargeGlyph = global::MasterCom.RAMS.Properties.Resources.restart;
            this.barBtnProgramRestart.Name = "barBtnProgramRestart";
            this.barBtnProgramRestart.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnProgramRestart_ItemClick);
            // 
            // ribbonPageStart
            // 
            this.ribbonPageStart.Groups.AddRange(new DevExpress.XtraBars.Ribbon.RibbonPageGroup[] {
            this.ribbonPageGroupFile,
            this.ribbonPageGroupChange,
            this.ribbonPageGroupWorkSheet,
            this.ribbonPageGroupPlay,
            this.ribbonPageGroupScreen,
            this.ribbonPageGroupWindowStyle,
            this.ribbonPageGroupMobile,
            this.ribbonPageGroupChooseCity,
            this.ribbonPageGroupMap,
            this.ribbonPageGroupProgram});
            this.ribbonPageStart.Name = "ribbonPageStart";
            this.ribbonPageStart.Text = "开始";
            // 
            // ribbonPageGroupFile
            // 
            this.ribbonPageGroupFile.ItemLinks.Add(this.barSubItemOpen);
            this.ribbonPageGroupFile.ItemLinks.Add(this.barButtonItemSave);
            this.ribbonPageGroupFile.ItemLinks.Add(this.barButtonItemSaveAs);
            this.ribbonPageGroupFile.ItemLinks.Add(this.barButtonItemNew);
            this.ribbonPageGroupFile.Name = "ribbonPageGroupFile";
            this.ribbonPageGroupFile.ShowCaptionButton = false;
            this.ribbonPageGroupFile.Text = "工作空间";
            // 
            // ribbonPageGroupChange
            // 
            this.ribbonPageGroupChange.ItemLinks.Add(this.barButtonItemPreviousData);
            this.ribbonPageGroupChange.ItemLinks.Add(this.barButtonItemNextData);
            this.ribbonPageGroupChange.Name = "ribbonPageGroupChange";
            this.ribbonPageGroupChange.ShowCaptionButton = false;
            this.ribbonPageGroupChange.Text = "查询切换";
            // 
            // ribbonPageGroupWorkSheet
            // 
            this.ribbonPageGroupWorkSheet.ItemLinks.Add(this.barButtonItemNewWorkSheet, true);
            this.ribbonPageGroupWorkSheet.ItemLinks.Add(this.barButtonItemRenameWorkSheet);
            this.ribbonPageGroupWorkSheet.ItemLinks.Add(this.barButtonItemRemoveWorkSheet);
            this.ribbonPageGroupWorkSheet.ItemLinks.Add(this.barButtonItemPreviousWorkSheet, true);
            this.ribbonPageGroupWorkSheet.ItemLinks.Add(this.barButtonItemNextWorkSheet);
            this.ribbonPageGroupWorkSheet.Name = "ribbonPageGroupWorkSheet";
            this.ribbonPageGroupWorkSheet.ShowCaptionButton = false;
            this.ribbonPageGroupWorkSheet.Text = "工作台";
            // 
            // ribbonPageGroupPlay
            // 
            this.ribbonPageGroupPlay.ItemLinks.Add(this.barButtonItemPlayLog);
            this.ribbonPageGroupPlay.ItemLinks.Add(this.barButtonItemStopPlay);
            this.ribbonPageGroupPlay.ItemLinks.Add(this.barButtonItemSlower, true);
            this.ribbonPageGroupPlay.ItemLinks.Add(this.barButtonItemFaster);
            this.ribbonPageGroupPlay.Name = "ribbonPageGroupPlay";
            this.ribbonPageGroupPlay.ShowCaptionButton = false;
            this.ribbonPageGroupPlay.Text = "播放";
            // 
            // ribbonPageGroupScreen
            // 
            this.ribbonPageGroupScreen.ItemLinks.Add(this.barButtonItemFullScreen, true);
            this.ribbonPageGroupScreen.ItemLinks.Add(this.barSubItemCutScreen, "地图截图");
            this.ribbonPageGroupScreen.Name = "ribbonPageGroupScreen";
            this.ribbonPageGroupScreen.ShowCaptionButton = false;
            this.ribbonPageGroupScreen.Text = "屏幕";
            // 
            // ribbonPageGroupWindowStyle
            // 
            this.ribbonPageGroupWindowStyle.ItemLinks.Add(this.barButtonItemCascade);
            this.ribbonPageGroupWindowStyle.ItemLinks.Add(this.barButtonItemTileVertical);
            this.ribbonPageGroupWindowStyle.ItemLinks.Add(this.barButtonItemTileHorizontal);
            this.ribbonPageGroupWindowStyle.ItemLinks.Add(this.barButtonItemOpacitySetMi);
            this.ribbonPageGroupWindowStyle.ItemLinks.Add(this.barButtonItemMiAllTopFront);
            this.ribbonPageGroupWindowStyle.ItemLinks.Add(this.barButtonItemMiAllOpacitySet);
            this.ribbonPageGroupWindowStyle.ItemLinks.Add(this.barButtonItemArrangeIcons);
            this.ribbonPageGroupWindowStyle.ItemLinks.Add(this.barButtonItemTopFrontMi);
            this.ribbonPageGroupWindowStyle.ItemLinks.Add(this.barButtonItemCloseAll);
            this.ribbonPageGroupWindowStyle.Name = "ribbonPageGroupWindowStyle";
            this.ribbonPageGroupWindowStyle.ShowCaptionButton = false;
            this.ribbonPageGroupWindowStyle.Text = "窗口样式";
            // 
            // ribbonPageGroupMobile
            // 
            this.ribbonPageGroupMobile.ItemLinks.Add(this.barSubItemMS);
            this.ribbonPageGroupMobile.Name = "ribbonPageGroupMobile";
            this.ribbonPageGroupMobile.ShowCaptionButton = false;
            this.ribbonPageGroupMobile.Text = "端口";
            // 
            // ribbonPageGroupChooseCity
            // 
            this.ribbonPageGroupChooseCity.ItemLinks.Add(this.barSubItemChooseCity);
            this.ribbonPageGroupChooseCity.Name = "ribbonPageGroupChooseCity";
            this.ribbonPageGroupChooseCity.ShowCaptionButton = false;
            this.ribbonPageGroupChooseCity.Text = "城市";
            this.ribbonPageGroupChooseCity.Visible = false;
            // 
            // ribbonPageGroupMap
            // 
            this.ribbonPageGroupMap.ItemLinks.Add(this.barButtonItemShowMap);
            this.ribbonPageGroupMap.Name = "ribbonPageGroupMap";
            this.ribbonPageGroupMap.ShowCaptionButton = false;
            this.ribbonPageGroupMap.Text = "地图";
            // 
            // ribbonPageGroupProgram
            // 
            this.ribbonPageGroupProgram.ItemLinks.Add(this.barBtnOpenProgramLocation, true);
            this.ribbonPageGroupProgram.ItemLinks.Add(this.barBtnProgramRestart);
            this.ribbonPageGroupProgram.Name = "ribbonPageGroupProgram";
            this.ribbonPageGroupProgram.ShowCaptionButton = false;
            this.ribbonPageGroupProgram.Text = "客户端";
            // 
            // ribbonPageHelp
            // 
            this.ribbonPageHelp.Groups.AddRange(new DevExpress.XtraBars.Ribbon.RibbonPageGroup[] {
            this.ribbonPageGroupOpen,
            this.ribbonPageGroupCheck,
            this.ribbonPageGroupAbout});
            this.ribbonPageHelp.Name = "ribbonPageHelp";
            this.ribbonPageHelp.Text = "帮助";
            // 
            // ribbonPageGroupOpen
            // 
            this.ribbonPageGroupOpen.ItemLinks.Add(this.barButtonItemContents);
            this.ribbonPageGroupOpen.ItemLinks.Add(this.barChkItemDEBUG);
            this.ribbonPageGroupOpen.Name = "ribbonPageGroupOpen";
            this.ribbonPageGroupOpen.ShowCaptionButton = false;
            this.ribbonPageGroupOpen.Text = "帮助";
            // 
            // ribbonPageGroupCheck
            // 
            this.ribbonPageGroupCheck.ItemLinks.Add(this.barButtonItemViewHistory);
            this.ribbonPageGroupCheck.Name = "ribbonPageGroupCheck";
            this.ribbonPageGroupCheck.ShowCaptionButton = false;
            this.ribbonPageGroupCheck.Text = "查看";
            // 
            // ribbonPageGroupAbout
            // 
            this.ribbonPageGroupAbout.ItemLinks.Add(this.barButtonItemAbout);
            this.ribbonPageGroupAbout.Name = "ribbonPageGroupAbout";
            this.ribbonPageGroupAbout.ShowCaptionButton = false;
            this.ribbonPageGroupAbout.Text = "关于";
            // 
            // ribbonStatusBar
            // 
            this.ribbonStatusBar.ItemLinks.Add(this.barStaticItemCellInfo, true);
            this.ribbonStatusBar.ItemLinks.Add(this.barBtnCellCfgHistorySet);
            this.ribbonStatusBar.ItemLinks.Add(this.barStaticUpdate);
            this.ribbonStatusBar.ItemLinks.Add(this.lblCurServerIp);
            this.ribbonStatusBar.Location = new System.Drawing.Point(0, 739);
            this.ribbonStatusBar.Name = "ribbonStatusBar";
            this.ribbonStatusBar.Ribbon = this.ribbon;
            this.ribbonStatusBar.Size = new System.Drawing.Size(1020, 32);
            // 
            // imageListMenuItem
            // 
            this.imageListMenuItem.ImageStream = ((System.Windows.Forms.ImageListStreamer)(resources.GetObject("imageListMenuItem.ImageStream")));
            this.imageListMenuItem.TransparentColor = System.Drawing.Color.Transparent;
            this.imageListMenuItem.Images.SetKeyName(0, "DefaultApp.png");
            this.imageListMenuItem.Images.SetKeyName(1, "folder_close_blue.png");
            this.imageListMenuItem.Images.SetKeyName(2, "folder_blue.png");
            this.imageListMenuItem.Images.SetKeyName(3, "folder_closed.gif");
            this.imageListMenuItem.Images.SetKeyName(4, "folder_opened.gif");
            // 
            // contextMenuStripTreeView
            // 
            this.contextMenuStripTreeView.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.minAddChildNode,
            this.minEditCurrentNode,
            this.minDelCurrentNode,
            this.toolStripSeparator17,
            this.minAddRootNode});
            this.contextMenuStripTreeView.Name = "contextMenuStripTreeView";
            this.contextMenuStripTreeView.Size = new System.Drawing.Size(149, 98);
            // 
            // minAddChildNode
            // 
            this.minAddChildNode.Name = "minAddChildNode";
            this.minAddChildNode.Size = new System.Drawing.Size(148, 22);
            this.minAddChildNode.Text = "增加子节点";
            this.minAddChildNode.Click += new System.EventHandler(this.minAddChildNode_Click);
            // 
            // minEditCurrentNode
            // 
            this.minEditCurrentNode.Name = "minEditCurrentNode";
            this.minEditCurrentNode.Size = new System.Drawing.Size(148, 22);
            this.minEditCurrentNode.Text = "编辑当前节点";
            this.minEditCurrentNode.Click += new System.EventHandler(this.minEditCurrentNode_Click);
            // 
            // minDelCurrentNode
            // 
            this.minDelCurrentNode.Image = global::MasterCom.RAMS.Properties.Resources.delete;
            this.minDelCurrentNode.Name = "minDelCurrentNode";
            this.minDelCurrentNode.Size = new System.Drawing.Size(148, 22);
            this.minDelCurrentNode.Text = "删除当前节点";
            this.minDelCurrentNode.Click += new System.EventHandler(this.minDelCurrentNode_Click);
            // 
            // toolStripSeparator17
            // 
            this.toolStripSeparator17.Name = "toolStripSeparator17";
            this.toolStripSeparator17.Size = new System.Drawing.Size(145, 6);
            // 
            // minAddRootNode
            // 
            this.minAddRootNode.Name = "minAddRootNode";
            this.minAddRootNode.Size = new System.Drawing.Size(148, 22);
            this.minAddRootNode.Text = "增加根节点";
            this.minAddRootNode.Click += new System.EventHandler(this.minAddRootNode_Click);
            // 
            // imageListVisibleParam
            // 
            this.imageListVisibleParam.ImageStream = ((System.Windows.Forms.ImageListStreamer)(resources.GetObject("imageListVisibleParam.ImageStream")));
            this.imageListVisibleParam.TransparentColor = System.Drawing.Color.Transparent;
            this.imageListVisibleParam.Images.SetKeyName(0, "iegroup.gif");
            this.imageListVisibleParam.Images.SetKeyName(1, "ieicon.gif");
            this.imageListVisibleParam.Images.SetKeyName(2, "coloricon.gif");
            this.imageListVisibleParam.Images.SetKeyName(3, "sizeicon.gif");
            this.imageListVisibleParam.Images.SetKeyName(4, "symbolicon.gif");
            // 
            // imageListWorkSpace
            // 
            this.imageListWorkSpace.ImageStream = ((System.Windows.Forms.ImageListStreamer)(resources.GetObject("imageListWorkSpace.ImageStream")));
            this.imageListWorkSpace.TransparentColor = System.Drawing.Color.Transparent;
            this.imageListWorkSpace.Images.SetKeyName(0, "worksheet.gif");
            // 
            // LeftToolStripPanel
            // 
            this.LeftToolStripPanel.Location = new System.Drawing.Point(-46, 0);
            this.LeftToolStripPanel.Name = "LeftToolStripPanel";
            this.LeftToolStripPanel.Orientation = System.Windows.Forms.Orientation.Horizontal;
            this.LeftToolStripPanel.RowMargin = new System.Windows.Forms.Padding(3, 0, 0, 0);
            this.LeftToolStripPanel.Size = new System.Drawing.Size(0, 0);
            // 
            // RightToolStripPanel
            // 
            this.RightToolStripPanel.Location = new System.Drawing.Point(-46, 0);
            this.RightToolStripPanel.Name = "RightToolStripPanel";
            this.RightToolStripPanel.Orientation = System.Windows.Forms.Orientation.Horizontal;
            this.RightToolStripPanel.RowMargin = new System.Windows.Forms.Padding(3, 0, 0, 0);
            this.RightToolStripPanel.Size = new System.Drawing.Size(0, 0);
            // 
            // contextMenuStripTabControlWorkSpace
            // 
            this.contextMenuStripTabControlWorkSpace.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.toolStripMenuItemNewWorkSpace1,
            this.toolStripMenuItemRemoveWorkSpace1,
            this.toolStripMenuItemRenameWorkSpace1,
            this.miSaveAsWSTemplate,
            this.toolStripSeparator3,
            this.toolStripMenuItemPreviousWorkSpace,
            this.toolStripMenuItemNextWorkSpace});
            this.contextMenuStripTabControlWorkSpace.Name = "contextMenuStripTabControlWorkSpace";
            this.contextMenuStripTabControlWorkSpace.Size = new System.Drawing.Size(161, 142);
            this.contextMenuStripTabControlWorkSpace.Opening += new System.ComponentModel.CancelEventHandler(this.contextMenuStripTabControlWorkSpace_Opening);
            // 
            // toolStripMenuItemNewWorkSpace1
            // 
            this.toolStripMenuItemNewWorkSpace1.Image = ((System.Drawing.Image)(resources.GetObject("toolStripMenuItemNewWorkSpace1.Image")));
            this.toolStripMenuItemNewWorkSpace1.Name = "toolStripMenuItemNewWorkSpace1";
            this.toolStripMenuItemNewWorkSpace1.Size = new System.Drawing.Size(160, 22);
            this.toolStripMenuItemNewWorkSpace1.Text = "新建(&N)";
            this.toolStripMenuItemNewWorkSpace1.Click += new System.EventHandler(this.newWorkSheetStripMenuItem_Click);
            // 
            // toolStripMenuItemRemoveWorkSpace1
            // 
            this.toolStripMenuItemRemoveWorkSpace1.Name = "toolStripMenuItemRemoveWorkSpace1";
            this.toolStripMenuItemRemoveWorkSpace1.Size = new System.Drawing.Size(160, 22);
            this.toolStripMenuItemRemoveWorkSpace1.Text = "删除(&D)";
            this.toolStripMenuItemRemoveWorkSpace1.Click += new System.EventHandler(this.removeWorkSheet_Click);
            // 
            // toolStripMenuItemRenameWorkSpace1
            // 
            this.toolStripMenuItemRenameWorkSpace1.Name = "toolStripMenuItemRenameWorkSpace1";
            this.toolStripMenuItemRenameWorkSpace1.Size = new System.Drawing.Size(160, 22);
            this.toolStripMenuItemRenameWorkSpace1.Text = "重命名(&R)";
            this.toolStripMenuItemRenameWorkSpace1.Click += new System.EventHandler(this.renameWorkSheet_Click);
            // 
            // miSaveAsWSTemplate
            // 
            this.miSaveAsWSTemplate.Name = "miSaveAsWSTemplate";
            this.miSaveAsWSTemplate.Size = new System.Drawing.Size(160, 22);
            this.miSaveAsWSTemplate.Text = "保存为模板(&S)...";
            this.miSaveAsWSTemplate.Click += new System.EventHandler(this.miSaveAsWSTemplate_Click);
            // 
            // toolStripSeparator3
            // 
            this.toolStripSeparator3.Name = "toolStripSeparator3";
            this.toolStripSeparator3.Size = new System.Drawing.Size(157, 6);
            // 
            // toolStripMenuItemPreviousWorkSpace
            // 
            this.toolStripMenuItemPreviousWorkSpace.Image = ((System.Drawing.Image)(resources.GetObject("toolStripMenuItemPreviousWorkSpace.Image")));
            this.toolStripMenuItemPreviousWorkSpace.Name = "toolStripMenuItemPreviousWorkSpace";
            this.toolStripMenuItemPreviousWorkSpace.Size = new System.Drawing.Size(160, 22);
            this.toolStripMenuItemPreviousWorkSpace.Text = "前一个(&P)";
            this.toolStripMenuItemPreviousWorkSpace.Click += new System.EventHandler(this.previousWorkSheet_Click);
            // 
            // toolStripMenuItemNextWorkSpace
            // 
            this.toolStripMenuItemNextWorkSpace.Image = ((System.Drawing.Image)(resources.GetObject("toolStripMenuItemNextWorkSpace.Image")));
            this.toolStripMenuItemNextWorkSpace.Name = "toolStripMenuItemNextWorkSpace";
            this.toolStripMenuItemNextWorkSpace.Size = new System.Drawing.Size(160, 22);
            this.toolStripMenuItemNextWorkSpace.Text = "后一个(&E)";
            this.toolStripMenuItemNextWorkSpace.Click += new System.EventHandler(this.nextWorkSheet_Click);
            // 
            // toolStripMenuItemCloseAll
            // 
            this.toolStripMenuItemCloseAll.Image = ((System.Drawing.Image)(resources.GetObject("toolStripMenuItemCloseAll.Image")));
            this.toolStripMenuItemCloseAll.Name = "toolStripMenuItemCloseAll";
            this.toolStripMenuItemCloseAll.Size = new System.Drawing.Size(138, 22);
            this.toolStripMenuItemCloseAll.Text = "全部关闭(&L)";
            this.toolStripMenuItemCloseAll.Click += new System.EventHandler(this.toolStripMenuItemCloseAll_Click);
            // 
            // contextMenuStripVisibleParam
            // 
            this.contextMenuStripVisibleParam.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.ToolStripMenuItemModifyVisibleParam});
            this.contextMenuStripVisibleParam.Name = "contextMenuStripVisibleParam";
            this.contextMenuStripVisibleParam.Size = new System.Drawing.Size(121, 26);
            // 
            // ToolStripMenuItemModifyVisibleParam
            // 
            this.ToolStripMenuItemModifyVisibleParam.Name = "ToolStripMenuItemModifyVisibleParam";
            this.ToolStripMenuItemModifyVisibleParam.Size = new System.Drawing.Size(120, 22);
            this.ToolStripMenuItemModifyVisibleParam.Text = "修改(&M)";
            this.ToolStripMenuItemModifyVisibleParam.Click += new System.EventHandler(this.treeViewVisibleParam_DoubleClick);
            // 
            // toolStripMenuItemCloseWindow
            // 
            this.toolStripMenuItemCloseWindow.Name = "toolStripMenuItemCloseWindow";
            this.toolStripMenuItemCloseWindow.Size = new System.Drawing.Size(138, 22);
            this.toolStripMenuItemCloseWindow.Text = "关闭(&C)";
            this.toolStripMenuItemCloseWindow.Click += new System.EventHandler(this.toolStripMenuItemCloseWindow_Click);
            // 
            // toolStripMenuItemRenameWorkSpace
            // 
            this.toolStripMenuItemRenameWorkSpace.Name = "toolStripMenuItemRenameWorkSpace";
            this.toolStripMenuItemRenameWorkSpace.Size = new System.Drawing.Size(138, 22);
            this.toolStripMenuItemRenameWorkSpace.Text = "重命名(&R)";
            this.toolStripMenuItemRenameWorkSpace.Click += new System.EventHandler(this.toolStripMenuItemRenameWorkSpace_Click);
            // 
            // toolStripMenuItemNewWorkSpace
            // 
            this.toolStripMenuItemNewWorkSpace.Image = ((System.Drawing.Image)(resources.GetObject("toolStripMenuItemNewWorkSpace.Image")));
            this.toolStripMenuItemNewWorkSpace.Name = "toolStripMenuItemNewWorkSpace";
            this.toolStripMenuItemNewWorkSpace.Size = new System.Drawing.Size(138, 22);
            this.toolStripMenuItemNewWorkSpace.Text = "新建(&N)";
            this.toolStripMenuItemNewWorkSpace.Click += new System.EventHandler(this.newWorkSheetStripMenuItem_Click);
            // 
            // contextMenuStripMenuItem
            // 
            this.contextMenuStripMenuItem.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.ToolStripMenuItemOpenWindow});
            this.contextMenuStripMenuItem.Name = "contextMenuStripMenuItem";
            this.contextMenuStripMenuItem.Size = new System.Drawing.Size(119, 26);
            // 
            // ToolStripMenuItemOpenWindow
            // 
            this.ToolStripMenuItemOpenWindow.Name = "ToolStripMenuItemOpenWindow";
            this.ToolStripMenuItemOpenWindow.Size = new System.Drawing.Size(118, 22);
            this.ToolStripMenuItemOpenWindow.Text = "打开(&O)";
            this.ToolStripMenuItemOpenWindow.Click += new System.EventHandler(this.treeViewMenuItem_DoubleClick);
            // 
            // toolStripMenuItemRemoveWorkSpace
            // 
            this.toolStripMenuItemRemoveWorkSpace.Name = "toolStripMenuItemRemoveWorkSpace";
            this.toolStripMenuItemRemoveWorkSpace.Size = new System.Drawing.Size(138, 22);
            this.toolStripMenuItemRemoveWorkSpace.Text = "删除(&D)";
            this.toolStripMenuItemRemoveWorkSpace.Click += new System.EventHandler(this.toolStripMenuItemRemoveWorkSpace_Click);
            // 
            // toolStripDropDownLocal
            // 
            this.toolStripDropDownLocal.LayoutStyle = System.Windows.Forms.ToolStripLayoutStyle.Flow;
            this.toolStripDropDownLocal.Name = "toolStripDropDown1";
            this.toolStripDropDownLocal.Size = new System.Drawing.Size(2, 4);
            // 
            // ctxQuickRestoreWindows
            // 
            this.ctxQuickRestoreWindows.Name = "contextMenuStrip1";
            this.ctxQuickRestoreWindows.Size = new System.Drawing.Size(61, 4);
            // 
            // ToolStripMenuItemActive
            // 
            this.ToolStripMenuItemActive.Name = "ToolStripMenuItemActive";
            this.ToolStripMenuItemActive.Size = new System.Drawing.Size(138, 22);
            this.ToolStripMenuItemActive.Text = "激活(&A)";
            this.ToolStripMenuItemActive.Click += new System.EventHandler(this.treeViewWorkSpace_DoubleClick);
            // 
            // contextMenuStripTreeViewWorkSpace
            // 
            this.contextMenuStripTreeViewWorkSpace.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.ToolStripMenuItemActive,
            this.toolStripSeparator1,
            this.toolStripMenuItemNewWorkSpace,
            this.toolStripMenuItemRemoveWorkSpace,
            this.toolStripMenuItemRenameWorkSpace,
            this.toolStripSeparator2,
            this.toolStripMenuItemCloseWindow,
            this.toolStripMenuItemCloseAll});
            this.contextMenuStripTreeViewWorkSpace.Name = "contextMenuStripTabControlWorkSpace";
            this.contextMenuStripTreeViewWorkSpace.Size = new System.Drawing.Size(139, 148);
            // 
            // toolStripSeparator1
            // 
            this.toolStripSeparator1.Name = "toolStripSeparator1";
            this.toolStripSeparator1.Size = new System.Drawing.Size(135, 6);
            // 
            // toolStripSeparator2
            // 
            this.toolStripSeparator2.Name = "toolStripSeparator2";
            this.toolStripSeparator2.Size = new System.Drawing.Size(135, 6);
            // 
            // logTimer
            // 
            this.logTimer.Interval = 60000;
            this.logTimer.Tick += new System.EventHandler(this.logTimer_Tick);
            // 
            // bgWorkerLog
            // 
            this.bgWorkerLog.WorkerReportsProgress = true;
            this.bgWorkerLog.DoWork += new System.ComponentModel.DoWorkEventHandler(this.bgWorkerLog_DoWork);
            this.bgWorkerLog.RunWorkerCompleted += new System.ComponentModel.RunWorkerCompletedEventHandler(this.bgWorkerLog_RunWorkerCompleted);
            // 
            // playTimer
            // 
            this.playTimer.Interval = 500;
            this.playTimer.Tick += new System.EventHandler(this.playTimer_Tick);
            // 
            // imageListWnd
            // 
            this.imageListWnd.ImageStream = ((System.Windows.Forms.ImageListStreamer)(resources.GetObject("imageListWnd.ImageStream")));
            this.imageListWnd.TransparentColor = System.Drawing.Color.Transparent;
            this.imageListWnd.Images.SetKeyName(0, "blackblock.ico");
            // 
            // defaultLookAndFeel1
            // 
            this.defaultLookAndFeel1.LookAndFeel.SkinName = "Office 2010 Blue";
            // 
            // imageListVisibleEvent
            // 
            this.imageListVisibleEvent.ImageStream = ((System.Windows.Forms.ImageListStreamer)(resources.GetObject("imageListVisibleEvent.ImageStream")));
            this.imageListVisibleEvent.TransparentColor = System.Drawing.Color.Transparent;
            this.imageListVisibleEvent.Images.SetKeyName(0, "iegroup.gif");
            // 
            // panelControlWorkSheet
            // 
            this.panelControlWorkSheet.Controls.Add(this.btnAddNewTemplateWS);
            this.panelControlWorkSheet.Controls.Add(this.btnQuickRestoreWindow);
            this.panelControlWorkSheet.Controls.Add(this.xtraTabControlWorkSheet);
            this.panelControlWorkSheet.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.panelControlWorkSheet.Location = new System.Drawing.Point(20, 714);
            this.panelControlWorkSheet.Name = "panelControlWorkSheet";
            this.panelControlWorkSheet.Size = new System.Drawing.Size(726, 25);
            this.panelControlWorkSheet.TabIndex = 40;
            // 
            // btnAddNewTemplateWS
            // 
            this.btnAddNewTemplateWS.Dock = System.Windows.Forms.DockStyle.Right;
            this.btnAddNewTemplateWS.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.btnAddNewTemplateWS.Location = new System.Drawing.Point(574, 2);
            this.btnAddNewTemplateWS.Name = "btnAddNewTemplateWS";
            this.btnAddNewTemplateWS.Size = new System.Drawing.Size(75, 21);
            this.btnAddNewTemplateWS.TabIndex = 3;
            this.btnAddNewTemplateWS.Text = "新建工作台";
            this.btnAddNewTemplateWS.Click += new System.EventHandler(this.btnAddNewTemplateWS_Click);
            // 
            // btnQuickRestoreWindow
            // 
            this.btnQuickRestoreWindow.Dock = System.Windows.Forms.DockStyle.Right;
            this.btnQuickRestoreWindow.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.btnQuickRestoreWindow.Location = new System.Drawing.Point(649, 2);
            this.btnQuickRestoreWindow.Name = "btnQuickRestoreWindow";
            this.btnQuickRestoreWindow.Size = new System.Drawing.Size(75, 21);
            this.btnQuickRestoreWindow.TabIndex = 2;
            this.btnQuickRestoreWindow.Text = "还原窗口↑";
            this.btnQuickRestoreWindow.Click += new System.EventHandler(this.btnQuickRestoreWindow_Click);
            // 
            // xtraTabControlWorkSheet
            // 
            this.xtraTabControlWorkSheet.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.xtraTabControlWorkSheet.Appearance.BackColor = System.Drawing.Color.Transparent;
            this.xtraTabControlWorkSheet.Appearance.ForeColor = System.Drawing.SystemColors.ActiveCaption;
            this.xtraTabControlWorkSheet.Appearance.Options.UseBackColor = true;
            this.xtraTabControlWorkSheet.Appearance.Options.UseForeColor = true;
            this.xtraTabControlWorkSheet.ContextMenuStrip = this.contextMenuStripTabControlWorkSpace;
            this.xtraTabControlWorkSheet.HeaderLocation = DevExpress.XtraTab.TabHeaderLocation.Bottom;
            this.xtraTabControlWorkSheet.Location = new System.Drawing.Point(-1, 1);
            this.xtraTabControlWorkSheet.Name = "xtraTabControlWorkSheet";
            this.xtraTabControlWorkSheet.Size = new System.Drawing.Size(578, 24);
            this.xtraTabControlWorkSheet.TabIndex = 0;
            this.xtraTabControlWorkSheet.Deselected += new DevExpress.XtraTab.TabPageEventHandler(this.xtraTabControlWorkSheet_Deselected);
            this.xtraTabControlWorkSheet.Selected += new DevExpress.XtraTab.TabPageEventHandler(this.xtraTabControlWorkSheet_Selected);
            this.xtraTabControlWorkSheet.MouseDown += new System.Windows.Forms.MouseEventHandler(this.xtraTabControlWorkSheet_MouseDown);
            // 
            // bgWorkerFire
            // 
            this.bgWorkerFire.WorkerReportsProgress = true;
            this.bgWorkerFire.DoWork += new System.ComponentModel.DoWorkEventHandler(this.bgWorkerFire_DoWork);
            this.bgWorkerFire.ProgressChanged += new System.ComponentModel.ProgressChangedEventHandler(this.bgWorkerFire_ProgressChanged);
            // 
            // trackBarReplay
            // 
            this.trackBarReplay.Dock = System.Windows.Forms.DockStyle.Top;
            this.trackBarReplay.EditValue = null;
            this.trackBarReplay.Location = new System.Drawing.Point(20, 148);
            this.trackBarReplay.MenuManager = this.ribbon;
            this.trackBarReplay.Name = "trackBarReplay";
            this.trackBarReplay.Properties.AutoSize = false;
            this.trackBarReplay.Size = new System.Drawing.Size(726, 22);
            this.trackBarReplay.TabIndex = 48;
            this.trackBarReplay.EditValueChanged += new System.EventHandler(this.trackBarReplay_EditValueChanged);
            // 
            // lbSvCount
            // 
            this.lbSvCount.AutoSize = true;
            this.lbSvCount.BackColor = System.Drawing.Color.Transparent;
            this.lbSvCount.Font = new System.Drawing.Font("宋体", 8.25F);
            this.lbSvCount.ForeColor = System.Drawing.SystemColors.InfoText;
            this.lbSvCount.Location = new System.Drawing.Point(59, 7);
            this.lbSvCount.Name = "lbSvCount";
            this.lbSvCount.Size = new System.Drawing.Size(23, 11);
            this.lbSvCount.TabIndex = 5;
            this.lbSvCount.Text = "[0]";
            this.toolTip.SetToolTip(this.lbSvCount, "已选择个数");
            // 
            // lbProjCount
            // 
            this.lbProjCount.AutoSize = true;
            this.lbProjCount.BackColor = System.Drawing.Color.Transparent;
            this.lbProjCount.Font = new System.Drawing.Font("宋体", 8.25F);
            this.lbProjCount.ForeColor = System.Drawing.SystemColors.InfoText;
            this.lbProjCount.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.lbProjCount.Location = new System.Drawing.Point(59, 7);
            this.lbProjCount.Name = "lbProjCount";
            this.lbProjCount.Size = new System.Drawing.Size(23, 11);
            this.lbProjCount.TabIndex = 4;
            this.lbProjCount.Text = "[0]";
            this.toolTip.SetToolTip(this.lbProjCount, "已选择个数");
            // 
            // lblCityCount
            // 
            this.lblCityCount.AutoSize = true;
            this.lblCityCount.BackColor = System.Drawing.Color.Transparent;
            this.lblCityCount.Font = new System.Drawing.Font("宋体", 8.25F);
            this.lblCityCount.ForeColor = System.Drawing.SystemColors.InfoText;
            this.lblCityCount.Location = new System.Drawing.Point(33, 7);
            this.lblCityCount.Name = "lblCityCount";
            this.lblCityCount.Size = new System.Drawing.Size(23, 11);
            this.lblCityCount.TabIndex = 2;
            this.lblCityCount.Text = "[1]";
            this.toolTip.SetToolTip(this.lblCityCount, "已选择个数");
            // 
            // labelDeviceCount
            // 
            this.labelDeviceCount.AutoSize = true;
            this.labelDeviceCount.BackColor = System.Drawing.Color.Transparent;
            this.labelDeviceCount.Font = new System.Drawing.Font("宋体", 8.25F);
            this.labelDeviceCount.ForeColor = System.Drawing.SystemColors.ActiveCaptionText;
            this.labelDeviceCount.Location = new System.Drawing.Point(33, 7);
            this.labelDeviceCount.Name = "labelDeviceCount";
            this.labelDeviceCount.Size = new System.Drawing.Size(23, 11);
            this.labelDeviceCount.TabIndex = 2;
            this.labelDeviceCount.Text = "[1]";
            this.toolTip.SetToolTip(this.labelDeviceCount, "已选择个数");
            // 
            // textBoxFileName
            // 
            this.textBoxFileName.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.textBoxFileName.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.textBoxFileName.Enabled = false;
            this.textBoxFileName.Location = new System.Drawing.Point(5, 41);
            this.textBoxFileName.Name = "textBoxFileName";
            this.textBoxFileName.Size = new System.Drawing.Size(241, 21);
            this.textBoxFileName.TabIndex = 4;
            this.toolTip.SetToolTip(this.textBoxFileName, "支持按文件名模糊匹配文件，多个文件名时请用“ or ”隔开（不带双引号）");
            // 
            // dockManager
            // 
            this.dockManager.AutoHideContainers.AddRange(new DevExpress.XtraBars.Docking.AutoHideContainer[] {
            this.hideContainerLeft});
            this.dockManager.AutoHideSpeed = 100000;
            this.dockManager.Controller = this.barAndDockingController;
            this.dockManager.DockModeVS2005FadeSpeed = 500000;
            this.dockManager.Form = this;
            this.dockManager.RootPanels.AddRange(new DevExpress.XtraBars.Docking.DockPanel[] {
            this.pnlCondAndLegend});
            this.dockManager.TopZIndexControls.AddRange(new string[] {
            "DevExpress.XtraBars.BarDockControl",
            "DevExpress.XtraBars.StandaloneBarDockControl",
            "System.Windows.Forms.StatusBar",
            "DevExpress.XtraBars.Ribbon.RibbonStatusBar",
            "DevExpress.XtraBars.Ribbon.RibbonControl"});
            // 
            // hideContainerLeft
            // 
            this.hideContainerLeft.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(207)))), ((int)(((byte)(221)))), ((int)(((byte)(238)))));
            this.hideContainerLeft.Controls.Add(this.dockPanelNavi);
            this.hideContainerLeft.Dock = System.Windows.Forms.DockStyle.Left;
            this.hideContainerLeft.Location = new System.Drawing.Point(0, 148);
            this.hideContainerLeft.Name = "hideContainerLeft";
            this.hideContainerLeft.Size = new System.Drawing.Size(20, 591);
            // 
            // dockPanelNavi
            // 
            this.dockPanelNavi.Controls.Add(this.ctrlContainerNavi);
            this.dockPanelNavi.Dock = DevExpress.XtraBars.Docking.DockingStyle.Left;
            this.dockPanelNavi.ID = new System.Guid("b245068a-1f67-4645-889c-daf130e2ec98");
            this.dockPanelNavi.Location = new System.Drawing.Point(0, 0);
            this.dockPanelNavi.Name = "dockPanelNavi";
            this.dockPanelNavi.Options.ShowCloseButton = false;
            this.dockPanelNavi.OriginalSize = new System.Drawing.Size(200, 200);
            this.dockPanelNavi.SavedDock = DevExpress.XtraBars.Docking.DockingStyle.Left;
            this.dockPanelNavi.SavedIndex = 1;
            this.dockPanelNavi.Size = new System.Drawing.Size(200, 591);
            this.dockPanelNavi.Text = "导航";
            this.dockPanelNavi.Visibility = DevExpress.XtraBars.Docking.DockVisibility.AutoHide;
            // 
            // ctrlContainerNavi
            // 
            this.ctrlContainerNavi.Controls.Add(this.xtraTabControlNavigator);
            this.ctrlContainerNavi.Location = new System.Drawing.Point(4, 25);
            this.ctrlContainerNavi.Name = "ctrlContainerNavi";
            this.ctrlContainerNavi.Size = new System.Drawing.Size(192, 562);
            this.ctrlContainerNavi.TabIndex = 0;
            // 
            // xtraTabControlNavigator
            // 
            this.xtraTabControlNavigator.AppearancePage.Header.Options.UseTextOptions = true;
            this.xtraTabControlNavigator.AppearancePage.Header.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.xtraTabControlNavigator.Dock = System.Windows.Forms.DockStyle.Fill;
            this.xtraTabControlNavigator.HeaderAutoFill = DevExpress.Utils.DefaultBoolean.True;
            this.xtraTabControlNavigator.HeaderLocation = DevExpress.XtraTab.TabHeaderLocation.Bottom;
            this.xtraTabControlNavigator.Location = new System.Drawing.Point(0, 0);
            this.xtraTabControlNavigator.Name = "xtraTabControlNavigator";
            this.xtraTabControlNavigator.SelectedTabPage = this.tabPageSpace;
            this.xtraTabControlNavigator.Size = new System.Drawing.Size(192, 562);
            this.xtraTabControlNavigator.TabIndex = 1;
            this.xtraTabControlNavigator.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.tabPageMenu,
            this.tabPageParam,
            this.tabPageSpace});
            // 
            // tabPageSpace
            // 
            this.tabPageSpace.Controls.Add(this.xtraTabControlWks);
            this.tabPageSpace.Name = "tabPageSpace";
            this.tabPageSpace.Size = new System.Drawing.Size(184, 532);
            this.tabPageSpace.Text = "工作空间";
            // 
            // xtraTabControlWks
            // 
            this.xtraTabControlWks.Dock = System.Windows.Forms.DockStyle.Fill;
            this.xtraTabControlWks.Location = new System.Drawing.Point(0, 0);
            this.xtraTabControlWks.Name = "xtraTabControlWks";
            this.xtraTabControlWks.SelectedTabPage = this.xtraTabPageWksNavi;
            this.xtraTabControlWks.Size = new System.Drawing.Size(184, 532);
            this.xtraTabControlWks.TabIndex = 1;
            this.xtraTabControlWks.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.xtraTabPageWksNavi,
            this.xtraTabPageWksTemplates});
            // 
            // xtraTabPageWksNavi
            // 
            this.xtraTabPageWksNavi.Controls.Add(this.treeViewWorkSpace);
            this.xtraTabPageWksNavi.Name = "xtraTabPageWksNavi";
            this.xtraTabPageWksNavi.Size = new System.Drawing.Size(176, 502);
            this.xtraTabPageWksNavi.Text = "视图";
            // 
            // treeViewWorkSpace
            // 
            this.treeViewWorkSpace.Dock = System.Windows.Forms.DockStyle.Fill;
            this.treeViewWorkSpace.HideSelection = false;
            this.treeViewWorkSpace.ImageIndex = 0;
            this.treeViewWorkSpace.ImageList = this.imageListWorkSpace;
            this.treeViewWorkSpace.Location = new System.Drawing.Point(0, 0);
            this.treeViewWorkSpace.Name = "treeViewWorkSpace";
            this.treeViewWorkSpace.SelectedImageIndex = 0;
            this.treeViewWorkSpace.Size = new System.Drawing.Size(176, 502);
            this.treeViewWorkSpace.TabIndex = 0;
            this.treeViewWorkSpace.DoubleClick += new System.EventHandler(this.treeViewWorkSpace_DoubleClick);
            this.treeViewWorkSpace.MouseClick += new System.Windows.Forms.MouseEventHandler(this.treeViewWorkSpace_MouseClick);
            this.treeViewWorkSpace.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.treeViewWorkSpace_MouseDown);
            // 
            // xtraTabPageWksTemplates
            // 
            this.xtraTabPageWksTemplates.Controls.Add(this.treeViewTemplateWS);
            this.xtraTabPageWksTemplates.Name = "xtraTabPageWksTemplates";
            this.xtraTabPageWksTemplates.Size = new System.Drawing.Size(176, 502);
            this.xtraTabPageWksTemplates.Text = "模板";
            // 
            // treeViewTemplateWS
            // 
            this.treeViewTemplateWS.Dock = System.Windows.Forms.DockStyle.Fill;
            this.treeViewTemplateWS.HideSelection = false;
            this.treeViewTemplateWS.ImageIndex = 0;
            this.treeViewTemplateWS.ImageList = this.imageListWorkSpace;
            this.treeViewTemplateWS.Location = new System.Drawing.Point(0, 0);
            this.treeViewTemplateWS.Name = "treeViewTemplateWS";
            this.treeViewTemplateWS.SelectedImageIndex = 0;
            this.treeViewTemplateWS.Size = new System.Drawing.Size(176, 502);
            this.treeViewTemplateWS.TabIndex = 1;
            this.treeViewTemplateWS.DoubleClick += new System.EventHandler(this.treeViewWksTemplate_DoubleClick);
            // 
            // tabPageMenu
            // 
            this.tabPageMenu.Controls.Add(this.treeViewMenuItem);
            this.tabPageMenu.Name = "tabPageMenu";
            this.tabPageMenu.Size = new System.Drawing.Size(184, 532);
            this.tabPageMenu.Text = "菜单项";
            // 
            // treeViewMenuItem
            // 
            this.treeViewMenuItem.Dock = System.Windows.Forms.DockStyle.Fill;
            this.treeViewMenuItem.ImageIndex = 0;
            this.treeViewMenuItem.ImageList = this.imageListMenuItem;
            this.treeViewMenuItem.Location = new System.Drawing.Point(0, 0);
            this.treeViewMenuItem.Margin = new System.Windows.Forms.Padding(1);
            this.treeViewMenuItem.Name = "treeViewMenuItem";
            this.treeViewMenuItem.SelectedImageIndex = 0;
            this.treeViewMenuItem.Size = new System.Drawing.Size(184, 532);
            this.treeViewMenuItem.TabIndex = 0;
            this.treeViewMenuItem.AfterCollapse += new System.Windows.Forms.TreeViewEventHandler(this.treeViewMenuItem_AfterCollapse);
            this.treeViewMenuItem.AfterExpand += new System.Windows.Forms.TreeViewEventHandler(this.treeViewMenuItem_AfterExpand);
            this.treeViewMenuItem.DoubleClick += new System.EventHandler(this.treeViewMenuItem_DoubleClick);
            this.treeViewMenuItem.MouseClick += new System.Windows.Forms.MouseEventHandler(this.treeViewMenuItem_MouseClick);
            this.treeViewMenuItem.MouseDown += new System.Windows.Forms.MouseEventHandler(this.treeViewMenuItem_MouseDown);
            // 
            // tabPageParam
            // 
            this.tabPageParam.Controls.Add(this.xtraTabControlChildVisibleParam);
            this.tabPageParam.Name = "tabPageParam";
            this.tabPageParam.Size = new System.Drawing.Size(184, 532);
            this.tabPageParam.Text = "可视单元";
            // 
            // xtraTabControlChildVisibleParam
            // 
            this.xtraTabControlChildVisibleParam.Dock = System.Windows.Forms.DockStyle.Fill;
            this.xtraTabControlChildVisibleParam.Location = new System.Drawing.Point(0, 0);
            this.xtraTabControlChildVisibleParam.Name = "xtraTabControlChildVisibleParam";
            this.xtraTabControlChildVisibleParam.SelectedTabPage = this.xtraTabPageSignalParam;
            this.xtraTabControlChildVisibleParam.Size = new System.Drawing.Size(184, 532);
            this.xtraTabControlChildVisibleParam.TabIndex = 60;
            this.xtraTabControlChildVisibleParam.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.xtraTabPageSignalParam,
            this.xtraTabPageEventParam});
            // 
            // xtraTabPageSignalParam
            // 
            this.xtraTabPageSignalParam.Controls.Add(this.treeViewVisibleParam);
            this.xtraTabPageSignalParam.Controls.Add(this.pnlParam);
            this.xtraTabPageSignalParam.Name = "xtraTabPageSignalParam";
            this.xtraTabPageSignalParam.Size = new System.Drawing.Size(176, 502);
            this.xtraTabPageSignalParam.Text = "参数单元";
            // 
            // treeViewVisibleParam
            // 
            this.treeViewVisibleParam.ContextMenuStrip = this.contextMenuStripTreeView;
            this.treeViewVisibleParam.Dock = System.Windows.Forms.DockStyle.Fill;
            this.treeViewVisibleParam.ImageIndex = 0;
            this.treeViewVisibleParam.ImageList = this.imageListVisibleParam;
            this.treeViewVisibleParam.Location = new System.Drawing.Point(0, 0);
            this.treeViewVisibleParam.Name = "treeViewVisibleParam";
            this.treeViewVisibleParam.SelectedImageIndex = 0;
            this.treeViewVisibleParam.Size = new System.Drawing.Size(176, 473);
            this.treeViewVisibleParam.TabIndex = 0;
            this.treeViewVisibleParam.AfterLabelEdit += new System.Windows.Forms.NodeLabelEditEventHandler(this.treeViewVisibleParam_AfterLabelEdit);
            this.treeViewVisibleParam.ItemDrag += new System.Windows.Forms.ItemDragEventHandler(this.treeViewVisibleParam_ItemDrag);
            this.treeViewVisibleParam.AfterSelect += new System.Windows.Forms.TreeViewEventHandler(this.treeViewVisibleParam_AfterSelect);
            this.treeViewVisibleParam.DoubleClick += new System.EventHandler(this.treeViewVisibleParam_DoubleClick);
            this.treeViewVisibleParam.MouseClick += new System.Windows.Forms.MouseEventHandler(this.treeViewVisibleParam_MouseClick);
            this.treeViewVisibleParam.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.treeViewVisibleParam_MouseDown);
            // 
            // pnlParam
            // 
            this.pnlParam.Controls.Add(this.labelControl1);
            this.pnlParam.Controls.Add(this.btnVisibleParamNext);
            this.pnlParam.Controls.Add(this.txtVisibleParamSearch);
            this.pnlParam.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.pnlParam.Location = new System.Drawing.Point(0, 473);
            this.pnlParam.Name = "pnlParam";
            this.pnlParam.Size = new System.Drawing.Size(176, 29);
            this.pnlParam.TabIndex = 1;
            // 
            // labelControl1
            // 
            this.labelControl1.Location = new System.Drawing.Point(3, 7);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(24, 14);
            this.labelControl1.TabIndex = 1;
            this.labelControl1.Text = "搜索";
            // 
            // btnVisibleParamNext
            // 
            this.btnVisibleParamNext.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnVisibleParamNext.Image = ((System.Drawing.Image)(resources.GetObject("btnVisibleParamNext.Image")));
            this.btnVisibleParamNext.Location = new System.Drawing.Point(144, 4);
            this.btnVisibleParamNext.Name = "btnVisibleParamNext";
            this.btnVisibleParamNext.Size = new System.Drawing.Size(27, 21);
            this.btnVisibleParamNext.TabIndex = 2;
            this.btnVisibleParamNext.ToolTip = "下一个";
            this.btnVisibleParamNext.Click += new System.EventHandler(this.btnVisibleParamNext_Click);
            // 
            // txtVisibleParamSearch
            // 
            this.txtVisibleParamSearch.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.txtVisibleParamSearch.Location = new System.Drawing.Point(31, 4);
            this.txtVisibleParamSearch.MenuManager = this.ribbon;
            this.txtVisibleParamSearch.Name = "txtVisibleParamSearch";
            this.txtVisibleParamSearch.Size = new System.Drawing.Size(107, 21);
            this.txtVisibleParamSearch.TabIndex = 0;
            this.txtVisibleParamSearch.KeyUp += new System.Windows.Forms.KeyEventHandler(this.txtVisibleParam_KeyUp);
            // 
            // xtraTabPageEventParam
            // 
            this.xtraTabPageEventParam.Controls.Add(this.treeViewVisibleEvent);
            this.xtraTabPageEventParam.Controls.Add(this.pnlEventParam);
            this.xtraTabPageEventParam.Name = "xtraTabPageEventParam";
            this.xtraTabPageEventParam.Size = new System.Drawing.Size(176, 502);
            this.xtraTabPageEventParam.Text = "事件单元";
            // 
            // treeViewVisibleEvent
            // 
            this.treeViewVisibleEvent.Dock = System.Windows.Forms.DockStyle.Fill;
            this.treeViewVisibleEvent.ImageIndex = 0;
            this.treeViewVisibleEvent.ImageList = this.imageListVisibleEvent;
            this.treeViewVisibleEvent.Location = new System.Drawing.Point(0, 0);
            this.treeViewVisibleEvent.Name = "treeViewVisibleEvent";
            this.treeViewVisibleEvent.SelectedImageIndex = 0;
            this.treeViewVisibleEvent.Size = new System.Drawing.Size(176, 473);
            this.treeViewVisibleEvent.TabIndex = 1;
            this.treeViewVisibleEvent.ItemDrag += new System.Windows.Forms.ItemDragEventHandler(this.treeViewVisibleEvent_ItemDrag);
            this.treeViewVisibleEvent.DoubleClick += new System.EventHandler(this.treeViewVisibleEvent_DoubleClick);
            // 
            // pnlEventParam
            // 
            this.pnlEventParam.Controls.Add(this.labelControl3);
            this.pnlEventParam.Controls.Add(this.btnSearchEvent);
            this.pnlEventParam.Controls.Add(this.textEditSearchEvent);
            this.pnlEventParam.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.pnlEventParam.Location = new System.Drawing.Point(0, 473);
            this.pnlEventParam.Name = "pnlEventParam";
            this.pnlEventParam.Size = new System.Drawing.Size(176, 29);
            this.pnlEventParam.TabIndex = 2;
            // 
            // labelControl3
            // 
            this.labelControl3.Location = new System.Drawing.Point(3, 7);
            this.labelControl3.Name = "labelControl3";
            this.labelControl3.Size = new System.Drawing.Size(24, 14);
            this.labelControl3.TabIndex = 1;
            this.labelControl3.Text = "搜索";
            // 
            // btnSearchEvent
            // 
            this.btnSearchEvent.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnSearchEvent.Image = ((System.Drawing.Image)(resources.GetObject("btnSearchEvent.Image")));
            this.btnSearchEvent.Location = new System.Drawing.Point(144, 4);
            this.btnSearchEvent.Name = "btnSearchEvent";
            this.btnSearchEvent.Size = new System.Drawing.Size(27, 21);
            this.btnSearchEvent.TabIndex = 2;
            this.btnSearchEvent.ToolTip = "下一个";
            this.btnSearchEvent.Click += new System.EventHandler(this.btnSearchEvent_Click);
            // 
            // textEditSearchEvent
            // 
            this.textEditSearchEvent.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.textEditSearchEvent.Location = new System.Drawing.Point(31, 4);
            this.textEditSearchEvent.MenuManager = this.ribbon;
            this.textEditSearchEvent.Name = "textEditSearchEvent";
            this.textEditSearchEvent.Size = new System.Drawing.Size(107, 21);
            this.textEditSearchEvent.TabIndex = 0;
            // 
            // pnlCondAndLegend
            // 
            this.pnlCondAndLegend.ActiveChild = this.dockPanelQueryCond;
            this.pnlCondAndLegend.Controls.Add(this.dockPanelQueryCond);
            this.pnlCondAndLegend.Controls.Add(this.dockPanelLegend);
            this.pnlCondAndLegend.Dock = DevExpress.XtraBars.Docking.DockingStyle.Right;
            this.pnlCondAndLegend.FloatSize = new System.Drawing.Size(284, 262);
            this.pnlCondAndLegend.FloatVertical = true;
            this.pnlCondAndLegend.ID = new System.Guid("962eb50f-17bf-47ab-832e-f060eab75b6d");
            this.pnlCondAndLegend.Location = new System.Drawing.Point(746, 148);
            this.pnlCondAndLegend.Name = "pnlCondAndLegend";
            this.pnlCondAndLegend.OriginalSize = new System.Drawing.Size(274, 200);
            this.pnlCondAndLegend.Size = new System.Drawing.Size(274, 591);
            this.pnlCondAndLegend.Tabbed = true;
            this.pnlCondAndLegend.TabsPosition = DevExpress.XtraBars.Docking.TabsPosition.Top;
            this.pnlCondAndLegend.TabsScroll = true;
            this.pnlCondAndLegend.Text = "panelContainer2";
            // 
            // dockPanelQueryCond
            // 
            this.dockPanelQueryCond.Appearance.Options.UseTextOptions = true;
            this.dockPanelQueryCond.Appearance.TextOptions.Trimming = DevExpress.Utils.Trimming.None;
            this.dockPanelQueryCond.Controls.Add(this.dockPanel1_Container);
            this.dockPanelQueryCond.Dock = DevExpress.XtraBars.Docking.DockingStyle.Fill;
            this.dockPanelQueryCond.FloatSize = new System.Drawing.Size(284, 262);
            this.dockPanelQueryCond.FloatVertical = true;
            this.dockPanelQueryCond.ID = new System.Guid("22355c5a-b9e9-49b4-9345-4ea2081676d3");
            this.dockPanelQueryCond.Location = new System.Drawing.Point(4, 53);
            this.dockPanelQueryCond.Name = "dockPanelQueryCond";
            this.dockPanelQueryCond.Options.ShowCloseButton = false;
            this.dockPanelQueryCond.OriginalSize = new System.Drawing.Size(266, 534);
            this.dockPanelQueryCond.Size = new System.Drawing.Size(266, 534);
            this.dockPanelQueryCond.TabsPosition = DevExpress.XtraBars.Docking.TabsPosition.Top;
            this.dockPanelQueryCond.TabsScroll = true;
            this.dockPanelQueryCond.TabText = "检索";
            this.dockPanelQueryCond.Text = "检索条件";
            // 
            // dockPanel1_Container
            // 
            this.dockPanel1_Container.Controls.Add(this.scrollCtrlCondition);
            this.dockPanel1_Container.Location = new System.Drawing.Point(0, 0);
            this.dockPanel1_Container.Name = "dockPanel1_Container";
            this.dockPanel1_Container.Size = new System.Drawing.Size(266, 534);
            this.dockPanel1_Container.TabIndex = 0;
            // 
            // scrollCtrlCondition
            // 
            this.scrollCtrlCondition.Appearance.BackColor = System.Drawing.Color.Transparent;
            this.scrollCtrlCondition.Appearance.Options.UseBackColor = true;
            this.scrollCtrlCondition.Controls.Add(this.grpCtrlCarrier);
            this.scrollCtrlCondition.Controls.Add(this.grpCtrlDaiwei);
            this.scrollCtrlCondition.Controls.Add(this.grpCtrlRegion);
            this.scrollCtrlCondition.Controls.Add(this.grpCtrlDevice);
            this.scrollCtrlCondition.Controls.Add(this.grpCtrlFileFilter);
            this.scrollCtrlCondition.Controls.Add(this.grpCtrlPrjType);
            this.scrollCtrlCondition.Controls.Add(this.grpCtrlDataSrc);
            this.scrollCtrlCondition.Controls.Add(this.grpCtlCitys);
            this.scrollCtrlCondition.Controls.Add(this.grpCtrlPeriod);
            this.scrollCtrlCondition.Controls.Add(this.grpCtrlDiyCond);
            this.scrollCtrlCondition.Dock = System.Windows.Forms.DockStyle.Fill;
            this.scrollCtrlCondition.Location = new System.Drawing.Point(0, 0);
            this.scrollCtrlCondition.Name = "scrollCtrlCondition";
            this.scrollCtrlCondition.Size = new System.Drawing.Size(266, 534);
            this.scrollCtrlCondition.TabIndex = 62;
            // 
            // grpCtrlCarrier
            // 
            this.grpCtrlCarrier.Controls.Add(this.listViewCarrier);
            this.grpCtrlCarrier.Dock = System.Windows.Forms.DockStyle.Top;
            this.grpCtrlCarrier.Location = new System.Drawing.Point(0, 666);
            this.grpCtrlCarrier.Name = "grpCtrlCarrier";
            this.grpCtrlCarrier.Size = new System.Drawing.Size(249, 52);
            this.grpCtrlCarrier.TabIndex = 12;
            this.grpCtrlCarrier.Text = "运营商";
            // 
            // listViewCarrier
            // 
            this.listViewCarrier.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.listViewCarrier.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(207)))), ((int)(((byte)(221)))), ((int)(((byte)(238)))));
            this.listViewCarrier.CheckBoxes = true;
            this.listViewCarrier.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.columnHeader4});
            this.listViewCarrier.Location = new System.Drawing.Point(2, 26);
            this.listViewCarrier.Name = "listViewCarrier";
            this.listViewCarrier.Scrollable = false;
            this.listViewCarrier.Size = new System.Drawing.Size(245, 24);
            this.listViewCarrier.TabIndex = 1;
            this.listViewCarrier.UseCompatibleStateImageBehavior = false;
            this.listViewCarrier.View = System.Windows.Forms.View.List;
            // 
            // columnHeader4
            // 
            this.columnHeader4.Width = 80;
            // 
            // grpCtrlDaiwei
            // 
            this.grpCtrlDaiwei.Controls.Add(this.lbagCount);
            this.grpCtrlDaiwei.Controls.Add(this.btnPopupAgent);
            this.grpCtrlDaiwei.Controls.Add(this.listViewAgent);
            this.grpCtrlDaiwei.Dock = System.Windows.Forms.DockStyle.Top;
            this.grpCtrlDaiwei.Location = new System.Drawing.Point(0, 568);
            this.grpCtrlDaiwei.Name = "grpCtrlDaiwei";
            this.grpCtrlDaiwei.Size = new System.Drawing.Size(249, 98);
            this.grpCtrlDaiwei.TabIndex = 11;
            this.grpCtrlDaiwei.Text = "代维";
            // 
            // lbagCount
            // 
            this.lbagCount.BackColor = System.Drawing.Color.Transparent;
            this.lbagCount.ForeColor = System.Drawing.SystemColors.ActiveCaptionText;
            this.lbagCount.Location = new System.Drawing.Point(40, 6);
            this.lbagCount.Name = "lbagCount";
            this.lbagCount.Size = new System.Drawing.Size(42, 14);
            this.lbagCount.TabIndex = 0;
            // 
            // btnPopupAgent
            // 
            this.btnPopupAgent.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnPopupAgent.Location = new System.Drawing.Point(187, 0);
            this.btnPopupAgent.Name = "btnPopupAgent";
            this.btnPopupAgent.Size = new System.Drawing.Size(59, 21);
            this.btnPopupAgent.TabIndex = 1;
            this.btnPopupAgent.Text = "请选择↓";
            this.btnPopupAgent.Click += new System.EventHandler(this.btnPopupAgent_Click);
            // 
            // listViewAgent
            // 
            this.listViewAgent.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            columnHeader3});
            this.listViewAgent.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listViewAgent.HeaderStyle = System.Windows.Forms.ColumnHeaderStyle.None;
            this.listViewAgent.Location = new System.Drawing.Point(2, 23);
            this.listViewAgent.Name = "listViewAgent";
            this.listViewAgent.Size = new System.Drawing.Size(245, 73);
            this.listViewAgent.TabIndex = 0;
            this.listViewAgent.UseCompatibleStateImageBehavior = false;
            this.listViewAgent.View = System.Windows.Forms.View.Details;
            // 
            // grpCtrlRegion
            // 
            this.grpCtrlRegion.Controls.Add(this.treeListArea);
            this.grpCtrlRegion.Dock = System.Windows.Forms.DockStyle.Top;
            this.grpCtrlRegion.Location = new System.Drawing.Point(0, 473);
            this.grpCtrlRegion.Name = "grpCtrlRegion";
            this.grpCtrlRegion.Size = new System.Drawing.Size(249, 95);
            this.grpCtrlRegion.TabIndex = 10;
            this.grpCtrlRegion.Text = "区域";
            // 
            // treeListArea
            // 
            this.treeListArea.Appearance.Empty.BackColor = System.Drawing.Color.Ivory;
            this.treeListArea.Appearance.Empty.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(255)))));
            this.treeListArea.Appearance.Empty.Options.UseBackColor = true;
            this.treeListArea.Appearance.Empty.Options.UseForeColor = true;
            this.treeListArea.Appearance.EvenRow.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(253)))), ((int)(((byte)(245)))), ((int)(((byte)(230)))));
            this.treeListArea.Appearance.EvenRow.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(0)))));
            this.treeListArea.Appearance.EvenRow.Options.UseBackColor = true;
            this.treeListArea.Appearance.EvenRow.Options.UseForeColor = true;
            this.treeListArea.Appearance.FooterPanel.BackColor = System.Drawing.Color.NavajoWhite;
            this.treeListArea.Appearance.FooterPanel.BorderColor = System.Drawing.Color.NavajoWhite;
            this.treeListArea.Appearance.FooterPanel.Font = new System.Drawing.Font("Tahoma", 8.25F, System.Drawing.FontStyle.Bold);
            this.treeListArea.Appearance.FooterPanel.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(0)))));
            this.treeListArea.Appearance.FooterPanel.Options.UseBackColor = true;
            this.treeListArea.Appearance.FooterPanel.Options.UseBorderColor = true;
            this.treeListArea.Appearance.FooterPanel.Options.UseFont = true;
            this.treeListArea.Appearance.FooterPanel.Options.UseForeColor = true;
            this.treeListArea.Appearance.GroupButton.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(224)))), ((int)(((byte)(192)))));
            this.treeListArea.Appearance.GroupButton.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(224)))), ((int)(((byte)(192)))));
            this.treeListArea.Appearance.GroupButton.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(0)))));
            this.treeListArea.Appearance.GroupButton.Options.UseBackColor = true;
            this.treeListArea.Appearance.GroupButton.Options.UseBorderColor = true;
            this.treeListArea.Appearance.GroupButton.Options.UseForeColor = true;
            this.treeListArea.Appearance.GroupFooter.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(232)))), ((int)(((byte)(194)))), ((int)(((byte)(145)))));
            this.treeListArea.Appearance.GroupFooter.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(232)))), ((int)(((byte)(194)))), ((int)(((byte)(145)))));
            this.treeListArea.Appearance.GroupFooter.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(0)))));
            this.treeListArea.Appearance.GroupFooter.Options.UseBackColor = true;
            this.treeListArea.Appearance.GroupFooter.Options.UseBorderColor = true;
            this.treeListArea.Appearance.GroupFooter.Options.UseForeColor = true;
            this.treeListArea.Appearance.HeaderPanel.BackColor = System.Drawing.Color.BurlyWood;
            this.treeListArea.Appearance.HeaderPanel.BorderColor = System.Drawing.Color.BurlyWood;
            this.treeListArea.Appearance.HeaderPanel.Font = new System.Drawing.Font("Tahoma", 8.25F, System.Drawing.FontStyle.Bold);
            this.treeListArea.Appearance.HeaderPanel.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(0)))));
            this.treeListArea.Appearance.HeaderPanel.Options.UseBackColor = true;
            this.treeListArea.Appearance.HeaderPanel.Options.UseBorderColor = true;
            this.treeListArea.Appearance.HeaderPanel.Options.UseFont = true;
            this.treeListArea.Appearance.HeaderPanel.Options.UseForeColor = true;
            this.treeListArea.Appearance.HideSelectionRow.BackColor = System.Drawing.Color.White;
            this.treeListArea.Appearance.HideSelectionRow.Options.UseBackColor = true;
            this.treeListArea.Appearance.HorzLine.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(224)))), ((int)(((byte)(224)))), ((int)(((byte)(224)))));
            this.treeListArea.Appearance.HorzLine.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(224)))), ((int)(((byte)(224)))), ((int)(((byte)(224)))));
            this.treeListArea.Appearance.HorzLine.Options.UseBackColor = true;
            this.treeListArea.Appearance.HorzLine.Options.UseForeColor = true;
            this.treeListArea.Appearance.OddRow.BackColor = System.Drawing.Color.Bisque;
            this.treeListArea.Appearance.OddRow.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(0)))));
            this.treeListArea.Appearance.OddRow.Options.UseBackColor = true;
            this.treeListArea.Appearance.OddRow.Options.UseForeColor = true;
            this.treeListArea.Appearance.Preview.BackColor = System.Drawing.Color.Cornsilk;
            this.treeListArea.Appearance.Preview.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(255)))));
            this.treeListArea.Appearance.Preview.Options.UseBackColor = true;
            this.treeListArea.Appearance.Preview.Options.UseForeColor = true;
            this.treeListArea.Appearance.Preview.Options.UseTextOptions = true;
            this.treeListArea.Appearance.Preview.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Top;
            this.treeListArea.Appearance.Row.BackColor = System.Drawing.Color.Ivory;
            this.treeListArea.Appearance.Row.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(25)))), ((int)(((byte)(25)))), ((int)(((byte)(112)))));
            this.treeListArea.Appearance.Row.Options.UseBackColor = true;
            this.treeListArea.Appearance.Row.Options.UseForeColor = true;
            this.treeListArea.Appearance.SelectedRow.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(10)))), ((int)(((byte)(10)))), ((int)(((byte)(138)))));
            this.treeListArea.Appearance.SelectedRow.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(255)))));
            this.treeListArea.Appearance.SelectedRow.Options.UseBackColor = true;
            this.treeListArea.Appearance.SelectedRow.Options.UseForeColor = true;
            this.treeListArea.Appearance.TreeLine.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(128)))), ((int)(((byte)(128)))));
            this.treeListArea.Appearance.TreeLine.Options.UseForeColor = true;
            this.treeListArea.Appearance.VertLine.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(224)))), ((int)(((byte)(224)))), ((int)(((byte)(224)))));
            this.treeListArea.Appearance.VertLine.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(224)))), ((int)(((byte)(224)))), ((int)(((byte)(224)))));
            this.treeListArea.Appearance.VertLine.Options.UseBackColor = true;
            this.treeListArea.Appearance.VertLine.Options.UseForeColor = true;
            this.treeListArea.Appearance.VertLine.Options.UseTextOptions = true;
            this.treeListArea.Appearance.VertLine.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.treeListArea.BackgroundImage = ((System.Drawing.Image)(resources.GetObject("treeListArea.BackgroundImage")));
            this.treeListArea.Columns.AddRange(new DevExpress.XtraTreeList.Columns.TreeListColumn[] {
            this.colKey});
            this.treeListArea.Dock = System.Windows.Forms.DockStyle.Fill;
            this.treeListArea.Location = new System.Drawing.Point(2, 23);
            this.treeListArea.Name = "treeListArea";
            this.treeListArea.OptionsBehavior.AllowIndeterminateCheckState = true;
            this.treeListArea.OptionsBehavior.AutoChangeParent = false;
            this.treeListArea.OptionsBehavior.AutoNodeHeight = false;
            this.treeListArea.OptionsBehavior.AutoSelectAllInEditor = false;
            this.treeListArea.OptionsBehavior.CloseEditorOnLostFocus = false;
            this.treeListArea.OptionsBehavior.Editable = false;
            this.treeListArea.OptionsBehavior.KeepSelectedOnClick = false;
            this.treeListArea.OptionsBehavior.ResizeNodes = false;
            this.treeListArea.OptionsBehavior.SmartMouseHover = false;
            this.treeListArea.OptionsMenu.EnableFooterMenu = false;
            this.treeListArea.OptionsPrint.PrintHorzLines = false;
            this.treeListArea.OptionsPrint.PrintVertLines = false;
            this.treeListArea.OptionsPrint.UsePrintStyles = true;
            this.treeListArea.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.treeListArea.OptionsView.ShowCheckBoxes = true;
            this.treeListArea.OptionsView.ShowColumns = false;
            this.treeListArea.OptionsView.ShowFocusedFrame = false;
            this.treeListArea.OptionsView.ShowHorzLines = false;
            this.treeListArea.OptionsView.ShowIndicator = false;
            this.treeListArea.OptionsView.ShowVertLines = false;
            this.treeListArea.Size = new System.Drawing.Size(245, 70);
            this.treeListArea.TabIndex = 19;
            // 
            // colKey
            // 
            this.colKey.AllNodesSummary = true;
            this.colKey.Caption = "Registry Keys";
            this.colKey.FieldName = "Key";
            this.colKey.MinWidth = 36;
            this.colKey.Name = "colKey";
            this.colKey.SummaryFooter = DevExpress.XtraTreeList.SummaryItemType.Count;
            this.colKey.SummaryFooterStrFormat = "Count keys = {0}";
            this.colKey.Visible = true;
            this.colKey.VisibleIndex = 0;
            // 
            // grpCtrlDevice
            // 
            this.grpCtrlDevice.Controls.Add(this.labelDeviceCount);
            this.grpCtrlDevice.Controls.Add(this.btnPopupDevice);
            this.grpCtrlDevice.Dock = System.Windows.Forms.DockStyle.Top;
            this.grpCtrlDevice.Location = new System.Drawing.Point(0, 450);
            this.grpCtrlDevice.Name = "grpCtrlDevice";
            this.grpCtrlDevice.Size = new System.Drawing.Size(249, 23);
            this.grpCtrlDevice.TabIndex = 9;
            this.grpCtrlDevice.Text = "设备";
            // 
            // btnPopupDevice
            // 
            this.btnPopupDevice.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnPopupDevice.Location = new System.Drawing.Point(187, 2);
            this.btnPopupDevice.Name = "btnPopupDevice";
            this.btnPopupDevice.Size = new System.Drawing.Size(59, 21);
            this.btnPopupDevice.TabIndex = 0;
            this.btnPopupDevice.Text = "选择设备";
            this.btnPopupDevice.Click += new System.EventHandler(this.btnPopupDevice_Click);
            // 
            // grpCtrlFileFilter
            // 
            this.grpCtrlFileFilter.Controls.Add(this.popupContainer);
            this.grpCtrlFileFilter.Controls.Add(this.textBoxFileName);
            this.grpCtrlFileFilter.Controls.Add(this.checkBoxFileName);
            this.grpCtrlFileFilter.Controls.Add(this.radioGroupFileFilter);
            this.grpCtrlFileFilter.Dock = System.Windows.Forms.DockStyle.Top;
            this.grpCtrlFileFilter.Location = new System.Drawing.Point(0, 383);
            this.grpCtrlFileFilter.Name = "grpCtrlFileFilter";
            this.grpCtrlFileFilter.Size = new System.Drawing.Size(249, 67);
            this.grpCtrlFileFilter.TabIndex = 4;
            // 
            // popupContainer
            // 
            this.popupContainer.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder;
            this.popupContainer.Controls.Add(this.popupContainerSQL);
            this.popupContainer.Location = new System.Drawing.Point(5, 49);
            this.popupContainer.Name = "popupContainer";
            this.popupContainer.Ribbon = this.ribbon;
            this.popupContainer.ShowCloseButton = true;
            this.popupContainer.ShowSizeGrip = true;
            this.popupContainer.Size = new System.Drawing.Size(233, 212);
            this.popupContainer.TabIndex = 11;
            this.popupContainer.Visible = false;
            // 
            // popupContainerSQL
            // 
            this.popupContainerSQL.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder;
            this.popupContainerSQL.Location = new System.Drawing.Point(30, 11);
            this.popupContainerSQL.Name = "popupContainerSQL";
            this.popupContainerSQL.Ribbon = this.ribbon;
            this.popupContainerSQL.ShowCloseButton = true;
            this.popupContainerSQL.ShowSizeGrip = true;
            this.popupContainerSQL.Size = new System.Drawing.Size(600, 270);
            this.popupContainerSQL.TabIndex = 12;
            this.popupContainerSQL.Visible = false;
            // 
            // checkBoxFileName
            // 
            this.checkBoxFileName.AutoSize = true;
            this.checkBoxFileName.BackColor = System.Drawing.Color.Transparent;
            this.checkBoxFileName.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.checkBoxFileName.Location = new System.Drawing.Point(5, 2);
            this.checkBoxFileName.Name = "checkBoxFileName";
            this.checkBoxFileName.Padding = new System.Windows.Forms.Padding(2, 0, 4, 0);
            this.checkBoxFileName.Size = new System.Drawing.Size(78, 16);
            this.checkBoxFileName.TabIndex = 7;
            this.checkBoxFileName.Text = "文件筛选";
            this.checkBoxFileName.UseVisualStyleBackColor = false;
            this.checkBoxFileName.CheckedChanged += new System.EventHandler(this.checkBoxFileName_CheckedChanged);
            // 
            // radioGroupFileFilter
            // 
            this.radioGroupFileFilter.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.radioGroupFileFilter.EditValue = true;
            this.radioGroupFileFilter.Enabled = false;
            this.radioGroupFileFilter.Location = new System.Drawing.Point(-2, 20);
            this.radioGroupFileFilter.Margin = new System.Windows.Forms.Padding(2);
            this.radioGroupFileFilter.MenuManager = this.ribbon;
            this.radioGroupFileFilter.Name = "radioGroupFileFilter";
            this.radioGroupFileFilter.Properties.Appearance.BackColor = System.Drawing.Color.Transparent;
            this.radioGroupFileFilter.Properties.Appearance.Options.UseBackColor = true;
            this.radioGroupFileFilter.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder;
            this.radioGroupFileFilter.Properties.Items.AddRange(new DevExpress.XtraEditors.Controls.RadioGroupItem[] {
            new DevExpress.XtraEditors.Controls.RadioGroupItem(true, "文件名"),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(false, "路径"),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(false, "备注"),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(false, "标记"),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(false, "高级")});
            this.radioGroupFileFilter.Size = new System.Drawing.Size(264, 24);
            this.radioGroupFileFilter.TabIndex = 1;
            this.radioGroupFileFilter.SelectedIndexChanged += new System.EventHandler(this.radioGroupFileFilter_SelectedIndexChanged);
            this.radioGroupFileFilter.MouseClick += new System.Windows.Forms.MouseEventHandler(this.radioGroupFileFilter_MouseClick);
            // 
            // grpCtrlPrjType
            // 
            this.grpCtrlPrjType.Controls.Add(this.listViewService);
            this.grpCtrlPrjType.Controls.Add(this.lbSvCount);
            this.grpCtrlPrjType.Controls.Add(this.btnPopupService);
            this.grpCtrlPrjType.Dock = System.Windows.Forms.DockStyle.Top;
            this.grpCtrlPrjType.Location = new System.Drawing.Point(0, 273);
            this.grpCtrlPrjType.Name = "grpCtrlPrjType";
            this.grpCtrlPrjType.Size = new System.Drawing.Size(249, 110);
            this.grpCtrlPrjType.TabIndex = 3;
            this.grpCtrlPrjType.Text = "业务类型";
            // 
            // listViewService
            // 
            this.listViewService.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            columnHeader2});
            this.listViewService.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listViewService.HeaderStyle = System.Windows.Forms.ColumnHeaderStyle.None;
            this.listViewService.Location = new System.Drawing.Point(2, 23);
            this.listViewService.Name = "listViewService";
            this.listViewService.Size = new System.Drawing.Size(245, 85);
            this.listViewService.TabIndex = 0;
            this.listViewService.UseCompatibleStateImageBehavior = false;
            this.listViewService.View = System.Windows.Forms.View.Details;
            // 
            // btnPopupService
            // 
            this.btnPopupService.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnPopupService.Location = new System.Drawing.Point(186, 0);
            this.btnPopupService.Name = "btnPopupService";
            this.btnPopupService.Size = new System.Drawing.Size(59, 21);
            this.btnPopupService.TabIndex = 6;
            this.btnPopupService.Text = "请选择↓";
            this.btnPopupService.Click += new System.EventHandler(this.btnPopupService_Click);
            // 
            // grpCtrlDataSrc
            // 
            this.grpCtrlDataSrc.Controls.Add(this.listViewProject);
            this.grpCtrlDataSrc.Controls.Add(this.btnPopupProject);
            this.grpCtrlDataSrc.Controls.Add(this.lbProjCount);
            this.grpCtrlDataSrc.Dock = System.Windows.Forms.DockStyle.Top;
            this.grpCtrlDataSrc.Location = new System.Drawing.Point(0, 168);
            this.grpCtrlDataSrc.Name = "grpCtrlDataSrc";
            this.grpCtrlDataSrc.Size = new System.Drawing.Size(249, 105);
            this.grpCtrlDataSrc.TabIndex = 2;
            this.grpCtrlDataSrc.Text = "数据来源";
            // 
            // listViewProject
            // 
            this.listViewProject.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            columnHeader1});
            this.listViewProject.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listViewProject.HeaderStyle = System.Windows.Forms.ColumnHeaderStyle.None;
            this.listViewProject.Location = new System.Drawing.Point(2, 23);
            this.listViewProject.Name = "listViewProject";
            this.listViewProject.Size = new System.Drawing.Size(245, 80);
            this.listViewProject.TabIndex = 0;
            this.listViewProject.UseCompatibleStateImageBehavior = false;
            this.listViewProject.View = System.Windows.Forms.View.Details;
            // 
            // btnPopupProject
            // 
            this.btnPopupProject.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnPopupProject.Location = new System.Drawing.Point(186, 1);
            this.btnPopupProject.Name = "btnPopupProject";
            this.btnPopupProject.Size = new System.Drawing.Size(59, 21);
            this.btnPopupProject.TabIndex = 6;
            this.btnPopupProject.Text = "请选择↓";
            this.btnPopupProject.Click += new System.EventHandler(this.btnPopupProject_Click);
            // 
            // grpCtlCitys
            // 
            this.grpCtlCitys.Controls.Add(this.lblCityCount);
            this.grpCtlCitys.Controls.Add(this.btnPopupCitys);
            this.grpCtlCitys.Dock = System.Windows.Forms.DockStyle.Top;
            this.grpCtlCitys.Location = new System.Drawing.Point(0, 145);
            this.grpCtlCitys.Name = "grpCtlCitys";
            this.grpCtlCitys.Size = new System.Drawing.Size(249, 23);
            this.grpCtlCitys.TabIndex = 8;
            this.grpCtlCitys.Text = "地市";
            this.grpCtlCitys.Visible = false;
            // 
            // btnPopupCitys
            // 
            this.btnPopupCitys.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnPopupCitys.Location = new System.Drawing.Point(186, 1);
            this.btnPopupCitys.Name = "btnPopupCitys";
            this.btnPopupCitys.Size = new System.Drawing.Size(59, 21);
            this.btnPopupCitys.TabIndex = 0;
            this.btnPopupCitys.Text = "选择地市";
            this.btnPopupCitys.Click += new System.EventHandler(this.btnPopupCitys_Click);
            // 
            // grpCtrlPeriod
            // 
            this.grpCtrlPeriod.Controls.Add(this.panelControl3);
            this.grpCtrlPeriod.Controls.Add(this.dateTimePickerEndDate);
            this.grpCtrlPeriod.Controls.Add(this.labelBeginDate);
            this.grpCtrlPeriod.Controls.Add(this.dateTimePickerBeginDate);
            this.grpCtrlPeriod.Controls.Add(this.labelByRoundBegin);
            this.grpCtrlPeriod.Controls.Add(this.labelEndDate);
            this.grpCtrlPeriod.Controls.Add(this.buttonPeriodClear);
            this.grpCtrlPeriod.Controls.Add(this.buttonPeriodRemove);
            this.grpCtrlPeriod.Controls.Add(this.cbxByRoundFromYear);
            this.grpCtrlPeriod.Controls.Add(this.buttonPeriodAdd);
            this.grpCtrlPeriod.Controls.Add(this.cbxByRoundFromRound);
            this.grpCtrlPeriod.Controls.Add(this.listBoxPeriod);
            this.grpCtrlPeriod.Dock = System.Windows.Forms.DockStyle.Top;
            this.grpCtrlPeriod.Location = new System.Drawing.Point(0, 51);
            this.grpCtrlPeriod.Name = "grpCtrlPeriod";
            this.grpCtrlPeriod.Size = new System.Drawing.Size(249, 94);
            this.grpCtrlPeriod.TabIndex = 1;
            this.grpCtrlPeriod.Text = "时间";
            // 
            // panelControl3
            // 
            this.panelControl3.Appearance.BackColor = System.Drawing.Color.Transparent;
            this.panelControl3.Appearance.Options.UseBackColor = true;
            this.panelControl3.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder;
            this.panelControl3.Controls.Add(this.cbxByRound);
            this.panelControl3.Controls.Add(this.checkBoxPeriodAdvance);
            this.panelControl3.Controls.Add(this.chkMultiTime);
            this.panelControl3.Location = new System.Drawing.Point(98, 2);
            this.panelControl3.Name = "panelControl3";
            this.panelControl3.Size = new System.Drawing.Size(148, 23);
            this.panelControl3.TabIndex = 31;
            // 
            // cbxByRound
            // 
            this.cbxByRound.AutoSize = true;
            this.cbxByRound.BackColor = System.Drawing.Color.Transparent;
            this.cbxByRound.Dock = System.Windows.Forms.DockStyle.Right;
            this.cbxByRound.Location = new System.Drawing.Point(12, 0);
            this.cbxByRound.Name = "cbxByRound";
            this.cbxByRound.Size = new System.Drawing.Size(48, 23);
            this.cbxByRound.TabIndex = 29;
            this.cbxByRound.Text = "按轮";
            this.cbxByRound.UseVisualStyleBackColor = false;
            this.cbxByRound.Visible = false;
            this.cbxByRound.CheckedChanged += new System.EventHandler(this.cbxByRound_CheckedChanged);
            // 
            // checkBoxPeriodAdvance
            // 
            this.checkBoxPeriodAdvance.Dock = System.Windows.Forms.DockStyle.Right;
            this.checkBoxPeriodAdvance.Location = new System.Drawing.Point(60, 0);
            this.checkBoxPeriodAdvance.MenuManager = this.ribbon;
            this.checkBoxPeriodAdvance.Name = "checkBoxPeriodAdvance";
            this.checkBoxPeriodAdvance.Properties.Caption = "高级";
            this.checkBoxPeriodAdvance.Size = new System.Drawing.Size(44, 19);
            this.checkBoxPeriodAdvance.TabIndex = 30;
            this.checkBoxPeriodAdvance.CheckedChanged += new System.EventHandler(this.checkBoxPeriodAdvance_CheckedChanged);
            // 
            // chkMultiTime
            // 
            this.chkMultiTime.Dock = System.Windows.Forms.DockStyle.Right;
            this.chkMultiTime.Enabled = false;
            this.chkMultiTime.Location = new System.Drawing.Point(104, 0);
            this.chkMultiTime.Name = "chkMultiTime";
            this.chkMultiTime.Properties.Caption = "分开";
            this.chkMultiTime.Size = new System.Drawing.Size(44, 19);
            this.chkMultiTime.TabIndex = 30;
            this.chkMultiTime.Visible = false;
            this.chkMultiTime.CheckedChanged += new System.EventHandler(this.checkBoxPeriodAdvance_CheckedChanged);
            // 
            // dateTimePickerEndDate
            // 
            this.dateTimePickerEndDate.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.dateTimePickerEndDate.Location = new System.Drawing.Point(59, 63);
            this.dateTimePickerEndDate.Name = "dateTimePickerEndDate";
            this.dateTimePickerEndDate.Size = new System.Drawing.Size(185, 21);
            this.dateTimePickerEndDate.TabIndex = 21;
            // 
            // labelBeginDate
            // 
            this.labelBeginDate.Location = new System.Drawing.Point(5, 35);
            this.labelBeginDate.Name = "labelBeginDate";
            this.labelBeginDate.Size = new System.Drawing.Size(48, 14);
            this.labelBeginDate.TabIndex = 10;
            this.labelBeginDate.Text = "开始日期";
            // 
            // dateTimePickerBeginDate
            // 
            this.dateTimePickerBeginDate.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.dateTimePickerBeginDate.Location = new System.Drawing.Point(58, 31);
            this.dateTimePickerBeginDate.Name = "dateTimePickerBeginDate";
            this.dateTimePickerBeginDate.Size = new System.Drawing.Size(185, 21);
            this.dateTimePickerBeginDate.TabIndex = 15;
            // 
            // labelByRoundBegin
            // 
            this.labelByRoundBegin.Location = new System.Drawing.Point(5, 51);
            this.labelByRoundBegin.Name = "labelByRoundBegin";
            this.labelByRoundBegin.Size = new System.Drawing.Size(48, 14);
            this.labelByRoundBegin.TabIndex = 10;
            this.labelByRoundBegin.Text = "指定轮次";
            this.labelByRoundBegin.Visible = false;
            // 
            // labelEndDate
            // 
            this.labelEndDate.Location = new System.Drawing.Point(5, 67);
            this.labelEndDate.Name = "labelEndDate";
            this.labelEndDate.Size = new System.Drawing.Size(48, 14);
            this.labelEndDate.TabIndex = 10;
            this.labelEndDate.Text = "结束日期";
            // 
            // buttonPeriodClear
            // 
            this.buttonPeriodClear.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.buttonPeriodClear.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.buttonPeriodClear.Location = new System.Drawing.Point(207, 69);
            this.buttonPeriodClear.Name = "buttonPeriodClear";
            this.buttonPeriodClear.Size = new System.Drawing.Size(48, 23);
            this.buttonPeriodClear.TabIndex = 22;
            this.buttonPeriodClear.Text = "清除";
            this.buttonPeriodClear.UseVisualStyleBackColor = true;
            this.buttonPeriodClear.Visible = false;
            this.buttonPeriodClear.Click += new System.EventHandler(this.buttonPeriodClear_Click);
            // 
            // buttonPeriodRemove
            // 
            this.buttonPeriodRemove.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.buttonPeriodRemove.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.buttonPeriodRemove.Location = new System.Drawing.Point(207, 47);
            this.buttonPeriodRemove.Name = "buttonPeriodRemove";
            this.buttonPeriodRemove.Size = new System.Drawing.Size(48, 23);
            this.buttonPeriodRemove.TabIndex = 19;
            this.buttonPeriodRemove.Text = "删除";
            this.buttonPeriodRemove.UseVisualStyleBackColor = true;
            this.buttonPeriodRemove.Visible = false;
            this.buttonPeriodRemove.Click += new System.EventHandler(this.buttonPeriodRemove_Click);
            // 
            // cbxByRoundFromYear
            // 
            this.cbxByRoundFromYear.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cbxByRoundFromYear.FormattingEnabled = true;
            this.cbxByRoundFromYear.Location = new System.Drawing.Point(67, 48);
            this.cbxByRoundFromYear.Name = "cbxByRoundFromYear";
            this.cbxByRoundFromYear.Size = new System.Drawing.Size(57, 20);
            this.cbxByRoundFromYear.TabIndex = 25;
            this.cbxByRoundFromYear.Visible = false;
            // 
            // buttonPeriodAdd
            // 
            this.buttonPeriodAdd.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.buttonPeriodAdd.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.buttonPeriodAdd.Location = new System.Drawing.Point(207, 24);
            this.buttonPeriodAdd.Name = "buttonPeriodAdd";
            this.buttonPeriodAdd.Size = new System.Drawing.Size(48, 23);
            this.buttonPeriodAdd.TabIndex = 18;
            this.buttonPeriodAdd.Text = "增加";
            this.buttonPeriodAdd.UseVisualStyleBackColor = true;
            this.buttonPeriodAdd.Visible = false;
            this.buttonPeriodAdd.Click += new System.EventHandler(this.buttonPeriodAdd_Click);
            // 
            // cbxByRoundFromRound
            // 
            this.cbxByRoundFromRound.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cbxByRoundFromRound.FormattingEnabled = true;
            this.cbxByRoundFromRound.Location = new System.Drawing.Point(128, 48);
            this.cbxByRoundFromRound.Name = "cbxByRoundFromRound";
            this.cbxByRoundFromRound.Size = new System.Drawing.Size(57, 20);
            this.cbxByRoundFromRound.TabIndex = 24;
            this.cbxByRoundFromRound.Visible = false;
            // 
            // listBoxPeriod
            // 
            this.listBoxPeriod.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.listBoxPeriod.FormattingEnabled = true;
            this.listBoxPeriod.HorizontalScrollbar = true;
            this.listBoxPeriod.ItemHeight = 12;
            this.listBoxPeriod.Location = new System.Drawing.Point(5, 25);
            this.listBoxPeriod.Name = "listBoxPeriod";
            this.listBoxPeriod.Size = new System.Drawing.Size(191, 64);
            this.listBoxPeriod.TabIndex = 13;
            this.listBoxPeriod.Visible = false;
            // 
            // grpCtrlDiyCond
            // 
            this.grpCtrlDiyCond.Controls.Add(this.labelControl2);
            this.grpCtrlDiyCond.Controls.Add(this.btnQuickCond);
            this.grpCtrlDiyCond.Controls.Add(this.btnResvRegion);
            this.grpCtrlDiyCond.Dock = System.Windows.Forms.DockStyle.Top;
            this.grpCtrlDiyCond.Location = new System.Drawing.Point(0, 0);
            this.grpCtrlDiyCond.Name = "grpCtrlDiyCond";
            this.grpCtrlDiyCond.Size = new System.Drawing.Size(249, 51);
            this.grpCtrlDiyCond.TabIndex = 0;
            this.grpCtrlDiyCond.Text = "自定义条件/区域";
            // 
            // labelControl2
            // 
            this.labelControl2.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.labelControl2.Location = new System.Drawing.Point(79, 30);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(108, 14);
            this.labelControl2.TabIndex = 10;
            this.labelControl2.Text = "从地图选取，或点击";
            // 
            // btnQuickCond
            // 
            this.btnQuickCond.Location = new System.Drawing.Point(5, 26);
            this.btnQuickCond.Name = "btnQuickCond";
            this.btnQuickCond.Size = new System.Drawing.Size(59, 21);
            this.btnQuickCond.TabIndex = 9;
            this.btnQuickCond.Text = "条件";
            this.btnQuickCond.ToolTip = "保存/选择查询条件";
            this.btnQuickCond.Click += new System.EventHandler(this.btnQuickCond_Click);
            // 
            // btnResvRegion
            // 
            this.btnResvRegion.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnResvRegion.Location = new System.Drawing.Point(187, 26);
            this.btnResvRegion.Name = "btnResvRegion";
            this.btnResvRegion.Size = new System.Drawing.Size(59, 21);
            this.btnResvRegion.TabIndex = 9;
            this.btnResvRegion.Text = "预存区域";
            this.btnResvRegion.ToolTip = "选择查询区域";
            this.btnResvRegion.Click += new System.EventHandler(this.btnResvRegion_Click);
            // 
            // dockPanelLegend
            // 
            this.dockPanelLegend.Controls.Add(this.ctrlContainerLegend);
            this.dockPanelLegend.Dock = DevExpress.XtraBars.Docking.DockingStyle.Fill;
            this.dockPanelLegend.FloatSize = new System.Drawing.Size(284, 262);
            this.dockPanelLegend.ID = new System.Guid("e224cf7d-4239-4ebb-a070-f46a13dda039");
            this.dockPanelLegend.Location = new System.Drawing.Point(4, 53);
            this.dockPanelLegend.Name = "dockPanelLegend";
            this.dockPanelLegend.Options.ShowCloseButton = false;
            this.dockPanelLegend.OriginalSize = new System.Drawing.Size(266, 534);
            this.dockPanelLegend.Size = new System.Drawing.Size(266, 534);
            this.dockPanelLegend.TabsPosition = DevExpress.XtraBars.Docking.TabsPosition.Top;
            this.dockPanelLegend.TabsScroll = true;
            this.dockPanelLegend.Text = "图例";
            // 
            // ctrlContainerLegend
            // 
            this.ctrlContainerLegend.Controls.Add(this.legendPanel);
            this.ctrlContainerLegend.Location = new System.Drawing.Point(0, 0);
            this.ctrlContainerLegend.Name = "ctrlContainerLegend";
            this.ctrlContainerLegend.Size = new System.Drawing.Size(266, 534);
            this.ctrlContainerLegend.TabIndex = 0;
            // 
            // legendPanel
            // 
            this.legendPanel.Dock = System.Windows.Forms.DockStyle.Fill;
            this.legendPanel.Location = new System.Drawing.Point(0, 0);
            this.legendPanel.Name = "legendPanel";
            this.legendPanel.Size = new System.Drawing.Size(266, 534);
            this.legendPanel.TabIndex = 61;
            // 
            // dockPanelConditonBatch
            // 
            this.dockPanelConditonBatch.Controls.Add(this.controlContainer1);
            this.dockPanelConditonBatch.Dock = DevExpress.XtraBars.Docking.DockingStyle.Fill;
            this.dockPanelConditonBatch.FloatVertical = true;
            this.dockPanelConditonBatch.ID = new System.Guid("4ea660b0-597d-4049-9951-e8988e633888");
            this.dockPanelConditonBatch.Location = new System.Drawing.Point(4, 53);
            this.dockPanelConditonBatch.Name = "dockPanelConditonBatch";
            this.dockPanelConditonBatch.OriginalSize = new System.Drawing.Size(266, 534);
            this.dockPanelConditonBatch.Size = new System.Drawing.Size(266, 534);
            this.dockPanelConditonBatch.Text = "检索条件和轮次";
            // 
            // controlContainer1
            // 
            this.controlContainer1.Controls.Add(this.splitContainerConditionBatch);
            this.controlContainer1.Location = new System.Drawing.Point(0, 0);
            this.controlContainer1.Name = "controlContainer1";
            this.controlContainer1.Size = new System.Drawing.Size(266, 534);
            this.controlContainer1.TabIndex = 0;
            // 
            // splitContainerConditionBatch
            // 
            this.splitContainerConditionBatch.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerConditionBatch.IsSplitterFixed = true;
            this.splitContainerConditionBatch.Location = new System.Drawing.Point(0, 0);
            this.splitContainerConditionBatch.Name = "splitContainerConditionBatch";
            this.splitContainerConditionBatch.Orientation = System.Windows.Forms.Orientation.Horizontal;
            // 
            // splitContainerConditionBatch.Panel1
            // 
            this.splitContainerConditionBatch.Panel1.AutoScroll = true;
            this.splitContainerConditionBatch.Panel1MinSize = 145;
            // 
            // splitContainerConditionBatch.Panel2
            // 
            this.splitContainerConditionBatch.Panel2.Controls.Add(this.gridControlAll);
            this.splitContainerConditionBatch.Size = new System.Drawing.Size(266, 534);
            this.splitContainerConditionBatch.SplitterDistance = 145;
            this.splitContainerConditionBatch.TabIndex = 0;
            // 
            // gridControlAll
            // 
            this.gridControlAll.Dock = System.Windows.Forms.DockStyle.Fill;
            gridLevelNode2.RelationName = "Level1";
            this.gridControlAll.LevelTree.Nodes.AddRange(new DevExpress.XtraGrid.GridLevelNode[] {
            gridLevelNode2});
            this.gridControlAll.Location = new System.Drawing.Point(0, 0);
            this.gridControlAll.MainView = this.gridViewTotal;
            this.gridControlAll.Name = "gridControlAll";
            this.gridControlAll.Size = new System.Drawing.Size(266, 385);
            this.gridControlAll.TabIndex = 41;
            this.gridControlAll.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridViewTotal,
            this.gridView2});
            // 
            // gridViewTotal
            // 
            this.gridViewTotal.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gridViewTotal.Appearance.HeaderPanel.TextOptions.Trimming = DevExpress.Utils.Trimming.None;
            this.gridViewTotal.Appearance.HeaderPanel.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridViewTotal.ColumnPanelRowHeight = 25;
            this.gridViewTotal.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn1,
            this.gridColumn21});
            this.gridViewTotal.GridControl = this.gridControlAll;
            this.gridViewTotal.Name = "gridViewTotal";
            this.gridViewTotal.OptionsBehavior.Editable = false;
            this.gridViewTotal.OptionsDetail.ShowDetailTabs = false;
            this.gridViewTotal.OptionsDetail.SmartDetailExpand = false;
            this.gridViewTotal.OptionsView.ColumnAutoWidth = false;
            this.gridViewTotal.OptionsView.ShowGroupPanel = false;
            this.gridViewTotal.PaintStyleName = "WindowsXP";
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "轮次";
            this.gridColumn1.FieldName = "batchName";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.Visible = true;
            this.gridColumn1.VisibleIndex = 0;
            this.gridColumn1.Width = 60;
            // 
            // gridColumn21
            // 
            this.gridColumn21.Caption = "采集时间段";
            this.gridColumn21.FieldName = "timeBucket";
            this.gridColumn21.Name = "gridColumn21";
            this.gridColumn21.Visible = true;
            this.gridColumn21.VisibleIndex = 1;
            this.gridColumn21.Width = 166;
            // 
            // gridView2
            // 
            this.gridView2.GridControl = this.gridControlAll;
            this.gridView2.Name = "gridView2";
            // 
            // checkBoxAdvance
            // 
            this.checkBoxAdvance.AutoSize = true;
            this.checkBoxAdvance.ForeColor = System.Drawing.SystemColors.ActiveCaption;
            this.checkBoxAdvance.Location = new System.Drawing.Point(6, 291);
            this.checkBoxAdvance.Name = "checkBoxAdvance";
            this.checkBoxAdvance.Size = new System.Drawing.Size(84, 16);
            this.checkBoxAdvance.TabIndex = 5;
            this.checkBoxAdvance.Text = "其它设置↑";
            this.checkBoxAdvance.UseVisualStyleBackColor = true;
            // 
            // toolStripDropDownRegion
            // 
            this.toolStripDropDownRegion.LayoutStyle = System.Windows.Forms.ToolStripLayoutStyle.Flow;
            this.toolStripDropDownRegion.Name = "toolStripDropDown1";
            this.toolStripDropDownRegion.Size = new System.Drawing.Size(2, 4);
            // 
            // toolStripDropDownProject
            // 
            this.toolStripDropDownProject.LayoutStyle = System.Windows.Forms.ToolStripLayoutStyle.Flow;
            this.toolStripDropDownProject.Name = "toolStripDropDown1";
            this.toolStripDropDownProject.Size = new System.Drawing.Size(2, 4);
            this.toolStripDropDownProject.Closed += new System.Windows.Forms.ToolStripDropDownClosedEventHandler(this.toolStripDropDownProject_Closed);
            this.toolStripDropDownProject.Opened += new System.EventHandler(this.toolStripDropDownProject_Opened);
            // 
            // toolStripDropDownService
            // 
            this.toolStripDropDownService.LayoutStyle = System.Windows.Forms.ToolStripLayoutStyle.Flow;
            this.toolStripDropDownService.Name = "toolStripDropDown1";
            this.toolStripDropDownService.Size = new System.Drawing.Size(2, 4);
            this.toolStripDropDownService.Closed += new System.Windows.Forms.ToolStripDropDownClosedEventHandler(this.toolStripDropDownService_Closed);
            this.toolStripDropDownService.Opened += new System.EventHandler(this.toolStripDropDownService_Opened);
            // 
            // ctxQuickCond
            // 
            this.ctxQuickCond.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.toolStripMenuItem1,
            this.miSaveCurCond});
            this.ctxQuickCond.Name = "ctxQuickCond";
            this.ctxQuickCond.Size = new System.Drawing.Size(149, 32);
            // 
            // toolStripMenuItem1
            // 
            this.toolStripMenuItem1.Name = "toolStripMenuItem1";
            this.toolStripMenuItem1.Size = new System.Drawing.Size(145, 6);
            // 
            // miSaveCurCond
            // 
            this.miSaveCurCond.Name = "miSaveCurCond";
            this.miSaveCurCond.Size = new System.Drawing.Size(148, 22);
            this.miSaveCurCond.Text = "保存当前条件";
            // 
            // toolStripDropDownMarkFile
            // 
            this.toolStripDropDownMarkFile.LayoutStyle = System.Windows.Forms.ToolStripLayoutStyle.HorizontalStackWithOverflow;
            this.toolStripDropDownMarkFile.Name = "toolStripDropDown1";
            this.toolStripDropDownMarkFile.Size = new System.Drawing.Size(102, 25);
            // 
            // toolStripDropDownAgent
            // 
            this.toolStripDropDownAgent.LayoutStyle = System.Windows.Forms.ToolStripLayoutStyle.Flow;
            this.toolStripDropDownAgent.Name = "toolStripDropDown1";
            this.toolStripDropDownAgent.Size = new System.Drawing.Size(2, 4);
            this.toolStripDropDownAgent.Closed += new System.Windows.Forms.ToolStripDropDownClosedEventHandler(this.toolStripDropDownAgent_Closed);
            this.toolStripDropDownAgent.Opened += new System.EventHandler(this.toolStripDropDownAgent_Opened);
            // 
            // barManager
            // 
            this.barManager.Controller = this.barAndDockingController;
            this.barManager.DockControls.Add(this.barDockControlTop);
            this.barManager.DockControls.Add(this.barDockControlBottom);
            this.barManager.DockControls.Add(this.barDockControlLeft);
            this.barManager.DockControls.Add(this.barDockControlRight);
            this.barManager.DockManager = this.dockManager;
            this.barManager.Form = this;
            this.barManager.MaxItemId = 0;
            this.barManager.ToolTipController = this.toolTip;
            // 
            // barDockControlTop
            // 
            this.barDockControlTop.Dock = System.Windows.Forms.DockStyle.Top;
            this.barDockControlTop.Location = new System.Drawing.Point(0, 0);
            this.barDockControlTop.Size = new System.Drawing.Size(1020, 0);
            // 
            // barDockControlBottom
            // 
            this.barDockControlBottom.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.barDockControlBottom.Location = new System.Drawing.Point(0, 771);
            this.barDockControlBottom.Size = new System.Drawing.Size(1020, 0);
            // 
            // barDockControlLeft
            // 
            this.barDockControlLeft.Dock = System.Windows.Forms.DockStyle.Left;
            this.barDockControlLeft.Location = new System.Drawing.Point(0, 0);
            this.barDockControlLeft.Size = new System.Drawing.Size(0, 771);
            // 
            // barDockControlRight
            // 
            this.barDockControlRight.Dock = System.Windows.Forms.DockStyle.Right;
            this.barDockControlRight.Location = new System.Drawing.Point(1020, 0);
            this.barDockControlRight.Size = new System.Drawing.Size(0, 771);
            // 
            // toolStripDropDownCity
            // 
            this.toolStripDropDownCity.LayoutStyle = System.Windows.Forms.ToolStripLayoutStyle.Flow;
            this.toolStripDropDownCity.Name = "toolStripDropDownCity";
            this.toolStripDropDownCity.Size = new System.Drawing.Size(2, 4);
            // 
            // bgWorkerBackground
            // 
            this.bgWorkerBackground.DoWork += new System.ComponentModel.DoWorkEventHandler(this.bgWorkerBackground_DoWork);
            this.bgWorkerBackground.RunWorkerCompleted += new System.ComponentModel.RunWorkerCompletedEventHandler(this.bgWorkerBackground_RunWorkerCompleted);
            // 
            // timerBackground
            // 
            this.timerBackground.Interval = 600000;
            this.timerBackground.Tick += new System.EventHandler(this.timerBackground_Tick);
            // 
            // toolStripDropDownDevice
            // 
            this.toolStripDropDownDevice.LayoutStyle = System.Windows.Forms.ToolStripLayoutStyle.Flow;
            this.toolStripDropDownDevice.Name = "toolStripDropDownCity";
            this.toolStripDropDownDevice.Size = new System.Drawing.Size(2, 4);
            // 
            // timerWeakRoadAutoStats
            // 
            this.timerWeakRoadAutoStats.Interval = 7200000;
            this.timerWeakRoadAutoStats.Tick += new System.EventHandler(this.timerWeakRoadAutoStats_Tick);
            // 
            // bgWeakRoadAutoStats
            // 
            this.bgWeakRoadAutoStats.DoWork += new System.ComponentModel.DoWorkEventHandler(this.bgWeakRoadAutoStats_DoWork);
            this.bgWeakRoadAutoStats.RunWorkerCompleted += new System.ComponentModel.RunWorkerCompletedEventHandler(this.bgWeakRoadAutoStats_RunWorkerCompleted);
            // 
            // ctxMenuResvRegTv
            // 
            this.ctxMenuResvRegTv.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miLoadExcelChkNode});
            this.ctxMenuResvRegTv.Name = "contextMenuStrip1";
            this.ctxMenuResvRegTv.Size = new System.Drawing.Size(178, 26);
            this.ctxMenuResvRegTv.Closed += new System.Windows.Forms.ToolStripDropDownClosedEventHandler(this.ctxMenuResvRegTv_Closed);
            // 
            // miLoadExcelChkNode
            // 
            this.miLoadExcelChkNode.Name = "miLoadExcelChkNode";
            this.miLoadExcelChkNode.Size = new System.Drawing.Size(177, 22);
            this.miLoadExcelChkNode.Text = "读取Excel进行勾选";
            this.miLoadExcelChkNode.Click += new System.EventHandler(this.miLoadExcelChkNode_Click);
            // 
            // MainForm
            // 
            this.AllowMdiBar = true;
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.ClientSize = new System.Drawing.Size(1020, 771);
            this.Controls.Add(this.trackBarReplay);
            this.Controls.Add(this.panelControlWorkSheet);
            this.Controls.Add(this.LeftToolStripPanel);
            this.Controls.Add(this.RightToolStripPanel);
            this.Controls.Add(this.pnlCondAndLegend);
            this.Controls.Add(this.hideContainerLeft);
            this.Controls.Add(this.ribbon);
            this.Controls.Add(this.ribbonStatusBar);
            this.Controls.Add(this.barDockControlLeft);
            this.Controls.Add(this.barDockControlRight);
            this.Controls.Add(this.barDockControlBottom);
            this.Controls.Add(this.barDockControlTop);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.IsMdiContainer = true;
            this.KeyPreview = true;
            this.Name = "MainForm";
            this.Ribbon = this.ribbon;
            this.ShowInTaskbar = false;
            this.StatusBar = this.ribbonStatusBar;
            this.Text = "路测数据综合分析应用平台";
            this.WindowState = System.Windows.Forms.FormWindowState.Minimized;
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.MainForm_FormClosing);
            this.KeyUp += new System.Windows.Forms.KeyEventHandler(this.MainForm_KeyUp);
            ((System.ComponentModel.ISupportInitialize)(this.ribbon)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.applicationMenu1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemComboBox1)).EndInit();
            this.contextMenuStripTreeView.ResumeLayout(false);
            this.contextMenuStripTabControlWorkSpace.ResumeLayout(false);
            this.contextMenuStripVisibleParam.ResumeLayout(false);
            this.contextMenuStripMenuItem.ResumeLayout(false);
            this.contextMenuStripTreeViewWorkSpace.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.panelControlWorkSheet)).EndInit();
            this.panelControlWorkSheet.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControlWorkSheet)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.trackBarReplay.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.trackBarReplay)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dockManager)).EndInit();
            this.hideContainerLeft.ResumeLayout(false);
            this.dockPanelNavi.ResumeLayout(false);
            this.ctrlContainerNavi.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControlNavigator)).EndInit();
            this.xtraTabControlNavigator.ResumeLayout(false);
            this.tabPageSpace.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControlWks)).EndInit();
            this.xtraTabControlWks.ResumeLayout(false);
            this.xtraTabPageWksNavi.ResumeLayout(false);
            this.xtraTabPageWksTemplates.ResumeLayout(false);
            this.tabPageMenu.ResumeLayout(false);
            this.tabPageParam.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControlChildVisibleParam)).EndInit();
            this.xtraTabControlChildVisibleParam.ResumeLayout(false);
            this.xtraTabPageSignalParam.ResumeLayout(false);
            this.pnlParam.ResumeLayout(false);
            this.pnlParam.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtVisibleParamSearch.Properties)).EndInit();
            this.xtraTabPageEventParam.ResumeLayout(false);
            this.pnlEventParam.ResumeLayout(false);
            this.pnlEventParam.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.textEditSearchEvent.Properties)).EndInit();
            this.pnlCondAndLegend.ResumeLayout(false);
            this.dockPanelQueryCond.ResumeLayout(false);
            this.dockPanel1_Container.ResumeLayout(false);
            this.scrollCtrlCondition.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.grpCtrlCarrier)).EndInit();
            this.grpCtrlCarrier.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.grpCtrlDaiwei)).EndInit();
            this.grpCtrlDaiwei.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.grpCtrlRegion)).EndInit();
            this.grpCtrlRegion.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.treeListArea)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.grpCtrlDevice)).EndInit();
            this.grpCtrlDevice.ResumeLayout(false);
            this.grpCtrlDevice.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.grpCtrlFileFilter)).EndInit();
            this.grpCtrlFileFilter.ResumeLayout(false);
            this.grpCtrlFileFilter.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.popupContainer)).EndInit();
            this.popupContainer.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.popupContainerSQL)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.radioGroupFileFilter.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.grpCtrlPrjType)).EndInit();
            this.grpCtrlPrjType.ResumeLayout(false);
            this.grpCtrlPrjType.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.grpCtrlDataSrc)).EndInit();
            this.grpCtrlDataSrc.ResumeLayout(false);
            this.grpCtrlDataSrc.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.grpCtlCitys)).EndInit();
            this.grpCtlCitys.ResumeLayout(false);
            this.grpCtlCitys.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.grpCtrlPeriod)).EndInit();
            this.grpCtrlPeriod.ResumeLayout(false);
            this.grpCtrlPeriod.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl3)).EndInit();
            this.panelControl3.ResumeLayout(false);
            this.panelControl3.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.checkBoxPeriodAdvance.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkMultiTime.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.grpCtrlDiyCond)).EndInit();
            this.grpCtrlDiyCond.ResumeLayout(false);
            this.grpCtrlDiyCond.PerformLayout();
            this.dockPanelLegend.ResumeLayout(false);
            this.ctrlContainerLegend.ResumeLayout(false);
            this.dockPanelConditonBatch.ResumeLayout(false);
            this.controlContainer1.ResumeLayout(false);
            this.splitContainerConditionBatch.Panel2.ResumeLayout(false);
            this.splitContainerConditionBatch.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlAll)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewTotal)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).EndInit();
            this.ctxQuickCond.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.barManager)).EndInit();
            this.ctxMenuResvRegTv.ResumeLayout(false);
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraBars.Ribbon.RibbonControl ribbon;
        private DevExpress.XtraBars.Ribbon.RibbonPage ribbonPageStart;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroupFile;
        private DevExpress.XtraBars.Ribbon.RibbonStatusBar ribbonStatusBar;
        private System.Windows.Forms.ImageList imageListMenuItem;
        private System.Windows.Forms.ImageList imageListVisibleParam;
        private System.Windows.Forms.ImageList imageListWorkSpace;
        private System.Windows.Forms.ToolStripPanel LeftToolStripPanel;
        private System.Windows.Forms.ToolStripPanel RightToolStripPanel;
        private System.Windows.Forms.ContextMenuStrip contextMenuStripTabControlWorkSpace;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItemNewWorkSpace1;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItemRemoveWorkSpace1;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItemRenameWorkSpace1;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItemPreviousWorkSpace;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItemNextWorkSpace;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItemCloseAll;
        private System.Windows.Forms.ContextMenuStrip contextMenuStripVisibleParam;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemModifyVisibleParam;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItemCloseWindow;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItemRenameWorkSpace;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItemNewWorkSpace;
        private System.Windows.Forms.ContextMenuStrip contextMenuStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemOpenWindow;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItemRemoveWorkSpace;
        private System.Windows.Forms.ToolStripDropDown toolStripDropDownLocal;
        private System.Windows.Forms.ContextMenuStrip ctxQuickRestoreWindows;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemActive;
        private System.Windows.Forms.ContextMenuStrip contextMenuStripTreeViewWorkSpace;

        private System.Windows.Forms.Timer logTimer;
        private System.ComponentModel.BackgroundWorker bgWorkerLog;
        private System.Windows.Forms.Timer playTimer;
        private System.Windows.Forms.ImageList imageListWnd;

        private bool disposing;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroupChooseCity;
        private DevExpress.XtraBars.Ribbon.ApplicationMenu applicationMenu1;
        private DevExpress.XtraBars.BarButtonItem barButtonItemOpenWorkSheet;
        private DevExpress.XtraBars.BarButtonItem barButtonItemNew;
        private DevExpress.XtraBars.BarButtonItem barButtonItemSave;
        private DevExpress.XtraBars.BarButtonItem barButtonItemNavigator;
        private DevExpress.XtraBars.BarButtonItem barButtonItemPreviousData;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroupChange;
        private DevExpress.XtraBars.BarButtonItem barButtonItemNextData;
        private DevExpress.XtraBars.BarButtonItem barButtonItemNewWorkSheet;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroupWorkSheet;
        private DevExpress.XtraBars.BarButtonItem barButtonItemRenameWorkSheet;
        private DevExpress.XtraBars.BarButtonItem barButtonItemRemoveWorkSheet;
        private DevExpress.XtraBars.BarButtonItem barButtonItemPreviousWorkSheet;
        private DevExpress.XtraBars.BarButtonItem barButtonItemNextWorkSheet;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroupWindowStyle;
        private DevExpress.XtraBars.BarButtonItem barButtonItemCascade;
        private DevExpress.XtraBars.BarButtonItem barButtonItemTileVertical;
        private DevExpress.XtraBars.BarButtonItem barButtonItemTileHorizontal;
        private DevExpress.XtraBars.BarButtonItem barButtonItemCloseAll;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroupPlay;
        private DevExpress.XtraBars.BarEditItem barEditItemWKSList;
        private DevExpress.XtraEditors.Repository.RepositoryItemComboBox repositoryItemComboBox1;
        private DevExpress.XtraBars.BarButtonItem barButtonItemPlayLog;
        private DevExpress.XtraBars.BarButtonItem barButtonItemStopPlay;
        private DevExpress.XtraBars.BarButtonItem barButtonItemSlower;
        private DevExpress.XtraBars.BarButtonItem barButtonItemFaster;
        private DevExpress.XtraBars.BarButtonItem barButtonItemFullScreen;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroupScreen;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroupMobile;
        private DevExpress.XtraBars.BarSubItem barSubItemMS;
        private DevExpress.XtraBars.BarButtonItem barButtonItemMiMS_All;
        private DevExpress.XtraBars.BarButtonItem barButtonItemMiMS_1;
        private DevExpress.XtraBars.BarButtonItem barButtonItemMiMS_2;
        private DevExpress.XtraBars.BarButtonItem barButtonItemMiMS_3;
        private DevExpress.XtraBars.BarButtonItem barButtonItemMiMS_4;
        private DevExpress.XtraBars.BarButtonItem barButtonItemMiMS_5;
        private DevExpress.XtraBars.BarButtonItem barButtonItemMiMS_6;
        private DevExpress.XtraBars.BarButtonItem barButtonItemMiMS_7;
        private DevExpress.XtraBars.BarButtonItem barButtonItemMiMS_8;
        private DevExpress.XtraBars.BarSubItem barSubItemOpen;
        private DevExpress.XtraBars.BarButtonItem barButtonItemTopFrontMi;
        private DevExpress.XtraBars.BarButtonItem barButtonItemMiAllTopFront;
        private DevExpress.XtraBars.BarButtonItem barButtonItemOpacitySetMi;
        private DevExpress.XtraBars.BarButtonItem barButtonItemMiAllOpacitySet;
        private DevExpress.XtraBars.BarButtonItem barButtonItemArrangeIcons;
        private DevExpress.XtraBars.BarButtonItem barButtonItemLoadCellConfig_tsmi;
        private DevExpress.XtraBars.BarButtonItem barButtonItemQueryParam;
        private DevExpress.XtraBars.BarButtonItem barButtonItemPlanningInfoImport;
        private DevExpress.XtraBars.Ribbon.RibbonPage ribbonPageHelp;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroupOpen;
        private DevExpress.XtraBars.BarButtonItem barButtonItemOwnDataUpload;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroupCheck;
        private DevExpress.XtraBars.BarButtonItem barButtonItemContents;
        private DevExpress.XtraBars.BarButtonItem barButtonItemViewHistory;
        private DevExpress.XtraBars.BarButtonItem barButtonItemAbout;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroupAbout;
        private DevExpress.XtraBars.BarButtonItem barButtonItemSystemSetting;
        private DevExpress.XtraBars.BarButtonItem barButtonItemLink2SZMap;
        private DevExpress.XtraBars.BarButtonItem barButtonItemExit;
        private DevExpress.XtraBars.BarButtonItem barButtonItemSaveWorkSheet;
        private DevExpress.XtraBars.BarButtonItem barButtonItemSaveAs;
        private DevExpress.XtraBars.BarButtonItem barButtonItemExport2Tab;
        private DevExpress.XtraBars.BarButtonItem barButtonItemSysSetting;
        private DevExpress.XtraBars.BarSubItem barSubItemOpenWorkSheet;
        private DevExpress.XtraBars.BarButtonItem barButtonItemFunctionNav;
        private DevExpress.LookAndFeel.DefaultLookAndFeel defaultLookAndFeel1;
        private DevExpress.XtraBars.BarButtonItem barButtonItemNewWS;
        private DevExpress.XtraBars.BarButtonItem barButtonItemFullScr;
        private DevExpress.XtraBars.BarButtonItem barButtonItemWKSManage;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator1;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator2;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator3;
        private DevExpress.XtraEditors.PanelControl panelControlWorkSheet;
        private DevExpress.XtraTab.XtraTabControl xtraTabControlWorkSheet;
        private DevExpress.XtraBars.BarStaticItem barStaticItemCellInfo;
        private System.ComponentModel.BackgroundWorker bgWorkerFire;
        private DevExpress.XtraEditors.TrackBarControl trackBarReplay;
        private DevExpress.Utils.ToolTipController toolTip;
        private System.Windows.Forms.ContextMenuStrip contextMenuStripTreeView;
        private System.Windows.Forms.ToolStripMenuItem minAddChildNode;
        private System.Windows.Forms.ToolStripMenuItem minEditCurrentNode;
        private System.Windows.Forms.ToolStripMenuItem minDelCurrentNode;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator17;
        private System.Windows.Forms.ToolStripMenuItem minAddRootNode;
        private DevExpress.XtraBars.BarButtonItem barButtonItemExportKML;
        private System.Windows.Forms.ImageList imageListVisibleEvent;
        private DevExpress.XtraBars.Docking.DockManager dockManager;
        private DevExpress.XtraBars.Docking.DockPanel dockPanelQueryCond;
        private DevExpress.XtraBars.Docking.ControlContainer dockPanel1_Container;
        private System.Windows.Forms.CheckBox checkBoxAdvance;
        private System.Windows.Forms.ComboBox cbxByRoundFromRound;
        private System.Windows.Forms.ComboBox cbxByRoundFromYear;
        private System.Windows.Forms.DateTimePicker dateTimePickerBeginDate;
        private System.Windows.Forms.DateTimePicker dateTimePickerEndDate;
        private System.Windows.Forms.Button buttonPeriodClear;
        private System.Windows.Forms.Button buttonPeriodRemove;
        private System.Windows.Forms.Button buttonPeriodAdd;
        private System.Windows.Forms.ListBox listBoxPeriod;
        private System.Windows.Forms.ListView listViewProject;
        private System.Windows.Forms.ListView listViewService;
        private System.Windows.Forms.TextBox textBoxFileName;
        private System.Windows.Forms.CheckBox checkBoxFileName;
        private System.Windows.Forms.ToolStripDropDown toolStripDropDownRegion;
        private System.Windows.Forms.ToolStripDropDown toolStripDropDownProject;
        private System.Windows.Forms.Label lbSvCount;
        private System.Windows.Forms.ToolStripDropDown toolStripDropDownService;
        private System.Windows.Forms.CheckBox cbxByRound;
        private DevExpress.XtraBars.Docking.DockPanel pnlCondAndLegend;
        private DevExpress.XtraBars.Docking.DockPanel dockPanelLegend;
        private DevExpress.XtraBars.Docking.ControlContainer ctrlContainerLegend;
        private System.Windows.Forms.ContextMenuStrip ctxQuickCond;
        private System.Windows.Forms.ToolStripSeparator toolStripMenuItem1;
        private System.Windows.Forms.ToolStripMenuItem miSaveCurCond;
        private DevExpress.XtraBars.Docking.DockPanel dockPanelNavi;
        private DevExpress.XtraBars.Docking.ControlContainer ctrlContainerNavi;
        private DevExpress.XtraTab.XtraTabControl xtraTabControlNavigator;
        private DevExpress.XtraTab.XtraTabPage tabPageSpace;
        private System.Windows.Forms.TreeView treeViewWorkSpace;
        private DevExpress.XtraTab.XtraTabPage tabPageMenu;
        public System.Windows.Forms.TreeView treeViewMenuItem;
        private DevExpress.XtraTab.XtraTabPage tabPageParam;
        private DevExpress.XtraTab.XtraTabControl xtraTabControlChildVisibleParam;
        private DevExpress.XtraTab.XtraTabPage xtraTabPageSignalParam;
        private System.Windows.Forms.TreeView treeViewVisibleParam;
        private System.Windows.Forms.Panel pnlParam;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.SimpleButton btnVisibleParamNext;
        private DevExpress.XtraEditors.TextEdit txtVisibleParamSearch;
        private DevExpress.XtraTab.XtraTabPage xtraTabPageEventParam;
        private System.Windows.Forms.TreeView treeViewVisibleEvent;
        private DevExpress.XtraEditors.RadioGroup radioGroupFileFilter;
        private System.Windows.Forms.ToolStripDropDown toolStripDropDownMarkFile;
        private DevExpress.XtraBars.PopupControlContainer popupContainer;
        private DevExpress.XtraEditors.SimpleButton btnQuickCond;
        private DevExpress.XtraEditors.CheckEdit checkBoxPeriodAdvance;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.SimpleButton btnResvRegion;
        private DevExpress.XtraEditors.LabelControl labelByRoundBegin;
        private DevExpress.XtraEditors.LabelControl labelEndDate;
        private DevExpress.XtraEditors.LabelControl labelBeginDate;
        private DevExpress.XtraEditors.CheckEdit chkMultiTime;
        private System.Windows.Forms.Label lbProjCount;
        private DevExpress.XtraEditors.SimpleButton btnPopupService;
        private DevExpress.XtraEditors.SimpleButton btnPopupProject;
        private DevExpress.XtraEditors.XtraScrollableControl scrollCtrlCondition;
        private DevExpress.XtraEditors.GroupControl grpCtrlDiyCond;
        private DevExpress.XtraEditors.GroupControl grpCtrlPeriod;
        private DevExpress.XtraEditors.GroupControl grpCtrlDataSrc;
        private DevExpress.XtraEditors.GroupControl grpCtrlFileFilter;
        private DevExpress.XtraEditors.GroupControl grpCtrlPrjType;
        private DevExpress.XtraEditors.PanelControl panelControl3;
        private System.Windows.Forms.ToolStripDropDown toolStripDropDownAgent;
        private DevExpress.XtraBars.BarButtonItem barButtonItemShowMap;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroupMap;
        private DevExpress.XtraEditors.SimpleButton btnQuickRestoreWindow;
        private DevExpress.XtraBars.BarSubItem barSubItemCutScreen;
        private DevExpress.XtraBars.BarButtonItem barButtonItemCutScreen;
        private DevExpress.XtraBars.BarButtonItem barButtonItemHDCut;
        private System.Windows.Forms.TreeView treeViewTemplateWS;
        private DevExpress.XtraTab.XtraTabControl xtraTabControlWks;
        private DevExpress.XtraTab.XtraTabPage xtraTabPageWksNavi;
        private DevExpress.XtraTab.XtraTabPage xtraTabPageWksTemplates;
        private DevExpress.XtraBars.BarAndDockingController barAndDockingController;
        private DevExpress.XtraBars.BarButtonItem barSubItemChooseCity;
        private DevExpress.XtraBars.BarDockControl barDockControlLeft;
        private DevExpress.XtraBars.BarDockControl barDockControlRight;
        private DevExpress.XtraBars.BarDockControl barDockControlBottom;
        private DevExpress.XtraBars.BarDockControl barDockControlTop;
        private DevExpress.XtraBars.BarManager barManager;
        private DevExpress.XtraEditors.GroupControl grpCtlCitys;
        private DevExpress.XtraEditors.SimpleButton btnPopupCitys;
        private System.Windows.Forms.Label lblCityCount;
        private System.Windows.Forms.ToolStripDropDown toolStripDropDownCity;
        private System.Windows.Forms.ToolStripMenuItem miSaveAsWSTemplate;
        private LegendPanel legendPanel;
        private System.ComponentModel.BackgroundWorker bgWorkerBackground;
        private System.Windows.Forms.Timer timerBackground;
        private DevExpress.XtraEditors.SimpleButton btnAddNewTemplateWS;
        private DevExpress.XtraBars.BarButtonItem barBtnUserFuncPermission;
        private DevExpress.XtraBars.BarSubItem barSubItemPermissionSetting;
        private DevExpress.XtraBars.BarButtonItem barBtnUserDataSrcPermission;
        private DevExpress.XtraEditors.GroupControl grpCtrlCarrier;
        private System.Windows.Forms.ListView listViewCarrier;
        private System.Windows.Forms.ColumnHeader columnHeader4;
        private DevExpress.XtraEditors.GroupControl grpCtrlDaiwei;
        private System.Windows.Forms.Label lbagCount;
        private DevExpress.XtraEditors.SimpleButton btnPopupAgent;
        private System.Windows.Forms.ListView listViewAgent;
        private DevExpress.XtraEditors.GroupControl grpCtrlRegion;
        private DevExpress.XtraEditors.GroupControl grpCtrlDevice;
        private System.Windows.Forms.Label labelDeviceCount;
        private DevExpress.XtraEditors.SimpleButton btnPopupDevice;
        private System.Windows.Forms.ToolStripDropDown toolStripDropDownDevice;
        private DevExpress.XtraBars.BarButtonItem barBtnShowUserForm;
        private DevExpress.XtraBars.BarButtonItem barButtonItemUpdateInterface;
        private DevExpress.XtraBars.BarButtonItem barBtnUserCityRight;
        private DevExpress.XtraBars.PopupControlContainer popupContainerSQL;
        private DevExpress.XtraTreeList.TreeList treeListArea;
        private DevExpress.XtraTreeList.Columns.TreeListColumn colKey;
        private DevExpress.XtraBars.BarCheckItem barChkItemDEBUG;
        private DevExpress.XtraBars.BarButtonItem barBtnCellCfgHistorySet;
        private DevExpress.XtraBars.BarButtonItem barButtonItemHDCutAndPrint;
        private System.Windows.Forms.Timer timerWeakRoadAutoStats;
        private System.ComponentModel.BackgroundWorker bgWeakRoadAutoStats;
        private System.Windows.Forms.Panel pnlEventParam;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.SimpleButton btnSearchEvent;
        private DevExpress.XtraEditors.TextEdit textEditSearchEvent;
        private DevExpress.XtraBars.Docking.AutoHideContainer hideContainerLeft;
        private System.Windows.Forms.ContextMenuStrip ctxMenuResvRegTv;
        private System.Windows.Forms.ToolStripMenuItem miLoadExcelChkNode;

        private DevExpress.XtraBars.BarStaticItem barStaticUpdate;
        private DevExpress.XtraBars.Docking.DockPanel dockPanelConditonBatch;
        private DevExpress.XtraBars.Docking.ControlContainer controlContainer1;
        private System.Windows.Forms.SplitContainer splitContainerConditionBatch;
        public DevExpress.XtraGrid.GridControl gridControlAll;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewTotal;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn21;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView2;
        private DevExpress.XtraBars.BarStaticItem lblCurServerIp;
        private DevExpress.XtraBars.BarButtonItem barBtnOpenProgramLocation;
        private DevExpress.XtraBars.BarButtonItem barBtnProgramRestart;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroupProgram;
    }
}