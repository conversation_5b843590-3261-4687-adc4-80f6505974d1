﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.ZTFunc.AcceptHistory;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    static class AcceptHistoryResultCreator
    {
        public static List<AcceptHistorySite> GetResult()
        {
            DiySqlSiteSelect diySqlSiteSelect = new DiySqlSiteSelect(MainModel.GetInstance());
            diySqlSiteSelect.Query();

            DiySqlCellSelect diySqlCellSelect = new DiySqlCellSelect(MainModel.GetInstance());
            diySqlCellSelect.ListResultItem = diySqlSiteSelect.ListResultItem;
            diySqlCellSelect.Query();
            return diySqlCellSelect.ListResultItem;
        }
    }

    class AcceptHistorySite
    {
        public AcceptHistorySite()
        {
            HistoryCells = new List<AcceptHistoryCell>();
        }

        public long ID
        {
            get;
            set;
        }

        public int? HandoverRequestCnt
        {
            get;
            set;
        }

        public int? HandoverSuccessCnt
        {
            get;
            set;
        }

        public string DateTimeStr
        {
            get;
            set;
        }

        public string CityName
        {
            get;
            set;
        }

        public string SiteName
        {
            get;
            set;
        }

        public string IsPass
        {
            get;
            set;
        }

        public string SiteTypeName
        {
            get;
            set;
        }

        public int EnodebID
        {
            get;
            set;
        }

        public List<AcceptHistoryCell> HistoryCells
        {
            get;
            private set;
        }
    }

    class AcceptHistoryCell
    {
        public long SID
        {
            get;
            set;
        }

        public string CellStr
        {
            get;
            set;
        }

        public string IsPass
        {
            set;
            get;
        }

        public double? DownSinrGood
        {
            get;
            set;
        }

        public double? DownSinrTall
        {
            get;
            set;
        }

        public double? DownSinrBad
        {
            get;
            set;
        }

        public double? UpSinrGood
        {
            get;
            set;
        }

        public double? UpSinrTall
        {
            get;
            set;
        }

        public double? UpSinrBad
        {
            get;
            set;
        }

        public double? DownRsrpGood
        {
            get;
            set;
        }

        public double? DownRsrpTall
        {
            get;
            set;
        }

        public double? DownRsrpBad
        {
            get;
            set;
        }

        public double? UpRsrpGood
        {
            get;
            set;
        }

        public double? UpRsrpTall
        {
            get;
            set;
        }

        public double? UpRsrpBad
        {
            get;
            set;
        }

        public double? UpSpeedGood
        {
            get;
            set;
        }

        public double? UpSpeedTall
        {
            get;
            set;
        }

        public double? UpSpeedBad
        {
            get;
            set;
        }

        public double? DownSpeedGood
        {
            get;
            set;
        }

        public double? DownSpeedTall
        {
            get;
            set;
        }

        public double? DownSpeedBad
        {
            get;
            set;
        }

        public int? RrcRequestCnt
        {
            get;
            set;
        }

        public int? RrcSuccessCnt
        {
            get;
            set;
        }

        public double? RrcSuccessRate
        {
            get { return RrcRequestCnt == null || RrcRequestCnt == 0 ? null : RrcSuccessCnt / RrcRequestCnt; }
        }

        public int? ErabRequestCnt
        {
            get;
            set;
        }

        public int? ErabSuccessCnt
        {
            get;
            set;
        }

        public double? ErabSuccessRate
        {
            get { return ErabRequestCnt == null || ErabRequestCnt == 0 ? null : ErabSuccessCnt / ErabRequestCnt; }
        }

        public int? CsfbRequestCnt
        {
            get;
            set;
        }

        public int? CsfbSuccessCnt
        {
            get;
            set;
        }

        public double? CsfbSuccessRate
        {
            get { return CsfbRequestCnt == null || CsfbRequestCnt == 0 ? null : CsfbSuccessCnt / CsfbRequestCnt; }
        }

        public int? AccessRequestCnt
        {
            get;
            set;
        }

        public int? AccessSuccessCnt
        {
            get;
            set;
        }

        public double? AccessSuccessRate
        {
            get { return AccessRequestCnt == null || AccessRequestCnt == 0 ? null : AccessSuccessCnt / AccessRequestCnt; }
        }

        public int? ReselectRequestCnt
        {
            get;
            set;
        }

        public int? ReselectSuccessCnt
        {
            get;
            set;
        }

        public double? ReselectSuccessRate
        {
            get { return ReselectRequestCnt == null || ReselectRequestCnt == 0 ? null : ReselectSuccessCnt / ReselectRequestCnt; }
        }

        public int? HandoverRequestCnt
        {
            get;
            set;
        }

        public int? HandoverSuccessCnt
        {
            get;
            set;
        }

        public double? HandoverSuccessRate
        {
            get { return HandoverRequestCnt == null || HandoverRequestCnt == 0 ? null : HandoverSuccessCnt / HandoverRequestCnt; }
        }

        public double? CallSuccessRate
        {
            get { return RrcSuccessRate * ErabSuccessRate; }
        }
    }
}
