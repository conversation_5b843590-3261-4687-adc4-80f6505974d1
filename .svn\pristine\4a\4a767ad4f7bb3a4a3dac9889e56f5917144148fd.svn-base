﻿using MasterCom.RAMS.Grid;
using MasterCom.RAMS.KPI_Statistics;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTWirelessNetTest
{
    public static class WirelessNetTestStatHelper
    {
        #region 基于图层 - 添加对应网格内的数据
        /// <summary>
        /// 添加网格区域内的数据
        /// </summary>
        public static void AddValidGridData(Dictionary<string, ResvRegion> regionDic
            , List<KPIStatDataBase> dataList, bool isEvt, Dictionary<string, StatData> resSceneDic)
        {
            foreach (var data in dataList)
            {
                string regionName = getInRegionName(regionDic, data, isEvt);
                if (!string.IsNullOrEmpty(regionName))
                {
                    if (!resSceneDic.TryGetValue(regionName, out var statData))
                    {
                        statData = new StatData();
                        resSceneDic.Add(regionName, statData);
                    }
                    statData.AddStatData(data);
                }
            }
        }

        /// <summary>
        /// 根据数据的经纬度在图层列表中获取所属区域名
        /// </summary>
        private static string getInRegionName(Dictionary<string, ResvRegion> regionDic, KPIStatDataBase data
            , bool isEvt)
        {
            double centerLng;
            double centerLat;
            if (isEvt)
            {
                StatDataEvent evt = data as StatDataEvent;
                GridUnitBase grid = new GridUnitBase(evt.LTLng, evt.LTLat);
                centerLng = grid.CenterLng;
                centerLat = grid.CenterLat;
            }
            else
            {
                GridUnitBase grid = new GridUnitBase(data.LTLng, data.LTLat);
                centerLng = grid.CenterLng;
                centerLat = grid.CenterLat;
            }

            foreach (var region in regionDic)
            {
                if (region.Value.GeoOp.Contains(centerLng, centerLat))
                {
                    return region.Key;
                }
            }
            return null;
        }
        #endregion

        #region 基于场景 - 添加对应场景内的数据
        public static void AddValidSceneData<T>(Dictionary<int, T> sceneDic, List<KPIStatDataBase> dataList
            , Dictionary<T, StatData> resSceneDic)
        {
            foreach (var data in dataList)
            {
                if (sceneDic.TryGetValue(data.FileID, out var sceneInfo))
                {
                    if (!resSceneDic.TryGetValue(sceneInfo, out var statData))
                    {
                        statData = new StatData();
                        resSceneDic.Add(sceneInfo, statData);
                    }
                    statData.AddStatData(data);
                }
            }
        }

        public static void ReplayFile(int districtID, List<FileInfo> files)
        {
            QueryCondition condition = new QueryCondition();
            condition.DistrictID = districtID;
            condition.FileInfos.AddRange(files);
            DIYReplayFileWithNoWaitBox query = new DIYReplayFileWithNoWaitBox(MainModel.GetInstance());
            query.FilterSampleByRegion = false;
            List<string> columns = new List<string>();
            columns.Add("NR_NCI");
            columns.Add("NR_lte_ECI");
            columns.Add("lte_ECI");
            query.Columns = columns;
            query.IncludeEvent = false;
            query.SetQueryCondition(condition);
            query.Query();
        }
        #endregion

        #region
        public static WirelessNetTestStatBase.SceneCell DealSceneTestpoints(WirelessNetTestScene scene)
        {
            Dictionary<string, string> nrCellDic = new Dictionary<string, string>();
            Dictionary<string, string> nrBtsDic = new Dictionary<string, string>();

            Dictionary<string, string> lteCellDic = new Dictionary<string, string>();
            Dictionary<string, string> lteBtsDic = new Dictionary<string, string>();

            var fileDataManagers = MainModel.GetInstance().DTDataManager.FileDataManagers;
            foreach (DTFileDataManager fileDataManager in fileDataManagers)
            {
                foreach (TestPoint tp in fileDataManager.TestPoints)
                {
                    if (tp is TestPoint_NR)
                    {
                        var info = getNrCell(tp);
                        getSceneBtsCell(scene.NrBtsDic, nrCellDic, nrBtsDic, info);

                        info = getNrLteCell(tp);
                        getSceneBtsCell(scene.LteBtsDic, lteCellDic, lteBtsDic, info);
                    }
                    else if (tp is LTETestPointDetail)
                    {
                        var info = getLteCell(tp);
                        getSceneBtsCell(scene.LteBtsDic, lteCellDic, lteBtsDic, info);
                    }
                }
            }

            var sceneCell = new WirelessNetTestStatBase.SceneCell();
            sceneCell.LteBtsOccupyRate.Count = lteBtsDic.Count;
            sceneCell.LteBtsOccupyRate.TotalCount = scene.LteBtsCount;

            sceneCell.LteCellOccupyRate.Count = lteCellDic.Count;
            sceneCell.LteCellOccupyRate.TotalCount = scene.LteCellCount;

            sceneCell.NrBtsOccupyRate.Count = nrBtsDic.Count;
            sceneCell.NrBtsOccupyRate.TotalCount = scene.NrBtsCount;

            sceneCell.NrCellOccupyRate.Count = nrCellDic.Count;
            sceneCell.NrCellOccupyRate.TotalCount = scene.NrCellCount;

            sceneCell.Calculate();
            return sceneCell;
        }

        private static void getSceneBtsCell(Dictionary<string, WirelessNetTestBts> sceneBtsDic, Dictionary<string, string> cellDic, Dictionary<string, string> btsDic, TPCellInfo info)
        {
            if (info !=null && !string.IsNullOrEmpty(info.ENodebID) 
                && sceneBtsDic.TryGetValue(info.ENodebID, out var btsInfo))
            {
                cellDic[info.ENodebID] = "";
                if (btsInfo.CIDic.TryGetValue(info.CI, out _))
                {
                    btsDic[info.CI] = "";
                }
            }
        }

        private static TPCellInfo getNrCell(TestPoint tp)
        {
            long? nci = (long?)NRTpHelper.NrTpManager.GetNCI(tp);
            if (nci == null)
            {
                return null;
            }

            long btsID = (long)nci / 4096;
            //long cellID = (long)nci % 4096;
            //string cgi = $"460-00-{btsID}-{cellID}";
            TPCellInfo info = new TPCellInfo()
            {
                ENodebID = btsID.ToString(),
                CI = nci.ToString()
            };
            return info;
        }

        private static TPCellInfo getNrLteCell(TestPoint tp)
        {
            int? eci = (int?)NRTpHelper.NrLteTpManager.GetNCI(tp);
            if (eci == null)
            {
                return null;
            }

            int btsID = (int)eci / 256;
            //int cellID = (int)eci % 256;
            //string cgi = $"460-00-{btsID}-{cellID}";
            TPCellInfo info = new TPCellInfo()
            {
                ENodebID = btsID.ToString(),
                CI = eci.ToString()
            };
            return info;
        }

        private static TPCellInfo getLteCell(TestPoint tp)
        {
            int? eci = (int?)tp["lte_ECI"];
            if (eci == null)
            {
                return null;
            }

            int btsID = (int)eci / 256;
            //int cellID = (int)eci % 256;
            //string cgi = $"460-00-{btsID}-{cellID}";
            TPCellInfo info = new TPCellInfo()
            {
                ENodebID = btsID.ToString(),
                CI = eci.ToString()
            };
            return info;
        }

        private class TPCellInfo
        {
            public string ENodebID { get; set; } = "";
            public string CI { get; set; } = "";
        }
        #endregion
    }
}
