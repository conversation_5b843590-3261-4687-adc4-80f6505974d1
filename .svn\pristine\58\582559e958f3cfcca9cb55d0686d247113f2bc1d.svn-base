﻿using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    class NRUrlAnaByRegion : DIYAnalyseFilesOneByOneByRegion
    {
        public NRUrlAnaByRegion(MainModel mainModel)
             : base(mainModel)
        {
            this.FilterEventByRegion = false;
            this.IncludeMessage = true;
            this.IncludeTestPoint = false;

            analyzer = new NRUrlAnalyzerByRegion();
        }
        protected List<DTFileDataManager> fileManagers = null;
        private readonly NRUrlAnalyzerByRegion analyzer;
        protected Dictionary<string, List<ResvRegion>> resvRegionsDic = null;

        private static NRUrlAnaByRegion instance;
        public static NRUrlAnaByRegion GetInstance()
        {
            if (instance == null)
            {
                instance = new NRUrlAnaByRegion(MainModel.GetInstance());
            }
            return instance;
        }

        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager fileMng in MainModel.DTDataManager.FileDataManagers)
            {
                DTFileDataManager file = new DTFileDataManager(fileMng.FileID, 
                    fileMng.FileName, 
                    fileMng.ProjectType, 
                    fileMng.TestType,
                    fileMng.CarrierType, 
                    fileMng.LogTable, 
                    fileMng.SampleTableName, 
                    fileMng.ServiceType, 
                    fileMng.MoMtFlag);
                foreach (Event evt in fileMng.Events)
                {
                    if (ZTUrlAnalyzer.HttpIDList.Contains(evt.ID)
                        || ZTUrlAnalyzer.DownloadIDList.Contains(evt.ID)
                        || ZTUrlAnalyzer.VideoIDList.Contains(evt.ID))
                    {
                        file.Add(evt);
                    }
                }
                foreach (MasterCom.RAMS.Model.Message msg in fileMng.Messages)
                {
                    if (msg.ID == (int)NRMsgManager.Http_Page_Request
                        || msg.ID == (int)NRMsgManager.Http_Download_Begin
                        || msg.ID == (int)NRMsgManager.Video_Play_Request
                        || msg.ID == (int)NRMsgManager.Http_Page_Start)
                    {
                        file.Add(msg);
                    }
                }
                fileManagers.Add(file);
            }
        }

        protected override void getResultsAfterQuery()
        {
            analyzer.Analyze(fileManagers);
        }

        protected override void fireShowForm()
        {
            NRUrlAnaForm resultForm = MainModel.CreateResultForm(typeof(NRUrlAnaForm)) as NRUrlAnaForm;
            resultForm.FillData(analyzer.ResultsRegion, true);
            resultForm.Visible = true;
            resultForm.BringToFront();
        }

        protected override void getReadyBeforeQuery()
        {
            if (fileManagers == null)
            {
                fileManagers = new List<DTFileDataManager>();
            }
            else
            {
                fileManagers.Clear();
            }
        }

        protected override void clearDataBeforeAnalyseFiles()
        {
            resvRegionsDic = condition.Geometorys.SelectedResvRegionDic;
            if (resvRegionsDic != null && resvRegionsDic.Count > 0)
            {
                analyzer.SetResvRegion(resvRegionsDic);
            }
            else
            {
                List<ResvRegion> ress = new List<ResvRegion>();
                condition.Geometorys.RegionInfo.Shape = Condition.Geometorys.RegionInfo.Shape;
                ResvRegion resTemp = condition.Geometorys.RegionInfo;
                ress.Add(resTemp);
                resvRegionsDic = new Dictionary<string, List<ResvRegion>>();
                resvRegionsDic.Add("自选区域", ress);
                analyzer.SetResvRegion(resvRegionsDic);
            }
        }

        public override string Name
        {
            get { return "URL统计分析(按区域)"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 35000, 35052, this.Name);
        }
        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.Region;
        }
    }
}
