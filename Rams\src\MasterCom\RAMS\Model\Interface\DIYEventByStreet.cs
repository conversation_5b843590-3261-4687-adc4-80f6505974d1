﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Stat;
using MasterCom.MTGis;

namespace MasterCom.RAMS.Net
{
    public class DIYEventByStreet : DIYEventQuery
    {
        public DIYEventByStreet(MainModel mainModel)
            : base(mainModel)
        {
        }
        public override string Name
        {
            get { return "事件查询(按道路)"; }
        }
        public override string IconName
        {
            get { return "Images/regionevt.gif"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 11000, 11005, this.Name);
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.Street;
        }

        protected override void AddDIYRegion_Intersect(Package package)
        {
            package.Content.AddParam((byte)OpOptionDef.AreaSelectIntersect);
            package.Content.AddParam(condition.Geometorys.Polygon.Bounds.x1);
            package.Content.AddParam(condition.Geometorys.Polygon.Bounds.y2);
            package.Content.AddParam(condition.Geometorys.Polygon.Bounds.x2);
            package.Content.AddParam(condition.Geometorys.Polygon.Bounds.y1);
        }
        protected override void prepareOtherEventFilter(Package package)
        {
            AddDIYStreets_Sample(package);
        }
        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            if (searchGeometrys.SelectedStreets.Count <= 0)
            {
                return false;
            }
            return true;
        }
        protected override bool isValidPoint(double jd, double wd)
        {
            return intersects(jd, wd);
        }

        private bool intersects(double longitude, double latitude)
        {
            return Condition.Geometorys.GeoOp.Contains(longitude,latitude);
        }

        protected override bool prepareAskWhatEvent()
        {
            if (showEventChooser)
            {
                return base.prepareAskWhatEvent();
            }
            else
            {
                Condition.EventIDs = new List<int>();
                Condition.EventIDs.Add(-1);
                return true;
            }
        }
    }
}
