﻿using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.BackgroundFunc
{
    #region 室外站
    public class NbIotOutDoorBtsAcceptInfoXJ
    {
        public NbIotOutDoorBtsAcceptInfoXJ(LTEBTS bts, string btsName)
        {
            this.NBIOTBts = bts;
            BtsName = btsName;
            IsAccordAccept = false;
            CellsAcceptDic = new Dictionary<int, NbIotOutDoorCellAcceptInfoXJ>();
        }

        public LTEBTS NBIOTBts { get; set; }
        public string BtsName { get; private set; }
        public int BtsId { get { return NBIOTBts.BTSID; } }
        public double Longitude { get { return NBIOTBts.Longitude; } }
        public double Latitude { get { return NBIOTBts.Latitude; } }

        /// <summary>
        /// 不通过原因描述
        /// </summary>
        public string NotAccordKpiDes { get; set; }

        /// <summary>
        /// 站点(所有小区)是否验收合格描述
        /// </summary>
        public string IsAccordAcceptStr { get { return IsAccordAccept ? "是" : "否"; } }

        /// <summary>
        /// 判断站点(所有小区)是否验收合格,更改上传工参表中描述状态
        /// </summary>
        public bool IsAccordAccept { get; set; }

        //各小区验收信息key:cellid
        public Dictionary<int, NbIotOutDoorCellAcceptInfoXJ> CellsAcceptDic { get; set; }

        #region 各项是否通过结论描述
        /// <summary>
        /// 覆盖
        /// </summary>
        public string IsCoverAccordDes { get; set; }
        /// <summary>
        /// ATTACH成功率
        /// </summary>
        public string IsAttachAccordDes { get; set; }
        /// <summary>
        /// 重选
        /// </summary>
        public string IsReselectionDes { get; set; }
        /// <summary>
        /// PING成功率
        /// </summary>
        public string IsPingRateAccordDes { get; set; }
        /// <summary>
        /// PING时延
        /// </summary>
        public string IsPingAccordDes { get; set; }
        /// <summary>
        /// 极好点上传RSRP
        /// </summary>
        public string IsULRSRPAccordDes { get; set; }
        /// <summary>
        /// 极好点上传SINR
        /// </summary>
        public string IsULSINRAccordDes { get; set; }
        /// <summary>
        /// 极好点上传速率
        /// </summary>
        public string IsULThroughputAccordDes { get; set; }
        /// <summary>
        /// 极好点下载速率
        /// </summary>
        public string IsDLThroughputAccordDes { get; set; }
        #endregion

        /// <summary>
        /// 检验基站和各小区指标是否合格,获取完基站和各小区的指标信息后调用
        /// </summary>
        public virtual void CheckBtsIsAccordAccept()
        {
            StringBuilder strbNotAccordDes = new StringBuilder();
            bool allCellAccord = true;
            foreach (LTECell cell in NBIOTBts.Cells)
            {
                NbIotOutDoorCellAcceptInfoXJ cellInfo;
                if (this.CellsAcceptDic.TryGetValue(cell.CellID, out cellInfo))
                {
                    cellInfo.CheckCellIsAccordAccept();
                    setBtsKpiInfo(cellInfo);
                    if (!cellInfo.IsAccord)
                    {
                        allCellAccord = false;
                        strbNotAccordDes.AppendLine(cell.Name + "小区不达标的指标：" + cellInfo.NotAccordKpiDes);
                    }
                }
                else
                {
                    cellInfo = new NbIotOutDoorCellAcceptInfoXJ(cell);
                    setBtsKpiInfo(cellInfo);
                    allCellAccord = false;
                    strbNotAccordDes.AppendLine(cell.Name + "小区未查询到指标信息;");
                }
            }
            NotAccordKpiDes = strbNotAccordDes.ToString();
            IsAccordAccept = allCellAccord;
        }

        /// <summary>
        /// 设置各项最终是否合格
        /// </summary>
        /// <param name="cellInfo"></param>
        protected void setBtsKpiInfo(NbIotOutDoorCellAcceptInfoXJ cellInfo)
        {
            this.IsAttachAccordDes = isKpiAccord(this.IsAttachAccordDes, cellInfo.AttachRate.IsAccord);
            this.IsPingRateAccordDes = isKpiAccord(this.IsPingRateAccordDes, cellInfo.PingRate.IsAccord);
            this.IsPingAccordDes = isKpiAccord(this.IsPingAccordDes, cellInfo.PingDelay.IsAccord);     
            this.IsULRSRPAccordDes = isKpiAccord(this.IsULRSRPAccordDes, cellInfo.ULRSRP.IsAccord);
            this.IsULSINRAccordDes = isKpiAccord(this.IsULSINRAccordDes, cellInfo.ULSINR.IsAccord);
            this.IsULThroughputAccordDes = isKpiAccord(this.IsULThroughputAccordDes, cellInfo.ULThroughput.IsAccord);
            this.IsDLThroughputAccordDes = isKpiAccord(this.IsDLThroughputAccordDes, cellInfo.DLThroughput.IsAccord);
        }

        private string isKpiAccord(string btsKpiDes, bool cellKpi)
        {
            if (btsKpiDes == null)
            {
                return cellKpi ? "是" : "否";
            }

            if ((btsKpiDes == "是") && cellKpi)
            {
                return "是";
            }
            return "否";
        }
    }

    public class NbIotOutDoorCellAcceptInfoXJ
    {
        public NbIotOutDoorCellAcceptInfoXJ(LTECell cell)
        {
            this.NBIOTCell = cell;
        }

        protected List<string> hasGotKpiKeyList = new List<string>();//已经获取到的指标Key集合

        protected bool isAccord = false;
        public bool IsAccord { get { return isAccord; } }
        public string IsAccordDes { get { return isAccord ? "是" : "否"; } }
        public string NotAccordKpiDes { get; set; }
        public LTECell NBIOTCell { get; set; }

        public int CellId
        {
            get
            {
                if (NBIOTCell != null)
                {
                    return NBIOTCell.CellID;
                }
                return 0;
            }
        }

        public string CellName
        {
            get
            {
                if (NBIOTCell != null)
                {
                    return NBIOTCell.Name;
                }
                return "";
            }
        }

        private readonly NBIotKpiInfo attachRate = new NBIotKpiInfo("Attach成功率");
        public NBIotKpiInfo AttachRate { get { return attachRate; } }

        private readonly NBIotKpiInfo instationReselectRate = new NBIotKpiInfo("站内重选成功率");
        public NBIotKpiInfo InstationReselectRate { get { return instationReselectRate; } }

        private readonly NBIotKpiInfo betweenstationReselectRate = new NBIotKpiInfo("站间重选成功率");
        public NBIotKpiInfo BetweenstationReselectRate { get { return betweenstationReselectRate; } }

        private readonly NBIotKpiInfo pingRate = new NBIotKpiInfo("Ping成功率");
        public NBIotKpiInfo PingRate { get { return pingRate; } }

        private readonly NBIotKpiInfo pingDelay = new NBIotKpiInfo("Ping时延");
        public NBIotKpiInfo PingDelay { get { return pingDelay; } }

        private readonly NBIotKpiInfo ulRSRP = new NBIotKpiInfo("极好点RSRP");
        public NBIotKpiInfo ULRSRP { get { return ulRSRP; } }

        private readonly NBIotKpiInfo ulSINR = new NBIotKpiInfo("极好点SINR");
        public NBIotKpiInfo ULSINR { get { return ulSINR; } }

        private readonly NBIotKpiInfo ulThroughput = new NBIotKpiInfo("上传吞吐率");
        public NBIotKpiInfo ULThroughput { get { return ulThroughput; } }

        private readonly NBIotKpiInfo dlThroughput = new NBIotKpiInfo("下载吞吐率");
        public NBIotKpiInfo DLThroughput { get { return dlThroughput; } }

        private readonly NBIotPicInfo coverProperty = new NBIotPicInfo("覆盖");
        public NBIotPicInfo CoverProperty { get { return coverProperty; } }

        public void AddAcceptKpiInfo(Dictionary<uint, object> kpiDic)
        {
            statsKpiNewestValues(kpiDic);

            dealWithData();
        }

        protected void dealWithData()
        {
            attachRate.DealWithData();
            pingRate.DealWithData();
            pingDelay.DealWithData();
            ulRSRP.DealWithData();
            ulSINR.DealWithData();
            ulThroughput.DealWithData();
            dlThroughput.DealWithData();
            coverProperty.DealWithData();
        }

        /// <summary>
        /// 统计各指标的最新一个指标值
        /// </summary>
        /// <param name="kpiDic"></param>
        protected void statsKpiNewestValues(Dictionary<uint, object> kpiDic)
        {
            foreach (uint key in kpiDic.Keys)
            {
                NbIotKpiKeyXJ kpiKey = (NbIotKpiKeyXJ)key;

                if (!hasGotKpiKeyList.Contains(kpiKey.ToString()))
                {
                    object objValue = kpiDic[key];
                    hasGotKpiKeyList.Add(kpiKey.ToString());

                    statsKpiNewestValue(kpiKey, objValue);
                }
            }
        }

        protected virtual void statsKpiNewestValue(NbIotKpiKeyXJ kpiKey, object objValue)
        {
            switch (kpiKey)
            {
                case NbIotKpiKeyXJ.AttachRate:
                    this.attachRate.SuccessRate = (double)objValue;
                    break;
                case NbIotKpiKeyXJ.InStationReselection:
                    this.instationReselectRate.SuccessRate = (double)objValue;
                    break;
                case NbIotKpiKeyXJ.BetweenStationReselection:
                    this.betweenstationReselectRate.SuccessRate = (double)objValue;
                    break;
                case NbIotKpiKeyXJ.PingRate:
                    this.pingRate.SuccessRate = (double)objValue;
                    break;
                case NbIotKpiKeyXJ.PingDelay:
                    this.pingDelay.Data = (double)objValue;
                    break;
                case NbIotKpiKeyXJ.DLThroughputRate:
                    this.dlThroughput.Data = (double)objValue;
                    break;

                case NbIotKpiKeyXJ.AttachRateDis:
                    this.attachRate.Distance = (double)objValue;
                    break;
                case NbIotKpiKeyXJ.ReselectionDis:
                    this.betweenstationReselectRate.Distance = (double)objValue;
                    this.instationReselectRate.Distance = (double)objValue;
                    break;
                case NbIotKpiKeyXJ.PingRateDis:
                    this.pingRate.Distance = (double)objValue;
                    break;
                case NbIotKpiKeyXJ.PingDelayDis:
                    this.pingDelay.Distance = (double)objValue;
                    break;
                case NbIotKpiKeyXJ.DLThroughputRateDis:
                    this.dlThroughput.Distance = (double)objValue;
                    break;
                case NbIotKpiKeyXJ.ULThroughputRateDis:
                    this.ulThroughput.Distance = (double)objValue;
                    break;
            }

            getULData(kpiKey, objValue);
            getCoverData(kpiKey, objValue);
        }

        protected virtual void getULData(NbIotKpiKeyXJ kpiKey, object objValue)
        {
            switch (kpiKey)
            {
                case NbIotKpiKeyXJ.ULAvgRSRP:
                    this.ulRSRP.Data = (double)objValue;
                    break;
                case NbIotKpiKeyXJ.ULAvgSINR:
                    this.ulSINR.Data = (double)objValue;
                    break;
                case NbIotKpiKeyXJ.ULThroughputRate:
                    this.ulThroughput.Data = (double)objValue;
                    break;
            }
        }

        protected virtual void getCoverData(NbIotKpiKeyXJ kpiKey, object objValue)
        {
            switch (kpiKey)
            {
                case NbIotKpiKeyXJ.CoverRsrpPath:
                    this.coverProperty.CoverPicPath_Rsrp = objValue.ToString();
                    break;
                case NbIotKpiKeyXJ.CoverSinrPath:
                    this.coverProperty.CoverPicPath_Sinr = objValue.ToString();
                    break;
                case NbIotKpiKeyXJ.CoverULPath:
                    this.coverProperty.CoverPicPath_UL = objValue.ToString();
                    break;
                case NbIotKpiKeyXJ.CoverAvgRSRP:
                    this.coverProperty.AvgRSRP = (double)objValue;
                    break;
                case NbIotKpiKeyXJ.CoverAvgSINR:
                    this.coverProperty.AvgSINR = (double)objValue;
                    break;
                case NbIotKpiKeyXJ.CoverAvgSpeed:
                    this.coverProperty.CoverAvgSpeed = (double)objValue;
                    break;
                case NbIotKpiKeyXJ.CoverSpeedUpTen:
                    this.coverProperty.CoverSpeedUpTen = (double)objValue;
                    break;
                case NbIotKpiKeyXJ.CoverCoverRate:
                    this.coverProperty.CoverCoverRate = (double)objValue;
                    break;
            }
        }


        /// <summary>
        /// 检验小区指标是否合格,获取完小区各指标信息后调用
        /// </summary>
        /// <returns></returns>
        public void CheckCellIsAccordAccept()
        {
            attachRate.CheckIsAccordRate(100);
            pingRate.CheckIsAccordRate(100);
            pingDelay.CheckIsAccordData(double.MinValue, 1.5);
            ulRSRP.CheckIsAccordData(-80, double.MaxValue);
            ulSINR.CheckIsAccordData(20, double.MaxValue);
            ulThroughput.CheckIsAccordData(10, double.MaxValue);
            dlThroughput.CheckIsAccordData(13, double.MaxValue);

            bool isAllKpiAccord = true;
            StringBuilder strbNotAccordKpiName = new StringBuilder();
            strbNotAccordKpiName.Append(getNotAccordKpiInfo(this.attachRate, ref isAllKpiAccord));
            strbNotAccordKpiName.Append(getNotAccordKpiInfo(this.pingRate, ref isAllKpiAccord));
            strbNotAccordKpiName.Append(getNotAccordKpiInfo(this.pingDelay, ref isAllKpiAccord));
            strbNotAccordKpiName.Append(getNotAccordKpiInfo(this.ulRSRP, ref isAllKpiAccord));
            strbNotAccordKpiName.Append(getNotAccordKpiInfo(this.ulSINR, ref isAllKpiAccord));
            strbNotAccordKpiName.Append(getNotAccordKpiInfo(this.ulThroughput, ref isAllKpiAccord));
            strbNotAccordKpiName.Append(getNotAccordKpiInfo(this.dlThroughput, ref isAllKpiAccord));

            this.isAccord = isAllKpiAccord;
            this.NotAccordKpiDes = strbNotAccordKpiName.ToString();
        }

        protected string getNotAccordKpiInfo(NBIotKpiInfo kpi, ref bool isAllKpiAccord)
        {
            return getNotAccordKpiInfo(kpi.IsAccord, kpi.KpiName, ref isAllKpiAccord);
        }

        protected string getNotAccordKpiInfo(bool isKpiAccord, string strKpiName, ref bool isAllKpiAccord)
        {
            if (!isKpiAccord)
            {
                isAllKpiAccord = false;
                return strKpiName + ";";
            }
            return string.Empty;
        }
    }
    #endregion
}
