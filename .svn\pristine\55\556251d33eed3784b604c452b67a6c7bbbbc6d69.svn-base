﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class MoBlockCallResultListForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.ListViewMoBlockCall = new BrightIdeasSoftware.TreeListView();
            this.olvColumnStatSN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMoFileName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnTime = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnIsContain = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.ctmStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.ToolStripReplay = new System.Windows.Forms.ToolStripMenuItem();
            this.ToolStripExport = new System.Windows.Forms.ToolStripMenuItem();
            this.olvColumnDate = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            ((System.ComponentModel.ISupportInitialize)(this.ListViewMoBlockCall)).BeginInit();
            this.ctmStrip.SuspendLayout();
            this.SuspendLayout();
            // 
            // ListViewMoBlockCall
            // 
            this.ListViewMoBlockCall.AllColumns.Add(this.olvColumnStatSN);
            this.ListViewMoBlockCall.AllColumns.Add(this.olvColumnMoFileName);
            this.ListViewMoBlockCall.AllColumns.Add(this.olvColumnDate);
            this.ListViewMoBlockCall.AllColumns.Add(this.olvColumnTime);
            this.ListViewMoBlockCall.AllColumns.Add(this.olvColumnIsContain);
            this.ListViewMoBlockCall.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnStatSN,
            this.olvColumnMoFileName,
            this.olvColumnDate,
            this.olvColumnTime,
            this.olvColumnIsContain});
            this.ListViewMoBlockCall.Cursor = System.Windows.Forms.Cursors.Default;
            this.ListViewMoBlockCall.Dock = System.Windows.Forms.DockStyle.Fill;
            this.ListViewMoBlockCall.FullRowSelect = true;
            this.ListViewMoBlockCall.GridLines = true;
            this.ListViewMoBlockCall.HeaderWordWrap = true;
            this.ListViewMoBlockCall.IsNeedShowOverlay = false;
            this.ListViewMoBlockCall.Location = new System.Drawing.Point(0, 0);
            this.ListViewMoBlockCall.Name = "ListViewMoBlockCall";
            this.ListViewMoBlockCall.OwnerDraw = true;
            this.ListViewMoBlockCall.ShowGroups = false;
            this.ListViewMoBlockCall.Size = new System.Drawing.Size(604, 450);
            this.ListViewMoBlockCall.TabIndex = 9;
            this.ListViewMoBlockCall.UseCompatibleStateImageBehavior = false;
            this.ListViewMoBlockCall.View = System.Windows.Forms.View.Details;
            this.ListViewMoBlockCall.VirtualMode = true;
            // 
            // olvColumnStatSN
            // 
            this.olvColumnStatSN.AspectName = "";
            this.olvColumnStatSN.HeaderFont = null;
            this.olvColumnStatSN.Text = "序号";
            this.olvColumnStatSN.Width = 40;
            // 
            // olvColumnMoFileName
            // 
            this.olvColumnMoFileName.HeaderFont = null;
            this.olvColumnMoFileName.Text = "主叫文件";
            this.olvColumnMoFileName.Width = 250;
            // 
            // olvColumnTime
            // 
            this.olvColumnTime.HeaderFont = null;
            this.olvColumnTime.Text = "信令开始时间";
            this.olvColumnTime.Width = 80;
            // 
            // olvColumnIsContain
            // 
            this.olvColumnIsContain.HeaderFont = null;
            this.olvColumnIsContain.Text = "是否包含IMS_SIP_INVITE->Session_Progress (183)";
            this.olvColumnIsContain.Width = 150;
            // 
            // ctmStrip
            // 
            this.ctmStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.ToolStripReplay,
            this.ToolStripExport});
            this.ctmStrip.Name = "ctmStrip";
            this.ctmStrip.Size = new System.Drawing.Size(142, 48);
            // 
            // ToolStripReplay
            // 
            this.ToolStripReplay.Name = "ToolStripReplay";
            this.ToolStripReplay.Size = new System.Drawing.Size(141, 22);
            this.ToolStripReplay.Text = "回放文件";
            this.ToolStripReplay.Click += new System.EventHandler(this.ToolStripReplay_Click);
            // 
            // ToolStripExport
            // 
            this.ToolStripExport.Name = "ToolStripExport";
            this.ToolStripExport.Size = new System.Drawing.Size(141, 22);
            this.ToolStripExport.Text = "导出到Excel";
            this.ToolStripExport.Click += new System.EventHandler(this.ToolStripExport_Click);
            // 
            // olvColumnDate
            // 
            this.olvColumnDate.HeaderFont = null;
            this.olvColumnDate.Text = "信令开始日期";
            this.olvColumnDate.Width = 80;
            // 
            // MoBlockCallResultListForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(604, 450);
            this.ContextMenuStrip = this.ctmStrip;
            this.Controls.Add(this.ListViewMoBlockCall);
            this.Name = "MoBlockCallResultListForm";
            this.Text = "主叫未接通结果分析";
            ((System.ComponentModel.ISupportInitialize)(this.ListViewMoBlockCall)).EndInit();
            this.ctmStrip.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private BrightIdeasSoftware.TreeListView ListViewMoBlockCall;
        private BrightIdeasSoftware.OLVColumn olvColumnStatSN;
        private BrightIdeasSoftware.OLVColumn olvColumnMoFileName;
        private BrightIdeasSoftware.OLVColumn olvColumnTime;
        private BrightIdeasSoftware.OLVColumn olvColumnIsContain;
        private System.Windows.Forms.ContextMenuStrip ctmStrip;
        private System.Windows.Forms.ToolStripMenuItem ToolStripReplay;
        private System.Windows.Forms.ToolStripMenuItem ToolStripExport;
        private BrightIdeasSoftware.OLVColumn olvColumnDate;

    }
}