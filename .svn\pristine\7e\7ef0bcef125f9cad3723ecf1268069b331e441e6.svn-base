﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class WeakSinrRoadCompareCondition : WeakSINRRoadCondition
    {

        public TimePeriod Period1 { get; set; }
        public TimePeriod Period2 { get; set; }

        public double GridSpanDegree
        {
            get
            {
                return GridSpanM*0.00001;
            }
        }

        public double GridSpanM { get; set; }
    }

}
