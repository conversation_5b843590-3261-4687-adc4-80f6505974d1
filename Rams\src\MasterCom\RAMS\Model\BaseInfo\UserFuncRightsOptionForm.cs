﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraTreeList.Nodes;
using MasterCom.RAMS.Func;
using MasterCom.Util;
using MasterCom.Util.UiEx;
using MasterCom.RAMS.UserMng;
using DevExpress.XtraTreeList;
using DevExpress.XtraTreeList.Columns;

namespace MasterCom.RAMS.Model.BaseInfo
{
    public partial class UserFuncRightsOptionForm : BaseForm
    {
        bool isShowExportPermitColumn = false;
        private List<User> modifiedUsers = new List<User>();
        private List<FunctionRole> modifiedRoles = new List<FunctionRole>();
        private List<FunctionRole> modifiedRoles_Export = new List<FunctionRole>();

        private void addModifiedUser(User usr)
        {
            if (!modifiedUsers.Contains(usr))
            {
                modifiedUsers.Add(usr);
            }
        }

        public UserFuncRightsOptionForm(MainModel mm)
            : base(mm)
        {
            InitializeComponent();
            if (!mm.PermissionManager.HasQueriedUsers)
            {
                QueryUserPermission query = new QueryUserPermission(mm);
                query.Query();
            }

#if PermissionControl_DataExport
            isShowExportPermitColumn = true;
            colFuncExportPermit.Visible = colFuncExportZip.Visible = colFuncExportLog.Visible = colFuncExportCause.Visible 
            = colFuncIsNeedHideKeyInfo.Visible = true;
            
            this.treeListFunc.CellValueChanging += this.treeListFunc_CellValueChanging;
#else
            isShowExportPermitColumn = false;
            colFuncExportPermit.Visible = colFuncExportZip.Visible = colFuncExportLog.Visible = colFuncExportCause.Visible 
            = colFuncIsNeedHideKeyInfo.Visible = false;
#endif

            fillViews();
        }

        private void fillViews()
        {
            gvUser.SelectionChanged -= gvUser_SelectionChanged;
            fillFuncView();
            fillRoleView();
            fillUserView();
            gvUser.SelectionChanged += gvUser_SelectionChanged;
        }

        private void fillUserView()
        {
            gridCtrlUser.BeginInit();
            gridCtrlUser.DataSource = MainModel.PermissionManager.Users;
            gridCtrlUser.RefreshDataSource();
            gridCtrlUser.EndInit();
        }

        private void fillRoleView()
        {
            treeListFunc.BeginUnboundLoad();
            treeListRole.FocusedNodeChanged -= treeListRole_FocusedNodeChanged;
            treeListRole.AfterCheckNode -= treeListRole_AfterCheckNode;
            treeListRole.BeforeCheckNode -= treeListFunc_BeforeCheckNode;
            treeListRole.CellValueChanged -= treeListRole_CellValueChanged;
            treeListRole.Nodes.Clear();
            foreach (FunctionRole role in MainModel.PermissionManager.FunctionRoles)
            {
                TreeListNode root = treeListRole.AppendNode(new object[] { role.Name, role.Description }, null);
                root.Tag = role;
            }
            treeListRole.FocusedNodeChanged += treeListRole_FocusedNodeChanged;
            treeListRole.AfterCheckNode += treeListRole_AfterCheckNode;
            treeListRole.BeforeCheckNode += treeListFunc_BeforeCheckNode;
            treeListRole.CellValueChanged += treeListRole_CellValueChanged;
            treeListFunc.EndUnboundLoad();
            treeListFunc.Refresh();
            if (treeListRole.Nodes.Count>0)
            {
                treeListRole.SetFocusedNode(treeListRole.Nodes[0]);
                setFuncListCheckState((FunctionRole)treeListRole.Nodes[0].Tag);
            }
        }

        void treeListRole_AfterCheckNode(object sender, DevExpress.XtraTreeList.NodeEventArgs e)
        {
            if (e.Node.Tag is FunctionRole && curSelUser != null)
            {
                FunctionRole role = e.Node.Tag as FunctionRole;
                if (role != null)
                {
                    curSelUser.UpdateRole(role.ID, e.Node.Checked);
                    gridCtrlUser.RefreshDataSource();
                    addModifiedUser(curSelUser);
                }
            }
        }
        FunctionRole curSelRole = null;
        void treeListRole_FocusedNodeChanged(object sender, DevExpress.XtraTreeList.FocusedNodeChangedEventArgs e)
        {
            if (e.Node != null && e.Node.Tag is FunctionRole)
            {
                setFuncListCheckState((FunctionRole)e.Node.Tag);
            }
            else
            {
                curSelRole = null;
            }
        }

        private void fillFuncView()
        {
            treeListFunc.Nodes.Clear();
            treeListFunc.BeginUnboundLoad();
            List<MainFunction> funcSet = MainModel.PermissionManager.FuncList;
            foreach (MainFunction mFunc in funcSet)
            {
                TreeListNode mainFuncNode = treeListFunc.AppendNode(new object[] { mFunc.LoginName, mFunc.Description }, null);
                mainFuncNode.Tag = mFunc;
                mainFuncNode.HasChildren = mFunc.SubFuncList.Count > 0;
                foreach (SubFunction sFunc in mFunc.SubFuncList)
                {
                    TreeListNode subNode = treeListFunc.AppendNode(new object[] { sFunc.LoginName, sFunc.Description }, mainFuncNode);
                    subNode.Tag = sFunc;
                    subNode.HasChildren = false;
                }
            }
            treeListFunc.EndUnboundLoad();
            treeListFunc.Refresh();
        }


        User curSelUser = null;
        private void gvUser_SelectionChanged(object sender, DevExpress.Data.SelectionChangedEventArgs e)
        {
            int[] rowsHandle = gvUser.GetSelectedRows();
            if (rowsHandle.Length>0)
            {
                setRoleViewCheckState((User)gvUser.GetRow(rowsHandle[0]));
            }
        }
        private void setRoleViewCheckState(User selUser)
        {
            curSelUser = selUser;
            if (curSelUser == null)
            {
                return;
            }
            treeListRole.FocusedNodeChanged -= treeListRole_FocusedNodeChanged;
            treeListRole.AfterCheckNode -= treeListRole_AfterCheckNode;
            foreach (TreeListNode node in treeListRole.Nodes)
            {
                FunctionRole r = node.Tag as FunctionRole;
                if (r != null)
                {
                    node.Checked = curSelUser.FunctionRoleIDList.Contains(r.ID);
                }
            }
            treeListRole.FocusedNodeChanged += treeListRole_FocusedNodeChanged;
            treeListRole.AfterCheckNode += treeListRole_AfterCheckNode;
        }


        private void setFuncListCheckState(FunctionRole role)
        {
            if (role == null)
            {
                treeListFunc.Enabled = false;
                return;
            }
            else
            {
                treeListFunc.Enabled = true;
            }
            curSelRole = null;//先指向null，刷新treelist节点checkstate时，才不会同步更新到role。
            treeListFunc.BeginUpdate();
            foreach (TreeListNode root in treeListFunc.Nodes)
            {
                foreach (TreeListNode subNode in root.Nodes)
                {
                    SubFunction sub = subNode.Tag as SubFunction;
                    if (sub != null)
                    {
                        subNode.Checked = role.Permissions.Contains(sub.ID);

                        if (isShowExportPermitColumn)//显示专题结果导出鉴权列时
                        {
                            FuncExportPermit exportPermit;
                            role.FuncExportPermitDic.TryGetValue(sub.ID, out exportPermit);
                            setFuncExportPermit(subNode, exportPermit);
                        }

                        setCheckedParentNodes(subNode, subNode.CheckState);
                    }
                }
            }
            treeListFunc.EndUpdate();
            //最后才更新当前选择的role
            curSelRole = role;
        }

        #region 导出专题结果鉴权
        private void setFuncExportPermit(TreeListNode subNode, FuncExportPermit exportPermit)
        {
            if (exportPermit == null)
            {
                subNode.SetValue("IsCanExportResult", false);
                subNode.SetValue("IsExportToZip", false);
                subNode.SetValue("IsNeedExportLog", false);
                subNode.SetValue("IsNeedExportCause", false);
                subNode.SetValue("IsHideKeyInfo", false);
            }
            else
            {
                subNode.SetValue("IsCanExportResult", exportPermit.IsCanExportResult);
                subNode.SetValue("IsExportToZip", exportPermit.IsExportToZip);
                subNode.SetValue("IsNeedExportLog", exportPermit.IsNeedExportLog);
                subNode.SetValue("IsNeedExportCause", exportPermit.IsNeedExportCause);
                subNode.SetValue("IsHideKeyInfo", exportPermit.IsHideKeyInfo);
            }
        }
        private void treeListFunc_CellValueChanging(object sender, CellValueChangedEventArgs e)
        {
            if (isShowExportPermitColumn)
            {
                TreeListColumn treeCol = e.Column;
                TreeListNode node = e.Node;
                string colFieldName = treeCol.FieldName;
                bool isCheck = getColumnValueCheckedState(node, colFieldName) != true;

                node.SetValue(colFieldName, isCheck);
                setColumnValueChangdeChildNodes(node, colFieldName, isCheck);
                setColumnValueChangdeParentNodes(node, colFieldName, isCheck);
                if (curSelRole != null)
                {
                    updateRoleExportPermit(e.Node);
                }
            }
        }
        private void setColumnValueChangdeChildNodes(TreeListNode node, string colFieldName, bool isCheck)
        {
            for (int i = 0; i < node.Nodes.Count; i++)
            {
                TreeListNode childNode = node.Nodes[i];
                childNode.SetValue(colFieldName, isCheck);
                setColumnValueChangdeChildNodes(node.Nodes[i], colFieldName, isCheck);
            }
        }
        private void setColumnValueChangdeParentNodes(TreeListNode node, string colFieldName, bool isCheck)
        {
            if (node.ParentNode != null)
            {
                bool b = false;
                bool? isBrotherNodeChecked;
                for (int i = 0; i < node.ParentNode.Nodes.Count; i++)
                {
                    isBrotherNodeChecked = getColumnValueCheckedState(node.ParentNode.Nodes[i], colFieldName);
                    if (!isCheck.Equals(isBrotherNodeChecked))
                    {
                        b = !b;
                        break;
                    }
                }

                bool? isParentNodeChecked;
                if (b)
                {
                    isParentNodeChecked = null;
                }
                else
                {
                    isParentNodeChecked = isCheck;
                }
                node.ParentNode.SetValue(colFieldName, isParentNodeChecked);
                setColumnValueChangdeParentNodes(node.ParentNode, colFieldName, isCheck);
            }
        }
        private bool? getColumnValueCheckedState(TreeListNode node, string filedName)
        {
            bool isCheck;
            object obj = node.GetValue(filedName);
            if (obj != null)
            {
                bool.TryParse(obj.ToString(), out isCheck);
                return isCheck;
            }
            return null;
        }

        private void updateRoleExportPermit(TreeListNode node)
        {
            if (!modifiedRoles_Export.Contains(curSelRole))
            {
                modifiedRoles_Export.Add(curSelRole);
            }
            if (node.ParentNode != null)
            {
                SubFunction subFunc = node.Tag as SubFunction;
                if (subFunc != null)
                {
                    subFunc.ExportPermit = getNodeFuncPermit(node, subFunc.ID);
                    curSelRole.UpdateExportPermit(subFunc.ExportPermit, node.Checked);
                }
            }
            else if (node.HasChildren)
            {
                foreach (TreeListNode subNode in node.Nodes)
                {
                    SubFunction subFunc = subNode.Tag as SubFunction;
                    if (subFunc != null)
                    {
                        subFunc.ExportPermit = getNodeFuncPermit(subNode, subFunc.ID);
                        curSelRole.UpdateExportPermit(subFunc.ExportPermit, subNode.Checked);
                    }
                }
            }
        }
        private FuncExportPermit getNodeFuncPermit(TreeListNode node, int subFuncid)
        {
            FuncExportPermit ep = new FuncExportPermit();
            ep.SubFuncID = subFuncid;
            ep.IsCanExportResult = getColumnValueCheckedState(node, "IsCanExportResult") == true;
            ep.IsExportToZip = getColumnValueCheckedState(node, "IsExportToZip") == true;
            ep.IsNeedExportLog = getColumnValueCheckedState(node, "IsNeedExportLog") == true;
            ep.IsNeedExportCause = getColumnValueCheckedState(node, "IsNeedExportCause") == true;
            ep.IsHideKeyInfo = getColumnValueCheckedState(node, "IsHideKeyInfo") == true;
            return ep;
        }
        #endregion

        void treeListFunc_BeforeCheckNode(object sender, DevExpress.XtraTreeList.CheckNodeEventArgs e)
        {
            e.State = (e.PrevState == CheckState.Checked ? CheckState.Unchecked : CheckState.Checked);
        }

        void treeListFunc_AfterCheckNode(object sender, DevExpress.XtraTreeList.NodeEventArgs e)
        {
            setCheckedChildNodes(e.Node, e.Node.CheckState);
            setCheckedParentNodes(e.Node, e.Node.CheckState);
            if (curSelRole != null)
            {
                updateRolePermission(e.Node);
                if (isShowExportPermitColumn)
                {
                    updateRoleExportPermit(e.Node);
                }
            }
        }

        private void setCheckedChildNodes(TreeListNode node, CheckState check)
        {
            for (int i = 0; i < node.Nodes.Count; i++)
            {
                node.Nodes[i].CheckState = check;
                setCheckedChildNodes(node.Nodes[i], check);
            }
        }
        private void setCheckedParentNodes(TreeListNode node, CheckState check)
        {
            if (node.ParentNode != null)
            {
                bool b = false;
                CheckState state;
                for (int i = 0; i < node.ParentNode.Nodes.Count; i++)
                {
                    state = node.ParentNode.Nodes[i].CheckState;
                    if (!check.Equals(state))
                    {
                        b = !b;
                        break;
                    }
                }
                node.ParentNode.CheckState = b ? CheckState.Indeterminate : check;
                setCheckedParentNodes(node.ParentNode, check);
            }
        }

        private void updateRolePermission(TreeListNode node)
        {
            if (!modifiedRoles.Contains(curSelRole))
            {
                modifiedRoles.Add(curSelRole);
            }
            if (node.ParentNode!=null)
            {
                SubFunction subFunc = node.Tag as SubFunction;
                if (subFunc != null)
                {
                    curSelRole.UpdatePermission(subFunc.ID, node.Checked);
                }
            }
            else if(node.HasChildren)
            {
                foreach (TreeListNode subNode in node.Nodes)
                {
                    SubFunction subFunc = subNode.Tag as SubFunction;
                    if (subFunc != null)
                    {
                        curSelRole.UpdatePermission(subFunc.ID, subNode.Checked);
                    }
                }  
            }
        }

        private void btnAddRole_Click(object sender, EventArgs e)
        {
            TextInputBox nameBox = new TextInputBox("权限组名称", "组名", "新权限组");
            if (nameBox.ShowDialog() == DialogResult.OK)
            {
                FunctionRole role = new FunctionRole();
                role.ID = MainModel.PermissionManager.GetNewFunctionRoleID();
                role.Name = nameBox.TextInput;
                MainModel.PermissionManager.AddRole(role);
                modifiedRoles.Add(role);
                if (isShowExportPermitColumn)
                {
                    modifiedRoles_Export.Add(role);
                }
                fillRoleView();
                setRoleViewCheckState(curSelUser);
            }
        }

        private void treeListRole_CellValueChanged(object sender, DevExpress.XtraTreeList.CellValueChangedEventArgs e)
        {
            if (!(e.Node.Tag is FunctionRole))
            {
                return;
            }
            if (e.Column == colRoleName)
            {
                if (string.IsNullOrEmpty(e.Value.ToString().Trim()))
                {
                    e.Node.SetValue(e.Column, ((FunctionRole)e.Node.Tag).Name);
                }
                else
                {
                    modifiedRoles.Add((FunctionRole)e.Node.Tag);
                }
            }
            else if (e.Column == colRoleDesc)
            {
                modifiedRoles.Add((FunctionRole)e.Node.Tag);
            }
        }

        private void btnSubmit_Click(object sender, EventArgs e)
        {
            if (modifiedUsers.Count == 0 && modifiedRoles.Count == 0 && modifiedRoles_Export.Count == 0)
            {
                MessageBox.Show("当前无修改！");
                return;
            }
            WaitTextBox.Show("正在更新数据...", submit2DBInThread);
        }

        private void submit2DBInThread()
        {
            bool succ = true;
            try
            {
                if (modifiedUsers.Count > 0)
                {
                    UpdateUserPermission updateUser = new UpdateUserPermission(MainModel, modifiedUsers);
                    updateUser.Query();
                    modifiedUsers.Clear();
                }
                if (modifiedRoles.Count > 0)
                {
                    SubmitRolesInfo submitRoles = new SubmitRolesInfo(MainModel, modifiedRoles);
                    submitRoles.Query();
                    modifiedRoles.Clear();
                }
                if (modifiedRoles_Export.Count > 0 && isShowExportPermitColumn)
                {
                    SubmitRolesExportPermit submitRoles_Export = new SubmitRolesExportPermit(MainModel, modifiedRoles_Export);
                    submitRoles_Export.Query();
                    modifiedRoles_Export.Clear();
                }
            }
            catch (Exception ex)
            {
                succ = false;
                MessageBox.Show("更新失败！" + ex.ToString());
            }
            finally
            {
                System.Threading.Thread.Sleep(100);
                WaitTextBox.Close();
                if (succ)
                {
                    MessageBox.Show(this,"更新完毕，将在下次登录系统时生效！");
                }
            }
        }

    }
}
