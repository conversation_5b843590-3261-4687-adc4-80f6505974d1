﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTStationAcceptance_XJ
{
    /// <summary>
    /// 站审数据
    /// </summary>
    class IndoorBtsCheck
    {
        /// <summary>
        /// 地址
        /// </summary>
        public string Address { get; set; }
        /// <summary>
        /// 区县
        /// </summary>
        public string District { get; set; }
        public double BtsLatitude { get; set; }
        public int BBU { get; set; }
        public int RRU { get; set; }
        public int CellCount { get; set; }
        public List<IndoorBtsCheckCell> CellList { get; set; }
    }

    class IndoorBtsCheckCell
    {
        public double Longitude { get; set; }
        public double Latitude { get; set; }
        /// <summary>
        /// 通道数
        /// </summary>
        public int Channels { get; set; }
    }

    /// <summary>
    /// 无线规划
    /// </summary>
    class IndoorNRWirelessPlanning
    {
        public int TAC { get; set; }

        public List<IndoorNRWirelessPlanningCell> CellList { get; set; }
    }

    class IndoorNRWirelessPlanningCell
    {
        public int CellID { get; set; }
        public int PCI { get; set; }
        /// <summary>
        /// 频段
        /// </summary>
        public string FreqBand { get; set; }
        /// <summary>
        /// 频点
        /// </summary>
        public string Freq { get; set; }
        /// <summary>
        /// SSB频点
        /// </summary>
        public string SSBFreq { get; set; }
        /// <summary>
        /// 小区带宽（兆）
        /// </summary>
        public string Bandwidth { get; set; }
        /// <summary>
        /// 根序列(PRACH)
        /// </summary>
        public string PRACH { get; set; }
        /// <summary>
        /// 子帧配比
        /// </summary>
        public string SubFrameRatio { get; set; }
    }

    /// <summary>
    /// 网管配置
    /// </summary>
    class IndoorNetworkConfiguration
    {
        public int NodeBID { get; set; }
        public double BtsLongitude { get; set; }
        public double BtsLatitude { get; set; }
        public int BBU { get; set; }
        public int RRU { get; set; }
        public int CellCount { get; set; }
        public int TAC { get; set; }

        public List<IndoorNetworkConfigurationCell> CellList { get; set; }
    }

    class IndoorNetworkConfigurationCell
    {
        public int CellID { get; set; }
        public int PCI { get; set; }
        /// <summary>
        /// 频段
        /// </summary>
        public string FreqBand { get; set; }
        /// <summary>
        /// 频点
        /// </summary>
        public string Freq { get; set; }
        /// <summary>
        /// SSB频点
        /// </summary>
        public string SSBFreq { get; set; }
        /// <summary>
        /// 小区带宽（兆）
        /// </summary>
        public string Bandwidth { get; set; }
        /// <summary>
        /// 根序列(PRACH)
        /// </summary>
        public string PRACH { get; set; }
        /// <summary>
        /// 子帧配比
        /// </summary>
        public string SubFrameRatio { get; set; }
        /// <summary>
        /// 通道数
        /// </summary>
        public int Channels { get; set; }
    }

    /// <summary>
    /// 天资平台
    /// </summary>
    class IndoorAntennaAttitude
    {
        public List<IndoorAntennaAttitudeCell> CellList { get; set; }
    }

    class IndoorAntennaAttitudeCell
    {
        public double Longitude { get; set; }
        public double Latitude { get; set; }
    }
}
