﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class HandoverPingPangConditionDialog : BaseDialog
    {
        public HandoverPingPangConditionDialog()
        {
            InitializeComponent();
            
        }

        public void SetCondition(int timeLimit, bool bSpeedLimit, int speedLimitMin, int speedLimitMax)
        {
            numTimeLimit.Value = (decimal)timeLimit;
            chkSpeedLimit.Checked = bSpeedLimit;
            numSpeedLimitMin.Value = (decimal)speedLimitMin;
            numSpeedLimitMax.Value = (decimal)speedLimitMax;
            chkSpeedLimit_CheckedChanged(null, null);
        }

        public void GetCondition(ref int timeLimit, ref bool bSpeedLimit, ref int speedLimitMin, ref int speedLimitMax)
        {
            timeLimit = (int)numTimeLimit.Value;
            bSpeedLimit = chkSpeedLimit.Checked;
            speedLimitMin = (int)numSpeedLimitMin.Value;
            speedLimitMax = (int)numSpeedLimitMax.Value;
        }

        private void chkSpeedLimit_CheckedChanged(object sender, EventArgs e)
        {
            numSpeedLimitMin.Enabled = chkSpeedLimit.Checked;
            numSpeedLimitMax.Enabled = numSpeedLimitMin.Enabled;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
        }
    }
}
