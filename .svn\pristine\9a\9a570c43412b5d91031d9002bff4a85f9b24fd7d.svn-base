﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.Util;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.CQT
{
    public class CQTTestTask
    {
        public CQTTestTask()
        { 
        }
        
        public Guid ID { get; set; }
        public int CreatorID { get; set; }
        public DateTime BeginTime { get; set; }
        public DateTime EndTime { get; set; }
        public string Name { get; set; }

        private CategoryEnumItem agentItem;
        public int AgentID
        {
            get { return agentItem.ID; }
            set { agentItem = ((CategoryEnum)CategoryManager.GetInstance()["Agent"])[value]; }
        }
        public string Agent
        {
            get { return agentItem.ToString(); }
        }

        
        public CQTPoint CQTPoint { get; set; }

        public string CQTPointName
        {
            get { return CQTPoint.Name; }
        }

        private CategoryEnumItem careerItem;
        public int CareerID
        {
            get { return careerItem == null ? -1 : careerItem.ID; }
            set { careerItem = ((CategoryEnum)CategoryManager.GetInstance()["Carrier"])[value]; }
        }
        public string CareerName
        {
            get { return careerItem == null ? "" : careerItem.ToString(); }
        }

        public int ServiceID
        {
            get { return serviceItem.ID; }
            set { serviceItem = ((CategoryEnum)CategoryManager.GetInstance()["ServiceType"])[value]; }
        }
        public string ServiceName
        {
            get { return serviceItem.ToString(); }
        }

        private CategoryEnumItem serviceItem;
        
        public string Comment { get; set; } = "";
        /// <summary>
        /// 目标测试时长（分钟）
        /// </summary>
        public TimeSpan Target { get; set; }

        public int RestDays
        {
            get { return (EndTime.Date - DateTime.Now.Date).Days; }
        }

        public TimeSpan TestedTimeSpan
        {
            get
            {
                TimeSpan ts = new TimeSpan();
                if (pointTestInfo != null)
                {
                    foreach (SubPointTestInfo item in pointTestInfo)
                    {
                        ts = ts.Add(item.TestTimeSpan);
                    }
                }
                return ts;
            }
        }
        public double ComplianceRate
        {
            get { return Math.Round(TestedTimeSpan.TotalSeconds * 100.0 / Target.TotalSeconds, 2); }
        }

        public void AddTestFile(FileInfo fi,string subPointName)
        {
            if (fi.CarrierType != careerItem.ID) return;
            if (fi.CompanyID != AgentID) return;
            if (fi.ServiceType != ServiceID) return;
            if (JavaDate.GetDateTimeFromMilliseconds(fi.BeginTime * 1000L) >= BeginTime && JavaDate.GetDateTimeFromMilliseconds(fi.EndTime * 1000L) <= EndTime)
            {
                if (pointTestInfo == null)
                {
                    pointTestInfo = new List<SubPointTestInfo>();
                }
                bool exist = false;
                foreach (SubPointTestInfo testInfo in pointTestInfo)
                {
                    if (testInfo.Name == subPointName)
                    {
                        testInfo.FileInfoList.Add(fi);
                        exist = true;
                        break;
                    }
                }
                if (!exist)
                {
                    SubPointTestInfo testInfo = new SubPointTestInfo();
                    testInfo.Name = subPointName;
                    testInfo.FileInfoList.Add(fi);
                    pointTestInfo.Add(testInfo);
                }
            }
        }

        private List<SubPointTestInfo> pointTestInfo = null;
        public List<SubPointTestInfo> SubPointsTestInfo
        {
            get { return pointTestInfo; }
        }

        public void ClearTestInfo()
        {
            pointTestInfo = new List<SubPointTestInfo>();
        }

    }

    public class SubPointTestInfo
    {
        public string Name { get; set; } = "";
        public SubPointTestInfo()
        { 
            
        }
        private TimeSpan timeSpan = TimeSpan.MinValue;
        public TimeSpan TestTimeSpan
        {
            get
            {
                if (timeSpan == TimeSpan.MinValue)
                {
                    foreach (FileInfo fi in FileInfoList)
                    {
                        if (timeSpan==TimeSpan.MinValue)
                        {
                            timeSpan = TimeSpan.FromSeconds(fi.EndTime - fi.BeginTime);
                        }
                        else
                        {
                            timeSpan.Add(TimeSpan.FromSeconds(fi.EndTime - fi.BeginTime));
                        }
                    }
                }
                return timeSpan;
            }
        }
        
        public List<FileInfo> FileInfoList { get; set; } = new List<FileInfo>();
    }
}
