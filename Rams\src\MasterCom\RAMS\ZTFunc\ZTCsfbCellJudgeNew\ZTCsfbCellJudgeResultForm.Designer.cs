﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTCsfbCellJudgeResultForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            DevExpress.XtraGrid.GridLevelNode gridLevelNode1 = new DevExpress.XtraGrid.GridLevelNode();
            this.gridViewSubCsfbCellJudge = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn24 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn25 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn27 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn28 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn17 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridControlCsfbCellJudge = new DevExpress.XtraGrid.GridControl();
            this.ctxResult = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miResultExport = new System.Windows.Forms.ToolStripMenuItem();
            this.gridViewCsfbCellJudge = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColMtFileName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn6 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn7 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn8 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn9 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn10 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn11 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn12 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn13 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn14 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn15 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn16 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColFallLongitude = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColFallLatitude = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn19 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColFallRxLev = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColMaxFallDistance = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColMaxFallRxLev = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn23 = new DevExpress.XtraGrid.Columns.GridColumn();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewSubCsfbCellJudge)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlCsfbCellJudge)).BeginInit();
            this.ctxResult.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewCsfbCellJudge)).BeginInit();
            this.SuspendLayout();
            // 
            // gridViewSubCsfbCellJudge
            // 
            this.gridViewSubCsfbCellJudge.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn3,
            this.gridColumn24,
            this.gridColumn25,
            this.gridColumn27,
            this.gridColumn28,
            this.gridColumn17});
            this.gridViewSubCsfbCellJudge.GridControl = this.gridControlCsfbCellJudge;
            this.gridViewSubCsfbCellJudge.Name = "gridViewSubCsfbCellJudge";
            this.gridViewSubCsfbCellJudge.OptionsBehavior.AutoPopulateColumns = false;
            this.gridViewSubCsfbCellJudge.OptionsBehavior.Editable = false;
            this.gridViewSubCsfbCellJudge.OptionsDetail.ShowDetailTabs = false;
            this.gridViewSubCsfbCellJudge.OptionsView.EnableAppearanceEvenRow = true;
            this.gridViewSubCsfbCellJudge.OptionsView.ShowDetailButtons = false;
            this.gridViewSubCsfbCellJudge.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn3
            // 
            this.gridColumn3.Caption = "小区名";
            this.gridColumn3.FieldName = "CellName";
            this.gridColumn3.Name = "gridColumn3";
            this.gridColumn3.Visible = true;
            this.gridColumn3.VisibleIndex = 0;
            this.gridColumn3.Width = 120;
            // 
            // gridColumn24
            // 
            this.gridColumn24.Caption = "小区类别";
            this.gridColumn24.FieldName = "CellType";
            this.gridColumn24.Name = "gridColumn24";
            this.gridColumn24.Visible = true;
            this.gridColumn24.VisibleIndex = 1;
            // 
            // gridColumn25
            // 
            this.gridColumn25.Caption = "小区ID";
            this.gridColumn25.FieldName = "CellID";
            this.gridColumn25.Name = "gridColumn25";
            this.gridColumn25.Visible = true;
            this.gridColumn25.VisibleIndex = 2;
            // 
            // gridColumn27
            // 
            this.gridColumn27.Caption = "频点";
            this.gridColumn27.FieldName = "PCI";
            this.gridColumn27.Name = "gridColumn27";
            this.gridColumn27.Visible = true;
            this.gridColumn27.VisibleIndex = 3;
            // 
            // gridColumn28
            // 
            this.gridColumn28.Caption = "扰码";
            this.gridColumn28.FieldName = "BSIC";
            this.gridColumn28.Name = "gridColumn28";
            this.gridColumn28.Visible = true;
            this.gridColumn28.VisibleIndex = 4;
            // 
            // gridColumn17
            // 
            this.gridColumn17.Caption = "场强";
            this.gridColumn17.FieldName = "RxLev";
            this.gridColumn17.Name = "gridColumn17";
            this.gridColumn17.Visible = true;
            this.gridColumn17.VisibleIndex = 5;
            // 
            // gridControlCsfbCellJudge
            // 
            this.gridControlCsfbCellJudge.ContextMenuStrip = this.ctxResult;
            this.gridControlCsfbCellJudge.Dock = System.Windows.Forms.DockStyle.Fill;
            gridLevelNode1.LevelTemplate = this.gridViewSubCsfbCellJudge;
            gridLevelNode1.RelationName = "SubInfos";
            this.gridControlCsfbCellJudge.LevelTree.Nodes.AddRange(new DevExpress.XtraGrid.GridLevelNode[] {
            gridLevelNode1});
            this.gridControlCsfbCellJudge.Location = new System.Drawing.Point(0, 0);
            this.gridControlCsfbCellJudge.MainView = this.gridViewCsfbCellJudge;
            this.gridControlCsfbCellJudge.Name = "gridControlCsfbCellJudge";
            this.gridControlCsfbCellJudge.ShowOnlyPredefinedDetails = true;
            this.gridControlCsfbCellJudge.Size = new System.Drawing.Size(1003, 503);
            this.gridControlCsfbCellJudge.TabIndex = 0;
            this.gridControlCsfbCellJudge.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridViewCsfbCellJudge,
            this.gridViewSubCsfbCellJudge});
            // 
            // ctxResult
            // 
            this.ctxResult.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miResultExport});
            this.ctxResult.Name = "ctxSummary";
            this.ctxResult.Size = new System.Drawing.Size(139, 26);
            // 
            // miResultExport
            // 
            this.miResultExport.Name = "miResultExport";
            this.miResultExport.Size = new System.Drawing.Size(138, 22);
            this.miResultExport.Text = "导出Excel...";
            this.miResultExport.Click += new System.EventHandler(this.miResultExport_Click);
            // 
            // gridViewCsfbCellJudge
            // 
            this.gridViewCsfbCellJudge.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn1,
            this.gridColumn2,
            this.gridColMtFileName,
            this.gridColumn4,
            this.gridColumn5,
            this.gridColumn6,
            this.gridColumn7,
            this.gridColumn8,
            this.gridColumn9,
            this.gridColumn10,
            this.gridColumn11,
            this.gridColumn12,
            this.gridColumn13,
            this.gridColumn14,
            this.gridColumn15,
            this.gridColumn16,
            this.gridColFallLongitude,
            this.gridColFallLatitude,
            this.gridColumn19,
            this.gridColFallRxLev,
            this.gridColMaxFallDistance,
            this.gridColMaxFallRxLev,
            this.gridColumn23});
            this.gridViewCsfbCellJudge.GridControl = this.gridControlCsfbCellJudge;
            this.gridViewCsfbCellJudge.Name = "gridViewCsfbCellJudge";
            this.gridViewCsfbCellJudge.OptionsBehavior.Editable = false;
            this.gridViewCsfbCellJudge.OptionsDetail.ShowDetailTabs = false;
            this.gridViewCsfbCellJudge.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridViewCsfbCellJudge.OptionsView.ColumnAutoWidth = false;
            this.gridViewCsfbCellJudge.OptionsView.EnableAppearanceEvenRow = true;
            this.gridViewCsfbCellJudge.OptionsView.ShowGroupPanel = false;
            this.gridViewCsfbCellJudge.DoubleClick += new System.EventHandler(this.gridViewCsfbCellJudge_DoubleClick);
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "序号";
            this.gridColumn1.FieldName = "SN";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.Visible = true;
            this.gridColumn1.VisibleIndex = 0;
            // 
            // gridColumn2
            // 
            this.gridColumn2.Caption = "CSFB文件";
            this.gridColumn2.FieldName = "MoFileName";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.Visible = true;
            this.gridColumn2.VisibleIndex = 1;
            // 
            // gridColMtFileName
            // 
            this.gridColMtFileName.Caption = "GSM文件";
            this.gridColMtFileName.FieldName = "MtFileName";
            this.gridColMtFileName.Name = "gridColMtFileName";
            this.gridColMtFileName.Visible = true;
            this.gridColMtFileName.VisibleIndex = 2;
            // 
            // gridColumn4
            // 
            this.gridColumn4.Caption = "回落时间";
            this.gridColumn4.FieldName = "BeginTime";
            this.gridColumn4.Name = "gridColumn4";
            this.gridColumn4.Visible = true;
            this.gridColumn4.VisibleIndex = 3;
            // 
            // gridColumn5
            // 
            this.gridColumn5.Caption = "经度";
            this.gridColumn5.FieldName = "Longitude";
            this.gridColumn5.Name = "gridColumn5";
            this.gridColumn5.Visible = true;
            this.gridColumn5.VisibleIndex = 4;
            // 
            // gridColumn6
            // 
            this.gridColumn6.Caption = "纬度";
            this.gridColumn6.FieldName = "Latitude";
            this.gridColumn6.Name = "gridColumn6";
            this.gridColumn6.Visible = true;
            this.gridColumn6.VisibleIndex = 5;
            // 
            // gridColumn7
            // 
            this.gridColumn7.Caption = "回落前4G小区";
            this.gridColumn7.FieldName = "LteCellName";
            this.gridColumn7.Name = "gridColumn7";
            this.gridColumn7.Visible = true;
            this.gridColumn7.VisibleIndex = 6;
            // 
            // gridColumn8
            // 
            this.gridColumn8.Caption = "回落前4G小区ECI";
            this.gridColumn8.FieldName = "LteECI";
            this.gridColumn8.Name = "gridColumn8";
            this.gridColumn8.Visible = true;
            this.gridColumn8.VisibleIndex = 7;
            // 
            // gridColumn9
            // 
            this.gridColumn9.Caption = "ENBID-LOCALCELLID";
            this.gridColumn9.FieldName = "ENBID";
            this.gridColumn9.Name = "gridColumn9";
            this.gridColumn9.Visible = true;
            this.gridColumn9.VisibleIndex = 8;
            // 
            // gridColumn10
            // 
            this.gridColumn10.Caption = "回落前4G小区TAC";
            this.gridColumn10.FieldName = "LteTAC";
            this.gridColumn10.Name = "gridColumn10";
            this.gridColumn10.Visible = true;
            this.gridColumn10.VisibleIndex = 9;
            // 
            // gridColumn11
            // 
            this.gridColumn11.Caption = "回落前4G小区经度";
            this.gridColumn11.FieldName = "LteLongitude";
            this.gridColumn11.Name = "gridColumn11";
            this.gridColumn11.Visible = true;
            this.gridColumn11.VisibleIndex = 10;
            // 
            // gridColumn12
            // 
            this.gridColumn12.Caption = "回落前4G小区纬度";
            this.gridColumn12.FieldName = "LteLatitude";
            this.gridColumn12.Name = "gridColumn12";
            this.gridColumn12.Visible = true;
            this.gridColumn12.VisibleIndex = 11;
            // 
            // gridColumn13
            // 
            this.gridColumn13.Caption = "回落点与LTE基站距离";
            this.gridColumn13.FieldName = "LTEDistance";
            this.gridColumn13.Name = "gridColumn13";
            this.gridColumn13.Visible = true;
            this.gridColumn13.VisibleIndex = 12;
            // 
            // gridColumn14
            // 
            this.gridColumn14.Caption = "回落小区";
            this.gridColumn14.FieldName = "CellName";
            this.gridColumn14.Name = "gridColumn14";
            this.gridColumn14.Visible = true;
            this.gridColumn14.VisibleIndex = 13;
            // 
            // gridColumn15
            // 
            this.gridColumn15.Caption = "回落小区CellID";
            this.gridColumn15.FieldName = "CellID";
            this.gridColumn15.Name = "gridColumn15";
            this.gridColumn15.Visible = true;
            this.gridColumn15.VisibleIndex = 14;
            // 
            // gridColumn16
            // 
            this.gridColumn16.Caption = "LAC";
            this.gridColumn16.FieldName = "LAC";
            this.gridColumn16.Name = "gridColumn16";
            this.gridColumn16.Visible = true;
            this.gridColumn16.VisibleIndex = 15;
            // 
            // gridColFallLongitude
            // 
            this.gridColFallLongitude.Caption = "回落后GSM小区经度";
            this.gridColFallLongitude.FieldName = "FallLongitude";
            this.gridColFallLongitude.Name = "gridColFallLongitude";
            this.gridColFallLongitude.Visible = true;
            this.gridColFallLongitude.VisibleIndex = 16;
            // 
            // gridColFallLatitude
            // 
            this.gridColFallLatitude.Caption = "回落后GSM小区纬度";
            this.gridColFallLatitude.FieldName = "FallLatitude";
            this.gridColFallLatitude.Name = "gridColFallLatitude";
            this.gridColFallLatitude.Visible = true;
            this.gridColFallLatitude.VisibleIndex = 17;
            // 
            // gridColumn19
            // 
            this.gridColumn19.Caption = "回落距离";
            this.gridColumn19.FieldName = "FallDistance";
            this.gridColumn19.Name = "gridColumn19";
            this.gridColumn19.Visible = true;
            this.gridColumn19.VisibleIndex = 18;
            // 
            // gridColFallRxLev
            // 
            this.gridColFallRxLev.Caption = "回落后GSM小区电平";
            this.gridColFallRxLev.FieldName = "FallRxLev";
            this.gridColFallRxLev.Name = "gridColFallRxLev";
            this.gridColFallRxLev.Visible = true;
            this.gridColFallRxLev.VisibleIndex = 19;
            // 
            // gridColMaxFallDistance
            // 
            this.gridColMaxFallDistance.Caption = "最强GSM小区距离";
            this.gridColMaxFallDistance.FieldName = "MaxFallDistance";
            this.gridColMaxFallDistance.Name = "gridColMaxFallDistance";
            this.gridColMaxFallDistance.Visible = true;
            this.gridColMaxFallDistance.VisibleIndex = 20;
            // 
            // gridColMaxFallRxLev
            // 
            this.gridColMaxFallRxLev.Caption = "最强GSM小区电平";
            this.gridColMaxFallRxLev.FieldName = "MaxFallRxLev";
            this.gridColMaxFallRxLev.Name = "gridColMaxFallRxLev";
            this.gridColMaxFallRxLev.Visible = true;
            this.gridColMaxFallRxLev.VisibleIndex = 21;
            // 
            // gridColumn23
            // 
            this.gridColumn23.Caption = "是否在前六强小区中";
            this.gridColumn23.FieldName = "IsInListDisplay";
            this.gridColumn23.Name = "gridColumn23";
            this.gridColumn23.Visible = true;
            this.gridColumn23.VisibleIndex = 22;
            // 
            // ZTCsfbCellJudgeResultForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1003, 503);
            this.Controls.Add(this.gridControlCsfbCellJudge);
            this.Name = "ZTCsfbCellJudgeResultForm";
            this.Text = "CSFB回落非最佳小区";
            ((System.ComponentModel.ISupportInitialize)(this.gridViewSubCsfbCellJudge)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlCsfbCellJudge)).EndInit();
            this.ctxResult.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridViewCsfbCellJudge)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraGrid.GridControl gridControlCsfbCellJudge;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewCsfbCellJudge;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColMtFileName;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn6;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn7;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn8;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn9;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn10;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn11;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn12;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn13;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn14;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn15;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn16;
        private DevExpress.XtraGrid.Columns.GridColumn gridColFallLongitude;
        private DevExpress.XtraGrid.Columns.GridColumn gridColFallLatitude;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn19;
        private DevExpress.XtraGrid.Columns.GridColumn gridColFallRxLev;
        private DevExpress.XtraGrid.Columns.GridColumn gridColMaxFallDistance;
        private DevExpress.XtraGrid.Columns.GridColumn gridColMaxFallRxLev;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn23;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewSubCsfbCellJudge;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn24;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn25;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn27;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn28;
        private System.Windows.Forms.ContextMenuStrip ctxResult;
        private System.Windows.Forms.ToolStripMenuItem miResultExport;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn17;
    }
}