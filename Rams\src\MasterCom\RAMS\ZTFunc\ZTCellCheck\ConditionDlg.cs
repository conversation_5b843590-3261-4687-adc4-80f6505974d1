﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc.ZTCellCheck
{
    public partial class ConditionDlg : BaseForm
    {
        public ConditionDlg()
        {
            InitializeComponent();
        }

        public void SetCondition(FuncCondition cond)
        {
            if (cond==null)
            {
                return;
            }
            numSiteDistance.Value = (decimal)cond.UltraSiteCondition.NearDistanceMin;
            numAltitude.Value = (decimal)cond.UltraSiteCondition.AltitudeMax;
            numDirRange.Value = (decimal)cond.UltraSiteCondition.AvgSitesDistanceAngle;
            numAvgDistance.Value = (decimal)cond.UltraSiteCondition.AvgSiteDistanceMax;
            numDiffBandDis.Value = (decimal)cond.UltraSiteCondition.DiffBandDistanceMin;

            this.numOvrLapRSRP.Value = (decimal)cond.CoverlapRSRPMin;
            this.numSiteCnt.Value = (decimal)cond.CoverSiteNum;
            this.numCvrDisFactor.Value = (decimal)cond.CvrDisFactorMax;

            this.numMultiCvrRSRP.Value = (decimal)cond.MultiCoverRSRP;
            this.numMultiRSRPDiff.Value = (decimal)cond.MultiCoverDiff;

            this.numMod3RSRP.Value = (decimal)cond.Mod3RSRP;
            this.numMod3RSRPDiff.Value = (decimal)cond.Mod3Diff;

            this.numWeakCoverRSRP.Value = (decimal)cond.WeakCoverRSRP;
        }

        public FuncCondition GetCondition()
        {
            FuncCondition cond = new FuncCondition();

            UltraSiteCondition uCond = new UltraSiteCondition();
            uCond.NearDistanceMin = (double)numSiteDistance.Value;
            uCond.AltitudeMax = (double)numAltitude.Value;
            uCond.AvgSitesDistanceAngle = (int)numDirRange.Value;
            uCond.AvgSiteDistanceMax = (double)numAvgDistance.Value;
            uCond.DiffBandDistanceMin = (double)numDiffBandDis.Value;
            cond.UltraSiteCondition = uCond;

            cond.CoverlapRSRPMin = (float)this.numOvrLapRSRP.Value;
            cond.CoverSiteNum = (int)this.numSiteCnt.Value;
            cond.CvrDisFactorMax = (double)this.numCvrDisFactor.Value;

            cond.MultiCoverRSRP = (float)this.numMultiCvrRSRP.Value;
            cond.MultiCoverDiff = (float)this.numMultiRSRPDiff.Value;

            cond.Mod3RSRP = (float)this.numMod3RSRP.Value;
            cond.Mod3Diff = (float)this.numMod3RSRPDiff.Value;

            cond.WeakCoverRSRP = (float)this.numWeakCoverRSRP.Value;
            return cond;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
        }


    }
}
