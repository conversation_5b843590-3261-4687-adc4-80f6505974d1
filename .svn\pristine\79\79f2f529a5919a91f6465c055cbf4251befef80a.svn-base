using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Collections.ObjectModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.Func
{
    public partial class MapFormExportDTTabSettingDlg : Form
    {
        public MapFormExportDTTabSettingDlg(ReadOnlyCollection<MapSerialInfo> serialInfos, IEnumerable<TestPoint> tps)
        {
            InitializeComponent();
            foreach (MapSerialInfo serialInfo in serialInfos)
            {
                cbbxSerial.Items.Add(serialInfo);
            }
            Dictionary<string, DTParameter> nameDic = new Dictionary<string, DTParameter>();
            foreach (TestPoint tp in tps)
            {
                foreach (DTParameter param in tp.Parameters.Map.Keys)
                {
                    if (nameDic.ContainsKey(param.Info.Name))
                    {
                        continue;
                    }
                    nameDic[param.Info.Name] = null;
                    ListViewItem item = new ListViewItem(param.Info.Name);
                    item.Tag = param;
                    lvAvailableCol.Items.Add(item);
                }
            }
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        public List<string> SelectedCols()
        {
            List<string> selItem = new List<string>();
            foreach (ListViewItem item in lvSelectedCol.Items)
            {
                selItem.Add(item.Text);
            }
            return selItem;
        }

        public MapSerialInfo SelectedSerialInfo
        {
            get
            {
                return cbbxSerial.SelectedItem as MapSerialInfo;
            }
            set
            {
                cbbxSerial.SelectedItem = value;
            }
        }

        private void lvAddItem(bool isAll)
        {
            List<ListViewItem> selectedItems = new List<ListViewItem>();
            if (isAll)
            {
                foreach (ListViewItem item in lvAvailableCol.Items)
                {
                      selectedItems.Add(item);
                }
            }
            else
            {
                foreach (ListViewItem item in lvAvailableCol.SelectedItems)
                {
                    selectedItems.Add(item);
                }
            }
           
            foreach (ListViewItem item in selectedItems)
            {
                lvAvailableCol.Items.Remove(item);
                lvSelectedCol.Items.Add(item);
            }
            lvSelectedCol.Focus();
            checkMSButtonState();
        }
        private void lvRemoveItem(bool isAll) 
        {
            List<ListViewItem> selectedItems = new List<ListViewItem>();
            if (isAll)
            {
                foreach (ListViewItem item in lvSelectedCol.Items)
                {
                    selectedItems.Add(item);
                }
            }
            else
            {
                foreach (ListViewItem item in lvSelectedCol.SelectedItems)
                {
                    selectedItems.Add(item);
                }
            }
           
            foreach (ListViewItem item in selectedItems)
            {
                lvSelectedCol.Items.Remove(item);
                lvAvailableCol.Items.Add(item);
            }
            lvAvailableCol.Focus();
            checkMSButtonState();
        }
        private void checkMSButtonState()
        {
            buttonAdd.Enabled = lvAvailableCol.SelectedIndices.Count > 0;
            buttonRemove.Enabled = lvSelectedCol.SelectedIndices.Count > 0;
            buttonAddAll.Enabled = lvAvailableCol.Items.Count > 0;
            buttonRemoveAll.Enabled = lvSelectedCol.Items.Count > 0;
        }

        private void lvAvailableCol_SelectedIndexChanged(object sender, EventArgs e)
        {
            checkMSButtonState();
        }
        private void lvSelectedCol_SelectedIndexChanged(object sender, EventArgs e)
        {
            checkMSButtonState();
        }
        private void buttonAdd_Click(object sender, EventArgs e)
        {
            lvAddItem(false);
        }
        private void buttonRemove_Click(object sender, EventArgs e)
        {
            lvRemoveItem(false);
        }
        private void buttonEventAddAll_Click(object sender, EventArgs e)
        {
            lvAddItem(true);
        }
        private void buttonEventRemoveAll_Click(object sender, EventArgs e)
        {
            lvRemoveItem(true);
        }
        private void lvAvailableCol_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            lvAddItem(false);
        }
        private void lvSelectedCol_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            lvRemoveItem(false);
        }
    }
}