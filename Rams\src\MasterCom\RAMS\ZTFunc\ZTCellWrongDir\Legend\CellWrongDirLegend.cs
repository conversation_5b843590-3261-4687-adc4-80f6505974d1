﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.MTGis;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc.ZTCellWrongDir
{
    public class CellWrongDirLegend : TestPointLegend
    {
        public CellWrongDirLegend()
        {
            this.Title = "扫频小区覆盖异常";
            this.Items = new List<LegendGroupSubItem<TestPoint>>();
            Items.Add(new MainCellWrongDirLegendSubItem());
            Items.Add(new NCellWrongDirLegendSubItem());
        }
    }

    public class MainCellWrongDirLegendSubItem : LegendGroupSubItem<TestPoint>
    {
        public MainCellWrongDirLegendSubItem()
        {
            this.Path = SymbolManager.GetInstance().Paths[0];
            this.Desc = "第一强小区覆盖异常";
            this.Color = Color.LawnGreen;
        }

        public override bool IsMatch(TestPoint obj)
        {
            LTECell cell = obj.GetCell_LTEScan(0);
            return Tag.Equals(cell);
        }

        public override void DrawOnListBox(ListBox listBox, DrawItemEventArgs e)
        {
            string checkInfo = this.Visible ? "[√]" : "[  ]";
            e.Graphics.DrawString(checkInfo, listBox.Font, Brushes.Black, e.Bounds.X, e.Bounds.Y + 2);
            SizeF size = e.Graphics.MeasureString(checkInfo, listBox.Font);
            e.Graphics.TranslateTransform(e.Bounds.X + size.Width + 10, e.Bounds.Y + 8);
            e.Graphics.FillPath(new SolidBrush(this.Color), Path);
            e.Graphics.ResetTransform();
            e.Graphics.DrawString(Desc, listBox.Font, Brushes.Black, e.Bounds.X + 48, e.Bounds.Y + 2);
        }
    }

    public class NCellWrongDirLegendSubItem : MainCellWrongDirLegendSubItem
    {
        public NCellWrongDirLegendSubItem()
        {
            this.Path = SymbolManager.GetInstance().Paths[0];
            this.Desc = "非第一强小区覆盖异常";
            this.Color = Color.Orange;
        }
        public override bool IsMatch(TestPoint obj)
        {
            LTECell cell = obj.GetCell_LTEScan(0);
            return !Tag.Equals(cell);
        }
    }
}
