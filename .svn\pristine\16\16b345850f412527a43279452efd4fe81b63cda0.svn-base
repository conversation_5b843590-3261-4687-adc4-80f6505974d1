﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTNoFlowForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(ZTNoFlowForm));
            this.contextMenuStripDisconnection = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miReplayEvent = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.gridControl = new DevExpress.XtraGrid.GridControl();
            this.gridView = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.contextMenuStripDisconnection.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView)).BeginInit();
            this.SuspendLayout();
            // 
            // contextMenuStripDisconnection
            // 
            this.contextMenuStripDisconnection.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miReplayEvent,
            this.miExportExcel});
            this.contextMenuStripDisconnection.Name = "contextMenuStripDisconnection";
            this.contextMenuStripDisconnection.Size = new System.Drawing.Size(141, 48);
            // 
            // miReplayEvent
            // 
            this.miReplayEvent.Image = ((System.Drawing.Image)(resources.GetObject("miReplayEvent.Image")));
            this.miReplayEvent.Name = "miReplayEvent";
            this.miReplayEvent.Size = new System.Drawing.Size(140, 22);
            this.miReplayEvent.Text = "回放事件(&R)";
            this.miReplayEvent.Click += new System.EventHandler(this.miReplayEvent_Click);
            // 
            // miExportExcel
            // 
            this.miExportExcel.Name = "miExportExcel";
            this.miExportExcel.Size = new System.Drawing.Size(140, 22);
            this.miExportExcel.Text = "导出Excel...";
            this.miExportExcel.Click += new System.EventHandler(this.miExportExcel_Click);
            // 
            // gridControl
            // 
            this.gridControl.ContextMenuStrip = this.contextMenuStripDisconnection;
            this.gridControl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControl.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gridControl.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gridControl.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gridControl.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gridControl.EmbeddedNavigator.Buttons.Remove.Visible = false;
            this.gridControl.Location = new System.Drawing.Point(0, 0);
            this.gridControl.MainView = this.gridView;
            this.gridControl.Name = "gridControl";
            this.gridControl.Size = new System.Drawing.Size(792, 384);
            this.gridControl.TabIndex = 1;
            this.gridControl.UseEmbeddedNavigator = true;
            this.gridControl.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView});
            // 
            // gridView
            // 
            this.gridView.GridControl = this.gridControl;
            this.gridView.Name = "gridView";
            this.gridView.OptionsBehavior.Editable = false;
            this.gridView.OptionsSelection.MultiSelect = true;
            this.gridView.OptionsView.ColumnAutoWidth = false;
            this.gridView.OptionsView.ShowDetailButtons = false;
            this.gridView.OptionsView.ShowGroupPanel = false;
            // 
            // ZTNoFlowForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(792, 384);
            this.Controls.Add(this.gridControl);
            this.Name = "ZTNoFlowForm";
            this.Text = "断流事件分析";
            this.contextMenuStripDisconnection.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControl)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.ContextMenuStrip contextMenuStripDisconnection;
        private System.Windows.Forms.ToolStripMenuItem miReplayEvent;
        private System.Windows.Forms.ToolStripMenuItem miExportExcel;
        private DevExpress.XtraGrid.GridControl gridControl;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView;
    }
}