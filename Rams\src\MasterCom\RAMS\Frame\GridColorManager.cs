﻿using MasterCom.RAMS.Grid;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Xml;

namespace MasterCom.RAMS.Frame
{
    public class GridColorManager
    {
        public static readonly string CfgFileName = string.Format(System.Windows.Forms.Application.StartupPath + "/config/GridColorList.xml");
        private static GridColorManager instance = null;

        public GridColorManager()
        { 
            //加载内容

            LoadCfg();
            
        }

        public static GridColorManager Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new GridColorManager();
                }
                return instance;
            }
        }

        public List<GridColorModeItem> GridColorItems { get; set; } = new List<GridColorModeItem>();

        private List<object> cfgParam
        { 
            get
            {
                List<object> items = new List<object>();
                foreach (GridColorModeItem gcmi in GridColorItems)
                {
                    items.Add(gcmi.Param);
                }
                return items;
            }
            set
            {
                if (value == null)
                {
                    return;
                }
                GridColorItems.Clear();
                foreach (object obj in value)
                {
                    GridColorModeItem gcmi = new GridColorModeItem();
                    gcmi.Param = obj as Dictionary<string, object>;
                    GridColorItems.Add(gcmi);
                }
            }
        }

        public bool LoadCfg()
        {
            if (File.Exists(CfgFileName))
            {
                XmlConfigFile configFile = new XmlConfigFile(CfgFileName);
                cfgParam = configFile.GetItemValue("GridColorItems", "GridColorModeItem") as List<object>;
                return true;
            }
            else
                return false;
        }

        public void Save()
        {
            XmlConfigFile xmlFile = new XmlConfigFile();
            XmlElement cfgE = xmlFile.AddConfig("GridColorItems");
            xmlFile.AddItem(cfgE, "GridColorModeItem", this.cfgParam);
            xmlFile.Save(CfgFileName);
        }
    }
}
