﻿
namespace MasterCom.RAMS.ZTFunc
{
    public partial class NbIotMgrsNoRsrpResult : NbIotMgrsWeakRsrpResult
    {
        public NbIotMgrsNoRsrpResult()
        {
            InitializeComponent();
        }

        public override string Desc
        {
            get { return "连续无覆盖"; }
        }

        public override void DrawOnLayer()
        {
            mf = MainModel.MainForm.GetMapForm();
            MTGis.LayerBase clayer = mf.GetTempLayerBase(typeof(ZTScanGridLayer));
            layer = clayer as ZTScanGridLayer;
            layer.GridInfos = scanGridInfoList;
            layer.SelectedGrid = null;
            layer.InitColor(ZTScanGridLayer.RenderingIndex.R0_RP);
            layer.CurType = typeof(NbIotMgrsNoRsrpResult);
            MainModel.FireSetDefaultMapSerialTheme(layer.SerialInfoName);
            mf.updateMap();
            if (carrierDataList[0].AreaGridViews.Count > 0 && carrierDataList[0].AreaGridViews[0].SerialGridViews.Count > 0)
            {
                GotoSelectedViewGV(carrierDataList[0].AreaGridViews[0].SerialGridViews[0], 0);
            }
        }

        protected override void GetData()
        {
            NbIotMgrsWeakRsrpStater stater = this.funcItem.Stater as NbIotMgrsWeakRsrpStater;
            carrierDataList = stater.GetViews();
        }
    }
}
