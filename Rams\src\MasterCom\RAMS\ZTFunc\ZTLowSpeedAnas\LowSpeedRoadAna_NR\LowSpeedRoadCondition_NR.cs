﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public class LowSpeedRoadCondition_NR
    {
        public LowSpeedRoadCondition_NR()
        {
        }
        public ESpeedType SpeedTypeSelected { get; set; } = ESpeedType.APP;
        public bool CheckFTPDL { get; set; } = true;
        public double FTPDLRateMax { get; set; } = 150;
        public bool CheckFTPUL { get; set; } = true;
        public double FTPULRateMax { get; set; } = 2;
        public bool CheckHTTP { get; set; } = true;
        public double HTTPRateMax { get; set; } = 10;
        public bool CheckVideo { get; set; } = true;
        public double VideoRateMax { get; set; } = 10;
        public bool CheckEmail { get; set; } = true;
        public double EmailRateMax { get; set; } = 2;
        public bool CheckThroughputDl { get; set; } = true;
        public double ThroughputDlRateMax { get; set; } = 150;
        public bool CheckThroughputUl { get; set; } = true;
        public double ThroughputUlRateMax { get; set; } = 2;

        public double DistanceFTPDLMin { get; set; } = 50;
        public double DistanceFTPULMin { get; set; } = 50;
        public double DistanceEMailMin { get; set; } = 20;
        public double DistanceHTTPMin { get; set; } = 20;
        public double DistanceVideoMin { get; set; } = 20;
        public double DistanceThroughputDlMin { get; set; } = 50;
        public double DistanceThroughputUlMin { get; set; } = 50;
        public double EmailRateMin { get; set; }
        public double FTPDLRateMin { get; set; }
        public double FTPULRateMin { get; set; }
        public double HTTPRateMin { get; set; }
        public double VideoRateMin { get; set; }
        public double ThroughputDlRateMin { get; set; }
        public double ThroughputUlRateMin { get; set; }
        public double LowPercent { get; set; } = 80;

        public float RsrpMin { get; set; } = -130;
        public float RsrpMax { get; set; } = -40;
        public bool CheckRsrp { get; set; }
        public bool CheckSinr { get; set; }
        public float SinrMin { get; set; } = -10;
        public float SinrMax { get; set; } = 50;

        //是否选择综合低速率路段
        public bool CheckSynthesisLowSpeed { get; set; } = false;
        public bool CheckLTELowSpeed { get; set; } = false;
        public bool CheckNRLowSpeed { get; set; } = true;
        public double TestPointDistance { get; set; } = 50;

        public virtual double? GetSpeed(TestPoint tp)
        {
            if (SpeedTypeSelected == ESpeedType.APP)
            {
                return NRTpHelper.NrTpManager.GetAppSpeedMb(tp);
            }
            else
            {
                return getThroughputSpeed(tp);
            }
        }
        private double? getThroughputSpeed(TestPoint tp)
        {
            short? type = NRTpHelper.NrTpManager.GetAppType(tp);
            int? status = NRTpHelper.NrTpManager.GetAppStatus(tp); 
            if (type == null || status == null)
            {
                return null;
            }

            if (type == (int)AppType.FTP_Download 
                && (status == (int)EAppStatus.FTPSTATUS_DOWNLOAD_CONTINUE 
                || status == (int)EAppStatus.FTPSTATUS_DOWNLOAD_Qos))
            {
                if (SpeedTypeSelected == ESpeedType.MAC)
                {
                    return NRTpHelper.NrTpManager.GetMacDLMb(tp);
                }
                else
                {
                    return NRTpHelper.NrTpManager.GetPdcpDLMb(tp);
                }
            }
            else if (type == (int)AppType.FTP_Upload 
                && (status == (int)EAppStatus.FTPSTATUS_UPLOAD_CONTINUE
                || status == (int)EAppStatus.FTPSTATUS_UPLOAD_Qos))
            {
                if (SpeedTypeSelected == ESpeedType.MAC)
                {
                    return NRTpHelper.NrTpManager.GetMacULMb(tp);
                }
                else
                {
                    return NRTpHelper.NrTpManager.GetPdcpULMb(tp);
                }
            }
            return null;
        }

        public virtual bool IsLowSpeed(TestPoint tp)
        {
            double? speed = GetSpeed(tp);
            if (speed == null)
            {
                return false;
            }

            short? type = NRTpHelper.NrTpManager.GetAppType(tp);
            if (type == null)
            {
                return false;
            }
            if (SpeedTypeSelected == ESpeedType.APP)
            {
                return IsLowAppSpeed((short)type, (double)speed);
            }
            else
            {
                return IsLowThroughputSpeed((short)type, (double)speed);
            }
        }
        protected bool IsLowAppSpeed(short type, double speed)
        {
            if (type == (int)AppType.FTP_Download && CheckFTPDL)
            {
                return speed <= FTPDLRateMax && speed >= FTPDLRateMin;
            }
            if (type == (int)AppType.FTP_Upload && CheckFTPUL)
            {
                return speed <= FTPULRateMax && speed >= FTPULRateMin;
            }
            if (type == (int)AppType.Http_Download && CheckHTTP)
            {
                return speed <= HTTPRateMax && speed >= HTTPRateMin;
            }
            if (type == (int)AppType.Email_SMTP && CheckEmail)
            {
                return speed <= EmailRateMax && speed >= EmailRateMin;
            }
            if (type == (int)AppType.Http_Video && CheckVideo)
            {
                return speed <= VideoRateMax && speed >= VideoRateMin;
            }

            return false;
        }
        protected bool IsLowThroughputSpeed(short type, double speed)
        {
            if (type == (int)AppType.FTP_Download && CheckThroughputDl)
            {
                return speed <= ThroughputDlRateMax && speed >= ThroughputDlRateMin;
            }
            else if (type == (int)AppType.FTP_Upload && CheckThroughputUl)
            {
                return speed <= ThroughputUlRateMax && speed >= ThroughputUlRateMin;
            }
            return false;
        }

        public virtual bool IsIgnoreByRsrp(TestPoint tp)
        {
            if (CheckRsrp)
            {
                float? rsrp = NRTpHelper.NrTpManager.GetSCellRsrp(tp);
                bool isIgnore = NRTpHelper.JudgeValidData(rsrp, RsrpMin, RsrpMax);
                return !isIgnore;
            }
            return false;
        }
        public virtual bool IsIgnoreBySinr(TestPoint tp)
        {
            if (CheckSinr)
            {
                float? sinr = NRTpHelper.NrTpManager.GetSCellSinr(tp);
                bool isIgnore = NRTpHelper.JudgeValidData(sinr, SinrMin, SinrMax);
                return !isIgnore;
            }
            return false;
        }

        public enum ESpeedType
        {
            MAC,
            PDCP,
            APP
        }
    }
}
