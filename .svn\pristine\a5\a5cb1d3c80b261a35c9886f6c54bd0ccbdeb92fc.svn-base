﻿using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTStationAcceptance_XJ
{
    public class DIYQueryNRWirelessPlanning : DiyQueryDataBase
    {
        public string TableName { get; set; } = "";
        public List<NRWirelessPlanningDBInfo> NRWirelessPlanningDBInfoList { get; private set; }

        public DIYQueryNRWirelessPlanning()
            : base()
        { }

        public override string Name { get { return "查询NR无线规划"; } }

        protected override string getSqlTextString()
        {
            //string name = $"{TableName}_" + DateTime.Now.ToString("yyyyMMdd");
            StringBuilder selectSQL = new StringBuilder();
            selectSQL.AppendFormat(@"SELECT [基站名称],[小区名称],[TAC],[CellID],[PCI],[频段],[频点],[SSB频点],[小区带宽]
,[根序列],[子帧配比],[核心网接入模式] FROM {0} where [基站名称]='{1}'", TableName, btsName);

            return selectSQL.ToString();
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            int idx = 0;
            E_VType[] rType = new E_VType[12];
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_Int;
            rType[idx++] = E_VType.E_Int;
            rType[idx++] = E_VType.E_Int;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_Int;
            rType[idx++] = E_VType.E_Int;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx] = E_VType.E_String;
            return rType;
        }

        protected override void initData()
        {
            NRWirelessPlanningDBInfoList = new List<NRWirelessPlanningDBInfo>();
        }

        protected override void dealReceiveData(Package package)
        {
            NRWirelessPlanningDBInfo info = new NRWirelessPlanningDBInfo();
            info.FillData(package);
            NRWirelessPlanningDBInfoList.Add(info);
        }
    }

    public class DIYQueryNRWirelessPlanningTable : DiyQueryDataBase
    {
        public string TableName { get; private set; } = "";

        public DIYQueryNRWirelessPlanningTable()
            : base()
        { }

        public override string Name { get { return "查询最新NR无线规划表名"; } }

        protected override string getSqlTextString()
        {
            string sql = @"select top 1 name from sysobjects where type = 'U' and name like '%tb_xinjiang_NRWirelessPlanning%' order by name desc";
            return sql.ToString();
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            int idx = 0;
            E_VType[] rType = new E_VType[1];
            rType[idx] = E_VType.E_String;
            return rType;
        }

        protected override void dealReceiveData(Package package)
        {
            TableName = package.Content.GetParamString();
        }
    }

    public class NRWirelessPlanningDBInfo
    {
        public string BtsName { get; set; }
        public string CellName { get; set; }
        public int TAC { get; set; }
        public int CellID { get; set; }
        public int PCI { get; set; }
        public string FreqBand { get; set; }
        public int Freq { get; set; }
        public int SSBFreq { get; set; }
        public string Bandwidth { get; set; }
        public string PRACH { get; set; }
        public string SubFrameRatio { get; set; }
        public string Mode { get; set; }

        public void FillData(Package package)
        {
            BtsName = package.Content.GetParamString();
            CellName = package.Content.GetParamString();
            TAC = package.Content.GetParamInt();
            CellID = package.Content.GetParamInt();
            PCI = package.Content.GetParamInt();

            FreqBand = package.Content.GetParamString();
            Freq = package.Content.GetParamInt();
            SSBFreq = package.Content.GetParamInt();
            Bandwidth = package.Content.GetParamString();
            PRACH = package.Content.GetParamString();
            SubFrameRatio = package.Content.GetParamString();
            Mode = package.Content.GetParamString();
        }
    }
}
