﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class TDPoorC2IRoadSetForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.btnCancel = new DevExpress.XtraEditors.SimpleButton();
            this.btnOK = new DevExpress.XtraEditors.SimpleButton();
            this.labelControl6 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl5 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.numMaxDistance = new DevExpress.XtraEditors.SpinEdit();
            this.numDistance = new DevExpress.XtraEditors.SpinEdit();
            this.numDpchciThreshold = new DevExpress.XtraEditors.SpinEdit();
            this.numPccpchciThreshold = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl7 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl8 = new DevExpress.XtraEditors.LabelControl();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxDistance.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numDistance.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numDpchciThreshold.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numPccpchciThreshold.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // btnCancel
            // 
            this.btnCancel.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnCancel.Appearance.Options.UseFont = true;
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnCancel.Location = new System.Drawing.Point(195, 141);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 41;
            this.btnCancel.Text = "取消";
            this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
            // 
            // btnOK
            // 
            this.btnOK.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnOK.Appearance.Options.UseFont = true;
            this.btnOK.Location = new System.Drawing.Point(100, 141);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 40;
            this.btnOK.Text = "确定";
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // labelControl6
            // 
            this.labelControl6.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl6.Appearance.Options.UseFont = true;
            this.labelControl6.Location = new System.Drawing.Point(254, 101);
            this.labelControl6.Name = "labelControl6";
            this.labelControl6.Size = new System.Drawing.Size(12, 12);
            this.labelControl6.TabIndex = 39;
            this.labelControl6.Text = "米";
            // 
            // labelControl5
            // 
            this.labelControl5.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl5.Appearance.Options.UseFont = true;
            this.labelControl5.Location = new System.Drawing.Point(254, 75);
            this.labelControl5.Name = "labelControl5";
            this.labelControl5.Size = new System.Drawing.Size(12, 12);
            this.labelControl5.TabIndex = 38;
            this.labelControl5.Text = "米";
            // 
            // labelControl4
            // 
            this.labelControl4.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl4.Appearance.Options.UseFont = true;
            this.labelControl4.Location = new System.Drawing.Point(38, 101);
            this.labelControl4.Name = "labelControl4";
            this.labelControl4.Size = new System.Drawing.Size(102, 12);
            this.labelControl4.TabIndex = 37;
            this.labelControl4.Text = "相邻采样点距离 ≤";
            // 
            // labelControl3
            // 
            this.labelControl3.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl3.Appearance.Options.UseFont = true;
            this.labelControl3.Location = new System.Drawing.Point(80, 75);
            this.labelControl3.Name = "labelControl3";
            this.labelControl3.Size = new System.Drawing.Size(66, 12);
            this.labelControl3.TabIndex = 36;
            this.labelControl3.Text = "持续距离 ≥";
            // 
            // labelControl2
            // 
            this.labelControl2.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl2.Appearance.Options.UseFont = true;
            this.labelControl2.Location = new System.Drawing.Point(45, 48);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(96, 12);
            this.labelControl2.TabIndex = 35;
            this.labelControl2.Text = "TD_PCCPCH_C/I ≤";
            // 
            // labelControl1
            // 
            this.labelControl1.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl1.Appearance.Options.UseFont = true;
            this.labelControl1.Location = new System.Drawing.Point(59, 21);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(84, 12);
            this.labelControl1.TabIndex = 35;
            this.labelControl1.Text = "TD_DPCH_C/I ≤";
            // 
            // numMaxDistance
            // 
            this.numMaxDistance.EditValue = new decimal(new int[] {
            20,
            0,
            0,
            0});
            this.numMaxDistance.Location = new System.Drawing.Point(155, 98);
            this.numMaxDistance.Name = "numMaxDistance";
            this.numMaxDistance.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numMaxDistance.Properties.Appearance.Options.UseFont = true;
            this.numMaxDistance.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numMaxDistance.Properties.IsFloatValue = false;
            this.numMaxDistance.Properties.Mask.EditMask = "N00";
            this.numMaxDistance.Size = new System.Drawing.Size(87, 20);
            this.numMaxDistance.TabIndex = 35;
            // 
            // numDistance
            // 
            this.numDistance.EditValue = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.numDistance.Location = new System.Drawing.Point(155, 71);
            this.numDistance.Name = "numDistance";
            this.numDistance.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numDistance.Properties.Appearance.Options.UseFont = true;
            this.numDistance.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numDistance.Properties.IsFloatValue = false;
            this.numDistance.Properties.Mask.EditMask = "N00";
            this.numDistance.Size = new System.Drawing.Size(87, 20);
            this.numDistance.TabIndex = 34;
            // 
            // numDpchciThreshold
            // 
            this.numDpchciThreshold.EditValue = new decimal(new int[] {
            6,
            0,
            0,
            0});
            this.numDpchciThreshold.Location = new System.Drawing.Point(155, 17);
            this.numDpchciThreshold.Name = "numDpchciThreshold";
            this.numDpchciThreshold.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numDpchciThreshold.Properties.Appearance.Options.UseFont = true;
            this.numDpchciThreshold.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numDpchciThreshold.Properties.IsFloatValue = false;
            this.numDpchciThreshold.Properties.Mask.EditMask = "N00";
            this.numDpchciThreshold.Size = new System.Drawing.Size(87, 20);
            this.numDpchciThreshold.TabIndex = 32;
            // 
            // numPccpchciThreshold
            // 
            this.numPccpchciThreshold.EditValue = new decimal(new int[] {
            8,
            0,
            0,
            0});
            this.numPccpchciThreshold.Location = new System.Drawing.Point(155, 44);
            this.numPccpchciThreshold.Name = "numPccpchciThreshold";
            this.numPccpchciThreshold.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numPccpchciThreshold.Properties.Appearance.Options.UseFont = true;
            this.numPccpchciThreshold.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numPccpchciThreshold.Properties.IsFloatValue = false;
            this.numPccpchciThreshold.Properties.Mask.EditMask = "N00";
            this.numPccpchciThreshold.Size = new System.Drawing.Size(87, 20);
            this.numPccpchciThreshold.TabIndex = 33;
            // 
            // labelControl7
            // 
            this.labelControl7.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl7.Appearance.Options.UseFont = true;
            this.labelControl7.Location = new System.Drawing.Point(254, 21);
            this.labelControl7.Name = "labelControl7";
            this.labelControl7.Size = new System.Drawing.Size(12, 12);
            this.labelControl7.TabIndex = 42;
            this.labelControl7.Text = "dB";
            // 
            // labelControl8
            // 
            this.labelControl8.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl8.Appearance.Options.UseFont = true;
            this.labelControl8.Location = new System.Drawing.Point(254, 48);
            this.labelControl8.Name = "labelControl8";
            this.labelControl8.Size = new System.Drawing.Size(12, 12);
            this.labelControl8.TabIndex = 43;
            this.labelControl8.Text = "dB";
            // 
            // TDPoorC2IRoadSetForm
            // 
            this.AcceptButton = this.btnOK;
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.CancelButton = this.btnCancel;
            this.ClientSize = new System.Drawing.Size(307, 190);
            this.Controls.Add(this.labelControl8);
            this.Controls.Add(this.labelControl7);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.Controls.Add(this.labelControl6);
            this.Controls.Add(this.labelControl5);
            this.Controls.Add(this.labelControl4);
            this.Controls.Add(this.labelControl3);
            this.Controls.Add(this.labelControl2);
            this.Controls.Add(this.labelControl1);
            this.Controls.Add(this.numMaxDistance);
            this.Controls.Add(this.numDistance);
            this.Controls.Add(this.numDpchciThreshold);
            this.Controls.Add(this.numPccpchciThreshold);
            this.Name = "TDPoorC2IRoadSetForm";
            this.Text = "弱C2I路段分析条件设置";
            ((System.ComponentModel.ISupportInitialize)(this.numMaxDistance.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numDistance.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numDpchciThreshold.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numPccpchciThreshold.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraEditors.SimpleButton btnCancel;
        private DevExpress.XtraEditors.SimpleButton btnOK;
        private DevExpress.XtraEditors.LabelControl labelControl6;
        private DevExpress.XtraEditors.LabelControl labelControl5;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.SpinEdit numMaxDistance;
        private DevExpress.XtraEditors.SpinEdit numDistance;
        private DevExpress.XtraEditors.SpinEdit numDpchciThreshold;
        private DevExpress.XtraEditors.SpinEdit numPccpchciThreshold;
        private DevExpress.XtraEditors.LabelControl labelControl7;
        private DevExpress.XtraEditors.LabelControl labelControl8;
    }
}