﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class SetHavePilotFrequencyPolluteFilterDlg
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.btnCancel = new System.Windows.Forms.Button();
            this.btnOK = new System.Windows.Forms.Button();
            this.label2 = new System.Windows.Forms.Label();
            this.numRadius = new System.Windows.Forms.NumericUpDown();
            this.label3 = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.numSampleCountLimit = new System.Windows.Forms.NumericUpDown();
            this.numRxLevDValue = new System.Windows.Forms.NumericUpDown();
            this.numCellCountLimit = new System.Windows.Forms.NumericUpDown();
            this.label8 = new System.Windows.Forms.Label();
            this.label7 = new System.Windows.Forms.Label();
            this.numRxLevMax = new System.Windows.Forms.NumericUpDown();
            this.label1 = new System.Windows.Forms.Label();
            this.numRxlevMin = new System.Windows.Forms.NumericUpDown();
            this.label5 = new System.Windows.Forms.Label();
            this.label6 = new System.Windows.Forms.Label();
            this.label9 = new System.Windows.Forms.Label();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.chkCoOnly = new System.Windows.Forms.CheckBox();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            ((System.ComponentModel.ISupportInitialize)(this.numRadius)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSampleCountLimit)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRxLevDValue)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numCellCountLimit)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRxLevMax)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRxlevMin)).BeginInit();
            this.groupBox1.SuspendLayout();
            this.groupBox2.SuspendLayout();
            this.groupBox3.SuspendLayout();
            this.SuspendLayout();
            // 
            // btnCancel
            // 
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnCancel.Location = new System.Drawing.Point(319, 349);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 4;
            this.btnCancel.Text = "取消";
            this.btnCancel.UseVisualStyleBackColor = true;
            this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
            // 
            // btnOK
            // 
            this.btnOK.Location = new System.Drawing.Point(225, 349);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 3;
            this.btnOK.Text = "确定";
            this.btnOK.UseVisualStyleBackColor = true;
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(88, 34);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(65, 12);
            this.label2.TabIndex = 11;
            this.label2.Text = "汇聚半径≥";
            // 
            // numRadius
            // 
            this.numRadius.Increment = new decimal(new int[] {
            50,
            0,
            0,
            0});
            this.numRadius.Location = new System.Drawing.Point(157, 28);
            this.numRadius.Maximum = new decimal(new int[] {
            500,
            0,
            0,
            0});
            this.numRadius.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numRadius.Name = "numRadius";
            this.numRadius.Size = new System.Drawing.Size(94, 21);
            this.numRadius.TabIndex = 0;
            this.numRadius.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numRadius.Value = new decimal(new int[] {
            50,
            0,
            0,
            0});
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(255, 34);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(17, 12);
            this.label3.TabIndex = 13;
            this.label3.Text = "米";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(88, 68);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(65, 12);
            this.label4.TabIndex = 14;
            this.label4.Text = "采样点数≥";
            // 
            // numSampleCountLimit
            // 
            this.numSampleCountLimit.Location = new System.Drawing.Point(157, 64);
            this.numSampleCountLimit.Name = "numSampleCountLimit";
            this.numSampleCountLimit.Size = new System.Drawing.Size(96, 21);
            this.numSampleCountLimit.TabIndex = 1;
            this.numSampleCountLimit.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numSampleCountLimit.Value = new decimal(new int[] {
            10,
            0,
            0,
            0});
            // 
            // numRxLevDValue
            // 
            this.numRxLevDValue.Location = new System.Drawing.Point(157, 22);
            this.numRxLevDValue.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numRxLevDValue.Name = "numRxLevDValue";
            this.numRxLevDValue.Size = new System.Drawing.Size(94, 21);
            this.numRxLevDValue.TabIndex = 0;
            this.numRxLevDValue.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numRxLevDValue.Value = new decimal(new int[] {
            6,
            0,
            0,
            0});
            // 
            // numCellCountLimit
            // 
            this.numCellCountLimit.Location = new System.Drawing.Point(157, 55);
            this.numCellCountLimit.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numCellCountLimit.Name = "numCellCountLimit";
            this.numCellCountLimit.Size = new System.Drawing.Size(94, 21);
            this.numCellCountLimit.TabIndex = 1;
            this.numCellCountLimit.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numCellCountLimit.Value = new decimal(new int[] {
            4,
            0,
            0,
            0});
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(74, 28);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(77, 12);
            this.label8.TabIndex = 21;
            this.label8.Text = "覆盖带差值≤";
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(52, 59);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(101, 12);
            this.label7.TabIndex = 20;
            this.label7.Text = "覆盖带内小区数≥";
            // 
            // numRxLevMax
            // 
            this.numRxLevMax.Location = new System.Drawing.Point(238, 26);
            this.numRxLevMax.Maximum = new decimal(new int[] {
            10,
            0,
            0,
            -2147483648});
            this.numRxLevMax.Minimum = new decimal(new int[] {
            140,
            0,
            0,
            -2147483648});
            this.numRxLevMax.Name = "numRxLevMax";
            this.numRxLevMax.Size = new System.Drawing.Size(96, 21);
            this.numRxLevMax.TabIndex = 1;
            this.numRxLevMax.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numRxLevMax.Value = new decimal(new int[] {
            10,
            0,
            0,
            -2147483648});
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(155, 29);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(77, 12);
            this.label1.TabIndex = 23;
            this.label1.Text = "≤信号强度≤";
            // 
            // numRxlevMin
            // 
            this.numRxlevMin.Location = new System.Drawing.Point(55, 26);
            this.numRxlevMin.Maximum = new decimal(new int[] {
            10,
            0,
            0,
            -2147483648});
            this.numRxlevMin.Minimum = new decimal(new int[] {
            140,
            0,
            0,
            -2147483648});
            this.numRxlevMin.Name = "numRxlevMin";
            this.numRxlevMin.Size = new System.Drawing.Size(94, 21);
            this.numRxlevMin.TabIndex = 0;
            this.numRxlevMin.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numRxlevMin.Value = new decimal(new int[] {
            85,
            0,
            0,
            -2147483648});
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(255, 59);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(17, 12);
            this.label5.TabIndex = 24;
            this.label5.Text = "个";
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(255, 28);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(17, 12);
            this.label6.TabIndex = 25;
            this.label6.Text = "dB";
            // 
            // label9
            // 
            this.label9.AutoSize = true;
            this.label9.Location = new System.Drawing.Point(255, 68);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(17, 12);
            this.label9.TabIndex = 26;
            this.label9.Text = "个";
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.numRxlevMin);
            this.groupBox1.Controls.Add(this.label1);
            this.groupBox1.Controls.Add(this.numRxLevMax);
            this.groupBox1.Location = new System.Drawing.Point(28, 12);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(366, 64);
            this.groupBox1.TabIndex = 0;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "主服及邻区信号强度设置";
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.chkCoOnly);
            this.groupBox2.Controls.Add(this.label8);
            this.groupBox2.Controls.Add(this.numRxLevDValue);
            this.groupBox2.Controls.Add(this.label6);
            this.groupBox2.Controls.Add(this.label5);
            this.groupBox2.Controls.Add(this.label7);
            this.groupBox2.Controls.Add(this.numCellCountLimit);
            this.groupBox2.Location = new System.Drawing.Point(28, 82);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(366, 132);
            this.groupBox2.TabIndex = 1;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "覆盖带设置及同异频";
            // 
            // chkCoOnly
            // 
            this.chkCoOnly.AutoSize = true;
            this.chkCoOnly.Location = new System.Drawing.Point(54, 98);
            this.chkCoOnly.Name = "chkCoOnly";
            this.chkCoOnly.Size = new System.Drawing.Size(84, 16);
            this.chkCoOnly.TabIndex = 2;
            this.chkCoOnly.Text = "只分析同频";
            this.chkCoOnly.UseVisualStyleBackColor = true;
            // 
            // groupBox3
            // 
            this.groupBox3.Controls.Add(this.label2);
            this.groupBox3.Controls.Add(this.numRadius);
            this.groupBox3.Controls.Add(this.label3);
            this.groupBox3.Controls.Add(this.label9);
            this.groupBox3.Controls.Add(this.label4);
            this.groupBox3.Controls.Add(this.numSampleCountLimit);
            this.groupBox3.Location = new System.Drawing.Point(28, 220);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.Size = new System.Drawing.Size(366, 105);
            this.groupBox3.TabIndex = 2;
            this.groupBox3.TabStop = false;
            this.groupBox3.Text = "汇聚条件设置";
            // 
            // SetHavePilotFrequencyPolluteFilterDlg
            // 
            this.AcceptButton = this.btnOK;
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.CancelButton = this.btnCancel;
            this.ClientSize = new System.Drawing.Size(418, 386);
            this.Controls.Add(this.groupBox3);
            this.Controls.Add(this.groupBox2);
            this.Controls.Add(this.groupBox1);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedToolWindow;
            this.Name = "SetHavePilotFrequencyPolluteFilterDlg";
            this.Text = "导频污染分析条件设置";
            ((System.ComponentModel.ISupportInitialize)(this.numRadius)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSampleCountLimit)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRxLevDValue)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numCellCountLimit)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRxLevMax)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRxlevMin)).EndInit();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            this.groupBox3.ResumeLayout(false);
            this.groupBox3.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.Button btnOK;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.NumericUpDown numRadius;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.NumericUpDown numSampleCountLimit;
        private System.Windows.Forms.NumericUpDown numRxLevDValue;
        private System.Windows.Forms.NumericUpDown numCellCountLimit;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.NumericUpDown numRxLevMax;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.NumericUpDown numRxlevMin;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.Label label9;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.GroupBox groupBox3;
        private System.Windows.Forms.CheckBox chkCoOnly;
    }
}