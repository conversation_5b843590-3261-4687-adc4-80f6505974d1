﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class LTEAntennaOverlapCoverageForm 
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(LTEAntennaOverlapCoverageForm));
            this.dataGridViewCell = new BrightIdeasSoftware.TreeListView();
            this.olvColumnSN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnTAC = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnECI = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnEARFCN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnPCI = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnGrid = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCovType = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnAzimuth = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnPresetTILT = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMTILT = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnETILT = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnHeight = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnSampleCount = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRSRPAvg = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnSINRAvg = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnDistanceAvg = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnDiffRel = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnAreaAvg = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnPCellSampleCount = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnPCellRSRPAvg = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnPCellSINRAvg = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnPCellDistanceAvg = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnPCellDiffRel = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnPCellAreaAvg = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnOverlapCellSampleCount = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnOverlapCellRsrpAvg = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnOverlapCellSinrAvg = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnOverlapCellDistanceAvg = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnOverlapCellDiffRel = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnOverlapCellAreaAvg = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRSRP0_3dB采样点占比 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRSRP0_3dB覆盖面积 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRSRP3_6dB采样点占比 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRSRP3_6dB覆盖面积 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRSRP6_10dB采样点占比 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRSRP6_10dB覆盖面积 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRSRP10_15dB采样点占比 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRSRP10_15dB覆盖面积 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRSRP15dB采样点占比 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRSRP15dB覆盖面积 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnOtherCellSN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnOtherCellName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNGrid = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnM3 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnM6 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNAzimuth = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNPresetTILT = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNMTILT = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNETILT = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNHeight = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnSameBts = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnSameBts90 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnSameBtsAngle = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnAntennaType = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnOverlapSampleCount = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNeighbourRsrpAvg = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNeighbourSinrAvg = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNeighbourDistanceAvg = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNeighbourDiffRel = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNeighbourAreaAvg = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnThisCellRsrpAvg = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnThisCellSinrAvg = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnThisCellDistanceAvg = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnThisCellDiffRel = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnThisCellAreaAvg = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnPNDistance = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNeighbourTopSampleCount = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNeighbourTopRSRPAvg = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNeighbourTopSINRAvg = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNeighbourTopDistanceAvg = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNeighbourTopDiffRel = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNeighbourTopAreaAvg = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNeighbourTopCellRSRPAvg = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNeighbourTopCellSINRAvg = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNeighbourTopCellDistanceAvg = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNeighbourTopCellDiffRel = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNeighbourTopCellAreaAvg = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnDrangeport1 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnDrangeport2 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnDrangeport3 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnDrangeport4 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnDrangeport5 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnDrangeport6 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnDrangeport7 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnDrangeport8 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnDphaseport1 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnDphaseport2 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnDphaseport3 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnDphaseport4 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnDphaseport5 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnDphaseport6 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnDphaseport7 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnDphaseport8 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnGMax = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumn3dbValue = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumn6dbValue = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNDrangeport1 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNDrangeport2 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNDrangeport3 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNDrangeport4 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNDrangeport5 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNDrangeport6 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNDrangeport7 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNDrangeport8 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNDphaseport1 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNDphaseport2 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNDphaseport3 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNDphaseport4 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNDphaseport5 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNDphaseport6 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNDphaseport7 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNDphaseport8 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNGMax = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnN3dbValue = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnN6dbValue = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.ctxMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator1 = new System.Windows.Forms.ToolStripSeparator();
            this.miExpandAll = new System.Windows.Forms.ToolStripMenuItem();
            this.miCallapsAll = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem1 = new System.Windows.Forms.ToolStripSeparator();
            this.miShowGis = new System.Windows.Forms.ToolStripMenuItem();
            this.miShowSimulation = new System.Windows.Forms.ToolStripMenuItem();
            this.miShowSimulationAll = new System.Windows.Forms.ToolStripMenuItem();
            this.miShowSimulationMain = new System.Windows.Forms.ToolStripMenuItem();
            this.miShowSimulationOverlap = new System.Windows.Forms.ToolStripMenuItem();
            this.btnNextpage = new System.Windows.Forms.Button();
            this.btnPrevpage = new System.Windows.Forms.Button();
            this.label5 = new System.Windows.Forms.Label();
            this.txtPage = new System.Windows.Forms.TextBox();
            this.labPage = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.btnGo = new System.Windows.Forms.Button();
            this.labNum = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.btnSearch = new System.Windows.Forms.Button();
            this.txtCellName = new System.Windows.Forms.TextBox();
            this.label1 = new System.Windows.Forms.Label();
            this.olvColumn站间距 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumn所属区域 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewCell)).BeginInit();
            this.ctxMenu.SuspendLayout();
            this.SuspendLayout();
            // 
            // dataGridViewCell
            // 
            this.dataGridViewCell.AllColumns.Add(this.olvColumnSN);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnCellName);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnTAC);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnECI);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnEARFCN);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnPCI);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnGrid);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnCovType);
            this.dataGridViewCell.AllColumns.Add(this.olvColumn站间距);
            this.dataGridViewCell.AllColumns.Add(this.olvColumn所属区域);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnAzimuth);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnPresetTILT);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnMTILT);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnETILT);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnHeight);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnSampleCount);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnRSRPAvg);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnSINRAvg);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnDistanceAvg);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnDiffRel);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnAreaAvg);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnPCellSampleCount);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnPCellRSRPAvg);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnPCellSINRAvg);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnPCellDistanceAvg);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnPCellDiffRel);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnPCellAreaAvg);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnOverlapCellSampleCount);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnOverlapCellRsrpAvg);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnOverlapCellSinrAvg);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnOverlapCellDistanceAvg);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnOverlapCellDiffRel);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnOverlapCellAreaAvg);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnRSRP0_3dB采样点占比);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnRSRP0_3dB覆盖面积);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnRSRP3_6dB采样点占比);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnRSRP3_6dB覆盖面积);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnRSRP6_10dB采样点占比);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnRSRP6_10dB覆盖面积);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnRSRP10_15dB采样点占比);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnRSRP10_15dB覆盖面积);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnRSRP15dB采样点占比);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnRSRP15dB覆盖面积);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnOtherCellSN);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnOtherCellName);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnNGrid);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnM3);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnM6);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnNAzimuth);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnNPresetTILT);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnNMTILT);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnNETILT);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnNHeight);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnSameBts);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnSameBts90);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnSameBtsAngle);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnAntennaType);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnOverlapSampleCount);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnNeighbourRsrpAvg);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnNeighbourSinrAvg);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnNeighbourDistanceAvg);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnNeighbourDiffRel);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnNeighbourAreaAvg);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnThisCellRsrpAvg);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnThisCellSinrAvg);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnThisCellDistanceAvg);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnThisCellDiffRel);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnThisCellAreaAvg);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnPNDistance);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnNeighbourTopSampleCount);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnNeighbourTopRSRPAvg);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnNeighbourTopSINRAvg);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnNeighbourTopDistanceAvg);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnNeighbourTopDiffRel);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnNeighbourTopAreaAvg);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnNeighbourTopCellRSRPAvg);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnNeighbourTopCellSINRAvg);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnNeighbourTopCellDistanceAvg);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnNeighbourTopCellDiffRel);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnNeighbourTopCellAreaAvg);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnDrangeport1);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnDrangeport2);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnDrangeport3);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnDrangeport4);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnDrangeport5);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnDrangeport6);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnDrangeport7);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnDrangeport8);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnDphaseport1);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnDphaseport2);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnDphaseport3);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnDphaseport4);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnDphaseport5);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnDphaseport6);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnDphaseport7);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnDphaseport8);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnGMax);
            this.dataGridViewCell.AllColumns.Add(this.olvColumn3dbValue);
            this.dataGridViewCell.AllColumns.Add(this.olvColumn6dbValue);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnNDrangeport1);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnNDrangeport2);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnNDrangeport3);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnNDrangeport4);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnNDrangeport5);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnNDrangeport6);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnNDrangeport7);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnNDrangeport8);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnNDphaseport1);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnNDphaseport2);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnNDphaseport3);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnNDphaseport4);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnNDphaseport5);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnNDphaseport6);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnNDphaseport7);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnNDphaseport8);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnNGMax);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnN3dbValue);
            this.dataGridViewCell.AllColumns.Add(this.olvColumnN6dbValue);
            this.dataGridViewCell.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.dataGridViewCell.BackColor = System.Drawing.SystemColors.Window;
            this.dataGridViewCell.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnSN,
            this.olvColumnCellName,
            this.olvColumnTAC,
            this.olvColumnECI,
            this.olvColumnEARFCN,
            this.olvColumnPCI,
            this.olvColumnGrid,
            this.olvColumnCovType,
            this.olvColumn站间距,
            this.olvColumn所属区域,
            this.olvColumnAzimuth,
            this.olvColumnPresetTILT,
            this.olvColumnMTILT,
            this.olvColumnETILT,
            this.olvColumnHeight,
            this.olvColumnSampleCount,
            this.olvColumnRSRPAvg,
            this.olvColumnSINRAvg,
            this.olvColumnDistanceAvg,
            this.olvColumnDiffRel,
            this.olvColumnAreaAvg,
            this.olvColumnPCellSampleCount,
            this.olvColumnPCellRSRPAvg,
            this.olvColumnPCellSINRAvg,
            this.olvColumnPCellDistanceAvg,
            this.olvColumnPCellDiffRel,
            this.olvColumnPCellAreaAvg,
            this.olvColumnOverlapCellSampleCount,
            this.olvColumnOverlapCellRsrpAvg,
            this.olvColumnOverlapCellSinrAvg,
            this.olvColumnOverlapCellDistanceAvg,
            this.olvColumnOverlapCellDiffRel,
            this.olvColumnOverlapCellAreaAvg,
            this.olvColumnRSRP0_3dB采样点占比,
            this.olvColumnRSRP0_3dB覆盖面积,
            this.olvColumnRSRP3_6dB采样点占比,
            this.olvColumnRSRP3_6dB覆盖面积,
            this.olvColumnRSRP6_10dB采样点占比,
            this.olvColumnRSRP6_10dB覆盖面积,
            this.olvColumnRSRP10_15dB采样点占比,
            this.olvColumnRSRP10_15dB覆盖面积,
            this.olvColumnRSRP15dB采样点占比,
            this.olvColumnRSRP15dB覆盖面积,
            this.olvColumnOtherCellSN,
            this.olvColumnOtherCellName,
            this.olvColumnNGrid,
            this.olvColumnM3,
            this.olvColumnM6,
            this.olvColumnNAzimuth,
            this.olvColumnNPresetTILT,
            this.olvColumnNMTILT,
            this.olvColumnNETILT,
            this.olvColumnNHeight,
            this.olvColumnSameBts,
            this.olvColumnSameBts90,
            this.olvColumnSameBtsAngle,
            this.olvColumnAntennaType,
            this.olvColumnOverlapSampleCount,
            this.olvColumnNeighbourRsrpAvg,
            this.olvColumnNeighbourSinrAvg,
            this.olvColumnNeighbourDistanceAvg,
            this.olvColumnNeighbourDiffRel,
            this.olvColumnNeighbourAreaAvg,
            this.olvColumnThisCellRsrpAvg,
            this.olvColumnThisCellSinrAvg,
            this.olvColumnThisCellDistanceAvg,
            this.olvColumnThisCellDiffRel,
            this.olvColumnThisCellAreaAvg,
            this.olvColumnPNDistance,
            this.olvColumnNeighbourTopSampleCount,
            this.olvColumnNeighbourTopRSRPAvg,
            this.olvColumnNeighbourTopSINRAvg,
            this.olvColumnNeighbourTopDistanceAvg,
            this.olvColumnNeighbourTopDiffRel,
            this.olvColumnNeighbourTopAreaAvg,
            this.olvColumnNeighbourTopCellRSRPAvg,
            this.olvColumnNeighbourTopCellSINRAvg,
            this.olvColumnNeighbourTopCellDistanceAvg,
            this.olvColumnNeighbourTopCellDiffRel,
            this.olvColumnNeighbourTopCellAreaAvg,
            this.olvColumnDrangeport1,
            this.olvColumnDrangeport2,
            this.olvColumnDrangeport3,
            this.olvColumnDrangeport4,
            this.olvColumnDrangeport5,
            this.olvColumnDrangeport6,
            this.olvColumnDrangeport7,
            this.olvColumnDrangeport8,
            this.olvColumnDphaseport1,
            this.olvColumnDphaseport2,
            this.olvColumnDphaseport3,
            this.olvColumnDphaseport4,
            this.olvColumnDphaseport5,
            this.olvColumnDphaseport6,
            this.olvColumnDphaseport7,
            this.olvColumnDphaseport8,
            this.olvColumnGMax,
            this.olvColumn3dbValue,
            this.olvColumn6dbValue,
            this.olvColumnNDrangeport1,
            this.olvColumnNDrangeport2,
            this.olvColumnNDrangeport3,
            this.olvColumnNDrangeport4,
            this.olvColumnNDrangeport5,
            this.olvColumnNDrangeport6,
            this.olvColumnNDrangeport7,
            this.olvColumnNDrangeport8,
            this.olvColumnNDphaseport1,
            this.olvColumnNDphaseport2,
            this.olvColumnNDphaseport3,
            this.olvColumnNDphaseport4,
            this.olvColumnNDphaseport5,
            this.olvColumnNDphaseport6,
            this.olvColumnNDphaseport7,
            this.olvColumnNDphaseport8,
            this.olvColumnNGMax,
            this.olvColumnN3dbValue,
            this.olvColumnN6dbValue});
            this.dataGridViewCell.ContextMenuStrip = this.ctxMenu;
            this.dataGridViewCell.Cursor = System.Windows.Forms.Cursors.Default;
            this.dataGridViewCell.FullRowSelect = true;
            this.dataGridViewCell.GridLines = true;
            this.dataGridViewCell.HeaderWordWrap = true;
            this.dataGridViewCell.IsNeedShowOverlay = false;
            this.dataGridViewCell.Location = new System.Drawing.Point(0, 0);
            this.dataGridViewCell.Name = "dataGridViewCell";
            this.dataGridViewCell.OwnerDraw = true;
            this.dataGridViewCell.ShowGroups = false;
            this.dataGridViewCell.Size = new System.Drawing.Size(1039, 506);
            this.dataGridViewCell.TabIndex = 6;
            this.dataGridViewCell.UseCompatibleStateImageBehavior = false;
            this.dataGridViewCell.View = System.Windows.Forms.View.Details;
            this.dataGridViewCell.VirtualMode = true;
            // 
            // olvColumnSN
            // 
            this.olvColumnSN.AspectName = "";
            this.olvColumnSN.HeaderFont = null;
            this.olvColumnSN.Text = "序号";
            this.olvColumnSN.Width = 50;
            // 
            // olvColumnCellName
            // 
            this.olvColumnCellName.HeaderFont = null;
            this.olvColumnCellName.Text = "小区名称";
            this.olvColumnCellName.Width = 80;
            // 
            // olvColumnTAC
            // 
            this.olvColumnTAC.HeaderFont = null;
            this.olvColumnTAC.Text = "小区TAC";
            this.olvColumnTAC.Width = 80;
            // 
            // olvColumnECI
            // 
            this.olvColumnECI.HeaderFont = null;
            this.olvColumnECI.Text = "小区ECI";
            this.olvColumnECI.Width = 80;
            // 
            // olvColumnEARFCN
            // 
            this.olvColumnEARFCN.HeaderFont = null;
            this.olvColumnEARFCN.Text = "小区EARFCN";
            // 
            // olvColumnPCI
            // 
            this.olvColumnPCI.HeaderFont = null;
            this.olvColumnPCI.Text = "小区PCI";
            // 
            // olvColumnGrid
            // 
            this.olvColumnGrid.HeaderFont = null;
            this.olvColumnGrid.Text = "网格";
            // 
            // olvColumnCovType
            // 
            this.olvColumnCovType.HeaderFont = null;
            this.olvColumnCovType.Text = "覆盖类型";
            // 
            // olvColumnAzimuth
            // 
            this.olvColumnAzimuth.HeaderFont = null;
            this.olvColumnAzimuth.Text = "方位角";
            // 
            // olvColumnPresetTILT
            // 
            this.olvColumnPresetTILT.HeaderFont = null;
            this.olvColumnPresetTILT.Text = "预置下倾角";
            // 
            // olvColumnMTILT
            // 
            this.olvColumnMTILT.HeaderFont = null;
            this.olvColumnMTILT.Text = "机械下倾角";
            // 
            // olvColumnETILT
            // 
            this.olvColumnETILT.HeaderFont = null;
            this.olvColumnETILT.Text = "电调下倾角";
            // 
            // olvColumnHeight
            // 
            this.olvColumnHeight.HeaderFont = null;
            this.olvColumnHeight.Text = "挂高";
            // 
            // olvColumnSampleCount
            // 
            this.olvColumnSampleCount.HeaderFont = null;
            this.olvColumnSampleCount.Text = "采样点总数";
            // 
            // olvColumnRSRPAvg
            // 
            this.olvColumnRSRPAvg.HeaderFont = null;
            this.olvColumnRSRPAvg.Text = "小区平均RSRP";
            this.olvColumnRSRPAvg.Width = 61;
            // 
            // olvColumnSINRAvg
            // 
            this.olvColumnSINRAvg.HeaderFont = null;
            this.olvColumnSINRAvg.Text = "小区平均SINR";
            // 
            // olvColumnDistanceAvg
            // 
            this.olvColumnDistanceAvg.HeaderFont = null;
            this.olvColumnDistanceAvg.Text = "小区平均覆盖距离";
            // 
            // olvColumnDiffRel
            // 
            this.olvColumnDiffRel.HeaderFont = null;
            this.olvColumnDiffRel.Text = "小区扫描角";
            // 
            // olvColumnAreaAvg
            // 
            this.olvColumnAreaAvg.HeaderFont = null;
            this.olvColumnAreaAvg.Text = "小区覆盖面积";
            // 
            // olvColumnPCellSampleCount
            // 
            this.olvColumnPCellSampleCount.HeaderFont = null;
            this.olvColumnPCellSampleCount.Text = "小区主覆盖采样点";
            // 
            // olvColumnPCellRSRPAvg
            // 
            this.olvColumnPCellRSRPAvg.HeaderFont = null;
            this.olvColumnPCellRSRPAvg.Text = "小区主覆盖平均RSRP";
            // 
            // olvColumnPCellSINRAvg
            // 
            this.olvColumnPCellSINRAvg.HeaderFont = null;
            this.olvColumnPCellSINRAvg.Text = "小区主覆盖平均SINR";
            // 
            // olvColumnPCellDistanceAvg
            // 
            this.olvColumnPCellDistanceAvg.HeaderFont = null;
            this.olvColumnPCellDistanceAvg.Text = "小区主覆盖平均覆盖距离";
            // 
            // olvColumnPCellDiffRel
            // 
            this.olvColumnPCellDiffRel.HeaderFont = null;
            this.olvColumnPCellDiffRel.Text = "小区主扫描角";
            // 
            // olvColumnPCellAreaAvg
            // 
            this.olvColumnPCellAreaAvg.HeaderFont = null;
            this.olvColumnPCellAreaAvg.Text = "小区主覆盖面积";
            // 
            // olvColumnOverlapCellSampleCount
            // 
            this.olvColumnOverlapCellSampleCount.HeaderFont = null;
            this.olvColumnOverlapCellSampleCount.Text = "小区重叠覆盖采样点数";
            // 
            // olvColumnOverlapCellRsrpAvg
            // 
            this.olvColumnOverlapCellRsrpAvg.HeaderFont = null;
            this.olvColumnOverlapCellRsrpAvg.Text = "小区重叠覆盖平均RSRP";
            // 
            // olvColumnOverlapCellSinrAvg
            // 
            this.olvColumnOverlapCellSinrAvg.HeaderFont = null;
            this.olvColumnOverlapCellSinrAvg.Text = "小区重叠覆盖平均SINR";
            // 
            // olvColumnOverlapCellDistanceAvg
            // 
            this.olvColumnOverlapCellDistanceAvg.HeaderFont = null;
            this.olvColumnOverlapCellDistanceAvg.Text = "小区重叠覆盖平均覆盖距离";
            // 
            // olvColumnOverlapCellDiffRel
            // 
            this.olvColumnOverlapCellDiffRel.HeaderFont = null;
            this.olvColumnOverlapCellDiffRel.Text = "小区重叠覆盖扫描角";
            // 
            // olvColumnOverlapCellAreaAvg
            // 
            this.olvColumnOverlapCellAreaAvg.HeaderFont = null;
            this.olvColumnOverlapCellAreaAvg.Text = "小区重叠覆盖面积";
            // 
            // olvColumnRSRP0_3dB采样点占比
            // 
            this.olvColumnRSRP0_3dB采样点占比.HeaderFont = null;
            this.olvColumnRSRP0_3dB采样点占比.Text = "小区场强差值[0,3]dB采样点占比";
            // 
            // olvColumnRSRP0_3dB覆盖面积
            // 
            this.olvColumnRSRP0_3dB覆盖面积.HeaderFont = null;
            this.olvColumnRSRP0_3dB覆盖面积.Text = "小区场强差值[0,3]dB覆盖面积";
            // 
            // olvColumnRSRP3_6dB采样点占比
            // 
            this.olvColumnRSRP3_6dB采样点占比.HeaderFont = null;
            this.olvColumnRSRP3_6dB采样点占比.Text = "小区场强差值(3,6]dB采样点占比";
            // 
            // olvColumnRSRP3_6dB覆盖面积
            // 
            this.olvColumnRSRP3_6dB覆盖面积.HeaderFont = null;
            this.olvColumnRSRP3_6dB覆盖面积.Text = "小区场强差值(3,6]dB覆盖面积";
            // 
            // olvColumnRSRP6_10dB采样点占比
            // 
            this.olvColumnRSRP6_10dB采样点占比.HeaderFont = null;
            this.olvColumnRSRP6_10dB采样点占比.Text = "小区场强差值(6,10]dB采样点占比";
            // 
            // olvColumnRSRP6_10dB覆盖面积
            // 
            this.olvColumnRSRP6_10dB覆盖面积.HeaderFont = null;
            this.olvColumnRSRP6_10dB覆盖面积.Text = "小区场强差值(6,10]dB覆盖面积";
            // 
            // olvColumnRSRP10_15dB采样点占比
            // 
            this.olvColumnRSRP10_15dB采样点占比.HeaderFont = null;
            this.olvColumnRSRP10_15dB采样点占比.Text = "小区场强差值(10,15]dB采样点占比";
            // 
            // olvColumnRSRP10_15dB覆盖面积
            // 
            this.olvColumnRSRP10_15dB覆盖面积.HeaderFont = null;
            this.olvColumnRSRP10_15dB覆盖面积.Text = "小区场强差值(10,15]dB覆盖面积";
            // 
            // olvColumnRSRP15dB采样点占比
            // 
            this.olvColumnRSRP15dB采样点占比.HeaderFont = null;
            this.olvColumnRSRP15dB采样点占比.Text = "小区场强差值(15,∞)dB采样点占比";
            // 
            // olvColumnRSRP15dB覆盖面积
            // 
            this.olvColumnRSRP15dB覆盖面积.HeaderFont = null;
            this.olvColumnRSRP15dB覆盖面积.Text = "小区场强差值(15,∞)dB覆盖面积";
            // 
            // olvColumnOtherCellSN
            // 
            this.olvColumnOtherCellSN.HeaderFont = null;
            this.olvColumnOtherCellSN.Text = "邻小区序号";
            // 
            // olvColumnOtherCellName
            // 
            this.olvColumnOtherCellName.HeaderFont = null;
            this.olvColumnOtherCellName.Text = "邻小区名称";
            // 
            // olvColumnNGrid
            // 
            this.olvColumnNGrid.HeaderFont = null;
            this.olvColumnNGrid.Text = "网格";
            // 
            // olvColumnM3
            // 
            this.olvColumnM3.HeaderFont = null;
            this.olvColumnM3.Text = "是否模3干扰";
            // 
            // olvColumnM6
            // 
            this.olvColumnM6.HeaderFont = null;
            this.olvColumnM6.Text = "是否模6干扰";
            // 
            // olvColumnNAzimuth
            // 
            this.olvColumnNAzimuth.HeaderFont = null;
            this.olvColumnNAzimuth.Text = "方位角";
            // 
            // olvColumnNPresetTILT
            // 
            this.olvColumnNPresetTILT.HeaderFont = null;
            this.olvColumnNPresetTILT.Text = "预置下倾角";
            // 
            // olvColumnNMTILT
            // 
            this.olvColumnNMTILT.HeaderFont = null;
            this.olvColumnNMTILT.Text = "机械下倾角";
            // 
            // olvColumnNETILT
            // 
            this.olvColumnNETILT.HeaderFont = null;
            this.olvColumnNETILT.Text = "电调下倾角";
            // 
            // olvColumnNHeight
            // 
            this.olvColumnNHeight.HeaderFont = null;
            this.olvColumnNHeight.Text = "挂高";
            // 
            // olvColumnSameBts
            // 
            this.olvColumnSameBts.HeaderFont = null;
            this.olvColumnSameBts.Text = "是否主邻同频共站";
            // 
            // olvColumnSameBts90
            // 
            this.olvColumnSameBts90.HeaderFont = null;
            this.olvColumnSameBts90.Text = "共站小区间夹角是否小于90度";
            // 
            // olvColumnSameBtsAngle
            // 
            this.olvColumnSameBtsAngle.HeaderFont = null;
            this.olvColumnSameBtsAngle.Text = "小区间夹角";
            // 
            // olvColumnAntennaType
            // 
            this.olvColumnAntennaType.HeaderFont = null;
            this.olvColumnAntennaType.Text = "天线辐射对类型";
            // 
            // olvColumnOverlapSampleCount
            // 
            this.olvColumnOverlapSampleCount.HeaderFont = null;
            this.olvColumnOverlapSampleCount.Text = "重叠采样点数";
            // 
            // olvColumnNeighbourRsrpAvg
            // 
            this.olvColumnNeighbourRsrpAvg.HeaderFont = null;
            this.olvColumnNeighbourRsrpAvg.Text = "邻小区覆盖平均RSRP";
            // 
            // olvColumnNeighbourSinrAvg
            // 
            this.olvColumnNeighbourSinrAvg.HeaderFont = null;
            this.olvColumnNeighbourSinrAvg.Text = "邻小区覆盖平均SINR";
            // 
            // olvColumnNeighbourDistanceAvg
            // 
            this.olvColumnNeighbourDistanceAvg.HeaderFont = null;
            this.olvColumnNeighbourDistanceAvg.Text = "邻小区覆盖平均覆盖距离";
            // 
            // olvColumnNeighbourDiffRel
            // 
            this.olvColumnNeighbourDiffRel.HeaderFont = null;
            this.olvColumnNeighbourDiffRel.Text = "邻小区覆盖扫描角";
            // 
            // olvColumnNeighbourAreaAvg
            // 
            this.olvColumnNeighbourAreaAvg.HeaderFont = null;
            this.olvColumnNeighbourAreaAvg.Text = "邻小区覆盖面积";
            // 
            // olvColumnThisCellRsrpAvg
            // 
            this.olvColumnThisCellRsrpAvg.HeaderFont = null;
            this.olvColumnThisCellRsrpAvg.Text = "本小区覆盖平均RSRP";
            // 
            // olvColumnThisCellSinrAvg
            // 
            this.olvColumnThisCellSinrAvg.HeaderFont = null;
            this.olvColumnThisCellSinrAvg.Text = "本小区覆盖平均SINR";
            // 
            // olvColumnThisCellDistanceAvg
            // 
            this.olvColumnThisCellDistanceAvg.HeaderFont = null;
            this.olvColumnThisCellDistanceAvg.Text = "本小区覆盖平均覆盖距离";
            // 
            // olvColumnThisCellDiffRel
            // 
            this.olvColumnThisCellDiffRel.HeaderFont = null;
            this.olvColumnThisCellDiffRel.Text = "本小区覆盖扫描角";
            // 
            // olvColumnThisCellAreaAvg
            // 
            this.olvColumnThisCellAreaAvg.HeaderFont = null;
            this.olvColumnThisCellAreaAvg.Text = "本小区覆盖面积";
            // 
            // olvColumnPNDistance
            // 
            this.olvColumnPNDistance.HeaderFont = null;
            this.olvColumnPNDistance.Text = "主邻小区距离";
            // 
            // olvColumnNeighbourTopSampleCount
            // 
            this.olvColumnNeighbourTopSampleCount.HeaderFont = null;
            this.olvColumnNeighbourTopSampleCount.Text = "邻区最强时邻区采样点";
            // 
            // olvColumnNeighbourTopRSRPAvg
            // 
            this.olvColumnNeighbourTopRSRPAvg.HeaderFont = null;
            this.olvColumnNeighbourTopRSRPAvg.Text = "邻区最强时邻区平均RSRP";
            // 
            // olvColumnNeighbourTopSINRAvg
            // 
            this.olvColumnNeighbourTopSINRAvg.HeaderFont = null;
            this.olvColumnNeighbourTopSINRAvg.Text = "邻区最强时邻区平均SINR";
            // 
            // olvColumnNeighbourTopDistanceAvg
            // 
            this.olvColumnNeighbourTopDistanceAvg.HeaderFont = null;
            this.olvColumnNeighbourTopDistanceAvg.Text = "邻区最强时邻区平均覆盖距离";
            // 
            // olvColumnNeighbourTopDiffRel
            // 
            this.olvColumnNeighbourTopDiffRel.HeaderFont = null;
            this.olvColumnNeighbourTopDiffRel.Text = "邻区最强时邻区扫描角";
            // 
            // olvColumnNeighbourTopAreaAvg
            // 
            this.olvColumnNeighbourTopAreaAvg.HeaderFont = null;
            this.olvColumnNeighbourTopAreaAvg.Text = "邻区最强时邻区覆盖面积";
            // 
            // olvColumnNeighbourTopCellRSRPAvg
            // 
            this.olvColumnNeighbourTopCellRSRPAvg.HeaderFont = null;
            this.olvColumnNeighbourTopCellRSRPAvg.Text = "邻区最强时本小区平均RSRP";
            // 
            // olvColumnNeighbourTopCellSINRAvg
            // 
            this.olvColumnNeighbourTopCellSINRAvg.HeaderFont = null;
            this.olvColumnNeighbourTopCellSINRAvg.Text = "邻区最强时本小区平均SINR";
            // 
            // olvColumnNeighbourTopCellDistanceAvg
            // 
            this.olvColumnNeighbourTopCellDistanceAvg.HeaderFont = null;
            this.olvColumnNeighbourTopCellDistanceAvg.Text = "邻区最强时本小区平均覆盖距离";
            // 
            // olvColumnNeighbourTopCellDiffRel
            // 
            this.olvColumnNeighbourTopCellDiffRel.HeaderFont = null;
            this.olvColumnNeighbourTopCellDiffRel.Text = "邻区最强时本小区扫描角";
            // 
            // olvColumnNeighbourTopCellAreaAvg
            // 
            this.olvColumnNeighbourTopCellAreaAvg.HeaderFont = null;
            this.olvColumnNeighbourTopCellAreaAvg.Text = "邻区最强时本小区覆盖面积";
            // 
            // olvColumnDrangeport1
            // 
            this.olvColumnDrangeport1.HeaderFont = null;
            this.olvColumnDrangeport1.Text = "主小区端口1";
            // 
            // olvColumnDrangeport2
            // 
            this.olvColumnDrangeport2.HeaderFont = null;
            this.olvColumnDrangeport2.Text = "主小区端口2";
            // 
            // olvColumnDrangeport3
            // 
            this.olvColumnDrangeport3.HeaderFont = null;
            this.olvColumnDrangeport3.Text = "主小区端口3";
            // 
            // olvColumnDrangeport4
            // 
            this.olvColumnDrangeport4.HeaderFont = null;
            this.olvColumnDrangeport4.Text = "主小区端口4";
            // 
            // olvColumnDrangeport5
            // 
            this.olvColumnDrangeport5.HeaderFont = null;
            this.olvColumnDrangeport5.Text = "主小区端口5";
            // 
            // olvColumnDrangeport6
            // 
            this.olvColumnDrangeport6.HeaderFont = null;
            this.olvColumnDrangeport6.Text = "主小区端口6";
            // 
            // olvColumnDrangeport7
            // 
            this.olvColumnDrangeport7.HeaderFont = null;
            this.olvColumnDrangeport7.Text = "主小区端口7";
            // 
            // olvColumnDrangeport8
            // 
            this.olvColumnDrangeport8.HeaderFont = null;
            this.olvColumnDrangeport8.Text = "主小区端口8";
            // 
            // olvColumnDphaseport1
            // 
            this.olvColumnDphaseport1.HeaderFont = null;
            this.olvColumnDphaseport1.Text = "主小区相位1";
            // 
            // olvColumnDphaseport2
            // 
            this.olvColumnDphaseport2.HeaderFont = null;
            this.olvColumnDphaseport2.Text = "主小区相位2";
            // 
            // olvColumnDphaseport3
            // 
            this.olvColumnDphaseport3.HeaderFont = null;
            this.olvColumnDphaseport3.Text = "主小区相位3";
            // 
            // olvColumnDphaseport4
            // 
            this.olvColumnDphaseport4.HeaderFont = null;
            this.olvColumnDphaseport4.Text = "主小区相位4";
            // 
            // olvColumnDphaseport5
            // 
            this.olvColumnDphaseport5.HeaderFont = null;
            this.olvColumnDphaseport5.Text = "主小区相位5";
            // 
            // olvColumnDphaseport6
            // 
            this.olvColumnDphaseport6.HeaderFont = null;
            this.olvColumnDphaseport6.Text = "主小区相位6";
            // 
            // olvColumnDphaseport7
            // 
            this.olvColumnDphaseport7.HeaderFont = null;
            this.olvColumnDphaseport7.Text = "主小区相位7";
            // 
            // olvColumnDphaseport8
            // 
            this.olvColumnDphaseport8.HeaderFont = null;
            this.olvColumnDphaseport8.Text = "主小区相位8";
            // 
            // olvColumnGMax
            // 
            this.olvColumnGMax.HeaderFont = null;
            this.olvColumnGMax.Text = "主小区GMax";
            // 
            // olvColumn3dbValue
            // 
            this.olvColumn3dbValue.HeaderFont = null;
            this.olvColumn3dbValue.Text = "主小区3dbValue";
            // 
            // olvColumn6dbValue
            // 
            this.olvColumn6dbValue.HeaderFont = null;
            this.olvColumn6dbValue.Text = "主小区6dbValue";
            // 
            // olvColumnNDrangeport1
            // 
            this.olvColumnNDrangeport1.HeaderFont = null;
            this.olvColumnNDrangeport1.Text = "邻小区端口1";
            // 
            // olvColumnNDrangeport2
            // 
            this.olvColumnNDrangeport2.HeaderFont = null;
            this.olvColumnNDrangeport2.Text = "邻小区端口2";
            // 
            // olvColumnNDrangeport3
            // 
            this.olvColumnNDrangeport3.HeaderFont = null;
            this.olvColumnNDrangeport3.Text = "邻小区端口3";
            // 
            // olvColumnNDrangeport4
            // 
            this.olvColumnNDrangeport4.HeaderFont = null;
            this.olvColumnNDrangeport4.Text = "邻小区端口4";
            // 
            // olvColumnNDrangeport5
            // 
            this.olvColumnNDrangeport5.HeaderFont = null;
            this.olvColumnNDrangeport5.Text = "邻小区端口5";
            // 
            // olvColumnNDrangeport6
            // 
            this.olvColumnNDrangeport6.HeaderFont = null;
            this.olvColumnNDrangeport6.Text = "邻小区端口6";
            // 
            // olvColumnNDrangeport7
            // 
            this.olvColumnNDrangeport7.HeaderFont = null;
            this.olvColumnNDrangeport7.Text = "邻小区端口7";
            // 
            // olvColumnNDrangeport8
            // 
            this.olvColumnNDrangeport8.HeaderFont = null;
            this.olvColumnNDrangeport8.Text = "邻小区端口8";
            // 
            // olvColumnNDphaseport1
            // 
            this.olvColumnNDphaseport1.HeaderFont = null;
            this.olvColumnNDphaseport1.Text = "邻小区相位1";
            // 
            // olvColumnNDphaseport2
            // 
            this.olvColumnNDphaseport2.HeaderFont = null;
            this.olvColumnNDphaseport2.Text = "邻小区相位2";
            // 
            // olvColumnNDphaseport3
            // 
            this.olvColumnNDphaseport3.HeaderFont = null;
            this.olvColumnNDphaseport3.Text = "邻小区相位3";
            // 
            // olvColumnNDphaseport4
            // 
            this.olvColumnNDphaseport4.HeaderFont = null;
            this.olvColumnNDphaseport4.Text = "邻小区相位4";
            // 
            // olvColumnNDphaseport5
            // 
            this.olvColumnNDphaseport5.HeaderFont = null;
            this.olvColumnNDphaseport5.Text = "邻小区相位5";
            // 
            // olvColumnNDphaseport6
            // 
            this.olvColumnNDphaseport6.HeaderFont = null;
            this.olvColumnNDphaseport6.Text = "邻小区相位6";
            // 
            // olvColumnNDphaseport7
            // 
            this.olvColumnNDphaseport7.HeaderFont = null;
            this.olvColumnNDphaseport7.Text = "邻小区相位7";
            // 
            // olvColumnNDphaseport8
            // 
            this.olvColumnNDphaseport8.HeaderFont = null;
            this.olvColumnNDphaseport8.Text = "邻小区相位8";
            // 
            // olvColumnNGMax
            // 
            this.olvColumnNGMax.HeaderFont = null;
            this.olvColumnNGMax.Text = "邻小区GMax";
            // 
            // olvColumnN3dbValue
            // 
            this.olvColumnN3dbValue.HeaderFont = null;
            this.olvColumnN3dbValue.Text = "邻小区3dbValue";
            // 
            // olvColumnN6dbValue
            // 
            this.olvColumnN6dbValue.HeaderFont = null;
            this.olvColumnN6dbValue.Text = "邻小区6dbValue";
            // 
            // ctxMenu
            // 
            this.ctxMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExportExcel,
            this.toolStripSeparator1,
            this.miExpandAll,
            this.miCallapsAll,
            this.toolStripMenuItem1,
            this.miShowGis,
            this.miShowSimulation});
            this.ctxMenu.Name = "ctxMenu";
            this.ctxMenu.Size = new System.Drawing.Size(209, 126);
            // 
            // miExportExcel
            // 
            this.miExportExcel.Name = "miExportExcel";
            this.miExportExcel.Size = new System.Drawing.Size(208, 22);
            this.miExportExcel.Text = "导出Excel";
            this.miExportExcel.Click += new System.EventHandler(this.miExportExcel_Click);
            // 
            // toolStripSeparator1
            // 
            this.toolStripSeparator1.Name = "toolStripSeparator1";
            this.toolStripSeparator1.Size = new System.Drawing.Size(205, 6);
            // 
            // miExpandAll
            // 
            this.miExpandAll.Name = "miExpandAll";
            this.miExpandAll.Size = new System.Drawing.Size(208, 22);
            this.miExpandAll.Text = "全部展开";
            this.miExpandAll.Click += new System.EventHandler(this.miExpandAll_Click);
            // 
            // miCallapsAll
            // 
            this.miCallapsAll.Name = "miCallapsAll";
            this.miCallapsAll.Size = new System.Drawing.Size(208, 22);
            this.miCallapsAll.Text = "全部合并";
            this.miCallapsAll.Click += new System.EventHandler(this.miCallapsAll_Click);
            // 
            // toolStripMenuItem1
            // 
            this.toolStripMenuItem1.Name = "toolStripMenuItem1";
            this.toolStripMenuItem1.Size = new System.Drawing.Size(205, 6);
            // 
            // miShowGis
            // 
            this.miShowGis.Name = "miShowGis";
            this.miShowGis.Size = new System.Drawing.Size(208, 22);
            this.miShowGis.Text = "显示选择小区及其采样点";
            this.miShowGis.Click += new System.EventHandler(this.miShowGis_Click);
            // 
            // miShowSimulation
            // 
            this.miShowSimulation.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miShowSimulationAll,
            this.miShowSimulationMain,
            this.miShowSimulationOverlap});
            this.miShowSimulation.Name = "miShowSimulation";
            this.miShowSimulation.Size = new System.Drawing.Size(208, 22);
            this.miShowSimulation.Text = "显示天线覆盖";
            // 
            // miShowSimulationAll
            // 
            this.miShowSimulationAll.Name = "miShowSimulationAll";
            this.miShowSimulationAll.Size = new System.Drawing.Size(148, 22);
            this.miShowSimulationAll.Text = "当前覆盖小区";
            this.miShowSimulationAll.Click += new System.EventHandler(this.miShowSimulationAll_Click);
            // 
            // miShowSimulationMain
            // 
            this.miShowSimulationMain.Name = "miShowSimulationMain";
            this.miShowSimulationMain.Size = new System.Drawing.Size(148, 22);
            this.miShowSimulationMain.Text = "主覆盖小区";
            this.miShowSimulationMain.Click += new System.EventHandler(this.miShowSimulationMain_Click);
            // 
            // miShowSimulationOverlap
            // 
            this.miShowSimulationOverlap.Name = "miShowSimulationOverlap";
            this.miShowSimulationOverlap.Size = new System.Drawing.Size(148, 22);
            this.miShowSimulationOverlap.Text = "重叠覆盖小区";
            this.miShowSimulationOverlap.Click += new System.EventHandler(this.miShowSimulationOverlap_Click);
            // 
            // btnNextpage
            // 
            this.btnNextpage.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnNextpage.Location = new System.Drawing.Point(718, 516);
            this.btnNextpage.Name = "btnNextpage";
            this.btnNextpage.Size = new System.Drawing.Size(33, 23);
            this.btnNextpage.TabIndex = 68;
            this.btnNextpage.Text = ">>";
            this.btnNextpage.UseVisualStyleBackColor = true;
            this.btnNextpage.Click += new System.EventHandler(this.btnNextpage_Click);
            // 
            // btnPrevpage
            // 
            this.btnPrevpage.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnPrevpage.Location = new System.Drawing.Point(679, 516);
            this.btnPrevpage.Name = "btnPrevpage";
            this.btnPrevpage.Size = new System.Drawing.Size(33, 23);
            this.btnPrevpage.TabIndex = 67;
            this.btnPrevpage.Text = "<<";
            this.btnPrevpage.UseVisualStyleBackColor = true;
            this.btnPrevpage.Click += new System.EventHandler(this.btnPrevpage_Click);
            // 
            // label5
            // 
            this.label5.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(621, 518);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(19, 14);
            this.label5.TabIndex = 66;
            this.label5.Text = "页";
            // 
            // txtPage
            // 
            this.txtPage.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.txtPage.Location = new System.Drawing.Point(556, 515);
            this.txtPage.Name = "txtPage";
            this.txtPage.Size = new System.Drawing.Size(63, 22);
            this.txtPage.TabIndex = 65;
            this.txtPage.Text = "1";
            // 
            // labPage
            // 
            this.labPage.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.labPage.AutoSize = true;
            this.labPage.Location = new System.Drawing.Point(456, 520);
            this.labPage.Name = "labPage";
            this.labPage.Size = new System.Drawing.Size(14, 14);
            this.labPage.TabIndex = 64;
            this.labPage.Text = "0";
            // 
            // label4
            // 
            this.label4.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(413, 519);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(43, 14);
            this.label4.TabIndex = 63;
            this.label4.Text = "个，共";
            // 
            // btnGo
            // 
            this.btnGo.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnGo.Location = new System.Drawing.Point(640, 515);
            this.btnGo.Name = "btnGo";
            this.btnGo.Size = new System.Drawing.Size(33, 23);
            this.btnGo.TabIndex = 62;
            this.btnGo.Text = "GO";
            this.btnGo.UseVisualStyleBackColor = true;
            this.btnGo.Click += new System.EventHandler(this.btnGo_Click);
            // 
            // labNum
            // 
            this.labNum.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.labNum.AutoSize = true;
            this.labNum.Location = new System.Drawing.Point(368, 520);
            this.labNum.Name = "labNum";
            this.labNum.Size = new System.Drawing.Size(14, 14);
            this.labNum.TabIndex = 61;
            this.labNum.Text = "0";
            // 
            // label3
            // 
            this.label3.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(490, 519);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(67, 14);
            this.label3.TabIndex = 60;
            this.label3.Text = "页，跳转至";
            // 
            // label2
            // 
            this.label2.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(292, 519);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(79, 14);
            this.label2.TabIndex = 59;
            this.label2.Text = "总计小区共：";
            // 
            // btnSearch
            // 
            this.btnSearch.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnSearch.Location = new System.Drawing.Point(986, 515);
            this.btnSearch.Name = "btnSearch";
            this.btnSearch.Size = new System.Drawing.Size(42, 23);
            this.btnSearch.TabIndex = 58;
            this.btnSearch.Text = "查找";
            this.btnSearch.UseVisualStyleBackColor = true;
            this.btnSearch.Click += new System.EventHandler(this.btnSearch_Click);
            // 
            // txtCellName
            // 
            this.txtCellName.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.txtCellName.Location = new System.Drawing.Point(824, 516);
            this.txtCellName.Name = "txtCellName";
            this.txtCellName.Size = new System.Drawing.Size(157, 22);
            this.txtCellName.TabIndex = 57;
            // 
            // label1
            // 
            this.label1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(763, 520);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(67, 14);
            this.label1.TabIndex = 56;
            this.label1.Text = "小区名称：";
            // 
            // olvColumn站间距
            // 
            this.olvColumn站间距.HeaderFont = null;
            this.olvColumn站间距.Text = "站间距";
            // 
            // olvColumn所属区域
            // 
            this.olvColumn所属区域.HeaderFont = null;
            this.olvColumn所属区域.Text = "所属区域";
            // 
            // LTEAntennaOverlapCoverageForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1039, 549);
            this.Controls.Add(this.btnNextpage);
            this.Controls.Add(this.btnPrevpage);
            this.Controls.Add(this.label5);
            this.Controls.Add(this.txtPage);
            this.Controls.Add(this.labPage);
            this.Controls.Add(this.label4);
            this.Controls.Add(this.btnGo);
            this.Controls.Add(this.labNum);
            this.Controls.Add(this.label3);
            this.Controls.Add(this.label2);
            this.Controls.Add(this.btnSearch);
            this.Controls.Add(this.txtCellName);
            this.Controls.Add(this.label1);
            this.Controls.Add(this.dataGridViewCell);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Name = "LTEAntennaOverlapCoverageForm";
            this.Text = "LTE天线覆盖优化";
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewCell)).EndInit();
            this.ctxMenu.ResumeLayout(false);
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private BrightIdeasSoftware.TreeListView dataGridViewCell;
        private BrightIdeasSoftware.OLVColumn olvColumnSN;
        private BrightIdeasSoftware.OLVColumn olvColumnCellName;
        private BrightIdeasSoftware.OLVColumn olvColumnTAC;
        private BrightIdeasSoftware.OLVColumn olvColumnECI;
        private BrightIdeasSoftware.OLVColumn olvColumnEARFCN;
        private BrightIdeasSoftware.OLVColumn olvColumnPCI;
        private BrightIdeasSoftware.OLVColumn olvColumnCovType;
        private BrightIdeasSoftware.OLVColumn olvColumnAzimuth;
        private BrightIdeasSoftware.OLVColumn olvColumnPresetTILT;
        private BrightIdeasSoftware.OLVColumn olvColumnMTILT;
        private BrightIdeasSoftware.OLVColumn olvColumnETILT;
        private BrightIdeasSoftware.OLVColumn olvColumnSampleCount;
        private BrightIdeasSoftware.OLVColumn olvColumnRSRPAvg;
        private BrightIdeasSoftware.OLVColumn olvColumnSINRAvg;
        private BrightIdeasSoftware.OLVColumn olvColumnDistanceAvg;
        private BrightIdeasSoftware.OLVColumn olvColumnAreaAvg;
        private BrightIdeasSoftware.OLVColumn olvColumnPCellSampleCount;
        private BrightIdeasSoftware.OLVColumn olvColumnPCellRSRPAvg;
        private BrightIdeasSoftware.OLVColumn olvColumnPCellSINRAvg;
        private BrightIdeasSoftware.OLVColumn olvColumnPCellDistanceAvg;
        private BrightIdeasSoftware.OLVColumn olvColumnPCellAreaAvg;
        private BrightIdeasSoftware.OLVColumn olvColumnOverlapCellSampleCount;
        private BrightIdeasSoftware.OLVColumn olvColumnOverlapCellRsrpAvg;
        private BrightIdeasSoftware.OLVColumn olvColumnOverlapCellSinrAvg;
        private BrightIdeasSoftware.OLVColumn olvColumnOverlapCellDistanceAvg;
        private BrightIdeasSoftware.OLVColumn olvColumnOverlapCellAreaAvg;
        private BrightIdeasSoftware.OLVColumn olvColumnOtherCellSN;
        private BrightIdeasSoftware.OLVColumn olvColumnOtherCellName;
        private BrightIdeasSoftware.OLVColumn olvColumnOverlapSampleCount;
        private BrightIdeasSoftware.OLVColumn olvColumnM3;
        private BrightIdeasSoftware.OLVColumn olvColumnM6;
        private BrightIdeasSoftware.OLVColumn olvColumnNeighbourRsrpAvg;
        private BrightIdeasSoftware.OLVColumn olvColumnNeighbourSinrAvg;
        private BrightIdeasSoftware.OLVColumn olvColumnNeighbourDistanceAvg;
        private BrightIdeasSoftware.OLVColumn olvColumnNeighbourAreaAvg;
        private BrightIdeasSoftware.OLVColumn olvColumnThisCellRsrpAvg;
        private BrightIdeasSoftware.OLVColumn olvColumnThisCellSinrAvg;
        private BrightIdeasSoftware.OLVColumn olvColumnThisCellDistanceAvg;
        private BrightIdeasSoftware.OLVColumn olvColumnThisCellAreaAvg;
        private BrightIdeasSoftware.OLVColumn olvColumnPNDistance;
        private BrightIdeasSoftware.OLVColumn olvColumnNeighbourTopSampleCount;
        private BrightIdeasSoftware.OLVColumn olvColumnNeighbourTopRSRPAvg;
        private BrightIdeasSoftware.OLVColumn olvColumnNeighbourTopDistanceAvg;
        private BrightIdeasSoftware.OLVColumn olvColumnNeighbourTopAreaAvg;
        private BrightIdeasSoftware.OLVColumn olvColumnNeighbourTopCellRSRPAvg;
        private BrightIdeasSoftware.OLVColumn olvColumnNeighbourTopCellSINRAvg;
        private BrightIdeasSoftware.OLVColumn olvColumnNeighbourTopCellDistanceAvg;
        private BrightIdeasSoftware.OLVColumn olvColumnNeighbourTopCellAreaAvg;
        private System.Windows.Forms.Button btnNextpage;
        private System.Windows.Forms.Button btnPrevpage;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.TextBox txtPage;
        private System.Windows.Forms.Label labPage;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.Button btnGo;
        private System.Windows.Forms.Label labNum;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Button btnSearch;
        private System.Windows.Forms.TextBox txtCellName;
        private System.Windows.Forms.Label label1;
        private BrightIdeasSoftware.OLVColumn olvColumnHeight;
        private BrightIdeasSoftware.OLVColumn olvColumnNeighbourTopSINRAvg;
        private System.Windows.Forms.ContextMenuStrip ctxMenu;
        private System.Windows.Forms.ToolStripMenuItem miExportExcel;
        private System.Windows.Forms.ToolStripSeparator toolStripMenuItem1;
        private System.Windows.Forms.ToolStripMenuItem miExpandAll;
        private System.Windows.Forms.ToolStripMenuItem miCallapsAll;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator1;
        private System.Windows.Forms.ToolStripMenuItem miShowGis;
        private BrightIdeasSoftware.OLVColumn olvColumnNAzimuth;
        private BrightIdeasSoftware.OLVColumn olvColumnNPresetTILT;
        private BrightIdeasSoftware.OLVColumn olvColumnNMTILT;
        private BrightIdeasSoftware.OLVColumn olvColumnNETILT;
        private BrightIdeasSoftware.OLVColumn olvColumnNHeight;
        private BrightIdeasSoftware.OLVColumn olvColumnDiffRel;
        private BrightIdeasSoftware.OLVColumn olvColumnPCellDiffRel;
        private BrightIdeasSoftware.OLVColumn olvColumnOverlapCellDiffRel;
        private BrightIdeasSoftware.OLVColumn olvColumnNeighbourDiffRel;
        private BrightIdeasSoftware.OLVColumn olvColumnThisCellDiffRel;
        private BrightIdeasSoftware.OLVColumn olvColumnNeighbourTopDiffRel;
        private BrightIdeasSoftware.OLVColumn olvColumnNeighbourTopCellDiffRel;
        private BrightIdeasSoftware.OLVColumn olvColumnSameBts;
        private BrightIdeasSoftware.OLVColumn olvColumnSameBts90;
        private BrightIdeasSoftware.OLVColumn olvColumnGrid;
        private BrightIdeasSoftware.OLVColumn olvColumnNGrid;
        private BrightIdeasSoftware.OLVColumn olvColumnSameBtsAngle;
        private BrightIdeasSoftware.OLVColumn olvColumnDrangeport1;
        private BrightIdeasSoftware.OLVColumn olvColumnDrangeport2;
        private BrightIdeasSoftware.OLVColumn olvColumnDrangeport3;
        private BrightIdeasSoftware.OLVColumn olvColumnDrangeport4;
        private BrightIdeasSoftware.OLVColumn olvColumnDrangeport5;
        private BrightIdeasSoftware.OLVColumn olvColumnDrangeport6;
        private BrightIdeasSoftware.OLVColumn olvColumnDrangeport7;
        private BrightIdeasSoftware.OLVColumn olvColumnDrangeport8;
        private BrightIdeasSoftware.OLVColumn olvColumnDphaseport1;
        private BrightIdeasSoftware.OLVColumn olvColumnDphaseport2;
        private BrightIdeasSoftware.OLVColumn olvColumnDphaseport3;
        private BrightIdeasSoftware.OLVColumn olvColumnDphaseport4;
        private BrightIdeasSoftware.OLVColumn olvColumnDphaseport5;
        private BrightIdeasSoftware.OLVColumn olvColumnDphaseport6;
        private BrightIdeasSoftware.OLVColumn olvColumnDphaseport7;
        private BrightIdeasSoftware.OLVColumn olvColumnDphaseport8;
        private BrightIdeasSoftware.OLVColumn olvColumnGMax;
        private BrightIdeasSoftware.OLVColumn olvColumn3dbValue;
        private BrightIdeasSoftware.OLVColumn olvColumn6dbValue;
        private BrightIdeasSoftware.OLVColumn olvColumnNDrangeport1;
        private BrightIdeasSoftware.OLVColumn olvColumnNDrangeport2;
        private BrightIdeasSoftware.OLVColumn olvColumnNDrangeport3;
        private BrightIdeasSoftware.OLVColumn olvColumnNDrangeport4;
        private BrightIdeasSoftware.OLVColumn olvColumnNDrangeport5;
        private BrightIdeasSoftware.OLVColumn olvColumnNDrangeport6;
        private BrightIdeasSoftware.OLVColumn olvColumnNDrangeport7;
        private BrightIdeasSoftware.OLVColumn olvColumnNDrangeport8;
        private BrightIdeasSoftware.OLVColumn olvColumnNDphaseport1;
        private BrightIdeasSoftware.OLVColumn olvColumnNDphaseport2;
        private BrightIdeasSoftware.OLVColumn olvColumnNDphaseport3;
        private BrightIdeasSoftware.OLVColumn olvColumnNDphaseport4;
        private BrightIdeasSoftware.OLVColumn olvColumnNDphaseport5;
        private BrightIdeasSoftware.OLVColumn olvColumnNDphaseport6;
        private BrightIdeasSoftware.OLVColumn olvColumnNDphaseport7;
        private BrightIdeasSoftware.OLVColumn olvColumnNDphaseport8;
        private BrightIdeasSoftware.OLVColumn olvColumnNGMax;
        private BrightIdeasSoftware.OLVColumn olvColumnN3dbValue;
        private BrightIdeasSoftware.OLVColumn olvColumnN6dbValue;
        private System.Windows.Forms.ToolStripMenuItem miShowSimulation;
        private System.Windows.Forms.ToolStripMenuItem miShowSimulationAll;
        private System.Windows.Forms.ToolStripMenuItem miShowSimulationMain;
        private System.Windows.Forms.ToolStripMenuItem miShowSimulationOverlap;
        private BrightIdeasSoftware.OLVColumn olvColumnRSRP0_3dB采样点占比;
        private BrightIdeasSoftware.OLVColumn olvColumnRSRP0_3dB覆盖面积;
        private BrightIdeasSoftware.OLVColumn olvColumnRSRP3_6dB采样点占比;
        private BrightIdeasSoftware.OLVColumn olvColumnRSRP3_6dB覆盖面积;
        private BrightIdeasSoftware.OLVColumn olvColumnRSRP6_10dB采样点占比;
        private BrightIdeasSoftware.OLVColumn olvColumnRSRP6_10dB覆盖面积;
        private BrightIdeasSoftware.OLVColumn olvColumnRSRP10_15dB采样点占比;
        private BrightIdeasSoftware.OLVColumn olvColumnRSRP10_15dB覆盖面积;
        private BrightIdeasSoftware.OLVColumn olvColumnRSRP15dB采样点占比;
        private BrightIdeasSoftware.OLVColumn olvColumnRSRP15dB覆盖面积;
        private BrightIdeasSoftware.OLVColumn olvColumnAntennaType;
        private BrightIdeasSoftware.OLVColumn olvColumn站间距;
        private BrightIdeasSoftware.OLVColumn olvColumn所属区域;
    }
}