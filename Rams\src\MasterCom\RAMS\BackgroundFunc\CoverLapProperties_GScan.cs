﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;

using MasterCom.RAMS.ZTFunc;

namespace MasterCom.RAMS.Func.SystemSetting
{
    public partial class CoverLapProperties_GScan : PropertiesControl
    {
        public CoverLapProperties_GScan(ZTCellCoverLapByRegion_GScan queryFunc)
        {
            InitializeComponent();
            this.queryFunc = queryFunc;
        }

        public override string ParentName
        {
            get { return queryFunc.FuncType.ToString(); }
        }

        public override string ParentSubName
        {
            get { return queryFunc.SubFuncType.ToString(); }
        }

        public override string SelfName
        {
            get { return queryFunc.Name; }
        }

        public override string TabPageName
        {
            get { return queryFunc.Name; }
        }

        public override void Flush()
        {
            chkBackgroundStat.Checked = queryFunc.BackgroundStat;
            numMinRxLev.Value = queryFunc.curFilterRxlev;
            numNearestCellCount.Value = queryFunc.nearestCellCount;
            numDisFactor.Value = (decimal)queryFunc.disFactor;
            numSameFreq.Value = queryFunc.sameFrq;
            numAdjFreq.Value = queryFunc.adjacentFrq;
        }

        public override bool IsValid()
        {
            return true;
        }

        public override void Apply()
        {
            queryFunc.BackgroundStat = chkBackgroundStat.Checked;
            queryFunc.curFilterRxlev = (int)numMinRxLev.Value;
            queryFunc.nearestCellCount = (int)numNearestCellCount.Value;
            queryFunc.disFactor = (float)numDisFactor.Value;
            queryFunc.sameFrq = (int)numSameFreq.Value;
            queryFunc.adjacentFrq = (int)numAdjFreq.Value;
        }

        private ZTCellCoverLapByRegion_GScan queryFunc;
    }
}
