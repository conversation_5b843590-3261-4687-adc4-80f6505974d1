﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.Util;

namespace MasterCom.ES.UI
{
    public partial class FindResultForm : BaseFormStyle
    {
        ESProcGraphForm esGraphForm = null;
        public FindResultForm(ESProcGraphForm esGraphForm)
        {
            InitializeComponent();
            this.esGraphForm = esGraphForm;
        }
        public void FillFindResult(List<FindResultItem> findResultList)
        {
            lstViewResult.Items.Clear();
            int idx= 1;
            foreach(FindResultItem fri in findResultList)
            {
                ListViewItem lvi = new ListViewItem();
                lvi.Text = "" + idx++;
                lvi.Tag = fri;
                lvi.SubItems.Add(fri.node.ExpString);
                lvi.SubItems.Add(fri.node.DecideExp);
                lvi.SubItems.Add(fri.routine.Name);
                lvi.SubItems.Add(fri.group.Name);
                lstViewResult.Items.Add(lvi);
            }
        }

        private void lstViewResult_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            if(lstViewResult.SelectedItems.Count>0)
            {
                FindResultItem result = lstViewResult.SelectedItems[0].Tag as FindResultItem;
                esGraphForm.FireGoToShowFind(result);
            }
        }
    }
}