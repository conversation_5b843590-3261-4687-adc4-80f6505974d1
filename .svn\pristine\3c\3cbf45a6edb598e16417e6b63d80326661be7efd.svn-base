﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class LastRoadReportSelectDlg : BaseForm
    {
        public LastRoadReportSelectDlg(LastRoadReport selRpt)
        {
            InitializeComponent();
            fillCbx(selRpt);
        }

        private void fillCbx(LastRoadReport selRpt)
        {
            cbxReports.SelectedIndexChanged -= cbxReports_SelectedIndexChanged;
            cbxReports.Items.Clear();
            foreach (LastRoadReport rpt in LastRoadReportManager.GetInstance().Reports)
            {
                cbxReports.Items.Add(rpt);
            }
            cbxReports.SelectedIndexChanged += cbxReports_SelectedIndexChanged;
            if (cbxReports.Items.Count > 0)
            {
                if (selRpt!=null)
                {
                    cbxReports.SelectedItem = selRpt;
                }
                else
                {
                    cbxReports.SelectedIndex = 0;
                }
            }
        }

        void cbxReports_SelectedIndexChanged(object sender, EventArgs e)
        {
            LastRoadReport rpt = cbxReports.SelectedItem as LastRoadReport;
            if (rpt==null)
            {
                return;
            }
            rTxtDesc.Text = rpt.ConditionDescription;
        }

        private void btnEdit_Click(object sender, EventArgs e)
        {
            LastRoadReportCustomForm frm = new LastRoadReportCustomForm(this.SelectedReport);
            frm.ShowDialog();
            fillCbx(frm.SelectedReport);
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            if (cbxReports.SelectedItem==null)
            {
                MessageBox.Show("请选择一个报表！");
                return;
            }
            this.DialogResult = DialogResult.OK;
        }

        public LastRoadReport SelectedReport
        {
            get { return cbxReports.SelectedItem as LastRoadReport; }
        }

    }
}
