﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTDIYCellAbnormalLongAndLatSettingDlg : Form
    {
        public ZTDIYCellAbnormalLongAndLatSettingDlg()
        {
            InitializeComponent();
        }

        public int RxlevMin
        {
            get { return (int)spEdtRxlev.Value; }
        }

        public int DistanceMin
        {
            get { return (int)spEdtDistance.Value; }
        }

        private void simpleBtnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void simpleBtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }
    }
}
