﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public class ChkMgrsGridQuery : ShowFuncForm
    {
        public ChkMgrsGridQuery(MainModel mainModel) : base(mainModel)
        {
        }

        public override string Name
        {
            get { return "栅格GIS对比"; }
        }

        protected override void showForm()
        {
            ChkMgrsGridForm form = MainModel.GetObjectFromBlackboard(typeof(ChkMgrsGridForm).FullName) as ChkMgrsGridForm;
            if (form == null || form.IsDisposed)
            {
                form = new ChkMgrsGridForm(MainModel);
            }
            if (!form.Visible)
            {
                form.Show(MainModel.MainForm);
            }
        }
    }
}
