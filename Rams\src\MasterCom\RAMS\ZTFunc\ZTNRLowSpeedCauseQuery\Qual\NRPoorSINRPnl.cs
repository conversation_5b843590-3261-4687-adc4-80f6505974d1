﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NRPoorSINRPnl : UserControl
    {
        public NRPoorSINRPnl()
        {
            InitializeComponent();
        }

        NRPoorSINRCause mainReason = null;
        NRPoorQualMod3Interf mod3 = null;
        NRPoorQualWeakCover wc = null;
        public void LinkCondition(NRPoorSINRCause reason)
        {
            this.mainReason = reason;
            foreach (NRLowSpeedCauseBase item in reason.SubCauses)
            {
                if (item is NRPoorQualMod3Interf)
                {
                    mod3 = item as NRPoorQualMod3Interf;
                }
                else if (item is NRPoorQualWeakCover)
                {
                    wc = item as NRPoorQualWeakCover;
                }
            }

            numSINRMax.Value = (decimal)mainReason.SINRMax;
            numSINRMax.ValueChanged += numSINRMax_ValueChanged;

            numRSRPDiff.Value = (decimal)mod3.RSRPDiffMax;
            numRSRPDiff.ValueChanged += numRSRPDiff_ValueChanged;

            numWCRSRPMax.Value = (decimal)wc.RSRPMax;
            numWCRSRPMax.ValueChanged += numWCRSRPMax_ValueChanged;
        }

        void numWCRSRPMax_ValueChanged(object sender, EventArgs e)
        {
            wc.RSRPMax = (float)numWCRSRPMax.Value;
        }

        void numRSRPDiff_ValueChanged(object sender, EventArgs e)
        {
            mod3.RSRPDiffMax = (float)numRSRPDiff.Value;
        }

        void numSINRMax_ValueChanged(object sender, EventArgs e)
        {
            mainReason.SINRMax = (float)numSINRMax.Value;
        }
    }
}
