﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Drawing;

using MasterCom.MControls;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using System.Xml;

namespace MasterCom.RAMS.Func
{
    public class ScanGridAnaColorRanger
    {
        private readonly ScanGridAnaCoverageRange coverageRange;
        private readonly ScanGridAnaCompareRange compareRange;
        private readonly ScanGridAnaRxlevRange rxlevRange;
        private readonly ScanGridAnaWeakRxlevRange weakRxlevRange;
        private readonly ScanGridAnaHighCoverageRange highCoverageRange;
        private static ScanGridAnaColorRanger instance;

        private ScanGridAnaColorRanger()
        {
            coverageRange = new ScanGridAnaCoverageRange();
            compareRange = new ScanGridAnaCompareRange();
            rxlevRange = new ScanGridAnaRxlevRange();
            weakRxlevRange = new ScanGridAnaWeakRxlevRange();
            highCoverageRange = new ScanGridAnaHighCoverageRange();
        }

        public static ScanGridAnaColorRanger Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new ScanGridAnaColorRanger();
                }
                return instance;
            }
        }

        public void SaveAllColorRanges(XmlConfigFile xcfg)
        {
            XmlElement cfgColorRange = xcfg.GetConfig("CoverageColorRange");
            if (cfgColorRange != null)
            {
                cfgColorRange.RemoveAll();
                cfgColorRange.SetAttribute("name", "CoverageColorRange");
            }
            else
            {
                cfgColorRange = xcfg.AddConfig("CoverageColorRange");
            }

            foreach (ColorRange range in coverageRange.ColorRanges)
            {
                xcfg.AddItem(cfgColorRange, range.desInfo,
                    string.Format("{0},{1},{2}", range.minValue, range.maxValue, range.color.ToArgb()));
            }

            cfgColorRange = xcfg.GetConfig("CompareColorRange");
            if (cfgColorRange != null)
            {
                cfgColorRange.RemoveAll();
                cfgColorRange.SetAttribute("name", "CompareColorRange");
            }
            else
            {
                cfgColorRange = xcfg.AddConfig("CompareColorRange");
            }

            foreach (ColorRange range in compareRange.ColorRanges)
            {
                xcfg.AddItem(cfgColorRange, range.desInfo,
                    string.Format("{0},{1},{2}", range.minValue, range.maxValue, range.color.ToArgb()));
            }


            cfgColorRange = xcfg.GetConfig("RxlevColorRange");
            if (cfgColorRange != null)
            {
                cfgColorRange.RemoveAll();
                cfgColorRange.SetAttribute("name", "RxlevColorRange");
            }
            else
            {
                cfgColorRange = xcfg.AddConfig("RxlevColorRange");
            }

            foreach (ColorRange range in rxlevRange.ColorRanges)
            {
                xcfg.AddItem(cfgColorRange, range.desInfo,
                    string.Format("{0},{1},{2}", range.minValue, range.maxValue, range.color.ToArgb()));
            }

        }

        public float MinRangeValue
        {
            get
            {
                switch (CurRangeType)
                {
                    case ScanGridAnaRangeType.Coverage:
                        return coverageRange.MinRangeValue;
                    case ScanGridAnaRangeType.Compare:
                        return compareRange.MinRangeValue;
                    case ScanGridAnaRangeType.Rxlev:
                        return rxlevRange.MinRangeValue;
                }
                return 100;
            }
        }

        public float MaxRangeValue
        {
            get
            {
                switch (CurRangeType)
                {
                    case ScanGridAnaRangeType.Coverage:
                        return coverageRange.MaxRangeValue;
                    case ScanGridAnaRangeType.Compare:
                        return compareRange.MaxRangeValue;
                    case ScanGridAnaRangeType.Rxlev:
                        return rxlevRange.MaxRangeValue;
                }
                return 100;
            }
        }

        public ScanGridAnaRangeType CurRangeType
        {
            get;
            set;
        }

        public int CurCoverageType
        {
            get;
            set;
        }

        public Color InvalidateColor
        {
            get
            {
                return coverageRange.InvalidateColor;
            }
            set
            {
                coverageRange.InvalidateColor = value;
            }
        }

        public List<ColorRange> GetColorRanges()
        {
            switch (CurRangeType)
            {
                case ScanGridAnaRangeType.Coverage:
                    return coverageRange.ColorRanges;
                case ScanGridAnaRangeType.Compare:
                    return compareRange.ColorRanges;
                case ScanGridAnaRangeType.Rxlev:
                    return rxlevRange.ColorRanges;
            }
            return new List<ColorRange>();
        }

        public List<ColorRange> GetColorRanges(ScanGridAnaRangeType rangeType)
        {
            switch (rangeType)
            {
                case ScanGridAnaRangeType.Coverage:
                    return coverageRange.ColorRanges;
                case ScanGridAnaRangeType.Compare:
                    return compareRange.ColorRanges;
                case ScanGridAnaRangeType.Rxlev:
                    return rxlevRange.ColorRanges;
            }
            return new List<ColorRange>();
        }

        public void SetColorRanges(List<ColorRange> colorRanges)
        {
            switch (CurRangeType)
            {
                case ScanGridAnaRangeType.Coverage:
                    coverageRange.ColorRanges = colorRanges;
                    break;
                case ScanGridAnaRangeType.Compare:
                    compareRange.ColorRanges = colorRanges;
                    break;
                case ScanGridAnaRangeType.Rxlev:
                    rxlevRange.ColorRanges = colorRanges;
                    break;
            }
        }

        public Color GetColor(ScanGridAnaGridInfo grid)
        {
            switch (CurRangeType)
            {
                case ScanGridAnaRangeType.Coverage:
                    return coverageRange.GetColor(grid, CurCoverageType);
                case ScanGridAnaRangeType.Compare:
                    return compareRange.GetColor(grid);
                case ScanGridAnaRangeType.Rxlev:
                    return rxlevRange.GetColor(grid);
                case ScanGridAnaRangeType.WeakRxlev:
                    return weakRxlevRange.GetColor(grid);
                case ScanGridAnaRangeType.HighCoverage:
                    return highCoverageRange.GetColor(grid);
            }
            return Color.Empty;
        }
    }

    public enum ScanGridAnaRangeType
    {
        Coverage,
        Rxlev,
        Compare,
        WeakRxlev,
        HighCoverage,
    }

    public class ScanGridAnaRangeBase
    {
        public List<ColorRange> ColorRanges { get; set; }

        public ScanGridAnaRangeBase()
        {
            loadConfig();
        }

        protected void loadConfig()
        {
            ColorRanges = new List<ColorRange>();
            XmlConfigFile xcfg = new XmlConfigFile(ScanGridAnaSettingCondition.ConfigPath);
            if (xcfg.Load())
            {
                string name = getConfigName();
                XmlElement configColorRanges = xcfg.GetConfig(name);
                if (configColorRanges != null)
                {
                    addColorRangesByConfig(configColorRanges);
                }
            }
        }

        protected virtual string getConfigName()
        {
            return "";
        }

        protected void addColorRangesByConfig(XmlElement configColorRanges)
        {
            foreach (XmlNode node in configColorRanges.ChildNodes)
            {
                if (node.Attributes.Count == 0)
                {
                    continue;
                }
                string des = node.Attributes["name"] == null ? "" : node.Attributes["name"].InnerText;
                string text = node.InnerText;
                string[] s = text.Split(',');
                float min, max;
                int color;
                if (s.Length == 3 && float.TryParse(s[0], out min) && float.TryParse(s[1], out max)
                    && int.TryParse(s[2], out color))
                {
                    ColorRanges.Add(new ColorRange(min, max, Color.FromArgb(color), des));
                }
            }
        }
    }



    public class ScanGridAnaCoverageRange : ScanGridAnaRangeBase
    {
        public Color InvalidateColor { get; set; }
        public float MaxRangeValue { get; set; } = 50;
        public float MinRangeValue { get; set; } = 0;

        public ScanGridAnaCoverageRange() : base()
        {
            if (ColorRanges.Count == 0)
            {
                ColorRanges.Add(new ColorRange(0, 4, Color.Green, ""));
                ColorRanges.Add(new ColorRange(4, 7, Color.Blue, ""));
                ColorRanges.Add(new ColorRange(7, 10, Color.Yellow, ""));
                ColorRanges.Add(new ColorRange(10, 50, Color.Pink, ""));
            }

            if (ColorRanges.Count > 0)
            {
                ColorRanges.Sort(ScanGridAnaCompareRange.CompareColorRange);
                MaxRangeValue = ColorRanges[ColorRanges.Count - 1].maxValue;
                MinRangeValue = ColorRanges[0].minValue;
            }
            InvalidateColor = Color.Black;
        }

        protected override string getConfigName()
        {
            return "CoverageColorRange";
        }

        public Color GetColor(ScanGridAnaGridInfo grid, int covType)
        {
            if (!grid.IsValidGrid)
            {
                return InvalidateColor;
            }

            double value = 0;
            if (covType == 0)
            {
                value = grid.RelLevel;
            }
            else if (covType == 1)
            {
                value = grid.AbsLevel;
            }
            else
            {
                value = grid.RelAndAbsLevel;
            }

            foreach (ColorRange range in ColorRanges)
            {
                if (value >= range.minValue && value < range.maxValue)
                {
                    return range.color;
                }
            }

            if (ColorRanges.Count > 0)
            {
                return ColorRanges[ColorRanges.Count - 1].color;
            }
            return Color.Empty;
        }
    }

    public class ScanGridAnaCompareRange : ScanGridAnaRangeBase
    {
        public float MaxRangeValue { get; set; } = 3;
        public float MinRangeValue { get; set; } = 1;

        public ScanGridAnaCompareRange() : base()
        {
            if (ColorRanges.Count == 0)
            {
                ColorRanges.Add(new ColorRange(0, 1, Color.Green, "共同出现"));
                ColorRanges.Add(new ColorRange(1, 2, Color.Blue, "当前时间段"));
                ColorRanges.Add(new ColorRange(2, 3, Color.Red, "对比时间段"));
            }

            if (ColorRanges.Count > 0)
            {
                ColorRanges.Sort(CompareColorRange);
                this.MinRangeValue = ColorRanges[0].minValue;
                this.MaxRangeValue = ColorRanges[ColorRanges.Count - 1].maxValue;
            }
        }

        protected override string getConfigName()
        {
            return "CompareColorRange";
        }

        public static int CompareColorRange(ColorRange a, ColorRange b)
        {
            return a.maxValue.CompareTo(b.maxValue);
        }

        public Color GetColor(ScanGridAnaGridInfo grid)
        {
            if (ColorRanges.Count != 3 || grid.Existence < 0 || grid.Existence > 2)
            {
                return Color.Empty;
            }
            return ColorRanges[grid.Existence].color;
        }
    }

    public class ScanGridAnaRxlevRange
    {
        public List<ColorRange> ColorRanges { get; set; }

        public float MaxRangeValue { get; set; }
        public float MinRangeValue { get; set; }

        public ScanGridAnaRxlevRange()
        {
            DTDisplayParameterInfo gsmRxlevParam = DTDisplayParameterManager.GetInstance()["GSM_SCAN", "RxLev"];
            ColorRanges = new List<ColorRange>();

            XmlConfigFile xcfg = new XmlConfigFile(ScanGridAnaSettingCondition.ConfigPath);
            if (xcfg.Load())
            {
                XmlElement configColorRanges = xcfg.GetConfig("RxlevColorRange");
                if (configColorRanges != null)
                {
                    addColorRanges(configColorRanges);
                }
            }

            if (ColorRanges.Count == 0)
            {
                foreach (DTParameterRangeColor range in gsmRxlevParam.RangeColors)
                {
                    ColorRanges.Add(new ColorRange(range.Min, range.Max, range.Value));
                }
            }

            if (ColorRanges.Count > 0)
            {
                ColorRanges.Sort(ScanGridAnaCompareRange.CompareColorRange);
                MinRangeValue = ColorRanges[0].minValue;
                MaxRangeValue = ColorRanges[ColorRanges.Count - 1].maxValue;
            }
        }

        private void addColorRanges(XmlElement configColorRanges)
        {
            foreach (XmlNode node in configColorRanges.ChildNodes)
            {
                if (node.Attributes.Count != 0)
                {
                    string des = node.Attributes["name"] == null ? "" : node.Attributes["name"].InnerText;
                    string text = node.InnerText;
                    string[] s = text.Split(',');
                    float min, max;
                    int color;
                    if (s.Length == 3 && float.TryParse(s[0], out min) && float.TryParse(s[1], out max)
                        && int.TryParse(s[2], out color))
                    {
                        ColorRanges.Add(new ColorRange(min, max, Color.FromArgb(color), des));
                    }
                }
            }
        }

        public Color GetColor(ScanGridAnaGridInfo grid)
        {
            foreach (ColorRange range in ColorRanges)
            {
                if (grid.MaxRxlev >= range.minValue && grid.MaxRxlev < range.maxValue)
                {
                    return range.color;
                }
            }
            return Color.Empty;
        }
    }

    public class ScanGridAnaWeakRxlevRange
    {
        public Color GetColor(ScanGridAnaGridInfo grid)
        {
            return Color.Red;
        }
    }

    public class ScanGridAnaHighCoverageRange
    {
        public Color GetColor(ScanGridAnaGridInfo grid)
        {
            return Color.Blue;
        }
    }
}
