﻿using DevExpress.Utils.Design;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using System.Xml;

namespace MasterCom.RAMS.ZTFunc
{
    public class FreqBandManager
    {
        private static FreqBandManager instance = null;
        public static FreqBandManager Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new FreqBandManager();
                }
                return instance;
            }
        }

        private FreqBandManager()
        {
            //LoadConfig();
        }

        public readonly string ConfigPath = Application.StartupPath + @"\config\FreqBandSetting.xml";
        public Dictionary<FreqBandAnaClassType, FreqBandList> FreqBandAnaDic { get; set; }
            = new Dictionary<FreqBandAnaClassType, FreqBandList>();

        public void LoadConfig()
        {
            XmlConfigFile configFile = new MyXmlConfigFile(ConfigPath);
            bool isLoad = false;
            try
            {
                if (configFile.Load())
                {
                    Dictionary<string, object> newDic = configFile.GetItemValue("BaseSetting", "BaseSettingConfig") as Dictionary<string, object>;
                    if (newDic != null)
                    {
                        Param = newDic;
                        isLoad = true;
                    }
                }
            }
            catch
            {
                //continue
            }

            if (!isLoad)
            {
                Param.Clear();
                initDefaultConfig();
                SaveConfig();

                //if (configFile.Load())
                //{
                //    Dictionary<string, object> newDic = configFile.GetItemValue("BaseSetting", "BaseSettingConfig") as Dictionary<string, object>;
                //    if (newDic != null)
                //    {
                //        Param = newDic;
                //    }
                //}
            }
        }

        private void initDefaultConfig()
        {
            Dictionary<string, object> param = new Dictionary<string, object>();
            Dictionary<string, object> rangeParams = new Dictionary<string, object>();
            param["FreqBandAnaDic"] = rangeParams;

            FreqBandList freqBands = new FreqBandList
            {
                Type = FreqBandAnaClassType.BaseType
            };
            FreqBandInfo freqBand = new FreqBandInfo
            {
                FreqBandName = "D",
                ParentFreqBandName = "",
                RangeList = new List<FreqInfo>()
                {
                    new FreqInfo 
                    {
                        FreqBandName = "D",
                        Type = 1,
                        MinRangeValue = 37750,
                        MaxRangeValue = 38249
                    },
                    new FreqInfo
                    {
                        FreqBandName = "D",
                        Type = 1,
                        MinRangeValue = 39650,
                        MaxRangeValue = 41589
                    },
                }
            };
            freqBands.FreqBandInfoDic.Add(freqBand.FreqBandName, freqBand);

            rangeParams.Add(FreqBandAnaClassType.BaseType.ToString(), freqBands.Param);
            Param = param;
        }

        public void AddDefaultConfig(FreqBandAnaClassType type, FreqBandList freqBands)
        {
            if (!FreqBandAnaDic.ContainsKey(type))
            {
                FreqBandAnaDic.Add(type, freqBands);
            }
        }

        public void SaveConfig()
        {
            XmlConfigFile xcfg = new XmlConfigFile();
            XmlElement cfg = xcfg.AddConfig("BaseSetting");
            xcfg.AddItem(cfg, "BaseSettingConfig", this.Param);
            xcfg.Save(ConfigPath);
        }

        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                Dictionary<string, object> rangeParams = new Dictionary<string, object>();
                param["FreqBandAnaDic"] = rangeParams;
                foreach (var band in FreqBandAnaDic)
                {
                    rangeParams[band.Key.ToString()] = band.Value.Param;
                }
                return param;
            }
            set
            {
                Dictionary<string, object> rangeParams = (Dictionary<string, object>)value["FreqBandAnaDic"];
                foreach (var o in rangeParams)
                {
                    FreqBandAnaClassType type = (FreqBandAnaClassType)Enum.Parse(typeof(FreqBandAnaClassType), o.Key);
                    FreqBandList rangeParam = new FreqBandList();
                    rangeParam.Param = o.Value as Dictionary<string, object>;
                    FreqBandAnaDic[type] = rangeParam;
                }
            }
        }
    }

    public enum FreqBandAnaClassType
    {
        [EnumDescriptionAttribute("基础LTE频段类型")]
        BaseType
    }

    public class FreqBandList
    {
        //public bool AllowRangeIntersect { get; set; }
        public FreqBandAnaClassType Type { get; set; } = FreqBandAnaClassType.BaseType;

        public Dictionary<string, FreqBandInfo> FreqBandInfoDic { get; set; } = new Dictionary<string, FreqBandInfo>();
        public List<FreqBandInfo> FreqBandInfoList { get; set; }

        public bool JudgeExsistFreqBand(string freqBandName)
        {
            if (FreqBandInfoDic.ContainsKey(freqBandName))
            {
                return true;
            }
            return false;
        }

        public bool JudgeExsistFreqBand(string freqBandName, string curFreqBandName)
        {
            if (freqBandName == curFreqBandName)
            {
                return false;
            }
            return JudgeExsistFreqBand(freqBandName);
        }

        public string AddFreqBand(string freqBandName, string parentFreqBandName, FreqInfo freqInfo)
        {
            FreqBandInfo freqBandInfo = new FreqBandInfo()
            {
                FreqBandName = freqBandName,
                ParentFreqBandName = parentFreqBandName
            };
            string errMsg = freqBandInfo.AddFreqBand(freqInfo, FreqBandInfoDic);
            if (string.IsNullOrEmpty(errMsg))
            {
                FreqBandInfoDic.Add(freqBandName, freqBandInfo);
                FreqBandInfoList.Add(freqBandInfo);
            }
            return errMsg;
        }

        public string AddFreq(string freqBandName, FreqInfo freqInfo)
        {
            string errMsg = "";
            FreqBandInfo freqBandInfo;
            if (FreqBandInfoDic.TryGetValue(freqBandName, out freqBandInfo))
            {
                errMsg = freqBandInfo.AddFreqBand(freqInfo, FreqBandInfoDic);
            }
            return errMsg;
        }

        public void EditFreqBand(string oldFreqBandName, string freqBandName, string parentFreqBandName)
        {
            FreqBandInfo freqBandInfo;
            if (FreqBandInfoDic.TryGetValue(oldFreqBandName, out freqBandInfo))
            {
                freqBandInfo.ParentFreqBandName = parentFreqBandName;
                freqBandInfo.FreqBandName = freqBandName;
                foreach (var item in freqBandInfo.RangeList)
                {
                    item.FreqBandName = freqBandName;
                }
                FreqBandInfoDic.Remove(oldFreqBandName);
                FreqBandInfoDic.Add(freqBandName, freqBandInfo);
            }
        }

        //public void EditFreq(string freqBandName, FreqInfo freqInfo)
        //{
        //    FreqBandInfo freqBandInfo;
        //    if (FreqBandInfoDic.TryGetValue(freqBandName, out freqBandInfo))
        //    {
        //        freqBandInfo
        //    }
        //}

        public void RemoveFreqBand(string freqBandName)
        {
            FreqBandInfo freqBandInfo;
            if (FreqBandInfoDic.TryGetValue(freqBandName, out freqBandInfo))
            {
                FreqBandInfoDic.Remove(freqBandName);
                FreqBandInfoList.Remove(freqBandInfo);
            }
        }

        public void RemoveFreq(string freqBandName, FreqInfo freqInfo)
        {
            FreqBandInfo freqBandInfo;
            if (FreqBandInfoDic.TryGetValue(freqBandName, out freqBandInfo))
            {
                freqBandInfo.RemoveFreq(freqInfo);
            }
            if (freqBandInfo.RangeList.Count == 0)
            {
                FreqBandInfoDic.Remove(freqBandName);
                FreqBandInfoList.Remove(freqBandInfo);
            }
        }

        private void calculate()
        {
            FreqBandInfoList = new List<FreqBandInfo>(FreqBandInfoDic.Values);
        }

        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                //param["AllowRangeIntersect"] = AllowRangeIntersect;
                param["Type"] = Type.ToString();
                Dictionary<string, object> rangeParams = new Dictionary<string, object>();
                param["FreqBandInfoDic"] = rangeParams;
                foreach (var band in FreqBandInfoDic)
                {
                    rangeParams.Add(band.Key, band.Value.Param);
                }
                return param;
            }
            set
            {
                //AllowRangeIntersect = (bool)value["AllowRangeIntersect"];
                Type = (FreqBandAnaClassType)Enum.Parse(typeof(FreqBandAnaClassType), value["Type"].ToString());
                Dictionary<string, object> rangeParams = (Dictionary<string, object>)value["FreqBandInfoDic"];
                foreach (var o in rangeParams)
                {
                    FreqBandInfo rangeParam = new FreqBandInfo();
                    rangeParam.Param = o.Value as Dictionary<string, object>;
                    FreqBandInfoDic.Add(o.Key, rangeParam);
                }
                calculate();
            }
        }
    }

    public class FreqBandInfo
    {
        public bool Enable { get; set; } = true;
        public string FreqBandName { get; set; }
        public string ParentFreqBandName { get; set; }
        public List<FreqInfo> RangeList { get; set; } = new List<FreqInfo>();
        public string FreqBandDesc { get; set; }

        public override string ToString()
        {
            return FreqBandName;
        }

        public string JudgeRangeIntersect(FreqInfo freqInfo, Dictionary<string, FreqBandInfo> freqBandInfoDic)
        {
            foreach (var freq in RangeList)
            {
                bool isIntersect = freq.JudgeRangeIntersect(freqInfo);
                if (isIntersect)
                {
                    return $"与频点[{freq.FreqDesc}]的范围冲突";
                }
            }

            foreach (var freqBand in freqBandInfoDic.Values)
            {
                if (freqBand.FreqBandName == ParentFreqBandName || freqBand.ParentFreqBandName == FreqBandName)
                {
                    continue;
                }

                foreach (var freq in freqBand.RangeList)
                {
                    bool isIntersect = freq.JudgeRangeIntersect(freqInfo);
                    if (isIntersect)
                    {
                        return $"与频段[{freqBand.FreqBandName}]频点[{freq.FreqDesc}]的范围冲突";
                    }
                }
            }
            return "";
        }

        public string JudgeRangeIntersect(string oldFreqDesc, FreqInfo freqInfo, Dictionary<string, FreqBandInfo> freqBandInfoDic)
        {
            foreach (var freqBand in freqBandInfoDic.Values)
            {
                if (freqBand.FreqBandName == ParentFreqBandName || freqBand.ParentFreqBandName == FreqBandName)
                {
                    continue;
                }

                foreach (var freq in freqBand.RangeList)
                {
                    if (oldFreqDesc == freq.FreqDesc)
                    {
                        continue;
                    }

                    bool isIntersect = freq.JudgeRangeIntersect(freqInfo);
                    if (isIntersect)
                    {
                        return $"与频段[{freqBand.FreqBandName}]频点[{freq.FreqDesc}]的范围冲突";
                    }
                }
            }
            return "";
        }

        public string AddFreqBand(FreqInfo freqInfo, Dictionary<string, FreqBandInfo> freqBandInfoDic)
        {
            string intersectMsg = JudgeRangeIntersect(freqInfo, freqBandInfoDic);
            if (string.IsNullOrEmpty(intersectMsg))
            {
                RangeList.Add(freqInfo);
                freqInfo.Calculate();
                Calculate();
            }
            return intersectMsg;
        }

        public void EditFreq(FreqInfo freqInfo, FreqInfo oldFreqInfo)
        {
            RangeList.Remove(oldFreqInfo);
            RangeList.Add(freqInfo);
            Calculate();
        }

        public void RemoveFreq(FreqInfo freqInfo)
        {
            RangeList.Remove(freqInfo);
            Calculate();
        }

        public void Calculate()
        {
            StringBuilder sb = new StringBuilder();
            foreach (var range in RangeList)
            {
                sb.Append(range.FreqDesc + ";");
            }
            FreqBandDesc = sb.ToString();
        }

        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["Enable"] = Enable;
                param["FreqBandName"] = FreqBandName;
                param["ParentFreqBandName"] = ParentFreqBandName;
                List<object> freqParams = new List<object>();
                param["FreqInfoList"] = freqParams;
                foreach (FreqInfo freq in RangeList)
                {
                    freqParams.Add(freq.Param);
                }
                return param;
            }
            set
            {
                Enable = (bool)value["Enable"];
                FreqBandName = value["FreqBandName"].ToString();
                ParentFreqBandName = value["ParentFreqBandName"]?.ToString();
                List<object> freqParams = (List<object>)value["FreqInfoList"];
                foreach (object o in freqParams)
                {
                    Dictionary<string, object> freqParam = (Dictionary<string, object>)o;
                    FreqInfo freq = new FreqInfo();
                    freq.Param = freqParam;
                    RangeList.Add(freq);
                }
                Calculate();
            }
        }
    }

    public class FreqInfo
    {
        public bool Enable { get; set; } = true;
        public string FreqBandName { get; set; }
        /// <summary>
        /// 类型 0标识值,1标识范围
        /// </summary>
        public int Type { get; set; }
        /// <summary>
        /// 值
        /// </summary>
        public int Value { get; set; }
        /// <summary>
        /// 范围
        /// </summary>
        public int MaxRangeValue { get; set; }
        public int MinRangeValue { get; set; }

        public string FreqDesc { get; set; }

        public override string ToString()
        {
            return FreqBandName;
        }

        public bool JudgeInRange(int arfcn)
        {
            if (Type == 0 && Value == arfcn)
            {
                return true;
            }
            else if (Type == 1 && arfcn >= MinRangeValue && arfcn <= MaxRangeValue)
            {
                return true;
            }
            return false;
        }

        /// <summary>
        /// 判断频点范围是否相交,添加或修改频点时调用
        /// </summary>
        /// <param name="info"></param>
        /// <returns></returns>
        public bool JudgeRangeIntersect(FreqInfo info)
        {
            if (Type == 0 && info.Type == 0
                && Value != info.Value)
            {
                return false;
            }
            else if (Type == 0 && info.Type == 1
                && (info.MaxRangeValue < Value || info.MinRangeValue > Value))
            {
                return false;
            }
            else if (Type == 1 && info.Type == 0
                && (MaxRangeValue < info.Value || MinRangeValue > info.Value))
            {
                return false;
            }
            else if (Type == 1 && info.Type == 1
                && (MaxRangeValue < info.MinRangeValue || info.MaxRangeValue < MinRangeValue))
            {
                return false;
            }
            return true;
        }

        public void Calculate()
        {
            if (Type == 0)
            {
                FreqDesc = $"[x={Value}]";
            }
            else
            {
                FreqDesc = $"[{MinRangeValue}<=x<={MaxRangeValue}]";
            }
        }

        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["Enable"] = Enable;
                param["FreqBandName"] = FreqBandName;
                param["Type"] = Type;
                param["Value"] = Value;
                param["MaxRangeValue"] = MaxRangeValue;
                param["MinRangeValue"] = MinRangeValue;
                return param;
            }
            set
            {
                Enable = (bool)value["Enable"];
                FreqBandName = value["FreqBandName"].ToString();
                Type = (int)value["Type"];
                Value = (int)value["Value"];
                MaxRangeValue = (int)value["MaxRangeValue"];
                MinRangeValue = (int)value["MinRangeValue"];
                Calculate();
            }
        }
    }
}
