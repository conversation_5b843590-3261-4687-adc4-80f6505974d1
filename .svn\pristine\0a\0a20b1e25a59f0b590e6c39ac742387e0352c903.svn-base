﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using System.Drawing;
using System.Runtime.Serialization;
using System.Windows.Forms;
using MasterCom.RAMS.Net;
using System.Drawing.Drawing2D;
using MasterCom.RAMS.Grid;
using System.Drawing.Imaging;
using Adrian.PhotoX.Lib;
using MasterCom.Util;
using System.Xml;
using System.Threading;
using MasterCom.RAMS.Stat.Data;
using MasterCom.MControls;
using MasterCom.RAMS.Func.GridQueryHistogram;
using MasterCom.RAMS.AnaZT;
using MasterCom.MTGis;

namespace MasterCom.RAMS.Func
{
    [Serializable()]
    public class MapFormAnaZTShowLayer : CustomDrawLayer
    {
        private static readonly log4net.ILog log = log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        public MapFormAnaZTShowLayer(MapOperation mp, string name)
            : base(mp, name)
        {
            penHasLost = new Pen(Color.FromArgb(255,0,255), 3);
            penNormal = new Pen(Color.FromArgb(0, 255, 0), 1);

        }

        private Pen penHasLost;
        private Pen penNormal;

        private IdleCellsInGrid curSelGridUnit = null;
        public override void Draw(Rectangle clientRect, Rectangle updateRect, Graphics graphics)
        {
            try
            {
                IdleCellsInGrid[,] matrix = mainModel.IdleNeibScanLostMatrix;
                if(matrix!=null)
                {
                    drawGridMatrix(graphics, matrix);

                    drawCurSelectedGrid(graphics);
                }
            }
            catch (Exception e)
            {
                //MessageBox.Show("出错:" + e.Message);
                log.Error("Draw Idle Scan Error" + e.Message);
            }
        }

        private void drawGridMatrix(Graphics graphics, IdleCellsInGrid[,] matrix)
        {
            int rowCount = matrix.GetLength(0);
            int colCount = matrix.GetLength(1);
            for (int r = 0; r < rowCount; r++)
            {
                for (int c = 0; c < colCount; c++)
                {
                    IdleCellsInGrid cu = matrix[r, c];
                    if (cu != null)
                    {
                        DbPoint ltPointDSel = new DbPoint(cu.ltLong, cu.ltLat);
                        PointF ltPointSel;
                        Map.ToDisplay(ltPointDSel, out ltPointSel);
                        DbPoint brPointDSel = new DbPoint(cu.brLong, cu.brLat);
                        PointF brPointSel;
                        Map.ToDisplay(brPointDSel, out brPointSel);
                        if (cu.lostScannedCellsDic.Count > 0)
                        {
                            graphics.DrawRectangle(penHasLost, ltPointSel.X, ltPointSel.Y, brPointSel.X - ltPointSel.X, brPointSel.Y - ltPointSel.Y);
                        }
                        else if (cu.idleCellsDic.Count > 0)
                        {
                            graphics.DrawRectangle(penNormal, ltPointSel.X, ltPointSel.Y, brPointSel.X - ltPointSel.X, brPointSel.Y - ltPointSel.Y);
                        }
                        //graphics.FillRectangle(Brushes.Yellow, ltPointSel.X, ltPointSel.Y, brPointSel.X - ltPointSel.X, brPointSel.Y - ltPointSel.Y);
                    }
                }
            }
        }

        private void drawCurSelectedGrid(Graphics graphics)
        {
            //绘制当前所选格
            if (curSelGridUnit != null)
            {
                Font font = new Font("宋体", 10);
                DbPoint ltPointDSel = new DbPoint(curSelGridUnit.ltLong, curSelGridUnit.ltLat);
                PointF ltPointSel;
                Map.ToDisplay(ltPointDSel, out ltPointSel);
                DbPoint brPointDSel = new DbPoint(curSelGridUnit.brLong, curSelGridUnit.brLat);
                PointF brPointSel;
                Map.ToDisplay(brPointDSel, out brPointSel);
                float gridXCenter = 0.5f * (ltPointSel.X + brPointSel.X);
                float gridYCenter = 0.5f * (ltPointSel.Y + brPointSel.Y);
                graphics.DrawRectangle(Pens.Red, ltPointSel.X, ltPointSel.Y, brPointSel.X - ltPointSel.X, brPointSel.Y - ltPointSel.Y);
                drawIdleCells(graphics, font, gridXCenter, gridYCenter);
                drawLostScannedCells(graphics, font, gridXCenter, gridYCenter);
            }
        }

        private void drawIdleCells(Graphics graphics, Font font, float gridXCenter, float gridYCenter)
        {
            //Idle包含的
            foreach (string cellname in curSelGridUnit.idleCellsDic.Keys)
            {
                Cell cell = CellManager.GetInstance().GetCellByName(cellname);
                if (cell != null)
                {
                    DbPoint cellPos = new DbPoint(cell.EndPointLongitude, cell.EndPointLatitude);
                    PointF cellPointF;
                    Map.ToDisplay(cellPos, out cellPointF);
                    graphics.DrawLine(Pens.Blue, cellPointF.X, cellPointF.Y, gridXCenter, gridYCenter);
                    graphics.DrawString(cellname, font, Brushes.Blue, cellPointF);
                    CellRemData cellRemData = curSelGridUnit.idleCellsDic[cellname];
                    float lineCenterX = 0.5f * (cellPointF.X + gridXCenter);
                    float lineCenterY = 0.5f * (cellPointF.Y + gridYCenter);
                    graphics.DrawString(string.Format("{0:F0}({1:F0})", cellRemData.rxlevMean, cellRemData.sampleCount), font, Brushes.Blue, lineCenterX, lineCenterY);

                }
            }
        }

        private void drawLostScannedCells(Graphics graphics, Font font, float gridXCenter, float gridYCenter)
        {
            //扫频发现缺失的
            foreach (string cellname in curSelGridUnit.lostScannedCellsDic.Keys)
            {
                Cell cell = CellManager.GetInstance().GetCellByName(cellname);
                if (cell != null)
                {
                    DbPoint cellPos = new DbPoint(cell.EndPointLongitude, cell.EndPointLatitude);
                    PointF cellPointF;
                    Map.ToDisplay(cellPos, out cellPointF);
                    graphics.DrawLine(Pens.Red, cellPointF.X, cellPointF.Y, gridXCenter, gridYCenter);
                    graphics.DrawString(cellname, font, Brushes.Red, cellPointF);
                    CellRemData cellRemData = curSelGridUnit.lostScannedCellsDic[cellname];
                    float lineCenterX = 0.5f * (cellPointF.X + gridXCenter);
                    float lineCenterY = 0.5f * (cellPointF.Y + gridYCenter);
                    graphics.DrawString(string.Format("{0:F0}({1:F0})", cellRemData.rxlevMean, cellRemData.sampleCount), font, Brushes.Red, lineCenterX, lineCenterY);
                }
            }
        }

        internal int Select(MapOperation2 mop2)
        {
            IdleCellsInGrid cu = null;
            Select(mop2, ref cu);
            return cu == null ? 0 : 1;
        }
        int gridSizeFactor = 1;
        public void Select(MapOperation2 mop2, ref IdleCellsInGrid cu)
        {
            curSelGridUnit = null;
            if (IsVisible)
            {

                IdleCellsInGrid[,] matrix = mainModel.IdleNeibScanLostMatrix;
                if (matrix == null)
                {
                    cu = null;
                    return;
                }
                cu = null;
                DbPoint clickCenter = mop2.GetRegion().Bounds.Center();
                int rowCount = matrix.GetLength(0);
                int colCount = matrix.GetLength(1);
                if (rowCount > 0 && colCount > 0)
                {
                    double ltLong = matrix[0, 0].ltLong;
                    double ltLat = matrix[0, 0].ltLat;
                    double brLong = matrix[rowCount - 1, colCount - 1].brLong;
                    double brLat = matrix[rowCount - 1, colCount - 1].brLat;
                    if (clickCenter.x > ltLong && clickCenter.x < brLong && clickCenter.y > brLat && clickCenter.y < ltLat)
                    {
                        int colAt = (int)((clickCenter.x - ltLong) / (IdleLeakNeighboursQuery.GRID_SPAN_LONG * gridSizeFactor));
                        int rowAt = (int)((ltLat - clickCenter.y) / (IdleLeakNeighboursQuery.GRID_SPAN_LAT * gridSizeFactor));
                        if (rowAt >= 0 && rowAt < rowCount && colAt >= 0 && colAt < colCount)
                        {
                            cu = matrix[rowAt, colAt];
                            curSelGridUnit = cu;
                        }
                    }
                }

            }
        }
    }
}
