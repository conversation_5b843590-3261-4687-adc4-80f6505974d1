﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class LteComplaintSetForm : BaseDialog
    {
        public LteComplaintSetForm()
        {
            InitializeComponent();
            initCondition();
            refreshCond();
        }
        string strExcelPath = "";

        private void initCondition()
        {
            cbOperat.SelectedIndex = 1;
            CondList = new List<LteComplaintCond>();
            CondList.Add(new LteComplaintCond(CondList.Count + 1, 0, 15, 1, 0, 50, 1));
            CondList.Add(new LteComplaintCond(CondList.Count + 1, 15, 30, 0.8, 50, 150, 0.8));
            CondList.Add(new LteComplaintCond(CondList.Count + 1, 30, 45, 0.6, 150, 250, 0.6));
            CondList.Add(new LteComplaintCond(CondList.Count + 1, 45, 60, 0.4, 250, 400, 0.4));
            CondList.Add(new LteComplaintCond(CondList.Count + 1, 60, 90, 0.2, 400, 500, 0.2));
            CondList.Add(new LteComplaintCond(CondList.Count + 1, 90, 360, 0.0, 500, 3000, 0.0));
        }

        private void refreshCond()
        {
            BindingSource source = new BindingSource();
            source.DataSource = CondList;
            gcList.DataSource = source;
            gcList.RefreshDataSource();
        }

        private void gcList_Click(object sender, EventArgs e)
        {
            int[] rows = gridView1.GetSelectedRows();
            if (rows.Length == 0)
                return;
            object o = gridView1.GetRow(rows[0]);
            LteComplaintCond cod = o as LteComplaintCond;

            setValue(cod);
        }

        private void setValue(LteComplaintCond cod)
        {
            numAngMin.Value = (decimal)cod.IAngleMin;
            numAngMax.Value = (decimal)cod.IAngleMax;
            numAngWh.Value = (decimal)cod.DAngleWeight;

            numDisMin.Value = (decimal)cod.IDistanceMin;
            numDisMax.Value = (decimal)cod.IDistanceMax;
            numDisWh.Value = (decimal)cod.DDistanceWeight;
        }

        private void btnUpdate_Click(object sender, EventArgs e)
        {
            int[] rows = gridView1.GetSelectedRows();
            if (rows.Length == 0)
                return;
            object o = gridView1.GetRow(rows[0]);
            LteComplaintCond cod = o as LteComplaintCond;

            LteComplaintCond codUpdate = new LteComplaintCond(cod.ISN, (int)numAngMin.Value
                , (int)numAngMax.Value, (double)numAngWh.Value, (int)numDisMin.Value, (int)numDisMax.Value, (double)numDisWh.Value);
            applySet(cod, codUpdate);
        }

        private void applySet(LteComplaintCond cod, LteComplaintCond codUpdate)
        {
            switch (cbOperat.SelectedItem.ToString().Trim())
            {
                case "修改":
                    {
                        foreach (LteComplaintCond codTmp in CondList)
                        {
                            if (codTmp.ISN == cod.ISN)
                            {
                                codTmp.IAngleMin = codUpdate.IAngleMin;
                                codTmp.IAngleMax = codUpdate.IAngleMax;
                                codTmp.DAngleWeight = codUpdate.DAngleWeight;
                                codTmp.IDistanceMin = codUpdate.IDistanceMax;
                                codTmp.IDistanceMax = codUpdate.IDistanceMax;
                                codTmp.DDistanceWeight = codUpdate.DDistanceWeight;
                            }
                        }
                    }
                    break;
                case "增加":
                    {
                        codUpdate.ISN = CondList.Count + 1;
                        CondList.Add(codUpdate);
                    }
                    break;
                case "删除":
                    {
                        CondList.Remove(cod);
                    }
                    break;
            }
            refreshCond();
        }

        private void btnImportFile_Click(object sender, EventArgs e)
        {
            lbPath.Text = strExcelPath = "";
            OpenFileDialog dlg = new OpenFileDialog();
            dlg.Filter = FilterHelper.Excel;
            dlg.FilterIndex = 2;
            dlg.Multiselect = false;
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return;
            }
            lbPath.Text = strExcelPath = dlg.FileName;
        }

        public string StrExcelPath
        {
            get
            {
                return strExcelPath;
            }
        }

        public List<LteComplaintCond> CondList { get; set; }

        private void btnOK_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
        }
    }
}
