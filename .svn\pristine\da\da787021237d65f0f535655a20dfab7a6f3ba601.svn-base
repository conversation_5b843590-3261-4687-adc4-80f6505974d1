﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Runtime.Serialization;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using MapWinGIS;
using MasterCom.MTGis;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.ZTFunc;
using MasterCom.Util;

namespace MasterCom.RAMS.Func
{
    [Serializable()]
    public class MapFormCellEmulateCovLayer : CustomDrawLayer
    {
        private static readonly log4net.ILog log = log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        int queryid { get; set; }
        public MapFormCellEmulateCovLayer(MapOperation mp, string name,int queryid)
            : base(mp, name)
        {
            this.queryid=queryid;
        }

        public bool NeedFreshFullImg { get; set; } = false;//是否数据发生了变化需要更新整个栅格
        public override void SetObjectData(SerializationInfo info, StreamingContext context)
        {
            base.SetObjectData(info, context);
            if (MainModel == null || MainModel.RxlevGridLongLat.Rxlev70GllList.Count == 0 && MainModel.RxlevGridLongLat.Rxlev80GllList.Count == 0
                && MainModel.RxlevGridLongLat.Rxlev85GllList.Count == 0 && MainModel.RxlevGridLongLat.Rxlev90GllList.Count == 0
                && MainModel.RxlevGridLongLat.Rxlev94GllList.Count == 0 && MainModel.RxlevGridLongLat.Rxlev95GllList.Count == 0
                && MainModel.RxlevGridLongLat.RxlevNonCoverGllList.Count == 0)
            {
                NeedFreshFullImg = false;
                MainModel.cellEmulateShowItem.mapCellEmulateNeedFreshImg = false;
            }
            else
            {
                NeedFreshFullImg = true;
            }
        }

        public override void Draw(Rectangle clientRect, Rectangle updateRect, Graphics graphics)
        {
            try
            {
                RxlevGridLongLat rxlevGridLongLat = MainModel.RxlevGridLongLat;
                if (rxlevGridLongLat.Rxlev70GllList.Count + rxlevGridLongLat.Rxlev80GllList.Count + rxlevGridLongLat.Rxlev85GllList.Count + rxlevGridLongLat.Rxlev90GllList.Count
                    + rxlevGridLongLat.Rxlev94GllList.Count + rxlevGridLongLat.Rxlev95GllList.Count + rxlevGridLongLat.RxlevNonCoverGllList.Count > 10000)
                    useWaitBox = true;
                else
                    useWaitBox = false;

                if (useWaitBox && NeedFreshFullImg)
                    WaitBox.Show(doPrepareColorMatrixThread);
                else
                    doPrepareColorMatrixThread();

                doDrawCoverImg(updateRect, graphics);
               
            }
            catch
            {
                //continue
            }
        }


        private bool useWaitBox = false;
        private DbRect bounds;
        private void doPrepareColorMatrixThread()
        {
            try
            {
                if (!NeedFreshFullImg && !mainModel.cellEmulateShowItem.mapCellEmulateNeedFreshImg)
                {
                    return;
                }

                bounds = new DbRect();
                bool first = true;

                if (useWaitBox)
                {
                    WaitBox.Text = "正在判断数据边界...";
                }
                //数据范围
                RxlevGridLongLat rxlevGridLongLat = MainModel.RxlevGridLongLat;

                setBounds(ref first, rxlevGridLongLat.Rxlev70GllList);
                setBounds(ref first, rxlevGridLongLat.Rxlev80GllList);
                setBounds(ref first, rxlevGridLongLat.Rxlev85GllList);
                setBounds(ref first, rxlevGridLongLat.Rxlev90GllList);
                setBounds(ref first, rxlevGridLongLat.Rxlev94GllList);
                setBounds(ref first, rxlevGridLongLat.Rxlev95GllList);
                setBounds(ref first, rxlevGridLongLat.RxlevNonCoverGllList);

                yGapPixel = xGapPixel = 1;

                if (useWaitBox)
                {
                    WaitBox.Text = "正在生成栅格渲染结构...";
                }

                prepareImage();

                NeedFreshFullImg = false;
                MainModel.cellEmulateShowItem.mapCellEmulateNeedFreshImg = false;
            }
            catch
            {
                NeedFreshFullImg = false;
                MainModel.cellEmulateShowItem.mapCellEmulateNeedFreshImg = false;
            }
            finally
            {
                if (useWaitBox)
                {
                    Thread.Sleep(2000);
                    WaitBox.Close();
                }
            }
        }

        private void setBounds(ref bool first, List<GridLongLat> rxlevGllList)
        {
            foreach (GridLongLat g in rxlevGllList)
            {
                if (first)
                {
                    bounds.x1 = g.fltlongitude;
                    bounds.x2 = g.fbrlongitude;
                    bounds.y1 = g.fbrlatitude;
                    bounds.y2 = g.fltlatitude;
                    first = false;
                }
                else
                {
                    setGridBounds(g);
                }
            }
        }

        private void setGridBounds(GridLongLat g)
        {
            if (bounds.x1 > g.fltlongitude)
            {
                bounds.x1 = g.fltlongitude;
            }
            if (bounds.x2 < g.fbrlongitude)
            {
                bounds.x2 = g.fbrlongitude;
            }
            if (bounds.y1 > g.fbrlatitude)
            {
                bounds.y1 = g.fbrlatitude;
            }
            if (bounds.y2 < g.fltlatitude)
            {
                bounds.y2 = g.fltlatitude;
            }
        }

        private int xGapPixel = 3;
        private int yGapPixel = 3;
        public double GRID_SPAN_LONG { get; set; } = CD.ATOM_SPAN_LONG * 2 * 3;//当前运算中使用的值
        public double GRID_SPAN_LAT { get; set; } = CD.ATOM_SPAN_LAT * 2 * 3;//当前运算中使用的值

        #region 生成图像
        Bitmap img = null;
        public void prepareImage()
        {
            try
            {
                RxlevGridLongLat rxlevGridLongLat = MainModel.RxlevGridLongLat;
                GRID_SPAN_LONG = mainModel.cellEmulateShowItem.gridSpanLon;
                GRID_SPAN_LAT = mainModel.cellEmulateShowItem.gridSpanLat;
                int columnCount = (int)(bounds.Width() / GRID_SPAN_LONG + 0.5);
                int rowCount = (int)(bounds.Height() / GRID_SPAN_LAT + 0.5);
                if (rowCount < 1 || columnCount < 1)
                {
                    return;
                }
                if (img != null)
                {
                    img.Dispose();
                }

                img = new Bitmap(columnCount * xGapPixel, yGapPixel * rowCount, System.Drawing.Imaging.PixelFormat.Format32bppArgb);
                Graphics graphics = Graphics.FromImage(img);

                drawGridUnit(rxlevGridLongLat.Rxlev70GllList, graphics, mainModel.cellEmulateShowItem.rxlev70Color);

                drawTopNGridUnit(rxlevGridLongLat.Rxlev80GllList, graphics, 1, mainModel.cellEmulateShowItem.rxlev80Color);
                drawTopNGridUnit(rxlevGridLongLat.Rxlev85GllList, graphics, 2, mainModel.cellEmulateShowItem.rxlev85Color);
                drawTopNGridUnit(rxlevGridLongLat.Rxlev90GllList, graphics, 3, mainModel.cellEmulateShowItem.rxlev90Color);
                drawTopNGridUnit(rxlevGridLongLat.Rxlev94GllList, graphics, 4, mainModel.cellEmulateShowItem.rxlev94Color);
                drawTopNGridUnit(rxlevGridLongLat.Rxlev95GllList, graphics, 5, mainModel.cellEmulateShowItem.rxlev95Color);

                drawGridUnit(rxlevGridLongLat.RxlevNonCoverGllList, graphics, mainModel.cellEmulateShowItem.rxlevNonCovColor);

                graphics.Save();
                graphics.Dispose();
            }
            catch (Exception e)
            {
                log.Error("Prepare Image Error :" + e.Message);
            }
        }

        private void drawTopNGridUnit(List<GridLongLat> rxlevGllList, Graphics graphics, int topN, Color rxlevColor)
        {
            if (mainModel.cellEmulateShowItem.visibleRxlevTopN > topN)
            {
                drawGridUnit(rxlevGllList, graphics, rxlevColor);
            }
        }

        private void drawGridUnit(List<GridLongLat> rxlevGllList, Graphics graphics, Color rxlevColor)
        {
            foreach (GridLongLat ll in rxlevGllList)
            {
                int rAt, cAt;
                getInPlace(ll, bounds, out rAt, out cAt);
                drawGridColorUnitToImg(rxlevColor, graphics, cAt * xGapPixel, rAt * yGapPixel, xGapPixel, yGapPixel);
            }
        }

        #endregion


        /// <summary>
        /// 获取所在矩阵中的位置 【rAt，cAt】 
        /// </summary>
        /// <param name="gll"></param>
        /// <param name="bounds"></param>
        /// <param name="xAt"></param>
        /// <param name="yAt"></param>
        private void getInPlace(GridLongLat gll, DbRect bounds, out int rAt, out int cAt)
        {
            double xDis = gll.fltlongitude - bounds.x1;
            cAt = (int)(Math.Round(xDis / this.GRID_SPAN_LONG));
            double yDis = bounds.y2 - gll.fltlatitude;
            rAt = (int)(Math.Round(yDis / this.GRID_SPAN_LAT));
        }


        private void drawGridColorUnitToImg(Color gridColor, Graphics graphics, int left, int top, int width, int height)
        {
            Color color = Color.FromArgb(135, gridColor);
            graphics.FillRectangle(new SolidBrush(color), left, top, width, height);
        }

        private DbRect IntSecSrcView(DbRect imgDRect, DbRect viewRect)
        {
            DbRect rect = MapOperation.IntersectRect(imgDRect, viewRect);
            return rect;
        }

        private void doDrawCoverImg(Rectangle updateRect, Graphics graphics)
        {
            if (MainModel.RxlevGridLongLat.Rxlev70GllList.Count == 0 && MainModel.RxlevGridLongLat.Rxlev80GllList.Count == 0
                && MainModel.RxlevGridLongLat.Rxlev85GllList.Count == 0 && MainModel.RxlevGridLongLat.Rxlev90GllList.Count == 0
                && MainModel.RxlevGridLongLat.Rxlev94GllList.Count == 0 && MainModel.RxlevGridLongLat.Rxlev95GllList.Count == 0
                && MainModel.RxlevGridLongLat.RxlevNonCoverGllList.Count == 0)
            {
                return;
            }
            if (img != null)
            {
                int columnCount = (int)(bounds.Width() / GRID_SPAN_LONG + 0.5);
                int rowCount = (int)(bounds.Height() / GRID_SPAN_LAT + 0.5);
                if (rowCount < 1 || columnCount < 1)
                {
                    return;
                }

                DbRect imgDRect = bounds;//实际图像经纬度跨度
                DbRect viewRect;
                Map.FromDisplay(updateRect, out viewRect);

                DbRect vImgRect = IntSecSrcView(imgDRect, viewRect);//需要显示的图像经纬度跨度

                DbPoint ltPointD = new DbPoint(vImgRect.x1, vImgRect.y2);
                PointF ltPoint;
                Map.ToDisplay(ltPointD, out ltPoint);
                DbPoint brPointD = new DbPoint(vImgRect.x2, vImgRect.y1);
                PointF brPoint;
                Map.ToDisplay(brPointD, out brPoint);

                PointF ulCorner = new PointF(ltPoint.X, ltPoint.Y);
                PointF urCorner = new PointF(brPoint.X, ltPoint.Y);
                PointF llCorner = new PointF(ltPoint.X, brPoint.Y);
                PointF[] destPara = { ulCorner, urCorner, llCorner };

                int imgXSize = columnCount * xGapPixel;
                int imgYSize = rowCount * yGapPixel;

                float x0 = (float)((vImgRect.x1 - imgDRect.x1) * imgXSize / imgDRect.Width());
                float y0 = (float)((imgDRect.y2 - vImgRect.y2) * imgYSize / imgDRect.Height());

                float w0 = (float)(vImgRect.Width() * imgXSize / imgDRect.Width());
                float h0 = (float)(vImgRect.Height() * imgYSize / imgDRect.Height());

                // Create rectangle for source image.
                RectangleF srcRect = new RectangleF(x0, y0, w0, h0);
                GraphicsUnit units = GraphicsUnit.Pixel;

                float opqF = (float)(MapFormCompareHisShowLayer.OpaqueValue) / 255;
                float[][] ptsArray ={ 
                        new float[] {1, 0, 0, 0, 0},
                        new float[] {0, 1, 0, 0, 0},
                        new float[] {0, 0, 1, 0, 0},
                    new float[] {0, 0, 0, opqF, 0}, //注意：此处为0.5f，图像为半透明
                    new float[] {0, 0, 0, 0, 1}};
                ColorMatrix clrMatrix = new ColorMatrix(ptsArray);
                ImageAttributes imgAttributes = new ImageAttributes();
                //设置图像的颜色属性
                imgAttributes.SetColorMatrix(clrMatrix, ColorMatrixFlag.Default,
                ColorAdjustType.Bitmap);

                graphics.DrawImage(img, destPara, srcRect, units, imgAttributes);
            }
        }

        internal int MakeShpFile(string filename)
        {
            try
            {
                if (MainModel.RxlevGridLongLat.Rxlev70GllList.Count == 0&&MainModel.RxlevGridLongLat.Rxlev80GllList.Count == 0 
                    && MainModel.RxlevGridLongLat.Rxlev85GllList.Count == 0 && MainModel.RxlevGridLongLat.Rxlev90GllList.Count == 0
                    &&MainModel.RxlevGridLongLat.Rxlev94GllList.Count == 0&& MainModel.RxlevGridLongLat.Rxlev95GllList.Count == 0
                    && MainModel.RxlevGridLongLat.RxlevNonCoverGllList.Count == 0)
                {
                    return 0;
                }

                Shapefile shpFile = new Shapefile();
                int idIdx = 0;     
                int flRxlev = idIdx++;
                int flLtlongitude = idIdx++;
                int flLtlatitude = idIdx++;
                int flBrlongitude = idIdx++;
                int flBrlatitude = idIdx++;
                int flColor = idIdx;
                bool result = shpFile.CreateNewWithShapeID("", MapWinGIS.ShpfileType.SHP_POLYGON);
                if (!result)
                {
                    MessageBox.Show(shpFile.get_ErrorMsg(shpFile.LastErrorCode));
                    return -1;
                }       
                ShapeHelper.InsertNewField(shpFile, "rxlev", FieldType.STRING_FIELD, 7, 20, ref flRxlev);
                ShapeHelper.InsertNewField(shpFile, "ltlongitude", FieldType.DOUBLE_FIELD, 7, 20, ref flLtlongitude);
                ShapeHelper.InsertNewField(shpFile, "ltlatitude", FieldType.DOUBLE_FIELD, 7, 20, ref flLtlatitude);
                ShapeHelper.InsertNewField(shpFile, "brlongitude", FieldType.DOUBLE_FIELD, 7, 20, ref flBrlongitude);
                ShapeHelper.InsertNewField(shpFile, "brlatitude", FieldType.DOUBLE_FIELD, 7, 20, ref flBrlatitude);
                ShapeHelper.InsertNewField(shpFile, "color", FieldType.INTEGER_FIELD, 7, 20, ref flColor);

                int shpIdx = 0;

                WaitBox.Text = "正在导出70覆盖率图层…";
                Color fillColor70 = mainModel.cellEmulateShowItem.rxlev70Color;
                fillGllValue(shpFile, MainModel.RxlevGridLongLat.Rxlev70GllList, fillColor70, ref shpIdx, flRxlev, "70", flColor);

                WaitBox.Text = "正在导出80覆盖率图层…";
                Color fillColor80 = mainModel.cellEmulateShowItem.rxlev80Color;
                fillGllValue(shpFile, MainModel.RxlevGridLongLat.Rxlev80GllList, fillColor80, ref shpIdx, flRxlev, "80", flColor);

                WaitBox.Text = "正在导出85覆盖率图层…";
                Color fillColor85 = mainModel.cellEmulateShowItem.rxlev85Color;
                fillGllValue(shpFile, MainModel.RxlevGridLongLat.Rxlev85GllList, fillColor85, ref shpIdx, flRxlev, "85", flColor);

                WaitBox.Text = "正在导出90覆盖率图层…";
                Color fillColor90 = mainModel.cellEmulateShowItem.rxlev90Color;
                fillGllValue(shpFile, MainModel.RxlevGridLongLat.Rxlev90GllList, fillColor90, ref shpIdx, flRxlev, "90", flColor);

                WaitBox.Text = "正在导出94覆盖率图层…";
                Color fillColor94 = mainModel.cellEmulateShowItem.rxlev94Color;
                fillGllValue(shpFile, MainModel.RxlevGridLongLat.Rxlev94GllList, fillColor94, ref shpIdx, flRxlev, "94", flColor);

                WaitBox.Text = "正在导出95覆盖率图层…";
                Color fillColor95 = mainModel.cellEmulateShowItem.rxlev95Color;
                fillGllValue(shpFile, MainModel.RxlevGridLongLat.Rxlev95GllList, fillColor95, ref shpIdx, flRxlev, "95", flColor);

                WaitBox.Text = "正在导出无覆盖图层…";
                Color fillColorNonCov = mainModel.cellEmulateShowItem.rxlevNonCovColor;
                fillGllValue(shpFile, MainModel.RxlevGridLongLat.RxlevNonCoverGllList, fillColorNonCov, ref shpIdx, flRxlev, "无覆盖", flColor);

                //====Set Color
                Dictionary<uint, int> categoryByColor = new Dictionary<uint, int>();
                //=================区域着色渲染
                for (int i = 0; i < shpFile.NumShapes; i++)
                {
                    object vx = shpFile.get_CellValue(flColor, i);
                    uint color = (uint)(int)vx;
                    if (!categoryByColor.ContainsKey(color))
                    {
                        string name = shpFile.Categories.Count.ToString();
                        MapWinGIS.ShapefileCategory cat = shpFile.Categories.Add(name);
                        if (cat != null)
                        {
                            cat.DrawingOptions.FillColor = color;
                            categoryByColor.Add(color, shpFile.Categories.Count - 1);
                        }
                    }
                }
                //================apply categories
                for (int i = 0; i < shpFile.NumShapes; i++)
                {
                    object vx = shpFile.get_CellValue(flColor, i);
                    uint color = (uint)(int)vx;
                    uint clr = color;
                    int catIndex = categoryByColor[clr];
                    shpFile.set_ShapeCategory(i, catIndex);
                }
                //=======

                shpFile.SaveAs(filename, null);
                shpFile.Close();
                return 1;
            }
            catch (Exception)
            {
                return -1;
            }
        }

        /*
        /// <summary>
        /// 填充各电平栅格数据
        /// </summary>
        private void fillGllValue(Shapefile shapeFile, List<GridLongLat> rxlevGllList,ref int shpIdx,int flRxlev, string covType, int flLtlongitude, int flLtlatitude, int flBrlongitude, int flBrlatitude)
        {
            int iLoop = 1;
            foreach (GridLongLat gll in rxlevGllList)
            {
                WaitBox.ProgressPercent = (int)(100.0 * iLoop++ / rxlevGllList.Count);
                MapWinGIS.Shape shp = new MapWinGIS.Shape();
                shp.Create(ShpfileType.SHP_POLYGON);
                shapeFile.EditInsertShape(shp, ref shpIdx);
                shapeFile.EditCellValue(flRxlev, shpIdx, covType);
                shapeFile.EditCellValue(flLtlongitude, shpIdx, gll.fltlongitude);
                shapeFile.EditCellValue(flLtlatitude, shpIdx, gll.fltlatitude);
                shapeFile.EditCellValue(flBrlongitude, shpIdx, gll.fbrlongitude);
                shapeFile.EditCellValue(flBrlatitude, shpIdx, gll.fbrlatitude);
            }
        }
        */

        private void fillGllValue(Shapefile shapeFile, List<GridLongLat> rxlevGllList, Color fillColor, ref int shpIdx, int flRxlev
            , string covType, int flColor)
        {
            uint oleColor = (uint)ColorTranslator.ToOle(fillColor);
            int iLoop = 1;
            foreach (GridLongLat gll in rxlevGllList)
            {
                WaitBox.ProgressPercent = (int)(100.0 * iLoop++ / rxlevGllList.Count);
                MapWinGIS.Shape shpBase = ShapeHelper.CreateRectShape(gll.fltlongitude, gll.fltlatitude, gll.fbrlongitude, gll.fbrlatitude);

                shapeFile.EditInsertShape(shpBase, ref shpIdx);
                shapeFile.EditCellValue(flRxlev, shpIdx, covType);
                shapeFile.EditCellValue(flColor, shpIdx, (int)oleColor);
            }
        }


        public int MakeTxtFile(string filename)
        {
            try
            {
                if (MainModel.RxlevGridLongLat.Rxlev70GllList.Count == 0 && MainModel.RxlevGridLongLat.Rxlev80GllList.Count == 0
                    && MainModel.RxlevGridLongLat.Rxlev85GllList.Count == 0 && MainModel.RxlevGridLongLat.Rxlev90GllList.Count == 0
                    && MainModel.RxlevGridLongLat.Rxlev94GllList.Count == 0 && MainModel.RxlevGridLongLat.Rxlev95GllList.Count == 0
                    && MainModel.RxlevGridLongLat.RxlevNonCoverGllList.Count == 0)
                {
                    return 0;
                }

                int sn = 1;
                using (StreamWriter writer = new StreamWriter(filename, false, Encoding.Default))
                {  
                    WaitBox.Text = "正在导出70覆盖率数据…";
                    foreach (GridLongLat gll in MainModel.RxlevGridLongLat.Rxlev70GllList)
                    {
                        WaitBox.ProgressPercent = 30;
                        string record = string.Format("{0}  rxlev:{1}  ltlongitude:{2}  ltlatitude:{3}  brlongitude:{4}  brlatitude:{5}", sn++, "70", gll.fltlongitude, gll.fltlatitude, gll.fbrlongitude, gll.fbrlatitude);
                        writer.WriteLine(record);
                    }

                    WaitBox.Text = "正在导出80覆盖率数据…";
                    foreach (GridLongLat gll in MainModel.RxlevGridLongLat.Rxlev80GllList)
                    {
                        WaitBox.ProgressPercent = 50;
                        string record = string.Format("{0}  rxlev:{1}  ltlongitude:{2}  ltlatitude:{3}  brlongitude:{4}  brlatitude:{5}", sn++, "80", gll.fltlongitude, gll.fltlatitude, gll.fbrlongitude, gll.fbrlatitude);
                        writer.WriteLine(record);
                    }

                    WaitBox.Text = "正在导出85覆盖率数据…";
                    foreach (GridLongLat gll in MainModel.RxlevGridLongLat.Rxlev85GllList)
                    {
                        WaitBox.ProgressPercent = 60;
                        string record = string.Format("{0}  rxlev:{1}  ltlongitude:{2}  ltlatitude:{3}  brlongitude:{4}  brlatitude:{5}", sn++, "85", gll.fltlongitude, gll.fltlatitude, gll.fbrlongitude, gll.fbrlatitude);
                        writer.WriteLine(record);
                    }

                    WaitBox.Text = "正在导出90覆盖率数据…";
                    foreach (GridLongLat gll in MainModel.RxlevGridLongLat.Rxlev90GllList)
                    {
                        WaitBox.ProgressPercent = 70;
                        string record = string.Format("{0}  rxlev:{1}  ltlongitude:{2}  ltlatitude:{3}  brlongitude:{4}  brlatitude:{5}", sn++, "90", gll.fltlongitude, gll.fltlatitude, gll.fbrlongitude, gll.fbrlatitude);
                        writer.WriteLine(record);
                    }

                    WaitBox.Text = "正在导出94覆盖率数据…";
                    foreach (GridLongLat gll in MainModel.RxlevGridLongLat.Rxlev94GllList)
                    {
                        WaitBox.ProgressPercent =80;
                        string record = string.Format("{0}  rxlev:{1}  ltlongitude:{2}  ltlatitude:{3}  brlongitude:{4}  brlatitude:{5}", sn++, "94", gll.fltlongitude, gll.fltlatitude, gll.fbrlongitude, gll.fbrlatitude);
                        writer.WriteLine(record);
                    }

                    WaitBox.Text = "正在导出95覆盖率数据…";
                    foreach (GridLongLat gll in MainModel.RxlevGridLongLat.Rxlev95GllList)
                    {
                        WaitBox.ProgressPercent = 90;
                        string record = string.Format("{0}  rxlev:{1}  ltlongitude:{2}  ltlatitude:{3}  brlongitude:{4}  brlatitude:{5}", sn++, "95", gll.fltlongitude, gll.fltlatitude, gll.fbrlongitude, gll.fbrlatitude);
                        writer.WriteLine(record);
                    }

                    WaitBox.Text = "正在导出无覆盖率数据…";
                    foreach (GridLongLat gll in MainModel.RxlevGridLongLat.RxlevNonCoverGllList)
                    {
                        WaitBox.ProgressPercent = 90;
                        string record = string.Format("{0}  rxlev:{1}  ltlongitude:{2}  ltlatitude:{3}  brlongitude:{4}  brlatitude:{5}", sn++, "无覆盖", gll.fltlongitude, gll.fltlatitude, gll.fbrlongitude, gll.fbrlatitude);
                        writer.WriteLine(record);
                    }
                }

                return 1;
            }
            catch(Exception)
            {
                return -1;
            }
        }

        List<string> tempList = new List<string>();//临时存放历史查询条件
        public int MakeCheckBcpFile(string filename)
        {
            try
            {
                using (StreamWriter writer = new StreamWriter(filename, false, Encoding.Default))
                {
                    WaitBox.Text = "正在导出历史查询条件...";
                    WaitBox.ProgressPercent = 30;

                    recordFormat();
                    string record = string.Format("{0}^^{1}^^{2}^^{3}^^{4}^^{5}^^{6}^^{7}^^{8}^^{9}^^{10}^^{11}^^",
                                                  tempList[0], tempList[1], tempList[2], tempList[3], tempList[4], tempList[5], tempList[6],
                                                  tempList[7], tempList[8], tempList[9], tempList[10], tempList[11]);
                    writer.WriteLine(record);
                    tempList.Clear();
                    return 1;
                }
            }
            catch (Exception)
            {
                return -1;
            }
        }

        private void recordFormat()
        {
            CfgSettingDlg dlg = CfgSettingDlg.GetInstance();

            int queryidFinal = 0;
            string strRelatedMap = "";//该字段用于记录地图结果表的表名，由于地图结果的数据比较大，每份bcp对应一张表
            if (ZTGSMCellEmulateCoverAnaByMR.queryidGSM > ZTTDCellEmulateCoverAnaByMR.queryidTD)
            {
                queryidFinal = ZTGSMCellEmulateCoverAnaByMR.queryidGSM;
                strRelatedMap = "tb_mr_cover_history_data_map_" + queryidFinal.ToString();
            }
            else
            {
                queryidFinal = ZTTDCellEmulateCoverAnaByMR.queryidTD;
                strRelatedMap = "tb_tdmr_cover_history_data_map_" + queryidFinal.ToString();
            }
            StringBuilder sb = new StringBuilder();//用于记录预存区域的名称，不选择预存区域的，将不保留其历史查询条件
            List<string> strRegionList = getStrRegionList();
            foreach (string rName in strRegionList)
            {
                sb.Append(sb + rName + ",");
            }
            string strRegion = sb.ToString();
            strRegion = strRegion.Substring(0, strRegion.Length - 1);
            strRegionList.Clear();
            string recordName = "";//用于记录历史查询条件的名称，方便用户下次点击该名称时能还原对应的操作
            recordName = dlg.BeginTime.ToString() + "_" + dlg.EndTime.ToString() + "_" + strRegion;
            int igridSize = 0;
            switch (dlg.gridSizeLon)
            {
                case 1000: igridSize = 0; break;
                case 2000: igridSize = 1; break;
                case 4000: igridSize = 2; break;
                case 8000: igridSize = 3; break;
                default: break;
            }

            string bitIsTaDownWard = "";
            if (dlg.IsTaDownWard)
            {
                bitIsTaDownWard = "1";
            }
            else
            {
                bitIsTaDownWard = "0";
            }

            string bitIsAbisMR = "";
            if (dlg.IsAddAbisMr)
            {
                bitIsAbisMR = "1";
            }
            else
            {
                bitIsAbisMR = "0";
            }

            string strTestValue = getStrTestValue(dlg);

            string strRepeaterDeal = getStrRepeaterDeal(dlg);

            tempList.Add(queryidFinal.ToString());
            tempList.Add(recordName);
            tempList.Add(dlg.BeginTime.ToString());
            tempList.Add(dlg.EndTime.ToString());
            tempList.Add(igridSize.ToString());
            tempList.Add(bitIsTaDownWard);
            tempList.Add(dlg.Rxlev.ToString());
            tempList.Add(bitIsAbisMR);
            tempList.Add(strTestValue);
            tempList.Add(strRepeaterDeal);
            tempList.Add(strRegion);
            tempList.Add(strRelatedMap);
        }

        private static List<string> getStrRegionList()
        {
            List<string> strRegionList = new List<string>();
            List<ResvRegion> resvRegions = MainModel.GetInstance().SearchGeometrys.SelectedResvRegions;//预存区域
            if (resvRegions == null)
            {
                MessageBox.Show("无法导出非预存区域！");
            }
            else
            {
                foreach (ResvRegion resvRegion in resvRegions)
                {
                    if (!strRegionList.Contains(resvRegion.RegionName))
                    {
                        strRegionList.Add(resvRegion.RegionName);
                    }
                }
                strRegionList.Sort();
            }

            return strRegionList;
        }

        private static string getStrTestValue(CfgSettingDlg dlg)
        {
            string strTestValue = "";
            switch (dlg.TestValue)
            {
                case 1: strTestValue = "最大"; break;
                case 2: strTestValue = "平均"; break;
                case 3: strTestValue = "最小"; break;
                default: break;
            }

            return strTestValue;
        }

        private string getStrRepeaterDeal(CfgSettingDlg dlg)
        {
            string strRepeaterDeal = "";
            switch (dlg.RepeaterBts)
            {
                case 1: strRepeaterDeal = "仅保存正常"; break;
                case 2: strRepeaterDeal = "不过滤直放站"; break;
                case 3: strRepeaterDeal = "截断空值TA"; break;
                default: break;
            }

            return strRepeaterDeal;
        }

        public int MakeMapBcpFile(string filename)
        {
            int queryidFinal = 0;
            if (ZTGSMCellEmulateCoverAnaByMR.queryidGSM > ZTTDCellEmulateCoverAnaByMR.queryidTD)
            {
                queryidFinal = ZTGSMCellEmulateCoverAnaByMR.queryidGSM;
            }
            else
            {
                queryidFinal = ZTTDCellEmulateCoverAnaByMR.queryidTD;
            }
            try
            {
                if (MainModel.RxlevGridLongLat.Rxlev70GllList.Count == 0 && MainModel.RxlevGridLongLat.Rxlev80GllList.Count == 0
                    && MainModel.RxlevGridLongLat.Rxlev85GllList.Count == 0 && MainModel.RxlevGridLongLat.Rxlev90GllList.Count == 0
                    && MainModel.RxlevGridLongLat.Rxlev94GllList.Count == 0 && MainModel.RxlevGridLongLat.Rxlev95GllList.Count == 0
                    && MainModel.RxlevGridLongLat.RxlevNonCoverGllList.Count == 0)
                {
                    return 0;
                }

                using (StreamWriter writer = new StreamWriter(filename, false, Encoding.Default))
                {
                    WaitBox.Text = "正在导出70覆盖率数据…";
                    bool isValid = true;
                    writeValidBcpFile70(queryidFinal, writer, isValid, "70", 30, MainModel.RxlevGridLongLat.Rxlev70GllList);

                    WaitBox.Text = "正在导出80覆盖率数据…";
                    isValid = MainModel.RxlevGridLongLat.Rxlev70GllList.Count == 0;
                    writeValidBcpFile(queryidFinal, writer, isValid, "80", 40, MainModel.RxlevGridLongLat.Rxlev80GllList);

                    WaitBox.Text = "正在导出85覆盖率数据…";
                    isValid = isValid && MainModel.RxlevGridLongLat.Rxlev80GllList.Count == 0;
                    writeValidBcpFile(queryidFinal, writer, isValid, "85", 50, MainModel.RxlevGridLongLat.Rxlev85GllList);

                    WaitBox.Text = "正在导出90覆盖率数据…";
                    isValid = isValid && MainModel.RxlevGridLongLat.Rxlev85GllList.Count == 0;
                    writeValidBcpFile(queryidFinal, writer, isValid, "90", 60, MainModel.RxlevGridLongLat.Rxlev90GllList);

                    WaitBox.Text = "正在导出94覆盖率数据…";
                    isValid = isValid && MainModel.RxlevGridLongLat.Rxlev90GllList.Count == 0;
                    writeValidBcpFile(queryidFinal, writer, isValid, "94", 70, MainModel.RxlevGridLongLat.Rxlev94GllList);

                    WaitBox.Text = "正在导出95覆盖率数据…";
                    isValid = isValid && MainModel.RxlevGridLongLat.Rxlev94GllList.Count == 0;
                    writeValidBcpFile(queryidFinal, writer, isValid, "95", 80, MainModel.RxlevGridLongLat.Rxlev95GllList);

                    WaitBox.Text = "正在导出无覆盖率数据…";
                    isValid = isValid && MainModel.RxlevGridLongLat.Rxlev95GllList.Count == 0;
                    writeValidBcpFile(queryidFinal, writer, isValid, "无覆盖", 90, MainModel.RxlevGridLongLat.RxlevNonCoverGllList);
                }

                return 1;
            }
            catch (Exception)
            {
                return -1;
            }
        }

        private void writeValidBcpFile70(int queryidFinal, StreamWriter writer, bool isValid, string strRxlev,
            int progressPercent, List<GridLongLat> rxlevGllList)
        {
            if (isValid && rxlevGllList.Count > 0)
            {
                string firstRecord = string.Format("{0}^^{1}^^{2}^^", queryidFinal.ToString() + "_" + strRxlev, rxlevGllList[0].fltlongitude, rxlevGllList[0].fltlatitude);
                writer.WriteLine(firstRecord);
                writeBcpFile(writer, strRxlev, progressPercent, rxlevGllList, 1);
            }
        }

        private void writeValidBcpFile(int queryidFinal, StreamWriter writer, bool isValid, string strRxlev, 
            int progressPercent, List<GridLongLat> rxlevGllList)
        {
            if (isValid && rxlevGllList.Count > 0)
            {
                string firstRecord = string.Format("{0}^^{1}^^{2}^^", queryidFinal.ToString() + "_" + strRxlev, rxlevGllList[0].fltlongitude, rxlevGllList[0].fltlatitude);
                writer.WriteLine(firstRecord);
                writeBcpFile(writer, strRxlev, progressPercent, rxlevGllList, 1);
            }
            else
            {
                writeBcpFile(writer, strRxlev, progressPercent, rxlevGllList, 0);
            }
        }

        private void writeBcpFile(StreamWriter writer, string strRxlev, int progressPercent, List<GridLongLat> rxlevGllList, int start)
        {
            for (int i = start; i < rxlevGllList.Count; i++)
            {
                WaitBox.ProgressPercent = progressPercent;
                string record = string.Format("{0}^^{1}^^{2}^^", strRxlev, rxlevGllList[i].fltlongitude, rxlevGllList[i].fltlatitude);
                writer.WriteLine(record);
            }
        }

        public int MakeResultBcpFile(string filename,List<CellEmulateCovResult> retList)
        {
            int queryidFinal = 0;
            if (ZTGSMCellEmulateCoverAnaByMR.queryidGSM > ZTTDCellEmulateCoverAnaByMR.queryidTD)
            {
                queryidFinal = ZTGSMCellEmulateCoverAnaByMR.queryidGSM;
            }
            else
            {
                queryidFinal = ZTTDCellEmulateCoverAnaByMR.queryidTD;
            }
            try
            {
                using (StreamWriter writer = new StreamWriter(filename, false, Encoding.Default))
                {
                    WaitBox.Text = "正在导出MR仿真覆盖结果…";
                    int round = 1;
                    foreach (CellEmulateCovResult ret in retList)
                    {
                        WaitBox.ProgressPercent = (100 / retList.Count) * round;
                        string record = string.Format("{0}^^{1}^^{2}^^{3}^^{4}^^{5}^^{6}^^{7}^^{8}^^{9}^^{10}^^{11}^^{12}^^{13}^^{14}^^{15}^^{16}^^{17}^^{18}^^{19}^^{20}^^{21}^^{22}^^{23}^^{24}^^",
                                                    queryidFinal, ret.region, ret.totalGrid, ret.totalSquare, ret.coverGrid, ret.coverSquare, ret.coverRate,
                                                    ret.coverGrid70, ret.coverGrid80, ret.coverGrid85, ret.coverGrid90, ret.coverGrid94, ret.coverGrid95,
                                                    ret.coverSquare70, ret.coverSquare80, ret.coverSquare85, ret.coverSquare90, ret.coverSquare94, ret.coverSquare95,
                                                    ret.coverRate70, ret.coverRate80, ret.coverRate85, ret.coverRate90, ret.coverRate94, ret.coverRate95);
                        writer.WriteLine(record);
                        round++;
                    }
                    return 1;
                }
            }
            catch (Exception)
            {
                return -1;
            }
        }
    }
}
