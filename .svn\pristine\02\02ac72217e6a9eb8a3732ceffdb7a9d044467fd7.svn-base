using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Frame;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.Func
{
    public partial class WlanChannelApInfoForm : ChildForm
    {
        public Dictionary<string, WlanApChannelInfo> apChannelDic { get; set; }

        public WlanChannelApInfoForm()
        {
            apChannelDic = new Dictionary<string, WlanApChannelInfo>();
            InitializeComponent();
            initColumn();
        }

        public void initColumn()
        {
            this.columnHeaderChannel_Channel.AspectGetter = delegate (object row)
            {
                if (row is WlanApInfo)
                {
                    WlanApInfo apInfo = row as WlanApInfo;
                    return apInfo.channel;
                }
                else if (row is WlanApChannelInfo)
                {
                    WlanApChannelInfo apChannel = row as WlanApChannelInfo;
                    return apChannel.channel;
                }
                return "";
            };
            addPart1Data();
            addPart2Data();
            this.listViewChannel.CanExpandGetter = delegate (object x)
            {
                return x is WlanApChannelInfo;
            };
            this.listViewChannel.ChildrenGetter = delegate (object x)
            {
                WlanApChannelInfo macInfo = (WlanApChannelInfo)x;
                return macInfo.apInfoList;
            };
        }

        private void addPart1Data()
        {
            this.columnHeaderSSID_Channel.AspectGetter = delegate (object row)
            {
                if (row is WlanApInfo)
                {
                    WlanApInfo apInfo = row as WlanApInfo;
                    return apInfo.ssid;
                }
                return "";
            };
            this.columnHeaderBSSID_Channel.AspectGetter = delegate (object row)
            {
                if (row is WlanApInfo)
                {
                    WlanApInfo apInfo = row as WlanApInfo;
                    return apInfo.bssid;
                }
                return "";
            };
            this.columnHeaderFreq_Channel.AspectGetter = delegate (object row)
            {
                if (row is WlanApInfo)
                {
                    WlanApInfo apInfo = row as WlanApInfo;
                    return apInfo.freqecncy;
                }
                return "";
            };
            this.columnHeaderRSSI_Channel.AspectGetter = delegate (object row)
            {
                if (row is WlanApInfo)
                {
                    WlanApInfo apInfo = row as WlanApInfo;
                    return apInfo.rssi;
                }
                return "";
            };
            this.columnHeaderCI_Channel.AspectGetter = delegate (object row)
            {
                if (row is WlanApInfo)
                {
                    WlanApInfo apInfo = row as WlanApInfo;
                    return apInfo.ci;
                }
                return "";
            };
            this.columnHeaderSFI_Channel.AspectGetter = delegate (object row)
            {
                if (row is WlanApInfo)
                {
                    WlanApInfo apInfo = row as WlanApInfo;
                    return apInfo.sfi;
                }
                return "";
            };
            this.columnHeaderNFI_Channel.AspectGetter = delegate (object row)
            {
                if (row is WlanApInfo)
                {
                    WlanApInfo apInfo = row as WlanApInfo;
                    return apInfo.nfi;
                }
                return "";
            };
            this.columnHeaderFirstSeen_Channel.AspectGetter = delegate (object row)
            {
                if (row is WlanApInfo)
                {
                    WlanApInfo apInfo = row as WlanApInfo;
                    return apInfo.firstSeen;
                }
                return "";
            };
            this.columnHeaderLastSeen_Channel.AspectGetter = delegate (object row)
            {
                if (row is WlanApInfo)
                {
                    WlanApInfo apInfo = row as WlanApInfo;
                    return apInfo.lastSeen;
                }
                return "";
            };
            this.columnHeaderActiveTimes_Channel.AspectGetter = delegate (object row)
            {
                if (row is WlanApInfo)
                {
                    WlanApInfo apInfo = row as WlanApInfo;
                    return apInfo.activeTime;
                }
                return "";
            };
            this.columnHeaderRx_Channel.AspectGetter = delegate (object row)
            {
                if (row is WlanApInfo)
                {
                    WlanApInfo apInfo = row as WlanApInfo;
                    return apInfo.rx;
                }
                return "";
            };
            this.columnHeaderTx_Channel.AspectGetter = delegate (object row)
            {
                if (row is WlanApInfo)
                {
                    WlanApInfo apInfo = row as WlanApInfo;
                    return apInfo.tx;
                }
                return "";
            };
            this.columnHeaderBand_Channel.AspectGetter = delegate (object row)
            {
                if (row is WlanApInfo)
                {
                    WlanApInfo apInfo = row as WlanApInfo;
                    return apInfo.band;
                }
                return "";
            };
            this.columnHeaderABGN_Channel.AspectGetter = delegate (object row)
            {
                if (row is WlanApInfo)
                {
                    WlanApInfo apInfo = row as WlanApInfo;
                    return apInfo.abgn;
                }
                return "";
            };
        }

        private void addPart2Data()
        {
            this.columnHeaderType_Channel.AspectGetter = delegate (object row)
            {
                if (row is WlanApInfo)
                {
                    WlanApInfo apInfo = row as WlanApInfo;
                    return apInfo.type;
                }
                return "";
            };
            this.columnHeaderEncrytion_Channel.AspectGetter = delegate (object row)
            {
                if (row is WlanApInfo)
                {
                    WlanApInfo apInfo = row as WlanApInfo;
                    return apInfo.encrytion;
                }
                return "";
            };
            this.columnHeaderSecurity_Channel.AspectGetter = delegate (object row)
            {
                if (row is WlanApInfo)
                {
                    WlanApInfo apInfo = row as WlanApInfo;
                    return apInfo.security;
                }
                return "";
            };
            this.columnHeaderBridge_Channel.AspectGetter = delegate (object row)
            {
                if (row is WlanApInfo)
                {
                    WlanApInfo apInfo = row as WlanApInfo;
                    return apInfo.bridge;
                }
                return "";
            };
            this.columnHeaderDCFPCF_Channel.AspectGetter = delegate (object row)
            {
                if (row is WlanApInfo)
                {
                    WlanApInfo apInfo = row as WlanApInfo;
                    return apInfo.dcf_pcf;
                }
                return "";
            };
            this.columnHeaderBeaconInterval_Channel.AspectGetter = delegate (object row)
            {
                if (row is WlanApInfo)
                {
                    WlanApInfo apInfo = row as WlanApInfo;
                    return apInfo.beaconInterval;
                }
                return "";
            };
            this.columnHeaderSupportRate_Channel.AspectGetter = delegate (object row)
            {
                if (row is WlanApInfo)
                {
                    WlanApInfo apInfo = row as WlanApInfo;
                    return apInfo.supportRate;
                }
                return "";
            };
            this.columnHeaderMaxRate_Channel.AspectGetter = delegate (object row)
            {
                if (row is WlanApInfo)
                {
                    WlanApInfo apInfo = row as WlanApInfo;
                    return apInfo.maxRate;
                }
                return "";
            };
            this.columnHeaderPreamble_Channel.AspectGetter = delegate (object row)
            {
                if (row is WlanApInfo)
                {
                    WlanApInfo apInfo = row as WlanApInfo;
                    return apInfo.preamble;
                }
                return "";
            };
            this.columnHeaderNoise_Channel.AspectGetter = delegate (object row)
            {
                if (row is WlanApInfo)
                {
                    WlanApInfo apInfo = row as WlanApInfo;
                    return apInfo.noise;
                }
                return "";
            };
        }

        public override void Init()
        {
            base.Init();
            //MainModel.DistrictChanged += districtChanged
            //MainModel.DTDataChanged += dtDataChanged
            MainModel.SelectedTestPointsChanged += selectedTestPointsChanged;
            //MainModel.SelectedEventsChanged += selectedEventsChanged
            //MainModel.SelectedMessageChanged += selectedMessageChanged
            Disposed += disposed;
            initializeComponent();
            //dtDataChanged(null, null)
        }

        public override Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                return param;
            }
            set
            {
                Console.Write(value);
            }
        }

        private void initializeComponent()
        {
            //columnCountChanged()
            //rowCountChanged()
            //cellDefineChanged()
        }

        private void disposed(object sender, EventArgs e)
        {
            //MainModel.DistrictChanged -= districtChanged
            MainModel.SelectedTestPointsChanged -= selectedTestPointsChanged;
            //MainModel.SelectedEventsChanged -= selectedEventsChanged
            //MainModel.SelectedMessageChanged -= selectedMessageChanged
        }

        private void selectedTestPointsChanged(object sender, EventArgs e)
        {
            apChannelDic.Clear();
            if (MainModel.SelectedTestPoints.Count > 0
                && MainModel.SelectedTestPoints[0] is WLANTestPoint)
            {
                addApChannelDic();
            }
            List<WlanApChannelInfo> channelInfoList = new List<WlanApChannelInfo>(apChannelDic.Values);
            FillForm(channelInfoList);
        }

        private void addApChannelDic()
        {
            WLANTestPoint testPoint = MainModel.SelectedTestPoints[0] as WLANTestPoint;
            for (int i = 0; i < 50; i++)
            {
                int? channel = (int?)testPoint["WLAN_ARR_AP_Channel", i];
                int? rssi = (int?)testPoint["WLAN_ARR_AP_RSSI", i];
                object bssidObj = testPoint["WLAN_ARR_AP_BSSID_String", i];
                if (channel == null || rssi == null || bssidObj == null)
                {
                    //continue
                }
                else
                {
                    WlanApInfo apInfo = new WlanApInfo();
                    apInfo.FillInfo(testPoint, i);
                    if (!apChannelDic.ContainsKey(channel.ToString()))
                    {
                        apChannelDic[channel.ToString()] = new WlanApChannelInfo();
                        apChannelDic[channel.ToString()].channel = channel.ToString();
                    }
                    apChannelDic[channel.ToString()].apInfoList.Add(apInfo);
                }
            }
        }

        public void FillForm(List<WlanApChannelInfo> channelInfoList)
        {
            listViewChannel.Items.Clear();
            listViewChannel.SetObjects(channelInfoList);
        }

        private void listViewChannel_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            if (listViewChannel.SelectedObject is WlanApInfo)
            {
                WlanApInfo apInfo = listViewChannel.SelectedObject as WlanApInfo;
                MainModel.GetInstance().FireWlanApInfoDetailShow(apInfo);
            }
        }
    }
}