﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.ZTFunc.AreaArchiveManage;
using MasterCom.Util;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class TestWorkStatAna : AreaKpiQueryBase
    {
        public TestWorkStatAna(MainModel mainModel)
            : base(mainModel)
        {
            isQueryEvents = false;
            areaCondition = new WorkTestCondition();
            anaDealer = new WorkTestAnaDealer(areaCondition as WorkTestCondition);
        }

        public override string Name
        {
            get { return "测试工作统计"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 31000, 31011, this.Name);
        }

        protected override bool setConditionDlg()
        {
            TestWorkSettingDlg dlg = new TestWorkSettingDlg();
            dlg.SetCondition((WorkTestCondition)areaCondition);

            if (dlg.ShowDialog() == DialogResult.OK)
            {
                areaCondition = dlg.GetCondition();
                return true;
            }
            return false;
        }

        protected override List<string> getFormulas()
        {
            return new List<string> { "Mx_0801", "Tx_0801", "Wx_0801", "Cx_0801", "Ex_0801" };
        }

        protected override void fireShowForm()
        {
            TestWorkForm form = MainModel.CreateResultForm(typeof(TestWorkForm)) as TestWorkForm;
            form.FillData(anaDealer, areaSummaryMap);
            form.Show(MainModel.MainForm);
        }
    }
}
