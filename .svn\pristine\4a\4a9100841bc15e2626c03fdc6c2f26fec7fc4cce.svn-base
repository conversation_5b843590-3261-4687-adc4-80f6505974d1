﻿using System.Collections.Generic;
using MasterCom.RAMS.Model;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc
{
    public class NbIotMgrsStaterBase
    {
        protected MainModel MainModel;
        protected List<NbIotMgrsResultControlBase> resultControlList = new List<NbIotMgrsResultControlBase>();
        protected List<MTPolygon> selectPolygons = new List<MTPolygon>();

        public NbIotMgrsStaterBase()
        {
            MainModel = MainModel.GetInstance();
        }

        public virtual void DoStat(NbIotMgrsFuncItem curFuncItem)
        {
            init();
        }

        public virtual void SetPolygon(List<MTPolygon> selectPolygons)
        {
            this.selectPolygons = selectPolygons;
        }

        public virtual List<NbIotMgrsResultControlBase> GetResultControl()
        {
            return resultControlList;
        }

        public virtual void SetResultControl()
        {

        }

        public virtual void Clear()
        {

        }

        protected Dictionary<string, string> resultList;
        public Dictionary<string, string> ResultList
        {
            get { return resultList; }
        }


        protected static CarrierAreaResult cmCarrierAreaResult { get; set; }
        protected static CarrierAreaResult cuCarrierAreaResult { get; set; }
        protected static CarrierAreaResult ctCarrierAreaResult { get; set; }

        /// <summary>
        /// 初始化汇聚栅格并统计区域内栅格
        /// </summary>
        protected void init()
        {
            cmCarrierAreaResult = new CarrierAreaResult(selectPolygons);
            cmCarrierAreaResult.SetAreaGrid(NbIotMgrsQueryFuncBase.CMScanGridInfoList, "中国移动");

            cuCarrierAreaResult = new CarrierAreaResult(selectPolygons);
            cuCarrierAreaResult.SetAreaGrid(NbIotMgrsQueryFuncBase.CUScanGridInfoList, "中国联通");

            ctCarrierAreaResult = new CarrierAreaResult(selectPolygons);
            ctCarrierAreaResult.SetAreaGrid(NbIotMgrsQueryFuncBase.CTScanGridInfoList, "中国电信");
        }

        protected double totalGridCount;
        /// <summary>
        /// 区域内总栅格数
        /// </summary>
        public double TotalGridCount
        {
            get { return totalGridCount; }
        }

        protected double serialWeakGridCount;
        /// <summary>
        /// 连续栅格数
        /// </summary>
        public double SerialWeakGridCount
        {
            get { return serialWeakGridCount; }
        }

        /// <summary>
        /// 专题名称
        /// </summary>
        protected string staterName;

        /// <summary>
        /// 添加结果用于统计界面
        /// </summary>
        /// <param name="carrierType"></param>
        protected virtual void addResultData(CarrierType carrierType)
        {
            string weakGridCoverage;
            if (TotalGridCount != 0)
            {
                weakGridCoverage = (SerialWeakGridCount * 100 / TotalGridCount).ToString("f2") + "%";
            }
            else
            {
                weakGridCoverage = "0.00%";
            }
            string key = staterName + carrierType;
            resultList.Add(key, weakGridCoverage);
        }

        /// <summary>
        /// 获取结果
        /// </summary>
        /// <returns></returns>
        public virtual Dictionary<string, string> GetResultData()
        {
            return new Dictionary<string, string>();
        }
    }

    public class CarrierAreaResult
    {
        protected List<MTPolygon> selectPolygons;
        public CarrierAreaResult(List<MTPolygon> selectPolygons)
        {
            this.selectPolygons = selectPolygons;
            carrierResult = new NbIotMgrsResultInfo();
        }

        public NbIotMgrsResultInfo carrierResult { get; set; }

        /// <summary>
        /// 汇聚后的栅格合集
        /// </summary>
        public Dictionary<string, List<ScanGridInfo>> GridList
        {
            get;
            private set;
        }

        /// <summary>
        /// 区域内栅格总数
        /// </summary>
        public int TotalGridCount
        {
            get 
            {
                int res = 0;
                foreach (var item in AreaGridCount.Values)
                {
                    res += item;
                }
                return res;
            }
        }

        /// <summary>
        /// 网格内的栅格数
        /// </summary>
        public Dictionary<string, int> AreaGridCount
        {
            get;
            private set;
        }

        /// <summary>
        /// 网格内的结果集
        /// </summary>
        public Dictionary<string, Dictionary<string, string>> AreaResultList
        {
            get;
            set;
        }

        public void SetAreaGrid(List<ScanGridInfo> scanGridInfoList, string name)
        {
            AreaGridCount = new Dictionary<string, int>();
            GridList = new Dictionary<string, List<ScanGridInfo>>();
            AreaResultList = new Dictionary<string, Dictionary<string, string>>();
            carrierResult.Name = name;

            GridList = ConvergeScanGridInfo(scanGridInfoList);

            foreach (var grid in GridList)
            {
                combineAreaGrid(grid);
            }
        }

        /// <summary>
        /// 按区域汇聚栅格
        /// </summary>
        /// <param name="grid"></param>
        private void combineAreaGrid(KeyValuePair<string, List<ScanGridInfo>> grid)
        {
            foreach (MTPolygon polygon in selectPolygons)
            {
                if (polygon.CheckPointInRegion(grid.Value[0].CentLng, grid.Value[0].CentLat))
                {
                    if (!AreaGridCount.ContainsKey(polygon.Name))
                    {
                        AreaGridCount[polygon.Name] = 1;
                        carrierResult.AreaList.Add(new NbIotMgrsAreaResultInfo(polygon.Name));
                        AreaResultList[polygon.Name] = new Dictionary<string, string>();
                    }
                    else
                    {
                        AreaGridCount[polygon.Name]++;
                    }
                    break;
                }
            }
        }

        protected Dictionary<string, List<ScanGridInfo>> ConvergeScanGridInfo(List<ScanGridInfo> scanGridInfoList)
        {
            Dictionary<string, Dictionary<int, ScanGridInfo>> convergeDic = ScanGridInfo.ConvergeScanGridInfo(scanGridInfoList);
            Dictionary<string, List<ScanGridInfo>> gridList = new Dictionary<string, List<ScanGridInfo>>();
            foreach (var grid in convergeDic)
            {
                List<ScanGridInfo> cellGridList = new List<ScanGridInfo>(grid.Value.Values);
                cellGridList.Sort((x, y) => { return -x.R0_RP.CompareTo(y.R0_RP); });
                gridList.Add(grid.Key, cellGridList);
            }
            return gridList;
        }

        public string getAreaResultData(string areaName, int issuesCount)
        {
            string issuesCoverage = "0.00%";
            foreach (var area in AreaGridCount)
            {
                if (area.Key == areaName)
                {
                    if (area.Value != 0 && issuesCount != 0)
                    {
                        issuesCoverage = (issuesCount * 100d / area.Value).ToString("f2") + "%";
                    }
                    break;
                }
            }
            return issuesCoverage;
        }

        public string getTotalResultData(double issuesCount)
        {
            string issuesCoverage = "0.00%";
            if (TotalGridCount != 0 && issuesCount != 0)
            {
                issuesCoverage = (issuesCount * 100d / TotalGridCount).ToString("f2") + "%";
            }
            return issuesCoverage;
        }
    }
}
