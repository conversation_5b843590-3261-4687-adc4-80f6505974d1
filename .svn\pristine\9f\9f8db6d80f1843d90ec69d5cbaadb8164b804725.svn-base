﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTStationAcceptance_XJ
{
    public class NRIndoorCellInfo : CellInfoBase
    {
        public NRIndoorCellServiceInfo SAInfo { get; private set; }
        public NRIndoorCellServiceInfo NSAInfo { get; private set; }

        public NRIndoorCellInfo(ICell cell)
            : base(cell)
        {

        }

        public void Init(ICell cell, NRServiceName serviceType)
        {
            if (SAInfo == null && serviceType == NRServiceName.SA)
            {
                SAInfo = new NRIndoorCellServiceInfo(cell as NRCell);
            }
            else if (NSAInfo == null && serviceType == NRServiceName.NSA)
            {
                NSAInfo = new NRIndoorCellServiceInfo(cell as NRCell);
            }
        }
    }

    public class NRIndoorCellServiceInfo
    {
        public NRIndoorCellServiceInfo(ICell cell)
        {
            Cell = cell;
        }
        public ICell Cell { get; set; }
        public SuccessRateKpiInfo HandOverInfo { get; set; } = new SuccessRateKpiInfo();
        public SuccessRateKpiInfo AccessInfo { get; set; } = new SuccessRateKpiInfo();
        public SuccessRateKpiInfo GNBAddInfo { get; set; } = new SuccessRateKpiInfo();

        public FtpPointInfo SampleDL { get; set; } = new FtpPointInfo();
        public FtpPointInfo SampleUL { get; set; } = new FtpPointInfo();

        public DataKpiInfo BigPackageDelay { get; set; } = new DataKpiInfo();
        public DataKpiInfo SmallPackageDelay { get; set; } = new DataKpiInfo();

        public DataKpiInfo LeakOutLock { get; set; } = new DataKpiInfo();

        public SuccessRateKpiInfo EPSFBInfo { get; set; } = new SuccessRateKpiInfo();
        public DataKpiInfo EPSFBDelay { get; set; } = new DataKpiInfo();
        public SuccessRateKpiInfo VONRInfo { get; set; } = new SuccessRateKpiInfo();
    }

    public class NRIndoorCellParameters : CellParameters
    {
        #region 小区天线参数验证
        public string CellName { get; set; }

        /// <summary>
        /// 经度
        /// </summary>
        public ParamInfo<double> Longitude { get; set; } = new ParamInfo<double>();
        /// <summary>
        /// 纬度
        /// </summary>
        public ParamInfo<double> Latitude { get; set; } = new ParamInfo<double>();
        public ParamInfo<int> CellID { get; set; } = new ParamInfo<int>();
        public ParamInfo<int> PCI { get; set; } = new ParamInfo<int>();
        /// <summary>
        /// 频段
        /// </summary>
        public ParamInfo<string> FreqBand { get; set; } = new ParamInfo<string>();
        /// <summary>
        /// 频点
        /// </summary>
        public ParamInfo<string> Freq { get; set; } = new ParamInfo<string>();
        /// <summary>
        /// SSB频点
        /// </summary>
        public ParamInfo<string> SSBFreq { get; set; } = new ParamInfo<string>();
        /// <summary>
        /// 小区带宽（兆）
        /// </summary>
        public ParamInfo<string> Bandwidth { get; set; } = new ParamInfo<string>();
        /// <summary>
        /// 根序列(PRACH)
        /// </summary>
        public ParamInfo<string> PRACH { get; set; } = new ParamInfo<string>();
        /// <summary>
        /// 子帧配比
        /// </summary>
        public ParamInfo<string> SubFrameRatio { get; set; } = new ParamInfo<string>();
        /// <summary>
        /// 通道数
        /// </summary>
        public ParamInfo<int> Channels { get; set; } = new ParamInfo<int>();
        #endregion

        public void Caluculate()
        {
            Longitude.JudgeValidLongitude(50);
            Latitude.JudgeValidLatitude(50);
            CellID.JudgeValid();
            PCI.JudgeValid();
            FreqBand.JudgeValid();
            Freq.JudgeValid();
            SSBFreq.JudgeValid();
            Bandwidth.JudgeValid();
            PRACH.JudgeValid();
            SubFrameRatio.JudgeValid();
            Channels.JudgeValid();
        }
    }
}
