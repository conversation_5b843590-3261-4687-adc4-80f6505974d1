﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Net;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Net;
using MasterCom.MTGis;
using MasterCom.RAMS.Func.SystemSetting;
using MapWinGIS;
using MasterCom.RAMS.ZTFunc;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTRailwayLongLat : DIYGridQuery
    {
        public ZTRailwayLongLat(MainModel mainModel)
            : base(mainModel)
        {
            this.mainModel = mainModel;
        }
        public override string Name
        {
            get { return "高铁测试经纬度回填"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 20000, 20044, this.Name);
        }
        public enum ReplayType
        {
            /// <summary>
            /// 默认_原始采样点
            /// </summary>
            Default = 1,
            /// <summary>
            /// 完整采样点，包含回填的采样点
            /// </summary>
            Dealwith
        }
        #region 全局变量
        public Dictionary<int, FileInfo> logFileDic { get; set; } = new Dictionary<int, FileInfo>();
        public Dictionary<int, SampleStatuInfo> sampleStatuDic { get; set; } = new Dictionary<int, SampleStatuInfo>();
        private readonly List<FileInfo> fileList = new List<FileInfo>();
        readonly List<NPOIRow> nrDataList = new List<NPOIRow>();
        #endregion

        private void  clearData()
        {
            logFileDic.Clear();
            fileList.Clear();
            sampleStatuDic.Clear();
            nrDataList.Clear();
        }

        protected override void query()
        {
            clearData();
            WaitBox.CanCancel = true;
            WaitBox.Show("正在查询文件...", getFileInfo);
            fireShowResult(nrDataList);
        }

        /// <summary>
        /// 获取文件列表
        /// </summary>
        private void getFileInfo()
        {
            Dictionary<int, List<int>> AreasDic = new Dictionary<int, List<int>>();
            AreasDic[4] = new List<int>();

            WaitBox.ProgressPercent = 30;
            DIYFileInfoData diyFileInfoData = new DIYFileInfoData(mainModel);
            this.condition.Areas = AreasDic;
            diyFileInfoData.SetQueryCondition(this.Condition);
            diyFileInfoData.Query();
            fileList.AddRange(diyFileInfoData.FlieInfoData);

            List<int> CityConnsList = this.Condition.DistrictIDs;
            int CityIdCur = MainModel.DistrictID;
            WaitBox.ProgressPercent = 70;
            foreach (int CityId in CityConnsList)
            {
                MainModel.DistrictID = CityId;
                WaitBox.Text = "正在查询 " + DistrictManager.GetInstance().getDistrictName(CityId) + " 已回填经纬度信息...";
                DIYQuerySampleStatu query = new DIYQuerySampleStatu(mainModel);
                query.Query();
                foreach(int iFileidTmp in query.sampleStatuDic.Keys)
                {
                    if (!sampleStatuDic.ContainsKey(iFileidTmp))
                        sampleStatuDic.Add(iFileidTmp, query.sampleStatuDic[iFileidTmp]);
                }
            }
            MainModel.DistrictID = CityIdCur;
            if (sampleStatuDic.Count > 0)
            {
                doWithSampleLongLat();
                dealLogFileDataSet();
            }
            WaitBox.Close();
        }

        /// <summary>
        /// 匹配文件信息
        /// </summary>
        private void doWithSampleLongLat()
        {
            foreach (FileInfo fi in fileList)
            {
                if (sampleStatuDic.ContainsKey(fi.ID))
                {
                    if (fi.LogTable != sampleStatuDic[fi.ID].tbfileName)
                        continue;

                    if (!logFileDic.ContainsKey(fi.ID))
                        logFileDic[fi.ID] = new FileInfo();
                    logFileDic[fi.ID] = fi;
                }
            }
        }

        /// <summary>
        /// 显示数据集整合
        /// </summary>
        private void dealLogFileDataSet()
        {
            NPOIRow nrHead = new NPOIRow();
            nrHead.cellValues = addHeadName();
            nrDataList.Add(nrHead);
            int idx = 1;
            foreach (int iKey in logFileDic.Keys)
            {
                NPOIRow nrData = new NPOIRow();
                nrData.cellValues = addRowData(logFileDic[iKey], idx);
                nrDataList.Add(nrData);
                idx++;
            }
        }

        #region 显示数据组织
        /// <summary>
        /// 列头信息
        /// </summary>
        private List<object> addHeadName()
        {
            List<object> objList = new List<object>();
            objList.Add("序号");
            objList.Add("来源地市");
            objList.Add("文件名");
            objList.Add("测试方向");
            objList.Add("开始时间");
            objList.Add("结束时间");
            objList.Add("时长");
            objList.Add("项目类型");
            objList.Add("业务类型");
            objList.Add("区域类型");
            objList.Add("区域信息");
            return objList;
        }

        /// <summary>
        /// 添加数据内容
        /// </summary>
        private List<object> addRowData(FileInfo logfile, int idx)
        {
            List<object> objList = new List<object>();
            objList.Add(idx);
            objList.Add(logfile.ID);
            objList.Add(logfile.DistrictName);
            objList.Add(logfile.Name);
            if (logfile.Name.Contains("正向"))
                objList.Add("正向");
            else if (logfile.Name.Contains("反向"))
                objList.Add("反向");
            else
                objList.Add("");
            objList.Add(logfile.BeginTimeString);
            objList.Add(logfile.EndTimeString);
            objList.Add(logfile.Duration);
            objList.Add(logfile.ProjectDescription);
            objList.Add(logfile.ServiceTypeDescription);
            objList.Add(logfile.AreaString);
            objList.Add(logfile.AreaTypeString);
            return objList;
        }
        #endregion

        protected void fireShowResult(List<NPOIRow> nrDataList)
        {
            object obj = MainModel.GetInstance().GetObjectFromBlackboard(typeof(ZTRailwayLongLatForm).FullName);
            ZTRailwayLongLatForm form = obj == null ? null : obj as ZTRailwayLongLatForm;
            if (form == null || form.IsDisposed)
            {
                form = new ZTRailwayLongLatForm(MainModel);
            }
            form.InitData(nrDataList, logFileDic, this, this.Condition);
            form.fillData();
            if (!form.Visible)
            {
                form.Show(MainModel.MainForm);
            }
        }

        /// <summary>
        /// 回放文件（用于Form调用）
        /// </summary>
        public void repalyerSample(FileInfo fi,ReplayType replayType)
        {
            int CityIdCur = MainModel.DistrictID;
            ReplayFileQuery(fi);
            if (replayType == ReplayType.Dealwith)
            {
                WaitBox.CanCancel = true;
                WaitBox.Show("正在查询回填采样点...", repalyerSampleByFile, fi);
            }
            MainModel.DistrictID = CityIdCur;
            MainModel.FireDTDataChanged(this);
        }
        
        private void ReplayFileQuery(FileInfo fi)
        {
            QueryCondition condition = new QueryCondition();
            condition.QueryType = Condition.QueryType;
            condition.DistrictID = fi.DistrictID;
            condition.FileInfos.Add(fi);
            condition.Geometorys = Condition.Geometorys;

            DIYReplayFileQuery query = new DIYReplayFileQuery(MainModel);
            query.SetIsAddSampleToDTDataManager(true);
            query.SetQueryCondition(condition);
            query.Query();
        }

        /// <summary>
        /// 回放文件下的采样点
        /// </summary>
        private void repalyerSampleByFile(object obj)
        {
            FileInfo fi = obj as FileInfo;

            WaitBox.ProgressPercent = 50;
            WaitBox.Text = "正在查询回填采样点...";

            int? CityIdByFile = DistrictManager.GetInstance().GetDistrictID(fi.DistrictName);
            MainModel.DistrictID = CityIdByFile == null ? 1 : (int)CityIdByFile;
            DIVQuerySampleInfo divQuery = new DIVQuerySampleInfo(mainModel);
            divQuery.setQueryCondition(fi.LogTable,sampleStatuDic[fi.ID].SampleId);
            divQuery.Query();
            Dictionary<int, DIVSampleInfo> sampleInfoDic = divQuery.sampleInfoDic;

            WaitBox.ProgressPercent = 80;
            WaitBox.Text = "正在匹配采样点...";
            if (sampleInfoDic.Count > 0)
                doStat(sampleInfoDic);
          
            WaitBox.Close();
        }
        /// <summary>
        /// 回放文件，并回填经纬度
        /// </summary>
        private void doStat(Dictionary<int, DIVSampleInfo> sampleInfoDic)
        {
            try
            {
                foreach (DTFileDataManager fileDataManager in MainModel.DTDataManager.FileDataManagers)
                {
                    List<TestPoint> testPointList = fileDataManager.TestPoints;
                    for (int idx = 0; idx < testPointList.Count; idx++)
                    {
                        setTPLatitudeLongitude(sampleInfoDic, testPointList, idx);
                    }
                }
            }
            catch
            {
                //continue
            }
        }

        private void setTPLatitudeLongitude(Dictionary<int, DIVSampleInfo> sampleInfoDic, List<TestPoint> testPointList, int idx)
        {
            TestPoint testPoint = testPointList[idx];
            if (sampleInfoDic.ContainsKey(testPoint.Time))
            {
                testPoint.Longitude = sampleInfoDic[testPoint.Time].dLongitude;
                testPoint.Latitude = sampleInfoDic[testPoint.Time].dLatitude;
            }
            else
            {
                for (int i = testPoint.Time - 1; i < testPoint.Time + 5; i++)
                {
                    if (sampleInfoDic.ContainsKey(i))
                    {
                        testPoint.Longitude = sampleInfoDic[i].dLongitude;
                        testPoint.Latitude = sampleInfoDic[i].dLatitude;
                        break;
                    }
                }
            }
        }

        public class SampleStatuInfo
        {
            public int ifileid { get; set; }
            public int iStarSampleId { get; set; }
            public int iEndSampleId { get; set; }
            public int iCount { get; set; }
            public string tbfileName { get; set; }
            public string SampleId { get; set; }
        }

        public class DIVSampleInfo
        {
            public int ilongitude { get; set; }
            public int ilatitude { get; set; }
            public double dLongitude
            {
                get { return Convert.ToDouble(ilongitude) / 10000000; }
            }
            public double dLatitude
            {
                get { return Convert.ToDouble(ilatitude) / 10000000; }
            }
        }

        /// <summary>
        /// 查询 表tb_auto_railway_status
        /// </summary>
        public class DIYQuerySampleStatu : DIYSQLBase
        {
            public Dictionary<int, ZTRailwayLongLat.SampleStatuInfo> sampleStatuDic { get; set; }
            public DIYQuerySampleStatu(MainModel mainModel)
                : base(mainModel)
            { }

            public override string Name
            {
                get
                {
                    return "查询高铁经纬度状态表";
                }
            }

            protected override string getSqlTextString()
            {
                string strSQL = "select [ifileid],[istartsampleid],[iendsampleid],[inum],[tbfilename],SampleId from [tb_auto_railway_status]";
                return strSQL;
            }

            protected override E_VType[] getSqlRetTypeArr()
            {
                int idx = 0;
                E_VType[] rType = new E_VType[6];
                rType[idx++] = E_VType.E_Int;
                rType[idx++] = E_VType.E_Int;
                rType[idx++] = E_VType.E_Int;
                rType[idx++] = E_VType.E_Int;
                rType[idx++] = E_VType.E_String;
                rType[idx] = E_VType.E_String;
                return rType;
            }

            protected override void receiveRetData(ClientProxy clientProxy)
            {
                sampleStatuDic = new Dictionary<int, ZTRailwayLongLat.SampleStatuInfo>();
                Package package = clientProxy.Package;
                while (true)
                {
                    clientProxy.Recieve();
                    package.Content.PrepareGetParam();
                    if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                    {
                        ZTRailwayLongLat.SampleStatuInfo ssi = new ZTRailwayLongLat.SampleStatuInfo();
                        ssi.ifileid = package.Content.GetParamInt();
                        ssi.iStarSampleId = package.Content.GetParamInt();
                        ssi.iEndSampleId = package.Content.GetParamInt();
                        ssi.iCount = package.Content.GetParamInt();
                        ssi.tbfileName = package.Content.GetParamString();
                        ssi.SampleId = package.Content.GetParamString();
                        if (!sampleStatuDic.ContainsKey(ssi.ifileid))
                            sampleStatuDic.Add(ssi.ifileid, ssi);
                    }
                    else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                    {
                        break;
                    }
                    else
                    {
                        log.Error("Unexpected type: " + package.Content.Type);
                        break;
                    }
                }
            }
        }

        /// <summary>
        /// 查询 tb_auto_longlat_railway_20XX_XX 月表
        /// </summary>
        public class DIVQuerySampleInfo : DIYSQLBase
        {
            public Dictionary<int, DIVSampleInfo> sampleInfoDic { get; set; }
            private string tableName = "";
            private string strSampleID = "";
            public DIVQuerySampleInfo(MainModel mainModel)
                : base(mainModel)
            { }

            public override string Name
            {
                get
                {
                    return "查询文件采样点经纬度";
                }
            }

            public void setQueryCondition(string tableName, string strSampleID)
            {
                this.tableName = tableName.Replace("tb_log_file", "tb_auto_longlat_railway");
                this.strSampleID = strSampleID;
            }

            protected override string getSqlTextString()
            {
                string selectSQL = string.Format("SELECT [itime],[ilongitude],[ilatitude] FROM [{0}] where [SampleId]='{1}' order by itime", tableName, strSampleID);
                return selectSQL;
            }

            protected override E_VType[] getSqlRetTypeArr()
            {
                int idx = 0;
                E_VType[] rType = new E_VType[3];
                rType[idx++] = E_VType.E_Int;
                rType[idx++] = E_VType.E_Int;
                rType[idx] = E_VType.E_Int;
                return rType;
            }

            protected override void receiveRetData(ClientProxy clientProxy)
            {
                sampleInfoDic = new Dictionary<int, DIVSampleInfo>();
                Package package = clientProxy.Package;
                while (true)
                {
                    clientProxy.Recieve();
                    package.Content.PrepareGetParam();
                    if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                    {
                        DIVSampleInfo si = new DIVSampleInfo();
                        int iTime = package.Content.GetParamInt();
                        si.ilongitude = package.Content.GetParamInt();
                        si.ilatitude = package.Content.GetParamInt();
                        if (!sampleInfoDic.ContainsKey(iTime))
                            sampleInfoDic.Add(iTime, si);
                    }
                    else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                    {
                        break;
                    }
                    else
                    {
                        log.Error("Unexpected type: " + package.Content.Type);
                        break;
                    }
                }
            }
        }
    }
}
    
   
