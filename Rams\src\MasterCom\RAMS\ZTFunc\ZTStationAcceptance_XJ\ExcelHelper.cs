﻿using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Data;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc.ZTStationAcceptance_XJ
{
    public static class ExcelHelper
    {
        public delegate void DealDataDel<in T>(T content, DataRow dr);

        public static T ReadExcel<T>(string fileName, DealDataDel<T> func)
             where T : new()
        {
            //WaitBox.ProgressPercent = 10;
            T content = new T();
            DataSet ds = ExcelNPOIManager.ImportFromExcel(fileName);
            if (ds == null || ds.Tables == null || ds.Tables.Count == 0)
            {
                MessageBox.Show("Excel中没有数据或读取失败！");
                return content;
            }

            try
            {
                foreach (DataTable dt in ds.Tables)
                {
                    //WaitBox.Text = "正在读取Excel信息..." + dt.TableName;
                    //WaitBox.ProgressPercent += 30;
                    foreach (DataRow dr in dt.Rows)
                    {
                        func(content, dr);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message + Environment.NewLine + ex.Source + Environment.NewLine + ex.StackTrace);
                return new T();
            }
            return content;
        }

    }
}
