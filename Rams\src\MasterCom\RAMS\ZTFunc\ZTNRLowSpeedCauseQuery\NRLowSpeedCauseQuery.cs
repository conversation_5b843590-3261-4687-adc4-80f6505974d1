﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRLowSpeedCauseQuery : DIYAnalyseByFileBackgroundBase
    {
        private NRLowSpeedCauseCondition funcCond = null;
        protected static readonly object lockObj = new object();
        private static NRLowSpeedCauseQuery instance = null;
        public static NRLowSpeedCauseQuery GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new NRLowSpeedCauseQuery();
                    }
                }
            }
            return instance;
        }

        protected NRLowSpeedCauseQuery()
            : base(MainModel.GetInstance())
        {
            Columns = NRTpHelper.InitBaseReplayParamBackground(false, false);
            Columns.Add("NR_APP_type");
            Columns.Add("NR_APP_Speed");

            ServiceTypes = ServiceTypeManager.GetServiceTypesByServiceName(ServiceName.NR);
            carrierID = CarrierType.ChinaMobile;
        }

        public override string Name
        {
            get
            {
                return "NR低速率原因分析";
            }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 35000, 35031, this.Name);
        }

        protected override bool getCondition()
        {
            segs = new List<NRLowSpeedSeg>();
            NRLowSpeedCauseDlg dlg = new NRLowSpeedCauseDlg(funcCond);
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            funcCond = dlg.Condition;
            return funcCond != null;
        }

        List<NRLowSpeedSeg> segs = new List<NRLowSpeedSeg>();
        protected override void fireShowForm()
        {
            if (segs.Count == 0)
            {
                MessageBox.Show("没有符合条件的数据。");
                return;
            }
            NRLowSpeedCauseForm frm = MainModel.GetObjectFromBlackboard(typeof(NRLowSpeedCauseForm)) as NRLowSpeedCauseForm;
            if (frm == null || frm.IsDisposed)
            {
                frm = new NRLowSpeedCauseForm();
            }
            frm.FillData(segs, funcCond);
            frm.Owner = MainModel.MainForm;
            frm.Visible = true;
            frm.BringToFront();
            segs = null;
        }

        /// <summary>
        ///  保存符合条件的路段，并重置seg变量为null
        /// </summary>
        /// <param name="seg"></param>
        /// <param name="evts"></param>
        private void saveSegment(ref NRLowSpeedSeg seg, List<Event> evts)
        {
            if (seg == null || !funcCond.IsValidSegment(seg))
            {
                seg = null;
                return;
            }
            //保留路段前后10秒的事件，以便后续分析
            if (evts != null)
            {
                int bTime = seg.TestPoints[0].Time - 10;
                int eTime = seg.TestPoints[seg.TestPoints.Count - 1].Time + 10;
                foreach (Event e in evts)
                {
                    if (bTime <= e.Time && e.Time <= eTime)
                    {
                        seg.AddEvent(e);
                    }
                }
            }
            segs.Add(seg);
            seg = null;
        }

        protected override void doStatWithQuery()
        {
            try
            {
                foreach (DTFileDataManager fileDataManager in MainModel.DTDataManager.FileDataManagers)
                {
                    NRLowSpeedSeg seg = getLowSpeedSeg(fileDataManager);
                    //避免遗漏文件最后一路段
                    if (seg != null && segs.Contains(seg))
                    {
                        saveSegment(ref seg, fileDataManager.Events);
                    }
                    foreach (NRLowSpeedSeg segItem in segs)
                    {
                        funcCond.Judge(segItem, fileDataManager.Events, fileDataManager.TestPoints, NRTpHelper.NrTpManager);
                        segItem.MakeSummary();
                    }
                }
            }
            catch (Exception e)
            {
                MessageBox.Show(e.Message + Environment.NewLine + e.StackTrace);
            }
        }

        private NRLowSpeedSeg getLowSpeedSeg(DTFileDataManager fileDataManager)
        {
            NRLowSpeedSeg seg = null;
            foreach (TestPoint tp in fileDataManager.TestPoints)
            {
                if (!isValidTestPoint(tp))
                {
                    saveSegment(ref seg, fileDataManager.Events);
                    continue;
                }
                if (funcCond.IsValidSpeed(tp))
                {
                    if (seg == null)
                    {
                        seg = new NRLowSpeedSeg();
                    }
                    seg.AddTestPoint(tp);
                }
                else
                {
                    saveSegment(ref seg, fileDataManager.Events);
                }
            }

            return seg;
        }
    }

    public class NRLowSpeedCauseQueryByFile : NRLowSpeedCauseQuery
    {
        private static NRLowSpeedCauseQueryByFile instance = null;
        public new static NRLowSpeedCauseQueryByFile GetInstance()
        {
            if (instance == null)
            {
                instance = new NRLowSpeedCauseQueryByFile();
            }
            return instance;
        }

        public override string Name
        {
            get { return "NR低速率原因分析(按文件)"; }
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MainModel.NeedSearchType.File;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            return true;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                return true;
            }
            return false;
        }
    }
}
