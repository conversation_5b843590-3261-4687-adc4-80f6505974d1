﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using System.Xml;
using MasterCom.Util;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc.KPISheet_HaiNan
{
    public class ReportTemplateMng
    {
        private static ReportTemplateMng instance = null;
        private ReportTemplateMng()
        {
            Load();
        }
        public static ReportTemplateMng Instance
        {
            get {
                if (instance==null)
                {
                    instance = new ReportTemplateMng();
                }
                return instance;
            }
        }
        public List<ReportTemplate> Templates
        {
            get;
            private set;
        }

        private readonly string path = string.Format(Application.StartupPath + "/config/homepage/templates.xml");

        private List<object> cfgParam
        {
            get
            {
                List<object> rpts = new List<object>();
                foreach (ReportTemplate rpt in Templates)
                {
                    rpts.Add(rpt.Param);
                }
                return rpts;
            }
            set
            {
                if (value == null)
                {
                    return;
                }
                Templates.Clear();
                foreach (object obj in value)
                {
                    ReportTemplate rpt = new ReportTemplate();
                    rpt.Param = obj as Dictionary<string, object>;
                    Templates.Add(rpt);
                }
            }
        }

        public void Load()
        {
            Templates = new List<ReportTemplate>();
            if (System.IO.File.Exists(path))
            {
                XmlConfigFile configFile = new XmlConfigFile(path);
                cfgParam = configFile.GetItemValue("Cfg", "Templates") as List<object>;
            }
        }

        public void Save()
        {
            MasterCom.Util.XmlConfigFile xmlFile = new MasterCom.Util.XmlConfigFile();
            XmlElement cfgE = xmlFile.AddConfig("Cfg");
            xmlFile.AddItem(cfgE, "Templates", this.cfgParam);
            xmlFile.Save(path);
        }

    }


    public class ReportTemplate
    {
        public ReportTemplate()
        { }
        public ReportTemplate(string name)
        {
            this.Name = name;
            Columns = new List<ReportColunm>();
        }
        public string Name { get; set; }
        public override string ToString()
        {
            if (Name == null)
            {
                return string.Empty;
            }
            return Name;
        }

        public Dictionary<TableTemplate, List<ReportColunm>> TableColDic
        {
            get
            {
                Dictionary<TableTemplate, List<ReportColunm>> dic = new Dictionary<TableTemplate, List<ReportColunm>>();
                foreach (ReportColunm col in Columns)
                {
                    List<ReportColunm> colSet = null;
                    if (!dic.TryGetValue(col.TableCol.Table, out colSet))
                    {
                        colSet = new List<ReportColunm>();
                        dic[col.TableCol.Table] = colSet;
                    }
                    colSet.Add(col);
                }
                return dic;
            }
        }

        public List<ReportColunm> Columns
        {
            get;
            set;
        }

        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> dic = new Dictionary<string, object>();
                dic["Name"] = this.Name;
                List<object> cols = new List<object>();
                dic["Columns"] = cols;
                foreach (ReportColunm col in Columns)
                {
                    cols.Add(col.Param);
                }
                return dic;
            }
            set
            {
                this.Name = value["Name"] as string;
                this.Columns = new List<ReportColunm>();
                List<object> obj = value["Columns"] as List<object>;
                foreach (Dictionary<string, object> dic in obj)
                {
                    ReportColunm col = new ReportColunm();
                    col.Param = dic;
                    this.Columns.Add(col);
                }
            }
        }

    }


    public class ReportColunm
    {
        public string FullName
        {
            get
            {
                if (tbCol == null)
                {
                    return this.Name;
                }
                return tbCol.FullName;
            }
        }

        public string Name
        {
            get;
            set;
        }

        public bool IsFrozen
        {
            get;
            set;
        }

        TableColumn tbCol = null;
        public TableColumn TableCol
        {
            get { return tbCol; }
        }
        public ReportColunm(TableColumn tbCol)
            : this()
        {
            this.tbCol = tbCol;
            this.Name = tbCol.Name;
        }

        public ReportColunm()
        {
            IsStaticColor = true;
            CellDynamicBkColorRanges = new List<Model.DTParameterRangeColor>();
        }

        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> dic = new Dictionary<string, object>();
                dic["Name"] = this.Name;
                dic["ColHashName"] = this.tbCol.HashName;
                dic["IsFrozen"] = this.IsFrozen;
                dic["ColHeaderBkColor"] = ColHeaderBkColor.ToArgb();
                dic["IsStaticColor"] = IsStaticColor;
                dic["CellStaticBkColor"] = CellStaticBkColor.ToArgb();
                dic["ValueRangeMin"] = ValueRangeMin;
                dic["ValueRangeMax"] = ValueRangeMax;
                dic["CellDynamicBkColorRanges"] = CellDynamicBkColorRanges;
                List<object> colorParam = new List<object>();
                if (CellDynamicBkColorRanges != null)
                {
                    foreach (DTParameterRangeColor item in CellDynamicBkColorRanges)
                    {
                        colorParam.Add(item.Params);
                    }
                }
                dic["ColorParam"] = colorParam;
                return dic;
            }
            set
            {
                this.Name = value["Name"] as string;
                string hashName = value["ColHashName"] as string;
                this.tbCol = TableCfgManager.Instance.GetTableColumn(hashName);
                this.IsFrozen = (bool)value["IsFrozen"];
                ColHeaderBkColor = Color.FromArgb((int)value["ColHeaderBkColor"]);
                IsStaticColor = (bool)value["IsStaticColor"];
                CellStaticBkColor = Color.FromArgb((int)value["CellStaticBkColor"]);
                ValueRangeMin = (double)value["ValueRangeMin"];
                ValueRangeMax = (double)value["ValueRangeMax"];

                List<object> colorParam = value["ColorParam"] as List<object>;
                if (colorParam != null)
                {
                    this.CellDynamicBkColorRanges = new List<DTParameterRangeColor>();
                    foreach (Dictionary<string, object> item in colorParam)
                    {
                        DTParameterRangeColor color = new DTParameterRangeColor();
                        color.Params = item;
                        this.CellDynamicBkColorRanges.Add(color);
                    }
                }
            }
        }


        public System.Drawing.Color ColHeaderBkColor { get; set; }
        public bool IsStaticColor
        { get; set; }

        public System.Drawing.Color CellStaticBkColor { get; set; }

        public double ValueRangeMin { get; set; }

        public double ValueRangeMax { get; set; }

        public List<DTParameterRangeColor> CellDynamicBkColorRanges { get; set; }
    }
}
