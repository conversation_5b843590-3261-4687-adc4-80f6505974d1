﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util.UiEx;

namespace MasterCom.RAMS.NOP
{
    public class ShowTaskESResult : QueryBase
    {
        private readonly bool includeVer2017;
        public ShowTaskESResult(TaskEventItem taskItem, bool includeVer2017 = false)
            : base(MainModel.GetInstance())
        {
            this.taskItem = taskItem;
            this.includeVer2017 = includeVer2017;
        }
        readonly TaskEventItem taskItem = null;

        public override string Name
        {
            get { return "查询并显示工单问题点预判详情"; }
        }

        protected override bool isValidCondition()
        {
            return taskItem != null;
        }

        protected override void query()
        {
            WaitTextBox.Show("正在获取问题点预判详情...", queryTaskESResult);
            fireShowFormAfterQuery();
        }

        private void fireShowFormAfterQuery()
        {
            if (this.taskItem.ESResultInfo == null && this.taskItem.ESResultInfoV2017 == null)
            {
                System.Windows.Forms.MessageBox.Show("获取问题点预判详情异常!");
                return;
            }

            if (this.taskItem.ESResultInfo != null)
            {
                TaskFlowDiagramForm frm = MainModel.CreateResultForm(typeof(TaskFlowDiagramForm)) as TaskFlowDiagramForm;
                frm.TaskEventItem = this.taskItem;
                frm.Visible = true;
                frm.BringToFront();
            }

            if (this.taskItem.ESResultInfoV2017 != null)
            {
                TaskFlowDiagramForm2017 frm2017 = MainModel.CreateResultForm(typeof(TaskFlowDiagramForm2017)) as TaskFlowDiagramForm2017;
                frm2017.TaskEventItem = this.taskItem;
                frm2017.Visible = true;
                frm2017.BringToFront();
            }

            //object obj = MainModel.GetInstance().GetObjectFromBlackboard(typeof(TaskFlowDiagramForm2017).FullName + "_" + text);
            //TaskFlowDiagramForm2017 frmV2017 = obj as TaskFlowDiagramForm2017;
            //if (frmV2017 == null || frmV2017.IsDisposed)
            //{
            //    frmV2017 = new TaskFlowDiagramForm(text);
            //}
            //frmV2017.TaskEventItem = this.taskItem;
            //frmV2017.Visible = true;
            //frmV2017.BringToFront();
        }

        private void queryTaskESResult()
        {
            QueryTaskESResultInfo qry = new QueryTaskESResultInfo(this.taskItem, includeVer2017);
            qry.Query();
            WaitTextBox.Close();
        }

    }
}
