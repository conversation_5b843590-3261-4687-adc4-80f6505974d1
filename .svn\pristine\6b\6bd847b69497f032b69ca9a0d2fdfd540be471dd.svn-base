﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.Model
{
    public class AvgInfo
    {
        public double Sum { get; set; }
        public int Count { get; set; }
        public double Avg { get; set; }
        public string AvgDesc { get; set; }
        public double Max { get; set; }
        public string MaxDesc { get; set; }
        public double Min { get; set; }
        public string MinDesc { get; set; }

        public void Calculate()
        {
            if (Count != 0)
            {
                Avg = Math.Round(Sum / Count, 2);
                Max = Math.Round(Max, 2);
                Min = Math.Round(Min, 2);
                AvgDesc = Avg.ToString();
                MaxDesc = Max.ToString();
                MinDesc = Min.ToString();
            }
            else
            {
                AvgDesc = "-";
                MaxDesc = "-";
                MinDesc = "-";
            }
        }

        public void Add(float data, int count = 1)
        {
            Sum += data;
            Count += count;

            dealMaxAndMin(data);
        }

        public void Add(double data, int count = 1)
        {
            Sum += data;
            Count += count;

            dealMaxAndMin(data);
        }

        public void Add(float? data, int count = 1)
        {
            if (data != null)
            {
                Add((float)data, count);
            }
        }

        public void Add(double? data, int count = 1)
        {
            if (data != null)
            {
                Add((double)data, count);
            }
        }

        public void Add(AvgInfo info)
        {
            Sum += info.Sum;
            Count += info.Count;

            dealMaxAndMin(info);
        }

        private void dealMaxAndMin(double data)
        {
            if (Count == 1)
            {
                Max = data;
                Min = data;
            }
            else
            {
                if (Max < data)
                {
                    Max = data;
                }
                if (Min > data)
                {
                    Min = data;
                }
            }
        }

        private void dealMaxAndMin(AvgInfo info)
        {
            if (Max < info.Max)
            {
                Max = info.Max;
            }
            if (Min > info.Min)
            {
                Min = info.Min;
            }
        }
    }

    public class RateInfo
    {
        public int Count { get; set; }
        public int TotalCount { get; set; }
        public double Rate { get; set; }
        public string RateDesc { get; set; }

        public void Calculate(bool isPercentage)
        {
            if (TotalCount != 0)
            {
                Rate = Math.Round(Count * 1d / TotalCount, 4);
                RateDesc = Rate.ToString();
                if (isPercentage)
                {
                    Rate = Rate * 100;
                    RateDesc = Rate + "%";
                }
            }
            else
            {
                RateDesc = "-";
            }
        }

        public void Add(RateInfo info)
        {
            Count += info.Count;
            TotalCount += info.TotalCount;
        }
    }
}
