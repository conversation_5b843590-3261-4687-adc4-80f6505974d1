﻿namespace MasterCom.RAMS.BackgroundFunc
{
    partial class StationAcceptReportForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(StationAcceptReportForm));
            this.gridControlReportInfo = new DevExpress.XtraGrid.GridControl();
            this.contextMenuStripFile = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.toolStripMenuItemDownload = new System.Windows.Forms.ToolStripMenuItem();
            this.miDownloadSelFiles = new System.Windows.Forms.ToolStripMenuItem();
            this.miDownloadAllFiles = new System.Windows.Forms.ToolStripMenuItem();
            this.gridViewReportInfo = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn6 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn7 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColReportPath = new DevExpress.XtraGrid.Columns.GridColumn();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlReportInfo)).BeginInit();
            this.contextMenuStripFile.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewReportInfo)).BeginInit();
            this.SuspendLayout();
            // 
            // gridControlReportInfo
            // 
            this.gridControlReportInfo.ContextMenuStrip = this.contextMenuStripFile;
            this.gridControlReportInfo.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlReportInfo.Location = new System.Drawing.Point(0, 0);
            this.gridControlReportInfo.MainView = this.gridViewReportInfo;
            this.gridControlReportInfo.Name = "gridControlReportInfo";
            this.gridControlReportInfo.Size = new System.Drawing.Size(1131, 421);
            this.gridControlReportInfo.TabIndex = 0;
            this.gridControlReportInfo.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridViewReportInfo});
            // 
            // contextMenuStripFile
            // 
            this.contextMenuStripFile.ImageScalingSize = new System.Drawing.Size(24, 24);
            this.contextMenuStripFile.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.toolStripMenuItemDownload});
            this.contextMenuStripFile.Name = "contextMenuStrip1";
            this.contextMenuStripFile.Size = new System.Drawing.Size(145, 34);
            // 
            // toolStripMenuItemDownload
            // 
            this.toolStripMenuItemDownload.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miDownloadSelFiles,
            this.miDownloadAllFiles});
            this.toolStripMenuItemDownload.Image = ((System.Drawing.Image)(resources.GetObject("toolStripMenuItemDownload.Image")));
            this.toolStripMenuItemDownload.Name = "toolStripMenuItemDownload";
            this.toolStripMenuItemDownload.Size = new System.Drawing.Size(144, 30);
            this.toolStripMenuItemDownload.Text = "下载到本地";
            // 
            // miDownloadSelFiles
            // 
            this.miDownloadSelFiles.Name = "miDownloadSelFiles";
            this.miDownloadSelFiles.Size = new System.Drawing.Size(148, 22);
            this.miDownloadSelFiles.Text = "下载选中文件";
            this.miDownloadSelFiles.Click += new System.EventHandler(this.miDownloadSelFiles_Click);
            // 
            // miDownloadAllFiles
            // 
            this.miDownloadAllFiles.Name = "miDownloadAllFiles";
            this.miDownloadAllFiles.Size = new System.Drawing.Size(148, 22);
            this.miDownloadAllFiles.Text = "下载所有文件";
            this.miDownloadAllFiles.Click += new System.EventHandler(this.miDownloadAllFiles_Click);
            // 
            // gridViewReportInfo
            // 
            this.gridViewReportInfo.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn1,
            this.gridColumn2,
            this.gridColumn3,
            this.gridColumn4,
            this.gridColumn5,
            this.gridColumn6,
            this.gridColumn7,
            this.gridColReportPath});
            this.gridViewReportInfo.GridControl = this.gridControlReportInfo;
            this.gridViewReportInfo.Name = "gridViewReportInfo";
            this.gridViewReportInfo.OptionsBehavior.Editable = false;
            this.gridViewReportInfo.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridViewReportInfo.OptionsSelection.MultiSelect = true;
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "地市名";
            this.gridColumn1.FieldName = "DistictName";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.Visible = true;
            this.gridColumn1.VisibleIndex = 0;
            this.gridColumn1.Width = 107;
            // 
            // gridColumn2
            // 
            this.gridColumn2.Caption = "基站名";
            this.gridColumn2.FieldName = "BtsName";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.Visible = true;
            this.gridColumn2.VisibleIndex = 1;
            this.gridColumn2.Width = 152;
            // 
            // gridColumn3
            // 
            this.gridColumn3.Caption = "eNodeBID";
            this.gridColumn3.FieldName = "ENodeBID";
            this.gridColumn3.Name = "gridColumn3";
            this.gridColumn3.Visible = true;
            this.gridColumn3.VisibleIndex = 2;
            this.gridColumn3.Width = 107;
            // 
            // gridColumn4
            // 
            this.gridColumn4.Caption = "覆盖类型";
            this.gridColumn4.FieldName = "CoverType";
            this.gridColumn4.Name = "gridColumn4";
            this.gridColumn4.Visible = true;
            this.gridColumn4.VisibleIndex = 3;
            // 
            // gridColumn5
            // 
            this.gridColumn5.Caption = "基站是否通过验收";
            this.gridColumn5.FieldName = "IsBtsPassAccept";
            this.gridColumn5.Name = "gridColumn5";
            this.gridColumn5.Visible = true;
            this.gridColumn5.VisibleIndex = 4;
            this.gridColumn5.Width = 120;
            // 
            // gridColumn6
            // 
            this.gridColumn6.Caption = "验收时间";
            this.gridColumn6.FieldName = "UpdateTime";
            this.gridColumn6.Name = "gridColumn6";
            this.gridColumn6.Visible = true;
            this.gridColumn6.VisibleIndex = 5;
            this.gridColumn6.Width = 143;
            // 
            // gridColumn7
            // 
            this.gridColumn7.Caption = "备注";
            this.gridColumn7.FieldName = "ErrorInfo";
            this.gridColumn7.Name = "gridColumn7";
            this.gridColumn7.Visible = true;
            this.gridColumn7.VisibleIndex = 6;
            this.gridColumn7.Width = 215;
            // 
            // gridColReportPath
            // 
            this.gridColReportPath.Caption = "报告路径";
            this.gridColReportPath.FieldName = "Strdesc";
            this.gridColReportPath.Name = "gridColReportPath";
            this.gridColReportPath.Visible = true;
            this.gridColReportPath.VisibleIndex = 7;
            this.gridColReportPath.Width = 211;
            // 
            // StationAcceptReportForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1131, 421);
            this.Controls.Add(this.gridControlReportInfo);
            this.Name = "StationAcceptReportForm";
            this.Text = "单站验收报告";
            ((System.ComponentModel.ISupportInitialize)(this.gridControlReportInfo)).EndInit();
            this.contextMenuStripFile.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridViewReportInfo)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraGrid.GridControl gridControlReportInfo;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewReportInfo;
        private System.Windows.Forms.ContextMenuStrip contextMenuStripFile;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItemDownload;
        private System.Windows.Forms.ToolStripMenuItem miDownloadSelFiles;
        private System.Windows.Forms.ToolStripMenuItem miDownloadAllFiles;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn6;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn7;
        private DevExpress.XtraGrid.Columns.GridColumn gridColReportPath;
    }
}