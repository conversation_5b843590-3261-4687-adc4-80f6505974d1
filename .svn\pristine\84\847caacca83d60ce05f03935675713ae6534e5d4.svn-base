using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.NewBlackBlock
{
    public partial class BlackBlockHandleForm : Form
    {
        Dictionary<int, List<BlackBlockHandle>> blackBlockHandleDic = new Dictionary<int, List<BlackBlockHandle>>();
        List<BlackBlockHandle> curBlackBlockHandleList = new List<BlackBlockHandle>();
        public BlackBlockHandleForm()
        {
            InitializeComponent();
            init();
            chkReason.Checked = true;
            chkHandleStatus.Checked = true;
            chkHandleAdvice.Checked = true;
        }

        private void init()
        {
            this.olvColumnIndex.AspectGetter = delegate(object row)
            {
                List<BlackBlockHandle> list = objectListView.Objects as List<BlackBlockHandle>;
                return list.IndexOf(row as BlackBlockHandle) + 1;
            };
        }

        public void FillData(Dictionary<int, List<BlackBlockHandle>> blackBlockHandleDic)
        {
            this.blackBlockHandleDic = blackBlockHandleDic;
            fillListView();
        }

        private void fillListView()
        {
            curBlackBlockHandleList.Clear();
            foreach (KeyValuePair<int, List<BlackBlockHandle>> keyValue in blackBlockHandleDic)
            {
                if (keyValue.Key == 0 && chkReason.Checked)
                {
                    curBlackBlockHandleList.AddRange(keyValue.Value);
                }
                else if (keyValue.Key == 1 && chkHandleStatus.Checked)
                {
                    curBlackBlockHandleList.AddRange(keyValue.Value);
                }
                else if (keyValue.Key == 2 && chkHandleAdvice.Checked)
                {
                    curBlackBlockHandleList.AddRange(keyValue.Value);
                }
            }
            curBlackBlockHandleList.Sort(BlackBlockHandle.GetCompareByDateTime());

            objectListView.ClearObjects();
            if (curBlackBlockHandleList.Count > 0)
            {
                objectListView.SetObjects(curBlackBlockHandleList);
            }
        }

        private void chkReason_CheckedChanged(object sender, EventArgs e)
        {
            fillListView();
            txtContent.Text = "";
        }

        private void objectListView_SelectionChanged(object sender, EventArgs e)
        {
            if (objectListView.SelectedObjects.Count > 0)
            {
                txtContent.Text = (objectListView.SelectedObject as BlackBlockHandle).content;
            }
        }
    }
}