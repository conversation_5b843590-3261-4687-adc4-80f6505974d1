﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.Net
{
    public static partial class RequestType
    {
        public const byte REQTYPE_FILEDISTRIBUTE = 0x0F;	//REQUEST
    }

    public static partial class ResponseType
    {
        public const byte RESTYPE_FILEDISTRIBUTE_SUCCESS = 0x12;
        public const byte RESTYPE_FILEDISTRIBUTE_FAIL = 0x13;
    }

    public class FileDistributeCommand
        : QueryBase
    {
        public FileDistributeCommand(MainModel mainModel)
            : base(mainModel)
        {
        }

        public override bool IsNeedSetQueryCondition
        {
            get { return false; }
        }

        public override string Name
        {
            get { return "服务端文件拷贝"; }
        }

        public override string IconName
        {
            get { return string.Empty; }
        }

        public FileInfo SrcFileInfo
        {
            get;
            set;
        }

        public string TarSavepath
        {
            get;
            set;
        }

        public bool IsSucceed
        {
            get;
            set;
        }

        protected override bool isValidCondition()
        {
            if (SrcFileInfo == null || string.IsNullOrEmpty(TarSavepath))
            {
                return IsSucceed = false;
            }
            return true;
        }

        protected override void query()
        {
            IsSucceed = false;

            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    IsSucceed = false;
                    return;
                }
                PrepareQueryPackage(clientProxy.Package);
                clientProxy.Send();
                Recieve(clientProxy);
            }
            finally
            {
                clientProxy.Close();
            }
        }

        protected virtual void PrepareQueryPackage(Package package)
        {
            package.Command = Command.DataManage;
            package.SubCommand = SubCommand.Request;
            package.Content.Type = RequestType.REQTYPE_FILEDISTRIBUTE;
            package.Content.PrepareAddParam();
            package.Content.AddParam(SrcFileInfo.ID);
            package.Content.AddParam(SrcFileInfo.ProjectID);
            package.Content.AddParam(SrcFileInfo.LogTable);
            package.Content.AddParam(TarSavepath);
        }

        protected virtual void Recieve(ClientProxy clientProxy)
        {
            while (true)
            {
                clientProxy.Recieve();
                clientProxy.Package.Content.PrepareGetParam();

                if (clientProxy.Package.Content.Type == ResponseType.RESTYPE_FILEDISTRIBUTE_SUCCESS)
                {
                    this.IsSucceed = true;
                    break;
                }
                else if (clientProxy.Package.Content.Type == ResponseType.RESTYPE_FILEDISTRIBUTE_FAIL)
                {
                    this.IsSucceed = false;
                    break;
                }
                else if (clientProxy.Package.Content.Type == ResponseType.END)
                {
                    break;
                }
            }
        }
    }
}
