﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func.SystemSetting;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using MasterCom.RAMS.ZTFunc.ZTPoorRSRQRoad;

namespace MasterCom.RAMS.ZTFunc
{
    public class QueryPoorRsrqRoad : QueryPoorRsrqRoadBase
    {
        protected override string themeName { get { return "LTE_TDD:RSRQ"; } }
        protected override string rsrpName { get { return "lte_RSRP"; } }
        protected override string sinrName { get { return "lte_SINR"; } }
        protected override string rsrqName { get { return "lte_RSRQ"; } }

        protected static readonly object lockObj = new object();
        private static QueryPoorRsrqRoad intance = null;
        public static QueryPoorRsrqRoad Instance
        {
            get
            {
                if (intance == null)
                {
                    lock (lockObj)
                    {
                        if (intance == null)
                        {
                            intance = new QueryPoorRsrqRoad();
                        }
                    }
                }
                return intance;
            }
        }

        protected QueryPoorRsrqRoad()
            : base()
        {
            if (intance != null)
            {
                return;
            }
            ServiceTypes.Clear();
        }

        public QueryPoorRsrqRoad(ServiceName serviceName)
            : base()
        {
            ServiceTypes.Clear();
            ServiceTypes = ServiceTypeManager.GetServiceTypesByServiceName(serviceName);
        }

        public override string Name
        {
            get { return "低RSRQ路段"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22009, this.Name);//////
        }
    }

    public class QueryPoorRsrqRoad_FDD : QueryPoorRsrqRoad
    {
        protected override string themeName { get { return "LTE_FDD:RSRQ"; } }
        protected override string rsrpName { get { return "lte_fdd_RSRP"; } }
        protected override string sinrName { get { return "lte_fdd_SINR"; } }
        protected override string rsrqName { get { return "lte_fdd_RSRQ"; } }

        private static QueryPoorRsrqRoad_FDD instance = null;
        public static QueryPoorRsrqRoad_FDD GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new QueryPoorRsrqRoad_FDD();
                    }
                }
            }
            return instance;
        }

        public override string Name
        {
            get { return "低RSRQ路段_LTE_FDD"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 26000, 26025, this.Name);//////
        }
    }

    public class QueryPoorRsrqRoad_FDD_VOLTE : QueryPoorRsrqRoad_FDD
    {
        private static QueryPoorRsrqRoad_FDD_VOLTE instance = null;
        public static new QueryPoorRsrqRoad_FDD_VOLTE GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new QueryPoorRsrqRoad_FDD_VOLTE();
                    }
                }
            }
            return instance;
        }

        public QueryPoorRsrqRoad_FDD_VOLTE()
            : base()
        {
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_FDD_VOLTE);
            ServiceTypes.Add(ServiceType.SER_LTE_FDD_VIDEO_VOLTE);
        }

        public override string Name
        {
            get { return "VOLTE_FDD低RSRQ路段"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 30000, 30017, this.Name);//////
        }
    }
}
