﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    class NRUrlAnalyzerByFile : ZTUrlAnalyzer
    {
        public List<NRUrlAnaInfo> Results = null;
        protected Dictionary<string, List<Event>> dicBro = null;
        protected Dictionary<string, List<Event>> dicDown = null;
        protected Dictionary<string, List<Event>> dicVideo = null;

        public void Analyze(List<DTFileDataManager> fileManagers, Dictionary<int, string> dic)
        {
            Results = new List<NRUrlAnaInfo>();
            foreach (DTFileDataManager dtFile in fileManagers)
            {
                Dictionary<int, string> dicName = dic;
                Analyze(dtFile, dicName);
            }
            setSN(Results);
        }

        protected void Analyze(DTFileDataManager dtFile, Dictionary<int, string> dic)
        {
            NRUrlAnaInfoByFile dtlModel = new NRUrlAnaInfoByFile(dtFile.FileName);
            if (dic.ContainsKey(dtFile.FileID))
            {
                dtlModel.DistrictName = dic[dtFile.FileID];
            }
            DTFileDataManager f = dtFile;
            dicBro = new Dictionary<string, List<Event>>();
            dicDown = new Dictionary<string, List<Event>>();
            dicVideo = new Dictionary<string, List<Event>>();
            AnaToDictionary(f);
            var dtlBros = HttpAnalyze(dicBro);
            dtlModel.Bros = dtlBros;

            var dtlDowns = DownAnalyze(dicDown);
            dtlModel.Downs = dtlDowns;

            var dtlVideos = VideoAnalyze(dicVideo);
            dtlModel.Videos = dtlVideos;
            Results.Add(dtlModel);
        }

        protected void AnaToDictionary(DTFileDataManager dtFile)
        {
            string broURL = "";
            string downURL = "";
            string videoURL = "";
            foreach (Event evt in dtFile.Events)
            {
                if (HttpIDList.Contains(evt.ID))
                {
                    broURL = addValidData(dtFile, broURL, evt, dicBro, (int)NREventManager.HttpRequest);
                }
                else if (DownloadIDList.Contains(evt.ID))
                {
                    downURL = addValidData(dtFile, downURL, evt, dicDown, (int)NREventManager.DownRequest);
                }
                else if (VideoIDList.Contains(evt.ID))
                {
                    videoURL = addValidData(dtFile, videoURL, evt, dicVideo, (int)NREventManager.VideoRequest);
                }
            }
        }

        private string addValidData(DTFileDataManager dtFile, string url, Event evt, Dictionary<string, List<Event>> dic,
            int tddRequestEventID)
        {
            if (evt.ID == tddRequestEventID)
            {
                url = GetURL(evt.SN, dtFile.Messages);
                if (dic.ContainsKey(url))
                {
                    dic[url].Add(evt);
                }
                else
                {
                    List<Event> listTemp = new List<Event>();
                    listTemp.Add(evt);
                    dic.Add(url, listTemp);
                }
            }
            else
            {
                if (dic.ContainsKey(url))
                {
                    dic[url].Add(evt);
                }
            }

            return url;
        }
    }
}
