﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraTreeList.Nodes;
using DevExpress.XtraTreeList;

namespace MasterCom.RAMS.ZTFunc.AreaArchiveManage
{
    public partial class AreaPanel : UserControl
    {
        private List<AreaBase> rootVec;

        private Dictionary<AreaBase, TreeListNode> areaNodeMap;

        private AreaCondition areaCond;

        private VillagePanel villagePanel;

        public AreaPanel()
        {
            InitializeComponent();

            areaNodeMap = new Dictionary<AreaBase, TreeListNode>();
            areaCond = new AreaCondition();

            comboBoxEditCity.SelectedIndexChanged += new EventHandler(comboBoxEditCity_SelectedIndexChanged);
            comboBoxEditCountry.SelectedIndexChanged += new EventHandler(comboBoxEditCountry_SelectedIndexChanged);

            villagePanel = new VillagePanel(btnAddVillages_Click);
            toolStripDropDownVillage.Items.Clear();
            toolStripDropDownVillage.Items.Add(new ToolStripControlHost(villagePanel));
        }

        public void FillData(ZTAreaManager manager)
        {
            rootVec = manager.GetArea(manager.Ranks[0]);

            if (rootVec == null)
            {
                return;
            }
            foreach (AreaBase area in rootVec)
            {
                if (area.SubAreas == null)
                {
                    continue;
                }

                comboBoxEditCity.Properties.Items.Add(area);
            }

            if(comboBoxEditCity.Properties.Items.Count > 0)
                comboBoxEditCity.SelectedIndex = 0;
        }

        public void SetCondition(AreaCondition cond)
        {
            this.areaCond = cond;
            fillCondition(areaCond);
        }

        public bool CheckCondition()
        {
            if (treeListArea.Nodes.Count == 0)
            {
                MessageBox.Show("未选择区域...");
                return false;
            }
            return true;
        }

        private void fillCondition(AreaCondition cond)
        {
            treeListArea.Nodes.Clear();

            foreach (AreaBase root in cond.RootLeafDic.Keys)
            {
                appendTreeNode(root, cond.AreaSubDic, treeListArea, null);
            }
        }

        private void appendTreeNode(AreaBase area, Dictionary<AreaBase, List<AreaBase>> areaSubDic, 
            TreeList treeList, TreeListNode parentNode)
        {
            TreeListNode node = treeList.AppendNode(new object[] { area.Name }, parentNode);
            node.Tag = area;
            areaNodeMap[area] = node;
            if (areaSubDic.ContainsKey(area))
            {
                foreach (AreaBase subArea in areaSubDic[area])
                {
                    appendTreeNode(subArea, areaSubDic, treeList, node);
                }
            }
        }

        public AreaCondition GetCondition()
        {
            return areaCond;
        }

        private void comboBoxEditCity_SelectedIndexChanged(object sender, EventArgs e)
        {
            comboBoxEditCountry.Properties.Items.Clear();

            AreaBase root = comboBoxEditCity.SelectedItem as AreaBase;
            if (root == null || root.SubAreas == null) return;

            foreach (AreaBase sub in root.SubAreas)
            {
                comboBoxEditCountry.Properties.Items.Add(sub);
            }

            AreaGroup group = new AreaGroup();
            group.GroupVec = root.SubAreas;
            comboBoxEditCountry.Properties.Items.Insert(0, group);
            comboBoxEditCountry.SelectedItem = group;
        }

        private void comboBoxEditCountry_SelectedIndexChanged(object sender, EventArgs e)
        {
            comboBoxEditTown.Properties.Items.Clear();
            object o = comboBoxEditCountry.SelectedItem;
            if (o is AreaGroup)
            {
                AreaGroup group = o as AreaGroup;
                AreaGroup townGroup = new AreaGroup();
                foreach (AreaBase country in group.GroupVec)
                {
                    if (country.SubAreas != null)
                        townGroup.GroupVec.AddRange(country.SubAreas);
                }
                comboBoxEditTown.Properties.Items.Add(townGroup);
                comboBoxEditTown.SelectedItem = townGroup;
            }
            else if (o is AreaBase)
            {
                AreaBase country = comboBoxEditCountry.SelectedItem as AreaBase;
                if (country == null || country.SubAreas == null) return;

                foreach (AreaBase sub in country.SubAreas)
                {
                    comboBoxEditTown.Properties.Items.Add(sub);
                }
                AreaGroup group = new AreaGroup();
                group.GroupVec = country.SubAreas;
                comboBoxEditTown.Properties.Items.Insert(0, group);
                comboBoxEditTown.SelectedItem = group;
            }
        }

        private void fillVillage()
        {
            object o = comboBoxEditTown.SelectedItem;
            List<AreaBase> vilShow = new List<AreaBase>();
            if (o is AreaGroup)
            {
                AreaGroup townGroup = o as AreaGroup;
                foreach (AreaBase town in townGroup.GroupVec)
                {
                    if (town.SubAreas != null)
                        vilShow.AddRange(town.SubAreas);
                }
            }
            else if (o is AreaBase)
            {
                AreaBase town = o as AreaBase;
                if (town == null || town.SubAreas == null) return;
                vilShow.AddRange(town.SubAreas);
            }

            string filterTxt = buttonEditVillage.Text.Trim();
            List<AreaBase> filters = new List<AreaBase>();
            foreach (AreaBase village in vilShow)
            {
                if (filterTxt == "" || village.Name.Contains(filterTxt))
                    filters.Add(village);
            }
            villagePanel.FillData(filters);
        }

        private void btnAddVillages_Click(object sender, EventArgs e)
        {
            List<AreaBase> checkVillages = villagePanel.GetChecks();
            if (checkVillages.Count == 0) return;

            Dictionary<AreaBase, List<AreaBase>> townVillDic = new Dictionary<AreaBase, List<AreaBase>>();
            foreach (AreaBase village in checkVillages)
            {
                AreaBase town = village.ParentArea;
                List<AreaBase> villages;
                if (!townVillDic.TryGetValue(town, out villages))
                {
                    villages = new List<AreaBase>();
                    townVillDic[town] = villages;
                }
                villages.Add(village);
            }
            foreach (AreaBase town in townVillDic.Keys)
            {
                AreaBase country = town.ParentArea;
                AreaBase city = country.ParentArea;

                areaCond.AddArea(city, country, town, townVillDic[town]);
            }

            toolStripDropDownVillage.Close();

            fillCondition(areaCond);
        }

        private void buttonEditVillage_ButtonClick(object sender, DevExpress.XtraEditors.Controls.ButtonPressedEventArgs e)
        {
            fillVillage();
            System.Drawing.Point pt = new System.Drawing.Point(buttonEditVillage.Width, buttonEditVillage.Height);
            toolStripDropDownVillage.Show(buttonEditVillage, pt, ToolStripDropDownDirection.BelowLeft);
        }

        private void ToolStripMenuItemRemove_Click(object sender, EventArgs e)
        {
            AreaBase area = treeListArea.FocusedNode.Tag as AreaBase;
            areaCond.RemoveArea(area);
            fillCondition(areaCond);
        }
    }

    public class AreaGroup
    {
        public List<AreaBase> GroupVec { get; set; }

        public AreaGroup()
        {
            GroupVec = new List<AreaBase>();
        }

        public override string ToString()
        {
            return "不限";
        }
    }

    public class AreaCondition
    {
        public Dictionary<AreaBase, List<AreaBase>> AreaSubDic { get; set; }
        public Dictionary<AreaBase, List<AreaBase>> TownLeafDic { get; set; }

        public Dictionary<AreaBase, List<AreaBase>> RootLeafDic
        {
            get
            {
                Dictionary<AreaBase, List<AreaBase>> rootLeafDic = new Dictionary<AreaBase, List<AreaBase>>();
                foreach (AreaBase town in TownLeafDic.Keys)
                {
                    AreaBase country = town.ParentArea;
                    AreaBase city = country.ParentArea;

                    List<AreaBase> rtVill;
                    if (!rootLeafDic.TryGetValue(city, out rtVill))
                    {
                        rtVill = new List<AreaBase>();
                        rootLeafDic[city] = rtVill;
                    }
                    rtVill.AddRange(TownLeafDic[town]);
                }
                return rootLeafDic;
            }
        }

        public Dictionary<int, Dictionary<int, AreaBase>> TypeIdDic
        {
            get
            {
                Dictionary<int, Dictionary<int, AreaBase>> typeIdDic = new Dictionary<int, Dictionary<int, AreaBase>>();
                foreach (AreaBase town in TownLeafDic.Keys)
                {
                    AreaBase pArea = town;
                    while (pArea != null)
                    {
                        Dictionary<int, AreaBase> idDic;
                        if (!typeIdDic.TryGetValue(pArea.AreaTypeID, out idDic))
                        {
                            idDic = new Dictionary<int, AreaBase>();
                            typeIdDic[pArea.AreaTypeID] = idDic;
                        }
                        idDic[pArea.AreaID] = pArea;

                        pArea = pArea.ParentArea;
                    }
                    foreach (AreaBase vil in TownLeafDic[town])
                    {
                        Dictionary<int, AreaBase> idVilDic;
                        if (!typeIdDic.TryGetValue(vil.AreaTypeID, out idVilDic))
                        {
                            idVilDic = new Dictionary<int, AreaBase>();
                            typeIdDic[vil.AreaTypeID] = idVilDic;
                        }
                        idVilDic[vil.AreaID] = vil;
                    }
                }
                return typeIdDic;
            }
        }

        public Dictionary<AreaBase, bool> AreaSelDic
        {
            get
            {
                Dictionary<AreaBase, bool> areaSelDic = new Dictionary<AreaBase, bool>();

                foreach (AreaBase town in TownLeafDic.Keys)
                {
                    AreaBase pArea = town;
                    while (pArea != null)
                    {
                        areaSelDic[pArea] = true;

                        pArea = pArea.ParentArea;
                    }
                    foreach (AreaBase vil in TownLeafDic[town])
                    {
                        areaSelDic[vil] = true;
                    }
                }
                return areaSelDic;
            }
        }

        public AreaCondition()
        {
            AreaSubDic = new Dictionary<AreaBase, List<AreaBase>>();
            TownLeafDic = new Dictionary<AreaBase, List<AreaBase>>();
        }

        public MasterCom.MTGis.DbRect GetBound()
        {
            MasterCom.MTGis.DbRect rect = null;
            foreach (List<AreaBase> vills in TownLeafDic.Values)
            {
                foreach (AreaBase vil in vills)
                {
                    if (rect == null)
                        rect = vil.Bounds.Clone();
                    else
                        rect.MergeRects(vil.Bounds);
                }
            }
            return rect;
        }

        private void removeArea(AreaBase area)
        {
            if (AreaSubDic.ContainsKey(area))
            {
                for (int idx = 0; idx < AreaSubDic[area].Count;idx++ )
                {
                    removeArea(AreaSubDic[area][idx]);
                }
                AreaSubDic.Remove(area);
            }
            else if (area.ParentArea != null && AreaSubDic.ContainsKey(area.ParentArea))
            {
                AreaSubDic[area.ParentArea].Remove(area);
            }
        }

        public void AddArea(AreaBase city, AreaBase country, AreaBase town, List<AreaBase> villages)
        {
            List<AreaBase> subs;
            if (!AreaSubDic.TryGetValue(city, out subs))
            {
                subs = new List<AreaBase>();
                AreaSubDic[city] = subs;
            }
            if (!subs.Contains(country))
            {
                subs.Add(country);
            }

            if (!AreaSubDic.TryGetValue(country, out subs))
            {
                subs = new List<AreaBase>();
                AreaSubDic[country] = subs;
            }
            if (!subs.Contains(town))
            {
                subs.Add(town);
            }

            AreaSubDic[town] = villages;

            TownLeafDic[town] = villages;
        }

        public void RemoveArea(AreaBase area)
        {
            removeArea(area);
            Dictionary<AreaBase, List<AreaBase>> townDic = new Dictionary<AreaBase, List<AreaBase>>();
            foreach (AreaBase town in AreaSubDic.Keys)
            {
                if (town.RankName == "乡镇")
                {
                    townDic[town] = AreaSubDic[town];
                }
            }
            TownLeafDic.Clear();
            AreaSubDic.Clear();

            foreach (AreaBase town in townDic.Keys)
            {
                AreaBase country = town.ParentArea;
                AreaBase city = country.ParentArea;
                AddArea(city, country, town, townDic[town]);
            }
        }

        public Dictionary<string, object> GetAreaCondDic()
        {
            Dictionary<string, object> rtDic = new Dictionary<string, object>();
            foreach (AreaBase city in RootLeafDic.Keys)
            {
                List<object> oVillVec = new List<object>();
                foreach (AreaBase vill in RootLeafDic[city])
                {
                    oVillVec.Add(string.Join("", vill.GetAreaPath()));
                }
                rtDic[city.Name] = oVillVec;
            }
            return rtDic;
        }

        public void ParseAreaDic(Dictionary<string, object> areaDic)
        {
            int villRank = ZTAreaManager.Instance.Ranks.Count;
            List<AreaBase> villages = ZTAreaManager.Instance.GetArea(ZTAreaManager.Instance.Ranks[villRank - 1]);
            List<string> villVec = new List<string>();
            Dictionary<AreaBase, List<AreaBase>> townVillDic = new Dictionary<AreaBase, List<AreaBase>>();
            foreach (string city in areaDic.Keys)
            {
                foreach (object vill in areaDic[city] as List<object>)
                {
                    villVec.Add(vill.ToString());
                }
            }
            foreach (AreaBase area in villages)
            {
                if (villVec.Contains(string.Join("", area.GetAreaPath())))
                {
                    AreaBase town = area.ParentArea;
                    List<AreaBase> rtVillVec;
                    if (!townVillDic.TryGetValue(town, out rtVillVec))
                    {
                        rtVillVec = new List<AreaBase>();
                        townVillDic[town] = rtVillVec;
                    }
                    rtVillVec.Add(area);
                }
            }
            addTownVillDic(townVillDic);
        }

        private void addTownVillDic(Dictionary<AreaBase, List<AreaBase>> townVillDic)
        {
            foreach (AreaBase town in townVillDic.Keys)
            {
                AreaBase country = town.ParentArea;
                AreaBase city = country.ParentArea;

                AddArea(city, country, town, townVillDic[town]);
            }
        }
    }
}
