using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class WeakRxqualitySetForm : DevExpress.XtraEditors.XtraForm
    {
        public WeakRxqualitySetForm()
        {
            InitializeComponent();
        }

        public float RxlevThreShold
        {
            get { return (float)spinEditRxlevMean.Value; }
        }

        public int RxqualThrShold
        {
            get { return (int)spinEditRxqual.Value; }
        }

        public double RoadStru
        {
            get { return (double)spinEditStru.Value; }
        }

        public int RoadStruPercent
        {
            get { return (int)spinEditStruPercent.Value; }
        }

        public int TotalCount900
        {
            get { return (int)spinEditTotal900.Value; }
        }

        public int TotalCount1800
        {
            get { return (int)spinEditTotal1800.Value; }
        }

        private void simpleButtonOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void simpleButtonCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }
    }
}