﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Xml.Serialization;

namespace MasterCom.RAMS.ZTFunc
{
    /// <summary>
    /// 指标门限值详细信息
    /// </summary>
    [XmlRoot("指标门限值详细信息")]
    public class IndicatorThresholdInf
    {
        /// <summary>
        /// 指标名称
        /// </summary>
        [XmlElement("指标名称")]
        public string Name { get; set; }
        /// <summary>
        /// 门限
        /// </summary>
        [XmlElement("门限")]
        public double ThreadHoldData { get; set; }
        /// <summary>
        /// 满分指标
        /// </summary>
        [XmlElement("满分指标")]
        public double FullData { get; set; }
        /// <summary>
        /// 总分分值
        /// </summary>
        [XmlElement("总分分值")]
        public double FullScore { get; set; }
        /// <summary>
        /// 0分指标
        /// </summary>
        [XmlElement("零分指标")]
        public double ZeroData { get; set; }
        /// <summary>
        /// 门限指标分
        /// </summary>
        [XmlElement("门限指标分")]
        public double ThreadHoldScore { get; set; }

        public IndicatorThresholdInf(string name)
        {
            Name = name;
            FullScore = 0;
            ZeroData = 0;
            ThreadHoldScore = 0;
            FullData = 0;
            ThreadHoldData = 0;
        }

        public IndicatorThresholdInf()
        {
            Name = "";
            FullScore = 0;
            ZeroData = 0;
            ThreadHoldScore = 0;
            FullData = 0;
            ThreadHoldData = 0;
        }
    }
}
