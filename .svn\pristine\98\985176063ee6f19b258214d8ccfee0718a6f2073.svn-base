﻿using MasterCom.MTGis;
using MasterCom.RAMS.ExMap;
using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Text;
using System.Xml;

namespace MasterCom.RAMS.Func
{
    public class MapWCellLayer : LayerBase, IKMLExport
    {
        public static bool DrawCurrent { get; set; } = true;
        public static DateTime CurShowTimeAt { get; set; } = DateTime.Now;

        static MapWCellLayer()
        {
            creatGraphicsPath();
        }

        protected static void creatGraphicsPath()
        {
            float radius = 6;
            cellPoints.Add(new PointF[] { new PointF(-radius, -radius), new PointF(radius, -radius), new PointF(radius, radius), new PointF(-radius, radius) });
            radius = 50;
            float x1 = 0.75f;
            float y1 = 0.12f;
            float x2 = 0.85f;
            float y2 = 0.13f;
            float x3 = 0.92f;
            float y3 = 0.14f;
            float x4 = 0.96f;
            float y4 = 0.11f;
            float x5 = 0.99f;
            float y5 = 0.06f;
            cellPoints.Add(new PointF[] 
                {
                    new PointF(0, 0), 
                    new PointF(radius * x1, -radius * y1), 
                    new PointF(radius * x2, -radius * y2), 
                    new PointF(radius * x3, -radius * y3), 
                    new PointF(radius * x4, -radius * y4), 
                    new PointF(radius * x5, -radius * y5), 
                    new PointF(radius, 0),
                    new PointF(radius * x5, radius * y5), 
                    new PointF(radius * x4, radius * y4), 
                    new PointF(radius * x3, radius * y3), 
                    new PointF(radius * x2, radius * y2), 
                    new PointF(radius * x1, radius * y1)
                });
            GraphicsPath path = new GraphicsPath();
            path.AddEllipse(cellPoints[0][0].X, cellPoints[0][0].Y, cellPoints[0][2].X * 2, cellPoints[0][2].Y * 2);
            cellPaths.Add(path);
            path = new GraphicsPath();
            path.AddPolygon(cellPoints[1]);
            cellPaths.Add(path);
            foreach (GraphicsPath pathTemp in cellPaths)
            {
                PathGradientBrush pathGradientBrush = new PathGradientBrush(pathTemp);
                pathGradientBrush.CenterPoint = new PointF(0, 0);
                cellPathGradientBrushs.Add(pathGradientBrush);
            }
            radius = 6;
            antennaPoints.Add(new PointF[] { new PointF(-radius, -radius), new PointF(radius, -radius), new PointF(radius, radius), new PointF(-radius, radius) });
            radius = 50;
            antennaPoints.Add(new PointF[] 
                {
                    new PointF(0, -1), 
                    new PointF(radius - 3, -1), 
                    new PointF(radius - 5, -5), 
                    new PointF(radius, 0), 
                    new PointF(radius - 5, 5), 
                    new PointF(radius - 3, 1), 
                    new PointF(0, 1), 
                });
            path = new GraphicsPath();
            path.AddEllipse(antennaPoints[0][0].X, antennaPoints[0][0].Y, antennaPoints[0][2].X * 2, antennaPoints[0][2].Y * 2);
            antennaPaths.Add(path);
            path = new GraphicsPath();
            path.AddPolygon(antennaPoints[1]);
            antennaPaths.Add(path);

            double radiusGE = 0.00004;
            int part = 36;//将一个圆分为几份
            antennaGEPoints = GEdrawCircle(radiusGE, part, 0);

            radiusGE = 0.00050;
            antennaGEPoints.Add(new LayerPoint(0, -0.00001, 1));
            antennaGEPoints.Add(new LayerPoint(radiusGE - 0.00003, -0.00001, 1));
            antennaGEPoints.Add(new LayerPoint(radiusGE - 0.00005, -0.00005, 1));
            antennaGEPoints.Add(new LayerPoint(radiusGE, 0, 1));
            antennaGEPoints.Add(new LayerPoint(radiusGE - 0.00005, 0.00005, 1));
            antennaGEPoints.Add(new LayerPoint(radiusGE - 0.00003, 0.00001, 1));
            antennaGEPoints.Add(new LayerPoint(0, 0.00001, 1));
            antennaGEPoints.Add(new LayerPoint(0, -0.00001, 1));
        }

        private static List<PointF[]> cellHighlightPoints = new List<PointF[]>();
        public List<PointF[]> CellHighlightPoints
        {
            get { return cellHighlightPoints; }
        }

        private static List<GraphicsPath> cellHighlightPaths = new List<GraphicsPath>();
        public List<GraphicsPath> CellHighlightPaths
        {
            get { return cellHighlightPaths; }
        }

        private static List<PathGradientBrush> cellHighlightPathGradientBrushs = new List<PathGradientBrush>();
        public List<PathGradientBrush> CellHighlightPathGradientBrushs
        {
            get { return cellHighlightPathGradientBrushs; }
        }

        private static List<PointF[]> antennaHighlightPoints = new List<PointF[]>();
        public List<PointF[]> AntennaHighlightPoints
        {
            get { return antennaHighlightPoints; }
        }
        
        public static List<LayerPoint> AntennaGEHighlightPoints { get; private set; } = new List<LayerPoint>();

        private static List<GraphicsPath> antennaHighlightPaths = new List<GraphicsPath>();
        public List<GraphicsPath> AntennaHighlightPaths
        {
            get { return antennaHighlightPaths; }
        }

        public void InitCellPath()
        {
            cellHighlightPoints.Clear();
            cellHighlightPaths.Clear();
            cellHighlightPathGradientBrushs.Clear();
            antennaHighlightPoints.Clear();
            AntennaGEHighlightPoints.Clear();
            antennaHighlightPaths.Clear();
            float radius = 6 * CD.CellLengthRadio_W;
            cellHighlightPoints.Add(new PointF[] { new PointF(-radius, -radius), new PointF(radius, -radius), new PointF(radius, radius), new PointF(-radius, radius) });
            radius = 40 * CD.CellLengthRadio_W;
            float x1 = 0.75f;
            float y1 = 0.12f;
            float x2 = 0.85f;
            float y2 = 0.13f;
            float x3 = 0.92f;
            float y3 = 0.14f;
            float x4 = 0.96f;
            float y4 = 0.11f;
            float x5 = 0.99f;
            float y5 = 0.06f;
            cellHighlightPoints.Add(new PointF[] 
                {
                    new PointF(0, 0), 
                    new PointF(radius * x1, -radius * y1), 
                    new PointF(radius * x2, -radius * y2), 
                    new PointF(radius * x3, -radius * y3), 
                    new PointF(radius * x4, -radius * y4), 
                    new PointF(radius * x5, -radius * y5), 
                    new PointF(radius, 0),
                    new PointF(radius * x5, radius * y5), 
                    new PointF(radius * x4, radius * y4), 
                    new PointF(radius * x3, radius * y3), 
                    new PointF(radius * x2, radius * y2), 
                    new PointF(radius * x1, radius * y1)
                });
            GraphicsPath path = new GraphicsPath();
            path.AddEllipse(cellHighlightPoints[0][0].X, cellHighlightPoints[0][0].Y, cellHighlightPoints[0][2].X * 2, cellHighlightPoints[0][2].Y * 2);
            cellHighlightPaths.Add(path);
            path = new GraphicsPath();
            path.AddPolygon(cellPoints[1]);
            cellHighlightPaths.Add(path);
            foreach (GraphicsPath pathTemp in cellHighlightPaths)
            {
                PathGradientBrush pathGradientBrush = new PathGradientBrush(pathTemp);
                pathGradientBrush.CenterPoint = new PointF(0, 0);
                cellHighlightPathGradientBrushs.Add(pathGradientBrush);
            }

            radius = 6 * CD.CellLengthRadio_W;
            antennaHighlightPoints.Add(new PointF[] { new PointF(-radius, -radius), new PointF(radius, -radius), new PointF(radius, radius), new PointF(-radius, radius) });
            radius = 40 * CD.CellLengthRadio_W;
            antennaHighlightPoints.Add(new PointF[] 
                {
                    new PointF(0, -1), 
                    new PointF(radius - 3, -1), 
                    new PointF(radius - 5, -5), 
                    new PointF(radius, 0), 
                    new PointF(radius - 5, 5), 
                    new PointF(radius - 3, 1), 
                    new PointF(0, 1), 
                });
            path = new GraphicsPath();
            path.AddEllipse(antennaHighlightPoints[0][0].X, antennaHighlightPoints[0][0].Y, antennaHighlightPoints[0][2].X * 2, antennaHighlightPoints[0][2].Y * 2);
            antennaHighlightPaths.Add(path);
            path = new GraphicsPath();
            path.AddPolygon(antennaHighlightPoints[1]);
            antennaHighlightPaths.Add(path);

            double radiusGE = 0.00004 * CD.CellLengthRadio_W;
            int part = 36;//将一个圆分为几份
            AntennaGEHighlightPoints = GEdrawCircle(radiusGE, part, 0);

            radiusGE = 0.0005 * CD.CellLengthRadio_W;
            AntennaGEHighlightPoints.Add(new LayerPoint(0, -0.00001, 1));
            AntennaGEHighlightPoints.Add(new LayerPoint(radiusGE - 0.00003, -0.00001, 1));
            AntennaGEHighlightPoints.Add(new LayerPoint(radiusGE - 0.00005, -0.00005, 1));
            AntennaGEHighlightPoints.Add(new LayerPoint(radiusGE, 0, 1));
            AntennaGEHighlightPoints.Add(new LayerPoint(radiusGE - 0.00005, 0.00005, 1));
            AntennaGEHighlightPoints.Add(new LayerPoint(radiusGE - 0.00003, 0.00001, 1));
            AntennaGEHighlightPoints.Add(new LayerPoint(0, 0.00001, 1));
            AntennaGEHighlightPoints.Add(new LayerPoint(0, -0.00001, 1));
        }

        public MapWCellLayer(string name)
            : base(name)
        {
            this.VisibleScaleEnabled = true;
            this.VisibleScale = new VisibleScale(0, 80000);
        }

        public override MainModel MainModel
        {
            get { return mainModel; }
            set
            {
                mainModel = value;
                mainModel.ServerCellsChanged += serverCellsChanged;
                serverCellsChanged(null, null);
            }
        }

        public override void Draw(System.Drawing.Rectangle clientRect, System.Drawing.Rectangle updateRect, Graphics graphics)
        {
            updateRect.Inflate((int)(40 * 10000 / mapScale), (int)(40 * 10000 / mapScale));
            DbRect dRect;
            gisAdapter.FromDisplay(updateRect, out dRect);
            graphics.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.HighQuality;
            if (DrawCell)
            {
                ReadOnlyCollection<WCell> cells = null;
                if (DrawCurrent)
                {
                    cells = mainModel.CellManager.GetCurrentWCellsReadOnly();
                }
                else
                {
                    cells = mainModel.CellManager.GetWCellsReadOnly(CurShowTimeAt);
                }

                if (cells != null)
                {
                    foreach (WCell cell in cells)
                    {
                        if (cell.Within(dRect.x1, dRect.y1, dRect.x2, dRect.y2)
                            && ((cell.Type == WNodeBType.Outdoor && DrawOutdoor) || (cell.Type == WNodeBType.Indoor && DrawIndoor)))
                        {
                            paintCell(cell, graphics);
                        }
                    }
                }
                if (DrawServer)
                {
                    cells = mainModel.ServerWCells.AsReadOnly();
                }
                if (cells != null)
                {
                    foreach (WCell cell in cells)
                    {
                        if (cell.Within(dRect.x1, dRect.y1, dRect.x2, dRect.y2)
                            && ((cell.Type == WNodeBType.Outdoor && DrawOutdoor) || (cell.Type == WNodeBType.Indoor && DrawIndoor)))
                        {
                            paintCell(cell, graphics);
                        }
                    }
                }
            }
            if (DrawAntenna)
            {
                ReadOnlyCollection<WAntenna> antennas = null;
                if (DrawCurrent)
                {
                    antennas = mainModel.CellManager.GetCurrentWAntennas();
                }
                else
                {
                    antennas = mainModel.CellManager.GetWAntennas(CurShowTimeAt);
                }
                if (antennas != null)
                {
                    foreach (WAntenna antenna in antennas)
                    {
                        //避免GIS上同时显示不同时间的工参
                        if (curMapType == LayerMapType.MTGis && !MainModel.SystemConfigInfo.isDisplayHisCell
                            && !antenna.ValidPeriod.Contains(DateTime.Now))
                        {
                            continue;
                        }

                        if (antenna.Within(dRect.x1, dRect.y1, dRect.x2, dRect.y2)
                            && ((antenna.Type == WNodeBType.Outdoor && DrawOutdoor) || (antenna.Type == WNodeBType.Indoor && DrawIndoor)))
                        {
                            paintAntenna(antenna, graphics, mapScale);
                        }
                    }
                }
                List<WCell> cells = null;
                if (DrawServer)
                {
                    cells = mainModel.ServerWCells;
                }
                if (cells != null)
                {
                    foreach (WCell cell in cells)
                    {
                        antennas = cell.Antennas;
                        if (antennas != null)
                        {
                            foreach (WAntenna antenna in antennas)
                            {
                                if (curMapType == LayerMapType.MTGis && !MainModel.SystemConfigInfo.isDisplayHisCell
                                    && !antenna.ValidPeriod.Contains(DateTime.Now))
                                {
                                    continue;
                                }

                                if (antenna.Within(dRect.x1, dRect.y1, dRect.x2, dRect.y2)
                                    && ((antenna.Type == WNodeBType.Outdoor && DrawOutdoor) || (antenna.Type == WNodeBType.Indoor && DrawIndoor)))
                                {
                                    paintAntenna(antenna, graphics, mapScale);
                                }
                            }
                        }
                    }
                }
            }

            if (DrawBTS)
            {
                ReadOnlyCollection<WNodeB> btss = null;
                if (DrawCurrent)
                {
                    btss = mainModel.CellManager.GetCurrentWNodeBs();
                }
                else
                {
                    btss = mainModel.CellManager.GetWNodeBs(CurShowTimeAt);
                }
                if (btss != null)
                {
                    foreach (WNodeB bts in btss)
                    {
                        if (bts.Within(dRect.x1, dRect.y1, dRect.x2, dRect.y2)
                             && ((bts.Type == WNodeBType.Outdoor && DrawOutdoor) || (bts.Type == WNodeBType.Indoor && DrawIndoor)))
                        {
                            paintBTS(bts, graphics);
                        }
                    }
                }
            }
            if (DrawBTSLabel && mapScale < 50000)
            {
                ReadOnlyCollection<WNodeB> btss = null;
                if (DrawCurrent)
                {
                    btss = mainModel.CellManager.GetCurrentWNodeBs();
                }
                else
                {
                    btss = mainModel.CellManager.GetWNodeBs(CurShowTimeAt);
                }
                if (btss != null)
                {
                    drawedBTSLabelRectangles.Clear();
                    foreach (WNodeB bts in btss)
                    {
                        if (bts.Within(dRect.x1, dRect.y1, dRect.x2, dRect.y2)
                            && ((bts.Type == WNodeBType.Outdoor && DrawOutdoor) || (bts.Type == WNodeBType.Indoor && DrawIndoor)))
                        {
                            paintBTSLabel(bts, graphics);
                        }
                    }
                }
            }
            if (DrawCellLabel && mapScale < 50000)
            {
                ReadOnlyCollection<WCell> cells = null;
                if (DrawCurrent)
                {
                    cells = mainModel.CellManager.GetCurrentWCellsReadOnly();
                }
                else
                {
                    cells = mainModel.CellManager.GetWCellsReadOnly(CurShowTimeAt);
                }
                if (cells != null)
                {
                    drawedCellLabelRectangles.Clear();
                    foreach (WCell cell in cells)
                    {
                        if (cell.Within(dRect.x1, dRect.y1, dRect.x2, dRect.y2)
                            && ((cell.Type == WNodeBType.Outdoor && DrawOutdoor) || (cell.Type == WNodeBType.Indoor && DrawIndoor)))
                        {
                            paintCellLabel(cell, graphics);
                        }
                    }
                }
            }
            if (DrawAntennaLabel && mapScale < 50000)
            {
                ReadOnlyCollection<WAntenna> antennas = null;
                if (DrawCurrent)
                {
                    antennas = mainModel.CellManager.GetCurrentWAntennas();
                }
                else
                {
                    antennas = mainModel.CellManager.GetWAntennas(CurShowTimeAt);
                }
                if (antennas != null)
                {
                    drawedAntennaLabelRectangles.Clear();
                    foreach (WAntenna antenna in antennas)
                    {
                        if (antenna.Within(dRect.x1, dRect.y1, dRect.x2, dRect.y2)
                            && ((antenna.Type == WNodeBType.Outdoor && DrawOutdoor) || (antenna.Type == WNodeBType.Indoor && DrawIndoor)))
                        {
                            paintAntennaLabel(antenna, graphics);
                        }
                    }
                }
            }

            if (curMapType == LayerMapType.MTGis && mainModel.ClusterAnalysisSet.JamWCellOnGIS != null
                && mainModel.ClusterAnalysisSet.OrgWCellsOnGIS.Count > 0
                && mainModel.ClusterAnalysisSet.JamWCellOnGIS.Antennas.Count > 0)
            {
                foreach (WCell orgCell in mainModel.ClusterAnalysisSet.OrgWCellsOnGIS)
                {
                    if (orgCell.Antennas.Count > 0)
                    {
                        int jamCellCount = mainModel.ClusterAnalysisSet.JamWCellOnGIS.Antennas.Count;
                        int orgCellCount = orgCell.Antennas.Count;
                        DbPoint jamDPoint = new DbPoint(mainModel.ClusterAnalysisSet.JamWCellOnGIS.Antennas[jamCellCount - 1].EndPointLongitude, mainModel.ClusterAnalysisSet.JamWCellOnGIS.Antennas[jamCellCount - 1].EndPointLatitude);
                        PointF jamPointF;
                        gisAdapter.ToDisplay(jamDPoint, out jamPointF);

                        DbPoint orgDPoint = new DbPoint(orgCell.Antennas[orgCellCount - 1].EndPointLongitude, orgCell.Antennas[orgCellCount - 1].EndPointLatitude);
                        PointF orgPointF;
                        gisAdapter.ToDisplay(orgDPoint, out orgPointF);

                        graphics.DrawLine(new Pen(new SolidBrush(Color.Magenta), 2), jamPointF, orgPointF);
                    }
                }
            }
        }

        public override void mapForm_MapFeatureSelecting(object sender, EventArgs e)
        {
            MapForm mf = sender as MapForm;
            Select(((MapForm.MapEventArgs)e).MapOp2, mf.WNodeBs, mf.WCells, mf.WAntennas);
        }

        public void Select(MapOperation2 mop2, List<WNodeB> selectedBTSs, List<WCell> selectedCells, List<WAntenna> selectedAntennas)
        {
            if (IsVisible)
            {
                DbRect dRect = mop2.GetRegion().Bounds;
                if (DrawBTS)
                {
                    ReadOnlyCollection<WNodeB> btss = null;
                    if (DrawCurrent)
                    {
                        btss = mainModel.CellManager.GetCurrentWNodeBs();
                    }
                    else
                    {
                        btss = mainModel.CellManager.GetWNodeBs(CurShowTimeAt);
                    }
                    if (btss != null)
                    {
                        foreach (WNodeB bts in btss)
                        {
                            if (bts.Within(dRect.x1 - 0.01, dRect.y1 - 0.01, dRect.x2 + 0.01, dRect.y2 + 0.01)
                                && ((bts.Type == WNodeBType.Outdoor && DrawOutdoor) || (bts.Type == WNodeBType.Indoor && DrawIndoor)))
                            {
                                WNodeB selectedBTS = selectBTS(bts, mop2);
                                if (selectedBTS != null)
                                {
                                    selectedBTSs.Add(selectedBTS);
                                }
                            }
                        }
                    }
                }

                if (DrawAntenna)
                {
                    ReadOnlyCollection<WAntenna> antennas = null;
                    List<WCell> cells = null;
                    if (DrawServer)
                    {
                        cells = mainModel.ServerWCells;
                    }
                    if (cells != null)
                    {
                        foreach (WCell cell in cells)
                        {
                            antennas = cell.Antennas;
                            if (antennas != null)
                            {
                                foreach (WAntenna antenna in antennas)
                                {
                                    if (antenna.Within(dRect.x1 - 0.1, dRect.y1 - 0.1, dRect.x2 + 0.1, dRect.y2 + 0.1))
                                    {
                                        WAntenna selectedAntenna = selectAntenna(antenna, mop2, mapScale);
                                        if (selectedAntenna != null)
                                        {
                                            selectedAntennas.Add(selectedAntenna);
                                        }
                                    }
                                }
                            }
                        }
                    }
                    if (DrawCurrent)
                    {
                        antennas = mainModel.CellManager.GetCurrentWAntennas();
                    }
                    else
                    {
                        antennas = mainModel.CellManager.GetWAntennas(CurShowTimeAt);
                    }
                    if (antennas != null)
                    {
                        foreach (WAntenna antenna in antennas)
                        {
                            if (antenna.Within(dRect.x1 - 0.1, dRect.y1 - 0.1, dRect.x2 + 0.1, dRect.y2 + 0.1))
                            {
                                WAntenna selectedAntenna = selectAntenna(antenna, mop2, mapScale);
                                if (selectedAntenna != null && !selectedAntennas.Contains(selectedAntenna))
                                {
                                    selectedAntennas.Add(selectedAntenna);
                                }
                            }
                        }
                    }
                }
                if (DrawCell)
                {
                    ReadOnlyCollection<WCell> cells = null;
                    if (DrawServer)
                    {
                        cells = mainModel.ServerWCells.AsReadOnly();
                    }
                    if (cells != null)
                    {
                        foreach (WCell cell in cells)
                        {
                            if (cell.Within(dRect.x1 - 0.1, dRect.y1 - 0.1, dRect.x2 + 0.1, dRect.y2 + 0.1))
                            {
                                WCell selectedCell = selectCell(cell, mop2, mapScale);
                                if (selectedCell != null)
                                {
                                    selectedCells.Add(selectedCell);
                                }
                            }
                        }
                    }
                    if (DrawCurrent)
                    {
                        cells = mainModel.CellManager.GetCurrentWCellsReadOnly();
                    }
                    else
                    {
                        cells = mainModel.CellManager.GetWCellsReadOnly(CurShowTimeAt);
                    }
                    if (cells != null)
                    {
                        foreach (WCell cell in cells)
                        {
                            if (cell.Within(dRect.x1 - 0.1, dRect.y1 - 0.1, dRect.x2 + 0.1, dRect.y2 + 0.1))
                            {
                                WCell selectedCell = selectCell(cell, mop2, mapScale);
                                if (selectedCell != null && !selectedCells.Contains(selectedCell))
                                {
                                    selectedCells.Add(selectedCell);
                                }
                            }
                        }
                    }
                }
            }
        }

        public WCell SelectCell(MapOperation2 mop2)
        {
            if (IsVisible)
            {
                DbRect dRect = mop2.GetRegion().Bounds;
                if (DrawAntenna && mapScale < 100000)
                {
                    ReadOnlyCollection<WAntenna> antennas = null;
                    List<WCell> cells = null;
                    if (DrawServer)
                    {
                        cells = mainModel.ServerWCells;
                    }
                    if (cells != null)
                    {
                        foreach (WCell cell in cells)
                        {
                            antennas = cell.Antennas;
                            if (antennas != null)
                            {
                                foreach (WAntenna antenna in antennas)
                                {
                                    if (antenna.Within(dRect.x1 - 0.01, dRect.y1 - 0.01, dRect.x2 + 0.01, dRect.y2 + 0.01))
                                    {
                                        WAntenna selectedAntenna = selectAntenna(antenna, mop2, mapScale);
                                        if (selectedAntenna != null)
                                        {
                                            return cell;
                                        }
                                    }
                                }
                            }
                        }
                    }
                    if (DrawCurrent)
                    {
                        antennas = mainModel.CellManager.GetCurrentWAntennas();
                    }
                    else
                    {
                        antennas = mainModel.CellManager.GetWAntennas(CurShowTimeAt);
                    }
                    if (antennas != null)
                    {
                        foreach (WAntenna antenna in antennas)
                        {
                            if (antenna.Within(dRect.x1 - 0.01, dRect.y1 - 0.01, dRect.x2 + 0.01, dRect.y2 + 0.01))
                            {
                                WAntenna selectedAntenna = selectAntenna(antenna, mop2, mapScale);
                                if (selectedAntenna != null && DrawCurrent)
                                {
                                    int count = selectedAntenna.BelongCells.Count;
                                    for (int i = 0; i < count; i++)
                                    {
                                        WCell cellv = selectedAntenna.BelongCells[i].Current;
                                        if (cellv != null)
                                        {
                                            return cellv;
                                        }
                                    }
                                    return null;
                                }
                            }
                        }
                    }
                }
                if (DrawCell)
                {
                    ReadOnlyCollection<WCell> cells = null;
                    if (DrawServer)
                    {
                        cells = mainModel.ServerWCells.AsReadOnly();
                    }
                    if (cells != null)
                    {
                        foreach (WCell cell in cells)
                        {
                            if (cell.Within(dRect.x1 - 0.01, dRect.y1 - 0.01, dRect.x2 + 0.01, dRect.y2 + 0.01))
                            {
                                WCell selectedCell = selectCell(cell, mop2, mapScale);
                                if (selectedCell != null)
                                {
                                    return selectedCell;
                                }
                            }
                        }
                    }
                    if (DrawCurrent)
                    {
                        cells = mainModel.CellManager.GetCurrentWCellsReadOnly();
                    }
                    else
                    {
                        cells = mainModel.CellManager.GetWCellsReadOnly(CurShowTimeAt);
                    }
                    if (cells != null)
                    {
                        foreach (WCell cell in cells)
                        {
                            if (cell.Within(dRect.x1 - 0.01, dRect.y1 - 0.01, dRect.x2 + 0.01, dRect.y2 + 0.01))
                            {
                                WCell selectedCell = selectCell(cell, mop2, mapScale);
                                if (selectedCell != null)
                                {
                                    return selectedCell;
                                }
                            }
                        }
                    }
                }
            }
            return null;
        }

        private void paintCell(WCell cell, Graphics graphics)
        {
            DbPoint dPoint = new DbPoint(cell.Longitude, cell.Latitude);
            PointF point;
            gisAdapter.ToDisplay(dPoint, out point);
            Brush brush = brushCell;
            int index = 0;

            if (cell.DirectionType != AntennaDirectionType.Omni)
            {
                index = 1;
            }
            brush = getServerCellBrush(cell, brush);

            bool alarmCell = false;
            if (DrawAlarm && mainModel.OutServiceInfoManager.isContainSnapShot(MasterCom.RAMS.Func.OutServiceInfo.NetType.WCDMA, cell))
            {
                brush = brushAlarmCell;
                alarmCell = true;
            }

            List<GraphicsPath> paths = getCellPaths(cell);

            graphics.TranslateTransform(point.X, point.Y);
            graphics.RotateTransform(cell.Direction - 90);

            graphics.ScaleTransform((float)(10000 / mapScale), (float)(10000 / mapScale));
            if (alarmCell)
            {
                graphics.ScaleTransform(ZoomScaleAlarm, ZoomScaleAlarm);
            }
            graphics.FillPath(brush, paths[index]);
            if (mainModel.SelectedWCell == cell)
            {
                graphics.DrawPath(penSelected, paths[index]);
            }
            graphics.ResetTransform();
        }

        private Brush getServerCellBrush(WCell cell, Brush brush)
        {
            if (DrawServer)
            {
                for (int i = 0; i < mainModel.ServerWCells.Count; i++)
                {
                    if (brushesServer.Count < mainModel.ServerWCells.Count) break;
                    if (mainModel.ServerWCells[i] == cell)
                    {
                        brush = brushesServer[i];
                        break;
                    }
                }
            }

            return brush;
        }

        private List<GraphicsPath> getCellPaths(WCell cell)
        {
            List<GraphicsPath> paths;
            if (mainModel.CellHighLightAll)
            {
                paths = cellHighlightPaths;
            }
            else
            {
                if (mainModel.CellHighlightList_W.Contains(cell))
                {
                    paths = cellHighlightPaths;
                }
                else
                {
                    paths = cellPaths;
                }
            }

            return paths;
        }

        private void paintCellLabel(WCell cell, Graphics graphics)
        {
            DbPoint dPoint = new DbPoint(cell.EndPointLongitude, cell.EndPointLatitude);
            PointF point;
            gisAdapter.ToDisplay(dPoint, out point);
            graphics.TranslateTransform(point.X, point.Y);
            string cellDes = GetCellLabelDes(cell, 100);
            SizeF size = graphics.MeasureString(cellDes, FontCellLabel);
            size.Height *= 0.8f;
            System.Drawing.Rectangle rectangle = new System.Drawing.Rectangle((int)point.X - 3, (int)(point.Y - (size.Height / 2 - 5)), (int)size.Width + 10, (int)size.Height + 10);
            bool needDraw = true;
            foreach (System.Drawing.Rectangle rectangleTemp in drawedCellLabelRectangles)
            {
                if (rectangle.Right >= rectangleTemp.Left && rectangle.Left <= rectangleTemp.Right
                    && rectangle.Bottom >= rectangleTemp.Top && rectangle.Top <= rectangleTemp.Bottom)
                {
                    needDraw = false;
                    break;
                }
            }
            if (needDraw)
            {
                graphics.FillRectangle(Brushes.White, 3, -size.Height / 2, size.Width, size.Height);
                graphics.DrawString(cellDes, FontCellLabel, Brushes.Black, 3, -size.Height / 2);
                drawedCellLabelRectangles.Add(rectangle);
            }
            graphics.ResetTransform();
        }

        public string GetCellLabelDes(WCell cell, int length)
        {
            string des = "";
            if (DrawCellName)
            {
                des += cell.Name + " ";
            }
            if (DrawCellCode)
            {
                des += cell.Code + " ";
            }
            if (DrawCellLAC)
            {
                des += cell.LAC.ToString() + " ";
            }
            if (DrawCellCI)
            {
                des += cell.CI.ToString() + " ";
            }
            if (DrawCellUARFCN)
            {
                des += cell.UARFCN.ToString() + " ";
            }
            if (DrawCellPSC)
            {
                des += cell.PSC.ToString() + " ";
            }
            if (DrawCellDes)
            {
                des += cell.Description + " ";
            }
            if (des.Length > length)
            {
                des = des.Substring(0, length);
                des += "...";
            }
            return des;
        }

        private readonly List<System.Drawing.Rectangle> drawedCellLabelRectangles = new List<System.Drawing.Rectangle>();
        public List<System.Drawing.Rectangle> DrawedCellLabelRectangles
        {
            get { return drawedCellLabelRectangles; }
        }

        private void paintAntenna(WAntenna antenna, Graphics graphics, double scale)
        {
            DbPoint dPoint = new DbPoint(antenna.Longitude, antenna.Latitude);
            PointF point;
            gisAdapter.ToDisplay(dPoint, out point);
            Brush brush = brushAntenna;
            Pen pen = penAntenna;
            int index = 1;
            bool isneighbour = false;
            if (antenna.DirectionType == AntennaDirectionType.Omni)
            {
                index = 0;
            }
            brush = getServerAntennaBrush(antenna, brush);
            setNeighbourAntenna(antenna, ref brush, ref pen, ref isneighbour);

            bool alarmCell = getAlarmCell(antenna, ref brush);

            List<GraphicsPath> paths = getAntennaPaths(antenna);

            graphics.TranslateTransform(point.X, point.Y);
            graphics.RotateTransform(antenna.Direction - 90);
            graphics.ScaleTransform((float)(10000 / scale), (float)(10000 / scale));
            if (alarmCell)
            {
                graphics.ScaleTransform(ZoomScaleAlarm, ZoomScaleAlarm);
            }
            if (index == 0)
            {
                graphics.DrawPath(pen, paths[index]);
                if (isneighbour)
                {
                    graphics.DrawPath(new Pen(brushWNeighbourWCell, 5), paths[index]);
                }
            }
            else
            {
                graphics.FillPath(brush, paths[index]);
                if (isneighbour)
                {
                    graphics.DrawPath(new Pen(brushWNeighbourWCell, 5), paths[index]);
                }
            }
            if (antenna.BelongCells.Contains(mainModel.SelectedWCell))
            {
                graphics.DrawPath(penSelected, paths[index]);
            }
            graphics.ResetTransform();
        }

        private Brush getServerAntennaBrush(WAntenna antenna, Brush brush)
        {
            if (DrawServer)
            {
                for (int i = 0; i < mainModel.ServerWCells.Count; i++)
                {
                    if (brushesServer.Count < mainModel.ServerWCells.Count) break;
                    if (antenna.BelongCells.Contains(mainModel.ServerWCells[i]))
                    {
                        brush = brushesServer[i];
                        break;
                    }
                }
            }

            return brush;
        }

        private void setNeighbourAntenna(WAntenna antenna, ref Brush brush, ref Pen pen, ref bool isneighbour)
        {
            foreach (WCell cell in mainModel.WNeighbourWCells)
            {
                if (antenna.BelongCells.Contains(cell))
                {
                    brush = brushWNeighbourWCell;
                    pen = new Pen(brush, 2);
                    isneighbour = true;
                    break;
                }
            }
        }

        private bool getAlarmCell(WAntenna antenna, ref Brush brush)
        {
            bool alarmCell = false;
            if (DrawAlarm)
            {
                foreach (WCell cell in antenna.BelongCells)
                {
                    if (mainModel.OutServiceInfoManager.isContainSnapShot(MasterCom.RAMS.Func.OutServiceInfo.NetType.WCDMA, cell))
                    {
                        brush = brushAlarm;
                        alarmCell = true;
                        break;
                    }
                }
            }

            return alarmCell;
        }

        private List<GraphicsPath> getAntennaPaths(WAntenna antenna)
        {
            List<GraphicsPath> paths;
            if (mainModel.CellHighLightAll)
            {
                paths = antennaHighlightPaths;
            }
            else
            {
                paths = antennaPaths;
                foreach (WCell cell in mainModel.CellHighlightList_W)
                {
                    if (cell.Antennas.Contains(antenna))
                    {
                        paths = antennaHighlightPaths;
                        break;
                    }
                }
            }

            return paths;
        }

        private void paintAntennaLabel(WAntenna antenna, Graphics graphics)
        {
            DbPoint dPoint = new DbPoint(antenna.EndPointLongitude, antenna.EndPointLatitude);
            PointF point;
            gisAdapter.ToDisplay(dPoint, out point);
            graphics.TranslateTransform(point.X, point.Y);
            string antennaDes = GetAntennaLabelDes(antenna, 100);
            SizeF size = graphics.MeasureString(antennaDes, FontAntennaLabel);
            size.Height *= 0.8f;
            System.Drawing.Rectangle rectangle = new System.Drawing.Rectangle((int)point.X - 3, (int)(point.Y - (size.Height / 2 - 5)), (int)size.Width + 10, (int)size.Height + 10);
            bool needDraw = true;
            foreach (System.Drawing.Rectangle rectangleTemp in drawedAntennaLabelRectangles)
            {
                if (rectangle.Right >= rectangleTemp.Left && rectangle.Left <= rectangleTemp.Right
                    && rectangle.Bottom >= rectangleTemp.Top && rectangle.Top <= rectangleTemp.Bottom)
                {
                    needDraw = false;
                    break;
                }
            }
            if (needDraw)
            {
                graphics.FillRectangle(Brushes.White, 3, -size.Height / 2, size.Width, size.Height);
                graphics.DrawString(antennaDes, FontAntennaLabel, Brushes.Black, 3, -size.Height / 2);
                drawedAntennaLabelRectangles.Add(rectangle);
            }
            graphics.ResetTransform();
        }

        public string GetAntennaLabelDes(WAntenna antenna, int length)
        {
            string des = "";
            if (DrawAntennaGroup)
            {
                des += antenna.AnttenaGroupSN.ToString() + " ";
            }
            if (DrawAntennaSN)
            {
                des += antenna.SN.ToString() + " ";
            }
            if (DrawAntennaLongitude)
            {
                des += antenna.Longitude.ToString() + " ";
            }
            if (DrawAntennaLatitude)
            {
                des += antenna.Latitude.ToString() + " ";
            }
            if (DrawAntennaDirectionType)
            {
                des += antenna.DirectionTypeDescription + " ";
            }
            if (DrawAntennaDirection)
            {
                des += antenna.Direction.ToString() + " ";
            }
            if (DrawAntennaDownwardE)
            {
                des += antenna.DownwardE.ToString() + " ";
            }
            if (DrawAntennaDownwardM)
            {
                des += antenna.DownwardM.ToString() + " ";
            }
            if (DrawAntennaDownward)
            {
                des += antenna.Downward.ToString() + " ";
            }
            if (DrawAntennaAltitude)
            {
                des += antenna.Altitude.ToString() + " ";
            }
            if (DrawAntennaDescription)
            {
                des += antenna.Description + " ";
            }
            if (des.Length > length)
            {
                des = des.Substring(0, length);
                des += "...";
            }
            return des;
        }

        private readonly List<System.Drawing.Rectangle> drawedAntennaLabelRectangles = new List<System.Drawing.Rectangle>();
        public List<System.Drawing.Rectangle> DrawedAntennaLabelRectangles
        {
            get { return drawedAntennaLabelRectangles; }
        }

        private void paintBTS(WNodeB bts, Graphics graphics)
        {
            DbPoint dPoint = new DbPoint(bts.Longitude, bts.Latitude);
            PointF point;
            gisAdapter.ToDisplay(dPoint, out point);
            graphics.TranslateTransform(point.X, point.Y);

            graphics.DrawRectangle(penNodeB, -SizeBTS / 2, -SizeBTS / 2, SizeBTS, SizeBTS);

            graphics.ResetTransform();
        }

        private void paintBTSLabel(WNodeB bts, Graphics graphics)
        {
            DbPoint dPoint = new DbPoint(bts.Longitude, bts.Latitude);
            PointF point;
            gisAdapter.ToDisplay(dPoint, out point);
            graphics.TranslateTransform(point.X, point.Y);
            string btsDes = GetBTSLabelDes(bts, 100);
            SizeF size = graphics.MeasureString(btsDes, FontBTSLabel);
            size.Height *= 0.8f;
            System.Drawing.Rectangle rectangle = new System.Drawing.Rectangle((int)point.X - 3, (int)(point.Y - (size.Height / 2 - 5)), (int)size.Width + 10, (int)size.Height + 10);
            bool needDraw = true;
            foreach (System.Drawing.Rectangle rectangleTemp in drawedBTSLabelRectangles)
            {
                if (rectangle.Right >= rectangleTemp.Left && rectangle.Left <= rectangleTemp.Right
                    && rectangle.Bottom >= rectangleTemp.Top && rectangle.Top <= rectangleTemp.Bottom)
                {
                    needDraw = false;
                    break;
                }
            }
            if (needDraw)
            {
                graphics.FillRectangle(Brushes.White, 3, -size.Height / 2, size.Width, size.Height);
                graphics.DrawString(btsDes, FontBTSLabel, Brushes.Black, 3, -size.Height / 2);
                drawedBTSLabelRectangles.Add(rectangle);
            }
            graphics.ResetTransform();
        }

        public string GetBTSLabelDes(WNodeB bts, int length)
        {
            string des = "";
            if (DrawBTSName)
            {
                des += bts.Name + " ";
            }
            if (DrawBTSCode)
            {
                des += bts.Code + " ";
            }
            if (DrawBTSMGW)
            {
                des += bts.BelongRNC.BelongMGW.Name + " ";
            }
            if (DrawBTSRNC)
            {
                des += bts.BelongRNC.Name + " ";
            }
            if (DrawBTSLongitude)
            {
                des += bts.Longitude.ToString() + " ";
            }
            if (DrawBTSLatitude)
            {
                des += bts.Latitude.ToString() + " ";
            }
            if (DrawBTSType)
            {
                des += bts.TypeDescription + " ";
            }
            if (DrawBTSDescription)
            {
                des += bts.Description + " ";
            }
            if (des.Length > length)
            {
                des = des.Substring(0, length);
                des += "...";
            }
            return des;
        }

        private readonly List<System.Drawing.Rectangle> drawedBTSLabelRectangles = new List<System.Drawing.Rectangle>();
        public List<System.Drawing.Rectangle> DrawedBTSLabelRectangles
        {
            get { return drawedBTSLabelRectangles; }
        }

        private WNodeB selectBTS(WNodeB bts, MapOperation2 mop2)
        {
            DbPoint dPoint = new DbPoint(bts.Longitude, bts.Latitude);
            PointF point;
            gisAdapter.ToDisplay(dPoint, out point);
            RectangleF rect;

            rect = new RectangleF(point.X - SizeBTS / 2, point.Y - SizeBTS / 2, SizeBTS, SizeBTS);

            DbRect dRect;
            gisAdapter.FromDisplay(rect, out dRect);
            if (mop2.CheckCenterInDRect(dRect))
            {
                return bts;
            }
            return null;
        }

        private WCell selectCell(WCell cell, MapOperation2 mop2, double scale)
        {
            DbPoint dPoint = new DbPoint(cell.Longitude, cell.Latitude);
            PointF point;
            gisAdapter.ToDisplay(dPoint, out point);
            float ratio = (float)(10000 / scale);
            if (DrawAlarm && mainModel.OutServiceInfoManager.isContainSnapShot(MasterCom.RAMS.Func.OutServiceInfo.NetType.WCDMA, cell))
            {
                ratio *= ZoomScaleAlarm;
            }
            int index = 1;
            bool circle = false;
            if (cell.DirectionType == AntennaDirectionType.Omni)
            {
                index = 0;
                circle = true;
            }

            if (circle)
            {
                RectangleF rect = new RectangleF(point.X + cellPoints[index][0].X * ratio, point.Y + cellPoints[index][0].Y * ratio, cellPoints[index][2].X * 2 * ratio, cellPoints[index][2].Y * 2 * ratio);
                DbRect dRect;
                gisAdapter.FromDisplay(rect, out dRect);
                if (mop2.CheckCenterInDRect(dRect))
                {
                    return cell;
                }
            }
            else
            {
                PointF[] pointsNew = getPointsNew(cellPoints, cell.Direction, point, ratio, index);

                DbPoint[] dpoints;
                gisAdapter.FromDisplay(pointsNew, out dpoints);
                if (mop2.CheckCenterInDbPoints(dpoints))
                {
                    return cell;
                }
            }
            return null;
        }

        private WAntenna selectAntenna(WAntenna antenna, MapOperation2 mop2, double scale)
        {
            DbPoint dPoint = new DbPoint(antenna.Longitude, antenna.Latitude);
            PointF point;
            gisAdapter.ToDisplay(dPoint, out point);
            float ratio = (float)(10000 / scale);
            if (DrawAlarm)
            {
                foreach (WCell cell in antenna.BelongCells)
                {
                    if (mainModel.OutServiceInfoManager.isContainSnapShot(MasterCom.RAMS.Func.OutServiceInfo.NetType.WCDMA, cell))
                    {
                        ratio *= ZoomScaleAlarm;
                        break;
                    }
                }
            }
            int index = 1;
            bool circle = false;
            if (antenna.DirectionType == AntennaDirectionType.Omni)
            {
                index = 0;
                circle = true;
            }

            if (circle)
            {
                RectangleF rect = new RectangleF(point.X + antennaPoints[index][0].X * ratio, point.Y + antennaPoints[index][0].Y * ratio, antennaPoints[index][2].X * 2 * ratio, antennaPoints[index][2].Y * 2 * ratio);
                DbRect dRect;
                gisAdapter.FromDisplay(rect, out dRect);
                if (mop2.CheckCenterInDRect(dRect)) //Ellipse
                {
                    return antenna;
                }
            }
            else
            {
                PointF[] pointsNew = getPointsNew(antennaPoints, antenna.Direction, point, ratio, index);

                DbPoint[] dpoints;
                gisAdapter.FromDisplay(pointsNew, out dpoints);
                if (mop2.CheckCenterInDbPoints(dpoints))
                {
                    return antenna;
                }
            }
            return null;
        }

        private PointF[] getPointsNew(List<PointF[]> pointList, short direction, PointF point, float ratio, int index)
        {
            PointF[] pointsNew = new PointF[pointList[index].Length + 1];
            for (int i = 0; i < pointList[index].Length + 1; i++)
            {
                PointF pointNew = pointList[index][i % pointList[index].Length];
                if (pointNew.X == 0 && pointNew.Y == 0)
                {
                    pointNew.X = point.X;
                    pointNew.Y = point.Y;
                }
                else
                {
                    double pointRadius = Math.Sqrt(pointNew.X * ratio * pointNew.X * ratio + pointNew.Y * ratio * pointNew.Y * ratio);
                    double pointDirection = (direction - 90) / 180.0F * Math.PI + Math.Atan2(pointNew.Y, pointNew.X);
                    pointNew.X = point.X + (float)(pointRadius * Math.Cos(pointDirection));
                    pointNew.Y = point.Y + (float)(pointRadius * Math.Sin(pointDirection));
                }
                pointsNew[i] = pointNew;
            }

            return pointsNew;
        }

        private void serverCellsChanged(object sender, EventArgs e)
        {
            makeBrushes();
            Invalidate();
        }
        public void RefreshBrushes()
        {
            makeBrushes();
        }
        private void makeBrushes()
        {
            if (mainModel == null)
            {
                return;
            }
            brushCell = new SolidBrush(ColorCell);
            brushAntenna = new SolidBrush(ColorAntenna);
            brushCoBCCH = new SolidBrush(ColorCoBCCH);
            brushCoTCH = new SolidBrush(ColorCoTCH);
            brushAdjBCCH = new SolidBrush(ColorAdjBCCH);
            brushAdjTCH = new SolidBrush(ColorAdjTCH);
            brushCoBSIC = new SolidBrush(ColorCoBSIC);
            brushNeighbour = new SolidBrush(ColorNeighbour);
            penSelected = new Pen(ColorSelected, 3);
            penNodeB = new Pen(ColorBTS, 1);
            brushesServer.Clear();
            mainModel.ServerCellPens.Clear();
            int count = mainModel.ServerCells.Count + mainModel.ServerWCells.Count;
            for (int i = 0; i < count; i++)
            {
                float percent = count == 1 ? 0.5F : (float)i / (count - 1);
                Color beginColor;
                Color endColor;
                if (ColorViaEnabled)
                {
                    percent = percent * 2;
                    if (percent < 1)
                    {
                        beginColor = ColorBegin;
                        endColor = ColorVia;
                    }
                    else
                    {
                        percent -= 1;
                        beginColor = ColorVia;
                        endColor = ColorEnd;
                    }
                }
                else
                {
                    beginColor = ColorBegin;
                    endColor = ColorEnd;
                }
                brushesServer.Add(new SolidBrush(Color.FromArgb(
                    (int)(beginColor.R + (endColor.R - beginColor.R) * percent),
                    (int)(beginColor.G + (endColor.G - beginColor.G) * percent),
                    (int)(beginColor.B + (endColor.B - beginColor.B) * percent)
                    )));
                mainModel.ServerCellPens.Add(new Pen(Color.FromArgb(
                    (int)(beginColor.R + (endColor.R - beginColor.R) * percent),
                    (int)(beginColor.G + (endColor.G - beginColor.G) * percent),
                    (int)(beginColor.B + (endColor.B - beginColor.B) * percent)
                    ), 1));
            }
        }
        
        private XmlElement TransformPoint(WAntenna antenna, int index, XmlDocument doc, string ColorCode)
        {
            StringBuilder location = new StringBuilder();
            foreach (LayerPoint p in antennaGEPoints)
            {

                if (p.Index == index)
                {
                    if (index == 1 || index == 3)
                    {
                        double angle1 = (90 - antenna.Direction) * Math.PI / 180;
                        double angle2 = Math.Atan(p.Y / p.X);
                        double angle3 = angle1 + angle2;
                        double r = Math.Sqrt(p.X * p.X + p.Y * p.Y);
                        double tarX = r * Math.Cos(angle3) + antenna.Longitude;
                        double tarY = r * Math.Sin(angle3) + antenna.Latitude;
                        location.Append(tarX.ToString() + "," + tarY.ToString() + ",10 ");
                    }
                    else if (index == 0 || index == 2)
                    {
                        double tarX = antenna.Longitude + p.X;
                        double tarY = antenna.Latitude + p.Y;
                        location.Append(tarX.ToString() + "," + tarY.ToString() + ",10 ");

                    }
                }
            }
            Brush brush;
            if (DrawServer)
            {
                for (int i = 0; i < mainModel.ServerWCells.Count; i++)
                {
                    if (antenna.BelongCells.Contains(mainModel.ServerWCells[i]))
                    {
                        brush = brushesServer[i];
                        SolidBrush sbrush = brush as SolidBrush;
                        ColorCode = ChangeColor(sbrush.Color);
                        break;
                    }
                }
            }
            return AddLine(doc, antenna.BelongLastCell.Name, antenna.Description, location.ToString(), ColorCode);
        }
        private string ChangeColor(Color color)//将Color类型转为string类型
        {
            string alpha = Convert.ToString(color.A, 16);
            string red = Convert.ToString(color.R, 16);
            string green = Convert.ToString(color.G, 16);
            string blue = Convert.ToString(color.B, 16);
            return DealwithColor(alpha) + DealwithColor(blue) + DealwithColor(green) + DealwithColor(red);
        }

        private string DealwithColor(string ColorCode)//对不足两位的颜色值进行补零处理
        {
            if (ColorCode.Length < 2)
            {
                for (int i = 0; i < 2 - ColorCode.Length; i++)
                {
                    ColorCode = "0" + ColorCode;
                }
            }
            return ColorCode;
        }

        private XmlElement AddLine(XmlDocument doc, string Name, string description, string location, string ColorCode)
        {
            XmlElement elemPlacemark = doc.CreateElement("Placemark");
            XmlElement elemName = doc.CreateElement("name");
            elemName.AppendChild(doc.CreateTextNode("小区：" + Name));
            elemPlacemark.AppendChild(elemName);
            XmlElement elemdescription = doc.CreateElement("description");
            elemdescription.AppendChild(doc.CreateTextNode(description));
            elemPlacemark.AppendChild(elemdescription);
            XmlElement elemstyle = doc.CreateElement("Style");
            elemPlacemark.AppendChild(elemstyle);
            XmlElement elemPolyStyle = doc.CreateElement("PolyStyle");
            elemstyle.AppendChild(elemPolyStyle);
            XmlElement elemcolor = doc.CreateElement("color");
            elemcolor.AppendChild(doc.CreateTextNode(ColorCode));
            elemPolyStyle.AppendChild(elemcolor);
            XmlElement elemoutline = doc.CreateElement("outline");
            elemoutline.AppendChild(doc.CreateTextNode("0"));
            elemPolyStyle.AppendChild(elemoutline);
            XmlElement elemPolygon = doc.CreateElement("Polygon");
            elemPlacemark.AppendChild(elemPolygon);
            XmlElement elemextrude = doc.CreateElement("extrude");
            elemextrude.AppendChild(doc.CreateTextNode("1"));
            elemPolygon.AppendChild(elemextrude);
            XmlElement elemaltitudeMode = doc.CreateElement("altitudeMode");
            elemaltitudeMode.AppendChild(doc.CreateTextNode("relativeToGround"));
            elemPolygon.AppendChild(elemaltitudeMode);
            XmlElement elemouterBoundaryIs = doc.CreateElement("outerBoundaryIs");
            elemPolygon.AppendChild(elemouterBoundaryIs);
            XmlElement elemLinearRing = doc.CreateElement("LinearRing");
            elemPolygon.AppendChild(elemLinearRing);
            XmlElement elemCoordinates = doc.CreateElement("coordinates");
            elemCoordinates.AppendChild(doc.CreateTextNode(location));
            elemLinearRing.AppendChild(elemCoordinates);
            elemouterBoundaryIs.AppendChild(elemLinearRing);
            return elemPlacemark;
        }

        private static List<LayerPoint> GEdrawCircle(double radius, int part, int index)
        {
            List<LayerPoint> basePoint = new List<LayerPoint>();
            double angel = 2 * Math.PI / part;
            for (int i = 0; i <= part; i++)
            {
                basePoint.Add(new LayerPoint(radius * Math.Sin(i * angel), radius * Math.Cos(i * angel), index));
            }
            return basePoint;
        }

        private static List<PointF[]> cellPoints = new List<PointF[]>();
        public List<PointF[]> CellPoints
        {
            get { return cellPoints; }
        }

        private static List<GraphicsPath> cellPaths = new List<GraphicsPath>();
        public List<GraphicsPath> CellPaths
        {
            get { return cellPaths; }
        }

        private static List<PathGradientBrush> cellPathGradientBrushs = new List<PathGradientBrush>();

        private static List<PointF[]> antennaPoints = new List<PointF[]>();
        public List<PointF[]> AntennaPoints
        {
            get { return antennaPoints; }
        }

        private static List<LayerPoint> antennaGEPoints = new List<LayerPoint>();

        private static List<GraphicsPath> antennaPaths = new List<GraphicsPath>();
        public List<GraphicsPath> AntennaPaths
        {
            get { return antennaPaths; }
        }


        #region layer properties
        public bool DrawServer { get; set; } = true;
        public Color ColorBegin { get; set; } = Color.Red;
        public bool ColorViaEnabled { get; set; } = true;
        public Color ColorVia { get; set; } = Color.Yellow;
        public Color ColorEnd { get; set; } = Color.Green;
        public bool DrawUpper { get; set; } = true;
        public bool DrawOutdoor { get; set; } = true;
        public bool DrawIndoor { get; set; } = true;
        public Color ColorSelected { get; set; } = Color.Red;
        public bool DrawBTS { get; set; } = false;
        public Color ColorBTS { get; set; } = Color.Black;
        public int SizeBTS { get; set; } = 6;
        public bool DrawBTSLabel { get; set; } = true;
        public Font FontBTSLabel { get; set; } = new Font(new FontFamily("宋体"), 9, FontStyle.Bold);
        public bool DrawCell { get; set; } = false;
        public Color ColorCell { get; set; } = Color.Purple;
        public bool DrawAntenna { get; set; } = true;
        public Color ColorAntenna { get; set; } = Color.Blue;

        public bool DrawPlanningLabel { get; set; } = false;
        public bool DrawCoBCCH { get; set; } = true;
        public bool DrawCoTCH { get; set; } = true;
        public bool DrawAdjBCCH { get; set; } = true;
        public bool DrawAdjTCH { get; set; } = true;
        public Color ColorCoBCCH { get; set; } = Color.Red;
        public Color ColorCoTCH { get; set; } = Color.Orange;
        public Color ColorAdjBCCH { get; set; } = Color.Yellow;
        public Color ColorAdjTCH { get; set; } = Color.Violet;
        public Color ColorCoBSIC { get; set; } = Color.BurlyWood;
        public Color ColorNeighbour { get; set; } = Color.Cyan;

        public Font FontPlanningLabel { get; set; } = new Font(new FontFamily("宋体"), 9);
        public bool DrawRepeater { get; set; } = true;
        public int SizeRepeater { get; set; } = 16;
        public bool DrawLineDonarCell2Repeater { get; set; } = true;

        public override void GetObjectData(System.Runtime.Serialization.SerializationInfo info, System.Runtime.Serialization.StreamingContext context)
        {
            base.GetObjectData(info, context);
            info.AddValue("drawCurrent", DrawCurrent);
            info.AddValue("drawServer", DrawServer);
            info.AddValue("colorBegin", ColorBegin);
            info.AddValue("colorViaEnabled", ColorViaEnabled);
            info.AddValue("colorVia", ColorVia);
            info.AddValue("colorEnd", ColorEnd);
            info.AddValue("drawUpper", DrawUpper);
            info.AddValue("drawOutdoor", DrawOutdoor);
            info.AddValue("drawIndoor", DrawIndoor);
            info.AddValue("colorSelected", ColorSelected);

            info.AddValue("drawBTS", DrawBTS);
            info.AddValue("colorBTS", ColorBTS);
            info.AddValue("sizeBTS", SizeBTS);
            info.AddValue("drawBTSLabel", DrawBTSLabel);
            info.AddValue("fontBTSLabelFontFamilyName", FontBTSLabel.FontFamily.Name);
            info.AddValue("fontBTSLabelFontSize", FontBTSLabel.Size);
            info.AddValue("fontBTSLabelFontStyle", (int)FontBTSLabel.Style);

            info.AddValue("drawCell", DrawCell);
            info.AddValue("colorCell", ColorCell);

            info.AddValue("drawAntenna", DrawAntenna);
            info.AddValue("colorAntenna", ColorAntenna);

            info.AddValue("drawPlanningLabel", DrawPlanningLabel);
            info.AddValue("drawCoBCCH", DrawCoBCCH);
            info.AddValue("drawCoTCH", DrawCoTCH);
            info.AddValue("drawAdjBCCH", DrawAdjBCCH);
            info.AddValue("drawAdjTCH", DrawAdjTCH);
            info.AddValue("colorCoBCCH", ColorCoBCCH);
            info.AddValue("colorCoTCH", ColorCoTCH);
            info.AddValue("colorAdjBCCH", ColorAdjBCCH);
            info.AddValue("colorAdjTCH", ColorAdjTCH);
            info.AddValue("colorCoBSIC", ColorCoBSIC);
            info.AddValue("colorNeighbour", ColorNeighbour);
            info.AddValue("fontPlanningLabelFontFamilyName", FontPlanningLabel.FontFamily.Name);
            info.AddValue("fontPlanningLabelFontSize", FontPlanningLabel.Size);
            info.AddValue("fontPlanningLabelFontStyle", (int)FontPlanningLabel.Style);

            info.AddValue("drawRepeater", DrawRepeater);
            info.AddValue("sizeRepeater", SizeRepeater);
            info.AddValue("drawLineDonarCell2Repeater", DrawLineDonarCell2Repeater);
            info.AddValue("drawAlarm", DrawAlarm);
            info.AddValue("zoomScaleAlarm", ZoomScaleAlarm);
            info.AddValue("colorAlarm", ColorAlarm);

            //BTS Index
            info.AddValue("drawBTSName", DrawBTSName);
            info.AddValue("drawBTSCode", DrawBTSCode);
            info.AddValue("drawBTSMGW", DrawBTSMGW);
            info.AddValue("drawBTSRNC", DrawBTSRNC);
            info.AddValue("drawBTSLongitude", DrawBTSLongitude);
            info.AddValue("drawBTSLatitude", DrawBTSLatitude);
            info.AddValue("drawBTSType", DrawBTSType);
            info.AddValue("drawBTSDescription", DrawBTSDescription);

            //Cell Index
            info.AddValue("drawCellLabel", DrawCellLabel);
            info.AddValue("fontCellLabelFamilyName", FontCellLabel.FontFamily.Name);
            info.AddValue("fontCellLabelFontSize", FontCellLabel.Size);
            info.AddValue("fontCellLabelFontStyle", (int)FontCellLabel.Style);
            info.AddValue("drawCellName", DrawCellName);
            info.AddValue("drawCellCode", DrawCellCode);
            info.AddValue("drawCellLAC", DrawCellLAC);
            info.AddValue("drawCellCI", DrawCellCI);
            info.AddValue("drawCellUARFCN", DrawCellUARFCN);
            info.AddValue("drawCellPSC", DrawCellPSC);
            info.AddValue("drawCellDes", DrawCellDes);

            //Antenna Index
            info.AddValue("drawAntennaLabel", DrawAntennaLabel);
            info.AddValue("fontAntennaLabelFamilyName", FontAntennaLabel.FontFamily.Name);
            info.AddValue("fontAntennaLabelFontSize", FontAntennaLabel.Size);
            info.AddValue("fontAntennaLabelFontStyle", (int)FontAntennaLabel.Style);
            info.AddValue("drawAntennaGroup", DrawAntennaGroup);
            info.AddValue("drawAntennaSN", DrawAntennaSN);
            info.AddValue("drawAntennaLongitude", DrawAntennaLongitude);
            info.AddValue("drawAntennaLatitude", DrawAntennaLatitude);
            info.AddValue("drawAntennaDirectionType", DrawAntennaDirectionType);
            info.AddValue("drawAntennaDirection", DrawAntennaDirection);
            info.AddValue("drawAntennaDownwardM", DrawAntennaDownwardM);
            info.AddValue("drawAntennaDownwardE", DrawAntennaDownwardE);
            info.AddValue("drawAntennaDownward", DrawAntennaDownward);
            info.AddValue("drawAntennaAltitude", DrawAntennaAltitude);
            info.AddValue("drawAntennaDescription", DrawAntennaDescription);
        }

        public void SetObjectData(System.Runtime.Serialization.SerializationInfo info, System.Runtime.Serialization.StreamingContext context)
        {
            DrawCurrent = info.GetBoolean("drawCurrent");
            DrawServer = info.GetBoolean("drawServer");
            ColorBegin = (Color)info.GetValue("colorBegin", typeof(Color));
            ColorViaEnabled = info.GetBoolean("colorViaEnabled");
            ColorVia = (Color)info.GetValue("colorVia", typeof(Color));
            ColorEnd = (Color)info.GetValue("colorEnd", typeof(Color));
            DrawUpper = info.GetBoolean("drawUpper");
            DrawOutdoor = info.GetBoolean("drawOutdoor");
            DrawIndoor = info.GetBoolean("drawIndoor");
            ColorSelected = (Color)info.GetValue("colorSelected", typeof(Color));
            DrawAlarm = info.GetBoolean("drawAlarm");
            ZoomScaleAlarm = info.GetInt32("zoomScaleAlarm");
            ColorAlarm = (Color)info.GetValue("colorAlarm", typeof(Color));

            DrawBTS = info.GetBoolean("drawBTS");
            ColorBTS = (Color)info.GetValue("colorBTS", typeof(Color));
            SizeBTS = info.GetInt32("sizeBTS");
            DrawBTSLabel = info.GetBoolean("drawBTSLabel");
            FontBTSLabel = new Font(new FontFamily(info.GetString("fontBTSLabelFontFamilyName")), info.GetSingle("fontBTSLabelFontSize"), (FontStyle)info.GetInt32("fontBTSLabelFontStyle"));

            DrawCell = info.GetBoolean("drawCell");
            ColorCell = (Color)info.GetValue("colorCell", typeof(Color));

            DrawAntenna = info.GetBoolean("drawAntenna");
            ColorAntenna = (Color)info.GetValue("colorAntenna", typeof(Color));

            DrawPlanningLabel = info.GetBoolean("drawPlanningLabel");
            DrawCoBCCH = info.GetBoolean("drawCoBCCH");
            DrawCoTCH = info.GetBoolean("drawCoTCH");
            DrawAdjBCCH = info.GetBoolean("drawAdjBCCH");
            DrawAdjTCH = info.GetBoolean("drawAdjTCH");
            ColorCoBCCH = (Color)info.GetValue("colorCoBCCH", typeof(Color));
            ColorCoTCH = (Color)info.GetValue("colorCoTCH", typeof(Color));
            ColorAdjBCCH = (Color)info.GetValue("colorAdjBCCH", typeof(Color));
            ColorAdjTCH = (Color)info.GetValue("colorAdjTCH", typeof(Color));
            ColorCoBSIC = (Color)info.GetValue("colorCoBSIC", typeof(Color));
            ColorNeighbour = (Color)info.GetValue("colorNeighbour", typeof(Color));
            FontPlanningLabel = new Font(new FontFamily(info.GetString("fontPlanningLabelFontFamilyName")), info.GetSingle("fontPlanningLabelFontSize"), (FontStyle)info.GetInt32("fontPlanningLabelFontStyle"));

            DrawRepeater = info.GetBoolean("drawRepeater");
            SizeRepeater = info.GetInt32("sizeRepeater");
            DrawLineDonarCell2Repeater = info.GetBoolean("drawLineDonarCell2Repeater");

            //BTS Index
            DrawBTSName = info.GetBoolean("drawBTSName");
            DrawBTSCode = info.GetBoolean("drawBTSCode");
            DrawBTSMGW = info.GetBoolean("drawBTSMGW");
            DrawBTSRNC = info.GetBoolean("drawBTSRNC");
            DrawBTSLongitude = info.GetBoolean("drawBTSLongitude");
            DrawBTSLatitude = info.GetBoolean("drawBTSLatitude");
            DrawBTSType = info.GetBoolean("drawBTSType");
            DrawBTSDescription = info.GetBoolean("drawBTSDescription");

            //Cell Index
            DrawCellLabel = info.GetBoolean("drawCellLabel");
            FontCellLabel = new Font(new FontFamily(info.GetString("fontCellLabelFamilyName")), info.GetSingle("fontCellLabelFontSize"), (FontStyle)info.GetInt32("fontCellLabelFontStyle"));
            DrawCellName = info.GetBoolean("drawCellName");
            DrawCellCode = info.GetBoolean("drawCellCode");
            DrawCellLAC = info.GetBoolean("drawCellLAC");
            DrawCellCI = info.GetBoolean("drawCellCI");
            DrawCellUARFCN = info.GetBoolean("drawCellUARFCN");
            DrawCellPSC = info.GetBoolean("drawCellPSC");
            DrawCellDes = info.GetBoolean("drawCellDes");

            //Antenna Index
            DrawAntennaLabel = info.GetBoolean("drawAntennaLabel");
            FontAntennaLabel = new Font(new FontFamily(info.GetString("fontAntennaLabelFamilyName")), info.GetSingle("fontAntennaLabelFontSize"), (FontStyle)info.GetInt32("fontAntennaLabelFontStyle"));
            DrawAntennaGroup = info.GetBoolean("drawAntennaGroup");
            DrawAntennaSN = info.GetBoolean("drawAntennaSN");
            DrawAntennaLongitude = info.GetBoolean("drawAntennaLongitude");
            DrawAntennaLatitude = info.GetBoolean("drawAntennaLatitude");
            DrawAntennaDirectionType = info.GetBoolean("drawAntennaDirectionType");
            DrawAntennaDirection = info.GetBoolean("drawAntennaDirection");
            DrawAntennaDownwardM = info.GetBoolean("drawAntennaDownwardM");
            DrawAntennaDownwardE = info.GetBoolean("drawAntennaDownwardE");
            DrawAntennaDownward = info.GetBoolean("drawAntennaDownward");
            DrawAntennaAltitude = info.GetBoolean("drawAntennaAltitude");
            DrawAntennaDescription = info.GetBoolean("drawAntennaDescription");

            makeBrushes();
        }

        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["drawServer"] = DrawServer;
                param["colorBegin"] = ColorBegin.ToArgb();
                param["colorViaEnabled"] = ColorViaEnabled;
                param["colorVia"] = ColorVia.ToArgb();
                param["colorEnd"] = ColorEnd.ToArgb();
                param["drawIndoor"] = DrawIndoor;
                param["drawOutdoor"] = DrawOutdoor;
                param["colorSelected"] = ColorSelected.ToArgb();
                param["drawAlarm"] = DrawAlarm;
                param["zoomScaleAlarm"] = ZoomScaleAlarm;
                param["colorAlarm"] = ColorAlarm.ToArgb();

                param["drawBTS"] = DrawBTS;
                param["colorBTS"] = ColorBTS.ToArgb();
                param["sizeBTS"] = SizeBTS;
                param["drawBTSLabel"] = DrawBTSLabel;
                param["fontBTSLabelFontFamilyName"] = FontBTSLabel.FontFamily.Name;
                param["fontBTSLabelFontSize"] = FontBTSLabel.Size;
                param["fontBTSLabelFontStyle"] = (int)FontBTSLabel.Style;
                param["drawBTSName"] = DrawBTSName;
                param["drawBTSRNC"] = DrawBTSRNC;
                param["drawBTSLongitude"] = DrawBTSLongitude;
                param["drawBTSLatitude"] = DrawBTSLatitude;
                param["drawBTSType"] = DrawBTSType;
                param["drawBTSDescription"] = DrawBTSDescription;

                param["drawCell"] = DrawCell;
                param["colorCell"] = ColorCell.ToArgb();
                param["drawCellLabel"] = DrawCellLabel;
                param["drawCellName"] = DrawCellName;
                param["drawCellCode"] = DrawCellCode;
                param["drawCellLAC"] = DrawCellLAC;
                param["drawCellCI"] = DrawCellCI;
                param["drawCellUARFCN"] = DrawCellUARFCN;
                param["drawCellPSC"] = DrawCellPSC;
                param["drawCellDes"] = DrawCellDes;

                param["drawAntenna"] = DrawAntenna;
                param["colorAntenna"] = ColorAntenna.ToArgb();
                param["drawAntennaLabel"] = DrawAntennaLabel;
                param["fontAntennaLabelFamilyName"] = FontAntennaLabel.FontFamily.Name;
                param["fontAntennaLabelFontSize"] = FontAntennaLabel.Size;
                param["fontAntennaLabelFontStyle"] = (int)FontAntennaLabel.Style;
                param["drawAntennaLongitude"] = DrawAntennaLongitude;
                param["drawAntennaLatitude"] = DrawAntennaLatitude;
                param["drawAntennaDirectionType"] = DrawAntennaDirectionType;
                param["drawAntennaDirection"] = DrawAntennaDirection;
                param["drawAntennaDownward"] = DrawAntennaDownward;
                param["drawAntennaAltitude"] = DrawAntennaAltitude;
                param["drawAntennaDescription"] = DrawAntennaDescription;

                param["colorNeighbour"] = ColorNeighbour.ToArgb();
                param["isEnabled"] = this.Enabled;
                return param;
            }
            set
            {
                try
                {
                    DrawServer = (bool)value["drawServer"];
                    ColorBegin = Color.FromArgb((int)value["colorBegin"]);
                    ColorViaEnabled = (bool)value["colorViaEnabled"];
                    ColorVia = Color.FromArgb((int)value["colorVia"]);
                    ColorEnd = Color.FromArgb((int)value["colorEnd"]);
                    DrawIndoor = (bool)value["drawIndoor"];
                    DrawOutdoor = (bool)value["drawOutdoor"];
                    ColorSelected = Color.FromArgb((int)value["colorSelected"]);
                    try
                    {
                        DrawAlarm = (bool)value["drawAlarm"];
                        ZoomScaleAlarm = (int)value["zoomScaleAlarm"];
                        ColorAlarm = Color.FromArgb((int)value["colorAlarm"]);
                    }
                    catch
                    {
                        //continue
                    }

                    DrawBTS = (bool)value["drawBTS"];
                    ColorBTS = Color.FromArgb((int)value["colorBTS"]);
                    SizeBTS = (int)value["sizeBTS"];
                    DrawBTSLabel = (bool)value["drawBTSLabel"];
                    FontBTSLabel = new Font(new FontFamily((String)value["fontBTSLabelFontFamilyName"]), (float)value["fontBTSLabelFontSize"], (FontStyle)(int)value["fontBTSLabelFontStyle"]);
                    DrawBTSName = getParamBoolValue(value, "drawBTSName");
                    DrawBTSRNC = getParamBoolValue(value, "drawBTSRNC");
                    DrawBTSLongitude = getParamBoolValue(value, "drawBTSLongitude");
                    DrawBTSLatitude = getParamBoolValue(value, "drawBTSLatitude");
                    DrawBTSType = getParamBoolValue(value, "drawBTSType");
                    DrawBTSDescription = getParamBoolValue(value, "drawBTSDescription");

                    DrawCell = (bool)value["drawCell"];
                    ColorCell = Color.FromArgb((int)value["colorCell"]);
                    DrawCellLabel = getParamBoolValue(value, "drawCellLabel");
                    DrawCellName = getParamBoolValue(value, "drawCellName");
                    DrawCellCode = getParamBoolValue(value, "drawCellCode");
                    DrawCellLAC = getParamBoolValue(value, "drawCellLAC");
                    DrawCellCI = getParamBoolValue(value, "drawCellCI");
                    DrawCellUARFCN = getParamBoolValue(value, "drawCellUARFCN");
                    DrawCellPSC = getParamBoolValue(value, "drawCellPSC");
                    DrawCellDes = getParamBoolValue(value, "drawCellDes");

                    DrawAntenna = (bool)value["drawAntenna"];
                    Color antennaColor = Color.FromArgb((int)value["colorAntenna"]);
                    penAntenna = new Pen(antennaColor, 2);
                    brushAntenna = new SolidBrush(antennaColor);
                    DrawAntennaLabel = getParamBoolValue(value, "drawAntennaLabel");

                    string FontAntennaLabelFamilyName = "宋体";
                    if (value.ContainsKey("fontAntennaLabelFamilyName") && value["fontAntennaLabelFamilyName"] != null)
                    {
                        FontAntennaLabelFamilyName = (string)value["fontAntennaLabelFamilyName"];
                    }
                    float FontAntennaLabelFontSize = 9;
                    if (value.ContainsKey("fontAntennaLabelFontSize") && value["fontAntennaLabelFontSize"] != null)
                    {
                        FontAntennaLabelFontSize = (float)value["fontAntennaLabelFontSize"];
                    }
                    int FontAntennaLabelFontStyle = 1;
                    if (value.ContainsKey("fontAntennaLabelFontStyle") && value["fontAntennaLabelFontStyle"] != null)
                    {
                        FontAntennaLabelFontStyle = (int)value["fontAntennaLabelFontStyle"];
                    }
                    FontAntennaLabel = new Font(new FontFamily(FontAntennaLabelFamilyName), FontAntennaLabelFontSize, (FontStyle)FontAntennaLabelFontStyle);
                    DrawAntennaLongitude = getParamBoolValue(value, "drawAntennaLongitude");
                    DrawAntennaLatitude = getParamBoolValue(value, "drawAntennaLatitude");
                    DrawAntennaDirectionType = getParamBoolValue(value, "drawAntennaDirectionType");
                    DrawAntennaDirection = getParamBoolValue(value, "drawAntennaDirection");
                    DrawAntennaDownward = getParamBoolValue(value, "drawAntennaDownward");
                    DrawAntennaAltitude = getParamBoolValue(value, "drawAntennaAltitude");
                    DrawAntennaDescription = getParamBoolValue(value, "drawAntennaDescription");

                    int ColorNeighbourArgb = 1;
                    if (value.ContainsKey("colorNeighbour") && value["colorNeighbour"] != null)
                    {
                        ColorNeighbourArgb = (int)value["colorNeighbour"];
                        ColorNeighbour = Color.FromArgb(ColorNeighbourArgb);
                    }
                    this.Enabled = (bool)value["isEnabled"];
                }
                catch
                {
                    //continue
                }
                makeBrushes();
            }
        }
        private bool getParamBoolValue(Dictionary<string, object> paramDic, string paramName)
        {
            bool paramValue = false;
            if (paramDic != null && paramDic.ContainsKey(paramName) && paramDic[paramName] != null)
            {
                paramValue = (bool)paramDic[paramName];
            }
            return paramValue;
        }

        private readonly List<Brush> brushesServer = new List<Brush>();
        public List<Brush> BrushesServer
        {
            get { return brushesServer; }
        }

        private Pen penSelected = new Pen(Color.Red, 1);
        public Pen PenSelected
        {
            get { return penSelected; }
        }

        private Pen penNodeB = new Pen(Color.Black, 1);
        public Pen PenNodeB
        {
            get { return penNodeB; }
        }

        private Brush brushCell = new SolidBrush(Color.Purple);
        public Brush BrushCell
        {
            get { return brushCell; }
        }

        private readonly Brush brushWNeighbourWCell = new SolidBrush(Color.LimeGreen);
        public Brush BrushWNeighbourWCell
        {
            get { return brushWNeighbourWCell; }
        }

        private readonly Brush brushAlarmCell = new SolidBrush(Color.Red);
        public Brush BrushAlarmCell
        {
            get { return brushAlarmCell; }
        }

        private Brush brushAntenna = new SolidBrush(Color.Blue);
        public Brush BrushAntenna
        {
            get { return brushAntenna; }
        }

        private Pen penAntenna = new Pen(Brushes.Blue, 2);
        public Pen PenAntenna
        {
            get { return penAntenna; }
        }

        private Brush brushCoBCCH { get; set; } = new SolidBrush(Color.Red);
        private Brush brushCoTCH { get; set; } = new SolidBrush(Color.Orange);
        private Brush brushAdjBCCH { get; set; } = new SolidBrush(Color.Yellow);
        private Brush brushAdjTCH { get; set; } = new SolidBrush(Color.Violet);
        private Brush brushCoBSIC { get; set; } = new SolidBrush(Color.BurlyWood);
        private Brush brushNeighbour { get; set; } = new SolidBrush(Color.Cyan);
        
        public bool DrawAlarm { get; set; }

        private readonly Brush brushAlarm = new SolidBrush(Color.Red);
        public Brush BrushAlarm
        {
            get { return brushAlarm; }
        }

        public Color ColorAlarm { get; set; } = Color.Red;
        public int ZoomScaleAlarm { get; set; } = 2;

        //BTS指标显示设置 
        //Hui 2010.08.15
        public bool DrawBTSName { get; set; } = true;
        public bool DrawBTSCode { get; set; } = false;
        public bool DrawBTSMGW { get; set; } = false;
        public bool DrawBTSRNC { get; set; } = false;
        public bool DrawBTSLongitude { get; set; } = false;
        public bool DrawBTSLatitude { get; set; } = false;
        public bool DrawBTSType { get; set; } = false;
        public bool DrawBTSDescription { get; set; } = false;

        //Cell指标显示设置 
        //Hui 2010.08.15
        public bool DrawCellLabel { get; set; } = false;
        public Font FontCellLabel { get; set; } = new Font(new FontFamily("宋体"), 9, FontStyle.Bold);
        public bool DrawCellName { get; set; } = true;
        public bool DrawCellCode { get; set; } = false;
        public bool DrawCellLAC { get; set; } = false;
        public bool DrawCellCI { get; set; } = false;
        public bool DrawCellUARFCN { get; set; } = false;
        public bool DrawCellPSC { get; set; } = false;
        public bool DrawCellDes { get; set; } = false;

        //Antenna指标显示设置 
        //Hui 2010.08.15
        public bool DrawAntennaLabel { get; set; } = false;
        public Font FontAntennaLabel { get; set; } = new Font(new FontFamily("宋体"), 9, FontStyle.Bold);
        public bool DrawAntennaGroup { get; set; } = false;
        public bool DrawAntennaSN { get; set; } = false;
        public bool DrawAntennaLongitude { get; set; } = false;
        public bool DrawAntennaLatitude { get; set; } = false;
        public bool DrawAntennaDirectionType { get; set; } = false;
        public bool DrawAntennaDirection { get; set; } = false;
        public bool DrawAntennaDownwardM { get; set; } = false;
        public bool DrawAntennaDownwardE { get; set; } = false;
        public bool DrawAntennaDownward { get; set; } = false;
        public bool DrawAntennaAltitude { get; set; } = false;
        public bool DrawAntennaDescription { get; set; } = false;
        #endregion

        #region IKMLExport 成员

        void IKMLExport.ExportKml(KMLExporter exporter, XmlElement parentElem)
        {
            XmlElement layerElement = exporter.CreateFolder("WCDMA小区信息", false);
            parentElem.AppendChild(layerElement);

            if (DrawAntenna && mapScale < 100000)
            {
                ReadOnlyCollection<WAntenna> antennas = null;
                if (DrawCurrent)
                {
                    antennas = mainModel.CellManager.GetCurrentWAntennas();
                }
                else
                {
                    antennas = mainModel.CellManager.GetWAntennas(CurShowTimeAt);
                }
                if (antennas != null)
                {
                    foreach (WAntenna antenna in antennas)
                    {
                        int index = 0;
                        string ColorCode;
                        if (antenna.Direction == 0)
                        {
                            index = 0;
                            ColorCode = "FFCC00AA";
                        }
                        else
                        {
                            index = 1;
                            ColorCode = "FFCC00AA";
                        }
                        layerElement.AppendChild(TransformPoint(antenna, index, exporter.getRootNode(), ColorCode));
                    }
                }
            }
        }

        #endregion
    }
}
