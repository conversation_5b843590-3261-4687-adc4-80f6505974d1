﻿namespace MasterCom.RAMS.Model.BaseInfo
{
    partial class UserFuncRightsOptionForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.splitContainerControl1 = new DevExpress.XtraEditors.SplitContainerControl();
            this.grpUser = new DevExpress.XtraEditors.GroupControl();
            this.gridCtrlUser = new DevExpress.XtraGrid.GridControl();
            this.gvUser = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gcName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcDesc = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcUserRole = new DevExpress.XtraGrid.Columns.GridColumn();
            this.splitContainerControl2 = new DevExpress.XtraEditors.SplitContainerControl();
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.treeListRole = new DevExpress.XtraTreeList.TreeList();
            this.colRoleName = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.colRoleDesc = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.groupControl2 = new DevExpress.XtraEditors.GroupControl();
            this.treeListFunc = new DevExpress.XtraTreeList.TreeList();
            this.treeListColumn2 = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.treeListColumn3 = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.colFuncExportPermit = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.repositoryItemCheckEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemCheckEdit();
            this.colFuncExportZip = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.colFuncExportLog = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.colFuncExportCause = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.colFuncIsNeedHideKeyInfo = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.panelControl2 = new DevExpress.XtraEditors.PanelControl();
            this.label1 = new System.Windows.Forms.Label();
            this.btnAddRole = new DevExpress.XtraEditors.SimpleButton();
            this.btnSubmit = new DevExpress.XtraEditors.SimpleButton();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).BeginInit();
            this.splitContainerControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.grpUser)).BeginInit();
            this.grpUser.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridCtrlUser)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvUser)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl2)).BeginInit();
            this.splitContainerControl2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.treeListRole)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).BeginInit();
            this.groupControl2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.treeListFunc)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemCheckEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl2)).BeginInit();
            this.panelControl2.SuspendLayout();
            this.SuspendLayout();
            // 
            // splitContainerControl1
            // 
            this.splitContainerControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl1.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl1.Name = "splitContainerControl1";
            this.splitContainerControl1.Panel1.Controls.Add(this.grpUser);
            this.splitContainerControl1.Panel1.Text = "Panel1";
            this.splitContainerControl1.Panel2.Controls.Add(this.splitContainerControl2);
            this.splitContainerControl1.Panel2.Controls.Add(this.panelControl2);
            this.splitContainerControl1.Panel2.Text = "Panel2";
            this.splitContainerControl1.Size = new System.Drawing.Size(1265, 506);
            this.splitContainerControl1.SplitterPosition = 406;
            this.splitContainerControl1.TabIndex = 7;
            this.splitContainerControl1.Text = "splitContainerControl1";
            // 
            // grpUser
            // 
            this.grpUser.Controls.Add(this.gridCtrlUser);
            this.grpUser.Dock = System.Windows.Forms.DockStyle.Fill;
            this.grpUser.Location = new System.Drawing.Point(0, 0);
            this.grpUser.Name = "grpUser";
            this.grpUser.Size = new System.Drawing.Size(406, 506);
            this.grpUser.TabIndex = 3;
            this.grpUser.Text = "用户";
            // 
            // gridCtrlUser
            // 
            this.gridCtrlUser.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridCtrlUser.Location = new System.Drawing.Point(2, 23);
            this.gridCtrlUser.MainView = this.gvUser;
            this.gridCtrlUser.Name = "gridCtrlUser";
            this.gridCtrlUser.Size = new System.Drawing.Size(402, 481);
            this.gridCtrlUser.TabIndex = 2;
            this.gridCtrlUser.UseEmbeddedNavigator = true;
            this.gridCtrlUser.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gvUser});
            // 
            // gvUser
            // 
            this.gvUser.Appearance.SelectedRow.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(128)))));
            this.gvUser.Appearance.SelectedRow.BackColor2 = System.Drawing.Color.Lime;
            this.gvUser.Appearance.SelectedRow.Options.UseBackColor = true;
            this.gvUser.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gcName,
            this.gcDesc,
            this.gcUserRole});
            this.gvUser.GridControl = this.gridCtrlUser;
            this.gvUser.GroupSummary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridGroupSummaryItem(DevExpress.Data.SummaryItemType.Count, "Name", null, "")});
            this.gvUser.Name = "gvUser";
            this.gvUser.OptionsBehavior.Editable = false;
            this.gvUser.OptionsDetail.EnableMasterViewMode = false;
            this.gvUser.OptionsDetail.ShowDetailTabs = false;
            this.gvUser.OptionsSelection.MultiSelect = true;
            this.gvUser.OptionsView.EnableAppearanceEvenRow = true;
            this.gvUser.OptionsView.EnableAppearanceOddRow = true;
            this.gvUser.PaintStyleName = "Skin";
            this.gvUser.SelectionChanged += new DevExpress.Data.SelectionChangedEventHandler(this.gvUser_SelectionChanged);
            // 
            // gcName
            // 
            this.gcName.Caption = "用户名";
            this.gcName.FieldName = "LoginName";
            this.gcName.Name = "gcName";
            this.gcName.Visible = true;
            this.gcName.VisibleIndex = 0;
            this.gcName.Width = 119;
            // 
            // gcDesc
            // 
            this.gcDesc.Caption = "描述";
            this.gcDesc.FieldName = "Description";
            this.gcDesc.Name = "gcDesc";
            this.gcDesc.Visible = true;
            this.gcDesc.VisibleIndex = 1;
            this.gcDesc.Width = 131;
            // 
            // gcUserRole
            // 
            this.gcUserRole.Caption = "权限组";
            this.gcUserRole.FieldName = "PermissionRoles";
            this.gcUserRole.Name = "gcUserRole";
            this.gcUserRole.Visible = true;
            this.gcUserRole.VisibleIndex = 2;
            this.gcUserRole.Width = 131;
            // 
            // splitContainerControl2
            // 
            this.splitContainerControl2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl2.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl2.Name = "splitContainerControl2";
            this.splitContainerControl2.Panel1.Controls.Add(this.groupControl1);
            this.splitContainerControl2.Panel1.Text = "Panel1";
            this.splitContainerControl2.Panel2.Controls.Add(this.groupControl2);
            this.splitContainerControl2.Panel2.Text = "Panel2";
            this.splitContainerControl2.Size = new System.Drawing.Size(853, 463);
            this.splitContainerControl2.SplitterPosition = 323;
            this.splitContainerControl2.TabIndex = 8;
            this.splitContainerControl2.Text = "splitContainerControl2";
            // 
            // groupControl1
            // 
            this.groupControl1.Controls.Add(this.treeListRole);
            this.groupControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControl1.Location = new System.Drawing.Point(0, 0);
            this.groupControl1.Name = "groupControl1";
            this.groupControl1.Size = new System.Drawing.Size(323, 463);
            this.groupControl1.TabIndex = 4;
            this.groupControl1.Text = "权限组";
            // 
            // treeListRole
            // 
            this.treeListRole.Appearance.SelectedRow.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(128)))));
            this.treeListRole.Appearance.SelectedRow.BackColor2 = System.Drawing.Color.Lime;
            this.treeListRole.Columns.AddRange(new DevExpress.XtraTreeList.Columns.TreeListColumn[] {
            this.colRoleName,
            this.colRoleDesc});
            this.treeListRole.Dock = System.Windows.Forms.DockStyle.Fill;
            this.treeListRole.Location = new System.Drawing.Point(2, 23);
            this.treeListRole.LookAndFeel.SkinName = "Office 2007 Blue";
            this.treeListRole.LookAndFeel.UseDefaultLookAndFeel = false;
            this.treeListRole.Name = "treeListRole";
            this.treeListRole.BeginUnboundLoad();
            this.treeListRole.AppendNode(new object[] {
            null,
            null}, -1);
            this.treeListRole.AppendNode(new object[] {
            null,
            null}, 0);
            this.treeListRole.AppendNode(new object[] {
            null,
            null}, 0);
            this.treeListRole.AppendNode(new object[] {
            null,
            null}, 0);
            this.treeListRole.AppendNode(new object[] {
            null,
            null}, -1);
            this.treeListRole.AppendNode(new object[] {
            null,
            null}, -1);
            this.treeListRole.AppendNode(new object[] {
            null,
            null}, -1);
            this.treeListRole.AppendNode(new object[] {
            null,
            null}, 6);
            this.treeListRole.AppendNode(new object[] {
            null,
            null}, 6);
            this.treeListRole.EndUnboundLoad();
            this.treeListRole.OptionsBehavior.AllowIndeterminateCheckState = true;
            this.treeListRole.OptionsView.ShowCheckBoxes = true;
            this.treeListRole.Size = new System.Drawing.Size(319, 438);
            this.treeListRole.TabIndex = 3;
            this.treeListRole.CellValueChanged += new DevExpress.XtraTreeList.CellValueChangedEventHandler(this.treeListRole_CellValueChanged);
            // 
            // colRoleName
            // 
            this.colRoleName.Caption = "名称";
            this.colRoleName.FieldName = "Name";
            this.colRoleName.MinWidth = 51;
            this.colRoleName.Name = "colRoleName";
            this.colRoleName.OptionsColumn.AllowEdit = false;
            this.colRoleName.OptionsColumn.AllowSort = false;
            this.colRoleName.Visible = true;
            this.colRoleName.VisibleIndex = 0;
            this.colRoleName.Width = 140;
            // 
            // colRoleDesc
            // 
            this.colRoleDesc.Caption = "描述";
            this.colRoleDesc.FieldName = "Description";
            this.colRoleDesc.Name = "colRoleDesc";
            this.colRoleDesc.OptionsColumn.AllowEdit = false;
            this.colRoleDesc.OptionsColumn.AllowSort = false;
            this.colRoleDesc.Visible = true;
            this.colRoleDesc.VisibleIndex = 1;
            this.colRoleDesc.Width = 141;
            // 
            // groupControl2
            // 
            this.groupControl2.Controls.Add(this.treeListFunc);
            this.groupControl2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControl2.Location = new System.Drawing.Point(0, 0);
            this.groupControl2.Name = "groupControl2";
            this.groupControl2.Size = new System.Drawing.Size(524, 463);
            this.groupControl2.TabIndex = 4;
            this.groupControl2.Text = "功能组";
            // 
            // treeListFunc
            // 
            this.treeListFunc.Columns.AddRange(new DevExpress.XtraTreeList.Columns.TreeListColumn[] {
            this.treeListColumn2,
            this.treeListColumn3,
            this.colFuncExportPermit,
            this.colFuncExportZip,
            this.colFuncExportLog,
            this.colFuncExportCause,
            this.colFuncIsNeedHideKeyInfo});
            this.treeListFunc.Dock = System.Windows.Forms.DockStyle.Fill;
            this.treeListFunc.Enabled = false;
            this.treeListFunc.Location = new System.Drawing.Point(2, 23);
            this.treeListFunc.LookAndFeel.SkinName = "Office 2007 Blue";
            this.treeListFunc.LookAndFeel.UseDefaultLookAndFeel = false;
            this.treeListFunc.Name = "treeListFunc";
            this.treeListFunc.BeginUnboundLoad();
            this.treeListFunc.AppendNode(new object[] {
            null,
            null,
            null,
            null,
            null,
            null,
            null}, -1);
            this.treeListFunc.AppendNode(new object[] {
            null,
            null,
            null,
            null,
            null,
            null,
            null}, 0);
            this.treeListFunc.AppendNode(new object[] {
            null,
            null,
            null,
            null,
            null,
            null,
            null}, 0);
            this.treeListFunc.AppendNode(new object[] {
            null,
            null,
            null,
            null,
            null,
            null,
            null}, 0);
            this.treeListFunc.AppendNode(new object[] {
            null,
            null,
            null,
            null,
            null,
            null,
            null}, -1);
            this.treeListFunc.AppendNode(new object[] {
            null,
            null,
            null,
            null,
            null,
            null,
            null}, -1);
            this.treeListFunc.AppendNode(new object[] {
            null,
            null,
            null,
            null,
            null,
            null,
            null}, -1);
            this.treeListFunc.AppendNode(new object[] {
            null,
            null,
            null,
            null,
            null,
            null,
            null}, 6);
            this.treeListFunc.AppendNode(new object[] {
            null,
            null,
            null,
            null,
            null,
            null,
            null}, 6);
            this.treeListFunc.EndUnboundLoad();
            this.treeListFunc.OptionsBehavior.AllowIndeterminateCheckState = true;
            this.treeListFunc.OptionsView.ShowCheckBoxes = true;
            this.treeListFunc.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repositoryItemCheckEdit1});
            this.treeListFunc.Size = new System.Drawing.Size(520, 438);
            this.treeListFunc.TabIndex = 2;
            this.treeListFunc.BeforeCheckNode += new DevExpress.XtraTreeList.CheckNodeEventHandler(this.treeListFunc_BeforeCheckNode);
            this.treeListFunc.AfterCheckNode += new DevExpress.XtraTreeList.NodeEventHandler(this.treeListFunc_AfterCheckNode);
            // 
            // treeListColumn2
            // 
            this.treeListColumn2.Caption = "名称";
            this.treeListColumn2.FieldName = "Name";
            this.treeListColumn2.MinWidth = 51;
            this.treeListColumn2.Name = "treeListColumn2";
            this.treeListColumn2.OptionsColumn.AllowEdit = false;
            this.treeListColumn2.OptionsColumn.AllowSort = false;
            this.treeListColumn2.Visible = true;
            this.treeListColumn2.VisibleIndex = 0;
            this.treeListColumn2.Width = 140;
            // 
            // treeListColumn3
            // 
            this.treeListColumn3.Caption = "描述";
            this.treeListColumn3.FieldName = "Description";
            this.treeListColumn3.Name = "treeListColumn3";
            this.treeListColumn3.OptionsColumn.AllowEdit = false;
            this.treeListColumn3.OptionsColumn.AllowSort = false;
            this.treeListColumn3.Visible = true;
            this.treeListColumn3.VisibleIndex = 1;
            this.treeListColumn3.Width = 141;
            // 
            // colFuncExportPermit
            // 
            this.colFuncExportPermit.Caption = "允许导出结果";
            this.colFuncExportPermit.ColumnEdit = this.repositoryItemCheckEdit1;
            this.colFuncExportPermit.FieldName = "IsCanExportResult";
            this.colFuncExportPermit.Name = "colFuncExportPermit";
            this.colFuncExportPermit.Visible = true;
            this.colFuncExportPermit.VisibleIndex = 2;
            // 
            // repositoryItemCheckEdit1
            // 
            this.repositoryItemCheckEdit1.AutoHeight = false;
            this.repositoryItemCheckEdit1.Name = "repositoryItemCheckEdit1";
            // 
            // colFuncExportZip
            // 
            this.colFuncExportZip.Caption = "压缩加密导出";
            this.colFuncExportZip.ColumnEdit = this.repositoryItemCheckEdit1;
            this.colFuncExportZip.FieldName = "IsExportToZip";
            this.colFuncExportZip.Name = "colFuncExportZip";
            this.colFuncExportZip.Visible = true;
            this.colFuncExportZip.VisibleIndex = 3;
            // 
            // colFuncExportLog
            // 
            this.colFuncExportLog.Caption = "写导出日志";
            this.colFuncExportLog.ColumnEdit = this.repositoryItemCheckEdit1;
            this.colFuncExportLog.FieldName = "IsNeedExportLog";
            this.colFuncExportLog.Name = "colFuncExportLog";
            this.colFuncExportLog.Visible = true;
            this.colFuncExportLog.VisibleIndex = 4;
            // 
            // colFuncExportCause
            // 
            this.colFuncExportCause.Caption = "写导出原因";
            this.colFuncExportCause.ColumnEdit = this.repositoryItemCheckEdit1;
            this.colFuncExportCause.FieldName = "IsNeedExportCause";
            this.colFuncExportCause.Name = "colFuncExportCause";
            this.colFuncExportCause.Visible = true;
            this.colFuncExportCause.VisibleIndex = 5;
            // 
            // colFuncIsNeedHideKeyInfo
            // 
            this.colFuncIsNeedHideKeyInfo.Caption = "脱敏显示";
            this.colFuncIsNeedHideKeyInfo.ColumnEdit = this.repositoryItemCheckEdit1;
            this.colFuncIsNeedHideKeyInfo.FieldName = "IsHideKeyInfo";
            this.colFuncIsNeedHideKeyInfo.Name = "colFuncIsNeedHideKeyInfo";
            this.colFuncIsNeedHideKeyInfo.Visible = true;
            this.colFuncIsNeedHideKeyInfo.VisibleIndex = 6;
            // 
            // panelControl2
            // 
            this.panelControl2.Controls.Add(this.label1);
            this.panelControl2.Controls.Add(this.btnAddRole);
            this.panelControl2.Controls.Add(this.btnSubmit);
            this.panelControl2.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.panelControl2.Location = new System.Drawing.Point(0, 463);
            this.panelControl2.Name = "panelControl2";
            this.panelControl2.Size = new System.Drawing.Size(853, 43);
            this.panelControl2.TabIndex = 0;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(7, 19);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(317, 12);
            this.label1.TabIndex = 8;
            this.label1.Text = "注意：修改后，点击【提交修改】按钮才会更新到数据库。";
            // 
            // btnAddRole
            // 
            this.btnAddRole.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnAddRole.Location = new System.Drawing.Point(645, 8);
            this.btnAddRole.Name = "btnAddRole";
            this.btnAddRole.Size = new System.Drawing.Size(87, 27);
            this.btnAddRole.TabIndex = 7;
            this.btnAddRole.Text = "添加权限组";
            this.btnAddRole.Click += new System.EventHandler(this.btnAddRole_Click);
            // 
            // btnSubmit
            // 
            this.btnSubmit.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnSubmit.Location = new System.Drawing.Point(754, 8);
            this.btnSubmit.Name = "btnSubmit";
            this.btnSubmit.Size = new System.Drawing.Size(87, 27);
            this.btnSubmit.TabIndex = 7;
            this.btnSubmit.Text = "提交修改";
            this.btnSubmit.Click += new System.EventHandler(this.btnSubmit_Click);
            // 
            // UserFuncRightsOptionForm
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1265, 506);
            this.Controls.Add(this.splitContainerControl1);
            this.Name = "UserFuncRightsOptionForm";
            this.Text = "用户功能点权限设定";
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).EndInit();
            this.splitContainerControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.grpUser)).EndInit();
            this.grpUser.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridCtrlUser)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvUser)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl2)).EndInit();
            this.splitContainerControl2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.treeListRole)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).EndInit();
            this.groupControl2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.treeListFunc)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemCheckEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl2)).EndInit();
            this.panelControl2.ResumeLayout(false);
            this.panelControl2.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl1;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl2;
        private DevExpress.XtraTreeList.TreeList treeListFunc;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumn2;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumn3;
        private DevExpress.XtraGrid.GridControl gridCtrlUser;
        private DevExpress.XtraGrid.Views.Grid.GridView gvUser;
        private DevExpress.XtraGrid.Columns.GridColumn gcName;
        private DevExpress.XtraGrid.Columns.GridColumn gcDesc;
        private DevExpress.XtraGrid.Columns.GridColumn gcUserRole;
        private DevExpress.XtraEditors.GroupControl grpUser;
        private DevExpress.XtraEditors.GroupControl groupControl1;
        private DevExpress.XtraEditors.PanelControl panelControl2;
        private DevExpress.XtraEditors.GroupControl groupControl2;
        private DevExpress.XtraEditors.SimpleButton btnAddRole;
        private DevExpress.XtraEditors.SimpleButton btnSubmit;
        private DevExpress.XtraTreeList.TreeList treeListRole;
        private DevExpress.XtraTreeList.Columns.TreeListColumn colRoleName;
        private DevExpress.XtraTreeList.Columns.TreeListColumn colRoleDesc;
        private System.Windows.Forms.Label label1;
        private DevExpress.XtraTreeList.Columns.TreeListColumn colFuncExportPermit;
        private DevExpress.XtraEditors.Repository.RepositoryItemCheckEdit repositoryItemCheckEdit1;
        private DevExpress.XtraTreeList.Columns.TreeListColumn colFuncExportZip;
        private DevExpress.XtraTreeList.Columns.TreeListColumn colFuncExportLog;
        private DevExpress.XtraTreeList.Columns.TreeListColumn colFuncExportCause;
        private DevExpress.XtraTreeList.Columns.TreeListColumn colFuncIsNeedHideKeyInfo;

    }
}