﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class DIYFastFadingByRegion_GSM : DIYFastFadingByRegion_GSCAN
    {
        protected int rxLevMin = -80;
        public DIYFastFadingByRegion_GSM(MainModel mainModel)
            : base(mainModel)
        {
            FilterSampleByRegion = false;
            IncludeEvent = false;
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.GSM_VOICE);
            ServiceTypes.Add(ServiceType.GPRS_DATA);
            ServiceTypes.Add(ServiceType.EDGE_DATA);
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 12000, 12059, this.Name);//////
        }

        readonly FastFadingDlg_GSM conditionDlg = new FastFadingDlg_GSM();
        protected override bool getCondition()
        {
            if (conditionDlg.ShowDialog() == DialogResult.OK)
            {
                conditionDlg.GetFilterCondition(out rxLevMin, out secondLast, out secondFading, out rxLevDValueFading);
                return true;
            }
            return false;
        }

        protected override void doStatWithQuery()
        {
            try
            {
                foreach (DTFileDataManager fileDataManager in MainModel.DTDataManager.FileDataManagers)
                {
                    List<TestPoint> testPointList = fileDataManager.TestPoints;
                    clearIndermediateVariable();
                    dealTPInfo(testPointList);
                }
            }
            catch
            {
                //continue
            }
        }

        private void dealTPInfo(List<TestPoint> testPointList)
        {
            for (int i = 0; i < testPointList.Count; i++)
            {
                TestPoint testPoint = testPointList[i];
                if (isValidTestPoint(testPoint))
                {
                    Dictionary<string, float> cellRxLevDic = new Dictionary<string, float>();
                    float? rxLevMain = (float?)(short?)testPoint["RxLevSub"];
                    Cell mainCell = CellManager.GetInstance().GetNearestCell(testPoint.DateTime, (ushort?)(int?)testPoint["LAC"], (ushort?)(int?)testPoint["CI"], (short?)testPoint["BCCH"], (byte?)testPoint["BSIC"], testPoint.Longitude, testPoint.Latitude);
                    if (mainCell != null)
                    {
                        cellRxLevDic[mainCell.Name] = (float)rxLevMain;
                        judgeCell(rxLevMain, mainCell.Name, testPointList, i);
                    }

                    judgeTestPoint(testPointList, i, cellRxLevDic);
                }
                else
                {
                    clearIndermediateVariable();
                }
            }
        }

        /// <summary>
        /// 4件事：
        /// 1.主服大于等于-80db的放入小区队列；
        /// 2.主服大于等于-80db持续满5秒的小区放入对象小区队列；
        /// 3.主服大于等于-80db并且前一个采样点中有该小区，延续到当前采样点；
        /// 4.主服小于-80db，小区队列中移除该小区。
        /// </summary>
        /// <param name="rxLevMax">主服场强</param>
        /// <param name="cell">当前小区名</param>
        /// <param name="testPointList">采样点列表</param>
        /// <param name="curIndex">当前采样点序号</param>
        protected void judgeCell(float? rxLevMain, string cell, List<TestPoint> testPointList, int curIndex)
        {
            try
            {
                TestPoint testPoint = testPointList[curIndex];
                if (rxLevMain >= rxLevMin)    //主服大于等于-80db
                {
                    if (!cellLastDic.ContainsKey(cell))
                    {
                        CellLast cellLast = new CellLast();
                        cellLastDic[cell] = cellLast;
                    }
                    cellLastDic[cell].Add(testPoint, (float)rxLevMain);
                    if (cellLastDic[cell].SecondLast >= secondLast) //持续5秒，满足前提条件
                    {
                        if (!tpCellValidDic.ContainsKey(curIndex))
                        {
                            Dictionary<string, CellLast> cellValidDic = new Dictionary<string, CellLast>();
                            tpCellValidDic[curIndex] = cellValidDic;
                        }
                        tpCellValidDic[curIndex][cell] = cellLastDic[cell];
                        cellLastDic.Remove(cell);
                    }

                    //更新已经满5秒，未形成快衰的点，延续到下个点判断
                    List<int> tpList = new List<int>(tpCellValidDic.Keys);
                    foreach (int index in tpList)
                    {
                        if (index == curIndex - 1)//当前点前一个采样点
                        {
                            dealSameTP(rxLevMain, cell, curIndex, testPoint, index);
                        }
                    }
                    return;
                }

                //未满10秒移除
                if (cellLastDic.ContainsKey(cell))
                {
                    cellLastDic.Remove(cell);
                }
            }
            catch
            {
                //continue
            }
        }

        private void dealSameTP(float? rxLevMain, string cell, int curIndex, TestPoint testPoint, int index)
        {
            List<string> tmpCellList = new List<string>(tpCellValidDic[index].Keys);
            foreach (string cellTP in tmpCellList)
            {
                if (cell == cellTP)
                {
                    //前一个采样点中小区延续到当前采样点
                    if (!tpCellValidDic.ContainsKey(curIndex))
                    {
                        Dictionary<string, CellLast> cellValidDic = new Dictionary<string, CellLast>();
                        tpCellValidDic[curIndex] = cellValidDic;
                    }
                    if (!tpCellValidDic[curIndex].ContainsKey(cell))
                    {
                        tpCellValidDic[curIndex][cell] = tpCellValidDic[index][cell];
                    }
                    tpCellValidDic[curIndex][cell].Add(testPoint, (float)rxLevMain);

                    //前一个采样点中移除该小区，如果移除完采样点没有其他小区，移除采样点
                    tpCellValidDic[index].Remove(cell);
                    if (tpCellValidDic[index].Count == 0)
                    {
                        tpCellValidDic.Remove(index);
                    }
                }
            }
        }
    }
}
