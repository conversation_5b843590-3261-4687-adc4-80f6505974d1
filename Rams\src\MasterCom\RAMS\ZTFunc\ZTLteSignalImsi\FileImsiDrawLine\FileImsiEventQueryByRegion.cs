﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc.LteSignalImsi;

namespace MasterCom.RAMS.ZTFunc
{
    public class FileImsiEventQueryByRegion : DIYEventByRegion
    {
        public FileImsiEventQueryByRegion(MainModel mainModel) : base(mainModel)
        {
            showEventChooser = false;
            showForm = false;
            saveAsFileEventsDic = false;
            IsQueryAllEvents = true;
        }

        public override string Name
        {
            get { return "区域事件"; }
        }

        public override string IconName
        {
            get { return "Images/regionevt.gif"; }
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.Region;
        }

        protected override bool isValidCondition()
        {
            if (setForm == null)
            {
                setForm = new FileImsiDrawLineSetForm();
            }
            return setForm.ShowDialog() == System.Windows.Forms.DialogResult.OK;
        }

        protected override void getResultAfterQuery()
        {
            FileImsiEventDrawLineLayer layer = FileImsiEventDrawLineLayer.Instance;
            layer.IsGisIntervalLimit = setForm.IsGisIntervalLimit;
            layer.GisIntervalMinutes = setForm.GisIntervalMinutes;

            base.ClearEventList();
        }

        protected override void fireShowFormAfterQuery()
        {
            MainModel.MainForm.GetMapForm().GoToView(MainModel.SearchGeometrys.RegionBounds);
        }

        private FileImsiDrawLineSetForm setForm = null;
    }
}
