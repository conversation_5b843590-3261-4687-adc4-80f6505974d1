﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Stat;
using MasterCom.RAMS.Stat.Data;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.Net
{
    public class DIYReplayFileByMultiService : DIYReplayFileQuery
    {
        /// <summary>
        /// 业务对应的回放模版
        /// </summary>
        protected Dictionary<int, DIYReplayContentOption> serviceOptionDic = new Dictionary<int, DIYReplayContentOption>();

        public DIYReplayFileByMultiService(MainModel mainModel) : base(mainModel)
        {
        }

        /// <summary>
        /// 回放内容设置
        /// </summary>
        protected virtual void GetDIYReplayOption()
        {
            DIYReplayOptionByMultiServiceDlg dlg = new DIYReplayOptionByMultiServiceDlg(Condition);
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                serviceOptionDic = null;
                return;
            }
            isAutoLoadCQTPicture = dlg.IsAutoLoadCQTPicture;
            serviceOptionDic = dlg.ReplayOptionDic;
        }

        /// <summary>
        /// 重写了回放模板和MapSerialTheme的设置
        /// </summary>
        protected override void query()
        {
            GetDIYReplayOption();
            if (serviceOptionDic==null)
            {
                return;
            }
            WaitBox.CanCancel = true;
            WaitBox.Text = "正在查询...";
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, condition.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
                WaitBox.Text = "正在查询...";
                MainModel.DTDataManager.Clear();
                MainModel.SelectedTestPoints.Clear();
                MainModel.SelectedEvents.Clear();
                MainModel.SelectedMessage = null;
                MainModel.IsDrawEventResult = false;
                MainModel.IsFileReplayByCompareMode = Condition.isCompareMode;
                MainModel.IsFileReplayByMTRMode = condition.isMTRMode;
                MainModel.IsFileReplayByMTRToLogMode = condition.isMTRToLogMode;

                ResetMapSerialTheme();
                WaitBox.Show("开始统计数据...", queryInThread, clientProxy);
                doPostReplayAction();
                foreach (DIYReplayContentOption option in serviceOptionDic.Values)
                {
                    if (option.DefaultSerialThemeName != null)
                    {
                        SetMapSerialTheme(option.DefaultSerialThemeName);
                    }
                }
                MainModel.RefreshLegend();
            }
            finally
            {
                MainModel.FireDTDataChanged(this);
                clientProxy.Close();
            }
        }

        protected override void queryInThread(object o)
        {
            try
            {
                ClientProxy clientProxy = (ClientProxy)o;
                Package package = clientProxy.Package;
                int index = 0;
                MainModel.SelectedFileInfo = Condition.FileInfos[0];
                foreach (MasterCom.RAMS.Model.FileInfo fileInfo in Condition.FileInfos)
                {
                    if (IsMutCitys)//支持同时回放多地市的文件
                    {
                        clientProxy.Close();
                        clientProxy = new ClientProxy();
                        if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName
                            , MainModel.User.Password, fileInfo.DistrictID) != ConnectResult.Success)
                        {
                            ErrorInfo = fileInfo.Name + " 回放失败，其余文件正在继续!!!";
                            continue;
                        }
                        package = clientProxy.Package;
                    }
                    if (fileInfo.FileTypeDescription.Contains("鼎利"))
                    {//鼎利测试文件以左下角为坐标原点
                        MainModel.CQTPlanImgOrigin = MainModel.OriginType.LeftBottom;
                    }
                    else if (fileInfo.FileTypeDescription.Contains("烽火"))
                    {//烽火测试文件以左上角为坐标原点
                        MainModel.CQTPlanImgOrigin = MainModel.OriginType.LeftTop;
                    }
                    index = setMTRModeInfo(index, fileInfo);
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                    WaitBox.Text = "开始获取[" + fileInfo.Name + "]数据...";

                    // 设置回放模板
                    replayContentOption = serviceOptionDic[fileInfo.ServiceType];
                    queryReplayInfo(clientProxy, package, fileInfo);
                }
            }
            catch (DebugAssertException dbgE)
            {
                MessageBox.Show("Debug Assert 失败:" + dbgE.ToString());
            }
            catch (Exception ex)
            {
                log.Error(ex.StackTrace);
            }
            finally
            {
                WaitBox.Close();
            }
        }

        private int setMTRModeInfo(int index, FileInfo fileInfo)
        {
            if (MainModel.IsFileReplayByMTRMode)
            {
                if (index == 1)
                {
                    fileIdMTR = fileInfo.ID;
                    canGetHeaderMTR = true;
                }
                if (index > 0)
                {
                    fileOffsetTimeMS = fileInfo.OffsetTimeMS;
                }
            }
            if (MainModel.IsFileReplayByMTRToLogMode && index == 0)
            {
                fileOffsetTimeMS = fileInfo.OffsetTimeMS;
            }
            index++;
            fileIndex++;
            return index;
        }

        protected void ResetMapSerialTheme()
        {
            try
            {
                foreach (MapSerialInfo info in MainModel.MainForm.GetMapForm().GetDTLayer().SerialInfos)
                {
                    info.Visible = false;
                }
            }
            catch (Exception ex)
            {
                log.Error(ex.StackTrace);
            }
        }

        protected void SetMapSerialTheme(string themeName)
        {
            try
            {
                foreach (MapSerialInfo info in MainModel.MainForm.GetMapForm().GetDTLayer().SerialInfos)
                {
                    if (info.Name.ToUpper() == themeName.ToUpper())
                    {
                        info.Visible = true;
                    }
                }
            }
            catch (Exception ex)
            {
                log.Error(ex.StackTrace);
            }
        }
    }
}
