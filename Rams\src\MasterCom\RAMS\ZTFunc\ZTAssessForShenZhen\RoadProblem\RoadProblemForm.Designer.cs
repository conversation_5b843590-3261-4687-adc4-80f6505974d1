﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class RoadProblemForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.panelControl1 = new DevExpress.XtraEditors.PanelControl();
            this.edtRoadName = new DevExpress.XtraEditors.TextEdit();
            this.cbxLevel = new DevExpress.XtraEditors.CheckedComboBoxEdit();
            this.btnSetting = new DevExpress.XtraEditors.SimpleButton();
            this.labelControl6 = new DevExpress.XtraEditors.LabelControl();
            this.cbxStatus = new DevExpress.XtraEditors.ComboBoxEdit();
            this.btnQuery = new DevExpress.XtraEditors.SimpleButton();
            this.cbxAreaName = new DevExpress.XtraEditors.ComboBoxEdit();
            this.labelControl5 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.splitContainerControl1 = new DevExpress.XtraEditors.SplitContainerControl();
            this.gridControlRoad = new DevExpress.XtraGrid.GridControl();
            this.contextMenuStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExportToExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.gridViewRoad = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumnID = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnAreaName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnLevel = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnRoadName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnStatus = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnCreatedMonth = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnLastAbnormalMonth = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnLastTestMonth = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnClosedMonth = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnGoodDaysCount = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnValidateStatus = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnGridRepeatCount = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridControlRoadKPI = new DevExpress.XtraGrid.GridControl();
            this.gridViewRoadKPI = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.miQuerySample = new System.Windows.Forms.ToolStripMenuItem();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl1)).BeginInit();
            this.panelControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.edtRoadName.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbxLevel.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbxStatus.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbxAreaName.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).BeginInit();
            this.splitContainerControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlRoad)).BeginInit();
            this.contextMenuStrip.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewRoad)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlRoadKPI)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewRoadKPI)).BeginInit();
            this.SuspendLayout();
            // 
            // panelControl1
            // 
            this.panelControl1.Controls.Add(this.edtRoadName);
            this.panelControl1.Controls.Add(this.cbxLevel);
            this.panelControl1.Controls.Add(this.btnSetting);
            this.panelControl1.Controls.Add(this.labelControl6);
            this.panelControl1.Controls.Add(this.cbxStatus);
            this.panelControl1.Controls.Add(this.btnQuery);
            this.panelControl1.Controls.Add(this.cbxAreaName);
            this.panelControl1.Controls.Add(this.labelControl5);
            this.panelControl1.Controls.Add(this.labelControl4);
            this.panelControl1.Controls.Add(this.labelControl2);
            this.panelControl1.Dock = System.Windows.Forms.DockStyle.Top;
            this.panelControl1.Location = new System.Drawing.Point(0, 0);
            this.panelControl1.Name = "panelControl1";
            this.panelControl1.Size = new System.Drawing.Size(1007, 46);
            this.panelControl1.TabIndex = 0;
            // 
            // edtRoadName
            // 
            this.edtRoadName.Location = new System.Drawing.Point(563, 12);
            this.edtRoadName.Name = "edtRoadName";
            this.edtRoadName.Size = new System.Drawing.Size(100, 21);
            this.edtRoadName.TabIndex = 15;
            // 
            // cbxLevel
            // 
            this.cbxLevel.Location = new System.Drawing.Point(226, 12);
            this.cbxLevel.Name = "cbxLevel";
            this.cbxLevel.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cbxLevel.Properties.SelectAllItemCaption = "(选中全部)";
            this.cbxLevel.Size = new System.Drawing.Size(105, 21);
            this.cbxLevel.TabIndex = 14;
            // 
            // btnSetting
            // 
            this.btnSetting.Location = new System.Drawing.Point(942, 11);
            this.btnSetting.Name = "btnSetting";
            this.btnSetting.Size = new System.Drawing.Size(53, 23);
            this.btnSetting.TabIndex = 13;
            this.btnSetting.Text = "设置";
            // 
            // labelControl6
            // 
            this.labelControl6.Location = new System.Drawing.Point(21, 15);
            this.labelControl6.Name = "labelControl6";
            this.labelControl6.Size = new System.Drawing.Size(36, 14);
            this.labelControl6.TabIndex = 12;
            this.labelControl6.Text = "状态：";
            // 
            // cbxStatus
            // 
            this.cbxStatus.Location = new System.Drawing.Point(63, 12);
            this.cbxStatus.Name = "cbxStatus";
            this.cbxStatus.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cbxStatus.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            this.cbxStatus.Size = new System.Drawing.Size(76, 21);
            this.cbxStatus.TabIndex = 1;
            // 
            // btnQuery
            // 
            this.btnQuery.Location = new System.Drawing.Point(870, 11);
            this.btnQuery.Name = "btnQuery";
            this.btnQuery.Size = new System.Drawing.Size(53, 23);
            this.btnQuery.TabIndex = 6;
            this.btnQuery.Text = "查询";
            this.btnQuery.Click += new System.EventHandler(this.btnQuery_Click);
            // 
            // cbxAreaName
            // 
            this.cbxAreaName.Location = new System.Drawing.Point(395, 12);
            this.cbxAreaName.Name = "cbxAreaName";
            this.cbxAreaName.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cbxAreaName.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            this.cbxAreaName.Size = new System.Drawing.Size(79, 21);
            this.cbxAreaName.TabIndex = 4;
            // 
            // labelControl5
            // 
            this.labelControl5.Location = new System.Drawing.Point(497, 15);
            this.labelControl5.Name = "labelControl5";
            this.labelControl5.Size = new System.Drawing.Size(60, 14);
            this.labelControl5.TabIndex = 10;
            this.labelControl5.Text = "道路名称：";
            // 
            // labelControl4
            // 
            this.labelControl4.Location = new System.Drawing.Point(353, 15);
            this.labelControl4.Name = "labelControl4";
            this.labelControl4.Size = new System.Drawing.Size(36, 14);
            this.labelControl4.TabIndex = 8;
            this.labelControl4.Text = "片区：";
            // 
            // labelControl2
            // 
            this.labelControl2.Location = new System.Drawing.Point(160, 15);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(60, 14);
            this.labelControl2.TabIndex = 4;
            this.labelControl2.Text = "道路等级：";
            // 
            // splitContainerControl1
            // 
            this.splitContainerControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl1.FixedPanel = DevExpress.XtraEditors.SplitFixedPanel.Panel2;
            this.splitContainerControl1.Horizontal = false;
            this.splitContainerControl1.Location = new System.Drawing.Point(0, 46);
            this.splitContainerControl1.Name = "splitContainerControl1";
            this.splitContainerControl1.Panel1.Controls.Add(this.gridControlRoad);
            this.splitContainerControl1.Panel1.Text = "Panel1";
            this.splitContainerControl1.Panel2.Controls.Add(this.gridControlRoadKPI);
            this.splitContainerControl1.Panel2.Text = "Panel2";
            this.splitContainerControl1.Size = new System.Drawing.Size(1007, 438);
            this.splitContainerControl1.SplitterPosition = 160;
            this.splitContainerControl1.TabIndex = 1;
            this.splitContainerControl1.Text = "splitContainerControl1";
            // 
            // gridControlRoad
            // 
            this.gridControlRoad.ContextMenuStrip = this.contextMenuStrip;
            this.gridControlRoad.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlRoad.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gridControlRoad.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gridControlRoad.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gridControlRoad.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gridControlRoad.EmbeddedNavigator.Buttons.Remove.Visible = false;
            this.gridControlRoad.Location = new System.Drawing.Point(0, 0);
            this.gridControlRoad.MainView = this.gridViewRoad;
            this.gridControlRoad.Name = "gridControlRoad";
            this.gridControlRoad.Size = new System.Drawing.Size(1007, 272);
            this.gridControlRoad.TabIndex = 3;
            this.gridControlRoad.UseEmbeddedNavigator = true;
            this.gridControlRoad.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridViewRoad});
            // 
            // contextMenuStrip
            // 
            this.contextMenuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miQuerySample,
            this.miExportToExcel});
            this.contextMenuStrip.Name = "contextMenuStrip";
            this.contextMenuStrip.Size = new System.Drawing.Size(153, 70);
            this.contextMenuStrip.Opening += new System.ComponentModel.CancelEventHandler(this.contextMenuStrip_Opening);
            // 
            // miExportToExcel
            // 
            this.miExportToExcel.Name = "miExportToExcel";
            this.miExportToExcel.Size = new System.Drawing.Size(152, 22);
            this.miExportToExcel.Text = "导出Excel...";
            this.miExportToExcel.Click += new System.EventHandler(this.miExportToExcel_Click);
            // 
            // gridViewRoad
            // 
            this.gridViewRoad.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumnID,
            this.gridColumnAreaName,
            this.gridColumnLevel,
            this.gridColumnRoadName,
            this.gridColumnStatus,
            this.gridColumnCreatedMonth,
            this.gridColumnLastAbnormalMonth,
            this.gridColumnLastTestMonth,
            this.gridColumnClosedMonth,
            this.gridColumnGoodDaysCount,
            this.gridColumnValidateStatus,
            this.gridColumnGridRepeatCount});
            this.gridViewRoad.GridControl = this.gridControlRoad;
            this.gridViewRoad.Name = "gridViewRoad";
            this.gridViewRoad.OptionsBehavior.Editable = false;
            this.gridViewRoad.OptionsView.ColumnAutoWidth = false;
            this.gridViewRoad.OptionsView.ShowDetailButtons = false;
            this.gridViewRoad.OptionsView.ShowGroupPanel = false;
            this.gridViewRoad.FocusedRowChanged += new DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventHandler(this.gridViewGrid_FocusedRowChanged);
            this.gridViewRoad.DoubleClick += new System.EventHandler(this.gridViewGrid_DoubleClick);
            // 
            // gridColumnID
            // 
            this.gridColumnID.Caption = "问题点编号";
            this.gridColumnID.FieldName = "ID";
            this.gridColumnID.Name = "gridColumnID";
            this.gridColumnID.Visible = true;
            this.gridColumnID.VisibleIndex = 0;
            // 
            // gridColumnAreaName
            // 
            this.gridColumnAreaName.Caption = "片区";
            this.gridColumnAreaName.FieldName = "AreaName";
            this.gridColumnAreaName.Name = "gridColumnAreaName";
            this.gridColumnAreaName.Visible = true;
            this.gridColumnAreaName.VisibleIndex = 1;
            // 
            // gridColumnLevel
            // 
            this.gridColumnLevel.Caption = "道路级别";
            this.gridColumnLevel.FieldName = "Level";
            this.gridColumnLevel.Name = "gridColumnLevel";
            this.gridColumnLevel.Visible = true;
            this.gridColumnLevel.VisibleIndex = 2;
            // 
            // gridColumnRoadName
            // 
            this.gridColumnRoadName.Caption = "道路名";
            this.gridColumnRoadName.FieldName = "RoadName";
            this.gridColumnRoadName.Name = "gridColumnRoadName";
            this.gridColumnRoadName.Visible = true;
            this.gridColumnRoadName.VisibleIndex = 3;
            // 
            // gridColumnStatus
            // 
            this.gridColumnStatus.Caption = "状态";
            this.gridColumnStatus.FieldName = "StatusString";
            this.gridColumnStatus.Name = "gridColumnStatus";
            this.gridColumnStatus.Visible = true;
            this.gridColumnStatus.VisibleIndex = 4;
            // 
            // gridColumnCreatedMonth
            // 
            this.gridColumnCreatedMonth.Caption = "创建月份";
            this.gridColumnCreatedMonth.FieldName = "CreatedMonth";
            this.gridColumnCreatedMonth.Name = "gridColumnCreatedMonth";
            this.gridColumnCreatedMonth.Visible = true;
            this.gridColumnCreatedMonth.VisibleIndex = 5;
            // 
            // gridColumnLastAbnormalMonth
            // 
            this.gridColumnLastAbnormalMonth.Caption = "最后异常月份";
            this.gridColumnLastAbnormalMonth.FieldName = "LastAbnormalMonth";
            this.gridColumnLastAbnormalMonth.Name = "gridColumnLastAbnormalMonth";
            this.gridColumnLastAbnormalMonth.Visible = true;
            this.gridColumnLastAbnormalMonth.VisibleIndex = 6;
            this.gridColumnLastAbnormalMonth.Width = 105;
            // 
            // gridColumnLastTestMonth
            // 
            this.gridColumnLastTestMonth.Caption = "最后测试月份";
            this.gridColumnLastTestMonth.FieldName = "LastTestMonth";
            this.gridColumnLastTestMonth.Name = "gridColumnLastTestMonth";
            this.gridColumnLastTestMonth.Visible = true;
            this.gridColumnLastTestMonth.VisibleIndex = 7;
            this.gridColumnLastTestMonth.Width = 95;
            // 
            // gridColumnClosedMonth
            // 
            this.gridColumnClosedMonth.Caption = "关闭月份";
            this.gridColumnClosedMonth.FieldName = "ClosedMonth";
            this.gridColumnClosedMonth.Name = "gridColumnClosedMonth";
            this.gridColumnClosedMonth.Visible = true;
            this.gridColumnClosedMonth.VisibleIndex = 8;
            // 
            // gridColumnGoodDaysCount
            // 
            this.gridColumnGoodDaysCount.Caption = "验证正常次数";
            this.gridColumnGoodDaysCount.FieldName = "GoodDaysCount";
            this.gridColumnGoodDaysCount.Name = "gridColumnGoodDaysCount";
            this.gridColumnGoodDaysCount.Visible = true;
            this.gridColumnGoodDaysCount.VisibleIndex = 9;
            this.gridColumnGoodDaysCount.Width = 99;
            // 
            // gridColumnValidateStatus
            // 
            this.gridColumnValidateStatus.Caption = "验证测试状态";
            this.gridColumnValidateStatus.FieldName = "ValidateStatus";
            this.gridColumnValidateStatus.Name = "gridColumnValidateStatus";
            this.gridColumnValidateStatus.Visible = true;
            this.gridColumnValidateStatus.VisibleIndex = 10;
            this.gridColumnValidateStatus.Width = 99;
            // 
            // gridColumnGridRepeatCount
            // 
            this.gridColumnGridRepeatCount.Caption = "道路问题次数";
            this.gridColumnGridRepeatCount.FieldName = "GridRepeatCount";
            this.gridColumnGridRepeatCount.Name = "gridColumnGridRepeatCount";
            this.gridColumnGridRepeatCount.Visible = true;
            this.gridColumnGridRepeatCount.VisibleIndex = 11;
            this.gridColumnGridRepeatCount.Width = 103;
            // 
            // gridControlRoadKPI
            // 
            this.gridControlRoadKPI.ContextMenuStrip = this.contextMenuStrip;
            this.gridControlRoadKPI.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlRoadKPI.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gridControlRoadKPI.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gridControlRoadKPI.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gridControlRoadKPI.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gridControlRoadKPI.EmbeddedNavigator.Buttons.Remove.Visible = false;
            this.gridControlRoadKPI.Location = new System.Drawing.Point(0, 0);
            this.gridControlRoadKPI.MainView = this.gridViewRoadKPI;
            this.gridControlRoadKPI.Name = "gridControlRoadKPI";
            this.gridControlRoadKPI.Size = new System.Drawing.Size(1007, 160);
            this.gridControlRoadKPI.TabIndex = 4;
            this.gridControlRoadKPI.UseEmbeddedNavigator = true;
            this.gridControlRoadKPI.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridViewRoadKPI});
            // 
            // gridViewRoadKPI
            // 
            this.gridViewRoadKPI.GridControl = this.gridControlRoadKPI;
            this.gridViewRoadKPI.Name = "gridViewRoadKPI";
            this.gridViewRoadKPI.OptionsBehavior.Editable = false;
            this.gridViewRoadKPI.OptionsView.ColumnAutoWidth = false;
            this.gridViewRoadKPI.OptionsView.ShowDetailButtons = false;
            this.gridViewRoadKPI.OptionsView.ShowGroupPanel = false;
            // 
            // miQuerySample
            // 
            this.miQuerySample.Name = "miQuerySample";
            this.miQuerySample.Size = new System.Drawing.Size(152, 22);
            this.miQuerySample.Text = "查询采样点";
            this.miQuerySample.Click += new System.EventHandler(this.miQuerySample_Click);
            // 
            // RoadProblemForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1007, 484);
            this.Controls.Add(this.splitContainerControl1);
            this.Controls.Add(this.panelControl1);
            this.Name = "RoadProblemForm";
            this.Text = "道路问题点";
            ((System.ComponentModel.ISupportInitialize)(this.panelControl1)).EndInit();
            this.panelControl1.ResumeLayout(false);
            this.panelControl1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.edtRoadName.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbxLevel.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbxStatus.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbxAreaName.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).EndInit();
            this.splitContainerControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlRoad)).EndInit();
            this.contextMenuStrip.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridViewRoad)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlRoadKPI)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewRoadKPI)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.PanelControl panelControl1;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl1;
        private DevExpress.XtraGrid.GridControl gridControlRoad;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewRoad;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnStatus;
        private DevExpress.XtraGrid.GridControl gridControlRoadKPI;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewRoadKPI;
        private DevExpress.XtraEditors.LabelControl labelControl5;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.SimpleButton btnQuery;
        private DevExpress.XtraEditors.ComboBoxEdit cbxAreaName;
        private DevExpress.XtraEditors.ComboBoxEdit cbxStatus;
        private DevExpress.XtraEditors.LabelControl labelControl6;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip;
        private System.Windows.Forms.ToolStripMenuItem miExportToExcel;
        private DevExpress.XtraEditors.SimpleButton btnSetting;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnGoodDaysCount;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnValidateStatus;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnGridRepeatCount;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnCreatedMonth;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnLastAbnormalMonth;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnClosedMonth;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnID;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnAreaName;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnLevel;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnRoadName;
        private DevExpress.XtraEditors.CheckedComboBoxEdit cbxLevel;
        private DevExpress.XtraEditors.TextEdit edtRoadName;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnLastTestMonth;
        private System.Windows.Forms.ToolStripMenuItem miQuerySample;
    }
}