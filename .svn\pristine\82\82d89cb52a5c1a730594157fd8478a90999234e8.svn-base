﻿namespace MasterCom.RAMS.CQT
{
    partial class CQTPointListForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.splitContainerControl1 = new DevExpress.XtraEditors.SplitContainerControl();
            this.cqtInfoFilterPanel1 = new MasterCom.RAMS.CQT.CQTInfoFilterPanel();
            this.btnFilter = new DevExpress.XtraEditors.SimpleButton();
            this.btnQuery = new DevExpress.XtraEditors.SimpleButton();
            this.panel2 = new System.Windows.Forms.Panel();
            this.simpleButtonUpdate = new DevExpress.XtraEditors.SimpleButton();
            this.simpleButtonPictMng = new DevExpress.XtraEditors.SimpleButton();
            this.simpleButtonDelete = new DevExpress.XtraEditors.SimpleButton();
            this.simpleButtonInsert = new DevExpress.XtraEditors.SimpleButton();
            this.panel1 = new System.Windows.Forms.Panel();
            this.cqtPointPanel1 = new MasterCom.RAMS.CQT.CQTPointPanel();
            this.gridControlCQTPoint = new DevExpress.XtraGrid.GridControl();
            this.gridView = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gcName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcAddrAt = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcDesc = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcLongitude = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcLatitude = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcAltitude = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcPointType = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcDensityType = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcSpaceType = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcCoverType = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcNetworkType = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcBelongArea = new DevExpress.XtraGrid.Columns.GridColumn();
            this.simpleButtonImportAll = new DevExpress.XtraEditors.SimpleButton();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).BeginInit();
            this.splitContainerControl1.SuspendLayout();
            this.panel2.SuspendLayout();
            this.panel1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlCQTPoint)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView)).BeginInit();
            this.SuspendLayout();
            // 
            // splitContainerControl1
            // 
            this.splitContainerControl1.Collapsed = true;
            this.splitContainerControl1.CollapsePanel = DevExpress.XtraEditors.SplitCollapsePanel.Panel1;
            this.splitContainerControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl1.Horizontal = false;
            this.splitContainerControl1.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl1.Name = "splitContainerControl1";
            this.splitContainerControl1.Panel1.Controls.Add(this.cqtInfoFilterPanel1);
            this.splitContainerControl1.Panel1.Controls.Add(this.btnFilter);
            this.splitContainerControl1.Panel1.Controls.Add(this.btnQuery);
            this.splitContainerControl1.Panel1.Text = "Panel1";
            this.splitContainerControl1.Panel2.Controls.Add(this.panel2);
            this.splitContainerControl1.Panel2.Controls.Add(this.panel1);
            this.splitContainerControl1.Panel2.Text = "Panel2";
            this.splitContainerControl1.Size = new System.Drawing.Size(893, 411);
            this.splitContainerControl1.SplitterPosition = 0;
            this.splitContainerControl1.TabIndex = 0;
            this.splitContainerControl1.Text = "splitContainerControl1";
            // 
            // cqtInfoFilterPanel1
            // 
            this.cqtInfoFilterPanel1.Location = new System.Drawing.Point(5, 7);
            this.cqtInfoFilterPanel1.Name = "cqtInfoFilterPanel1";
            this.cqtInfoFilterPanel1.Size = new System.Drawing.Size(842, 62);
            this.cqtInfoFilterPanel1.TabIndex = 3;
            // 
            // btnFilter
            // 
            this.btnFilter.Location = new System.Drawing.Point(853, 38);
            this.btnFilter.Name = "btnFilter";
            this.btnFilter.Size = new System.Drawing.Size(75, 23);
            this.btnFilter.TabIndex = 2;
            this.btnFilter.Text = "筛选";
            // 
            // btnQuery
            // 
            this.btnQuery.Location = new System.Drawing.Point(853, 7);
            this.btnQuery.Name = "btnQuery";
            this.btnQuery.Size = new System.Drawing.Size(75, 23);
            this.btnQuery.TabIndex = 2;
            this.btnQuery.Text = "查询";
            // 
            // panel2
            // 
            this.panel2.Controls.Add(this.simpleButtonImportAll);
            this.panel2.Controls.Add(this.simpleButtonUpdate);
            this.panel2.Controls.Add(this.simpleButtonPictMng);
            this.panel2.Controls.Add(this.simpleButtonDelete);
            this.panel2.Controls.Add(this.simpleButtonInsert);
            this.panel2.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.panel2.Location = new System.Drawing.Point(0, 374);
            this.panel2.Name = "panel2";
            this.panel2.Size = new System.Drawing.Size(893, 31);
            this.panel2.TabIndex = 3;
            // 
            // simpleButtonUpdate
            // 
            this.simpleButtonUpdate.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.simpleButtonUpdate.Location = new System.Drawing.Point(709, 4);
            this.simpleButtonUpdate.Name = "simpleButtonUpdate";
            this.simpleButtonUpdate.Size = new System.Drawing.Size(75, 23);
            this.simpleButtonUpdate.TabIndex = 1;
            this.simpleButtonUpdate.Text = "修改";
            this.simpleButtonUpdate.Click += new System.EventHandler(this.simpleButtonUpdate_Click);
            // 
            // simpleButtonPictMng
            // 
            this.simpleButtonPictMng.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.simpleButtonPictMng.Location = new System.Drawing.Point(529, 4);
            this.simpleButtonPictMng.Name = "simpleButtonPictMng";
            this.simpleButtonPictMng.Size = new System.Drawing.Size(75, 23);
            this.simpleButtonPictMng.TabIndex = 1;
            this.simpleButtonPictMng.Text = "图片管理";
            this.simpleButtonPictMng.Click += new System.EventHandler(this.simpleButtonPictMng_Click);
            // 
            // simpleButtonDelete
            // 
            this.simpleButtonDelete.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.simpleButtonDelete.Location = new System.Drawing.Point(619, 4);
            this.simpleButtonDelete.Name = "simpleButtonDelete";
            this.simpleButtonDelete.Size = new System.Drawing.Size(75, 23);
            this.simpleButtonDelete.TabIndex = 1;
            this.simpleButtonDelete.Text = "删除";
            this.simpleButtonDelete.Click += new System.EventHandler(this.simpleButtonDelete_Click);
            // 
            // simpleButtonInsert
            // 
            this.simpleButtonInsert.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.simpleButtonInsert.Location = new System.Drawing.Point(799, 4);
            this.simpleButtonInsert.Name = "simpleButtonInsert";
            this.simpleButtonInsert.Size = new System.Drawing.Size(75, 23);
            this.simpleButtonInsert.TabIndex = 1;
            this.simpleButtonInsert.Text = "新增";
            this.simpleButtonInsert.Click += new System.EventHandler(this.simpleButtonInsert_Click);
            // 
            // panel1
            // 
            this.panel1.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
                        | System.Windows.Forms.AnchorStyles.Left)
                        | System.Windows.Forms.AnchorStyles.Right)));
            this.panel1.Controls.Add(this.cqtPointPanel1);
            this.panel1.Controls.Add(this.gridControlCQTPoint);
            this.panel1.Location = new System.Drawing.Point(3, 6);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(891, 357);
            this.panel1.TabIndex = 2;
            // 
            // cqtPointPanel1
            // 
            this.cqtPointPanel1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.cqtPointPanel1.Location = new System.Drawing.Point(0, 0);
            this.cqtPointPanel1.Name = "cqtPointPanel1";
            this.cqtPointPanel1.Size = new System.Drawing.Size(891, 357);
            this.cqtPointPanel1.TabIndex = 1;
            // 
            // gridControlCQTPoint
            // 
            this.gridControlCQTPoint.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlCQTPoint.Location = new System.Drawing.Point(0, 0);
            this.gridControlCQTPoint.MainView = this.gridView;
            this.gridControlCQTPoint.Name = "gridControlCQTPoint";
            this.gridControlCQTPoint.Size = new System.Drawing.Size(891, 357);
            this.gridControlCQTPoint.TabIndex = 0;
            this.gridControlCQTPoint.UseEmbeddedNavigator = true;
            this.gridControlCQTPoint.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView});
            // 
            // gridView
            // 
            this.gridView.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gcName,
            this.gcAddrAt,
            this.gcDesc,
            this.gcLongitude,
            this.gcLatitude,
            this.gcAltitude,
            this.gcPointType,
            this.gcDensityType,
            this.gcSpaceType,
            this.gcCoverType,
            this.gcNetworkType,
            this.gcBelongArea});
            this.gridView.GridControl = this.gridControlCQTPoint;
            this.gridView.GroupSummary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridGroupSummaryItem(DevExpress.Data.SummaryItemType.Count, "Name", null, "")});
            this.gridView.Name = "gridView";
            this.gridView.OptionsBehavior.Editable = false;
            this.gridView.OptionsDetail.EnableMasterViewMode = false;
            this.gridView.OptionsDetail.ShowDetailTabs = false;
            this.gridView.FocusedRowChanged += new DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventHandler(this.gridView_FocusedRowChanged);
            this.gridView.DoubleClick += new System.EventHandler(this.gridView_DoubleClick);
            // 
            // gcName
            // 
            this.gcName.Caption = "名称";
            this.gcName.FieldName = "Name";
            this.gcName.Fixed = DevExpress.XtraGrid.Columns.FixedStyle.Left;
            this.gcName.Name = "gcName";
            this.gcName.Visible = true;
            this.gcName.VisibleIndex = 0;
            // 
            // gcAddrAt
            // 
            this.gcAddrAt.Caption = "地址";
            this.gcAddrAt.FieldName = "AddrAt";
            this.gcAddrAt.Fixed = DevExpress.XtraGrid.Columns.FixedStyle.Left;
            this.gcAddrAt.Name = "gcAddrAt";
            this.gcAddrAt.Visible = true;
            this.gcAddrAt.VisibleIndex = 1;
            // 
            // gcDesc
            // 
            this.gcDesc.Caption = "描述";
            this.gcDesc.FieldName = "Desc";
            this.gcDesc.Name = "gcDesc";
            this.gcDesc.Visible = true;
            this.gcDesc.VisibleIndex = 2;
            // 
            // gcLongitude
            // 
            this.gcLongitude.Caption = "经度";
            this.gcLongitude.FieldName = "Longitude";
            this.gcLongitude.Name = "gcLongitude";
            this.gcLongitude.Visible = true;
            this.gcLongitude.VisibleIndex = 3;
            // 
            // gcLatitude
            // 
            this.gcLatitude.Caption = "纬度";
            this.gcLatitude.FieldName = "Latitude";
            this.gcLatitude.Name = "gcLatitude";
            this.gcLatitude.Visible = true;
            this.gcLatitude.VisibleIndex = 4;
            // 
            // gcAltitude
            // 
            this.gcAltitude.Caption = "高度";
            this.gcAltitude.FieldName = "Altitude";
            this.gcAltitude.Name = "gcAltitude";
            this.gcAltitude.Visible = true;
            this.gcAltitude.VisibleIndex = 5;
            // 
            // gcPointType
            // 
            this.gcPointType.Caption = "地点类型";
            this.gcPointType.FieldName = "PointTypeDesc";
            this.gcPointType.Name = "gcPointType";
            this.gcPointType.Visible = true;
            this.gcPointType.VisibleIndex = 6;
            // 
            // gcDensityType
            // 
            this.gcDensityType.Caption = "密度类型";
            this.gcDensityType.FieldName = "DensityTypeDesc";
            this.gcDensityType.Name = "gcDensityType";
            this.gcDensityType.Visible = true;
            this.gcDensityType.VisibleIndex = 7;
            // 
            // gcSpaceType
            // 
            this.gcSpaceType.Caption = "建筑类型";
            this.gcSpaceType.FieldName = "SpaceTypeDesc";
            this.gcSpaceType.Name = "gcSpaceType";
            this.gcSpaceType.Visible = true;
            this.gcSpaceType.VisibleIndex = 8;
            // 
            // gcCoverType
            // 
            this.gcCoverType.Caption = "覆盖类型";
            this.gcCoverType.FieldName = "CoverTypeDesc";
            this.gcCoverType.Name = "gcCoverType";
            this.gcCoverType.Visible = true;
            this.gcCoverType.VisibleIndex = 9;
            // 
            // gcNetworkType
            // 
            this.gcNetworkType.Caption = "网络类型";
            this.gcNetworkType.FieldName = "NetworkTypeDesc";
            this.gcNetworkType.Name = "gcNetworkType";
            this.gcNetworkType.Visible = true;
            this.gcNetworkType.VisibleIndex = 10;
            // 
            // gcBelongArea
            // 
            this.gcBelongArea.Caption = "所属区域";
            this.gcBelongArea.FieldName = "BelongArea";
            this.gcBelongArea.Name = "gcBelongArea";
            this.gcBelongArea.Visible = true;
            this.gcBelongArea.VisibleIndex = 11;
            // 
            // simpleButtonImportAll
            // 
            this.simpleButtonImportAll.Location = new System.Drawing.Point(439, 4);
            this.simpleButtonImportAll.Name = "simpleButtonImportAll";
            this.simpleButtonImportAll.Size = new System.Drawing.Size(75, 23);
            this.simpleButtonImportAll.TabIndex = 2;
            this.simpleButtonImportAll.Text = "批量导入";
            this.simpleButtonImportAll.Click += new System.EventHandler(this.simpleButtonImportAll_Click);
            // 
            // CQTPointListForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(893, 411);
            this.Controls.Add(this.splitContainerControl1);
            this.Name = "CQTPointListForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "CQT地点信息列表";
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).EndInit();
            this.splitContainerControl1.ResumeLayout(false);
            this.panel2.ResumeLayout(false);
            this.panel1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlCQTPoint)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl1;
        private DevExpress.XtraGrid.GridControl gridControlCQTPoint;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView;
        private DevExpress.XtraEditors.SimpleButton btnQuery;
        private DevExpress.XtraEditors.SimpleButton btnFilter;
        private CQTInfoFilterPanel cqtInfoFilterPanel1;
        private DevExpress.XtraGrid.Columns.GridColumn gcName;
        private DevExpress.XtraGrid.Columns.GridColumn gcAddrAt;
        private DevExpress.XtraGrid.Columns.GridColumn gcDesc;
        private DevExpress.XtraGrid.Columns.GridColumn gcLongitude;
        private DevExpress.XtraGrid.Columns.GridColumn gcLatitude;
        private DevExpress.XtraGrid.Columns.GridColumn gcAltitude;
        private DevExpress.XtraGrid.Columns.GridColumn gcPointType;
        private DevExpress.XtraGrid.Columns.GridColumn gcDensityType;
        private DevExpress.XtraGrid.Columns.GridColumn gcSpaceType;
        private DevExpress.XtraGrid.Columns.GridColumn gcCoverType;
        private DevExpress.XtraGrid.Columns.GridColumn gcNetworkType;
        private DevExpress.XtraGrid.Columns.GridColumn gcBelongArea;
        private DevExpress.XtraEditors.SimpleButton simpleButtonInsert;
        private DevExpress.XtraEditors.SimpleButton simpleButtonUpdate;
        private DevExpress.XtraEditors.SimpleButton simpleButtonDelete;
        private System.Windows.Forms.Panel panel2;
        private System.Windows.Forms.Panel panel1;
        private CQTPointPanel cqtPointPanel1;
        private DevExpress.XtraEditors.SimpleButton simpleButtonPictMng;
        private DevExpress.XtraEditors.SimpleButton simpleButtonImportAll;
    }
}