﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using System.Windows.Forms;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;
using MasterCom.MTGis;
using DevExpress.XtraEditors;
using MasterCom.RAMS.Func;
using CQTLibrary.CqtZTFunc;
using CQTLibrary.PublicItem;
namespace MasterCom.RAMS.CQT
{
   public class CQTProblemSummaryAnalys2G : QueryBase
   {
        public CQTProblemSummaryAnalys2G(MainModel mainModel)
            : base(mainModel)
        {
        }
        protected override bool isValidCondition()
        {
            return true;
        }
        public override string Name
        {
            get { return "2G问题点统计汇总"; }
        }
        public override string IconName
        {
            get { return "Images/regionq.gif"; }
        }
        #region 全局变量
        readonly Func_ProblemAna probAna = new Func_ProblemAna();
        Dictionary<AreaCityKey, AreaDetail> cqtDetailDic = new Dictionary<AreaCityKey, AreaDetail>();
        List<CQTLibrary.PublicItem.ProblemItem> result = new List<CQTLibrary.PublicItem.ProblemItem>();
        readonly CQTLibrary.RAMS.NET.MainModel cqtModel = new CQTLibrary.RAMS.NET.MainModel();
        SumarryDdataInfoGSM sumarryInfo;
        public static List<ProblemSummaryPont2G> problemPointSummarryList { get; set; } = new List<ProblemSummaryPont2G>();
        public static List<ProblemPont2G> problemPointList { get; set; } = new List<ProblemPont2G>();
        private bool pointNum = false;
        bool isPartDb = false;
        int iNo = 1;
        int strcityid = 0;
        int newpoint = 0;
        #endregion
        protected override void query()
        {
            WaitBox.Show("准备获取CQT点...", seachdata);

            showData();
        }
        /// <summary>
        /// 准备查询数据的条件
        /// </summary>
        private void seachdata()
        {
            problemPointSummarryList.Clear();
            problemPointList.Clear();
            sumarryInfo = new SumarryDdataInfoGSM();
            WaitBox.ProgressPercent = 20;
            iNo = 1;
            cqtModel.DistrictID = MainModel.DistrictID;
            cqtModel.UserName = MainModel.User.LoginName;
            cqtModel.UserPass = MainModel.User.Password;
            if (MainModel.Server.IP.Equals("*************") || MainModel.Server.IP.Equals("**************"))
            {
                isPartDb = true;
            }
            cqtModel.ServerIP = MainModel.Server.IP;
            cqtModel.ServerPort = MainModel.Server.Port;
            if (MainModel.User.DBID == -1)
            {
                string strCity = getStrCity();
                cqtDetailDic = probAna.getCqtDetailDic(cqtModel, strCity, isPartDb);
                WaitBox.ProgressPercent = 30;
                List<int> disid = getDisid();
                for (int chdistrid = 0; chdistrid < disid.Count; chdistrid++)
                {
                    cqtModel.DistrictID = disid[chdistrid];
                    strcityid = disid[chdistrid];
                    WaitBox.Text = "正在获取" + DistrictManager.GetInstance().getDistrictName(disid[chdistrid]) + "市的CQT点...";
                    WaitBox.ProgressPercent = WaitBox.ProgressPercent + 1;
                    dealWithDataXQ();
                    dealWithSummarryData();
                }
            }
            else
            {
                string strCity = condition.DistrictID.ToString();
                strcityid = cqtModel.DistrictID;
                cqtDetailDic = probAna.getCqtDetailDic(cqtModel, strCity, isPartDb);
                dealWithDataXQ();
                dealWithSummarryData();
            }
            WaitBox.ProgressPercent = 80;
            if (problemPointSummarryList.Count != 0)
            {
                WaitBox.Text = "正在进行汇总处理...";
                CQTProblemSummaryAnalys2G.problemPointSummarryList.Add(ProblemSummaryPont2G.fillDataTotal(sumarryInfo));
            }
            WaitBox.Close();
        }

        private List<int> getDisid()
        {
            List<int> disid = new List<int>();
            //按特定顺序排列查询各是数据         
            int[] zhidingID = { 1, 2, 3, 12, 6, 5, 4, 10, 13, 14, 16, 21, 17, 7, 8, 9, 11, 15, 18, 19, 20, 22, 23 };
            for (int idk = 0; idk < zhidingID.Length; idk++)
            {
                if (condition.DistrictIDs.Contains(zhidingID[idk]))
                {
                    disid.Add(zhidingID[idk]);
                }
            }

            return disid;
        }

        private string getStrCity()
        {
            StringBuilder strCity = new StringBuilder();
            foreach (int DistrictID1 in condition.DistrictIDs)
            {
                if (strCity.Length == 0)
                    strCity.Append(DistrictID1.ToString());
                else
                    strCity.Append("," + DistrictID1.ToString());
            }
            return strCity.ToString();
        }

        /// <summary>
        /// 处理数据
        /// </summary>
        private void dealWithSummarryData()
        {
            DateTime sDtime = condition.Periods[0].BeginTime;
            DateTime eDtime = condition.Periods[0].EndTime.AddSeconds(-1);
            string project = "";
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < condition.Projects.Count; i++)
            {
                if (i < condition.Projects.Count - 1)
                    sb.Append(condition.Projects[i].ToString() + ",");
                else
                    sb.Append(condition.Projects[i].ToString());
            }
            project = sb.ToString();
            XQDataInfoGSM xqInfo = new XQDataInfoGSM();
            if (pointNum)
            {
                for (int id = problemPointList.Count - newpoint; id < problemPointList.Count; id++)
                {
                    addXQDataInfoCount(xqInfo, id);
                }
            }
            int iTestDicCount = probAna.getGsmTestCqtNum(cqtModel, sDtime, eDtime, project, isPartDb);
            ProblemSummaryPont2G lisFTem = ProblemSummaryPont2G.fillXQData(xqInfo, iTestDicCount, strcityid);
            CQTProblemSummaryAnalys2G.problemPointSummarryList.Add(lisFTem);
            SumarryDdataInfoGSM.fillSummarryData(ref sumarryInfo, xqInfo, iTestDicCount, strcityid);
        }

        private static void addXQDataInfoCount(XQDataInfoGSM xqInfo, int id)
        {
            if (problemPointList[id].SMainType == "弱覆盖")
                xqInfo.iweakcoverage++;
            else if (problemPointList[id].SMainType == "话质差")
                xqInfo.ipoorvoice++;
            else if (problemPointList[id].SMainType == "未接通")
                xqInfo.inotconnected++;
            else if (problemPointList[id].SMainType == "掉话")
                xqInfo.idroppedcalls++;
            else if (problemPointList[id].SMainType == "感知掉话")
                xqInfo.ifillDropedCall++;
            else if (problemPointList[id].SMainType == "下载速率低")
                xqInfo.idownless++;
            else if (problemPointList[id].SMainType == "深度覆盖不足")
                xqInfo.ilessthendeep++;
            else if (problemPointList[id].SMainType == "下载掉线")
                xqInfo.idowndrop++;
            else if (problemPointList[id].SMainType == "下载超时")
                xqInfo.idowntimeout++;
            else if (problemPointList[id].SMainType == "GSM深度覆盖不达标")
                xqInfo.ilessthendeepGSM++;
            else if (problemPointList[id].SMainType == "TD深度覆盖不达标")
                xqInfo.ilessthendeepTD++;
            else if (problemPointList[id].SMainType == "G劣")
                xqInfo.ilessGHG++;
            else if (problemPointList[id].SMainType == "G劣1")
                xqInfo.ilessG1++;
            else if (problemPointList[id].SMainType == "G劣2")
                xqInfo.ilessG2++;

            string type = problemPointList[id].SMainType;
            switch (type)
            {
                case "弱覆盖":
                    xqInfo.iweakcoverage++;
                    break;
                case "话质差":
                    xqInfo.ipoorvoice++;
                    break;
                case "未接通":
                    xqInfo.inotconnected++;
                    break;
                case "掉话":
                    xqInfo.idroppedcalls++;
                    break;
                case "感知掉话":
                    xqInfo.ifillDropedCall++;
                    break;
                case "下载速率低":
                    xqInfo.idownless++;
                    break;
                case "深度覆盖不足":
                    xqInfo.ilessthendeep++;
                    break;
                case "下载掉线":
                    xqInfo.idowndrop++;
                    break;
                case "下载超时":
                    xqInfo.idowntimeout++;
                    break;
                case "GSM深度覆盖不达标":
                    xqInfo.ilessthendeepGSM++;
                    break;
                case "TD深度覆盖不达标":
                    xqInfo.ilessthendeepTD++;
                    break;
                case "G劣":
                    xqInfo.ilessGHG++;
                    break;
                case "G劣1":
                    xqInfo.ilessG1++;
                    break;
                case "G劣2":
                    xqInfo.ilessG2++;
                    break;
            }
        }

        /// <summary>
        /// 对事前信息进行处理，以便对应原来的统计接口
        /// </summary>
        private void dealWithDataXQ()
        {
            DateTime sDtime = condition.Periods[0].BeginTime;
            DateTime eDtime = condition.Periods[0].EndTime.AddSeconds(-1);
            string project = "";
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < condition.Projects.Count; i++)
            {
                if (i < condition.Projects.Count - 1)
                    sb.Append(condition.Projects[i].ToString() + ",");
                else
                    sb.Append(condition.Projects[i].ToString());
            }
            project = sb.ToString();
            result = probAna.getGsmProblemInfo(cqtModel, sDtime, eDtime, project, cqtDetailDic, isPartDb);
            if (result.Count > 0)
                pointNum = true;
            else
                pointNum = false;
            newpoint = 0;
            int mainProblemID = 0;
            for (int id = 0; id < result.Count; id++)
            {
                if (result[id].probFileList.Count != 0 && result[id].probFileList[0].ToString() != "")
                {
                    newpoint++;
                    ProblemPont2G lis1 = new ProblemPont2G();
                    mainProblemID = addResult(mainProblemID, id, lis1);

                    setProblemPont2GSSecondType(mainProblemID, id, lis1);

                    setProblemPont2GInfo(id, lis1);

                    problemPointList.Add(lis1);
                }

            }
        }

        private int addResult(int mainProblemID, int id, ProblemPont2G lis1)
        {
            if (result[id].StrMainProblem2 != "")
            {
                lis1.SMainType = result[id].StrMainProblem2;
                lis1.SPointPosition = CQTProblemSummaryAnalysGSM.getsub(result[id].StrTestSite, 2);
                mainProblemID = 0;
            }
            else if (result[id].StrMainProblem3 != "")
            {
                lis1.SMainType = result[id].StrMainProblem3;
                lis1.SPointPosition = CQTProblemSummaryAnalysGSM.getsub(result[id].StrTestSite, 3);
                mainProblemID = 1;
            }
            else if (result[id].StrMainProblem1 != "")
            {
                lis1.SMainType = result[id].StrMainProblem1;
                lis1.SPointPosition = CQTProblemSummaryAnalysGSM.getsub(result[id].StrTestSite, 1);
                mainProblemID = 2;
            }
            else if (result[id].StrSecProblem1 != "")
            {
                lis1.SMainType = result[id].StrSecProblem1;
                mainProblemID = 3;
            }

            return mainProblemID;
        }

        private void setProblemPont2GSSecondType(int mainProblemID, int id, ProblemPont2G lis1)
        {
            if (mainProblemID == 0)
            {
                lis1.SSecondType = "";
                addSSecondType(lis1, result[id].StrMainProblem3, true);
                addSSecondType(lis1, result[id].StrMainProblem1, true);
                addSSecondType(lis1, result[id].StrSecProblem1, false);
            }
            else if (mainProblemID == 1)
            {
                lis1.SSecondType = "";
                addSSecondType(lis1, result[id].StrMainProblem1, true);
                addSSecondType(lis1, result[id].StrSecProblem1, false);
            }
            else if (mainProblemID == 2)
            {
                lis1.SSecondType = "";
                addSSecondType(lis1, result[id].StrSecProblem1, false);
            }
            else if (mainProblemID == 3)
            {
                lis1.SSecondType = "";
            }
        }

        private void addSSecondType(ProblemPont2G lis1, string problem, bool addSplit)
        {
            if (problem != "")
            {
                lis1.SSecondType += problem;
            }
            if (addSplit)
            {
                lis1.SSecondType += ",";
            }
        }

        private void setProblemPont2GInfo(int id, ProblemPont2G lis1)
        {
            int ifile;
            int ilac = 0, ici = 0;
            lis1.IID = iNo++;
            lis1.SCity = DistrictManager.GetInstance().getDistrictName(result[id].Icity);
            lis1.STestTime = result[id].Dtime;
            lis1.STestPoint = result[id].Strcqtname;
            lis1.SSitePro = result[id].Strcqttype;
            lis1.SImportLeve = "-";
            StringBuilder s1 = new StringBuilder();
            for (ifile = 0; ifile < result[id].probFileList.Count; ifile++)
            {
                s1.Append(result[id].probFileList[ifile].ToString() + "；");
            }
            lis1.STestFilePosition = s1.ToString();
            StringBuilder s2 = new StringBuilder();
            for (ifile = 0; ifile < result[id].probCellInfoList.Count; ifile++)
            {
                ilac = result[id].probCellInfoList[ifile].Ilac;
                ici = result[id].probCellInfoList[ifile].Ici;
                if (result[id].probCellInfoList[ifile].Strcellname == "")
                    s2.Append("(" + ilac + "_" + ici + "),采样点共" + result[id].probCellInfoList[ifile].Isampleid + "个,场强为" + result[id].probCellInfoList[ifile].Irxlev + "dBm" + "；");
                else
                    s2.Append(result[id].probCellInfoList[ifile].Strcellname + "(" + ilac + "_" + ici + "),采样点共" + result[id].probCellInfoList[ifile].Isampleid + "个,场强为" + result[id].probCellInfoList[ifile].Irxlev + "dBm" + "；");
            }
            lis1.SReasonAna = s2.ToString();
            lis1.SReasonType = result[id].StrCauseType;
        }

        /// <summary>
        /// 显示处理结果窗体
        /// </summary>
        private void showData()
        {
            CQTProblemSummary2G cqtproblemShowForm = null;
            object obj = MainModel.GetObjectFromBlackboard(typeof(CQTProblemSummary2G).FullName);
            cqtproblemShowForm = obj == null ? null : obj as CQTProblemSummary2G;
            if (cqtproblemShowForm == null || cqtproblemShowForm.IsDisposed)
            {
                cqtproblemShowForm = new CQTProblemSummary2G(MainModel, "通报问题点");
            }
            cqtproblemShowForm.filldataSummary2G(0);
            cqtproblemShowForm.Show(MainModel.MainForm);
        }
   }
}
