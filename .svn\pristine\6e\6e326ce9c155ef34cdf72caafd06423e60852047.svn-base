﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRHandoverFailInfo
    {
        public NRHandoverFailInfo(int sn, Event evt, NRHandoverEventHelper.HandOverCellInfo cellInfo)
        {
            SN = sn;
            Event = evt;
            EventName = evt.Name;
            Time = evt.DateTimeStringWithMillisecond;
            Longitude = evt.Longitude;
            Latitude = evt.Latitude;
            FileName = evt.FileName;

            SrcNRCell.Init(cellInfo.NRSrcCell);
            TarNRCell.Init(cellInfo.NRTarCell);
            SrcLteCell.Init(cellInfo.LTESrcCell);
            TarLteCell.Init(cellInfo.LTETarCell);
        }

        public Event Event { get; private set; }
        public string EventName { get; private set; }
        public int SN { get; private set; }
        public string Time { get; private set; }
        public double Longitude { get; private set; }
        public double Latitude { get; private set; }
        public string FileName { get; private set; }
        public CellInfo SrcNRCell { get; private set; } = new CellInfo();
        public CellInfo TarNRCell { get; private set; } = new CellInfo();
        public CellInfo SrcLteCell { get; private set; } = new CellInfo();
        public CellInfo TarLteCell { get; private set; } = new CellInfo();

        public class CellInfo
        {
            public int Arfcn { get; set; }
            public int Pci { get; set; }
            public int? CellID { get; set; }
            public string CellName { get; set; }

            public void Init(HandOverEventBase.CellInfo cellInfo)
            {
                if (cellInfo.Cell != null)
                {
                    CellID = cellInfo.Cell.ID;
                    CellName = cellInfo.Cell.Name;
                }

                Arfcn = cellInfo.ARFCN;
                Pci = cellInfo.PCI;
            }
        }
    }
}
