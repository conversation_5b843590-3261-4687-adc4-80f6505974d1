﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.MTGis;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class SimpleGridControlForm : MinCloseForm
    {
        public SimpleGridControlForm(MainModel mm) : base(mm)
        {
            mainModel = mm;
            InitializeComponent();
            InitDefaultCondition();

            btnOK.Click += BtnOK_Click;
            btnClear.Click += BtnClear_Click;
            btnExport.Click += BtnExport_Click;
            FireResultChanged();
        }

        protected override void MinCloseForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            btnClear.PerformClick();
            base.MinCloseForm_FormClosing(sender, e);
        }

        private void InitDefaultCondition()
        {
            cbxStartPoint.Items.Clear();
            cbxStartPoint.Items.AddRange(cbxItems);
            cbxStartPoint.SelectedIndex = 1;
            numXStep.Value = 1000;
            numYStep.Value = 1000;
        }

        private SimpleGridCondition GetCondition()
        {
            SimpleGridCondition cond = new SimpleGridCondition();
            cond.Region = mainModel.SearchGeometrys.Region;
            if (cond.Region == null)
            {
                return cond;
            }

            DbPoint startPoint = null;
            switch (cbxStartPoint.SelectedIndex)
            {
                case 0:
                    startPoint = new DbPoint(cond.Region.Extents.xMin, cond.Region.Extents.yMax);
                    break;
                case 1:
                    startPoint = new DbPoint(cond.Region.Center.x, cond.Region.Center.y);
                    break;
                default:
                    startPoint = new DbPoint(cond.Region.Extents.xMin, cond.Region.Extents.yMax);
                    break;
            }
            cond.StartPoint = startPoint;

            double longPerMeter = DistanceTranslator.LongitudePerMeter(cond.Region.Center.y);
            double latPerMeter = DistanceTranslator.LatitudePerMeter();
            cond.XStep = (double)numXStep.Value * longPerMeter;
            cond.YStep = (double)numYStep.Value * latPerMeter;

            return cond;
        }

        private void BtnOK_Click(object sender, EventArgs e)
        {
            SimpleGridCondition cond = GetCondition();
            if (cond.Region == null)
            {
                MessageBox.Show("请先选择矩形或多边形区域", "提示");
                FireResultChanged();
                return;
            }

            this.Cursor = Cursors.WaitCursor;
            SimpleGridCreater creater = new SimpleGridCreater(cond);
            shp = creater.Create();
            if (shp != null)
            {
                shp.DefaultDrawingOptions.FillVisible = false;
                shp.DefaultDrawingOptions.LineColor = (uint)ColorTranslator.ToOle(Color.Blue);
                SimpleGridShpLayer.Show(shp);
            }
            else
            {
                MessageBox.Show("网格生成失败，请修改条件后重试!");
            }

            this.Cursor = Cursors.Default;
            FireResultChanged();
        }

        private void BtnClear_Click(object sender, EventArgs e)
        {
            SimpleGridShpLayer.Clear();
            if (shp != null)
            {
                shp.Close();
                shp = null;
            }
            FireResultChanged();
        }

        private void BtnExport_Click(object sender, EventArgs e)
        {
            if (shp == null)
            {
                return;
            }

            SaveFileDialog dlg = new SaveFileDialog();
            dlg.InitialDirectory = Application.StartupPath;
            dlg.Filter = FilterHelper.Shp;
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return;
            }

            if (!shp.SaveAs(dlg.FileName, null))
            {
                MessageBox.Show("导出网格失败: " + shp.get_ErrorMsg(shp.LastErrorCode));
            }
            else
            {
                MessageBox.Show("导出网格成功!");
            }
        }

        private void FireResultChanged()
        {
            btnExport.Enabled = shp != null;
        }

        private MainModel mainModel;
        private MapWinGIS.Shapefile shp;
        private string[] cbxItems = 
        {
            "区域左上角",
            "区域中心",
        };
    }
}
