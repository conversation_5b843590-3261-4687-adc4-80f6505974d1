﻿using MasterCom.RAMS.Grid;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Stat;
using MasterCom.RAMS.UserMng;
using MasterCom.RAMS.ZTFunc;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.KPI_Statistics
{
    public class ZTVillageTestQueryCellGrid : QueryKPIStatBase
    {
        public List<ZTVillageTestCellGrid> CellGridList { get; set; }

        public ZTVillageTestQueryCellGrid()
            : base()
        { 
        }

        protected override StatTbToken getTableNameToken()
        {
            return StatTbToken.cell_grid;
        }

        public override bool CanEnabled(Model.SearchGeometrys searchGeometrys)
        {
            return true;
        }

        public override string Name
        {
            get
            {
                return "查询小区栅格";
            }
        }

        protected override bool isValidCondition()
        {
            return true;
        }

        protected override void preparePackageCommand(MasterCom.RAMS.Net.Package package)
        {
            package.Command = Command.DIYSearch;
            package.SubCommand = SubCommand.Request;
            package.Content.Type = RequestType.KPI_CELL_GRID;
            package.Content.PrepareAddParam();
        }

        /// <summary>
        /// 添加地理过滤器
        /// </summary>
        /// <param name="package"></param>
        protected override void AddGeographicFilter(Package package)
        {
            AddDIYRegion_Intersect(package);
            //package.Content.AddParam((byte)OpOptionDef.AreaSelectIntersect);
            //DbRect rect = condition.Geometorys.RegionBounds;
            //package.Content.AddParam(rect.x1);
            //package.Content.AddParam(rect.y2);
            //package.Content.AddParam(rect.x2);
            //package.Content.AddParam(rect.y1);
        }

        protected override void AddExtraCondition(Package package, params object[] reservedParams)//增加外条件(KPI_CELL)
        {
            AddDIYEndOpFlag(package);
        }

        private readonly string defaultTemplateName = "VillageTestRpt.xml";
        protected override bool getConditionBeforeQuery()//弹出列表选择框
        {
            try
            {
                string path = Application.StartupPath + @"\config\templates\" + defaultTemplateName;
                XmlConfigFile configFile = new XmlConfigFile(path);
                object value = configFile.GetItemValue("ReportSetting", "styles");
                if (value == null)
                {
                    return false;
                }
                Dictionary<string, object> dic = value as Dictionary<string, object>;
                if (dic == null)
                {
                    return false;
                }

                ReportStyle rptstyle = new ReportStyle();
                rptstyle.Param = dic;
                rptstyle.name = defaultTemplateName.Replace(".xml", "");

                curReportStyle = rptstyle;
                CellGridList = new List<ZTVillageTestCellGrid>();
                return true;
            }
            catch
            {
                return false;
            }
        }

        protected override void queryDistrictData(int districtID)
        {
            ClientProxy clientProxy = new ClientProxy();
            if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, districtID) != ConnectResult.Success)
            {
                ErrorInfo = "连接服务器失败!";
                return;
            }
            try
            {
                queryInThread(clientProxy);
            }
            finally
            {
                clientProxy.Close();
            }
        }

        protected override void queryInThread(object o)
        {
            try
            {
                WaitBox.Text = "开始查询KPI统计数据...";
                WaitBox.CanCancel = true;
                ClientProxy clientProxy = (ClientProxy)o;

                string imgTriadIDSet = getStatImgNeededTriadID();
                if (condition.IsByRound)
                {
                    queryPeriodInfo(null, clientProxy, imgTriadIDSet);
                }
                else
                {
                    foreach (TimePeriod period in Condition.Periods)
                    {
                        WaitBox.Text = "正在统计时段[" + period.GetShortString() + "]内的数据...";
                        WaitBox.ProgressPercent = 10;
                        queryPeriodInfo(period, clientProxy, imgTriadIDSet);
                        if (WaitBox.CancelRequest)
                        {
                            break;
                        }
                    }
                }
            }
            catch (Exception e)
            {
                MessageBox.Show("Error" + e.StackTrace + e.ToString());
            }
        }

        protected override void queryPeriodInfo(TimePeriod period, ClientProxy clientProxy, params object[] reservedParams)
        {
            //stat
            isQueringEvent = false;
            preparePackageBasicContent(clientProxy.Package, period, reservedParams);
            preparePackageNeededInfo_StatImg(clientProxy.Package, reservedParams);
            clientProxy.Send();
            recieveInfo_ImgGrid(clientProxy);

            afterRecieveOnePeriodData(period);
        }

        //接收和处理特定的统计数据
        protected override void recieveAndHandleSpecificStatData(Net.Package package, List<Model.Interface.StatImgDefItem> curImgColumnDef, KPIStatDataBase singleStatData)
        {
            #region
            int lac = package.Content.GetParamInt();
            int ci = package.Content.GetParamInt();
            double lng = package.Content.GetParamDouble();//经度
            double lat = package.Content.GetParamDouble();//纬度
            GridUnitBase grid = new Grid.GridUnitBase(lng, lat);
            if (!condition.Geometorys.GeoOp.Contains(grid.CenterLng, grid.CenterLat))
            {
                return;
            }
            filterAddStatData(package, curImgColumnDef, singleStatData, lac, ci);
            #endregion
        }

        protected void filterAddStatData(Net.Package package, List<Model.Interface.StatImgDefItem> curImgColumnDef, KPIStatDataBase singleStatData, int lac, int ci)
        {
            ICell cell = getCell(package, lac, ci);
            fillStatData(package, curImgColumnDef, singleStatData);
            int fileID = (int)singleStatData[KPIStatDataBase.FileIDKey, KPIStatDataBase.NewGridFileIDKey, -1];
            FileInfo fi = DTDataHeaderManager.GetInstance().GetHeaderByFileID(fileID);
            //KpiDataManager.AddStatData(string.Empty, cell.Name, fi, singleStatData, false);

            ZTVillageTestCellGrid grid = new ZTVillageTestCellGrid();
            grid.FillData(cell, fi, singleStatData);
            CellGridList.Add(grid);
        }

        protected ICell getCell(Package package, int lac, int ci)
        {
            ICell cell = null;
            if (package.Content.Type == ResponseType.KPI_LTE_AMR)
            {
                cell = MainModel.CellManager.GetCurrentLTECell(lac, ci);
            }
            else if (package.Content.Type == ResponseType.RESTYPE_DIY_AREA_COVER_GRID_LTE_FDD_AMR)
            {
                cell = MainModel.CellManager.GetCurrentLTECell(lac, ci);
            }

            if (cell == null)//未知小区_lac_ci
            {
                cell = new UnknowCell(lac, ci);
            }
            return cell;
        }


    }
}
