<?xml version="1.0"?>
<Configs>
  <Config name="StatReports">
    <Item name="reports" typeName="IList">
      <Item typeName="ReporterTemplate">
        <Item name="Param" typeName="IDictionary">
          <Item typeName="String" key="Name">GSM语音业务_月表</Item>
          <Item typeName="Int32" key="KeyCount">2</Item>
          <Item typeName="Int32" key="TimeShowType">1</Item>
          <Item typeName="IList" key="Columns">
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">所属项目</Item>
              <Item typeName="String" key="Exp">{kProjId}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak">所属项目</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">日期</Item>
              <Item typeName="String" key="Exp">{kTimeValue}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak">时间范围</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">测试里程(公里)</Item>
              <Item typeName="String" key="Exp">{Mx_0806/1000}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak">测试里程(公里)</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">测试时长(分钟)</Item>
              <Item typeName="String" key="Exp">{Mx_0805/(60*1000)}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak">测试时长(分钟)</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">平均时速(公里/小时)</Item>
              <Item typeName="String" key="Exp">{(Mx_0806/1000)/(Mx_0805/3600000) }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak">平均时速(公里/小时)</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">联通75覆盖率(%)</Item>
              <Item typeName="String" key="Exp">{100*(Mx_640117)/Mx_640101}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak">Rxlev大于等于-75dBm所占百分比</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">联通85覆盖率(%)</Item>
              <Item typeName="String" key="Exp">{100*(Mx_640117+Mx_64010A+Mx_640109)/Mx_640101}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak">Rxlev大于等于-85dBm所占百分比</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">联通90覆盖率(%)</Item>
              <Item typeName="String" key="Exp">{100*(Mx_640117+Mx_64010A+Mx_640109+Mx_640108)/Mx_640101}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak">联通90覆盖率(%)</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">联通94覆盖率(%)</Item>
              <Item typeName="String" key="Exp">{100*(Mx_640117+Mx_64010A+Mx_640109+Mx_640108+Mx_640107)/Mx_640101}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak">Rxlev大于等于-94dBm所占百分比</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">联通话音质量(%)</Item>
              <Item typeName="String" key="Exp">{100*((Mx_5A010501+Mx_5A010502+Mx_5A010503+Mx_5A010504)+0.7*(Mx_5A010505+Mx_5A010506+Mx_5A010507))/Mx_5A01050C }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">4</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak">联通话音质量(%)</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">平均TA</Item>
              <Item typeName="String" key="Exp">{Mx_67010B}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak">平均TA</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">平均PESQ</Item>
              <Item typeName="String" key="Exp">{Mx_5A010B53}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak">平均PESQ</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">主叫试呼次数</Item>
              <Item typeName="String" key="Exp">{evtIdCount[0]}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak">主叫试呼次数</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">主叫接通次数</Item>
              <Item typeName="String" key="Exp">{evtIdCount[0]-(evtIdCount[7]+evtIdCount[9]+evtIdCount[81]+evtIdCount[87])}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak">主叫接通次数</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">主叫接通率(%)</Item>
              <Item typeName="String" key="Exp">{100.0*(evtIdCount[0]-(evtIdCount[7]+evtIdCount[9]+evtIdCount[81]+evtIdCount[87])) /evtIdCount[0] }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak">主叫接通率(%)</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">主叫时延(秒)</Item>
              <Item typeName="String" key="Exp">{value1[13]/(1000.0*evtIdCount[13]) }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak">主叫时延(秒)</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">主叫掉话次数</Item>
              <Item typeName="String" key="Exp">{evtIdCount[5]}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak">主叫掉话次数</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">掉话率(%)</Item>
              <Item typeName="String" key="Exp">{100.0*(evtIdCount[5]+evtIdCount[6])/(evtIdCount[3]+evtIdCount[4])}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak">掉话率(%)</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">切换请求次数</Item>
              <Item typeName="String" key="Exp">{evtIdCount[15]}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak">切换请求次数</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">切换失败次数</Item>
              <Item typeName="String" key="Exp">{evtIdCount[17]}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak">切换失败次数</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">切换失败率(%)</Item>
              <Item typeName="String" key="Exp">{100.0*evtIdCount[17]/evtIdCount[15]}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak">切换失败率(%)</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">切换频度(次/公里)</Item>
              <Item typeName="String" key="Exp">{(evtIdCount[16]+evtIdCount[17])*1000/Mx_0806 }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak">切换频度(次/公里)</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">切换失败频度(次/公里)</Item>
              <Item typeName="String" key="Exp">{(evtIdCount[17])*1000/Mx_0806}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak">切换失败频度(次/公里)</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">位置更新次数</Item>
              <Item typeName="String" key="Exp">{evtIdCount[18]}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak">位置更新次数</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">位置更新失败次数</Item>
              <Item typeName="String" key="Exp">{evtIdCount[20]}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak">位置更新失败次数</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">位置更新失败率(%)</Item>
              <Item typeName="String" key="Exp">{100.0*evtIdCount[20]/evtIdCount[18]}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak">位置更新失败率(%)</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">位置更新频度(次/公里)</Item>
              <Item typeName="String" key="Exp">{(evtIdCount[19] )*1000/Mx_0806}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak">位置更新频度(次/公里)</Item>
            </Item>
          </Item>
        </Item>
      </Item>
    </Item>
  </Config>
</Configs>