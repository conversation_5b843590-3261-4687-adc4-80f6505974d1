﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTLowSpeedCauseQuery : DIYAnalyseByFileBackgroundBase
    {
        private FunctionCondition funcCond = null;
        protected static readonly object lockObj = new object();
        private static ZTLowSpeedCauseQuery instance = null;
        public static ZTLowSpeedCauseQuery GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new ZTLowSpeedCauseQuery();
                    }
                }
            }
            return instance;
        }

        protected ZTLowSpeedCauseQuery()
            : base(MainModel.GetInstance())
        {
            ServiceTypes.Clear();
            carrierID = CarrierType.ChinaMobile;
        }

        public override string Name
        {
            get
            {
                return "LTE低速率原因分析";
            }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22034, this.Name);
        }

        protected override bool getCondition()
        {
            segs = new List<LowSpeedSeg>();
            ConditionDlg dlg = new ConditionDlg(funcCond);
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            funcCond = dlg.Condition;
            return funcCond != null;
        }

        List<LowSpeedSeg> segs = new List<LowSpeedSeg>();
        protected override void fireShowForm()
        {
            if (segs.Count==0)
            {
                 MessageBox.Show("没有符合条件的数据。");
                 return;
            }
            ResultListForm frm = MainModel.GetObjectFromBlackboard(typeof(ResultListForm)) as ResultListForm;
            if (frm==null||frm.IsDisposed)
            {
                frm = new ResultListForm();
            }
            frm.FillData(segs, funcCond);
            frm.Owner = MainModel.MainForm;
            frm.Visible = true;
            frm.BringToFront();
            segs = null;
        }

        /// <summary>
        ///  保存符合条件的路段，并重置seg变量为null
        /// </summary>
        /// <param name="seg"></param>
        /// <param name="evts"></param>
        private void saveSegment(ref LowSpeedSeg seg, List<Event> evts)
        {
            if (seg == null || !funcCond.IsValidSegment(seg))
            {
                seg = null;
                return;
            }
            //保留路段前后10秒的事件，以便后续分析
            if (evts != null)
            {
                int bTime = seg.TestPoints[0].Time - 10;
                int eTime = seg.TestPoints[seg.TestPoints.Count - 1].Time + 10;
                foreach (Event e in evts)
                {
                    if (bTime <= e.Time && e.Time <= eTime)
                    {
                        seg.AddEvent(e);
                    }
                }
            }
            segs.Add(seg);
            seg = null;
        }

        protected override void doStatWithQuery()
        {
            try
            {
                foreach (DTFileDataManager fileDataManager in MainModel.DTDataManager.FileDataManagers)
                {
                    LowSpeedSeg seg = getLowSpeedSeg(fileDataManager);
                    //避免遗漏文件最后一路段
                    if (seg != null && segs.Contains(seg))
                    {
                        saveSegment(ref seg, fileDataManager.Events);
                    }
                    foreach (LowSpeedSeg segItem in segs)
                    {
                        funcCond.Judge(segItem, fileDataManager.Events, fileDataManager.TestPoints);
                        segItem.MakeSummary();
                    }
                }
            }
            catch (Exception e)
            {
                MessageBox.Show(e.Message + Environment.NewLine + e.StackTrace);
            }
        }

        private LowSpeedSeg getLowSpeedSeg(DTFileDataManager fileDataManager)
        {
            LowSpeedSeg seg = null;
            foreach (TestPoint tp in fileDataManager.TestPoints)
            {
                if (!isValidTestPoint(tp))
                {
                    saveSegment(ref seg, fileDataManager.Events);
                    continue;
                }
                if (funcCond.IsValidSpeed(tp))
                {
                    if (seg == null)
                    {
                        seg = new LowSpeedSeg();
                    }
                    seg.AddTestPoint(tp);
                }
                else
                {
                    saveSegment(ref seg, fileDataManager.Events);
                }
            }

            return seg;
        }
    }

    public class ZTLowSpeedCauseQuery_FDD : ZTLowSpeedCauseQuery
    {
        private static ZTLowSpeedCauseQuery_FDD instance = null;
        public static new ZTLowSpeedCauseQuery_FDD GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new ZTLowSpeedCauseQuery_FDD();
                    }
                }
            }
            return instance;
        }
        protected ZTLowSpeedCauseQuery_FDD()
            : base()
        {
            carrierID = CarrierType.ChinaUnicom;
            //this.Columns = new List<string>();
            //this.Columns.Add("lte_fdd_APP_type");
        }
        public override string Name
        {
            get
            {
                return "LTE_FDD低速率原因分析";
            }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 26000, 26074, this.Name);
        }
    }

    public class LowSpeedSeg
    {
        private string roadName = string.Empty;
        public string RoadName
        {
            get { return roadName; }
        }
        public string FileName
        {
            get
            {
                string fName = string.Empty;
                if (points.Count > 0)
                {
                    fName = points[0].FileName;
                }
                return fName;
            }
        }
        public double MidLng
        {
            get
            {
                return points[points.Count / 2].Longitude;
            }
        }

        public double MidLat
        {
            get
            {
                return points[points.Count / 2].Latitude;
            }
        }

        public double AvgSpeed
        {
            get;
            private set;
        }

        public void MakeSummary()
        {
            this.PointDetails.Sort();

            List<double> lngs = new List<double>();
            List<double> lats = new List<double>();
            lngs.Add(points[0].Longitude);
            lats.Add(points[0].Latitude);
            lngs.Add(MidLng);
            lats.Add(MidLat);
            lngs.Add(points[points.Count - 1].Longitude);
            lats.Add(points[points.Count - 1].Latitude);
            roadName = MasterCom.Util.GISManager.GetInstance().GetRoadPlaceDesc(lngs, lats);

            Dictionary<string, int> causeNumDic = new Dictionary<string, int>();
            double totalSpeed = 0;
            foreach (LowSpeedPointDetail detail in pntReasonDic.Values)
            {
                if (!causeNumDic.ContainsKey(detail.CauseScene))
                {
                    causeNumDic[detail.CauseScene] = 1;
                }
                else
                {
                    causeNumDic[detail.CauseScene]++;
                }
                if (detail.TestPoint is LTEFddTestPoint)
                {
                    totalSpeed += (double)detail.TestPoint["lte_fdd_APP_Speed_Mb"];
                }
                else
                {
                    totalSpeed += (double)detail.TestPoint["lte_APP_Speed_Mb"];
                }
            }
            AvgSpeed = Math.Round(totalSpeed / points.Count, 2);


            int maxNum = -1;
            foreach (string name in causeNumDic.Keys)
            {
                int num = causeNumDic[name];
                if (num > maxNum)
                {
                    MainCause = name;
                    maxNum = num;
                }
            }
            if (string.IsNullOrEmpty(MainCause))
            {
                MainCause = "未知原因";
            }
            MainCausePer = Math.Round(100.0 * maxNum / pntReasonDic.Count, 2);
        }
        public string MainCause { get; private set; }
        public double? MainCausePer
        { get; private set; }

        public int SampleCount
        {
            get { return points.Count; }
        }
        private readonly List<TestPoint> points = new List<TestPoint>();
        public List<TestPoint> TestPoints
        {
            get { return points; }
        }
        private readonly List<Event> events = new List<Event>();
        public List<Event> Events
        {
            get { return events; }
        }
        public void AddEvent(Event e)
        {
            if (!events.Contains(e))
            {
                events.Add(e);
            }
        }

        private List<LowSpeedPointDetail> details = null;
        public List<LowSpeedPointDetail> PointDetails
        {
            get
            {
                if (details == null)
                {
                    details = new List<LowSpeedPointDetail>(pntReasonDic.Values);
                }
                return details;
            }
        }
        private readonly Dictionary<TestPoint, LowSpeedPointDetail> pntReasonDic = new Dictionary<TestPoint, LowSpeedPointDetail>();
        public bool NeedJudge
        {
            get { return pntReasonDic.Count != points.Count; }
        }

        public void SetUnknowReason()
        {
            foreach (TestPoint pnt in TestPoints)
            {
                if (!pntReasonDic.ContainsKey(pnt))
                {
                    pntReasonDic[pnt] = new LowSpeedPointDetail(pnt, new UnknowReason());
                }
            }
        }

        public bool IsNeedJudge(TestPoint pnt)
        {
            return !pntReasonDic.ContainsKey(pnt);
        }

        internal void SetReason(LowSpeedPointDetail reasonInfo)
        {
            if (points.Contains(reasonInfo.TestPoint))
            {
                pntReasonDic[reasonInfo.TestPoint] = reasonInfo;
            }
        }

        double distance = 0;
        public double Distance
        {
            get { return distance; }
        }
        public double Second
        {
            get { return points[points.Count - 1].Time - points[0].Time; }
        }
        internal void AddTestPoint(TestPoint tp)
        {
            if (points.Count > 0)
            {
                distance += points[points.Count - 1].Distance2(tp);
            }
            points.Add(tp);
        }
    }

    public class LowSpeedPointDetail : IComparable<LowSpeedPointDetail>
    {
        public LowSpeedPointDetail(TestPoint pnt, CauseBase reason)
        {
            this.TestPoint = pnt;
            this.Reason = reason;
        }
        public TestPoint TestPoint
        {
            get;
            private set;
        }
        public string SCellName
        {
            get
            {
                if (SCell == null)
                {
                    return string.Empty;
                }
                return SCell.Name;
            }
        }
        public ICell SCell
        {
            get
            {
                if (TestPoint != null)
                {
                    return TestPoint.GetMainLTECell_TdOrFdd();
                }
                return null;
            }
        }
        public CauseBase Reason
        {
            get;
            set;
        }

        public double Speed
        {
            get
            {
                if (TestPoint is LTEFddTestPoint)
                {
                    return (double)TestPoint["lte_fdd_APP_Speed_Mb"];
                }
                return (double)TestPoint["lte_APP_Speed_Mb"];
            }
        }

        public string CauseType
        {
            get { return Reason.Ancestor.Name; }
        }

        public string CauseScene
        {
            get {
                string ret = string.Empty;
                if (Reason.Parent != null)
                {
                    if (Reason.Parent == Reason.Ancestor)
                    {
                        ret = Reason.Name;
                    }
                    else
                    {
                        ret = Reason.Parent.Name;
                    }
                }
                return ret;
            }
        }

        public string CauseDetailName
        {
            get {
                return Reason.Name;
            }
        }

        public float? RSRP
        {
            get
            {
                if (TestPoint is LTEFddTestPoint)
                {
                    return (float?)TestPoint["lte_fdd_RSRP"];
                }
                return (float?)TestPoint["lte_RSRP"];
            }
        }

        public float? SINR
        {
            get
            {
                if (TestPoint is LTEFddTestPoint)
                {
                    return (float?)TestPoint["lte_fdd_SINR"];
                }
                return (float?)TestPoint["lte_SINR"];
            }
        }

        public DateTime Time
        {
            get { return TestPoint.DateTime; }
        }

        public double Longitude
        {
            get { return TestPoint.Longitude; }
        }
        public double Latitude
        {
            get { return TestPoint.Latitude; }
        }

        #region IComparable<LowSpeedPointDetail> 成员

        public int CompareTo(LowSpeedPointDetail other)
        {
            return this.TestPoint.SN.CompareTo(other.TestPoint.SN);
        }

        #endregion
    }

    public class ZTLowSpeedCauseQuery_NB : ZTLowSpeedCauseQuery
    {
        private static ZTLowSpeedCauseQuery_NB instance = null;
        public static new ZTLowSpeedCauseQuery_NB GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new ZTLowSpeedCauseQuery_NB();
                    }
                }
            }
            return instance;
        }
        protected ZTLowSpeedCauseQuery_NB()
            : base()
        {

        }
        public override string Name
        {
            get
            {
                return "NBIOT低速率原因分析";
            }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 34000, 34024, this.Name);
        }
    }
}
