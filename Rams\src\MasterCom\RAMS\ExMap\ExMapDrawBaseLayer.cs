﻿using System;
using System.Collections.Generic;
using System.Text;
using GMap.NET;

namespace MasterCom.RAMS.ExMap
{
    public abstract class ExMapDrawBaseLayer
    {
        protected MTExGMap exMap = null;
        public bool Visible { get; set; } = true;
        protected ExMapDrawBaseLayer(MTExGMap map)
        {
            this.exMap = map;
        }
        public virtual void Draw(System.Drawing.Graphics g, PointLatLng ltPt, PointLatLng brPt)
        {
            
        }

        public abstract string Alias
        {
            get;
        }

        
    }
}
