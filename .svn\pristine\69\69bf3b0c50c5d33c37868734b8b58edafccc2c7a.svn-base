﻿using System.Collections.Generic;
using MasterCom.MTGis;
using System.Data;

namespace MasterCom.RAMS.ZTFunc
{
    public class NbIotMgrsOverlapCoverageStater : NbIotMgrsOverlapCoverageRatioStater
    {
        private NbIotMgrsFuncItem tmpFuncItem;
        
        public bool CheckAllRSRP { get; set; }
        public double AllRSRPMin { get; set; }
        private string type = "";

        List<CoverageRegion> coverageRegionList;

        public override void DoStat(NbIotMgrsFuncItem curFuncItem)
        {
            this.tmpFuncItem = curFuncItem;
        }

        public override void SetResultControl()
        {
            NbIotMgrsOverlapCoverageResult resultControl = new NbIotMgrsOverlapCoverageResult();
            object[] values = tmpFuncItem.FuncCondtion as object[];
            CurRsrpMin = (double)values[0];
            CheckAllRSRP = (bool)values[1];
            AllRSRPMin = (double)values[2];
            RsrpDis = (double)values[3];
            resultControl.FillData(tmpFuncItem);
            resultControlList = new List<NbIotMgrsResultControlBase>() { resultControl };
        }

        public DataSet GetDataSet(string type)
        {
            this.type = type;
            DataSet ds = new DataSet();

            DataTable mainTable = new DataTable("Main");
            mainTable.Columns.AddRange(BuildColumns());
            coverageRegionList = new List<CoverageRegion>();
            //构建主表
            List<List<object>> dataRow;
            dataRow = dealWithData(cmCarrierAreaResult.GridList, "中国移动", true);
            mainTable.Rows.Add(dataRow[0].ToArray());
            dataRow = dealWithData(cuCarrierAreaResult.GridList, "中国联通", true);
            mainTable.Rows.Add(dataRow[0].ToArray());
            dataRow = dealWithData(ctCarrierAreaResult.GridList, "中国电信", true);
            mainTable.Rows.Add(dataRow[0].ToArray());

            ds.Tables.Add(mainTable);
            if (selectPolygons.Count > 1)
            {
                //构建从表
                DataTable subTable = new DataTable("Sub");
                subTable.Columns.AddRange(BuildColumns());
                dataRow = dealWithData(cmCarrierAreaResult.GridList, "中国移动", false);
                foreach (var item in dataRow)
                {
                    subTable.Rows.Add(item.ToArray());
                }
                dataRow = dealWithData(cuCarrierAreaResult.GridList, "中国联通", false);
                foreach (var item in dataRow)
                {
                    subTable.Rows.Add(item.ToArray());
                }
                dataRow = dealWithData(ctCarrierAreaResult.GridList, "中国电信", false);
                foreach (var item in dataRow)
                {
                    subTable.Rows.Add(item.ToArray());
                }
                ds.Tables.Add(subTable);

                //添加主从表关联
                DataColumn keyColumn = ds.Tables["Main"].Columns["运营商"];       //主键
                DataColumn foreignColumn = ds.Tables["Sub"].Columns["运营商"];    //外键
                //第一个参数必须和gridView中的levelName相同
                DataRelation dr = new DataRelation("Area", keyColumn, foreignColumn);
                ds.Relations.Add(dr);
            }
            return ds;
        }

        protected List<List<object>> dealWithData(Dictionary<string, List<ScanGridInfo>> gridList, string carrierTypeName, bool allArea)
        {
            List<List<object>> result = new List<List<object>>();
            if (allArea)
            {
                List<object> dataRow = getAllCoverageRegionList(gridList, carrierTypeName);
                result.Add(dataRow);
            }
            else
            {
                result = getCoverageRegionList(gridList, carrierTypeName);
            }

            return result;
        }

        protected List<object> getAllCoverageRegionList(Dictionary<string, List<ScanGridInfo>> gridList, string carrierTypeName)
        {
            int invalidCnt = 0;
            int[] rangesCnt = new int[ZTScanGridCoverageLayer.Ranges.ColorRanges.Count];
            string polygonName = "所有区域";
            foreach (var item in gridList)
            {
                //将栅格中的小区按rsrp降序排序
                item.Value.Sort((x, y) => { return -x.R0_RP.CompareTo(y.R0_RP); });
                //最强RSRP小区
                ScanGridInfo grid = item.Value[0];
                CoverageRegion coverageRegion = new CoverageRegion();
                if (grid.R0_RP >= CurRsrpMin)
                {
                    invalidCnt = addAllCoverageRegionData(invalidCnt, rangesCnt, item, grid, coverageRegion);
                }
                else
                {
                    coverageRegion.CellGrid = new List<ScanGridInfo>();
                    coverageRegion.CellGrid.Add(grid);
                    coverageRegion.Invaild = true;
                    coverageRegionList.Add(coverageRegion);
                    ++invalidCnt;
                }
            }

            // add to table
            List<object> dataRow = new List<object>();
            dataRow.Add(carrierTypeName);
            dataRow.Add(polygonName);
            foreach (int cnt in rangesCnt)
            {
                dataRow.Add(cnt);
            }
            dataRow.Add(invalidCnt);
            dataRow.Add(gridList.Count);

            return dataRow;
        }

        private int addAllCoverageRegionData(int invalidCnt, int[] rangesCnt, KeyValuePair<string, List<ScanGridInfo>> item, ScanGridInfo grid, CoverageRegion coverageRegion)
        {
            List<ScanGridInfo> cellList = NbIotMgrsGridHelper.FilterCells(item.Value, type);
            if (cellList.Count == 0)
            {
                coverageRegion.CellGrid = new List<ScanGridInfo>();
                coverageRegion.CellGrid.Add(grid);
                coverageRegion.Invaild = true;
                coverageRegionList.Add(coverageRegion);
                ++invalidCnt;
            }
            else
            {
                coverageRegion.CellGrid = new List<ScanGridInfo>();
                for (int i = 0; i < cellList.Count; i++)
                {
                    if (CheckAllRSRP && cellList[i].R0_RP < AllRSRPMin)
                    {
                        break;
                    }

                    double curRsrpDis = grid.R0_RP - cellList[i].R0_RP;
                    if (curRsrpDis < RsrpDis)
                    {
                        coverageRegion.CellGrid.Add(cellList[i]);
                    }
                    else
                    {
                        break;
                    }
                }
                coverageRegionList.Add(coverageRegion);

                int idx = ZTScanGridCoverageLayer.Ranges.GetIndex(coverageRegion.CellGrid.Count);
                ++rangesCnt[idx];
            }

            return invalidCnt;
        }

        protected List<List<object>> getCoverageRegionList(Dictionary<string, List<ScanGridInfo>> gridList, string carrierTypeName)
        {
            Dictionary<string, int> areaInvalidCnt = new Dictionary<string, int>();
            Dictionary<string, int[]> areaRangesCnt = new Dictionary<string, int[]>();
            string polygonName;
            foreach (var item in gridList)
            {
                //先判断栅格所属区域
                polygonName = "";
                foreach (MTPolygon polygon in selectPolygons)
                {
                    if (polygon.CheckPointInRegion(item.Value[0].CentLng, item.Value[0].CentLat))
                    {
                        polygonName = polygon.Name;
                        if (!areaRangesCnt.ContainsKey(polygonName))
                        {
                            areaRangesCnt[polygonName] = new int[ZTScanGridCoverageLayer.Ranges.ColorRanges.Count];
                            areaInvalidCnt[polygonName] = 0;
                        }
                        break;
                    }
                }

                //将栅格中的小区按rsrp降序排序
                item.Value.Sort((x, y) => { return -x.R0_RP.CompareTo(y.R0_RP); });
                //最强RSRP小区
                ScanGridInfo grid = item.Value[0];
                CoverageRegion coverageRegion = new CoverageRegion();
                if (grid.R0_RP >= CurRsrpMin)
                {
                    addCoverageRegionData(areaInvalidCnt, areaRangesCnt, polygonName, item, grid, coverageRegion);
                }
                else
                {
                    coverageRegion.CellGrid = new List<ScanGridInfo>();
                    ++areaInvalidCnt[polygonName];
                }
            }

            // add to table
            List<List<object>> result = addResultToTable(carrierTypeName, areaInvalidCnt, areaRangesCnt);
            return result;
        }

        private List<List<object>> addResultToTable(string carrierTypeName, Dictionary<string, int> areaInvalidCnt, Dictionary<string, int[]> areaRangesCnt)
        {
            List<List<object>> result = new List<List<object>>();
            foreach (var item in areaRangesCnt)
            {
                List<object> dataRow = new List<object>();
                dataRow.Add(carrierTypeName);
                dataRow.Add(item.Key);
                int areaTotalCount = 0;
                foreach (int cnt in item.Value)
                {
                    areaTotalCount += cnt;
                    dataRow.Add(cnt);
                }
                areaTotalCount += areaInvalidCnt[item.Key];
                dataRow.Add(areaInvalidCnt[item.Key]);
                dataRow.Add(areaTotalCount);
                result.Add(dataRow);
            }

            return result;
        }

        private void addCoverageRegionData(Dictionary<string, int> areaInvalidCnt, Dictionary<string, int[]> areaRangesCnt, string polygonName, KeyValuePair<string, List<ScanGridInfo>> item, ScanGridInfo grid, CoverageRegion coverageRegion)
        {
            List<ScanGridInfo> cellList = NbIotMgrsGridHelper.FilterCells(item.Value, type);
            if (cellList.Count == 0)
            {
                coverageRegion.CellGrid = new List<ScanGridInfo>();
                ++areaInvalidCnt[polygonName];
            }
            else
            {
                coverageRegion.CellGrid = new List<ScanGridInfo>();
                for (int i = 0; i < cellList.Count; i++)
                {
                    if (CheckAllRSRP && cellList[i].R0_RP < AllRSRPMin)
                    {
                        break;
                    }

                    double curRsrpDis = grid.R0_RP - cellList[i].R0_RP;
                    if (curRsrpDis < RsrpDis)
                    {
                        coverageRegion.CellGrid.Add(cellList[i]);
                    }
                    else
                    {
                        break;
                    }
                }

                int idx = ZTScanGridCoverageLayer.Ranges.GetIndex(coverageRegion.CellGrid.Count);
                ++areaRangesCnt[polygonName][idx];
            }
        }

        public List<CoverageRegion> GetCoverageRegionList()
        {
            return coverageRegionList;
        }

        public override Dictionary<string, string> GetResultData()
        {
            return new Dictionary<string, string>();
        }

        public override void Clear()
        {
            this.tmpFuncItem = null;
        }

        private DataColumn[] BuildColumns()
        {
            List<DataColumn> columns = new List<DataColumn>();
            columns.Add(new DataColumn("运营商", typeof(string)));
            columns.Add(new DataColumn("区域", typeof(string)));

            for (int i = 0; i < ZTScanGridCoverageLayer.Ranges.ColorRanges.Count; ++i)
            {
                columns.Add(new DataColumn(ZTScanGridCoverageLayer.Ranges.ColorRanges[i].desInfo, typeof(string)));
            }
            columns.Add(new DataColumn("无效栅格", typeof(string))); // 无效栅格定义：最强信号少于设定条件
            columns.Add(new DataColumn("栅格总数", typeof(string))); // 栅格总数定义：区域内所有栅格，包括无效栅格
            return columns.ToArray();
        }
    }

    public class NBMgrsCoverageBandType
    {
        public NBMgrsCoverageBandType()
        {
            BandType = new Dictionary<string, string>();
            BandType.Add("不分段", "不分段");
        }
        
        public Dictionary<string, string> BandType { get; set; }
    }
}
