﻿using MasterCom.RAMS.KPI_Statistics;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc.ZTStationAcceptance_XJ;
using MasterCom.Util.UiEx;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using Excel = Microsoft.Office.Interop.Excel;
using FileInfo = MasterCom.RAMS.Model.FileInfo;

namespace MasterCom.RAMS.ZTFunc
{
    public class NROptimizationcCustersBase : QueryKpiStatByFiles
    {
        public override string Name { get { return "5G簇优化"; } }
        protected string errMsg;

        //簇优化模板所在目录
        protected string workDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, @"userdata\NROptimizationcCusters\簇优化模板.xlsx");

        public string SaveFolder { get; set; }
        public Dictionary<string, BtsInfoBase> btsInfoDic { get; set; }
        public string custersName { get; set; }
        protected string uploadDic = "";
        protected string picPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, @"userdata\NROptimizationcCusters\Pictures");
        NROptimizationcIndicator nROptimizationcIndicator = null;

        protected override bool getConditionBeforeQuery()
        {
            string savePath = SaveFolder;
            uploadDic = Path.Combine(savePath, "NR簇" + custersName + "网络优化报告.xlsx");
            System.IO.File.Copy(workDir, uploadDic, true);
            KpiDataManager = new KPIDataManager();
            return true;
        }

        protected override void handleStatEvent(Event evt)
        {
            StatDataEvent eventData = new StatDataEvent(evt, this.curReportStyle != null && this.curReportStyle.IsSeparateEvtByServiceID, needSeparateByFileName(evt));
            KpiDataManager.AddStatData(string.Empty, curFile, curFile, eventData, false);
        }

        protected override void recieveAndHandleSpecificStatData(Package package, List<StatImgDefItem> curImgColumnDef, KPIStatDataBase singleStatData)
        {
            fillStatData(package, curImgColumnDef, singleStatData);
            KpiDataManager.AddStatData(string.Empty, curFile, curFile, singleStatData, false);
        }

        protected override void afterRecieveAllData(params object[] reservedParams)
        {
            nROptimizationcIndicator = new NROptimizationcIndicator();
            List<KPIDataGroup> dataSet = KpiDataManager.GetReportGroupDataSet();
            dataSet[dataSet.Count - 1].statisficNROptimizationc(nROptimizationcIndicator);
        }

        protected override void fireShowResult()
        {
            doPostReplayAction();
        }

        protected override string getStatImgNeededTriadID(params object[] paramSet)
        {
            string strs = "";
            List<string> formulaSet = new List<string>();
            #region 报告参数
            /*
            formulaSet.Add("{Nr_8004001F/Nr_80040005*100}");//5G时长驻留比
            formulaSet.Add("{Nr_BA040053/Nr_BA040052*100 }");//城区5G道路测试覆盖率(SS-RSRP≥-91dBm&SS-SINR≥-3)
            formulaSet.Add("{Nr_BA0400D7/Nr_BA040001 *100}");//城区5G道路测试覆盖率(SS-RSRP≥-94dBm&SS-SINR≥-3)
            formulaSet.Add("{Nr_BA0400DC/Nr_BA040001 *100 }");//城区5G道路测试覆盖率(SS-RSRP≥-96dBm&SS-SINR≥-3)
            formulaSet.Add("{Nr_BA040103/1000/1000}");//5G下行平均吞吐率
            formulaSet.Add("{Nr_BA0401AA/Nr_BA0401A4 * 100}");//5G下行吞吐率高于30Mbps占比
            formulaSet.Add("{Nr_BA0400FC/1000/1000}");//5G上行平均吞吐率
            formulaSet.Add("{Nr_BA0401AE/Nr_BA0401A6 *100}");//5G上行吞吐率高于5Mbps占比
            formulaSet.Add("{(evtIdCount[9292]/evtIdCount[9291])*100}");//SA连接成功率
            formulaSet.Add("{evtIdCount[9292]}");//SA连接成功率(分子)
            formulaSet.Add("{evtIdCount[9291]}");//SA连接成功率（分母）
            formulaSet.Add("{(evtIdCount[9332]/evtIdCount[9291])*100}");//NR掉线率
            formulaSet.Add("{evtIdCount[9332]}");//NR掉线率(分子)
            formulaSet.Add("{evtIdCount[9291]}");//NR掉线率（分母）
            formulaSet.Add("{100*(evtIdCount[9295]+evtIdCount[9298])/(evtIdCount[9294]+evtIdCount[9297])}");//5G切换成功率
            formulaSet.Add("{evtIdCount[9295]+evtIdCount[9298]}");//5G切换成功率(分子)
            formulaSet.Add("{evtIdCount[9294]+evtIdCount[9297]}");//5G切换成功率（分母）
            formulaSet.Add("{evtIdCount[9540] / evtIdCount[9539] * 100}");//EPS Fallback端到端呼叫成功率
            formulaSet.Add("{evtIdCount[9540]}");//EPS Fallback端到端呼叫成功率(分子)
            formulaSet.Add("{evtIdCount[9539]}");//EPS Fallback端到端呼叫成功率（分母）
            formulaSet.Add("{100 - 100*(evtIdCount[9538] / (evtIdCount[9335]+evtIdCount[9336]+evtIdCount[9347]+evtIdCount[9348]))}");//EPS Fallback呼叫时延小于5秒比例
             */


            #endregion
            formulaSet.Add("{Nr_8004001F/Nr_80040005*100}");//5G时长驻留比
            formulaSet.Add("{Nr_BA040053/Nr_BA040052*100 }");//5G道路测试覆盖率指标的统计(SS-RSRP≥-91dBm&SS-SINR≥-3)
            formulaSet.Add("{Nr_BA040103/1000/1000}");//5G下行平均速率指标统计
            formulaSet.Add("{Nr_BA040205/Nr_BA0401A4*100}");//5G下行速率高于100Mbps占比指标的统计
            formulaSet.Add("{Nr_BA0400FC/1000/1000}");//5G上行平均速率指标的统计
            formulaSet.Add("{Nr_BA040206/Nr_BA0401A6*100}");//5G上行速率高于2Mbps占比指标的统计
            formulaSet.Add("{(value3[9074]/evtIdCount[9021])*100}");//5G SCG添加成功率指标的统计
            formulaSet.Add("{(evtIdCount[9332]/evtIdCount[9291])*100}");//NR掉线率
            formulaSet.Add("{100*(evtIdCount[9295]+evtIdCount[9298])/(evtIdCount[9294]+evtIdCount[9297])}");//5G切换成功率
            //formulaSet.Add("{}");//锚点MR覆盖率
            formulaSet.Add("{100*Nr_BA0400DB/Nr_BA040050}");//4G锚点覆盖率

            strs = this.getTriadIDIgnoreServiceType(formulaSet);
            return strs;
        }

        /// <summary>
        /// 分析完所有文件后调用
        /// </summary>
        protected void doPostReplayAction()
        {
            WaitTextBox.Show("正在处理数据并导出Excel文件...", afterAnalyzeInThread);
        }

        protected void afterAnalyzeInThread()
        {
            try
            {
                Excel.Application xlApp = null;
                try
                {
                    WaitTextBox.Text = "正在导出Excel...";
                    xlApp = new Excel.Application();
                    xlApp.Visible = false;
                    Excel.Workbook eBook = xlApp.Workbooks.Open(uploadDic,
                        Type.Missing, Type.Missing, Type.Missing, Type.Missing,
                        Type.Missing, Type.Missing, Type.Missing, Type.Missing,
                        Type.Missing, Type.Missing, Type.Missing, Type.Missing,
                        Type.Missing, Type.Missing);
                    fillResult(eBook);
                    eBook.Save();
                    WaitTextBox.Text = "簇优化导出完成！";
                    System.Windows.Forms.MessageBox.Show(string.Format("簇优化报告导出完成!"), Name, System.Windows.Forms.MessageBoxButtons.OK);
                    eBook.Close(Type.Missing, Type.Missing, Type.Missing);
                }
                finally
                {
                    if (xlApp != null)
                    {
                        xlApp.Quit();
                    }
                }
            }
            catch (Exception ex)
            {
                errMsg = ex.Message + Environment.NewLine + ex.StackTrace;
            }
            finally
            {
                System.Threading.Thread.Sleep(300);
                WaitTextBox.Close();
            }
        }

        public void fillResult(Excel.Workbook eBook)
        {
            //指标插入
            Excel.Worksheet sheet = (Excel.Worksheet)eBook.Sheets["Sheet1"];
            sheet.get_Range("C3").set_Value(Type.Missing, nROptimizationcIndicator.NRDurationRate + "%");
            sheet.get_Range("C4").set_Value(Type.Missing, nROptimizationcIndicator.NRCoreCityCoverRate + "%");
            sheet.get_Range("C5").set_Value(Type.Missing, nROptimizationcIndicator.NRDownAvgThroughput);
            sheet.get_Range("C6").set_Value(Type.Missing, nROptimizationcIndicator.NRDownThroughputRate + "%");
            sheet.get_Range("C7").set_Value(Type.Missing, nROptimizationcIndicator.NRUploadAvgThroughput);
            sheet.get_Range("C8").set_Value(Type.Missing, nROptimizationcIndicator.NRUploadThroughputRate + "%");
            sheet.get_Range("C9").set_Value(Type.Missing, nROptimizationcIndicator.NRSCGAddSuccess + "%");
            sheet.get_Range("C10").set_Value(Type.Missing, nROptimizationcIndicator.NRDroppedRate + "%");
            sheet.get_Range("C11").set_Value(Type.Missing, nROptimizationcIndicator.NRSwitchRate + "%");
            sheet.get_Range("C12").set_Value(Type.Missing, nROptimizationcIndicator.LteNRCoverRate + "%");
            sheet.get_Range("C13").set_Value(Type.Missing, nROptimizationcIndicator.LteCoverRate + "%");

            InsertCoverPic(eBook); //覆盖图插入

            InsertDistributedPic(eBook);//分布式图插入
        }

        public double width = 500;
        public double height = 272;
        public void InsertCoverPic(Excel.Workbook eBook)
        {
            Excel.Worksheet sheet2 = (Excel.Worksheet)eBook.Sheets["Sheet2"];
            string picsPath = Path.Combine(picPath, custersName);
            DirectoryInfo dir = new DirectoryInfo(picsPath);
            var files = dir.GetFiles();
            foreach (var file in files)
            {
                if (file.Name.Contains("NR_SS_RSRP"))
                {
                    insertExcelPicture(sheet2, file.FullName, 4, 1, width, height);
                }
                else if (file.Name.Contains("NR_SS_SINR"))
                {
                    insertExcelPicture(sheet2, file.FullName, 29, 1, width, height);
                }
                else if (file.Name.Contains("NR_Throughput_MAC_DL"))
                {
                    insertExcelPicture(sheet2, file.FullName, 54, 1, width, height);
                }
                else if (file.Name.Contains("NR_Throughput_MAC_UL"))
                {
                    insertExcelPicture(sheet2, file.FullName, 79, 1, width, height);
                }
                else if (file.Name.Contains("NR_lte_RSRP"))
                {
                    insertExcelPicture(sheet2, file.FullName, 104, 1, width, height);
                }
                else if (file.Name.Contains("NR_lte_SINR"))
                {
                    insertExcelPicture(sheet2, file.FullName, 129, 1, width, height);
                }
            }
        }

        public void InsertDistributedPic(Excel.Workbook eBook)
        {
            Excel.Worksheet sheet2 = (Excel.Worksheet)eBook.Sheets["Sheet3"];
            string picsPath = Path.Combine(picPath, custersName);
            DirectoryInfo dir = new DirectoryInfo(picsPath);
            var files = dir.GetFiles();
            foreach (var file in files)
            {
                if (file.Name.Contains("NRWeakCover"))
                {
                    insertExcelPicture(sheet2, file.FullName, 4, 1, width, height);
                }
                else if (file.Name.Contains("NRSinr"))
                {
                    insertExcelPicture(sheet2, file.FullName, 29, 1, width, height);
                }
                else if (file.Name.Contains("NRLowDown"))
                {
                    insertExcelPicture(sheet2, file.FullName, 54, 1, width, height);
                }
                else if (file.Name.Contains("NRLowUpload"))
                {
                    insertExcelPicture(sheet2, file.FullName, 79, 1, width, height);
                }
                else if (file.Name.Contains("NRSwtich"))
                {
                    insertExcelPicture(sheet2, file.FullName, 104, 1, width, height);
                }
                else if (file.Name.Contains("NRDrop"))
                {
                    insertExcelPicture(sheet2, file.FullName, 129, 1, width, height);
                }
                else if (file.Name.Contains("NRConnect"))
                {
                    insertExcelPicture(sheet2, file.FullName, 154, 1, width, height);
                }
                else if (file.Name.Contains("NRTaskOfNet"))
                {
                    insertExcelPicture(sheet2, file.FullName, 179, 1, width, height);
                }
                else if (file.Name.Contains("NRLteSwitchFail"))
                {
                    insertExcelPicture(sheet2, file.FullName, 204, 1, width, height);
                }
                else if (file.Name.Contains("NRLteDrop"))
                {
                    insertExcelPicture(sheet2, file.FullName, 229, 1, width, height);
                }
                else if (file.Name.Contains("NRLteConnect"))
                {
                    insertExcelPicture(sheet2, file.FullName, 254, 1, width, height);
                }
            }
        }

        /// <summary>
        /// 簇优化指标
        /// </summary>
        public class NROptimizationcIndicator
        {
            public double occupyDuration;
            public double connetionDurant;

            /// <summary>
            /// 5G时长驻留比
            /// </summary>
            public double NRDurationRate
            {
                get
                {
                    if (occupyDuration == 0 || connetionDurant == 0)
                    {
                        return 0;
                    }
                    else
                    {
                        return Math.Round((occupyDuration / connetionDurant * 100), 2);
                    }
                }
            }


            public double corecityFZ;
            public double corecityFM;
            /// <summary>
            /// 5G道路测试覆盖率指标统计
            /// </summary>
            public double NRCoreCityCoverRate
            {
                get
                {
                    if (corecityFZ == 0 || corecityFM == 0)
                    {
                        return 0;
                    }
                    else
                    {
                        return Math.Round((corecityFZ / corecityFM * 100), 2);
                    }
                }
            }

            public double downAvgFZ;
            /// <summary>
            /// 5G下行平均速率指标统计
            /// </summary>
            public double NRDownAvgThroughput
            {
                get
                {
                    if (downAvgFZ == 0)
                    {
                        return 0;
                    }
                    else
                    {
                        return Math.Round((downAvgFZ / 1000 / 1000), 2);
                    }
                }
            }

            public double downFZ;
            public double downFM;
            /// <summary>
            /// 5G下行速率高于100Mbps占比指标统计
            /// </summary>
            public double NRDownThroughputRate
            {
                get
                {
                    if (downFZ == 0 || downFM == 0)
                    {
                        return 0;
                    }
                    else
                    {
                        return Math.Round((downFZ / downFM * 100), 2);
                    }
                }
            }

            public double uploadAvgFZ;
            /// <summary>
            /// 5G上行平均速率指标统计
            /// </summary>
            public double NRUploadAvgThroughput
            {
                get
                {
                    if (uploadAvgFZ == 0)
                    {
                        return 0;
                    }
                    else
                    {
                        return Math.Round((uploadAvgFZ / 1000 / 1000), 2);
                    }
                }
            }

            public double uploadFZ;
            public double uploadFM;
            /// <summary>
            /// 5G上行速率高于2Mbps占比指标统计
            /// </summary>
            public double NRUploadThroughputRate
            {
                get
                {
                    if (uploadFZ == 0 || uploadFM == 0)
                    {
                        return 0;
                    }
                    else
                    {
                        return Math.Round((uploadFZ / uploadFM * 100), 2);
                    }
                }
            }

            public double addsucessFZ;
            public double addsucessFM;
            /// <summary>
            /// 5G SCG添加成功率指标统计
            /// </summary>
            public double NRSCGAddSuccess
            {
                get
                {
                    if (addsucessFZ == 0 || addsucessFM == 0)
                    {
                        return 0;
                    }
                    else
                    {
                        return Math.Round((addsucessFZ / addsucessFM * 100), 2);
                    }
                }
            }

            public double droppedFZ;
            public double droppedFM;
            /// <summary>
            /// NR掉线率指标统计
            /// </summary>
            public double NRDroppedRate
            {
                get
                {
                    if (droppedFZ == 0 || droppedFM == 0)
                    {
                        return 0;
                    }
                    else
                    {
                        return Math.Round((droppedFZ / droppedFM * 100), 2);
                    }
                }
            }

            public double switchdFZ;
            public double switchFM;
            /// <summary>
            /// 5G切换成功率指标统计
            /// </summary>
            public double NRSwitchRate
            {
                get
                {
                    if (switchdFZ == 0 || switchFM == 0)
                    {
                        return 0;
                    }
                    else
                    {
                        return Math.Round((switchdFZ / switchFM * 100), 2);
                    }
                }
            }

            public double ltenrcoverFZ;
            public double ltenrcoverFM;
            /// <summary>
            /// 锚点MR覆盖率指标统计
            /// </summary>
            public double LteNRCoverRate
            {
                get
                {
                    if (ltenrcoverFZ == 0 || ltenrcoverFM == 0)
                    {
                        return 0;
                    }
                    else
                    {
                        return Math.Round((ltenrcoverFZ / ltenrcoverFM * 100), 2);
                    }
                }
                set { value = 0; }
            }

            public double ltecoverFZ;
            public double ltecoverFM;
            /// <summary>
            /// 4G锚点覆盖率指标统计
            /// </summary>
            public double LteCoverRate
            {
                get
                {
                    if (ltecoverFZ == 0 || ltecoverFM == 0)
                    {
                        return 0;
                    }
                    else
                    {
                        return Math.Round((ltecoverFZ / ltecoverFM * 100), 2);
                    }
                }
            }
        }

        protected void insertExcelPicture(Excel.Worksheet eSheet, string picPath
    , int ltRowIndex, int ltColIndex, double width, double height)
        {
            if (System.IO.File.Exists(picPath))
            {
                Excel.Range rng = eSheet.get_Range(eSheet.Cells[ltRowIndex, ltColIndex], eSheet.Cells[ltRowIndex, ltColIndex]);
                eSheet.Shapes.AddPicture(picPath,
                    Microsoft.Office.Core.MsoTriState.msoFalse,
                    Microsoft.Office.Core.MsoTriState.msoCTrue,
                    (float)(double)rng.Left, (float)(double)rng.Top, (float)width, (float)height);
            }
        }
    }
}
