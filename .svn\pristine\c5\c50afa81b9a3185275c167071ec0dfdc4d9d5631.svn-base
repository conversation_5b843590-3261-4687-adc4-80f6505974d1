﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.Net
{
    public static partial class RequestType
    {
        public const byte REQTYPE_MCFILETIME = 0xab;
        public const byte RESTYPE_MCFILETIME = 0xab;
    }

    public class QueryMCFileTime : QueryBase
    {
        const int RESTYPE_MCFILETIMECONTINUE = 1;
        const int RESTYPE_MCFILETIMEEND = 2;

        public Dictionary<string, MCFileTime> NameFileTimeList { get; set; }

        public QueryMCFileTime(MainModel mainModel)
            : base(mainModel)
        {
            NameFileTimeList = new Dictionary<string, MCFileTime>();
        }

        public override string Name
        {
            get { return "查询接口文件时间"; }
        }

        public override string IconName
        {
            get { return "Images/cellcover.gif"; }
        }

        protected override bool isValidCondition()
        {
            return true;
        }

        protected override void query()
        {
            NameFileTimeList.Clear();
            ClientProxy clientProxy = connectServerWithWaitBox();
            if (clientProxy.ConnectResult != ConnectResult.Success)
            {
                ErrorInfo = "连接服务器失败!";
                return;
            }
            try
            {
                WaitBox.Show("校验接口...", queryInThread, clientProxy);
            }
            finally
            {
                clientProxy.Close();
            }
        }

        protected virtual void queryInThread(object o)
        {
            try
            {
                System.Threading.Thread.Sleep(20);
                ClientProxy clientProxy = (ClientProxy)o;
                Package package = clientProxy.Package;

                package.Command = Command.DataManage;
                package.SubCommand = SubCommand.Request;
                package.Content.Type = RequestType.REQTYPE_MCFILETIME;
                package.Content.PrepareAddParam();
                clientProxy.Send();
                int index = 0;

                while (true)
                {
                    clientProxy.Recieve();

                    if (!recieveData(package.Content))
                    {
                        break;
                    }
                    if ((Math.Log(index++) * 8) > WaitBox.ProgressPercent)
                    {
                        WaitBox.ProgressPercent++;
                    }
                }
            }
            catch (Exception e)
            {
                ErrorInfo += e.Message;
            }
            finally
            {
                WaitBox.Close();
            }
        }

        protected bool recieveData(Content content)
        {
            try
            {
                switch (content.Type)
                {
                    case RESTYPE_MCFILETIMECONTINUE:
                        content.PrepareGetParam();
                        string name = content.GetParamString();
                        string time = content.GetParamString();
                        NameFileTimeList[name] = new MCFileTime(name, Convert.ToDateTime(time));
                        return true;
                    case RESTYPE_MCFILETIMEEND:
                    case ResponseType.END:
                        return false;
                    default:
                        return false;
                }
            }
            catch
            {
                return false;
            }
        }
    }

    public class MCFileTime
    {
        public string FileName
        {
            get;
            private set;
        }
        public DateTime LastWriteTime
        { get; private set; }
        /// <summary>
        /// 默认需要下载MC文件到本地
        /// </summary>
        public bool NeedDownload { get; set; } = true;

        public MCFileTime(string fileName, DateTime lastWriteTime)
        {
            this.FileName = fileName;
            this.LastWriteTime = lastWriteTime;
        }
    }
}
