﻿using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class LteComplaintForm : MinCloseForm
    {
        public LteComplaintForm(MainModel mainModel)
            : base(mainModel)
        {
            InitializeComponent();
        }
        public void FillData(Dictionary<string, List<ComplaitInfo>> complaitInfoDic)
        {
            listView.Items.Clear();

            listView.AutoResizeColumns(ColumnHeaderAutoResizeStyle.HeaderSize);

            foreach (string strCity in complaitInfoDic.Keys)
            {
                foreach (ComplaitInfo info in complaitInfoDic[strCity])
                {
                    ListViewItem item = new ListViewItem();
                    item.SubItems[0].Text = info.Idx + "";
                    item.SubItems.Add(info.StrNo);
                    item.SubItems.Add(info.StrID);
                    item.SubItems.Add(info.StrCity);
                    item.SubItems.Add(info.StrRegion);
                    item.SubItems.Add(info.StrAddr);
                    item.SubItems.Add(info.StrAddr2);
                    item.SubItems.Add(info.FLongitude.ToString());
                    item.SubItems.Add(info.FLatitude.ToString());
                    setCellValue(ref item, info.gsmCell);
                    setCellValue(ref item, info.tdCell);
                    setCellValue(ref item, info.lteCell);
                    item.SubItems.Add("");
                    listView.Items.Add(item);
                }
            }
        }

        private void setCellValue(ref ListViewItem item, NOPCellInfo cellInfo)
        {
            if (cellInfo == null)
            {
                for (int i = 0; i < 9; i++)
                {
                    item.SubItems.Add("");
                }
            }
            else
            {
                item.SubItems.Add("");
                item.SubItems.Add(cellInfo.StrCellName);
                item.SubItems.Add(cellInfo.StrOMC);
                item.SubItems.Add(cellInfo.StrCGI);
                item.SubItems.Add(cellInfo.Longitude.ToString());
                item.SubItems.Add(cellInfo.Latitude.ToString());
                item.SubItems.Add(Math.Round(cellInfo.DDist, 2, MidpointRounding.AwayFromZero).ToString());
                item.SubItems.Add(cellInfo.iDiffDirection.ToString());
                item.SubItems.Add("0");
            }
        }

        private void outPutExcel_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(listView);
        }
    }
}
