﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause
{
    public partial class RepeatCoverPnl : UserControl
    {
        public RepeatCoverPnl()
        {
            InitializeComponent();
        }

        RepeatCoverCause mainReason = null;

        public void LinkCondition(RepeatCoverCause reason)
        {
            this.mainReason = reason;

            numCellRSRP.Value = (decimal)reason.RSRPDiff;
            numCellRSRP.ValueChanged += numCellRSRP_ValueChanged;

            numLowSpeedSec.Value = (decimal)reason.Second;
            numLowSpeedSec.ValueChanged += numLowSpeedSec_ValueChanged;
        }


        void numLowSpeedSec_ValueChanged(object sender, EventArgs e)
        {
            mainReason.Second = (int)numLowSpeedSec.Value;
        }

        void numCellRSRP_ValueChanged(object sender, EventArgs e)
        {
            mainReason.RSRPDiff = (int)numCellRSRP.Value;
        }
    }
}
