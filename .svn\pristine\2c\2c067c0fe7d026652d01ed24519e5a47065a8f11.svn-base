﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using MasterCom.Util;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.Func.PopShow
{
    public partial class CitySelectionPanel : UserControl
    {
        Dictionary<string, List<string>> cityMap = new Dictionary<string, List<string>>();
        ToolStripDropDown toolStripDropDownCity = null;
        private List<string> selectedCityList = new List<string>();//已选的地市名

        public CitySelectionPanel(ToolStripDropDown toolStripDropDownCity)
        {
            InitializeComponent();
            MasterCom.Util.TreeViewCheckHelper.AutoUpdateCheckState(treeViewProject);
            init();
            this.toolStripDropDownCity = toolStripDropDownCity;
            toolStripDropDownCity.Tag = selectedCityList;
        }

        private void initCityConfig()
        {
            cityMap.Clear();
#if Guangdong
            List<string> cityNames = new List<string>();
            cityNames.Add("广州");
            cityNames.Add("深圳");
            cityNames.Add("东莞");
            cityNames.Add("佛山");
            cityMap.Add("一类地市", cityNames);

            cityNames = new List<string>();
            cityNames.Add("汕头");
            cityNames.Add("珠海");
            cityNames.Add("中山");
            cityNames.Add("惠州");
            cityNames.Add("江门");
            cityNames.Add("湛江");
            cityMap.Add("二类地市", cityNames);

            cityNames = new List<string>();
            cityNames.Add("茂名");
            cityNames.Add("清远");
            cityNames.Add("肇庆");
            cityNames.Add("揭阳");
            cityNames.Add("韶关");
            cityNames.Add("潮州");
            cityNames.Add("阳江");
            cityNames.Add("梅州");
            cityNames.Add("河源");
            cityNames.Add("汕尾");
            cityNames.Add("云浮");
            cityMap.Add("三类地市", cityNames);
#endif
            MainModel mainModel = MainModel.GetInstance();
            if (mainModel.User.DBID == -1)  //如果是省用户的话就默认选择全部地市
            {
                selectedCityList.Clear();
                foreach (List<string> list in cityMap.Values)
                {
                    foreach (string cityName in list)
                    {
                        selectedCityList.Add(cityName);
                    }
                }
            }
            else //如果是地市用户的话就默认选择当前地市
            {
                string cityName = DistrictManager.GetInstance().getDistrictName(mainModel.User.DBID);
                selectedCityList.Add(cityName);
            }
        }

        private void init()
        {
            initCityConfig();
            treeViewProject.Nodes.Clear();
            foreach (string cityLevel in cityMap.Keys)
            {
                TreeNode node = new TreeNode(cityLevel);
                foreach (string cityName in cityMap[cityLevel])
                {
                    TreeNode node2 = new TreeNode(cityName);
                    node2.Checked = true;
                    node.Nodes.Add(node2);
                }
                node.Checked = true;
                treeViewProject.Nodes.Add(node);
            }
            treeViewProject.ExpandAll();
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            selectedCityList.Clear();
            foreach (TreeNode node1 in treeViewProject.Nodes)
            {
                if (node1.Checked)
                {
                    foreach (TreeNode node2 in node1.Nodes)
                    {
                        if (node2.Checked)
                        {
                            selectedCityList.Add(node2.Text);
                        }
                    }
                }
            }
            this.toolStripDropDownCity.Tag = selectedCityList;
            this.toolStripDropDownCity.Close();
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            toolStripDropDownCity.Close();
        }
    }
}
