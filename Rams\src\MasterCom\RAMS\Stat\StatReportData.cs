﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Stat.Data;

namespace MasterCom.RAMS.Stat
{
    public class StatReportData 
    {
        public List<DataUnitAreaKPIQuery> periodList { get; set; }
        public StatReportData(List<DataUnitAreaKPIQuery> paraPeriodList)
        {
            this.periodList = paraPeriodList;
        }
        public StatReportData(DataUnitAreaKPIQuery data)
        {
            this.periodList = new List<DataUnitAreaKPIQuery>();
            this.periodList.Add(data);
        }
        public void AddData(DataUnitAreaKPIQuery data)
        {
            this.periodList.Add(data);
        }
    }
}
