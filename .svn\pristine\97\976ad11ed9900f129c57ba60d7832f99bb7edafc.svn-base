﻿using System;
using System.Collections.Generic;
using System.Text;
using DevExpress.XtraTreeList.Nodes;
using DevExpress.Utils.Paint;
using DevExpress.XtraTreeList;
using System.Drawing;

namespace MasterCom.Util
{
    public class TreeListSearchHelper
    {
        public TreeListSearchHelper(TreeList treeList)
        {
            this.treeList = treeList;
            treeList.CustomDrawNodeCell += treeList_CustomDrawNodeCell;
            treeList.FocusedNodeChanged += new FocusedNodeChangedEventHandler(treeList_FocusedNodeChanged);
        }

        void treeList_FocusedNodeChanged(object sender, FocusedNodeChangedEventArgs e)
        {
            CurrentNode = e.Node;
        }

        readonly XPaint paint = new XPaint();
        void treeList_CustomDrawNodeCell(object sender, CustomDrawNodeCellEventArgs e)
        {
            if (Text == null || Text.Length == 0)
            {
                return;
            }
            int index = e.CellText.IndexOf(Text, 0);
            if (index >= 0)
            {
                e.Handled = true;
                if (e.Node.Focused && e.Column.FieldName == FieldName)
                {
                    paint.DrawMultiColorString(e.Cache, e.<PERSON>, e.CellText, Text, e.Appearance, Color.Red, Color.Yellow, false, index);
                }
                else
                    paint.DrawMultiColorString(e.Cache, e.Bounds, e.CellText, Text, e.Appearance, Color.Blue, e.Appearance.GetBackColor(), false, index);
            }
        }

        private TreeListNode currentNode;
        private readonly TreeList treeList;

        public TreeListNode CurrentNode
        {
            get
            {
                if (currentNode == null)
                    return treeList.FocusedNode;
                else
                    return currentNode;
            }
            set { currentNode = value; }
        }
        
        public string FieldName { get; set; }
        
        public string Text { get; set; }

        public void FindNext()
        {
            PerformSearch(true);
        }

        public void FindPrev()
        {
            PerformSearch(false);
        }

        public void PerformSearch(bool forward)
        {
            CurrentNode = FindNode(forward);
        }

        private TreeListNode FindNode(bool forward)
        {
            if (CurrentNode == null)
                return CurrentNode;
            TreeListNode node = GetNextNode(CurrentNode, forward);
            if (node == null)
                return node;
            while (!MatchCondition(node))
            {
                node = GetNextNode(node, forward);
            }
            return node;
        }
        private TreeListNode GetNextNode(TreeListNode node, bool forward)
        {
            node.ExpandAll();
            return forward ? node.NextVisibleNode : node.PrevVisibleNode;
        }
        private bool MatchCondition(TreeListNode node)
        {
            if (node == null)
                return true;
            if (node.GetDisplayText(FieldName).Contains(Text))
                return true;
            return false;
        }

    }
}
