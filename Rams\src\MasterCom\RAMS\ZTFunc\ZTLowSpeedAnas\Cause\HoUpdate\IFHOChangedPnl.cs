﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause
{
    public partial class IFHOChangedPnl : UserControl
    {
        public IFHOChangedPnl()
        {
            InitializeComponent();
        }

        IFHOChangedCause mainReason = null;

        public void LinkCondition(IFHOChangedCause reason)
        {
            this.mainReason = reason;

            numSecond.Value = (decimal)reason.Second;
            numSecond.ValueChanged += numSecond_ValueChanged;
        }

        void numSecond_ValueChanged(object sender, EventArgs e)
        {
            mainReason.Second = (int)numSecond.Value;
        }
    }
}
