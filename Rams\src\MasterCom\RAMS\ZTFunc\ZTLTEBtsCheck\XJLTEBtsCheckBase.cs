﻿using MasterCom.RAMS.KPI_Statistics;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using System.Linq;

namespace MasterCom.RAMS.ZTFunc
{
    class XJLTEBtsCheckBase : XJLTEBtsCheckOld
    {
        protected int day = 7;
        DateTime bTime;
        DateTime eTime;
        public override void ClearData()
        {
            BaseList.Clear();
        }
        public void SetBeginTime(DateTime b)
        {
            bTime = b;
        }
        public void SetEndTime(DateTime e)
        {
            eTime = e;
        }
        protected override void queryInThread(object o)
        {
            WaitBox.Text = "正在查询基站数据......";
            base.queryInThread(o);
        }
        protected override string getSqlTextString()
        {
            string sql = "exec sp_tb_xinjiang_btsPK_BaseSites_get '"
                + bTime.ToString("yyyyMMdd") + "','" + eTime.ToString("yyyyMMdd") + "'";
            return sql;
        }
        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[7];
            rType[0] = E_VType.E_String;
            rType[1] = E_VType.E_String;
            rType[2] = E_VType.E_String;
            rType[3] = E_VType.E_Int;
            rType[4] = E_VType.E_String;
            rType[5] = E_VType.E_String;
            rType[6] = E_VType.E_String;
            return rType;
        }
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    XJBaseSitesInfo info = new XJBaseSitesInfo();
                    info.DateTime = Convert.ToDateTime(package.Content.GetParamString());
                    info.City = package.Content.GetParamString();
                    info.Company = package.Content.GetParamString();
                    info.ENODEBID = package.Content.GetParamInt();
                    info.Belong = package.Content.GetParamString();
                    info.ChineseName = package.Content.GetParamString();
                    info.NetType = package.Content.GetParamString();
                    info.SN = BaseList.Count + 1;
                    BaseList.Add(info);
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }
        public List<XJBaseSitesInfo> BaseList { get; set; } = new List<XJBaseSitesInfo>();
    }
    public class XJLTEBtsCheckOld : DIYSQLBase
    {
        public XJLTEBtsCheckOld()
            : base(MainModel.GetInstance())
        {
            MainDB = true;
        }
        public override bool IsNeedSetQueryCondition
        {
            get { return false; }
        }
        public virtual void ClearData()
        {
            ENodeBIDOldSiteDic.Clear();
            ENodeBNameOldSiteDic.Clear();
        }
        protected override void query()
        {
            //clearData();
            ClientProxy clientProxy = new ClientProxy();//获取老站点表
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, userName, password, dbid > 0 ? dbid : MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
                WaitBox.Show("正在查询老站点数据......", queryInThread, clientProxy);
            }
            catch (Exception ee)
            {
                MessageBox.Show(ee.Message + Environment.NewLine + ee.Source + Environment.NewLine + ee.StackTrace);
            }
            finally
            {
                clientProxy.Close();
            }
        }
        protected override void queryInThread(object o)
        {
            base.queryInThread(o);
            System.Threading.Thread.Sleep(200);
            WaitBox.Close();
        }
        protected override string getSqlTextString()
        {
            return "SELECT [所属ENODEB],[ENODEBID] from [tb_xinjiang_btsPK_OldSites]";
        }
        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[2];
            rType[0] = E_VType.E_String;
            rType[1] = E_VType.E_Int;
            return rType;
        }
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    XJOldSitesInfo info = new XJOldSitesInfo();
                    info.Belong = package.Content.GetParamString();
                    info.ENODEBID = package.Content.GetParamInt();
                    if (!ENodeBIDOldSiteDic.ContainsKey(info.ENODEBID))
                    {
                        ENodeBIDOldSiteDic.Add(info.ENODEBID, info);
                    }
                    if (!ENodeBNameOldSiteDic.ContainsKey(info.Belong))
                    {
                        ENodeBNameOldSiteDic.Add(info.Belong, info);
                    }
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }

        public Dictionary<int, XJOldSitesInfo> ENodeBIDOldSiteDic { get; set; } = new Dictionary<int, XJOldSitesInfo>();
        public Dictionary<string, XJOldSitesInfo> ENodeBNameOldSiteDic { get; set; } = new Dictionary<string, XJOldSitesInfo>();
    }
    public class XJLTEBtsCheckAsset : XJLTEBtsCheckOld
    {
        public override void ClearData()
        {
            ENodeBIDAssetDic.Clear();
            ENodeBNameAssetDic.Clear();
            ENodeBElementNameAssetDic.Clear();
        }
        protected override void queryInThread(object o)
        {
            WaitBox.Text = "正在查询资管数据......";
            base.queryInThread(o);
        }
        protected override string getSqlTextString()
        {
            return "SELECT [ENODEB名称],[ENODEB编码],[网管中网元名称],[所属管理区域],[ENODEBID],[生产厂家],[生命周期状态] from [tb_xinjiang_btsPK_AssetManage]";
        }
        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[7];
            rType[0] = E_VType.E_String;
            rType[1] = E_VType.E_String;
            rType[2] = E_VType.E_String;
            rType[3] = E_VType.E_String;
            rType[4] = E_VType.E_Int;
            rType[5] = E_VType.E_String;
            rType[6] = E_VType.E_String;
            return rType;
        }
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    XJAssetManageInfo info = new XJAssetManageInfo();
                    info.Name = package.Content.GetParamString();
                    info.Code = package.Content.GetParamString();
                    info.ElementName = package.Content.GetParamString();
                    info.ManageArea = package.Content.GetParamString();
                    info.ENODEBID = package.Content.GetParamInt();
                    info.Company = package.Content.GetParamString();
                    info.Status = package.Content.GetParamString();

                    if (!ENodeBIDAssetDic.TryGetValue(info.ENODEBID, out List<XJAssetManageInfo> infoIDList))
                    {
                        infoIDList = new List<XJAssetManageInfo>();
                        ENodeBIDAssetDic.Add(info.ENODEBID, infoIDList);
                    }
                    infoIDList.Add(info);

                    if (!ENodeBNameAssetDic.TryGetValue(info.Name, out List<XJAssetManageInfo> infoNameList))
                    {
                        infoNameList = new List<XJAssetManageInfo>();
                        ENodeBNameAssetDic.Add(info.Name, infoNameList);
                    }
                    infoNameList.Add(info);

                    if (!ENodeBElementNameAssetDic.TryGetValue(info.ElementName, out List<XJAssetManageInfo> infoElementNameList))
                    {
                        infoElementNameList = new List<XJAssetManageInfo>();
                        ENodeBElementNameAssetDic.Add(info.ElementName, infoElementNameList);
                    }
                    infoElementNameList.Add(info);
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }

        public Dictionary<int, List<XJAssetManageInfo>> ENodeBIDAssetDic { get; set; } = new Dictionary<int, List<XJAssetManageInfo>>();
        public Dictionary<string, List<XJAssetManageInfo>> ENodeBNameAssetDic { get; set; } = new Dictionary<string, List<XJAssetManageInfo>>();
        public Dictionary<string, List<XJAssetManageInfo>> ENodeBElementNameAssetDic { get; set; } = new Dictionary<string, List<XJAssetManageInfo>>();
    }
}
