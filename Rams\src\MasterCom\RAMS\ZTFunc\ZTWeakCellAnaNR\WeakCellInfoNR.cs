﻿using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class WeakCellInfoNR : LteWeakCellInfo
    {
        public NRCell Cell { get; set; }
        public override string CellName { get { return Cell.Name; } }
        public override int TAC { get { return Cell.TAC; } }
        public long NCI { get { return Cell.NCI; } }
        public int ARFCN { get { return Cell.SSBARFCN; } }
        public int PCI { get { return Cell.PCI; } }
        public override double Longitude { get { return Cell.Longitude; } }
        public override double Latitude { get { return Cell.Latitude; } }

        public string AreaName { get; set; }

        protected int lteRsrpCount = 0;
        protected float lteRsrpSum = 0;
        public float? LteRsrpAvg
        {
            get
            {
                return lteRsrpCount > 0 ? (float?)(Math.Round(lteRsrpSum / lteRsrpCount, 2)) : null;
            }
        }

        protected int lteSinrCount = 0;
        protected float lteSinrSum = 0;
        public float? LteSinrAvg
        {
            get
            {
                return lteSinrCount > 0 ? (float?)(Math.Round(lteSinrSum / lteSinrCount, 2)) : null;
            }
        }

        public WeakCellInfoNR(int districtId, NRCell cell)
            : base(districtId)
        {
            Cell = cell;
            AreaName = GISManager.GetInstance().GetAreaPlaceDesc(Cell.Longitude, Cell.Latitude);
        }

        public override void AddTestPoint(TestPoint tp)
        {
            this.TestPoints.Add(tp);
            if (!this.fileNameList.Contains(tp.FileName))
            {
                this.fileNameList.Add(tp.FileName);
            }

            float? rsrp = NRTpHelper.NrTpManager.GetSCellRsrp(tp, true);
            if (rsrp != null)
            {
                rsrpCount++;
                rsrpSum += (float)rsrp;
                RsrpMax = RsrpMax == null ? rsrp : Math.Max((float)rsrp, (float)RsrpMax);
                RsrpMin = RsrpMin == null ? rsrp : Math.Min((float)rsrp, (float)RsrpMin);
            }

            float? sinr = NRTpHelper.NrTpManager.GetSCellSinr(tp, true);
            if (sinr != null)
            {
                sinrCount++;
                sinrSum += (float)sinr;
                SinrMax = SinrMax == null ? sinr : Math.Max((float)sinr, (float)SinrMax);
                SinrMin = SinrMin == null ? sinr : Math.Min((float)sinr, (float)SinrMin);
            }

            float? rssi = NRTpHelper.NrTpManager.GetSCellRssi(tp, true);
            if (rssi != null)
            {
                rssiCount++;
                rssiSum += (float)rssi;
            }

            addNRLteData(tp);
        }

        private void addNRLteData(TestPoint tp)
        {
            float? lteRSRP = NRTpHelper.NrLteTpManager.GetSCellRsrp(tp, true);
            if (lteRSRP != null)
            {
                lteRsrpCount++;
                lteRsrpSum += (float)lteRSRP;
            }

            float? lteSINR = NRTpHelper.NrLteTpManager.GetSCellSinr(tp, true);
            if (lteSINR != null)
            {
                lteSinrCount++;
                lteSinrSum += (float)lteSINR;
            }
        }
    }
}
