﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.NewBlackBlock;
using MasterCom.RAMS.ZTFunc;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using Excel = Microsoft.Office.Interop.Excel;

namespace MasterCom.RAMS.BackgroundFunc
{
    class ExportOutdoorBtsReportHelper_XJ : ExportOutdoorBtsReportBase
    {
        public static string ExportReports(OutDoorBtsAcceptInfo btsInfo, BtsWorkParam_XJ btsWorkParamInfo)
        {
            string targetFile = "";
            if (MainModel.GetInstance().BackgroundStopRequest)
            {
                return targetFile;
            }

            Excel.Application xlApp = null;
            try
            {
                StationAcceptAutoSet_XJ funcSet = MultiStationExportReportAna.GetInstance().FuncSet;
                string folderPath = Path.Combine(funcSet.ReportSavePath, btsWorkParamInfo.DateDes);
                if (!Directory.Exists(folderPath))
                {
                    Directory.CreateDirectory(folderPath);
                }

                //string btsFileKey = string.Format("{0}_{1}_{2}", btsInfo.BtsName, btsWorkParamInfo.ENodeBID, btsWorkParamInfo.WorkOrderNum);
                targetFile = LteStationAcceptManager.GetTargetFile(btsInfo.BtsName, btsInfo.LteBts.Cells.Count, folderPath);
                reportInfo(string.Format("开始导出 {0} 站点的单验报告", btsInfo.BtsName));

                xlApp = new Excel.Application();
                xlApp.Visible = false;
                Excel.Workbook eBook = xlApp.Workbooks.Open(targetFile);

                fillHomePage(eBook, btsWorkParamInfo, btsInfo);
                fillKpiPage(eBook, btsInfo);
                fillCoverPicPage(eBook, btsInfo.LteBts);
                fillBtsPic(eBook, btsInfo.BtsName, funcSet.OutdoorBtsPicFolderPath);

                BtsFusionInfo_XJ btsFusionInfo = btsInfo.FusionInfo as BtsFusionInfo_XJ;
                fillFusionKpiPage(eBook, btsFusionInfo);

                eBook.Save();
                eBook.Close(Type.Missing, Type.Missing, Type.Missing);

                reportInfo(string.Format("成功导出 {0} 站点的单验报告。", btsInfo.BtsName));
                return targetFile;
            }
            catch (Exception ex)
            {
                reportError(ex);
                return "";
            }
            finally
            {
                if (xlApp != null)
                {
                    xlApp.Quit();
                }
                //GC.Collect();
            }
        }

        //填充报告首页-宏站验收记录单
        protected static void fillHomePage(Excel.Workbook eBook, BtsWorkParam_XJ btsWorkParamInfo
            , OutDoorBtsAcceptInfo btsInfo)
        {
            Excel.Worksheet homePageSheet = (Excel.Worksheet)eBook.Sheets[1];
            string districtName = DistrictManager.GetInstance().getDistrictName(MainModel.GetInstance().DistrictID);
            LTEBTS srcLteBts = btsInfo.LteBts;

            #region 基站描述及基站参数
            homePageSheet.get_Range("e3").set_Value(Type.Missing, srcLteBts.Name);
            homePageSheet.get_Range("e5").set_Value(Type.Missing, srcLteBts.BTSID);
            homePageSheet.get_Range("e7").set_Value(Type.Missing, btsWorkParamInfo.Address);
            homePageSheet.get_Range("z3").set_Value(Type.Missing, DateTime.Now.ToString("yyyy-MM-dd"));
            homePageSheet.get_Range("z5").set_Value(Type.Missing, districtName);
            homePageSheet.get_Range("z7").set_Value(Type.Missing, "室外");
            homePageSheet.get_Range("h13").set_Value(Type.Missing, srcLteBts.Longitude);
            homePageSheet.get_Range("h14").set_Value(Type.Missing, srcLteBts.Latitude);
            homePageSheet.get_Range("n13").set_Value(Type.Missing, btsWorkParamInfo.TestLongitude);
            homePageSheet.get_Range("n14").set_Value(Type.Missing, btsWorkParamInfo.TestLatitude);
            #endregion

            int cellIndex = 0;
            foreach (LTECell icell in srcLteBts.Cells)
            {
                #region 小区参数
                int cellParamColIndex = 8 + (cellIndex * 8);//小区参数列：首个小区所在列号为8，其他小区列号以8递增
                homePageSheet.Cells[23, cellParamColIndex] = icell.Altitude;
                homePageSheet.Cells[24, cellParamColIndex] = icell.Direction;
                homePageSheet.Cells[25, cellParamColIndex] = icell.Downward;

                CellWorkParamBase cellParam;
                if (btsWorkParamInfo.CellWorkParamDic.TryGetValue(icell.CellID, out cellParam))
                {
                    CellWorkParam_XJ cellParamXj = cellParam as CellWorkParam_XJ;
                    if (cellParamXj != null)
                    {
                        homePageSheet.Cells[23, cellParamColIndex + 3] = cellParamXj.AntennaInfo.Altitude;
                        homePageSheet.Cells[24, cellParamColIndex + 3] = cellParamXj.AntennaInfo.Direction;
                        homePageSheet.Cells[25, cellParamColIndex + 3] = cellParamXj.AntennaInfo.Downward;
                    }
                }
                #endregion

                #region 小区指标
                OutDoorCellAcceptInfo cellInfo;
                if (btsInfo.CellsAcceptDic.TryGetValue(icell.CellID, out cellInfo))
                {
                    int colIndex = 12 + (cellIndex * 5);
                    homePageSheet.Cells[31, colIndex] = cellInfo.FtpDlInfo.IsAccordDes;
                    homePageSheet.Cells[32, colIndex] = cellInfo.FtpUlInfo.IsAccordDes;
                    homePageSheet.Cells[33, colIndex] = cellInfo.RrcInfo.IsAccordDes;
                    homePageSheet.Cells[34, colIndex] = cellInfo.ErabInfo.IsAccordDes;
                    homePageSheet.Cells[35, colIndex] = cellInfo.AccessInfo.IsAccordDes;
                }
                #endregion
                cellIndex++;
            }
            homePageSheet.get_Range("n38").set_Value(Type.Missing, btsInfo.HandOverInfo.IsAccordDes);
            homePageSheet.get_Range("n39").set_Value(Type.Missing, btsInfo.Is34G_ReselectAccordDes);
            homePageSheet.get_Range("n40").set_Value(Type.Missing, btsInfo.Is24G_ReselectAccordDes);
            homePageSheet.get_Range("n41").set_Value(Type.Missing, btsInfo.IsCsfbAccordDes);

            homePageSheet.get_Range("h56").set_Value(Type.Missing, btsInfo.IsAccordAcceptStr);//验收结论
            homePageSheet.get_Range("a59").set_Value(Type.Missing, btsInfo.NotAccordKpiDes);//未通过验收的原因
        }

        //填充报告第二页-性能验收测试表格
        protected static void fillKpiPage(Excel.Workbook eBook, OutDoorBtsAcceptInfo btsInfo)
        {
            LTEBTS srcLteBts = btsInfo.LteBts;
            Excel.Worksheet kpiPageSheet = (Excel.Worksheet)eBook.Sheets[2];
            kpiPageSheet.get_Range("c2").set_Value(Type.Missing, srcLteBts.Name);
            kpiPageSheet.get_Range("m2").set_Value(Type.Missing, srcLteBts.BTSID);

            int cellIndex = 0;
            int firstColIndex = 16;//尝试次数或FTP相关指标所在列
            int secondColIndex = 23;//成功次数所在列
            foreach (LTECell icell in srcLteBts.Cells)
            {
                OutDoorCellAcceptInfo cellInfo;
                if (btsInfo.CellsAcceptDic.TryGetValue(icell.CellID, out cellInfo))
                {
                    int rowIndex = 5 + (cellIndex * 16);//小区RRC指标所在行：首个小区所在行号为5，其他小区行号以16递增
                    kpiPageSheet.Cells[rowIndex, firstColIndex] = cellInfo.RrcInfo.TotalCount;
                    kpiPageSheet.Cells[rowIndex, secondColIndex] = cellInfo.RrcInfo.ValidCount;
                    kpiPageSheet.Cells[rowIndex + 1, firstColIndex] = cellInfo.ErabInfo.TotalCount;
                    kpiPageSheet.Cells[rowIndex + 1, secondColIndex] = cellInfo.ErabInfo.ValidCount;
                    kpiPageSheet.Cells[rowIndex + 2, firstColIndex] = cellInfo.AccessInfo.TotalCount;
                    kpiPageSheet.Cells[rowIndex + 2, secondColIndex] = cellInfo.AccessInfo.ValidCount;
                    kpiPageSheet.Cells[rowIndex + 3, firstColIndex] = cellInfo.Reselect34Info.TotalCount;
                    kpiPageSheet.Cells[rowIndex + 3, secondColIndex] = cellInfo.Reselect34Info.ValidCount;
                    kpiPageSheet.Cells[rowIndex + 4, firstColIndex] = cellInfo.Reselect24Info.TotalCount;
                    kpiPageSheet.Cells[rowIndex + 4, secondColIndex] = cellInfo.Reselect24Info.ValidCount;
                    kpiPageSheet.Cells[rowIndex + 6, firstColIndex] = cellInfo.CsfbInfo.TotalCount;
                    kpiPageSheet.Cells[rowIndex + 6, secondColIndex] = cellInfo.CsfbInfo.ValidCount;

                    kpiPageSheet.Cells[rowIndex + 8, firstColIndex] = cellInfo.FtpDlInfo.RsrpAvg;
                    kpiPageSheet.Cells[rowIndex + 9, firstColIndex] = cellInfo.FtpDlInfo.SinrAvg;
                    kpiPageSheet.Cells[rowIndex + 10, firstColIndex] = cellInfo.FtpDlInfo.SpeedAvg;
                    kpiPageSheet.Cells[rowIndex + 11, firstColIndex] = cellInfo.FtpUlInfo.RsrpAvg;
                    kpiPageSheet.Cells[rowIndex + 12, firstColIndex] = cellInfo.FtpUlInfo.SinrAvg;
                    kpiPageSheet.Cells[rowIndex + 13, firstColIndex] = cellInfo.FtpUlInfo.SpeedAvg;
                }
                cellIndex++;
            }

            int handOverRowIndex;
            if (srcLteBts.Cells.Count > 3)
            {
                handOverRowIndex = 99;
            }
            else
            {
                handOverRowIndex = 52;
            }
            kpiPageSheet.Cells[handOverRowIndex, firstColIndex] = btsInfo.HandOverInfo.TotalCount;
            kpiPageSheet.Cells[handOverRowIndex, secondColIndex] = btsInfo.HandOverInfo.ValidCount;
        }

        //填充报告第三页-性能验收覆盖效果图
        protected static void fillCoverPicPage(Excel.Workbook eBook, LTEBTS srcLteBts)
        {
            int cellIndex = 0;
            foreach (LTECell lteCell in srcLteBts.Cells)
            {
                int rowIndex = 11 + cellIndex * 92;
                insertCoverPic(eBook, lteCell, "RSRP", "DT下载", ref rowIndex);
                insertCoverPic(eBook, lteCell, "SINR", "DT下载", ref rowIndex);
                insertCoverPic(eBook, lteCell, "PDCP_DL_Mb", "DT下载", ref rowIndex);
                insertCoverPic(eBook, lteCell, "PDCP_UL_Mb", "DT上传", ref rowIndex);

                cellIndex++;
            }

            string folderPath = AcpAutoCoverPicture.Instance.GetBtsPicFolder(srcLteBts.Name);
            if (System.IO.Directory.Exists(folderPath))
            {
                System.IO.Directory.Delete(folderPath, true);
            }
        }
        protected static void insertCoverPic(Excel.Workbook eBook, LTECell lteCell, string param
            , string strDtType, ref int rowIndex)
        {
            string coverPicPath = AcpAutoCoverPicture.Instance.GetCoverPicPath(lteCell.BTSName, lteCell.Name, param);
            if (File.Exists(coverPicPath))
            {
                string picCell = "a" + rowIndex;
                AcpAutoCoverPicture.InsertExcelPicture(eBook, picCell, coverPicPath);
            }

            string tpDes = AcpAutoCoverPicture.Instance.ReadTpdesFromTxt(lteCell, strDtType);
            if (!string.IsNullOrEmpty(tpDes))
            {
                string tpDesCell = "s" + rowIndex;
                Excel.Worksheet eSheet = (Excel.Worksheet)eBook.Sheets[3];
                eSheet.get_Range(tpDesCell).set_Value(Type.Missing, tpDes);
            }

            rowIndex += 23;
        }

        //填充报告第四页-站点验收天面勘测报告
        protected static void fillBtsPic(Excel.Workbook eBook, string btsName, string picFolderPath)
        {
            if (string.IsNullOrEmpty(picFolderPath))
            {
                return;
            }

            string btsPicFolder = Path.Combine(picFolderPath, btsName);
            if (Directory.Exists(btsPicFolder))
            {
                Excel.Worksheet btsPicPageSheet = (Excel.Worksheet)eBook.Sheets[4];
                insertBtsPicture(btsPicPageSheet, btsPicFolder, 1, 26, btsName + "-建筑物全景图.jpg");
                insertBtsPicture(btsPicPageSheet, btsPicFolder, 3, 26, btsName + "-站点入口图.jpg");
                insertBtsPicture(btsPicPageSheet, btsPicFolder, 1, 28, btsName + "-屋顶天面全景图.jpg");
                insertBtsPicture(btsPicPageSheet, btsPicFolder, 1, 36, btsName + "-0度.jpg");
                insertBtsPicture(btsPicPageSheet, btsPicFolder, 3, 36, btsName + "-45度.jpg");
                insertBtsPicture(btsPicPageSheet, btsPicFolder, 1, 38, btsName + "-90度.jpg");
                insertBtsPicture(btsPicPageSheet, btsPicFolder, 3, 38, btsName + "-135度.jpg");
                insertBtsPicture(btsPicPageSheet, btsPicFolder, 1, 40, btsName + "-180度.jpg");
                insertBtsPicture(btsPicPageSheet, btsPicFolder, 3, 40, btsName + "-225度.jpg");
                insertBtsPicture(btsPicPageSheet, btsPicFolder, 1, 42, btsName + "-270度.jpg");
                insertBtsPicture(btsPicPageSheet, btsPicFolder, 3, 42, btsName + "-315度.jpg");
            }
        }

        //填充报告第五页-站点后台指标监控
        protected static void fillFusionKpiPage(Excel.Workbook eBook, BtsFusionInfo_XJ btsFusionInfo)
        {
            if (btsFusionInfo == null)
            {
                return;
            }
            Excel.Worksheet bgKpiPageSheet = (Excel.Worksheet)eBook.Sheets[5];

            int dateIndex = 0;
            int increaseRowCount = btsFusionInfo.BtsWorkParamInfo.CellWorkParams.Count > 3 ? 6 : 3;
            DateTime curDate = btsFusionInfo.BeginTime.Date;
            while (curDate <= btsFusionInfo.EndTime.Date)
            {
                string dateDes = BtsFusionInfo_XJ.GetDateKeyDes(curDate);
                int cellIndex = 0;
                foreach (CellWorkParamBase cellWorkParam in btsFusionInfo.BtsWorkParamInfo.CellWorkParams)
                {
                    int perfRowIndex = 4 + (increaseRowCount * dateIndex) + cellIndex;
                    bgKpiPageSheet.Cells[perfRowIndex, 1] = dateDes;
                    bgKpiPageSheet.Cells[perfRowIndex, 2] = "";
                    bgKpiPageSheet.Cells[perfRowIndex, 3] = btsFusionInfo.BtsWorkParamInfo.BtsName;
                    bgKpiPageSheet.Cells[perfRowIndex, 4] = cellWorkParam.CellName;
                    bgKpiPageSheet.Cells[perfRowIndex, 5] = cellWorkParam.ENodeBID;
                    bgKpiPageSheet.Cells[perfRowIndex, 6] = cellWorkParam.SectorID;
                    bgKpiPageSheet.Cells[perfRowIndex, 7] = cellWorkParam.CellID;

                    #region 性能数据
                    setPerfData(btsFusionInfo, bgKpiPageSheet, dateDes, cellWorkParam, perfRowIndex);
                    #endregion

                    #region MR数据
                    setMRData(btsFusionInfo, bgKpiPageSheet, dateDes, cellWorkParam, perfRowIndex);
                    #endregion

                    cellIndex++;
                }
                dateIndex++;
                curDate = curDate.AddDays(1);
            }

            #region 告警数据
            setAlarmData(btsFusionInfo, bgKpiPageSheet);
            #endregion
        }

        private static void setPerfData(BtsFusionInfo_XJ btsFusionInfo, Excel.Worksheet bgKpiPageSheet, string dateDes, CellWorkParamBase cellWorkParam, int perfRowIndex)
        {
            Dictionary<string, CellPerfDataBase> date_cellPerfDataDic;
            if (btsFusionInfo.CellPerfInfoDic.TryGetValue(cellWorkParam.CGI, out date_cellPerfDataDic))
            {
                CellPerfDataBase cellPerfInfo;
                if (date_cellPerfDataDic.TryGetValue(dateDes, out cellPerfInfo))
                {
                    int perfColIndex = 8;//列序号
                    setFloatValue(bgKpiPageSheet, perfRowIndex, perfColIndex++, cellPerfInfo.RrcConnectTryCount);
                    setFloatValue(bgKpiPageSheet, perfRowIndex, perfColIndex++, cellPerfInfo.RrcSetupSuccessRate);
                    setFloatValue(bgKpiPageSheet, perfRowIndex, perfColIndex++, cellPerfInfo.ErabConnectTryCount);
                    setFloatValue(bgKpiPageSheet, perfRowIndex, perfColIndex++, cellPerfInfo.ErabSetupSuccessRate);
                    setFloatValue(bgKpiPageSheet, perfRowIndex, perfColIndex++, cellPerfInfo.WirelessConnectRate);
                    setFloatValue(bgKpiPageSheet, perfRowIndex, perfColIndex++, cellPerfInfo.WirelessDropRate);
                    setFloatValue(bgKpiPageSheet, perfRowIndex, perfColIndex++, cellPerfInfo.ErabDropRate);
                    setFloatValue(bgKpiPageSheet, perfRowIndex, perfColIndex++, cellPerfInfo.InnerHandoverSuccessRate);
                    setFloatValue(bgKpiPageSheet, perfRowIndex, perfColIndex++, cellPerfInfo.PdcpThroughput_UL);
                    setFloatValue(bgKpiPageSheet, perfRowIndex, perfColIndex, cellPerfInfo.PdcpThroughput_DL);
                }
            }
        }

        private static void setMRData(BtsFusionInfo_XJ btsFusionInfo, Excel.Worksheet bgKpiPageSheet, string dateDes, CellWorkParamBase cellWorkParam, int perfRowIndex)
        {
            Dictionary<string, CellMRDataBase> date_CellMRDataDic;
            if (btsFusionInfo.CellMRInfoDic.TryGetValue(cellWorkParam.CGI, out date_CellMRDataDic))
            {
                CellMRDataBase cellMrInfo;
                if (date_CellMRDataDic.TryGetValue(dateDes, out cellMrInfo))
                {
                    setFloatValue(bgKpiPageSheet, perfRowIndex, 18, cellMrInfo.MrCoverRate);
                }
            }
        }

        private static void setAlarmData(BtsFusionInfo_XJ btsFusionInfo, Excel.Worksheet bgKpiPageSheet)
        {
            int alarmRowIndex = 28;
            if (btsFusionInfo.BtsWorkParamInfo.CellWorkParams.Count > 3)
            {
                alarmRowIndex = 49;
            }
            if (btsFusionInfo.BtsAlarmInfoDic != null && btsFusionInfo.BtsAlarmInfoDic.Count > 0)
            {
                bgKpiPageSheet.Cells[alarmRowIndex, 2] = "有";
                alarmRowIndex += 3;
                foreach (BtsAlarmDataBase data in btsFusionInfo.BtsAlarmInfoDic.Values)
                {
                    BtsAlarmData_XJ alarmData = data as BtsAlarmData_XJ;
                    bgKpiPageSheet.Cells[alarmRowIndex, 1] = alarmData.AlarmTitle;
                    bgKpiPageSheet.Cells[alarmRowIndex, 2] = alarmData.BeginTimeStr;
                    bgKpiPageSheet.Cells[alarmRowIndex, 3] = alarmData.EndTimeStr;
                    bgKpiPageSheet.Cells[alarmRowIndex, 4] = alarmData.Desc;
                    alarmRowIndex++;
                }
            }
            else
            {
                bgKpiPageSheet.Cells[alarmRowIndex, 2] = "无";
            }
        }
    }

    abstract class ExportOutdoorBtsReportBase
    {
        protected ExportOutdoorBtsReportBase()
        {

        }

        protected static void insertBtsPicture(Excel.Worksheet eSheet, string picFolderPath
            , int colIndex, int rowIndex, string picFileName)
        {
            string picPath = Path.Combine(picFolderPath, picFileName);
            if (File.Exists(picPath))
            {
                Excel.Range rng = eSheet.get_Range(eSheet.Cells[rowIndex, colIndex]
                    , eSheet.Cells[rowIndex, colIndex + 1]);

                eSheet.Shapes.AddPicture(picPath, Microsoft.Office.Core.MsoTriState.msoFalse
                    , Microsoft.Office.Core.MsoTriState.msoCTrue, (float)(double)rng.Left
                    , (float)(double)rng.Top, (float)(double)rng.Width, (float)(double)rng.Height);
            }
        }
        protected static void setFloatValue(Excel.Worksheet workSheet, int rowIndex, int colIndex, float value)
        {
            if (float.IsNaN(value) || value == float.MinValue)
            {
                workSheet.Cells[rowIndex, colIndex] = "";
            }
            else
            {
                workSheet.Cells[rowIndex, colIndex] = value;
            }
        }

        protected static void reportInfo(string str)
        {
            BackgroundFuncManager.GetInstance().ReportBackgroundInfo(str);
        }
        protected static void reportError(Exception ex)
        {
            BackgroundFuncManager.GetInstance().ReportBackgroundError(ex);
        }
    }
}
