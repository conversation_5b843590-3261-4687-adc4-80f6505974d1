﻿using System;
using System.Collections.Generic;
using System.Text;
using System.IO;

using NPOI.XSSF.UserModel;
using NPOI.SS.UserModel;

namespace MasterCom.RAMS.ZTFunc
{
    public class ExcelReader
    {
        public ExcelInput Input
        {
            get;
            private set;
        }

        public ExcelReader(ExcelInput input)
        {
            Input = input;
        }

        public ExcelOutput Read()
        {
            try
            {
                InitBook();
                InitSheet();
            }
            catch (Exception ex)
            {
                return new ExcelOutput(ex);
            }

            return ReadRows();
        }

        private object GetCellValue(ICell cell)
        {
            switch (cell.CellType)
            {
                case CellType.Blank:
                    return "";
                case CellType.Boolean:
                    return cell.BooleanCellValue;
                case CellType.Numeric:
                    return cell.NumericCellValue;
                case CellType.String:
                    return cell.StringCellValue;
                default:
                    return null;
            }
        }

        private void InitBook()
        {
            using (FileStream fs = File.OpenRead(Input.FileName))
            {
                book = WorkbookFactory.Create(fs);
                if (book == null)
                {
                    throw (new Exception("Create 'IWorkbook' Error"));
                }
            }
        }

        private void InitSheet()
        {
            if (Input.ColumnNames == null || Input.ColumnNames.Count < 1)
            {
                throw (new Exception("ColumnNames not Defined"));
            }

            List<ISheet> sheetList = getSheetList();

            if (sheetList.Count == 0)
            {
                throw (new Exception("Sheet not Found in File"));
            }
            for (int i = 0; i < sheetList.Count; ++i)
            {
                sheet = sheetList[i];
                try
                {
                    InitIndex();
                    break; // no exception here
                }
                catch
                {
                    if (i >= sheetList.Count - 1)
                    {
                        throw;
                    }
                }
            }
        }

        private List<ISheet> getSheetList()
        {
            List<ISheet> sheetList = new List<ISheet>();
            if (Input.SheetName != null)
            {
                ISheet curSheet = book.GetSheet(Input.SheetName);
                if (curSheet != null)
                {
                    sheetList.Add(curSheet);
                }
            }
            else
            {
                for (int i = 0; i < book.NumberOfSheets; ++i)
                {
                    ISheet curSheet = book.GetSheetAt(i);
                    if (curSheet == null)
                    {
                        continue;
                    }
                    sheetList.Add(curSheet);
                }
            }

            return sheetList;
        }

        private void InitIndex()
        {
            IRow firstRow = sheet.GetRow(sheet.FirstRowNum);
            if (firstRow == null)
            {
                throw (new Exception("Get First Physical Row Error"));
            }

            index = new int[Input.ColumnNames.Count];
            for (int i = 0; i < index.Length; ++i)
            {
                index[i] = -1;
            }

            for (int i = 0; i < Input.ColumnNames.Count; ++i)
            {
                string colName = Input.ColumnNames[i];
                if (colName == null)
                {
                    throw (new Exception(string.Format("ColumnNames[{0}] not Defined", i)));
                }
                setIndex(firstRow, i, colName);
            }

            for (int i = 0; i < index.Length; ++i)
            {
                if (index[i] == -1)
                {
                    throw (new Exception(string.Format("ColumnName '{0}' not Found", Input.ColumnNames[i])));
                }
            }
        }

        private void setIndex(IRow firstRow, int i, string colName)
        {
            for (int j = firstRow.FirstCellNum; j <= firstRow.LastCellNum; ++j)
            {
                ICell cell = firstRow.GetCell(j);
                if (cell == null)
                {
                    continue;
                }

                object obj = GetCellValue(cell);
                if (obj == null)
                {
                    continue;
                }

                string value = obj.ToString();
                if (value != colName)
                {
                    continue;
                }

                index[i] = j;
            }
        }

        private ExcelOutput ReadRows()
        {
            List<string> warns = new List<string>();
            List<object[]> result = new List<object[]>();
            List<int> columnsIndex = new List<int>();
            List<int> rowsIndex = new List<int>();

            foreach (int v in index)
            {
                columnsIndex.Add(v + 1);
            }

            for (int i = sheet.FirstRowNum + 1; i <= sheet.LastRowNum; ++i)
            {
                IRow row = sheet.GetRow(i);
                if (row == null)
                {
                    warns.Add(string.Format("Row {0}: GetRow Error", i + 1));
                    continue;
                }
                if (row.PhysicalNumberOfCells == 0)
                {
                    warns.Add(string.Format("Row {0}: Empty Row", i + 1));
                    continue;
                }

                object[] values = new object[index.Length];
                for (int j = 0; j < index.Length; ++j)
                {
                    object obj = null;
                    ICell cell = row.GetCell(index[j]);
                    if (cell == null || (obj = GetCellValue(cell)) == null)
                    {
                        //warns.Add(string.Format("Row {0}, Column {1}: Unknow Value", i + 1, index[j] + 1));
                    }
                    values[j] = obj;
                }
                result.Add(values);
                rowsIndex.Add(i + 1);
            }

            return new ExcelOutput(rowsIndex, columnsIndex, warns, result);
        }

        private IWorkbook book;
        private ISheet sheet;
        private int[] index;  // 返回行的第i个值应该从IRow的第index[i]的位置读取
    }

    public class ExcelInput
    {
        public string FileName
        {
            get;
            set;
        }
        public string SheetName
        {
            get;
            set;
        }
        public List<string> ColumnNames
        {
            get;
            set;
        }
        public ExcelInput(string fileName, string sheetName, List<string> columnsName)
        {
            FileName = fileName;
            SheetName = sheetName;
            ColumnNames = columnsName;
        }
        public ExcelInput(string fileName, List<string> columnsName)
        {
            FileName = fileName;
            ColumnNames = columnsName;
            SheetName = null;   // 使用第一个匹配ColumnNames的Sheet
        }
    }

    public class ExcelOutput
    {
        public Exception Error
        {
            get;
            set;
        }
        public List<string> Warns
        {
            get;
            set;
        }
        public List<object[]> Result
        {
            get;
            set;
        }

        public List<int> RowsIndex
        {
            get;
            set;
        }

        public List<int> ColumnsIndex
        {
            get;
            set;
        }

        public ExcelOutput()
        {
        }

        public ExcelOutput(Exception ex)
        {
            Error = ex;
        }

        public ExcelOutput(List<int> rowsIndex, List<int> columnsIndex, List<string> warns, List<object[]> result)
        {
            RowsIndex = rowsIndex;
            ColumnsIndex = columnsIndex;
            Warns = warns;
            Result = result;
        }
    }
}