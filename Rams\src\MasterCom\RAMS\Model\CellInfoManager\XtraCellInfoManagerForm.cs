﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using System.Collections;
using System.Text.RegularExpressions;
using System.IO;
using DevExpress.Utils;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraGrid.Views.Grid.ViewInfo;
using DevExpress.XtraGrid.Views.Base;
using DevExpress.XtraGrid;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using DevExpress.XtraGrid.Columns;

namespace MasterCom.RAMS.Model
{
    public partial class XtraCellInfoManagerForm : DevExpress.XtraEditors.XtraForm
    {
        private DataTable datatable = new DataTable();
        private Dictionary<string, string> lac_ci = new Dictionary<string, string>();
        private Dictionary<string, string> fieldNameList = new Dictionary<string, string>();

        private List<string> noList = new List<string>();

        private MainModel mainmodel;

        private bool backup = false;  //是否备份excel

        private string sheetname = "";  //sheet名称

        private List<ColorCell> colorcellList = new List<ColorCell>();  //有问题需要着色的单元格

        public XtraCellInfoManagerForm(MainModel mymainmodel)
        {
            InitializeComponent();

            mainmodel = mymainmodel;

            datatable.Clear();
            lac_ci.Clear();
            fieldNameList.Clear();
            noList.Clear();
            backup = false;
            colorcellList.Clear();
        }


        private void btnFileOpen_Click(object sender, EventArgs e)
        {
            string resultfile = "";
            OpenFileDialog openfiledialog1 = new OpenFileDialog();
            openfiledialog1.InitialDirectory = "";
            openfiledialog1.Filter = "Excel files |*.xls;*.xlsx";
            openfiledialog1.FilterIndex = 2;
            openfiledialog1.RestoreDirectory = true;
            if (openfiledialog1.ShowDialog() == DialogResult.OK)
            {
                resultfile = openfiledialog1.FileName;
            }
            else
            {
                return;
            }
            textBox1.Text = resultfile;
            ArrayList sheets = ExcelTools.GetSheetNameList(textBox1.Text.Trim());
            List<DataTable> datatableList = new List<DataTable>();
            int GSMORTD = -1;

            foreach (string item in sheets)
            {
                if (item.IndexOf("GSM_") != -1)
                {
                    datatableList.Add(ExcelTools.ExcelToDataTable(textBox1.Text, item));
                    listBox1.Items.Add("GSM工参加载成功");
                    GSMORTD = 1;
                    sheetname = item;
                    break;
                }

                if (item.IndexOf("TD_") != -1)
                {
                    datatableList.Add(ExcelTools.ExcelToDataTable(textBox1.Text, item));
                    listBox1.Items.Add("TD工参加载成功");
                    GSMORTD = 2;
                    sheetname = item;
                    break;
                }
            }

            if (datatableList.Count == 0)
                XtraMessageBox.Show("似乎没有检查到此EXCEL中有符合规范的sheet,请检查~\r\n正确的命名规范应该为 : GSM_20130812 或 TD_20130813");

            gridControl1.DataSource = AddSeriNumToDataTable(datatableList[0]);
            datatable = AddSeriNumToDataTable(datatableList[0]);

            double allGSMCell = mainmodel.CellManager.GetCurrentCells().Count;  //全网GSM小区
            double allTDCell = mainmodel.CellManager.GetCurrentTDCells().Count;   //全网TD小区
            double excelCell = datatableList[0].Rows.Count;                  //excel小区

            double gsmpro = (allGSMCell - excelCell) / allGSMCell * 100;
            double tdpro = (allTDCell - excelCell) / allTDCell * 100;

            setListBox(GSMORTD, allGSMCell, excelCell, gsmpro, tdpro);

            checkData();

            gridControl1.RefreshDataSource();
            gridView1.RefreshData();
            this.Refresh();

            gridView1.Columns[0].Width = gridView1.Columns[0].Width + 1;
            gridView1.Columns[0].Width = gridView1.Columns[0].Width - 1;

            if (fieldNameList.Count == 0)
            {
                listBox1.Items.Add("数据格式正确,可以提交");
                label2.Text = "正确";
            }
            else
            {
                listBox1.Items.Add("发现了一些错误的数据格式,请根据提示修改正确后在进行提交");
                label2.Text = "错误";
                backup = true;
            }
        }

        private void setListBox(int GSMORTD, double allGSMCell, double excelCell, double gsmpro, double tdpro)
        {
            if (GSMORTD == 1)
            {
                if (gsmpro >= 10)
                {
                    listBox1.Items.Add("全网GSM小区数量 :" + allGSMCell + " ,本Excel小区数量 :" + excelCell + " ,退服小区占比超过10%,禁止提交,请检查数据有效性!");
                }
                else
                {
                    listBox1.Items.Add("全网GSM小区数量 :" + allGSMCell + " ,本Excel小区数量 :" + excelCell + " ,小区数量正常");
                }
            }
            else if (GSMORTD == 2)
            {
                if (tdpro >= 10)
                {
                    listBox1.Items.Add("全网TD小区数量 :" + allGSMCell + " ,本Excel小区数量 :" + excelCell + " ,退服小区占比超过10%,禁止提交,请检查数据有效性!");
                }
                else
                {
                    listBox1.Items.Add("全网TD小区数量 :" + allGSMCell + " ,本Excel小区数量 :" + excelCell + " ,小区数量正常");
                }
            }
        }

        #region 加载文件
        /**
        //private void btnLoadFile_Click(object sender, EventArgs e)
        //{
        //    try
        //    {
        //        ArrayList sheets = new ArrayList();
        //        sheets = ExcelTools.GetSheetNameList(textBox1.Text.Trim());

        //        List<DataTable> datatableList = new List<DataTable>();
        //        foreach (string item in sheets)
        //        {
        //            if (item.IndexOf("GSM_") != -1)
        //            {
        //                datatableList.Add(ExcelTools.ExcelToDataTable(textBox1.Text, item));
        //                listBox1.Items.Add("GSM工参加载成功");
        //            }

        //            if (item.IndexOf("TD_") != -1)
        //            {
        //                datatableList.Add(ExcelTools.ExcelToDataTable(textBox1.Text, item));
        //                listBox1.Items.Add("TD工参加载成功");
        //            }
        //        }

        //        if (datatableList.Count == 0)
        //            XtraMessageBox.Show("似乎没有检查到此EXCEL中有符合规范的sheet,请检查~\r\n正确的命名规范应该为 : GSM_20130812 或 TD_20130813");


        //        gridControl1.DataSource = AddSeriNumToDataTable(datatableList[0]);
        //        datatable = AddSeriNumToDataTable(datatableList[0]);
        //        gridControl1.RefreshDataSource();

        //        gridView1.RefreshData();
        //        this.Refresh();

        //        gridView1.Columns[0].Width = gridView1.Columns[0].Width + 1;
        //        gridView1.Columns[0].Width = gridView1.Columns[0].Width - 1;




        //        //gridControl1.RefreshDataSource();

        //        //timer1.Enabled = true;
        //    }
        //    catch (Exception ee)
        //    {

        //        throw;
        //    }



        //    if (fieldNameList.Count == 0)
        //    {
        //        listBox1.Items.Add("数据格式正确,可以提交");
        //    }
        //    else
        //    {
        //        listBox1.Items.Add("发现了一些错误的数据格式,请根据提示修改正确后在进行提交");
        //    }

        //}
        */
        #endregion

        private void button1_Click(object sender, EventArgs e)
        {
            for (int i = 0; i < gridView1.Columns.Count; i++)
            {
                gridView1.Columns[i].ImageIndex = -1;
            }
            gridControl1.RefreshDataSource();
            gridControl1.Refresh();
            gridView1.RefreshData();
            checkData();
        }

        private void gridView1_CellValueChanged(object sender, DevExpress.XtraGrid.Views.Base.CellValueChangedEventArgs e)
        {
            for (int i = 0; i < gridView1.Columns.Count; i++)
            {
                gridView1.Columns[i].ImageIndex = -1;
            }

            noList.Clear();
            lac_ci.Clear();
            fieldNameList.Clear();
            colorcellList.Clear();

            gridControl1.RefreshDataSource();
            gridControl1.Refresh();
            gridView1.RefreshData();
            checkData();
            label2.Text = "错误";
            if (fieldNameList.Count == 0)
            {
                listBox1.Items.Add("数据格式正确,可以提交");
                label2.Text = "正确";
            }
            
        }

        private void toolTipController1_GetActiveObjectInfo(object sender, DevExpress.Utils.ToolTipControllerGetActiveObjectInfoEventArgs e)
        {
            listBox1.Text = "";

            if (e.SelectedControl != gridControl1) return;
            ToolTipControlInfo info = null;
            try
            {
                GridView view = gridControl1.GetViewAt(e.ControlMousePosition) as GridView;
                if (view == null) return;
                GridHitInfo hi = view.CalcHitInfo(e.ControlMousePosition);
                if (hi.Column != null)
                {
                    if (fieldNameList.ContainsKey(hi.Column.FieldName))
                    {
                        info = gettooltipinfo(hi.Column, fieldNameList[hi.Column.FieldName]);
                    }
                    return;
                }
            }
            finally
            {
                e.Info = info;
            }
        }

        private ToolTipControlInfo gettooltipinfo(DevExpress.XtraGrid.Columns.GridColumn col, string disStr)
        {
            ToolTipControlInfo info = new ToolTipControlInfo(col, disStr);
            info.Title = " 错误提示 ";   //标题
            info.ToolTipType = ToolTipType.SuperTip;  //超级样式
            info.IconType = ToolTipIconType.Error;  // 设置图标 

            return info;
        }

        private void timer1_Tick(object sender, EventArgs e)
        {
            this.Refresh();
        }

        private void checkBox1_CheckedChanged(object sender, EventArgs e)
        {
            if (checkBox1.Checked)  //只显示出错的数据
            {
                StringBuilder strtemp = new StringBuilder();
                foreach (string item in noList)
                {
                    strtemp.Append(item + " ,");
                }
                string str = strtemp.ToString().TrimEnd(',');

                gridView1.ActiveFilterString = "[序号] in (" + str + ")";
                gridView1.OptionsView.ShowFilterPanelMode = ShowFilterPanelMode.Never;
            }
            else
            {
                gridView1.ActiveFilterString = "";
            }

            colorcellList.Clear();
            checkData();
        }
        
        private void gridView1_EndSorting(object sender, EventArgs e)
        {
            colorcellList.Clear();
            checkData();
        }

        /// <summary>
        /// 在DataTable中添加一序号列，编号从1依次递增
        /// </summary>
        /// <param >DataTable</param>
        /// <returns></returns>
        private DataTable AddSeriNumToDataTable(DataTable dt)
        {
            //需要返回的值
            DataTable dtNew;
            if (dt.Columns.IndexOf("序号") >= 0)
            {
                dtNew = dt;
            }
            else //添加一序号列,并且在第一列
            {
                int rowLength = dt.Rows.Count;
                int colLength = dt.Columns.Count;
                DataRow[] newRows = new DataRow[rowLength];

                dtNew = new DataTable();
                //在第一列添加“序号”列
                dtNew.Columns.Add("序号");
                for (int i = 0; i < colLength; i++)
                {
                    dtNew.Columns.Add(dt.Columns[i].ColumnName);
                    //复制dt中的数据
                    for (int j = 0; j < rowLength; j++)
                    {
                        if (newRows[j] == null)
                            newRows[j] = dtNew.NewRow();
                        //将其他数据填充到第二列之后，因为第一列为新增的序号列
                        newRows[j][i + 1] = dt.Rows[j][i];
                    }
                }
                foreach (DataRow row in newRows)
                {
                    dtNew.Rows.Add(row);
                }

            }
            //对序号列填充，从1递增
            for (int i = 0; i < dt.Rows.Count; i++)
            {
                dtNew.Rows[i]["序号"] = i + 1;
            }

            return dtNew;
        }

        private void checkData()
        {
            for (int i = 0; i < gridView1.RowCount; i++)
            {
                foreach (GridColumn item in gridView1.Columns)
                {
                    if (item.FieldName == "经度"
                        && !Regex.IsMatch(gridView1.GetRowCellDisplayText(i, gridView1.Columns["经度"]), @"\d+\.\d+"))//浮点数，四位小数
                    {
                        //找到不符合数据
                        item.ImageIndex = 0;
                        gridView1.Columns[item.FieldName].ImageIndex = 0;
                        ColorCell cell = new ColorCell();
                        cell.Rowindex = i;
                        cell.Gridcolumn = item;
                        cell.Color = Color.FromArgb(255, 199, 199);

                        colorcellList.Add(cell);

                        if (!noList.Contains(gridView1.GetRowCellDisplayText(i, gridView1.Columns["序号"])))
                            noList.Add(gridView1.GetRowCellDisplayText(i, gridView1.Columns["序号"]));

                        if (!fieldNameList.ContainsKey(item.FieldName))
                            fieldNameList.Add(item.FieldName, "必须为浮点数,至少1位小数");
                    }

                    if (item.FieldName == "纬度"
                        && !Regex.IsMatch(gridView1.GetRowCellDisplayText(i, gridView1.Columns["纬度"]), @"\d+\.\d+"))//浮点数，四位小数
                    {
                        //找到不符合数据
                        item.ImageIndex = 0;

                        //e.Column.ToolTip = "必须为浮点数,至少4位小数";
                        ColorCell cell = new ColorCell();
                        cell.Rowindex = i;
                        cell.Gridcolumn = item;
                        cell.Color = Color.FromArgb(255, 199, 199);

                        colorcellList.Add(cell);
                        //gridView1.rowcell
                        if (!noList.Contains(gridView1.GetRowCellDisplayText(i, gridView1.Columns["序号"])))
                            noList.Add(gridView1.GetRowCellDisplayText(i, gridView1.Columns["序号"]));

                        if (!fieldNameList.ContainsKey(item.FieldName))
                            fieldNameList.Add(item.FieldName, "必须为浮点数,至少1位小数");
                    }

                    if (item.FieldName == "基站类型"
                        && !Regex.IsMatch(gridView1.GetRowCellDisplayText(i, gridView1.Columns["基站类型"]), @"[室内,室外]"))//室内\室外
                    {
                        //找到不符合数据
                        //dc.ToolTip = "必须为浮点数,至少4位小数";
                        item.ImageIndex = 0;
                        ColorCell cell = new ColorCell();
                        cell.Rowindex = i;
                        cell.Gridcolumn = item;
                        cell.Color = Color.FromArgb(255, 199, 199);

                        colorcellList.Add(cell);
                        //gridView1.rowcell
                        if (!noList.Contains(gridView1.GetRowCellDisplayText(i, gridView1.Columns["序号"])))
                            noList.Add(gridView1.GetRowCellDisplayText(i, gridView1.Columns["序号"]));

                        if (!fieldNameList.ContainsKey(item.FieldName))
                            fieldNameList.Add(item.FieldName, "必须为 室内或室外");
                    }

                    if (item.FieldName == "频带类型"
                        && !Regex.IsMatch(gridView1.GetRowCellDisplayText(i, gridView1.Columns["频带类型"]), @"900|1800"))//900\1800
                    {
                        //找到不符合数据
                        item.ImageIndex = 0;
                        //dc.ToolTip = "必须为浮点数,至少4位小数";
                        ColorCell cell = new ColorCell();
                        cell.Rowindex = i;
                        cell.Gridcolumn = item;
                        cell.Color = Color.FromArgb(255, 199, 199);

                        colorcellList.Add(cell);
                        //gridView1.rowcell
                        if (!noList.Contains(gridView1.GetRowCellDisplayText(i, gridView1.Columns["序号"])))
                            noList.Add(gridView1.GetRowCellDisplayText(i, gridView1.Columns["序号"]));

                        if (!fieldNameList.ContainsKey(item.FieldName))
                            fieldNameList.Add(item.FieldName, "必须为 900或1800");
                    }
                    
                    if (item.FieldName == "LAC"
                        && !Regex.IsMatch(gridView1.GetRowCellDisplayText(i, gridView1.Columns["LAC"]), @"\d+"))//整型
                    {
                        //找到不符合数据
                        item.ImageIndex = 0;
                        //dc.ToolTip = "必须为浮点数,至少4位小数";
                        ColorCell cell = new ColorCell();
                        cell.Rowindex = i;
                        cell.Gridcolumn = item;
                        cell.Color = Color.FromArgb(255, 199, 199);

                        colorcellList.Add(cell);
                        //gridView1.rowcell
                        if (!noList.Contains(gridView1.GetRowCellDisplayText(i, gridView1.Columns["序号"])))
                            noList.Add(gridView1.GetRowCellDisplayText(i, gridView1.Columns["序号"]));

                        if (!fieldNameList.ContainsKey(item.FieldName))
                            fieldNameList.Add(item.FieldName, "必须为整型");
                    }

                    if (item.FieldName == "CI")
                    {
                        if (!Regex.IsMatch(gridView1.GetRowCellDisplayText(i, gridView1.Columns["CI"]), @"\d+"))//整型
                        {
                            //找到不符合数据
                            item.ImageIndex = 0;
                            //dc.ToolTip = "必须为浮点数,至少4位小数";
                            ColorCell cell = new ColorCell();
                            cell.Rowindex = i;
                            cell.Gridcolumn = item;
                            cell.Color = Color.FromArgb(255, 199, 199);

                            colorcellList.Add(cell);
                            //gridView1.rowcell

                            if (!noList.Contains(gridView1.GetRowCellDisplayText(i, gridView1.Columns["序号"])))
                                noList.Add(gridView1.GetRowCellDisplayText(i, gridView1.Columns["序号"]));

                            if (!fieldNameList.ContainsKey(item.FieldName))
                                fieldNameList.Add(item.FieldName, "必须为整型,并且LAC组合CI需唯一");
                        }

                        //LAC_CI必须唯一
                        if (lac_ci.ContainsKey(gridView1.GetRowCellDisplayText(i, gridView1.Columns["LAC"]) + "_" + gridView1.GetRowCellDisplayText(i, gridView1.Columns["CI"]))
                            && lac_ci[gridView1.GetRowCellDisplayText(i, gridView1.Columns["LAC"]) + "_" + gridView1.GetRowCellDisplayText(i, gridView1.Columns["CI"])] != gridView1.GetRowCellDisplayText(i, gridView1.Columns["小区名称"]))
                        {
                            //找到不符合数据
                            item.ImageIndex = 0;
                            //dc.ToolTip = "必须为浮点数,至少4位小数";
                            ColorCell cell = new ColorCell();
                            cell.Rowindex = i;
                            cell.Gridcolumn = item;
                            cell.Color = Color.FromArgb(255, 199, 199);

                            colorcellList.Add(cell);
                            //gridView1.rowcell
                            if (!noList.Contains(gridView1.GetRowCellDisplayText(i, gridView1.Columns["序号"])))
                                noList.Add(gridView1.GetRowCellDisplayText(i, gridView1.Columns["序号"]));

                            if (!fieldNameList.ContainsKey(item.FieldName))
                                fieldNameList.Add(item.FieldName, "必须为整型,并且LAC组合CI需唯一");
                        }

                        if (!lac_ci.ContainsKey(gridView1.GetRowCellDisplayText(i, gridView1.Columns["LAC"]) + "_" + gridView1.GetRowCellDisplayText(i, gridView1.Columns["CI"])))
                        {
                            lac_ci.Add(gridView1.GetRowCellDisplayText(i, gridView1.Columns["LAC"]) + "_" + gridView1.GetRowCellDisplayText(i, gridView1.Columns["CI"]), gridView1.GetRowCellDisplayText(i, gridView1.Columns["小区名称"]));
                        }
                    }

                    if (item.FieldName == "BCCH"
                        && !Regex.IsMatch(gridView1.GetRowCellDisplayText(i, gridView1.Columns["BCCH"]), @"\d+"))//整型
                    {
                        //找到不符合数据
                        item.ImageIndex = 0;
                        //dc.ToolTip = "必须为浮点数,至少4位小数";
                        ColorCell cell = new ColorCell();
                        cell.Rowindex = i;
                        cell.Gridcolumn = item;
                        cell.Color = Color.FromArgb(255, 199, 199);

                        colorcellList.Add(cell);
                        //gridView1.rowcell
                        if (!noList.Contains(gridView1.GetRowCellDisplayText(i, gridView1.Columns["序号"])))
                            noList.Add(gridView1.GetRowCellDisplayText(i, gridView1.Columns["序号"]));

                        if (!fieldNameList.ContainsKey(item.FieldName))
                            fieldNameList.Add(item.FieldName, "必须为整型");
                    }

                    if (item.FieldName == "BSIC"
                        && !Regex.IsMatch(gridView1.GetRowCellDisplayText(i, gridView1.Columns["BSIC"]), @"^[0-7]{1}[0-7]{1}$|^[0-7]{1}$"))//八进制的，最大值为77、整型
                    {
                        //找到不符合数据
                        item.ImageIndex = 0;
                        //dc.ToolTip = "必须为浮点数,至少4位小数";
                        ColorCell cell = new ColorCell();
                        cell.Rowindex = i;
                        cell.Gridcolumn = item;
                        cell.Color = Color.FromArgb(255, 199, 199);

                        colorcellList.Add(cell);
                        //gridView1.rowcell
                        if (!noList.Contains(gridView1.GetRowCellDisplayText(i, gridView1.Columns["序号"])))
                            noList.Add(gridView1.GetRowCellDisplayText(i, gridView1.Columns["序号"]));
                    
                        if (!fieldNameList.ContainsKey(item.FieldName))
                            fieldNameList.Add(item.FieldName, "必须为八进制数据,最大值为77");
                    }

                    if (item.FieldName == "TCH"
                        && !Regex.IsMatch(gridView1.GetRowCellDisplayText(i, gridView1.Columns["TCH"]), @"(\d{0,4}[\,,\s])+\d{0,4}|\d{0,4}"))//用逗号或空格来分隔频点
                    {
                        //找到不符合数据
                        item.ImageIndex = 0;
                        //dc.ToolTip = "必须为浮点数,至少4位小数";
                        ColorCell cell = new ColorCell();
                        cell.Rowindex = i;
                        cell.Gridcolumn = item;
                        cell.Color = Color.FromArgb(255, 199, 199);

                        colorcellList.Add(cell);
                        //gridView1.rowcell
                        if (!noList.Contains(gridView1.GetRowCellDisplayText(i, gridView1.Columns["序号"])))
                            noList.Add(gridView1.GetRowCellDisplayText(i, gridView1.Columns["序号"]));

                        if (!fieldNameList.ContainsKey(item.FieldName))
                            fieldNameList.Add(item.FieldName, "必须为 逗号或空格来分隔频点");
                    }

                    if (item.FieldName == "天线经度"
                        && !Regex.IsMatch(gridView1.GetRowCellDisplayText(i, gridView1.Columns["天线经度"]), @"\d+\.\d+"))//用逗号或空格来分隔频点
                    {
                        //找到不符合数据
                        item.ImageIndex = 0;
                        //dc.ToolTip = "必须为浮点数,至少4位小数";
                        ColorCell cell = new ColorCell();
                        cell.Rowindex = i;
                        cell.Gridcolumn = item;
                        cell.Color = Color.FromArgb(255, 199, 199);

                        colorcellList.Add(cell);
                        //gridView1.rowcell
                        if (!noList.Contains(gridView1.GetRowCellDisplayText(i, gridView1.Columns["序号"])))
                            noList.Add(gridView1.GetRowCellDisplayText(i, gridView1.Columns["序号"]));

                        if (!fieldNameList.ContainsKey(item.FieldName))
                            fieldNameList.Add(item.FieldName, "必须为浮点数,至少1位小数");
                    }

                    if (item.FieldName == "天线纬度"
                        && !Regex.IsMatch(gridView1.GetRowCellDisplayText(i, gridView1.Columns["天线纬度"]), @"\d+\.\d+"))//用逗号或空格来分隔频点
                    {
                        //找到不符合数据
                        item.ImageIndex = 0;
                        //dc.ToolTip = "必须为浮点数,至少4位小数";
                        ColorCell cell = new ColorCell();
                        cell.Rowindex = i;
                        cell.Gridcolumn = item;
                        cell.Color = Color.FromArgb(255, 199, 199);

                        colorcellList.Add(cell);
                        //gridView1.rowcell
                        if (!noList.Contains(gridView1.GetRowCellDisplayText(i, gridView1.Columns["序号"])))
                            noList.Add(gridView1.GetRowCellDisplayText(i, gridView1.Columns["序号"]));

                        if (!fieldNameList.ContainsKey(item.FieldName))
                            fieldNameList.Add(item.FieldName, "必须为浮点数,至少1位小数");
                    }

                    if (item.FieldName == "方向角"
                        && !Regex.IsMatch(gridView1.GetRowCellDisplayText(i, gridView1.Columns["方向角"]), @"^-1|(\d{1,2}|([0-2]\d{2})|(3[0-5]\d)|(360))$"))//-1至360、整型
                    {
                        //找到不符合数据
                        item.ImageIndex = 0;
                        //dc.ToolTip = "必须为浮点数,至少4位小数";
                        ColorCell cell = new ColorCell();
                        cell.Rowindex = i;
                        cell.Gridcolumn = item;
                        cell.Color = Color.FromArgb(255, 199, 199);

                        colorcellList.Add(cell);
                        //gridView1.rowcell
                        if (!noList.Contains(gridView1.GetRowCellDisplayText(i, gridView1.Columns["序号"])))
                            noList.Add(gridView1.GetRowCellDisplayText(i, gridView1.Columns["序号"]));

                        if (!fieldNameList.ContainsKey(item.FieldName))
                            fieldNameList.Add(item.FieldName, "必须为 整型的度数");
                    }

                    if (item.FieldName == "下倾角"
                        && !Regex.IsMatch(gridView1.GetRowCellDisplayText(i, gridView1.Columns["下倾角"]), @"^-?[1-2]\d{2}([.]\d+$)?|^-?3[0-5]{1}\d{1}([.]\d+$)?|^-?\d{1,2}([.]\d+$)?"))//整型
                    {
                        //找到不符合数据
                        item.ImageIndex = 0;
                        //dc.ToolTip = "必须为浮点数,至少4位小数";
                        ColorCell cell = new ColorCell();
                        cell.Rowindex = i;
                        cell.Gridcolumn = item;
                        cell.Color = Color.FromArgb(255, 199, 199);

                        colorcellList.Add(cell);
                        //gridView1.rowcell
                        if (!noList.Contains(gridView1.GetRowCellDisplayText(i, gridView1.Columns["序号"])))
                            noList.Add(gridView1.GetRowCellDisplayText(i, gridView1.Columns["序号"]));

                        if (!fieldNameList.ContainsKey(item.FieldName))
                            fieldNameList.Add(item.FieldName, "必须为 度数");
                    }

                    if (item.FieldName == "挂高"//^(\d{1,2}|([0-2]\d{2})|(3[0-5]\d)|(360))$
                        && !Regex.IsMatch(gridView1.GetRowCellDisplayText(i, gridView1.Columns["挂高"]), @"^[1-2]\d{2}([.]\d+$)?|^3[0-5]{1}\d{1}([.]\d+$)?|^\d{1,2}([.]\d+$)?"))//正数度数,可有小数点
                    {
                        //找到不符合数据
                        item.ImageIndex = 0;
                        //dc.ToolTip = "必须为浮点数,至少4位小数";
                        ColorCell cell = new ColorCell();
                        cell.Rowindex = i;
                        cell.Gridcolumn = item;
                        cell.Color = Color.FromArgb(255, 199, 199);

                        colorcellList.Add(cell);
                        //gridView1.rowcell
                        if (!noList.Contains(gridView1.GetRowCellDisplayText(i, gridView1.Columns["序号"])))
                            noList.Add(gridView1.GetRowCellDisplayText(i, gridView1.Columns["序号"]));

                        if (!fieldNameList.ContainsKey(item.FieldName))
                            fieldNameList.Add(item.FieldName, "必须为 正数的度数");
                    }

                    if (item.FieldName.IndexOf("邻区标识") != -1
                        && !Regex.IsMatch(gridView1.GetRowCellDisplayText(i, item), @"\d+_\d+|\d+|\w+") 
                        && gridView1.GetRowCellDisplayText(i, item) != "")//整型
                    {
                        //找到不符合数据
                        item.ImageIndex = 0;
                        //dc.ToolTip = "必须为浮点数,至少4位小数";
                        ColorCell cell = new ColorCell();
                        cell.Rowindex = i;
                        cell.Gridcolumn = item;
                        cell.Color = Color.FromArgb(255, 199, 199);

                        colorcellList.Add(cell);
                        //gridView1.rowcell
                        if (!noList.Contains(gridView1.GetRowCellDisplayText(i, gridView1.Columns["序号"])))
                            noList.Add(gridView1.GetRowCellDisplayText(i, gridView1.Columns["序号"]));

                        if (!fieldNameList.ContainsKey(item.FieldName))
                            fieldNameList.Add(item.FieldName, "必须为 LAC_CI或CI或cellcode 的数据格式,且不能全部邻区为空");
                    }
                }
            }
        }

        private void gridView1_CustomDrawCell(object sender, RowCellCustomDrawEventArgs e)
        {
            /**
            //if (e.Column.FieldName == "经度")
            //{
            //    if (!Regex.IsMatch(gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns["经度"]), @"\d+\.\d{3,}"))//浮点数，四位小数
            //    {
            //        //找到不符合数据
            //        //e.Column.ImageIndex = 0;
            //        gridView1.Columns[e.Column.FieldName].ImageIndex = 0;
            //        e.Appearance.BackColor = Color.FromArgb(255, 199, 199);
            //        e.Appearance.BackColor2 = Color.LightCyan;
            //        //gridView1.rowcell

            //        if (!noList.Contains(gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns["序号"])))
            //            noList.Add(gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns["序号"]));

            //        if (!fieldNameList.ContainsKey(e.Column.FieldName))
            //            fieldNameList.Add(e.Column.FieldName, "必须为浮点数,至少3位小数");
            //    }

            //}



            //if (e.Column.FieldName == "纬度")
            //{
            //    if (!Regex.IsMatch(gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns["纬度"]), @"\d+\.\d{3,}"))//浮点数，四位小数
            //    {
            //        //找到不符合数据
            //        e.Column.ImageIndex = 0;
            //        //e.Column.ToolTip = "必须为浮点数,至少4位小数";
            //        e.Appearance.BackColor = Color.FromArgb(255, 199, 199);
            //        e.Appearance.BackColor2 = Color.LightCyan;
            //        //gridView1.rowcell
            //        if (!noList.Contains(gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns["序号"])))
            //            noList.Add(gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns["序号"]));

            //        if (!fieldNameList.ContainsKey(e.Column.FieldName))
            //            fieldNameList.Add(e.Column.FieldName, "必须为浮点数,至少3位小数");
            //    }
            //}

            //if (e.Column.FieldName == "基站类型")
            //{
            //    if (!Regex.IsMatch(gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns["基站类型"]), @"[室内,室外]"))//室内\室外
            //    {
            //        //找到不符合数据
            //        //dc.ToolTip = "必须为浮点数,至少4位小数";
            //        e.Column.ImageIndex = 0;
            //        e.Appearance.BackColor = Color.FromArgb(255, 199, 199);
            //        e.Appearance.BackColor2 = Color.LightCyan;
            //        //gridView1.rowcell
            //        if (!noList.Contains(gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns["序号"])))
            //            noList.Add(gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns["序号"]));

            //        if (!fieldNameList.ContainsKey(e.Column.FieldName))
            //            fieldNameList.Add(e.Column.FieldName, "必须为 室内或室外");
            //    }
            //}

            //if (e.Column.FieldName == "频带类型")
            //{
            //    if (!Regex.IsMatch(gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns["频带类型"]), @"900|1800"))//900\1800
            //    {
            //        //找到不符合数据
            //        e.Column.ImageIndex = 0;
            //        //dc.ToolTip = "必须为浮点数,至少4位小数";
            //        e.Appearance.BackColor = Color.FromArgb(255, 199, 199);
            //        e.Appearance.BackColor2 = Color.LightCyan;
            //        //gridView1.rowcell
            //        if (!noList.Contains(gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns["序号"])))
            //            noList.Add(gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns["序号"]));


            //        if (!fieldNameList.ContainsKey(e.Column.FieldName))
            //            fieldNameList.Add(e.Column.FieldName, "必须为 900或1800");
            //    }
            //}


            //if (e.Column.FieldName == "LAC")
            //{
            //    if (!Regex.IsMatch(gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns["LAC"]), @"\d+"))//整型
            //    {
            //        //找到不符合数据
            //        e.Column.ImageIndex = 0;
            //        //dc.ToolTip = "必须为浮点数,至少4位小数";
            //        e.Appearance.BackColor = Color.FromArgb(255, 199, 199);
            //        e.Appearance.BackColor2 = Color.LightCyan;
            //        //gridView1.rowcell
            //        if (!noList.Contains(gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns["序号"])))
            //            noList.Add(gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns["序号"]));


            //        if (!fieldNameList.ContainsKey(e.Column.FieldName))
            //            fieldNameList.Add(e.Column.FieldName, "必须为整型");
            //    }
            //}
            //if (e.Column.FieldName == "CI")
            //{
            //    if (!Regex.IsMatch(gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns["CI"]), @"\d+"))//整型
            //    {
            //        //找到不符合数据
            //        e.Column.ImageIndex = 0;
            //        //dc.ToolTip = "必须为浮点数,至少4位小数";
            //        e.Appearance.BackColor = Color.FromArgb(255, 199, 199);
            //        e.Appearance.BackColor2 = Color.LightCyan;
            //        //gridView1.rowcell

            //        if (!noList.Contains(gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns["序号"])))
            //            noList.Add(gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns["序号"]));

            //        if (!fieldNameList.ContainsKey(e.Column.FieldName))
            //            fieldNameList.Add(e.Column.FieldName, "必须为整型,并且LAC组合CI需唯一");
            //    }

            //    //LAC_CI必须唯一
            //    if (lac_ci.ContainsKey(gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns["LAC"]) + "_" + gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns["CI"])))
            //    {
            //        if (lac_ci[gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns["LAC"]) + "_" + gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns["CI"])] != gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns["小区名称"]))
            //        {
            //            //找到不符合数据
            //            e.Column.ImageIndex = 0;
            //            //dc.ToolTip = "必须为浮点数,至少4位小数";
            //            e.Appearance.BackColor = Color.FromArgb(255, 199, 199);
            //            e.Appearance.BackColor2 = Color.LightCyan;
            //            //gridView1.rowcell
            //            if (!noList.Contains(gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns["序号"])))
            //                noList.Add(gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns["序号"]));


            //            if (!fieldNameList.ContainsKey(e.Column.FieldName))
            //                fieldNameList.Add(e.Column.FieldName, "必须为整型,并且LAC组合CI需唯一");
            //        }
            //    }


            //    if (!lac_ci.ContainsKey(gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns["LAC"]) + "_" + gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns["CI"])))
            //    {
            //        lac_ci.Add(gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns["LAC"]) + "_" + gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns["CI"]), gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns["小区名称"]));
            //    }
            //}


            //if (e.Column.FieldName == "BCCH")
            //{
            //    if (!Regex.IsMatch(gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns["BCCH"]), @"\d+"))//整型
            //    {
            //        //找到不符合数据
            //        e.Column.ImageIndex = 0;
            //        //dc.ToolTip = "必须为浮点数,至少4位小数";
            //        e.Appearance.BackColor = Color.FromArgb(255, 199, 199);
            //        e.Appearance.BackColor2 = Color.LightCyan;
            //        //gridView1.rowcell
            //        if (!noList.Contains(gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns["序号"])))
            //            noList.Add(gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns["序号"]));


            //        if (!fieldNameList.ContainsKey(e.Column.FieldName))
            //            fieldNameList.Add(e.Column.FieldName, "必须为整型");
            //    }
            //}

            //if (e.Column.FieldName == "BSIC")
            //{
            //    if (!Regex.IsMatch(gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns["BSIC"]), @"^[0-7]{1}[0-7]{1}$"))//八进制的，最大值为77、整型
            //    {
            //        //找到不符合数据
            //        e.Column.ImageIndex = 0;
            //        //dc.ToolTip = "必须为浮点数,至少4位小数";
            //        e.Appearance.BackColor = Color.FromArgb(255, 199, 199);
            //        e.Appearance.BackColor2 = Color.LightCyan;
            //        //gridView1.rowcell
            //        if (!noList.Contains(gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns["序号"])))
            //            noList.Add(gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns["序号"]));


            //        if (!fieldNameList.ContainsKey(e.Column.FieldName))
            //            fieldNameList.Add(e.Column.FieldName, "必须为八进制数据,最大值为77");
            //    }
            //}

            //if (e.Column.FieldName == "TCH")
            //{
            //    if (!Regex.IsMatch(gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns["TCH"]), @"(\d{0,4}[\,,\s])+\d{0,4}"))//用逗号或空格来分隔频点
            //    {
            //        //找到不符合数据
            //        e.Column.ImageIndex = 0;
            //        //dc.ToolTip = "必须为浮点数,至少4位小数";
            //        e.Appearance.BackColor = Color.FromArgb(255, 199, 199);
            //        e.Appearance.BackColor2 = Color.LightCyan;
            //        //gridView1.rowcell
            //        if (!noList.Contains(gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns["序号"])))
            //            noList.Add(gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns["序号"]));


            //        if (!fieldNameList.ContainsKey(e.Column.FieldName))
            //            fieldNameList.Add(e.Column.FieldName, "必须为 逗号或空格来分隔频点");
            //    }
            //}

            //if (e.Column.FieldName == "天线经度")
            //{
            //    if (!Regex.IsMatch(gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns["天线经度"]), @"\d+\.\d{3,}"))//用逗号或空格来分隔频点
            //    {
            //        //找到不符合数据
            //        e.Column.ImageIndex = 0;
            //        //dc.ToolTip = "必须为浮点数,至少4位小数";
            //        e.Appearance.BackColor = Color.FromArgb(255, 199, 199);
            //        e.Appearance.BackColor2 = Color.LightCyan;
            //        //gridView1.rowcell
            //        if (!noList.Contains(gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns["序号"])))
            //            noList.Add(gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns["序号"]));


            //        if (!fieldNameList.ContainsKey(e.Column.FieldName))
            //            fieldNameList.Add(e.Column.FieldName, "必须为浮点数,至少3位小数");
            //    }
            //}

            //if (e.Column.FieldName == "天线纬度")
            //{
            //    if (!Regex.IsMatch(gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns["天线纬度"]), @"\d+\.\d{3,}"))//用逗号或空格来分隔频点
            //    {
            //        //找到不符合数据
            //        e.Column.ImageIndex = 0;
            //        //dc.ToolTip = "必须为浮点数,至少4位小数";
            //        e.Appearance.BackColor = Color.FromArgb(255, 199, 199);
            //        e.Appearance.BackColor2 = Color.LightCyan;
            //        //gridView1.rowcell
            //        if (!noList.Contains(gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns["序号"])))
            //            noList.Add(gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns["序号"]));


            //        if (!fieldNameList.ContainsKey(e.Column.FieldName))
            //            fieldNameList.Add(e.Column.FieldName, "必须为浮点数,至少3位小数");
            //    }
            //}

            //if (e.Column.FieldName == "方向角")
            //{
            //    if (!Regex.IsMatch(gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns["方向角"]), @"^-1|(\d{1,2}|([0-2]\d{2})|(3[0-5]\d)|(360))$"))//-1至360、整型
            //    {
            //        //找到不符合数据
            //        e.Column.ImageIndex = 0;
            //        //dc.ToolTip = "必须为浮点数,至少4位小数";
            //        e.Appearance.BackColor = Color.FromArgb(255, 199, 199);
            //        e.Appearance.BackColor2 = Color.LightCyan;
            //        //gridView1.rowcell
            //        if (!noList.Contains(gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns["序号"])))
            //            noList.Add(gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns["序号"]));


            //        if (!fieldNameList.ContainsKey(e.Column.FieldName))
            //            fieldNameList.Add(e.Column.FieldName, "必须为 整型的度数");
            //    }
            //}

            //if (e.Column.FieldName == "下倾角")
            //{
            //    if (!Regex.IsMatch(gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns["下倾角"]), @"^(\d{1,2}|([0-2]\d{2})|(3[0-5]\d)|(360))$"))//整型
            //    {
            //        //找到不符合数据
            //        e.Column.ImageIndex = 0;
            //        //dc.ToolTip = "必须为浮点数,至少4位小数";
            //        e.Appearance.BackColor = Color.FromArgb(255, 199, 199);
            //        e.Appearance.BackColor2 = Color.LightCyan;
            //        //gridView1.rowcell
            //        if (!noList.Contains(gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns["序号"])))
            //            noList.Add(gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns["序号"]));


            //        if (!fieldNameList.ContainsKey(e.Column.FieldName))
            //            fieldNameList.Add(e.Column.FieldName, "必须为 整型的度数");
            //    }
            //}

            //if (e.Column.FieldName == "挂高")
            //{
            //    if (!Regex.IsMatch(gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns["挂高"]), @"^(\d{1,2}|([0-2]\d{2})|(3[0-5]\d)|(360))$"))//整型
            //    {
            //        //找到不符合数据
            //        e.Column.ImageIndex = 0;
            //        //dc.ToolTip = "必须为浮点数,至少4位小数";
            //        e.Appearance.BackColor = Color.FromArgb(255, 199, 199);
            //        e.Appearance.BackColor2 = Color.LightCyan;
            //        //gridView1.rowcell
            //        if (!noList.Contains(gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns["序号"])))
            //            noList.Add(gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns["序号"]));


            //        if (!fieldNameList.ContainsKey(e.Column.FieldName))
            //            fieldNameList.Add(e.Column.FieldName, "必须为 整型的度数");
            //    }
            //}

            //if (e.Column.FieldName.IndexOf("邻区标识") != -1)
            //{
            //    if (!Regex.IsMatch(gridView1.GetRowCellDisplayText(e.RowHandle, e.Column), @"\d+_\d+") && gridView1.GetRowCellDisplayText(e.RowHandle, e.Column) != "")//整型
            //    {
            //        //找到不符合数据
            //        e.Column.ImageIndex = 0;
            //        //dc.ToolTip = "必须为浮点数,至少4位小数";
            //        e.Appearance.BackColor = Color.FromArgb(255, 199, 199);
            //        e.Appearance.BackColor2 = Color.LightCyan;
            //        //gridView1.rowcell
            //        if (!noList.Contains(gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns["序号"])))
            //            noList.Add(gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns["序号"]));


            //        if (!fieldNameList.ContainsKey(e.Column.FieldName))
            //            fieldNameList.Add(e.Column.FieldName, "必须为 LAC_CI的数据格式,且不能全部邻区为空");
            //    }
            //}

            //gridControl1.RefreshDataSource();
            //gridView1.Columns[0].Width = gridView1.Columns[0].Width + 1;
            //gridView1.Columns[0].Width = gridView1.Columns[0].Width - 1;
            */

            foreach (ColorCell item in colorcellList)
            {
                if (e.Column == item.Gridcolumn && e.RowHandle == item.Rowindex)
                {
                    e.Appearance.BackColor = item.Color;
                }
            }

            if (fieldNameList.Count == 0)
            {
                label2.Text = "正确";
            }
            else
            {
                label2.Text = "错误";
            }
        }

        private void button2_Click(object sender, EventArgs e)
        {
            if (label2.Text != "正确")
            {
                XtraMessageBox.Show("请先将工参中存在的错误修正后在提交!");
                return;
            }

            checkBox1.Checked = false;
            gridView1.ActiveFilterString = "";

            if (backup)
            {
                try
                {
                    File.Move(textBox1.Text, textBox1.Text + ".bak");
                    ExcelNPOIManager.ExportToExcel(gridView1, textBox1.Text,sheetname);

                    System.IO.FileInfo f = new System.IO.FileInfo(textBox1.Text);
                    if (f.Length < 10)
                    {
                        listBox1.Items.Add("保存Excel失败");
                        return;
                    }
                    listBox1.Items.Add("保存Excel成功");
                }
                catch (Exception)
                {                   
                    listBox1.Items.Add("备份Excel失败");
                    return;
                }

                listBox1.Items.Add("备份Excel成功");
            }


            ClientProxy clientProxy = new ClientProxy();
            if (clientProxy.connect(mainmodel.Server.IP, mainmodel.Server.Port, mainmodel.User.LoginName, mainmodel.User.Password, mainmodel.DistrictID) != ConnectResult.Success)
            {
                XtraMessageBox.Show("连接服务器失败!");
                return;
            }



            Package package = clientProxy.Package;
            package.Command = Command.DataManage;
            package.SubCommand = SubCommand.Request;
            package.Content.PrepareAddParam();
            package.Content.Type = 0xaa;
            package.Content.AddParam("");
            clientProxy.Send();

            totalSize = 0;
            size = 0;
            sendFile(clientProxy, textBox1.Text);

            listBox1.Items.Add("工参上传成功");
        }

        private long totalSize;
        private long size;
        private void sendFile(ClientProxy clientProxy, string filePath)
        {
            System.IO.FileInfo fi = new System.IO.FileInfo(filePath);
            const int MaxBuffSize = 8100;
            int contentLen;
            int offset = 10;//Header(2byte),package.length(2byte),command(1byte),subcommand(1byte),文件体长度（4byte）共10byte
            FileStream fs = new FileStream(filePath, FileMode.Open);
            totalSize = fi.Length;
            int pN = 1;//包号
            byte[] tempBuff = new byte[MaxBuffSize];

            Package package = clientProxy.Package;
            package.Command = Command.DataManage;
            package.SubCommand = SubCommand.Request;
            package.Content.PrepareAddParam();
            package.Content.Type = 0xaa;    //#define   RESTYPE_FILEUPLOAD_BLACKBLOCK  0xa7
            //package.Content.AddParam(bb.blockId);//黑点ID int
            package.Content.AddParam(fi.Name);//文件名称 string ,内容为:长度（short） + 名称
            package.Content.AddParam(pN);//包号 int
            offset += package.Content.Length;
            contentLen = fs.Read(tempBuff, 0, MaxBuffSize - offset);
            package.Content.AddParam(contentLen);//文件体长度
            package.Content.AddParam(tempBuff, contentLen);//文件体内容 byte[]
            pN++;
            try
            {
                while (contentLen != 0)
                {
                    clientProxy.Send();
                    offset = 10;
                    size += contentLen;

                    package.Content.PrepareAddParam();
                    //package.Content.AddParam(bb.blockId);//黑点ID int
                    package.Content.AddParam(fi.Name);//文件名称 string
                    package.Content.AddParam(pN);//包号 int
                    offset += package.Content.Length;
                    contentLen = fs.Read(tempBuff, 0, MaxBuffSize - offset);
                    package.Content.AddParam(contentLen);//文件体长度
                    package.Content.AddParam(tempBuff, contentLen);//文件体内容 byte[]
                    pN++;

                    if (totalSize <= 0)
                    {
                        if ((Math.Log(size) * 6) > WaitBox.ProgressPercent)
                        {
                            WaitBox.ProgressPercent++;
                        }
                    }
                    else
                    {
                        if (WaitBox.ProgressPercent < size * 100 / totalSize)
                        {
                            WaitBox.ProgressPercent = (int)(size * 100 / totalSize);
                        }
                    }
                    if (contentLen == 0)
                    {
                        clientProxy.Send();
                        package.Command = Command.DataManage;
                        package.SubCommand = SubCommand.Request;
                        package.Content.PrepareAddParam();
                        package.Content.Type = 0xaa; //RESTYPE_FILEUPLOAD_BLACKBLOCK_UPDATE    0xa8 
                        //package.Content.AddParam(bb.blockId);//黑点ID int
                        package.Content.AddParam(fi.Name);//文件名称 string
                        clientProxy.Send();
                        break;
                    }
                }
            }
            catch
            {
                //continue
            }
            finally
            {
                fs.Close();
            }
        }
    }

    class ColorCell
    {
        public int Rowindex { get; set; }
        public GridColumn Gridcolumn { get; set; }
        public Color Color { get; set; }
    }
}