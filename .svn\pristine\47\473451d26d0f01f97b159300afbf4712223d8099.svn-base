﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors.Controls;

namespace MasterCom.RAMS.CQT
{
    public partial class CQTInfoFilterPanel : UserControl
    {
        public CQTInfoFilterPanel()
        {
            InitializeComponent();
        }

        public void InitFilter(CQTCfgManager cfgMng)
        {
            ccmbxCvrType.Properties.Items.AddRange(cfgMng.CoverTypeList.ToArray());
            ccmbxCvrType.CheckAll();
            ccmbxDensityType.Properties.Items.AddRange(cfgMng.DensityTypeList.ToArray());
            ccmbxDensityType.CheckAll();
            ccmbxPointType.Properties.Items.AddRange(cfgMng.PointTypeList.ToArray());
            ccmbxPointType.CheckAll();
            ccmbxNetType.Properties.Items.AddRange(cfgMng.NetworkypeList.ToArray());
            ccmbxNetType.CheckAll();
            ccmbxSpaceType.Properties.Items.AddRange(cfgMng.SpaceTypeList.ToArray());
            ccmbxSpaceType.CheckAll();
        }

        //private const string pointID = "pointID"
        //private const string pointName = "pointName"
        //private const string pointAddr = "pointAddr"
        //private const string pointDesc = "pointDesc"
        //private const string tlLongitude = "tlLongitude"
        //private const string tlLatitude = "tlLatitude"
        //private const string brLongitude = "brLongitude"
        //private const string brLatitude = "brLatitude"
        //private const string altitude = "altitude"
        private const string pointType = "pointType";
        private const string densityType = "densityType";
        //private const string aliasName = "aliasName"
        private const string spaceType = "spaceType";
        private const string coverType = "coverType";
        private const string networkType = "networkType";
        //private const string otherType1 = "otherType1"
        //private const string otherType2 = "otherType2"
        //private const string otherType3 = "otherType3"
        //private const string otherType4 = "otherType4"
        //private const string belongArea = "belongArea"
        //private const string belongArea2 = "belongArea2"

        public string GetFilterSQLStr()
        {
            StringBuilder sb = new StringBuilder();
            sb.Append(pointType + " in (" + getCheckedTypeIDStr(ccmbxPointType.Properties.Items) + ")");
            sb.Append(" and " + densityType + "in (" + getCheckedTypeIDStr(ccmbxDensityType.Properties.Items) + ")");
            sb.Append(" and " + coverType + "in (" + getCheckedTypeIDStr(ccmbxCvrType.Properties.Items) + ")");
            sb.Append(" and " + spaceType + "in (" + getCheckedTypeIDStr(ccmbxSpaceType.Properties.Items) + ")");
            sb.Append(" and " + networkType + "in (" + getCheckedTypeIDStr(ccmbxNetType.Properties.Items) + ")");
            return sb.ToString();
        }

        /// <summary>
        /// 获取选择的typeID,逗号分隔
        /// </summary>
        /// <param name="list">Items</param>
        /// <returns>字符串形式为 1,2,3,4</returns>
        private string getCheckedTypeIDStr(CheckedListBoxItemCollection items)
        {
            string str = "";
            StringBuilder sb = new StringBuilder();
            foreach (CheckedListBoxItem item in items)
            {
                if (item.CheckState == CheckState.Checked)
                {
                    CQTCfgTypeBase cfg = item.Value as CQTCfgTypeBase;
                    if (cfg != null)
                    {
                        sb.Append(cfg.ID.ToString() + ",");
                    }
                }
            }
            str = sb.ToString();
            if (str.Length > 0)
            {//删掉最后一个逗号
                str = str.Remove(str.Length - 1, 1);
            }
            return str;
        }

    }
}
