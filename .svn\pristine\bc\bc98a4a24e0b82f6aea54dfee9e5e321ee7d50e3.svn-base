﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    [Serializable]
    public class NRPoorSINRCause : NRLowSpeedCauseBase
    {
        public NRPoorSINRCause()
        {
            AddSubReason(new NRPoorQualMod3Interf());
            AddSubReason(new NRPoorQualWeakCover());
        }

        public override string Name
        {
            get { return "SINR差"; }
        }

        public float SINRMax { get; set; } = 6;

        public override string Desc
        {
            get
            {
                return string.Format("SINR≤{0}", SINRMax);
            }
        }

        public override string Suggestion
        {
            get
            {
                return null;
            }
        }

        public override void Judge(NRLowSpeedSeg segItem, List<Event> evts, List<TestPoint> allTP, NRTpManagerBase nRCond)
        {
            foreach (TestPoint pnt in segItem.TestPoints)
            {
                if (!segItem.IsNeedJudge(pnt))
                {
                    continue;
                }

                float? sinr = nRCond.GetSCellSinr(pnt); 
                if (sinr == null || sinr >= SINRMax)
                {
                    continue;
                }

                foreach (NRLowSpeedCauseBase subReason in SubCauses)
                {
                    if (!segItem.IsNeedJudge(pnt))
                    {
                        break;
                    }
                    subReason.JudgeSinglePoint(segItem, pnt, nRCond);
                }

                if (segItem.IsNeedJudge(pnt))
                {
                    NRLowSpeedUnknowReason r = new NRLowSpeedUnknowReason();
                    r.Parent = this;
                    segItem.SetReason(new NRLowSpeedPointDetail(pnt, r, nRCond));
                }
                if (!segItem.NeedJudge)
                {
                    return;
                }

            }
        }

        public override Dictionary<string, object> CfgParam
        {
            get
            {
                Dictionary<string, object> paramDic = new Dictionary<string, object>();
                paramDic["TypeName"] = this.GetType().FullName;

                paramDic["sinrMax"] = this.SINRMax;

                List<object> list = new List<object>();
                foreach (NRLowSpeedCauseBase cause in SubCauses)
                {
                    list.Add(cause.CfgParam);
                }
                paramDic["SubCauseSet"] = list;

                return paramDic;

            }
            set
            {
                if (value == null)
                {
                    return;
                }

                this.SINRMax = (float)value["sinrMax"];

                SubCauses = new List<NRLowSpeedCauseBase>();
                List<object> list = value["SubCauseSet"] as List<object>;
                foreach (object item in list)
                {
                    Dictionary<string, object> dic = item as Dictionary<string, object>;
                    string typeName = dic["TypeName"].ToString();
                    System.Reflection.Assembly assembly = System.Reflection.Assembly.GetExecutingAssembly();
                    NRLowSpeedCauseBase cause = (NRLowSpeedCauseBase)assembly.CreateInstance(typeName);
                    cause.CfgParam = dic;
                    AddSubReason(cause);
                }
            }
        }
    }

    [Serializable]
    public class NRPoorQualMod3Interf : NRLowSpeedCauseBase
    {
        public override string Name
        {
            get { return "PCI模三冲突"; }
        }

        public float RSRPDiffMax { get; set; } = 6;
        public override string Desc
        {
            get
            {
                return string.Format("与主服相差{0}dB内的邻区中，存在与主服同模的小区", RSRPDiffMax);
            }
        }
        [NonSerialized]
        private ICell serverCell = null;
        [NonSerialized]
        private ICell nbCell = null;
        public override string Suggestion
        {
            get
            {
                return string.Format("建议对{0}小区（与主服{1}同模）的发射功率进行调整，或者调整PCI"
                    , nbCell != null ? nbCell.Name : "", serverCell != null ? serverCell.Name : "");
            }
        }

        public override void JudgeSinglePoint(NRLowSpeedSeg segItem, TestPoint testPoint, NRTpManagerBase nRCond)
        {
            float? rsrp = nRCond.GetSCellRsrp(testPoint);
            if (rsrp == null || rsrp < -141 || rsrp > 25)
            {
                return;
            }
            NRCell scell = testPoint.GetMainCell_NR();
            if (scell == null)
            {
                return;
            }
            for (int i = 0; i < 6; i++)
            {
                float? nRsrp = nRCond.GetNCellRsrp(testPoint, i);
                if (nRsrp == null)
                {
                    continue;
                }
                if (Math.Abs((float)rsrp - (float)nRsrp) <= RSRPDiffMax)
                {
                    NRCell ncell = testPoint.GetNBCell_NR(i);
                    if (ncell == null)
                    {
                        continue;
                    }
                    if (ncell.PCI % 3 == scell.PCI % 3)
                    {
                        nbCell = ncell;
                        break;
                    }
                }
            }
            if (nbCell != null)
            {
                NRPoorQualMod3Interf cln = this.Clone() as NRPoorQualMod3Interf;
                cln.serverCell = scell;
                cln.nbCell = nbCell;
                segItem.SetReason(new NRLowSpeedPointDetail(testPoint, cln, nRCond));
            }
        }

        public override Dictionary<string, object> CfgParam
        {
            get
            {
                Dictionary<string, object> paramDic = new Dictionary<string, object>();
                paramDic["TypeName"] = this.GetType().FullName;
                paramDic["rsrpDiffMax"] = this.RSRPDiffMax;

                return paramDic;
            }
            set
            {
                if (value == null)
                {
                    return;
                }

                this.RSRPDiffMax = (float)value["rsrpDiffMax"];
            }
        }
    }

    [Serializable]
    public class NRPoorQualWeakCover : NRLowSpeedCauseBase
    {
        public override string Name
        {
            get { return "弱覆盖"; }
        }
        public float RSRPMax { get; set; } = -85;
        public override string Desc
        {
            get
            {
                return string.Format("信号≤{0}dBm", RSRPMax);
            }
        }
        [NonSerialized]
        private ICell serverCell = null;
        public override string Suggestion
        {
            get
            {
                return string.Format("对小区{0}的发射功率进行调整", serverCell != null ? serverCell.Name : "");
            }
        }

        public override void JudgeSinglePoint(NRLowSpeedSeg segItem, TestPoint testPoint, NRTpManagerBase nRCond)
        {
            float? rsrp = nRCond.GetSCellRsrp(testPoint);
            if (rsrp == null || rsrp < -141 || rsrp > 25)
            {
                return;
            }
            if (rsrp < RSRPMax)
            {
                NRPoorQualWeakCover cln = this.Clone() as NRPoorQualWeakCover;
                segItem.SetReason(new NRLowSpeedPointDetail(testPoint, cln, nRCond));
            }
        }

        public override Dictionary<string, object> CfgParam
        {
            get
            {
                Dictionary<string, object> paramDic = new Dictionary<string, object>();
                paramDic["TypeName"] = this.GetType().FullName;
                paramDic["rsrpMax"] = this.RSRPMax;

                return paramDic;
            }
            set
            {
                if (value == null)
                {
                    return;
                }

                this.RSRPMax = (float)value["rsrpMax"];
            }
        }
    }
}
