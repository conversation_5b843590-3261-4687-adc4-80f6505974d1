﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc.ZTSINR.WeakSINRReason
{
    public class FuncCondition
    {
        public FuncCondition()
        {
            Reasons = new List<ReasonBase>();
            Reasons.Add(new ReasonWeakCover());
            Reasons.Add(new ReasonMultiCover());
            Reasons.Add(new ReasonMod3());
            //Reasons.Add(new ReasonMixCover());
            Reasons.Add(new ReasonChangeFreq());
            Reasons.Add(new ReasonHandOverProblem());
            Reasons.Add(new ReasonHandOverUnTimely());
            Reasons.Add(new ReasonsOverCover());
            Reasons.Add(new ReasonsBackCover());
            Reasons.Add(new ReasonsIndoorOutCover());
            Reasons.Add(new ReasonSuddenWeak());
            Reasons.Add(ReasonUnknow.Instance);
        }
        
        public float MaxSINR { get; set; } = 3;

        public List<ReasonBase> Reasons
        {
            get;
            set;
        }

        /// <summary>
        /// 判断质差原因。应先调用IsValid（）判断是否为质差点，然后才判断原因。
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        public ReasonBase JudgeReason(TestPoint tp)
        {
            foreach (ReasonBase item in Reasons)
            {
                if (item.IsValid(tp))
                {
                    return item;
                }
            }
            return ReasonUnknow.Instance;
        }

        /// <summary>
        /// 是否为质差点
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        internal bool IsValid(TestPoint tp)
        {
            object obj = GetSINR(tp);
            if (obj == null)
            {
                return false;
            }
            float sinr = float.Parse(obj.ToString());
            return sinr <= MaxSINR && -50 <= sinr;
        }

        protected virtual float? GetSINR(TestPoint tp)
        {
            return (float?)tp["lte_SINR"];
        }

        public Dictionary<string, object> ReasonsParam
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                foreach (ReasonBase reason in this.Reasons)
                {
                    param[reason.Name] = reason.Param;
                }
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                List<ReasonBase> reasonList = new List<ReasonBase>();
                foreach (var keyValuePair in param)
                {
                    foreach (ReasonBase reason in this.Reasons)
                    {
                        if (keyValuePair.Key == reason.Name)
                        {
                            reason.Param = (Dictionary<string, object>)param[keyValuePair.Key];
                            reasonList.Add(reason);
                            this.Reasons.Remove(reason);
                            break;
                        }
                    }
                }
                this.Reasons = reasonList;
            }
        }
    }

    public class FuncCondition_FDD : FuncCondition
    {
        public FuncCondition_FDD()
            : base()
        {

        }

        protected override float? GetSINR(TestPoint tp)
        {
            return (float?)tp["lte_fdd_SINR"];
        }
    }

    public class FuncCondition_Scan: FuncCondition
    {
        public FuncCondition_Scan()
            : base()
        {
            Reasons = new List<ReasonBase>();
            Reasons.Add(new ReasonWeakCover());
            Reasons.Add(new ReasonMultiCover());
            Reasons.Add(new ReasonMod3());
            Reasons.Add(new ReasonsOverCover());
            Reasons.Add(new ReasonsBackCover());
            Reasons.Add(new ReasonsIndoorOutCover());
            Reasons.Add(new ReasonSuddenWeak());
            Reasons.Add(ReasonUnknow.Instance);
        }

        protected override float? GetSINR(TestPoint tp)
        {
            return (float?)tp["LTESCAN_TopN_CELL_Specific_RSSINR"];
        }
    }
}
