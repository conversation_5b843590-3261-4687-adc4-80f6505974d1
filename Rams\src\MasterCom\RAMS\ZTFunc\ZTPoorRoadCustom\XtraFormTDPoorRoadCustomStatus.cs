﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class XtraFormTDPoorRoadCustomStatus : DevExpress.XtraEditors.XtraForm
    {
        public XtraFormTDPoorRoadCustomStatus()
        {
            InitializeComponent();
        }

        private void button2_Click(object sender, EventArgs e)
        {
               
                    this.DialogResult = DialogResult.OK;
                
           
            
        }

        private void button1_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }


        public void getStatusSelect(out int maxsub, out int minsub, out int dis)
        {
            maxsub = Convert.ToInt32(numericUpDown1.Value);
            minsub = Convert.ToInt32(numericUpDown2.Value);
            dis = Convert.ToInt32(numericUpDown3.Value);
        }
    }
}