﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using MasterCom.RAMS.Model;
using System.Xml.Serialization;
using System.IO;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class IndicatorShowFrm : DevExpress.XtraEditors.XtraForm
    {
        private MainModel mainModel;
        //查询性能指标
        private PreIndicatorInfQuery queryPreData;
        private TestIndicatorInfQuery queryTestData;

        private PreIndicatorInfQuery queryPreDataComp;
        private TestIndicatorInfQuery queryTestDataComp;

        //比较数据
        private List<Indicator> preSourceCompData;
        private List<Indicator> testSourceCompData;

        //时间数据
        private DateTime timeStart;
        private DateTime timeEnd;

        private bool IsTDInf = false;
        public IndicatorShowFrm(MainModel mainModel,bool IsTDInf)
        {
            InitializeComponent();
            this.mainModel = mainModel;
            testCompShow.Hide();
            perCompShow.Hide();

            this.IsTDInf = IsTDInf;
            if (IsTDInf)
            {
                this.Text = "T" + this.Text;
            }
            else
            {
                this.Text = "G" + this.Text;
            }

            labelControlScoreCom1.Hide();
            labelControlScoreCom2.Hide();
            labelControlScoreCom3.Hide();
            labelControlScoreCom4.Hide();
            labelControlScoreCom5.Hide();
            labelControlScoreCom6.Hide();
        }
        private List<Indicator> preSourceData;
        /// <summary>
        /// 性能数据
        /// </summary>
        public List<Indicator> PreSource
        {
            get
            {
                return preSourceData;
            }
            set
            {
                preSourceData = value;
                if (value != null)
                {
                    double scoreSum = 0;
                    for (int i = 0; i < value.Count; i++)
                    {
                        scoreSum += value[i].Score;
                    }
                    this.labelControlScorePer.Text = scoreSum.ToString("0.00");
                }
                else
                {
                    this.labelControlScorePer.Text = "0";
                }
            }
        }
        List<Indicator> testSourceData;

        public List<Indicator> TestDataSource
        {
            get
            {
                return testSourceData;
            }
            set
            {
                this.testSourceData = value;
                if (value != null)
                {
                    double scoreSum = 0;
                    for (int i = 0; i < value.Count; i++)
                    {
                        scoreSum += value[i].Score;
                    }
                    this.labelControlScoreTest.Text = scoreSum.ToString("0.00");
                }
                else
                {
                    this.labelControlScoreTest.Text = "0";
                }
            }
        }


        private void IndicatorShowFrm_Load(object sender, EventArgs e)
        {
            if (preSourceData != null && (!IsAllScoreIsZero(preSourceData)))
            {
                perScoreShow.IniData(preSourceData);
            }
            if (testSourceData != null && (!IsAllScoreIsZero(testSourceData)))
            {
                testScoreShow.IniData(testSourceData);
            }
            dTPickerCompelEnd.Visible = false;
            dTPickerCompelStart.Visible = false;

            this.dTPickerIniStart.Value = mainModel.MainForm.getQueryConditionBySettings().Periods[0].BeginTime;
            this.dTPickerIniEnd.Value = mainModel.MainForm.getQueryConditionBySettings().Periods[0].EndTime;
            labelControlScoreSum.Text = (double.Parse(this.labelControlScorePer.Text) + double.Parse(this.labelControlScoreTest.Text)).ToString("0.00");
            timeStart = new DateTime(this.dTPickerIniStart.Value.Year,this.dTPickerIniStart.Value.Month,
                this.dTPickerIniStart.Value.Day);
            timeEnd = new DateTime(this.dTPickerCompelEnd.Value.Year, this.dTPickerCompelEnd.Value.Month,
                this.dTPickerCompelEnd.Value.Day); 
            this.textBoxAdvise.Text = AdviseProcessor.GetAdviseOfTimeRegion(mainModel,
                this.dTPickerIniStart.Value, this.dTPickerIniEnd.Value, IsTDInf);
        }
        private bool IsAllScoreIsZero(List<Indicator> indicatorInfs)
        {
            foreach (Indicator ind in indicatorInfs)
            {
                if (ind.Score > 0)
                {
                    return false;
                }
            }
            return true;
        }

        private void checkEditPerCompel_CheckedChanged(object sender, EventArgs e)
        {
            if (checkEditCompel.Checked)
            {
                btnQuery.Location = new Point(4, 190);
                dTPickerCompelEnd.Visible = true;
                dTPickerCompelStart.Visible = true;

                textBoxAdvise.Visible = false;
                btnOK.Visible = false;
            }
            else
            {
                btnQuery.Location = new Point(4, 130);
                dTPickerCompelEnd.Visible = false;
                dTPickerCompelStart.Visible = false;

                textBoxAdvise.Visible = true;
                btnOK.Visible = true;
            }
        }

        private void btnQuery_Click(object sender, EventArgs e)
        {
            if (checkEditCompel.Checked)
            {
                ShowCompaleData();
                perScoreShow.Hide();
                testScoreShow.Hide();
                perCompShow.Show();
                testCompShow.Show();
            }
            else
            {
                ChangeIniTimeData();

                perScoreShow.Show();
                testScoreShow.Show();
                perCompShow.Hide();
                testCompShow.Hide();

                labelControlScoreCom1.Hide();
                labelControlScoreCom2.Hide();
                labelControlScoreCom3.Hide();
                labelControlScoreCom4.Hide();
                labelControlScoreCom5.Hide();
                labelControlScoreCom6.Hide();
            }

        }
        private void ChangeIniTimeData()
        {
            //查询性能指标
            preSourceData = null;
            queryPreData = new PreIndicatorInfQuery(mainModel, IsTDInf);
            queryTestData = new TestIndicatorInfQuery(mainModel, IsTDInf);
            timeStart = new DateTime(this.dTPickerIniStart.Value.Year,
                this.dTPickerIniStart.Value.Month, this.dTPickerIniStart.Value.Day);
            timeEnd = new DateTime(this.dTPickerIniEnd.Value.Year,
                this.dTPickerIniEnd.Value.Month, this.dTPickerIniEnd.Value.Day);

            queryPreData.TimeStart = new DateTime(this.dTPickerIniStart.Value.Year,
                this.dTPickerIniStart.Value.Month, this.dTPickerIniStart.Value.Day);
            queryPreData.TimeEnd = new DateTime(this.dTPickerIniEnd.Value.Year,
                this.dTPickerIniEnd.Value.Month, this.dTPickerIniEnd.Value.Day);

            queryTestData.TimeStart = new DateTime(this.dTPickerIniStart.Value.Year,
                this.dTPickerIniStart.Value.Month, this.dTPickerIniStart.Value.Day);
            queryTestData.TimeEnd = new DateTime(this.dTPickerIniEnd.Value.Year,
                this.dTPickerIniEnd.Value.Month, this.dTPickerIniEnd.Value.Day);

            WaitBox.Show(QueryPerData);
            this.labelControlScoreSum.Text = "0";
            if (preSourceData != null && (!IsAllScoreIsZero(preSourceData)))
            {
                double scoreSum = 0;
                for (int i = 0; i < preSourceData.Count; i++)
                {
                    scoreSum += preSourceData[i].Score;
                }
                this.labelControlScorePer.Text = scoreSum.ToString("0.00");
                this.labelControlScoreSum.Text = (scoreSum + double.Parse(labelControlScoreSum.Text)).ToString("0.00");
                perScoreShow.IniData(preSourceData);
                this.labelControlScorePer.Show();
            }
            else
            {
                perScoreShow.ClearShow();
            }
            if (testSourceData != null && (!IsAllScoreIsZero(testSourceData)))
            {
                double scoreSum = 0;
                for (int i = 0; i < testSourceData.Count; i++)
                {
                    scoreSum += testSourceData[i].Score;
                }
                this.labelControlScoreTest.Text = scoreSum.ToString("0.00");
                this.labelControlScoreSum.Text = (scoreSum + double.Parse(labelControlScoreSum.Text)).ToString("0.00");

                testScoreShow.IniData(testSourceData);
                this.labelControlScoreTest.Show();
            }
            else
            {
                testScoreShow.ClearShow();
                this.labelControlScoreTest.Text = "0";
            }

            this.textBoxAdvise.Text = AdviseProcessor.GetAdviseOfTimeRegion(mainModel,
                timeStart, timeEnd, IsTDInf);
        }

        private void ShowCompaleData()
        {
            //重新获得原始数据
            ChangeIniTimeData();
            //获得比较数据
            preSourceCompData = new List<Indicator>();
            testSourceCompData = new List<Indicator>();
            queryPreDataComp = new PreIndicatorInfQuery(mainModel, IsTDInf);
            queryTestDataComp = new TestIndicatorInfQuery(mainModel, IsTDInf);

            timeStart = new DateTime(this.dTPickerCompelStart.Value.Year,
                this.dTPickerCompelStart.Value.Month, this.dTPickerCompelStart.Value.Day);
            timeEnd = new DateTime(this.dTPickerCompelEnd.Value.Year,
                this.dTPickerCompelEnd.Value.Month, this.dTPickerCompelEnd.Value.Day);

            queryPreDataComp.TimeStart = new DateTime(this.dTPickerCompelStart.Value.Year,
                this.dTPickerCompelStart.Value.Month, this.dTPickerCompelStart.Value.Day);
            queryPreDataComp.TimeEnd = new DateTime(this.dTPickerCompelEnd.Value.Year,
                this.dTPickerCompelEnd.Value.Month, this.dTPickerCompelEnd.Value.Day);

            queryTestDataComp.TimeStart = new DateTime(this.dTPickerCompelStart.Value.Year,
                this.dTPickerCompelStart.Value.Month, this.dTPickerCompelStart.Value.Day);
            queryTestDataComp.TimeEnd = new DateTime(this.dTPickerCompelEnd.Value.Year,
                this.dTPickerCompelEnd.Value.Month, this.dTPickerCompelEnd.Value.Day);

            labelControlScorePer.Text = "0";
            labelControlScoreTest.Text = "0";
            labelControlScoreSum.Text = "0";
            labelControlScoreCom2.Text = "0";
            labelControlScoreCom4.Text = "0";
            labelControlScoreCom6.Text = "0";

            WaitBox.Show(QueryCompData);
            showPreSource();
            showTestSource();

            labelControlScoreSum.Text = (double.Parse(labelControlScorePer.Text) + double.Parse(labelControlScoreTest.Text)).ToString("0.00");
            labelControlScoreCom6.Text = (double.Parse(labelControlScoreCom2.Text) + double.Parse(labelControlScoreCom4.Text)).ToString("0.00");

            labelControlScoreCom5.Show();
            labelControlScoreCom6.Show();
        }

        private void showPreSource()
        {
            if (preSourceCompData != null && preSourceData != null)
            {
                perCompShow.Show();
                if (IsAllScoreIsZero(preSourceData))
                {
                    perCompShow.IniShow(null, null);
                }
                else
                {
                    perCompShow.IniShow(preSourceData, preSourceCompData);
                }
                double sumScore = 0;
                for (int i = 0; i < preSourceData.Count; i++)
                {
                    sumScore += preSourceData[i].Score;
                }
                labelControlScorePer.Text = sumScore.ToString("0.00");
                sumScore = 0;
                for (int i = 0; i < preSourceCompData.Count; i++)
                {
                    sumScore += preSourceCompData[i].Score;
                }
                labelControlScoreCom1.Show();
                labelControlScoreCom2.Show();
                labelControlScoreCom2.Text = sumScore.ToString("0.00");
            }
        }

        private void showTestSource()
        {
            if (testSourceCompData != null && testSourceData != null)
            {
                testCompShow.Show();
                if (IsAllScoreIsZero(testSourceData))
                {
                    perCompShow.IniShow(null, null);
                }
                else
                {
                    testCompShow.IniShow(testSourceData, testSourceCompData);
                }
                double sumScore = 0;
                for (int i = 0; i < testSourceData.Count; i++)
                {
                    sumScore += testSourceData[i].Score;
                }
                labelControlScoreTest.Text = sumScore.ToString("0.00");
                sumScore = 0;
                for (int i = 0; i < testSourceCompData.Count; i++)
                {
                    sumScore += testSourceCompData[i].Score;
                }
                labelControlScoreCom3.Show();
                labelControlScoreCom4.Show();
                labelControlScoreCom4.Text = sumScore.ToString("0.00");
            }
        }

        private void QueryPerData()
        {
            WaitBox.ProgressPercent = 20;
            preSourceData = new List<Indicator>();
            queryPreData.Query();
            preSourceData = new List<Indicator>(queryPreData.ScoreList);
            WaitBox.ProgressPercent = 65;

            testSourceData = new List<Indicator>();
            queryTestData.Query();
            testSourceData = new List<Indicator>(queryTestData.ScoreList);
            WaitBox.ProgressPercent = 100;
            WaitBox.Close();
        }

        private void QueryCompData()
        {
            WaitBox.ProgressPercent = 20;

            preSourceCompData = new List<Indicator>();
            queryPreDataComp.Query();
            preSourceCompData = new List<Indicator>(queryPreDataComp.ScoreList);
            WaitBox.ProgressPercent = 65;

            testSourceCompData = new List<Indicator>();
            queryTestDataComp.Query();
            testSourceCompData = new List<Indicator>(queryTestDataComp.ScoreList);
            WaitBox.ProgressPercent = 100;
            WaitBox.Close();
        }
        //性能数据钻取
        private void btnPerDetail_Click(object sender, EventArgs e)
        {
            EleSelectFrm eleSelFrm = new EleSelectFrm();
            if (IsTDInf)
            {
                eleSelFrm.AllEleNames = new List<string>(TDPerEles);
            }
            else
            {
                eleSelFrm.AllEleNames = new List<string>(GSMPerEles);
            }
            if (eleSelFrm.ShowDialog() == DialogResult.OK)
            {
                perDetailQuery = new PerDetailInfQuery(mainModel, IsTDInf);

                perDetailQuery.TimeStart = new DateTime(this.dTPickerIniStart.Value.Year,
                    this.dTPickerIniStart.Value.Month, this.dTPickerIniStart.Value.Day);
                perDetailQuery.TimeEnd = new DateTime(this.dTPickerIniEnd.Value.Year,
                    this.dTPickerIniEnd.Value.Month, this.dTPickerIniEnd.Value.Day);
                perDetailQuery.EleName = eleSelFrm.SelectedEleName;
                WaitBox.Show(QueryPerDetailInf);
                if (perDetailQuery.DetailInf != null && perDetailQuery.DetailInf.Count != 0)
                {
                    EleRadioAndLACCI radioShow = new EleRadioAndLACCI(mainModel, IsTDInf);
                    radioShow.MainSelectedEleName = eleSelFrm.SelectedEleName;
                    radioShow.TimeStart = new DateTime(this.dTPickerIniStart.Value.Year,
                        this.dTPickerIniStart.Value.Month, this.dTPickerIniStart.Value.Day);
                    radioShow.TimeEnd = new DateTime(this.dTPickerIniEnd.Value.Year,
                        this.dTPickerIniEnd.Value.Month, this.dTPickerIniEnd.Value.Day);
                    radioShow.DetailInf = new List<EleDetailInf>(perDetailQuery.DetailInf);
                    radioShow.ShowDialog();
                }
                else
                {
                    XtraMessageBox.Show("读取数据出现问题，请检查时间输入！");
                }
            }
        }
        PerDetailInfQuery perDetailQuery;
        private void QueryPerDetailInf()
        {
            WaitBox.ProgressPercent = 25;
            if (perDetailQuery != null)
            {
                perDetailQuery.Query();
            }
            WaitBox.ProgressPercent = 100;
            WaitBox.Close();
        }
        //测试数据钻取

        TestDetailInfQuery testDetailQuery;
        private void btnTestDetail_Click(object sender, EventArgs e)
        {

            testDetailQuery = new TestDetailInfQuery(mainModel, IsTDInf);

            testDetailQuery.TimeStart = new DateTime(this.dTPickerIniStart.Value.Year,
                this.dTPickerIniStart.Value.Month, this.dTPickerIniStart.Value.Day);
            testDetailQuery.TimeEnd = new DateTime(this.dTPickerIniEnd.Value.Year,
                this.dTPickerIniEnd.Value.Month, this.dTPickerIniEnd.Value.Day);

            WaitBox.Show(QueryTestDetailInf);
            if (testDetailQuery.DetailInf != null && testDetailQuery.DetailInf.Count != 0)
            {
                TestDetailShow testDetailShow = new TestDetailShow();
                testDetailShow.AllShowInfs = testDetailQuery.DetailInf;
                testDetailShow.ShowDialog();
            }
            else
            {
                XtraMessageBox.Show("读取数据出现问题，请检查时间输入！");
            }
        }
        private void QueryTestDetailInf()
        {
            WaitBox.ProgressPercent = 25;
            if (testDetailQuery != null)
            {
                testDetailQuery.Query();
            }
            WaitBox.ProgressPercent = 100;
            WaitBox.Close();
        }

        public string[] GSMPerEles { get; set; } = new string[] {
            "信令信道分配成功率",
            "话音信道分配成功率(不含切)",
            "无线接通率",
            "信令信道拥塞率",
            "话音信道拥塞率(不含切)",
            "话音信道掉话率(不含切)",
            "非PBGT切换占比",
            "下行话音质量",
            "切换成功率",
            "上行TBF建立成功率",
            "下行TBF建立成功率",
            "下行TBF掉线率",
            "PDCH分配成功率"
        };
        public string[] TDPerEles { get; set; } = new string[] { 
            "CS域RAB拥塞率",
            "CS域无线接通率",
            "CS域误块率",
            "语音业务无线掉话率",
            "PS域RAB拥塞率",
            "PS域RAB建立成功率",
            "PS域误块率",
            "PS域无线掉线率",
            "接力切换成功率",
            "码资源利用率"
        };

        private void btnOK_Click(object sender, EventArgs e)
        {
            string advise = textBoxAdvise.Text;
            bool subRes = AdviseProcessor.SubmitAdviseToDB(mainModel, advise,timeStart, timeEnd, IsTDInf);
            if (!subRes)
            {
                XtraMessageBox.Show("提交建议出错！");
            }
        }
    }
}