﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using BrightIdeasSoftware;
using DevExpress.XtraCharts;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc.VoLTEBlockCallCause
{
    public partial class BlockCallCauseForm : MinCloseForm
    {
        CallInfo clickedCallInfo = new CallInfo();                  //保存当前点击的CallInfo 

        public BlockCallCauseForm():base()
        {
            InitializeComponent();
            this.DisposeWhenClose = true;
            init();
        }

        private void init()
        {
            addBlockCallInfo();

            addCallInfo();
        }

        private void addCallInfo()
        {
            colFileName.AspectGetter += delegate (object row)
            {
                if (row is CallInfo)
                {
                    return (row as CallInfo).FileName;
                }
                return null;
            };

            colHoNum.AspectGetter += delegate (object row)
            {
                if (row is CallInfo)
                {
                    return (row as CallInfo).HoNum;
                }
                return null;
            };

            colIsBlock.AspectGetter += delegate (object row)
            {
                if (row is CallInfo)
                {
                    return (row as CallInfo).IsBlockCall ? "是" : "否";
                }
                return null;
            };

            addMessageInfo();

            addBlockEvtInfo();

            this.colMoMt.AspectGetter += delegate (object row)
            {
                if (row is CallInfo)
                {
                    CallInfo call = row as CallInfo;
                    return call.MoMtDesc;
                }
                return null;
            };

            this.colRsrp.AspectGetter += delegate (object row)
            {
                if (row is CallInfo)
                {
                    CallInfo call = row as CallInfo;
                    return call.RsrpAvg;
                }
                return null;
            };

            this.colSinr.AspectGetter += delegate (object row)
            {
                if (row is CallInfo)
                {
                    CallInfo call = row as CallInfo;
                    return call.SinrAvg;
                }
                return null;
            };

            this.colTime.AspectGetter += delegate (object row)
            {
                if (row is CallInfo)
                {
                    CallInfo call = row as CallInfo;
                    return call.BlockTime;
                }
                return null;
            };

            this.colCause.AspectGetter += delegate (object row)
            {
                if (row is CallInfo)
                {
                    CallInfo call = row as CallInfo;
                    if (call.IsBlockCall)
                    {
                        return call.BlockCause;
                    }
                    return null;
                }
                return null;
            };

            this.colMultiCvrPer.AspectGetter += delegate (object row)
            {
                if (row is CallInfo)
                {
                    CallInfo call = row as CallInfo;
                    return call.MultiCvrPer;
                }
                return null;
            };
        }

        private void addMessageInfo()
        {
            colIMSErrorMsg.AspectGetter += delegate (object row)
            {
                if (row is CallInfo)
                {
                    CallInfo call = row as CallInfo;
                    if (call.ErrorMsg != null)
                    {
                        MessageInfo messageInfo = MessageInfoManager.GetInstance()[call.ErrorMsg.ID];
                        if (messageInfo != null)
                        {
                            return messageInfo.Name;
                        }
                    }
                }
                return null;
            };
        }

        private void addBlockEvtInfo()
        {
            colLat.AspectGetter += delegate (object row)
            {
                if (row is CallInfo)
                {
                    CallInfo call = row as CallInfo;
                    if (call.IsBlockCall)
                    {
                        return call.BlockEvt.Latitude;
                    }
                }
                return null;
            };

            this.colLng.AspectGetter += delegate (object row)
            {
                if (row is CallInfo)
                {
                    CallInfo call = row as CallInfo;
                    if (call.IsBlockCall)
                    {
                        return call.BlockEvt.Longitude;
                    }
                }
                return null;
            };
        }

        private void addBlockCallInfo()
        {
            this.objLv.CanExpandGetter += delegate (object row)
            {
                return row is BlockCallInfo;
            };

            this.objLv.ChildrenGetter += delegate (object row)
            {
                if (row is BlockCallInfo)
                {
                    return (row as BlockCallInfo).MoMtCalls;
                }
                return null;
            };

            this.colSN.AspectGetter += delegate (object row)
            {
                if (row is BlockCallInfo)
                {
                    BlockCallInfo call = row as BlockCallInfo;
                    return call.SN;
                }
                return null;
            };
        }

        private List<BlockCallInfo> calls = null;
        private BlockCallCondition blockCallCond;
        internal void FillData(List<BlockCallInfo> calls,BlockCallCondition blockCallCond)
        {
            this.calls = calls;
            this.blockCallCond = blockCallCond;
            makeSummary();
            this.colSinr.Text = string.Format("未接通前{0}秒平均SINR",blockCallCond.PoorSinrSec);
            this.colRsrp.Text = string.Format("未接通前{0}秒平均RSRP", blockCallCond.WeakRsrpSec);
            this.colHoNum.Text = string.Format("未接通前{0}秒切换次数", blockCallCond.HoSec);
            this.objLv.ClearObjects();
            this.objLv.RebuildColumns();
            this.objLv.SetObjects(calls);
            this.objLv.ExpandAll();
        }

        private void makeSummary()
        {
            Dictionary<string, int> causeDic = new Dictionary<string, int>();
            foreach (string name in Enum.GetNames(typeof(BlockCallCause)))
            {
                causeDic[name] = 0;
            }
            int dropNum = 0;
            foreach (BlockCallInfo dropCall in calls)
            {
                foreach (CallInfo call in dropCall.MoMtCalls)
                {
                    if (call.IsBlockCall)
                    {
                        dropNum++;
                        causeDic[call.BlockCause.ToString()]++;
                    }
                }
            }
            DataTable tb = new DataTable();
            tb.Columns.Add("原因", typeof(string));
            tb.Columns.Add("未接通个数", typeof(int));
            tb.Columns.Add("占比(%)", typeof(double));

            Series mainSer = chartMain.Series[0];
            mainSer.Points.Clear();
            foreach (KeyValuePair<string, int> pair in causeDic)
            {
                DataRow row = tb.NewRow();
                row["原因"] = pair.Key;
                row["未接通个数"] = pair.Value;
                double per = Math.Round(100.0 * pair.Value / dropNum, 2);
                row["占比(%)"] = per;
                tb.Rows.Add(row);
                SeriesPoint pnt = new SeriesPoint(pair.Key, per);
                mainSer.Points.Add(pnt);
            }
            DataRow srow = tb.NewRow();
            srow["原因"] = "汇总";
            srow["未接通个数"] = dropNum;
            srow["占比(%)"] = 100;
            tb.Rows.Add(srow);
            gridSummary.DataSource = tb;
            viewSummary.PopulateColumns();
            viewSummary.BestFitColumns();
        }

        private void objLv_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            OlvListViewHitTestInfo info = this.objLv.OlvHitTest(e.X, e.Y);
            if (info.RowObject is BlockCallInfo)
            {
                MainModel.ClearDTData();
                BlockCallInfo dc = info.RowObject as BlockCallInfo;
                foreach (CallInfo call in dc.MoMtCalls)
                {
                    addDTData(call);
                }
                this.MainModel.IsFileReplayByCompareMode = true;
                this.MainModel.FireDTDataChanged(this);
                this.MainModel.FireSetDefaultMapSerialTheme("LTE_TDD:RSRP");
            }
            else if (info.RowObject is CallInfo)
            {
                MainModel.ClearDTData();
                CallInfo call = info.RowObject as CallInfo;
                addDTData(call);
                this.MainModel.IsFileReplayByCompareMode = false;
                this.MainModel.FireDTDataChanged(this);
                this.MainModel.FireSetDefaultMapSerialTheme("LTE_TDD:RSRP");
            }
        }

        private void addDTData(CallInfo call)
        {
            foreach (TestPoint tp in call.TestPoints)
            {
                MainModel.DTDataManager.Add(tp);
            }
            foreach (Event evt in call.Events)
            {
                MainModel.DTDataManager.Add(evt);
            }
            foreach (MasterCom.RAMS.Model.Message msg in call.Messages)
            {
                MainModel.DTDataManager.Add(msg);
            }
        }

        private void miExportXls_Click(object sender, EventArgs e)
        {
            List<NPOIRow> rows = new List<NPOIRow>();
            NPOIRow row = new NPOIRow();
            rows.Add(row);
            foreach (OLVColumn col in objLv.Columns)
            {
                row.AddCellValue(col.Text);
            }
            foreach (BlockCallInfo dc in calls)
            {
                row = new NPOIRow();
                rows.Add(row);
                row.AddCellValue(dc.SN);
                foreach (CallInfo call in dc.MoMtCalls)
                {
                    NPOIRow subRow = new NPOIRow();
                    row.AddSubRow(subRow);
                    foreach (OLVColumn col in objLv.Columns)
                    {
                        if (col==this.colSN)
                        {
                            continue;
                        }
                        subRow.AddCellValue(col.GetValue(call));
                    }
                }
            }
            ExcelNPOIManager.ExportToExcel(rows);
        }

        private void miExpandAll_Click(object sender, EventArgs e)
        {
            this.objLv.ExpandAll();
        }

        private void miCollapseAll_Click(object sender, EventArgs e)
        {
            this.objLv.CollapseAll();
        }

        private void miExportXlsSum_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(this.viewSummary);
        }

        /// <summary>
        /// 当ctxMenu关闭时使回放功能失效
        /// </summary>
        private void ctxMenu_Closed(object sender, ToolStripDropDownClosedEventArgs e)
        {
            miReplayFile.Enabled = false;
            miReplayEvent.Enabled = false;
            miCompareReplayEvent.Enabled = false;
        }

        /// <summary>
        /// 回放所属文件
        /// </summary>
        private void miReplayFile_Click(object sender, EventArgs e)
        {
            if (clickedCallInfo == null)
            {
                MessageBox.Show("请选择文件！");
                miReplayFile.Enabled = false;
                miReplayEvent.Enabled = false;
                miCompareReplayEvent.Enabled = false;
            }
            else
            {
                if (clickedCallInfo.BlockEvt != null)
                {
                    DTData data = clickedCallInfo.BlockEvt;
                    Model.Interface.FileReplayer.Replay(data, false);
                }
                else
                    MessageBox.Show("未接通事件为空！");
            }
        }

        /// <summary>
        /// 回放事件
        /// </summary>
        private void miReplayEvent_Click(object sender, EventArgs e)
        {
            if (clickedCallInfo == null)
            {
                MessageBox.Show("请选择文件！");
                miReplayFile.Enabled = false;
                miReplayEvent.Enabled = false;
                miCompareReplayEvent.Enabled = false;
            }
            else
            {
                if (clickedCallInfo.BlockEvt != null)
                {
                    Model.Interface.FileReplayer.Replay(clickedCallInfo.BlockEvt, true);
                }
                else
                    MessageBox.Show("未接通事件为空！");
            }
        }

        /// <summary>
        /// 对比回放事件
        /// </summary>
        private void miCompareReplayEvent_Click(object sender, EventArgs e)
        {
            if (clickedCallInfo == null)
            {
                MessageBox.Show("请选择文件！");
                miReplayFile.Enabled = false;
                miReplayEvent.Enabled = false;
                miCompareReplayEvent.Enabled = false;
            }
            else
            {
                if (clickedCallInfo.BlockEvt != null)
                {
                    Model.Interface.FileReplayer.ReplayOnePartBothSides(clickedCallInfo.BlockEvt);
                }
                else
                    MessageBox.Show("未接通事件为空！");
            }
        }

        /// <summary>
        /// 保存当前右击的行所对应的CallInfo
        /// </summary>
        private void objLv_MouseDown(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Right)
            {
                TreeListView lv = sender as TreeListView;
                OlvListViewHitTestInfo info = lv.OlvHitTest(e.X, e.Y);
                if (info.RowObject is CallInfo)
                {
                    clickedCallInfo = info.RowObject as CallInfo;
                    miReplayFile.Enabled = true;
                    miReplayEvent.Enabled = true;
                    miCompareReplayEvent.Enabled = true;
                }
                else
                {
                    clickedCallInfo = null;
                    miReplayFile.Enabled = false;
                    miReplayEvent.Enabled = false;
                    miCompareReplayEvent.Enabled = false;
                }
            }
        }

    }
}
