<?xml version="1.0"?>
<Configs>
  <Config name="Main">
    <Item name="Report" typeName="CQTKPIReport">
      <Item name="Name" typeName="String">测试报表</Item>
      <Item name="CarreerID" typeName="Int32">1</Item>
      <Item name="Columns" typeName="IList">
        <Item typeName="CQTKPIReportColumn">
          <Item name="Name" typeName="String">RxLev90覆盖率</Item>
          <Item name="CarreerID" typeName="Int32">1</Item>
          <Item name="Formula" typeName="String">{100*(Mx_640117+Mx_64010A+Mx_640109+Mx_640108)/Mx_640101}</Item>
          <Item name="KPIValueRangeMin" typeName="Single">0</Item>
          <Item name="KPIValueRangeMax" typeName="Single">100</Item>
          <Item name="ScoreRangeMin" typeName="Double">0</Item>
          <Item name="ScoreRangeMax" typeName="Double">100</Item>
          <Item name="ScoreScheme" typeName="CQTKPIScoreScheme">
            <Item name="Name" typeName="String" />
            <Item name="MinScore" typeName="Double">0</Item>
            <Item name="MaxScore" typeName="Double">100</Item>
            <Item name="MinKPIValue" typeName="Double">0</Item>
            <Item name="MaxKPIValue" typeName="Double">100</Item>
            <Item name="ScoreOrderType" typeName="Int32">0</Item>
            <Item name="IsSmoothScore" typeName="Boolean">True</Item>
            <Item name="ScoreColorRanges" typeName="IList">
              <Item typeName="CQTKPIScoreColorRange">
                <Item name="ScoreRangeMin" typeName="Double">0</Item>
                <Item name="ScoreRangeMax" typeName="Double">20</Item>
                <Item name="MinKPIValue" typeName="Single">0</Item>
                <Item name="MaxKPIValue" typeName="Single">20</Item>
                <Item name="MinIncluded" typeName="Boolean">True</Item>
                <Item name="MaxIncluded" typeName="Boolean">False</Item>
                <Item name="R" typeName="Int32">255</Item>
                <Item name="G" typeName="Int32">0</Item>
                <Item name="B" typeName="Int32">0</Item>
                <Item name="Desc" typeName="String" />
                <Item name="Visible" typeName="Boolean">True</Item>
                <Item name="IsSmoothScore" typeName="Boolean">True</Item>
              </Item>
              <Item typeName="CQTKPIScoreColorRange">
                <Item name="ScoreRangeMin" typeName="Double">20</Item>
                <Item name="ScoreRangeMax" typeName="Double">40</Item>
                <Item name="MinKPIValue" typeName="Single">20</Item>
                <Item name="MaxKPIValue" typeName="Single">40</Item>
                <Item name="MinIncluded" typeName="Boolean">True</Item>
                <Item name="MaxIncluded" typeName="Boolean">False</Item>
                <Item name="R" typeName="Int32">255</Item>
                <Item name="G" typeName="Int32">127</Item>
                <Item name="B" typeName="Int32">0</Item>
                <Item name="Desc" typeName="String" />
                <Item name="Visible" typeName="Boolean">True</Item>
                <Item name="IsSmoothScore" typeName="Boolean">True</Item>
              </Item>
              <Item typeName="CQTKPIScoreColorRange">
                <Item name="ScoreRangeMin" typeName="Double">40</Item>
                <Item name="ScoreRangeMax" typeName="Double">60</Item>
                <Item name="MinKPIValue" typeName="Single">40</Item>
                <Item name="MaxKPIValue" typeName="Single">60</Item>
                <Item name="MinIncluded" typeName="Boolean">True</Item>
                <Item name="MaxIncluded" typeName="Boolean">False</Item>
                <Item name="R" typeName="Int32">255</Item>
                <Item name="G" typeName="Int32">255</Item>
                <Item name="B" typeName="Int32">0</Item>
                <Item name="Desc" typeName="String" />
                <Item name="Visible" typeName="Boolean">True</Item>
                <Item name="IsSmoothScore" typeName="Boolean">True</Item>
              </Item>
              <Item typeName="CQTKPIScoreColorRange">
                <Item name="ScoreRangeMin" typeName="Double">60</Item>
                <Item name="ScoreRangeMax" typeName="Double">80</Item>
                <Item name="MinKPIValue" typeName="Single">60</Item>
                <Item name="MaxKPIValue" typeName="Single">80</Item>
                <Item name="MinIncluded" typeName="Boolean">True</Item>
                <Item name="MaxIncluded" typeName="Boolean">False</Item>
                <Item name="R" typeName="Int32">127</Item>
                <Item name="G" typeName="Int32">191</Item>
                <Item name="B" typeName="Int32">0</Item>
                <Item name="Desc" typeName="String" />
                <Item name="Visible" typeName="Boolean">True</Item>
                <Item name="IsSmoothScore" typeName="Boolean">True</Item>
              </Item>
              <Item typeName="CQTKPIScoreColorRange">
                <Item name="ScoreRangeMin" typeName="Double">80</Item>
                <Item name="ScoreRangeMax" typeName="Double">100</Item>
                <Item name="MinKPIValue" typeName="Single">80</Item>
                <Item name="MaxKPIValue" typeName="Single">100</Item>
                <Item name="MinIncluded" typeName="Boolean">True</Item>
                <Item name="MaxIncluded" typeName="Boolean">True</Item>
                <Item name="R" typeName="Int32">0</Item>
                <Item name="G" typeName="Int32">128</Item>
                <Item name="B" typeName="Int32">0</Item>
                <Item name="Desc" typeName="String" />
                <Item name="Visible" typeName="Boolean">True</Item>
                <Item name="IsSmoothScore" typeName="Boolean">True</Item>
              </Item>
            </Item>
          </Item>
        </Item>
      </Item>
      <Item name="SummaryColumns" typeName="IList">
        <Item typeName="CQTKPISummaryColumn">
          <Item name="Name" typeName="String">评分</Item>
          <Item name="Items" typeName="IList">
            <Item typeName="CQTKPIScoreColorRange">
              <Item name="Included" typeName="Boolean">False</Item>
              <Item name="ID" typeName="String">aebf0121-3084-4623-8570-d1be453877e2</Item>
              <Item name="Name" typeName="String" />
              <Item name="Weight" typeName="Double">1</Item>
            </Item>
          </Item>
          <Item name="ScoreRangeColors" typeName="IList">
            <Item typeName="DTParameterRangeColor">
              <Item name="Min" typeName="Single">0</Item>
              <Item name="Max" typeName="Single">40</Item>
              <Item name="MinIncluded" typeName="Boolean">True</Item>
              <Item name="MaxIncluded" typeName="Boolean">False</Item>
              <Item name="DescInfo" typeName="String" />
              <Item name="Value" typeName="Color">
                <Item name="R" typeName="Int32">255</Item>
                <Item name="G" typeName="Int32">0</Item>
                <Item name="B" typeName="Int32">0</Item>
              </Item>
            </Item>
            <Item typeName="DTParameterRangeColor">
              <Item name="Min" typeName="Single">40</Item>
              <Item name="Max" typeName="Single">60</Item>
              <Item name="MinIncluded" typeName="Boolean">True</Item>
              <Item name="MaxIncluded" typeName="Boolean">False</Item>
              <Item name="DescInfo" typeName="String" />
              <Item name="Value" typeName="Color">
                <Item name="R" typeName="Int32">255</Item>
                <Item name="G" typeName="Int32">127</Item>
                <Item name="B" typeName="Int32">0</Item>
              </Item>
            </Item>
            <Item typeName="DTParameterRangeColor">
              <Item name="Min" typeName="Single">60</Item>
              <Item name="Max" typeName="Single">80</Item>
              <Item name="MinIncluded" typeName="Boolean">True</Item>
              <Item name="MaxIncluded" typeName="Boolean">False</Item>
              <Item name="DescInfo" typeName="String" />
              <Item name="Value" typeName="Color">
                <Item name="R" typeName="Int32">255</Item>
                <Item name="G" typeName="Int32">255</Item>
                <Item name="B" typeName="Int32">0</Item>
              </Item>
            </Item>
            <Item typeName="DTParameterRangeColor">
              <Item name="Min" typeName="Single">80</Item>
              <Item name="Max" typeName="Single">98</Item>
              <Item name="MinIncluded" typeName="Boolean">True</Item>
              <Item name="MaxIncluded" typeName="Boolean">False</Item>
              <Item name="DescInfo" typeName="String" />
              <Item name="Value" typeName="Color">
                <Item name="R" typeName="Int32">127</Item>
                <Item name="G" typeName="Int32">191</Item>
                <Item name="B" typeName="Int32">0</Item>
              </Item>
            </Item>
            <Item typeName="DTParameterRangeColor">
              <Item name="Min" typeName="Single">98</Item>
              <Item name="Max" typeName="Single">100</Item>
              <Item name="MinIncluded" typeName="Boolean">True</Item>
              <Item name="MaxIncluded" typeName="Boolean">True</Item>
              <Item name="DescInfo" typeName="String" />
              <Item name="Value" typeName="Color">
                <Item name="R" typeName="Int32">0</Item>
                <Item name="G" typeName="Int32">128</Item>
                <Item name="B" typeName="Int32">0</Item>
              </Item>
            </Item>
          </Item>
        </Item>
      </Item>
    </Item>
  </Config>
</Configs>