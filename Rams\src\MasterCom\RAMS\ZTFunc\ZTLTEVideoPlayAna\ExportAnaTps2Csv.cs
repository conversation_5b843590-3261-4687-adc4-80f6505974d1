﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;
using System.Windows.Forms;
using System.IO;
using DevExpress.XtraEditors;

namespace MasterCom.RAMS.ZTFunc.LteHttpPageOrVideoPlay
{
    class ExportAnaItems2Csv
    {
        protected ExportAnaItems2Csv()
        {

        }

        private static string fileName;
        protected static string getContentRow(ExportParm ep)
        {
            StringBuilder row = new StringBuilder();
            row.Append(ep.SN + ",");
            row.Append(ep.FileName + ",");
            row.Append(ep.TpTimeStr + ",");
            row.Append(ep.Longitude + ",");
            row.Append(ep.Latitude + ",");
            row.Append(ep.RSRP + ",");
            row.Append(ep.SINR + ",");
            row.Append(ep.AppSpeed + ",");
            row.Append(ep.AppType + ",");
            row.Append(ep.TransMode + ",");
            row.Append(ep.BLER + ",");
            row.Append(ep.RankIndicator + ",");
            row.Append(ep.DLGrantCount + ",");
            row.Append(ep.PrbNumsSlot + ",");
            row.Append(ep.MaxNbCellRsrp + ",");
            row.Append(ep.MaxNbCellSinr + ",");
            return row.ToString();
        }
        protected static string getTitleRow()
        {
            StringBuilder row = new StringBuilder();
            row.Append("序号,");
            row.Append("文件名,");
            row.Append("时间,");
            row.Append("采样点经度,");
            row.Append("采样点纬度,");
            row.Append("RSRP,");
            row.Append("SINR,");
            row.Append("App_Speed,");
            row.Append("App_Type,");
            row.Append("传输模式,");
            row.Append("BLER,");
            row.Append("单双流,");
            row.Append("下行子帧调度个数,");
            row.Append("每时隙调度RB,");
            row.Append("最强邻区RSRP,");
            row.Append("最强邻区SINR,");
            return row.ToString();
        }
        /// <summary>
        /// 分析结果生成导出内容
        /// </summary>
        /// <param name="resultList"></param>
        /// <returns></returns>
        protected static List<string> httpPageListToRecord(List<ZTLTEHttpPageAnaItem> resultList)
        {
            List<string> record = new List<string>();
            List<ExportParm> parmList;
            record.Add(getTitleRow());
            if (resultList != null)
            {
                foreach (LteCommonParm result in resultList)
                {
                    parmList = anaItemToParmList(result);
                    foreach (ExportParm parm in parmList)
                    {
                        record.Add(getContentRow(parm));
                    }
                }
            }
            return record;
        }

        /// <summary>
        /// 分析结果生成导出内容
        /// </summary>
        /// <param name="resultList"></param>
        /// <returns></returns>
        protected static List<string> videoPlayListToRecords(List<ZTLTEVideoPlayAnaItem> resultList, bool isOther)
        {
            List<string> record = new List<string>();
            int index = isOther ? 1 : 0;
            List<ExportParm> parmList;
            record.Add(getTitleRow());
            if (resultList != null)
            {
                foreach (ZTLTEVideoPlayAnaItem result in resultList)
                {
                    parmList = videoPlayAnaItemToParmList(result)[index];
                    foreach (ExportParm parm in parmList)
                    {
                        record.Add(getContentRow(parm));
                    }
                }
            }
            return record;
        }
        /// <summary>
        /// 统计单元的采样点生成指标表
        /// </summary>
        protected static List<ExportParm> anaItemToParmList(LteCommonParm item)
        {
            List<ExportParm> epList = new List<ExportParm>();
            for (int i = 0; i < item.StatTpsList.Count; i++)
            {
                int maxNBIndex = item.StatTpsList[i].Value;
                TestPoint tp = item.StatTpsList[i].Key;
                epList.Add(new ExportParm(item.SN, maxNBIndex, tp));
            }
            return epList;
        }
        /// <summary>
        /// 流媒体播放统计单元的采样点生成指标表
        /// </summary>
        /// <param name="item"></param>
        /// <returns>卡顿非卡顿（缓冲非缓冲）指标</returns>
        protected static List<List<ExportParm>> videoPlayAnaItemToParmList(ZTLTEVideoPlayAnaItem item)
        {
            List<List<ExportParm>> retList = new List<List<ExportParm>>();
            List<ExportParm> epList = new List<ExportParm>();
            for (int i = 0; i < item.StatTpsList.Count; i++)    //先统计卡顿（缓冲）区间的采样点
            {
                int maxNBIndex = item.StatTpsList[i].Value;
                TestPoint tp = item.StatTpsList[i].Key;
                epList.Add(new ExportParm(item.SN, maxNBIndex, tp));
            }
            retList.Add(epList);
            epList = new List<ExportParm>();
            for (int i = 0; i < item.OtherTpsList.Count; i++)  //再统计非卡顿（缓冲）区间的采样点
            {
                epList.Add(new ExportParm(item.SN, item.OtherMaxNBIndexList[i], item.OtherTpsList[i]));
            }
            retList.Add(epList);
            return retList;
        }

        /// <summary>
        /// 导出Http浏览采样点结果到Csv
        /// </summary>
        /// <param name="list"></param>
        public static void ExportHttpAnaResult2Csv(List<ZTLTEHttpPageAnaItem> list)
        {
            SaveFileDialog fileDlg = new SaveFileDialog();
            fileDlg.RestoreDirectory = true;
            fileDlg.Filter = "CSV文件(*.csv)|*.csv";
            fileDlg.Title = "选择保存的文件";
            if (fileDlg.ShowDialog() != DialogResult.OK)
            {
                return;
            }

            fileName = fileDlg.FileName;
            if (System.IO.File.Exists(fileName))
            {
                System.IO.File.Delete(fileName);
            }
            List<string> lstRecord = httpPageListToRecord(list);
            object obj = lstRecord;
            WaitBox.Show("正在导出到Csv..", doExport2Csv, obj);
            if (DialogResult.Yes == XtraMessageBox.Show("Csv文件保存完毕，是否现在打开?", "打开文件", MessageBoxButtons.YesNo))  //添加了自动打开文件的提示
            {
                try
                {
                    System.Diagnostics.Process.Start(fileName);
                }
                catch
                {
                    MessageBox.Show("打开失败!\r\n文件名:" + fileName);
                }
            }
        }
        /// <summary>
        /// 导出流媒体分析结果到Csv
        /// </summary>
        /// <param name="list"></param>
        /// <param name="isOther"></param>
        public static void ExportVideoAnaResult2Csv(List<ZTLTEVideoPlayAnaItem> list, bool isOther)
        {
            SaveFileDialog fileDlg = new SaveFileDialog();
            fileDlg.RestoreDirectory = true;
            fileDlg.Filter = "CSV文件(*.csv)|*.csv";
            fileDlg.Title = "选择保存的文件";
            if (fileDlg.ShowDialog() != DialogResult.OK)
            {
                return;
            }

            fileName = fileDlg.FileName;
            if (System.IO.File.Exists(fileName))
            {
                System.IO.File.Delete(fileName);
            }
            List<string> lstRecord = videoPlayListToRecords(list, isOther);
            object obj = lstRecord;
            WaitBox.Show("正在导出到Csv..", doExport2Csv, obj);
            if (DialogResult.Yes == XtraMessageBox.Show("Csv文件保存完毕，是否现在打开?", "打开文件", MessageBoxButtons.YesNo))  //添加了自动打开文件的提示
            {
                try
                {
                    System.Diagnostics.Process.Start(fileName);
                }
                catch
                {
                    MessageBox.Show("打开失败!\r\n文件名:" + fileName);
                }
            }
        }
        private static void doExport2Csv(object objValue)
        {
            try
            {
                FileStream fileStream = new System.IO.FileStream(fileName, System.IO.FileMode.CreateNew, System.IO.FileAccess.Write, System.IO.FileShare.Read);
                StreamWriter streamWriter = new System.IO.StreamWriter(fileStream, Encoding.UTF8);
                if (objValue is List<string>)
                {
                    List<string> record = objValue as List<string>;
                    for (int i = 0; i < record.Count; i++)
                    {
                        streamWriter.WriteLine(record[i]);
                        streamWriter.Flush();
                        WaitBox.ProgressPercent = (i * 100 / record.Count);
                    }
                }
                streamWriter.Close();
            }
            catch (Exception e)
            {
                XtraMessageBox.Show("Csv导出出错：" + e.ToString());
            }
            finally
            {
                System.Threading.Thread.Sleep(300);
                WaitBox.Close();
                //GC.Collect();
            }
        }
    }
    /// <summary>
    /// 采样点导出指标单元
    /// </summary>
    class ExportParm
    {
        public ExportParm(int sn, int maxNbIndex, TestPoint tp)
        {
            this.SN = sn;
            fillParm(tp, maxNbIndex);
        }
        /// <summary>
        /// 根据采样点填充指标单元
        /// </summary>
        /// <param name="tp">采样点</param>
        /// <param name="maxNbIndex">最大邻区索引</param>
        private void fillParm(TestPoint tp, int maxNbIndex)
        {
            this.tpTime = tp.DateTime;
            this.FileName = tp.FileName;
            this.Longitude = tp.Longitude;
            this.Latitude = tp.Latitude;
            this.RSRP = (float?)getLteValue(tp, "RSRP");
            this.SINR = (float?)getLteValue(tp, "SINR");
            this.AppSpeed = (int?)getLteValue(tp, "APP_Speed");
            this.AppType = (short?)getLteValue(tp, "APP_type");
            this.TransMode = (short?)getLteValue(tp, "Transmission_Mode");
            this.BLER = (float?)getLteValue(tp, "PDSCH_BLER");
            this.RankIndicator = (short?)getLteValue(tp, "Rank_Indicator");
            this.DLGrantCount = (short?)getLteValue(tp, "PDCCH_DL_Grant_Count");
            this.PrbNumsSlot = (int?)getLteValue(tp, "PDSCH_PRb_Num_slot");
            if (maxNbIndex != -1)
            {
                this.MaxNbCellRsrp = (float?)getLteValueWithIndex(tp, "NCell_RSRP", maxNbIndex);
                this.MaxNbCellSinr = (float?)getLteValueWithIndex(tp, "NCell_SINR", maxNbIndex);
            }
        }

        /// <summary>
        /// 获取LTE或LTEFDD采样点的参数值
        /// </summary>
        /// <param name="tp">采样点</param>
        /// <param name="ParmStr">LTE参数名</param>
        /// <returns></returns>
        protected static object getLteValue(TestPoint tp, string ParmStr)
        {
            string LteParmStr = string.Format("lte_{0}", ParmStr);
            if (tp[LteParmStr] != null)
            {
                return tp[LteParmStr];
            }
            else
            {
                LteParmStr = string.Format("lte_fdd_{0}", ParmStr);
                return tp[LteParmStr];
            }
        }
        /// <summary>
        /// 获取LTE或LTEFDD采样点的参数值(带索引）
        /// </summary>
        /// <param name="tp">采样点</param>
        /// <param name="ParmStr">LTE参数名</param>
        ///  <param name="index">索引</param>
        /// <returns></returns>
        protected static object getLteValueWithIndex(TestPoint tp, string ParmStr, int index)
        {
            string LteParmStr = string.Format("lte_{0}", ParmStr);
            if (tp[LteParmStr] != null)
            {
                return tp[LteParmStr, index];
            }
            else
            {
                LteParmStr = string.Format("lte_fdd_{0}", ParmStr);
                return tp[LteParmStr, index];
            }
        }

        private DateTime tpTime;
        public string FileName
        {
            get;
            private set;
        }
        public int SN
        {
            get;
            private set;
        }
        public string TpTimeStr
        {
            get
            {
                return tpTime.ToString("yy-MM-dd HH:mm:ss.fff");
            }
        }
        /// <summary>
        /// 经度
        /// </summary>
        public double? Longitude
        {
            get;
            private set;
        }
        /// <summary>
        /// 纬度
        /// </summary>
        public double? Latitude
        {
            get;
            private set;
        }
        public float? RSRP
        {
            get;
            private set;
        }
        public float? SINR
        {
            get;
            private set;
        }
        public int? AppSpeed
        {
            get;
            private set;
        }
        public short? AppType
        {
            get;
            private set;
        }
        /// <summary>
        /// 传输模式
        /// </summary>
        public short? TransMode
        {
            get;
            private set;
        }
        /// <summary>
        /// PDSCH_BLER
        /// </summary>
        public float? BLER
        {
            get;
            private set;
        }
        /// <summary>
        /// 单双流
        /// </summary>
        public short? RankIndicator
        {
            get;
            private set;
        }
        /// <summary>
        /// 下行子帧调度个数
        /// </summary>
        public short? DLGrantCount
        {
            get;
            private set;
        }
        /// <summary>
        /// 下行每秒调度PRB个数
        /// </summary>
        public int? PrbNumsSlot
        {
            get;
            private set;
        }
        /// <summary>
        /// 最强邻区RSRP
        /// </summary>
        public float? MaxNbCellRsrp
        {
            get;
            private set;
        }
        /// <summary>
        /// 最强邻区SINR
        /// </summary>
        public float? MaxNbCellSinr
        {
            get;
            private set;
        }
    }
}
