﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.Util;
using DevExpress.XtraEditors;

namespace MasterCom.RAMS.Frame
{
    public partial class InputServerIPDlg : BaseFormStyle
    {
        public InputServerIPDlg()
        {
            InitializeComponent();
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }
        public string GetIPString()
        {
            return iPbox.Text;
        }
        private void btnOK_Click(object sender, EventArgs e)
        {
            if(iPbox.IPaddress.ToString().Equals("0.0.0.0"))
            {
                XtraMessageBox.Show(this, "请输入合法的IP地址!");
                return;
            }
            this.DialogResult = DialogResult.OK;
        }
    }
}