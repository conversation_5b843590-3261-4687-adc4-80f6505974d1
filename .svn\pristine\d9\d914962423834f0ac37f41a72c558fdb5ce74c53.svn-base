﻿using MasterCom.RAMS.Func;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NRHandoverFailForm : MinCloseForm
    {
        public NRHandoverFailForm()
        {
            InitializeComponent();
        }

        public void FillData(List<NRHandoverFailInfo> infoSet)
        {
            MainModel.DTDataManager.Sort();
            MainModel.SelectedEvents.Clear();

            gridControl.DataSource = infoSet;
            gridControl.RefreshDataSource();
            //gridView.BestFitColumns();
        }

        private void miExp2Xls_Click(object sender, EventArgs e)
        {
            MasterCom.Util.ExcelNPOIManager.ExportToExcel(gridView);
        }

        private void gridView_DoubleClick(object sender, EventArgs e)
        {
            NRHandoverFailInfo info = gridView.GetRow(gridView.FocusedRowHandle) as NRHandoverFailInfo;
            if (info == null)
            {
                return;
            }
            MapForm mf = MainModel.MainForm.GetMapForm();
            if (mf == null)
            {
                return;
            }
            mf.GoToView(info.Longitude, info.Latitude, 5000);
        }
    }
}
