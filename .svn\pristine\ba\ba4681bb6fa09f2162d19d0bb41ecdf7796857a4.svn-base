﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Data.SqlClient;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.NOP;

namespace MasterCom.RAMS.NOP
{
    public partial class TaskFlowDiagramForm2017 : TaskFlowDiagramForm
    {
        public TaskFlowDiagramForm2017()
        {
            InitializeComponent();
        }
        public TaskFlowDiagramForm2017(string text)
            : this()
        {
            if (!string.IsNullOrEmpty(text))
            {
                this.Text = text;
            }
        }

        protected override ESResultInfo esResultInfo
        {
            get
            {
                if (taskEventItem == null)
                {
                    return null;
                }
                return taskEventItem.ESResultInfoV2017;
            }
        }

        protected override ProcRelation getProcRelation(int id)
        {
            return ProcRoutineManager2017.Instance.GetRelation(id);
        }
    }

    public class ProcSequenceInfo
    {
        public ProcSequenceInfo(ProcRoutine proc)
        {
            this.Proc = proc;
            this.Name = proc.Name;
            this.Seq = proc.ID.ToString();
        }
        public string Name { get; set; }

        public string Seq { get; set; }

        public ProcRoutine Proc { get; set; }
        public ProcSequenceInfo CreateSeq(ProcRoutine proc)
        {
            ProcSequenceInfo p = new ProcSequenceInfo(proc);
            p.Name = string.Format("{0}->{1}", this.Name, proc.Name);
            p.Seq = string.Format("{0}>{1}", this.Seq, proc.ID);
            return p;
        }
    }
}
