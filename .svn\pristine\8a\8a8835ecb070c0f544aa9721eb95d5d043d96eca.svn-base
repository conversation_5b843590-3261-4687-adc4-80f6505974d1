﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc.ZTFileCompare
{
    public class TestPlan_Beijing
    {
        public string GridName { get; private set; }

        public string CityName { get; private set; }

        public string VoiceDevice { get; private set; }

        public string DataDevice { get; private set; }

        public int STime { get; private set; }

        public DateTime SDateTime
        {
            get
            {
                return JavaDate.GetDateTimeFromMilliseconds(STime * 1000L);
            }
        }

        public int ETime { get; private set; }

        public DateTime EDateTime
        {
            get
            {
                return JavaDate.GetDateTimeFromMilliseconds(ETime * 1000L);
            }
        }

        public string Project { get; private set; }

        public string Comment { get; private set; }

        public bool JudgeFile(FileState fi)
        {
            bool fiInPlan = false;
            if (SDateTime <= fi.TimeFromFileName && fi.TimeFromFileName <= EDateTime
                && (fi.Device.Equals(VoiceDevice) || fi.Device.Equals(DataDevice)))
            {
                fiInPlan = true;
                if (fi.IsExist)
                {
                    filesExist.Add(fi);
                }
                else
                {
                    filesNotExist.Add(fi);
                }
            }
            return fiInPlan;
        }
        public int ShouldExistNum
        {
            get { return ExistNum + NotExistNum; }
        }
        public int ExistNum
        {
            get { return filesExist.Count; }
        }

        public int NotExistNum
        {
            get { return filesNotExist.Count; }
        }

        private readonly List<FileState> filesExist = new List<FileState>();
        public List<FileState> FilesExist
        {
            get { return filesExist; }
        }
        private readonly List<FileState> filesNotExist = new List<FileState>();
        public List<FileState> FilesNotExist
        {
            get { return filesNotExist; }
        }

        public double NotExistRate
        {
            get
            {
                if (ShouldExistNum == 0)
                {
                    return 0;
                }
                return Math.Round(NotExistNum * 100.0 / ShouldExistNum, 2);
            }
        }

        //string selSql = "select [gridname],[cityname],[voicedevice],[datadevice],[stime],[etime],[project],[comment] from tb_testplan_info where ";
        public TestPlan_Beijing(Content content)
        {
            this.GridName = content.GetParamString();
            this.CityName = content.GetParamString();
            this.VoiceDevice = content.GetParamString();
            this.DataDevice = content.GetParamString();
            this.STime = content.GetParamInt();
            this.ETime = content.GetParamInt();
            this.Project = content.GetParamString();
            this.Comment = content.GetParamString();
        }


    }
}
