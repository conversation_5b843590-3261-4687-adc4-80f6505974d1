﻿using MasterCom.RAMS.Chris.Util;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Reflection;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class VolteMosWithRtpEvtAnaBase : DIYAnalyseByFileBackgroundBase
    {
        protected static readonly object lockObj = new object();
        readonly List<int> rtpEvtIds = new List<int> { 10313, 10314, 10315 };
        float mosMaxGate = 3.0f;
        float mosMinGate = 0;

        readonly List<VolteMosAndRtpItem> volteMosAndRtpList = new List<VolteMosAndRtpItem>();
        readonly List<VolteRtpAndMosItem> volteRtpAndMosList = new List<VolteRtpAndMosItem>();
        readonly Dictionary<string, VolteMosAndRtpSum> sumInfoDic = new Dictionary<string, VolteMosAndRtpSum>();
        VolteMosAndRtpSum sumInfo_Sum;
        protected VolteMosWithRtpEvtAnaBase()
            : base(MainModel.GetInstance())
        {
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_TDD_VOLTE);
            ServiceTypes.Add(ServiceType.LTE_FDD_VOLTE);
            ServiceTypes.Add(ServiceType.SER_LTE_TDD_VIDEO_VOLTE);
            ServiceTypes.Add(ServiceType.SER_LTE_FDD_VIDEO_VOLTE);

            Columns = new List<string>();
            Columns.Add("lte_TAC");
            Columns.Add("lte_ECI");
            Columns.Add("lte_RSRP");
            Columns.Add("lte_SINR");
            Columns.Add("lte_EARFCN");
            Columns.Add("lte_PCI");
            Columns.Add("lte_PESQMos");
            Columns.Add("lte_POLQA_Score_SWB");
            Columns.Add("mode");
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 27000, 27019, this.Name);
        }

        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }
            VolteMosWithRtpSettingDlg dlg = new VolteMosWithRtpSettingDlg(mosMaxGate, mosMinGate);
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                dlg.GetCondition(out mosMaxGate, out mosMinGate);
                return true;
            }
            return false;
        }

        protected override void clearDataBeforeAnalyseFiles()
        {
            sumInfo_Sum = new VolteMosAndRtpSum(mosMinGate, mosMaxGate);
            volteMosAndRtpList.Clear();
            volteRtpAndMosList.Clear();
            sumInfoDic.Clear();

            int startIdx = (int)(mosMinGate * 10);
            for (int i = startIdx; i <= mosMaxGate * 10; i++)
            {
                float d = (float)i / 10;

                VolteMosAndRtpSum item = new VolteMosAndRtpSum(d, (float)(d + 0.1));
                sumInfoDic.Add(item.MosValueDes, item);
            }
        }

        protected override void doStatWithQuery()
        {
            try
            {
                foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
                {
                    List<RtpEvent> rtpValidEvts = new List<RtpEvent>();
                    List<VolteMosTp> mosTps_All = new List<VolteMosTp>();
                    List<VolteMosTp> mosTps_Weak = new List<VolteMosTp>();
                    foreach (Event evt in file.Events)
                    {
                        if (rtpEvtIds.Contains(evt.ID))
                        {
                            rtpValidEvts.Add(new RtpEvent(evt));
                        }
                    }

                    addMosTps(file, mosTps_All, mosTps_Weak);

                    int evtIdx = 0;
                    foreach (VolteMosTp mosTp in mosTps_Weak)
                    {
                        VolteMosAndRtpItem item = getMosWithRtpInfo(rtpValidEvts, mosTp, ref evtIdx);
                        item.GetSumInfo();
                        item.SN = volteMosAndRtpList.Count + 1;
                        volteMosAndRtpList.Add(item);

                        float mosValue = (float)Math.Floor(item.MosValue * 10) / 10;
                        string strKey = VolteMosAndRtpSum.GetMosValueDes(mosValue, (float)(mosValue + 0.1));
                        VolteMosAndRtpSum sumInfo;
                        if (sumInfoDic.TryGetValue(strKey, out sumInfo))
                        {
                            sumInfo.AddInfo(item);
                        }
                    }

                    int mosTpIdx = 0;
                    foreach (RtpEvent rtpEvt in rtpValidEvts)
                    {
                        VolteRtpAndMosItem item = getRtpWithMosInfo(mosTps_All, rtpEvt, ref mosTpIdx);
                        item.GetSumInfo();
                        item.SN = volteRtpAndMosList.Count + 1;
                        volteRtpAndMosList.Add(item);
                    }
                }
            }
            catch (Exception ee)
            {
                System.Windows.Forms.MessageBox.Show(ee.Message + Environment.NewLine + ee.Source + Environment.NewLine + ee.StackTrace);
            }
        }

        private void addMosTps(DTFileDataManager file, List<VolteMosTp> mosTps_All, List<VolteMosTp> mosTps_Weak)
        {
            string mosParamName = "";
            foreach (TestPoint tp in file.TestPoints)
            {
                if (!getMOSParamName(tp, ref mosParamName))
                {
                    continue;
                }
                float? mos = (float?)tp[mosParamName];
                if (mos != null && mos > 0)
                {
                    VolteMosTp mosTp = new VolteMosTp(tp, (float)mos);
                    mosTps_All.Add(mosTp);

                    if (mos <= mosMaxGate && mos >= mosMinGate)
                    {
                        mosTps_Weak.Add(mosTp);
                    }
                }
            }
        }

        private VolteMosAndRtpItem getMosWithRtpInfo(List<RtpEvent> rtpValidEvts, VolteMosTp mosTp
            , ref int evtIdx)
        {
            VolteMosAndRtpItem item = new VolteMosAndRtpItem(mosTp);

            List<RtpEvent> evts = getMosDurationRtpEvt(rtpValidEvts, mosTp.MosPeriod, ref evtIdx);
            foreach (RtpEvent evt in evts)
            {
                item.AddRtpEvt(evt);
            }
            return item;
        }
        private List<RtpEvent> getMosDurationRtpEvt(List<RtpEvent> evts, TimePeriod mosPeriod, ref int index)
        {
            if (index > 0)
            {
                index -= 1;
            }

            List<RtpEvent> ret = new List<RtpEvent>();
            for (; index < evts.Count; index++)
            {
                RtpEvent e = evts[index];
                if (e.EndTime > mosPeriod.BeginTime && e.BeginTime < mosPeriod.EndTime)
                {
                    ret.Add(e);
                }
                else if (e.BeginTime >= mosPeriod.EndTime)
                {
                    break;
                }
            }
            return ret;
        }


        private VolteRtpAndMosItem getRtpWithMosInfo(List<VolteMosTp> rtpValidTps, RtpEvent rtpEvt, ref int tpIdx)
        {
            VolteRtpAndMosItem item = new VolteRtpAndMosItem(rtpEvt);

            List<VolteMosTp> tps = getRtpDurationMosTp(rtpValidTps, rtpEvt.EvtPeriod, ref tpIdx);
            foreach (VolteMosTp tp in tps)
            {
                item.AddVolteTp(tp);
            }
            return item;
        }
        private List<VolteMosTp> getRtpDurationMosTp(List<VolteMosTp> tps, TimePeriod mosPeriod, ref int index)
        {
            if (index > 0)
            {
                index -= 1;
            }

            List<VolteMosTp> ret = new List<VolteMosTp>();
            for (; index < tps.Count; index++)
            {
                VolteMosTp e = tps[index];
                if (e.EndTime > mosPeriod.BeginTime && e.BeginTime < mosPeriod.EndTime)
                {
                    ret.Add(e);
                }
                else if (e.BeginTime >= mosPeriod.EndTime)
                {
                    break;
                }
            }
            return ret;
        }
        protected virtual bool getMOSParamName(TestPoint tp, ref string mosParamName)
        {
            if (mosParamName == "")
            {
                float? pesq = (float?)tp["lte_PESQMos"];
                float? polqa = (float?)tp["lte_POLQA_Score_SWB"];
                if (pesq != null && pesq > 0 && pesq <= 5)
                {
                    mosParamName = "lte_PESQMos";
                }
                else if (polqa != null && polqa > 0 && polqa <= 5)
                {
                    mosParamName = "lte_POLQA_Score_SWB";
                }
                else
                {
                    return false;
                }
            }
            return true;
        }

        protected override void doSomethingAfterAnalyseFiles()
        {
            foreach (VolteMosAndRtpSum sumItem in sumInfoDic.Values)
            {
                sumInfo_Sum.Merge(sumItem);
            }
        }
        protected override void fireShowForm()
        {
            if (volteMosAndRtpList.Count == 0)
            {
                System.Windows.Forms.MessageBox.Show("没有符合条件的信息！");
                return;
            }
            VolteMosWithRtpAnaResultForm frm = MainModel.CreateResultForm(typeof(VolteMosWithRtpAnaResultForm)) as VolteMosWithRtpAnaResultForm;
            frm.FillData(volteMosAndRtpList, volteRtpAndMosList, sumInfoDic, sumInfo_Sum);
            frm.Owner = MainModel.MainForm;
            frm.Visible = true;
            frm.BringToFront();
        }
    }

    public class VolteMosWithRtpEvtAnaBase_FDD : VolteMosWithRtpEvtAnaBase
    {
        protected VolteMosWithRtpEvtAnaBase_FDD()
            : base()
        {
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_FDD_VOLTE);
            ServiceTypes.Add(ServiceType.SER_LTE_FDD_VIDEO_VOLTE);

            Columns = new List<string>();
            Columns.Add("lte_fdd_TAC");
            Columns.Add("lte_fdd_ECI");
            Columns.Add("lte_fdd_RSRP");
            Columns.Add("lte_fdd_SINR");
            Columns.Add("lte_fdd_EARFCN");
            Columns.Add("lte_fdd_PCI");
            Columns.Add("LAC");
            Columns.Add("CI");
            Columns.Add("BSIC");
            Columns.Add("BCCH");
            Columns.Add("lte_fdd_PESQMos");
            Columns.Add("lte_fdd_POLQA_Score_SWB");
            Columns.Add("mode");
            Columns.Add("lte_fdd_gsm_DM_RxLevSub");
            Columns.Add("lte_fdd_gsm_DM_RxQualSub");
            Columns.Add("lte_fdd_gsm_SC_LAC");
            Columns.Add("lte_fdd_gsm_SC_CI");
            Columns.Add("lte_fdd_gsm_SC_BCCH");
            Columns.Add("lte_fdd_gsm_SC_BSIC");
            Columns.Add("lte_fdd_gsm_NC_RxLev");
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 30000, 30024, this.Name);
        }
        protected override bool getMOSParamName(TestPoint tp, ref string mosParamName)
        {
            if (mosParamName == "")
            {
                float? pesq = (float?)tp["lte_fdd_PESQMos"];
                float? polqa = (float?)tp["lte_fdd_POLQA_Score_SWB"];
                if (pesq != null && pesq > 0 && pesq <= 5)
                {
                    mosParamName = "lte_fdd_PESQMos";
                }
                else if (polqa != null && polqa > 0 && polqa <= 5)
                {
                    mosParamName = "lte_fdd_POLQA_Score_SWB";
                }
                else
                {
                    return false;
                }
            }
            return true;
        }
    }
}
