﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Stat;

namespace MasterCom.RAMS.KPI_Statistics
{
    public partial class TestPlanReportPickerDlg : BaseDialog
    {

        public List<string> list_TestPlan { get; set; }
        private List<string> list_NoFilter = new List<string>();

        public TestPlanReportPickerDlg(List<string> testPlans)
        {
            InitializeComponent();
            reportFilter.initReportPicker();
            initLB(testPlans);
        }

        private void initLB(List<string> testPlans)
        {
            lB_TestPlans.HorizontalScrollbar = true;
            lB_SelectedTestPlans.HorizontalScrollbar = true;
            lB_TestPlans.SelectionMode = SelectionMode.MultiExtended;
            lB_SelectedTestPlans.SelectionMode = SelectionMode.MultiExtended;

            foreach (string testPlan in testPlans)
            {
                lB_TestPlans.Items.Add(testPlan);
            }
            refreshLab();
        }

        private void lnkReloadRpt_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            KPIReportManager.Instance.Init();
            reportFilter.initReportPicker();
        }

        public ReportStyle Report
        {
            get
            {
                ReportStyle rpt = reportFilter.SelectedReport;
                return rpt;
            }
        }

        public bool DisplayRounds
        {
            get { return pnlRound.Visible; }
            set
            {
                pnlRound.Visible = value;
                if (value && chkCbxRound.Properties.Items.Count == 0)
                {
                    for (int i = 1; i < 31; i++)
                    {
                        chkCbxRound.Properties.Items.Add(i, string.Format("第{0}轮", i));
                    }
                }
            }
        }

        public List<int> Rounds
        {
            get
            {
                List<int> rounds = new List<int>();
                foreach (DevExpress.XtraEditors.Controls.CheckedListBoxItem
 item in chkCbxRound.Properties.Items)
                {
                    if (item.CheckState == CheckState.Checked)
                    {
                        rounds.Add((int)item.Value);
                    }
                }
                return rounds;
            }
        }

        public bool IsQueryAllParams
        {
            get { return chkAllParam.Checked; }
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            ReportStyle rpt = reportFilter.SelectedReport;
            if (rpt == null)
            {
                MessageBox.Show("请选择报表！");
                return;
            }
            object item = chkCbxRound.Properties.GetCheckedItems();
            if (DisplayRounds && (item == null || item.ToString() == string.Empty))
            {
                MessageBox.Show("请至少选择一轮测试");
                return;
            }
            list_TestPlan = new List<string>();
            foreach (string testPlan in lB_SelectedTestPlans.Items)
            {
                list_TestPlan.Add(testPlan);
            }

            if (list_TestPlan.Count <= 0)
            {
                MessageBox.Show("请至少选择一个测试计划");
                return;
            }
            DialogResult = DialogResult.OK;
        }

        private void dgv_TestPlan_RowPostPaint(object sender, DataGridViewRowPostPaintEventArgs e)
        {
            var dgv = sender as DataGridView;
            if (dgv != null)
            {
                Rectangle rect = new Rectangle(e.RowBounds.Location.X, e.RowBounds.Location.Y, dgv.RowHeadersWidth - 4, e.RowBounds.Height);
                TextRenderer.DrawText(e.Graphics, (e.RowIndex + 1).ToString(), dgv.RowHeadersDefaultCellStyle.Font, rect, dgv.RowHeadersDefaultCellStyle.ForeColor, TextFormatFlags.VerticalCenter | TextFormatFlags.Right);

            }
        }


        private void tBoxSelect_TextChanged(object sender, EventArgs e)
        {
            if (tBoxSelect.Text == string.Empty)
            {
                string[] list = new string[list_NoFilter.Count];
                list_NoFilter.CopyTo(list, 0);
                for (int i = 0; i < list.Length; i++)
                {
                    lB_TestPlans.Items.Add(list[i]);
                    list_NoFilter.Remove(list[i]);
                }
            }
            else
            {
                string[] list = new string[lB_TestPlans.Items.Count];
                lB_TestPlans.Items.CopyTo(list, 0);
                for (int i = 0; i < list.Length; i++)
                {
                    if (list[i].IndexOf(tBoxSelect.Text) < 0)
                    {
                        list_NoFilter.Add(list[i]);
                        lB_TestPlans.Items.Remove(list[i]);
                    }
                }

                list = new string[list_NoFilter.Count];
                list_NoFilter.CopyTo(list, 0);
                for (int i = 0; i < list.Length; i++)
                {
                    if (list[i].IndexOf(tBoxSelect.Text) >= 0)
                    {
                        lB_TestPlans.Items.Add(list[i]);
                        list_NoFilter.Remove(list[i]);
                    }
                }
            }
            refreshLab();
        }

        private void btnToSelect_Click(object sender, EventArgs e)
        {
            List<string> list = new List<string>();
            foreach (string item in lB_TestPlans.SelectedItems)
            {
                list.Add(item);
            }
            foreach (string item in list)
            {
                lB_TestPlans.Items.Remove(item);
                lB_SelectedTestPlans.Items.Add(item);
            }
            refreshLab();
        }

        private void btnToNoSelect_Click(object sender, EventArgs e)
        {
            List<string> list = new List<string>();
            foreach (string item in lB_SelectedTestPlans.SelectedItems)
            {
                list.Add(item);
            }
            foreach (string item in list)
            {
                lB_SelectedTestPlans.Items.Remove(item);
                lB_TestPlans.Items.Add(item);
            }
            refreshLab();
        }

        private void btnToSelectAll_Click(object sender, EventArgs e)
        {
            List<string> list = new List<string>();
            foreach (string item in lB_TestPlans.Items)
            {
                list.Add(item);
            }
            foreach (string item in list)
            {
                lB_TestPlans.Items.Remove(item);
                lB_SelectedTestPlans.Items.Add(item);
            }
            refreshLab();
        }

        private void btnToNoSelectAll_Click(object sender, EventArgs e)
        {
            List<string> list = new List<string>();
            foreach (string item in lB_SelectedTestPlans.Items)
            {
                list.Add(item);
            }
            foreach (string item in list)
            {
                lB_SelectedTestPlans.Items.Remove(item);
                lB_TestPlans.Items.Add(item);
            }
            refreshLab();
        }

        private void lB_TestPlans_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            if (lB_TestPlans.SelectedItem == null)
                return;
            string item = lB_TestPlans.SelectedItem.ToString();
            if (item != null)
            {
                lB_TestPlans.Items.Remove(item);
                lB_SelectedTestPlans.Items.Add(item);
            }
            refreshLab();
        }

        private void lB_SelectedTestPlans_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            if (lB_SelectedTestPlans.SelectedItem == null)
                return;
            string item = lB_SelectedTestPlans.SelectedItem.ToString();
            if (item != null)
            {
                lB_SelectedTestPlans.Items.Remove(item);
                lB_TestPlans.Items.Add(item);
            }
            refreshLab();
        }

        private void gbx_TestPlan_Paint(object sender, PaintEventArgs e)
        {
            e.Graphics.Clear(gbx_TestPlan.BackColor);
            e.Graphics.DrawString(gbx_TestPlan.Text, gbx_TestPlan.Font, Brushes.Black, 10, 1);
            e.Graphics.DrawLine(Pens.Black, 1, 7, 8, 7);
            e.Graphics.DrawLine(Pens.Black, e.Graphics.MeasureString(gbx_TestPlan.Text, gbx_TestPlan.Font).Width + 8, 7, gbx_TestPlan.Width - 2, 7);
            e.Graphics.DrawLine(Pens.Black, 1, 7, 1, gbx_TestPlan.Height - 2);
            e.Graphics.DrawLine(Pens.Black, 1, gbx_TestPlan.Height - 2, gbx_TestPlan.Width - 2, gbx_TestPlan.Height - 2);
            e.Graphics.DrawLine(Pens.Black, gbx_TestPlan.Width - 2, 7, gbx_TestPlan.Width - 2, gbx_TestPlan.Height - 2);
        }

        private void refreshLab()
        {
            lab_TestPlans.Text = "可选择测试计划（" + lB_TestPlans.Items.Count.ToString() + "）";
            lab_SelectedTestPlans.Text = "已选择测试计划（" + lB_SelectedTestPlans.Items.Count.ToString() + "）";
        }

    }
}
