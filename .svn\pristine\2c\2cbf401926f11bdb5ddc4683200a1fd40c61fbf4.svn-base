﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.CQT
{
    public partial class CQTGSMFailuresQueryForm : DevExpress.XtraEditors.XtraForm
    {
        public CQTGSMFailuresQueryForm()
        {
            InitializeComponent();
        }

        public void getSelect(out double num0_4, out  int numganzhi, out int numchixu)
        {
            num0_4 = double.Parse(spinEdit1.Value.ToString());
            numganzhi = Convert.ToInt32(spinEdit2.Value);
            numchixu = Convert.ToInt32(spinEdit3.Value);
        }

        private void simpleButton1_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void simpleButton2_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

       
    }
}