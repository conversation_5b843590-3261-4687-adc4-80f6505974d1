﻿using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc.ZTHighReverseFlowCoverage._45GHighReverseFlowCoverage
{
    /// <summary>
    /// 初始化编号表,没有则创建,有则备份
    /// </summary>
    public class DiyInitSerialNumber : DiySqlMultiNonQuery
    {
        public string ErrMsg { get; protected set; }

        public DiyInitSerialNumber()
        {
            MainDB = true;
        }

        protected override void query()
        {
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, userName, password, dbid > 0 ? dbid : MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrMsg = "连接服务器失败!";
                    return;
                }
                WaitBox.Text = $"正在向数据库导入{Name}数据......";
                queryInThread(clientProxy);
            }
            catch (Exception ee)
            {
                ErrMsg = ee.Message + ee.Source + ee.StackTrace;
                MessageBox.Show(ErrorInfo);
            }
            finally
            {
                clientProxy.Close();
            }
        }

        protected override void queryInThread(object o)
        {
            base.queryInThread(o);
            System.Threading.Thread.Sleep(200);
            WaitBox.Text = $"导入{Name}完毕.....";
        }

        protected override string getSqlTextString()
        {
            StringBuilder strb = new StringBuilder();
            string tableName = DiyQuerySerialNumber.TableName;
            string bcpTableName = $"{tableName}_{DateTime.Now:yyyyMMdd}";

            strb.Append($@"IF NOT EXISTS(SELECT 1 FROM sysobjects WHERE type = 'U' AND name = '{tableName}')
                BEGIN
                    CREATE TABLE {tableName} (
                        [id]            INT,
                        [city]          VARCHAR(30), 
                        [cityid]        INT,
                        [enodebid]      INT,
                        [serialnumber]  VARCHAR(50), 
                        [longitude]     INT,
                        [latitude]      INT
                    );
                END
            ");
            strb.Append($@"IF EXISTS(SELECT 1 FROM sysobjects WHERE type = 'U' AND name = '{bcpTableName}')
                BEGIN
                    drop table {bcpTableName}
                END
                select * into {bcpTableName} from {tableName}
            ");

            return strb.ToString();
        }
    }

}
