﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;


namespace MasterCom.RAMS.ZTFunc
{
    /// <summary>
    /// 测试数据异常情况
    /// </summary>
    class TestDetailQuery : DIYSQLBase
    {
        /// <summary>
        /// SQL查询语句
        /// </summary>
        private readonly bool IsTDIn = false;
        private readonly int lac;
        private readonly int ci;
        public TestDetailQuery(MainModel mainModel, bool IsTDIn,int lac,int ci)
            : base(mainModel)
        {
            this.IsTDIn = IsTDIn;
            this.lac = lac;
            this.ci = ci;
        }

        protected override string getSqlTextString()
        {
            if (IsTDIn)
            {
                string statement = "select strfilename,tmdat,ilac,ici,strtype from tb_para_utrancell_event_info where ilac = #lac and ici = #ci ";
                statement = statement.Replace("#lac", lac.ToString());
                statement = statement.Replace("#ci", ci.ToString());
                return statement;
            }
            else
            {
                string statement = "select strfilename,tmdat,ilac,ici,strtype from tb_para_cell_event_info where ilac = #lac and ici = #ci ";
                statement = statement.Replace("#lac", lac.ToString());
                statement = statement.Replace("#ci", ci.ToString());
                return statement;
                //"tb_auto_gridroad_info as b where a.isample= b.isampleid";
            }
        }
        
        public List<TestDetailInf> CellcoverinfoList { get; set; } = new List<TestDetailInf>();

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[5];
            rType[0] = E_VType.E_String;
            rType[1] = E_VType.E_String;
            rType[2] = E_VType.E_Int;
            rType[3] = E_VType.E_Int;
            rType[4] = E_VType.E_String;
            return rType;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    TestDetailInf info = new TestDetailInf();
                    info.FileName = package.Content.GetParamString();
                    info.Tmdat = package.Content.GetParamString();
                    info.LAC = package.Content.GetParamInt();
                    info.CI = package.Content.GetParamInt();
                    info.ProbleName = package.Content.GetParamString();
                    CellcoverinfoList.Add(info);
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                if (Math.Log(index++) * (100000 / 10000) > WaitBox.ProgressPercent)
                {
                    progress++;
                    if (progress > 95)
                    {
                        progress = 5;
                        index = 0;
                    }
                    if (progress % 5 == 0)
                    {
                        WaitBox.ProgressPercent = progress;
                    }
                }
            }
        }

        public override string Name
        {
            get { return "DiySqlQueryCellCoverage"; }
        }
    }
}
