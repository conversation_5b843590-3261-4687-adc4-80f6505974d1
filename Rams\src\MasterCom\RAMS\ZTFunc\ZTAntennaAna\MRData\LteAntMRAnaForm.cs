﻿using MasterCom.RAMS.Func;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using DevExpress.XtraCharts;
using System.Reflection;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class LteAntMRAnaForm : MinCloseForm
    {
        MapForm mapForm;
        public LteAntMRAnaForm(MainModel mainModel)
            : base(mainModel)
        {
            InitializeComponent();
            rtbDesc.Text = LteScanAntennaForm.WriteMRDesc();
            this.mapForm = MainModel.MainForm.GetMapForm();
        }

        protected LteAntMRAnaForm()
        {
        }

        public List<List<NPOIRow>> nrDatasList { get; set; }
        public List<string> sheetNames { get; set; }
        protected int iPointIndex = 0;
        protected int iTagKey = 0;
        private Dictionary<string, ZTLteAntMRAna.CellMRData> DicCellParaData = new Dictionary<string, ZTLteAntMRAna.CellMRData>();
        private Dictionary<int, List<string>> enodebDic = new Dictionary<int, List<string>>();

        /// <summary>
        /// 数据初始化，加载前200个小区
        /// </summary>
        public void FillData(List<ZTLteAntMRAna.CellMRData> listCellParaData)
        {
            iTagKey = 3;//小区名称
            foreach (ZTLteAntMRAna.CellMRData cellPara in listCellParaData)
            {
                if (!DicCellParaData.ContainsKey(cellPara.cellname))
                    DicCellParaData.Add(cellPara.cellname, cellPara);

                int iEnodebId = cellPara.antCfg.iEnodebID;
                List<string> cellList;
                if (!enodebDic.TryGetValue(iEnodebId,out cellList))
                    cellList = new List<string>();
                if(!cellList.Contains(cellPara.cellname))
                    cellList.Add(cellPara.cellname);
                enodebDic[iEnodebId] = cellList;
            }

            labNum.Text = listCellParaData.Count.ToString();
            int iPage = listCellParaData.Count % 200 > 0 ? listCellParaData.Count / 200 + 1 : listCellParaData.Count / 200;
            labPage.Text = iPage.ToString();

            dataGridViewCell.Rows.Clear();//小区级
            int rowCellAt = 0;
            foreach (NPOIRow rowData in nrDatasList[0])
            {
                if (rowCellAt == 0)
                {
                    intiDataViewColumn(dataGridViewCell, rowData.cellValues);
                    rowCellAt++;
                    continue;
                }
                if (rowCellAt > 200)
                    break;
                initDataRow(dataGridViewCell, rowData);
                rowCellAt++;
            }

            txtPage.Text = "1";
        }

        /// <summary>
        /// 按小区模糊查找，前200个小区
        /// </summary>
        protected void FillData(string strCellName)
        {
            dataGridViewCell.Rows.Clear();//小区级
            int rowCellAt = 0;
            foreach (NPOIRow row in nrDatasList[0])
            {
                if (nrDatasList[0][0].cellValues[0].ToString() == row.cellValues[0].ToString())
                    continue;

                if (strCellName != "" && row.cellValues[iTagKey].ToString().IndexOf(strCellName) < 0)
                    continue;

                if (rowCellAt >= 200)
                    break;

                initDataRow(dataGridViewCell, row);
                rowCellAt++;
            }
        }

        /// <summary>
        /// 按页数查找
        /// </summary>
        protected void FillData(int iPage)
        {
            dataGridViewCell.Rows.Clear();//小区级
            int iCount = -1;
            int rowCellAt = 0;
            foreach (NPOIRow rowData in nrDatasList[0])
            {
                if (nrDatasList[0][0].cellValues[0].ToString() == rowData.cellValues[0].ToString())
                    continue;

                iCount++;
                if (iCount / 200 != iPage)
                    continue;

                initDataRow(dataGridViewCell, rowData);
                rowCellAt++;
            }
        }

        /// <summary>
        /// 初始化数据列头名称
        /// </summary>
        protected void intiDataViewColumn(DataGridView dataGridView, List<object> objs)
        {
            dataGridView.Columns.Clear();
            int idx = 1;
            foreach (object obj in objs)
            {
                dataGridView.Columns.Add(idx++.ToString(), obj.ToString());
            }
        }

        /// <summary>
        /// 数据赋赋值
        /// </summary>
        protected void initDataRow(DataGridView datatGridView, NPOIRow nop)
        {
            DataGridViewRow row = new DataGridViewRow();
            row.Tag = nop.cellValues[iTagKey];//键值
            foreach (object obj in nop.cellValues)
            {
                DataGridViewTextBoxCell boxcell = new DataGridViewTextBoxCell();
                boxcell.Value = obj.ToString();
                row.Cells.Add(boxcell);
            }
            datatGridView.Rows.Add(row);
        }

        ZTLteAntMRAna.CellMRData data;
        private void miShowChart_Click(object sender, EventArgs e)
        {
            string cellname = dataGridViewCell.SelectedRows[0].Tag as string;
            if (!DicCellParaData.TryGetValue(cellname, out data))
                return;
            
            if (data != null)
            {
                fillCheckBox(data.antCfg.iEnodebID);

                groupControl4.Text = string.Format("MR测量项数据图表({0})", data.cellname);
                this.xtraTabControl1.SelectedTabPageIndex = 2;
                //统计图表
                DrawPowerTableSeries(data.cellMrData.lteMRPowerHeadRoomItem.dataValue);
                DrawRsrpTableSeries(data.cellMrData.lteMRRsrpItem.dataValue);
                DrawAoaTableSeries(data.cellMrData.lteMRAoaItem.dataValue);
                DrawSinrTableSeries(data.cellMrData.lteMRSinrUlItem.dataValue);
                DrawTATableSeries(data.cellMrData.lteMRTaItem.dataValue);
                //小区级雷达图
                drawCellMRRadarSeries();
                drawCellCoverRadarSeries(data.maxTaArray);
                //站点级雷达图
                drawEnodeBCoverRadarSeries();
                drawEnodeBMRRadarSeries();
                //多小区匹配
                macthCell(500);
            }
        }

        private void miShowSimulation_Click(object sender, EventArgs e)
        {
            string cellname = dataGridViewCell.SelectedRows[0].Tag as string;
            if (!DicCellParaData.TryGetValue(cellname, out data))
                return;

            if (data != null)
            {
                LTECell lteCell = CellManager.GetInstance().GetLTECellLatest(data.cellname);
                Dictionary<int, List<LongLat>> gisSampleDic = getGisSampleDic(lteCell);

                AntPointLayer antLayer = mapForm.GetLayerBase(typeof(AntPointLayer)) as AntPointLayer;
                if (antLayer != null)
                {
                    if (lteCell != null && lteCell.Antennas != null)
                        MainModel.MainForm.GetMapForm().GoToView(lteCell.Antennas[0].Longitude, lteCell.Antennas[0].Latitude);
                    else
                        MainModel.MainForm.GetMapForm().GoToView(data.dLongitude, data.dLatitude);

                    antLayer.iFunc = 1;
                    antLayer.gisSampleDic = gisSampleDic;
                    antLayer.Invalidate();
                }
            }
        }

        private Dictionary<int, List<LongLat>> getGisSampleDic(LTECell lteCell)
        {
            Dictionary<int, List<LongLat>> gisSampleDic = new Dictionary<int, List<LongLat>>();
            foreach (int iColor in data.aoaTaDic.Keys)
            {
                List<LongLat> longLatList = new List<LongLat>();
                foreach (ZTLteAntMRAna.AntMRAoaTa aoaTa in data.aoaTaDic[iColor])
                {
                    int iDist = ZTAntFuncHelper.calcDistByLteMrTa(aoaTa.iTaId);
                    int iAngle = aoaTa.iAoaId * 5;

                    LongLat cellLongLat = new LongLat();
                    if (lteCell != null && lteCell.Antennas != null)
                    {
                        cellLongLat.fLongitude = (float)lteCell.Antennas[0].Longitude;
                        cellLongLat.fLatitude = (float)lteCell.Antennas[0].Latitude;
                    }
                    else
                    {
                        cellLongLat.fLongitude = (float)data.dLongitude;
                        cellLongLat.fLatitude = (float)data.dLatitude;
                    }
                    LongLat tmpLongLat = ZTAntFuncHelper.calcPointX(iAngle, iDist, cellLongLat);
                    longLatList.Add(tmpLongLat);
                }
                gisSampleDic.Add(iColor, longLatList);
            }

            return gisSampleDic;
        }

        private void miExportWholeExcel_Click(object sender, EventArgs e)
        {
            doExport(nrDatasList, sheetNames[0]);
        }

        private void 导出CSVToolStripMenuItem_Click(object sender, EventArgs e)
        {
            ZTAntFuncHelper.OutputCsvFile(nrDatasList, sheetNames);
        }

        public void doExport(List<List<NPOIRow>> nrDatasList, string strSheetName)
        {
            string fileName;
            if (!ExportResultSecurityHelper.GetExportPermit(FileSimpleTypeHelper.Excel, out fileName))
            {
                return;
            }
            Microsoft.Office.Interop.Excel.Application excel = new Microsoft.Office.Interop.Excel.Application();  //Execl的操作类
            Microsoft.Office.Interop.Excel.Workbook bookDest = excel.Workbooks.Add(Missing.Value);
            Microsoft.Office.Interop.Excel.Worksheet sheetDest = bookDest.Worksheets.Add(Missing.Value, Missing.Value, Missing.Value, Missing.Value) as Microsoft.Office.Interop.Excel.Worksheet;//给工作薄添加一个Sheet   
            sheetDest.Name = strSheetName;
            for (int i = bookDest.Worksheets.Count; i > 1; i--)
            {
                Microsoft.Office.Interop.Excel.Worksheet wt = (Microsoft.Office.Interop.Excel.Worksheet)bookDest.Worksheets[i];
                if (wt.Name != strSheetName)
                {
                    wt.Delete();
                }
            }
            try
            {
                Microsoft.Office.Interop.Excel.Range rngRow = (Microsoft.Office.Interop.Excel.Range)sheetDest.Columns[1, Type.Missing];
                rngRow.UseStandardWidth = 70;
                int idx = 0;
                setExcelRange(sheetDest, "小区状态库信息", 14, ref idx);
                setExcelRange(sheetDest, "小区性能统计", 7, ref idx);
                setExcelRange(sheetDest, "小区测量统计", 4, ref idx);
                excel.Application.Workbooks.Add(true);

                //导入数据行
                foreach (List<NPOIRow> listNPOI in nrDatasList)
                {
                    int row = 2;
                    foreach (NPOIRow npoi in listNPOI)
                    {
                        idx = npoi.cellValues.Count;
                        Microsoft.Office.Interop.Excel.Range cell1ran = sheetDest.get_Range(sheetDest.Cells[row, 1], sheetDest.Cells[row++, idx]);
                        cell1ran.Value2 = npoi.cellValues.ToArray();
                    }
                }
                bookDest.Saved = true;
                bookDest.SaveCopyAs(fileName);//保存
                MessageBox.Show("导出成功！");
            }
            catch (Exception w)
            {
                MessageBox.Show("导出异常:" + w.Message);
            }
            finally
            {
                excel.Quit();
                //GC.Collect();//垃圾回收   
            }
        }

        private void setExcelRange(Microsoft.Office.Interop.Excel.Worksheet sheetDest, string value, int col, ref int idx)
        {
            int row = 1;
            idx += 1;
            int cell1Col = idx;
            idx += col;
            int cell2Col = idx;
            Microsoft.Office.Interop.Excel.Range ran = sheetDest.get_Range(sheetDest.Cells[row, cell1Col], sheetDest.Cells[row, (cell2Col)]);
            ran.Merge(ran.MergeCells);//合并单元格
            ran.HorizontalAlignment = Microsoft.Office.Interop.Excel.XlHAlign.xlHAlignCenter;
            ran.Value2 = value;
        }

        private void xtraTabControl1_SelectedPageChanged(object sender, DevExpress.XtraTab.TabPageChangedEventArgs e)
        {
            if (xtraTabControl1.SelectedTabPageIndex == 0 || xtraTabControl1.SelectedTabPageIndex == 1)
            {
                miShowChart.Visible = true;
                miShowSimulation.Visible = true;
            }
        }

        private void btnSearch_Click(object sender, EventArgs e)
        {
            string strCellName = txtCellName.Text;
            FillData(strCellName);
        }

        private void btnGo_Click(object sender, EventArgs e)
        {
            int iPage = 0;
            if (!int.TryParse(txtPage.Text.ToString(), out iPage))
            {
                iPage = 0;
            }
            iPage -= 1;
            int iCount = DicCellParaData.Count;
            iCount = iCount % 200 > 0 ? iCount / 200 + 1 : iCount / 200;
            if (iPage < 0)
                iPage = 0;
            else if (iPage > iCount - 1)
                iPage = iCount - 1;
            txtPage.Text = (iPage + 1).ToString();
            FillData(iPage);
        }

        private void btnPrevpage_Click(object sender, EventArgs e)
        {
            int iPage = 0;
            if (!int.TryParse(txtPage.Text.ToString(), out iPage))
            {
                iPage = 0;
            }
            iPage -= 1;
            iPage = iPage - 1 >= 0 ? iPage - 1 : iPage;
            txtPage.Text = (iPage + 1).ToString();
            FillData(iPage);
        }

        private void btnNextpage_Click(object sender, EventArgs e)
        {
            int iPage = 0;
            if (!int.TryParse(txtPage.Text.ToString(), out iPage))
            {
                iPage = 0;
            }
            iPage -= 1;
            int iCount = DicCellParaData.Count;
            iCount = iCount % 200 > 0 ? iCount / 200 + 1 : iCount / 200;

            iPage = iPage + 1 >= iCount ? iPage : iPage + 1;
            txtPage.Text = (iPage + 1).ToString();
            FillData(iPage);
        }


        #region MR统计图表绘制

        /// <summary>
        /// 按MR发射功率余量图表
        /// </summary>
        private void DrawPowerTableSeries(int[] modelSeries)
        {
            chartControlPower.Series.Clear();

            Series series = new Series();
            series.ShowInLegend = false;
            series.PointOptions.PointView = PointView.Values;

            SideBySideBarSeriesView sideBySideBarSeriesView = new SideBySideBarSeriesView();
            sideBySideBarSeriesView.BarWidth = 0.1;
            sideBySideBarSeriesView.Border.Visible = false;
            series.View = sideBySideBarSeriesView;

            series.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
            series.Label.Visible = false;

            for (int i = 0; i < 64; i++)
            {
                int iPower = i - 23;
                series.Points.Add(new SeriesPoint(iPower.ToString(), modelSeries[i]));
            }
            iPointIndex = 0;
            chartControlPower.Series.Insert(0, series);

            ((XYDiagram)chartControlPower.Diagram).AxisY.Range.MinValue = 0;
            ((XYDiagram)chartControlPower.Diagram).AxisY.Range.MaxValue = ZTAntFuncHelper.getMaxValue(modelSeries);

            chartControlPower.Focus();
        }

        /// <summary>
        /// 按MR参考信号接收功率余量图表
        /// </summary>
        private void DrawRsrpTableSeries(int[] modelSeries)
        {
            chartControlRsrp.Series.Clear();

            Series series = new Series();
            series.ShowInLegend = false;
            series.PointOptions.PointView = PointView.Values;

            SideBySideBarSeriesView sideBySideBarSeriesView = new SideBySideBarSeriesView();
            sideBySideBarSeriesView.BarWidth = 0.1;
            sideBySideBarSeriesView.Border.Visible = false;
            series.View = sideBySideBarSeriesView;

            series.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
            series.Label.Visible = false;

            for (int i = 0; i < 48; i++)
            {
                int iRsrp = calcRsrpByNum(i);
                series.Points.Add(new SeriesPoint(iRsrp.ToString(), modelSeries[i]));
            }
            iPointIndex = 0;
            chartControlRsrp.Series.Insert(0, series);

            ((XYDiagram)chartControlRsrp.Diagram).AxisY.Range.MinValue = 0;
            ((XYDiagram)chartControlRsrp.Diagram).AxisY.Range.MaxValue = ZTAntFuncHelper.getMaxValue(modelSeries);

            chartControlRsrp.Focus();
        }

        /// <summary>
        /// 按时间差计算RSRP
        /// </summary>
        private int calcRsrpByNum(int iNum)
        {
            int iRsrp = 0;
            if (iNum == 0)
            {
                iRsrp = -120;
            }
            else if (iNum == 1)
            {
                iRsrp = -116;
            }
            else if (iNum < 37)
            {
                iRsrp = iNum - 117;
            }
            else if (iNum < 47)
            {
                iRsrp = (iNum - 37) * 2 + 37 - 116;
            }
            else
            {
                iRsrp = -60;
            }
            return iRsrp;
        }

        /// <summary>
        /// 按MR天线到达角图表
        /// </summary>
        private void DrawAoaTableSeries(int[] modelSeries)
        {
            chartControlAoa.Series.Clear();

            Series series = new Series();
            series.ShowInLegend = false;
            series.PointOptions.PointView = PointView.Values;

            SideBySideBarSeriesView sideBySideBarSeriesView = new SideBySideBarSeriesView();
            sideBySideBarSeriesView.BarWidth = 0.1;
            sideBySideBarSeriesView.Border.Visible = false;
            series.View = sideBySideBarSeriesView;

            series.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
            series.Label.Visible = false;

            for (int i = 0; i < 72; i++)
            {
                int iAoa = (i + 1) * 5;
                series.Points.Add(new SeriesPoint(iAoa.ToString(), modelSeries[i]));
            }
            iPointIndex = 0;
            chartControlAoa.Series.Insert(0, series);

            ((XYDiagram)chartControlAoa.Diagram).AxisY.Range.MinValue = 0;
            ((XYDiagram)chartControlAoa.Diagram).AxisY.Range.MaxValue = ZTAntFuncHelper.getMaxValue(modelSeries);

            chartControlAoa.Focus();
        }

        /// <summary>
        /// 按MR上行信噪比图表
        /// </summary>
        private void DrawSinrTableSeries(int[] modelSeries)
        {
            chartControlSinr.Series.Clear();

            Series series = new Series();
            series.ShowInLegend = false;
            series.PointOptions.PointView = PointView.Values;

            SideBySideBarSeriesView sideBySideBarSeriesView = new SideBySideBarSeriesView();
            sideBySideBarSeriesView.BarWidth = 0.1;
            sideBySideBarSeriesView.Border.Visible = false;
            series.View = sideBySideBarSeriesView;

            series.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
            series.Label.Visible = false;

            for (int i = 0; i < 37; i++)
            {
                int iSinr = i - 11;
                series.Points.Add(new SeriesPoint(iSinr.ToString(), modelSeries[i]));
            }
            iPointIndex = 0;
            chartControlSinr.Series.Insert(0, series);

            ((XYDiagram)chartControlSinr.Diagram).AxisY.Range.MinValue = 0;
            ((XYDiagram)chartControlSinr.Diagram).AxisY.Range.MaxValue = ZTAntFuncHelper.getMaxValue(modelSeries);

            chartControlSinr.Focus();
        }

        /// <summary>
        /// 按MR时间提前量图表
        /// </summary>
        private void DrawTATableSeries(int[] modelSeries)
        {
            chartControlTA.Series.Clear();

            Series series = new Series();
            series.ShowInLegend = false;
            series.PointOptions.PointView = PointView.Values;

            SideBySideBarSeriesView sideBySideBarSeriesView = new SideBySideBarSeriesView();
            sideBySideBarSeriesView.BarWidth = 0.1;
            sideBySideBarSeriesView.Border.Visible = false;
            series.View = sideBySideBarSeriesView;

            series.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
            series.Label.Visible = false;

            for (int i = 0; i < 44; i++)
            {
                int iDist = ZTAntFuncHelper.calcDistByLteMrTa(i);
                series.Points.Add(new SeriesPoint(iDist.ToString(), modelSeries[i]));
            }
            iPointIndex = 0;
            chartControlTA.Series.Insert(0, series);

            ((XYDiagram)chartControlTA.Diagram).AxisY.Range.MinValue = 0;
            ((XYDiagram)chartControlTA.Diagram).AxisY.Range.MaxValue = ZTAntFuncHelper.getMaxValue(modelSeries);

            chartControlTA.Focus();
        }

        /// <summary>
        /// 按MR二维数据绘雷达图-小区级
        /// </summary>
        private void drawCellMRRadarSeries()
        {
            chartCellPoint.Series.Clear();
            int idx = 0;
            int iMaxValue = 0;
            string strCellPart = cbPart.Text;

            foreach (int iColor in data.aoaTaDic.Keys)
            {
                if (ZTAntFuncHelper.checkLegendIsVaild(iColor, strCellPart))
                {
                    AntLegend antLegend = ZTAntFuncHelper.GetMRDataAnaLegend(iColor);
                    Series series = new Series();
                    series.ShowInLegend = true;
                    series.LegendText = antLegend.strLegend;
                    series.PointOptions.PointView = PointView.Values;

                    RadarPointSeriesView pointSeriesView = new RadarPointSeriesView();
                    pointSeriesView.Color = antLegend.colorType;
                    pointSeriesView.PointMarkerOptions.Size = 3;

                    series.View = pointSeriesView;
                    series.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
                    series.Label.Visible = false;

                    foreach (ZTLteAntMRAna.AntMRAoaTa aoaTa in data.aoaTaDic[iColor])
                    {
                        int iDist = ZTAntFuncHelper.calcDistByLteMrTa(aoaTa.iTaId);
                        int n = aoaTa.iAoaId * 5;

                        if (iDist > iMaxValue)
                            iMaxValue = iDist;
                        series.Points.Add(new SeriesPoint(n.ToString(), iDist.ToString()));
                    }

                    chartCellPoint.Series.Insert(idx, series);
                    idx++;
                }
            }

            //增加天线方位角连线
            Series seriesCell = ZTAntFuncHelper.DrawRadarLine(Color.Red,"工参",(int)data.antCfg.方向角,iMaxValue);
            chartCellPoint.Series.Insert(idx, seriesCell);
            idx++;

            Series seriesMR = ZTAntFuncHelper.DrawRadarLine(Color.DarkBlue,"MR",data.iMainDir,iMaxValue);
            chartCellPoint.Series.Insert(idx, seriesMR);

            if (data.aoaTaDic.Count > 0)
            {
                ((RadarDiagram)chartCellPoint.Diagram).AxisY.Range.MinValue = -1;
                ((RadarDiagram)chartCellPoint.Diagram).AxisY.Range.MaxValue = iMaxValue + 300;

                ((RadarDiagram)chartCellPoint.Diagram).AxisX.GridSpacing = 20;

                ((RadarDiagram)chartCellPoint.Diagram).AxisX.Range.SetInternalMinMaxValues(0, 340);
                ((RadarDiagram)chartCellPoint.Diagram).RotationDirection = RadarDiagramRotationDirection.Clockwise;
            }
            else
            {
                Series series = new Series();
                series.ShowInLegend = false;
                series.PointOptions.PointView = PointView.Values;

                RadarLineSeriesView lineSeriesView = new RadarLineSeriesView();
                lineSeriesView.Color = Color.Blue;
                lineSeriesView.LineMarkerOptions.Size = 2;

                series.View = lineSeriesView;
                series.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
                series.Label.Visible = false;
                chartCellPoint.Series.Insert(0, series);
            }
            chartCellPoint.Focus();
        }

        /// <summary>
        /// 按MR二维数据绘雷达图-站点级
        /// </summary>
        private void drawEnodeBMRRadarSeries()
        {
            chartEnodebPoint.Series.Clear();
            int idx = 0;
            int iMaxValue = 0;
            string strCellPart = cbBtsPart.Text;
            
            List<string> cellNameList = new List<string>();
            if (cbCellName.Text == "全部")
            {
                if (data != null && enodebDic.ContainsKey(data.antCfg.iEnodebID))
                    cellNameList = enodebDic[data.antCfg.iEnodebID];
            }
            else
                cellNameList.Add(cbCellName.Text);

            cellNameList.Sort();
            foreach (string cellName in cellNameList)
            {
                if (!DicCellParaData.ContainsKey(cellName))
                    continue;

                ZTLteAntMRAna.CellMRData tmpData = DicCellParaData[cellName];

                Series series = new Series();
                series.ShowInLegend = true;
                series.LegendText = string.Format("Sector={0}", tmpData.antCfg.iSectorID);
                series.PointOptions.PointView = PointView.Values;

                RadarPointSeriesView pointSeriesView = new RadarPointSeriesView();
                pointSeriesView.Color = ZTAntFuncHelper.getCellColor(tmpData.antCfg.iSectorID);
                pointSeriesView.PointMarkerOptions.Size = 3;
                series.View = pointSeriesView;
                series.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
                series.Label.Visible = false;

                foreach (int iColor in tmpData.aoaTaDic.Keys)
                {
                    iMaxValue = addValidLegendPoint(iMaxValue, strCellPart, tmpData, series, iColor);
                }
                chartEnodebPoint.Series.Insert(idx, series);
                idx++;
            }

            if (idx > 0)
            {
                ((RadarDiagram)chartEnodebPoint.Diagram).AxisY.Range.MinValue = -1;
                ((RadarDiagram)chartEnodebPoint.Diagram).AxisY.Range.MaxValue = iMaxValue + 300;
                ((RadarDiagram)chartEnodebPoint.Diagram).AxisX.GridSpacing = 20;
                ((RadarDiagram)chartEnodebPoint.Diagram).AxisX.Range.SetInternalMinMaxValues(0, 340);
                ((RadarDiagram)chartEnodebPoint.Diagram).RotationDirection = RadarDiagramRotationDirection.Clockwise;
            }
            else
            {
                Series series = new Series();
                series.ShowInLegend = false;
                series.PointOptions.PointView = PointView.Values;

                RadarLineSeriesView lineSeriesView = new RadarLineSeriesView();
                lineSeriesView.Color = Color.Blue;
                lineSeriesView.LineMarkerOptions.Size = 2;

                series.View = lineSeriesView;
                series.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
                series.Label.Visible = false;
                chartEnodebPoint.Series.Insert(0, series);
            }
            chartEnodebPoint.Focus();
        }

        private int addValidLegendPoint(int iMaxValue, string strCellPart, ZTLteAntMRAna.CellMRData tmpData, Series series, int iColor)
        {
            if (ZTAntFuncHelper.checkLegendIsVaild(iColor, strCellPart))
            {
                foreach (ZTLteAntMRAna.AntMRAoaTa aoaTa in tmpData.aoaTaDic[iColor])
                {
                    int iDist = ZTAntFuncHelper.calcDistByLteMrTa(aoaTa.iTaId);

                    int n = aoaTa.iAoaId * 5;
                    //if (tmpData.antCfg.strVender == "爱立信" || tmpData.antCfg.strVender == "中兴")
                    //    n = (aoaTa.iAoaId * 5 + tmpData.iMainDir) % 360;

                    if (iDist > iMaxValue)
                        iMaxValue = iDist;
                    series.Points.Add(new SeriesPoint(n.ToString(), iDist.ToString()));
                }
            }

            return iMaxValue;
        }

        /// <summary>
        /// 按MR模拟覆盖图-小区级
        /// </summary>
        private void drawCellCoverRadarSeries(double[] seriesValues)
        {
            chartCellLine.Series.Clear();

            Series series = new Series();
            series.ShowInLegend = false;
            series.PointOptions.PointView = PointView.Values;

            RadarLineSeriesView lineSeriesView = new RadarLineSeriesView();
            lineSeriesView.Color = Color.Blue;
            lineSeriesView.LineMarkerOptions.Size = 2;

            series.View = lineSeriesView;
            series.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
            series.Label.Visible = false;

            for (int i = 0; i < 72; i++)
            {
                int j = i * 5;

                series.Points.Add(new SeriesPoint(j, seriesValues[i]));
            }
            chartCellLine.Series.Insert(0, series);

            ((RadarDiagram)chartCellLine.Diagram).AxisY.Range.MinValue = -1;
            ((RadarDiagram)chartCellLine.Diagram).AxisY.Range.MaxValue = ZTAntFuncHelper.getMaxValue(seriesValues) + 300;
            ((RadarDiagram)chartCellLine.Diagram).AxisX.GridSpacing = 20;
            ((RadarDiagram)chartCellLine.Diagram).AxisX.Range.SetInternalMinMaxValues(0, 340);
            ((RadarDiagram)chartCellLine.Diagram).RotationDirection = RadarDiagramRotationDirection.Clockwise;

            chartCellLine.Focus();
        }

        /// <summary>
        /// 按MR模拟覆盖图-基站级
        /// </summary>
        private void drawEnodeBCoverRadarSeries()
        {
            chartEnodebLine.Series.Clear();
            
            List<string> cellNameList = new List<string>();
            if (cbCellName.Text == "全部")
            {
                if (data != null && enodebDic.ContainsKey(data.antCfg.iEnodebID))
                    cellNameList = enodebDic[data.antCfg.iEnodebID];
            }
            else
                cellNameList.Add(cbCellName.Text);

            int idx = 0;
            double dMaxDist = 0;
            cellNameList.Sort();
            foreach (string cellName in cellNameList)
            {
                if (!DicCellParaData.ContainsKey(cellName))
                    continue;

                ZTLteAntMRAna.CellMRData tmpData = DicCellParaData[cellName];
                Series series = new Series();
                series.ShowInLegend = true;
                series.LegendText = string.Format("Sector={0}", tmpData.antCfg.iSectorID);
                series.PointOptions.PointView = PointView.Values;

                RadarLineSeriesView lineSeriesView = new RadarLineSeriesView();
                lineSeriesView.Color = ZTAntFuncHelper.getCellColor(tmpData.antCfg.iSectorID);
                lineSeriesView.LineMarkerOptions.Size = 2;

                series.View = lineSeriesView;
                series.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
                series.Label.Visible = false;

                string strPart = cbBtsPart.Text;
                double[] seriesValues = getMaxTaArray(tmpData, strPart);

                for (int i = 0; i < 72; i++)
                {
                    int j = i * 5;

                    series.Points.Add(new SeriesPoint(j, seriesValues[i]));
                }
                if (dMaxDist < ZTAntFuncHelper.getMaxValue(seriesValues))
                    dMaxDist = ZTAntFuncHelper.getMaxValue(seriesValues);

                chartEnodebLine.Series.Insert(idx, series);
                idx++;
            }

            if (idx > 0)
            {
                ((RadarDiagram)chartEnodebLine.Diagram).AxisY.Range.MinValue = -1;
                ((RadarDiagram)chartEnodebLine.Diagram).AxisY.Range.MaxValue = dMaxDist + 300;
                ((RadarDiagram)chartEnodebLine.Diagram).AxisX.GridSpacing = 20;
                ((RadarDiagram)chartEnodebLine.Diagram).AxisX.Range.SetInternalMinMaxValues(0, 340);
                ((RadarDiagram)chartEnodebLine.Diagram).RotationDirection = RadarDiagramRotationDirection.Clockwise;
            }
            else
            {
                Series series = new Series();
                series.ShowInLegend = false;
                series.PointOptions.PointView = PointView.Values;

                RadarLineSeriesView lineSeriesView = new RadarLineSeriesView();
                lineSeriesView.Color = Color.Blue;
                lineSeriesView.LineMarkerOptions.Size = 2;

                series.View = lineSeriesView;
                series.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
                series.Label.Visible = false;
                chartEnodebPoint.Series.Insert(0, series);
            }

            chartEnodebLine.Focus();
        }
        #endregion

        #region 分级呈现图层
        /// <summary>
        /// 下拉选择框填值
        /// </summary>
        private void fillCheckBox(int iEnodebId)
        {
            cbPart.Items.Clear();
            cbPart.Text = "全部";
            cbPart.Items.Add("全部");
            cbPart.Items.Add("(0%,50%]");
            cbPart.Items.Add("(0%,70%]");
            cbPart.Items.Add("(0%,80%]");

            cbNPart.Items.Clear();
            cbNPart.Text = "全部";
            cbNPart.Items.Add("全部");
            cbNPart.Items.Add("(0%,50%]");
            cbNPart.Items.Add("(0%,70%]");
            cbNPart.Items.Add("(0%,80%]");

            cbBtsPart.Items.Clear();
            cbBtsPart.Text = "全部";
            cbBtsPart.Items.Add("全部");
            cbBtsPart.Items.Add("(0%,50%]");
            cbBtsPart.Items.Add("(0%,70%]");
            cbBtsPart.Items.Add("(0%,80%]");

            cbCellName.Items.Clear();
            cbCellName.Text = "全部";
            cbCellName.Items.Add("全部");
            if (enodebDic.ContainsKey(iEnodebId))
            {
                List<string> cellNameList = enodebDic[iEnodebId];
                foreach (string cellName in cellNameList)
                {
                    cbCellName.Items.Add(cellName);
                }
            }
        }

        /// <summary>
        /// 重新计算覆盖曲线
        /// </summary>
        private double[] getMaxTaArray(ZTLteAntMRAna.CellMRData mrData, string cellPart)
        {
            List<ZTLteAntMRAna.AntMRAoaTa> mrList = new List<ZTLteAntMRAna.AntMRAoaTa>();
            foreach (int iColor in mrData.aoaTaDic.Keys)
            {
                if (iColor == 2 && cellPart == "(0%,50%]")
                    continue;

                if (iColor == 3 && (cellPart == "(0%,50%]" || cellPart == "(0%,70%]"))
                    continue;

                if (iColor == 4 && (cellPart == "(0%,50%]" || cellPart == "(0%,70%]" || cellPart == "(0%,80%]"))
                    continue;

                mrList.AddRange(mrData.aoaTaDic[iColor]);
            }

            int[,] AnaRttdAoaTmp = new int[44, 72];
            int iNum = mrList.Count;
            for (int i = 0; i < iNum; i++)
            {
                ZTLteAntMRAna.AntMRAoaTa at = mrList[i];
                AnaRttdAoaTmp[at.iTaId, at.iAoaId] = at.iCount;
            }
            ZTLteAntMRAna.AnaRttdAoaArray ary = new ZTLteAntMRAna.AnaRttdAoaArray();
            ary.AnaRttdAoa90 = AnaRttdAoaTmp;
            double[] maxTaArray = ZTLteAntMRAna.getDirMaxTa(ary);

            return maxTaArray;
        }

        private void cbPart_SelectedIndexChanged(object sender, EventArgs e)
        {
            drawCellMRRadarSeries();
            string cellPart = cbPart.Text;
            double[] maxTaArray = getMaxTaArray(data,cellPart);
            drawCellCoverRadarSeries(maxTaArray);
        }

        private void cb_SelectedIndexChanged(object sender, EventArgs e)
        {
            drawEnodeBCoverRadarSeries();
            drawEnodeBMRRadarSeries();
        }
        #endregion

        #region 多小区GIS覆盖曲线绘制

        List<matchCellInfo> mcList = null;
        ColorComboBox cmb_Temp = null;
        //选择行index
        int iDataGridViewRow = 0;
        //选中单元格index
        int iDataGridViewCell = 5;
        private void macthCell(double MaxDistance)
        {
            mcList = new List<matchCellInfo>();
            int idx = 1;
            foreach (string strNCell in DicCellParaData.Keys)
            {
                double distance = MathFuncs.GetDistance(data.dLongitude, data.dLatitude, DicCellParaData[strNCell].dLongitude, DicCellParaData[strNCell].dLatitude);
                if (distance <= MaxDistance && data.cellname != strNCell)
                {
                    matchCellInfo mcInfo = new matchCellInfo();
                    mcInfo.iSN = idx;
                    mcInfo.strCellName = strNCell;
                    mcInfo.dDistance = Math.Round(distance, 2);
                    mcInfo.fDiffAngle = ZTAntFuncHelper.CalcAntDir((int)data.antCfg.方向角, (int)DicCellParaData[strNCell].antCfg.方向角);

                    CellRelate info = new CellRelate();
                    info.DPCellLongitude = data.dLongitude;
                    info.DPCellLatitude = data.dLatitude;
                    info.FPCell_方位角 = data.antCfg.方向角;
                    info.DNCellLongitude = DicCellParaData[strNCell].dLongitude;
                    info.DNCellLatitude = DicCellParaData[strNCell].dLatitude;
                    info.FNCell_方位角 = DicCellParaData[strNCell].antCfg.方向角;
                    info.Flag = 1;
                    mcInfo.strRelation = info.JudgeCellRel(info);
                    mcList.Add(mcInfo);
                    idx++;
                }
            }
            setControl();
            foreach(matchCellInfo mcInfo in mcList)
            {
                DataGridViewRow row = new DataGridViewRow();
                List<object> objs = mcInfo.getRowObjectList();
                foreach (object obj in objs)
                {
                    DataGridViewTextBoxCell boxcell1 = new DataGridViewTextBoxCell();
                    boxcell1.Value = obj.ToString();
                    row.Cells.Add(boxcell1);
                }
                dataGridViewLeft.Rows.Add(row);
            }
            
        }


        #endregion

        #region 多小区设置控件操作
        private void btnOneIn_Click(object sender, EventArgs e)
        {
            if (dataGridViewLeft.SelectedRows.Count == 0)
            {
                MessageBox.Show("请选择小区。", "提示");
                return;
            }
            moveDataGridViewData(dataGridViewLeft, dataGridViewRight, 1, 1, 20);
        }

        private void btnAllIn_Click(object sender, EventArgs e)
        {
            if (dataGridViewLeft.Rows.Count > 0)
            {
                moveDataGridViewData(dataGridViewLeft, dataGridViewRight, 1, 2, 20);
            }
        }

        private void btnOneOut_Click(object sender, EventArgs e)
        {
            if (dataGridViewRight.SelectedRows.Count == 0)
            {
                MessageBox.Show("请选择小区。", "提示");
                return;
            }
            moveDataGridViewData(dataGridViewRight, dataGridViewLeft, 1, 1, 100);

        }

        private void btnAllOut_Click(object sender, EventArgs e)
        {
            if (dataGridViewRight.Rows.Count > 0)
            {
                moveDataGridViewData(dataGridViewRight, dataGridViewLeft, 1, 2, 100);
            }
        }

        private void serBtn_Click(object sender, EventArgs e)
        {
            double maxDis;
            if (DicCellParaData.Count == 0)
                return;
            if (!double.TryParse(tbDistance.Text.ToString(), out maxDis))
            {
                MessageBox.Show("不是有效数字");
                return;
            }
            if (maxDis > 5000)
            {
                maxDis = 500;
                tbDistance.Text = maxDis.ToString();
                MessageBox.Show("最大距离不能超过5000米");
            }
            macthCell(maxDis);
        }

        private void btnDraw_Click(object sender, EventArgs e)
        {
            string strPart = cbNPart.Text;
            Dictionary<string, List<LongLat>> cellLongLatDic = new Dictionary<string, List<LongLat>>();
            Dictionary<string, Color> cellColorDic = new Dictionary<string, Color>();
            int iRowNum = dataGridViewRight.Rows.Count;
            for (int i = 0; i < iRowNum; i++)
            {
                string strCellName = dataGridViewRight.Rows[i].Cells[1].Value.ToString();
                Color color = dataGridViewRight.Rows[i].Cells[5].Style.BackColor;
                if (!cellColorDic.ContainsKey(strCellName))
                    cellColorDic.Add(strCellName, color);
            }

            foreach (string strCellName in cellColorDic.Keys)
            {
                ZTLteAntMRAna.CellMRData mrData;
                if (!DicCellParaData.TryGetValue(strCellName, out mrData))
                    return;

                addCellLongLatDic(strPart, cellLongLatDic, strCellName, mrData);
            }

            string cellname = dataGridViewCell.SelectedRows[0].Tag as string;
            if (!DicCellParaData.TryGetValue(cellname, out data))
                return;

            setLayerData(strPart, cellLongLatDic, cellColorDic);
        }

        private void setLayerData(string strPart, Dictionary<string, List<LongLat>> cellLongLatDic, Dictionary<string, Color> cellColorDic)
        {
            if (data != null)
            {
                LongLat mainLongLat = getCellLongLat(data.cellname);

                double[] maxTaArray = getMaxTaArray(data, strPart);
                int iCount = maxTaArray.Length;
                List<LongLat> longLatList = new List<LongLat>();
                for (int i = 0; i < iCount; i++)
                {
                    int iAngle = i * 5;
                    int iDist = (int)maxTaArray[i];
                    LongLat tmpLongLat = ZTAntFuncHelper.calcPointX(iAngle, iDist, mainLongLat);
                    longLatList.Add(tmpLongLat);
                }
                cellLongLatDic.Add(data.cellname, longLatList);
                cellColorDic.Add(data.cellname, Color.DarkViolet);

                MainModel.SelectedCell = CellManager.GetInstance().GetCellByName(data.cellname);
                MainModel.MainForm.GetMapForm().GoToView(mainLongLat.fLongitude, mainLongLat.fLatitude);

                AntRegionLayer antLayer = mapForm.GetLayerBase(typeof(AntRegionLayer)) as AntRegionLayer;
                if (antLayer != null)
                {
                    antLayer.cellLongLatDic = cellLongLatDic;
                    antLayer.cellColorDic = cellColorDic;
                    antLayer.Invalidate();
                }
            }
        }

        private void addCellLongLatDic(string strPart, Dictionary<string, List<LongLat>> cellLongLatDic, string strCellName, ZTLteAntMRAna.CellMRData mrData)
        {
            if (mrData != null)
            {
                LongLat cellLongLat = getCellLongLat(strCellName);

                double[] maxTaArray = getMaxTaArray(mrData, strPart);
                int iCount = maxTaArray.Length;
                List<LongLat> longLatList = new List<LongLat>();
                for (int i = 0; i < iCount; i++)
                {
                    int iAngle = i * 5;
                    //if (data.antCfg.strVender == "爱立信" || data.antCfg.strVender == "中兴")
                    //    iAngle = ((i * 5) + data.iMainDir) % 360;

                    int iDist = (int)maxTaArray[i];
                    LongLat tmpLongLat = ZTAntFuncHelper.calcPointX(iAngle, iDist, cellLongLat);
                    longLatList.Add(tmpLongLat);
                }
                cellLongLatDic.Add(strCellName, longLatList);
            }
        }

        private LongLat getCellLongLat(string strCellName)
        {
            LongLat cellLongLat = new LongLat();
            LTECell lteCell = CellManager.GetInstance().GetLTECellLatest(strCellName);

            if (lteCell != null && lteCell.Antennas != null)
            {
                cellLongLat.fLongitude = (float)lteCell.Antennas[0].Longitude;
                cellLongLat.fLatitude = (float)lteCell.Antennas[0].Latitude;
            }
            else
            {
                cellLongLat.fLongitude = (float)data.dLongitude;
                cellLongLat.fLatitude = (float)data.dLatitude;
            }

            return cellLongLat;
        }

        /// <summary>
        /// 初始化控件
        /// </summary>
        private void setControl()
        {
            this.dataGridViewLeft.Columns.Clear();
            this.dataGridViewLeft.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            this.dataGridViewLeft.Columns.Add("序号", "序号");
            this.dataGridViewLeft.Columns.Add("小区名", "小区名");
            this.dataGridViewLeft.Columns.Add("距离", "距离");
            this.dataGridViewLeft.Columns.Add("夹角", "夹角");
            this.dataGridViewLeft.Columns.Add("关系", "关系");
            this.dataGridViewLeft.Columns[0].Width = 60;
            this.dataGridViewLeft.Columns[1].Width = 150;
            this.dataGridViewLeft.Columns[2].Width = 60;
            this.dataGridViewLeft.Columns[3].Width = 60;
            this.dataGridViewLeft.Columns[4].Width = 60;
            this.dataGridViewLeft.Tag = "dataGridViewLeft";

            this.dataGridViewRight.Columns.Clear();
            this.dataGridViewRight.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            this.dataGridViewRight.Columns.Add("序号", "序号");
            this.dataGridViewRight.Columns.Add("小区名", "小区名");
            this.dataGridViewRight.Columns.Add("距离", "距离");
            this.dataGridViewRight.Columns.Add("夹角", "夹角");
            this.dataGridViewRight.Columns.Add("关系", "关系");
            this.dataGridViewRight.Columns.Add("着色方案", "着色方案");
            this.dataGridViewRight.Columns[0].Width = 60;
            this.dataGridViewRight.Columns[1].Width = 150;
            this.dataGridViewRight.Columns[2].Width = 60;
            this.dataGridViewRight.Columns[3].Width = 60;
            this.dataGridViewRight.Columns[4].Width = 60;
            this.dataGridViewRight.Columns[5].Width = 100;
            this.dataGridViewRight.Tag = "dataGridViewRight";

            cmb_Temp = new ColorComboBox();
            cmb_Temp.Visible = false;
            cmb_Temp.SelectedValueChanged += new EventHandler((obj, even) =>
            {
                dataGridViewRight.Rows[iDataGridViewRow].Cells[iDataGridViewCell].Style.BackColor = cmb_Temp.SelectedColor;
            });
            this.dataGridViewRight.Controls.Add(cmb_Temp);
            #region 颜色设置
            listStaticColor = new List<Color>();
            listTmpColor = new List<Color>();
            listStaticColor.Add(Color.Red);
            listStaticColor.Add(Color.Orange);
            listStaticColor.Add(Color.Yellow);
            listStaticColor.Add(Color.GreenYellow);
            listStaticColor.Add(Color.Green);
            listStaticColor.Add(Color.SlateBlue);
            listStaticColor.Add(Color.Cyan);
            listStaticColor.Add(Color.Violet);
            listStaticColor.Add(Color.DeepPink);
            listStaticColor.Add(Color.BlueViolet);
            listStaticColor.Add(Color.Brown);
            listStaticColor.Add(Color.BurlyWood);
            listStaticColor.Add(Color.Blue);
            listStaticColor.Add(Color.DarkRed);
            listStaticColor.Add(Color.DarkOrchid);
            listStaticColor.Add(Color.DarkSeaGreen);
            listStaticColor.Add(Color.DarkSlateBlue);
            listStaticColor.Add(Color.DarkSlateGray);
            listStaticColor.Add(Color.DarkOliveGreen);
            listStaticColor.Add(Color.Black);
            listStaticColor.Add(Color.SteelBlue);
            #endregion
            listTmpColor.AddRange(listStaticColor);
        }

        /// <summary>
        /// dataGridView之间传值
        /// </summary>
        /// <param name="dgvSource">源dataGridView</param>
        /// <param name="dgvTarget">目标dataGridView</param>
        /// <param name="command">1.移动  2.复制</param>
        /// <param name="type">1.单个  2.全部</param>
        ///  <param name="iMax">目标 dataGridView 接收的最大值</param>
        private void moveDataGridViewData(DataGridView dgvSource, DataGridView dgvTarget, int command, int type ,int iMax)
        {
            if (mcList != null)
            {
                if (type == 1)
                {
                    if (dgvSource.SelectedRows.Count > 0)
                    {
                        string cellName = dgvSource.SelectedRows[0].Cells[1].Value.ToString();
                        foreach (matchCellInfo mcInfo in mcList)
                        {
                            if (mcInfo.strCellName == cellName)
                            {
                                if (dataGridViewRight.Rows.Count > iMax-1)
                                {
                                    MessageBox.Show("最多选择20个小区");
                                    break;
                                }
                                DataGridViewRow row = new DataGridViewRow();
                                List<object> objs = mcInfo.getRowObjectList();
                                foreach (object obj in objs)
                                {
                                    DataGridViewTextBoxCell boxcell1 = new DataGridViewTextBoxCell();
                                    boxcell1.Value = obj.ToString();
                                    row.Cells.Add(boxcell1);
                                }
                                dgvTarget.Rows.Add(row);

                                if (dgvTarget.Tag.ToString() == "dataGridViewRight")
                                {
                                    //设置默认色
                                    if (listTmpColor.Count == 0)
                                        listTmpColor.AddRange(listStaticColor);
                                    row.Cells[5].Style.BackColor = listTmpColor[0]; //getColor(indexColor);
                                    listTmpColor.RemoveAt(0);
                                }
                                if (dgvSource.Tag.ToString() == "dataGridViewRight")
                                {
                                   listTmpColor.Add(dgvSource.SelectedRows[0].Cells[5].Style.BackColor);
                                }
                                if (command == 1)
                                {
                                    dgvSource.Rows.Remove(dgvSource.SelectedRows[0]);
                                }
                            }
                        }
                        dgvSource.Refresh();
                        dgvTarget.Refresh();
                    }
                }
                else
                {
                    for (int iRow = dgvSource.Rows.Count-1; iRow >= 0; iRow--)
                    {
                        if (dataGridViewRight.Rows.Count > iMax-1)
                        {
                            MessageBox.Show("最多选择20个小区");
                            break;
                        }
                        if (dgvSource.Rows[iRow].Cells[1].Value == null)
                            continue;
                        string cellName = dgvSource.Rows[iRow].Cells[1].Value.ToString();
                        foreach (matchCellInfo mcInfo in mcList)
                        {
                            if (mcInfo.strCellName == cellName)
                            {
                                DataGridViewRow row = new DataGridViewRow();
                                List<object> objs = mcInfo.getRowObjectList();
                                foreach (object obj in objs)
                                {
                                    DataGridViewTextBoxCell boxcell1 = new DataGridViewTextBoxCell();
                                    boxcell1.Value = obj.ToString();
                                    row.Cells.Add(boxcell1);
                                }
                                dgvTarget.Rows.Add(row);

                                if (dgvTarget.Tag.ToString() == "dataGridViewRight")
                                {
                                    //设置默认色
                                    if (listTmpColor.Count == 0)
                                        listTmpColor.AddRange(listStaticColor);
                                    row.Cells[5].Style.BackColor = listTmpColor[0]; //getColor(indexColor);
                                    listTmpColor.RemoveAt(0);
                                }
                                if (dgvSource.Tag.ToString() == "dataGridViewRight")
                                {
                                    listTmpColor.Clear();
                                    listTmpColor.AddRange(listStaticColor);
                                }
                                if (command == 1)
                                {
                                    dgvSource.Rows.RemoveAt(iRow);
                                }
                            }
                        }
                    }
                    dgvSource.Refresh();
                    dgvTarget.Refresh();
                }
            }
        }

        private List<Color> listStaticColor = null;
        private List<Color> listTmpColor = null;
        /**
        /// <summary>
        ///设置默认颜色 
        /// </summary>
        private Color getColor(int indexColor)
        {
            Color cc = Color.Black;
            switch (indexColor)
            {
                #region 颜色设置
                case 1:
                    cc = Color.Red;
                    break;
                case 2:
                    cc = Color.Orange;
                    break;
                case 3:
                    cc = Color.Yellow;
                    break;
                case 4:
                    cc = Color.GreenYellow;
                    break;
                case 5:
                    cc = Color.Green;
                    break;
                case 6:
                    cc = Color.SlateBlue;
                    break;
                case 7:
                    cc = Color.Cyan;
                    break;
                case 8:
                    cc = Color.Violet;
                    break;
                case 9:
                    cc = Color.DeepPink;
                    break;
                case 10:
                    cc = Color.BlueViolet;
                    break;
                case 11:
                    cc = Color.Brown;
                    break;
                case 12:
                    cc = Color.BurlyWood;
                    break;
                case 13:
                    cc = Color.Blue;
                    break;
                case 14:
                    cc = Color.DarkRed;
                    break;
                case 15:
                    cc = Color.DarkOrchid;
                    break;
                case 16:
                    cc = Color.DarkSeaGreen;
                    break;
                case 17:
                    cc = Color.DarkSlateBlue;
                    break;
                case 18:
                    cc = Color.DarkSlateGray;
                    break;
                case 19:
                    cc = Color.DarkOliveGreen;
                    break;
                case 20:
                    cc = Color.Black;
                    break;
                #endregion
            }
            return cc;
        }*/

        private void dataGridViewRight_CurrentCellChanged(object sender, EventArgs e)
        {
            try
            {
                if (this.dataGridViewRight.CurrentCell.ColumnIndex == iDataGridViewCell)
                {
                    Rectangle rec = this.dataGridViewRight.GetCellDisplayRectangle(dataGridViewRight.CurrentCell.ColumnIndex, dataGridViewRight.CurrentCell.RowIndex, false);
                    cmb_Temp.Left = rec.Left;
                    cmb_Temp.Top = rec.Top;
                    cmb_Temp.Width = rec.Width;
                    cmb_Temp.Height = rec.Height;
                    cmb_Temp.Visible = true;
                    
                    iDataGridViewRow = dataGridViewRight.CurrentCell.RowIndex;
                }
                else
                {
                    cmb_Temp.Visible = false;
                }
            }
            catch
            {
                //continue
            }  
        }

        private void dataGridViewLeft_SortCompare(object sender, DataGridViewSortCompareEventArgs e)
        {
            if (e.Column.Name == "序号" || e.Column.Name == "距离" || e.Column.Name == "夹角")
            {
                if (Convert.ToDouble(e.CellValue1) - Convert.ToDouble(e.CellValue2) > 0)
                {
                    e.SortResult = 1;
                }
                else if (Convert.ToDouble(e.CellValue1) - Convert.ToDouble(e.CellValue2) < 0)
                {
                    e.SortResult = -1;
                }
                else
                {
                    e.SortResult = 0;
                }
            }
            else
            {
                e.SortResult = System.String.Compare(Convert.ToString(e.CellValue1), Convert.ToString(e.CellValue2));
            }
            e.Handled = true;
        }
        #endregion


    }

    public class matchCellInfo
    {
        /// <summary>
        /// 序号
        /// </summary>
        public int iSN { get; set; }
        /// <summary>
        /// 小区名
        /// </summary>
        public string strCellName { get; set; }
        /// <summary>
        /// 距离
        /// </summary>
        public double dDistance { get; set; }
        /// <summary>
        /// 夹角
        /// </summary>
        public float fDiffAngle { get; set; }
        /// <summary>
        /// 关系
        /// </summary>
        public string strRelation { get; set; }
        public List<object> getRowObjectList()
        {
            List<object> objs = new List<object>();
            objs.Add(iSN.ToString());
            objs.Add(strCellName.ToString());
            objs.Add(dDistance.ToString());
            objs.Add(fDiffAngle.ToString());
            objs.Add(strRelation.ToString());
            return objs;
        }
        
    }

    /// <summary>
    /// 带颜色下来框的ComboBox 
    /// </summary>
    public class ColorComboBox : ComboBox
    {
        public Color SelectedColor
        {
            get { return Color.FromName(this.Text); }
        }
        public ColorComboBox()
        {
            this.DrawMode = DrawMode.OwnerDrawFixed;
            this.DropDownStyle = ComboBoxStyle.DropDownList;
            this.ItemHeight = 15;
            PropertyInfo[] propInfoList = typeof(Color).GetProperties(BindingFlags.Static | BindingFlags.DeclaredOnly | BindingFlags.Public);
            foreach (PropertyInfo c in propInfoList)
            {
                this.Items.Add(c.Name);
            }
            this.Text = "Black"; //设置默认色
        }
        protected override void OnDrawItem(DrawItemEventArgs e)
        {
            Rectangle rect = e.Bounds;
            if (e.Index >= 0)
            {
                string colorName = this.Items[e.Index].ToString();
                Color c = Color.FromName(colorName);
                using (Brush b = new SolidBrush(c)) //预留下拉项间距
                {
                    e.Graphics.FillRectangle(b, rect.X, rect.Y + 2, rect.Width, rect.Height - 4);
                }
            }
        }
    }

    public class CellRelate
    {
        // <param name="dPCellLongitude">主小区 经度</param>
        // <param name="dPCellLatitude">主小区 纬度</param>
        // <param name="fPCell_方位角">主小区 方向角</param>
        // <param name="dNCellLongitude">邻小区 经度</param>
        // <param name="dNCellLatitude">邻小区 纬度</param>
        // <param name="fNCell_方位角">邻小区 方向角</param>
        // <param name="flag">标志位，以当前小区判断邻小区的相对位置，若无法判断，则由邻小区判断主小区的相对位置，若还是无法确定，则标志设置为2，返回"无关"</param>

        public double DPCellLongitude { get; set; }
        public double DPCellLatitude { get; set; }
        public float FPCell_方位角 { get; set; }
        public double DNCellLongitude { get; set; }
        public double DNCellLatitude { get; set; }
        public float FNCell_方位角 { get; set; }
        public int Flag { get; set; }

        /// <summary>
        /// 判定两个小区的关系  同站、对打、相交、相反、平行
        /// </summary>
        public string JudgeCellRel(CellRelate info)
        {
            if (info.DPCellLongitude == info.DNCellLongitude && info.DPCellLatitude == info.DNCellLatitude)
            {
                return "同站";
            }
            else
            {
                int iAngleSection = 60;
                //正方向 位置角度
                int iSiteAngleX_Y = ZTAntFuncHelper.calcSampleAngle(info.DPCellLongitude, info.DPCellLatitude,
                                                                    info.DNCellLongitude, info.DNCellLatitude, (short)info.FPCell_方位角, 0);
                int iSiteAngleY_X = ZTAntFuncHelper.calcSampleAngle(info.DNCellLongitude, info.DNCellLatitude,
                                                                    info.DPCellLongitude, info.DPCellLatitude, (short)info.FNCell_方位角, 0);
                //反方向 位置角度
                int iNeSiteAngleX_Y = ZTAntFuncHelper.calcSampleAngle(info.DPCellLongitude, info.DPCellLatitude,
                                                                   info.DNCellLongitude, info.DNCellLatitude,
                                                                   (short)ZTAntFuncHelper.getReverseAngle(info.FPCell_方位角),
                                                                   0);
                int iNeSiteAngleY_X = ZTAntFuncHelper.calcSampleAngle(info.DPCellLongitude, info.DPCellLatitude,
                                                                  info.DNCellLongitude, info.DNCellLatitude,
                                                                  (short)ZTAntFuncHelper.getReverseAngle(info.FNCell_方位角),
                                                                  0);
                if (info.FPCell_方位角 == info.FNCell_方位角)
                {
                    if (iSiteAngleX_Y <= iAngleSection || iSiteAngleY_X <= iAngleSection)
                    {
                        return "相交";
                    }
                    return "平行";
                }
                else
                {
                    return judgeDiffAngleCellRel(info, iAngleSection, iSiteAngleX_Y, iSiteAngleY_X, iNeSiteAngleX_Y, iNeSiteAngleY_X);
                }
            }
        }

        private string judgeDiffAngleCellRel(CellRelate info, int iAngleSection, int iSiteAngleX_Y, int iSiteAngleY_X, int iNeSiteAngleX_Y, int iNeSiteAngleY_X)
        {
            if (iSiteAngleX_Y <= iAngleSection)
            {
                if (iSiteAngleY_X <= iAngleSection)
                {
                    return "对打";
                }
                return "相交";
            }
            else
            {
                if (iNeSiteAngleX_Y <= iAngleSection)
                {
                    if (iNeSiteAngleY_X <= iAngleSection)
                    {
                        return "相反";
                    }
                    return "无关";
                }
                else
                {
                    if (info.Flag == 2)
                    {
                        return "无关";
                    }
                    CellRelate info2 = new CellRelate();
                    info2.DPCellLongitude = info.DNCellLongitude;
                    info2.DPCellLatitude = info.DNCellLatitude;
                    info2.FPCell_方位角 = info.FNCell_方位角;
                    info2.DPCellLongitude = info.DPCellLongitude;
                    info2.DPCellLatitude = info.DPCellLatitude;
                    info2.FPCell_方位角 = info.FPCell_方位角;
                    info2.Flag = 2;
                    return JudgeCellRel(info2);
                }
            }
        }
    }
}
