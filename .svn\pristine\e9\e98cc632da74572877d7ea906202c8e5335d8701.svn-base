﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTStationAcceptance_XJ
{
    public class NR700MBtsInfo : BtsInfoBase
    {
        public NR700MBtsServiceInfo SABtsInfo { get; set; }

        /// <summary>
        /// 由于存在拉远站,使用拉远小区名中的部分作为基站名
        /// </summary>
        public string FileBtsName { get; set; }

        public NR700MBtsInfo(NRBTS bts, string fileBtsName)
            : base(bts)
        {
            BtsName = bts.Name;
            BtsID = bts.BTSID;
            FileBtsName = fileBtsName;
        }

        public void Init(NRServiceName serviceType)
        {
            if (SABtsInfo == null && serviceType == NRServiceName.SA)
            {
                SABtsInfo = new NR700MBtsServiceInfo();
            }
        }

        public override void Calculate()
        {
            base.Calculate();
            SABtsInfo?.Calculate(BtsName);
        }
    }

    public class NR700MBtsServiceInfo
    {
        //由于存在拉远站,会存在多个切换文件,每个文件分开出结果
        public Dictionary<string, Bts700MHandoverInfo> FileBtsHandOverInfoDic { get; set; } = new Dictionary<string, Bts700MHandoverInfo>();

        public List<Bts700MHandoverInfo> FileBtsHandOverInfoList { get; set; } = new List<Bts700MHandoverInfo>();

        public void Calculate(string btsName)
        {
            foreach (var handOverInfo in FileBtsHandOverInfoDic.Values)
            {
                if (handOverInfo.FileBtsName == btsName)
                {
                    //相同名字作为主站,放第一个
                    FileBtsHandOverInfoList.Insert(0, handOverInfo);
                }
                else
                {
                    //不同名字为拉远站
                    FileBtsHandOverInfoList.Add(handOverInfo);
                }
            }
        }
    }

    public class Bts700MHandoverInfo
    {
        public Bts700MHandoverInfo(string fileBtsName)
        {
            FileBtsName = fileBtsName;
        }

        public string FileBtsName { get; set; }
        public PicKpiInfo PCIPicInfo { get; set; } = new PicKpiInfo();
        public SuccessRateKpiInfo InterHandoverRate { get; set; } = new SuccessRateKpiInfo();
        public SuccessRateKpiInfo IntraHandoverRate { get; set; } = new SuccessRateKpiInfo();

        public void Calculate()
        {
            InterHandoverRate.Calculate();
            IntraHandoverRate.Calculate();
        }
    }

    public class NR700MBtsParameters : BtsParameters
    {
        /// <summary>
        /// 站名
        /// </summary>
        public string BtsName { get; set; }
        /// <summary>
        /// 站号
        /// </summary>
        public int ENodeBID { get; set; }
        /// <summary>
        /// 地址
        /// </summary>
        public string Address { get; set; }
        /// <summary>
        /// 区县
        /// </summary>
        public string Country { get; set; }

        public ParamInfo<double?> BtsLongutide { get; set; } = new ParamInfo<double?>();
        public ParamInfo<double?> BtsLatitude { get; set; } = new ParamInfo<double?>();
        public ParamInfo<int?> NodeBID { get; set; } = new ParamInfo<int?>();
        public ParamInfo<int?> TAC { get; set; } = new ParamInfo<int?>();
        public ParamInfo<int?> Altitude { get; set; } = new ParamInfo<int?>();

        public Dictionary<string, NR700MCellParameters> CellDic { get; set; } = new Dictionary<string, NR700MCellParameters>();

        public void Caluculate()
        {
            BtsLongutide.JudgeValidLongitude(50);
            BtsLatitude.JudgeValidLatitude(50);
            NodeBID.JudgeValid();
            TAC.JudgeValid();
            Altitude.JudgeValid(100);

            foreach (var cell in CellDic.Values)
            {
                cell.Caluculate();
            }
        }
    }
}
