﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using System.Windows.Forms;
using MasterCom.Util;
using System.Drawing;

namespace MasterCom.RAMS.ZTFunc
{
    public class FRFrequencyConVerifyByRegion : DIYAnalyseByFileBackgroundBase
    {
        const double roadWidth = 0.0001;//约为10米
        const double widthRange = 2000;
        protected static readonly object lockObj = new object();
        private static FRFrequencyConVerifyByRegion instance = null;
        private int index { get; set; } = 1;
        protected int channelReleaseMsgId = 1549;
        protected string earfcnMsgParamName = "gsm_a.rr.earfcn";

        public FRFrequencyConVerifyByCondition frCondition { get; set; } = new FRFrequencyConVerifyByCondition();   //查询条件

        private List<FRFreConVerifyInfo> listFrItemsInfo;
        public static FRFrequencyConVerifyByRegion GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new FRFrequencyConVerifyByRegion();
                    }
                }
            }
            return instance;
        }

         protected FRFrequencyConVerifyByRegion()
            : base(MainModel.GetInstance())
        {
            this.IncludeMessage = true;
            ServiceTypes.Clear();
        }

         public override string Name
         {
             get
             {
                 return "FR频点配置核查分析(按区域)";
             }
         }
         protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
         {
             return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22100, this.Name);
         }

         protected override bool getCondition()
         {
             ZTLteFRFrequencyConVerifySetForm setForm = new ZTLteFRFrequencyConVerifySetForm();
             if (setForm.ShowDialog() != DialogResult.OK)
             {
                 return false;
             }
            frCondition = setForm.GetCondition();
             this.listFrItemsInfo = new List<FRFreConVerifyInfo>();
             this.index = 1;
             return true;
         }

         protected override void fireShowForm()
         {
             if (listFrItemsInfo == null || listFrItemsInfo.Count == 0)
             {
                 MessageBox.Show("没有符合条件的数据。");
                 return;
             }
             ZTLteFRFrequencyConVerifyInfoForm frm = MainModel.CreateResultForm(typeof(ZTLteFRFrequencyConVerifyInfoForm)) as ZTLteFRFrequencyConVerifyInfoForm;
             frm.FillData(listFrItemsInfo);
             frm.Owner = MainModel.MainForm;
             frm.Visible = true;
             frm.BringToFront();
             this.listFrItemsInfo = null;
             this.index = 1;
         }

         protected override void doStatWithQuery()
         {
             foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
             {
                 int innerSn = 1;
                 FRFreConVerifyInfo frInfoItem = new FRFreConVerifyInfo();
                 for (int i = 0; i < file.DTDatas.Count; i++)
                 {
                     DTData data = file.DTDatas[i];
                     if (data is MessageWithSource)
                     {
                         MessageWithSource msg = data as MessageWithSource;
                         if (msg.ID == channelReleaseMsgId)//channel release 信令
                         {
                             FrItemInfo frItem = new FrItemInfo(file.FileName);
                             frItem.SN = innerSn++;
                             this.getNearMess(file, frItem, msg, i);
                             this.concatNearItems(frItem);
                             this.getVauleFromMess(msg, earfcnMsgParamName, frItem);
                             frInfoItem.ListFrItemsInfo.Add(frItem);
                         }
                     }
                 }
                 if (frInfoItem.ListFrItemsInfo.Count > 0)
                 {
                     frInfoItem.SN = listFrItemsInfo.Count + 1;
                     this.listFrItemsInfo.Add(frInfoItem);
                 }
             }
         }
         private void getNearMess(DTFileDataManager file, FrItemInfo frItem, MessageWithSource msg, int index)
         {
             if (file == null || frItem == null || msg == null)
             {
                 return;
             }
             //前X秒内邻近的GSM小区信息
             for (int i = index - 1; i >= 0; i--)
             {
                 DTData data = file.DTDatas[i];
                 if (Math.Abs((msg.DateTime - data.DateTime).TotalSeconds) > frCondition.BeforeSecond)
                 {
                     break;
                 }
                dealDTData(frItem, data, frItem.ListGSMPoints);
            }

             //后X秒内邻近的LTE小区信息
             for (int i = index + 1; i < file.DTDatas.Count; i++)
            {
                DTData data = file.DTDatas[i];
                double tt = Math.Abs((data.DateTime - msg.DateTime).TotalSeconds);
                if (tt > frCondition.AfterSecond)
                {
                    break;
                }
                dealDTData(frItem, data, frItem.ListLTEPoints);
            }
            //附近LTE小区
            CellManager cm = CellManager.GetInstance();
             List<LTEBTS> listLTEBTS = cm.GetLTEBTSs(msg.DateTime);

             double range = widthRange / 10 * roadWidth;//求出2KM以内经纬度范围
             double lon = frItem.EventLongitude;
             double lar = frItem.EventLatitude;
             
             float lonF = Convert.ToSingle(lon);
             float larF = Convert.ToSingle(lar);
             double minAngel = 360;

             foreach (LTEBTS item in listLTEBTS)
            {
                if (Math.Abs(lon - item.Longitude) > range || Math.Abs(lar - item.Latitude) > range)
                {
                    continue;
                }

                minAngel = addListLTECells(frItem, lon, lar, lonF, larF, minAngel, item);
            }
        }

        private void dealDTData(FrItemInfo frItem, DTData data, List<TestPoint> tps)
        {
            if (data is Event)
            {
                Event evt = data as Event;
                if (frItem.EvtDone == null && (evt.ID == 883 || evt.ID == 852
                    || evt.ID == 3082 || evt.ID == 3083 || evt.ID == 3171))
                {//Return Back to LTE Request
                 //Track Area Update Attempt
                    this.setEventData(frItem, evt);
                }
            }
            else if (data is TestPoint)
            {
                TestPoint tp = data as TestPoint;
                tps.Add(tp);
            }
        }

        private static double addListLTECells(FrItemInfo frItem, double lon, double lar, float lonF, float larF, double minAngel, LTEBTS item)
        {
            List<LTECell> listLTECell = item.Cells;
            foreach (LTECell aCell in listLTECell)
            {
                double dist = aCell.GetDistance(lon, lar);
                if (dist < widthRange)
                {
                    double angels = MathFuncs.CalAngle(new PointF(Convert.ToSingle(aCell.Longitude), Convert.ToSingle(aCell.Latitude)),
                                   new PointF(Convert.ToSingle(aCell.EndPointLongitude), Convert.ToSingle(aCell.EndPointLatitude)), new PointF(lonF, larF));
                    if (angels < minAngel)
                    {
                        frItem.ListLTECells.Clear();
                        minAngel = angels;
                        frItem.ListLTECells.Add(aCell);
                    }
                    else if (minAngel == angels)
                    {
                        frItem.ListLTECells.Add(aCell);
                    }
                }
            }

            return minAngel;
        }

        private void setEventData(FrItemInfo frItem, Event evt)
         {
             frItem.EvtDone = evt;
             frItem.EventLatitude = evt.Latitude;
             frItem.EventLongitude = evt.Longitude;
             frItem.EventTime = evt.DateTime;
             frItem.GridName = evt.GridDesc;
         }

         private void concatNearItems(FrItemInfo frItem)
        {
            if (frItem == null)
            {
                return;
            }

            if (frItem.ListGSMPoints != null && frItem.ListGSMPoints.Count > 0)
            {
                Boolean hasSearch = false;

                foreach (TestPoint tp in frItem.ListGSMPoints)
                {
                    int? lac = tp.GetLAC();
                    int? ci = tp.GetCI();
                    if (lac != null && ci != null)
                    {
                        hasSearch = setCellInfo(frItem, hasSearch, tp, lac, ci);
                    }
                }

                frItem.GSMQual = Math.Round(frItem.GSMQual / frItem.ListGSMPoints.Count, 2);
                frItem.GSMRxLev = Math.Round(frItem.GSMRxLev / frItem.ListGSMPoints.Count, 2);
            }

            if (frItem.ListLTEPoints != null && frItem.ListLTEPoints.Count > 0)
            {
                setSCellInfo(frItem);

                frItem.SCell_RSRP = Math.Round(frItem.SCell_RSRP / frItem.ListLTEPoints.Count, 2);
                frItem.SCell_RSRQ = Math.Round(frItem.SCell_RSRQ / frItem.ListLTEPoints.Count, 2);
                frItem.SCell_RSSI = Math.Round(frItem.SCell_RSSI / frItem.ListLTEPoints.Count, 2);
                frItem.SCell_SINR = Math.Round(frItem.SCell_SINR / frItem.ListLTEPoints.Count, 2);
            }

            setNearCellInfo(frItem);
        }

        private bool setCellInfo(FrItemInfo frItem, bool hasSearch, TestPoint tp, int? lac, int? ci)
        {
            if (!hasSearch)
            {
                frItem.GSM_LAC = (int)lac;
                frItem.GSM_CI = (int)ci;
                frItem.GSM_Distance = Math.Round(MathFuncs.GetDistance(tp.Longitude, tp.Latitude, frItem.EventLongitude, frItem.EventLatitude), 2);

                ICell iCell = tp.GetMainCell();
                if (iCell is Cell)
                {
                    frItem.GSM_Distance = Math.Round((iCell as Cell).GetDistance(frItem.EventLongitude, frItem.EventLatitude), 2);
                    frItem.GSM_Name = iCell.Name;
                }
                else if (iCell is WCell)
                {
                    frItem.GSM_Distance = Math.Round((iCell as WCell).GetDistance(frItem.EventLongitude, frItem.EventLatitude), 2);
                    frItem.GSM_Name = iCell.Name;
                }
                else
                {
                    frItem.GSM_Name = frItem.GSM_LAC + "_" + frItem.GSM_CI;
                }
                hasSearch = true;
            }

            float? rxlev = tp.GetRxlev();
            if (rxlev != null)
            {
                frItem.GSMRxLev += (float)rxlev;
            }

            float? rxqual = null;
            if (tp is LTEFddTestPoint)
            {
                rxqual = (float?)tp["lte_fdd_wcdma_TotalEc_Io"];
            }
            else
            {
                rxqual = (float?)tp["lte_gsm_DM_RxQualSub"];
            }

            if (rxqual != null)
            {
                frItem.GSMQual += (float)rxqual;
            }

            return hasSearch;
        }

        private void setSCellInfo(FrItemInfo frItem)
        {
            Boolean hasSearch = false;
            foreach (TestPoint tp in frItem.ListLTEPoints)
            {
                LTECell lteCell = tp.GetMainLTECell_TdOrFdd();
                if (lteCell != null)
                {
                    if (!hasSearch)
                    {
                        frItem.SCell_BaseName = lteCell.BTSName;
                        frItem.SCell_Distance = Math.Round(lteCell.GetDistance(frItem.EventLongitude, frItem.EventLatitude), 2);
                        frItem.SCell_ECI = lteCell.ECI;
                        frItem.SCell_LAC = lteCell.TAC;
                        frItem.SCell_Name = lteCell.Name;

                        //回落LTE小区信息
                        frItem.Back_SCell_EARFCN = lteCell.EARFCN;
                        frItem.Back_SCell_PCI = lteCell.PCI;
                        hasSearch = true;
                    }
                    //by xiechang 求均值
                    frItem.SCell_RSRP += Convert.ToDouble(GetRSRP(tp));
                    frItem.SCell_RSRQ += Convert.ToDouble(GetRSRQ(tp));
                    frItem.SCell_RSSI += Convert.ToDouble(GetRSSI(tp));
                    frItem.SCell_SINR += Convert.ToDouble(GetSINR(tp));
                }
            }
        }

        private void setNearCellInfo(FrItemInfo frItem)
        {
            if (frItem.ListLTECells != null && frItem.ListLTECells.Count > 0)
            {
                StringBuilder sbECT = new StringBuilder();
                StringBuilder sbFre = new StringBuilder();
                StringBuilder sbName = new StringBuilder();
                foreach (LTECell item in frItem.ListLTECells)
                {
                    if (item.EARFCN != 0)
                        sbFre.AppendFormat("{0};", item.EARFCN);
                    if (item.ECI != 0)
                        sbECT.AppendFormat("{0};", item.ECI);
                    if (!string.IsNullOrEmpty(item.Name))
                        sbName.AppendFormat("{0};", item.Name);
                }
                frItem.NearLTECI = sbECT.ToString().Trim(';');
                frItem.NearLTEFre = sbFre.ToString().Trim(';');
                frItem.NearLTEName = sbName.ToString().Trim(';');
            }
        }

        protected object GetRSRP(TestPoint tp)
         {
             if (tp is LTEFddTestPoint)
             {
                 return tp["lte_fdd_RSRP"];
             }
             return tp["lte_RSRP"];
         }
         protected object GetRSRQ(TestPoint tp)
         {
             if (tp is LTEFddTestPoint)
             {
                 return tp["lte_fdd_RSRQ"];
             }
             return tp["lte_RSRQ"];
         }
         protected object GetRSSI(TestPoint tp)
         {
             if (tp is LTEFddTestPoint)
             {
                 return tp["lte_fdd_RSSI"];
             }
             return tp["lte_RSSI"];
         }
         protected object GetSINR(TestPoint tp)
         {
             if (tp is LTEFddTestPoint)
             {
                 return tp["lte_fdd_SINR"];
             }
             return tp["lte_SINR"];
         }
         
         private void getVauleFromMess(MessageWithSource msg, string strFieldName, FrItemInfo frItem)
         {
            try
            {
                MessageWithSource msgSource = msg;

                MessageDecodeHelper.StartDissect(msg.Direction, msgSource.Source, msgSource.Length, msgSource.ID);

                int[] ChannelReleList = new int[20];
                if (!MessageDecodeHelper.GetMultiSInt(strFieldName, ref ChannelReleList, ChannelReleList.Length))
                {
                    frItem.HasFreON = "否";
                    return;
                }
                frItem.HasFreON = "是";
                StringBuilder sb = new StringBuilder();
                if (ChannelReleList.Length > 0)
                {
                    for (int i = 0, len = ChannelReleList.Length; i < len; i++)
                    {
                        sb.Append(string.Format("{0};", ChannelReleList[i]));
                    }
                }
                frItem.ChannelReleList = sb.ToString().TrimEnd(';');
            }
            catch
            {
                //contine
            }
         }
    }

    public class FRFrequencyConVerifyByRegion_FDD : FRFrequencyConVerifyByRegion
    {
        private static FRFrequencyConVerifyByRegion_FDD instance = null;
        public static new FRFrequencyConVerifyByRegion_FDD GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new FRFrequencyConVerifyByRegion_FDD();
                    }
                }
            }
            return instance;
        }
        protected FRFrequencyConVerifyByRegion_FDD()
            : base()
        {
            channelReleaseMsgId = 1093600016;
            earfcnMsgParamName = "rrc.dlEUTRACarrierFreq";
        }
        public override string Name
        {
            get
            {
                return "LTE_FDD FR频点配置核查分析(按区域)";
            }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 26000, 26067, this.Name);
        }
    }

    public class FRFrequencyConVerifyByCondition
    {
        public int BeforeSecond { get; set; }           //返回前时长
        public int AfterSecond { get; set; }            //返回后时长

        public FRFrequencyConVerifyByCondition()
        {
            BeforeSecond = 3;
            AfterSecond = 3;
        }
    }
}
