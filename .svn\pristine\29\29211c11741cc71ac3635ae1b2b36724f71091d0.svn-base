﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.ZTFunc;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class XtraSetCellMultiForm_LTE : BaseDialog
    {
        public XtraSetCellMultiForm_LTE()
        {
            InitializeComponent();
        }
        public void SetCondition(CellMultiCoverageCondition condition)
        {
            if (condition == null)
            {
                return;
            }
            numRxLevDValue.Value = (decimal)condition.SetRxlevDiff;
             numRxLevThreshold.Value= (decimal)condition.SetRxlev ;
            spinEditInvalidThresold.Value = (decimal)condition.InvalidPointRxLev;
            chkSaveSample.Checked = condition.IsSaveSample;

            freqBandControl1.chkFreqBand.Checked = condition.CheckFreqBandOnly;
            //设置频点条件
            if (condition.ListFreqPoint!=null&&condition.ListFreqPoint.Count > 0)
            {
                freqBandControl1.lvFreqBand.Items.Clear();
                foreach (FreqPoint fp in condition.ListFreqPoint)
                {
                    ListViewItem lvi = new ListViewItem();
                    lvi.Text = fp.Carrier + "_" + fp.FreqPointName;
                    lvi.Tag = fp;
                    freqBandControl1.lvFreqBand.Items.Add(lvi);
                }
            }
        }

        public CellMultiCoverageCondition GetSettingFilterRet()
        {
            CellMultiCoverageCondition condition = new CellMultiCoverageCondition();
            condition.SetRxlevDiff = (int)numRxLevDValue.Value;
            condition.SetRxlev = (int)numRxLevThreshold.Value;
            condition.CoFreq = false;
            condition.InterferenceType = (MapForm.DisplayInterferenceType)0;
            condition.InvalidPointRxLev = (int)spinEditInvalidThresold.Value;
            condition.IsSaveSample = chkSaveSample.Checked;
            condition.CheckFreqBandOnly = freqBandControl1.chkFreqBand.Checked;

            //得到频点集合
            condition.ListFreqPoint = freqBandControl1.GetListViewItems();
            return condition;
        }


        private void chkFreqBand_CheckedChanged(object sender, EventArgs e)
        {
            freqBandControl1.lvFreqBand.Enabled = freqBandControl1.chkFreqBand.Checked;
            freqBandControl1.btnFreqBand.Enabled = freqBandControl1.chkFreqBand.Checked;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            if (freqBandControl1.chkFreqBand.Checked && freqBandControl1.lvFreqBand.Items.Count <= 0)
            {
                XtraMessageBox.Show("请选择频点", "提示");
                DialogResult = DialogResult.None;
                return;
            }
            DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        private void XtraSetCellMultiForm_LTE_Load(object sender, EventArgs e)
        {
            freqBandControl1.ChkFreqBandChange_click += new FreqBandControl.ChkChangeDelegate(chkFreqBand_CheckedChanged);//把事件绑定到自定义的委托上
        }
    }
}