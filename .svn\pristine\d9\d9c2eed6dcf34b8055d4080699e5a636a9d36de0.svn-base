﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc.ZTCellCheck.RoadSegment
{
    public class OverHandover : IProblemData
    {
        public override string ToString()
        {
            return "切换频繁";
        }
        public OverHandover(List<Event> evts, DTFileDataManager file)
        {
            this.Events = evts;
            int begin = evts[0].SN;
            int end = evts[evts.Count - 1].SN;
            testPoints = new List<TestPoint>();
            foreach (TestPoint tp in file.TestPoints)
            {
                if (tp.SN >= begin && tp.SN <= end)
                {
                    testPoints.Add(tp);
                }
            }
        }
        
        #region IRelatedData 成员

        private readonly List<TestPoint> testPoints;
        public List<TestPoint> TestPoints
        {
            get { return testPoints; }
        }

        public List<Event> Events
        {
            get;
            set;
        }

        public List<string> Cells
        {
            get
            {
                List<string> names = new List<string>();
                foreach (Event evt in Events)
                {
                    LTECell cell = evt.GetTargetCell() as LTECell;
                    if (cell != null && !names.Contains(cell.Name))
                    {
                        names.Add(cell.Name);
                    }
                    cell = evt.GetSrcCell() as LTECell;
                    if (cell != null && !names.Contains(cell.Name))
                    {
                        names.Add(cell.Name);
                    }
                }
                return names;
            }
        }

        public string roadDesc { get; set; }
        public string RoadDesc
        {
            get
            {
                getRoadDesc();
                return roadDesc;
            }
        }

        public string HoDesc
        {
            get;
            private set;
        }

        public void MakeSummary()
        {
            getRoadDesc();
        }

        private void getRoadDesc()
        {
            if (roadDesc != null)
            {
                return;
            }
            List<double> lng = new List<double>();
            List<double> lat = new List<double>();
            if (TestPoints != null)
            {
                dealTPs(lng, lat);
            }
            if (Events != null)
            {
                dealEvtsHoDesc(lng, lat);
            }
            roadDesc = MasterCom.Util.GISManager.GetInstance().GetRoadPlaceDesc(lng, lat);
        }

        private void dealTPs(List<double> lng, List<double> lat)
        {
            float rsrpMin = float.MaxValue;
            float rsrpMax = float.MinValue;
            float rsrpSum = 0;
            int rsrpNum = 0;
            float sinrMin = float.MaxValue;
            float sinrMax = float.MinValue;
            float sinrSum = 0;
            int sinrNum = 0;
            foreach (TestPoint tp in TestPoints)
            {
                lng.Add(tp.Longitude);
                lat.Add(tp.Latitude);
                float? rsrpObj = null;
                float? sinrObj = null;
                getTPParam(tp, out rsrpObj, out sinrObj);
                if (rsrpObj != null && -141 <= rsrpObj && rsrpObj <= 25)
                {
                    rsrpMin = Math.Min(rsrpMin, (float)rsrpObj);
                    rsrpMax = Math.Max(rsrpMax, (float)rsrpObj);
                    rsrpSum += (float)rsrpObj;
                    rsrpNum++;
                }
                if (sinrObj != null && -50 <= sinrObj && sinrObj <= 50)
                {
                    sinrMin = Math.Min(sinrMin, (float)sinrObj);
                    sinrMax = Math.Max(sinrMax, (float)sinrObj);
                    sinrSum += (float)sinrObj;
                    sinrNum++;
                }
            }
            if (float.MinValue != rsrpMax)
            {
                this.RSRPMax = rsrpMax;
            }
            if (float.MaxValue != rsrpMin)
            {
                this.RSRPMin = rsrpMin;
            }
            if (rsrpNum != 0)
            {
                this.RSRPAvg = (float)Math.Round(1.0 * rsrpSum / rsrpNum, 2);
            }
            if (float.MinValue != sinrMax)
            {
                this.SINRMax = sinrMax;
            }
            if (float.MaxValue != sinrMin)
            {
                this.SINRMin = sinrMin;
            }
            if (sinrNum != 0)
            {
                this.SINRAvg = (float)Math.Round(1.0 * sinrSum / sinrNum, 2);
            }
        }

        private static void getTPParam(TestPoint tp, out float? rsrpObj, out float? sinrObj)
        {
            if (tp is LTEFddTestPoint)
            {
                rsrpObj = (float?)tp["lte_fdd_RSRP"];
                sinrObj = (float?)tp["lte_fdd_SINR"];
            }
            else
            {
                rsrpObj = (float?)tp["lte_RSRP"];
                sinrObj = (float?)tp["lte_SINR"];
            }
        }

        private void dealEvtsHoDesc(List<double> lng, List<double> lat)
        {
            StringBuilder hoDesc = new StringBuilder();
            string lastCellName = string.Empty;
            foreach (Event evt in Events)
            {
                lng.Add(evt.Longitude);
                lat.Add(evt.Latitude);
                ICell cell = evt.GetSrcCell();
                if (cell != null)
                {
                    if (string.IsNullOrEmpty(lastCellName))
                    {
                        hoDesc.Append(cell + "->");
                    }
                    else if (cell.Name != lastCellName)
                    {
                        hoDesc.Append(cell.Name + "->");
                    }
                }
                ICell tarCell = evt.GetTargetCell();
                if (tarCell != null)
                {
                    lastCellName = tarCell.Name;
                    hoDesc.Append(tarCell.Name + "->");
                }
            }
            HoDesc = hoDesc.ToString().TrimEnd('-', '>');
        }

        #endregion

        #region IProblemData 成员


        public float? RSRPMin
        {
            get;
            private set;
        }

        public float? RSRPMax
        {
            get;
            private set;
        }

        public float? RSRPAvg
        {
            get;
            private set;
        }

        public float? SINRMin
        {
            get;
            private set;
        }

        public float? SINRMax
        {
            get;
            private set;
        }

        public float? SINRAvg
        {
            get;
            private set;
        }

        #endregion
    }
}
