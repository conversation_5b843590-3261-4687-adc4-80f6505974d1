﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public class LowSpeedConditionLTE
    {
        public LowSpeedConditionLTE()
        {
            TestPointDistance = 50;
            IsAppSpeed = true;
        }

        /// <summary>
        /// 是否分析应用层速率
        /// </summary>
        public bool IsAppSpeed { get; set; }
        public bool CheckFTPDL { get; set; } = true;
        public double FTPDLRateMax { get; set; } = 10;
        public bool CheckFTPUL { get; set; } = true;
        public double FTPULRateMax { get; set; } = 1;
        public bool CheckHTTP { get; set; } = true;
        public double HTTPRateMax { get; set; } = 2;
        public bool CheckVideo { get; set; } = true;
        public double VideoRateMax { get; set; } = 2;
        public bool CheckEmail { get; set; } = true;
        public double EmailRateMax { get; set; } = 2;
        public bool CheckPdcpDl { get; set; } = true;
        public double PdcpDlRateMax { get; set; } = 10;
        public bool CheckPdcpUl { get; set; } = true;
        public double PdcpUlRateMax { get; set; } = 10;
        public double DistanceMin { get; set; } = 50;
        public double DistanceFTPDLMin { get; set; } = 50;
        public double DistanceFTPULMin { get; set; } = 50;
        public double DistanceEMailMin { get; set; } = 20;
        public double DistanceHTTPMin { get; set; } = 20;
        public double DistanceVideoMin { get; set; } = 20;
        public double DistancePdcpDlMin { get; set; } = 50;
        public double DistancePdcpUlMin { get; set; } = 50;
        public double EmailRateMin { get; set; }
        public double FTPDLRateMin { get; set; }
        public double FTPULRateMin { get; set; }
        public double HTTPRateMin { get; set; }
        public double VideoRateMin { get; set; }
        public double PdcpDlRateMin { get; set; }
        public double PdcpUlRateMin { get; set; }
        /// <summary>
        /// 上传速率单位 0:Mbps ; 1:Kbps
        /// </summary>
        public int PdcpUlRateUnit { get; set; }
        /// <summary>
        /// 下载速率单位 0:Mbps ; 1:Kbps
        /// </summary>
        public int PdcpDlRateUnit { get; set; }
        public double LowPercent { get; set; } = 80;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="tp"></param>
        /// <param name="speed">单位为Mb</param>
        /// <returns></returns>
        public virtual bool IsValidSpeed(TestPoint tp, out float? speed)
        {
            speed = null;
            short? type = (short?)tp["lte_APP_type"];
            if (type == null)
            {
                return false;
            }
            if (this.IsAppSpeed)
            {
                object obj = tp["lte_APP_Speed_Mb"];
                if (obj == null)
                {
                    return false;
                }
                speed = float.Parse(obj.ToString());
                if (speed < 0)
                {
                    return false;
                }
                return judgeValidAppSpeed(speed, type);
            }
            else if (type == (int)AppType.FTP_Download || type == (int)AppType.FTP_Upload)
            {
                return judgeValidDataSpeed(tp, ref speed, type);
            }
            return false;
        }

        private bool judgeValidAppSpeed(float? speed, short? type)
        {
            if (type == (int)AppType.FTP_Download && CheckFTPDL)
            {
                return speed <= FTPDLRateMax && speed >= FTPDLRateMin;
            }
            if (type == (int)AppType.FTP_Upload && CheckFTPUL)
            {
                return speed <= FTPULRateMax && speed >= FTPULRateMin;
            }
            if (type == (int)AppType.Http_Download && CheckHTTP)
            {
                return speed <= HTTPRateMax && speed >= HTTPRateMin;
            }
            if (type == (int)AppType.Email_SMTP && CheckEmail)
            {
                return speed <= EmailRateMax && speed >= EmailRateMin;
            }
            if (type == (int)AppType.Http_Video && CheckVideo)
            {
                return speed <= VideoRateMax && speed >= VideoRateMin;
            }

            return false;
        }

        private bool judgeValidDataSpeed(TestPoint tp, ref float? speed, short? type)
        {
            double rateMax = 0;
            double rateMin = 0;
            object obj = null;
            int? status = (int?)(short?)tp["lte_APP_Status"];
            if (type == (int)AppType.FTP_Download && CheckPdcpDl)
            {
                if (status != 12)
                {
                    return false;
                }
                obj = tp["lte_PDCP_DL_Mb"];

                if (obj == null)
                {
                    return false;
                }
                rateMax = PdcpDlRateMax;
                rateMin = PdcpDlRateMin;
            }
            else if (type == (int)AppType.FTP_Upload && CheckPdcpUl)
            {
                if (status != 21)
                {
                    return false;
                }
                obj = tp["lte_PDCP_UL_Mb"];

                if (obj == null)
                {
                    return false;
                }
                rateMax = PdcpUlRateMax;
                rateMin = PdcpUlRateMin;
            }
            else
            {
                return false;
            }

            speed = float.Parse(obj.ToString());
            if (speed < 0)
            {
                return false;
            }
            return speed <= rateMax && speed >= rateMin;
        }

        public bool IsValidSpeed(TestPoint tp)
        {
            float? temp;
            return IsValidSpeed(tp, out temp);
        }

        public bool IsValidDistance(double distance)
        {
            return distance >= DistanceMin;
        }

        public double TestPointDistance
        {
            get;
            set;
        }
        internal bool IsValid2PointDis(double distance2Tp)
        {
            return distance2Tp <= TestPointDistance;
        }

        public float RsrpMin { get; set; }

        public float RsrpMax { get; set; }

        public bool CheckRsrp { get; set; }

        public bool CheckSinr { get; set; }

        public float SinrMin { get; set; }

        public float SinrMax { get; set; }

        public bool CheckHo { get; set; }

        public int HoSecond { get; set; }

        public bool bMatchRoad { get; set; } = false;
        public virtual bool IsIgnoreByRsrp(TestPoint tp)
        {
            if (CheckRsrp)
            {
                float? rsrp = (float?)tp["lte_RSRP"];
                return !(RsrpMin <= rsrp && rsrp <= RsrpMax);
            }
            return false;
        }

        public virtual bool IsIgnoreBySinr(TestPoint tp)
        {
            if (CheckSinr)
            {
                float? sinr = (float?)tp["lte_SINR"];
                return !(SinrMin <= sinr && sinr <= SinrMax);
            }
            return false;
        }

        internal bool IsIgnoreByHo(TestPoint tp, List<Event> hoEvents)
        {
            if (CheckHo)
            {
                for (int i = hoEvents.Count - 1; i >= 0; i--)
                {
                    Event e = hoEvents[i];
                    if (e.SN <= tp.SN)
                    {
                        return (tp.DateTime.AddMilliseconds(tp.Millisecond) - e.DateTime).TotalSeconds > HoSecond;
                    }
                }
            }
            return false;
        }

        //是否选择综合低速率路段
        public bool CheckSynthesisLowSpeed { get; set; } = false;
        //是否选择LTE低速率路段
        public bool CheckLTELowSpeed { get; set; } = true;
        //是否选择TD_W低速率路段
        public bool CheckLowSpeed_TDOrW { get; set; } = true;
        //是否选择GSM低速率路段
        public bool CheckGSMLowSpeed { get; set; } = true;
    }

    public class LowSpeedConditionLTEFDD : LowSpeedConditionLTE
    {
        public LowSpeedConditionLTEFDD()
            : base()
        {
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="tp"></param>
        /// <param name="speed">单位为Mb</param>
        /// <returns></returns>
        public override bool IsValidSpeed(TestPoint tp, out float? speed)
        {
            speed = null;
            short? type = (short?)tp["lte_fdd_APP_type"];
            if (type == null)
            {
                return false;
            }
            if (this.IsAppSpeed)
            {
                object obj = tp["lte_fdd_APP_Speed_Mb"];
                if (obj == null)
                {
                    return false;
                }
                speed = float.Parse(obj.ToString());
                if (speed < 0)
                {
                    return false;
                }
                return JudgeAppSpeed(ref speed, type);
            }
            else if (type == (int)AppType.FTP_Download || type == (int)AppType.FTP_Upload)
            {
                return JudgeFTPData(tp, ref speed, type);
            }
            return false;
        }

        private bool JudgeFTPData(TestPoint tp, ref float? speed, short? type)
        {
            double rateMax = 0;
            double rateMin = 0;
            object obj = null;
            int? status = (int?)(short?)tp["lte_fdd_APP_Status"];
            if (type == (int)AppType.FTP_Download && CheckPdcpDl)
            {
                if (status != 12)
                {
                    return false;
                }

                obj = tp["lte_fdd_PDCP_DL_Mb"];

                rateMax = PdcpDlRateMax;
                rateMin = PdcpDlRateMin;
            }
            else if (type == (int)AppType.FTP_Upload && CheckPdcpUl)
            {
                if (status != 21)
                {
                    return false;
                }
                obj = tp["lte_fdd_PDCP_UL_Mb"];

                rateMax = PdcpUlRateMax;
                rateMin = PdcpUlRateMin;
            }
            if (obj == null)
            {
                return false;
            }
            speed = float.Parse(obj.ToString());
            if (speed < 0)
            {
                return false;
            }
            return speed <= rateMax && speed >= rateMin;
        }

        private bool JudgeAppSpeed(ref float? speed, short? type)
        {
            if (type == (int)AppType.FTP_Download && CheckFTPDL)
            {
                return speed <= FTPDLRateMax && speed >= FTPDLRateMin;
            }
            if (type == (int)AppType.FTP_Upload && CheckFTPUL)
            {
                return speed <= FTPULRateMax && speed >= FTPULRateMin;
            }
            if (type == (int)AppType.Http_Download && CheckHTTP)
            {
                return speed <= HTTPRateMax && speed >= HTTPRateMin;
            }
            if (type == (int)AppType.Email_SMTP && CheckEmail)
            {
                return speed <= EmailRateMax && speed >= EmailRateMin;
            }
            if (type == (int)AppType.Http_Video && CheckVideo)
            {
                return speed <= VideoRateMax && speed >= VideoRateMin;
            }
            return false;
        }

        public override bool IsIgnoreByRsrp(TestPoint tp)
        {
            if (CheckRsrp)
            {
                float? rsrp = (float?)tp["lte_fdd_RSRP"];
                return !(RsrpMin <= rsrp && rsrp <= RsrpMax);
            }
            return false;
        }

        public override bool IsIgnoreBySinr(TestPoint tp)
        {
            if (CheckSinr)
            {
                float? sinr = (float?)tp["lte_fdd_SINR"];
                return !(SinrMin <= sinr && sinr <= SinrMax);
            }
            return false;
        }
    }
}
