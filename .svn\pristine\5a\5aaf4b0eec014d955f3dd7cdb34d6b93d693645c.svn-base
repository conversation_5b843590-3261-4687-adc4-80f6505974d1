﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class FileProjectRecordResultForm : MinCloseForm
    {
        public FileProjectRecordResultForm(MainModel mModel) : base(mModel)
        {
            InitializeComponent();

            miExportExcel.Click += MiExportExcel_Click;
        }

        public void FillData(object result)
        {
            gridControl1.DataSource = result;
            gridControl1.RefreshDataSource();
        }

        private void MiExportExcel_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(gridView1);
        }
    }
}
