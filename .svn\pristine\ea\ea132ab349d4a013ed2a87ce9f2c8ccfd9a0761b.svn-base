﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Data;

using MasterCom.NOP.WF.Core;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.NOP
{
    public class PredealReasonStatRow : ReasonStatRow
    {
        public PredealReasonStatRow(string cityName) : base(cityName)
        {
            this.reasonCnts = new int[sIntegerColumnNames.Length];
        }

        public override void ProcessOrder(Task task)
        {
            string reason = task.GetValue("深入分析主要原因") as string;
            if (string.IsNullOrEmpty(reason))
            {
                reason = "其他";
            }

            int index = 0;
            for (; index < sIntegerColumnNames.Length; ++index)
            {
                if (sIntegerColumnNames[index] == reason)
                {
                    break;
                }
            }

            if (index >= sIntegerColumnNames.Length)
            {
                index = sIntegerColumnNames.Length - 1; // 未找到任何原因，默认为最后一个，即"其他"
            }
            ++reasonCnts[index];
        }

        public new static DataColumn[] DataColumns
        {
            get
            {
                List<DataColumn> gridColumns = new List<DataColumn>();

                DataColumn column = new DataColumn();
                column.ColumnName = "地市名称";
                column.DataType = typeof(string);
                gridColumns.Add(column);

                foreach (string columnName in sIntegerColumnNames)
                {
                    AddIntegerDataColumn(gridColumns, columnName);
                }

                return gridColumns.ToArray();
            }
        }

        private static string[] sIntegerColumnNames = new string[] {
                "故障",
                "弱覆盖",
                "质差",
                "高干扰问题",
                "资源问题",
                "射频优化问题",
                "其他" };
    }

    public class PredealReasonStater : ReasonStater
    {
        protected new ReasonStatRow GetStatRow(string key)
        {
            return new PredealReasonStatRow(key);
        }

        protected override DataColumn[] GetColumns()
        {
            return PredealReasonStatRow.DataColumns;
        }
    }
}
