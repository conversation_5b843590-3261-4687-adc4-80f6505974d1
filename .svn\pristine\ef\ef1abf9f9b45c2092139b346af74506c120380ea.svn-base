﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.BackgroundFunc;

namespace MasterCom.RAMS.Func
{
    public class DIYAnalyseByCellBackgroundBaseByFileOfPeriod : DIYAnalyseByCellBackgroundBaseByFile
    {
        public DIYAnalyseByCellBackgroundBaseByFileOfPeriod(MainModel mainModel)
            : base(mainModel)
        {
        }

        public override BackgroundStatType StatType
        {
            get { return BackgroundStatType.Cell_Region; }
        }

        protected override void doBackgroundStatByFile(ClientProxy clientProxy)
        {
            doBackgroundStatByPeriod(clientProxy);
        }

        protected override List<TimePeriod> GetStatedTimePeriod()
        {
            return BackgroundFuncQueryManager.GetInstance().GetStatedTimePeriod_Cell(GetSubFuncID(), BackgroundFuncBaseSetting.GetInstance().projectType);
        }

        protected override void statData(ClientProxy clientProxy)
        {
            getFilesForAnalyse();
            analyseFiles();
        }

        protected override void getFilesForAnalyse()
        {
            //base.getFilesForAnalyse();
            BackgroundFuncQueryManager.GetInstance().GetFile_Cell(GetSubFuncID(), ServiceTypeString, ((int)carrierID).ToString(),
                Condition.Periods[0].IBeginTime, Condition.Periods[0].IEndTime);
        }

        protected override void replay()
        {
            QueryCondition condition = new QueryCondition();
            condition.QueryType = Condition.QueryType;
            condition.FileInfos.AddRange(Condition.FileInfos);
            condition.Geometorys = Condition.Geometorys;
            DIYReplayFileWithNoWaitBox query = new DIYReplayFileWithNoWaitBox(MainModel);
            query.FilterSampleByRegion = FilterSampleByRegion;
            query.FilterEventByRegion = FilterEventByRegion;
            query.IncludeEvent = IncludeEvent;
            query.IncludeMessage = IncludeMessage;
            query.IncludeAllRtpMessage = IncludeAllRtpMessage;
            query.SetQueryCondition(condition);
            query.Query();
            doStatWithQuery();
            MainModel.ClearDTData();
        }

        protected override bool stopQuery()
        {
            return false;
        }
    }
}
