using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.Frame
{
    public partial class UpdateListViewDlg : BaseFormStyle
    {
        public UpdateListViewDlg()
        {
            InitializeComponent();
        }

        internal void FireShowList(List<UpdateNote> updateNoteList)
        {
            foreach (UpdateNote un in updateNoteList)
            {
                ListViewItem listViewItem = new System.Windows.Forms.ListViewItem();
                listViewItem.Tag = un;
                listViewItem.Text = un.DateTimeString;
                listViewItem.SubItems.Add(un.desc);
                listView.Items.Add(listViewItem);
            }
        }
    }
}