﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Data.SqlClient;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.Func
{
    public partial class DataBaseConnection : UserControl
    {
        public DataBaseConnection()
        {
            InitializeComponent();
        }

        public void Init(string title)
        {
            gc.Text = title;
        }

        public void SetCondition(DataBaseCondition condition)
        {
            if (condition == null)
            {
                return;
            }
            txtIPPort.Text = condition.IP;
            txtDBName.Text = condition.DBName;
            txtUsrName.Text = condition.User;
            txtPW.Text = condition.PW;
        }

        public DataBaseCondition GetCondition()
        {
            var condition = new DataBaseCondition
            {
                IP = txtIPPort.Text,
                DBName = txtDBName.Text,
                User = txtUsrName.Text,
                PW = txtPW.Text
            };
            bool isValid = condition.JudgeConnectValid();
            if (isValid)
            {
                return condition;
            }
            else
            {
                return null;
            }
        }
    }

    public class DataBaseCondition
    {
        public string IP { get; set; } = "";
        public string DBName { get; set; } = "";
        public string User { get; set; } = "";
        public string PW { get; set; } = "";

        public SqlConnectionStringBuilder SqlConnect { get; set; } = new SqlConnectionStringBuilder();

        public bool JudgeConnectValid()
        {
            if (string.IsNullOrEmpty(IP) ||
                string.IsNullOrEmpty(DBName) ||
                string.IsNullOrEmpty(User) ||
                string.IsNullOrEmpty(PW))
            {
                return false;
            }

            SqlConnect.DataSource = IP;
            SqlConnect.InitialCatalog = DBName;
            SqlConnect.UserID = User;
            SqlConnect.Password = PW;
            return true;
        }
    }
}
