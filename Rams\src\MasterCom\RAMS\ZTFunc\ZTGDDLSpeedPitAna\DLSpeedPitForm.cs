﻿using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class DLSpeedPitForm : MinCloseForm
    {
        public DLSpeedPitForm(MainModel mainModel)
            : base(mainModel)
        {
            InitializeComponent();
        }

        public void FillData(List<DLSpeedPitInfo> pitInfoList)
        {
            BindingSource source = new BindingSource();
            source.DataSource = pitInfoList;
            gridData.DataSource = source;
            gridData.RefreshDataSource();
        }

        private void outPutExcel_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(gv);
        }

        private void miRePlay_Click(object sender, EventArgs e)
        {
            int[] row = gv.GetSelectedRows();
            if (row.Length == 0)
                return;
            object o = gv.GetRow(row[0]);

            DLSpeedPitInfo dlSpeedInfo = o as DLSpeedPitInfo;
            DateTime dtime = dlSpeedInfo.TpPitStart.DateTime;
            MasterCom.RAMS.Model.Interface.FileReplayer.ReplayOnePart(dlSpeedInfo.FileInfoMsg, dtime);
        }
    }
}
