﻿using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Imaging;
using System.Text;
using Excel = Microsoft.Office.Interop.Excel;

namespace MasterCom.RAMS.BackgroundFunc
{
    public class KpiFileKey
    {
        #region instance
        protected static readonly object lockObj = new object();
        private static KpiFileKey instance = null;
        public static KpiFileKey GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new KpiFileKey();
                    }
                }
            }
            return instance;
        }
        #endregion

        public KpiFileKey()
        {
            KeyFileCorrespond = init();
        }

        public Dictionary<KpiKey_TJ, KpiFile_TJ> KeyFileCorrespond { get; private set; }

        private Dictionary<KpiKey_TJ, KpiFile_TJ> init()
        {
            Dictionary<KpiKey_TJ, KpiFile_TJ> key = new Dictionary<KpiKey_TJ, KpiFile_TJ>();
            key.Add(KpiKey_TJ.DLAvgRsrp, KpiFile_TJ.DL);
            key.Add(KpiKey_TJ.DLAvgSinr, KpiFile_TJ.DL);
            key.Add(KpiKey_TJ.DLMaxRsrp, KpiFile_TJ.DL);
            key.Add(KpiKey_TJ.DLMaxSinr, KpiFile_TJ.DL);
            key.Add(KpiKey_TJ.DLEdgeRsrp, KpiFile_TJ.DL);
            key.Add(KpiKey_TJ.DLEdgeSinr, KpiFile_TJ.DL);
            key.Add(KpiKey_TJ.DLAvgThroughput, KpiFile_TJ.DL);
            key.Add(KpiKey_TJ.DLMaxThroughput, KpiFile_TJ.DL);
            key.Add(KpiKey_TJ.DLEdgeThroughput, KpiFile_TJ.DL);
            key.Add(KpiKey_TJ.DLEarfcn, KpiFile_TJ.DL);
            key.Add(KpiKey_TJ.DLPci, KpiFile_TJ.DL);
            key.Add(KpiKey_TJ.DLCover, KpiFile_TJ.DL);

            key.Add(KpiKey_TJ.ULAvgThroughput, KpiFile_TJ.UL);
            key.Add(KpiKey_TJ.ULMaxThroughput, KpiFile_TJ.UL);
            key.Add(KpiKey_TJ.ULEdgeThroughput, KpiFile_TJ.UL);

            key.Add(KpiKey_TJ.PingRate, KpiFile_TJ.PING);
            key.Add(KpiKey_TJ.PingDelay, KpiFile_TJ.PING);

            key.Add(KpiKey_TJ.CsfbRate, KpiFile_TJ.CSFB);
            key.Add(KpiKey_TJ.CsfbDelay, KpiFile_TJ.CSFB);

            key.Add(KpiKey_TJ.DTAvgRsrp, KpiFile_TJ.Handover);
            key.Add(KpiKey_TJ.DTAvgSinr, KpiFile_TJ.Handover);
            key.Add(KpiKey_TJ.DTMaxRsrp, KpiFile_TJ.Handover);
            key.Add(KpiKey_TJ.DTMaxSinr, KpiFile_TJ.Handover);
            key.Add(KpiKey_TJ.DTEdgeRsrp, KpiFile_TJ.Handover);
            key.Add(KpiKey_TJ.DTEdgeSinr, KpiFile_TJ.Handover);

            key.Add(KpiKey_TJ.DTHandOver, KpiFile_TJ.Handover);
            key.Add(KpiKey_TJ.DTCover, KpiFile_TJ.Handover);

            key.Add(KpiKey_TJ.DTRsrpPic, KpiFile_TJ.Handover);
            key.Add(KpiKey_TJ.DTSinrPic, KpiFile_TJ.Handover);
            key.Add(KpiKey_TJ.DTHandOverPic, KpiFile_TJ.Handover);

            key.Add(KpiKey_TJ.DTOverLapRate, KpiFile_TJ.Handover);
            key.Add(KpiKey_TJ.DTRRCRate, KpiFile_TJ.Handover);
            key.Add(KpiKey_TJ.DTHandOverRate, KpiFile_TJ.Handover);
            key.Add(KpiKey_TJ.DTAntennaOpposite, KpiFile_TJ.Handover);
            return key;
        }
    }

    public enum KpiFile_TJ
    {
        None,
        DL,
        UL,
        PING,
        CSFB,
        Handover
    }

    public enum KpiKey_TJ
    {
        DLAvgRsrp = 0x04030000,
        DLAvgSinr = 0x04030001,
        DLMaxRsrp = 0x04030002,
        DLMaxSinr = 0x04030003,
        DLEdgeRsrp = 0x04030004,
        DLEdgeSinr = 0x04030005,
        DLAvgThroughput = 0x04030006,
        DLMaxThroughput = 0x04030007,
        DLEdgeThroughput = 0x04030008,
        DLEarfcn = 0x040300009,
        DLPci = 0x04030000A,
        DLCover = 0x04030000B,

        ULAvgThroughput = 0x04030010,
        ULMaxThroughput = 0x04030011,
        ULEdgeThroughput = 0x04030012,

        PingRate = 0x04030020,
        PingDelay = 0x04030021,

        CsfbRate = 0x04030030,
        CsfbDelay = 0x04030031,

        DTAvgRsrp = 0x04030040,
        DTAvgSinr = 0x04030041,
        DTMaxRsrp = 0x04030042,
        DTMaxSinr = 0x04030043,
        DTEdgeRsrp = 0x04030044,
        DTEdgeSinr = 0x04030045,
        DTEarfcn = 0x040300046,
        DTPci = 0x040300047,
        DTHandOver = 0x04030048,
        DTCover = 0x04030049,
        DTRsrpPic = 0x0403004A,
        DTSinrPic = 0x0403004B,
        DTHandOverPic = 0x0403004C,
        DTOverLapRate = 0x04030004D,
        DTRRCRate = 0x04030004E,
        DTHandOverRate = 0x04030004F,
        DTAntennaOpposite = 0x04030050,

        InterOperationNum = 0x04030060,
        InterOperationCsfbRate = 0x04030061,
        InterOperationCsfbDelay= 0x04030062,
        InterOperationFRDelay = 0x04030063
    }

    public enum AnaType
    {
        //1个文件分析1个小区
        AnaCell,
        //1个文件分析多个小区
        AnaMultiCells,
        //多个文件分析的结果汇聚成一个结果
        AnaMultiFiles
    }

    public abstract class CellAcceptKpiAna_TianJin
    {
        public AnaType IsAnaBtsInfo { get; set; } = AnaType.AnaCell;

        protected List<int> evtSuccList = new List<int>();
        protected List<int> evtFailList = new List<int>();
        protected List<int> evtRequList = new List<int>();

        public virtual bool IsValidFile(FileInfo fileInfo)
        {
            return false;
        }

        protected static readonly log4net.ILog log = log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        public virtual Dictionary<KpiKey_TJ, object> GetFileKpiInfos(FileInfo fileInfo
            , DTFileDataManager fileManager, LTECell targetCell)
        {
            CellKpi kpiCell = anaFile(fileInfo, fileManager, targetCell);

            if (filterDataInfo(kpiCell, fileInfo))
            {
                return getKpiInfos(kpiCell);
            }
            else
            {
                return new Dictionary<KpiKey_TJ, object>();
            }
        }

        protected virtual CellKpi anaFile(FileInfo fileInfo, DTFileDataManager fileManager, LTECell targetCell)
        {
            CellKpi targetKpiCell = new CellKpi(targetCell.Name);
            judgeEventSuccessRate(fileManager, targetKpiCell);

            //获取平均RSRP,SINR,判断RSRP,SINR是否满足条件
            getTestPointKpi(fileManager.TestPoints, targetCell, targetKpiCell);

            return targetKpiCell;
        }

        protected virtual void judgeEventSuccessRate(DTFileDataManager fileManager, CellKpi targetKpiCell)
        {
            //判断事件成功率
            foreach (Event evt in fileManager.Events)
            {
                if (evtRequList.Contains(evt.ID))
                {
                    ++targetKpiCell.RequestCnt;
                }
                else if (evtSuccList.Contains(evt.ID))
                {
                    ++targetKpiCell.SucceedCnt;
                }
                else if (evtFailList.Contains(evt.ID))
                {
                    ++targetKpiCell.FailedCnt;
                }
            }
        }

        protected virtual Dictionary<KpiKey_TJ, object> getKpiInfos(CellKpi kpiCell)
        {
            Dictionary<KpiKey_TJ, object> kpiInfos = new Dictionary<KpiKey_TJ, object>();
            return kpiInfos;
        }

        protected virtual bool filterDataInfo(CellKpi kpiCell, FileInfo fileInfo)
        {
            return true;
        }

        protected virtual void getTestPointKpi(List<TestPoint> testPoints, LTECell targetCell, CellKpi targetKpiCell)
        {
            foreach (TestPoint tp in testPoints)
            {
                LTECell cell = getTpSrcCell(tp);
                if (cell != null && cell.Token == targetCell.Token)
                {
                    targetKpiCell.AddPoint(tp);
                }
            }
        }

        protected LTECell getTpSrcCell(TestPoint tp)
        {
            return StationAcceptManager_TianJin.GetTpSrcCell(tp);
        }

        protected void reportInfo(string strInfo)
        {
            if (MainModel.GetInstance().BackgroundStarted)
            {
                BackgroundFuncManager.GetInstance().ReportBackgroundInfo(strInfo);
            }
            else
            {
                log.Info(strInfo);
            }
        }

        protected void reportInfo(Exception ex)
        {
            if (MainModel.GetInstance().BackgroundStarted)
            {
                BackgroundFuncManager.GetInstance().ReportBackgroundError(ex);
            }
            else
            {
                System.Windows.Forms.MessageBox.Show(ex.Message + Environment.NewLine + ex.Source + Environment.NewLine + ex.StackTrace);
            }
        }

        public class CellKpi
        {
            public CellKpi(string cellName)
            {
                this.CellName = cellName;
            }
            public string CellName { get; set; }
            public int PointCount { get; protected set; }

            protected double sumSinr;
            protected int cntSinr;

            protected double sumRsrp;
            protected int cntRsrp;

            public double AvgSinr { get; set; } = double.MinValue;
            public double AvgRsrp { get; set; } = double.MinValue;

            //事件请求数
            public int RequestCnt { get; set; }
            //事件成功数
            public int SucceedCnt { get; set; }
            //事件失败数
            public int FailedCnt { get; set; }
            //成功率
            public double SuccessRate
            {
                get
                {
                    if (RequestCnt != 0)
                    {
                        return Math.Round(SucceedCnt * 1d / RequestCnt, 4);
                    }
                    else if (SucceedCnt + FailedCnt != 0)
                    {
                        return Math.Round(SucceedCnt * 1d / (SucceedCnt + FailedCnt), 4);
                    }
                    else
                    {
                        return 0;
                    }
                }
            }

            public virtual void AddPoint(TestPoint tp)
            {
                ++PointCount;

                float? sinr = (float?)tp["lte_fdd_SINR"];
                if (sinr != null && sinr != -10000000)
                {
                    ++cntSinr;
                    sumSinr += (float)sinr;
                }

                float? rsrp = (float?)tp["lte_fdd_RSRP"];
                if (rsrp != null && rsrp != -10000000)
                {
                    ++cntRsrp;
                    sumRsrp += (float)rsrp;
                }
            }

            public virtual void CalculateFinalRes()
            {
                if (cntRsrp > 0)
                {
                    AvgRsrp = Math.Round(sumRsrp / cntRsrp, 2);
                }
                else
                {
                    AvgRsrp = double.MinValue;
                }

                if (cntSinr > 0)
                {
                    AvgSinr = Math.Round(sumSinr / cntSinr, 2);
                }
                else
                {
                    AvgSinr = double.MinValue;
                }
            }
        }
    }

    class AcpAutoFtpDL_TianJin : CellAcceptKpiAna_TianJin
    {
        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("CQT好点下载");
        }

        public override Dictionary<KpiKey_TJ, object> GetFileKpiInfos(FileInfo fileInfo
            , DTFileDataManager fileManager, LTECell targetCell)
        {
            CellKpi kpiCell = anaFile(fileInfo, fileManager, targetCell);
            if (kpiCell == null)
            {
                reportInfo(string.Format("文件{0}获取目标小区信息失败", fileInfo.Name));
                return new Dictionary<KpiKey_TJ, object>();
            }

            if (filterDataInfo(kpiCell, fileInfo))
            {
                return getKpiInfos(kpiCell);
            }
            else
            {
                return new Dictionary<KpiKey_TJ, object>();
            }
        }

        protected override CellKpi anaFile(FileInfo fileInfo, DTFileDataManager fileManager, LTECell targetCell)
        {
            CellKpi targetKpiCell = new DLCellKpi(targetCell.Name);
            getTestPointKpi(fileManager.TestPoints, targetCell, targetKpiCell);
            targetKpiCell.CalculateFinalRes();
            return targetKpiCell;
        }

        protected override void getTestPointKpi(List<TestPoint> testPoints, LTECell targetCell, CellKpi targetKpiCell)
        {
            foreach (TestPoint tp in testPoints)
            {
                LTECell cell = getTpSrcCell(tp);
                if (cell != null && cell.Token == targetCell.Token)
                {
                    // as DLCellKpi
                    targetKpiCell.AddPoint(tp);
                }
            }
        }

        protected override Dictionary<KpiKey_TJ, object> getKpiInfos(CellKpi kpiCell)
        {
            DLCellKpi cellKpi = kpiCell as DLCellKpi;
            Dictionary<KpiKey_TJ, object> kpiInfos = new Dictionary<KpiKey_TJ, object>();
            kpiInfos.Add(KpiKey_TJ.DLAvgRsrp, cellKpi.AvgRsrp);
            kpiInfos.Add(KpiKey_TJ.DLAvgSinr, cellKpi.AvgSinr);
            kpiInfos.Add(KpiKey_TJ.DLMaxRsrp, cellKpi.MaxRsrp);
            kpiInfos.Add(KpiKey_TJ.DLMaxSinr, cellKpi.MaxSinr);
            kpiInfos.Add(KpiKey_TJ.DLEdgeRsrp, cellKpi.EdgeRsrp);
            kpiInfos.Add(KpiKey_TJ.DLEdgeSinr, cellKpi.EdgeSinr);
            kpiInfos.Add(KpiKey_TJ.DLAvgThroughput, cellKpi.AvgMacDL);
            kpiInfos.Add(KpiKey_TJ.DLMaxThroughput, cellKpi.MaxDL);
            kpiInfos.Add(KpiKey_TJ.DLEdgeThroughput, cellKpi.EdgeMacDL);
            kpiInfos.Add(KpiKey_TJ.DLPci, cellKpi.PCI);
            kpiInfos.Add(KpiKey_TJ.DLEarfcn, cellKpi.EARFCN);
            return kpiInfos;
        }

        public class DLCellKpi : CellKpi
        {
            public DLCellKpi(string cellName)
                : base(cellName)
            {

            }

            public double PCI { get; set; } = double.MinValue;
            public double EARFCN { get; set; } = double.MinValue;

            protected Dictionary<int, int> pciList = new Dictionary<int, int>();
            protected Dictionary<int, int> earfcnList = new Dictionary<int, int>();

            protected List<double> rsrpList = new List<double>();
            protected List<double> sinrList = new List<double>();
            protected List<double> dlSpeedList = new List<double>();

            public double EdgeRsrp { get; set; } = double.MinValue;
            public double EdgeSinr { get; set; } = double.MinValue;
            public double EdgeMacDL { get; set; } = double.MinValue;

            protected double sumMacDL;
            protected int cntMacDL;
            public double AvgMacDL { get; protected set; } = double.MinValue;
            public double MaxDL { get; protected set; } = double.MinValue;

            public double MaxRsrp { get; protected set; } = double.MinValue;
            public double MaxSinr { get; protected set; } = double.MinValue;

            public override void AddPoint(TestPoint tp)
            {
                ++PointCount;

                addRsrpSinr(tp);

                addEarfcnPci(tp);

                addMacDL(tp);
            }

            protected void addRsrpSinr(TestPoint tp)
            {
                float? rsrp = (float?)tp["lte_fdd_RSRP"];
                if (rsrp != null && rsrp != -10000000)
                {
                    float curRsrp = (float)rsrp;
                    ++cntRsrp;
                    sumRsrp += curRsrp;
                    if (MaxRsrp < curRsrp)
                    {
                        MaxRsrp = curRsrp;
                    }
                    rsrpList.Add(curRsrp);
                }

                float? sinr = (float?)tp["lte_fdd_SINR"];
                if (sinr != null && sinr != -10000000)
                {
                    float curSinr = (float)sinr;
                    ++cntSinr;
                    sumSinr += curSinr;
                    if (MaxSinr < curSinr)
                    {
                        MaxSinr = curSinr;
                    }
                    sinrList.Add(curSinr);
                }
            }

            protected void addEarfcnPci(TestPoint tp)
            {
                int? earfcn = (int?)tp["lte_fdd_EARFCN"];
                if (earfcn != null)
                {
                    int curEarfcn = (int)earfcn;
                    int count;
                    if (!earfcnList.TryGetValue(curEarfcn, out count))
                    {
                        earfcnList.Add(curEarfcn, 0);
                    }
                    earfcnList[curEarfcn]++;
                }

                int? pci = (short?)tp["lte_fdd_PCI"];
                if (pci != null)
                {
                    int curPci = (int)pci;
                    int count;
                    if (!pciList.TryGetValue(curPci, out count))
                    {
                        pciList.Add(curPci, 0);
                    }
                    pciList[curPci]++;
                }
            }

            protected void addMacDL(TestPoint tp)
            {
                int? macDL = (int?)tp["lte_fdd_MAC_DL"];
                if (macDL != null)
                {
                    int curMacDL = (int)macDL;
                    ++cntMacDL;
                    sumMacDL += curMacDL;
                    if (MaxDL < macDL)
                    {
                        MaxDL = curMacDL;
                    }
                    dlSpeedList.Add(curMacDL);
                }
            }

            public override void CalculateFinalRes()
            {
                calculateRsrp();

                calculateSinr();

                calculateMacDL();

                calculateEarfcnPci();
            }

            protected void calculateRsrp()
            {
                if (cntRsrp > 0)
                {
                    AvgRsrp = Math.Round(sumRsrp / cntRsrp, 2);

                    //最差的百分之五的点算边缘值
                    int lastCount = (int)(cntRsrp * 0.05);
                    rsrpList.Sort();
                    double rsrpSumTmp = 0;
                    for (int i = 0; i < lastCount; i++)
                    {
                        rsrpSumTmp += rsrpList[i];
                    }
                    EdgeRsrp = Math.Round(rsrpSumTmp / lastCount, 2);
                }
                else
                {
                    MaxRsrp = double.MinValue;
                    AvgRsrp = double.MinValue;
                    EdgeRsrp = double.MinValue;
                }
            }

            protected void calculateSinr()
            {
                if (cntSinr > 0)
                {
                    AvgSinr = Math.Round(sumSinr / cntSinr, 2);

                    int lastCount = (int)(cntSinr * 0.05);
                    sinrList.Sort();
                    double sinrSumTmp = 0;
                    for (int i = 0; i < lastCount; i++)
                    {
                        sinrSumTmp += sinrList[i];
                    }
                    EdgeSinr = Math.Round(sinrSumTmp / lastCount, 2);
                }
                else
                {
                    MaxSinr = double.MinValue;
                    AvgSinr = double.MinValue;
                    EdgeSinr = double.MinValue;
                }
            }

            protected void calculateMacDL()
            {
                if (cntMacDL > 0)
                {
                    MaxDL = Math.Round(MaxDL / (1000 * 1000), 2);
                    AvgMacDL = Math.Round(sumMacDL / cntMacDL / (1000 * 1000), 2);

                    int lastCount = (int)(cntMacDL * 0.05);
                    dlSpeedList.Sort();
                    double dlSumTmp = 0;
                    for (int i = 0; i < lastCount; i++)
                    {
                        dlSumTmp += dlSpeedList[i];
                    }
                    EdgeMacDL = Math.Round(dlSumTmp / lastCount / (1000 * 1000), 2);
                }
                else
                {
                    MaxDL = double.MinValue;
                    AvgMacDL = double.MinValue;
                    EdgeMacDL = double.MinValue;
                }
            }

            protected void calculateEarfcnPci()
            {
                int maxCount = 0;
                foreach (var earfcn in earfcnList)
                {
                    if (earfcn.Value > maxCount)
                    {
                        maxCount = earfcn.Value;
                        EARFCN = earfcn.Key;
                    }
                }
                maxCount = 0;
                foreach (var pci in pciList)
                {
                    if (pci.Value > maxCount)
                    {
                        maxCount = pci.Value;
                        PCI = pci.Key;
                    }
                }
            }
        }
    }

    class AcpAutoFtpUL_TianJin : CellAcceptKpiAna_TianJin
    {
        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("CQT好点上传");
        }

        protected override CellKpi anaFile(FileInfo fileInfo, DTFileDataManager fileManager, LTECell targetCell)
        {
            CellKpi targetKpiCell = new ULCellKpi(targetCell.Name);
            getTestPointKpi(fileManager.TestPoints, targetCell, targetKpiCell);
            targetKpiCell.CalculateFinalRes();
            return targetKpiCell;
        }

        protected override Dictionary<KpiKey_TJ, object> getKpiInfos(CellKpi kpiCell)
        {
            ULCellKpi cellKpi = kpiCell as ULCellKpi;
            Dictionary<KpiKey_TJ, object> kpiInfos = new Dictionary<KpiKey_TJ, object>();
            kpiInfos.Add(KpiKey_TJ.ULMaxThroughput, cellKpi.MaxUL);
            kpiInfos.Add(KpiKey_TJ.ULAvgThroughput, cellKpi.AvgMacUL);
            kpiInfos.Add(KpiKey_TJ.ULEdgeThroughput, cellKpi.EdgeMacUL);
            return kpiInfos;
        }

        public class ULCellKpi : CellKpi
        {
            public ULCellKpi(string cellName)
                : base(cellName)
            {

            }

            protected List<double> ulSpeedList = new List<double>();
            public double EdgeMacUL { get; protected set; } = double.MinValue;

            protected double sumMacUL;
            protected int cntMacUL;
            public double AvgMacUL { get; protected set; } = double.MinValue;
            public double MaxUL { get; protected set; } = double.MinValue;

            public override void AddPoint(TestPoint tp)
            {
                int? macUL = (int?)tp["lte_fdd_MAC_UL"];
                if (macUL != null)
                {
                    int curMacUL = (int)macUL;
                    ++cntMacUL;
                    sumMacUL += curMacUL;
                    if (MaxUL < macUL)
                    {
                        MaxUL = curMacUL;
                    }
                    ulSpeedList.Add(curMacUL);
                }
            }

            public override void CalculateFinalRes()
            {
                if (cntMacUL > 0)
                {
                    MaxUL = Math.Round(MaxUL / (1000 * 1000), 2);
                    AvgMacUL = Math.Round(sumMacUL / cntMacUL / (1000 * 1000), 2);

                    int lastCount = (int)(cntMacUL * 0.05);
                    ulSpeedList.Sort();
                    double ulSumTmp = 0;
                    for (int i = 0; i < lastCount; i++)
                    {
                        ulSumTmp += ulSpeedList[i];
                    }
                    EdgeMacUL = Math.Round(ulSumTmp / lastCount / (1000 * 1000), 2);
                }
                else
                {
                    MaxUL = double.MinValue;
                    AvgMacUL = double.MinValue;
                    EdgeMacUL = double.MinValue;
                }
            }
        }
    }

    class AcpAutoPing_TianJin : CellAcceptKpiAna_TianJin
    {
        public AcpAutoPing_TianJin()
        {
            evtSuccList = new List<int> { 3555 };
            evtFailList = new List<int> { 3556 };
        }

        public override bool IsValidFile(FileInfo fileInfo)
        {
            //CQT好点PING 暂时提供的测试log与命名规范不符
            return fileInfo.Name.Contains("CQTPING");
        }

        protected override CellKpi anaFile(FileInfo fileInfo, DTFileDataManager fileManager, LTECell targetCell)
        {
            PingCellKpi targetKpiCell = new PingCellKpi(targetCell.Name);
            targetKpiCell.EvtList = evtSuccList;
            judgeEventSuccessRate(fileManager, targetKpiCell);
            targetKpiCell.CalculateDelay();
            return targetKpiCell;
        }

        protected override void judgeEventSuccessRate(DTFileDataManager fileManager, CellKpi targetKpiCell)
        {
            //判断事件成功率
            foreach (Event evt in fileManager.Events)
            {
                if (evtSuccList.Contains(evt.ID))
                {
                    ++targetKpiCell.SucceedCnt;
                }
                else if (evtFailList.Contains(evt.ID))
                {
                    ++targetKpiCell.FailedCnt;
                }
                ((PingCellKpi)targetKpiCell).AddTotalEvtList(evt);
            }
        }

        protected override Dictionary<KpiKey_TJ, object> getKpiInfos(CellKpi kpiCell)
        {
            PingCellKpi cellKpi = kpiCell as PingCellKpi;
            Dictionary<KpiKey_TJ, object> kpiInfos = new Dictionary<KpiKey_TJ, object>();
            kpiInfos.Add(KpiKey_TJ.PingDelay, cellKpi.AvgDelay);
            kpiInfos.Add(KpiKey_TJ.PingRate, cellKpi.SuccessRate);
            return kpiInfos;
        }

        public class PingCellKpi : CellKpi
        {
            public PingCellKpi(string cellName)
                : base(cellName)
            {

            }

            public List<Event> TotalEvtList = new List<Event>();

            /// <summary>
            /// 有效的测试次数
            /// </summary>
            public int TestCount { get; protected set; }

            /// <summary>
            /// 平均时延(毫秒)
            /// </summary>
            public double AvgDelay { get; protected set; }

            public List<int> EvtList { get; set; } = new List<int>();

            public void AddTotalEvtList(Event evt)
            {
                if (EvtList.Contains(evt.ID))
                {
                    TotalEvtList.Add(evt);
                }
            }

            public virtual void CalculateDelay()
            {
                double sumDelay = 0;
                int validEvtNum = 0;
                foreach (Event evt in TotalEvtList)
                {
                    object delay = evt["Value1"];
                    if (delay != null)
                    {
                        sumDelay += int.Parse(delay.ToString());
                        validEvtNum++;
                    }
                }
                if (validEvtNum > 0)
                {
                    TestCount = validEvtNum;
                    AvgDelay = sumDelay / validEvtNum;
                }
                else
                {
                    AvgDelay = double.MinValue;
                }
            }
        }
    }

    class AcpAutoCSFB_TianJin : AcpAutoPing_TianJin
    {
        public AcpAutoCSFB_TianJin()
        {
            evtRequList = new List<int> { 3070 };
            evtSuccList = new List<int> { 3072 };
        }

        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("CQTCSFB") && fileInfo.Momt == 1;
        }

        protected override CellKpi anaFile(FileInfo fileInfo, DTFileDataManager fileManager, LTECell targetCell)
        {
            CsfbCellKpi targetKpiCell = new CsfbCellKpi(targetCell.Name);
            targetKpiCell.EvtList = new List<int>() { 3012 };
            judgeEventSuccessRate(fileManager, targetKpiCell);
            targetKpiCell.CalculateDelay();
            return targetKpiCell;
        }

        protected override Dictionary<KpiKey_TJ, object> getKpiInfos(CellKpi kpiCell)
        {
            CsfbCellKpi cellKpi = kpiCell as CsfbCellKpi;
            Dictionary<KpiKey_TJ, object> kpiInfos = new Dictionary<KpiKey_TJ, object>();
            kpiInfos.Add(KpiKey_TJ.CsfbDelay, cellKpi.AvgDelay);
            kpiInfos.Add(KpiKey_TJ.CsfbRate, cellKpi.SuccessRate);
            return kpiInfos;
        }

        public class CsfbCellKpi : PingCellKpi
        {
            public CsfbCellKpi(string cellName)
                : base(cellName)
            {

            }

            public override void CalculateDelay()
            {
                base.CalculateDelay();
                AvgDelay = Math.Round(AvgDelay / 1000, 2);
            }
        }
    }

    class AcpAutoInterOperation_TianJin : CellAcceptKpiAna_TianJin
    {
        public AcpAutoInterOperation_TianJin()
        {
            IsAnaBtsInfo = AnaType.AnaMultiFiles;
        }

        public CsfbCellKpi TargetKpiCell { get; set; }

        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("CQTCSFB") && fileInfo.Momt == 1;
        }

        public Dictionary<KpiKey_TJ, object> DealFinalData(List<CellAcceptKpiAna_TianJin> allFileRes)
        {
            Dictionary<KpiKey_TJ, object> kpiInfos = new Dictionary<KpiKey_TJ, object>();
            double delaySum = 0;
            int count = 0;
            foreach (var res in allFileRes)
            {
                AcpAutoInterOperation_TianJin acp = res as AcpAutoInterOperation_TianJin;
                if (acp.TargetKpiCell.MoInfo.CsfbDelay > 0)
                {
                    count++;
                    delaySum += acp.TargetKpiCell.MoInfo.CsfbDelay;
                }
            }
            if (count > 0)
            {
                AcpAutoInterOperation_TianJin acp = allFileRes[0] as AcpAutoInterOperation_TianJin;
                kpiInfos.Add(KpiKey_TJ.InterOperationNum, acp.TargetKpiCell.MoInfo.PhoneNumber);
                kpiInfos.Add(KpiKey_TJ.InterOperationCsfbRate, acp.TargetKpiCell.MoInfo.CsfbSuccessRate);
                kpiInfos.Add(KpiKey_TJ.InterOperationFRDelay, acp.TargetKpiCell.MoInfo.FRDelay);
                delaySum = Math.Round(delaySum / count, 2);
                kpiInfos.Add(KpiKey_TJ.InterOperationCsfbDelay, delaySum);
            }
          
            return kpiInfos;
        }

        protected override CellKpi anaFile(FileInfo fileInfo, DTFileDataManager fileManager, LTECell targetCell)
        {
            TargetKpiCell = new CsfbCellKpi(targetCell.Name);

            TargetKpiCell.MoInfo.PhoneNumber = getPhoneNumber(fileManager);

            anaCsfbEvt(fileManager, TargetKpiCell, fileInfo.Momt);

            TargetKpiCell.CalculateFinalRes();
            return TargetKpiCell;
        }

        private string getPhoneNumber(DTFileDataManager fileManager)
        {
            int setup3GMsgID = 1899627269;
            string phoneNumber = "";
            foreach (Message msg in fileManager.Messages)
            {
                if (msg.ID == setup3GMsgID && phoneNumber == "")
                {
                    MessageWithSource msgSource = msg as MessageWithSource;
                    switch (msgSource.MoMtFlag)
                    {
                        case 1://MO
                            MessageDecodeHelper.StartDissect(msgSource.Direction, msgSource.Source, msgSource.Length, msg.ID);
                            MessageDecodeHelper.GetSingleString("gsm_a.cld_party_bcd_num", ref phoneNumber);
                            phoneNumber = phoneNumber.Replace("\0", "").Trim();
                            break;
                        case 2://MT
                            MessageDecodeHelper.StartDissect(msgSource.Direction, msgSource.Source, msgSource.Length, msg.ID);
                            MessageDecodeHelper.GetSingleString("gsm_a.clg_party_bcd_num", ref phoneNumber);
                            phoneNumber = phoneNumber.Replace("\0", "").Trim();
                            break;
                    }
                    break;
                }
            }

            return phoneNumber;
        }

        private void anaCsfbEvt(DTFileDataManager fileManager, CsfbCellKpi targetKpiCell, int momt)
        {
            CsfbInfo info;
            if (momt == 1)
            {
                info = targetKpiCell.MoInfo;
            }
            else if (momt == 2)
            {
                info = targetKpiCell.MtInfo;
            }
            else
            {
                return;
            }
            
            foreach (var evt in fileManager.Events)
            {
                if (targetKpiCell.CsfbRequestList.Contains(evt.ID))
                {
                    info.CsfbRequestCount++;
                }
                else if (targetKpiCell.CsfbSuccessList.Contains(evt.ID))
                {
                    info.CsfbSuccessCount++;
                }
                else if (targetKpiCell.CsfbCallList.Contains(evt.ID))
                {
                    //添加CSFB计算时延的事件
                    info.CsfbCallEvtList.Add(evt);
                }
            }

            Message startMsg = null;
            foreach (var msg in fileManager.Messages)
            {
                if (msg.ID == targetKpiCell.RRCConReleaseMsg)
                {
                    startMsg = msg;
                }
                if (startMsg != null && msg.ID == targetKpiCell.TAUCompleteMsg)
                {
                    //记录RRC Release 到TAU Accept的时延
                    info.FRCount++;
                    info.FRDelaySum += msg.lTimeWithMillsecond - startMsg.lTimeWithMillsecond;
                    startMsg = null;
                }
            }
        }

        protected override Dictionary<KpiKey_TJ, object> getKpiInfos(CellKpi kpiCell)
        {
            CsfbCellKpi cellKpi = kpiCell as CsfbCellKpi;
            Dictionary<KpiKey_TJ, object> kpiInfos = new Dictionary<KpiKey_TJ, object>();
            kpiInfos.Add(KpiKey_TJ.InterOperationNum, cellKpi.MoInfo.PhoneNumber);
            kpiInfos.Add(KpiKey_TJ.InterOperationCsfbRate, cellKpi.MoInfo.CsfbSuccessRate);
            kpiInfos.Add(KpiKey_TJ.InterOperationCsfbDelay, cellKpi.MoInfo.CsfbDelay);
            kpiInfos.Add(KpiKey_TJ.InterOperationFRDelay, cellKpi.MoInfo.FRDelay);
            return kpiInfos;
        }

        public class CsfbCellKpi : CellKpi
        {
            public CsfbCellKpi(string cellName)
                : base(cellName)
            {
            }

            public List<int> CsfbRequestList = new List<int>() { 3070, 3071 };
            public List<int> CsfbSuccessList = new List<int>() { 3072, 3073 };
            public List<int> CsfbCallList = new List<int>() { 3012, 3013 };
            
            //public List<int> RrcReleaseList = new List<int>() { 3072, 3073 };
            //public List<int> TauAcceptList = new List<int>() { 3172 };

            public int RRCConReleaseMsg { get; set; } = 1093600016;
            public int TAUCompleteMsg { get; set; } = 1097533258;

            public CsfbInfo MoInfo { get; set; } = new CsfbInfo();
            public CsfbInfo MtInfo { get; set; } = new CsfbInfo();

            public override void CalculateFinalRes()
            {
                MoInfo.CalculateFinalRes();
            }
        }

        public class CsfbInfo
        {
            public FileInfo File { get; set; }
            public string PhoneNumber { get; set; } = "";
            public double CsfbSuccessRate { get; set; } = double.NaN;
            public double CsfbDelay { get; set; } = double.NaN;
            public double FRDelay { get; set; } = double.NaN;

            public int CsfbRequestCount { get; set; }
            public int CsfbSuccessCount { get; set; }
            public List<Event> CsfbCallEvtList = new List<Event>();
            public int FRCount { get; set; }
            public double FRDelaySum { get; set; }

            public void CalculateFinalRes()
            {
                if (CsfbRequestCount > 0)
                {
                    CsfbSuccessRate = Math.Round(CsfbSuccessCount * 1d/ CsfbRequestCount, 4);
                }

                double sumDelay = 0;
                int validEvtNum = 0;
                foreach (Event evt in CsfbCallEvtList)
                {
                    object delay = evt["Value1"];
                    if (delay != null)
                    {
                        sumDelay += int.Parse(delay.ToString());
                        validEvtNum++;
                    }
                }
                if (validEvtNum > 0)
                {
                    CsfbDelay = Math.Round(sumDelay / (validEvtNum * 1000), 2);
                }

                if (FRCount > 0)
                {
                    FRDelay = Math.Round(FRDelaySum / (FRCount * 1000), 2);
                }
            }

            //public FileInfo Mtfile { get; set; }
            //public string MtPhoneNumber { get; set; }
            //public double MtCsfbSuccessRate { get; set; }
            //public double MtCsfbDelay { get; set; }
            //public double MtFRDelay { get; set; }
        }
    }

    class AcpAutoHandover_TianJin : AcpAutoFtpDL_TianJin
    {
        public AcpAutoHandover_TianJin()
        {
            evtRequList = new List<int> { 3155 };
            evtSuccList = new List<int> { 3156 };
        }

        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("DT切换");
        }

        protected override CellKpi anaFile(FileInfo fileInfo, DTFileDataManager fileManager, LTECell targetCell)
        {
            CellKpi targetKpiCell = new DTHandoverCellKpi(targetCell.Name);
            getTestPointKpi(fileManager.TestPoints, targetCell, targetKpiCell);

            anaCellHandOverEvt(fileManager, targetCell, targetKpiCell);

            targetKpiCell.CalculateFinalRes();
            return targetKpiCell;
        }

        #region 切换
        protected void anaCellHandOverEvt(DTFileDataManager fileManager, LTECell targetCell, CellKpi targetKpiCell)
        {
            //1.当前小区切换成功事件数
            List<Event> successdEvtList = getCurCellEvtList(fileManager, targetCell, evtSuccList);
            //2.当前小区切换失败事件数
            List<Event> failedEvtList = getCurCellEvtList(fileManager, targetCell, evtFailList);

            //3.计算切换成功率
            int totalEvt = successdEvtList.Count + failedEvtList.Count;
            if (totalEvt != 0)
            {
                ((DTHandoverCellKpi)targetKpiCell).HandoverRate = Math.Round(successdEvtList.Count * 1d / totalEvt, 4);
            }

            //4.是否与同站间其他小区切换均能成功
            judgeHandoverSuccess(targetKpiCell, successdEvtList);
            //judgeHandoverSuccess(targetCell, targetKpiCell, successdEvtList);
        }

        private List<Event> getCurCellEvtList(DTFileDataManager fileManager, LTECell targetCell, List<int> evtTypeList)
        {
            List<Event> evtList = new List<Event>();
            //获取所有由本小区发起的切换事件
            foreach (Event evt in fileManager.Events)
            {
                if (evtTypeList.Contains(evt.ID))
                {
                    int earfcn;
                    int pci;
                    object objEarfcn = evt["Value2"];
                    object objPci = evt["Value3"];
                    bool isValid = judgeValidEvt(objEarfcn, objPci, out earfcn, out pci);

                    //添加由本小区发起的切换
                    if (isValid && targetCell.EARFCN == earfcn && targetCell.PCI == pci)
                    {
                        evtList.Add(evt);
                    }
                }
            }

            return evtList;
        }

        private bool judgeValidEvt(object objEarfcn, object objPci, out int earfcn, out int pci)
        {
            if (objEarfcn != null && objPci != null)
            {
                earfcn = int.Parse(objEarfcn.ToString());
                pci = int.Parse(objPci.ToString());
                return true;
            }
            earfcn = 0;
            pci = 0;
            return false;
        }

        //判断只要有切换成功就算成功
        private void judgeHandoverSuccess(CellKpi targetKpiCell, List<Event> successdEvtList)
        {
            if (successdEvtList.Count > 0)
            {
                ((DTHandoverCellKpi)targetKpiCell).IsHandOverSuccess = true;
            }
        }

        //判断小区两两切换均成功才为切换成功(原先算法,防止需求再次变更先保留)
        //private void judgeHandoverSuccess(LTECell targetCell, CellKpi targetKpiCell, List<Event> successdEvtList)
        //{
        //    Dictionary<string, bool> cellHandoverRes = new Dictionary<string, bool>();
        //    foreach (var cell in targetCell.BelongBTS.Cells)
        //    {
        //        if (cell.Token != targetCell.Token)
        //        {
        //            cellHandoverRes.Add(cell.Token, false);
        //        }
        //    }

        //    foreach (var evt in successdEvtList)
        //    {
        //        int earfcn;
        //        int pci;
        //        object objEarfcn = evt["Value4"];
        //        object objPci = evt["Value5"];
        //        bool isValid = judgeValidEvt(objEarfcn, objPci, out earfcn, out pci);
        //        if (isValid)
        //        {
        //            List<LTECell> handOverCells = CellManager.GetInstance().GetLTECellListByEARFCNPCI(evt.DateTime, earfcn, pci);
        //            foreach (var cell in handOverCells)
        //            {
        //                if (cellHandoverRes.ContainsKey(cell.Token))
        //                {
        //                    cellHandoverRes[cell.Token] = true;
        //                }
        //            }
        //        }
        //    }

        //    setHandOverRes(targetKpiCell, cellHandoverRes);
        //}

        //private void setHandOverRes(CellKpi targetKpiCell, Dictionary<string, bool> cellHandoverRes)
        //{
        //    //与其他小区均切换成功才算切换成功
        //    foreach (var res in cellHandoverRes)
        //    {
        //        if (!res.Value)
        //        {
        //            return;
        //        }
        //    }
        //    ((DTHandoverCellKpi)targetKpiCell).IsHandOverSuccess = true;
        //}
        #endregion

        protected override Dictionary<KpiKey_TJ, object> getKpiInfos(CellKpi kpiCell)
        {
            DTHandoverCellKpi kpi = kpiCell as DTHandoverCellKpi;
            Dictionary<KpiKey_TJ, object> kpiInfos = new Dictionary<KpiKey_TJ, object>();
            kpiInfos.Add(KpiKey_TJ.DTAvgRsrp, kpi.AvgRsrp);
            kpiInfos.Add(KpiKey_TJ.DTAvgSinr, kpi.AvgSinr);
            kpiInfos.Add(KpiKey_TJ.DTMaxRsrp, kpi.MaxRsrp);
            kpiInfos.Add(KpiKey_TJ.DTMaxSinr, kpi.MaxSinr);
            kpiInfos.Add(KpiKey_TJ.DTEdgeRsrp, kpi.EdgeRsrp);
            kpiInfos.Add(KpiKey_TJ.DTEdgeSinr, kpi.EdgeSinr);

            kpiInfos.Add(KpiKey_TJ.DTHandOver, kpi.IsHandOverSuccess);
            kpiInfos.Add(KpiKey_TJ.DTHandOverRate, kpi.HandoverRate);
            return kpiInfos;
        }

        public class DTHandoverCellKpi : DLCellKpi
        {
            public DTHandoverCellKpi(string cellName)
                : base(cellName)
            {

            }

            public bool IsHandOverSuccess { get; set; }
            
            public double HandoverRate { get; set; } = double.NaN;

            public override void AddPoint(TestPoint tp)
            {
                ++PointCount;

                addRsrpSinr(tp);
            }

            public override void CalculateFinalRes()
            {
                calculateRsrp();

                calculateSinr();
            }
        }
    }

    class AcpAutoAntenna_TianJin : CellAcceptKpiAna_TianJin
    {
        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("DT切换");
        }

        protected override Dictionary<KpiKey_TJ, object> getKpiInfos(CellKpi kpiCell)
        {
            DTCoverCellKpi cellKpi = kpiCell as DTCoverCellKpi;
            Dictionary<KpiKey_TJ, object> kpiInfos = new Dictionary<KpiKey_TJ, object>();
            kpiInfos.Add(KpiKey_TJ.DTAntennaOpposite, cellKpi.IsAntennaOpposite);
            return kpiInfos;
        }

        protected override CellKpi anaFile(FileInfo fileInfo, DTFileDataManager fileManager, LTECell targetCell)
        {
            CellKpi targetKpiCell = new DTCoverCellKpi(targetCell.Name);
            getTestPointKpi(fileManager.TestPoints, targetCell, targetKpiCell);
            targetKpiCell.CalculateFinalRes();
            return targetKpiCell;
        }

        protected override void getTestPointKpi(List<TestPoint> testPoints, LTECell targetCell, CellKpi targetKpiCell)
        {
            foreach (TestPoint tp in testPoints)
            {
                LTECell cell = getTpSrcCell(tp);
                if (cell != null && cell.Token == targetCell.Token)
                {
                    ((DTCoverCellKpi)targetKpiCell).AddPoint(tp, targetCell);
                }
            }
        }

        public class DTCoverCellKpi : CellKpi
        {
            public DTCoverCellKpi(string cellName)
                : base(cellName)
            {
            }

            /// <summary>
            /// 天线是否接反
            /// </summary>
            public bool IsAntennaOpposite { get; set; }

            /// <summary>
            /// 天线接反的采样点数
            /// </summary>
            public int TpCountAntennaOpposite { get; set; }

            public double AntennaOppositeRate { get; private set; }

            /// <summary>
            /// 采样点与小区覆盖方向夹角
            /// </summary>
            /// <param name="longitude"></param>
            /// <param name="latitude"></param>
            /// <returns></returns>
            public double GetTp_CellAngle(TestPoint tp, LTECell srcCell)
            {
                double tpLongitude = tp.Longitude;
                double tpLatitude = tp.Latitude;

                double angleDiff = 0;
                double distance = MathFuncs.GetDistance(srcCell.Longitude, srcCell.Latitude, tpLongitude, tpLatitude);

                ///所有角度按正北方向算起始，顺时针算夹角，正北为0度
                double angle;
                double ygap = MathFuncs.GetDistance(srcCell.Longitude, srcCell.Latitude, srcCell.Longitude, tpLatitude);
                double angleV = Math.Acos(ygap / distance);
                if (tpLongitude >= srcCell.Longitude && tpLatitude >= srcCell.Latitude)//1象限
                {
                    angle = angleV * 180 / Math.PI;
                }
                else if (tpLongitude <= srcCell.Longitude && tpLatitude >= srcCell.Latitude)//2象限
                {
                    angle = 360 - angleV * 180 / Math.PI;
                }
                else if (tpLongitude <= srcCell.Longitude && tpLatitude <= srcCell.Latitude)//3象限
                {
                    angle = 180 + angleV * 180 / Math.PI;
                }
                else//4象限
                {
                    angle = 180 - angleV * 180 / Math.PI;
                }

                angleDiff = Math.Abs(angle - srcCell.Direction);
                if (angleDiff > 180)
                {
                    angleDiff = 360 - angleDiff;
                }
                return angleDiff;
            }

            public virtual void AddPoint(TestPoint tp, LTECell targetCell)
            {
                double tpCellAngle = GetTp_CellAngle(tp, targetCell);
                if (tpCellAngle > 90)
                {
                    TpCountAntennaOpposite++;
                }

                PointCount++;
            }

            public override void CalculateFinalRes()
            {
                if (PointCount > 0)
                {
                    AntennaOppositeRate = Math.Round(TpCountAntennaOpposite * 1d / PointCount, 4);
                    if (AntennaOppositeRate > 0.7)
                    {
                        IsAntennaOpposite = false;
                    }
                    else
                    {
                        IsAntennaOpposite = true;
                    }
                }
            }
        }
    }

    /// <summary>
    /// 小区覆盖测试
    /// </summary>
    class AcpAutoCellCoverTest_TianJin : AcpAutoAntenna_TianJin
    {
        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("CQT好点下载");
        }

        protected override Dictionary<KpiKey_TJ, object> getKpiInfos(CellKpi kpiCell)
        {
            CellCoverTestCellKpi cellKpi = kpiCell as CellCoverTestCellKpi;
            Dictionary<KpiKey_TJ, object> kpiInfos = new Dictionary<KpiKey_TJ, object>();
            kpiInfos.Add(KpiKey_TJ.DLCover, cellKpi.IsCoverDirectionQualified);
            return kpiInfos;
        }

        protected override CellKpi anaFile(FileInfo fileInfo, DTFileDataManager fileManager, LTECell targetCell)
        {
            CellKpi targetKpiCell = new CellCoverTestCellKpi(targetCell.Name);
            getTestPointKpi(fileManager.TestPoints, targetCell, targetKpiCell);
            targetKpiCell.CalculateFinalRes();
            return targetKpiCell;
        }

        protected override void getTestPointKpi(List<TestPoint> testPoints, LTECell targetCell, CellKpi targetKpiCell)
        {
            foreach (TestPoint tp in testPoints)
            {
                LTECell cell = getTpSrcCell(tp);
                if (cell != null && cell.Token == targetCell.Token)
                {
                    ((CellCoverTestCellKpi)targetKpiCell).AddPoint(tp, targetCell);
                }
            }
        }

        public class CellCoverTestCellKpi : DTCoverCellKpi
        {
            public CellCoverTestCellKpi(string cellName)
                : base(cellName)
            {
            }

            /// <summary>
            /// 是否在小区主覆盖方向上合格
            /// </summary>
            public bool IsCoverDirectionQualified { get; private set; }

            public override void AddPoint(TestPoint tp, LTECell targetCell)
            {
                //好点数据可能不存在经纬度,不判断覆盖方向,如改为DT再判断
                //double tpCellAngle = GetTp_CellAngle(tp, targetCell);
                //if (tpCellAngle <= 60)
                //{
                //    base.AddPoint(tp);
                //}
                base.AddPoint(tp);
            }

            public override void CalculateFinalRes()
            {
                if (cntRsrp > 0)
                {
                    AvgRsrp = Math.Round(sumRsrp / cntRsrp, 2);
                }
                else
                {
                    AvgRsrp = double.MinValue;
                }

                if (cntSinr > 0)
                {
                    AvgSinr = Math.Round(sumSinr / cntSinr, 2);
                }
                else
                {
                    AvgSinr = double.MinValue;
                }

                if (PointCount > 0)
                {
                    if (AvgRsrp > -90 && AvgSinr > 5)
                    {
                        IsCoverDirectionQualified = true;
                    }
                    else
                    {
                        IsCoverDirectionQualified = false;
                    }
                }
            }
        }
    }

    class AcpAutoCellCoverPic_TianJin : AcpAutoAntenna_TianJin
    {
        public AcpAutoCellCoverPic_TianJin()
        {
            reSetRsrpMapView();
            reSetSinrMapView();
        }

        protected string picFolderPath = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory,
            "userdata\\StationAcceptance");

        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("DT切换");
        }

        public override Dictionary<KpiKey_TJ, object> GetFileKpiInfos(FileInfo fileInfo
            , DTFileDataManager fileManager, LTECell targetCell)
        {
            CellKpi kpiCell = anaFile(fileInfo, fileManager, targetCell);

            if (filterDataInfo(kpiCell, fileInfo))
            {
                return getKpiInfos(kpiCell);
            }
            else
            {
                return new Dictionary<KpiKey_TJ, object>();
            }
        }

        protected override Dictionary<KpiKey_TJ, object> getKpiInfos(CellKpi kpiCell)
        {
            Dictionary<KpiKey_TJ, object> kpiInfos = new Dictionary<KpiKey_TJ, object>();
            CellCoverPicKpi cellKpi = kpiCell as CellCoverPicKpi;
            kpiInfos.Add(KpiKey_TJ.DTRsrpPic, cellKpi.RsrpPicPath);
            kpiInfos.Add(KpiKey_TJ.DTSinrPic, cellKpi.SinrPicPath);
            return kpiInfos;
        }

        protected override CellKpi anaFile(FileInfo fileInfo, DTFileDataManager fileManager, LTECell targetCell)
        {
            CellCoverPicKpi targetKpiCell = new CellCoverPicKpi(targetCell);
            try
            {
                MTGis.DbRect bounds = GetCoverBounds(fileManager, targetCell);
                List<TestPoint> bakList = new List<TestPoint>(fileManager.TestPoints);
                fileManager.TestPoints = filterCellTestPoint(fileManager, targetCell);
                List<Event> evtBakList = new List<Event>(fileManager.Events);
                fileManager.Events = new List<Event>();
                if (fileManager.TestPoints.Count > 0)
                {
                    targetKpiCell.RsrpPicPath = FireMapAndTakePic("RSRP", bounds, targetCell);
                    targetKpiCell.SinrPicPath = FireMapAndTakePic("SINR", bounds, targetCell);
                }
                fileManager.TestPoints = bakList;
                fileManager.Events = evtBakList;
            }
            catch (Exception ex)
            {
                reportInfo(ex);
            }
            return targetKpiCell;
        }

        protected void reSetRsrpMapView()
        {
            MapSerialInfo msi = DTLayerSerialManager.Instance.GetSerialByName("lte_fdd_RSRP");
            if (msi != null)
            {
                List<RangeInfo> ranges = new List<RangeInfo>
                {
                    new RangeInfo(false, false, -150, -110, Color.FromArgb(255, 0, 0)),
                    new RangeInfo(true, false, -110, -105, Color.FromArgb(255, 0, 255)),
                    new RangeInfo(true, false, -105, -100, Color.FromArgb(255, 153, 204)),
                    new RangeInfo(true, false, -100, -95, Color.FromArgb(255, 204, 153)),
                    new RangeInfo(true, false, -95, -90, Color.FromArgb(255, 255, 0)),
                    new RangeInfo(true, false, -90, -85, Color.FromArgb(255, 192, 0)),
                    new RangeInfo(true, false, -85, -80, Color.FromArgb(0, 0, 255)),
                    new RangeInfo(true, false, -80, -75, Color.FromArgb(0, 255, 255)),
                    new RangeInfo(true, false, -75, -65, Color.FromArgb(0, 255, 0)),
                    new RangeInfo(true, false, -70, -40, Color.FromArgb(0, 176, 80))
                };

                msi.ColorDisplayParam.Info.RangeColors = new List<DTParameterRangeColor>();
                foreach (RangeInfo range in ranges)
                {
                    DTParameterRangeColor paramColor = new DTParameterRangeColor(range.Min, range.Max, range.RangeColor);
                    paramColor.MaxIncluded = range.InculdeMax;
                    paramColor.MinIncluded = range.InculdeMin;
                    msi.ColorDisplayParam.Info.RangeColors.Add(paramColor);
                }
            }
        }

        protected void reSetSinrMapView()
        {
            MapSerialInfo msi = DTLayerSerialManager.Instance.GetSerialByName("lte_fdd_SINR");
            if (msi != null)
            {
                List<RangeInfo> ranges = new List<RangeInfo>
                {
                    new RangeInfo(false, false, -150, -10, Color.FromArgb(255, 0, 0)),
                    new RangeInfo(true, false, -10, -5, Color.FromArgb(255, 0, 255)),
                    new RangeInfo(true, false, -5, -3, Color.FromArgb(255, 153, 204)),
                    new RangeInfo(true, false, -3, 0, Color.FromArgb(255, 204, 153)),
                    new RangeInfo(true, false, 0, 5, Color.FromArgb(255, 255, 0)),
                    new RangeInfo(true, false, 5, 10, Color.FromArgb(0, 0, 255)),
                    new RangeInfo(true, false, 10, 15, Color.FromArgb(0, 255, 255)),
                    new RangeInfo(true, false, 15, 20, Color.FromArgb(0, 255, 0)),
                    new RangeInfo(true, false, 20, 100, Color.FromArgb(0, 176, 80))
                };

                msi.ColorDisplayParam.Info.RangeColors = new List<DTParameterRangeColor>();
                foreach (RangeInfo range in ranges)
                {
                    DTParameterRangeColor paramColor = new DTParameterRangeColor(range.Min, range.Max, range.RangeColor);
                    paramColor.MaxIncluded = range.InculdeMax;
                    paramColor.MinIncluded = range.InculdeMin;
                    msi.ColorDisplayParam.Info.RangeColors.Add(paramColor);
                }
            }
        }

        private List<TestPoint> filterCellTestPoint(DTFileDataManager fileManager, LTECell LTECell)
        {
            List<TestPoint> tmpList = new List<TestPoint>();
            foreach (TestPoint tp in fileManager.TestPoints)
            {
                LTECell cell = getTpSrcCell(tp);
                if (cell != null && cell.Token == LTECell.Token)
                {
                    tmpList.Add(tp);
                }
            }
            return tmpList;
        }

        /// <summary>
        /// 获取小区及所有采样点占用的最大范围
        /// </summary>
        /// <param name="fileManager"></param>
        /// <param name="LTECell"></param>
        /// <returns></returns>
        public MTGis.DbRect GetCoverBounds(DTFileDataManager fileManager, LTECell LTECell)
        {
            double lngMin = LTECell.Longitude;
            double lngMax = LTECell.Longitude;
            double latMin = LTECell.Latitude;
            double latMax = LTECell.Latitude;

            foreach (TestPoint tp in fileManager.TestPoints)
            {
                if (tp.Longitude > 70 && tp.Latitude > 3 && tp.GetMainCell() == LTECell)
                {
                    lngMin = Math.Min(lngMin, tp.Longitude);
                    lngMax = Math.Max(lngMax, tp.Longitude);
                    latMin = Math.Min(latMin, tp.Latitude);
                    latMax = Math.Max(latMax, tp.Latitude);
                }
            }
            MTGis.DbRect bounds = new MTGis.DbRect(lngMin - 0.001, latMin - 0.001, lngMax + 0.001, latMax + 0.001);
            return bounds;
        }

        public virtual string FireMapAndTakePic(string paramName, MTGis.DbRect bounds, LTECell srcLTECell)
        {
            MainModel mModel = MainModel.GetInstance();
            mModel.FireSetDefaultMapSerialTheme("LTE_FDD", paramName);
            mModel.DrawFlyLines = false;

            mModel.FireDTDataChanged(mModel.MainForm);
            mModel.MainForm.GetMapForm().GoToView(bounds);

            return takePicture(srcLTECell.BTSName, srcLTECell.Name, paramName);
        }

        #region 覆盖截图
        protected string takePicture(string btsName, string cellName, string paramName)
        {
            string filePath = GetCoverPicPath(btsName, cellName, paramName);
            if (System.IO.Directory.Exists(filePath))//删除本站之前的覆盖截图
            {
                System.IO.Directory.Delete(filePath, true);
            }

            Bitmap bitMap = MainModel.GetInstance().MainForm.GetMapForm().DrawToBitmapDIY();
            bitMap.Save(filePath, ImageFormat.Png);
            bitMap.Dispose();
            return filePath;
        }

        /// <summary>
        /// 获取小区某种覆盖截图的保存路径
        /// </summary>
        /// <param name="btsName"></param>
        /// <param name="cellName"></param>
        /// <param name="postfix"></param>
        /// <returns></returns>
        protected string GetCoverPicPath(string btsName, string cellName, string paramName)
        {
            string folderPath = GetBtsPicFolder(btsName);
            if (!System.IO.Directory.Exists(folderPath))
            {
                System.IO.Directory.CreateDirectory(folderPath);
            }
            return System.IO.Path.Combine(folderPath, cellName + "_" + paramName + ".png");
        }

        /// <summary>
        /// 获取站点覆盖截图保存文件夹地址
        /// </summary>
        /// <param name="btsName"></param>
        /// <returns></returns>
        protected string GetBtsPicFolder(string btsName)
        {
            return System.IO.Path.Combine(picFolderPath, btsName.Trim());
        }
        #endregion


        public static void InsertExcelPicture(Excel.Workbook eBook, string startCell, string picPath, int sheetIndex, double imgWidth, double imgHeight)
        {
            Excel.Worksheet eSheet = (Excel.Worksheet)eBook.Sheets[sheetIndex];
            Excel.Range rng = eSheet.get_Range(startCell, Type.Missing);

            double width = eBook.Application.CentimetersToPoints(imgWidth);
            double height = eBook.Application.CentimetersToPoints(imgHeight);
            eSheet.Shapes.AddPicture(picPath,
                Microsoft.Office.Core.MsoTriState.msoFalse,
                Microsoft.Office.Core.MsoTriState.msoCTrue,
                (float)(double)rng.Left, (float)(double)rng.Top,
                (float)width, (float)height);
        }

        public class CellCoverPicKpi : CellKpi
        {
            public CellCoverPicKpi(LTECell cell)
                : base(cell.Name)
            {

            }

            public string RsrpPicPath { get; set; } = "";
            public string SinrPicPath { get; set; } = "";
            public string HandOverPicPath { get; set; } = "";
        }

        protected class RangeInfo
        {
            public RangeInfo(bool inculdeMin, bool inculdeMax, float min, float max, Color rangeColor)
            {
                Min = min;
                InculdeMin = inculdeMin;
                Max = max;
                InculdeMax = inculdeMax;
                RangeColor = rangeColor;
            }

            public float Min
            {
                get;
                private set;
            }
            public bool InculdeMin
            {
                get;
                private set;
            }
            public float Max
            {
                get;
                private set;
            }
            public bool InculdeMax
            {
                get;
                private set;
            }

            public Color RangeColor
            {
                get;
                private set;
            }
        }
    }

    class AcpAutoCellBtsCoverPic_TianJin : AcpAutoCellCoverPic_TianJin
    {
        public AcpAutoCellBtsCoverPic_TianJin()
        {
            IsAnaBtsInfo = AnaType.AnaMultiCells;
        }

        protected override Dictionary<KpiKey_TJ, object> getKpiInfos(CellKpi kpiCell)
        {
            Dictionary<KpiKey_TJ, object> kpiInfos = new Dictionary<KpiKey_TJ, object>();
            CellCoverPicKpi cellKpi = kpiCell as CellCoverPicKpi;
            kpiInfos.Add(KpiKey_TJ.DTRsrpPic, cellKpi.RsrpPicPath);
            kpiInfos.Add(KpiKey_TJ.DTSinrPic, cellKpi.SinrPicPath);
            kpiInfos.Add(KpiKey_TJ.DTHandOverPic, cellKpi.HandOverPicPath);
            return kpiInfos;
        }

        protected override CellKpi anaFile(FileInfo fileInfo, DTFileDataManager fileManager, LTECell targetCell)
        {
            CellCoverPicKpi targetKpiCell = new CellCoverPicKpi(targetCell);
            try
            {
                MapForm mf = MainModel.GetInstance().MainForm.GetMapForm();
                MainModel.GetInstance().FireDTDataChanged(mf);
                fileManager.Events = new List<Event>();
                MTGis.DbRect bounds = GetCoverBounds(fileManager, targetCell.BelongBTS);
                targetKpiCell.RsrpPicPath = FireMapAndTakePic("RSRP", bounds, targetCell);
                targetKpiCell.SinrPicPath = FireMapAndTakePic("SINR", bounds, targetCell);

                //PCI图
                reSetPCIMapView(targetCell.BelongBTS, fileManager);
                MainModel.GetInstance().DrawDifferentServerColor = true;
                targetKpiCell.HandOverPicPath = FireMapAndTakePic("PCI", bounds, targetCell);
                MainModel.GetInstance().DrawDifferentServerColor = false;
            }
            catch (Exception ex)
            {
                reportInfo(ex);
            }
            return targetKpiCell;
        }

        public override string FireMapAndTakePic(string paramName, MTGis.DbRect bounds, LTECell srcLTECell)
        {
            MapForm mf = MainModel.GetInstance().MainForm.GetMapForm();
            mf.GoToView(bounds);
            //换成了按基站命名
            string filePath = GetCoverPicPath(srcLTECell.BTSName, srcLTECell.BTSName, paramName);

            string serialByName = "lte_fdd_" + paramName;
            mf.FireAndOutputCurMapToPic(serialByName, false, filePath);
            return filePath;
        }

        protected virtual void reSetPCIMapView(LTEBTS bts, DTFileDataManager fileManager)
        {
            MapSerialInfo msi = DTLayerSerialManager.Instance.GetSerialByName("lte_fdd_PCI");
            if (msi == null)
            {
                return;
            }

            //获取文件采样点对应的所有主服小区PCI
            List<int> pciList = getPCIList(fileManager);

            //根据PCI设置颜色
            List<RangeInfo> ranges = getRangesList(pciList);

            //设置图例
            changePCISerial(msi, ranges);

            //设置主服小区颜色
            setServerCellColorByPCI(bts, ranges);
        }

        private List<int> getPCIList(DTFileDataManager fileManager)
        {
            Dictionary<int, int> cellDic = new Dictionary<int, int>();
            foreach (var tp in fileManager.TestPoints)
            {
                int? pci = (short?)tp["lte_fdd_PCI"];
                if (pci != null && !cellDic.ContainsKey((int)pci))
                {
                    cellDic.Add((int)pci, (int)pci);
                }
            }
            return new List<int>(cellDic.Keys);
        }

        private List<RangeInfo> getRangesList(List<int> pciList)
        {
            List<RangeInfo> ranges = new List<RangeInfo>();
            List<Color> colorList = makeColorRangeList(pciList.Count);
            for (int i = 0; i < pciList.Count; i++)
            {
                ranges.Add(new RangeInfo(true, false, pciList[i], pciList[i] + 1, colorList[i]));
            }
            return ranges;

            ////预设9个小区的颜色
            //List<Color> colorList = new List<Color> { Color.Red, Color.Magenta, Color.Pink, Color.Yellow, Color.Orange, Color.Blue, Color.Cyan, Color.Lime, Color.Green };
            //for (int i = 0; i < pciList.Count; i++)
            //{
            //    ranges.Add(new RangeInfo(true, false, pciList[i], pciList[i] + 1, colorList[i]));
            //}
            //return ranges;
        }

        public List<Color> makeColorRangeList(int intervals)
        {
            List<Color> rglist = new List<Color>();

            GradientDrawingSupplier gds = new GradientDrawingSupplier(intervals);
            gds.addColorSample(Color.Red);
            gds.addColorSample(Color.Lime);
            gds.addColorSample(Color.Pink);

            gds.addColorSample(Color.Blue);
            gds.addColorSample(Color.Green);
            gds.addColorSample(Color.Magenta);

            gds.addColorSample(Color.Orange);
            gds.addColorSample(Color.Yellow);
            gds.addColorSample(Color.Cyan);
            for (int i = 0; i < intervals; i++)
            {
                Color color = gds.getColor(i);
                rglist.Add(color);
            }
            return rglist;
        }

        private void changePCISerial(MapSerialInfo msi, List<RangeInfo> ranges)
        {
            msi.ColorDisplayParam.Info.RangeColors = new List<DTParameterRangeColor>();
            foreach (RangeInfo range in ranges)
            {
                DTParameterRangeColor paramColor = new DTParameterRangeColor(range.Min, range.Max, range.RangeColor);
                paramColor.MaxIncluded = range.InculdeMax;
                paramColor.MinIncluded = range.InculdeMin;
                msi.ColorDisplayParam.Info.RangeColors.Add(paramColor);
            }
        }

        private void setServerCellColorByPCI(LTEBTS bts, List<RangeInfo> ranges)
        {
            foreach (LTECell btsLteCell in bts.Cells)
            {
                foreach (RangeInfo range in ranges)
                {
                    if (btsLteCell.PCI >= range.Min && btsLteCell.PCI < range.Max)
                    {
                        btsLteCell.ServerCellColor = range.RangeColor;
                        break;
                    }
                }
            }
        }

        /// <summary>
        /// 获取基站及所有采样点的最大范围
        /// </summary>
        /// <param name="fileManager"></param>
        /// <param name="LTECell"></param>
        /// <returns></returns>
        public MTGis.DbRect GetCoverBounds(DTFileDataManager fileManager, LTEBTS bts)
        {
            double lngMin = bts.Longitude;
            double lngMax = bts.Longitude;
            double latMin = bts.Latitude;
            double latMax = bts.Latitude;

            foreach (TestPoint tp in fileManager.TestPoints)
            {
                if (tp.Longitude > 70 && tp.Latitude > 3)
                {
                    lngMin = Math.Min(lngMin, tp.Longitude);
                    lngMax = Math.Max(lngMax, tp.Longitude);
                    latMin = Math.Min(latMin, tp.Latitude);
                    latMax = Math.Max(latMax, tp.Latitude);
                }
            }
            MTGis.DbRect bounds = new MTGis.DbRect(lngMin - 0.001, latMin - 0.001, lngMax + 0.001, latMax + 0.001);
            return bounds;
        }
    }

    class AcpAutoOverlap_TianJin : CellAcceptKpiAna_TianJin
    {
        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("DT切换");
        }

        protected override CellKpi anaFile(FileInfo fileInfo, DTFileDataManager fileManager, LTECell targetCell)
        {
            CellKpi targetKpiCell = new OverlapCellKpi(targetCell.Name);
            getTestPointKpi(fileManager.TestPoints, targetCell, targetKpiCell);
            targetKpiCell.CalculateFinalRes();
            return targetKpiCell;
        }

        protected override Dictionary<KpiKey_TJ, object> getKpiInfos(CellKpi kpiCell)
        {
            OverlapCellKpi cellKpi = kpiCell as OverlapCellKpi;
            Dictionary<KpiKey_TJ, object> kpiInfos = new Dictionary<KpiKey_TJ, object>();
            kpiInfos.Add(KpiKey_TJ.DTOverLapRate, cellKpi.OverlapRate);
            return kpiInfos;
        }

        public class OverlapCellKpi : CellKpi
        {
            public OverlapCellKpi(string cellName)
                : base(cellName)
            {

            }

            protected List<int> overlapList = new List<int>();
            public double OverlapRate { get; set; }

            public override void AddPoint(TestPoint tp)
            {
                PointCount++;

                //1.获取采样点的最强电平小区,最强小区RSRP>=-100dbm
                List<float> cellRsrp = new List<float>();
                float maxRsrp = -999;
                maxRsrp = getMaxRsrp(tp, cellRsrp, maxRsrp);

                //2.筛选与最强小区RSRP的差值大于-6db的邻区数记为重叠覆盖度
                addOverlap(cellRsrp, maxRsrp);
            }

            private float getMaxRsrp(TestPoint tp, List<float> cellRsrp, float maxRsrp)
            {
                float? rsrp = (float?)tp["lte_fdd_Cell_RSRP"];
                if (rsrp != null)
                {
                    cellRsrp.Add((float)rsrp);
                    if (maxRsrp < rsrp)
                    {
                        maxRsrp = (float)rsrp;
                    }
                }

                for (int i = 0; i < 20; i++)
                {
                    float? nRsrp = (float?)tp["lte_fdd_NCell_RSRP", i];
                    if (nRsrp != null)
                    {
                        cellRsrp.Add((float)nRsrp);
                        if (maxRsrp < nRsrp)
                        {
                            maxRsrp = (float)nRsrp;
                        }
                    }
                }

                return maxRsrp;
            }

            private void addOverlap(List<float> cellRsrp, float maxRsrp)
            {
                if (maxRsrp >= -100)
                {
                    int overlap = 0;
                    foreach (var rsrp in cellRsrp)
                    {
                        if (maxRsrp - rsrp >= 6)
                        {
                            overlap++;
                        }
                    }
                    overlapList.Add(overlap);
                }
            }

            public override void CalculateFinalRes()
            {
                //计算重叠覆盖率  重叠覆盖度>=3的采样点/总采样点*100%
                int validOverlapCount = 0;
                foreach (var overlap in overlapList)
                {
                    if (overlap >= 3)
                    {
                        validOverlapCount++;
                    }
                }

                OverlapRate = Math.Round(validOverlapCount * 1d / PointCount, 4);
            }
        }
    }




}
