﻿using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public static class NearestCellsGetter
    {
        //以某点(lngCenter,latCenter)为中心，获取半径radius内周围某个时间点dtValid的LTE小区信息
        public static List<LTECell> GetNearestLteCells(double lngCenter, double latCenter, DateTime dtValid, double radius)
        {
            List<LTECell> lstCells = CellManager.GetInstance().GetLTECells(dtValid);

            double lngStep = radius * 0.00001;
            double latStep = radius * 0.000009;

            List<LTECell> lstRtCells = new List<LTECell>();
            foreach (LTECell cell in lstCells)
            {
                if (Math.Abs(cell.Longitude - lngCenter) > lngStep ||
                    Math.Abs(cell.Latitude - latCenter) > latStep)
                    continue;

                double distance = MathFuncs.GetDistance(cell.Longitude, cell.Latitude, lngCenter, latCenter);

                if (distance <= radius)
                    lstRtCells.Add(cell);
            }

            return lstRtCells;
        }
    }
}
