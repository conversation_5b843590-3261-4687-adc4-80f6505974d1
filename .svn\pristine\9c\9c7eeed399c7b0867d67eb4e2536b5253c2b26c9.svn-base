﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.CQT;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model.Interface;

namespace MasterCom.RAMS.ZTFunc.ZTPerformanceParamNew
{
    public class GetCellCollection 
    {
        public GetCellCollection(MainModel mainModel)
        {
            this.mainModel = mainModel;
        }
        private readonly MainModel mainModel;     

        public void displayGetCell()
        {
            try
            {
                QueryCondition condition = new QueryCondition();
                FileInfo selectFile = mainModel.SelectedFileInfo;
                condition.FileInfos.Add(selectFile);
                condition.DistrictID = selectFile.DistrictID;
                QueryDisplayGetCell query = new QueryDisplayGetCell(mainModel);
                query.SetQueryCondition(condition);
                query.Query();
            }
            catch
            {
                //
            }
        }
    }
}
