using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.Stat
{
    public class IDNamePair
    {
        public int id { get; set; }
        public string Name { get; set; }
        public int districtId { get; set; }
        public IDNamePair()
        {

        }
        public IDNamePair(int id,string name)
        {
            this.id = id;
            this.Name = name;
        }
        public override string ToString()
        {
            return Name;
        }
        public static IDNamePair FillFrom(MasterCom.RAMS.Net.Content c)
        {
            IDNamePair d = new IDNamePair();
            d.id = c.GetParamInt();
            d.Name = c.GetParamString();
            c.GetParamString();
            return d;
        }

        public void SetParam(Dictionary<string, object> value)
        {
            id = (int)value["ID"];
            Name = (String)value["Name"];
        }
    }
    
}
