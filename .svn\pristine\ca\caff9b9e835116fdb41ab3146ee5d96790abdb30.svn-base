﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.Net
{
    public class ZTCellSetResult
    {
        public BackgroundCellType ResultCellType { get; set; }
        public string StrCellName { get; set; }
        public int ILAC { get; set; }
        public int ICI { get; set; }
        public int IBCCH { get; set; }
        public int ISCellNum { get; set; }
        public int INCellNum { get; set; }

        public ZTCellSetResult(string cellName, int lac, int ci, int bcch, BackgroundCellType resultCellType, int scellNum, int ncellNum)
        {
            this.StrCellName = cellName;
            this.ILAC = lac;
            this.ICI = ci;
            this.IBCCH = bcch;
            this.ResultCellType = resultCellType;
            this.ISCellNum = scellNum;
            this.INCellNum = ncellNum;
        }

        public BackgroundResult ConvertToBackgroundResult(int istime, int ietime)
        {
            BackgroundResult bgResult = new BackgroundResult();
            bgResult.ISTime = istime;
            bgResult.IETime = ietime;
            bgResult.LAC = this.ILAC;
            bgResult.CI = this.ICI;
            bgResult.BCCH = this.IBCCH;
            bgResult.CellType = this.ResultCellType;
            bgResult.AddImageValue((int)ResultCellType);
            bgResult.AddImageValue(ISCellNum);
            bgResult.AddImageValue(INCellNum);
            return bgResult;
        }
    }
}
