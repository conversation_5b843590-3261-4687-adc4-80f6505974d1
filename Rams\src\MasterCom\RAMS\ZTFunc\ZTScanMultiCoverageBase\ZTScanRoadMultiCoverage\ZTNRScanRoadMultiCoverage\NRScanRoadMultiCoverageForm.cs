﻿using DevExpress.XtraCharts;
using MasterCom.MControls;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Reflection;
using System.Text;
using System.Windows.Forms;
using Excel = Microsoft.Office.Interop.Excel;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NRScanRoadMultiCoverageForm : MinCloseForm
    {
        MapForm mapForm;
        NRScanRoadMultiCoverageLayer roadMultiCoverageLayer;

        public NRScanRoadMultiCoverageForm()
            : base()
        {
            InitializeComponent();
            this.mapForm = MainModel.MainForm.GetMapForm();

            roadMultiCoverageLayer = mapForm.GetCustomLayerBase(typeof(NRScanRoadMultiCoverageLayer)) as NRScanRoadMultiCoverageLayer;

            edtWeakCoverThreshold.Enabled = chkFilterWeakCover.Checked;
            DisposeWhenClose = true;
        }
        //private Dictionary<string, int> rangeRelativeLevelDic = new Dictionary<string, int>();
        //private Dictionary<string, int> rangeAbsoluteLevelDic = new Dictionary<string, int>();
        //private Dictionary<string, int> rangeRelORAbsLevelDic = new Dictionary<string, int>();
        //private DataTable tbRelative;
        //private DataTable tbAbsolute;
        //private DataTable tbRelORAbs;
        private ColorRange curAllColorRange;

        //所有区域采样点
        Dictionary<string, List<NRScanRoadMultiCoverageInfo>> regionRoadMultiCoveragePoints;
        //当前区域采样点
        List<NRScanRoadMultiCoverageInfo> CurregionRoadMultiCoveragePoints;
        //当前区域栅格
        List<NRScanGridRoadMultiCoverageInfo> CurRegionRoadMultiCoverageGrids = new List<NRScanGridRoadMultiCoverageInfo>();
        //当前区域弱覆盖栅格
        List<NRScanGridRoadMultiCoverageInfo> CurRegionRoadMultiCoverageWeakCoverGrids = new List<NRScanGridRoadMultiCoverageInfo>();

        public void FillData(ScanMultiCoverageCondition funcCond
            , Dictionary<string, List<NRScanRoadMultiCoverageInfo>> regionRoadMultiCoveragePoints)
        {
            this.regionRoadMultiCoveragePoints = regionRoadMultiCoveragePoints;
            var radio = radioGroupType.Properties.Items;
            radio[0].Description = "相对覆盖度(-" + funcCond.CoverBandDiff + "dBm内)";
            radio[1].Description = "绝对覆盖度( >" + funcCond.AbsoluteValue + "dBm)";
            cbxEditRegion.Properties.SelectedIndexChanged -= cbxEditRegion_SelectedIndexChanged;
            cbxEditRegion.Properties.Items.Clear();
            foreach (string region in regionRoadMultiCoveragePoints.Keys)
            {
                cbxEditRegion.Properties.Items.Add(region);
            }
            cbxEditRegion.SelectedIndex = -1;
            cbxEditRegion.Properties.SelectedIndexChanged += cbxEditRegion_SelectedIndexChanged;
            if (cbxEditRegion.Properties.Items.Count > 0)
            {
                if (regionRoadMultiCoveragePoints.ContainsKey("全部汇总"))
                {
                    cbxEditRegion.SelectedItem = "全部汇总";
                }
                else
                {
                    cbxEditRegion.SelectedIndex = 0;
                }
            }
        }

        private string level4Rel = "4<=X<50r", level4Abs = "4<=X<50a", level4RelAndAbs = "4<=X<50n";
        private Dictionary<string, int> level4Dic = new Dictionary<string, int>();
        private Dictionary<string, int> levelAllDic = new Dictionary<string, int>();

        private void initLevelDic()
        {
            level4Dic.Clear();
            levelAllDic.Clear();

            level4Dic.Add(level4Rel, 0);
            level4Dic.Add(level4Abs, 0);
            level4Dic.Add(level4RelAndAbs, 0);

            levelAllDic.Add(level4Rel, 0);
            levelAllDic.Add(level4Abs, 0);
            levelAllDic.Add(level4RelAndAbs, 0);
        }

        private void calcLevel4(NRScanRoadMultiCoverageInfo multiCovLevInfo)
        {
            levelAllDic[level4Rel]++;
            levelAllDic[level4Abs]++;
            levelAllDic[level4RelAndAbs]++;

            if (multiCovLevInfo.RelativeLevel >= 4 && multiCovLevInfo.RelativeLevel < 50)
            {
                level4Dic[level4Rel]++;
            }
            if (multiCovLevInfo.AbsoluteLevel >= 4 && multiCovLevInfo.AbsoluteLevel < 50)
            {
                level4Dic[level4Abs]++;
            }
            if (multiCovLevInfo.RelANDAbsLevel >= 4 && multiCovLevInfo.RelANDAbsLevel < 50)
            {
                level4Dic[level4RelAndAbs]++;
            }
        }

        #region ColorRangeSetting
        private void btnColorRangeSetting_Click(object sender, EventArgs e)
        {
            MultiCvrColorDlg mngDlg = new MultiCvrColorDlg();
            mngDlg.FixMinMax(0, 50);
            mngDlg.MakeRangeModeOnly();
            mngDlg.FillColorRanges(NRScanRoadMultiCoverageColorRanges.Instance.ColorRanges);

            if (mapForm != null)
            {
                mngDlg.InvalidatePointColor = roadMultiCoverageLayer.InvalidPointColor;
                mngDlg.Invalidate1800Color = roadMultiCoverageLayer.NoneMainPointColor;
            }
            if (DialogResult.OK == mngDlg.ShowDialog(this))
            {
                if (mapForm != null)
                {
                    NRScanRoadMultiCoverageColorRanges.Instance.ColorRanges = mngDlg.ColorRanges;
                    showData();
                    roadMultiCoverageLayer.ColorRanges = mngDlg.ColorRanges;
                    roadMultiCoverageLayer.InvalidPointColor = mngDlg.InvalidatePointColor;
                    roadMultiCoverageLayer.NoneMainPointColor = mngDlg.Invalidate1800Color;
                    roadMultiCoverageLayer.Invalidate();
                }
                MainModel.RefreshLegend();
            }
        }

        private int invalidPointCount = 0;
        private void showData()
        {
            gridControlRelative.DataSource = null;
            gridControlAbs.DataSource = null;
            gridControlRelORAbs.DataSource = null;

            invalidPointCount = 0;
            initLevelDic();

            List<ColorRange> ranges = NRScanRoadMultiCoverageColorRanges.Instance.ColorRanges;
            int count = ranges.Count;
            if (count == 0)
            {
                return;
            }
            curAllColorRange = new ColorRange();
            curAllColorRange.minValue = 50;
            DataTable tbRelative, tbAbsolute, tbRelORAbs;
            setDataTableValue(ranges, CurregionRoadMultiCoveragePoints, false, out tbRelative, out tbAbsolute, out tbRelORAbs);

            gridControlRelative.DataSource = tbRelative;
            gridControlAbs.DataSource = tbAbsolute;
            gridControlRelORAbs.DataSource = tbRelORAbs;

            radioGroupType.SelectedIndex = 0;
            radioGroupType_SelectedIndexChanged(null, null);
        }
        #endregion

        #region Export2Xls
        private void tsmiExport2Xls_Click(object sender, EventArgs e)
        {
            ExportResultSecurityHelper.ExportToExcel(export2Xls, ExportResultSecurityHelper.ObjFileName, true);
        }

        private void export2Xls(object nameObj)
        {
            Excel.Application excel = null;
            try
            {
                string name = nameObj.ToString();
                excel = new Microsoft.Office.Interop.Excel.Application();
                Excel.Workbook workbook = excel.Workbooks.Add(true);
                Excel.Worksheet worksheet = (Excel.Worksheet)excel.ActiveSheet;
                excel.Visible = false;
                worksheet.Name = "扫频道路重叠覆盖度";
                int rowIndex = 1;

                List<string> regionKeys = new List<string>(regionRoadMultiCoveragePoints.Keys);
                if (regionRoadMultiCoveragePoints.ContainsKey("全部汇总"))
                {
                    regionKeys.Remove("全部汇总");
                    regionKeys.Insert(0, "全部汇总");
                }
                foreach (string region in regionKeys)
                {
                    DataTable dtbRelative;
                    DataTable dtbAbsolute;
                    DataTable dtbRelORAbs;
                    List<ColorRange> ranges = NRScanRoadMultiCoverageColorRanges.Instance.ColorRanges;
                    setDataTableValue(ranges, regionRoadMultiCoveragePoints[region], true, out dtbRelative, out dtbAbsolute, out dtbRelORAbs);

                    worksheet.Cells[rowIndex++, 1] = region;
                    WaitBox.ProgressPercent = 10;
                    exportATable(dtbRelative, workbook, worksheet, "相对重叠覆盖度", ref rowIndex);
                    WaitBox.ProgressPercent = 40;
                    exportATable(dtbAbsolute, workbook, worksheet, "绝对重叠覆盖度", ref rowIndex);
                    WaitBox.ProgressPercent = 70;
                    exportATable(dtbRelORAbs, workbook, worksheet, "综合重叠覆盖度", ref rowIndex);
                }
                worksheet._SaveAs(name, Missing.Value, Missing.Value, Missing.Value, Missing.Value, Missing.Value, Missing.Value, Missing.Value, Missing.Value);
                WaitBox.ProgressPercent = 100;
            }
            finally
            {
                if (excel != null)
                {
                    excel.Quit();
                }
                WaitBox.Close();
            }
        }

        private void exportATable(DataTable tb, Excel.Workbook workbook, Excel.Worksheet worksheet, string typeName, ref int beginRow)
        {
            if (tb == null)
            {
                return;
            }
            int nextBeginRow = fillCellsData(tb, worksheet, beginRow);
            Excel.Chart chart = (Excel.Chart)workbook.Charts.Add(Missing.Value, Missing.Value, Missing.Value, Missing.Value);

            Excel.Range range = worksheet.get_Range("C" + beginRow, "C" + (beginRow + tb.Rows.Count + 1));

            chart.ChartWizard(range, Excel.XlChartType.xl3DColumn, Missing.Value,
                Excel.XlRowCol.xlColumns, 0, 1, true, typeName, Missing.Value, Missing.Value, Missing.Value);

            Excel.Series series = (Excel.Series)chart.SeriesCollection(1);
            series.BarShape = Excel.XlBarShape.xlCylinder;
            series.XValues = worksheet.get_Range("A" + (beginRow + 1), "A" + (beginRow + tb.Rows.Count + 1));
            series.HasDataLabels = true;

            Excel.Axis yAxis = (Excel.Axis)chart.Axes(Excel.XlAxisType.xlValue, Excel.XlAxisGroup.xlPrimary);
            yAxis.MinimumScale = 0.0;
            yAxis.HasMajorGridlines = true;
            yAxis.MajorGridlines.Border.ColorIndex = 15;
            yAxis.TickLabels.NumberFormat = "0.00%";

            chart.Location(Excel.XlChartLocation.xlLocationAsObject, worksheet.Name);
            Excel.Range rangeLT = worksheet.get_Range("H" + beginRow, "H" + beginRow);
            worksheet.Shapes.Item(worksheet.Shapes.Count).Top = (float)(double)rangeLT.Top;
            worksheet.Shapes.Item(worksheet.Shapes.Count).Left = (float)(double)rangeLT.Left;
            beginRow = beginRow + 16;
            if (nextBeginRow > beginRow)
            {
                beginRow = nextBeginRow;
            }
        }

        private int fillCellsData(DataTable tb, Excel.Worksheet worksheet, int rowIndex)
        {
            int colCount = tb.Columns.Count;
            int rowCount = tb.Rows.Count;
            for (int c = 0; c < colCount + 1; c++)//表格标题
            {
                worksheet.Cells[rowIndex, c + 1] = tb.Columns[c].ColumnName;
            }
            rowIndex++;
            for (int r = 0; r < rowCount; r++)//表格内容
            {
                for (int c = 0; c < colCount; c++)
                {
                    worksheet.Cells[rowIndex, c + 1] = tb.Rows[r][c];
                }
                rowIndex++;
            }
            rowIndex += 2;
            return rowIndex;
        }
        #endregion

        #region 设置DataTable
        private void setDataTableValue(List<ColorRange> ranges, List<NRScanRoadMultiCoverageInfo> roadCoverageList, bool isExport, out DataTable tbRelative, out DataTable tbAbsolute, out DataTable tbRelORAbs)
        {
            tbRelative = new DataTable();
            tbRelative.Columns.Add("重叠覆盖度", typeof(string));
            tbRelative.Columns.Add("重叠覆盖个数", typeof(int));
            tbRelative.Columns.Add("重叠覆盖占比", typeof(double));

            tbAbsolute = new DataTable();
            tbAbsolute.Columns.Add("重叠覆盖度", typeof(string));
            tbAbsolute.Columns.Add("重叠覆盖个数", typeof(int));
            tbAbsolute.Columns.Add("重叠覆盖占比", typeof(double));

            tbRelORAbs = new DataTable();
            tbRelORAbs.Columns.Add("重叠覆盖度", typeof(string));
            tbRelORAbs.Columns.Add("重叠覆盖个数", typeof(int));
            tbRelORAbs.Columns.Add("重叠覆盖占比", typeof(double));

            Dictionary<string, int> rangeRelativeLevelDic = new Dictionary<string, int>();
            Dictionary<string, int> rangeAbsoluteLevelDic = new Dictionary<string, int>();
            Dictionary<string, int> rangeRelORAbsLevelDic = new Dictionary<string, int>();
            calRangeLevlData(ranges, roadCoverageList, isExport, rangeRelativeLevelDic, rangeAbsoluteLevelDic, rangeRelORAbsLevelDic);

            int testPointCount = roadCoverageList.Count;
            addTableData(testPointCount, ranges, rangeRelativeLevelDic, tbRelative, isExport
                , chartControlRelative, level4Rel);
            addTableData(testPointCount, ranges, rangeAbsoluteLevelDic, tbAbsolute, isExport
                , chartControlAbs, level4Abs);
            addTableData(testPointCount, ranges, rangeRelORAbsLevelDic, tbRelORAbs, isExport
                , chartControlRelORAbs, level4RelAndAbs);
        }

        public void addTableData(int testPointCount, List<ColorRange> ranges
            , Dictionary<string, int> rangeLevelDic, DataTable tb, bool isExport
            , ChartControl chartControl, string level)
        {
            Series series = new Series("覆盖度", ViewType.Bar);
            foreach (ColorRange range in ranges)
            {
                var desc = range.GetRangeDesc(false);
                double rlevel = rangeLevelDic[desc];
                double rlevelPerct = 0;

                if (testPointCount != 0)
                {
                    rlevelPerct = Math.Round(rlevel / testPointCount, 4);
                }

                tb.Rows.Add(new object[] { desc, rlevel, rlevelPerct });
                if (!isExport)
                {
                    series.Points.Add(new SeriesPoint(desc, rlevelPerct));
                }
            }

            double invalidPer = 0;
            if (testPointCount > 0)
            {
                invalidPer = Math.Round((1.0 * invalidPointCount / testPointCount), 4);
            }
            tb.Rows.Add(new object[] { "无效点", invalidPointCount, invalidPer });

            if (!isExport)
            {
                tb.Rows.Add(new object[] { level, level4Dic[level], Math.Round((double)level4Dic[level] / testPointCount, 4) });

                chartControl.Series.Clear();
                chartControl.Series.Add(series);
            }
        }

        private void calRangeLevlData(List<ColorRange> ranges
            , List<NRScanRoadMultiCoverageInfo> roadCoverageList, bool isExport
            , Dictionary<string, int> rangeRelativeLevelDic
            , Dictionary<string, int> rangeAbsoluteLevelDic
            , Dictionary<string, int> rangeRelORAbsLevelDic)
        {
            foreach (ColorRange range in ranges)
            {
                if (curAllColorRange.minValue > range.minValue)
                {
                    curAllColorRange.minValue = range.minValue;
                }
                if (curAllColorRange.maxValue < range.maxValue)
                {
                    curAllColorRange.maxValue = range.maxValue;
                }
                var desc = range.GetRangeDesc(false);
                rangeRelativeLevelDic[desc] = 0;
                rangeAbsoluteLevelDic[desc] = 0;
                rangeRelORAbsLevelDic[desc] = 0;
            }

            foreach (var multiCovLevInfo in roadCoverageList)
            {
                if (multiCovLevInfo.InvalidatePoint)
                {
                    invalidPointCount++;
                    continue;
                }
                calRangeDic(ranges, rangeRelativeLevelDic, multiCovLevInfo.RelativeLevel);
                calRangeDic(ranges, rangeAbsoluteLevelDic, multiCovLevInfo.AbsoluteLevel);
                calRangeDic(ranges, rangeRelORAbsLevelDic, multiCovLevInfo.RelANDAbsLevel);
                if (!isExport)
                {
                    calcLevel4(multiCovLevInfo);
                }
            }
        }

        private void calRangeDic(List<ColorRange> ranges, Dictionary<string, int> rangeLevelDic, int level)
        {
            for (int i = 0; i < ranges.Count; i++)
            {
                ColorRange cr = ranges[i];
                if (level >= cr.minValue && level < cr.maxValue
                    || level == ranges[ranges.Count - 1].maxValue)
                {
                    rangeLevelDic[cr.GetRangeDesc(false)]++;
                    break;
                }
            }
        }
        #endregion

        private void radioGroupType_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (mapForm == null)
            {
                return;
            }

            roadMultiCoverageLayer.SetShowCoverageType(radioGroupType.SelectedIndex);
            roadMultiCoverageLayer.Invalidate();
        }

        #region ExportSample
        private void miExportSample_Click(object sender, EventArgs e)
        {
            if (CurregionRoadMultiCoveragePoints.Count == 0)
            {
                MessageBox.Show("没有采样点可以导出", "提示");
                return;
            }
            if (CurregionRoadMultiCoveragePoints[0].TestPoint.GetType() == typeof(ScanTestPoint_LTE))
            {
                exportLTEScanSample();
            }
            else if (CurregionRoadMultiCoveragePoints[0].TestPoint.GetType() == typeof(TestPoint_NR))
            {
                exportNRSample();
            }
        }

        private void exportLTEScanSample()
        {
            List<List<NPOIRow>> npoiRowsList = new List<List<NPOIRow>>();
            List<NPOIRow> relativeList = NPOIRowList("相对重叠覆盖度");
            List<NPOIRow> absoluteList = NPOIRowList("绝对重叠覆盖度");
            npoiRowsList.Add(relativeList);
            npoiRowsList.Add(absoluteList);

            List<string> sheetNames = new List<string>();
            sheetNames.Add("相对重叠覆盖度");
            sheetNames.Add("绝对重叠覆盖度");

            ExcelNPOIManager.ExportToExcel(npoiRowsList, sheetNames);
        }

        private List<NPOIRow> NPOIRowList(string type)
        {
            List<NPOIRow> rowList = new List<NPOIRow>();
            NPOIRow row = new NPOIRow();
            row.AddCellValue("序号");
            row.AddCellValue("网格号");
            row.AddCellValue("采样点经度");
            row.AddCellValue("采样点纬度");
            row.AddCellValue(type);
            row.AddCellValue("小区名称");
            row.AddCellValue("小区经度");
            row.AddCellValue("小区纬度");
            row.AddCellValue("NodeBID");
            row.AddCellValue("CellID");
            row.AddCellValue("TAC");
            row.AddCellValue("PCI");
            row.AddCellValue("频点");
            row.AddCellValue("场强");
            row.AddCellValue("距离(米)");
            rowList.Add(row);
            int iLoop = 1;
            int rowCount = 1;

            foreach (NRScanRoadMultiCoverageInfo multiCovLelInfo in CurregionRoadMultiCoveragePoints)
            {
                TestPoint tp = multiCovLelInfo.TestPoint;
                int relRate = 0;
                List<NRCellRsrpInfo> lstCells;
                if (type == "相对重叠覆盖度")
                {
                    relRate = multiCovLelInfo.RelativeLevel;

                    lstCells = multiCovLelInfo.LstRelatedCell;
                }
                else
                {
                    relRate = multiCovLelInfo.AbsoluteLevel;

                    lstCells = multiCovLelInfo.LstAbsoluteCell;
                }
                row = new NPOIRow();
                row.AddCellValue(iLoop++);
                row.AddCellValue(multiCovLelInfo.StrRegionName);
                row.AddCellValue(tp.Longitude);
                row.AddCellValue(tp.Latitude);
                row.AddCellValue(relRate);


                foreach (var cell in lstCells)
                {
                    var curCell = cell.Cell;

                    NPOIRow subRow = new NPOIRow();
                    if (curCell != null)
                    {
                        subRow.AddCellValue(curCell.Name);
                        subRow.AddCellValue(curCell.Longitude);
                        subRow.AddCellValue(curCell.Latitude);
                        subRow.AddCellValue(curCell.BelongBTS.BTSID);
                        subRow.AddCellValue(curCell.CellID);
                        subRow.AddCellValue(curCell.TAC);
                        subRow.AddCellValue(cell.Pci);
                        subRow.AddCellValue(cell.Earfcn);
                        subRow.AddCellValue(cell.Rsrp);
                        subRow.AddCellValue(curCell.GetDistance(tp.Longitude, tp.Latitude));
                    }
                    else
                    {
                        subRow.AddCellValue(cell.StrName);
                        subRow.AddCellValue("");
                        subRow.AddCellValue("");
                        subRow.AddCellValue("");
                        subRow.AddCellValue("");
                        subRow.AddCellValue("");
                        subRow.AddCellValue("");
                        subRow.AddCellValue("");
                        subRow.AddCellValue("");
                        subRow.AddCellValue("");
                    }
                    row.AddSubRow(subRow);
                    rowCount++;
                }
                rowList.Add(row);
            }
            return rowList;
        }


        delegate void Func(TestPoint tp, List<KeyValuePair<string, float>> cellList, int i, NPOIRow subRow, string strArfcn, string strPci);

        private int addRelate(List<NPOIRow> rowList, NPOIRow row, int rowCount, TestPoint tp, int relRate, List<KeyValuePair<string, float>> cellList, Func func)
        {
            for (int i = 0; i < relRate; i++)
            {
                NPOIRow subRow = new NPOIRow();

                string arfcnPci = cellList[i].Key;
                int pos = arfcnPci.IndexOf("_");
                string strArfcn = arfcnPci.Substring(0, pos);
                string strPci = arfcnPci.Substring(pos + 1, arfcnPci.Length - pos - 1);
                func(tp, cellList, i, subRow, strArfcn, strPci);
                row.AddSubRow(subRow);
                rowCount++;
            }
            rowList.Add(row);
            return rowCount;
        }

        #region NRSample
        private bool exportNRSample()
        {
            List<NPOIRow> rowList = new List<NPOIRow>();
            NPOIRow row = new NPOIRow();
            row.AddCellValue("序号");
            row.AddCellValue("经度");
            row.AddCellValue("纬度");
            row.AddCellValue("相对重叠覆盖度");
            row.AddCellValue("小区名称");
            row.AddCellValue("TAC");
            row.AddCellValue("ECI");
            row.AddCellValue("频点");
            row.AddCellValue("扰码");
            row.AddCellValue("场强");
            row.AddCellValue("距离(米)");
            rowList.Add(row);
            int iLoop = 1;
            int rowCount = 1;

            foreach (NRScanRoadMultiCoverageInfo multiCovLelInfo in CurregionRoadMultiCoveragePoints)
            {
                if (curAllColorRange.minValue > multiCovLelInfo.RelativeLevel || curAllColorRange.maxValue < multiCovLelInfo.RelativeLevel)
                {
                    continue;
                }

                TestPoint tp = multiCovLelInfo.TestPoint;
                int relRate = multiCovLelInfo.RelativeLevel;
                row = new NPOIRow();
                row.AddCellValue(iLoop++);
                row.AddCellValue(tp.Longitude);
                row.AddCellValue(tp.Latitude);
                row.AddCellValue(relRate);

                Dictionary<string, float> cellDic = new Dictionary<string, float>();

                #region 所有数据进行排序
                dealNRTPInfo(multiCovLelInfo, tp, cellDic);

                List<KeyValuePair<string, float>> cellList = new List<KeyValuePair<string, float>>(cellDic);
                cellList.Sort(delegate (KeyValuePair<string, float> s1, KeyValuePair<string, float> s2)
                {
                    return s2.Value.CompareTo(s1.Value);
                });
                #endregion

                rowCount = addRelate(rowList, row, rowCount, tp, relRate, cellList, addNRCellInfo);
            }
            ExcelNPOIManager.ExportToExcel(rowList);
            return true;
        }

        private void dealNRTPInfo(NRScanRoadMultiCoverageInfo multiCovLelInfo, TestPoint tp, Dictionary<string, float> cellDic)
        {
            float? rscp = NRTpHelper.NrTpManager.GetSCellRsrp(tp);
            int? arfcn = (int?)NRTpHelper.NrTpManager.GetEARFCN(tp);
            int? pci = (int?)NRTpHelper.NrTpManager.GetPCI(tp);

            if (rscp != null && arfcn != null && pci != null)
            {
                string freqCpi = arfcn.ToString() + "_" + pci.ToString();
                if ((multiCovLelInfo.MainCellName == freqCpi) || (multiCovLelInfo.RelCoverCellsNameStr.Contains(freqCpi)))
                {
                    cellDic.Add(freqCpi, (float)rscp);
                }
            }

            for (int i = 0; i < 16; i++)
            {
                float? nRscp = NRTpHelper.NrTpManager.GetNCellRsrp(tp, i);
                int? nArfcn = (int?)NRTpHelper.NrTpManager.GetNEARFCN(tp, i);
                int? nPci = (int?)NRTpHelper.NrTpManager.GetNPCI(tp, i);
                bool isNCell = NRTpHelper.NrTpManager.JudgeIsNCell(tp, i);
                if (!isNCell || nRscp == null || nArfcn == null || nPci == null)
                {
                    continue;
                }

                string freqCpi = nArfcn.ToString() + "_" + nPci.ToString();
                if (((multiCovLelInfo.MainCellName == freqCpi) || (multiCovLelInfo.RelCoverCellsNameStr.Contains(freqCpi))) && !cellDic.ContainsKey(freqCpi))
                {
                    cellDic.Add(freqCpi, (float)nRscp);
                }
            }
        }

        private void addNRCellInfo(TestPoint tp, List<KeyValuePair<string, float>> cellList, int i, NPOIRow subRow, string strArfcn, string strPci)
        {
            NRCell curCell = CellManager.GetInstance().GetNearestNRCellByARFCNPCI(tp.DateTime, Convert.ToInt32(strArfcn), Convert.ToInt32(strPci), tp.Longitude, tp.Latitude);
            if (curCell != null)
            {
                subRow.AddCellValue(curCell.Name);
                subRow.AddCellValue(curCell.TAC);
                subRow.AddCellValue(curCell.NCI);
                subRow.AddCellValue(curCell.SSBARFCN);
                subRow.AddCellValue(curCell.PCI);
                subRow.AddCellValue(cellList[i].Value);
                subRow.cellValues.Add(Math.Round(curCell.GetDistance(tp.Longitude, tp.Latitude), 2));
            }
            else
            {
                subRow.AddCellValue("");
                subRow.AddCellValue("");
                subRow.AddCellValue("");
                subRow.AddCellValue(strArfcn);
                subRow.AddCellValue(strPci);
                subRow.AddCellValue(cellList[i].Value);
                subRow.cellValues.Add("");
            }
        }
        #endregion
        #endregion

        private void contextMenuStrip_Opening(object sender, CancelEventArgs e)
        {
            miExportSample.Enabled = CurregionRoadMultiCoveragePoints.Count > 0 &&
             CurregionRoadMultiCoveragePoints[0].TestPoint != null;
            miExportToMapInfoGrid.Enabled = CurRegionRoadMultiCoverageGrids.Count > 0;
            miExportToMapInfoWeakCoverGrid.Enabled = CurRegionRoadMultiCoverageWeakCoverGrids.Count > 0;
        }

        private void btnConvertToGrid_Click(object sender, EventArgs e)
        {
            bool includeInvalidatePoint = false;
            if (invalidPointCount > 0)
            {
                includeInvalidatePoint = MessageBox.Show("无效点（第一强信号强度小于设定值）是否参与栅格化计算？", "注意", MessageBoxButtons.YesNo) == DialogResult.Yes;
            }
            WaitBox.Show("正在进行栅格化...", convertToGrid, includeInvalidatePoint);
            if (mapForm != null)
            {
                roadMultiCoverageLayer.GridList.Clear();
                foreach (var item in CurRegionRoadMultiCoverageGrids)
                {
                    roadMultiCoverageLayer.GridList.Add(item);
                }

                roadMultiCoverageLayer.Invalidate();
            }
        }

        private void convertToGrid(object includeInvalidatePoint)
        {
            try
            {
                bool includeVp = (bool)includeInvalidatePoint;
                CurRegionRoadMultiCoverageGrids.Clear();
                CurRegionRoadMultiCoverageWeakCoverGrids.Clear();
                var gridDic = new Dictionary<string, NRScanGridRoadMultiCoverageInfo>();
                int iLoop = 0;
                foreach (NRScanRoadMultiCoverageInfo testPoint in CurregionRoadMultiCoveragePoints)
                {
                    if (!includeVp && testPoint.InvalidatePoint)
                    {
                        continue;
                    }
                    double longitude = GridBase.GetGridTLLong(testPoint.Longitude);
                    double latitude = GridBase.GetGridTLLat(testPoint.Latitude); 
                    string key = longitude + "|" + latitude;
                    if (!gridDic.ContainsKey(key))
                    {
                        gridDic[key] = new NRScanGridRoadMultiCoverageInfo(longitude, latitude);
                    }
                    gridDic[key].Add(testPoint);
                    WaitBox.ProgressPercent = (int)(100.0 * ++iLoop / CurregionRoadMultiCoveragePoints.Count);
                }
                if (chkFilterWeakCover.Checked)
                {
                    foreach (NRScanGridRoadMultiCoverageInfo item in gridDic.Values)
                    {
                        if (item.RsrpAvg < (double)edtWeakCoverThreshold.Value)
                        {
                            CurRegionRoadMultiCoverageWeakCoverGrids.Add(item);
                        }
                        else
                        {
                            CurRegionRoadMultiCoverageGrids.Add(item);
                        }
                    }
                }
                else
                {
                    CurRegionRoadMultiCoverageGrids = new List<NRScanGridRoadMultiCoverageInfo>(gridDic.Values);
                }

                foreach (var grid in CurRegionRoadMultiCoverageGrids)
                {
                    grid.Calculate();
                }
            }
            finally
            {
                WaitBox.Close();
            }
        }

        private void chkFilterWeakCover_CheckedChanged(object sender, EventArgs e)
        {
            edtWeakCoverThreshold.Enabled = chkFilterWeakCover.Checked;
        }

        private void miExportMapInfo_Click(object sender, EventArgs e)
        {
            //TODO:
            //ExportResultSecurityHelper.ExportToShp(roadMultiCoverageLayer.OutputTdShpFileWithWaitBox
            // , ExportResultSecurityHelper.ObjFileName, true);
        }

        private void miExportToMapInfoGrid_Click(object sender, EventArgs e)
        {
            exportToMapInfoGrid(false);
        }

        private void miExportToMapInfoWeakCoverGrid_Click(object sender, EventArgs e)
        {
            exportToMapInfoGrid(true);
        }

        private void exportToMapInfoGrid(bool weakGrid)
        {
            string fileName = "";
            ExportSecurityCondition exportCond;
            if (ExportResultSecurityHelper.GetExportPermit(this.LogItemSrc, FileSimpleTypeHelper.Shp
                , ref fileName, out exportCond))
            {
                object[] objs = new object[2];
                objs[0] = fileName;
                objs[1] = weakGrid;
                //TODO:
                //WaitBox.Show("开始导出ShapeFile..."
                //    , roadMultiCoverageLayer.OutputTDShpFileWithWaitBox_Grid, objs);
                //if (ExportResultSecurityHelper.ExportSecurity(this.LogItemSrc, ref fileName, exportCond))
                //{
                //    MessageBox.Show("导出完毕！");
                //}
            }

            //SaveFileDialog saveDlg = new SaveFileDialog();
            //saveDlg.RestoreDirectory = false;
            //saveDlg.Filter = FilterHelper.Shp;
            //if (saveDlg.ShowDialog() == DialogResult.OK)
            //{
            //    object[] objs = new object[2];
            //    objs[0] = saveDlg.FileName;
            //    objs[1] = weakGrid;
            //    WaitBox.Show("开始导出ShapeFile...", mapForm.GetRoadMultiCoverageLayer().OutputTDShpFileWithWaitBox_Grid, objs);
            //    MessageBox.Show("导出完毕！");
            //}
        }

        private void cbxEditRegion_SelectedIndexChanged(object sender, EventArgs e)
        {
            string region = (string)cbxEditRegion.SelectedItem;
            if (string.IsNullOrEmpty(region)) return;
            CurregionRoadMultiCoveragePoints = regionRoadMultiCoveragePoints[region];
            //MainModel.CurRegionRoadMultiCoverageGrids_TD = MainModel.RegionRoadMultiCoverageGrids_TD[region];
            //MainModel.CurRegionRoadMultiCoverageWeakCoverGrids_TD = MainModel.RegionRoadMultiCoverageWeakCoverGrids_TD[region];

            roadMultiCoverageLayer.DataList.Clear();
            foreach (var item in CurregionRoadMultiCoveragePoints)
            {
                roadMultiCoverageLayer.DataList.Add(item);
            }

            showData();
            MainModel.RefreshLegend();
        }
    }
}
