﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class XtraSetCellMultiForm_LTE
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.labelControlBand = new DevExpress.XtraEditors.LabelControl();
            this.numRxLevDValue = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.label4 = new System.Windows.Forms.Label();
            this.numRxLevThreshold = new DevExpress.XtraEditors.SpinEdit();
            this.btnOK = new DevExpress.XtraEditors.SimpleButton();
            this.btnCancel = new DevExpress.XtraEditors.SimpleButton();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl5 = new DevExpress.XtraEditors.LabelControl();
            this.spinEditInvalidThresold = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.chkSaveSample = new DevExpress.XtraEditors.CheckEdit();
            this.labelControl6 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.groupBox4 = new System.Windows.Forms.GroupBox();
            this.freqBandControl1 = new MasterCom.RAMS.ZTFunc.FreqBandControl();
            this.toolStripDropDownFreq = new System.Windows.Forms.ToolStripDropDown();
            ((System.ComponentModel.ISupportInitialize)(this.numRxLevDValue.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRxLevThreshold.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditInvalidThresold.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkSaveSample.Properties)).BeginInit();
            this.groupBox1.SuspendLayout();
            this.groupBox2.SuspendLayout();
            this.groupBox3.SuspendLayout();
            this.groupBox4.SuspendLayout();
            this.SuspendLayout();
            // 
            // labelControlBand
            // 
            this.labelControlBand.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControlBand.Appearance.Options.UseFont = true;
            this.labelControlBand.Location = new System.Drawing.Point(21, 27);
            this.labelControlBand.Name = "labelControlBand";
            this.labelControlBand.Size = new System.Drawing.Size(168, 12);
            this.labelControlBand.TabIndex = 3;
            this.labelControlBand.Text = "相对覆盖带：与最强信号差异 <";
            // 
            // numRxLevDValue
            // 
            this.numRxLevDValue.EditValue = new decimal(new int[] {
            6,
            0,
            0,
            0});
            this.numRxLevDValue.Location = new System.Drawing.Point(195, 22);
            this.numRxLevDValue.Name = "numRxLevDValue";
            this.numRxLevDValue.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numRxLevDValue.Properties.Appearance.Options.UseFont = true;
            this.numRxLevDValue.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numRxLevDValue.Properties.IsFloatValue = false;
            this.numRxLevDValue.Properties.Mask.AutoComplete = DevExpress.XtraEditors.Mask.AutoCompleteType.None;
            this.numRxLevDValue.Properties.Mask.EditMask = "N00";
            this.numRxLevDValue.Size = new System.Drawing.Size(82, 20);
            this.numRxLevDValue.TabIndex = 4;
            // 
            // labelControl1
            // 
            this.labelControl1.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl1.Appearance.Options.UseFont = true;
            this.labelControl1.Location = new System.Drawing.Point(283, 25);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(12, 12);
            this.labelControl1.TabIndex = 5;
            this.labelControl1.Text = "dB";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label4.Location = new System.Drawing.Point(22, 29);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(137, 12);
            this.label4.TabIndex = 8;
            this.label4.Text = "绝对覆盖带：信号强度 >";
            // 
            // numRxLevThreshold
            // 
            this.numRxLevThreshold.EditValue = new decimal(new int[] {
            80,
            0,
            0,
            -2147483648});
            this.numRxLevThreshold.Location = new System.Drawing.Point(165, 25);
            this.numRxLevThreshold.Name = "numRxLevThreshold";
            this.numRxLevThreshold.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numRxLevThreshold.Properties.Appearance.Options.UseFont = true;
            this.numRxLevThreshold.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numRxLevThreshold.Properties.IsFloatValue = false;
            this.numRxLevThreshold.Properties.Mask.EditMask = "N00";
            this.numRxLevThreshold.Size = new System.Drawing.Size(82, 20);
            this.numRxLevThreshold.TabIndex = 9;
            // 
            // btnOK
            // 
            this.btnOK.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnOK.Appearance.Options.UseFont = true;
            this.btnOK.Location = new System.Drawing.Point(350, 237);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 10;
            this.btnOK.Text = "确定";
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // btnCancel
            // 
            this.btnCancel.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnCancel.Appearance.Options.UseFont = true;
            this.btnCancel.Location = new System.Drawing.Point(480, 237);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 11;
            this.btnCancel.Text = "取消";
            this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
            // 
            // labelControl2
            // 
            this.labelControl2.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl2.Appearance.Options.UseFont = true;
            this.labelControl2.Location = new System.Drawing.Point(253, 30);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(18, 12);
            this.labelControl2.TabIndex = 12;
            this.labelControl2.Text = "dBm";
            // 
            // labelControl5
            // 
            this.labelControl5.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl5.Appearance.Options.UseFont = true;
            this.labelControl5.Location = new System.Drawing.Point(263, 26);
            this.labelControl5.Name = "labelControl5";
            this.labelControl5.Size = new System.Drawing.Size(18, 12);
            this.labelControl5.TabIndex = 17;
            this.labelControl5.Text = "dBm";
            // 
            // spinEditInvalidThresold
            // 
            this.spinEditInvalidThresold.EditValue = new decimal(new int[] {
            95,
            0,
            0,
            -2147483648});
            this.spinEditInvalidThresold.Location = new System.Drawing.Point(175, 22);
            this.spinEditInvalidThresold.Name = "spinEditInvalidThresold";
            this.spinEditInvalidThresold.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditInvalidThresold.Properties.Appearance.Options.UseFont = true;
            this.spinEditInvalidThresold.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditInvalidThresold.Properties.IsFloatValue = false;
            this.spinEditInvalidThresold.Properties.Mask.EditMask = "N00";
            this.spinEditInvalidThresold.Properties.MaxValue = new decimal(new int[] {
            10,
            0,
            0,
            -2147483648});
            this.spinEditInvalidThresold.Properties.MinValue = new decimal(new int[] {
            120,
            0,
            0,
            -2147483648});
            this.spinEditInvalidThresold.Size = new System.Drawing.Size(82, 20);
            this.spinEditInvalidThresold.TabIndex = 15;
            // 
            // labelControl4
            // 
            this.labelControl4.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl4.Appearance.Options.UseFont = true;
            this.labelControl4.Location = new System.Drawing.Point(13, 26);
            this.labelControl4.Name = "labelControl4";
            this.labelControl4.Size = new System.Drawing.Size(156, 12);
            this.labelControl4.TabIndex = 16;
            this.labelControl4.Text = "计算有效性：最强信号强度≥";
            // 
            // chkSaveSample
            // 
            this.chkSaveSample.Location = new System.Drawing.Point(20, 234);
            this.chkSaveSample.Name = "chkSaveSample";
            this.chkSaveSample.Properties.Caption = "保留采样点信息";
            this.chkSaveSample.Size = new System.Drawing.Size(137, 19);
            this.chkSaveSample.TabIndex = 18;
            this.chkSaveSample.ToolTip = "选择保留会占用较大内存";
            // 
            // labelControl6
            // 
            this.labelControl6.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl6.Appearance.ForeColor = System.Drawing.Color.Red;
            this.labelControl6.Appearance.Options.UseFont = true;
            this.labelControl6.Appearance.Options.UseForeColor = true;
            this.labelControl6.Location = new System.Drawing.Point(287, 26);
            this.labelControl6.Name = "labelControl6";
            this.labelControl6.Size = new System.Drawing.Size(216, 12);
            this.labelControl6.TabIndex = 19;
            this.labelControl6.Text = "（不符合该条件的采样点，视为无效点）";
            // 
            // labelControl3
            // 
            this.labelControl3.Appearance.ForeColor = System.Drawing.Color.Red;
            this.labelControl3.Appearance.Options.UseForeColor = true;
            this.labelControl3.Location = new System.Drawing.Point(137, 238);
            this.labelControl3.Name = "labelControl3";
            this.labelControl3.Size = new System.Drawing.Size(180, 14);
            this.labelControl3.TabIndex = 20;
            this.labelControl3.Text = "（用于导出采样点，内存占用大）";
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.labelControlBand);
            this.groupBox1.Controls.Add(this.numRxLevDValue);
            this.groupBox1.Controls.Add(this.labelControl1);
            this.groupBox1.Location = new System.Drawing.Point(20, 70);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(302, 64);
            this.groupBox1.TabIndex = 23;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "相对覆盖度";
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.numRxLevThreshold);
            this.groupBox2.Controls.Add(this.label4);
            this.groupBox2.Controls.Add(this.labelControl2);
            this.groupBox2.Location = new System.Drawing.Point(20, 153);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(302, 67);
            this.groupBox2.TabIndex = 24;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "绝对覆盖度";
            // 
            // groupBox3
            // 
            this.groupBox3.Controls.Add(this.labelControl6);
            this.groupBox3.Controls.Add(this.labelControl4);
            this.groupBox3.Controls.Add(this.spinEditInvalidThresold);
            this.groupBox3.Controls.Add(this.labelControl5);
            this.groupBox3.Location = new System.Drawing.Point(20, 9);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.Size = new System.Drawing.Size(537, 53);
            this.groupBox3.TabIndex = 24;
            this.groupBox3.TabStop = false;
            this.groupBox3.Text = "计算有效性";
            // 
            // groupBox4
            // 
            this.groupBox4.Controls.Add(this.freqBandControl1);
            this.groupBox4.Location = new System.Drawing.Point(331, 70);
            this.groupBox4.Name = "groupBox4";
            this.groupBox4.Size = new System.Drawing.Size(226, 150);
            this.groupBox4.TabIndex = 25;
            this.groupBox4.TabStop = false;
            this.groupBox4.Text = "剔除规则";
            // 
            // freqBandControl1
            // 
            this.freqBandControl1.ChkFreqBandChange_click = null;
            this.freqBandControl1.Location = new System.Drawing.Point(8, 20);
            this.freqBandControl1.Name = "freqBandControl1";
            this.freqBandControl1.Size = new System.Drawing.Size(206, 127);
            this.freqBandControl1.TabIndex = 0;
            // 
            // toolStripDropDownFreq
            // 
            this.toolStripDropDownFreq.LayoutStyle = System.Windows.Forms.ToolStripLayoutStyle.Flow;
            this.toolStripDropDownFreq.Name = "toolStripDropDown1";
            this.toolStripDropDownFreq.Size = new System.Drawing.Size(2, 4);
            // 
            // XtraSetCellMultiForm_LTE
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(577, 274);
            this.Controls.Add(this.groupBox4);
            this.Controls.Add(this.groupBox2);
            this.Controls.Add(this.groupBox3);
            this.Controls.Add(this.groupBox1);
            this.Controls.Add(this.labelControl3);
            this.Controls.Add(this.chkSaveSample);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.Name = "XtraSetCellMultiForm_LTE";
            this.Text = "小区重叠覆盖度分析条件设置";
            this.Load += new System.EventHandler(this.XtraSetCellMultiForm_LTE_Load);
            ((System.ComponentModel.ISupportInitialize)(this.numRxLevDValue.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRxLevThreshold.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditInvalidThresold.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkSaveSample.Properties)).EndInit();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            this.groupBox3.ResumeLayout(false);
            this.groupBox3.PerformLayout();
            this.groupBox4.ResumeLayout(false);
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraEditors.LabelControl labelControlBand;
        private DevExpress.XtraEditors.SpinEdit numRxLevDValue;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private System.Windows.Forms.Label label4;
        private DevExpress.XtraEditors.SpinEdit numRxLevThreshold;
        private DevExpress.XtraEditors.SimpleButton btnOK;
        private DevExpress.XtraEditors.SimpleButton btnCancel;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.LabelControl labelControl5;
        private DevExpress.XtraEditors.SpinEdit spinEditInvalidThresold;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private DevExpress.XtraEditors.CheckEdit chkSaveSample;
        private DevExpress.XtraEditors.LabelControl labelControl6;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.GroupBox groupBox3;
        private System.Windows.Forms.GroupBox groupBox4;
        private System.Windows.Forms.ToolStripDropDown toolStripDropDownFreq;
        private FreqBandControl freqBandControl1;
    }
}