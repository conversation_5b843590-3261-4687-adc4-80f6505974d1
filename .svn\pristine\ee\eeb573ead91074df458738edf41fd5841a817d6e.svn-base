﻿using System;
using System.Collections.Generic;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.MTGis;
using MasterCom.Util;
using DevExpress.XtraGrid.Views.Grid;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class HighSpeedRailPrivateNetForm : MinCloseForm
    {
        public HighSpeedRailPrivateNetForm(MainModel mm)
            : base(mm)
        {
            InitializeComponent();
        }

        public HighSpeedRailPrivateNetCondition PrivateNetCondition { get; set; }
        List<PrivateNetWeakCoverInfo> weakCoverInfoList = new List<PrivateNetWeakCoverInfo>();
        List<PrivateNetWeakSinrInfo> weakSINRInfoList = new List<PrivateNetWeakSinrInfo>();
        List<OutPrivateNetInfo> outPrivateNetInfoList = new List<OutPrivateNetInfo>();
        public void FillData(List<PrivateNetWeakCoverInfo> weakCoverInfoList, List<PrivateNetWeakSinrInfo> weakSINRInfoList, List<OutPrivateNetInfo> outPrivateNetInfoList)
        {
            this.weakCoverInfoList = weakCoverInfoList;
            this.weakSINRInfoList = weakSINRInfoList;
            this.outPrivateNetInfoList = outPrivateNetInfoList;

#if Henan
            //由于河南必须要显示省份,而且固定显示河南,只给河南显示
            gridColProvinceCover.Visible = true;
            gridColProvinceSINR.Visible = true;
            gridColProvincePNet.Visible = true;
#endif

            gridControlWeakCover.DataSource = weakCoverInfoList;
            gridControlWeakCover.RefreshDataSource();
            if (xTabCtrHighSpeedRail.SelectedTabPage == xTabPageWeakCover)
            {
                xTabCtrHighSpeedRail_SelectedPageChanged(null, null);
            }
            gridControlWeakSINR.DataSource = weakSINRInfoList;
            gridControlWeakSINR.RefreshDataSource();
            if (xTabCtrHighSpeedRail.SelectedTabPage == xTabPageWeakSINR)
            {
                xTabCtrHighSpeedRail_SelectedPageChanged(null, null);
            }
            gridControlOutPrivateNet.DataSource = outPrivateNetInfoList;
            gridControlOutPrivateNet.RefreshDataSource();
            if (xTabCtrHighSpeedRail.SelectedTabPage == xTabPageOutPrivateNet)
            {
                xTabCtrHighSpeedRail_SelectedPageChanged(null, null);
            }
        }

        private void miExport2Xls_Click(object sender, EventArgs e)
        {
            if (xTabCtrHighSpeedRail.SelectedTabPageIndex == 0)
            {
                List<List<object>> exportList = GridViewTransfer.Transfer(gridControlWeakCover);
                ExcelNPOIManager.ExportToExcel(exportList);
            }
            else if (xTabCtrHighSpeedRail.SelectedTabPageIndex == 1)
            {
                List<List<object>> exportList = GridViewTransfer.Transfer(gridControlWeakSINR);
                ExcelNPOIManager.ExportToExcel(exportList);
            }
            else 
            {
                List<List<object>> exportList = GridViewTransfer.Transfer(gridControlOutPrivateNet);
                ExcelNPOIManager.ExportToExcel(exportList);
            }
        }

        private void miExportAllXls_Click(object sender, EventArgs e)
        {
            List<GridView> gvList = new List<GridView>();
            gvList.Add(gvWeakCover);
            gvList.Add(gvWeakSINR);
            gvList.Add(gvOutPrivateNet);

            List<string> sheetNames = new List<string>();
            sheetNames.Add("连续弱覆盖详表");
            sheetNames.Add("连续质差详表");
            sheetNames.Add("出专网详表");
            ExcelNPOIManager.ExportToExcel(gvList, sheetNames);
        }


        private void xTabCtrHighSpeedRail_SelectedPageChanged(object sender, DevExpress.XtraTab.TabPageChangedEventArgs e)
        {
            if (xTabCtrHighSpeedRail.SelectedTabPageIndex == 0)
            {
                MainModel.ClearDTData();
                foreach (PrivateNetWeakCoverInfo item in weakCoverInfoList)
                {
                    addTPs(item.TPLists);
                }
                MainModel.FireDTDataChanged(this);
                MainModel.FireSetDefaultMapSerialTheme("lte_RSRP");
            }
            else if (xTabCtrHighSpeedRail.SelectedTabPageIndex == 1)
            {
                MainModel.ClearDTData();
                foreach (PrivateNetWeakSinrInfo item in weakSINRInfoList)
                {
                    addTPs(item.TPLists);
                }
                MainModel.FireDTDataChanged(this);
                MainModel.FireSetDefaultMapSerialTheme("lte_SINR");
            }
            else
            {
                MainModel.ClearDTData();
                foreach (OutPrivateNetInfo item in outPrivateNetInfoList)
                {
                    addTPs(item.TPLists);
                }
                MainModel.FireDTDataChanged(this);
                MainModel.FireSetDefaultMapSerialTheme("lte_RSRP");
            }
        }

        private void addTPs(List<TestPoint> tpLists)
        {
            foreach (TestPoint tp in tpLists)
            {
                MainModel.DTDataManager.Add(tp);
            }
        }

        private void gvOutPrivateNet_DoubleClick(object sender, EventArgs e)
        {
            OutPrivateNetInfo info = gvOutPrivateNet.GetFocusedRow() as OutPrivateNetInfo;
            if (info != null)
            {
                MainModel.ClearSelectedTestPoints();
                MainModel.DTDataManager.Clear();
                foreach (TestPoint tp in info.TPLists)
                {
                    if (tp.Longitude == 0 || tp.Latitude == 0)
                    {
                        continue;
                    }
                    MainModel.DTDataManager.Add(tp);
                }
                MainModel.FireDTDataChanged(MainModel.MainForm);
                OutlineOfRoad outRoad = new OutlineOfRoad();
                outRoad.SetPoints(info.TPLists);
                TempLayer.Instance.Draw(outRoad.Drawer);
            }
        }

        private void gvWeakSINR_DoubleClick(object sender, EventArgs e)
        {
            PrivateNetWeakSinrInfo info = gvWeakSINR.GetFocusedRow() as PrivateNetWeakSinrInfo;
            if (info != null)
            {
                MainModel.ClearSelectedTestPoints();
                MainModel.DTDataManager.Clear();
                foreach (TestPoint tp in info.TPLists)
                {
                    if (tp.Longitude == 0 || tp.Latitude == 0)
                    {
                        continue;
                    }
                    MainModel.DTDataManager.Add(tp);
                }
                MainModel.FireDTDataChanged(MainModel.MainForm);
                OutlineOfRoad outRoad = new OutlineOfRoad();
                outRoad.SetPoints(info.TPLists);
                TempLayer.Instance.Draw(outRoad.Drawer);
            }
        }

        private void gvWeakCover_DoubleClick(object sender, EventArgs e)
        {
            PrivateNetWeakCoverInfo info = gvWeakCover.GetFocusedRow() as PrivateNetWeakCoverInfo;
            if (info != null)
            {
                MainModel.ClearSelectedTestPoints();
                MainModel.DTDataManager.Clear();
                foreach (TestPoint tp in info.TPLists)
                {
                    if (tp.Longitude == 0 || tp.Latitude == 0)
                    {
                        continue;
                    }
                    MainModel.DTDataManager.Add(tp);
                }
                MainModel.FireDTDataChanged(MainModel.MainForm);
                OutlineOfRoad outRoad = new OutlineOfRoad();
                outRoad.SetPoints(info.TPLists);
                TempLayer.Instance.Draw(outRoad.Drawer);
            }
        }
    }
}
