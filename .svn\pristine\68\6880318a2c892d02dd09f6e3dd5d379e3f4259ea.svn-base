﻿namespace MasterCom.RAMS.Func
{
    partial class MultiTimePeriodChooser
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.btnOK = new System.Windows.Forms.Button();
            this.btnCancel = new System.Windows.Forms.Button();
            this.listBoxTimePeriods = new System.Windows.Forms.ListBox();
            this.btnAdd = new System.Windows.Forms.Button();
            this.btnDel = new System.Windows.Forms.Button();
            this.label2 = new System.Windows.Forms.Label();
            this.pickerDateEnd = new System.Windows.Forms.DateTimePicker();
            this.pickerTimeEnd = new System.Windows.Forms.DateTimePicker();
            this.label1 = new System.Windows.Forms.Label();
            this.pickerDateStart = new System.Windows.Forms.DateTimePicker();
            this.pickerTimeStart = new System.Windows.Forms.DateTimePicker();
            this.SuspendLayout();
            // 
            // btnOK
            // 
            this.btnOK.Location = new System.Drawing.Point(256, 366);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 24;
            this.btnOK.Text = "确定";
            this.btnOK.UseVisualStyleBackColor = true;
            // 
            // btnCancel
            // 
            this.btnCancel.Location = new System.Drawing.Point(337, 366);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 25;
            this.btnCancel.Text = "取消";
            this.btnCancel.UseVisualStyleBackColor = true;
            // 
            // listBoxTimePeriods
            // 
            this.listBoxTimePeriods.FormattingEnabled = true;
            this.listBoxTimePeriods.ItemHeight = 14;
            this.listBoxTimePeriods.Location = new System.Drawing.Point(12, 100);
            this.listBoxTimePeriods.Name = "listBoxTimePeriods";
            this.listBoxTimePeriods.Size = new System.Drawing.Size(400, 256);
            this.listBoxTimePeriods.TabIndex = 23;
            // 
            // btnAdd
            // 
            this.btnAdd.Location = new System.Drawing.Point(337, 16);
            this.btnAdd.Name = "btnAdd";
            this.btnAdd.Size = new System.Drawing.Size(75, 23);
            this.btnAdd.TabIndex = 21;
            this.btnAdd.Text = "添加";
            this.btnAdd.UseVisualStyleBackColor = true;
            // 
            // btnDel
            // 
            this.btnDel.Location = new System.Drawing.Point(337, 54);
            this.btnDel.Name = "btnDel";
            this.btnDel.Size = new System.Drawing.Size(75, 23);
            this.btnDel.TabIndex = 22;
            this.btnDel.Text = "删除";
            this.btnDel.UseVisualStyleBackColor = true;
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(19, 60);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(59, 12);
            this.label2.TabIndex = 27;
            this.label2.Text = "结束时间:";
            // 
            // pickerDateEnd
            // 
            this.pickerDateEnd.CustomFormat = "yyyy-MM-dd HH:mm";
            this.pickerDateEnd.Location = new System.Drawing.Point(84, 56);
            this.pickerDateEnd.Name = "pickerDateEnd";
            this.pickerDateEnd.Size = new System.Drawing.Size(118, 21);
            this.pickerDateEnd.TabIndex = 19;
            // 
            // pickerTimeEnd
            // 
            this.pickerTimeEnd.CustomFormat = "HH:mm:ss";
            this.pickerTimeEnd.Format = System.Windows.Forms.DateTimePickerFormat.Custom;
            this.pickerTimeEnd.Location = new System.Drawing.Point(223, 56);
            this.pickerTimeEnd.Name = "pickerTimeEnd";
            this.pickerTimeEnd.ShowUpDown = true;
            this.pickerTimeEnd.Size = new System.Drawing.Size(81, 21);
            this.pickerTimeEnd.TabIndex = 20;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(19, 22);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(59, 12);
            this.label1.TabIndex = 26;
            this.label1.Text = "开始时间:";
            // 
            // pickerDateStart
            // 
            this.pickerDateStart.CustomFormat = "yyyy-MM-dd HH:mm";
            this.pickerDateStart.Location = new System.Drawing.Point(84, 18);
            this.pickerDateStart.Name = "pickerDateStart";
            this.pickerDateStart.Size = new System.Drawing.Size(118, 21);
            this.pickerDateStart.TabIndex = 17;
            // 
            // pickerTimeStart
            // 
            this.pickerTimeStart.CustomFormat = "HH:mm:ss";
            this.pickerTimeStart.Format = System.Windows.Forms.DateTimePickerFormat.Custom;
            this.pickerTimeStart.Location = new System.Drawing.Point(223, 18);
            this.pickerTimeStart.Name = "pickerTimeStart";
            this.pickerTimeStart.ShowUpDown = true;
            this.pickerTimeStart.Size = new System.Drawing.Size(81, 21);
            this.pickerTimeStart.TabIndex = 18;
            // 
            // MultiTimePeriodChooser
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(426, 400);
            this.Controls.Add(this.btnOK);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.listBoxTimePeriods);
            this.Controls.Add(this.btnAdd);
            this.Controls.Add(this.btnDel);
            this.Controls.Add(this.label2);
            this.Controls.Add(this.pickerDateEnd);
            this.Controls.Add(this.pickerTimeEnd);
            this.Controls.Add(this.label1);
            this.Controls.Add(this.pickerDateStart);
            this.Controls.Add(this.pickerTimeStart);
            this.Name = "MultiTimePeriodChooser";
            this.Text = "多时间段选择";
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.Button btnOK;
        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.ListBox listBoxTimePeriods;
        private System.Windows.Forms.Button btnAdd;
        private System.Windows.Forms.Button btnDel;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.DateTimePicker pickerDateEnd;
        private System.Windows.Forms.DateTimePicker pickerTimeEnd;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.DateTimePicker pickerDateStart;
        private System.Windows.Forms.DateTimePicker pickerTimeStart;
    }
}