﻿using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc.ZTNRDominantAreaAna
{
    public class DiyInsertSceneInfo : DiySqlMultiNonQuery
    {
        public const string TableName = "tb_NRDominantArea_Scene";
        readonly List<NRDominantAreaSceneInfo> infolist = null;
        public DiyInsertSceneInfo(List<NRDominantAreaSceneInfo> infolist)
        {
            MainDB = true;
            this.infolist = infolist;
        }

        protected override void query()
        {
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, userName, password, dbid > 0 ? dbid : MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
                WaitBox.Text = "正在向数据库导入数据......";
                queryInThread(clientProxy);
            }
            catch (Exception ee)
            {
                MessageBox.Show(ee.Message + Environment.NewLine + ee.Source + Environment.NewLine + ee.StackTrace);
            }
            finally
            {
                clientProxy.Close();
            }
        }

        protected override void queryInThread(object o)
        {
            base.queryInThread(o);
            System.Threading.Thread.Sleep(200);
            WaitBox.Text = "导入完毕.....";
        }

        protected override string getSqlTextString()
        {
            StringBuilder strb = new StringBuilder();

            strb.Append($@"IF NOT EXISTS(SELECT 1 FROM sysobjects WHERE type = 'U' AND name = '{TableName}')
                BEGIN
                    CREATE TABLE {TableName} (
                        [地市]        VARCHAR(50), 
                        [区县]        VARCHAR(50), 
                        [场景区域]     VARCHAR(50), 
                        [场景类型]     VARCHAR(10)
                    );
                END;
                truncate table {TableName};
            ");
            foreach (NRDominantAreaSceneInfo info in infolist)
            {
                strb.Append($@"insert into [{TableName}]([地市],[区县],[场景区域],[场景类型]) values ('{info.DistrictName}','{info.CountryName}','{info.SceneName}','{info.SceneType}');");
            }

            return strb.ToString();
        }

        public override string Name
        {
            get { return "导入场景信息表"; }
        }
    }
}
