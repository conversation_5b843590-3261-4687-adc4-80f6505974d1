﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class NRPoorSINRPnl
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.grpOutdoorWC = new System.Windows.Forms.GroupBox();
            this.numWCRSRPMax = new DevExpress.XtraEditors.SpinEdit();
            this.label9 = new System.Windows.Forms.Label();
            this.label12 = new System.Windows.Forms.Label();
            this.numSINRMax = new DevExpress.XtraEditors.SpinEdit();
            this.grpOverCover = new System.Windows.Forms.GroupBox();
            this.label2 = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.numRSRPDiff = new DevExpress.XtraEditors.SpinEdit();
            this.label4 = new System.Windows.Forms.Label();
            this.label15 = new System.Windows.Forms.Label();
            this.groupBox1.SuspendLayout();
            this.grpOutdoorWC.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numWCRSRPMax.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSINRMax.Properties)).BeginInit();
            this.grpOverCover.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numRSRPDiff.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // groupBox1
            // 
            this.groupBox1.AutoSizeMode = System.Windows.Forms.AutoSizeMode.GrowAndShrink;
            this.groupBox1.Controls.Add(this.grpOutdoorWC);
            this.groupBox1.Controls.Add(this.numSINRMax);
            this.groupBox1.Controls.Add(this.grpOverCover);
            this.groupBox1.Controls.Add(this.label15);
            this.groupBox1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupBox1.Location = new System.Drawing.Point(0, 0);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(416, 199);
            this.groupBox1.TabIndex = 3;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "SINR差";
            // 
            // grpOutdoorWC
            // 
            this.grpOutdoorWC.Controls.Add(this.numWCRSRPMax);
            this.grpOutdoorWC.Controls.Add(this.label9);
            this.grpOutdoorWC.Controls.Add(this.label12);
            this.grpOutdoorWC.Location = new System.Drawing.Point(33, 132);
            this.grpOutdoorWC.Name = "grpOutdoorWC";
            this.grpOutdoorWC.Size = new System.Drawing.Size(355, 49);
            this.grpOutdoorWC.TabIndex = 2;
            this.grpOutdoorWC.TabStop = false;
            this.grpOutdoorWC.Text = "弱覆盖原因";
            // 
            // numWCRSRPMax
            // 
            this.numWCRSRPMax.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.numWCRSRPMax.Location = new System.Drawing.Point(86, 16);
            this.numWCRSRPMax.Name = "numWCRSRPMax";
            this.numWCRSRPMax.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numWCRSRPMax.Properties.MaxValue = new decimal(new int[] {
            25,
            0,
            0,
            0});
            this.numWCRSRPMax.Properties.MinValue = new decimal(new int[] {
            141,
            0,
            0,
            -2147483648});
            this.numWCRSRPMax.Size = new System.Drawing.Size(60, 21);
            this.numWCRSRPMax.TabIndex = 1;
            // 
            // label9
            // 
            this.label9.AutoSize = true;
            this.label9.Location = new System.Drawing.Point(151, 21);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(23, 12);
            this.label9.TabIndex = 0;
            this.label9.Text = "dBm";
            // 
            // label12
            // 
            this.label12.AutoSize = true;
            this.label12.Location = new System.Drawing.Point(15, 21);
            this.label12.Name = "label12";
            this.label12.Size = new System.Drawing.Size(65, 12);
            this.label12.TabIndex = 0;
            this.label12.Text = "信号强度＜";
            // 
            // numSINRMax
            // 
            this.numSINRMax.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.numSINRMax.Location = new System.Drawing.Point(86, 20);
            this.numSINRMax.Name = "numSINRMax";
            this.numSINRMax.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numSINRMax.Properties.MaxValue = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numSINRMax.Size = new System.Drawing.Size(60, 21);
            this.numSINRMax.TabIndex = 0;
            // 
            // grpOverCover
            // 
            this.grpOverCover.Controls.Add(this.label2);
            this.grpOverCover.Controls.Add(this.label3);
            this.grpOverCover.Controls.Add(this.numRSRPDiff);
            this.grpOverCover.Controls.Add(this.label4);
            this.grpOverCover.ForeColor = System.Drawing.SystemColors.ControlText;
            this.grpOverCover.Location = new System.Drawing.Point(33, 52);
            this.grpOverCover.Name = "grpOverCover";
            this.grpOverCover.Size = new System.Drawing.Size(355, 74);
            this.grpOverCover.TabIndex = 0;
            this.grpOverCover.TabStop = false;
            this.grpOverCover.Text = "PCI模三冲突";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(15, 50);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(161, 12);
            this.label2.TabIndex = 0;
            this.label2.Text = "存在与主服PCI模3相同的小区";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(151, 21);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(89, 12);
            this.label3.TabIndex = 0;
            this.label3.Text = "dB内的邻区中，";
            // 
            // numRSRPDiff
            // 
            this.numRSRPDiff.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.numRSRPDiff.Location = new System.Drawing.Point(85, 16);
            this.numRSRPDiff.Name = "numRSRPDiff";
            this.numRSRPDiff.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numRSRPDiff.Properties.MaxValue = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numRSRPDiff.Size = new System.Drawing.Size(60, 21);
            this.numRSRPDiff.TabIndex = 0;
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(15, 21);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(65, 12);
            this.label4.TabIndex = 0;
            this.label4.Text = "与主服相差";
            // 
            // label15
            // 
            this.label15.AutoSize = true;
            this.label15.Location = new System.Drawing.Point(39, 27);
            this.label15.Name = "label15";
            this.label15.Size = new System.Drawing.Size(41, 12);
            this.label15.TabIndex = 0;
            this.label15.Text = "SINR＜";
            // 
            // NRPoorSINRPnl
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.groupBox1);
            this.Name = "NRPoorSINRPnl";
            this.Size = new System.Drawing.Size(416, 199);
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.grpOutdoorWC.ResumeLayout(false);
            this.grpOutdoorWC.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numWCRSRPMax.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSINRMax.Properties)).EndInit();
            this.grpOverCover.ResumeLayout(false);
            this.grpOverCover.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numRSRPDiff.Properties)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.GroupBox grpOutdoorWC;
        private DevExpress.XtraEditors.SpinEdit numWCRSRPMax;
        private System.Windows.Forms.Label label9;
        private System.Windows.Forms.Label label12;
        private DevExpress.XtraEditors.SpinEdit numSINRMax;
        private System.Windows.Forms.GroupBox grpOverCover;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label label3;
        private DevExpress.XtraEditors.SpinEdit numRSRPDiff;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.Label label15;
    }
}
