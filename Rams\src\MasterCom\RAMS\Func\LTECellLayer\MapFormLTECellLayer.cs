﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.MTGis;
using System.Drawing;
using MasterCom.RAMS.Model;
using System.Drawing.Drawing2D;
using MapWinGIS;

namespace MasterCom.RAMS.Func
{
    [Serializable()]
    public class MapLTECellLayer : LayerBase
    {
        public void SetDrawCustomCellDic(Dictionary<int,Color> dic)
        {
            this.dicCustomCellECIColorDic = dic;
            drawCustomCellColor = dic != null;
            this.Invalidate();
        }
        internal void ClearCellDicData()
        {
            this.dicCustomCellECIColorDic = null;
            drawCustomCellColor = false;
            this.Invalidate();
        }
        public bool DrawCustomCellColor
        {
            get
            {
                return drawCustomCellColor;
            }
        }
        private bool drawCustomCellColor = false;
        public Dictionary<int, Color> DicCustomCellECIColorDic
        {
            get{ return dicCustomCellECIColorDic;}
        }
        private Dictionary<int, Color> dicCustomCellECIColorDic = null;
        public Dictionary<Color, OneColorBrushPen> TmpSolidBrushDic
        {
            get
            {
                return tmpSolidBrushDic;
            }
        }
        private Dictionary<Color, OneColorBrushPen> tmpSolidBrushDic = new Dictionary<Color, OneColorBrushPen>();
        /// <summary>
        /// 画小区的方式
        /// </summary>
        public DrawCellMode DrawCellMode { get; set; } = DrawCellMode.Default;

        private Brush brushLTENeighbourLTECell = new SolidBrush(Color.LimeGreen);
        public Brush BrushLTENeighbourLTECell
        {
            get { return brushLTENeighbourLTECell; }
        }


        /// <summary>
        /// 是否显示最新工参，true：是；false:否。可在对应的图层控制里面选择，或在功能点工参显示时间设置统一设置所有图层工参时间点。
        /// </summary>
        public static bool DrawCurrent { get; set; } = true;
        /// <summary>
        /// 当DrawCurrent为false时，显示该快照时间的工参
        /// </summary>
        public static DateTime CurShowSnapshotTime { get; set; } = DateTime.Now;
        /// <summary>
        /// 小区默认显示长度
        /// </summary>
        public static readonly float CellDefaultDisplayLength = 20f;
        /// <summary>
        /// 全向小区默认显示长度
        /// </summary>
        public static readonly float CellOmniDefaultDisplayLength = 3f;
        /// <summary>
        /// 单位长度、宽度的小区path，通过Matrix放大缩小
        /// </summary>
        private static List<GraphicsPath> cellPathFactor { get; set; }
        /// <summary>
        ///  单位长度、宽度的天线path，通过Matrix放大缩小
        /// </summary>
        private List<GraphicsPath> antennaPathFactor { get; set; }
        /// <summary>
        /// 图元长度比例，数值越大，长度越大。值域为【1，10】
        /// </summary>
        private static float shapeLengthScale = 1;
        public static float ShapeLengthScale
        {
            get { return shapeLengthScale; }
            set
            {
                if (value < 1)
                {
                    shapeLengthScale = 1;
                }
                else if (value > 10)
                {
                    shapeLengthScale = 10;
                }
                else
                {
                    shapeLengthScale = value;
                }
            }
        }

        /// <summary>
        /// 图元宽度比例，数值越大，宽度越大。值域为【1，10】
        /// </summary>
        private static float shapeWidthScale = 1;
        public static float ShapeWidthScale
        {
            get { return shapeWidthScale; }
            set
            {
                if (value < 1)
                {
                    shapeWidthScale = 1;
                }
                else if (value > 10)
                {
                    shapeWidthScale = 10;
                }
                else
                {
                    shapeWidthScale = value;
                }
            }
        }

        public MapLTECellLayer(string name)
            : base(name)
        {
            this.VisibleScaleEnabled = true;
            this.VisibleScale = new VisibleScale(0, 80000);
            initPath();
#if Beijing
            CellShapeIdx = 1;
            ColorCell = Color.Orange;
#endif
        }

        public int CellShapeIdx
        {
            get;
            set;
        }

        private void initPath()
        {
            GraphicsPath path = new GraphicsPath();
            cellPathFactor = new List<GraphicsPath>();
            float length = CellOmniDefaultDisplayLength;
            path.AddEllipse(0, 0, length, length);//室内
            cellPathFactor.Add(path);

            #region 扇形
            path = new GraphicsPath();
            length = CellDefaultDisplayLength;
            //扇形的圆心坐标应为0,0
            path.AddPie(-length, -length, length + length, length + length, -15, 30);
            cellPathFactor.Add(path);
            //D-F
            for (int i = 1; i < 4; i++)
            {
                cellPathFactor.Add(transformPath(path, 1 + (0.2f * i), 1));
            }
#if NBIOTLayer
            //NBIOT小区图形
            cellPathFactor.Add(transformPath(path, 1 + (0.2f * 5), 1));
#endif
            #endregion

            #region 纺锤形
            float x1 = 0.75f;
            float y1 = 0.16f;
            float x2 = 0.85f;
            float y2 = 0.18f;
            float x3 = 0.92f;
            float y3 = 0.16f;
            float x4 = 0.96f;
            float y4 = 0.12f;
            float x5 = 0.99f;
            float y5 = 0.07f;
            PointF[] cellPoints = new PointF[] 
                {
                    new PointF(0, 0), 
                    new PointF(length * x1, -length * y1), 
                    new PointF(length * x2, -length * y2), 
                    new PointF(length * x3, -length * y3), 
                    new PointF(length * x4, -length * y4), 
                    new PointF(length * x5, -length * y5), 
                    new PointF(length, 0),
                    new PointF(length * x5, length * y5), 
                    new PointF(length * x4, length * y4), 
                    new PointF(length * x3, length * y3), 
                    new PointF(length * x2, length * y2), 
                    new PointF(length * x1, length * y1)
                };
            path = new GraphicsPath();
            path.AddPolygon(cellPoints);

            cellPathFactor.Add(path);
            //D-F
            for (int i = 1; i < 4; i++)
            {
                cellPathFactor.Add(transformPath(path, 1 + (0.2f * i), 1));
            }
            #endregion

            path = new GraphicsPath();
            antennaPathFactor = new List<GraphicsPath>();
            length = CellOmniDefaultDisplayLength;
            path.AddEllipse(0, 0, length, length);//室内
            antennaPathFactor.Add(path);

            length = CellDefaultDisplayLength;
            path = new GraphicsPath();
            path.AddPolygon(new PointF[] 
                {
                    new PointF(0, 0), //第一点为原点
                    new PointF(0, -1), 
                    new PointF(length - 3, -1), 
                    new PointF(length - 5, -5), 
                    new PointF(length, 0), 
                    new PointF(length - 5, 5), 
                    new PointF(length - 3, 1), 
                    new PointF(0, 1), 
                });
            antennaPathFactor.Add(path);
            for (int i = 1; i < 4; i++)
            {
                antennaPathFactor.Add(transformPath(path, 1 + (0.2f * i), 1));
            }

#if NBIOTLayer
            //NBIOT天线图形
            antennaPathFactor.Add(transformPath(path, 1 + (0.2f * 5), 1));
#endif

            TransformDisplayShape(shapeLengthScale, shapeWidthScale);
        }

        /// <summary>
        /// 按比例转变小区、天线图元（放大、缩小）
        /// </summary>
        /// <param name="lengthScale"></param>
        /// <param name="widthScale"></param>
        public void TransformDisplayShape(float lengthScale, float widthScale)
        {
            ShapeLengthScale = lengthScale;
            ShapeWidthScale = widthScale;
            cellPaths = new List<GraphicsPath>();
            transformPathOriginAlign(cellPathFactor, cellPaths, lengthScale, widthScale);
            antennaPaths = new List<GraphicsPath>();
            transformPathOriginAlign(antennaPathFactor, antennaPaths, lengthScale, widthScale);
        }

        /// <summary>
        /// 保持原点，放大缩小图形
        /// </summary>
        /// <param name="basePaths"></param>
        /// <param name="transformPaths"></param>
        /// <param name="xScale"></param>
        /// <param name="yScale"></param>
        private void transformPathOriginAlign(List<GraphicsPath> basePaths, List<GraphicsPath> transformPaths, float xScale, float yScale)
        {
            transformPaths.Clear();
            foreach (GraphicsPath path in basePaths)
            {
                transformPaths.Add(transformPath(path,xScale,yScale));
            }
        }

        private GraphicsPath transformPath(GraphicsPath orgPath, float xScale, float yScale)
        {
            //保持基础形状不变，只转换克隆的path
            GraphicsPath clonePath = orgPath.Clone() as GraphicsPath;
            Matrix matrix = new Matrix();
            matrix.Reset();
            matrix.Scale(xScale, yScale);
            //原点，必须为0,0
            PointF oldOrigin = new PointF(clonePath.PathPoints[0].X, clonePath.PathPoints[0].Y);
            clonePath.Transform(matrix);
            //缩放后的“原点”
            PointF newOrigin = clonePath.PathPoints[0];
            matrix.Reset();
            //图形偏移回原点
            matrix.Translate(oldOrigin.X - newOrigin.X, oldOrigin.Y - newOrigin.Y);
            clonePath.Transform(matrix);
            return clonePath;
        }


        public List<PointF[]> GetLteHighlightPoints()
        {
            List<PointF[]> lteHighlightPoints = new List<PointF[]>();
            if (cellPathFactor != null)
            {
                foreach (GraphicsPath gp in cellPathFactor)
                {
                    lteHighlightPoints.Add(gp.PathPoints);
                }
            }
            return lteHighlightPoints;
        }


        public List<PointF[]> GetAntennaPoints()
        {
            List<PointF[]> antennaPoints = new List<PointF[]>();
            if (antennaPathFactor != null)
            {
                foreach (GraphicsPath gp in antennaPathFactor)
                {
                    antennaPoints.Add(gp.PathPoints);
                }
            }
            return antennaPoints;
        }
        #region path
        private List<GraphicsPath> cellPaths = new List<GraphicsPath>();
        public List<GraphicsPath> CellPaths
        {
            get { return cellPaths; }
        }

        private List<GraphicsPath> antennaPaths = new List<GraphicsPath>();
        public List<GraphicsPath> AntennaPaths
        {
            get { return antennaPaths; }
        }
        #endregion

        /// <summary>
        /// 当前可视区域内小区
        /// </summary>
        List<LTECell> cellsInView = null;

        public void SetActiveCellsInView(List<LTECell> list)
        {
            this.cellsInView = list;
        }

        /// <summary>
        /// 当前可视区域内天线
        /// </summary>
        List<LTEAntenna> antennasInView = null;

        /// <summary>
        /// 当期可视区域内BTS
        /// </summary>
        List<LTEBTS> btssInView = null;

        /// <summary>
        /// 各图元绘画的先后顺序：小区，天线，基站；标签的绘画的顺序刚好倒过来。
        /// </summary>
        /// <param name="clientRect"></param>
        /// <param name="updateRect"></param>
        /// <param name="graphics"></param>
        public override void Draw(System.Drawing.Rectangle clientRect, System.Drawing.Rectangle updateRect
            , Graphics graphics)
        {
            if (!VisibleScale.IsWithinScale(mapScale))
            {
                return;
            }
            cellsInView = null;
            antennasInView = null;
            btssInView = null;
            updateRect.Inflate((int)(400000 / mapScale), (int)(400000 / mapScale));
            DbRect dRect;
            gisAdapter.FromDisplay(updateRect, out dRect);
            graphics.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.HighQuality;
            if (DrawCell)
            {//画小区
                cellsInView = drawCells(graphics, dRect, mapScale);
            }
            if (DrawAntenna)
            {//画天线
                antennasInView = drawAntennas(graphics, dRect, mapScale);
            }
            if (DrawBTS)
            {//画BTS
                btssInView = drawBTSs(graphics, dRect);
            }
            bool isValidScale = judgeValidScale();
            drawBTSLabels(graphics, dRect, isValidScale);
            drawAntennasLabel(graphics, dRect, isValidScale);
            drawCellsLabel(graphics, dRect, isValidScale);

            bool needDraw = judgeDrawSimulationLine();
            if (needDraw) //天线分析采样点覆盖仿真点
            {
                drawSimulationLine(graphics);
            }
        }

        private bool judgeValidScale()
        {
            return (curMapType == LayerMapType.MTGis || curMapType == LayerMapType.Google && mapScale < 50000);
        }

        #region 小区相关
        private List<LTECell> getCellsInView(DbRect dRect)
        {
            List<LTECell> cellsView = null;
            List<LTECell> cells = null;
            if (DrawCurrent)
            {
                cells = CellManager.GetInstance().GetCurrentLTECells();
            }
            else
            {
                cells = CellManager.GetInstance().GetLTECells(CurShowSnapshotTime);
            }
            if (cells != null)
            {
                cellsView = new List<LTECell>();
                addCellsView(dRect, cellsView, cells);
            }
            if (DrawServer && mainModel.ServerLTECells != null && cellsView != null)
            {
                addCellsView(dRect, cellsView, mainModel.ServerLTECells);
            }
            return cellsView;
        }

        private void addCellsView(DbRect dRect, List<LTECell> cellsView, List<LTECell> cells)
        {
            foreach (LTECell cell in cells)
            {
                if (cell.Antennas.Count > 0
                    && cell.Within(dRect.x1, dRect.y1, dRect.x2, dRect.y2)
                    && judgeCoverType(cell.Type))
                {
                    cellsView.Add(cell);
                }
            }
        }

#if NBIOTLayer
        private List<LTECell> drawCells(Graphics graphics, DbRect dRect, double mapScale)
        {
            List<LTECell> curCellsInView = getCellsInView(dRect);
            if (curCellsInView != null)
            {
                foreach (LTECell cell in curCellsInView)
                {
                    //LTE可见&&不绘制NB图层时,无需判断小区频点直接绘制LTE小区
                    if (LTEVisible && !SeparateNBIOTInfo)
                    {
                        paintCell(cell, graphics, mapScale, false);
                    }
                    else
                    {
                        bool isNBIOTCell = NBIOTSetting.IsValidNBIOT(cell.EARFCN);
                        //绘制NB小区,小区频点在设置的NB频段内
                        if (NBIOTVisible && isNBIOTCell)
                        {
                            paintCell(cell, graphics, mapScale, true);
                        }
                        else if (LTEVisible && !isNBIOTCell)
                        {
                            paintCell(cell, graphics, mapScale, false);
                        }
                    }
                }
            }
            return curCellsInView;
        }

        private void paintCell(LTECell cell, Graphics graphics, double scale, bool isNBIOTCell)
        {/*小区画刷选取顺序：
          * 1）默认设置颜色画刷
          * 2）主服小区颜色画刷
          * 3）专题专题功能设置画刷
            */
            if (cell.Antennas.Count == 0)
            {
                return;
            }
            DbPoint dPoint = new DbPoint(cell.Longitude, cell.Latitude);
            PointF point;
            gisAdapter.ToDisplay(dPoint, out point);
            Brush brush = brushCell;
            int index = 0;//默认为室内小区index

           
            if (isNBIOTCell)
            {
                if (cell.Type != LTEBTSType.Indoor)
                {
                    index = 5;
                }
                else
                {
                    brush = NBIOTSetting.BrushCell;
                }
            }
            else
            {
                if (cell.DirectionType == LTEAntennaDirectionType.Beam && cell.Type != LTEBTSType.Indoor)
                {
                    switch (cell.BandType)
                    {
                        case LTEBandType.Undefined:
                        case LTEBandType.A:
                            index = 1 + (CellShapeIdx * 4);
                            break;
                        case LTEBandType.D:
                        case LTEBandType.D_37900:
                        case LTEBandType.D_38100:
                        case LTEBandType.D_38098:
                            index = 2 + (CellShapeIdx * 4);
                            break;
                        case LTEBandType.E:
                            index = 3 + (CellShapeIdx * 4);
                            break;
                        case LTEBandType.F:
                        case LTEBandType.F_38350:
                        case LTEBandType.F_38400:
                        case LTEBandType.F_38544:
                            index = 4 + (CellShapeIdx * 4);
                            break;
                        default:
                            break;
                    }
                }
            }
            if (DrawServer)
            {
                for (int i = 0; i < mainModel.ServerLTECells.Count; i++)
                {
                    if (mainModel.ServerLTECells[i] == cell)
                    {
                        if (mainModel.DrawDifferentServerColor)
                        {
                            brush = new SolidBrush(cell.ServerCellColor);
                        }
                        else
                        {
                            brush = brushServerCell;
                        }
                        break;
                    }
                }
            }
            if (curMapType == LayerMapType.MTGis)
            {
                getCellMultiCovInfoBrush(cell, ref brush);
            }
            else if (curMapType == LayerMapType.Google && mainModel.OutServiceInfoManager.isContainSnapShot(MasterCom.RAMS.Func.OutServiceInfo.NetType.LTE, cell))
            {
                brush = BrushAlarmCell;
            }

            List<GraphicsPath> paths = null;
            paths = cellPaths;
            graphics.TranslateTransform(point.X, point.Y);//把小区坐标当作坐标原点
            graphics.RotateTransform(cell.Direction - 90);//方向角的计算，以-90°为坐标0°，故要选中旋转坐标为方向角度-90
            scale = getDisplayScale(scale);
            graphics.ScaleTransform((float)(10000 / scale), (float)(10000 / scale));

            int newPci;
            if (!int.TryParse(cell.NewPCI, out newPci))
            {
                newPci = cell.PCI;
            }

            Pen curActiveCellPen = null;
            Pen cellPen = null;
            if (this.drawCustomCellColor && dicCustomCellECIColorDic != null)
            {
                Color cuColor;
                if (dicCustomCellECIColorDic.TryGetValue(cell.ECI, out cuColor))
                {
                    OneColorBrushPen oneColorBrushPen;
                    if (!tmpSolidBrushDic.TryGetValue(cuColor, out oneColorBrushPen))
                    {
                        oneColorBrushPen = new OneColorBrushPen();
                        oneColorBrushPen.theBrush = new SolidBrush(cuColor);
                        oneColorBrushPen.thePen = new Pen(cuColor);
                        tmpSolidBrushDic[cuColor] = oneColorBrushPen;
                    }
                    brush = oneColorBrushPen.theBrush;
                    cellPen = oneColorBrushPen.thePen;
                }
                else
                {
                    brush = noDataCellBrush;
                    cellPen = noDataCellPen;
                }
            }

            if (FDDSetting.IsVisible)
            {
                bool isFddCell = FDDSetting.IsValidFdd(cell.EARFCN);
                if (isFddCell)
                {
                    brush = FDDSetting.CellBrush;
                    cellPen = FDDSetting.CellPen;
                }
            }

            switch (this.DrawCellMode)
            {
                case DrawCellMode.Default:
                    graphics.FillPath(brush, paths[index]);
                    break;
                case DrawCellMode.PCI_Mod3:
                    cell.PCI_MOD3 = (LTEPCI_Mod3)(newPci % 3);
                    drawCellMod3(cell, graphics, index);
                    break;
                case DrawCellMode.PCI_Mod6:
                    cell.PCI_MOD6 = (LTEPCI_Mod6)(newPci % 6);
                    drawCellMod6(cell, graphics, index);
                    break;
            }

            if (mainModel.SelectedLTECell == cell || (mainModel.SelectedLTECells != null && mainModel.SelectedLTECells.Contains(cell)))
            {
                switch (cell.CellType)
                {
                    case MainOrNBCell.MainCell:
                        graphics.FillPath(selecctedBrushMainCell, paths[index]);
                        break;
                    case MainOrNBCell.NBCell:
                        graphics.FillPath(selecctedBrushNBCell, paths[index]);
                        break;
                    default:
                        graphics.DrawPath(penSelected, paths[index]);
                        break;
                }
            }
            else if (mainModel.LTENeighbourLTECells.Contains(cell) && BrushNeighbourLTECell != null)
            {
                graphics.FillPath(BrushNeighbourLTECell, paths[index]);
            }

            if (isNBIOTCell)
            {
                cellPen = NBIOTSetting.PenEarfcn_NBIOT_Cell;
            }
            else
            {
                if (cellPen == null)
                {
                    switch (cell.BandType)
                    {
                        case LTEBandType.Undefined:
                            break;
                        case LTEBandType.A:
                            cellPen = penEarfcn_A_Cell;
                            break;
                        case LTEBandType.D:
                            cellPen = penEarfcn_D_Cell;
                            break;
                        case LTEBandType.E:
                            cellPen = penEarfcn_E_Cell;
                            break;
                        case LTEBandType.F:
                            cellPen = penEarfcn_F_Cell;
                            break;
                        default:
                            break;
                    }
                }
            }

            if (curActiveCellPen != null)
            {
                graphics.DrawPath(curActiveCellPen, paths[index]);
            }
            else
            {
                if (cellPen != null)
                {
                    graphics.DrawPath(cellPen, paths[index]);
                }
            }

            graphics.ResetTransform();//还原坐标原点
        }
#else
        /// <summary>
        /// 先获取当前可视区域小区（主服小区），然后再遍历画形状
        /// </summary>
        /// <param name="graphics"></param>
        /// <param name="dRect"></param>
        /// <param name="mapScale"></param>
        /// <returns>当前可视区域小区（主服小区）</returns>
        private List<LTECell> drawCells(Graphics graphics, DbRect dRect, double mapScale)
        {
            List<LTECell> curCellsInView = getCellsInView(dRect);
            if (curCellsInView != null)
            {
                foreach (LTECell cell in curCellsInView)
                {
                    paintCell(cell, graphics, mapScale);
                }
            }
            return curCellsInView;
        }

        #region paintCell
        private void paintCell(LTECell cell, Graphics graphics, double scale)
        {/*小区画刷选取顺序：
          * 1）默认设置颜色画刷
          * 2）主服小区颜色画刷
          * 3）专题专题功能设置画刷
            */
            if (cell.Antennas.Count == 0)
            {
                return;
            }
            DbPoint dPoint = new DbPoint(cell.Longitude, cell.Latitude);
            PointF point;
            gisAdapter.ToDisplay(dPoint, out point);
            Brush brush = brushCell;
            int index = getCellShpIndex(cell);
            if (DrawServer)
            {
                brush = getServerCellBrush(cell, brush);
            }
            if (curMapType == LayerMapType.MTGis)
            {
                getCellMultiCovInfoBrush(cell, ref brush);
            }
            else if (curMapType == LayerMapType.Google && mainModel.OutServiceInfoManager.isContainSnapShot(MasterCom.RAMS.Func.OutServiceInfo.NetType.LTE, cell))
            {
                brush = BrushAlarmCell;
            }

            List<GraphicsPath> paths = null;
            paths = cellPaths;
            graphics.TranslateTransform(point.X, point.Y);//把小区坐标当作坐标原点
            graphics.RotateTransform(cell.Direction - 90);//方向角的计算，以-90°为坐标0°，故要选中旋转坐标为方向角度-90
            scale = getDisplayScale(scale);
            graphics.ScaleTransform((float)(10000 / scale), (float)(10000 / scale));

            int newPci;
            if (!int.TryParse(cell.NewPCI, out newPci))
            {
                newPci = cell.PCI;
            }

            Pen curActiveCellPen = null;
            Pen cellPen = null;
            getCustomCellPaintTool(cell, ref brush, ref cellPen);

            switch (this.DrawCellMode)
            {
                case DrawCellMode.Default:
                    graphics.FillPath(brush, paths[index]);
                    break;
                case DrawCellMode.PCI_Mod3:
                    cell.PCI_MOD3 = (LTEPCI_Mod3)(newPci % 3);
                    drawCellMod3(cell, graphics, index);
                    break;
                case DrawCellMode.PCI_Mod6:
                    cell.PCI_MOD6 = (LTEPCI_Mod6)(newPci % 6);
                    drawCellMod6(cell, graphics, index);
                    break;
            }

            drawSelected(cell, graphics, index, paths);

            cellPen = getCellPenByBandType(cell, cellPen);

            if (curActiveCellPen != null)
            {
                graphics.DrawPath(curActiveCellPen, paths[index]);
            }
            else
            {
                if (cellPen != null)
                {
                    graphics.DrawPath(cellPen, paths[index]);
                }
            }

            graphics.ResetTransform();//还原坐标原点
        }

        private int getCellShpIndex(LTECell cell)
        {
            int index = 0;//默认为室内小区index

            if (cell.DirectionType == LTEAntennaDirectionType.Beam && cell.Type != LTEBTSType.Indoor)
            {
                switch (cell.BandType)
                {
                    case LTEBandType.Undefined:
                    case LTEBandType.A:
                        index = 1 + (CellShapeIdx * 4);
                        break;
                    case LTEBandType.D:
                    case LTEBandType.D_37900:
                    case LTEBandType.D_38100:
                    case LTEBandType.D_38098:
                        index = 2 + (CellShapeIdx * 4);
                        break;
                    case LTEBandType.E:
                        index = 3 + (CellShapeIdx * 4);
                        break;
                    case LTEBandType.F:
                    case LTEBandType.F_38350:
                    case LTEBandType.F_38400:
                    case LTEBandType.F_38544:
                        index = 4 + (CellShapeIdx * 4);
                        break;
                    default:
                        break;
                }
            }

            return index;
        }

        private Brush getServerCellBrush(LTECell cell, Brush brush)
        {
            for (int i = 0; i < mainModel.ServerLTECells.Count; i++)
            {
                if (mainModel.ServerLTECells[i] == cell)
                {
                    if (mainModel.DrawDifferentServerColor)
                    {
                        brush = new SolidBrush(cell.ServerCellColor);
                    }
                    else
                    {
                        brush = brushServerCell;
                    }
                    break;
                }
            }

            return brush;
        }

        private void getCustomCellPaintTool(LTECell cell, ref Brush brush, ref Pen cellPen)
        {
            if (this.drawCustomCellColor && dicCustomCellECIColorDic != null)
            {
                Color cuColor;
                if (dicCustomCellECIColorDic.TryGetValue(cell.ECI, out cuColor))
                {
                    OneColorBrushPen oneColorBrushPen;
                    if (!tmpSolidBrushDic.TryGetValue(cuColor, out oneColorBrushPen))
                    {
                        oneColorBrushPen = new OneColorBrushPen();
                        oneColorBrushPen.theBrush = new SolidBrush(cuColor);
                        oneColorBrushPen.thePen = new Pen(cuColor);
                        tmpSolidBrushDic[cuColor] = oneColorBrushPen;
                    }
                    brush = oneColorBrushPen.theBrush;
                    cellPen = oneColorBrushPen.thePen;
                }
                else
                {
                    brush = noDataCellBrush;
                    cellPen = noDataCellPen;
                }
            }
        }

        private void drawSelected(LTECell cell, Graphics graphics, int index, List<GraphicsPath> paths)
        {
            if (mainModel.SelectedLTECell == cell || (mainModel.SelectedLTECells != null && mainModel.SelectedLTECells.Contains(cell)))
            {
                //graphics.DrawPath(penSelected, paths[index]);
                switch (cell.CellType)
                {
                    case MainOrNBCell.MainCell:
                        graphics.FillPath(selecctedBrushMainCell, paths[index]);
                        break;
                    case MainOrNBCell.NBCell:
                        graphics.FillPath(selecctedBrushNBCell, paths[index]);
                        break;
                    default:
                        graphics.DrawPath(penSelected, paths[index]);
                        break;
                }
            }
            else if (mainModel.LTENeighbourLTECells.Contains(cell) && BrushNeighbourLTECell != null)
            {
                graphics.FillPath(BrushNeighbourLTECell, paths[index]);
            }
        }

        private Pen getCellPenByBandType(LTECell cell, Pen cellPen)
        {
            if (cellPen == null)
            {
                switch (cell.BandType)
                {
                    case LTEBandType.Undefined:
                        break;
                    case LTEBandType.A:
                        cellPen = penEarfcn_A_Cell;
                        break;
                    case LTEBandType.D:
                        cellPen = penEarfcn_D_Cell;
                        break;
                    case LTEBandType.E:
                        cellPen = penEarfcn_E_Cell;
                        break;
                    case LTEBandType.F:
                        cellPen = penEarfcn_F_Cell;
                        break;
                    default:
                        break;
                }
            }

            return cellPen;
        }
        #endregion
#endif

        private void drawCellMod3(LTECell cell, Graphics graphics, int index)
        {
            // PCI模三画不同颜色
            switch (cell.PCI_MOD3)
            {
                case LTEPCI_Mod3.Value0:
                    graphics.FillPath(this.brushCell_Mod3_0, this.cellPaths[index]);
                    break;
                case LTEPCI_Mod3.Value1:
                    graphics.FillPath(this.brushCell_Mod3_1, this.cellPaths[index]);
                    break;
                case LTEPCI_Mod3.Value2:
                    graphics.FillPath(this.brushCell_Mod3_2, this.cellPaths[index]);
                    break;
            }
        }
        /**
        private Color getColorCellMod3(LTEPCI_Mod3 value)
        {
            Color color = Color.Empty;
            switch (value)
            {
                case LTEPCI_Mod3.Value0:
                    color = this.brushCell_Mod3_0.Color;
                    break;
                case LTEPCI_Mod3.Value1:
                    color = this.brushCell_Mod3_1.Color;
                    break;
                case LTEPCI_Mod3.Value2:
                    color = this.brushCell_Mod3_2.Color;
                    break;
            }
            return color;
        }
        */
        private void drawCellMod6(LTECell cell, Graphics graphics, int index)
        {
            // PCI模6画不同颜色
            switch (cell.PCI_MOD6)
            {
                case LTEPCI_Mod6.Value0:
                    graphics.FillPath(this.brushCell_Mod6_0, this.cellPaths[index]);
                    break;
                case LTEPCI_Mod6.Value1:
                    graphics.FillPath(this.brushCell_Mod6_1, this.cellPaths[index]);
                    break;
                case LTEPCI_Mod6.Value2:
                    graphics.FillPath(this.brushCell_Mod6_2, this.cellPaths[index]);
                    break;
                case LTEPCI_Mod6.Value3:
                    graphics.FillPath(this.brushCell_Mod6_3, this.cellPaths[index]);
                    break;
                case LTEPCI_Mod6.Value4:
                    graphics.FillPath(this.brushCell_Mod6_4, this.cellPaths[index]);
                    break;
                case LTEPCI_Mod6.Value5:
                    graphics.FillPath(this.brushCell_Mod6_5, this.cellPaths[index]);
                    break;
            }
        }
        /**
        private Color getColorCellMod6(LTEPCI_Mod6 value)
        {
            Color color = Color.Empty;
            switch (value)
            {
                case LTEPCI_Mod6.Value0:
                    color = this.brushCell_Mod6_0.Color;
                    break;
                case LTEPCI_Mod6.Value1:
                    color = this.brushCell_Mod6_1.Color;
                    break;
                case LTEPCI_Mod6.Value2:
                    color = this.brushCell_Mod6_2.Color;
                    break;
                case LTEPCI_Mod6.Value3:
                    color = this.brushCell_Mod6_3.Color;
                    break;
                case LTEPCI_Mod6.Value4:
                    color = this.brushCell_Mod6_4.Color;
                    break;
                case LTEPCI_Mod6.Value5:
                    color = this.brushCell_Mod6_5.Color;
                    break;
            }
            return color;
        }
        */
        private void getCellMultiCovInfoBrush(LTECell cell, ref Brush brush)
        {
            if (mainModel.CellMultiCovList_LTE == null && mainModel.CellMultiCovList_LTE.Count == 0)
            {
                return;
            }
            if (mainModel.SelectedCellMultiCov_LTE != null)
            {//双击小区重叠覆盖窗口行时，只显示对应的小区情况
                brush = getSelectedBrush(cell, brush);
            }
            else
            {
                brush = getNormalBrush(cell, brush);
            }
        }

        private Brush getSelectedBrush(LTECell cell, Brush brush)
        {
            Color clr = Color.Empty;
            if (mainModel.SelectedCellMultiCov_LTE.Cell == cell)
            {
                clr = Color.Red;
            }
            if (Color.Empty.Equals(clr) && mainModel.SelectedCellMultiCov_LTE.OtherCellDic.ContainsKey(cell.Name))
            {
                clr = Color.Cyan;
            }
            if (!Color.Empty.Equals(clr))
            {
                brush = new SolidBrush(clr);
            }

            return brush;
        }

        private Brush getNormalBrush(LTECell cell, Brush brush)
        {
            MasterCom.RAMS.ZTFunc.LTECellMultiCovInfo info = null;
            info = mainModel.CellMultiCovList_LTE.Find(delegate (MasterCom.RAMS.ZTFunc.LTECellMultiCovInfo item) { return item.Cell == cell; });
            if (info != null)
            {
                float rate = 0;
                switch (mainModel.CellMultiCovType)
                {
                    case MasterCom.RAMS.ZTFunc.CellMultiCovType.Abs:
                        rate = info.AbsRate;
                        break;
                    case MasterCom.RAMS.ZTFunc.CellMultiCovType.Rel:
                        rate = info.RelRate;
                        break;
                    case MasterCom.RAMS.ZTFunc.CellMultiCovType.Both:
                        rate = info.MulRate;
                        break;
                    default:
                        break;
                }
                foreach (MasterCom.MControls.ColorRange rng in mainModel.CellMultiCoverageRanges_LTE.Ranges)
                {
                    if (rate >= rng.minValue && rate < rng.maxValue)
                    {
                        brush = new SolidBrush(rng.color);
                    }
                }
            }

            return brush;
        }

        private void drawCellsLabel(Graphics graphics, DbRect dRect, bool isValidScale)
        {
            if (DrawCellLabel && isValidScale)
            {//画小区标签
                if (DrawCell)
                {
                    drawCellsLabel(cellsInView, graphics);
                }
                else
                {
                    drawCellsLabel(getCellsInView(dRect), graphics);
                }
            }
        }

        private void drawCellsLabel(List<LTECell> cells, Graphics graphics)
        {
            DrawedCellLabelRectangles = new List<Rectangle>();
            if (cells == null)
            {
                return;
            }
            foreach (LTECell cell in cells)
            {
#if NBIOTLayer
                //LTE可见&&不绘制NB图层时,无需判断小区频点直接绘制LTE小区
                if (LTEVisible && !SeparateNBIOTInfo)
                {
                    paintCellLabel(cell, graphics);
                }
                else
                {
                    bool isNBIOTCell = NBIOTSetting.IsValidNBIOT(cell.EARFCN);
                    //绘制NB小区,小区频点在设置的NB频段内
                    if (NBIOTVisible && isNBIOTCell)
                    {
                        paintCellLabel(cell, graphics);
                    }
                    else if (LTEVisible && !isNBIOTCell)
                    {
                        paintCellLabel(cell, graphics);
                    }
                }
#else
                paintCellLabel(cell, graphics);
#endif
            }
        }

        private void paintCellLabel(LTECell cell, Graphics graphics)
        {
            DbPoint dPoint = new DbPoint(cell.EndPointLongitude, cell.EndPointLatitude);
            PointF point;
            gisAdapter.ToDisplay(dPoint, out point);
            graphics.TranslateTransform(point.X, point.Y);
            string cellDes = getCellLabelDes(cell, 100);
            SizeF size = graphics.MeasureString(cellDes, FontCellLabel);
            size.Height *= 0.8f;
            System.Drawing.Rectangle rectangle = new System.Drawing.Rectangle((int)point.X - 3, (int)(point.Y - (size.Height / 2 - 5)), (int)size.Width + 10, (int)size.Height + 10);
            bool needDraw = true;
            foreach (System.Drawing.Rectangle rectangleTemp in DrawedCellLabelRectangles)
            {
                if (rectangle.Right >= rectangleTemp.Left && rectangle.Left <= rectangleTemp.Right
                    && rectangle.Bottom >= rectangleTemp.Top && rectangle.Top <= rectangleTemp.Bottom)
                {
                    needDraw = false;
                    break;
                }
            }
            if (needDraw)
            {
                Brush cellLabelBrush = BrushCellLabel;
                if (FDDSetting.IsVisible)
                {
                    bool isFddCell = FDDSetting.IsValidFdd(cell.EARFCN);
                    if (isFddCell)
                    {
                        cellLabelBrush = new SolidBrush(FDDSetting.ColorCellLabel);
                    }
                }

                graphics.FillRectangle(Brushes.White, 3, -size.Height / 2, size.Width, size.Height);
                graphics.DrawString(cellDes, FontCellLabel, cellLabelBrush, 3, -size.Height / 2);
                DrawedCellLabelRectangles.Add(rectangle);
            }
            graphics.ResetTransform();
        }

        public string getCellLabelDes(LTECell cell, int length)
        {
            string des = "";
            if (DrawCellName)
            {
                des += cell.Name + " ";
            }
            if (DrawCellCode)
            {
                des += cell.Code + " ";
            }
            if (DrawCellTAC)
            {
                des += cell.TAC.ToString() + " ";
            }
            if (DrawCellECI)
            {
                des += cell.ECI.ToString() + " ";
            }
            if (DrawCellEARFCN)
            {
                des += cell.EARFCN.ToString() + " ";
            }
            if (DrawCellPCI)
            {
                des += cell.PCI.ToString() + " ";
            }
            if (DrawCellNewPCI && cell.NewPCI.Trim() != "")
            {
                des += "_" + cell.NewPCI + " ";
            }
            if (DrawCellDes)
            {
                des += cell.DESC.ToString() + " ";
            }
            if (des.Length > length)
            {
                des = des.Substring(0, length);
                des += "...";
            }
            return des;
        }
        
        public List<Rectangle> DrawedCustomCellLabelRectangles { get; set; } = new List<Rectangle>();
        public List<Rectangle> DrawedCellLabelRectangles { get; set; } = new List<Rectangle>();
        #endregion 

        #region 天线相关
        /// <summary>
        /// 获取可视区域内的天线（包括服务小区天线，历史天线）
        /// </summary>
        /// <param name="dRect"></param>
        /// <returns></returns>
        private List<LTEAntenna> getAntennasInView(DbRect dRect)
        {
            List<LTECell> cells = null;
            if (DrawCurrent)
            {
                cells = CellManager.GetInstance().GetCurrentLTECells();
            }
            else
            {
                cells = CellManager.GetInstance().GetLTECells(CurShowSnapshotTime);
            }

            List<LTEAntenna> curAntennasInView = new List<LTEAntenna>();
            foreach (LTECell cell in cells)
            {
                foreach (LTEAntenna antenna in cell.Antennas)
                {
                    bool isValid = antenna.Within(dRect.x1, dRect.y1, dRect.x2, dRect.y2)
                        && judgeCoverType(antenna.Type);
                    if (isValid)
                    {
                        curAntennasInView.Add(antenna);
                    }
                }
            }

            if (DrawServer && mainModel.ServerLTECells != null && mainModel.ServerLTECells.Count > 0)
            {
                foreach (LTECell cell in mainModel.ServerLTECells)
                {
                    curAntennasInView.AddRange(cell.Antennas);
                }
            }
            return curAntennasInView;
        }

        private bool judgeCoverType(LTEBTSType type)
        {
            return (type == LTEBTSType.Outdoor && DrawOutdoor) || (type == LTEBTSType.Indoor && DrawIndoor);
        }

#if NBIOTLayer
        private List<LTEAntenna> drawAntennas(Graphics graphics, DbRect dRect, double mapScale)
        {
            List<LTEAntenna> curAntennasInView = getAntennasInView(dRect);
            foreach (LTEAntenna antenna in curAntennasInView)
            {
                //LTE可见&&不绘制NB图层时,无需判断小区频点直接绘制LTE小区
                if (LTEVisible && !SeparateNBIOTInfo)
                {
                    paintAntenna(antenna, graphics, mapScale, false);
                }
                else
                {
                    bool isNBIOTCell = NBIOTSetting.IsValidNBIOT(antenna.Cell.EARFCN);
                    //绘制NB小区,小区频点在设置的NB频段内
                    if (NBIOTVisible && isNBIOTCell)
                    {
                        paintAntenna(antenna, graphics, mapScale, true);
                    }
                    else if (LTEVisible && !isNBIOTCell)
                    {
                        paintAntenna(antenna, graphics, mapScale, false);
                    }
                }
            }
            return curAntennasInView;
        }

        private void paintAntenna(LTEAntenna antenna, Graphics graphics, double scale, bool isNBIOTCell)
        {
            DbPoint dPoint = new DbPoint(antenna.Longitude, antenna.Latitude);
            PointF point;
            gisAdapter.ToDisplay(dPoint, out point);
            Brush brush = brushAntenna;
            Pen pen = penAntenna;
            int index = 0;
            if (isNBIOTCell)
            {
                if (antenna.Cell.Type != LTEBTSType.Indoor)
                {
                    index = 5;
                }
                brush = NBIOTSetting.BrushCell;
                pen = NBIOTSetting.PenEarfcn_NBIOT_Cell;
            }
            else if (antenna.DirectionType == LTEAntennaDirectionType.Beam)
            {
                index = 1;
                if (antenna.Cell != null)
                {
                    index = getIndexByBandType(antenna.Cell.BandType);
                }
            }

            if (DrawServer)
            {
                for (int i = 0; i < mainModel.ServerLTECells.Count; i++)
                {
                    if (antenna.Cell == mainModel.ServerLTECells[i])
                    {
                        brush = brushServerCell;
                        break;
                    }
                }
            }

            bool isneighbour = getNeighbourBrush(antenna, ref brush, ref pen);

            List<GraphicsPath> paths = antennaPaths;
            graphics.TranslateTransform(point.X, point.Y);
            graphics.RotateTransform(antenna.Direction - 90);
            scale = getDisplayScale(scale);
            graphics.ScaleTransform((float)(10000 / scale), (float)(10000 / scale));
            if (index == 0)
            {
                graphics.DrawPath(pen, paths[index]);
            }
            else
            {
                graphics.FillPath(brush, paths[index]);
            }
            if (isneighbour)
            {
                graphics.DrawPath(new Pen(brushLTENeighbourLTECell, 5), paths[index]);
            }

            paintSelectedCell(antenna, graphics, index, paths);
            graphics.ResetTransform();
        }

        private bool getNeighbourBrush(LTEAntenna antenna, ref Brush brush, ref Pen pen)
        {
            bool isneighbour = false;
            if (curMapType == LayerMapType.Google)
            {
                foreach (LTECell cell in mainModel.LTENeighbourLTECells)
                {
                    if (antenna.Cell == cell)
                    {
                        brush = brushLTENeighbourLTECell;
                        pen = new Pen(brush, 2);
                        isneighbour = true;
                        break;
                    }
                }
            }

            return isneighbour;
        }

        private void paintSelectedCell(LTEAntenna antenna, Graphics graphics, int index, List<GraphicsPath> paths)
        {
            if (antenna.Cell == mainModel.SelectedLTECell)
            {
                graphics.DrawPath(penSelected, paths[index]);
            }
            if (curMapType == LayerMapType.MTGis && mainModel.SelectedLTECells != null)
            {
                foreach (LTECell lCell in mainModel.SelectedLTECells)
                {
                    if (antenna.Cell == lCell)
                    {
                        graphics.DrawPath(penSelected, paths[index]);
                    }
                }
            }
        }
#else
        private List<LTEAntenna> drawAntennas(Graphics graphics, DbRect dRect, double mapScale)
        {
            List<LTEAntenna> curAntennasInView = getAntennasInView(dRect);
            foreach (LTEAntenna antenna in curAntennasInView)
            {
                paintAntenna(antenna, graphics, mapScale);
            }
            return curAntennasInView;
        }

        private void paintAntenna(LTEAntenna antenna, Graphics graphics, double scale)
        {
            DbPoint dPoint = new DbPoint(antenna.Longitude, antenna.Latitude);
            PointF point;
            gisAdapter.ToDisplay(dPoint, out point);
            Brush brush = brushAntenna;
            Pen pen = penAntenna;
            int index = 0;
            if (antenna.DirectionType == LTEAntennaDirectionType.Beam)
            {
                index = 1;
                if (antenna.Cell != null)
                {
                    index = getIndexByBandType(antenna.Cell.BandType);
                }
            }

            if (DrawServer)
            {
                for (int i = 0; i < mainModel.ServerLTECells.Count; i++)
                {
                    if (antenna.Cell == mainModel.ServerLTECells[i])
                    {
                        brush = brushServerCell;
                        break;
                    }
                }
            }

            bool isneighbour = false;
            if (curMapType == LayerMapType.Google)
            {
                foreach (LTECell cell in mainModel.LTENeighbourLTECells)
                {
                    if (antenna.Cell == cell)
                    {
                        brush = brushLTENeighbourLTECell;
                        pen = new Pen(brush, 2);
                        isneighbour = true;
                        break;
                    }
                }
            }

            List<GraphicsPath> paths = antennaPaths;
            graphics.TranslateTransform(point.X, point.Y);
            graphics.RotateTransform(antenna.Direction - 90);
            scale = getDisplayScale(scale);
            graphics.ScaleTransform((float)(10000 / scale), (float)(10000 / scale));

            paintAntenna(graphics, brush, pen, index, isneighbour, paths);
            paintSelectedAntenna(antenna, graphics, index, paths);

            graphics.ResetTransform();
        }

        private void paintAntenna(Graphics graphics, Brush brush, Pen pen, int index, bool isneighbour, List<GraphicsPath> paths)
        {
            if (index == 0)
            {
                graphics.DrawPath(pen, paths[index]);
                if (isneighbour)
                {
                    graphics.DrawPath(new Pen(brushLTENeighbourLTECell, 5), paths[index]);
                }
            }
            else
            {
                graphics.FillPath(brush, paths[index]);
                if (isneighbour)
                {
                    graphics.DrawPath(new Pen(brushLTENeighbourLTECell, 5), paths[index]);
                }
            }
        }

        private void paintSelectedAntenna(LTEAntenna antenna, Graphics graphics, int index, List<GraphicsPath> paths)
        {
            if (antenna.Cell == mainModel.SelectedLTECell)
            {
                graphics.DrawPath(penSelected, paths[index]);
            }

            if (curMapType == LayerMapType.MTGis && mainModel.SelectedLTECells != null)
            {
                foreach (LTECell lCell in mainModel.SelectedLTECells)
                {
                    if (antenna.Cell == lCell)
                    {
                        graphics.DrawPath(penSelected, paths[index]);
                    }
                }
            }
        }
#endif
        private void drawAntennasLabel(Graphics graphics, DbRect dRect, bool isValidScale)
        {
            if (DrawAntennaLabel && isValidScale)
            {
                if (DrawAntenna)
                {
                    drawAntennasLabel(antennasInView, graphics);
                }
                else
                {
                    drawAntennasLabel(getAntennasInView(dRect), graphics);
                }
            }
        }

        private void drawAntennasLabel(List<LTEAntenna> antennas, Graphics graphics)
        {
            if (antennas == null)
            {
                return;
            }
            DrawedAntennaLabelRectangles = new List<Rectangle>();
            foreach (LTEAntenna antenna in antennas)
            {
#if NBIOTLayer
                //LTE可见&&不绘制NB图层时,无需判断小区频点直接绘制LTE小区
                if (LTEVisible && !SeparateNBIOTInfo)
                {
                    paintAntennaLabel(antenna, graphics);
                }
                else
                {
                    bool isNBIOTCell = NBIOTSetting.IsValidNBIOT(antenna.Cell.EARFCN);
                    //绘制NB小区,小区频点在设置的NB频段内
                    if (NBIOTVisible && isNBIOTCell)
                    {
                        paintAntennaLabel(antenna, graphics);
                    }
                    else if (LTEVisible && !isNBIOTCell)
                    {
                        paintAntennaLabel(antenna, graphics);
                    }
                }
#else
                paintAntennaLabel(antenna, graphics);
#endif
            }
        }

        private void paintAntennaLabel(LTEAntenna antenna, Graphics graphics)
        {
            DbPoint dPoint = new DbPoint(antenna.EndPointLongitude, antenna.EndPointLatitude);
            PointF point;
            gisAdapter.ToDisplay(dPoint, out point);
            graphics.TranslateTransform(point.X, point.Y);
            string antennaDes = getAntennaLabelDes(antenna, 100);
            SizeF size = graphics.MeasureString(antennaDes, FontAntennaLabel);
            size.Height *= 0.8f;
            System.Drawing.Rectangle rectangle = new System.Drawing.Rectangle((int)point.X - 3, (int)(point.Y - (size.Height / 2 - 5)), (int)size.Width + 10, (int)size.Height + 10);
            bool needDraw = true;
            foreach (System.Drawing.Rectangle rectangleTemp in DrawedAntennaLabelRectangles)
            {
                if (rectangle.Right >= rectangleTemp.Left && rectangle.Left <= rectangleTemp.Right
                    && rectangle.Bottom >= rectangleTemp.Top && rectangle.Top <= rectangleTemp.Bottom)
                {
                    needDraw = false;
                    break;
                }
            }
            if (needDraw)
            {
                graphics.FillRectangle(Brushes.White, 3, -size.Height / 2, size.Width, size.Height);
                graphics.DrawString(antennaDes, FontAntennaLabel, BrushAntennaLabel, 3, -size.Height / 2);
                DrawedAntennaLabelRectangles.Add(rectangle);
            }
            graphics.ResetTransform();
        }

        public string getAntennaLabelDes(LTEAntenna antenna, int length)
        {
            string des = "";
            if (DrawAntennaLongitude)
            {
                des += antenna.Longitude.ToString() + " ";
            }
            if (DrawAntennaLatitude)
            {
                des += antenna.Latitude.ToString() + " ";
            }
            if (DrawAntennaDirectionType)
            {
                des += antenna.DirectionType.ToString() + " ";
            }
            if (DrawAntennaDirection)
            {
                des += antenna.Direction.ToString() + " ";
            }
            if (DrawAntennaDownward)
            {
                des += antenna.Downward.ToString() + " ";
            }
            if (DrawAntennaAltitude)
            {
                des += antenna.Altitude.ToString() + " ";
            }
            if (DrawAntennaDescription)
            {
                des += antenna.Description + " ";
            }
            if (des.Length > length)
            {
                des = des.Substring(0, length);
                des += "...";
            }
            return des;
        }
        
        public List<Rectangle> DrawedAntennaLabelRectangles { get; set; } = new List<Rectangle>();
        #endregion

        #region BTS 相关
        private List<LTEBTS> getBTSsInView(DbRect dRect)
        {
            List<LTEBTS> curBtssInView = new List<LTEBTS>();
            List<LTEBTS> btss = null;
            if (DrawCurrent)
            {
                btss = mainModel.CellManager.GetCurrentLTEBTSs();
            }
            else
            {
                btss = mainModel.CellManager.GetLTEBTSs(CurShowSnapshotTime);
            }
            if (btss != null)
            {
                foreach (LTEBTS bts in btss)
                {
                    if (bts.Within(dRect.x1, dRect.y1, dRect.x2, dRect.y2))
                    {
                        curBtssInView.Add(bts);
                    }
                }
            }
            return curBtssInView;
        }

#if NBIOTLayer
        /// <summary>
        /// 绘画可视区域内的BTS
        /// </summary>
        /// <param name="graphics"></param>
        /// <param name="dRect"></param>
        /// <param name="mapScale"></param>
        private List<LTEBTS> drawBTSs(Graphics graphics, DbRect dRect)
        {
            List<LTEBTS> curBtssInView = getBTSsInView(dRect);
            foreach (LTEBTS bts in curBtssInView)
            {
                //LTE可见&&不绘制NB图层时,无需判断小区频点直接绘制LTE小区
                if (LTEVisible && !SeparateNBIOTInfo)
                {
                    paintBTS(bts, graphics, false);
                }
                else
                {
                    bool isNBIOTCell = NBIOTSetting.IsValidNBIOT(bts.Cells[0].EARFCN);
                    //绘制NB小区,小区频点在设置的NB频段内
                    if (NBIOTVisible && isNBIOTCell)
                    {
                        paintBTS(bts, graphics, true);
                    }
                    else if (LTEVisible && !isNBIOTCell)
                    {
                        paintBTS(bts, graphics, false);
                    }
                }
            }
            return curBtssInView;
        }

        /// <summary>
        /// 画单个BTS
        /// </summary>
        /// <param name="bts"></param>
        /// <param name="graphics"></param>
        /// <param name="scale"></param>
        private void paintBTS(LTEBTS bts, Graphics graphics, bool isNBIOTCell)
        {
            DbPoint dPoint = new DbPoint(bts.Longitude, bts.Latitude);
            PointF point;
            gisAdapter.ToDisplay(dPoint, out point);
            graphics.TranslateTransform(point.X, point.Y);
           
            if (bts == mainModel.SelectedLTEBTS && curMapType == LayerMapType.MTGis)
            {
                graphics.DrawRectangle(penSelected, -SizeBTS / 2, -SizeBTS / 2, SizeBTS, SizeBTS);
            }
            else
            {
                if (isNBIOTCell)
                {
                    graphics.DrawRectangle(NBIOTSetting.PenEarfcn_NBIOT_Cell, -SizeBTS / 2, -SizeBTS / 2, SizeBTS, SizeBTS);
                }
                else
                {
                    graphics.DrawRectangle(penBTS, -SizeBTS / 2, -SizeBTS / 2, SizeBTS, SizeBTS);
                }
            }
            graphics.ResetTransform();
        }      
#else
        /// <summary>
        /// 绘画可视区域内的BTS
        /// </summary>
        /// <param name="graphics"></param>
        /// <param name="dRect"></param>
        /// <param name="mapScale"></param>
        private List<LTEBTS> drawBTSs(Graphics graphics, DbRect dRect)
        {
            List<LTEBTS> curBtssInView = getBTSsInView(dRect);
            foreach (LTEBTS bts in curBtssInView)
            {
                paintBTS(bts, graphics);
            }
            return curBtssInView;
        }

        /// <summary>
        /// 画单个BTS
        /// </summary>
        /// <param name="bts"></param>
        /// <param name="graphics"></param>
        /// <param name="scale"></param>
        private void paintBTS(LTEBTS bts, Graphics graphics)
        {
            DbPoint dPoint = new DbPoint(bts.Longitude, bts.Latitude);
            PointF point;
            gisAdapter.ToDisplay(dPoint, out point);
            graphics.TranslateTransform(point.X, point.Y);
            if (bts==mainModel.SelectedLTEBTS && curMapType == LayerMapType.MTGis)
            {
                graphics.DrawRectangle(penSelected, -SizeBTS / 2, -SizeBTS / 2, SizeBTS, SizeBTS);
            }
            else
            {
                graphics.DrawRectangle(penBTS, -SizeBTS / 2, -SizeBTS / 2, SizeBTS, SizeBTS);
            }
            graphics.ResetTransform();
        }
#endif
        private void drawBTSLabels(Graphics graphics, DbRect dRect, bool isValidScale)
        {
            if (DrawBTSLabel && isValidScale)
            {//画BTS Label
                if (DrawBTS)
                {
                    drawBTSLabels(btssInView, graphics);
                }
                else
                {
                    drawBTSLabels(getBTSsInView(dRect), graphics);
                }
            }
        }

        /// <summary>
        ///  绘画BTS Label入口，btsList为可见区域内的BTS集合
        /// </summary>
        /// <param name="btsList"></param>
        /// <param name="graphics"></param>
        /// <param name="dRect"></param>
        /// <param name="mapScale"></param>
        private void drawBTSLabels(List<LTEBTS> btsList, Graphics graphics)
        {
            DrawedBTSLabelRectangles = new List<Rectangle>();
            if (btsList == null)
            {
                return;
            }
            foreach (LTEBTS bts in btsList)
            {
#if NBIOTLayer
                if (LTEVisible && !SeparateNBIOTInfo)
                {
                    paintBTSLabel(bts, graphics);
                }
                else
                {
                    //判断站的小区类型,根据不同类型决定是否绘制标签
                    int cellType = NBIOTSetting.GetBTSCellType(bts);
                    if (NBIOTVisible && cellType == 1)
                    {
                        paintBTSLabel(bts, graphics);
                    }
                    else if (LTEVisible && cellType == 2)
                    {
                        paintBTSLabel(bts, graphics);
                    }
                    else if ((NBIOTVisible || LTEVisible) && cellType == 3)
                    {
                        paintBTSLabel(bts, graphics);
                    }
                }
#else
                paintBTSLabel(bts, graphics);
#endif
            }
        }

        /// <summary>
        /// 画单个BTS Label
        /// </summary>
        /// <param name="bts"></param>
        /// <param name="graphics"></param>
        /// <param name="scale"></param>
        private void paintBTSLabel(LTEBTS bts, Graphics graphics)
        {
            DbPoint dPoint = new DbPoint(bts.Longitude, bts.Latitude);
            PointF point;
            gisAdapter.ToDisplay(dPoint, out point);
            graphics.TranslateTransform(point.X, point.Y);
            string btsDes = getBTSLabelDes(bts, 100);
            SizeF size = graphics.MeasureString(btsDes, FontBTSLabel);
            size.Height *= 0.8f;
            System.Drawing.Rectangle rectangle = new System.Drawing.Rectangle((int)point.X - 3, (int)(point.Y - (size.Height / 2 - 5)), (int)size.Width + 10, (int)size.Height + 10);
            bool needDraw = true;
            foreach (System.Drawing.Rectangle rectangleTemp in DrawedBTSLabelRectangles)
            {
                if (rectangle.Right >= rectangleTemp.Left && rectangle.Left <= rectangleTemp.Right
                    && rectangle.Bottom >= rectangleTemp.Top && rectangle.Top <= rectangleTemp.Bottom)
                {
                    needDraw = false;
                    break;
                }
            }
            if (needDraw)
            {
                graphics.FillRectangle(Brushes.White, 3, -size.Height / 2, size.Width, size.Height);
                graphics.DrawString(btsDes, FontBTSLabel, BrushBTSLabel, 3, -size.Height / 2);
                DrawedBTSLabelRectangles.Add(rectangle);
            }
            graphics.ResetTransform();
        }

        public string getBTSLabelDes(LTEBTS bts, int length)
        {
            string des = "";
            if (DrawBTSName)
            {
                des += bts.Name + " ";
            }
            if (DrawBTSLongitude)
            {
                des += bts.Longitude.ToString() + " ";
            }
            if (DrawBTSLatitude)
            {
                des += bts.Latitude.ToString() + " ";
            }
            if (DrawBTSType)
            {
                des += bts.TypeStringDesc + " ";
            }
            if (DrawBTSDescription)
            {
                des += bts.Description + " ";
            }
            if (des.Length > length)
            {
                des = des.Substring(0, length);
                des += "...";
            }
            return des;
        }
        
        public List<Rectangle> DrawedBTSLabelRectangles { get; set; } = new List<Rectangle>();
        #endregion

        public override void mapForm_MapFeatureSelecting(object sender, EventArgs e)
        {
            MapForm mf = sender as MapForm;
            Select(((MapForm.MapEventArgs)e).MapOp2, mf.LTEBTSs, mf.LTECells, mf.LTEAntennas);
        }

        public void Select(MapOperation2 mop2, List<LTEBTS> selectedBTSs, List<LTECell> selectedCells, List<LTEAntenna> selectedAntennas)
        {
            select(mop2, selectedBTSs, selectedCells, selectedAntennas, DrawBTS, DrawAntenna, DrawCell);
        }

        private void select(MapOperation2 mop2, List<LTEBTS> selectedBTSs, List<LTECell> selectedCells, List<LTEAntenna> selectedAntennas, bool DrawBTS, bool DrawAntenna, bool DrawCell)
        {
            if (!IsVisible)
            {
                return;
            }
            DbRect dRect = mop2.GetRegion().Bounds;
            if (DrawBTS && btssInView != null)
            {
                addselectedBTS(mop2, selectedBTSs, dRect);
            }
            if (DrawAntenna && antennasInView != null)
            {
                addselectedAntenna(mop2, selectedAntennas, dRect);
            }
            if (DrawCell && cellsInView != null)
            {
                addSelectedCell(mop2, selectedCells, selectedAntennas, DrawAntenna, dRect);
            }
        }

        private void addselectedBTS(MapOperation2 mop2, List<LTEBTS> selectedBTSs, DbRect dRect)
        {
            foreach (LTEBTS bts in btssInView)
            {
                if (bts.Within(dRect.x1 - 0.01, dRect.y1 - 0.01, dRect.x2 + 0.01, dRect.y2 + 0.01)
                    && judgeCoverType(bts.Type))
                {
                    LTEBTS selectedBTS = selectBTS(bts, mop2);
                    if (selectedBTS != null)
                    {
                        selectedBTSs.Add(selectedBTS);
                    }
                }
            }
        }

        private void addselectedAntenna(MapOperation2 mop2, List<LTEAntenna> selectedAntennas, DbRect dRect)
        {
            foreach (LTEAntenna antenna in antennasInView)
            {
                if (antenna.Within(dRect.x1 - 0.01, dRect.y1 - 0.01, dRect.x2 + 0.01, dRect.y2 + 0.01))
                {
                    LTEAntenna selectedAntenna = selectAntenna(antenna, mop2, mapScale);
                    if (selectedAntenna != null && !selectedAntennas.Contains(selectedAntenna))
                    {
                        selectedAntennas.Add(selectedAntenna);
                    }
                }
            }
        }

        private void addSelectedCell(MapOperation2 mop2, List<LTECell> selectedCells, List<LTEAntenna> selectedAntennas, bool DrawAntenna, DbRect dRect)
        {
            foreach (LTECell cell in cellsInView)
            {
                if (cell.Antennas.Count > 0
                    && cell.Within(dRect.x1 - 0.01, dRect.y1 - 0.01, dRect.x2 + 0.01, dRect.y2 + 0.01))
                {
                    addSelectedCellInfo(mop2, selectedCells, selectedAntennas, DrawAntenna, cell);
                }
            }
        }

        private void addSelectedCellInfo(MapOperation2 mop2, List<LTECell> selectedCells, List<LTEAntenna> selectedAntennas, bool DrawAntenna, LTECell cell)
        {
            LTECell selectedCell = selectCell(cell, mop2, mapScale);
            if (selectedCell != null && !selectedCells.Contains(selectedCell))
            {
                selectedCells.Add(selectedCell);
                if (DrawAntenna)
                {
                    foreach (LTEAntenna ant in cell.Antennas)
                    {
                        if (!selectedAntennas.Contains(ant))
                        {
                            selectedAntennas.Add(ant);
                        }
                    }
                }
            }
        }

        public LTECell SelectCell(MapOperation2 mop2)
        {
            if (!IsVisible)
            {
                return null;
            }
            DbRect dRect = mop2.GetRegion().Bounds;
            if (DrawCell && cellsInView != null)
            {
                LTECell selectedCell = getSelectedCell(mop2, dRect);
                if (selectedCell != null)
                {
                    return selectedCell;
                }
            }

            if (DrawAntenna && antennasInView != null)
            {
                LTEAntenna selectedAntenna = getSelectedAntenna(mop2, dRect);
                if (selectedAntenna != null)
                {
                    return selectedAntenna.Cell;
                }
            }
            return null;
        }

        private LTECell getSelectedCell(MapOperation2 mop2, DbRect dRect)
        {
            foreach (LTECell cell in cellsInView)
            {
                if (cell.Antennas.Count == 0)
                {
                    continue;
                }
                if (cell.Within(dRect.x1 - 0.01, dRect.y1 - 0.01, dRect.x2 + 0.01, dRect.y2 + 0.01))
                {
                    LTECell selectedCell = selectCell(cell, mop2, mapScale);
                    if (selectedCell != null)
                    {
                        return selectedCell;
                    }
                }
            }
            return null;
        }

        private LTEAntenna getSelectedAntenna(MapOperation2 mop2, DbRect dRect)
        {
            foreach (LTEAntenna ant in antennasInView)
            {
                if (ant.Within(dRect.x1 - 0.01, dRect.y1 - 0.01, dRect.x2 + 0.01, dRect.y2 + 0.01))
                {
                    LTEAntenna selectedAntenna = selectAntenna(ant, mop2, mapScale);
                    if (selectedAntenna != null)
                    {
                        return selectedAntenna;
                    }
                }
            }
            return null;
        }

        private LTECell selectCell(LTECell cell, MapOperation2 mop2, double scale)
        {
            if (cell.Antennas.Count == 0)
            {
                return null;
            }
            DbPoint dPoint = new DbPoint(cell.Longitude, cell.Latitude);
            PointF point;
            gisAdapter.ToDisplay(dPoint, out point);
            scale = getDisplayScale(scale);
            float ratio = (float)(10000 / scale);
            int index = 1;
            bool circle = false;
           
#if NBIOTLayer
            bool isNBIOTCell = NBIOTSetting.IsValidNBIOT(cell.EARFCN);
            if (isNBIOTCell)
            {
                index = 5;
            }
            else
            {
                index = getIndexByBandType(cell.BandType);
            }
#else
            index = getIndexByBandType(cell.BandType);
#endif

            if (cell.DirectionType == LTEAntennaDirectionType.Omni || cell.Type == LTEBTSType.Indoor)
            {
                index = 0;
                circle = true;
            }

            if (circle)
            {
                RectangleF rect = new RectangleF(point.X - (CellOmniDefaultDisplayLength * shapeLengthScale * ratio)
                    , point.Y - (CellOmniDefaultDisplayLength * shapeLengthScale * ratio)
                    , (CellOmniDefaultDisplayLength * shapeLengthScale * ratio) * 2, (CellOmniDefaultDisplayLength * shapeLengthScale * ratio) * 2);
                DbRect dRect;
                gisAdapter.FromDisplay(rect, out dRect);
                if (mop2.CheckCenterInDRect(dRect))
                {
                    return cell;
                }
            }
            else
            {
                Matrix translateMatrix = new Matrix();
                translateMatrix.Translate(point.X, point.Y);
                translateMatrix.Rotate(cell.Direction - 90);
                translateMatrix.Scale(ratio, ratio);
                GraphicsPath cellPath = cellPaths[index].Clone() as GraphicsPath;
                cellPath.Transform(translateMatrix);
                PointF selPnt;
                gisAdapter.ToDisplay(mop2.GetRegion().Bounds.Center(), out selPnt);
                if (cellPath.IsVisible(selPnt))
                {
                    return cell;
                }
            }
            return null;
        }

        private LTEBTS selectBTS(LTEBTS bts, MapOperation2 mop2)
        {
            DbPoint dPoint = new DbPoint(bts.Longitude, bts.Latitude);
            PointF point;
            gisAdapter.ToDisplay(dPoint, out point);
            RectangleF rect;

            rect = new RectangleF(point.X - SizeBTS / 2, point.Y - SizeBTS / 2, SizeBTS, SizeBTS);

            DbRect dRect;
            gisAdapter.FromDisplay(rect, out dRect);
            if (mop2.CheckCenterInDRect(dRect))
            {
                return bts;
            }
            return null;
        }

        private LTEAntenna selectAntenna(LTEAntenna antenna, MapOperation2 mop2, double scale)
        {
            DbPoint dPoint = new DbPoint(antenna.Longitude, antenna.Latitude);
            PointF point;
            gisAdapter.ToDisplay(dPoint, out point);
            scale = getDisplayScale(scale);
            float ratio = (float)(10000 / scale);
            int index = 1;
            bool circle = false;

            if (antenna.Cell != null)
            {
                
#if NBIOTLayer
                bool isNBIOTCell = NBIOTSetting.IsValidNBIOT(antenna.Cell.EARFCN);
                if (isNBIOTCell)
                {
                    index = 5;
                }
                else
                {
                    index = getIndexByBandType(antenna.Cell.BandType);
                }
#else
                index = getIndexByBandType(antenna.Cell.BandType);
#endif
            }

            if (antenna.DirectionType == LTEAntennaDirectionType.Omni)
            {
                index = 0;
                circle = true;
            }

            if (circle)
            {
                RectangleF rect = new RectangleF(point.X - (CellOmniDefaultDisplayLength * shapeLengthScale * ratio)
                  , point.Y - (CellOmniDefaultDisplayLength * shapeLengthScale * ratio)
                  , (CellOmniDefaultDisplayLength * shapeLengthScale * ratio) * 2, (CellOmniDefaultDisplayLength * shapeLengthScale * ratio) * 2);
                DbRect dRect;
                gisAdapter.FromDisplay(rect, out dRect);
                if (mop2.CheckCenterInDRect(dRect)) //Ellipse
                {
                    return antenna;
                }
            }
            else
            {
                Matrix translateMatrix = new Matrix();
                translateMatrix.Translate(point.X, point.Y);
                translateMatrix.Rotate(antenna.Direction - 90);
                translateMatrix.Scale(ratio, ratio);
                GraphicsPath path = antennaPaths[index].Clone() as GraphicsPath;
                path.Transform(translateMatrix);
                PointF selPnt;
                gisAdapter.ToDisplay(mop2.GetRegion().Bounds.Center(), out selPnt);
                if (path.IsVisible(selPnt))
                {
                    return antenna;
                }
            }
            return null;
        }

        private int getIndexByBandType(LTEBandType bandType)
        {
            int index = 1;
            switch (bandType)
            {
                case LTEBandType.Undefined:
                case LTEBandType.A:
                    index = 1;
                    break;
                case LTEBandType.D:
                case LTEBandType.D_37900:
                case LTEBandType.D_38100:
                case LTEBandType.D_38098:
                    index = 2;
                    break;
                case LTEBandType.E:
                    index = 3;
                    break;
                case LTEBandType.F:
                case LTEBandType.F_38350:
                case LTEBandType.F_38400:
                case LTEBandType.F_38544:
                    index = 4;
                    break;
                default:
                    break;
            }
            return index;
        }

        internal int MakeShpFile(string filename)
        {
            Shapefile shpFile = new Shapefile();
            try
            {
                int idIdx = 0;
                int fLongId = idIdx++;
                int fLatId = idIdx++;
                int fNameId = idIdx++;
                int fCellIDId = idIdx++;
                int fTacId = idIdx++;
                int fEciId = idIdx++;
                int fEarfcnId = idIdx++;
                int fPciId = idIdx++;
                int fDirectionId = idIdx++;
                int fDownwordId = idIdx++;
                int fTypeId = idIdx;

                bool result = shpFile.CreateNew("", MapWinGIS.ShpfileType.SHP_POLYGON);
                if (!result)
                {
                    DevExpress.XtraEditors.XtraMessageBox.Show(shpFile.get_ErrorMsg(shpFile.LastErrorCode));
                    return -1;
                }
                ShapeHelper.InsertNewField(shpFile, "Longitude", FieldType.DOUBLE_FIELD, 7, 0, ref fLongId);
                ShapeHelper.InsertNewField(shpFile, "Latitude", FieldType.DOUBLE_FIELD, 7, 0, ref fLatId);
                ShapeHelper.InsertNewField(shpFile, "Name", FieldType.STRING_FIELD, 7, 30, ref fNameId);
                ShapeHelper.InsertNewField(shpFile, "CellID", FieldType.STRING_FIELD, 7, 30, ref fCellIDId);
                ShapeHelper.InsertNewField(shpFile, "TAC", FieldType.INTEGER_FIELD, 7, 0, ref fTacId);
                ShapeHelper.InsertNewField(shpFile, "ECI", FieldType.INTEGER_FIELD, 7, 0, ref fEciId);
                ShapeHelper.InsertNewField(shpFile, "EARFCN", FieldType.INTEGER_FIELD, 7, 0, ref fEarfcnId);
                ShapeHelper.InsertNewField(shpFile, "PCI", FieldType.INTEGER_FIELD, 7, 0, ref fPciId);
                ShapeHelper.InsertNewField(shpFile, "方向角", FieldType.INTEGER_FIELD, 7, 0, ref fDirectionId);
                ShapeHelper.InsertNewField(shpFile, "下倾角", FieldType.INTEGER_FIELD, 7, 0, ref fDownwordId);
                ShapeHelper.InsertNewField(shpFile, "室内室外", FieldType.STRING_FIELD, 7, 30, ref fTypeId);
                List<LTECell> cells = null;
                if (DrawCurrent)
                {
                    cells = mainModel.CellManager.GetCurrentLTECells();
                }
                else
                {
                    cells = mainModel.CellManager.GetLTECells(CurShowSnapshotTime);
                }
                double radius = 0.00048775;//大约50米
                foreach (LTECell cell in cells)
                {
                    MapWinGIS.Shape shp = null;
                    if (cell.DirectionType == LTEAntennaDirectionType.Omni || cell.Type== LTEBTSType.Indoor)
                    {
                        shp = ShapeHelper.CreateCircleShape(cell.Longitude, cell.Latitude, radius);
                    }
                    else if (cell.DirectionType == LTEAntennaDirectionType.Beam)
                    {
                        shp = ShapeHelper.CreateOutdoorCellShape(cell.Longitude, cell.Latitude, cell.EndPointLongitude, cell.EndPointLatitude,cell.Direction);
                    }
                    int shpIdx = 0;
                    shpFile.EditInsertShape(shp, ref shpIdx);
                    shpFile.EditCellValue(fLongId, shpIdx, cell.Longitude);
                    shpFile.EditCellValue(fLatId, shpIdx, cell.Latitude);
                    shpFile.EditCellValue(fNameId, shpIdx, cell.Name);
                    shpFile.EditCellValue(fCellIDId, shpIdx, cell.SCellID);
                    shpFile.EditCellValue(fTacId, shpIdx, cell.TAC);
                    shpFile.EditCellValue(fEciId, shpIdx, cell.ECI);
                    shpFile.EditCellValue(fEarfcnId, shpIdx, cell.EARFCN);
                    shpFile.EditCellValue(fPciId, shpIdx, cell.PCI);
                    shpFile.EditCellValue(fDirectionId, shpIdx, cell.Direction);
                    shpFile.EditCellValue(fDownwordId, shpIdx, cell.Downward);
                    shpFile.EditCellValue(fTypeId, shpIdx, cell.Type.ToString());
                }
                ShapeHelper.DeleteShpFile(filename);
                shpFile.SaveAs(filename, null);
                return 1;
            }
            catch
            {
                return -1;
            }
            finally
            {
                shpFile.Close();
            }
        }

        private bool judgeDrawSimulationLine()
        {
            return curMapType == LayerMapType.MTGis && mainModel.SimulationPoints != null
                            && (mainModel.SimulationPoints.strNet == "LTE"
                                && (mainModel.SimulationPoints.longLatTestList.Count > 0
                                    || mainModel.SimulationPoints.longLatModelList.Count > 0)
                                || mainModel.SimulationPoints.longLatMRList.Count > 0);
        }

        /// <summary>
        /// 绘制天线仿真曲线
        /// </summary>
        private void drawSimulationLine(Graphics graphics)
        {
            #region 测试曲线
            List<PointF> ptfList = new List<PointF>();
            foreach (MasterCom.RAMS.ZTFunc.LongLat longlat in mainModel.SimulationPoints.longLatTestList)
            {
                DbPoint dPt = new DbPoint(longlat.fLongitude, longlat.fLatitude);
                PointF ptf;
                gisAdapter.ToDisplay(dPt, out ptf);
                ptfList.Add(ptf);
            }

            for (int i = 0; i < ptfList.Count; i++)
            {
                if (i + 1 >= ptfList.Count)
                    graphics.DrawLine(new Pen(new SolidBrush(Color.Blue), 2), ptfList[i], ptfList[0]);
                else
                    graphics.DrawLine(new Pen(new SolidBrush(Color.Blue), 2), ptfList[i], ptfList[i + 1]);
            }
            #endregion

            #region 仿真模型
            List<PointF> modelList = new List<PointF>();
            foreach (MasterCom.RAMS.ZTFunc.LongLat longlat in mainModel.SimulationPoints.longLatModelList)
            {
                DbPoint dPt = new DbPoint(longlat.fLongitude, longlat.fLatitude);
                PointF ptf;
                gisAdapter.ToDisplay(dPt, out ptf);
                modelList.Add(ptf);
            }

            for (int i = 0; i < modelList.Count; i++)
            {
                if (i + 1 >= modelList.Count)
                    graphics.DrawLine(new Pen(new SolidBrush(Color.Red), 2), modelList[i], modelList[0]);
                else
                    graphics.DrawLine(new Pen(new SolidBrush(Color.Red), 2), modelList[i], modelList[i + 1]);
            }
            #endregion

            #region MR覆盖仿真
            List<PointF> mrList = new List<PointF>();
            foreach (MasterCom.RAMS.ZTFunc.LongLat longlat in mainModel.SimulationPoints.longLatMRList)
            {
                DbPoint dPt = new DbPoint(longlat.fLongitude, longlat.fLatitude);
                PointF ptf;
                gisAdapter.ToDisplay(dPt, out ptf);
                mrList.Add(ptf);
            }

            for (int i = 0; i < mrList.Count; i++)
            {
                if (i + 1 >= mrList.Count)
                    graphics.DrawLine(new Pen(new SolidBrush(Color.DarkViolet), 2), mrList[i], mrList[0]);
                else
                    graphics.DrawLine(new Pen(new SolidBrush(Color.DarkViolet), 2), mrList[i], mrList[i + 1]);
            }
            #endregion
        }

        #region layer properties
        public bool DrawServer { get; set; } = true;
        public bool DrawOutdoor { get; set; } = true;
        public bool DrawIndoor { get; set; } = true;
        public bool DrawBTS { get; set; } = false;
        public int SizeBTS { get; set; } = 6;
        public bool DrawBTSLabel { get; set; } = true;
        public Font FontBTSLabel { get; set; } = new Font(new FontFamily("宋体"), 9, FontStyle.Bold);
        public Brush BrushBTSLabel
        {
            get { return new SolidBrush(ColorBTSLabel); }
        }
        public Color ColorBTSLabel { get; set; } = Color.Black;
        public bool DrawCell { get; set; } = true;

        #region FDD设置
        public FddSetting FDDSetting
        {
            get { return mainModel.FDDSetting; }
            set { mainModel.FDDSetting = value; }
        }
        #endregion

        #region NBIOT设置
        public NbIotSetting NBIOTSetting
        {
            get { return mainModel.NBIOTSetting; }
            set { mainModel.NBIOTSetting = value; }
        } 
        
        /// <summary>
        /// 界面点选NBIOT可见性
        /// </summary>
        public bool NBIOTVisible { get; set; } = true;
        public bool LTEVisible { get; set; } = true;

        /// <summary>
        /// 图层设置是否分离NBIOT信息
        /// </summary>
        public bool SeparateNBIOTInfo
        {
            get { return NBIOTSetting.IsSeparated; }
        }
        #endregion

        /// <summary>
        /// 对内为画刷，对外可见为颜色。
        /// </summary>
        private SolidBrush brushCell = new SolidBrush(Color.DodgerBlue);
        public SolidBrush BrushCell
        {
            get { return brushCell; }
        }

        private SolidBrush brushCell_Mod3_0 = new SolidBrush(Color.White);
        public SolidBrush BrushCell_Mod3_0
        {
            get { return brushCell_Mod3_0; }
        }

        private SolidBrush brushCell_Mod3_1 = new SolidBrush(Color.Green);
        public SolidBrush BrushCell_Mod3_1
        {
            get { return brushCell_Mod3_1; }
        }

        private SolidBrush brushCell_Mod3_2 = new SolidBrush(Color.Gray);
        public SolidBrush BrushCell_Mod3_2
        {
            get { return brushCell_Mod3_2; }
        }

        private SolidBrush brushCell_Mod6_0 = new SolidBrush(Color.Gray);
        public SolidBrush BrushCell_Mod6_0
        {
            get { return brushCell_Mod6_0; }
        }

        private SolidBrush brushCell_Mod6_1 = new SolidBrush(Color.BlueViolet);
        public SolidBrush BrushCell_Mod6_1
        {
            get { return brushCell_Mod6_1; }
        }

        private SolidBrush brushCell_Mod6_2 = new SolidBrush(Color.HotPink);
        public SolidBrush BrushCell_Mod6_2
        {
            get { return brushCell_Mod6_2; }
        }

        private SolidBrush brushCell_Mod6_3 = new SolidBrush(Color.Maroon);
        public SolidBrush BrushCell_Mod6_3
        {
            get { return brushCell_Mod6_3; }
        }

        private SolidBrush brushCell_Mod6_4 = new SolidBrush(Color.OldLace);
        public SolidBrush BrushCell_Mod6_4
        {
            get { return brushCell_Mod6_4; }
        }

        private SolidBrush brushCell_Mod6_5 = new SolidBrush(Color.GreenYellow);
        public SolidBrush BrushCell_Mod6_5
        {
            get { return brushCell_Mod6_5; }
        }

        public Color ColorCellMod3_0
        {
            get { return brushCell_Mod3_0.Color; }
            set
            {
                brushCell_Mod3_0 = new SolidBrush(value);
            }
        }

        public Color ColorCellMod3_1
        {
            get { return brushCell_Mod3_1.Color; }
            set
            {
                brushCell_Mod3_1 = new SolidBrush(value);
            }
        }

        public Color ColorCellMod3_2
        {
            get { return brushCell_Mod3_2.Color; }
            set
            {
                brushCell_Mod3_2 = new SolidBrush(value);
            }
        }

        public Color ColorCellMod6_0
        {
            get { return brushCell_Mod6_0.Color; }
            set
            {
                brushCell_Mod6_0 = new SolidBrush(value);
            }
        }

        public Color ColorCellMod6_1
        {
            get { return brushCell_Mod6_1.Color; }
            set
            {
                brushCell_Mod6_1 = new SolidBrush(value);
            }
        }

        public Color ColorCellMod6_2
        {
            get { return brushCell_Mod6_2.Color; }
            set
            {
                brushCell_Mod6_2 = new SolidBrush(value);
            }
        }

        public Color ColorCellMod6_3
        {
            get { return brushCell_Mod6_3.Color; }
            set
            {
                brushCell_Mod6_3 = new SolidBrush(value);
            }
        }

        public Color ColorCellMod6_4
        {
            get { return brushCell_Mod6_4.Color; }
            set
            {
                brushCell_Mod6_4 = new SolidBrush(value);
            }
        }

        public Color ColorCellMod6_5
        {
            get { return brushCell_Mod6_5.Color; }
            set
            {
                brushCell_Mod6_5 = new SolidBrush(value);
            }
        }

        private SolidBrush selecctedBrushMainCell = new SolidBrush(Color.Red);
        private SolidBrush selecctedBrushNBCell = new SolidBrush(Color.Pink);
        public SolidBrush SelecctedBrushMainCell
        {
            get { return selecctedBrushMainCell; }
        }

        private Pen selecctedPenMainCell = new Pen(Color.Yellow,4);
        public Pen SelecctedPenMainCell
        {
            get { return selecctedPenMainCell;}

        }

        public SolidBrush SelectedBrushNBCell
        {
            get { return selecctedBrushNBCell; }
        }

        public Color ColorCell
        {
            get { return brushCell.Color; }
            set
            {
                brushCell = new SolidBrush(value);
            }
        }

        public bool DrawAntenna { get; set; } = false;
        
        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["featrueMaxZoomScale"] = FeatrueMaxZoomScale;
                param["drawCurrent"] = DrawCurrent;
                param["CurShowSnapshotTime"] = CurShowSnapshotTime.Ticks;
                param["drawServer"] = DrawServer;
                param["drawIndoor"] = DrawIndoor;
                param["drawOutdoor"] = DrawOutdoor;
                param["colorSelected"] = penSelected.Color.ToArgb();

                //BTS
                param["drawBTS"] = DrawBTS;
                param["colorBTS"] = penBTS.Color.ToArgb();
                param["sizeBTS"] = SizeBTS;
                param["drawBTSLabel"] = DrawBTSLabel;
                param["fontBTSLabelFontFamilyName"] = FontBTSLabel.FontFamily.Name;
                param["fontBTSLabelFontSize"] = FontBTSLabel.Size;
                param["fontBTSLabelFontStyle"] = (int)FontBTSLabel.Style;
                param["colorBTSLabel"] = ColorBTSLabel.ToArgb();
                param["drawBTSName"] = DrawBTSName;
                param["drawBTSLongitude"] = DrawBTSLongitude;
                param["drawBTSLatitude"] = DrawBTSLatitude;
                param["drawBTSType"] = DrawBTSType;
                param["drawBTSDescription"] = DrawBTSDescription;

                //cell
                param["drawCell"] = DrawCell;
                param["colorCell"] = brushCell.Color.ToArgb();
                param["colorCellA"] = ColorCellA.ToArgb();
                param["colorCellD"] = ColorCellD.ToArgb();
                param["colorCellE"] = ColorCellE.ToArgb();
                param["colorCellF"] = ColorCellF.ToArgb();
                param["colorCellMod3_0"] = ColorCellMod3_0.ToArgb();
                param["colorCellMod3_1"] = ColorCellMod3_1.ToArgb();
                param["colorCellMod3_2"] = ColorCellMod3_2.ToArgb();
                param["colorCellMod6_0"] = ColorCellMod6_0.ToArgb();
                param["colorCellMod6_1"] = ColorCellMod6_1.ToArgb();
                param["colorCellMod6_2"] = ColorCellMod6_2.ToArgb();
                param["colorCellMod6_3"] = ColorCellMod6_3.ToArgb();
                param["colorCellMod6_4"] = ColorCellMod6_4.ToArgb();
                param["colorCellMod6_5"] = ColorCellMod6_5.ToArgb();
                param["drawCellLabel"] = DrawCellLabel;
                param["fontCellLabelFamilyName"] = FontCellLabel.FontFamily.Name;
                param["fontCellLabelFontSize"] = FontCellLabel.Size;
                param["fontCellLabelFontStyle"] = (int)FontCellLabel.Style;
                param["colorCellLabel"] = ColorCellLabel.ToArgb();
                param["drawCellName"] = DrawCellName;
                param["drawCellCode"] = DrawCellCode;
                param["drawCellTAC"] = DrawCellTAC;
                param["drawCellECI"] = DrawCellECI;
                param["drawCellDes"] = DrawCellDes;
                param["drawCellPCI"] = DrawCellPCI;
                param["drawCellEARFCN"] = DrawCellEARFCN;
                param["drawCellFreqs"] = DrawCellFreqs;

                //Antenna
                param["drawAntenna"] = DrawAntenna;
                param["colorAntenna"] = brushAntenna.Color.ToArgb();
                param["drawAntennaLabel"] = DrawAntennaLabel;
                param["fontAntennaLabelFamilyName"] = FontAntennaLabel.FontFamily.Name;
                param["fontAntennaLabelFontSize"] = FontAntennaLabel.Size;
                param["fontAntennaLabelFontStyle"] = (int)FontAntennaLabel.Style;
                param["colorAntennaLabel"] = ColorAntennaLabel.ToArgb();
                param["drawAntennaLongitude"] = DrawAntennaLongitude;
                param["drawAntennaLatitude"] = DrawAntennaLatitude;
                param["drawAntennaDirectionType"] = DrawAntennaDirectionType;
                param["drawAntennaDirection"] = DrawAntennaDirection;
                param["drawAntennaDownward"] = DrawAntennaDownward;
                param["drawAntennaAltitude"] = DrawAntennaAltitude;
                param["drawAntennaDescription"] = DrawAntennaDescription;
                param["isVisible"] = IsVisible;
                param["FddSetting"] = FDDSetting.Param;
#if NBIOTLayer
                param["nbiotSetting"] = NBIOTSetting.Param;
#endif
                return param;
            }
            set
            {
                FeatrueMaxZoomScale = (double)value["featrueMaxZoomScale"];
                DrawCurrent = (bool)value["drawCurrent"];
                if (value.ContainsKey("CurShowSnapshotTime"))
                {
                    CurShowSnapshotTime = new DateTime((long)(value["CurShowSnapshotTime"]));
                }
                DrawServer = (bool)value["drawServer"];
                DrawIndoor = (bool)value["drawIndoor"];
                DrawOutdoor = (bool)value["drawOutdoor"];
                penSelected = new Pen(Color.FromArgb((int)value["colorSelected"]), 4);

                DrawBTS = (bool)value["drawBTS"];
                penBTS = new Pen(Color.FromArgb((int)value["colorBTS"]), 2);
                SizeBTS = (int)value["sizeBTS"];
                DrawBTSLabel = (bool)value["drawBTSLabel"];
                FontBTSLabel = new Font(new FontFamily((String)value["fontBTSLabelFontFamilyName"]), (float)value["fontBTSLabelFontSize"], (FontStyle)(int)value["fontBTSLabelFontStyle"]);
                ColorBTSLabel = getValidColor(value, "colorBTSLabel", ColorBTSLabel);
                DrawBTSName = (bool)value["drawBTSName"];
                DrawBTSLongitude = (bool)value["drawBTSLongitude"];
                DrawBTSLatitude = (bool)value["drawBTSLatitude"];
                DrawBTSType = (bool)value["drawBTSType"];
                DrawBTSDescription = (bool)value["drawBTSDescription"];

                DrawCell = (bool)value["drawCell"];
                brushCell = new SolidBrush(Color.FromArgb((int)(value["colorCell"])));

                penEarfcn_A_Cell = new Pen(getValidColor(value, "colorCellA", penEarfcn_A_Cell.Color));
                penEarfcn_D_Cell = new Pen(getValidColor(value, "colorCellD", penEarfcn_D_Cell.Color));
                penEarfcn_E_Cell = new Pen(getValidColor(value, "colorCellE", penEarfcn_E_Cell.Color));
                penEarfcn_F_Cell = new Pen(getValidColor(value, "colorCellF", penEarfcn_F_Cell.Color));

                brushCell_Mod3_0 = new SolidBrush(getValidColor(value, "colorCellMod3_0", brushCell_Mod3_0.Color));
                brushCell_Mod3_1 = new SolidBrush(getValidColor(value, "colorCellMod3_1", brushCell_Mod3_1.Color));
                brushCell_Mod3_2 = new SolidBrush(getValidColor(value, "colorCellMod3_2", brushCell_Mod3_2.Color));

                brushCell_Mod6_0 = new SolidBrush(getValidColor(value, "colorCellMod6_0", brushCell_Mod6_0.Color));
                brushCell_Mod6_1 = new SolidBrush(getValidColor(value, "colorCellMod6_1", brushCell_Mod6_1.Color));
                brushCell_Mod6_2 = new SolidBrush(getValidColor(value, "colorCellMod6_2", brushCell_Mod6_2.Color));
                brushCell_Mod6_3 = new SolidBrush(getValidColor(value, "colorCellMod6_3", brushCell_Mod6_3.Color));
                brushCell_Mod6_4 = new SolidBrush(getValidColor(value, "colorCellMod6_4", brushCell_Mod6_4.Color));
                brushCell_Mod6_5 = new SolidBrush(getValidColor(value, "colorCellMod6_5", brushCell_Mod6_5.Color));

                FontCellLabel = new Font(new FontFamily((String)value["fontCellLabelFamilyName"]), (float)value["fontCellLabelFontSize"], (FontStyle)(int)value["fontCellLabelFontStyle"]);
                ColorCellLabel = getValidColor(value, "colorCellLabel", ColorCellLabel);
                DrawCellLabel = (bool)value["drawCellLabel"];
                DrawCellName = (bool)value["drawCellName"];
                DrawCellCode = (bool)value["drawCellCode"];
                DrawCellTAC = (bool)value["drawCellTAC"];
                DrawCellECI = (bool)value["drawCellECI"];
                DrawCellDes = (bool)value["drawCellDes"];
                DrawCellPCI = (bool)value["drawCellPCI"];
                DrawCellEARFCN = (bool)value["drawCellEARFCN"];
                DrawCellFreqs = (bool)value["drawCellFreqs"];

                DrawAntenna = (bool)value["drawAntenna"];
                Color antennaColor = Color.FromArgb((int)value["colorAntenna"]);
                penAntenna = new Pen(antennaColor, 2);
                brushAntenna = new SolidBrush(antennaColor);
                DrawAntennaLabel = (bool)value["drawAntennaLabel"];
                FontAntennaLabel = new Font(new FontFamily((string)value["fontAntennaLabelFamilyName"]), (float)value["fontAntennaLabelFontSize"], (FontStyle)(int)value["fontAntennaLabelFontStyle"]);
                ColorAntennaLabel = getValidColor(value, "colorAntennaLabel", ColorAntennaLabel);
                DrawAntennaLongitude = (bool)value["drawAntennaLongitude"];
                DrawAntennaLatitude = (bool)value["drawAntennaLatitude"];
                DrawAntennaDirectionType = (bool)value["drawAntennaDirectionType"];
                DrawAntennaDirection = (bool)value["drawAntennaDirection"];
                DrawAntennaDownward = (bool)value["drawAntennaDownward"];
                DrawAntennaAltitude = (bool)value["drawAntennaAltitude"];
                DrawAntennaDescription = (bool)value["drawAntennaDescription"];
                IsVisible = (bool)value["isVisible"];

                if (value.ContainsKey("FddSetting"))
                {
                    FDDSetting.Param = (Dictionary<string, object>)value["FddSetting"];
                }
#if NBIOTLayer
                try
                {
                    NBIOTSetting.Param = (Dictionary<string, object>)value["nbiotSetting"];
                }
                catch
                {
                    //continue
                }
#endif
            }
        }

        private Color getValidColor(Dictionary<string, object> value, string name, Color defaultColor)
        {
            if (value.ContainsKey(name))
            {
                return Color.FromArgb((int)value[name]);
            }
            return defaultColor;
        }

        private SolidBrush brushServerCell = new SolidBrush(Color.Red);
        public SolidBrush NoDataCellBrush
        {
            get
            {
                return noDataCellBrush;
            }
        }
        private SolidBrush noDataCellBrush = new SolidBrush(Color.LightGray);
        public Pen NoDataCellPen
        {
            get
            {
                return noDataCellPen;
            }
        }

        private Pen noDataCellPen = new Pen(Brushes.Gray);
        public SolidBrush BrushesServer
        {
            get { return brushServerCell; }
        }
        public Color ColorServerCell
        {
            get { return brushServerCell.Color; }
            set { brushServerCell = new SolidBrush(value); }
        }

        public Color ColorSelected
        {
            get { return penSelected.Color; }
            set { penSelected =new Pen(value,2); }
        }
        private Pen penSelected = new Pen(Color.Red, 4);
        public Pen PenSelected
        {
            get { return penSelected; }
        }

        private Pen penEarfcn_A_Cell = new Pen(Color.Gold, 2);
        public Pen PenEarfcn_A_Cell
        {
            get { return penEarfcn_A_Cell; }
        }
        public Color ColorCellA
        {
            get { return penEarfcn_A_Cell.Color; }
            set { penEarfcn_A_Cell.Color = value; }
        }

        private Pen penEarfcn_D_Cell = new Pen(Color.Gray, 2);
        public Pen PenEarfcn_D_Cell
        {
            get { return penEarfcn_D_Cell; }
        }
        public Color ColorCellD
        {
            get { return penEarfcn_D_Cell.Color; }
            set { penEarfcn_D_Cell.Color = value; }
        }

        private Pen penEarfcn_E_Cell = new Pen(Color.Green, 2);
        public Pen PenEarfcn_E_Cell
        {
            get { return penEarfcn_E_Cell; }
        }
        public Color ColorCellE
        {
            get { return penEarfcn_E_Cell.Color; }
            set { penEarfcn_E_Cell.Color = value; }
        }

        private Pen penEarfcn_F_Cell = new Pen(Color.Pink, 2);
        public Pen PenEarfcn_F_Cell
        {
            get { return penEarfcn_F_Cell; }
        }
        public Color ColorCellF
        {
            get { return penEarfcn_F_Cell.Color; }
            set { penEarfcn_F_Cell.Color = value; }
        }
        
        private Pen penBTS = new Pen(Color.Black, 2);
        public Pen PenBTS
        {
            get { return penBTS; }
        }

        public Color ColorBTS
        {
            get { return penBTS.Color; }
            set { penBTS =new Pen(value,2); }
        }

        private Pen penAntenna = new Pen(Brushes.Black, 2);
        public Pen PenAntenna
        {
            get { return penAntenna; }
        }

        private SolidBrush brushAntenna = new SolidBrush(Color.Black);
        public SolidBrush BrushAntenna
        {
            get { return brushAntenna; }
        }

        public Color ColorAntenna
        {
            get { return brushAntenna.Color; }
            set
            {
                brushAntenna = new SolidBrush(value);
                penAntenna = new Pen(value, 2);
            }
        }

        private SolidBrush brushAlarmCell = new SolidBrush(Color.Red);
        public SolidBrush BrushAlarmCell
        {
            get { return brushAlarmCell; }
        }
        
        public bool DrawBTSName { get; set; } = true;
        public bool DrawBTSLongitude { get; set; } = false;
        public bool DrawBTSLatitude { get; set; } = false;
        public bool DrawBTSType { get; set; } = false;
        public bool DrawBTSDescription { get; set; } = false;
        public bool DrawCellLabel { get; set; } = true;
        public Font FontCellLabel { get; set; } = new Font(new FontFamily("宋体"), 9, FontStyle.Bold);
        public Brush BrushCellLabel
        {
            get { return new SolidBrush(ColorCellLabel); }
        }
        public Color ColorCellLabel { get; set; } = Color.Black;

        public bool DrawCellName { get; set; } = true;
        public bool DrawCellCode { get; set; } = false;
        public bool DrawCellTAC { get; set; } = false;
        public bool DrawCellECI { get; set; } = false;
        public bool DrawCellDes { get; set; } = false;
        public bool DrawCellPCI { get; set; } = false;
        public bool DrawCellNewPCI { get; set; } = false;
        public bool DrawCellEARFCN { get; set; }
        public bool DrawCellFreqs { get; set; }
        public bool DrawAntennaLabel { get; set; } = true;
        public Font FontAntennaLabel { get; set; } = new Font(new FontFamily("宋体"), 9, FontStyle.Bold);
        public Brush BrushAntennaLabel
        {
            get { return new SolidBrush(ColorAntennaLabel); }
        }
        public Color ColorAntennaLabel { get; set; } = Color.Black;
        public bool DrawAntennaLongitude { get; set; } = false;
        public bool DrawAntennaLatitude { get; set; } = false;
        public bool DrawAntennaDirectionType { get; set; } = false;
        public bool DrawAntennaDirection { get; set; } = false;
        public bool DrawAntennaDownward { get; set; } = false;
        public bool DrawAntennaAltitude { get; set; } = false;
        public bool DrawAntennaDescription { get; set; } = false;

        private Color colorNeighbourCell = Color.Cyan;
        public Color ColorNeighbourCell
        {
            get { return colorNeighbourCell; }
            set
            {
                colorNeighbourCell = value;
                if (brushNbCell != null)
                {
                    brushNbCell.Color = value;
                }
            }
        }

        private Color colorNeighbourTDCell = Color.Blue;
        public Color ColorNeighbourTDCell
        {
            get { return colorNeighbourTDCell; }
            set
            {
                colorNeighbourTDCell = value;
                if (brushNbTdCell != null)
                {
                    brushNbTdCell.Color = value;
                }
            }
        }

        private Color colorNeighbourLTECell = Color.Lime;
        public Color ColorNeighbourLTECell
        {
            get { return colorNeighbourLTECell; }
            set
            {
                colorNeighbourLTECell = value;
                if (brushNbLteCell != null)
                {
                    brushNbLteCell.Color = value;
                }
            }
        }

        private SolidBrush brushNbCell = null;
        public SolidBrush BrushNeighbourCell
        {
            get
            {
                if (brushNbCell == null)
                {
                    brushNbCell = new SolidBrush(colorNeighbourCell);
                }
                return brushNbCell;
            }
        }

        private SolidBrush brushNbTdCell = null;
        public SolidBrush BrushNeighbourTDCell
        {
            get
            {
                if (brushNbTdCell == null)
                {
                    brushNbTdCell = new SolidBrush(colorNeighbourTDCell);
                }
                return brushNbTdCell;
            }
        }

        private SolidBrush brushNbLteCell = null;
        public SolidBrush BrushNeighbourLTECell
        {
            get
            {
                if (brushNbLteCell == null)
                {
                    brushNbLteCell = new SolidBrush(colorNeighbourLTECell);
                }
                return brushNbLteCell;
            }
        }
        #endregion
    }

    public class OneColorBrushPen
    {
        public SolidBrush theBrush { get; set; }
        public Pen thePen { get; set; }
    }

    public enum DrawCellMode
    {
        Default,
        PCI_Mod3,
        PCI_Mod6,
    }
}
