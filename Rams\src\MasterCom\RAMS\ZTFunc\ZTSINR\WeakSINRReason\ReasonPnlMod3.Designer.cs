﻿namespace MasterCom.RAMS.ZTFunc.ZTSINR.WeakSINRReason
{
    partial class ReasonPnlMod3
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.label1 = new System.Windows.Forms.Label();
            this.numDiffMax = new DevExpress.XtraEditors.SpinEdit();
            this.label3 = new System.Windows.Forms.Label();
            ((System.ComponentModel.ISupportInitialize)(this.grp)).BeginInit();
            this.grp.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numDiffMax.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // grp
            // 
            this.grp.Controls.Add(this.numDiffMax);
            this.grp.Controls.Add(this.label3);
            this.grp.Controls.Add(this.label1);
            this.grp.Size = new System.Drawing.Size(678, 70);
            this.grp.Text = "模3干扰";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(36, 35);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(311, 12);
            this.label1.TabIndex = 0;
            this.label1.Text = "主服务小区与邻区同频且PCI模3值相同，即PCI/3余数相同";
            // 
            // numDiffMax
            // 
            this.numDiffMax.EditValue = new decimal(new int[] {
            6,
            0,
            0,
            0});
            this.numDiffMax.Location = new System.Drawing.Point(564, 30);
            this.numDiffMax.Name = "numDiffMax";
            this.numDiffMax.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numDiffMax.Properties.MaxValue = new decimal(new int[] {
            166,
            0,
            0,
            0});
            this.numDiffMax.Properties.MinValue = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numDiffMax.Size = new System.Drawing.Size(75, 21);
            this.numDiffMax.TabIndex = 3;
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(373, 35);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(185, 12);
            this.label3.TabIndex = 2;
            this.label3.Text = "主小区RSRP与模三小区RSRP差值≤";
            // 
            // ReasonPnlMod3
            // 
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.Name = "ReasonPnlMod3";
            this.Size = new System.Drawing.Size(678, 70);
            ((System.ComponentModel.ISupportInitialize)(this.grp)).EndInit();
            this.grp.ResumeLayout(false);
            this.grp.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numDiffMax.Properties)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.Label label1;
        private DevExpress.XtraEditors.SpinEdit numDiffMax;
        private System.Windows.Forms.Label label3;
    }
}
