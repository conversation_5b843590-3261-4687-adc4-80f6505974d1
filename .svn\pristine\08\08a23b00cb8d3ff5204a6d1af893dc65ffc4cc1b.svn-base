﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Data.SqlClient;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;
using System.ComponentModel;
using MasterCom.RAMS.NewBlackBlock;

namespace MasterCom.RAMS.Func
{
    public class BBSummaryItem
    {
        public int id { get; set; }                      //黑点ID
        public int status { get; set; }                  //黑点状态
        public int created_date { get; set; }            //黑点创建时间
        public int closed_date { get; set; }             //黑点关闭时间
        public int first_abnormal_date { get; set; }     //第一个异常时间  
        public int last_abnormal_date { get; set; }      //最后异常时间
        public int last_test_date { get; set; }          //最后测试时间
        public int abnormal_event_count { get; set; }    //异常事件个数
        public int abnormal_days { get; set; }           //问题天数
        public int normal_days { get; set; }             //正常天数
        public string area_id { get; set; }
        public string area_names { get; set; }           //位置描述、中心点经度、中心点纬度
        public string road_names { get; set; }           //道路信息
        public string cell_names { get; set; }           //相关小区
        public string name { get; set; }                 //名称
        public string reason { get; set; }               //问题原因
        public string attfiledesc { get; set; }
        public string dwdesc { get; set; }
        public string devdwdesc { get; set; }
        public int type { get; set; }                    //是否考核
        public int dwfiledate { get; set; }
        public int gooddays_count { get; set; }          //最近正常测试天数
        public int last_validate_date { get; set; }      //最近验证日期
        public int validate_status { get; set; }         //验证情况
        public string griddesc { get; set; }             //归属网格
        public string handleuser { get; set; }
        public int discoverTime { get; set; }         //黑点记录生成的日期

        //从原始信息中计算获取
        public string midLong { get; set; }              //中心经度
        public string midLat { get; set; }               //中心纬度
        public string eventInfo { get; set; }            //事件详情
        public string eventLast { get; set; }            //最后一个测试天出现的异常事件，如果最后一个测试天是正常，此处无值

        public const string ColumnSelSql = @"[id]
                ,[status]
                ,[created_date]
                ,[closed_date]
                ,[first_abnormal_date]
                ,[last_abnormal_date]
                ,[last_test_date]
                ,[abnormal_event_count]
                ,[abnormal_days]
                ,[normal_days]
                ,[area_id]
                ,[area_names]
                ,[road_names]
                ,[cell_names]
                ,[name]
                ,[reason]
                ,[attfiledesc]
                ,[dwdesc]
                ,[devdwdesc]
                ,[type]
                ,[dwfiledate]
                ,[gooddays_count]
                ,[last_validate_date]
                ,[validate_status]
                ,[griddesc]
                ,[handleuser]
                ,[discovertime]";

        public static BBSummaryItem FillFrom(Content content)
        {
            BBSummaryItem v = new BBSummaryItem();

            v.id = content.GetParamInt();
            v.status = content.GetParamInt();
            v.created_date = content.GetParamInt();
            v.closed_date = content.GetParamInt();
            v.first_abnormal_date = content.GetParamInt();
            v.last_abnormal_date = content.GetParamInt();
            v.last_test_date = content.GetParamInt();
            v.abnormal_event_count = content.GetParamInt();
            v.abnormal_days = content.GetParamInt();
            v.normal_days = content.GetParamInt();
            v.area_id = content.GetParamString();
            v.area_names = content.GetParamString();
            v.road_names = content.GetParamString();
            v.cell_names = content.GetParamString();
            v.name = content.GetParamString();
            v.reason = content.GetParamString();
            v.attfiledesc = content.GetParamString();
            v.dwdesc = content.GetParamString();
            v.devdwdesc = content.GetParamString();
            v.type = content.GetParamInt();
            v.dwfiledate = content.GetParamInt();
            v.gooddays_count = content.GetParamInt();
            v.last_validate_date = content.GetParamInt();
            v.validate_status = content.GetParamInt();
            v.griddesc = content.GetParamString();
            v.handleuser = content.GetParamString();
            v.discoverTime = content.GetParamInt();

            SetCoordinate(v);
            return v;
        }

        public static void SetCoordinate(BBSummaryItem item)
        {
            //|网络区域黑点专用_昆明城区网格05|中心点坐标[102.67414_25.07403]|
            if (item.area_names.IndexOf("|") < 0) //没有地址信息
            {
                return;
            }
            string[] strArr = item.area_names.Split(new string[] { "|" }, StringSplitOptions.RemoveEmptyEntries);

            for (int i = 0; i < strArr.Length; i++)
            {
                if (strArr[i].IndexOf("中心点坐标") >= 0)
                {
                    int pos = strArr[i].IndexOf("[");
                    string coordinate = strArr[i].Substring(pos + 1, strArr[i].Length - pos - 2);   //中心点坐标[102.67414_25.07403]

                    if (coordinate.IndexOf("_") >= 1)
                    {
                        pos = coordinate.IndexOf("_");
                        item.midLong = coordinate.Substring(0, pos);
                        item.midLat = coordinate.Substring(pos + 1, coordinate.Length - pos - 1);
                    }
                }
            }
        }
    }

    public class BBEventItem
    {
        public string cityName { get; set; }
        public string token { get; set; }

        public int black_block_id { get; set; }
        public int file_id { get; set; }
        public int sn { get; set; }
        public int date { get; set; }
        public int longitude { get; set; }
        public int latitude { get; set; }
        public int event_id { get; set; }
        public int lac { get; set; }
        public int ci { get; set; }
        public int project_id { get; set; }
        public int timevalue { get; set; }
        public int type { get; set; }
        public string logtbname { get; set; }

        public const string ColumnSelSql = @"[black_block_id]
                    ,[file_id]
                    ,[sn]
                    ,[date]
                    ,[longitude]
                    ,[latitude]
                    ,[event_id]
                    ,[lac]
                    ,[ci]
                    ,[project_id]
                    ,[timevalue]
                    ,[type]
                    ,[logtbname]";

        public static BBEventItem FillFrom(Content content, string token, string name)
        {
            BBEventItem v = new BBEventItem();

            v.black_block_id = content.GetParamInt();
            v.file_id = content.GetParamInt();
            v.sn = content.GetParamInt();
            v.date = content.GetParamInt();
            v.longitude = content.GetParamInt();
            v.latitude = content.GetParamInt();
            v.event_id = content.GetParamInt();
            v.lac = content.GetParamInt();
            v.ci = content.GetParamInt();
            v.project_id = content.GetParamInt();
            v.timevalue = content.GetParamInt();
            v.type = content.GetParamInt();
            v.logtbname = content.GetParamString();

            v.cityName = name;
            v.token = token;
            return v;
        }
    }

    public class BBStatItem
    {
        public int bbTotal { get; set; }         //已经建立的黑点总数
        public int bbAdded { get; set; }         //指定时间内新增黑点
        public int bbClosed { get; set; }        //指定时间内关闭黑点

        public BBStatItem()
        {
            bbTotal = 0;
            bbAdded = 0;
            bbClosed = 0;
        }
    }

    public class BBCityTokenItem
    {
        public string CityName { get; set; } = "";
        public string Token { get; set; } = "";
        public string TokenName { get; set; } = "";
    }

    public class BBDetailItem
    {
        [DisplayName("序号")]
        public string SN { get; set; }
        [DisplayName("地市")]
        public string CityName { get; set; }
        [DisplayName("黑点ID")]
        public string BlackID { get; set; }
        [DisplayName("名称")]
        public string Name { get; set; }
        [DisplayName("问题天数")]
        public string WrongDays { get; set; }
        [DisplayName("正常天数")]
        public string Days { get; set; }
        [DisplayName("异常事件个数")]
        public string EventsNum { get; set; }
        [DisplayName("黑点状态")]
        public string BlackStatus { get; set; }
        [DisplayName("中心点经度")]
        public string Longitude { get; set; }
        [DisplayName("中心点纬度")]
        public string Latitude { get; set; }
        [DisplayName("位置描述")]
        public string Location { get; set; }
        [DisplayName("归属网格")]
        public string Network { get; set; }
        [DisplayName("问题原因")]
        public string Reason { get; set; }
        [DisplayName("黑点创建时间")]
        public string CreateDate { get; set; }
        [DisplayName("黑点关闭时间")]
        public string CloseDate { get; set; }
        [DisplayName("第一个异常时间")]
        public string BeginWrongDate { get; set; }
        [DisplayName("最后异常时间")]
        public string EndWrongDate { get; set; }
        [DisplayName("最后测试时间")]
        public string EndTestDate { get; set; }
        [DisplayName("事件详情")]
        public string EventDetail { get; set; }
        [DisplayName("是否考核")]
        public string IsChecked { get; set; }
        [DisplayName("最近正常测试天数")]
        public string TestDay { get; set; }
        [DisplayName("最近验证日期")]
        public string TestDate { get; set; }
        [DisplayName("验证情况")]
        public string TestResult { get; set; }
        [DisplayName("最近异常事件")]
        public string Events { get; set; }
        [DisplayName("相关小区")]
        public string CellName { get; set; }
        [DisplayName("周边建筑")]
        public string Building { get; set; }
        [DisplayName("黑点发现时间")]
        public string DiscoverTime { get; set; }

        private string token;
        private string tokenName;
        public void SetToken(string token, string tokenName)
        {
            this.token = token;
            this.tokenName = tokenName;
        }

        public string GetToken()
        {
            return token;
        }

        public string GetTokenName()
        {
            return tokenName;
        }
    }
    public class BlackBlockTimeSetCondition
    {
        public int beginTime { get; set; }
        public int endTime { get; set; }

        private readonly List<BlackBlockToken> tokenList = new List<BlackBlockToken>();
        public List<BlackBlockToken> TokenList
        {
            get { return tokenList; }
        }
        public void AddToken(BlackBlockToken token)
        {
            tokenList.Add(token);
        }
    }

    public class DIYSQLBlackBlockSummary : DIYSQLBase
    {
        private readonly List<BBSummaryItem> bbSummaryList = new List<BBSummaryItem>();
        private string token { get; set; }
        private int stime { get; set; }
        private int etime { get; set; }

        private readonly List<string> cityList = new List<string>();
        public List<string> CityList
        {
            get { return cityList; }
        }
        public List<BBSummaryItem> GetBlackBlockSummaryList()
        {
            return bbSummaryList;
        }
        public DIYSQLBlackBlockSummary(MainModel mainModel, int dbid, string token, int stime, int etime)
            : base(mainModel)
        {
            this.dbid = dbid;
            this.token = token;
            this.stime = stime;
            this.etime = etime;
        }

        protected override string getSqlTextString()
        {
            string tbName = "tb_black_" + token + "block";
            string strSql = @"if exists (select * from sysobjects where type = 'U' and name = '" + tbName + "')"
                        + " begin "
                        + " select "
                        + BBSummaryItem.ColumnSelSql
                        + " from " + tbName
                        + " end ";

            return strSql;
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] types = new E_VType[27];
            types[0] = E_VType.E_Int;
            types[1] = E_VType.E_Int;
            types[2] = E_VType.E_Int;
            types[3] = E_VType.E_Int;
            types[4] = E_VType.E_Int;
            types[5] = E_VType.E_Int;
            types[6] = E_VType.E_Int;
            types[7] = E_VType.E_Int;
            types[8] = E_VType.E_Int;
            types[9] = E_VType.E_Int;
            types[10] = E_VType.E_String;
            types[11] = E_VType.E_String;
            types[12] = E_VType.E_String;
            types[13] = E_VType.E_String;
            types[14] = E_VType.E_String;
            types[15] = E_VType.E_String;
            types[16] = E_VType.E_String;
            types[17] = E_VType.E_String;
            types[18] = E_VType.E_String;
            types[19] = E_VType.E_Int;
            types[20] = E_VType.E_Int;
            types[21] = E_VType.E_Int;
            types[22] = E_VType.E_Int;
            types[23] = E_VType.E_Int;
            types[24] = E_VType.E_String;
            types[25] = E_VType.E_String;
            types[26] = E_VType.E_Int;
            return types;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    BBSummaryItem bbItem = BBSummaryItem.FillFrom(package.Content);
                    bbSummaryList.Add(bbItem);
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                if (Math.Log(index++) * (100000 / 10000) > WaitBox.ProgressPercent)
                {
                    progress++;
                    if (progress > 95)
                    {
                        progress = 5;
                        index = 0;
                    }
                    if (progress % 5 == 0)
                    {
                        WaitBox.ProgressPercent = progress;
                    }
                }
            }
        }
        public override string Name
        {
            get { return "DIYSQLBlackBlockSummary"; }
        }
    }

    public class DIYSQLBlackBlockEvt : DIYSQLBase
    {
        private readonly List<BBEventItem> bbEvtList = new List<BBEventItem>();
        private string token { get; set; }
        private string cityName { get; set; }
        private int stime { get; set; }
        private int etime { get; set; }
        public List<BBEventItem> GetBlackBlockEvtList()
        {
            return bbEvtList;
        }
        public DIYSQLBlackBlockEvt(MainModel mainModel, int dbid, string token, int stime, int etime, string name)
            : base(mainModel)
        {
            this.dbid = dbid;
            this.token = token;
            this.stime = stime;
            this.etime = etime;
            this.cityName = name;
        }

        protected override string getSqlTextString()
        {
            string tbName = "tb_black_" + token + "block_event";
            string strSql = @"if exists (select * from sysobjects where type = 'U' and name = '" + tbName + "')"
                        + " begin "
                        + " select "
                        + BBEventItem.ColumnSelSql
                        + " from " + tbName
                        + " end ";

            return strSql;
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] types = new E_VType[13];
            types[0] = E_VType.E_Int;
            types[1] = E_VType.E_Int;
            types[2] = E_VType.E_Int;
            types[3] = E_VType.E_Int;
            types[4] = E_VType.E_Int;
            types[5] = E_VType.E_Int;
            types[6] = E_VType.E_Int;
            types[7] = E_VType.E_Int;
            types[8] = E_VType.E_Int;
            types[9] = E_VType.E_Int;
            types[10] = E_VType.E_Int;
            types[11] = E_VType.E_Int;
            types[12] = E_VType.E_String;
            return types;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    BBEventItem evtItem = BBEventItem.FillFrom(package.Content, token, cityName);
                    bbEvtList.Add(evtItem);
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                if (Math.Log(index++) * (100000 / 10000) > WaitBox.ProgressPercent)
                {
                    progress++;
                    if (progress > 95)
                    {
                        progress = 5;
                        index = 0;
                    }
                    if (progress % 5 == 0)
                    {
                        WaitBox.ProgressPercent = progress;
                    }
                }
            }
        }
        public override string Name
        {
            get { return "DIYSQLBlackBlockEvent"; }
        }
    }


}
