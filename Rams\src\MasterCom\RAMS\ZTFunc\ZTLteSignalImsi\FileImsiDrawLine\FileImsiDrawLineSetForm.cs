﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc.LteSignalImsi
{
    public partial class FileImsiDrawLineSetForm : BaseDialog
    {
        public FileImsiDrawLineSetForm()
        {
            InitializeComponent();

            btnOK.Click += BtnOK_Click;
            btnCancel.Click += BtnCancel_Click;
            cbxGisInterval.CheckedChanged += GisInterval_CheckedChanged;
            numberInterval.Enabled = cbxGisInterval.Checked;
        }

        public bool IsGisIntervalLimit
        {
            get;
            private set;
        }

        public int GisIntervalMinutes
        {
            get;
            private set;
        }

        private void BtnOK_Click(object sender, EventArgs e)
        {
            IsGisIntervalLimit = cbxGisInterval.Checked;
            GisIntervalMinutes = (int)numberInterval.Value;

            DialogResult = System.Windows.Forms.DialogResult.OK;
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = System.Windows.Forms.DialogResult.Cancel;
        }

        private void GisInterval_CheckedChanged(object sender, EventArgs e)
        {
            numberInterval.Enabled = cbxGisInterval.Checked;
        }
    }
}
