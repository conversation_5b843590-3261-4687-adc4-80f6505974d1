﻿using System;
using System.Collections.Generic;
using System.Text;
using System.IO;
using System.Windows.Forms;
using System.Drawing;
using System.Drawing.Imaging;

using DevExpress.XtraCharts;
using DevExpress.XtraGrid.Views.Grid;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func.ScanGridAnaExporter;
using MasterCom.MControls;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public static class LteMgrsGridHelper
    {
        /// <summary>
        /// 获取栅格中指定频段的第一强
        /// </summary>
        /// <param name="grid"></param>
        /// <param name="type"></param>
        /// <returns>如果指定频段无值返回NaN</returns>
        public static float GetTopRsrp(LteMgrsGrid grid, LteMgrsRsrpBandType type)
        {
            List<LteMgrsFreq> freqList = grid.FreqList;
            for (int i = 0; i < freqList.Count; ++i)
            {
                LteMgrsFreq freq = freqList[i];

                if (type == LteMgrsRsrpBandType.Top
                    || (type == LteMgrsRsrpBandType.SingleD && LTECell.GetBandTypeByEarfcn(freq.Earfcn) == LTEBandType.D)
                    || (type == LteMgrsRsrpBandType.SingleF && LTECell.GetBandTypeByEarfcn(freq.Earfcn) == LTEBandType.F))
                {
                    return freq.RsrpAvg;
                }
            }
            return float.NaN;
        }

        /// <summary>
        /// 获取栅格中指定频段的相对重叠覆盖度
        /// </summary>
        /// <returns>如果指定频段无第一强，返回0</returns>
        public static float GetRelativeCoverage(LteMgrsGrid grid, LteMgrsCoverageCondition cond,ref List<LteMgrsCellData> cellData)
        {
            int retValue = 0;
            List<LteMgrsCell> cellList = FilterCells(grid.CellList, cond.FreqType);
            for (int i = 0; i < cellList.Count; ++i)
            {
                LteMgrsCell cell = cellList[i];
                if (i == 0 && cell.AvgRsrp < (decimal)cond.MinRsrp)
                {
                    break;
                }

                if (cond.EnableOptional && cell.AvgRsrp < (decimal)cond.OptionalRsrp)
                {
                    continue;
                }
               
                if (cellList[0].AvgRsrp-cell.AvgRsrp<=(decimal)cond.DiffRsrp)
                {
                    cellData.Add(new LteMgrsCellData(cell));
                    ++retValue;
                }
            }
            return retValue;
        }

        /// <summary>
        /// 按集团2015第四季度报表剔除多层网算法 (LTE扫频-重叠覆盖度)
        /// </summary>
        public static float GetCoverageJT(List<CCellWithRsrp> lstCellWithRsrps, int retValue, out List<string> lstCellNames)
        {
            lstCellNames = new List<string>();
            Dictionary<string, string> cellNameKey = new Dictionary<string, string>();//已经合并的站点
            Dictionary<string, List<CCellWithRsrp>> tpMultiDic = new Dictionary<string, List<CCellWithRsrp>>();//将50米内小区合成物理站
            
            foreach (CCellWithRsrp mainCell in lstCellWithRsrps)
            {
                if (cellNameKey.ContainsKey(mainCell.StrName))
                    continue;
                else
                {
                    List<CCellWithRsrp> tpaList = new List<CCellWithRsrp>();
                    tpaList.Add(mainCell);
                    tpMultiDic.Add(mainCell.StrName, tpaList);
                    cellNameKey.Add(mainCell.StrName, mainCell.StrName);
                }

                dealCCellWithRsrp(lstCellWithRsrps, cellNameKey, tpMultiDic, mainCell);
            }
            calcCoverageByBts(tpMultiDic, ref retValue, ref lstCellNames);

            return retValue;
        }

        private static void dealCCellWithRsrp(List<CCellWithRsrp> lstCellWithRsrps, Dictionary<string, string> cellNameKey, Dictionary<string, List<CCellWithRsrp>> tpMultiDic, CCellWithRsrp mainCell)
        {
            foreach (CCellWithRsrp secondCell in lstCellWithRsrps)
            {
                if (cellNameKey.ContainsKey(secondCell.StrName))
                    continue;

                if (mainCell.StrName != secondCell.StrName)
                {
                    double d = MathFuncs.GetDistance(mainCell.LteCell.Longitude, mainCell.LteCell.Latitude, secondCell.LteCell.Longitude, secondCell.LteCell.Latitude);
                    if (d < 50)
                    {
                        List<CCellWithRsrp> tpaList = tpMultiDic[mainCell.StrName];
                        tpaList.Add(secondCell);
                        tpMultiDic[mainCell.StrName] = tpaList;
                        cellNameKey.Add(secondCell.StrName, mainCell.StrName);
                    }
                }
            }
        }

        private static void calcCoverageByBts(Dictionary<string, List<CCellWithRsrp>> tpMultiDic, 
            ref int retValue, ref List<string> lstCellNames)
        {
            foreach (string strCellName in tpMultiDic.Keys)
            {
                List<CCellWithRsrp> tpMultiList = tpMultiDic[strCellName];
                int iFNum = calcBandTypeNum(LTEBandTypeJT.F, tpMultiList);
                int iDNum = calcBandTypeNum(LTEBandTypeJT.D, tpMultiList);

                if (iFNum + iDNum >= 3)
                {
                    retValue = dealMore(retValue, lstCellNames, tpMultiList, iFNum, iDNum);
                }
                else
                {
                    retValue = dealLess(retValue, lstCellNames, tpMultiList);
                }
            }
        }

        private static int dealMore(int retValue, List<string> lstCellNames, List<CCellWithRsrp> tpMultiList, int iFNum, int iDNum)
        {
            if (iDNum >= iFNum)
            {
                List<LTEBandTypeJT> btList = calcABValue(tpMultiList);
                foreach (CCellWithRsrp cell in tpMultiList)
                {
                    if (btList.Contains(LTECell.GetBandTypeByJT(cell.Iearfcn)))
                    {
                        retValue++;
                        addBandCell(lstCellNames, cell);
                    }
                }
            }
            else
            {
                foreach (CCellWithRsrp cell in tpMultiList)
                {
                    if (LTECell.GetBandTypeByJT(cell.Iearfcn) == LTEBandTypeJT.F)
                    {
                        retValue++;
                        addBandCell(lstCellNames, cell);
                    }
                }
            }

            return retValue;
        }

        private static int dealLess(int retValue, List<string> lstCellNames, List<CCellWithRsrp> tpMultiList)
        {
            CCellWithRsrp teamMaxRsrp = tpMultiList[0];
            foreach (CCellWithRsrp cell in tpMultiList)
            {
                if (LTECell.GetBandTypeByJT(teamMaxRsrp.Iearfcn) == LTEBandTypeJT.F)
                {
                    if (LTECell.GetBandTypeByJT(cell.Iearfcn) == LTEBandTypeJT.F)
                    {
                        retValue++;
                        addBandCell(lstCellNames, cell);
                    }
                }
                else if ((LTECell.GetBandTypeByJT(teamMaxRsrp.Iearfcn) == LTEBandTypeJT.D1
                       || LTECell.GetBandTypeByJT(teamMaxRsrp.Iearfcn) == LTEBandTypeJT.D2
                       || LTECell.GetBandTypeByJT(teamMaxRsrp.Iearfcn) == LTEBandTypeJT.D3)
                    && (LTECell.GetBandTypeByJT(cell.Iearfcn) == LTEBandTypeJT.D1
                       || LTECell.GetBandTypeByJT(cell.Iearfcn) == LTEBandTypeJT.D2
                       || LTECell.GetBandTypeByJT(cell.Iearfcn) == LTEBandTypeJT.D3))
                {
                    retValue++;
                    addBandCell(lstCellNames, cell);
                }
            }

            return retValue;
        }

        private static void addBandCell(List<string> lstCellNames, CCellWithRsrp cell)
        {
            if (!lstCellNames.Contains(cell.StrName))
                lstCellNames.Add(cell.StrName);
        }

        private static List<LTEBandTypeJT> calcABValue(List<CCellWithRsrp> tpMultiList)
        {
            int iD1Num = calcBandTypeNum(LTEBandTypeJT.D1, tpMultiList);
            int iD2Num = calcBandTypeNum(LTEBandTypeJT.D2, tpMultiList);
            int iD3Num = calcBandTypeNum(LTEBandTypeJT.D3, tpMultiList);

            int iA1 = 0;
            int iA2 = 0;
            int iA3 = 0;

            if (iD1Num + iD2Num >= 3)
                iA1 = 1;
            if (iD1Num + iD3Num >= 3)
                iA2 = 1;
            if (iD2Num + iD3Num >= 3)
                iA3 = 1;

            List<LTEBandTypeJT> btList = new List<LTEBandTypeJT>();
            if (iA1 + iA2 + iA3 >= 1)
            {
                //Modify by JYH 应该是小区数据大值，并非RSRP大值
                if (iD1Num >= iD2Num && iD1Num >= iD3Num)
                    btList.Add(LTEBandTypeJT.D1);
                else if (iD2Num >= iD1Num && iD2Num >= iD3Num)
                    btList.Add(LTEBandTypeJT.D2);
                else// if (iD3Num >= iD1Num && iD3Num >= iD2Num)
                    btList.Add(LTEBandTypeJT.D3);
            }
            else
            {
                btList.Add(LTEBandTypeJT.D1);
                btList.Add(LTEBandTypeJT.D2);
                btList.Add(LTEBandTypeJT.D3);
            }

            return btList;
        }

        private static int calcBandTypeNum(LTEBandTypeJT bandType, List<CCellWithRsrp> tpMultiList)
        {
            int iBandTypeNum = 0;
            foreach (CCellWithRsrp cell in tpMultiList)
            {
                if (LTECell.GetBandTypeByJT(cell.Iearfcn) == bandType)
                {
                    iBandTypeNum++;
                }
                else if (bandType == LTEBandTypeJT.D
                    && (LTECell.GetBandTypeByJT(cell.Iearfcn) == LTEBandTypeJT.D1 || LTECell.GetBandTypeByJT(cell.Iearfcn) == LTEBandTypeJT.D2 || LTECell.GetBandTypeByJT(cell.Iearfcn) == LTEBandTypeJT.D3))
                {
                    iBandTypeNum++;
                }
            }
            return iBandTypeNum;
        }

        /// <summary>
        /// 按集团2015第四季度报表剔除多层网算法
        /// </summary>
        public static float GetRelativeCoverageJT(LteMgrsGrid grid, LteMgrsCoverageCondition cond, ref List<LteMgrsCellData> cellData)
        {
            if (!cond.CheckTwoEarfcn)
                return LteMgrsGridHelper.GetRelativeCoverage(grid, cond, ref cellData);

            List<LteMgrsCell> cellList = getFilterCellList(grid, cond);

            int retValue = 0;
            if (cellList.Count == 0)
            {
                return retValue;
            }

            LteMgrsCell cellMax = cellList[0];//D或F频中最强信号
            if (cellMax.AvgRsrp < (decimal)cond.MinRsrp)
            {
                return retValue;
            }
            decimal cmpValue = cond.EnableOptional ? Math.Max(cellMax.AvgRsrp - (decimal)cond.DiffRsrp, (decimal)cond.OptionalRsrp)
                 : cellMax.AvgRsrp - (decimal)cond.DiffRsrp;
            List<LteMgrsCell> multiCellList = new List<LteMgrsCell>();
            foreach (LteMgrsCell cell in cellList)
            {
                if (cell.AvgRsrp < cmpValue)
                    break;
                DualBand dualBand = DualBandGetter.GetDualBand(cell.Cell as LTECell, cell.Time);
                if (dualBand == null || dualBand.EDualBandTypeJT == EDualBand.Normal)
                {
                    cellData.Add(new LteMgrsCellData(cell));
                    retValue++;
                }
                else if (dualBand.EDualBandTypeJT == EDualBand.MultiBand)
                    multiCellList.Add(cell);
            }

            Dictionary<string, string> cellNameKey = new Dictionary<string, string>();//已经合并的站点
            Dictionary<string, List<LteMgrsCell>> tpMultiDic = new Dictionary<string, List<LteMgrsCell>>();//将50米内小区合成物理站
            foreach (LteMgrsCell mainCell in multiCellList)
            {
                if (cellNameKey.ContainsKey(mainCell.CellName))
                    continue;
                else
                {
                    List<LteMgrsCell> tpaList = new List<LteMgrsCell>();
                    tpaList.Add(mainCell);
                    tpMultiDic.Add(mainCell.CellName, tpaList);
                    cellNameKey.Add(mainCell.CellName, mainCell.CellName);
                }

                mergeSecondCell(multiCellList, cellNameKey, tpMultiDic, mainCell);
            }
            calcCoverageByBts(tpMultiDic, cond, ref retValue, ref cellData);

            return retValue;
        }

        private static List<LteMgrsCell> getFilterCellList(LteMgrsGrid grid, LteMgrsCoverageCondition cond)
        {
            //多层网剔除算法，计算覆盖度
            List<LteMgrsCell> cellList = LteMgrsGridHelper.FilterCells(grid.CellList, cond.FreqType);
            //只分析D和F频段小区，过滤其他频段
            for (int i = 0; i < cellList.Count; i++)
            {
                LteMgrsCell cell = cellList[i];
                if (cell.BandJT != LTEBandTypeJT.D1
                  && cell.BandJT != LTEBandTypeJT.D2
                  && cell.BandJT != LTEBandTypeJT.D3
                  && cell.BandJT != LTEBandTypeJT.F)
                {//非D/F需过滤
                    cellList.RemoveAt(i);
                    i--;
                }
                else if (cond.FilterF2)
                {//过滤F2频点，内蒙需求
                    LTEBandType_F bandF = GetBandTypeByJT_F(cell.Earfcn);
                    if (bandF == LTEBandType_F.F2)
                    {
                        cellList.RemoveAt(i);
                        i--;
                    }
                }
            }

            return cellList;
        }

        private static void mergeSecondCell(List<LteMgrsCell> multiCellList, Dictionary<string, string> cellNameKey, Dictionary<string, List<LteMgrsCell>> tpMultiDic, LteMgrsCell mainCell)
        {
            foreach (LteMgrsCell secondCell in multiCellList)
            {
                if (cellNameKey.ContainsKey(secondCell.CellName))
                    continue;

                if (mainCell.CellName != secondCell.CellName)
                {
                    double d = MathFuncs.GetDistance((double)mainCell.Longitude, (double)mainCell.Latitude, (double)secondCell.Longitude, (double)secondCell.Latitude);
                    if (d < 50)
                    {
                        tpMultiDic[mainCell.CellName].Add(secondCell);
                        cellNameKey.Add(secondCell.CellName, mainCell.CellName);
                    }
                }
            }
        }

        /// <summary>
        /// 遍历物理站计算重叠度
        /// </summary>
        private static void calcCoverageByBts(Dictionary<string, List<LteMgrsCell>> tpMultiDic, LteMgrsCoverageCondition cond, ref int retValue, ref List<LteMgrsCellData> cellData)
        {
            foreach (string strCellName in tpMultiDic.Keys)
            {
                List<LteMgrsCell> tpMultiList = tpMultiDic[strCellName];
                int iFNum = calcBandTypeNum(LTEBandTypeJT.F, tpMultiList);
                int iDNum = calcBandTypeNum(LTEBandTypeJT.D, tpMultiList);

                if (iFNum + iDNum >= 3)
                {
                    retValue = dealMore(cond, retValue, cellData, tpMultiList, iFNum, iDNum);
                }
                else
                {
                    retValue = dealLess(retValue, cellData, tpMultiList);
                }
            }
        }

        private static int dealMore(LteMgrsCoverageCondition cond, int retValue, List<LteMgrsCellData> cellData, List<LteMgrsCell> tpMultiList, int iFNum, int iDNum)
        {
            if (iDNum >= iFNum)
            {
                List<LTEBandTypeJT> btList = calcABValue(tpMultiList);
                foreach (LteMgrsCell cell in tpMultiList)
                {
                    bool isValid = btList.Contains(cell.BandJT);
                    retValue = addCellData(retValue, cellData, cell, isValid);
                }
            }
            else
            {
                if (cond.EnableFBandType)//F频分F1/F2处理 Modfiy By JYH 20160623
                {
                    List<LTEBandType_F> btList = calcABValue_F(tpMultiList);
                    foreach (LteMgrsCell cell in tpMultiList)
                    {
                        bool isValid = btList.Contains(GetBandTypeByJT_F(cell.Earfcn));
                        retValue = addCellData(retValue, cellData, cell, isValid);
                    }
                }
                else
                {
                    foreach (LteMgrsCell cell in tpMultiList)
                    {
                        bool isValid = cell.BandJT == LTEBandTypeJT.F;
                        retValue = addCellData(retValue, cellData, cell, isValid);
                    }
                }
            }

            return retValue;
        }

        private static int dealLess(int retValue, List<LteMgrsCellData> cellData, List<LteMgrsCell> tpMultiList)
        {
            LteMgrsCell teamMaxRsrp = tpMultiList[0];
            foreach (LteMgrsCell cell in tpMultiList)
            {
                if (LTECell.GetBandTypeByJT(teamMaxRsrp.Earfcn) == LTEBandTypeJT.F)
                {
                    bool isValid = cell.BandJT == LTEBandTypeJT.F;
                    retValue = addCellData(retValue, cellData, cell, isValid);
                }
                else if (teamMaxRsrp.BandJT == LTEBandTypeJT.D1
                    || teamMaxRsrp.BandJT == LTEBandTypeJT.D2
                    || teamMaxRsrp.BandJT == LTEBandTypeJT.D3)
                {
                    bool isValid = (cell.BandJT == LTEBandTypeJT.D1
                         || cell.BandJT == LTEBandTypeJT.D2
                         || cell.BandJT == LTEBandTypeJT.D3);
                    retValue = addCellData(retValue, cellData, cell, isValid);
                }
            }

            return retValue;
        }

        private static int addCellData(int retValue, List<LteMgrsCellData> cellData, LteMgrsCell cell, bool isValid)
        {
            bool filtered = false;
            if (isValid)
                retValue++;
            else
                filtered = true;

            cellData.Add(new LteMgrsCellData(cell, filtered));
            return retValue;
        }

        /// <summary>
        /// 按频段统计个数
        /// </summary>
        private static int calcBandTypeNum(LTEBandTypeJT bandType, List<LteMgrsCell> tpMultiList)
        {
            int iBandTypeNum = 0;
            foreach (LteMgrsCell cell in tpMultiList)
            {
                if (cell.BandJT == bandType)
                {
                    iBandTypeNum++;
                }
                else if (bandType == LTEBandTypeJT.D
                    && (cell.BandJT == LTEBandTypeJT.D1
                    || cell.BandJT == LTEBandTypeJT.D2
                    || cell.BandJT == LTEBandTypeJT.D3)
                    )
                {
                    iBandTypeNum++;
                }
            }
            return iBandTypeNum;
        }

        /// <summary>
        /// 计算AB值(D频)
        /// </summary>
        private static List<LTEBandTypeJT> calcABValue(List<LteMgrsCell> tpMultiList)
        {
            int iD1Num = calcBandTypeNum(LTEBandTypeJT.D1, tpMultiList);
            int iD2Num = calcBandTypeNum(LTEBandTypeJT.D2, tpMultiList);
            int iD3Num = calcBandTypeNum(LTEBandTypeJT.D3, tpMultiList);

            int iA1 = 0;
            int iA2 = 0;
            int iA3 = 0;

            if (iD1Num + iD2Num >= 3)
                iA1 = 1;
            if (iD1Num + iD3Num >= 3)
                iA2 = 1;
            if (iD2Num + iD3Num >= 3)
                iA3 = 1;

            List<LTEBandTypeJT> btList = new List<LTEBandTypeJT>();
            if (iA1 + iA2 + iA3 >= 1)
            {//Modify by JYH 应该是小区数据大值，并非RSRP大值
                if (iD1Num >= iD2Num && iD1Num >= iD3Num)
                    btList.Add(LTEBandTypeJT.D1);
                else if (iD2Num >= iD1Num && iD2Num >= iD3Num)
                    btList.Add(LTEBandTypeJT.D2);
                else// if (iD3Num >= iD1Num && iD3Num >= iD2Num)
                    btList.Add(LTEBandTypeJT.D3);
            }
            else
            {
                btList.Add(LTEBandTypeJT.D1);
                btList.Add(LTEBandTypeJT.D2);
                btList.Add(LTEBandTypeJT.D3);
            }

            return btList;
        }

#region 新增F频判断
        /// <summary>
        /// 计算AB值(F频)
        /// </summary>
        private static List<LTEBandType_F> calcABValue_F(List<LteMgrsCell> tpMultiList)
        {
            int iF1Num = 0;
            int iF2Num = 0;
            LteMgrsCell maxRsrpCell = null;
            foreach (LteMgrsCell lteCell in tpMultiList)
            {
                if (GetBandTypeByJT_F(lteCell.Earfcn) == LTEBandType_F.F1)
                    iF1Num += 1;
                if (GetBandTypeByJT_F(lteCell.Earfcn) == LTEBandType_F.F2)
                    iF2Num += 1;

                if (iF1Num + iF2Num > 0 && maxRsrpCell == null)
                    maxRsrpCell = lteCell;
            }

            List<LTEBandType_F> btList = new List<LTEBandType_F>();
            if (iF1Num + iF2Num >= 3)
            {
                if(iF1Num>= iF2Num)
                    btList.Add(LTEBandType_F.F1);
                else
                    btList.Add(LTEBandType_F.F2);
            }
            else
            {
                btList.Add(LTEBandType_F.F1);
                btList.Add(LTEBandType_F.F2);
            }
            return btList;
        }

        /// <summary>
        /// 按集团划分规则(内蒙首提需求)
        /// </summary>
        public static LTEBandType_F GetBandTypeByJT_F(int earfcn)
        {
            LTEBandType_F band = LTEBandType_F.Undefined;
            if (38300 <= earfcn && earfcn < 38600)
            {
                if (38300 <= earfcn && earfcn < 38500)
                    band = LTEBandType_F.F1;
                else
                    band = LTEBandType_F.F2;
            }
            return band;
        }

#endregion

        /// <summary>
        ///  按指定频段过滤栅格列表(按小区)
        /// </summary>
        public static List<LteMgrsCell> FilterCells(List<LteMgrsCell> cellList, LteMgrsCoverageBandType type)
        {
            if (type == LteMgrsCoverageBandType.All)
            {
                return new List<LteMgrsCell>(cellList);
            }

            List<LteMgrsCell> retList = new List<LteMgrsCell>();
            for (int i = 0; i < cellList.Count; ++i)
            {
                LteMgrsCell cell = cellList[i];
                addValidRetList(cellList, type, retList, cell);
            }
            return retList;
        }

        class FreqInfo
        {
            public FreqInfo(List<LTEBandTypeJT> bandTypes)
            {
                BandTypes = bandTypes;
            }

            public FreqInfo(int earfcn)
            {
                Earfcn = earfcn;
            }

            public List<LTEBandTypeJT> BandTypes { get; set; }
            public int Earfcn { get; set; }

            public bool Judge(LTEBandTypeJT bandType)
            {
                foreach (var type in BandTypes)
                {
                    if (bandType == type)
                    {
                        return true;
                    }
                }
                return false;
            }

            public bool Judge(int earfcn)
            {
                if (Earfcn != 0 && earfcn == Earfcn)
                {
                    return true;
                }
                return false;
            }
        }

        private static Dictionary<LteMgrsCoverageBandType, FreqInfo> freqInfoDic = initFreqInfoDic();
        private static Dictionary<LteMgrsCoverageBandType, FreqInfo> initFreqInfoDic()
        {
            Dictionary<LteMgrsCoverageBandType, FreqInfo> dic = new Dictionary<LteMgrsCoverageBandType, FreqInfo>();
            dic.Add(LteMgrsCoverageBandType.SingleD, new FreqInfo(new List<LTEBandTypeJT> { LTEBandTypeJT.D1, LTEBandTypeJT.D2, LTEBandTypeJT.D3 }));
            dic.Add(LteMgrsCoverageBandType.SingleF, new FreqInfo(new List<LTEBandTypeJT> { LTEBandTypeJT.F }));
            dic.Add(LteMgrsCoverageBandType.SingleD1, new FreqInfo(new List<LTEBandTypeJT> { LTEBandTypeJT.D1 }));
            dic.Add(LteMgrsCoverageBandType.SingleD2, new FreqInfo(new List<LTEBandTypeJT> { LTEBandTypeJT.D2 }));
            dic.Add(LteMgrsCoverageBandType.SingleD_38098, new FreqInfo(38098));
            dic.Add(LteMgrsCoverageBandType.SingleF1, new FreqInfo(38400));
            dic.Add(LteMgrsCoverageBandType.SingleF2, new FreqInfo(38544));
            dic.Add(LteMgrsCoverageBandType.SingleD_40936, new FreqInfo(40936));
            dic.Add(LteMgrsCoverageBandType.SingleD_40940, new FreqInfo(40940));
            dic.Add(LteMgrsCoverageBandType.SingleE_38950, new FreqInfo(38950));
            dic.Add(LteMgrsCoverageBandType.SingleE_39148, new FreqInfo(39148));
            dic.Add(LteMgrsCoverageBandType.FDD1_3683, new FreqInfo(3683));
            dic.Add(LteMgrsCoverageBandType.FDD1_3692, new FreqInfo(3692));
            dic.Add(LteMgrsCoverageBandType.FDD2_1259, new FreqInfo(1259));
            dic.Add(LteMgrsCoverageBandType.FDD2_1300, new FreqInfo(1300));
            dic.Add(LteMgrsCoverageBandType.FDD2_1309, new FreqInfo(1309));
            dic.Add(LteMgrsCoverageBandType.FDD2_1359, new FreqInfo(1359));
            return dic;
        }


        private static void addValidRetList(List<LteMgrsCell> cellList, LteMgrsCoverageBandType type, List<LteMgrsCell> retList, LteMgrsCell cell)
        {
            bool isValid = false;
            FreqInfo info;
            if (freqInfoDic.TryGetValue(type, out info)
                && (info.Judge(cell.BandJT) || info.Judge(cell.Earfcn)))
            {
                isValid = true;
            }

            if (isValid)
            {
                retList.Add(cell);
            }
            else if (type == LteMgrsCoverageBandType.Top
                && (LTECell.GetBandTypeByJT(cellList[0].Earfcn) == LTEBandTypeJT.D1
                    || LTECell.GetBandTypeByJT(cellList[0].Earfcn) == LTEBandTypeJT.D2
                    || LTECell.GetBandTypeByJT(cellList[0].Earfcn) == LTEBandTypeJT.D3) // top is SingleD
                && (cell.BandJT == LTEBandTypeJT.D1
                    || cell.BandJT == LTEBandTypeJT.D2
                    || cell.BandJT == LTEBandTypeJT.D3))
            {
                retList.Add(cell);
            }
            else if (type == LteMgrsCoverageBandType.Top
                && LTECell.GetBandTypeByJT(cellList[0].Earfcn) == LTEBandTypeJT.F // top is SingleF
                && cell.BandJT == LTEBandTypeJT.F)
            {
                retList.Add(cell);
            }
            else if (type == LteMgrsCoverageBandType.TopEarfcn
                && LTECell.GetBandTypeByJT(cellList[0].Earfcn) == cell.BandJT)
            {
                retList.Add(cell);
            }
        }

        /// <summary>
        /// 按指定频段过滤栅格列表(按小区)
        /// </summary>
        public static List<LteMgrsCell> FilterCells(List<LteMgrsCell> cellList, LteMgrsRsrpBandType type)
        {
            List<LteMgrsCell> retList = new List<LteMgrsCell>();
            for (int i = 0; i < cellList.Count; ++i)
            {
                LteMgrsCell cell = cellList[i];
                if ((type == LteMgrsRsrpBandType.SingleD && cell.BandJT == LTEBandTypeJT.D1)
                    || (type == LteMgrsRsrpBandType.SingleD && cell.BandJT == LTEBandTypeJT.D2)
                    || (type == LteMgrsRsrpBandType.SingleD && cell.BandJT == LTEBandTypeJT.D3)
                    || (type == LteMgrsRsrpBandType.SingleF && cell.BandJT == LTEBandTypeJT.F))
                {
                    retList.Add(cell);
                }
                else if (type == LteMgrsRsrpBandType.Top
                    && (LTECell.GetBandTypeByJT(cellList[0].Earfcn) == LTEBandTypeJT.D1
                        || LTECell.GetBandTypeByJT(cellList[0].Earfcn) == LTEBandTypeJT.D2
                        || LTECell.GetBandTypeByJT(cellList[0].Earfcn) == LTEBandTypeJT.D3) // top is SingleD
                    && (cell.BandJT == LTEBandTypeJT.D1
                        || cell.BandJT == LTEBandTypeJT.D2
                        || cell.BandJT == LTEBandTypeJT.D3))
                {
                    retList.Add(cell);
                }
                else if (type == LteMgrsRsrpBandType.Top
                    && LTECell.GetBandTypeByJT(cellList[0].Earfcn) == LTEBandTypeJT.F // top is SingleF
                    && cell.BandJT == LTEBandTypeJT.F)
                {
                    retList.Add(cell);
                }
            }
            return retList;
        }
    }

    public class LteMgrsCellMatcher
    {
        public List<LteMgrsCell> GetCells(List<LteMgrsTestPoint> testPoints)
        {
            Dictionary<ICell, LteMgrsCell> cellDic = new Dictionary<ICell, LteMgrsCell>();

            Dictionary<string, ICell> cellTmpDic = new Dictionary<string, ICell>();//存储EARFCN、PCI与小区的关系
            foreach (LteMgrsTestPoint curPoint in testPoints)
            {
                for (int i = 0; i < curPoint.EarfcnList.Count && i < curPoint.PciList.Count; ++i)
                {
                    addCellDic(cellTmpDic, curPoint, i);
                }
            }

            foreach (LteMgrsTestPoint curPoint in testPoints)
            {
                for (int i = 0; i < curPoint.EarfcnList.Count && i < curPoint.PciList.Count; ++i)
                {
                    ICell cell = getCell(cellTmpDic, curPoint, i);

                    LteMgrsCell mgrsCell = getMgrsCell(cellDic, curPoint, i, cell);
                    mgrsCell.SetTime(curPoint.Time);
                    mgrsCell.AddPoint(curPoint.RsrpList[i], curPoint.SinrList[i]);
                }
            }

            return new List<LteMgrsCell>(cellDic.Values);
        }

        private void addCellDic(Dictionary<string, ICell> cellTmpDic, LteMgrsTestPoint curPoint, int i)
        {
            string strKey = string.Format("{0}-{1}", curPoint.EarfcnList[i], curPoint.PciList[i]);
            if (!cellTmpDic.ContainsKey(strKey))
            {
                ICell cell = GetCell(curPoint, i);
                if (cell != null)
                {
                    cellTmpDic.Add(strKey, cell);
                }
            }
        }

        private static ICell getCell(Dictionary<string, ICell> cellTmpDic, LteMgrsTestPoint curPoint, int i)
        {
            ICell cell;
            string strKey = string.Format("{0}-{1}", curPoint.EarfcnList[i], curPoint.PciList[i]);
            if (cellTmpDic.ContainsKey(strKey))
                cell = cellTmpDic[strKey];
            else
                cell = new UnknowCell(strKey);
            return cell;
        }

        private static LteMgrsCell getMgrsCell(Dictionary<ICell, LteMgrsCell> cellDic, LteMgrsTestPoint curPoint, int i, ICell cell)
        {
            LteMgrsCell mgrsCell;
            if (!cellDic.TryGetValue(cell, out mgrsCell))
            {
                if (cell is UnknowCell)
                {
                    mgrsCell = new LteMgrsCell(curPoint.EarfcnList[i], curPoint.PciList[i]);
                }
                else
                {
                    mgrsCell = new LteMgrsCell(cell);
                }
                cellDic.Add(cell, mgrsCell);
            }

            return mgrsCell;
        }

        /// <summary>
        /// 通过栅格的频点获取小区的话，是没法统计小区的采样点个数的
        /// </summary>
        /// <param name="freqList"></param>
        /// <returns></returns>
        public List<ICell> GetCells(List<LteMgrsFreq> freqList)
        {
            return new List<ICell>();
        }

        protected virtual ICell GetCell(LteMgrsTestPoint curPoint, int index)
        {
            return CellManager.GetInstance().GetNearestLTECellByEARFCNPCI(curPoint.Time, curPoint.EarfcnList[index], curPoint.PciList[index],
                curPoint.Longitude, curPoint.Latitude);
        }
    }

    public static class LteMgrsScreenShoter
    {
        public static string GisScreenshot(string fileName)
        {
            string picDir = Path.Combine(Application.StartupPath, @"userdata\ScanGridPictures");
            if (!Directory.Exists(picDir))
            {
                Directory.CreateDirectory(picDir);
            }

            string picFile = Path.Combine(picDir, fileName + ".png");
            Bitmap bitMap = MainModel.GetInstance().MainForm.GetMapForm().DrawToBitmapDIY();
            bitMap.Save(picFile, ImageFormat.Png);
            bitMap.Dispose();
            return picFile;
        }

        public static string ChartScreenshot(ChartControl chart, int rowCnt, string fileName)
        {
            string picDir = Path.Combine(Application.StartupPath, @"userdata\ScanGridPictures");
            if (!Directory.Exists(picDir))
            {
                Directory.CreateDirectory(picDir);
            }

            string picFile = Path.Combine(picDir, fileName + ".wmf");
            ChartControl tmpChart = chart.Clone() as ChartControl;
            tmpChart.Size = new Size(540, Math.Max(360, rowCnt * 40));
            tmpChart.ExportToImage(picFile, ImageFormat.Wmf);
            tmpChart.Dispose();
            return picFile;
        }
    }

    public class LteMgrsWordControl : WordControl
    {
        public LteMgrsWordControl(bool visible) : base(visible)
        {
        }

        public LteMgrsWordControl(string filename, bool visible) : base(filename, visible)
        {
        }

        public override void InsertText(string pText, object styles)
        {
            object styleObj = styles;
            object Units = Word.WdUnits.wdStory;
            WordApp.Selection.EndKey(ref Units, ref oMissing);

            if (styles.ToString() == "标题")
            {
                WordApp.Application.Selection.Font.Size = 18;
            }
            else if (styles.ToString() == "标题 1" || styles.ToString() == "标题 2" || styles.ToString() == "标题 3")
            {
                WordApp.Application.Selection.Font.Size = 14;
            }
            else if (styles.ToString() == "正文")
            {
                WordApp.Application.Selection.Font.Size = 10.5f;
            }

            WordApp.Application.Selection.set_Style(ref styleObj);
            WordApp.Application.Selection.TypeText(pText);
        }

        public void ApplyGlobalStyle()
        {
            WordDoc.Content.Font.Name = "微软雅黑";

            WordDoc.Paragraphs.LineSpacingRule = Word.WdLineSpacing.wdLineSpace1pt5;
            foreach (Word.Table tb in WordDoc.Tables)
            {
                tb.Range.Font.Size = 9;
                tb.Range.ParagraphFormat.LineSpacingRule = Word.WdLineSpacing.wdLineSpaceSingle;
            }
        }

        public virtual void InsertGridView(GridView gv, List<ColorRange> colorRange)
        {

            Word.Table wtb = this.CreateTable(gv.RowCount + 1, gv.Columns.Count);
            wtb.AutoFitBehavior(Word.WdAutoFitBehavior.wdAutoFitWindow);

            int curRow = 1;
            for (int i = 0; i < gv.Columns.Count; ++i)
            {
                this.InsertText(
                    wtb,
                    curRow, i + 1,
                    gv.Columns[i].Caption == "" ? gv.Columns[i].ToString() : gv.Columns[i].Caption,
                    "正文");
                wtb.Cell(curRow, i + 1).Range.ParagraphFormat.Alignment = Word.WdParagraphAlignment.wdAlignParagraphCenter;
                wtb.Cell(curRow, i + 1).Range.Orientation = Word.WdTextOrientation.wdTextOrientationVerticalFarEast;
                wtb.Cell(curRow, i + 1).Range.Bold = 1;
                if (colorRange != null && i > 0 && i - 1 < colorRange.Count)
                {
                    this.ApplyStyle(wtb, curRow, i + 1, Color.Black, colorRange[i - 1].color);
                }
            }
            wtb.Rows[curRow].Cells.VerticalAlignment = Word.WdCellVerticalAlignment.wdCellAlignVerticalCenter;

            for (int i = 0; i < gv.RowCount; ++i)
            {
                for (int j = 0; j < gv.Columns.Count; ++j)
                {
                    string txt = gv.GetRowCellDisplayText(i, gv.Columns[j]);
                    this.InsertText(wtb, i + 2, j + 1, txt, "正文");
                    wtb.Cell(i + 2, j + 1).Range.ParagraphFormat.Alignment = Word.WdParagraphAlignment.wdAlignParagraphCenter;
                }
            }
        }
    }

    /// <summary>
    /// F频段的F1、F2
    /// </summary>
    public enum LTEBandType_F
    {
        Undefined,
        F1,
        F2
    }
}
