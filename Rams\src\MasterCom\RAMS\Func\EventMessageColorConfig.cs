﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Drawing;
using System.Xml;
using MasterCom.Util;
using System.Windows.Forms;

namespace MasterCom.RAMS.Func
{
    /// <summary>
    /// 事件信令列表中用于对名称列进行颜色渲染
    /// </summary>
    public sealed class EventMessageColorConfig : XmlConfigFile
    {
        private static uint defaultBackColor = (uint)Color.White.ToArgb();
        private static uint defaultForeColor = (uint)Color.Black.ToArgb();
        private static bool defaultFontBold = false;
        public static uint DefaultBackColor
        {
            get { return defaultBackColor; }
        }
        public static uint DefaultForeColor
        {
            get { return defaultForeColor; }
        }
        public static bool DefaultFontBold
        {
            get { return defaultFontBold; }
        }

        private static EventMessageColorConfig instance = null;
        public static EventMessageColorConfig GetInstance()
        {
            if (instance == null)
            {
                instance = new EventMessageColorConfig();
            }
            return instance;
        }

        private EventMessageColorConfig()
        {
            this.filePath = Application.StartupPath + @"\userdata\EventMessageColor.xml";
            if (System.IO.File.Exists(filePath))
            {
                Load();
            }
            else
            {
                Save();
            }
        }

        public bool GetFontBold(string key, ConfigType type)
        {
            if (type != ConfigType.EventFontBold && type != ConfigType.MessageFontBold)
            {
                return defaultFontBold;
            }

            DataType dataType;
            StyleType fontType;
            this.splitType(type, out dataType, out fontType);

            XmlElement config = GetConfig(Enum.GetName(typeof(DataType), dataType));
            if (config == null)
            {
                return defaultFontBold;
            }

            XmlElement keyItem = GetItem(config, key);
            if (keyItem == null)
            {
                return defaultFontBold;
            }

            XmlElement fontItem = GetItem(keyItem, Enum.GetName(typeof(StyleType), fontType));
            if (fontItem == null)
            {
                return defaultFontBold;
            }

            return fontItem.InnerText == true.ToString();
        }

        public bool SaveFontBold(string key, bool value, ConfigType type)
        {
            DataType dataType;
            StyleType fontType;
            this.splitType(type, out dataType, out fontType);

            XmlElement config = GetConfig(Enum.GetName(typeof(DataType), dataType));
            if (config == null)
            {
                config = AddConfig(Enum.GetName(typeof(DataType), dataType));
            }

            XmlElement keyItem = GetItem(config, key);
            if (keyItem == null)
            {
                keyItem = AddItem(config, key);
            }

            XmlElement fontItem = GetItem(keyItem, Enum.GetName(typeof(StyleType), fontType));
            if (fontItem == null)
            {
                fontItem = AddItem(keyItem, Enum.GetName(typeof(StyleType), fontType));
            }

            fontItem.InnerText = value.ToString();
            //return Save();
            return true;
        }

        public uint GetColor(string key, ConfigType type)
        {
            DataType dataType;
            StyleType colorType;
            this.splitType(type, out dataType, out colorType);

            XmlElement config = GetConfig(Enum.GetName(typeof(DataType), dataType));
            if (config == null)
            {
                return colorType == StyleType.ForeColor ? EventMessageColorConfig.DefaultForeColor : EventMessageColorConfig.DefaultBackColor;
            }

            XmlElement keyItem = GetItem(config, key);
            if (keyItem == null)
            {
                return colorType == StyleType.ForeColor ? EventMessageColorConfig.DefaultForeColor : EventMessageColorConfig.DefaultBackColor;
            }

            XmlElement colorItem = GetItem(keyItem, Enum.GetName(typeof(StyleType), colorType));
            if (colorItem == null)
            {
                return colorType == StyleType.ForeColor ? EventMessageColorConfig.DefaultForeColor : EventMessageColorConfig.DefaultBackColor;
            }

            uint defaultValue = colorType == StyleType.ForeColor ? EventMessageColorConfig.DefaultForeColor : EventMessageColorConfig.DefaultBackColor;
            return colorItem.InnerText == "" ? defaultValue : uint.Parse(colorItem.InnerText);
        }

        public bool SaveColor(string key, uint value, ConfigType type)
        {
            DataType dataType;
            StyleType colorType;
            this.splitType(type, out dataType, out colorType);

            XmlElement config = GetConfig(Enum.GetName(typeof(DataType), dataType));
            if (config == null)
            {
                config = AddConfig(Enum.GetName(typeof(DataType), dataType));
            }

            XmlElement keyItem = GetItem(config, key);
            if (keyItem == null)
            {
                keyItem = AddItem(config, key);
            }

            XmlElement colorItem = GetItem(keyItem, Enum.GetName(typeof(StyleType), colorType));
            if (colorItem == null)
            {
                colorItem = AddItem(keyItem, Enum.GetName(typeof(StyleType), colorType));
            }

            colorItem.InnerText = value.ToString();
            //return Save();
            return true;
        }

        private void splitType(ConfigType type, out DataType dataType, out StyleType styleType)
        {
            dataType = DataType.Event;
            styleType = StyleType.ForeColor;
            switch (type)
            {
                case ConfigType.EventForeColor:
                    dataType = DataType.Event;
                    styleType = StyleType.ForeColor;
                    break;
                case ConfigType.EventBackColor:
                    dataType = DataType.Event;
                    styleType = StyleType.BackColor;
                    break;
                case ConfigType.MessageForeColor:
                    dataType = DataType.Message;
                    styleType = StyleType.ForeColor;
                    break;
                case ConfigType.MessageBackColor:
                    dataType = DataType.Message;
                    styleType = StyleType.BackColor;
                    break;
                case ConfigType.EventFontBold:
                    dataType = DataType.Event;
                    styleType = StyleType.FontBold;
                    break;
                case ConfigType.MessageFontBold:
                    dataType = DataType.Message;
                    styleType = StyleType.FontBold;
                    break;
            }
        }

        public enum ConfigType
        {
            EventForeColor,
            EventBackColor,
            MessageForeColor,
            MessageBackColor,
            EventFontBold,
            MessageFontBold,
        }

        private enum DataType
        {
            Event,
            Message,
        }

        private enum StyleType
        {
            ForeColor,
            BackColor,
            FontBold,
        }
    }
}
