﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;

namespace MasterCom.RAMS.KPI_Statistics
{
    public class QueryKpiStatByFilesAsPair : QueryKpiStatByFiles
    {
        public override string Name
        {
            get { return "主被叫关联统计"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 11000, 11027, this.Name);
        }

        protected FileInfo curOtherFile = null;

        protected override void queryInThread(object o)
        {
            WaitBox.CanCancel = true;
            ClientProxy clientProxy = (ClientProxy)o;
            try
            {
                string imgTriadIDSet = getStatImgNeededTriadID();
                Dictionary<FileInfo, FileInfo> fileDic = new Dictionary<FileInfo, FileInfo>();
                Dictionary<int, bool> fileQueried = new Dictionary<int, bool>();
                WaitBox.Text = "正在进行被叫关联...";
                int index = 0;
                foreach (FileInfo file in condition.FileInfos)
                {
                    WaitBox.ProgressPercent = ++index * 100 / condition.FileInfos.Count;
                    if (file.DistrictID != clientProxy.DbID
                        || fileQueried.ContainsKey(file.ID))
                    {
                        continue;
                    }
                    fileQueried[file.ID] = true;

                    QueryOtherSideFile queryFile = new QueryOtherSideFile(file, clientProxy);
                    queryFile.Query();
                    FileInfo other = queryFile.OtherFile;
                    fileDic[file] = other;
                    if (other != null)
                    {
                        fileQueried[other.ID] = true;
                    }
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                }

                int idx = 1;
                foreach (FileInfo file in fileDic.Keys)
                {
                    curFile = file;
                    curOtherFile = fileDic[file];
                    WaitBox.Text = "(" + (idx++) + "/" + fileDic.Count + ")正在统计文件对[" + file.Name + "]...";
                    queryPeriodInfo(null, clientProxy, imgTriadIDSet, curFile);

                    if (curOtherFile != null && curOtherFile != curFile)
                    {
                        queryPeriodInfo(null, clientProxy, imgTriadIDSet, curOtherFile);
                    }
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                }
            }
            catch (Exception e)
            {
                MessageBox.Show("Error" + e.StackTrace + e.ToString());
            }
            finally
            {
                curFile = null;
                curOtherFile = null;
                WaitBox.Close();
            }
        }

        protected override void recieveAndHandleSpecificStatData(Net.Package package, List<Model.Interface.StatImgDefItem> curImgColumnDef, KPIStatDataBase singleStatData)
        {
            fillStatData(package, curImgColumnDef, singleStatData);
            int fileID = (int)singleStatData[KPIStatDataBase.FileIDKey, KPIStatDataBase.NewGridFileIDKey, -1];
            MasterCom.RAMS.Model.FileInfo fi = DTDataHeaderManager.GetInstance().GetHeaderByFileID(fileID);
            string pairName = curFile.Name;
            if (curOtherFile!=null)
            {
                pairName += ";" + curOtherFile.Name;
            }
            KpiDataManager.AddStatData(string.Empty, pairName, fi, singleStatData, false);
        }

        protected override void handleStatEvent(Model.Event evt)
        {
            StatDataEvent eventData = new StatDataEvent(evt, needSeparateByServiceID(evt), needSeparateByFileName(evt));
            FileInfo fi = DTDataHeaderManager.GetInstance().GetHeaderByFileID(evt.FileID);
            string pairName = curFile.Name;
            if (curOtherFile != null)
            {
                pairName += ";" + curOtherFile.Name;
            }
            KpiDataManager.AddStatData(string.Empty, pairName, fi, eventData, false);
        }

    }
}
