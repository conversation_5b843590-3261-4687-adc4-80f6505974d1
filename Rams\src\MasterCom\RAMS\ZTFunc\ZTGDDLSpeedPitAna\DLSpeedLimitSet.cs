﻿using MasterCom.RAMS.Func;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class DLSpeedLimitSet : BaseDialog
    {
        public DLSpeedLimitSet()
        {
            InitializeComponent();
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        public DLSeepLimitCond GetCond()
        {         
            DLSeepLimitCond limitCond = new DLSeepLimitCond();
            limitCond.DLastTime = (double)dLastTime.Value;
            limitCond.DSeepAvgMin = (double)dSpeedAvgMin.Value;
            limitCond.DSeepAvgMax = (double)dSpeedAvgMax.Value;
            limitCond.DVarianceMin = (double)dVarianceMin.Value;
            limitCond.DVarianceMax = (double)dVarianceMax.Value;
            limitCond.IsTpRegion = chkRegion.Checked;
            return limitCond;
        }
    }
}
