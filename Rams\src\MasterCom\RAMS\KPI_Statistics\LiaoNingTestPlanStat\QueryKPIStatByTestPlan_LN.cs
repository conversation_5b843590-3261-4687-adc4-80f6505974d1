﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.UserMng;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.KPI_Statistics
{
    public class QueryKPIStatByTestPlan_LN : QueryKPIStatBase
    {
        public QueryKPIStatByTestPlan_LN(MainModel mainModel)
            : base(mainModel) { }

        public override string Name
        {
            get { return "城市网格KPI统计(按测试计划)"; }
        }

        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }

        protected override LogInfoItem getRecLogItem()
        {
            return new LogInfoItem(2, 11000, 11062, this.Name);
        }

        protected override StatTbToken getTableNameToken()
        {
            return StatTbToken.log;
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MainModel.NeedSearchType.None;/*按不同方式查询（文件、小区，栅格）None控件可用*/
        }

        public override bool CanEnabled(Model.SearchGeometrys searchGeometrys)
        {
            return true;
        }

        Dictionary<string, List<string>> logName_CityGridDic = null;
        Dictionary<string, List<string>> district_LogNamesDic = null;
        string curDistrictName = string.Empty;

        private void queryTestPlanInfo()
        {
            QueryTestPlanInfo_LN queryLogAttribute = new QueryTestPlanInfo_LN(MainModel);
            queryLogAttribute.SetQueryCondition(condition);
            queryLogAttribute.Query();
            district_LogNamesDic = queryLogAttribute.City_Log_Dic;
            logName_CityGridDic = queryLogAttribute.Log_CityGrid_Dic;

            System.Threading.Thread.Sleep(500);
            WaitBox.Close();
        }
        protected override void query()
        {
            WaitBox.Show("正在查询测试计划信息...", queryTestPlanInfo);
            if (district_LogNamesDic.Count <= 0)
            {
                MessageBox.Show("未查询到测试计划相关信息！");
                return;
            }

            if (!getConditionBeforeQuery())
            {
                return;
            }
            DistrictManager disManager = DistrictManager.GetInstance();
            foreach (int districtID in condition.DistrictIDs)
            {
                curDistrictName = disManager.getDistrictName(districtID);
                if (!district_LogNamesDic.ContainsKey(curDistrictName))
                {
                    continue;
                }
                condition.DistrictID = districtID;
                queryDistrictData(districtID);
            }
            afterRecieveAllData();
            if (IsShowResultForm)
            {
                fireShowResult();
            }
        }

        protected override void queryInThread(object o)
        {
            System.Threading.Thread.Sleep(100);
            WaitBox.Text = "开始查询KPI统计数据...";
            WaitBox.CanCancel = true;
            ClientProxy clientProxy = (ClientProxy)o;
            try
            {
                string imgTriadIDSet = getStatImgNeededTriadID();

                int splitTokenNum = 0;
                condition.NameFilterType = FileFilterType.ByFileName;

                StringBuilder strbFileNameKey = new StringBuilder();
                List<string> fileNames = district_LogNamesDic[curDistrictName];
                foreach (string fileName in fileNames)
                {
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }

                    strbFileNameKey.Append(fileName + "{,}");
                    splitTokenNum++;
                    if (strbFileNameKey.Length > 1000)
                    {
                        queryStatInfoByFileNames(clientProxy, imgTriadIDSet, ref strbFileNameKey, ref splitTokenNum);
                    }
                }
                queryStatInfoByFileNames(clientProxy, imgTriadIDSet, ref strbFileNameKey, ref splitTokenNum);            
            }
            catch (Exception e)
            {
                MessageBox.Show("Error" + e.StackTrace + e.ToString());
            }
            finally
            {
                System.Threading.Thread.Sleep(100);
                WaitBox.Close();
            }
        }
        private void queryStatInfoByFileNames(ClientProxy clientProxy, string imgTriadIDSet
            , ref StringBuilder strbFileNameKey, ref int splitTokenNum)
        {
            if (strbFileNameKey.Length > 3)
            {
                condition.FileName = strbFileNameKey.Remove(strbFileNameKey.Length - 3, 3).ToString();
                condition.FileNameOrNum = splitTokenNum;
                foreach (TimePeriod period in condition.Periods)
                {
                    WaitBox.Text = "正在统计时段[" + period.ToString() + "]内的数据...";
                    WaitBox.ProgressPercent = 10;

                    queryPeriodInfo(period, clientProxy, imgTriadIDSet);
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                    GC.Collect();
                }
                strbFileNameKey = new StringBuilder();
                splitTokenNum = 0;
            }
        }
        protected override void preparePackageCommand(Package package)
        {
            if (isQueringEvent)
            {
                package.Command = Command.DIYSearch;
                package.SubCommand = SubCommand.Request;
                package.Content.Type = RequestType.REQTYPE_DIY_AREA_EVENT;
                package.Content.PrepareAddParam();
            }
            else
            {
                package.Command = Command.DIYSearch;
                package.SubCommand = SubCommand.Request;
                package.Content.Type = RequestType.REQTYPE_DIY_LOG_KPI;
                package.Content.PrepareAddParam();
            }
        }

        protected override void recieveAndHandleSpecificStatData(Net.Package package, List<Model.Interface.StatImgDefItem> curImgColumnDef, KPIStatDataBase singleStatData)
        {
            try
            {
                fillStatData(package, curImgColumnDef, singleStatData);/*满足统计数据*/
                int fileID = (int)singleStatData[KPIStatDataBase.FileIDKey, KPIStatDataBase.NewGridFileIDKey, -1];
                FileInfo fi = DTDataHeaderManager.GetInstance().GetHeaderByFileID(fileID);

                List<string> listCityGrid = getCityGrids(fi);
                foreach (string cityGrid in listCityGrid)
                {
                    bool save = false;
                    if (curReportStyle != null)
                    {
                        save = curReportStyle.HasGridPerCell;
                    }

                    KpiDataManager.AddStatData(string.Empty, cityGrid, fi, singleStatData, save);
                }
            }
            catch
            { 
                //continue
            }
        }

        protected override void handleStatEvent(Event evt)
        {
            try
            {
                StatDataEvent eventData = new StatDataEvent(evt, needSeparateByServiceID(evt), needSeparateByFileName(evt));
                FileInfo fi = DTDataHeaderManager.GetInstance().GetHeaderByFileID(evt.FileID);

                List<string> listCityGrid = getCityGrids(fi);
                foreach (string cityGrid in listCityGrid)
                {
                    bool save = false;
                    if (curReportStyle != null)
                    {
                        save = curReportStyle.HasGridPerCell;
                    }
                    KpiDataManager.AddStatData(string.Empty, cityGrid, fi, eventData, save);
                }
            }
            catch
            { 
                //continue
            }
        }

        private List<string> getCityGrids(FileInfo fi)
        {
            List<string> cityGrids = new List<string>();

            if (fi != null)
            {
                logName_CityGridDic.TryGetValue(fi.Name, out cityGrids);
            }
            return cityGrids;
        }
    }

}
