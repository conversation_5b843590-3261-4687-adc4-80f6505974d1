﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class MainRoadCellInfo : NOPCellInfo
    {
        public int ISN { get; set; }
        public string StrMainRoadName { get; set; }
        public string StrTestMonth { get; set; }
        public string StrTestDir { get; set; }
        public string StrNet { get; set; }
        public int IFileID { get; set; }
        public int ISampleNum { get; set; }
        public double DDistance { get; set; }
        public double DTestRoadDistance { get; set; } = 3000;

        public string StrTestRoadDistance
        {
            get
            {
                if (DTestRoadDistance == 3000)
                    return "";
                else
                    return Math.Round(DTestRoadDistance, 2).ToString();
            }
        }
        
        public string StrRoadPointLng { get; set; } = "";
        public string StrRoadPointLat { get; set; } = "";
        public List<string> CellNameList { get; set; } = new List<string>();
        public int ICellNum { get; set; }
        public List<string> BtsLogicList { get; set; } = new List<string>();
        public int IBTSLogicNum { get; set; }
        public List<string> BtsPhysiList { get; set; } = new List<string>();
        public int IBTSPhysiNum { get; set; }
        public int ITestNum { get; set; }
        public int ICoverNum { get; set; }
    }

    public class DiyMainRoaCellInfo : DIYSQLBase
    {
        string strCityName = "";
        string strMainRoad = "";
        string strMonth = "";
        string strFileID = "";
        public DiyMainRoaCellInfo(MainModel mainModel)
            : base(mainModel)
        {
        }

        public void SetCondition(string strMainRoad, string strCityName, string strMonth, List<FileInfo> fileList)
        {
            this.strMainRoad = strMainRoad;
            this.strCityName = strCityName;
            this.strMonth = strMonth;
            StringBuilder sb = new StringBuilder();
            foreach (FileInfo file in fileList)
            {
                sb.Append( file.ID + ",");
            }
            this.strFileID = sb.ToString().TrimEnd(',');
        }

        protected override string getSqlTextString()
        {
            string strSql = string.Format(" select ifileid, case when idir = 0 then '正向' else '反向' end as '方向',ilac,ici,isamplenum "
                          + " ,case when inet = 2 then 'GSM' when inet = 3 then 'TD' else 'LTE' end as '网络',fvalue1 "
                          + " from tb_auto_hsroad_cellinfo_{0} where ifileid in({1}) and inet !=3 ", this.strMonth, this.strFileID);
            return strSql;
        }

        public override string Name
        {
            get { return "DiyMainRoaCellInfo"; }
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[7];
            int index = 0;
            rType[index++] = E_VType.E_Int;
            rType[index++] = E_VType.E_String;
            rType[index++] = E_VType.E_Int;
            rType[index++] = E_VType.E_Int;
            rType[index++] = E_VType.E_Int;
            rType[index++] = E_VType.E_String;
            rType[index] = E_VType.E_Float;
            return rType;
        }

        public List<MainRoadCellInfo> MainRoadCellSetInfoList { get; set; } = new List<MainRoadCellInfo>();
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Dictionary<CityMainRoadCellKey, MainRoadCellInfo> mainRoadCellSetDic = new Dictionary<CityMainRoadCellKey, MainRoadCellInfo>();
            Package package = clientProxy.Package;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    try
                    {
                        MainRoadCellInfo cellInfo = new MainRoadCellInfo();
                        cellInfo.StrMainRoadName = this.strMainRoad;
                        cellInfo.StrCity = this.strCityName;
                        cellInfo.StrTestMonth = this.strMonth;
                        cellInfo.IFileID = package.Content.GetParamInt();
                        cellInfo.StrTestDir = package.Content.GetParamString();
                        cellInfo.ILAC = package.Content.GetParamInt();
                        cellInfo.ICI = package.Content.GetParamInt();
                        cellInfo.ISampleNum = package.Content.GetParamInt();
                        cellInfo.StrNet = package.Content.GetParamString();
                        cellInfo.DDistance = Math.Round(package.Content.GetParamFloat(), 2);

                        CityMainRoadCellKey cellKey = new CityMainRoadCellKey(this.strMainRoad, this.strCityName, this.strMonth, cellInfo.StrTestDir, cellInfo.StrNet);
                        cellKey.ILAC = cellInfo.ILAC;
                        cellKey.ICI = cellInfo.ICI;

                        if (!mainRoadCellSetDic.ContainsKey(cellKey))
                            mainRoadCellSetDic[cellKey] = cellInfo;
                        else
                        {
                            mainRoadCellSetDic[cellKey].ISampleNum += cellInfo.ISampleNum;
                            mainRoadCellSetDic[cellKey].DDistance += cellInfo.DDistance;
                        }
                    }
                    catch
                    {
                        //continue
                    }
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }

            MainRoadCellSetInfoList.Clear();
            MainRoadCellSetInfoList.AddRange(mainRoadCellSetDic.Values);
            mainRoadCellSetDic.Clear();
        }
    }

    public class NOPCellInfo
    {
        public string StrNetWork
        {
            get;
            set;
        }
        public string StrCity
        {
            get;
            set;
        }
        public string StrCGI
        {
            get;
            set;
        }
        public int ILAC
        {
            get;
            set;
        }
        public int ICI
        {
            get;
            set;
        }
        public string StrCellName
        {
            get;
            set;
        }
        public string StrOMC
        {
            get;
            set;
        }
        public string StrCellEngName
        {
            get;
            set;
        }
        public string StrENODEBID
        {
            get;
            set;
        }
        public string StrDirection
        {
            get;
            set;
        }
        public string StrDownward
        {
            get;
            set;
        }
        public string StrLongitude
        {
            get;
            set;
        }
        public string StrLatitude
        {
            get;
            set;
        }
        public string StrBTSLogicName
        {
            get;
            set;
        }
        public string StrBTSPhysiName
        {
            get;
            set;
        }
        public double DLongitude
        {
           get
            {
                double dLng;
                if (double.TryParse(StrLongitude, out dLng))
                {
                    return dLng;
                }
                return double.MinValue;
            }
        }
        public double DLatitude
        {
            get
            {
                double dLat;
                if (double.TryParse(StrLatitude, out dLat))
                {
                    return dLat;
                }
                return double.MinValue;
            }
        }
        public double Longitude
        {
            get;
            set;
        }
        public double Latitude
        {
            get;
            set;
        }
        public int iDirection
        {
            get;
            set;
        }
        public double DDist
        {
            get;
            set;
        }
        public int iDiffDirection
        {
            get;
            set;
        }
        public double DCellWeight
        {
            get;
            set;
        }
    }

    public class DiyNopCellInfo : DIYSQLBase
    {
        readonly string strCityName;
        public bool LteComplaint { get; set; } = false;
        public DiyNopCellInfo(MainModel mainModel, string strCityName)
            : base(mainModel)
        {
            this.strCityName = strCityName;
        }

        protected override string getSqlTextString()
        {
            if (!LteComplaint)
            {
                string strSql = string.Format(" SELECT [CGI],[LAC],[CI],'' as eNodeBID,名称,isnull(OMC小区名,'') as OMC小区名,convert(varchar,天线方向角) as 天线方向角 "
                              + " ,convert(varchar,天线总下倾角) as 天线总下倾角,convert(varchar,天线经度) as 天线经度,convert(varchar,天线纬度) as 天线纬度,[基站名称],[物理站名称] "
                              + " FROM NOPSERVER.[MTNOH_AAA_Resource].[dbo].[TB_当前_GSM小区] Where 地市 = '{0}' and [LAC] is not null ", this.strCityName);
                strSql += string.Format(" union all SELECT [ECGI],[TAC],CONVERT(VARCHAR,[ECI])ECI,convert(varchar,eNodeBID) as eNodeBID,名称,isnull(OMC小区名,'') as OMC小区名,convert(varchar,天线方向角) as 天线方向角"
                              + " ,convert(varchar,天线总下倾角) as 天线总下倾角,convert(varchar,天线经度) as 天线经度,convert(varchar,天线纬度) as 天线纬度,[基站名称],[物理站名称] "
                              + " FROM NOPSERVER.[MTNOH_AAA_Resource].[dbo].[TB_当前_LTE小区] Where 地市 = '{0}' and [TAC] is not null ", this.strCityName);
                return strSql;
            }
            else
            {
                return getAllCellInfo();
            }
        }

        private string getAllCellInfo()
        {
            string strSql = @"select 'LTE' as '网络制式',地市,名称,OMC小区名,ECGI,case when ISNUMERIC(replace(eNodeBID,'460-00-','')) = 1 then convert(int,ISNUMERIC(replace(eNodeBID,'460-00-',''))) else 0 end as LAC,CI,
	                           convert(int,case when 天线经度 is null or 天线纬度 is null then 基站经度 else 天线经度 end * 10000000) as 经度,
	                           convert(int,case when 天线经度 is null or 天线纬度 is null then 基站纬度 else 天线纬度 end * 10000000) as 纬度,
	                           convert(int,天线方向角) as 天线方向角 
                        from NOPSERVER.MTNOH_AAA_Resource.dbo.TB_当前_LTE小区
                        where 覆盖类别 = '室外' and 地市 is not null 
                        union all
                        select 'GSM' as '网络制式',地市,名称,OMC小区名,CGI,LAC,CI,
	                           convert(int,case when 天线经度 is null or 天线纬度 is null then 基站经度 else 天线经度 end * 10000000) as 经度,
	                           convert(int,case when 天线经度 is null or 天线纬度 is null then 基站纬度 else 天线纬度 end * 10000000) as 纬度,
	                           convert(int,天线方向角) as 天线方向角 
                        from NOPSERVER.MTNOH_AAA_Resource.dbo.TB_当前_GSM小区
                        where 覆盖类别 = '室外' and 地市 is not null 
                        union all
                        select 'TD' as '网络制式',地市,名称,OMC小区名,CGI,LAC,CI,
	                           convert(int,case when 天线经度 is null or 天线纬度 is null then 基站经度 else 天线经度 end * 10000000) as 经度,
	                           convert(int,case when 天线经度 is null or 天线纬度 is null then 基站纬度 else 天线纬度 end * 10000000) as 纬度,
	                           convert(int,天线方向角) as 天线方向角 
                        from NOPSERVER.MTNOH_AAA_Resource.dbo.TB_当前_TD小区
                        where 覆盖类别 = '室外' and 地市 is not null ";
            return strSql;
        }

        public override string Name
        {
            get { return "DiyNopCellInfo"; }
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[LteComplaint ? 10 : 12];
            int index = 0;
            rType[index++] = E_VType.E_String;
            rType[index++] = E_VType.E_String;
            rType[index++] = E_VType.E_String;
            rType[index++] = E_VType.E_String;
            rType[index++] = E_VType.E_String;
            if (!LteComplaint)
            {
                rType[index++] = E_VType.E_String;
                rType[index++] = E_VType.E_String;
                rType[index++] = E_VType.E_String;
                rType[index++] = E_VType.E_String;
                rType[index++] = E_VType.E_String;
                rType[index++] = E_VType.E_String;
                rType[index] = E_VType.E_String;
            } 
            else
            {
                rType[index++] = E_VType.E_Int;
                rType[index++] = E_VType.E_Int;
                rType[index++] = E_VType.E_Int;
                rType[index++] = E_VType.E_Int;
                rType[index] = E_VType.E_Int;
            }
            return rType;
        }

        public Dictionary<string, NOPCellInfo> cgiCellDic { get; set; } = new Dictionary<string, NOPCellInfo>();
        public Dictionary<string, Dictionary<string, List<NOPCellInfo>>> nopCellInfoDic { get; set; }
            = new Dictionary<string, Dictionary<string, List<NOPCellInfo>>>();
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            cgiCellDic.Clear();
            nopCellInfoDic.Clear();
            Package package = clientProxy.Package;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    fillData(package);
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }

        private void fillData(Package package)
        {
            try
            {
                if (!LteComplaint)
                {
                    NOPCellInfo cellInfo = new NOPCellInfo();
                    cellInfo.StrCity = this.strCityName;
                    cellInfo.StrCGI = package.Content.GetParamString();
                    cellInfo.ILAC = Convert.ToInt32(package.Content.GetParamString());
                    cellInfo.ICI = Convert.ToInt32(package.Content.GetParamString());
                    cellInfo.StrENODEBID = package.Content.GetParamString();
                    cellInfo.StrCellName = package.Content.GetParamString();
                    cellInfo.StrCellEngName = package.Content.GetParamString();
                    cellInfo.StrDirection = package.Content.GetParamString();
                    cellInfo.StrDownward = package.Content.GetParamString();
                    cellInfo.StrLongitude = package.Content.GetParamString();
                    cellInfo.StrLatitude = package.Content.GetParamString();
                    cellInfo.StrBTSLogicName = package.Content.GetParamString();
                    cellInfo.StrBTSPhysiName = package.Content.GetParamString();

                    string strLac_Ci = cellInfo.ILAC + "_" + cellInfo.ICI;
                    if (!cgiCellDic.ContainsKey(strLac_Ci))
                    {
                        cgiCellDic[strLac_Ci] = cellInfo;
                    }
                }
                else
                {
                    NOPCellInfo cellInfo = new NOPCellInfo();
                    cellInfo.StrNetWork = package.Content.GetParamString();
                    cellInfo.StrCity = package.Content.GetParamString();
                    cellInfo.StrCellName = package.Content.GetParamString();
                    cellInfo.StrOMC = package.Content.GetParamString();
                    cellInfo.StrCGI = package.Content.GetParamString();
                    cellInfo.ILAC = package.Content.GetParamInt();
                    cellInfo.ICI = package.Content.GetParamInt();
                    cellInfo.Longitude = package.Content.GetParamInt() / 10000000.0;
                    cellInfo.Latitude = package.Content.GetParamInt() / 10000000.0;
                    cellInfo.iDirection = package.Content.GetParamInt();
                    Dictionary<string, List<NOPCellInfo>> tmpDic;
                    List<NOPCellInfo> tmpList;
                    if (!nopCellInfoDic.TryGetValue(cellInfo.StrCity, out tmpDic))
                    {
                        tmpDic = new Dictionary<string, List<NOPCellInfo>>();
                    }
                    if (!tmpDic.TryGetValue(cellInfo.StrNetWork, out tmpList))
                    {
                        tmpList = new List<NOPCellInfo>();
                    }
                    tmpList.Add(cellInfo);
                    tmpDic[cellInfo.StrNetWork] = tmpList;
                    nopCellInfoDic[cellInfo.StrCity] = tmpDic;
                }
            }
            catch
            {
                //continue
            }
        }
    }

    public class CityMainRoadCellKey
    {
        public string StrMainRoad { get; set; } = "";
        public string StrCity { get; set; }
        public string StrTestMonth { get; set; } = "";
        public string StrTestDir { get; set; } = "";
        public string StrNet { get; set; } = "";
        public int ILAC { get; set; }
        public int ICI { get; set; }

        public override bool Equals(object obj)
        {
            CityMainRoadCellKey other = obj as CityMainRoadCellKey;
            if (other == null)
                return false;

            if (!base.GetType().Equals(obj.GetType()))
                return false;

            return (this.StrMainRoad.Equals(other.StrMainRoad) && this.StrCity.Equals(other.StrCity)
                  && this.StrTestMonth.Equals(other.StrTestMonth) && this.StrTestDir.Equals(other.StrTestDir)
                  && this.StrNet.Equals(other.StrNet) && this.ILAC.Equals(other.ILAC) && this.ICI.Equals(other.ICI));
        }

        public override int GetHashCode()
        {
            return (this.StrMainRoad + this.StrCity + this.StrTestMonth
                    + this.StrTestDir + this.StrNet).GetHashCode();
        }

        public CityMainRoadCellKey(string MainRoadName, string City, string TestMonth, string TestDir, string Net)
        {
            this.StrMainRoad = MainRoadName;
            this.StrCity = City;
            this.StrTestMonth = TestMonth;
            this.StrTestDir = TestDir;
            this.StrNet = Net;
        }
        public CityMainRoadCellKey()
        {

        }
    }

    public class DiyMainRoadPoint : DIYSQLBase
    {
        public DiyMainRoadPoint(MainModel mainModel)
            : base(mainModel)
        {
        }

        protected override string getSqlTextString()
        {
            return " select strroad,convert(varchar,ilongitude / 10000000.0) as strlng,convert(varchar,ilatitude / 10000000.0) as strlat "
                                 + " from tb_auto_gridroad_info6 order by strroad,isampleid "; 
        }

        public override string Name
        {
            get { return "DiyMainRoadSerialize"; }
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[3];
            int index = 0;
            rType[index++] = E_VType.E_String;
            rType[index++] = E_VType.E_String;
            rType[index] = E_VType.E_String;
            return rType;
        }

        public Dictionary<string, List<MainRoadCellInfo>> mainRoadPointInfoDic { get; set; } = new Dictionary<string, List<MainRoadCellInfo>>();
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            mainRoadPointInfoDic.Clear();
            Package package = clientProxy.Package;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    try
                    {
                        MainRoadCellInfo roadPointInfo = new MainRoadCellInfo();
                        roadPointInfo.StrMainRoadName = package.Content.GetParamString();
                        roadPointInfo.StrLongitude = package.Content.GetParamString();
                        roadPointInfo.StrLatitude = package.Content.GetParamString();

                        if (!mainRoadPointInfoDic.ContainsKey(roadPointInfo.StrMainRoadName))
                            mainRoadPointInfoDic[roadPointInfo.StrMainRoadName] = new List<MainRoadCellInfo>() { roadPointInfo };
                        else
                            mainRoadPointInfoDic[roadPointInfo.StrMainRoadName].Add(roadPointInfo);
                    }
                    catch
                    {
                        //continue
                    }
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }
    }
}
