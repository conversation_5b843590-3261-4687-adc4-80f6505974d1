﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using System.IO;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause
{
    public partial class ConditionDlg : BaseDialog
    {


        public ConditionDlg()
        {
            InitializeComponent();
        }

        private FunctionCondition cond = null;
        public FunctionCondition Condition
        {
            get { return cond; }
        }
        public ConditionDlg(FunctionCondition cond)
            : this()
        {
            /*
             * （第一次打开该窗口时加载xml文件保存的内容，没有文件则创建。
             * 点击确定时进行保存，若第一次打开后不是点击确定进行保存，则再次打开该窗口时会重新读取文件保存的内容。
             * 之后打开该窗口不会从文件读取。）
             */
            if (cond==null)
            {
                cond = new FunctionCondition();
                cond.LoadCfg();
            }
            this.cond = cond;
            init(cond);
        }

        private void init(FunctionCondition cond)
        {
            numFTPMax.Value = (decimal)cond.FTPRateMax;
            numFTPMax.ValueChanged += numSpeedMax_ValueChanged;
            chkFTP.Checked = cond.CheckFTP;
            chkFTP.CheckedChanged += chkFTP_CheckedChanged;

            numHTTPMax.Value = (decimal)cond.HTTPRateMax;
            numHTTPMax.ValueChanged += numHTTPMax_ValueChanged;
            chkHttp.Checked = cond.CheckHTTP;
            chkHttp.CheckedChanged += chkHttp_CheckedChanged;

            numEmailMax.Value = (decimal)cond.EmailRateMax;
            numEmailMax.ValueChanged += numEmailMax_ValueChanged;
            chkEmail.Checked = cond.CheckEmail;
            chkEmail.CheckedChanged += chkEmail_CheckedChanged;

            numSampleCnt.Value = (decimal)cond.TestPointCountMin;
            numSampleCnt.ValueChanged += numSampleCnt_ValueChanged;

            numDistance.Value = (decimal)cond.DistanceMin;
            numDistance.ValueChanged += numDistance_ValueChanged;

            numSecond.Value = (decimal)cond.SecondMin;
            numSecond.ValueChanged += numSecond_ValueChanged;

            initNaviTree(cond);

            coverPnl1.LinkCondition(cond);
            qualPnl.LinkCondition(cond);
            hoUpdatePnl.LinkCondition(cond);
        }


        void numSecond_ValueChanged(object sender, EventArgs e)
        {
            cond.SecondMin = (double)numSecond.Value;
        }

        void numDistance_ValueChanged(object sender, EventArgs e)
        {
            cond.DistanceMin = (double)numDistance.Value;
        }

        void chkEmail_CheckedChanged(object sender, EventArgs e)
        {
            cond.CheckEmail = chkEmail.Checked;
        }

        void numEmailMax_ValueChanged(object sender, EventArgs e)
        {
            cond.EmailRateMax = (double)numEmailMax.Value;
        }

        void chkHttp_CheckedChanged(object sender, EventArgs e)
        {
            cond.CheckHTTP = chkHttp.Checked;
        }

        void numHTTPMax_ValueChanged(object sender, EventArgs e)
        {
            cond.HTTPRateMax = (double)numHTTPMax.Value;
        }

        void chkFTP_CheckedChanged(object sender, EventArgs e)
        {
            cond.CheckFTP = chkFTP.Checked;
        }

        void numSampleCnt_ValueChanged(object sender, EventArgs e)
        {
            cond.TestPointCountMin = (int)numSampleCnt.Value;
        }

        void numSpeedMax_ValueChanged(object sender, EventArgs e)
        {
            cond.FTPRateMax = (float)numFTPMax.Value;
        }

        private void initNaviTree(FunctionCondition cond)
        {
            tvReason.Nodes.Clear();
            foreach (CauseBase r in cond.Causes)
            {
                TreeNode node = createNode(r);
                if (node != null)
                {
                    tvReason.Nodes.Add(node);
                }
            }
        }

        private TreeNode createNode(CauseBase r)
        {
            if (r == null)
            {
                return null;
            }
            TreeNode node = new TreeNode(r.Name);
            node.Tag = r;
            if (r.SubCauses != null)
            {
                foreach (CauseBase subR in r.SubCauses)
                {
                    TreeNode subNode = createNode(subR);
                    if (subNode != null)
                    {
                        node.Nodes.Add(subNode);
                    }
                }
            }
            return node;
        }

        private void tvReason_AfterSelect(object sender, TreeViewEventArgs e)
        {
            checkBtnStatus();
        }

        private void checkBtnStatus()
        {
            TreeNode node = tvReason.SelectedNode;
            if (node == null || node.Tag == null)
            {
                btnUp.Enabled = btnDown.Enabled = false;
            }
            else
            {
                btnUp.Enabled = node.PrevNode != null;
                btnDown.Enabled = node.NextNode != null;
            }
        }

        private void btnUp_Click(object sender, EventArgs e)
        {
            moveNode(tvReason.SelectedNode, true);
        }

        private void btnDown_Click(object sender, EventArgs e)
        {
            moveNode(tvReason.SelectedNode, false);
        }

        private void moveNode(TreeNode node, bool isUp)
        {
            if (node == null)
            {
                MessageBox.Show("请选择需要调整位置的节点");
                return;
            }
            else if (node.Tag == null)
            {
                MessageBox.Show("该节点不支持调整位置");
                return;
            }

            int newIdx = isUp ? node.PrevNode.Index : node.NextNode.Index;
            TreeNodeCollection parent = node.Parent != null ? node.Parent.Nodes : tvReason.Nodes;
            node.Remove();
            parent.Insert(newIdx, node);
            tvReason.SelectedNode = node;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            cond.Causes.Clear();
            foreach (TreeNode node in tvReason.Nodes)
            {
                cond.Causes.Add(getCauseBase(node));
            }
            DialogResult = DialogResult.OK;
            cond.Save();
        }

        private CauseBase getCauseBase(TreeNode node)
        {
            CauseBase cause = node.Tag as CauseBase;
            if (cause.SubCauses!=null)
            {
                cause.SubCauses.Clear();
            }
            foreach (TreeNode subNode in node.Nodes)
            {
                cause.SubCauses.Add(getCauseBase(subNode));
            }
            return cause;
        }

        
    }
}
