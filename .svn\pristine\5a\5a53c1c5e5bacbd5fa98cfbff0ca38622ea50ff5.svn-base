﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public class QueryCoverageDetailInfoByRegion : DIYSampleByRegion
    {
        public CoverageAreaInfo CovAreaInfo { get; set; }

        public QueryCoverageDetailInfoByRegion(MainModel mainModel, string btsName, double longitude, double latitude)
            : base(mainModel)
        {
            isAddSampleToDTDataManager = false;
            CovAreaInfo = new CoverageAreaInfo(btsName, longitude, latitude);
        }

        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            List<object> columnsDef = new List<object>();
            Dictionary<string, object> param = new Dictionary<string, object>();
            param["param_name"] = "RxLevSub";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "RxQualSub";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "TD_PCCPCH_RSCP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "TD_GSM_RxQualSub";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "W_Reference_RSCP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "CD_RxAGC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            Dictionary<string, object> tmpDic = new Dictionary<string, object>();
            tmpDic.Add("name", (object)">GSM & TD");
            tmpDic.Add("themeName", (object)"GSM RxLevSub");//TD_PCCPCH_RSCP
            tmpDic.Add("columnsDef", (object)columnsDef);

            DIYSampleGroup group = new DIYSampleGroup();
            group.Param = tmpDic;
            return group;
        }

        protected override void doWithDTData(MasterCom.RAMS.Model.TestPoint tp)
        {
            if (tp is TestPointDetail)
            {
                short? rxlev = (short?)tp["RxLevSub", 0];
                int? rxqual = (int?)(byte?)tp["RxQualSub", 0];
                if (judgeValid(rxlev, -140, -10)) return;
                CovAreaInfo.CoverageStat(tp, (float)rxlev, rxqual, ServiceType.GSM_VOICE);
            }
            else if (tp is TDTestPointDetail)
            {
                float? rscp = (float?)tp["TD_PCCPCH_RSCP", 0];
                byte? rxqual = (byte?)tp["TD_GSM_RxQualSub", 0];
                if (judgeValid(rscp, -140, -10)) return;
                CovAreaInfo.CoverageStat(tp, (float)rscp, rxqual, ServiceType.TDSCDMA_VOICE);
            }
            else if (tp is WCDMATestPointDetail)
            {
                float? rscp = (float?)tp["W_Reference_RSCP"];
                if (judgeValid(rscp, -140, -10)) return;
                CovAreaInfo.CoverageStat(tp, (float)rscp, null, ServiceType.WCDMA_VOICE);
            }
            else if (tp is CDMATestPointDetail)
            {
                float? rxagc = (float?)tp["CD_RxAGC"];
                if (judgeValid(rxagc, -120, 10))
                    CovAreaInfo.CoverageStat(tp, (float)rxagc, null, ServiceType.CDMA_VOICE);
            }
        }

        private bool judgeValid(float? value, float min, float max)
        {
            if (value == null || value < min || value > max)
            {
                return false;
            }
            return true;
        }
    }

    public class CoverageAreaInfo
    {
        public string BtsName { get; set; }
        public double Longitude { get; set; }
        public double Latitude { get; set; }

        public int Gsm_95 { get; set; }
        public int Gsm_Rxqual { get; set; }
        public int GsmSampleNum { get; set; }
        public string GSMRxqual
        {
            get
            {
                if (GsmSampleNum > 0)
                {
                    return Math.Round(Gsm_Rxqual * 1.0 / GsmSampleNum, 2).ToString();
                }
                return "-";
            }
        }
        public string GSMCoverage_95
        {
            get
            {
                if (GsmSampleNum > 0)
                {
                    return (Gsm_95 * 1.0 / GsmSampleNum).ToString("p2");
                }
                return "-";
            }
        }

        public int TD_95 { get; set; }
        public int TD_Rxqual { get; set; }
        public int TDSampleNum { get; set; }
        public string TDRxqual
        {
            get
            {
                if (TDSampleNum > 0)
                {
                    return Math.Round(TD_Rxqual * 1.0 / TDSampleNum, 2).ToString();
                }
                return "-";
            }
        }
        public string TDCoverage_95
        {
            get
            {
                if (TDSampleNum > 0)
                {
                    return (TD_95 * 1.0 / TDSampleNum).ToString("p2");
                }
                return "-";
            }
        }

        public int Gsm_95_LT { get; set; }
        public int GsmSampleNum_LT { get; set; }
        public string GSMCoverage_95_LT
        {
            get
            {
                if (GsmSampleNum_LT > 0)
                {
                    return (Gsm_95_LT * 1.0 / GsmSampleNum_LT).ToString("p2");
                }
                return "-";
            }
        }

        public int W_95 { get; set; }
        public int WSampleNum { get; set; }
        public string WCoverage_95
        {
            get
            {
                if (WSampleNum > 0)
                {
                    return (W_95 * 1.0 / WSampleNum).ToString("p2");
                }
                return "-";
            }
        }

        public int CdmaRxagc_90 { get; set; }
        public int CdmaSampleNum { get; set; }
        public string CdmaCoverage_90
        {
            get
            {
                if (CdmaSampleNum > 0)
                {
                    return (CdmaRxagc_90 * 1.0 / CdmaSampleNum).ToString("p2");
                }
                return "-";
            }
        }

        public List<TestPoint> TpList { get; set; }

        public CoverageAreaInfo(string btsName, double longitude, double latitude)
        {
            this.BtsName = btsName;
            this.Longitude = longitude;
            this.Latitude = latitude;
            Gsm_95 = 0;
            Gsm_Rxqual = 0;
            GsmSampleNum = 0;
            TD_95 = 0;
            TD_Rxqual = 0;
            TDSampleNum = 0;
            Gsm_95_LT = 0;
            GsmSampleNum_LT = 0;
            W_95 = 0;
            WSampleNum = 0;
            CdmaRxagc_90 = 0;
            CdmaSampleNum = 0;
            TpList = new List<TestPoint>();
        }

        public void CoverageStat(TestPoint tp, float rxlev, int? rxqual, ServiceType service)
        {
            switch (service)
            {
                case ServiceType.GSM_VOICE:
                    addGsmTP(tp, rxlev, rxqual);
                    break;
                case ServiceType.TDSCDMA_VOICE:
                    TDSampleNum++;
                    if (rxqual != null && rxqual >= 0 && rxqual <= 7)
                    {
                        TD_Rxqual += (int)rxqual;
                    }
                    if (rxlev >= -95)
                    {
                        TD_95++;
                        TpList.Add(tp);
                    }
                    break;
                case ServiceType.WCDMA_VOICE:
                    WSampleNum++;
                    if (rxlev >= -95)
                    {
                        W_95++;
                        TpList.Add(tp);
                    }
                    break;
                case ServiceType.CDMA_VOICE:
                    CdmaSampleNum++;
                    if (rxlev >= -90)
                    {
                        CdmaRxagc_90++;
                        TpList.Add(tp);
                    }
                    break;
                default:
                    break;
            }
        }

        private void addGsmTP(TestPoint tp, float rxlev, int? rxqual)
        {
            switch (tp.CarrierType)
            {
                case (int)CarrierType.ChinaMobile:
                    GsmSampleNum++;
                    if (rxqual != null && rxqual >= 0 && rxqual <= 7)
                    {
                        Gsm_Rxqual += (int)rxqual;
                    }
                    if (rxlev >= -95)
                    {
                        Gsm_95++;
                        TpList.Add(tp);
                    }
                    break;
                case (int)CarrierType.ChinaUnicom:
                    GsmSampleNum_LT++;
                    if (rxlev >= -95)
                    {
                        Gsm_95_LT++;
                        TpList.Add(tp);
                    }
                    break;
            }
        }
    }
}
