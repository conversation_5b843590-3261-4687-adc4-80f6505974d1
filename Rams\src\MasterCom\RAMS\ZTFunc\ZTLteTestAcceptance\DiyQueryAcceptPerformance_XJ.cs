﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class DiyQueryAcceptPerformance_XJ : DIYSQLBase
    {
        public DiyQueryAcceptPerformance_XJ(int eci, FddDatabaseSetting setting)
            : base()
        {
            MainDB = true;
            this.setting = setting;
            this.eci = eci;
            Result = new AcceptPerformanceInfo();
        }

        #region 基础数据重写
        public override string Name
        {
            get { return "查询新疆单验性能测试数据"; }
        }

        public override string IconName
        {
            get { return string.Empty; }
        }
        #endregion

        readonly FddDatabaseSetting setting;
        readonly int eci;
        public AcceptPerformanceInfo Result { get; private set; }

        protected override string getSqlTextString()
        {
            string spName = string.Format(@"{0}.{1}.[dbo].[{2}]", setting.ServerIp, setting.DbName, setting.TableNameHead);
            string strSQL = string.Format(@"EXEC {0} {1}", spName, eci);
            return strSQL;
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[9];
            rType[0] = E_VType.E_String;
            rType[1] = E_VType.E_Int;
            rType[2] = E_VType.E_Int;
            rType[3] = E_VType.E_Float;
            rType[4] = E_VType.E_Float;
            rType[5] = E_VType.E_Float;
            rType[6] = E_VType.E_Float;
            rType[7] = E_VType.E_Float;
            rType[8] = E_VType.E_Float;
            return rType;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;

            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    AcceptPerformanceInfo acceptInfo = new AcceptPerformanceInfo();
                    acceptInfo.Fill(package);
                    Result = acceptInfo;
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error(Name + " Unexpected type: " + package.Content.Type);
                    break;
                }
                setProgressPercent(ref index, ref progress);
            }
        }
    }

    public class AcceptPerformanceInfo
    {
        public string ECI { get; private set; }
        public int CsfbRequestCnt { get; private set; }
        public int CsfbSuccessCnt { get; private set; }
        public double CsfbSuccessRate { get; set; }
        public double CoverRate { get; private set; }
        public double Rsrp { get; private set; }
        public double Sinr { get; private set; }
        public double DLThroughput { get; set; }
        public double ULThroughput { get; set; }

        public void Fill(Package package)
        {
            ECI = package.Content.GetParamString();
            CsfbRequestCnt = package.Content.GetParamInt();
            CsfbSuccessCnt = package.Content.GetParamInt();
            CsfbSuccessRate = package.Content.GetParamFloat();
            CoverRate = package.Content.GetParamFloat();
            Rsrp = package.Content.GetParamFloat();
            Sinr = package.Content.GetParamFloat();
            DLThroughput = package.Content.GetParamFloat();
            ULThroughput = package.Content.GetParamFloat();
        }
    }
}
