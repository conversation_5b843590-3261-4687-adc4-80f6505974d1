﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Stat;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Stat.Data;
using MasterCom.RAMS.ZTQuery;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using MasterCom.MTGis;

namespace MasterCom.RAMS.Net
{
    public static partial class RequestType
    {
        public const byte REQTYPE_STREET_INJECT = 0xd3;
    }
    public static partial class ResponseType
    {
        public const byte RESTYPE_STREET_INJECT = 0xd3;
    }

    public class InjectionGridQuryDetail:QueryBase
    {
        public InjectionGridQuryDetail(MainModel mainModel)
            : base(mainModel)
        { }

        public override string Name
        {
            get { return "道路测试渗透率查询(按预存区域按运营商)"; }
        }

        public override string IconName
        {
            get { return null; }
        }

        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 18000, 18008, this.Name);
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            if (searchGeometrys.SelectedResvRegions == null || searchGeometrys.SelectedResvRegions.Count <= 0)
            {
                return false;
            }
            return true;
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MainModel.NeedSearchType.ResvRegion;
        }

        protected override bool isValidCondition()
        {
            return MainModel.SearchGeometrys.SelectedResvRegions != null && MainModel.SearchGeometrys.SelectedResvRegions.Count > 0;
        }

        protected override void query()
        {
            List<AreaStreetCarrierInjectInfo> asciInfos = new List<AreaStreetCarrierInjectInfo>();
            MainModel.TotalStreetInjInfoResultList = new List<StreetInjectInfo>();
            foreach (ResvRegion region in MainModel.SearchGeometrys.SelectedResvRegions)
            {
                List<StreetInjectInfo> list = new List<StreetInjectInfo>();

                foreach (int carrierID in condition.CarrierTypes)
                {
                    DIYInjectionGridQuery query = new DIYInjectionGridQuery(MainModel, carrierID, region);
                    query.SetQueryCondition(this.condition);
                    query.Query();

                    foreach (StreetInjectInfo siInfo in query.carrierAreaInjectionInfo.TotalInfoResultList)
                    {
                        if (siInfo.StreetName.Contains("未命名"))
                        {
                            DbRect bnds = MapOperation.GetShapeBounds(siInfo.streetCurv);
                            siInfo.StreetName = "未命名_" + (UInt32)(bnds.x1 * 10000000 * 37
                            + bnds.y1 * 10000000 * 37 * 37
                            + bnds.x2 * 10000000 * 41
                            + bnds.y1 * 10000000 * 41 * 41
                            + ((UInt32)(siInfo.distCovered + siInfo.distUnCovered) / 100) * 43 * 43);
                        }

                        StreetInjectInfo infoClone = siInfo.Clone() as StreetInjectInfo;
                        list.Add(infoClone);
                        MainModel.TotalStreetInjInfoResultList.Add(infoClone);
                    }
                }

                addAsciInfos(asciInfos, region, list);
            }
            MainModel.AreaStreetCarrierInjectInfos = asciInfos;
            MainModel.FireStreetInjectDetailQueried(this);
        }

        private void addAsciInfos(List<AreaStreetCarrierInjectInfo> asciInfos, ResvRegion region, List<StreetInjectInfo> list)
        {
            AreaStreetCarrierInjectInfo info = new AreaStreetCarrierInjectInfo();
            info.areaName = region.RegionName;
            Dictionary<string, StreetCarrierInjectInfo> dict = new Dictionary<string, StreetCarrierInjectInfo>();
            foreach (StreetInjectInfo siInfo in list)
            {
                if (dict.ContainsKey(siInfo.StreetName))
                {
                    addStreetInjectInfo(dict, siInfo);
                }
                else
                {
                    addStreetCarrierInjectInfo(info, dict, siInfo);
                }
            }
            asciInfos.Add(info);
        }

        private void addStreetInjectInfo(Dictionary<string, StreetCarrierInjectInfo> dict, StreetInjectInfo siInfo)
        {
            StreetCarrierInjectInfo tempInfo = dict[siInfo.StreetName];

            if (siInfo.carrierID == 1)
            {
                if (tempInfo.cmccStreetInjectInfo.StreetName == "")
                {
                    tempInfo.cmccStreetInjectInfo = siInfo;
                }
                else
                {
                    tempInfo.cmccStreetInjectInfo.AddData(siInfo);
                }
            }
            else if (siInfo.carrierID == 2)
            {
                if (tempInfo.cuStreetInjectInfo.StreetName == "")
                {
                    tempInfo.cuStreetInjectInfo = siInfo;
                }
                else
                {
                    tempInfo.cuStreetInjectInfo.AddData(siInfo);
                }
            }
            else if (siInfo.carrierID == 3)
            {
                if (tempInfo.ctStreetInjectInfo.StreetName == "")
                {
                    tempInfo.ctStreetInjectInfo = siInfo;
                }
                else
                {
                    tempInfo.ctStreetInjectInfo.AddData(siInfo);
                }
            }
        }

        private void addStreetCarrierInjectInfo(AreaStreetCarrierInjectInfo info, Dictionary<string, StreetCarrierInjectInfo> dict, StreetInjectInfo siInfo)
        {
            StreetCarrierInjectInfo tempInfo = new StreetCarrierInjectInfo();
            tempInfo.StreetName = siInfo.StreetName;
            if (siInfo.carrierID == 1)
            {
                tempInfo.cmccStreetInjectInfo = siInfo;
            }
            else if (siInfo.carrierID == 2)
            {
                tempInfo.cuStreetInjectInfo = siInfo;
            }
            else if (siInfo.carrierID == 3)
            {
                tempInfo.ctStreetInjectInfo = siInfo;
            }
            info.streetCarrierInjectInfos.Add(tempInfo);
            dict.Add(siInfo.StreetName, tempInfo);
        }
    }

}
