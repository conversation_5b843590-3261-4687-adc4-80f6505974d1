﻿using System;
using System.Collections.Generic;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTCQTCellSetAnaBase : DIYAnalyseFilesOneByOneByRegion
    {
        public Dictionary<string, ZTCQTCellSetAnaCityItem> resultDic { get; set; } = new Dictionary<string, ZTCQTCellSetAnaCityItem>();    //保存结果
        public Dictionary<string, string> areaCoverTypeDic { get; set; } = new Dictionary<string, string>();
        public Dictionary<string, string> areaNameDic { get; set; } = new Dictionary<string, string>();

        public ZTCQTCellSetAnaBase(MainModel mainModel)
            : base(mainModel)
        {
            this.IncludeEvent = false;
            this.IncludeMessage = false;
        } 

        protected override void clearDataBeforeAnalyseFiles()
        {
            resultDic = new Dictionary<string, ZTCQTCellSetAnaCityItem>();

            getAreaCoverDic();
            getAreaNameDic();
        }

        private void getAreaCoverDic()
        {
            if (areaNameDic.Count > 0)  //已经获取过
            {
                return;
            }

            areaCoverTypeDic = new Dictionary<string, string>();

            ZTCQTCellSetAnaDIYSqlAreaCover diySqlQuery = new ZTCQTCellSetAnaDIYSqlAreaCover(MainModel, MainModel.DistrictID);
            diySqlQuery.Query();
            areaCoverTypeDic = diySqlQuery.GetAreaCoverTypeDic();
        }

        /// <summary>
        /// 由于广东在初始化时，没有读取全部的arealist信息，需要单独获取区域名称进行匹配
        /// </summary>
        private void getAreaNameDic()
        {
            if (areaNameDic.Count > 0)  //已经获取过
            {
                return;
            }

            areaNameDic = new Dictionary<string, string>();

            ZTCQTCellSetAnaDIYSqlAreaName diySqlQuery = new ZTCQTCellSetAnaDIYSqlAreaName(MainModel, MainModel.DistrictID);
            diySqlQuery.Query();
            areaNameDic = diySqlQuery.GetAreaNameDic();
        }

        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager fileMng in MainModel.DTDataManager.FileDataManagers)
            {
                string strCityInfo = DistrictManager.GetInstance().getDistrictName(MainModel.DistrictID);

                ZTCQTCellSetAnaCityItem cityItem = null;
                if (!resultDic.TryGetValue(strCityInfo, out cityItem))
                {
                    cityItem = new ZTCQTCellSetAnaCityItem(strCityInfo);
                    cityItem.SN = resultDic.Count + 1;
                    resultDic.Add(strCityInfo, cityItem);
                }

                string strAreaInfo = fileMng.GetFileInfo().AreaTypeID.ToString() + "|" + fileMng.GetFileInfo().AreaID.ToString();
                ZTCQTCellSetAnaAreaItem areaItem = null;
                if (!cityItem.AreaDic.TryGetValue(strAreaInfo, out areaItem))
                {
                    string strAreaCover = "未知";

                    if (areaCoverTypeDic.ContainsKey(strAreaInfo))
                    {
                        strAreaCover = areaCoverTypeDic[strAreaInfo];
                    }

                    string strAreaName = "未匹配";
                    if (areaNameDic.ContainsKey(strAreaInfo))
                    {
                        strAreaName = areaNameDic[strAreaInfo];
                    }
                    areaItem = new ZTCQTCellSetAnaAreaItem(strAreaCover, fileMng.GetFileInfo().AreaTypeString, strAreaName);
                    areaItem.SN = cityItem.AreaDic.Count + 1;
                    cityItem.AreaDic.Add(strAreaInfo, areaItem);
                }

                dealTP(fileMng, areaItem);
            }
        }

        private void dealTP(DTFileDataManager fileMng, ZTCQTCellSetAnaAreaItem areaItem)
        {
            foreach (TestPoint tp in fileMng.TestPoints)
            {
                if (tp is TestPointDetail)
                {
                    doGSMCell(tp, ref areaItem);
                }
                else if (tp is TDTestPointDetail)
                {
                    doTDCell(tp, ref areaItem);
                }
                else if (tp is LTETestPointDetail)
                {
                    doTDDLTECell(tp, ref areaItem);
                }
            }
        }

        private void doGSMCell(TestPoint tp, ref ZTCQTCellSetAnaAreaItem areaItem)
        {
            int? lac = (int?)tp["LAC"];
            int? ci = (int?)tp["CI"];
            short? rxlev = (short?)tp["RxLevSub"];

            if (lac == null || ci == null || rxlev == null)     
            {
                return;
            }

            string cellName = "";
            string cellType = "未知";
            Cell tpCell = tp.GetMainCell_GSM();
            if (tpCell != null)
            {
                cellName = tpCell.Name;

                if (tpCell.Type == BTSType.Indoor)
                {
                    areaItem.SampleIndoor++;
                    cellType = "室分";
                }
                else
                {
                    areaItem.SampleOutdoor++;
                    cellType = "非室分";
                }
            }
            else
            {
                cellName = ((int)lac).ToString() + "_" + ((int)ci).ToString();

                areaItem.SampleUnknown++;
            }

            ZTCQTCellSetAnaCellItem cellItem = null;
            if (!areaItem.CellDic.TryGetValue(cellName, out cellItem))
            {
                cellItem = new ZTCQTCellSetAnaCellItem(cellName, cellType, tpCell);
                cellItem.SN = areaItem.CellDic.Count + 1;
                areaItem.CellDic.Add(cellName, cellItem);
            }
            cellItem.AddGSMParamValue(tp);           
        }

        private void doTDCell(TestPoint tp, ref ZTCQTCellSetAnaAreaItem areaItem)
        {
            int? lac = (int?)tp["TD_LAC"];
            int? ci = (int?)tp["TD_CI"];
            float? rscp = (float?)tp["TD_PCCPCH_RSCP"];

            if (lac == null || ci == null || rscp == null)
            {
                return;
            }

            string cellName = "";
            string cellType = "未知";
            TDCell tpCell = tp.GetMainCell_TD_TDCell();
            if (tpCell != null)
            {
                cellName = tpCell.Name;

                if (tpCell.Type == TDNodeBType.Indoor)
                {
                    areaItem.SampleIndoor++;
                    cellType = "室分";
                }
                else
                {
                    areaItem.SampleOutdoor++;
                    cellType = "非室分";
                }
            }
            else
            {
                cellName = ((int)lac).ToString() + "_" + ((int)ci).ToString();

                areaItem.SampleUnknown++;
            }

            ZTCQTCellSetAnaCellItem cellItem = null;
            if (!areaItem.CellDic.TryGetValue(cellName, out cellItem))
            {
                cellItem = new ZTCQTCellSetAnaCellItem(cellName, cellType, tpCell);
                cellItem.SN = areaItem.CellDic.Count + 1;
                areaItem.CellDic.Add(cellName, cellItem);
            }
            cellItem.AddTDParamValue(tp);    
        }

        private void doTDDLTECell(TestPoint tp, ref ZTCQTCellSetAnaAreaItem areaItem)
        {
            int? tac = (int?)(ushort?)tp["lte_TAC"];
            int? eci = (int?)tp["lte_ECI"];
            float? rsrp = (float?)tp["lte_RSRP"];

            if (tac == null || tac == 65535 || eci == null || rsrp == null)
            {
                return;
            }

            string cellName = "";
            string cellType = "未知";
            LTECell tpCell = tp.GetMainCell_LTE();
            if (tpCell != null)
            {
                cellName = tpCell.Name;

                if (tpCell.Type == LTEBTSType.Indoor)
                {
                    areaItem.SampleIndoor++;
                    cellType = "室分";
                }
                else
                {
                    areaItem.SampleOutdoor++;
                    cellType = "非室分";
                }
            }
            else
            {
                cellName = ((int)tac).ToString() + "_" + ((int)eci).ToString();

                areaItem.SampleUnknown++;
            }

            ZTCQTCellSetAnaCellItem cellItem = null;
            if (!areaItem.CellDic.TryGetValue(cellName, out cellItem))
            {
                cellItem = new ZTCQTCellSetAnaCellItem(cellName, cellType, tpCell);
                cellItem.SN = areaItem.CellDic.Count + 1;
                areaItem.CellDic.Add(cellName, cellItem);
            }
            cellItem.AddLTEParamValue(tp);    
        }

         /// <summary>
        /// 显示结果
        /// </summary>
        protected override void fireShowForm()
        {
            if (resultDic.Count == 0)
            {
                System.Windows.Forms.MessageBox.Show("没有符合条件的信息！");
                return;
            }

            object obj = MainModel.GetInstance().GetObjectFromBlackboard(typeof(ZTCQTCellSetAnaListForm).FullName);
            ZTCQTCellSetAnaListForm resultForm = obj == null ? null : obj as ZTCQTCellSetAnaListForm;
            if (resultForm == null || resultForm.IsDisposed)
            {
                resultForm = new ZTCQTCellSetAnaListForm(MainModel);
            }

            resultForm.FillData(resultDic);
            if (!resultForm.Visible)
            {
                resultForm.Show(MainModel.MainForm);
            }
        }

        protected override void releaseSource()
        {
            resultDic = null;
            areaCoverTypeDic = null;
            areaNameDic = null;
        }
    }

    public class ZTCQTCellSetAnaCityItem
    {
        public int SN { get; set; }
        public string CityName { get; set; }

        public Dictionary<string, ZTCQTCellSetAnaAreaItem> AreaDic { get; set; }

        public ZTCQTCellSetAnaCityItem(string cityName)
        {
            CityName = cityName;
            AreaDic = new Dictionary<string, ZTCQTCellSetAnaAreaItem>();
        }
    }

    public class ZTCQTCellSetAnaAreaItem
    {
        public int SN { get; set; }
        public string AreaCover { get; set; }
        public string AreaType { get; set; }
        public string AreaName { get; set; }

        public float SampleIndoor { get; set; }      //占用室内小区采样点数
        public float SampleOutdoor { get; set; }     //占用室外小区采样点数
        public float SampleUnknown { get; set; }     //未知采样点数采样点数（没有解析出小区的属性）

        public Dictionary<string, ZTCQTCellSetAnaCellItem> CellDic { get; set; }

        public ZTCQTCellSetAnaAreaItem(string areaCover, string areaType, string areaName)
        {
            AreaCover = areaCover; 
            AreaType = areaType;
            AreaName = areaName;

            SampleIndoor = 0;
            SampleOutdoor = 0;
            SampleUnknown = 0;

            CellDic = new Dictionary<string, ZTCQTCellSetAnaCellItem>();
        }

        #region 预处理
        public double PercentIndoor
        {
            get
            {
                return Math.Round(100*SampleIndoor / (SampleIndoor + SampleOutdoor + SampleUnknown), 2);
            }
        }

        public double PercentOutdoor
        {
            get
            {
                return Math.Round(100 * SampleOutdoor / (SampleIndoor + SampleOutdoor + SampleUnknown), 2);
            }
        }

        public double PercentUnknown
        {
            get
            {
                return Math.Round(100 * SampleUnknown / (SampleIndoor + SampleOutdoor + SampleUnknown), 2);
            }
        }
        #endregion
    }

    public class ZTCQTCellSetAnaCellItem
    {
        public int SN { get; set; }
        public string CellName { get; set; }
        public string CellType { get; set; }

        public ICell CurCell { get; set; }
        public ZTCQTCellSetAnaParamItem RxlevItem { get; set; }
        public ZTCQTCellSetAnaParamItem RxQualItem { get; set; }
        public ZTCQTCellSetAnaParamItem DistanceItem { get; set; }

        public ZTCQTCellSetAnaCellItem(string cellName, string cellType, ICell curCell)
        {
            CellName = cellName;
            CellType = cellType;
            CurCell = curCell;

            RxlevItem = new ZTCQTCellSetAnaParamItem();
            RxQualItem = new ZTCQTCellSetAnaParamItem();
            DistanceItem = new ZTCQTCellSetAnaParamItem();
        }

        public void AddGSMParamValue(TestPoint tp)
        {
            short? rxlev = (short?)tp["RxLevSub"];
            if (rxlev != null)
            {
                RxlevItem.AddOneValue((float)(short)rxlev);
            }

            int? rxQual = (int?)(byte?)tp["RxQualSub"];
            if (rxQual != null)
            {
                RxQualItem.AddOneValue((float)(int)rxQual);
            }

            AddDistanceParamValue(tp);
        }

        public void AddTDParamValue(TestPoint tp)
        {
            float? rscp = (float?)tp["TD_PCCPCH_RSCP"];
            if (rscp != null)
            {
                RxlevItem.AddOneValue((float)rscp);
            }

            int? c2i = (int?)tp["TD_PCCPCH_C2I"];
            if (c2i != null)
            {
                RxQualItem.AddOneValue((float)c2i);
            }

            AddDistanceParamValue(tp);
        }

        public void AddLTEParamValue(TestPoint tp)
        {
            float? rsrp = (float?)tp["lte_RSRP"];
            if (rsrp != null)
            {
                RxlevItem.AddOneValue((float)rsrp);
            }

            float? sinr = (float?)tp["lte_SINR"];
            if (sinr != null)
            {
                RxQualItem.AddOneValue((float)sinr);
            }

            AddDistanceParamValue(tp);
        }

        public void AddDistanceParamValue(TestPoint tp)
        {
            if (CurCell != null)
            {
                double distance = MathFuncs.GetDistance(CurCell.Longitude, CurCell.Latitude, tp.Longitude, tp.Latitude);
                DistanceItem.AddOneValue((float)distance);
            }
        }
    }
 
    public class ZTCQTCellSetAnaParamItem
    {
        public float ParamMax { get; set; }
        public float ParamMin { get; set; }
        public float ParamTotal { get; set; }
        public float ParamSample { get; set; }

        public ZTCQTCellSetAnaParamItem()
        {
            ParamMax = float.MinValue;
            ParamMin = float.MaxValue;
            ParamTotal = 0;
            ParamSample = 0;
        }

        public void AddOneValue(float curValue)
        {
            if (curValue > ParamMax)
            {
                ParamMax = curValue;
            }
            if (curValue < ParamMin)
            {
                ParamMin = curValue;
            }

            ParamTotal += curValue;
            ParamSample++;
        }
    }
}
