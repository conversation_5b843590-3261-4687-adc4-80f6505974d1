﻿using System;
using System.Collections.Generic;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using EvtEngineLib;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTLTEHandOverAnaBase : DIYAnalyseFilesOneByOneByRegion
    {
        public List<ZTLTEHandOverAnaItem> resultList { get; set; } = new List<ZTLTEHandOverAnaItem>();    //保存结果
        public ZTLTEHandOverAnaCondition hoCondition { get; set; } = new ZTLTEHandOverAnaCondition();   //查询条件
        protected List<int> valuedEvtId;

        public ZTLTEHandOverAnaBase(MainModel mainModel)
            : base(mainModel)
        {
            this.IncludeEvent = true;
            this.IncludeMessage = false;
#if AllRtpMsg
            this.IncludeMessage = true;
            this.IncludeAllRtpMessage = true;
#endif
            valuedEvtId = new List<int> { 851, 899 }; //851:Intra Handover Success  899:Inter Handover Success
        } 

        protected override void clearDataBeforeAnalyseFiles()
        {
            resultList = new List<ZTLTEHandOverAnaItem>();
        }

        ZTLTEHandOverAnaSetForm setForm = null;
        List<int> businessBeginEventIDs = null;
        List<int> businessEndEventIDs = null;
        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }
            if (setForm == null)
            {
                setForm = new ZTLTEHandOverAnaSetForm();
            }
            if (setForm.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                hoCondition = setForm.GetCondition();
                if (hoCondition.IsBusiness)
                {
                    businessBeginEventIDs = new List<int>() { 57, 60, 1211, 1216 };
                    businessEndEventIDs = new List<int>() { 58, 59, 61, 62, 1214, 1213
                                                      , 1215, 1269, 1217, 1218, 1270 };
                } 
                return true;
            }
            return false;
        }

        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager fileMng in MainModel.DTDataManager.FileDataManagers)
            {
                List<TestPoint> testPointList = fileMng.TestPoints;
                List<Event> eventList = fileMng.Events;
                List<Message> msgList = fileMng.Messages;

                int idx = 0;
                msgList.Sort((x, y) =>
                {
                    return x.HandsetTime.CompareTo(y.HandsetTime);
                });
                for (int eLoop = 0; eLoop < eventList.Count; ++eLoop)
                {
                    Event e = eventList[eLoop];
                    if (!valuedEvtId.Contains(e.ID))
                    {
                        continue;
                    }

                    int index = -1;
                    if ((index = GetNearestTestPointIndex(e.SN, testPointList)) == -1)
                    {
                        continue;
                    }
                    if (hoCondition.IsBusiness && !IsBusinessHandOver(eventList, eLoop))
                    {
                        continue;
                    }

                    idx = addResultList(testPointList, msgList, idx, e, index);
                }
            } 
        }

        private int addResultList(List<TestPoint> testPointList, List<Message> msgList, int idx, Event e, int index)
        {
            ZTLTEHandOverAnaItem item = new ZTLTEHandOverAnaItem(e.FileName, e);

            long tpTimeHead = (long)(e.Time * 1000L + e.Millisecond - hoCondition.BeforeSecond * 1000L);
            long tpTimeTail = (long)(e.Time * 1000L + e.Millisecond + hoCondition.AfterSecond * 1000L);

            for (int i = index; i >= 0; --i)
            {
                TestPoint tp = testPointList[i];
                if ((tp.Time * 1000L + tp.Millisecond) < tpTimeHead)
                {
                    break;
                }
                item.AddBeforeTp(tp);
            }
            for (int i = index + 1; i < testPointList.Count; ++i)
            {
                TestPoint tp = testPointList[i];
                if (tpTimeTail < (tp.Time * 1000L + tp.Millisecond))
                {
                    break;
                }
                item.AddAfterTp(tp);
            }

            item.SetSameSiteFlag(hoCondition.SiteDistance);

            item.SN = resultList.Count + 1;
#if AllRtpMsg
            item.SetLostPacketNum(msgList, ref idx, e.DateTime);
#endif
#if DEBUG
            Console.Write(msgList);
#endif
            item.Calculate();
            resultList.Add(item);
            return idx;
        }

        private bool IsBusinessHandOver(List<Event> eventList, int eLoop)
        {
            bool isBusiness = false;
            for (int i = eLoop - 1; i >= 0; i--)
            {
                int iEventID = eventList[i].ID;
                if (businessEndEventIDs.Contains(iEventID))
                    break;
                if (businessBeginEventIDs.Contains(iEventID))
                {
                    isBusiness = true;
                    break;
                }
            }
            if (!isBusiness)
            {
                for (int i = eLoop + 1; i < eventList.Count; i++)
                {
                    int iEventID = eventList[i].ID;
                    if (businessBeginEventIDs.Contains(iEventID))
                        break;
                    if (businessEndEventIDs.Contains(iEventID))
                    {
                        isBusiness = true;
                        break;
                    }
                }
            }
            return isBusiness;
        }

         /// <summary>
        /// 显示结果
        /// </summary>
        protected override void fireShowForm()
        {
            if (resultList.Count == 0)
            {
                System.Windows.Forms.MessageBox.Show("没有符合条件的信息！");
                return;
            }

            object obj = MainModel.GetInstance().GetObjectFromBlackboard(typeof(ZTLTEHandOverAnaListForm).FullName);
            ZTLTEHandOverAnaListForm cellReselectAnaListForm = obj == null ? null : obj as ZTLTEHandOverAnaListForm;
            if (cellReselectAnaListForm == null || cellReselectAnaListForm.IsDisposed)
            {
                cellReselectAnaListForm = new ZTLTEHandOverAnaListForm(MainModel);
            }

            cellReselectAnaListForm.FillData(resultList);
            if (!cellReselectAnaListForm.Visible)
            {
                cellReselectAnaListForm.Show(MainModel.MainForm);
            }
        }

        protected override void releaseSource()
        {
            resultList = null;
        }

        /// <summary>
        /// tpList升序，寻找最大的index使得tpList[index] <= eventSN
        /// </summary>
        /// <param name="eventSN"></param>
        /// <param name="tpList"></param>
        /// <returns></returns>
        protected int GetNearestTestPointIndex(int eventSN, List<TestPoint> tpList)
        {
            int index = -1;
            for (int i = 0; i < tpList.Count; i++)
            {
                if (tpList[i].SN > eventSN)
                {
                    index = i - 1;
                    break;
                }
            }
            return index;
        }

        private Comparer comparer { get; set; } = new Comparer();
        private class Comparer : IComparer<DTData>
        {
            public int Compare(DTData x, DTData y)
            {
                return x.SN - y.SN;
            }
        }
    }

    public class ZTLTEHandOverAnaBase_FDD : ZTLTEHandOverAnaBase
    {
        public ZTLTEHandOverAnaBase_FDD(MainModel mainModel)
            : base(mainModel)
        {
            carrierID = CarrierType.ChinaUnicom;
            this.IncludeEvent = true;
            this.IncludeMessage = false;
            valuedEvtId = new List<int> { 3156, 3159 }; //3156:LTE_FDD Intra Handover Success  3159:LTE_FDD Inter Handover Success
        }

        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 26000, 26043, this.Name);//////
        }
    }

    public class ZTLTEHandOverAnaItem
    {
        public int SN { get; set; }
        public string FileName { get; set; }
        public string DateTime { get; private set; } = "";
        public double Longitude { get; private set; } = 0;
        public double Latitude { get; private set; } = 0;
        public string HandOverResult { get; private set; } = "";
        public string HnadOverDirection { get; private set; }
        public string HandOverType { get; private set; }
        private long downLostPacketNum = 0;
        public string DownLostPacketNum { get; private set; }

        public void Calculate()
        {
            if (HOEvt != null)
            {
                DateTime = HOEvt.DateTime.ToString("yyyy-MM-dd HH:mm:ss");
                Longitude = HOEvt.Longitude;
                Latitude = HOEvt.Latitude;


                if (HOEvt.ID == 851 || HOEvt.ID == 899 || HOEvt.ID == 3156 || HOEvt.ID == 3159)
                {
                    HandOverResult = "成功";
                }
                else if (HOEvt.ID == 870 || HOEvt.ID == 1100 || HOEvt.ID == 3157 || HOEvt.ID == 3160)
                {
                    HandOverResult = "失败";
                }
            }

            HnadOverDirection = SrcCellItem.FreqBand + "->" + DestCellItem.FreqBand;
            if (SrcCellItem.FreqBand == DestCellItem.FreqBand)
            {
                HandOverType = "同频切换";
            }
            else
            {
                HandOverType = "异频切换";
            }

            DownLostPacketNum = downLostPacketNum.ToString();
        }

        public void SetLostPacketNum(List<Message> msgList,ref int index,DateTime dt)
        {
            Message preMsgForUpOrDown = null;   //在前面的信令（判断方向）
            Message nextMsgForUpOrDown = null;  //在后面的信令
            int msgIdRRCComplete = 1093626370;  //RRC Connection Reconfiguration Complete信令
            int msgIdPayload = 2147426825;      //IMS_RTP_SN_AND_Payload信令的数字

            getNextMsgForUpOrDown(msgList, ref index, dt, ref nextMsgForUpOrDown, msgIdRRCComplete, msgIdPayload);
            if (nextMsgForUpOrDown != null)
            {
                for (int i = index; i > 0; i--)
                {
                    if (msgList[i].ID == msgIdPayload && msgList[i].Direction == nextMsgForUpOrDown.Direction)
                    {
                        preMsgForUpOrDown = msgList[i];//自减并找到之前的信令，判断方向
                        break;
                    }
                }
                if (preMsgForUpOrDown != null && nextMsgForUpOrDown.Direction == 1)
                {
                    downLostPacketNum = GetLossNum(preMsgForUpOrDown, nextMsgForUpOrDown);//下行丢包数
                }
            }
        }

        private void getNextMsgForUpOrDown(List<Message> msgList, ref int index, DateTime dt, ref Message nextMsgForUpOrDown, 
            int msgIdRRCComplete, int msgIdPayload)
        {
            for (int i = index; i < msgList.Count; i++)
            {
                if (dt.CompareTo(msgList[i].DateTime) <= 0 && msgList[i].ID == msgIdRRCComplete)//找到该信令并指出下标
                {
                    index = i;
                    break;
                }
            }
            for (int i = index; i < msgList.Count; i++)
            {
                if (msgList[i].ID == msgIdPayload && msgList[i].Direction == 1)//下行信令
                {
                    nextMsgForUpOrDown = msgList[i];
                    break;
                }
            }
        }

        private readonly string signRTPNumber = "eSam_VOLTE_RTP_Packets_Lost_Num";
        private long GetLossNum(Model.Message msgStart, Model.Message msgEnd)
        {
            long result = 0;
            long lossNumStart = 0;
            long lossNumEnd = 0;
            MessageWithSource msgStartWithSource = msgStart as MessageWithSource;
            MessageWithSource msgEndWithSource = msgEnd as MessageWithSource;

            OwnMsgDecode.StartDissect(msgStartWithSource.Source, msgStartWithSource.ID);
            OwnMsgDecode.GetIntValue(signRTPNumber, ref lossNumStart);
            OwnMsgDecode.StartDissect(msgEndWithSource.Source, msgEndWithSource.ID);
            OwnMsgDecode.GetIntValue(signRTPNumber, ref lossNumEnd);

            result = lossNumEnd - lossNumStart;
            return result;
        }

        public ZTLTEHandOverAnaCellItem SrcCellItem{ get; set; }
        public ZTLTEHandOverAnaCellItem DestCellItem { get; set; }

        public ZTLTEHandOverAnaTpItem BeforeTpItem{ get; set; }
        public ZTLTEHandOverAnaTpItem AfterTpItem { get; set; }

        public Event HOEvt { get; set; }
        public List<TestPoint> TpList { get; set; }
        public string IsSameSite { get; set; }

        public ZTLTEHandOverAnaItem(string fileName, Event hoEvt)
        {
            FileName = fileName;
            HOEvt = hoEvt;
            SrcCellItem = new ZTLTEHandOverAnaCellItem();
            DestCellItem = new ZTLTEHandOverAnaCellItem();
            BeforeTpItem = new ZTLTEHandOverAnaTpItem();
            AfterTpItem = new ZTLTEHandOverAnaTpItem();
            TpList = new List<TestPoint>();

            SetCellItem(SrcCellItem, (int)hoEvt["LAC"], (int)hoEvt["CI"], Convert.ToInt32(hoEvt["Value2"]), Convert.ToInt32(hoEvt["Value3"]), hoEvt);
            SetCellItem(DestCellItem, (int)hoEvt["TargetLAC"], (int)hoEvt["TargetCI"], Convert.ToInt32(hoEvt["Value4"]), Convert.ToInt32(hoEvt["Value5"]), hoEvt);
        }

        public void AddBeforeTp(TestPoint tp)
        {
            AddTpInfo(this.BeforeTpItem, tp);
        }

        public void AddAfterTp(TestPoint tp)
        {
            //判断邻区是否包含源小区,包含则记录源小区
            for (int j = 0; j < 20; j++)
            {
                LTECell nCell = tp.GetNBCell_LTE(j);
                if (nCell != null && nCell.TAC.ToString() == SrcCellItem.TAC && nCell.ECI.ToString() == SrcCellItem.ECI)
                {
                    float? nRsrp = GetNRSRP(tp, j);
                    if (nRsrp != null && -141 <= nRsrp && nRsrp <= 25)
                    {
                        AfterTpItem.SrcRsrp += (float)nRsrp;
                        AfterTpItem.SrcRsrpCount++;
                    }
                }
            }
            AddTpInfo(this.AfterTpItem, tp);
        }

        public void AddTpInfo(ZTLTEHandOverAnaTpItem tpItem, TestPoint tp)
        {
            TpList.Add(tp);
            float? rsrp = GetRSRP(tp);
            if (rsrp != null && rsrp >= -140 && rsrp <= -10)
            {
                tpItem.Rsrp += (float)rsrp;
                tpItem.RsrpCount++;
            }

            float? sinr = GetSINR(tp);
            if (sinr != null && sinr >= -50 && sinr <= 50)
            {
                tpItem.Sinr += (float)sinr;
                tpItem.SinrCount++;
            }

            float? appSpeed = GetAppSpeed(tp);
            if (appSpeed != null && appSpeed >= 0)
            {
                tpItem.AppSpeed += (float)appSpeed / 1000 / 1000;
                tpItem.AppSpeedCount++;
            }

            int? status = GetStatus(tp);
            float? pdcpSpeed = GetPdchSpeed(tp);
            if (pdcpSpeed != null && pdcpSpeed >= 0 && status != null && status == 12)
            {
                tpItem.PdcpSpeed += (float)pdcpSpeed / 1000 / 1000;
                tpItem.PdcpSpeedCount++;
            }

            float? rsrq = GetRSRQ(tp);
            if (rsrq != null && rsrq >= -40 && rsrq <= 40)
            {
                tpItem.Rsrq += (float)rsrq;
                tpItem.RsrqCount++;
            }

            float? rssi = GetRSSI(tp);
            if (rssi != null && rssi >= -125 && rssi <= -25)
            {
                tpItem.Rssi += (float)rssi;
                tpItem.RssiCount++;
            }
        }

        protected virtual float? GetRSRP(TestPoint tp)
        {
            if (tp is LTEFddTestPoint)
            {
                return (float?)tp["lte_fdd_RSRP"];
            }
            return (float?)tp["lte_RSRP"];
        }

        protected virtual float? GetNRSRP(TestPoint tp, int index)
        {
            if (tp is LTEFddTestPoint)
            {
                return (float?)tp["lte_fdd_NCell_RSRP", index];
            }
            return (float?)tp["lte_NCell_RSRP", index];
        }

        protected virtual float? GetSINR(TestPoint tp)
        {
            if (tp is LTEFddTestPoint)
            {
                return (float?)tp["lte_fdd_SINR"];
            }
            return (float?)tp["lte_SINR"];
        }
        protected virtual float? GetAppSpeed(TestPoint tp)
        {
            if (tp is LTEFddTestPoint)
            {
                return (float?)(int?)tp["lte_fdd_APP_ThroughputDL"];
            }
            if (tp.FileName.Contains("上传"))
            {
                return (float?)(int?)tp["lte_APP_ThroughputUL"];
            } 
            else
            {
                return (float?)(int?)tp["lte_APP_ThroughputDL"];
            }
        }
        protected virtual int? GetStatus(TestPoint tp)
        {
            if (tp is LTEFddTestPoint)
            {
                return (int?)(short?)tp["lte_fdd_APP_Status"];
            }
            return (int?)(short?)tp["lte_APP_Status"];
        }
        protected virtual float? GetPdchSpeed(TestPoint tp)
        {
            if (tp is LTEFddTestPoint)
            {
                return (float?)(int?)tp["lte_fdd_PDCP_DL"];
            }
            return (float?)(int?)tp["lte_PDCP_DL"];
        }
        protected virtual float? GetRSRQ(TestPoint tp)
        {
            if (tp is LTEFddTestPoint)
            {
                return (float?)tp["lte_fdd_RSRQ"];
            }
            return (float?)tp["lte_RSRQ"]; 
        }
        protected virtual float? GetRSSI(TestPoint tp)
        {
            if (tp is LTEFddTestPoint)
            {
                return (float?)tp["lte_fdd_RSSI"];
            }
            return (float?)tp["lte_RSSI"];
        }

        public void SetCellItem(ZTLTEHandOverAnaCellItem cellItem, int tac, int eci, int earfrn, int pci, Event reselEvt)
        {
            LTECell cell = CellManager.GetInstance().GetLTECell(reselEvt.DateTime, tac, eci);

            if (cell != null)
            {
                cellItem.LteCell = cell;
                cellItem.CellName = cell.Name;
                cellItem.CellID = cell.CellID.ToString();
                cellItem.Distance = Math.Round(cell.GetDistance(reselEvt.Longitude, reselEvt.Latitude),2).ToString();
            }

            cellItem.TAC = tac.ToString();
            cellItem.ECI = eci.ToString();
            cellItem.EARFCN = earfrn;
            cellItem.PCI = pci;
            cellItem.FreqBand = LTECell.GetBandTypeByEarfcn(earfrn).ToString();
        }

        public void SetSameSiteFlag(int siteDistance)
        {
            if (SrcCellItem.LteCell != null && DestCellItem.LteCell != null)
            {
                if (SrcCellItem.LteCell.GetDistance(DestCellItem.LteCell.Longitude, DestCellItem.LteCell.Latitude) <= siteDistance)
                {
                    IsSameSite = "是";
                }
                else
                {
                    IsSameSite = "否";
                }
            }
        } 
        
        #region 预处理
        public int BeforeEarfcn 
        { 
            get
            { 
                return SrcCellItem.EARFCN;
            } 
        }
        public int BeforePCI 
        { 
            get 
            { 
                return SrcCellItem.PCI;
            } 
        }
        public string BeforeCellName
        {
            get
            {
                return SrcCellItem.CellName;
            }
        }
        public string BeforeCellDistance
        {
            get
            {
                return SrcCellItem.Distance;
            }
        }
        public string BeforeRsrpAvg
        {
            get
            {
                if (BeforeTpItem.RsrpCount > 0)
                {
                    return Math.Round(BeforeTpItem.Rsrp / BeforeTpItem.RsrpCount, 2).ToString();
                }
                else
                {
                    return "";
                }
            }
        }
        public string BeforeSinrAvg
        {
            get
            {
                if (BeforeTpItem.SinrCount > 0)
                {
                    return Math.Round(BeforeTpItem.Sinr / BeforeTpItem.SinrCount, 2).ToString();
                }
                else
                {
                    return "";
                }
            }
        }
        public string BeforeAppSpeedAvg
        {
            get
            {
                if (BeforeTpItem.AppSpeedCount > 0)
                {
                    return Math.Round(BeforeTpItem.AppSpeed / BeforeTpItem.AppSpeedCount, 2).ToString();
                }
                else
                {
                    return "";
                }
            }
        }
        public string BeforePdcpSpeedAvg
        {
            get
            {
                if (BeforeTpItem.PdcpSpeedCount > 0)
                {
                    return Math.Round(BeforeTpItem.PdcpSpeed / BeforeTpItem.PdcpSpeedCount, 2).ToString();
                }
                else
                {
                    return "";
                }
            }
        }
        public string BeforeRsrqAvg
        {
            get
            {
                if (BeforeTpItem.RsrqCount > 0)
                {
                    return Math.Round(BeforeTpItem.Rsrq / BeforeTpItem.RsrqCount, 2).ToString();
                }
                else
                {
                    return "";
                }
            }
        }
        public string BeforeRssiAvg
        {
            get
            {
                if (BeforeTpItem.RssiCount > 0)
                {
                    return Math.Round(BeforeTpItem.Rssi / BeforeTpItem.RssiCount, 2).ToString();
                }
                else
                {
                    return "";
                }
            }
        }

        public int AfterEarfcn
        {
            get
            { 
                return DestCellItem.EARFCN; 
            }
        }
        public int AfterPCI 
        { 
            get
            { 
                return DestCellItem.PCI; 
            } 
        }
        public string AfterCellName
        {
            get
            {
                return DestCellItem.CellName;
            }
        }
        public string AfterCellDistance
        {
            get
            {
                return DestCellItem.Distance;
            }
        }
        public string AfterRsrpAvg
        {
            get
            {
                if (AfterTpItem.RsrpCount > 0)
                {
                    return Math.Round(AfterTpItem.Rsrp / AfterTpItem.RsrpCount, 2).ToString();
                }
                else
                {
                    return "";
                }
            }
        }
        public string AfterSrcRsrpAvg
        {
            get
            {
                if (AfterTpItem.SrcRsrpCount > 0)
                {
                    return Math.Round(AfterTpItem.SrcRsrp / AfterTpItem.SrcRsrpCount, 2).ToString();
                }
                else
                {
                    return "";
                }
            }
        }
        public string AfterSinrAvg
        {
            get
            {
                if (AfterTpItem.SinrCount > 0)
                {
                    return Math.Round(AfterTpItem.Sinr / AfterTpItem.SinrCount, 2).ToString();
                }
                else
                {
                    return "";
                }
            }
        }
        public string AfterAppSpeedAvg
        {
            get
            {
                if (AfterTpItem.AppSpeedCount > 0)
                {
                    return Math.Round(AfterTpItem.AppSpeed / AfterTpItem.AppSpeedCount, 2).ToString();
                }
                else
                {
                    return "";
                }
            }
        }
        public string AfterPdcpSpeedAvg
        {
            get
            {
                if (AfterTpItem.PdcpSpeedCount > 0)
                {
                    return Math.Round(AfterTpItem.PdcpSpeed / AfterTpItem.PdcpSpeedCount, 2).ToString();
                }
                else
                {
                    return "";
                }
            }
        }
        public string AfterRsrqAvg
        {
            get
            {
                if (AfterTpItem.RsrqCount > 0)
                {
                    return Math.Round(AfterTpItem.Rsrq / AfterTpItem.RsrqCount, 2).ToString();
                }
                else
                {
                    return "";
                }
            }
        }
        public string AfterRssiAvg
        {
            get
            {
                if (AfterTpItem.RssiCount > 0)
                {
                    return Math.Round(AfterTpItem.Rssi / AfterTpItem.RssiCount, 2).ToString();
                }
                else
                {
                    return "";
                }
            }
        }
        public string GridName
        {
            get
            {
                return GISManager.GetInstance().GetGridDesc(HOEvt.Longitude, HOEvt.Latitude);
            }
        }
        #endregion
    }

    public class ZTLTEHandOverAnaCellItem
    {
        public LTECell LteCell { get; set; }
        public string CellName { get; set; }
        public string TAC { get; set; }
        public string ECI { get; set; }
        public int EARFCN { get; set; }
        public string FreqBand { get; set; }
        public int PCI { get; set; }
        public string CellID { get; set; }
        public string Distance { get; set; }
    }

    public class ZTLTEHandOverAnaTpItem
    {
        public float Rsrp { get; set; }
        public float RsrpCount { get; set; }
        public float Sinr { get; set; }
        public float SinrCount { get; set; }
        public float AppSpeed { get; set; }
        public float AppSpeedCount { get; set; }
        public float PdcpSpeed { get; set; }
        public float PdcpSpeedCount { get; set; }
        public float Rsrq { get; set; }
        public float RsrqCount { get; set; }
        public float Rssi { get; set; }
        public float RssiCount { get; set; }
        public float SrcRsrp { get; set; }
        public float SrcRsrpCount { get; set; }
    }

    public class ZTLTEHandOverAnaCondition
    {
        public double BeforeSecond { get; set; }           //切换前时长
        public double AfterSecond { get; set; }            //切换后时长
        public int SiteDistance { get; set; }           //同站距离
        public bool IsBusiness { get; set; }            //只分析业务态 

        public ZTLTEHandOverAnaCondition()
        {
            BeforeSecond = 5;
            AfterSecond = 5;
            SiteDistance = 50;
            IsBusiness = false;
        }
    }
}
