﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model.BaseInfo;

namespace MasterCom.RAMS.Model
{
    public partial class UserInfoForm : BaseForm
    {
        private List<User> existUsers = null;
        private bool isAdmin = false;
        public UserInfoForm(List<User> existUsers, User user2Modify, bool isAdmin, List<UserStatusInfo> userStatusList)
        {
            InitializeComponent();
            this.isAdmin = isAdmin;
            this.existUsers = existUsers;
            foreach (UserStatusInfo status in userStatusList)
            {
                cbxStatus.Items.Add(status.UserStatusDes);
            }

            init(user2Modify);
        }

        private void init(User user2Modify)
        {
#if NotModifyLoginName
            if (user2Modify != null)
            {
                txtLoginName.Enabled = false; 
            }
#endif
            //panelChangePwdCause是在密码修改后才出现,所以默认去掉,在panelChangePwdCause下面的控件上移
            this.Height -= panelChangePwdCause.Height;
            panelStatus.Top -= panelChangePwdCause.Height;
            this.Height -= panelStatus.Height;

            //panelOther不显示时,窗体高度去掉panelOther的高度,下方控件上移panelOther的高度,其他panel控件同理
            panelOther.Visible = false;
            this.Height -= panelOther.Height;
            panelChangePwdCause.Top -= panelOther.Height;
            panelStatus.Top -= panelOther.Height;

            #region 添加帐号属性
            cbxUserPro.Items.Clear();
            cbxUserPro.Items.Add("内部帐号");
            cbxUserPro.Items.Add("外部帐号");
            cbxUserPro.SelectedIndex = 0;
            #endregion

            #region 账号角色
            cbxUserRole.Items.Clear();
            cbxUserRole.Items.Add("市级普通账号");
            cbxUserRole.Items.Add("省级级普通账号");
            cbxUserRole.Items.Add("管理员");
            cbxUserRole.SelectedIndex = 0;
            #endregion


            #region 添加所属责任人
            cbxBelong.Items.Clear();
            Dictionary<string, bool> existDic = new Dictionary<string, bool>();
            foreach (User item in mainModel.PermissionManager.Users)
            {
                if (!existDic.ContainsKey(item.Name))
                {
                    cbxBelong.Items.Add(item.Name);
                    existDic[item.Name] = true;
                }

            }
            #endregion

            this.user2Modify = user2Modify;
            cbxCity.Items.Clear();
            addAdminInfo();

            setFormInfo(user2Modify);
        }

        private void addAdminInfo()
        {
            if (isAdmin)
            {
                lblDistrict.Visible = lblDx.Visible = cbxCity.Visible = true;
#if UserCityControl
                if (user2Modify != null)
                {
                    string[] citys = DistrictManager.GetInstance().DistrictNames;
                    int cityid = 0;
                    for (int i = 0; i < citys.Length; i++)
                    {
                        string name = citys[i];
                        if (string.IsNullOrEmpty(name) && user2Modify.CityID == -1)
                        {
                            cbxCity.Items.Add("所有地市(管理员)");
                            continue;
                        }
                        cityid = i;
                        if (user2Modify.CityIDs.Count != 0 && cityid != 0)
                        { //user_city表有配置，按这配置决定地市权限（管理员账号同样适用）
                            if (user2Modify.CityIDs.Contains(cityid))
                            {
                                cbxCity.Items.Add(citys[i]);
                            }
                        }
                        else if (user2Modify.CityID == -1)
                        { //没有配置时，如果是管理员，则拥有所有地市权限
                            cbxCity.Items.Add(citys[i]);
                        }
                        else if (user2Modify.CityID == cityid)
                        {//非管理员，只拥有单一地市权限
                            cbxCity.Items.Add(citys[i]);
                            break;
                        }
                    }
                }
                else
                {
                    DistrictManager mng = DistrictManager.GetInstance();
                    for (int i = 0; i < mng.DistrictNames.Length; i++)
                    {
                        if (string.IsNullOrEmpty(mng.DistrictNames[i]))
                        {
                            cbxCity.Items.Add("所有地市(管理员)");
                        }
                        else
                        {
                            cbxCity.Items.Add(mng.DistrictNames[i]);
                        }
                    }
                }
#else
                DistrictManager mng = DistrictManager.GetInstance();
                for (int i = 0; i < mng.DistrictNames.Length; i++)
                {
                    if (string.IsNullOrEmpty(mng.DistrictNames[i]))
                    {
                        cbxCity.Items.Add("所有地市(管理员)");
                    }
                    else
                    {
                        cbxCity.Items.Add(mng.DistrictNames[i]);
                    }
                }
#endif
#if LoginManage
                panelStatus.Visible = true;
                this.Height += panelStatus.Height;
#endif
            }
        }

        private void setFormInfo(User user2Modify)
        {
            if (user2Modify != null)
            {
                if (isAdmin && cbxCity.Items.Count > 0 && user2Modify.CityID < cbxCity.Items.Count)
                {
                    cbxCity.SelectedIndex = user2Modify.CityID == -1 ? 0 : user2Modify.CityID;
                }
                txtName.Text = user2Modify.Name;
                txtLoginName.Text = user2Modify.LoginName;
                txtDesc.Text = user2Modify.Description;
                txtPhone.Text = user2Modify.Phone;
                txtPW2.Text = txtPW1.Text = "not real password!";
                txtPW1.TextChanged += txtPW1_TextChanged;
                txtPW2.TextChanged += txtPW2_TextChanged;
                cbxUserPro.Text = user2Modify.UserProDes;
                cbxBelong.Text = user2Modify.BelongPerson;
                cbxUserRole.SelectedIndex = user2Modify.RoleID;
                cbxStatus.Text = user2Modify.UserStatusDes;
            }
        }

        bool pwChanged = false;
        void txtPW2_TextChanged(object sender, EventArgs e)
        {
            pwdChangeFunc();
        }

        void txtPW1_TextChanged(object sender, EventArgs e)
        {
            pwdChangeFunc();
        }

        private void pwdChangeFunc()
        {
            pwChanged = true;

#if LoginManage
            if (user2Modify != null && panelChangePwdCause.Visible == false)
            {
                panelChangePwdCause.Visible = true;
                this.Height += panelChangePwdCause.Height;
                panelStatus.Top += panelChangePwdCause.Height;
            }
#endif
        }

        private User user2Modify = null;
        public User User
        {
            get;
            private set;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            bool isValid = judgeValid();
            if (!isValid)
            {
                return;
            }
            if (user2Modify == null || pwChanged)
            {
                string strWarn;
                if (!User.IsPasswordValid(txtLoginName.Text.Trim(), txtPW2.Text.Trim(), out strWarn))
                {
                    MessageBox.Show(strWarn);
                    txtPW1.Focus();
                    return;
                }
            }
            if (user2Modify != null)
            {
                User = user2Modify;
                User.NewPassword = "";
                User.ModifyPasswordCause = "";
                if (pwChanged)
                {
#if LoginManage
                    if (txtChangePasswordDes.Text.Trim().Length == 0)
                    {
                        MessageBox.Show("请填写修改密码的原因！");
                        txtChangePasswordDes.Focus();
                        return;
                    }
                    User.ModifyPasswordCause = txtChangePasswordDes.Text.Trim();
#endif
                    User.NewPassword = txtPW2.Text;
                }
            }
            else
            {//new user
                User = new User();
                int maxId = int.MinValue;
                foreach (User u in existUsers)
                {
                    maxId = Math.Max(maxId, u.ID);
                }
                User.ID = maxId + 1;
                User.Password = User.NewPassword = txtPW2.Text;
                User.ModifyPasswordCause = "新增账户";
            }

            User.Name = txtName.Text;
            User.LoginName = txtLoginName.Text;
            if (isAdmin)
            {
                User.CityID = DistrictManager.GetInstance().GetDistrictID(cbxCity.SelectedItem.ToString());
            }
            User.Phone = txtPhone.Text;
            User.Description = txtDesc.Text;
            User.UserProDes = cbxUserPro.Text;
            User.BelongPerson = cbxBelong.Text;
            User.RoleID = cbxUserRole.SelectedIndex;
            User.UserStatus = cbxStatus.SelectedIndex + 1;
            User.UserStatusDes = cbxStatus.Text;
            DialogResult = DialogResult.OK;
        }

        private bool judgeValid()
        {
            if (txtName.Text.Trim().Length == 0)
            {
                MessageBox.Show("用户名不能为空！");
                txtName.Focus();
                return false;
            }
            if (txtLoginName.Text.Trim().Length == 0)
            {
                MessageBox.Show("登录名不能为空！");
                txtLoginName.Focus();
                return false;
            }
            User existUsr = existUsers.Find(delegate (User u) { return u.LoginName == txtLoginName.Text; });

            if (existUsr != null && (user2Modify == null || existUsr.ID != user2Modify.ID))
            {
                MessageBox.Show("已存在相同登录名，请修改！");
                txtLoginName.Focus();
                return false;
            }
            if (txtPW1.Text.Trim().Length == 0)
            {
                MessageBox.Show("密码不能为空！");
                txtPW1.Focus();
                return false;
            }
            if (txtPW2.Text != txtPW1.Text)
            {
                MessageBox.Show("两次密码输入不一致！");
                txtPW2.Focus();
                return false;
            }
            if (cbxCity.Visible && cbxCity.SelectedItem == null)
            {
                MessageBox.Show("请选择地市！");
                cbxCity.Focus();
                return false;
            }

         
            return true;
        }

        private void cbxCity_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (cbxCity.SelectedIndex == 0)
            {
                cbxUserRole.Enabled = true;
                if (cbxUserRole.SelectedIndex == 0)
                {
                    cbxUserRole.SelectedIndex = 1;
                }
            }
            else
            {
                cbxUserRole.SelectedIndex = 0;
                cbxUserRole.Enabled = false;
            }
        }

        private void cbxUserRole_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (cbxCity.SelectedIndex == 0)
            {
                if (cbxUserRole.SelectedIndex == 0)
                {
                    cbxUserRole.SelectedIndex = 1;
                }
            }
            else
            {
                cbxUserRole.SelectedIndex = 0;
            }
        }

    }
}
