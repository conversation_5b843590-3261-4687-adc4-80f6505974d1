﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using BrightIdeasSoftware;
using MasterCom.MTGis;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.Func.EventBlock
{
    public partial class EventBlockCompetitionResultForm : MinCloseForm
    {
        public EventBlockCompetitionResultForm(MainModel mm) : base(mm)
        {
            InitializeComponent();
            initUI();
        }

        #region initUI
        private void initUI()
        {
            initOverlap();

            initHost();

            initGuest();
        }

        private void initOverlap()
        {
            this.olvColumnSN.AspectGetter = delegate (object row)
            {
                if (row is EventBlock)
                {
                    IList list = listViewOverlap.Roots as IList;
                    if (list != null)
                    {
                        return list.IndexOf(row) + 1;
                    }
                    else
                    {
                        return null;
                    }
                }
                return null;
            };
            this.olvColumnAbCount.AspectGetter = delegate (object row)
            {
                if (row is EventBlock)
                {
                    EventBlock item = row as EventBlock;
                    return item.AbnormalEventCount;
                }
                else
                {
                    return "";
                }
            };
            this.olvColumnEvtName.AspectGetter = delegate (object row)
            {
                return getEventInfoParam(row, (evt) => { return evt.Name; }, listViewOverlap);
            };
            this.olvColumnEvtName.ImageGetter = delegate (object row)
            {
                return getEventInfoParam(row, (evt) => { return evt.Image; }, listViewOverlap);
            };
            this.olvColumnTime.AspectGetter = delegate (object row)
            {
                return getEventParam(row, (evt) => { return $"{evt.DateTime:yyyy-MM-dd HH:mm:ss}"; }, listViewOverlap);
            };
            this.olvColumnLAC.AspectGetter = delegate (object row)
            {
                return getEventParam(row, (evt) => { return Convert.ToInt32(evt["LAC"]); }, listViewOverlap);
            };
            this.olvColumnCI.AspectGetter = delegate (object row)
            {
                return getEventParam(row, (evt) => { return Convert.ToInt64(evt["CI"]); }, listViewOverlap);
            };
            this.olvColumnLongitude.AspectGetter = delegate (object row)
            {
                return getEventParam(row, (evt) => { return $"{evt.Longitude:F7}"; }, listViewOverlap);
            };
            this.olvColumnLatitude.AspectGetter = delegate (object row)
            {
                return getEventParam(row, (evt) => { return $"{evt.Latitude:F7}"; }, listViewOverlap);
            };
            this.olvColumnLogfile.AspectGetter = delegate (object row)
            {
                return getEventParam(row, (evt) => { return evt.FileName; }, listViewOverlap);
            };
            this.listViewOverlap.CanExpandGetter = delegate (object x)
            {
                return x is EventBlock;
            };
            this.listViewOverlap.ChildrenGetter = delegate (object x)
            {
                EventBlock kpair = (EventBlock)x;
                List<Event> evtLists = new List<Event>();
                evtLists.AddRange(kpair.AbnormalEvents);
                return evtLists;
            };
        }

        private void initHost()
        {
            this.olvColumnSN1.AspectGetter = delegate (object row)
            {
                if (row is EventBlock)
                {
                    IList list = listView1.Roots as IList;
                    if (list != null)
                    {
                        return list.IndexOf(row) + 1;
                    }
                    else
                    {
                        return null;
                    }
                }
                return null;
            };
            this.olvColumnAbCount1.AspectGetter = delegate (object row)
            {
                if (row is EventBlock)
                {
                    EventBlock item = row as EventBlock;
                    return item.AbnormalEventCount;
                }
                else
                {
                    return "";
                }
            };
            this.olvColumnEvtName1.AspectGetter = delegate (object row)
            {
                return getEventInfoParam(row, (evt) => { return evt.Name; }, listView1);
            };
            this.olvColumnEvtName1.ImageGetter = delegate (object row)
            {
                return getEventInfoParam(row, (evt) => { return evt.Image; }, listView1);
            };
            this.olvColumnTime1.AspectGetter = delegate (object row)
            {
                return getEventParam(row, (evt) => { return $"{evt.DateTime:yyyy-MM-dd HH:mm:ss}"; }, listView1);
            };
            this.olvColumnLAC1.AspectGetter = delegate (object row)
            {
                return getEventParam(row, (evt) => { return Convert.ToInt32(evt["LAC"]); }, listView1);
            };
            this.olvColumnCI1.AspectGetter = delegate (object row)
            {
                return getEventParam(row, (evt) => { return Convert.ToInt64(evt["CI"]); }, listView1);
            };
            this.olvColumnLongitude1.AspectGetter = delegate (object row)
            {
                return getEventParam(row, (evt) => { return $"{evt.Longitude:F7}"; }, listView1);
            };
            this.olvColumnLatitude1.AspectGetter = delegate (object row)
            {
                return getEventParam(row, (evt) => { return $"{evt.Latitude:F7}"; }, listView1);
            };
            olvColumnRoadDesc1.AspectGetter = delegate (object row)
            {
                if (row is EventBlock)
                {
                    EventBlock block = row as EventBlock;
                    return block.RoadPlaceDesc;
                }
                return "";
            };
            olvColumnRoadDesc.AspectGetter = delegate (object row)
            {
                if (row is EventBlock)
                {
                    EventBlock block = row as EventBlock;
                    return block.RoadPlaceDesc;
                }
                return "";
            };
            olvColumnRoadDesc2.AspectGetter = delegate (object row)
            {
                if (row is EventBlock)
                {
                    EventBlock block = row as EventBlock;
                    return block.RoadPlaceDesc;
                }
                return "";
            };
            this.olvColumnLogfile1.AspectGetter = delegate (object row)
            {
                return getEventParam(row, (evt) => { return evt.FileName; }, listView1);
            };
            this.listView1.CanExpandGetter = delegate (object x)
            {
                return x is EventBlock;
            };
            this.listView1.ChildrenGetter = delegate (object x)
            {
                EventBlock kpair = (EventBlock)x;
                List<Event> evtLists = new List<Event>();
                evtLists.AddRange(kpair.AbnormalEvents);
                return evtLists;
            };
        }

        private void initGuest()
        {
            this.olvColumnSN2.AspectGetter = delegate (object row)
            {
                if (row is EventBlock)
                {
                    IList list = listView2.Roots as IList;
                    if (list != null)
                    {
                        return list.IndexOf(row) + 1;
                    }
                    else
                    {
                        return null;
                    }
                }
                return null;
            };
            this.olvColumnAbCount2.AspectGetter = delegate (object row)
            {
                if (row is EventBlock)
                {
                    EventBlock item = row as EventBlock;
                    return item.AbnormalEventCount;
                }
                else
                {
                    return "";
                }
            };
            this.olvColumnEvtName2.AspectGetter = delegate (object row)
            {
                return getEventInfoParam(row, (evt) => { return evt.Name; }, listView2);
            };
            this.olvColumnEvtName2.ImageGetter = delegate (object row)
            {
                return getEventInfoParam(row, (evt) => { return evt.Image; }, listView2);
            };
            this.olvColumnTime2.AspectGetter = delegate (object row)
            {
                return getEventParam(row, (evt) => { return $"{evt.DateTime:yyyy-MM-dd HH:mm:ss}"; }, listView2);
            };
            this.olvColumnLAC2.AspectGetter = delegate (object row)
            {
                return getEventParam(row, (evt) => { return Convert.ToInt32(evt["LAC"]); }, listView2);
            };
            this.olvColumnCI2.AspectGetter = delegate (object row)
            {
                return getEventParam(row, (evt) => { return Convert.ToInt64(evt["CI"]); }, listView2);
            };
            this.olvColumnLongitude2.AspectGetter = delegate (object row)
            {
                return getEventParam(row, (evt) => { return $"{evt.Longitude:F7}"; }, listView2);
            };
            this.olvColumnLatitude2.AspectGetter = delegate (object row)
            {
                return getEventParam(row, (evt) => { return $"{evt.Latitude:F7}"; }, listView2);
            };
            this.olvColumnLogfile2.AspectGetter = delegate (object row)
            {
                return getEventParam(row, (evt) => { return evt.FileName; }, listView2);
            };
            this.listView2.CanExpandGetter = delegate (object x)
            {
                return x is EventBlock;
            };
            this.listView2.ChildrenGetter = delegate (object x)
            {
                EventBlock kpair = (EventBlock)x;
                List<Event> evtLists = new List<Event>();
                evtLists.AddRange(kpair.AbnormalEvents);
                return evtLists;
            };
        }

        private object getEventParam(object row, EventFunc func, TreeListView listView)
        {
            try
            {
                if (row is Event item)
                {
                    return func(item);
                }
                else if (row is EventBlock block && !listView.IsExpanded(row))
                {
                    List<Event> evts = block.AbnormalEvents;
                    if (evts.Count > 0)
                    {
                        return func(evts[0]);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message + ex.StackTrace);
            }
            return "";
        }

        delegate object EventFunc(Event evt);

        private object getEventInfoParam(object row, EventInfoFunc func, TreeListView listView)
        {
            try
            {
                if (row is Event item)
                {
                    if (item.EventInfo == null)
                    {
                        return "";
                    }
                    return func(item.EventInfo);
                }
                else if (row is EventBlock block && !listView.IsExpanded(row))
                {
                    List<Event> evts = block.AbnormalEvents;
                    int cnt = evts.Count;
                    for (int i = 0; i < cnt; i++)
                    {
                        var evt = evts[i];
                        if (evt != null && evt.EventInfo != null)
                        {
                            return func(evt.EventInfo);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message + ex.StackTrace);
            }
            return "";
        }

        delegate object EventInfoFunc(EventInfo evtInfo);
        #endregion

        private List<EventBlock> hostBlocks = null;
        private List<EventBlock> guestBlocks = null;
        private List<EventBlock> hostOnlyBlocks = null;
        private List<EventBlock> hostOverlapBlocks = null;
        private List<EventBlock> guestOnlyBlocks = null;
        private List<EventBlock> guestOverlapBlocks = null;
        private List<EventBlock> overlapBlocks = null;
        EventBlockCompetitionLayer layer = null;
        public void FillData(List<EventBlock> hostBlocks, List<EventBlock> guestBlocks, List<EventBlock> overlapBlocks, double blockRadius)
        {
            this.hostBlocks = hostBlocks;
            this.guestBlocks = guestBlocks;
            this.overlapBlocks = overlapBlocks;
            this.radius = blockRadius;
            hostOnlyBlocks = new List<EventBlock>();
            hostOverlapBlocks = new List<EventBlock>();
            foreach (EventBlock blk in hostBlocks)
            {
                if (blk.Repeat)
                {
                    hostOverlapBlocks.Add(blk);
                }
                else
                {
                    hostOnlyBlocks.Add(blk);
                }
            }
            guestOnlyBlocks = new List<EventBlock>();
            guestOverlapBlocks = new List<EventBlock>();
            foreach (EventBlock blk in guestBlocks)
            {
                if (blk.Repeat)
                {
                    guestOverlapBlocks.Add(blk);
                }
                else
                {
                    guestOnlyBlocks.Add(blk);
                }
            }
            makesureLayerVisible();
            fillListView();
            MainModel.ClearDTData();
            foreach (EventBlock blk in hostBlocks)
            {
                foreach (Event evt in blk.AbnormalEvents)
                {
                    MainModel.DTDataManager.Add(evt);
                }
            }
            foreach (EventBlock blk in guestBlocks)
            {
                foreach (Event evt in blk.AbnormalEvents)
                {
                    MainModel.DTDataManager.Add(evt);
                }
            }
            MainModel.IsDrawEventResult = false;
            MainModel.FireDTDataChanged(this);
        }

        double radius;
        private void makesureLayerVisible()
        {
            MapForm mf = MainModel.MainForm.GetMapForm();
            if (mf != null)
            {
                MasterCom.MTGis.CustomDrawLayer cLayer = mf.GetCustomLayer(typeof(EventBlockCompetitionLayer));
                if (cLayer == null)
                {
                    layer = new EventBlockCompetitionLayer(mf.GetMapOperation(), "事件汇聚对比图层");
                    mf.AddTempCustomLayer(layer);
                }
                else
                {
                    layer = cLayer as EventBlockCompetitionLayer;
                }
            }
            layer.BlockRadius = radius;
        }

        private void fillListView()
        {
            listViewOverlap.ClearObjects();
            if (overlapBlocks != null)
            {
                listViewOverlap.SetObjects(overlapBlocks);
                listViewOverlap.Tag = overlapBlocks;
                listViewOverlap.ExpandAll();
            }
            layer.OverlapBlocks = overlapBlocks;
            listView1.ClearObjects();
            listView2.ClearObjects();
            if (rbDiff.Checked)
            {
                listView1.SetObjects(hostOnlyBlocks);
                listView1.Tag = hostOnlyBlocks;
                listView1.ExpandAll();
                layer.HostBlocks = hostOnlyBlocks;
                listView2.SetObjects(guestOnlyBlocks);
                listView2.Tag = guestOnlyBlocks;
                listView2.ExpandAll();
                layer.GuestBlocks = guestOnlyBlocks;
            }
            else if (rbRepeat.Checked)
            {
                listView1.SetObjects(hostOverlapBlocks);
                listView1.Tag = hostOverlapBlocks;
                listView1.ExpandAll();
                layer.HostBlocks = hostOverlapBlocks;
                listView2.SetObjects(guestOverlapBlocks);
                listView2.Tag = guestOverlapBlocks;
                listView2.ExpandAll();
                layer.GuestBlocks = guestOverlapBlocks;
            }
            else
            {
                listView1.SetObjects(hostBlocks);
                listView1.Tag = hostBlocks;
                listView1.ExpandAll();
                layer.HostBlocks = hostBlocks;
                listView2.SetObjects(guestBlocks);
                listView2.Tag = guestBlocks;
                listView2.ExpandAll();
                layer.GuestBlocks = guestBlocks;
            }
            layer.Invalidate();
        }

        private void rbAll_CheckedChanged(object sender, EventArgs e)
        {
            fillListView();
        }

        private void rbDiff_CheckedChanged(object sender, EventArgs e)
        {
            fillListView();
        }

        private void rbRepeat_CheckedChanged(object sender, EventArgs e)
        {
            fillListView();
        }

        private void colorHost_EditValueChanged(object sender, EventArgs e)
        {
            if (layer != null)
            {
                layer.HostColor = colorHost.Color;
                layer.Invalidate();
            }
        }

        private void colorGuest_EditValueChanged(object sender, EventArgs e)
        {
            if (layer != null)
            {
                layer.GuestColor = colorGuest.Color;
                layer.Invalidate();
            }
        }

        private void colorOverlap_EditValueChanged(object sender, EventArgs e)
        {
            if (layer != null)
            {
                layer.OverlapColor = colorOverlap.Color;
                layer.Invalidate();
            }
        }

        private void chkHost_CheckedChanged(object sender, EventArgs e)
        {
            colorHost.Enabled = chkHost.Checked;
            if (layer != null)
            {
                layer.DrawHost = chkHost.Checked;
                layer.Invalidate();
            }
        }

        private void chkGuest_CheckedChanged(object sender, EventArgs e)
        {
            colorGuest.Enabled = chkGuest.Checked;
            if (layer != null)
            {
                layer.DrawGuest = chkGuest.Checked;
                layer.Invalidate();
            }
        }

        private void chkOverlap_CheckedChanged(object sender, EventArgs e)
        {
            colorOverlap.Enabled = chkOverlap.Checked;
            if (layer != null)
            {
                layer.DrawOverlap = chkOverlap.Checked;
                layer.Invalidate();
            }
        }

        private void miExpandAll1_Click(object sender, EventArgs e)
        {
            listView1.ExpandAll();
        }

        private void miCollapseAll1_Click(object sender, EventArgs e)
        {
            listView1.CollapseAll();
        }

        private void miExport1_Click(object sender, EventArgs e)
        {
            listView1.ExpandAll();
            MasterCom.Util.ExcelNPOIManager.ExportToExcel(listView1);
        }

        private void miExpandAll2_Click(object sender, EventArgs e)
        {
            listView2.ExpandAll();
        }

        private void miCollapseAll2_Click(object sender, EventArgs e)
        {
            listView2.CollapseAll();
        }

        private void miExport2_Click(object sender, EventArgs e)
        {
            listView2.ExpandAll();
            MasterCom.Util.ExcelNPOIManager.ExportToExcel(listView2);
        }

        private void miExpandAllOverlap_Click(object sender, EventArgs e)
        {
            listViewOverlap.ExpandAll();
        }

        private void miCollapseAllOverlap_Click(object sender, EventArgs e)
        {
            listViewOverlap.CollapseAll();
        }

        private void miExportOverlap_Click(object sender, EventArgs e)
        {
            listViewOverlap.ExpandAll();
            MasterCom.Util.ExcelNPOIManager.ExportToExcel(listViewOverlap);
        }

        private void listView_DoubleClick(object sender, EventArgs e)
        {
            setSelected(hostBlocks);
            setSelected(guestBlocks);
            TreeListView view = sender as TreeListView;
            if (view.SelectedObject is EventBlock)
            {
                EventBlock blk = view.SelectedObject as EventBlock;
                double minLng = double.MaxValue;
                double minLat = double.MaxValue;
                double maxLng = double.MinValue;
                double maxLat = double.MinValue;
                foreach (Event item in blk.AbnormalEvents)
                {
                    item.Selected = true;
                    minLng = Math.Min(minLng, item.Longitude);
                    minLat = Math.Min(minLat, item.Latitude);
                    maxLng = Math.Max(maxLng, item.Longitude);
                    maxLat = Math.Max(maxLat, item.Latitude);
                }
                DbRect bound = new DbRect(minLng, minLat, maxLng, maxLat);
                MainModel.MainForm.GetMapForm().GoToView(bound);
            }
            else if (view.SelectedObject is Event)
            {
                Event evt = view.SelectedObject as Event;
                evt.Selected = true;
                MainModel.MainForm.GetMapForm().GoToView(evt.Longitude, evt.Latitude, 6000);
            }
            MainModel.IsDrawEventResult = false;
        }

        private void setSelected(List<EventBlock> blocks)
        {
            if (blocks != null)
            {
                foreach (EventBlock blk in blocks)
                {
                    foreach (Event item in blk.AbnormalEvents)
                    {
                        item.Selected = false;
                    }
                }
            }
        }
    }
}
