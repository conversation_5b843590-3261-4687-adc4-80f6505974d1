﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTDIYCellWeakCoverByCellDir_TDScan : ZTDIYCellWeakCoverByCellDir_GScan
    {
        private static ZTDIYCellWeakCoverByCellDir_TDScan intance = null;
        public new static ZTDIYCellWeakCoverByCellDir_TDScan GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new ZTDIYCellWeakCoverByCellDir_TDScan();
                    }
                }
            }
            return intance;
        }

        protected ZTDIYCellWeakCoverByCellDir_TDScan()
            : base()
        {
            isAddSampleToDTDataManager = false;
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.TD_SCAN);
            carrierID = CarrierType.ChinaMobile;
        }

        public override string Name
        {
            get { return "弱覆盖小区_TD扫频"; }
        }
        
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 16000, 16007, this.Name);
        }

        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            List<object> columnsDef = new List<object>();

            Dictionary<string, object> param = new Dictionary<string, object>();
            param["param_name"] = "TDS_PCCPCH_RSCP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            Dictionary<string, object> tmpDic = new Dictionary<string, object>();
            tmpDic.Add("name", (object)"TD扫频");
            tmpDic.Add("themeName", (object)"TDSCAN_PCCPCH_RSCP");
            tmpDic.Add("columnsDef", (object)columnsDef);

            DIYSampleGroup group = new DIYSampleGroup();
            group.Param = tmpDic;
            return group;
        }

        protected override float? getBestRxLev(TestPoint tp)
        {
            if (tp is ScanTestPoint_TD)
            {
                float? rxLev = (float?)tp["TDS_PCCPCH_RSCP", 0];
                if (rxLev == null || rxLev < -120 || rxLev > -10)
                {
                    return null;
                }
                return rxLev;
            }
            return null;
        }

        protected override void getWeakCellDic(List<GridForCellWeakCover> weakGridList, Dictionary<ICell, CellWeakCoverByGridInfoBase> weakCellDic,
          double lonDiff, double latDiff)
        {
            List<TDCell> cellList = getTDCellsOfRegion();
            int iLoop = 1;
            WaitBox.Text = "开始获取弱覆盖小区...";
            foreach (TDCell cell in cellList)
            {
                dealWeakCellByWeakGrid(weakGridList, weakCellDic, lonDiff, latDiff, cell);
                WaitBox.ProgressPercent = (int)(100.0 * iLoop++ / cellList.Count);
            }
        }

        protected override CellWeakCoverByGridInfoBase getCellWeakCoverByGrid(ICell cell)
        {
            if (cell is TDCell)
            {
                TDCell tdCell = cell as TDCell;
                CellWeakCoverByGridInfoTd weakCell = new CellWeakCoverByGridInfoTd();
                weakCell.FillCellData(tdCell);
                //weakCell.Calculate();
                return weakCell;
            }
            return null;
        }

        private List<TDCell> getTDCellsOfRegion()
        {
            List<TDCell> cellList = new List<TDCell>();
            int index = 1;
            List<TDCell> curCells = MainModel.CellManager.GetCurrentTDCells();
            WaitBox.Text = "开始获取选择区域内小区...";
            foreach (TDCell cell in curCells)
            {
                if (cell.Type == TDNodeBType.Outdoor && condition.Geometorys.GeoOp.Contains(cell.Longitude, cell.Latitude))
                {
                    cellList.Add(cell);
                }
                WaitBox.ProgressPercent = (int)(100.0 * index++ / curCells.Count);
            }
            return cellList;
        }

        #region Background
        public override BackgroundFuncType FuncType
        {
            get { return BackgroundFuncType.TD扫频; }
        }
        #endregion
    }

    public class ZTDIYCellWeakCoverByCellDir_WCDMAScan : ZTDIYCellWeakCoverByCellDir_GScan
    {
        private static ZTDIYCellWeakCoverByCellDir_WCDMAScan intance = null;
        public new static ZTDIYCellWeakCoverByCellDir_WCDMAScan GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new ZTDIYCellWeakCoverByCellDir_WCDMAScan();
                    }
                }
            }
            return intance;
        }

        protected ZTDIYCellWeakCoverByCellDir_WCDMAScan()
            : base()
        {
            isAddSampleToDTDataManager = false;
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.WCDMA_SCAN);
            carrierID = CarrierType.ChinaUnicom;
        }

        public override string Name
        {
            get { return "弱覆盖小区_WCDMA扫频"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 32000, 32007, this.Name);
        }

        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            List<object> columnsDef = new List<object>();

            Dictionary<string, object> param = new Dictionary<string, object>();
            param["param_name"] = "WS_CPICHTotalRSCP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            Dictionary<string, object> tmpDic = new Dictionary<string, object>();
            tmpDic.Add("name", (object)"WCDMA扫频");
            tmpDic.Add("themeName", (object)"WCDAMSCAN_CPICHTotalRSCP");
            tmpDic.Add("columnsDef", (object)columnsDef);

            DIYSampleGroup group = new DIYSampleGroup();
            group.Param = tmpDic;
            return group;
        }

        protected override float? getBestRxLev(TestPoint tp)
        {
            if (tp is ScanTestPoint_W)
            {
                float? rxLev = (float?)tp["WS_CPICHTotalRSCP", 0];
                if (rxLev == null || rxLev < -120 || rxLev > -10)
                {
                    return null;
                }
                return rxLev;
            }
            return null;
        }

        protected override void getWeakCellDic(List<GridForCellWeakCover> weakGridList, Dictionary<ICell, CellWeakCoverByGridInfoBase> weakCellDic,
         double lonDiff, double latDiff)
        {
            List<WCell> cellList = getWCellsOfRegion();
            int iLoop = 1;
            WaitBox.Text = "开始获取弱覆盖小区...";
            foreach (WCell cell in cellList)
            {
                dealWeakCellByWeakGrid(weakGridList, weakCellDic, lonDiff, latDiff, cell);
                WaitBox.ProgressPercent = (int)(100.0 * iLoop++ / cellList.Count);
            }
        }

        protected override CellWeakCoverByGridInfoBase getCellWeakCoverByGrid(ICell cell)
        {
            if (cell is WCell)
            {
                WCell wCell = cell as WCell;
                CellWeakCoverByGridInfoWcdma weakCell = new CellWeakCoverByGridInfoWcdma();
                weakCell.FillCellData(wCell);
                //weakCell.Calculate();
                return weakCell;
            }
            return null;
        }

        private List<WCell> getWCellsOfRegion()
        {
            List<WCell> cellList = new List<WCell>();
            int index = 1;
            List<WCell> curCells = MainModel.CellManager.GetCurrentWCells();
            WaitBox.Text = "开始获取选择区域内小区...";
            foreach (WCell cell in curCells)
            {
                if (cell.Type == WNodeBType.Outdoor && condition.Geometorys.GeoOp.Contains(cell.Longitude, cell.Latitude))
                {
                    cellList.Add(cell);
                }
                WaitBox.ProgressPercent = (int)(100.0 * index++ / curCells.Count);
            }
            return cellList;
        }

        #region Background
        public override BackgroundFuncType FuncType
        {
            get { return BackgroundFuncType.WCDMA扫频; }
        }
        #endregion
    }
}
