﻿using System;
using System.Collections.Generic;
using System.IO;
using MasterCom.Util.UiEx;
using MasterCom.RAMS.Model;
using Excel = Microsoft.Office.Interop.Excel;
using System.Text;
using MasterCom.RAMS.ZTFunc.ZTStationAcceptance_XJ;

namespace MasterCom.RAMS.ZTFunc
{
    public class LteStationAcceptManager : IStationAcceptManager
    {
        public const int TddMaxCellCount = 9;
        private static readonly string workDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "userdata/LteStationAcceptance");
        protected LteStationAcceptCondition acceptCond = null;
        protected List<LteStationAcceptBase> acceptorList = null;

        protected static readonly log4net.ILog log = log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        protected string errMsg;
        public string ErrMsg
        {
            get { return errMsg; }
        }

        protected string hasExportedFiles;
        public string HasExportedFiles
        {
            get { return hasExportedFiles; }
        }

        public virtual void SetAcceptCond(LteStationAcceptCondition cond)
        {
            errMsg = "";
            acceptCond = cond;
            acceptorList = new List<LteStationAcceptBase>()
            {
                new AcpCellName(),
                new AcpBtsInfo(),
                //new AcpCellParameter(),
                cond.IsByFile ? new AcpFtpDownload() : new AcpFtpDownloadEx(),
                cond.IsByFile ? new AcpFtpUpload() : (LteStationAcceptBase)new AcpFtpUploadEx(),
                new Acp34ReselectRate(),
                new Acp24ReselectRate(),
                new AcpInnerHandover(),
                new AcpCsfbRate(),
                new AcpRrcRate(),
                new AcpAccRate(),
                new AcpErabRate(),
                new AcpCoverPicture(),
                new AcpTitle(),
                //new AcpHomePage(),
                new AcpVolteVoiceMo(),
                new AcpVolteVoiceMt(),
                new AcpHandoverPic()
            };
        }

        public virtual void AnalyzeFile(Model.FileInfo fileInfo, DTFileDataManager fileManager)
        {
            try
            {
                ICell targetCell = GetTargetCell(fileManager);
                if (targetCell == null || targetCell is UnknowCell)
                {
                    log.Info(string.Format("文件{0}未找到目标小区", fileInfo.Name));
                    return;
                }

                LTEBTS bts = (LTEBTS)targetCell.Site;
                string btsName = getTddBtsName(bts.Name);
                List<LTECell> tddCells = getTddCells(bts);

                foreach (LteStationAcceptBase acp in acceptorList)
                {
                    acp.AnalyzeFile(fileInfo, fileManager, targetCell, btsName, tddCells);
                }
            }
            catch
            {
                Clear();
                throw;
            }
        }

        private string getTddBtsName(string btsName)
        {
            //存在共站情况,站点名可能包含-FDD,-NB
            var tddBtsName = btsName;
            int len = btsName.LastIndexOf('-');
            string btsNamePostfix = btsName.Substring(len);
            if (!judgeTdd(btsNamePostfix))
            {
                tddBtsName = btsName.Substring(0, len);
            }

            return tddBtsName;
        }

        protected List<LTECell> getTddCells(LTEBTS bts)
        {
            List<LTECell> tddCells = new List<LTECell>();
            foreach (LTECell btsLteCell in bts.Cells)
            {
                if (judgeTdd(btsLteCell.Name))
                {
                    tddCells.Add(btsLteCell);
                }
            }
            tddCells.Sort((a, b) => a.Name.ToUpper().CompareTo(b.Name.ToUpper()));
            return tddCells;
        }

        protected bool judgeTdd(string name)
        {
            if (name.Contains("-FDD") || name.Contains("-NB"))
            {
                return false;
            }
            return true;
        }


        public void DoWorkAfterAnalyze()
        {
            try
            {
                Dictionary<string, int> btsDic = FindAllBts();
                if (btsDic.Count > 1)
                {
                    StringBuilder btsNames = new StringBuilder();
                    foreach (var item in btsDic)
                    {
                        btsNames.Append("[" + item + "],");
                    }
                    errMsg = "选择了多个站文件" + btsNames.ToString().TrimEnd(',');
                }
                else
                {
                    CreateFileForBts(btsDic);
                }
            }
            finally
            {
                Clear();
            }
        }

        protected virtual void CreateFileForBts(Dictionary<string, int> btsDic)
        {
            StringBuilder exportedFiles = new StringBuilder();
            foreach (string bts in btsDic.Keys)
            {
                int cellCount = 0;
                foreach (LteStationAcceptBase acp in acceptorList)
                {
                    cellCount = Math.Max(cellCount, acp.GetCellCount(bts));
                }
                bool isValid = judgeValidFile(bts, cellCount);
                if (isValid)
                {
                    string targetFile = GetTargetFile(bts, cellCount, acceptCond.SaveFolder);
                    if (!string.IsNullOrEmpty(targetFile))
                    {
                        exportFile(exportedFiles, bts, targetFile, cellCount);
                    }
                    saveCurReport(bts, cellCount);
                }
            }
            hasExportedFiles = exportedFiles.ToString().TrimEnd(',');
        }

        protected virtual void exportFile(StringBuilder exportedFiles, string bts, string targetFile, int cellCount)
        {
            Excel.Application xlApp = null;
            try
            {
                WaitTextBox.Text = "正在导出Excel...";
                xlApp = new Excel.Application();
                xlApp.Visible = false;
                Excel.Workbook eBook = xlApp.Workbooks.Open(targetFile,
                    Type.Missing, Type.Missing, Type.Missing, Type.Missing,
                    Type.Missing, Type.Missing, Type.Missing, Type.Missing,
                    Type.Missing, Type.Missing, Type.Missing, Type.Missing,
                    Type.Missing, Type.Missing);

                foreach (LteStationAcceptBase acp in acceptorList)
                {
                    try
                    {
                        if (acp is AcpInnerHandover)
                        {
                            (acp as AcpInnerHandover).ReSetResultGridValue(cellCount);
                        }
                        acp.FillResult(bts, eBook);
                    }
                    catch( Exception e)
                    {
                        log.Error(acp.GetType().ToString() + "["  + acp.GetCellCount(bts)  + "]" + e.StackTrace);
                        throw;
                    }
                }

                eBook.Save();

                if (!MainModel.GetInstance().BackgroundStarted)
                {
                    WaitTextBox.Text = "正在上传结果...";
                    AcceptHistory.UploaderManager.Instance.UploadResult(eBook, bts, cellCount);
                }

                eBook.Close(Type.Missing, Type.Missing, Type.Missing);
                exportedFiles.Append(bts);
                exportedFiles.Append(",");
            }
            finally
            {
                if (xlApp != null)
                {
                    xlApp.Quit();
                }
            }
        }

        protected virtual void saveCurReport(string bts, int cellCount)
        {
            string path = getPath();
            if (string.IsNullOrEmpty(path))
            {
                return;
            }

            string targetFile = GetTargetFile(bts, cellCount, path);
            if (!string.IsNullOrEmpty(targetFile))
            {
                exportFile(new StringBuilder(), bts, targetFile, cellCount);
            }
        }

        protected virtual string getPath()
        {
            return Singleton<TddStationAcceptConfigHelper>.Instance.GetCurSavePath();
        }

        protected void Clear()
        {
            foreach (LteStationAcceptBase acp in acceptorList)
            {
                acp.Clear();
            }
        }

        protected virtual LTECell GetTargetCell(DTFileDataManager fileManager)
        {
            bool isHandoverFile = fileManager.FileName.Contains("系统内切换");
            LTECell targeCell = null;
            foreach (TestPoint tp in fileManager.TestPoints)
            {
                LTECell cell = StationAcceptCellHelper_XJ.Instance.GetLTECell(tp);
                if (cell == null)
                {
                    continue;
                }
                string keyStr = cell.Name;
                if (isHandoverFile)
                {
                    keyStr = getBtsName(fileManager.FileName);
                    if (!string.IsNullOrEmpty(keyStr) && cell.Name.Contains(keyStr))
                    {
                        targeCell = cell;
                        break;
                    }
                }
                else if (fileManager.FileName.Contains(keyStr.Trim()))
                {
                    targeCell = cell;
                    break;
                }
            }
            return targeCell;
        }

        protected string getBtsName(string fileName)
        {
            string btsNameTmp = "";
            string[] names = fileName.Split('_');
            if (names.Length >= 3)
            {
                int suffix = names[2].LastIndexOf('.');
                if (suffix > 0)
                {
                    names[2] = names[2].Remove(suffix);
                }
                btsNameTmp = names[2].Substring(0, names[2].Length - 5);
            }
            return btsNameTmp;
        }

        protected virtual bool judgeValidFile(string btsName, int cellCount)
        {
            if (cellCount > TddMaxCellCount)
            {
                errMsg = $"基站{btsName}小区数超过{TddMaxCellCount}个，不支持报告导出";
                return false;
            }
            return true;
        }

        public static string GetTargetFile(string btsName, int cellCount, string saveFolder)
        {
            string templateFile;
            if (cellCount <= 3)
            {
                templateFile = "LTE新站验收模板_3扇区.xlsx";
            }
            else if (cellCount <= 6)
            {
                templateFile = "LTE新站验收模板_6扇区.xlsx";
            }
            else
            {
                templateFile = "LTE新站验收模板_9扇区.xlsx";
            }
            templateFile = Path.Combine(workDir, templateFile);

            string targetFile = string.Format("LTE新站验收_{0}.xlsx", btsName);
            targetFile = Path.Combine(saveFolder, targetFile);
            if (File.Exists(targetFile))
            {
                File.Delete(targetFile);
            }
            File.Copy(templateFile, targetFile);
            return targetFile;
        }

        private Dictionary<string, int> FindAllBts()
        {
            Dictionary<string, int> btsDic = new Dictionary<string, int>();
            foreach (LteStationAcceptBase acp in acceptorList)
            {
                foreach (string bts in acp.BtsNames)
                {
                    if (!btsDic.ContainsKey(bts))
                    {
                        btsDic.Add(bts, 0);
                    }
                    ++btsDic[bts];
                }
            }
            return btsDic;
        }
    }
}
