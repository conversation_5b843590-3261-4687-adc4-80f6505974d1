﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc.AreaArchiveManage
{
    public abstract class TestPointBaseDeal
    {
        protected TestPointBaseDeal() { }

        public abstract string ThemeName { get; }

        public abstract ICell GetMainCell(TestPoint tp);

        public abstract object GetLAC(TestPoint tp);

        public abstract object GetCI(TestPoint tp);

        public abstract object Get<PERSON>CH(TestPoint tp);

        public abstract object GetBSIC(TestPoint tp);

        public abstract object GetRxlev(TestPoint tp);

        public abstract ICell GetNbCell(TestPoint tp, int idx);

        public abstract object GetNbBCCH(TestPoint tp, int idx);

        public abstract object GetNbBSIC(TestPoint tp, int idx);

        public abstract object GetNbRxlev(TestPoint tp, int idx);
    }

    public class TestPointGsmDeal : TestPointBaseDeal
    {
        public TestPointGsmDeal() { }

        public override string ThemeName
        {
            get { return "GSM RxLevSub"; }
        }

        public override ICell GetMainCell(TestPoint tp)
        {
            Cell cell = CellManager.GetInstance().GetNearestCell(tp.DateTime, (ushort?)(int?)GetLAC(tp), (ushort?)(int?)GetCI(tp),
                (short?)GetBCCH(tp), (byte?)GetBSIC(tp), tp.Longitude, tp.Latitude);

            return cell;
        }

        public override object GetLAC(TestPoint tp)
        {
            return tp["LAC"];
        }

        public override object GetCI(TestPoint tp)
        {
            return tp["CI"];
        }

        public override object GetBCCH(TestPoint tp)
        {
            return tp["BCCH"];
        }

        public override object GetBSIC(TestPoint tp)
        {
            return tp["BSIC"];
        }

        public override object GetRxlev(TestPoint tp)
        {
            return tp["RxLevSub"];
        }

        public override ICell GetNbCell(TestPoint tp, int idx)
        {
            Cell cell = CellManager.GetInstance().GetNearestCell(tp.DateTime, null, null,
                (short?)GetNbBCCH(tp, idx), (byte?)GetNbBSIC(tp, idx), tp.Longitude, tp.Latitude);

            return cell;
        }

        public override object GetNbBCCH(TestPoint tp, int idx)
        {
            return tp["N_BCCH", idx];
        }

        public override object GetNbBSIC(TestPoint tp, int idx)
        {
            return tp["N_BSIC", idx];
        }

        public override object GetNbRxlev(TestPoint tp, int idx)
        {
            return tp["N_RxLev", idx];
        }
    }

    public class TestPointLteGsmDeal : TestPointBaseDeal
    {
        public TestPointLteGsmDeal() { }

        public override string ThemeName
        {
            get { return "TD_LTE_GSM_RxLev"; }
        }

        public override ICell GetMainCell(TestPoint tp)
        {
            Cell cell = CellManager.GetInstance().GetNearestCell(tp.DateTime, (ushort?)(int?)GetLAC(tp), (ushort?)(int?)GetCI(tp),
                (short?)(int?)GetBCCH(tp), (byte?)GetBSIC(tp), tp.Longitude, tp.Latitude);

            return cell;
        }

        public override object GetLAC(TestPoint tp)
        {
            return tp["lte_gsm_SC_LAC"];
        }

        public override object GetCI(TestPoint tp)
        {
            return tp["lte_gsm_SC_CI"];
        }

        public override object GetBCCH(TestPoint tp)
        {
            return tp["lte_gsm_SC_BCCH"];
        }

        public override object GetBSIC(TestPoint tp)
        {
            return tp["lte_gsm_SC_BSIC"];
        }

        public override object GetRxlev(TestPoint tp)
        {
            return tp["lte_gsm_DM_RxLevSub"];
        }

        public override ICell GetNbCell(TestPoint tp, int idx)
        {
            Cell cell = CellManager.GetInstance().GetNearestCell(tp.DateTime, null, null,
                (short?)(int?)GetNbBCCH(tp, idx), (byte?)GetNbBSIC(tp, idx), tp.Longitude, tp.Latitude);

            return cell;
        }

        public override object GetNbBCCH(TestPoint tp, int idx)
        {
            return tp["lte_gsm_NC_BCCH", idx];
        }

        public override object GetNbBSIC(TestPoint tp, int idx)
        {
            return tp["lte_gsm_NC_BSIC", idx];
        }

        public override object GetNbRxlev(TestPoint tp, int idx)
        {
            return tp["lte_gsm_NC_RxLev", idx];
        }
    }

    public class TestPointLteFddGsmDeal : TestPointBaseDeal
    {
        public TestPointLteFddGsmDeal() { }

        public override string ThemeName
        {
            get { return "LTE_FDD_GSM_RxLev"; }
        }

        public override ICell GetMainCell(TestPoint tp)
        {
            Cell cell = CellManager.GetInstance().GetNearestCell(tp.DateTime, (ushort?)(int?)GetLAC(tp), (ushort?)(int?)GetCI(tp),
                (short?)(int?)GetBCCH(tp), (byte?)GetBSIC(tp), tp.Longitude, tp.Latitude);

            return cell;
        }

        public override object GetLAC(TestPoint tp)
        {
            return tp["lte_fdd_gsm_SC_LAC"];
        }

        public override object GetCI(TestPoint tp)
        {
            return tp["lte_fdd_gsm_SC_CI"];
        }

        public override object GetBCCH(TestPoint tp)
        {
            return tp["lte_fdd_gsm_SC_BCCH"];
        }

        public override object GetBSIC(TestPoint tp)
        {
            return tp["lte_fdd_gsm_SC_BSIC"];
        }

        public override object GetRxlev(TestPoint tp)
        {
            return tp["lte_fdd_gsm_DM_RxLevSub"];
        }

        public override ICell GetNbCell(TestPoint tp, int idx)
        {
            Cell cell = CellManager.GetInstance().GetNearestCell(tp.DateTime, null, null,
                (short?)(int?)GetNbBCCH(tp, idx), (byte?)GetNbBSIC(tp, idx), tp.Longitude, tp.Latitude);

            return cell;
        }

        public override object GetNbBCCH(TestPoint tp, int idx)
        {
            return tp["lte_fdd_gsm_NC_BCCH", idx];
        }

        public override object GetNbBSIC(TestPoint tp, int idx)
        {
            return tp["lte_fdd_gsm_NC_BSIC", idx];
        }

        public override object GetNbRxlev(TestPoint tp, int idx)
        {
            return tp["lte_fdd_gsm_NC_RxLev", idx];
        }
    }

    public class TestPointTDDeal : TestPointBaseDeal
    {
        public TestPointTDDeal() { }

        public override string ThemeName
        {
            get { return "TD_PCCPCH_RSCP"; }
        }

        public override ICell GetMainCell(TestPoint tp)
        {
            TDCell cell = CellManager.GetInstance().GetNearestTDCell(tp.DateTime, (ushort?)(int?)GetLAC(tp), (ushort?)(int?)GetCI(tp),
                (short?)(int?)GetBCCH(tp), (byte?)(int?)GetBSIC(tp), tp.Longitude, tp.Latitude);

            return cell;
        }

        public override object GetLAC(TestPoint tp)
        {
            return tp["TD_SCell_LAC"];
        }

        public override object GetCI(TestPoint tp)
        {
            return tp["TD_SCell_CI"];
        }

        public override object GetBCCH(TestPoint tp)
        {
            return tp["TD_SCell_UARFCN"];
        }

        public override object GetBSIC(TestPoint tp)
        {
            return tp["TD_SCell_CPI"];
        }

        public override object GetRxlev(TestPoint tp)
        {
            return tp["TD_PCCPCH_RSCP"];
        }

        public override ICell GetNbCell(TestPoint tp, int idx)
        {
            TDCell cell = CellManager.GetInstance().GetNearestTDCell(tp.DateTime, null, null,
                (short?)(int?)GetNbBCCH(tp, idx), (byte?)(int?)GetNbBSIC(tp, idx), tp.Longitude, tp.Latitude);

            return cell;
        }

        public override object GetNbBCCH(TestPoint tp, int idx)
        {
            return tp["TD_NCell_UARFCN", idx];
        }

        public override object GetNbBSIC(TestPoint tp, int idx)
        {
            return tp["TD_NCell_CPI", idx];
        }

        public override object GetNbRxlev(TestPoint tp, int idx)
        {
            return tp["TD_NCell_PCCPCH_RSCP", idx];
        }
    }

    public class TestPointLteTDDeal : TestPointBaseDeal
    {
        public TestPointLteTDDeal() { }

        public override string ThemeName
        {
            get { return "TD_LTE_TD_PCCPCH_RSCP"; }
        }

        public override ICell GetMainCell(TestPoint tp)
        {
            TDCell cell = CellManager.GetInstance().GetNearestTDCell(tp.DateTime, (ushort?)(int?)GetLAC(tp), (ushort?)(int?)GetCI(tp),
                (short?)(int)GetBCCH(tp), (byte?)(int?)GetBSIC(tp), tp.Longitude, tp.Latitude);

            return cell;
        }

        public override object GetLAC(TestPoint tp)
        {
            return tp["lte_td_SC_LAC"];
        }

        public override object GetCI(TestPoint tp)
        {
            return tp["lte_td_SC_CellID"];
        }

        public override object GetBCCH(TestPoint tp)
        {
            return tp["lte_td_SC_UARFCN"];
        }

        public override object GetBSIC(TestPoint tp)
        {
            return tp["lte_td_SC_CPI"];
        }

        public override object GetRxlev(TestPoint tp)
        {
            return tp["lte_td_DM_PCCPCH_RSCP"];
        }

        public override ICell GetNbCell(TestPoint tp, int idx)
        {
            TDCell cell = CellManager.GetInstance().GetNearestTDCell(tp.DateTime, null, null,
                (short?)(int?)GetNbBCCH(tp, idx), (byte?)(int?)GetNbBSIC(tp, idx), tp.Longitude, tp.Latitude);

            return cell;
        }

        public override object GetNbBCCH(TestPoint tp, int idx)
        {
            return tp["lte_td_NCell_UARFCN", idx];
        }

        public override object GetNbBSIC(TestPoint tp, int idx)
        {
            return tp["lte_td_NCell_CPI", idx];
        }

        public override object GetNbRxlev(TestPoint tp, int idx)
        {
            return tp["lte_td_NCell_PCCPCH_RSCP", idx];
        }
    }

    public class TestPointWDeal : TestPointBaseDeal
    {
        public TestPointWDeal() { }

        public override string ThemeName
        {
            get { return "W_TotalRSCP"; }
        }

        public override ICell GetMainCell(TestPoint tp)
        {
            WCell cell = CellManager.GetInstance().GetNearestWCell(tp.DateTime, (ushort?)(int?)GetLAC(tp), (ushort?)(int?)GetCI(tp),
                (short?)(int?)GetBCCH(tp), (byte?)(int?)GetBSIC(tp), tp.Longitude, tp.Latitude);

            return cell;
        }

        public override object GetLAC(TestPoint tp)
        {
            return tp["W_SysLAI"];
        }

        public override object GetCI(TestPoint tp)
        {
            return tp["W_SysCellID"];
        }

        public override object GetBCCH(TestPoint tp)
        {
            return tp["W_frequency"];
        }

        public override object GetBSIC(TestPoint tp)
        {
            return tp["W_Reference_PSC"];
        }

        public override object GetRxlev(TestPoint tp)
        {
            return tp["W_Reference_RSCP"];
        }

        public override ICell GetNbCell(TestPoint tp, int idx)
        {
            WCell cell = CellManager.GetInstance().GetNearestWCell(tp.DateTime, null, null,
                (short?)(int?)GetNbBCCH(tp, idx), (byte?)(int?)GetNbBSIC(tp, idx), tp.Longitude, tp.Latitude);

            return cell;
        }

        public override object GetNbBCCH(TestPoint tp, int idx)
        {
            return tp["W_SNeiFreq", idx];
        }

        public override object GetNbBSIC(TestPoint tp, int idx)
        {
            return tp["W_SNeiPSC", idx];
        }

        public override object GetNbRxlev(TestPoint tp, int idx)
        {
            return tp["W_SNeiRSCP", idx];
        }
    }

    public class TestPointLteFddWDeal : TestPointBaseDeal
    {
        public TestPointLteFddWDeal() { }

        public override string ThemeName
        {
            get { return "LTE_FDD_W_TotalRSCP"; }
        }

        public override ICell GetMainCell(TestPoint tp)
        {
            WCell cell = CellManager.GetInstance().GetNearestWCell(tp.DateTime, (ushort?)(int?)GetLAC(tp), (ushort?)(int?)GetCI(tp),
                (short?)(int?)GetBCCH(tp), (byte?)GetBSIC(tp), tp.Longitude, tp.Latitude);

            return cell;
        }

        public override object GetLAC(TestPoint tp)
        {
            return tp["lte_fdd_wcdma_SysLAI"];
        }

        public override object GetCI(TestPoint tp)
        {
            return tp["lte_fdd_wcdma_SysCellID"];
        }

        public override object GetBCCH(TestPoint tp)
        {
            return tp["lte_fdd_wcdma_frequency"];
        }

        public override object GetBSIC(TestPoint tp)
        {
            return tp["lte_fdd_wcdma_Reference_PSC"];
        }

        public override object GetRxlev(TestPoint tp)
        {
            return tp["lte_fdd_wcdma_TotalRSCP"];
        }

        public override ICell GetNbCell(TestPoint tp, int idx)
        {
            WCell cell = CellManager.GetInstance().GetNearestWCell(tp.DateTime, null, null,
                (short?)(int?)GetNbBCCH(tp, idx), (byte?)GetNbBSIC(tp, idx), tp.Longitude, tp.Latitude);

            return cell;
        }

        public override object GetNbBCCH(TestPoint tp, int idx)
        {
            return tp["lte_fdd_wcdma_SNeiFreq", idx];
        }

        public override object GetNbBSIC(TestPoint tp, int idx)
        {
            return tp["lte_fdd_wcdma_SNeiPSC", idx];
        }

        public override object GetNbRxlev(TestPoint tp, int idx)
        {
            return tp["lte_fdd_wcdma_SNeiRSCP", idx];
        }
    }

    public class TestPointLteDeal : TestPointBaseDeal
    {
        public TestPointLteDeal() { }

        public override string ThemeName
        {
            get { return "TD_LTE_RSRP"; }
        }

        public override ICell GetMainCell(TestPoint tp)
        {
            LTECell cell = CellManager.GetInstance().GetNearestLTECell(tp.DateTime, (ushort?)(int?)GetLAC(tp), (ushort?)(int?)GetCI(tp),
                (short?)(int?)GetBCCH(tp), (byte?)GetBSIC(tp), tp.Longitude, tp.Latitude);

            return cell;
        }

        public override object GetLAC(TestPoint tp)
        {
            return tp["lte_TAC"];
        }

        public override object GetCI(TestPoint tp)
        {
            return tp["lte_ECI"];
        }

        public override object GetBCCH(TestPoint tp)
        {
            return tp["lte_EARFCN"];
        }

        public override object GetBSIC(TestPoint tp)
        {
            return tp["lte_PCI"];
        }

        public override object GetRxlev(TestPoint tp)
        {
            return tp["lte_RSRP"];
        }

        public override ICell GetNbCell(TestPoint tp, int idx)
        {
            LTECell cell = CellManager.GetInstance().GetNearestLTECell(tp.DateTime, null, null,
                (short?)(int?)GetNbBCCH(tp, idx), (byte?)GetNbBSIC(tp, idx), tp.Longitude, tp.Latitude);

            return cell;
        }

        public override object GetNbBCCH(TestPoint tp, int idx)
        {
            return tp["lte_NCell_EARFCN", idx];
        }

        public override object GetNbBSIC(TestPoint tp, int idx)
        {
            return tp["lte_NCell_PCI", idx];
        }

        public override object GetNbRxlev(TestPoint tp, int idx)
        {
            return tp["lte_NCell_RSRP", idx];
        }
    }

    public class TestPointLteFddDeal : TestPointBaseDeal
    {
        public TestPointLteFddDeal() { }

        public override string ThemeName
        {
            get { return "LTE_FDD:RSRP"; }
        }

        public override ICell GetMainCell(TestPoint tp)
        {
            LTECell cell = CellManager.GetInstance().GetNearestLTECell(tp.DateTime, (ushort?)(int?)GetLAC(tp), (ushort?)(int?)GetCI(tp),
                (short?)(int?)GetBCCH(tp), (byte?)GetBSIC(tp), tp.Longitude, tp.Latitude);

            return cell;
        }

        public override object GetLAC(TestPoint tp)
        {
            return tp["lte_fdd_SCell_LAC"];
        }

        public override object GetCI(TestPoint tp)
        {
            return tp["lte_fdd_SCell_CI"];
        }

        public override object GetBCCH(TestPoint tp)
        {
            return tp["lte_fdd_EARFCN"];
        }

        public override object GetBSIC(TestPoint tp)
        {
            return tp["lte_fdd_PCI"];
        }

        public override object GetRxlev(TestPoint tp)
        {
            return tp["lte_fdd_RSRP"];
        }

        public override ICell GetNbCell(TestPoint tp, int idx)
        {
            LTECell cell = CellManager.GetInstance().GetNearestLTECell(tp.DateTime, null, null,
                (short?)(int?)GetNbBCCH(tp, idx), (byte?)GetNbBSIC(tp, idx), tp.Longitude, tp.Latitude);

            return cell;
        }

        public override object GetNbBCCH(TestPoint tp, int idx)
        {
            return tp["lte_fdd_NCell_EARFCN", idx];
        }

        public override object GetNbBSIC(TestPoint tp, int idx)
        {
            return tp["lte_fdd_NCell_PCI", idx];
        }

        public override object GetNbRxlev(TestPoint tp, int idx)
        {
            return tp["lte_fdd_NCell_RSRP", idx];
        }
    }

    public class TestPointGsmScanDeal : TestPointBaseDeal
    {
        public TestPointGsmScanDeal() { }

        public override string ThemeName
        {
            get { return "GSM_SCAN_RxLev"; }
        }

        public override ICell GetMainCell(TestPoint tp)
        {
            ICell cell = GetNbCell(tp, 0);

            return cell;
        }

        public override object GetLAC(TestPoint tp)
        {
            return null;
        }

        public override object GetCI(TestPoint tp)
        {
            return null;
        }

        public override object GetBCCH(TestPoint tp)
        {
            return null;
        }

        public override object GetBSIC(TestPoint tp)
        {
            return null;
        }

        public override object GetRxlev(TestPoint tp)
        {
            return null;
        }

        public override ICell GetNbCell(TestPoint tp, int idx)
        {
            Cell cell = CellManager.GetInstance().GetNearestCell(tp.DateTime, null, null,
                (short?)(int?)GetNbBCCH(tp, idx), (byte?)GetNbBSIC(tp, idx), tp.Longitude, tp.Latitude);

            return cell;
        }

        public override object GetNbBCCH(TestPoint tp, int idx)
        {
            return tp["GSCAN_BCCH", idx];
        }

        public override object GetNbBSIC(TestPoint tp, int idx)
        {
            return tp["GSCAN_BSIC", idx];
        }

        public override object GetNbRxlev(TestPoint tp, int idx)
        {
            return tp["GSCAN_RxLev", idx];
        }
    }

    public class TestPointTDScanDeal : TestPointBaseDeal
    {
        public TestPointTDScanDeal() { }

        public override string ThemeName
        {
            get { return "TDSCAN_PCCPCH_RSCP"; }
        }

        public override ICell GetMainCell(TestPoint tp)
        {
            ICell cell = GetNbCell(tp, 0);

            return cell;
        }

        public override object GetLAC(TestPoint tp)
        {
            return null;
        }

        public override object GetCI(TestPoint tp)
        {
            return null;
        }

        public override object GetBCCH(TestPoint tp)
        {
            return null;
        }

        public override object GetBSIC(TestPoint tp)
        {
            return null;
        }

        public override object GetRxlev(TestPoint tp)
        {
            return null;
        }

        public override ICell GetNbCell(TestPoint tp, int idx)
        {
            TDCell cell = CellManager.GetInstance().GetNearestTDCell(tp.DateTime, null, null,
                (short?)(int?)GetNbBCCH(tp, idx), (byte?)(int?)GetNbBSIC(tp, idx), tp.Longitude, tp.Latitude);

            return cell;
        }

        public override object GetNbBCCH(TestPoint tp, int idx)
        {
            return tp["TDS_PCCPCH_Channel", idx];
        }

        public override object GetNbBSIC(TestPoint tp, int idx)
        {
            return tp["TDS_PCCPCH_CPI", idx];
        }

        public override object GetNbRxlev(TestPoint tp, int idx)
        {
            return tp["TDS_PCCPCH_RSCP", idx];
        }
    }

    public class TestPointLteScanDeal : TestPointBaseDeal
    {
        public TestPointLteScanDeal() { }

        public override string ThemeName
        {
            get { return "TopN_CELL_Specific_RSRP"; }
        }

        public override ICell GetMainCell(TestPoint tp)
        {
            ICell cell = GetNbCell(tp, 0);

            return cell;
        }

        public override object GetLAC(TestPoint tp)
        {
            return null;
        }

        public override object GetCI(TestPoint tp)
        {
            return null;
        }

        public override object GetBCCH(TestPoint tp)
        {
            return null;
        }

        public override object GetBSIC(TestPoint tp)
        {
            return null;
        }

        public override object GetRxlev(TestPoint tp)
        {
            return null;
        }

        public override ICell GetNbCell(TestPoint tp, int idx)
        {
            LTECell cell = CellManager.GetInstance().GetNearestLTECell(tp.DateTime, null, null,
                (short?)(int?)GetNbBCCH(tp, idx), (byte?)GetNbBSIC(tp, idx), tp.Longitude, tp.Latitude);

            return cell;
        }

        public override object GetNbBCCH(TestPoint tp, int idx)
        {
            return tp["LTESCAN_TopN_EARFCN", idx];
        }

        public override object GetNbBSIC(TestPoint tp, int idx)
        {
            return tp["LTESCAN_TopN_PCI", idx];
        }

        public override object GetNbRxlev(TestPoint tp, int idx)
        {
            return tp["LTESCAN_TopN_PSS_RP", idx];
        }
    }
}
