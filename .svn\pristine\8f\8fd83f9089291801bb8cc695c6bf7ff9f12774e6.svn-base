﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;
using System.Globalization;

namespace MasterCom.RAMS.ZTFunc
{
    /// <summary>
    /// 性能数据详细
    /// </summary>
    class PerDetailEleInfQuery: DIYSQLBase
    {
        public int LAC { get; set; }
        public int CI { get; set; }

        private readonly bool IsTDIn = false;        
        private DateTime timeStart;
        public DateTime TimeStart
        {
            get
            {
                return timeStart;
            }
            set
            {
                timeStart = new DateTime(value.Year, value.Month, value.Day);
            }
        }
        public DateTime TimeEnd { get; set; }
        public PerDetailEleInfQuery(MainModel mainModel, bool IsTDIn)
            : base(mainModel)
        {
            this.IsTDIn = IsTDIn;
            timeStart = DateTime.MaxValue;
            TimeEnd = DateTime.MaxValue;
            LAC = 0;
            CI = 0;
        }
        private readonly string selectSqlTemplateGSM = @"
        select tmdat as '时间',
        无线接通率,
        [话音信道拥塞率(不含切)],
        [话音信道掉话率(不含切)],
        [上行TBF建立成功率],
        [下行TBF建立成功率],
        [PDCH分配成功率],
        [信令信道分配成功率],
        [话音信道分配成功率(不含切)],
        [信令信道拥塞率],
        [非PBGT切换占比],
        [下行话音质量],
        [切换成功率],
        [下行TBF掉线率]
        from tb_para_cell_counter_detail_#1_#2
        where 
        无线接通率  +  
        [话音信道拥塞率(不含切)]  +  
        [话音信道掉话率(不含切)] +  
        [上行TBF建立成功率]  + 
        [下行TBF建立成功率]  + 
        [PDCH分配成功率]  +  
        [信令信道分配成功率]  + 
        [话音信道分配成功率(不含切)]  +  
        [信令信道拥塞率]  +  
        [非PBGT切换占比]  + 
        [下行话音质量]  + 
        [切换成功率]  + 
        [下行TBF掉线率] > 0
        and LAC = #LAC and CI = #CI and ";

        private readonly string selectSqlTemplateTD = @"
        select tmdat as '时间',
        [CS域无线接通率],
        [CS域误块率], 
        [语音业务无线掉话率], 
        [接力切换成功率], 
        [码资源利用率], 
        [CS域RAB拥塞率], 
        [PS域RAB拥塞率], 
        [PS域RAB建立成功率], 
        [PS域误块率], 
        [PS域无线掉线率]
        from tb_para_utrancell_counter_detail_#1_#2
        where 
        [CS域无线接通率] + 
        [CS域误块率] + 
        [语音业务无线掉话率] + 
        [接力切换成功率] + 
        [码资源利用率]  + 
        [CS域RAB拥塞率] + 
        [PS域RAB拥塞率]  + 
        [PS域RAB建立成功率]  + 
        [PS域误块率]  + 
        [PS域无线掉线率] > 0
        and LAC = #LAC and CI = #CI and ";
        private TableNamesQuery tableNamesQuery;
        protected override string getSqlTextString()
        {
            if (LAC == 0 && CI == 0)
            {
                return "";
            }
            if (timeStart == DateTime.MaxValue && TimeEnd == DateTime.MaxValue)
            {
                TimePeriod timePeriod = condition.Periods[0];
                timeStart = timePeriod.BeginTime;
                TimeEnd = timePeriod.EndTime;
            }
            tableNamesQuery = new TableNamesQuery(MainModel,IsTDIn);
            tableNamesQuery.Query();
            string  selectSqlTemplateGSMTemp =selectSqlTemplateGSM+ "\r\ntmdat >= '" + timeStart.ToString("yyyy-MM-dd 00:00:00") + "' and tmdat < '" +
                TimeEnd.AddDays(1).ToString("yyyy-MM-dd 00:00:00") + "'\r\n";
            string selectSqlTemplateTDTemp = selectSqlTemplateTD + "\r\ntmdat >= '" + timeStart.ToString("yyyy-MM-dd 00:00:00") + "' and tmdat < '" +
                TimeEnd.AddDays(1).ToString("yyyy-MM-dd 00:00:00") + "'\r\n";
            string statement = "";
            if (IsTDIn)
            {
                statement = getStatement(selectSqlTemplateTDTemp);
                return statement;
            }
            else
            {
                statement = getStatement(selectSqlTemplateGSMTemp);
                return statement;
            }
        }

        private string getStatement(string selectSqlTemplateTemp)
        {
            StringBuilder statement = new StringBuilder();
            List<string> tableTmdat = GetTableName();
            for (int i = 0; i < tableTmdat.Count; i++)
            {
                if (i != 0)
                {
                    statement.Append("\r\nunion \r\n");
                }
                statement.Append(selectSqlTemplateTemp.Replace("#1_#2", tableTmdat[i]));
                statement = statement.Replace("#LAC", LAC.ToString());
                statement = statement.Replace("#CI", CI.ToString());
            }

            return statement.ToString();
        }

        private List<string> GetTableName()
        {
            List<string> tableNames = new List<string>();
            if (IsTmdatTableNameIn(timeStart.Year.ToString("0000") + "_" + timeStart.Month.ToString("00")))
            {
                tableNames.Add(timeStart.Year.ToString("0000") + "_" + timeStart.Month.ToString("00"));
            }
            DateTime timeTemp = new DateTime(timeStart.AddMonths(1).Year, timeStart.AddMonths(1).Month, 1);
            while (timeTemp <= TimeEnd)
            {
                if (IsTmdatTableNameIn(timeTemp.Year.ToString("0000") + "_" + timeTemp.Month.ToString("00")))
                {
                    tableNames.Add(timeTemp.Year.ToString("0000") + "_" + timeTemp.Month.ToString("00"));
                }
                timeTemp = timeTemp.AddMonths(1);
                timeTemp = new DateTime(timeTemp.Year, timeTemp.Month, timeTemp.Day);
            }            
            return tableNames;
        }
        private bool IsTmdatTableNameIn(string tmdatStr)
        {
            if (tableNamesQuery != null)
            {
                if (IsTDIn)
                {
                    if (tableNamesQuery.TableNames.Contains("tb_para_utrancell_counter_detail_" + tmdatStr))
                    {
                        return true;
                    }
                    return false;
                }
                else
                {
                    if (tableNamesQuery.TableNames.Contains("tb_para_cell_counter_detail_" + tmdatStr))
                    {
                        return true;
                    }
                    return false;
                }
            }
            return true;
        }
        
        public List<GSMPerDetailEleInf> DetailInfGSM { get; set; } = new List<GSMPerDetailEleInf>();
        public List<TDPerDetailEleInf> DetailInfTD { get; set; } = new List<TDPerDetailEleInf>();
        protected override E_VType[] getSqlRetTypeArr()
        {
            if (IsTDIn)
            {
                E_VType[] rType = new E_VType[11];
                rType[0] = E_VType.E_String;
                rType[1] = E_VType.E_Float; 
                rType[2] = E_VType.E_Float; 
                rType[3] = E_VType.E_Float; 
                rType[4] = E_VType.E_Float; 
                rType[5] = E_VType.E_Float; 
                rType[6] = E_VType.E_Float; 
                rType[7] = E_VType.E_Float; 
                rType[8] = E_VType.E_Float; 
                rType[9] = E_VType.E_Float; 
                rType[10] = E_VType.E_Float;

                return rType;
            }
            else
            {
                E_VType[] rType = new E_VType[14];
                rType[0] = E_VType.E_String;
                rType[1] = E_VType.E_Float; 
                rType[2] = E_VType.E_Float; 
                rType[3] = E_VType.E_Float; 
                rType[4] = E_VType.E_Float; 
                rType[5] = E_VType.E_Float; 
                rType[6] = E_VType.E_Float; 
                rType[7] = E_VType.E_Float; 
                rType[8] = E_VType.E_Float; 
                rType[9] = E_VType.E_Float; 
                rType[10] = E_VType.E_Float;
                rType[11] = E_VType.E_Float;
                rType[12] = E_VType.E_Float;
                rType[13] = E_VType.E_Float;

                return rType;
            }
        }
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    fillData(package);
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                if (Math.Log(index++) * (100000 / 10000) > WaitBox.ProgressPercent)
                {
                    progress++;
                    if (progress > 95)
                    {
                        progress = 5;
                        index = 0;
                    }
                    if (progress % 5 == 0)
                    {
                        WaitBox.ProgressPercent = progress;
                    }
                }
            }
        }

        private void fillData(Package package)
        {
            try
            {
                if (IsTDIn)
                {
                    TDPerDetailEleInf detInf = new TDPerDetailEleInf();
                    detInf.Tmdat = package.Content.GetParamString();
                    detInf.data1 = package.Content.GetParamFloat();
                    detInf.data2 = package.Content.GetParamFloat();
                    detInf.data3 = package.Content.GetParamFloat();
                    detInf.data4 = package.Content.GetParamFloat();
                    detInf.data5 = package.Content.GetParamFloat();
                    detInf.data6 = package.Content.GetParamFloat();
                    detInf.data7 = package.Content.GetParamFloat();
                    detInf.data8 = package.Content.GetParamFloat();
                    detInf.data9 = package.Content.GetParamFloat();
                    detInf.data10 = package.Content.GetParamFloat();

                    DetailInfTD.Add(detInf);
                }
                else
                {
                    GSMPerDetailEleInf detInf = new GSMPerDetailEleInf();
                    detInf.Tmdat = package.Content.GetParamString();
                    detInf.data1 = package.Content.GetParamFloat();
                    detInf.data2 = package.Content.GetParamFloat();
                    detInf.data3 = package.Content.GetParamFloat();
                    detInf.data4 = package.Content.GetParamFloat();
                    detInf.data5 = package.Content.GetParamFloat();
                    detInf.data6 = package.Content.GetParamFloat();
                    detInf.data7 = package.Content.GetParamFloat();
                    detInf.data8 = package.Content.GetParamFloat();
                    detInf.data9 = package.Content.GetParamFloat();
                    detInf.data10 = package.Content.GetParamFloat();
                    detInf.data11 = package.Content.GetParamFloat();
                    detInf.data12 = package.Content.GetParamFloat();
                    detInf.data13 = package.Content.GetParamFloat();

                    DetailInfGSM.Add(detInf);
                }
            }
            catch
            {
                //continue
            }
        }

        public override string Name
        {
            get { return "PerDetailInfQuery"; }
        }
    }

    public class GSMPerDetailEleInf
    {
        /// <summary>
        /// 时间
        /// </summary>
        public string Tmdat { get; set; }
        //无线接通率
        /// <summary>
        /// 无线接通率
        /// </summary>
        public double data1 { get; set; }
        //话音信道拥塞率(不含切)
        /// <summary>
        /// 话音信道拥塞率(不含切)
        /// </summary>
        public double data2 { get; set; }
        //话音信道掉话率(不含切)
        /// <summary>
        /// 话音信道掉话率(不含切)
        /// </summary>
        public double data3 { get; set; }
        //上行TBF建立成功率
        /// <summary>
        /// 上行TBF建立成功率
        /// </summary>
        public double data4 { get; set; }

        //下行TBF建立成功率
        /// <summary>
        /// 下行TBF建立成功率
        /// </summary>
        public double data5 { get; set; }
        //PDCH分配成功率
        /// <summary>
        /// PDCH分配成功率
        /// </summary>
        public double data6 { get; set; }
        //信令信道分配成功率
        /// <summary>
        /// 信令信道分配成功率
        /// </summary>
        public double data7 { get; set; }
        //话音信道分配成功率(不含切)
        /// <summary>
        /// 话音信道分配成功率(不含切)
        /// </summary>
        public double data8 { get; set; }

        //信令信道拥塞率
        /// <summary>
        /// 信令信道拥塞率
        /// </summary>
        public double data9 { get; set; }
        //非PBGT切换占比
        /// <summary>
        /// 非PBGT切换占比
        /// </summary>
        public double data10 { get; set; }
        //下行话音质量
        /// <summary>
        ///下行话音质量 
        /// </summary>
        public double data11 { get; set; }
        //切换成功率
        /// <summary>
        /// 切换成功率
        /// </summary>
        public double data12 { get; set; }

        //下行TBF掉线率
        /// <summary>
        /// 下行TBF掉线率
        /// </summary>
        public double data13 { get; set; }

        public GSMPerDetailEleInf()
        {
            Tmdat = "";
            data1 = 0;
            data2 = 0;
            data3 = 0;
            data4 = 0;
            data5 = 0;
            data6 = 0;
            data7 = 0;
            data8 = 0;
            data9 = 0;
            data10 = 0;
            data11 = 0;
            data12 = 0;
            data13 = 0;
        }
    }

    public class TDPerDetailEleInf
    {
        /// <summary>
        /// 时间
        /// </summary>
        public string Tmdat { get; set; }
        //CS域无线接通率 
        /// <summary>
        /// CS域无线接通率
        /// </summary>
        public double data1 { get; set; }

        //CS域误块率
        /// <summary>
        /// CS域误块率
        /// </summary>
        public double data2 { get; set; }

        //语音业务无线掉话率
        /// <summary>
        /// 语音业务无线掉话率
        /// </summary>
        public double data3 { get; set; }

        //接力切换成功率
        /// <summary>
        /// 接力切换成功率
        /// </summary>
        public double data4 { get; set; }

        //码资源利用率 
        /// <summary>
        /// 码资源利用率 
        /// </summary>
        public double data5 { get; set; }

        //CS域RAB拥塞率 
        /// <summary>
        /// CS域RAB拥塞率 
        /// </summary>
        public double data6 { get; set; }

        //PS域RAB拥塞率
        /// <summary>
        /// PS域RAB拥塞率
        /// </summary>
        public double data7 { get; set; }

        //PS域RAB建立成功率
        /// <summary>
        /// PS域RAB建立成功率
        /// </summary>
        public double data8 { get; set; }

        //PS域误块率
        /// <summary>
        /// PS域误块率
        /// </summary>
        public double data9 { get; set; }

        //PS域无线掉线率
        /// <summary>
        /// PS域无线掉线率
        /// </summary>
        public double data10 { get; set; }
        

        public TDPerDetailEleInf()
        {
            Tmdat = "";
            data1 = 0;
            data2 = 0;
            data3 = 0;
            data4 = 0;
            data5 = 0;
            data6 = 0;
            data7 = 0;
            data8 = 0;
            data9 = 0;
            data10 = 0;
        }
    }
}
