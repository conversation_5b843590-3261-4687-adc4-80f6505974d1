﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.ZTFunc;
using MasterCom.Util;

namespace MasterCom.MTGis
{
    public partial class ColorOrImageDlg : Form
    {
        public ColorOrImageDlg()
        {
            InitializeComponent();
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            if(rbUseColor.Checked)
            {
                this.DialogResult = DialogResult.OK;
            }
            else if(rbUseIcon.Checked)
            {
                if(labelSymbolPreview.Image==null)
                {
                    MessageBox.Show("请选择图标文件！");
                    return;
                }
                this.DialogResult = DialogResult.OK;
            }
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = System.Windows.Forms.DialogResult.Cancel;
        }

        private void btnOpen_Click(object sender, EventArgs e)
        {
            OpenFileDialog dlg = new OpenFileDialog();
            dlg.Filter = MasterCom.Util.FilterHelper.Png + "|" + MasterCom.Util.FilterHelper.Jpg;
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return;
            }
            txtFile.Text = dlg.FileName;
            labelSymbolPreview.Image =  Image.FromFile(txtFile.Text);
            
        }

        internal void SetColorPair(ValueColorPair vcp)
        {
            if(vcp.image == null)
            {
                rbUseColor.Checked = true;
                rbUseIcon.Checked = false;
                pnUseColor.BackColor = vcp.color;
            }
            else
            {
                rbUseColor.Checked = false;
                rbUseIcon.Checked = true;
                labelSymbolPreview.Image = vcp.image;
                txtFile.Text = "";
            }
        }
        public ValueColorPair GetColorPairValue()
        {
            if(rbUseColor.Checked)
            {
                return new ValueColorPair("",pnUseColor.BackColor,null);
            }
            else
            {
                if(labelSymbolPreview.Image==null)
                {
                    return null;
                }
                else
                {
                    return new ValueColorPair("",Color.Empty,labelSymbolPreview.Image);
                }
            }
        }
        ColorDialog dialog = new ColorDialog();
        private void pnUseColor_Click(object sender, EventArgs e)
        {
            dialog.Color = pnUseColor.BackColor;
            if (dialog.ShowDialog() == DialogResult.OK)
            {
                pnUseColor.BackColor = dialog.Color;
            }
        }
    }
}
