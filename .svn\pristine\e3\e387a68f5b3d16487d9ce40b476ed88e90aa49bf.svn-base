﻿using MasterCom.ES.ColorManager;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.ZTFunc;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NRHandoverTooMuchForm : MinCloseForm
    {
        public NRHandoverTooMuchForm() : base()
        {
            InitializeComponent();
            DisposeWhenClose = true;
            OnTreeViewDataChanged += new TreeViewDataChanged(afterTreeViewDataChanged);
        }

        List<NRHandoverFileDataManager> rootDatas = new List<NRHandoverFileDataManager>();
        /// <summary>
        /// 保存查询出的结果集
        /// </summary>
        public List<NRHandoverFileDataManager> RootDatas
        {
            get { return rootDatas; }
            set
            {
                rootDatas = value;

                if (OnTreeViewDataChanged != null)
                {
                    OnTreeViewDataChanged(this, null);
                }
            }
        }

        /// <summary>
        /// 暂时没有用上 -- 事件ID(之前是在Excel导出时使用的)
        /// </summary>
        public int EventID { get; set; } = 17;

        /// <summary>
        /// 暂时没有用上 -- 频繁切换类型(之前是在Excel导出时使用的,后续修改部分代码本页面也可用于乒乓切换继承)
        /// </summary>
        public HandoverProblemType ProblemType { get; set; } = HandoverProblemType.PingPang;

        /// <summary>
        /// 窗体名称
        /// </summary>
        public virtual string FormName
        {
            get { return "切换过频繁分析"; }
        }

        //定义的委托
        private delegate void TreeViewDataChanged(object sender, EventArgs e);
        //与委托相关联的事件
        private event TreeViewDataChanged OnTreeViewDataChanged;
        /// <summary>
        /// RootDatas数据发生变化时重新加载TreeView
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void afterTreeViewDataChanged(object sender, EventArgs e)
        {
            loadTreeViewData(rootDatas);
            rtbHandoverTitle.Text = "";
            gridCtrHandoverDetail.DataSource = null;
        }

        /// <summary>
        /// 点击TreeView
        /// 如果是子节点,加载GridControl和RichTextBox中的数据
        /// 父节点清空GridControl数据,加载RichTextBox数据
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void treeViewFileList_AfterSelect(object sender, TreeViewEventArgs e)
        {
            if (treeViewFileList.SelectedNode != null)
            {
                NRHandoverProblemItem sub = treeViewFileList.SelectedNode.Tag as NRHandoverProblemItem;
                if (sub != null)
                {
                    string rtbData = string.Empty;
                    List<NRHandoverItem> gridDataRes = DealWithData(sub, ref rtbData);

                    treeViewFileList.SelectedNode.Name = rtbData;
                    gridCtrHandoverDetail.DataSource = gridDataRes;
                    gridViewDetail.BestFitColumns();
                }
                else
                {
                    gridCtrHandoverDetail.DataSource = null;
                }
                rtbHandoverTitle.Text = treeViewFileList.SelectedNode.Name;
            }
        }

        public virtual void FillData(List<NRHandoverFileDataManager> dataList, List<Event> allEvents)
        {
            StringBuilder sbText = new StringBuilder(FormName);
            this.Text = sbText.ToString();
            MainModel.ClearDTData();
            foreach (Event e in allEvents)
            {
                MainModel.DTDataManager.Add(e);
            }
            MainModel.FireDTDataChanged(this);
            RootDatas = dataList;
        }

        /// <summary>
        /// 加载treeview数据(如果以后数据格式发生改变,有3级子目录等情况,考虑改成虚函数)
        /// </summary>
        protected void loadTreeViewData(List<NRHandoverFileDataManager> rootData)
        {
            treeViewFileList.Nodes.Clear();
            foreach (NRHandoverFileDataManager root in rootData)
            {
                TreeNode rootNode = new TreeNode();
                //父节点text保存treeview显示数据
                rootNode.Text = root.Name;
                //父节点name保存点击后显示到richtextbox的数据
                rootNode.Name = root.Name;
                foreach (NRHandoverProblemItem sub in root.HandoverItems)
                {
                    TreeNode subNode = new TreeNode();
                    string cellName = sub.HandoverItems[0].TypeSrc + ":" + sub.HandoverItems[0].CellNameSrc;
                    //子节点Text保存TreeView显示数据
                    subNode.Text = cellName;
                    subNode.Tag = sub;
                    rootNode.Nodes.Add(subNode);
                }
                treeViewFileList.Nodes.Add(rootNode);
            }
        }

        /// <summary>
        /// 处理RichTextBox和GridControl数据
        /// </summary>
        /// <param name="sub">数据源</param>
        /// <param name="rtbData">RichTextBox数据</param>
        public virtual List<NRHandoverItem> DealWithData(NRHandoverProblemItem sub, ref string rtbData)
        {
            //GridControl数据
            List<NRHandoverItem> gridDataRes = new List<NRHandoverItem>();
            //RichTextBox数据
            StringBuilder strRTBDetail = new StringBuilder("【");
            foreach (NRHandoverItem eventItem in sub.HandoverItems)
            {
                //添加数据源
                gridDataRes.Add(eventItem);

                strRTBDetail.Append(eventItem.CellNameSrc);
                strRTBDetail.Append("】 -> 【");
            }
            strRTBDetail.Append(sub.HandoverItems[sub.HandoverItems.Count - 1].CellNameTarget);
            strRTBDetail.Append("】");
            rtbData = strRTBDetail.ToString();

            return gridDataRes;
        }

        /// <summary>
        /// 双击地图联动
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void gridViewDetail_DoubleClick(object sender, EventArgs e)
        {
            object objSelectRow = gridViewDetail.GetFocusedRow();
            if (objSelectRow is NRHandoverItem)
            {
                NRHandoverItem ci = objSelectRow as NRHandoverItem;
                fileSelectedCellItem(ci);
                MainModel.MainForm.FireGotoView(ci.Longitude, ci.Latitude);
            }
        }

        private void treeViewFileList_DoubleClick(object sender, EventArgs e)
        {
            object objSelectData = treeViewFileList.SelectedNode.Tag;
            if (objSelectData is NRHandoverProblemItem)
            {
                fireSelectedHandoverItem(objSelectData as NRHandoverProblemItem);
            }
        }

        /// <summary>
        /// 地图呈现
        /// </summary>
        /// <param name="cellItem"></param>
        protected void fileSelectedCellItem(NRHandoverItem cellItem)
        {
            MainModel.SelectedEvents.Clear();
            MainModel.DTDataManager.FileDataManagers.Clear();
            MainModel.DTDataManager.MakeEmptyMap();
            MainModel.FireDTDataChanged(this);
            MainModel.FireSelectedEventsChanged(this);

            if (cellItem == null) return;

            List<Event> eventList = new List<Event>();
            ((NRHandoverCellItem)cellItem.Tag).Ev.Selected = true;
            eventList.Add(((NRHandoverCellItem)cellItem.Tag).Ev);
            MainModel.HandOverSeqEvents = eventList;
            MainModel.DrawHandoverSeq = true;
            MainModel.DTDataManager.Clear();
            MainModel.DTDataManager.MakeEmptyMap();
            foreach (Event e in eventList)
            {
                MainModel.DTDataManager.Add(e);
            }
            MainModel.FireDTDataChanged(this);
            MainModel.SelectedEvents = eventList;
        }

        protected void fireSelectedHandoverItem(NRHandoverProblemItem handoverItem)
        {
            mModel.SelectedEvents.Clear();
            mModel.DTDataManager.FileDataManagers.Clear();
            mModel.DTDataManager.MakeEmptyMap();
            mModel.FireDTDataChanged(this);
            mModel.FireSelectedEventsChanged(this);

            if (handoverItem == null) return;

            List<Event> eventList = new List<Event>();
            foreach (Event eSelect in handoverItem.Events)
            {
                eSelect.Selected = true;
            }
            eventList.AddRange(handoverItem.Events);
            mModel.HandOverSeqEvents = eventList;
            mModel.DrawHandoverSeq = true;
            mModel.DTDataManager.Clear();
            mModel.DTDataManager.MakeEmptyMap();
            foreach (Event e in eventList)
            {
                mModel.DTDataManager.Add(e);
            }
            mModel.FireDTDataChanged(this);
            mModel.SelectedEvents = eventList;
        }

        /// <summary>
        /// 右键菜单--展开TreeView所有Node
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void toolStripMenuItemExpandAll_Click(object sender, EventArgs e)
        {
            treeViewFileList.ExpandAll();
        }

        /// <summary>
        /// 右键菜单--折叠TreeView所有Node
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void toolStripMenuItemCollapseAll_Click(object sender, EventArgs e)
        {
            treeViewFileList.CollapseAll();
        }

        /// <summary>
        /// 右键菜单--导出
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void miExportExcel_Click(object sender, EventArgs e)
        {
            export2ExcelNPOI();
        }

        #region 导出Excel相关
        private readonly string strNull = "-";
        private List<string> firstList = null;
        private List<string> secondList = null;
        private List<string> thirdList = null;

        private void export2ExcelNPOI()
        {
            List<NPOIRow> rowList = new List<NPOIRow>();
            NPOIRow titleRow = new NPOIRow();//添加列头
            firstList = new List<string>();//一级数据
            secondList = new List<string>();//二级数据
            thirdList = new List<string>();//三级数据

            //一级
            titleRow.AddCellValue(RowTitle.文件序号);
            titleRow.AddCellValue(RowTitle.文件名);
            titleRow.AddCellValue(RowTitle.过频繁组数);
            firstList.Add(RowTitle.文件序号);
            firstList.Add(RowTitle.文件名);
            firstList.Add(RowTitle.过频繁组数);

            //次级
            titleRow.AddCellValue(RowTitle.切换序号);
            titleRow.AddCellValue(RowTitle.切换名称);
            titleRow.AddCellValue(RowTitle.次数);
            secondList.Add(RowTitle.切换序号);
            secondList.Add(RowTitle.切换名称);
            secondList.Add(RowTitle.次数);

            //三级
            titleRow.AddCellValue(RowTitle.小区序号);
            titleRow.AddCellValue(RowTitle.小区名称);
            thirdList.Add(RowTitle.小区序号);
            thirdList.Add(RowTitle.小区名称);

            titleRow.AddCellValue(RowTitle.切换时间);
            titleRow.AddCellValue(RowTitle.切换时经度);
            titleRow.AddCellValue(RowTitle.切换时纬度);
            titleRow.AddCellValue(RowTitle.切换前RSRP);
            titleRow.AddCellValue(RowTitle.切换后RSRP);
            titleRow.AddCellValue(RowTitle.切换前SINR);
            titleRow.AddCellValue(RowTitle.切换后SINR);
            thirdList.Add(RowTitle.切换时间);
            thirdList.Add(RowTitle.切换时经度);
            thirdList.Add(RowTitle.切换时纬度);
            thirdList.Add(RowTitle.切换前RSRP);
            thirdList.Add(RowTitle.切换后RSRP);
            thirdList.Add(RowTitle.切换前SINR);
            thirdList.Add(RowTitle.切换后SINR);

            //特殊列
            foreach (NRHandoverFileDataManager fileItem in rootDatas)
            {
                if (fileItem.fmnger.ServiceType == (int)ServiceType.LTE_TDD_DATA
                    || fileItem.fmnger.ServiceType == (int)ServiceType.LTE_TDD_MULTI
                    || fileItem.fmnger.ServiceType == (int)ServiceType.LTE_TDD_VOICE)
                {
                    titleRow.cellValues.Insert(6, RowTitle.平均RSRP);
                    titleRow.cellValues.Insert(7, RowTitle.平均SINR);
                    titleRow.cellValues.Insert(8, RowTitle.平均PDCP下载速度);
                    titleRow.cellValues.Insert(9, RowTitle.平均PDCCHDLGrant个数);
                    titleRow.cellValues.Insert(10, RowTitle.采样点数);
                    titleRow.cellValues.Insert(11, RowTitle.平均APP速率);
                    secondList.Add(RowTitle.平均RSRP);
                    secondList.Add(RowTitle.平均SINR);
                    secondList.Add(RowTitle.平均PDCP下载速度);
                    secondList.Add(RowTitle.平均PDCCHDLGrant个数);
                    secondList.Add(RowTitle.采样点数);
                    secondList.Add(RowTitle.平均APP速率);
                    break;
                }
            }

            rowList.Add(titleRow);

            foreach (NRHandoverFileDataManager fileItem in rootDatas)
            {
                NPOIRow row = new NPOIRow();
                fillRow(row, fileItem);
                rowList.Add(row);
            }

            ExcelNPOIManager.ExportToExcel(rowList);
            if (ExcelNPOIManager.error)
            {
                MessageBox.Show("导出xls失败");
            }
        }

        private void fillRow(NPOIRow row, NRHandoverFileDataManager fileItem)
        {
            if (row == null || fileItem == null)
                return;

            foreach (string rowName in firstList)
                row.AddCellValue(getRowVal(rowName, fileItem));

            foreach (NRHandoverProblemItem handoverItem in fileItem.HandoverItems)
            {
                NPOIRow subRow = new NPOIRow();

                foreach (string rowName in secondList)
                    subRow.AddCellValue(getRowVal(rowName, handoverItem));

                foreach (NRHandoverItem cellItem in handoverItem.HandoverItems)
                {
                    NPOIRow thirdRow = new NPOIRow();

                    foreach (string rowName in thirdList)
                        thirdRow.AddCellValue(getRowVal(rowName, cellItem));

                    subRow.AddSubRow(thirdRow);
                }

                row.AddSubRow(subRow);
            }
        }

        private object getRowVal(string rowName, NRHandoverFileDataManager fileItem)
        {
            object result = strNull;
            if (fileItem == null)
                return result;

            if (rowName == RowTitle.文件序号)
                result = fileItem.Index;
            else if (rowName == RowTitle.文件名)
                result = fileItem.Name;
            else if (rowName == RowTitle.过频繁组数)
                result = fileItem.HandoverTimes;

            return result;
        }

        private object getRowVal(string rowName, NRHandoverProblemItem handoverItem)
        {
            object result = strNull;
            if (handoverItem == null)
                return result;

            if (rowName == RowTitle.切换序号)
                result = handoverItem.Index;
            else if (rowName == RowTitle.切换名称)
            {
                string str = handoverItem.Name.Replace("=", "--");
                if (str.IndexOf("->") == 0)
                    str = str.Remove(0, 2);
                result = str;
            }
            else if (rowName == RowTitle.次数)
                result = handoverItem.Events.Count;
            else if (rowName == RowTitle.平均RSRP)
                handoverItem.Param.TryGetValue("lte_RSRP_Avg", out result);
            else if (rowName == RowTitle.平均SINR)
                handoverItem.Param.TryGetValue("lte_SINR_Avg", out result);
            else if (rowName == RowTitle.平均PDCP下载速度)
                handoverItem.Param.TryGetValue("lte_PDCP_DL_Mb_Avg", out result);
            else if (rowName == RowTitle.平均PDCCHDLGrant个数)
                handoverItem.Param.TryGetValue("lte_PDCCH_DL_Grant_Count_Avg", out result);
            else if (rowName == RowTitle.采样点数)
                handoverItem.Param.TryGetValue("lte_APP_ThroughputDL_Num", out result);
            else if (rowName == RowTitle.平均APP速率)
                handoverItem.Param.TryGetValue("lte_APP_ThroughputDL_Avg", out result);

            if (result == null)
                result = strNull;

            return result;
        }

        private object getRowVal(string rowName, NRHandoverItem cellItem)
        {
            object result = strNull;
            if (cellItem == null)
                return result;

            if (rowName == RowTitle.小区序号)
                result = cellItem.Index;
            else if (rowName == RowTitle.小区名称)
            {
                string str = cellItem.Name.Replace("=", "--");
                if (str.IndexOf("->") == 0)
                    str = str.Remove(0, 2);
                result = str;
            }
            else if (rowName == RowTitle.切换时间)
                result = cellItem.DateTimeString;
            else if (rowName == RowTitle.切换时经度)
                result = cellItem.Longitude;
            else if (rowName == RowTitle.切换时纬度)
                result = cellItem.Latitude;
            else if (rowName == RowTitle.切换前RSRP)
                result = cellItem.RsrpBefore;
            else if (rowName == RowTitle.切换后RSRP)
                result = cellItem.RsrpAfter;
            else if (rowName == RowTitle.切换前SINR)
                result = cellItem.SinrBefore;
            else if (rowName == RowTitle.切换后SINR)
                result = cellItem.SinrAfter;


            if (result == null)
                result = strNull;

            return result;
        }

        private static class RowTitle
        {
            public static string 文件序号 { get { return "文件序号"; } }
            public static string 文件名 { get { return "文件名"; } }
            public static string 过频繁组数 { get { return "过频繁组数"; } }
            public static string 切换序号 { get { return "切换序号"; } }
            public static string 切换名称 { get { return "切换名称"; } }
            public static string 次数 { get { return "次数"; } }
            public static string 小区序号 { get { return "小区序号"; } }
            public static string 小区名称 { get { return "小区名称"; } }
            public static string 切换时间 { get { return "切换时间"; } }
            public static string 切换时经度 { get { return "切换时经度"; } }
            public static string 切换时纬度 { get { return "切换时纬度"; } }
            public static string 切换前RSRP { get { return "切换前RSRP"; } }
            public static string 切换后RSRP { get { return "切换后RSRP"; } }
            public static string 切换前SINR { get { return "切换前SINR"; } }
            public static string 切换后SINR { get { return "切换后SINR"; } }

            public static string 平均RSRP { get { return "平均RSRP"; } }
            public static string 平均SINR { get { return "平均SINR"; } }
            public static string 平均PDCP下载速度 { get { return "平均PDCP下载速度(Mb)"; } }
            public static string 平均PDCCHDLGrant个数 { get { return "平均PDCCH DL Grant个数"; } }
            public static string 采样点数 { get { return "采样点数"; } }
            public static string 平均APP速率 { get { return "平均APP速率"; } }
        }
        #endregion
    }
}
