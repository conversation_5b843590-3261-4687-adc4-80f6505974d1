﻿namespace MasterCom.RAMS.CQT
{
    partial class KPISummaryColumnPanel
    {
        /// <summary> 
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Component Designer generated code

        /// <summary> 
        /// Required method for Designer support - do not modify 
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            DevExpress.XtraGrid.StyleFormatCondition styleFormatCondition1 = new DevExpress.XtraGrid.StyleFormatCondition();
            this.gridControl = new DevExpress.XtraGrid.GridControl();
            this.gridView = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcWeight = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repositoryItemSpinEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemSpinEdit();
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.panel1 = new System.Windows.Forms.Panel();
            this.txtMaxScore = new System.Windows.Forms.TextBox();
            this.txtMinScore = new System.Windows.Forms.TextBox();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.groupControl2 = new DevExpress.XtraEditors.GroupControl();
            this.scoreRangeColorSettingPanel1 = new MasterCom.RAMS.CQT.ScoreRangeColorSettingPanel();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemSpinEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            this.panel1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).BeginInit();
            this.groupControl2.SuspendLayout();
            this.SuspendLayout();
            // 
            // gridControl
            // 
            this.gridControl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControl.Location = new System.Drawing.Point(2, 57);
            this.gridControl.MainView = this.gridView;
            this.gridControl.Name = "gridControl";
            this.gridControl.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repositoryItemSpinEdit1});
            this.gridControl.Size = new System.Drawing.Size(716, 227);
            this.gridControl.TabIndex = 0;
            this.gridControl.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView});
            // 
            // gridView
            // 
            this.gridView.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn2,
            this.gridColumn4,
            this.gridColumn5,
            this.gcWeight});
            styleFormatCondition1.Condition = DevExpress.XtraGrid.FormatConditionEnum.Equal;
            this.gridView.FormatConditions.AddRange(new DevExpress.XtraGrid.StyleFormatCondition[] {
            styleFormatCondition1});
            this.gridView.GridControl = this.gridControl;
            this.gridView.Name = "gridView";
            this.gridView.OptionsBehavior.EditorShowMode = DevExpress.Utils.EditorShowMode.MouseDownFocused;
            this.gridView.OptionsView.ShowFooter = true;
            this.gridView.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn2
            // 
            this.gridColumn2.Caption = "参与汇总列";
            this.gridColumn2.FieldName = "Name";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.OptionsColumn.AllowEdit = false;
            this.gridColumn2.OptionsFilter.AllowAutoFilter = false;
            this.gridColumn2.OptionsFilter.AllowFilter = false;
            this.gridColumn2.Visible = true;
            this.gridColumn2.VisibleIndex = 0;
            // 
            // gridColumn4
            // 
            this.gridColumn4.Caption = "最小分数";
            this.gridColumn4.FieldName = "MinScore";
            this.gridColumn4.Name = "gridColumn4";
            this.gridColumn4.SummaryItem.DisplayFormat = "最小值={0}";
            this.gridColumn4.SummaryItem.SummaryType = DevExpress.Data.SummaryItemType.Min;
            this.gridColumn4.Visible = true;
            this.gridColumn4.VisibleIndex = 1;
            // 
            // gridColumn5
            // 
            this.gridColumn5.Caption = "最大分数";
            this.gridColumn5.FieldName = "MaxScore";
            this.gridColumn5.Name = "gridColumn5";
            this.gridColumn5.SummaryItem.DisplayFormat = "最大值={0}";
            this.gridColumn5.SummaryItem.SummaryType = DevExpress.Data.SummaryItemType.Max;
            this.gridColumn5.Visible = true;
            this.gridColumn5.VisibleIndex = 2;
            // 
            // gcWeight
            // 
            this.gcWeight.Caption = "权重";
            this.gcWeight.ColumnEdit = this.repositoryItemSpinEdit1;
            this.gcWeight.FieldName = "Weight";
            this.gcWeight.Name = "gcWeight";
            this.gcWeight.OptionsFilter.AllowAutoFilter = false;
            this.gcWeight.OptionsFilter.AllowFilter = false;
            this.gcWeight.SummaryItem.DisplayFormat = "和={0}";
            this.gcWeight.SummaryItem.SummaryType = DevExpress.Data.SummaryItemType.Sum;
            this.gcWeight.Visible = true;
            this.gcWeight.VisibleIndex = 3;
            // 
            // repositoryItemSpinEdit1
            // 
            this.repositoryItemSpinEdit1.AutoHeight = false;
            this.repositoryItemSpinEdit1.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.repositoryItemSpinEdit1.Name = "repositoryItemSpinEdit1";
            // 
            // groupControl1
            // 
            this.groupControl1.Controls.Add(this.gridControl);
            this.groupControl1.Controls.Add(this.panel1);
            this.groupControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControl1.Location = new System.Drawing.Point(0, 0);
            this.groupControl1.Name = "groupControl1";
            this.groupControl1.Size = new System.Drawing.Size(720, 286);
            this.groupControl1.TabIndex = 3;
            this.groupControl1.Text = "汇总列设置";
            // 
            // panel1
            // 
            this.panel1.Controls.Add(this.txtMaxScore);
            this.panel1.Controls.Add(this.txtMinScore);
            this.panel1.Controls.Add(this.labelControl3);
            this.panel1.Controls.Add(this.labelControl2);
            this.panel1.Controls.Add(this.labelControl1);
            this.panel1.Dock = System.Windows.Forms.DockStyle.Top;
            this.panel1.Location = new System.Drawing.Point(2, 23);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(716, 34);
            this.panel1.TabIndex = 3;
            // 
            // txtMaxScore
            // 
            this.txtMaxScore.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.txtMaxScore.Location = new System.Drawing.Point(675, 10);
            this.txtMaxScore.Name = "txtMaxScore";
            this.txtMaxScore.ReadOnly = true;
            this.txtMaxScore.Size = new System.Drawing.Size(36, 21);
            this.txtMaxScore.TabIndex = 2;
            this.txtMaxScore.Text = "100";
            this.txtMaxScore.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            // 
            // txtMinScore
            // 
            this.txtMinScore.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.txtMinScore.Location = new System.Drawing.Point(567, 10);
            this.txtMinScore.Name = "txtMinScore";
            this.txtMinScore.ReadOnly = true;
            this.txtMinScore.Size = new System.Drawing.Size(36, 21);
            this.txtMinScore.TabIndex = 2;
            this.txtMinScore.Text = "0";
            this.txtMinScore.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            // 
            // labelControl3
            // 
            this.labelControl3.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.labelControl3.Location = new System.Drawing.Point(501, 13);
            this.labelControl3.Name = "labelControl3";
            this.labelControl3.Size = new System.Drawing.Size(60, 14);
            this.labelControl3.TabIndex = 1;
            this.labelControl3.Text = "总分范围：";
            // 
            // labelControl2
            // 
            this.labelControl2.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.labelControl2.Location = new System.Drawing.Point(609, 13);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(60, 14);
            this.labelControl2.TabIndex = 1;
            this.labelControl2.Text = "<=总分<=";
            // 
            // labelControl1
            // 
            this.labelControl1.Location = new System.Drawing.Point(23, 13);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(447, 14);
            this.labelControl1.TabIndex = 1;
            this.labelControl1.Text = "说明：汇总分数=（汇总列1*对应权重+汇总列2*对应权重+...+汇总列n*对应权重）";
            // 
            // groupControl2
            // 
            this.groupControl2.Controls.Add(this.scoreRangeColorSettingPanel1);
            this.groupControl2.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.groupControl2.Location = new System.Drawing.Point(0, 286);
            this.groupControl2.Name = "groupControl2";
            this.groupControl2.Size = new System.Drawing.Size(720, 174);
            this.groupControl2.TabIndex = 4;
            this.groupControl2.Text = "汇总打分设置";
            // 
            // scoreRangeColorSettingPanel1
            // 
            this.scoreRangeColorSettingPanel1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.scoreRangeColorSettingPanel1.Location = new System.Drawing.Point(2, 23);
            this.scoreRangeColorSettingPanel1.Name = "scoreRangeColorSettingPanel1";
            this.scoreRangeColorSettingPanel1.Size = new System.Drawing.Size(716, 149);
            this.scoreRangeColorSettingPanel1.TabIndex = 0;
            // 
            // KPISummaryColumnPanel
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.groupControl1);
            this.Controls.Add(this.groupControl2);
            this.Name = "KPISummaryColumnPanel";
            this.Size = new System.Drawing.Size(720, 460);
            ((System.ComponentModel.ISupportInitialize)(this.gridControl)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemSpinEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            this.panel1.ResumeLayout(false);
            this.panel1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).EndInit();
            this.groupControl2.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraGrid.GridControl gridControl;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraGrid.Columns.GridColumn gcWeight;
        private DevExpress.XtraEditors.Repository.RepositoryItemSpinEdit repositoryItemSpinEdit1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn5;
        private DevExpress.XtraEditors.GroupControl groupControl1;
        private System.Windows.Forms.Panel panel1;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.GroupControl groupControl2;
        private ScoreRangeColorSettingPanel scoreRangeColorSettingPanel1;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private System.Windows.Forms.TextBox txtMaxScore;
        private System.Windows.Forms.TextBox txtMinScore;
        private DevExpress.XtraEditors.LabelControl labelControl3;

    }
}
