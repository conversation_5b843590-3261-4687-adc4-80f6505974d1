﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.Net
{
    public class ZTDIYCellSetByFile_TD : ZTDIYCellSetByFile
    {
        public ZTDIYCellSetByFile_TD(MainModel mainModel)
            : base(mainModel)
        {
            isAddSampleToDTDataManager = false;
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 13000, 13051, this.Name);
        }

        protected override bool getConditionBeforeQuery()
        {
            SetConditionServerType();
            return true;
        }
    }
}
