﻿using MasterCom.RAMS.Grid;
using MasterCom.RAMS.KPI_Statistics;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTWirelessNetTest
{
    public static class WirelessNetTestStat
    {
        public static void StatKpiData(WirelessNetTestStatInfo statInfo, WirelessNetTestResult singleDistrictData)
        {
            if (statInfo.DataList.Count == 0 && statInfo.EventList.Count == 0)
            {
                return;
            }

            var msg = $"开始统计[{statInfo.DistrictName}_{statInfo.Carrier}_{statInfo.Type}]指标数据...";
            WirelessNetTestConfig.Instance.WriteLogWithWaitBox(msg);

            if (WirelessNetTestHelper.Instance.StatTypeDic.TryGetValue(statInfo.Type, out var statList))
            {
                foreach (var statType in statList)
                {
                    var stat = WirelessNetTestStatFactory.Stat(statType);
                    stat.Stat(statInfo, singleDistrictData);
                }
            }
        }
    }

    #region 按各级别进行指标数据统计
    public static class WirelessNetTestStatFactory
    {
        public static WirelessNetTestStatBase Stat(WirelessNetTestStatType statType)
        {
            WirelessNetTestStatBase stat = null;
            switch (statType)
            {
                case WirelessNetTestStatType.地市级:
                    stat = new WirelessNetTestStatDistrict(statType);
                    break;
                case WirelessNetTestStatType.区县级:
                    stat = new WirelessNetTestStatCounty(statType);
                    break;
                case WirelessNetTestStatType.场景级:
                    stat = new WirelessNetTestStatScene(statType);
                    break;
                case WirelessNetTestStatType.子场景级:
                    stat = new WirelessNetTestStatSubScene(statType);
                    break;
            }
            return stat;
        }
    }

    public abstract class WirelessNetTestStatBase
    {
        protected WirelessNetTestStatType statType;
        protected WirelessNetTestStatBase(WirelessNetTestStatType statType)
        {
            this.statType = statType;
        }

        public abstract void Stat(WirelessNetTestStatInfo statInfo, WirelessNetTestResult singleDistrictData);

        protected Dictionary<ReportExcelStyle, double> getFormulaData(StatData statData
            , List<ReportExcelStyle> formulas)
        {
            var formulaDataDic = new Dictionary<ReportExcelStyle, double>();
            foreach (var formula in formulas)
            {
                var value = statData.CalcValueByFormula(formula.Formula);
                formulaDataDic.Add(formula, value);
            }

            return formulaDataDic;
        }

        public class SceneCell
        {
            public void Calculate()
            {
                LteCellOccupyRate.Calculate(true);
                NrCellOccupyRate.Calculate(true);
                LteBtsOccupyRate.Calculate(true);
                NrBtsOccupyRate.Calculate(true);
            }

            public RateInfo LteCellOccupyRate { get; set; } = new RateInfo();
            public RateInfo NrCellOccupyRate { get; set; } = new RateInfo();
            public RateInfo LteBtsOccupyRate { get; set; } = new RateInfo();
            public RateInfo NrBtsOccupyRate { get; set; } = new RateInfo();
        }
    }

    public class WirelessNetTestStatDistrict : WirelessNetTestStatBase
    {
        public WirelessNetTestStatDistrict(WirelessNetTestStatType statType)
            : base(statType)
        {
        }

        public override void Stat(WirelessNetTestStatInfo statInfo, WirelessNetTestResult singleDistrictData)
        {
            //一个地市的地市级只有一条结果
            StatData statData = new StatData();
            foreach (var data in statInfo.DataList)
            {
                statData.AddStatData(data, false);
            }

            foreach (var data in statInfo.EventList)
            {
                statData.AddStatData(data, false);
            }

            var resList = new List<WirelessNetTestKpiDistrict>();

            var formulas = WirelessNetTestReport.Instance.FormulaDic[FormulaType.全部];
            var formulaDataDic = getFormulaData(statData, formulas);
            var res = new WirelessNetTestKpiDistrict();
            res.DistrictName.FormulaData = statInfo.DistrictName;
            if (!res.FormulaDataDic.TryGetValue(statInfo.Carrier, out _))
            {
                res.FormulaDataDic.Add(statInfo.Carrier, formulaDataDic);
            }
            resList.Add(res);

            singleDistrictData.DistrictResList.AddRange(resList);

            WirelessNetTestConfig.Instance.WriteLog($"{statType}-{statInfo.Type}结果{resList.Count}条");
        }
    }

    public class WirelessNetTestStatCounty : WirelessNetTestStatBase
    {
        public WirelessNetTestStatCounty(WirelessNetTestStatType statType)
         : base(statType)
        {
        }

        public override void Stat(WirelessNetTestStatInfo statInfo, WirelessNetTestResult singleDistrictData)
        {
            //按区县汇聚结果
            Dictionary<string, StatData> countyResDic = new Dictionary<string, StatData>();

            WirelessNetTestStatHelper.AddValidGridData(WirelessNetTestMap.Instance.CountyDic, statInfo.DataList, false, countyResDic);
            WirelessNetTestStatHelper.AddValidGridData(WirelessNetTestMap.Instance.CountyDic, statInfo.EventList, true, countyResDic);

            var resList = new List<WirelessNetTestKpiCounty>();

            var formulas = WirelessNetTestReport.Instance.FormulaDic[FormulaType.全部];
            foreach (var statData in countyResDic)
            {
                var res = new WirelessNetTestKpiCounty();
                res.DistrictName.FormulaData = statInfo.DistrictName;
                res.CountyName.FormulaData = statData.Key;

                var formulaDataDic = getFormulaData(statData.Value, formulas);
                if (!res.FormulaDataDic.TryGetValue(statInfo.Carrier, out _))
                {
                    res.FormulaDataDic.Add(statInfo.Carrier, formulaDataDic);
                }
                resList.Add(res);
            }

            singleDistrictData.CountyResList.AddRange(resList);
            WirelessNetTestConfig.Instance.WriteLog($"{statType}-{statInfo.Type}结果{resList.Count}条");
        }
    }

    public class WirelessNetTestStatScene : WirelessNetTestStatBase
    {
        public WirelessNetTestStatScene(WirelessNetTestStatType statType)
         : base(statType)
        {
        }

        public override void Stat(WirelessNetTestStatInfo statInfo, WirelessNetTestResult singleDistrictData)
        {
            var resList = new List<WirelessNetTestKpiScene>();
            switch (statInfo.Type)
            {
                case WirelessNetTestProjectType.城区:
                    resList = dealGrid(statInfo, WirelessNetTestMap.Instance.CityGridDic);
                    break;
                case WirelessNetTestProjectType.区县:
                    resList = dealGrid(statInfo, WirelessNetTestMap.Instance.CountyGridDic);
                    break;
                case WirelessNetTestProjectType.高铁:
                case WirelessNetTestProjectType.地铁:
                case WirelessNetTestProjectType.高速:
                case WirelessNetTestProjectType.机场:
                    resList = dealScene(statInfo);
                    break;
            }
            singleDistrictData.SceneResList.AddRange(resList);
            WirelessNetTestConfig.Instance.WriteLog($"{statType}-{statInfo.Type}结果{resList.Count}条");
        }

        #region 处理城区,区县网格
        private List<WirelessNetTestKpiScene> dealGrid(WirelessNetTestStatInfo statInfo
            , Dictionary<string, ResvRegion> regionDic)
        {
            //按网格汇聚结果
            var resGridDic = new Dictionary<string, StatData>();

            WirelessNetTestStatHelper.AddValidGridData(regionDic, statInfo.DataList, false, resGridDic);
            WirelessNetTestStatHelper.AddValidGridData(regionDic, statInfo.EventList, true, resGridDic);

            var resList = new List<WirelessNetTestKpiScene>();

            var formulas = WirelessNetTestReport.Instance.FormulaDic[FormulaType.全部];
            foreach (var statData in resGridDic)
            {
                var res = new WirelessNetTestKpiScene();
                res.DistrictName.FormulaData = statInfo.DistrictName;
                res.Scene.FormulaData = statData.Key;

                var formulaDataDic = getFormulaData(statData.Value, formulas);
                if (!res.FormulaDataDic.TryGetValue(statInfo.Carrier, out _))
                {
                    res.FormulaDataDic.Add(statInfo.Carrier, formulaDataDic);
                }

                dealOtherInfo(statInfo, statData.Value, res);

                resList.Add(res);
            }

            return resList;
        }

        private void dealOtherInfo(WirelessNetTestStatInfo statInfo, StatData data, WirelessNetTestKpiScene res)
        {
            if (statInfo.Carrier != CarrierStatType.移动)
            {
                return;
            }

            res.CarSpeed.FormulaData = WirelessNetTestReport.Instance.GetCarSpeed(data);

            //只有城区需要计算渗透率
            if (statInfo.Type == WirelessNetTestProjectType.城区)
            {
                string sceneName = res.Scene.FormulaData;
                var files = data.GetFileIDList();
                WirelessNetTestConfig.Instance.WriteLog($"城区[{sceneName}]开始统计渗透率...");
                bool isValid = WirelessNetTestInject.Instance.initInjectGrid(statInfo.DistrictID
                    , statInfo.TestPeriod, WirelessNetTestMap.Instance.CityMergeShape
                    , files, data.GetFileID);
                if (isValid)
                {
                    var region = WirelessNetTestMap.Instance.CityGridDic[sceneName];
                    var injection = WirelessNetTestInject.Instance.GetInjectDatByShp(region
                        , sceneName, WirelessNetTestMap.Instance.RoadDic);
                    res.Injection.FormulaData = injection.CoverPercent.ToString();
                }
                else
                {
                    WirelessNetTestConfig.Instance.WriteLog($"[{sceneName}]没有有效的栅格信息", "Error");
                }
            }
        }
        #endregion

        #region 处理高铁,地铁,高速,机场类别中的各个场景
        private List<WirelessNetTestKpiScene> dealScene(WirelessNetTestStatInfo statInfo)
        {
            //按场景名汇聚结果
            var resSceneDic = new Dictionary<WirelessNetTestScene, StatData>();

            WirelessNetTestStatHelper.AddValidSceneData(WirelessNetTestSceneCell.Instance.FileSceneDic, statInfo.DataList, resSceneDic);
            WirelessNetTestStatHelper.AddValidSceneData(WirelessNetTestSceneCell.Instance.FileSceneDic, statInfo.EventList, resSceneDic);

            var resList = new List<WirelessNetTestKpiScene>();

            var formulas = WirelessNetTestReport.Instance.FormulaDic[FormulaType.全部];
            foreach (var statData in resSceneDic)
            {
                var scene = statData.Key;
                var data = statData.Value;

                var res = new WirelessNetTestKpiScene();
                res.DistrictName.FormulaData = statInfo.DistrictName;
                res.Scene.FormulaData = scene.SceneName;

                var formulaDataDic = getFormulaData(data, formulas);
                if (!res.FormulaDataDic.TryGetValue(statInfo.Carrier, out _))
                {
                    res.FormulaDataDic.Add(statInfo.Carrier, formulaDataDic);
                }
                resList.Add(res);

                dealOtherInfo(statInfo, scene, data, res);
            }
            return resList;
        }

        private void dealOtherInfo(WirelessNetTestStatInfo statInfo, WirelessNetTestScene scene, StatData data, WirelessNetTestKpiScene res)
        {
            if (statInfo.Carrier != CarrierStatType.移动)
            {
                return;
            }

            res.CarSpeed.FormulaData = WirelessNetTestReport.Instance.GetCarSpeed(data);

            //根据文件回放采样点,再统计对应小区和基站数
            WirelessNetTestStatHelper.ReplayFile(statInfo.DistrictID, scene.Files);
            var sceneCell = dealSceneTestpoints(statInfo, scene.SceneName);
            setOccupyRate(res, sceneCell);

            MainModel.GetInstance().DTDataManager.Clear();
        }

        private SceneCell dealSceneTestpoints(WirelessNetTestStatInfo statInfo, string sceneName)
        {
            if (!WirelessNetTestSceneCell.Instance.DistrictDic.TryGetValue(statInfo.DistrictName, out var sceneTypeInfo)
                || !sceneTypeInfo.SceneTypeDic.TryGetValue(statInfo.Type, out var sceneTypeDic)
                || !sceneTypeDic.TryGetValue(sceneName, out var scene))
            {
                return null;
            }

            return WirelessNetTestStatHelper.DealSceneTestpoints(scene);
        }

        private void setOccupyRate(WirelessNetTestKpiScene res, SceneCell sceneCell)
        {
            res.LteCellOccupyRate.FormulaData = sceneCell.LteCellOccupyRate.RateDesc;
            res.LteBtsOccupyRate.FormulaData = sceneCell.LteBtsOccupyRate.RateDesc;
            res.NrCellOccupyRate.FormulaData = sceneCell.NrCellOccupyRate.RateDesc;
            res.NrBtsOccupyRate.FormulaData = sceneCell.NrBtsOccupyRate.RateDesc;
        }
        #endregion
    }

    public class WirelessNetTestStatSubScene : WirelessNetTestStatBase
    {
        public WirelessNetTestStatSubScene(WirelessNetTestStatType statType)
         : base(statType)
        {
        }

        public override void Stat(WirelessNetTestStatInfo statInfo, WirelessNetTestResult singleDistrictData)
        {
            List<WirelessNetTestKpiSubScene> resList = dealScene(statInfo);

            singleDistrictData.SubSceneResList.AddRange(resList);
            WirelessNetTestConfig.Instance.WriteLog($"{statType}-{statInfo.Type}结果{resList.Count}条");
        }

        private List<WirelessNetTestKpiSubScene> dealScene(WirelessNetTestStatInfo statInfo)
        {
            //按子场景名汇聚结果
            var resSceneDic = new Dictionary<WirelessNetTestSubScene, StatData>();

            WirelessNetTestStatHelper.AddValidSceneData(WirelessNetTestSceneCell.Instance.FileSubSceneDic, statInfo.DataList, resSceneDic);
            WirelessNetTestStatHelper.AddValidSceneData(WirelessNetTestSceneCell.Instance.FileSubSceneDic, statInfo.EventList, resSceneDic);

            var resList = new List<WirelessNetTestKpiSubScene>();

            var formulas = WirelessNetTestReport.Instance.FormulaDic[FormulaType.全部];
            foreach (var statData in resSceneDic)
            {
                var scene = statData.Key;
                var data = statData.Value;

                var res = new WirelessNetTestKpiSubScene();
                res.DistrictName.FormulaData = statInfo.DistrictName;
                res.SubScene.FormulaData = scene.SubSceneName;

                var formulaDataDic = getFormulaData(data, formulas);
                if (!res.FormulaDataDic.TryGetValue(statInfo.Carrier, out _))
                {
                    res.FormulaDataDic.Add(statInfo.Carrier, formulaDataDic);
                }
                resList.Add(res);

                dealOtherInfo(statInfo, scene, data, res);
            }

            return resList;
        }

        #region 只有移动需要计算其他信息
        private void dealOtherInfo(WirelessNetTestStatInfo statInfo, WirelessNetTestSubScene scene, StatData data, WirelessNetTestKpiSubScene res)
        {
            if (statInfo.Carrier != CarrierStatType.移动)
            {
                return;
            }

            res.CarSpeed.FormulaData = WirelessNetTestReport.Instance.GetCarSpeed(data);

            //根据文件回放采样点,再统计对应小区和基站数
            WirelessNetTestStatHelper.ReplayFile(statInfo.DistrictID, scene.Files);
            var sceneCell = dealSceneTestpoints(statInfo, scene.SceneName, scene.SubSceneName);
            setOccupyRate(res, sceneCell);

            MainModel.GetInstance().DTDataManager.Clear();
        }

        private SceneCell dealSceneTestpoints(WirelessNetTestStatInfo statInfo, string sceneName, string subSceneName)
        {
            if (!WirelessNetTestSceneCell.Instance.DistrictDic.TryGetValue(statInfo.DistrictName, out var sceneTypeInfo)
                || !sceneTypeInfo.SceneTypeDic.TryGetValue(statInfo.Type, out var sceneTypeDic)
                || !sceneTypeDic.TryGetValue(sceneName, out var netScene)
                || !netScene.SubSceneDic.TryGetValue(subSceneName, out var subScene))
            {
                return null;
            }

            return WirelessNetTestStatHelper.DealSceneTestpoints(subScene);
        }

        private void setOccupyRate(WirelessNetTestKpiSubScene res, SceneCell sceneCell)
        {
            res.LteCellOccupyRate.FormulaData = sceneCell.LteCellOccupyRate.RateDesc;
            res.LteBtsOccupyRate.FormulaData = sceneCell.LteBtsOccupyRate.RateDesc;
            res.NrCellOccupyRate.FormulaData = sceneCell.NrCellOccupyRate.RateDesc;
            res.NrBtsOccupyRate.FormulaData = sceneCell.NrBtsOccupyRate.RateDesc;
        }
        #endregion
    }
    #endregion
}
