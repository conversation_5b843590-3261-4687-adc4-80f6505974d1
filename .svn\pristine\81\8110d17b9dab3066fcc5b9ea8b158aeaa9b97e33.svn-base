﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Data;

using MasterCom.NOP.WF.Core;
using MasterCom.RAMS.Model;
using MasterCom.NOP.DataSet;
using MasterCom.NOP.Report;

namespace MasterCom.RAMS.NOP
{
    public class TaskEventStater : ReasonStater
    {
        public override object GetResult(DateTime sTime, DateTime eTime)
        {
            DataTable focusEvents = QueryFocusEvents(sTime, eTime);
            if (focusEvents.Rows.Count == 0)
            {
                throw (new Exception("从tb_focus_event_result月表未查询到事件，请检查sp_focus_query_event存储过程"));
            }
            log.Info(string.Format("从tb_focus_event_result月表查询到{0}个事件", focusEvents.Rows.Count));

            Dictionary<string, TaskEvent> nopTaskEventDic = QueryNopTaskEvent();
            if (nopTaskEventDic.Count == 0)
            {
                throw (new Exception("未能从工单库中加载到任何工单"));
            }
            log.Info(string.Format("从工单库查询到{0}个汇聚事件", focusEvents.Rows.Count));

            DataTable resultTable = CompareEvents(focusEvents, nopTaskEventDic);
            return resultTable;
        }

        private DataColumn[] GetDataColumns()
        {
            List<DataColumn> gridColumns = new List<DataColumn>();

            DataColumn column = new DataColumn();
            column.ColumnName = "地市";
            column.DataType = typeof(string);
            gridColumns.Add(column);

            column = new DataColumn();
            column.ColumnName = "序号";
            column.DataType = typeof(int);
            gridColumns.Add(column);

            column = new DataColumn();
            column.ColumnName = "问题点月份";
            column.DataType = typeof(string);
            gridColumns.Add(column);

            column = new DataColumn();
            column.ColumnName = "归属工单名称";
            column.DataType = typeof(string);
            gridColumns.Add(column);

            column = new DataColumn();
            column.ColumnName = "问题点文件名称";
            column.DataType = typeof(string);
            gridColumns.Add(column);

            column = new DataColumn();
            column.ColumnName = "问题点名称";
            column.DataType = typeof(string);
            gridColumns.Add(column);

            column = new DataColumn();
            column.ColumnName = "预处理主因";
            column.DataType = typeof(string);
            gridColumns.Add(column);

            column = new DataColumn();
            column.ColumnName = "预处理具因";
            column.DataType = typeof(string);
            gridColumns.Add(column);

            column = new DataColumn();
            column.ColumnName = "预处理详情";
            column.DataType = typeof(string);
            gridColumns.Add(column);

            column = new DataColumn();
            column.ColumnName = "预处理优化建议";
            column.DataType = typeof(string);
            gridColumns.Add(column);

            column = new DataColumn();
            column.ColumnName = "问题点时间";
            column.DataType = typeof(string);
            gridColumns.Add(column);

            column = new DataColumn();
            column.ColumnName = "问题点经度";
            column.DataType = typeof(double);
            gridColumns.Add(column);

            column = new DataColumn();
            column.ColumnName = "问题点纬度";
            column.DataType = typeof(double);
            gridColumns.Add(column);

            column = new DataColumn();
            column.ColumnName = "TAC-CI";
            column.DataType = typeof(string);
            gridColumns.Add(column);

            column = new DataColumn();
            column.ColumnName = "小区名称";
            column.DataType = typeof(string);
            gridColumns.Add(column);

            column = new DataColumn();
            column.ColumnName = "问题道路里程(米)";
            column.DataType = typeof(double);
            gridColumns.Add(column);

            column = new DataColumn();
            column.ColumnName = "区域名称";
            column.DataType = typeof(string);
            gridColumns.Add(column);

            column = new DataColumn();
            column.ColumnName = "道路名称";
            column.DataType = typeof(string);
            gridColumns.Add(column);

            column = new DataColumn();
            column.ColumnName = "问题开始时间";
            column.DataType = typeof(string);
            gridColumns.Add(column);

            column = new DataColumn();
            column.ColumnName = "问题结束时间";
            column.DataType = typeof(string);
            gridColumns.Add(column);

            return gridColumns.ToArray();
        }

        private Dictionary<string, TaskEvent> QueryNopTaskEvent()
        {
            // 工单查询
            TaskOrderQuerier querier = new TaskOrderQuerier(MainModel.GetInstance());
            ICollection<Task> tasks = querier.Query();
            if (tasks == null)
            {
                throw (new Exception("工单查询失败"));
            }

            // 查询shema
            Schema schema = querier.QuerySchema();
            if (schema == null)
            {
                throw (new Exception("查询工单Schema失败"));
            }

            // 构造字段返回
            Dictionary<string, TaskEvent> nopTaskEventDic = new Dictionary<string, TaskEvent>();
            int errCnt = 0;
            foreach (Task task in tasks)
            {
                byte[] bytes = (byte[])task.GetValue("汇聚事件列表");
                if (bytes.Length <= 0)
                {
                    continue;
                }
                MasterCom.NOP.Report.TemplateInfo info = new MasterCom.NOP.Report.TemplateInfo(schema);
                ResultSet resultSet = ReportNetHelper.BytesToResultSet(bytes, ref schema, ref info);
                //ResultSet resultSet = ResultSet.BytesToResultSet(bytes, schema);                
                foreach (MasterCom.NOP.DataSet.Row nopRow in resultSet.Rows)
                {
                    try
                    {
                        int cityid = (int)nopRow.GetValue("districtID");
                        int fileid = (int)nopRow.GetValue("fileID");
                        int eventid = (int)nopRow.GetValue("eventID");
                        int seqid = (int)nopRow.GetValue("eventSN");
                        string key = MakeEvtKey(cityid, fileid, eventid, seqid);

                        nopTaskEventDic[key] = new TaskEvent(task, nopRow);
                    }
                    catch
                    {
                        ++errCnt;
                    }
                }
            }
            // System.Windows.Forms.MessageBox.Show(errCnt.ToString());
            return nopTaskEventDic;
        }

        private DataTable QueryFocusEvents(DateTime sTime, DateTime eTime)
        {
            DiySqlTaskEvent diyEvents = new DiySqlTaskEvent(MainModel.GetInstance());
            diyEvents.STime = sTime;
            diyEvents.ETime = eTime;
            diyEvents.Query();
            return diyEvents.ResultTable;
        }

        private DataTable CompareEvents(DataTable dtFocusEvents, Dictionary<string, TaskEvent> nopTaskEventDic)
        {
            DataTable dtTable = new DataTable();
            dtTable.Columns.AddRange(GetDataColumns());

            Dictionary<int, int> citySnDic = new Dictionary<int, int>();
            foreach (DataRow dr in dtFocusEvents.Rows)
            {
                int cityid = (int)dr["cityid"];
                int fileid = (int)dr["fileid"];
                int evtid = (int)dr["evtid"];
                int seqid = (int)dr["seqid"];
                string key = MakeEvtKey(cityid, fileid, evtid, seqid);

                if (!citySnDic.ContainsKey(cityid))
                {
                    citySnDic.Add(cityid, 0);
                }

                if (nopTaskEventDic.ContainsKey(key))
                {
                    AppendDataRow(dtTable, nopTaskEventDic[key], citySnDic);
                }
                else
                {
                    AppendDataRow(dtTable, dr, citySnDic);
                    log.Debug(string.Format("focus_event未找到归属工单: cityid={0}, fileid={1}, evtid={2}, seqid={3}",
                        cityid, fileid, evtid, seqid));
                }
            }
            return dtTable;
        }

        //private void AppendDataRow(DataTable dtTable, Task task, Dictionary<string, int> citySnDic)
        //{
        //    byte[] bytes = (byte[])task.GetValue("汇聚事件列表");
        //    MasterCom.NOP.Report.TemplateInfo info = new MasterCom.NOP.Report.TemplateInfo(schema);
        //    ResultSet resultSet = ReportNetHelper.BytesToResultSet(bytes, ref schema, ref info);

        //    foreach (MasterCom.NOP.DataSet.Row nopRow in resultSet.Rows)
        //    {
        //        string city = task.GetValue("地市") as string;
        //        if (string.IsNullOrEmpty(city))
        //        {
        //            continue;
        //        }
        //        if (!citySnDic.ContainsKey(city))
        //        {
        //            citySnDic.Add(city, 0);
        //        }

        //        DataRow dr = dtTable.NewRow();
        //        dr["地市"] = task.GetValue("地市");
        //        dr["序号"] = ++citySnDic[city];
        //        dr["问题点月份"] = ((DateTime)nopRow.GetValue("问题点时间")).ToString("yyyy-MM");
        //        dr["归属工单名称"] = task.Name;
        //        dr["问题点文件名称"] = nopRow.GetValue("文件名称");
        //        dr["问题点名称"] = nopRow.GetValue("问题点名称");
        //        dr["预处理主因"] = nopRow.GetValue("预处理主因");
        //        dr["预处理具因"] = nopRow.GetValue("预处理具因");
        //        dr["预处理详情"] = nopRow.GetValue("预处理详情");
        //        dr["预处理优化建议"] = nopRow.GetValue("预处理优化建议");
        //        dr["问题点时间"] = ((DateTime)nopRow.GetValue("问题点时间")).ToString("yyyy-MM-dd HH:mm:ss");
        //        dr["问题点经度"] = nopRow.GetValue("问题点经度");
        //        dr["问题点纬度"] = nopRow.GetValue("问题点纬度");
        //        dr["TAC-CI"] = nopRow.GetValue("TAC-CI");
        //        dr["小区名称"] = nopRow.GetValue("小区名称");
        //        dr["问题道路里程(米)"] = nopRow.GetValue("问题路段里程(米)");
        //        dr["区域名称"] = task.GetValue("区域名称");
        //        dr["道路名称"] = task.GetValue("道路名称");
        //        dr["问题开始时间"] = ((DateTime)nopRow.GetValue("beginTime")).ToString("yyyy-MM-dd HH:mm:ss");
        //        dr["问题结束时间"] = ((DateTime)nopRow.GetValue("endTime")).ToString("yyyy-MM-dd HH:mm:ss");
        //        dtTable.Rows.Add(dr);
        //    }
        //}

        private void AppendDataRow(DataTable dtTable, TaskEvent taskEvent, Dictionary<int, int> citySnDic)
        {
            Task task = taskEvent.NopTask;
            MasterCom.NOP.DataSet.Row nopRow = taskEvent.NopRow;
            int cityid = (int)nopRow.GetValue("districtID");

            DataRow dr = dtTable.NewRow();
            dr["地市"] = task.GetValue("地市");
            dr["序号"] = ++citySnDic[cityid];
            dr["问题点月份"] = ((DateTime)nopRow.GetValue("问题点时间")).ToString("yyyy-MM");
            dr["归属工单名称"] = task.Name;
            dr["问题点文件名称"] = nopRow.GetValue("文件名称");
            dr["问题点名称"] = nopRow.GetValue("问题点名称");
            dr["预处理主因"] = nopRow.GetValue("预处理主因");
            dr["预处理具因"] = nopRow.GetValue("预处理具因");
            dr["预处理详情"] = nopRow.GetValue("预处理详情");
            dr["预处理优化建议"] = nopRow.GetValue("预处理优化建议");
            dr["问题点时间"] = ((DateTime)nopRow.GetValue("问题点时间")).ToString("yyyy-MM-dd HH:mm:ss");
            dr["问题点经度"] = nopRow.GetValue("问题点经度");
            dr["问题点纬度"] = nopRow.GetValue("问题点纬度");
            dr["TAC-CI"] = nopRow.GetValue("TAC-CI");
            dr["小区名称"] = nopRow.GetValue("小区名称");
            dr["问题道路里程(米)"] = nopRow.GetValue("问题路段里程(米)");
            dr["区域名称"] = task.GetValue("区域名称");
            dr["道路名称"] = task.GetValue("道路名称");
            dr["问题开始时间"] = ((DateTime)nopRow.GetValue("beginTime")).ToString("yyyy-MM-dd HH:mm:ss");
            dr["问题结束时间"] = ((DateTime)nopRow.GetValue("endTime")).ToString("yyyy-MM-dd HH:mm:ss");
            dtTable.Rows.Add(dr);
        }

        private void AppendDataRow(DataTable dtTable, DataRow focusEvt, Dictionary<int, int> citySnDic)
        {
            int cityid = (int)focusEvt["cityid"];

            DataRow dr = dtTable.NewRow();
            dr["地市"] = DistrictManager.GetInstance().getDistrictName(cityid);
            dr["序号"] = ++citySnDic[cityid];
            dr["问题点月份"] = ((DateTime)focusEvt["evttime"]).ToString("yyyy-MM");
            dr["归属工单名称"] = "无";
            dr["问题点文件名称"] = focusEvt["filename"];
            dr["问题点名称"] = focusEvt["evtname"];
            dr["预处理主因"] = focusEvt["primarytype"];
            dr["预处理具因"] = focusEvt["specifictype"];
            dr["预处理详情"] = focusEvt["detail"];
            dr["预处理优化建议"] = focusEvt["suggest"];
            dr["问题点时间"] = ((DateTime)focusEvt["evttime"]).ToString("yyyy-MM-dd HH:mm:ss");
            dr["问题点经度"] = focusEvt["midlongitude"];
            dr["问题点纬度"] = focusEvt["midlatitude"];
            dr["问题开始时间"] = ((DateTime)focusEvt["stime"]).ToString("yyyy-MM-dd HH:mm:ss");
            dr["问题结束时间"] = ((DateTime)focusEvt["etime"]).ToString("yyyy-MM-dd HH:mm:ss");
            dtTable.Rows.Add(dr);
        }

        //private MasterCom.NOP.DataSet.Schema schema = null;

        private string MakeEvtKey(int cityid, int fileid, int evtid, int seqid)
        {
            return string.Format("{0}_{1}_{2}_{3}", cityid, fileid, evtid, seqid);
        }

        private static readonly log4net.ILog log = log4net.LogManager.GetLogger(
            System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        private class TaskEvent
        {
            public Task NopTask
            {
                get;
                private set;
            }

            public MasterCom.NOP.DataSet.Row NopRow
            {
                get;
                private set;
            }

            public TaskEvent(Task nopTask, MasterCom.NOP.DataSet.Row nopRow)
            {
                NopTask = nopTask;
                NopRow = nopRow;
            }
        }
    }
}
