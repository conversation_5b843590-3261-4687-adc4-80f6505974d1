﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Drawing.Drawing2D;
using System.Drawing;
using MapWinGIS;
using AxMapWinGIS;

namespace MasterCom.RAMS.Func
{
    public class PointMark
    {
        public double Longitude { get; set; }
        public double Latitude { get; set; }
        public string FieldValue { get; set; }  // 在一个图层文件中字段值具有唯一性
        private readonly int fieldIndex = 0;

        public PointMark(double longitude, double latitude, string fieldName)
        {
            this.Longitude = longitude;
            this.Latitude = latitude;
            this.FieldValue = fieldName;
        }

        /// <summary>
        /// 将点插入指定的shpfile中
        /// </summary>
        /// <param name="shpFile">打开的shpFile对象</param>
        /// <returns></returns>
        public bool Insert(Shapefile shpFile)
        {
            int shpIndex = shpFile.NumShapes;
            int ptIndex = 0;
            Shape shape = new Shape();
            shape.ShapeType = ShpfileType.SHP_POINT;
            MapWinGIS.Point point = new MapWinGIS.Point();
            shpFile.StartEditingShapes(true, null);
            point.x = this.Longitude;
            point.y = this.Latitude;
            shape.InsertPoint(point, ref ptIndex);
            shpFile.EditInsertShape(shape, ref shpIndex);
            shpFile.EditCellValue(this.fieldIndex, shpIndex, this.FieldValue);
            shpFile.StopEditingShapes(true, true, null);
            return true;
        }

        /// <summary>
        /// 将点从指定的shpFile中删除
        /// </summary>
        /// <param name="shpFile">打开的shpFile对象</param>
        /// <returns></returns>
        public bool Delete(Shapefile shpFile)
        {
            int shpIndex = this.IndexOf(shpFile);
            if (shpIndex == -1)
            {
                return false;
            }

            shpFile.StartEditingShapes(true, null);
            shpFile.EditDeleteShape(shpIndex);
            shpFile.StopEditingShapes(true, true, null);
            return true;
        }

        /// <summary>
        /// 在shpFile中使用指定点更新当前的点标记
        /// </summary>
        /// <param name="shpFile">打开的shpFile对象</param>
        /// <param name="newPoint"></param>
        /// <returns></returns>
        public bool Update(Shapefile shpFile, PointMark newPoint)
        {
            bool ret = this.Delete(shpFile);
            return ret ? newPoint.Insert(shpFile) : ret;
        }

        public int IndexOf(Shapefile shpFile)
        {
            int shpIndex = -1;
            for (int i = 0; i < shpFile.NumShapes; ++i)
            {
                string value = shpFile.get_CellValue(this.fieldIndex, i) as string;
                if (value.Equals(this.FieldValue))
                {
                    shpIndex = i;
                    break;
                }
            }
            return shpIndex;
        }

    }

    public class ExPointMark : PointMark
    {
        public string FileName { get; set; }
        public ExPointMark(double longitude, double latitude, string fieldValue)
            :base(longitude, latitude, fieldValue)
        {
            
        }
    }
}
