﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc.ZTLTESINR
{
    public partial class RangeValuesSetting : BaseDialog
    {
        public RangeValuesSetting(string kpiname)
        {
            InitializeComponent();
            Items = new List<SINRConditionItem>();
            KPIName = kpiname;
        }

        public List<SINRConditionItem> Items { get; set; }
        private string KPIName;

        protected void MakeRange()
        {
            int count = (int)numIntervals.Value;
            int min = (int)numMin.Value;
            int max = (int)numMax.Value;
            int IntervalValue = 0;
            
            IntervalValue = (max - min) / (count - 2);

            SINRConditionItem temp = new SINRConditionItem(1, max, 0, KPIName);
            Items.Add(temp);
            for (int i = 1; i < count - 2; i++)
            {
                temp = new SINRConditionItem(2, max - IntervalValue, max, KPIName);
                Items.Add(temp);
                max = max - IntervalValue;
            }
            temp = new SINRConditionItem(2, min, max, KPIName);
            Items.Add(temp);
            temp = new SINRConditionItem(3, min, 0, KPIName);
            Items.Add(temp);
        }

        private void buttonOK_Click(object sender, EventArgs e)
        {
            if ((int)numMin.Value > (int)numMax.Value)
            {
                MessageBox.Show("最大值应大于最小值!", "错误");
                return;
            }
            MakeRange();
            DialogResult = DialogResult.OK;
        }

        private void buttonCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
        }
    }
}
