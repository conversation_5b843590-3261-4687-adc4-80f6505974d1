﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model.Interface;

namespace MasterCom.RAMS.ZTFunc
{
    public class QueryScanCellCoverByRegion_TD : QueryScanCellCoverByRegion
    {
        private readonly Dictionary<string, ScanCellCoverInfo_TD> nameTDCellCoverInfoDic;

        public QueryScanCellCoverByRegion_TD(MainModel mainModel)
            : base(mainModel)
        {
            this.mainModel = mainModel;
            nameTDCellCoverInfoDic = new Dictionary<string, ScanCellCoverInfo_TD>();
        }

        public override string IconName
        {
            get { return ""; }
        }

        public override string Name
        {
            get { return "小区覆盖(按区域)"; }
        }

        protected override bool isValidCondition()
        {
            return true;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            if (searchGeometrys.Region == null)
            {
                return false;
            }
            return searchGeometrys.IsValidRegion;
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.Region;
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 16000, 16006, this.Name);//////
        }

        protected override bool getConditionBeforeQuery()
        {
            clear();
            if (!bExpand)
            {
                regionRectExpand(500);
                bExpand = true;
            }
            return true;
        }

        private void clear()
        {
            bExpand = false;
            nameTDCellCoverInfoDic.Clear();
        }

        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            DIYSampleGroup cellSetGroup = new DIYSampleGroup();
            cellSetGroup.ThemeName = "TDSCAN_PCCPCH_RSCP";

            DTParameter parameter = DTParameterManager.GetInstance().GetParameter("TDS_PCCPCH_RSCP");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                cellSetGroup.ColumnsDefSet.Add(pDef);
            }

            parameter = DTParameterManager.GetInstance().GetParameter("TDS_PCCPCH_CPI");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                cellSetGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("TDS_PCCPCH_Channel");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                cellSetGroup.ColumnsDefSet.Add(pDef);
            }

            parameter = DTParameterManager.GetInstance().GetParameter("TDS_PCCPCH_SIR");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                cellSetGroup.ColumnsDefSet.Add(pDef);
            }

            parameter = DTParameterManager.GetInstance().GetParameter("TDS_PCCPCH_C_I");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                cellSetGroup.ColumnsDefSet.Add(pDef);
            }

            parameter = DTParameterManager.GetInstance().GetParameter("TDS_PCCPCH_Ec_Io");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                cellSetGroup.ColumnsDefSet.Add(pDef);
            }

            parameter = DTParameterManager.GetInstance().GetParameter("TDS_PCCPCH_BLER");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                cellSetGroup.ColumnsDefSet.Add(pDef);
            }

            return cellSetGroup;
        }

        protected override void fireShowForm()
        {
            object obj = MainModel.GetInstance().GetObjectFromBlackboard(typeof(ScanCellCoverInfoForm_TD).FullName);
            ScanCellCoverInfoForm_TD scanTDCellCoverForm = obj == null ? null : obj as ScanCellCoverInfoForm_TD;
            if (scanTDCellCoverForm == null || scanTDCellCoverForm.IsDisposed)
            {
                scanTDCellCoverForm = new ScanCellCoverInfoForm_TD(MainModel);
            }
            scanTDCellCoverForm.FillData(new List<ScanCellCoverInfo_TD>(nameTDCellCoverInfoDic.Values));
            scanTDCellCoverForm.Owner = mainModel.MainForm;
            scanTDCellCoverForm.Visible = true;
            scanTDCellCoverForm.BringToFront();
        }

        protected override void fireSerialInfo()
        {
            string serialInfoName = "TDSCAN_PCCPCH_RSCP";
            foreach (MasterCom.RAMS.Func.MapSerialInfo serialInfo in mainModel.MainForm.GetMapForm().GetDTLayer().SerialInfos)
            {
                if (serialInfo.Name.Equals(serialInfoName))
                {
                    mainModel.MainForm.GetMapForm().GetDTLayer().CurFlyLinesSerialInfo = serialInfo;
                }
            }
        }

        protected override void doWithDTData(TestPoint tp)
        {
            dealTestPoint(tp);
        }

        private void dealTestPoint(TestPoint tp)
        {
            if (tp is ScanTestPoint_TD)
            {
                try
                {
                    if (bExpand)
                    {
                        regionRectExpand(-500);
                        bExpand = false;
                    }
                    addCoverInfo(tp);
                }
                catch
                {
                    //continue
                }
            }
        }

        private void addCoverInfo(TestPoint tp)
        {
            ScanTestPoint_TD tdTP = tp as ScanTestPoint_TD;
            for (int idx = 0; idx < 50; ++idx)
            {
                TDCell tdCell = tp.GetCell_TDScan(idx);
                if (tdCell != null && condition.Geometorys.GeoOp.Contains(tdCell.Longitude, tdCell.Latitude))
                {
                    ScanCellCoverInfo_TD tdCellCoverInfo;
                    if (!nameTDCellCoverInfoDic.TryGetValue(tdCell.Name, out tdCellCoverInfo))
                    {
                        tdCellCoverInfo = new ScanCellCoverInfo_TD(tdCell);
                        nameTDCellCoverInfoDic.Add(tdCell.Name, tdCellCoverInfo);
                    }
                    tdCellCoverInfo.TpInfoList.Add(new ScanCellCoverPointInfo_TD(tdCell, tdTP));
                    tdCellCoverInfo.DealParam(tdTP);
                }
            }
        }
    }

    public class QueryScanCellCoverByRegion_WCDMA : QueryScanCellCoverByRegion
    {
        private readonly Dictionary<string, ScanCellCoverInfo_W> nameWCellCoverInfoDic;

        public QueryScanCellCoverByRegion_WCDMA(MainModel mainModel)
            : base(mainModel)
        {
            this.mainModel = mainModel;
            nameWCellCoverInfoDic = new Dictionary<string, ScanCellCoverInfo_W>();
        }

        public override string IconName
        {
            get { return ""; }
        }

        public override string Name
        {
            get { return "WCDMA小区覆盖(按区域)"; }
        }

        protected override bool isValidCondition()
        {
            return true;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            if (searchGeometrys.Region == null)
            {
                return false;
            }
            return searchGeometrys.IsValidRegion;
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.Region;
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 32000, 32006, this.Name);
        }

        protected override bool getConditionBeforeQuery()
        {
            clear();
            if (!bExpand)
            {
                regionRectExpand(500);
                bExpand = true;
            }
            return true;
        }

        private void clear()
        {
            bExpand = false;
            nameWCellCoverInfoDic.Clear();
        }

        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            DIYSampleGroup cellSetGroup = new DIYSampleGroup();
            cellSetGroup.ThemeName = "WS_CPICHTotalRSCP";

            DTParameter parameter = DTParameterManager.GetInstance().GetParameter("WS_CPICHTotalRSCP");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                cellSetGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("WS_CPICHPilot");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                cellSetGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("WS_CPICHChannel");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                cellSetGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("WS_CPICHSIR");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                cellSetGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("WS_CPICHTotalEcIo");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                cellSetGroup.ColumnsDefSet.Add(pDef);
            }
            return cellSetGroup;
        }

        protected override void fireShowForm()
        {
            object obj = MainModel.GetInstance().GetObjectFromBlackboard(typeof(ScanCellCoverInfoForm_W).FullName);
            ScanCellCoverInfoForm_W scanTDCellCoverForm = obj == null ? null : obj as ScanCellCoverInfoForm_W;
            if (scanTDCellCoverForm == null || scanTDCellCoverForm.IsDisposed)
            {
                scanTDCellCoverForm = new ScanCellCoverInfoForm_W(MainModel);
            }
            scanTDCellCoverForm.FillData(new List<ScanCellCoverInfo_W>(nameWCellCoverInfoDic.Values));
            scanTDCellCoverForm.Owner = mainModel.MainForm;
            scanTDCellCoverForm.Visible = true;
            scanTDCellCoverForm.BringToFront();
        }

        protected override void fireSerialInfo()
        {
            string serialInfoName = "WS_CPICHTotalRSCP";
            foreach (MasterCom.RAMS.Func.MapSerialInfo serialInfo in mainModel.MainForm.GetMapForm().GetDTLayer().SerialInfos)
            {
                if (serialInfo.Name.Equals(serialInfoName))
                {
                    mainModel.MainForm.GetMapForm().GetDTLayer().CurFlyLinesSerialInfo = serialInfo;
                }
            }
        }

        protected override void doWithDTData(TestPoint tp)
        {
            dealTestPoint(tp);
        }

        private void dealTestPoint(TestPoint tp)
        {
            if (tp is ScanTestPoint_W)
            {
                try
                {
                    if (bExpand)
                    {
                        regionRectExpand(-500);
                        bExpand = false;
                    }
                    addCoverInfo(tp);
                }
                catch
                {
                    //continue
                }
            }
        }

        private void addCoverInfo(TestPoint tp)
        {
            ScanTestPoint_W wTP = tp as ScanTestPoint_W;
            for (int idx = 0; idx < 50; ++idx)
            {
                WCell wCell = tp.GetCell_WScan(idx);
                if (wCell != null && condition.Geometorys.GeoOp.Contains(wCell.Longitude, wCell.Latitude))
                {
                    ScanCellCoverInfo_W wCellCoverInfo;
                    if (!nameWCellCoverInfoDic.TryGetValue(wCell.Name, out wCellCoverInfo))
                    {
                        wCellCoverInfo = new ScanCellCoverInfo_W(wCell);
                        nameWCellCoverInfoDic.Add(wCell.Name, wCellCoverInfo);
                    }
                    wCellCoverInfo.TpInfoList.Add(new ScanCellCoverPointInfo_W(wCell, wTP));
                    wCellCoverInfo.DealParam(wTP);
                }
            }
        }
    }

    public class ScanCellCoverInfo_TD : ScanCellCoverInfo
    {
        public TDCell CoverTDCell { get; set; }
        public string CellName { get; set; }
        public int FREQ { get; set; }
        public int CPI { get; set; }

        private float rscpMax;
        private float rscpMin;
        private float rscpMean;
        private int rscpSampleNum;
        public string RscpMax
        {
            get
            {
                if (rscpSampleNum > 0)
                {
                    return rscpMax.ToString();
                }
                return "";
            }
        }
        public string RscpMin
        {
            get
            {
                if (rscpSampleNum > 0)
                {
                    return rscpMin.ToString();
                }
                return "";
            }
        }
        public string RscpMean
        {
            get
            {
                if (rscpSampleNum > 0)
                {
                    return Math.Round(rscpMean / rscpSampleNum, 2).ToString();
                }
                return "";
            }
        }

        private float c2iMax;
        private float c2iMin;
        private float c2iMean;
        private int c2iSampleNum;
        public string C2IMax
        {
            get
            {
                if (c2iSampleNum > 0)
                {
                    return c2iMax.ToString();
                }
                return "";
            }
        }
        public string C2IMin
        {
            get
            {
                if (c2iSampleNum > 0)
                {
                    return c2iMin.ToString();
                }
                return "";
            }
        }
        public string C2IMean
        {
            get
            {
                if (c2iSampleNum > 0)
                {
                    return Math.Round(c2iMean / c2iSampleNum, 2).ToString();
                }
                return "";
            }
        }

        private float sirMax;
        private float sirMin;
        private float sirMean;
        private int sirSampleNum;
        public string SirMax
        {
            get
            {
                if (sirSampleNum > 0)
                {
                    return sirMax.ToString();
                }
                return "";
            }
        }
        public string SirMin
        {
            get
            {
                if (sirSampleNum > 0)
                {
                    return sirMin.ToString();
                }
                return "";
            }
        }
        public string SirMean
        {
            get
            {
                if (sirSampleNum > 0)
                {
                    return Math.Round(sirMean / sirSampleNum, 2).ToString();
                }
                return "";
            }
        }

        private float ecioMax;
        private float ecioMin;
        private float ecioMean;
        private int ecioSampleNum;
        public string EcioMax
        {
            get
            {
                if (ecioSampleNum > 0)
                {
                    return ecioMax.ToString();
                }
                return "";
            }
        }
        public string EcioMin
        {
            get
            {
                if (ecioSampleNum > 0)
                {
                    return ecioMin.ToString();
                }
                return "";
            }
        }
        public string EcioMean
        {
            get
            {
                if (ecioSampleNum > 0)
                {
                    return Math.Round(ecioMean * 1.0 / ecioSampleNum, 2).ToString();
                }
                return "";
            }
        }

        private float blerMax;
        private float blerMin;
        private float blerMean;
        private int blerSampleNum;
        public string BlerMax
        {
            get
            {
                if (blerSampleNum > 0)
                {
                    return blerMax.ToString();
                }
                return "";
            }
        }
        public string BlerMin
        {
            get
            {
                if (blerSampleNum > 0)
                {
                    return blerMin.ToString();
                }
                return "";
            }
        }
        public string BlerMean
        {
            get
            {
                if (blerSampleNum > 0)
                {
                    return Math.Round(blerMean * 1.0 / blerSampleNum, 2).ToString();
                }
                return "";
            }
        }

        public int TPCount { get { return TpInfoList.Count; } }
        public List<ScanCellCoverPointInfo_TD> TpInfoList { get; set; }

        public ScanCellCoverInfo_TD(TDCell tdCell)
        {
            this.CoverTDCell = tdCell;
            this.CellName = CoverTDCell.Name;
            this.FREQ = CoverTDCell.FREQ;
            this.CPI = CoverTDCell.CPI;
            this.Longitude = CoverTDCell.Longitude;
            this.Latitude = CoverTDCell.Latitude;
            initParam();
            TpInfoList = new List<ScanCellCoverPointInfo_TD>();
        }

        private void initParam()
        {
            rscpMax = -10000000;
            rscpMin = 10000000;
            rscpMean = 0;
            rscpSampleNum = 0;

            c2iMax = -10000000;
            c2iMin = 10000000;
            c2iMean = 0;
            c2iSampleNum = 0;

            sirMax = -10000000;
            sirMin = 10000000;
            sirMean = 0;
            sirSampleNum = 0;

            ecioMax = -10000000;
            ecioMin = 10000000;
            ecioMean = 0;
            ecioSampleNum = 0;

            blerMax = -10000000;
            blerMin = 10000000;
            blerMean = 0;
            blerSampleNum = 0;
        }

        public void DealParam(ScanTestPoint_TD tp)
        {
            float rscp = getTPData((float?)tp["TDS_PCCPCH_RSCP", 0]);
            float c2i = getTPData((float?)tp["TDS_PCCPCH_C_I", 0]);
            float sir = getTPData((float?)tp["TDS_PCCPCH_SIR", 0]);
            float ecio = getTPData((float?)tp["TDS_PCCPCH_Ec_Io", 0]);
            float bler = getTPData((float?)tp["TDS_PCCPCH_BLER", 0]);

            getValidRscpSample(rscp);
            getValidC2iSample(c2i);
            getValidSinrSample(sir);
            getValidEcioSample(ecio);
            getValidBlerSample(bler);
        }

        private float getTPData(float? value)
        {
            if (value == null)
            {
                return -10000000;
            }
            else
            {
                return (float)value;
            }
        }

        private void getValidBlerSample(float bler)
        {
            if (bler >= 0 && bler <= 100)
            {
                setValue(bler, ref blerMax, ref blerMin, ref blerMean, ref blerSampleNum);
            }
        }

        private void getValidEcioSample(float ecio)
        {
            if (ecio >= -140 && ecio <= 0)
            {
                setValue(ecio, ref ecioMax, ref ecioMin, ref ecioMean, ref ecioSampleNum);
            }
        }

        private void getValidSinrSample(float sir)
        {
            if (sir >= -999 && sir <= 9000000)
            {
                setValue(sir, ref sirMax, ref sirMin, ref sirMean, ref sirSampleNum);
            }
        }

        private void getValidC2iSample(float c2i)
        {
            if (c2i >= -35 && c2i <= 35)
            {
                setValue(c2i, ref c2iMax, ref c2iMin, ref c2iMean, ref c2iSampleNum);
            }
        }

        private void getValidRscpSample(float rscp)
        {
            if (rscp >= -140 && rscp <= -10)
            {
                setValue(rscp, ref rscpMax, ref rscpMin, ref rscpMean, ref rscpSampleNum);
            }
        }

        private void setValue(float vllue, ref float maxValue, ref float minValue, ref float meanValue, ref int sampleNum)
        {
            if (maxValue < vllue)
            {
                maxValue = vllue;
            }
            if (minValue > vllue)
            {
                minValue = vllue;
            }
            meanValue += vllue;
            sampleNum++;
        }
    }

    public class ScanCellCoverInfo_W : ScanCellCoverInfo
    {
        public WCell CoverWCell { get; set; }
        public string CellName { get; set; }
        public int FREQ { get; set; }
        public int CPI { get; set; }

        private float rscpMax;
        private float rscpMin;
        private float rscpMean;
        private int rscpSampleNum;
        public string RscpMax
        {
            get
            {
                if (rscpSampleNum > 0)
                {
                    return rscpMax.ToString();
                }
                return "";
            }
        }
        public string RscpMin
        {
            get
            {
                if (rscpSampleNum > 0)
                {
                    return rscpMin.ToString();
                }
                return "";
            }
        }
        public string RscpMean
        {
            get
            {
                if (rscpSampleNum > 0)
                {
                    return Math.Round(rscpMean / rscpSampleNum, 2).ToString();
                }
                return "";
            }
        }

        private float c2iMax;
        private float c2iMin;
        private float c2iMean;
        private int c2iSampleNum;
        public string C2IMax
        {
            get
            {
                if (c2iSampleNum > 0)
                {
                    return c2iMax.ToString();
                }
                return "";
            }
        }
        public string C2IMin
        {
            get
            {
                if (c2iSampleNum > 0)
                {
                    return c2iMin.ToString();
                }
                return "";
            }
        }
        public string C2IMean
        {
            get
            {
                if (c2iSampleNum > 0)
                {
                    return Math.Round(c2iMean / c2iSampleNum, 2).ToString();
                }
                return "";
            }
        }

        private float sirMax;
        private float sirMin;
        private float sirMean;
        private int sirSampleNum;
        public string SirMax
        {
            get
            {
                if (sirSampleNum > 0)
                {
                    return sirMax.ToString();
                }
                return "";
            }
        }
        public string SirMin
        {
            get
            {
                if (sirSampleNum > 0)
                {
                    return sirMin.ToString();
                }
                return "";
            }
        }
        public string SirMean
        {
            get
            {
                if (sirSampleNum > 0)
                {
                    return Math.Round(sirMean / sirSampleNum, 2).ToString();
                }
                return "";
            }
        }

        private float ecioMax;
        private float ecioMin;
        private float ecioMean;
        private int ecioSampleNum;
        public string EcioMax
        {
            get
            {
                if (ecioSampleNum > 0)
                {
                    return ecioMax.ToString();
                }
                return "";
            }
        }
        public string EcioMin
        {
            get
            {
                if (ecioSampleNum > 0)
                {
                    return ecioMin.ToString();
                }
                return "";
            }
        }
        public string EcioMean
        {
            get
            {
                if (ecioSampleNum > 0)
                {
                    return Math.Round(ecioMean * 1.0 / ecioSampleNum, 2).ToString();
                }
                return "";
            }
        }

        private float blerMax;
        private float blerMin;
        private float blerMean;
        private int blerSampleNum;
        public string BlerMax
        {
            get
            {
                if (blerSampleNum > 0)
                {
                    return blerMax.ToString();
                }
                return "";
            }
        }
        public string BlerMin
        {
            get
            {
                if (blerSampleNum > 0)
                {
                    return blerMin.ToString();
                }
                return "";
            }
        }
        public string BlerMean
        {
            get
            {
                if (blerSampleNum > 0)
                {
                    return Math.Round(blerMean * 1.0 / blerSampleNum, 2).ToString();
                }
                return "";
            }
        }

        public int TPCount { get { return TpInfoList.Count; } }
        public List<ScanCellCoverPointInfo_W> TpInfoList { get; set; }

        public ScanCellCoverInfo_W(WCell wCell)
        {
            this.CoverWCell = wCell;
            this.CellName = CoverWCell.Name;
            this.FREQ = CoverWCell.UARFCN;
            this.CPI = CoverWCell.PSC;
            this.Longitude = CoverWCell.Longitude;
            this.Latitude = CoverWCell.Latitude;
            initParam();
            TpInfoList = new List<ScanCellCoverPointInfo_W>();
        }

        private void initParam()
        {
            rscpMax = -10000000;
            rscpMin = 10000000;
            rscpMean = 0;
            rscpSampleNum = 0;

            c2iMax = -10000000;
            c2iMin = 10000000;
            c2iMean = 0;
            c2iSampleNum = 0;

            sirMax = -10000000;
            sirMin = 10000000;
            sirMean = 0;
            sirSampleNum = 0;

            ecioMax = -10000000;
            ecioMin = 10000000;
            ecioMean = 0;
            ecioSampleNum = 0;

            blerMax = -10000000;
            blerMin = 10000000;
            blerMean = 0;
            blerSampleNum = 0;
        }

        public void DealParam(ScanTestPoint_W tp)
        {
            float rscp = getTPData((float?)tp["WS_CPICHTotalRSCP", 0]);
            float c2i = getTPData((float?)tp["WS_CPICHCI", 0]);
            float sir = getTPData((float?)tp["WS_CPICHSIR", 0]);
            float ecio = getTPData((float?)tp["WS_CPICHTotalEcIo", 0]);
            float bler = getTPData((float?)tp["WS_CPICHIo", 0]);

            getValidRscpSample(rscp);
            getValidC2iSample(c2i);
            getValidSinrSample(sir);
            getValidEcioSample(ecio);
            getValidBlerSample(bler);
        }

        private float getTPData(float? value)
        {
            if (value == null)
            {
                return -10000000;
            }
            else
            {
                return (float)value;
            }
        }

        private void getValidBlerSample(float bler)
        {
            if (bler >= 0 && bler <= 100)
            {
                setValue(bler, ref blerMax, ref blerMin, ref blerMean, ref blerSampleNum);
            }
        }

        private void getValidEcioSample(float ecio)
        {
            if (ecio >= -140 && ecio <= 0)
            {
                setValue(ecio, ref ecioMax, ref ecioMin, ref ecioMean, ref ecioSampleNum);
            }
        }

        private void getValidSinrSample(float sir)
        {
            if (sir >= -999 && sir <= 9000000)
            {
                setValue(sir, ref sirMax, ref sirMin, ref sirMean, ref sirSampleNum);
            }
        }

        private void getValidC2iSample(float c2i)
        {
            if (c2i >= -35 && c2i <= 35)
            {
                setValue(c2i, ref c2iMax, ref c2iMin, ref c2iMean, ref c2iSampleNum);
            }
        }

        private void getValidRscpSample(float rscp)
        {
            if (rscp >= -140 && rscp <= -10)
            {
                setValue(rscp, ref rscpMax, ref rscpMin, ref rscpMean, ref rscpSampleNum);
            }
        }

        private void setValue(float vllue, ref float maxValue, ref float minValue, ref float meanValue, ref int sampleNum)
        {
            if (maxValue < vllue)
            {
                maxValue = vllue;
            }
            if (minValue > vllue)
            {
                minValue = vllue;
            }
            meanValue += vllue;
            sampleNum++;
        }
    }


    public class ScanCellCoverPointInfo_TD
    {
        public TestPoint TP { get; set; }
        public double Longitude { get { return TP.Longitude; } }
        public double Latitude { get { return TP.Latitude; } }

        public TDCell CoverTDCell { get; set; }
        public double Distance
        {
            get
            {
                return Math.Round(MasterCom.Util.MathFuncs.GetDistance(CoverTDCell.Longitude, CoverTDCell.Latitude, TP.Longitude, TP.Latitude), 2);
            }
        }
        private float rscp;
        public string Rscp
        {
            get
            {
                if (rscp >= -140 && rscp <= -10)
                {
                    return Math.Round(rscp, 2).ToString();
                }
                return "";
            }
        }

        private float c2i;
        public string C2I
        {
            get
            {
                if (c2i >= -35 && c2i <= 35)
                {
                    return c2i.ToString();
                }
                return "";
            }
        }

        private float sir;
        public string Sir
        {
            get
            {
                if (sir >= -999 && sir <= 9000000)
                {
                    return sir.ToString();
                }
                return "";
            }
        }

        private float ecio;
        public string Ecio
        {
            get
            {
                if (ecio >= -140 && ecio <= 0)
                {
                    return ecio.ToString();
                }
                return "";
            }
        }

        private float bler;
        public string Bler
        {
            get
            {
                if (bler >= 0 && bler <= 100)
                {
                    return bler.ToString();
                }
                return "";
            }
        }

        public ScanCellCoverPointInfo_TD(TDCell tdCell, ScanTestPoint_TD tp)
        {
            this.CoverTDCell = tdCell;
            this.TP = tp;
            fill(tp);
        }

        private void fill(ScanTestPoint_TD tp)
        {
            this.rscp = (float?)tp["TDS_PCCPCH_RSCP", 0] == null ? -10000000 : (float)(float?)tp["TDS_PCCPCH_RSCP", 0];
            this.c2i = (float?)tp["TDS_PCCPCH_C_I", 0] == null ? -10000000 : (float)(float?)tp["TDS_PCCPCH_C_I", 0];
            this.sir = (float?)tp["TDS_PCCPCH_SIR", 0] == null ? -10000000 : (float)(float?)tp["TDS_PCCPCH_SIR", 0];
            this.ecio = (float?)tp["TDS_PCCPCH_Ec_Io", 0] == null ? -10000000 : (float)(float?)tp["TDS_PCCPCH_Ec_Io", 0];
            this.bler = (float?)tp["TDS_PCCPCH_BLER", 0] == null ? -10000000 : (float)(float?)tp["TDS_PCCPCH_BLER", 0];
        }
    }

     public class ScanCellCoverPointInfo_W
    {
        public TestPoint TP { get; set; }
        public double Longitude { get { return TP.Longitude; } }
        public double Latitude { get { return TP.Latitude; } }

        public WCell CoverWCell { get; set; }
        public double Distance
        {
            get
            {
                return Math.Round(MasterCom.Util.MathFuncs.GetDistance(CoverWCell.Longitude, CoverWCell.Latitude, TP.Longitude, TP.Latitude), 2);
            }
        }
        private float rscp;
        public string Rscp
        {
            get
            {
                if (rscp >= -140 && rscp <= -10)
                {
                    return Math.Round(rscp, 2).ToString();
                }
                return "";
            }
        }

        private float c2i;
        public string C2I
        {
            get
            {
                if (c2i >= -35 && c2i <= 35)
                {
                    return c2i.ToString();
                }
                return "";
            }
        }

        private float sir;
        public string Sir
        {
            get
            {
                if (sir >= -999 && sir <= 9000000)
                {
                    return sir.ToString();
                }
                return "";
            }
        }

        private float ecio;
        public string Ecio
        {
            get
            {
                if (ecio >= -140 && ecio <= 0)
                {
                    return ecio.ToString();
                }
                return "";
            }
        }

        private float bler;
        public string Bler
        {
            get
            {
                if (bler >= 0 && bler <= 100)
                {
                    return bler.ToString();
                }
                return "";
            }
        }

        public ScanCellCoverPointInfo_W(WCell wCell, ScanTestPoint_W tp)
        {
            this.CoverWCell = wCell;
            this.TP = tp;
            fill(tp);
        }

        private void fill(ScanTestPoint_W tp)
        {
            this.rscp = (float?)tp["TDS_PCCPCH_RSCP", 0] == null ? -10000000 : (float)(float?)tp["TDS_PCCPCH_RSCP", 0];
            this.c2i = (float?)tp["TDS_PCCPCH_C_I", 0] == null ? -10000000 : (float)(float?)tp["TDS_PCCPCH_C_I", 0];
            this.sir = (float?)tp["TDS_PCCPCH_SIR", 0] == null ? -10000000 : (float)(float?)tp["TDS_PCCPCH_SIR", 0];
            this.ecio = (float?)tp["TDS_PCCPCH_Ec_Io", 0] == null ? -10000000 : (float)(float?)tp["TDS_PCCPCH_Ec_Io", 0];
            this.bler = (float?)tp["TDS_PCCPCH_BLER", 0] == null ? -10000000 : (float)(float?)tp["TDS_PCCPCH_BLER", 0];
        }
    }
}
