﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.ZTFunc;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using System.Collections.ObjectModel;
using System.Drawing.Drawing2D;
using System.Drawing;
using MasterCom.MTGis;
using MapWinGIS;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTAntennaBase : DIYAnalyseByPeriodBackgroundBase_Sample
    {
        public ZTAntennaBase(MainModel mainModel)
            : base(mainModel)
        {
            isAddSampleToDTDataManager = false;
        }
        public override string Name
        {
            get { return "天线分析基类"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 13000, 12330, this.Name);
        }

        public MTPolygon mapOp2 { get; set; }
        public string[] strProject { get; set; }
        public List<BTS> btsList { get; set; } = new List<BTS>();
        public List<TDNodeB> nodebList { get; set; } = new List<TDNodeB>();
        public List<LTEBTS> lteBTSList { get; set; } = new List<LTEBTS>();
        public Dictionary<string, Dictionary<string, MapOperation2>> mutRegionMopDic { get; set; }

        /// <summary>
        /// 查询前进行处理
        /// </summary>
        public void setVarBeforQuery()
        {
            btsList.Clear();
            nodebList.Clear();
            lteBTSList.Clear();
            curSelDIYSampleGroup = getCurSelDIYSampleGroupPrepare();
            if (curSelDIYSampleGroup == null)
                return;
            MainModel.ClearDTData();

            MapWinGIS.Shape curRegion = null;
            if (Condition.Geometorys == null)
            {
                curRegion = getDefaultRegionByCity();
                SearchGeometrys sGeo = new SearchGeometrys();
                sGeo.Region = curRegion;
                Condition.Geometorys = sGeo;
            }
            else
            {
                curRegion = Condition.Geometorys.Region;
            }
            mapOp2 = new MTPolygon();
            if (Condition.Geometorys.GeoOp2 == null)
            {
                mapOp2.Append(curRegion);
            }
            else
            {
                mapOp2 = Condition.Geometorys.GeoOp2;
            }
        }

        static Dictionary<string, MapRegion> mapRegionList = initMapRegion();
        private static Dictionary<string, MapRegion> initMapRegion()
        {
            Dictionary<string, MapRegion> gdMapRegionList = new Dictionary<string, MapRegion>();
            gdMapRegionList.Add("广州", new MapRegion("广州", new MapPoint(113.1824493, 23.1951265), new MapPoint(113.2390905, 23.1951265),
                new MapPoint(113.2390905, 23.1420254), new MapPoint(113.1824493, 23.1420254)));
            gdMapRegionList.Add("深圳", new MapRegion("深圳", new MapPoint(113.7414700, 22.7461380), new MapPoint(113.8005120, 22.7461380),
                new MapPoint(113.8005120, 22.7003460), new MapPoint(113.7414700, 22.7003460)));
            gdMapRegionList.Add("珠海", new MapRegion("珠海", new MapPoint(113.5395753, 22.2948729), new MapPoint(113.5779195, 22.2948729),
                new MapPoint(113.5779195, 22.2594025), new MapPoint(113.5395753, 22.2594025)));
            gdMapRegionList.Add("汕头", new MapRegion("汕头", new MapPoint(116.7210108, 23.4909781), new MapPoint(116.7745572, 23.4909781),
                new MapPoint(116.7745572, 23.4470190), new MapPoint(116.7210108, 23.4470190)));
            gdMapRegionList.Add("佛山", new MapRegion("佛山", new MapPoint(113.0474747, 23.0666261), new MapPoint(113.1159694, 23.0666261),
                new MapPoint(113.1159694, 23.0379173), new MapPoint(113.0474747, 23.0379173)));
            gdMapRegionList.Add("韶关", new MapRegion("韶关", new MapPoint(113.508863, 24.8261237), new MapPoint(113.5872684, 24.8261237),
                new MapPoint(113.5872684, 24.7589631), new MapPoint(113.508863, 24.7589631)));
            gdMapRegionList.Add("河源", new MapRegion("河源", new MapPoint(114.6302481, 23.7877138), new MapPoint(114.7387757, 23.7877138),
                new MapPoint(114.7387757, 23.7177063), new MapPoint(114.6302481, 23.7177063)));
            gdMapRegionList.Add("梅州", new MapRegion("梅州", new MapPoint(116.0518969, 24.3352961), new MapPoint(116.1386665, 24.3352961),
                new MapPoint(116.1386665, 24.2596331), new MapPoint(116.0518969, 24.2596331)));
            gdMapRegionList.Add("惠州", new MapRegion("惠州", new MapPoint(114.3832720, 23.1679775), new MapPoint(114.4411033, 23.1679775),
                new MapPoint(114.4411033, 23.1073924), new MapPoint(114.3832720, 23.1073924)));
            gdMapRegionList.Add("汕尾", new MapRegion("汕尾", new MapPoint(115.3497591, 22.8083738), new MapPoint(115.4014227, 22.8083738),
                new MapPoint(115.4014227, 22.7620036), new MapPoint(115.3497591, 22.7620036)));
            gdMapRegionList.Add("东莞", new MapRegion("东莞", new MapPoint(113.7025927, 23.0150277), new MapPoint(113.7461705, 23.0150277),
                new MapPoint(113.7461705, 22.9737136), new MapPoint(113.7025927, 22.9737136)));
            gdMapRegionList.Add("中山", new MapRegion("中山", new MapPoint(113.2912535, 22.5332520), new MapPoint(113.3380597, 22.5332520),
                new MapPoint(113.3380597, 22.4952165), new MapPoint(113.2912535, 22.4952165)));
            gdMapRegionList.Add("江门", new MapRegion("江门", new MapPoint(113.0472378, 22.6443412), new MapPoint(113.0858332, 22.6443412),
                new MapPoint(113.0858332, 22.6042962), new MapPoint(113.0472378, 22.6042962)));
            gdMapRegionList.Add("阳江", new MapRegion("阳江", new MapPoint(111.9936078, 21.8970772), new MapPoint(111.9666530, 21.8970772),
                new MapPoint(111.9666530, 21.8432944), new MapPoint(111.9936078, 21.8432944)));
            gdMapRegionList.Add("湛江", new MapRegion("湛江", new MapPoint(110.2823614, 21.3176244), new MapPoint(110.3461704, 21.3176244),
                new MapPoint(110.3461704, 21.2510807), new MapPoint(110.2823614, 21.2510807)));
            gdMapRegionList.Add("茂名", new MapRegion("茂名", new MapPoint(110.9128702, 21.6663430), new MapPoint(110.9457767, 21.6663430),
                new MapPoint(110.9457767, 21.6464460), new MapPoint(110.9128702, 21.6464460)));
            gdMapRegionList.Add("肇庆", new MapRegion("肇庆", new MapPoint(112.3979705, 23.0884794), new MapPoint(112.4633718, 23.0884794),
                new MapPoint(112.4633718, 23.0454007), new MapPoint(112.3979705, 23.0454007)));
            gdMapRegionList.Add("清远", new MapRegion("清远", new MapPoint(113.0010117, 23.7307296), new MapPoint(113.0607662, 23.7307296),
                new MapPoint(113.0607662, 23.6929627), new MapPoint(113.0010117, 23.6929627)));
            gdMapRegionList.Add("潮州", new MapRegion("潮州", new MapPoint(116.5419196, 23.6911073), new MapPoint(116.6064549, 23.6911073),
                new MapPoint(116.6064549, 23.6274460), new MapPoint(116.5419196, 23.6274460)));
            gdMapRegionList.Add("揭阳", new MapRegion("揭阳", new MapPoint(116.2910224, 23.5682456), new MapPoint(116.3673679, 23.5682456),
                new MapPoint(116.3673679, 23.4940958), new MapPoint(116.2910224, 23.4940958)));
            gdMapRegionList.Add("云浮", new MapRegion("云浮", new MapPoint(112.0079777, 22.9584125), new MapPoint(112.0907054, 22.9584125),
                new MapPoint(112.0907054, 22.8941338), new MapPoint(112.0079777, 22.8941338)));
            return gdMapRegionList;
        }

        public class MapRegion
        {
            public MapRegion(string name, MapPoint ltPoint, MapPoint rtPoint, MapPoint rbPoint, MapPoint lbPoint)
            {
                Name = name;
                LTPoint = ltPoint;
                RTPoint = rtPoint;
                RBPoint = rbPoint;
                LBPoint = lbPoint;
            }

            public string Name { get; set; }
            public MapPoint LTPoint { get; set; }
            public MapPoint LBPoint { get; set; }
            public MapPoint RTPoint { get; set; }
            public MapPoint RBPoint { get; set; }
        }

        public class MapPoint
        {
            public MapPoint(double longitude, double latitude)
            {
                Longitude = longitude;
                Latitude = latitude;
            }

            public double Longitude { get; set; }
            public double Latitude { get; set; }
        }

        #region 图层管理类
        /// <summary>
        /// 按城市获取默认图层
        /// </summary>
        public MapWinGIS.Shape getDefaultRegionByCity()
        {
            string strCity = DistrictManager.GetInstance().getDistrictName(MainModel.DistrictID);
            MapWinGIS.Shape defaultShape = new MapWinGIS.Shape();
            if (mapRegionList.ContainsKey(strCity))
            {
                MapRegion region = mapRegionList[strCity];
                defaultShape = getDefaultRegionByCity(region.LTPoint, region.LBPoint, region.RTPoint, region.RBPoint);
            }
            return defaultShape;
        }

        public MapWinGIS.Shape getDefaultRegionByCity(MapPoint ltPoint, MapPoint lbPoint, MapPoint rtPoint, MapPoint rbPoint)
        {
            MapWinGIS.Shape defaultShape = new MapWinGIS.Shape();
            defaultShape.Create(ShpfileType.SHP_POLYGON);

            int idx = 0;
            MapWinGIS.Point pnt1 = new MapWinGIS.Point();
            pnt1.x = ltPoint.Longitude;
            pnt1.y = ltPoint.Latitude;
            defaultShape.InsertPoint(pnt1, ref idx);
            idx++;

            MapWinGIS.Point pnt2 = new MapWinGIS.Point();
            pnt2.x = lbPoint.Longitude;
            pnt2.y = lbPoint.Latitude;
            defaultShape.InsertPoint(pnt2, ref idx);
            idx++;

            MapWinGIS.Point pnt3 = new MapWinGIS.Point();
            pnt3.x = rtPoint.Longitude;
            pnt3.y = rtPoint.Latitude;
            defaultShape.InsertPoint(pnt3, ref idx);
            idx++;

            MapWinGIS.Point pnt4 = new MapWinGIS.Point();
            pnt4.x = rbPoint.Longitude;
            pnt4.y = rbPoint.Latitude;
            defaultShape.InsertPoint(pnt4, ref idx);

            return defaultShape;
        }

        /// <summary>
        /// 获取预存或圈选的区域
        /// </summary>
        public void InitRegionMop2()
        {
            mutRegionMopDic = new Dictionary<string, Dictionary<string, MapOperation2>>();
            Dictionary<string, List<ResvRegion>> resvRegionsDic = MainModel.SearchGeometrys.SelectedResvRegionDic;
            MapWinGIS.Shape gmt = MainModel.SearchGeometrys.Region;
            if (resvRegionsDic != null && resvRegionsDic.Count > 0)
            {
                foreach (string strGridType in resvRegionsDic.Keys)
                {
                    if (!mutRegionMopDic.ContainsKey(strGridType))
                    {
                        addRegionMap(resvRegionsDic, strGridType);
                    }
                }
            }
            else if (gmt != null)//单个区域
            {
                Dictionary<string, MapOperation2> regionMopDic =
                    new Dictionary<string, MapOperation2>();
                MapOperation2 mapOp = new MapOperation2();
                mapOp.FillPolygon(gmt);
                regionMopDic.Add("当前区域", mapOp);
                mutRegionMopDic.Add("无网格类型", regionMopDic);
            }
            if (mutRegionMopDic.Count == 0)
            {
                Dictionary<string, MapOperation2> regionMopDic =
                    new Dictionary<string, MapOperation2>();
                foreach (string strshap in ShapeHelper.ShapeAndRegionNameDic.Keys)
                {
                    MapOperation2 mapOp = new MapOperation2();
                    mapOp.FillPolygon(ShapeHelper.ShapeAndRegionNameDic[strshap][0]);
                    regionMopDic.Add(strshap, mapOp);
                }
                mutRegionMopDic.Add("后台区域", regionMopDic);
            }
        }

        private void addRegionMap(Dictionary<string, List<ResvRegion>> resvRegionsDic, string strGridType)
        {
            Dictionary<string, MapOperation2> regionMop = new Dictionary<string, MapOperation2>();
            foreach (ResvRegion region in resvRegionsDic[strGridType])
            {
                if (!regionMop.ContainsKey(region.RegionName))
                {
                    MapOperation2 mapOp = new MapOperation2();
                    mapOp.FillPolygon(region.Shape);
                    regionMop.Add(region.RegionName, mapOp);
                }
            }
            mutRegionMopDic.Add(strGridType, regionMop);
        }

        /// <summary>
        /// 定位所在网格(点)
        /// </summary>
        public void isContainPoint(double x, double y, ref string gridTypeGrid)
        {
            foreach (string gridType in mutRegionMopDic.Keys)
            {
                foreach (string grid in mutRegionMopDic[gridType].Keys)
                {
                    if (mutRegionMopDic[gridType][grid].CheckPointInRegion(x, y))
                    {
                        //gridTypeGrid = gridType + "," + grid;
                        gridTypeGrid = grid;
                        break;
                    }
                }
            }
        }

        #endregion

        #region 站点资源计算类
        private class RegionRange
        {
            public double MinLong { get; set; }
            public double MaxLong { get; set; }
            public double MinLat { get; set; }
            public double MaxLat { get; set; }
        }

        /// <summary>
        /// 获取某采样点与主服小区间基站数
        /// </summary>
        public int CalcCellNum(double flongitude, double flatitude, Cell cell, TDCell tdCell, LTECell lteCell, double maxDistance)
        {
            List<BTS> distList = new List<BTS>();
            List<TDNodeB> tdDistList = new List<TDNodeB>();
            List<LTEBTS> lteDistList = new List<LTEBTS>();
       
            float fi = (float)((maxDistance + 500) * 100 / 10000000);//加上500米冗余距离
            RegionRange range = new RegionRange();
            range.MinLong = flongitude - fi;
            range.MaxLong = flongitude + fi;
            range.MinLat = flatitude - fi;
            range.MaxLat = flatitude + fi;
            if (cell != null)
            {
                return getGsmNum(flongitude, flatitude, cell, maxDistance, distList, range);
            }
            else if (tdCell != null)
            {
                return getTdNum(flongitude, flatitude, tdCell, maxDistance, tdDistList, range);
            }
            else if (lteCell != null)
            {
                return getLteNum(flongitude, flatitude, lteCell, maxDistance, lteDistList, range);
            }
            else
            {
                return 0;
            }
        }

        private int getGsmNum(double flongitude, double flatitude, Cell cell, double maxDistance, List<BTS> distList, RegionRange range)
        {
            string strBTSName = "";
            if (btsList.Count == 0)
                btsList = CellManager.GetInstance().GetCurrentBTSs();
            strBTSName = cell.BelongBTS.Name;
            foreach (BTS bts in btsList)
            {
                if (bts.Type == BTSType.Indoor || bts.Name == strBTSName
                    || bts.Cells.Count == 0)
                    continue;
                if ((bts.Longitude >= range.MinLong) && (bts.Longitude <= range.MaxLong) && (bts.Latitude <= range.MaxLat) && (bts.Latitude >= range.MinLat))
                {
                    double tmpDist = MathFuncs.GetDistance(bts.Longitude, bts.Latitude, flongitude, flatitude);
                    if (tmpDist <= maxDistance)
                        distList.Add(bts);
                }
            }
            return distList.Count;
        }

        private int getTdNum(double flongitude, double flatitude, TDCell tdCell, double maxDistance, List<TDNodeB> tdDistList, RegionRange range)
        {
            string strBTSName = "";
            if (nodebList.Count == 0)
                nodebList = CellManager.GetInstance().GetCurrentTDBTSs();
            strBTSName = tdCell.BelongBTS.Name;
            foreach (TDNodeB nodeb in nodebList)
            {
                if (nodeb.Type == TDNodeBType.Indoor || nodeb.Name == strBTSName
                    || nodeb.Cells.Count == 0)
                    continue;
                if ((nodeb.Longitude >= range.MinLong) && (nodeb.Longitude <= range.MaxLong) && (nodeb.Latitude <= range.MaxLat) && (nodeb.Latitude >= range.MinLat))
                {
                    double tmpDist = MathFuncs.GetDistance(nodeb.Longitude, nodeb.Latitude, flongitude, flatitude);
                    if (tmpDist <= maxDistance)
                        tdDistList.Add(nodeb);
                }
            }
            return tdDistList.Count;
        }

        private int getLteNum(double flongitude, double flatitude, LTECell lteCell, double maxDistance, List<LTEBTS> lteDistList, RegionRange range)
        {
            string strBTSName = "";
            if (lteBTSList.Count == 0)
                lteBTSList = CellManager.GetInstance().GetCurrentLTEBTSs();
            strBTSName = lteCell.BelongBTS.Name;
            foreach (LTEBTS ltebts in lteBTSList)
            {
                if (ltebts.Type == LTEBTSType.Indoor || ltebts.Name == strBTSName
                    || ltebts.Cells.Count == 0)
                    continue;
                if ((ltebts.Longitude >= range.MinLong) && (ltebts.Longitude <= range.MaxLong) && (ltebts.Latitude <= range.MaxLat) && (ltebts.Latitude >= range.MinLat))
                {
                    double tmpDist = MathFuncs.GetDistance(ltebts.Longitude, ltebts.Latitude, flongitude, flatitude);
                    if (tmpDist <= maxDistance)
                        lteDistList.Add(ltebts);
                }
            }
            return lteDistList.Count;
        }

        /// <summary>
        /// 获取某采样点与主服小区间基站列表
        /// </summary>
        public List<BtsSubInfo> GetBtsListBySample(double flongitude, double flatitude, string strNet)
        {
            List<BtsSubInfo> btsInfoList = new List<BtsSubInfo>();
            float fi = 0.05F;//加上5000米冗余距离

            if (strNet == "2G")
            {
                getGsmBts(flongitude, flatitude, btsInfoList, fi, "", new judgeFunc(defaultJudge));
            }
            else if (strNet == "3G")
            {
                getTdBts(flongitude, flatitude, btsInfoList, fi, "", new judgeFunc(defaultJudge));
            }
            else if (strNet == "4G")
            {
                getLteBts(flongitude, flatitude, btsInfoList, fi, "", new judgeFunc(defaultJudge));
            }

            return btsInfoList;
        }

        private delegate bool judgeFunc(string cellBelongBtsName, string btsName);

        private bool defaultJudge(string cellBelongBtsName, string btsName)
        {
            return false;
        }

        private void getGsmBts(double flongitude, double flatitude, List<BtsSubInfo> btsInfoList, float fi, string cellBelongBtsName, judgeFunc judgeName)
        {
            if (btsList.Count == 0)
                btsList = CellManager.GetInstance().GetCurrentBTSs();

            foreach (BTS bts in btsList)
            {
                if (bts.Type == BTSType.Indoor || bts.Cells.Count == 0 || judgeName(cellBelongBtsName, bts.Name))
                    continue;

                if ((bts.Longitude >= flongitude - fi) && (bts.Longitude <= flongitude + fi)
                 && (bts.Latitude >= flatitude - fi) && (bts.Latitude <= flatitude + fi))
                {
                    double tmpDist = MathFuncs.GetDistance(bts.Longitude, bts.Latitude, flongitude, flatitude);
                    if (tmpDist < 5000)
                    {
                        BtsSubInfo bsInfo = new BtsSubInfo(bts.Name, (float)bts.Longitude, (float)bts.Latitude, (float)tmpDist);
                        btsInfoList.Add(bsInfo);
                    }
                }
            }
        }

        private void getTdBts(double flongitude, double flatitude, List<BtsSubInfo> btsInfoList, float fi, string cellBelongBtsName, judgeFunc judgeName)
        {
            if (nodebList.Count == 0)
                nodebList = CellManager.GetInstance().GetCurrentTDBTSs();

            foreach (TDNodeB nodeb in nodebList)
            {
                if (nodeb.Type == TDNodeBType.Indoor || nodeb.Cells.Count == 0 || judgeName(cellBelongBtsName, nodeb.Name))
                    continue;

                if ((nodeb.Longitude >= flongitude - fi) && (nodeb.Longitude <= flongitude + fi)
                 && (nodeb.Latitude >= flatitude - fi) && (nodeb.Latitude <= flatitude + fi))
                {
                    double tmpDist = MathFuncs.GetDistance(nodeb.Longitude, nodeb.Latitude, flongitude, flatitude);
                    if (tmpDist < 5000)
                    {
                        BtsSubInfo bsInfo = new BtsSubInfo(nodeb.Name, (float)nodeb.Longitude, (float)nodeb.Latitude, (float)tmpDist);
                        btsInfoList.Add(bsInfo);
                    }
                }
            }
        }

        private void getLteBts(double flongitude, double flatitude, List<BtsSubInfo> btsInfoList, float fi, string cellBelongBtsName, judgeFunc judgeName)
        {
            if (lteBTSList.Count == 0)
                lteBTSList = CellManager.GetInstance().GetCurrentLTEBTSs();

            foreach (LTEBTS ltebts in lteBTSList)
            {
                if (ltebts.Type == LTEBTSType.Indoor || ltebts.Cells.Count == 0 || judgeName(cellBelongBtsName, ltebts.Name))
                    continue;

                if ((ltebts.Longitude >= flongitude - fi) && (ltebts.Longitude <= flongitude + fi)
                 && (ltebts.Latitude >= flatitude - fi && (ltebts.Latitude <= flatitude + fi)))
                {
                    double tmpDist = MathFuncs.GetDistance(ltebts.Longitude, ltebts.Latitude, flongitude, flatitude);
                    if (tmpDist < 5000)
                    {
                        BtsSubInfo bsInfo = new BtsSubInfo(ltebts.Name, (float)ltebts.Longitude, (float)ltebts.Latitude, (float)tmpDist);
                        btsInfoList.Add(bsInfo);
                    }
                }
            }
        }

        /// <summary>
        /// 获取某采样点与主服小区间基站数
        /// </summary>
        public int CalcCellNum(List<BtsSubInfo> bsList, double maxDistance)
        {
            int iBtsNum = 0;
            foreach (BtsSubInfo bsInfo in bsList)
            {
                if (bsInfo.FDistance <= (int)maxDistance + 1)
                    iBtsNum++;
                else
                    return iBtsNum;
            }
            return iBtsNum;
        }

        /// <summary>
        /// 获得某小区的理想覆盖半径
        /// </summary>
        public double CalcRadius(Cell cell, TDCell tdCell, LTECell lteCell, int nearestCellCount)
        {
            List<double> distList = new List<double>();
            if (cell != null)
            {
                getGsmDistList(cell, distList);
            }
            else if (tdCell != null)
            {
                getTdDistList(tdCell, distList);
            }
            else if (lteCell != null)
            {
                getLteDistList(lteCell, distList);
            }

            double meanDistance = calcMeanDistance(nearestCellCount, distList);
            return meanDistance;
        }

        private void getGsmDistList(Cell cell, List<double> distList)
        {
            if (btsList.Count == 0)
                btsList = CellManager.GetInstance().GetCurrentBTSs();
            string strBTSName = cell.BelongBTS.Name;
            Antenna antenna = cell.Antennas[0];
            double minLong = antenna.Longitude - 0.02;
            double maxLong = antenna.Longitude + 0.02;
            double minLat = antenna.Latitude - 0.02;
            double maxLat = antenna.Latitude + 0.02;

            foreach (BTS bts in btsList)
            {
                if (bts.Type == BTSType.Indoor || bts.Name == strBTSName
                    || bts.Cells.Count == 0)
                    continue;
                if ((bts.Longitude >= minLong) && (bts.Longitude <= maxLong) && (bts.Latitude <= maxLat) && (bts.Latitude >= minLat))
                {
                    double distance = MathFuncs.GetDistance(antenna.Longitude, antenna.Latitude, bts.Longitude, bts.Latitude);
                    if ((distance < CD.MAX_COV_DISTANCE_GSM) && (distance > 20))
                        distList.Add(distance);
                }
            }
        }

        private void getTdDistList(TDCell tdCell, List<double> distList)
        {
            if (nodebList.Count == 0)
                nodebList = CellManager.GetInstance().GetCurrentTDBTSs();
            string strBTSName = tdCell.BelongBTS.Name;
            TDAntenna antenna = tdCell.Antenna;
            double minLong = antenna.Longitude - 0.02;
            double maxLong = antenna.Longitude + 0.02;
            double minLat = antenna.Latitude - 0.02;
            double maxLat = antenna.Latitude + 0.02;
            foreach (TDNodeB nodeb in nodebList)
            {
                if (nodeb.Type == TDNodeBType.Indoor || nodeb.Name == strBTSName
                    || nodeb.Cells.Count == 0)
                    continue;
                if ((nodeb.Longitude >= minLong) && (nodeb.Longitude <= maxLong) && (nodeb.Latitude <= maxLat) && (nodeb.Latitude >= minLat))
                {
                    double distance = MathFuncs.GetDistance(antenna.Longitude, antenna.Latitude, nodeb.Longitude, nodeb.Latitude);
                    if ((distance < CD.MAX_COV_DISTANCE_TD) && (distance > 20))
                        distList.Add(distance);
                }
            }
        }

        private void getLteDistList(LTECell lteCell, List<double> distList)
        {
            if (lteBTSList.Count == 0)
                lteBTSList = CellManager.GetInstance().GetCurrentLTEBTSs();
            string strBTSName = lteCell.BelongBTS.Name;
            double minLong = lteCell.Antennas[0].Longitude - 0.02;
            double maxLong = lteCell.Antennas[0].Longitude + 0.02;
            double minLat = lteCell.Antennas[0].Latitude - 0.02;
            double maxLat = lteCell.Antennas[0].Latitude + 0.02;
            foreach (LTEBTS ltebts in lteBTSList)
            {
                if (ltebts.Type == LTEBTSType.Indoor || ltebts.Name == strBTSName
                    || ltebts.Cells.Count == 0)
                    continue;
                if ((ltebts.Longitude >= minLong) && (ltebts.Longitude <= maxLong) && (ltebts.Latitude <= maxLat) && (ltebts.Latitude >= minLat))
                {
                    double distance = MathFuncs.GetDistance(lteCell.Antennas[0].Longitude, lteCell.Antennas[0].Latitude, ltebts.Longitude, ltebts.Latitude);
                    if ((distance < CD.MAX_COV_DISTANCE_LTE) && (distance > 20))
                        distList.Add(distance);
                }
            }
        }

        private static double calcMeanDistance(int nearestCellCount, List<double> distList)
        {
            double meanDistance = 0;
            if (distList.Count > 0)
            {
                distList.Sort();
                int counter = 0;
                for (int i = 0; i < distList.Count; i++)
                {
                    counter++;
                    meanDistance += distList[i];

                    if (counter >= nearestCellCount)
                    {
                        break;
                    }
                }
                meanDistance = meanDistance / counter;
            }
            else
            {
                meanDistance = 0;
            }

            return meanDistance;
        }

        /// <summary>
        /// 获取每小区5公里以内的站点
        /// </summary>
        public List<BtsSubInfo> GetBtsList(Cell cell, TDCell tdCell, LTECell lteCell)
        {
            List<BtsSubInfo> btsInfoList = new List<BtsSubInfo>();
            float fi = 0.05F;//加上5000米冗余距离

            if (cell != null)
            {
                Antenna ant = cell.Antennas[0];
                getGsmBts(ant.Longitude, ant.Latitude, btsInfoList, fi, cell.BelongBTS.Name, new judgeFunc(judgeName));
            }
            else if (tdCell != null)
            {
                TDAntenna tdAnt = tdCell.Antenna;
                getTdBts(tdAnt.Longitude, tdAnt.Latitude, btsInfoList, fi, tdCell.BelongBTS.Name, new judgeFunc(judgeName));
            }
            else if (lteCell != null && lteCell.Antennas.Count > 0)
            {
                getLteBts(lteCell.Antennas[0].Longitude, lteCell.Antennas[0].Latitude, btsInfoList, fi, lteCell.BelongBTS.Name, new judgeFunc(judgeName));
            }
            return btsInfoList;
        }

        private bool judgeName(string cellBelongBtsName, string btsName)
        {
            return cellBelongBtsName == btsName;
        }

        /// <summary>
        /// 获得某小区的理想覆盖半径
        /// </summary>
        public double CalcRadius(List<BtsSubInfo> bsList)
        {
            double meanDistance = 0;
            if (bsList.Count > 0)
            {
                int counter = 0;
                for (int i = 0; i < bsList.Count; i++)
                {
                    if (bsList[i].FDistance > 20)//剔除共站等超近站
                    {
                        counter++;
                        meanDistance += bsList[i].FDistance;
                    }

                    if (counter >= 3)
                    {
                        break;
                    }
                }
                meanDistance = meanDistance / counter;
            }
            else
            {
                meanDistance = 0;
            }
            return meanDistance;
        }


        #endregion

        public class SimulationPoints
        {
            public double cellLongitude { get; set; }
            public double cellLatitude { get; set; }
            public List<LongLat> longLatList { get; set; } = new List<LongLat>();
            public string strNet { get; set; } = "LTE";

            public List<LongLat> longLatTestList { get; set; } = new List<LongLat>();//测试波形重建
            public List<LongLat> longLatModelList { get; set; } = new List<LongLat>();//理想覆盖模拟
            public List<LongLat> longLatMRList { get; set; } = new List<LongLat>();//MR覆盖模拟
        }

        /// <summary>
        /// 获取网络体检中扫频与路测项目
        /// </summary>
        public void GetDt_ScanProject(AntTimeCfg timeCfg,string strNet)
        {
            DiyAntProjectCfg diyAntProjectCfg = new DiyAntProjectCfg(MainModel);
            diyAntProjectCfg.SetCondition(timeCfg.ISitme, timeCfg.IEitme, strNet);
            diyAntProjectCfg.Query();
            strProject = diyAntProjectCfg.StrProjectCfg;
        }
    }

    //基站信息概要
    public class BtsSubInfo : IComparable
    {
        public string StrBtsName { get; set; }
        public float FLongitude { get; set; }
        public float FLatitude { get; set; }
        public float FDistance { get; set; }

        public BtsSubInfo()
        {
            StrBtsName = "";
            FLongitude = 0;
            FLatitude = 0;
            FDistance = 0;
        }

        public BtsSubInfo(string tmpBtsName,float tmpLongitude,float tmpLatitude,float tmpDistance)
        {
            StrBtsName = tmpBtsName;
            FLongitude = tmpLongitude;
            FLatitude = tmpLatitude;
            FDistance = tmpDistance;
        }

        public int CompareTo(object obj)
        {
            int res = 0;
            try
            {
                BtsSubInfo sObj = (BtsSubInfo)obj;
                if (this.FDistance >= sObj.FDistance)
                {
                    res = 1;
                }
                else if (this.FDistance < sObj.FDistance)
                {
                    res = -1;
                }
            }
            catch (Exception ex)
            {
                throw (new Exception("比较异常", ex.InnerException));
            }
            return res;
        }
    }

    public class AntLegend
    {
        public Color colorType { get; set; }
        public string strLegend { get; set; }

        public AntLegend()
        {
            colorType = new Color();
            strLegend = "";
        }
    }
}
