using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.ZTFunc;
using MasterCom.RAMS.Chris.Util;

namespace MasterCom.RAMS.Func
{
    public partial class PRBSettingDlg : BaseForm
    {
        PrbCondition prbCondition;
        public PRBSettingDlg(PrbCondition condition)
        {
            prbCondition = condition;
            InitializeComponent();
            rangeSetSetting1.RangeAll = new Range(0, true, 100, true);
            if (prbCondition == null)
            {
                prbCondition = new PrbCondition();
                prbCondition.PrbRangeValues = getAutoRangeSet();
            }
            rangeSetSetting1.RangeSet = prbCondition.PrbRangeValues;
            rangeSetSetting1.AutoRangeSet = getAutoRangeSet();
        }

        public PrbCondition GetCondition()
        {
            prbCondition = new PrbCondition();
            prbCondition.PrbRangeValues = rangeSetSetting1.RangeSet;

            return prbCondition;
        }

        private RangeSet getAutoRangeSet()
        {
            RangeSet set = new RangeSet();
            for (int i = 0; i < 10; i++)
            {
                if (i == 9)
                {
                    set.Add(new Range(90, true, 100, true));
                }
                else
                {
                    set.Add(new Range(i * 10, true, i * 10 + 10, false));
                }
            }
            return set;
        }

        private void buttonCancel_Click(object sender, EventArgs e)
        {
            this.Dispose();
        }
    }


}