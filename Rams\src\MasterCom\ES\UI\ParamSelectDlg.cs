﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.ES.Data;
using MasterCom.Util;

namespace MasterCom.ES.UI
{
    public partial class ParamSelectDlg : BaseFormStyle
    {
        public ParamSelectDlg()
        {
            InitializeComponent();
        }
        public void FillParams(Dictionary<string, DTParameter> paraDic)
        {
            this.lbxParams.Items.Clear();
            foreach (DTParameter s in paraDic.Values)
            {
                this.lbxParams.Items.Add(s);
            }
        }
        public DTParameter GetSelectedParam()
        {
            return this.lbxParams.SelectedItem as DTParameter;
        }
        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        private void lbxParams_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            if(lbxParams.SelectedItem !=null)
            {
                this.DialogResult = DialogResult.OK;
            }
        }
    }
}