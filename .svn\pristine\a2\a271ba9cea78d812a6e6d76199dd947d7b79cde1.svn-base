﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Net;
using System.Windows.Forms;
using MasterCom.RAMS.Model;

using MasterCom.Util;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.ZTFunc;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTDiyNoCellCoverQueryByRegion_LTE : ZTDiyNoCellCoverQueryByRegion_TD
    {
        private static ZTDiyNoCellCoverQueryByRegion_LTE intance = null;
        public new static ZTDiyNoCellCoverQueryByRegion_LTE GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new ZTDiyNoCellCoverQueryByRegion_LTE();
                    }
                }
            }
            return intance;
        }

        protected ZTDiyNoCellCoverQueryByRegion_LTE()
            : base()
        {
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_SCAN_TOPN);
        }

        public override string Name
        {
            get { return "无归属信号分析_LTE扫频"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 23000, 23008, this.Name);
        }

        protected override int validSignal(TestPoint testPoint, int index, ref int? uarfcn, ref short? cpi)
        {
            float? rxLev = (float?)testPoint["LTESCAN_TopN_CELL_Specific_RSRP", index];
            uarfcn = (int?)testPoint["LTESCAN_TopN_EARFCN", index];
            int? cpiRef = (int?)(short?)testPoint["LTESCAN_TopN_PCI", index];
            if (rxLev == null || uarfcn == null || cpiRef == null || rxLev < rxLevThreshold)
            {
                return -1;
            }
            LTECell cell = testPoint.GetCell_LTEScan(index);
            if (cell != null)
            {
                return 0;
            }
            cpi = (short?)cpiRef;
            return 1;
        }

        protected override void getResultsAfterQuery()
        {
            for (int i = 0; i < noCellCoverList.Count; i++)
            {
                noCellCoverList[i].GetResult();
            }
            MainModel.FireSetDefaultMapSerialTheme("LTE_SCAN", "TopN_CELL_Specific_RSRP");
        }

        #region Background
        public override BackgroundFuncType FuncType
        {
            get { return BackgroundFuncType.LTE扫频专题; }
        }
        #endregion
    }
}
