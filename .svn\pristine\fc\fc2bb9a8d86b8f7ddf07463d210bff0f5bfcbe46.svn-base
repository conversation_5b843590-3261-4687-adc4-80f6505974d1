using System;
using System.Collections.Generic;
using System.Text;
using GeneGraph;
using System.Drawing.Drawing2D;
using System.Drawing;
using MasterCom.MTGis;

namespace MasterCom.RAMS.Func.AssistLayer
{
    [Serializable()]
    public class BaseElementEx:BaseElement
    {
        public BaseElementEx()
        {
        }
        
        public DbPoint LocationM { get; set; }
        
        public IGisAdapter GisAdapter { get; set; }

        public new PointF Location
        {
            get
            {
                PointF pf;
                GisAdapter.ToDisplay(LocationM, out pf);
                return pf;
            }
        }

        public override GraphicsPath GraphicsPath
        {
            get
            {
                if (graphicsPath == null)
                {
                    graphicsPath = new GraphicsPath();
                    graphicsPath.AddLines(new PointF[] { this.Location });
                    return graphicsPath;
                }
                PointF pf = Location;
                Matrix ma = new Matrix();
                ma.Translate(pf.X, pf.Y);
                if (isScale)
                    ma.Scale((float)(10000 / GisAdapter.GetScale()), (float)(10000 / GisAdapter.GetScale()));
                GraphicsPath gp = this.graphicsPath.Clone() as GraphicsPath;
                gp.Transform(ma);
                return gp;
            }
            set
            {
                this.graphicsPath = value;
            }
        }

        public override void Draw(Graphics g)
        {
            IsInvalidated = false;

            GraphicsPath gp = GraphicsPath;
            RectangleF r = GetUnsignedRectangle();
            if(r.X<-100 || r.X>6000 || r.Y<-100 || r.Y>6000)
            {
                return;
            }
            Brush b = GetBrush(r);
            g.FillPath(b, gp);
            Pen p = GetPen();
            g.DrawPath(p, gp);

            p.Dispose();
            b.Dispose();
        }

    }
}
