﻿namespace MasterCom.RAMS.Func
{
    partial class CellFusionKpiEditSettingBox
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.btnOK = new DevExpress.XtraEditors.SimpleButton();
            this.btnCancel = new DevExpress.XtraEditors.SimpleButton();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.textKpiName = new DevExpress.XtraEditors.TextEdit();
            this.textKpiKey = new DevExpress.XtraEditors.TextEdit();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.chkIsShow = new DevExpress.XtraEditors.CheckEdit();
            ((System.ComponentModel.ISupportInitialize)(this.textKpiName.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textKpiKey.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkIsShow.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // btnOK
            // 
            this.btnOK.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnOK.Location = new System.Drawing.Point(145, 118);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(60, 23);
            this.btnOK.TabIndex = 0;
            this.btnOK.Text = "确定";
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // btnCancel
            // 
            this.btnCancel.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnCancel.Location = new System.Drawing.Point(225, 118);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(60, 23);
            this.btnCancel.TabIndex = 1;
            this.btnCancel.Text = "取消";
            // 
            // labelControl1
            // 
            this.labelControl1.Location = new System.Drawing.Point(22, 22);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(60, 14);
            this.labelControl1.TabIndex = 2;
            this.labelControl1.Text = "指标名称：";
            // 
            // labelControl2
            // 
            this.labelControl2.Location = new System.Drawing.Point(22, 57);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(60, 14);
            this.labelControl2.TabIndex = 3;
            this.labelControl2.Text = "指标键值：";
            // 
            // textKpiName
            // 
            this.textKpiName.Location = new System.Drawing.Point(88, 19);
            this.textKpiName.Name = "textKpiName";
            this.textKpiName.Size = new System.Drawing.Size(197, 21);
            this.textKpiName.TabIndex = 4;
            // 
            // textKpiKey
            // 
            this.textKpiKey.Location = new System.Drawing.Point(88, 50);
            this.textKpiKey.Name = "textKpiKey";
            this.textKpiKey.Size = new System.Drawing.Size(197, 21);
            this.textKpiKey.TabIndex = 5;
            // 
            // labelControl3
            // 
            this.labelControl3.Location = new System.Drawing.Point(22, 94);
            this.labelControl3.Name = "labelControl3";
            this.labelControl3.Size = new System.Drawing.Size(60, 14);
            this.labelControl3.TabIndex = 6;
            this.labelControl3.Text = "是否显示：";
            // 
            // chkIsShow
            // 
            this.chkIsShow.Location = new System.Drawing.Point(86, 91);
            this.chkIsShow.Name = "chkIsShow";
            this.chkIsShow.Properties.Caption = "显示";
            this.chkIsShow.Size = new System.Drawing.Size(61, 19);
            this.chkIsShow.TabIndex = 7;
            // 
            // CellFusionKpiEditSettingBox
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(302, 153);
            this.Controls.Add(this.chkIsShow);
            this.Controls.Add(this.labelControl3);
            this.Controls.Add(this.textKpiKey);
            this.Controls.Add(this.textKpiName);
            this.Controls.Add(this.labelControl2);
            this.Controls.Add(this.labelControl1);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.Name = "CellFusionKpiEditSettingBox";
            this.Text = "增加关联指标设置";
            ((System.ComponentModel.ISupportInitialize)(this.textKpiName.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textKpiKey.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkIsShow.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraEditors.SimpleButton btnOK;
        private DevExpress.XtraEditors.SimpleButton btnCancel;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.TextEdit textKpiName;
        private DevExpress.XtraEditors.TextEdit textKpiKey;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.CheckEdit chkIsShow;
    }
}