﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;

namespace MasterCom.RAMS.Net
{
    public class GridCompareLongLat
    {
        public int LtLongitude { get; set; }

        public double DLtLongitude
        {
            get
            {
                return LtLongitude / 10000000.0;
            }
        }

        public int LtLatitude { get; set; }

        public double DLtLatitude
        {
            get
            {
                return LtLatitude / 10000000.0;
            }
        }

        public int BrLongitude { get; set; }

        public double DBrLongitude
        {
            get
            {
                return BrLongitude / 10000000.0;
            }
        }

        public int BrLatitude { get; set; }

        public double DBrLatitude
        {
            get
            {
                return BrLatitude / 10000000.0;
            }
        }

        public GridCompareLongLat()
        {

        }

        public GridCompareLongLat(int iLongitude, int iLatitude, int iRadius)
        {
            int x = iRadius * 100;
            int y = iRadius * 90;

            this.LtLongitude = iLongitude / x * x;
            this.LtLatitude = iLatitude / y * y + y;
            this.BrLongitude = iLongitude / x * x + x;
            this.BrLatitude = iLatitude / y * y;
        }

        public override bool Equals(object obj)
        {
            GridCompareLongLat other = obj as GridCompareLongLat;
            if (other == null)
                return false;

            if (!base.GetType().Equals(obj.GetType()))
                return false;

            return (this.LtLongitude.Equals(other.LtLongitude) &&
                    this.LtLatitude.Equals(other.LtLatitude) &&
                    this.BrLongitude.Equals(other.BrLongitude) &&
                    this.BrLatitude.Equals(other.BrLatitude));
        }

        public override int GetHashCode()
        {
            return this.LtLongitude.GetHashCode();
        }
    }

    public class GridCellKey
    {
        public string StrGrid { get; set; }
        public int IECI { get; set; }

        public override bool Equals(object obj)
        {
            GridCellKey other = obj as GridCellKey;
            if (other == null)
                return false;

            if (!base.GetType().Equals(obj.GetType()))
                return false;

            return (this.StrGrid.Equals(other.StrGrid) &&
                    this.IECI.Equals(other.IECI));
        }

        public override int GetHashCode()
        {
            return this.IECI.GetHashCode();
        }

        public GridCellKey(string strGrid, int iECI)
        {
            this.StrGrid = strGrid;
            this.IECI = iECI;
        }
    }

    public class TimeGridCellKey
    {
        public int IECI { get; set; }

        public string StrTestDate
        {
            get
            {
                if (this.TimePeriodSpan != null)
                {
                    return TimePeriodSpan.BeginTime.ToString("yyyy-MM-dd");
                }
                else
                {
                    return "";
                }
            }
        }

        public GridTimePeriod TimePeriodSpan { get; set; }

        public override bool Equals(object obj)
        {
            TimeGridCellKey other = obj as TimeGridCellKey;
            if (other == null)
                return false;

            if (!base.GetType().Equals(obj.GetType()))
                return false;

            return (this.IECI.Equals(other.IECI) && this.StrTestDate.Equals(other.StrTestDate)
                    && this.TimePeriodSpan.Equals(other.TimePeriodSpan));
        }

        public override int GetHashCode()
        {
            return this.TimePeriodSpan.GetHashCode() + this.IECI.GetHashCode();
        }

        public TimeGridCellKey(GridTimePeriod timePeriodSpan, int iECI)
        {
            this.TimePeriodSpan = timePeriodSpan;
            this.IECI = iECI;
        }
    }

    public class GridTimePeriod
    {
        private DateTime beginTime;

        private DateTime endTime;

        public GridTimePeriod()
        {
            beginTime = DateTime.MinValue;
            endTime = DateTime.MaxValue;
        }

        public GridTimePeriod(DateTime beginTime, DateTime endTime)
        {
            SetPeriod(beginTime, endTime);
        }

        public bool SetPeriod(DateTime beginTime, DateTime endTime)
        {
            if (beginTime <= endTime)
            {
                this.beginTime = beginTime;
                this.endTime = endTime;
                return true;
            }
            return false;
        }

        public DateTime BeginTime
        {
            get { return beginTime; }
        }

        public DateTime EndTime
        {
            get { return endTime; }
        }

        public bool Contains(DateTime time)
        {
            if (this.beginTime > time || this.endTime <= time)
            {
                return false;
            }
            return true;
        }

        public override bool Equals(object obj)
        {
            GridTimePeriod other = obj as GridTimePeriod;
            if (other == null)
                return false;

            if (!base.GetType().Equals(obj.GetType()))
                return false;

            return (this.beginTime.Equals(other.beginTime) &&
                    this.endTime.Equals(other.endTime));
        }

        public override int GetHashCode()
        {
            return this.BeginTime.GetHashCode();
        }
    }

    public class TestPointExtend
    {
        public DateTime DTime { get; set; } = DateTime.MinValue;
        public long LTimeMill { get; set; } = 0;
        public double DLng { get; set; } = 0;
        public double DLat { get; set; } = 0;
        public string StrGrid { get; set; } = "";
        public double Distance { get; set; } = 0;
        public string StrFileName { get; set; } = "";

        public TestPointExtend()
        {

        }

        public TestPointExtend(TestPoint tp, string strGrid, double dis)
        {
            this.DTime = tp.DateTime;
            this.DLng = tp.Longitude;
            this.DLat = tp.Latitude;
            this.StrFileName = tp.FileName;
            this.StrGrid = strGrid;
            this.Distance = dis;
        }

        public static IComparer<TestPointExtend> GetCompareByTime()
        {
            if (comparerByTime == null)
            {
                comparerByTime = new ComparerByTime();
            }
            return comparerByTime;
        }

        private static IComparer<TestPointExtend> comparerByTime;

        public class ComparerByTime : IComparer<TestPointExtend>
        {
            public int Compare(TestPointExtend x, TestPointExtend y)
            {
                return x.DTime.CompareTo(y.DTime);
            }
        }
    }

    public class TimeSpandGridCellInfo
    {
        public string StrCityName { get; set; }
        public int ISN { get; set; }
        public string StrGridName { get; set; } = "";
        public string StrCellName { get; set; } = "";
        public int IECI { get; set; }
        public double DDistance { get; set; }

        public string StrTestDate
        {
            get
            {
                if (this.ITestPointNum > 0)
                {
                    return this.DStartTime.ToString("yyyy-MM-dd");
                }
                else
                {
                    return "";
                }
            }
        }

        public string StrTestFileName { get; set; } = "";
        public GridTimePeriod TimePeriodSpan { get; set; }

        public string strTimeSpanDes
        {
            get
            {
                if (this.ITestPointNum > 0)
                {
                    return this.DStartTime.ToString("HH:mm:ss") + "-" + this.DEndeTime.ToString("HH:mm:ss");
                }
                else
                {
                    return "";
                }
            }
        }

        public int ITestPointNum { get; set; }
        public DateTime DStartTime { get; set; } = DateTime.MaxValue;
        public DateTime DEndeTime { get; set; } = DateTime.MinValue;
        public DateTime DTimeIn { get; set; } = DateTime.MaxValue;
        public DateTime DTimeOut { get; set; } = DateTime.MinValue;
        public string StrTestGridLngLat { get; set; } = "";
        public string StrTestTakeUpTime { get; set; } = "";

        private string getStrTestTakeUpTime(List<TestPointExtend> tpeList)
        {
            StringBuilder strTmp = new StringBuilder();
            int iCount = tpeList.Count;
            int iStartDex = -1;
            int iEndDex = -1;
            if (iCount == 1)
                strTmp.Append(tpeList[0].DTime.ToString("HH:mm:ss") + "-" + tpeList[0].DTime.AddSeconds(1).ToString("HH:mm:ss"));
            else if (iCount > 1)
            {
                addTpeTime(tpeList, strTmp, iCount, ref iStartDex, ref iEndDex);
            }
            return strTmp.ToString();
        }

        private static void addTpeTime(List<TestPointExtend> tpeList, StringBuilder strTmp, int iCount, ref int iStartDex, ref int iEndDex)
        {
            for (int i = 0; i < iCount - 1; i++)
            {
                if (iStartDex == -1)
                    iStartDex = i;
                if (iStartDex == iCount - 1)
                {
                    strTmp.Append(tpeList[iStartDex].DTime.AddSeconds(-1).ToString("HH:mm:ss") + "-" + tpeList[iStartDex].DTime.ToString("HH:mm:ss") + ",");
                    break;
                }
                int iDiffSecons = (int)(JavaDate.GetMilliseconds(tpeList[i + 1].DTime) / 1000) - (int)(JavaDate.GetMilliseconds(tpeList[i].DTime) / 1000);
                double dDiffDistance = MathFuncs.GetDistance(tpeList[i + 1].DLng, tpeList[i + 1].DLat, tpeList[i].DLng, tpeList[i].DLat);
                if (iDiffSecons > 5 || dDiffDistance > 20 || (i + 1) >= iCount - 1)
                {
                    iEndDex = i;
                    if (iStartDex == iEndDex)
                        iEndDex++;
                    strTmp.Append(tpeList[iStartDex].DTime.ToString("HH:mm:ss") + "-" + tpeList[iEndDex].DTime.ToString("HH:mm:ss") + ",");
                    iStartDex = -1;
                }
            }
        }

        public void AddTestPoint(List<TestPointExtend> tpeList, string strTestGrid)
        {
            this.StrTestGridLngLat = strTestGrid;
            tpeList.Sort(TestPointExtend.GetCompareByTime());
            for (int i = 0; i < tpeList.Count; i++)
            {
                if (tpeList[i].Distance <= 20)
                {
                    ITestPointNum++;
                    DDistance += tpeList[i].Distance;
                    if (!StrGridName.Contains(tpeList[i].StrGrid))
                    {
                        StringBuilder sb = new StringBuilder(StrGridName);
                        sb.Append(tpeList[i].StrGrid + ",");
                        StrGridName = sb.ToString();
                    }

                    if (!StrTestFileName.Contains(tpeList[i].StrFileName))
                    {
                        StringBuilder sb = new StringBuilder(StrTestFileName);
                        sb.Append(tpeList[i].StrFileName + ",");
                        StrTestFileName = sb.ToString();
                    }
                }
            }
            this.StrTestTakeUpTime += getStrTestTakeUpTime(tpeList);
        }

        public string StrAlarm { get; set; } = "否";
        public string StrAlarmDate { get; set; } = "否";
        public CellOutOfServiceAlarmInfo CellAlarmInfoNew { get; set; } = new CellOutOfServiceAlarmInfo();
    }

    public class GridCellDateInfo
    {
        public string StrCityName { get; set; }
        public int ISN { get; set; }
        public string StrGridName { get; set; } = "";
        public string StrCellName { get; set; } = "";
        public int IECI { get; set; }
        public string StrAlarm { get; set; } = "否";
        public string StrAlarmDate { get; set; } = "否";
    }

    public class GridCellTestPointInfo
    {
        public string StrCityName { get; set; }
        public int ISN { get; set; }
        public string StrGridName { get; set; }
        public string StrDataSource { get; set; }
        public string StrTakeUp { get; set; }
        public string StrCellName { get; set; } = "";
        public int IECI { get; set; }
        public double DDistance { get; set; }
        public string StrTestDate { get; set; } = "";

        public void AddTestPoint(double dis, string strDate)
        {
            if (dis <= 20)
            {
                this.DDistance += dis;
            }
            if (!this.StrTestDate.Contains(strDate))
            {
                this.StrTestDate += strDate + ",";
            }
        }
    }

    public class CellOutOfServiceAlarmInfo
    {
        public string StrDate { get; set; } = "";
        public string StrCity { get; set; } = "";
        public string StrCellName { get; set; } = "";
        public string StrCGI { get; set; } = "";
        public string StrBtsName { get; set; } = "";
        public string StrNetWork { get; set; } = "";
        public string StrManufacturer { get; set; } = "";
        public string StrCoverType { get; set; } = "";
        public string StrScene { get; set; } = "";
        public string StrProblemType { get; set; } = "";
        public DateTime DStartTime { get; set; } = DateTime.MinValue;
        public DateTime DEndTime { get; set; } = DateTime.MinValue;
        public string StrStartTime
        {
            get
            {
                if (DStartTime == DateTime.MinValue)
                {
                    return "";
                }
                return DStartTime.ToString("yyyy-MM-dd HH:mm:ss");
            }
        }
        public string StrEndTime
        {
            get
            {
                if (DEndTime == DateTime.MinValue)
                {
                    return "";
                }
                return DEndTime.ToString("yyyy-MM-dd HH:mm:ss");
            }
        }
    }

    public class DIYQueryCellAlarmInfo : DIYSQLBase
    {
        public DIYQueryCellAlarmInfo(MainModel mm)
            : base(mm)
        {
            mainModel = mm;
        }
        string strCityName = "";
        string strStime = "";
        string strEtime = "";

        public override string Name
        {
            get { return "查询GSM天线360角度信息"; }
        }
        protected override string getSqlTextString()
        {
            string sql = string.Format(" exec DTASYSTEM.dbo.sp_auto_告警_小区退服信息 '{0}','{1}','{2}'", this.strCityName, this.strStime, this.strEtime);
            return sql;
        }

        public void SetCondition(string strStime, string strEtime, string strCityName)
        {
            this.strStime = strStime;
            this.strEtime = strEtime;
            this.strCityName = strCityName;
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            int idx = 0;
            E_VType[] rType = new E_VType[12];
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx] = E_VType.E_String;
            return rType;
        }

        public Dictionary<int, Dictionary<string, List<CellOutOfServiceAlarmInfo>>> cellEciAlarmInfoDic { get; set; }
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            cellEciAlarmInfoDic = new Dictionary<int, Dictionary<string, List<CellOutOfServiceAlarmInfo>>>();
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    CellOutOfServiceAlarmInfo cellAlarmInfo = new CellOutOfServiceAlarmInfo();
                    cellAlarmInfo.StrDate = Convert.ToDateTime(package.Content.GetParamString()).ToString("yyyy-MM-dd");
                    cellAlarmInfo.StrCity = package.Content.GetParamString();
                    cellAlarmInfo.StrCellName = package.Content.GetParamString();
                    cellAlarmInfo.StrCGI = package.Content.GetParamString();
                    cellAlarmInfo.StrBtsName = package.Content.GetParamString();
                    cellAlarmInfo.StrNetWork = package.Content.GetParamString();
                    cellAlarmInfo.StrManufacturer = package.Content.GetParamString();
                    cellAlarmInfo.StrCoverType = package.Content.GetParamString();
                    cellAlarmInfo.StrScene = package.Content.GetParamString();
                    cellAlarmInfo.StrProblemType = package.Content.GetParamString();
                    cellAlarmInfo.DStartTime = Convert.ToDateTime(package.Content.GetParamString());
                    cellAlarmInfo.DEndTime = Convert.ToDateTime(package.Content.GetParamString());
                    int iECI = Convert.ToInt32(cellAlarmInfo.StrCGI.Split('-')[2]) * 256 + Convert.ToInt32(cellAlarmInfo.StrCGI.Split('-')[3]);
                    if (!cellEciAlarmInfoDic.ContainsKey(iECI))
                    {
                        Dictionary<string, List<CellOutOfServiceAlarmInfo>> dateCellAlarmInfoDic = new Dictionary<string, List<CellOutOfServiceAlarmInfo>>();
                        dateCellAlarmInfoDic[cellAlarmInfo.StrDate] = new List<CellOutOfServiceAlarmInfo>() { cellAlarmInfo };
                        cellEciAlarmInfoDic[iECI] = dateCellAlarmInfoDic;
                    }
                    else
                    {
                        if (!cellEciAlarmInfoDic[iECI].ContainsKey(cellAlarmInfo.StrDate))
                            cellEciAlarmInfoDic[iECI][cellAlarmInfo.StrDate] = new List<CellOutOfServiceAlarmInfo>() { cellAlarmInfo };
                        else
                            cellEciAlarmInfoDic[iECI][cellAlarmInfo.StrDate].Add(cellAlarmInfo);
                    }
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }
    }
}
