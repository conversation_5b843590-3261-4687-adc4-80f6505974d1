﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Stat;
using MasterCom.Util;
using MasterCom.Util.UiEx;
using System.Data;
using DevExpress.XtraEditors;
using MasterCom.RAMS.Model.Interface;

namespace MasterCom.RAMS.ZTFunc
{
    public class CoverRateAnaByTunnel : DIYAnalyseByFileBackgroundBase
    {
        Dictionary<string, List<TunnelBaseInfo>> baseInfoDic = new Dictionary<string, List<TunnelBaseInfo>>();
        TunnelInfo curTunnelInfo = new TunnelInfo();
        List<TunnelInfo> resultList = new List<TunnelInfo>();
        TunnelCondition coverCond = new TunnelCondition();
        DIYReplayContentOption replayContent = null;

        protected delegate void doWithTestPointDelegate(TestPoint tp);

        public CoverRateAnaByTunnel()
            : base(MainModel.GetInstance())
        {
        }
        public override MainModel.NeedSearchType needSearchType()
        {
            return MainModel.NeedSearchType.None;
        }
        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }
        public override string Name
        {
            get { return "隧道LTE/GSM覆盖率统计"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 11000, 11057, this.Name);
        }
        protected bool getTunnelInfoFromExcle(string fileName)
        {
            try
            {
                baseInfoDic = new Dictionary<string, List<TunnelBaseInfo>>();
                DataSet dataSet = ExcelNPOIManager.ImportFromExcel(fileName);
                if (dataSet == null || dataSet.Tables.Count <= 0)
                {
                    return false;
                }
                DataTable tb = dataSet.Tables[0];
                int index = 1;
                StringBuilder strbWrongTimeIndex = new StringBuilder();
                foreach (DataRow row in tb.Rows)
                {
                    index++;
                    TunnelBaseInfo info = new TunnelBaseInfo();
                    info.RoadName = row["高速名称"].ToString();
                    info.Direction = row["方向"].ToString();
                    info.Date = row["日期"].ToString();
                    info.TunnelName = row["隧道名称"].ToString();
                    info.TestTime = row["测试时间(进及出)"].ToString();
                    info.LogName = row["LOG名称"].ToString();

                    if (info.SetTestTimeToDateTime())
                    {
                        string strKey = info.Date + info.LogName;
                        List<TunnelBaseInfo> baseInfoList;
                        if (!baseInfoDic.TryGetValue(strKey, out baseInfoList))
                        {
                            baseInfoList = new List<TunnelBaseInfo>();
                            baseInfoDic[strKey] = baseInfoList;
                        }
                        baseInfoList.Add(info);
                    }
                    else
                    {
                        strbWrongTimeIndex.Append(index + ",");
                    }
                }

                if (strbWrongTimeIndex.Length > 0)
                {
                    baseInfoDic = new Dictionary<string, List<TunnelBaseInfo>>();
                    XtraMessageBox.Show("读取表格出错，请检查时间格式是否符合规范！\r\n错误行：" + strbWrongTimeIndex.Remove(strbWrongTimeIndex.Length - 1, 1).ToString());
                    return false;
                }
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show(ex.Message + Environment.NewLine + ex.Source + Environment.NewLine + ex.StackTrace);
                return false;
            }
            return true;
        }

        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }
            TunnelConditionDlg dlg = new TunnelConditionDlg();
            dlg.SetCondition(coverCond);
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                coverCond = dlg.GetCondition();
                if (getTunnelInfoFromExcle(coverCond.FilePath))
                {
                    return baseInfoDic != null && baseInfoDic.Count > 0;
                }
            }
            return false;
        }

        protected override void getReadyBeforeQuery()
        {
            resultList = new List<TunnelInfo>();
            replayContent = getDIYReplayContent();
        }
        protected DIYReplayContentOption getDIYReplayContent()
        {
            DIYReplayContentOption content = new DIYReplayContentOption();
            content.EventInclude = false;
            content.MessageInclude = false;
            content.MessageL3HexCode = false;
            content.DefaultSerialThemeName = null;

            content.SampleColumns = new List<ColumnDefItem>();
            content.SampleColumns.AddRange(InterfaceManager.GetInstance().GetColumnDefByShowName("RxLevSub"));
            content.SampleColumns.AddRange(InterfaceManager.GetInstance().GetColumnDefByShowName("lte_RSRP"));
            content.SampleColumns.AddRange(InterfaceManager.GetInstance().GetColumnDefByShowName("lte_SINR"));

            return content;
        }
        protected override void query()
        {
            if (MainModel.IsBackground && !MainModel.BackgroundStarted)
            {
                return;
            }
            if (!getCondition())
            {
                return;
            }
            getReadyBeforeQuery();
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
            }
            catch
            {
                //continue
            }
            IsShowFileInfoForm = false;
            MainModel.IsDrawEventResult = false;
            bool drawServer = MainModel.MainForm.GetMapForm().GetCellLayer().DrawServer;
            MainModel.MainForm.GetMapForm().GetCellLayer().DrawServer = false;
            if (!MainModel.IsBackground)
            {
                clientProxy.Close();
                if (!MainModel.QueryFromBackground)
                {
                    queryInThread();
                }
                else
                {
                    getBackgroundData();
                }
                MainModel.MainForm.GetMapForm().GetCellLayer().DrawServer = drawServer;
                if (!MainModel.QueryFromBackground)
                {
                    MainModel.FireDTDataChanged(this);
                    fireShowForm();
                    fireSetDefaultMapSerialTheme();
                }
                else
                {
                    initBackgroundImageDesc();
                }
            }
            else
            {
                doBackgroundStatByFile(clientProxy);
                clientProxy.Close();
            }
        }
        protected virtual void queryInThread()
        {
            condition.Geometorys = null;
            foreach (var varPair in baseInfoDic)
            {
                List<TunnelBaseInfo> baseInoList = varPair.Value;
                baseInoList.Sort();

                TunnelBaseInfo firstBaseInfo = baseInoList[0];

                TimePeriod period = new TimePeriod(firstBaseInfo.BeginTime, baseInoList[baseInoList.Count -1].EndTime);
                condition.Periods = new List<TimePeriod> { period };
                condition.NameFilterType = FileFilterType.ByFileName;
                int orNum = 1;
                condition.FileName = QueryCondition.MakeFileFilterString(firstBaseInfo.LogName, ref orNum).Replace("[_]", "_");
                condition.FileNameOrNum = orNum;
                queryFileToAnalyse();

                foreach (TunnelBaseInfo info in baseInoList)
                {
                    curTunnelInfo = new TunnelInfo(info);

                    if (MainModel.FileInfos.Count > 0)
                    {
                        period = new TimePeriod(info.BeginTime, info.EndTime);
                        condition.Periods = new List<TimePeriod> { period };
                        analyseFiles();
                    }
                    curTunnelInfo.SN = resultList.Count + 1;
                    resultList.Add(curTunnelInfo);
                }
            }
        }
        protected override void analyseFiles()
        {
            try
            {
                int iloop = 0;
                foreach (FileInfo fileInfo in MainModel.FileInfos)
                {
                    WaitBox.Text = "正在分析文件( " + (++iloop) + "/" + MainModel.FileInfos.Count + " )...";
                    WaitBox.ProgressPercent = (int)(iloop * 100.0 / MainModel.FileInfos.Count);
                    if (filterFile(fileInfo))
                    {
                        continue;
                    }
                    curAnaFileInfo = fileInfo;
                    Condition.FileInfos.Clear();
                    Condition.FileInfos.Add(fileInfo);
                    MainModel.IsFileReplayByCompareMode = Condition.isCompareMode;

                    replay();
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                }
            }
            finally
            {
                System.Threading.Thread.Sleep(20);
                WaitBox.Close();
            }
        }
        protected override bool filterFile(FileInfo fileInfo)
        {
            int beginTime = (int)(JavaDate.GetMilliseconds(curTunnelInfo.BeginTime) / 1000);
            int endTime = (int)(JavaDate.GetMilliseconds(curTunnelInfo.EndTime) / 1000);
            if (fileInfo.BeginTime <= beginTime && endTime <= fileInfo.EndTime)
            {
                return false;
            }
            return true;
        }
        protected override void replay()
        {
            QueryCondition condition = new QueryCondition();
            condition.QueryType = Condition.QueryType;
            condition.FileInfos.AddRange(Condition.FileInfos);
            condition.Geometorys = Condition.Geometorys;
            condition.Periods = Condition.Periods;
            ReplayFileInPeriodQueryByDIYContent query = new ReplayFileInPeriodQueryByDIYContent(MainModel);
            query.SetQueryCondition(condition);
            query.DIYReplayContent = replayContent;
            query.Query();
            doStatWithQuery();
            MainModel.ClearDTData();
        }
        protected override void doStatWithQuery()
        {
            try
            {
                foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
                {
                    doWithTestPointDelegate doWithTpFunc = null;
                    ServiceName serviceType = ServiceTypeManager.getServiceNameFromTypeID(file.ServiceType);
                    if (serviceType == ServiceName.LTE)
                    {
                        curTunnelInfo.LteTpCountSum += file.TestPoints.Count;
                        doWithTpFunc = new doWithTestPointDelegate(doWithLteTp);
                    }
                    else if (serviceType == ServiceName.GSM)
                    {
                        curTunnelInfo.GsmTpCountSum += file.TestPoints.Count;
                        doWithTpFunc = new doWithTestPointDelegate(doWithGsmTp);
                    }
                    else
                    {
                        return;
                    }

                    if (!curTunnelInfo.FileNameList.Contains(file.FileName))
                    {
                        curTunnelInfo.FileNameList.Add(file.FileName);
                    }

                    foreach (TestPoint tp in file.TestPoints)
                    {
                        doWithTpFunc(tp);
                    }
                }
            }
            catch (Exception ee)
            {
                System.Windows.Forms.MessageBox.Show(ee.Message + Environment.NewLine + ee.Source + Environment.NewLine + ee.StackTrace);
            }
        }
        protected void doWithGsmTp(TestPoint tp)
        {
            short? rxlev = (short?)tp["RxLevSub"];
            if (rxlev != null && rxlev <= -10 && rxlev >= coverCond.RxLevMin)
            {
                curTunnelInfo.GsmTpCountValid++;
            }
        }
        protected void doWithLteTp(TestPoint tp)
        {
            float? rsrp = (float?)tp["lte_RSRP"];
            float? sinr = (float?)tp["lte_SINR"];
            if (rsrp != null && rsrp <= 25 && sinr != null && sinr <= 50 && rsrp >= coverCond.RsrpMin && sinr >= coverCond.SinrMin)
            {
                curTunnelInfo.LteTpCountValid++;
            }
        }
        protected override void fireShowForm()
        {
            if (resultList.Count == 0)
            {
                System.Windows.Forms.MessageBox.Show("没有符合条件的信息！");
                return;
            }
            TunnelCoverInfoListForm frm = MainModel.CreateResultForm(typeof(TunnelCoverInfoListForm)) as TunnelCoverInfoListForm;
            frm.FillData(resultList);
            frm.Owner = MainModel.MainForm;
            frm.Visible = true;
            frm.BringToFront();
        }

    }
}
