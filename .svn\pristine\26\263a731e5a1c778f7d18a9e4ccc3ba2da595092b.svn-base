﻿using MasterCom.RAMS.Func;
using MasterCom.RAMS.Func.SystemSetting;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.BackgroundFunc.StationAccept_Shanxi
{
    public class QueryAndReplayCirCleFileInfo : DIYAnalyseFilesOneByOneByRegion
    {
        public List<DTFileDataManager> DTFiles { get; set; }
        protected NetType type;
        public QueryAndReplayCirCleFileInfo(NetType type)
            : base(MainModel.GetInstance())
        {
            this.type = type;
        }
        public override string Name
        {
            get
            {
                return "单站验收";
            }
        }

        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 18000, 18040, "查询");
        }

        protected override bool getCondition()
        {
            FilterSampleByRegion = false;
            IncludeEvent = false;
         
            if (type == NetType.LTE)
            {
                ServiceTypes.Clear();
                ServiceTypes.AddRange(ServiceTypeManager.GetServiceTypesByServiceName(ServiceName.LTE));
                ServiceTypes.AddRange(ServiceTypeManager.GetServiceTypesByServiceName(ServiceName.VoLTE));
            }
            else
            {
                ServiceTypes.Clear();
                ServiceTypes = ServiceTypeManager.GetServiceTypesByServiceName(ServiceName.NR);
            }
            return true;
        }
        protected override void query()
        {
            if (!getCondition())
            {
                return;
            }
            getReadyBeforeQuery();
            IsShowFileInfoForm = false;
            MainModel.IsDrawEventResult = false;
            bool drawServer = MainModel.MainForm.GetMapForm().GetCellLayer().DrawServer;
            MainModel.MainForm.GetMapForm().GetCellLayer().DrawServer = false;

            queryFileToAnalyse();
            analyseFiles();

            MainModel.MainForm.GetMapForm().GetCellLayer().DrawServer = drawServer;
        }
        protected override void clearDataBeforeAnalyseFiles()
        {
            DTFiles = new List<DTFileDataManager>();
        }

        protected override void analyseFiles()
        {
            try
            {
                //BackgroundFuncManager.GetInstance().ReportBackgroundInfo("DeBug : 文件:" + MainModel.FileInfos.Count + "个...");
                List<FileInfo> files = new List<FileInfo>();
                foreach (FileInfo fileInfo in MainModel.FileInfos)
                {
                    if (ServiceTypes.Count > 0 && fileInfo.ServiceType > 0 && !ServiceTypes.Contains((ServiceType)fileInfo.ServiceType))
                    {
                        continue;
                    }
                    files.Add(fileInfo);
                }
                clearDataBeforeAnalyseFiles();
                foreach (FileInfo fileInfo in files)
                {
                    //BackgroundFuncManager.GetInstance().ReportBackgroundInfo($"DeBug : Table : {fileInfo.LogTable} ID : {fileInfo.ID}");
                    Condition.FileInfos.Clear();
                    Condition.FileInfos.Add(fileInfo);
                    MainModel.IsFileReplayByCompareMode = Condition.isCompareMode;

                    replay();
                    if (mainModel.BackgroundStopRequest)
                    {
                        break;
                    }
                }
                MainModel.ClearDTData();
            }
            catch (Exception e)
            {
                BackgroundFuncManager.GetInstance().ReportBackgroundError(e);
            }
            finally
            {
                System.Threading.Thread.Sleep(20);
            }
        }
        protected override void doStatWithQuery()
        {
            DTFiles.AddRange(MainModel.DTDataManager.FileDataManagers);
        }
    }
}
