﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using BrightIdeasSoftware;
using DevExpress.XtraCharts;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc.VoLTEDropCallCause
{
    public partial class LTEDropCallCauseForm : MinCloseForm
    {
        CallInfo clickedCallInfo = new CallInfo();                  //保存当前点击的CallInfo

        public LTEDropCallCauseForm():base()
        {
            InitializeComponent();
            this.DisposeWhenClose = true;
            init();
        }

        private void init()
        {
            addDropCallInfo();

            addCallInfo();
        }

        private void addCallInfo()
        {
            colFileName.AspectGetter += delegate (object row)
            {
                if (row is CallInfo)
                {
                    return (row as CallInfo).FileName;
                }
                return null;
            };

            colHoNum.AspectGetter += delegate (object row)
            {
                if (row is CallInfo)
                {
                    return (row as CallInfo).HoNum;
                }
                return null;
            };

            colIsDrop.AspectGetter += delegate (object row)
            {
                if (row is CallInfo)
                {
                    return (row as CallInfo).IsDropCall ? "是" : "否";
                }
                return null;
            };

            addDropEvtInfo();

            this.colMoMt.AspectGetter += delegate (object row)
            {
                if (row is CallInfo)
                {
                    CallInfo call = row as CallInfo;
                    return call.MoMtDesc;
                }
                return null;
            };

            this.colRsrp.AspectGetter += delegate (object row)
            {
                if (row is CallInfo)
                {
                    CallInfo call = row as CallInfo;
                    return call.RsrpAvg;
                }
                return null;
            };

            this.colSinr.AspectGetter += delegate (object row)
            {
                if (row is CallInfo)
                {
                    CallInfo call = row as CallInfo;
                    return call.SinrAvg;
                }
                return null;
            };

            this.colTime.AspectGetter += delegate (object row)
            {
                if (row is CallInfo)
                {
                    CallInfo call = row as CallInfo;
                    return call.DropTime;
                }
                return null;
            };

            this.colCause.AspectGetter += delegate (object row)
            {
                if (row is CallInfo)
                {
                    CallInfo call = row as CallInfo;
                    if (call.IsDropCall)
                    {
                        return call.DropCause;
                    }
                    return null;
                }
                return null;
            };

            this.colMultiCvrPer.AspectGetter += delegate (object row)
            {
                if (row is CallInfo)
                {
                    CallInfo call = row as CallInfo;
                    return call.MultiCvrPer;
                }
                return null;
            };
        }

        private void addDropEvtInfo()
        {
            colLat.AspectGetter += delegate (object row)
            {
                if (row is CallInfo)
                {
                    CallInfo call = row as CallInfo;
                    if (call.IsDropCall)
                    {
                        return call.DropEvt.Latitude;
                    }
                }
                return null;
            };

            this.colLng.AspectGetter += delegate (object row)
            {
                if (row is CallInfo)
                {
                    CallInfo call = row as CallInfo;
                    if (call.IsDropCall)
                    {
                        return call.DropEvt.Longitude;
                    }
                }
                return null;
            };
        }

        private void addDropCallInfo()
        {
            this.objLv.CanExpandGetter += delegate (object row)
            {
                return row is DropCallInfo;
            };

            this.objLv.ChildrenGetter += delegate (object row)
            {
                if (row is DropCallInfo)
                {
                    return (row as DropCallInfo).MoMtCalls;
                }
                return null;
            };

            this.colSN.AspectGetter += delegate (object row)
            {
                if (row is DropCallInfo)
                {
                    DropCallInfo call = row as DropCallInfo;
                    return call.SN;
                }
                return null;
            };
        }

        private List<DropCallInfo> calls = null;
        private DropCallCondition dropCallCond;
        internal void FillData(List<DropCallInfo> calls,DropCallCondition dropCallCond)
        {
            this.calls = calls;
            this.dropCallCond = dropCallCond;
            makeSummary();
            this.colSinr.Text = string.Format("掉话前{0}秒平均SINR",dropCallCond.PoorSinrSec);
            this.colRsrp.Text = string.Format("掉话前{0}秒平均RSRP",dropCallCond.WeakRsrpSec);
            this.colHoNum.Text = string.Format("掉话前{0}秒切换次数", dropCallCond.HoSec);
            this.objLv.ClearObjects();
            this.objLv.RebuildColumns();
            this.objLv.SetObjects(calls);
            this.objLv.ExpandAll();
        }

        private void makeSummary()
        {
            Dictionary<string, int> causeDic = new Dictionary<string, int>();
            foreach (string name in Enum.GetNames(typeof(DropCallCause)))
            {
                causeDic[name] = 0;
            }
            int dropNum = 0;
            foreach (DropCallInfo dropCall in calls)
            {
                foreach (CallInfo call in dropCall.MoMtCalls)
                {
                    if (call.IsDropCall)
                    {
                        dropNum++;
                        causeDic[call.DropCause.ToString()]++;
                    }
                }
            }
            DataTable tb = new DataTable();
            tb.Columns.Add("原因", typeof(string));
            tb.Columns.Add("掉话个数", typeof(int));
            tb.Columns.Add("占比(%)", typeof(double));

            Series mainSer = chartMain.Series[0];
            mainSer.Points.Clear();
            foreach (KeyValuePair<string, int> pair in causeDic)
            {
                DataRow row = tb.NewRow();
                row["原因"] = pair.Key;
                row["掉话个数"] = pair.Value;
                double per = Math.Round(100.0 * pair.Value / dropNum, 2);
                row["占比(%)"] = per;
                tb.Rows.Add(row);
                SeriesPoint pnt = new SeriesPoint(pair.Key, per);
                mainSer.Points.Add(pnt);
            }
            DataRow srow = tb.NewRow();
            srow["原因"] = "汇总";
            srow["掉话个数"] = dropNum;
            srow["占比(%)"] = 100;
            tb.Rows.Add(srow);
            gridSummary.DataSource = tb;
            viewSummary.PopulateColumns();
            viewSummary.BestFitColumns();
        }

        private void objLv_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            OlvListViewHitTestInfo info = this.objLv.OlvHitTest(e.X, e.Y);
            if (info.RowObject is DropCallInfo)
            {
                MainModel.ClearDTData();
                DropCallInfo dc = info.RowObject as DropCallInfo;
                foreach (CallInfo call in dc.MoMtCalls)
                {
                    addDTData(call);
                }
                this.MainModel.IsFileReplayByCompareMode = true;
                this.MainModel.FireDTDataChanged(this);
                this.MainModel.FireSetDefaultMapSerialTheme("LTE_TDD:RSRP");
            }
            else if (info.RowObject is CallInfo)
            {
                MainModel.ClearDTData();
                CallInfo call = info.RowObject as CallInfo;
                addDTData(call);
                this.MainModel.IsFileReplayByCompareMode = false;
                this.MainModel.FireDTDataChanged(this);
                this.MainModel.FireSetDefaultMapSerialTheme("LTE_TDD:RSRP");
            }
        }

        private void addDTData(CallInfo call)
        {
            foreach (TestPoint tp in call.TestPoints)
            {
                MainModel.DTDataManager.Add(tp);
            }
            foreach (Event evt in call.Events)
            {
                MainModel.DTDataManager.Add(evt);
            }
            foreach (MasterCom.RAMS.Model.Message msg in call.Messages)
            {
                MainModel.DTDataManager.Add(msg);
            }
        }

        private void miExportXls_Click(object sender, EventArgs e)
        {
            List<NPOIRow> rows = new List<NPOIRow>();
            NPOIRow row = new NPOIRow();
            rows.Add(row);
            foreach (OLVColumn col in objLv.Columns)
            {
                row.AddCellValue(col.Text);
            }
            foreach (DropCallInfo dc in calls)
            {
                row = new NPOIRow();
                rows.Add(row);
                row.AddCellValue(dc.SN);
                foreach (CallInfo call in dc.MoMtCalls)
                {
                    NPOIRow subRow = new NPOIRow();
                    row.AddSubRow(subRow);
                    foreach (OLVColumn col in objLv.Columns)
                    {
                        if (col==this.colSN)
                        {
                            continue;
                        }
                        subRow.AddCellValue(col.GetValue(call));
                    }
                }
            }
            ExcelNPOIManager.ExportToExcel(rows);
        }

        private void miExpandAll_Click(object sender, EventArgs e)
        {
            this.objLv.ExpandAll();
        }

        private void miCollapseAll_Click(object sender, EventArgs e)
        {
            this.objLv.CollapseAll();
        }

        private void miExportXlsSum_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(this.viewSummary);
        }

        /// <summary>
        /// 保存当前右击的行所对应的CallInfo
        /// </summary>
        private void objLv_MouseDown(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Right)
            {
                TreeListView lv = sender as TreeListView;
                OlvListViewHitTestInfo info = lv.OlvHitTest(e.X, e.Y);
                if (info.RowObject is CallInfo)
                {
                    clickedCallInfo = info.RowObject as CallInfo;
                    miReplayFile.Enabled = true;
                    miReplayEvent.Enabled = true;
                    miCompareReplayEvent.Enabled = true;
                }
                else
                {
                    clickedCallInfo = null;
                    miReplayFile.Enabled = false;
                    miReplayEvent.Enabled = false;
                    miCompareReplayEvent.Enabled = false;
                }
            }
        }

        /// <summary>
        /// 当ctxMenu关闭时使回放功能失效
        /// </summary>
        private void ctxMenu_Closed(object sender, ToolStripDropDownClosedEventArgs e)
        {
            miReplayFile.Enabled = false;
            miReplayEvent.Enabled = false;
            miCompareReplayEvent.Enabled = false;
        }

        /// <summary>
        /// 回放所属文件
        /// </summary>
        private void miReplayFile_Click(object sender, EventArgs e)
        {
            if (clickedCallInfo == null)
            {
                MessageBox.Show("请选择文件！");
                miReplayFile.Enabled = false;
                miReplayEvent.Enabled = false;
                miCompareReplayEvent.Enabled = false;
            }
            else
            {
                if (clickedCallInfo.DropEvt != null)
                {
                    DTData data = clickedCallInfo.DropEvt;
                    Model.Interface.FileReplayer.Replay(data, false);
                }
                else
                    MessageBox.Show("掉话事件为空！");
            }
        }

        /// <summary>
        /// 回放事件
        /// </summary>
        private void miReplayEvent_Click(object sender, EventArgs e)
        {
            if (clickedCallInfo == null)
            {
                MessageBox.Show("请选择文件！");
                miReplayFile.Enabled = false;
                miReplayEvent.Enabled = false;
                miCompareReplayEvent.Enabled = false;
            }
            else
            {
                if (clickedCallInfo.DropEvt != null)
                {
                    Model.Interface.FileReplayer.Replay(clickedCallInfo.DropEvt, true);
                }
                else
                    MessageBox.Show("掉话事件为空！");
            }
        }

        /// <summary>
        /// 对比回放事件
        /// </summary>
        private void miCompareReplayEvent_Click(object sender, EventArgs e)
        {
            if (clickedCallInfo == null)
            {
                MessageBox.Show("请选择文件！");
                miReplayFile.Enabled = false;
                miReplayEvent.Enabled = false;
                miCompareReplayEvent.Enabled = false;
            }
            else
            {
                if (clickedCallInfo.DropEvt != null)
                {
                    Model.Interface.FileReplayer.ReplayOnePartBothSides(clickedCallInfo.DropEvt);
                }
                else
                    MessageBox.Show("掉话事件为空！");
            }

        }
        
    }
}
