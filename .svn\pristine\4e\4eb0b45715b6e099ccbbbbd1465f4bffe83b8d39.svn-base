﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.CQT
{
    public class DIYQueryCQTPicture : QueryBase
    {
        public DIYQueryCQTPicture(MainModel mm)
            : base(mm)
        { }
        public override string Name
        {
            get {return "查询CQT图片信息"; }
        }

        public override string IconName
        {
            get { return ""; }
        }

        protected override bool isValidCondition()
        {
            return true;
        }

        protected override void query()
        {
            ClientProxy clientProxy = new ClientProxy();
            if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
            {
                ErrorInfo = "连接服务器失败!";
                return;
            }
            try
            {
                WaitBox.Show("开始接收数据...", queryInThread, clientProxy);
            }
            finally
            {
                clientProxy.Close();
            }
        }

        private void queryInThread(object o)
        {
            //
        }

        protected virtual void prepareSearchPackage(Package package)
        {
            package.Command = Command.CellConfigManage;
            package.SubCommand = SubCommand.Request;
            //package.Content.Type = RequestType.REQTYPE_CQT_PICTURE_INFO_INSERT;
        }
    }

}
