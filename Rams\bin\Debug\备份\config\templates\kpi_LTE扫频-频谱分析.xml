<?xml version="1.0"?>
<Configs>
  <Config name="ReportSetting">
    <Item name="styles" typeName="IDictionary">
      <Item typeName="String" key="Name">LTE扫频-频谱分析</Item>
      <Item typeName="IList" key="Cells">
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Lc_5F250210}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">9</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Lc_5F250201}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RSRP_SampleNum</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Lc_5F250202}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Lc_5F250203}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Lc_5F250204}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">3</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Lc_5F250205}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">4</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Lc_5F250206}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">5</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Lc_5F250207}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">6</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Lc_5F250208}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">7</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Lc_5F250209}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">8</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RSRP_Other</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">10</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Lc_5F250211}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">10</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RSRP_SampleMax</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RSRP_SampleMin</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RSRP_[F90,) </Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">3</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RSRP_[F94,)</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">4</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RSRP_[F100,) </Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">5</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RSRP_[F105,) </Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">6</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RSRP_[F110,) </Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">7</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RSRP_[F115,) </Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">8</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RSRP_[F120,)  </Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">9</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
      </Item>
      <Item typeName="IList" key="Graphs" />
      <Item typeName="IList" key="ColInfo" />
      <Item typeName="IList" key="ColWidth" />
    </Item>
  </Config>
</Configs>