﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTScanLTEInterferenceListForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(ZTScanLTEInterferenceListForm));
            this.ctxMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem1 = new System.Windows.Forms.ToolStripSeparator();
            this.miExpandAll = new System.Windows.Forms.ToolStripMenuItem();
            this.miCallapsAll = new System.Windows.Forms.ToolStripMenuItem();
            this.ListViewInter = new BrightIdeasSoftware.TreeListView();
            this.olvColumnType = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnSample = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnRate = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnLongitude = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnLatitude = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnARFCN = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnRxlev = new BrightIdeasSoftware.OLVColumn();
            this.ctxMenu.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.ListViewInter)).BeginInit();
            this.SuspendLayout();
            // 
            // ctxMenu
            // 
            this.ctxMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExportExcel,
            this.toolStripMenuItem1,
            this.miExpandAll,
            this.miCallapsAll});
            this.ctxMenu.Name = "ctxMenu";
            this.ctxMenu.Size = new System.Drawing.Size(130, 76);
            // 
            // miExportExcel
            // 
            this.miExportExcel.Name = "miExportExcel";
            this.miExportExcel.Size = new System.Drawing.Size(129, 22);
            this.miExportExcel.Text = "导出Excel";
            this.miExportExcel.Click += new System.EventHandler(this.miExportExcel_Click);
            // 
            // toolStripMenuItem1
            // 
            this.toolStripMenuItem1.Name = "toolStripMenuItem1";
            this.toolStripMenuItem1.Size = new System.Drawing.Size(126, 6);
            this.toolStripMenuItem1.Visible = false;
            // 
            // miExpandAll
            // 
            this.miExpandAll.Name = "miExpandAll";
            this.miExpandAll.Size = new System.Drawing.Size(129, 22);
            this.miExpandAll.Text = "全部展开";
            this.miExpandAll.Click += new System.EventHandler(this.miExpandAll_Click);
            // 
            // miCallapsAll
            // 
            this.miCallapsAll.Name = "miCallapsAll";
            this.miCallapsAll.Size = new System.Drawing.Size(129, 22);
            this.miCallapsAll.Text = "全部合并";
            this.miCallapsAll.Click += new System.EventHandler(this.miCallapsAll_Click);
            // 
            // ListViewInter
            // 
            this.ListViewInter.AllColumns.Add(this.olvColumnType);
            this.ListViewInter.AllColumns.Add(this.olvColumnSample);
            this.ListViewInter.AllColumns.Add(this.olvColumnRate);
            this.ListViewInter.AllColumns.Add(this.olvColumnLongitude);
            this.ListViewInter.AllColumns.Add(this.olvColumnLatitude);
            this.ListViewInter.AllColumns.Add(this.olvColumnARFCN);
            this.ListViewInter.AllColumns.Add(this.olvColumnRxlev);
            this.ListViewInter.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnType,
            this.olvColumnSample,
            this.olvColumnRate,
            this.olvColumnLongitude,
            this.olvColumnLatitude,
            this.olvColumnARFCN,
            this.olvColumnRxlev});
            this.ListViewInter.ContextMenuStrip = this.ctxMenu;
            this.ListViewInter.Cursor = System.Windows.Forms.Cursors.Default;
            this.ListViewInter.Dock = System.Windows.Forms.DockStyle.Fill;
            this.ListViewInter.FullRowSelect = true;
            this.ListViewInter.GridLines = true;
            this.ListViewInter.HeaderWordWrap = true;
            this.ListViewInter.IsNeedShowOverlay = false;
            this.ListViewInter.Location = new System.Drawing.Point(0, 0);
            this.ListViewInter.Name = "ListViewInter";
            this.ListViewInter.OwnerDraw = true;
            this.ListViewInter.ShowGroups = false;
            this.ListViewInter.Size = new System.Drawing.Size(732, 502);
            this.ListViewInter.TabIndex = 5;
            this.ListViewInter.UseCompatibleStateImageBehavior = false;
            this.ListViewInter.View = System.Windows.Forms.View.Details;
            this.ListViewInter.VirtualMode = true;
            this.ListViewInter.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.listViewTotal_MouseDoubleClick);
            // 
            // olvColumnType
            // 
            this.olvColumnType.AspectName = "";
            this.olvColumnType.HeaderFont = null;
            this.olvColumnType.Text = "类型";
            this.olvColumnType.Width = 150;
            // 
            // olvColumnSample
            // 
            this.olvColumnSample.HeaderFont = null;
            this.olvColumnSample.Text = "采样点数";
            this.olvColumnSample.Width = 80;
            // 
            // olvColumnRate
            // 
            this.olvColumnRate.HeaderFont = null;
            this.olvColumnRate.Text = "比例";
            this.olvColumnRate.Width = 80;
            // 
            // olvColumnLongitude
            // 
            this.olvColumnLongitude.HeaderFont = null;
            this.olvColumnLongitude.Text = "经度";
            this.olvColumnLongitude.Width = 100;
            // 
            // olvColumnLatitude
            // 
            this.olvColumnLatitude.HeaderFont = null;
            this.olvColumnLatitude.Text = "纬度";
            this.olvColumnLatitude.Width = 100;
            // 
            // olvColumnARFCN
            // 
            this.olvColumnARFCN.HeaderFont = null;
            this.olvColumnARFCN.Text = "频点";
            this.olvColumnARFCN.Width = 100;
            // 
            // olvColumnRxlev
            // 
            this.olvColumnRxlev.HeaderFont = null;
            this.olvColumnRxlev.Text = "信号强度";
            this.olvColumnRxlev.Width = 100;
            // 
            // ZTScanLTEInterferenceListForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(732, 502);
            this.Controls.Add(this.ListViewInter);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Name = "ZTScanLTEInterferenceListForm";
            this.Text = "LTE干扰分析";
            this.ctxMenu.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.ListViewInter)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.ContextMenuStrip ctxMenu;
        private System.Windows.Forms.ToolStripMenuItem miExportExcel;
        private System.Windows.Forms.ToolStripSeparator toolStripMenuItem1;
        private System.Windows.Forms.ToolStripMenuItem miExpandAll;
        private System.Windows.Forms.ToolStripMenuItem miCallapsAll;
        private BrightIdeasSoftware.TreeListView ListViewInter;
        private BrightIdeasSoftware.OLVColumn olvColumnType;
        private BrightIdeasSoftware.OLVColumn olvColumnARFCN;
        private BrightIdeasSoftware.OLVColumn olvColumnRate;
        private BrightIdeasSoftware.OLVColumn olvColumnSample;
        private BrightIdeasSoftware.OLVColumn olvColumnLatitude;
        private BrightIdeasSoftware.OLVColumn olvColumnRxlev;
        private BrightIdeasSoftware.OLVColumn olvColumnLongitude;

    }
}