﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.MTGis
{
    public partial class CommonCustLayerSetProperties : MTLayerPropUserControl
    {
        private CustomDrawLayer layer;
        AxMapWinGIS.AxMap mapControl = null;
        public event EventHandler LayerPropertyChanged;
        public CommonCustLayerSetProperties()
        {
            InitializeComponent();
        }
        public override void Setup(Object obj)
        {
            // the obj parameter should never be null, but just to be on the safe side:
            if (obj == null)
            {
                return;
            }
            Text = "基础设置";
            layer = (CustomDrawLayer)obj;
            updateControls();

        }

        private void updateControls()
        {
            if (layer != null)
            {
                cbxRange.Checked = layer.VisibleScaleEnabled;
                cbxVisible.Checked = layer.IsVisible;

                txtRangMax.Text = ((int)layer.VisibleScale.ScaleMax).ToString();
                txtRangMin.Text = ((int)layer.VisibleScale.ScaleMin).ToString();
            }
        }
        internal void SetMapControl(AxMapWinGIS.AxMap axMap)
        {
            this.mapControl = axMap;
        }

        private void cbxVisible_CheckedChanged(object sender, EventArgs e)
        {
            layer.IsVisible = cbxVisible.Checked;
            CustLayerEventArgs arg = new CustLayerEventArgs();
            arg.custLayer = layer;
            LayerPropertyChanged(this, arg);
        }

        private void cbxRange_CheckedChanged(object sender, EventArgs e)
        {
            bool rangeEnabled = cbxRange.Checked;
            layer.VisibleScaleEnabled = rangeEnabled;
            txtRangMax.Enabled = rangeEnabled;
            txtRangMin.Enabled = rangeEnabled;
            btnScaleCurMax.Enabled = rangeEnabled;
            btnScaleCurMin.Enabled = rangeEnabled;
            CustLayerEventArgs arg = new CustLayerEventArgs();
            arg.custLayer = layer;
            LayerPropertyChanged(this, arg);
        }

        private void txtRangMax_TextChanged(object sender, EventArgs e)
        {
            int value;
            int.TryParse(txtRangMax.Text, out value);
            layer.VisibleScale.ScaleMax = value;
            CustLayerEventArgs arg = new CustLayerEventArgs();
            arg.custLayer = layer;
            LayerPropertyChanged(this, arg);
        }

        private void txtRangMin_TextChanged(object sender, EventArgs e)
        {
            int value;
            int.TryParse(txtRangMin.Text, out value);
            layer.VisibleScale.ScaleMin = value;
            CustLayerEventArgs arg = new CustLayerEventArgs();
            arg.custLayer = layer;
            LayerPropertyChanged(this, arg);
        }

        private void btnScaleCurMax_Click(object sender, EventArgs e)
        {
            txtRangMax.Text = "" + (int)mapControl.CurrentScale; 
        }

        private void btnScaleCurMin_Click(object sender, EventArgs e)
        {
            txtRangMin.Text = "" + (int)mapControl.CurrentScale;
        }

        private void trackBarAlpha_Scroll(object sender, EventArgs e)
        {
            if (layer != null)
            {
                layer.Alpha = trackBarAlpha.Value;
                layer.Invalidate();
            }
        }
        
    }
    public class CustLayerEventArgs : EventArgs
    {
        public CustomDrawLayer custLayer { get; set; }
    }
}
