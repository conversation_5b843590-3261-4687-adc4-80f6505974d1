﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class DropPerceptionForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.tabControl = new System.Windows.Forms.TabControl();
            this.tabPage1 = new System.Windows.Forms.TabPage();
            this.listView404 = new System.Windows.Forms.ListView();
            this.columnHeaderSN = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderDistrict = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderFileName = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderEventHappenTime = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderEventName = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderEventLongitude = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderEventLatitude = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderEventLAC = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderEventCI = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderTpKind = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderTpLongitude = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderTpLatitude = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderTpLAC = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderTpCI = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderBeginDropTime = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderBeginDropArfcn = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderBeginDropCpi = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderBeginDropDPCH = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderBeginDropHSDA_RSCP = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderBeginDropHSDPA_CI = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderBeginDropHSDPA_Uarfcn = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderAvgPCCPCH_RSCP = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderAvgPCCPCH_C2I = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderAvgDPCH_RSCP = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderAvgDPCH_C2I = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderSwitchTimes = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderNbcell1Arfcn = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderNbcell1Cpi = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderNbcell1PCCPCH_RSCP = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderNbcell2Arfcn = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderNbcell2Cpi = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderNbcell2PCCPCH_RSCP = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderNbcell3Arfcn = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderNbcell3Cpi = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderNbcell3PCCPCH_RSCP = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderNbcell4Arfcn = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderNbcell4Cpi = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderNbcell4PCCPCH_RSCP = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderNbcell5Arfcn = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderNbcell5Cpi = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderNbcell5PCCPCH_RSCP = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.tabPage2 = new System.Windows.Forms.TabPage();
            this.listView9893 = new System.Windows.Forms.ListView();
            this.columnHeaderSNP2 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderDistrictP2 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderFileNameP2 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderEventHappenTimeP2 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderEventNameP2 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderEventLongitudeP2 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderEventLatitudeP2 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderEventLACP2 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderEventCIP2 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderTpKindP2 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderArfcnP2 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderCpiP2 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderDPCHP2 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderHSDPA_RSCPP2 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderHSDPA_CIP2 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderHSDPA_UarfcnP2 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderAvgPCCPCH_RSCPP2 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderAvgPCCPCH_C2IP2 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderAvgDPCH_RSCPP2 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderAvgDPCH_C2IP2 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderSwitchTimesP2 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderNbcell1ArfcnP2 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderNbcell1CpiP2 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderNbcell1PCCPCH_RSCPP2 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderNbcell2ArfcnP2 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderNbcell2CpiP2 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderNbcell2PCCPCH_RSCPP2 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderNbcell3ArfcnP2 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderNbcell3CpiP2 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderNbcell3PCCPCH_RSCPP2 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderNbcell4ArfcnP2 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderNbcell4CpiP2 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderNbcell4PCCPCH_RSCPP2 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderNbcell5ArfcnP2 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderNbcell5CpiP2 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderNbcell5PCCPCH_RSCPP2 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.contextMenuStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.tabControl.SuspendLayout();
            this.tabPage1.SuspendLayout();
            this.tabPage2.SuspendLayout();
            this.contextMenuStrip.SuspendLayout();
            this.SuspendLayout();
            // 
            // tabControl
            // 
            this.tabControl.Alignment = System.Windows.Forms.TabAlignment.Bottom;
            this.tabControl.Controls.Add(this.tabPage1);
            this.tabControl.Controls.Add(this.tabPage2);
            this.tabControl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabControl.Location = new System.Drawing.Point(0, 0);
            this.tabControl.Name = "tabControl";
            this.tabControl.SelectedIndex = 0;
            this.tabControl.Size = new System.Drawing.Size(1109, 577);
            this.tabControl.TabIndex = 0;
            // 
            // tabPage1
            // 
            this.tabPage1.Controls.Add(this.listView404);
            this.tabPage1.Location = new System.Drawing.Point(4, 4);
            this.tabPage1.Name = "tabPage1";
            this.tabPage1.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage1.Size = new System.Drawing.Size(1101, 550);
            this.tabPage1.TabIndex = 0;
            this.tabPage1.Text = "感知掉线";
            this.tabPage1.UseVisualStyleBackColor = true;
            // 
            // listView404
            // 
            this.listView404.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.columnHeaderSN,
            this.columnHeaderDistrict,
            this.columnHeaderFileName,
            this.columnHeaderEventHappenTime,
            this.columnHeaderEventName,
            this.columnHeaderEventLongitude,
            this.columnHeaderEventLatitude,
            this.columnHeaderEventLAC,
            this.columnHeaderEventCI,
            this.columnHeaderTpKind,
            this.columnHeaderTpLongitude,
            this.columnHeaderTpLatitude,
            this.columnHeaderTpLAC,
            this.columnHeaderTpCI,
            this.columnHeaderBeginDropTime,
            this.columnHeaderBeginDropArfcn,
            this.columnHeaderBeginDropCpi,
            this.columnHeaderBeginDropDPCH,
            this.columnHeaderBeginDropHSDA_RSCP,
            this.columnHeaderBeginDropHSDPA_CI,
            this.columnHeaderBeginDropHSDPA_Uarfcn,
            this.columnHeaderAvgPCCPCH_RSCP,
            this.columnHeaderAvgPCCPCH_C2I,
            this.columnHeaderAvgDPCH_RSCP,
            this.columnHeaderAvgDPCH_C2I,
            this.columnHeaderSwitchTimes,
            this.columnHeaderNbcell1Arfcn,
            this.columnHeaderNbcell1Cpi,
            this.columnHeaderNbcell1PCCPCH_RSCP,
            this.columnHeaderNbcell2Arfcn,
            this.columnHeaderNbcell2Cpi,
            this.columnHeaderNbcell2PCCPCH_RSCP,
            this.columnHeaderNbcell3Arfcn,
            this.columnHeaderNbcell3Cpi,
            this.columnHeaderNbcell3PCCPCH_RSCP,
            this.columnHeaderNbcell4Arfcn,
            this.columnHeaderNbcell4Cpi,
            this.columnHeaderNbcell4PCCPCH_RSCP,
            this.columnHeaderNbcell5Arfcn,
            this.columnHeaderNbcell5Cpi,
            this.columnHeaderNbcell5PCCPCH_RSCP});
            this.listView404.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listView404.FullRowSelect = true;
            this.listView404.GridLines = true;
            this.listView404.Location = new System.Drawing.Point(3, 3);
            this.listView404.Name = "listView404";
            this.listView404.Size = new System.Drawing.Size(1095, 544);
            this.listView404.TabIndex = 0;
            this.listView404.UseCompatibleStateImageBehavior = false;
            this.listView404.View = System.Windows.Forms.View.Details;
            // 
            // columnHeaderSN
            // 
            this.columnHeaderSN.Text = "序号";
            this.columnHeaderSN.Width = 50;
            // 
            // columnHeaderDistrict
            // 
            this.columnHeaderDistrict.Text = "地市";
            this.columnHeaderDistrict.Width = 50;
            // 
            // columnHeaderFileName
            // 
            this.columnHeaderFileName.Text = "文件名";
            this.columnHeaderFileName.Width = 300;
            // 
            // columnHeaderEventHappenTime
            // 
            this.columnHeaderEventHappenTime.Text = "事件发生时间";
            this.columnHeaderEventHappenTime.Width = 100;
            // 
            // columnHeaderEventName
            // 
            this.columnHeaderEventName.Text = "事件类型";
            this.columnHeaderEventName.Width = 100;
            // 
            // columnHeaderEventLongitude
            // 
            this.columnHeaderEventLongitude.Text = "事件经度";
            this.columnHeaderEventLongitude.Width = 80;
            // 
            // columnHeaderEventLatitude
            // 
            this.columnHeaderEventLatitude.Text = "事件纬度";
            // 
            // columnHeaderEventLAC
            // 
            this.columnHeaderEventLAC.Text = "事件LAC";
            this.columnHeaderEventLAC.Width = 80;
            // 
            // columnHeaderEventCI
            // 
            this.columnHeaderEventCI.Text = "事件CI";
            this.columnHeaderEventCI.Width = 80;
            // 
            // columnHeaderTpKind
            // 
            this.columnHeaderTpKind.Text = "开始断流点网络类型";
            this.columnHeaderTpKind.Width = 120;
            // 
            // columnHeaderTpLongitude
            // 
            this.columnHeaderTpLongitude.Text = "开始断流点经度";
            this.columnHeaderTpLongitude.Width = 80;
            // 
            // columnHeaderTpLatitude
            // 
            this.columnHeaderTpLatitude.Text = "开始断流点纬度";
            this.columnHeaderTpLatitude.Width = 80;
            // 
            // columnHeaderTpLAC
            // 
            this.columnHeaderTpLAC.Text = "开始断流点LAC";
            this.columnHeaderTpLAC.Width = 80;
            // 
            // columnHeaderTpCI
            // 
            this.columnHeaderTpCI.Text = "开始断流点CI";
            this.columnHeaderTpCI.Width = 80;
            // 
            // columnHeaderBeginDropTime
            // 
            this.columnHeaderBeginDropTime.Text = "断流开始时间";
            this.columnHeaderBeginDropTime.Width = 100;
            // 
            // columnHeaderBeginDropArfcn
            // 
            this.columnHeaderBeginDropArfcn.Text = "开始断流频点";
            this.columnHeaderBeginDropArfcn.Width = 80;
            // 
            // columnHeaderBeginDropCpi
            // 
            this.columnHeaderBeginDropCpi.Text = "开始断流扰码";
            this.columnHeaderBeginDropCpi.Width = 80;
            // 
            // columnHeaderBeginDropDPCH
            // 
            this.columnHeaderBeginDropDPCH.Text = "开始断流DPCH频点";
            this.columnHeaderBeginDropDPCH.Width = 80;
            // 
            // columnHeaderBeginDropHSDA_RSCP
            // 
            this.columnHeaderBeginDropHSDA_RSCP.Text = "开始断流TD_HSDPA_HS_PDSCH_RSCP";
            this.columnHeaderBeginDropHSDA_RSCP.Width = 80;
            // 
            // columnHeaderBeginDropHSDPA_CI
            // 
            this.columnHeaderBeginDropHSDPA_CI.Text = "开始断流TD_HSDPA_HS_PDSCH_CI";
            this.columnHeaderBeginDropHSDPA_CI.Width = 80;
            // 
            // columnHeaderBeginDropHSDPA_Uarfcn
            // 
            this.columnHeaderBeginDropHSDPA_Uarfcn.Text = "开始断流TD_HSDPA_HS_WorkUARFCN";
            this.columnHeaderBeginDropHSDPA_Uarfcn.Width = 80;
            // 
            // columnHeaderAvgPCCPCH_RSCP
            // 
            this.columnHeaderAvgPCCPCH_RSCP.Text = "断流5秒前平均PCCPCH_RSCP";
            this.columnHeaderAvgPCCPCH_RSCP.Width = 80;
            // 
            // columnHeaderAvgPCCPCH_C2I
            // 
            this.columnHeaderAvgPCCPCH_C2I.Text = "断流5秒前平均PCCPCH_C2I";
            this.columnHeaderAvgPCCPCH_C2I.Width = 80;
            // 
            // columnHeaderAvgDPCH_RSCP
            // 
            this.columnHeaderAvgDPCH_RSCP.Text = "断流5秒前平均DPCH_RSCP";
            this.columnHeaderAvgDPCH_RSCP.Width = 80;
            // 
            // columnHeaderAvgDPCH_C2I
            // 
            this.columnHeaderAvgDPCH_C2I.Text = "断流5秒前平均DPCH_C2I";
            this.columnHeaderAvgDPCH_C2I.Width = 80;
            // 
            // columnHeaderSwitchTimes
            // 
            this.columnHeaderSwitchTimes.Text = "断流十秒前切换次数";
            // 
            // columnHeaderNbcell1Arfcn
            // 
            this.columnHeaderNbcell1Arfcn.Text = "邻区1主频";
            this.columnHeaderNbcell1Arfcn.Width = 80;
            // 
            // columnHeaderNbcell1Cpi
            // 
            this.columnHeaderNbcell1Cpi.Text = "邻区1扰码";
            this.columnHeaderNbcell1Cpi.Width = 80;
            // 
            // columnHeaderNbcell1PCCPCH_RSCP
            // 
            this.columnHeaderNbcell1PCCPCH_RSCP.Text = "邻区1PCCPCH_RSCP";
            this.columnHeaderNbcell1PCCPCH_RSCP.Width = 80;
            // 
            // columnHeaderNbcell2Arfcn
            // 
            this.columnHeaderNbcell2Arfcn.Text = "邻区2主频";
            this.columnHeaderNbcell2Arfcn.Width = 80;
            // 
            // columnHeaderNbcell2Cpi
            // 
            this.columnHeaderNbcell2Cpi.Text = "邻区2扰码";
            this.columnHeaderNbcell2Cpi.Width = 80;
            // 
            // columnHeaderNbcell2PCCPCH_RSCP
            // 
            this.columnHeaderNbcell2PCCPCH_RSCP.Text = "邻区2PCCPCH_RSCP";
            this.columnHeaderNbcell2PCCPCH_RSCP.Width = 80;
            // 
            // columnHeaderNbcell3Arfcn
            // 
            this.columnHeaderNbcell3Arfcn.Text = "邻区3主频";
            this.columnHeaderNbcell3Arfcn.Width = 80;
            // 
            // columnHeaderNbcell3Cpi
            // 
            this.columnHeaderNbcell3Cpi.Text = "邻区3扰码";
            this.columnHeaderNbcell3Cpi.Width = 80;
            // 
            // columnHeaderNbcell3PCCPCH_RSCP
            // 
            this.columnHeaderNbcell3PCCPCH_RSCP.Text = "邻区3PCCPCH_RSCP";
            this.columnHeaderNbcell3PCCPCH_RSCP.Width = 80;
            // 
            // columnHeaderNbcell4Arfcn
            // 
            this.columnHeaderNbcell4Arfcn.Text = "邻区4主频";
            this.columnHeaderNbcell4Arfcn.Width = 80;
            // 
            // columnHeaderNbcell4Cpi
            // 
            this.columnHeaderNbcell4Cpi.Text = "邻区4扰码";
            this.columnHeaderNbcell4Cpi.Width = 80;
            // 
            // columnHeaderNbcell4PCCPCH_RSCP
            // 
            this.columnHeaderNbcell4PCCPCH_RSCP.Text = "邻区4PCCPCH_RSCP";
            this.columnHeaderNbcell4PCCPCH_RSCP.Width = 80;
            // 
            // columnHeaderNbcell5Arfcn
            // 
            this.columnHeaderNbcell5Arfcn.Text = "邻区5主频";
            this.columnHeaderNbcell5Arfcn.Width = 80;
            // 
            // columnHeaderNbcell5Cpi
            // 
            this.columnHeaderNbcell5Cpi.Text = "邻区5扰码";
            this.columnHeaderNbcell5Cpi.Width = 80;
            // 
            // columnHeaderNbcell5PCCPCH_RSCP
            // 
            this.columnHeaderNbcell5PCCPCH_RSCP.Text = "邻区5PCCPCH_RSCP";
            this.columnHeaderNbcell5PCCPCH_RSCP.Width = 80;
            // 
            // tabPage2
            // 
            this.tabPage2.Controls.Add(this.listView9893);
            this.tabPage2.Location = new System.Drawing.Point(4, 4);
            this.tabPage2.Name = "tabPage2";
            this.tabPage2.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage2.Size = new System.Drawing.Size(1101, 550);
            this.tabPage2.TabIndex = 1;
            this.tabPage2.Text = "网络PDP去激活/路由区更新拒绝";
            this.tabPage2.UseVisualStyleBackColor = true;
            // 
            // listView9893
            // 
            this.listView9893.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.columnHeaderSNP2,
            this.columnHeaderDistrictP2,
            this.columnHeaderFileNameP2,
            this.columnHeaderEventHappenTimeP2,
            this.columnHeaderEventNameP2,
            this.columnHeaderEventLongitudeP2,
            this.columnHeaderEventLatitudeP2,
            this.columnHeaderEventLACP2,
            this.columnHeaderEventCIP2,
            this.columnHeaderTpKindP2,
            this.columnHeaderArfcnP2,
            this.columnHeaderCpiP2,
            this.columnHeaderDPCHP2,
            this.columnHeaderHSDPA_RSCPP2,
            this.columnHeaderHSDPA_CIP2,
            this.columnHeaderHSDPA_UarfcnP2,
            this.columnHeaderAvgPCCPCH_RSCPP2,
            this.columnHeaderAvgPCCPCH_C2IP2,
            this.columnHeaderAvgDPCH_RSCPP2,
            this.columnHeaderAvgDPCH_C2IP2,
            this.columnHeaderSwitchTimesP2,
            this.columnHeaderNbcell1ArfcnP2,
            this.columnHeaderNbcell1CpiP2,
            this.columnHeaderNbcell1PCCPCH_RSCPP2,
            this.columnHeaderNbcell2ArfcnP2,
            this.columnHeaderNbcell2CpiP2,
            this.columnHeaderNbcell2PCCPCH_RSCPP2,
            this.columnHeaderNbcell3ArfcnP2,
            this.columnHeaderNbcell3CpiP2,
            this.columnHeaderNbcell3PCCPCH_RSCPP2,
            this.columnHeaderNbcell4ArfcnP2,
            this.columnHeaderNbcell4CpiP2,
            this.columnHeaderNbcell4PCCPCH_RSCPP2,
            this.columnHeaderNbcell5ArfcnP2,
            this.columnHeaderNbcell5CpiP2,
            this.columnHeaderNbcell5PCCPCH_RSCPP2});
            this.listView9893.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listView9893.FullRowSelect = true;
            this.listView9893.GridLines = true;
            this.listView9893.Location = new System.Drawing.Point(3, 3);
            this.listView9893.Name = "listView9893";
            this.listView9893.Size = new System.Drawing.Size(1095, 544);
            this.listView9893.TabIndex = 0;
            this.listView9893.UseCompatibleStateImageBehavior = false;
            this.listView9893.View = System.Windows.Forms.View.Details;
            // 
            // columnHeaderSNP2
            // 
            this.columnHeaderSNP2.Text = "序号";
            this.columnHeaderSNP2.Width = 50;
            // 
            // columnHeaderDistrictP2
            // 
            this.columnHeaderDistrictP2.Text = "地市";
            this.columnHeaderDistrictP2.Width = 50;
            // 
            // columnHeaderFileNameP2
            // 
            this.columnHeaderFileNameP2.Text = "文件名";
            this.columnHeaderFileNameP2.Width = 300;
            // 
            // columnHeaderEventHappenTimeP2
            // 
            this.columnHeaderEventHappenTimeP2.Text = "事件发生时间";
            this.columnHeaderEventHappenTimeP2.Width = 100;
            // 
            // columnHeaderEventNameP2
            // 
            this.columnHeaderEventNameP2.Text = "事件类型";
            this.columnHeaderEventNameP2.Width = 100;
            // 
            // columnHeaderEventLongitudeP2
            // 
            this.columnHeaderEventLongitudeP2.Text = "经度";
            this.columnHeaderEventLongitudeP2.Width = 80;
            // 
            // columnHeaderEventLatitudeP2
            // 
            this.columnHeaderEventLatitudeP2.Text = "纬度";
            this.columnHeaderEventLatitudeP2.Width = 80;
            // 
            // columnHeaderEventLACP2
            // 
            this.columnHeaderEventLACP2.Text = "LAC";
            this.columnHeaderEventLACP2.Width = 80;
            // 
            // columnHeaderEventCIP2
            // 
            this.columnHeaderEventCIP2.Text = "CI";
            this.columnHeaderEventCIP2.Width = 80;
            // 
            // columnHeaderTpKindP2
            // 
            this.columnHeaderTpKindP2.Text = "频点网络类型";
            this.columnHeaderTpKindP2.Width = 120;
            // 
            // columnHeaderArfcnP2
            // 
            this.columnHeaderArfcnP2.Text = "当前频点频点";
            this.columnHeaderArfcnP2.Width = 80;
            // 
            // columnHeaderCpiP2
            // 
            this.columnHeaderCpiP2.Text = "当前频点扰码";
            this.columnHeaderCpiP2.Width = 80;
            // 
            // columnHeaderDPCHP2
            // 
            this.columnHeaderDPCHP2.Text = "当前频点DPCH频点";
            this.columnHeaderDPCHP2.Width = 80;
            // 
            // columnHeaderHSDPA_RSCPP2
            // 
            this.columnHeaderHSDPA_RSCPP2.Text = "当前频点TD_HSDPA_HS_PDSCH_RSCP";
            this.columnHeaderHSDPA_RSCPP2.Width = 80;
            // 
            // columnHeaderHSDPA_CIP2
            // 
            this.columnHeaderHSDPA_CIP2.Text = "当前频点TD_HSDPA_HS_PDSCH_CI";
            this.columnHeaderHSDPA_CIP2.Width = 80;
            // 
            // columnHeaderHSDPA_UarfcnP2
            // 
            this.columnHeaderHSDPA_UarfcnP2.Text = "当前频点TD_HSDPA_HS_WorkUARFCN";
            this.columnHeaderHSDPA_UarfcnP2.Width = 80;
            // 
            // columnHeaderAvgPCCPCH_RSCPP2
            // 
            this.columnHeaderAvgPCCPCH_RSCPP2.Text = "5秒前平均PCCPCH_RSCP";
            this.columnHeaderAvgPCCPCH_RSCPP2.Width = 80;
            // 
            // columnHeaderAvgPCCPCH_C2IP2
            // 
            this.columnHeaderAvgPCCPCH_C2IP2.Text = "5秒前平均PCCPCH_C2I";
            this.columnHeaderAvgPCCPCH_C2IP2.Width = 80;
            // 
            // columnHeaderAvgDPCH_RSCPP2
            // 
            this.columnHeaderAvgDPCH_RSCPP2.Text = "5秒前平均DPCH_RSCP";
            this.columnHeaderAvgDPCH_RSCPP2.Width = 80;
            // 
            // columnHeaderAvgDPCH_C2IP2
            // 
            this.columnHeaderAvgDPCH_C2IP2.Text = "5秒前平均DPCH_C2I";
            this.columnHeaderAvgDPCH_C2IP2.Width = 80;
            // 
            // columnHeaderSwitchTimesP2
            // 
            this.columnHeaderSwitchTimesP2.Text = "十秒前切换次数";
            // 
            // columnHeaderNbcell1ArfcnP2
            // 
            this.columnHeaderNbcell1ArfcnP2.Text = "邻区1主频";
            this.columnHeaderNbcell1ArfcnP2.Width = 80;
            // 
            // columnHeaderNbcell1CpiP2
            // 
            this.columnHeaderNbcell1CpiP2.Text = "邻区1扰码";
            this.columnHeaderNbcell1CpiP2.Width = 80;
            // 
            // columnHeaderNbcell1PCCPCH_RSCPP2
            // 
            this.columnHeaderNbcell1PCCPCH_RSCPP2.Text = "邻区1PCCPCH_RSCP";
            this.columnHeaderNbcell1PCCPCH_RSCPP2.Width = 80;
            // 
            // columnHeaderNbcell2ArfcnP2
            // 
            this.columnHeaderNbcell2ArfcnP2.Text = "邻区2主频";
            this.columnHeaderNbcell2ArfcnP2.Width = 80;
            // 
            // columnHeaderNbcell2CpiP2
            // 
            this.columnHeaderNbcell2CpiP2.Text = "邻区2扰码";
            this.columnHeaderNbcell2CpiP2.Width = 80;
            // 
            // columnHeaderNbcell2PCCPCH_RSCPP2
            // 
            this.columnHeaderNbcell2PCCPCH_RSCPP2.Text = "邻区2PCCPCH_RSCP";
            this.columnHeaderNbcell2PCCPCH_RSCPP2.Width = 80;
            // 
            // columnHeaderNbcell3ArfcnP2
            // 
            this.columnHeaderNbcell3ArfcnP2.Text = "邻区3主频";
            this.columnHeaderNbcell3ArfcnP2.Width = 80;
            // 
            // columnHeaderNbcell3CpiP2
            // 
            this.columnHeaderNbcell3CpiP2.Text = "邻区3扰码";
            this.columnHeaderNbcell3CpiP2.Width = 80;
            // 
            // columnHeaderNbcell3PCCPCH_RSCPP2
            // 
            this.columnHeaderNbcell3PCCPCH_RSCPP2.Text = "邻区3PCCPCH_RSCP";
            this.columnHeaderNbcell3PCCPCH_RSCPP2.Width = 80;
            // 
            // columnHeaderNbcell4ArfcnP2
            // 
            this.columnHeaderNbcell4ArfcnP2.Text = "邻区4主频";
            this.columnHeaderNbcell4ArfcnP2.Width = 80;
            // 
            // columnHeaderNbcell4CpiP2
            // 
            this.columnHeaderNbcell4CpiP2.Text = "邻区4扰码";
            this.columnHeaderNbcell4CpiP2.Width = 80;
            // 
            // columnHeaderNbcell4PCCPCH_RSCPP2
            // 
            this.columnHeaderNbcell4PCCPCH_RSCPP2.Text = "邻区4PCCPCH_RSCP";
            this.columnHeaderNbcell4PCCPCH_RSCPP2.Width = 80;
            // 
            // columnHeaderNbcell5ArfcnP2
            // 
            this.columnHeaderNbcell5ArfcnP2.Text = "邻区5主频";
            this.columnHeaderNbcell5ArfcnP2.Width = 80;
            // 
            // columnHeaderNbcell5CpiP2
            // 
            this.columnHeaderNbcell5CpiP2.Text = "邻区5扰码";
            this.columnHeaderNbcell5CpiP2.Width = 80;
            // 
            // columnHeaderNbcell5PCCPCH_RSCPP2
            // 
            this.columnHeaderNbcell5PCCPCH_RSCPP2.Text = "邻区5PCCPCH_RSCP";
            this.columnHeaderNbcell5PCCPCH_RSCPP2.Width = 80;
            // 
            // contextMenuStrip
            // 
            this.contextMenuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExportExcel});
            this.contextMenuStrip.Name = "contextMenuStrip";
            this.contextMenuStrip.Size = new System.Drawing.Size(130, 26);
            // 
            // miExportExcel
            // 
            this.miExportExcel.Name = "miExportExcel";
            this.miExportExcel.Size = new System.Drawing.Size(129, 22);
            this.miExportExcel.Text = "导出Excel";
            this.miExportExcel.Click += new System.EventHandler(this.miExportExcel_Click);
            // 
            // DropPerceptionForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1109, 577);
            this.ContextMenuStrip = this.contextMenuStrip;
            this.Controls.Add(this.tabControl);
            this.Name = "DropPerceptionForm";
            this.Text = "TD感知掉线";
            this.tabControl.ResumeLayout(false);
            this.tabPage1.ResumeLayout(false);
            this.tabPage2.ResumeLayout(false);
            this.contextMenuStrip.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.TabControl tabControl;
        private System.Windows.Forms.TabPage tabPage1;
        private System.Windows.Forms.ListView listView404;
        private System.Windows.Forms.TabPage tabPage2;
        private System.Windows.Forms.ColumnHeader columnHeaderSN;
        private System.Windows.Forms.ColumnHeader columnHeaderDistrict;
        private System.Windows.Forms.ColumnHeader columnHeaderFileName;
        private System.Windows.Forms.ColumnHeader columnHeaderEventHappenTime;
        private System.Windows.Forms.ColumnHeader columnHeaderEventName;
        private System.Windows.Forms.ColumnHeader columnHeaderEventLongitude;
        private System.Windows.Forms.ColumnHeader columnHeaderEventLatitude;
        private System.Windows.Forms.ColumnHeader columnHeaderEventLAC;
        private System.Windows.Forms.ColumnHeader columnHeaderEventCI;
        private System.Windows.Forms.ColumnHeader columnHeaderTpKind;
        private System.Windows.Forms.ColumnHeader columnHeaderTpLongitude;
        private System.Windows.Forms.ColumnHeader columnHeaderTpLatitude;
        private System.Windows.Forms.ColumnHeader columnHeaderTpLAC;
        private System.Windows.Forms.ColumnHeader columnHeaderTpCI;
        private System.Windows.Forms.ColumnHeader columnHeaderBeginDropTime;
        private System.Windows.Forms.ColumnHeader columnHeaderBeginDropArfcn;
        private System.Windows.Forms.ColumnHeader columnHeaderBeginDropCpi;
        private System.Windows.Forms.ColumnHeader columnHeaderBeginDropDPCH;
        private System.Windows.Forms.ColumnHeader columnHeaderBeginDropHSDA_RSCP;
        private System.Windows.Forms.ColumnHeader columnHeaderBeginDropHSDPA_CI;
        private System.Windows.Forms.ColumnHeader columnHeaderBeginDropHSDPA_Uarfcn;
        private System.Windows.Forms.ColumnHeader columnHeaderAvgPCCPCH_RSCP;
        private System.Windows.Forms.ColumnHeader columnHeaderAvgPCCPCH_C2I;
        private System.Windows.Forms.ColumnHeader columnHeaderAvgDPCH_RSCP;
        private System.Windows.Forms.ColumnHeader columnHeaderAvgDPCH_C2I;
        private System.Windows.Forms.ColumnHeader columnHeaderSwitchTimes;
        private System.Windows.Forms.ColumnHeader columnHeaderNbcell1Arfcn;
        private System.Windows.Forms.ColumnHeader columnHeaderNbcell1Cpi;
        private System.Windows.Forms.ColumnHeader columnHeaderNbcell1PCCPCH_RSCP;
        private System.Windows.Forms.ColumnHeader columnHeaderNbcell2Arfcn;
        private System.Windows.Forms.ColumnHeader columnHeaderNbcell2Cpi;
        private System.Windows.Forms.ColumnHeader columnHeaderNbcell2PCCPCH_RSCP;
        private System.Windows.Forms.ColumnHeader columnHeaderNbcell3Arfcn;
        private System.Windows.Forms.ColumnHeader columnHeaderNbcell3Cpi;
        private System.Windows.Forms.ColumnHeader columnHeaderNbcell3PCCPCH_RSCP;
        private System.Windows.Forms.ColumnHeader columnHeaderNbcell4Arfcn;
        private System.Windows.Forms.ColumnHeader columnHeaderNbcell4Cpi;
        private System.Windows.Forms.ColumnHeader columnHeaderNbcell4PCCPCH_RSCP;
        private System.Windows.Forms.ColumnHeader columnHeaderNbcell5Arfcn;
        private System.Windows.Forms.ColumnHeader columnHeaderNbcell5Cpi;
        private System.Windows.Forms.ColumnHeader columnHeaderNbcell5PCCPCH_RSCP;
        private System.Windows.Forms.ListView listView9893;
        private System.Windows.Forms.ColumnHeader columnHeaderSNP2;
        private System.Windows.Forms.ColumnHeader columnHeaderFileNameP2;
        private System.Windows.Forms.ColumnHeader columnHeaderEventHappenTimeP2;
        private System.Windows.Forms.ColumnHeader columnHeaderEventNameP2;
        private System.Windows.Forms.ColumnHeader columnHeaderEventLongitudeP2;
        private System.Windows.Forms.ColumnHeader columnHeaderEventLatitudeP2;
        private System.Windows.Forms.ColumnHeader columnHeaderEventLACP2;
        private System.Windows.Forms.ColumnHeader columnHeaderEventCIP2;
        private System.Windows.Forms.ColumnHeader columnHeaderTpKindP2;
        private System.Windows.Forms.ColumnHeader columnHeaderArfcnP2;
        private System.Windows.Forms.ColumnHeader columnHeaderCpiP2;
        private System.Windows.Forms.ColumnHeader columnHeaderDPCHP2;
        private System.Windows.Forms.ColumnHeader columnHeaderHSDPA_RSCPP2;
        private System.Windows.Forms.ColumnHeader columnHeaderHSDPA_CIP2;
        private System.Windows.Forms.ColumnHeader columnHeaderHSDPA_UarfcnP2;
        private System.Windows.Forms.ColumnHeader columnHeaderAvgPCCPCH_RSCPP2;
        private System.Windows.Forms.ColumnHeader columnHeaderAvgPCCPCH_C2IP2;
        private System.Windows.Forms.ColumnHeader columnHeaderAvgDPCH_RSCPP2;
        private System.Windows.Forms.ColumnHeader columnHeaderAvgDPCH_C2IP2;
        private System.Windows.Forms.ColumnHeader columnHeaderSwitchTimesP2;
        private System.Windows.Forms.ColumnHeader columnHeaderNbcell1ArfcnP2;
        private System.Windows.Forms.ColumnHeader columnHeaderNbcell1CpiP2;
        private System.Windows.Forms.ColumnHeader columnHeaderNbcell1PCCPCH_RSCPP2;
        private System.Windows.Forms.ColumnHeader columnHeaderNbcell2ArfcnP2;
        private System.Windows.Forms.ColumnHeader columnHeaderNbcell2CpiP2;
        private System.Windows.Forms.ColumnHeader columnHeaderNbcell2PCCPCH_RSCPP2;
        private System.Windows.Forms.ColumnHeader columnHeaderNbcell3ArfcnP2;
        private System.Windows.Forms.ColumnHeader columnHeaderNbcell3CpiP2;
        private System.Windows.Forms.ColumnHeader columnHeaderNbcell3PCCPCH_RSCPP2;
        private System.Windows.Forms.ColumnHeader columnHeaderNbcell4ArfcnP2;
        private System.Windows.Forms.ColumnHeader columnHeaderNbcell4CpiP2;
        private System.Windows.Forms.ColumnHeader columnHeaderNbcell4PCCPCH_RSCPP2;
        private System.Windows.Forms.ColumnHeader columnHeaderNbcell5ArfcnP2;
        private System.Windows.Forms.ColumnHeader columnHeaderNbcell5CpiP2;
        private System.Windows.Forms.ColumnHeader columnHeaderNbcell5PCCPCH_RSCPP2;
        private System.Windows.Forms.ColumnHeader columnHeaderDistrictP2;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip;
        private System.Windows.Forms.ToolStripMenuItem miExportExcel;
    }
}