﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTDIYCellWeakCoverByCellDir_TDLTEScan : ZTDIYCellWeakCoverByCellDir_GScan
    {
        private static ZTDIYCellWeakCoverByCellDir_TDLTEScan intance = null;
        public new static ZTDIYCellWeakCoverByCellDir_TDLTEScan GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new ZTDIYCellWeakCoverByCellDir_TDLTEScan();
                    }
                }
            }
            return intance;
        }

        protected ZTDIYCellWeakCoverByCellDir_TDLTEScan()
            : base()
        {
            isAddSampleToDTDataManager = false;
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_SCAN_TOPN);
            carrierID = CarrierType.ChinaMobile;
        }

        public override string Name
        {
            get { return "弱覆盖小区_TDLTE扫频"; }
        }
        
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22002, this.Name);
        }

        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            List<object> columnsDef = new List<object>();

            Dictionary<string, object> param = new Dictionary<string, object>();
            param["param_name"] = "LTESCAN_TopN_CELL_Specific_RSRP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            Dictionary<string, object> tmpDic = new Dictionary<string, object>();
            tmpDic.Add("name", (object)"TDLTE扫频");
            tmpDic.Add("themeName", (object)"LTESCAN_TopN_CELL_Specific_RSRP");
            tmpDic.Add("columnsDef", (object)columnsDef);

            DIYSampleGroup group = new DIYSampleGroup();
            group.Param = tmpDic;
            return group;
        }

        protected override float? getBestRxLev(TestPoint tp)
        {
            if (tp is ScanTestPoint_LTE)
            {
                float? rxLev = (float?)tp["LTESCAN_TopN_CELL_Specific_RSRP", 0];
                if (rxLev == null || rxLev < -120 || rxLev > -10)
                {
                    return null;
                }
                return rxLev;
            }
            return null;
        }

        protected override void getWeakCellDic(List<GridForCellWeakCover> weakGridList, Dictionary<ICell, CellWeakCoverByGridInfoBase> weakCellDic,
            double lonDiff, double latDiff)
        {
            List<LTECell> cellList = getLTECellsOfRegion();
            int iLoop = 1;
            WaitBox.Text = "开始获取弱覆盖小区...";
            foreach (LTECell cell in cellList)
            {
                dealWeakCellByWeakGrid(weakGridList, weakCellDic, lonDiff, latDiff, cell);
                WaitBox.ProgressPercent = (int)(100.0 * iLoop++ / cellList.Count);
            }
        }

        protected override CellWeakCoverByGridInfoBase getCellWeakCoverByGrid(ICell cell)
        {
            if (cell is LTECell)
            {
                LTECell lteCell = cell as LTECell;
                CellWeakCoverByGridInfoLte weakCell = new CellWeakCoverByGridInfoLte();
                weakCell.FillCellData(lteCell);
                //weakCell.Calculate();
                return weakCell;
            }
            return null;
        }

        protected virtual List<LTECell> getLTECellsOfRegion()
        {
            List<LTECell> cellList = new List<LTECell>();
            int index = 1;
            List<LTECell> curCells = MainModel.CellManager.GetCurrentLTECells();
            WaitBox.Text = "开始获取选择区域内小区...";
            foreach (LTECell cell in curCells)
            {
                if (cell.Type == LTEBTSType.Outdoor && condition.Geometorys.GeoOp.Contains(cell.Longitude, cell.Latitude))
                {
                    cellList.Add(cell);
                }
                WaitBox.ProgressPercent = (int)(100.0 * index++ / curCells.Count);
            }
            return cellList;
        }

        #region Background
        public override BackgroundFuncType FuncType
        {
            get { return BackgroundFuncType.LTE扫频专题; }
        }
        #endregion
    }
}
