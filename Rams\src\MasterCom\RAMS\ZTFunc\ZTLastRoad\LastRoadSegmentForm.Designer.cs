﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class LastRoadSegmentForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.lv = new BrightIdeasSoftware.TreeListView();
            this.colSN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colRoadNames = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colTestPointCnt = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colStaySecond = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colDistance = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colCell2TpDisAvg = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colLng = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colLat = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colFile = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colTime = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.cms = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExpandAll = new System.Windows.Forms.ToolStripMenuItem();
            this.miCollapseAll = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator1 = new System.Windows.Forms.ToolStripSeparator();
            this.miExport = new System.Windows.Forms.ToolStripMenuItem();
            this.colCellsToken = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.miExportLevelCell = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportLevelRoad = new System.Windows.Forms.ToolStripMenuItem();
            ((System.ComponentModel.ISupportInitialize)(this.lv)).BeginInit();
            this.cms.SuspendLayout();
            this.SuspendLayout();
            // 
            // lv
            // 
            this.lv.AllColumns.Add(this.colSN);
            this.lv.AllColumns.Add(this.colRoadNames);
            this.lv.AllColumns.Add(this.colCellsToken);
            this.lv.AllColumns.Add(this.colTestPointCnt);
            this.lv.AllColumns.Add(this.colStaySecond);
            this.lv.AllColumns.Add(this.colDistance);
            this.lv.AllColumns.Add(this.colCell2TpDisAvg);
            this.lv.AllColumns.Add(this.colLng);
            this.lv.AllColumns.Add(this.colLat);
            this.lv.AllColumns.Add(this.colFile);
            this.lv.AllColumns.Add(this.colTime);
            this.lv.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.colSN,
            this.colRoadNames,
            this.colCellsToken,
            this.colTestPointCnt,
            this.colStaySecond,
            this.colDistance,
            this.colCell2TpDisAvg,
            this.colLng,
            this.colLat,
            this.colFile,
            this.colTime});
            this.lv.ContextMenuStrip = this.cms;
            this.lv.Cursor = System.Windows.Forms.Cursors.Default;
            this.lv.Dock = System.Windows.Forms.DockStyle.Fill;
            this.lv.FullRowSelect = true;
            this.lv.GridLines = true;
            this.lv.HeaderWordWrap = true;
            this.lv.IsNeedShowOverlay = false;
            this.lv.Location = new System.Drawing.Point(0, 0);
            this.lv.Name = "lv";
            this.lv.OwnerDraw = true;
            this.lv.ShowGroups = false;
            this.lv.Size = new System.Drawing.Size(1080, 449);
            this.lv.TabIndex = 3;
            this.lv.UseCompatibleStateImageBehavior = false;
            this.lv.View = System.Windows.Forms.View.Details;
            this.lv.VirtualMode = true;
            this.lv.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.lv_MouseDoubleClick);
            // 
            // colSN
            // 
            this.colSN.HeaderFont = null;
            this.colSN.Text = "序号";
            // 
            // colRoadNames
            // 
            this.colRoadNames.HeaderFont = null;
            this.colRoadNames.Text = "道路/小区";
            this.colRoadNames.Width = 105;
            // 
            // colTestPointCnt
            // 
            this.colTestPointCnt.HeaderFont = null;
            this.colTestPointCnt.Text = "采样点个数";
            this.colTestPointCnt.Width = 44;
            // 
            // colStaySecond
            // 
            this.colStaySecond.HeaderFont = null;
            this.colStaySecond.Text = "持续时间（秒）";
            this.colStaySecond.Width = 58;
            // 
            // colDistance
            // 
            this.colDistance.HeaderFont = null;
            this.colDistance.Text = "持续距离（米）";
            this.colDistance.Width = 59;
            // 
            // colCell2TpDisAvg
            // 
            this.colCell2TpDisAvg.HeaderFont = null;
            this.colCell2TpDisAvg.Text = "小区与采样点平均距离（米）";
            this.colCell2TpDisAvg.Width = 98;
            // 
            // colLng
            // 
            this.colLng.HeaderFont = null;
            this.colLng.Text = "经度(中心点)";
            this.colLng.Width = 106;
            // 
            // colLat
            // 
            this.colLat.HeaderFont = null;
            this.colLat.Text = "纬度(中心点)";
            this.colLat.Width = 119;
            // 
            // colFile
            // 
            this.colFile.HeaderFont = null;
            this.colFile.Text = "文件名";
            this.colFile.Width = 238;
            // 
            // colTime
            // 
            this.colTime.AspectName = "";
            this.colTime.HeaderFont = null;
            this.colTime.Text = "时间";
            this.colTime.Width = 206;
            // 
            // cms
            // 
            this.cms.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExpandAll,
            this.miCollapseAll,
            this.toolStripSeparator1,
            this.miExport,
            this.miExportLevelCell,
            this.miExportLevelRoad});
            this.cms.Name = "contextMenuStrip1";
            this.cms.Size = new System.Drawing.Size(187, 142);
            // 
            // miExpandAll
            // 
            this.miExpandAll.Name = "miExpandAll";
            this.miExpandAll.Size = new System.Drawing.Size(186, 22);
            this.miExpandAll.Text = "展开所有节点";
            this.miExpandAll.Click += new System.EventHandler(this.miExpandAll_Click);
            // 
            // miCollapseAll
            // 
            this.miCollapseAll.Name = "miCollapseAll";
            this.miCollapseAll.Size = new System.Drawing.Size(186, 22);
            this.miCollapseAll.Text = "折叠所有节点";
            this.miCollapseAll.Click += new System.EventHandler(this.miCollapseAll_Click);
            // 
            // toolStripSeparator1
            // 
            this.toolStripSeparator1.Name = "toolStripSeparator1";
            this.toolStripSeparator1.Size = new System.Drawing.Size(183, 6);
            // 
            // miExport
            // 
            this.miExport.Name = "miExport";
            this.miExport.Size = new System.Drawing.Size(186, 22);
            this.miExport.Text = "导出Excel（全部）...";
            this.miExport.Click += new System.EventHandler(this.miExport_Click);
            // 
            // colCellsToken
            // 
            this.colCellsToken.HeaderFont = null;
            this.colCellsToken.Text = "涉及小区标记";
            // 
            // miExportLevelCell
            // 
            this.miExportLevelCell.Name = "miExportLevelCell";
            this.miExportLevelCell.Size = new System.Drawing.Size(186, 22);
            this.miExportLevelCell.Text = "导出Excel（小区）...";
            this.miExportLevelCell.Click += new System.EventHandler(this.miExportLevelCell_Click);
            // 
            // miExportLevelRoad
            // 
            this.miExportLevelRoad.Name = "miExportLevelRoad";
            this.miExportLevelRoad.Size = new System.Drawing.Size(186, 22);
            this.miExportLevelRoad.Text = "导出Excel（道路）...";
            this.miExportLevelRoad.Click += new System.EventHandler(this.miExportLevelRoad_Click);
            // 
            // LastRoadSegmentForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1080, 449);
            this.Controls.Add(this.lv);
            this.Name = "LastRoadSegmentForm";
            this.ShowInTaskbar = false;
            this.Text = "持续路段";
            ((System.ComponentModel.ISupportInitialize)(this.lv)).EndInit();
            this.cms.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private BrightIdeasSoftware.TreeListView lv;
        private BrightIdeasSoftware.OLVColumn colSN;
        private BrightIdeasSoftware.OLVColumn colTestPointCnt;
        private BrightIdeasSoftware.OLVColumn colLng;
        private BrightIdeasSoftware.OLVColumn colLat;
        private BrightIdeasSoftware.OLVColumn colFile;
        private BrightIdeasSoftware.OLVColumn colTime;
        private BrightIdeasSoftware.OLVColumn colRoadNames;
        private System.Windows.Forms.ContextMenuStrip cms;
        private System.Windows.Forms.ToolStripMenuItem miExpandAll;
        private System.Windows.Forms.ToolStripMenuItem miCollapseAll;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator1;
        private System.Windows.Forms.ToolStripMenuItem miExport;
        private BrightIdeasSoftware.OLVColumn colStaySecond;
        private BrightIdeasSoftware.OLVColumn colDistance;
        private BrightIdeasSoftware.OLVColumn colCell2TpDisAvg;
        private BrightIdeasSoftware.OLVColumn colCellsToken;
        private System.Windows.Forms.ToolStripMenuItem miExportLevelCell;
        private System.Windows.Forms.ToolStripMenuItem miExportLevelRoad;
    }
}