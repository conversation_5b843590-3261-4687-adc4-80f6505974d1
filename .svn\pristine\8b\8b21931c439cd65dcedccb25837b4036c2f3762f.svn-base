﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause
{
    [Serializable]
    public class QualCause : CauseBase
    {
        public QualCause()
        {
            AddSubReason(new PoorSINRCause());
            AddSubReason(new PoorBLERCause());
        }

        public override string Name
        {
            get { return "质量"; }
        }

        public override string Desc
        {
            get { return "质量引起的低速率"; }
        }

        public override string Suggestion
        {
            get { return "未知质量原因"; }
        }

        public override void Judge(LowSpeedSeg segItem, List<Model.Event> evts, List<TestPoint> allTP)
        {
            foreach (CauseBase r in SubCauses)
            {
                if (segItem.NeedJudge)
                {
                    r.<PERSON>(segItem, evts,allTP);
                }
                else
                {
                    break;
                }
            }
        }

        public override Dictionary<string, object> CfgParam
        {
            get
            {
                Dictionary<string, object> paramDic = new Dictionary<string, object>();
                paramDic["TypeName"] = this.GetType().FullName;
                List<object> list = new List<object>();
                foreach (CauseBase cause in SubCauses)
                {
                    list.Add(cause.CfgParam);
                }
                paramDic["SubCauseSet"] = list;

                return paramDic;

            }
            set
            {
                if (value == null)
                {
                    return;
                }

                SubCauses = new List<CauseBase>();
                List<object> list = value["SubCauseSet"] as List<object>;
                foreach (object item in list)
                {
                    Dictionary<string, object> dic = item as Dictionary<string, object>;
                    string typeName = dic["TypeName"].ToString();
                    System.Reflection.Assembly assembly = System.Reflection.Assembly.GetExecutingAssembly();
                    CauseBase cause = (CauseBase)assembly.CreateInstance(typeName);
                    cause.CfgParam = dic;
                    AddSubReason(cause);
                }
            }
        }
    }
}
