﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

using DevExpress.XtraGrid.Columns;

using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.MTGis;
using MasterCom.Util;

namespace MasterCom.RAMS.Func
{
    public partial class RoadInfoForm : MinCloseForm
    {
        public RoadInfoForm(MainModel mainModel) : base(mainModel) 
        {
            InitializeComponent();
            miExportExcel.Click += MiExportExcel_Click;
            gridView1.DoubleClick += GridView_DoubleClick;
        }

        public string FormCaption
        {
            get { return this.Text; }
            set { this.Text = value; }
        }

        public void RebuildColumns(GridColumn[] columns)
        {
            gridView1.Columns.Clear();
            for (int i = 0; i < columns.Length; ++i)
            {
                columns[i].Visible = true;
                columns[i].VisibleIndex = i;
            }
            gridView1.Columns.AddRange(columns);
        }

        public void FillData(object roadList)
        {
            gridControl1.DataSource = roadList;
            gridControl1.RefreshDataSource();
        }

        private void MiExportExcel_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(gridView1);
        }

        private void GridView_DoubleClick(object sender, EventArgs e)
        {
            RoadInfoViewBase roadView = gridView1.GetRow(gridView1.GetSelectedRows()[0]) as RoadInfoViewBase;
            if (roadView == null)
            {
                return;
            }

            MainModel.DTDataManager.Clear();
            foreach (TestPoint tp in roadView.RoadPoints)
            {
                MainModel.DTDataManager.Add(tp);
            }
            MainModel.FireDTDataChanged(MainModel.MainForm);
            MainModel.MainForm.GetMapForm().GoToView(roadView.CentLng, roadView.CentLat);

            OutlineOfRoad outRoad = new OutlineOfRoad();
            outRoad.SetPoints(roadView.RoadPoints);
            TempLayer.Instance.Draw(outRoad.Drawer);
        }
    }
}
