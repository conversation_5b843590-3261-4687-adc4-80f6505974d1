﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTReportEventStatInfo
    {
        public string NetType { get; set; }
        public string EventType { get; set; }
        public int EventCount { get; set; }
        public int EventFinishedCount { get; set; }
        public int EventUnFinishedCount { get; set; }
        //已完成处理的事件中，各类原因的数量
        public Dictionary<string, int> ReasonDic { get; set; }

        public ZTReportEventStatInfo(string netType, string eventType)
        {
            this.NetType = netType;
            this.EventType = eventType;

            this.EventCount = 0;
            this.EventFinishedCount = 0;
            this.EventUnFinishedCount = 0;
            this.ReasonDic = new Dictionary<string, int>();
        }

        internal void Merge(ZTReportEventInfo_BJ eventInfo)
        {
            this.EventCount++;
            if (eventInfo.MethodDeal.Length > 0)
            {
                this.EventFinishedCount++;

                if (this.ReasonDic.ContainsKey(eventInfo.CauseDeal))
                {
                    this.ReasonDic[eventInfo.CauseDeal]++;  //原因值累加
                }
                else
                {
                    this.ReasonDic.Add(eventInfo.CauseDeal, 1); //新增原因值
                }
            }
            else
            {
                this.EventUnFinishedCount++;
            }

        }
    }

    public class ZTReportEventStatProfile
    {
        public string AreaName { get; set; }
        public string GroupName { get; set; }
        public string BeginTime { get; set; }
        public string EndTime { get; set; }
        public int EventCount { get; set; }
        public int EventFinishedCount { get; set; }
        public int EventUnFinishedCount { get; set; }
        public double EventFinishedPercent { get; set; }
        public int DropCount { get; set; }
        public int DropFinishedCount { get; set; }
        public int DropUnFinishedCount { get; set; }
        public double DropFinishedPercent { get; set; }
        public int BlockCount { get; set; }
        public int BlockFinishedCount { get; set; }
        public int BlockUnFinishedCount { get; set; }
        public double BlockFinishedPercent { get; set; }

        public ZTReportEventStatProfile(string areaName, string groupName, DateTime beginTime, DateTime endTime)
        {
            this.AreaName = areaName;
            this.GroupName = groupName;
            this.BeginTime = beginTime.ToString("yyyy-MM-dd HH:mm:ss");
            this.EndTime = endTime.ToString("yyyy-MM-dd HH:mm:ss");

            this.EventCount = 0;
            this.EventFinishedCount = 0;
            this.EventUnFinishedCount = 0;
            this.EventFinishedPercent = 0;

            this.DropCount = 0;
            this.DropFinishedCount = 0;
            this.DropUnFinishedCount = 0;
            this.DropFinishedPercent = 0;

            this.BlockCount = 0;
            this.BlockFinishedCount = 0;
            this.BlockUnFinishedCount = 0;
            this.BlockFinishedPercent = 0;
        }

        internal void fillCount(ZTReportEventStatInfo statInfo)
        {
            this.EventCount += statInfo.EventCount;
            this.EventFinishedCount += statInfo.EventFinishedCount;
            this.EventUnFinishedCount += statInfo.EventUnFinishedCount;

            if (statInfo.EventType == "掉话")
            {
                this.DropCount += statInfo.EventCount;
                this.DropFinishedCount += statInfo.EventFinishedCount;
                this.DropUnFinishedCount += statInfo.EventUnFinishedCount;
            }
            else if (statInfo.EventType == "未接通")
            {
                this.BlockCount += statInfo.EventCount;
                this.BlockFinishedCount += statInfo.EventFinishedCount;
                this.BlockUnFinishedCount += statInfo.EventUnFinishedCount;
            }
        }
    }

    public class ZTReportEventStatReason
    {
        public string AreaName { get; set; }
        public string BeginTime { get; set; }
        public string EndTime { get; set; }
        public string ReasonName { get; set; }
        public int ReasonCount { get; set; }
        public double ReasonPercent { get; set; }

        public ZTReportEventStatReason(string areaName, DateTime beginTime, DateTime endTime, string reasonName, int reasonCount, int eventCount)
        {
            this.AreaName = areaName;
            this.BeginTime = beginTime.ToString("yyyy-MM-dd HH:mm:ss");
            this.EndTime = endTime.ToString("yyyy-MM-dd HH:mm:ss");
            this.ReasonName = reasonName;
            this.ReasonCount = reasonCount;

            if(eventCount > 0)
            {
                this.ReasonPercent = Math.Round(100*(double)reasonCount/(double)eventCount,2);
            }
            else
            {
                this.ReasonPercent = 0;
            }
        }

        public static List<ZTReportEventStatReason> GetReasonList(string areaName, DateTime beginTime, DateTime endTime, ZTReportEventStatInfo statInfo)
        {
            List<ZTReportEventStatReason> reasonList = new List<ZTReportEventStatReason>();

            foreach(string reason in statInfo.ReasonDic.Keys)
            {
                ZTReportEventStatReason statItem = new ZTReportEventStatReason(areaName, beginTime, endTime, reason, statInfo.ReasonDic[reason], statInfo.EventFinishedCount);
                reasonList.Add(statItem);
            }

            return reasonList;
        }
    }
}
