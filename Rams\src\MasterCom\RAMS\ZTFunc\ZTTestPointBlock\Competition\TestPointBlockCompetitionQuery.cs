﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc.ZTTestPointBlock.Competition;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class TestPointBlockCompetitionQuery : DIYAnalyseByPeriodBackgroundBase_Sample
    {
        private static TestPointBlockCompetitionQuery intance = null;
        protected static readonly object lockObj = new object();
        public static TestPointBlockCompetitionQuery GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new TestPointBlockCompetitionQuery();
                    }
                }
            }
            return intance;
        }

        protected TestPointBlockCompetitionQuery()
            : base(MainModel.GetInstance())
        {
            isAddSampleToDTDataManager = false;
        }

        /// <summary>
        /// 由于是竞比，时间，运营商，项目类型等条件，都有2个，在功能点内弹窗设置条件，而不用主界面上的条件。
        /// </summary>
        public override bool IsNeedSetQueryCondition
        {
            get
            {
                return false;
            }
        }

        public override string Name
        {
            get { return "区域采样点汇聚竞比"; }
        }

        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 20016, this.Name);
        }
        protected override void query()
        {
            if (MainModel.IsBackground && !MainModel.BackgroundStarted)
            {
                return;
            }
            if (!getConditionBeforeQuery())
            {
                return;
            }
            WaitBox.CanCancel = true;
            if (!MainModel.IsBackground && !MainModel.QueryFromBackground)
            {
                WaitBox.Text = "正在查询...";
            }
            hostBlocks = new List<TestPointBlock>();
            guestBlocks = new List<TestPointBlock>();
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
                MainModel.ClearDTData();
                if (!MainModel.IsBackground)
                {
                    if (!MainModel.QueryFromBackground)
                    {
                        WaitBox.Show("开始统计数据...", queryInThread, clientProxy);
                        //if (curSelDIYSampleGroup.themeName != null)
                        //{
                        //    MainModel.FireSetDefaultMapSerialTheme(curSelDIYSampleGroup.themeName);
                        //}
                        FireShowFormAfterQuery();
                        MainModel.FireDTDataChanged(this);
                    }
                    else
                    {
                        getBackgroundData();
                        initBackgroundImageDesc();
                    }
                }
                else
                {
                    doBackgroundStatByPeriod(clientProxy);
                }
            }
            finally
            {
                clientProxy.Close();
            }
        }

        private bool isHostCurrent = true;
        protected override void queryInThread(object o)
        {
            try
            {
                doSomethingBeforeQueryInThread();
                ClientProxy clientProxy = (ClientProxy)o;
                queryOnePart(clientProxy, true);
                queryOnePart(clientProxy, false);
                getResultAfterQuery();
            }
            catch (Exception ex)
            {
                System.Console.WriteLine(ex);
            }
            finally
            {
                WaitBox.Close();
            }
        }

        private void queryOnePart(ClientProxy proxy, bool isHostPart)
        {
            isHostCurrent = isHostPart;
            prepareCondition(isHostPart);
            foreach (TimePeriod period in Condition.Periods)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                WaitBox.Text = string.Format("正在查询{0}信息...", isHostPart ? "主队" : "客队");
                queryPeriodInfo(proxy, proxy.Package, period, false);
            }
        }

        private void prepareCondition(bool isHost)
        {
            prepareTestPointParamNeed(isHost);
            condition = new QueryCondition();
            condition.Geometorys = MainModel.SearchGeometrys;
            condition.Periods.Clear();
            condition.CarrierTypes.Clear();
            if (isHost)
            {
                condition.CarrierTypes.Add(report.HostCarrierType);
                condition.Periods.Add(new TimePeriod(report.HostBeginTime, report.HostEndTime));
                condition.Projects = report.HostProjIDList;
                condition.ServiceTypes = report.HostServiceTypeList;
            }
            else
            {
                condition.CarrierTypes.Add(report.GuestCarrierType);
                condition.Periods.Add(new TimePeriod(report.GuestBeginTime, report.GuestEndTime));
                condition.Projects = report.GuestProjIDList;
                condition.ServiceTypes = report.GuestServiceTypeList;
            }
        }

        /// <summary>
        /// 准备好curSelDIYSampleGroup
        /// </summary>
        /// <param name="isHost"></param>
        private void prepareTestPointParamNeed(bool isHost)
        {
            curSelDIYSampleGroup = new DIYSampleGroup();
            List<DTParameter> paramSet = report.GetNeedParam(isHost);
            foreach (DTParameter param in paramSet)
            {
                curSelDIYSampleGroup.ColumnsDefSet.Add(new DIYSampleParamDef(param));
            }
        }

        protected override void getResultAfterQuery()
        {
            List<TestPointBlock> blk2Remove = new List<TestPointBlock>();
            foreach (TestPointBlock blk in hostBlocks)
            {
                if (blk.TestPointCount<report.MinTestPointCnt)
                {
                    blk2Remove.Add(blk);
                    continue;
                }
                blk.Grid = getMainGridName(blk);
            }
            foreach (TestPointBlock blk in blk2Remove)
            {
                hostBlocks.Remove(blk);
            }
            blk2Remove.Clear();
            foreach (TestPointBlock blk in guestBlocks)
            {
                if (blk.TestPointCount < report.MinTestPointCnt)
                {
                    blk2Remove.Add(blk);
                    continue;
                }
                blk.Grid = getMainGridName(blk);   
            }
            foreach (TestPointBlock blk in blk2Remove)
            {
                guestBlocks.Remove(blk);
            }
            overlapBlocks = analyseOverlapBlock();
        }

        private string getMainGridName(TestPointBlock blk)
        {
            string grid = "";
            Dictionary<string, int> dicGrid = new Dictionary<string, int>();

            string currentGrid = "";
            foreach (TestPoint tp in blk.TestPoints)
            {
                currentGrid = GISManager.GetInstance().GetGridDesc(tp.Longitude, tp.Latitude);
                if (!dicGrid.ContainsKey(currentGrid))
                {
                    dicGrid.Add(currentGrid, 1);
                }
                else
                {
                    dicGrid[currentGrid] += 1;
                }
            }
            if (dicGrid.Count <= 1)
            {
                grid = currentGrid;
            }
            else//采样点中有多个网格时，取占比最高的那个网格
            {
                List<KeyValuePair<string, int>> lst = new List<KeyValuePair<string, int>>(dicGrid);
                lst.Sort(delegate(KeyValuePair<string, int> s1, KeyValuePair<string, int> s2)
                {
                    return s2.Value.CompareTo(s1.Value);
                });
                grid = lst[0].Key;
            }
            dicGrid.Clear();
            return grid;
        }

        /// <summary>
        /// 主客队汇聚块分析
        /// </summary>
        private List<TestPointBlock> analyseOverlapBlock()
        {
            List<TestPointBlock> overlapBlockList = new List<TestPointBlock>();
            foreach (TestPointBlock hostBlk in hostBlocks)
            {
                foreach (TestPointBlock guestBlk in guestBlocks)
                {
                    foreach (TestPoint tp in guestBlk.TestPoints)
                    {
                        if (hostBlk.CanCollect(tp, report.BlockRadius))
                        {
                            TestPointBlock block = new TestPointBlock();
                            block.Join(hostBlk);
                            block.Join(guestBlk);
                            block.Grid = getMainGridName(block);
                            overlapBlockList.Add(block);
                            guestBlk.Overlap = true;
                            hostBlk.Overlap = true;
                            break;
                        }
                    }
                }
            }
            return overlapBlockList;
        }

        TestPointBlockReport report = null;
        protected override bool getConditionBeforeQuery()
        {
            CompetitionSettingDlg dlg = new CompetitionSettingDlg(MainModel);
            if (dlg.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                report = dlg.CurSelReport;
                return report != null;
            }
            return false;
        }

        List<TestPointBlock> hostBlocks = new List<TestPointBlock>();
        List<TestPointBlock> guestBlocks = new List<TestPointBlock>();
        List<TestPointBlock> overlapBlocks = new List<TestPointBlock>();
        protected override void doWithDTData(TestPoint tp)
        {
            TestPointConvergedCondition tpCondition = null;
            List<TPBlockDisplayColumn> cols = null;
            List<TestPointBlock> blocks = null;
            if (isHostCurrent)
            {
                tpCondition = report.HostTestPointCondition;
                cols = report.HostColumns;
                blocks = hostBlocks;
            }
            else
            {
                tpCondition = report.GuestTestPointCondition;
                cols = report.GuestColumns;
                blocks = guestBlocks;
            }
            if (!tpCondition.IsMatched(tp))
            {
                return;
            }
            List<TestPointBlock> canMergeBlocks = new List<TestPointBlock>();
            foreach (TestPointBlock block in blocks)
            {
                if (block.CanCollect(tp, report.BlockRadius))
                {
                    canMergeBlocks.Add(block);
                }
            }
            if (canMergeBlocks.Count == 0)
            {
                TestPointBlock newBlk = new TestPointBlock();
                newBlk.CollectPoint(tp, cols);
                blocks.Add(newBlk);
            }
            else
            {
                TestPointBlock firstBlk = canMergeBlocks[0];
                firstBlk.CollectPoint(tp, cols);
                for (int i = 1; i < canMergeBlocks.Count; i++)
                {
                    TestPointBlock otherBlk = canMergeBlocks[i];
                    firstBlk.Join(otherBlk);
                    blocks.Remove(otherBlk);
                }
            }
        }

        protected override void FireShowFormAfterQuery()
        {
            if (hostBlocks.Count == 0 && guestBlocks.Count == 0)
            {
                System.Windows.Forms.MessageBox.Show("没有符合汇聚条件的采样点！");
                return;
            }
            ResultForm frm = MainModel.CreateResultForm(typeof(ResultForm)) as ResultForm;
            frm.FillData(report, hostBlocks, guestBlocks, overlapBlocks);
            frm.Visible = true;
            frm.BringToFront();
        }

    }

}
