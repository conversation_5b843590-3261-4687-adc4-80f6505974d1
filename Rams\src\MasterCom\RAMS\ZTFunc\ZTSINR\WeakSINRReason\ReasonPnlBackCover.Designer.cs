﻿namespace MasterCom.RAMS.ZTFunc.ZTSINR.WeakSINRReason
{
    partial class ReasonPnlBackCover
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.label3 = new System.Windows.Forms.Label();
            this.numDirectionDif = new DevExpress.XtraEditors.SpinEdit();
            this.label2 = new System.Windows.Forms.Label();
            this.numCell2TpDis = new DevExpress.XtraEditors.SpinEdit();
            this.label1 = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            ((System.ComponentModel.ISupportInitialize)(this.grp)).BeginInit();
            this.grp.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numDirectionDif.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numCell2TpDis.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // grp
            // 
            this.grp.Controls.Add(this.numCell2TpDis);
            this.grp.Controls.Add(this.label1);
            this.grp.Controls.Add(this.label4);
            this.grp.Controls.Add(this.numDirectionDif);
            this.grp.Controls.Add(this.label2);
            this.grp.Controls.Add(this.label3);
            this.grp.Size = new System.Drawing.Size(606, 68);
            this.grp.Text = "背向覆盖";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(36, 37);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(113, 12);
            this.label3.TabIndex = 0;
            this.label3.Text = "方位角 ≥ 主瓣正负";
            // 
            // numDirectionDif
            // 
            this.numDirectionDif.EditValue = new decimal(new int[] {
            90,
            0,
            0,
            0});
            this.numDirectionDif.Location = new System.Drawing.Point(152, 32);
            this.numDirectionDif.Name = "numDirectionDif";
            this.numDirectionDif.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numDirectionDif.Properties.MaxValue = new decimal(new int[] {
            360,
            0,
            0,
            0});
            this.numDirectionDif.Properties.MinValue = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numDirectionDif.Size = new System.Drawing.Size(75, 21);
            this.numDirectionDif.TabIndex = 2;
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(233, 37);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(17, 12);
            this.label2.TabIndex = 0;
            this.label2.Text = "度";
            // 
            // numCell2TpDis
            // 
            this.numCell2TpDis.EditValue = new decimal(new int[] {
            500,
            0,
            0,
            0});
            this.numCell2TpDis.Location = new System.Drawing.Point(433, 32);
            this.numCell2TpDis.Name = "numCell2TpDis";
            this.numCell2TpDis.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numCell2TpDis.Properties.MaxValue = new decimal(new int[] {
            20000,
            0,
            0,
            0});
            this.numCell2TpDis.Properties.MinValue = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numCell2TpDis.Size = new System.Drawing.Size(75, 21);
            this.numCell2TpDis.TabIndex = 5;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(514, 37);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(17, 12);
            this.label1.TabIndex = 3;
            this.label1.Text = "米";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(302, 37);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(125, 12);
            this.label4.TabIndex = 4;
            this.label4.Text = "小区与采样点距离 ≥ ";
            // 
            // ReasonPnlBackCover
            // 
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.Name = "ReasonPnlBackCover";
            this.Size = new System.Drawing.Size(606, 68);
            ((System.ComponentModel.ISupportInitialize)(this.grp)).EndInit();
            this.grp.ResumeLayout(false);
            this.grp.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numDirectionDif.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numCell2TpDis.Properties)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.Label label3;
        private DevExpress.XtraEditors.SpinEdit numDirectionDif;
        private System.Windows.Forms.Label label2;
        private DevExpress.XtraEditors.SpinEdit numCell2TpDis;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Label label4;
    }
}
