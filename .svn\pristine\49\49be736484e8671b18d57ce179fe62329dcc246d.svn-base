﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRVoiceAnaByFreqBandHelper : VoiceAnaByFreqBandHelperBase
    {
        protected override VoiceAnaByFreqBandResult initRes(string str)
        {
            return new NRVoiceAnaByFreqBandResult(str);
        }

        protected override bool intEvtList(int moMtFlag)
        {
            if (moMtFlag == 1)
            {
                CallRequestEvtList.Add((int)NREventManager.VoLTE_Audio_MO_Call_Attempt);
                CallEstablishedEvtList.Add((int)NREventManager.VoLTE_Audio_MO_Call_Established);
                DropCallEvtList.Add((int)NREventManager.VoLTE_Audio_MO_Drop_Call);
            }
            else if (moMtFlag == 2)
            {
                CallRequestEvtList.Add((int)NREventManager.VoLTE_Audio_MT_Call_Attempt);
                CallEstablishedEvtList.Add((int)NREventManager.VoLTE_Audio_MT_Call_Established);
                DropCallEvtList.Add((int)NREventManager.VoLTE_Audio_MT_Drop_Call);
            }
            else
            {
                return false;
            }
            return true;
        }

        protected override int getEarfcn(TestPoint tp)
        {
            int earfcn = 0;
            int? nrEarfcn = (int?)NRTpHelper.NrLteTpManager.GetEARFCN(tp);
            if (nrEarfcn != null)
            {
                earfcn = (int)nrEarfcn;
            }
            return earfcn;
        }

        protected override float? getRsrp(TestPoint tp)
        {
            float? rsrp = NRTpHelper.NrLteTpManager.GetSCellRsrp(tp);
            if (rsrp != null && rsrp > -150 && rsrp < 50)
            {
                return rsrp;
            }
            return null;
        }

        protected override float? getSinr(TestPoint tp)
        {
            float? sinr = NRTpHelper.NrLteTpManager.GetSCellSinr(tp);
            if (sinr != null && sinr > -50 && sinr < 50)
            {
                return sinr;
            }
            return null;
        }

        public NRVoiceAnaByFreqBandFileResult GetResult(string fileName)
        {
            if (resDic == null || resDic.Values.Count <= 1)
            {
                return null;
            }

            NRVoiceAnaByFreqBandFileResult fileRes = new NRVoiceAnaByFreqBandFileResult();
            fileRes.FileName = fileName;
            foreach (var res in resDic.Values)
            {
                if (res is NRVoiceAnaByFreqBandResult)
                {
                    fileRes.ResList.Add(res as NRVoiceAnaByFreqBandResult);
                }
            }
            return fileRes;
        }

        public NRVoiceAnaByFreqBandFileResult StatTotalResult(List<NRVoiceAnaByFreqBandFileResult> fileResList)
        {
            NRVoiceAnaByFreqBandFileResult totalFileRes = new NRVoiceAnaByFreqBandFileResult();
            totalFileRes.FileName = "总体";

            Dictionary<string, NRVoiceAnaByFreqBandResult> resDic = new Dictionary<string, NRVoiceAnaByFreqBandResult>
            {
                { "A", new NRVoiceAnaByFreqBandResult("A") },
                { "D", new NRVoiceAnaByFreqBandResult("D") },
                { "E", new NRVoiceAnaByFreqBandResult("E") },
                { "F", new NRVoiceAnaByFreqBandResult("F") },
                { "FDD900", new NRVoiceAnaByFreqBandResult("FDD900") },
                { "FDD1800", new NRVoiceAnaByFreqBandResult("FDD1800") },
                { "总体", new NRVoiceAnaByFreqBandResult("总体") }
            };

            foreach (var fileRes in fileResList)
            {
                foreach (var res in fileRes.ResList)
                {
                    NRVoiceAnaByFreqBandResult curRes;
                    if (resDic.TryGetValue(res.FreqBand, out curRes))
                    {
                        curRes.SampleRate.TotalCount += res.SampleRate.TotalCount;
                        curRes.Add(res);
                    }
                }
            }

            foreach (var res in resDic.Values)
            {
                res.Calculate();
            }

            totalFileRes.ResList = new List<NRVoiceAnaByFreqBandResult>(resDic.Values);
            return totalFileRes;
        }
    }
}
