﻿namespace MasterCom.RAMS.CQT
{
    partial class CQTPointQuerySettingDlg
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(CQTPointQuerySettingDlg));
            this.gcPntType = new DevExpress.XtraEditors.GroupControl();
            this.cePointAll = new DevExpress.XtraEditors.CheckEdit();
            this.gcCvrType = new DevExpress.XtraEditors.GroupControl();
            this.ceCvrAll = new DevExpress.XtraEditors.CheckEdit();
            this.gcNetType = new DevExpress.XtraEditors.GroupControl();
            this.ceNetAll = new DevExpress.XtraEditors.CheckEdit();
            this.gcSpaceType = new DevExpress.XtraEditors.GroupControl();
            this.ceSpaceAll = new DevExpress.XtraEditors.CheckEdit();
            this.gcDensityType = new DevExpress.XtraEditors.GroupControl();
            this.ceDensityAll = new DevExpress.XtraEditors.CheckEdit();
            this.btnOK = new DevExpress.XtraEditors.SimpleButton();
            this.btnCancel = new DevExpress.XtraEditors.SimpleButton();
            ((System.ComponentModel.ISupportInitialize)(this.gcPntType)).BeginInit();
            this.gcPntType.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cePointAll.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gcCvrType)).BeginInit();
            this.gcCvrType.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.ceCvrAll.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gcNetType)).BeginInit();
            this.gcNetType.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.ceNetAll.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gcSpaceType)).BeginInit();
            this.gcSpaceType.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.ceSpaceAll.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gcDensityType)).BeginInit();
            this.gcDensityType.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.ceDensityAll.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // gcPntType
            // 
            this.gcPntType.Controls.Add(this.cePointAll);
            this.gcPntType.Dock = System.Windows.Forms.DockStyle.Top;
            this.gcPntType.Location = new System.Drawing.Point(0, 221);
            this.gcPntType.Name = "gcPntType";
            this.gcPntType.Size = new System.Drawing.Size(554, 111);
            this.gcPntType.TabIndex = 1;
            this.gcPntType.Text = "地点类型";
            // 
            // cePointAll
            // 
            this.cePointAll.Location = new System.Drawing.Point(503, 0);
            this.cePointAll.Name = "cePointAll";
            this.cePointAll.Properties.AutoWidth = true;
            this.cePointAll.Properties.Caption = "全选";
            this.cePointAll.Size = new System.Drawing.Size(47, 19);
            this.cePointAll.TabIndex = 0;
            this.cePointAll.CheckedChanged += new System.EventHandler(this.checkAll_CheckedChanged);
            // 
            // gcCvrType
            // 
            this.gcCvrType.Controls.Add(this.ceCvrAll);
            this.gcCvrType.Dock = System.Windows.Forms.DockStyle.Top;
            this.gcCvrType.Location = new System.Drawing.Point(0, 0);
            this.gcCvrType.Name = "gcCvrType";
            this.gcCvrType.Size = new System.Drawing.Size(554, 62);
            this.gcCvrType.TabIndex = 2;
            this.gcCvrType.Text = "覆盖类型";
            // 
            // ceCvrAll
            // 
            this.ceCvrAll.Location = new System.Drawing.Point(503, 0);
            this.ceCvrAll.Name = "ceCvrAll";
            this.ceCvrAll.Properties.AutoWidth = true;
            this.ceCvrAll.Properties.Caption = "全选";
            this.ceCvrAll.Size = new System.Drawing.Size(47, 19);
            this.ceCvrAll.TabIndex = 0;
            this.ceCvrAll.CheckedChanged += new System.EventHandler(this.checkAll_CheckedChanged);
            // 
            // gcNetType
            // 
            this.gcNetType.Controls.Add(this.ceNetAll);
            this.gcNetType.Dock = System.Windows.Forms.DockStyle.Top;
            this.gcNetType.Location = new System.Drawing.Point(0, 62);
            this.gcNetType.Name = "gcNetType";
            this.gcNetType.Size = new System.Drawing.Size(554, 53);
            this.gcNetType.TabIndex = 3;
            this.gcNetType.Text = "网络类型";
            // 
            // ceNetAll
            // 
            this.ceNetAll.Location = new System.Drawing.Point(503, 0);
            this.ceNetAll.Name = "ceNetAll";
            this.ceNetAll.Properties.AutoWidth = true;
            this.ceNetAll.Properties.Caption = "全选";
            this.ceNetAll.Size = new System.Drawing.Size(47, 19);
            this.ceNetAll.TabIndex = 0;
            this.ceNetAll.CheckedChanged += new System.EventHandler(this.checkAll_CheckedChanged);
            // 
            // gcSpaceType
            // 
            this.gcSpaceType.Controls.Add(this.ceSpaceAll);
            this.gcSpaceType.Dock = System.Windows.Forms.DockStyle.Top;
            this.gcSpaceType.Location = new System.Drawing.Point(0, 115);
            this.gcSpaceType.Name = "gcSpaceType";
            this.gcSpaceType.Size = new System.Drawing.Size(554, 53);
            this.gcSpaceType.TabIndex = 4;
            this.gcSpaceType.Text = "建筑类型";
            // 
            // ceSpaceAll
            // 
            this.ceSpaceAll.Location = new System.Drawing.Point(503, 0);
            this.ceSpaceAll.Name = "ceSpaceAll";
            this.ceSpaceAll.Properties.AutoWidth = true;
            this.ceSpaceAll.Properties.Caption = "全选";
            this.ceSpaceAll.Size = new System.Drawing.Size(47, 19);
            this.ceSpaceAll.TabIndex = 0;
            this.ceSpaceAll.CheckedChanged += new System.EventHandler(this.checkAll_CheckedChanged);
            // 
            // gcDensityType
            // 
            this.gcDensityType.Controls.Add(this.ceDensityAll);
            this.gcDensityType.Dock = System.Windows.Forms.DockStyle.Top;
            this.gcDensityType.Location = new System.Drawing.Point(0, 168);
            this.gcDensityType.Name = "gcDensityType";
            this.gcDensityType.Size = new System.Drawing.Size(554, 53);
            this.gcDensityType.TabIndex = 5;
            this.gcDensityType.Text = "密度类型";
            // 
            // ceDensityAll
            // 
            this.ceDensityAll.Location = new System.Drawing.Point(503, 0);
            this.ceDensityAll.Name = "ceDensityAll";
            this.ceDensityAll.Properties.AutoWidth = true;
            this.ceDensityAll.Properties.Caption = "全选";
            this.ceDensityAll.Size = new System.Drawing.Size(47, 19);
            this.ceDensityAll.TabIndex = 0;
            this.ceDensityAll.CheckedChanged += new System.EventHandler(this.checkAll_CheckedChanged);
            // 
            // btnOK
            // 
            this.btnOK.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnOK.DialogResult = System.Windows.Forms.DialogResult.OK;
            this.btnOK.Location = new System.Drawing.Point(389, 353);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 6;
            this.btnOK.Text = "确定";
            // 
            // btnCancel
            // 
            this.btnCancel.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnCancel.Location = new System.Drawing.Point(470, 353);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 6;
            this.btnCancel.Text = "取消";
            // 
            // CQTPointQuerySettingDlg
            // 
            this.AcceptButton = this.btnOK;
            this.Appearance.Font = new System.Drawing.Font("SimSun", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("CQTPointQuerySettingDlg.Appearance.Image")));
            this.Appearance.Options.UseFont = true;
            this.Appearance.Options.UseImage = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(554, 388);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.Controls.Add(this.gcPntType);
            this.Controls.Add(this.gcDensityType);
            this.Controls.Add(this.gcSpaceType);
            this.Controls.Add(this.gcNetType);
            this.Controls.Add(this.gcCvrType);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "CQTPointQuerySettingDlg";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "地点查询设置";
            ((System.ComponentModel.ISupportInitialize)(this.gcPntType)).EndInit();
            this.gcPntType.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.cePointAll.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gcCvrType)).EndInit();
            this.gcCvrType.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.ceCvrAll.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gcNetType)).EndInit();
            this.gcNetType.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.ceNetAll.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gcSpaceType)).EndInit();
            this.gcSpaceType.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.ceSpaceAll.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gcDensityType)).EndInit();
            this.gcDensityType.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.ceDensityAll.Properties)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.GroupControl gcPntType;
        private DevExpress.XtraEditors.GroupControl gcCvrType;
        private DevExpress.XtraEditors.GroupControl gcNetType;
        private DevExpress.XtraEditors.GroupControl gcSpaceType;
        private DevExpress.XtraEditors.GroupControl gcDensityType;
        private DevExpress.XtraEditors.CheckEdit ceCvrAll;
        private DevExpress.XtraEditors.CheckEdit cePointAll;
        private DevExpress.XtraEditors.CheckEdit ceNetAll;
        private DevExpress.XtraEditors.CheckEdit ceSpaceAll;
        private DevExpress.XtraEditors.CheckEdit ceDensityAll;
        private DevExpress.XtraEditors.SimpleButton btnOK;
        private DevExpress.XtraEditors.SimpleButton btnCancel;

    }
}