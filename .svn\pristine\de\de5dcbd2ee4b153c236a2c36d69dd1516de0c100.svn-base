using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.Func
{
    partial class RepeaterInfoForm : Form
    {
        public RepeaterInfoForm(MainModel mainModel, Repeater repeater)
        {
            InitializeComponent();
            this.mainModel = mainModel;
            this.repeater = repeater;
            textBoxName.Text = repeater.Name;
            textBoxBand.Text = repeater.BandType.ToString();
            textBoxLongitude.Text = repeater.Longitude.ToString();
            textBoxLatitude.Text = repeater.Latitude.ToString();
            textBoxAddress.Text = repeater.Address;
            if (MapCellLayer.DrawCurrent)
            {
                if (repeater.CurrentDonarCell != null)
                {
                    textBoxDonarCell.Text = repeater.CurrentDonarCell.Name;
                    buttonDonarCellInfo.Enabled = true;
                }
                else
                {
                    buttonDonarCellInfo.Enabled = false;
                }
            }
            else
            {
                if (repeater.GetDonarCellByTime(MapCellLayer.CurShowTimeAt) != null)
                {
                    textBoxDonarCell.Text = repeater.GetDonarCellByTime(MapCellLayer.CurShowTimeAt).Name;
                    buttonDonarCellInfo.Enabled = true;
                }
                else
                {
                    buttonDonarCellInfo.Enabled = false;
                }
            }
        }

        private void buttonDonarCellInfo_Click(object sender, EventArgs e)
        {
            if (MapCellLayer.DrawCurrent)
            {
                new CellInfoForm(mainModel, repeater.CurrentDonarCell).Show(Owner);
            }
            else
            {
                new CellInfoForm(mainModel, repeater.GetDonarCellByTime(MapCellLayer.CurShowTimeAt)).Show(Owner);
            }
        }

        private void buttonLocation_Click(object sender, EventArgs e)
        {
            mainModel.SelectedRepeater = repeater;
            mainModel.FireSelectedRepeaterChanged(this);
        }

        private void buttonOK_Click(object sender, EventArgs e)
        {
            Close();
        }

        private void InitializeComponent()
        {
            System.Windows.Forms.Label label8;
            System.Windows.Forms.Label label2;
            System.Windows.Forms.Label label5;
            System.Windows.Forms.Label label6;
            System.Windows.Forms.Label labelTCH;
            System.Windows.Forms.Label label7;
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(RepeaterInfoForm));
            this.buttonOK = new System.Windows.Forms.Button();
            this.textBoxName = new System.Windows.Forms.TextBox();
            this.textBoxLatitude = new System.Windows.Forms.TextBox();
            this.textBoxBand = new System.Windows.Forms.TextBox();
            this.textBoxLongitude = new System.Windows.Forms.TextBox();
            this.textBoxAddress = new System.Windows.Forms.TextBox();
            this.textBoxDonarCell = new System.Windows.Forms.TextBox();
            this.buttonDonarCellInfo = new System.Windows.Forms.Button();
            this.buttonLocation = new System.Windows.Forms.Button();
            label8 = new System.Windows.Forms.Label();
            label2 = new System.Windows.Forms.Label();
            label5 = new System.Windows.Forms.Label();
            label6 = new System.Windows.Forms.Label();
            labelTCH = new System.Windows.Forms.Label();
            label7 = new System.Windows.Forms.Label();
            this.SuspendLayout();
            // 
            // label8
            // 
            label8.AutoSize = true;
            label8.Location = new System.Drawing.Point(12, 15);
            label8.Name = "label8";
            label8.Size = new System.Drawing.Size(35, 12);
            label8.TabIndex = 0;
            label8.Text = "&Name:";
            // 
            // label2
            // 
            label2.AutoSize = true;
            label2.Location = new System.Drawing.Point(158, 15);
            label2.Name = "label2";
            label2.Size = new System.Drawing.Size(35, 12);
            label2.TabIndex = 2;
            label2.Text = "&Band:";
            // 
            // label5
            // 
            label5.AutoSize = true;
            label5.Location = new System.Drawing.Point(12, 41);
            label5.Name = "label5";
            label5.Size = new System.Drawing.Size(35, 12);
            label5.TabIndex = 4;
            label5.Text = "&Long:";
            // 
            // label6
            // 
            label6.AutoSize = true;
            label6.Location = new System.Drawing.Point(158, 41);
            label6.Name = "label6";
            label6.Size = new System.Drawing.Size(29, 12);
            label6.TabIndex = 6;
            label6.Text = "&Lat:";
            // 
            // labelTCH
            // 
            labelTCH.AutoSize = true;
            labelTCH.Location = new System.Drawing.Point(12, 67);
            labelTCH.Name = "labelTCH";
            labelTCH.Size = new System.Drawing.Size(35, 12);
            labelTCH.TabIndex = 8;
            labelTCH.Text = "&Addr:";
            // 
            // label7
            // 
            label7.AutoSize = true;
            label7.Location = new System.Drawing.Point(11, 93);
            label7.Name = "label7";
            label7.Size = new System.Drawing.Size(41, 12);
            label7.TabIndex = 10;
            label7.Text = "&Donar:";
            // 
            // buttonOK
            // 
            this.buttonOK.DialogResult = System.Windows.Forms.DialogResult.OK;
            this.buttonOK.Location = new System.Drawing.Point(227, 116);
            this.buttonOK.Name = "buttonOK";
            this.buttonOK.Size = new System.Drawing.Size(87, 23);
            this.buttonOK.TabIndex = 14;
            this.buttonOK.Text = "&OK";
            this.buttonOK.UseVisualStyleBackColor = true;
            this.buttonOK.Click += new System.EventHandler(this.buttonOK_Click);
            // 
            // textBoxName
            // 
            this.textBoxName.BackColor = System.Drawing.SystemColors.Window;
            this.textBoxName.Location = new System.Drawing.Point(56, 12);
            this.textBoxName.Name = "textBoxName";
            this.textBoxName.ReadOnly = true;
            this.textBoxName.Size = new System.Drawing.Size(96, 21);
            this.textBoxName.TabIndex = 1;
            // 
            // textBoxLatitude
            // 
            this.textBoxLatitude.BackColor = System.Drawing.SystemColors.Window;
            this.textBoxLatitude.Location = new System.Drawing.Point(202, 38);
            this.textBoxLatitude.Name = "textBoxLatitude";
            this.textBoxLatitude.ReadOnly = true;
            this.textBoxLatitude.Size = new System.Drawing.Size(112, 21);
            this.textBoxLatitude.TabIndex = 7;
            // 
            // textBoxBand
            // 
            this.textBoxBand.BackColor = System.Drawing.SystemColors.Window;
            this.textBoxBand.Location = new System.Drawing.Point(202, 12);
            this.textBoxBand.Name = "textBoxBand";
            this.textBoxBand.ReadOnly = true;
            this.textBoxBand.Size = new System.Drawing.Size(112, 21);
            this.textBoxBand.TabIndex = 3;
            // 
            // textBoxLongitude
            // 
            this.textBoxLongitude.BackColor = System.Drawing.SystemColors.Window;
            this.textBoxLongitude.Location = new System.Drawing.Point(56, 38);
            this.textBoxLongitude.Name = "textBoxLongitude";
            this.textBoxLongitude.ReadOnly = true;
            this.textBoxLongitude.Size = new System.Drawing.Size(96, 21);
            this.textBoxLongitude.TabIndex = 5;
            // 
            // textBoxAddress
            // 
            this.textBoxAddress.BackColor = System.Drawing.SystemColors.Window;
            this.textBoxAddress.Location = new System.Drawing.Point(56, 64);
            this.textBoxAddress.Name = "textBoxAddress";
            this.textBoxAddress.ReadOnly = true;
            this.textBoxAddress.Size = new System.Drawing.Size(258, 21);
            this.textBoxAddress.TabIndex = 9;
            // 
            // textBoxDonarCell
            // 
            this.textBoxDonarCell.BackColor = System.Drawing.SystemColors.Window;
            this.textBoxDonarCell.Location = new System.Drawing.Point(56, 90);
            this.textBoxDonarCell.Name = "textBoxDonarCell";
            this.textBoxDonarCell.ReadOnly = true;
            this.textBoxDonarCell.Size = new System.Drawing.Size(96, 21);
            this.textBoxDonarCell.TabIndex = 11;
            // 
            // buttonDonarCellInfo
            // 
            this.buttonDonarCellInfo.Location = new System.Drawing.Point(158, 90);
            this.buttonDonarCellInfo.Name = "buttonDonarCellInfo";
            this.buttonDonarCellInfo.Size = new System.Drawing.Size(42, 20);
            this.buttonDonarCellInfo.TabIndex = 12;
            this.buttonDonarCellInfo.Text = "&Info...";
            this.buttonDonarCellInfo.UseVisualStyleBackColor = true;
            this.buttonDonarCellInfo.Click += new System.EventHandler(this.buttonDonarCellInfo_Click);
            // 
            // buttonLocation
            // 
            this.buttonLocation.Location = new System.Drawing.Point(12, 116);
            this.buttonLocation.Name = "buttonLocation";
            this.buttonLocation.Size = new System.Drawing.Size(87, 23);
            this.buttonLocation.TabIndex = 13;
            this.buttonLocation.Text = "&Location";
            this.buttonLocation.UseVisualStyleBackColor = true;
            this.buttonLocation.Click += new System.EventHandler(this.buttonLocation_Click);
            // 
            // RepeaterInfoForm
            // 
            this.AcceptButton = this.buttonLocation;
            this.CancelButton = this.buttonOK;
            this.ClientSize = new System.Drawing.Size(326, 151);
            this.Controls.Add(this.buttonLocation);
            this.Controls.Add(this.textBoxDonarCell);
            this.Controls.Add(this.buttonDonarCellInfo);
            this.Controls.Add(label7);
            this.Controls.Add(this.textBoxAddress);
            this.Controls.Add(labelTCH);
            this.Controls.Add(this.textBoxLatitude);
            this.Controls.Add(this.textBoxBand);
            this.Controls.Add(label2);
            this.Controls.Add(this.textBoxLongitude);
            this.Controls.Add(label5);
            this.Controls.Add(label6);
            this.Controls.Add(label8);
            this.Controls.Add(this.textBoxName);
            this.Controls.Add(this.buttonOK);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle;
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "RepeaterInfoForm";
            this.ShowInTaskbar = false;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "Repeater Info";
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        private readonly MainModel mainModel;

        private readonly Repeater repeater;

        private TextBox textBoxName;

        private TextBox textBoxBand;

        private TextBox textBoxLongitude;

        private TextBox textBoxLatitude;

        private TextBox textBoxAddress;

        private TextBox textBoxDonarCell;

        private Button buttonDonarCellInfo;

        private Button buttonLocation;

        private Button buttonOK;
    }
}
