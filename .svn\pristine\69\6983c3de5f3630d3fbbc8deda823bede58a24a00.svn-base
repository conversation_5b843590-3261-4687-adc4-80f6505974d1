﻿using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRModRoadStater : ModRoadStaterBase
    {
        public NRModRoadStater(MainModel mm, NRModRoadCondition cond)
            : base(mm, cond)
        {
            this.lteCond = cond;
        }

        public override object GetStatResult(object param)
        {
            NRModInterfereCondition interfereCond = param as NRModInterfereCondition;
            if (interfereCond == null)
            {
                return null;
            }
            List<NRModRoadItem> resultList = new List<NRModRoadItem>();
            object[] args = new object[] { interfereCond, resultList };
            if (mainModel.IsBackground)
            {
                GetStatResultInThread(args);
            }
            else
            {
                WaitBox.Show("正在统计道路信息...", GetStatResultInThread, args);
            }
            return resultList;
        }

        private void GetStatResultInThread(object param)
        {
            try
            {
                object[] args = param as object[];
                NRModInterfereCondition interfereCond = args[0] as NRModInterfereCondition;
                List<NRModRoadItem> resultList = args[1] as List<NRModRoadItem>;

                int iLoop = 0;
                foreach (ModRoadItemBase roadBase in this.roadList)
                {
                    if (!mainModel.IsBackground)
                    {
                        WaitBox.ProgressPercent = (++iLoop) * 100 / this.roadList.Count;
                    }

                    NRModRoadItem road = roadBase as NRModRoadItem;
                    road.FilterInterfere(interfereCond);
                    if (road.InterSampleRate >= interfereCond.InterfereRate)
                    {
                        resultList.Add(road);
                        road.SN = resultList.Count;
                    }
                }
            }
            finally
            {
                System.Threading.Thread.Sleep(500);
                if (!mainModel.IsBackground)
                {
                    WaitBox.Close();
                }
            }
        }

        private NRModRoadCondition lteCond { get; set; }
        public void ClearRoadList()
        {
            if (roadList != null)
            {
                roadList.Clear();
            }
        }
    }
}
