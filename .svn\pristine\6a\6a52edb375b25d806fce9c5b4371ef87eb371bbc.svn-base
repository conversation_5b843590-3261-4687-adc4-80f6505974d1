﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.KPI_Statistics;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using Mobius.Utility;
using MasterCom.RAMS.ZTFunc;
using MasterCom.RAMS.BackgroundFunc;

namespace MasterCom.RAMS.Stat
{
    public class CellKPIAutoQuery : CellKPIQuery
    {
        public Dictionary<string, Dictionary<int, CellAutoStatInfo>> CellFileDataDic { get; set; }

        public CellKPIAutoQuery(ReporterTemplate template)
            : base(template, null, NetworkType.LTE_TDD)
        {
            this.rptTemplate = template;
            this.IsShowResultForm = false;
        }

        protected override bool getConditionBeforeQuery()
        {
            CellFileDataDic = new Dictionary<string, Dictionary<int, CellAutoStatInfo>>();
            return true;
        }

        protected override void afterRecieveAllData(params object[] reservedParams)
        {
            //
        }

        protected override void recieveAndHandleSpecificStatData(Package package
            , List<StatImgDefItem> curImgColumnDef
            , KPIStatDataBase singleStatData)
        {
            int lac = package.Content.GetParamInt();
            int ci = package.Content.GetParamInt();
            fillStatData(package, curImgColumnDef, singleStatData);

            SaveCellFileInfo(lac, ci, singleStatData);
        }
        protected override void handleStatEvent(Event evt)
        {
            int lac = (int)evt["LAC"];
            int ci = (int)evt["CI"];
            StatDataEvent singleStatData = new StatDataEvent(evt, needSeparateByServiceID(evt), needSeparateByFileName(evt));

            SaveCellFileInfo(lac, ci, singleStatData);
        }

        private void SaveCellFileInfo(int lac, int ci, KPIStatDataBase singleStatData)
        {
            DTDataHeader fi = DTDataHeaderManager.GetInstance().GetHeaderByFileID(singleStatData.FileID);
            CellAutoStatInfo tmp = new CellAutoStatInfo();
            tmp.LAC = lac;
            tmp.CI = ci;
            tmp.FileHeader = fi;
            tmp.KPIData.AddStatData(fi, singleStatData, false);

            string cellKey = lac + "_" + ci;

            Dictionary<int, CellAutoStatInfo> fileDataDic;
            if (!this.CellFileDataDic.TryGetValue(cellKey, out fileDataDic))
            {
                fileDataDic = new Dictionary<int, CellAutoStatInfo>();
                CellFileDataDic[cellKey] = fileDataDic;
            }

            CellAutoStatInfo fsi = null;
            if (fileDataDic.TryGetValue(singleStatData.FileID, out fsi))
            {
                fsi.KPIData.AddStatData(fi, singleStatData, false);
            }
            else
            {
                fileDataDic[singleStatData.FileID] = tmp;
            }
        }
        public List<NPOIRow> CreateReport(ReporterTemplate tpl, List<CellKpiAutoExportInfo> dataList, bool isCheckCellBasicInfo)
        {
            if (dataList == null)
                return new List<NPOIRow>();

            List<NPOIRow> outputRows = new List<NPOIRow>();

            int staticTitleCount = 1;
            if (isCheckCellBasicInfo)
            {
                staticTitleCount = 13;
            }

            NPOIRow row;
            for (int r = 0; r < dataList.Count; r++)
            {
                CellKpiAutoExportInfo cellInfo = dataList[r];
                row = new NPOIRow();
                outputRows.Add(row);
                row.cellValues = new List<object>(tpl.Columns.Count + staticTitleCount);
                row.cellValues.Add(r + 1);//序号
                if (isCheckCellBasicInfo)
                {
                    row.cellValues.Add(cellInfo.DistrictName);
                    row.cellValues.Add(cellInfo.BtsName);
                    row.cellValues.Add(cellInfo.BtsID);
                    row.cellValues.Add(cellInfo.Longitude);
                    row.cellValues.Add(cellInfo.Latitude);
                    row.cellValues.Add("");
                    row.cellValues.Add(cellInfo.CellName);
                    row.cellValues.Add(cellInfo.StatInfo.LAC);
                    row.cellValues.Add(cellInfo.CellID);
                    row.cellValues.Add(cellInfo.Earfcn);
                    row.cellValues.Add(cellInfo.PCI);
                    row.cellValues.Add(cellInfo.HasFoundFile ? "是" : "否");
                }

                #region KPI信息
                for (int i = staticTitleCount; i < tpl.Columns.Count + staticTitleCount; i++)
                {
                    row.cellValues.Add(null);
                    row.cellValues[i] = getColumnSetValue(cellInfo.StatInfo, tpl, i - staticTitleCount);
                }
                #endregion
            }
            return outputRows;
        }

    }
    public class CellAutoStatInfo : CellStatInfoItem, IComparable<CellAutoStatInfo>
    {
        #region IComparable<SingleCellStatInfo> 成员

        public int CompareTo(CellAutoStatInfo other)
        {
            if (this.FileHeader != null && other.FileHeader != null)
            {
                return other.FileHeader.BeginTime.CompareTo(this.FileHeader.BeginTime);
            }
            else
            {
                return other.DistrictID.CompareTo(this.DistrictID);
            }
        }
        #endregion
    }
}
