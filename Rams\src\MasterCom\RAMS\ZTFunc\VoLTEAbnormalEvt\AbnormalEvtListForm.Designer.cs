﻿namespace MasterCom.RAMS.ZTFunc.VoLTEAbnormalEvt
{
    partial class AbnormalEvtListForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.ctxMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExportXlsx = new System.Windows.Forms.ToolStripMenuItem();
            this.objectListView = new BrightIdeasSoftware.ObjectListView();
            this.olvColumnIndex = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnDateTime = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLongitude = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLatitude = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnTAC = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnECI = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRoadName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnFileName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colCellCode = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.ctxMenu.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.objectListView)).BeginInit();
            this.SuspendLayout();
            // 
            // ctxMenu
            // 
            this.ctxMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExportXlsx});
            this.ctxMenu.Name = "ctxMenu";
            this.ctxMenu.Size = new System.Drawing.Size(139, 26);
            // 
            // miExportXlsx
            // 
            this.miExportXlsx.Name = "miExportXlsx";
            this.miExportXlsx.Size = new System.Drawing.Size(138, 22);
            this.miExportXlsx.Text = "导出Excel...";
            this.miExportXlsx.Click += new System.EventHandler(this.miExportXlsx_Click);
            // 
            // objectListView
            // 
            this.objectListView.AllColumns.Add(this.olvColumnIndex);
            this.objectListView.AllColumns.Add(this.olvColumnName);
            this.objectListView.AllColumns.Add(this.olvColumnDateTime);
            this.objectListView.AllColumns.Add(this.olvColumnLongitude);
            this.objectListView.AllColumns.Add(this.olvColumnLatitude);
            this.objectListView.AllColumns.Add(this.olvColumnCellName);
            this.objectListView.AllColumns.Add(this.colCellCode);
            this.objectListView.AllColumns.Add(this.olvColumnTAC);
            this.objectListView.AllColumns.Add(this.olvColumnECI);
            this.objectListView.AllColumns.Add(this.olvColumnRoadName);
            this.objectListView.AllColumns.Add(this.olvColumnFileName);
            this.objectListView.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnIndex,
            this.olvColumnName,
            this.olvColumnDateTime,
            this.olvColumnLongitude,
            this.olvColumnLatitude,
            this.olvColumnCellName,
            this.colCellCode,
            this.olvColumnTAC,
            this.olvColumnECI,
            this.olvColumnRoadName,
            this.olvColumnFileName});
            this.objectListView.ContextMenuStrip = this.ctxMenu;
            this.objectListView.Cursor = System.Windows.Forms.Cursors.Default;
            this.objectListView.Dock = System.Windows.Forms.DockStyle.Fill;
            this.objectListView.FullRowSelect = true;
            this.objectListView.GridLines = true;
            this.objectListView.HeaderWordWrap = true;
            this.objectListView.Location = new System.Drawing.Point(0, 0);
            this.objectListView.Name = "objectListView";
            this.objectListView.ShowGroups = false;
            this.objectListView.Size = new System.Drawing.Size(916, 349);
            this.objectListView.TabIndex = 3;
            this.objectListView.UseCompatibleStateImageBehavior = false;
            this.objectListView.View = System.Windows.Forms.View.Details;
            this.objectListView.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.objectListView_MouseDoubleClick);
            // 
            // olvColumnIndex
            // 
            this.olvColumnIndex.HeaderFont = null;
            this.olvColumnIndex.Text = "序号";
            this.olvColumnIndex.Width = 50;
            // 
            // olvColumnName
            // 
            this.olvColumnName.HeaderFont = null;
            this.olvColumnName.MinimumWidth = 150;
            this.olvColumnName.Text = "事件名称";
            this.olvColumnName.Width = 150;
            // 
            // olvColumnDateTime
            // 
            this.olvColumnDateTime.HeaderFont = null;
            this.olvColumnDateTime.MinimumWidth = 160;
            this.olvColumnDateTime.Text = "时间";
            this.olvColumnDateTime.Width = 160;
            // 
            // olvColumnLongitude
            // 
            this.olvColumnLongitude.HeaderFont = null;
            this.olvColumnLongitude.MinimumWidth = 100;
            this.olvColumnLongitude.Text = "经度";
            this.olvColumnLongitude.Width = 100;
            // 
            // olvColumnLatitude
            // 
            this.olvColumnLatitude.HeaderFont = null;
            this.olvColumnLatitude.MinimumWidth = 100;
            this.olvColumnLatitude.Text = "纬度";
            this.olvColumnLatitude.Width = 100;
            // 
            // olvColumnCellName
            // 
            this.olvColumnCellName.HeaderFont = null;
            this.olvColumnCellName.MinimumWidth = 120;
            this.olvColumnCellName.Text = "小区名";
            this.olvColumnCellName.Width = 120;
            // 
            // olvColumnTAC
            // 
            this.olvColumnTAC.HeaderFont = null;
            this.olvColumnTAC.Text = "TAC";
            // 
            // olvColumnECI
            // 
            this.olvColumnECI.HeaderFont = null;
            this.olvColumnECI.Text = "ECI";
            // 
            // olvColumnRoadName
            // 
            this.olvColumnRoadName.HeaderFont = null;
            this.olvColumnRoadName.Text = "道路名称";
            this.olvColumnRoadName.Width = 100;
            // 
            // olvColumnFileName
            // 
            this.olvColumnFileName.HeaderFont = null;
            this.olvColumnFileName.Text = "文件名";
            this.olvColumnFileName.Width = 100;
            // 
            // colCellCode
            // 
            this.colCellCode.HeaderFont = null;
            this.colCellCode.MinimumWidth = 80;
            this.colCellCode.Text = "CellCode";
            this.colCellCode.Width = 80;
            // 
            // AbnormalEvtListForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(916, 349);
            this.Controls.Add(this.objectListView);
            this.Name = "AbnormalEvtListForm";
            this.Text = "VoLTE异常事件列表";
            this.ctxMenu.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.objectListView)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.ContextMenuStrip ctxMenu;
        private System.Windows.Forms.ToolStripMenuItem miExportXlsx;
        private BrightIdeasSoftware.ObjectListView objectListView;
        private BrightIdeasSoftware.OLVColumn olvColumnIndex;
        private BrightIdeasSoftware.OLVColumn olvColumnName;
        private BrightIdeasSoftware.OLVColumn olvColumnDateTime;
        private BrightIdeasSoftware.OLVColumn olvColumnLongitude;
        private BrightIdeasSoftware.OLVColumn olvColumnLatitude;
        private BrightIdeasSoftware.OLVColumn olvColumnCellName;
        private BrightIdeasSoftware.OLVColumn olvColumnTAC;
        private BrightIdeasSoftware.OLVColumn olvColumnECI;
        private BrightIdeasSoftware.OLVColumn olvColumnRoadName;
        private BrightIdeasSoftware.OLVColumn olvColumnFileName;
        private BrightIdeasSoftware.OLVColumn colCellCode;
    }
}