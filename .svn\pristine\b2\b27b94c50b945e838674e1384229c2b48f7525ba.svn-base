﻿using MasterCom.MTGis;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Grid;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class NREdgeSpeedAnaGrid : DIYGridQuery
    {
        public Dictionary<string, Dictionary<string, MapOperation2>> mutRegionMopDic { get; set; }
        public Dictionary<string, Dictionary<string, List<float>>> cityGridTypeNameEdgeSpeedDic { get; set; }
        public List<NREdgeSpeedInfo> nrEdgeSpeedInfoList { get; set; }
        protected string strCityName = "";
        protected NREdgeSpeedCondtion curCondition = new NREdgeSpeedCondtion();
        protected NREdgeSpeedAnaDlg dlg;
        protected NREdgeSpeedAnaHelper helper = new NREdgeSpeedAnaHelper();
        protected string size;
        protected string time;

        public NREdgeSpeedAnaGrid(MainModel mainModel)
           : base(mainModel)
        {
            this.mainModel = mainModel;
            this.size = "Nr_052164020101";
            this.time = "Nr_052164020102";
        }

        public override string Name
        {
            get { return "边缘下载速率分析_NR(按栅格)"; }
        }

        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(3, 35000, 35049, "边缘下载速率分析_NR(按栅格)");
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MainModel.NeedSearchType.Region;
        }

        protected override void AddGeographicFilter(Package package)
        {
            AddDIYRegion_Intersect(package);
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            if (searchGeometrys.Region == null)
            {
                return false;
            }
            return searchGeometrys.IsValidRegion;
        }

        protected override bool isValidStatImg(double lng, double lat)
        {
            GridUnitBase grid = new GridUnitBase(lng, lat);
            return condition.Geometorys.GeoOp.ContainsRectCenter(grid.Bounds);
        }

        protected override string getStatImgNeededTriadID(params object[] paramSet)
        {
            return getTriadIDIgnoreServiceType(new string[] { this.size, this.time });
        }

        protected override bool getConditionBeforeQuery()
        {
            base.getConditionBeforeQuery();

            if (dlg == null)
            {
                dlg = new NREdgeSpeedAnaDlg();
            }
            dlg.SetCondition(curCondition);
            if (dlg.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                mutRegionMopDic = new Dictionary<string, Dictionary<string, MapOperation2>>();
                cityGridTypeNameEdgeSpeedDic = new Dictionary<string, Dictionary<string, List<float>>>();
                nrEdgeSpeedInfoList = new List<NREdgeSpeedInfo>();
                mutRegionMopDic = EdgeSpeedAnaHelper.InitRegionMop2();

                curCondition = dlg.GetCondition();
                return true;
            }

            return false;
        }

        protected override void query()
        {
            if (!getConditionBeforeQuery())
            {
                return;
            }
            foreach (int districtID in condition.DistrictIDs)
            {
                strCityName = DistrictManager.GetInstance().getDistrictName(districtID);
                queryDistrictData(districtID);
                changGridDataFormula();
                MainModel.CurGridColorUnitMatrix = new GridMatrix<ColorUnit>();
            }
            afterRecieveAllData();
            fireShowResult();
        }

        protected override void queryInThread(object o)
        {
            try
            {
                ClientProxy clientProxy = (ClientProxy)o;
                MainModel.DTDataManager.Clear();
                MainModel.SelectedTestPoints.Clear();
                MainModel.SelectedEvents.Clear();
                MainModel.SelectedMessage = null;
                MainModel.CurGridCoverData = null;
                MainModel.CurGridColorUnitMatrix = new GridMatrix<ColorUnit>();
                MainModel.MainForm.GetMapForm().GetGridShowLayer();
                WaitBox.Text = "开始统计查询栅格数据...";
                WaitBox.CanCancel = true;
                MainModel.CurGridColorUnitMatrix = new GridMatrix<ColorUnit>();

                string statImgIDSet = this.getStatImgNeededTriadID();
                if (condition.IsByRound)
                {
                    queryPeriodInfo(null, clientProxy, statImgIDSet, MainModel.CurGridColorUnitMatrix);
                }
                else
                {
                    foreach (TimePeriod period in condition.Periods)
                    {
                        queryPeriodInfo(period, clientProxy, statImgIDSet, MainModel.CurGridColorUnitMatrix);
                    }
                }
                WaitBox.Text = "数据获取完毕，进行显示预处理...";
                if (MainModel.CurGridCoverData != null)
                {
                    MainModel.CurGridCoverData.Clear();
                }
            }
            catch (Exception e)
            {
                System.Windows.Forms.MessageBox.Show("Error" + e.StackTrace + e.ToString());
            }
            finally
            {
                WaitBox.Close();
            }
            MapGridLayer.NeedFreshFullImg = true;
        }

        private void changGridDataFormula()
        {
            float fDownLoadSize = 0;
            float fDownLoadTime = 0;
            float fEdgeSpeed = 0;
            foreach (ColorUnit cu in MainModel.CurGridColorUnitMatrix)
            {
                fDownLoadSize = (float)cu.DataHub.CalcValueByFormula(this.size);
                fDownLoadTime = (float)cu.DataHub.CalcValueByFormula(this.time);
                fEdgeSpeed = fDownLoadSize / fDownLoadTime / 1024 / 1024 * 8000;
                if (fDownLoadTime > 0)
                {
                    helper.AddValidData(cu.LTLng, cu.LTLat, fEdgeSpeed, strCityName, mutRegionMopDic, cityGridTypeNameEdgeSpeedDic);
                }
            }
        }

        protected override void afterRecieveAllData(params object[] reservedParams)
        {
            List<EdgeSpeedInfo> infoList = helper.GetResultAfterAllQuery(cityGridTypeNameEdgeSpeedDic, curCondition.EdgeSpeedThreshold);
            foreach (var info in infoList)
            {
                nrEdgeSpeedInfoList.Add(info as NREdgeSpeedInfo);
            }
        }

        protected override void fireShowResult()
        {
            NREdgeSpeedAnaForm frm = MainModel.CreateResultForm(typeof(NREdgeSpeedAnaForm)) as NREdgeSpeedAnaForm;
            frm.Init("有效栅格数");
            frm.FillData(nrEdgeSpeedInfoList);
            frm.Visible = true;
            frm.BringToFront();
        }
    }
}
