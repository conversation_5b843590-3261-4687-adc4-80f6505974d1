﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public class StreetCellInfo
    {
        private string streetName;
        private string subStreetName { get; set; }
        private string netType;
        private string coverType;
        private int seqNo;
        public int SeqNo { get { return seqNo; } }
        private int lac;
        public int LAC { get { return lac; } }
        private int ci;
        public int CI { get { return ci; } }
        private string cellName;
        public string CellName { get { return cellName; } }
        private float lastedSeconds;
        public float LastedSeconds { get { return lastedSeconds; } }
        private string areaType1 { get; set; }
        private string areaType2 { get; set; }
        private string remark { get; set; }

        public void FillForm(Content content)
        {
            this.streetName = content.GetParamString();
            this.subStreetName = content.GetParamString();
            this.netType = content.GetParamString();
            this.coverType = content.GetParamString();
            this.seqNo = content.GetParamInt();
            this.lac = content.GetParamInt();
            this.ci = content.GetParamInt();
            this.cellName = content.GetParamString();
            this.lastedSeconds = content.GetParamFloat();
            this.areaType1 = content.GetParamString();
            this.areaType2 = content.GetParamString();
            this.remark = content.GetParamString();
        }

        public bool IsBelong(HandoverSequenceCondition hosqCond)
        {
            if (hosqCond.StreetNameStr.Trim() == streetName.Trim() &&
                hosqCond.NetTypeStr.Trim() == netType.Trim() &&
                hosqCond.CoverTypeStr.Trim() == coverType.Trim())
            {
                return true;
            }
            return false;
        }
    }

    public class SearchStreetCells : DIYSQLBase
    {
        private readonly List<StreetCellInfo> streetCellLst;

        public SearchStreetCells(MainModel mModel)
            : base(mModel)
        {
            streetCellLst = new List<StreetCellInfo>();
        }

        protected override string getSqlTextString()
        {
            return "select streetName,subStreetName,netType,coverType,seqNo,LAC,CI,cellName,lastedSeconds,areaType1,areaType2,remark from tb_cfg_street_cell order by streetName,netType,coverType,seqNo";
        }

        protected override MasterCom.RAMS.Model.Interface.E_VType[] getSqlRetTypeArr()
        {
            E_VType[] type = new E_VType[12];
            type[0] = E_VType.E_String;
            type[1] = E_VType.E_String;
            type[2] = E_VType.E_String;
            type[3] = E_VType.E_String;
            type[4] = E_VType.E_Int;
            type[5] = E_VType.E_Int;
            type[6] = E_VType.E_Int;
            type[7] = E_VType.E_String;
            type[8] = E_VType.E_Float;
            type[9] = E_VType.E_String;
            type[10] = E_VType.E_String;
            type[11] = E_VType.E_String;
            return type;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            streetCellLst.Clear();
            Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    StreetCellInfo cellInfo = new StreetCellInfo();
                    cellInfo.FillForm(package.Content);
                    streetCellLst.Add(cellInfo);
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_SUCCESS)
                {
                    //
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_FAIL)
                {
                    //
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                setProgressPercent(ref index, ref progress);
            }
        }

        public List<StreetCellInfo> GetResult()
        {
            return streetCellLst;
        }
    }
}
