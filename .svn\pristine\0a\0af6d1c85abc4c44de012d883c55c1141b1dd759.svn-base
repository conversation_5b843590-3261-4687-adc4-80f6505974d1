﻿using MasterCom.RAMS.Net;
using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.BackgroundFunc
{
    public abstract class StationAcceptBaseWithWorkParams<V, U> : DIYAnalyseByCellBackgroundBaseByFile
    {
        /// <summary>
        /// 当前地市名
        /// </summary>
        protected string curDistrictName { get; set; }
        public string FileNameKeyStr { get; set; } = "";
        protected BtsAcceptWorkParamBase<U> curBtsInfo { get; set; }

        protected StationAcceptBaseWithWorkParams(MainModel mainModel) : base(mainModel)
        {
            this.isIgnoreExport = true;
            FilterSampleByRegion = false;
            FilterEventByRegion = false;
            IncludeMessage = true;
        }

        public override void DealBeforeBackgroundQueryByCity()
        {
            reportBackgroundInfo($"*****   正在初始化{Name}   *****");
            init();
            loadWorkParams();
            reportBackgroundInfo($"*****   初始化{Name}完毕   *****");
        }

        /// <summary>
        /// 初始化
        /// </summary>
        protected abstract void init();
        /// <summary>
        /// 加载工参
        /// </summary>
        protected abstract void loadWorkParams();

        protected override bool getCondition()
        {
            curDistrictName = DistrictManager.GetInstance().getDistrictName(MainModel.DistrictID).Replace("市", "");
            bool isValid = judgeWorkParams();
            return isValid;
        }

        /// <summary>
        /// 判断工参是否有效
        /// </summary>
        /// <returns></returns>
        protected abstract bool judgeWorkParams();

        protected override void query()
        {
            reportBackgroundInfo($"*****   开始[{Name}]  *****");
            if (!getCondition())
            {
                return;
            }
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
                clientProxy.Close();

                analyseCurWorkParam(clientProxy);
            }
            catch (Exception ex)
            {
                reportBackgroundError(ex);
            }
            finally
            {
                reportBackgroundInfo($"*****   [{Name}]处理完毕   *****");
            }
        }

        protected Dictionary<string, Dictionary<V, BtsAcceptWorkParamBase<U>>> workParamSumDic { get; set; }
        protected virtual void analyseCurWorkParam(ClientProxy clientProxy)
        {
            string city = curDistrictName.Replace("市", "");
            Dictionary<V, BtsAcceptWorkParamBase<U>> curDistrictWorkParam;
            if (!workParamSumDic.TryGetValue(city, out curDistrictWorkParam))
            {
                reportBackgroundInfo("工参不包含" + city + "地市的信息");
                return;
            }

            reportBackgroundInfo("读取到" + city + "包含" + curDistrictWorkParam.Count + "个站点的工参信息");
            //按工参表中的基站数进行分析
            foreach (BtsAcceptWorkParamBase<U> btsInfo in curDistrictWorkParam.Values)
            {
                if (MainModel.BackgroundStopRequest)
                {
                    return;
                }

                initCurBtsAcceptInfo();
                bool isValid = judgeValidBts(btsInfo);
                if (isValid)
                {
                    curBtsInfo = btsInfo;
                    setFileNameKeyStr(btsInfo);

                    analyseCurBts(clientProxy, btsInfo);
                }
            }
        }

        /// <summary>
        /// 初始化当前基站的单验数据
        /// </summary>
        protected virtual void initCurBtsAcceptInfo()
        {

        }

        protected virtual bool judgeValidBts(BtsAcceptWorkParamBase<U> btsInfo)
        {
            return true;
        }

        protected virtual void setFileNameKeyStr(BtsAcceptWorkParamBase<U> btsInfo)
        {
            StringBuilder strbFilter = new StringBuilder();
            //根据Log命名规则部分文件只包含基站名
            strbFilter.Append(btsInfo.BtsNameFull);
            foreach (CellAcceptWorkParamBase info in btsInfo.CellWorkParamDic.Values)
            {
                //根据Log命名规则部分文件 基站名称_小区号
                strbFilter.Append(string.Format(" or {0}_{1}", btsInfo.BtsNameFull, info.CellID));
            }
            //筛选包含基站或小区的文件
            FileNameKeyStr = strbFilter.ToString();
        }

        protected virtual void analyseCurBts(ClientProxy clientProxy, BtsAcceptWorkParamBase<U> btsInfo)
        {
            loadCurBtsWorkParam(btsInfo);

            //分析工参文件
            reportBackgroundInfo(string.Format("开始读取基站 {0} 的待分析文件...", btsInfo.BtsNameFull));
            doBackgroundStatByFile(clientProxy);

            removeCurBtsWorkParam(btsInfo);
        }

        protected virtual void loadCurBtsWorkParam(BtsAcceptWorkParamBase<U> btsInfo)
        {
            btsInfo.LoadCurBtsWorkParam(mainModel.CellManager);
        }

        protected virtual void removeCurBtsWorkParam(BtsAcceptWorkParamBase<U> btsInfo)
        {
            btsInfo.RemoveCurBtsWorkParam(mainModel.CellManager);
            MainModel.ClearDTData();
        }

        /// <summary>
        /// 获取待分析的文件信息
        /// </summary>
        protected override void getFilesForAnalyse()
        {
            BackgroundFuncQueryManager.GetInstance().GetFilterFile_CellAccept(GetSubFuncID(), ServiceTypeString
                            , ((int)carrierID).ToString(), "strfilename", FileNameKeyStr);
        }
    }
}
