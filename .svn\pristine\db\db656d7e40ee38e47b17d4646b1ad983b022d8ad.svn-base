﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class TunnelConditionDlg : BaseDialog
    {
        public TunnelConditionDlg()
            : base()
        {
            InitializeComponent();
        }

        public TunnelCondition GetCondition()
        {
            TunnelCondition cond = new TunnelCondition();
            cond.RxLevMin = (short)numMinRxLev.Value;
            cond.RsrpMin = (float)numMinRsrp.Value;
            cond.SinrMin = (float)numMinSinr.Value;
            cond.FilePath = txtExcelPath.Text;
            return cond;
        }

        public void SetCondition(TunnelCondition cond)
        {
            numMinRxLev.Value = cond.RxLevMin;
            numMinRsrp.Value = (decimal)cond.RsrpMin;
            numMinSinr.Value = (decimal)cond.SinrMin;
            txtExcelPath.Text = cond.FilePath;
        }

        private void btnExcelPath_Click(object sender, EventArgs e)
        {
            OpenFileDialog dialog = new OpenFileDialog();
            dialog.Filter = FilterHelper.Excel;
            if (dialog.ShowDialog() == DialogResult.OK)
            {
                txtExcelPath.Text = dialog.FileName;
            }
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(txtExcelPath.Text))
            {
                MessageBox.Show("请设置隧道文件信息路径");
                return;
            }
            this.DialogResult = DialogResult.OK;
        }

    }
    public class TunnelCondition
    {
        public TunnelCondition()
        {
            RxLevMin = -90;
            RsrpMin = -110;
            SinrMin = -3;
        }
        public short RxLevMin { get; set; }
        public float RsrpMin { get; set; }
        public float SinrMin { get; set; }
        public string FilePath { get; set; }
    }
}
