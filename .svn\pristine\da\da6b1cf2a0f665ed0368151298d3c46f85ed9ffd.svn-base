﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTLteNBCellCheckAnaSetForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.btnCancel = new DevExpress.XtraEditors.SimpleButton();
            this.btnOK = new DevExpress.XtraEditors.SimpleButton();
            this.labelControl5 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.numDistance = new DevExpress.XtraEditors.SpinEdit();
            this.numRSRP = new DevExpress.XtraEditors.SpinEdit();
            this.numSampleCount = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl6 = new DevExpress.XtraEditors.LabelControl();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.labelControl8 = new DevExpress.XtraEditors.LabelControl();
            this.numTimeSpan = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl7 = new DevExpress.XtraEditors.LabelControl();
            this.checkEditShowGSMNbCell = new DevExpress.XtraEditors.CheckEdit();
            ((System.ComponentModel.ISupportInitialize)(this.numDistance.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRSRP.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSampleCount.Properties)).BeginInit();
            this.groupBox1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numTimeSpan.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.checkEditShowGSMNbCell.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // btnCancel
            // 
            this.btnCancel.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnCancel.Appearance.Options.UseFont = true;
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnCancel.Location = new System.Drawing.Point(242, 221);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 41;
            this.btnCancel.Text = "取消";
            this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
            // 
            // btnOK
            // 
            this.btnOK.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnOK.Appearance.Options.UseFont = true;
            this.btnOK.Location = new System.Drawing.Point(147, 221);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 40;
            this.btnOK.Text = "确定";
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // labelControl5
            // 
            this.labelControl5.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl5.Appearance.Options.UseFont = true;
            this.labelControl5.Location = new System.Drawing.Point(244, 79);
            this.labelControl5.Name = "labelControl5";
            this.labelControl5.Size = new System.Drawing.Size(12, 12);
            this.labelControl5.TabIndex = 39;
            this.labelControl5.Text = "米";
            // 
            // labelControl4
            // 
            this.labelControl4.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl4.Appearance.Options.UseFont = true;
            this.labelControl4.Location = new System.Drawing.Point(244, 49);
            this.labelControl4.Name = "labelControl4";
            this.labelControl4.Size = new System.Drawing.Size(18, 12);
            this.labelControl4.TabIndex = 38;
            this.labelControl4.Text = "dBm";
            // 
            // labelControl3
            // 
            this.labelControl3.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl3.Appearance.Options.UseFont = true;
            this.labelControl3.Location = new System.Drawing.Point(46, 78);
            this.labelControl3.Name = "labelControl3";
            this.labelControl3.Size = new System.Drawing.Size(102, 12);
            this.labelControl3.TabIndex = 37;
            this.labelControl3.Text = "与主服小区距离 ≤";
            // 
            // labelControl2
            // 
            this.labelControl2.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl2.Appearance.Options.UseFont = true;
            this.labelControl2.Location = new System.Drawing.Point(82, 48);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(66, 12);
            this.labelControl2.TabIndex = 36;
            this.labelControl2.Text = "信号强度 ≥";
            // 
            // labelControl1
            // 
            this.labelControl1.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl1.Appearance.Options.UseFont = true;
            this.labelControl1.Location = new System.Drawing.Point(82, 19);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(66, 12);
            this.labelControl1.TabIndex = 35;
            this.labelControl1.Text = "采样点数 ≥";
            // 
            // numDistance
            // 
            this.numDistance.EditValue = new decimal(new int[] {
            1000,
            0,
            0,
            0});
            this.numDistance.Location = new System.Drawing.Point(154, 75);
            this.numDistance.Name = "numDistance";
            this.numDistance.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numDistance.Properties.Appearance.Options.UseFont = true;
            this.numDistance.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numDistance.Properties.IsFloatValue = false;
            this.numDistance.Properties.Mask.EditMask = "N00";
            this.numDistance.Properties.MaxValue = new decimal(new int[] {
            5000,
            0,
            0,
            0});
            this.numDistance.Properties.MinValue = new decimal(new int[] {
            500,
            0,
            0,
            0});
            this.numDistance.Size = new System.Drawing.Size(81, 20);
            this.numDistance.TabIndex = 34;
            // 
            // numRSRP
            // 
            this.numRSRP.EditValue = new decimal(new int[] {
            110,
            0,
            0,
            -2147483648});
            this.numRSRP.Location = new System.Drawing.Point(154, 45);
            this.numRSRP.Name = "numRSRP";
            this.numRSRP.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numRSRP.Properties.Appearance.Options.UseFont = true;
            this.numRSRP.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numRSRP.Properties.IsFloatValue = false;
            this.numRSRP.Properties.Mask.EditMask = "N00";
            this.numRSRP.Properties.MaxValue = new decimal(new int[] {
            10,
            0,
            0,
            -2147483648});
            this.numRSRP.Properties.MinValue = new decimal(new int[] {
            140,
            0,
            0,
            -2147483648});
            this.numRSRP.Size = new System.Drawing.Size(81, 20);
            this.numRSRP.TabIndex = 33;
            // 
            // numSampleCount
            // 
            this.numSampleCount.EditValue = new decimal(new int[] {
            15,
            0,
            0,
            0});
            this.numSampleCount.Location = new System.Drawing.Point(154, 15);
            this.numSampleCount.Name = "numSampleCount";
            this.numSampleCount.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numSampleCount.Properties.Appearance.Options.UseFont = true;
            this.numSampleCount.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numSampleCount.Properties.IsFloatValue = false;
            this.numSampleCount.Properties.Mask.EditMask = "N00";
            this.numSampleCount.Properties.MaxValue = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numSampleCount.Properties.MinValue = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numSampleCount.Size = new System.Drawing.Size(81, 20);
            this.numSampleCount.TabIndex = 32;
            // 
            // labelControl6
            // 
            this.labelControl6.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl6.Appearance.Options.UseFont = true;
            this.labelControl6.Location = new System.Drawing.Point(244, 19);
            this.labelControl6.Name = "labelControl6";
            this.labelControl6.Size = new System.Drawing.Size(12, 12);
            this.labelControl6.TabIndex = 42;
            this.labelControl6.Text = "次";
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.labelControl8);
            this.groupBox1.Controls.Add(this.numTimeSpan);
            this.groupBox1.Controls.Add(this.labelControl7);
            this.groupBox1.Location = new System.Drawing.Point(16, 105);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(287, 54);
            this.groupBox1.TabIndex = 43;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "CSFB下GSM邻区检测设置";
            // 
            // labelControl8
            // 
            this.labelControl8.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl8.Appearance.Options.UseFont = true;
            this.labelControl8.Location = new System.Drawing.Point(260, 26);
            this.labelControl8.Name = "labelControl8";
            this.labelControl8.Size = new System.Drawing.Size(12, 12);
            this.labelControl8.TabIndex = 44;
            this.labelControl8.Text = "秒";
            // 
            // numTimeSpan
            // 
            this.numTimeSpan.EditValue = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.numTimeSpan.Location = new System.Drawing.Point(173, 22);
            this.numTimeSpan.Name = "numTimeSpan";
            this.numTimeSpan.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numTimeSpan.Properties.Appearance.Options.UseFont = true;
            this.numTimeSpan.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numTimeSpan.Properties.IsFloatValue = false;
            this.numTimeSpan.Properties.Mask.EditMask = "N00";
            this.numTimeSpan.Properties.MaxValue = new decimal(new int[] {
            60,
            0,
            0,
            0});
            this.numTimeSpan.Properties.MinValue = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.numTimeSpan.Size = new System.Drawing.Size(81, 20);
            this.numTimeSpan.TabIndex = 44;
            // 
            // labelControl7
            // 
            this.labelControl7.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl7.Appearance.Options.UseFont = true;
            this.labelControl7.Location = new System.Drawing.Point(23, 26);
            this.labelControl7.Name = "labelControl7";
            this.labelControl7.Size = new System.Drawing.Size(144, 12);
            this.labelControl7.TabIndex = 44;
            this.labelControl7.Text = "与最近LTE采样点时间差 ≤";
            // 
            // checkEditShowGSMNbCell
            // 
            this.checkEditShowGSMNbCell.EditValue = true;
            this.checkEditShowGSMNbCell.Location = new System.Drawing.Point(14, 177);
            this.checkEditShowGSMNbCell.Name = "checkEditShowGSMNbCell";
            this.checkEditShowGSMNbCell.Properties.Caption = "只显示GSM邻区";
            this.checkEditShowGSMNbCell.Size = new System.Drawing.Size(119, 19);
            this.checkEditShowGSMNbCell.TabIndex = 44;
            // 
            // ZTLteNBCellCheckAnaSetForm
            // 
            this.AcceptButton = this.btnOK;
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.CancelButton = this.btnCancel;
            this.ClientSize = new System.Drawing.Size(329, 259);
            this.Controls.Add(this.checkEditShowGSMNbCell);
            this.Controls.Add(this.groupBox1);
            this.Controls.Add(this.labelControl6);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.Controls.Add(this.labelControl5);
            this.Controls.Add(this.labelControl4);
            this.Controls.Add(this.labelControl3);
            this.Controls.Add(this.labelControl2);
            this.Controls.Add(this.labelControl1);
            this.Controls.Add(this.numDistance);
            this.Controls.Add(this.numRSRP);
            this.Controls.Add(this.numSampleCount);
            this.Name = "ZTLteNBCellCheckAnaSetForm";
            this.Text = "邻区核查条件设置";
            ((System.ComponentModel.ISupportInitialize)(this.numDistance.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRSRP.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSampleCount.Properties)).EndInit();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numTimeSpan.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.checkEditShowGSMNbCell.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraEditors.SimpleButton btnCancel;
        private DevExpress.XtraEditors.SimpleButton btnOK;
        private DevExpress.XtraEditors.LabelControl labelControl5;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.SpinEdit numDistance;
        private DevExpress.XtraEditors.SpinEdit numRSRP;
        private DevExpress.XtraEditors.SpinEdit numSampleCount;
        private DevExpress.XtraEditors.LabelControl labelControl6;
        private System.Windows.Forms.GroupBox groupBox1;
        private DevExpress.XtraEditors.LabelControl labelControl7;
        private DevExpress.XtraEditors.LabelControl labelControl8;
        private DevExpress.XtraEditors.SpinEdit numTimeSpan;
        private DevExpress.XtraEditors.CheckEdit checkEditShowGSMNbCell;
    }
}