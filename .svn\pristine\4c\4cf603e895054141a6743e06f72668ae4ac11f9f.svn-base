﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRDIYQueryHandoverTooMuch : DIYAnalyseByFileBackgroundBase
    {
        protected List<int> statNREvtIDs = null;
        protected List<int> statLteEvtIDs = null;
        //protected List<int> statEventIDs = new List<int>();
        protected List<DTFileDataManager> dtFileDataBak = new List<DTFileDataManager>();
        protected List<NRHandoverFileDataManager> handoverFileList = new List<NRHandoverFileDataManager>();
        protected Dictionary<int, NRHandoverFileDataManager> handoverFileDic = new Dictionary<int, NRHandoverFileDataManager>();

        protected List<Event> handoverEvents = new List<Event>();

        NRHandoverTooMuchCondition curCondition = null;
        NRHandoverTooMuchDlg dlg = null;

        protected static readonly object lockObj = new object();
        private static NRDIYQueryHandoverTooMuch intance = null;
        public static NRDIYQueryHandoverTooMuch GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new NRDIYQueryHandoverTooMuch();
                    }
                }
            }
            return intance;
        }

        protected NRDIYQueryHandoverTooMuch()
            : base()
        {
            MainModel.NeedType = true;
            ServiceTypes = ServiceTypeManager.GetServiceTypesByServiceName(ServiceName.NR);

            Columns = NRTpHelper.InitBaseReplayParamBackground(true, true);
        }

        public override string Name
        {
            get
            {
                return "切换过频繁分析";
            }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 35000, 35022, this.Name);
        }

        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }
            if (dlg == null)
            {
                dlg = new NRHandoverTooMuchDlg();
                curCondition = new NRHandoverTooMuchCondition();
                dlg.SetCondition(curCondition);
            }
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                dlg.GetCondition(curCondition);

                return true;
            }
            return false;
        }

        protected override void clearDataBeforeAnalyseFiles()
        {
            handoverFileDic.Clear();
            handoverFileList.Clear();
            handoverEvents.Clear();

            initStatEventIDs();
        }

        protected virtual void initStatEventIDs()
        {
            //statEventIDs = NREventHelper.HandoverHelper.GetHandoverSuccessEvt(curCondition.IsAnaLTE);

            statLteEvtIDs = NREventHelper.HandoverHelper.GetIncludeLTEHandover();
            statNREvtIDs = NREventHelper.HandoverHelper.GetIncludeNRHandover();
        }

        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager fileDataManager in MainModel.DTDataManager.FileDataManagers)
            {
                List<Event> nrEvts = filterEvents(fileDataManager.Events, statNREvtIDs);
                List<Event> lteEvts = null;
                if (curCondition.IsAnaLTE)
                {
                    lteEvts = filterEvents(fileDataManager.Events, statLteEvtIDs);
                }

                dealEvts(fileDataManager, nrEvts);
                if (curCondition.IsAnaLTE)
                {
                    NREventHelper.HandoverHelper.FilterHandoverEvents(lteEvts);
                    dealEvts(fileDataManager, lteEvts);
                }
            }
        }

        protected void dealEvts(DTFileDataManager fileDataManager, List<Event> statEventIDs)
        {
            fileDataManager.Events = statEventIDs;
            NRHandoverFileDataManager curHandoverFile = NRHandoverAndReselectionManager.GetHandoverTooMuchResult(fileDataManager,
                  curCondition.TimeLimit, curCondition.DistanceLimit, curCondition.HandoverCount);
            if (curHandoverFile.HandoverTimes > 0)
            {
                curHandoverFile.fmnger.TestPoints = filterTestPointsByEvents(curHandoverFile.Events, fileDataManager.TestPoints);
                NRHandoverAndReselectionManager.GetHandoverToMuchDetails(curHandoverFile, curHandoverFile.fmnger);
                curHandoverFile.Index = handoverFileDic.Count + 1;
                handoverEvents.AddRange(curHandoverFile.Events);

                NRHandoverFileDataManager data;
                if (!handoverFileDic.TryGetValue(fileDataManager.FileID, out data))
                {
                    handoverFileDic.Add(fileDataManager.FileID, curHandoverFile);
                }
                else
                {
                    data.Merge(curHandoverFile);
                }
            }
        }

        protected List<Event> filterEvents(List<Event> events, List<int> statEventIDs)
        {
            List<Event> refList = new List<Event>();
            for (int eLoop = 0; eLoop < events.Count; ++eLoop)
            {
                Event e = events[eLoop];
                if (statEventIDs.Contains(e.ID))
                {
                    refList.Add(e);
                }
            }
            return refList;
        }

        //按事件过滤采样点
        protected List<TestPoint> filterTestPointsByEvents(List<Event> listEvt, List<TestPoint> listTp)
        {
            if (listTp != null && listTp.Count > 0 && listEvt != null && listEvt.Count > 1)
            {
                List<TestPoint> list = new List<TestPoint>();
                DateTime dtStart = listEvt[0].DateTime.AddSeconds(-10);
                DateTime dtEnd = listEvt[listEvt.Count - 1].DateTime.AddSeconds(5);

                foreach (TestPoint tp in listTp)
                {
                    if (tp.DateTime > dtEnd)
                        break;
                    if (tp.DateTime >= dtStart)
                        list.Add(tp);
                }

                return list;
            }
            return listTp;
        }

        protected override void fireShowForm()
        {
            handoverFileList = new List<NRHandoverFileDataManager>(handoverFileDic.Values);
            NRHandoverTooMuchForm frm = null;
            frm = MainModel.CreateResultForm(typeof(NRHandoverTooMuchForm)) as NRHandoverTooMuchForm;
            frm.FillData(handoverFileList, handoverEvents);
            frm.Visible = true;
            frm.BringToFront();
        }
    }
}
