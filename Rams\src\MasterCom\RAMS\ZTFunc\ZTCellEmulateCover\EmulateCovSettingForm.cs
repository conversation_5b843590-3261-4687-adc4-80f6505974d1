﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class EmulateCovSettingForm : BaseForm
    {
        public EmulateCovSettingForm()
        {
            InitializeComponent();

            cbxWay.Items.Add("简单工参仿真");
            cbxWay.Items.Add("传播模型仿真");
            cbxWay.SelectedIndex = 0;
        }

        public double getCovDistance()
        {
            return (double)this.numUDDistance.Value;
        }

        public bool getIsDrawPoints()
        {
            return this.chkDrawPoints.Checked;
        }

        public void queryAllArea()
        {
            this.numUDDistance.Enabled = false;
        }

        public int getPt()
        {
            return (int)this.numUDPt.Value;
        }

        public int getPr()
        {
            return (int)this.numUDPr.Value;
        }

        public bool getIsIdealCellCov()
        {
            if (cbxWay.SelectedIndex==0)
                return true;
            else
                return false;
        }

        private void cbxWay_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (cbxWay.SelectedIndex==0)
            {
                numUDPt.Enabled = false;
                numUDPr.Enabled = false;
            }
            else
            {
                numUDPt.Enabled = true;
                numUDPr.Enabled = true;
            }
        }
    }
}
