﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;

namespace MasterCom.RAMS.Func.PopShow
{
    public partial class ESInfoPanel : UserControl, PopShowPanelInterface
    {
        public const byte REQTYPE_TABLE_DATA_INFO = 0x83; //REQUEST 
        public const byte RESTYPE_TABLE_DATA_INFO = 0x83; 

        private MainModel MainModel;
        public ESInfoPanel()
        {
            InitializeComponent();
        }

        #region PopShowPanelInterface 成员

        public void RunQuery(BackgroundWorker worker, TaskInfo task)
        {
            List<PopESEvent> popEvents = queryPopESEventsFrom(worker, MainModel.User.DBID);
            Dictionary<string, List<PopESEvent>> taskEventsDic = new Dictionary<string, List<PopESEvent>>();
            foreach(PopESEvent pes in popEvents)
            {
                string taskName = pes.strtaskname;
                List<PopESEvent> taskEvents = null;
                if(!taskEventsDic.TryGetValue(taskName,out taskEvents))
                {
                    taskEvents = new List<PopESEvent>();
                    taskEventsDic[taskName] = taskEvents;
                }
                taskEvents.Add(pes);
            }
            task.retResultInfo = taskEventsDic;
        }
        
        private List<PopESEvent> queryPopESEventsFrom(BackgroundWorker worker, int dbid)
        {
            List<PopESEvent> retList = new List<PopESEvent>();
            ClientProxy clientProxy = new ClientProxy();

            if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
            {
                worker.ReportProgress(99, "连接服务器端出错！");
                return retList;
            }
            try
            {
                Package package = clientProxy.Package;
                package.Command = Command.InfoQuery;
                package.SubCommand = SubCommand.Request;
                package.Content.Type = REQTYPE_TABLE_DATA_INFO;
                package.Content.PrepareAddParam();
                package.Content.AddParam(dbid);
                package.Content.AddParam("tb_pop_esinsight");
                clientProxy.Send();
                while (true)
                {
                    clientProxy.Recieve();
                    if (package.Content.Type == RESTYPE_TABLE_DATA_INFO)
                    {
                        package.Content.PrepareGetParam();
                        PopESEvent retItem = PopESEvent.ReadResultItemFrom(package.Content);
                        if (dbid == -1)//若为多地市的，需要将片区名修改为地市名
                        {
                            retItem.strname = DistrictManager.GetInstance().getDistrictName(retItem.dbid);
                            retList.Add(retItem);
                        }
                        else
                        {
                            retList.Add(retItem);
                        }
                    }
                    else if (package.Content.Type == ResponseType.END)
                    {
                        break;
                    }
                }
                return retList;
            }

            finally
            {
                clientProxy.Close();
            }
        }

        #endregion

        #region PopShowPanelInterface 成员


        public void FireFreshShowData(TaskInfo task)
        {
            if (task.retResultInfo is Dictionary<string, List<PopESEvent>>)
            {
                Dictionary<string, List<PopESEvent>> taskEventsDic = task.retResultInfo as Dictionary<string, List<PopESEvent>>;
                if (taskEventsDic.Count == 0)
                {
                    return;
                }
                int totalHeight = 0;
                this.tableLayoutMain.Controls.Clear();
                this.tableLayoutMain.RowCount = taskEventsDic.Count;
                foreach (string taskname in taskEventsDic.Keys)
                {
                    List<PopESEvent> list = taskEventsDic[taskname];
                    ESTaskItemPanel taskPanel = new ESTaskItemPanel();
                    taskPanel.FillInfo(taskname, list);
                    totalHeight += taskPanel.Height + 10;
                    this.tableLayoutMain.Controls.Add(taskPanel);
                    taskPanel.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
                }
                this.tableLayoutMain.Height = totalHeight + 20;
                this.Height = this.tableLayoutMain.Height + 20;
            }
        }

        #endregion

        #region PopShowPanelInterface 成员


        public void SetMainModal(MasterCom.RAMS.Model.MainModel mm, WelcomForm welcomform)
        {
            this.MainModel = mm;
        }

        #endregion
    }
    internal class PopESEvent
    {
        public int dbid;
        public string strname;
        public string strtaskname;
        public string strbranch;
        public int ifileid;
        public int iprojecttype;
        public int iseqid;
        public int itime;
        public short wtimems;
        public byte bms;
        public int iEventID;
        public int ilongitude;
        public int ilatitude;
        public int icqtposid;
        public int iLAC;
        public short wRAC;
        public int iCI;
        public int iTargetLAC;
        public short wTargetRAC;
        public int iTargetCI;
        public int[] ivalues = new int[10];
        public int ianatime;
        public string pretype_desc;
        public string filaname;
        public string reason_desc;
        public int iservice;
        public string sampletbname;
        internal static PopESEvent ReadResultItemFrom(Content content)
        {
            PopESEvent evt = new PopESEvent();
            evt.dbid = content.GetParamInt();
            evt.strname = content.GetParamString();
            evt.strtaskname = content.GetParamString();
            evt.strbranch = content.GetParamString();
            evt.ifileid = content.GetParamInt();
            evt.iprojecttype = content.GetParamInt();
            evt.iseqid = content.GetParamInt();
            evt.itime = content.GetParamInt();
            evt.wtimems = content.GetParamShort();
            evt.bms = content.GetParamByte();
            evt.iEventID = content.GetParamInt();
            evt.ilongitude = content.GetParamInt();
            evt.ilatitude = content.GetParamInt();
            evt.icqtposid = content.GetParamInt();
            evt.iLAC = content.GetParamInt();
            evt.wRAC = content.GetParamShort();
            evt.iCI = content.GetParamInt();
            evt.iTargetLAC = content.GetParamInt();
            evt.wTargetRAC = content.GetParamShort();
            evt.iTargetCI = content.GetParamInt();
/*Need2BePerfect_Qiujianwei
     * value8 value10问题
     */ 
            for(int i=0;i<10;i++)
            {
                evt.ivalues[i] = content.GetParamInt();
            }
            evt.ianatime = content.GetParamInt();
            evt.pretype_desc = content.GetParamString();
            evt.filaname = content.GetParamString();
            evt.iservice = content.GetParamInt();
            evt.sampletbname = content.GetParamString();
            StringBuilder sbDesc = new StringBuilder();
            for (int i = 0; i < 5; i++)
            {
                sbDesc.Append(content.GetParamString());
            }
            evt.reason_desc = sbDesc.ToString();
            return evt;
        }

        public Event ConvertToEvent()
        {
            Event evtCur = new Event();
            evtCur.Time = itime;

            DTDataHeader header = new DTDataHeader();
            header.ID = ifileid;
            header.Name = filaname;
            header.ProjectID = iprojecttype;
            header.ServiceType = iservice;
            DateTime dt = JavaDate.GetDateTimeFromMilliseconds(1000L * itime);
            header.LogTable = string.Format("tb_log_file_{0}_{1:D2}", dt.Year, dt.Month);
            header.SampleTbName = sampletbname;
            evtCur.ApplyHeader(header);

            evtCur.ID = iEventID;
            evtCur.Longitude = 0.0000001 * ilongitude;
            evtCur.Latitude = 0.0000001 * ilatitude;
            evtCur.Millisecond = wtimems;
            evtCur.SN = iseqid;

            return evtCur;
        }

    };
}
