﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.Stat
{
    public class ShowNoGisBatExpRptForm : QueryBase
    {
        public ShowNoGisBatExpRptForm()
            : base(MainModel.GetInstance())
        {
        }
        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 11000, 11045, this.Name);
        }
        public override bool IsNeedSetQueryCondition
        {
            get
            {
                return false;
            }
        }

        public override string Name
        {
            get { return "打开一键导出报表窗体"; }
        }

        public override string IconName
        {
            get { return null; }
        }

        protected override bool isValidCondition()
        {
            return true;
        }

        protected override void query()
        {
            NoGisBatExpRptForm.Instance.Visible = true;
            NoGisBatExpRptForm.Instance.WindowState = System.Windows.Forms.FormWindowState.Normal;
            NoGisBatExpRptForm.Instance.BringToFront();
        }
    }
}
