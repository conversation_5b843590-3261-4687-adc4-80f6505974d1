﻿using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc.ZTHighReverseFlowCoverage;
using MasterCom.RAMS.ZTFunc.ZTHighReverseFlowCoverage.HighReverseFlowCoverageImport;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class HighReverseFlowCoverageImport : QueryBase
    {
        public HighReverseFlowCoverageImport()
            : base()
        {
        }

        public override bool IsNeedSetQueryCondition { get { return false; } }

        public override string Name
        {
            get { return "4G-5G高倒流共覆盖工参入库"; }
        }

        ImportCondition curCondition = new ImportCondition();
        private ImportExcelDataHelper import = null;
        protected override bool isValidCondition()
        {
            bool isValid = loadConfig();
            if (!isValid)
            {
                return false;
            }

            var dlg = new HighReverseFlowCoverageImportDlg(import);
            dlg.SetCondition(curCondition);
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                curCondition = dlg.GetCondition();
                HighReverseFlowCoverageConfig.Instance.SaveConfig(curCondition.Condition);
                return true;
            }
            return false;
        }

        private bool loadConfig()
        {
            if (import == null)
            {
                curCondition.Condition = HighReverseFlowCoverageConfig.Instance.LoadConfig();
                if (!string.IsNullOrEmpty(HighReverseFlowCoverageConfig.Instance.ErrMsg))
                {
                    HighReverseFlowCoverageConfig.Instance.WriteLogWithMsgBox(HighReverseFlowCoverageConfig.Instance.ErrMsg);
                    return false;
                }
                else
                {
                    import = new ImportExcelDataHelper(Name);
                }
            }
            return true;
        }

        protected override void query()
        {
            try
            {
                HighReverseFlowCoverageConfig.Instance.WriteLog($"开始读取{Name}数据...", "info");
                WaitBox.Show("正在读取数据...", loadData);

                HighReverseFlowCoverageConfig.Instance.WriteLog($"读取到NR数据[{import.NrCellList.Count}]条" +
                    $",读取到LTE数据[{import.LteCellList.Count}]条", "info");

                HighReverseFlowCoverageConfig.Instance.WriteLog($"开始导入{Name}数据...", "info");
                import.SqlConnect = curCondition.Condition.DBCond.SqlConnect;
                import.ImportDataToDB();

                MessageBox.Show("导入成功");
                HighReverseFlowCoverageConfig.Instance.WriteLog($"导入完毕", "info");
            }
            catch (Exception ex)
            {
                HighReverseFlowCoverageConfig.Instance.WriteLogWithMsgBox($"导入失败:{ex}");
            }
            finally
            {
                import.Clear();
            }
        }

        private void loadData()
        {
            import.LoadImportData(curCondition.Path);
            WaitBox.Close();
        }
    }
}
