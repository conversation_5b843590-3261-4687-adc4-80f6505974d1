﻿using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Data;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTConvergeEventToOrder : QueryBase
    {
        public ZTConvergeEventToOrder()
            : base()
        {
        }

        public override string Name
        {
            get { return "北京事件列表汇聚生成派发工单"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 20000, 20056, this.Name);
        }

        readonly ZTConvergeEventToOrderDlg dlg = new ZTConvergeEventToOrderDlg();
        ZTConvergeEventToOrderDlg.SettingCondition settingCondition;
        //excel中读出的事件信息
        List<EventInfo> evtList;

        protected override bool isValidCondition()
        {
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                settingCondition = dlg.Condition;
                return true;
            }
            return false;
        }

        protected override void query()
        {
            //1.读取事件Excel
            bool readSuccess = readExcel();
            if (readSuccess)
            {
                //2.按分公司、具体场景和问题类型汇聚数据
                convergeData();

                //3.导出工单Excel
                exportExcel();
            }
        }

        protected bool readExcel()
        {
            try
            {
                DataSet ds = ExcelNPOIManager.ImportFromExcel(settingCondition.FilePath);
                if (ds != null && ds.Tables != null)
                {
                    evtList = getEventListInfo(ds);
                    if (evtList.Count == 0)
                    {
                        MessageBox.Show(string.Format("{0}文件为空", settingCondition.FilePath));
                        return false;
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(string.Format("{0}读取出错,{1}", settingCondition.FilePath, ex.Message));
                return false;
            }

            return true;
        }

        private List<EventInfo> getEventListInfo(DataSet ds)
        {
            List<EventInfo> evtListTmp = new List<EventInfo>();
            foreach (DataTable dt in ds.Tables)
            {
                foreach (DataRow dr in dt.Rows)
                {
                    EventInfo info = new EventInfo();
                    if (info.FillDataByExcel(dr))
                    {
                        evtListTmp.Add(info);
                    }
                }
            }
            return evtListTmp;
        }

        //汇聚后的问题点信息
        Dictionary<string, ProblemPointInfo> problemPointInfoDic;
        protected virtual void convergeData()
        {
            int id = 1;
            problemPointInfoDic = new Dictionary<string, ProblemPointInfo>();
            foreach (var evtInfo in evtList)
            {
                //将事件信息处理一下,看是否包含有效的信息
                ProblemPointInfo info = new ProblemPointInfo();
                if (info.SetBaseData(evtInfo))
                {
                    addProblemPointInfoDic(ref id, evtInfo, info);
                }
            }
        }

        private void addProblemPointInfoDic(ref int id, EventInfo evtInfo, ProblemPointInfo info)
        {
            ProblemPointInfo savedInfo;
            if (!problemPointInfoDic.TryGetValue(info.Token, out savedInfo))
            {
                savedInfo = info;
                savedInfo.ID = id++;
                savedInfo.OrderCreateTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                savedInfo.ProblemTime = evtInfo.Time;
                problemPointInfoDic.Add(savedInfo.Token, savedInfo);
            }
            else
            {
                char[] splitChar = new char[] { ';', '；' };
                string[] logNameAry = info.LogNames.Split(splitChar);
                foreach (var logName in logNameAry)
                {
                    if (!savedInfo.LogNames.Contains(logName))
                    {
                        savedInfo.LogNames = string.Format(savedInfo.LogNames + "," + logName);
                    }
                }
            }
        }

        protected virtual void exportExcel()
        {
            try
            {
                List<NPOIRow> rowList = getNPOIRow();
                ExcelNPOIManager.ExportToExcel(rowList);
            }
            catch(Exception ex)
            {
                MessageBox.Show(string.Format("导出异常:{0}", ex.Message));
            }
        }

        private List<NPOIRow> getNPOIRow()
        {
            List<NPOIRow> rowList = new List<NPOIRow>();
            //row为标题
            NPOIRow titleRow = new NPOIRow();
            titleRow.AddCellValue("分公司");
            titleRow.AddCellValue("工单派发时间");
            titleRow.AddCellValue("问题点ID");
            titleRow.AddCellValue("场景");
            titleRow.AddCellValue("具体场景");
            titleRow.AddCellValue("问题类型");
            titleRow.AddCellValue("问题点描述");
            titleRow.AddCellValue("道路名称");
            titleRow.AddCellValue("经度");
            titleRow.AddCellValue("纬度");
            titleRow.AddCellValue("LOG名称");
            titleRow.AddCellValue("问题点开始时间");
            rowList.Add(titleRow);

            foreach (var info in problemPointInfoDic)
            {
                NPOIRow row = new NPOIRow();
                fillRow(ref row, info.Value);
                rowList.Add(row);
            }
            return rowList;
        }

        private void fillRow(ref NPOIRow row, ProblemPointInfo info)
        {
            if (row == null || info == null)
                return;
            row.AddCellValue(info.Company);
            row.AddCellValue(info.OrderCreateTime);
            row.AddCellValue(info.ID);
            row.AddCellValue(info.Area);
            row.AddCellValue(info.DetailArea);
            row.AddCellValue(info.ProblemType);
            row.AddCellValue(info.ProblemDesc);
            row.AddCellValue(info.RoadName);
            row.AddCellValue(info.Longitude);
            row.AddCellValue(info.Latitude);
            row.AddCellValue(info.LogNames);
            row.AddCellValue(info.ProblemTime);
        }

        class EventInfo
        {
            public string Time { get; set; }
            public string EventType { get; set; }
            public string LogName { get; set; }
            public string Longitude { get; set; }
            public string Latitude { get; set; }

            public bool FillDataByExcel(DataRow dr)
            {
                try
                {
                    DateTime dt;
                    bool convertResult = ConvertDateTime(dr[0].ToString(), out dt);
                    if (!convertResult)
                    {
                        Time = "";
                    }
                    else
                    {
                        Time = dt.ToString("yyyy-MM-dd HH:mm:ss");
                    }
                    EventType = dr[1].ToString();
                    LogName = dr[2].ToString();
                    Longitude = dr[3].ToString();
                    Latitude = dr[4].ToString();
                }
                catch
                {
                    return false;
                }
                return true;
            }

            private bool ConvertDateTime(string str, out DateTime dt)
            {
                string[] dateFormates = { "yyyy-MM-dd", "MM/yyyy/dd", "yyyyMMdd", "yyyy/MM/dd", "yyyy/M/dd", "yyyy/M/d", "M/d/yyyy h/mm/ss", "M/d/yyyy h:mm:ss tt", "M/d/yyyy", "yyyy/m/d h:mm", "yyyy!/m!/d h:mm" };           
                if (DateTime.TryParse(str, out dt))
                {
                    return true;
                }
                else if (DateTime.TryParseExact(str, dateFormates, System.Globalization.CultureInfo.InvariantCulture, System.Globalization.DateTimeStyles.None, out dt))//格式比较特殊,尝试自行转换时间
                {
                    return true;
                }
                else
                {
                    return false;
                }
            }
        }

        class ProblemPointInfo
        {
            public string Token { get; set; }

            public string Company { get; set; }
            public string OrderCreateTime { get; set; }
            public int ID { get; set; }
            public string Area { get; set; } = "";
            public string DetailArea { get; set; }
            public string ProblemType { get; set; }
            public string ProblemDesc { get; set; }
            public string RoadName { get; set; } = "";
            public string Longitude { get; set; }
            public string Latitude { get; set; }
            public string LogNames { get; set; }
            public string ProblemTime { get; set; }

            public bool SetBaseData(EventInfo evtInfo)
            {
                if (string.IsNullOrEmpty(evtInfo.LogName))
                {
                    return false;
                }

                string logName;
                if (evtInfo.EventType == "三方竞对")
                {
                    //多个文件取第一个
                    char[] splitChar = new char[] { ';', '；' };
                    string[] logArray = evtInfo.LogName.Split(splitChar);
                    logName = logArray[0];
                    dealSpecialValue(evtInfo);
                }
                else
                {
                    logName = evtInfo.LogName;

                    Longitude = evtInfo.Longitude;
                    Latitude = evtInfo.Latitude;
                }
            
                string[] logInfoArray = logName.Split('_');
                if (logInfoArray.Length < 2)
                {
                    return false;
                }
                Company = logInfoArray[0].Replace("CQT", "");
                DetailArea = logInfoArray[1];

                LogNames = evtInfo.LogName;
                ProblemType = evtInfo.EventType;
                ProblemDesc = ProblemType;
                Token = Company + "_" + DetailArea + "_" + ProblemType;
                return true;
            }

            private void dealSpecialValue(EventInfo evtInfo)
            {
                //三方竞对的经纬度格式为 经度_纬度, 经度_纬度...  (放在经度的单元格内)
                //只取第一个经纬度
                char[] splitChar = new char[] { ',', '，' };
                string[] coordinateArray = evtInfo.Longitude.Split(splitChar);
                if (coordinateArray.Length > 0)
                {
                    string coordinate = coordinateArray[0];
                    string[] valueArray = coordinate.Split('_');
                    if (valueArray.Length == 2)
                    {
                        Longitude = valueArray[0];
                        Latitude = valueArray[1];
                    }
                }
            }
        }
    }
}
