﻿using System;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTNoCoverRoadSetForm_TDScan : Form
    {
        public ZTNoCoverRoadSetForm_TDScan()
        {
            InitializeComponent();
        }

        public static ZTNoCoverRoadSetForm_TDScan GetDlg()
        {
            return dlg;
        }
        private static ZTNoCoverRoadSetForm_TDScan dlg = new ZTNoCoverRoadSetForm_TDScan();

        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        public int PcchThreshold
        {
            get { return int.Parse(numPccpchRscpThreshold.Value.ToString()); }
        }

        public int MinDistance
        {
            get { return int.Parse(numDistance.Value.ToString()); }
        }

        public int MaxDistance
        {
            get { return int.Parse(numMaxDistance.Value.ToString()); }
        }

        public int MergeDistance
        {
            get { return int.Parse(numMergeDistance.Value.ToString()); }
        }

        public bool MergeCheckState
        {
            get { return checkBoxMerge.CheckState == CheckState.Checked; }
        }

        private void checkBoxMerge_CheckStateChanged(object sender, EventArgs e)
        {
            if (checkBoxMerge.CheckState == CheckState.Checked)
            {
                numMergeDistance.Enabled = true;
            }
            else
            {
                numMergeDistance.Enabled = false;
            }
        }
    }
}