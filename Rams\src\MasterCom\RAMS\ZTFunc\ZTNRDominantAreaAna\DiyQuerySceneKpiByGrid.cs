﻿using MasterCom.RAMS.Func;
using MasterCom.RAMS.Grid;
using MasterCom.RAMS.KPI_Statistics;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTNRDominantAreaAna
{
    public class DiyQuerySceneKpiByGrid : QueryKPIStatBase
    {
        public SceneKpiInfo Info { get; set; } = new SceneKpiInfo();
        readonly int districtID;
        List<StatDataHubBase> dataList;

        public DiyQuerySceneKpiByGrid(int districtID)
            : base(MainModel.GetInstance())
        {
            this.districtID = districtID;
        }

        public override string Name
        {
            get { return "5G优势区域按栅格查询指标"; }
        }

        protected override StatTbToken getTableNameToken()
        {
            return StatTbToken.grid;
        }

        protected override void query()
        {
            ClientProxy clientProxy = new ClientProxy();
            if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, districtID) != ConnectResult.Success)
            {
                ErrorInfo = "连接服务器失败!";
                return;
            }
            try
            {
                queryInThread(clientProxy);
            }
            finally
            {
                clientProxy.Close();
            }

            changGridDataFormula();
        }

        protected override void queryInThread(object o)
        {
            try
            {
                ClientProxy clientProxy = (ClientProxy)o;
                Info = new SceneKpiInfo();
                dataList = new List<StatDataHubBase>();
                WaitBox.CanCancel = true;

                string statImgIDSet = this.getStatImgNeededTriadID();
                foreach (TimePeriod period in condition.Periods)
                {
                    queryPeriodInfo(period, clientProxy, statImgIDSet);
                }
            }
            catch (Exception e)
            {
                System.Windows.Forms.MessageBox.Show("Error" + e.StackTrace + e.ToString());
            }
        }

        protected override string getStatImgNeededTriadID(params object[] paramSet)
        {
            return getTriadIDIgnoreServiceType(NRDominantAreaImage.GetImage());
        }

        /// <summary>
        /// 查询某时间段内的数据
        /// </summary>
        /// <param name="period">当该参数为null时，视为按轮查询</param>
        /// <param name="clientProxy"></param>
        /// <param name="package"></param>
        protected override void queryPeriodInfo(TimePeriod period, ClientProxy clientProxy, params object[] reservedParams)
        {
            isQueringEvent = false;
            preparePackageBasicContent(clientProxy.Package, period);
            preparePackageNeededInfo_StatImg(clientProxy.Package, reservedParams[0]);
            clientProxy.Send();
            recieveInfo_ImgGrid(clientProxy, null);

            afterRecieveOnePeriodData();
        }

        protected override void AddGeographicFilter(Package package)
        {
            AddDIYRegion_Intersect(package);
        }

        protected override void recieveInfo_ImgGrid(ClientProxy clientProxy, params object[] reservedParams)
        {
            Package package = clientProxy.Package;
            int counter = 0;
            int curPercent = 11;
            DTDataHeaderManager.GetInstance();
            List<StatImgDefItem> curImgColumnDef = new List<StatImgDefItem>();
            List<ColumnDefItem> fileHeaderColumnDef = new List<ColumnDefItem>();
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                KPIStatDataBase singleStatData;
                if (isFileHeaderContentType(package.Content.Type))
                {
                    recieveFileHeader(clientProxy.DbID, package.Content, fileHeaderColumnDef);
                }
                else if (isImgColDefContent(package, curImgColumnDef))
                {
                    //
                }
                else if (isKPIDataContent(package, out singleStatData))
                {
                    fillData(package, curImgColumnDef, singleStatData);
                }
                else
                {
                    break;
                }
                setProgressPercent(ref counter, ref curPercent);
            }
        }

        private void fillData(Package package, List<StatImgDefItem> curImgColumnDef, KPIStatDataBase singleStatData)
        {
            double lng = package.Content.GetParamDouble();
            double lat = package.Content.GetParamDouble();
            if (isValidStatImg(lng, lat))
            {
                fillStatData(package, curImgColumnDef, singleStatData);
                StatDataHubBase data = new StatDataHubBase();
                data.AddStatData(singleStatData, false);
                dataList.Add(data);
            }
        }

        protected bool isValidStatImg(double lng, double lat)
        {
            GridUnitBase grid = new GridUnitBase(lng, lat);
            return condition.Geometorys.GeoOp.ContainsRectCenter(grid.Bounds);
        }

        private void changGridDataFormula()
        {
            foreach (StatDataHubBase data in dataList)
            {
                Info.AddData(data);
            }
        }
    }
}
