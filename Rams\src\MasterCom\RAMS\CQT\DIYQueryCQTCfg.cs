﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.CQT
{
    public class DIYQueryCQTCfg : DIYSQLBase
    {
        public DIYQueryCQTCfg(MainModel mm)
            : base(mm)
        { MainDB = true; }
        protected override void query()
        {
            results = new List<CQTCfgTypeBase>();
            base.query();
        }
        string sqlTxt = string.Empty;
        public void SetSQLTxt(string sqlStr)
        {
            sqlTxt = sqlStr;
        }
        protected override string getSqlTextString()
        {
            return sqlTxt;
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[3];
            rType[0] = E_VType.E_Int;
            rType[1] = E_VType.E_String;
            rType[2] = E_VType.E_String;
            return rType;
        }
        private List<CQTCfgTypeBase> results;
        public List<CQTCfgTypeBase> GetResults()
        {
            return results;
        }
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    results.Add(CQTCfgTypeBase.FillFrom(package.Content));
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL_SUCCESS)
                {
                    //
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL_FAIL)
                {
                    //
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }

        public override string Name
        {
            get { return "CQT基础类型信息查询"; }
        }
    }
}
