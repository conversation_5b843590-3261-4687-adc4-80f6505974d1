﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class NRScanHighCoverateRoadDlg
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.groupControl2 = new DevExpress.XtraEditors.GroupControl();
            this.spinEditMaxDiff = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.chbAbslute = new System.Windows.Forms.CheckBox();
            this.labelControl10 = new DevExpress.XtraEditors.LabelControl();
            this.chbRelative = new System.Windows.Forms.CheckBox();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.absValue = new DevExpress.XtraEditors.SpinEdit();
            this.spinEditCoverage = new DevExpress.XtraEditors.SpinEdit();
            this.absCoverate = new DevExpress.XtraEditors.SpinEdit();
            this.label15 = new System.Windows.Forms.Label();
            this.label16 = new System.Windows.Forms.Label();
            this.labelControl6 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl8 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl7 = new DevExpress.XtraEditors.LabelControl();
            this.spinEditRoadPercent = new DevExpress.XtraEditors.SpinEdit();
            this.spinEditRoadDistance = new DevExpress.XtraEditors.SpinEdit();
            this.spinEditSampleDistance = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl9 = new DevExpress.XtraEditors.LabelControl();
            this.groupControl3 = new DevExpress.XtraEditors.GroupControl();
            this.groupControl4 = new DevExpress.XtraEditors.GroupControl();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.spinEditRxlevMin = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl5 = new DevExpress.XtraEditors.LabelControl();
            this.btnCancel = new DevExpress.XtraEditors.SimpleButton();
            this.btnOK = new DevExpress.XtraEditors.SimpleButton();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).BeginInit();
            this.groupControl2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditMaxDiff.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.absValue.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditCoverage.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.absCoverate.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditRoadPercent.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditRoadDistance.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditSampleDistance.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl3)).BeginInit();
            this.groupControl3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl4)).BeginInit();
            this.groupControl4.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditRxlevMin.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // groupControl1
            // 
            this.groupControl1.Controls.Add(this.labelControl4);
            this.groupControl1.Controls.Add(this.spinEditRxlevMin);
            this.groupControl1.Controls.Add(this.labelControl5);
            this.groupControl1.Controls.Add(this.groupControl4);
            this.groupControl1.Controls.Add(this.groupControl3);
            this.groupControl1.Location = new System.Drawing.Point(0, 0);
            this.groupControl1.Name = "groupControl1";
            this.groupControl1.Size = new System.Drawing.Size(551, 203);
            this.groupControl1.TabIndex = 0;
            this.groupControl1.Text = "指标";
            // 
            // groupControl2
            // 
            this.groupControl2.Controls.Add(this.label15);
            this.groupControl2.Controls.Add(this.label16);
            this.groupControl2.Controls.Add(this.labelControl6);
            this.groupControl2.Controls.Add(this.labelControl8);
            this.groupControl2.Controls.Add(this.labelControl7);
            this.groupControl2.Controls.Add(this.spinEditRoadPercent);
            this.groupControl2.Controls.Add(this.spinEditRoadDistance);
            this.groupControl2.Controls.Add(this.spinEditSampleDistance);
            this.groupControl2.Controls.Add(this.labelControl9);
            this.groupControl2.Location = new System.Drawing.Point(0, 203);
            this.groupControl2.Name = "groupControl2";
            this.groupControl2.Size = new System.Drawing.Size(551, 110);
            this.groupControl2.TabIndex = 1;
            this.groupControl2.Text = "持续性";
            // 
            // spinEditMaxDiff
            // 
            this.spinEditMaxDiff.EditValue = new decimal(new int[] {
            6,
            0,
            0,
            0});
            this.spinEditMaxDiff.Location = new System.Drawing.Point(215, 36);
            this.spinEditMaxDiff.Name = "spinEditMaxDiff";
            this.spinEditMaxDiff.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditMaxDiff.Properties.Appearance.Options.UseFont = true;
            this.spinEditMaxDiff.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditMaxDiff.Properties.IsFloatValue = false;
            this.spinEditMaxDiff.Properties.Mask.AutoComplete = DevExpress.XtraEditors.Mask.AutoCompleteType.None;
            this.spinEditMaxDiff.Properties.Mask.EditMask = "N00";
            this.spinEditMaxDiff.Properties.MaxValue = new decimal(new int[] {
            30,
            0,
            0,
            0});
            this.spinEditMaxDiff.Size = new System.Drawing.Size(82, 20);
            this.spinEditMaxDiff.TabIndex = 34;
            // 
            // labelControl3
            // 
            this.labelControl3.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl3.Appearance.Options.UseFont = true;
            this.labelControl3.Location = new System.Drawing.Point(343, 40);
            this.labelControl3.Name = "labelControl3";
            this.labelControl3.Size = new System.Drawing.Size(78, 12);
            this.labelControl3.TabIndex = 38;
            this.labelControl3.Text = "重叠覆盖度 ≥";
            // 
            // chbAbslute
            // 
            this.chbAbslute.AutoSize = true;
            this.chbAbslute.Location = new System.Drawing.Point(50, 37);
            this.chbAbslute.Name = "chbAbslute";
            this.chbAbslute.Size = new System.Drawing.Size(150, 16);
            this.chbAbslute.TabIndex = 42;
            this.chbAbslute.Text = "绝对覆盖带：信号强度>";
            this.chbAbslute.UseVisualStyleBackColor = true;
            this.chbAbslute.CheckedChanged += new System.EventHandler(this.chbAbslute_CheckedChanged);
            // 
            // labelControl10
            // 
            this.labelControl10.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl10.Appearance.Options.UseFont = true;
            this.labelControl10.Location = new System.Drawing.Point(343, 40);
            this.labelControl10.Name = "labelControl10";
            this.labelControl10.Size = new System.Drawing.Size(78, 12);
            this.labelControl10.TabIndex = 39;
            this.labelControl10.Text = "重叠覆盖度 ≥";
            // 
            // chbRelative
            // 
            this.chbRelative.AutoSize = true;
            this.chbRelative.Checked = true;
            this.chbRelative.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chbRelative.Location = new System.Drawing.Point(14, 39);
            this.chbRelative.Name = "chbRelative";
            this.chbRelative.Size = new System.Drawing.Size(186, 16);
            this.chbRelative.TabIndex = 43;
            this.chbRelative.Text = "相对覆盖带：与最强信号差异<";
            this.chbRelative.UseVisualStyleBackColor = true;
            this.chbRelative.CheckedChanged += new System.EventHandler(this.chbRelative_CheckedChanged);
            // 
            // labelControl1
            // 
            this.labelControl1.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl1.Appearance.Options.UseFont = true;
            this.labelControl1.Location = new System.Drawing.Point(303, 40);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(12, 12);
            this.labelControl1.TabIndex = 40;
            this.labelControl1.Text = "dB";
            // 
            // labelControl2
            // 
            this.labelControl2.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl2.Appearance.Options.UseFont = true;
            this.labelControl2.Location = new System.Drawing.Point(303, 40);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(12, 12);
            this.labelControl2.TabIndex = 41;
            this.labelControl2.Text = "dB";
            // 
            // absValue
            // 
            this.absValue.EditValue = new decimal(new int[] {
            110,
            0,
            0,
            -2147483648});
            this.absValue.Enabled = false;
            this.absValue.Location = new System.Drawing.Point(215, 35);
            this.absValue.Name = "absValue";
            this.absValue.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.absValue.Properties.Appearance.Options.UseFont = true;
            this.absValue.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.absValue.Properties.IsFloatValue = false;
            this.absValue.Properties.Mask.AutoComplete = DevExpress.XtraEditors.Mask.AutoCompleteType.None;
            this.absValue.Properties.Mask.EditMask = "N00";
            this.absValue.Properties.MaxValue = new decimal(new int[] {
            30,
            0,
            0,
            0});
            this.absValue.Size = new System.Drawing.Size(82, 20);
            this.absValue.TabIndex = 35;
            // 
            // spinEditCoverage
            // 
            this.spinEditCoverage.EditValue = new decimal(new int[] {
            4,
            0,
            0,
            0});
            this.spinEditCoverage.Location = new System.Drawing.Point(437, 36);
            this.spinEditCoverage.Name = "spinEditCoverage";
            this.spinEditCoverage.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditCoverage.Properties.Appearance.Options.UseFont = true;
            this.spinEditCoverage.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditCoverage.Properties.IsFloatValue = false;
            this.spinEditCoverage.Properties.Mask.AutoComplete = DevExpress.XtraEditors.Mask.AutoCompleteType.None;
            this.spinEditCoverage.Properties.Mask.EditMask = "N00";
            this.spinEditCoverage.Properties.MaxValue = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.spinEditCoverage.Properties.MinValue = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.spinEditCoverage.Size = new System.Drawing.Size(82, 20);
            this.spinEditCoverage.TabIndex = 36;
            // 
            // absCoverate
            // 
            this.absCoverate.EditValue = new decimal(new int[] {
            11,
            0,
            0,
            0});
            this.absCoverate.Enabled = false;
            this.absCoverate.Location = new System.Drawing.Point(437, 35);
            this.absCoverate.Name = "absCoverate";
            this.absCoverate.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.absCoverate.Properties.Appearance.Options.UseFont = true;
            this.absCoverate.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.absCoverate.Properties.IsFloatValue = false;
            this.absCoverate.Properties.Mask.AutoComplete = DevExpress.XtraEditors.Mask.AutoCompleteType.None;
            this.absCoverate.Properties.Mask.EditMask = "N00";
            this.absCoverate.Properties.MaxValue = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.absCoverate.Properties.MinValue = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.absCoverate.Size = new System.Drawing.Size(82, 20);
            this.absCoverate.TabIndex = 37;
            // 
            // label15
            // 
            this.label15.AutoSize = true;
            this.label15.Location = new System.Drawing.Point(36, 76);
            this.label15.Name = "label15";
            this.label15.Size = new System.Drawing.Size(125, 12);
            this.label15.TabIndex = 44;
            this.label15.Text = "高重叠覆盖度点占比≥";
            // 
            // label16
            // 
            this.label16.AutoSize = true;
            this.label16.Location = new System.Drawing.Point(242, 76);
            this.label16.Name = "label16";
            this.label16.Size = new System.Drawing.Size(11, 12);
            this.label16.TabIndex = 43;
            this.label16.Text = "%";
            // 
            // labelControl6
            // 
            this.labelControl6.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl6.Appearance.Options.UseFont = true;
            this.labelControl6.Location = new System.Drawing.Point(78, 39);
            this.labelControl6.Name = "labelControl6";
            this.labelControl6.Size = new System.Drawing.Size(66, 12);
            this.labelControl6.TabIndex = 39;
            this.labelControl6.Text = "持续距离 ≥";
            // 
            // labelControl8
            // 
            this.labelControl8.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl8.Appearance.Options.UseFont = true;
            this.labelControl8.Location = new System.Drawing.Point(285, 40);
            this.labelControl8.Name = "labelControl8";
            this.labelControl8.Size = new System.Drawing.Size(102, 12);
            this.labelControl8.TabIndex = 37;
            this.labelControl8.Text = "相邻采样点距离 ≤";
            // 
            // labelControl7
            // 
            this.labelControl7.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl7.Appearance.Options.UseFont = true;
            this.labelControl7.Location = new System.Drawing.Point(248, 40);
            this.labelControl7.Name = "labelControl7";
            this.labelControl7.Size = new System.Drawing.Size(12, 12);
            this.labelControl7.TabIndex = 42;
            this.labelControl7.Text = "米";
            // 
            // spinEditRoadPercent
            // 
            this.spinEditRoadPercent.EditValue = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.spinEditRoadPercent.Location = new System.Drawing.Point(176, 71);
            this.spinEditRoadPercent.Name = "spinEditRoadPercent";
            this.spinEditRoadPercent.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditRoadPercent.Properties.Increment = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.spinEditRoadPercent.Properties.MaxValue = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.spinEditRoadPercent.Size = new System.Drawing.Size(60, 21);
            this.spinEditRoadPercent.TabIndex = 45;
            // 
            // spinEditRoadDistance
            // 
            this.spinEditRoadDistance.EditValue = new decimal(new int[] {
            50,
            0,
            0,
            0});
            this.spinEditRoadDistance.Location = new System.Drawing.Point(160, 36);
            this.spinEditRoadDistance.Name = "spinEditRoadDistance";
            this.spinEditRoadDistance.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditRoadDistance.Properties.Appearance.Options.UseFont = true;
            this.spinEditRoadDistance.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditRoadDistance.Properties.Increment = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.spinEditRoadDistance.Properties.IsFloatValue = false;
            this.spinEditRoadDistance.Properties.Mask.AutoComplete = DevExpress.XtraEditors.Mask.AutoCompleteType.None;
            this.spinEditRoadDistance.Properties.Mask.EditMask = "N00";
            this.spinEditRoadDistance.Properties.MaxValue = new decimal(new int[] {
            100000,
            0,
            0,
            0});
            this.spinEditRoadDistance.Size = new System.Drawing.Size(82, 20);
            this.spinEditRoadDistance.TabIndex = 38;
            // 
            // spinEditSampleDistance
            // 
            this.spinEditSampleDistance.EditValue = new decimal(new int[] {
            50,
            0,
            0,
            0});
            this.spinEditSampleDistance.Location = new System.Drawing.Point(402, 36);
            this.spinEditSampleDistance.Name = "spinEditSampleDistance";
            this.spinEditSampleDistance.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditSampleDistance.Properties.Appearance.Options.UseFont = true;
            this.spinEditSampleDistance.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditSampleDistance.Properties.IsFloatValue = false;
            this.spinEditSampleDistance.Properties.Mask.AutoComplete = DevExpress.XtraEditors.Mask.AutoCompleteType.None;
            this.spinEditSampleDistance.Properties.Mask.EditMask = "N00";
            this.spinEditSampleDistance.Properties.MaxValue = new decimal(new int[] {
            50,
            0,
            0,
            0});
            this.spinEditSampleDistance.Properties.MinValue = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.spinEditSampleDistance.Size = new System.Drawing.Size(82, 20);
            this.spinEditSampleDistance.TabIndex = 40;
            // 
            // labelControl9
            // 
            this.labelControl9.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl9.Appearance.Options.UseFont = true;
            this.labelControl9.Location = new System.Drawing.Point(491, 40);
            this.labelControl9.Name = "labelControl9";
            this.labelControl9.Size = new System.Drawing.Size(12, 12);
            this.labelControl9.TabIndex = 41;
            this.labelControl9.Text = "米";
            // 
            // groupControl3
            // 
            this.groupControl3.Controls.Add(this.chbRelative);
            this.groupControl3.Controls.Add(this.spinEditCoverage);
            this.groupControl3.Controls.Add(this.spinEditMaxDiff);
            this.groupControl3.Controls.Add(this.labelControl1);
            this.groupControl3.Controls.Add(this.labelControl3);
            this.groupControl3.Location = new System.Drawing.Point(0, 62);
            this.groupControl3.Name = "groupControl3";
            this.groupControl3.Size = new System.Drawing.Size(551, 70);
            this.groupControl3.TabIndex = 44;
            this.groupControl3.Text = "相对覆盖";
            // 
            // groupControl4
            // 
            this.groupControl4.Controls.Add(this.chbAbslute);
            this.groupControl4.Controls.Add(this.absCoverate);
            this.groupControl4.Controls.Add(this.absValue);
            this.groupControl4.Controls.Add(this.labelControl10);
            this.groupControl4.Controls.Add(this.labelControl2);
            this.groupControl4.Location = new System.Drawing.Point(0, 132);
            this.groupControl4.Name = "groupControl4";
            this.groupControl4.Size = new System.Drawing.Size(551, 70);
            this.groupControl4.TabIndex = 45;
            this.groupControl4.Text = "绝对覆盖";
            // 
            // labelControl4
            // 
            this.labelControl4.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl4.Appearance.Options.UseFont = true;
            this.labelControl4.Location = new System.Drawing.Point(19, 38);
            this.labelControl4.Name = "labelControl4";
            this.labelControl4.Size = new System.Drawing.Size(60, 12);
            this.labelControl4.TabIndex = 47;
            this.labelControl4.Text = "最强信号 >";
            // 
            // spinEditRxlevMin
            // 
            this.spinEditRxlevMin.EditValue = new decimal(new int[] {
            105,
            0,
            0,
            -2147483648});
            this.spinEditRxlevMin.Location = new System.Drawing.Point(82, 34);
            this.spinEditRxlevMin.Name = "spinEditRxlevMin";
            this.spinEditRxlevMin.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditRxlevMin.Properties.Appearance.Options.UseFont = true;
            this.spinEditRxlevMin.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditRxlevMin.Properties.IsFloatValue = false;
            this.spinEditRxlevMin.Properties.Mask.EditMask = "N00";
            this.spinEditRxlevMin.Properties.MaxValue = new decimal(new int[] {
            10,
            0,
            0,
            -2147483648});
            this.spinEditRxlevMin.Properties.MinValue = new decimal(new int[] {
            120,
            0,
            0,
            -2147483648});
            this.spinEditRxlevMin.Size = new System.Drawing.Size(62, 20);
            this.spinEditRxlevMin.TabIndex = 46;
            // 
            // labelControl5
            // 
            this.labelControl5.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl5.Appearance.Options.UseFont = true;
            this.labelControl5.Location = new System.Drawing.Point(147, 38);
            this.labelControl5.Name = "labelControl5";
            this.labelControl5.Size = new System.Drawing.Size(18, 12);
            this.labelControl5.TabIndex = 48;
            this.labelControl5.Text = "dBm";
            // 
            // btnCancel
            // 
            this.btnCancel.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnCancel.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnCancel.Appearance.Options.UseFont = true;
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnCancel.Location = new System.Drawing.Point(447, 328);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 27;
            this.btnCancel.Text = "取消";
            // 
            // btnOK
            // 
            this.btnOK.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnOK.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnOK.Appearance.Options.UseFont = true;
            this.btnOK.Location = new System.Drawing.Point(346, 328);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 26;
            this.btnOK.Text = "确定";
            // 
            // NRScanHighCoverateRoadDlg
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(551, 363);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.Controls.Add(this.groupControl2);
            this.Controls.Add(this.groupControl1);
            this.Name = "NRScanHighCoverateRoadDlg";
            this.Text = "高重叠覆盖度";
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            this.groupControl1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).EndInit();
            this.groupControl2.ResumeLayout(false);
            this.groupControl2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditMaxDiff.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.absValue.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditCoverage.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.absCoverate.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditRoadPercent.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditRoadDistance.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditSampleDistance.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl3)).EndInit();
            this.groupControl3.ResumeLayout(false);
            this.groupControl3.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl4)).EndInit();
            this.groupControl4.ResumeLayout(false);
            this.groupControl4.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditRxlevMin.Properties)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.GroupControl groupControl1;
        private DevExpress.XtraEditors.GroupControl groupControl2;
        private DevExpress.XtraEditors.SpinEdit spinEditMaxDiff;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private System.Windows.Forms.CheckBox chbAbslute;
        private DevExpress.XtraEditors.LabelControl labelControl10;
        private System.Windows.Forms.CheckBox chbRelative;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.SpinEdit absValue;
        private DevExpress.XtraEditors.SpinEdit spinEditCoverage;
        private DevExpress.XtraEditors.SpinEdit absCoverate;
        private DevExpress.XtraEditors.GroupControl groupControl4;
        private DevExpress.XtraEditors.GroupControl groupControl3;
        private System.Windows.Forms.Label label15;
        private System.Windows.Forms.Label label16;
        private DevExpress.XtraEditors.LabelControl labelControl6;
        private DevExpress.XtraEditors.LabelControl labelControl8;
        private DevExpress.XtraEditors.LabelControl labelControl7;
        private DevExpress.XtraEditors.SpinEdit spinEditRoadPercent;
        private DevExpress.XtraEditors.SpinEdit spinEditRoadDistance;
        private DevExpress.XtraEditors.SpinEdit spinEditSampleDistance;
        private DevExpress.XtraEditors.LabelControl labelControl9;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private DevExpress.XtraEditors.SpinEdit spinEditRxlevMin;
        private DevExpress.XtraEditors.LabelControl labelControl5;
        private DevExpress.XtraEditors.SimpleButton btnCancel;
        private DevExpress.XtraEditors.SimpleButton btnOK;
    }
}