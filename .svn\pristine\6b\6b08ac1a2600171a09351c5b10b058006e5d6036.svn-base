﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ScanFarCellSetDlg : BaseDialog
    {
        public ScanFarCellSetDlg()
        {
            InitializeComponent();
        }

        public void SetSettingFilterRet(int maxRxlev, int roadDistance, int cellDistance)
        {
            numRxlevThreshold.Value = maxRxlev;
            numDistance.Value = roadDistance;
            numCellDistance.Value = cellDistance;
        }

        public void GetSettingFilterRet(out int maxRxlev, out int roadDistance, out int cellDistance)
        {
            maxRxlev = (int)numRxlevThreshold.Value;
            roadDistance = (int)numDistance.Value;
            cellDistance = (int)numCellDistance.Value;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }
    }
}