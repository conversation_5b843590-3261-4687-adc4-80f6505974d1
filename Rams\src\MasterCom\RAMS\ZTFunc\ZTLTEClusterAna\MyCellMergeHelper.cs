﻿using System;
using System.Collections.Generic;
using System.Text;

using DevExpress.XtraGrid.Views.Base;
using DevExpress.XtraGrid.Columns;
using System.Windows.Forms;

using System.Drawing;

using DevExpress.XtraGrid.Views.Grid.ViewInfo;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraGrid.Views.Grid.Drawing;
using DevExpress.Utils.Drawing;

namespace MasterCom.RAMS.ZTFunc.ZTLTEClusterAna
{
    /// <summary>
    /// 合并行的类,只是绘画单元格,并没有实现两个单元格实际的合并
    /// </summary>
    class MyCellMergeHelper
    {
        public MyCellMergeHelper(GridView view)
        {
            _view = view;
            view.CustomDrawCell += view_CustomDrawCell;
            view.GridControl.Paint += GridControl_Paint;
            view.CellValueChanged += view_CellValueChanged;
            painter = new MyGridPainter(view);
        }

        private readonly MyGridPainter painter;
        private readonly GridView _view;
        private readonly List<MyMergedCell> _MergedCells = new List<MyMergedCell>();

        public List<MyMergedCell> MergedCells
        {
            get { return _MergedCells; }
        }

        public MyMergedCell AddMergedCell(int rowHandle, GridColumn col1, GridColumn col2)
        {
            MyMergedCell cell = new MyMergedCell(rowHandle, col1, col2);
            _MergedCells.Add(cell);
            return cell;
        }

        public void AddMergedCell(int rowHandle, int col1, int col2, object value)
        {
            AddMergedCell(rowHandle, _view.Columns[col1], _view.Columns[col2], value);
        }

        public void AddMergedCell(int rowHandle, GridColumn col1, GridColumn col2, object value)
        {
            MyMergedCell cell = AddMergedCell(rowHandle, col1, col2);
            SafeSetMergedCellValue(cell, value);
        }

        public void SafeSetMergedCellValue(MyMergedCell cell, object value)
        {
            if (cell != null)
            {
                SafeSetCellValue(cell.RowHandle, cell.Column1, value);
                SafeSetCellValue(cell.RowHandle, cell.Column2, value);
            }
        }

        public void SafeSetCellValue(int rowHandle, GridColumn column, object value)
        {
            if (!object.ReferenceEquals(_view.GetRowCellValue(rowHandle, column), value))
            {
                _view.SetRowCellValue(rowHandle, column, value);
            }
        }

        private MyMergedCell GetMergedCell(int rowHandle, GridColumn column)
        {
            foreach (MyMergedCell cell in _MergedCells)
            {
                if (cell.RowHandle == rowHandle && (object.ReferenceEquals(column, cell.Column1) || object.ReferenceEquals(column, cell.Column2)))
                {
                    return cell;
                }
            }
            return null;
        }

        private bool IsMergedCell(int rowHandle, GridColumn column)
        {
            return GetMergedCell(rowHandle, column) != null;
        }

        private void DrawMergedCells(PaintEventArgs e)
        {
            foreach (MyMergedCell cell in _MergedCells)
            {
                painter.DrawMergedCell(e,cell);
            }
        }

        private void view_CellValueChanged(object sender, CellValueChangedEventArgs e)
        {
            SafeSetMergedCellValue(GetMergedCell(e.RowHandle, e.Column), e.Value);
        }

        private void GridControl_Paint(object sender, PaintEventArgs e)
        {
            DrawMergedCells(e);
        }

        private void view_CustomDrawCell(object sender, RowCellCustomDrawEventArgs e)
        {
            if (IsMergedCell(e.RowHandle, e.Column))
            {
                e.Handled = !painter.IsCustomPainting;
            }
        }
    }

    class MyGridPainter : GridPainter
    {
        public MyGridPainter(GridView view)
            : base(view)
        {

        }

        public bool IsCustomPainting;
        public void DrawMergedCell(PaintEventArgs e, MyMergedCell cell)
        {
            //base.DrawMergedCell(e, Cell);
            int delta = cell.Column1.VisibleIndex - cell.Column2.VisibleIndex;
            if (Math.Abs(delta) > 1)
            {
                return;
            }
            GridViewInfo vi = View.GetViewInfo() as GridViewInfo;
            GridCellInfo gridCellInfo1 = vi.GetGridCellInfo(cell.RowHandle, cell.Column1.AbsoluteIndex);
            GridCellInfo gridCellInfo2 = vi.GetGridCellInfo(cell.RowHandle, cell.Column2.AbsoluteIndex);
            if (gridCellInfo1 == null || gridCellInfo2 == null)
            {
                return;
            }
            Rectangle targetRect = Rectangle.Union(gridCellInfo1.Bounds, gridCellInfo2.Bounds);
            gridCellInfo1.Bounds = targetRect;
            gridCellInfo1.CellValueRect = targetRect;
            gridCellInfo2.Bounds = targetRect;
            gridCellInfo2.CellValueRect = targetRect;
            if (delta < 0)
            {
                gridCellInfo1 = gridCellInfo2;
            }
            Rectangle bounds = gridCellInfo1.ViewInfo.Bounds;
            bounds.Width = targetRect.Width;
            bounds.Height = targetRect.Height;
            gridCellInfo1.ViewInfo.Bounds = bounds;
            gridCellInfo1.ViewInfo.CalcViewInfo(e.Graphics);
            IsCustomPainting = true;
            GraphicsCache cache = new GraphicsCache(e.Graphics);
            gridCellInfo1.Appearance.FillRectangle(cache, gridCellInfo1.Bounds);
            DrawRowCell(new GridViewDrawArgs(cache, vi, vi.ViewRects.Bounds), gridCellInfo1);
            IsCustomPainting = false;
        }
    }


    class MyMergedCell
    {
        public int RowHandle { get; set; }
        public GridColumn Column1 { get; set; }
        public GridColumn Column2 { get; set; }
        public MyMergedCell(int rowHandle, GridColumn col1, GridColumn col2)
        {
            RowHandle = rowHandle;
            Column1 = col1;
            Column2 = col2;
        }
    }
}
