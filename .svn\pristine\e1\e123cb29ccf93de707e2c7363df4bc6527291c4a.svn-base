﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRCellWrongDir : TDCellWrongDir
    {
        public NRCellWrongDir(NRCell cell)
           : base(cell)
        {
        }

        public new NRCell Cell
        {
            get { return (NRCell)cell; }
        }

        public override string CellName
        {
            get
            {
                return Cell.Name;
            }
        }

        public override int Direction
        {
            get
            {
                return Cell.Direction;
            }
        }

        public override double Latitude
        {
            get
            {
                return Cell.Latitude;
            }
        }

        public override double Longitude
        {
            get
            {
                return Cell.Longitude;
            }
        }

        public int TAC
        {
            get
            {
                return Cell.TAC;
            }
        }

        public long NCI
        {
            get
            {
                return Cell.NCI;
            }
        }

        public override int FREQ
        {
            get { return Cell.SSBARFCN; }
        }

        public override int CPI
        {
            get { return Cell.PCI; }
        }

        public new NRCellWrongDir Clone()
        {
            NRCellWrongDir cellWrong = new NRCellWrongDir(Cell);
            cellWrong.cellWrongBatch = this.cellWrongBatch;
            cellWrong.resultFirstBatch = this.resultFirstBatch;
            cellWrong.resultSecondBatch = this.resultSecondBatch;

            return cellWrong;
        }
    }
}
