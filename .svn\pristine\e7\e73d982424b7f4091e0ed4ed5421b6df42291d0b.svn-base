﻿using System;
using System.Collections.Generic;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using System.Data;
using MasterCom.RAMS.Model.Interface;
using Excel = Microsoft.Office.Interop.Excel;
using System.Reflection;
using MasterCom.RAMS.Func;
using DevExpress.XtraEditors;
using MasterCom.RAMS.ZTFunc;

namespace MasterCom.RAMS.Net
{
    public class MOSChartAnaByFile : MOSAnaByFile
    {
        public MOSChartAnaByFile(MainModel mainModel)
            : base(mainModel)
        {
        }

        public override string Name
        {
            get { return "按MOS进行主被叫关联统计"; }
        }

        public override string IconName
        {
            get { return "Images/replay.gif"; }
        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                return true;
            }
            return false;
        }

        protected override void doWithMOSResult(List<GSMMosParam> mosParamList)
        {
            MOSChartAnaManager mosAnaManager = MOSChartAnaManager.GetInstance();
            mosAnaManager.InitData();
            mosAnaManager.StatMOS(mosParamList);
            mosAnaManager.ShowResult();
        }
    }
}