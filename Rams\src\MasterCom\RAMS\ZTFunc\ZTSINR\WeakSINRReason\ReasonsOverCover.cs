﻿using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTSINR.WeakSINRReason
{
    class ReasonsOverCover : ReasonBase
    {
        public ReasonsOverCover()
        {
            this.Name = "过覆盖";
        }
        
        public double OverRsrpMin { get; set; } = -100;
        public double CoverFactor { get; set; } = 1.1;

        public override bool IsValid(Model.TestPoint tp, params object[] resvParams)
        {
            float? rsrp = (float?)GetRSRP(tp);
            if (rsrp == null || rsrp < OverRsrpMin)
            {
                return false;
            }
            LTECell curCell = null;
            if (tp is ScanTestPoint_NBIOT)
            {
                curCell = tp.GetCell_LTEScan(0);
            }
            else
            {
                curCell = tp.GetMainLTECell_TdOrFdd();
            }
            if (curCell != null)
            {
                if (curCell.Type == LTEBTSType.Indoor)
                {
                    return false;
                }
                if (curCell.Antennas == null || curCell.Antennas.Count == 0)//避免工参不对Antennas为空时CalculateRadius()中报错
                {
                    return false;
                }
                double radiusOfCell = MasterCom.ES.Data.CfgDataProvider.CalculateRadius(curCell, 3);
                double rationalDistance = radiusOfCell * CoverFactor;

                double distanceToCell = MathFuncs.GetDistance(tp.Longitude, tp.Latitude, curCell.Longitude, curCell.Latitude);
                if (distanceToCell > rationalDistance)
                {
                    return true;
                }
            }
            return false;
        }
        protected object GetRSRP(TestPoint tp)
        {
            if (tp is LTEFddTestPoint)
            {
                return tp["lte_fdd_RSRP"];
            }
            else if (tp is Model.ScanTestPoint_NBIOT)
            {
                return tp["LTESCAN_TopN_CELL_Specific_RSRP"];
            }
            return tp["lte_RSRP"];
        }

        public override Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["Enable"] = this.Enable;
                param["OverRsrpMin"] = this.OverRsrpMin;
                param["CoverFactor"] = this.CoverFactor;
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                if (param.ContainsKey("Enable"))
                {
                    this.Enable = (bool)param["Enable"];
                }
                if (param.ContainsKey("OverRsrpMin"))
                {
                    this.OverRsrpMin = (double)param["OverRsrpMin"];
                }
                if (param.ContainsKey("CoverFactor"))
                {
                    this.CoverFactor = (double)param["CoverFactor"];
                }
            }
        }
    }
}
