﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NRModRoadSettingForm : BaseForm
    {
        public NRModRoadSettingForm()
        {
            InitializeComponent();
            btnOK.Click += BtnOK_Click;
            btnCancel.Click += BtnCancel_Click;
        }

        public void BtnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
        }

        public void BtnOK_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.OK;
        }

        public NRModRoadCondition GetCondition()
        {
            NRModRoadCondition cond = new NRModRoadCondition();
            cond.MaxRxlev = (double)numMaxRxlev.Value;
            cond.SampleInterval = (double)numSampleInterval.Value;
            cond.RoadLength = (double)numRoadLength.Value;
            string type = cmbModType.SelectedItem.ToString();
            cond.FilterCond.ModX = cond.FilterCond.GetModeType(type);
            return cond;
        }

       
    }
}
