﻿using System;
using System.Collections.Generic;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using System.Reflection;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class LTEAntennaOverlapCoverageForm : MinCloseForm
    {
        private Dictionary<string, LteSampleMainInfo> dicLteOverlapData = new Dictionary<string, LteSampleMainInfo>();
        private Dictionary<string, LteSampleMainInfo> dicLteKeyWord = new Dictionary<string, LteSampleMainInfo>();
        private int iNowPag = 0;
        private int iSumPage = 0;
        private bool isKeyWord = false;
        MapForm mapForm;

        private int iAntennaParaFlag = -10000;
        public LTEAntennaOverlapCoverageForm(MainModel mainModel)
            : base(mainModel)
        {
            InitializeComponent();
            init();
            mapForm = MainModel.MainForm.GetMapForm();
        }
        #region 控件初始化
        private void init()
        {
            #region 小区基本信息
            this.olvColumnSN.AspectGetter = delegate (object row)
            {
                if (row is LteSampleMainInfo)
                {
                    LteSampleMainInfo item = row as LteSampleMainInfo;
                    return item.idx;
                }
                return "";
            };
            this.olvColumnCellName.AspectGetter = delegate (object row)
            {
                if (row is LteSampleMainInfo)
                {
                    LteSampleMainInfo item = row as LteSampleMainInfo;
                    return item.CellName;
                }
                return "";
            };
            this.olvColumnTAC.AspectGetter = delegate (object row)
            {
                if (row is LteSampleMainInfo)
                {
                    LteSampleMainInfo item = row as LteSampleMainInfo;
                    if (item.servCell != null)
                    {
                        return item.servCell.TAC;
                    }
                }
                return "";
            };
            this.olvColumnECI.AspectGetter = delegate (object row)
            {
                if (row is LteSampleMainInfo)
                {
                    LteSampleMainInfo item = row as LteSampleMainInfo;
                    if (item.servCell != null)
                    {
                        return item.servCell.ECI;
                    }
                }
                return "";
            };
            this.olvColumnEARFCN.AspectGetter = delegate (object row)
            {
                if (row is LteSampleMainInfo)
                {
                    LteSampleMainInfo item = row as LteSampleMainInfo;
                    return item.iEarfcn;
                }
                return "";
            };
            this.olvColumnPCI.AspectGetter = delegate (object row)
            {
                if (row is LteSampleMainInfo)
                {
                    LteSampleMainInfo item = row as LteSampleMainInfo;
                    return item.iPCI;
                }
                return "";
            };
            this.olvColumnCovType.AspectGetter = delegate (object row)
            {
                if (row is LteSampleMainInfo)
                {
                    LteSampleMainInfo item = row as LteSampleMainInfo;
                    return item.antCgfSub.strType;
                }
                return "";
            };
            this.olvColumnGrid.AspectGetter = delegate (object row)
            {
                if (row is LteSampleMainInfo)
                {
                    LteSampleMainInfo item = row as LteSampleMainInfo;
                    return item.LTECellInfo.strGrid;
                }
                return "";
            };
            this.olvColumn站间距.AspectGetter = delegate (object row)
            {
                if (row is LteSampleMainInfo)
                {
                    LteSampleMainInfo item = row as LteSampleMainInfo;
                    return item.d站间距;
                }
                return "";
            };
            this.olvColumn所属区域.AspectGetter = delegate (object row)
            {
                if (row is LteSampleMainInfo)
                {
                    LteSampleMainInfo item = row as LteSampleMainInfo;
                    return item.str所属区域;
                }
                return "";
            };
            this.olvColumnAzimuth.AspectGetter = delegate (object row)
            {
                if (row is LteSampleMainInfo)
                {
                    LteSampleMainInfo item = row as LteSampleMainInfo;
                    return item.antCgfSub.方向角;
                }
                return "";
            };
            this.olvColumnPresetTILT.AspectGetter = delegate (object row)
            {
                if (row is LteSampleMainInfo)
                {
                    LteSampleMainInfo item = row as LteSampleMainInfo;
                    return item.antCgfSub.预置下倾角;
                }
                return "";
            };
            this.olvColumnMTILT.AspectGetter = delegate (object row)
            {
                if (row is LteSampleMainInfo)
                {
                    LteSampleMainInfo item = row as LteSampleMainInfo;
                    return item.antCgfSub.机械下倾角;
                }
                return "";
            };
            this.olvColumnETILT.AspectGetter = delegate (object row)
            {
                if (row is LteSampleMainInfo)
                {
                    LteSampleMainInfo item = row as LteSampleMainInfo;
                    return item.antCgfSub.电调下倾角;
                }
                return "";
            };
            this.olvColumnHeight.AspectGetter = delegate (object row)
            {
                if (row is LteSampleMainInfo)
                {
                    LteSampleMainInfo item = row as LteSampleMainInfo;
                    return item.antCgfSub.挂高;
                }
                return "";
            };
            #endregion
            #region 小区占用情况
            this.olvColumnSampleCount.AspectGetter = delegate (object row)
            {
                if (row is LteSampleMainInfo)
                {
                    LteSampleMainInfo item = row as LteSampleMainInfo;
                    return item.LTECellInfo.isampleCount;
                }
                return "";
            };
            this.olvColumnRSRPAvg.AspectGetter = delegate (object row)
            {
                if (row is LteSampleMainInfo)
                {
                    LteSampleMainInfo item = row as LteSampleMainInfo;
                    return item.LTECellInfo.dRSRPAvg;
                }
                return "";
            };
            this.olvColumnSINRAvg.AspectGetter = delegate (object row)
            {
                if (row is LteSampleMainInfo)
                {
                    LteSampleMainInfo item = row as LteSampleMainInfo;
                    return item.LTECellInfo.dSINRAvg;
                }
                return "";
            };
            this.olvColumnDistanceAvg.AspectGetter = delegate (object row)
            {
                if (row is LteSampleMainInfo)
                {
                    LteSampleMainInfo item = row as LteSampleMainInfo;
                    return item.LTECellInfo.cellDistanceAvg;
                }
                return "";
            };
            #endregion
            #region 小区主覆盖情况
            this.olvColumnDiffRel.AspectGetter = delegate (object row)
            {
                if (row is LteSampleMainInfo)
                {
                    LteSampleMainInfo item = row as LteSampleMainInfo;
                    return item.LTECellInfo.testPointDic.Count;
                }
                return "";
            };
            this.olvColumnAreaAvg.AspectGetter = delegate (object row)
            {
                if (row is LteSampleMainInfo)
                {
                    LteSampleMainInfo item = row as LteSampleMainInfo;
                    return item.LTECellInfo.cellAreaSub;
                }
                return "";
            };
            this.olvColumnPCellSampleCount.AspectGetter = delegate (object row)
            {
                if (row is LteSampleMainInfo)
                {
                    LteSampleMainInfo item = row as LteSampleMainInfo;
                    return item.LTEMainCellInfo.isampleCount;
                }
                return "";
            };
            this.olvColumnPCellRSRPAvg.AspectGetter = delegate (object row)
            {
                if (row is LteSampleMainInfo)
                {
                    LteSampleMainInfo item = row as LteSampleMainInfo;
                    return item.LTEMainCellInfo.dRSRPAvg;
                }
                return "";
            };
            this.olvColumnPCellSINRAvg.AspectGetter = delegate (object row)
            {
                if (row is LteSampleMainInfo)
                {
                    LteSampleMainInfo item = row as LteSampleMainInfo;
                    return item.LTEMainCellInfo.dSINRAvg;
                }
                return "";
            };
            this.olvColumnPCellDistanceAvg.AspectGetter = delegate (object row)
            {
                if (row is LteSampleMainInfo)
                {
                    LteSampleMainInfo item = row as LteSampleMainInfo;
                    return item.LTEMainCellInfo.cellDistanceAvg;
                }
                return "";
            };
            #endregion
            #region 小区重叠覆盖情况
            this.olvColumnPCellDiffRel.AspectGetter = delegate (object row)
            {
                if (row is LteSampleMainInfo)
                {
                    LteSampleMainInfo item = row as LteSampleMainInfo;
                    return item.LTEMainCellInfo.testPointDic.Count;
                }
                return "";
            };
            this.olvColumnPCellAreaAvg.AspectGetter = delegate (object row)
            {
                if (row is LteSampleMainInfo)
                {
                    LteSampleMainInfo item = row as LteSampleMainInfo;
                    return item.LTEMainCellInfo.cellAreaSub;
                }
                return "";
            };
            this.olvColumnOverlapCellSampleCount.AspectGetter = delegate (object row)
            {
                if (row is LteSampleMainInfo)
                {
                    LteSampleMainInfo item = row as LteSampleMainInfo;
                    return item.LTEOverlapCellInfo.isampleCount;
                }
                return "";
            };
            this.olvColumnOverlapCellRsrpAvg.AspectGetter = delegate (object row)
            {
                if (row is LteSampleMainInfo)
                {
                    LteSampleMainInfo item = row as LteSampleMainInfo;
                    return item.LTEOverlapCellInfo.dRSRPAvg;
                }
                return "";
            };
            this.olvColumnOverlapCellSinrAvg.AspectGetter = delegate (object row)
            {
                if (row is LteSampleMainInfo)
                {
                    LteSampleMainInfo item = row as LteSampleMainInfo;
                    return item.LTEOverlapCellInfo.dSINRAvg;
                }
                return "";
            };
            this.olvColumnOverlapCellDistanceAvg.AspectGetter = delegate (object row)
            {
                if (row is LteSampleMainInfo)
                {
                    LteSampleMainInfo item = row as LteSampleMainInfo;
                    return item.LTEOverlapCellInfo.cellDistanceAvg;
                }
                return "";
            };
            #endregion
            #region 覆盖分段统计
            this.olvColumnRSRP0_3dB采样点占比.AspectGetter = delegate (object row)
            {
                if (row is LteSampleMainInfo)
                {
                    LteSampleMainInfo item = row as LteSampleMainInfo;
                    return item.LTEMainCellInfo.lteMainCellCover.getSample0_3;
                }
                return "";
            };
            this.olvColumnRSRP0_3dB覆盖面积.AspectGetter = delegate (object row)
            {
                if (row is LteSampleMainInfo)
                {
                    LteSampleMainInfo item = row as LteSampleMainInfo;
                    return item.LTEMainCellInfo.lteMainCellCover.dArea0_3;
                }
                return "";
            };
            this.olvColumnRSRP3_6dB采样点占比.AspectGetter = delegate (object row)
            {
                if (row is LteSampleMainInfo)
                {
                    LteSampleMainInfo item = row as LteSampleMainInfo;
                    return item.LTEMainCellInfo.lteMainCellCover.getSample3_6;
                }
                return "";
            };
            this.olvColumnRSRP3_6dB覆盖面积.AspectGetter = delegate (object row)
            {
                if (row is LteSampleMainInfo)
                {
                    LteSampleMainInfo item = row as LteSampleMainInfo;
                    return item.LTEMainCellInfo.lteMainCellCover.dArea3_6;
                }
                return "";
            };
            this.olvColumnRSRP6_10dB采样点占比.AspectGetter = delegate (object row)
            {
                if (row is LteSampleMainInfo)
                {
                    LteSampleMainInfo item = row as LteSampleMainInfo;
                    return item.LTEMainCellInfo.lteMainCellCover.getSample6_10;
                }
                return "";
            };
            this.olvColumnRSRP6_10dB覆盖面积.AspectGetter = delegate (object row)
            {
                if (row is LteSampleMainInfo)
                {
                    LteSampleMainInfo item = row as LteSampleMainInfo;
                    return item.LTEMainCellInfo.lteMainCellCover.dArea6_10;
                }
                return "";
            };
            this.olvColumnRSRP10_15dB采样点占比.AspectGetter = delegate (object row)
            {
                if (row is LteSampleMainInfo)
                {
                    LteSampleMainInfo item = row as LteSampleMainInfo;
                    return item.LTEMainCellInfo.lteMainCellCover.getSample10_15;
                }
                return "";
            };
            this.olvColumnRSRP10_15dB覆盖面积.AspectGetter = delegate (object row)
            {
                if (row is LteSampleMainInfo)
                {
                    LteSampleMainInfo item = row as LteSampleMainInfo;
                    return item.LTEMainCellInfo.lteMainCellCover.dArea10_15;
                }
                return "";
            };
            this.olvColumnRSRP15dB采样点占比.AspectGetter = delegate (object row)
            {
                if (row is LteSampleMainInfo)
                {
                    LteSampleMainInfo item = row as LteSampleMainInfo;
                    return item.LTEMainCellInfo.lteMainCellCover.getSample15;
                }
                return "";
            };
            this.olvColumnRSRP15dB覆盖面积.AspectGetter = delegate (object row)
            {
                if (row is LteSampleMainInfo)
                {
                    LteSampleMainInfo item = row as LteSampleMainInfo;
                    return item.LTEMainCellInfo.lteMainCellCover.dArea15;
                }
                return "";
            };
            #endregion
            #region 同频6dB内小区干扰情况
            this.olvColumnOverlapCellDiffRel.AspectGetter = delegate (object row)
            {
                if (row is LteSampleMainInfo)
                {
                    LteSampleMainInfo item = row as LteSampleMainInfo;
                    return item.LTEOverlapCellInfo.testPointDic.Count;
                }
                return "";
            };
            this.olvColumnOverlapCellAreaAvg.AspectGetter = delegate (object row)
            {
                if (row is LteSampleMainInfo)
                {
                    LteSampleMainInfo item = row as LteSampleMainInfo;
                    return item.LTEOverlapCellInfo.cellAreaSub;
                }
                return "";
            };
            this.olvColumnOtherCellSN.AspectGetter = delegate (object row)
            {
                if (row is LteCellPairCoverInfo)
                {
                    LteCellPairCoverInfo item = row as LteCellPairCoverInfo;
                    return item.idx;
                }
                return "";
            };
            this.olvColumnOtherCellName.AspectGetter = delegate (object row)
            {
                if (row is LteCellPairCoverInfo)
                {
                    LteCellPairCoverInfo item = row as LteCellPairCoverInfo;
                    return item.CellName;
                }
                return "";
            };
            this.olvColumnOverlapSampleCount.AspectGetter = delegate (object row)
            {
                if (row is LteCellPairCoverInfo)
                {
                    LteCellPairCoverInfo item = row as LteCellPairCoverInfo;
                    return item.iSampleNum;
                }
                return "";
            };
            this.olvColumnNGrid.AspectGetter = delegate (object row)
            {
                if (row is LteCellPairCoverInfo)
                {
                    LteCellPairCoverInfo item = row as LteCellPairCoverInfo;
                    return item.nbCellInfo.strGrid;
                }
                return "";
            };
            this.olvColumnM3.AspectGetter = delegate (object row)
            {
                if (row is LteCellPairCoverInfo)
                {
                    LteCellPairCoverInfo item = row as LteCellPairCoverInfo;
                    return item.isMod3;
                }
                return "";
            };
            this.olvColumnM6.AspectGetter = delegate (object row)
            {
                if (row is LteCellPairCoverInfo)
                {
                    LteCellPairCoverInfo item = row as LteCellPairCoverInfo;
                    return item.isMod6;
                }
                return "";
            };
            this.olvColumnNAzimuth.AspectGetter = delegate (object row)
            {
                if (row is LteCellPairCoverInfo)
                {
                    LteCellPairCoverInfo item = row as LteCellPairCoverInfo;
                    return item.antCgfSub.方向角;
                }
                return "";
            };
            this.olvColumnNPresetTILT.AspectGetter = delegate (object row)
            {
                if (row is LteCellPairCoverInfo)
                {
                    LteCellPairCoverInfo item = row as LteCellPairCoverInfo;
                    return item.antCgfSub.预置下倾角;
                }
                return "";
            };
            this.olvColumnNMTILT.AspectGetter = delegate (object row)
            {
                if (row is LteCellPairCoverInfo)
                {
                    LteCellPairCoverInfo item = row as LteCellPairCoverInfo;
                    return item.antCgfSub.机械下倾角;
                }
                return "";
            };
            this.olvColumnNETILT.AspectGetter = delegate (object row)
            {
                if (row is LteCellPairCoverInfo)
                {
                    LteCellPairCoverInfo item = row as LteCellPairCoverInfo;
                    return item.antCgfSub.电调下倾角;
                }
                return "";
            };
            this.olvColumnNHeight.AspectGetter = delegate (object row)
            {
                if (row is LteCellPairCoverInfo)
                {
                    LteCellPairCoverInfo item = row as LteCellPairCoverInfo;
                    return item.antCgfSub.挂高;
                }
                return "";
            };
            this.olvColumnSameBts.AspectGetter = delegate (object row)
            {
                if (row is LteCellPairCoverInfo)
                {
                    LteCellPairCoverInfo item = row as LteCellPairCoverInfo;
                    return item.strSameBts;
                }
                return "";
            };
            this.olvColumnSameBts90.AspectGetter = delegate (object row)
            {
                if (row is LteCellPairCoverInfo)
                {
                    LteCellPairCoverInfo item = row as LteCellPairCoverInfo;
                    return item.strSameBts90;
                }
                return "";
            };
            this.olvColumnSameBtsAngle.AspectGetter = delegate (object row)
            {
                if (row is LteCellPairCoverInfo)
                {
                    LteCellPairCoverInfo item = row as LteCellPairCoverInfo;
                    return item.fSameAngle;
                }
                return "";
            };
            this.olvColumnAntennaType.AspectGetter = delegate (object row)
            {
                if (row is LteCellPairCoverInfo)
                {
                    LteCellPairCoverInfo item = row as LteCellPairCoverInfo;
                    return item.strAntennaType;
                }
                return "";
            };
            this.olvColumnNeighbourRsrpAvg.AspectGetter = delegate (object row)
            {
                if (row is LteCellPairCoverInfo)
                {
                    LteCellPairCoverInfo item = row as LteCellPairCoverInfo;
                    return item.nbCellInfo.dRSRPAvg;
                }
                return "";
            };
            this.olvColumnNeighbourSinrAvg.AspectGetter = delegate (object row)
            {
                if (row is LteCellPairCoverInfo)
                {
                    LteCellPairCoverInfo item = row as LteCellPairCoverInfo;
                    return item.nbCellInfo.dSINRAvg;
                }
                return "";
            };
            this.olvColumnNeighbourDistanceAvg.AspectGetter = delegate (object row)
            {
                if (row is LteCellPairCoverInfo)
                {
                    LteCellPairCoverInfo item = row as LteCellPairCoverInfo;
                    return item.nbCellInfo.cellDistanceAvg;
                }
                return "";
            };
            this.olvColumnNeighbourDiffRel.AspectGetter = delegate (object row)
            {
                if (row is LteCellPairCoverInfo)
                {
                    LteCellPairCoverInfo item = row as LteCellPairCoverInfo;
                    return item.nbTpDic.Count;
                }
                return "";
            };
            this.olvColumnNeighbourAreaAvg.AspectGetter = delegate (object row)
            {
                if (row is LteCellPairCoverInfo)
                {
                    LteCellPairCoverInfo item = row as LteCellPairCoverInfo;
                    return item.nbCellInfo.cellAreaSub;
                }
                return "";
            };
            this.olvColumnThisCellRsrpAvg.AspectGetter = delegate (object row)
            {
                if (row is LteCellPairCoverInfo)
                {
                    LteCellPairCoverInfo item = row as LteCellPairCoverInfo;
                    return item.mainCellInfo.dRSRPAvg;
                }
                return "";
            };
            this.olvColumnThisCellSinrAvg.AspectGetter = delegate (object row)
            {
                if (row is LteCellPairCoverInfo)
                {
                    LteCellPairCoverInfo item = row as LteCellPairCoverInfo;
                    return item.mainCellInfo.dSINRAvg;
                }
                return "";
            };
            this.olvColumnThisCellDistanceAvg.AspectGetter = delegate (object row)
            {
                if (row is LteCellPairCoverInfo)
                {
                    LteCellPairCoverInfo item = row as LteCellPairCoverInfo;
                    return item.mainCellInfo.cellDistanceAvg;
                }
                return "";
            };
            this.olvColumnThisCellDiffRel.AspectGetter = delegate (object row)
            {
                if (row is LteCellPairCoverInfo)
                {
                    LteCellPairCoverInfo item = row as LteCellPairCoverInfo;
                    return item.mainTpDic.Count;
                }
                return "";
            };
            this.olvColumnThisCellAreaAvg.AspectGetter = delegate (object row)
            {
                if (row is LteCellPairCoverInfo)
                {
                    LteCellPairCoverInfo item = row as LteCellPairCoverInfo;
                    return item.mainCellInfo.cellAreaSub;
                }
                return "";
            };
            this.olvColumnPNDistance.AspectGetter = delegate (object row)
            {
                if (row is LteCellPairCoverInfo)
                {
                    LteCellPairCoverInfo item = row as LteCellPairCoverInfo;
                    return item.dCellDist;
                }
                return "";
            };
            #endregion
            #region 邻区作为最强小区时信息
            this.olvColumnNeighbourTopSampleCount.AspectGetter = delegate (object row)
            {
                if (row is LteCellPairCoverInfo)
                {
                    LteCellPairCoverInfo item = row as LteCellPairCoverInfo;
                    return item.mainCellInfo.isampleCount;
                }
                return "";
            };
            this.olvColumnNeighbourTopRSRPAvg.AspectGetter = delegate (object row)
            {
                if (row is LteCellPairCoverInfo)
                {
                    LteCellPairCoverInfo item = row as LteCellPairCoverInfo;
                    return item.mainCellInfo.dRSRPAvg;
                }
                return "";
            };
            this.olvColumnNeighbourTopSINRAvg.AspectGetter = delegate (object row)
            {
                if (row is LteCellPairCoverInfo)
                {
                    LteCellPairCoverInfo item = row as LteCellPairCoverInfo;
                    return item.mainCellInfo.dSINRAvg;
                }
                return "";
            };
            this.olvColumnNeighbourTopDistanceAvg.AspectGetter = delegate (object row)
            {
                if (row is LteCellPairCoverInfo)
                {
                    LteCellPairCoverInfo item = row as LteCellPairCoverInfo;
                    return item.mainCellInfo.cellDistanceAvg;
                }
                return "";
            };
            this.olvColumnNeighbourTopDiffRel.AspectGetter = delegate (object row)
            {
                if (row is LteCellPairCoverInfo)
                {
                    LteCellPairCoverInfo item = row as LteCellPairCoverInfo;
                    return item.mainTpDic.Count;
                }
                return "";
            };
            this.olvColumnNeighbourTopAreaAvg.AspectGetter = delegate (object row)
            {
                if (row is LteCellPairCoverInfo)
                {
                    LteCellPairCoverInfo item = row as LteCellPairCoverInfo;
                    return item.mainCellInfo.cellAreaSub;
                }
                return "";
            };
            this.olvColumnNeighbourTopCellRSRPAvg.AspectGetter = delegate (object row)
            {
                if (row is LteCellPairCoverInfo)
                {
                    LteCellPairCoverInfo item = row as LteCellPairCoverInfo;
                    return item.nbCellInfo.dRSRPAvg;
                }
                return "";
            };
            this.olvColumnNeighbourTopCellSINRAvg.AspectGetter = delegate (object row)
            {
                if (row is LteCellPairCoverInfo)
                {
                    LteCellPairCoverInfo item = row as LteCellPairCoverInfo;
                    return item.nbCellInfo.dSINRAvg;
                }
                return "";
            };
            this.olvColumnNeighbourTopCellDistanceAvg.AspectGetter = delegate (object row)
            {
                if (row is LteCellPairCoverInfo)
                {
                    LteCellPairCoverInfo item = row as LteCellPairCoverInfo;
                    return item.nbCellInfo.cellDistanceAvg;
                }
                return "";
            };
            this.olvColumnNeighbourTopCellDiffRel.AspectGetter = delegate (object row)
            {
                if (row is LteCellPairCoverInfo)
                {
                    LteCellPairCoverInfo item = row as LteCellPairCoverInfo;
                    return item.nbTpDic.Count;
                }
                return "";
            };
            this.olvColumnNeighbourTopCellAreaAvg.AspectGetter = delegate (object row)
            {
                if (row is LteCellPairCoverInfo)
                {
                    LteCellPairCoverInfo item = row as LteCellPairCoverInfo;
                    return item.nbCellInfo.cellAreaSub;
                }
                return "";
            };
            #endregion
            #region 主小区权值信息
            this.olvColumnDrangeport1.AspectGetter = delegate (object row)
            {
                if (row is LteSampleMainInfo)
                {
                    LteSampleMainInfo item = row as LteSampleMainInfo;
                    if (item.antennaPare.drangeport1 == iAntennaParaFlag)
                        return "-";
                    return item.antennaPare.drangeport1;
                }
                return "";
            };
            this.olvColumnDrangeport2.AspectGetter = delegate (object row)
            {
                if (row is LteSampleMainInfo)
                {
                    LteSampleMainInfo item = row as LteSampleMainInfo;
                    if (item.antennaPare.drangeport2 == iAntennaParaFlag)
                        return "-";
                    return item.antennaPare.drangeport2;
                }
                return "";
            };
            this.olvColumnDrangeport3.AspectGetter = delegate (object row)
            {
                if (row is LteSampleMainInfo)
                {
                    LteSampleMainInfo item = row as LteSampleMainInfo;
                    if (item.antennaPare.drangeport3 == iAntennaParaFlag)
                        return "-";
                    return item.antennaPare.drangeport3;
                }
                return "";
            };
            this.olvColumnDrangeport4.AspectGetter = delegate (object row)
            {
                if (row is LteSampleMainInfo)
                {
                    LteSampleMainInfo item = row as LteSampleMainInfo;
                    if (item.antennaPare.drangeport4 == iAntennaParaFlag)
                        return "-";
                    return item.antennaPare.drangeport4;
                }
                return "";
            };
            this.olvColumnDrangeport5.AspectGetter = delegate (object row)
            {
                if (row is LteSampleMainInfo)
                {
                    LteSampleMainInfo item = row as LteSampleMainInfo;
                    if (item.antennaPare.drangeport5 == iAntennaParaFlag)
                        return "-";
                    return item.antennaPare.drangeport5;
                }
                return "";
            };
            this.olvColumnDrangeport6.AspectGetter = delegate (object row)
            {
                if (row is LteSampleMainInfo)
                {
                    LteSampleMainInfo item = row as LteSampleMainInfo;
                    if (item.antennaPare.drangeport6 == iAntennaParaFlag)
                        return "-";
                    return item.antennaPare.drangeport6;
                }
                return "";
            };
            this.olvColumnDrangeport7.AspectGetter = delegate (object row)
            {
                if (row is LteSampleMainInfo)
                {
                    LteSampleMainInfo item = row as LteSampleMainInfo;
                    if (item.antennaPare.drangeport7 == iAntennaParaFlag)
                        return "-";
                    return item.antennaPare.drangeport7;
                }
                return "";
            };
            this.olvColumnDrangeport8.AspectGetter = delegate (object row)
            {
                if (row is LteSampleMainInfo)
                {
                    LteSampleMainInfo item = row as LteSampleMainInfo;
                    if (item.antennaPare.drangeport8 == iAntennaParaFlag)
                        return "-";
                    return item.antennaPare.drangeport8;
                }
                return "";
            };
            this.olvColumnDphaseport1.AspectGetter = delegate (object row)
            {
                if (row is LteSampleMainInfo)
                {
                    LteSampleMainInfo item = row as LteSampleMainInfo;
                    if (item.antennaPare.dphaseport1 == iAntennaParaFlag)
                        return "-";
                    return item.antennaPare.dphaseport1;
                }
                return "";
            };
            this.olvColumnDphaseport2.AspectGetter = delegate (object row)
            {
                if (row is LteSampleMainInfo)
                {
                    LteSampleMainInfo item = row as LteSampleMainInfo;
                    if (item.antennaPare.dphaseport2 == iAntennaParaFlag)
                        return "-";
                    return item.antennaPare.dphaseport2;
                }
                return "";
            };
            this.olvColumnDphaseport3.AspectGetter = delegate (object row)
            {
                if (row is LteSampleMainInfo)
                {
                    LteSampleMainInfo item = row as LteSampleMainInfo;
                    if (item.antennaPare.dphaseport3 == iAntennaParaFlag)
                        return "-";
                    return item.antennaPare.dphaseport3;
                }
                return "";
            };
            this.olvColumnDphaseport4.AspectGetter = delegate (object row)
            {
                if (row is LteSampleMainInfo)
                {
                    LteSampleMainInfo item = row as LteSampleMainInfo;
                    if (item.antennaPare.dphaseport4 == iAntennaParaFlag)
                        return "-";
                    return item.antennaPare.dphaseport4;
                }
                return "";
            };
            this.olvColumnDphaseport5.AspectGetter = delegate (object row)
            {
                if (row is LteSampleMainInfo)
                {
                    LteSampleMainInfo item = row as LteSampleMainInfo;
                    if (item.antennaPare.dphaseport5 == iAntennaParaFlag)
                        return "-";
                    return item.antennaPare.dphaseport5;
                }
                return "";
            };
            this.olvColumnDphaseport6.AspectGetter = delegate (object row)
            {
                if (row is LteSampleMainInfo)
                {
                    LteSampleMainInfo item = row as LteSampleMainInfo;
                    if (item.antennaPare.dphaseport6 == iAntennaParaFlag)
                        return "-";
                    return item.antennaPare.dphaseport6;
                }
                return "";
            };
            this.olvColumnDphaseport7.AspectGetter = delegate (object row)
            {
                if (row is LteSampleMainInfo)
                {
                    LteSampleMainInfo item = row as LteSampleMainInfo;
                    if (item.antennaPare.dphaseport7 == iAntennaParaFlag)
                        return "-";
                    return item.antennaPare.dphaseport7;
                }
                return "";
            };
            this.olvColumnDphaseport8.AspectGetter = delegate (object row)
            {
                if (row is LteSampleMainInfo)
                {
                    LteSampleMainInfo item = row as LteSampleMainInfo;
                    if (item.antennaPare.dphaseport8 == iAntennaParaFlag)
                        return "-";
                    return item.antennaPare.dphaseport8;
                }
                return "";
            };
            this.olvColumnGMax.AspectGetter = delegate (object row)
            {
                if (row is LteSampleMainInfo)
                {
                    LteSampleMainInfo item = row as LteSampleMainInfo;
                    if (item.antennaPare.GMax == iAntennaParaFlag)
                        return "-";
                    return item.antennaPare.GMax;
                }
                return "";
            };
            this.olvColumn3dbValue.AspectGetter = delegate (object row)
            {
                if (row is LteSampleMainInfo)
                {
                    LteSampleMainInfo item = row as LteSampleMainInfo;
                    if (item.antennaPare._3dbValue == iAntennaParaFlag)
                        return "-";
                    return item.antennaPare._3dbValue;
                }
                return "";
            };
            this.olvColumn6dbValue.AspectGetter = delegate (object row)
            {
                if (row is LteSampleMainInfo)
                {
                    LteSampleMainInfo item = row as LteSampleMainInfo;
                    if (item.antennaPare.GMax == iAntennaParaFlag)
                        return "-";
                    return item.antennaPare._6dbValue;
                }
                return "";
            };
            #endregion
            #region 邻小区权值信息
            this.olvColumnNDrangeport1.AspectGetter = delegate (object row)
            {
                if (row is LteCellPairCoverInfo)
                {
                    LteCellPairCoverInfo item = row as LteCellPairCoverInfo;
                    if (item.antennaPare.drangeport1 == iAntennaParaFlag)
                        return "-";
                    return item.antennaPare.drangeport1;
                }
                return "";
            };
            this.olvColumnNDrangeport2.AspectGetter = delegate (object row)
            {
                if (row is LteCellPairCoverInfo)
                {
                    LteCellPairCoverInfo item = row as LteCellPairCoverInfo;
                    if (item.antennaPare.drangeport2 == iAntennaParaFlag)
                        return "-";
                    return item.antennaPare.drangeport2;
                }
                return "";
            };
            this.olvColumnNDrangeport3.AspectGetter = delegate (object row)
            {
                if (row is LteCellPairCoverInfo)
                {
                    LteCellPairCoverInfo item = row as LteCellPairCoverInfo;
                    if (item.antennaPare.drangeport3 == iAntennaParaFlag)
                        return "-";
                    return item.antennaPare.drangeport3;
                }
                return "";
            };
            this.olvColumnNDrangeport4.AspectGetter = delegate (object row)
            {
                if (row is LteCellPairCoverInfo)
                {
                    LteCellPairCoverInfo item = row as LteCellPairCoverInfo;
                    if (item.antennaPare.drangeport4 == iAntennaParaFlag)
                        return "-";
                    return item.antennaPare.drangeport4;
                }
                return "";
            };
            this.olvColumnNDrangeport5.AspectGetter = delegate (object row)
            {
                if (row is LteCellPairCoverInfo)
                {
                    LteCellPairCoverInfo item = row as LteCellPairCoverInfo;
                    if (item.antennaPare.drangeport5 == iAntennaParaFlag)
                        return "-";
                    return item.antennaPare.drangeport5;
                }
                return "";
            };
            this.olvColumnNDrangeport6.AspectGetter = delegate (object row)
            {
                if (row is LteCellPairCoverInfo)
                {
                    LteCellPairCoverInfo item = row as LteCellPairCoverInfo;
                    if (item.antennaPare.drangeport6 == iAntennaParaFlag)
                        return "-";
                    return item.antennaPare.drangeport6;
                }
                return "";
            };
            this.olvColumnNDrangeport7.AspectGetter = delegate (object row)
            {
                if (row is LteCellPairCoverInfo)
                {
                    LteCellPairCoverInfo item = row as LteCellPairCoverInfo;
                    if (item.antennaPare.drangeport7 == iAntennaParaFlag)
                        return "-";
                    return item.antennaPare.drangeport7;
                }
                return "";
            };
            this.olvColumnNDrangeport8.AspectGetter = delegate (object row)
            {
                if (row is LteCellPairCoverInfo)
                {
                    LteCellPairCoverInfo item = row as LteCellPairCoverInfo;
                    if (item.antennaPare.drangeport8 == iAntennaParaFlag)
                        return "-";
                    return item.antennaPare.drangeport8;
                }
                return "";
            };
            this.olvColumnNDphaseport1.AspectGetter = delegate (object row)
            {
                if (row is LteCellPairCoverInfo)
                {
                    LteCellPairCoverInfo item = row as LteCellPairCoverInfo;
                    if (item.antennaPare.dphaseport1 == iAntennaParaFlag)
                        return "-";
                    return item.antennaPare.dphaseport1;
                }
                return "";
            };
            this.olvColumnNDphaseport2.AspectGetter = delegate (object row)
            {
                if (row is LteCellPairCoverInfo)
                {
                    LteCellPairCoverInfo item = row as LteCellPairCoverInfo;
                    if (item.antennaPare.dphaseport2 == iAntennaParaFlag)
                        return "-";
                    return item.antennaPare.dphaseport2;
                }
                return "";
            };
            this.olvColumnNDphaseport3.AspectGetter = delegate (object row)
            {
                if (row is LteCellPairCoverInfo)
                {
                    LteCellPairCoverInfo item = row as LteCellPairCoverInfo;
                    if (item.antennaPare.dphaseport3 == iAntennaParaFlag)
                        return "-";
                    return item.antennaPare.dphaseport3;
                }
                return "";
            };
            this.olvColumnNDphaseport4.AspectGetter = delegate (object row)
            {
                if (row is LteCellPairCoverInfo)
                {
                    LteCellPairCoverInfo item = row as LteCellPairCoverInfo;
                    if (item.antennaPare.dphaseport4 == iAntennaParaFlag)
                        return "-";
                    return item.antennaPare.dphaseport4;
                }
                return "";
            };
            this.olvColumnNDphaseport5.AspectGetter = delegate (object row)
            {
                if (row is LteCellPairCoverInfo)
                {
                    LteCellPairCoverInfo item = row as LteCellPairCoverInfo;
                    if (item.antennaPare.dphaseport5 == iAntennaParaFlag)
                        return "-";
                    return item.antennaPare.dphaseport5;
                }
                return "";
            };
            this.olvColumnNDphaseport6.AspectGetter = delegate (object row)
            {
                if (row is LteCellPairCoverInfo)
                {
                    LteCellPairCoverInfo item = row as LteCellPairCoverInfo;
                    if (item.antennaPare.dphaseport6 == iAntennaParaFlag)
                        return "-";
                    return item.antennaPare.dphaseport6;
                }
                return "";
            };
            this.olvColumnNDphaseport7.AspectGetter = delegate (object row)
            {
                if (row is LteCellPairCoverInfo)
                {
                    LteCellPairCoverInfo item = row as LteCellPairCoverInfo;
                    if (item.antennaPare.dphaseport7 == iAntennaParaFlag)
                        return "-";
                    return item.antennaPare.dphaseport7;
                }
                return "";
            };
            this.olvColumnNDphaseport8.AspectGetter = delegate (object row)
            {
                if (row is LteCellPairCoverInfo)
                {
                    LteCellPairCoverInfo item = row as LteCellPairCoverInfo;
                    if (item.antennaPare.dphaseport8 == iAntennaParaFlag)
                        return "-";
                    return item.antennaPare.dphaseport8;
                }
                return "";
            };
            this.olvColumnNGMax.AspectGetter = delegate (object row)
            {
                if (row is LteCellPairCoverInfo)
                {
                    LteCellPairCoverInfo item = row as LteCellPairCoverInfo;
                    if (item.antennaPare.GMax == iAntennaParaFlag)
                        return "-";
                    return item.antennaPare.GMax;
                }
                return "";
            };
            this.olvColumnN3dbValue.AspectGetter = delegate (object row)
            {
                if (row is LteCellPairCoverInfo)
                {
                    LteCellPairCoverInfo item = row as LteCellPairCoverInfo;
                    if (item.antennaPare._3dbValue == iAntennaParaFlag)
                        return "-";
                    return item.antennaPare._3dbValue;
                }
                return "";
            };
            this.olvColumnN6dbValue.AspectGetter = delegate (object row)
            {
                if (row is LteCellPairCoverInfo)
                {
                    LteCellPairCoverInfo item = row as LteCellPairCoverInfo;
                    if (item.antennaPare._6dbValue == iAntennaParaFlag)
                        return "-";
                    return item.antennaPare._6dbValue;
                }
                return "";
            };
            #endregion
            dataGridViewCell.ChildrenGetter = delegate (object o)
            {
                if (o is LteSampleMainInfo)
                {
                    LteSampleMainInfo info = o as LteSampleMainInfo;

                    List<LteCellPairCoverInfo> tmpList = new List<LteCellPairCoverInfo>();
                    foreach (string strCellName in info.nbCellInfoDic.Keys)
                    {
                        tmpList.Add(info.nbCellInfoDic[strCellName]);
                    }
                    return tmpList;
                }
                return null;
            };

            dataGridViewCell.CanExpandGetter = delegate (object o)
            {
                return o is LteSampleMainInfo;
            };
        }
        #endregion
        public void FillData(Dictionary<string, LteSampleMainInfo> dicLteOverlapData)
        {
            if (!isKeyWord)
                this.dicLteOverlapData = dicLteOverlapData;
            int iDataNum = dicLteOverlapData.Keys.Count;
            labNum.Text = iDataNum.ToString();
            iSumPage = iDataNum % 200 > 0 ? iDataNum / 200 + 1 : iDataNum / 200;
            labPage.Text = iSumPage.ToString();
            FillData(1, dicLteOverlapData);
        }
        /// <summary>
        /// 按页数填充
        /// </summary>
        public void FillData(int iPage, Dictionary<string, LteSampleMainInfo> dicNow)
        {
            dataGridViewCell.ClearObjects();
            iPage = iPage - 1;
            iPage = iPage >= iSumPage ? iSumPage - 1 : iPage;
            iPage = iPage <= 0 ? 0 : iPage;
            int iCount = 0;
            int rowCellAt = 1;
            Dictionary<string, LteSampleMainInfo> dicTmp = new Dictionary<string, LteSampleMainInfo>();
            foreach (string cellName in dicNow.Keys)
            {
                if (iCount++ / 200 != iPage)
                    continue;
                dicTmp.Add(cellName, dicNow[cellName]);
                if (rowCellAt == 200)
                    break;
                rowCellAt++;
            }
            dataGridViewCell.SetObjects(dicTmp.Values);
            iNowPag = iPage + 1;
            txtPage.Text = iNowPag.ToString();
        }
        /// <summary>
        /// 按小区名填充
        /// </summary>
        private void FillData(string cellName)
        {
            dicLteKeyWord.Clear();
            foreach (string strCellName in dicLteOverlapData.Keys)
            {
                if (dicLteOverlapData[strCellName].CellName.Contains(cellName))
                {
                    dicLteKeyWord.Add(strCellName, dicLteOverlapData[strCellName]);
                }
            }
            FillData(dicLteKeyWord);
        }
        private void miExpandAll_Click(object sender, EventArgs e)
        {
            dataGridViewCell.ExpandAll();
        }

        private void miCallapsAll_Click(object sender, EventArgs e)
        {
            dataGridViewCell.CollapseAll();
        }

        private void miExportExcel_Click(object sender, EventArgs e)
        {
            List<NPOIRow> datas = initColumns();
            foreach (string strCellName in dicLteOverlapData.Keys)
            {
                LteSampleMainInfo mainInfo = dicLteOverlapData[strCellName];
                fillValue(mainInfo, ref datas);
            }

            List<List<NPOIRow>> nrDatasList = new List<List<NPOIRow>>();
            nrDatasList.Add(datas);
            List<string> sheetNames = new List<string>();
            sheetNames.Add("LTE重叠覆盖干扰");
            OutputTmpFile(nrDatasList, sheetNames);
        }
        private void btnGo_Click(object sender, EventArgs e)
        {
            int iPage = 0;
            if (Int32.TryParse(txtPage.Text, out iPage))
            {
                FillData(Convert.ToInt32(txtPage.Text), dicLteOverlapData);
            }
        }

        private void btnPrevpage_Click(object sender, EventArgs e)
        {
            if (!isKeyWord)
            {
                FillData(--iNowPag, dicLteOverlapData);
            }
            else
            {
                FillData(--iNowPag, dicLteKeyWord);
            }
        }

        private void btnNextpage_Click(object sender, EventArgs e)
        {
            if (!isKeyWord)
            {
                FillData(++iNowPag, dicLteOverlapData);
            }
            else
            {
                FillData(++iNowPag, dicLteKeyWord);
            }
        }

        private void btnSearch_Click(object sender, EventArgs e)
        {
            if (txtCellName.Text == "")
            {
                isKeyWord = false;
                FillData(dicLteOverlapData);
                return;
            }
            isKeyWord = true;
            FillData(txtCellName.Text);
        }

        #region excel导出
        private void fillValue(LteSampleMainInfo mainInfo, ref List<NPOIRow> datas)
        {
            List<object> mainCols = new List<object>();
            #region 小区基本信息
            mainCols.Add(mainInfo.idx);
            mainCols.Add(mainInfo.CellName);
            if (mainInfo.servCell == null)
            {
                mainCols.AddRange(getDefaultValue(2));
            }
            else
            {
                mainCols.Add(mainInfo.servCell.TAC);
                mainCols.Add(mainInfo.servCell.ECI);
            }
            mainCols.Add(mainInfo.iEarfcn);
            mainCols.Add(mainInfo.iPCI);
            mainCols.Add(mainInfo.LTECellInfo.strGrid);
            mainCols.Add(mainInfo.antCgfSub.strType);
            mainCols.Add(mainInfo.d站间距);
            mainCols.Add(mainInfo.str所属区域);
            mainCols.Add(mainInfo.antCgfSub.方向角);
            mainCols.Add(mainInfo.antCgfSub.预置下倾角);
            mainCols.Add(mainInfo.antCgfSub.机械下倾角);
            mainCols.Add(mainInfo.antCgfSub.电调下倾角);
            mainCols.Add(mainInfo.antCgfSub.挂高);
            #endregion
            #region 小区占用情况
            mainCols.Add(mainInfo.LTECellInfo.isampleCount);
            mainCols.Add(mainInfo.LTECellInfo.dRSRPAvg);
            mainCols.Add(mainInfo.LTECellInfo.dSINRAvg);
            mainCols.Add(mainInfo.LTECellInfo.cellDistanceAvg);
            mainCols.Add(mainInfo.LTECellInfo.testPointDic.Count);
            mainCols.Add(mainInfo.LTECellInfo.cellAreaSub);
            #endregion
            #region 小区主覆盖情况
            mainCols.Add(mainInfo.LTEMainCellInfo.isampleCount);
            mainCols.Add(mainInfo.LTEMainCellInfo.dRSRPAvg);
            mainCols.Add(mainInfo.LTEMainCellInfo.dSINRAvg);
            mainCols.Add(mainInfo.LTEMainCellInfo.cellDistanceAvg);
            mainCols.Add(mainInfo.LTEMainCellInfo.testPointDic.Count);
            mainCols.Add(mainInfo.LTEMainCellInfo.cellAreaSub);
            #endregion
            #region 小区重叠覆盖情况
            mainCols.Add(mainInfo.LTEOverlapCellInfo.isampleCount);
            mainCols.Add(mainInfo.LTEOverlapCellInfo.dRSRPAvg);
            mainCols.Add(mainInfo.LTEOverlapCellInfo.dSINRAvg);
            mainCols.Add(mainInfo.LTEOverlapCellInfo.cellDistanceAvg);
            mainCols.Add(mainInfo.LTEOverlapCellInfo.testPointDic.Count);
            mainCols.Add(mainInfo.LTEOverlapCellInfo.cellAreaSub);
            #endregion
            #region 覆盖分段统计
            mainCols.Add(mainInfo.LTEMainCellInfo.lteMainCellCover.getSample0_3);
            mainCols.Add(mainInfo.LTEMainCellInfo.lteMainCellCover.dArea0_3);
            mainCols.Add(mainInfo.LTEMainCellInfo.lteMainCellCover.getSample3_6);
            mainCols.Add(mainInfo.LTEMainCellInfo.lteMainCellCover.dArea3_6);
            mainCols.Add(mainInfo.LTEMainCellInfo.lteMainCellCover.getSample6_10);
            mainCols.Add(mainInfo.LTEMainCellInfo.lteMainCellCover.dArea6_10);
            mainCols.Add(mainInfo.LTEMainCellInfo.lteMainCellCover.getSample10_15);
            mainCols.Add(mainInfo.LTEMainCellInfo.lteMainCellCover.dArea10_15);
            mainCols.Add(mainInfo.LTEMainCellInfo.lteMainCellCover.getSample15);
            mainCols.Add(mainInfo.LTEMainCellInfo.lteMainCellCover.dArea15);
            #endregion
            List<LteCellPairCoverInfo> tmpList = new List<LteCellPairCoverInfo>();
            foreach (string strCellName in mainInfo.nbCellInfoDic.Keys)
            {
                tmpList.Add(mainInfo.nbCellInfoDic[strCellName]);
            }
            tmpList.Sort(LteSampleByNum.GetCompareByNum());

            foreach (LteCellPairCoverInfo pairInfo in tmpList)
            {
                List<object> secCols = new List<object>();
                #region 同频6dB内小区干扰情况
                secCols.AddRange(mainCols);
                secCols.Add(pairInfo.idx);
                secCols.Add(pairInfo.CellName);
                secCols.Add(pairInfo.nbCellInfo.strGrid);
                secCols.Add(pairInfo.isMod3);
                secCols.Add(pairInfo.isMod6);
                secCols.Add(pairInfo.antCgfSub.方向角);
                secCols.Add(pairInfo.antCgfSub.预置下倾角);
                secCols.Add(pairInfo.antCgfSub.机械下倾角);
                secCols.Add(pairInfo.antCgfSub.电调下倾角);
                secCols.Add(pairInfo.antCgfSub.挂高);

                secCols.Add(pairInfo.strSameBts);
                secCols.Add(pairInfo.strSameBts90);
                secCols.Add(pairInfo.fSameAngle);
                secCols.Add(pairInfo.strAntennaType);
                secCols.Add(pairInfo.nbCellInfo.isampleCount);
                secCols.Add(pairInfo.nbCellInfo.dRSRPAvg);
                secCols.Add(pairInfo.nbCellInfo.dSINRAvg);
                secCols.Add(pairInfo.nbCellInfo.cellDistanceAvg);
                secCols.Add(pairInfo.nbTpDic.Count);
                secCols.Add(pairInfo.nbCellInfo.cellAreaSub);

                secCols.Add(pairInfo.mainCellInfo.dRSRPAvg);
                secCols.Add(pairInfo.mainCellInfo.dSINRAvg);
                secCols.Add(pairInfo.mainCellInfo.cellDistanceAvg);
                secCols.Add(pairInfo.mainTpDic.Count);
                secCols.Add(pairInfo.mainCellInfo.cellAreaSub);
                secCols.Add(pairInfo.dCellDist);
                #endregion
                if (dicLteOverlapData.ContainsKey(pairInfo.CellName))
                {
                    LteSampleMainInfo nbMaxInfo = dicLteOverlapData[pairInfo.CellName];//邻区最强时
                    if (nbMaxInfo.nbCellInfoDic.ContainsKey(mainInfo.CellName))
                    {
                        LteCellPairCoverInfo cellTmpInfo = nbMaxInfo.nbCellInfoDic[mainInfo.CellName];
                        #region 邻区最强时本小区情况
                        secCols.Add(cellTmpInfo.mainCellInfo.isampleCount);
                        secCols.Add(cellTmpInfo.mainCellInfo.dRSRPAvg);
                        secCols.Add(cellTmpInfo.mainCellInfo.dSINRAvg);
                        secCols.Add(cellTmpInfo.mainCellInfo.cellDistanceAvg);
                        secCols.Add(cellTmpInfo.mainTpDic.Count);
                        secCols.Add(cellTmpInfo.mainCellInfo.cellAreaSub);

                        secCols.Add(cellTmpInfo.nbCellInfo.dRSRPAvg);
                        secCols.Add(cellTmpInfo.nbCellInfo.dSINRAvg);
                        secCols.Add(cellTmpInfo.nbCellInfo.cellDistanceAvg);
                        secCols.Add(cellTmpInfo.nbTpDic.Count);
                        secCols.Add(cellTmpInfo.nbCellInfo.cellAreaSub);
                        #endregion
                    }
                    else
                        secCols.AddRange(getDefaultValue(11));
                }
                else
                    secCols.AddRange(getDefaultValue(11));

                secCols.AddRange(getAntennaPareInfo(mainInfo, pairInfo));
                NPOIRow nr = new NPOIRow();
                nr.cellValues = secCols;
                datas.Add(nr);
            }
            if (tmpList.Count == 0)
            {
                List<object> secCols = new List<object>();
                secCols.AddRange(mainCols);
                secCols.AddRange(getDefaultValue(37));
                secCols.AddRange(getAntennaPareInfo(mainInfo, new LteCellPairCoverInfo()));
                NPOIRow nr = new NPOIRow();
                nr.cellValues = secCols;
                datas.Add(nr);
            }
        }

        private List<object> getAntennaPareInfo(LteSampleMainInfo mainInfo, LteCellPairCoverInfo pairInfo)
        {
            List<object> mainCols = new List<object>();
            #region 权值信息
            mainCols.Add(getDefaultValue(mainInfo.antennaPare.drangeport1));
            mainCols.Add(getDefaultValue(mainInfo.antennaPare.drangeport2));
            mainCols.Add(getDefaultValue(mainInfo.antennaPare.drangeport3));
            mainCols.Add(getDefaultValue(mainInfo.antennaPare.drangeport4));
            mainCols.Add(getDefaultValue(mainInfo.antennaPare.drangeport5));
            mainCols.Add(getDefaultValue(mainInfo.antennaPare.drangeport6));
            mainCols.Add(getDefaultValue(mainInfo.antennaPare.drangeport7));
            mainCols.Add(getDefaultValue(mainInfo.antennaPare.drangeport8));

            mainCols.Add(getDefaultValue(mainInfo.antennaPare.dphaseport1));
            mainCols.Add(getDefaultValue(mainInfo.antennaPare.dphaseport2));
            mainCols.Add(getDefaultValue(mainInfo.antennaPare.dphaseport3));
            mainCols.Add(getDefaultValue(mainInfo.antennaPare.dphaseport4));
            mainCols.Add(getDefaultValue(mainInfo.antennaPare.dphaseport5));
            mainCols.Add(getDefaultValue(mainInfo.antennaPare.dphaseport6));
            mainCols.Add(getDefaultValue(mainInfo.antennaPare.dphaseport7));
            mainCols.Add(getDefaultValue(mainInfo.antennaPare.dphaseport8));
            mainCols.Add(getDefaultValue(mainInfo.antennaPare.GMax));
            mainCols.Add(getDefaultValue(mainInfo.antennaPare._3dbValue));
            mainCols.Add(getDefaultValue(mainInfo.antennaPare._6dbValue));
            if (pairInfo.idx == 0)
            {
                mainCols.AddRange(getDefaultValue(19));
            }
            else
            {
                mainCols.Add(getDefaultValue(pairInfo.antennaPare.drangeport1));
                mainCols.Add(getDefaultValue(pairInfo.antennaPare.drangeport2));
                mainCols.Add(getDefaultValue(pairInfo.antennaPare.drangeport3));
                mainCols.Add(getDefaultValue(pairInfo.antennaPare.drangeport4));
                mainCols.Add(getDefaultValue(pairInfo.antennaPare.drangeport5));
                mainCols.Add(getDefaultValue(pairInfo.antennaPare.drangeport6));
                mainCols.Add(getDefaultValue(pairInfo.antennaPare.drangeport7));
                mainCols.Add(getDefaultValue(pairInfo.antennaPare.drangeport8));

                mainCols.Add(getDefaultValue(pairInfo.antennaPare.dphaseport1));
                mainCols.Add(getDefaultValue(pairInfo.antennaPare.dphaseport2));
                mainCols.Add(getDefaultValue(pairInfo.antennaPare.dphaseport3));
                mainCols.Add(getDefaultValue(pairInfo.antennaPare.dphaseport4));
                mainCols.Add(getDefaultValue(pairInfo.antennaPare.dphaseport5));
                mainCols.Add(getDefaultValue(pairInfo.antennaPare.dphaseport6));
                mainCols.Add(getDefaultValue(pairInfo.antennaPare.dphaseport7));
                mainCols.Add(getDefaultValue(pairInfo.antennaPare.dphaseport8));
                mainCols.Add(getDefaultValue(pairInfo.antennaPare.GMax));
                mainCols.Add(getDefaultValue(pairInfo.antennaPare._3dbValue));
                mainCols.Add(getDefaultValue(pairInfo.antennaPare._6dbValue));
            }
            #endregion
            return mainCols;
        }
        private object getDefaultValue(float fTmp)
        {
            if (fTmp == iAntennaParaFlag)
                return "-";
            else
                return fTmp;
        }
        /// <summary>
        /// 默认列值
        /// </summary>
        private List<object> getDefaultValue(int index)
        {
            List<object> tmpCols = new List<object>();
            for (int i = 0; i < index; i++)
            {
                tmpCols.Add("");
            }
            return tmpCols;
        }
        private List<NPOIRow> initColumns()
        {
            List<NPOIRow> datas = new List<NPOIRow>();
            List<object> cols = new List<object>();
            for (int index = 0; index < dataGridViewCell.Columns.Count; index++)
            {
                cols.Add(dataGridViewCell.Columns[index].Text);
            }
            NPOIRow nr1 = new NPOIRow();
            nr1.cellValues = cols;
            datas.Add(nr1);
            return datas;
        }
        private void OutputTmpFile(List<List<NPOIRow>> nrDatasList, List<string> strSheetName)
        {
            string fileName;
            if (!ExportResultSecurityHelper.GetExportPermit(FileSimpleTypeHelper.Excel, out fileName))
            {
                return;
            }

            Microsoft.Office.Interop.Excel.Application excel = new Microsoft.Office.Interop.Excel.Application();  //Execl的操作类
            Microsoft.Office.Interop.Excel.Workbook bookDest = excel.Workbooks.Add(Missing.Value);
            Microsoft.Office.Interop.Excel.Worksheet sheetDest = bookDest.Worksheets.Add(Missing.Value, Missing.Value, Missing.Value, Missing.Value) as Microsoft.Office.Interop.Excel.Worksheet;//给工作薄添加一个Sheet   
            sheetDest.Name = strSheetName[0];

            for (int i = bookDest.Worksheets.Count; i > 1; i--)
            {
                Microsoft.Office.Interop.Excel.Worksheet wt = (Microsoft.Office.Interop.Excel.Worksheet)bookDest.Worksheets[i];
                if (!strSheetName.Contains(wt.Name))
                    wt.Delete();
            }
            try
            {
                Microsoft.Office.Interop.Excel.Range rngRow = (Microsoft.Office.Interop.Excel.Range)sheetDest.Columns[1, Type.Missing];
                rngRow.UseStandardWidth = 70;
             
                int idx = 0;
                setExcelRange(sheetDest, "小区基本信息", 14, ref idx);
                setExcelRange(sheetDest, "小区占用情况", 5, ref idx);
                setExcelRange(sheetDest, "小区主覆盖情况", 5, ref idx);
                setExcelRange(sheetDest, "小区重叠覆盖情况", 5, ref idx);
                setExcelRange(sheetDest, "覆盖分段统计", 9, ref idx);
                setExcelRange(sheetDest, "同频6dB内小区干扰情况", 25, ref idx);
                setExcelRange(sheetDest, "邻区作为最强小区时信息", 10, ref idx);
                setExcelRange(sheetDest, "主小区权值信息", 18, ref idx);
                setExcelRange(sheetDest, "邻小区权值信息", 18, ref idx);
                excel.Application.Workbooks.Add(true);

                int row = 2;
                //导入数据行
                foreach (List<NPOIRow> listNPOI in nrDatasList)
                {
                    foreach (NPOIRow npoi in listNPOI)
                    {
                        idx = npoi.cellValues.Count;
                        Microsoft.Office.Interop.Excel.Range cell1ran = sheetDest.get_Range(sheetDest.Cells[row, 1], sheetDest.Cells[row++, idx]);
                        cell1ran.Value2 = npoi.cellValues.ToArray();
                    }
                }
                bookDest.Saved = true;
                bookDest.SaveCopyAs(fileName);//保存
                MessageBox.Show("导出成功！");
            }
            catch (Exception w)
            {
                MessageBox.Show("导出异常:" + w.Message);
            }
            finally
            {
                excel.Quit();
                //GC.Collect();//垃圾回收   
            }
        }

        private void setExcelRange(Microsoft.Office.Interop.Excel.Worksheet sheetDest, string value, int col, ref int idx)
        {
            int row = 1;
            idx += 1;
            int cell1Col = idx;
            idx += col;
            int cell2Col = idx;
            Microsoft.Office.Interop.Excel.Range ran = sheetDest.get_Range(sheetDest.Cells[row, cell1Col], sheetDest.Cells[row, (cell2Col)]);
            ran.Merge(ran.MergeCells);//合并单元格
            ran.HorizontalAlignment = Microsoft.Office.Interop.Excel.XlHAlign.xlHAlignCenter;
            ran.Value2 = value;
        }
        #endregion
        private void miShowGis_Click(object sender, EventArgs e)
        {
            MainModel.ClearDTData();
            string strCellName = "";
            if (this.dataGridViewCell.SelectedIndices.Count == 0)
            {
                return;
            }

            LteSampleMainInfo lteSample;
            var cellName = dataGridViewCell.Items[dataGridViewCell.SelectedIndices[0]];
            strCellName = cellName.SubItems[1].Text;
            if (!dicLteOverlapData.TryGetValue(strCellName, out lteSample))
            {
                return;
            }
            foreach (TestPoint tp in lteSample.LTECellInfo.tpList)
            {
                MainModel.DTDataManager.Add(tp);
            }
            MainModel.CellAntSampleIndexDic = lteSample.LTECellInfo.tpIndexDic;
            MainModel.SelectedLTECell = CellManager.GetInstance().GetLTECellLatest(lteSample.CellName);
            MainModel.FireSetDefaultMapSerialTheme("LTE_SCAN", "TopN_CELL_Specific_RSRP");
            MainModel.FireDTDataChanged(this);
        }
        /// <summary>
        /// 显示当前小区天线覆盖 
        /// </summary>
        private void miShowSimulationAll_Click(object sender, EventArgs e)
        {
            string strCellName = "";
            if (this.dataGridViewCell.SelectedIndices.Count == 0)
            {
                return;
            }
            var cellName = dataGridViewCell.Items[dataGridViewCell.SelectedIndices[0]];
            strCellName = cellName.SubItems[1].Text;
            showSimulation(strCellName, 1);
        }
        /// <summary>
        /// 显示主小区天线覆盖 
        /// </summary>
        private void miShowSimulationMain_Click(object sender, EventArgs e)
        {
            string strCellName = "";
            if (this.dataGridViewCell.SelectedIndices.Count == 0)
            {
                return;
            }
            var cellName = dataGridViewCell.Items[dataGridViewCell.SelectedIndices[0]];
            strCellName = cellName.SubItems[1].Text;
            showSimulation(strCellName, 2);
        }
        /// <summary>
        /// 显示重叠小区天线覆盖 
        /// </summary>
        private void miShowSimulationOverlap_Click(object sender, EventArgs e)
        {
            string strCellName = "";
            if (this.dataGridViewCell.SelectedIndices.Count == 0)
            {
                return;
            }
            var cellName = dataGridViewCell.Items[dataGridViewCell.SelectedIndices[0]];
            strCellName = cellName.SubItems[1].Text;
            showSimulation(strCellName, 3);
        }

        private void showSimulation(string cellName, int iLevel)
        {
            LteSampleMainInfo lteSample;
            if (!dicLteOverlapData.TryGetValue(cellName, out lteSample))
            {
                return;
            }
            if (lteSample != null && lteSample.servCell != null)
            {
                ZTAntennaBase.SimulationPoints simulationPoints = new ZTAntennaBase.SimulationPoints();
                simulationPoints.cellLongitude = lteSample.servCell.Longitude;
                simulationPoints.cellLatitude = lteSample.servCell.Latitude;
                List<LongLat> longLatModelList = new List<LongLat>();
                List<LongLat> longLatTestList = new List<LongLat>();
                simulationPoints.strNet = "LTE";
                AnaCellAngleData(lteSample, ref longLatModelList, ref longLatTestList, iLevel);
                simulationPoints.longLatTestList.AddRange(longLatTestList);
                simulationPoints.longLatModelList.AddRange(longLatModelList);

                MainModel.SimulationPoints = simulationPoints;
                MainModel.SelectedLTECell = CellManager.GetInstance().GetLTECellLatest(cellName);
                MainModel.MainForm.GetMapForm().GoToView(lteSample.servCell.Longitude, lteSample.servCell.Latitude);
                MainModel.FireCellDrawInfoChanged(this);
            }
        }

        /// <summary>
        /// 小区测试模型&理想模型
        /// </summary>
        private void AnaCellAngleData(LteSampleMainInfo lteSample, ref List<LongLat> longLatModelList
            , ref List<LongLat> longLatTestList, int iLevel)
        {
            //权值理想模型
            double[] model1Array;
            double[] model2Array;
            double[] modelMaxArray;

            AntParaItem ant1Item = new AntParaItem(lteSample.antennaPare.strbandtype, lteSample.antennaPare.drangeport1, lteSample.antennaPare.drangeport2, lteSample.antennaPare.drangeport3, lteSample.antennaPare.drangeport4, lteSample.antennaPare.strdevvender);
            ant1Item.Init(lteSample.antennaPare.dphaseport1, lteSample.antennaPare.dphaseport2, lteSample.antennaPare.dphaseport3, lteSample.antennaPare.dphaseport4);
            AntParaItem ant2Item = new AntParaItem(lteSample.antennaPare.strbandtype, lteSample.antennaPare.drangeport5, lteSample.antennaPare.drangeport6, lteSample.antennaPare.drangeport7, lteSample.antennaPare.drangeport8, lteSample.antennaPare.strdevvender);
            ant1Item.Init(lteSample.antennaPare.dphaseport5, lteSample.antennaPare.dphaseport6, lteSample.antennaPare.dphaseport7, lteSample.antennaPare.dphaseport8);

            model1Array = ant1Item.getPowerArray();
            model2Array = ant2Item.getPowerArray();
            modelMaxArray = getMaxPowerArray(model1Array, model2Array);

            LongLat ll = new LongLat();
            ll.fLongitude = (float)(lteSample.servCell.Longitude);
            ll.fLatitude = (float)(lteSample.servCell.Latitude);

            double[] rsrpNewArray = new double[360];
            switch (iLevel)
            {
                case 1:
                    rsrpNewArray = newRsrpArray(lteSample.LTECellInfo);
                    break;
                case 2:
                    rsrpNewArray = newRsrpArray(lteSample.LTEMainCellInfo);
                    break;
                case 3:
                    rsrpNewArray = newRsrpArray(lteSample.LTEOverlapCellInfo);
                    break;
            }
            //Model
            int iMaxValue = -50;
            int iMinValue = 50;
            ZTAntFuncHelper.getMaxAndMinValue(rsrpNewArray, ref iMaxValue, ref iMinValue);
            longLatTestList = ZTAntFuncHelper.getCellEmulateCoverTest(ll, rsrpNewArray, iMaxValue, iMinValue, lteSample.servCell.Direction);

            int iMaxValue2 = -50;
            int iMinValue2 = 50;
            ZTAntFuncHelper.getMaxAndMinValue(modelMaxArray, ref iMaxValue2, ref iMinValue2);
            longLatModelList = ZTAntFuncHelper.getCellEmulateCoverModel(ll, modelMaxArray, iMaxValue2, iMinValue, lteSample.servCell.Direction);
        }
        /// 取两数组中的大值
        /// </summary>
        public double[] getMaxPowerArray(double[] power1Array, double[] power2Array)
        {
            double[] powerArray = new double[180];
            for (int i = 0; i < 180; i++)
            {
                double dTmp = power1Array[i] >= power2Array[i] ? power1Array[i] : power2Array[i];
                powerArray[i] = dTmp > -120 ? dTmp : -120;
            }
            return powerArray;
        }
        /// <summary>
        /// 平滑RSRP
        /// </summary>
        public double[] newRsrpArray(LteCellCoverInfo LTECellCover)
        {
            int iStart = 0;
            int iEnd = 360;
            double[] rsrpPathLossArray = new double[360];//修正距离损耗RSRP
            double[] rsrpUnifiedArray = new double[360];//归一化RSRP
            double[] rsrpNewArray = new double[360];//平滑RSRP

            int[] rsrpArray = LTECellCover.rsrpArray;
            double[] sampleDistArray = LTECellCover.sampArray;
            int[] sampleNumArray = LTECellCover.sampNumArray;
            double dSumValue = 0;
            double dNewValue = 0;
            for (int i = iStart; i < iEnd; i++)
            {
                double dTmpRsrp = (double)(sampleNumArray[i] == 0 ? -140 : rsrpArray[i] / sampleNumArray[i]);
                double dTmpDist = Math.Round((sampleNumArray[i] == 0 ? 0 : sampleDistArray[i] / sampleNumArray[i]), 2) / 1000;
                //修正自由空间路损
                double dFreeSpacePathLoss = dTmpRsrp == -140 ? 0 : 33 * Math.Log10(dTmpDist);
                double dDistancePathLoss = dTmpRsrp + dFreeSpacePathLoss;
                rsrpPathLossArray[i] = dDistancePathLoss;
                double dTmpValue = Math.Pow(10, dDistancePathLoss / 10);
                dSumValue += dTmpValue;
            }
            dNewValue = 10 * Math.Log10(dSumValue / 360);

            for (int i = iStart; i < iEnd; i++)
            {
                double dDistancePathLoss = rsrpPathLossArray[i];
                double dUnifiedValue = dDistancePathLoss > -120 ? dDistancePathLoss - dNewValue : -20;
                rsrpUnifiedArray[i] = dUnifiedValue;
            }
            for (int i = iStart; i < iEnd; i++)
            {
                double iNewRsrp = ZTAntFuncHelper.getAvgRsrp(rsrpUnifiedArray, i);
                rsrpNewArray[i] = iNewRsrp;
            }
            return rsrpNewArray;

        }
    }
}
