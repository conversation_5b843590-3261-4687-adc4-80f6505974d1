﻿using System;
using System.Collections.Generic;
using System.Text;
using System.IO;

using MasterCom.Util.UiEx;
using MasterCom.RAMS.Model;
using Excel = Microsoft.Office.Interop.Excel;
using MasterCom.RAMS.Func;
using System.Drawing;

namespace MasterCom.RAMS.ZTFunc
{
    public class GsmStationAcceptManager
    {
        protected string saveFolder = "";
        public virtual void SetAcceptCond(string saveFolder)
        {
            this.saveFolder = saveFolder;
           
            this.acceptorList = new List<GsmStationAcceptBase>()
            {
                new GsmAcpVoice(),
                new GsmAcpGPRSAttachRate(),
                new GsmAcpPingRate(),
                new GsmAcpFtpDownload(),
                new GsmAcpCoverPicture(),
                new GsmAcpWithInstationHandOver(),
                new GsmAcpBetweenstationHandOver(),
                new GsmAcpHomePage(),
            };
        }

        public virtual void AnalyzeFile(MasterCom.RAMS.Model.FileInfo fileInfo, DTFileDataManager fileManager)
        {
            try
            {
                ICell targetCell = GetTargetCell(fileManager);
                if (targetCell == null || targetCell is UnknowCell)
                {
                    log.Info(string.Format("文件{0}未找到目标小区", fileInfo.Name));
                    return;
                }

                foreach (GsmStationAcceptBase acp in acceptorList)
                {
                    acp.AnalyzeFile(fileInfo, fileManager, targetCell);
                }
            }
            catch
            {
                clear();
                throw;
            }
        }

        public virtual void DoWorkAfterAnalyze()
        {
            try
            {
                Dictionary<string, int> btsDic = FindAllBts();
                CreateFileForBts(btsDic);
            }
            finally
            {
                clear();
            }
        }

        public virtual void CreateFileForBts(Dictionary<string, int> btsDic)
        {
            foreach (string bts in btsDic.Keys)
            {
                int cellCount = 0;
                foreach (GsmStationAcceptBase acp in acceptorList)
                {
                    cellCount = Math.Max(cellCount, acp.GetCellCount(bts));
                }
                string targetFile = GetTargetFile(bts, cellCount, saveFolder);

                Excel.Application xlApp = null;
                try
                {
                    WaitTextBox.Text = "正在导出Excel...";
                    xlApp = new Excel.Application();
                    xlApp.Visible = false;
                    Excel.Workbook eBook = xlApp.Workbooks.Open(targetFile);

                    foreach (GsmStationAcceptBase acp in acceptorList)
                    {
                        acp.FillResult(bts, eBook);
                    }

                    eBook.Save();
                    eBook.Close(Type.Missing, Type.Missing, Type.Missing);
                }
                finally
                {
                    if (xlApp != null)
                    {
                        xlApp.Quit();
                    }
                }
            }
        }

        #region 重置图例
        public static void ReSetRxLevelMapView()
        {
            MapSerialInfo msi = DTLayerSerialManager.Instance.GetSerialByName("RxLevSub");
            if (msi != null)
            {
                List<RangeInfo> ranges = new List<RangeInfo>();
                ranges.Add(new RangeInfo(false, false, int.MinValue, -90, Color.Red));
                ranges.Add(new RangeInfo(true, false, -90, -80, Color.Yellow));
                ranges.Add(new RangeInfo(true, false, -80, -70, Color.Blue));
                ranges.Add(new RangeInfo(true, false, -70, int.MaxValue, Color.Green));

                msi.ColorDisplayParam.Info.RangeColors = new List<DTParameterRangeColor>();
                foreach (RangeInfo range in ranges)
                {
                    DTParameterRangeColor paramColor = new DTParameterRangeColor(range.Min, range.Max, range.RangeColor);
                    paramColor.MaxIncluded = range.InculdeMax;
                    paramColor.MinIncluded = range.InculdeMin;
                    msi.ColorDisplayParam.Info.RangeColors.Add(paramColor);
                }
            }
        }

        public static void ReSetRxQualityMapView()
        {
            MapSerialInfo msi = DTLayerSerialManager.Instance.GetSerialByName("RxQualSub");
            if (msi != null)
            {
                List<RangeInfo> ranges = new List<RangeInfo>();
                ranges.Add(new RangeInfo(true, true, 0, 1, Color.Green));
                ranges.Add(new RangeInfo(false, true, 1, 3, Color.Blue));
                ranges.Add(new RangeInfo(false, true, 3, 5, Color.Yellow));
                ranges.Add(new RangeInfo(false, true, 5, 7, Color.Red));

                msi.ColorDisplayParam.Info.RangeColors = new List<DTParameterRangeColor>();
                foreach (RangeInfo range in ranges)
                {
                    DTParameterRangeColor paramColor = new DTParameterRangeColor(range.Min, range.Max, range.RangeColor);
                    paramColor.MaxIncluded = range.InculdeMax;
                    paramColor.MinIncluded = range.InculdeMin;
                    msi.ColorDisplayParam.Info.RangeColors.Add(paramColor);
                }
            }
        }

        class RangeInfo
        {
            public RangeInfo(bool inculdeMin, bool inculdeMax, int min, int max, Color rangeColor)
            {
                Min = min;
                InculdeMin = inculdeMin;
                Max = max;
                InculdeMax = inculdeMax;
                RangeColor = rangeColor;
            }

            public int Min
            {
                get;
                private set;
            }
            public bool InculdeMin
            {
                get;
                private set;
            }
            public int Max
            {
                get;
                private set;
            }
            public bool InculdeMax
            {
                get;
                private set;
            }

            public Color RangeColor
            {
                get;
                private set;
            }
        }
        #endregion

        protected virtual void clear()
        {
            foreach (GsmStationAcceptBase acp in acceptorList)
            {
                acp.Clear();
            }
        }
        protected virtual Cell GetTargetCell(DTFileDataManager fileManager)
        {
            bool isHandoverFile = false;
            if (fileManager.FileName.Contains("切换") || fileManager.FileName.Contains("站间切换"))
            {
                isHandoverFile = true;
            }

            Cell targeCell = null;
            foreach (TestPoint tp in fileManager.TestPoints)
            {
                Cell cell = GetTpSrcCell(tp);
                if (cell == null)
                {
                    continue;
                }
                string keyStr = cell.Name;
                if (isHandoverFile && cell.BelongBTS != null)
                {
                    keyStr = cell.BelongBTS.Name;
                }
                if (fileManager.FileName.Contains(keyStr.Trim()))
                {
                    targeCell = cell;
                    break;
                }
            }
            return targeCell;
        }

        public static Cell GetTpSrcCell(TestPoint tp)
        {
            Cell cell = null;
            if (tp is TestPointDetail)
            {
                cell = CellManager.GetInstance().GetNearestCell(DateTime.Now, (ushort?)(int?)tp["LAC"], (ushort?)(int?)tp["CI"]
                    , tp.Longitude, tp.Latitude);

                short? bcch = (short?)tp["BCCH"];
                byte? bsic = (byte?)tp["BSIC"];

                if (bcch != null && bsic != null
                    && (cell == null || cell.BCCH != (short)bcch || cell.BSIC != (byte)bsic))
                {
                    cell = CellManager.GetInstance().GetNearestCell(DateTime.Now, (short)bcch, (byte)bsic, tp.Longitude, tp.Latitude);
                }
            }
            return cell;
        }

        public virtual string GetTargetFile(string btsName, int cellCount, string saveFolder)
        {
            if (cellCount > 6)
            {
                throw (new Exception(string.Format("基站{0}小区数超过6个，不支持报告导出", btsName)));
            }

            string templateFile = "新疆中移GSM搬迁工程优化单站验证(宏站)-XXXX站-ZGH-模板.xlsx";
            templateFile = Path.Combine(workDir, templateFile);

            string targetFile = string.Format("GSM搬迁站验收_{0}.xlsx", btsName);
            targetFile = Path.Combine(saveFolder, targetFile);
            if (File.Exists(targetFile))
            {
                File.Delete(targetFile);
            }
            File.Copy(templateFile, targetFile, true);
            return targetFile;
        }

        protected virtual Dictionary<string, int> FindAllBts()
        {
            Dictionary<string, int> btsDic = new Dictionary<string, int>();
            foreach (GsmStationAcceptBase acp in acceptorList)
            {
                foreach (string bts in acp.BtsNames)
                {
                    if (!btsDic.ContainsKey(bts))
                    {
                        btsDic.Add(bts, 0);
                    }
                    ++btsDic[bts];
                }
            }
            return btsDic;
        }

        private static readonly string workDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "userdata/GsmStationAcceptance");
        protected virtual List<GsmStationAcceptBase> acceptorList
        {
            get;
            set;
        }

        protected static readonly log4net.ILog log = log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
    }
}
