﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using Excel = Microsoft.Office.Interop.Excel;

namespace MasterCom.RAMS.ZTFunc.ZTStationAcceptance_XJ
{
    class NR700MStationAcceptManager : NRStationAcceptManager
    {
        protected override string templateFileName { get { return "NR700M验收模板.xlsx"; } }
        protected override string reportFileName { get { return "NR700M验收"; } }

        public override void SetAcceptCond(StationAcceptConditionBase cond)
        {
            Singleton<NR700MStationAcceptConfigHelper>.Instance.LoadConfig();
            workDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "userdata/NR700MStationAcceptance");
            string picPath = Path.Combine(workDir, "Pictures");
            acceptCond = cond;

            acceptorList = new List<StationAcceptBase>()
            {
                new NR700MAcpAccRate(),
                new NR700MAcpGoodPointFtpDownload(),
                new NR700MAcpGoodPointFtpUpload(),
                new NR700MAcpBadPointFtpDownload(),
                new NR700MAcpBadPointFtpUpload(),
                //new NR700MAcpEPSFBRate(),
                //new NR700MAcpEPSFBDelay(),
                //new NR700MAcpEPSFB_FRRate(),
                //new NR700MAcpEPSFB_FRDelay(),
                new NR700MAcpEPSFB(),
                new NR700MAcpPing(),
                new NR700MAcpHandoverRate(),
                new NR700MAcpChartLinePic(picPath),
                new NR700MAcpCoverPic(picPath),
                new NR700MAcpHandoverPic(picPath),
            };
        }

        protected override string getPath()
        {
            return Singleton<NR700MStationAcceptConfigHelper>.Instance.GetCurSavePath();
        }

        public override void AnalyzeFile(Model.FileInfo fileInfo, DTFileDataManager fileManager)
        {
            try
            {
                string errMsg = "";
                bool isHandoverFile = fileManager.FileName.Contains("切换");
                NRCell targetCell = getTargetCell(fileManager, isHandoverFile, out errMsg);
                if (!string.IsNullOrEmpty(errMsg))
                {
                    errMsg = $"文件{fileInfo.Name}未找到目标小区;{errMsg}";
                    log.Info(errMsg);
                    ErrMsg.AppendLine(errMsg);
                    return;
                }

                string fileBtsName = NRStationAcceptFileNameHelper.GetFileBtsName(targetCell, fileManager.FileName, isHandoverFile, out errMsg);
                if (!string.IsNullOrEmpty(errMsg))
                {
                    errMsg = $"文件{fileInfo.Name}未找到目标基站;{errMsg}";
                    log.Info(errMsg);
                    ErrMsg.AppendLine(errMsg);
                    return;
                }

                analyzeFile(fileInfo, fileManager, targetCell, fileBtsName, isHandoverFile);
            }
            catch (Exception e)
            {
                string errMsg = $"[{fileInfo.Name}]文件分析时产生异常;";
                log.Error(string.Format("{0} : {1}", errMsg, e.StackTrace));
                ErrMsg.AppendLine(errMsg);
            }
        }

        protected new void analyzeFile(Model.FileInfo fileInfo, DTFileDataManager fileManager, NRCell targetCell, string fileBtsName, bool isHandoverFile)
        {
            var curCond = acceptCond as NR700MStationAcceptCondition;
            curCond.NRServiceType = NRStationAcceptFileNameHelper.GetServiceName(fileInfo.Name);
            if (curCond.NRServiceType != NRServiceName.SA)
            {
                string errMsg = $"[{fileInfo.Name}]不为SA文件";
                log.Debug(errMsg);
                ErrMsg.AppendLine(errMsg);
                return;
            }
            string btsName = targetCell.BelongBTS.Name;
            if (!BtsInfoDic.TryGetValue(btsName, out BtsInfoBase bts))
            {
                bts = initBtsInfo(targetCell, fileBtsName);
                BtsInfoDic.Add(btsName, bts);
            }

            NR700MBtsInfo nrBtsInfo = bts as NR700MBtsInfo;
            nrBtsInfo.Init(curCond.NRServiceType);

            if (!bts.CellInfoDic.TryGetValue(targetCell.Name, out CellInfoBase cell))
            {
                cell = initCellInfo(targetCell);
                if (!isHandoverFile)
                {
                    //切换文件不增加小区结果数
                    bts.CellInfoDic.Add(targetCell.Name, cell);
                }
            }
            NR700MCellInfo nrCellInfo = cell as NR700MCellInfo;
            nrCellInfo.Init(targetCell);

            foreach (StationAcceptBase acp in acceptorList)
            {
                acp.AnalyzeFile(fileInfo, fileManager, bts, cell, curCond);
            }
        }

        protected override BtsInfoBase initBtsInfo(NRCell targetCell, string fileBtsName)
        {
            return new NR700MBtsInfo(targetCell.BelongBTS, fileBtsName);
        }

        protected override CellInfoBase initCellInfo(NRCell targetCell)
        {
            return new NR700MCellInfo(targetCell);
        }

        protected override void getExportedFiles(StringBuilder exportedFiles, BtsInfoBase bts)
        {
            NR700MBtsInfo nrBtsInfo = bts as NR700MBtsInfo;
            exportedFiles.Append(nrBtsInfo.BtsName);
            exportedFiles.Append(",");
        }

        protected override void fillResult(BtsInfoBase bts, Excel.Workbook eBook)
        {
            //fillHomePage(eBook, bts);
            fillNetOptimizationTestPageSA(eBook, bts);
            fillCoverPicPageSA(eBook, bts);
        }

        #region fillHomePage
        //private void fillHomePage(Excel.Workbook eBook, BtsInfoBase btsInfo)
        //{

        //    Excel.Worksheet sheet = (Excel.Worksheet)eBook.Sheets[2];
        //    sheet.get_Range("C5").set_Value(Type.Missing, btsInfo.BtsName);

        //    NR700MBtsInfo nrBtsInfo = btsInfo as NR700MBtsInfo;
        //    if (nrBtsInfo.BtsBaseInfo != null)
        //    {
        //        NR700MBtsParameters nrBaseInfo = nrBtsInfo.BtsBaseInfo as NR700MBtsParameters;

        //        sheet.get_Range("T5").set_Value(Type.Missing, nrBaseInfo.ENodeBID);
        //        sheet.get_Range("C7").set_Value(Type.Missing, nrBaseInfo.Address);
        //        sheet.get_Range("T7").set_Value(Type.Missing, nrBaseInfo.Country);

        //        setCellValue(sheet.Cells, 11, 6, nrBaseInfo.BtsLongutide.Planing, nrBaseInfo.BtsLongutide.IsValid);
        //        setCellValue(sheet.Cells, 11, 9, nrBaseInfo.BtsLatitude.Planing, nrBaseInfo.BtsLatitude.IsValid);
        //        setCellValue(sheet.Cells, 11, 12, nrBaseInfo.BtsLongutide.Real, nrBaseInfo.BtsLongutide.IsValid);
        //        setCellValue(sheet.Cells, 11, 16, nrBaseInfo.BtsLatitude.Real, nrBaseInfo.BtsLatitude.IsValid);
        //        setCellValue(sheet.Cells, 11, 19, nrBaseInfo.BtsLongutide.IsValid && nrBaseInfo.BtsLatitude.IsValid);
        //        setCellValue(sheet.Cells, 12, 6, nrBaseInfo.BBU.Planing, nrBaseInfo.BBU.IsValid);
        //        setCellValue(sheet.Cells, 12, 12, nrBaseInfo.BBU.Real, nrBaseInfo.BBU.IsValid);
        //        setCellValue(sheet.Cells, 12, 19, nrBaseInfo.BBU.IsValid);
        //        setCellValue(sheet.Cells, 13, 6, nrBaseInfo.RRU.Planing, nrBaseInfo.RRU.IsValid);
        //        setCellValue(sheet.Cells, 13, 12, nrBaseInfo.RRU.Real, nrBaseInfo.RRU.IsValid);
        //        setCellValue(sheet.Cells, 13, 19, nrBaseInfo.RRU.IsValid);
        //        setCellValue(sheet.Cells, 14, 6, nrBaseInfo.CellCount.Planing, nrBaseInfo.CellCount.IsValid);
        //        setCellValue(sheet.Cells, 14, 12, nrBaseInfo.CellCount.Real, nrBaseInfo.CellCount.IsValid);
        //        setCellValue(sheet.Cells, 14, 19, nrBaseInfo.CellCount.IsValid);
        //        setCellValue(sheet.Cells, 15, 6, nrBaseInfo.TAC.Planing, nrBaseInfo.TAC.IsValid);
        //        setCellValue(sheet.Cells, 15, 12, nrBaseInfo.TAC.Real, nrBaseInfo.TAC.IsValid);
        //        setCellValue(sheet.Cells, 15, 19, nrBaseInfo.TAC.IsValid);

        //        ExcelCell antennaInfo = new ExcelCell(18, 6, 13, 5);
        //        setCellResInfo(sheet, antennaInfo, new List<NR700MCellParameters>(nrBaseInfo.CellDic.Values), setCellAntennaInfo);
        //    }

        //    ExcelCell fileAnaInfo = new ExcelCell(59, 6, 9, 4);
        //    setCellResInfo(sheet, fileAnaInfo, btsInfo.CellInfoList, setFileAnaResult);
        //}

        //delegate void SetResFunc<in T>(Excel.Worksheet sheet, int rowIdx, int colIdx, T nrCellInfo);
        //private void setCellResInfo<T>(Excel.Worksheet sheet, ExcelCell info, List<T> dataList, SetResFunc<T> func)
        //{
        //    int cellIndex = 0;
        //    int rowIdx = info.FstRowIdx;
        //    int colIdx;
        //    foreach (var cellInfo in dataList)
        //    {
        //        if (cellIndex % 3 == 0)
        //        {
        //            rowIdx += info.RowInterval * (cellIndex / 3);
        //            colIdx = info.FstColIdx;
        //        }
        //        else
        //        {
        //            colIdx = info.FstColIdx + ((cellIndex % 3) * info.ColInterval);
        //        }

        //        func(sheet, rowIdx, colIdx, cellInfo);
        //        cellIndex++;
        //    }
        //}

        //private void setCellAntennaInfo(Excel.Worksheet sheet, int rowIdx, int colIdx, NR700MCellParameters cellInfo)
        //{
        //    setCellValue(sheet.Cells, rowIdx, colIdx, cellInfo.CellName, true);
        //    setCellBaseInfo(sheet, rowIdx + 2, colIdx, cellInfo.Longitude);
        //    setCellBaseInfo(sheet, rowIdx + 3, colIdx, cellInfo.Latitude);
        //    setCellBaseInfo(sheet, rowIdx + 4, colIdx, cellInfo.CellID);
        //    setCellBaseInfo(sheet, rowIdx + 5, colIdx, cellInfo.PCI);
        //    setCellBaseInfo(sheet, rowIdx + 6, colIdx, cellInfo.FreqBand);
        //    setCellBaseInfo(sheet, rowIdx + 7, colIdx, cellInfo.Freq);
        //    setCellBaseInfo(sheet, rowIdx + 8, colIdx, cellInfo.SSBFreq);
        //    setCellBaseInfo(sheet, rowIdx + 9, colIdx, cellInfo.Bandwidth);
        //    setCellBaseInfo(sheet, rowIdx + 10, colIdx, cellInfo.PRACH);
        //    setCellBaseInfo(sheet, rowIdx + 11, colIdx, cellInfo.SubFrameRatio);
        //    setCellBaseInfo(sheet, rowIdx + 12, colIdx, cellInfo.Channels);
        //}

        //private void setCellBaseInfo<T>(Excel.Worksheet sheet, int rowIdx, int colIdx, ParamInfo<T> info)
        //{
        //    setCellValue(sheet.Cells, rowIdx, colIdx, info.Planing, info.IsValid);
        //    setCellValue(sheet.Cells, rowIdx, colIdx + 2, info.Real, info.IsValid);
        //    setCellValue(sheet.Cells, rowIdx, colIdx + 4, info.IsValid);
        //}

        //private void setFileAnaResult(Excel.Worksheet sheet, int rowIdx, int colIdx, CellInfoBase cellInfo)
        //{
        //    NR700MCellInfo nrCellInfo = cellInfo as NR700MCellInfo;
        //    if (nrCellInfo.SAInfo != null)
        //    {
        //        setCellValue(sheet.Cells, rowIdx, colIdx, nrCellInfo.SAInfo.GNBAddInfo.IsValid);
        //        setCellValue(sheet.Cells, rowIdx + 1, colIdx, nrCellInfo.SAInfo.HandOverInfo.IsValid);
        //        setCellValue(sheet.Cells, rowIdx + 2, colIdx, nrCellInfo.SAInfo.AccessInfo.IsValid);
        //        setCellValue(sheet.Cells, rowIdx + 3, colIdx, nrCellInfo.SAInfo.SmallPackageDelay.IsValid
        //     && nrCellInfo.SAInfo.BigPackageDelay.IsValid);
        //        setCellValue(sheet.Cells, rowIdx + 4, colIdx, nrCellInfo.SAInfo.SampleDL.IsValid);
        //        setCellValue(sheet.Cells, rowIdx + 5, colIdx, nrCellInfo.SAInfo.SampleUL.IsValid);
        //        setCellValue(sheet.Cells, rowIdx + 6, colIdx, nrCellInfo.SAInfo.EPSFBInfo.IsValid
        //            && nrCellInfo.SAInfo.EPSFBDelay.IsValid);
        //        setCellValue(sheet.Cells, rowIdx + 7, colIdx, nrCellInfo.SAInfo.LeakOutLock.IsValid);
        //    }

        //    colIdx += 2;
        //    if (nrCellInfo.NSAInfo != null)
        //    {
        //        setCellValue(sheet.Cells, rowIdx, colIdx, nrCellInfo.NSAInfo.GNBAddInfo.IsValid);
        //        setCellValue(sheet.Cells, rowIdx + 1, colIdx, nrCellInfo.NSAInfo.HandOverInfo.IsValid);
        //        setCellValue(sheet.Cells, rowIdx + 2, colIdx, nrCellInfo.NSAInfo.AccessInfo.IsValid);
        //        setCellValue(sheet.Cells, rowIdx + 3, colIdx, nrCellInfo.NSAInfo.SmallPackageDelay.IsValid
        //     && nrCellInfo.NSAInfo.BigPackageDelay.IsValid);
        //        setCellValue(sheet.Cells, rowIdx + 4, colIdx, nrCellInfo.NSAInfo.SampleDL.IsValid);
        //        setCellValue(sheet.Cells, rowIdx + 5, colIdx, nrCellInfo.NSAInfo.SampleUL.IsValid);
        //        setCellValue(sheet.Cells, rowIdx + 6, colIdx, nrCellInfo.NSAInfo.EPSFBInfo.IsValid
        //            && nrCellInfo.NSAInfo.EPSFBDelay.IsValid);
        //        setCellValue(sheet.Cells, rowIdx + 7, colIdx, nrCellInfo.NSAInfo.LeakOutLock.IsValid);
        //    }
        //}
        #endregion

        #region fillNetOptimizationTestPage
        private void fillNetOptimizationTestPageSA(Excel.Workbook eBook, BtsInfoBase btsInfo)
        {
            Excel.Worksheet sheet = (Excel.Worksheet)eBook.Sheets[3];
            sheet.get_Range("A1").set_Value(Type.Missing, btsInfo.BtsName + "-NR单站验证测试表格");
            sheet.get_Range("C2").set_Value(Type.Missing, btsInfo.BtsName);
            sheet.get_Range("S2").set_Value(Type.Missing, btsInfo.BtsID);
            sheet.get_Range("AC2").set_Value(Type.Missing, DateTime.Now.ToShortDateString());

            double width = 350;
            double height = 250;

            int cellIndex = 0;
            foreach (var cellInfo in btsInfo.CellInfoList)
            {
                NR700MCellInfo nrCellInfo = cellInfo as NR700MCellInfo;
                if (nrCellInfo.SAInfo == null)
                {
                    continue;
                }

                int rowIdx = 7 + (cellIndex * 16);

                setCellValue(sheet.Cells, rowIdx, 1, nrCellInfo.SAInfo.Cell.Name, true);

                //折线图
                insertExcelPicture(sheet, nrCellInfo.SAInfo.NRDLLineChartPic.PicPath, rowIdx
                    , 51, width, height);//AY
                insertExcelPicture(sheet, nrCellInfo.SAInfo.NRULLineChartPic.PicPath, rowIdx
                    , 58, width, height);//BF

                //Access Success Rate
                rowIdx = rowIdx + 1;
                setKpiInfo(sheet, rowIdx, nrCellInfo.SAInfo.AccessInfo);

                //Ping
                rowIdx = rowIdx + 1;
                setKpiInfo(sheet, rowIdx, nrCellInfo.SAInfo.SmallPackagePing, true);
                rowIdx = rowIdx + 1;
                setKpiInfo(sheet, rowIdx, nrCellInfo.SAInfo.BigPackagePing, true);

                //FTP
                rowIdx = rowIdx + 2;
                setFtpPointInfo(sheet, rowIdx, 16, nrCellInfo.SAInfo.GoodSampleDL);
                setFtpPointInfo(sheet, rowIdx + 3, 16, nrCellInfo.SAInfo.GoodSampleUL);
                setFtpPointInfo(sheet, rowIdx, 33, nrCellInfo.SAInfo.BadSampleDL);
                setFtpPointInfo(sheet, rowIdx + 3, 33, nrCellInfo.SAInfo.BadSampleUL);

                //EPSFB
                rowIdx = rowIdx + 7;
                setKpiInfo(sheet, rowIdx, nrCellInfo.SAInfo.EpsfbNrCallNr, false);
                rowIdx = rowIdx + 1;
                setKpiInfo(sheet, rowIdx, nrCellInfo.SAInfo.EpsfbNrCallLte, false);
                rowIdx = rowIdx + 1;
                setKpiInfo(sheet, rowIdx, nrCellInfo.SAInfo.EpsfbNrCallNrFR, false);
                rowIdx = rowIdx + 1;
                setKpiInfo(sheet, rowIdx, nrCellInfo.SAInfo.EpsfbNrCallLteFR, false);

                ////5G切换成功率
                //rowIdx = rowIdx + 8;
                //setSuccessRateKpiInfo(sheet, rowIdx, nrCellInfo.SAInfo.HandOverInfo);

                cellIndex++;
            }

            NR700MBtsInfo nrBtsInfo = btsInfo as NR700MBtsInfo;
            if (nrBtsInfo.SABtsInfo == null)
            {
                return;
            }

            int btsRowIdx = 151;
            foreach (var info in nrBtsInfo.SABtsInfo.FileBtsHandOverInfoList)
            {
                setKpiInfo(sheet, btsRowIdx, info.IntraHandoverRate);
                setKpiInfo(sheet, btsRowIdx + 1, info.InterHandoverRate);
                btsRowIdx += 2;
            }
        }

        private void setFtpPointInfo(Excel.Worksheet sheet, int rowIdx, int colIdx, FtpPointInfo info)
        {
            setCellValue(sheet.Cells, rowIdx, colIdx, info.Rsrp.Data, info.Rsrp.IsValid);
            setCellValue(sheet.Cells, rowIdx + 1, colIdx, info.Sinr.Data, info.Sinr.IsValid);
            setCellValue(sheet.Cells, rowIdx + 2, colIdx, info.Throughput.Data, info.Throughput.IsValid);
        }

        private void setKpiInfo(Excel.Worksheet sheet, int rowIdx, SuccessRateKpiInfo rate)
        {
            setCellValue(sheet.Cells, rowIdx, 16, rate.RequestCnt, rate.IsValid);
            setCellValue(sheet.Cells, rowIdx, 23, rate.SucceedCnt, rate.IsValid);
            setCellValue(sheet.Cells, rowIdx, 29, rate.SuccessRate, rate.IsValid);
        }

        private void setKpiInfo(Excel.Worksheet sheet, int rowIdx, NR700MCellServiceInfo.EvtInfo evtInfo, bool setShake)
        {
            setCellValue(sheet.Cells, rowIdx, 16, evtInfo.RateInfo.RequestCnt, evtInfo.RateInfo.IsValid);
            setCellValue(sheet.Cells, rowIdx, 23, evtInfo.RateInfo.SucceedCnt, evtInfo.RateInfo.IsValid);
            setCellValue(sheet.Cells, rowIdx, 29, evtInfo.RateInfo.SuccessRate, evtInfo.RateInfo.IsValid);
            if (evtInfo.Delay != null)
            {
                setCellValue(sheet, rowIdx, 35, evtInfo, "/", setShake);
            }
        }

        /// <summary>
        /// 将2个数据写入同一个单元格
        /// 并且根据其有效性设置颜色
        /// </summary>
        protected void setCellValue(Excel.Worksheet sheet, int rowIndex, int colIndex
            , NR700MCellServiceInfo.EvtInfo evtInfo, string splitStr, bool setShake)
        {
            string col = MasterCom.Util.ExcelHelper.GetColNameByIndex(colIndex);
            string cell = $"{col}{rowIndex}";

            var range = sheet.get_Range(cell);

            if (setShake)
            {
                string delay = evtInfo.Delay.Data.ToString();
                string shake = evtInfo.Shake.Data.ToString();
                string str = shake + splitStr + delay;
                range.set_Value(Type.Missing, str);

                int idx = 1;
                setColor(evtInfo.Shake.IsValid, range, shake, idx);

                idx += shake.Length + splitStr.Length;
                setColor(evtInfo.Delay.IsValid, range, delay, idx);
            }
            else
            {
                string delay = evtInfo.Delay.Data.ToString();
                range.set_Value(Type.Missing, delay);

                int idx = 1;
                setColor(evtInfo.Delay.IsValid, range, delay, idx);
            }
        }

        private void setColor(bool isValid, Excel.Range range, string data, int idx)
        {
            if (isValid)
            {
                range.Characters[idx, data.Length].Font.ColorIndex = 1;
            }
            else
            {
                range.Characters[idx, data.Length].Font.ColorIndex = 3;
            }
        }
        #endregion

        private void fillCoverPicPageSA(Excel.Workbook eBook, BtsInfoBase btsInfo)
        {
            Excel.Worksheet picPageSheet = (Excel.Worksheet)eBook.Sheets[4];

            double width = 300;
            double height = 190;

            int cellIndex = 0;
            foreach (var cellInfo in btsInfo.CellInfoList)
            {
                NR700MCellInfo nrCellInfo = cellInfo as NR700MCellInfo;
                if (nrCellInfo.SAInfo == null)
                {
                    continue;
                }
                int paraColIndex = 1 + (cellIndex * 2);

                //SS-RSRP
                setCellValue(picPageSheet.Cells, 6, paraColIndex, nrCellInfo.SAInfo.Cell.Name, true);
                insertExcelPicture(picPageSheet, nrCellInfo.SAInfo.NRRsrpPic.PicPath, 7, paraColIndex, width, height);
                //SS-SINR
                setCellValue(picPageSheet.Cells, 10, paraColIndex, nrCellInfo.SAInfo.Cell.Name, true);
                insertExcelPicture(picPageSheet, nrCellInfo.SAInfo.NRSinrPic.PicPath, 11, paraColIndex, width, height);
                //小区覆盖RSRP：SSB
                setCellValue(picPageSheet.Cells, 15, paraColIndex, nrCellInfo.SAInfo.Cell.Name, true);
                insertExcelPicture(picPageSheet, nrCellInfo.SAInfo.NRRsrpPic.PicPath, 16, paraColIndex, width, height);
                //小区覆盖SINR：SSB
                setCellValue(picPageSheet.Cells, 19, paraColIndex, nrCellInfo.SAInfo.Cell.Name, true);
                insertExcelPicture(picPageSheet, nrCellInfo.SAInfo.NRSinrPic.PicPath, 20, paraColIndex, width, height);
                //小区上传下载速率验证
                setCellValue(picPageSheet.Cells, 23, paraColIndex, nrCellInfo.SAInfo.Cell.Name, true);
                insertExcelPicture(picPageSheet, nrCellInfo.SAInfo.NRULPic.PicPath, 24, paraColIndex, width, height);
                setCellValue(picPageSheet.Cells, 25, paraColIndex, nrCellInfo.SAInfo.Cell.Name, true);
                insertExcelPicture(picPageSheet, nrCellInfo.SAInfo.NRDLPic.PicPath, 26, paraColIndex, width, height);
                cellIndex++;
            }

            NR700MBtsInfo nrBtsInfo = btsInfo as NR700MBtsInfo;
            if (nrBtsInfo.SABtsInfo == null)
            {
                return;
            }

            int btsColIdx = 1;
            foreach (var info in nrBtsInfo.SABtsInfo.FileBtsHandOverInfoList)
            {
                setCellValue(picPageSheet.Cells, 27, btsColIdx, info.FileBtsName, true);
                insertExcelPicture(picPageSheet, info.PCIPicInfo.PicPath, 28, btsColIdx, 920, 290);
                btsColIdx += 6;
            }
        }

        public void InsertExcelPicture(Excel.Workbook eBook, string startCell, string picPath)
        {
            if (string.IsNullOrEmpty(picPath))
            {
                return;
            }

            Excel.Worksheet eSheet = (Excel.Worksheet)eBook.Sheets[5];
            Excel.Range rng = eSheet.get_Range(startCell, Type.Missing);

            double width = eBook.Application.CentimetersToPoints(10.93);
            double height = eBook.Application.CentimetersToPoints(7.14);
            eSheet.Shapes.AddPicture(picPath,
                Microsoft.Office.Core.MsoTriState.msoFalse,
                Microsoft.Office.Core.MsoTriState.msoCTrue,
                (float)(double)rng.Left, (float)(double)rng.Top,
                (float)width, (float)height);
        }

        protected override void clear()
        {
            foreach (var acp in acceptorList)
            {
                acp.Clear();
            }
        }
    }
}

