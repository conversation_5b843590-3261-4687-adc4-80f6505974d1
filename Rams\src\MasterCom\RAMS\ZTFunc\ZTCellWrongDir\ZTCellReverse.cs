﻿using System;
using System.IO;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;
using MasterCom.RAMS.Func.SystemSetting;
using System.Text.RegularExpressions;
using System.Data;

namespace MasterCom.RAMS.ZTFunc
{
    //=======================================================================
    //This function doesn't need to replay file, 
    //it just need to read MC files and analysis cell handover by MC files.
    //=======================================================================
    public class ZTCellReverse : QueryBase
    {
        private static ZTCellReverse instance = null;
        public static ZTCellReverse GetInstance()
        {
            if (instance == null)
            {
                instance = new ZTCellReverse();
            }
            return instance;
        }

        public static string fileDir { get; set; } = string.Empty;
        public static string file { get; set; } = string.Empty;
        public static int wrongPercent { get; set; } = 60;
        private readonly char strSplit = '$';
        private Dictionary<string, List<string>> dicData { get; set; } = new Dictionary<string, List<string>>();
        private Dictionary<string, string> dicFileTime { get; set; } = new Dictionary<string, string>();
        private List<ReverseCellInfo> listReserseCellInfo { get; set; }
        private List<ReverseCellInfo> listResult { get; set; }

        public override bool IsNeedSetQueryCondition
        {
            get
            {
                return false;
            }
        }

        public override string Name
        {
            get { return "小区接反"; }
        }

        public override string IconName
        {
            get { return null; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 12000, 12097, this.Name);
        }

        protected override bool isValidCondition()
        {
            ZTCellReverseDlg condDlg = ZTCellReverseDlg.GetInstance();
            if (condDlg.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                return true;
            }
            return false;
        }

        protected override void query()
        {
            getDataFormFile();
            DataToCell();
            ShowResult();
        }

        private void ShowResult()
        {
            listResult = new List<ReverseCellInfo>();
            foreach (ReverseCellInfo info in listReserseCellInfo)
            {
                if (info.Status == 1 && info.WrongPercentage >= wrongPercent)
                    listResult.Add(info);
            }
            if (listResult.Count == 0)
            {
                DevExpress.XtraEditors.XtraMessageBox.Show("没有符合条件的数据！");
                return;
            }
            CellReverseShowForm frm = MainModel.CreateResultForm(typeof(CellReverseShowForm)) as CellReverseShowForm;
            frm.FillData(listResult);
            frm.Visible = true;
            frm.BringToFront();
        }

        /// <summary>
        ///  According to lac_ci and time of file, get the mainCell and the targetCell.
        /// </summary>
        private void DataToCell()
        {
            listReserseCellInfo = new List<ReverseCellInfo>();
            foreach (KeyValuePair<string, List<string>> item in dicData)
            {
                try
                {
                    string[] datas = item.Key.Split(strSplit);

                    DateTime fileTime = DateTime.Now.AddYears(-1);
                    if (dicFileTime.ContainsKey(item.Key))
                        fileTime = Convert.ToDateTime(dicFileTime[item.Key]);

                    Cell mCell = CellManager.GetInstance().GetNearestCell(fileTime, (ushort?)Convert.ToInt32(datas[0]), (ushort?)Convert.ToInt32(datas[1]),
                    null, null, 0, 0);

                    if (mCell == null)
                    {
                        mCell = CellManager.GetInstance().GetNearestCell(fileTime.AddYears(-20), (ushort?)Convert.ToInt32(datas[0]), (ushort?)Convert.ToInt32(datas[1]),
                        null, null, 0, 0);
                    }
                    if (mCell == null)
                    {
                        continue;
                    }

                    List<ReverseCell> rCells = getReverseCells(item, fileTime, mCell);

                    if (rCells.Count > 0)
                    {
                        listReserseCellInfo.Add(new ReverseCellInfo(mCell, rCells));
                    }
                }
                catch
                {
                    //continue
                }
            }
        }

        private List<ReverseCell> getReverseCells(KeyValuePair<string, List<string>> item, DateTime fileTime, Cell mCell)
        {
            List<ReverseCell> rCells = new List<ReverseCell>();
            foreach (string val in item.Value)
            {
                string[] vals = val.Split(strSplit);
                Cell cell = CellManager.GetInstance().GetNearestCell(fileTime, (ushort?)Convert.ToInt32(vals[0]), (ushort?)Convert.ToInt32(vals[1]),
                null, null, 0, 0);

                if (cell != null && cell.Type == BTSType.Outdoor
                    && cell.BandType == mCell.BandType && !mCell.BelongBTS.Cells.Contains(cell))
                {
                    rCells.Add(new ReverseCell(Convert.ToInt32(vals[2]), cell));
                }
            }

            return rCells;
        }

        private void getDataFormFile()
        {
            List<System.IO.FileInfo> listFile = new List<System.IO.FileInfo>();
            string erroFile = string.Empty;

            if (!string.IsNullOrEmpty(file))
            {
                listFile.Add(new System.IO.FileInfo(file));
            }
            else if (!string.IsNullOrEmpty(fileDir))
            {
                DirectoryInfo dirInfo = new DirectoryInfo(fileDir);
                System.IO.FileInfo[] fielInfos = dirInfo.GetFiles();

                foreach (System.IO.FileInfo info in fielInfos)
                {
                    listFile.Add(info);
                }
            }
            dicData.Clear();
            dicFileTime.Clear();

            try
            {
                foreach (System.IO.FileInfo item in listFile)
                {
                    erroFile = item.FullName;
                    if (item.Extension == ".xlsx")
                    {
                        readExcel(item);
                    }
                    else
                    {
                        readFile(item);
                    }
                }
            }
            catch (Exception ex)
            {
                DevExpress.XtraEditors.XtraMessageBox.Show("读取文件出错! " + erroFile);
                DevExpress.XtraEditors.XtraMessageBox.Show(ex.ToString());
                dicData.Clear();
            }

        }

        private void readExcel(System.IO.FileInfo item)
        {
            DataSet ds = ExcelNPOIManager.ImportFromExcel(item.FullName);

            if (ds != null && ds.Tables[0] != null)
            {
                DataTable dt = ds.Tables[0];
                if (dt.Columns.Count == 18)
                {
                    addColumnCount18Model(dt);
                }
                else if (dt.Columns.Count == 7)
                {
                    addColumnCount7Model(dt);
                }

                //GC.Collect();

                //ExcelNPOIReader reader = new ExcelNPOIReader(item.FullName);
                //ExcelNPOITable table = reader.GetTable("Sheet1");

                //if (table != null && table.CellValues != null && table.ColumnsIndex.Length == 18)
                //{
                //    table.CellValues.ForEach(t =>
                //    {
                //        if (t[3] != null && t[4] != null && t[6] != null && t[7] != null && t[8] != null)
                //        {
                //            string key = t[3].ToString() + strSplit + t[4];
                //            string val = t[6].ToString() + strSplit + t[7] + strSplit + t[8];

                //            if (!dicFileTime.ContainsKey(key) && t[1] != null)
                //            {
                //                string time = t[1].ToString().Trim();
                //                if (time.Length >= 8)
                //                {
                //                    time = time.Substring(0, 4) + "-"
                //                        + time.Substring(4, 2) + "-"
                //                        + time.Substring(6, 2);
                //                    dicFileTime.Add(key, time);
                //                }
                //            }
                //            if (dicData.ContainsKey(key))
                //            {
                //                dicData[key].Add(val);
                //            }
                //            else
                //            {
                //                dicData.Add(key, new List<string> { val });
                //            }
                //        }
                //    });
                //}
                //else if (table != null && table.CellValues != null && table.ColumnsIndex.Length == 7)
                //{
                //    table.CellValues.ForEach(t =>
                //    {
                //        if (t[0] != null && t[1] != null && t[2] != null)
                //        {
                //            string key = t[0].ToString().Trim().Replace("_", strSplit.ToString());
                //            string val = t[1].ToString().Trim().Replace("_", strSplit.ToString()) + strSplit + t[2].ToString().Trim();

                //            if (dicData.ContainsKey(key))
                //            {
                //                dicData[key].Add(val);
                //            }
                //            else
                //            {
                //                dicData.Add(key, new List<string> { val });
                //            }
                //        }
                //    });
                //}
                //reader = null;
                //table = null;
                //GC.Collect();
            }
        }

        private void addColumnCount18Model(DataTable dt)
        {
            for (int i = 0; i < dt.Rows.Count; i++)
            {
                if (!IsInt(dt.Rows[i][0].ToString()))
                    continue;

                string key = dt.Rows[i][3].ToString() + strSplit + dt.Rows[i][4];
                string val = dt.Rows[i][6].ToString() + strSplit + dt.Rows[i][7] + strSplit + dt.Rows[i][8];

                if (!dicFileTime.ContainsKey(key) && dt.Rows[i][1] != null)
                {
                    string time = dt.Rows[i][1].ToString().Trim();
                    if (time.Length >= 8)
                    {
                        time = time.Substring(0, 4) + "-"
                            + time.Substring(4, 2) + "-"
                            + time.Substring(6, 2);
                        dicFileTime.Add(key, time);
                    }
                }
                if (dicData.ContainsKey(key))
                {
                    dicData[key].Add(val);
                }
                else
                {
                    dicData.Add(key, new List<string> { val });
                }
            }
        }

        private void addColumnCount7Model(DataTable dt)
        {
            for (int i = 0; i < dt.Rows.Count; i++)
            {
                if (!IsInt(dt.Rows[i][0].ToString().Split('_')[0]))
                    continue;

                string key = dt.Rows[i][0].ToString().Trim().Replace("_", strSplit.ToString());
                string val = dt.Rows[i][1].ToString().Trim().Replace("_", strSplit.ToString()) + strSplit + dt.Rows[i][2].ToString().Trim();

                if (dicData.ContainsKey(key))
                {
                    dicData[key].Add(val);
                }
                else
                {
                    dicData.Add(key, new List<string> { val });
                }
            }
        }

        private void readFile(System.IO.FileInfo item)
        {
            FileStream fs = null;
            try
            {
                fs = new FileStream(item.FullName, FileMode.Open, FileAccess.Read);
                using (StreamReader sr = new StreamReader(fs, Encoding.GetEncoding("utf-8")))
                {
                    if (item.Extension == ".csv")
                    {
                        readCsvFile(sr);
                    }
                    else
                    {
                        readOtherFile(sr);
                    }
                    fs = null;
                }
            }
            finally
            {
                if (fs != null)
                {
                    fs.Dispose();
                }
            }
        }

        private void readCsvFile(StreamReader sr)
        {
            while (!sr.EndOfStream)
            {
                string[] datas = sr.ReadLine().Split(',');

                if (datas.Length == 28 && IsInt(datas[0]))
                {
                    string key = datas[12] + strSplit + datas[13];
                    string[] lacCi = datas[19].Split(':');
                    string val = lacCi[2] + strSplit + lacCi[3] + strSplit + datas[20];

                    if (!dicFileTime.ContainsKey(key))
                    {
                        dicFileTime.Add(key, datas[1].Split(' ')[0]);
                    }
                    if (dicData.ContainsKey(key))
                    {
                        dicData[key].Add(val);
                    }
                    else
                    {
                        dicData.Add(key, new List<string> { val });
                    }
                }
            }
        }

        private void readOtherFile(StreamReader sr)
        {
            string fileTime = string.Empty;
            while (!sr.EndOfStream)
            {
                string[] datas = sr.ReadLine().Split('\t');

                if (!string.IsNullOrEmpty(fileTime) && datas.Length == 10 && IsInt(datas[0]))
                {
                    //string key = datas[3] + strSplit + datas[2];
                    //string val = datas[1] + strSplit + datas[0] + strSplit + datas[7];

                    string key = datas[1] + strSplit + datas[0];
                    string val = datas[3] + strSplit + datas[2] + strSplit + datas[7];

                    if (!dicFileTime.ContainsKey(key))
                    {
                        dicFileTime.Add(key, fileTime);
                    }
                    if (dicData.ContainsKey(key))
                    {
                        dicData[key].Add(val);
                    }
                    else
                    {
                        dicData.Add(key, new List<string> { val });
                    }
                }
                else if (datas[0].Contains("begin date"))
                {
                    fileTime = datas[2];
                }
            }
        }

        private bool IsInt(string value)
        {
            return Regex.IsMatch(value, @"^[+-]?\d*$");
        }
    }

    public class ReverseCellInfo
    {
        public ReverseCellInfo(Cell mainCell, List<ReverseCell> cells)
        {
            this.MainCell = mainCell;
            this.bandType = mainCell.BandType;
            this.MCellDir = mainCell.Direction;
            getMCellCoverRange();
            anaRCells(cells);
        }

        private short MCellDir { get; set; }

        private BTSBandType bandType { get; set; }

        private int CoverRangeStart = 0;

        private int CoverRangeEnd = 0;

        private int CoverCellQuest = 0;

        private int UnCoverCellQuest = 0;

        public int Status { get { return this.status; } }

        public string CellName { get { return MainCell.Name; } }

        public int LAC { get { return MainCell.LAC; } }

        public int CI { get { return MainCell.CI; } }

        public double Longitude { get { return MainCell.Longitude; } }

        public double Latitude { get { return MainCell.Latitude; } }

        public double Direction { get { return MCellDir; } }

        public string CellCoveredRange { get { return CoverRangeStart.ToString() + " - " + CoverRangeEnd.ToString(); } }

        public int CoveredCellNum { get { return CoverCellQuest; } }

        public int UCoveredCellNum { get { return UnCoverCellQuest; } }

        public double WrongPercentage { get { return Math.Round((double)UnCoverCellQuest / (double)(CoverCellQuest + UnCoverCellQuest) * 100, 2); } }

        private int status { get; set; }

        private Cell MainCell { get; set; }

        private List<ReverseCell> CoverCells { get; set; }

        private List<ReverseCell> UnCoverCells { get; set; }

        private void anaRCells(List<ReverseCell> cells)
        {
            if (this.status == 1)
            {
                CoverCells = new List<ReverseCell>();
                UnCoverCells = new List<ReverseCell>();
                foreach (ReverseCell rCell in cells)
                {
                    if (isCovered(rCell.Cell))
                    {
                        CoverCells.Add(rCell);
                        CoverCellQuest += rCell.HOFrequency;
                    }
                    else
                    {
                        UnCoverCells.Add(rCell);
                        UnCoverCellQuest += rCell.HOFrequency;
                    }

                }
            }
        }

        private bool isCovered(Cell cell)
        {
            int angle = MathFuncs.getAngleFromPointToPoint
                        (MainCell.Longitude, MainCell.Latitude, cell.Longitude, cell.Latitude);

            if ((CoverRangeStart < angle && angle < CoverRangeEnd))
                return true;

            if (CoverRangeStart > CoverRangeEnd)
            {
                if (angle > CoverRangeEnd && angle > CoverRangeStart)
                    return true;
                if (angle < CoverRangeEnd)
                    return true;
            }

            return false;
        }

        private bool isCurDir(int val)
        {
            if (0 <= val && val <= 360)
                return true;
            else
                return false;
        }

        //============================================================================================
        //The mainCell CoverRange is the included angle
        //between the center lines between azimuth of mainCell and it's azimuth of adjacent cells 
        //============================================================================================
        private void getMCellCoverRange()
        {
            this.status = -1;
            List<Cell> BTScells = MainCell.BelongBTS.Cells;
            if (BTScells == null || BTScells.Count <= 1 || !isCurDir(MCellDir))
                return;

            List<Cell> cells = getCellList(BTScells);

            if (cells.Count == 1)
            {
                getOneCellCoverInfo(cells);
            }
            else if (cells.Count > 1)
            {
                getMultiCellCoverInfo(cells);
            }
        }

        private void getMultiCellCoverInfo(List<Cell> cells)
        {
            List<int> dirs = new List<int>();

            foreach (Cell cell in cells)
            {
                dirs.Add(cell.Direction);
            }

            if (dirs.Count < 2)
                return;

            int dirStart = 0;
            int dirEnd = 0;

            getDir(ref dirStart, ref dirEnd, dirs);
            if (dirStart == dirEnd)
                return;

            CoverRangeStart = calcRange(true, dirStart);
            CoverRangeEnd = calcRange(false, dirEnd);

            if (CoverRangeStart != CoverRangeEnd)
                this.status = 1;
        }

        private void getOneCellCoverInfo(List<Cell> cells)
        {
            foreach (Cell cell in cells)
            {
                int acuteAng = calcRange(cell.Direction, MCellDir, true);
                int ang = calcRange(cell.Direction, MCellDir, false);
                int Dval = MCellDir - acuteAng > 0 ? MCellDir - acuteAng : MCellDir - acuteAng + 360;

                if (Dval < 180)
                {
                    CoverRangeStart = acuteAng;
                    CoverRangeEnd = ang;
                }
                else
                {
                    CoverRangeStart = ang;
                    CoverRangeEnd = acuteAng;
                }

                if (CoverRangeStart != CoverRangeEnd)
                    this.status = 1;
            }
        }

        private List<Cell> getCellList(List<Cell> BTScells)
        {
            List<Cell> cells = new List<Cell>();
            List<int> listDir = new List<int>();
            foreach (Cell cell in BTScells)
            {
                if (isCurDir(cell.Direction)
                    && cell.Direction != MCellDir
                    && !listDir.Contains(cell.Direction))
                {
                    cells.Add(cell);
                    listDir.Add(cell.Direction);
                }
            }

            return cells;
        }

        private void getDir(ref int dirStart, ref int dirEnd, List<int> dirs)
        {
            int lastDvalStart = 0;
            int lastDvalEnd = 0;

            for (int i = 0; i < dirs.Count; i++)
            {
                if (i == 0)
                {
                    dirStart = dirs[i];
                    dirEnd = dirs[i];
                    lastDvalStart = MCellDir - dirs[i];
                    lastDvalEnd = dirs[i] - MCellDir;
                }
                else
                {
                    if (isNeedReplace(MCellDir - dirs[i], lastDvalStart))
                    {
                        dirStart = dirs[i];
                        lastDvalStart = MCellDir - dirs[i];
                    }
                    if (isNeedReplace(dirs[i] - MCellDir, lastDvalEnd))
                    {
                        dirEnd = dirs[i];
                        lastDvalStart = dirs[i] - MCellDir;
                    }
                }
            }
        }

        private bool isNeedReplace(int dval, int lastDval)
        {
            if ((dval > 0 && lastDval > 0 && dval < lastDval)
                || (dval < 0 && lastDval < 0 && dval < lastDval)
                || (dval > 0 && lastDval < 0))
                return true;

            return false;
        }

        private int calcRange(bool isStart, int dir)
        {
            if (MCellDir == dir)
                return 0;

            if (isStart)
            {
                if (MCellDir > dir)
                    return MCellDir - (MCellDir - dir) / 2;
                else
                {
                    int result = dir - (dir - MCellDir) / 2;
                    return result < 180 ? result + 180 : result - 180;
                }
            }
            else
            {
                if (dir > MCellDir)
                    return dir - (dir - MCellDir) / 2;
                else
                {
                    int result = MCellDir - (MCellDir - dir) / 2;
                    return result < 180 ? result + 180 : result - 180;
                }
            }

        }

        private int calcRange(short val1, short val2, bool isAcuteAngle)
        {
            if (val1 == val2)
                return 0;

            short big = val1 > val2 ? val1 : val2;
            short small = val1 < val2 ? val1 : val2;
            int result = big - (big - small) / 2;

            if (isAcuteAngle)
            {
                return result;
            }
            else
            {
                if (result < 180)
                {
                    return result + 180;
                }
                else
                {
                    return result - 180;
                }
            }
        }
    }

    public class ReverseCell
    {
        public ReverseCell(int frequency, Cell cell)
        {
            this.HOFrequency = frequency;
            this.Cell = cell;
        }

        public int HOFrequency { get; set; }

        public Cell Cell { get; set; }
    }
}
