﻿using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;
using System.Xml;

namespace MasterCom.RAMS.ZTFunc.ZTHighReverseFlowCoverage
{
    public class HighReverseFlowCoverageConfig : ConfigHelper<CoverageCondition>
    {
        private static HighReverseFlowCoverageConfig instance = null;
        public static HighReverseFlowCoverageConfig Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new HighReverseFlowCoverageConfig();
                }
                return instance;
            }
        }
        
        public override string ConfigPath { get; } = $@"{AppDomain.CurrentDomain.BaseDirectory}\config\HighReverseFlowCoverage\HighReverseFlowCoverage.xml";
        public override string LogPath { get; } = @"\BackGroundLog\HighReverseFlowCoverage\";
        public override string LogName { get; } = "-高倒流共覆盖.txt";

        protected override void loadConfig(XmlConfigFile xcfg, CoverageCondition configInfo)
        {
            try
            {
                XmlElement config = xcfg.GetConfig("Configs");
                configInfo.Distance = getValidData(xcfg, config, "Distance", 50);
                configInfo.DBCond.IP = getValidData(xcfg, config, "IP", "");
                configInfo.DBCond.DBName = getValidData(xcfg, config, "DBName", "");
                configInfo.DBCond.User = getValidData(xcfg, config, "User", "");
                string pw = getValidData(xcfg, config, "PW", "");
                configInfo.DBCond.PW = DES.Decode(pw);
            }
            catch (Exception ex)
            {
                ErrMsg = $"加载配置出错:{ex.Message}";
            }
        }

        public override void SaveConfig(CoverageCondition configInfo)
        {
            try
            {
                var newConfig = new XmlConfigFile();
                XmlElement cfg = newConfig.AddConfig("Configs");
                newConfig.AddItem(cfg, "Distance", configInfo.Distance);
                newConfig.AddItem(cfg, "IP", configInfo.DBCond.IP);
                newConfig.AddItem(cfg, "DBName", configInfo.DBCond.DBName);
                newConfig.AddItem(cfg, "User", configInfo.DBCond.User);
                newConfig.AddItem(cfg, "PW", DES.Encode(configInfo.DBCond.PW));
                newConfig.Save(ConfigPath);
            }
            catch (Exception ex)
            {
                ErrMsg = $"保存配置出错:{ex.Message}";
            }
        }
    }
}
