﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.Util;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NbIotMgrsConditionForm : BaseDialog
    {
        public NbIotMgrsConditionForm(List<NbIotMgrsFuncItem> funcItems)
        {
            InitializeComponent();

            listBox.DisplayMember = "ConditionTitle";
            foreach (NbIotMgrsFuncItem item in funcItems)
            {
                if (item.ConditionTitle == null)
                {
                    continue;
                }
                listBox.Items.Add(item);
            }
            listBox.Tag = funcItems;

            btnOK.Click += BtnOK_Click;
            btnCancel.Click += BtnCancel_Click;
            if (funcItems.Count > 0)
            {
                listBox.SelectedItem = funcItems[0];
                ListBox_SelectedChanged(listBox, new EventArgs());
            }
            listBox.SelectedIndexChanged += ListBox_SelectedChanged;
        }


        private void BtnOK_Click(object sender, EventArgs e)
        {
            List<NbIotMgrsFuncItem> funcItems = listBox.Tag as List<NbIotMgrsFuncItem>;
            XmlConfigFile configFile = new XmlConfigFile();
            foreach (NbIotMgrsFuncItem item in funcItems)
            {
                if (item.ConditionControl == null)
                {
                    continue;
                }

                string invalidReason = null;
                item.FuncCondtion = item.ConditionControl.GetCondition(out invalidReason);
                if (!string.IsNullOrEmpty(invalidReason))
                {
                    MessageBox.Show(string.Format("[{0}]: {1}", item.ConditionTitle, invalidReason), "设置错误", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    DialogResult = DialogResult.None;
                    return;
                }
                item.ConditionControl.SaveCondititon(configFile);
            }
            configFile.Save(NbIotMgrsBaseSettingManager.Instance.ConfigPath);
            if (ZTScanGridCoverageLayer.Ranges == null)
            {
                ZTScanGridCoverageLayer.Ranges = new NbIotMgrsCoverageColorRange();
            }
            ZTScanGridCoverageLayer.Ranges.SaveColorRange("Coverage");

            DialogResult = DialogResult.OK;
        }


        private void BtnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
        }

        private void ListBox_SelectedChanged(object sender, EventArgs e)
        {
            NbIotMgrsFuncItem funcItem = listBox.SelectedItem as NbIotMgrsFuncItem;
            if (panel.Controls.Count > 0 && panel.Controls[0] == funcItem.ConditionControl)
            {
                return;
            }
            panel.Controls.Clear();
            panel.Controls.Add(funcItem.ConditionControl);
            funcItem.ConditionControl.Dock = DockStyle.Fill;
        }
    }
}
