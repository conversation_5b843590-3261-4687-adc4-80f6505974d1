﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using DevExpress.XtraEditors;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using System.Windows.Forms;
using System.IO;
using MasterCom.MTGis;

namespace MasterCom.RAMS.CQT
{
    public partial class CQTPointListForm : MinCloseForm
    {
        public CQTPictureForm pictureForm { get; set; }
        MainModel mainModel;
        public CQTPointListForm(MainModel mm)
            : base(mm)
        {
            this.mainModel = mm;
            InitializeComponent();
            cqtPointPanel1.Visible = false;
        }
        public void FillData(List<CQTPoint> pnts)
        {
            gridControlCQTPoint.DataSource = pnts;
            gridControlCQTPoint.RefreshDataSource();
            MapForm mf = MainModel.MainForm.GetMapForm();
            if (mf != null)
            {
                MasterCom.MTGis.CustomDrawLayer cLayer = mf.GetCustomLayer(typeof(CQTPointMapLayer));
                if (cLayer == null)
                {
                    cLayer = new CQTPointMapLayer(mf.GetMapOperation(), "CQT");
                    mf.AddTempCustomLayer(cLayer);
                }
                CQTPointMapLayer layer = cLayer as CQTPointMapLayer;
                layer.CQTPoints2Show = CQTPointManager.GetInstance().CQTPoints;
                layer.Invalidate();
            }
        }

        private void gridView_DoubleClick(object sender, EventArgs e)
        {
            int[] rs = gridView.GetSelectedRows();
            if (rs.Length > 0)
            {
                MapForm mf = MainModel.MainForm.GetMapForm();
                if (mf != null)
                {
                    CQTPoint pnt = gridView.GetRow(rs[0]) as CQTPoint;
                    if (pnt!=null)
                    {
                        MainModel.SelCQTPoint = pnt;
                        mf.GoToView(pnt.LTLongitude, pnt.LTLatitude, 5000);
                    }
                }
            }
        }

        private void simpleButtonDelete_Click(object sender, EventArgs e)
        {
            int[] rs = gridView.GetSelectedRows();
            if (rs.Length > 0)
            {
                CQTPoint cqt = gridView.GetRow(rs[0]) as CQTPoint;
                DiySqlNonQuery queryDelete;
                string sql = "delete tb_cqt_point where pointID = " + cqt.ID;
                queryDelete = new DiySqlNonQuery(mainModel, sql);
                queryDelete.Query();
                sql = "delete tb_cqt_point_cells where pointID = " + cqt.ID;
                queryDelete = new DiySqlNonQuery(mainModel, sql);
                queryDelete.Query();
                sql = "delete tb_cqt_picture where pointID = " + cqt.ID;
                queryDelete = new DiySqlNonQuery(mainModel, sql);
                queryDelete.Query();

                CQTPointManager.GetInstance().CQTPoints.Remove(cqt);
                gridControlCQTPoint.RefreshDataSource();
            }
        }

        private void simpleButtonUpdate_Click(object sender, EventArgs e)
        {
            int[] rs = gridView.GetSelectedRows();
            if (rs.Length > 0)
            {
                cqtPointPanel1.Visible = true;
                CQTPoint cqtOld = gridView.GetRow(rs[0]) as CQTPoint;
                cqtPointPanel1.Fill(cqtOld, PointOperator.Update, this);                
            }
        }

        private void simpleButtonInsert_Click(object sender, EventArgs e)
        {
            int[] rs = gridView.GetSelectedRows();
            if (rs.Length > 0)
            {
                cqtPointPanel1.Visible = true;
                CQTPoint cqtOld = gridView.GetRow(rs[0]) as CQTPoint;
                cqtPointPanel1.Fill(cqtOld, PointOperator.Insert, this);
            }
        }

        private void gridView_FocusedRowChanged(object sender, DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventArgs e)
        {
            if (gridView.GetSelectedRows().Length > 0)
            {
                simpleButtonDelete.Enabled = true;
                simpleButtonUpdate.Enabled = true;
                simpleButtonPictMng.Enabled = true;
            }
            else
            {
                simpleButtonDelete.Enabled = false;
                simpleButtonUpdate.Enabled = false;
                simpleButtonPictMng.Enabled = false;
            }
        }

        private void simpleButtonPictMng_Click(object sender, EventArgs e)
        {
            if (gridView.GetFocusedRow() != null && gridView.GetFocusedRow() is CQTPoint)
            {
                CQTPoint pnt = gridView.GetFocusedRow() as CQTPoint;
                object obj = mainModel.GetObjectFromBlackboard(typeof(CQTPictureForm).FullName);
                pictureForm = obj == null ? null : obj as CQTPictureForm;
                if (pictureForm == null || pictureForm.IsDisposed)
                {
                    pictureForm = new CQTPictureForm(MainModel);
                }
                pictureForm.setPoint(pnt);
                pictureForm.Show();
            }
        }

        private void importMTCQTPicture(DirectoryInfo cFolder, CQTPoint cqtPoint)
        {
            System.IO.FileInfo[] cFileInfo = cFolder.GetFiles();
            foreach (System.IO.FileInfo file in cFileInfo)
            {
                CQTPointPicture picture = buildPicture(file.FullName, file.Name, cqtPoint);
                if (picture == null) continue;
                DIYUpdateCQTPicture insertDIY = new DIYUpdateCQTPicture(mainModel, SqlOperator.Insert, picture, null, cqtPoint);
                insertDIY.Query();
                cqtPoint.Pictures.Add(picture);
            }
        }

        private void importAtuCQTPicture(DirectoryInfo cFolder, CQTPoint cqtPoint)
        {
            System.IO.FileInfo[] cFileInfo = cFolder.GetFiles();
            foreach (System.IO.FileInfo fileJPG in cFileInfo)
            {
                if (fileJPG.Extension.ToLower().Equals(".jpg"))
                {
                    addPicture(cqtPoint, cFileInfo, fileJPG);
                }
            }
        }

        private void addPicture(CQTPoint cqtPoint, System.IO.FileInfo[] cFileInfo, System.IO.FileInfo fileJPG)
        {
            foreach (System.IO.FileInfo fileFP in cFileInfo)
            {
                if (fileFP.Extension.ToLower().Equals(".fp") &&
                    fileFP.Name.Remove(fileFP.Name.LastIndexOf('.')).Equals(fileJPG.Name.Remove(fileJPG.Name.LastIndexOf('.'))))
                {
                    CQTPointPicture picture = BuildAtuPicture(fileJPG, fileFP, cqtPoint);
                    if (picture == null) continue;
                    DIYUpdateCQTPicture insertDIY = new DIYUpdateCQTPicture(mainModel, SqlOperator.Insert, picture, null, cqtPoint);
                    insertDIY.Query();
                    cqtPoint.Pictures.Add(picture);
                    break;
                }
            }
        }

        private void simpleButtonImportAll_Click(object sender, EventArgs e)
        {
            FolderBrowserDialog dlg = new FolderBrowserDialog();
            dlg.SelectedPath = Application.StartupPath;
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return;
            }
            DirectoryInfo rootFolder = new DirectoryInfo(dlg.SelectedPath);
            DirectoryInfo[] cDirInfo = rootFolder.GetDirectories();
            foreach (DirectoryInfo cFolder in cDirInfo)
            {
                foreach (CQTPoint cqtPoint in CQTPointManager.GetInstance().CQTPoints)
                {
                    if (cqtPoint.Name.Equals(cFolder.Name))
                    {
                        switch (cqtPoint.otherType1)
                        {
                            case (int)CQTPointBeLong.MTPoint:
                                importMTCQTPicture(cFolder, cqtPoint);
                                break;
                            case (int)CQTPointBeLong.AtuPoint:
                                importAtuCQTPicture(cFolder, cqtPoint);
                                break;
                            default:
                                break;
                        }
                        break;
                    }
                }
            }
            MessageBox.Show("导入完成");
        }

        public static CQTPointPicture BuildAtuPicture(System.IO.FileInfo fileJPG, System.IO.FileInfo fileFP, CQTPoint cqtPoint)
        {
            DbPoint ltPos = new DbPoint();
            DbPoint brPos = new DbPoint();
            float ratio = 0;
            Image image = Image.FromFile(fileJPG.FullName);
            StreamReader fpRead = new StreamReader(fileFP.FullName);
            GetMapAddrInfo(fpRead, image, ref ltPos, ref brPos, ref ratio);
            if (ratio <= 0) return null;

            string[] parts = fileJPG.Name.Split(new char[] { '#' }, StringSplitOptions.RemoveEmptyEntries);
            if (parts.Length != 6) return null;
            parts[parts.Length - 1] = parts[parts.Length - 1].Remove(parts[parts.Length - 1].LastIndexOf('.'));
            if (!parts[3].Equals("FD") && !parts[3].Equals("FU")) return null;
            CQTPointPicture cqtPicture = new CQTPointPicture();
            cqtPicture.BelongPoint = cqtPoint;
            cqtPicture.pointID = cqtPoint.ID;
            cqtPicture.pictureID = cqtPicture.FindNextID();
            cqtPicture.bottomFloor = parts[3].Equals("FD") ? 0 - Convert.ToInt32(parts[5]) : Convert.ToInt32(parts[4]);
            cqtPicture.topFloor = parts[3].Equals("FU") ? Convert.ToInt32(parts[5]) : 0 - Convert.ToInt32(parts[4]);
            cqtPicture.tlLongitude = ltPos.x;
            cqtPicture.tlLatitude = ltPos.y;
            cqtPicture.brLongitude = brPos.x;
            cqtPicture.brLatitude = brPos.y;
            cqtPicture.altitude = cqtPoint.Altitude;
            cqtPicture.pictureLength = (int)(ratio * image.Height);
            cqtPicture.pictureWidth = (int)(ratio * image.Width);
            cqtPicture.pictureName = fileJPG.Name.Remove(fileJPG.Name.LastIndexOf('.'));
            cqtPicture.aliasName = cqtPicture.pictureName;
            cqtPicture.pictureDesc = cqtPicture.pictureName;
            cqtPicture.pictureImage = image;
            cqtPicture.ratio = ratio;
            return cqtPicture;
        }

        public static void GetMapAddrInfo(StreamReader reader, Image image, ref DbPoint ltPos, ref DbPoint brPos, ref float ratio)
        {
            try
            {
                int iLoop = 0;
                MapAddrInfo info = new MapAddrInfo();
                while (true)
                {
                    iLoop++;
                    if (iLoop > 100)
                    {
                        break;
                    }
                    string sLine = reader.ReadLine();
                    if (!string.IsNullOrEmpty(sLine))
                    {
                        bool isEnd = getValidDbPoint(ref ltPos, info, sLine);
                        if (isEnd)
                        {
                            break;
                        }
                    }
                }

                if (info.scaleLong != 0 && info.scaleLat != 0)
                {
                    double brLongtide = ltPos.x + image.Width * info.scaleLong;
                    double brLatitude = ltPos.y - image.Height * info.scaleLat;
                    brPos = new DbPoint(brLongtide, brLatitude);
                    ratio = (float)MainModel.GetInstance().MainForm.GetMapForm().GetCQTImgScaleFromLTBR(ltPos, brPos, image);
                }
            }
            catch
            {
                //continue
            }
            finally
            {
                reader.Close();
            }
        }

        private static bool getValidDbPoint(ref DbPoint ltPos, MapAddrInfo info, string sLine)
        {
            string[] strings = sLine.Split(new string[1] { "\t" }, StringSplitOptions.RemoveEmptyEntries);
            if (strings.Length > 5)
            {
                if (strings[0].Equals("kp") || strings[0].Equals("ap"))
                {
                    if (info.scale == 0)
                    {
                        return true;
                    }
                    if (int.TryParse(strings[2], out info.x)
                        && int.TryParse(strings[3], out info.y)
                        && double.TryParse(strings[4], out info.longitude)
                        && double.TryParse(strings[5], out info.latitude)
                        && info.x >= 0 && info.y >= 0 && info.longitude >= 0 && info.latitude >= 0)
                    {
                        double ltLongtide = info.longitude - info.x * info.scaleLong;
                        double ltLatitude = info.latitude + info.y * info.scaleLat;
                        ltPos = new DbPoint(ltLongtide, ltLatitude);
                        return true;
                    }
                }
            }
            else if (strings.Length > 2 && strings[0].Equals("is"))
            {
                bool isvlaid = int.TryParse(strings[2], out info.scale);
                if (isvlaid)    //读取纬度方向距离/像素比，单位为毫米
                {
                    info.scaleLatDis = 1.0 * info.scale / 1000;   //转换成米
                    info.scaleLongDis = info.scaleLatDis * 0.9;    //经度方向距离/像素比
                    info.scaleLong = info.scaleLongDis * 0.00001; //经度/像素比
                    info.scaleLat = info.scaleLatDis * 0.000009;  //纬度/像素比
                }
            }

            return false;
        }

        private class MapAddrInfo
        {
            public int scale = 0;
            public double scaleLongDis = 0, scaleLatDis = 0;   //距离像素比
            public double scaleLong = 0, scaleLat = 0; //经纬度像素比
            public int x, y;
            public double longitude, latitude;
        }


        private CQTPointPicture buildPicture(string fullName, string fileName, CQTPoint cqtPoint)
        {
            string[] nameApart = fileName.Split(new char[] { '@' }, StringSplitOptions.RemoveEmptyEntries);
            if (nameApart.Length != 2) return null;
            string[] parts = nameApart[1].Split(new char[] { '#' }, StringSplitOptions.RemoveEmptyEntries);
            if (parts.Length != 7) return null;
            parts[parts.Length - 1] = parts[parts.Length - 1].Remove(parts[parts.Length - 1].LastIndexOf('.'));
            if (!parts[4].Equals("FD") && !parts[4].Equals("FU")) return null;
            Image img = Image.FromFile(fullName);
            CQTPointPicture cqtPicture = new CQTPointPicture();
            cqtPicture.BelongPoint = cqtPoint;
            cqtPicture.pointID = cqtPoint.ID;
            cqtPicture.pictureID = cqtPicture.FindNextID();
            cqtPicture.bottomFloor = parts[4].Equals("FD") ? 0 - Convert.ToInt32(parts[6]) :Convert.ToInt32(parts[5]);
            cqtPicture.topFloor = parts[4].Equals("FU") ? Convert.ToInt32(parts[6]) : 0 - Convert.ToInt32(parts[5]);
            cqtPicture.tlLongitude = cqtPoint.LTLongitude;
            cqtPicture.tlLatitude = cqtPoint.LTLatitude;
            cqtPicture.brLongitude = cqtPoint.BRLongitude;
            cqtPicture.brLatitude = cqtPoint.BRLatitude;
            cqtPicture.altitude = cqtPoint.Altitude;
            cqtPicture.pictureLength = img.Height;
            cqtPicture.pictureWidth = img.Width;
            cqtPicture.pictureName = fileName;
            cqtPicture.aliasName = parts[3];
            cqtPicture.pictureDesc = parts[0] + "#" + parts[1] + "#" + parts[2];
            cqtPicture.pictureImage = img;
            return cqtPicture;
        }
    }
}