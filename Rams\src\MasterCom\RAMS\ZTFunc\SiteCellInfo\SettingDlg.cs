﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc.SiteCellInfo
{
    public partial class SettingDlg : BaseDialog
    {
        public SettingDlg()
        {
            InitializeComponent();
            this.btnBrowse.ButtonClick += btnBrowse_ButtonClick;
        }

        void btnBrowse_ButtonClick(object sender, DevExpress.XtraEditors.Controls.ButtonPressedEventArgs e)
        {
            OpenFileDialog dlg = new OpenFileDialog();
            dlg.RestoreDirectory = true;
            dlg.Multiselect = false;
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                this.btnBrowse.Text = dlg.FileName;
            }
        }

        public string FileName
        {
            get { return this.btnBrowse.Text; }
            set { this.btnBrowse.Text=value; }
        }

        public double CellDistance
        {
            get { return (double)numCellDistance.Value; }
            set { numCellDistance.Value = (decimal)value; }
        }

        public double SiteDistance
        {
            get { return (double)numSiteDistance.Value; }
            set { numSiteDistance.Value = (decimal)value; }
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(this.btnBrowse.Text))
            {
                MessageBox.Show("请指定地点文件！");
                return;
            }
            if (!System.IO.File.Exists(this.FileName))
            {
                MessageBox.Show("指定地点文件不存在！");
                return;
            }
            DialogResult = DialogResult.OK;
        }
    }
}
