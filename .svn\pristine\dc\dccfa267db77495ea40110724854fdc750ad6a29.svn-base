﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;


using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Stat;
using DBDataViewer;
using MasterCom.RAMS.Stat.Data;
using MasterCom.RAMS.Model.Interface;

namespace MasterCom.RAMS.Net
{
    public class DIYStatByAllRegion : DIYStatQuery
    {
        public DIYStatByAllRegion(MainModel mainModel)
            : base(mainModel)
        {
        }
        public override string Name
        {
            get { return "KPI统计(全区域)"; }
        }
        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 11000, 11018, this.Name);
        }
        protected override StatTbToken getBranchToken()
        {
            return StatTbToken.log;
        }
        protected override void prepareStatPackage_ImgGrid_FileFilter(Package package, TimePeriod period,byte carrierID,bool byRound)
        {
            package.Command = Command.DIYSearch;
            package.SubCommand = SubCommand.Request;
            package.Content.Type = RequestType.REQTYPE_DIY_LOG_KPI;
            package.Content.PrepareAddParam();
            if(byRound)
            {
                AddDIYRound(package, condition.ByRoundYear, condition.ByRoundRound);
            }
            else
            {
                AddDIYPeriod(package, period);
            }
            AddDIYProject(package, condition.Projects);
            AddDIYService(package, condition.ServiceTypes);
            if (!condition.IsAllAgent)
            {
                AddDIYAgent(package, condition.AgentIds);
            }
            AddDIYCarrierType(package, carrierID);
            AddDIYFileFilter(package, condition);

            AddDIYStatStatus(package);
            AddDIYAreaTypeAndID(package, condition.Areas);
            AddDIYMomt(package, condition.Momt);
            //
            AddDIYEndOpFlag(package);
            
        }
        protected override void prepareStatPackage_Event_FileFilter(Package package, TimePeriod period, byte carrierID,bool byRound)
        {
            package.Command = Command.DIYSearch;
            package.SubCommand = SubCommand.Request;
            package.Content.Type = RequestType.REQTYPE_DIY_AREA_EVENT;
            package.Content.PrepareAddParam();
            if (byRound)
            {
                AddDIYRound(package, condition.ByRoundYear, condition.ByRoundRound);
            }
            else
            {
                AddDIYPeriod(package, period);
            }
            AddDIYProject(package, condition.Projects);
            AddDIYService(package, condition.ServiceTypes);
            if (!condition.IsAllAgent)
            {
                AddDIYAgent(package, condition.AgentIds);
            }
            AddDIYCarrierType(package, carrierID);
            AddDIYFileFilter(package, condition);
            AddDIYStatStatus(package);
            AddDIYAreaTypeAndID(package, condition.Areas);
            AddDIYMomt(package, condition.Momt);
            //
            AddDIYEndOpFlag(package);
        }
        protected override void prepareStatPackage_Event_EventFilter(Package package, TimePeriod period)
        {
       //     AddDIYPeriod_Between(package, period); 因为可能要按轮查询，所以不加时间过滤
            AddDIYEndOpFlag(package);
        }

        protected override void query()
        {
            loadReportFromFile();
            SelectReportDlg dlg = new SelectReportDlg();
            dlg.FillCurrentReports(ref rptStyleList);
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return;
            }
            bool curSelStyleContentAllTmp;
            curSelStyle = dlg.GetSelectedReport(out curSelStyleContentAllTmp);
            curSelStyleContentAll = curSelStyleContentAllTmp;
            curEventStatFilter = dlg.GetEventStatFilter();
            bool momtFlag = dlg.GetMomtFlag();
            //==
            MainModel.CurChinaMobileStatReportData = null;
            MainModel.CurChinaUnicomStatReportData = null;
            MainModel.CurChinaTelecomStatReportData = null;
            MainModel.CurChinaMobileStatReportDataList.Clear();
            MainModel.CurChinaUnicomStatReportDataList.Clear();
            MainModel.CurChinaTelecomStatReportDataList.Clear();

            MainModel.CurChinaMobileStatReportDataMo = null;
            MainModel.CurChinaUnicomStatReportDataMo = null;
            MainModel.CurChinaTelecomStatReportDataMo = null;
            MainModel.CurChinaMobileStatReportDataListMo.Clear();
            MainModel.CurChinaUnicomStatReportDataListMo.Clear();
            MainModel.CurChinaTelecomStatReportDataListMo.Clear();

            MainModel.CurChinaMobileStatReportDataMt = null;
            MainModel.CurChinaUnicomStatReportDataMt = null;
            MainModel.CurChinaTelecomStatReportDataMt = null;
            MainModel.CurChinaMobileStatReportDataListMt.Clear();
            MainModel.CurChinaUnicomStatReportDataListMt.Clear();
            MainModel.CurChinaTelecomStatReportDataListMt.Clear();

            cmDataUnitAreaKPIQueryDic = null;
            cuDataUnitAreaKPIQueryDic = null;
            ctDataUnitAreaKPIQueryDic = null;

            cmDataUnitAreaKPIQueryDicMo = null;
            cuDataUnitAreaKPIQueryDicMo = null;
            ctDataUnitAreaKPIQueryDicMo = null;

            cmDataUnitAreaKPIQueryDicMt = null;
            cuDataUnitAreaKPIQueryDicMt = null;
            ctDataUnitAreaKPIQueryDicMt = null;
         
            WaitBox.CanCancel = true;
            WaitBox.Text = "正在查询...";
            multiGeometrys = false;
            stopQuery = false;
            regionDic.Clear();
            if (Condition.MultiTime)
            {
                foreach (TimePeriod period in Condition.Periods)
                {
                    regionDic[period.GetShortString()] = null;
                }
            }
            if (Condition.CarrierTypes.Contains(1))
            {
                queryByCarrier(momtFlag, queryChinaMobileInThread);
            }
            if (Condition.CarrierTypes.Contains(2))
            {
                queryByCarrier(momtFlag, queryChinaUnicomInThread);
            }
            if (Condition.CarrierTypes.Contains(3))
            {
                queryByCarrier(momtFlag, queryChinaTelecomInThread);
            }
            if (Condition.MultiTime)
            {
                addModelData();
            }
            MainModel.FireMultiRegionStatQueried(this, curSelStyle, regionDic, false);
        }

        private void queryByCarrier(bool momtFlag, CallBackMethodWithParams queryInThread)
        {
            if (!WaitBox.CancelRequest)
            {
                Condition.Momt = 0;
                queryByCarrier(queryInThread);

                if (momtFlag)
                {
                    Condition.Momt = 1;
                    queryByCarrier(queryInThread);
                    Condition.Momt = 2;
                    queryByCarrier(queryInThread);
                }
            }
        }

        private void addModelData()
        {
            addAreaKPI();

            addAreaKPIMo();

            addAreaKPIMt();
        }

        private void addAreaKPI()
        {
            if (cmDataUnitAreaKPIQueryDic != null)
            {
                foreach (DataUnitAreaKPIQuery data in cmDataUnitAreaKPIQueryDic.Values)
                {
                    MainModel.CurChinaMobileStatReportDataList.Add(new StatReportData(data));
                }
            }
            if (cuDataUnitAreaKPIQueryDic != null)
            {
                foreach (DataUnitAreaKPIQuery data in cuDataUnitAreaKPIQueryDic.Values)
                {
                    MainModel.CurChinaUnicomStatReportDataList.Add(new StatReportData(data));
                }
            }
            if (ctDataUnitAreaKPIQueryDic != null)
            {
                foreach (DataUnitAreaKPIQuery data in ctDataUnitAreaKPIQueryDic.Values)
                {
                    MainModel.CurChinaTelecomStatReportDataList.Add(new StatReportData(data));
                }
            }
        }

        private void addAreaKPIMo()
        {
            if (cmDataUnitAreaKPIQueryDicMo != null)
            {
                foreach (DataUnitAreaKPIQuery data in cmDataUnitAreaKPIQueryDicMo.Values)
                {
                    MainModel.CurChinaMobileStatReportDataListMo.Add(new StatReportData(data));
                }
            }

            if (cuDataUnitAreaKPIQueryDicMo != null)
            {
                foreach (DataUnitAreaKPIQuery data in cuDataUnitAreaKPIQueryDicMo.Values)
                {
                    MainModel.CurChinaUnicomStatReportDataListMo.Add(new StatReportData(data));
                }
            }

            if (ctDataUnitAreaKPIQueryDicMo != null)
            {
                foreach (DataUnitAreaKPIQuery data in ctDataUnitAreaKPIQueryDicMo.Values)
                {
                    MainModel.CurChinaTelecomStatReportDataListMo.Add(new StatReportData(data));
                }
            }
        }

        private void addAreaKPIMt()
        {
            if (cmDataUnitAreaKPIQueryDicMt != null)
            {
                foreach (DataUnitAreaKPIQuery data in cmDataUnitAreaKPIQueryDicMt.Values)
                {
                    MainModel.CurChinaMobileStatReportDataListMt.Add(new StatReportData(data));
                }
            }
            if (cuDataUnitAreaKPIQueryDicMt != null)
            {
                foreach (DataUnitAreaKPIQuery data in cuDataUnitAreaKPIQueryDicMt.Values)
                {
                    MainModel.CurChinaUnicomStatReportDataListMt.Add(new StatReportData(data));
                }
            }
            if (ctDataUnitAreaKPIQueryDicMt != null)
            {
                foreach (DataUnitAreaKPIQuery data in ctDataUnitAreaKPIQueryDicMt.Values)
                {
                    MainModel.CurChinaTelecomStatReportDataListMt.Add(new StatReportData(data));
                }
            }
        }

        protected override void recieveInfo_ImgGrid(ClientProxy clientProxy, List<DataUnitAreaKPIQuery> paraRetList, int carrierId, TimePeriod period)
        {
            DataUnitAreaKPIQuery retResult = new DataUnitAreaKPIQuery();
            DTDataHeaderManager.GetInstance();
            List<StatImgDefItem> curImgColumnDef = new List<StatImgDefItem>();
            int index = 0;
            Package package = clientProxy.Package;
            int progress = 0;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                #region Read Stat Data
                if (isFileHeaderContentType(package.Content.Type))
                {
                    //
                }
                else if (package.Content.Type == ResponseType.COLUMN_DEFINE)
                {
                    curImgColumnDef.Clear();
                    string idpairs = package.Content.GetParamString();
                    parseToCurImgColumnDef(idpairs, curImgColumnDef);
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIY_LOG_KPI_LTE_AMR)
                {
                    DataLTE newImg = new DataLTE();
                    foreach (StatImgDefItem cdf in curImgColumnDef)
                    {
                        byte[] imgBytes = package.Content.GetParamBytes();
                        Dictionary<String, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgBytes);
                        foreach (string str in cellStatInfoDic.Keys)
                        {
                            newImg.WInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                        }
                    }
                    retResult.addStatData(newImg);
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIY_AREA_COVER_GRID_SCAN_LTE_TOPN
                      || package.Content.Type == ResponseType.RESTYPE_DIY_LOG_KPI_LTE_FREQSPECTRUM)
                {
                    DataScan_LTE newImg = new DataScan_LTE();
                    foreach (StatImgDefItem cdf in curImgColumnDef)
                    {
                        byte[] imgBytes = package.Content.GetParamBytes();
                        Dictionary<string, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgBytes);
                        foreach (string str in cellStatInfoDic.Keys)
                        {
                            newImg.wInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                        }
                    }
                    if (Condition.MultiTime)
                    {
                        DataUnitAreaKPIQuery data = new DataUnitAreaKPIQuery();
                        data.addStatData(newImg);
                        saveDataByRegion(0, 0, data, carrierId, period);
                    }
                    else
                    {
                        retResult.addStatData(newImg);
                    }
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIY_AREA_COVER_GRID_SCAN_NR
                      || package.Content.Type == ResponseType.RESTYPE_DIY_LOG_KPI_NR_FREQSPECTRUM)
                {
                    DataScan_NR newImg = new DataScan_NR();
                    foreach (StatImgDefItem cdf in curImgColumnDef)
                    {
                        byte[] imgBytes = package.Content.GetParamBytes();
                        Dictionary<string, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgBytes);
                        foreach (string str in cellStatInfoDic.Keys)
                        {
                            newImg.wInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                        }
                    }
                    if (Condition.MultiTime)
                    {
                        DataUnitAreaKPIQuery data = new DataUnitAreaKPIQuery();
                        data.addStatData(newImg);
                        saveDataByRegion(0, 0, data, carrierId, period);
                    }
                    else
                    {
                        retResult.addStatData(newImg);
                    }
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIY_LOG_KPI_GSM
                || package.Content.Type == ResponseType.RESTYPE_DIY_LOG_KPI_GPRS)
                {

                    DataGSM_NewImg newImg = new DataGSM_NewImg();
                    foreach (StatImgDefItem cdf in curImgColumnDef)
                    {
                        byte[] imgBytes = package.Content.GetParamBytes();
                        Dictionary<string, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgBytes);
                        foreach (string str in cellStatInfoDic.Keys)
                        {
                            newImg.wInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                        }
                    }
                    if (Condition.MultiTime)
                    {
                        DataUnitAreaKPIQuery data = new DataUnitAreaKPIQuery();
                        data.addStatData(newImg);
                        saveDataByRegion(0, 0, data, carrierId, period);
                    }
                    else
                    {
                        retResult.addStatData(newImg);
                    }
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIY_LOG_KPI_TDSCDMA_AMR
               || package.Content.Type == ResponseType.RESTYPE_DIY_LOG_KPI_TDSCDMA_PS
               || package.Content.Type == ResponseType.RESTYPE_DIY_LOG_KPI_TDSCDMA_VP)
                {
                    DataTDSCDMA_NewImg newImg = new DataTDSCDMA_NewImg();
                    foreach (StatImgDefItem cdf in curImgColumnDef)
                    {
                        byte[] imgBytes = package.Content.GetParamBytes();
                        Dictionary<string, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgBytes);
                        foreach (string str in cellStatInfoDic.Keys)
                        {
                            newImg.wInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                        }
                    }
                    if (Condition.MultiTime)
                    {
                        DataUnitAreaKPIQuery data = new DataUnitAreaKPIQuery();
                        data.addStatData(newImg);
                        saveDataByRegion(0, 0, data, carrierId, period);
                    }
                    else
                    {
                        retResult.addStatData(newImg);
                    }
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIY_LOG_KPI_WCDMA_AMR
                   || package.Content.Type == ResponseType.RESTYPE_DIY_LOG_KPI_WCDMA_PS
                   || package.Content.Type == ResponseType.RESTYPE_DIY_LOG_KPI_WCDMA_VP
                   || package.Content.Type == ResponseType.RESTYPE_DIY_LOG_KPI_WCDMA_PSHS)
                {
                    DataWCDMA_AMR newImg = new DataWCDMA_AMR();
                    foreach (StatImgDefItem cdf in curImgColumnDef)
                    {
                        byte[] imgBytes = package.Content.GetParamBytes();
                        Dictionary<string, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgBytes);
                        foreach (string str in cellStatInfoDic.Keys)
                        {
                            newImg.wInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                        }
                    }
                    if (Condition.MultiTime)
                    {
                        DataUnitAreaKPIQuery data = new DataUnitAreaKPIQuery();
                        data.addStatData(newImg);
                        saveDataByRegion(0, 0, data, carrierId, period);
                    }
                    else
                    {
                        retResult.addStatData(newImg);
                    }
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIY_LOG_KPI_SCAN_TD)
                {
                    DataScan_TD newImg = new DataScan_TD();
                    foreach (StatImgDefItem cdf in curImgColumnDef)
                    {
                        byte[] imgBytes = package.Content.GetParamBytes();
                        Dictionary<string, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgBytes);
                        foreach (string str in cellStatInfoDic.Keys)
                        {
                            newImg.wInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                        }
                    }
                    if (Condition.MultiTime)
                    {
                        DataUnitAreaKPIQuery data = new DataUnitAreaKPIQuery();
                        data.addStatData(newImg);
                        saveDataByRegion(0, 0, data, carrierId, period);
                    }
                    else
                    {
                        retResult.addStatData(newImg);
                    }
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIY_LOG_KPI_CDMA_V
               || package.Content.Type == ResponseType.RESTYPE_DIY_LOG_KPI_CDMA_D)
                {
                    DataCDMA_Voice newImg = new DataCDMA_Voice();
                    foreach (StatImgDefItem cdf in curImgColumnDef)
                    {
                        byte[] imgBytes = package.Content.GetParamBytes();
                        Dictionary<string, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgBytes);
                        foreach (string str in cellStatInfoDic.Keys)
                        {
                            newImg.wInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                        }
                    }
                    if (Condition.MultiTime)
                    {
                        DataUnitAreaKPIQuery data = new DataUnitAreaKPIQuery();
                        data.addStatData(newImg);
                        saveDataByRegion(0, 0, data, carrierId, period);
                    }
                    else
                    {
                        retResult.addStatData(newImg);
                    }
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIY_LOG_KPI_CDMA2000_D)
                {
                    DataEVDO_Data newImg = new DataEVDO_Data();
                    foreach (StatImgDefItem cdf in curImgColumnDef)
                    {
                        byte[] imgBytes = package.Content.GetParamBytes();
                        Dictionary<string, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgBytes);
                        foreach (string str in cellStatInfoDic.Keys)
                        {
                            newImg.wInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                        }
                    }
                    if (Condition.MultiTime)
                    {
                        DataUnitAreaKPIQuery data = new DataUnitAreaKPIQuery();
                        data.addStatData(newImg);
                        saveDataByRegion(0, 0, data, carrierId, period);
                    }
                    else
                    {
                        retResult.addStatData(newImg);
                    }
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIY_LOG_KPI_GSM_MTR)
                {
                    DataMTR_GSM newImg = new DataMTR_GSM();
                    foreach (StatImgDefItem cdf in curImgColumnDef)
                    {
                        byte[] imgBytes = package.Content.GetParamBytes();
                        Dictionary<string, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgBytes);
                        foreach (string str in cellStatInfoDic.Keys)
                        {
                            newImg.wInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                        }
                    }
                    if (Condition.MultiTime)
                    {
                        DataUnitAreaKPIQuery data = new DataUnitAreaKPIQuery();
                        data.addStatData(newImg);
                        saveDataByRegion(0, 0, data, carrierId, period);
                    }
                    else
                    {
                        retResult.addStatData(newImg);
                    }
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                #endregion

                if (Math.Log(index++) * (100000 / 10000) > WaitBox.ProgressPercent)
                {
                    progress++;
                    if (progress > 95)
                    {
                        progress = 5;
                        index = 0;
                    }
                    if (progress % 5 == 0)
                    {
                        WaitBox.ProgressPercent = progress;
                    }
                }
            }
            paraRetList.Add(retResult);
        }
        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

    }
}
