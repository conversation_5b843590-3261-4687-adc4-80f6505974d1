﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ScanOverlapCellForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            DevExpress.XtraGrid.GridLevelNode gridLevelNode1 = new DevExpress.XtraGrid.GridLevelNode();
            this.gridViewSub = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gcSubCellName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcSubCellCI = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcSubTestPointCount = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repositoryItemMemoEdit2 = new DevExpress.XtraEditors.Repository.RepositoryItemMemoEdit();
            this.gcSubDistanceAvg = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcSubRxLevAvg = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcOvlCellDistanceAvg = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcOvlCellRxLevAvg = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcSubDistanceDiffAvg = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcSubRxLevDiffAvg = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridControl = new DevExpress.XtraGrid.GridControl();
            this.contextMenuStrip1 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExp2Xls = new System.Windows.Forms.ToolStripMenuItem();
            this.gridView = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gcOverlapCellName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcOverlapCellCI = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcOvlPntCnt = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcDistanceAvg = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcRxLevAvg = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repositoryItemMemoEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemMemoEdit();
            this.splitContainerControl1 = new DevExpress.XtraEditors.SplitContainerControl();
            this.btnShowAll = new DevExpress.XtraEditors.SimpleButton();
            this.checkEditShowNearestLine = new DevExpress.XtraEditors.CheckEdit();
            this.checkEditShowOvrlLine = new DevExpress.XtraEditors.CheckEdit();
            this.gridControlPnt = new DevExpress.XtraGrid.GridControl();
            this.gridViewPnt = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn19 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn20 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn21 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn37 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn22 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn24 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn25 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn26 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn27 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn23 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repositoryItemMemoEdit5 = new DevExpress.XtraEditors.Repository.RepositoryItemMemoEdit();
            this.repositoryItemMemoEdit6 = new DevExpress.XtraEditors.Repository.RepositoryItemMemoEdit();
            this.gridView4 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn28 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn29 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn30 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn31 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn32 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn33 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn34 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn35 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn36 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridControlNew = new DevExpress.XtraGrid.GridControl();
            this.gridView2 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn10 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn11 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn13 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn14 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn15 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn16 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn17 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn18 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn12 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repositoryItemMemoEdit4 = new DevExpress.XtraEditors.Repository.RepositoryItemMemoEdit();
            this.repositoryItemMemoEdit3 = new DevExpress.XtraEditors.Repository.RepositoryItemMemoEdit();
            this.gridView1 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn6 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn7 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn8 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn9 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.btnGIS = new DevExpress.XtraEditors.SimpleButton();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewSub)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemMemoEdit2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl)).BeginInit();
            this.contextMenuStrip1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridView)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemMemoEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).BeginInit();
            this.splitContainerControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.checkEditShowNearestLine.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.checkEditShowOvrlLine.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlPnt)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewPnt)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemMemoEdit5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemMemoEdit6)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlNew)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemMemoEdit4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemMemoEdit3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).BeginInit();
            this.SuspendLayout();
            // 
            // gridViewSub
            // 
            this.gridViewSub.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gcSubCellName,
            this.gcSubCellCI,
            this.gcSubTestPointCount,
            this.gcSubDistanceAvg,
            this.gcSubRxLevAvg,
            this.gcOvlCellDistanceAvg,
            this.gcOvlCellRxLevAvg,
            this.gcSubDistanceDiffAvg,
            this.gcSubRxLevDiffAvg});
            this.gridViewSub.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.gridViewSub.GridControl = this.gridControl;
            this.gridViewSub.Name = "gridViewSub";
            this.gridViewSub.OptionsBehavior.AllowAddRows = DevExpress.Utils.DefaultBoolean.False;
            this.gridViewSub.OptionsBehavior.AllowDeleteRows = DevExpress.Utils.DefaultBoolean.False;
            this.gridViewSub.OptionsBehavior.Editable = false;
            this.gridViewSub.OptionsDetail.ShowDetailTabs = false;
            this.gridViewSub.OptionsView.ShowDetailButtons = false;
            this.gridViewSub.ShowButtonMode = DevExpress.XtraGrid.Views.Base.ShowButtonModeEnum.ShowForFocusedRow;
            this.gridViewSub.DoubleClick += new System.EventHandler(this.gridViewSub_DoubleClick);
            // 
            // gcSubCellName
            // 
            this.gcSubCellName.Caption = "最近小区名";
            this.gcSubCellName.FieldName = "CellName";
            this.gcSubCellName.Name = "gcSubCellName";
            this.gcSubCellName.Visible = true;
            this.gcSubCellName.VisibleIndex = 0;
            this.gcSubCellName.Width = 232;
            // 
            // gcSubCellCI
            // 
            this.gcSubCellCI.Caption = "最近小区CI";
            this.gcSubCellCI.FieldName = "CI";
            this.gcSubCellCI.Name = "gcSubCellCI";
            this.gcSubCellCI.Visible = true;
            this.gcSubCellCI.VisibleIndex = 1;
            // 
            // gcSubTestPointCount
            // 
            this.gcSubTestPointCount.Caption = "采样点个数";
            this.gcSubTestPointCount.ColumnEdit = this.repositoryItemMemoEdit2;
            this.gcSubTestPointCount.FieldName = "TestPointCount";
            this.gcSubTestPointCount.Name = "gcSubTestPointCount";
            this.gcSubTestPointCount.Visible = true;
            this.gcSubTestPointCount.VisibleIndex = 2;
            this.gcSubTestPointCount.Width = 76;
            // 
            // repositoryItemMemoEdit2
            // 
            this.repositoryItemMemoEdit2.Name = "repositoryItemMemoEdit2";
            // 
            // gcSubDistanceAvg
            // 
            this.gcSubDistanceAvg.Caption = "距离(m)";
            this.gcSubDistanceAvg.FieldName = "DistanceAvg";
            this.gcSubDistanceAvg.Name = "gcSubDistanceAvg";
            this.gcSubDistanceAvg.Visible = true;
            this.gcSubDistanceAvg.VisibleIndex = 3;
            this.gcSubDistanceAvg.Width = 134;
            // 
            // gcSubRxLevAvg
            // 
            this.gcSubRxLevAvg.Caption = "RxLev(dBm)";
            this.gcSubRxLevAvg.FieldName = "RxLevAvg";
            this.gcSubRxLevAvg.Name = "gcSubRxLevAvg";
            this.gcSubRxLevAvg.Visible = true;
            this.gcSubRxLevAvg.VisibleIndex = 5;
            this.gcSubRxLevAvg.Width = 98;
            // 
            // gcOvlCellDistanceAvg
            // 
            this.gcOvlCellDistanceAvg.Caption = "最强信号距离(m)";
            this.gcOvlCellDistanceAvg.FieldName = "OverlapCellDistanceAvg";
            this.gcOvlCellDistanceAvg.Name = "gcOvlCellDistanceAvg";
            this.gcOvlCellDistanceAvg.Visible = true;
            this.gcOvlCellDistanceAvg.VisibleIndex = 6;
            this.gcOvlCellDistanceAvg.Width = 145;
            // 
            // gcOvlCellRxLevAvg
            // 
            this.gcOvlCellRxLevAvg.Caption = "最强信号小区RxLev(dBm)";
            this.gcOvlCellRxLevAvg.FieldName = "OverlapCellRxLevAvg";
            this.gcOvlCellRxLevAvg.Name = "gcOvlCellRxLevAvg";
            this.gcOvlCellRxLevAvg.Visible = true;
            this.gcOvlCellRxLevAvg.VisibleIndex = 4;
            this.gcOvlCellRxLevAvg.Width = 157;
            // 
            // gcSubDistanceDiffAvg
            // 
            this.gcSubDistanceDiffAvg.Caption = "平均距离差(m)";
            this.gcSubDistanceDiffAvg.FieldName = "DistanceDiffAvg";
            this.gcSubDistanceDiffAvg.Name = "gcSubDistanceDiffAvg";
            this.gcSubDistanceDiffAvg.Visible = true;
            this.gcSubDistanceDiffAvg.VisibleIndex = 7;
            this.gcSubDistanceDiffAvg.Width = 112;
            // 
            // gcSubRxLevDiffAvg
            // 
            this.gcSubRxLevDiffAvg.Caption = "平均电平差(dB)";
            this.gcSubRxLevDiffAvg.FieldName = "RxLevDiffAvg";
            this.gcSubRxLevDiffAvg.Name = "gcSubRxLevDiffAvg";
            this.gcSubRxLevDiffAvg.Visible = true;
            this.gcSubRxLevDiffAvg.VisibleIndex = 8;
            this.gcSubRxLevDiffAvg.Width = 127;
            // 
            // gridControl
            // 
            this.gridControl.ContextMenuStrip = this.contextMenuStrip1;
            this.gridControl.Dock = System.Windows.Forms.DockStyle.Fill;
            gridLevelNode1.LevelTemplate = this.gridViewSub;
            gridLevelNode1.RelationName = "SubCells";
            this.gridControl.LevelTree.Nodes.AddRange(new DevExpress.XtraGrid.GridLevelNode[] {
            gridLevelNode1});
            this.gridControl.Location = new System.Drawing.Point(0, 0);
            this.gridControl.MainView = this.gridView;
            this.gridControl.Name = "gridControl";
            this.gridControl.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repositoryItemMemoEdit1,
            this.repositoryItemMemoEdit2});
            this.gridControl.Size = new System.Drawing.Size(1112, 338);
            this.gridControl.TabIndex = 3;
            this.gridControl.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView,
            this.gridViewSub});
            this.gridControl.Visible = false;
            // 
            // contextMenuStrip1
            // 
            this.contextMenuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExp2Xls});
            this.contextMenuStrip1.Name = "contextMenuStrip1";
            this.contextMenuStrip1.Size = new System.Drawing.Size(139, 26);
            // 
            // miExp2Xls
            // 
            this.miExp2Xls.Name = "miExp2Xls";
            this.miExp2Xls.Size = new System.Drawing.Size(138, 22);
            this.miExp2Xls.Text = "导出Excel...";
            this.miExp2Xls.Click += new System.EventHandler(this.miExp2Xls_Click);
            // 
            // gridView
            // 
            this.gridView.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gcOverlapCellName,
            this.gcOverlapCellCI,
            this.gcOvlPntCnt,
            this.gcDistanceAvg,
            this.gcRxLevAvg});
            this.gridView.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.gridView.GridControl = this.gridControl;
            this.gridView.Name = "gridView";
            this.gridView.OptionsBehavior.AllowAddRows = DevExpress.Utils.DefaultBoolean.False;
            this.gridView.OptionsBehavior.AllowDeleteRows = DevExpress.Utils.DefaultBoolean.False;
            this.gridView.OptionsBehavior.Editable = false;
            this.gridView.OptionsDetail.ShowDetailTabs = false;
            this.gridView.OptionsPrint.PrintDetails = true;
            this.gridView.OptionsView.RowAutoHeight = true;
            this.gridView.DoubleClick += new System.EventHandler(this.gridView_DoubleClick);
            // 
            // gcOverlapCellName
            // 
            this.gcOverlapCellName.Caption = "最强小区";
            this.gcOverlapCellName.FieldName = "CellName";
            this.gcOverlapCellName.Name = "gcOverlapCellName";
            this.gcOverlapCellName.Visible = true;
            this.gcOverlapCellName.VisibleIndex = 0;
            this.gcOverlapCellName.Width = 281;
            // 
            // gcOverlapCellCI
            // 
            this.gcOverlapCellCI.Caption = "最强小区CI";
            this.gcOverlapCellCI.FieldName = "CI";
            this.gcOverlapCellCI.Name = "gcOverlapCellCI";
            this.gcOverlapCellCI.Visible = true;
            this.gcOverlapCellCI.VisibleIndex = 4;
            // 
            // gcOvlPntCnt
            // 
            this.gcOvlPntCnt.Caption = "过覆盖总采样点个数";
            this.gcOvlPntCnt.FieldName = "TestPointCount";
            this.gcOvlPntCnt.Name = "gcOvlPntCnt";
            this.gcOvlPntCnt.Visible = true;
            this.gcOvlPntCnt.VisibleIndex = 1;
            this.gcOvlPntCnt.Width = 171;
            // 
            // gcDistanceAvg
            // 
            this.gcDistanceAvg.Caption = "与采样点的平均距离(m)";
            this.gcDistanceAvg.FieldName = "DistanceAvg";
            this.gcDistanceAvg.Name = "gcDistanceAvg";
            this.gcDistanceAvg.Visible = true;
            this.gcDistanceAvg.VisibleIndex = 2;
            this.gcDistanceAvg.Width = 181;
            // 
            // gcRxLevAvg
            // 
            this.gcRxLevAvg.Caption = "平均信号强度(dBm)";
            this.gcRxLevAvg.FieldName = "RxLevAvg";
            this.gcRxLevAvg.Name = "gcRxLevAvg";
            this.gcRxLevAvg.Visible = true;
            this.gcRxLevAvg.VisibleIndex = 3;
            this.gcRxLevAvg.Width = 190;
            // 
            // repositoryItemMemoEdit1
            // 
            this.repositoryItemMemoEdit1.Name = "repositoryItemMemoEdit1";
            // 
            // splitContainerControl1
            // 
            this.splitContainerControl1.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Default;
            this.splitContainerControl1.CollapsePanel = DevExpress.XtraEditors.SplitCollapsePanel.Panel1;
            this.splitContainerControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl1.Horizontal = false;
            this.splitContainerControl1.Location = new System.Drawing.Point(0, 23);
            this.splitContainerControl1.LookAndFeel.SkinName = "Office 2010 Blue";
            this.splitContainerControl1.Name = "splitContainerControl1";
            this.splitContainerControl1.Panel1.Controls.Add(this.btnShowAll);
            this.splitContainerControl1.Panel1.Controls.Add(this.checkEditShowNearestLine);
            this.splitContainerControl1.Panel1.Controls.Add(this.checkEditShowOvrlLine);
            this.splitContainerControl1.Panel1.Text = "PanelGIS";
            this.splitContainerControl1.Panel2.Controls.Add(this.gridControlPnt);
            this.splitContainerControl1.Panel2.Controls.Add(this.gridControlNew);
            this.splitContainerControl1.Panel2.Controls.Add(this.gridControl);
            this.splitContainerControl1.Panel2.Text = "Panel2";
            this.splitContainerControl1.Size = new System.Drawing.Size(1116, 378);
            this.splitContainerControl1.SplitterPosition = 30;
            this.splitContainerControl1.TabIndex = 4;
            this.splitContainerControl1.Text = "splitContainerControl1";
            // 
            // btnShowAll
            // 
            this.btnShowAll.Location = new System.Drawing.Point(360, 4);
            this.btnShowAll.Name = "btnShowAll";
            this.btnShowAll.Size = new System.Drawing.Size(116, 23);
            this.btnShowAll.TabIndex = 1;
            this.btnShowAll.Text = "显示全部过覆盖点";
            this.btnShowAll.Click += new System.EventHandler(this.btnShowAll_Click);
            // 
            // checkEditShowNearestLine
            // 
            this.checkEditShowNearestLine.Location = new System.Drawing.Point(186, 6);
            this.checkEditShowNearestLine.Name = "checkEditShowNearestLine";
            this.checkEditShowNearestLine.Properties.Caption = "显示最近小区与采样点拉线";
            this.checkEditShowNearestLine.Size = new System.Drawing.Size(168, 19);
            this.checkEditShowNearestLine.TabIndex = 0;
            this.checkEditShowNearestLine.CheckedChanged += new System.EventHandler(this.checkEditShowNearestLine_CheckedChanged);
            // 
            // checkEditShowOvrlLine
            // 
            this.checkEditShowOvrlLine.Location = new System.Drawing.Point(12, 6);
            this.checkEditShowOvrlLine.Name = "checkEditShowOvrlLine";
            this.checkEditShowOvrlLine.Properties.Caption = "显示最强小区与采样点拉线";
            this.checkEditShowOvrlLine.Size = new System.Drawing.Size(168, 19);
            this.checkEditShowOvrlLine.TabIndex = 0;
            this.checkEditShowOvrlLine.CheckedChanged += new System.EventHandler(this.checkEditShowOvrlLine_CheckedChanged);
            // 
            // gridControlPnt
            // 
            this.gridControlPnt.ContextMenuStrip = this.contextMenuStrip1;
            this.gridControlPnt.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlPnt.Location = new System.Drawing.Point(0, 0);
            this.gridControlPnt.MainView = this.gridViewPnt;
            this.gridControlPnt.Name = "gridControlPnt";
            this.gridControlPnt.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repositoryItemMemoEdit5,
            this.repositoryItemMemoEdit6});
            this.gridControlPnt.Size = new System.Drawing.Size(1112, 338);
            this.gridControlPnt.TabIndex = 5;
            this.gridControlPnt.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridViewPnt,
            this.gridView4});
            // 
            // gridViewPnt
            // 
            this.gridViewPnt.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn19,
            this.gridColumn20,
            this.gridColumn21,
            this.gridColumn37,
            this.gridColumn22,
            this.gridColumn24,
            this.gridColumn25,
            this.gridColumn26,
            this.gridColumn27,
            this.gridColumn23});
            this.gridViewPnt.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.gridViewPnt.GridControl = this.gridControlPnt;
            this.gridViewPnt.Name = "gridViewPnt";
            this.gridViewPnt.OptionsBehavior.AllowAddRows = DevExpress.Utils.DefaultBoolean.False;
            this.gridViewPnt.OptionsBehavior.AllowDeleteRows = DevExpress.Utils.DefaultBoolean.False;
            this.gridViewPnt.OptionsBehavior.Editable = false;
            this.gridViewPnt.OptionsDetail.ShowDetailTabs = false;
            this.gridViewPnt.OptionsView.RowAutoHeight = true;
            this.gridViewPnt.OptionsView.ShowDetailButtons = false;
            this.gridViewPnt.DoubleClick += new System.EventHandler(this.gridViewPnt_DoubleClick);
            // 
            // gridColumn19
            // 
            this.gridColumn19.Caption = "经度";
            this.gridColumn19.FieldName = "Longitude";
            this.gridColumn19.Name = "gridColumn19";
            this.gridColumn19.Visible = true;
            this.gridColumn19.VisibleIndex = 0;
            this.gridColumn19.Width = 108;
            // 
            // gridColumn20
            // 
            this.gridColumn20.Caption = "纬度";
            this.gridColumn20.FieldName = "Latitude";
            this.gridColumn20.Name = "gridColumn20";
            this.gridColumn20.Visible = true;
            this.gridColumn20.VisibleIndex = 1;
            this.gridColumn20.Width = 101;
            // 
            // gridColumn21
            // 
            this.gridColumn21.Caption = "最近小区CI";
            this.gridColumn21.FieldName = "CINearest";
            this.gridColumn21.Name = "gridColumn21";
            this.gridColumn21.Visible = true;
            this.gridColumn21.VisibleIndex = 2;
            this.gridColumn21.Width = 80;
            // 
            // gridColumn37
            // 
            this.gridColumn37.Caption = "最近小区距离(m)";
            this.gridColumn37.FieldName = "DistanceNearest";
            this.gridColumn37.Name = "gridColumn37";
            this.gridColumn37.Visible = true;
            this.gridColumn37.VisibleIndex = 4;
            this.gridColumn37.Width = 111;
            // 
            // gridColumn22
            // 
            this.gridColumn22.Caption = "最近小区场强";
            this.gridColumn22.FieldName = "RxLevNearest";
            this.gridColumn22.Name = "gridColumn22";
            this.gridColumn22.Visible = true;
            this.gridColumn22.VisibleIndex = 3;
            this.gridColumn22.Width = 100;
            // 
            // gridColumn24
            // 
            this.gridColumn24.Caption = "最强信号小区CI";
            this.gridColumn24.FieldName = "CIMaxRxLev";
            this.gridColumn24.Name = "gridColumn24";
            this.gridColumn24.Visible = true;
            this.gridColumn24.VisibleIndex = 5;
            this.gridColumn24.Width = 102;
            // 
            // gridColumn25
            // 
            this.gridColumn25.Caption = "最强信号小区距离(m)";
            this.gridColumn25.FieldName = "DistanceMaxRxLev";
            this.gridColumn25.Name = "gridColumn25";
            this.gridColumn25.Visible = true;
            this.gridColumn25.VisibleIndex = 6;
            this.gridColumn25.Width = 138;
            // 
            // gridColumn26
            // 
            this.gridColumn26.Caption = "最强信号小区场强";
            this.gridColumn26.FieldName = "RxLevMax";
            this.gridColumn26.Name = "gridColumn26";
            this.gridColumn26.Visible = true;
            this.gridColumn26.VisibleIndex = 7;
            this.gridColumn26.Width = 136;
            // 
            // gridColumn27
            // 
            this.gridColumn27.Caption = "距离差";
            this.gridColumn27.FieldName = "DistanceDiff";
            this.gridColumn27.Name = "gridColumn27";
            this.gridColumn27.Visible = true;
            this.gridColumn27.VisibleIndex = 8;
            this.gridColumn27.Width = 111;
            // 
            // gridColumn23
            // 
            this.gridColumn23.Caption = "电平差";
            this.gridColumn23.FieldName = "RxLevDiff";
            this.gridColumn23.Name = "gridColumn23";
            this.gridColumn23.Visible = true;
            this.gridColumn23.VisibleIndex = 9;
            this.gridColumn23.Width = 104;
            // 
            // repositoryItemMemoEdit5
            // 
            this.repositoryItemMemoEdit5.Name = "repositoryItemMemoEdit5";
            // 
            // repositoryItemMemoEdit6
            // 
            this.repositoryItemMemoEdit6.Name = "repositoryItemMemoEdit6";
            // 
            // gridView4
            // 
            this.gridView4.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn28,
            this.gridColumn29,
            this.gridColumn30,
            this.gridColumn31,
            this.gridColumn32,
            this.gridColumn33,
            this.gridColumn34,
            this.gridColumn35,
            this.gridColumn36});
            this.gridView4.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.gridView4.GridControl = this.gridControlPnt;
            this.gridView4.Name = "gridView4";
            this.gridView4.OptionsBehavior.AllowAddRows = DevExpress.Utils.DefaultBoolean.False;
            this.gridView4.OptionsBehavior.AllowDeleteRows = DevExpress.Utils.DefaultBoolean.False;
            this.gridView4.OptionsBehavior.Editable = false;
            this.gridView4.OptionsDetail.ShowDetailTabs = false;
            this.gridView4.OptionsView.ShowDetailButtons = false;
            this.gridView4.ShowButtonMode = DevExpress.XtraGrid.Views.Base.ShowButtonModeEnum.ShowForFocusedRow;
            // 
            // gridColumn28
            // 
            this.gridColumn28.Caption = "最近小区名";
            this.gridColumn28.FieldName = "CellName";
            this.gridColumn28.Name = "gridColumn28";
            this.gridColumn28.Visible = true;
            this.gridColumn28.VisibleIndex = 0;
            this.gridColumn28.Width = 232;
            // 
            // gridColumn29
            // 
            this.gridColumn29.Caption = "最近小区CI";
            this.gridColumn29.FieldName = "CI";
            this.gridColumn29.Name = "gridColumn29";
            this.gridColumn29.Visible = true;
            this.gridColumn29.VisibleIndex = 1;
            // 
            // gridColumn30
            // 
            this.gridColumn30.Caption = "采样点个数";
            this.gridColumn30.ColumnEdit = this.repositoryItemMemoEdit6;
            this.gridColumn30.FieldName = "TestPointCount";
            this.gridColumn30.Name = "gridColumn30";
            this.gridColumn30.Visible = true;
            this.gridColumn30.VisibleIndex = 2;
            this.gridColumn30.Width = 76;
            // 
            // gridColumn31
            // 
            this.gridColumn31.Caption = "距离(m)";
            this.gridColumn31.FieldName = "DistanceAvg";
            this.gridColumn31.Name = "gridColumn31";
            this.gridColumn31.Visible = true;
            this.gridColumn31.VisibleIndex = 3;
            this.gridColumn31.Width = 134;
            // 
            // gridColumn32
            // 
            this.gridColumn32.Caption = "RxLev(dBm)";
            this.gridColumn32.FieldName = "RxLevAvg";
            this.gridColumn32.Name = "gridColumn32";
            this.gridColumn32.Visible = true;
            this.gridColumn32.VisibleIndex = 5;
            this.gridColumn32.Width = 98;
            // 
            // gridColumn33
            // 
            this.gridColumn33.Caption = "最强信号距离(m)";
            this.gridColumn33.FieldName = "OverlapCellDistanceAvg";
            this.gridColumn33.Name = "gridColumn33";
            this.gridColumn33.Visible = true;
            this.gridColumn33.VisibleIndex = 6;
            this.gridColumn33.Width = 145;
            // 
            // gridColumn34
            // 
            this.gridColumn34.Caption = "最强信号小区RxLev(dBm)";
            this.gridColumn34.FieldName = "OverlapCellRxLevAvg";
            this.gridColumn34.Name = "gridColumn34";
            this.gridColumn34.Visible = true;
            this.gridColumn34.VisibleIndex = 4;
            this.gridColumn34.Width = 157;
            // 
            // gridColumn35
            // 
            this.gridColumn35.Caption = "平均距离差(m)";
            this.gridColumn35.FieldName = "DistanceDiffAvg";
            this.gridColumn35.Name = "gridColumn35";
            this.gridColumn35.Visible = true;
            this.gridColumn35.VisibleIndex = 7;
            this.gridColumn35.Width = 112;
            // 
            // gridColumn36
            // 
            this.gridColumn36.Caption = "平均电平差(dB)";
            this.gridColumn36.FieldName = "RxLevDiffAvg";
            this.gridColumn36.Name = "gridColumn36";
            this.gridColumn36.Visible = true;
            this.gridColumn36.VisibleIndex = 8;
            this.gridColumn36.Width = 127;
            // 
            // gridControlNew
            // 
            this.gridControlNew.ContextMenuStrip = this.contextMenuStrip1;
            this.gridControlNew.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlNew.Location = new System.Drawing.Point(0, 0);
            this.gridControlNew.MainView = this.gridView2;
            this.gridControlNew.Name = "gridControlNew";
            this.gridControlNew.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repositoryItemMemoEdit4,
            this.repositoryItemMemoEdit3});
            this.gridControlNew.Size = new System.Drawing.Size(1112, 338);
            this.gridControlNew.TabIndex = 4;
            this.gridControlNew.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView2,
            this.gridView1});
            // 
            // gridView2
            // 
            this.gridView2.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn10,
            this.gridColumn11,
            this.gridColumn13,
            this.gridColumn14,
            this.gridColumn15,
            this.gridColumn16,
            this.gridColumn17,
            this.gridColumn18,
            this.gridColumn12});
            this.gridView2.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.gridView2.GridControl = this.gridControlNew;
            this.gridView2.Name = "gridView2";
            this.gridView2.OptionsBehavior.AllowAddRows = DevExpress.Utils.DefaultBoolean.False;
            this.gridView2.OptionsBehavior.AllowDeleteRows = DevExpress.Utils.DefaultBoolean.False;
            this.gridView2.OptionsBehavior.Editable = false;
            this.gridView2.OptionsDetail.ShowDetailTabs = false;
            this.gridView2.OptionsView.RowAutoHeight = true;
            this.gridView2.OptionsView.ShowDetailButtons = false;
            this.gridView2.DoubleClick += new System.EventHandler(this.gridViewSub_DoubleClick);
            // 
            // gridColumn10
            // 
            this.gridColumn10.Caption = "最近小区名";
            this.gridColumn10.FieldName = "CellName";
            this.gridColumn10.Name = "gridColumn10";
            this.gridColumn10.Visible = true;
            this.gridColumn10.VisibleIndex = 0;
            this.gridColumn10.Width = 171;
            // 
            // gridColumn11
            // 
            this.gridColumn11.Caption = "最近小区CI";
            this.gridColumn11.FieldName = "CI";
            this.gridColumn11.Name = "gridColumn11";
            this.gridColumn11.Visible = true;
            this.gridColumn11.VisibleIndex = 1;
            this.gridColumn11.Width = 74;
            // 
            // gridColumn13
            // 
            this.gridColumn13.Caption = "最近小区距离(m)";
            this.gridColumn13.FieldName = "DistanceAvg";
            this.gridColumn13.Name = "gridColumn13";
            this.gridColumn13.Visible = true;
            this.gridColumn13.VisibleIndex = 2;
            this.gridColumn13.Width = 107;
            // 
            // gridColumn14
            // 
            this.gridColumn14.Caption = "最近小区RxLev";
            this.gridColumn14.FieldName = "RxLevAvg";
            this.gridColumn14.Name = "gridColumn14";
            this.gridColumn14.Visible = true;
            this.gridColumn14.VisibleIndex = 3;
            this.gridColumn14.Width = 93;
            // 
            // gridColumn15
            // 
            this.gridColumn15.Caption = "最强信号小区名";
            this.gridColumn15.FieldName = "OverlapCellName";
            this.gridColumn15.Name = "gridColumn15";
            this.gridColumn15.Visible = true;
            this.gridColumn15.VisibleIndex = 4;
            this.gridColumn15.Width = 161;
            // 
            // gridColumn16
            // 
            this.gridColumn16.Caption = "最强信号小区CI";
            this.gridColumn16.FieldName = "MaxCellCI";
            this.gridColumn16.Name = "gridColumn16";
            this.gridColumn16.Visible = true;
            this.gridColumn16.VisibleIndex = 5;
            this.gridColumn16.Width = 107;
            // 
            // gridColumn17
            // 
            this.gridColumn17.Caption = "最强信号小区距离(m)";
            this.gridColumn17.FieldName = "OverlapCellDistanceAvg";
            this.gridColumn17.Name = "gridColumn17";
            this.gridColumn17.Visible = true;
            this.gridColumn17.VisibleIndex = 6;
            this.gridColumn17.Width = 129;
            // 
            // gridColumn18
            // 
            this.gridColumn18.Caption = "最强信号小区RxLev";
            this.gridColumn18.FieldName = "OverlapCellRxLevAvg";
            this.gridColumn18.Name = "gridColumn18";
            this.gridColumn18.Visible = true;
            this.gridColumn18.VisibleIndex = 7;
            this.gridColumn18.Width = 124;
            // 
            // gridColumn12
            // 
            this.gridColumn12.Caption = "过覆盖采样点个数";
            this.gridColumn12.FieldName = "TestPointCount";
            this.gridColumn12.Name = "gridColumn12";
            this.gridColumn12.Visible = true;
            this.gridColumn12.VisibleIndex = 8;
            this.gridColumn12.Width = 125;
            // 
            // repositoryItemMemoEdit4
            // 
            this.repositoryItemMemoEdit4.Name = "repositoryItemMemoEdit4";
            // 
            // repositoryItemMemoEdit3
            // 
            this.repositoryItemMemoEdit3.Name = "repositoryItemMemoEdit3";
            // 
            // gridView1
            // 
            this.gridView1.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn1,
            this.gridColumn2,
            this.gridColumn3,
            this.gridColumn4,
            this.gridColumn5,
            this.gridColumn6,
            this.gridColumn7,
            this.gridColumn8,
            this.gridColumn9});
            this.gridView1.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.gridView1.GridControl = this.gridControlNew;
            this.gridView1.Name = "gridView1";
            this.gridView1.OptionsBehavior.AllowAddRows = DevExpress.Utils.DefaultBoolean.False;
            this.gridView1.OptionsBehavior.AllowDeleteRows = DevExpress.Utils.DefaultBoolean.False;
            this.gridView1.OptionsBehavior.Editable = false;
            this.gridView1.OptionsDetail.ShowDetailTabs = false;
            this.gridView1.OptionsView.ShowDetailButtons = false;
            this.gridView1.ShowButtonMode = DevExpress.XtraGrid.Views.Base.ShowButtonModeEnum.ShowForFocusedRow;
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "最近小区名";
            this.gridColumn1.FieldName = "CellName";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.Visible = true;
            this.gridColumn1.VisibleIndex = 0;
            this.gridColumn1.Width = 232;
            // 
            // gridColumn2
            // 
            this.gridColumn2.Caption = "最近小区CI";
            this.gridColumn2.FieldName = "CI";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.Visible = true;
            this.gridColumn2.VisibleIndex = 1;
            // 
            // gridColumn3
            // 
            this.gridColumn3.Caption = "采样点个数";
            this.gridColumn3.ColumnEdit = this.repositoryItemMemoEdit3;
            this.gridColumn3.FieldName = "TestPointCount";
            this.gridColumn3.Name = "gridColumn3";
            this.gridColumn3.Visible = true;
            this.gridColumn3.VisibleIndex = 2;
            this.gridColumn3.Width = 76;
            // 
            // gridColumn4
            // 
            this.gridColumn4.Caption = "距离(m)";
            this.gridColumn4.FieldName = "DistanceAvg";
            this.gridColumn4.Name = "gridColumn4";
            this.gridColumn4.Visible = true;
            this.gridColumn4.VisibleIndex = 3;
            this.gridColumn4.Width = 134;
            // 
            // gridColumn5
            // 
            this.gridColumn5.Caption = "RxLev(dBm)";
            this.gridColumn5.FieldName = "RxLevAvg";
            this.gridColumn5.Name = "gridColumn5";
            this.gridColumn5.Visible = true;
            this.gridColumn5.VisibleIndex = 5;
            this.gridColumn5.Width = 98;
            // 
            // gridColumn6
            // 
            this.gridColumn6.Caption = "最强信号距离(m)";
            this.gridColumn6.FieldName = "OverlapCellDistanceAvg";
            this.gridColumn6.Name = "gridColumn6";
            this.gridColumn6.Visible = true;
            this.gridColumn6.VisibleIndex = 6;
            this.gridColumn6.Width = 145;
            // 
            // gridColumn7
            // 
            this.gridColumn7.Caption = "最强信号小区RxLev(dBm)";
            this.gridColumn7.FieldName = "OverlapCellRxLevAvg";
            this.gridColumn7.Name = "gridColumn7";
            this.gridColumn7.Visible = true;
            this.gridColumn7.VisibleIndex = 4;
            this.gridColumn7.Width = 157;
            // 
            // gridColumn8
            // 
            this.gridColumn8.Caption = "平均距离差(m)";
            this.gridColumn8.FieldName = "DistanceDiffAvg";
            this.gridColumn8.Name = "gridColumn8";
            this.gridColumn8.Visible = true;
            this.gridColumn8.VisibleIndex = 7;
            this.gridColumn8.Width = 112;
            // 
            // gridColumn9
            // 
            this.gridColumn9.Caption = "平均电平差(dB)";
            this.gridColumn9.FieldName = "RxLevDiffAvg";
            this.gridColumn9.Name = "gridColumn9";
            this.gridColumn9.Visible = true;
            this.gridColumn9.VisibleIndex = 8;
            this.gridColumn9.Width = 127;
            // 
            // btnGIS
            // 
            this.btnGIS.Appearance.Options.UseTextOptions = true;
            this.btnGIS.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.btnGIS.Dock = System.Windows.Forms.DockStyle.Top;
            this.btnGIS.Location = new System.Drawing.Point(0, 0);
            this.btnGIS.Name = "btnGIS";
            this.btnGIS.Size = new System.Drawing.Size(1116, 23);
            this.btnGIS.TabIndex = 8;
            this.btnGIS.Text = "GIS设置";
            this.btnGIS.Click += new System.EventHandler(this.btnGIS_Click);
            // 
            // ScanOverlapCellForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1116, 401);
            this.Controls.Add(this.splitContainerControl1);
            this.Controls.Add(this.btnGIS);
            this.LookAndFeel.SkinName = "Office 2010 Blue";
            this.Name = "ScanOverlapCellForm";
            this.Text = "过覆盖小区";
            ((System.ComponentModel.ISupportInitialize)(this.gridViewSub)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemMemoEdit2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl)).EndInit();
            this.contextMenuStrip1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridView)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemMemoEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).EndInit();
            this.splitContainerControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.checkEditShowNearestLine.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.checkEditShowOvrlLine.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlPnt)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewPnt)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemMemoEdit5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemMemoEdit6)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlNew)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemMemoEdit4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemMemoEdit3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraGrid.GridControl gridControl;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewSub;
        private DevExpress.XtraGrid.Columns.GridColumn gcSubCellName;
        private DevExpress.XtraGrid.Columns.GridColumn gcSubTestPointCount;
        private DevExpress.XtraEditors.Repository.RepositoryItemMemoEdit repositoryItemMemoEdit2;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView;
        private DevExpress.XtraGrid.Columns.GridColumn gcOverlapCellName;
        private DevExpress.XtraGrid.Columns.GridColumn gcOvlPntCnt;
        private DevExpress.XtraEditors.Repository.RepositoryItemMemoEdit repositoryItemMemoEdit1;
        private DevExpress.XtraGrid.Columns.GridColumn gcOvlCellRxLevAvg;
        private DevExpress.XtraGrid.Columns.GridColumn gcOvlCellDistanceAvg;
        private DevExpress.XtraGrid.Columns.GridColumn gcDistanceAvg;
        private DevExpress.XtraGrid.Columns.GridColumn gcRxLevAvg;
        private DevExpress.XtraGrid.Columns.GridColumn gcSubDistanceAvg;
        private DevExpress.XtraGrid.Columns.GridColumn gcSubRxLevAvg;
        private DevExpress.XtraGrid.Columns.GridColumn gcSubDistanceDiffAvg;
        private DevExpress.XtraGrid.Columns.GridColumn gcSubRxLevDiffAvg;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl1;
        private DevExpress.XtraEditors.SimpleButton btnGIS;
        private DevExpress.XtraEditors.CheckEdit checkEditShowOvrlLine;
        private DevExpress.XtraEditors.CheckEdit checkEditShowNearestLine;
        private DevExpress.XtraEditors.SimpleButton btnShowAll;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip1;
        private System.Windows.Forms.ToolStripMenuItem miExp2Xls;
        private DevExpress.XtraGrid.Columns.GridColumn gcSubCellCI;
        private DevExpress.XtraGrid.Columns.GridColumn gcOverlapCellCI;
        private DevExpress.XtraGrid.GridControl gridControlNew;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn10;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn11;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn13;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn14;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn12;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn15;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn16;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn17;
        private DevExpress.XtraEditors.Repository.RepositoryItemMemoEdit repositoryItemMemoEdit4;
        private DevExpress.XtraEditors.Repository.RepositoryItemMemoEdit repositoryItemMemoEdit3;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn6;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn7;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn8;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn9;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn18;
        private DevExpress.XtraGrid.GridControl gridControlPnt;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewPnt;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn19;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn20;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn21;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn22;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn23;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn24;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn25;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn26;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn27;
        private DevExpress.XtraEditors.Repository.RepositoryItemMemoEdit repositoryItemMemoEdit5;
        private DevExpress.XtraEditors.Repository.RepositoryItemMemoEdit repositoryItemMemoEdit6;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn28;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn29;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn30;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn31;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn32;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn33;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn34;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn35;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn36;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn37;
    }
}