﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using System.Drawing;
using System.Drawing.Drawing2D;
using MasterCom.Util;
using System.Windows.Forms;
using System.Runtime.Serialization;
using System.Collections;
using MasterCom.RAMS.Func.EventBlock;
using MasterCom.MTGis;
using MapWinGIS;
using MasterCom.RAMS.Func.ProblemBlock;
namespace MasterCom.RAMS.Func
{
    [Serializable()]
    public class ProblemBlockOpLayer_GDI : CustomDrawLayer
    {
        public Pen selPen { get; set; } = new Pen(Color.Cyan, 3);
        string name;
        public ProblemBlockOpLayer_GDI(MapOperation mp, string name)
           : base(mp, name)
        {
            this.name = name;
        }
        public static bool isOrder { get; set; } = false;
        public override void Draw(System.Drawing.Rectangle clientRect, System.Drawing.Rectangle updateRect, Graphics graphics)
        {
            if (!IsVisible)
            {
                return;
            }
            updateRect.Inflate((int)(40 * 10000 / Map.Scale), (int)(40 * 10000 / Map.Scale));
            DbRect dRect;
            Map.FromDisplay(updateRect, out dRect);
            graphics.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.HighQuality;

            double temp_long = Map.GetCenter().x;
            double temp_lati = Map.GetCenter().y;
            DbPoint ptDSel = new DbPoint(temp_long, temp_lati);
            PointF scrPointSel;
            Map.ToDisplay(ptDSel, out scrPointSel);
            //底层20米的精度跨度大小 0.0001951
            double llGap = (0.0001951 / 20) * 100;
            DbPoint ptDSel2 = new DbPoint(temp_long + llGap, temp_lati);
            PointF scrPointSel2;
            Map.ToDisplay(ptDSel2, out scrPointSel2);
            int rGap = (int)((scrPointSel2.X - scrPointSel.X) / 2) + 1;
            if (name == "结构问题点图层")
                DrawItemByStruct(MainModel.CurProblemBlockList, dRect, rGap, graphics);
            else
                DrawItem(MainModel.CurProblemBlockList, dRect, rGap, graphics);
        }

        private void DrawItemByStruct(List<ProblemBlockItem> problemBlockList, DbRect dRect, int rGap, Graphics graphics)
        {
            int iRed = 0;
            int iBlue = 0;
            int iGreen = 0;
            foreach (ProblemBlockItem block in problemBlockList)
            {
                bool isClosePoint = false;
                if (block.attachFileDesc.Equals(""))
                {
                    iRed = 195;
                    iBlue = 0;
                    iGreen = 0;
                    if (block.round_id != 0)
                    {
                        if (block.status == 1)
                        {
                            iRed = 0;
                            iBlue = 0;
                            iGreen = 0;
                        }
                        else if (block.status == 2)
                        {
                            iRed = 255;
                            iBlue = 255;
                            iGreen = 255;
                            isClosePoint = true;
                        }
                    }
                }
                SolidBrush brush = new SolidBrush(Color.FromArgb(230, Color.FromArgb(iRed, iBlue, iGreen)));
                drawBolck(dRect, rGap, graphics, block, isClosePoint, brush);
            }
        }

        private void drawBolck(DbRect dRect, int rGap, Graphics graphics, ProblemBlockItem block, bool isClosePoint, SolidBrush brush)
        {
            if (block.Within(dRect.x1, dRect.y1, dRect.x2, dRect.y2))
            {
                bool selected = false;
                if (mainModel.MainForm.GetMapForm().CurSelProblemBlockID == block.blockId)
                {
                    selected = true;
                }
                Region blockReg = null;
                DbPoint dPoint = new DbPoint(Convert.ToDouble(block.CenterLongitude), Convert.ToDouble(block.CenterLatitude));
                PointF point;
                this.Map.ToDisplay(dPoint, out point);
                float radius = rGap > 2 ? rGap : 2;
                RectangleF rect = new RectangleF(point.X - radius, point.Y - radius, radius * 2, radius * 2);
                GraphicsPath gp = new GraphicsPath();
                if (isClosePoint)
                    graphics.DrawEllipse(Pens.Black, new RectangleF(point.X - radius, point.Y - radius, radius * 2, radius * 2));
                gp.AddEllipse(rect);
                blockReg = new Region(gp);
                graphics.FillRegion(brush, blockReg);
                if (selected)
                {
                    RectangleF rctf = blockReg.GetBounds(graphics);
                    graphics.DrawRectangle(selPen, (int)rctf.Left, (int)rctf.Top, (int)rctf.Width, (int)rctf.Height);
                }
            }
        }

        private void DrawItem(List<ProblemBlockItem> problemBlockList, DbRect dRect, int rGap, Graphics graphics)
        {
            int iRed = 0;
            int iBlue = 0;
            int iGreen = 0;
            foreach (ProblemBlockItem block in problemBlockList)
            {
                if (isOrder && (block.order_seq == null || block.order_seq == ""))
                {
                    continue;
                }
                if (block.attachFileDesc.Equals(""))
                {
                    iRed = 195;
                    iBlue = 0;
                    iGreen = 0;
                }
                else if (block.attachFileDesc.Equals("申请挂起中"))
                {
                    iRed = 255;
                    iBlue = 255;
                    iGreen = 30;
                }
                else if (block.attachFileDesc.Equals("挂起审核通过"))
                {
                    iRed = 0;
                    iBlue = 175;
                    iGreen = 80;
                }
                else if (block.attachFileDesc.Equals("取消挂起"))
                {
                    iRed = 128;
                    iBlue = 128;
                    iGreen = 0;
                }
                else if (block.attachFileDesc.Equals("申请黑点中"))
                {
                    iRed = 0;
                    iBlue = 112;
                    iGreen = 192;
                }
                else if (block.attachFileDesc.Equals("黑点审核通过"))
                {
                    iRed = 0;
                    iBlue = 32;
                    iGreen = 96;
                }
                else if (block.attachFileDesc.Equals("黑点审核不通过"))
                {
                    iRed = 112;
                    iBlue = 48;
                    iGreen = 160;
                }
                SolidBrush brush = new SolidBrush(Color.FromArgb(230, Color.FromArgb(iRed, iBlue, iGreen)));
                drawBlock(dRect, rGap, graphics, block, brush);
            }
        }

        private void drawBlock(DbRect dRect, int rGap, Graphics graphics, ProblemBlockItem block, SolidBrush brush)
        {
            if (block.Within(dRect.x1, dRect.y1, dRect.x2, dRect.y2))
            {
                bool selected = false;
                if (mainModel.MainForm.GetMapForm().CurSelProblemBlockID == block.blockId)
                {
                    selected = true;
                }
                List<ProblemBlockEventItem> evtList = block.AbEvents;
                Region blockReg = null;
                foreach (ProblemBlockEventItem evt in evtList)
                {
                    DbPoint dPoint = new DbPoint(evt.ilongitude, evt.ilatitude);
                    PointF point;
                    this.Map.ToDisplay(dPoint, out point);
                    float radius = rGap > 2 ? rGap : 2;
                    RectangleF rect = new RectangleF(point.X - radius, point.Y - radius, radius * 2, radius * 2);
                    GraphicsPath gp = new GraphicsPath();

                    gp.AddEllipse(rect);
                    if (blockReg == null)
                    {
                        blockReg = new Region(gp);
                    }
                    else
                    {
                        blockReg.Union(gp);
                    }
                }
                graphics.FillRegion(brush, blockReg);

                if (selected && blockReg != null)
                {
                    RectangleF rctf = blockReg.GetBounds(graphics);
                    graphics.DrawRectangle(selPen, (int)rctf.Left, (int)rctf.Top, (int)rctf.Width, (int)rctf.Height);
                }
            }
        }

        public int Select(MapOperation2 mop2)
        {
            if (IsVisible)
            {
                double temp_long = Map.GetCenter().x;
                double temp_lati = Map.GetCenter().y;
                DbPoint ptDSel = new DbPoint(temp_long, temp_lati);
                PointF scrPointSel;
                Map.ToDisplay(ptDSel, out scrPointSel);
                //底层20米的精度跨度大小 0.0001951
                double llGap = (0.0001951 / 20) * 100;
                DbPoint ptDSel2 = new DbPoint(temp_long + llGap, temp_lati);
                PointF scrPointSel2;
                Map.ToDisplay(ptDSel2, out scrPointSel2);
                float rGap = ((scrPointSel2.X - scrPointSel.X) / 2) + 1;

                return selectItem(MainModel.CurProblemBlockList, rGap, mop2);
            }
            return 0;
        }
        private int selectItem(List<ProblemBlockItem> problemBlockList, float rGap, MapOperation2 mop2)
        {
            foreach (ProblemBlockItem block in problemBlockList)
            {
                foreach (ProblemBlockEventItem e in block.AbEvents)
                {
                    DbPoint dPoint = new DbPoint(e.ilongitude, e.ilatitude);
                    PointF point;
                    this.Map.ToDisplay(dPoint, out point);
                    float radius = rGap;
                    RectangleF rect = new RectangleF(point.X - radius, point.Y - radius, radius * 2, radius * 2);
                    DbRect dRect;
                    this.Map.FromDisplay(rect, out dRect);
                    if (mop2.CheckCenterInDRect(dRect))
                        return block.blockId;
                }
            }
            return 0;
        }

        public static int OutputShapeFile(string filename)
        {
            try
            {
                if (MainModel.GetInstance().CurProblemBlockList.Count > 0)
                {
                    Shapefile shpFile = new Shapefile();
                    int idIdx = 0;
                    int fBlockId = idIdx++;
                    int fLongId = idIdx++;
                    int fLatId = idIdx++;
                    int fAbCntId = idIdx++;
                    int fNormalDay = idIdx++;
                    int fAbnormalDay = idIdx;

                    bool result = shpFile.CreateNew("", MapWinGIS.ShpfileType.SHP_POLYGON);
                    if (!result)
                    {
                        DevExpress.XtraEditors.XtraMessageBox.Show(shpFile.get_ErrorMsg(shpFile.LastErrorCode));
                        return -1;
                    }

                    ShapeHelper.InsertNewField(shpFile, "BlockId", FieldType.INTEGER_FIELD, 7, 20, ref fBlockId);
                    ShapeHelper.InsertNewField(shpFile, "Longitude", FieldType.DOUBLE_FIELD, 7, 20, ref fLongId);
                    ShapeHelper.InsertNewField(shpFile, "Latitude", FieldType.DOUBLE_FIELD, 7, 20, ref fLatId);
                    ShapeHelper.InsertNewField(shpFile, "AbnormalCount", FieldType.INTEGER_FIELD, 7, 20, ref fAbCntId);
                    ShapeHelper.InsertNewField(shpFile, "NormalDay", FieldType.INTEGER_FIELD, 7, 20, ref fNormalDay);
                    ShapeHelper.InsertNewField(shpFile, "AbnormalDay", FieldType.INTEGER_FIELD, 7, 20, ref fAbnormalDay);

                    double radius = 0.00048775;//大约50米
                    int shpIdx = 0;
                    MainModel.GetInstance().MainForm.Cursor = Cursors.WaitCursor;
                    foreach (ProblemBlockItem bb in MainModel.GetInstance().CurProblemBlockList)
                    {
                        if (isOrder && (bb.order_seq == null || bb.order_seq == ""))
                        {
                            continue;
                        }
                        MapWinGIS.Shape spBase = getSpBase(radius, bb);
                        shpFile.EditInsertShape(spBase, ref shpIdx);
                        shpFile.EditCellValue(fBlockId, shpIdx, bb.blockId);
                        shpFile.EditCellValue(fLongId, shpIdx, bb.longitude);
                        shpFile.EditCellValue(fLatId, shpIdx, bb.latitude);
                        shpFile.EditCellValue(fAbCntId, shpIdx, bb.abnormal_event_count);
                        shpFile.EditCellValue(fNormalDay, shpIdx, bb.normal_days);
                        shpFile.EditCellValue(fAbnormalDay, shpIdx, bb.abnormal_days);
                        shpIdx++;
                    }
                    ShapeHelper.DeleteShpFile(filename);
                    shpFile.SaveAs(filename, null);
                    shpFile.Close();
                    return 1;
                }
                else
                {
                    return 0;
                }
            }
            catch
            {
                return -1;
            }
            finally
            {
                MainModel.GetInstance().MainForm.Cursor = Cursors.Default;
            }
        }

        private static MapWinGIS.Shape getSpBase(double radius, ProblemBlockItem bb)
        {
            MapWinGIS.Shape spBase = null;
            foreach (ProblemBlockEventItem evtItem in bb.AbEvents)
            {
                if (evtItem.ilongitude < 90 || evtItem.ilatitude > 45)
                    continue;
                MapWinGIS.Shape evtShp = ShapeHelper.CreateCircleShape(evtItem.ilongitude, evtItem.ilatitude, radius);
                if (spBase != null)
                {
                    spBase = spBase.Clip(evtShp, MapWinGIS.tkClipOperation.clUnion);
                }
                else
                {
                    spBase = evtShp;
                }
            }

            return spBase;
        }
    }
}
