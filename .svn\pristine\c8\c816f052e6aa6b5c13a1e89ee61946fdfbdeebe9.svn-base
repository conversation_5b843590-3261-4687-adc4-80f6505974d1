﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using DevExpress.XtraEditors;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class CellWrongDirSettingDlg_CP : BaseDialog
    {
        public CellWrongDirSettingDlg_CP()
        {
            InitializeComponent();

            chkTime.Checked = false;
            gbxSecondBatch.Enabled = false;
        }

        public void SetCondition(CellWrongDirCondition cond)
        {
            if (cond == null)
                return;

            spinEditRSCP.Value = (decimal)cond.RxLevMin;
            spinEditDis.Value = (decimal)cond.DistanceMin;
            spinEditAngle.Value = cond.AngleMin;
            spinEditPer.Value = (decimal)cond.WrongRateMin;

            chkTime.Checked = cond.IsTwiceBatch;
            dtPickerFirstStart.Value = cond.PeriodFirstBatch.BeginTime;
            dtPickerFirstEnd.Value = cond.PeriodFirstBatch.EndTime;
            dtPickerSecondStart.Value = cond.PeriodSecondBatch.BeginTime;
            dtPickerSecondEnd.Value = cond.PeriodSecondBatch.EndTime;
        }

        public CellWrongDirCondition GetConditon()
        {
            CellWrongDirCondition cond = new CellWrongDirCondition((float)spinEditRSCP.Value, (double)spinEditDis.Value
                , (int)spinEditAngle.Value, (double)spinEditPer.Value, chkTime.Checked,
                new MasterCom.Util.TimePeriod(dtPickerFirstStart.Value, dtPickerFirstEnd.Value),
                new MasterCom.Util.TimePeriod(dtPickerSecondStart.Value, dtPickerSecondEnd.Value));
            return cond;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
        }

        //private void cbxSecondBatch_CheckedChanged(object sender, EventArgs e)
        //{
        //    gbxSecondBatch.Enabled = cbxSecondBatch.Checked;
        //}

        private void dtPickerFirstStart_ValueChanged(object sender, EventArgs e)
        {
            if (dtPickerFirstStart.Value > dtPickerFirstEnd.Value)
                dtPickerFirstStart.Value = dtPickerFirstEnd.Value;
        }

        private void dtPickerFirstEnd_ValueChanged(object sender, EventArgs e)
        {
            if (dtPickerFirstStart.Value > dtPickerFirstEnd.Value)
                dtPickerFirstEnd.Value = dtPickerFirstStart.Value;
        }

        private void dtPickerSecondStart_ValueChanged(object sender, EventArgs e)
        {
            if (dtPickerSecondStart.Value > dtPickerSecondEnd.Value)
                dtPickerSecondStart.Value = dtPickerSecondEnd.Value;
        }

        private void dtPickerSecondEnd_ValueChanged(object sender, EventArgs e)
        {
            if (dtPickerSecondStart.Value > dtPickerSecondEnd.Value)
                dtPickerSecondEnd.Value = dtPickerSecondStart.Value;
        }

        private void chkTime_CheckedChanged(object sender, EventArgs e)
        {
            gbxFirstBatch.Enabled = gbxSecondBatch.Enabled = chkTime.Checked;
        }
    }
}
