﻿using MasterCom.RAMS.Func;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using DevExpress.XtraCharts;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class TdAntennaForm : MinCloseForm
    {
        MapForm mapForm;
        public TdAntennaForm(MainModel mainModel):base(mainModel)
        {
            InitializeComponent();
            initAngleArgs();
            this.dataGridViewAngle.Columns[0].Frozen = true;
            this.dataGridViewAngle.Columns[1].Frozen = true;
            this.mapForm = MainModel.MainForm.GetMapForm();
        }     

        public List<List<NPOIRow>> nrDatasList { get; set; }
        public List<string> sheetNames { get; set; }
        int iDataNum = 0;
        Dictionary<String, ZTTdAntenna.CellAngleData> DicCellAngleData = new Dictionary<string, ZTTdAntenna.CellAngleData>();

        public void FillData(List<ZTTdAntenna.CellAngleData> listCellAngleData)
        {
            foreach (ZTTdAntenna.CellAngleData cellData in listCellAngleData)
            {
                if (!DicCellAngleData.ContainsKey(cellData.cellname))
                    DicCellAngleData.Add(cellData.cellname, cellData);
            }

            dataGridView.Columns.Clear();
            dataGridView.Rows.Clear();
            dataGridViewCell.Columns.Clear();
            dataGridViewCell.Rows.Clear();
            dataGridViewAbnormal.Columns.Clear();
            dataGridViewAbnormal.Rows.Clear();
            dataGridViewBts.Columns.Clear();
            dataGridViewBts.Rows.Clear();

            iDataNum = DicCellAngleData.Count;
            labNum.Text = iDataNum.ToString();
            int iPage = iDataNum % 200 > 0 ? iDataNum / 200 + 1 : iDataNum / 200;
            labPage.Text = iPage.ToString();

            for (int i = 0; i < nrDatasList.Count; i++)
            {
                int idx = 0;
                foreach (NPOIRow row in nrDatasList[i])
                {
                    if (idx == 0)
                    {
                        intDataViewColumn(row.cellValues, i);
                        idx++;
                    }
                    else
                    {
                        switch (i)
                        {
                            case 0:
                                initDataRow(dataGridView, row, row.cellValues[0].ToString());
                                break;
                            case 2:
                                initDataRow(dataGridViewAbnormal, row, row.cellValues[1].ToString());
                                break;
                            case 3:
                                initDataRow(dataGridViewCell, row, row.cellValues[6].ToString());
                                break;
                            case 4:
                                initDataRow(dataGridViewBts, row, row.cellValues[5].ToString());
                                break;
                            default:
                                break;
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 按小区模糊查找，前200个小区
        /// </summary>
        private void FillData(string strCellName)
        {
            dataGridViewCell.Rows.Clear();//小区级
            int rowCellAt = 0;
            foreach (NPOIRow rowData in nrDatasList[3])
            {
                if (nrDatasList[3][0].cellValues[0].ToString() == rowData.cellValues[0].ToString())
                    continue;

                if (strCellName != "" && rowData.cellValues[6].ToString().IndexOf(strCellName) < 0)
                    continue;

                if (rowCellAt >= 200)
                    break;
                initDataRow(dataGridViewCell, rowData, rowData.cellValues[6].ToString());
                rowCellAt++;
            }
            FillRegionAndAngleData();
            FillBtsAngleData();
        }

        /// <summary>
        /// 按页数查找
        /// </summary>
        private void FillData(int iPage)
        {
            dataGridViewCell.Rows.Clear();//小区级
            int iCount = -1;
            int rowCellAt = 0;
            foreach (NPOIRow rowData in nrDatasList[3])
            {
                if (nrDatasList[3][0].cellValues[0].ToString() == rowData.cellValues[0].ToString())
                    continue;

                iCount++;
                if (iCount / 200 != iPage)
                    continue;
                initDataRow(dataGridViewCell, rowData, rowData.cellValues[6].ToString());
                rowCellAt++;
            }
            FillRegionAndAngleData();
            FillBtsAngleData();
        }

        /// <summary>
        /// 区间级数据赋值
        /// </summary>
        private void FillRegionAndAngleData()
        {
            dataGridView.Rows.Clear();

            foreach (DataGridViewRow dgCell in dataGridViewCell.Rows)
            {
                if (dgCell == null || dgCell.Tag == null)
                    continue;

                foreach (NPOIRow rowData in nrDatasList[0])
                {
                    if (rowData.cellValues[0].ToString() == dgCell.Tag.ToString())
                    {
                        initDataRow(dataGridView, rowData, rowData.cellValues[0].ToString());
                    }
                }
            }
        }

        /// <summary>
        /// 刷新基站列表
        /// </summary>
        private void FillBtsAngleData()
        {
            dataGridViewBts.Rows.Clear();
            List<string> strBtsNameList = new List<string>();
            foreach (DataGridViewRow dgCell in dataGridViewCell.Rows)
            {
                if (dgCell == null || dgCell.Tag == null
                    || !DicCellAngleData.ContainsKey(dgCell.Tag.ToString()))
                    continue;
                if (!strBtsNameList.Contains(DicCellAngleData[dgCell.Tag.ToString()].strbtsname))
                {
                    strBtsNameList.Add(DicCellAngleData[dgCell.Tag.ToString()].strbtsname);
                }
            }
            foreach (string strBtsName in strBtsNameList)
            {
                foreach (NPOIRow rowData in nrDatasList[4])
                {
                    if (rowData.cellValues[5].ToString() == strBtsName)
                    {
                        initDataRow(dataGridViewBts, rowData, rowData.cellValues[5].ToString());
                    }
                }
            }
        }

        /// <summary>
        /// 初始化列头赋值
        /// </summary>
        private void intDataViewColumn(List<object> objList, int index)
        {
            int idx = 1;
            foreach (object obj in objList)
            {
                switch (index)
                {
                    case 0:
                        dataGridView.Columns.Add(idx++.ToString(), obj.ToString());
                        break;
                    case 2:
                        dataGridViewAbnormal.Columns.Add(idx++.ToString(), obj.ToString());
                        break;
                    case 3:
                        dataGridViewCell.Columns.Add(idx++.ToString(), obj.ToString());
                        break;
                    case 4:
                        dataGridViewBts.Columns.Add(idx++.ToString(), obj.ToString());
                        break;
                    default:
                        break;
                }
            }
        }

        /// <summary>
        /// 初始化数据赋值
        /// </summary>
        private void initDataRow(DataGridView datatGridView, NPOIRow nop, string strCellName)
        {
            DataGridViewRow row = new DataGridViewRow();
            row.Tag = strCellName;//小区名称
            foreach (object obj in nop.cellValues)
            {
                DataGridViewTextBoxCell boxcell = new DataGridViewTextBoxCell();
                boxcell.Value = obj.ToString();
                row.Cells.Add(boxcell);
            }
            datatGridView.Rows.Add(row);
        }

        ZTTdAntenna.CellAngleData data;
        private void miShowGis_Click(object sender, EventArgs e)
        {
            MainModel.ClearDTData();

            string strCellName = "";
            if (xTabPageData.SelectedTabPageIndex == 0)
            {
                int iRowId = dataGridViewCell.SelectedCells[0].RowIndex;
                strCellName = dataGridViewCell.Rows[iRowId].Tag.ToString();
            }
            else if (xTabPageData.SelectedTabPageIndex == 2)
            {
                strCellName = dataGridView.SelectedRows[0].Tag as string;
            }
            else
            {
                return;
            }

            if (DicCellAngleData.ContainsKey(strCellName))
                data = DicCellAngleData[strCellName];
            else
                return;

            if (data != null)
            {
                foreach (TestPoint tp in data.tpList)
                {
                    MainModel.DTDataManager.Add(tp);
                }
                MainModel.SelectedTDCell = CellManager.GetInstance().GetTDCellByName(data.cellname);
                MainModel.FireDTDataChanged(this);
                if (!ZTGsmAntenna.isScanStat)
                {
                    MainModel.FireSetDefaultMapSerialTheme("TDSCDMA", "PCCPCH_RSCP");
                } 
                else
                {
                    MainModel.FireSetDefaultMapSerialTheme("TDSCDMA_SCAN", "PCCPCH_RSCP");
                }
            }
        }       

        private void miShowSimulation_Click(object sender, EventArgs e)
        {
            string strCellName = "";
            if (xTabPageData.SelectedTabPageIndex == 0)
            {
                int iRowId = dataGridViewCell.SelectedCells[0].RowIndex;
                strCellName = dataGridViewCell.Rows[iRowId].Tag.ToString();
            }
            else if (xTabPageData.SelectedTabPageIndex == 2)
            {
                strCellName = dataGridView.SelectedRows[0].Tag as string;
            }
            else
            {
                return;
            }

            if (DicCellAngleData.ContainsKey(strCellName))
                data = DicCellAngleData[strCellName];
            else
                return;

            if (data != null)
            {
                CalcAntDetailValue();

                MainModel.SelectedTDCell = CellManager.GetInstance().GetTDCellByName(data.cellname);
                MainModel.MainForm.GetMapForm().GoToView(data.cellLongitude, data.cellLatitude);

                AntLineLayer antLayer = mapForm.GetLayerBase(typeof(AntLineLayer)) as AntLineLayer;
                if (antLayer != null)
                {
                    antLayer.Invalidate();
                    antLayer.dtLongLatList = data.longLatDTList;
                    antLayer.modelLongLatList = data.longLatModelList;
                    antLayer.Invalidate();
                }
            }
        }

        /// <summary>
        /// 计算权值
        /// </summary>
        private void CalcAntDetailValue()
        {
            AntParaItem antItem = new AntParaItem("D", 1, 1, 1, 1, "");
            antItem.Init(0, 90, 90, 0);
            double[] modelArray = antItem.getPowerArray();

            LongLat ll = new LongLat();
            ll.fLongitude = (float)(data.cellLongitude);
            ll.fLatitude = (float)(data.cellLatitude);
            data.longLatModelList = ZTAntFuncHelper.getCellEmulateCoverModel(ll, modelArray, 10, -20, data.iangle_dir);

            int iMaxValue = -19;
            int iMinValue = 50;
            ZTAntFuncHelper.getMaxAndMinValue(data.ciItem.rscpNewArray, ref iMaxValue, ref iMinValue);
            data.longLatDTList = ZTAntFuncHelper.getCellEmulateCoverTest(ll, data.ciItem.rscpNewArray, iMaxValue, iMinValue, data.iangle_dir);
        }

        private void 拆分导出CSVToolStripMenuItem_Click(object sender, EventArgs e)
        {
            ZTAntFuncHelper.OutputCsvFile(nrDatasList, sheetNames);
        }

        private void miExportWholeExcel_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(nrDatasList, sheetNames);
        }

        private void miShowChart_Click(object sender, EventArgs e)
        {
            string strCellName = "";
            if (xTabPageData.SelectedTabPageIndex == 0)
            {
                int iRowId = dataGridViewCell.SelectedCells[0].RowIndex;
                strCellName = dataGridViewCell.Rows[iRowId].Tag.ToString();
            }
            else if (xTabPageData.SelectedTabPageIndex == 1)
            {
                int iRowId = dataGridViewBts.SelectedCells[0].RowIndex;
                strCellName = dataGridViewBts.Rows[iRowId].Tag.ToString();
            }
            else if (xTabPageData.SelectedTabPageIndex == 2)
            {
                strCellName = dataGridView.SelectedRows[0].Tag as string;
            }
            else
            {
                return;
            }
            if (xTabPageData.SelectedTabPageIndex != 1)
            {
                if (DicCellAngleData.ContainsKey(strCellName))
                    data = DicCellAngleData[strCellName];
            }
            else
            {
                data = getBtsData(strCellName);
            }

            if ( data != null)
            {
                setAngelTable(data);
                if (chartControl1.Series.Count == 3)
                {
                    chartControl1.Series.RemoveAt(1);
                    chartControl1.Series.RemoveAt(1);
                }
                else if (chartControl1.Series.Count == 2)
                {
                    chartControl1.Series.RemoveAt(1);
                }

                cbbxSeries1_SelectedIndexChanged(null, null);
                cbbxSeries2_SelectedIndexChanged(null, null);
                drawAntRadarSeries(data.ciItem.rscpNewArray);
                drawAntC2ISeries();
                drawAntRscpSeries();
            }
            this.xTabPageData.SelectedTabPageIndex = 3;
        }

        private ZTTdAntenna.CellAngleData getBtsData(string strBts)
        {
            ZTTdAntenna.CellAngleData btsData = new ZTTdAntenna.CellAngleData();
            btsData.cellname = strBts;
            foreach (string strcell in DicCellAngleData.Keys)
            {
                if (strBts.Equals(DicCellAngleData[strcell].strbtsname))
                {
                    if (btsData.c2iNumDic.Count == 0 || btsData.rscpNumDic.Count == 0)
                    {
                        btsData.c2iNumDic = DicCellAngleData[strcell].bExt.c2iNumDic;
                        btsData.rscpNumDic = DicCellAngleData[strcell].bExt.rscpNumDic;
                        btsData.sampleTotal = DicCellAngleData[strcell].bExt.pcchRscpSampleNum;
                    }
                    for (int i = 0; i < 360; i++)
                    {
                        btsData.ciItem.samplNumArray[(i + DicCellAngleData[strcell].iangle_dir) % 360]
                            += DicCellAngleData[strcell].ciItem.samplNumArray[(i + DicCellAngleData[strcell].iangle_dir) % 360];
                        btsData.ciItem.rscpArray[(i + DicCellAngleData[strcell].iangle_dir) % 360]
                            += DicCellAngleData[strcell].ciItem.rscpArray[(i + DicCellAngleData[strcell].iangle_dir) % 360];
                        btsData.ciItem.coverArray[(i + DicCellAngleData[strcell].iangle_dir) % 360]
                            += DicCellAngleData[strcell].ciItem.coverArray[(i + DicCellAngleData[strcell].iangle_dir) % 360];
                        btsData.ciItem.samplArray[(i + DicCellAngleData[strcell].iangle_dir) % 360]
                            += DicCellAngleData[strcell].ciItem.samplArray[(i + DicCellAngleData[strcell].iangle_dir) % 360];
                        btsData.ciItem.dpchRscpArray[(i + DicCellAngleData[strcell].iangle_dir) % 360]
                            += DicCellAngleData[strcell].ciItem.dpchRscpArray[(i + DicCellAngleData[strcell].iangle_dir) % 360];
                        btsData.ciItem.dpchc2iArray[(i + DicCellAngleData[strcell].iangle_dir) % 360]
                            += DicCellAngleData[strcell].ciItem.dpchc2iArray[(i + DicCellAngleData[strcell].iangle_dir) % 360];
                        btsData.ciItem.blerArray[(i + DicCellAngleData[strcell].iangle_dir) % 360]
                            += DicCellAngleData[strcell].ciItem.blerArray[(i + DicCellAngleData[strcell].iangle_dir) % 360];
                        btsData.ciItem.pcchc2iArrayAll[(i + DicCellAngleData[strcell].iangle_dir) % 360]
                            += DicCellAngleData[strcell].ciItem.pcchc2iArrayAll[(i + DicCellAngleData[strcell].iangle_dir) % 360];
                        btsData.ciItem.pcchc2iArrayAll_EcIo[(i + DicCellAngleData[strcell].iangle_dir) % 360]
                            += DicCellAngleData[strcell].ciItem.pcchc2iArrayAll_EcIo[(i + DicCellAngleData[strcell].iangle_dir) % 360];
                        btsData.ciItem.pcchc2iArray3[(i + DicCellAngleData[strcell].iangle_dir) % 360]
                            += DicCellAngleData[strcell].ciItem.pcchc2iArray3[(i + DicCellAngleData[strcell].iangle_dir) % 360];
                    }
                }
            }
            return btsData;
        }

        /// <summary>
        /// 按平滑线绘雷达图
        /// </summary>
        private void drawAntRadarSeries(double[] seriesValues)
        {
            chartControl2.Series.Clear();

            Series series = new Series();
            series.ShowInLegend = false;
            series.PointOptions.PointView = PointView.Values;

            RadarLineSeriesView lineSeriesView = new RadarLineSeriesView();
            lineSeriesView.Color = Color.Blue;
            lineSeriesView.LineMarkerOptions.Size = 2;

            series.View = lineSeriesView;
            series.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
            series.Label.Visible = false;

            for (int i = 359; i >= 0; i--)
            {
                series.Points.Add(new SeriesPoint(i.ToString(), Math.Round(seriesValues[i], 2)));
            }
            chartControl2.Series.Insert(0, series);

            ((RadarDiagram)chartControl2.Diagram).AxisY.Range.MinValue = -140;
            ((RadarDiagram)chartControl2.Diagram).AxisY.Range.MaxValue = -40;
            ((RadarDiagram)chartControl2.Diagram).AxisX.GridSpacing = 20;
            ((RadarDiagram)chartControl2.Diagram).AxisX.Range.SetInternalMinMaxValues(0, 340);
            ((RadarDiagram)chartControl2.Diagram).RotationDirection = RadarDiagramRotationDirection.Clockwise;
            chartControl2.Focus();
        }

        /// <summary>
        /// 画C2I_CDF_PDF图
        /// </summary>
        private void drawAntC2ISeries()
        {
            int iMax = -40;
            double sumCdf = 0;

            Series seriesPDF = new Series();
            seriesPDF.ShowInLegend = false;
            seriesPDF.LegendText = "PDF";
            seriesPDF.PointOptions.PointView = PointView.Values;
            SplineSeriesView lineSeriesViewPDF = new SplineSeriesView();
            lineSeriesViewPDF.Color = Color.Blue;
            lineSeriesViewPDF.LineMarkerOptions.Size = 2;
            seriesPDF.View = lineSeriesViewPDF;
            seriesPDF.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
            seriesPDF.Label.Visible = false;

            Series seriesCDF = new Series();
            seriesCDF.ShowInLegend = false;
            seriesCDF.LegendText = "CDF";
            seriesCDF.PointOptions.PointView = PointView.Values;
            SplineAreaSeriesView lineSeriesViewCDF = new SplineAreaSeriesView();
            lineSeriesViewCDF.Color = Color.Orange;
            seriesCDF.View = lineSeriesViewCDF;
            seriesCDF.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
            seriesCDF.Label.Visible = false;

            ((SplineSeriesView)seriesPDF.View).AxisY = ((XYDiagram)chartC2I.Diagram).SecondaryAxesY[0];

            for (int i = -20; i < 31; i++)
            {
                int iCount = 0;
                if (!data.c2iNumDic.TryGetValue(i, out iCount))
                    iCount = 0;

                double pdf = Math.Round(100.0 * iCount / data.sampleTotal, 2);
                seriesPDF.Points.Add(new SeriesPoint(i.ToString(), pdf));
                sumCdf += pdf;
                if (iMax < (int)pdf + 1)
                    iMax = (int)pdf + 1;

                double cdf = Math.Round(sumCdf, 2);
                seriesCDF.Points.Add(new SeriesPoint(i.ToString(), cdf));
            }

            chartC2I.Series.Clear();
            chartC2I.Series.Insert(0, seriesCDF);
            chartC2I.Series.Insert(1, seriesPDF);
            ((XYDiagram)chartC2I.Diagram).AxisX.GridSpacing = 3;
            ((XYDiagram)chartC2I.Diagram).AxisY.GridSpacing = 10;
            ((XYDiagram)chartC2I.Diagram).SecondaryAxesY[0].GridSpacing = iMax > 20 ? 5 : 2;
            ((SplineSeriesView)seriesPDF.View).AxisY.Range.MaxValue = iMax + 1 >= 100 ? 100 : iMax + 1;
            ((SplineSeriesView)seriesPDF.View).AxisY.Range.MinValue = 0;
            ((SplineSeriesView)seriesPDF.View).AxisX.Range.MaxValue = 30;
            ((SplineSeriesView)seriesPDF.View).AxisX.Range.MinValue = -20;
        }

        /// <summary>
        /// 画Rscp_CDF_PDF图
        /// </summary>
        private void drawAntRscpSeries()
        {
            int iMax = -1;
            double sumCdf = 0;

            Series seriesPDF = new Series();
            seriesPDF.ShowInLegend = false;
            seriesPDF.LegendText = "PDF";
            seriesPDF.PointOptions.PointView = PointView.Values;
            SplineSeriesView lineSeriesViewPDF = new SplineSeriesView();
            lineSeriesViewPDF.Color = Color.Blue;
            lineSeriesViewPDF.LineMarkerOptions.Size = 2;
            seriesPDF.View = lineSeriesViewPDF;
            seriesPDF.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
            seriesPDF.Label.Visible = false;

            Series seriesCDF = new Series();
            seriesCDF.ShowInLegend = false;
            seriesCDF.LegendText = "CDF";
            seriesCDF.PointOptions.PointView = PointView.Values;
            SplineAreaSeriesView lineSeriesViewCDF = new SplineAreaSeriesView();
            lineSeriesViewCDF.Color = Color.Orange;
            seriesCDF.View = lineSeriesViewCDF;
            seriesCDF.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
            seriesCDF.Label.Visible = false;

            ((SplineSeriesView)seriesPDF.View).AxisY = ((XYDiagram)chartRSCP.Diagram).SecondaryAxesY[0];
            for (int i = -140; i < -10; i++)
            {
                int iCount = 0;
                if (!data.rscpNumDic.TryGetValue(i, out iCount))
                    iCount = 0;

                double pdf = Math.Round(100.0 * iCount / data.sampleTotal, 2);
                seriesPDF.Points.Add(new SeriesPoint(i.ToString(), pdf));
                sumCdf += pdf;
                if (iMax < (int)pdf + 1)
                    iMax = (int)pdf + 1;

                double cdf = Math.Round(sumCdf, 2);
                seriesCDF.Points.Add(new SeriesPoint(i.ToString(), cdf));
            }

            chartRSCP.Series.Clear();
            chartRSCP.Series.Insert(0, seriesCDF);
            chartRSCP.Series.Insert(1, seriesPDF);
            ((XYDiagram)chartRSCP.Diagram).AxisX.GridSpacing = 5;
            ((XYDiagram)chartRSCP.Diagram).AxisY.GridSpacing = 10;
            ((SplineSeriesView)seriesPDF.View).AxisY.Range.MaxValue = iMax + 1 >= 100 ? 100 : iMax + 1;
            ((SplineSeriesView)seriesPDF.View).AxisY.Range.MinValue = 0;
            ((SplineSeriesView)seriesPDF.View).AxisX.Range.MaxValue = -10;
            ((SplineSeriesView)seriesPDF.View).AxisX.Range.MinValue = -140;
        }

        ///<summary>
        ///生成0~360度的角度列
        ///</summary>
        public void initAngleArgs()
        {
            for (int i = -179; i < 181; i++ )
            {
                DataGridViewTextBoxColumn angelCol = new DataGridViewTextBoxColumn();
                angelCol.HeaderText = i + "°";
                angelCol.Width = 63;
                dataGridViewAngle.Columns.Add(angelCol);
            }

            cbbxSeries1.Items.Add("PCCPCH RSCP");
            cbbxSeries2.Items.Add("PCCPCH RSCP");
            if (!ZTGsmAntenna.isScanStat)
            {
                cbbxSeries1.Items.Add("DPCH RSCP");
                cbbxSeries1.Items.Add("TD BLER");
                cbbxSeries1.Items.Add("过覆盖指数");
                cbbxSeries1.Items.Add("DPCH C/I");
                cbbxSeries2.Items.Add("DPCH RSCP");
                cbbxSeries2.Items.Add("TD BLER");
                cbbxSeries2.Items.Add("过覆盖指数");
                cbbxSeries2.Items.Add("DPCH C/I");
            }
            else
            {
                cbbxSeries1.Items.Add("平滑PCCPCH RSCP");
                cbbxSeries1.Items.Add("PCCPCH C/I");
                cbbxSeries1.Items.Add("PCCPCH Ec/Io");
                cbbxSeries2.Items.Add("平滑PCCPCH RSCP");
                cbbxSeries2.Items.Add("PCCPCH C/I");
                cbbxSeries2.Items.Add("PCCPCH Ec/Io");
            }
            cbbxSeries1.Items.Add("通信距离");
            cbbxSeries2.Items.Add("通信距离");
            cbbxSeries1.SelectedIndex = 0;
            cbbxSeries2.SelectedIndex = 4;

            Series series = chartControl1.Series[0];
            series.ShowInLegend = false;
            series.PointOptions.PointView = PointView.Values;
            series.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical; //设置x轴有固定间距
            SeriesPoint pt;
            pt = new SeriesPoint(-150, 50);
            series.Points.Add(pt);
            pt = new SeriesPoint(-90, 50);
            series.Points.Add(pt);
            pt = new SeriesPoint(-60, 50);
            series.Points.Add(pt);
            pt = new SeriesPoint(-30, 50);
            series.Points.Add(pt);
            pt = new SeriesPoint(-15, 50);
            series.Points.Add(pt);
            pt = new SeriesPoint(0, 50);
            series.Points.Add(pt);
            pt = new SeriesPoint(15, 50);
            series.Points.Add(pt);
            pt = new SeriesPoint(30, 50);
            series.Points.Add(pt);
            pt = new SeriesPoint(60, 50);
            series.Points.Add(pt);
            pt = new SeriesPoint(90, 50);
            series.Points.Add(pt);
            pt = new SeriesPoint(150, 50);
            series.Points.Add(pt);

            ((XYDiagram)chartControl1.Diagram).AxisX.Range.MinValue = -179;
            ((XYDiagram)chartControl1.Diagram).AxisX.Range.MaxValue = 180;
        }

        /// <summary>
        /// "天线辐射波形重建"中显示表格信息
        /// </summary>
        public void setAngelTable( ZTTdAntenna.CellAngleData data)
        {
            dataGridViewAngle.Rows.Clear();
            int rowAt = 0;
            dataGridViewAngle.Rows.Add(1);
            int colAt = 0;
            dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = data.cellname;
            dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = "PCCPCH RSCP";
            for (int i = 181; i < 360; i++) //181~359度（按正负算-179~-1）
            {
                dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = (data.ciItem.samplNumArray[i] == 0 ? 0 : 
                    data.ciItem.rscpArray[i] / data.ciItem.samplNumArray[i]).ToString();               
            }
            for (int i = 0; i < 181; i++) //0~180度（按正负算0~180）
            {
                dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = (data.ciItem.samplNumArray[i] == 0 ? 0 : 
                    data.ciItem.rscpArray[i] / data.ciItem.samplNumArray[i]).ToString();
            }
            if (!ZTGsmAntenna.isScanStat)
            {
                dataGridViewAngle.Rows.Add(1);
                rowAt++;
                colAt = 0;
                dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = data.cellname;
                dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = "DPCH RSCP";
                for (int i = 181; i < 360; i++)
                {
                    dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = (data.ciItem.samplNumArray[i] == 0 ? 0 : 
                        data.ciItem.dpchRscpArray[i] / data.ciItem.samplNumArray[i]).ToString();
                }
                for (int i = 0; i < 181; i++) //0~180度（按正负算0~180）
                {
                    dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = (data.ciItem.samplNumArray[i] == 0 ? 0 : 
                        data.ciItem.dpchRscpArray[i] / data.ciItem.samplNumArray[i]).ToString();
                }

                dataGridViewAngle.Rows.Add(1);
                rowAt++;
                colAt = 0;
                dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = data.cellname;
                dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = "TD BLER";
                for (int i = 181; i < 360; i++)
                {
                    dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = (data.ciItem.samplNumArray[i] == 0 ? 0 : 
                        data.ciItem.blerArray[i] / data.ciItem.samplNumArray[i]).ToString();
                }
                for (int i = 0; i < 181; i++) //0~180度（按正负算0~180）
                {
                    dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = (data.ciItem.samplNumArray[i] == 0 ? 0 : 
                        data.ciItem.blerArray[i] / data.ciItem.samplNumArray[i]).ToString();
                }

                dataGridViewAngle.Rows.Add(1);
                rowAt++;
                colAt = 0;
                dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = data.cellname;
                dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = "过覆盖指数";
                for (int i = 181; i < 360; i++)
                {
                    dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = (Math.Round(data.ciItem.samplNumArray[i] == 0 ? 0 : 
                        data.ciItem.coverArray[i] * 1.0 / data.ciItem.samplNumArray[i], 2)).ToString();

                }
                for (int i = 0; i < 181; i++) //0~180度（按正负算0~180）
                {
                    dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = (Math.Round(data.ciItem.samplNumArray[i] == 0 ? 0 : 
                        data.ciItem.coverArray[i] * 1.0 / data.ciItem.samplNumArray[i], 2)).ToString();
                }

                dataGridViewAngle.Rows.Add(1);
                rowAt++;
                colAt = 0;
                dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = data.cellname;
                dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = "DPCH C/I";
                for (int i = 181; i < 360; i++)
                {
                    dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = (data.ciItem.samplNumArray[i] == 0 ? 0 : 
                        data.ciItem.dpchc2iArray[i] / data.ciItem.samplNumArray[i]).ToString();
                }
                for (int i = 0; i < 181; i++) //0~180度（按正负算0~180）
                {
                    dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = (data.ciItem.samplNumArray[i] == 0 ? 0 : 
                        data.ciItem.dpchc2iArray[i] / data.ciItem.samplNumArray[i]).ToString();
                }
            } 
            else
            {
                dataGridViewAngle.Rows.Add(1);
                rowAt++;
                colAt = 0;
                dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = data.cellname;
                dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = "平滑PCCPCH RSCP";
                for (int i = 181; i < 360; i++)
                {
                    dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = (data.ciItem.samplNumArray[i] == 0 ? 0 : 
                        data.ciItem.rscpNewArray[i] / data.ciItem.samplNumArray[i]).ToString();
                }
                for (int i = 0; i < 181; i++) //0~180度（按正负算0~180）
                {
                    dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = (data.ciItem.samplNumArray[i] == 0 ? 0 : 
                        data.ciItem.rscpNewArray[i] / data.ciItem.samplNumArray[i]).ToString();
                }

                dataGridViewAngle.Rows.Add(1);
                rowAt++;
                colAt = 0;
                dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = data.cellname;
                dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = "PCCPCH C/I";
                for (int i = 181; i < 360; i++)
                {
                    dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = (data.ciItem.samplNumArray[i] == 0 ?
                        0 : data.ciItem.pcchc2iArrayAll[i] / data.ciItem.samplNumArray[i]).ToString();
                }
                for (int i = 0; i < 181; i++) //0~180度（按正负算0~180）
                {
                    dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = (data.ciItem.samplNumArray[i] == 0 ?
                        0 : data.ciItem.pcchc2iArrayAll[i] / data.ciItem.samplNumArray[i]).ToString();
                }

                dataGridViewAngle.Rows.Add(1);
                rowAt++;
                colAt = 0;
                dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = data.cellname;
                dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = "PCCPCH Ec/Io";
                for (int i = 181; i < 360; i++)
                {
                    dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = (Math.Round(data.ciItem.samplNumArray[i] == 0 ? 
                        0 : data.ciItem.pcchc2iArrayAll_EcIo[i] * 1.0 / data.ciItem.samplNumArray[i], 2)).ToString();

                }
                for (int i = 0; i < 181; i++) //0~180度（按正负算0~180）
                {
                    dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = (Math.Round(data.ciItem.samplNumArray[i] == 0 ?
                        0 : data.ciItem.pcchc2iArrayAll_EcIo[i] * 1.0 / data.ciItem.samplNumArray[i], 2)).ToString();
                }
            }
            dataGridViewAngle.Rows.Add(1);
            rowAt++;
            colAt = 0;
            dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = data.cellname;
            dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = "通信距离";
            for (int i = 181; i < 360; i++)
            {
                dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = (Math.Round(data.ciItem.samplNumArray[i] == 0 ?
                    0 : (float)(data.ciItem.samplArray[i] / data.ciItem.samplNumArray[i]), 2)).ToString();
            }
            for (int i = 0; i < 181; i++) //0~180度（按正负算0~180）
            {
                dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = (Math.Round(data.ciItem.samplNumArray[i] == 0 ?
                    0 : (float)(data.ciItem.samplArray[i] / data.ciItem.samplNumArray[i]), 2)).ToString();
            }
        }

        private void cbbxSeries1_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (data == null)
            {
                return;
            }

            Cursor.Current = Cursors.WaitCursor;
            if (this.cbbxSeries1.Text == this.cbbxSeries2.Text)
            {
                if (chartControl1.Series.Count == 1)
                {
                    return;
                }
                Series series = chartControl1.Series[1];
                series.Points.Clear();
                checkReverse();
                return; //选同一个参数，不作比较画图
            }
            if (!ZTGsmAntenna.isScanStat)
            {
                switch (this.cbbxSeries1.Text)
                {
                    case "PCCPCH RSCP":
                        {
                            DrawTable1Series(data.ciItem.rscpArray);
                        }
                        break;
                    case "DPCH RSCP":
                        {
                            DrawTable1Series(data.ciItem.dpchRscpArray);
                        }
                        break;
                    case "TD BLER":
                        {
                            DrawTable1Series(data.ciItem.blerArray);
                        }
                        break;
                    case "过覆盖指数":
                        {
                            DrawTable1Series(data.ciItem.coverArray);
                        }
                        break;
                    case "通信距离":
                        {
                            DrawTable1Series(data.ciItem.samplArray);
                        }
                        break;
                    case "DPCH C/I":
                        {
                            DrawTable1Series(data.ciItem.dpchc2iArray);
                        }
                        break;
                    default:
                        break;
                }
            }
            else
            {
                switch (this.cbbxSeries1.Text)
                {
                    case "PCCPCH RSCP":
                        {
                            DrawTable1Series(data.ciItem.rscpArray);
                        }
                        break;
                    case "平滑PCCPCH RSCP":
                        {
                            DrawTable1Series(data.ciItem.rscpNewArray);
                        }
                        break;
                    case "PCCPCH C/I":
                        {
                            DrawTable1Series(data.ciItem.pcchc2iArrayAll);
                        }
                        break;
                    case "PCCPCH Ec/Io":
                        {
                            DrawTable1Series(data.ciItem.pcchc2iArrayAll_EcIo);
                        }
                        break;
                    case "通信距离":
                        {
                            DrawTable1Series(data.ciItem.samplArray);
                        }
                        break;
                    default:
                        break;
                }
            }
            checkReverse();
            Cursor.Current = Cursors.Default;
        }
      
        private void cbbxSeries2_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (data == null)
            {
                return;
            }

            Cursor.Current = Cursors.WaitCursor;
            if (this.cbbxSeries1.Text == this.cbbxSeries2.Text)
            {
                if (chartControl1.Series.Count == 1)
                {
                    return;
                }
                Series series = chartControl1.Series[2];
                series.Points.Clear();
                checkReverse();
                return; //选同一个参数，不作比较画图
            }

            if (!ZTGsmAntenna.isScanStat)
            {
                switch (this.cbbxSeries2.Text)
                {
                    case "PCCPCH RSCP":
                        {
                            drawTable2Series(data.ciItem.rscpArray);
                        }
                        break;
                    case "DPCH RSCP":
                        {
                            drawTable2Series(data.ciItem.dpchRscpArray);
                        }
                        break;
                    case "TD BLER":
                        {
                            drawTable2Series(data.ciItem.blerArray);
                        }
                        break;
                    case "过覆盖指数":
                        {
                            drawTable2Series(data.ciItem.coverArray);
                        }
                        break;
                    case "通信距离":
                        {
                            drawTable2Series(data.ciItem.samplArray);
                        }
                        break;
                    case "DPCH C/I":
                        {
                            drawTable2Series(data.ciItem.dpchc2iArray);
                        }
                        break;
                    default:
                        break;
                }
            }
            else
            {
                switch (this.cbbxSeries2.Text)
                {
                    case "PCCPCH RSCP":
                        {
                            drawTable2Series(data.ciItem.rscpArray);
                        }
                        break;
                    case "平滑PCCPCH RSCP":
                        {
                            drawTable2Series(data.ciItem.rscpNewArray);
                        }
                        break;
                    case "PCCPCH C/I":
                        {
                            drawTable2Series(data.ciItem.pcchc2iArrayAll);
                        }
                        break;
                    case "PCCPCH Ec/Io":
                        {
                            drawTable2Series(data.ciItem.pcchc2iArrayAll_EcIo);
                        }
                        break;
                    case "通信距离":
                        {
                            drawTable2Series(data.ciItem.samplArray);
                        }
                        break;
                    default:
                        break;
                }
            }
            checkReverse();
            Cursor.Current = Cursors.Default;
        }

        /// <summary>
        /// 调整Y轴，按绝对值大小描写刻度
        /// </summary>
        private void checkReverse()
        {
            if ((double)(((XYDiagram)chartControl1.Diagram).AxisY.Range.MaxValue) > 0)
            {
                ((XYDiagram)chartControl1.Diagram).AxisY.Reverse = false;
            }
            else
            {
                ((XYDiagram)chartControl1.Diagram).AxisY.Reverse = true;
            }

            if ((double)(((XYDiagram)chartControl1.Diagram).SecondaryAxesY[0].Range.MaxValue) > 0)
            {
                ((XYDiagram)chartControl1.Diagram).SecondaryAxesY[0].Reverse = false;
            }
            else
            {
                ((XYDiagram)chartControl1.Diagram).SecondaryAxesY[0].Reverse = true;
            }
        }

        /// <summary>
        /// 按参数值1画图表
        /// </summary>
        /// <param name="seriesValue"></param>
        private void DrawTable1Series(int[] seriesValues)
        {
            int count = 0;
            SeriesPoint pt;
            if (chartControl1.Series.Count > 1)
            {
                chartControl1.Series.RemoveAt(1);
            }
            Series series = new Series();
            series.ShowInLegend = false;
            series.PointOptions.PointView = PointView.Values;
            SideBySideBarSeriesView sideBySideBarSeriesView = new SideBySideBarSeriesView();
            sideBySideBarSeriesView.BarWidth = 0.2;
            sideBySideBarSeriesView.Color = Color.Orange;
            sideBySideBarSeriesView.Border.Visible = false;
            series.View = sideBySideBarSeriesView;
            series.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
            series.Label.Visible = false;

            for (int i = 181; i < 360; i++)
            {
                string arg = (-179 + count).ToString();
                double value = Math.Round(data.ciItem.samplNumArray[i] == 0 ? 0 : seriesValues[i] * 1.0 / data.ciItem.samplNumArray[i], 2);
                pt = new SeriesPoint(arg, value);
                series.Points.Add(pt);
                count++;
            }
            for (int i = 1; i < 181; i++)
            {
                string arg = i.ToString();
                double value = Math.Round(data.ciItem.samplNumArray[i] == 0 ? 0 : seriesValues[i] * 1.0 / data.ciItem.samplNumArray[i], 2);
                pt = new SeriesPoint(arg, value);
                series.Points.Add(pt);
            }

            chartControl1.Series.Insert(1, series);
            ((XYDiagram)chartControl1.Diagram).AxisX.Range.MinValue = -179;
            ((XYDiagram)chartControl1.Diagram).AxisX.Range.MaxValue = 180;

            ((XYDiagram)chartControl1.Diagram).AxisY.Range.Auto = true;
            chartControl1.Focus();
        }

        /// <summary>
        /// 按参数值1画图表
        /// </summary>
        /// <param name="seriesValues"></param>
        private void DrawTable1Series(double[] seriesValues)
        {
            if (chartControl1.Series.Count > 0)
            {
                chartControl1.Series.RemoveAt(1);
            }

            Series series = new Series();
            series.ShowInLegend = false;
            series.PointOptions.PointView = PointView.Values;
            SideBySideBarSeriesView sideBySideBarSeriesView = new SideBySideBarSeriesView();
            sideBySideBarSeriesView.BarWidth = 0.2;
            sideBySideBarSeriesView.Color = Color.Orange;
            sideBySideBarSeriesView.Border.Visible = false;
            series.View = sideBySideBarSeriesView;
            series.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
            series.Label.Visible = false;
            int count = 0;
            for (int i = 181; i < 360; i++)
            {
                count++;
                series.Points.Add(new SeriesPoint((-179 + count).ToString(), Math.Round(data.ciItem.samplNumArray[i] == 0 ? 0 : 
                    seriesValues[i] / data.ciItem.samplNumArray[i], 2)));
            }
            for (int i = 1; i < 181; i++)
            {
                series.Points.Add(new SeriesPoint(i.ToString(), Math.Round(data.ciItem.samplNumArray[i] == 0 ? 0 : 
                    seriesValues[i] / data.ciItem.samplNumArray[i], 2)));
            }

            chartControl1.Series.Insert(1, series);
            ((XYDiagram)chartControl1.Diagram).AxisX.Range.MinValue = -179;
            ((XYDiagram)chartControl1.Diagram).AxisX.Range.MaxValue = 180;


            ((XYDiagram)chartControl1.Diagram).AxisY.Range.Auto = true;
            chartControl1.Focus();
        }

        /// <summary>
        /// 按参数值2画图表
        /// </summary>
        /// <param name="seriesValues"></param>
        private void drawTable2Series(int[] seriesValues)
        {
            int count = 0;
            if (chartControl1.Series.Count > 2)
            {
                chartControl1.Series.RemoveAt(2);
            }

            Series series = new Series();
            series.ShowInLegend = false;
            series.PointOptions.PointView = PointView.Values;
            LineSeriesView lineSeriesView = new LineSeriesView();
            lineSeriesView.Color = System.Drawing.Color.Blue;
            lineSeriesView.LineMarkerOptions.Size = 2;
            series.View = lineSeriesView;
            series.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
            series.Label.Visible = false;
            ((LineSeriesView)series.View).AxisY = ((XYDiagram)chartControl1.Diagram).SecondaryAxesY[0];

            for (int i = 181; i < 360; i++)
            {
                count++;
                series.Points.Add(new SeriesPoint((-179 + count).ToString(), Math.Round(data.ciItem.samplNumArray[i] == 0 ? 0 : 
                    seriesValues[i] * 1.0 / data.ciItem.samplNumArray[i], 2)));
            }
            for (int i = 0; i < 181; i++)
            {
                series.Points.Add(new SeriesPoint(i.ToString(), Math.Round(data.ciItem.samplNumArray[i] == 0 ? 0 : 
                    seriesValues[i] * 1.0 / data.ciItem.samplNumArray[i], 2)));
            }

            chartControl1.Series.Insert(2, series);
            ((XYDiagram)chartControl1.Diagram).AxisX.Range.MinValue = -179;
            ((XYDiagram)chartControl1.Diagram).AxisX.Range.MaxValue = 180;

            ((LineSeriesView)series.View).AxisY.Range.Auto = true;

            chartControl1.Focus();
        }

        /// <summary>
        /// 按参数值2画图表
        /// </summary>
        /// <param name="seriesValues"></param>
        private void drawTable2Series(double[] seriesValues)
        {
            int count = 0;

            if (chartControl1.Series.Count > 2)
            {
                chartControl1.Series.RemoveAt(2);
            }

            Series series = new Series();
            series.ShowInLegend = false;
            series.PointOptions.PointView = PointView.Values;
            LineSeriesView lineSeriesView = new LineSeriesView();
            lineSeriesView.Color = System.Drawing.Color.Blue;
            lineSeriesView.LineMarkerOptions.Size = 2;
            series.View = lineSeriesView;
            series.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
            series.Label.Visible = false;
            ((LineSeriesView)series.View).AxisY = ((XYDiagram)chartControl1.Diagram).SecondaryAxesY[0];

            for (int i = 181; i < 360; i++)
            {
                count++;
                series.Points.Add(new SeriesPoint((-179 + count).ToString(), Math.Round(data.ciItem.samplNumArray[i] == 0 ? 0 : 
                    seriesValues[i] / data.ciItem.samplNumArray[i], 2)));
            }
            for (int i = 0; i < 181; i++)
            {
                series.Points.Add(new SeriesPoint(i.ToString(), Math.Round(data.ciItem.samplNumArray[i] == 0 ? 0 : 
                    seriesValues[i] / data.ciItem.samplNumArray[i], 2)));
            }

            chartControl1.Series.Insert(2, series);

            ((XYDiagram)chartControl1.Diagram).AxisX.Range.MinValue = -179;
            ((XYDiagram)chartControl1.Diagram).AxisX.Range.MaxValue = 180;
            ((LineSeriesView)series.View).AxisY.Range.Auto = true;

            chartControl1.Focus();
        }

        private void xTabPageData_SelectedPageChanged(object sender, DevExpress.XtraTab.TabPageChangedEventArgs e)
        {
            if (xTabPageData.SelectedTabPageIndex == 0)
            {
                miShwoChart.Visible = true;
                miShowGis.Visible = true;
                miShowSimulation.Visible = true;
                miExportWholeExcel.Visible = true;
            }
            else if (xTabPageData.SelectedTabPageIndex == 1)
            {
                miShwoChart.Visible = true;
                miShowGis.Visible = false;
                miShowSimulation.Visible = false;
                miExportWholeExcel.Visible = false;
            }
            else if (xTabPageData.SelectedTabPageIndex == 2)
            {
                miShwoChart.Visible = true;
                miShowGis.Visible = true;
                miShowSimulation.Visible = true;
                miExportWholeExcel.Visible = false;
            }
            else if (xTabPageData.SelectedTabPageIndex == 3 || xTabPageData.SelectedTabPageIndex == 4)
            {
                miShwoChart.Visible = false;
                miShowGis.Visible = false;
                miShowSimulation.Visible = false;
                miExportWholeExcel.Visible = false;
            }
        }

        private void btnGo_Click(object sender, EventArgs e)
        {
            int iPage = 0;
            if (!int.TryParse(txtPage.Text.ToString(), out iPage))
            {
                iPage = 0;
            }
            iPage -= 1;
            int iCount = iDataNum;
            iCount = iCount % 200 > 0 ? iCount / 200 + 1 : iCount / 200;
            if (iPage < 0)
                iPage = 0;
            else if (iPage > iCount - 1)
                iPage = iCount - 1;
            txtPage.Text = (iPage + 1).ToString();
            FillData(iPage);
        }

        private void btnPrevpage_Click(object sender, EventArgs e)
        {
            int iPage = 0;
            if (!int.TryParse(txtPage.Text.ToString(), out iPage))
            {
                iPage = 0;
            }
            iPage -= 1;
            iPage = iPage - 1 >= 0 ? iPage - 1 : iPage;
            txtPage.Text = (iPage + 1).ToString();
            FillData(iPage);
        }

        private void btnNextpage_Click(object sender, EventArgs e)
        {
            int iPage = 0;
            if (!int.TryParse(txtPage.Text.ToString(), out iPage))
            {
                iPage = 0;
            }
            iPage -= 1;
            int iCount = iDataNum;
            iCount = iCount % 200 > 0 ? iCount / 200 + 1 : iCount / 200;

            iPage = iPage + 1 >= iCount ? iPage : iPage + 1;
            txtPage.Text = (iPage + 1).ToString();
            FillData(iPage);
        }

        private void btnSearch_Click(object sender, EventArgs e)
        {
            string strCellName = txtCellName.Text;
            FillData(strCellName);
        }
    }
}
