﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class WeakQualReasonSetForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.comboBoxEdit12 = new DevExpress.XtraEditors.ComboBoxEdit();
            this.comboBoxEdit11 = new DevExpress.XtraEditors.ComboBoxEdit();
            this.comboBoxEdit10 = new DevExpress.XtraEditors.ComboBoxEdit();
            this.comboBoxEdit9 = new DevExpress.XtraEditors.ComboBoxEdit();
            this.comboBoxEdit8 = new DevExpress.XtraEditors.ComboBoxEdit();
            this.comboBoxEdit7 = new DevExpress.XtraEditors.ComboBoxEdit();
            this.comboBoxEdit6 = new DevExpress.XtraEditors.ComboBoxEdit();
            this.comboBoxEdit5 = new DevExpress.XtraEditors.ComboBoxEdit();
            this.comboBoxEdit4 = new DevExpress.XtraEditors.ComboBoxEdit();
            this.comboBoxEdit3 = new DevExpress.XtraEditors.ComboBoxEdit();
            this.comboBoxEdit2 = new DevExpress.XtraEditors.ComboBoxEdit();
            this.comboBoxEdit1 = new DevExpress.XtraEditors.ComboBoxEdit();
            this.toolStrip1 = new System.Windows.Forms.ToolStrip();
            this.toolStripButtonSetting = new System.Windows.Forms.ToolStripLabel();
            this.simpleButtonOK = new DevExpress.XtraEditors.SimpleButton();
            this.simpleButtonCancel = new DevExpress.XtraEditors.SimpleButton();
            this.toolStripDropDownSetting = new System.Windows.Forms.ToolStripDropDown();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.comboBoxEdit12.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.comboBoxEdit11.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.comboBoxEdit10.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.comboBoxEdit9.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.comboBoxEdit8.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.comboBoxEdit7.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.comboBoxEdit6.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.comboBoxEdit5.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.comboBoxEdit4.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.comboBoxEdit3.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.comboBoxEdit2.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.comboBoxEdit1.Properties)).BeginInit();
            this.toolStrip1.SuspendLayout();
            this.SuspendLayout();
            // 
            // groupControl1
            // 
            this.groupControl1.Controls.Add(this.comboBoxEdit12);
            this.groupControl1.Controls.Add(this.comboBoxEdit11);
            this.groupControl1.Controls.Add(this.comboBoxEdit10);
            this.groupControl1.Controls.Add(this.comboBoxEdit9);
            this.groupControl1.Controls.Add(this.comboBoxEdit8);
            this.groupControl1.Controls.Add(this.comboBoxEdit7);
            this.groupControl1.Controls.Add(this.comboBoxEdit6);
            this.groupControl1.Controls.Add(this.comboBoxEdit5);
            this.groupControl1.Controls.Add(this.comboBoxEdit4);
            this.groupControl1.Controls.Add(this.comboBoxEdit3);
            this.groupControl1.Controls.Add(this.comboBoxEdit2);
            this.groupControl1.Controls.Add(this.comboBoxEdit1);
            this.groupControl1.Location = new System.Drawing.Point(4, 28);
            this.groupControl1.Name = "groupControl1";
            this.groupControl1.Size = new System.Drawing.Size(200, 339);
            this.groupControl1.TabIndex = 0;
            this.groupControl1.Text = "处理清单";
            // 
            // comboBoxEdit12
            // 
            this.comboBoxEdit12.EditValue = "覆盖杂乱";
            this.comboBoxEdit12.Location = new System.Drawing.Point(5, 312);
            this.comboBoxEdit12.Name = "comboBoxEdit12";
            this.comboBoxEdit12.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.comboBoxEdit12.Properties.Items.AddRange(new object[] {
            "扫频弱覆盖",
            "室分外泄",
            "过覆盖",
            "背向覆盖",
            "道路结构指数",
            "重选过慢",
            "切换不合理",
            "切换不及时",
            "质量毛刺",
            "频率干扰或故障",
            "路测弱覆盖",
            "覆盖杂乱"});
            this.comboBoxEdit12.Size = new System.Drawing.Size(188, 21);
            this.comboBoxEdit12.TabIndex = 1;
            // 
            // comboBoxEdit11
            // 
            this.comboBoxEdit11.EditValue = "路测弱覆盖";
            this.comboBoxEdit11.Location = new System.Drawing.Point(5, 286);
            this.comboBoxEdit11.Name = "comboBoxEdit11";
            this.comboBoxEdit11.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.comboBoxEdit11.Properties.Items.AddRange(new object[] {
            "扫频弱覆盖",
            "室分外泄",
            "过覆盖",
            "背向覆盖",
            "道路结构指数",
            "重选过慢",
            "切换不合理",
            "切换不及时",
            "质量毛刺",
            "频率干扰或故障",
            "路测弱覆盖",
            "覆盖杂乱"});
            this.comboBoxEdit11.Size = new System.Drawing.Size(188, 21);
            this.comboBoxEdit11.TabIndex = 1;
            // 
            // comboBoxEdit10
            // 
            this.comboBoxEdit10.EditValue = "频率干扰或故障";
            this.comboBoxEdit10.Location = new System.Drawing.Point(5, 260);
            this.comboBoxEdit10.Name = "comboBoxEdit10";
            this.comboBoxEdit10.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.comboBoxEdit10.Properties.Items.AddRange(new object[] {
            "扫频弱覆盖",
            "室分外泄",
            "过覆盖",
            "背向覆盖",
            "道路结构指数",
            "重选过慢",
            "切换不合理",
            "切换不及时",
            "质量毛刺",
            "频率干扰或故障",
            "路测弱覆盖",
            "覆盖杂乱"});
            this.comboBoxEdit10.Size = new System.Drawing.Size(188, 21);
            this.comboBoxEdit10.TabIndex = 1;
            // 
            // comboBoxEdit9
            // 
            this.comboBoxEdit9.EditValue = "质量毛刺";
            this.comboBoxEdit9.Location = new System.Drawing.Point(5, 234);
            this.comboBoxEdit9.Name = "comboBoxEdit9";
            this.comboBoxEdit9.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.comboBoxEdit9.Properties.Items.AddRange(new object[] {
            "扫频弱覆盖",
            "室分外泄",
            "过覆盖",
            "背向覆盖",
            "道路结构指数",
            "重选过慢",
            "切换不合理",
            "切换不及时",
            "质量毛刺",
            "频率干扰或故障",
            "路测弱覆盖",
            "覆盖杂乱"});
            this.comboBoxEdit9.Size = new System.Drawing.Size(188, 21);
            this.comboBoxEdit9.TabIndex = 1;
            // 
            // comboBoxEdit8
            // 
            this.comboBoxEdit8.EditValue = "切换不及时";
            this.comboBoxEdit8.Location = new System.Drawing.Point(5, 208);
            this.comboBoxEdit8.Name = "comboBoxEdit8";
            this.comboBoxEdit8.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.comboBoxEdit8.Properties.Items.AddRange(new object[] {
            "扫频弱覆盖",
            "室分外泄",
            "过覆盖",
            "背向覆盖",
            "道路结构指数",
            "重选过慢",
            "切换不合理",
            "切换不及时",
            "质量毛刺",
            "频率干扰或故障",
            "路测弱覆盖",
            "覆盖杂乱"});
            this.comboBoxEdit8.Size = new System.Drawing.Size(188, 21);
            this.comboBoxEdit8.TabIndex = 1;
            // 
            // comboBoxEdit7
            // 
            this.comboBoxEdit7.EditValue = "切换不合理";
            this.comboBoxEdit7.Location = new System.Drawing.Point(5, 182);
            this.comboBoxEdit7.Name = "comboBoxEdit7";
            this.comboBoxEdit7.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.comboBoxEdit7.Properties.Items.AddRange(new object[] {
            "扫频弱覆盖",
            "室分外泄",
            "过覆盖",
            "背向覆盖",
            "道路结构指数",
            "重选过慢",
            "切换不合理",
            "切换不及时",
            "质量毛刺",
            "频率干扰或故障",
            "路测弱覆盖",
            "覆盖杂乱"});
            this.comboBoxEdit7.Size = new System.Drawing.Size(188, 21);
            this.comboBoxEdit7.TabIndex = 1;
            // 
            // comboBoxEdit6
            // 
            this.comboBoxEdit6.EditValue = "重选过慢";
            this.comboBoxEdit6.Location = new System.Drawing.Point(5, 156);
            this.comboBoxEdit6.Name = "comboBoxEdit6";
            this.comboBoxEdit6.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.comboBoxEdit6.Properties.Items.AddRange(new object[] {
            "扫频弱覆盖",
            "室分外泄",
            "过覆盖",
            "背向覆盖",
            "道路结构指数",
            "重选过慢",
            "切换不合理",
            "切换不及时",
            "质量毛刺",
            "频率干扰或故障",
            "路测弱覆盖",
            "覆盖杂乱"});
            this.comboBoxEdit6.Size = new System.Drawing.Size(188, 21);
            this.comboBoxEdit6.TabIndex = 1;
            // 
            // comboBoxEdit5
            // 
            this.comboBoxEdit5.EditValue = "道路结构指数";
            this.comboBoxEdit5.Location = new System.Drawing.Point(5, 130);
            this.comboBoxEdit5.Name = "comboBoxEdit5";
            this.comboBoxEdit5.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.comboBoxEdit5.Properties.Items.AddRange(new object[] {
            "扫频弱覆盖",
            "室分外泄",
            "过覆盖",
            "背向覆盖",
            "道路结构指数",
            "重选过慢",
            "切换不合理",
            "切换不及时",
            "质量毛刺",
            "频率干扰或故障",
            "路测弱覆盖",
            "覆盖杂乱"});
            this.comboBoxEdit5.Size = new System.Drawing.Size(188, 21);
            this.comboBoxEdit5.TabIndex = 1;
            // 
            // comboBoxEdit4
            // 
            this.comboBoxEdit4.EditValue = "背向覆盖";
            this.comboBoxEdit4.Location = new System.Drawing.Point(5, 104);
            this.comboBoxEdit4.Name = "comboBoxEdit4";
            this.comboBoxEdit4.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.comboBoxEdit4.Properties.Items.AddRange(new object[] {
            "扫频弱覆盖",
            "室分外泄",
            "过覆盖",
            "背向覆盖",
            "道路结构指数",
            "重选过慢",
            "切换不合理",
            "切换不及时",
            "质量毛刺",
            "频率干扰或故障",
            "路测弱覆盖",
            "覆盖杂乱"});
            this.comboBoxEdit4.Size = new System.Drawing.Size(188, 21);
            this.comboBoxEdit4.TabIndex = 1;
            // 
            // comboBoxEdit3
            // 
            this.comboBoxEdit3.EditValue = "过覆盖";
            this.comboBoxEdit3.Location = new System.Drawing.Point(5, 78);
            this.comboBoxEdit3.Name = "comboBoxEdit3";
            this.comboBoxEdit3.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.comboBoxEdit3.Properties.Items.AddRange(new object[] {
            "扫频弱覆盖",
            "室分外泄",
            "过覆盖",
            "背向覆盖",
            "道路结构指数",
            "重选过慢",
            "切换不合理",
            "切换不及时",
            "质量毛刺",
            "频率干扰或故障",
            "路测弱覆盖",
            "覆盖杂乱"});
            this.comboBoxEdit3.Size = new System.Drawing.Size(188, 21);
            this.comboBoxEdit3.TabIndex = 1;
            // 
            // comboBoxEdit2
            // 
            this.comboBoxEdit2.EditValue = "室分外泄";
            this.comboBoxEdit2.Location = new System.Drawing.Point(5, 52);
            this.comboBoxEdit2.Name = "comboBoxEdit2";
            this.comboBoxEdit2.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.comboBoxEdit2.Properties.Items.AddRange(new object[] {
            "扫频弱覆盖",
            "室分外泄",
            "过覆盖",
            "背向覆盖",
            "道路结构指数",
            "重选过慢",
            "切换不合理",
            "切换不及时",
            "质量毛刺",
            "频率干扰或故障",
            "路测弱覆盖",
            "覆盖杂乱"});
            this.comboBoxEdit2.Size = new System.Drawing.Size(188, 21);
            this.comboBoxEdit2.TabIndex = 1;
            // 
            // comboBoxEdit1
            // 
            this.comboBoxEdit1.EditValue = "扫频弱覆盖";
            this.comboBoxEdit1.Location = new System.Drawing.Point(5, 26);
            this.comboBoxEdit1.Name = "comboBoxEdit1";
            this.comboBoxEdit1.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.comboBoxEdit1.Properties.Items.AddRange(new object[] {
            "扫频弱覆盖",
            "室分外泄",
            "过覆盖",
            "背向覆盖",
            "道路结构指数",
            "重选过慢",
            "切换不合理",
            "切换不及时",
            "质量毛刺",
            "频率干扰或故障",
            "路测弱覆盖",
            "覆盖杂乱"});
            this.comboBoxEdit1.Size = new System.Drawing.Size(188, 21);
            this.comboBoxEdit1.TabIndex = 1;
            // 
            // toolStrip1
            // 
            this.toolStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.toolStripButtonSetting});
            this.toolStrip1.Location = new System.Drawing.Point(0, 0);
            this.toolStrip1.Name = "toolStrip1";
            this.toolStrip1.Size = new System.Drawing.Size(208, 25);
            this.toolStrip1.TabIndex = 1;
            this.toolStrip1.Text = "toolStrip1";
            // 
            // toolStripButtonSetting
            // 
            this.toolStripButtonSetting.Image = global::MasterCom.RAMS.Properties.Resources.open;
            this.toolStripButtonSetting.Name = "toolStripButtonSetting";
            this.toolStripButtonSetting.Size = new System.Drawing.Size(96, 22);
            this.toolStripButtonSetting.Text = "各项详细设置";
            this.toolStripButtonSetting.Click += new System.EventHandler(this.toolStripButtonSetting_Click);
            // 
            // simpleButtonOK
            // 
            this.simpleButtonOK.Location = new System.Drawing.Point(30, 374);
            this.simpleButtonOK.Name = "simpleButtonOK";
            this.simpleButtonOK.Size = new System.Drawing.Size(75, 23);
            this.simpleButtonOK.TabIndex = 2;
            this.simpleButtonOK.Text = "确定";
            this.simpleButtonOK.Click += new System.EventHandler(this.simpleButtonOK_Click);
            // 
            // simpleButtonCancel
            // 
            this.simpleButtonCancel.Location = new System.Drawing.Point(121, 374);
            this.simpleButtonCancel.Name = "simpleButtonCancel";
            this.simpleButtonCancel.Size = new System.Drawing.Size(75, 23);
            this.simpleButtonCancel.TabIndex = 2;
            this.simpleButtonCancel.Text = "取消";
            this.simpleButtonCancel.Click += new System.EventHandler(this.simpleButtonCancel_Click);
            // 
            // toolStripDropDownSetting
            // 
            this.toolStripDropDownSetting.LayoutStyle = System.Windows.Forms.ToolStripLayoutStyle.Flow;
            this.toolStripDropDownSetting.Name = "toolStripDropDown1";
            this.toolStripDropDownSetting.Size = new System.Drawing.Size(2, 4);
            // 
            // WeakQualReasonSetForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(208, 403);
            this.Controls.Add(this.toolStrip1);
            this.Controls.Add(this.simpleButtonCancel);
            this.Controls.Add(this.groupControl1);
            this.Controls.Add(this.simpleButtonOK);
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "WeakQualReasonSetForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "质差设置";
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.comboBoxEdit12.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.comboBoxEdit11.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.comboBoxEdit10.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.comboBoxEdit9.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.comboBoxEdit8.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.comboBoxEdit7.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.comboBoxEdit6.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.comboBoxEdit5.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.comboBoxEdit4.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.comboBoxEdit3.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.comboBoxEdit2.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.comboBoxEdit1.Properties)).EndInit();
            this.toolStrip1.ResumeLayout(false);
            this.toolStrip1.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraEditors.GroupControl groupControl1;
        private DevExpress.XtraEditors.ComboBoxEdit comboBoxEdit1;
        private DevExpress.XtraEditors.ComboBoxEdit comboBoxEdit10;
        private DevExpress.XtraEditors.ComboBoxEdit comboBoxEdit9;
        private DevExpress.XtraEditors.ComboBoxEdit comboBoxEdit8;
        private DevExpress.XtraEditors.ComboBoxEdit comboBoxEdit7;
        private DevExpress.XtraEditors.ComboBoxEdit comboBoxEdit6;
        private DevExpress.XtraEditors.ComboBoxEdit comboBoxEdit5;
        private DevExpress.XtraEditors.ComboBoxEdit comboBoxEdit4;
        private DevExpress.XtraEditors.ComboBoxEdit comboBoxEdit3;
        private DevExpress.XtraEditors.ComboBoxEdit comboBoxEdit2;
        private System.Windows.Forms.ToolStrip toolStrip1;
        private System.Windows.Forms.ToolStripLabel toolStripButtonSetting;
        private DevExpress.XtraEditors.SimpleButton simpleButtonOK;
        private DevExpress.XtraEditors.SimpleButton simpleButtonCancel;
        private System.Windows.Forms.ToolStripDropDown toolStripDropDownSetting;
        private DevExpress.XtraEditors.ComboBoxEdit comboBoxEdit12;
        private DevExpress.XtraEditors.ComboBoxEdit comboBoxEdit11;



    }
}