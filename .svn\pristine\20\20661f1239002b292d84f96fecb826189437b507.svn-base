﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using System.IO;
using System.Drawing.Imaging;

using DevExpress.XtraTab;
using DevExpress.XtraCharts;
using DevExpress.XtraGrid;
using DevExpress.XtraGrid.Views.Grid;
using MapWinGIS;

using MasterCom.Util;
using MasterCom.MControls;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.ZTFunc;
using MasterCom.MTGis;
using MasterCom.RAMS.Func.ScanGridAnaExporter;
using MasterCom.Util.UiEx;
using System.Xml;

namespace MasterCom.RAMS.Func
{
    public partial class ScanGridMultiCoverageResultForm : MinCloseForm
    {
        public ScanGridMultiCoverageResultForm(MainModel mainModel) : base(mainModel)
        {
            InitializeComponent();
            btnColoring.Click += BtnColoring_Click;
            miExportWord.Click += MiBtnExportWord_Click;
            miBtnExportDetail.Click += MiBtnExportDetail_Click;
            miBtnExportExcel.Click += MiBtnExportExcel_Click;
            miExportShp.Click += MiExportShp_Click;
            cbxNetType.SelectedIndexChanged += CbxNetType_SelectedChanged;
            cbxCoverageType.SelectedIndexChanged += CbxCoverageType_SelectedChanged;
            cbxGis.SelectedIndexChanged += CbxGis_SelectedChanged;
            xtraTabControl.SelectedPageChanged += TabControl_SelectedPageChanged;
            this.Load += Form_Load;
        }

        public void FillData(ScanGridAnaResult statResult, ScanGridAnaResult cmpResult, int serviceType, List<TimePeriod> timePeriods)
        {
            this.anaResult = statResult;
            this.cmpResult = cmpResult;
            this.serviceType = serviceType;
            this.timePeriods = timePeriods;

            colorRanger = ScanGridAnaColorRanger.Instance;
            colorRanger.CurCoverageType = cbxCoverageType.SelectedIndex;
            widget = new ScanGridAnaWidget(xtraTabControl, anaResult, cmpResult);
            giser = new ScanGridAnaGisColoring(mModel, anaResult, cmpResult);
        }

        private void Form_Load(object sender, EventArgs e)
        {
            miExportShp.Visible = false;
            xtraTabControl.TabPages[4].PageVisible = cmpResult != null;

            cbxNetType.Properties.Items.Clear();
            if (serviceType == 12 || serviceType == 1212)
            {
                cbxNetType.Properties.Items.Add(ScanGridAnaGridType.GSM900);
                cbxNetType.Properties.Items.Add(ScanGridAnaGridType.DCS1800);
                cbxNetType.Properties.Items.Add(ScanGridAnaGridType.最强信号);
            }
            else
            {
                cbxNetType.Properties.Items.Add(ScanGridAnaGridType.TD);
            }
            cbxNetType.SelectedIndex = 0;

            cbxGis.Properties.Items.Clear();
            if (cmpResult != null)
            {
                cbxGis.Properties.Items.Add("时间段对比");
            }
            cbxGis.Properties.Items.Add("按覆盖度");
            cbxGis.Properties.Items.Add("按场强值");
            cbxGis.Properties.Items.Add("弱覆盖区域");
            cbxGis.Properties.Items.Add("高重叠度区域");
            cbxGis.SelectedIndex = 0;
        }

        private void BtnColoring_Click(object sender, EventArgs e)
        {
            if (colorRanger.CurRangeType == ScanGridAnaRangeType.Compare)
            {
                ScanGridCompareColorSettingForm form = new ScanGridCompareColorSettingForm(colorRanger.GetColorRanges());
                if (form.ShowDialog() != DialogResult.OK)
                {
                    return;
                }
                List<ColorRange> retList = form.ColorRanges;
                colorRanger.SetColorRanges(retList);
                saveColorRanges("Compare", form.ColorRanges);
            }
            else if (colorRanger.CurRangeType == ScanGridAnaRangeType.Coverage)
            {
                MultiCvrColorDlg mngDlg = new MultiCvrColorDlg();
                mngDlg.FixMinMax(colorRanger.MinRangeValue, colorRanger.MaxRangeValue);
                mngDlg.MakeRangeModeOnly();
                mngDlg.FillColorRanges(colorRanger.GetColorRanges());
                mngDlg.InvalidatePointColor = colorRanger.InvalidateColor;
                if (DialogResult.OK != mngDlg.ShowDialog(this))
                {
                    return;
                }
                colorRanger.SetColorRanges(mngDlg.ColorRanges);
                colorRanger.InvalidateColor = mngDlg.InvalidatePointColor;
                saveColorRanges("Coverage", mngDlg.ColorRanges);
            }
            else if (colorRanger.CurRangeType == ScanGridAnaRangeType.Rxlev)
            {
                MasterCom.Grid.ColorRangeMngDlg mngDlg = new MasterCom.Grid.ColorRangeMngDlg();
                mngDlg.FixMinMax(colorRanger.MinRangeValue, colorRanger.MaxRangeValue);
                mngDlg.MakeRangeModeOnly();
                mngDlg.FillColorRanges(colorRanger.GetColorRanges());
                if (DialogResult.OK != mngDlg.ShowDialog(this))
                {
                    return;
                }
                colorRanger.SetColorRanges(mngDlg.ColorRanges);
                saveColorRanges("Rxlev", mngDlg.ColorRanges);
            }
            else
            {
                return;
            }

            ScanGridAnaGridType netType = (ScanGridAnaGridType)Enum.Parse(typeof(ScanGridAnaGridType), cbxNetType.SelectedItem.ToString(), true);
            widget.ColorChanged(colorRanger.CurRangeType, netType, cbxCoverageType.SelectedIndex);
            giser.Refresh(netType);
        }

        private void saveColorRanges(string type,List<ColorRange> ranges)
        {
            XmlConfigFile configFile = new XmlConfigFile(ScanGridAnaSettingCondition.ConfigPath);
            XmlElement cfgColorRange = configFile.GetConfig(string.Format("{0}ColorRange", type));
            if (cfgColorRange != null)
            {
                cfgColorRange.RemoveAll();
                cfgColorRange.SetAttribute("name", string.Format("{0}ColorRange", type));
            }
            else
            {
                cfgColorRange = configFile.AddConfig(string.Format("{0}ColorRange", type));
            }

            foreach (ColorRange range in ranges)
            {
                configFile.AddItem(cfgColorRange, range.desInfo,
                    string.Format("{0},{1},{2}", range.minValue, range.maxValue, range.color.ToArgb()));
            }
            configFile.Save();
        }

        private void MiBtnExportWord_Click(object sender, EventArgs e)
        {
            SaveFileDialog dlg = new SaveFileDialog();
            dlg.Filter = FilterHelper.Word;
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return;
            }

            ScanGridAnaWordExportForm form = new ScanGridAnaWordExportForm();
            form.SavePath = dlg.FileName;
            form.MainModel = mModel;
            form.AnaResult = anaResult;
            form.CmpResult = cmpResult;
            WaitTextBox.Show(this, form.Export);
            form.Dispose();

            colorRanger.CurCoverageType = cbxCoverageType.SelectedIndex;
            CbxGis_SelectedChanged(this, new EventArgs());
        }

        private void MiBtnExportDetail_Click(object sender, EventArgs e)
        {
            List<NPOIRow> content = new List<NPOIRow>();
            NPOIRow titleRow = new NPOIRow();
            titleRow.AddCellValue("网格名称");
            titleRow.AddCellValue("数据源");
            titleRow.AddCellValue("栅格名称");
            titleRow.AddCellValue("最强信号电平");
            titleRow.AddCellValue("最强信号频点");
            titleRow.AddCellValue("网络重叠覆盖度");
            titleRow.AddCellValue("网络强信号数");
            titleRow.AddCellValue("小区名");
            titleRow.AddCellValue("频点_扰码");
            content.Add(titleRow);

            foreach (ScanGridAnaRegionInfo region in anaResult.RegionList)
            {
                foreach (ScanGridAnaGridInfo grid in region.GridList)
                {
                    NPOIRow row = new NPOIRow();
                    row.AddCellValue(region.RegionName);
                    row.AddCellValue(grid.LastTime.ToString("yyyy_MM"));
                    row.AddCellValue(grid.MGRSGridString);
                    row.AddCellValue(grid.MaxRxlev);
                    row.AddCellValue(grid.MaxBcch);
                    row.AddCellValue(grid.RelLevel);
                    row.AddCellValue(grid.StrongRxlevCount);
                    foreach (ScanGridAnaCellInfo cell in grid.CellInfoDic.Values)
                    {
                        row.AddCellValue(cell.CellName);
                        row.AddCellValue(cell.Bcch + "_" + cell.Bsic);
                    }
                    content.Add(row);
                }
            }

            ExcelNPOIManager.ExportToExcel(content);
        }

        private void MiBtnExportExcel_Click(object sender, EventArgs e)
        {
            XtraTabPage curPage = xtraTabControl.SelectedTabPage;
            if (typeof(List<ScanGridAnaGridBlockLevelOne>) == curPage.Tag.GetType())
            {
                List<ScanGridAnaGridBlockLevelOne> levelOne = curPage.Tag as List<ScanGridAnaGridBlockLevelOne>;
                List<NPOIRow> content = new List<NPOIRow>();
                #region foreach add content
                NPOIRow titleRow = new NPOIRow();
                titleRow.AddCellValue("区域编号");
                titleRow.AddCellValue("栅格个数");
                titleRow.AddCellValue("中心经度");
                titleRow.AddCellValue("中心纬度");
                titleRow.AddCellValue("道路名称");
                titleRow.AddCellValue("栅格编号");
                titleRow.AddCellValue("小区个数");
                titleRow.AddCellValue("最强信号");
                titleRow.AddCellValue("最强BCCH");
                titleRow.AddCellValue("最强BSIC");
                titleRow.AddCellValue("相对覆盖度");
                titleRow.AddCellValue("绝对覆盖度");
                titleRow.AddCellValue("综合覆盖度");
                titleRow.AddCellValue("采样点个数");
                titleRow.AddCellValue("中心经度");
                titleRow.AddCellValue("中心纬度");
                titleRow.AddCellValue("小区名");
                titleRow.AddCellValue("频点");
                titleRow.AddCellValue("扰码");
                titleRow.AddCellValue("采样点数");
                titleRow.AddCellValue("经度");
                titleRow.AddCellValue("纬度");
                content.Add(titleRow);
                addContent(levelOne, content);
                #endregion
                ExcelNPOIManager.ExportToExcel(new List<List<NPOIRow>>() { content }, new List<string>() { curPage.Text });
            }
            else if (curPage.Tag.GetType() == typeof(GridView[]))
            {
                GridView[] gvs = curPage.Tag as GridView[];
                if (gvs.Length == 1)
                {
                    ExcelNPOIManager.ExportToExcel(new List<GridView>(gvs), new List<string>() { curPage.Text });
                }
                else if (gvs.Length == 2)
                {
                    ExcelNPOIManager.ExportToExcel(
                    new List<GridView>(gvs),
                    new List<string>()
                        { string.Format("{0}_{1}", "当前时间段", curPage.Text),
                          string.Format("{0}_{1}", "对比时间段", curPage.Text),
                        });
                }
            }
        }

        private void addContent(List<ScanGridAnaGridBlockLevelOne> levelOne, List<NPOIRow> content)
        {
            foreach (ScanGridAnaGridBlockLevelOne one in levelOne)
            {
                NPOIRow partOne = new NPOIRow();
                partOne.AddCellValue(one.ID);
                partOne.AddCellValue(one.GridCount);
                partOne.AddCellValue(one.CentLng);
                partOne.AddCellValue(one.CentLat);
                partOne.AddCellValue(one.RoadName);
                foreach (ScanGridAnaGridBlockLevelTwo two in one.LevelTwoList)
                {
                    NPOIRow partTwo = new NPOIRow();
                    partTwo.cellValues.AddRange(partOne.cellValues);
                    partTwo.AddCellValue(two.MgrsString);
                    partTwo.AddCellValue(two.CellCount);
                    partTwo.AddCellValue(two.MaxRxlev);
                    partTwo.AddCellValue(two.MaxBcch);
                    partTwo.AddCellValue(two.MaxBsic);
                    partTwo.AddCellValue(two.RelLevel);
                    partTwo.AddCellValue(two.AbsLevel);
                    partTwo.AddCellValue(two.RelAndAbsLevel);
                    partTwo.AddCellValue(two.TestPointCount);
                    partTwo.AddCellValue(two.CentLng);
                    partTwo.AddCellValue(two.CentLat);
                    foreach (ScanGridAnaGridBlockLevelThree three in two.LevelThreeList)
                    {
                        NPOIRow partThree = new NPOIRow();
                        partThree.cellValues.AddRange(partTwo.cellValues);
                        partThree.AddCellValue(three.CellName);
                        partThree.AddCellValue(three.Bcch);
                        partThree.AddCellValue(three.Bsic);
                        partThree.AddCellValue(three.TestPointCount);
                        partThree.AddCellValue(three.Longitude);
                        partThree.AddCellValue(three.Latitude);
                        content.Add(partThree);
                    }
                    if (two.LevelThreeList.Count == 0)
                    {
                        content.Add(partTwo);
                    }
                }
            }
        }

        private void CbxNetType_SelectedChanged(object sender, EventArgs e)
        {
            ScanGridAnaGridType netType = (ScanGridAnaGridType)Enum.Parse(typeof(ScanGridAnaGridType), cbxNetType.SelectedItem.ToString(), true);
            widget.NetTypeChanged(netType, cbxCoverageType.SelectedIndex);
            giser.Refresh(netType);
        }

        private void CbxCoverageType_SelectedChanged(object sender, EventArgs e)
        {
            ScanGridAnaGridType netType = (ScanGridAnaGridType)Enum.Parse(typeof(ScanGridAnaGridType), cbxNetType.SelectedItem.ToString(), true);
            widget.CovTypeChanged(netType, cbxCoverageType.SelectedIndex);
            colorRanger.CurCoverageType = cbxCoverageType.SelectedIndex;
            giser.Refresh(netType);
        }

        private void CbxGis_SelectedChanged(object sender, EventArgs e)
        {
            switch (cbxGis.SelectedItem.ToString())
            {
                case "时间段对比":
                    colorRanger.CurRangeType = ScanGridAnaRangeType.Compare;
                    break;
                case "按覆盖度":
                    colorRanger.CurRangeType = ScanGridAnaRangeType.Coverage;
                    break;
                case "按场强值":
                    colorRanger.CurRangeType = ScanGridAnaRangeType.Rxlev;
                    break;
                case "弱覆盖区域":
                    colorRanger.CurRangeType = ScanGridAnaRangeType.WeakRxlev;
                    break;
                case "高重叠度区域":
                    colorRanger.CurRangeType = ScanGridAnaRangeType.HighCoverage;
                    break;
            }
            ScanGridAnaGridType netType = (ScanGridAnaGridType)Enum.Parse(typeof(ScanGridAnaGridType), cbxNetType.SelectedItem.ToString(), true);
            giser.Refresh(netType);
        }

        private void GridView_DoubleClick(object sender, EventArgs e)
        {
            GridView gv = sender as GridView;
            if (gv.SelectedRowsCount < 1)
            {
                return;
            }

            object objRow = gv.GetRow(gv.GetSelectedRows()[0]);
            double lng = 0;
            double lat = 0;
            if (objRow is ScanGridAnaGridBlockLevelOne)
            {
                lng = (objRow as ScanGridAnaGridBlockLevelOne).CentLng;
                lat = (objRow as ScanGridAnaGridBlockLevelOne).CentLat;
            }
            else if (objRow is ScanGridAnaGridBlockLevelTwo)
            {
                lng = (objRow as ScanGridAnaGridBlockLevelTwo).CentLng;
                lat = (objRow as ScanGridAnaGridBlockLevelTwo).CentLat;
                ScanGridAnaLayer.SelectedGrid = (objRow as ScanGridAnaGridBlockLevelTwo).Grid;
            }
            else if (objRow is ScanGridAnaGridBlockLevelThree)
            {
                lng = (objRow as ScanGridAnaGridBlockLevelThree).Longitude;
                lat = (objRow as ScanGridAnaGridBlockLevelThree).Latitude;
            }
            else
            {
                return;
            }
            MainModel.MainForm.GetMapForm().GoToView(lng, lat);
        }

        private void TabControl_SelectedPageChanged(object sender, TabPageChangedEventArgs e)
        {
            miExportShp.Visible = e.Page.Text.IndexOf("区域") != -1;
        }

        private void MiExportShp_Click(object sender, EventArgs e)
        {
            XtraTabPage curPage = xtraTabControl.SelectedTabPage;
            if (typeof(List<ScanGridAnaGridBlockLevelOne>) != curPage.Tag.GetType())
            {
                return;
            }
            SaveFileDialog dlg = new SaveFileDialog();
            dlg.Filter = FilterHelper.Shp;
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return;
            }

            int shapeIndex = 0;
            int fieldIndex = 0;
            Shapefile shp = new Shapefile();
            shp.CreateNew("", ShpfileType.SHP_POLYGON);
            Field field = new Field();
            field.Name = "id";
            field.Type = FieldType.INTEGER_FIELD;
            shp.EditInsertField(field, ref fieldIndex, null);

            List<ScanGridAnaGridBlockLevelOne> oneList = curPage.Tag as List<ScanGridAnaGridBlockLevelOne>;
            foreach (ScanGridAnaGridBlockLevelOne levelOne in oneList)
            {
                MapWinGIS.Shape shape = new MapWinGIS.Shape();
                shape.ShapeType = ShpfileType.SHP_MULTIPOINT;
                int pointIndex = 0;
                foreach (ScanGridAnaGridBlockLevelTwo levelTwo in levelOne.LevelTwoList)
                {
                    ScanGridAnaGridInfo grid = levelTwo.Grid;

                    MapWinGIS.Point lt = new MapWinGIS.Point();
                    lt.x = grid.TLLongitude; lt.y = grid.TLLatitude;
                    shape.InsertPoint(lt, ref pointIndex);
                    ++pointIndex;

                    MapWinGIS.Point rt = new MapWinGIS.Point();
                    rt.x = grid.BRLongitude; rt.y = grid.TLLatitude;
                    shape.InsertPoint(rt, ref pointIndex);
                    ++pointIndex;

                    MapWinGIS.Point rb = new MapWinGIS.Point();
                    rb.x = grid.BRLongitude; rb.y = grid.BRLatitude;
                    shape.InsertPoint(rb, ref pointIndex);
                    ++pointIndex;

                    MapWinGIS.Point lb = new MapWinGIS.Point();
                    lb.x = grid.TLLongitude; lb.y = grid.BRLatitude;
                    shape.InsertPoint(lb, ref pointIndex);
                    ++pointIndex;
                }

                MapWinGIS.Shape hull = shape.ConvexHull();
                shp.EditInsertShape(hull, ref shapeIndex);
                shp.EditCellValue(fieldIndex, shapeIndex, levelOne.ID);
                ++shapeIndex;
            }

            shp.SaveAs(dlg.FileName, null);
            shp.Close();
            MessageBox.Show("导出Shape图层完成!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private int serviceType;
        private List<TimePeriod> timePeriods;
        private ScanGridAnaResult anaResult;
        private ScanGridAnaResult cmpResult;
        private ScanGridAnaWidget widget;
        private ScanGridAnaColorRanger colorRanger;
        private ScanGridAnaGisColoring giser;
    }
}
