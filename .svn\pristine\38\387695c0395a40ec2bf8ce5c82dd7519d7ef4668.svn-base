﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc.AreaArchiveManage.WeakCoverArea
{
    public class WeakCoverArea
    {
        public WeakCoverArea(AreaBase areaBase)
        {
            this.Area = areaBase;
        }
        public AreaBase Area
        { get; private set; }

        public double RxLevAvg
        { get; set; }

        internal void AttachNearestCells(List<Cell> cells)
        {
            NearestCellDis = double.MaxValue;
            double x = Area.Shape.Centroid.x;
            double y = Area.Shape.Centroid.y;
            foreach (Cell cell in cells)
            {
                double d = Math.Round(cell.GetDistance(x, y), 2);
                if (d < NearestCellDis)
                {
                    NearestSite = cell.Site;
                    NearestCellDis = d;
                }
            }
        }

        public double NearestCellDis { get; set; }

        public List<WeakCoverArea> NbWeakAreas
        {
            get;
            private set;
        }
        internal void AddEachOtherNbArea(WeakCoverArea otherArea)
        {
            if (NbWeakAreas == null)
            {
                NbWeakAreas = new List<WeakCoverArea>();
            }
            NbWeakAreas.Add(otherArea);
            if (otherArea.NbWeakAreas == null)
            {
                otherArea.NbWeakAreas = new List<WeakCoverArea>();
            }
            otherArea.NbWeakAreas.Add(this);
        }

        public bool IsFarCell { get; set; }

        public int NbWeakAreasNum
        {
            get
            {
                return NbWeakAreas == null ? 0 : NbWeakAreas.Count;
            }
        }

        public bool IsLackCell { get; set; }

        public string NbWeakAreaNames
        {
            get
            {
                StringBuilder names = new StringBuilder();
                if (NbWeakAreas!=null)
                {
                    foreach (WeakCoverArea wa in NbWeakAreas)
                    {
                        names.Append(wa.Area.Name + ";");
                    }
                }
                return names.ToString().TrimEnd(';');
            }
        }

        public ISite NearestSite { get; private set; }
    }
}
