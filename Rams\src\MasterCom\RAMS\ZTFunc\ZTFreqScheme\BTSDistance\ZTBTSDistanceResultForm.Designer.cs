﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTBTSDistanceResultForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(ZTBTSDistanceResultForm));
            this.treeListView = new BrightIdeasSoftware.TreeListView();
            this.olvAreaName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvNetType = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvCellCount = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvBTSName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvTAC = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olveNodeBID = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvNearestBTSName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvNearestDistance = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvNearestBTSTac = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvNearestBTSENodeBID = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.contextMenuStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExpandAll = new System.Windows.Forms.ToolStripMenuItem();
            this.miCollapseAll = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportToExcel = new System.Windows.Forms.ToolStripMenuItem();
            ((System.ComponentModel.ISupportInitialize)(this.treeListView)).BeginInit();
            this.contextMenuStrip.SuspendLayout();
            this.SuspendLayout();
            // 
            // treeListView
            // 
            this.treeListView.AllColumns.Add(this.olvAreaName);
            this.treeListView.AllColumns.Add(this.olvNetType);
            this.treeListView.AllColumns.Add(this.olvCellCount);
            this.treeListView.AllColumns.Add(this.olvBTSName);
            this.treeListView.AllColumns.Add(this.olvTAC);
            this.treeListView.AllColumns.Add(this.olveNodeBID);
            this.treeListView.AllColumns.Add(this.olvNearestBTSName);
            this.treeListView.AllColumns.Add(this.olvNearestDistance);
            this.treeListView.AllColumns.Add(this.olvNearestBTSTac);
            this.treeListView.AllColumns.Add(this.olvNearestBTSENodeBID);
            this.treeListView.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvAreaName,
            this.olvNetType,
            this.olvCellCount,
            this.olvBTSName,
            this.olvTAC,
            this.olveNodeBID,
            this.olvNearestBTSName,
            this.olvNearestDistance,
            this.olvNearestBTSTac,
            this.olvNearestBTSENodeBID});
            this.treeListView.ContextMenuStrip = this.contextMenuStrip;
            this.treeListView.Cursor = System.Windows.Forms.Cursors.Default;
            this.treeListView.Dock = System.Windows.Forms.DockStyle.Fill;
            this.treeListView.FullRowSelect = true;
            this.treeListView.GridLines = true;
            this.treeListView.Location = new System.Drawing.Point(0, 0);
            this.treeListView.MultiSelect = false;
            this.treeListView.Name = "treeListView";
            this.treeListView.OwnerDraw = true;
            this.treeListView.ShowGroups = false;
            this.treeListView.Size = new System.Drawing.Size(1055, 422);
            this.treeListView.TabIndex = 1;
            this.treeListView.UseCompatibleStateImageBehavior = false;
            this.treeListView.View = System.Windows.Forms.View.Details;
            this.treeListView.VirtualMode = true;
            this.treeListView.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.treeListView_MouseDoubleClick);
            // 
            // olvAreaName
            // 
            this.olvAreaName.AspectName = "";
            this.olvAreaName.HeaderFont = null;
            this.olvAreaName.Text = "区域名称";
            this.olvAreaName.Width = 100;
            // 
            // olvNetType
            // 
            this.olvNetType.HeaderFont = null;
            this.olvNetType.Text = "基站类型";
            // 
            // olvCellCount
            // 
            this.olvCellCount.HeaderFont = null;
            this.olvCellCount.Text = "小区数量";
            // 
            // olvBTSName
            // 
            this.olvBTSName.HeaderFont = null;
            this.olvBTSName.Text = "基站名称";
            this.olvBTSName.Width = 195;
            // 
            // olvTAC
            // 
            this.olvTAC.HeaderFont = null;
            this.olvTAC.Text = "TAC";
            // 
            // olveNodeBID
            // 
            this.olveNodeBID.HeaderFont = null;
            this.olveNodeBID.Text = "eNodeBID";
            // 
            // olvNearestBTSName
            // 
            this.olvNearestBTSName.HeaderFont = null;
            this.olvNearestBTSName.Text = "最近基站名称";
            this.olvNearestBTSName.Width = 195;
            // 
            // olvNearestDistance
            // 
            this.olvNearestDistance.HeaderFont = null;
            this.olvNearestDistance.Text = "最近基站距离（米）";
            this.olvNearestDistance.Width = 133;
            // 
            // olvNearestBTSTac
            // 
            this.olvNearestBTSTac.HeaderFont = null;
            this.olvNearestBTSTac.Text = "最近基站TAC";
            this.olvNearestBTSTac.Width = 110;
            // 
            // olvNearestBTSENodeBID
            // 
            this.olvNearestBTSENodeBID.HeaderFont = null;
            this.olvNearestBTSENodeBID.Text = "最近基站eNodeBID";
            this.olvNearestBTSENodeBID.Width = 120;
            // 
            // contextMenuStrip
            // 
            this.contextMenuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExpandAll,
            this.miCollapseAll,
            this.miExportToExcel});
            this.contextMenuStrip.Name = "contextMenuStrip";
            this.contextMenuStrip.Size = new System.Drawing.Size(130, 70);
            this.contextMenuStrip.Opening += new System.ComponentModel.CancelEventHandler(this.contextMenuStrip_Opening);
            // 
            // miExpandAll
            // 
            this.miExpandAll.Name = "miExpandAll";
            this.miExpandAll.Size = new System.Drawing.Size(129, 22);
            this.miExpandAll.Text = "全部展开";
            this.miExpandAll.Click += new System.EventHandler(this.miExpandAll_Click);
            // 
            // miCollapseAll
            // 
            this.miCollapseAll.Name = "miCollapseAll";
            this.miCollapseAll.Size = new System.Drawing.Size(129, 22);
            this.miCollapseAll.Text = "全部折叠";
            this.miCollapseAll.Click += new System.EventHandler(this.miCollapseAll_Click);
            // 
            // miExportToExcel
            // 
            this.miExportToExcel.Name = "miExportToExcel";
            this.miExportToExcel.Size = new System.Drawing.Size(129, 22);
            this.miExportToExcel.Text = "导出Excel";
            this.miExportToExcel.Click += new System.EventHandler(this.miExportToExcel_Click);
            // 
            // ZTBTSDistanceResultForm
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("ZTBTSDistanceResultForm.Appearance.Image")));
            this.Appearance.Options.UseFont = true;
            this.Appearance.Options.UseImage = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1055, 422);
            this.Controls.Add(this.treeListView);
            this.Name = "ZTBTSDistanceResultForm";
            this.Text = "平均站间距";
            ((System.ComponentModel.ISupportInitialize)(this.treeListView)).EndInit();
            this.contextMenuStrip.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private BrightIdeasSoftware.TreeListView treeListView;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip;
        private System.Windows.Forms.ToolStripMenuItem miExportToExcel;
        private System.Windows.Forms.ToolStripMenuItem miExpandAll;
        private System.Windows.Forms.ToolStripMenuItem miCollapseAll;
        private BrightIdeasSoftware.OLVColumn olvAreaName;
        private BrightIdeasSoftware.OLVColumn olvNetType;
        private BrightIdeasSoftware.OLVColumn olvCellCount;
        private BrightIdeasSoftware.OLVColumn olvBTSName;
        private BrightIdeasSoftware.OLVColumn olvTAC;
        private BrightIdeasSoftware.OLVColumn olveNodeBID;
        private BrightIdeasSoftware.OLVColumn olvNearestBTSName;
        private BrightIdeasSoftware.OLVColumn olvNearestDistance;
        private BrightIdeasSoftware.OLVColumn olvNearestBTSTac;
        private BrightIdeasSoftware.OLVColumn olvNearestBTSENodeBID;
    }
}