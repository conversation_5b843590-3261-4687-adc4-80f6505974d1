﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class CellChangeFreqSettingDlg
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.cbxFrequencyType = new DevExpress.XtraEditors.ComboBoxEdit();
            this.labelControl5 = new DevExpress.XtraEditors.LabelControl();
            this.simpleButtonDel = new DevExpress.XtraEditors.SimpleButton();
            this.simpleButtonAddFreq = new DevExpress.XtraEditors.SimpleButton();
            this.spinEditEnd = new DevExpress.XtraEditors.SpinEdit();
            this.spinEditStart = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.listBoxControlFreq = new DevExpress.XtraEditors.ListBoxControl();
            this.groupControl3 = new DevExpress.XtraEditors.GroupControl();
            this.cbxIndoorCell = new DevExpress.XtraEditors.CheckEdit();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.spinEdiRadius = new DevExpress.XtraEditors.SpinEdit();
            this.simpleButtonCancel = new DevExpress.XtraEditors.SimpleButton();
            this.simpleButtonOK = new DevExpress.XtraEditors.SimpleButton();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cbxFrequencyType.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditEnd.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditStart.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.listBoxControlFreq)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl3)).BeginInit();
            this.groupControl3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cbxIndoorCell.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEdiRadius.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // groupControl1
            // 
            this.groupControl1.Controls.Add(this.cbxFrequencyType);
            this.groupControl1.Controls.Add(this.labelControl5);
            this.groupControl1.Controls.Add(this.simpleButtonDel);
            this.groupControl1.Controls.Add(this.simpleButtonAddFreq);
            this.groupControl1.Controls.Add(this.spinEditEnd);
            this.groupControl1.Controls.Add(this.spinEditStart);
            this.groupControl1.Controls.Add(this.labelControl3);
            this.groupControl1.Controls.Add(this.labelControl2);
            this.groupControl1.Controls.Add(this.listBoxControlFreq);
            this.groupControl1.Location = new System.Drawing.Point(30, 104);
            this.groupControl1.Name = "groupControl1";
            this.groupControl1.Size = new System.Drawing.Size(399, 204);
            this.groupControl1.TabIndex = 0;
            this.groupControl1.Text = "设置频段";
            // 
            // cbxFrequencyType
            // 
            this.cbxFrequencyType.EditValue = "BCCH";
            this.cbxFrequencyType.Location = new System.Drawing.Point(81, 26);
            this.cbxFrequencyType.Name = "cbxFrequencyType";
            this.cbxFrequencyType.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cbxFrequencyType.Properties.Items.AddRange(new object[] {
            "BCCH",
            "TCH"});
            this.cbxFrequencyType.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            this.cbxFrequencyType.Size = new System.Drawing.Size(86, 21);
            this.cbxFrequencyType.TabIndex = 12;
            // 
            // labelControl5
            // 
            this.labelControl5.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl5.Appearance.Options.UseFont = true;
            this.labelControl5.Location = new System.Drawing.Point(20, 31);
            this.labelControl5.Name = "labelControl5";
            this.labelControl5.Size = new System.Drawing.Size(60, 12);
            this.labelControl5.TabIndex = 11;
            this.labelControl5.Text = "频段类型：";
            // 
            // simpleButtonDel
            // 
            this.simpleButtonDel.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.simpleButtonDel.Appearance.Options.UseFont = true;
            this.simpleButtonDel.Location = new System.Drawing.Point(312, 82);
            this.simpleButtonDel.Name = "simpleButtonDel";
            this.simpleButtonDel.Size = new System.Drawing.Size(75, 23);
            this.simpleButtonDel.TabIndex = 3;
            this.simpleButtonDel.Text = "删除";
            this.simpleButtonDel.Click += new System.EventHandler(this.simpleButtonDel_Click);
            // 
            // simpleButtonAddFreq
            // 
            this.simpleButtonAddFreq.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.simpleButtonAddFreq.Appearance.Options.UseFont = true;
            this.simpleButtonAddFreq.Location = new System.Drawing.Point(312, 53);
            this.simpleButtonAddFreq.Name = "simpleButtonAddFreq";
            this.simpleButtonAddFreq.Size = new System.Drawing.Size(75, 23);
            this.simpleButtonAddFreq.TabIndex = 3;
            this.simpleButtonAddFreq.Text = "增加";
            this.simpleButtonAddFreq.Click += new System.EventHandler(this.simpleButtonAddFreq_Click);
            // 
            // spinEditEnd
            // 
            this.spinEditEnd.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.spinEditEnd.Location = new System.Drawing.Point(204, 56);
            this.spinEditEnd.Name = "spinEditEnd";
            this.spinEditEnd.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditEnd.Properties.Appearance.Options.UseFont = true;
            this.spinEditEnd.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditEnd.Properties.Mask.EditMask = "f0";
            this.spinEditEnd.Size = new System.Drawing.Size(86, 20);
            this.spinEditEnd.TabIndex = 2;
            // 
            // spinEditStart
            // 
            this.spinEditStart.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.spinEditStart.Location = new System.Drawing.Point(81, 56);
            this.spinEditStart.Name = "spinEditStart";
            this.spinEditStart.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditStart.Properties.Appearance.Options.UseFont = true;
            this.spinEditStart.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditStart.Properties.Mask.EditMask = "f0";
            this.spinEditStart.Size = new System.Drawing.Size(86, 20);
            this.spinEditStart.TabIndex = 2;
            // 
            // labelControl3
            // 
            this.labelControl3.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl3.Appearance.Options.UseFont = true;
            this.labelControl3.Location = new System.Drawing.Point(173, 59);
            this.labelControl3.Name = "labelControl3";
            this.labelControl3.Size = new System.Drawing.Size(18, 12);
            this.labelControl3.TabIndex = 1;
            this.labelControl3.Text = "---";
            // 
            // labelControl2
            // 
            this.labelControl2.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl2.Appearance.Options.UseFont = true;
            this.labelControl2.Location = new System.Drawing.Point(41, 59);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(36, 12);
            this.labelControl2.TabIndex = 1;
            this.labelControl2.Text = "频段：";
            // 
            // listBoxControlFreq
            // 
            this.listBoxControlFreq.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.listBoxControlFreq.Appearance.Options.UseFont = true;
            this.listBoxControlFreq.Location = new System.Drawing.Point(81, 82);
            this.listBoxControlFreq.Name = "listBoxControlFreq";
            this.listBoxControlFreq.Size = new System.Drawing.Size(209, 117);
            this.listBoxControlFreq.TabIndex = 0;
            // 
            // groupControl3
            // 
            this.groupControl3.Controls.Add(this.cbxIndoorCell);
            this.groupControl3.Controls.Add(this.labelControl4);
            this.groupControl3.Controls.Add(this.labelControl1);
            this.groupControl3.Controls.Add(this.spinEdiRadius);
            this.groupControl3.Location = new System.Drawing.Point(30, 10);
            this.groupControl3.Name = "groupControl3";
            this.groupControl3.Size = new System.Drawing.Size(399, 69);
            this.groupControl3.TabIndex = 3;
            this.groupControl3.Text = "设置条件";
            // 
            // cbxIndoorCell
            // 
            this.cbxIndoorCell.Location = new System.Drawing.Point(254, 35);
            this.cbxIndoorCell.Name = "cbxIndoorCell";
            this.cbxIndoorCell.Properties.Caption = "排除室内小区";
            this.cbxIndoorCell.Size = new System.Drawing.Size(97, 19);
            this.cbxIndoorCell.TabIndex = 10;
            // 
            // labelControl4
            // 
            this.labelControl4.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl4.Appearance.Options.UseFont = true;
            this.labelControl4.Location = new System.Drawing.Point(173, 40);
            this.labelControl4.Name = "labelControl4";
            this.labelControl4.Size = new System.Drawing.Size(12, 12);
            this.labelControl4.TabIndex = 1;
            this.labelControl4.Text = "米";
            // 
            // labelControl1
            // 
            this.labelControl1.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl1.Appearance.Options.UseFont = true;
            this.labelControl1.Location = new System.Drawing.Point(41, 40);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(36, 12);
            this.labelControl1.TabIndex = 1;
            this.labelControl1.Text = "距离≤";
            // 
            // spinEdiRadius
            // 
            this.spinEdiRadius.EditValue = new decimal(new int[] {
            1500,
            0,
            0,
            0});
            this.spinEdiRadius.Location = new System.Drawing.Point(81, 37);
            this.spinEdiRadius.Name = "spinEdiRadius";
            this.spinEdiRadius.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEdiRadius.Properties.Appearance.Options.UseFont = true;
            this.spinEdiRadius.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEdiRadius.Properties.Mask.EditMask = "f0";
            this.spinEdiRadius.Size = new System.Drawing.Size(86, 20);
            this.spinEdiRadius.TabIndex = 0;
            // 
            // simpleButtonCancel
            // 
            this.simpleButtonCancel.Location = new System.Drawing.Point(354, 332);
            this.simpleButtonCancel.Name = "simpleButtonCancel";
            this.simpleButtonCancel.Size = new System.Drawing.Size(75, 23);
            this.simpleButtonCancel.TabIndex = 9;
            this.simpleButtonCancel.Text = "取消";
            this.simpleButtonCancel.Click += new System.EventHandler(this.simpleButtonCancel_Click);
            // 
            // simpleButtonOK
            // 
            this.simpleButtonOK.Location = new System.Drawing.Point(263, 332);
            this.simpleButtonOK.Name = "simpleButtonOK";
            this.simpleButtonOK.Size = new System.Drawing.Size(75, 23);
            this.simpleButtonOK.TabIndex = 8;
            this.simpleButtonOK.Text = "确定";
            this.simpleButtonOK.Click += new System.EventHandler(this.simpleButtonOK_Click);
            // 
            // CellChangeFreqSettingDlg
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(462, 372);
            this.Controls.Add(this.simpleButtonCancel);
            this.Controls.Add(this.simpleButtonOK);
            this.Controls.Add(this.groupControl3);
            this.Controls.Add(this.groupControl1);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "CellChangeFreqSettingDlg";
            this.Text = "频点切换设置";
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            this.groupControl1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cbxFrequencyType.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditEnd.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditStart.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.listBoxControlFreq)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl3)).EndInit();
            this.groupControl3.ResumeLayout(false);
            this.groupControl3.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cbxIndoorCell.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEdiRadius.Properties)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.GroupControl groupControl1;
        private DevExpress.XtraEditors.GroupControl groupControl3;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.SpinEdit spinEdiRadius;
        private DevExpress.XtraEditors.SimpleButton simpleButtonCancel;
        private DevExpress.XtraEditors.SimpleButton simpleButtonOK;
        private DevExpress.XtraEditors.ListBoxControl listBoxControlFreq;
        private DevExpress.XtraEditors.SpinEdit spinEditEnd;
        private DevExpress.XtraEditors.SpinEdit spinEditStart;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.SimpleButton simpleButtonAddFreq;
        private DevExpress.XtraEditors.SimpleButton simpleButtonDel;
        private DevExpress.XtraEditors.CheckEdit cbxIndoorCell;
        private DevExpress.XtraEditors.LabelControl labelControl5;
        private DevExpress.XtraEditors.ComboBoxEdit cbxFrequencyType;

    }
}