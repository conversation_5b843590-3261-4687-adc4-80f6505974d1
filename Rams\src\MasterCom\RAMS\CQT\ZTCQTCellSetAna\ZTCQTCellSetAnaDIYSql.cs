﻿using System;
using System.Collections.Generic;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTCQTCellSetAnaDIYSqlAreaCover : DIYSQLBase
    {
        private readonly Dictionary<string, string> areaCoverTypeDic = new Dictionary<string, string>();

        public Dictionary<string, string> GetAreaCoverTypeDic()
        {
            return areaCoverTypeDic;
        }
        public ZTCQTCellSetAnaDIYSqlAreaCover(MainModel mainModel)
            : base(mainModel)
        {
            this.dbid = mainModel.DistrictID;
        }
        public ZTCQTCellSetAnaDIYSqlAreaCover(MainModel mainModel, int Dbid)
            : base(mainModel)
        {
            this.dbid = Dbid;
        }

        protected override string getSqlTextString()
        {
            return "select iareatypeid, iareaid, strcover from tb_cfg_static_areainfo ";
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[3];
            rType[0] = E_VType.E_Int;
            rType[1] = E_VType.E_Int;
            rType[2] = E_VType.E_String;
            return rType;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    int areaTypeID = package.Content.GetParamInt();
                    int areaID = package.Content.GetParamInt();
                    string coverType = package.Content.GetParamString();

                    string key = areaTypeID.ToString() + "|" + areaID.ToString();
                    if (!areaCoverTypeDic.ContainsKey(key))
                    {
                        areaCoverTypeDic.Add(key, coverType);
                    }
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                setProgressPercent(ref index, ref progress);
            }
        }

        public override string Name
        {
            get { return "ZTCQTCellSetAnaDIYSqlAreaCover"; }
        }
    }

    public class ZTCQTCellSetAnaDIYSqlAreaName : DIYSQLBase
    {
        private readonly Dictionary<string, string> areaNameDic = new Dictionary<string, string>();

        public Dictionary<string, string> GetAreaNameDic()
        {
            return areaNameDic;
        }
        public ZTCQTCellSetAnaDIYSqlAreaName(MainModel mainModel)
            : base(mainModel)
        {
            this.dbid = mainModel.DistrictID;
        }
        public ZTCQTCellSetAnaDIYSqlAreaName(MainModel mainModel, int Dbid)
            : base(mainModel)
        {
            this.dbid = Dbid;
        }

        protected override string getSqlTextString()
        {
            return "select iareatypeid, iareaid, strareaname from tb_cfg_static_arealist where iareatypeid in (24,25,28)";
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[3];
            rType[0] = E_VType.E_Int;
            rType[1] = E_VType.E_Int;
            rType[2] = E_VType.E_String;
            return rType;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    int areaTypeID = package.Content.GetParamInt();
                    int areaID = package.Content.GetParamInt();
                    string areaName = package.Content.GetParamString();

                    string key = areaTypeID.ToString() + "|" + areaID.ToString();
                    if (!areaNameDic.ContainsKey(key))
                    {
                        areaNameDic.Add(key, areaName);
                    }
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                setProgressPercent(ref index, ref progress);
            }
        }

        public override string Name
        {
            get { return "ZTCQTCellSetAnaDIYSqlAreaName"; }
        }
    }
}