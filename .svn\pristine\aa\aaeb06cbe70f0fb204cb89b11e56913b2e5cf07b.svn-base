﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Data;

namespace MasterCom.RAMS.Func
{
    public class ScanGridAnaCoverage07Stater
    {
        private readonly double minCovValue = 0;
        private readonly double maxCovValue = 7;
        private readonly ScanGridAnaResult anaResult;
        private readonly ScanGridAnaStater stater;
        private readonly int serviceType;
        private ScanGridAnaGridType netType;
        private readonly DataTable dt;

        public ScanGridAnaCoverage07Stater(ScanGridAnaResult anaResult, ScanGridAnaStater stater)
        {
            this.anaResult = anaResult;
            this.stater = stater;
            this.serviceType = ScanGridAnaSettingCondition.Instance.ServiceType;
            dt = new DataTable();
        }

        public void Stat()
        {
            if (serviceType == 12) // gsm
            {
                PrepareColumnnForGsm(dt);
                netType = ScanGridAnaGridType.GSM900;
            }
            else
            {
                PrepareColumnForTd(dt);
                netType = ScanGridAnaGridType.TD;
            }

            Dictionary<string, double[]> regionCovDic = StatResult(anaResult);
            Dictionary<string, double> regionDepDic = GetDepth(stater, regionCovDic.Keys);
            Dictionary<string, int[]> regionNetCount = GetRegionNetCount(anaResult);
            string totalName = "";
            foreach (string regionName in regionDepDic.Keys)
            {
                if (!regionCovDic.ContainsKey(regionName)) // 汇总信息行
                {
                    totalName = regionName;
                    continue;
                }

                int cntSum = regionNetCount[regionName][(int)netType];
                object[] objs = new object[dt.Columns.Count];
                objs[0] = regionName;
                for (int i = 1; i < objs.Length - 1; ++i)
                {
                    objs[i] = cntSum == 0 ? 0 : regionCovDic[regionName][i - 1] / cntSum;
                }
                objs[dt.Columns.Count - 1] = regionDepDic[regionName];

                dt.Rows.Add(objs);
            }

            // 汇总信息
            sumTotalRes(regionCovDic, regionDepDic, regionNetCount, totalName);
        }

        private void sumTotalRes(Dictionary<string, double[]> regionCovDic, Dictionary<string, double> regionDepDic, Dictionary<string, int[]> regionNetCount, string totalName)
        {
            int totalCount = 0;
            foreach (int[] its in regionNetCount.Values)
            {
                totalCount += its[(int)netType];
            }
            double[] totalSum = new double[dt.Columns.Count - 2];
            foreach (double[] ds in regionCovDic.Values)
            {
                for (int i = 0; i < ds.Length; ++i)
                {
                    totalSum[i] += ds[i];
                }
            }
            List<object> objLst = new List<object>();
            objLst.Add(totalName);
            foreach (double d in totalSum)
            {
                objLst.Add(totalCount == 0 ? 0 : d / totalCount);
            }
            objLst.Add(regionDepDic[totalName]);
            dt.Rows.Add(objLst.ToArray());
        }

        public List<DataTable> GetResult()
        {
            List<DataTable> dts = new List<DataTable>();
            dts.Add(dt);
            return dts;
        }

        private Dictionary<string, double> GetDepth(ScanGridAnaStater stater, IEnumerable<string> regionNames)
        {
            Dictionary<string, double> regionDepthDic = new Dictionary<string, double>();
            List<DataTable> dts = stater.GetResult(ScanGridAnaStatType.Compare, netType, 0);
            if (dts == null)
            {
                foreach (string r in regionNames)
                {
                    regionDepthDic.Add(r, 0);
                }
                regionDepthDic.Add("合计", 0);
                return regionDepthDic;
            }
            foreach (DataRow dr in dts[0].Rows)
            {
                string regionName = dr.ItemArray[0] as string;
                double depth = (double)dr.ItemArray[dr.ItemArray.Length - 1];
                regionDepthDic.Add(regionName, depth);
            }
            return regionDepthDic;
        }

        private Dictionary<string, double[]> StatResult(ScanGridAnaResult result)
        {
            Dictionary<string, double[]> retDic = new Dictionary<string, double[]>();
            foreach (ScanGridAnaRegionInfo region in result.RegionList)
            {
                double[] values = new double[dt.Columns.Count - 2];
                retDic.Add(region.RegionName, values);
                foreach (ScanGridAnaGridInfo grid in region.GridList)
                {
                    if (grid.GridType == ScanGridAnaGridType.GSM900 || grid.GridType == ScanGridAnaGridType.TD)
                    {
                        addValue(values, grid, 0);
                    }
                    else if (grid.GridType == ScanGridAnaGridType.DCS1800)
                    {
                        addValue(values, grid, 2);
                    }
                    else if (grid.GridType == ScanGridAnaGridType.最强信号)
                    {
                        addValue(values, grid, 4);
                    }
                } // end foreach (grid)
            } // end foreach (region)

            return retDic;
        }

        private void addValue(double[] values, ScanGridAnaGridInfo grid, int index)
        {
            if (grid.RelLevel >= minCovValue && grid.RelLevel < maxCovValue)
            {
                ++values[index];
            }
            if (grid.AbsLevel >= minCovValue && grid.AbsLevel < maxCovValue)
            {
                ++values[index + 1];
            }
        }

        public Dictionary<string, int[]> GetRegionNetCount(ScanGridAnaResult result)
        {
            Dictionary<string, int[]> retDic = new Dictionary<string,int[]>();
            foreach (ScanGridAnaRegionInfo region in result.RegionList)
            {
                int[] netCount = new int[4];
                foreach (ScanGridAnaGridInfo grid in region.GridList)
                {
                    ++netCount[(int)grid.GridType];
                }
                retDic.Add(region.RegionName, netCount);
            }
            return retDic;
        }

        private void PrepareColumnnForGsm(DataTable dt)
        {
            dt.Columns.Add(new DataColumn("网格ID", typeof(string)));
            dt.Columns.Add(new DataColumn(ScanGridAnaGridType.GSM900.ToString() + "相对覆盖度", typeof(double)));
            dt.Columns.Add(new DataColumn(ScanGridAnaGridType.GSM900.ToString() + "绝对覆盖度", typeof(double)));
            dt.Columns.Add(new DataColumn(ScanGridAnaGridType.DCS1800.ToString() + "相对覆盖度", typeof(double)));
            dt.Columns.Add(new DataColumn(ScanGridAnaGridType.DCS1800.ToString() + "绝对覆盖度", typeof(double)));
            dt.Columns.Add(new DataColumn(ScanGridAnaGridType.最强信号.ToString() + "相对覆盖度", typeof(double)));
            dt.Columns.Add(new DataColumn(ScanGridAnaGridType.最强信号.ToString() + "绝对覆盖度", typeof(double)));
            dt.Columns.Add(new DataColumn("测试深度", typeof(double)));
        }

        private void PrepareColumnForTd(DataTable dt)
        {
            dt.Columns.Add(new DataColumn("网格ID", typeof(string)));
            dt.Columns.Add(new DataColumn(ScanGridAnaGridType.TD.ToString() + "相对覆盖度", typeof(double)));
            dt.Columns.Add(new DataColumn(ScanGridAnaGridType.TD.ToString() + "绝对覆盖度", typeof(double)));
            dt.Columns.Add(new DataColumn("测试深度", typeof(double)));
        }
    }
}
