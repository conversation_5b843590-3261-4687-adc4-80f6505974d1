﻿using MasterCom.MTGis;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Stat;
using MasterCom.RAMS.ZTFunc.ZTNRDominantAreaAna;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    /// <summary>
    /// 四川专用功能
    /// </summary>
    public class NRDominantAreaAna : QueryBase
    {
        const int maxDay = 90;
        NRDominantAreaAnaCondition anaCondition;
        readonly List<NRDominantAreaResult> resultList = new List<NRDominantAreaResult>();
        readonly List<NRDominantAreaFileResult> fileResultList = new List<NRDominantAreaFileResult>();

        public NRDominantAreaAna()
            : base(MainModel.GetInstance())
        {
        }

        public override bool IsNeedSetQueryCondition
        {
            get
            {
                return false;
            }
        }

        public override string Name
        {
            get { return "5G优势区域指标分析"; }
        }

        protected override bool isValidCondition()
        {
            if (anaCondition == null)
            {
                anaCondition = NRDominantAreaConfig.LoadConfig();
            }
            NRDominantAreaAnaDlg dlg = new NRDominantAreaAnaDlg();          
            dlg.SetCondition(anaCondition);
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            anaCondition = dlg.GetCondition();
            NRDominantAreaConfig.SaveConfig(anaCondition);
            return true;
        }

        protected override void query()
        {
            try
            {
                clearData();

                int curDistrictID = mainModel.DistrictID;

                foreach (int districtID in anaCondition.DistrictIDs)
                {
                    resetCellInfo(districtID, true);
                    System.Threading.Thread.Sleep(200);
                    WaitBox.Show("开始统计分析数据...", queryInThread, districtID);
                }
                NRDominantAreaConfig.WriteLog($"{Name}处理完毕");
                resetCellInfo(curDistrictID, false);

                fireShowForm();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message + ex.Source + ex.StackTrace);
            }
        }

        private void clearData()
        {
            resultList.Clear();
            fileResultList.Clear();
        }

        private void resetCellInfo(int curDistrictID, bool queryOnly)
        {
            if (mainModel.DistrictID != curDistrictID)
            {
                mainModel.DistrictID = curDistrictID;
                mainModel.ModelConfig.Init(queryOnly);
            }
        }

        protected void queryInThread(object obj)
        {
            try
            {
                int districtID = (int)obj;
                dealSingleDistridFiles(districtID);
            }
            catch (Exception ex)
            {
                NRDominantAreaConfig.WriteLog(ex.Message + ex.Source + ex.StackTrace, "Error");
                MessageBox.Show(ex.Message + ex.Source + ex.StackTrace);
            }
            finally
            {
                System.Threading.Thread.Sleep(200);
                WaitBox.Close();
            }
        }

        private void dealSingleDistridFiles(int districtID)
        {
            string districtName = DistrictManager.GetInstance().getDistrictName(districtID);
            if (!anaCondition.DistrictConfigDic.TryGetValue(districtName, out NRDominantAreaDistrictInfo districtInfo))
            {
                NRDominantAreaConfig.WriteLog($"没有[{districtName}]地市的配置信息,检查场景Excel数据是否正确", "Error");
                return;
            }
            NRDominantAreaConfig.WriteLog($"正在处理--[{districtName}]--地市");

            WaitBox.Text = $"正在查询[{districtName}]的测试文件...";
            List<NRDominantAreaFileInfo> totalFileList = getFileList(districtID);
            if (totalFileList.Count == 0)
            {
                NRDominantAreaConfig.WriteLog($"没有查询到符合格式的测试文件", "Error");
                return;
            }
            NRDominantAreaConfig.WriteLog($"查询到-[{districtName}]-中符合格式的测试文件共[{totalFileList.Count}]个");

            var sceneFileDic = new Dictionary<string, List<NRDominantAreaFileInfo>>();
            foreach (NRDominantAreaFileInfo file in totalFileList)
            {
                if (!sceneFileDic.TryGetValue(file.SceneName, out List<NRDominantAreaFileInfo> fileList))
                {
                    fileList = new List<NRDominantAreaFileInfo>();
                    sceneFileDic.Add(file.SceneName, fileList);
                }
                fileList.Add(file);
            }

            WaitBox.Text = $"正在处理各个场景的文件信息...";
            dealSceneFile(districtID, districtInfo, sceneFileDic);

            clearOneDistrictData();
        }

        private void dealSceneFile(int districtID, NRDominantAreaDistrictInfo districtInfo, Dictionary<string, List<NRDominantAreaFileInfo>> sceneFileDic)
        {
            foreach (var sceneFile in sceneFileDic)
            {
                var sceneIndexInfo = new NRDominantAreaSceneIndexInfo();
                if (districtInfo.SceneConfigDic.TryGetValue(sceneFile.Key, out NRDominantAreaConfigInfo configInfo))
                {
                    sceneIndexInfo.SceneInfo = configInfo.SceneInfo;
                    sceneIndexInfo.Region = configInfo.SceneRegion;
                    NRDominantAreaConfig.WriteLog($"查询到[{sceneFile.Key}]中的测试文件共[{sceneFile.Value.Count}]个");
                }
                else
                {
                    NRDominantAreaConfig.WriteLog($"不存在[{sceneFile.Key}]场景的配置.该场景包含的文件数[{sceneFile.Value.Count}]个", "Error");
                    continue;
                }

                WaitBox.Text = $"正在处理[{sceneIndexInfo.SceneInfo.SceneName}]的文件指标...";
                dealSceneFiles(sceneFile.Value, sceneIndexInfo, configInfo, districtID);

                if (sceneIndexInfo.ValidFileCount > 0)
                {
                    dealCellTotalCount(sceneIndexInfo, configInfo);

                    WaitBox.Text = $"正在处理[{sceneIndexInfo.SceneInfo.SceneName}]的渗透率...";
                    dealInject(districtID, sceneIndexInfo, configInfo, sceneFile.Value, districtInfo.RoadShpNames);

                    WaitBox.Text = $"正在处理[{sceneIndexInfo.SceneInfo.SceneName}]的结果信息...";
                    WaitBox.ProgressPercent += 10;
                    sceneIndexInfo.Calculate();
                    resultList.Add(sceneIndexInfo.Result);
                    fileResultList.AddRange(sceneIndexInfo.FileResult);
                }
                else
                {
                    sceneIndexInfo.CalculateFileResult();
                    fileResultList.AddRange(sceneIndexInfo.FileResult);
                }
            }
        }

        #region 查询地市在设置时间段内的有效文件
        private List<NRDominantAreaFileInfo> getFileList(int districtID)
        {
            WaitBox.ProgressPercent += 10;
            List<NRDominantAreaFileInfo> fileList = new List<NRDominantAreaFileInfo>();
            MainModel model = MainModel.GetInstance();
            QueryCondition cond = new QueryCondition();
            cond.DistrictID = districtID;
            cond.DistrictIDs = new List<int>() { districtID };
            cond.Areas = new Dictionary<int, List<int>>();
            cond.Periods.Add(anaCondition.TestPeriod);
            cond.CarrierTypes.Add(1);

            List<ServiceType> serviceTypes = ServiceTypeManager.GetServiceTypesByServiceName(ServiceName.NR);
            foreach (ServiceType item in serviceTypes)
            {
                cond.ServiceTypes.Add((int)item);
            }

            model.MainForm.SetQueryConditionBySettings(cond);
            DIYQueryFileInfo query = new DIYQueryFileInfo(model);
            query.SetQueryCondition(cond);
            query.IsShowFileInfoForm = false;
            query.Query();
            WaitBox.ProgressPercent += 10;
            //StringBuilder sb = new StringBuilder();
            NRDominantAreaConfig.WriteLog($"共查到文件[{model.FileInfos.Count}]个");
            foreach (FileInfo file in model.FileInfos)
            {
                NRDominantAreaFileInfo fileInfo = new NRDominantAreaFileInfo();
                string errMsg = fileInfo.Init(file);
                if (string.IsNullOrEmpty(errMsg))
                {
                    fileList.Add(fileInfo);
                }
                else
                {
                    //记录错误信息
                    //sb.AppendLine($"[{file.Name}]{errMsg}");
                }
            }

            return fileList;
        }
        #endregion

        #region 统计文件指标
        /// <summary>
        /// 分析场景内的所有文件
        /// </summary>
        private void dealSceneFiles(List<NRDominantAreaFileInfo> sceneFile, NRDominantAreaSceneIndexInfo sceneIndexInfo, NRDominantAreaConfigInfo configInfo, int districtID)
        {
            NRDominantAreaCellCount cellCount = new NRDominantAreaCellCount();
            SceneKpiInfo info = new SceneKpiInfo();
            foreach (NRDominantAreaFileInfo file in sceneFile)
            {
                WaitBox.ProgressPercent += 10;
                sceneIndexInfo.FileList.Add(file);
                NRDominantAreaInvalidType invalidType = judgeFileValid(file, configInfo);
             
                if (invalidType != NRDominantAreaInvalidType.无)
                {
                    file.SetInvalidReason(invalidType, anaCondition);
                }
                else
                {
                    sceneIndexInfo.ValidFileCount++;

                    statCellCount(cellCount, file.SceneType, configInfo);

                    mainModel.ClearDTData();

                    WaitBox.Text = $"正在查询[{file.FileName}]文件指标...";
                    if (file.SceneType == NRDominantAreaSceneType.室内)
                    {
                        queryKpiByLog(districtID, info, file);
                    }
                    else if (file.SceneType == NRDominantAreaSceneType.室外)
                    {
                        queryKpiByGrid(configInfo, districtID, info, file);
                    }
                }
            }
            sceneIndexInfo.KpiInfo = info;
            sceneIndexInfo.CellCount = cellCount;
        }

        private void queryKpiByLog(int districtID, SceneKpiInfo info, NRDominantAreaFileInfo file)
        {
            QueryCondition cond = new QueryCondition();
            cond.DistrictID = districtID;
            cond.DistrictIDs = new List<int>() { districtID };
            cond.Areas = new Dictionary<int, List<int>>();
            cond.FileInfos.Clear();
            cond.FileInfos.Add(file.File);
            cond.CarrierTypes.Add(1);

            DiyQuerySceneKpiByLog query = new DiyQuerySceneKpiByLog(districtID);
            query.SetQueryCondition(cond);
            query.Query();
            info.AddData(query.Info);
        }

        private void queryKpiByGrid(NRDominantAreaConfigInfo configInfo, int districtID, SceneKpiInfo info, NRDominantAreaFileInfo file)
        {
            QueryCondition cond = new QueryCondition();
            cond.DistrictID = districtID;
            cond.DistrictIDs = new List<int>() { districtID };
            cond.Areas = new Dictionary<int, List<int>>();
            cond.Periods.Add(anaCondition.TestPeriod);
            cond.CarrierTypes.Add(1);
            cond.NameFilterType = FileFilterType.ByMark_ID;
            cond.FileName = file.File.ID.ToString();
            cond.Geometorys = new SearchGeometrys();
            cond.Geometorys.Region = configInfo.SceneRegion.Shape;

            DiyQuerySceneKpiByGrid query = new DiyQuerySceneKpiByGrid(districtID);
            query.SetQueryCondition(cond);
            query.Query();
            info.AddData(query.Info);
        }
        #endregion

        #region 分析单个文件的有效性
        private NRDominantAreaInvalidType judgeFileValid(NRDominantAreaFileInfo file, NRDominantAreaConfigInfo configInfo)
        {
            NRDominantAreaInvalidType invalidType = file.JudgeFileTime(maxDay);
            if (invalidType != NRDominantAreaInvalidType.无)
            {
                return invalidType;
            }

            replayFile(file.File);
            if (file.SceneType == NRDominantAreaSceneType.室外)
            {
                invalidType = judgeOutdoorFileValid(configInfo, out double percent);
                file.Percent = percent;
                return invalidType;
            }
            else if (file.SceneType == NRDominantAreaSceneType.室内)
            {
                return judgeIndoorFileValid(configInfo);
            }

            return invalidType;
        }

        private NRDominantAreaInvalidType judgeOutdoorFileValid(NRDominantAreaConfigInfo configInfo, out double percent)
        {
            percent = 0;
            if (configInfo.SceneRegion == null)
            {
                NRDominantAreaConfig.WriteLog($"不存在[{configInfo.SceneInfo.SceneName}]场景的场景图层配置", "Error");
                return NRDominantAreaInvalidType.不在区域内;
            }

            foreach (DTFileDataManager fileDataManager in MainModel.DTDataManager.FileDataManagers)
            {
                int totalNum = fileDataManager.TestPoints.Count;
                int inRegionNum = 0;
                foreach (TestPoint tp in fileDataManager.TestPoints)
                {
                    if (configInfo.SceneRegion.GeoOp.CheckPointInRegion(tp.Longitude, tp.Latitude))
                    {
                        inRegionNum++;
                    }
                }

                percent = inRegionNum * 100d / totalNum;
                if (percent >= anaCondition.TestPointRadio)
                {
                    return NRDominantAreaInvalidType.无;
                }
            }
            return NRDominantAreaInvalidType.不在区域内;
        }

        private NRDominantAreaInvalidType judgeIndoorFileValid(NRDominantAreaConfigInfo configInfo)
        {
            if (configInfo.SceneCell == null)
            {
                NRDominantAreaConfig.WriteLog($"不存在[{configInfo.SceneInfo.SceneName}]场景的小区配置", "Error");
                return NRDominantAreaInvalidType.主服不匹配;
            }

            foreach (DTFileDataManager fileDataManager in MainModel.DTDataManager.FileDataManagers)
            {
                foreach (TestPoint tp in fileDataManager.TestPoints)
                {
                    string cgi = getNRCellCGI(tp);
                    cgi = getValidCGI(configInfo.SceneCell.NrCellDic, true, cgi);
                    if (!string.IsNullOrEmpty(cgi))
                    {
                        return NRDominantAreaInvalidType.无;
                    }

                    cgi = getLTECellCGI(tp);
                    cgi = getValidCGI(configInfo.SceneCell.LteCellDic, true, cgi);
                    if (!string.IsNullOrEmpty(cgi))
                    {
                        return NRDominantAreaInvalidType.无;
                    }
                }
            }
            return NRDominantAreaInvalidType.主服不匹配;
        }

        private string getNRCellCGI(TestPoint tp)
        {
            long? nci = (long?)NRTpHelper.NrTpManager.GetNCI(tp);
            if (nci == null)
            {
                return "";
            }

            long btsID = (long)nci / 4096;
            long cellID = (long)nci % 4096;
            string cgi = $"460-00-{btsID}-{cellID}";
            return cgi;
        }

        private string getLTECellCGI(TestPoint tp)
        {
            int? eci = (int?)NRTpHelper.NrLteTpManager.GetNCI(tp);
            if (eci == null)
            {
                return "";
            }

            int btsID = (int)eci / 256;
            int cellID = (int)eci % 256;
            string cgi = $"460-00-{btsID}-{cellID}";
            return cgi;
        }

        private string getValidCGI<T>(Dictionary<string, T> dic, bool isContain, string cgi)
        {
            if (isContain)
            {
                if (dic.ContainsKey(cgi))
                {
                    return cgi;
                }
            }
            else
            {
                if (!dic.ContainsKey(cgi))
                {
                    return cgi;
                }
            }
            return "";
        }

        private void replayFile(FileInfo fi)
        {
            QueryCondition condition = new QueryCondition();
            condition.DistrictID = fi.DistrictID;
            condition.FileInfos.Add(fi);
            DIYReplayFileWithNoWaitBox query = new DIYReplayFileWithNoWaitBox(MainModel);
            query.FilterSampleByRegion = false;
            query.Columns = NRTpHelper.InitBaseReplayParamBackground(true, false);
            query.IncludeEvent = false;
            query.SetQueryCondition(condition);
            query.Query();
        }
        #endregion

        #region 统计占用小区个数
        private void statCellCount(NRDominantAreaCellCount cellCount, NRDominantAreaSceneType sceneType, NRDominantAreaConfigInfo configInfo)
        {
            List<TestPoint> tps = MainModel.DTDataManager.FileDataManagers[0].TestPoints;
            if (sceneType == NRDominantAreaSceneType.室外)
            {
                addOutdoorCell(tps, configInfo.SceneRegion, cellCount);
            }
            else if (sceneType == NRDominantAreaSceneType.室内)
            {
                addIndoorCell(tps, configInfo.SceneCell, cellCount);
            }
        }

        private void addOutdoorCell(List<TestPoint> tps, ResvRegion region, NRDominantAreaCellCount cellCount)
        {
            foreach (TestPoint tp in tps)
            {
                if (region.GeoOp.CheckPointInRegion(tp.Longitude, tp.Latitude))
                {
                    string cgi = getNRCellCGI(tp);
                    cgi = getValidCGI(cellCount.NrCellDic, false, cgi);
                    if (!string.IsNullOrEmpty(cgi))
                    {
                        cellCount.NrCellDic[cgi] = true;
                    }

                    cgi = getLTECellCGI(tp);
                    cgi = getValidCGI(cellCount.LteCellDic, false, cgi);
                    if (!string.IsNullOrEmpty(cgi))
                    {
                        cellCount.LteCellDic[cgi] = true;
                    }
                }
            }
        }

        private void addIndoorCell(List<TestPoint> tps, NRDominantAreaCellInfo cellInfo, NRDominantAreaCellCount cellCount)
        {
            foreach (TestPoint tp in tps)
            {
                string cgi = getNRCellCGI(tp);
                if (!string.IsNullOrEmpty(cgi)
                    && !cellCount.NrCellDic.ContainsKey(cgi)
                    && cellInfo.NrCellDic.ContainsKey(cgi))
                {
                    cellCount.NrCellDic[cgi] = true;
                }

                cgi = getLTECellCGI(tp);
                if (!string.IsNullOrEmpty(cgi)
                    && !cellCount.LteCellDic.ContainsKey(cgi)
                    && cellInfo.LteCellDic.ContainsKey(cgi))
                {
                    cellCount.LteCellDic[cgi] = true;
                }
            }
        }
        #endregion

        #region 计算场景内小区总数
        private void dealCellTotalCount(NRDominantAreaSceneIndexInfo sceneIndexInfo, NRDominantAreaConfigInfo configInfo)
        {
            int nrCellCount = 0;
            int lteCellCount = 0;
            if (sceneIndexInfo.SceneInfo.SceneType == NRDominantAreaSceneType.室内)
            {
                getIndoorTotolCellCount(ref nrCellCount, ref lteCellCount, configInfo.SceneCell);
            }
            else if (sceneIndexInfo.SceneInfo.SceneType == NRDominantAreaSceneType.室外)
            {
                getOutdoorTotolCellCount(ref nrCellCount, ref lteCellCount, configInfo.SceneRegion);
            }
            else
            {
                getIndoorTotolCellCount(ref nrCellCount, ref lteCellCount, configInfo.SceneCell);
                getOutdoorTotolCellCount(ref nrCellCount, ref lteCellCount, configInfo.SceneRegion);
            }
            sceneIndexInfo.CellCount.NrCellTotalCount = nrCellCount;
            sceneIndexInfo.CellCount.LteCellTotalCount = lteCellCount;
        }

        private void getOutdoorTotolCellCount(ref int nrCellCount, ref int lteCellCount, ResvRegion region)
        {
            if(region == null)
            {
                return;
            }

            List<NRCell> nrCells = MainModel.CellManager.GetCurrentNRCells();
            foreach (NRCell nrCell in nrCells)
            {
                if (region.GeoOp.CheckPointInRegion(nrCell.Longitude, nrCell.Latitude))
                {
                    nrCellCount++;
                }
            }

            List<LTECell> lteCells = MainModel.CellManager.GetCurrentLTECells();
            foreach (LTECell lteCell in lteCells)
            {
                if (region.GeoOp.CheckPointInRegion(lteCell.Longitude, lteCell.Latitude))
                {
                    lteCellCount++;
                }
            }
        }

        private void getIndoorTotolCellCount(ref int nrCellCount, ref int lteCellCount, NRDominantAreaCellInfo cellInfo)
        {
            if (cellInfo == null)
            {
                return;
            }

            nrCellCount += cellInfo.NrCellDic.Count;
            lteCellCount += cellInfo.LteCellDic.Count;
        }
        #endregion

        #region 计算渗透率
        private void dealInject(int districtID, NRDominantAreaSceneIndexInfo sceneIndexInfo, NRDominantAreaConfigInfo configInfo, List<NRDominantAreaFileInfo> sceneFile, Dictionary<string, MapWinGIS.Shapefile> roadShpNames)
        {
            if (sceneIndexInfo.SceneInfo.SceneType != NRDominantAreaSceneType.室外
                && sceneIndexInfo.SceneInfo.SceneType != NRDominantAreaSceneType.室内外)
            {
                return;
            }

            if (configInfo.SceneRegion == null)
            {
                return;
            }

            if (roadShpNames == null || roadShpNames.Count == 0)
            {
                NRDominantAreaConfig.WriteLog($"[{configInfo.SceneInfo.DistrictName}]没有配置道路图层", "Error");
                return;
            }

            //室外/室内外场景才获取渗透率
            bool isValid = initInjectGrid(districtID, configInfo, sceneFile);
            if (isValid)
            {
                getInjectDatByShp(sceneIndexInfo, roadShpNames);
            }
            else
            {
                NRDominantAreaConfig.WriteLog($"[{configInfo.SceneInfo.SceneName}]场景没有栅格信息", "Error");
            }
        }

        private void getInjectDatByShp(NRDominantAreaSceneIndexInfo sceneIndexInfo, Dictionary<string, MapWinGIS.Shapefile> roadShpNames)
        {
            var regionInjectDic = new Dictionary<string, StreetInjectInfoTotal>();
            var regionTotal = new StreetInjectInfoTotal();
            regionTotal.RegionName = "汇总";

            List<string> shpList = new List<string>(roadShpNames.Keys);
            foreach (string shp in shpList)
            {
                MapWinGIS.Shapefile streetTable = new MapWinGIS.Shapefile();
                if (roadShpNames[shp] == null)
                {
                    streetTable.Open(shp, null);
                    roadShpNames[shp] = streetTable;
                }
                else
                {
                    streetTable = roadShpNames[shp];
                }

                //每个道路图层计算渗透率
                var coveredGridDic = new Dictionary<InjectGridUnit, int>();

                List<StreetInjectInfo> resList = StreetInjectHelper.ParseStreetsOfTable(streetTable, sceneIndexInfo.Region.GeoOp, "Name", "", sceneIndexInfo.SceneInfo.SceneName, ref coveredGridDic);

                var regionMileageInfoDic = new Dictionary<string, RegionMileageInfo>();

                StreetInjectHelper.DealGridInfo(sceneIndexInfo.Region, coveredGridDic, regionMileageInfoDic);

                foreach (StreetInjectInfo res in resList)
                {
                    StreetInjectHelper.AddRegionInject(res, regionInjectDic, regionTotal, regionMileageInfoDic);
                }
            }

            sceneIndexInfo.InjectInfo = regionTotal;
        }

        private bool initInjectGrid(int districtID, NRDominantAreaConfigInfo configInfo, List<NRDominantAreaFileInfo> sceneFile)
        {
            Grid.GridMatrix<InjectGridUnit> injectGridMatrix = null;
            QueryCondition cond = new QueryCondition();
            cond.DistrictID = districtID;
            cond.DistrictIDs = new List<int>() { districtID };
            cond.Areas = new Dictionary<int, List<int>>();
            cond.Periods.Add(anaCondition.TestPeriod);
            cond.CarrierTypes.Add(1);
            cond.NameFilterType = FileFilterType.ByMark_ID;
            cond.Geometorys = new SearchGeometrys();
            cond.Geometorys.Region = configInfo.SceneRegion.Shape;

            int curIdx = 0;
            while (curIdx < sceneFile.Count)//分批次发送包
            {
                StringBuilder strb = getCurFiles(sceneFile, ref curIdx);
                string fileIDs = strb.ToString().TrimEnd(',');
                cond.FileName = fileIDs;

                DiyQueryInjectGrid query = new DiyQueryInjectGrid(districtID);
                query.SetQueryCondition(cond);
                query.Query();
                if (injectGridMatrix == null)
                {
                    injectGridMatrix = query.injectGridMatrix;
                }
                else
                {
                    mergeInjectGridMatrix(injectGridMatrix, query.injectGridMatrix);
                }
            }
            bool isValid = StreetInjectHelper.Init(injectGridMatrix, true);
            return isValid;
        }

        private StringBuilder getCurFiles(List<NRDominantAreaFileInfo> totalFileList, ref int curIdx)
        {
            StringBuilder strb = new StringBuilder();
            for (; curIdx < totalFileList.Count; curIdx++)
            {
                string curStr = totalFileList[curIdx].File.ID.ToString();
                int bufferLength = Encoding.Default.GetBytes(strb + curStr).Length;
                if (bufferLength > 4000)
                {
                    break;
                }
                strb.Append(curStr + ",");
            }

            return strb;
        }

        private void mergeInjectGridMatrix(Grid.GridMatrix<InjectGridUnit> injectGridMatrix, Grid.GridMatrix<InjectGridUnit> queryInjectGridMatrix)
        {
            foreach (var item in queryInjectGridMatrix)
            {
                InjectGridUnit igu = injectGridMatrix[item.RowIdx, item.ColIdx];
                if (igu == null)
                {
                    igu = new InjectGridUnit();
                    igu.LTLng = item.LTLng;
                    igu.LTLat = item.LTLat;
                    injectGridMatrix[item.RowIdx, item.ColIdx] = igu;
                    igu.Status++;
                }
                igu.repeatCount++;
                foreach (var repeat in item.fileRepeatDic)
                {
                    if (!igu.fileRepeatDic.ContainsKey(repeat.Key))
                        igu.fileRepeatDic.Add(repeat.Key, repeat.Value);
                }
                igu.TestDistance += item.TestDistance;
                igu.TestDuration += item.TestDuration;
            }
        }
        #endregion

        private void clearOneDistrictData()
        {
            foreach (var district in anaCondition.DistrictConfigDic.Values)
            {
                foreach (var road in district.RoadShpNames.Values)
                {
                    road?.Close();
                }
            }
        }

        protected void fireShowForm()
        {
            NRDominantAreaAnaForm frm = MainModel.CreateResultForm(typeof(NRDominantAreaAnaForm)) as NRDominantAreaAnaForm;
            frm.FillData(resultList, fileResultList);
            frm.Owner = MainModel.MainForm;
            frm.Visible = true;
            frm.BringToFront();
        }
    }
}
