﻿using System;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTLteNBCellCheckBothAnaSetForm : BaseDialog
    {
        public ZTLteNBCellCheckBothAnaSetForm()
        {
            InitializeComponent();
        }

        public ZTLteNBCellCheckCondition GetCondition()
        {
            ZTLteNBCellCheckCondition condition = new ZTLteNBCellCheckCondition();
            condition.SampleCount = (int)numSampleCount.Value;
            condition.RSRP = (int)numRSRP.Value;
            condition.Distance = (int)numDistance.Value;
            return condition;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }
    }
}
