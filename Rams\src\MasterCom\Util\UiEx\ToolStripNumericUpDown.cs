using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.Util.UiEx
{
    class ToolStripNumericUpDown : ToolStripControlHost
    {
        public ToolStripNumericUpDown()
            : base(new NumericUpDown())
        {
            
            this.Size = new System.Drawing.Size(60, 21);
            ((NumericUpDown)this.Control).ValueChanged += new EventHandler(ToolStripNumericUpDown_ValueChanged);
        }

        public decimal Increment
        {
            get { return ((NumericUpDown)this.Control).Increment; }
            set { ((NumericUpDown)this.Control).Increment = value; }
        }

        public decimal Minimum
        {
            get { return ((NumericUpDown)this.Control).Minimum; }
            set { ((NumericUpDown)this.Control).Minimum = value; }
        }

        public decimal Maximum
        {
            get { return ((NumericUpDown)this.Control).Maximum; }
            set { ((NumericUpDown)this.Control).Maximum = value; }
        }

        private void ToolStripNumericUpDown_ValueChanged(object send, EventArgs e)
        {
            if (ValueChanged != null) 
                ValueChanged(this, new EventArgs());
        }

        public decimal Value
        {
            get { return ((NumericUpDown)this.Control).Value; }
            set { ((NumericUpDown)this.Control).Value = value; }
        }

        public event EventHandler ValueChanged;
    }
}
