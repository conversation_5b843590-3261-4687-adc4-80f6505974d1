﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class GridCompareCountForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.contextMenuStrip1 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.conOutExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.xtraTabControl1 = new DevExpress.XtraTab.XtraTabControl();
            this.xtraTabPage1 = new DevExpress.XtraTab.XtraTabPage();
            this.dataGrid = new DevExpress.XtraGrid.GridControl();
            this.gridView1 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn29 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn30 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn11 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn12 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn13 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn68 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn69 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn70 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn81 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn82 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn83 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn17 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn19 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn20 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn55 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn71 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn56 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn72 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn73 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn57 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn74 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn58 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn75 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn59 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn76 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn77 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn18 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn78 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn14 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn60 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn39 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn79 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn15 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn61 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn40 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn80 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn31 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn41 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn42 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn43 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn33 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn62 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn63 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn44 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn45 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn46 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn6 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn7 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn8 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn9 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn10 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn26 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.xtraTabPage2 = new DevExpress.XtraTab.XtraTabPage();
            this.dataGridInfo = new DevExpress.XtraGrid.GridControl();
            this.gridView2 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn16 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn47 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn48 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn21 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn51 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn52 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn53 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn54 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn37 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn27 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn22 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn49 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn23 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn35 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn32 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn64 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn66 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn38 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn28 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn24 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn50 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn25 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn36 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn34 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn65 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn67 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.contextMenuStrip1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).BeginInit();
            this.xtraTabControl1.SuspendLayout();
            this.xtraTabPage1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataGrid)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).BeginInit();
            this.xtraTabPage2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridInfo)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).BeginInit();
            this.SuspendLayout();
            // 
            // contextMenuStrip1
            // 
            this.contextMenuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.conOutExcel});
            this.contextMenuStrip1.Name = "contextMenuStrip1";
            this.contextMenuStrip1.Size = new System.Drawing.Size(125, 26);
            // 
            // conOutExcel
            // 
            this.conOutExcel.Name = "conOutExcel";
            this.conOutExcel.Size = new System.Drawing.Size(124, 22);
            this.conOutExcel.Text = "导出Excel";
            this.conOutExcel.Click += new System.EventHandler(this.conOutExcel_Click);
            // 
            // xtraTabControl1
            // 
            this.xtraTabControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.xtraTabControl1.Location = new System.Drawing.Point(0, 0);
            this.xtraTabControl1.Name = "xtraTabControl1";
            this.xtraTabControl1.SelectedTabPage = this.xtraTabPage1;
            this.xtraTabControl1.Size = new System.Drawing.Size(1124, 404);
            this.xtraTabControl1.TabIndex = 5;
            this.xtraTabControl1.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.xtraTabPage1,
            this.xtraTabPage2});
            // 
            // xtraTabPage1
            // 
            this.xtraTabPage1.Controls.Add(this.dataGrid);
            this.xtraTabPage1.Name = "xtraTabPage1";
            this.xtraTabPage1.Size = new System.Drawing.Size(1117, 374);
            this.xtraTabPage1.Text = "分段汇总信息";
            // 
            // dataGrid
            // 
            this.dataGrid.ContextMenuStrip = this.contextMenuStrip1;
            this.dataGrid.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dataGrid.Location = new System.Drawing.Point(0, 0);
            this.dataGrid.MainView = this.gridView1;
            this.dataGrid.Name = "dataGrid";
            this.dataGrid.Size = new System.Drawing.Size(1117, 374);
            this.dataGrid.TabIndex = 5;
            this.dataGrid.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView1});
            // 
            // gridView1
            // 
            this.gridView1.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn1,
            this.gridColumn2,
            this.gridColumn29,
            this.gridColumn30,
            this.gridColumn11,
            this.gridColumn12,
            this.gridColumn13,
            this.gridColumn68,
            this.gridColumn69,
            this.gridColumn70,
            this.gridColumn81,
            this.gridColumn82,
            this.gridColumn83,
            this.gridColumn17,
            this.gridColumn19,
            this.gridColumn20,
            this.gridColumn55,
            this.gridColumn71,
            this.gridColumn56,
            this.gridColumn72,
            this.gridColumn73,
            this.gridColumn57,
            this.gridColumn74,
            this.gridColumn58,
            this.gridColumn75,
            this.gridColumn59,
            this.gridColumn76,
            this.gridColumn77,
            this.gridColumn18,
            this.gridColumn78,
            this.gridColumn14,
            this.gridColumn60,
            this.gridColumn39,
            this.gridColumn79,
            this.gridColumn15,
            this.gridColumn61,
            this.gridColumn40,
            this.gridColumn80,
            this.gridColumn31,
            this.gridColumn41,
            this.gridColumn42,
            this.gridColumn43,
            this.gridColumn33,
            this.gridColumn62,
            this.gridColumn63,
            this.gridColumn44,
            this.gridColumn45,
            this.gridColumn46,
            this.gridColumn3,
            this.gridColumn4,
            this.gridColumn5,
            this.gridColumn6,
            this.gridColumn7,
            this.gridColumn8,
            this.gridColumn9,
            this.gridColumn10,
            this.gridColumn26});
            this.gridView1.GridControl = this.dataGrid;
            this.gridView1.Name = "gridView1";
            this.gridView1.OptionsBehavior.Editable = false;
            this.gridView1.OptionsView.ColumnAutoWidth = false;
            this.gridView1.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "序号";
            this.gridColumn1.FieldName = "ISN";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.Visible = true;
            this.gridColumn1.VisibleIndex = 0;
            this.gridColumn1.Width = 58;
            // 
            // gridColumn2
            // 
            this.gridColumn2.Caption = "地市";
            this.gridColumn2.FieldName = "StrCity";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.Visible = true;
            this.gridColumn2.VisibleIndex = 1;
            this.gridColumn2.Width = 55;
            // 
            // gridColumn29
            // 
            this.gridColumn29.Caption = "网格类型";
            this.gridColumn29.FieldName = "StrGridType";
            this.gridColumn29.Name = "gridColumn29";
            this.gridColumn29.Visible = true;
            this.gridColumn29.VisibleIndex = 2;
            // 
            // gridColumn30
            // 
            this.gridColumn30.Caption = "网格号";
            this.gridColumn30.FieldName = "StrGridName";
            this.gridColumn30.Name = "gridColumn30";
            this.gridColumn30.Visible = true;
            this.gridColumn30.VisibleIndex = 3;
            // 
            // gridColumn11
            // 
            this.gridColumn11.Caption = "含历史主优栅格总数（低于25M或关系）";
            this.gridColumn11.FieldName = "IHostGoodGrid";
            this.gridColumn11.Name = "gridColumn11";
            this.gridColumn11.Visible = true;
            this.gridColumn11.VisibleIndex = 4;
            // 
            // gridColumn12
            // 
            this.gridColumn12.Caption = "含历史主劣栅格总数（低于25M或关系）";
            this.gridColumn12.FieldName = "IHostWeakGrid";
            this.gridColumn12.Name = "gridColumn12";
            this.gridColumn12.Visible = true;
            this.gridColumn12.VisibleIndex = 5;
            // 
            // gridColumn13
            // 
            this.gridColumn13.Caption = "含历史栅格优胜率（低于25M或关系）";
            this.gridColumn13.FieldName = "StrHostGoodRate";
            this.gridColumn13.Name = "gridColumn13";
            this.gridColumn13.Visible = true;
            this.gridColumn13.VisibleIndex = 6;
            // 
            // gridColumn68
            // 
            this.gridColumn68.Caption = "本月主优栅格总数（低于25M或关系）";
            this.gridColumn68.FieldName = "IHostGoodGridCurMonth";
            this.gridColumn68.Name = "gridColumn68";
            this.gridColumn68.Visible = true;
            this.gridColumn68.VisibleIndex = 7;
            // 
            // gridColumn69
            // 
            this.gridColumn69.Caption = "本月主劣栅格总数（低于25M或关系）";
            this.gridColumn69.FieldName = "IHostWeakGridCurMonth";
            this.gridColumn69.Name = "gridColumn69";
            this.gridColumn69.Visible = true;
            this.gridColumn69.VisibleIndex = 8;
            // 
            // gridColumn70
            // 
            this.gridColumn70.Caption = "本月栅格优胜率（低于25M或关系）";
            this.gridColumn70.FieldName = "StrHostGoodRateCurMonth";
            this.gridColumn70.Name = "gridColumn70";
            this.gridColumn70.Visible = true;
            this.gridColumn70.VisibleIndex = 9;
            // 
            // gridColumn81
            // 
            this.gridColumn81.Caption = "本月主优栅格总数（低于25M或关系）--不含脱网";
            this.gridColumn81.FieldName = "I本月主优栅格总数_不含脱网";
            this.gridColumn81.Name = "gridColumn81";
            this.gridColumn81.Visible = true;
            this.gridColumn81.VisibleIndex = 10;
            // 
            // gridColumn82
            // 
            this.gridColumn82.Caption = "本月主劣栅格总数（低于25M或关系）-不含脱网";
            this.gridColumn82.FieldName = "I本月主劣栅格总数_不含脱网";
            this.gridColumn82.Name = "gridColumn82";
            this.gridColumn82.Visible = true;
            this.gridColumn82.VisibleIndex = 11;
            // 
            // gridColumn83
            // 
            this.gridColumn83.Caption = "本月栅格优胜率（低于25M或关系）-不含脱网";
            this.gridColumn83.FieldName = "Str本月栅格优胜率_不含脱网";
            this.gridColumn83.Name = "gridColumn83";
            this.gridColumn83.Visible = true;
            this.gridColumn83.VisibleIndex = 12;
            // 
            // gridColumn17
            // 
            this.gridColumn17.Caption = "主队原始数据";
            this.gridColumn17.FieldName = "IHostGrid";
            this.gridColumn17.Name = "gridColumn17";
            this.gridColumn17.Visible = true;
            this.gridColumn17.VisibleIndex = 13;
            this.gridColumn17.Width = 79;
            // 
            // gridColumn19
            // 
            this.gridColumn19.Caption = "客队原总栅格数";
            this.gridColumn19.FieldName = "IGuestGrid";
            this.gridColumn19.Name = "gridColumn19";
            this.gridColumn19.Visible = true;
            this.gridColumn19.VisibleIndex = 14;
            this.gridColumn19.Width = 83;
            // 
            // gridColumn20
            // 
            this.gridColumn20.Caption = "总栅格数(并集)";
            this.gridColumn20.FieldName = "IAllGrid";
            this.gridColumn20.Name = "gridColumn20";
            this.gridColumn20.Visible = true;
            this.gridColumn20.VisibleIndex = 15;
            this.gridColumn20.Width = 89;
            // 
            // gridColumn55
            // 
            this.gridColumn55.Caption = "主队本月有下载栅格数";
            this.gridColumn55.FieldName = "I主队本月下载栅格数";
            this.gridColumn55.Name = "gridColumn55";
            this.gridColumn55.Visible = true;
            this.gridColumn55.VisibleIndex = 16;
            // 
            // gridColumn71
            // 
            this.gridColumn71.Caption = "主队本月有下载栅格数占总栅格数比例";
            this.gridColumn71.FieldName = "StrHostDwonloadGridPercent";
            this.gridColumn71.Name = "gridColumn71";
            this.gridColumn71.Visible = true;
            this.gridColumn71.VisibleIndex = 17;
            // 
            // gridColumn56
            // 
            this.gridColumn56.Caption = "客队本月有下载栅格数";
            this.gridColumn56.FieldName = "I客队本月下载栅格数";
            this.gridColumn56.Name = "gridColumn56";
            this.gridColumn56.Visible = true;
            this.gridColumn56.VisibleIndex = 18;
            // 
            // gridColumn72
            // 
            this.gridColumn72.Caption = "客队本月有下载栅格数占总栅格比例";
            this.gridColumn72.FieldName = "StrGuestDwonloadGridPercent";
            this.gridColumn72.Name = "gridColumn72";
            this.gridColumn72.Visible = true;
            this.gridColumn72.VisibleIndex = 19;
            // 
            // gridColumn73
            // 
            this.gridColumn73.Caption = "本月不含脱网对比总栅格数";
            this.gridColumn73.FieldName = "I本月不含脱网对比总栅格数";
            this.gridColumn73.Name = "gridColumn73";
            this.gridColumn73.Visible = true;
            this.gridColumn73.VisibleIndex = 20;
            // 
            // gridColumn57
            // 
            this.gridColumn57.Caption = "本月含脱网对比总栅格数";
            this.gridColumn57.FieldName = "I本月对比总栅格数";
            this.gridColumn57.Name = "gridColumn57";
            this.gridColumn57.Visible = true;
            this.gridColumn57.VisibleIndex = 21;
            // 
            // gridColumn74
            // 
            this.gridColumn74.Caption = "本月含脱网对比栅格数占总栅格比例";
            this.gridColumn74.FieldName = "Str本月含脱网对比栅格数占总栅格比例";
            this.gridColumn74.Name = "gridColumn74";
            this.gridColumn74.Visible = true;
            this.gridColumn74.VisibleIndex = 22;
            // 
            // gridColumn58
            // 
            this.gridColumn58.Caption = "主队含历史有下载栅格数";
            this.gridColumn58.FieldName = "I含历史主队下载栅格数";
            this.gridColumn58.Name = "gridColumn58";
            this.gridColumn58.Visible = true;
            this.gridColumn58.VisibleIndex = 23;
            // 
            // gridColumn75
            // 
            this.gridColumn75.Caption = "主队含历史有下载栅格数占总栅格比例";
            this.gridColumn75.FieldName = "StrHostHistoryDwonloadGridPercent";
            this.gridColumn75.Name = "gridColumn75";
            this.gridColumn75.Visible = true;
            this.gridColumn75.VisibleIndex = 24;
            // 
            // gridColumn59
            // 
            this.gridColumn59.Caption = "客队含历史有下载栅格数";
            this.gridColumn59.FieldName = "I含历史客队下载栅格数";
            this.gridColumn59.Name = "gridColumn59";
            this.gridColumn59.Visible = true;
            this.gridColumn59.VisibleIndex = 25;
            // 
            // gridColumn76
            // 
            this.gridColumn76.Caption = "客队含历史有下载栅格数占总栅格比例";
            this.gridColumn76.FieldName = "StrGuestHistoryDwonloadGridPercent";
            this.gridColumn76.Name = "gridColumn76";
            this.gridColumn76.Visible = true;
            this.gridColumn76.VisibleIndex = 26;
            // 
            // gridColumn77
            // 
            this.gridColumn77.Caption = "含历史不含脱网对比总栅格数";
            this.gridColumn77.FieldName = "I含历史不含脱网对比总栅格数";
            this.gridColumn77.Name = "gridColumn77";
            this.gridColumn77.Visible = true;
            this.gridColumn77.VisibleIndex = 27;
            // 
            // gridColumn18
            // 
            this.gridColumn18.Caption = "含历史对比总栅格数";
            this.gridColumn18.FieldName = "ICompareGrid";
            this.gridColumn18.Name = "gridColumn18";
            this.gridColumn18.Visible = true;
            this.gridColumn18.VisibleIndex = 28;
            this.gridColumn18.Width = 89;
            // 
            // gridColumn78
            // 
            this.gridColumn78.Caption = "含历史对比栅格数占总栅格比例";
            this.gridColumn78.FieldName = "Str含历史对比栅格数占总栅格比例";
            this.gridColumn78.Name = "gridColumn78";
            this.gridColumn78.Visible = true;
            this.gridColumn78.VisibleIndex = 29;
            // 
            // gridColumn14
            // 
            this.gridColumn14.Caption = "主队历史栅格数";
            this.gridColumn14.FieldName = "IHostHistoryGrid";
            this.gridColumn14.Name = "gridColumn14";
            this.gridColumn14.Visible = true;
            this.gridColumn14.VisibleIndex = 30;
            // 
            // gridColumn60
            // 
            this.gridColumn60.Caption = "主队历史栅格占含历史对比总栅格比例";
            this.gridColumn60.FieldName = "str主队占含历史对比总数比例";
            this.gridColumn60.Name = "gridColumn60";
            this.gridColumn60.Visible = true;
            this.gridColumn60.VisibleIndex = 31;
            // 
            // gridColumn39
            // 
            this.gridColumn39.Caption = "主队历史栅格数列表";
            this.gridColumn39.FieldName = "StrHostHistoryGrid";
            this.gridColumn39.Name = "gridColumn39";
            this.gridColumn39.Visible = true;
            this.gridColumn39.VisibleIndex = 32;
            // 
            // gridColumn79
            // 
            this.gridColumn79.Caption = "主队历史栅格比例列表";
            this.gridColumn79.FieldName = "StrHostHistoryGridPercent";
            this.gridColumn79.Name = "gridColumn79";
            this.gridColumn79.Visible = true;
            this.gridColumn79.VisibleIndex = 34;
            // 
            // gridColumn15
            // 
            this.gridColumn15.Caption = "客队历史栅格数";
            this.gridColumn15.FieldName = "IGuestHistoryGrid";
            this.gridColumn15.Name = "gridColumn15";
            this.gridColumn15.Visible = true;
            this.gridColumn15.VisibleIndex = 33;
            // 
            // gridColumn61
            // 
            this.gridColumn61.Caption = "客队历史栅格数占含历史对比总栅格比例";
            this.gridColumn61.FieldName = "str客队占含历史对比总数比例";
            this.gridColumn61.Name = "gridColumn61";
            this.gridColumn61.Visible = true;
            this.gridColumn61.VisibleIndex = 37;
            // 
            // gridColumn40
            // 
            this.gridColumn40.Caption = "客队历史栅格数列表";
            this.gridColumn40.FieldName = "StrGuestHistoryGrid";
            this.gridColumn40.Name = "gridColumn40";
            this.gridColumn40.Visible = true;
            this.gridColumn40.VisibleIndex = 35;
            // 
            // gridColumn80
            // 
            this.gridColumn80.Caption = "客队历史栅格比例列表";
            this.gridColumn80.FieldName = "StrGuestHistoryGridPercent";
            this.gridColumn80.Name = "gridColumn80";
            this.gridColumn80.Visible = true;
            this.gridColumn80.VisibleIndex = 40;
            // 
            // gridColumn31
            // 
            this.gridColumn31.Caption = "主队脱网栅格数";
            this.gridColumn31.FieldName = "I主队脱网栅格数";
            this.gridColumn31.Name = "gridColumn31";
            this.gridColumn31.Visible = true;
            this.gridColumn31.VisibleIndex = 36;
            // 
            // gridColumn41
            // 
            this.gridColumn41.Caption = "主队脱网(脱网标识)";
            this.gridColumn41.FieldName = "I主队脱网栅格数_脱网标记";
            this.gridColumn41.Name = "gridColumn41";
            this.gridColumn41.Visible = true;
            this.gridColumn41.VisibleIndex = 38;
            // 
            // gridColumn42
            // 
            this.gridColumn42.Caption = "主队脱网(占它网时长)";
            this.gridColumn42.FieldName = "I主队脱网栅格数_占它网时长";
            this.gridColumn42.Name = "gridColumn42";
            this.gridColumn42.Visible = true;
            this.gridColumn42.VisibleIndex = 39;
            // 
            // gridColumn43
            // 
            this.gridColumn43.Caption = "主队脱网(无RSRP采样点)";
            this.gridColumn43.FieldName = "I主队脱网栅格数_无RSRP采样点";
            this.gridColumn43.Name = "gridColumn43";
            this.gridColumn43.Visible = true;
            this.gridColumn43.VisibleIndex = 41;
            // 
            // gridColumn33
            // 
            this.gridColumn33.Caption = "客队脱网栅格数";
            this.gridColumn33.FieldName = "I客队脱网栅格数";
            this.gridColumn33.Name = "gridColumn33";
            this.gridColumn33.Visible = true;
            this.gridColumn33.VisibleIndex = 42;
            // 
            // gridColumn62
            // 
            this.gridColumn62.Caption = "客队脱网栅格占总栅格比例";
            this.gridColumn62.FieldName = "str客队脱网占总栅格数比例";
            this.gridColumn62.Name = "gridColumn62";
            this.gridColumn62.Visible = true;
            this.gridColumn62.VisibleIndex = 46;
            // 
            // gridColumn63
            // 
            this.gridColumn63.Caption = "客队脱网栅格占含历史对比栅格比例";
            this.gridColumn63.FieldName = "str客队脱网占含历史总栅格数比例";
            this.gridColumn63.Name = "gridColumn63";
            this.gridColumn63.Visible = true;
            this.gridColumn63.VisibleIndex = 47;
            // 
            // gridColumn44
            // 
            this.gridColumn44.Caption = "客队脱网(脱网标记)";
            this.gridColumn44.FieldName = "I客队脱网栅格数_脱网标记";
            this.gridColumn44.Name = "gridColumn44";
            this.gridColumn44.Visible = true;
            this.gridColumn44.VisibleIndex = 43;
            // 
            // gridColumn45
            // 
            this.gridColumn45.Caption = "客队脱网(占它网时长)";
            this.gridColumn45.FieldName = "I客队脱网栅格数_占它网时长";
            this.gridColumn45.Name = "gridColumn45";
            this.gridColumn45.Visible = true;
            this.gridColumn45.VisibleIndex = 44;
            // 
            // gridColumn46
            // 
            this.gridColumn46.Caption = "客队脱网(无RSRP采样点)";
            this.gridColumn46.FieldName = "I客队脱网栅格数_无RSRP采样点";
            this.gridColumn46.Name = "gridColumn46";
            this.gridColumn46.Visible = true;
            this.gridColumn46.VisibleIndex = 45;
            // 
            // gridColumn3
            // 
            this.gridColumn3.Caption = "同高优栅格数";
            this.gridColumn3.FieldName = "IMoreGoodGrid";
            this.gridColumn3.Name = "gridColumn3";
            this.gridColumn3.Visible = true;
            this.gridColumn3.VisibleIndex = 48;
            this.gridColumn3.Width = 89;
            // 
            // gridColumn4
            // 
            this.gridColumn4.Caption = "同高劣栅格数";
            this.gridColumn4.FieldName = "IMoreWeakGrid";
            this.gridColumn4.Name = "gridColumn4";
            this.gridColumn4.Visible = true;
            this.gridColumn4.VisibleIndex = 49;
            this.gridColumn4.Width = 86;
            // 
            // gridColumn5
            // 
            this.gridColumn5.Caption = "同高优胜率";
            this.gridColumn5.FieldName = "StrMoreGoodRate";
            this.gridColumn5.Name = "gridColumn5";
            this.gridColumn5.Visible = true;
            this.gridColumn5.VisibleIndex = 50;
            // 
            // gridColumn6
            // 
            this.gridColumn6.Caption = "同低优栅格数";
            this.gridColumn6.FieldName = "ILessGoodGrid";
            this.gridColumn6.Name = "gridColumn6";
            this.gridColumn6.Visible = true;
            this.gridColumn6.VisibleIndex = 51;
            // 
            // gridColumn7
            // 
            this.gridColumn7.Caption = "同低劣栅格数";
            this.gridColumn7.FieldName = "ILessWeakGrid";
            this.gridColumn7.Name = "gridColumn7";
            this.gridColumn7.Visible = true;
            this.gridColumn7.VisibleIndex = 52;
            // 
            // gridColumn8
            // 
            this.gridColumn8.Caption = "同低优胜率";
            this.gridColumn8.FieldName = "StrLessGoodRate";
            this.gridColumn8.Name = "gridColumn8";
            this.gridColumn8.Visible = true;
            this.gridColumn8.VisibleIndex = 53;
            // 
            // gridColumn9
            // 
            this.gridColumn9.Caption = "主高客低栅格数";
            this.gridColumn9.FieldName = "IHostMoreGuestLess";
            this.gridColumn9.Name = "gridColumn9";
            this.gridColumn9.Visible = true;
            this.gridColumn9.VisibleIndex = 54;
            // 
            // gridColumn10
            // 
            this.gridColumn10.Caption = "主低客高优栅格数";
            this.gridColumn10.FieldName = "IHostLessGuestMore";
            this.gridColumn10.Name = "gridColumn10";
            this.gridColumn10.Visible = true;
            this.gridColumn10.VisibleIndex = 55;
            // 
            // gridColumn26
            // 
            this.gridColumn26.Caption = "主低客高劣栅格数";
            this.gridColumn26.FieldName = "IHostLessGuestMoreWeak";
            this.gridColumn26.Name = "gridColumn26";
            this.gridColumn26.Visible = true;
            this.gridColumn26.VisibleIndex = 56;
            // 
            // xtraTabPage2
            // 
            this.xtraTabPage2.Controls.Add(this.dataGridInfo);
            this.xtraTabPage2.Name = "xtraTabPage2";
            this.xtraTabPage2.Size = new System.Drawing.Size(1117, 374);
            this.xtraTabPage2.Text = "栅格详细信息";
            // 
            // dataGridInfo
            // 
            this.dataGridInfo.ContextMenuStrip = this.contextMenuStrip1;
            this.dataGridInfo.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dataGridInfo.Location = new System.Drawing.Point(0, 0);
            this.dataGridInfo.MainView = this.gridView2;
            this.dataGridInfo.Name = "dataGridInfo";
            this.dataGridInfo.Size = new System.Drawing.Size(1117, 374);
            this.dataGridInfo.TabIndex = 0;
            this.dataGridInfo.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView2});
            // 
            // gridView2
            // 
            this.gridView2.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn16,
            this.gridColumn47,
            this.gridColumn48,
            this.gridColumn21,
            this.gridColumn51,
            this.gridColumn52,
            this.gridColumn53,
            this.gridColumn54,
            this.gridColumn37,
            this.gridColumn27,
            this.gridColumn22,
            this.gridColumn49,
            this.gridColumn23,
            this.gridColumn35,
            this.gridColumn32,
            this.gridColumn64,
            this.gridColumn66,
            this.gridColumn38,
            this.gridColumn28,
            this.gridColumn24,
            this.gridColumn50,
            this.gridColumn25,
            this.gridColumn36,
            this.gridColumn34,
            this.gridColumn65,
            this.gridColumn67});
            this.gridView2.GridControl = this.dataGridInfo;
            this.gridView2.Name = "gridView2";
            this.gridView2.OptionsBehavior.Editable = false;
            this.gridView2.OptionsView.ColumnAutoWidth = false;
            this.gridView2.OptionsView.ShowGroupedColumns = true;
            this.gridView2.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn16
            // 
            this.gridColumn16.Caption = "地市";
            this.gridColumn16.FieldName = "StrCityInfo";
            this.gridColumn16.Name = "gridColumn16";
            this.gridColumn16.Visible = true;
            this.gridColumn16.VisibleIndex = 0;
            // 
            // gridColumn47
            // 
            this.gridColumn47.Caption = "网格类型";
            this.gridColumn47.FieldName = "StrGridType";
            this.gridColumn47.Name = "gridColumn47";
            this.gridColumn47.Visible = true;
            this.gridColumn47.VisibleIndex = 1;
            // 
            // gridColumn48
            // 
            this.gridColumn48.Caption = "网格号";
            this.gridColumn48.FieldName = "StrGridName";
            this.gridColumn48.Name = "gridColumn48";
            this.gridColumn48.Visible = true;
            this.gridColumn48.VisibleIndex = 2;
            // 
            // gridColumn21
            // 
            this.gridColumn21.Caption = "栅格中心经纬度";
            this.gridColumn21.FieldName = "StrGirdCenterInfo";
            this.gridColumn21.Name = "gridColumn21";
            this.gridColumn21.Visible = true;
            this.gridColumn21.VisibleIndex = 3;
            this.gridColumn21.Width = 105;
            // 
            // gridColumn51
            // 
            this.gridColumn51.Caption = "左上经度";
            this.gridColumn51.FieldName = "Itllng";
            this.gridColumn51.Name = "gridColumn51";
            this.gridColumn51.Visible = true;
            this.gridColumn51.VisibleIndex = 4;
            // 
            // gridColumn52
            // 
            this.gridColumn52.Caption = "左上纬度";
            this.gridColumn52.FieldName = "Itllat";
            this.gridColumn52.Name = "gridColumn52";
            this.gridColumn52.Visible = true;
            this.gridColumn52.VisibleIndex = 5;
            // 
            // gridColumn53
            // 
            this.gridColumn53.Caption = "中心经度";
            this.gridColumn53.FieldName = "Icentlng";
            this.gridColumn53.Name = "gridColumn53";
            this.gridColumn53.Visible = true;
            this.gridColumn53.VisibleIndex = 6;
            // 
            // gridColumn54
            // 
            this.gridColumn54.Caption = "中心纬度";
            this.gridColumn54.FieldName = "Icentlat";
            this.gridColumn54.Name = "gridColumn54";
            this.gridColumn54.Visible = true;
            this.gridColumn54.VisibleIndex = 7;
            // 
            // gridColumn37
            // 
            this.gridColumn37.Caption = "TDD_RSRP采样点数";
            this.gridColumn37.FieldName = "D主队RSRP采样点数";
            this.gridColumn37.Name = "gridColumn37";
            this.gridColumn37.Visible = true;
            this.gridColumn37.VisibleIndex = 8;
            this.gridColumn37.Width = 110;
            // 
            // gridColumn27
            // 
            this.gridColumn27.Caption = "TDD_下载采样点数";
            this.gridColumn27.FieldName = "DTDDSampleNum";
            this.gridColumn27.Name = "gridColumn27";
            this.gridColumn27.Visible = true;
            this.gridColumn27.VisibleIndex = 9;
            this.gridColumn27.Width = 110;
            // 
            // gridColumn22
            // 
            this.gridColumn22.Caption = "TDD下载时长";
            this.gridColumn22.FieldName = "DTDDDownTime";
            this.gridColumn22.Name = "gridColumn22";
            this.gridColumn22.Visible = true;
            this.gridColumn22.VisibleIndex = 10;
            this.gridColumn22.Width = 100;
            // 
            // gridColumn49
            // 
            this.gridColumn49.Caption = "TDD下载量";
            this.gridColumn49.FieldName = "DTDDDownSize";
            this.gridColumn49.Name = "gridColumn49";
            this.gridColumn49.Visible = true;
            this.gridColumn49.VisibleIndex = 11;
            // 
            // gridColumn23
            // 
            this.gridColumn23.Caption = "TDD下载速率";
            this.gridColumn23.FieldName = "DTDDDownSpeed";
            this.gridColumn23.Name = "gridColumn23";
            this.gridColumn23.Visible = true;
            this.gridColumn23.VisibleIndex = 12;
            this.gridColumn23.Width = 100;
            // 
            // gridColumn35
            // 
            this.gridColumn35.Caption = "主队脱网标记";
            this.gridColumn35.FieldName = "D主队脱网标记";
            this.gridColumn35.Name = "gridColumn35";
            this.gridColumn35.Visible = true;
            this.gridColumn35.VisibleIndex = 13;
            this.gridColumn35.Width = 100;
            // 
            // gridColumn32
            // 
            this.gridColumn32.Caption = "主队占它网时长";
            this.gridColumn32.FieldName = "D主队占它网时长";
            this.gridColumn32.Name = "gridColumn32";
            this.gridColumn32.Visible = true;
            this.gridColumn32.VisibleIndex = 14;
            this.gridColumn32.Width = 100;
            // 
            // gridColumn64
            // 
            this.gridColumn64.Caption = "主队栅格状态";
            this.gridColumn64.FieldName = "StrGridTDDStatstatus";
            this.gridColumn64.Name = "gridColumn64";
            this.gridColumn64.Visible = true;
            this.gridColumn64.VisibleIndex = 15;
            // 
            // gridColumn66
            // 
            this.gridColumn66.Caption = "主队数据源";
            this.gridColumn66.FieldName = "Str主队数据源";
            this.gridColumn66.Name = "gridColumn66";
            this.gridColumn66.Visible = true;
            this.gridColumn66.VisibleIndex = 16;
            // 
            // gridColumn38
            // 
            this.gridColumn38.Caption = "FDD_RSRP采样点数";
            this.gridColumn38.FieldName = "D客队RSRP采样点数";
            this.gridColumn38.Name = "gridColumn38";
            this.gridColumn38.Visible = true;
            this.gridColumn38.VisibleIndex = 17;
            this.gridColumn38.Width = 110;
            // 
            // gridColumn28
            // 
            this.gridColumn28.Caption = "FDD_下载采样点数";
            this.gridColumn28.FieldName = "DFDDSampleNum";
            this.gridColumn28.Name = "gridColumn28";
            this.gridColumn28.Visible = true;
            this.gridColumn28.VisibleIndex = 18;
            this.gridColumn28.Width = 110;
            // 
            // gridColumn24
            // 
            this.gridColumn24.Caption = "FDD下载时长";
            this.gridColumn24.FieldName = "DFDDDownTime";
            this.gridColumn24.Name = "gridColumn24";
            this.gridColumn24.Visible = true;
            this.gridColumn24.VisibleIndex = 19;
            this.gridColumn24.Width = 100;
            // 
            // gridColumn50
            // 
            this.gridColumn50.Caption = "FDD下载量";
            this.gridColumn50.FieldName = "DFDDDownSize";
            this.gridColumn50.Name = "gridColumn50";
            this.gridColumn50.Visible = true;
            this.gridColumn50.VisibleIndex = 20;
            // 
            // gridColumn25
            // 
            this.gridColumn25.Caption = "FDD下载速率";
            this.gridColumn25.FieldName = "DFDDDownSpeed";
            this.gridColumn25.Name = "gridColumn25";
            this.gridColumn25.Visible = true;
            this.gridColumn25.VisibleIndex = 21;
            this.gridColumn25.Width = 100;
            // 
            // gridColumn36
            // 
            this.gridColumn36.Caption = "客队脱网标记";
            this.gridColumn36.FieldName = "D客队脱网标记";
            this.gridColumn36.Name = "gridColumn36";
            this.gridColumn36.Visible = true;
            this.gridColumn36.VisibleIndex = 23;
            this.gridColumn36.Width = 100;
            // 
            // gridColumn34
            // 
            this.gridColumn34.Caption = "客队占它网时长";
            this.gridColumn34.FieldName = "D客队占它网时长";
            this.gridColumn34.Name = "gridColumn34";
            this.gridColumn34.Visible = true;
            this.gridColumn34.VisibleIndex = 22;
            this.gridColumn34.Width = 100;
            // 
            // gridColumn65
            // 
            this.gridColumn65.Caption = "客队栅格状态";
            this.gridColumn65.FieldName = "StrGridFDDStatstatus";
            this.gridColumn65.Name = "gridColumn65";
            this.gridColumn65.Visible = true;
            this.gridColumn65.VisibleIndex = 24;
            // 
            // gridColumn67
            // 
            this.gridColumn67.Caption = "客队数据源";
            this.gridColumn67.FieldName = "Str客队数据源";
            this.gridColumn67.Name = "gridColumn67";
            this.gridColumn67.Visible = true;
            this.gridColumn67.VisibleIndex = 25;
            // 
            // GridCompareCountForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1124, 404);
            this.Controls.Add(this.xtraTabControl1);
            this.Name = "GridCompareCountForm";
            this.Text = "分段统计栅格数目";
            this.contextMenuStrip1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).EndInit();
            this.xtraTabControl1.ResumeLayout(false);
            this.xtraTabPage1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dataGrid)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).EndInit();
            this.xtraTabPage2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dataGridInfo)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.ContextMenuStrip contextMenuStrip1;
        private System.Windows.Forms.ToolStripMenuItem conOutExcel;
        private DevExpress.XtraTab.XtraTabControl xtraTabControl1;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage1;
        private DevExpress.XtraGrid.GridControl dataGrid;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn17;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn19;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn20;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn14;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn15;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn18;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn6;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn7;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn8;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn9;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn10;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn11;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn12;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn13;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage2;
        private DevExpress.XtraGrid.GridControl dataGridInfo;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn16;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn21;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn22;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn23;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn24;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn25;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn26;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn27;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn28;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn29;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn30;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn32;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn34;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn35;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn36;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn37;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn38;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn31;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn33;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn39;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn40;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn41;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn42;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn43;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn44;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn45;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn46;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn47;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn48;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn49;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn50;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn51;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn52;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn53;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn54;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn55;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn56;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn57;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn58;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn59;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn60;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn61;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn62;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn63;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn64;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn65;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn66;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn67;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn68;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn69;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn70;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn71;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn72;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn73;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn74;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn75;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn76;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn77;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn78;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn79;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn80;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn81;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn82;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn83;
    }
}