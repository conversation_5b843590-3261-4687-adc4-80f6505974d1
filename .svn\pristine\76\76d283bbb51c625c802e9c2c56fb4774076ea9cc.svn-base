﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause
{
    public partial class QualPnl : UserControl
    {
        public QualPnl()
        {
            InitializeComponent();
        }

        public void LinkCondition(FunctionCondition cond)
        {
            QualCause cr = null;
            foreach (CauseBase r in cond.Causes)
            {
                if (r is QualCause)
                {
                    cr = r as QualCause;
                    break;
                }
            }
            if (cr == null)
            {
                return;
            }
            foreach (CauseBase r in cr.SubCauses)
            {
                if (r is PoorSINRCause)
                {
                    poorSINRPnl1.LinkCondition(r as PoorSINRCause);
                }
                else if (r is PoorBLERCause)
                {
                    poorBLERPnl1.LinkCondition(r as PoorBLERCause);
                }
            }
        }


    }
}
