﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc.JiLinFocusSet
{
    public class FocusSetMainItem
    {
        internal static FocusSetMainItem Create(Net.Content content)
        {
            FocusSetMainItem item = new FocusSetMainItem();
            item.Items = new List<EventItem>();
            item.LogItems = new List<CheckLogItem>();
            item.DistrictID = content.GetParamInt();
            item.SetOrderType = content.GetParamString();
            item.ID = content.GetParamInt();
            item.CreateDate = MasterCom.Util.JavaDate.GetDateTimeFromMilliseconds(content.GetParamInt() * 1000L);
            item.Lng = content.GetParamDouble();
            item.Lat = content.GetParamDouble();
            item.EventCount = content.GetParamInt();
            string areas = content.GetParamString();
            string[] arr = areas.Split(new char[] { '|' }, StringSplitOptions.RemoveEmptyEntries);
            if (arr.Length > 0&&(!arr[0].Contains("中心点经纬度")))
            {
                item.AreaNames = arr[0];
            }
            item.RoadNames = content.GetParamString();
            item.OrderID = content.GetParamString();
            item.Status = content.GetParamInt();
            return item;
        }

        public string Key
        {
            get { return string.Format("{0}.{1}.{2}", DistrictID, SetOrderType, ID); }
        }

        public int DistrictID { get; set; }

        public int ID { get; set; }

        public DateTime CreateDate { get; set; }

        public double Lng { get; set; }

        public double Lat { get; set; }

        public int EventCount { get; set; }

        public string AreaNames { get; set; }

        public string RoadNames { get; set; }

        public List<EventItem> Items
        { get; set; }

        internal void AddItem(EventItem evtItem)
        {
            Items.Add(evtItem);
        }
        public List<CheckLogItem> LogItems
        { get; set; }


        public string OrderID { get; set; }

        public string SetOrderType { get; set; }

        public int Status { get; set; }
        public string StatusDesc
        {
            get
            {
                if (Status == 1)
                {
                    return "已创建";
                }
                else if (Status == 2)
                {
                    return "验证通过";
                }
                else if (Status == 3)
                {
                    return "验证失败";
                }
                return string.Empty;
            }
        }
    }

    public class CheckLogItem : FileInfo
    {
        internal static CheckLogItem Create(Net.Content content)
        {
            CheckLogItem item = new CheckLogItem();
            item.SetID = content.GetParamInt();
            item.CityID = content.GetParamInt();
            item.OrderType = content.GetParamString();
            item.CheckTime = Convert.ToDateTime(content.GetParamString());
            item.Status = content.GetParamString();
            item.FileName = content.GetParamString();
            item.ID = content.GetParamInt();
            item.BeginTime = content.GetParamInt();
            item.EndTime = content.GetParamInt();
            item.ProjectID = content.GetParamInt();
            item.ServiceType = content.GetParamInt();
            item.CarrierType = content.GetParamInt();
            item.CheckCount = content.GetParamInt();
            item.Name = item.FileName;
            item.DistrictID = item.CityID;

            DateTime filetime = JavaDate.GetDateTimeFromMilliseconds(1000L * item.BeginTime);
            string mouth = "";
            if (filetime.Month < 10)
            {
                mouth = filetime.Year + "_0" + filetime.Month;
            }
            else
            {
                mouth = filetime.Year + "_" + filetime.Month;
            }
            
            item.LogTable = "tb_log_file_" + mouth;
          
            return item;
        }

        public int SetID
        {
            get;
            set;
        }
        public int CityID
        { get; set; }
        public string OrderType
        { get; set; }
        public DateTime CheckTime
        {
            get;
            set;
        }
        
        public string Status
        {
            get;
            set;
        }
        public string FileName
        { get; set; }
        public int CheckCount
        { get; set; }
        
        public DateTime STime
        { get { return JavaDate.GetDateTimeFromMilliseconds(BeginTime * 1000L); } }

        public DateTime ETime
        {
            get { return JavaDate.GetDateTimeFromMilliseconds(EndTime * 1000L);  }
        }
        public string Key
        {
            get { return string.Format("{0}.{1}.{2}", CityID, OrderType, SetID); }
        }

    }
}
