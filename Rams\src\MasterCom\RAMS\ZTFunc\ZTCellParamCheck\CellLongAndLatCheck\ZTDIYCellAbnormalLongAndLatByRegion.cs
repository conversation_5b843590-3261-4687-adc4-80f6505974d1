﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTDIYCellAbnormalLongAndLatByRegion : DIYSampleByRegion
    {
        private int rxlevMin;
        private int distanceMin;
        private readonly Dictionary<string, AbnormalLongLatInfo> nameAbnormalLongLatInfoDic;
        private readonly List<AbnormalLongLatInfo> abnormalLongLatInfoList;
        private readonly Dictionary<string, int> normalCellTPDic;

        public ZTDIYCellAbnormalLongAndLatByRegion(MainModel mainModel)
            : base(mainModel)
        {
            isAddSampleToDTDataManager = false;
            nameAbnormalLongLatInfoDic = new Dictionary<string, AbnormalLongLatInfo>();
            abnormalLongLatInfoList = new List<AbnormalLongLatInfo>();
            normalCellTPDic = new Dictionary<string, int>();
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 19000, 19035, this.Name);
        }

        protected override void query()
        {
            if (!getConditionBeforeQuery())
            {
                return;
            }
            curSelDIYSampleGroup = getCurSelDIYSampleGroupPrepare();
            if (curSelDIYSampleGroup == null)
            {
                return;
            }
            WaitBox.CanCancel = true;
            WaitBox.Text = "正在查询...";
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                dataClear();
                
                foreach (int districtID in condition.DistrictIDs)
                {
                    //strCityName = DistrictManager.GetInstance().getDistrictName(districtID);
                    queryDistrictData(districtID);
                }

                //if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
                //{
                //    ErrorInfo = "连接服务器失败!";
                //    return;
                //}

                //WaitBox.Show("开始统计数据...", queryInThread, clientProxy);

                afterRecieveAllData();

                FireShowFormAfterQuery();//在该方法最后设置了默认的指标

                //加多一个对ThemeName内容的判断，避免默认指标被清空
                if (curSelDIYSampleGroup.ThemeName != null && curSelDIYSampleGroup.ThemeName != "")
                {
                    MainModel.FireSetDefaultMapSerialTheme(curSelDIYSampleGroup.ThemeName);
                }
                MainModel.FireDTDataChanged(this);
            }
            catch (Exception e)
            {
                System.Windows.Forms.MessageBox.Show(e.Source + Environment.NewLine + e.Message);
            }
            finally
            {
                clientProxy.Close();
            }
        }

        protected virtual void queryDistrictData(int districtID)
        {
            ClientProxy clientProxy = new ClientProxy();
            if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, districtID) != ConnectResult.Success)
            {
                ErrorInfo = "连接服务器失败!";
                return;
            }
            try
            {
                WaitBox.Show("开始统计数据...", queryInThread, clientProxy);
            }
            finally
            {
                clientProxy.Close();
            }
        }

        protected override MasterCom.RAMS.Model.Interface.DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            List<object> columnsDef = new List<object>();
            Dictionary<string, object> param = new Dictionary<string, object>();
            param["param_name"] = "TD_PCCPCH_RSCP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "TD_BLER";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "TD_SCell_LAC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "TD_SCell_CI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "TD_SCell_UARFCN";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "TD_SCell_CPI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "RxLevSub";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "RxQualSub";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "LAC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "CI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "BCCH";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "BSIC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "W_Reference_RSCP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "W_SysLAI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "W_SysCellID";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "W_frequency";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "W_Reference_PSC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);
            //LTE
            param = new Dictionary<string, object>();
            param["param_name"] = "lte_TAC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_ECI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_RSRP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_SINR";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            //NR
            param = new Dictionary<string, object>();
            param["param_name"] = "NR_TAC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "NR_NCI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "NR_SS_RSRP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "NR_SS_SINR";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            Dictionary<string, object> tmpDic = new Dictionary<string, object>();
            tmpDic.Add("name", (object)"TD_PCCPCH_RSCP");
            tmpDic.Add("themeName", (object)"TD_PCCPCH_RSCP");
            tmpDic.Add("columnsDef", (object)columnsDef);

            DIYSampleGroup group = new DIYSampleGroup();
            group.Param = tmpDic;
            return group;
        }

        protected override bool getConditionBeforeQuery()
        {
            ZTDIYCellAbnormalLongAndLatSettingDlg dlg = new ZTDIYCellAbnormalLongAndLatSettingDlg();
            if (dlg.ShowDialog() != System.Windows.Forms.DialogResult.OK)
            {
                return false;
            }
            rxlevMin = dlg.RxlevMin;
            distanceMin = dlg.DistanceMin;
            return true;
        }

        private void dataClear()
        {
            MainModel.ClearDTData();
            nameAbnormalLongLatInfoDic.Clear();
            abnormalLongLatInfoList.Clear();
        }

        protected override bool isValidTestPoint(TestPoint tp)
        {
            float? rxlev = null;
            if (tp is TestPointDetail)
            {
                short? rxlevSub = (short?)tp["RxLevSub"];
                rxlev = rxlevSub;
            }
            else if (tp is TestPoint_NR)
            {
                rxlev = (float?)tp["NR_SS_RSRP"];
            }
            else if (tp is LTETestPointDetail)
            {
                rxlev = (float?)tp["lte_RSRP"];
            }
            else if (tp is TDTestPointDetail)
            {
                float? rscp = (float?)tp["TD_PCCPCH_RSCP"];
                rxlev = rscp;
            }
            else if (tp is WCDMATestPointDetail)
            {
                float? rscp = (float?)tp["W_Reference_RSCP"];
                rxlev = rscp;
            }
            return rxlev != null && rxlev >= -140 && rxlev <= -10 && rxlev >= rxlevMin;
        }

        private void updateThemeName()
        {
            if (abnormalLongLatInfoList.Count <= 0 || abnormalLongLatInfoList[0].PointList.Count <= 0)
            {
                return;
            }
            if (abnormalLongLatInfoList[0].PointList[0].TPoint is TestPointDetail)
            {
                curSelDIYSampleGroup.ThemeName = "GSM RxLevSub";
            }
            else if (abnormalLongLatInfoList[0].PointList[0].TPoint is TestPoint_NR)
            {
                curSelDIYSampleGroup.ThemeName = "NR_SS_RSRP";
            }
            else if (abnormalLongLatInfoList[0].PointList[0].TPoint is LTETestPointDetail)
            {
                curSelDIYSampleGroup.ThemeName = "lte_RSRP";
            }
            else if (abnormalLongLatInfoList[0].PointList[0].TPoint is TDTestPointDetail)
            {
                curSelDIYSampleGroup.ThemeName = "TD_PCCPCH_RSCP";
            }
            else if (abnormalLongLatInfoList[0].PointList[0].TPoint is WCDMATestPointDetail)
            {
                curSelDIYSampleGroup.ThemeName = "W_Reference_RSCP";
            }
            else
            {
                curSelDIYSampleGroup.ThemeName = "";
            }
        }

        protected virtual void afterRecieveAllData(params object[] reservedParams)
        {
            foreach (AbnormalLongLatInfo info in abnormalLongLatInfoList)
            {
                if (normalCellTPDic.TryGetValue(info.CellName, out var num))
                {
                    info.NormalPointNum = num;
                }

                info.CalcResult();
            }
        }

        protected override void FireShowFormAfterQuery()
        {
            updateThemeName();
            object obj = MainModel.GetInstance().GetObjectFromBlackboard(typeof(ZTDIYCellAbnormalLongAndLatInfoForm).FullName);
            ZTDIYCellAbnormalLongAndLatInfoForm form = obj == null ? null : obj as ZTDIYCellAbnormalLongAndLatInfoForm;
            if (form == null || form.IsDisposed)
            {
                form = new ZTDIYCellAbnormalLongAndLatInfoForm(MainModel.GetInstance());
            }
            form.FillData(abnormalLongLatInfoList);
            form.Visible = false;
            form.Show(MainModel.GetInstance().MainForm);
        }

        protected override void doWithDTData(MasterCom.RAMS.Model.TestPoint tp)
        {
            try
            {
                if (tp is TestPointDetail)
                {
                    deal2GPoint(tp);
                }
                else if (tp is TestPoint_NR)
                {
                    dealNRPoint(tp);
                }
                else if (tp is LTETestPointDetail)
                {
                    dealLTEPoint(tp);
                }
                else if (tp is TDTestPointDetail)
                {
                    dealTDPoint(tp);
                }
                else if (tp is WCDMATestPointDetail)
                {
                    dealWPoint(tp);
                }
            }
            catch
            {
                //continue
            }
        }

        private void deal2GPoint(TestPoint tp)
        {
            short? rxlev = (short?)tp["RxLevSub"];
            int? lac = (int?)tp["LAC"];
            int? ci = (int?)tp["CI"];
            if (lac == null || ci == null || lac == 255 || lac == -255 || ci == 255 || ci == -255)
            {
                return;
            }
            Cell cell = CellManager.GetInstance().GetCell(tp.DateTime, (ushort)lac, (ushort)ci);
            if (cell == null) return;
            double distance = cell.GetDistance(tp.Longitude, tp.Latitude);
            addAbnormalInfo(tp, rxlev, cell, cell.LAC, cell.CI, distance);
        }

        private void dealTDPoint(TestPoint tp)
        {
            float? rscp = (float?)tp["TD_PCCPCH_RSCP"];
            int? lac = (int?)tp["TD_SCell_LAC"];
            int? ci = (int?)tp["TD_SCell_CI"];
            if (lac == null || ci == null || lac == 255 || lac == -255 || ci == 255 || ci == -255)
            {
                return;
            }
            TDCell tdCell = CellManager.GetInstance().GetTDCell(tp.DateTime, (int)lac, (int)ci);
            if (tdCell == null) return;
            double distance = tdCell.GetDistance(tp.Longitude, tp.Latitude);
            addAbnormalInfo(tp, rscp, tdCell, tdCell.LAC, tdCell.CI, distance);
        }

        private void dealWPoint(TestPoint tp)
        {
            float? rscp = (float?)tp["W_Reference_RSCP"];
            int? lac = (int?)tp["W_SysLAI"];
            int? ci = (int?)tp["W_SysCellID"];
            WCell wCell = CellManager.GetInstance().GetWCell(tp.DateTime, (int)lac, (int)ci);
            if (wCell == null) return;
            double distance = wCell.GetDistance(tp.Longitude, tp.Latitude);
            addAbnormalInfo(tp, rscp, wCell, wCell.LAC, wCell.CI, distance);
        }

        private void dealLTEPoint(TestPoint tp)
        {
            int? tac = (int?)(ushort?)tp["lte_TAC"];
            int? eci = (int?)tp["lte_ECI"];
            float? rsrp = (float?)tp["lte_RSRP"];
            if (tac == null || tac == -1 || eci == null || eci == -10000000 || rsrp == null || rsrp == -10000000)
            {
                return;
            }
            LTECell lteCell = CellManager.GetInstance().GetLTECell(tp.DateTime, (int)tac, (int)eci);
            if (lteCell == null) return;
            double distance = lteCell.GetDistance(tp.Longitude, tp.Latitude);
            addAbnormalInfo(tp, rsrp, lteCell, lteCell.TAC, lteCell.ECI, distance);
        }

        private void dealNRPoint(TestPoint tp)
        {
            int? tac = (int?)NRTpHelper.NrTpManager.GetTAC(tp);
            long? nci = (long?)NRTpHelper.NrTpManager.GetNCI(tp);
            float? rsrp = NRTpHelper.NrTpManager.GetSCellRsrp(tp, true);
            if (tac == null || nci == null || rsrp == null)
            {
                return;
            }

            NRCell nrCell = CellManager.GetInstance().GetNRCell(tp.DateTime, (int)tac, (long)nci);
            if (nrCell == null) return;
            double distance = nrCell.GetDistance(tp.Longitude, tp.Latitude);
            addAbnormalInfo(tp, rsrp, nrCell, nrCell.TAC, nrCell.NCI, distance);
        }

        private void addAbnormalInfo(TestPoint tp, float? rsrp, ICell cell, int lac, long ci,  double distance)
        {
            if (distance > distanceMin)
            {
                if (!nameAbnormalLongLatInfoDic.TryGetValue(cell.Name, out var abnormalInfo))
                {
                    abnormalInfo = new AbnormalLongLatInfo(cell.Name, lac, ci, cell.Longitude, cell.Latitude, cell);
                    nameAbnormalLongLatInfoDic.Add(cell.Name, abnormalInfo);
                    abnormalLongLatInfoList.Add(abnormalInfo);
                }
                abnormalInfo.PointList.Add(new AbnormalLongLatPoint(tp, distance, (float)rsrp, cell));
            }
            else
            {
                if (!normalCellTPDic.TryGetValue(cell.Name, out var _))
                {
                    normalCellTPDic.Add(cell.Name, 0);
                }
                normalCellTPDic[cell.Name]++;
            }
        }
    }

    public class AbnormalLongLatInfo
    {
        public string CellName { get; set; }
        public int LAC { get; set; }
        public long CI { get; set; }
        public double Longitude { get; set; }
        public double Latitude { get; set; }
        public Cell GCCell { get; set; }
        public TDCell GCTDCell { get; set; }
        public WCell GCWCell { get; set; }
        public LTECell GCLTECell { get; set; }
        public NRCell GCNRCell { get; set; }
        public List<AbnormalLongLatPoint> PointList { get; set; }
        public string FileName { get; set; }
        public string MeanRxlev { get; set; }
        public string MeanDistance { get; set; }
        public string MaxVar { get; private set; }
        public string MinVar { get; private set; }
        public string MeanVar { get; private set; }

        public double AbnormalRate { get; set; }
        public int NormalPointNum { get; set; }
        public int PointNum 
        {
            get 
            {
                if (this.PointList == null) return 0;
                return this.PointList.Count;
            } 
        }

        public AbnormalLongLatInfo(string cellName, int lac, long ci, double longitude, double latitude, ICell cell)
        {
            this.CellName = cellName;
            this.LAC = lac;
            this.CI = ci;
            this.Longitude = longitude;
            this.Latitude = latitude;
            this.FileName = null;
            this.MaxVar = "";
            this.MinVar = "";
            this.MeanVar = "";
            PointList = new List<AbnormalLongLatPoint>();

            switch (cell)
            {
                case NRCell nrCell:
                    GCNRCell = nrCell;
                    break;
                case LTECell lteCell:
                    GCLTECell = lteCell;
                    break;
                case Cell gsmCell:
                    GCCell = gsmCell;
                    break;
                case TDCell tdCell:
                    GCTDCell = tdCell;
                    break;
                case WCell wCell:
                    GCWCell = wCell;
                    break;
            }
        }

        private double maxVar = double.MinValue;
        private double minVar = double.MaxValue;
        private double sumVar = 0;

        private double sumRxlev = 0;
        private double sumDistacne = 0;
        private int cntVar = 0;
        public void CalcResult()
        {
            if (PointNum == 0)
            {
                return;
            }

            AbnormalRate = Math.Round(PointNum * 100d / (PointNum + NormalPointNum), 2);

            if (FileName == null)
            {
                FileName = PointList[0].TPoint.FileName;
            }
            foreach (AbnormalLongLatPoint pt in PointList)
            {
                if (pt.Var != null)
                {
                    sumVar += (double)pt.Var;
                    maxVar = Math.Max(maxVar, (double)pt.Var);
                    minVar = Math.Min(minVar, (double)pt.Var);
                    ++cntVar;
                }
                sumRxlev += pt.Rxlev;
                sumDistacne += pt.Distance;
            }
            if (cntVar != 0)
            {
                MaxVar = Math.Round(maxVar, 3).ToString();
                MinVar = Math.Round(minVar, 3).ToString();
                MeanVar = Math.Round(sumVar / cntVar, 3).ToString();
            }
            if (this.PointList != null && this.PointList.Count > 0)
            {
                this.MeanRxlev = Math.Round(sumRxlev / PointList.Count, 3).ToString();
                this.MeanDistance = Math.Round(sumDistacne / PointList.Count, 3).ToString();
            }
        }
    }

    public class AbnormalLongLatPoint
    {
        public Cell GCCell { get; set; }
        public TDCell GCTDCell { get; set; }
        public WCell GCWCell { get; set; }
        public LTECell GCLTECell { get; set; }
        public NRCell GCNRCell { get; set; }
        public TestPoint TPoint { get; set; }
        public double Longitude { get; set; }
        public double Latitude { get; set; }
        public float Rxlev { get; set; }
        public double Distance { get; set; }
        public double? Var { get; set; }

        public AbnormalLongLatPoint(TestPoint tPoint, double distance, float rxlev, ICell cell)
        {
            this.TPoint = tPoint;
            this.Longitude = tPoint.Longitude;
            this.Latitude = tPoint.Latitude;
            this.Rxlev = rxlev;
            this.Distance = Math.Round(distance, 3);
            
            if (tPoint is TestPointDetail)
            {
                this.GCCell = cell as Cell;
                Var = (double?)(byte?)tPoint["RxQualSub"];
                setInValid(0, 7);
            }
            else if (tPoint is TDTestPointDetail)
            {
                this.GCTDCell = cell as TDCell;
                Var = (int?)tPoint["TD_BLER"];
                setInValid(0, 100);
            }
            else if (tPoint is WCDMATestPointDetail)
            {
                this.GCWCell = cell as WCell;
                Var = null;
            }
            else if (tPoint is LTETestPointDetail)
            {
                this.GCLTECell = cell as LTECell;
                Var = (double?)(float?)tPoint["lte_SINR"];
                setInValid(0, 100);
            }
            else if (tPoint is TestPoint_NR)
            {
                this.GCNRCell = cell as NRCell;
                Var = (double?)(float?)tPoint["NR_SS_SINR"];
                setInValid(0, 100);
            }
        }

        private void setInValid(double min,double max)
        {
            if (Var != null && (Var < min || Var > max))
            {
                Var = null;
            }
        }
    }
}
