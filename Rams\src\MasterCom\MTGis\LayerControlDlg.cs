﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.IO;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using MapWinGIS;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.ZTFunc;
using MasterCom.RAMS.ZTFunc.ZTLastWeakRoadAna;
using MasterCom.RAMS.ZTFunc.ZTLastWeakRoadData;
using MasterCom.Util;

namespace MasterCom.MTGis
{
    public partial class LayerControlDlg : BaseDialog
    {
        public LayerControlDlg()
        {
            InitializeComponent();
            ColVisible.Tag = "Visible";
            ColName.Tag = "Name";
            ColLabel.Tag = "Label";
            ColNote.Tag = "Note";
        }
        public SimpleButton ApplyButton
        {
            get
            {
                return btnApply;
            }
        }
        
        private List<SfLayerInfo> sLayerList = null;
        private List<CustomDrawLayer> custLayers = null;
        private List<LayerBase> baseLayers = null;
        private AxMapWinGIS.AxMap mapControl = null;
        private MapForm mapForm = null;
        public event EventHandler BaseLayers_Changed;//BaseLayers changed
        public event EventHandler CustomDrawLayers_Changed;  //CustomDrawLayers数据源发生改变通知
        public event EventHandler SfLayerInfo_AllLayerList_Changed;//AllLayerList数据源发生改变统计
        internal void FillCurrentLayers(MapForm mapForm,AxMapWinGIS.AxMap mapControl, List<SfLayerInfo> sfLayers,List<CustomDrawLayer> clayers,List<LayerBase> blayers)
        {
            this.mapForm = mapForm;
            this.mapControl = mapControl;
            dataGridView.RowCount = sfLayers.Count + clayers.Count;
            sLayerList = sfLayers;
            custLayers = clayers;
            baseLayers = blayers;
            dataGridView.Invalidate();
        }

        public event EventHandler CustomBaseLayers_Changed;//CustomBaseLayers changed
        private List<LayerBase> customBaseLayers = null;//新的图层合集
        internal void FillCurrentLayers(MapForm mapForm, AxMapWinGIS.AxMap mapControl, List<SfLayerInfo> sfLayers, List<CustomDrawLayer> clayers, List<LayerBase> blayers, List<LayerBase> customblayers)
        {
            this.mapForm = mapForm;
            this.mapControl = mapControl;
            dataGridView.RowCount = sfLayers.Count + clayers.Count + customblayers.Count + blayers.Count;
            sLayerList = sfLayers;
            custLayers = clayers;
            baseLayers = blayers;
            customBaseLayers = customblayers;
            dataGridView.Invalidate();
        }

        private void dataGridView_CellValueNeeded(object sender, DataGridViewCellValueEventArgs e)
        {
            int revertRowIndex = sLayerList.Count + custLayers.Count + customBaseLayers.Count + baseLayers.Count - e.RowIndex - 1;
            DataGridViewColumn column = dataGridView.Columns[e.ColumnIndex];
            if (column.Tag.Equals("Visible"))
            {
                setVisible(e, revertRowIndex);
            }
            else if (column.Tag.Equals("Name"))
            {
                setName(e, revertRowIndex);
            }
            else if (column.Tag.Equals("Label"))
            {
                setLabel(e, revertRowIndex);
            }
            else if (column.Tag.Equals("Note"))
            {
                setNote(e, revertRowIndex);
            }
        }

        private void setNote(DataGridViewCellValueEventArgs e, int revertRowIndex)
        {
            if (revertRowIndex < sLayerList.Count && sLayerList[revertRowIndex].vecItem.is_road_layer == 1)
            {
                e.Value = "道路图层";
            }
            else
            {
                e.Value = "";
            }
        }

        private void setLabel(DataGridViewCellValueEventArgs e, int revertRowIndex)
        {
            if (revertRowIndex < sLayerList.Count && sLayerList[revertRowIndex].vecItem.label_show == 1)
            {
                e.Value = imageList.Images[0];
            }
            else
            {
                e.Value = imageList.Images[1];
            }
        }

        private void setName(DataGridViewCellValueEventArgs e, int revertRowIndex)
        {
            if (revertRowIndex < sLayerList.Count)
            {
                e.Value = sLayerList[revertRowIndex].vecItem.name;
            }
            else if (revertRowIndex < sLayerList.Count + baseLayers.Count)
            {
                e.Value = baseLayers[revertRowIndex - sLayerList.Count].getName();
            }
            else if (revertRowIndex < sLayerList.Count + baseLayers.Count + custLayers.Count)
            {
                e.Value = custLayers[revertRowIndex - sLayerList.Count - baseLayers.Count].Name;
            }
            else
            {
                e.Value = customBaseLayers[revertRowIndex - sLayerList.Count - baseLayers.Count - custLayers.Count].getName();
            }
        }

        private void setVisible(DataGridViewCellValueEventArgs e, int revertRowIndex)
        {
            if (revertRowIndex < sLayerList.Count)
            {
                e.Value = sLayerList[revertRowIndex].vecItem.visible == 1;
            }
            else if (revertRowIndex < sLayerList.Count + baseLayers.Count)
            {
                e.Value = baseLayers[revertRowIndex - sLayerList.Count].Enabled;
            }
            else if (revertRowIndex < sLayerList.Count + baseLayers.Count + custLayers.Count)
            {
                e.Value = custLayers[revertRowIndex - sLayerList.Count - baseLayers.Count].Enabled;
            }
            else
            {
                e.Value = customBaseLayers[revertRowIndex - sLayerList.Count - baseLayers.Count - custLayers.Count].IsVisible;
            }
        }

        CommonShpLayerSetProperties shpLayerCommonProp = null;
        CommonCustLayerSetProperties custLayerCommonProp = null;
        CommonCustBaseLayerSetProperties custBaseLayerCommonProp = null;
        CommonBaseLayerSetProperties baseLayerCommonProp = null;
        private void dataGridView_SelectionChanged(object sender, EventArgs e)
        {
            if (dataGridView.SelectedRows.Count <= 0)
            {
                return;
            }

            List<MTLayerPropUserControl> propControlList = new List<MTLayerPropUserControl>();
            int rowIndex = dataGridView.SelectedRows[0].Index;
            int revertRowIndex = sLayerList.Count + custLayers.Count + customBaseLayers.Count + baseLayers.Count - rowIndex - 1;
            if (revertRowIndex < sLayerList.Count)
            {
                setShpLayerProperties(propControlList, revertRowIndex);
            }
            else if (revertRowIndex < sLayerList.Count + baseLayers.Count)
            {
                setCustBaseLayerProperties(propControlList, revertRowIndex);
            }
            else if (revertRowIndex < sLayerList.Count + baseLayers.Count + custLayers.Count)
            {
                setCustLayerProperties(propControlList, revertRowIndex);
            }
            else if (revertRowIndex < sLayerList.Count + baseLayers.Count + custLayers.Count + customBaseLayers.Count)
            {
                setBaseLayerProperties(propControlList, revertRowIndex);
            }
            refreshPropControlTabs(propControlList);
        }

        private void setBaseLayerProperties(List<MTLayerPropUserControl> propControlList, int revertRowIndex)
        {
            int layerIdx = revertRowIndex - sLayerList.Count - baseLayers.Count - custLayers.Count;
            changBtnUpDownState(layerIdx, customBaseLayers.Count);
            LayerBase custBaseLayer = customBaseLayers[layerIdx];
            List<MTLayerPropUserControl> customControls = getBaseCustomControls(custBaseLayer);
            if (baseLayerCommonProp == null)
            {
                baseLayerCommonProp = new CommonBaseLayerSetProperties();
                baseLayerCommonProp.SetMapControl(this.mapControl);
                baseLayerCommonProp.LayerPropertyChanged += new EventHandler(baselayerPropertyChanged);
            }
            customControls.Insert(0, baseLayerCommonProp);
            foreach (MTLayerPropUserControl control in customControls)
            {
                control.Setup(custBaseLayer);
            }
            propControlList.AddRange(customControls);
        }

        private void setCustLayerProperties(List<MTLayerPropUserControl> propControlList, int revertRowIndex)
        {
            int layerIdx = revertRowIndex - sLayerList.Count - baseLayers.Count;
            changBtnUpDownState(layerIdx, custLayers.Count);
            CustomDrawLayer custLayer = custLayers[layerIdx];
            List<MTLayerPropUserControl> customControls = getCustomControls(custLayer);
            if (custLayerCommonProp == null)
            {
                custLayerCommonProp = new CommonCustLayerSetProperties();
                custLayerCommonProp.SetMapControl(this.mapControl);
                custLayerCommonProp.LayerPropertyChanged += new EventHandler(custlayerPropertyChanged);
            }
            customControls.Insert(0, custLayerCommonProp);
            foreach (MTLayerPropUserControl control in customControls)
            {
                control.Setup(custLayer);
            }
            propControlList.AddRange(customControls);
        }

        private void setCustBaseLayerProperties(List<MTLayerPropUserControl> propControlList, int revertRowIndex)
        {
            int layerIdx = revertRowIndex - sLayerList.Count;
            changBtnUpDownState(layerIdx, baseLayers.Count);
            LayerBase bLayer = baseLayers[layerIdx];
            List<MTLayerPropUserControl> customControls = getBaseCustomControls(bLayer);
            if (custBaseLayerCommonProp == null)
            {
                custBaseLayerCommonProp = new CommonCustBaseLayerSetProperties();
                custBaseLayerCommonProp.SetMapControl(this.mapControl);
                custBaseLayerCommonProp.LayerPropertyChanged += new EventHandler(custBaselayerPropertyChanged);
            }
            customControls.Insert(0, custBaseLayerCommonProp);
            foreach (MTLayerPropUserControl control in customControls)
            {
                control.Setup(bLayer);
            }
            propControlList.AddRange(customControls);
        }

        private void setShpLayerProperties(List<MTLayerPropUserControl> propControlList, int revertRowIndex)
        {
            SfLayerInfo sfLayerInfo = sLayerList[revertRowIndex];
            changBtnUpDownState(revertRowIndex, sLayerList.Count);
            if (shpLayerCommonProp == null)
            {
                shpLayerCommonProp = new CommonShpLayerSetProperties();
                shpLayerCommonProp.SetMapControl(this.mapControl);
                shpLayerCommonProp.LayerPropertyChanged += new EventHandler(shplayerPropertyChanged);
            }
            shpLayerCommonProp.Setup(sfLayerInfo);
            propControlList.Add(shpLayerCommonProp);
        }

        private void changBtnUpDownState(int rowIndex, int listCount)
        {
            if (rowIndex == 0)//底层
            {
                miBtnUp.Enabled = true;
                miBtnDown.Enabled = false;
            }
            else if (rowIndex == listCount - 1)//顶层
            {
                miBtnUp.Enabled = false;
                miBtnDown.Enabled = true;
            }
            else
            {
                miBtnUp.Enabled = miBtnDown.Enabled = true;
            }
            if (listCount <= 1)
            {
                miBtnUp.Enabled = miBtnDown.Enabled = false;
            }
        }

        void shplayerPropertyChanged(object sender, EventArgs e)
        {
            if (e is VecEventArgs)
            {
                VecEventArgs vArg = e as VecEventArgs;
                applyLayerShowStyle(vArg.sfInfo);
            }
        }
        void custlayerPropertyChanged(object sender, EventArgs e)
        {
            applyLayerShowStyle();
        }
        void custBaselayerPropertyChanged(object sender, EventArgs e)
        {
            applyLayerShowStyle();
        }
        void baselayerPropertyChanged(object sender, EventArgs e)
        {
            applyLayerShowStyle();
        }
        private void applyLayerShowStyle()
        {
            mapForm.SetRedrawBuffFlag();
            mapControl.Redraw();
        }

        private Dictionary<string, LinePatternItem> patternDic = makeLinePatternDic();
        private static Dictionary<string, LinePatternItem> makeLinePatternDic()
        {
            Dictionary<string, LinePatternItem> dic = new Dictionary<string, LinePatternItem>();
            List<LinePatternItem> linePatternList = LinePatternItem.InitCustomLineType();
            for (int i = 0; i < linePatternList.Count; i++)
            {
                LinePatternItem pattern = linePatternList[i];
                dic[pattern.Name] = pattern;
            }
            return dic;
        }
        public void ApplyLayerShowStyle(SfLayerInfo sfLayerInfo)
        {
            applyLayerShowStyle(sfLayerInfo);
        }

        private void applyLayerShowStyle(SfLayerInfo sfLayerInfo)
        {
            this.PointMarkHook(sfLayerInfo);
            int hnd = sfLayerInfo.hnd;
            VecLayerItem item = sfLayerInfo.vecItem;
            mapControl.set_LayerVisible(hnd, item.visible == 1);
            mapControl.set_ShapeLayerDrawFill(hnd, item.style_bg_fill == 1);
            sfLayerInfo.sf.Categories.Clear();

            fillLayerBackground(sfLayerInfo, hnd, item);

            fillLayerRoad(sfLayerInfo, hnd, item);

            setRangeVisible(hnd, item);
  
            fillLayerLabel(sfLayerInfo, item);
        }

        private void fillLayerLabel(SfLayerInfo sfLayerInfo, VecLayerItem item)
        {
            sfLayerInfo.sf.Labels.Clear();
            sfLayerInfo.sf.Labels.ClearCategories();
            if (item.label_show == 1)
            {
                if (sfLayerInfo.sf.ShapefileType == ShpfileType.SHP_POLYGON)
                {
                    sfLayerInfo.sf.Labels.Generate(string.Format("[{0}]", item.label_field), tkLabelPositioning.lpInteriorPoint, false);
                }
                int lbFldIdx = MapOperation.GetColumnFieldIndex(sfLayerInfo.sf, item.label_field);
                if (lbFldIdx != -1)
                {
                    LabelCategory lc = sfLayerInfo.sf.Labels.AddCategory("base");
                    for (int i = 0; i < sfLayerInfo.sf.NumShapes; i++)
                    {
                        addLayerLabel(sfLayerInfo, item, lbFldIdx, i);
                    }
                    lc.FontColor = (uint)ColorTranslator.ToOle(item.label_font_color);
                    lc.FontSize = item.label_font_size;
                }
            }
        }

        private static void addLayerLabel(SfLayerInfo sfLayerInfo, VecLayerItem item, int lbFldIdx, int i)
        {
            string text = sfLayerInfo.sf.get_CellValue(lbFldIdx, i).ToString();
            if (text != null && text.Length != 0)
            {
                MapWinGIS.Shape shp = sfLayerInfo.sf.get_Shape(i);
                if (item.is_road_layer != 1)
                {
                    MapWinGIS.Point pnt = shp.Center;
                    sfLayerInfo.sf.Labels.AddLabel(text, pnt.x, pnt.y, 0.0, 0);
                    sfLayerInfo.sf.Labels.Alignment = tkLabelAlignment.laTopCenter;
                    sfLayerInfo.sf.Labels.Positioning = tkLabelPositioning.lpCentroid;
                }
                else
                {
                    int pntCnt = shp.numPoints;
                    if (pntCnt < 10)
                    {
                        MapWinGIS.Point pnt = shp.get_Point((pntCnt + 1) / 2 - 1);
                        sfLayerInfo.sf.Labels.AddLabel(text, pnt.x, pnt.y, 0.0, 0);
                    }
                    else
                    {
                        addBigCntLabel(sfLayerInfo, text, shp, pntCnt);
                    }
                }
            }
        }

        private static void addBigCntLabel(SfLayerInfo sfLayerInfo, string text, MapWinGIS.Shape shp, int pntCnt)
        {
            int idxCnt = (int)Math.Floor((double)(pntCnt / 20));
            if (idxCnt == 1)
            {
                MapWinGIS.Point pnt = shp.get_Point((pntCnt + 1) / 2 - 1);
                sfLayerInfo.sf.Labels.AddLabel(text, pnt.x, pnt.y, 0.0, 0);
            }
            else
            {
                MapWinGIS.Point lastLabelPnt = null;
                for (int li = 0; li < idxCnt; li++)
                {
                    MapWinGIS.Point pnt = shp.get_Point(li * 20);
                    if (lastLabelPnt == null || Math.Abs(pnt.x - lastLabelPnt.x) >= 1.2 || Math.Abs(pnt.y - lastLabelPnt.y) >= 1.2)
                    {
                        lastLabelPnt = pnt;
                        sfLayerInfo.sf.Labels.AddLabel(text, pnt.x, pnt.y, 0.0, 0);
                    }
                }
            }
        }

        private void setRangeVisible(int hnd, VecLayerItem item)
        {
            if (item.visible_range_enable == 1)
            {
                mapControl.set_LayerDynamicVisibility(hnd, true);
                mapControl.set_LayerMinVisibleScale(hnd, item.visible_range_min);
                mapControl.set_LayerMaxVisibleScale(hnd, item.visible_range_max);
            }
            else
            {
                mapControl.set_LayerDynamicVisibility(hnd, false);
            }
        }

        private void fillLayerRoad(SfLayerInfo sfLayerInfo, int hnd, VecLayerItem item)
        {
            mapControl.set_ShapeLayerLineWidth(hnd, item.style_line_width);
            mapControl.set_ShapeLayerLineColor(hnd, (uint)ColorTranslator.ToOle(item.style_line_color));
            if (item.is_road_layer == 1)
            {
                sfLayerInfo.sf.SelectionAppearance = tkSelectionAppearance.saDrawingOptions;
                sfLayerInfo.sf.SelectionDrawingOptions.LineWidth = 3;
                sfLayerInfo.sf.SelectionDrawingOptions.LineColor = (uint)ColorTranslator.ToOle(Color.FromArgb(255, 0, 0));
                sfLayerInfo.sf.SelectionDrawingOptions.LineVisible = true;
                sfLayerInfo.sf.SelectionDrawingOptions.LineTransparency = 255;
                //===pattern
                if (item.style_line_patten != null)
                {
                    setLinePatten(sfLayerInfo, hnd, item);
                }
            }
        }

        private void setLinePatten(SfLayerInfo sfLayerInfo, int hnd, VecLayerItem item)
        {
            LinePatternItem linePatternItem;
            if (patternDic.TryGetValue(item.style_line_patten, out linePatternItem))
            {
                if (linePatternItem.UseLinePattern)
                {
                    sfLayerInfo.sf.Categories.Clear();
                    ShapefileCategory ct = sfLayerInfo.sf.Categories.Add(item.style_line_patten);
                    ct.DrawingOptions.LinePattern = linePatternItem.LinePattern;
                    ct.DrawingOptions.UseLinePattern = true;
                    for (int x = 0; x < sfLayerInfo.sf.NumShapes; x++)
                    {
                        sfLayerInfo.sf.set_ShapeCategory(x, 0);
                    }
                }
                else
                {
                    sfLayerInfo.sf.Categories.Clear();
                    if (item.style_line_patten != "基本")
                    {
                        mapControl.set_ShapeLayerLineWidth(hnd, linePatternItem.LineWidth);
                        mapControl.set_ShapeLayerLineColor(hnd, (uint)ColorTranslator.ToOle(linePatternItem.LineColor));
                    }
                }
            }
        }

        private void fillLayerBackground(SfLayerInfo sfLayerInfo, int hnd, VecLayerItem item)
        {
            if (item.style_bg_fill == 1)
            {
                if (item.style_bg_color_column != null && item.style_bg_color_column != "")
                {
                    int lbFldIdx = MapOperation.GetColumnFieldIndex(sfLayerInfo.sf, item.style_bg_color_column);
                    if (lbFldIdx != -1)
                    {
                        Dictionary<uint, int> categoryByColor = new Dictionary<uint, int>();
                        addCategoryByColor(sfLayerInfo, lbFldIdx, categoryByColor);
                        setCategoryToSFLayer(sfLayerInfo, lbFldIdx, categoryByColor);
                    }
                }
                else
                {
                    mapControl.set_ShapeLayerFillTransparency(hnd, item.style_bg_opaque);
                    mapControl.set_ShapeLayerFillColor(hnd, (uint)ColorTranslator.ToOle(item.style_bg_color));
                }
            }
        }

        private static void setCategoryToSFLayer(SfLayerInfo sfLayerInfo, int lbFldIdx, Dictionary<uint, int> categoryByColor)
        {
            for (int i = 0; i < sfLayerInfo.sf.NumShapes; i++)
            {
                string strv = sfLayerInfo.sf.get_CellValue(lbFldIdx, i) as string;
                Color bgcolor = MapOperation.ParseColorFrom(strv);
                uint color = (uint)ColorTranslator.ToOle(bgcolor);// your custom function
                int catIndex = categoryByColor[color];
                sfLayerInfo.sf.set_ShapeCategory(i, catIndex);
            }
        }

        private void addCategoryByColor(SfLayerInfo sfLayerInfo, int lbFldIdx, Dictionary<uint, int> categoryByColor)
        {
            for (int i = 0; i < sfLayerInfo.sf.NumShapes; i++)
            {
                string strv = sfLayerInfo.sf.get_CellValue(lbFldIdx, i) as string;
                Color bgcolor = MapOperation.ParseColorFrom(strv);
                uint color = (uint)ColorTranslator.ToOle(bgcolor);// your custom function
                if (!categoryByColor.ContainsKey(color))
                {
                    string name = sfLayerInfo.sf.Categories.Count.ToString();
                    MapWinGIS.ShapefileCategory cat = sfLayerInfo.sf.Categories.Add(name);
                    if (cat != null)
                    {
                        cat.DrawingOptions.FillColor = color;
                        categoryByColor.Add(color, sfLayerInfo.sf.Categories.Count - 1);
                    }
                }
            }
        }

        private void refreshPropControlTabs(List<MTLayerPropUserControl> propControlList)
        {
            tabMain.TabPages.Clear();
            foreach (MTLayerPropUserControl control in propControlList)
            {
                TabPage tp = new TabPage();
                tp.Text = control.Text;
                tp.Controls.Add(control);
                control.Dock = DockStyle.Fill;
                tabMain.TabPages.Add(tp);
            }
        }
        private List<MTLayerPropUserControl> getBaseCustomControls(LayerBase bLayer)
        {
            List<MTLayerPropUserControl> propControlList = new List<MTLayerPropUserControl>();
            //可考虑在基类LayerBase中添加AddProperties()的方法,各个子类图层中重写添加对应Properties
            if (bLayer is CustomThemeLayer)
            {
                propControlList.Add(new CustomThemeLayerProperties());
            }
            if (bLayer is MapCellLayer)
            {
                propControlList.Add(new MapCellLayerBaseProperties());
                propControlList.Add(new MapCellLayerBTSProperties());
                propControlList.Add(new MapCellLayerCellProperties());
                propControlList.Add(new MapCellLayerAntennaProperties());
                propControlList.Add(new MapCellLayerPlanningProperties());
                propControlList.Add(new MapCellLayerRepeaterProperties());
            }
            else if (bLayer is MapTDCellLayer)
            {
                propControlList.Add(new MapTDCellLayerBaseProperties());
                propControlList.Add(new MapTDCellLayerBTSProperties());
                propControlList.Add(new MapTDCellLayerCellProperties());
                propControlList.Add(new MapTDCellLayerAntennaProperties());
                propControlList.Add(new MapTDCellLayerPlanningProperties());
            }
            else if (bLayer is MapWCellLayer)
            {
                propControlList.Add(new MapWCellLayerBaseProperties());
                propControlList.Add(new MapWCellLayerBTSProperties());
                propControlList.Add(new MapWCellLayerCellProperties());
                propControlList.Add(new MapWCellLayerAntennaProperties());
                propControlList.Add(new MapWCellLayerPlanningProperties());
            }
            else if (bLayer is MapLTECellLayer)
            {
                propControlList.Add(new MapLTECellLayerBaseProperties());
                propControlList.Add(new MapLTECellLayerBTSPropertis());
                propControlList.Add(new MapLTECellLayerCellProperties());
                propControlList.Add(new MapLTECellLayerAntennaProperties());
                propControlList.Add(new MapLTECellLayerPlanningPropertis());
                propControlList.Add(new MapLTECellLayerFddPropertis());
#if NBIOTLayer
                propControlList.Add(new MapLTECellLayerNBIOTPropertis());
#endif
            }
            else if (bLayer is MapDTLayer)
            {
                propControlList.Add(new MapDTLayerSerialProperties());
                propControlList.Add(new MapDTLayerEventProperties());
            }
            else if (bLayer is MapGridLayer)
            {
                propControlList.Add(new MapGridLayerProperties());
            }
            else if (bLayer is MapCQTLayer)
            {
                propControlList.Add(new MapCQTLayerProperties());
            }
            else if (bLayer is MapNRCellLayer)
            {
                propControlList.Add(new MapNRCellLayerBaseProperties());
                propControlList.Add(new MapNRCellLayerBTSPropertis());
                propControlList.Add(new MapNRCellLayerCellProperties());
                propControlList.Add(new MapNRCellLayerAntennaProperties());
            }

            return propControlList;
        }
        private List<MTLayerPropUserControl> getCustomControls(CustomDrawLayer custLayer)
        {
            List<MTLayerPropUserControl> propControlList = new List<MTLayerPropUserControl>();
            if (custLayer is MapFormBlindLayer)
            {
                propControlList.Add(new MapFormBlindLayerProperties());
            }
            else if (custLayer is MapFormCDCellLayer)
            {
                propControlList.Add(new MapFormCDCellLayerBaseProperties());
                propControlList.Add(new MapFormCDCellLayerBTSProperties());
                propControlList.Add(new MapFormCDCellLayerCellProperties());
                propControlList.Add(new MapFormCDCellLayerAntennaProperties());
            }
            else if (custLayer is MapFormCompareHisShowLayer)
            {
                propControlList.Add(new CompareHisGridShowLayerProperties());
            }
            else if (custLayer is MapFormStreetInjectAnaLayer)
            {
                propControlList.Add(new StreetInjectionAnaLayerProperties());
                propControlList.Add(new StreetInjectionAnaLayerTableProperties());
            }
            else if (custLayer is MapFormLastWeakRoadLayer)
            {
                propControlList.Add(new LastWeakRoadLayerProperties());
                propControlList.Add(new TdLastWeakRoadLayerProperties());
            }
            else if (custLayer is MapFormLastWeakRoadLayerData)
            {
                propControlList.Add(new LastWeakRoadLayerPropertiesData());
                propControlList.Add(new TdLastWeakRoadLayerPropertiesData());
            }
            else if (custLayer is MapFormCellSplitLayer)
            {
                propControlList.Add(new CellSplitLayerProperties());
            }
            else if (custLayer is MapFormNoMainCellBlockLayer)
            {
                propControlList.Add(new MapFormNoMainCellBlockProperties());
            }
            else if (custLayer is MapFormGSMDataRateLowQualBlockLayer)
            {
                propControlList.Add(new MapFormGSMDataRateLowQualBlockLayerProperties());
            }
            else if (custLayer is MapFormGSMDataDLRateBlockLayer)
            {
                propControlList.Add(new MapFormGSMDataDLRateBlockLayerProperties());
            }
            else if (custLayer is MapFormPilotFrequencyPolluteBlockLayer)
            {
                propControlList.Add(new MapFormPilotFrequencyPolluteBlockProperties());
            }
            else if (custLayer is AlarmRecordLayer)
            {
                propControlList.Add(new AlarmRecordLayerPropertis());
            }
            
            return propControlList;
        }

        private void btnApply_Click(object sender, EventArgs e)
        {
            //listen by LayerControlDlg
        }

        private void LayerControlDlg_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (e.CloseReason == CloseReason.UserClosing)
            {
                this.Visible = false;
                e.Cancel = true;
                mapForm.updateMap();
                if (Owner!=null)
                {
                    Owner.Activate();
                }
            }
        }

        private void dataGridView_CellContentClick(object sender, DataGridViewCellEventArgs e)
        {
            int rowAt = e.RowIndex;
            if (rowAt == -1)
            {
                return;
            }
            DataGridViewColumn column = dataGridView.Columns[e.ColumnIndex];
            int rowIndex = rowAt;
            int revertRowIndex = sLayerList.Count + custLayers.Count + customBaseLayers.Count + baseLayers.Count - rowIndex - 1;
            if (revertRowIndex < sLayerList.Count)
            {
                setSFLayerVisible(column, revertRowIndex);
            }
            else if (revertRowIndex < sLayerList.Count + baseLayers.Count)
            {
                setBaseLayerVisible(column, revertRowIndex);
            }
            else if (revertRowIndex < sLayerList.Count + baseLayers.Count + custLayers.Count)
            {
                setCustLayerVisible(column, revertRowIndex);
            }
            else
            {
                setCustBaseLayerVisible(column, revertRowIndex);
            }
        }

        private void setCustBaseLayerVisible(DataGridViewColumn column, int revertRowIndex)
        {
            LayerBase custBaseLayer = customBaseLayers[revertRowIndex - sLayerList.Count - baseLayers.Count - custLayers.Count];
            if (column.Tag.Equals("Visible"))
            {
                custBaseLayer.IsVisible = !custBaseLayer.IsVisible;
                custBaseLayer.Enabled = custBaseLayer.IsVisible;

                if (CustomBaseLayers_Changed != null)
                {
                    CustomBaseLayers_Changed(custBaseLayer, EventArgs.Empty);
                }
            }
        }

        private void setCustLayerVisible(DataGridViewColumn column, int revertRowIndex)
        {
            CustomDrawLayer custLayer = custLayers[revertRowIndex - sLayerList.Count - baseLayers.Count];
            if (column.Tag.Equals("Visible"))
            {
                custLayer.IsVisible = !custLayer.IsVisible;
                custLayer.Enabled = custLayer.IsVisible;

                if (CustomDrawLayers_Changed != null)
                {   // 通知MapForm,CustomDrawLayer发生变化，更新小区按钮状态
                    CustomDrawLayers_Changed(custLayer, EventArgs.Empty);
                }
            }
        }

        private void setBaseLayerVisible(DataGridViewColumn column, int revertRowIndex)
        {
            LayerBase bLayer = baseLayers[revertRowIndex - sLayerList.Count];
            if (column.Tag.Equals("Visible"))
            {
                bLayer.IsVisible = !bLayer.IsVisible;
                bLayer.Enabled = bLayer.IsVisible;

                if (BaseLayers_Changed != null)
                {
                    BaseLayers_Changed(bLayer, EventArgs.Empty);
                }
            }
        }

        private void setSFLayerVisible(DataGridViewColumn column, int revertRowIndex)
        {
            SfLayerInfo sfLayerInfo = sLayerList[revertRowIndex];
            if (column.Tag.Equals("Visible"))
            {
                if (sfLayerInfo.vecItem.visible == 1)
                {
                    sfLayerInfo.vecItem.visible = 0;
                }
                else
                {
                    sfLayerInfo.vecItem.visible = 1;
                }
                applyLayerShowStyle(sfLayerInfo);
            }
            else if (column.Tag.Equals("Label"))
            {
                if (sfLayerInfo.vecItem.label_show == 1)
                {
                    sfLayerInfo.vecItem.label_show = 0;
                }
                else
                {
                    sfLayerInfo.vecItem.label_show = 1;
                }
                applyLayerShowStyle(sfLayerInfo);
            }
        }

        private void tsBtnAddLayer_Click(object sender, EventArgs e)
        {
            OpenFileDialog dlg = new OpenFileDialog();
            dlg.Filter = FilterHelper.Shp;
            dlg.RestoreDirectory = true;
            dlg.Multiselect = true;
            if (dlg.ShowDialog(this) == DialogResult.OK)
            {
                string[] filenames = dlg.FileNames;
                for (int i = 0; i < filenames.Length; i++)
                {
                    string filename = filenames[i];
                    VecLayerItem vLayer = new VecLayerItem();
                    vLayer.file_path_name = filename;
                    vLayer.name = Path.GetFileNameWithoutExtension(filename);
                    Shapefile sf = new Shapefile();
                    if (sf.Open(filename, null))
                    {
                        int hnd = mapControl.AddLayer(sf, true);
                        SfLayerInfo sfInfo = new SfLayerInfo();
                        sfInfo.hnd = hnd;
                        sfInfo.name = vLayer.name;
                        sfInfo.sf = sf;
                        sfInfo.vecItem = vLayer;
                        sLayerList.Add(sfInfo);
                        applyLayerShowStyle(sfInfo);
                    }
                    else
                    {
                        MessageBox.Show("未能打开文件:" + filename);
                    }
                }
                dataGridView.RowCount = sLayerList.Count + custLayers.Count + customBaseLayers.Count + baseLayers.Count;
            }
        }

        private void miBtnUp_Click(object sender, EventArgs e)
        {
            moveCurSelLayerOneLevel(true);
        }

        private void miBtnDown_Click(object sender, EventArgs e)
        {
            moveCurSelLayerOneLevel(false);
        }

        /// <summary>
        /// 往上/往下移动图层一级
        /// </summary>
        /// <param name="isMoveUp">ture:往上移动一层，false:往下移动一层</param>
        private void moveCurSelLayerOneLevel(bool isMoveUp)
        {
            if (dataGridView.SelectedRows.Count > 0)
            {
                int rowIndex = dataGridView.SelectedRows[0].Index;
                int revertRowIndex = sLayerList.Count + custLayers.Count + customBaseLayers.Count + baseLayers.Count - rowIndex - 1;
                if (revertRowIndex < sLayerList.Count)
                {
                    moveLayer(isMoveUp, revertRowIndex);
                    int targetIdx = setRowSelected(isMoveUp, rowIndex, revertRowIndex);               
                    SfLayerInfo tempLayer = sLayerList[targetIdx];
                    sLayerList.RemoveAt(targetIdx);
                    sLayerList.Insert(revertRowIndex, tempLayer);
                }
                else if (revertRowIndex < sLayerList.Count + baseLayers.Count)
                {
                    int layerIdx = revertRowIndex - sLayerList.Count;
                    int targetIdx = setRowSelected(isMoveUp, rowIndex, layerIdx);
                    LayerBase tempLayer = baseLayers[targetIdx];
                    baseLayers.RemoveAt(targetIdx);
                    baseLayers.Insert(layerIdx, tempLayer);
                }
                else if (revertRowIndex < sLayerList.Count + baseLayers.Count + custLayers.Count)
                {
                    int layerIdx = revertRowIndex - sLayerList.Count - baseLayers.Count;
                    int targetIdx = setRowSelected(isMoveUp, rowIndex, layerIdx);
                    CustomDrawLayer tempLayer = custLayers[targetIdx];
                    custLayers.RemoveAt(targetIdx);
                    custLayers.Insert(layerIdx, tempLayer);
                }
                else
                {
                    int layerIdx = revertRowIndex - sLayerList.Count - baseLayers.Count - custLayers.Count;
                    int targetIdx = setRowSelected(isMoveUp, rowIndex, layerIdx);
                    LayerBase tempLayer = customBaseLayers[targetIdx];
                    customBaseLayers.RemoveAt(targetIdx);
                    customBaseLayers.Insert(layerIdx, tempLayer);
                }
                dataGridView.Invalidate();
            }
        }

        private void moveLayer(bool isMoveUp, int revertRowIndex)
        {
            if (isMoveUp)
            {
                mapControl.MoveLayerUp(revertRowIndex);
            }
            else
            {
                mapControl.MoveLayerDown(revertRowIndex);
            }
        }

        private int setRowSelected(bool isMoveUp, int rowIndex, int layerIdx)
        {
            int targetIdx = -1;
            if (isMoveUp)
            {
                targetIdx = layerIdx + 1;
                if (rowIndex >= 1)
                {
                    dataGridView.Rows[rowIndex - 1].Selected = true;
                }
            }
            else
            {
                targetIdx = layerIdx - 1;
                if (rowIndex < dataGridView.Rows.Count - 1)
                {
                    dataGridView.Rows[rowIndex + 1].Selected = true;
                }
            }

            return targetIdx;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            mapForm.updateMap();
            Visible = false;
            if (Owner!=null)
            {
                Owner.Activate();
            }
        }

        private void PointMarkHook(SfLayerInfo sfInfo)
        {
            if (!sfInfo.name.StartsWith("点_") || sfInfo.sf.ShapefileType != ShpfileType.SHP_POINT)
            {
                return;
            }
            Shapefile shpFile = sfInfo.sf;

            shpFile.Labels.OffsetX = 12;
            shpFile.Labels.FramePaddingX = 0;
            shpFile.Labels.FramePaddingY = 0;
            shpFile.Labels.FrameVisible = true;
            shpFile.DefaultDrawingOptions.PointSize = 35;
        }

        private void btnSaveIt_Click(object sender, EventArgs e)
        {
            if (SfLayerInfo_AllLayerList_Changed != null)
            {//通知MapForm allLayerList 已经发生变化，进行保存操作。
                SfLayerInfo_AllLayerList_Changed(sLayerList, EventArgs.Empty);
            }
        }

        private void tsBtnRemoveLayer_Click(object sender, EventArgs e)
        {
            if (dataGridView.SelectedRows.Count > 0)
            {
                int rowIndex = dataGridView.SelectedRows[0].Index;
                int revertRowIndex = sLayerList.Count + customBaseLayers.Count + custLayers.Count + baseLayers.Count - rowIndex - 1;
                if (revertRowIndex < sLayerList.Count)
                {
                    mapControl.RemoveLayer(revertRowIndex);
                    sLayerList.RemoveAt(revertRowIndex);
                    if (rowIndex > 1)
                    {
                        dataGridView.Rows[rowIndex - 1].Selected = true;
                    }
                }
                else if (revertRowIndex < sLayerList.Count + baseLayers.Count)
                {
                    int layerIdx = revertRowIndex - sLayerList.Count;
                    baseLayers.RemoveAt(layerIdx);
                }
                else if (revertRowIndex < sLayerList.Count + baseLayers.Count + custLayers.Count)
                {
                    int layerIdx = revertRowIndex - sLayerList.Count - baseLayers.Count;
                    custLayers.RemoveAt(layerIdx);
                }
                else
                {
                    int layerIdx = revertRowIndex - sLayerList.Count - baseLayers.Count - custLayers.Count;
                    customBaseLayers.RemoveAt(layerIdx);
                }
                dataGridView.RowCount = sLayerList.Count + customBaseLayers.Count + custLayers.Count + baseLayers.Count;
            }
            dataGridView.Invalidate();
        }

        private void btnAddExcelPointLayer_Click(object sender, EventArgs e)
        {
            ImportFileForMapDlg importFileDlg = new ImportFileForMapDlg();
            if(DialogResult.OK == importFileDlg.ShowDialog())
            {
                DataTable dataTable = importFileDlg.GetGridDataTable();
                string layerName = importFileDlg.GetLayerName();
                CustomThemeLayer themeLayer = new CustomThemeLayer(layerName);
                themeLayer.SetGridDataInfo(dataTable);
                mapForm.AddLayerBase(themeLayer);
                dataGridView.RowCount = sLayerList.Count + customBaseLayers.Count + custLayers.Count + baseLayers.Count;
                dataGridView.Invalidate();
                dataGridView.ClearSelection();
                if(dataGridView.Rows.Count>0)
                {
                    dataGridView.Rows[0].Selected = true;
                }
                
            }
        }
    }
}
