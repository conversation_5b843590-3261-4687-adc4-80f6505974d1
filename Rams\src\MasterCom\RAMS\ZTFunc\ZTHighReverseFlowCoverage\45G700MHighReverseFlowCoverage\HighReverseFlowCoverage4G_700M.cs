﻿using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.ZTFunc.ZTHighReverseFlowCoverage;
using System.Windows.Forms;
using MasterCom.RAMS.ZTFunc.ZTHighReverseFlowCoverage._45G700MHighReverseFlowCoverage;

namespace MasterCom.RAMS.ZTFunc
{
    public class HighReverseFlowCoverage4G_700M : QueryBase
    {
        public HighReverseFlowCoverage4G_700M()
            : base(MainModel.GetInstance())
        {
        }

        public override bool IsNeedSetQueryCondition { get { return false; } }

        public override string Name
        {
            get { return "4G-5G 700M高倒流共覆盖"; }
        }

        CoverageCondition curCondition = null;
        protected override bool isValidCondition()
        {
            if (curCondition == null)
            {
                curCondition = HighReverseFlowCoverageConfig.Instance.LoadConfig();
                if (!string.IsNullOrEmpty(HighReverseFlowCoverageConfig.Instance.ErrMsg))
                {
                    HighReverseFlowCoverageConfig.Instance.WriteLogWithMsgBox(HighReverseFlowCoverageConfig.Instance.ErrMsg);
                }
            }

            HighReverseFlowCoverage4G_700MDlg dlg = new HighReverseFlowCoverage4G_700MDlg();
            dlg.SetCondition(curCondition);
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                curCondition = dlg.GetCondition();
                HighReverseFlowCoverageConfig.Instance.SaveConfig(curCondition);
                return true;
            }
            return false;
        }

        List<CellResult> nrCellResults;
        List<CellResult> lteCellResults;
        bool isValid = false;
        protected override void query()
        {
            try
            {
                HighReverseFlowCoverageConfig.Instance.WriteLog($"开始分析{Name}数据...", "info");
                WaitBox.Show("开始分析数据...", queryInThread);

                if (isValid)
                {
                    exportExcel(nrCellResults, lteCellResults);
                }
                HighReverseFlowCoverageConfig.Instance.WriteLog("分析完毕...", "info");
                nrCellResults = null;
                lteCellResults = null;
            }
            catch (Exception ex)
            {
                HighReverseFlowCoverageConfig.Instance.WriteLogWithMsgBox(ex);
            }
        }

        protected void queryInThread()
        {
            try
            {
                WaitBox.Text = "正在读取4/5G邻区对...";
                DiyQueryCellNeighbor query = new DiyQueryCellNeighbor();
                query.Query();
                var cellInfosDic = query.CellInfosDic;
                if (cellInfosDic.Count == 0)
                {
                    string errMsg = "没有读取到4G-5G邻区对信息";
                    HighReverseFlowCoverageConfig.Instance.WriteLogWithMsgBox(errMsg);
                    return;
                }

                var nrCellInfos = readNRParams();
                var lteCellInfos = readLTEParams();
                if (nrCellInfos.Count == 0 || lteCellInfos.Count == 0)
                {
                    string errMsg = "没有读取到'LTE'或'700M NR'工参信息";
                    HighReverseFlowCoverageConfig.Instance.WriteLogWithMsgBox(errMsg);
                    return;
                }

                WaitBox.Text = "正在匹配工参与邻区对信息...";
                CellNeighborHelper.DealCellParams(cellInfosDic, nrCellInfos, lteCellInfos
                    , out nrCellResults, out lteCellResults);

                isValid = insertResult(nrCellResults, lteCellResults);
            }
            catch (Exception ex)
            {
                HighReverseFlowCoverageConfig.Instance.WriteLogWithMsgBox(ex);
            }
            finally
            {
                System.Threading.Thread.Sleep(200);
                WaitBox.Close();
            }
        }

        #region 读取工参
        private Dictionary<long, CellParamInfo> readNRParams()
        {
            WaitBox.Text = "正在读取5G工参...";
            var query = new DiyQueryNRCellInfo();
            query.Query();
            HighReverseFlowCoverageConfig.Instance.WriteLog($"读取到NR工参[{query.TotalCount}]条, 无效数据{query.ErrTotalCount}条", "info");
            return query.CellInfos;
        }

        private Dictionary<long, CellParamInfo> readLTEParams()
        {
            WaitBox.Text = "正在读取4G工参...";
            var query = new DiyQueryLTECellInfo();
            query.Query();
            HighReverseFlowCoverageConfig.Instance.WriteLog($"读取到LTE工参[{query.TotalCount}]条, 无效数据{query.ErrTotalCount}条", "info");
            return query.CellInfos;
        }
        #endregion

        #region 结果插入数据库
        private bool insertResult(List<CellResult> nrCellResults, List<CellResult> lteCellResults)
        {
            bool isNRSuccess = insertNRResult(nrCellResults);
            bool isLTESuccess = insertLTEResult(lteCellResults);
            if (isNRSuccess && isLTESuccess)
            {
                return true;
            }
            return false;
        }

        private bool insertNRResult(List<CellResult> nrCellResults)
        {
            HighReverseFlowCoverageConfig.Instance.WriteLog("正在导入5G工参结果...", "info");
            var insert = new DiyInsertNRResultInfo(nrCellResults);

            insert.Bcp(curCondition.DBCond.SqlConnect);

            return judgeInsertResult("插入5G工参结果表失败", insert.ErrMsg);
        }

        private bool insertLTEResult(List<CellResult> lteCellResults)
        {
            HighReverseFlowCoverageConfig.Instance.WriteLog("正在导入4G工参结果...", "info");
            var insert = new DiyInsertLTEResultInfo(lteCellResults);
            insert.Bcp(curCondition.DBCond.SqlConnect);

            return judgeInsertResult("插入4G工参结果表失败", insert.ErrMsg);
        }

        private static bool judgeInsertResult(string desc, string errMsg)
        {
            if (!string.IsNullOrEmpty(errMsg))
            {
                HighReverseFlowCoverageConfig.Instance.WriteLog($"{desc}:{errMsg}", "error");
                return false;
            }
            return true;
        }
        #endregion

        #region 结果导出
        private void exportExcel(List<CellResult> nrCellResults, List<CellResult> lteCellResults)
        {
            HighReverseFlowCoverageConfig.Instance.WriteLog("正在导出Excel...", "info");
            List<NPOIRow> lteTables = new List<NPOIRow>();
            NPOIRow lteTitleRow = new NPOIRow();
            lteTitleRow.AddCellValue("省份");
            lteTitleRow.AddCellValue("省份ID");
            lteTitleRow.AddCellValue("地市");
            lteTitleRow.AddCellValue("地市ID");
            lteTitleRow.AddCellValue("区域");
            lteTitleRow.AddCellValue("网格");
            lteTitleRow.AddCellValue("基站号");
            lteTitleRow.AddCellValue("小区名");
            lteTitleRow.AddCellValue("覆盖类型");
            lteTitleRow.AddCellValue("TAC");
            lteTitleRow.AddCellValue("ECI");
            lteTitleRow.AddCellValue("TAC16");
            lteTitleRow.AddCellValue("ECI16");
            lteTitleRow.AddCellValue("编号");
            lteTables.Add(lteTitleRow);

            foreach (var lteCell in lteCellResults)
            {
                NPOIRow row = new NPOIRow();
                var lte = lteCell.CellInfo;
                row.AddCellValue(lte.Province);
                row.AddCellValue(lte.ProvinceID);
                row.AddCellValue(lte.City);
                row.AddCellValue(lte.CityID);
                row.AddCellValue(lte.Region);
                row.AddCellValue(lte.Grid);
                row.AddCellValue(lte.Enodebid);
                row.AddCellValue(lte.CellName);
                row.AddCellValue(lte.Type);
                row.AddCellValue(lte.TAC);
                row.AddCellValue(lte.CI);
                row.AddCellValue(lte.TAC16);
                row.AddCellValue(lte.CI16);
                row.AddCellValue(lteCell.SerialNumber);
                lteTables.Add(row);
            }

            List<NPOIRow> nrTables = new List<NPOIRow>();
            NPOIRow nrTitleRow = new NPOIRow();
            nrTitleRow.AddCellValue("省份");
            nrTitleRow.AddCellValue("省份ID");
            nrTitleRow.AddCellValue("地市");
            nrTitleRow.AddCellValue("地市ID");
            nrTitleRow.AddCellValue("区域");
            nrTitleRow.AddCellValue("网格");
            nrTitleRow.AddCellValue("基站号");
            nrTitleRow.AddCellValue("小区名");
            nrTitleRow.AddCellValue("覆盖类型");
            nrTitleRow.AddCellValue("TAC");
            nrTitleRow.AddCellValue("NCI");
            nrTitleRow.AddCellValue("TAC16");
            nrTitleRow.AddCellValue("NCI16");
            nrTitleRow.AddCellValue("编号");
            nrTables.Add(nrTitleRow);

            foreach (var nrCell in nrCellResults)
            {
                NPOIRow row = new NPOIRow();
                var nr = nrCell.CellInfo;
                row.AddCellValue(nr.Province);
                row.AddCellValue(nr.ProvinceID);
                row.AddCellValue(nr.City);
                row.AddCellValue(nr.CityID);
                row.AddCellValue(nr.Region);
                row.AddCellValue(nr.Grid);
                row.AddCellValue(nr.Enodebid);
                row.AddCellValue(nr.CellName);
                row.AddCellValue(nr.Type);
                row.AddCellValue(nr.TAC);
                row.AddCellValue(nr.CI);
                row.AddCellValue(nr.TAC16);
                row.AddCellValue(nr.CI16);
                row.AddCellValue(nrCell.SerialNumber);
                nrTables.Add(row);
            }

            List<List<NPOIRow>> npoiRowsList = new List<List<NPOIRow>>() { lteTables, nrTables };
            List<string> sheetNames = new List<string>() { "4G结果", "5G结果" };

            try
            {
                ExcelNPOIManager.ExportToExcel(npoiRowsList, sheetNames);
            }
            catch (Exception ex)
            {
                HighReverseFlowCoverageConfig.Instance.WriteLogWithMsgBox(ex);
            }
        }
        #endregion
    }
}
