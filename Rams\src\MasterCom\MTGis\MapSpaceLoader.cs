﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.Util;
using System.Xml;

namespace MasterCom.MTGis
{
    public class MapSpaceLoader
    {
        public List<VecLayerItem> VLayerItems { get; set; }
        public double curXMin { get; set; }
        public double curXMax { get; set; }
        public double curYMin { get; set; }
        public double curYMax { get; set; }
        public MapSpaceLoader()
        {
            VLayerItems = new List<VecLayerItem>();
        }
        public void Save(string filePath)
        {
            XmlConfigFile configFile = new XmlConfigFile();
            XmlElement configMain = configFile.AddConfig("MapSpace");
            configFile.AddItem(configMain, "DefautBounds", this, AddItem);
            configFile.Save(filePath);
        }

        public MapSpaceLoader(string filePath)
        {
            VLayerItems = new List<VecLayerItem>();
            Load(filePath);
        }

        public void Load(string filePath)
        {
            VLayerItems.Clear();
            XmlConfigFile configFile = new XmlConfigFile(filePath);
            configFile.GetItemValue("MapSpace", "DefautBounds", GetItemValue);
        }

        public object GetItemValue(XmlConfigFile configFile, XmlElement item, string typeName)
        {
            if (typeName.Equals("MapSpaceLoader"))
            {
                curXMin = double.Parse(configFile.GetItemValue(item, "xMin").ToString());
                curXMax = double.Parse(configFile.GetItemValue(item, "xMax").ToString());
                curYMin = double.Parse(configFile.GetItemValue(item, "yMin").ToString());
                curYMax = double.Parse(configFile.GetItemValue(item, "yMax").ToString());

                item = configFile.GetItem("MapSpace", "LayerInfo");
                List<object> list = configFile.GetItemValue(item, "VecLayerItems", GetItemValue) as List<object>;
                foreach (object value in list)
                {
                    if (value != null && value is VecLayerItem)
                    {
                        VLayerItems.Add((VecLayerItem)value);
                    }
                }
                return this;
            }
            else if (typeName.Equals("VecLayerItem"))
            {
                VecLayerItem layerItem = new VecLayerItem();
                layerItem.GetItemValue(configFile, item, typeName);
                return layerItem;
            }
            return null;
        }

        public XmlElement AddItem(XmlConfigFile configFile, XmlElement config, string name, object value)
        {
            if (value is MapSpaceLoader)
            {
                XmlElement item = configFile.AddItem(config, name, value.GetType());
                configFile.AddItem(item, "xMin", curXMin);
                configFile.AddItem(item, "xMax", curXMax);
                configFile.AddItem(item, "yMin", curYMin);
                configFile.AddItem(item, "yMax", curYMax);
                item = configFile.AddItem(config, "LayerInfo", value.GetType());
                configFile.AddItem(item, "VecLayerItems", VLayerItems, AddItem);
                return item;
            }
            else if (value is VecLayerItem)
            {
                return (value as VecLayerItem).AddItem(configFile, config, null, value);
            }
            return null;
        }
    }
}
