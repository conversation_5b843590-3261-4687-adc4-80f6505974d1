﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class LtePlanningRedirectSettingForm : BaseDialog
    {
        public LtePlanningRedirectSettingForm()
        {
            InitializeComponent();
            btnCancel.Click += BtnCancel_Click;
            btnOK.Click += BtnOK_Click;
            btnExcel.Click += BtnExcel_Click;
        }

        public string GetXlsFileName()
        {
            return txtExcel.Text;
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
        }

        private void BtnOK_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(txtExcel.Text))
            {
                MessageBox.Show("请选择Excel文件", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                DialogResult = DialogResult.None;
                return;
            }

            DialogResult = DialogResult.OK;
        }

        private void BtnExcel_Click(object sender, EventArgs e)
        {
            OpenFileDialog dlg = new OpenFileDialog();
            dlg.Filter = FilterHelper.Excel;
            dlg.FilterIndex = 2;
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return;
            }
            txtExcel.Text = dlg.FileName;
        }
    }
}
