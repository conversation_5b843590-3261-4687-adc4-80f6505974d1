<?xml version="1.0"?>
<Configs>
  <Config name="AtuLogGroupCfg">
    <Item name="Templates" typeName="IList">
      <Item typeName="IDictionary">
        <Item typeName="String" key="Name">设备状态查询</Item>
        <Item typeName="String" key="LogicType">与</Item>
        <Item typeName="IList" key="IndicatorOptions">
          <Item typeName="IDictionary">
            <Item typeName="String" key="KPIName">测试里程（公里）</Item>
            <Item typeName="String" key="KPIFormula">{(Mx_0806+Tx_0806+Lte_0806) /1000}</Item>
            <Item typeName="String" key="Logic">≥</Item>
            <Item typeName="Double" key="ReferentValue">0</Item>
            <Item typeName="Boolean" key="AsSatisfiedWhenNoValue">False</Item>
            <Item typeName="Boolean" key="IsMultiFormula">False</Item>
            <Item key="Formula1" />
            <Item key="Formula2" />
            <Item typeName="Int32" key="MoMt1">0</Item>
            <Item typeName="Int32" key="MoMt2">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="KPIName">测试时长（小时）</Item>
            <Item typeName="String" key="KPIFormula">{(Mx_0805+Tx_0805+Lte_0805)/(60*60*1000)}</Item>
            <Item typeName="String" key="Logic">≥</Item>
            <Item typeName="Double" key="ReferentValue">0</Item>
            <Item typeName="Boolean" key="AsSatisfiedWhenNoValue">False</Item>
            <Item typeName="Boolean" key="IsMultiFormula">False</Item>
            <Item key="Formula1" />
            <Item key="Formula2" />
            <Item typeName="Int32" key="MoMt1">0</Item>
            <Item typeName="Int32" key="MoMt2">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="KPIName">Rxlev90覆盖率（%）</Item>
            <Item typeName="String" key="KPIFormula">{100*(Mx_640117+Mx_64010A+Mx_640109+Mx_640108)/Mx_640101}</Item>
            <Item typeName="String" key="Logic">≥</Item>
            <Item typeName="Double" key="ReferentValue">99.8</Item>
            <Item typeName="Boolean" key="AsSatisfiedWhenNoValue">True</Item>
            <Item typeName="Boolean" key="IsMultiFormula">False</Item>
            <Item key="Formula1" />
            <Item key="Formula2" />
            <Item typeName="Int32" key="MoMt1">0</Item>
            <Item typeName="Int32" key="MoMt2">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="KPIName">接通率_主叫接通率（%）</Item>
            <Item typeName="String" key="KPIFormula">{100.0*((evtIdCount[0]+value9[0]) - (evtIdCount[7]+evtIdCount[9]+evtIdCount[81]+evtIdCount[87]+value9[7]+value9[9]+value9[81]))/(evtIdCount[0]+value9[0])}</Item>
            <Item typeName="String" key="Logic">≥</Item>
            <Item typeName="Double" key="ReferentValue">98</Item>
            <Item typeName="Boolean" key="AsSatisfiedWhenNoValue">True</Item>
            <Item typeName="Boolean" key="IsMultiFormula">False</Item>
            <Item key="Formula1" />
            <Item key="Formula2" />
            <Item typeName="Int32" key="MoMt1">0</Item>
            <Item typeName="Int32" key="MoMt2">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="KPIName">掉话率（%）</Item>
            <Item typeName="String" key="KPIFormula">{100*(evtIdCount[5]+value9[5]+evtIdCount[6]+value9[6]+evtIdCount[906]+value9[906]+evtIdCount[907]+value9[907])/((evtIdCount[0]+evtIdCount[1]+value9[0]+value9[1])-(evtIdCount[7]+evtIdCount[9]+evtIdCount[81]+evtIdCount[87]+evtIdCount[8]+evtIdCount[10]+evtIdCount[82]+value9[7]+value9[9]+value9[81]+value9[8]+value9[10]+value9[82]))}</Item>
            <Item typeName="String" key="Logic">≥</Item>
            <Item typeName="Double" key="ReferentValue">0</Item>
            <Item typeName="Boolean" key="AsSatisfiedWhenNoValue">True</Item>
            <Item typeName="Boolean" key="IsMultiFormula">False</Item>
            <Item key="Formula1" />
            <Item key="Formula2" />
            <Item typeName="Int32" key="MoMt1">0</Item>
            <Item typeName="Int32" key="MoMt2">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="KPIName">FTP下载速率_不含掉线（Kbps）</Item>
            <Item typeName="String" key="KPIFormula">{(value1[57])*(1000*8)/((value4[57])*1024) }</Item>
            <Item typeName="String" key="Logic">≥</Item>
            <Item typeName="Double" key="ReferentValue">140</Item>
            <Item typeName="Boolean" key="AsSatisfiedWhenNoValue">True</Item>
            <Item typeName="Boolean" key="IsMultiFormula">False</Item>
            <Item key="Formula1" />
            <Item key="Formula2" />
            <Item typeName="Int32" key="MoMt1">0</Item>
            <Item typeName="Int32" key="MoMt2">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="KPIName">主叫试呼次数</Item>
            <Item typeName="String" key="KPIFormula">{evtIdCount[0]+value9[0]}</Item>
            <Item typeName="String" key="Logic">=</Item>
            <Item typeName="Double" key="ReferentValue">0</Item>
            <Item typeName="Boolean" key="AsSatisfiedWhenNoValue">True</Item>
            <Item typeName="Boolean" key="IsMultiFormula">False</Item>
            <Item key="Formula1" />
            <Item key="Formula2" />
            <Item typeName="Int32" key="MoMt1">0</Item>
            <Item typeName="Int32" key="MoMt2">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="KPIName">主被叫电平差异</Item>
            <Item typeName="String" key="KPIFormula">{Mx_5A010202} - {Mx_5A010202}</Item>
            <Item typeName="String" key="Logic">=</Item>
            <Item typeName="Double" key="ReferentValue">0</Item>
            <Item typeName="Boolean" key="AsSatisfiedWhenNoValue">False</Item>
            <Item typeName="Boolean" key="IsMultiFormula">True</Item>
            <Item typeName="String" key="Formula1">{Mx_5A010202}</Item>
            <Item typeName="String" key="Formula2">{Mx_5A010202}</Item>
            <Item typeName="Int32" key="MoMt1">1</Item>
            <Item typeName="Int32" key="MoMt2">2</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="KPIName">电平平均值</Item>
            <Item typeName="String" key="KPIFormula">{Mx_5A010202}</Item>
            <Item typeName="String" key="Logic">=</Item>
            <Item typeName="Double" key="ReferentValue">0</Item>
            <Item typeName="Boolean" key="AsSatisfiedWhenNoValue">False</Item>
            <Item typeName="Boolean" key="IsMultiFormula">False</Item>
            <Item key="Formula1" />
            <Item key="Formula2" />
            <Item typeName="Int32" key="MoMt1">0</Item>
            <Item typeName="Int32" key="MoMt2">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
        </Item>
        <Item typeName="IList" key="RangeColorSet">
          <Item typeName="IDictionary">
            <Item typeName="Single" key="Min">0</Item>
            <Item typeName="Single" key="Max">60</Item>
            <Item typeName="Boolean" key="MinIncluded">True</Item>
            <Item typeName="Boolean" key="MaxIncluded">False</Item>
            <Item key="DescInfo" />
            <Item typeName="Int32" key="Value">-65536</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="Min">60</Item>
            <Item typeName="Single" key="Max">80</Item>
            <Item typeName="Boolean" key="MinIncluded">True</Item>
            <Item typeName="Boolean" key="MaxIncluded">False</Item>
            <Item key="DescInfo" />
            <Item typeName="Int32" key="Value">-128</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="Min">80</Item>
            <Item typeName="Single" key="Max">100</Item>
            <Item typeName="Boolean" key="MinIncluded">True</Item>
            <Item typeName="Boolean" key="MaxIncluded">True</Item>
            <Item key="DescInfo" />
            <Item typeName="Int32" key="Value">-16744448</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Name">LTE数据业务设备异常核查</Item>
        <Item typeName="String" key="LogicType">与</Item>
        <Item typeName="IList" key="IndicatorOptions">
          <Item typeName="IDictionary">
            <Item typeName="String" key="KPIName">LTE覆盖率3（RSRP&gt;-103且SINR&gt;=-3）</Item>
            <Item typeName="String" key="KPIFormula">{(Lte_6121010D/Lte_61210101) * 100}</Item>
            <Item typeName="String" key="Logic">≥</Item>
            <Item typeName="Double" key="ReferentValue">90</Item>
            <Item typeName="Boolean" key="AsSatisfiedWhenNoValue">True</Item>
            <Item typeName="Boolean" key="IsMultiFormula">False</Item>
            <Item key="Formula1" />
            <Item key="Formula2" />
            <Item typeName="Int32" key="MoMt1">0</Item>
            <Item typeName="Int32" key="MoMt2">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="KPIName">LTE网络下载速率</Item>
            <Item typeName="String" key="KPIFormula">{(value10[57])*(1000*8)/((value4[57])*(1024*1024))}</Item>
            <Item typeName="String" key="Logic">≥</Item>
            <Item typeName="Double" key="ReferentValue">15</Item>
            <Item typeName="Boolean" key="AsSatisfiedWhenNoValue">True</Item>
            <Item typeName="Boolean" key="IsMultiFormula">False</Item>
            <Item key="Formula1" />
            <Item key="Formula2" />
            <Item typeName="Int32" key="MoMt1">0</Item>
            <Item typeName="Int32" key="MoMt2">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="KPIName">业务类_FTP下载成功次数</Item>
            <Item typeName="String" key="KPIFormula">{evtIdCount[57]+value9[57]}</Item>
            <Item typeName="String" key="Logic">≥</Item>
            <Item typeName="Double" key="ReferentValue">5</Item>
            <Item typeName="Boolean" key="AsSatisfiedWhenNoValue">True</Item>
            <Item typeName="Boolean" key="IsMultiFormula">False</Item>
            <Item key="Formula1" />
            <Item key="Formula2" />
            <Item typeName="Int32" key="MoMt1">0</Item>
            <Item typeName="Int32" key="MoMt2">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
        </Item>
        <Item typeName="IList" key="RangeColorSet">
          <Item typeName="IDictionary">
            <Item typeName="Single" key="Min">0</Item>
            <Item typeName="Single" key="Max">50</Item>
            <Item typeName="Boolean" key="MinIncluded">True</Item>
            <Item typeName="Boolean" key="MaxIncluded">False</Item>
            <Item typeName="String" key="DescInfo">正常比例不到一半</Item>
            <Item typeName="Int32" key="Value">-65536</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="Min">50</Item>
            <Item typeName="Single" key="Max">80</Item>
            <Item typeName="Boolean" key="MinIncluded">True</Item>
            <Item typeName="Boolean" key="MaxIncluded">False</Item>
            <Item typeName="String" key="DescInfo">50-80正常</Item>
            <Item typeName="Int32" key="Value">-256</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="Min">80</Item>
            <Item typeName="Single" key="Max">100</Item>
            <Item typeName="Boolean" key="MinIncluded">True</Item>
            <Item typeName="Boolean" key="MaxIncluded">True</Item>
            <Item typeName="String" key="DescInfo">大部分正常</Item>
            <Item typeName="Int32" key="Value">-16744448</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Name">LTE语音业务设备异常核查</Item>
        <Item typeName="String" key="LogicType">与</Item>
        <Item typeName="IList" key="IndicatorOptions">
          <Item typeName="IDictionary">
            <Item typeName="String" key="KPIName">LTE覆盖率3（RSRP&gt;-103且SINR&gt;=-3）</Item>
            <Item typeName="String" key="KPIFormula">{(Lte_6121010D/Lte_61210101) * 100}</Item>
            <Item typeName="String" key="Logic">≥</Item>
            <Item typeName="Double" key="ReferentValue">90</Item>
            <Item typeName="Boolean" key="AsSatisfiedWhenNoValue">True</Item>
            <Item typeName="Boolean" key="IsMultiFormula">False</Item>
            <Item key="Formula1" />
            <Item key="Formula2" />
            <Item typeName="Int32" key="MoMt1">0</Item>
            <Item typeName="Int32" key="MoMt2">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="KPIName">试呼次数</Item>
            <Item typeName="String" key="KPIFormula">{evtIdCount[1000]+evtIdCount[1001]}</Item>
            <Item typeName="String" key="Logic">&gt;</Item>
            <Item typeName="Double" key="ReferentValue">5</Item>
            <Item typeName="Boolean" key="AsSatisfiedWhenNoValue">True</Item>
            <Item typeName="Boolean" key="IsMultiFormula">False</Item>
            <Item key="Formula1" />
            <Item key="Formula2" />
            <Item typeName="Int32" key="MoMt1">0</Item>
            <Item typeName="Int32" key="MoMt2">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="KPIName">测试总时长（分钟）</Item>
            <Item typeName="String" key="KPIFormula">{Lte_0805/(60*1000)}</Item>
            <Item typeName="String" key="Logic">≥</Item>
            <Item typeName="Double" key="ReferentValue">10</Item>
            <Item typeName="Boolean" key="AsSatisfiedWhenNoValue">True</Item>
            <Item typeName="Boolean" key="IsMultiFormula">False</Item>
            <Item key="Formula1" />
            <Item key="Formula2" />
            <Item typeName="Int32" key="MoMt1">0</Item>
            <Item typeName="Int32" key="MoMt2">0</Item>
            <Item typeName="Int32" key="CarrierID">1</Item>
          </Item>
        </Item>
        <Item typeName="IList" key="RangeColorSet">
          <Item typeName="IDictionary">
            <Item typeName="Single" key="Min">0</Item>
            <Item typeName="Single" key="Max">50</Item>
            <Item typeName="Boolean" key="MinIncluded">True</Item>
            <Item typeName="Boolean" key="MaxIncluded">False</Item>
            <Item key="DescInfo" />
            <Item typeName="Int32" key="Value">-65536</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="Min">50</Item>
            <Item typeName="Single" key="Max">80</Item>
            <Item typeName="Boolean" key="MinIncluded">True</Item>
            <Item typeName="Boolean" key="MaxIncluded">False</Item>
            <Item key="DescInfo" />
            <Item typeName="Int32" key="Value">-256</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="Min">80</Item>
            <Item typeName="Single" key="Max">100</Item>
            <Item typeName="Boolean" key="MinIncluded">True</Item>
            <Item typeName="Boolean" key="MaxIncluded">True</Item>
            <Item key="DescInfo" />
            <Item typeName="Int32" key="Value">-16711936</Item>
          </Item>
        </Item>
      </Item>
    </Item>
  </Config>
</Configs>