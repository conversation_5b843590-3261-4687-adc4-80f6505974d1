﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Stat;
using MasterCom.RAMS.Model.Interface;
using System.Windows.Forms;
using MasterCom.RAMS.Func.SystemSetting;
using MasterCom.RAMS.BackgroundFunc;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.ZTFunc
{
    public class OverCoverAnaByRegionFile_LTE : DIYAnalyseByCellBackgroundBaseByFile
    {//逐个文件分析，与ZTCellCoverLapByRegion_LTE相比较慢（网络体检用）

        protected Dictionary<string, CellCoverLap> cellLapRetDic = new Dictionary<string, CellCoverLap>();
        OverCoverLapSetForm fDlg = null;
        OverCoverCondition cond = new OverCoverCondition();

        protected static readonly object lockObj = new object();
        private static OverCoverAnaByRegionFile_LTE instance = null;
        public static OverCoverAnaByRegionFile_LTE GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new OverCoverAnaByRegionFile_LTE();
                    }
                }
            }
            return instance;
        }

        protected OverCoverAnaByRegionFile_LTE()
            : base(MainModel.GetInstance())
        {
            FilterSampleByRegion = true;
            IncludeEvent = false;
            ServiceTypes.Clear();
            ServiceTypes.AddRange(ServiceTypeManager.GetLteAllServiceTypes());
            Columns = new List<string>();
            Columns.Add("lte_TAC");
            Columns.Add("lte_ECI");
            Columns.Add("lte_EARFCN");
            Columns.Add("lte_PCI");
            Columns.Add("lte_RSRP");
            Columns.Add("lte_RSRQ");
        }

        public override string Name
        {
            get { return "过覆盖分析_LTE"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22010, this.Name);
        }
        protected override void getReadyBeforeQuery()
        {
            cellLapRetDic.Clear();
        }
        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }
            return showFuncCondSetDlg();
        }
        protected bool showFuncCondSetDlg()
        {
            if (fDlg == null)
            {
                fDlg = new OverCoverLapSetForm();
            }
            fDlg.SetCondition(cond);
            if (fDlg.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            cond = fDlg.GetCondition();
            return true;
        }
        protected override void doStatWithQuery()
        {
            try
            {
                foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
                {
                    if (MainModel.IsBackground)
                    {
                        FileInfo fileInfo = file.GetFileInfo();
                        curAnaFileInfo = fileInfo == null ? curAnaFileInfo : fileInfo;
                    }
                    foreach (TestPoint tp in file.TestPoints)
                    {
                        doWithDTData(tp);
                    }
                }
            }
            catch (Exception ee)
            {
                System.Windows.Forms.MessageBox.Show(ee.Message + Environment.NewLine + ee.Source + Environment.NewLine + ee.StackTrace);
            }
        }
        protected void doWithDTData(TestPoint tp)
        {
            if (!isValidBand(tp))
            {
                return;
            }
            float? rsrp = getRsrp(tp);
            if (rsrp == null || rsrp < cond.RxlevFilter)
            {
                return;
            }

            LTECell cell = tp.GetMainCell_LTE();
            if (cell != null && cell.Type != LTEBTSType.Indoor)
            {
                CellCoverLap_LTE covLap = null;
                CellCoverLap clTmp = null;
                if (!cellLapRetDic.TryGetValue(cell.Name, out clTmp))
                {
                    double radiusOfCell = MasterCom.ES.Data.CfgDataProvider.CalculateRadius(cell, cond.NearestCellCount);
                    covLap = new CellCoverLap_LTE(cell, radiusOfCell);
                    covLap.rationalDistance = radiusOfCell * cond.DisFactor;
                    covLap.nearestBTSs = MasterCom.ES.Data.CfgDataProvider.GetNearestBTSs(cell, cond.NearestCellCount);
                    covLap.mnger = new DTDataManager(MainModel.GetInstance());
                    cellLapRetDic[cell.Name] = covLap;
                }
                else
                {
                    covLap = (CellCoverLap_LTE)clTmp;
                }

                if (!covLap.strFileID.Contains(tp.FileName))
                {
                    covLap.strFileID += tp.FileName + ",";
                }

                double distanceToCell = MathFuncs.GetDistance(tp.Longitude, tp.Latitude, cell.Longitude, cell.Latitude);
                bool isBadCheck = distanceToCell > covLap.rationalDistance;
                if (isBadCheck)
                {
                    covLap.AddBadSample(tp, distanceToCell, (float)rsrp, (int?)(float?)tp["lte_RSRQ"]);
                }
                else
                {
                    covLap.goodSampleCount++;
                }
            }
        }

        protected bool isValidBand(TestPoint tp)
        {
            if (cond.IsChkBand)
            {
                int? earfcn = (int?)tp["lte_EARFCN"];
                if (earfcn == null)
                {
                    return false;
                }
                if (!LTECell.GetBandTypeByEarfcn((int)earfcn).ToString().Equals(cond.BandType))
                {
                    return false;
                }
            }
            return true;
        }

        protected virtual float? getRsrp(TestPoint tp)
        {
            return (float?)tp["lte_RSRP"];
        }

        protected override void getResultsAfterQuery()
        {
            filterCellCoverLap();
            fireDTDatas();
        }
        protected void filterCellCoverLap()
        {
            List<string> filterCells = new List<string>();
            foreach (KeyValuePair<string, CellCoverLap> keyValue in cellLapRetDic)
            {
                CellCoverLap cellLap = keyValue.Value;
                cellLap.GetResult();

                if (!(cellLap.badSampleCount > 0 && cellLap._CellCovRadius > 50
                    && (!cond.IsChkSampleCount || cellLap.TotalSampleCount >= cond.SampleMinCount)
                    && (!cond.IsChkOverPercent || cellLap.BadSamplePercent >= cond.BadPercent)
                    && (!cond.IsChkDistance || (cellLap.MeanBadDistance >= cond.MinDistance 
                    && cellLap.MeanBadDistance <= cond.MaxDistance))))
                {
                    filterCells.Add(keyValue.Key);
                }
            }
            foreach (string key in filterCells)
            {
                cellLapRetDic.Remove(key);
            }
        }
        protected void fireDTDatas()
        {
            MainModel.DTDataManager.Clear();
            if (!MainModel.IsBackground)
            {
                foreach (CellCoverLap ccl in cellLapRetDic.Values)
                {
                    if (ccl.mnger != null)
                    {
                        foreach (DTFileDataManager fmnger in ccl.mnger.FileDataManagers)
                        {
                            foreach (TestPoint tp in fmnger.TestPoints)
                            {
                                MainModel.DTDataManager.Add(tp);
                            }
                        }
                    }
                }
                MainModel.FireDTDataChanged(this);
            }
        }

        protected override void fireShowForm()
        {
            if (cellLapRetDic.Count == 0)
            {
                DevExpress.XtraEditors.XtraMessageBox.Show(MainModel.MainForm, "没有符合条件的数据。");
                return;
            }
            ZTCellCoverLapListForm cellCoverLapListForm = MainModel.GetInstance().GetObjectFromBlackboard(typeof(ZTCellCoverLapListForm)) as ZTCellCoverLapListForm;
            if (cellCoverLapListForm == null || cellCoverLapListForm.IsDisposed)
            {
                cellCoverLapListForm = new ZTCellCoverLapListForm(MainModel);
            }
            cellCoverLapListForm.showCellSet(cellLapRetDic, cond.SampleMinCount, cond.BadPercent, cond.MinDistance, cond.MaxDistance);
            cellCoverLapListForm.Owner = MainModel.MainForm;
            cellCoverLapListForm.Visible = true;
            cellCoverLapListForm.BringToFront();
            mainModel.FireSetDefaultMapSerialTheme("LTE_TDD:RSRP");
        }

        #region Background
        public override BackgroundFuncType FuncType
        {
            get { return BackgroundFuncType.LTE业务专题; }
        }
        public override BackgroundSubFuncType SubFuncType
        {
            get { return BackgroundSubFuncType.覆盖; }
        }
        public override Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["BackgroundStat"] = BackgroundStat;
                param["FilterRxlev"] = cond.RxlevFilter;
                param["NearestCellCount"] = cond.NearestCellCount;
                param["DisFactor"] = cond.DisFactor;

                param["IsChkSampleCount"] = cond.IsChkSampleCount;
                param["MinSampleCount"] = cond.SampleMinCount;
                param["IsChkOverPercent"] = cond.IsChkOverPercent;
                param["MinPercent"] = cond.BadPercent;
                param["IsChkDistance"] = cond.IsChkDistance;
                param["MinDistance"] = cond.MinDistance;
                param["MaxDistance"] = cond.MaxDistance;
                param["IsChkBand"] = cond.IsChkBand;
                param["BandType"] = cond.BandType;
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                if (param.ContainsKey("BackgroundStat"))
                {
                    BackgroundStat = (bool)param["BackgroundStat"];
                }
                if (param.ContainsKey("FilterRxlev"))
                {
                    cond.RxlevFilter = int.Parse(param["FilterRxlev"].ToString());
                }
                if (param.ContainsKey("NearestCellCount"))
                {
                    cond.NearestCellCount = int.Parse(param["NearestCellCount"].ToString());
                }
                if (param.ContainsKey("DisFactor"))
                {
                    cond.DisFactor = float.Parse(param["DisFactor"].ToString());
                }

                if (param.ContainsKey("IsChkSampleCount"))
                {
                    cond.IsChkSampleCount = (bool)param["IsChkSampleCount"];
                }
                if (param.ContainsKey("MinSampleCount"))
                {
                    cond.SampleMinCount = int.Parse(param["MinSampleCount"].ToString());
                }
                if (param.ContainsKey("IsChkOverPercent"))
                {
                    cond.IsChkOverPercent = (bool)param["IsChkOverPercent"];
                }
                if (param.ContainsKey("MinPercent"))
                {
                    cond.BadPercent = float.Parse(param["MinPercent"].ToString());
                }
                if (param.ContainsKey("IsChkDistance"))
                {
                    cond.IsChkDistance = (bool)param["IsChkDistance"];
                }
                if (param.ContainsKey("MinDistance"))
                {
                    cond.MinDistance = int.Parse(param["MinDistance"].ToString());
                }
                if (param.ContainsKey("MaxDistance"))
                {
                    cond.MaxDistance = int.Parse(param["MaxDistance"].ToString());
                }
                if (param.ContainsKey("IsChkBand"))
                {
                    cond.IsChkBand = (bool)param["IsChkBand"];
                }
                if (param.ContainsKey("BandType"))
                {
                    cond.BandType = param["BandType"].ToString();
                }
            }
        }
        public override PropertiesControl Properties
        {
            get
            {
                return new CommonSimpleProperties(this, showFuncCondSetDlg);
            }
        }

        protected override void saveBackgroundData()
        {
            List<BackgroundResult> bgResultList = new List<BackgroundResult>();
            foreach (CellCoverLap ccl in cellLapRetDic.Values)
            {
                ccl.GetResult();
                BackgroundResult result = ccl.ConvertToBackgroundResult();
                result.FileID = curAnaFileInfo.ID;
                result.FileName = curAnaFileInfo.Name;
                result.SubFuncID = GetSubFuncID();
                bgResultList.Add(result);
            }
            BackgroundFuncQueryManager.GetInstance().SaveResult_Cell(GetSubFuncID(), curAnaFileInfo, bgResultList);
            cellLapRetDic.Clear();
        }

        protected override void initBackgroundImageDesc()
        {
            foreach (BackgroundResult bgResult in BackgroundResultList)
            {
                int rxQualTestPointCount = bgResult.GetImageValueInt();
                int badSampleCount = bgResult.GetImageValueInt();
                int goodSampleCount = bgResult.GetImageValueInt();
                float MeanBadDistance = bgResult.GetImageValueFloat();
                float maxBadDistance = bgResult.GetImageValueFloat();
                float minBadDistance = bgResult.GetImageValueFloat();

                DateTime beginTime = MasterCom.Util.JavaDate.GetDateTimeFromMilliseconds(bgResult.ISTime * 1000L);
                LTECell cell = CellManager.GetInstance().GetNearestLTECell(beginTime, bgResult.LAC, bgResult.CI
                    , bgResult.BCCH, bgResult.BSIC, bgResult.LongitudeMid, bgResult.LatitudeMid);

                if (cell != null && cell.Type != LTEBTSType.Indoor)
                {
                    CellCoverLap_LTE covLap;
                    CellCoverLap clTmp;
                    if (!cellLapRetDic.TryGetValue(cell.Name, out clTmp))
                    {
                        double radiusOfCell = MasterCom.ES.Data.CfgDataProvider.CalculateRadius(cell, cond.NearestCellCount);
                        covLap = new CellCoverLap_LTE(cell, radiusOfCell);
                        covLap.rationalDistance = radiusOfCell * cond.DisFactor;
                        covLap.nearestBTSs = MasterCom.ES.Data.CfgDataProvider.GetNearestBTSs(cell, cond.NearestCellCount);
                        covLap.mnger = new DTDataManager(MainModel.GetInstance());

                        cellLapRetDic[cell.Name] = covLap;
                    }
                    else
                    {
                        covLap = (CellCoverLap_LTE)clTmp;
                    }

                    covLap.MeanBadDistance = ((covLap.MeanBadDistance * covLap.badSampleCount)
                        + (MeanBadDistance * badSampleCount)) / (covLap.badSampleCount + badSampleCount);
                    covLap.maxBadDistance = Math.Max(covLap.maxBadDistance, maxBadDistance);
                    covLap.minBadDistance = Math.Min(covLap.minBadDistance, minBadDistance);

                    covLap.badSampleCount += badSampleCount;
                    covLap.goodSampleCount += goodSampleCount;
                    covLap.TestPointCount += bgResult.SampleCount;
                    covLap.SumRxLev += bgResult.SampleCount * bgResult.RxLevMean;
                    covLap.MaxRxLev = Math.Max(covLap.MaxRxLev, bgResult.RxLevMax);
                    covLap.MinRxLev = Math.Min(covLap.MinRxLev, bgResult.RxLevMin);
                    covLap.RxQualTestPointCount += rxQualTestPointCount;
                    covLap.SumRxQual += rxQualTestPointCount * bgResult.RxQualMean;
                    covLap.MaxRxQual = Math.Max(covLap.MaxRxQual, bgResult.RxQualMax);
                    covLap.MinRxQual = Math.Min(covLap.MinRxQual, bgResult.RxQualMin);
                    covLap.istime = Math.Min(covLap.istime, bgResult.ISTime);
                    covLap.ietime = Math.Min(covLap.ietime, bgResult.IETime);
                    covLap.roadDesc = getMergeDes(covLap.roadDesc, bgResult.RoadDesc, ';');
                    covLap.areaName = getMergeDes(covLap.areaName, bgResult.AreaDesc, ',');
                    covLap.gridName = getMergeDes(covLap.gridName, bgResult.GridDesc, ',');
                    covLap.areaAgentName = getMergeDes(covLap.areaAgentName, bgResult.AreaAgentDesc, ',');

                    if (!covLap.strFileID.Contains(bgResult.FileName))
                    {
                        StringBuilder sb = new StringBuilder(covLap.strFileID);
                        sb.Append(bgResult.FileName + ",");
                        covLap.strFileID = sb.ToString();
                    }
                }
            }
            filterCellCoverLap();
            this.BackgroundNPOIRowResultDic.Clear();
            this.BackgroundNPOIRowResultDic[""] = getNPOIRowResults(cellLapRetDic);
        }
        protected static List<NPOIRow> getNPOIRowResults(Dictionary<string, CellCoverLap> resultDic)
        {
            List<NPOIRow> rows = new List<NPOIRow>();
            NPOIRow row = new NPOIRow();
            rows.Add(row);
            row.cellValues.Add("序号");
            row.cellValues.Add("文件名");
            row.cellValues.Add("小区名称");
            row.cellValues.Add("LAC");
            row.cellValues.Add("CI");
            row.cellValues.Add("最近3个基站名称");
            row.cellValues.Add("过覆盖采样点数");
            row.cellValues.Add("采样点总数");
            row.cellValues.Add("过覆盖占比(%)");
            row.cellValues.Add("过覆盖平均距离(m)");
            row.cellValues.Add("过覆盖最近距离(m)");
            row.cellValues.Add("过覆盖最远距离(m)");
            row.cellValues.Add("合理覆盖距离(m)");
            row.cellValues.Add("最大电平");
            row.cellValues.Add("最小电平");
            row.cellValues.Add("平均电平");
            row.cellValues.Add("最大质量");
            row.cellValues.Add("最小质量");
            row.cellValues.Add("平均质量");
            row.cellValues.Add("位置信息");
            row.cellValues.Add("小区属性");
            row.cellValues.Add("片区");
            row.cellValues.Add("网格");
            row.cellValues.Add("代维区");
            row.cellValues.Add("下倾角");
            row.cellValues.Add("建议下倾角");

            int i = 0;
            foreach (CellCoverLap lap in resultDic.Values)
            {
                CellCoverLap_LTE covLap = (CellCoverLap_LTE)lap;
                if (covLap == null)
                {
                    continue;
                }
                i++;
                row = new NPOIRow();
                rows.Add(row);

                row.cellValues.Add(i);
                row.cellValues.Add(covLap.strFileID);
                row.cellValues.Add(covLap.lteCell.Name);
                row.cellValues.Add(covLap.lteCell.TAC);
                row.cellValues.Add(covLap.lteCell.ECI);
                row.cellValues.Add(covLap.nearestBTSs);
                row.cellValues.Add(covLap.badSampleCount);
                row.cellValues.Add(covLap.TotalSampleCount);
                row.cellValues.Add(string.Format("{0:F2}", covLap.BadSamplePercent * 100f));
                row.cellValues.Add(covLap.MeanBadDistance);
                row.cellValues.Add(covLap.minBadDistance);
                row.cellValues.Add(covLap.maxBadDistance);
                row.cellValues.Add(covLap.rationalDistance);
                row.cellValues.Add(covLap.MaxRxLev);
                row.cellValues.Add(covLap.MinRxLev);
                row.cellValues.Add(covLap.AvgRxLev);
                row.cellValues.Add(covLap.MaxRxQualString);
                row.cellValues.Add(covLap.MinRxQualString);
                row.cellValues.Add(covLap.AvgRxQual);
                row.cellValues.Add(covLap.roadDesc);
                row.cellValues.Add(covLap.lteCell.Type == LTEBTSType.Outdoor ? "室外" : "室内");
                row.cellValues.Add(covLap.areaName);
                row.cellValues.Add(covLap.gridName);
                row.cellValues.Add(covLap.areaAgentName);
                row.cellValues.Add(covLap.CellDownDir);
                row.cellValues.Add(covLap.SuggustDownDir);
            }
            return rows;
        }

        protected static string getMergeDes(string strDes1, string strDes2, char splitChar)
        {
            if (string.IsNullOrEmpty(strDes1))
            {
                return strDes2;
            }
            if (string.IsNullOrEmpty(strDes2))
            {
                return strDes1;
            }

            List<string> strList = new List<string>();
            strList.AddRange(strDes1.Split(splitChar));
            strList.AddRange(strDes2.Split(splitChar));

            StringBuilder strbMerge = new StringBuilder();
            List<string> existStrList = new List<string>();
            foreach (string str in strList)
            {
                if (!existStrList.Contains(str))
                {
                    if (strbMerge.Length > 0)
                    {
                        strbMerge.Append(splitChar + str);
                    }
                    else
                    {
                        strbMerge.Append(str);
                    }
                    existStrList.Add(str);
                }
            }
            return strbMerge.ToString();
        }
        #endregion
    }
}
