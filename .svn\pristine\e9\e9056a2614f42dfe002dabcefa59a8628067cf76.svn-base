using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Chris.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class WCellMosSetForm : Form
    {
        public WCellMosSetForm()
        {
            InitializeComponent();
            rangeSettingMOS.NumericUpDownMin.Increment = 0.1M;
            rangeSettingMOS.NumericUpDownMin.DecimalPlaces = 1;
            rangeSettingMOS.NumericUpDownMax.Increment = 0.1M;
            rangeSettingMOS.NumericUpDownMax.DecimalPlaces = 1;

            rangeSettingMOS.RangeAll = new Range(0, true, 5, true);
            rangeSettingRscp.RangeAll = new Range(-115, true, -25, true);
            rangeSettingEcIo.RangeAll = new Range(-31.5, true, 0, true);

        }
        public Range MOSRange
        {
            get { return rangeSettingMOS.Range; }
        }
        public Range EcIoRange
        {
            get { return rangeSettingEcIo.Range; }
        }
        public Range RscpRange
        {
            get { return rangeSettingRscp.Range; }
        }

        private void buttonOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void buttonCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        private void WCellMosSetForm_Load(object sender, EventArgs e)
        {
            //
        }
    }
}