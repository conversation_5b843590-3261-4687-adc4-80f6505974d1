﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.Net
{
    public class ZTDIYQueryTAUHandoverTooMuchByFile : ZTDIYQueryTAUHandoverTooMuchLTE
    {
        public ZTDIYQueryTAUHandoverTooMuchByFile()
            : base()
        {
        }
        private static ZTDIYQueryTAUHandoverTooMuchByFile intance = null;
        public static new ZTDIYQueryTAUHandoverTooMuchByFile GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new ZTDIYQueryTAUHandoverTooMuchByFile();
                    }
                }
            }
            return intance;
        }
        public override string Name
        {
            get { return "跟踪区频繁更新-按文件(LTE)"; }
        }
        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.File;
        }
        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }
        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }
    }

    public class ZTDIYQueryTAUHandoverTooMuchByFile_FDD : ZTDIYQueryTAUHandoverTooMuchLTE_FDD
    {
        private static ZTDIYQueryTAUHandoverTooMuchByFile_FDD instance = null;
        public static new ZTDIYQueryTAUHandoverTooMuchByFile_FDD GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new ZTDIYQueryTAUHandoverTooMuchByFile_FDD();
                    }
                }
            }
            return instance;
        }

        public ZTDIYQueryTAUHandoverTooMuchByFile_FDD()
            : base()
        {

        }
        public override string Name
        {
            get { return "跟踪区频繁更新-按文件(LTE_FDD)"; }
        }
        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.File;
        }
        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }
        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }
    }
}
