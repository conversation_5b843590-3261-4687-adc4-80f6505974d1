﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Stat;
using MasterCom.Util;
using MasterCom.Util.UiEx;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class LteMultiCompareAnaByRegion : DIYAnalyseByFileBackgroundBase
    {
        LteMultiCompareAutoSet curSet = null;
        List<MultiCompareRoadInfo> roadInfoList = null;
        List<ProblemDetailInfo> problemDetailList = null;
        MultiCompareRoadInfo curRoadInfo = null;
        ReporterTemplate template = null;

        string strTestDetailPath = "";

        public LteMultiCompareAnaByRegion()
            : base(MainModel.GetInstance())
        {
            ServiceTypes.Clear();
        }
        public override string Name
        {
            get { return "多时段路测问题对比分析(按区域)"; }
        }
        public override bool IsNeedSetQueryCondition
        {
            get { return false; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 20000, 20043, "分析");
        }
        protected override bool getCondition()
        {
            LteMultiCompareSettingDlg dlg = new LteMultiCompareSettingDlg();
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            curSet = dlg.curSet;
            condition = new QueryCondition();
            condition.Geometorys = MainModel.SearchGeometrys;
            condition.Projects = curSet.ProjectIDSet;
            condition.ServiceTypes = curSet.ServiceIDSet;
            if (curSet.IsFilterFile)
            {
                condition.NameFilterType = FileFilterType.ByFileName; 
                int orNum = 1;
                condition.FileName = formulaQueryString(curSet.StrFileNameFilter, ref orNum);
                condition.FileNameOrNum = orNum;
            }

            template = ReporterTemplateManager.LoadSingleReportFromFile(LteMultiCompareSettingDlg.StrRptTitleHead);
            if (template == null)
            {
                template = new ReporterTemplate();
            }
            return true;
        }
        private string formulaQueryString(string str, ref int orNum)
        {
            string uppStr = str.ToUpper();
            uppStr = uppStr.Replace(" OR ", " or ");
            string[] split = uppStr.Replace(" or ", QueryCondition.Splitor).Split(new string[] { QueryCondition.Splitor }, StringSplitOptions.RemoveEmptyEntries);
            orNum = split.Length;
            uppStr = uppStr.Replace(" or ", "{,}");
            return uppStr;
        }

        protected override void query()
        {
            roadInfoList = new List<MultiCompareRoadInfo>();
            problemDetailList = new List<ProblemDetailInfo>();
            if (MainModel.IsBackground && !MainModel.BackgroundStarted)
            {
                return;
            }
            if (!getCondition())
            {
                return;
            }
            getReadyBeforeQuery();
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
            }
            catch
            {
                //continue
            }
            finally
            {
                clientProxy.Close();
            }
            IsShowFileInfoForm = false;
            MainModel.IsDrawEventResult = false;
            if (!MainModel.IsBackground)
            {
                if (!MainModel.QueryFromBackground)
                {
                    foreach (TimePeriod period in curSet.Periods)
                    {
                        curRoadInfo = new MultiCompareRoadInfo(period);
                        curRoadInfo.SN = roadInfoList.Count + 1;
                        roadInfoList.Add(curRoadInfo);

                        condition.EventIDs = new List<int>();
                        condition.Periods.Clear();
                        condition.Periods.Add(period);

                        doStatWithProblemEvt();//分析道路问题
                        WaitBox.Show("开始统计自定义指标......", doStatWithDiyKpi, period);//统计自定义指标
                    }
                    doSomethingAfterAnalyseFiles();
                }
                else
                {
                    getBackgroundData();
                }
                if (!MainModel.QueryFromBackground)
                {
                    MainModel.FireDTDataChanged(this);
                    fireShowForm();
                    fireSetDefaultMapSerialTheme();
                }
                else
                {
                    initBackgroundImageDesc();
                }
            }
        }
        protected override void clearDataBeforeAnalyseFiles()
        {
            // Method intentionally left empty.
        }
        protected void doStatWithProblemEvt()
        {
            try
            {
                condition.EventIDs = curSet.SelectEvtIDSet;
                QueryEvtMsgInfoByRegion query = new QueryEvtMsgInfoByRegion(curSet.MsgParamList);
                query.SetQueryCondition(condition);
                query.IsAnaMsgCellInfo = curSet.IsAnaMessageCellInfo;
                query.Query();

                curRoadInfo.UnfitParamCount = query.UnfitParamCount;
                curRoadInfo.UnfitCellMsgInfoDic = query.UnfitCellMsgInfoDic;

                MainModel.EventResultList = new List<EventResult>();
                QueryProblemEvtInfoByRegion queryResultEventInfo = new QueryProblemEvtInfoByRegion(MainModel);
                condition.FilterOffValue9 = false;
                queryResultEventInfo.SetQueryCondition(condition);
                queryResultEventInfo.Query();


                foreach (Event evt in query.ProblemEvtList)
                {
                    setEvtDesc(evt);

                    if (curSet.SelectEvtIDSet.Contains(evt.ID))
                    {
                        curRoadInfo.ProblemUnFinishCount++;
                        addProblemUnFinishDic(evt);
                    }
                }
            }
            catch (Exception ee)
            {
                System.Windows.Forms.MessageBox.Show(ee.Message + Environment.NewLine + ee.Source + Environment.NewLine + ee.StackTrace);
            }
        }

        private void setEvtDesc(Event evt)
        {
            foreach (EventResult evtResult in MainModel.EventResultList)
            {
                if (evt.FileID == evtResult.FileID && evt.SN == evtResult.SN)
                {
                    string preReasonDesc = evtResult.preTypeDesc;
                    if (preReasonDesc.Length > 0 && preReasonDesc[preReasonDesc.Length - 1].ToString() == "|")
                    {
                        evt.pretype_desc = preReasonDesc.Remove(preReasonDesc.Length - 1, 1);
                    }
                    else
                    {
                        evt.pretype_desc = preReasonDesc;
                    }
                    evt.reason_desc = evtResult.reasonDesc;
                    break;
                }
            }
        }

        private void addProblemUnFinishDic(Event evt)
        {
            RoadProblemInfo roadInfo;
            if (curRoadInfo.ProblemUnFinishDic.ContainsKey(evt.ID))
            {
                roadInfo = curRoadInfo.ProblemUnFinishDic[evt.ID];
                roadInfo.ProblemEvtList.Add(evt);
            }
            else
            {
                roadInfo = new RoadProblemInfo(evt);
                curRoadInfo.ProblemUnFinishDic[evt.ID] = roadInfo;
            }

            if (!string.IsNullOrEmpty(evt.pretype_desc))
            {
                if (roadInfo.ReasonCountDic.ContainsKey(evt.pretype_desc))
                {
                    roadInfo.ReasonCountDic[evt.pretype_desc]++;
                }
                else
                {
                    roadInfo.ReasonCountDic[evt.pretype_desc] = 1;
                }
            }
        }

        protected void doStatWithDiyKpi(object obj)
        {
            try
            {
                TimePeriod period = obj as TimePeriod;
                WaitBox.CanCancel = true;
                WaitBox.Text = "正在统计时段[" + period.ToString() + "]的自定义指标...";
                QueryDiyKpiByRegion curQuery = new QueryDiyKpiByRegion(template, period);
                curQuery.SetQueryCondition(condition);
                curQuery.Query();
                List<StatInfoBase> statInfoList = new List<StatInfoBase>(curQuery.FileKeyDataDic.Values);
                if (statInfoList.Count > 0)
                {
                    curRoadInfo.StatInfo = statInfoList[0];
                }
            }
            finally
            {
                WaitBox.Close();
            }
        }

        protected override void doSomethingAfterAnalyseFiles()
        {
            MultiCompareRoadInfo lastInfo = null;
            foreach (MultiCompareRoadInfo curInfo in roadInfoList)
            {
                if (lastInfo != null)
                {
                    //检查上轮测试中未解决的问题点在本次是否再出现
                    foreach (var lastUnFinishInfoPair in lastInfo.ProblemUnFinishDic)
                    {
                        int lastUnFinishProblemId = lastUnFinishInfoPair.Key;
                        RoadProblemInfo lastUnFinishProblemInfo = lastUnFinishInfoPair.Value;
                        if (curInfo.ProblemUnFinishDic.ContainsKey(lastUnFinishProblemId))//若上轮测试和本轮测试出现同种问题
                        {
                            dealProblemUnFinish(curInfo, lastUnFinishProblemId, lastUnFinishProblemInfo);
                        }
                        else
                        {
                            //上轮测试出现的问题在本轮测试没出现视为已解决
                            curInfo.ProblemHasFinishCount += lastUnFinishProblemInfo.ProblemEvtList.Count;
                            curInfo.ProblemHasFinishDic[lastUnFinishProblemId] = new RoadProblemInfo(lastUnFinishProblemInfo);
                        }
                    }
                }
                curInfo.SetUnFinishProblemPercent();

                saveProblemDetail(curInfo);
                lastInfo = curInfo;
            }
        }

        private void saveProblemDetail(MultiCompareRoadInfo curInfo)
        {
            #region 保存问题详表
            foreach (var InfoPair in curInfo.ProblemHasFinishDic)
            {
                foreach (Event evt in InfoPair.Value.ProblemEvtList)
                {
                    addToProblemDetailList(curInfo, evt, true, curInfo.Period.ToString());
                }
            }

            foreach (var InfoPair in curInfo.ProblemUnFinishDic)
            {
                foreach (Event evt in InfoPair.Value.ProblemEvtList)
                {
                    addToProblemDetailList(curInfo, evt, false);
                }
            }
            #endregion
        }

        private void dealProblemUnFinish(MultiCompareRoadInfo curInfo, int lastUnFinishProblemId, RoadProblemInfo lastUnFinishProblemInfo)
        {
            foreach (Event lastInfoEvt in lastUnFinishProblemInfo.ProblemEvtList)
            {
                bool isSolve = true;
                foreach (Event curInfoEvt in curInfo.ProblemUnFinishDic[lastUnFinishProblemId].ProblemEvtList)
                {
                    double distance = MathFuncs.GetDistance(lastInfoEvt.Longitude, lastInfoEvt.Latitude, curInfoEvt.Longitude, curInfoEvt.Latitude);
                    if (distance <= curSet.DistanceEvt)//在设定范围内本轮又出现同样问题，认为未解决
                    {
                        isSolve = false;
                        break;
                    }
                }

                if (isSolve)
                {
                    curInfo.ProblemHasFinishCount++;
                    RoadProblemInfo roadInfo;
                    if (curInfo.ProblemHasFinishDic.ContainsKey(lastUnFinishProblemId))
                    {
                        roadInfo = curInfo.ProblemHasFinishDic[lastUnFinishProblemId];
                        roadInfo.ProblemEvtList.Add(lastInfoEvt);
                    }
                    else
                    {
                        roadInfo = new RoadProblemInfo(lastInfoEvt);
                        curInfo.ProblemHasFinishDic[lastUnFinishProblemId] = roadInfo;
                    }
                }
                else
                {
                    curInfo.ProblemUnFinishCount++;
                    curInfo.ProblemUnFinishDic[lastUnFinishProblemId].ProblemEvtList.Add(lastInfoEvt);
                }
            }
        }

        private void addToProblemDetailList(MultiCompareRoadInfo roadInfo, Event evt, bool isSolve, params string[] strSolveDate)
        {
            ProblemDetailInfo detailInfo = new ProblemDetailInfo();
            detailInfo.SN = problemDetailList.Count + 1;
            detailInfo.PeriodIndex = roadInfo.PeriodIndex;
            detailInfo.TestTime = roadInfo.Period.ToString();
            detailInfo.Area = evt.AreaPlaceDesc;
            detailInfo.DistrictName = DistrictManager.GetInstance().getDistrictName(evt.DistrictID);
            detailInfo.Manufacturers = evt.FileInfo.CompanyName;
            detailInfo.ProblemType = evt.Name;
            detailInfo.PreReasonDesc = evt.pretype_desc;
            detailInfo.ReasonDesc = "";
            detailInfo.IsSolve = isSolve;
            if (isSolve && strSolveDate != null)
            {
                detailInfo.SolveDate = strSolveDate[0];
            }
            detailInfo.Note = "";
            detailInfo.FileName = evt.FileName;
            detailInfo.RoadName = evt.RoadPlaceDesc;
            detailInfo.CellName = evt.CellNameSrc;
            detailInfo.Longitude = evt.Longitude;
            detailInfo.Latitude = evt.Latitude;
            detailInfo.Note = "";
            problemDetailList.Add(detailInfo);
        }

        bool isExportSuccess = false;
        protected override void fireShowForm()
        {
            if (curRoadInfo != null)
            {
                MainModel.ClearDTData();
                foreach (var roadProblemInfoPair in curRoadInfo.ProblemUnFinishDic)
                {
                    foreach (Event evt in roadProblemInfoPair.Value.ProblemEvtList)
                    {
                        MainModel.DTDataManager.Add(evt);
                    }
                }
                MainModel.FireDTDataChanged(this);

                try
                {
                    strTestDetailPath = string.Format("{0}\\各测试日期路测问题详表.xls", curSet.ReprortSavePath);
                    List<NPOIRow> problemDetailNpoiRows = convertToProblemDetailNpoi(problemDetailList);
                    ExcelNPOIManager.AutoExportExcel(problemDetailNpoiRows, strTestDetailPath);
                }
                catch (Exception ex)
                {
                    MessageBox.Show(ex.Message + Environment.NewLine + ex.StackTrace, "导出问题详表错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }

            if (curSet.IsAnaMessageCellInfo)
            {
                try
                {
                    List<NPOIRow> unFitParamDetailNpoiRows = convertToUnFitParamDetailNpoi(roadInfoList);
                    ExcelNPOIManager.AutoExportExcel(unFitParamDetailNpoiRows, curSet.ReprortSavePath + "\\各测试日期具体不符合参数列表.xls");
                }
                catch (Exception ex)
                {
                    MessageBox.Show(ex.Message + Environment.NewLine + ex.StackTrace, "导出不符合参数列表错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }

            QueryDiyKpiByRegion query = new QueryDiyKpiByRegion(template, null);
            List<NPOIRow> roadInfoNpoiRows = query.CreateReportNPOI(roadInfoList, curSet.IsAnaMessageCellInfo);
            try
            {
                ExcelNPOIManager.AutoExportExcel(roadInfoNpoiRows, curSet.ReprortSavePath + "\\各测试日期路测指标对比.xls");
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message + Environment.NewLine + ex.StackTrace, "导出路测指标对比错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }

            WaitTextBox.Show("正在导出分析报告......", doExportWordInThread, roadInfoNpoiRows);
            if (isExportSuccess)
            {
                MessageBox.Show("路测对比分析报告已成功导出！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        protected List<NPOIRow> convertToProblemDetailNpoi(List<ProblemDetailInfo> datas)
        {
            List<NPOIRow> outputRows = new List<NPOIRow>();
            try
            {
                NPOIRow row = new NPOIRow();
                outputRows.Add(row);

                row.AddCellValue("序号");
                row.AddCellValue("轮次");
                row.AddCellValue("测试日期");
                row.AddCellValue("片区");
                row.AddCellValue("分公司");
                row.AddCellValue("厂家");
                row.AddCellValue("问题类型");
                row.AddCellValue("平台原因预判");
                row.AddCellValue("实际原因");
                row.AddCellValue("是否解决");
                row.AddCellValue("解决日期");
                row.AddCellValue("文件名");
                row.AddCellValue("道路");
                row.AddCellValue("小区名称");
                row.AddCellValue("中心经度");
                row.AddCellValue("中心纬度");
                row.AddCellValue("备注");
                foreach (ProblemDetailInfo detailInfo in datas)
                {
                    row = new NPOIRow();
                    outputRows.Add(row);
                    row.cellValues = new List<object>(10);
                    row.cellValues.Add(detailInfo.SN);
                    row.cellValues.Add(detailInfo.PeriodIndex);
                    row.cellValues.Add(detailInfo.TestTime);
                    row.cellValues.Add(detailInfo.Area);
                    row.cellValues.Add(detailInfo.DistrictName);
                    row.cellValues.Add(detailInfo.Manufacturers);
                    row.cellValues.Add(detailInfo.ProblemType);
                    row.cellValues.Add(detailInfo.PreReasonDesc);
                    row.cellValues.Add(detailInfo.ReasonDesc);
                    row.cellValues.Add(detailInfo.IsSolve ? "是" : "否");
                    row.cellValues.Add(detailInfo.SolveDate);
                    row.cellValues.Add(detailInfo.FileName);
                    row.cellValues.Add(detailInfo.RoadName);
                    row.cellValues.Add(detailInfo.CellName);
                    row.cellValues.Add(detailInfo.Longitude);
                    row.cellValues.Add(detailInfo.Latitude);
                    row.cellValues.Add(detailInfo.Note);
                }
            }
            catch
            {
                //continue
            }

            return outputRows;
        }
        protected List<NPOIRow> convertToUnFitParamDetailNpoi(List<MultiCompareRoadInfo> datas)
        {
            List<NPOIRow> outputRows = new List<NPOIRow>();
            try
            {
                NPOIRow row = new NPOIRow();
                outputRows.Add(row);

                row.AddCellValue("序号");
                row.AddCellValue("轮次");
                row.AddCellValue("测试日期");
                row.AddCellValue("小区名称");
                row.AddCellValue("PCI");
                row.AddCellValue("TAC");
                row.AddCellValue("EARFCN");
                row.AddCellValue("参数名称");
                row.AddCellValue("设置数值");
                row.AddCellValue("参考正常值");

                int sn = 0;
                foreach (MultiCompareRoadInfo roadInfo in datas)
                {
                    foreach (var cellMsgInfoPair in roadInfo.UnfitCellMsgInfoDic)
                    {
                        CelllMessageInfo cellMsgInfo = cellMsgInfoPair.Value;
                        foreach (var paramInfoPair in cellMsgInfo.ParamInfoDic)
                        {
                            MessageParamInfo paramInfo = paramInfoPair.Value;
                            sn++;
                            row = new NPOIRow();
                            outputRows.Add(row);
                            row.cellValues = new List<object>(9);
                            row.cellValues.Add(sn);
                            row.cellValues.Add(roadInfo.PeriodIndex);
                            row.cellValues.Add(roadInfo.Period.ToString());
                            row.cellValues.Add(cellMsgInfo.LteCellSrc.Name);
                            row.cellValues.Add(cellMsgInfo.LteCellSrc.PCI);
                            row.cellValues.Add(cellMsgInfo.LteCellSrc.TAC);
                            row.cellValues.Add(cellMsgInfo.LteCellSrc.EARFCN);
                            row.cellValues.Add(paramInfo.ParamName);
                            row.cellValues.Add(paramInfo.ParamCurrentValue);
                            row.cellValues.Add(paramInfo.ParamRightValueDes);
                        }
                    }
                }
            }
            catch
            {
                //continue
            }

            return outputRows;
        }
        protected void doExportWordInThread(object objRoadNpoiInfo)
        {
            isExportSuccess = false;
            if (template == null)
            {
                return;
            }
            List<NPOIRow> roadInfoNpoiList = objRoadNpoiInfo as List<NPOIRow>;
            string fileName = curSet.ReprortSavePath + "\\路测分析报告.doc";
            WordControl word = null;
            try
            {
                if (System.IO.File.Exists(fileName))
                {
                    System.IO.File.Delete(fileName);
                }
                word = new WordControl(false);

                dtProblemInfo(word);
                dataCompare(roadInfoNpoiList, word);

                if (!System.IO.File.Exists(fileName))
                {
                    word.SavedAsWord(fileName);
                }
                else
                {
                    word.SavedWord();
                }
                word = null;
                isExportSuccess = true;
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message + Environment.NewLine + ex.StackTrace, "导出报告错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                if (word != null)
                {
                    word.CloseWord();
                }
                System.Threading.Thread.Sleep(300);
                WaitTextBox.Close();
            }
        }

        private void dtProblemInfo(WordControl word)
        {
            string strTxt = "";
            #region 路测问题解决情况
            word.InsertText("LTE路测分析报告", "标题");
            word.NewLine();
            word.InsertText("1. 路测问题解决情况分析", "标题 1");
            word.NewLine();
            word.InsertText("1.1	各测试日期的路测问题数量", "标题 2");
            word.NewLine();

            MultiCompareRoadInfo lastInfo = null;
            if (roadInfoList.Count > 0)
            {
                lastInfo = roadInfoList[roadInfoList.Count - 1];
            }

            if (lastInfo != null)
            {
                strTxt = string.Format("测试日期{0}分析出有{1}个问题，解决了{2}个问题，还有{3}个问题未解决。"
                    , lastInfo.PeriodIndex, lastInfo.ProblemTotalCount, lastInfo.ProblemHasFinishCount, lastInfo.ProblemUnFinishCount);
                word.InsertText(strTxt, "正文");
                word.NewLine();

                string[,] everyDateData = new string[4, roadInfoList.Count + 1];
                everyDateData[1, 0] = "总数量";
                everyDateData[2, 0] = "已解决";
                everyDateData[3, 0] = "未解决";
                StringBuilder strbNote = new StringBuilder();
                strbNote.AppendLine("注:");
                int index = 1;
                foreach (MultiCompareRoadInfo curInfo in roadInfoList)
                {
                    everyDateData[0, index] = curInfo.PeriodIndex;
                    everyDateData[1, index] = curInfo.ProblemTotalCount.ToString();
                    everyDateData[2, index] = curInfo.ProblemHasFinishCount.ToString();
                    everyDateData[3, index] = curInfo.ProblemUnFinishCount.ToString();
                    strbNote.AppendLine(curInfo.PeriodIndex + ":" + curInfo.Period.ToString());
                    index++;
                }
                word.InsertChart("各测试日期的路测问题情况", Microsoft.Office.Interop.Graph.XlChartType.xlColumnClustered, everyDateData);

                word.InsertText(strbNote.ToString(), "正文");
                word.NewLine();
                word.InsertText("各测试日期路测问题详表：", "正文");
                word.InsertFile(strTestDetailPath);
                word.NewLine();

                if (lastInfo.ProblemUnFinishDic.Count > 0)
                {
                    word.InsertBreak();
                    strTxt = lastInfo.PeriodIndex + "未解决的路测问题分布：";
                    word.InsertText(strTxt, "正文");
                    word.NewLine();
                    word.NewLine();

                    Word.Table wordTable = word.CreateTable(4, lastInfo.ProblemUnFinishDic.Count + 1);
                    wordTable.Cell(1, 1).Range.Text = "问题事件ID";
                    wordTable.Cell(2, 1).Range.Text = "问题事件名称";
                    wordTable.Cell(3, 1).Range.Text = "问题占比";
                    wordTable.Cell(4, 1).Range.Text = "问题个数";

                    string[,] lastInfoData = new string[2, lastInfo.ProblemUnFinishDic.Count + 1];
                    index = 1;
                    foreach (var problemInfo in lastInfo.ProblemUnFinishDic)
                    {
                        wordTable.Cell(1, index + 1).Range.Text = problemInfo.Value.ProblemEvtId.ToString();
                        wordTable.Cell(2, index + 1).Range.Text = problemInfo.Value.ProblemName;
                        wordTable.Cell(3, index + 1).Range.Text = problemInfo.Value.Percent.ToString() + "%";
                        wordTable.Cell(4, index + 1).Range.Text = problemInfo.Value.ProblemTimes.ToString();

                        lastInfoData[0, index] = problemInfo.Value.ProblemEvtId.ToString();
                        lastInfoData[1, index] = problemInfo.Value.Percent.ToString() + "%";
                        index++;
                    }
                    wordTable.Columns.AutoFit();
                    word.InsertChart(lastInfo.PeriodIndex + "路测中各类未解决的问题对比图", Microsoft.Office.Interop.Graph.XlChartType.xlPie, lastInfoData);
                }
            }
            #endregion
        }

        private void dataCompare(List<NPOIRow> roadInfoNpoiList, WordControl word)
        {
            #region 指标对比
            word.InsertBreak();
            word.InsertText("2. 各测试日期路测指标对比", "标题 1");
            word.NewLine();
            word.InsertText("2.1	各测试日期基本信息对比", "标题 2");
            word.NewLine();

            int titleBaseCount = QueryDiyKpiByRegion.TitleBaseCount;
            Word.Table wtbBasicInfo = word.CreateTable(roadInfoNpoiList.Count, template.KeyCount + titleBaseCount);
            for (int i = 0; i < roadInfoNpoiList.Count; i++)
            {
                NPOIRow row = roadInfoNpoiList[i];
                for (int j = 0; j < template.KeyCount + titleBaseCount; j++)
                {
                    wtbBasicInfo.Cell(i + 1, j + 1).Range.Text = row.cellValues[j].ToString();
                }
            }
            wtbBasicInfo.Columns.AutoFit();

            //word.InsertBreak();
            int titleIndex = 2;
            for (int i = template.KeyCount + titleBaseCount; i < roadInfoNpoiList[0].cellValues.Count; i++)
            {
                List<int> maxValueRowIndexList = new List<int>();
                List<int> minValueRowIndexList = new List<int>();
                string[,] everyKpiData = new string[2, roadInfoNpoiList.Count];

                dealroadInfo(roadInfoNpoiList, i, ref maxValueRowIndexList, ref minValueRowIndexList, everyKpiData);

                StringBuilder strbMaxValuePeriods = new StringBuilder();
                StringBuilder strbMaxValueUnfinishReason = new StringBuilder();
                dealValueRow(roadInfoNpoiList, maxValueRowIndexList, strbMaxValuePeriods, strbMaxValueUnfinishReason);

                StringBuilder strbMinValuePeriods = new StringBuilder();
                StringBuilder strbMinValueUnfinishReason = new StringBuilder();
                dealValueRow(roadInfoNpoiList, minValueRowIndexList, strbMinValuePeriods, strbMinValueUnfinishReason);

                string strTitle = string.Format("2.{0}	各测试日期的路测{1}对比", titleIndex, roadInfoNpoiList[0].cellValues[i].ToString());
                word.InsertText(strTitle, "标题 2");
                word.NewLine();
                StringBuilder str = new StringBuilder();
                str.AppendFormat("{0}轮测试中，{1}最高的是{2}时段的测试",
                    roadInfoNpoiList.Count - 1, roadInfoNpoiList[0].cellValues[i].ToString(), strbMaxValuePeriods.ToString());
                if (strbMaxValueUnfinishReason.Length > 0)
                {
                    str.Append("，其中" + strbMaxValueUnfinishReason.ToString());
                }
                word.InsertText(str.ToString() + "。", "正文");
                word.NewLine();

                str = new StringBuilder();
                str.AppendFormat("{0}最低的是{1}时段的测试", roadInfoNpoiList[0].cellValues[i].ToString(), strbMinValuePeriods.ToString());
                if (strbMinValueUnfinishReason.Length > 0)
                {
                    str.Append("，其中" + strbMinValueUnfinishReason.ToString());
                }
                word.InsertText(str.ToString() + "。", "正文");
                word.NewLine();

                int endIndex = roadInfoList.Count;
                if (!maxValueRowIndexList.Contains(endIndex) && !minValueRowIndexList.Contains(endIndex))
                {
                    string strDescSum = roadInfoList[endIndex - 1].GetUnFinishPrbReasonDescSum();
                    if (!string.IsNullOrEmpty(strDescSum))
                    {
                        word.NewLine();
                        string strTxt = string.Format("最近{0}时段测试存在{1}。", roadInfoList[endIndex - 1].PeriodIndex, strDescSum);
                        word.InsertText(strTxt, "正文");
                    }
                }

                word.InsertChart(roadInfoNpoiList[0].cellValues[i].ToString(), Microsoft.Office.Interop.Graph.XlChartType.xlColumnClustered, everyKpiData, false);

                titleIndex++;
            }
            #endregion
        }

        private void dealroadInfo(List<NPOIRow> roadInfoNpoiList, int i, ref List<int> maxValueRowIndexList, ref List<int> minValueRowIndexList, string[,] everyKpiData)
        {
            double maxDVal = double.MinValue;
            double minDVal = double.MaxValue;
            double dVal;
            string strVal;
            for (int j = 1; j < roadInfoNpoiList.Count; j++)
            {
                strVal = roadInfoNpoiList[j].cellValues[i].ToString().Trim();

                everyKpiData[0, j] = roadInfoNpoiList[j].cellValues[0].ToString();
                everyKpiData[1, j] = strVal;

                strVal = strVal.Replace("%", "");
                if (double.TryParse(strVal, out dVal))
                {
                    if (dVal > maxDVal)
                    {
                        maxDVal = dVal;
                        maxValueRowIndexList = new List<int> { j };
                    }
                    else if (dVal == maxDVal)
                    {
                        maxValueRowIndexList.Add(j);
                    }

                    if (dVal < minDVal)
                    {
                        minDVal = dVal;
                        minValueRowIndexList = new List<int> { j };
                    }
                    else if (dVal == minDVal)
                    {
                        minValueRowIndexList.Add(j);
                    }
                }
            }
        }

        private void dealValueRow(List<NPOIRow> roadInfoNpoiList, List<int> valueRowIndexList, StringBuilder strbValuePeriods, StringBuilder strbValueUnfinishReason)
        {
            foreach (int rowIndex in valueRowIndexList)
            {
                strbValuePeriods.Append(string.Format("{0}({1})、", roadInfoNpoiList[rowIndex].cellValues[0], roadInfoNpoiList[rowIndex].cellValues[1]));
                if (roadInfoList[rowIndex - 1].PeriodIndex == roadInfoNpoiList[rowIndex].cellValues[0].ToString())
                {
                    string strDescSum = roadInfoList[rowIndex - 1].GetUnFinishPrbReasonDescSum();
                    if (!string.IsNullOrEmpty(strDescSum))
                    {
                        strbValueUnfinishReason.Append(roadInfoList[rowIndex - 1].PeriodIndex + "存在" + strDescSum);
                    }
                }
            }
            if (strbValuePeriods.Length > 0)
            {
                strbValuePeriods.Remove(strbValuePeriods.Length - 1, 1);
            }
        }
    }
}
