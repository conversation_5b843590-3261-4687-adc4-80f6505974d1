﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class LtePlanningTestPoint
    {
        public TestPoint TestPoint { get; set; }

        public int? Tac { get; set; }
        public int? Eci { get; set; }
        public int? Earfcn { get; set; }
        public int? Pci { get; set; }
        public float? Rsrp { get; set; }
        public float? Sinr { get; set; }
        public double CellDistance { get; set; }

        public List<int?> NbEarfcnList { get; set; } = new List<int?>();
        public List<int?> NbPciList { get; set; } = new List<int?>();
        public List<float?> NbRsrpList { get; set; } = new List<float?>();
        public List<float?> NbSinrList { get; set; } = new List<float?>();

        public LTECell MainCell { get; set; }
        public List<LTECell> NbCells { get; set; } = new List<LTECell>();

        public LtePlanningTestPoint(TestPoint tp)
        {
            TestPoint = tp;
            Tac = (int?)(ushort?)tp["lte_TAC"];
            Eci = (int?)tp["lte_ECI"];
            Earfcn = (int?)tp["lte_EARFCN"];
            Pci = (int?)(short?)tp["lte_PCI"];
            Rsrp = (float?)tp["lte_RSRP"];
            Sinr = (float?)tp["lte_SINR"];

            for (int i = 0; i < 6; ++i)
            {
                int? nbEarfcn = (int?)tp["lte_NCell_EARFCN", i];
                int? nbPci = (int?)(short?)tp["lte_NCell_PCI", i];
                if (nbEarfcn == null || nbPci == null)
                {
                    break;
                }
                float? nbRsrp = (float?)tp["lte_NCell_RSRP"];
                float? nbSinr = (float?)tp["lte_NCell_SINR"];

                NbEarfcnList.Add(nbEarfcn);
                NbPciList.Add(nbPci);
                NbRsrpList.Add(nbRsrp);
                NbSinrList.Add(nbSinr);
            }

            MainCell = GetMainCell();
            if (MainCell != null)
            {
                CellDistance = MathFuncs.GetDistance(tp.Longitude, tp.Latitude, MainCell.Longitude, MainCell.Latitude);
            }

            for (int i = 0; i < 6; ++i)
            {
                LTECell nbCell = GetNbCell(i);
                if (nbCell == null)
                {
                    break;
                }
                NbCells.Add(nbCell);
            }
        }

        private LTECell GetMainCell()
        {
            return LtePlanningInfoManager.Instance.GetLteCell(Tac, Eci, Earfcn, Pci, TestPoint.Longitude, TestPoint.Latitude);
        }

        private LTECell GetNbCell(int nbIndex)
        {
            if (nbIndex >= NbEarfcnList.Count)
            {
                return null;
            }
            return LtePlanningInfoManager.Instance.GetLteCell(null, null, NbEarfcnList[nbIndex], NbPciList[nbIndex], TestPoint.Longitude, TestPoint.Latitude);
        }
    }
}
