﻿namespace MasterCom.RAMS.Func
{
    partial class CellNeighbourInfoForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.Windows.Forms.Label label1;
            System.Windows.Forms.Label label12;
            this.panel1 = new System.Windows.Forms.Panel();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.labelColorNeighbourEachOther = new System.Windows.Forms.Label();
            this.labelColorNeighbour = new System.Windows.Forms.Label();
            this.listView = new BrightIdeasSoftware.ObjectListView();
            this.olvColumnIndex = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnRelation = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnCode = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnName = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnLAC = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnCI = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnBCCH = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnBSIC = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnLongitude = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnLatitude = new BrightIdeasSoftware.OLVColumn();
            this.contextMenuStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExport2xls = new System.Windows.Forms.ToolStripMenuItem();
            label1 = new System.Windows.Forms.Label();
            label12 = new System.Windows.Forms.Label();
            this.panel1.SuspendLayout();
            this.groupBox1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.listView)).BeginInit();
            this.contextMenuStrip.SuspendLayout();
            this.SuspendLayout();
            // 
            // label1
            // 
            label1.AutoSize = true;
            label1.Location = new System.Drawing.Point(166, 30);
            label1.Name = "label1";
            label1.Size = new System.Drawing.Size(53, 12);
            label1.TabIndex = 78;
            label1.Text = "双向邻区";
            label1.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // label12
            // 
            label12.AutoSize = true;
            label12.Location = new System.Drawing.Point(18, 30);
            label12.Name = "label12";
            label12.Size = new System.Drawing.Size(53, 12);
            label12.TabIndex = 76;
            label12.Text = "单向邻区";
            label12.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // panel1
            // 
            this.panel1.Controls.Add(this.groupBox1);
            this.panel1.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.panel1.Location = new System.Drawing.Point(0, 322);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(752, 61);
            this.panel1.TabIndex = 1;
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(label1);
            this.groupBox1.Controls.Add(this.labelColorNeighbourEachOther);
            this.groupBox1.Controls.Add(label12);
            this.groupBox1.Controls.Add(this.labelColorNeighbour);
            this.groupBox1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupBox1.Location = new System.Drawing.Point(0, 0);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(752, 61);
            this.groupBox1.TabIndex = 0;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "图例说明";
            // 
            // labelColorNeighbourEachOther
            // 
            this.labelColorNeighbourEachOther.BackColor = System.Drawing.Color.Red;
            this.labelColorNeighbourEachOther.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D;
            this.labelColorNeighbourEachOther.Location = new System.Drawing.Point(225, 24);
            this.labelColorNeighbourEachOther.Name = "labelColorNeighbourEachOther";
            this.labelColorNeighbourEachOther.Size = new System.Drawing.Size(25, 25);
            this.labelColorNeighbourEachOther.TabIndex = 77;
            // 
            // labelColorNeighbour
            // 
            this.labelColorNeighbour.BackColor = System.Drawing.Color.Red;
            this.labelColorNeighbour.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D;
            this.labelColorNeighbour.Location = new System.Drawing.Point(77, 24);
            this.labelColorNeighbour.Name = "labelColorNeighbour";
            this.labelColorNeighbour.Size = new System.Drawing.Size(25, 25);
            this.labelColorNeighbour.TabIndex = 75;
            // 
            // listView
            // 
            this.listView.AllColumns.Add(this.olvColumnIndex);
            this.listView.AllColumns.Add(this.olvColumnRelation);
            this.listView.AllColumns.Add(this.olvColumnCode);
            this.listView.AllColumns.Add(this.olvColumnName);
            this.listView.AllColumns.Add(this.olvColumnLAC);
            this.listView.AllColumns.Add(this.olvColumnCI);
            this.listView.AllColumns.Add(this.olvColumnBCCH);
            this.listView.AllColumns.Add(this.olvColumnBSIC);
            this.listView.AllColumns.Add(this.olvColumnLongitude);
            this.listView.AllColumns.Add(this.olvColumnLatitude);
            this.listView.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnIndex,
            this.olvColumnRelation,
            this.olvColumnCode,
            this.olvColumnName,
            this.olvColumnLAC,
            this.olvColumnCI,
            this.olvColumnBCCH,
            this.olvColumnBSIC,
            this.olvColumnLongitude,
            this.olvColumnLatitude});
            this.listView.ContextMenuStrip = this.contextMenuStrip;
            this.listView.Cursor = System.Windows.Forms.Cursors.Default;
            this.listView.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listView.FullRowSelect = true;
            this.listView.GridLines = true;
            this.listView.HeaderWordWrap = true;
            this.listView.Location = new System.Drawing.Point(0, 0);
            this.listView.Name = "listView";
            this.listView.ShowGroups = false;
            this.listView.Size = new System.Drawing.Size(752, 322);
            this.listView.TabIndex = 2;
            this.listView.UseCompatibleStateImageBehavior = false;
            this.listView.View = System.Windows.Forms.View.Details;
            this.listView.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.listView_MouseDoubleClick);
            // 
            // olvColumnIndex
            // 
            this.olvColumnIndex.HeaderFont = null;
            this.olvColumnIndex.Text = "序号";
            // 
            // olvColumnRelation
            // 
            this.olvColumnRelation.HeaderFont = null;
            this.olvColumnRelation.Text = "单双向";
            // 
            // olvColumnCode
            // 
            this.olvColumnCode.AspectName = "Code";
            this.olvColumnCode.HeaderFont = null;
            this.olvColumnCode.Text = "小区号";
            this.olvColumnCode.Width = 80;
            // 
            // olvColumnName
            // 
            this.olvColumnName.AspectName = "Name";
            this.olvColumnName.HeaderFont = null;
            this.olvColumnName.Text = "小区名";
            this.olvColumnName.Width = 100;
            // 
            // olvColumnLAC
            // 
            this.olvColumnLAC.AspectName = "LAC";
            this.olvColumnLAC.HeaderFont = null;
            this.olvColumnLAC.Text = "LAC";
            // 
            // olvColumnCI
            // 
            this.olvColumnCI.AspectName = "CI";
            this.olvColumnCI.HeaderFont = null;
            this.olvColumnCI.Text = "CI";
            // 
            // olvColumnBCCH
            // 
            this.olvColumnBCCH.AspectName = "BCCH";
            this.olvColumnBCCH.HeaderFont = null;
            this.olvColumnBCCH.Text = "BCCH";
            // 
            // olvColumnBSIC
            // 
            this.olvColumnBSIC.AspectName = "BSIC";
            this.olvColumnBSIC.HeaderFont = null;
            this.olvColumnBSIC.Text = "BSIC";
            // 
            // olvColumnLongitude
            // 
            this.olvColumnLongitude.AspectName = "Longitude";
            this.olvColumnLongitude.HeaderFont = null;
            this.olvColumnLongitude.Text = "经度";
            this.olvColumnLongitude.Width = 80;
            // 
            // olvColumnLatitude
            // 
            this.olvColumnLatitude.AspectName = "Latitude";
            this.olvColumnLatitude.HeaderFont = null;
            this.olvColumnLatitude.Text = "纬度";
            this.olvColumnLatitude.Width = 80;
            // 
            // contextMenuStrip
            // 
            this.contextMenuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExport2xls});
            this.contextMenuStrip.Name = "contextMenuStrip";
            this.contextMenuStrip.Size = new System.Drawing.Size(142, 26);
            // 
            // miExport2xls
            // 
            this.miExport2xls.Name = "miExport2xls";
            this.miExport2xls.Size = new System.Drawing.Size(141, 22);
            this.miExport2xls.Text = "导出到Excel";
            this.miExport2xls.Click += new System.EventHandler(this.miExport2xls_Click);
            // 
            // CellNeighbourInfoForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(752, 383);
            this.Controls.Add(this.listView);
            this.Controls.Add(this.panel1);
            this.Name = "CellNeighbourInfoForm";
            this.Text = "小区及邻区信息";
            this.Activated += new System.EventHandler(this.CellNeighbourInfoForm_Activated);
            this.panel1.ResumeLayout(false);
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.listView)).EndInit();
            this.contextMenuStrip.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.Panel panel1;
        private BrightIdeasSoftware.ObjectListView listView;
        private BrightIdeasSoftware.OLVColumn olvColumnIndex;
        private BrightIdeasSoftware.OLVColumn olvColumnRelation;
        private BrightIdeasSoftware.OLVColumn olvColumnCode;
        private BrightIdeasSoftware.OLVColumn olvColumnName;
        private BrightIdeasSoftware.OLVColumn olvColumnLAC;
        private BrightIdeasSoftware.OLVColumn olvColumnCI;
        private BrightIdeasSoftware.OLVColumn olvColumnBCCH;
        private BrightIdeasSoftware.OLVColumn olvColumnBSIC;
        private BrightIdeasSoftware.OLVColumn olvColumnLongitude;
        private BrightIdeasSoftware.OLVColumn olvColumnLatitude;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.Label labelColorNeighbourEachOther;
        private System.Windows.Forms.Label labelColorNeighbour;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip;
        private System.Windows.Forms.ToolStripMenuItem miExport2xls;

    }
}