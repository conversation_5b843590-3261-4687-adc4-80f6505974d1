﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class SiteDistanceCondSetDlg : BaseDialog
    {
        public SiteDistanceCondSetDlg()
        {
            InitializeComponent();
        }

        public bool IsQueryGSMCell
        {
            get { return this.checkEditCell.Checked; }
        }

        public bool IsQueryTDCell
        {
            get { return this.checkEditTDCell.Checked; }
        }

        public bool IsQueryWCell
        {
            get { return this.checkEditWCell.Checked; }
        }

        public double MaxDistanceToCell
        {
            get { return (double)this.spinEditMaxDistance.Value; }
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            //
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        public bool IsQueryLTECell {
            get { return this.checkLTE.Checked; }
        }

        public bool Is<PERSON>ueryNR<PERSON>ell
        {
            get { return this.checkNR.Checked; }
        }
    }
}
