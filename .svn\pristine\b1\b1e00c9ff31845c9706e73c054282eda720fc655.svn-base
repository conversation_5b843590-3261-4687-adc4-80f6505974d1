﻿namespace MasterCom.RAMS.Stat
{
    partial class CommonNoGisStatForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.Windows.Forms.ColumnHeader columnHeader2;
            System.Windows.Forms.ColumnHeader columnHeader3;
            System.Windows.Forms.ColumnHeader columnHeader4;
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(CommonNoGisStatForm));
            this.dropCondPanel = new ScrewTurn.DropDownPanel();
            this.panel2 = new System.Windows.Forms.Panel();
            this.groupBox6 = new System.Windows.Forms.GroupBox();
            this.lblQueryTime = new System.Windows.Forms.Label();
            this.cbxQueryFunc = new System.Windows.Forms.ComboBox();
            this.lbprogress = new System.Windows.Forms.Label();
            this.btnCancelRun = new System.Windows.Forms.Button();
            this.lbprogressText = new System.Windows.Forms.Label();
            this.progBar = new System.Windows.Forms.ProgressBar();
            this.lbStatus = new System.Windows.Forms.Label();
            this.btnDoRun = new System.Windows.Forms.Button();
            this.groupBoxFileName = new System.Windows.Forms.GroupBox();
            this.ccbePort = new DevExpress.XtraEditors.CheckedComboBoxEdit();
            this.cbxfileNumType = new System.Windows.Forms.ComboBox();
            this.btnClearPort = new System.Windows.Forms.Button();
            this.label_fileName = new System.Windows.Forms.Label();
            this.textBoxFileName = new System.Windows.Forms.TextBox();
            this.checkBoxFileName = new System.Windows.Forms.CheckBox();
            this.label10 = new System.Windows.Forms.Label();
            this.groupBoxPath = new System.Windows.Forms.GroupBox();
            this.btnSelectPath = new System.Windows.Forms.Button();
            this.labelPath = new System.Windows.Forms.Label();
            this.btnQuickSet = new System.Windows.Forms.Button();
            this.groupBox5 = new System.Windows.Forms.GroupBox();
            this.txtFilterReport = new System.Windows.Forms.TextBox();
            this.treeListParam = new DevExpress.XtraTreeList.TreeList();
            this.colKey = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.cMReport = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.addTeam = new System.Windows.Forms.ToolStripMenuItem();
            this.addSaveCurReport = new System.Windows.Forms.ToolStripMenuItem();
            this.saveChange = new System.Windows.Forms.ToolStripMenuItem();
            this.deteleSunReport = new System.Windows.Forms.ToolStripMenuItem();
            this.btnUpdateConditionTime = new System.Windows.Forms.Button();
            this.btnOneKeyQuery = new System.Windows.Forms.Button();
            this.btnMultiOK = new System.Windows.Forms.Button();
            this.label24 = new System.Windows.Forms.Label();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.label9 = new System.Windows.Forms.Label();
            this.checkAllCity = new System.Windows.Forms.CheckBox();
            this.listViewCity = new System.Windows.Forms.ListView();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.btnImportFilter = new System.Windows.Forms.Button();
            this.btnGainTemplate = new System.Windows.Forms.Button();
            this.btnResetArea = new System.Windows.Forms.Button();
            this.btnImport = new System.Windows.Forms.Button();
            this.lblFilter = new System.Windows.Forms.Label();
            this.txbFilter = new System.Windows.Forms.TextBox();
            this.cbxAllArea = new System.Windows.Forms.CheckBox();
            this.chListArea = new System.Windows.Forms.CheckedListBox();
            this.lbSelAreaType = new System.Windows.Forms.Label();
            this.label1 = new System.Windows.Forms.Label();
            this.groupBoxCarrier = new System.Windows.Forms.GroupBox();
            this.label_carrier = new System.Windows.Forms.Label();
            this.listViewCarrier = new System.Windows.Forms.ListView();
            this.groupBox4 = new System.Windows.Forms.GroupBox();
            this.listViewService = new System.Windows.Forms.ListView();
            this.lbSvCount = new System.Windows.Forms.Label();
            this.btnPopupService = new System.Windows.Forms.Button();
            this.tbTimeValue = new System.Windows.Forms.TabControl();
            this.tabPagetime = new System.Windows.Forms.TabPage();
            this.chbReject = new System.Windows.Forms.CheckBox();
            this.checkBoxPeriodAdvance = new System.Windows.Forms.CheckBox();
            this.panelCustomTime = new System.Windows.Forms.Panel();
            this.dateTimeTo = new System.Windows.Forms.DateTimePicker();
            this.label3 = new System.Windows.Forms.Label();
            this.dateTimeFrom = new System.Windows.Forms.DateTimePicker();
            this.label2 = new System.Windows.Forms.Label();
            this.panelAdvanceTime = new System.Windows.Forms.Panel();
            this.buttonPeriodClear = new System.Windows.Forms.Button();
            this.buttonPeriodAdd = new System.Windows.Forms.Button();
            this.listBoxPeriod = new System.Windows.Forms.ListBox();
            this.tabPageround = new System.Windows.Forms.TabPage();
            this.cbxProjToYear = new System.Windows.Forms.ComboBox();
            this.cbxProjFromYear = new System.Windows.Forms.ComboBox();
            this.cbxProjToRound = new System.Windows.Forms.ComboBox();
            this.cbxProjFromRound = new System.Windows.Forms.ComboBox();
            this.label5 = new System.Windows.Forms.Label();
            this.label8 = new System.Windows.Forms.Label();
            this.label7 = new System.Windows.Forms.Label();
            this.label6 = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.tabPageElapse = new System.Windows.Forms.TabPage();
            this.dateTimeElapse = new System.Windows.Forms.DateTimePicker();
            this.numElapseTo = new System.Windows.Forms.NumericUpDown();
            this.label22 = new System.Windows.Forms.Label();
            this.label23 = new System.Windows.Forms.Label();
            this.label25 = new System.Windows.Forms.Label();
            this.groupBox7 = new System.Windows.Forms.GroupBox();
            this.cbxRptList = new System.Windows.Forms.ComboBox();
            this.label11 = new System.Windows.Forms.Label();
            this.cbxShowMerge = new System.Windows.Forms.CheckBox();
            this.groupBoxDaiwei = new System.Windows.Forms.GroupBox();
            this.label_daiwei = new System.Windows.Forms.Label();
            this.chbAllAgent = new System.Windows.Forms.CheckBox();
            this.listViewAgent = new System.Windows.Forms.ListView();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.cbxAllProject = new System.Windows.Forms.CheckBox();
            this.chkListProject = new System.Windows.Forms.CheckedListBox();
            this.cbxElapse = new System.Windows.Forms.CheckBox();
            this.listItem1 = new MasterCom.Util.UiEx.ListItem();
            this.listItem2 = new MasterCom.Util.UiEx.ListItem();
            this.listItem3 = new MasterCom.Util.UiEx.ListItem();
            this.listItem4 = new MasterCom.Util.UiEx.ListItem();
            this.listItem5 = new MasterCom.Util.UiEx.ListItem();
            this.listItem6 = new MasterCom.Util.UiEx.ListItem();
            this.listItem7 = new MasterCom.Util.UiEx.ListItem();
            this.listItem8 = new MasterCom.Util.UiEx.ListItem();
            this.listItem9 = new MasterCom.Util.UiEx.ListItem();
            this.listItem10 = new MasterCom.Util.UiEx.ListItem();
            this.sortedDataGrid = new Mobius.Utility.SortedDataGridView();
            this.bgWorker = new System.ComponentModel.BackgroundWorker();
            this.toolTipBak = new System.Windows.Forms.ToolTip(this.components);
            this.statusStrip1 = new System.Windows.Forms.StatusStrip();
            this.panel1 = new System.Windows.Forms.Panel();
            this.toolStripDropDownService = new System.Windows.Forms.ToolStripDropDown();
            this.ctxQuickSet = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.toolStripSeparator1 = new System.Windows.Forms.ToolStripSeparator();
            this.miSaveCurSet = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripDropDownEquipment = new System.Windows.Forms.ToolStripDropDown();
            columnHeader2 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            columnHeader3 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            columnHeader4 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.dropCondPanel.SuspendLayout();
            this.panel2.SuspendLayout();
            this.groupBox6.SuspendLayout();
            this.groupBoxFileName.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.ccbePort.Properties)).BeginInit();
            this.groupBoxPath.SuspendLayout();
            this.groupBox5.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.treeListParam)).BeginInit();
            this.cMReport.SuspendLayout();
            this.groupBox2.SuspendLayout();
            this.groupBox1.SuspendLayout();
            this.groupBoxCarrier.SuspendLayout();
            this.groupBox4.SuspendLayout();
            this.tbTimeValue.SuspendLayout();
            this.tabPagetime.SuspendLayout();
            this.panelCustomTime.SuspendLayout();
            this.panelAdvanceTime.SuspendLayout();
            this.tabPageround.SuspendLayout();
            this.tabPageElapse.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numElapseTo)).BeginInit();
            this.groupBox7.SuspendLayout();
            this.groupBoxDaiwei.SuspendLayout();
            this.groupBox3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.sortedDataGrid)).BeginInit();
            this.panel1.SuspendLayout();
            this.ctxQuickSet.SuspendLayout();
            this.SuspendLayout();
            // 
            // columnHeader2
            // 
            columnHeader2.Width = 140;
            // 
            // columnHeader3
            // 
            columnHeader3.Width = 150;
            // 
            // columnHeader4
            // 
            columnHeader4.Width = 150;
            // 
            // dropCondPanel
            // 
            this.dropCondPanel.AutoCollapseDelay = -1;
            this.dropCondPanel.Controls.Add(this.panel2);
            this.dropCondPanel.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.dropCondPanel.EnableHeaderMenu = true;
            this.dropCondPanel.ExpandAnimationSpeed = ScrewTurn.AnimationSpeed.High;
            this.dropCondPanel.Expanded = true;
            this.dropCondPanel.HeaderFont = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.dropCondPanel.HeaderHeight = 23;
            this.dropCondPanel.HeaderIconNormal = global::MasterCom.RAMS.Properties.Resources.stat;
            this.dropCondPanel.HeaderIconOver = global::MasterCom.RAMS.Properties.Resources.stat;
            this.dropCondPanel.HeaderText = "统计条件";
            this.dropCondPanel.HomeLocation = new System.Drawing.Point(0, 386);
            this.dropCondPanel.HotTrackStyle = ScrewTurn.HotTrackStyle.Both;
            this.dropCondPanel.Location = new System.Drawing.Point(0, 386);
            this.dropCondPanel.ManageControls = false;
            this.dropCondPanel.Moveable = false;
            this.dropCondPanel.Name = "dropCondPanel";
            this.dropCondPanel.RoundedCorners = true;
            this.dropCondPanel.Size = new System.Drawing.Size(1284, 317);
            this.dropCondPanel.TabIndex = 0;
            // 
            // panel2
            // 
            this.panel2.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.panel2.AutoScroll = true;
            this.panel2.Controls.Add(this.groupBox6);
            this.panel2.Controls.Add(this.groupBoxFileName);
            this.panel2.Controls.Add(this.groupBoxPath);
            this.panel2.Controls.Add(this.btnQuickSet);
            this.panel2.Controls.Add(this.groupBox5);
            this.panel2.Controls.Add(this.groupBox2);
            this.panel2.Controls.Add(this.groupBox1);
            this.panel2.Controls.Add(this.groupBoxCarrier);
            this.panel2.Controls.Add(this.groupBox4);
            this.panel2.Controls.Add(this.tbTimeValue);
            this.panel2.Controls.Add(this.groupBox7);
            this.panel2.Controls.Add(this.groupBoxDaiwei);
            this.panel2.Controls.Add(this.groupBox3);
            this.panel2.Controls.Add(this.cbxElapse);
            this.panel2.Location = new System.Drawing.Point(3, 26);
            this.panel2.Name = "panel2";
            this.panel2.Size = new System.Drawing.Size(1277, 288);
            this.panel2.TabIndex = 35;
            // 
            // groupBox6
            // 
            this.groupBox6.Controls.Add(this.lblQueryTime);
            this.groupBox6.Controls.Add(this.cbxQueryFunc);
            this.groupBox6.Controls.Add(this.lbprogress);
            this.groupBox6.Controls.Add(this.btnCancelRun);
            this.groupBox6.Controls.Add(this.lbprogressText);
            this.groupBox6.Controls.Add(this.progBar);
            this.groupBox6.Controls.Add(this.lbStatus);
            this.groupBox6.Controls.Add(this.btnDoRun);
            this.groupBox6.Location = new System.Drawing.Point(913, 156);
            this.groupBox6.Name = "groupBox6";
            this.groupBox6.Size = new System.Drawing.Size(258, 108);
            this.groupBox6.TabIndex = 42;
            this.groupBox6.TabStop = false;
            // 
            // lblQueryTime
            // 
            this.lblQueryTime.AutoSize = true;
            this.lblQueryTime.Location = new System.Drawing.Point(45, 58);
            this.lblQueryTime.Name = "lblQueryTime";
            this.lblQueryTime.Size = new System.Drawing.Size(53, 12);
            this.lblQueryTime.TabIndex = 45;
            this.lblQueryTime.Text = "查询用时";
            this.lblQueryTime.Visible = false;
            // 
            // cbxQueryFunc
            // 
            this.cbxQueryFunc.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cbxQueryFunc.DropDownWidth = 54;
            this.cbxQueryFunc.FormattingEnabled = true;
            this.cbxQueryFunc.Items.AddRange(new object[] {
            "新版",
            "旧版",
            "质检文件"});
            this.cbxQueryFunc.Location = new System.Drawing.Point(162, 25);
            this.cbxQueryFunc.Name = "cbxQueryFunc";
            this.cbxQueryFunc.Size = new System.Drawing.Size(82, 22);
            this.cbxQueryFunc.TabIndex = 44;
            // 
            // lbprogress
            // 
            this.lbprogress.AutoSize = true;
            this.lbprogress.Location = new System.Drawing.Point(45, 85);
            this.lbprogress.Name = "lbprogress";
            this.lbprogress.Size = new System.Drawing.Size(17, 12);
            this.lbprogress.TabIndex = 42;
            this.lbprogress.Text = "  ";
            // 
            // btnCancelRun
            // 
            this.btnCancelRun.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Center;
            this.btnCancelRun.FlatStyle = System.Windows.Forms.FlatStyle.Popup;
            this.btnCancelRun.Image = global::MasterCom.RAMS.Properties.Resources.cancel;
            this.btnCancelRun.Location = new System.Drawing.Point(229, 80);
            this.btnCancelRun.Name = "btnCancelRun";
            this.btnCancelRun.Size = new System.Drawing.Size(20, 20);
            this.btnCancelRun.TabIndex = 28;
            this.toolTip.SetToolTip(this.btnCancelRun, "点击终止查询");
            this.btnCancelRun.UseVisualStyleBackColor = true;
            this.btnCancelRun.Visible = false;
            this.btnCancelRun.Click += new System.EventHandler(this.btnCancelRun_Click_1);
            // 
            // lbprogressText
            // 
            this.lbprogressText.AutoSize = true;
            this.lbprogressText.Location = new System.Drawing.Point(4, 86);
            this.lbprogressText.Name = "lbprogressText";
            this.lbprogressText.Size = new System.Drawing.Size(35, 12);
            this.lbprogressText.TabIndex = 43;
            this.lbprogressText.Text = "进度:";
            this.lbprogressText.Visible = false;
            // 
            // progBar
            // 
            this.progBar.Location = new System.Drawing.Point(47, 79);
            this.progBar.Name = "progBar";
            this.progBar.Size = new System.Drawing.Size(176, 22);
            this.progBar.TabIndex = 27;
            this.progBar.Visible = false;
            // 
            // lbStatus
            // 
            this.lbStatus.AutoSize = true;
            this.lbStatus.Location = new System.Drawing.Point(6, 65);
            this.lbStatus.Name = "lbStatus";
            this.lbStatus.Size = new System.Drawing.Size(23, 12);
            this.lbStatus.TabIndex = 24;
            this.lbStatus.Text = "   ";
            // 
            // btnDoRun
            // 
            this.btnDoRun.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnDoRun.Location = new System.Drawing.Point(19, 17);
            this.btnDoRun.Name = "btnDoRun";
            this.btnDoRun.Size = new System.Drawing.Size(135, 38);
            this.btnDoRun.TabIndex = 25;
            this.btnDoRun.Text = "开始查询";
            this.btnDoRun.UseVisualStyleBackColor = true;
            this.btnDoRun.Click += new System.EventHandler(this.btnDoRun_Click);
            // 
            // groupBoxFileName
            // 
            this.groupBoxFileName.Controls.Add(this.ccbePort);
            this.groupBoxFileName.Controls.Add(this.cbxfileNumType);
            this.groupBoxFileName.Controls.Add(this.btnClearPort);
            this.groupBoxFileName.Controls.Add(this.label_fileName);
            this.groupBoxFileName.Controls.Add(this.textBoxFileName);
            this.groupBoxFileName.Controls.Add(this.checkBoxFileName);
            this.groupBoxFileName.Controls.Add(this.label10);
            this.groupBoxFileName.Location = new System.Drawing.Point(624, 128);
            this.groupBoxFileName.Name = "groupBoxFileName";
            this.groupBoxFileName.Size = new System.Drawing.Size(280, 79);
            this.groupBoxFileName.TabIndex = 33;
            this.groupBoxFileName.TabStop = false;
            this.groupBoxFileName.Text = "  ";
            // 
            // ccbePort
            // 
            this.ccbePort.EditValue = "";
            this.ccbePort.Enabled = false;
            this.ccbePort.Location = new System.Drawing.Point(58, 23);
            this.ccbePort.Name = "ccbePort";
            this.ccbePort.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.ccbePort.Properties.Items.AddRange(new DevExpress.XtraEditors.Controls.CheckedListBoxItem[] {
            new DevExpress.XtraEditors.Controls.CheckedListBoxItem("1", "1"),
            new DevExpress.XtraEditors.Controls.CheckedListBoxItem("2", "2"),
            new DevExpress.XtraEditors.Controls.CheckedListBoxItem("3", "3"),
            new DevExpress.XtraEditors.Controls.CheckedListBoxItem("4", "4"),
            new DevExpress.XtraEditors.Controls.CheckedListBoxItem("5", "5"),
            new DevExpress.XtraEditors.Controls.CheckedListBoxItem("6", "6"),
            new DevExpress.XtraEditors.Controls.CheckedListBoxItem("7", "7"),
            new DevExpress.XtraEditors.Controls.CheckedListBoxItem("8", "8"),
            new DevExpress.XtraEditors.Controls.CheckedListBoxItem("9", "9")});
            this.ccbePort.Size = new System.Drawing.Size(106, 21);
            this.ccbePort.TabIndex = 46;
            this.ccbePort.EditValueChanged += new System.EventHandler(this.ccbePort_EditValueChanged);
            // 
            // cbxfileNumType
            // 
            this.cbxfileNumType.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cbxfileNumType.FormattingEnabled = true;
            this.cbxfileNumType.Items.AddRange(new object[] {
            "RCU",
            "ATU"});
            this.cbxfileNumType.Location = new System.Drawing.Point(170, 22);
            this.cbxfileNumType.Name = "cbxfileNumType";
            this.cbxfileNumType.Size = new System.Drawing.Size(53, 22);
            this.cbxfileNumType.TabIndex = 45;
            // 
            // btnClearPort
            // 
            this.btnClearPort.Enabled = false;
            this.btnClearPort.FlatStyle = System.Windows.Forms.FlatStyle.System;
            this.btnClearPort.Location = new System.Drawing.Point(229, 21);
            this.btnClearPort.Name = "btnClearPort";
            this.btnClearPort.Size = new System.Drawing.Size(45, 23);
            this.btnClearPort.TabIndex = 44;
            this.btnClearPort.Text = "清空";
            this.toolTip.SetToolTip(this.btnClearPort, "清空选择的端口");
            this.btnClearPort.UseVisualStyleBackColor = true;
            this.btnClearPort.Click += new System.EventHandler(this.btnClearPort_Click);
            // 
            // label_fileName
            // 
            this.label_fileName.AutoSize = true;
            this.label_fileName.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(34)))), ((int)(((byte)(57)))), ((int)(((byte)(94)))));
            this.label_fileName.Location = new System.Drawing.Point(7, 0);
            this.label_fileName.Name = "label_fileName";
            this.label_fileName.Size = new System.Drawing.Size(41, 12);
            this.label_fileName.TabIndex = 2;
            this.label_fileName.Text = "文件名";
            // 
            // textBoxFileName
            // 
            this.textBoxFileName.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.textBoxFileName.Enabled = false;
            this.textBoxFileName.Location = new System.Drawing.Point(8, 53);
            this.textBoxFileName.Name = "textBoxFileName";
            this.textBoxFileName.Size = new System.Drawing.Size(266, 21);
            this.textBoxFileName.TabIndex = 0;
            // 
            // checkBoxFileName
            // 
            this.checkBoxFileName.AutoSize = true;
            this.checkBoxFileName.Location = new System.Drawing.Point(55, 0);
            this.checkBoxFileName.Name = "checkBoxFileName";
            this.checkBoxFileName.Padding = new System.Windows.Forms.Padding(2, 0, 5, 0);
            this.checkBoxFileName.Size = new System.Drawing.Size(22, 14);
            this.checkBoxFileName.TabIndex = 1;
            this.checkBoxFileName.UseVisualStyleBackColor = true;
            this.checkBoxFileName.CheckedChanged += new System.EventHandler(this.checkBoxFileName_CheckedChanged);
            // 
            // label10
            // 
            this.label10.AutoSize = true;
            this.label10.Location = new System.Drawing.Point(9, 28);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(53, 12);
            this.label10.TabIndex = 47;
            this.label10.Text = "端口号：";
            // 
            // groupBoxPath
            // 
            this.groupBoxPath.Controls.Add(this.btnSelectPath);
            this.groupBoxPath.Controls.Add(this.labelPath);
            this.groupBoxPath.Location = new System.Drawing.Point(624, 218);
            this.groupBoxPath.Name = "groupBoxPath";
            this.groupBoxPath.Size = new System.Drawing.Size(280, 46);
            this.groupBoxPath.TabIndex = 40;
            this.groupBoxPath.TabStop = false;
            this.groupBoxPath.Text = "保存路径";
            // 
            // btnSelectPath
            // 
            this.btnSelectPath.Location = new System.Drawing.Point(229, 14);
            this.btnSelectPath.Name = "btnSelectPath";
            this.btnSelectPath.Size = new System.Drawing.Size(45, 23);
            this.btnSelectPath.TabIndex = 1;
            this.btnSelectPath.Text = "...";
            this.btnSelectPath.UseVisualStyleBackColor = true;
            this.btnSelectPath.Click += new System.EventHandler(this.btnSelectPath_Click);
            // 
            // labelPath
            // 
            this.labelPath.AutoSize = true;
            this.labelPath.Location = new System.Drawing.Point(7, 21);
            this.labelPath.Name = "labelPath";
            this.labelPath.Size = new System.Drawing.Size(77, 12);
            this.labelPath.TabIndex = 0;
            this.labelPath.Text = "C:\\MasterCom";
            // 
            // btnQuickSet
            // 
            this.btnQuickSet.Location = new System.Drawing.Point(1075, 7);
            this.btnQuickSet.Name = "btnQuickSet";
            this.btnQuickSet.Size = new System.Drawing.Size(96, 27);
            this.btnQuickSet.TabIndex = 36;
            this.btnQuickSet.Text = "快捷设置";
            this.toolTip.SetToolTip(this.btnQuickSet, "执行预定义的常用查询条件设置");
            this.btnQuickSet.UseVisualStyleBackColor = true;
            this.btnQuickSet.Click += new System.EventHandler(this.btnQuickSet_Click);
            // 
            // groupBox5
            // 
            this.groupBox5.Controls.Add(this.txtFilterReport);
            this.groupBox5.Controls.Add(this.treeListParam);
            this.groupBox5.Controls.Add(this.btnUpdateConditionTime);
            this.groupBox5.Controls.Add(this.btnOneKeyQuery);
            this.groupBox5.Controls.Add(this.btnMultiOK);
            this.groupBox5.Controls.Add(this.label24);
            this.groupBox5.Location = new System.Drawing.Point(1181, 3);
            this.groupBox5.Name = "groupBox5";
            this.groupBox5.Size = new System.Drawing.Size(173, 261);
            this.groupBox5.TabIndex = 39;
            this.groupBox5.TabStop = false;
            this.groupBox5.Text = "批处理";
            // 
            // txtFilterReport
            // 
            this.txtFilterReport.Location = new System.Drawing.Point(42, 202);
            this.txtFilterReport.Name = "txtFilterReport";
            this.txtFilterReport.Size = new System.Drawing.Size(128, 21);
            this.txtFilterReport.TabIndex = 45;
            this.txtFilterReport.TextChanged += new System.EventHandler(this.txtFilterReport_TextChanged);
            // 
            // treeListParam
            // 
            this.treeListParam.Appearance.Empty.BackColor = System.Drawing.Color.Ivory;
            this.treeListParam.Appearance.Empty.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(255)))));
            this.treeListParam.Appearance.Empty.Options.UseBackColor = true;
            this.treeListParam.Appearance.Empty.Options.UseForeColor = true;
            this.treeListParam.Appearance.EvenRow.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(253)))), ((int)(((byte)(245)))), ((int)(((byte)(230)))));
            this.treeListParam.Appearance.EvenRow.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(0)))));
            this.treeListParam.Appearance.EvenRow.Options.UseBackColor = true;
            this.treeListParam.Appearance.EvenRow.Options.UseForeColor = true;
            this.treeListParam.Appearance.FooterPanel.BackColor = System.Drawing.Color.NavajoWhite;
            this.treeListParam.Appearance.FooterPanel.BorderColor = System.Drawing.Color.NavajoWhite;
            this.treeListParam.Appearance.FooterPanel.Font = new System.Drawing.Font("Tahoma", 8.25F, System.Drawing.FontStyle.Bold);
            this.treeListParam.Appearance.FooterPanel.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(0)))));
            this.treeListParam.Appearance.FooterPanel.Options.UseBackColor = true;
            this.treeListParam.Appearance.FooterPanel.Options.UseBorderColor = true;
            this.treeListParam.Appearance.FooterPanel.Options.UseFont = true;
            this.treeListParam.Appearance.FooterPanel.Options.UseForeColor = true;
            this.treeListParam.Appearance.GroupButton.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(224)))), ((int)(((byte)(192)))));
            this.treeListParam.Appearance.GroupButton.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(224)))), ((int)(((byte)(192)))));
            this.treeListParam.Appearance.GroupButton.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(0)))));
            this.treeListParam.Appearance.GroupButton.Options.UseBackColor = true;
            this.treeListParam.Appearance.GroupButton.Options.UseBorderColor = true;
            this.treeListParam.Appearance.GroupButton.Options.UseForeColor = true;
            this.treeListParam.Appearance.GroupFooter.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(232)))), ((int)(((byte)(194)))), ((int)(((byte)(145)))));
            this.treeListParam.Appearance.GroupFooter.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(232)))), ((int)(((byte)(194)))), ((int)(((byte)(145)))));
            this.treeListParam.Appearance.GroupFooter.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(0)))));
            this.treeListParam.Appearance.GroupFooter.Options.UseBackColor = true;
            this.treeListParam.Appearance.GroupFooter.Options.UseBorderColor = true;
            this.treeListParam.Appearance.GroupFooter.Options.UseForeColor = true;
            this.treeListParam.Appearance.HeaderPanel.BackColor = System.Drawing.Color.BurlyWood;
            this.treeListParam.Appearance.HeaderPanel.BorderColor = System.Drawing.Color.BurlyWood;
            this.treeListParam.Appearance.HeaderPanel.Font = new System.Drawing.Font("Tahoma", 8.25F, System.Drawing.FontStyle.Bold);
            this.treeListParam.Appearance.HeaderPanel.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(0)))));
            this.treeListParam.Appearance.HeaderPanel.Options.UseBackColor = true;
            this.treeListParam.Appearance.HeaderPanel.Options.UseBorderColor = true;
            this.treeListParam.Appearance.HeaderPanel.Options.UseFont = true;
            this.treeListParam.Appearance.HeaderPanel.Options.UseForeColor = true;
            this.treeListParam.Appearance.HideSelectionRow.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(128)))), ((int)(((byte)(128)))));
            this.treeListParam.Appearance.HideSelectionRow.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(212)))), ((int)(((byte)(208)))), ((int)(((byte)(200)))));
            this.treeListParam.Appearance.HideSelectionRow.Options.UseBackColor = true;
            this.treeListParam.Appearance.HideSelectionRow.Options.UseForeColor = true;
            this.treeListParam.Appearance.HorzLine.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(224)))), ((int)(((byte)(224)))), ((int)(((byte)(224)))));
            this.treeListParam.Appearance.HorzLine.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(224)))), ((int)(((byte)(224)))), ((int)(((byte)(224)))));
            this.treeListParam.Appearance.HorzLine.Options.UseBackColor = true;
            this.treeListParam.Appearance.HorzLine.Options.UseForeColor = true;
            this.treeListParam.Appearance.OddRow.BackColor = System.Drawing.Color.Bisque;
            this.treeListParam.Appearance.OddRow.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(0)))));
            this.treeListParam.Appearance.OddRow.Options.UseBackColor = true;
            this.treeListParam.Appearance.OddRow.Options.UseForeColor = true;
            this.treeListParam.Appearance.Preview.BackColor = System.Drawing.Color.Cornsilk;
            this.treeListParam.Appearance.Preview.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(255)))));
            this.treeListParam.Appearance.Preview.Options.UseBackColor = true;
            this.treeListParam.Appearance.Preview.Options.UseForeColor = true;
            this.treeListParam.Appearance.Preview.Options.UseTextOptions = true;
            this.treeListParam.Appearance.Preview.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Top;
            this.treeListParam.Appearance.Row.BackColor = System.Drawing.Color.Ivory;
            this.treeListParam.Appearance.Row.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(25)))), ((int)(((byte)(25)))), ((int)(((byte)(112)))));
            this.treeListParam.Appearance.Row.Options.UseBackColor = true;
            this.treeListParam.Appearance.Row.Options.UseForeColor = true;
            this.treeListParam.Appearance.SelectedRow.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(10)))), ((int)(((byte)(10)))), ((int)(((byte)(138)))));
            this.treeListParam.Appearance.SelectedRow.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(255)))));
            this.treeListParam.Appearance.SelectedRow.Options.UseBackColor = true;
            this.treeListParam.Appearance.SelectedRow.Options.UseForeColor = true;
            this.treeListParam.Appearance.TreeLine.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(128)))), ((int)(((byte)(128)))));
            this.treeListParam.Appearance.TreeLine.Options.UseForeColor = true;
            this.treeListParam.Appearance.VertLine.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(224)))), ((int)(((byte)(224)))), ((int)(((byte)(224)))));
            this.treeListParam.Appearance.VertLine.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(224)))), ((int)(((byte)(224)))), ((int)(((byte)(224)))));
            this.treeListParam.Appearance.VertLine.Options.UseBackColor = true;
            this.treeListParam.Appearance.VertLine.Options.UseForeColor = true;
            this.treeListParam.Appearance.VertLine.Options.UseTextOptions = true;
            this.treeListParam.Appearance.VertLine.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.treeListParam.Columns.AddRange(new DevExpress.XtraTreeList.Columns.TreeListColumn[] {
            this.colKey});
            this.treeListParam.ContextMenuStrip = this.cMReport;
            this.treeListParam.Location = new System.Drawing.Point(3, 17);
            this.treeListParam.Name = "treeListParam";
            this.treeListParam.OptionsBehavior.AllowIndeterminateCheckState = true;
            this.treeListParam.OptionsBehavior.AutoChangeParent = false;
            this.treeListParam.OptionsBehavior.AutoNodeHeight = false;
            this.treeListParam.OptionsBehavior.AutoSelectAllInEditor = false;
            this.treeListParam.OptionsBehavior.CloseEditorOnLostFocus = false;
            this.treeListParam.OptionsBehavior.DragNodes = true;
            this.treeListParam.OptionsBehavior.Editable = false;
            this.treeListParam.OptionsBehavior.KeepSelectedOnClick = false;
            this.treeListParam.OptionsBehavior.ResizeNodes = false;
            this.treeListParam.OptionsBehavior.SmartMouseHover = false;
            this.treeListParam.OptionsMenu.EnableFooterMenu = false;
            this.treeListParam.OptionsPrint.PrintHorzLines = false;
            this.treeListParam.OptionsPrint.PrintVertLines = false;
            this.treeListParam.OptionsPrint.UsePrintStyles = true;
            this.treeListParam.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.treeListParam.OptionsView.ShowCheckBoxes = true;
            this.treeListParam.OptionsView.ShowColumns = false;
            this.treeListParam.OptionsView.ShowFocusedFrame = false;
            this.treeListParam.OptionsView.ShowHorzLines = false;
            this.treeListParam.OptionsView.ShowIndicator = false;
            this.treeListParam.OptionsView.ShowVertLines = false;
            this.treeListParam.Size = new System.Drawing.Size(167, 182);
            this.treeListParam.TabIndex = 44;
            this.treeListParam.BeforeDragNode += new DevExpress.XtraTreeList.BeforeDragNodeEventHandler(this.treeListParam_BeforeDragNode);
            this.treeListParam.AfterDragNode += new DevExpress.XtraTreeList.NodeEventHandler(this.treeListParam_AfterDragNode);
            this.treeListParam.AfterCheckNode += new DevExpress.XtraTreeList.NodeEventHandler(this.treeListParam_AfterCheckNode);
            this.treeListParam.FocusedNodeChanged += new DevExpress.XtraTreeList.FocusedNodeChangedEventHandler(this.treeListParam_FocusedNodeChanged);
            this.treeListParam.DragDrop += new System.Windows.Forms.DragEventHandler(this.treeListParam_DragDrop);
            this.treeListParam.DragOver += new System.Windows.Forms.DragEventHandler(this.treeListParam_DragOver);
            this.treeListParam.MouseDown += new System.Windows.Forms.MouseEventHandler(this.treeListParam_MouseDown);
            // 
            // colKey
            // 
            this.colKey.AllNodesSummary = true;
            this.colKey.Caption = "Registry Keys";
            this.colKey.FieldName = "Key";
            this.colKey.MinWidth = 27;
            this.colKey.Name = "colKey";
            this.colKey.SummaryFooter = DevExpress.XtraTreeList.SummaryItemType.Count;
            this.colKey.SummaryFooterStrFormat = "Count keys = {0}";
            this.colKey.Visible = true;
            this.colKey.VisibleIndex = 0;
            // 
            // cMReport
            // 
            this.cMReport.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.addTeam,
            this.addSaveCurReport,
            this.saveChange,
            this.deteleSunReport});
            this.cMReport.Name = "cMReport";
            this.cMReport.Size = new System.Drawing.Size(205, 92);
            // 
            // addTeam
            // 
            this.addTeam.Name = "addTeam";
            this.addTeam.Size = new System.Drawing.Size(204, 22);
            this.addTeam.Text = "添加分组(建临时子节点)";
            this.addTeam.Click += new System.EventHandler(this.addTeam_Click);
            // 
            // addSaveCurReport
            // 
            this.addSaveCurReport.Name = "addSaveCurReport";
            this.addSaveCurReport.Size = new System.Drawing.Size(204, 22);
            this.addSaveCurReport.Text = "新增保存当前条件";
            this.addSaveCurReport.Click += new System.EventHandler(this.addSaveCurReport_Click);
            // 
            // saveChange
            // 
            this.saveChange.Name = "saveChange";
            this.saveChange.Size = new System.Drawing.Size(204, 22);
            this.saveChange.Text = "更新当前条件";
            this.saveChange.Click += new System.EventHandler(this.saveChange_Click);
            // 
            // deteleSunReport
            // 
            this.deteleSunReport.Name = "deteleSunReport";
            this.deteleSunReport.Size = new System.Drawing.Size(204, 22);
            this.deteleSunReport.Text = "删除报表";
            this.deteleSunReport.Click += new System.EventHandler(this.deteleSunReport_Click);
            // 
            // btnUpdateConditionTime
            // 
            this.btnUpdateConditionTime.Location = new System.Drawing.Point(67, 230);
            this.btnUpdateConditionTime.Name = "btnUpdateConditionTime";
            this.btnUpdateConditionTime.Size = new System.Drawing.Size(61, 25);
            this.btnUpdateConditionTime.TabIndex = 43;
            this.btnUpdateConditionTime.Text = "更新时间";
            this.btnUpdateConditionTime.UseVisualStyleBackColor = true;
            this.btnUpdateConditionTime.Click += new System.EventHandler(this.btnUpdateConditionTime_Click);
            // 
            // btnOneKeyQuery
            // 
            this.btnOneKeyQuery.Location = new System.Drawing.Point(3, 230);
            this.btnOneKeyQuery.Name = "btnOneKeyQuery";
            this.btnOneKeyQuery.Size = new System.Drawing.Size(61, 25);
            this.btnOneKeyQuery.TabIndex = 41;
            this.btnOneKeyQuery.Text = "一键查询";
            this.btnOneKeyQuery.UseVisualStyleBackColor = true;
            this.btnOneKeyQuery.Click += new System.EventHandler(this.btnOneKeyQuery_Click);
            // 
            // btnMultiOK
            // 
            this.btnMultiOK.Location = new System.Drawing.Point(132, 230);
            this.btnMultiOK.Name = "btnMultiOK";
            this.btnMultiOK.Size = new System.Drawing.Size(38, 25);
            this.btnMultiOK.TabIndex = 40;
            this.btnMultiOK.Text = "确定";
            this.toolTip.SetToolTip(this.btnMultiOK, "按条件批量生成报表");
            this.btnMultiOK.UseVisualStyleBackColor = true;
            this.btnMultiOK.Click += new System.EventHandler(this.btnMultiOK_Click);
            // 
            // label24
            // 
            this.label24.AutoSize = true;
            this.label24.Location = new System.Drawing.Point(6, 207);
            this.label24.Name = "label24";
            this.label24.Size = new System.Drawing.Size(41, 12);
            this.label24.TabIndex = 46;
            this.label24.Text = "过滤：";
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.label9);
            this.groupBox2.Controls.Add(this.checkAllCity);
            this.groupBox2.Controls.Add(this.listViewCity);
            this.groupBox2.Location = new System.Drawing.Point(433, 128);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(185, 136);
            this.groupBox2.TabIndex = 35;
            this.groupBox2.TabStop = false;
            // 
            // label9
            // 
            this.label9.AutoSize = true;
            this.label9.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(34)))), ((int)(((byte)(57)))), ((int)(((byte)(94)))));
            this.label9.Location = new System.Drawing.Point(7, 0);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(53, 12);
            this.label9.TabIndex = 5;
            this.label9.Text = "地市公司";
            // 
            // checkAllCity
            // 
            this.checkAllCity.AutoSize = true;
            this.checkAllCity.Checked = true;
            this.checkAllCity.CheckState = System.Windows.Forms.CheckState.Checked;
            this.checkAllCity.Location = new System.Drawing.Point(130, -1);
            this.checkAllCity.Name = "checkAllCity";
            this.checkAllCity.Padding = new System.Windows.Forms.Padding(2, 0, 0, 0);
            this.checkAllCity.Size = new System.Drawing.Size(50, 16);
            this.checkAllCity.TabIndex = 1;
            this.checkAllCity.Text = "全选";
            this.checkAllCity.UseVisualStyleBackColor = true;
            this.checkAllCity.CheckedChanged += new System.EventHandler(this.checkAllCity_CheckedChanged);
            // 
            // listViewCity
            // 
            this.listViewCity.CheckBoxes = true;
            this.listViewCity.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            columnHeader4});
            this.listViewCity.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listViewCity.Enabled = false;
            this.listViewCity.HeaderStyle = System.Windows.Forms.ColumnHeaderStyle.None;
            this.listViewCity.Location = new System.Drawing.Point(3, 17);
            this.listViewCity.Name = "listViewCity";
            this.listViewCity.Size = new System.Drawing.Size(179, 116);
            this.listViewCity.TabIndex = 0;
            this.listViewCity.UseCompatibleStateImageBehavior = false;
            this.listViewCity.View = System.Windows.Forms.View.Details;
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.btnImportFilter);
            this.groupBox1.Controls.Add(this.btnGainTemplate);
            this.groupBox1.Controls.Add(this.btnResetArea);
            this.groupBox1.Controls.Add(this.btnImport);
            this.groupBox1.Controls.Add(this.lblFilter);
            this.groupBox1.Controls.Add(this.txbFilter);
            this.groupBox1.Controls.Add(this.cbxAllArea);
            this.groupBox1.Controls.Add(this.chListArea);
            this.groupBox1.Controls.Add(this.lbSelAreaType);
            this.groupBox1.Controls.Add(this.label1);
            this.groupBox1.Location = new System.Drawing.Point(3, 3);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(227, 261);
            this.groupBox1.TabIndex = 3;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "区域类别";
            // 
            // btnImportFilter
            // 
            this.btnImportFilter.FlatStyle = System.Windows.Forms.FlatStyle.Popup;
            this.btnImportFilter.Location = new System.Drawing.Point(12, 45);
            this.btnImportFilter.Name = "btnImportFilter";
            this.btnImportFilter.Size = new System.Drawing.Size(69, 27);
            this.btnImportFilter.TabIndex = 13;
            this.btnImportFilter.Text = "勾选地点";
            this.toolTip.SetToolTip(this.btnImportFilter, "通过导入Excel，勾选包含的地点");
            this.btnImportFilter.UseVisualStyleBackColor = true;
            this.btnImportFilter.Click += new System.EventHandler(this.btnImportFilter_Click);
            // 
            // btnGainTemplate
            // 
            this.btnGainTemplate.FlatStyle = System.Windows.Forms.FlatStyle.Popup;
            this.btnGainTemplate.Location = new System.Drawing.Point(87, 45);
            this.btnGainTemplate.Name = "btnGainTemplate";
            this.btnGainTemplate.Size = new System.Drawing.Size(63, 27);
            this.btnGainTemplate.TabIndex = 12;
            this.btnGainTemplate.Text = "获取模板";
            this.btnGainTemplate.UseVisualStyleBackColor = true;
            this.btnGainTemplate.Visible = false;
            this.btnGainTemplate.Click += new System.EventHandler(this.btnGainTemplate_Click);
            // 
            // btnResetArea
            // 
            this.btnResetArea.FlatStyle = System.Windows.Forms.FlatStyle.Popup;
            this.btnResetArea.Location = new System.Drawing.Point(154, 45);
            this.btnResetArea.Name = "btnResetArea";
            this.btnResetArea.Size = new System.Drawing.Size(63, 27);
            this.btnResetArea.TabIndex = 11;
            this.btnResetArea.Text = "重置设备";
            this.toolTip.SetToolTip(this.btnResetArea, "恢复系统提供的区域选项");
            this.btnResetArea.UseVisualStyleBackColor = true;
            this.btnResetArea.Visible = false;
            this.btnResetArea.Click += new System.EventHandler(this.btnResetArea_Click);
            // 
            // btnImport
            // 
            this.btnImport.FlatStyle = System.Windows.Forms.FlatStyle.Popup;
            this.btnImport.Location = new System.Drawing.Point(8, 45);
            this.btnImport.Name = "btnImport";
            this.btnImport.Size = new System.Drawing.Size(76, 27);
            this.btnImport.TabIndex = 10;
            this.btnImport.Text = "批量导入";
            this.toolTip.SetToolTip(this.btnImport, "通过导入Excel，添加设备和时间");
            this.btnImport.UseVisualStyleBackColor = true;
            this.btnImport.Visible = false;
            this.btnImport.Click += new System.EventHandler(this.btnImport_Click);
            // 
            // lblFilter
            // 
            this.lblFilter.AutoSize = true;
            this.lblFilter.Location = new System.Drawing.Point(91, 88);
            this.lblFilter.Name = "lblFilter";
            this.lblFilter.Size = new System.Drawing.Size(29, 12);
            this.lblFilter.TabIndex = 9;
            this.lblFilter.Text = "过滤";
            // 
            // txbFilter
            // 
            this.txbFilter.Location = new System.Drawing.Point(128, 84);
            this.txbFilter.Name = "txbFilter";
            this.txbFilter.Size = new System.Drawing.Size(89, 21);
            this.txbFilter.TabIndex = 8;
            this.txbFilter.TextChanged += new System.EventHandler(this.txbFilter_TextChanged);
            // 
            // cbxAllArea
            // 
            this.cbxAllArea.AutoSize = true;
            this.cbxAllArea.Location = new System.Drawing.Point(12, 85);
            this.cbxAllArea.Name = "cbxAllArea";
            this.cbxAllArea.Size = new System.Drawing.Size(48, 16);
            this.cbxAllArea.TabIndex = 7;
            this.cbxAllArea.Text = "全选";
            this.cbxAllArea.UseVisualStyleBackColor = true;
            this.cbxAllArea.CheckedChanged += new System.EventHandler(this.cbxAllArea_CheckedChanged);
            // 
            // chListArea
            // 
            this.chListArea.CheckOnClick = true;
            this.chListArea.ColumnWidth = 150;
            this.chListArea.FormattingEnabled = true;
            this.chListArea.HorizontalScrollbar = true;
            this.chListArea.Location = new System.Drawing.Point(8, 110);
            this.chListArea.Name = "chListArea";
            this.chListArea.Size = new System.Drawing.Size(208, 148);
            this.chListArea.Sorted = true;
            this.chListArea.TabIndex = 4;
            // 
            // lbSelAreaType
            // 
            this.lbSelAreaType.AutoSize = true;
            this.lbSelAreaType.Location = new System.Drawing.Point(114, 27);
            this.lbSelAreaType.Name = "lbSelAreaType";
            this.lbSelAreaType.Size = new System.Drawing.Size(65, 12);
            this.lbSelAreaType.TabIndex = 1;
            this.lbSelAreaType.Text = "(区域类别)";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(7, 27);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(77, 12);
            this.label1.TabIndex = 1;
            this.label1.Text = "已指定方式：";
            // 
            // groupBoxCarrier
            // 
            this.groupBoxCarrier.Controls.Add(this.label_carrier);
            this.groupBoxCarrier.Controls.Add(this.listViewCarrier);
            this.groupBoxCarrier.Location = new System.Drawing.Point(627, 2);
            this.groupBoxCarrier.Name = "groupBoxCarrier";
            this.groupBoxCarrier.Size = new System.Drawing.Size(280, 57);
            this.groupBoxCarrier.TabIndex = 34;
            this.groupBoxCarrier.TabStop = false;
            // 
            // label_carrier
            // 
            this.label_carrier.AutoSize = true;
            this.label_carrier.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(34)))), ((int)(((byte)(57)))), ((int)(((byte)(94)))));
            this.label_carrier.Location = new System.Drawing.Point(7, 0);
            this.label_carrier.Name = "label_carrier";
            this.label_carrier.Size = new System.Drawing.Size(41, 12);
            this.label_carrier.TabIndex = 1;
            this.label_carrier.Text = "运营商";
            // 
            // listViewCarrier
            // 
            this.listViewCarrier.CheckBoxes = true;
            this.listViewCarrier.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listViewCarrier.Location = new System.Drawing.Point(3, 17);
            this.listViewCarrier.Name = "listViewCarrier";
            this.listViewCarrier.Size = new System.Drawing.Size(274, 37);
            this.listViewCarrier.TabIndex = 0;
            this.listViewCarrier.UseCompatibleStateImageBehavior = false;
            this.listViewCarrier.View = System.Windows.Forms.View.List;
            // 
            // groupBox4
            // 
            this.groupBox4.Controls.Add(this.listViewService);
            this.groupBox4.Controls.Add(this.lbSvCount);
            this.groupBox4.Controls.Add(this.btnPopupService);
            this.groupBox4.Location = new System.Drawing.Point(236, 128);
            this.groupBox4.Name = "groupBox4";
            this.groupBox4.Size = new System.Drawing.Size(191, 136);
            this.groupBox4.TabIndex = 30;
            this.groupBox4.TabStop = false;
            this.groupBox4.Text = "业务类型";
            // 
            // listViewService
            // 
            this.listViewService.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            columnHeader2});
            this.listViewService.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listViewService.HeaderStyle = System.Windows.Forms.ColumnHeaderStyle.None;
            this.listViewService.Location = new System.Drawing.Point(3, 17);
            this.listViewService.Name = "listViewService";
            this.listViewService.Size = new System.Drawing.Size(185, 116);
            this.listViewService.TabIndex = 18;
            this.listViewService.UseCompatibleStateImageBehavior = false;
            this.listViewService.View = System.Windows.Forms.View.Details;
            // 
            // lbSvCount
            // 
            this.lbSvCount.AutoSize = true;
            this.lbSvCount.Font = new System.Drawing.Font("宋体", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.lbSvCount.ForeColor = System.Drawing.SystemColors.ActiveBorder;
            this.lbSvCount.Location = new System.Drawing.Point(71, 3);
            this.lbSvCount.Name = "lbSvCount";
            this.lbSvCount.Size = new System.Drawing.Size(23, 11);
            this.lbSvCount.TabIndex = 17;
            this.lbSvCount.Text = "[1]";
            // 
            // btnPopupService
            // 
            this.btnPopupService.FlatAppearance.BorderSize = 0;
            this.btnPopupService.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(128)))));
            this.btnPopupService.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnPopupService.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnPopupService.Location = new System.Drawing.Point(105, -5);
            this.btnPopupService.Name = "btnPopupService";
            this.btnPopupService.Size = new System.Drawing.Size(80, 22);
            this.btnPopupService.TabIndex = 4;
            this.btnPopupService.Text = "请选择↓";
            this.btnPopupService.UseVisualStyleBackColor = true;
            this.btnPopupService.Click += new System.EventHandler(this.btnPopupService_Click);
            // 
            // tbTimeValue
            // 
            this.tbTimeValue.Controls.Add(this.tabPagetime);
            this.tbTimeValue.Controls.Add(this.tabPageround);
            this.tbTimeValue.Controls.Add(this.tabPageElapse);
            this.tbTimeValue.Location = new System.Drawing.Point(913, 20);
            this.tbTimeValue.Name = "tbTimeValue";
            this.tbTimeValue.SelectedIndex = 0;
            this.tbTimeValue.Size = new System.Drawing.Size(262, 125);
            this.tbTimeValue.TabIndex = 32;
            // 
            // tabPagetime
            // 
            this.tabPagetime.Controls.Add(this.chbReject);
            this.tabPagetime.Controls.Add(this.checkBoxPeriodAdvance);
            this.tabPagetime.Controls.Add(this.panelCustomTime);
            this.tabPagetime.Controls.Add(this.panelAdvanceTime);
            this.tabPagetime.Location = new System.Drawing.Point(4, 23);
            this.tabPagetime.Name = "tabPagetime";
            this.tabPagetime.Padding = new System.Windows.Forms.Padding(3);
            this.tabPagetime.Size = new System.Drawing.Size(254, 98);
            this.tabPagetime.TabIndex = 0;
            this.tabPagetime.Text = "按时间";
            this.tabPagetime.UseVisualStyleBackColor = true;
            // 
            // chbReject
            // 
            this.chbReject.AutoSize = true;
            this.chbReject.Location = new System.Drawing.Point(78, 1);
            this.chbReject.Name = "chbReject";
            this.chbReject.Size = new System.Drawing.Size(96, 16);
            this.chbReject.TabIndex = 20;
            this.chbReject.Text = "剔除重复区域";
            this.chbReject.UseVisualStyleBackColor = true;
            this.chbReject.Visible = false;
            // 
            // checkBoxPeriodAdvance
            // 
            this.checkBoxPeriodAdvance.AutoSize = true;
            this.checkBoxPeriodAdvance.Location = new System.Drawing.Point(6, 1);
            this.checkBoxPeriodAdvance.Name = "checkBoxPeriodAdvance";
            this.checkBoxPeriodAdvance.Size = new System.Drawing.Size(48, 16);
            this.checkBoxPeriodAdvance.TabIndex = 19;
            this.checkBoxPeriodAdvance.Text = "高级";
            this.checkBoxPeriodAdvance.UseVisualStyleBackColor = true;
            this.checkBoxPeriodAdvance.CheckedChanged += new System.EventHandler(this.checkBoxAdvancePeriod_CheckedChanged);
            // 
            // panelCustomTime
            // 
            this.panelCustomTime.Controls.Add(this.dateTimeTo);
            this.panelCustomTime.Controls.Add(this.label3);
            this.panelCustomTime.Controls.Add(this.dateTimeFrom);
            this.panelCustomTime.Controls.Add(this.label2);
            this.panelCustomTime.Location = new System.Drawing.Point(6, 19);
            this.panelCustomTime.Name = "panelCustomTime";
            this.panelCustomTime.Size = new System.Drawing.Size(242, 63);
            this.panelCustomTime.TabIndex = 17;
            // 
            // dateTimeTo
            // 
            this.dateTimeTo.Location = new System.Drawing.Point(129, 24);
            this.dateTimeTo.Name = "dateTimeTo";
            this.dateTimeTo.Size = new System.Drawing.Size(110, 21);
            this.dateTimeTo.TabIndex = 13;
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(127, 10);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(17, 12);
            this.label3.TabIndex = 16;
            this.label3.Text = "至";
            // 
            // dateTimeFrom
            // 
            this.dateTimeFrom.Location = new System.Drawing.Point(9, 24);
            this.dateTimeFrom.Name = "dateTimeFrom";
            this.dateTimeFrom.Size = new System.Drawing.Size(111, 21);
            this.dateTimeFrom.TabIndex = 14;
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(7, 9);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(17, 12);
            this.label2.TabIndex = 15;
            this.label2.Text = "从";
            // 
            // panelAdvanceTime
            // 
            this.panelAdvanceTime.Controls.Add(this.buttonPeriodClear);
            this.panelAdvanceTime.Controls.Add(this.buttonPeriodAdd);
            this.panelAdvanceTime.Controls.Add(this.listBoxPeriod);
            this.panelAdvanceTime.Location = new System.Drawing.Point(15, 19);
            this.panelAdvanceTime.Name = "panelAdvanceTime";
            this.panelAdvanceTime.Size = new System.Drawing.Size(233, 63);
            this.panelAdvanceTime.TabIndex = 18;
            this.panelAdvanceTime.Visible = false;
            // 
            // buttonPeriodClear
            // 
            this.buttonPeriodClear.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.buttonPeriodClear.Location = new System.Drawing.Point(171, 34);
            this.buttonPeriodClear.Name = "buttonPeriodClear";
            this.buttonPeriodClear.Size = new System.Drawing.Size(59, 26);
            this.buttonPeriodClear.TabIndex = 2;
            this.buttonPeriodClear.Text = "清空";
            this.buttonPeriodClear.UseVisualStyleBackColor = true;
            this.buttonPeriodClear.Click += new System.EventHandler(this.buttonPeriodClear_Click);
            // 
            // buttonPeriodAdd
            // 
            this.buttonPeriodAdd.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.buttonPeriodAdd.Location = new System.Drawing.Point(171, 3);
            this.buttonPeriodAdd.Name = "buttonPeriodAdd";
            this.buttonPeriodAdd.Size = new System.Drawing.Size(59, 26);
            this.buttonPeriodAdd.TabIndex = 1;
            this.buttonPeriodAdd.Text = "增加";
            this.buttonPeriodAdd.UseVisualStyleBackColor = true;
            this.buttonPeriodAdd.Click += new System.EventHandler(this.buttonPeriodAdd_Click);
            // 
            // listBoxPeriod
            // 
            this.listBoxPeriod.FormattingEnabled = true;
            this.listBoxPeriod.HorizontalScrollbar = true;
            this.listBoxPeriod.ItemHeight = 14;
            this.listBoxPeriod.Location = new System.Drawing.Point(3, 1);
            this.listBoxPeriod.Name = "listBoxPeriod";
            this.listBoxPeriod.Size = new System.Drawing.Size(162, 60);
            this.listBoxPeriod.TabIndex = 0;
            // 
            // tabPageround
            // 
            this.tabPageround.Controls.Add(this.cbxProjToYear);
            this.tabPageround.Controls.Add(this.cbxProjFromYear);
            this.tabPageround.Controls.Add(this.cbxProjToRound);
            this.tabPageround.Controls.Add(this.cbxProjFromRound);
            this.tabPageround.Controls.Add(this.label5);
            this.tabPageround.Controls.Add(this.label8);
            this.tabPageround.Controls.Add(this.label7);
            this.tabPageround.Controls.Add(this.label6);
            this.tabPageround.Controls.Add(this.label4);
            this.tabPageround.Location = new System.Drawing.Point(4, 23);
            this.tabPageround.Name = "tabPageround";
            this.tabPageround.Padding = new System.Windows.Forms.Padding(3);
            this.tabPageround.Size = new System.Drawing.Size(254, 98);
            this.tabPageround.TabIndex = 1;
            this.tabPageround.Text = "按轮次";
            this.tabPageround.UseVisualStyleBackColor = true;
            // 
            // cbxProjToYear
            // 
            this.cbxProjToYear.FormattingEnabled = true;
            this.cbxProjToYear.Location = new System.Drawing.Point(46, 50);
            this.cbxProjToYear.Name = "cbxProjToYear";
            this.cbxProjToYear.Size = new System.Drawing.Size(68, 22);
            this.cbxProjToYear.TabIndex = 1;
            // 
            // cbxProjFromYear
            // 
            this.cbxProjFromYear.FormattingEnabled = true;
            this.cbxProjFromYear.Location = new System.Drawing.Point(46, 12);
            this.cbxProjFromYear.Name = "cbxProjFromYear";
            this.cbxProjFromYear.Size = new System.Drawing.Size(68, 22);
            this.cbxProjFromYear.TabIndex = 1;
            // 
            // cbxProjToRound
            // 
            this.cbxProjToRound.FormattingEnabled = true;
            this.cbxProjToRound.Location = new System.Drawing.Point(160, 51);
            this.cbxProjToRound.Name = "cbxProjToRound";
            this.cbxProjToRound.Size = new System.Drawing.Size(71, 22);
            this.cbxProjToRound.TabIndex = 1;
            // 
            // cbxProjFromRound
            // 
            this.cbxProjFromRound.FormattingEnabled = true;
            this.cbxProjFromRound.Location = new System.Drawing.Point(161, 13);
            this.cbxProjFromRound.Name = "cbxProjFromRound";
            this.cbxProjFromRound.Size = new System.Drawing.Size(71, 22);
            this.cbxProjFromRound.TabIndex = 1;
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(127, 16);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(41, 12);
            this.label5.TabIndex = 0;
            this.label5.Text = "轮次：";
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(127, 54);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(41, 12);
            this.label8.TabIndex = 0;
            this.label8.Text = "轮次：";
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(9, 55);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(41, 12);
            this.label7.TabIndex = 0;
            this.label7.Text = "年份：";
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(314, 16);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(17, 12);
            this.label6.TabIndex = 0;
            this.label6.Text = "到";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(9, 16);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(41, 12);
            this.label4.TabIndex = 0;
            this.label4.Text = "年份：";
            // 
            // tabPageElapse
            // 
            this.tabPageElapse.Controls.Add(this.dateTimeElapse);
            this.tabPageElapse.Controls.Add(this.numElapseTo);
            this.tabPageElapse.Controls.Add(this.label22);
            this.tabPageElapse.Controls.Add(this.label23);
            this.tabPageElapse.Controls.Add(this.label25);
            this.tabPageElapse.Location = new System.Drawing.Point(4, 23);
            this.tabPageElapse.Name = "tabPageElapse";
            this.tabPageElapse.Padding = new System.Windows.Forms.Padding(3);
            this.tabPageElapse.Size = new System.Drawing.Size(254, 98);
            this.tabPageElapse.TabIndex = 2;
            this.tabPageElapse.Text = "按推移";
            this.tabPageElapse.UseVisualStyleBackColor = true;
            // 
            // dateTimeElapse
            // 
            this.dateTimeElapse.Location = new System.Drawing.Point(34, 36);
            this.dateTimeElapse.Name = "dateTimeElapse";
            this.dateTimeElapse.Size = new System.Drawing.Size(112, 21);
            this.dateTimeElapse.TabIndex = 30;
            // 
            // numElapseTo
            // 
            this.numElapseTo.Location = new System.Drawing.Point(188, 38);
            this.numElapseTo.Maximum = new decimal(new int[] {
            300,
            0,
            0,
            0});
            this.numElapseTo.Name = "numElapseTo";
            this.numElapseTo.Size = new System.Drawing.Size(33, 21);
            this.numElapseTo.TabIndex = 27;
            // 
            // label22
            // 
            this.label22.AutoSize = true;
            this.label22.Location = new System.Drawing.Point(152, 42);
            this.label22.Name = "label22";
            this.label22.Size = new System.Drawing.Size(17, 12);
            this.label22.TabIndex = 22;
            this.label22.Text = "至";
            // 
            // label23
            // 
            this.label23.AutoSize = true;
            this.label23.Location = new System.Drawing.Point(11, 40);
            this.label23.Name = "label23";
            this.label23.Size = new System.Drawing.Size(17, 12);
            this.label23.TabIndex = 21;
            this.label23.Text = "从";
            // 
            // label25
            // 
            this.label25.AutoSize = true;
            this.label25.Location = new System.Drawing.Point(172, 42);
            this.label25.Name = "label25";
            this.label25.Size = new System.Drawing.Size(65, 12);
            this.label25.TabIndex = 29;
            this.label25.Text = "前      天";
            // 
            // groupBox7
            // 
            this.groupBox7.Controls.Add(this.cbxRptList);
            this.groupBox7.Controls.Add(this.label11);
            this.groupBox7.Controls.Add(this.cbxShowMerge);
            this.groupBox7.Location = new System.Drawing.Point(627, 65);
            this.groupBox7.Name = "groupBox7";
            this.groupBox7.Size = new System.Drawing.Size(280, 57);
            this.groupBox7.TabIndex = 26;
            this.groupBox7.TabStop = false;
            this.groupBox7.Text = "指标报表";
            // 
            // cbxRptList
            // 
            this.cbxRptList.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cbxRptList.DropDownWidth = 320;
            this.cbxRptList.FormattingEnabled = true;
            this.cbxRptList.Location = new System.Drawing.Point(35, 20);
            this.cbxRptList.Name = "cbxRptList";
            this.cbxRptList.Size = new System.Drawing.Size(242, 22);
            this.cbxRptList.TabIndex = 3;
            this.cbxRptList.SelectedIndexChanged += new System.EventHandler(this.cbxRptList_SelectedIndexChanged);
            // 
            // label11
            // 
            this.label11.AutoSize = true;
            this.label11.Location = new System.Drawing.Point(3, 25);
            this.label11.Name = "label11";
            this.label11.Size = new System.Drawing.Size(41, 12);
            this.label11.TabIndex = 4;
            this.label11.Text = "报表：";
            // 
            // cbxShowMerge
            // 
            this.cbxShowMerge.AutoSize = true;
            this.cbxShowMerge.Checked = true;
            this.cbxShowMerge.CheckState = System.Windows.Forms.CheckState.Checked;
            this.cbxShowMerge.Location = new System.Drawing.Point(201, 0);
            this.cbxShowMerge.Name = "cbxShowMerge";
            this.cbxShowMerge.Size = new System.Drawing.Size(72, 16);
            this.cbxShowMerge.TabIndex = 37;
            this.cbxShowMerge.Text = "汇总数据";
            this.cbxShowMerge.UseVisualStyleBackColor = true;
            // 
            // groupBoxDaiwei
            // 
            this.groupBoxDaiwei.Controls.Add(this.label_daiwei);
            this.groupBoxDaiwei.Controls.Add(this.chbAllAgent);
            this.groupBoxDaiwei.Controls.Add(this.listViewAgent);
            this.groupBoxDaiwei.Location = new System.Drawing.Point(433, 3);
            this.groupBoxDaiwei.Name = "groupBoxDaiwei";
            this.groupBoxDaiwei.Size = new System.Drawing.Size(188, 119);
            this.groupBoxDaiwei.TabIndex = 31;
            this.groupBoxDaiwei.TabStop = false;
            // 
            // label_daiwei
            // 
            this.label_daiwei.AutoSize = true;
            this.label_daiwei.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(34)))), ((int)(((byte)(57)))), ((int)(((byte)(94)))));
            this.label_daiwei.Location = new System.Drawing.Point(7, 0);
            this.label_daiwei.Name = "label_daiwei";
            this.label_daiwei.Size = new System.Drawing.Size(53, 12);
            this.label_daiwei.TabIndex = 5;
            this.label_daiwei.Text = "上传单位";
            // 
            // chbAllAgent
            // 
            this.chbAllAgent.AutoSize = true;
            this.chbAllAgent.Checked = true;
            this.chbAllAgent.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chbAllAgent.Location = new System.Drawing.Point(131, 0);
            this.chbAllAgent.Name = "chbAllAgent";
            this.chbAllAgent.Padding = new System.Windows.Forms.Padding(2, 0, 0, 0);
            this.chbAllAgent.Size = new System.Drawing.Size(50, 16);
            this.chbAllAgent.TabIndex = 1;
            this.chbAllAgent.Text = "全选";
            this.chbAllAgent.UseVisualStyleBackColor = true;
            this.chbAllAgent.CheckedChanged += new System.EventHandler(this.chbAllAgent_CheckedChanged);
            // 
            // listViewAgent
            // 
            this.listViewAgent.CheckBoxes = true;
            this.listViewAgent.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            columnHeader3});
            this.listViewAgent.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listViewAgent.Enabled = false;
            this.listViewAgent.HeaderStyle = System.Windows.Forms.ColumnHeaderStyle.None;
            this.listViewAgent.Location = new System.Drawing.Point(3, 17);
            this.listViewAgent.Name = "listViewAgent";
            this.listViewAgent.Size = new System.Drawing.Size(182, 99);
            this.listViewAgent.TabIndex = 0;
            this.listViewAgent.UseCompatibleStateImageBehavior = false;
            this.listViewAgent.View = System.Windows.Forms.View.Details;
            // 
            // groupBox3
            // 
            this.groupBox3.Controls.Add(this.cbxAllProject);
            this.groupBox3.Controls.Add(this.chkListProject);
            this.groupBox3.Location = new System.Drawing.Point(236, 3);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.Size = new System.Drawing.Size(191, 119);
            this.groupBox3.TabIndex = 29;
            this.groupBox3.TabStop = false;
            this.groupBox3.Text = "数据来源";
            // 
            // cbxAllProject
            // 
            this.cbxAllProject.AutoSize = true;
            this.cbxAllProject.Location = new System.Drawing.Point(132, -1);
            this.cbxAllProject.Name = "cbxAllProject";
            this.cbxAllProject.Padding = new System.Windows.Forms.Padding(2, 0, 0, 0);
            this.cbxAllProject.Size = new System.Drawing.Size(50, 16);
            this.cbxAllProject.TabIndex = 6;
            this.cbxAllProject.Text = "全选";
            this.cbxAllProject.UseVisualStyleBackColor = true;
            this.cbxAllProject.CheckedChanged += new System.EventHandler(this.cbxAllProject_CheckedChanged);
            // 
            // chkListProject
            // 
            this.chkListProject.CheckOnClick = true;
            this.chkListProject.ColumnWidth = 150;
            this.chkListProject.Dock = System.Windows.Forms.DockStyle.Fill;
            this.chkListProject.FormattingEnabled = true;
            this.chkListProject.HorizontalExtent = 300;
            this.chkListProject.HorizontalScrollbar = true;
            this.chkListProject.Location = new System.Drawing.Point(3, 17);
            this.chkListProject.Name = "chkListProject";
            this.chkListProject.ScrollAlwaysVisible = true;
            this.chkListProject.Size = new System.Drawing.Size(185, 99);
            this.chkListProject.TabIndex = 5;
            this.chkListProject.SelectedIndexChanged += new System.EventHandler(this.chkListProject_SelectedIndexChanged);
            // 
            // cbxElapse
            // 
            this.cbxElapse.AutoSize = true;
            this.cbxElapse.Location = new System.Drawing.Point(916, 3);
            this.cbxElapse.Name = "cbxElapse";
            this.cbxElapse.Size = new System.Drawing.Size(84, 16);
            this.cbxElapse.TabIndex = 41;
            this.cbxElapse.Text = "按推移统计";
            this.cbxElapse.UseVisualStyleBackColor = true;
            // 
            // listItem1
            // 
            this.listItem1.BackColor = System.Drawing.SystemColors.Window;
            this.listItem1.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.listItem1.ForeColor = System.Drawing.SystemColors.WindowText;
            this.listItem1.Text = "前一天";
            this.listItem1.Value = "-1";
            // 
            // listItem2
            // 
            this.listItem2.BackColor = System.Drawing.SystemColors.Window;
            this.listItem2.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.listItem2.ForeColor = System.Drawing.SystemColors.WindowText;
            this.listItem2.Text = "前二天";
            this.listItem2.Value = "-2";
            // 
            // listItem3
            // 
            this.listItem3.BackColor = System.Drawing.SystemColors.Window;
            this.listItem3.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.listItem3.ForeColor = System.Drawing.SystemColors.WindowText;
            this.listItem3.Text = "前三天";
            this.listItem3.Value = "-3";
            // 
            // listItem4
            // 
            this.listItem4.BackColor = System.Drawing.SystemColors.Window;
            this.listItem4.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.listItem4.ForeColor = System.Drawing.SystemColors.WindowText;
            this.listItem4.Text = "前四天";
            this.listItem4.Value = "-4";
            // 
            // listItem5
            // 
            this.listItem5.BackColor = System.Drawing.SystemColors.Window;
            this.listItem5.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.listItem5.ForeColor = System.Drawing.SystemColors.WindowText;
            this.listItem5.Text = "前五天";
            this.listItem5.Value = "-5";
            // 
            // listItem6
            // 
            this.listItem6.BackColor = System.Drawing.SystemColors.Window;
            this.listItem6.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.listItem6.ForeColor = System.Drawing.SystemColors.WindowText;
            this.listItem6.Text = "前一天";
            this.listItem6.Value = "-1";
            // 
            // listItem7
            // 
            this.listItem7.BackColor = System.Drawing.SystemColors.Window;
            this.listItem7.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.listItem7.ForeColor = System.Drawing.SystemColors.WindowText;
            this.listItem7.Text = "前二天";
            this.listItem7.Value = "-2";
            // 
            // listItem8
            // 
            this.listItem8.BackColor = System.Drawing.SystemColors.Window;
            this.listItem8.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.listItem8.ForeColor = System.Drawing.SystemColors.WindowText;
            this.listItem8.Text = "前三天";
            this.listItem8.Value = "-3";
            // 
            // listItem9
            // 
            this.listItem9.BackColor = System.Drawing.SystemColors.Window;
            this.listItem9.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.listItem9.ForeColor = System.Drawing.SystemColors.WindowText;
            this.listItem9.Text = "前四天";
            this.listItem9.Value = "-4";
            // 
            // listItem10
            // 
            this.listItem10.BackColor = System.Drawing.SystemColors.Window;
            this.listItem10.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.listItem10.ForeColor = System.Drawing.SystemColors.WindowText;
            this.listItem10.Text = "前五天";
            this.listItem10.Value = "-5";
            // 
            // sortedDataGrid
            // 
            this.sortedDataGrid.AllowUserToAddRows = false;
            this.sortedDataGrid.AllowUserToDeleteRows = false;
            this.sortedDataGrid.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.sortedDataGrid.Dock = System.Windows.Forms.DockStyle.Fill;
            this.sortedDataGrid.Location = new System.Drawing.Point(0, 0);
            this.sortedDataGrid.Name = "sortedDataGrid";
            this.sortedDataGrid.ReadOnly = true;
            this.sortedDataGrid.RowTemplate.DefaultCellStyle.SelectionBackColor = System.Drawing.Color.Lavender;
            this.sortedDataGrid.RowTemplate.DefaultCellStyle.SelectionForeColor = System.Drawing.Color.Black;
            this.sortedDataGrid.RowTemplate.Height = 18;
            this.sortedDataGrid.Size = new System.Drawing.Size(1284, 364);
            this.sortedDataGrid.TabIndex = 2;
            this.sortedDataGrid.CellContentDoubleClick += new System.Windows.Forms.DataGridViewCellEventHandler(this.sortedDataGrid_CellContentDoubleClick);
            this.sortedDataGrid.CellMouseClick += new System.Windows.Forms.DataGridViewCellMouseEventHandler(this.sortedDataGrid_CellMouseClick);
            // 
            // bgWorker
            // 
            this.bgWorker.WorkerReportsProgress = true;
            this.bgWorker.WorkerSupportsCancellation = true;
            this.bgWorker.DoWork += new System.ComponentModel.DoWorkEventHandler(this.bgWorker_DoWork);
            this.bgWorker.ProgressChanged += new System.ComponentModel.ProgressChangedEventHandler(this.bgWorker_ProgressChanged);
            this.bgWorker.RunWorkerCompleted += new System.ComponentModel.RunWorkerCompletedEventHandler(this.bgWorker_RunWorkerCompleted);
            // 
            // toolTipBak
            // 
            this.toolTipBak.BackColor = System.Drawing.Color.Khaki;
            this.toolTipBak.ForeColor = System.Drawing.SystemColors.ActiveCaption;
            this.toolTipBak.ToolTipIcon = System.Windows.Forms.ToolTipIcon.Info;
            // 
            // statusStrip1
            // 
            this.statusStrip1.Location = new System.Drawing.Point(0, 364);
            this.statusStrip1.Name = "statusStrip1";
            this.statusStrip1.Padding = new System.Windows.Forms.Padding(1, 0, 16, 0);
            this.statusStrip1.Size = new System.Drawing.Size(1284, 22);
            this.statusStrip1.TabIndex = 3;
            this.statusStrip1.Text = "statusStrip1";
            // 
            // panel1
            // 
            this.panel1.Controls.Add(this.sortedDataGrid);
            this.panel1.Controls.Add(this.statusStrip1);
            this.panel1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.panel1.Location = new System.Drawing.Point(0, 0);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(1284, 386);
            this.panel1.TabIndex = 4;
            // 
            // toolStripDropDownService
            // 
            this.toolStripDropDownService.LayoutStyle = System.Windows.Forms.ToolStripLayoutStyle.Flow;
            this.toolStripDropDownService.Name = "toolStripDropDown1";
            this.toolStripDropDownService.Size = new System.Drawing.Size(2, 4);
            // 
            // ctxQuickSet
            // 
            this.ctxQuickSet.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.toolStripSeparator1,
            this.miSaveCurSet});
            this.ctxQuickSet.Name = "ctxQuickSet";
            this.ctxQuickSet.Size = new System.Drawing.Size(149, 32);
            // 
            // toolStripSeparator1
            // 
            this.toolStripSeparator1.Name = "toolStripSeparator1";
            this.toolStripSeparator1.Size = new System.Drawing.Size(145, 6);
            // 
            // miSaveCurSet
            // 
            this.miSaveCurSet.Name = "miSaveCurSet";
            this.miSaveCurSet.Size = new System.Drawing.Size(148, 22);
            this.miSaveCurSet.Text = "保存当前条件";
            // 
            // toolStripDropDownEquipment
            // 
            this.toolStripDropDownEquipment.LayoutStyle = System.Windows.Forms.ToolStripLayoutStyle.Flow;
            this.toolStripDropDownEquipment.Name = "toolStripDropDownEquipment";
            this.toolStripDropDownEquipment.Size = new System.Drawing.Size(2, 4);
            // 
            // CommonNoGisStatForm
            // 
            this.Appearance.BackColor = System.Drawing.SystemColors.Control;
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("CommonNoGisStatForm.Appearance.Image")));
            this.Appearance.Options.UseBackColor = true;
            this.Appearance.Options.UseFont = true;
            this.Appearance.Options.UseImage = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1284, 703);
            this.Controls.Add(this.panel1);
            this.Controls.Add(this.dropCondPanel);
            this.Name = "CommonNoGisStatForm";
            this.Text = "非GIS的综合统计";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.StatBeginForm_FormClosing);
            this.dropCondPanel.ResumeLayout(false);
            this.panel2.ResumeLayout(false);
            this.panel2.PerformLayout();
            this.groupBox6.ResumeLayout(false);
            this.groupBox6.PerformLayout();
            this.groupBoxFileName.ResumeLayout(false);
            this.groupBoxFileName.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.ccbePort.Properties)).EndInit();
            this.groupBoxPath.ResumeLayout(false);
            this.groupBoxPath.PerformLayout();
            this.groupBox5.ResumeLayout(false);
            this.groupBox5.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.treeListParam)).EndInit();
            this.cMReport.ResumeLayout(false);
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.groupBoxCarrier.ResumeLayout(false);
            this.groupBoxCarrier.PerformLayout();
            this.groupBox4.ResumeLayout(false);
            this.groupBox4.PerformLayout();
            this.tbTimeValue.ResumeLayout(false);
            this.tabPagetime.ResumeLayout(false);
            this.tabPagetime.PerformLayout();
            this.panelCustomTime.ResumeLayout(false);
            this.panelCustomTime.PerformLayout();
            this.panelAdvanceTime.ResumeLayout(false);
            this.tabPageround.ResumeLayout(false);
            this.tabPageround.PerformLayout();
            this.tabPageElapse.ResumeLayout(false);
            this.tabPageElapse.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numElapseTo)).EndInit();
            this.groupBox7.ResumeLayout(false);
            this.groupBox7.PerformLayout();
            this.groupBoxDaiwei.ResumeLayout(false);
            this.groupBoxDaiwei.PerformLayout();
            this.groupBox3.ResumeLayout(false);
            this.groupBox3.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.sortedDataGrid)).EndInit();
            this.panel1.ResumeLayout(false);
            this.panel1.PerformLayout();
            this.ctxQuickSet.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private ScrewTurn.DropDownPanel dropCondPanel;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.CheckedListBox chListArea;
        private System.Windows.Forms.CheckBox cbxAllArea;
        private System.ComponentModel.BackgroundWorker bgWorker;
        private Mobius.Utility.SortedDataGridView sortedDataGrid;
        private System.Windows.Forms.ToolTip toolTipBak;
        private System.Windows.Forms.StatusStrip statusStrip1;
        private System.Windows.Forms.Panel panel1;
        private System.Windows.Forms.Label lbSelAreaType;
        private System.Windows.Forms.GroupBox groupBox4;
        private System.Windows.Forms.ListView listViewService;
        private System.Windows.Forms.Label lbSvCount;
        private System.Windows.Forms.Button btnPopupService;
        private System.Windows.Forms.TabControl tbTimeValue;
        private System.Windows.Forms.TabPage tabPagetime;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.DateTimePicker dateTimeFrom;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.DateTimePicker dateTimeTo;
        private System.Windows.Forms.TabPage tabPageround;
        private System.Windows.Forms.ComboBox cbxProjToYear;
        private System.Windows.Forms.ComboBox cbxProjFromYear;
        private System.Windows.Forms.ComboBox cbxProjToRound;
        private System.Windows.Forms.ComboBox cbxProjFromRound;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.GroupBox groupBoxDaiwei;
        private System.Windows.Forms.Label label_daiwei;
        private System.Windows.Forms.CheckBox chbAllAgent;
        private System.Windows.Forms.ListView listViewAgent;
        private System.Windows.Forms.GroupBox groupBox3;
        private System.Windows.Forms.GroupBox groupBox7;
        private System.Windows.Forms.ComboBox cbxRptList;
        private System.Windows.Forms.Label label11;
        private System.Windows.Forms.Button btnDoRun;
        private System.Windows.Forms.ProgressBar progBar;
        private System.Windows.Forms.Button btnCancelRun;
        private System.Windows.Forms.Label lbStatus;
        private System.Windows.Forms.GroupBox groupBoxFileName;
        private System.Windows.Forms.Label label_fileName;
        private System.Windows.Forms.TextBox textBoxFileName;
        private System.Windows.Forms.CheckBox checkBoxFileName;
        private System.Windows.Forms.GroupBox groupBoxCarrier;
        private System.Windows.Forms.Label label_carrier;
        private System.Windows.Forms.ListView listViewCarrier;
        private System.Windows.Forms.ToolStripDropDown toolStripDropDownService;
        private System.Windows.Forms.Panel panel2;
        private System.Windows.Forms.CheckedListBox chkListProject;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.Label label9;
        private System.Windows.Forms.ListView listViewCity;
        private System.Windows.Forms.Button btnQuickSet;
        private System.Windows.Forms.ContextMenuStrip ctxQuickSet;
        private System.Windows.Forms.ToolStripMenuItem miSaveCurSet;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator1;
        private System.Windows.Forms.TextBox txbFilter;
        private System.Windows.Forms.Label lblFilter;
        private System.Windows.Forms.CheckBox cbxShowMerge;
        private System.Windows.Forms.Button btnClearPort;
        private System.Windows.Forms.Panel panelCustomTime;
        private System.Windows.Forms.Panel panelAdvanceTime;
        private System.Windows.Forms.CheckBox checkBoxPeriodAdvance;
        private System.Windows.Forms.ListBox listBoxPeriod;
        private System.Windows.Forms.Button buttonPeriodAdd;
        private System.Windows.Forms.Button buttonPeriodClear;
        private System.Windows.Forms.Button btnImport;
        private System.Windows.Forms.ToolStripDropDown toolStripDropDownEquipment;
        private System.Windows.Forms.Button btnResetArea;
        private System.Windows.Forms.Button btnGainTemplate;
        private System.Windows.Forms.Button btnImportFilter;
        private System.Windows.Forms.GroupBox groupBox5;
        private System.Windows.Forms.Button btnMultiOK;
        private System.Windows.Forms.ComboBox cbxfileNumType;
        private System.Windows.Forms.GroupBox groupBoxPath;
        private System.Windows.Forms.Button btnSelectPath;
        private System.Windows.Forms.Label labelPath;
        private System.Windows.Forms.Button btnOneKeyQuery;
        private System.Windows.Forms.TabPage tabPageElapse;
        private System.Windows.Forms.Label label22;
        private System.Windows.Forms.Label label23;
        private MasterCom.Util.UiEx.ListItem listItem1;
        private MasterCom.Util.UiEx.ListItem listItem2;
        private MasterCom.Util.UiEx.ListItem listItem3;
        private MasterCom.Util.UiEx.ListItem listItem4;
        private MasterCom.Util.UiEx.ListItem listItem5;
        private MasterCom.Util.UiEx.ListItem listItem6;
        private MasterCom.Util.UiEx.ListItem listItem7;
        private MasterCom.Util.UiEx.ListItem listItem8;
        private MasterCom.Util.UiEx.ListItem listItem9;
        private MasterCom.Util.UiEx.ListItem listItem10;
        private System.Windows.Forms.CheckBox cbxElapse;
        private System.Windows.Forms.NumericUpDown numElapseTo;
        private System.Windows.Forms.Label label25;
        private System.Windows.Forms.DateTimePicker dateTimeElapse;
        private System.Windows.Forms.Label lbprogress;
        private System.Windows.Forms.Label lbprogressText;
        private System.Windows.Forms.Button btnUpdateConditionTime;
        private System.Windows.Forms.GroupBox groupBox6;
        private DevExpress.XtraTreeList.TreeList treeListParam;
        private DevExpress.XtraTreeList.Columns.TreeListColumn colKey;
        private System.Windows.Forms.CheckBox checkAllCity;
        private System.Windows.Forms.ContextMenuStrip cMReport;
        private System.Windows.Forms.ToolStripMenuItem addTeam;
        private System.Windows.Forms.ToolStripMenuItem saveChange;
        private System.Windows.Forms.ToolStripMenuItem deteleSunReport;
        private System.Windows.Forms.ToolStripMenuItem addSaveCurReport;
        private System.Windows.Forms.Label label24;
        private System.Windows.Forms.TextBox txtFilterReport;
        private DevExpress.XtraEditors.CheckedComboBoxEdit ccbePort;
        private System.Windows.Forms.Label label10;
        private System.Windows.Forms.CheckBox chbReject;
        private System.Windows.Forms.CheckBox cbxAllProject;
        private System.Windows.Forms.ComboBox cbxQueryFunc;
        private System.Windows.Forms.Label lblQueryTime;
    }
}