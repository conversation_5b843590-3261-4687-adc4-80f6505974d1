﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class LTEUnknownDisturbInfo
    {
        private readonly List<TestPoint> testPoints = new List<TestPoint>();
        public List<TestPoint> TestPoints
        {
            get { return testPoints; }
        }
        public int TestPointCount
        {
            get { return testPoints.Count; }
        }
        public double MidLng
        {
            get
            {
                double lng = double.NaN;
                if (TestPointCount > 0)
                {
                    lng = testPoints[TestPointCount / 2].Longitude;
                }
                return lng;
            }
        }
        public double MidLat
        {
            get
            {
                double lat = double.NaN;
                if (TestPointCount > 0)
                {
                    lat = testPoints[TestPointCount / 2].Latitude;
                }
                return lat;
            }
        }
        public int SN
        {
            get;
            set;
        }
        private float minRsrp = float.MaxValue;
        public float MinRsrp
        {
            get { return minRsrp; }
        }
        private float maxRsrp = float.MinValue;
        public float MaxRsrp
        {
            get { return maxRsrp; }
        }
        private float sumRsrp = 0;
        public float AvgRsrp
        {
            get
            {
                float avg = float.NaN;
                if (testPoints.Count > 0)
                {
                    avg = (float)Math.Round(sumRsrp / testPoints.Count, 2);
                }
                return avg;
            }
        }
        private float sumNMaxRsrp = 0;
        public float AvgNMaxRsrp
        {
            get
            {
                float avg = float.NaN;
                if (testPoints.Count > 0)
                {
                    avg = (float)Math.Round(sumNMaxRsrp / testPoints.Count, 2);
                }
                return avg;
            }
        }
        public string FileName
        {
            get
            {
                string name = string.Empty;
                if (testPoints.Count > 0)
                {
                    name = testPoints[0].FileName;
                }
                return name;
            }
        }
        private float minSinr = float.MaxValue;
        public float MinSinr
        {
            get { return minSinr; }
        }
        private float maxSinr = float.MinValue;
        public float MaxSinr
        {
            get { return maxSinr; }
        }
        private float sumSinr = 0;
        public float AvgSinr
        {
            get
            {
                float avg = float.NaN;
                if (testPoints.Count > 0)
                {
                    avg = (float)Math.Round(sumSinr / testPoints.Count, 2);
                }
                return avg;
            }
        }

        public double StaySecond
        {
            get
            {
                double sec = 0;
                if (testPoints.Count > 1)
                {
                    sec = (testPoints[testPoints.Count - 1].DateTime - testPoints[0].DateTime).TotalSeconds;
                }
                return sec;
            }
        }

        private double stayDistance = 0;
        public double StayDistance
        {
            get
            {
                return stayDistance;
            }
        }
        private string streetNames = null;
        public string RoadName
        {
            get
            {
                return streetNames;
            }
        }
        public void FindRoadName()
        {
            if (testPoints.Count > 0)
            {
                List<double> lngs = new List<double>();
                List<double> lats = new List<double>();
                TestPoint tp = testPoints[0];
                lngs.Add(tp.Longitude);
                lats.Add(tp.Latitude);
                tp = testPoints[(testPoints.Count / 2)];
                lngs.Add(tp.Longitude);
                lats.Add(tp.Latitude);
                tp = testPoints[testPoints.Count - 1];
                lngs.Add(tp.Longitude);
                lats.Add(tp.Latitude);
                streetNames = GISManager.GetInstance().GetRoadPlaceDesc(lngs, lats);
            }
        }

        public void AddTestPoint(TestPoint tp, LTEUnKnownDisturbAnaByRegion.TPInfo tpInfo, double dis2LastTP)
        {
            testPoints.Add(tp);
            minRsrp = Math.Min(minRsrp, tpInfo.Rsrp);
            maxRsrp = Math.Max(maxRsrp, tpInfo.Rsrp);
            sumRsrp += tpInfo.Rsrp;
            sumNMaxRsrp += tpInfo.NMaxRsrp;
            minSinr = Math.Min(minSinr, tpInfo.Sinr);
            maxSinr = Math.Max(maxSinr, tpInfo.Sinr);
            sumSinr += tpInfo.Sinr;
            stayDistance += dis2LastTP;
        }
    }
}
