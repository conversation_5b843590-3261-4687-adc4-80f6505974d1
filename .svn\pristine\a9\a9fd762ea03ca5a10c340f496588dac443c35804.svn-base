﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public abstract class CScene : ICloneable
    {
        public CCauseBase SceneCause { get; set; }
        
        public CCauseBase SceneCauseCat { get; set; }

        protected int msgIDBegin = -1;

        protected int msgIDEnd = -1;

        protected int msgIdxBegin = -1;

        protected int msgIdxEnd = -1;

        public virtual bool IsInScene(DTFileDataManager file, Event e)
        {
            int idxE = file.DTDatas.IndexOf(e);

            msgIdxBegin = idxE - 1;
            msgIdxEnd = idxE + 1;

            bool forwardFinded = false, backwardFinded = false;

            while (msgIdxBegin >= 0 && msgIdxEnd < file.DTDatas.Count)
            {
                bool isFind = judgeForwardFinded(file, ref forwardFinded);
                if (!isFind)
                {
                    return false;
                }
                isFind = judgeBackwardFinded(file, ref backwardFinded);
                if (!isFind)
                {
                    return false;
                }

                if (forwardFinded && backwardFinded)
                {
                    break;
                }
            }
            return forwardFinded && backwardFinded;
        }

        private bool judgeForwardFinded(DTFileDataManager file, ref bool forwardFinded)
        {
            if (!forwardFinded)
            {
                DTData dataForward = file.DTDatas[msgIdxBegin];

                if (dataForward is Message)
                {
                    Message ms = dataForward as Message;

                    if (ms.ID == msgIDEnd)
                    {
                        return false;
                    }
                    else if (ms.ID == msgIDBegin)
                    {
                        msgIdxBegin++;
                        forwardFinded = true;
                    }
                }
                msgIdxBegin--;
            }
            return true;
        }

        private bool judgeBackwardFinded(DTFileDataManager file, ref bool backwardFinded)
        {
            if (!backwardFinded)
            {
                DTData dataBackward = file.DTDatas[msgIdxEnd];

                if (dataBackward is Message)
                {
                    Message ms = dataBackward as Message;

                    if (ms.ID == msgIDBegin)
                    {
                        return false;
                    }
                    else if (ms.ID == msgIDEnd)
                    {
                        msgIdxEnd--;
                        backwardFinded = true;
                    }
                }
                msgIdxEnd++;
            }
            return true;
        }

        public virtual CCauseBase GetCause(DTFileDataManager file, Event e)
        {
            return SceneCause.GetCause(file, e, msgIdxBegin, msgIdxEnd);
        }

        public abstract string GetDesc();

        public virtual void ReCatCause(CCauseBase cause)
        {
            SceneCauseCat.NextCause = cause;
        }

        #region ICloneable 成员

        public object Clone()
        {
            CScene scene = (CScene)this.MemberwiseClone();
            scene.SceneCause = (CCauseBase)(scene.SceneCause.Clone());
            if (scene.SceneCauseCat != null)
                scene.SceneCauseCat = (CCauseBase)(scene.SceneCauseCat.Clone());
            return scene;
        }

        #endregion
    }

    public abstract class CNoCatScene : CScene
    {
        public override void ReCatCause(CCauseBase cause)
        {
            //
        }
    }

    public abstract class CEqualScene : CScene
    {
        public override void ReCatCause(CCauseBase cause)
        {
            SceneCause = cause;
        }
    }

    public abstract class CMoScene : CScene
    {
        public override bool IsInScene(DTFileDataManager file, Event e)
        {
            if (e.MoMtFlag == (int)MoMtFile.MoFlag) return false;

            return base.IsInScene(file, e);
        }
    }

    public abstract class CMtScene : CScene
    {
        public override bool IsInScene(DTFileDataManager file, Event e)
        {
            if (e.MoMtFlag == (int)MoMtFile.MtFlag) return false;

            return base.IsInScene(file, e);
        }
    }

    public class CScene_ServiceReject : CScene
    {
        public CScene_ServiceReject()
        {
            SceneCause = new CServiceRejectCause();
            SceneCauseCat = SceneCause;

            CScene_Normal normal = new CScene_Normal();

            SceneCause.NextCause = normal.SceneCause;

            msgIDBegin = (int)EMessage.Extended_Service_Request;
            msgIDEnd = (int)EMessage.RRCConnectionRelease;
        }

        public override string GetDesc()
        {
            return "Extended Service Request到CSFB RRC Release之间出现未接通";
        }
    }

    public class CScene_Fall2G : CNoCatScene
    {
        public CScene_Fall2G()
        {
            SceneCause = new CFall2GCause();
            SceneCauseCat = null;

            msgIDBegin = (int)EMessage.RRCConnectionRelease;
        }

        public override bool IsInScene(DTFileDataManager file, Event e)
        {
            int idxE = file.DTDatas.IndexOf(e);
            msgIdxBegin = idxE - 1;
            msgIdxEnd = idxE - 1;
            Message msg = null;

            for (; msgIdxBegin >= 0; msgIdxBegin--)
            {
                DTData data = file.DTDatas[msgIdxBegin];

                if (data is Message)
                {
                    Message ms = data as Message;
                    if (ms.ID == (int)EMessage.RRCConnectionRelease)
                    {
                        msg = ms;
                        break;
                    }
                }
            }

            if (msg == null) return false;

            uint carrier = 0;
            MasterCom.RAMS.Model.MessageWithSource msgWithSoure = msg as MasterCom.RAMS.Model.MessageWithSource;
            MessageDecodeHelper.StartDissect(msg.Direction,msgWithSoure.Source, msgWithSoure.Length, msgWithSoure.ID);
            if (!MessageDecodeHelper.GetSingleUInt("lte-rrc.redirectedCarrierInfo", ref carrier))
            {
                return false;
            }

            return carrier != (int)ECarrierInfo.geran;
        }

        public override string GetDesc()
        {
            return "CSFB Coverage （LTE->GSM）LTE网络回落到GSM网络之间出现未接通";
        }
    }

    public class CScene_Mo2G : CEqualScene
    {
        public CScene_Mo2G()
        {
            CScene_Normal normal = new CScene_Normal();

            SceneCause = normal.SceneCause;
            SceneCauseCat = null;

            msgIDBegin = (int)EMessage.CM_service_requestGSM;
            msgIDEnd = (int)EMessage.Authentication_RequestGSM;
        }

        public override bool IsInScene(DTFileDataManager file, Event e)
        {
            if (!base.IsInScene(file, e))
            {
                msgIDBegin = msgIDBegin == (int)EMessage.CM_service_requestGSM ? (int)EMessage.CM_service_requestTD : (int)EMessage.CM_service_requestGSM;
                msgIDEnd = msgIDEnd == (int)EMessage.Authentication_RequestGSM ? (int)EMessage.Authentication_RequestTD : (int)EMessage.Authentication_RequestGSM;
                return base.IsInScene(file, e);
            }
            return true;
        }

        public override string GetDesc()
        {
            return "CM service request到Authentication Request之间出现未接通";
        }
    }

    public class CScene_SDBlock : CScene
    {
        public CScene_SDBlock()
        {
            SceneCause = new CSDBlockCause();
            SceneCauseCat = SceneCause;

            CScene_Normal normal = new CScene_Normal();

            SceneCause.NextCause = normal.SceneCause;

            msgIDBegin = (int)EMessage.Authentication_RequestGSM;
            msgIDEnd = (int)EMessage.setupGSM;
        }

        public override bool IsInScene(DTFileDataManager file, Event e)
        {
            if (!base.IsInScene(file, e))
            {
                msgIDBegin = msgIDBegin == (int)EMessage.Authentication_RequestGSM ? (int)EMessage.Authentication_RequestTD : (int)EMessage.Authentication_RequestGSM;
                msgIDEnd = msgIDEnd == (int)EMessage.setupGSM ? (int)EMessage.setupLTE : (int)EMessage.setupGSM;
                return base.IsInScene(file, e);
            }
            return true;
        }

        public override string GetDesc()
        {
            return "Authentication Request到setup之间出现未接通";
        }
    }

    public class CScene_MtSetupAssignment : CMtScene
    {
        public CScene_MtSetupAssignment()
        {
            SceneCause = new CMtLocationUpdateCause();

            CMtOOSCause mtOOSCause = new CMtOOSCause();

            CMtNotReceiveCause mtNotReceive = new CMtNotReceiveCause();

            CScene_Normal normal = new CScene_Normal();

            SceneCause.NextCause = mtOOSCause;
            mtOOSCause.NextCause = mtNotReceive;
            mtNotReceive.NextCause = normal.SceneCause;

            SceneCauseCat = mtNotReceive;

            msgIDBegin = (int)EMessage.setupGSM;
            msgIDEnd = (int)EMessage.Assignment_complete;
        }

        public override bool IsInScene(DTFileDataManager file, Event e)
        {
            if (!base.IsInScene(file, e))
            {
                msgIDBegin = msgIDBegin == (int)EMessage.setupGSM ? (int)EMessage.setupLTE : (int)EMessage.setupGSM;
                msgIDEnd = (int)EMessage.Assignment_complete;
                return base.IsInScene(file, e);
            }
            return true;
        }

        public override string GetDesc()
        {
            return "setup到Assignment completed之间出现未接通";
        }
    }

    public class CScene_MoAssimentAlert : CMoScene
    {
        public CScene_MoAssimentAlert()
        {
            SceneCause = new CMoHangUpCause();

            CMoLocationUpdateCause moLPCause = new CMoLocationUpdateCause();

            CScene_Normal normal = new CScene_Normal();

            SceneCause.NextCause = moLPCause;
            moLPCause.NextCause = normal.SceneCause;

            SceneCauseCat = moLPCause;

            msgIDBegin = (int)EMessage.Assignment_complete;
            msgIDEnd = (int)EMessage.AlertingGSM;
        }

        public override bool IsInScene(DTFileDataManager file, Event e)
        {
            if (!base.IsInScene(file, e))
            {
                msgIDBegin = (int)EMessage.Assignment_complete;
                msgIDEnd = msgIDEnd == (int)EMessage.AlertingGSM ? (int)EMessage.AlertingTD : (int)EMessage.AlertingGSM;
                return base.IsInScene(file, e);
            }
            return true;
        }

        public override string GetDesc()
        {
            return "Assignment completed到Alerting之间出现未接通";
        }
    }

    public class CScene_CMoRequest2BlockCall : CMtScene
    {
        public CScene_CMoRequest2BlockCall()
        {
            SceneCause = new CMoProceedingCause();

            CPretendCause pretendCause = new CPretendCause();
            CLURCause lurCause = new CLURCause();
            CServiceRejectCause rejectCause = new CServiceRejectCause();
            CServiceAbortCause abortCause = new CServiceAbortCause();
            CSDBlockCause blockCause = new CSDBlockCause();
            CMtTACause mtTACause = new CMtTACause();
            CBlockCallCause blockCallCause = new CBlockCallCause();
            CMtCallingCause mtCallingCause = new CMtCallingCause();
            CMtCallingNoServiceRequestCause mtCallingNoServiceRequestCause = new CMtCallingNoServiceRequestCause();
            CMtCallingNoCallCause mtCallingNoCallCause = new CMtCallingNoCallCause();
            CMoProceedingNoRRCReleaseCause moPNoRRCReleaseCause = new CMoProceedingNoRRCReleaseCause();
            CMoProceedingRRCReleaseWithEarfcnCause moProceedingRRCWithEarfcnCause = new CMoProceedingRRCReleaseWithEarfcnCause();
            CMoProceedingRRCReleaseCause moProceedingRRCCause = new CMoProceedingRRCReleaseCause();
            CMtLURCauseCause mtLURCause = new CMtLURCauseCause();
            CMtOOSCause oosCause = new CMtOOSCause();
            CTCHDisconnectCause disconnectCause = new CTCHDisconnectCause();
            CPersonDoesCause personCause = new CPersonDoesCause();
            CPersonDoes2Cause personCause2 = new CPersonDoes2Cause();

            CScene_Normal normal = new CScene_Normal();

            //SceneCause.NextCause = pretendCause;
            //pretendCause.NextCause = lurCause;
            //lurCause.NextCause = rejectCause;
            //rejectCause.NextCause = abortCause;
            //abortCause.NextCause = blockCause;
            //blockCause.NextCause = mtTACause;
            //mtTACause.NextCause = blockCallCause;
            //blockCallCause.NextCause = mtCallingCause;
            //mtCallingCause.NextCause = mtCallingNoServiceRequestCause;
            //mtCallingNoServiceRequestCause.NextCause = mtCallingNoCallCause;
            //mtCallingNoCallCause.NextCause = moPNoRRCReleaseCause;
            //moPNoRRCReleaseCause.NextCause = moProceedingRRCWithEarfcnCause;
            //moProceedingRRCWithEarfcnCause.NextCause = moProceedingRRCCause;
            //moProceedingRRCCause.NextCause = mtLURCause;
            //mtLURCause.NextCause = oosCause;
            //oosCause.NextCause = disconnectCause;
            //disconnectCause.NextCause = personCause;
            //personCause.NextCause = personCause2;
            //personCause2.NextCause = normal.SceneCause;

            //SceneCauseCat = personCause;

            SceneCause = normal.SceneCause;
            SceneCauseCat = normal.SceneCause;

            msgIDBegin = (int)EMessage.CM_service_requestGSM;
        }

        public override bool IsInScene(DTFileDataManager file, Event e)
        {
            if (e.MoMtFlag == (int)MoMtFile.MtFlag) return false;

            int idxE = file.DTDatas.IndexOf(e);

            msgIdxBegin = idxE - 1;
            msgIdxEnd = idxE;

            bool forwardFinded = false;

            while (msgIdxBegin >= 0 && msgIdxEnd < file.DTDatas.Count)
            {

                DTData dataForward = file.DTDatas[msgIdxBegin];

                if (dataForward is Message)
                {
                    Message ms = dataForward as Message;

                    if (ms.ID == msgIDEnd)
                    {
                        return false;
                    }
                    else if (ms.ID == msgIDBegin)
                    {
                        msgIdxBegin++;
                        forwardFinded = true;
                        return forwardFinded;
                    }
                }
                msgIdxBegin--;

            }
            return forwardFinded;
        }

        public override string GetDesc()
        {
            return "主叫CM service request到未接通事件";
        }
    }

    public class CScene_CMtRequest2BlockCall : CMtScene
    {
        public CScene_CMtRequest2BlockCall()
        {
            SceneCause = new CMtOOSCause();

            CBlockCallCause blockCause = new CBlockCallCause();
            CPretendCause pretendCause = new CPretendCause();
            CTCHDisconnectCause disconnectCause = new CTCHDisconnectCause();
            CPersonDoesCause personCause = new CPersonDoesCause();

            CScene_Normal normal = new CScene_Normal();

            //SceneCause.NextCause = blockCause;
            //blockCause.NextCause = pretendCause;
            //pretendCause.NextCause = disconnectCause;
            //disconnectCause.NextCause = personCause;
            //personCause.NextCause = normal.SceneCause;

            //SceneCauseCat = personCause;

            SceneCause = normal.SceneCause;
            SceneCauseCat = normal.SceneCause;

            msgIDBegin = (int)EMessage.RRCConnectionRelease;
        }

        public override bool IsInScene(DTFileDataManager file, Event e)
        {
            if (e.MoMtFlag == (int)MoMtFile.MoFlag) return false;

            int idxE = file.DTDatas.IndexOf(e);

            msgIdxBegin = idxE - 1;
            msgIdxEnd = idxE;

            bool forwardFinded = false;

            while (msgIdxBegin >= 0 && msgIdxEnd < file.DTDatas.Count)
            {
                DTData dataForward = file.DTDatas[msgIdxBegin];

                if (dataForward is Message)
                {
                    Message ms = dataForward as Message;

                    if (ms.ID == msgIDEnd)
                    {
                        return false;
                    }
                    else if (ms.ID == msgIDBegin)
                    {
                        msgIdxBegin++;
                        forwardFinded = true;
                        return forwardFinded;
                    }
                }
                msgIdxBegin--;
            }
            return forwardFinded;
        }

        public override string GetDesc()
        {
            return "主叫CM service request到未接通事件";
        }
    }

    public class CScene_Normal : CScene
    {
        public CScene_Normal()
        {
            SceneCause = new CWeakCovCause();
            COverCoverCause overCovCause = new COverCoverCause();
            CCovInConformityCause covInconfCause = new CCovInConformityCause();
            CBadQuaCause badquaCause = new CBadQuaCause();
            CStrongRxlBadQuaCause stronbadquaCause = new CStrongRxlBadQuaCause();
            CCoverLapCause covlapCause = new CCoverLapCause();
            CModThreeCause mod3Cause = new CModThreeCause();
            CFrequentHOCause frequenthoCause = new CFrequentHOCause();
            CDelayHOCause delayhoCause = new CDelayHOCause();
            CUnReasonableHO unreasonhoCause = new CUnReasonableHO();
            CFastFailureCause fastfailureCause = new CFastFailureCause();
            CIndoorLeakCause indoorleakCause = new CIndoorLeakCause();

            SceneCause.PrevCause = null;
            SceneCause.NextCause = overCovCause;
            overCovCause.PrevCause = SceneCause;
            overCovCause.NextCause = covInconfCause;
            covInconfCause.PrevCause = overCovCause;
            covInconfCause.NextCause = badquaCause;
            badquaCause.PrevCause = covInconfCause;
            badquaCause.NextCause = stronbadquaCause;
            stronbadquaCause.PrevCause = badquaCause;
            stronbadquaCause.NextCause = covlapCause;
            covlapCause.PrevCause = stronbadquaCause;
            covlapCause.NextCause = mod3Cause;
            mod3Cause.PrevCause = covlapCause;
            mod3Cause.NextCause = frequenthoCause;
            frequenthoCause.PrevCause = mod3Cause;
            frequenthoCause.NextCause = delayhoCause;
            delayhoCause.PrevCause = frequenthoCause;
            delayhoCause.NextCause = unreasonhoCause;
            unreasonhoCause.PrevCause = delayhoCause;
            unreasonhoCause.NextCause = fastfailureCause;
            fastfailureCause.PrevCause = unreasonhoCause;
            fastfailureCause.NextCause = indoorleakCause;
            indoorleakCause.PrevCause = fastfailureCause;
            indoorleakCause.NextCause = null;
        }

        public override string GetDesc()
        {
            return "分析12种原因";
        }
    }
}
