﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class WeakCellConditionNRDlg : BaseDialog
    {
        public WeakCellConditionNRDlg()
            : base()
        {
            InitializeComponent();
        }

        public void SetCondition(WeakLteCellCondition cond)
        {
            numRSRP.Value = (decimal)cond.RsrpMax;
            numSINRMax.Value = (decimal)cond.SinrMax;
            chkSINR.Checked = cond.IsCheckSinr;
            radAndOr.SelectedIndex = cond.SinrIsAnd ? 0 : 1;
        }

        public WeakLteCellCondition GetCondition()
        {
            WeakLteCellCondition cond = new WeakLteCellCondition();
            cond.RsrpMax = (float)numRSRP.Value;
            cond.SinrMax = (float)numSINRMax.Value;
            cond.IsCheckSinr = chkSINR.Checked;
            cond.SinrIsAnd = radAndOr.SelectedIndex == 0;
            return cond;
        }

        private void chkSINR_CheckedChanged(object sender, EventArgs e)
        {
            numSINRMax.Enabled = chkSINR.Checked;
            radAndOr.Enabled = chkSINR.Checked;
        }
    }
}
