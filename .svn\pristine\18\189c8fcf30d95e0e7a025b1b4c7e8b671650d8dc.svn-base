﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Chris.Util;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public enum EEventLTEID
    {
        LTE_CellReselection_L2G = 1300,
        LTE_CellReselection_L2T = 1302,
        LTE_CellReselection_T2G = 1304,
        LTE_CellReselection_G2L = 1306,
        LTE_CellReselection_T2L = 1308,
        LTE_CellReselection_G2T = 1310,
        LTE_CellReselection_G2G = 1312,
        LTE_CellReselection_T2T = 1314,
        LTE_CellReselection_L2L = 1316,

        Intra_Handover_Success = 851,
        Inter_Handover_Success = 899,
        GSM_Handover_Success = 1309,
    }

    public enum EEventTDID
    {
        TD_CellReselection_T2G = 137,
        TD_CellReselection_G2T = 139,
        TD_CellReselection_T2T = 179,
        TD_CellReselection_G2G = 181,
        Cell_reselection_Success_in_TDD_Mode = 236,

        TD_HandoverSuccess_T2G = 142,
        TD_HandoverSuccess_IntraT = 145,
        TD_HandoverSuccess_Baton = 148,
        TD_HandoverSuccess_IntraG = 151,
        TD_HandoverSuccess_RBReconfigT = 232,
    }

    public enum EEventGSMID
    {
        Cell_Reselection = 40,
        Cell_Reselection_TooSlow = 75,
        Cell_Reselection_nonormal = 913,

        Handover_Success = 17,
    }

    public class ZTMainCellLastOccupyAnaBase : DIYAnalyseFilesOneByOneByRegion
    {
        private readonly List<CellOccupyFileInfo> occupyFileList = new List<CellOccupyFileInfo>();
        private readonly List<string> cellNameList = new List<string>();
        protected ParamName baseParamName;
        protected List<int> eventIDLst;
        public ZTMainCellLastOccupySetCondition hoCondition { get; set; }

        public ZTMainCellLastOccupyAnaBase(MainModel mainModel)
            : base(mainModel) 
        {
            FilterSampleByRegion = false;
            IncludeEvent = true;
            baseParamName = new ParamName("RxLevSub", "RxQualSub");
            eventIDLst = new List<int>();
        }

        protected override void clearDataBeforeAnalyseFiles()
        {
            occupyFileList.Clear();
            cellNameList.Clear();
        }

        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            bool rxlevValid = false;
            if (testPoint is TestPointDetail)
            {
                short? rxlev = (short?)testPoint["RxLevSub"];
                rxlevValid = rxlev != null && rxlev >= -125 && rxlev <= -10;
            }
            else if (testPoint is TDTestPointDetail)
            {
                float? rscp = (float?)testPoint["TD_PCCPCH_RSCP"];
                rxlevValid = rscp != null && rscp >= -140 && rscp <= -10;
            }
            else if (testPoint is LTETestPointDetail)
            {
                float? rsrp = (float?)testPoint["lte_RSRP"];
                rxlevValid = rsrp != null && rsrp >= -141 && rsrp <= 25;
            }
            return rxlevValid && base.isValidTestPoint(testPoint);
        }

        protected override void doStatWithQuery()
        {
            ZTAnaTpAmongEvents analyse = new ZTAnaTpAmongEvents(dealTimePeriodPnts, MainModel);
            analyse.SetEventID(eventIDLst);
            analyse.DealData();
        }

        private void dealTimePeriodPnts(List<TimePeriodPoints> timePeriodPnts, DTFileDataManager file)
        {
            if (timePeriodPnts.Count <= 0) return;
            CellOccupyFileInfo occupyFile = new CellOccupyFileInfo(file.GetFileInfo());
            foreach (TimePeriodPoints tpPnt in timePeriodPnts)
            {
                dealTimePeriodPnt(tpPnt, occupyFile, file);
            }
            if (occupyFile.CellInfoList.Count > 0)
            {
                occupyFileList.Add(occupyFile);
                occupyFile.Sn = occupyFileList.Count;
            }
        }

        private void dealTimePeriodPnt(TimePeriodPoints tpPnt, CellOccupyFileInfo occupyFile ,DTFileDataManager file)
        {
            if (tpPnt.TestPnts.Count <= 0) return;
            CellOccupyCellInfo cellInfo = new CellOccupyCellInfo(tpPnt);
            cellInfo.DTFile = file;
            foreach (TestPoint tp in tpPnt.TestPnts)
            {
                if (isValidTestPoint(tp))
                {
                    cellInfo.AddPnt(tp);
                }
            }
            if (cellInfo.TestPntList.Count > 0)
            {
                double sumRSRP = 0;
                double sumSINR = 0;
                int numRSRP = 0;
                int numSINR = 0;
                int smallRSRPCount = 0;
                int smallSINRCount = 0;

                List<TestPoint> tpList = new List<TestPoint>();
                foreach (TestPoint tp in cellInfo.TestPntList)
                {
                    if (tp is TestPointDetail
                        && tp["LAC"] != null && tp["CI"] != null && cellInfo.TpPnt.eEnd["LAC"] != null && cellInfo.TpPnt.eEnd["CI"] != null
                        && (int)tp["LAC"] != (int)cellInfo.TpPnt.eEnd["LAC"] && (int)tp["CI"] != (int)cellInfo.TpPnt.eEnd["CI"])
                    {
                        tpList.Add(tp);
                    }
                }

                foreach (TestPoint tp in tpList)
                {
                    if (tp is TestPointDetail)
                    {
                        cellInfo.TestPntList.Remove(tp);
                    }
                }

                foreach (TestPoint tp in cellInfo.TestPntList)
                {
                    if (tp is TestPointDetail)
                    {
                        if (tp["RxLevSub"] != null)
                        {
                            short? rxlev = (short?)tp["RxLevSub"];
                            sumRSRP += (short)rxlev;
                            numRSRP++;
                            if ((short)rxlev <= hoCondition.RxLev)
                            {
                                smallRSRPCount++;
                            }
                        }
                        if (tp["RxQualSub"] != null)
                        {
                            byte? rxqul = (byte?)tp["RxQualSub"];
                            sumSINR += (byte)rxqul;
                            numSINR++;
                            if ((byte)rxqul >= hoCondition.RxQul)
                            {
                                smallSINRCount++;
                            }
                        }
                    }
                    else if (tp is LTETestPointDetail)
                    {
                        if (tp["lte_SINR"] != null)
                        {
                            sumSINR += (float)tp["lte_SINR"];
                            numSINR++;
                            if ((float)tp["lte_SINR"] <= hoCondition.SINR)
                            {
                                smallSINRCount++;
                            }
                        }
                        if (tp["lte_RSRP"] != null)
                        {
                            sumRSRP += (float)tp["lte_RSRP"];
                            numRSRP++;
                            if ((float)tp["lte_RSRP"] <= hoCondition.RSRP)
                            {
                                smallRSRPCount++;
                            }
                        }
                    }
                    
                }
                if (numRSRP != 0)
                {
                    cellInfo.AvgRSRP = Math.Round(sumRSRP / numRSRP, 2);
                    cellInfo.SmallRSRPNum = smallRSRPCount;
                }
                if (numSINR != 0)
                {
                    cellInfo.AvgSINR = Math.Round(sumSINR / numSINR, 2);
                    cellInfo.SmallSINRNum = smallSINRCount;
                }
                occupyFile.CellInfoList.Add(cellInfo);
                cellInfo.Sn = occupyFile.CellInfoList.Count + 1000;
            }
        }

        protected override void fireShowForm()
        {
            ZTMainCellLastOccupyInfoForm form = MainModel.GetObjectFromBlackboard(typeof(ZTMainCellLastOccupyInfoForm)) as ZTMainCellLastOccupyInfoForm;
            if (form == null || form.IsDisposed)
            {
                form = new ZTMainCellLastOccupyInfoForm();
            }
            form.FillData(occupyFileList, baseParamName);
            form.Show(MainModel.MainForm);
        }
    }

    public class ZTMainCellLastOccupySetCondition
    {
        public float RSRP { get; set; }
        public float SINR { get; set; }
        public short RxLev { get; set; }
        public byte RxQul { get; set; }
        public ZTMainCellLastOccupySetCondition()
        {
            RSRP = -110;
            SINR = -3;
            RxLev = -90;
            RxQul = 5;
        }
    }

    public class ParamName
    {
        public string RxlevName { get; set; }
        public string RxqualName { get; set; }
        public RangeSet RangeSetRxlev { get; set; }
        public RangeSet RangeSetRxqual { get; set; }

        public ParamName(string rxlevName, string rxqualName)
        {
            this.RxlevName = rxlevName;
            this.RxqualName = rxqualName;

            RangeSetRxlev = new RangeSet();
            RangeSetRxqual = new RangeSet();
        }
    }

    public class CellOccupyFileInfo
    {
        public int Sn { get; set; }
        public FileInfo File { get; set; }
        public string FileName
        {
            get { return File.Name; }
        }
        public List<CellOccupyCellInfo> CellInfoList { get; set; }

        public CellOccupyFileInfo(FileInfo file)
        {
            this.File = file;
            this.CellInfoList = new List<CellOccupyCellInfo>();
        }
    }

    public class CellOccupyCellInfo
    {
        public int Sn { get; set; }
        public DTFileDataManager DTFile { get; set; }
        public double AvgRSRP { get; set; }
        public double AvgSINR { get; set; }
        public int SmallRSRPNum { get; set; }
        public int SmallSINRNum { get; set; }
        public TimePeriodPoints TpPnt { get; set; }
        public List<TestPoint> TestPntList { get; set; } = new List<TestPoint>();

        private string strEventName;
        public string StrEventName { get { return strEventName; } }

        public string CellName
        {
            get
            {
                string desc = "-";
                if (StrEventName.ToUpper().Contains("HANDOVER"))
                    desc = "->";
                else if (StrEventName.ToUpper().Contains("RESELECTION"))
                    desc = "=>";

                string targetName = "";

                targetName = TpPnt.eEnd.CellNameTarget.Trim();
                if (targetName == "")
                    targetName = string.Format("{0}_{1}", LACTar, CITar);
                return string.Format("{0} {1} {2}", TpPnt.eEnd.CellNameSrc, desc, targetName);
            }
        }

        public bool IsContains(string name, Array a)
        {
            foreach (EEventGSMID e in a)
            {
                if (e.ToString() == name)
                {
                    return true;
                }
            }
            return false;
        }

        public string LACSrc
        {
            get
            {
                return TpPnt.eEnd["LAC"].ToString();
            }
        }

        public string CISrc
        {
            get
            {
                return TpPnt.eEnd["CI"].ToString();
            }
        }

        public string LACTar
        {
            get
            {
                return TpPnt.eEnd["TargetLAC"].ToString();
            }
        }

        public string CITar
        {
            get
            {
                return TpPnt.eEnd["TargetCI"].ToString();
            }
        }

        public string Duration { get { return Math.Round(TpPnt.eEnd.DateTime.Subtract(TpPnt.eStart.DateTime).TotalSeconds, 2).ToString(); } }

        public double Longitude { get { return TpPnt.eStart.Longitude; } }

        public double Latitude { get { return TpPnt.eStart.Latitude; } }

        public string Time { get { return TpPnt.eStart.DateTime.ToString(); } }

        public CellOccupyCellInfo(TimePeriodPoints TpPnt)
        {
            this.TpPnt = TpPnt;
            SearchEventName();
        }

        private void SearchEventName()
        {
            foreach (EEventGSMID e in Enum.GetValues(typeof(EEventGSMID)))
            {
                if (TpPnt.eStart.ID == (int)e)
                {
                    strEventName = e.ToString();
                    return;
                }
            }
            foreach (EEventTDID e in Enum.GetValues(typeof(EEventTDID)))
            {
                if (TpPnt.eStart.ID == (int)e)
                {
                    strEventName = e.ToString();
                    return;
                }
            }
            foreach (EEventLTEID e in Enum.GetValues(typeof(EEventLTEID)))
            {
                if (TpPnt.eStart.ID == (int)e)
                {
                    strEventName = e.ToString();
                    return;
                }
            }
        }

        public void AddPnt(TestPoint tp)
        {
            TestPntList.Add(tp);
        }

        public string GetResult(MasterCom.RAMS.Chris.Util.Range range, string param)
        {
            double duration = 0;
            DateTime startDt = TpPnt.eStart.DateTime;
            foreach (TestPoint tp in TestPntList)
            {
                double dvalue = GetValue(tp, param);

                if (range.Contains(dvalue))
                {
                    duration += tp.DateTime.Subtract(startDt).TotalSeconds;
                }
                startDt = tp.DateTime;
            }
            return Math.Round(duration, 2).ToString();
        }

        private double GetValue(TestPoint tp, string param)
        {
            double dvalue = double.NaN;
            switch (param)
            {
                case "RxLevSub":
                    short? rxlev = (short?)tp[param];
                    if (rxlev != null)
                        dvalue = (double)rxlev;
                    break;
                case "RxQualSub":
                    byte? rxqual = (byte?)tp[param];
                    if (rxqual != null)
                        dvalue = (double)rxqual;
                    break;
                case "TD_PCCPCH_RSCP":
                    float? rscp = (float?)tp["TD_PCCPCH_RSCP"];
                    if (rscp != null)
                        dvalue = (double)rscp;
                    break;
                case "TD_PCCPCH_C2I":
                    int? c2i = (int?)tp["TD_PCCPCH_C2I"];
                    if (c2i != null)
                    {
                        dvalue = (double)c2i;
                    }
                    break;
                case "lte_RSRP":
                    float? rsrp = (float?)tp["lte_RSRP"];
                    if (rsrp != null)
                        dvalue = (double)rsrp;
                    break;
                case "lte_SINR":
                    float? sinr = (float?)tp["lte_SINR"];
                    if (sinr != null)
                    {
                        dvalue = (double)sinr;
                    }
                    break;
                default:
                    break;
            }
            return dvalue;
        }
    }
}
