﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Stat;
using MasterCom.ES.Data;
using MasterCom.RAMS.Model.Interface;
using MasterCom.MTGis;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Func.SystemSetting;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTCellCoverLapByRegion_GScan : DIYAnalyseByCellBackgroundBaseByPeriod
    {
        private static ZTCellCoverLapByRegion_GScan intance = null;
        protected static readonly object lockObj = new object();
        public static ZTCellCoverLapByRegion_GScan GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new ZTCellCoverLapByRegion_GScan();
                    }
                }
            }
            return intance;
        }

        protected ZTCellCoverLapByRegion_GScan()
            : base(MainModel.GetInstance())
        {
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.GSM_SCAN);
            carrierID = CarrierType.ChinaMobile;
        }
        public override string Name
        {
            get { return "扫频过覆盖分析(按区域)"; }
        }
        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 15000, 15002, this.Name);
        }

        protected override bool isValidTestPoint(TestPoint tp)
        {
            // 无效点过滤
            if (!Condition.Geometorys.GeoOp.Contains(tp.Longitude, tp.Latitude))
            {
                return false;
            }
            if (!(tp is ScanTestPoint_G))
            {
                return false;
            }

            // 遍历TOP12
            float? maxRxlev = null;
            CellCoverLap topCellLap = null;
            for (int i = 0; i < 12; ++i)
            {
                float? curRxlev = (float?)tp["GSCAN_RxLev", i];
                if (curRxlev == null || curRxlev <= curFilterRxlev)
                {
                    break;
                }

                // 得到一个小区
                CellCoverLap curCellLap = GetCellByFreq(tp, i);
                if (curCellLap == null)
                {
                    break;
                }
                double dis = curCellLap.cell.GetDistance(tp.Longitude, tp.Latitude);
                if (dis > this.disFactor * curCellLap._CellCovRadius) // 存在一个过覆盖点
                {
                    curCellLap.AddBadSample(tp, dis, (float)curRxlev);
                }
                else
                {
                    ++curCellLap.goodSampleCount;
                }
                AddToRetDic(curCellLap);

                // 如果是top1
                if (i == 0)
                {
                    maxRxlev = curRxlev;
                    topCellLap = curCellLap;
                    curCellLap.isMainCell = true;
                    continue;
                }

                // 非top1，计算其对top1的干扰次数
                curCellLap.isMainCell = false;
                if (topCellLap != null)
                {
                    topCellLap.InterfereCount += CalcInterfereCount(topCellLap, maxRxlev, curCellLap, curRxlev);
                }
            }

            return maxRxlev != null;
        }

        private CellCoverLap GetCellByFreq(TestPoint tp, int index)
        {
            Cell cell = tp.GetCell_GSMScan(index);
            if (cell == null)
            {
                return null;
            }

            if (cell.BCCH >= 1 && cell.BCCH < 125 && cellLapRetDic900.ContainsKey(cell.Name))
            {
                return cellLapRetDic900[cell.Name];
            }
            else if (cell.BCCH >= 512 && cell.BCCH <= 1025 && cellLapRetDic1800.ContainsKey(cell.Name))
            {
                return cellLapRetDic1800[cell.Name];
            }

            CellCoverLap covLap = new CellCoverLap();
            covLap._CellCovRadius = CfgDataProvider.CalculateRadius(cell, nearestCellCount,false);
            covLap.rationalDistance = covLap._CellCovRadius * this.disFactor;
            covLap.cell = cell;
            covLap.mnger = new DTDataManager(MainModel.GetInstance());
            return covLap;
        }

        private void AddToRetDic(CellCoverLap cellLap)
        {
            if (cellLap.cell.BCCH >= 1 && cellLap.cell.BCCH < 125 && !cellLapRetDic900.ContainsKey(cellLap.cell.Name))
            {
                cellLapRetDic900[cellLap.cell.Name] = cellLap;
            }
            else if (cellLap.cell.BCCH >= 512 && cellLap.cell.BCCH <= 1025 && !cellLapRetDic1800.ContainsKey(cellLap.cell.Name))
            {
                cellLapRetDic1800[cellLap.cell.Name] = cellLap;
            }
            // else do nothing
        }

        private int CalcInterfereCount(CellCoverLap topLap, float? maxRxlev, CellCoverLap curLap, float? curRxlev)
        {
            int interCnt = 0;

            bool[] mainFreq = new bool[2000];
            mainFreq[topLap.cell.BCCH] = true;
            foreach (int freq in topLap.cell.TCH)
            {
                mainFreq[freq] = true;
            }

            List<short> curFreq = new List<short>(curLap.cell.TCH);
            curFreq.Add(curLap.cell.BCCH);
            foreach (short freq in curFreq)
            {
                if (mainFreq[freq] && maxRxlev - curRxlev < this.sameFrq)
                {
                    ++interCnt;
                }
                if (freq - 1 >= 0 && mainFreq[freq - 1] && maxRxlev - curRxlev < this.adjacentFrq)
                {
                    ++interCnt;
                }
                if (freq + 1 < 2000 && mainFreq[freq + 1] && maxRxlev - curRxlev < this.adjacentFrq)
                {
                    ++interCnt;
                }
            }

            return interCnt;
        }

        public CellCoverLapCondition SettingCondition { get; set; } = new CellCoverLapCondition();
        private Dictionary<string, CellCoverLap> cellLapRetDic900 { get; set; } = new Dictionary<string, CellCoverLap>();
        private Dictionary<string, CellCoverLap> cellLapRetDic1800 { get; set; } = new Dictionary<string, CellCoverLap>();
        public int curFilterRxlev { get; set; } = -140;
        public int curMinSampleCount { get; set; } = 0;
        public float curMinPercent { get; set; } = 0;
        public int curMinDistance { get; set; } = 0;
        public int curMaxDistance { get; set; } = 1000000;
        public float disFactor { get; set; } = 1.6f;  
        public int nearestCellCount { get; set; } = 3;

        public int sameFrq { get; set; } = 9;
        public int adjacentFrq { get; set; } = -9;

        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            List<object> columnsDef = new List<object>();
            Dictionary<string, object> param = new Dictionary<string, object>();
            param["param_name"] = "GSCAN_RxLev";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "GSCAN_BCCH";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "GSCAN_BSIC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            Dictionary<string, object> tmpDic = new Dictionary<string, object>();
            tmpDic.Add("name", (object)">GSM扫频");
            tmpDic.Add("themeName", (object)"GSM_SCAN_RxLev");
            tmpDic.Add("columnsDef", (object)columnsDef);

            DIYSampleGroup group = new DIYSampleGroup();
            group.Param = tmpDic;
            return group;
        }

        ZTCellCoverLapSetForm fDlg = null;
        protected override bool getConditionBeforeQuery()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }
            if (fDlg == null)
            {
                fDlg = new ZTCellCoverLapSetForm(this.GetType());
                fDlg.SetSameNbhNumVisible(true);
            }
            if (fDlg.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            fDlg.GetSettingFilterRet(SettingCondition);
            curFilterRxlev = SettingCondition.CurFilterRxlev;
            curMinSampleCount = SettingCondition.CurMinSampleCount;
            curMinPercent = SettingCondition.CurMinPercent;
            curMinDistance = SettingCondition.CurMinDistance;
            curMaxDistance = SettingCondition.CurMaxDistance;
            nearestCellCount = SettingCondition.NearestCellCount;
            disFactor = SettingCondition.DisFactor;
            int iSameFrq, iAdjacentFrq;
            fDlg.GetSameNbhNumValue(out iSameFrq, out iAdjacentFrq);
            sameFrq = iSameFrq;
            adjacentFrq = iAdjacentFrq;
            return true;
        }

        protected override void FireShowFormAfterQuery()
        {
            object obj = MainModel.GetInstance().GetObjectFromBlackboard(typeof(ZTCellCoverLapListForm_GScan).FullName);
            ZTCellCoverLapListForm_GScan ztGsmScanCellCovLapSetForm = obj == null ? null : obj as ZTCellCoverLapListForm_GScan;
            if (ztGsmScanCellCovLapSetForm == null || ztGsmScanCellCovLapSetForm.IsDisposed)
            {
                ztGsmScanCellCovLapSetForm = new ZTCellCoverLapListForm_GScan(MainModel);
            }
            ztGsmScanCellCovLapSetForm.showCellSet(cellLapRetDic900, cellLapRetDic1800, curMinSampleCount,
                curMinPercent, curMinDistance, curMaxDistance);
            if (!ztGsmScanCellCovLapSetForm.Visible)
            {
                ztGsmScanCellCovLapSetForm.Show(MainModel.MainForm);
            }
        }

        protected override void doSomethingBeforeQueryInThread()
        {
            cellLapRetDic900.Clear();
            cellLapRetDic1800.Clear();
        }

        protected override void getResultAfterQuery()
        {
            FilterCellCoverLap();
        }

        private void FilterCellCoverLap()
        {
            List<string> filterCells900 = getFilterCells(cellLapRetDic900);
            List<string> filterCells1800 = getFilterCells(cellLapRetDic1800);
            foreach (string key in filterCells900)
            {
                cellLapRetDic900.Remove(key);
            }
            foreach (string key in filterCells1800)
            {
                cellLapRetDic1800.Remove(key);
            }

            MainModel.DTDataManager.Clear();
            DTDataManager newManager = new DTDataManager(MainModel);
            foreach (CellCoverLap ccl in cellLapRetDic900.Values)
            {
                addManagerTP(newManager, ccl);
            }
            foreach (CellCoverLap ccl in cellLapRetDic1800.Values)
            {
                addManagerTP(newManager, ccl);
            }
            MainModel.DTDataManager = newManager;
        }

        private void addManagerTP(DTDataManager newManager, CellCoverLap ccl)
        {
            if (ccl.mnger != null)
            {
                foreach (DTFileDataManager fmnger in ccl.mnger.FileDataManagers)
                {
                    foreach (TestPoint tp in fmnger.TestPoints)
                    {
                        newManager.Add(tp);
                    }
                }
            }
        }

        private List<string> getFilterCells(Dictionary<string, CellCoverLap> dic)
        {
            List<string> filterCells = new List<string>();
            foreach (KeyValuePair<string, CellCoverLap> keyValue in dic)
            {
                CellCoverLap cellLap = keyValue.Value;
                cellLap.GetResult();

                if (!(cellLap.badSampleCount > 0 && cellLap._CellCovRadius > 50
                     && cellLap.TotalSampleCount >= curMinSampleCount
                     && cellLap.BadSamplePercent >= curMinPercent
                     && cellLap.MeanBadDistance >= curMinDistance
                     && cellLap.MeanBadDistance <= curMaxDistance
                     ))
                {
                    filterCells.Add(keyValue.Key);
                }
            }
            return filterCells;
        }

        #region Background
        public override BackgroundFuncType FuncType
        {
            get { return BackgroundFuncType.GSM扫频; }
        }

        public override BackgroundSubFuncType SubFuncType
        {
            get { return BackgroundSubFuncType.覆盖; }
        }

        public override Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["BackgroundStat"] = BackgroundStat;
                param["FilterRxlev"] = curFilterRxlev;
                param["NearestCellCount"] = nearestCellCount;
                param["DisFactor"] = disFactor;
                param["SameFreq"] = sameFrq;
                param["AdjFreq"] = adjacentFrq;
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                if (param.ContainsKey("BackgroundStat"))
                {
                    BackgroundStat = (bool)param["BackgroundStat"];
                }
                if (param.ContainsKey("FilterRxlev"))
                {
                    curFilterRxlev = int.Parse(param["FilterRxlev"].ToString());
                }
                if (param.ContainsKey("NearestCellCount"))
                {
                    nearestCellCount = int.Parse(param["NearestCellCount"].ToString());
                }
                if (param.ContainsKey("DisFactor"))
                {
                    disFactor = float.Parse(param["DisFactor"].ToString());
                }
                if (param.ContainsKey("SameFreq"))
                {
                    sameFrq = int.Parse(param["SameFreq"].ToString());
                }
                if (param.ContainsKey("AdjFreq"))
                {
                    adjacentFrq = int.Parse(param["AdjFreq"].ToString());
                }
            }
        }

        public override PropertiesControl Properties
        {
            get
            {
                return new CoverLapProperties_GScan(this);
            }
        }

        protected override void saveBackgroundData()
        {
            List<BackgroundResult> bgResultList = new List<BackgroundResult>();
            foreach (string key in cellLapRetDic900.Keys)
            {
                CellCoverLap ccl = cellLapRetDic900[key];
                BackgroundResult result = ccl.ConvertToBackgroundResult();
                result.SubFuncID = GetSubFuncID();
                bgResultList.Add(result);
            }
            foreach (string key in cellLapRetDic1800.Keys)
            {
                CellCoverLap ccl = cellLapRetDic1800[key];
                BackgroundResult result = ccl.ConvertToBackgroundResult();
                result.SubFuncID = GetSubFuncID();
                bgResultList.Add(result);
            }
            BackgroundFuncQueryManager.GetInstance().SaveResult_Cell(GetSubFuncID(), Condition.Periods[0].IBeginTime,
                Condition.Periods[0].IEndTime, bgResultList);
        }

        protected override void initBackgroundImageDesc()
        {
            foreach (BackgroundResult bgResult in BackgroundResultList)
            {
                int badSampleCount = bgResult.GetImageValueInt();
                int goodSampleCount = bgResult.GetImageValueInt();
                float MeanBadDistance = bgResult.GetImageValueFloat();
                float maxBadDistance = bgResult.GetImageValueFloat();
                float minBadDistance = bgResult.GetImageValueFloat();
                bgResult.GetImageValueFloat();//_CellCovRadius
                float rationalDistance = bgResult.GetImageValueFloat();
                StringBuilder sb = new StringBuilder();
                sb.Append("总采样点数：");
                sb.Append(badSampleCount + goodSampleCount);
                sb.Append("\r\n");
                sb.Append("异常采样点数：");
                sb.Append(badSampleCount);
                sb.Append("\r\n");
                sb.Append("异常采样点比例：");
                sb.Append(Math.Round(100.0 * badSampleCount / (badSampleCount + goodSampleCount), 2));
                sb.Append("%");
                sb.Append("\r\n");
                sb.Append("平均过覆盖距离：");
                sb.Append(MeanBadDistance);
                sb.Append("\r\n");
                sb.Append("最大过覆盖距离：");
                sb.Append(maxBadDistance);
                sb.Append("\r\n");
                sb.Append("最小过覆盖距离：");
                sb.Append(minBadDistance);
                sb.Append("\r\n");
                sb.Append("合理覆盖距离：");
                sb.Append(rationalDistance);
                bgResult.ImageDesc = sb.ToString();
            }
        }
        #endregion
    }

}
