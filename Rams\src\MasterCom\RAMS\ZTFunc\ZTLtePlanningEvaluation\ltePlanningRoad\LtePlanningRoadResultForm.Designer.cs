﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class LtePlanningRoadResultForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.tabControl = new System.Windows.Forms.TabControl();
            this.contextMenuStrip1 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExportCurXls = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportAllXls = new System.Windows.Forms.ToolStripMenuItem();
            this.tabPage1 = new System.Windows.Forms.TabPage();
            this.gcWeakCoverage = new DevExpress.XtraGrid.GridControl();
            this.gvWeakCoverage = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn6 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn7 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn8 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn9 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn10 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn11 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.tabPage2 = new System.Windows.Forms.TabPage();
            this.gcMultiCoverage = new DevExpress.XtraGrid.GridControl();
            this.gvMultiCoverage = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn12 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn13 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn14 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn15 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn16 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn17 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn18 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn19 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn22 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.miExportCurShp = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportAllShp = new System.Windows.Forms.ToolStripMenuItem();
            this.tabControl.SuspendLayout();
            this.contextMenuStrip1.SuspendLayout();
            this.tabPage1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gcWeakCoverage)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvWeakCoverage)).BeginInit();
            this.tabPage2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gcMultiCoverage)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvMultiCoverage)).BeginInit();
            this.SuspendLayout();
            // 
            // tabControl
            // 
            this.tabControl.ContextMenuStrip = this.contextMenuStrip1;
            this.tabControl.Controls.Add(this.tabPage1);
            this.tabControl.Controls.Add(this.tabPage2);
            this.tabControl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabControl.Location = new System.Drawing.Point(0, 0);
            this.tabControl.Name = "tabControl";
            this.tabControl.SelectedIndex = 0;
            this.tabControl.Size = new System.Drawing.Size(968, 508);
            this.tabControl.TabIndex = 0;
            // 
            // contextMenuStrip1
            // 
            this.contextMenuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExportCurXls,
            this.miExportAllXls,
            this.miExportCurShp,
            this.miExportAllShp});
            this.contextMenuStrip1.Name = "contextMenuStrip1";
            this.contextMenuStrip1.Size = new System.Drawing.Size(154, 114);
            // 
            // miExportCurXls
            // 
            this.miExportCurXls.Name = "miExportCurXls";
            this.miExportCurXls.Size = new System.Drawing.Size(153, 22);
            this.miExportCurXls.Text = "导出当前Excel";
            // 
            // miExportAllXls
            // 
            this.miExportAllXls.Name = "miExportAllXls";
            this.miExportAllXls.Size = new System.Drawing.Size(153, 22);
            this.miExportAllXls.Text = "导出全部Excel";
            // 
            // tabPage1
            // 
            this.tabPage1.Controls.Add(this.gcWeakCoverage);
            this.tabPage1.Location = new System.Drawing.Point(4, 23);
            this.tabPage1.Name = "tabPage1";
            this.tabPage1.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage1.Size = new System.Drawing.Size(960, 481);
            this.tabPage1.TabIndex = 0;
            this.tabPage1.Text = "弱覆盖路段";
            this.tabPage1.UseVisualStyleBackColor = true;
            // 
            // gcWeakCoverage
            // 
            this.gcWeakCoverage.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gcWeakCoverage.EmbeddedNavigator.Buttons.Append.Enabled = false;
            this.gcWeakCoverage.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gcWeakCoverage.EmbeddedNavigator.Buttons.CancelEdit.Enabled = false;
            this.gcWeakCoverage.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gcWeakCoverage.EmbeddedNavigator.Buttons.Edit.Enabled = false;
            this.gcWeakCoverage.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gcWeakCoverage.EmbeddedNavigator.Buttons.EndEdit.Enabled = false;
            this.gcWeakCoverage.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gcWeakCoverage.EmbeddedNavigator.Buttons.NextPage.Visible = false;
            this.gcWeakCoverage.EmbeddedNavigator.Buttons.PrevPage.Visible = false;
            this.gcWeakCoverage.EmbeddedNavigator.Buttons.Remove.Visible = false;
            this.gcWeakCoverage.Location = new System.Drawing.Point(3, 3);
            this.gcWeakCoverage.MainView = this.gvWeakCoverage;
            this.gcWeakCoverage.Name = "gcWeakCoverage";
            this.gcWeakCoverage.ShowOnlyPredefinedDetails = true;
            this.gcWeakCoverage.Size = new System.Drawing.Size(954, 475);
            this.gcWeakCoverage.TabIndex = 0;
            this.gcWeakCoverage.UseEmbeddedNavigator = true;
            this.gcWeakCoverage.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gvWeakCoverage});
            // 
            // gvWeakCoverage
            // 
            this.gvWeakCoverage.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn1,
            this.gridColumn2,
            this.gridColumn3,
            this.gridColumn4,
            this.gridColumn5,
            this.gridColumn6,
            this.gridColumn7,
            this.gridColumn8,
            this.gridColumn9,
            this.gridColumn10,
            this.gridColumn11});
            this.gvWeakCoverage.GridControl = this.gcWeakCoverage;
            this.gvWeakCoverage.Name = "gvWeakCoverage";
            this.gvWeakCoverage.OptionsBehavior.Editable = false;
            this.gvWeakCoverage.OptionsDetail.ShowDetailTabs = false;
            this.gvWeakCoverage.OptionsView.ShowGroupPanel = false;
            this.gvWeakCoverage.OptionsView.ShowIndicator = false;
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "道路名";
            this.gridColumn1.FieldName = "RoadName";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.Visible = true;
            this.gridColumn1.VisibleIndex = 0;
            // 
            // gridColumn2
            // 
            this.gridColumn2.Caption = "总采样点";
            this.gridColumn2.FieldName = "SampleCount";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.Visible = true;
            this.gridColumn2.VisibleIndex = 1;
            // 
            // gridColumn3
            // 
            this.gridColumn3.Caption = "弱覆盖点";
            this.gridColumn3.FieldName = "WeakCoverageSampleCount";
            this.gridColumn3.Name = "gridColumn3";
            this.gridColumn3.Visible = true;
            this.gridColumn3.VisibleIndex = 2;
            // 
            // gridColumn4
            // 
            this.gridColumn4.Caption = "弱覆盖占比";
            this.gridColumn4.DisplayFormat.FormatString = "P2";
            this.gridColumn4.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn4.FieldName = "WeakCoverageSampleRate";
            this.gridColumn4.Name = "gridColumn4";
            this.gridColumn4.Visible = true;
            this.gridColumn4.VisibleIndex = 3;
            // 
            // gridColumn5
            // 
            this.gridColumn5.Caption = "持续时间(秒)";
            this.gridColumn5.FieldName = "LastTime";
            this.gridColumn5.Name = "gridColumn5";
            this.gridColumn5.Visible = true;
            this.gridColumn5.VisibleIndex = 4;
            // 
            // gridColumn6
            // 
            this.gridColumn6.Caption = "路段长度(米)";
            this.gridColumn6.DisplayFormat.FormatString = "F2";
            this.gridColumn6.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn6.FieldName = "Length";
            this.gridColumn6.Name = "gridColumn6";
            this.gridColumn6.Visible = true;
            this.gridColumn6.VisibleIndex = 5;
            // 
            // gridColumn7
            // 
            this.gridColumn7.Caption = "中心经度";
            this.gridColumn7.FieldName = "CentLng";
            this.gridColumn7.Name = "gridColumn7";
            this.gridColumn7.Visible = true;
            this.gridColumn7.VisibleIndex = 6;
            // 
            // gridColumn8
            // 
            this.gridColumn8.Caption = "中心纬度";
            this.gridColumn8.FieldName = "CentLat";
            this.gridColumn8.Name = "gridColumn8";
            this.gridColumn8.Visible = true;
            this.gridColumn8.VisibleIndex = 7;
            // 
            // gridColumn9
            // 
            this.gridColumn9.Caption = "中心区站点数";
            this.gridColumn9.FieldName = "CenterBtsCount";
            this.gridColumn9.Name = "gridColumn9";
            this.gridColumn9.Visible = true;
            this.gridColumn9.VisibleIndex = 8;
            // 
            // gridColumn10
            // 
            this.gridColumn10.Caption = "中心区站点名";
            this.gridColumn10.FieldName = "CenterBtsName";
            this.gridColumn10.Name = "gridColumn10";
            this.gridColumn10.Visible = true;
            this.gridColumn10.VisibleIndex = 9;
            // 
            // gridColumn11
            // 
            this.gridColumn11.Caption = "文件名";
            this.gridColumn11.FieldName = "FileName";
            this.gridColumn11.Name = "gridColumn11";
            this.gridColumn11.Visible = true;
            this.gridColumn11.VisibleIndex = 10;
            // 
            // tabPage2
            // 
            this.tabPage2.Controls.Add(this.gcMultiCoverage);
            this.tabPage2.Location = new System.Drawing.Point(4, 23);
            this.tabPage2.Name = "tabPage2";
            this.tabPage2.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage2.Size = new System.Drawing.Size(960, 481);
            this.tabPage2.TabIndex = 1;
            this.tabPage2.Text = "重叠覆盖路段";
            this.tabPage2.UseVisualStyleBackColor = true;
            // 
            // gcMultiCoverage
            // 
            this.gcMultiCoverage.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gcMultiCoverage.EmbeddedNavigator.Buttons.Append.Enabled = false;
            this.gcMultiCoverage.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gcMultiCoverage.EmbeddedNavigator.Buttons.CancelEdit.Enabled = false;
            this.gcMultiCoverage.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gcMultiCoverage.EmbeddedNavigator.Buttons.Edit.Enabled = false;
            this.gcMultiCoverage.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gcMultiCoverage.EmbeddedNavigator.Buttons.EndEdit.Enabled = false;
            this.gcMultiCoverage.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gcMultiCoverage.EmbeddedNavigator.Buttons.NextPage.Visible = false;
            this.gcMultiCoverage.EmbeddedNavigator.Buttons.PrevPage.Visible = false;
            this.gcMultiCoverage.EmbeddedNavigator.Buttons.Remove.Visible = false;
            this.gcMultiCoverage.Location = new System.Drawing.Point(3, 3);
            this.gcMultiCoverage.MainView = this.gvMultiCoverage;
            this.gcMultiCoverage.Name = "gcMultiCoverage";
            this.gcMultiCoverage.ShowOnlyPredefinedDetails = true;
            this.gcMultiCoverage.Size = new System.Drawing.Size(954, 475);
            this.gcMultiCoverage.TabIndex = 1;
            this.gcMultiCoverage.UseEmbeddedNavigator = true;
            this.gcMultiCoverage.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gvMultiCoverage});
            // 
            // gvMultiCoverage
            // 
            this.gvMultiCoverage.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn12,
            this.gridColumn13,
            this.gridColumn14,
            this.gridColumn15,
            this.gridColumn16,
            this.gridColumn17,
            this.gridColumn18,
            this.gridColumn19,
            this.gridColumn22});
            this.gvMultiCoverage.GridControl = this.gcMultiCoverage;
            this.gvMultiCoverage.Name = "gvMultiCoverage";
            this.gvMultiCoverage.OptionsBehavior.Editable = false;
            this.gvMultiCoverage.OptionsDetail.ShowDetailTabs = false;
            this.gvMultiCoverage.OptionsView.ShowGroupPanel = false;
            this.gvMultiCoverage.OptionsView.ShowIndicator = false;
            // 
            // gridColumn12
            // 
            this.gridColumn12.Caption = "道路名";
            this.gridColumn12.FieldName = "RoadName";
            this.gridColumn12.Name = "gridColumn12";
            this.gridColumn12.Visible = true;
            this.gridColumn12.VisibleIndex = 0;
            // 
            // gridColumn13
            // 
            this.gridColumn13.Caption = "总采样点";
            this.gridColumn13.FieldName = "SampleCount";
            this.gridColumn13.Name = "gridColumn13";
            this.gridColumn13.Visible = true;
            this.gridColumn13.VisibleIndex = 1;
            // 
            // gridColumn14
            // 
            this.gridColumn14.Caption = "重叠覆盖点";
            this.gridColumn14.FieldName = "MultiCoverageSampleCount";
            this.gridColumn14.Name = "gridColumn14";
            this.gridColumn14.Visible = true;
            this.gridColumn14.VisibleIndex = 2;
            // 
            // gridColumn15
            // 
            this.gridColumn15.Caption = "重叠覆盖占比";
            this.gridColumn15.DisplayFormat.FormatString = "P2";
            this.gridColumn15.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn15.FieldName = "MultiCoverageSampleRate";
            this.gridColumn15.Name = "gridColumn15";
            this.gridColumn15.Visible = true;
            this.gridColumn15.VisibleIndex = 3;
            // 
            // gridColumn16
            // 
            this.gridColumn16.Caption = "持续时间(秒)";
            this.gridColumn16.FieldName = "LastTime";
            this.gridColumn16.Name = "gridColumn16";
            this.gridColumn16.Visible = true;
            this.gridColumn16.VisibleIndex = 4;
            // 
            // gridColumn17
            // 
            this.gridColumn17.Caption = "路段长度(米)";
            this.gridColumn17.DisplayFormat.FormatString = "F2";
            this.gridColumn17.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn17.FieldName = "Length";
            this.gridColumn17.Name = "gridColumn17";
            this.gridColumn17.Visible = true;
            this.gridColumn17.VisibleIndex = 5;
            // 
            // gridColumn18
            // 
            this.gridColumn18.Caption = "中心经度";
            this.gridColumn18.FieldName = "CentLng";
            this.gridColumn18.Name = "gridColumn18";
            this.gridColumn18.Visible = true;
            this.gridColumn18.VisibleIndex = 6;
            // 
            // gridColumn19
            // 
            this.gridColumn19.Caption = "中心纬度";
            this.gridColumn19.FieldName = "CentLat";
            this.gridColumn19.Name = "gridColumn19";
            this.gridColumn19.Visible = true;
            this.gridColumn19.VisibleIndex = 7;
            // 
            // gridColumn22
            // 
            this.gridColumn22.Caption = "文件名";
            this.gridColumn22.FieldName = "FileName";
            this.gridColumn22.Name = "gridColumn22";
            this.gridColumn22.Visible = true;
            this.gridColumn22.VisibleIndex = 8;
            // 
            // miExportCurShp
            // 
            this.miExportCurShp.Name = "miExportCurShp";
            this.miExportCurShp.Size = new System.Drawing.Size(153, 22);
            this.miExportCurShp.Text = "导出当前图层";
            // 
            // miExportAllShp
            // 
            this.miExportAllShp.Name = "miExportAllShp";
            this.miExportAllShp.Size = new System.Drawing.Size(153, 22);
            this.miExportAllShp.Text = "导出全部图层";
            // 
            // LtePlanningRoadResultForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(968, 508);
            this.Controls.Add(this.tabControl);
            this.Name = "LtePlanningRoadResultForm";
            this.Text = "LTE路段评估结果";
            this.tabControl.ResumeLayout(false);
            this.contextMenuStrip1.ResumeLayout(false);
            this.tabPage1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gcWeakCoverage)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvWeakCoverage)).EndInit();
            this.tabPage2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gcMultiCoverage)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvMultiCoverage)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.TabControl tabControl;
        private System.Windows.Forms.TabPage tabPage1;
        private System.Windows.Forms.TabPage tabPage2;
        private DevExpress.XtraGrid.GridControl gcWeakCoverage;
        private DevExpress.XtraGrid.Views.Grid.GridView gvWeakCoverage;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn6;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn7;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn8;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn9;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn10;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn11;
        private DevExpress.XtraGrid.GridControl gcMultiCoverage;
        private DevExpress.XtraGrid.Views.Grid.GridView gvMultiCoverage;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn12;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn13;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn14;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn15;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn16;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn17;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn18;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn19;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn22;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip1;
        private System.Windows.Forms.ToolStripMenuItem miExportCurXls;
        private System.Windows.Forms.ToolStripMenuItem miExportAllXls;
        private System.Windows.Forms.ToolStripMenuItem miExportCurShp;
        private System.Windows.Forms.ToolStripMenuItem miExportAllShp;
    }
}