﻿using System;
using System.Collections.Generic;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.ZTFunc.ZTCellSet;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTDiyLteCellServiceConditionQuery : ZTDIYCellServiceConditionQuery
    {
        public Dictionary<string, Dictionary<string, LteCellServiceCondition>> regionLteCellServiceConditionsDic { get; set; } = new Dictionary<string, Dictionary<string, LteCellServiceCondition>>();

        private List<RegionLteCellServiceCondition> RegionLteCellServiceConditions = null;

        private static ZTDiyLteCellServiceConditionQuery instance = null;
        public static new ZTDiyLteCellServiceConditionQuery GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new ZTDiyLteCellServiceConditionQuery();
                    }
                }
            }
            return instance;
        }
        protected ZTDiyLteCellServiceConditionQuery()
        {
            this.IncludeMessage = true;
            this.Columns = new List<string>();
            Columns.Add("isampleid");
            Columns.Add("itime");
            Columns.Add("FileName");
            Columns.Add("ilongitude");
            Columns.Add("ilatitude");
            Columns.Add("lte_TAC");
            Columns.Add("lte_ECI");
            Columns.Add("lte_EARFCN");
            Columns.Add("lte_PCI");
            Columns.Add("RxLevSub");
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22056, this.Name);
        }

        protected override bool getCondition()
        {
            CellServiceConditionDlg dlg = new CellServiceConditionDlg();
            dlg.SetCondition(maxDelaySec);
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            dlg.GetCondition(out maxDelaySec);
            regionLteCellServiceConditionsDic = new Dictionary<string, Dictionary<string, LteCellServiceCondition>>();

            RegionLteCellServiceConditions = new List<RegionLteCellServiceCondition>();
            return RegionLteCellServiceConditions != null;
        }
        protected override void getReadyBeforeQuery()
        {
            regionLteCellServiceConditionsDic.Clear();
            regionNames.Clear();
        }

        protected override void fireShowForm()
        {
            WaitBox.Show("获取B、C类小区...", getCellServiceCondition);
            if (regionLteCellServiceConditionsDic == null || regionLteCellServiceConditionsDic.Count == 0)
            {
                MessageBox.Show("没有符合条件的数据。");
                return;
            }
            LteCellServiceConditionForm frm = MainModel.GetObjectFromBlackboard(typeof(LteCellServiceConditionForm)) as LteCellServiceConditionForm;
            if (frm == null || frm.IsDisposed)
            {
                frm = new LteCellServiceConditionForm(MainModel);
            }
            frm.FillData(RegionLteCellServiceConditions);
            frm.Owner = MainModel.MainForm;
            frm.Visible = true;
            frm.BringToFront();
            regionLteCellServiceConditionsDic = null;
        }

        protected override void judgeCell(ICell cell, ICell lastCell, TestPoint testPoint)
        {
            string regionName = getRegionName(testPoint.Longitude, testPoint.Latitude);
            if (regionLteCellServiceConditionsDic.ContainsKey(regionName))
            {
                if (regionLteCellServiceConditionsDic[regionName].ContainsKey(cell.Name))
                {
                    regionLteCellServiceConditionsDic[regionName][cell.Name].AddTestPoint(testPoint);
                }
                else
                {
                    LteCellServiceCondition lteCellServiceCondition = new LteCellServiceCondition(cell as LTECell, testPoint);
                    regionLteCellServiceConditionsDic[regionName][cell.Name] = lteCellServiceCondition;
                }
            }
            else
            {
                LteCellServiceCondition lteCellServiceCondition = new LteCellServiceCondition(cell as LTECell, testPoint);
                Dictionary<string, LteCellServiceCondition> cellDic = new Dictionary<string, LteCellServiceCondition>();
                cellDic[cell.Name] = lteCellServiceCondition;
                regionLteCellServiceConditionsDic[regionName] = cellDic;
                regionNames.Add(regionName);
            }
            if (cell != lastCell)
            {
                regionLteCellServiceConditionsDic[regionName][cell.Name].serviceTimes++;
            }
        }

        protected override void getCellServiceCondition()
        {
            try
            {
                List<LTECell> cells = MainModel.CellManager.GetCurrentLTECells();
                int iLoop = 0;
                foreach (string regionName in regionNames)
                {
                    Dictionary<string, LteCellServiceCondition> cellServiceConditionsDic = regionLteCellServiceConditionsDic[regionName];
                    List<string> cellNames = new List<string>();
                    List<string> cellCodes = new List<string>();
                    foreach (string cellName in cellServiceConditionsDic.Keys)
                    {
                        if (cellServiceConditionsDic[cellName].category == 0)
                        {
                            cellNames.Add(cellName);
                            cellCodes.Add(cellServiceConditionsDic[cellName].cell.Code);
                        }
                    }
                    foreach (LTECell cell in cells)
                    {
                        iLoop++;
                        if (cell.BelongBTS.Type != LTEBTSType.Indoor && !cellServiceConditionsDic.ContainsKey(cell.Name))
                        {
                            addOtherCellTestPoint(regionName, cellServiceConditionsDic, cellNames, cell);
                            WaitBox.ProgressPercent = (int)(100.8 * iLoop / cells.Count);
                        }
                    }
                }
                addRegionLteCellServiceConditions();
            }
            finally
            {
                WaitBox.Close();
            }
        }

        private void addOtherCellTestPoint(string regionName, Dictionary<string, LteCellServiceCondition> cellServiceConditionsDic, List<string> cellNames, LTECell cell)
        {
            for (int i = 0; i < cellNames.Count; i++)
            {
                string cellName = cellNames[i];

                LTECell otherCell = CellManager.GetInstance().GetLTECellLatest(cellName);
                if (otherCell != null && cell.BelongBTS.Name == otherCell.BelongBTS.Name)
                {
                    if (Math.Abs(cellServiceConditionsDic[cellName].cell.Direction - cell.Direction) <= maxDelaySec)
                    {
                        addTestPoint(cell, null, 1, regionName);
                    }
                    else
                    {
                        addTestPoint(cell, null, 2, regionName);
                    }
                }
            }
        }

        private void addRegionLteCellServiceConditions()
        {
            foreach (KeyValuePair<string, Dictionary<string, LteCellServiceCondition>> keyValue in regionLteCellServiceConditionsDic)
            {
                List<LteCellServiceCondition> lteCellServiceCondtionList = new List<LteCellServiceCondition>();
                foreach (KeyValuePair<string, LteCellServiceCondition> lteCellResult in keyValue.Value)
                {
                    lteCellServiceCondtionList.Add(lteCellResult.Value);
                }
                RegionLteCellServiceCondition regionLteCellServiceCondition = new RegionLteCellServiceCondition(keyValue.Key, lteCellServiceCondtionList);
                RegionLteCellServiceConditions.Add(regionLteCellServiceCondition);
            }
        }

        public virtual void addTestPoint(LTECell cell, TestPoint testPoint, int category, string regionName)
        {
            LteCellServiceCondition lteCellServiceCondition = new LteCellServiceCondition(cell, testPoint);
            lteCellServiceCondition.category = category;
            regionLteCellServiceConditionsDic[regionName][cell.Name] = lteCellServiceCondition;
        }
    }
    public class RegionLteCellServiceCondition
    {
        public RegionLteCellServiceCondition(string regionName, List<LteCellServiceCondition> cellList)
        {
            this.regionName = regionName;
            lteCellServiceConditions = cellList;
        }

        public string regionName { get; set; }
        public List<LteCellServiceCondition> lteCellServiceConditions { get; set; }
    }

    public class LteCellServiceCondition
    {
        public LteCellServiceCondition(LTECell cell, TestPoint testPoint)
        {
            this.cell = cell;
            AddTestPoint(testPoint);
        }

        public LteCellServiceCondition(string cellName, TestPoint testPoint)
        {
            this.cell = null;
            this.cellName = cellName;
            string[] s = cellName.Split(',');
            lac = s[0];
            ci = s[1];
            AddTestPoint(testPoint);
        }

        public LTECell cell { get; set; }
        public string cellName { get; set; }
        public string lac { get; set; }
        public string ci { get; set; }
        public int testPointCount { get; set; } = 0;
        public int serviceTimes { get; set; } = 0;
        public int category { get; set; } = 0;

        public double? ServiceSeconds
        {
            get
            {
                if (testPointCount == 0)
                {
                    return null;
                }
                return (double?)Math.Round(testPointCount * 480.0 / 1000.0, 2);
            }
        }

        public string Category
        {
            get
            {
                switch (category)
                {
                    case 0:
                        return "A";
                    case 1:
                        return "B";
                    case 2:
                        return "C";
                    default:
                        return "";
                }
            }
        }

        public void AddTestPoint(TestPoint testPoint)
        {
            if (testPoint == null)
            {
                return;
            }
            testPointCount++;
        }
    }
}
