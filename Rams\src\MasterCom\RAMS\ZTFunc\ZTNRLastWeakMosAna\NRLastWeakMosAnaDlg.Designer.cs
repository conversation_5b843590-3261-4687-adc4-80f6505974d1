﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class NRLastWeakMosAnaDlg
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.spinEditTPNum = new DevExpress.XtraEditors.SpinEdit();
            this.comboBoxMOS = new System.Windows.Forms.ComboBox();
            this.buttonCancel = new System.Windows.Forms.Button();
            this.label2 = new System.Windows.Forms.Label();
            this.buttonOK = new System.Windows.Forms.Button();
            this.label1 = new System.Windows.Forms.Label();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditTPNum.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // spinEditTPNum
            // 
            this.spinEditTPNum.EditValue = new decimal(new int[] {
            2,
            0,
            0,
            0});
            this.spinEditTPNum.Location = new System.Drawing.Point(101, 47);
            this.spinEditTPNum.Name = "spinEditTPNum";
            this.spinEditTPNum.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditTPNum.Properties.MaxValue = new decimal(new int[] {
            1000000,
            0,
            0,
            0});
            this.spinEditTPNum.Properties.MinValue = new decimal(new int[] {
            2,
            0,
            0,
            0});
            this.spinEditTPNum.Size = new System.Drawing.Size(136, 21);
            this.spinEditTPNum.TabIndex = 9;
            // 
            // comboBoxMOS
            // 
            this.comboBoxMOS.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.comboBoxMOS.FormattingEnabled = true;
            this.comboBoxMOS.Location = new System.Drawing.Point(101, 14);
            this.comboBoxMOS.Name = "comboBoxMOS";
            this.comboBoxMOS.Size = new System.Drawing.Size(135, 22);
            this.comboBoxMOS.TabIndex = 7;
            // 
            // buttonCancel
            // 
            this.buttonCancel.Location = new System.Drawing.Point(194, 104);
            this.buttonCancel.Name = "buttonCancel";
            this.buttonCancel.Size = new System.Drawing.Size(87, 27);
            this.buttonCancel.TabIndex = 8;
            this.buttonCancel.Text = "取消";
            this.buttonCancel.UseVisualStyleBackColor = true;
            this.buttonCancel.Click += new System.EventHandler(this.buttonCancel_Click);
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(15, 50);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(68, 14);
            this.label2.TabIndex = 5;
            this.label2.Text = "采样点数 ≥";
            // 
            // buttonOK
            // 
            this.buttonOK.Location = new System.Drawing.Point(99, 104);
            this.buttonOK.Name = "buttonOK";
            this.buttonOK.Size = new System.Drawing.Size(87, 27);
            this.buttonOK.TabIndex = 6;
            this.buttonOK.Text = "确定";
            this.buttonOK.UseVisualStyleBackColor = true;
            this.buttonOK.Click += new System.EventHandler(this.buttonOK_Click);
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(28, 17);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(57, 14);
            this.label1.TabIndex = 4;
            this.label1.Text = "MOS值 ≤";
            // 
            // NRLastWeakMosAnaDlg
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(295, 150);
            this.Controls.Add(this.spinEditTPNum);
            this.Controls.Add(this.comboBoxMOS);
            this.Controls.Add(this.buttonCancel);
            this.Controls.Add(this.label2);
            this.Controls.Add(this.buttonOK);
            this.Controls.Add(this.label1);
            this.Name = "NRLastWeakMosAnaDlg";
            this.Text = "持续弱MOS事件";
            ((System.ComponentModel.ISupportInitialize)(this.spinEditTPNum.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraEditors.SpinEdit spinEditTPNum;
        private System.Windows.Forms.ComboBox comboBoxMOS;
        private System.Windows.Forms.Button buttonCancel;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Button buttonOK;
        private System.Windows.Forms.Label label1;
    }
}