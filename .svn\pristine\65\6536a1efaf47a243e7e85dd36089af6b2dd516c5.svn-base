﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using System.Collections.ObjectModel;
using MapWinGIS;

using MasterCom.Util;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.MTGis;

namespace MasterCom.RAMS.Func
{
    /// <summary>
    /// 扫频栅格查询类
    /// </summary>
    public class ScanGridAnaQuery : DIYAnalyseFilesOneByOneByRegion
    {
        public ScanGridAnaQuery(MainModel mainModel)
            : base(mainModel)
        {
            IncludeEvent = false;
        }

        /// <summary>
        /// 调用Query之前先设置条件
        /// </summary>
        public ScanGridAnaCondition AnaCondition
        {
            get;
            set;
        }

        /// <summary>
        /// Query之后获取结果
        /// </summary>
        public ScanGridAnaResult AnaResult
        {
            get;
            private set;
        }

        public override bool IsNeedSetQueryCondition
        {
            get
            {
                return false;
            }
        }

        public override string Name
        {
            get { return "扫频栅格查询"; }
        }

        public override string IconName
        {
            get { return string.Empty; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 15000, 15040, this.Name);
        }

        protected override bool isValidCondition()
        {
            return AnaCondition != null && AnaCondition.IsValid();
        }

        /// <summary>
        /// 查询条件解析
        /// </summary>
        protected override void getReadyBeforeQuery()
        {
            // query condition
            QueryCondition qCond = new QueryCondition();
            qCond.Periods.Add(AnaCondition.TimePeriod);
            if (AnaCondition.ServiceType == 1919)
            {
                qCond.ServiceTypes.AddRange(AnaCondition.ServiceTypes_TD);
            }
            else if (AnaCondition.ServiceType == 1212)
            {
                qCond.ServiceTypes.AddRange(AnaCondition.ServiceTypes_GSM);
            }
            else
            {
                qCond.ServiceTypes.Add(AnaCondition.ServiceType);
            }
            qCond.DistrictIDs.Add(MainModel.DistrictID);
            qCond.CarrierTypes = AnaCondition.Carriers;
            if (AnaCondition.ProjectTypes != null)
            {
                qCond.Projects.AddRange(AnaCondition.ProjectTypes);
            }
            if (AnaCondition.FileName != null)
            {
                qCond.NameFilterType = AnaCondition.FilterType;
                qCond.FileName = AnaCondition.FileName;
                qCond.FileNameOrNum = AnaCondition.FileNameOrNum;
            }
            SetQueryCondition(qCond);

            // query column
            if (AnaCondition.ServiceType == 12)
            {
                this.Columns = new List<string>()
                    {
                        "GSCAN_BCCH",
                        "GSCAN_BSIC",
                        "GSCAN_RxLev",
                    };
            }
            else if (AnaCondition.ServiceType == 19)
            {
                this.Columns = new List<string>()
                {
                    "TDS_PCCPCH_RSCP",
                    "TDS_PCCPCH_CPI",
                    "TDS_PCCPCH_Channel",
                };
            }
            else if (AnaCondition.ServiceType == 1212)
            {
                this.Columns = new List<string>()
                {
                    "BCCH",
                    "N_BCCH",
                    "BSIC",
                    "N_BSIC",
                    "RxLevSub",
                    "N_RxLev",
                };
            }
            else if (AnaCondition.ServiceType == 1919)
            {
                this.Columns = new List<string>()
                {
                    "TD_PCCPCH_RSCP",
                    "TD_SCell_UARFCN",
                    "TD_SCell_CPI",
                    "TD_NCell_PCCPCH_RSCP",
                    "TD_NCell_UARFCN",
                    "TD_NCell_CPI",
                };
            }

            // result
            AnaResult = new ScanGridAnaResult();

            // shapeMopDic & shapeRegionDic
            shapeMopDic = new Dictionary<MapWinGIS.Shape, MapOperation2>();
            shapeRegionDic = new Dictionary<MapWinGIS.Shape, ScanGridAnaRegionInfo>();
            foreach (MapWinGIS.Shape shape in AnaCondition.ShapeNameDic.Keys)
            {
                MapOperation2 mop = new MapOperation2();
                mop.FillPolygon(shape);
                shapeMopDic.Add(shape, mop);

                ScanGridAnaRegionInfo regionInfo = new ScanGridAnaRegionInfo(shape, AnaCondition.ShapeNameDic[shape]);
                shapeRegionDic.Add(shape, regionInfo);
            }
            AnaResult.ShapeRegionDic = shapeRegionDic;

            // stringGridDic
            stringGridDic = new Dictionary<string, ScanGridAnaGridInfo>();
        }

        protected override void analyseFiles()
        {
            if (AnaCondition.ServiceType == 12 || AnaCondition.ServiceType == 1212)
            {
                base.analyseFiles();
            }
            else
            {
                analyseTdFiles();
            }
        }

        //判断GSM路测文件的网络类型，参考GScanTestPointSplitter.Split实现。
        private void testGTDNetType(TestPoint testPoint, out List<int> indexOf900, out List<int> indexOf1800)
        {
            indexOf900 = new List<int>();
            indexOf1800 = new List<int>();
            //主服
            {
                short? bcch = (short?)testPoint["BCCH"];
                if (bcch != null)
                {
                    if (1 <= bcch && bcch <= 124)
                    {
                        indexOf900.Add(0);//0 不代表任何意义，仅仅表示该网络类型有值。
                    }
                    else if (512 <= bcch && bcch <= 1024)
                    {
                        indexOf1800.Add(0);
                    }
                }
            }
            //邻区
            for (int i = 0; i < 6; i++)
            {
                short? bcch = (short?)testPoint["N_BCCH", i];
                if (bcch == null)
                {
                    continue;
                }
                if (1 <= bcch && bcch <= 124)
                {
                    indexOf900.Add(i);
                }
                else if (512 <= bcch && bcch <= 1024)
                {
                    indexOf1800.Add(i);
                }
            }
        }
        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager dtFileDataManager in MainModel.DTDataManager.FileDataManagers)
            {
                foreach (TestPoint tp in dtFileDataManager.TestPoints)
                {
                    if (tp.Longitude == 0 || tp.Latitude == 0)
                    {
                        continue;
                    }
                    dealTPByServiceType(tp);
                }
            }
        }

        private void dealTPByServiceType(TestPoint tp)
        {
            if (AnaCondition.ServiceType == 12 || AnaCondition.ServiceType == 1212)//GSM扫频、GSM路测
            {
                List<int> idx900 = null;
                List<int> idx1800 = null;
                if (AnaCondition.ServiceType == 12)
                {
                    GScanTestPointSplitter.Split(tp, out idx900, out idx1800);
                }
                else if (AnaCondition.ServiceType == 1212)
                {
                    this.testGTDNetType(tp, out idx900, out idx1800);
                }
                if (idx900 != null && idx900.Count > 0)
                {
                    doWithDTData(tp, ScanGridAnaGridType.GSM900);
                }
                if (idx1800 != null && idx1800.Count > 0)
                {
                    doWithDTData(tp, ScanGridAnaGridType.DCS1800);
                }
                doWithDTData(tp, ScanGridAnaGridType.最强信号);
            }
            else if (AnaCondition.ServiceType == 19 || AnaCondition.ServiceType == 1919)//TD扫频、TD路测
            {
                doWithDTData(tp, ScanGridAnaGridType.TD);
            }
        }

        protected override void getResultsAfterQuery()
        {
            foreach (ScanGridAnaGridInfo grid in AnaResult.GridList)
            {
                grid.CalcResult();
            }
        }

        protected virtual void doWithDTData(TestPoint tp, ScanGridAnaGridType gridType)
        {
            // find grid
            string mgrsString = MgrsGridConverter.GetMgrsString(tp.Longitude, tp.Latitude, AnaCondition.GridSize);
            string key = mgrsString + "_" + gridType;
            ScanGridAnaGridInfo grid = null;
            bool isNewGrid = false;
            if (!stringGridDic.TryGetValue(key, out grid))
            {
                grid = new ScanGridAnaGridInfo(mgrsString, gridType, AnaCondition.Argument);
                if (grid.CentLng > 180 || grid.CentLat > 90) // 转换库有问题！！！
                {
                    return;
                }
                if (AnaCondition.ServiceType == 12) grid.FileType = "GSM扫频";
                else if (AnaCondition.ServiceType == 19) grid.FileType = "TD扫频";
                else if (AnaCondition.ServiceType == 1212) grid.FileType = "GSM路测";
                else if (AnaCondition.ServiceType == 1919) grid.FileType = "TD路测";
                stringGridDic.Add(key, grid);
                isNewGrid = true;
            }
            grid.DoWithTestPoint(tp);

            if (!isNewGrid)
            {
                return;
            }
            // find region
            bool findShape = false;
            foreach (MapWinGIS.Shape shape in shapeMopDic.Keys)
            {
                //if (shapeMopDic[shape].CheckPointInRegion(tp.Longitude, tp.Latitude))
                if (shapeMopDic[shape].CheckPointInRegion(grid.CentLng, grid.CentLat))//集团算法应该为栅格的中心经纬度
                {
                    findShape = true;
                    shapeRegionDic[shape].AddGrid(grid);
                    break;
                }
            }

            // grid not in region
            if (!findShape)
            {
                AnaResult.OutsideShapeRegion.AddGrid(grid);
            }
        }

        private void analyseTdFiles()
        {
            base.analyseFiles();
        }

        /// <summary>
        /// 获取采样点所属栅格标识
        /// </summary>
        /// <param name="longitude"></param>
        /// <param name="latitude"></param>
        /// <returns></returns>
        protected string GetMGRSGridString(double longitude, double latitude)
        {
            int gridSize = AnaCondition.GridSize;

            LatLon2MGRUTM myClass = new LatLon2MGRUTM();
            string myString = myClass.convertLatLonToMGRUTM(latitude, longitude);

            int eastOffset = Convert.ToInt32(myString.Substring(5, 5));
            int northOffset = Convert.ToInt32(myString.Substring(10, 5));
            eastOffset = (eastOffset / gridSize);
            northOffset = (northOffset / gridSize);

            StringBuilder sb = new StringBuilder();
            sb.Append(myString.Substring(0, 5));
            sb.Append("-");
            sb.Append(eastOffset.ToString());
            sb.Append("-");
            sb.Append(northOffset.ToString());
            sb.Append("-");
            sb.Append(gridSize.ToString());

            return sb.ToString();
        }

        private Dictionary<MapWinGIS.Shape, MapOperation2> shapeMopDic;
        private Dictionary<MapWinGIS.Shape, ScanGridAnaRegionInfo> shapeRegionDic;
        private Dictionary<string, ScanGridAnaGridInfo> stringGridDic;
    }

    /// <summary>
    /// 栅格查询条件
    /// </summary>
    public class ScanGridAnaCondition
    {
        public Dictionary<MapWinGIS.Shape, string> ShapeNameDic { get; set; }
        public List<int> ProjectTypes { get; set; }
        /**
         * ServiceType:
         * 当选择gsm扫频、TD扫频的时候，值为12、19，这个值同时也是业务类型，
         * 当选择路测的时候，这个仅仅是个标识1212、1919，业务类型要看ServiceTypes_GSM、ServiceTypes_TD
         */
        public int ServiceType { get; set; }
        public List<int> ServiceTypes_GSM { get; set; } = new List<int>();//GSM路测业务类型
        public List<int> ServiceTypes_TD { get; set; } = new List<int>();//TD路测业务类型
        public List<int> Carriers { get; set; } = new List<int>();//运营商
        public TimePeriod TimePeriod { get; set; }
        public int GridSize { get; set; }
        public object Argument { get; set; }
        public string FileName { get; set; }
        public int FileNameOrNum { get; set; }
        public FileFilterType FilterType { get; set; }

        public ScanGridAnaCondition()
        {
            GridSize = 50;
        }

        public bool IsValid()
        {
            if (ShapeNameDic == null || ShapeNameDic.Count == 0)
            {
                return false;
            }
            if (ServiceType != 12 && ServiceType != 19 && ServiceType != 1212 && ServiceType != 1919)
            {
                return false;
            }
            if (TimePeriod == null)
            {
                return false;
            }
            //选择查询GSM路测数据时，GSM业务类型不应该为空
            if (ServiceType == 1212 && (ServiceTypes_GSM== null || ServiceTypes_GSM.Count <= 0))
            {
                return false;
            }
            //选择查询TD路测数据时，TD业务类型不应该为空
            if (ServiceType == 1919 && (ServiceTypes_TD == null || ServiceTypes_TD.Count <= 0))
            {
                return false;
            }
            if (Carriers == null || Carriers.Count <= 0)
            {
                return false;
            }
            return true;
        }
    }

    /// <summary>
    /// 栅格查询结果
    /// </summary>
    public class ScanGridAnaResult
    {
        public Dictionary<MapWinGIS.Shape, ScanGridAnaRegionInfo> ShapeRegionDic { get; set; }
        public ScanGridAnaRegionInfo OutsideShapeRegion { get; set; }

        public ScanGridAnaResult()
        {
            ShapeRegionDic = new Dictionary<MapWinGIS.Shape, ScanGridAnaRegionInfo>();
            OutsideShapeRegion = new ScanGridAnaRegionInfo();
        }

        public ReadOnlyCollection<ScanGridAnaGridInfo> GridList
        {
            get
            {
                List<ScanGridAnaGridInfo> gridList = new List<ScanGridAnaGridInfo>();
                gridList.AddRange(OutsideShapeRegion.GridList);
                foreach (ScanGridAnaRegionInfo region in ShapeRegionDic.Values)
                {
                    gridList.AddRange(region.GridList);
                }
                return gridList.AsReadOnly();
            }
        }

        public ReadOnlyCollection<ScanGridAnaRegionInfo> RegionList
        {
            get
            {
                List<ScanGridAnaRegionInfo> regionList = new List<ScanGridAnaRegionInfo>();
                regionList.Add(OutsideShapeRegion);
                regionList.AddRange(ShapeRegionDic.Values);
                return regionList.AsReadOnly();
            }
        }
    }

    /// <summary>
    /// 栅格网络类型
    /// </summary>
    public enum ScanGridAnaGridType
    {
        GSM900 = 0,
        DCS1800,
        最强信号,
        TD,
        Unset,
    }

    /// <summary>
    /// 图层网格
    /// </summary>
    public class ScanGridAnaRegionInfo : IComparable<ScanGridAnaRegionInfo>
    {
        public MapWinGIS.Shape RegionShape { get; set; }
        public string RegionName { get; set; }
        public ReadOnlyCollection<ScanGridAnaGridInfo> GridList
        {
            get
            {
                return gridList.AsReadOnly();
            }
        }

        private readonly Dictionary<string, ScanGridAnaGridInfo> gridInRegionDic;
        private readonly List<ScanGridAnaGridInfo> gridList;

        public ScanGridAnaRegionInfo(MapWinGIS.Shape shape, string name)
        {
            RegionShape = shape;
            RegionName = name;
            gridList = new List<ScanGridAnaGridInfo>();
            gridInRegionDic = new Dictionary<string, ScanGridAnaGridInfo>();
        }

        public ScanGridAnaRegionInfo()
        {
            RegionShape = null;
            RegionName = "网格外";
            gridList = new List<ScanGridAnaGridInfo>();
            gridInRegionDic = new Dictionary<string, ScanGridAnaGridInfo>();
        }

        public bool AddGrid(ScanGridAnaGridInfo grid)
        {
            string key = grid.MGRSGridString + "_" + grid.GridType;
            if (!gridInRegionDic.ContainsKey(key))
            {
                gridList.Add(grid);
                gridInRegionDic.Add(key, grid);
                return true;
            }
            return false;
        }

        public int CompareTo(ScanGridAnaRegionInfo other)
        {
            return this.RegionName.CompareTo(other.RegionName);
        }
    }

    /// <summary>
    /// 扫频栅格
    /// 无效栅格定义：栅格内所有采样点均无第一强
    /// </summary>
    public class ScanGridAnaGridInfo : IComparable<ScanGridAnaGridInfo>
    {
        public string MGRSGridString { get; set; }
        public string FileType { get; set; } = "";//扫频、路测
        public ScanGridAnaGridType GridType
        {
            get;
            private set;
        }
        public int TestPointCount
        {
            get;
            private set;
        }
        public int InvalidPointCount
        {
            get;
            private set;
        }
        public bool IsValidGrid
        {
            get
            {
                return HasMaxValue && MaxRxlev > args[2];
            }
        }
        public bool HasMaxValue
        {
            get
            {
                return TestPointCount > InvalidPointCount;
            }
        }
        public int AbsLevel
        {
            get;
            private set;
        }
        public int RelLevel
        {
            get;
            private set;
        }
        public int RelAndAbsLevel
        {
            get;
            private set;
        }
        public int Existence
        {
            get;
            set;
        }
        public double Rxlev
        {
            get;
            private set;
        }
        public double MaxRxlev
        {
            get;
            private set;
        }
        public double MinRxlev
        {
            get;
            private set;
        }
        public int MaxBcch
        {
            get;
            private set;
        }
        public int MinBcch
        {
            get;
            private set;
        }
        public int MaxBsic
        {
            get;
            private set;
        }
        public int MinBsic
        {
            get;
            private set;
        }
        public double TLLongitude
        {
            get;
            private set;
        }
        public double TLLatitude
        {
            get;
            private set;
        }
        public double BRLongitude
        {
            get;
            private set;
        }
        public double BRLatitude
        {
            get;
            private set;
        }
        public double CentLng
        {
            get;
            private set;
        }
        public double CentLat
        {
            get;
            private set;
        }
        public bool IsWeakRxlev
        {
            get
            {
                return MaxRxlev < args[3];
            }
        }
        public bool IsHighCoverage
        {
            get
            {
                return RelLevel >= args[4];
            }
        }
        public double ConsecutiveGridCount
        {
            get
            {
                return args[5];
            }
        }
        public int EastOffset
        {
            get;
            private set;
        }
        public int NorthOffset
        {
            get;
            private set;
        }
        public string GridZone
        {
            get;
            private set;
        }
        public int GridSize
        {
            get;
            private set;
        }
        public DateTime LastTime { get; set; } = DateTime.MinValue;
        public int StrongRxlevCount { get; set; }
        public Dictionary<ICell, ScanGridAnaCellInfo> CellInfoDic
        {
            get;
            private set;
        }

        public ScanGridAnaGridInfo(string mgrsString, ScanGridAnaGridType gridType, object args)
        {
            MGRSGridString = mgrsString;
            GridType = gridType;
            this.args = args as double[];
            valueSumDic = new Dictionary<string, double>();
            valueCountDic = new Dictionary<string, int>();
            CellInfoDic = new Dictionary<ICell, ScanGridAnaCellInfo>();
            string[] tmpStr = mgrsString.Split('-');
            GridZone = tmpStr[0];
            EastOffset = Convert.ToInt32(tmpStr[1]);
            NorthOffset = Convert.ToInt32(tmpStr[2]);
            GridSize = Convert.ToInt32(tmpStr[3]);
            CalcLngLat();
        }

        public bool Within(DbRect rect)
        {
            if (TLLongitude < rect.x1 || TLLatitude > rect.y2 || BRLongitude > rect.x2 || BRLatitude < rect.y1)
            {
                return false;
            }
            return true;
        }

        public void DoWithTestPoint(TestPoint tp)
        {
            if (tp.DateTime > LastTime)
            {
                LastTime = tp.DateTime;
            }

            if (this.FileType == "TD扫频")
            {
                DoWithTdTestPoint(tp);
            }
            else if (this.FileType == "TD路测")
            {
                DoWithTdTestPoint_DT(tp);
            }
            else if (this.FileType == "GSM扫频")
            {
                DoWithGsmTestPoint(tp);
            }
            else if (this.FileType == "GSM路测")
            {
                DoWithGsmTestPoint_DT(tp);
            }
        }

        public void CalcResult()
        {
            MaxRxlev = double.MinValue;
            MinRxlev = double.MaxValue;
            double sum = 0;
            int cnt = 0;
            string maxString = null;
            string minString = null;
            foreach (string key in valueSumDic.Keys)
            {
                double tmpSum = valueSumDic[key];
                int tmpCnt = valueCountDic[key];
                double tmpMean = tmpSum / tmpCnt;           // tmpCnt肯定大于0

                if (tmpMean > args[2])          //  强信号个数
                {
                    ++StrongRxlevCount;
                }
                if (tmpMean > MaxRxlev)
                {
                    MaxRxlev = tmpMean;
                    maxString = key;
                }
                if (tmpMean < MinRxlev)
                {
                    MinRxlev = tmpMean;
                    minString = key;
                }

                sum += tmpSum;
                cnt += tmpCnt;
            }
            Rxlev = cnt == 0 ? 0 : sum / cnt;
            if (maxString != null)
            {
                MaxBcch = int.Parse(maxString.Split('_')[0]);
                MaxBsic = int.Parse(maxString.Split('_')[1]);
            }
            if (minString != null)
            {
                MinBcch = int.Parse(minString.Split('_')[0]);
                MinBsic = int.Parse(minString.Split('_')[1]);
            }

            CalcMultiCoverage(maxString);
        }

        private void CalcMultiCoverage(string maxKey)
        {
            foreach (string key in valueSumDic.Keys)
            {
                if (key == maxKey)
                {
                    continue;
                }

                if (GridType == ScanGridAnaGridType.最强信号 && !IsSameFreqWithMax(key, maxKey))
                {
                    continue;
                }

                double tmpSum = valueSumDic[key];
                int tmpCnt = valueCountDic[key];
                double tmpMean = tmpSum / tmpCnt;       // tmpCnt肯定大于0

                if (tmpMean > args[1])
                {
                    ++AbsLevel;
                }
                if (tmpMean > Math.Max(MaxRxlev - args[0], args[6])) // args[6]信号允许最小值
                {
                    ++RelLevel;
                }
                if (tmpMean > args[1] && tmpMean - MaxRxlev > -args[0])
                {
                    ++RelAndAbsLevel;
                }
            }
            if (maxKey != null)
            {
                ++AbsLevel;
                ++RelLevel;
                ++RelAndAbsLevel;
            }
        }
        //GSM扫频
        private void DoWithGsmTestPoint(TestPoint tp)
        {
            List<int> freqIdxList = null;
            if (this.GridType == ScanGridAnaGridType.最强信号)
            {
                freqIdxList = tp.GetGSMScanIdxSpecify(GSMFreqBandType.All);
            }
            else if (this.GridType == ScanGridAnaGridType.GSM900)
            {
                freqIdxList = tp.GetGSMScanIdxSpecify(GSMFreqBandType.GSM900);
            }
            else
            {
                freqIdxList = tp.GetGSMScanIdxSpecify(GSMFreqBandType.DSC1800);
            }

            bool valid = false;
            foreach (int freqIdx in freqIdxList)
            {
                int i = freqIdx;
                float? rxlev = (float?)tp["GSCAN_RxLev", i];
                short? bcch = (short?)(int?)tp["GSCAN_BCCH", i];
                byte? bsic = (byte?)(int?)tp["GSCAN_BSIC", i];
                if (rxlev == null || bcch == null || bsic == null || rxlev >= sGsmRxlevParam.ValueMax || rxlev <= sGsmRxlevParam.ValueMin)
                {
                    break;
                }
                valid = true;

                string key = string.Format("{0}_{1}", (int)bcch, (int)bsic);
                if (!valueSumDic.ContainsKey(key))
                {
                    valueSumDic.Add(key, 0);
                    valueCountDic.Add(key, 0);
                }
                valueSumDic[key] += (float)rxlev;
                valueCountDic[key] += 1;

                ICell cell = tp.GetCell_GSMScan(i);
                if (cell == null)
                {
                    continue;
                }
                if (!CellInfoDic.ContainsKey(cell))
                {
                    CellInfoDic.Add(cell, new ScanGridAnaCellInfo(cell));
                }
                CellInfoDic[cell].Append((double)rxlev);
            }

            TestPointCount += 1;
            InvalidPointCount += (valid ? 0 : 1);
        }
        //TD扫频
        private void DoWithTdTestPoint(TestPoint tp)
        {
            bool valid = false;
            for (int i = 0; i < 50; ++i)
            {
                float? rxlev = (float?)tp["TDS_PCCPCH_RSCP", i];
                short? bcch = (short?)(int?)tp["TDS_PCCPCH_Channel", i];
                short? bsic = (byte?)(int?)tp["TDS_PCCPCH_CPI", i];
                if (rxlev == null || bcch == null || bsic == null || rxlev >= sTdRscpParam.ValueMax || rxlev <= sTdRscpParam.ValueMin)
                {
                    break;
                }
                valid = true;

                string key = string.Format("{0}_{1}", (int)bcch, (int)bsic);
                if (!valueSumDic.ContainsKey(key))
                {
                    valueSumDic.Add(key, 0);
                    valueCountDic.Add(key, 0);
                }
                valueSumDic[key] += (float)rxlev;
                valueCountDic[key] += 1;

                ICell cell = tp.GetCell_TDScan(i);
                if (cell == null)
                {
                    continue;
                }
                if (!CellInfoDic.ContainsKey(cell))
                {
                    CellInfoDic.Add(cell, new ScanGridAnaCellInfo(cell));
                }
                CellInfoDic[cell].Append((double)rxlev);

            }

            TestPointCount += 1;
            InvalidPointCount += (valid ? 0 : 1);
        }
        //GSM路测
        private void DoWithGsmTestPoint_DT(TestPoint tp)
        {
            bool valid = false;
            //主服
            {
                float? rxlev = (float?)(short?)tp["RxLevSub"];
                short? bcch = (short?)tp["BCCH"];
                byte? bsic = (byte?)tp["BSIC"];
                if (rxlev != null && bcch != null && bsic != null 
                    && rxlev < sGsmRxlevParam.ValueMax && rxlev > sGsmRxlevParam.ValueMin)
                {
                    ICell cell = tp.GetMainCell();
                    valid = true;
                    addCellInfoDic(rxlev, bcch, bsic, cell);
                }
            }
            //邻区
            for (int i = 0; i < 6; i++ )
            {
                float? nrxlev = (float?)(short?)tp["N_RxLev", i];
                short? nbcch = (short?)tp["N_BCCH", i];
                byte? nbsic = (byte?)tp["N_BSIC", i];
                if (nrxlev == null || nbcch == null || nbsic == null || nrxlev >= sGsmRxlevParam.ValueMax || nrxlev <= sGsmRxlevParam.ValueMin)
                {
                    continue;
                }
                ICell cell = tp.GetNBCell(i);
                valid = true;
                addCellInfoDic(nrxlev, nbcch, nbsic, cell);
            }

            TestPointCount += 1;
            InvalidPointCount += (valid ? 0 : 1);
        }

        private void addCellInfoDic(float? rxlev, short? bcch, byte? bsic, ICell cell)
        {
            string key = string.Format("{0}_{1}", (int)bcch, (int)bsic);
            if (!valueSumDic.ContainsKey(key))
            {
                valueSumDic.Add(key, 0);
                valueCountDic.Add(key, 0);
            }
            valueSumDic[key] += (float)rxlev;
            valueCountDic[key] += 1;

            if (cell != null)
            {
                if (!CellInfoDic.ContainsKey(cell))
                {
                    CellInfoDic.Add(cell, new ScanGridAnaCellInfo(cell));
                }
                CellInfoDic[cell].Append((double)rxlev);
            }
        }

        //TD路测
        private void DoWithTdTestPoint_DT(TestPoint tp)
        {
            bool valid = false;
            valid = dealMainCell(tp, valid);
            valid = dealNBCell(tp, valid);

            TestPointCount += 1;
            InvalidPointCount += (valid ? 0 : 1);
        }

        private bool dealMainCell(TestPoint tp, bool valid)
        {
            //主服
            float? rxlev = (float?)tp["TD_PCCPCH_RSCP"];
            short? bcch = (short?)(int?)tp[MainModel.TD_SCell_UARFCN];
            short? bsic = (short?)(int?)tp[MainModel.TD_SCell_CPI];
            if (rxlev != null && bcch != null && bsic != null
                && rxlev < sTdRscpParam.ValueMax && rxlev > sTdRscpParam.ValueMin)
            {
                valid = true;
                string key = string.Format("{0}_{1}", (int)bcch, (int)bsic);
                if (!valueSumDic.ContainsKey(key))
                {
                    valueSumDic.Add(key, 0);
                    valueCountDic.Add(key, 0);
                }
                valueSumDic[key] += (float)rxlev;
                valueCountDic[key] += 1;

                TDCell tc = null;
                Cell c = null;
                tp.GetMainCell_TD(out tc, out c);
                ICell cell = tc;
                if (cell == null)
                {
                    cell = c;
                }
                if (cell != null)
                {
                    if (!CellInfoDic.ContainsKey(cell))
                    {
                        CellInfoDic.Add(cell, new ScanGridAnaCellInfo(cell));
                    }
                    CellInfoDic[cell].Append((double)rxlev);
                }
            }

            return valid;
        }

        private bool dealNBCell(TestPoint tp, bool valid)
        {
            //邻区
            for (int i = 0; i < 6; ++i)
            {
                int? nrxlev = (int?)tp["TD_NCell_PCCPCH_RSCP", i];
                int? nbcch = (int?)tp["TD_NCell_UARFCN", i];
                int? nbsic = (int?)tp["TD_NCell_CPI", i];
                if (nrxlev == null || nbcch == null || nbsic == null || nrxlev >= sTdRscpParam.ValueMax || nrxlev <= sTdRscpParam.ValueMin)
                {
                    continue;
                }
                valid = true;

                string key = string.Format("{0}_{1}", (int)nbcch, (int)nbsic);
                if (!valueSumDic.ContainsKey(key))
                {
                    valueSumDic.Add(key, 0);
                    valueCountDic.Add(key, 0);
                }
                valueSumDic[key] += (float)nrxlev;
                valueCountDic[key] += 1;

                ICell cell = tp.GetNBCell_TD_TDCell(i);
                if (cell == null)
                {
                    continue;
                }
                if (!CellInfoDic.ContainsKey(cell))
                {
                    CellInfoDic.Add(cell, new ScanGridAnaCellInfo(cell));
                }
                CellInfoDic[cell].Append((double)nrxlev);
            }

            return valid;
        }

        public void CalcLngLat()
        {
            double tlLng, tlLat, brLng, brLat;
            MgrsGridConverter.GetGridLngLat(MGRSGridString, out tlLng, out tlLat, out brLng, out brLat);
            TLLongitude = tlLng;
            TLLatitude = tlLat;
            BRLongitude = brLng;
            BRLatitude = brLat;
            CentLng = (TLLongitude + BRLongitude) / 2;
            CentLat = (TLLatitude + BRLatitude) / 2;
        }

        private bool IsSameFreqWithMax(string curKey, string maxKey)
        {
            int curBcch = Convert.ToInt32(curKey.Split('_')[0]);
            int maxBcch = Convert.ToInt32(maxKey.Split('_')[0]);
            return (maxBcch <= 124 && curBcch <= 124) || (maxBcch >= 512 && curBcch >= 512);
        }

        public int CompareTo(ScanGridAnaGridInfo other)
        {
            double meter = 0.00001;
            if (other.CentLng - CentLng > meter)
            {
                return 1;
            }
            else if (CentLng - other.CentLng > meter)
            {
                return -1;
            }
            else if (CentLat - other.CentLat > meter)
            {
                return 1;
            }
            else if (other.CentLat - CentLat > meter)
            {
                return -1;
            }
            return 0;
        }

        // rel, abs, valid
        private readonly double[] args;
        private readonly Dictionary<string, double> valueSumDic;
        private readonly Dictionary<string, int> valueCountDic;

        private static DTDisplayParameterInfo sGsmRxlevParam { get; set; } = DTDisplayParameterManager.GetInstance()["GSM_SCAN", "RxLev"];
        private static DTDisplayParameterInfo sGsmBsicParam { get; set; } = DTDisplayParameterManager.GetInstance()["GSM_SCAN", "BSIC"];
        private static DTDisplayParameterInfo sTdRscpParam { get; set; } = DTDisplayParameterManager.GetInstance()["TDSCDMA_SCAN", "PCCPCH_RSCP"];
        private static DTDisplayParameterInfo sTdCpiParam { get; set; } = DTDisplayParameterManager.GetInstance()["TDSCDMA_SCAN", "PCCPCH_CPI"];
    }

    public class ScanGridAnaCellInfo
    {
        public ICell Cell
        {
            get;
            private set;
        }
        public string CellName
        {
            get
            {
                return Cell.Name;
            }
        }
        public int Bcch
        {
            get;
            private set;
        }
        public int Bsic
        {
            get;
            private set;
        }
        public double MaxRxlev
        {
            get
            {
                if (TestPointCount == 0)
                {
                    return 0;
                }
                return maxRxlev;
            }
        }
        public double MinRxlev
        {
            get
            {
                if (TestPointCount == 0)
                {
                    return 0;
                }
                return minRxlev;
            }
        }
        public double AvgRxlev
        {
            get
            {
                if (TestPointCount == 0)
                {
                    return 0;
                }
                return Math.Round(sumRxlev / TestPointCount, 2);
            }
        }
        public int TestPointCount
        {
            get;
            private set;
        }
        public ScanGridAnaCellInfo(ICell cell)
        {
            if (cell is Cell)
            {
                Bcch = (cell as Cell).BCCH;
                Bsic = (cell as Cell).BSIC;
            }
            else if (cell is TDCell)
            {
                Bcch = (cell as TDCell).FREQ;
                Bsic = (cell as TDCell).CPI;
            }
            this.Cell = cell;

            TestPointCount = 0;
            sumRxlev = 0;
            minRxlev = double.MaxValue;
            maxRxlev = double.MinValue;
        }
        public void Append(double rxlev)
        {
            ++TestPointCount;
            sumRxlev += rxlev;
            minRxlev = Math.Min(minRxlev, rxlev);
            maxRxlev = Math.Max(maxRxlev, rxlev);
        }
        private double maxRxlev;
        private double minRxlev;
        private double sumRxlev;
    }
}
