﻿namespace MasterCom.RAMS.ZTFunc.ZTCellCheck
{
    partial class CellCheckConditionNRDlg
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.label13 = new System.Windows.Forms.Label();
            this.label12 = new System.Windows.Forms.Label();
            this.numSiteCnt = new System.Windows.Forms.NumericUpDown();
            this.label2 = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.label1 = new System.Windows.Forms.Label();
            this.numCvrDisFactor = new System.Windows.Forms.NumericUpDown();
            this.numOvrLapRSRP = new System.Windows.Forms.NumericUpDown();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.label3 = new System.Windows.Forms.Label();
            this.label5 = new System.Windows.Forms.Label();
            this.label14 = new System.Windows.Forms.Label();
            this.label8 = new System.Windows.Forms.Label();
            this.numMultiRSRPDiff = new System.Windows.Forms.NumericUpDown();
            this.numMultiCvrRSRP = new System.Windows.Forms.NumericUpDown();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.label9 = new System.Windows.Forms.Label();
            this.label10 = new System.Windows.Forms.Label();
            this.label7 = new System.Windows.Forms.Label();
            this.label11 = new System.Windows.Forms.Label();
            this.numMod3RSRP = new System.Windows.Forms.NumericUpDown();
            this.numMod3RSRPDiff = new System.Windows.Forms.NumericUpDown();
            this.btnOK = new System.Windows.Forms.Button();
            this.btnCancel = new System.Windows.Forms.Button();
            this.groupBox4 = new System.Windows.Forms.GroupBox();
            this.numDirRange = new System.Windows.Forms.NumericUpDown();
            this.numAvgDistance = new System.Windows.Forms.NumericUpDown();
            this.label6 = new System.Windows.Forms.Label();
            this.label15 = new System.Windows.Forms.Label();
            this.label16 = new System.Windows.Forms.Label();
            this.label17 = new System.Windows.Forms.Label();
            this.groupBox5 = new System.Windows.Forms.GroupBox();
            this.numAltitude = new System.Windows.Forms.NumericUpDown();
            this.label18 = new System.Windows.Forms.Label();
            this.label19 = new System.Windows.Forms.Label();
            this.groupBox6 = new System.Windows.Forms.GroupBox();
            this.numDiffBandDis = new System.Windows.Forms.NumericUpDown();
            this.label23 = new System.Windows.Forms.Label();
            this.label25 = new System.Windows.Forms.Label();
            this.label24 = new System.Windows.Forms.Label();
            this.numSiteDistance = new System.Windows.Forms.NumericUpDown();
            this.label20 = new System.Windows.Forms.Label();
            this.label21 = new System.Windows.Forms.Label();
            this.groupBox7 = new System.Windows.Forms.GroupBox();
            this.label22 = new System.Windows.Forms.Label();
            this.label26 = new System.Windows.Forms.Label();
            this.numWeakCoverRSRP = new System.Windows.Forms.NumericUpDown();
            this.groupBox8 = new System.Windows.Forms.GroupBox();
            this.groupBox9 = new System.Windows.Forms.GroupBox();
            this.label27 = new System.Windows.Forms.Label();
            this.numMod4RSRPDiff = new System.Windows.Forms.NumericUpDown();
            this.numMod4RSRP = new System.Windows.Forms.NumericUpDown();
            this.label28 = new System.Windows.Forms.Label();
            this.label29 = new System.Windows.Forms.Label();
            this.label30 = new System.Windows.Forms.Label();
            this.groupBox10 = new System.Windows.Forms.GroupBox();
            this.label31 = new System.Windows.Forms.Label();
            this.numMod6RSRPDiff = new System.Windows.Forms.NumericUpDown();
            this.numMod6RSRP = new System.Windows.Forms.NumericUpDown();
            this.label32 = new System.Windows.Forms.Label();
            this.label33 = new System.Windows.Forms.Label();
            this.label34 = new System.Windows.Forms.Label();
            this.groupBox1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numSiteCnt)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numCvrDisFactor)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numOvrLapRSRP)).BeginInit();
            this.groupBox2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numMultiRSRPDiff)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMultiCvrRSRP)).BeginInit();
            this.groupBox3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numMod3RSRP)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMod3RSRPDiff)).BeginInit();
            this.groupBox4.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numDirRange)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numAvgDistance)).BeginInit();
            this.groupBox5.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numAltitude)).BeginInit();
            this.groupBox6.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numDiffBandDis)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSiteDistance)).BeginInit();
            this.groupBox7.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numWeakCoverRSRP)).BeginInit();
            this.groupBox8.SuspendLayout();
            this.groupBox9.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numMod4RSRPDiff)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMod4RSRP)).BeginInit();
            this.groupBox10.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numMod6RSRPDiff)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMod6RSRP)).BeginInit();
            this.SuspendLayout();
            // 
            // groupBox1
            // 
            this.groupBox1.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBox1.Controls.Add(this.label13);
            this.groupBox1.Controls.Add(this.label12);
            this.groupBox1.Controls.Add(this.numSiteCnt);
            this.groupBox1.Controls.Add(this.label2);
            this.groupBox1.Controls.Add(this.label4);
            this.groupBox1.Controls.Add(this.label1);
            this.groupBox1.Controls.Add(this.numCvrDisFactor);
            this.groupBox1.Controls.Add(this.numOvrLapRSRP);
            this.groupBox1.Location = new System.Drawing.Point(12, 168);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(726, 94);
            this.groupBox1.TabIndex = 0;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "过覆盖";
            // 
            // label13
            // 
            this.label13.AutoSize = true;
            this.label13.Location = new System.Drawing.Point(57, 25);
            this.label13.Name = "label13";
            this.label13.Size = new System.Drawing.Size(64, 14);
            this.label13.TabIndex = 21;
            this.label13.Text = "主服场强≥";
            // 
            // label12
            // 
            this.label12.AutoSize = true;
            this.label12.Location = new System.Drawing.Point(6, 58);
            this.label12.Name = "label12";
            this.label12.Size = new System.Drawing.Size(115, 14);
            this.label12.TabIndex = 20;
            this.label12.Text = "理想覆盖参考基站数";
            // 
            // numSiteCnt
            // 
            this.numSiteCnt.Location = new System.Drawing.Point(127, 56);
            this.numSiteCnt.Maximum = new decimal(new int[] {
            20,
            0,
            0,
            0});
            this.numSiteCnt.Minimum = new decimal(new int[] {
            2,
            0,
            0,
            0});
            this.numSiteCnt.Name = "numSiteCnt";
            this.numSiteCnt.Size = new System.Drawing.Size(75, 22);
            this.numSiteCnt.TabIndex = 1;
            this.numSiteCnt.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numSiteCnt.Value = new decimal(new int[] {
            3,
            0,
            0,
            0});
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(487, 58);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(19, 14);
            this.label2.TabIndex = 18;
            this.label2.Text = "倍";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(273, 58);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(127, 14);
            this.label4.TabIndex = 18;
            this.label4.Text = "超过理想覆盖半径系数";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(208, 25);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(31, 14);
            this.label1.TabIndex = 17;
            this.label1.Text = "dBm";
            // 
            // numCvrDisFactor
            // 
            this.numCvrDisFactor.DecimalPlaces = 1;
            this.numCvrDisFactor.Increment = new decimal(new int[] {
            5,
            0,
            0,
            65536});
            this.numCvrDisFactor.Location = new System.Drawing.Point(406, 56);
            this.numCvrDisFactor.Maximum = new decimal(new int[] {
            20,
            0,
            0,
            0});
            this.numCvrDisFactor.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            65536});
            this.numCvrDisFactor.Name = "numCvrDisFactor";
            this.numCvrDisFactor.Size = new System.Drawing.Size(75, 22);
            this.numCvrDisFactor.TabIndex = 2;
            this.numCvrDisFactor.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numCvrDisFactor.Value = new decimal(new int[] {
            16,
            0,
            0,
            65536});
            // 
            // numOvrLapRSRP
            // 
            this.numOvrLapRSRP.Increment = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.numOvrLapRSRP.Location = new System.Drawing.Point(127, 21);
            this.numOvrLapRSRP.Maximum = new decimal(new int[] {
            10,
            0,
            0,
            -2147483648});
            this.numOvrLapRSRP.Minimum = new decimal(new int[] {
            140,
            0,
            0,
            -2147483648});
            this.numOvrLapRSRP.Name = "numOvrLapRSRP";
            this.numOvrLapRSRP.Size = new System.Drawing.Size(75, 22);
            this.numOvrLapRSRP.TabIndex = 0;
            this.numOvrLapRSRP.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numOvrLapRSRP.Value = new decimal(new int[] {
            90,
            0,
            0,
            -2147483648});
            // 
            // groupBox2
            // 
            this.groupBox2.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBox2.Controls.Add(this.label3);
            this.groupBox2.Controls.Add(this.label5);
            this.groupBox2.Controls.Add(this.label14);
            this.groupBox2.Controls.Add(this.label8);
            this.groupBox2.Controls.Add(this.numMultiRSRPDiff);
            this.groupBox2.Controls.Add(this.numMultiCvrRSRP);
            this.groupBox2.Location = new System.Drawing.Point(12, 268);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(725, 58);
            this.groupBox2.TabIndex = 1;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "重叠覆盖";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(57, 25);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(64, 14);
            this.label3.TabIndex = 21;
            this.label3.Text = "主服场强≥";
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(272, 25);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(233, 14);
            this.label5.TabIndex = 21;
            this.label5.Text = "信号强度≥主服强度，或与主服强度差异 ≤";
            // 
            // label14
            // 
            this.label14.AutoSize = true;
            this.label14.Location = new System.Drawing.Point(595, 25);
            this.label14.Name = "label14";
            this.label14.Size = new System.Drawing.Size(21, 14);
            this.label14.TabIndex = 17;
            this.label14.Text = "dB";
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(208, 25);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(31, 14);
            this.label8.TabIndex = 17;
            this.label8.Text = "dBm";
            // 
            // numMultiRSRPDiff
            // 
            this.numMultiRSRPDiff.Increment = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.numMultiRSRPDiff.Location = new System.Drawing.Point(511, 21);
            this.numMultiRSRPDiff.Maximum = new decimal(new int[] {
            200,
            0,
            0,
            0});
            this.numMultiRSRPDiff.Name = "numMultiRSRPDiff";
            this.numMultiRSRPDiff.Size = new System.Drawing.Size(75, 22);
            this.numMultiRSRPDiff.TabIndex = 1;
            this.numMultiRSRPDiff.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numMultiRSRPDiff.Value = new decimal(new int[] {
            12,
            0,
            0,
            0});
            // 
            // numMultiCvrRSRP
            // 
            this.numMultiCvrRSRP.Increment = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.numMultiCvrRSRP.Location = new System.Drawing.Point(127, 21);
            this.numMultiCvrRSRP.Maximum = new decimal(new int[] {
            10,
            0,
            0,
            -2147483648});
            this.numMultiCvrRSRP.Minimum = new decimal(new int[] {
            140,
            0,
            0,
            -2147483648});
            this.numMultiCvrRSRP.Name = "numMultiCvrRSRP";
            this.numMultiCvrRSRP.Size = new System.Drawing.Size(75, 22);
            this.numMultiCvrRSRP.TabIndex = 0;
            this.numMultiCvrRSRP.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numMultiCvrRSRP.Value = new decimal(new int[] {
            90,
            0,
            0,
            -2147483648});
            // 
            // groupBox3
            // 
            this.groupBox3.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBox3.Controls.Add(this.groupBox10);
            this.groupBox3.Controls.Add(this.groupBox9);
            this.groupBox3.Controls.Add(this.groupBox8);
            this.groupBox3.Location = new System.Drawing.Point(11, 332);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.Size = new System.Drawing.Size(726, 224);
            this.groupBox3.TabIndex = 2;
            this.groupBox3.TabStop = false;
            this.groupBox3.Text = "干扰";
            // 
            // label9
            // 
            this.label9.AutoSize = true;
            this.label9.Location = new System.Drawing.Point(12, 28);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(64, 14);
            this.label9.TabIndex = 21;
            this.label9.Text = "主服场强≥";
            // 
            // label10
            // 
            this.label10.AutoSize = true;
            this.label10.Location = new System.Drawing.Point(227, 28);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(233, 14);
            this.label10.TabIndex = 21;
            this.label10.Text = "信号强度≥主服强度，或与主服强度差异 ≤";
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(550, 28);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(21, 14);
            this.label7.TabIndex = 17;
            this.label7.Text = "dB";
            // 
            // label11
            // 
            this.label11.AutoSize = true;
            this.label11.Location = new System.Drawing.Point(163, 28);
            this.label11.Name = "label11";
            this.label11.Size = new System.Drawing.Size(31, 14);
            this.label11.TabIndex = 17;
            this.label11.Text = "dBm";
            // 
            // numMod3RSRP
            // 
            this.numMod3RSRP.Increment = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.numMod3RSRP.Location = new System.Drawing.Point(81, 24);
            this.numMod3RSRP.Maximum = new decimal(new int[] {
            10,
            0,
            0,
            -2147483648});
            this.numMod3RSRP.Minimum = new decimal(new int[] {
            140,
            0,
            0,
            -2147483648});
            this.numMod3RSRP.Name = "numMod3RSRP";
            this.numMod3RSRP.Size = new System.Drawing.Size(75, 22);
            this.numMod3RSRP.TabIndex = 0;
            this.numMod3RSRP.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numMod3RSRP.Value = new decimal(new int[] {
            90,
            0,
            0,
            -2147483648});
            // 
            // numMod3RSRPDiff
            // 
            this.numMod3RSRPDiff.Increment = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.numMod3RSRPDiff.Location = new System.Drawing.Point(466, 24);
            this.numMod3RSRPDiff.Maximum = new decimal(new int[] {
            200,
            0,
            0,
            0});
            this.numMod3RSRPDiff.Name = "numMod3RSRPDiff";
            this.numMod3RSRPDiff.Size = new System.Drawing.Size(75, 22);
            this.numMod3RSRPDiff.TabIndex = 1;
            this.numMod3RSRPDiff.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numMod3RSRPDiff.Value = new decimal(new int[] {
            12,
            0,
            0,
            0});
            // 
            // btnOK
            // 
            this.btnOK.DialogResult = System.Windows.Forms.DialogResult.OK;
            this.btnOK.Location = new System.Drawing.Point(573, 562);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 3;
            this.btnOK.Text = "确定";
            this.btnOK.UseVisualStyleBackColor = true;
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // btnCancel
            // 
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnCancel.Location = new System.Drawing.Point(660, 562);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 4;
            this.btnCancel.Text = "取消";
            this.btnCancel.UseVisualStyleBackColor = true;
            this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
            // 
            // groupBox4
            // 
            this.groupBox4.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBox4.Controls.Add(this.numDirRange);
            this.groupBox4.Controls.Add(this.numAvgDistance);
            this.groupBox4.Controls.Add(this.label6);
            this.groupBox4.Controls.Add(this.label15);
            this.groupBox4.Controls.Add(this.label16);
            this.groupBox4.Controls.Add(this.label17);
            this.groupBox4.Location = new System.Drawing.Point(499, 13);
            this.groupBox4.Name = "groupBox4";
            this.groupBox4.Size = new System.Drawing.Size(239, 85);
            this.groupBox4.TabIndex = 7;
            this.groupBox4.TabStop = false;
            this.groupBox4.Text = "超远站";
            // 
            // numDirRange
            // 
            this.numDirRange.Location = new System.Drawing.Point(100, 21);
            this.numDirRange.Maximum = new decimal(new int[] {
            360,
            0,
            0,
            0});
            this.numDirRange.Name = "numDirRange";
            this.numDirRange.Size = new System.Drawing.Size(75, 22);
            this.numDirRange.TabIndex = 0;
            this.numDirRange.Value = new decimal(new int[] {
            120,
            0,
            0,
            0});
            // 
            // numAvgDistance
            // 
            this.numAvgDistance.Location = new System.Drawing.Point(100, 49);
            this.numAvgDistance.Maximum = new decimal(new int[] {
            100000,
            0,
            0,
            0});
            this.numAvgDistance.Name = "numAvgDistance";
            this.numAvgDistance.Size = new System.Drawing.Size(75, 22);
            this.numAvgDistance.TabIndex = 1;
            this.numAvgDistance.Value = new decimal(new int[] {
            700,
            0,
            0,
            0});
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(181, 25);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(13, 14);
            this.label6.TabIndex = 0;
            this.label6.Text = "°";
            // 
            // label15
            // 
            this.label15.AutoSize = true;
            this.label15.Location = new System.Drawing.Point(181, 55);
            this.label15.Name = "label15";
            this.label15.Size = new System.Drawing.Size(19, 14);
            this.label15.TabIndex = 0;
            this.label15.Text = "米";
            // 
            // label16
            // 
            this.label16.AutoSize = true;
            this.label16.Location = new System.Drawing.Point(32, 25);
            this.label16.Name = "label16";
            this.label16.Size = new System.Drawing.Size(67, 14);
            this.label16.TabIndex = 0;
            this.label16.Text = "搜索角度：";
            // 
            // label17
            // 
            this.label17.AutoSize = true;
            this.label17.Location = new System.Drawing.Point(21, 55);
            this.label17.Name = "label17";
            this.label17.Size = new System.Drawing.Size(79, 14);
            this.label17.TabIndex = 0;
            this.label17.Text = "平均站间距＞";
            // 
            // groupBox5
            // 
            this.groupBox5.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBox5.Controls.Add(this.numAltitude);
            this.groupBox5.Controls.Add(this.label18);
            this.groupBox5.Controls.Add(this.label19);
            this.groupBox5.Location = new System.Drawing.Point(264, 12);
            this.groupBox5.Name = "groupBox5";
            this.groupBox5.Size = new System.Drawing.Size(229, 86);
            this.groupBox5.TabIndex = 6;
            this.groupBox5.TabStop = false;
            this.groupBox5.Text = "超高站";
            // 
            // numAltitude
            // 
            this.numAltitude.Location = new System.Drawing.Point(102, 40);
            this.numAltitude.Maximum = new decimal(new int[] {
            100000,
            0,
            0,
            0});
            this.numAltitude.Name = "numAltitude";
            this.numAltitude.Size = new System.Drawing.Size(75, 22);
            this.numAltitude.TabIndex = 0;
            this.numAltitude.Value = new decimal(new int[] {
            50,
            0,
            0,
            0});
            // 
            // label18
            // 
            this.label18.AutoSize = true;
            this.label18.Location = new System.Drawing.Point(183, 42);
            this.label18.Name = "label18";
            this.label18.Size = new System.Drawing.Size(19, 14);
            this.label18.TabIndex = 0;
            this.label18.Text = "米";
            // 
            // label19
            // 
            this.label19.AutoSize = true;
            this.label19.Location = new System.Drawing.Point(29, 42);
            this.label19.Name = "label19";
            this.label19.Size = new System.Drawing.Size(67, 14);
            this.label19.TabIndex = 0;
            this.label19.Text = "天线挂高＞";
            // 
            // groupBox6
            // 
            this.groupBox6.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBox6.Controls.Add(this.numDiffBandDis);
            this.groupBox6.Controls.Add(this.label23);
            this.groupBox6.Controls.Add(this.label25);
            this.groupBox6.Controls.Add(this.label24);
            this.groupBox6.Controls.Add(this.numSiteDistance);
            this.groupBox6.Controls.Add(this.label20);
            this.groupBox6.Controls.Add(this.label21);
            this.groupBox6.Location = new System.Drawing.Point(13, 12);
            this.groupBox6.Name = "groupBox6";
            this.groupBox6.Size = new System.Drawing.Size(238, 86);
            this.groupBox6.TabIndex = 5;
            this.groupBox6.TabStop = false;
            this.groupBox6.Text = "超近站";
            // 
            // numDiffBandDis
            // 
            this.numDiffBandDis.Location = new System.Drawing.Point(127, 57);
            this.numDiffBandDis.Maximum = new decimal(new int[] {
            100000,
            0,
            0,
            0});
            this.numDiffBandDis.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            -2147483648});
            this.numDiffBandDis.Name = "numDiffBandDis";
            this.numDiffBandDis.Size = new System.Drawing.Size(75, 22);
            this.numDiffBandDis.TabIndex = 1;
            this.numDiffBandDis.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numDiffBandDis.Value = new decimal(new int[] {
            50,
            0,
            0,
            0});
            // 
            // label23
            // 
            this.label23.AutoSize = true;
            this.label23.Location = new System.Drawing.Point(207, 61);
            this.label23.Name = "label23";
            this.label23.Size = new System.Drawing.Size(19, 14);
            this.label23.TabIndex = 2;
            this.label23.Text = "米";
            // 
            // label25
            // 
            this.label25.AutoSize = true;
            this.label25.Location = new System.Drawing.Point(66, 69);
            this.label25.Name = "label25";
            this.label25.Size = new System.Drawing.Size(55, 14);
            this.label25.TabIndex = 3;
            this.label25.Text = "相距需＞";
            // 
            // label24
            // 
            this.label24.AutoSize = true;
            this.label24.Location = new System.Drawing.Point(6, 52);
            this.label24.Name = "label24";
            this.label24.Size = new System.Drawing.Size(125, 14);
            this.label24.TabIndex = 3;
            this.label24.Text = "若两基站异频(共站)，";
            // 
            // numSiteDistance
            // 
            this.numSiteDistance.Location = new System.Drawing.Point(127, 24);
            this.numSiteDistance.Maximum = new decimal(new int[] {
            100000,
            0,
            0,
            0});
            this.numSiteDistance.Name = "numSiteDistance";
            this.numSiteDistance.Size = new System.Drawing.Size(75, 22);
            this.numSiteDistance.TabIndex = 0;
            this.numSiteDistance.Value = new decimal(new int[] {
            100,
            0,
            0,
            0});
            // 
            // label20
            // 
            this.label20.AutoSize = true;
            this.label20.Location = new System.Drawing.Point(207, 29);
            this.label20.Name = "label20";
            this.label20.Size = new System.Drawing.Size(19, 14);
            this.label20.TabIndex = 0;
            this.label20.Text = "米";
            // 
            // label21
            // 
            this.label21.AutoSize = true;
            this.label21.Location = new System.Drawing.Point(42, 26);
            this.label21.Name = "label21";
            this.label21.Size = new System.Drawing.Size(79, 14);
            this.label21.TabIndex = 0;
            this.label21.Text = "两基站相距＜";
            // 
            // groupBox7
            // 
            this.groupBox7.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBox7.Controls.Add(this.label22);
            this.groupBox7.Controls.Add(this.label26);
            this.groupBox7.Controls.Add(this.numWeakCoverRSRP);
            this.groupBox7.Location = new System.Drawing.Point(13, 104);
            this.groupBox7.Name = "groupBox7";
            this.groupBox7.Size = new System.Drawing.Size(726, 58);
            this.groupBox7.TabIndex = 0;
            this.groupBox7.TabStop = false;
            this.groupBox7.Text = "弱覆盖";
            // 
            // label22
            // 
            this.label22.AutoSize = true;
            this.label22.Location = new System.Drawing.Point(57, 25);
            this.label22.Name = "label22";
            this.label22.Size = new System.Drawing.Size(64, 14);
            this.label22.TabIndex = 21;
            this.label22.Text = "主服场强≤";
            // 
            // label26
            // 
            this.label26.AutoSize = true;
            this.label26.Location = new System.Drawing.Point(208, 25);
            this.label26.Name = "label26";
            this.label26.Size = new System.Drawing.Size(31, 14);
            this.label26.TabIndex = 17;
            this.label26.Text = "dBm";
            // 
            // numWeakCoverRSRP
            // 
            this.numWeakCoverRSRP.Increment = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.numWeakCoverRSRP.Location = new System.Drawing.Point(127, 21);
            this.numWeakCoverRSRP.Maximum = new decimal(new int[] {
            10,
            0,
            0,
            -2147483648});
            this.numWeakCoverRSRP.Minimum = new decimal(new int[] {
            140,
            0,
            0,
            -2147483648});
            this.numWeakCoverRSRP.Name = "numWeakCoverRSRP";
            this.numWeakCoverRSRP.Size = new System.Drawing.Size(75, 22);
            this.numWeakCoverRSRP.TabIndex = 0;
            this.numWeakCoverRSRP.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numWeakCoverRSRP.Value = new decimal(new int[] {
            100,
            0,
            0,
            -2147483648});
            // 
            // groupBox8
            // 
            this.groupBox8.Controls.Add(this.label9);
            this.groupBox8.Controls.Add(this.numMod3RSRPDiff);
            this.groupBox8.Controls.Add(this.numMod3RSRP);
            this.groupBox8.Controls.Add(this.label11);
            this.groupBox8.Controls.Add(this.label7);
            this.groupBox8.Controls.Add(this.label10);
            this.groupBox8.Location = new System.Drawing.Point(47, 21);
            this.groupBox8.Name = "groupBox8";
            this.groupBox8.Size = new System.Drawing.Size(623, 59);
            this.groupBox8.TabIndex = 22;
            this.groupBox8.TabStop = false;
            this.groupBox8.Text = "模三干扰";
            // 
            // groupBox9
            // 
            this.groupBox9.Controls.Add(this.label27);
            this.groupBox9.Controls.Add(this.numMod4RSRPDiff);
            this.groupBox9.Controls.Add(this.numMod4RSRP);
            this.groupBox9.Controls.Add(this.label28);
            this.groupBox9.Controls.Add(this.label29);
            this.groupBox9.Controls.Add(this.label30);
            this.groupBox9.Location = new System.Drawing.Point(47, 83);
            this.groupBox9.Name = "groupBox9";
            this.groupBox9.Size = new System.Drawing.Size(623, 59);
            this.groupBox9.TabIndex = 23;
            this.groupBox9.TabStop = false;
            this.groupBox9.Text = "模四干扰";
            // 
            // label27
            // 
            this.label27.AutoSize = true;
            this.label27.Location = new System.Drawing.Point(15, 31);
            this.label27.Name = "label27";
            this.label27.Size = new System.Drawing.Size(64, 14);
            this.label27.TabIndex = 21;
            this.label27.Text = "主服场强≥";
            // 
            // numMod4RSRPDiff
            // 
            this.numMod4RSRPDiff.Increment = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.numMod4RSRPDiff.Location = new System.Drawing.Point(469, 27);
            this.numMod4RSRPDiff.Maximum = new decimal(new int[] {
            200,
            0,
            0,
            0});
            this.numMod4RSRPDiff.Name = "numMod4RSRPDiff";
            this.numMod4RSRPDiff.Size = new System.Drawing.Size(75, 22);
            this.numMod4RSRPDiff.TabIndex = 1;
            this.numMod4RSRPDiff.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numMod4RSRPDiff.Value = new decimal(new int[] {
            12,
            0,
            0,
            0});
            // 
            // numMod4RSRP
            // 
            this.numMod4RSRP.Increment = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.numMod4RSRP.Location = new System.Drawing.Point(84, 27);
            this.numMod4RSRP.Maximum = new decimal(new int[] {
            10,
            0,
            0,
            -2147483648});
            this.numMod4RSRP.Minimum = new decimal(new int[] {
            140,
            0,
            0,
            -2147483648});
            this.numMod4RSRP.Name = "numMod4RSRP";
            this.numMod4RSRP.Size = new System.Drawing.Size(75, 22);
            this.numMod4RSRP.TabIndex = 0;
            this.numMod4RSRP.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numMod4RSRP.Value = new decimal(new int[] {
            90,
            0,
            0,
            -2147483648});
            // 
            // label28
            // 
            this.label28.AutoSize = true;
            this.label28.Location = new System.Drawing.Point(166, 31);
            this.label28.Name = "label28";
            this.label28.Size = new System.Drawing.Size(31, 14);
            this.label28.TabIndex = 17;
            this.label28.Text = "dBm";
            // 
            // label29
            // 
            this.label29.AutoSize = true;
            this.label29.Location = new System.Drawing.Point(553, 31);
            this.label29.Name = "label29";
            this.label29.Size = new System.Drawing.Size(21, 14);
            this.label29.TabIndex = 17;
            this.label29.Text = "dB";
            // 
            // label30
            // 
            this.label30.AutoSize = true;
            this.label30.Location = new System.Drawing.Point(230, 31);
            this.label30.Name = "label30";
            this.label30.Size = new System.Drawing.Size(233, 14);
            this.label30.TabIndex = 21;
            this.label30.Text = "信号强度≥主服强度，或与主服强度差异 ≤";
            // 
            // groupBox10
            // 
            this.groupBox10.Controls.Add(this.label31);
            this.groupBox10.Controls.Add(this.numMod6RSRPDiff);
            this.groupBox10.Controls.Add(this.numMod6RSRP);
            this.groupBox10.Controls.Add(this.label32);
            this.groupBox10.Controls.Add(this.label33);
            this.groupBox10.Controls.Add(this.label34);
            this.groupBox10.Location = new System.Drawing.Point(47, 148);
            this.groupBox10.Name = "groupBox10";
            this.groupBox10.Size = new System.Drawing.Size(623, 59);
            this.groupBox10.TabIndex = 24;
            this.groupBox10.TabStop = false;
            this.groupBox10.Text = "模六干扰";
            // 
            // label31
            // 
            this.label31.AutoSize = true;
            this.label31.Location = new System.Drawing.Point(15, 31);
            this.label31.Name = "label31";
            this.label31.Size = new System.Drawing.Size(64, 14);
            this.label31.TabIndex = 21;
            this.label31.Text = "主服场强≥";
            // 
            // numMod6RSRPDiff
            // 
            this.numMod6RSRPDiff.Increment = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.numMod6RSRPDiff.Location = new System.Drawing.Point(469, 27);
            this.numMod6RSRPDiff.Maximum = new decimal(new int[] {
            200,
            0,
            0,
            0});
            this.numMod6RSRPDiff.Name = "numMod6RSRPDiff";
            this.numMod6RSRPDiff.Size = new System.Drawing.Size(75, 22);
            this.numMod6RSRPDiff.TabIndex = 1;
            this.numMod6RSRPDiff.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numMod6RSRPDiff.Value = new decimal(new int[] {
            12,
            0,
            0,
            0});
            // 
            // numMod6RSRP
            // 
            this.numMod6RSRP.Increment = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.numMod6RSRP.Location = new System.Drawing.Point(84, 27);
            this.numMod6RSRP.Maximum = new decimal(new int[] {
            10,
            0,
            0,
            -2147483648});
            this.numMod6RSRP.Minimum = new decimal(new int[] {
            140,
            0,
            0,
            -2147483648});
            this.numMod6RSRP.Name = "numMod6RSRP";
            this.numMod6RSRP.Size = new System.Drawing.Size(75, 22);
            this.numMod6RSRP.TabIndex = 0;
            this.numMod6RSRP.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numMod6RSRP.Value = new decimal(new int[] {
            90,
            0,
            0,
            -2147483648});
            // 
            // label32
            // 
            this.label32.AutoSize = true;
            this.label32.Location = new System.Drawing.Point(166, 31);
            this.label32.Name = "label32";
            this.label32.Size = new System.Drawing.Size(31, 14);
            this.label32.TabIndex = 17;
            this.label32.Text = "dBm";
            // 
            // label33
            // 
            this.label33.AutoSize = true;
            this.label33.Location = new System.Drawing.Point(553, 31);
            this.label33.Name = "label33";
            this.label33.Size = new System.Drawing.Size(21, 14);
            this.label33.TabIndex = 17;
            this.label33.Text = "dB";
            // 
            // label34
            // 
            this.label34.AutoSize = true;
            this.label34.Location = new System.Drawing.Point(230, 31);
            this.label34.Name = "label34";
            this.label34.Size = new System.Drawing.Size(233, 14);
            this.label34.TabIndex = 21;
            this.label34.Text = "信号强度≥主服强度，或与主服强度差异 ≤";
            // 
            // CellCheckConditionNRDlg
            // 
            this.AcceptButton = this.btnOK;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(750, 597);
            this.Controls.Add(this.groupBox4);
            this.Controls.Add(this.groupBox5);
            this.Controls.Add(this.groupBox6);
            this.Controls.Add(this.groupBox7);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.Controls.Add(this.groupBox3);
            this.Controls.Add(this.groupBox2);
            this.Controls.Add(this.groupBox1);
            this.Name = "CellCheckConditionNRDlg";
            this.Text = "设置";
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numSiteCnt)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numCvrDisFactor)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numOvrLapRSRP)).EndInit();
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numMultiRSRPDiff)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMultiCvrRSRP)).EndInit();
            this.groupBox3.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.numMod3RSRP)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMod3RSRPDiff)).EndInit();
            this.groupBox4.ResumeLayout(false);
            this.groupBox4.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numDirRange)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numAvgDistance)).EndInit();
            this.groupBox5.ResumeLayout(false);
            this.groupBox5.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numAltitude)).EndInit();
            this.groupBox6.ResumeLayout(false);
            this.groupBox6.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numDiffBandDis)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSiteDistance)).EndInit();
            this.groupBox7.ResumeLayout(false);
            this.groupBox7.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numWeakCoverRSRP)).EndInit();
            this.groupBox8.ResumeLayout(false);
            this.groupBox8.PerformLayout();
            this.groupBox9.ResumeLayout(false);
            this.groupBox9.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numMod4RSRPDiff)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMod4RSRP)).EndInit();
            this.groupBox10.ResumeLayout(false);
            this.groupBox10.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numMod6RSRPDiff)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMod6RSRP)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.Label label13;
        private System.Windows.Forms.Label label12;
        private System.Windows.Forms.NumericUpDown numSiteCnt;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.NumericUpDown numCvrDisFactor;
        private System.Windows.Forms.NumericUpDown numOvrLapRSRP;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.NumericUpDown numMultiRSRPDiff;
        private System.Windows.Forms.NumericUpDown numMultiCvrRSRP;
        private System.Windows.Forms.GroupBox groupBox3;
        private System.Windows.Forms.Label label11;
        private System.Windows.Forms.NumericUpDown numMod3RSRP;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label14;
        private System.Windows.Forms.Label label9;
        private System.Windows.Forms.Label label10;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.NumericUpDown numMod3RSRPDiff;
        private System.Windows.Forms.Button btnOK;
        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.GroupBox groupBox4;
        private System.Windows.Forms.NumericUpDown numDirRange;
        private System.Windows.Forms.NumericUpDown numAvgDistance;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.Label label15;
        private System.Windows.Forms.Label label16;
        private System.Windows.Forms.Label label17;
        private System.Windows.Forms.GroupBox groupBox5;
        private System.Windows.Forms.NumericUpDown numAltitude;
        private System.Windows.Forms.Label label18;
        private System.Windows.Forms.Label label19;
        private System.Windows.Forms.GroupBox groupBox6;
        private System.Windows.Forms.NumericUpDown numSiteDistance;
        private System.Windows.Forms.Label label20;
        private System.Windows.Forms.Label label21;
        private System.Windows.Forms.GroupBox groupBox7;
        private System.Windows.Forms.Label label22;
        private System.Windows.Forms.Label label26;
        private System.Windows.Forms.NumericUpDown numWeakCoverRSRP;
        private System.Windows.Forms.NumericUpDown numDiffBandDis;
        private System.Windows.Forms.Label label23;
        private System.Windows.Forms.Label label25;
        private System.Windows.Forms.Label label24;
        private System.Windows.Forms.GroupBox groupBox8;
        private System.Windows.Forms.GroupBox groupBox10;
        private System.Windows.Forms.Label label31;
        private System.Windows.Forms.NumericUpDown numMod6RSRPDiff;
        private System.Windows.Forms.NumericUpDown numMod6RSRP;
        private System.Windows.Forms.Label label32;
        private System.Windows.Forms.Label label33;
        private System.Windows.Forms.Label label34;
        private System.Windows.Forms.GroupBox groupBox9;
        private System.Windows.Forms.Label label27;
        private System.Windows.Forms.NumericUpDown numMod4RSRPDiff;
        private System.Windows.Forms.NumericUpDown numMod4RSRP;
        private System.Windows.Forms.Label label28;
        private System.Windows.Forms.Label label29;
        private System.Windows.Forms.Label label30;
    }
}