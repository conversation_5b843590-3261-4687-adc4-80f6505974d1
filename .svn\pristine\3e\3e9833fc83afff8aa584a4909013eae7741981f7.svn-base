﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;

namespace MasterCom.RAMS.Model.BaseInfo
{
    public class QueryUsers : DIYSQLBase
    {
        private readonly List<User> userList = new List<User>();
        public List<User> UserList
        {
            get { return userList; }
        }

        public QueryUsers(MainModel mainModel)
            : base(mainModel)
        {
            MainDB = true;
        }
        readonly User user;
        public QueryUsers(MainModel mainModel,User user)
            : base(mainModel)
        {
            MainDB = true;
            this.user = user;
        }
        protected override string getSqlTextString()
        {
            string condition = this.user != null ? "=" + this.user.ID.ToString() : ">-1";
#if LoginManage
            string sqlStr = @"select a.[iid],a.[strname],a.[logon_code],a.[phone],a.[strcomment],a.[icityid],a.[iuserstate],b.[strcomment]
from dbo.tb_cfg_static_user a left JOIN dbo.tb_cfg_static_user_state b ON a.iuserstate = b.iid
 where a.iid" + condition + " order by a.iid";
#else
            string sqlStr = "select [iid],[strname],[logon_code],[phone],[strcomment],[icityid] from dbo.tb_cfg_static_user where iid" + condition + " order by iid";
#endif
            return sqlStr;
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
#if LoginManage
            E_VType[] rType = new E_VType[8];
            rType[0] = E_VType.E_Int;
            rType[1] = E_VType.E_String;
            rType[2] = E_VType.E_String;
            rType[3] = E_VType.E_String;
            rType[4] = E_VType.E_String;
            rType[5] = E_VType.E_Int;
            rType[6] = E_VType.E_Int;
            rType[7] = E_VType.E_String;
#else
            E_VType[] rType = new E_VType[6];
            rType[0] = E_VType.E_Int;
            rType[1] = E_VType.E_String;
            rType[2] = E_VType.E_String;
            rType[3] = E_VType.E_String;
            rType[4] = E_VType.E_String;
            rType[5] = E_VType.E_Int;
#endif
            return rType;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    fillData(package);
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    //查询全部用户
                    MainModel.PermissionManager.HasQueriedUsers = this.user == null && userList.Count > 1;
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                setProgressPercent(ref index, ref progress);
            }
        }

        private void fillData(Package package)
        {
            User curUser = new User();
            curUser.ID = package.Content.GetParamInt();
            if (MainModel.User.ID == curUser.ID)
            {
                curUser = MainModel.User;
            }
            curUser.Name = package.Content.GetParamString();
            curUser.LoginName = package.Content.GetParamString();
            curUser.Phone = package.Content.GetParamString();
            curUser.Description = package.Content.GetParamString();
            curUser.CityID = package.Content.GetParamInt();
#if LoginManage
            curUser.UserStatus = package.Content.GetParamInt();
            curUser.UserStatusDes = package.Content.GetParamString();
#endif
            userList.Add(curUser);
        }

        public override string Name
        {
            get { return "查询已配置的用户信息。"; }
        }
    }
}
