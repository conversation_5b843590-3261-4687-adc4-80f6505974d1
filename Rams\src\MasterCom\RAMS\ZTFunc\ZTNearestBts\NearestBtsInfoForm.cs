﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using MasterCom.Util;
using DevExpress.XtraEditors;
using System.IO;
using System.Threading;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NearestBtsInfoForm : MinCloseForm
    {
        ListView lvBts;
        ListView lvTDNodeb;
        public NearestBtsInfoForm(MainModel model)
            : base(model)
        {
            InitializeComponent();

            lvBts = new ListView();
            lvTDNodeb = new ListView();
            lvBts.Parent = xtraTabPageGSM;
            lvTDNodeb.Parent = xtraTabPageTD;
            lvBts.MouseDoubleClick += new MouseEventHandler(lvBts_MouseDoubleClick);
            lvTDNodeb.MouseDoubleClick += new MouseEventHandler(lvTDNodeb_MouseDoubleClick);
        }

        private void initLV(int btsNum)
        {
            ColumnHeader chSN = new ColumnHeader();
            chSN.Text = "序号";
            ColumnHeader chProvince = new ColumnHeader();
            chProvince.Text = "省";
            ColumnHeader chCity = new ColumnHeader();
            chCity.Text = "地市";
            ColumnHeader chCountry = new ColumnHeader();
            chCountry.Text = "区/县";
            ColumnHeader chTown = new ColumnHeader();
            chTown.Text = "网格名称(乡镇名、村名、景点名、企业名)";
            ColumnHeader chScene = new ColumnHeader();
            chScene.Text = "场景类型";
            ColumnHeader chLongOne = new ColumnHeader();
            chLongOne.Text = "网格经度1";
            ColumnHeader chLatOne = new ColumnHeader();
            chLatOne.Text = "网格纬度1";
            ColumnHeader chArea = new ColumnHeader();
            chArea.Text = "区域面积(平方公里)";

            lvBts.Columns.Clear();
            lvTDNodeb.Columns.Clear();

            lvBts.Columns.Add(chSN);
            lvBts.Columns.Add(chProvince);
            lvBts.Columns.Add(chCity);
            lvBts.Columns.Add(chCountry);
            lvBts.Columns.Add(chTown);
            lvBts.Columns.Add(chScene);
            lvBts.Columns.Add(chLongOne);
            lvBts.Columns.Add(chLatOne);
            lvBts.Columns.Add(chArea);

            lvTDNodeb.Columns.Add((ColumnHeader)chSN.Clone());
            lvTDNodeb.Columns.Add((ColumnHeader)chProvince.Clone());
            lvTDNodeb.Columns.Add((ColumnHeader)chCity.Clone());
            lvTDNodeb.Columns.Add((ColumnHeader)chCountry.Clone());
            lvTDNodeb.Columns.Add((ColumnHeader)chTown.Clone());
            lvTDNodeb.Columns.Add((ColumnHeader)chScene.Clone());
            lvTDNodeb.Columns.Add((ColumnHeader)chLongOne.Clone());
            lvTDNodeb.Columns.Add((ColumnHeader)chLatOne.Clone());
            lvTDNodeb.Columns.Add((ColumnHeader)chArea.Clone());

            for (int i = 0; i < btsNum; i++)
            {
                ColumnHeader chName = new ColumnHeader();
                chName.Text = "基站名称" + i + 1;
                ColumnHeader chDis = new ColumnHeader();
                chDis.Text = "基站距离" + i + 1;

                lvBts.Columns.Add(chName);
                lvBts.Columns.Add(chDis);

                lvTDNodeb.Columns.Add((ColumnHeader)chName.Clone());
                lvTDNodeb.Columns.Add((ColumnHeader)chDis.Clone());
            }
        }

        private void refreshLV(List<VillageGrid> villageGrids)
        {
            foreach (VillageGrid vg in villageGrids)
            {
                ListViewItem lviBts = new ListViewItem();
                lviBts.SubItems[0].Text = vg.SN.ToString();
                lviBts.SubItems.Add(vg.Province);
                lviBts.SubItems.Add(vg.City);
                lviBts.SubItems.Add(vg.Country);
                lviBts.SubItems.Add(vg.Town);
                lviBts.SubItems.Add(vg.Scene);
                lviBts.SubItems.Add(vg.FirstPoint.Longitude.ToString());
                lviBts.SubItems.Add(vg.FirstPoint.Latitude.ToString());
                lviBts.SubItems.Add(vg.RegionArea.ToString());

                ListViewItem lviNodeB = lviBts.Clone() as ListViewItem;

                foreach (DisBts bts in vg.NearestBtsVec)
                {
                    lviBts.SubItems.Add(bts.bts.Name);
                    lviBts.SubItems.Add(bts.distance.ToString());
                }

                foreach (DisTDNodeB nodeb in vg.NearestNodebVec)
                {
                    lviNodeB.SubItems.Add(nodeb.nodeb.Name);
                    lviNodeB.SubItems.Add(nodeb.distance.ToString());
                }

                lviBts.Tag = vg;
                lviNodeB.Tag = vg;
                lvBts.Items.Add(lviBts);
                lvTDNodeb.Items.Add(lviNodeB);
            }
            lvBts.Dock = DockStyle.Fill;
            lvBts.View = View.Details;
            lvBts.GridLines = true;
            lvBts.MultiSelect = false;
            lvBts.FullRowSelect = true;

            lvTDNodeb.Dock = DockStyle.Fill;
            lvTDNodeb.View = View.Details;
            lvTDNodeb.GridLines = true;
            lvTDNodeb.MultiSelect = false;
            lvTDNodeb.FullRowSelect = true;
        }

        public void FillData(List<VillageGrid> villageGrids, int btsNum)
        {
            initLV(btsNum);
            refreshLV(villageGrids);
        }

        private void lvBts_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            if (lvBts.SelectedItems.Count <= 0) return;
            VillageGrid vg = lvBts.SelectedItems[0].Tag as VillageGrid;
            if (vg == null) return;
            MainModel.MainForm.GetMapForm().GoToView(vg.FirstPoint.Longitude, vg.FirstPoint.Latitude, 500);
        }

        private void lvTDNodeb_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            if (lvTDNodeb.SelectedItems.Count <= 0) return;
            VillageGrid vg = lvTDNodeb.SelectedItems[0].Tag as VillageGrid;
            if (vg == null) return;
            MainModel.MainForm.GetMapForm().GoToView(vg.FirstPoint.Longitude, vg.FirstPoint.Latitude, 500);
        }

        bool error = false;
        private void ToolStripMenuItemExport_Click(object sender, EventArgs e)
        {
            error = false;
            SaveFileDialog dlg = new SaveFileDialog();
            dlg.RestoreDirectory = false;
            dlg.Filter = FilterHelper.Excel;
            dlg.FilterIndex = 2;//默认xlsx
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                string fileName = dlg.FileName;
                WaitBox.Show("正在导出到Excel...", doExport, (object)fileName);
                if (!error)
                {
                    if (DialogResult.Yes == XtraMessageBox.Show("Excel文件保存完毕，是否现在打开?", "打开文件", MessageBoxButtons.YesNo))  //添加了自动打开文件的提示
                    {
                        try
                        {
                            System.Diagnostics.Process.Start(fileName);
                        }
                        catch
                        {
                            XtraMessageBox.Show("打开失败!\r\n文件名:" + fileName);
                        }
                    }
                }
                else
                {
                    XtraMessageBox.Show("导出xls失败");                    
                }
            }
        }

        private void doExport(object obj)
        {
            try
            {
                string fileName = obj as string;
                IWorkbook workBook = new XSSFWorkbook();

                WaitBox.Text = "正在导出GSM...";
                ISheet sheetGsm = workBook.CreateSheet(xtraTabPageGSM.Text);
                IRow rowH = sheetGsm.CreateRow(0);
                for (int h = 0; h < lvBts.Columns.Count;h++ )
                {
                    rowH.CreateCell(h).SetCellValue(lvBts.Columns[h].Text);
                }
                for (int i = 1; i <= lvBts.Items.Count; i++)
                {
                    IRow row = sheetGsm.CreateRow(i);
                    for (int j = 0; j < lvBts.Items[i - 1].SubItems.Count; j++)
                    {
                        row.CreateCell(j).SetCellValue(lvBts.Items[i - 1].SubItems[j].Text);
                    }
                    WaitBox.ProgressPercent = (int)(100.0 * i / lvBts.Items.Count);
                }

                WaitBox.Text = "正在导出TD...";
                ISheet sheetTD = workBook.CreateSheet(xtraTabPageTD.Text);
                IRow rowH_TD = sheetTD.CreateRow(0);
                for (int h = 0; h < lvTDNodeb.Columns.Count; h++)
                {
                    rowH_TD.CreateCell(h).SetCellValue(lvTDNodeb.Columns[h].Text);
                }
                for (int i = 1; i <= lvTDNodeb.Items.Count; i++)
                {
                    IRow row = sheetTD.CreateRow(i);
                    for (int j = 0; j < lvTDNodeb.Items[i - 1].SubItems.Count; j++)
                    {
                        row.CreateCell(j).SetCellValue(lvTDNodeb.Items[i - 1].SubItems[j].Text);
                    }
                    WaitBox.ProgressPercent = (int)(100.0 * i / lvTDNodeb.Items.Count);
                }
                saveExcel(workBook, fileName);
            }
            catch
            {
                error = true;
            }
            finally
            {
                Thread.Sleep(20);
                WaitBox.Close();
            }
        }

        private static void saveExcel(IWorkbook workbook, string fileName)
        {
            FileStream fileStream = new FileStream(fileName, FileMode.Create);
            try
            {
                workbook.Write(fileStream);
            }
            finally
            {
                fileStream.Close();
            }
        }
    }
}
