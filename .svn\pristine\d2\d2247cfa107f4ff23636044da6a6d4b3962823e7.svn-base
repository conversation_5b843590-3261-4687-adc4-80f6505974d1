﻿using System;
using System.Collections.Generic;
using System.Text;
using System.IO;
using MasterCom.RAMS.KPI_Statistics;

using MasterCom.RAMS.Model;
using MasterCom.Util.UiEx;

using Excel = Microsoft.Office.Interop.Excel;

namespace MasterCom.RAMS.ZTFunc.ZTLTEClusterAna
{
    class AnalyzerManager
    {
        private List<AnalyzerBase> acceptorList = null;

        public void SetAcceptCond(bool ClusterType)
        {

            if (ClusterType)
            {
                this.acceptorList = new List<AnalyzerBase>()
                {
                   new ShortCallAna(),
                   new ShortCallAna(),
                   new LongCallAna(),
                   new LongCallAna(),
                   new CSFBAna(),
                   new CSFBAna()
                };
            }
            else
            {
                this.acceptorList = new List<AnalyzerBase>()
                {
                   new ShortCallAna(),
                   new SpecialShortCall(),
                   new LongCallAna(),
                   new SpecialLongCall(),
                   new CSFBAna(),
                   new CSFBAna()
                };
            }
            
        }

        public void AnalyzeFile(MasterCom.RAMS.Model.FileInfo fileInfo, KPIDataGroup kpiDataGroup)
        {
            try
            {
                if (fileInfo.Name.Contains("优化后") && fileInfo.Name.Contains("CSFB"))
                {
                    acceptorList[5].AnalyzeFile(fileInfo, kpiDataGroup);
                }
                else if (fileInfo.Name.Contains("CSFB"))
                {
                    acceptorList[4].AnalyzeFile(fileInfo, kpiDataGroup);
                }
                else if (fileInfo.Name.Contains("优化后") && (fileInfo.Name.Contains("短呼") || fileInfo.Name.Contains("空扰")))
                {
                    acceptorList[1].AnalyzeFile(fileInfo, kpiDataGroup);
                }
                else if (fileInfo.Name.Contains("短呼") || fileInfo.Name.Contains("空扰"))
                {
                    acceptorList[0].AnalyzeFile(fileInfo, kpiDataGroup);
                }
                else if (fileInfo.Name.Contains("优化后") && fileInfo.Name.Contains("加扰"))
                {
                    acceptorList[3].AnalyzeFile(fileInfo, kpiDataGroup);
                }
                else if (fileInfo.Name.Contains("加扰"))
                {
                    acceptorList[2].AnalyzeFile(fileInfo, kpiDataGroup);
                }
            }
            catch
            {
                Clear();
                throw;
            }
        }

        private void Clear()
        {
            foreach (AnalyzerBase acp in acceptorList)
            {
                //acp.Clear();
            }
        }

        public List<ClusterResult> GetResult(double nodedistance, bool clusterType)
        {
            ShortCallAna firstShort = acceptorList[0] as ShortCallAna;
            ShortCallAna secondShort = acceptorList[1] as ShortCallAna;
            LongCallAna firstLong = acceptorList[2] as LongCallAna;
            LongCallAna secondLong = acceptorList[3] as LongCallAna;
            CSFBAna firstCSFB = acceptorList[4] as CSFBAna;
            CSFBAna secondCSFB = acceptorList[5] as CSFBAna;

            List<ClusterResult> results = new List<ClusterResult>();
            ClusterResult result = new ClusterResult(0, secondShort.ClusterSN, nodedistance);
            results.Add(result);

            result = new ClusterResult(1, firstShort.dateTime, secondShort.dateTime);
            results.Add(result);
            result = new ClusterResult(2, 0, 0, clusterType);
            results.Add(result);
            result = new ClusterResult(3, firstShort.RSRP, secondShort.RSRP, clusterType);
            results.Add(result);
            result = new ClusterResult(4, firstShort.SINR, secondShort.SINR, clusterType);
            results.Add(result);
            result = new ClusterResult(5, firstShort.AveSINR, secondShort.AveSINR, clusterType);
            results.Add(result);
            result = new ClusterResult(6, firstLong.RSRP, secondLong.RSRP, clusterType);
            results.Add(result);
            result = new ClusterResult(7, firstLong.SINR, secondLong.SINR, clusterType);
            results.Add(result);
            result = new ClusterResult(8, firstLong.AveSINR, secondLong.AveSINR, clusterType);
            results.Add(result);
            //result = new ClusterResult(9, firstLong.RoadCovRate, secondLong.RoadCovRate, clusterType);
            //results.Add(result);
            //result = new ClusterResult(10, firstLong.LTERate, secondLong.LTERate, clusterType);
            //results.Add(result);
            result = new ClusterResult(9, 0, 0, clusterType);
            results.Add(result);
            result = new ClusterResult(10, firstShort.SuccessRate, secondShort.SuccessRate, clusterType);
            results.Add(result);
            result = new ClusterResult(11, firstShort.ConnectDelay, secondShort.ConnectDelay, clusterType);
            results.Add(result);
            result = new ClusterResult(12, firstShort.DropRate, secondShort.DropRate, clusterType);
            results.Add(result);
            result = new ClusterResult(13, firstLong.HandoverRate, secondLong.HandoverRate, clusterType);
            results.Add(result);
            result = new ClusterResult(14, firstLong.HandoverDelay, secondLong.HandoverDelay, clusterType);
            results.Add(result);
            result = new ClusterResult(15, firstLong.UserHandoverDelay, secondLong.UserHandoverDelay, clusterType);
            results.Add(result);
            result = new ClusterResult(16, firstLong.L2Down, secondLong.L2Down, clusterType);
            results.Add(result);
            result = new ClusterResult(17, firstLong.L2Up, secondLong.L2Up, clusterType);
            results.Add(result);
            result = new ClusterResult(18, firstCSFB.CSFBRate, secondCSFB.CSFBRate, clusterType);
            results.Add(result);
            result = new ClusterResult(19, firstCSFB.CSFBDelay, secondCSFB.CSFBDelay, clusterType);
            results.Add(result);

            return results;

        }
    }
}
