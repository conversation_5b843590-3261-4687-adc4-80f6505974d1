﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class CompeteBasePanel : UserControl
    {
        private QueryCondition baseCond = null;

        public CompeteBasePanel()
        {
            InitializeComponent();

            DateTime dtNow = DateTime.Now.Date;
            dateTimePickerStart.Value = dtNow.AddDays(-1);
            dateTimePickerEnd.Value = dtNow.AddDays(1).AddMilliseconds(-1);

            attributePanelProj.SetAttribute("Project", "项目来源");
            checkEditFilter_CheckedChanged(null, null);
        }

        private void checkEditFilter_CheckedChanged(object sender, EventArgs e)
        {
            bool bCheck = checkEditFilter.Checked;
            radioGroupFileFilter.Enabled = bCheck;
            textBoxFileName.Enabled = bCheck;
        }

        public void SetCondition(QueryCondition cond)
        {
            this.baseCond = cond;

            refreshCond();
        }

        private void refreshCond()
        {
            if (baseCond == null) return;

            if (baseCond.Periods.Count > 0)
            {
                dateTimePickerStart.Value = baseCond.Periods[0].BeginTime;
                dateTimePickerEnd.Value = baseCond.Periods[0].EndTime;
            }

            attributePanelProj.FillData(baseCond.Projects);

            radioGroupFileFilter.SelectedIndex = (int)baseCond.NameFilterType;
            textBoxFileName.Text = baseCond.FileName;

            checkEditFilter.Checked = textBoxFileName.Text.Trim() != string.Empty;
        }

        public QueryCondition GetCondition()
        {
            if (baseCond == null)
                baseCond = new QueryCondition();

            baseCond.Periods.Clear();
            baseCond.Periods.Add(new MasterCom.Util.TimePeriod(dateTimePickerStart.Value, dateTimePickerEnd.Value));

            baseCond.Projects = attributePanelProj.GetAttribute();
            baseCond.FileName = null;
            if (checkEditFilter.Checked && textBoxFileName.Text.Trim() != "")
            {
                int num = 0;
                baseCond.FileName = QueryCondition.MakeFileFilterString(textBoxFileName.Text.Trim(), ref num);
                baseCond.FileNameOrNum = num;
                baseCond.NameFilterType = (FileFilterType)radioGroupFileFilter.SelectedIndex;
            }

            return baseCond;
        }

        public bool CheckCondition()
        {
            if (dateTimePickerStart.Value > dateTimePickerEnd.Value)
            {
                MessageBox.Show("开始时间大于结束时间...", "提示");
                return false;
            }
            else if (attributePanelProj.GetAttribute().Count == 0)
            {
                MessageBox.Show("未设置项目来源...", "提示");
                return false;
            }
            return true;
        }
    }
}
