﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas
{
    public partial class SettingDlgLTE : BaseDialog
    {
        public SettingDlgLTE()
        {
            InitializeComponent();
        }

        public void SetCondition(LowSpeedConditionLTE cond)
        {
            if (cond == null)
            {
                return;
            }
            radioApp.Checked = cond.IsAppSpeed;
            radioPdcp.Checked = !radioApp.Checked;
            chkFTPDL.Checked = cond.CheckFTPDL;
            chkFTPUL.Checked = cond.CheckFTPUL;
            chkEmail.Checked = cond.CheckEmail;
            chkHttp.Checked = cond.CheckHTTP;
            chkVideo.Checked = cond.CheckVideo;
            chkPdcpDl.Checked = cond.CheckPdcpDl;
            chkPdcpUl.Checked = cond.CheckPdcpUl;

            numEmailMax.Value = (decimal)cond.EmailRateMax;
            numFTPDLMax.Value = (decimal)cond.FTPDLRateMax;
            numFTPULMax.Value = (decimal)cond.FTPULRateMax;
            numHTTPMax.Value = (decimal)cond.HTTPRateMax;
            numVideoMax.Value = (decimal)cond.VideoRateMax;

            numEmailMin.Value = (decimal)cond.EmailRateMin;
            numFTPDLMin.Value = (decimal)cond.FTPDLRateMin;
            numFTPULMin.Value = (decimal)cond.FTPULRateMin;
            numHTTPMin.Value = (decimal)cond.HTTPRateMin;
            numVideoMin.Value = (decimal)cond.VideoRateMin;

            numFTPDLDistance.Value = (decimal)cond.DistanceFTPDLMin;
            numFTPULDistance.Value = (decimal)cond.DistanceFTPULMin;
            numEMailDistance.Value = (decimal)cond.DistanceEMailMin;
            numHTTPDistance.Value = (decimal)cond.DistanceHTTPMin;
            numVideoDistance.Value = (decimal)cond.DistanceVideoMin;
            num2TpDis.Value = (decimal)cond.TestPointDistance;

            cmbDlRateUnit.SelectedIndex = cond.PdcpDlRateUnit;
            cmbUlRateUnit.SelectedIndex = cond.PdcpUlRateUnit;
            numPdcpUlDistance.Value = (decimal)cond.DistancePdcpUlMin;
            numPdcpDlDistance.Value = (decimal)cond.DistancePdcpDlMin;

            if (cond.PdcpDlRateUnit == 1)
            {
                numPdcpDlMax.Value = (decimal)cond.PdcpDlRateMax * 1024;
                numPdcpDlMin.Value = (decimal)cond.PdcpDlRateMin * 1024;
            }
            else
            {
                numPdcpDlMax.Value = (decimal)cond.PdcpDlRateMax;
                numPdcpDlMin.Value = (decimal)cond.PdcpDlRateMin;
            }
            if (cond.PdcpUlRateUnit == 1)
            {
                numPdcpUlMax.Value = (decimal)cond.PdcpUlRateMax * 1024;
                numPdcpUlMin.Value = (decimal)cond.PdcpUlRateMin * 1024;
            }
            else
            {
                numPdcpUlMax.Value = (decimal)cond.PdcpUlRateMax;
                numPdcpUlMin.Value = (decimal)cond.PdcpUlRateMin;
            }

            chkRsrp.Checked = cond.CheckRsrp;
            numRsrpMin.Value = (decimal)cond.RsrpMin;
            numRsrpMax.Value = (decimal)cond.RsrpMax;

            chkSinr.Checked = cond.CheckSinr;
            numSinrMin.Value = (decimal)cond.SinrMin;
            numSinrMax.Value = (decimal)cond.SinrMax;

            chkHo.Checked = cond.CheckHo;
            numHoSecond.Value = (decimal)cond.HoSecond;

            numLowPer.Value = (decimal)cond.LowPercent;

            //网络
            this.checkBoxSynthesisLowSpeed.Checked = cond.CheckSynthesisLowSpeed;
            this.checkBoxLTELowSpeed.Checked = cond.CheckLTELowSpeed;
            this.checkBoxLowSpeed_TDOrW.Checked = cond.CheckLowSpeed_TDOrW;
            this.checkBoxGSMLowSpeed.Checked = cond.CheckGSMLowSpeed;
        }

        public void WCDMAReplaceTD()
        {
          this.checkBoxLowSpeed_TDOrW.Text = "WCDMA低速率路段";
        }

        public LowSpeedConditionLTE GetCondition()
        {
            LowSpeedConditionLTE cond = new LowSpeedConditionLTE();
            cond.IsAppSpeed = radioApp.Checked;
            cond.CheckFTPDL = chkFTPDL.Checked;
            cond.CheckFTPUL = chkFTPUL.Checked;
            cond.CheckEmail = chkEmail.Checked;
            cond.CheckHTTP = chkHttp.Checked;
            cond.CheckVideo = chkVideo.Checked;

            cond.EmailRateMax = (double)numEmailMax.Value;
            cond.FTPDLRateMax = (double)numFTPDLMax.Value;
            cond.FTPULRateMax = (double)numFTPULMax.Value;
            cond.HTTPRateMax = (double)numHTTPMax.Value;
            cond.VideoRateMax = (double)numVideoMax.Value;

            cond.EmailRateMin = (double)numEmailMin.Value;
            cond.FTPDLRateMin = (double)numFTPDLMin.Value;
            cond.FTPULRateMin = (double)numFTPULMin.Value;
            cond.HTTPRateMin = (double)numHTTPMin.Value;
            cond.VideoRateMin = (double)numVideoMin.Value;

            cond.DistanceFTPDLMin = (double)numFTPDLDistance.Value;
            cond.DistanceFTPULMin = (double)numFTPULDistance.Value;
            cond.DistanceEMailMin = (double)numEMailDistance.Value;
            cond.DistanceHTTPMin = (double)numHTTPDistance.Value;
            cond.DistanceVideoMin = (double)numVideoDistance.Value;

            cond.CheckPdcpDl = chkPdcpDl.Checked;
            cond.CheckPdcpUl = chkPdcpUl.Checked;
            cond.PdcpDlRateUnit = cmbDlRateUnit.SelectedIndex;
            cond.PdcpUlRateUnit = cmbUlRateUnit.SelectedIndex;

            if (cond.PdcpDlRateUnit == 1)
            {
                cond.PdcpDlRateMax = (double)numPdcpDlMax.Value / 1024.0;
                cond.PdcpDlRateMin = (double)numPdcpDlMin.Value / 1024.0;
            }
            else
            {
                cond.PdcpDlRateMax = (double)numPdcpDlMax.Value;
                cond.PdcpDlRateMin = (double)numPdcpDlMin.Value;
            }
            if (cond.PdcpUlRateUnit == 1)
            {
                cond.PdcpUlRateMax = (double)numPdcpUlMax.Value / 1024.0;
                cond.PdcpUlRateMin = (double)numPdcpUlMin.Value / 1024.0;
            }
            else
            {
                cond.PdcpUlRateMax = (double)numPdcpUlMax.Value;
                cond.PdcpUlRateMin = (double)numPdcpUlMin.Value;
            }
            cond.DistancePdcpDlMin = (double)numPdcpDlDistance.Value;
            cond.DistancePdcpUlMin = (double)numPdcpUlDistance.Value;

            cond.CheckHo = chkHo.Checked;
            cond.HoSecond = (int)numHoSecond.Value;

            cond.CheckRsrp = chkRsrp.Checked;
            cond.RsrpMax = (float)numRsrpMax.Value;
            cond.RsrpMin = (float)numRsrpMin.Value;

            cond.CheckSinr = chkSinr.Checked;
            cond.SinrMax = (float)numSinrMax.Value;
            cond.SinrMin = (float)numSinrMin.Value;

            cond.TestPointDistance = (double)num2TpDis.Value;
            cond.LowPercent = (double)numLowPer.Value;

            //网络
            cond.CheckSynthesisLowSpeed = this.checkBoxSynthesisLowSpeed.Checked;
            cond.CheckLTELowSpeed = this.checkBoxLTELowSpeed.Checked;
            cond.CheckLowSpeed_TDOrW = this.checkBoxLowSpeed_TDOrW.Checked;
            cond.CheckGSMLowSpeed = this.checkBoxGSMLowSpeed.Checked;

            return cond;
        }

        public LowSpeedConditionLTEFDD GetLTEFDDCondition()
        {
            LowSpeedConditionLTEFDD cond = new LowSpeedConditionLTEFDD();
            cond.IsAppSpeed = radioApp.Checked;
            cond.CheckFTPDL = chkFTPDL.Checked;
            cond.CheckFTPUL = chkFTPUL.Checked;
            cond.CheckEmail = chkEmail.Checked;
            cond.CheckHTTP = chkHttp.Checked;
            cond.CheckVideo = chkVideo.Checked;

            cond.EmailRateMax = (double)numEmailMax.Value;
            cond.FTPDLRateMax = (double)numFTPDLMax.Value;
            cond.FTPULRateMax = (double)numFTPULMax.Value;
            cond.HTTPRateMax = (double)numHTTPMax.Value;
            cond.VideoRateMax = (double)numVideoMax.Value;

            cond.DistanceFTPDLMin = (double)numFTPDLDistance.Value;
            cond.DistanceFTPULMin = (double)numFTPULDistance.Value;
            cond.DistanceEMailMin = (double)numEMailDistance.Value;
            cond.DistanceHTTPMin = (double)numHTTPDistance.Value;
            cond.DistanceVideoMin = (double)numVideoDistance.Value;
            cond.TestPointDistance = (double)num2TpDis.Value;

            cond.EmailRateMin = (double)numEmailMin.Value;
            cond.FTPDLRateMin = (double)numFTPDLMin.Value;
            cond.FTPULRateMin = (double)numFTPULMin.Value;
            cond.HTTPRateMin = (double)numHTTPMin.Value;
            cond.VideoRateMin = (double)numVideoMin.Value;

            cond.CheckPdcpDl = chkPdcpDl.Checked;
            cond.CheckPdcpUl = chkPdcpUl.Checked;
            cond.PdcpDlRateUnit = cmbDlRateUnit.SelectedIndex;
            cond.PdcpUlRateUnit = cmbUlRateUnit.SelectedIndex;

            if (cond.PdcpDlRateUnit == 1)
            {
                cond.PdcpDlRateMax = (double)numPdcpDlMax.Value / 1024.0;
                cond.PdcpDlRateMin = (double)numPdcpDlMin.Value / 1024.0;
            }
            else
            {
                cond.PdcpDlRateMax = (double)numPdcpDlMax.Value;
                cond.PdcpDlRateMin = (double)numPdcpDlMin.Value;
            }
            if (cond.PdcpUlRateUnit == 1)
            {
                cond.PdcpUlRateMax = (double)numPdcpUlMax.Value / 1024.0;
                cond.PdcpUlRateMin = (double)numPdcpUlMin.Value / 1024.0;
            }
            else
            {
                cond.PdcpUlRateMax = (double)numPdcpUlMax.Value;
                cond.PdcpUlRateMin = (double)numPdcpUlMin.Value;
            }

            cond.DistancePdcpDlMin = (double)numPdcpDlDistance.Value;
            cond.DistancePdcpUlMin = (double)numPdcpUlDistance.Value;

            cond.CheckHo = chkHo.Checked;
            cond.HoSecond = (int)numHoSecond.Value;

            cond.CheckRsrp = chkRsrp.Checked;
            cond.RsrpMax = (float)numRsrpMax.Value;
            cond.RsrpMin = (float)numRsrpMin.Value;

            cond.CheckSinr = chkSinr.Checked;
            cond.SinrMax = (float)numSinrMax.Value;
            cond.SinrMin = (float)numSinrMin.Value;

            cond.LowPercent = (double)numLowPer.Value;

            //网络
            cond.CheckSynthesisLowSpeed = this.checkBoxSynthesisLowSpeed.Checked;
            cond.CheckLTELowSpeed = this.checkBoxLTELowSpeed.Checked;
            cond.CheckLowSpeed_TDOrW = this.checkBoxLowSpeed_TDOrW.Checked;
            cond.CheckGSMLowSpeed = this.checkBoxGSMLowSpeed.Checked;

            return cond;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            if (radioApp.Checked
                && !chkEmail.Checked
                && !chkFTPDL.Checked
                && !chkFTPUL.Checked
                && !chkHttp.Checked 
                && !chkVideo.Checked)
            {
                MessageBox.Show("请至少选取一种速率！");
                return;
            }

            if (chkRsrp.Checked && numRsrpMin.Value > numRsrpMax.Value)
            {
                MessageBox.Show("最小RSRP不能大于最大RSRP！");
                return;
            }

            if (chkSinr.Checked && numSinrMin.Value > numSinrMax.Value)
            {
                MessageBox.Show("最小SINR不能大于最大SINR！");
                return;
            }

            if (!checkBoxSynthesisLowSpeed.Checked &&
                !checkBoxLTELowSpeed.Checked &&
                !checkBoxLowSpeed_TDOrW.Checked &&
                !checkBoxGSMLowSpeed.Checked)
            {
                MessageBox.Show("请至少选择一种网络！");
                return;
            }

            DialogResult = DialogResult.OK;
        }

        private void radioApp_CheckedChanged(object sender, EventArgs e)
        {
            grpPdcp.Enabled = !radioApp.Checked;
        }

        private void chkRsrp_CheckedChanged(object sender, EventArgs e)
        {
            numRsrpMax.Enabled = numRsrpMin.Enabled = chkRsrp.Checked;
        }

        private void chkSinr_CheckedChanged(object sender, EventArgs e)
        {
            numSinrMax.Enabled = numSinrMin.Enabled = chkSinr.Checked;
        }

        private void chkHo_CheckedChanged(object sender, EventArgs e)
        {
            numHoSecond.Enabled = chkHo.Checked;
        }

        private void radioPdcp_CheckedChanged(object sender, EventArgs e)
        {
            groupBox2.Enabled = !radioPdcp.Checked;
        }

    }
}
