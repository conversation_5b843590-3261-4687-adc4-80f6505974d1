﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause
{
    [Serializable]
    public class RepeatCoverCause : CauseBase
    {
        public override string Name
        {
            get { return "重叠覆盖"; }
        }
        
        public float RSRPDiff { get; set; } = 10;
        public int Second { get; set; } = 5;
        public override string Desc
        {
            get
            {
                return string.Format("在低速率发生{0}秒内，主服小区以及邻区电平值在{1}dB以内", Second, RSRPDiff);
            }
        }

        public override string Suggestion
        {
            get
            {
                return null;
            }
        }

        public override void Judge(LowSpeedSeg segItem, List<Event> evts, List<TestPoint> allTP)
        {
            foreach (TestPoint pnt in segItem.TestPoints)
            {
                if (segItem.IsNeedJudge(pnt))
                {
                    //记录低速率前n秒时间
                    int bTime = pnt.Time - Second;
                    foreach (TestPoint testPoint in allTP)
                    {
                        dealTPInfo(segItem, pnt, bTime, testPoint);
                    }
                }
            }
        }

        private void dealTPInfo(LowSpeedSeg segItem, TestPoint pnt, int bTime, TestPoint testPoint)
        {
            if (bTime <= testPoint.Time && testPoint.Time <= pnt.Time)
            {
                float? rsrp = (float?)GetRSRP(testPoint);
                for (int i = 0; i < 10; i++)
                {
                    float? nRsrp = (float?)GetNRSRP(testPoint, i);
                    //主服小区以及邻区电平值在n dB以内
                    if (nRsrp != null && rsrp != null
                            && Math.Abs((float)nRsrp - (float)rsrp) <= RSRPDiff)
                    {
                        RepeatCoverCause cln = this.Clone() as RepeatCoverCause;
                        segItem.SetReason(new LowSpeedPointDetail(testPoint, cln));
                        break;
                    }
                }
            }
        }

        protected object GetRSRP(TestPoint tp)
        {
            if (tp is LTEFddTestPoint)
            {
                return tp["lte_fdd_RSRP"];
            }
            return tp["lte_RSRP"];
        }
        protected object GetNRSRP(TestPoint tp, int index)
        {
            if (tp is LTEFddTestPoint)
            {
                return tp["lte_fdd_NCell_RSRP", index];
            }
            return tp["lte_NCell_RSRP", index];
        }

        public override Dictionary<string, object> CfgParam
        {
            get
            {
                Dictionary<string, object> paramDic = new Dictionary<string, object>();
                paramDic["TypeName"] = this.GetType().FullName;

                paramDic["rsrpDiff"] = this.RSRPDiff;
                paramDic["second"] = this.Second;

                return paramDic;
            }
            set
            {
                if (value == null)
                {
                    return;
                }

                this.RSRPDiff = (float)value["rsrpDiff"];
                this.Second = (int)value["second"];
            }
        }
    }
}
