using System;
using System.Collections.Generic;
using System.Text;
using System.Collections;

namespace MasterCom.Util
{
    public class SnapshotManager<T>:AConfig_Ext
    {
        public List<Snapshot<T>> Snapshots
        {
            get { return snapshots; }
        }

        public Snapshot<T> this[int id]
        {
            get
            {
                if (idSnapshotMap.ContainsKey(id))
                {
                    return idSnapshotMap[id];
                }
                return null;
            }
        }

        public List<T> GetAll(DateTime time)
        {
            List<T> list = new List<T>();
            foreach (Snapshot<T> temp in snapshots)
            {
                T t = temp.Get(time);
                if (t != null)
                {
                    list.Add(t);
                }
            }
            return list;
        }

        public T Get(int id, DateTime time)
        {
            if (idSnapshotMap.ContainsKey(id))
            {
                return idSnapshotMap[id].Get(time);
            }
            return Snapshot<T>.Empty;
        }

        public List<List<T>> GetAll(TimePeriod period)
        {
            List<List<T>> list = new List<List<T>>();
            foreach (Snapshot<T> temp in snapshots)
            {
                List<T> tempList = temp.GetAll(period);
                if (tempList.Count > 0)
                {
                    list.Add(tempList);
                }
            }
            return list;
        }

        public List<T> GetAll(int id, TimePeriod period)
        {
            if (idSnapshotMap.ContainsKey(id))
            {
                return idSnapshotMap[id].GetAll(period);
            }
            return new List<T>();
        }

        public List<T> GetCurrent()
        {
            List<T> list = new List<T>();
            foreach (Snapshot<T> temp in snapshots)
            {
                if (temp.CurrentSnapshot != null)
                {
                    list.Add(temp.Current);
                }
            }
            return list;
        }

        public int MaxId
        {
            get
            {
                int maxId = 0;
                foreach (int i in idSnapshotMap.Keys)
                {
                    maxId = maxId > i ? maxId : i;
                }
                return maxId;
            }
        }

        public T GetCurrent(int id)
        {
            if (idSnapshotMap.ContainsKey(id))
            {
                return idSnapshotMap[id].Current;
            }
            return Snapshot<T>.Empty;
        }

        public T GetLast(int id)
        {
            if (idSnapshotMap.ContainsKey(id))
            {
                return idSnapshotMap[id].Last;
            }
            return Snapshot<T>.Empty;
        }

        public void Clear()
        {
            snapshots.Clear();
            idSnapshotMap.Clear();
        }
        public void Remove(Snapshot<T> snapShot)
        {
            if (idSnapshotMap.ContainsKey(snapShot.ID))
            {
                idSnapshotMap.Remove(snapShot.ID);
            }
            if (snapshots.Contains(snapShot))
            {
                snapshots.Remove(snapShot);
            }
        }

        public void Add(Snapshot<T> snapShot)
        {
            if (idSnapshotMap.ContainsKey(snapShot.ID))
            {
                idSnapshotMap[snapShot.ID].Add(snapShot);
            }
            else
            {
                snapshots.Add(snapShot);
                idSnapshotMap[snapShot.ID] = snapShot;
            }
        }

        public void Add(Snapshot<T> snapShot,bool isLast)
        {
            if (idSnapshotMap.ContainsKey(snapShot.ID))
            {
                idSnapshotMap[snapShot.ID].Add(snapShot, isLast);
            }
            else
            {
                snapshots.Add(snapShot);
                idSnapshotMap[snapShot.ID] = snapShot;
            }
        }

        private readonly List<Snapshot<T>> snapshots = new List<Snapshot<T>>();

        private readonly Dictionary<int, Snapshot<T>> idSnapshotMap = new Dictionary<int, Snapshot<T>>();

        public override Dictionary<string, object> ConfigParames
        {
            get
            {
                Dictionary<string, object> objs = new Dictionary<string, object>();
                objs.Add("snapshots", getGetData(snapshots));
                return objs;
            }
            set
            {
                if (value == null) return;
                snapshots.Clear();
                idSnapshotMap.Clear();
                object obj = getSetData(typeof(List<Snapshot<T>>), value["snapshots"]);
                if (obj is IList<Snapshot<T>>) snapshots.AddRange(obj as IList<Snapshot<T>>);
                foreach(Snapshot<T> ss in snapshots)
                {
                    if (idSnapshotMap.ContainsKey(ss.ID))
                    {
                        idSnapshotMap[ss.ID].Add(ss);
                    }
                    else
                    {
                        idSnapshotMap[ss.ID] = ss;
                    }
                }
            }
        }
    }
}
