﻿using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using System;
using System.Collections.Generic;

namespace MasterCom.RAMS.ZTFunc
{
    public class DiyQueryFddFusionData : DiyQueryFddDataBase
    {
        protected int cellID = 0;
        protected string tableName = "";
        private List<FddFusionBtsCellData> fusionBtsCellInfo = null;
        public List<FddFusionBtsCellData> FusionBtsCellInfo
        {
            get { return fusionBtsCellInfo; }
        }

        public DiyQueryFddFusionData()
            : base()
        { }

        public override string Name
        {
            get
            {
                return "查询FDD单验性能数据";
            }
        }

        public virtual void SetCondition(int btsID, int cellID, string testDate)
        {
            this.btsID = btsID;
            this.cellID = cellID;
            tableName = "tb_xinjiang_fusion_perf_" + testDate;
        }

        protected override string getSqlTextString()
        {
            string selectSQL = string.Format(@"select top 1 [EnodebID],[CellID],[RRC连接尝试次数],[RRC连接建立成功率],[E_RAB连接尝试次数],[E_RAB建立成功率],[无线接通率],[无线掉线率],[E_RAB掉线率],[系统内切换成功率],[PDCP_UpLoadData_Mb],[PDCP_DownLoadData_Mb] FROM {0} where [EnodebID]={1} and [CellID] = {2}", tableName, btsID, cellID);
            return selectSQL;
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            int idx = 0;
            E_VType[] rType = new E_VType[12];
            rType[idx++] = E_VType.E_Int;
            rType[idx++] = E_VType.E_Int;
            rType[idx++] = E_VType.E_Int;
            rType[idx++] = E_VType.E_Float;
            rType[idx++] = E_VType.E_Int;
            rType[idx++] = E_VType.E_Float;
            rType[idx++] = E_VType.E_Float;
            rType[idx++] = E_VType.E_Float;
            rType[idx++] = E_VType.E_Float;
            rType[idx++] = E_VType.E_Float;
            rType[idx++] = E_VType.E_Float;
            rType[idx] = E_VType.E_Float;
            return rType;
        }

        protected override void initData()
        {
            fusionBtsCellInfo = new List<FddFusionBtsCellData>();
        }

        protected override void dealReceiveData(Package package)
        {
            FddFusionBtsCellData btsCell = new FddFusionBtsCellData();
            btsCell.FillData(package);
            FusionBtsCellInfo.Add(btsCell);
        }
    }

    public class FddFusionBtsCellData : BtsCellData
    {
        #region 基础信息
        protected int cellID;
        public int CellID
        {
            get { return cellID; }
        }

        protected int rrcConnectCount;
        /// <summary>
        /// RRC连接尝试次数
        /// </summary>
        public int RrcConnectCount
        {
            get { return rrcConnectCount; }
        }

        protected double rrcSuccessRate;
        /// <summary>
        /// RRC连接建立成功率
        /// </summary>
        public double RrcSuccessRate
        {
            get { return rrcSuccessRate; }
        }

        protected int eRABConnectCount;
        /// <summary>
        /// E-RAB连接尝试次数
        /// </summary>
        public int ERABConnectCount
        {
            get { return eRABConnectCount; }
        }

        protected double eRABSuccessRate;
        /// <summary>
        /// E-RAB建立成功率
        /// </summary>
        public double ERABSuccessRate
        {
            get { return eRABSuccessRate; }
        }

        protected double wirelessRate;
        /// <summary>
        /// 无线接通率
        /// </summary>
        public double WirelessRate
        {
            get { return wirelessRate; }
        }

        protected double wirelessDropRate;
        /// <summary>
        /// 无线掉线率
        /// </summary>
        public double WirelessDropRate
        {
            get { return wirelessDropRate; }
        }

        protected double eRABDropRate;
        /// <summary>
        /// E-RAB掉线率
        /// </summary>
        public double ERABDropRate
        {
            get { return eRABDropRate; }
        }

        protected double handOverSuccessRate;
        /// <summary>
        /// 系统内切换成功率
        /// </summary>
        public double HandOverSuccessRate
        {
            get { return handOverSuccessRate; }
        }

        protected double ulPDCPThroughput;
        /// <summary>
        /// 小区PDCP层所接收到的上行数据的总吞吐量（Gb）
        /// </summary>
        public double ULPDCPThroughput
        {
            get { return ulPDCPThroughput; }
        }

        protected double dlPDCPThroughput;
        /// <summary>
        /// 小区PDCP层所接收到的下行数据的总吞吐量（Gb）
        /// </summary>
        public double DLPDCPThroughput
        {
            get { return dlPDCPThroughput; }
        }
        #endregion

        public override void FillData(Package package)
        {
            ENodeBID = package.Content.GetParamInt();
            cellID = package.Content.GetParamInt();
            rrcConnectCount = package.Content.GetParamInt();

            rrcSuccessRate = getRealRate(package.Content.GetParamInt());
            eRABConnectCount = package.Content.GetParamInt();
            eRABSuccessRate = getRealRate(package.Content.GetParamInt());
            wirelessRate = getRealRate(package.Content.GetParamInt());
            wirelessDropRate = getRealRate(package.Content.GetParamInt());
            eRABDropRate = getRealRate(package.Content.GetParamInt());
            handOverSuccessRate = getRealRate(package.Content.GetParamInt());

            ulPDCPThroughput = changeMbIntoGb(package.Content.GetParamInt());
            dlPDCPThroughput = changeMbIntoGb(package.Content.GetParamInt());
        }

        private double getRealRate(int value)
        {
            //100000d是 1000d * 100
            //除1000d是由于使用float类型时存在精度损失导致数据异常,故这里直接获取int值再除1000d转为double
            //除100是由于在数据入库时乘过100
            double result = value / 100000d;
            return result;
        }

        private double changeMbIntoGb(int value)
        {
            double result = value / 1024000d;
            return result;
        }
    }

    public class FddFusionBtsCellDataResult
    {
        #region 多天平均数据
        private readonly List<FddFusionBtsCellData> dataList = new List<FddFusionBtsCellData>();
        public List<FddFusionBtsCellData> DataList
        {
            get { return dataList; }
        }
        public void Add(FddFusionBtsCellData data)
        {
            dataList.Add(data);
        }

        public int AvgRrcConnectCount
        {
            get
            {
                int totalCount = 0;
                foreach (var item in dataList)
                {
                    totalCount += item.RrcConnectCount;
                }
                return totalCount / dataList.Count;
            }
        }

        public string AvgRrcSuccessRate
        {
            get
            {
                double totalRate = 0;
                foreach (var item in dataList)
                {
                    totalRate += item.RrcSuccessRate;
                }
                string totalRateStr = Math.Round(totalRate / dataList.Count, 5).ToString();
                return totalRateStr;
            }
        }

        public int AvgERABConnectCount
        {
            get
            {
                int totalCount = 0;
                foreach (var item in dataList)
                {
                    totalCount += item.ERABConnectCount;
                }
                return totalCount / dataList.Count;
            }
        }

        public string AvgERABSuccessRate
        {
            get
            {
                double totalRate = 0;
                foreach (var item in dataList)
                {
                    totalRate += item.ERABSuccessRate;
                }
                string totalRateStr = Math.Round(totalRate / dataList.Count, 5).ToString();
                return totalRateStr;
            }
        }

        public string AvgWirelessRate
        {
            get
            {
                double totalRate = 0;
                foreach (var item in dataList)
                {
                    totalRate += item.WirelessRate;
                }
                string totalRateStr = Math.Round(totalRate / dataList.Count, 5).ToString();
                return totalRateStr;
            }
        }

        public string AvgWirelessDropRate
        {
            get
            {
                double totalRate = 0;
                foreach (var item in dataList)
                {
                    totalRate += item.WirelessDropRate;
                }
                //转(decimal)是为了避免数据过小时转成科学记数法显示
                string totalRateStr = ((decimal)Math.Round(totalRate / dataList.Count, 5)).ToString();
                return totalRateStr;
            }
        }

        public string AvgERABDropRate
        {
            get
            {
                double totalRate = 0;
                foreach (var item in dataList)
                {
                    totalRate += item.ERABDropRate;
                }
                string totalRateStr = ((decimal)Math.Round(totalRate / dataList.Count, 5)).ToString();
                return totalRateStr;
            }
        }

        public string AvgHandOverSuccessRate
        {
            get
            {
                double totalRate = 0;
                foreach (var item in dataList)
                {
                    totalRate += item.HandOverSuccessRate;
                }
                string totalRateStr = Math.Round(totalRate / dataList.Count, 5).ToString();
                return totalRateStr;
            }
        }

        public string AvgULPDCPThroughput
        {
            get
            {
                double total = 0;
                foreach (var item in dataList)
                {
                    total += item.ULPDCPThroughput;
                }
                string totalStr = ((decimal)Math.Round(total / dataList.Count, 3)).ToString();
                return totalStr;
            }
        }

        public string AvgDLPDCPThroughput
        {
            get
            {
                double total = 0;
                foreach (var item in dataList)
                {
                    total += item.DLPDCPThroughput;
                }
                string totalStr = ((decimal)Math.Round(total / dataList.Count, 3)).ToString();
                return totalStr;
            }
        }
        #endregion
    }
}
