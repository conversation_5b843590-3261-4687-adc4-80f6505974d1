﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.MTGis;
using MasterCom.RAMS.Model;
using System.Drawing;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public class BadRxQualRegionDrawLayer : CustomDrawLayer
    {
        public BadRxQualRegionDrawLayer(MapOperation mp, string name)
            : base(mp, name)
        {
        }

        private List<BadRxqualBlock> badBlockList;
        public void FillData(List<BadRxqualBlock> badBlockList)
        {
            this.badBlockList = badBlockList;
        }

        public override void Draw(Rectangle clientRect, Rectangle updateRect, Graphics graphics)
        {
            float ratio = CustomDrawLayer.GetTestPointDisplayRatio(Map.Scale);
            foreach (BadRxqualBlock block in badBlockList)
            {
                DbPoint pntTL = new DbPoint(block.GetBound().x1, block.GetBound().y2);
                PointF pntFTL;
                this.Map.ToDisplay(pntTL, out pntFTL);
                DbPoint pntBr = new DbPoint(block.GetBound().x2, block.GetBound().y1);
                PointF pntFBr;
                this.Map.ToDisplay(pntBr, out pntFBr);
                //矩形
                RectangleF rect = new RectangleF(pntFTL.X - 2 * ratio * 150, pntFTL.Y - 2 * ratio * 150, (pntFBr.X - pntFTL.X) + 4 * ratio * 150, (pntFBr.Y - pntFTL.Y) + 4 * ratio * 150);
                Pen recPen = new Pen(Color.MediumVioletRed, 3);
                graphics.DrawRectangle(recPen, rect.Left, rect.Top, rect.Width, rect.Height);
                //椭圆
                //if (pntFBr.X - pntFTL.X == 0)
                //{
                //    graphics.DrawEllipse(new Pen(Color.Green), pntFTL.X - 5, pntFTL.Y - 4, pntFBr.X - pntFTL.X + 10, pntFBr.Y - pntFTL.Y + 8);
                //}
                //else
                //{
                //    graphics.DrawEllipse(new Pen(Color.Green), pntFTL.X - 2 * ratio * 250, pntFTL.Y - 1 * ratio * 250, pntFBr.X - pntFTL.X + 4 * ratio * 250, pntFBr.Y - pntFTL.Y + 2 * ratio * 250);
                //}
                graphics.DrawString(block.ID.ToString(), new Font(new FontFamily("宋体"), 18, FontStyle.Regular), Brushes.Green, pntFTL.X + (pntFBr.X - pntFTL.X) / 2, pntFTL.Y + (pntFBr.Y - pntFTL.Y) / 2);
            }
        }
    }
}
