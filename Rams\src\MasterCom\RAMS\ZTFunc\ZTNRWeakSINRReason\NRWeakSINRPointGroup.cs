﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRWeakSINRPointGroup
    {
        public override bool Equals(object obj)
        {
            NRWeakSINRPointGroup other = obj as NRWeakSINRPointGroup;
            if (other==null)
            {
                return false;
            }
            return this.grpInfo.Equals(other.grpInfo);
        }
        public override string ToString()
        {
            return grpInfo.ToString();
        }
        public override int GetHashCode()
        {
            return grpInfo.GetHashCode();
        }
        private readonly object grpInfo = null;
        public object GroupInfo
        {
            get { return grpInfo; }
        }
        private int totalPntCnt = 0;
        public int TotalPntCnt
        {
            get { return totalPntCnt; }
        }
        private int weakPntCnt = 0;
        public int WeakPointCnt
        {
            get { return weakPntCnt; }
        }
        private readonly Dictionary<NRReasonBase, List<NRWeakSINRPoint>> reasonPntDic = new Dictionary<NRReasonBase, List<NRWeakSINRPoint>>();
        public Dictionary<NRReasonBase, List<NRWeakSINRPoint>> ReasonPntDic
        {
            get { return reasonPntDic; }
        }
        public List<NRWeakSINRPoint> Points
        {
            get
            {
                List<NRWeakSINRPoint> list = new List<NRWeakSINRPoint>();
                foreach (NRReasonBase item in reasonPntDic.Keys)
                {
                    list.AddRange(reasonPntDic[item]);
                }
                return list;
            }
        }
        public NRWeakSINRPointGroup(object grpItem, List<NRReasonBase> reasons)
        {
            this.grpInfo = grpItem;
            foreach (NRReasonBase item in reasons)
            {
                reasonPntDic[item] = new List<NRWeakSINRPoint>();
            }
        }

        public void AddWeakSINRPoint(NRWeakSINRPoint wp)
        {
            if (wp != null)
            {
                weakPntCnt++;
                reasonPntDic[wp.Reason].Add(wp);
            }
            else
            {
                totalPntCnt++;
            }
        }

        public void Gather(NRWeakSINRPointGroup otherGrp)
        {
            totalPntCnt += otherGrp.totalPntCnt;
            weakPntCnt += otherGrp.weakPntCnt;
            foreach (KeyValuePair<NRReasonBase, List<NRWeakSINRPoint>> kvp in otherGrp.ReasonPntDic)
            {
                reasonPntDic[kvp.Key].AddRange(kvp.Value);
            }
        }
    }
}
