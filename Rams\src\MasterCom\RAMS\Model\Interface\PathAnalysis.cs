﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Model.Interface;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.MTGis;

namespace MasterCom.RAMS.Net
{
    /// <summary>
    /// Written by WuJunHong 2012.11.3
    /// </summary>
    public class PathAnalysis : DIYReplayFileQuery
    {
        public PathAnalysis(MainModel mainModel)
            : base(mainModel)
        {
        }

        public PathAnalysis(MainModel mainModel, string regionTableName)
            : base(mainModel)
        {
            IsAddMessageToDTDataManager = false;
            IsAddSampleToDTDataManager = false;
            this.regionTableName = regionTableName;
        }
        string regionTableName { get; set; }
        public Dictionary<FileInfo, List<Event>> fileEventListDic { get; set; }  //文件对应其包含的事件字典
        public List<PathInfo> pathInfoList { get; set; }   //测试路线结果集
        protected override void query()
        {
            fileEventListDic = new Dictionary<FileInfo, List<Event>>();

            replayContentOption = getDIYReplayContent();

            WaitBox.CanCancel = true;
            WaitBox.Text = "正在查询...";
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
                WaitBox.Text = "正在查询...";
                MainModel.IsDrawEventResult = false;
                MainModel.IsFileReplayByCompareMode = Condition.isCompareMode;
                MainModel.IsFileReplayByMTRMode = condition.isMTRMode;
                MainModel.IsFileReplayByMTRToLogMode = condition.isMTRToLogMode;
                WaitBox.Show("开始统计数据...", queryInThread, clientProxy);
                doStat();
            }
            finally
            {
                //MainModel.FireDTDataChanged(this);
                clientProxy.Close();
            }
        }

        DIYReplayOptionDlg replayOptionDlg = null;
        protected override DIYReplayContentOption getDIYReplayContent()
        {
            if (replayOptionDlg == null)
            {
                replayOptionDlg = new DIYReplayOptionDlg();
                replayOptionDlg.InitLoadInfo("", "隐藏"); //modified by yht dont show 隐藏
            }
            if (Condition.FileInfos.Count > 0)
            {
                int svtype = Condition.FileInfos[0].ServiceType;
                replayOptionDlg.FillCurrentServiceType(svtype);
            }

            replayOptionDlg.DialogResult = DialogResult.OK;
            return replayOptionDlg.CurOption;
        }

        protected override void queryInThread(object o)
        {
            try
            {
                ClientProxy clientProxy = (ClientProxy)o;
                Package package = clientProxy.Package;
                int index = 0;
                foreach (MasterCom.RAMS.Model.FileInfo fileInfo in Condition.FileInfos)
                {
                    if (fileInfo.FileTypeDescription.Contains("鼎利"))
                    {//鼎利测试文件以左下角为坐标原点
                        MainModel.CQTPlanImgOrigin = MainModel.OriginType.LeftBottom;
                    }
                    else if (fileInfo.FileTypeDescription.Contains("烽火"))
                    {//烽火测试文件以左上角为坐标原点
                        MainModel.CQTPlanImgOrigin = MainModel.OriginType.LeftTop;
                    }
                    setReplayModeInfo(index, fileInfo);
                    index++;
                    fileIndex++;
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                    WaitBox.Text = "开始获取[" + fileInfo.Name + "]数据...";
                    queryReplayInfo(clientProxy, package, fileInfo);
                }
            }
            catch (DebugAssertException dbgE)
            {
                MessageBox.Show("Debug Assert 失败:" + dbgE.ToString());
            }
            catch (Exception ex)
            {
                log.Error(ex.StackTrace);
            }
            finally
            {
                WaitBox.Close();
            }
        }

        private void setReplayModeInfo(int index, FileInfo fileInfo)
        {
            if (MainModel.IsFileReplayByMTRMode)
            {
                if (index == 1)
                {
                    fileIdMTR = fileInfo.ID;
                    canGetHeaderMTR = true;
                }
                if (index > 0)
                {
                    fileOffsetTimeMS = fileInfo.OffsetTimeMS;
                }
            }
            if (MainModel.IsFileReplayByMTRToLogMode && index == 0)
            {
                fileOffsetTimeMS = fileInfo.OffsetTimeMS;
            }
        }

        FileInfo curFile;
        //回放文件只回放其中的事件
        protected override void queryReplayInfo(ClientProxy clientProxy, Package package, FileInfo fileInfo)
        {
            //event
            if (replayContentOption.EventInclude)
            {
                prepareStatPackage_Event_FileFilter(package, fileInfo);
                prepareStatPackage_Event_EventFilter(package);
                fillContentNeeded_Event(package);
                clientProxy.Send();
                curFile = fileInfo;
                recieveInfo_Event(clientProxy);
            }
            MainModel.IsFusionInclude = false;
        }

        protected override void recieveInfo_Event(ClientProxy clientProxy)
        {
            DTDataHeaderManager headerManager = DTDataHeaderManager.GetInstance();
            List<ColumnDefItem> fileHeaderColumnDef = new List<ColumnDefItem>();
            List<ColumnDefItem> curSampleColumnDef = new List<ColumnDefItem>();
            int index = 0;
            Package package = clientProxy.Package;
            int progress = 0;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                #region Read Stat Data
                if (isFileHeaderContentType(package.Content.Type))
                {
                    DTDataHeader header = recieveFileHeader(clientProxy.DbID, package.Content, fileHeaderColumnDef);
                    headerManager.AddDTDataHeader(header);
                }
                else if (package.Content.Type == ResponseType.RESTYPE_COLUMN_L0G_EVENT)
                {
                    curSampleColumnDef.Clear();
                    string idpairs = package.Content.GetParamString();
                    parseToCurColumnDef(idpairs, curSampleColumnDef);
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIY_LOG_EVENT)
                {
                    Event evt = Event.Create(package.Content, curSampleColumnDef);
                    DTDataHeader header = headerManager.GetHeaderByFileID(evt.FileID);
                    if (header != null)
                    {
                        addValidEvt(evt, header);
                    }
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                #endregion

                setProgressPercent(ref index, ref progress);
            }
        }

        private void addValidEvt(Event evt, DTDataHeader header)
        {
            bool isValid = isValidEvent(evt);
            if (isValid)
            {
                evt.ApplyHeader(header);
                //MainModel.DTDataManager.Add(evt);
                if (!fileEventListDic.ContainsKey(curFile))
                {
                    List<Event> events = new List<Event>();
                    events.Add(evt);
                    fileEventListDic.Add(curFile, events);
                }
                else
                {
                    fileEventListDic[curFile].Add(evt);
                }
            }
        }

        private void doStat()
        {
            try
            {
                pathInfoList = new List<PathInfo>();
                foreach (FileInfo file in fileEventListDic.Keys)
                {
                    PathInfo pathInfo = new PathInfo();
                    pathInfo.fileName = file.Name;
                    List<Event> eventList = fileEventListDic[file];
                    List<string> roads = new List<string>();
                    List<string> districts = new List<string>();
                    int nSeq = 0;
                    foreach (Event evt in eventList)
                    {
                        if (eventList.Count > 100 && ((nSeq++) % 5 != 0))
                        {
                            continue;
                        }
                        addRoads(roads, evt);

                        addDistricts(districts, evt);
                    }

                    setDistricts(pathInfo, districts);
                    setRoads(pathInfo, roads);
                    pathInfoList.Add(pathInfo);
                }
            }
            catch (Exception ex)
            {
                log.Error(ex.StackTrace);
            }
        }

        private void addRoads(List<string> roads, Event evt)
        {
            string[] roadDesc = GISManager.GetInstance().GetRoadPlaceDesc(evt.Longitude, evt.Latitude).Split(';');
            foreach (string r in roadDesc)
            {
                if (!roads.Contains(r) && r.Trim().Length > 0)
                {
                    roads.Add(r);
                }
            }
        }

        private void addDistricts(List<string> districts, Event evt)
        {
            foreach (ResvRegion region in MainModel.SearchGeometrys.SelectedResvRegions)
            {
                string name = region.RegionName;
                bool inRegion = region.GeoOp.CheckPointInRegion(evt.Longitude, evt.Latitude);
                if (name != null && name.Trim().Length > 0 && inRegion)
                {
                    if (!districts.Contains(name))
                    {
                        districts.Add(name);
                    }
                    break;
                }
            }
        }

        private void setRoads(PathInfo pathInfo, List<string> roads)
        {
            StringBuilder sb = new StringBuilder();
            foreach (string str in roads)
            {
                sb.Append(str);
                sb.Append(";");
            }
            pathInfo.roadDesc = sb.ToString();
        }

        private void setDistricts(PathInfo pathInfo, List<string> districts)
        {
            StringBuilder district = new StringBuilder(pathInfo.district);
            for (int i = 0; i < districts.Count; i++)
            {
                if (i == 0)
                {
                    district.Append(districts[i]);
                }
                else
                {
                    district.Append(";" + districts[i]);
                }
            }
            pathInfo.district = district.ToString();
        }
    }

    public class PathInfo
    {
        public string fileName { get; set; }
        public string district { get; set; }
        public string roadDesc { get; set; }
    }
}
