﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Drawing;
using System.Windows.Forms;
using MasterCom.Util;
using System.Drawing.Drawing2D;
using MasterCom.MTGis;

namespace MasterCom.RAMS.Model
{
    /// <summary>
    /// 三角形
    /// </summary>
    public class TrianglePoint
    {
        #region members
        // 三角形顶点 +　２个边对应顶点　+　圆点
        /// <summary>
        /// 顶点
        /// </summary>
        private PointF triPt { get; set; }

        //边
        private PointF p1 { get; set; }
        /// <summary>
        /// 边
        /// </summary>
        public PointF P1
        {
            get { return p1; }
        }

        //边
        private PointF p2 { get; set; }
        /// <summary>
        /// 边
        /// </summary>
        public PointF P2
        {
            get { return p2; }
        }

        /// <summary>
        /// 中心点，即外接圆心
        /// </summary>
        public PointF centerPt { get; set; }

        //中心点-跟顶点 形成 弧度
        public double radians { get; set; } = 0;

        #endregion
        public TrianglePoint(PointF p0 ,PointF p1 ,PointF p2 , PointF center)
        {
            triPt = p0;
            this.p1 = p1;
            this.p2 = p2;
            centerPt = center;

            Caculate();
        }
        /// <summary>
        /// 边缘点处理时需要增加边界交点
        /// </summary>
        /// <param name="p0">边缘点</param>
        /// <param name="center">边界交点</param>
        public TrianglePoint(PointF p0, PointF center)
        {
            triPt = p0;
            centerPt = center;
        }

        private void  Caculate()
        {
            double x = triPt.X - centerPt.X;
            double y = triPt.Y - centerPt.Y;

            radians = Math.Atan2(y, x);

        }

        /// <summary>
        /// 两个三角形是否相邻
        /// </summary>
        /// <param name="triangleOther"></param>
        /// <returns></returns>
        public bool IsBorderUpon(TrianglePoint triangleOther)
        {
            if (p1 == triangleOther.p1 || p1 == triangleOther.p2 || p2 == triangleOther.p1 || p2 == triangleOther.p2)
            {
                return true;
            }
            return false;
        }
    }

    /// <summary>
    /// 泰森多边形
    /// </summary>
    public class TrianglePointSets
    {
        #region members
        // 三角形顶点
        public PointF triPt { get; set; }

        // ２个边 +圆点
        private List<TrianglePoint> trianglePoints { get; set; } = new List<TrianglePoint>();
        public List<TrianglePoint> TrianglePoints
        {
            get { return trianglePoints; }
        }
        //边的顶点
        private Dictionary<PointF, int> edgePoints { get; set; } = new Dictionary<PointF, int>();
        public Dictionary<PointF, int> EdgePoints
        {
            get { return edgePoints; }
        }

        public int TriangleCount
        {
            get { return trianglePoints.Count; }
        }

        public int EdgeCount
        {
            get { return edgePoints.Count; }
        }

        #endregion
        public void Add(PointF p1, PointF p2 , PointF center)
        {
            TrianglePoint tmp = new TrianglePoint(triPt , p1, p2 ,center);

            int i;
            //找到已经存在的三角形的边,顺时针插入
            for ( i = 0; i < trianglePoints.Count; i++)
            {
                if (trianglePoints[i].radians < tmp.radians)
                {
                    break;
                }
            }
                
            trianglePoints.Insert(i , tmp); 
           
            if(!edgePoints.ContainsKey(p1))
            {
                edgePoints.Add(p1, 1);
            }
            else
            {
                edgePoints[p1]++;
            }
            if (!edgePoints.ContainsKey(p2))
            {
                edgePoints.Add(p2, 1);
            }
            else
            {
                edgePoints[p2]++;
            }
        }

        /// <summary>
        /// 对于未封闭的点，三角形顺时针排序，第一个，最后一个三角形为边界三角形，然后补充完整
        /// </summary>
        public void SortTrianglePoints()
        {
            if (TriangleCount > 2 && TriangleCount != EdgeCount)
            {
                while (trianglePoints[0].IsBorderUpon(trianglePoints[trianglePoints.Count - 1]))
                {
                    TrianglePoint triangleFirst = trianglePoints[0];
                    trianglePoints.RemoveAt(0);
                    trianglePoints.Add(triangleFirst);
                }
            }
        }

        public void ComplementSelf()
        {
            TrianglePoint triPFirst = TrianglePoints[0];
            PointF pointCenter;
            int dirFirst = 1;
            //根据首三角形的外边，获取延伸方向
            if (isEdgePointOfTrianglePointSets(triPFirst.P1))
            {
                pointCenter = new PointF((triPFirst.P1.X + triPt.X) / 2, (triPFirst.P1.Y + triPt.Y) / 2);
                dirFirst = getDir(triPFirst.P2, triPt, triPFirst.P1);
                if (triPFirst.centerPt == pointCenter)
                {
                    pointCenter = getCenterPointExtend(triPFirst.P1, triPt, dirFirst);
                }
            }
            else
            {
                pointCenter = new PointF((triPFirst.P2.X + triPt.X) / 2, (triPFirst.P2.Y + triPt.Y) / 2);
                dirFirst = getDir(triPFirst.P1, triPt, triPFirst.P2);
                if (triPFirst.centerPt == pointCenter)
                {
                    pointCenter = getCenterPointExtend(triPFirst.P2, triPt, dirFirst);
                }
            }
            PointF? newPoint1 = MathFuncs.GetPointOnLineFar(triPFirst.centerPt, pointCenter, dirFirst);

            int dirLast = 1;
            TrianglePoint triPLast = TrianglePoints[TriangleCount - 1];
            //根据首三角形的外边，获取延伸方向
            if (isEdgePointOfTrianglePointSets(triPLast.P2))
            {
                pointCenter = new PointF((triPLast.P2.X + triPt.X) / 2, (triPLast.P2.Y + triPt.Y) / 2);
                dirLast = getDir(triPLast.P1, triPt, triPLast.P2);
                if (triPLast.centerPt == pointCenter)
                {
                    pointCenter = getCenterPointExtend(triPLast.P2, triPt, dirLast);
                }
            }
            else
            {
                pointCenter = new PointF((triPLast.P1.X + triPt.X) / 2, (triPLast.P1.Y + triPt.Y) / 2);
                dirLast = getDir(triPLast.P2, triPt, triPLast.P1);
                if (triPLast.centerPt == pointCenter)
                {
                    pointCenter = getCenterPointExtend(triPLast.P1, triPt, dirLast);
                }
            }
            PointF? newPoint2 = MathFuncs.GetPointOnLineFar(triPLast.centerPt, pointCenter, dirLast);
            addTrianglePoints(triPFirst, newPoint1, triPLast, newPoint2);

            CheckIntersect();
        }

        private void addTrianglePoints(TrianglePoint triPFirst, PointF? newPoint1, TrianglePoint triPLast, PointF? newPoint2)
        {
            if (triPFirst.centerPt != triPLast.centerPt)
            {
                if (newPoint1 != null && newPoint2 != null)
                {
                    PointF? interPoint = MathFuncs.GetIntersectionOfTwoLineNotExtend(triPFirst.centerPt, (PointF)newPoint1, triPLast.centerPt, (PointF)newPoint2);
                    if (interPoint != null)
                    {
                        trianglePoints.Insert(0, new TrianglePoint(triPt, (PointF)interPoint));
                    }
                    else
                    {
                        trianglePoints.Insert(0, new TrianglePoint(triPt, (PointF)newPoint1));
                        trianglePoints.Add(new TrianglePoint(triPt, (PointF)newPoint2));
                    }
                }
                else if (newPoint1 != null)
                {
                    trianglePoints.Insert(0, new TrianglePoint(triPt, (PointF)newPoint1));
                }
                else if (newPoint2 != null)
                {
                    trianglePoints.Add(new TrianglePoint(triPt, (PointF)newPoint2));
                }
            }
            else
            {
                if (newPoint1 != null)
                {
                    trianglePoints.Insert(0, new TrianglePoint(triPt, (PointF)newPoint1));
                }
                if (newPoint2 != null)
                {
                    trianglePoints.Add(new TrianglePoint(triPt, (PointF)newPoint2));
                }
            }
        }

        /// <summary>
        /// 前面需要外接圆心向边的中点发射直线，如果在边上，两点重合，获取中垂线上一点与外接圆心连成直线
        /// </summary>
        /// <param name="p1"></param>
        /// <param name="p2"></param>
        /// <param name="dir">方向：1.xMin, 2.xMax, 3.yMin, 4.yMax</param>
        /// <returns></returns>
        public PointF getCenterPointExtend(PointF p1, PointF p2, int dir)
        {
            /* x1 == x2,斜率不存在，中垂线方程为y = (y1 + y2) / 2;
             * y1 == y2,中垂线斜率不存在，中垂线方程为x = (x1 + x2) / 2;
             * 斜率存在，p1p2斜率为(y2 - y1) / (x2 - x1)，中垂线斜率为(x2 - x1) / (y2 - y1)，
             * 中垂线方程为y - (y1 + y2) / 2=(x1 - x2) * [x - (x1 + x2) / 2] / (y2 - y1)
             * y = [x - (x1 + x2) / 2] * (x1 - x2) / (y2 - y1) + (y1 + y2) / 2;
             * x = [y - (y1 + y2) / 2] * (y2 - y1) / (x1 - x2) + (x1 + x2) / 2;
             */
            if (p1.X == p2.X) //不存在斜率
            {
                if (dir == 1)   //xMin
                {
                    return new PointF(p1.X - 1, (p1.Y + p2.Y) / 2);
                }
                else  //xMax
                {
                    return new PointF(p1.X + 1, (p1.Y + p2.Y) / 2);
                }
            }
            else if (p1.Y == p2.Y)  //中垂线不存在斜率
            {
                if (dir == 3)   //yMin
                {
                    return new PointF((p1.X + p2.X) / 2, p1.Y - 1);
                }
                else//yMax
                {
                    return new PointF((p1.X + p2.X) / 2, p1.Y + 1);
                }
            }
            else//存在斜率
            {
                return getExsistSlopePoint(p1, p2, dir);
            }
        }

        private static PointF getExsistSlopePoint(PointF p1, PointF p2, int dir)
        {
            if (dir == 1)   //xMin
            {
                return getXMinPoint(p1, p2);
            }
            else if (dir == 2)  //xMax
            {
                return getXMaxPoint(p1, p2);
            }
            else if (dir == 3)  //yMin
            {
                return getYMinPoint(p1, p2);
            }
            else//yMax
            {
                return getYMaxPoint(p1, p2);
            }
        }

        private static PointF getXMinPoint(PointF p1, PointF p2)
        {
            if (p1.X < p2.X)
            {
                return new PointF(p1.X, ((p1.X) - (p1.X + p2.X) / 2) * (p1.X - p2.X) / (p2.Y - p1.Y) + (p1.Y + p2.Y) / 2);
            }
            else
            {
                return new PointF(p2.X, ((p2.X) - (p1.X + p2.X) / 2) * (p1.X - p2.X) / (p2.Y - p1.Y) + (p1.Y + p2.Y) / 2);
            }
        }

        private static PointF getXMaxPoint(PointF p1, PointF p2)
        {
            if (p1.X < p2.X)
            {
                return new PointF(p2.X, ((p2.X) - (p1.X + p2.X) / 2) * (p1.X - p2.X) / (p2.Y - p1.Y) + (p1.Y + p2.Y) / 2);
            }
            else
            {
                return new PointF(p1.X, ((p1.X) - (p1.X + p2.X) / 2) * (p1.X - p2.X) / (p2.Y - p1.Y) + (p1.Y + p2.Y) / 2);
            }
        }

        private static PointF getYMinPoint(PointF p1, PointF p2)
        {
            if (p1.Y < p2.Y)
            {
                return new PointF((p1.Y - (p1.Y + p2.Y) / 2) * (p2.Y - p1.Y) / (p1.X - p2.X) + (p1.X + p2.X) / 2, p1.Y);
            }
            else
            {
                return new PointF((p2.Y - (p1.Y + p2.Y) / 2) * (p2.Y - p1.Y) / (p1.X - p2.X) + (p1.X + p2.X) / 2, p2.Y);
            }
        }

        private static PointF getYMaxPoint(PointF p1, PointF p2)
        {
            if (p1.Y < p2.Y)
            {
                return new PointF((p2.Y - (p1.Y + p2.Y) / 2) * (p2.Y - p1.Y) / (p1.X - p2.X) + (p1.X + p2.X) / 2, p2.Y);
            }
            else
            {
                return new PointF((p1.Y - (p1.Y + p2.Y) / 2) * (p2.Y - p1.Y) / (p1.X - p2.X) + (p1.X + p2.X) / 2, p1.Y);
            }
        }

        /// <summary>
        /// 根据点与另外两个点形成的边的相对方向获取延伸方向
        /// </summary>
        /// <param name="point"></param>
        /// <param name="p1"></param>
        /// <param name="p2"></param>
        /// <returns></returns>
        private int getDir(PointF point, PointF p1, PointF p2)
        {
            int dir = MathFuncs.PointToLinePosition(point.X, point.Y, p1.X, p1.Y, p2.X, p2.Y);
            if (dir == 3)   //点在线上方
            {
                return 4;
            }
            else if (dir == 4)  //点在线下方
            {
                return 3;
            }
            else if (dir == 1)  //点在线左方
            {
                return 2;
            }
            else
            {
                return 1;
            }
        }

        /// <summary>
        /// 是否不完整泰森多边形的最外三角形的外层边，即出现1次的边顶点
        /// </summary>
        /// <param name="point">边顶点</param>
        /// <returns></returns>
        private bool isEdgePointOfTrianglePointSets(PointF point)
        {
            if (edgePoints[point] == 1)
            {
                return true;
            }
            return false;
        }

        public bool IsInRegion(PointF[] borderPoints)
        {
            foreach (TrianglePoint point in trianglePoints)
            {
                if (MathFuncs.PointInPolygon(point.centerPt, borderPoints, 6) == -1)
                {
                    return false;
                }
            }
            return true;
        }

        public bool IsInRegion(MapOperation2 borderMop2)
        {
            foreach (TrianglePoint triangle in trianglePoints)
            {
                if (!borderMop2.CheckPointInRegion(triangle.centerPt.X, triangle.centerPt.Y))
                {
                    return false;
                }
            }
            return true;
        }

        /// <summary>
        /// 核查是否存在各边相交的情况，即顺序相连，围成了2个区域
        /// </summary>
        public void CheckIntersect()
        {
            if (TriangleCount <= 3)
            {
                return;
            }
            List<PointF> points = new List<PointF>();
            for (int i = TriangleCount - 1; i >= 0; i--)
            {
                TrianglePoint tp = TrianglePoints[i];
                points.Add(tp.centerPt);
            }
            PointF? pointInter = MathFuncs.GetIntersectionOfTwoLineNotExtend(points[0], points[1], points[points.Count - 2], points[points.Count - 1]);
            if (pointInter != null)
            {
                TrianglePoints.RemoveAt(0);
                TrianglePoints.RemoveAt(TriangleCount - 1);
                TrianglePoints.Add(new TrianglePoint(triPt, (PointF)pointInter));
            }
        }

        public void Daw(Graphics g, Pen pen,
            int xMin, int yMin, int xMax, int yMax)
        {
#if restrict
            if(EdgeCount != TriangleCount)
            {
                return;
            }
#endif

            Point p1 = new Point();
            Point p2 = new Point();

            for (int i = 0; i < trianglePoints.Count - 1; i++)
            {
                p1.X = (int)(trianglePoints[i].centerPt.X);
                p1.Y = (int)(trianglePoints[i].centerPt.Y);

                p2.X = (int)(trianglePoints[i + 1].centerPt.X);
                p2.Y = (int)(trianglePoints[i + 1].centerPt.Y);

                g.DrawLine(pen, p1, p2);
            }

            p1.X = (int)(trianglePoints[0].centerPt.X);
            p1.Y = (int)(trianglePoints[0].centerPt.Y);

            g.DrawLine(pen, p1, p2);
        }

        public bool ContainsPoint(double x, double y)
        {
            PointF[] cerPoints = new PointF[TriangleCount];
            for (int i = 0; i < TriangleCount; i++)
            {
                cerPoints[i] = trianglePoints[i].centerPt;
            }
            return MathFuncs.PtInPolygon(new PointF((float)x, (float)y), cerPoints) != -1;
        }
    }

    public class TriangulationMap
    {
        public Dictionary<PointF, TrianglePointSets> triPoints { get; set; } = new Dictionary<PointF, TrianglePointSets>();

        /// <summary>
        /// 整理泰森多边形结果，处理边界
        /// </summary>
        /// <param name="result"></param>
        /// <returns></returns>
        private void ConstructMap(List<PointF> result)
        {
            for (int i = 0; i < result.Count; i += 4)
            {
                AddTriangle(result[i + 0],
                            result[i + 1],
                            result[i + 2],
                            result[i + 3]);
            }
        }

        public int ConstructMap(List<PointF> result, MapWinGIS.Shape clipBorder, MapOperation2 borderMop2)
        {
            ConstructMap(result);
            checkBorderTriangulationMap(clipBorder, borderMop2);
            return 0;
        }

        private void AddTriangle(PointF p0, PointF p1, PointF p2, PointF center)
        {
            AddOnPointSet(p0, p1, p2, center);
            AddOnPointSet(p1, p0, p2, center);
            AddOnPointSet(p2, p0, p1, center);
        }

        private void AddOnPointSet(PointF value0, PointF value1, PointF value2, PointF center)
        {
            if (triPoints.ContainsKey(value0))
            {
                triPoints[value0].Add(value1, value2, center);
            }
            else
            {
                TrianglePointSets tmp = new TrianglePointSets();
                tmp.triPt = value0;
                tmp.Add(value1, value2, center);
                triPoints.Add(value0, tmp);
            }
        }

        /// <summary>
        /// 泰森多边形边界检查，检查对象：1.点的覆盖区域超出边界；2.边界点，对于边界点取两个最外三角形的最外边的中垂线上在边界外的点加入该点的泰森多边形点序列
        /// </summary>
        private void checkBorderTriangulationMap(MapWinGIS.Shape clipBorder, MapOperation2 borderMop2)
        {
            List<PointF> pointList = new List<PointF>(triPoints.Keys);
            WaitBox.Text = "正在进行边界整理...";
            WaitBox.ProgressPercent = 0;
            int loop = 0;
            foreach (PointF point in pointList)
            {
                WaitBox.ProgressPercent = ((++loop) * 100 / pointList.Count);
                TrianglePointSets pointSets = triPoints[point];

                // 对不完整的泰森多边形进行排序以及补全
                if (pointSets.TriangleCount != pointSets.EdgeCount)
                {
                    pointSets.SortTrianglePoints();
                    pointSets.ComplementSelf();
                }

                // 检查泰森多边形的顶点
                if (pointSets.IsInRegion(borderMop2))
                {
                    continue;
                }

                // 被切割的多边形
                List<PointF> centerPoints = new List<PointF>();
                for (int i = pointSets.TriangleCount - 1; i >= 0; i--)
                {
                    TrianglePoint tp = pointSets.TrianglePoints[i];
                    centerPoints.Add(tp.centerPt);
                }

                // 重新建立泰森多边形
                List<PointF[]> retList = ClipPolygon(centerPoints, clipBorder);
                if (retList.Count > 0)
                {
                    PointF[] resultPoints = retList[0];
                    pointSets.TrianglePoints.Clear();
                    foreach (PointF pointResult in resultPoints)
                    {
                        TrianglePoint newTriP = new TrianglePoint(pointSets.triPt, pointResult);
                        pointSets.TrianglePoints.Add(newTriP);
                    }
                }
            }
        }

        private MapWinGIS.Utils utils { get; set; } = new MapWinGIS.Utils();
        private List<PointF[]> ClipPolygon(List<PointF> polygon, MapWinGIS.Shape clipBorder)
        {
            List<PointF[]> retList = new List<PointF[]>();
            MapWinGIS.Shape polyShape = new MapWinGIS.Shape();
            polyShape.ShapeType = MapWinGIS.ShpfileType.SHP_POLYGON;
            MapWinGIS.Shape newShape = null;

            int pIndex = 0;
            for (int i = 0; i <= polygon.Count; ++i)
            {
                pIndex = i;
                MapWinGIS.Point p = new MapWinGIS.Point();
                p.x = polygon[i % polygon.Count].X;
                p.y = polygon[i % polygon.Count].Y;
                polyShape.InsertPoint(p, ref pIndex);
            }

            newShape = utils.ClipPolygon(MapWinGIS.PolygonOperation.DIFFERENCE_OPERATION, clipBorder, polyShape);
            if (newShape == null)
            {
                return retList;
            }

            for (int i = 0; i < newShape.NumParts; ++i)
            {
                List<DbPoint> pnts = ShapeHelper.GetPartShapePoints(newShape, i);
                if (pnts==null)
                {
                    continue;
                }
                PointF[] partPoints = new PointF[pnts.Count];
                for (int j = 0; j < partPoints.Length; ++j)
                {
                    partPoints[j] = new PointF((float)pnts[j].x, (float)pnts[j].y);
                }
                retList.Add(partPoints);
            }
            return retList;
        }
    }
}
