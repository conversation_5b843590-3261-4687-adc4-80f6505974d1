﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using System.Collections;
using System.Data;
using System.IO;
using System.Data.OleDb;
using MasterCom.NOP.DataSet;
using DevExpress.XtraGrid;

namespace MasterCom.Util
{
    #region 读取excel帮助类
    /// <summary>
    /// 读取excel帮助类    从nop框架偷来的   -小雷
    /// </summary>
    public class ExcelTools
    {
        public void GridControlOutOfExcel(GridControl gridControl)
        {
            //gridControl.ShowPrintPreview();
            SaveFileDialog openfiledialog = new SaveFileDialog();
            openfiledialog.Filter = FilterHelper.Xls;
            openfiledialog.RestoreDirectory = true;
            if (openfiledialog.ShowDialog() == DialogResult.OK)
            {
                string fileName = openfiledialog.FileName;
                if (fileName.EndsWith(".xlsx", true, null))
                {
                    gridControl.ExportToXlsx(fileName);
                }
                else
                {
                    gridControl.ExportToXls(fileName);
                }
            }
        }
        //public static void ExportExcelFromDataSet(string fileName, System.Data.DataSet ds)
        //{
        //    SpreadsheetInfo.SetLicense("EQU1-4YRI-KEYA-HERE");
        //    ExcelFile excel = new ExcelFile();
        //    foreach (DataTable dt in ds.Tables)
        //    {
        //        excel.Worksheets.Add(dt.TableName);
        //        excel.Worksheets[dt.TableName].InsertDataTable(dt, 0, 0, true);
        //    }
        //    excel.SaveXls(fileName);
        //}

        public static ArrayList GetSheetNameList(string fileName)
        {
            //ArrayList sheetNameList = new ArrayList();
            //SpreadsheetInfo.SetLicense("EQU1-4YRI-KEYA-HERE");
            //ExcelFile excel = new ExcelFile();
            //excel.LoadXlsxFromDirectory(fileName,XlsxOptions.PreserveKeepOpen);
            //foreach (ExcelWorksheet ew in excel.Worksheets)
            //{
            //    sheetNameList.Add(ew.Name);
            //}
            //return sheetNameList;

            ArrayList sheetNameList = new ArrayList();
            OleDbConnection myConn = new OleDbConnection(GetConnectionStringTemplate(fileName));
            myConn.Open();
            DataTable datatablesheet = myConn.GetOleDbSchemaTable(OleDbSchemaGuid.Tables, null);
            myConn.Close();

            for (int i = 0; i < datatablesheet.Rows.Count; i++)
            {
                if (!datatablesheet.Rows[i]["Table_Name"].ToString().Contains("$_"))
                {
                    sheetNameList.Add(datatablesheet.Rows[i]["Table_Name"].ToString().TrimEnd('$'));
                }
            }

            return sheetNameList;
        }

        public static void ResultToDataSet(ref DataTable dtInfo, ResultSet rs)
        {
            if (rs != null)
            {
                foreach (FieldInfo fi in rs.Schema.FieldInfos)
                {
                    dtInfo.Columns.Add(fi.Name);
                }
                foreach (Row rm in rs.Rows.ToArray())
                {
                    DataRow dr = dtInfo.NewRow();
                    foreach (DataColumn dt in dtInfo.Columns)
                    {
                        dr[dt.ColumnName] = rm.GetValue(dt.ColumnName);
                    }
                    dtInfo.Rows.Add(dr);
                }
            }
        }

        public static void ResultToDataSet(ref DataTable dtInfo, DataTable rs)
        {
            foreach (DataColumn fi in rs.Columns)
            {
                dtInfo.Columns.Add(fi.ColumnName);
            }
            foreach (DataRow rm in rs.Rows)
            {
                DataRow dr = dtInfo.NewRow();
                foreach (DataColumn dt in dtInfo.Columns)
                {
                    dr[dt.ColumnName] = rm[dt.ColumnName];
                }
                dtInfo.Rows.Add(dr);
            }
        }

        public static DataTable ExcelToDb(string filepath, string filename, string SheetName)
        {
            System.Data.DataTable Var10000;
            try
            {
                Var10000 = ExcelToDataTable(filepath + filename, SheetName);
            }
            catch
            {
                Var10000 = new DataTable();
            }
            return Var10000;


        }


        public static string GetConnectionStringTemplate(string filePath)
        {
            FileInfo file = new FileInfo(filePath);
            string fileExten = file.Extension;
            string connStr;
            switch (fileExten)
            {
                case ".xls":
                    connStr = "Provider=Microsoft.Jet.OLEDB.4.0;Data Source=" + filePath + ";Extended Properties='Excel 8.0;HDR=Yes;IMEX=1;'";
                    break;
                case ".xlsx":
                    connStr = "Provider=Microsoft.ACE.OLEDB.12.0;Data Source=" + filePath + ";Extended Properties='Excel 12.0;HDR=Yes;IMEX=1;'";
                    break;
                default:
                    connStr = "Provider=Microsoft.Jet.OLEDB.4.0;Data Source=" + filePath + ";Extended Properties='Excel 8.0;HDR=Yes;IMEX=1;'";
                    break;
            }
            return connStr;

        }
        /// <summary>
        /// 将Excel导入DataTable中（Excel第一行为DataTable列名）
        /// </summary>
        /// <param name="filePath"></param>
        /// <param name="sheetName"></param>
        /// <returns></returns>
        public static DataTable ExcelToDataTable(string filePath, string sheetName)
        {

            System.Data.DataSet dt = new System.Data.DataSet();
            //string connStr = "Provider=Microsoft.Jet.Oledb.4.0;Data Source=" + filePath + ";Extended Properties='Excel 8.0;HDR=YES;IMEX=1;'";//HDR=YES 有两个值:YES/NO,表示第一行是否字段名,默认是YES,第一行是字段名

            string sqlStr = "select * from [" + sheetName + "$]";
            OleDbConnection myConn = new OleDbConnection(GetConnectionStringTemplate(filePath));
            myConn.Open();
            OleDbCommand myCmd = new OleDbCommand(sqlStr, myConn);
            OleDbDataAdapter myda = new OleDbDataAdapter(myCmd);
            myda.Fill(dt, "table");
            myCmd.Dispose();
            myConn.Close();
            return dt.Tables["table"];
        }

        public static Hashtable ExcelToDataTable(string filePath, ArrayList sheetNameList)
        {
            Hashtable tableList = new Hashtable();
            foreach (string name in sheetNameList)
            {
                System.Data.DataSet dt = new System.Data.DataSet();
                //string connStr = "Provider=Microsoft.Jet.Oledb.4.0;Data Source=" + filePath + ";Extended Properties='Excel 8.0;HDR=YES;IMEX=1;'";//HDR=YES 有两个值:YES/NO,表示第一行是否字段名,默认是YES,第一行是字段名
                string sqlStr = "select * from [" + name + "$]";
                OleDbConnection myConn = new OleDbConnection(GetConnectionStringTemplate(filePath));
                myConn.Open();
                OleDbCommand myCmd = new OleDbCommand(sqlStr, myConn);
                OleDbDataAdapter myda = new OleDbDataAdapter(myCmd);
                myda.Fill(dt, name);
                myCmd.Dispose();
                myConn.Close();
                tableList.Add(name, dt.Tables[name]);
            }
            return tableList;
        }
    }
    #endregion
}
