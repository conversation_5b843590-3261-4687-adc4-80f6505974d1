﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc.ZTLteDownloadIpGroup
{
    public class DownloadInfo
    {
        public DownloadInfo(string fileName, string ip)
        {
            this.FileName = fileName;
            this.IP = ip;
        }
        public string FileName
        {
            get;
            private set;
        }
        public string IP
        {
            get;
            private set;
        }
        public DateTime BeginTime
        {
            get;
            set;
        }
        public DateTime EndTime
        {
            get
            {
                DateTime t = DateTime.MinValue;
                if (DownloadResultEvents.Count > 0)
                {
                    t = DownloadResultEvents[DownloadResultEvents.Count - 1].DateTime;
                }
                return t;
            }
        }
        public int DlDuration
        {
            get;
            set;
        }
        public List<TestPoint> TestPoints { get; set; } = new List<TestPoint>();
        public List<Event> DownloadResultEvents { get; set; } = new List<Event>();

        public double AvgRsrp
        {
            get;
            private set;
        }

        public double AvgSinr
        {
            get;
            private set;
        }

        public double AvgSpeedM
        {
            get;
            private set;
        }

        public double FailRate
        {
            get;
            private set;
        }

        public double DoubleRankTimeRate
        {
            get;
            private set;
        }

        public double AvgGrantCount
        {
            get;
            private set;
        }

        public double AvgPrbPerS
        {
            get;
            private set;
        }

        public double DlCode0Qam64Rate
        {
            get;
            private set;
        }

        public double DlCode1Qam64Rate
        {
            get;
            private set;
        }

        public double Cqi0 { get; set; }

        public double Cqi1 { get; set; }

        public double? Bler0 { get; set; }

        public double? Bler1 { get; set; }

        public double AckRate0 { get; set; }

        public double NackRate0 { get; set; }

        public double AckRate1 { get; set; }

        public double NackRate1 { get; set; }

        public double Msc0Avg { get; set; }

        public double Msc1Avg { get; set; }

        class DataInfo
        {
            public double msc0Sum = 0;
            public int msc0Num = 0;
            public double msc1Sum = 0;
            public int msc1Num = 0;
            public int time1Sum = 0;
            public int time2Sum = 0;
            public int lastRank = 0;
            public int lastIdx = -1;
        }

        public void MakeSummary()
        {
            AvgRsrp = this["RSRP", SummaryType.Avg];
            AvgSinr = this["SINR", SummaryType.Avg];
            AvgSpeedM = this["APP_Speed_Mb", SummaryType.Avg];
            int failNum = 0;
            foreach (Event evt in DownloadResultEvents)
            {
                if (evt.ID == 59)
                {
                    failNum++;
                }
            }
            FailRate = Math.Round(100.0 * failNum / DownloadResultEvents.Count, 2);

            AvgGrantCount = this["PDCCH_DL_Grant_Count", SummaryType.Avg];

            AvgPrbPerS = this["PDSCH_PRb_Num_s", SummaryType.Avg];

            double c0Qam64Sum = this["Times_QAM64_DLCode0", SummaryType.Sum];
            double c0QpskSum = this["Times_QPSK_DLCode0", SummaryType.Sum];
            double c0Qam16Sum = this["Times_QAM16_DLCode0", SummaryType.Sum];

            DlCode0Qam64Rate = Math.Round(100.0 * c0Qam64Sum / (c0Qam64Sum + c0QpskSum + c0Qam16Sum), 2);

            double c1Qam64Sum = this["Times_QAM64_DLCode1", SummaryType.Sum];
            double c1QpskSum = this["Times_QPSK_DLCode1", SummaryType.Sum];
            double c1Qam16Sum = this["Times_QAM16_DLCode1", SummaryType.Sum];
            DlCode1Qam64Rate = Math.Round(100.0 * c1Qam64Sum / (c1Qam64Sum + c1QpskSum + c1Qam16Sum), 2);

            Cqi0 = this["Wideband_CQI_for_CW0", SummaryType.Avg];
            Cqi1 = this["Wideband_CQI_for_CW1", SummaryType.Avg];
            Bler = this["PDSCH_BLER", SummaryType.Avg];
            Bler0 = this["PDSCH_Code0_BLER", SummaryType.Avg];
            Bler0 = double.IsNaN((double)Bler0) ? null : Bler0;
            Bler1 = this["PDSCH_Code1_BLER", SummaryType.Avg];
            Bler1 = double.IsNaN((double)Bler1) ? null : Bler1;

            Ack0Sum = this["Count_DL_Code0_HARQ_ACK", SummaryType.Sum];
            Nack0Sum = this["Count_DL_Code0_HARQ_NACK", SummaryType.Sum];
            AckRate0 = Math.Round(100.0 * Ack0Sum / (Ack0Sum + Nack0Sum), 2);
            NackRate0 = Math.Round(100 - AckRate0, 2);

            Ack1Sum = this["Count_DL_Code1_HARQ_ACK", SummaryType.Sum];
            Nack1Sum = this["Count_DL_Code1_HARQ_NACK", SummaryType.Sum];
            AckRate1 = Math.Round(100.0 * Ack1Sum / (Ack1Sum + Nack1Sum), 2);
            NackRate1 = Math.Round(100 - AckRate1, 2);

            DataInfo info = new DataInfo();
            for (int i = 0; i < TestPoints.Count; i++)
            {
                TestPoint tp = TestPoints[i];
                double val = GetLteMCSCode_DL_Avg(tp, "MCSCode0_DL");
                if (!double.IsNaN(val))
                {
                    info.msc0Sum += val;
                    info.msc0Num++;
                }
                val = GetLteMCSCode_DL_Avg(tp, "MCSCode1_DL");
                if (!double.IsNaN(val))
                {
                    info.msc1Sum += val;
                    info.msc1Num++;
                }

                dealTPTime(info, i, tp);

            }
            Msc0Avg = Math.Round(info.msc0Sum / info.msc0Num, 2);
            Msc1Avg = Math.Round(info.msc1Sum / info.msc1Num, 2);
            DoubleRankTimeRate = Math.Round(info.time2Sum * 100.0 / (info.time1Sum + info.time2Sum), 2);
        }

        private void dealTPTime(DataInfo info, int i, TestPoint tp)
        {
            bool added = false;
            int rank = getRank(tp);

            if (info.lastRank == 0 && (rank == 1 || rank == 2))
            {
                info.lastIdx = i;
                info.lastRank = rank;
            }

            if (testpointBandIdx.Contains(i))
            {
                added = addTime(info, TestPoints[i - 1], added);
                info.lastIdx = i;
                info.lastRank = rank;
            }

            if (rank != info.lastRank)
            {
                added = addTime(info, tp, added);
                info.lastIdx = i;
            }
            info.lastRank = rank;

            if (i == TestPoints.Count - 1 && !added)
            {//避免漏掉最后一段
                if (info.lastRank == 1)
                {
                    info.time1Sum += tp.Time - TestPoints[info.lastIdx].Time;
                }
                else if (info.lastRank == 2)
                {
                    info.time2Sum += tp.Time - TestPoints[info.lastIdx].Time;
                }
            }
        }

        private bool addTime(DataInfo info, TestPoint tp, bool added)
        {
            if (info.lastRank == 1)
            {
                info.time1Sum += tp.Time - TestPoints[info.lastIdx].Time;
                added = true;
            }
            else if (info.lastRank == 2)
            {
                info.time2Sum += tp.Time - TestPoints[info.lastIdx].Time;
                added = true;
            }

            return added;
        }

        private static int getRank(TestPoint tp)
        {
            int rank = 0;
            object obj = null;
            if (tp is LTEFddTestPoint)
            {
                obj = tp["lte_fdd_Rank_Indicator"];
            }
            else
            {
                obj = tp["lte_Rank_Indicator"];
            }
            if (obj != null)
            {
                int.TryParse(obj.ToString(), out rank);
            }

            return rank;
        }

        public double GetLteMCSCode_DL_Avg(TestPoint tp, string key)
        {
            if (tp is LTEFddTestPoint)
            {
                key = "lte_fdd_" + key;
            }
            else
            {
                key = "lte_" + key;
            }
            double d = 0;
            double p = 0;
            for (int i = 0; i < 32; i++)
            {
                object x = tp[key, i];
                if (x != null)
                {
                    double val;
                    if (double.TryParse(x.ToString(), out val))
                    {
                        p += val * i;
                        d += val;
                    }
                }
            }
            return Math.Round(p / d, 2);
        }

        public enum SummaryType
        {
            Num,
            Sum,
            Avg
        }

        public double this[string keyType, SummaryType type]
        {
            get
            {
                double sum = 0;
                int num = 0;
                string key = "";
                foreach (TestPoint tp in TestPoints)
                {
                    if (tp is LTEFddTestPoint)
                    {
                        key = "lte_fdd_" + keyType;
                    }
                    else
                    {
                        key = "lte_" + keyType;
                    }
                    object objV = tp[key];
                    if (objV == null)
                    {
                        continue;
                    }
                    double val;
                    if (double.TryParse(objV.ToString(), out val))
                    {
                        num++;
                        sum += val;
                    }
                }

                switch (type)
                {
                    case SummaryType.Num:
                        return num;
                    case SummaryType.Sum:
                        return Math.Round(sum, 2);
                    case SummaryType.Avg:
                        return Math.Round(sum / num, 2);
                    default:
                        break;
                }
                return double.NaN;
            }
        }

        private readonly List<int> testpointBandIdx = new List<int>();
        internal void AddTestPoints(List<TestPoint> dlingTpSet)
        {
            if (TestPoints.Count > 0 && dlingTpSet.Count > 0)
            {
                testpointBandIdx.Add(TestPoints.Count);
            }
            if (dlingTpSet.Count > 0)
            {
                DlDuration += dlingTpSet[dlingTpSet.Count - 1].Time - dlingTpSet[0].Time;
            }
            TestPoints.AddRange(dlingTpSet);
        }

        public double Ack0Sum { get; set; }

        public double Nack0Sum { get; set; }

        public double Ack1Sum { get; set; }

        public double Nack1Sum { get; set; }

        public double Bler { get; set; }
    }
}
