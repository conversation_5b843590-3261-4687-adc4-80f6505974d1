﻿using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTHighReverseFlowCoverage._45GHighReverseFlowCoverage
{
    public class SerialNumber
    {
        public int ID { get; set; }
        public string City { get; protected set; }
        public int CityID { get; protected set; }
        public int Enodebid { get; protected set; }
        public string Number { get; protected set; }
        public LongitudeLatitude Bts { get; set; } = new LongitudeLatitude();

        public string Key { get; private set; }
        /// <summary>
        /// 根据最新的工参和编号表进行对比,如果工参中存在才有效
        /// 不存在则认为是编号表中对应的站点拆站或者参数变化弃用了
        /// </summary>
        //public bool IsEnable { get; set; } = false;

        public void FillDataBySQL(Package package)
        {
            ID = package.Content.GetParamInt();
            City = package.Content.GetParamString();
            CityID = package.Content.GetParamInt();
            Enodebid = package.Content.GetParamInt();
            Number = package.Content.GetParamString();

            Bts.FillIntData(package);

            Key = SerialNumberHelper.GetKey(this);
        }

        public void FillData(int id, CellParamInfo info)
        {
            ID = id;
            City = info.City;
            CityID = info.CityID;
            Enodebid = info.Enodebid;
            Number = info.Number;
            Bts.ILongitude = info.Bts.ILongitude;
            Bts.ILatitude = info.Bts.ILongitude;

            Key = SerialNumberHelper.GetKey(this);
            //IsEnable = true;
        }
    }
}
