﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Runtime.Serialization;
using System.Text;
using MasterCom.MTGis;
using MasterCom.RAMS.Grid;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc.ZTTestPointOverMuchGrid
{
    public class TestPointOverMuchGridLayer: LayerBase
    {
        //需要绘制的图元链表
        public List<TestPointOverMuchGridInfo> Grids { get; set; }
        public TestPointOverMuchGridLayer()
            : base("停车测试栅格图层")
        {
        }

        readonly Font StringFont = new Font("宋体", 9, FontStyle.Bold);
        readonly SolidBrush stringBrush = new SolidBrush(Color.Red);
        readonly Pen pen = new Pen(new SolidBrush(Color.Red), 1);
        readonly Pen penSel = new Pen(new SolidBrush(Color.Red), 3);
        public override void Draw(System.Drawing.Rectangle clientRect, System.Drawing.Rectangle updateRect, System.Drawing.Graphics graphics)
        {
            if (this.Grids == null)
            {
                return;
            }
            graphics.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.AntiAlias;
            foreach (TestPointOverMuchGridInfo shape in this.Grids)
            {
                RectangleF rect;
                this.gisAdapter.ToDisplay(shape.Bounds, out rect);
                graphics.DrawRectangle(shape == SelGrid ? penSel : pen, rect.X, rect.Y, rect.Width, rect.Height);
                graphics.DrawString(shape.SerialNm.ToString(), StringFont
                    , stringBrush, rect.X + rect.Width / 2, rect.Y + rect.Height / 2);
            }
        }
        
        public TestPointOverMuchGridInfo SelGrid { get; set; }
    }
}
