﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Text;
using System.Windows.Forms;
using System.Xml;
using MasterCom.Util;

namespace MasterCom.RAMS.Model
{
    public class CellParamCfgManager
    {
        private static CellParamCfgManager instance = null;
        private CellParamCfgManager() {
            Load();
        }
        public static CellParamCfgManager GetInstance()
        {
            if (instance == null)
            {
                instance = new CellParamCfgManager();
            }
            return instance;
        }

        public void AddTable(CellParamTable tb)
        {
            bool added = false;
            for (int i = 0; i < dataBaseList.Count; i++)
            {
                CellParamDataBase db = dataBaseList[i];
                if (db.Name.Equals(tb.DataBase.Name))
                {
                    db.RemoveTable(tb.Name);
                    tb.DataBase = db;
                    db.Tables.Add(tb);
                    added = true;
                }
            }
            if (!added)
            {
                CellParamDataBase db = new CellParamDataBase(tb.DataBase.Name);
                tb.DataBase = db;
                db.Tables.Add(tb);
                dataBaseList.Add(db);
            }
        }

        /// <summary>
        /// 数据库，表，字段信息，供配置新参数用。(需要新增配置时再初始化)
        /// </summary>
        public List<CellParamDataBase> DataBaseInfoForAddParam
        {
            get;
            set;
        }

        public bool ContainsTable(string name)
        {
            foreach (CellParamDataBase db in dataBaseList)
            {
                if (db.ContainsTable(name))
                {
                    return true;
                }
            }
            return false;
        }

        public void RemoveTable(string name)
        {
            foreach (CellParamDataBase db in dataBaseList)
            {
                if (db.RemoveTable(name))
                {
                    break;
                }
            }
        }

        private readonly List<CellParamDataBase> dataBaseList = new List<CellParamDataBase>();
        /// <summary>
        /// 参数配置数据
        /// </summary>
        public List<CellParamDataBase> ParamDataBaseList
        {
            get { return dataBaseList; }
        }

        private SqlConnectionStringBuilder connSB = null;
        public string DBConnectionStr
        {
            get
            {
                if (connSB != null)
                {
                    return connSB.ConnectionString;
                }
                return string.Empty;
            }
        }

        #region congfig
        readonly string configFileName = Application.StartupPath + @"\config\CellParamSetting.xml";
        public void Save()
        {
            MasterCom.Util.XmlConfigFile xmlFile = new MasterCom.Util.XmlConfigFile();
            saveDBConnetion(xmlFile);
            saveCellParams(xmlFile);
            saveCustomGrp(xmlFile);
            xmlFile.Save(configFileName);
        }

        private void saveDBConnetion(XmlConfigFile xmlFile)
        {
            XmlElement cfgE = xmlFile.AddConfig("DBConnection");
            xmlFile.AddItem(cfgE, "DataSource", connSB.DataSource);
            xmlFile.AddItem(cfgE, "InitialCatalog", connSB.InitialCatalog);
            xmlFile.AddItem(cfgE, "UserID", connSB.UserID);
            xmlFile.AddItem(cfgE, "Password", DES.Encode(connSB.Password));
        }

        private void saveCellParams(XmlConfigFile xmlFile)
        {
            XmlElement cfgE = xmlFile.AddConfig("OptionalParams");
            xmlFile.AddItem(cfgE, "CellParams", this.cellParamCfg);
        }

        private void saveCustomGrp(XmlConfigFile xmlFile)
        {
            XmlElement cfgE = xmlFile.AddConfig("CustomParams");
            xmlFile.AddItem(cfgE, "CustomGroupCols", this.customGrpCfg);
        }

        public void Load()
        {
            if (File.Exists(configFileName))
            {
                XmlConfigFile configFile = new XmlConfigFile(configFileName);
                loadDBConnetion(configFile);
                loadParamSetting(configFile);
                loadCustomGrpCfg(configFile);
            }
            else
            {
                CellParamDBSettingDlg dlg = new CellParamDBSettingDlg(null);
                if (dlg.ShowDialog()==DialogResult.OK)
                {
                    this.connSB = dlg.ConnSB;
                }
            }
        }

        private void loadDBConnetion(XmlConfigFile configFile)
        {
            connSB = new SqlConnectionStringBuilder();
            connSB.DataSource = configFile.GetItemValue("DBConnection", "DataSource") as string;
            object value=configFile.GetItemValue("DBConnection", "InitialCatalog");
            if (value != null)
            {
                connSB.InitialCatalog = value as string;
            }
            connSB.UserID = configFile.GetItemValue("DBConnection", "UserID") as string;
            connSB.Password = DES.Decode(configFile.GetItemValue("DBConnection", "Password") as string);
        }

        private void loadParamSetting(XmlConfigFile configFile)
        {
            cellParamCfg = configFile.GetItemValue("OptionalParams", "CellParams") as Dictionary<string, object>;
        }
        private void loadCustomGrpCfg(XmlConfigFile configFile)
        {
            customGrpCfg = configFile.GetItemValue("CustomParams", "CustomGroupCols") as Dictionary<string, object>;
        }

        private Dictionary<string, object> cellParamCfg
        {
            get
            {
                Dictionary<string, object> cfgDic = new Dictionary<string, object>();
                List<object> dbCfg = new List<object>();
                foreach (CellParamDataBase db in dataBaseList)
                {
                    dbCfg.Add(db.CfgParam);
                }
                cfgDic["DataBases"] = dbCfg;
                return cfgDic;
            }
            set
            {
                if (value == null)
                {
                    return;
                }
                dataBaseList.Clear();
                List<object> dbCfg = value["DataBases"] as List<object>;
                foreach (object cfg in dbCfg)
                {
                    CellParamDataBase db = new CellParamDataBase();
                    db.CfgParam = cfg as Dictionary<string, object>;
                    dataBaseList.Add(db);
                }
            }
        }

        private Dictionary<string, object> customGrpCfg
        {
            get
            {
                Dictionary<string, object> cfgDic = new Dictionary<string, object>();
                foreach (KeyValuePair<string, List<CellParamColumn>> kvp in CustomGrpColDic)
                {
                    List<string> colNames = new List<string>();
                    foreach (CellParamColumn col in kvp.Value)
                    {
                        colNames.Add(col.FullName);
                    }
                    cfgDic.Add(kvp.Key, colNames);
                }
                return cfgDic;
            }
            set
            {
                if (value == null)
                {
                    return;
                }
                CustomGrpColDic = new Dictionary<string, List<CellParamColumn>>();
                foreach (KeyValuePair<string, object> kvp in value)
                {
                    List<CellParamColumn> paramCols = new List<CellParamColumn>();
                    List<object> colNames = kvp.Value as List<object>;
                    foreach (object fullName in colNames)
                    {
                        CellParamColumn col = this.GetParamColByFullName(fullName.ToString());
                        if (col != null && !paramCols.Contains(col))
                        {
                            paramCols.Add(col);
                        }
                    }
                    if (paramCols.Count > 0)
                    {
                        CustomGrpColDic.Add(kvp.Key, paramCols);
                    }
                }
            }
        }
        #endregion

        internal CellParamColumn GetParamColByFullName(string fullColName)
        {
            foreach (CellParamDataBase db in dataBaseList)
            {
                CellParamColumn col = null;
                if (db.TryGetColunmByFullName(fullColName, out col))
                {
                    return col;
                }
            }
            return null;
        }

        public void AddCustomGrp(string grpName,List<CellParamColumn> cols)
        {
            if (!CustomGrpColDic.ContainsKey(grpName))
            {
                CustomGrpColDic.Add(grpName,cols);
            }
            else
            {
                CustomGrpColDic[grpName] = cols;
            }
        }
        
        /// <summary>
        /// 自定义分组指标字典，key:分组名称,value:指标集
        /// </summary>
        public Dictionary<string, List<CellParamColumn>> CustomGrpColDic { get; set; } = new Dictionary<string, List<CellParamColumn>>();
    }
}
