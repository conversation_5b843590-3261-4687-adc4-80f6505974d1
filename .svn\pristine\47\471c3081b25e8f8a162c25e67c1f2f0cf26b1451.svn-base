﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using MasterCom.Util.UiEx;
using DevExpress.XtraEditors;

namespace MasterCom.RAMS.ZTFunc
{
    public abstract class VoNRMOSQueryAnaBase : QueryBase
    {
        public VoNRMOSQueryAnaBase(MainModel mm) : base(mm)
        {
            MosParamLst = new List<VoNRMosParam>();
        }

        public List<VoNRMosParam> MosParamLst;
        protected List<PESQInfo> pesqLst;      //记录mos相关信息
        protected List<TimePeriod> prds;       //记录mos分析时间段
        protected List<double> longitudeLst;   //mos值对应的经度集合
        protected List<double> latitudeLst;    //mos值对应的纬度集合

        /// <summary>
        /// 查询或回放MOS点及其所在文件信息，结果应保存在MainModel.DTDataManager里
        /// </summary>
        protected abstract void queryMOSFiles();
        //MOS门限
        protected float mosGate = 5;
        protected DTDataManager DTDataManager;
        protected List<int> fileStated = new List<int>();
        protected List<FileInfo> file2Stat = null;
        protected List<string> fileRepSampleColNames = null;
        StringBuilder strbErr = null;


        public override string Name
        {
            get { return "VONR-MOS分析"; }
        }
        protected override bool isValidCondition()
        {
            return true;
        }
        protected virtual void prepareSampleQryColumnNames(out List<string> colNames)
        {
            colNames = new List<string>();
            colNames.Add("NR_PESQMos");  // lte_PESQMos
            colNames.Add("NR_POLQA_Score_SWB");  // lte_POLQA_Score_SWB
            colNames.Add("NR_PESQLQ");  // lte_PESQLQ
            colNames.Add("itime");
            colNames.Add("ilongitude");
            colNames.Add("ilatitude");
            colNames.Add("ifileid");
            colNames.Add("isampleid");
        }

        protected virtual List<string> getFileReplaySampleColNames()
        {
            if (fileRepSampleColNames == null)
            {
                fileRepSampleColNames = new List<string>();
                fileRepSampleColNames.Add("isampleid");
                fileRepSampleColNames.Add("itime");
                fileRepSampleColNames.Add("ilongitude");
                fileRepSampleColNames.Add("ilatitude");
                fileRepSampleColNames.Add("NR_TAC");
                fileRepSampleColNames.Add("NR_NCI");
                fileRepSampleColNames.Add("NR_SS_RSRP");
                fileRepSampleColNames.Add("NR_SS_SINR");
                fileRepSampleColNames.Add("NR_PCI");
                fileRepSampleColNames.Add("NR_SS_RSRQ");
                fileRepSampleColNames.Add("NR_SSB_ARFCN");  // lte_EARFCN
                fileRepSampleColNames.Add("NR_PESQMos");  // lte_PESQMos
                fileRepSampleColNames.Add("NR_PESQLQ");  // lte_PESQLQ
                fileRepSampleColNames.Add("NR_POLQA_Score_SWB");  // lte_POLQA_Score_SWB
                fileRepSampleColNames.Add("NR_NCell_RSRP");  // lte_NCell_RSRP
                fileRepSampleColNames.Add("NR_PUSCH_TxPower");  // lte_PUSCH_Power
                fileRepSampleColNames.Add("NR_16QAM_Count_UL");  // lte_Times_QAM16_UL
                fileRepSampleColNames.Add("NR_64QAM_Count_UL");  // lte_Times_QAM64_UL
                fileRepSampleColNames.Add("NR_QPSK_Count_UL");  // lte_Times_QPSK_UL
                fileRepSampleColNames.Add("NR_16QAM_Count_DL_TB0");  // lte_Times_QAM16_DLCode0
                fileRepSampleColNames.Add("NR_16QAM_Count_DL_TB1");  // lte_Times_QAM16_DLCode1
                fileRepSampleColNames.Add("NR_64QAM_Count_DL_TB0");  // lte_Times_QAM64_DLCode0
                fileRepSampleColNames.Add("NR_64QAM_Count_DL_TB1");  // lte_Times_QAM64_DLCode1
                fileRepSampleColNames.Add("NR_QPSK_Count_DL_TB0");  // lte_Times_QPSK_DLCode0
                fileRepSampleColNames.Add("NR_QPSK_Count_DL_TB1");  // lte_Times_QPSK_DLCode1

                fileRepSampleColNames.Add("NR_lte_EARFCN");  // lte_gsm_SC_BCCH
                fileRepSampleColNames.Add("NR_lte_RSRP");  // lte_gsm_DM_RxLevSub
                fileRepSampleColNames.Add("NR_lte_SINR");  // lte_gsm_DM_RxQualSub
                fileRepSampleColNames.Add("mode");
                fileRepSampleColNames.Add("NR_lte_TAC");  // lte_gsm_SC_LAC
                fileRepSampleColNames.Add("NR_lte_ECI");  // lte_gsm_SC_CI
                fileRepSampleColNames.Add("NR_lte_EARFCN");  // lte_gsm_SC_BCCH
                fileRepSampleColNames.Add("NR_gsm_SC_BSIC");  // lte_gsm_SC_BSIC
                fileRepSampleColNames.Add("NR_lte_NCell_RSRP");  // lte_gsm_NC_RxLev

                fileRepSampleColNames.Add("NR_MCS_DL_Code0");  // lte_MCSCode0_DL
                fileRepSampleColNames.Add("NR_MCS_DL_Code1");  // lte_MCSCode1_DL
                fileRepSampleColNames.Add("NR_PDSCH_Retransmission_BLER");  // lte_PDSCH_RB_Number
                fileRepSampleColNames.Add("NR_PUSCH_PathLoss");  // lte_PUSCH_Pathloss
                fileRepSampleColNames.Add("NR_MCS_UL_Info");  // lte_MCS_UL
                fileRepSampleColNames.Add("NR_PUSCH_Initial_BLER");  // lte_PUSCH_Initial_BLER
                fileRepSampleColNames.Add("NR_PDSCH_Initial_BLER");  // lte_PDSCH_Init_BLER
                fileRepSampleColNames.Add("lte_PDSCH_Init_BLER_Code1");
                fileRepSampleColNames.Add("lte_PDSCH_Init_BLER_Code0");
                fileRepSampleColNames.Add("lte_Transmission_Mode");
                fileRepSampleColNames.Add("lte_Rank_Indicator");
                fileRepSampleColNames.Add("NR_lte_DL_Frequency");  // lte_DL_Frequency
                fileRepSampleColNames.Add("NR_PDSCH_Residual_BLER");  // lte_PDSCH_PRb_Num_s
                fileRepSampleColNames.Add("NR_PUSCH_Residual_BLER");  // lte_PUSCH_PRb_Num_s
                fileRepSampleColNames.Add("NR_VONR_RTP_Packets_Lost_Num");  // lte_volte_RTP_Packets_Lost_Num
                fileRepSampleColNames.Add("NR_VONR_RTP_Packets_Num");  // lte_volte_RTP_Packets_Num
                fileRepSampleColNames.Add("NR_VONR_RTP_Sequence_Number");  // lte_volte_RTP_Sequence_Number
                fileRepSampleColNames.Add("NR_VONR_RTP_Source_SSRC");  // lte_volte_Source_SSRC
                fileRepSampleColNames.Add("NR_VONR_RTP_Jitter");  // lte_volte_Jitter
            }
            return fileRepSampleColNames;
        }

        protected virtual bool getCondition()
        {
            return true;
        }

        protected override void query()
        {
            if (!getCondition())
            {
                return;
            }

            List<ServiceType> serviceList = new List<ServiceType>();
            serviceList.Add(ServiceType.NR_NSA_TDD_VOLTE);
            serviceList.Add(ServiceType.NR_SA_TDD_VOLTE);
            serviceList.Add(ServiceType.SER_NR_SA_TDD_EPSFB);
            serviceList.Add(ServiceType.NR_SA_TDD_VONR);

            fileStated = new List<int>();
            strbErr = new StringBuilder();
            DTDataManager = new DTDataManager(MainModel);
            fileRepSampleColNames = null;
            MosParamLst.Clear();
            file2Stat = new List<FileInfo>(condition.FileInfos);
            foreach (FileInfo fi in file2Stat)
            {
                if (!serviceList.Contains((ServiceType)fi.ServiceType))
                {
                    continue;
                }
                if (fileStated.Contains(fi.ID))
                {
                    continue;
                }

                try
                {
                    condition.FileInfos.Clear();
                    condition.FileInfos.Add(fi);
                    MainModel.DTDataManager.Clear();
                    MainModel.DTDataManager.HistoryData.Clear();
                    queryMOSFiles();
                    MainModel.DTDataManager.Sort();
                    mosQryAnaByPeerFiles(MainModel.DTDataManager.FileDataManagers);
                }
                catch (System.Exception ex)
                {
                    XtraMessageBox.Show(ex.Source);
                }
            }

            if (strbErr.Length > 0)
            {
                XtraMessageBox.Show(strbErr.ToString());
            }
            FireShowForm(MosParamLst);
            MainModel.DTDataManager.Clear();
            MainModel.DTDataManager.HistoryData.Clear();
            fileRepSampleColNames = null;
            GC.Collect();
        }

        /// <summary>
        /// 按文件回放分析MOS
        /// </summary>
        /// <param name="fileMngrs">DTFileDataManager集合，应由queryMOSSampleAndFiles方法提供</param>
        protected virtual void mosQryAnaByPeerFiles(List<DTFileDataManager> fileMngrs)
        {
            foreach (DTFileDataManager fileMngr in fileMngrs)  //遍历采样点所在的文件
            {
                //关联主被叫文件对
                DIYQueryPeerFileInfo qryFileInfo = new DIYQueryPeerFileInfo(MainModel, fileMngr.LogTable, fileMngr.FileID, fileMngr.FileName, false);
                qryFileInfo.Query();
                Dictionary<FileInfo, FileInfo> peerFileInfoDic = qryFileInfo.PeerFileInfoDic;
                if (peerFileInfoDic == null)
                {
                    if (strbErr.Length == 0)
                    {
                        strbErr.AppendLine("以下文件未能找到对应的主被叫文件：");
                    }
                    strbErr.AppendLine(fileMngr.FileName);
                    fileMngr.DTDatas.Clear();
                    continue;
                }

                QueryCondition cond = new QueryCondition();
                prds = new List<TimePeriod>();
                pesqLst = new List<PESQInfo>();
                longitudeLst = new List<double>();
                latitudeLst = new List<double>();
                statOneFile(fileMngr, out prds, out pesqLst, out longitudeLst, out latitudeLst);

                fileMngr.DTDatas.Clear();
                if (prds.Count == 0)
                {
                    continue;
                }

                foreach (var kvp in peerFileInfoDic)
                {
                    //多时间段回放主被叫文件
                    cond.FileInfos.Add(kvp.Key);
                    cond.FileInfos.Add(kvp.Value);
                    cond.Periods.Add(new TimePeriod(DateTime.Parse(kvp.Key.BeginTimeString), DateTime.Parse(kvp.Key.EndTimeString)));
                    List<string> colNames = getFileReplaySampleColNames();
                    MainModel.DTDataManager.Clear();
                    MainModel.DTDataManager.HistoryData.Clear();
                    GC.Collect();
                    DIYReplayFileQueryByCustom qry = new DIYReplayFileQueryByCustom(MainModel);
                    qry.SetQueryCondition(cond);
                    qry.SetReplayContent(colNames, true, true, true);
                    qry.Query();

                    //mos分析
                    List<DTFileDataManager> files = new List<DTFileDataManager>();
                    MainModel.DTDataManager.Sort();
                    if (MainModel.DTDataManager.FileDataManagers.Count != 2)
                    {
                        continue;
                    }

                    if (MainModel.DTDataManager.FileDataManagers[0].FileID == fileMngr.FileID)
                    {
                        files.Add(MainModel.DTDataManager.FileDataManagers[0]);
                        files.Add(MainModel.DTDataManager.FileDataManagers[1]);
                    }
                    else
                    {
                        files.Add(MainModel.DTDataManager.FileDataManagers[1]);
                        files.Add(MainModel.DTDataManager.FileDataManagers[0]);
                    }
                    WaitTextBox.Show("正在统计主端文件：" + fileMngr.FileName, statInThread, files);

                    foreach (FileInfo fi in file2Stat)
                    {
                        if (fi.ID == files[1].FileID && !fileStated.Contains(files[1].FileID))
                        {
                            statOneFile(files[1], out prds, out pesqLst, out longitudeLst, out latitudeLst);
                            List<DTFileDataManager> tempMngList = new List<DTFileDataManager>();
                            tempMngList.Add(files[1]);
                            tempMngList.Add(files[0]);
                            WaitTextBox.Show("正在统计主端文件：" + fileMngr.FileName, statInThread, tempMngList);
                            break;
                        }
                    }
                    MainModel.DTDataManager.Clear();
                    MainModel.DTDataManager.HistoryData.Clear();
                    GC.Collect();
                }
            }
        }

        protected virtual void statOneFile(DTFileDataManager fileMng, out List<TimePeriod> periods, out List<PESQInfo> pesqLst, out List<double> longitudeLst, out List<double> latitudeLst)
        {
            periods = new List<TimePeriod>();
            pesqLst = new List<PESQInfo>();
            longitudeLst = new List<double>();
            latitudeLst = new List<double>();

            string MOSParamName = "";
            string PESQMOSParamName = "";
            string PESQLQParamName = "";
            foreach (TestPoint tp in fileMng.TestPoints)//通过采样点mos值，确定回放的时间段
            {
                /*
                根据测试文件里不同的mos类型取变量名：
                1）如果PESQMOS有值，则MOS值列取PESQMOS，PESQLQ列取PESQLQ
                2）如果POLQA_Score_SWB有值，则MOS值和PESQLQ列均取POLQA_Score_SWB
                */
                if (MOSParamName == "" || PESQMOSParamName == "" || PESQLQParamName == "")
                {
                    if (tp is TestPoint_NR)
                    {
                        PESQMOSParamName = "NR_PESQMos";
                        float? pesq = (float?)tp["NR_PESQMos"];
                        float? polqa = (float?)tp["NR_POLQA_Score_SWB"];
                        if (pesq != null && pesq > 0 && pesq <= 5)
                        {
                            MOSParamName = "NR_PESQMos";
                            PESQLQParamName = "NR_PESQLQ";
                        }
                        else if (polqa != null && polqa > 0 && polqa <= 5)
                        {
                            MOSParamName = "NR_POLQA_Score_SWB";
                            PESQLQParamName = "NR_POLQA_Score_SWB";
                        }
                        else
                        {
                            continue;
                        }
                    }
                }

                float? mos = (float?)tp[MOSParamName];
                if (mos != null && mos > 0 && mos < mosGate)
                {
                    //设x为MOS值时间点，采样点分析的时间段为【x-10,x-2】，因为事件分析要包括切换事件所以事件的分析时间段为【x-10,x-1】，取2者最大值
                    //后面分析采样点时，需注意
                    DateTime endTime = tp.DateTime.AddSeconds(-1);
                    DateTime beginTime = tp.DateTime.AddSeconds(-10);
                    TimePeriod period = new TimePeriod(beginTime, endTime);
                    periods.Add(period);
                    pesqLst.Add(new PESQInfo((float)mos, tp[PESQLQParamName], tp[PESQMOSParamName]));
                    longitudeLst.Add(tp.Longitude);
                    latitudeLst.Add(tp.Latitude);
                }
            }
        }

        protected virtual void statInThread(object fileDataManagers)
        {
            List<DTFileDataManager> files = (List<DTFileDataManager>)fileDataManagers;
            DTFileDataManager moFile = files[0];
            DTFileDataManager mtFile = files[1];
            try
            {
                fileStated.Add(moFile.FileID);

                int tpIdx1 = 0;
                int tpIdx2 = 0;
                int evtIdx1 = 0;
                int evtIdx2 = 0;
                for (int i = 0; i < prds.Count; i++)
                {
                    TimePeriod period = prds[i];

                    List<Event> evts1 = MOSAnaManager.getEventsByPeriod(period, moFile.Events, ref evtIdx1);
                    List<Event> evts2 = MOSAnaManager.getEventsByPeriod(period, mtFile.Events, ref evtIdx2);

                    //采样点的时间段为【x-10,x-2】,x为MOS值时间点
                    TimePeriod tpPeriod = new TimePeriod(period.BeginTime, period.EndTime.AddSeconds(-1));
                    List<TestPoint> tps1 = MOSAnaManager.getTestPoinsByPeriod(tpPeriod, moFile.TestPoints, ref tpIdx1);
                    List<TestPoint> tps2 = MOSAnaManager.getTestPoinsByPeriod(tpPeriod, mtFile.TestPoints, ref tpIdx2);

                    var mosParam = new VoNRMosParam(tpPeriod.BeginTime);
                    mosParam.SN = MosParamLst.Count + 1;
                    mosParam.strFileName = moFile.FileName;
                    mosParam.dtStartTime = tpPeriod.BeginTime;
                    mosParam.dtEndTime = tpPeriod.EndTime;
                    mosParam.MOS = pesqLst[i].Mos;
                    mosParam.PESQLQ = pesqLst[i].PESQLQ;
                    mosParam.PESQMOS = pesqLst[i].PESQMOS;
                    mosParam.longitude = longitudeLst[i];
                    mosParam.latitude = latitudeLst[i];
                    mosParam.ownParam.Fill(tps1, evts1, tpPeriod.EndTime);
                    mosParam.otherParam.Fill(tps2, evts2, tpPeriod.EndTime);
                    mosParam.FillRTPInfo(tps2);
                    MosParamLst.Add(mosParam);
                }
                pesqLst.Clear();
                prds.Clear();
                longitudeLst.Clear();
                latitudeLst.Clear();
            }
            catch (System.Exception ex)
            {
                XtraMessageBox.Show(ex.ToString());
            }
            finally
            {
                System.Threading.Thread.Sleep(500);
                WaitTextBox.Close();
            }
        }

        protected virtual void FireShowForm(List<VoNRMosParam> mosParamList)
        {
        }

        public class PESQInfo
        {
            /// <summary>
            ///  PESQMOS、POLQA_Score_SWB二者取一
            /// </summary>
            public float Mos;

            /// <summary>
            /// PESQLQ、POLQA_Score_SWB二者取一
            /// </summary>
            public float? PESQLQ;

            public float? PESQMOS;

            public short? PDCCH_UL_Grant_Count;
            public short? PDCCH_DL_Grant_Count;
            public float? MCSCode0_DL;
            public float? MCSCode1_DL;
            public float? MCS_UL;
            public float? PDSCH_Code0_BLER;
            public float? PDSCH_Code1_BLER;
            public float? PUSCH_BLER;
            public short? PDCCH_CCE_Start;
            public short? PDCCH_CCEs_Number;

            public PESQInfo(float mos, float? pesqLQ, float? pesqMos)
            {
                this.Mos = mos;
                this.PESQLQ = pesqLQ;

                if (pesqMos != null && (pesqMos < 0 || pesqMos >= 5))
                {
                    this.PESQMOS = null;
                }
                else
                {
                    this.PESQMOS = pesqMos;
                }

            }
            public PESQInfo(float mos, object objLQ, object objMos)
                : this(mos, (float?)objLQ, (float?)objMos)
            {

            }

            public PESQInfo(float mos, object objLQ, object objMos, object obj_PDCCH_UL_Grant_Count,
                object obj_PDCCH_DL_Grant_Count, object obj_MCSCode0_DL, object obj_MCSCode1_DL,
                object obj_MCS_UL, object obj_PDSCH_Code0_BLER, object obj_PDSCH_Code1_BLER,
                object obj_PUSCH_BLER, object obj_PDCCH_CCE_Start, object obj_PDCCH_CCEs_Number)
            {
                this.Mos = mos;
                this.PESQLQ = (float?)objLQ;
                float? pesqMos = (float?)objMos;

                if (pesqMos != null && (pesqMos < 0 || pesqMos >= 5))
                {
                    this.PESQMOS = null;
                }
                else
                {
                    this.PESQMOS = pesqMos;
                }

                this.PDCCH_UL_Grant_Count = (short?)obj_PDCCH_UL_Grant_Count;
                this.PDCCH_DL_Grant_Count = (short?)obj_PDCCH_DL_Grant_Count;
                this.MCSCode0_DL = (float?)obj_MCSCode0_DL;
                this.MCSCode1_DL = (float?)obj_MCSCode1_DL;
                this.MCS_UL = (float?)obj_MCS_UL;
                this.PDSCH_Code0_BLER = (float?)obj_PDSCH_Code0_BLER;
                this.PDSCH_Code1_BLER = (float?)obj_PDSCH_Code1_BLER;
                this.PUSCH_BLER = (float?)obj_PUSCH_BLER;
                this.PDCCH_CCE_Start = (short?)obj_PDCCH_CCE_Start;
                this.PDCCH_CCEs_Number = (short?)obj_PDCCH_CCEs_Number;
            }

        }

    }
}
