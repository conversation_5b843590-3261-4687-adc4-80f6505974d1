﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using System.Windows.Forms;

namespace MasterCom.RAMS.BackgroundFunc
{
    public class DiyInsertTaskOrderResult : DiySqlMultiNonQuery
    {
        private readonly TaskOrderManageResult curTaskOrderResult;
        public DiyInsertTaskOrderResult(TaskOrderManageResult curTaskOrderResult)
        {
            MainDB = true;
            this.curTaskOrderResult = curTaskOrderResult;
        }

        protected override void query()
        {
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, userName, password, dbid > 0 ? dbid : MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
                queryInThread(clientProxy);
            }
            catch (Exception ee)
            {
                log.Error(ee.Message + Environment.NewLine + ee.Source + Environment.NewLine + ee.StackTrace);
            }
            finally
            {
                clientProxy.Close();
            }
        }

        protected override string getSqlTextString()
        {
            StringBuilder strb = new StringBuilder();
            strb.AppendFormat(@"IF EXISTS(SELECT 1 FROM [tb_lowtask_result] where [工单号] = '{0}') update [tb_lowtask_result] set [GSM测试公里数]={1},[GSM测试时长]={2},[LTE测试公里数]={3},[LTE测试时长]={4},[附件文件名]='{5}',[附件下载地址]='{6}',[附件大小]={7},[上传状态]={8},[上传时间]='{9}',[备注]=[上传状态],[异网测试公里数]={11},[异网测试时长]={12} where [工单号] = '{0}' ELSE insert into [tb_lowtask_result] ([工单号],[GSM测试公里数],[GSM测试时长],[LTE测试公里数],[LTE测试时长],[附件文件名],[附件下载地址],[附件大小],[上传状态],[上传时间],[备注],[异网测试公里数],[异网测试时长]) values ('{0}',{1},{2},{3},{4},'{5}','{6}',{7},{8},'{9}','{10}',{11},{12});",
                curTaskOrderResult.OrderID,
                curTaskOrderResult.CM2GDistance, curTaskOrderResult.CM2GDuration, curTaskOrderResult.CM4GDistance, curTaskOrderResult.CM4GDuration,
                curTaskOrderResult.AttachName, curTaskOrderResult.AttachUrl, curTaskOrderResult.AttachSize,
                curTaskOrderResult.UpLoadState, curTaskOrderResult.UpLoadDateTime, curTaskOrderResult.Remark,
                curTaskOrderResult.DiffNetDistance, curTaskOrderResult.DiffNetDuration);
            return strb.ToString();
        }

        public override string Name
        {
            get { return "导入工单信息结果"; }
        }
    }
}
