﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using System.IO;
using MasterCom.Util;
using System.Xml;

namespace MasterCom.RAMS.Frame
{
    public partial class DeviceIDPanel : UserControl
    {
        private ToolStripDropDown dropDownParent;
        private Label labelCount;
        private Dictionary<int, string> deviceDict;
        private List<int> selIDList;

        public DeviceIDPanel(ToolStripDropDown parent, Label label, List<int> selIDList)
        {
            InitializeComponent();
            dropDownParent = parent;
            labelCount = label;
            this.selIDList = selIDList;
            btnOK.Click += BtnOK_Click;
            chkAll.CheckedChanged += ChkAll_CheckedChanged;
            dropDownParent.Closed += DropDown_Closed;
            listView.ItemChecked += ListView_CheckedChanged;
        }

        private void BtnOK_Click(object sender, EventArgs e)
        {
            dropDownParent.Close();
        }

        private void ChkAll_CheckedChanged(object sender, EventArgs e)
        {
            listView.ItemChecked -= ListView_CheckedChanged;
            foreach (ListViewItem item in listView.Items)
            {
                item.Checked = chkAll.Checked;
            }
            listView.ItemChecked += ListView_CheckedChanged;
        }

        private void ListView_CheckedChanged(object sender, ItemCheckedEventArgs e)
        {
            bool allFlag = true;
            foreach (ListViewItem item in listView.Items)
            {
                if (!item.Checked)
                {
                    allFlag = false;
                    break;
                }
            }
            chkAll.CheckedChanged -= ChkAll_CheckedChanged;
            chkAll.Checked = allFlag;
            chkAll.CheckedChanged += ChkAll_CheckedChanged;
        }

        private void DropDown_Closed(object sender, ToolStripDropDownClosedEventArgs e)
        {
            SetResult();
        }

        private void SetResult()
        {
            int cnt = 0;
            selIDList.Clear();
            foreach (ListViewItem item in listView.Items)
            {
                if (item.Checked)
                {
                    ++cnt;
                    selIDList.Add(((KeyValuePair<int, string>)item.Tag).Key);
                }
            }
            labelCount.Text = "[" + (cnt == deviceDict.Count ? "所有" : cnt.ToString()) + "]";
        }

        public int LoadDevices()
        {
            deviceDict = DeviceIDManager.GetDevices();            
            InitListView();
            this.chkAll.Checked = true;
            SetResult();
            return deviceDict.Count;
        }

        public void SetDeviceChecked(List<int> ids)
        {
            foreach (ListViewItem item in listView.Items)
            {
                if (ids.Contains(((KeyValuePair<int, string>)item.Tag).Key))
                {
                    item.Checked = true;
                }
                else
                {
                    item.Checked = false;
                }
            }
            SetResult();
        }

        private void InitListView()
        {
            foreach (KeyValuePair<int, string> kvp in deviceDict)
            {
                ListViewItem item = new ListViewItem();
                item.Tag = kvp;
                item.Text = kvp.Value;
                listView.Items.Add(item);
            }
        }
    }

    public static class DeviceIDManager
    {
        public static Dictionary<int, string> GetDevices()
        {
            return new Dictionary<int, string>(idNameDict);
        }

        public static int Count
        {
            get
            {
                return idNameDict.Count;
            }
        }

        public static string GetName(int id)
        {
            if (idNameDict.ContainsKey(id))
            {
                return idNameDict[id];
            }
            return "";
        }

        static DeviceIDManager()
        {
            var devicces = ((CategoryEnum)CategoryManager.GetInstance()["DeviceType"]).Items;
            Array.Sort(devicces, (a, b) => { return a.ID.CompareTo(b.ID); });
            foreach (var device in devicces)
            {
                idNameDict[device.ID] = device.Description;
            }

            //不知道为何要加载配置文件,导致要维护多个地方,先屏蔽掉,直接使用数据库的数据
            //if (File.Exists(configFileName))
            //{
            //    LoadFromFile();
            //}
            //else
            //{
            //    LoadFromDefault();
            //}
        }

        //private static void LoadFromFile()
        //{
        //    XmlConfigFile configFile = new XmlConfigFile(configFileName);
        //    List<object> lst = configFile.GetItemValue(deviceConfigName, deviceListName, GetDeviceItem) as List<object>;

        //    if (lst == null)
        //    {
        //        LoadFromDefault();
        //        return;
        //    }

        //    foreach (object obj in lst)
        //    {
        //        KeyValuePair<int, string> kvp = (KeyValuePair<int, string>)obj;
        //        if (kvp.Key == -1)
        //        {
        //            continue;
        //        }
        //        idNameDict[kvp.Key] = kvp.Value;
        //    }
        //}

        //private static void LoadFromDefault()
        //{
        //    for (int i = 0; i < DefaultDevices.Length; ++i)
        //    {
        //        idNameDict[i + 1] = DefaultDevices[i];
        //    }
        //}

        //private static object GetDeviceItem(XmlConfigFile configFile, XmlElement element, string typeName)
        //{
        //    try
        //    {
        //        int key = (int)configFile.GetItemValue(element, "key");
        //        string value = (string)configFile.GetItemValue(element, "value");
        //        return new KeyValuePair<int, string>(key, value);
        //    }
        //    catch
        //    {
        //        return new KeyValuePair<int, string>(-1, "");
        //    }
        //}

        private static Dictionary<int, string> idNameDict = new Dictionary<int, string>();
        //private static string configFileName = Application.StartupPath + @"\config\DeviceList.xml";
        //private static string deviceConfigName = "DeviceConfig";
        //private static string deviceListName = "DeviceList";
        //private static string[] DefaultDevices =
        //{
        //    "爱立信TEMS",
        //    "诺基亚NEMO设备",
        //    "珠海万禾ANT设备",
        //    "珠海鼎利",
        //    "科虹扫频",
        //    "珠海鼎利自动测试",
        //    "爱立信自动测试",
        //    "华星设备",
        //    "日讯设备",
        //    "创远设备",
        //    "CDS设备",
        //    "Pioneer设备",
        //    "PANORAMA设备",
        //    "大唐设备",
        //    "microsoft",
        //    "迈为设备",
        //    "鼎星设备",
        //    "万禾设备",
        //    "TDSCDMA",
        //    "罗德斯瓦茨",
        //    "中兴设备",
        //    "丰联设备",
        //    "华为设备",
        //    "京信设备",
        //    "经纬设备",
        //    "安捷伦设备",
        //    "诺优设备",
        //    "烽火设备",
        //    "虹信设备",
        //    "NetworkStumbler",
        //    "艾尔麦设备",
        //    "泰合佳通设备",
        //    "卓信设备",
        //    "欧佩泰斯",
        //    "名通科技",
        //    "高通设备",
        //    "铁科院GSMR",
        //    "神州泰岳优化宝",
        //    "高通设备",
        //    "铭润设备",
        //    "天津PCTEL设备",
        //    "惠捷朗设备"
        //};
    }
}
