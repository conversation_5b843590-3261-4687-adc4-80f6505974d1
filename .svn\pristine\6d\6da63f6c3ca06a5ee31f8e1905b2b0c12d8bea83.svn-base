﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class PingPangResultForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            DevExpress.XtraGrid.GridLevelNode gridLevelNode1 = new DevExpress.XtraGrid.GridLevelNode();
            DevExpress.XtraGrid.GridLevelNode gridLevelNode2 = new DevExpress.XtraGrid.GridLevelNode();
            this.gvSeqPair = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn6 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcSequence = new DevExpress.XtraGrid.GridControl();
            this.gvSeqFile = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gvCellItem = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn17 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn18 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn19 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn23 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn24 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcCell = new DevExpress.XtraGrid.GridControl();
            this.gvCellFile = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn20 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn21 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn22 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.panel1 = new System.Windows.Forms.Panel();
            this.chkSpeedLimit = new System.Windows.Forms.CheckBox();
            this.numSpeedMin = new System.Windows.Forms.NumericUpDown();
            this.label4 = new System.Windows.Forms.Label();
            this.numSpeedMax = new System.Windows.Forms.NumericUpDown();
            this.label5 = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.btnAnalyze = new System.Windows.Forms.Button();
            this.numTimeLimit = new System.Windows.Forms.NumericUpDown();
            this.label2 = new System.Windows.Forms.Label();
            this.tabControl1 = new System.Windows.Forms.TabControl();
            this.contextMenuStrip1 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.tabPage1 = new System.Windows.Forms.TabPage();
            this.splitContainer1 = new System.Windows.Forms.SplitContainer();
            this.gcDetail = new DevExpress.XtraGrid.GridControl();
            this.gvSeqEvent = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn10 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn11 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn12 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn13 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn14 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn15 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn16 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridView3 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn7 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn8 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn9 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.tabPage2 = new System.Windows.Forms.TabPage();
            ((System.ComponentModel.ISupportInitialize)(this.gvSeqPair)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gcSequence)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvSeqFile)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvCellItem)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gcCell)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvCellFile)).BeginInit();
            this.panel1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numSpeedMin)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSpeedMax)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numTimeLimit)).BeginInit();
            this.tabControl1.SuspendLayout();
            this.contextMenuStrip1.SuspendLayout();
            this.tabPage1.SuspendLayout();
            this.splitContainer1.Panel1.SuspendLayout();
            this.splitContainer1.Panel2.SuspendLayout();
            this.splitContainer1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gcDetail)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvSeqEvent)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView3)).BeginInit();
            this.tabPage2.SuspendLayout();
            this.SuspendLayout();
            // 
            // gvSeqPair
            // 
            this.gvSeqPair.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn4,
            this.gridColumn5,
            this.gridColumn6});
            this.gvSeqPair.GridControl = this.gcSequence;
            this.gvSeqPair.Name = "gvSeqPair";
            this.gvSeqPair.OptionsBehavior.Editable = false;
            this.gvSeqPair.OptionsBehavior.ReadOnly = true;
            this.gvSeqPair.OptionsView.ShowGroupPanel = false;
            this.gvSeqPair.OptionsView.ShowIndicator = false;
            // 
            // gridColumn4
            // 
            this.gridColumn4.Caption = "切换序号";
            this.gridColumn4.FieldName = "SN";
            this.gridColumn4.Name = "gridColumn4";
            this.gridColumn4.Visible = true;
            this.gridColumn4.VisibleIndex = 0;
            this.gridColumn4.Width = 136;
            // 
            // gridColumn5
            // 
            this.gridColumn5.Caption = "切换小区";
            this.gridColumn5.FieldName = "Desc";
            this.gridColumn5.Name = "gridColumn5";
            this.gridColumn5.Visible = true;
            this.gridColumn5.VisibleIndex = 1;
            this.gridColumn5.Width = 718;
            // 
            // gridColumn6
            // 
            this.gridColumn6.Caption = "切换间隔(秒)";
            this.gridColumn6.FieldName = "Interval";
            this.gridColumn6.Name = "gridColumn6";
            this.gridColumn6.Visible = true;
            this.gridColumn6.VisibleIndex = 2;
            this.gridColumn6.Width = 138;
            // 
            // gcSequence
            // 
            this.gcSequence.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gcSequence.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gcSequence.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gcSequence.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gcSequence.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gcSequence.EmbeddedNavigator.Buttons.NextPage.Visible = false;
            this.gcSequence.EmbeddedNavigator.Buttons.PrevPage.Visible = false;
            this.gcSequence.EmbeddedNavigator.Buttons.Remove.Visible = false;
            gridLevelNode1.LevelTemplate = this.gvSeqPair;
            gridLevelNode1.RelationName = "Pairs";
            this.gcSequence.LevelTree.Nodes.AddRange(new DevExpress.XtraGrid.GridLevelNode[] {
            gridLevelNode1});
            this.gcSequence.Location = new System.Drawing.Point(0, 0);
            this.gcSequence.MainView = this.gvSeqFile;
            this.gcSequence.Name = "gcSequence";
            this.gcSequence.ShowOnlyPredefinedDetails = true;
            this.gcSequence.Size = new System.Drawing.Size(996, 323);
            this.gcSequence.TabIndex = 5;
            this.gcSequence.UseEmbeddedNavigator = true;
            this.gcSequence.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gvSeqFile,
            this.gvSeqPair});
            // 
            // gvSeqFile
            // 
            this.gvSeqFile.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn1,
            this.gridColumn2,
            this.gridColumn3});
            this.gvSeqFile.GridControl = this.gcSequence;
            this.gvSeqFile.Name = "gvSeqFile";
            this.gvSeqFile.OptionsBehavior.Editable = false;
            this.gvSeqFile.OptionsDetail.ShowDetailTabs = false;
            this.gvSeqFile.OptionsView.ColumnAutoWidth = false;
            this.gvSeqFile.OptionsView.ShowGroupPanel = false;
            this.gvSeqFile.OptionsView.ShowIndicator = false;
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "文件序号";
            this.gridColumn1.FieldName = "SN";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.Visible = true;
            this.gridColumn1.VisibleIndex = 0;
            this.gridColumn1.Width = 110;
            // 
            // gridColumn2
            // 
            this.gridColumn2.Caption = "文件名称";
            this.gridColumn2.FieldName = "FileName";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.Visible = true;
            this.gridColumn2.VisibleIndex = 1;
            this.gridColumn2.Width = 700;
            // 
            // gridColumn3
            // 
            this.gridColumn3.Caption = "乒乓切换组数";
            this.gridColumn3.FieldName = "PairCount";
            this.gridColumn3.Name = "gridColumn3";
            this.gridColumn3.Visible = true;
            this.gridColumn3.VisibleIndex = 2;
            this.gridColumn3.Width = 103;
            // 
            // gvCellItem
            // 
            this.gvCellItem.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn17,
            this.gridColumn18,
            this.gridColumn19,
            this.gridColumn23,
            this.gridColumn24});
            this.gvCellItem.GridControl = this.gcCell;
            this.gvCellItem.Name = "gvCellItem";
            this.gvCellItem.OptionsBehavior.Editable = false;
            this.gvCellItem.OptionsBehavior.ReadOnly = true;
            this.gvCellItem.OptionsView.ShowGroupPanel = false;
            this.gvCellItem.OptionsView.ShowIndicator = false;
            // 
            // gridColumn17
            // 
            this.gridColumn17.Caption = "小区序号";
            this.gridColumn17.FieldName = "SN";
            this.gridColumn17.Name = "gridColumn17";
            this.gridColumn17.Visible = true;
            this.gridColumn17.VisibleIndex = 0;
            this.gridColumn17.Width = 120;
            // 
            // gridColumn18
            // 
            this.gridColumn18.Caption = "小区名称";
            this.gridColumn18.FieldName = "Name";
            this.gridColumn18.Name = "gridColumn18";
            this.gridColumn18.Visible = true;
            this.gridColumn18.VisibleIndex = 1;
            this.gridColumn18.Width = 300;
            // 
            // gridColumn19
            // 
            this.gridColumn19.Caption = "TAC";
            this.gridColumn19.FieldName = "ILac";
            this.gridColumn19.Name = "gridColumn19";
            this.gridColumn19.Visible = true;
            this.gridColumn19.VisibleIndex = 2;
            this.gridColumn19.Width = 150;
            // 
            // gridColumn23
            // 
            this.gridColumn23.Caption = "ECI";
            this.gridColumn23.FieldName = "ICi";
            this.gridColumn23.Name = "gridColumn23";
            this.gridColumn23.Visible = true;
            this.gridColumn23.VisibleIndex = 3;
            this.gridColumn23.Width = 150;
            // 
            // gridColumn24
            // 
            this.gridColumn24.Caption = "切换次数";
            this.gridColumn24.FieldName = "Count";
            this.gridColumn24.Name = "gridColumn24";
            this.gridColumn24.Visible = true;
            this.gridColumn24.VisibleIndex = 4;
            this.gridColumn24.Width = 272;
            // 
            // gcCell
            // 
            this.gcCell.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gcCell.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gcCell.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gcCell.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gcCell.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gcCell.EmbeddedNavigator.Buttons.NextPage.Visible = false;
            this.gcCell.EmbeddedNavigator.Buttons.PrevPage.Visible = false;
            this.gcCell.EmbeddedNavigator.Buttons.Remove.Visible = false;
            gridLevelNode2.LevelTemplate = this.gvCellItem;
            gridLevelNode2.RelationName = "Cells";
            this.gcCell.LevelTree.Nodes.AddRange(new DevExpress.XtraGrid.GridLevelNode[] {
            gridLevelNode2});
            this.gcCell.Location = new System.Drawing.Point(3, 3);
            this.gcCell.MainView = this.gvCellFile;
            this.gcCell.Name = "gcCell";
            this.gcCell.ShowOnlyPredefinedDetails = true;
            this.gcCell.Size = new System.Drawing.Size(996, 477);
            this.gcCell.TabIndex = 6;
            this.gcCell.UseEmbeddedNavigator = true;
            this.gcCell.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gvCellFile,
            this.gvCellItem});
            // 
            // gvCellFile
            // 
            this.gvCellFile.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn20,
            this.gridColumn21,
            this.gridColumn22});
            this.gvCellFile.GridControl = this.gcCell;
            this.gvCellFile.Name = "gvCellFile";
            this.gvCellFile.OptionsBehavior.Editable = false;
            this.gvCellFile.OptionsDetail.ShowDetailTabs = false;
            this.gvCellFile.OptionsView.ColumnAutoWidth = false;
            this.gvCellFile.OptionsView.ShowGroupPanel = false;
            this.gvCellFile.OptionsView.ShowIndicator = false;
            // 
            // gridColumn20
            // 
            this.gridColumn20.Caption = "文件序号";
            this.gridColumn20.FieldName = "SN";
            this.gridColumn20.Name = "gridColumn20";
            this.gridColumn20.Visible = true;
            this.gridColumn20.VisibleIndex = 0;
            this.gridColumn20.Width = 110;
            // 
            // gridColumn21
            // 
            this.gridColumn21.Caption = "文件名称";
            this.gridColumn21.FieldName = "FileName";
            this.gridColumn21.Name = "gridColumn21";
            this.gridColumn21.Visible = true;
            this.gridColumn21.VisibleIndex = 1;
            this.gridColumn21.Width = 700;
            // 
            // gridColumn22
            // 
            this.gridColumn22.Caption = "小区个数";
            this.gridColumn22.FieldName = "CellCount";
            this.gridColumn22.Name = "gridColumn22";
            this.gridColumn22.Visible = true;
            this.gridColumn22.VisibleIndex = 2;
            this.gridColumn22.Width = 103;
            // 
            // panel1
            // 
            this.panel1.Controls.Add(this.chkSpeedLimit);
            this.panel1.Controls.Add(this.numSpeedMin);
            this.panel1.Controls.Add(this.label4);
            this.panel1.Controls.Add(this.numSpeedMax);
            this.panel1.Controls.Add(this.label5);
            this.panel1.Controls.Add(this.label3);
            this.panel1.Controls.Add(this.btnAnalyze);
            this.panel1.Controls.Add(this.numTimeLimit);
            this.panel1.Controls.Add(this.label2);
            this.panel1.Dock = System.Windows.Forms.DockStyle.Top;
            this.panel1.Location = new System.Drawing.Point(0, 0);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(1010, 45);
            this.panel1.TabIndex = 1;
            // 
            // chkSpeedLimit
            // 
            this.chkSpeedLimit.AutoSize = true;
            this.chkSpeedLimit.Font = new System.Drawing.Font("SimSun", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.chkSpeedLimit.Location = new System.Drawing.Point(206, 14);
            this.chkSpeedLimit.Name = "chkSpeedLimit";
            this.chkSpeedLimit.Size = new System.Drawing.Size(96, 16);
            this.chkSpeedLimit.TabIndex = 15;
            this.chkSpeedLimit.Text = "启用车速限制";
            this.chkSpeedLimit.UseVisualStyleBackColor = true;
            // 
            // numSpeedMin
            // 
            this.numSpeedMin.Font = new System.Drawing.Font("SimSun", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numSpeedMin.Increment = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.numSpeedMin.Location = new System.Drawing.Point(304, 11);
            this.numSpeedMin.Maximum = new decimal(new int[] {
            200,
            0,
            0,
            0});
            this.numSpeedMin.Name = "numSpeedMin";
            this.numSpeedMin.Size = new System.Drawing.Size(75, 21);
            this.numSpeedMin.TabIndex = 13;
            this.numSpeedMin.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Font = new System.Drawing.Font("SimSun", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label4.Location = new System.Drawing.Point(539, 16);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(59, 12);
            this.label4.TabIndex = 9;
            this.label4.Text = "公里/小时";
            // 
            // numSpeedMax
            // 
            this.numSpeedMax.Font = new System.Drawing.Font("SimSun", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numSpeedMax.Increment = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.numSpeedMax.Location = new System.Drawing.Point(458, 11);
            this.numSpeedMax.Maximum = new decimal(new int[] {
            200,
            0,
            0,
            0});
            this.numSpeedMax.Name = "numSpeedMax";
            this.numSpeedMax.Size = new System.Drawing.Size(75, 21);
            this.numSpeedMax.TabIndex = 7;
            this.numSpeedMax.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numSpeedMax.Value = new decimal(new int[] {
            180,
            0,
            0,
            0});
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Font = new System.Drawing.Font("SimSun", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label5.Location = new System.Drawing.Point(387, 16);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(65, 12);
            this.label5.TabIndex = 8;
            this.label5.Text = "≤ 时速 ≤";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Font = new System.Drawing.Font("SimSun", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label3.Location = new System.Drawing.Point(167, 16);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(17, 12);
            this.label3.TabIndex = 5;
            this.label3.Text = "秒";
            // 
            // btnAnalyze
            // 
            this.btnAnalyze.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnAnalyze.Location = new System.Drawing.Point(913, 7);
            this.btnAnalyze.Name = "btnAnalyze";
            this.btnAnalyze.Size = new System.Drawing.Size(87, 27);
            this.btnAnalyze.TabIndex = 4;
            this.btnAnalyze.Text = "重新统计";
            this.btnAnalyze.UseVisualStyleBackColor = true;
            // 
            // numTimeLimit
            // 
            this.numTimeLimit.Font = new System.Drawing.Font("SimSun", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numTimeLimit.Increment = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.numTimeLimit.Location = new System.Drawing.Point(89, 11);
            this.numTimeLimit.Maximum = new decimal(new int[] {
            60,
            0,
            0,
            0});
            this.numTimeLimit.Name = "numTimeLimit";
            this.numTimeLimit.Size = new System.Drawing.Size(75, 21);
            this.numTimeLimit.TabIndex = 3;
            this.numTimeLimit.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numTimeLimit.Value = new decimal(new int[] {
            10,
            0,
            0,
            0});
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Font = new System.Drawing.Font("SimSun", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label2.Location = new System.Drawing.Point(12, 16);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(71, 12);
            this.label2.TabIndex = 4;
            this.label2.Text = "时间间隔 ≤";
            // 
            // tabControl1
            // 
            this.tabControl1.ContextMenuStrip = this.contextMenuStrip1;
            this.tabControl1.Controls.Add(this.tabPage1);
            this.tabControl1.Controls.Add(this.tabPage2);
            this.tabControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabControl1.Location = new System.Drawing.Point(0, 45);
            this.tabControl1.Name = "tabControl1";
            this.tabControl1.SelectedIndex = 0;
            this.tabControl1.Size = new System.Drawing.Size(1010, 510);
            this.tabControl1.TabIndex = 2;
            // 
            // contextMenuStrip1
            // 
            this.contextMenuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExportExcel});
            this.contextMenuStrip1.Name = "contextMenuStrip1";
            this.contextMenuStrip1.Size = new System.Drawing.Size(139, 26);
            // 
            // miExportExcel
            // 
            this.miExportExcel.Name = "miExportExcel";
            this.miExportExcel.Size = new System.Drawing.Size(138, 22);
            this.miExportExcel.Text = "导出Excel...";
            // 
            // tabPage1
            // 
            this.tabPage1.Controls.Add(this.splitContainer1);
            this.tabPage1.Location = new System.Drawing.Point(4, 23);
            this.tabPage1.Name = "tabPage1";
            this.tabPage1.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage1.Size = new System.Drawing.Size(1002, 483);
            this.tabPage1.TabIndex = 0;
            this.tabPage1.Text = "切换序列";
            this.tabPage1.UseVisualStyleBackColor = true;
            // 
            // splitContainer1
            // 
            this.splitContainer1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainer1.Location = new System.Drawing.Point(3, 3);
            this.splitContainer1.Name = "splitContainer1";
            this.splitContainer1.Orientation = System.Windows.Forms.Orientation.Horizontal;
            // 
            // splitContainer1.Panel1
            // 
            this.splitContainer1.Panel1.Controls.Add(this.gcSequence);
            // 
            // splitContainer1.Panel2
            // 
            this.splitContainer1.Panel2.Controls.Add(this.gcDetail);
            this.splitContainer1.Size = new System.Drawing.Size(996, 477);
            this.splitContainer1.SplitterDistance = 323;
            this.splitContainer1.TabIndex = 1;
            // 
            // gcDetail
            // 
            this.gcDetail.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gcDetail.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gcDetail.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gcDetail.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gcDetail.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gcDetail.EmbeddedNavigator.Buttons.NextPage.Visible = false;
            this.gcDetail.EmbeddedNavigator.Buttons.PrevPage.Visible = false;
            this.gcDetail.EmbeddedNavigator.Buttons.Remove.Visible = false;
            this.gcDetail.Location = new System.Drawing.Point(0, 0);
            this.gcDetail.MainView = this.gvSeqEvent;
            this.gcDetail.Name = "gcDetail";
            this.gcDetail.ShowOnlyPredefinedDetails = true;
            this.gcDetail.Size = new System.Drawing.Size(996, 150);
            this.gcDetail.TabIndex = 6;
            this.gcDetail.UseEmbeddedNavigator = true;
            this.gcDetail.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gvSeqEvent,
            this.gridView3});
            // 
            // gvSeqEvent
            // 
            this.gvSeqEvent.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn10,
            this.gridColumn11,
            this.gridColumn12,
            this.gridColumn13,
            this.gridColumn14,
            this.gridColumn15,
            this.gridColumn16});
            this.gvSeqEvent.GridControl = this.gcDetail;
            this.gvSeqEvent.Name = "gvSeqEvent";
            this.gvSeqEvent.OptionsBehavior.Editable = false;
            this.gvSeqEvent.OptionsDetail.ShowDetailTabs = false;
            this.gvSeqEvent.OptionsView.ShowGroupPanel = false;
            this.gvSeqEvent.OptionsView.ShowIndicator = false;
            // 
            // gridColumn10
            // 
            this.gridColumn10.Caption = "切换时间";
            this.gridColumn10.FieldName = "TimeString";
            this.gridColumn10.Name = "gridColumn10";
            this.gridColumn10.Visible = true;
            this.gridColumn10.VisibleIndex = 0;
            this.gridColumn10.Width = 120;
            // 
            // gridColumn11
            // 
            this.gridColumn11.Caption = "切换前小区";
            this.gridColumn11.FieldName = "SrcCellName";
            this.gridColumn11.Name = "gridColumn11";
            this.gridColumn11.Visible = true;
            this.gridColumn11.VisibleIndex = 1;
            this.gridColumn11.Width = 190;
            // 
            // gridColumn12
            // 
            this.gridColumn12.Caption = "切换后小区";
            this.gridColumn12.FieldName = "TarCellName";
            this.gridColumn12.Name = "gridColumn12";
            this.gridColumn12.Visible = true;
            this.gridColumn12.VisibleIndex = 2;
            this.gridColumn12.Width = 190;
            // 
            // gridColumn13
            // 
            this.gridColumn13.Caption = "前场强";
            this.gridColumn13.FieldName = "BeforeRsrp";
            this.gridColumn13.Name = "gridColumn13";
            this.gridColumn13.Visible = true;
            this.gridColumn13.VisibleIndex = 3;
            this.gridColumn13.Width = 122;
            // 
            // gridColumn14
            // 
            this.gridColumn14.Caption = "后场强";
            this.gridColumn14.FieldName = "AfterRsrp";
            this.gridColumn14.Name = "gridColumn14";
            this.gridColumn14.Visible = true;
            this.gridColumn14.VisibleIndex = 4;
            this.gridColumn14.Width = 122;
            // 
            // gridColumn15
            // 
            this.gridColumn15.Caption = "前质量";
            this.gridColumn15.FieldName = "BeforeSinr";
            this.gridColumn15.Name = "gridColumn15";
            this.gridColumn15.Visible = true;
            this.gridColumn15.VisibleIndex = 5;
            this.gridColumn15.Width = 122;
            // 
            // gridColumn16
            // 
            this.gridColumn16.Caption = "后质量";
            this.gridColumn16.FieldName = "AfterSinr";
            this.gridColumn16.Name = "gridColumn16";
            this.gridColumn16.Visible = true;
            this.gridColumn16.VisibleIndex = 6;
            this.gridColumn16.Width = 126;
            // 
            // gridView3
            // 
            this.gridView3.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn7,
            this.gridColumn8,
            this.gridColumn9});
            this.gridView3.GridControl = this.gcDetail;
            this.gridView3.Name = "gridView3";
            this.gridView3.OptionsBehavior.Editable = false;
            this.gridView3.OptionsBehavior.ReadOnly = true;
            this.gridView3.OptionsView.ColumnAutoWidth = false;
            this.gridView3.OptionsView.ShowGroupPanel = false;
            this.gridView3.OptionsView.ShowIndicator = false;
            // 
            // gridColumn7
            // 
            this.gridColumn7.Caption = "切换序号";
            this.gridColumn7.FieldName = "SN";
            this.gridColumn7.Name = "gridColumn7";
            this.gridColumn7.Visible = true;
            this.gridColumn7.VisibleIndex = 0;
            this.gridColumn7.Width = 112;
            // 
            // gridColumn8
            // 
            this.gridColumn8.Caption = "切换小区";
            this.gridColumn8.FieldName = "Desc";
            this.gridColumn8.Name = "gridColumn8";
            this.gridColumn8.Visible = true;
            this.gridColumn8.VisibleIndex = 1;
            this.gridColumn8.Width = 343;
            // 
            // gridColumn9
            // 
            this.gridColumn9.Caption = "切换间隔(秒)";
            this.gridColumn9.FieldName = "Interval";
            this.gridColumn9.Name = "gridColumn9";
            this.gridColumn9.Visible = true;
            this.gridColumn9.VisibleIndex = 2;
            this.gridColumn9.Width = 123;
            // 
            // tabPage2
            // 
            this.tabPage2.Controls.Add(this.gcCell);
            this.tabPage2.Location = new System.Drawing.Point(4, 23);
            this.tabPage2.Name = "tabPage2";
            this.tabPage2.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage2.Size = new System.Drawing.Size(1002, 483);
            this.tabPage2.TabIndex = 1;
            this.tabPage2.Text = "切换小区";
            this.tabPage2.UseVisualStyleBackColor = true;
            // 
            // PingPangResultForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1010, 555);
            this.Controls.Add(this.tabControl1);
            this.Controls.Add(this.panel1);
            this.Name = "PingPangResultForm";
            this.Text = "乒乓切换分析";
            ((System.ComponentModel.ISupportInitialize)(this.gvSeqPair)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gcSequence)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvSeqFile)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvCellItem)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gcCell)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvCellFile)).EndInit();
            this.panel1.ResumeLayout(false);
            this.panel1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numSpeedMin)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSpeedMax)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numTimeLimit)).EndInit();
            this.tabControl1.ResumeLayout(false);
            this.contextMenuStrip1.ResumeLayout(false);
            this.tabPage1.ResumeLayout(false);
            this.splitContainer1.Panel1.ResumeLayout(false);
            this.splitContainer1.Panel2.ResumeLayout(false);
            this.splitContainer1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gcDetail)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvSeqEvent)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView3)).EndInit();
            this.tabPage2.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.Panel panel1;
        private System.Windows.Forms.CheckBox chkSpeedLimit;
        private System.Windows.Forms.NumericUpDown numSpeedMin;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.NumericUpDown numSpeedMax;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Button btnAnalyze;
        private System.Windows.Forms.NumericUpDown numTimeLimit;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.TabControl tabControl1;
        private System.Windows.Forms.TabPage tabPage1;
        private System.Windows.Forms.TabPage tabPage2;
        private System.Windows.Forms.SplitContainer splitContainer1;
        private DevExpress.XtraGrid.GridControl gcSequence;
        private DevExpress.XtraGrid.Views.Grid.GridView gvSeqPair;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn6;
        private DevExpress.XtraGrid.Views.Grid.GridView gvSeqFile;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private DevExpress.XtraGrid.GridControl gcDetail;
        private DevExpress.XtraGrid.Views.Grid.GridView gvSeqEvent;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn10;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn11;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn12;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn7;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn8;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn9;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn13;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn14;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn15;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn16;
        private DevExpress.XtraGrid.GridControl gcCell;
        private DevExpress.XtraGrid.Views.Grid.GridView gvCellItem;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn17;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn18;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn19;
        private DevExpress.XtraGrid.Views.Grid.GridView gvCellFile;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn20;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn21;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn22;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn23;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn24;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip1;
        private System.Windows.Forms.ToolStripMenuItem miExportExcel;
    }
}