﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Xml;
using MasterCom.Util;

namespace MasterCom.ES.Data
{
    public class L3MsgParam
    {
        public int msgId { get; set; }
        //
        public string keyStr { get; set; } = "";
        /// <summary>
        /// -1,Single
        /// >0 Multi
        /// </summary>
        public int argAt { get; set; } = -1;
        /// <summary>
        /// 0 int
        /// 1 string
        /// 2 num
        /// </summary>
        public int valueType { get; set; } = 0;

        public static XmlElement AddItem(XmlConfigFile configFile, XmlElement config, string name, object value)
        {
            if (value is L3MsgParam)
            {
                XmlElement item = configFile.AddItem(config, name, value.GetType());
                configFile.AddItem(item, "L3MsgParam", (value as L3MsgParam).Param);
                return item;
            }
            return null;
        }

        public static object GetItemValue(XmlConfigFile configFile, XmlElement item, string typeName)
        {
            if (typeName.Equals(typeof(L3MsgParam).Name))
            {
                Dictionary<string, object> param = configFile.GetItemValue(item, "L3MsgParam") as Dictionary<string, object>;
                L3MsgParam p = new L3MsgParam();
                p.Param = param;
                return p;
            }
            return null;
        }
        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["msgId"] = msgId;
                param["keyStr"] = keyStr;
                param["argAt"] = argAt;
                param["valueType"] = valueType;
                return param;
            }
            set
            {
                if (value == null || value.Count == 0)
                {
                    return;
                }
                this.msgId = (int)value["msgId"];
                this.keyStr = (string)value["keyStr"];
                this.argAt = (int)value["argAt"];
                this.valueType = (int)value["valueType"];
            }
        }
        public override string ToString()
        {
            return keyStr + " " + GetDesc();
        }
        public string GetDesc()
        {
            if (valueType == 0)
            {
                if (argAt >= 0)
                {
                    return "UINT[" + argAt + "]";
                }
                else
                {
                    return "UINT";
                }
            }
            else if (valueType == 1)
            {
                if (argAt >= 0)
                {
                    return "string[" + argAt + "]";
                }
                else
                {
                    return "string";
                }
            }
            else if (valueType == 2)
            {
                return "参数个数";
            }
            else
            {
                return "Unknown ERROR";
            }
        }
    }
}
