﻿using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    class LteFddSmallStationAcceptQuery : LteFddIndoorStationAcceptQuery
    {
        public LteFddSmallStationAcceptQuery(MainModel mainModel)
            : base(mainModel)
        {

        }

        public override string Name
        {
            get
            {
                return "LTEFDD小站验收";
            }
        }

        protected override void initManager(LteStationAcceptCondition cond)
        {
            manager = new LteFddSmallStationAcceptManager();
            manager.SetAcceptCond(cond);
        }
    }
}
