﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Stat.Data;

namespace MasterCom.RAMS.Model.Interface
{
    public static class EventStatFilterFactory
    {
        private static List<EventStatFilter> list = null;
        public static List<EventStatFilter> GetEventStatFilters()
        {
            if(list ==null)
            {
                list = new List<EventStatFilter>();
                list.Add(new CellReselectionTimeFilter());
            }
            return list;
        }
    };
    public abstract class EventStatFilter
    {
        public override string ToString()
        {
            return "未命名";
        }
        public virtual bool CheckEventFilter(DataEvent data)
        {
            return true;
        }
    }
    /// <summary>
    /// 小区重选时长统计过滤，过滤掉ivalue5<=0的
    /// </summary>
    public class CellReselectionTimeFilter : EventStatFilter
    {
        public override string ToString()
        {
            return "小区重选时间间隔统计过滤";
        }
        public override bool CheckEventFilter(DataEvent data)
        {
            if(data.evtId == 40)
            {
                return data.values[4] > 0;
            }
            else
            {
                return true;
            }
        }
    }
}
