﻿using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.Func
{
    /// <summary>
    /// 读取Excel或Csv文件数据组件
    /// 支持模板下载与导入数据到数据库
    /// </summary>
    public partial class ImportDataPanel : UserControl
    {
        public ImportDataPanel()
        {
            InitializeComponent();
            initLocationX = btnImport.Location.X;
        }

        public void Init(ImportDataBase import)
        {
            Import = import;
        }

        bool isLoadData = true;
        int initLocationX = 0;
        public ImportDataBase Import { get; private set; }

        public void SetCondtion(string path)
        {
            txtPath.Text = path;
        }

        public string GetCondtion()
        {
            return txtPath.Text;
        }

        private void btnSearchPath_Click(object sender, EventArgs e)
        {
            OpenFileDialog openDlg = new OpenFileDialog();
            openDlg.Filter = Import.Filter;
            openDlg.Title = Import.Title;
            if (openDlg.ShowDialog() != DialogResult.OK)
            {
                return;
            }
            bool isValid = false;
            if (!string.IsNullOrEmpty(openDlg.FileName) && File.Exists(openDlg.FileName))
            {
                isValid = true;
            }

            if (isLoadData)
            {
                try
                {
                    isValid = Import.LoadImportData(openDlg.FileName);
                }
                catch (Exception ex)
                {
                    MessageBox.Show(ex.Message + ex.StackTrace);
                }
            }

            if (isValid)
            {
                txtPath.Text = openDlg.FileName;
            }
        }

        private void btnDownLoad_Click(object sender, EventArgs e)
        {
            Import.DownloadTemplate();
        }

        private void btnImport_Click(object sender, EventArgs e)
        {
            Import.ImportDataToDB();
        }

        #region 可配置项
        /// <summary>
        /// 设置是否在选择文件路径后直接加载数据
        /// </summary>
        /// <param name="isLoad"></param>
        public void SetLoadData(bool isLoad)
        {
            isLoadData = isLoad;
            if (!isLoad)
            {
                SetImportVisable(false);
            }
        }

        /// <summary>
        /// 设置是否显示导入数据的按钮
        /// </summary>
        /// <param name="isVisable"></param>
        public void SetImportVisable(bool isVisable)
        {
            btnImport.Visible = isVisable;
        }

        /// <summary>
        /// 设置是否显示下载模板的按钮
        /// </summary>
        /// <param name="isVisable"></param>
        public void SetTemplateVisable(bool isVisable)
        {
            btnDownLoad.Visible = isVisable;
            int y = btnImport.Location.Y;
            if (isVisable)
            {
                if (btnImport.Location.X == initLocationX)
                {
                    int x = initLocationX + btnDownLoad.Size.Width;
                    btnImport.Location = new Point(x, y);
                }
            }
            else
            {
                if (btnImport.Location.X > initLocationX)
                {
                    int x = btnImport.Location.X - btnDownLoad.Size.Width;
                    btnImport.Location = new Point(x, y);
                }
            }
        }
        #endregion
    }

    public abstract class ImportDataBase
    {
        protected ImportDataBase(ImportDataFileType fileType)
        {
            FileType = fileType;
        }

        /// <summary>
        /// 文件类型
        /// </summary>
        public ImportDataFileType FileType { get; }
        /// <summary>
        /// 过滤文件类型
        /// </summary>
        public string Filter { get; set; } = "All|*.*";
        /// <summary>
        /// 窗口标题
        /// </summary>
        public string Title { get; set; } = "";

        //加载文件数据
        public abstract bool LoadImportData(string fileName);

        //数据入库
        public virtual void ImportDataToDB()
        {
            WaitBox.Show("正在导入信息...", importDataToDB);
        }

        protected abstract void importDataToDB();

        //下载模板
        public abstract void DownloadTemplate();
    }

    public abstract class ImportExcelData : ImportDataBase
    {
        protected ImportExcelData(string title)
            : base(ImportDataFileType.Excel)
        {
            Filter = "Excel|*.xlsx;*.xls";
            Title = title;
        }

        #region 加载文件数据
        public override bool LoadImportData(string fileName)
        {
            using (DataSet ds = ExcelNPOIManager.ImportFromExcel(fileName))
            {
                if (ds == null || ds.Tables == null)
                {
                    return false;
                }

                dealImportData(ds);
            }
            return true;
        }

        protected abstract void dealImportData(DataSet ds);
        #endregion

        protected override void importDataToDB()
        {
            WaitBox.Close();
        }

        #region 下载模板
        public override void DownloadTemplate()
        {
            List<ExportToExcelModel> lsData = dealTemplateData();
            ExcelNPOIManager.ExportToExcelMore(lsData);
        }

        protected abstract List<ExportToExcelModel> dealTemplateData();
        #endregion
    }

    public abstract class ImportCsvData : ImportDataBase
    {
        /// <summary>
        /// 尚未完成,目前没有读取CSV的需求,先保留接口
        /// </summary>
        /// <param name="title"></param>
        protected ImportCsvData(string title)
            : base(ImportDataFileType.Csv)
        {
            Filter = "Csv|*.csv";
            Title = title;
        }

        #region 加载文件数据
        public override bool LoadImportData(string fileName)
        {
            return false;
        }
        #endregion

        protected override void importDataToDB()
        {
            WaitBox.Close();
        }

        #region 下载模板
        public override void DownloadTemplate()
        {
        }
        #endregion
    }

    public enum ImportDataFileType
    {
        Other,
        Excel,
        Csv
    }
}
