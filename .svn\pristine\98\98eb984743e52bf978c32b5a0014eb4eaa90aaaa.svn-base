﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.MTGis
{
    public class MultiPolygon
    {
        public string Name
        {
            get;
            set;
        }

        public DbRect Bounds
        {
            get;
            private set;
        }

        public MultiPolygon(MapWinGIS.Shape shape)
        {
            atomicParts = new List<AtomicPolygon>();
            if (!(shape.ShapeType == MapWinGIS.ShpfileType.SHP_POLYGON || shape.ShapeType == MapWinGIS.ShpfileType.SHP_POLYGONZ))
            {
                throw new ArgumentException("参数 MapWinGIS.Shape shape.ShapeType 需为 SHP_POLYGON 或 SHP_POLYGONZ");
            }
            for (int x = 0; x < shape.NumParts; x++)
            {
                AtomicPolygon aP = new AtomicPolygon(ShapeHelper.GetPartShapePoints(shape, x));
                append(aP);
            }
        }

        public MultiPolygon(IList<DbPoint> vertexes)
        {
            atomicParts = new List<AtomicPolygon>();
            AtomicPolygon aP = new AtomicPolygon(vertexes);
            append(aP);
        }
        public List<AtomicPolygon> AtomicParts
        {
            get
            {
                return atomicParts;
            }
        }
        private List<AtomicPolygon> atomicParts;

        private void append(AtomicPolygon dPolygon)
        {
            if (this.Bounds == null)
            {
                this.Bounds = dPolygon.Bounds.Clone();
            }
            else
            {
                this.Bounds.MergeRects(dPolygon.Bounds);
            }
            if (atomicParts == null)
            {
                atomicParts = new List<AtomicPolygon>();
            }
            atomicParts.Add(dPolygon);
        }

        public bool Contains(double lng, double lat)
        {
            if (atomicParts == null)
            {
                return false;
            }
            if (!Bounds.IsPointInThisRect(lng, lat))
            {
                return false;
            }
            int inNum = 0;
            foreach (AtomicPolygon polygon in atomicParts)
            {
                if (polygon.IsPointInOrOnPolygon(lng, lat))
                {
                    inNum++;
                }
            }
            return inNum % 2 != 0;
        }

    }
}
