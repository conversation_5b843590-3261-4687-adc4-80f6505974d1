﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Stat;

namespace MasterCom.RAMS.ZTFunc.GridOrderCommon
{
    public partial class TaskIDFilterDlg : BaseDialog
    {
        public TaskIDFilterDlg()
        {
            InitializeComponent();
            chkAll.Checked = true;
            chkAllOrderType.Checked = true;
            chkAll.CheckedChanged += chkAll_CheckedChanged;
            chkAllOrderType.CheckedChanged += chkAllOrderType_CheckedChanged;
            tvCity.Nodes.Clear();
            foreach (IDNamePair item in mainModel.User.GetAvailableCitys())
            {
                TreeNode node = new TreeNode(item.Name);
                node.Checked = true;
                node.Tag = item;
                tvCity.Nodes.Add(node);
            }

            tvOrderType.Nodes.Clear();
            foreach (GridOrderToken token in OrderTokenMng.Instance.TokenSet)
            {
                TreeNode node = new TreeNode(token.Name);
                node.Checked = true;
                node.Tag = token;
                tvOrderType.Nodes.Add(node);
            }
        }

        void chkAllOrderType_CheckedChanged(object sender, EventArgs e)
        {
            foreach (TreeNode node in tvOrderType.Nodes)
            {
                node.Checked = chkAllOrderType.Checked;
            }
        }

        private void chkAll_CheckedChanged(object sender, EventArgs e)
        {
            foreach (TreeNode node in tvCity.Nodes)
            {
                node.Checked = chkAll.Checked;
            }
        }

        List<int> ids = new List<int>();
        public List<int> CityIDSet
        {
            get { return ids; }
            set
            {
                if (value == null)
                {
                    return;
                }
                ids.Clear();
                ids.AddRange(value);
                foreach (TreeNode node in tvCity.Nodes)
                {
                    IDNamePair item = node.Tag as IDNamePair;
                    node.Checked = ids.Contains(item.id);
                }
            }
        }

        public string OrderIDs
        {
            get { return this.txtOrderID.Text.Trim(); }
            set { this.txtOrderID.Text = value; }
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            getOrderMonths();
            ids.Clear();
            foreach (TreeNode node in tvCity.Nodes)
            {
                if (node.Checked)
                {
                    IDNamePair item = node.Tag as IDNamePair;
                    ids.Add(item.id);
                }
            }
            if (ids.Count == 0)
            {
                MessageBox.Show("请至少选择一个地市!");
                return;
            }

            TokenSet = new List<GridOrderToken>();
            foreach (TreeNode node in tvOrderType.Nodes)
            {
                if (node.Checked)
                {
                    GridOrderToken item = node.Tag as GridOrderToken;
                    TokenSet.Add(item);
                }
            }
            if (TokenSet.Count == 0)
            {
                MessageBox.Show("请至少选择一种栅格工单类型!");
                return;
            }

            DialogResult = DialogResult.OK;
        }

        public List<GridOrderToken> TokenSet { get; set; }

        public string orderMonths { get; set; }

#if Shandong || DEBUG
        private string dateType = "yyyy-M";
#else 
        private string dateType = "yyyy-MM";
#endif
        private void getOrderMonths()
        {
            if(!chk_orderMonths.Checked)
            {
                orderMonths = "''''";
                return;
            }

            DateTime start = orderMonthS.Value;
            DateTime end = orderMonthE.Value;
            TimeSpan ts = end.Subtract(start);
            orderMonths = "''''";

            if (ts.Days == 0)
            {
                orderMonths = "''" + start.ToString(dateType) + "''";
                return;
            }
            else if (ts.Days < 0)
            {
                DateTime dt = start;
                start = end;
                end = dt;
            }

            int diffMonths = (end.Year - start.Year) * 12 + (end.Month - start.Month);
            orderMonths = "''" + start.ToString(dateType) + "''";
            StringBuilder sb = new StringBuilder(orderMonths);
            for (int i = 1; i <= diffMonths; i++)
            {
                sb.Append(",''" + start.AddMonths(i).ToString(dateType) + "''");
            }
            orderMonths = sb.ToString();
        }

        private void chk_orderMonths_CheckedChanged(object sender, EventArgs e)
        {
            if (chk_orderMonths.Checked)
                orderMonthS.Enabled = orderMonthE.Enabled = true;
            else
                orderMonthS.Enabled = orderMonthE.Enabled = false;
        }
    }
}
