﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.Util;
using DevExpress.XtraGrid.Columns;
using DevExpress.XtraGrid.Views.Grid;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class LTELeakOutCellSetResultForm : MinCloseForm
    {
        public LTELeakOutCellSetResultForm()
            : base()
        {
            InitializeComponent();
            miExportSimpleExcel.Click += MiExportSimpleExcel_Click;
            gridView1.DoubleClick += GridView_DoubleClick;
            gridView2.DoubleClick += GridView_DoubleClick;
            gridView3.DoubleClick += GSMGridView_DoubleClick;
            gridView4.DoubleClick += GSMGridView_DoubleClick;

        }

        public void FillData(List<LteLeakOutIndoorCell> indoorCells, List<MasterCom.RAMS.ZTFunc.LteGSMLeakOutCell> GSMIndoorCells)
        {
            gridControl1.DataSource = indoorCells;
            gridControl1.RefreshDataSource();
            gridControl2.DataSource = GSMIndoorCells;
            gridControl2.RefreshDataSource();
            showAllLLteTestPoints();
        }

        private void showAllLLteTestPoints()
        {
            List<LteLeakOutIndoorCell> indoorCells = gridControl1.DataSource as List<LteLeakOutIndoorCell>;

            if (indoorCells == null)
                return;

            MainModel.DTDataManager.Clear();
            MainModel.SelectedLTECells.Clear();
            foreach(LteLeakOutCell cell in indoorCells)
            {
                MainModel.SelectedLTECells.Add(cell.LteCell);
                foreach (TestPoint tp in cell.TestPoints)
                {
                    MainModel.DTDataManager.Add(tp);
                }
            }
            MainModel.FireSetDefaultMapSerialTheme("TD_LTE_RSRP");
            MainModel.FireDTDataChanged(this);
            MainModel.FireSelectedCellChanged(this);
        }

        private void showAllLGsmTestPoints()
        {
            List<MasterCom.RAMS.ZTFunc.LteGSMLeakOutCell> indoorCells = 
                gridControl2.DataSource as List<MasterCom.RAMS.ZTFunc.LteGSMLeakOutCell>;

            if (indoorCells == null)
                return;

            MainModel.DTDataManager.Clear();
            MainModel.SelectedLTECells.Clear();
            foreach (MasterCom.RAMS.ZTFunc.LteGSMLeakOutCell cell in indoorCells)
            {
                MainModel.SelectedCells.Add(cell.Cell as Cell);
                foreach (TestPoint tp in cell.testPointList)
                {
                    MainModel.DTDataManager.Add(tp);
                }
            }
            MainModel.FireSetDefaultMapSerialTheme("lte_gsm_DM_RxLevSub");
            MainModel.FireDTDataChanged(this);
            MainModel.FireSelectedCellChanged(this);
        }

        //新添加 2015/1/23 LTE_FDD
        public void FillData(List<LteFddLeakOutIndoorCell> indoorCells)
        {
            gridControl1.DataSource = indoorCells;
            gridControl1.RefreshDataSource();
        }

        public void GridView_DoubleClick(object sender, EventArgs e)
        {
            MainModel.FireSetDefaultMapSerialTheme("TD_LTE_RSRP");
            DevExpress.XtraGrid.Views.Grid.GridView gv = sender as DevExpress.XtraGrid.Views.Grid.GridView;
            LteLeakOutCell leakOutCell = gv.GetRow(gv.GetSelectedRows()[0]) as LteLeakOutCell;

            MainModel.DTDataManager.Clear();
            foreach (TestPoint tp in leakOutCell.TestPoints)
            {
                MainModel.DTDataManager.Add(tp);
            }

            MainModel.SelectedLTECell = leakOutCell.LteCell;
            MainModel.FireDTDataChanged(this);
            MainModel.FireSelectedCellChanged(this);
            MainModel.MainForm.GetMapForm().GoToView(leakOutCell.LteCell.Longitude, leakOutCell.LteCell.Latitude);
        }

        public void GSMGridView_DoubleClick(object sender, EventArgs e)
        {
            MainModel.FireSetDefaultMapSerialTheme("lte_gsm_DM_RxLevSub");
            GridView gv = sender as GridView;
            LteGSMLeakOutCell leakOutCell = null;
            MasterCom.RAMS.Net.LeakOutOutDoorCellInfo OutDoorleakOutCell = null;
            if (gv.IsShowDetailButtons)
                leakOutCell = gv.GetRow(gv.GetSelectedRows()[0]) as LteGSMLeakOutCell;
            else
                OutDoorleakOutCell = gv.GetRow(gv.GetSelectedRows()[0]) as MasterCom.RAMS.Net.LeakOutOutDoorCellInfo;

            if (leakOutCell != null && leakOutCell.testPointList != null)
            {
                MainModel.DTDataManager.Clear();
                foreach (TestPoint tp in leakOutCell.testPointList)
                {
                    MainModel.DTDataManager.Add(tp);
                }
            }
            Cell cell = null;
            if (leakOutCell != null)
            {
                cell = leakOutCell.Cell as Cell;
            }
            else if (OutDoorleakOutCell != null)
            {
                cell = OutDoorleakOutCell.Cell as Cell;
            }
            if (cell != null)
            {
                MainModel.SelectedCell = cell;
                MainModel.FireDTDataChanged(this);
                MainModel.FireSelectedCellChanged(this);
                MainModel.MainForm.GetMapForm().GoToView(cell.Longitude, cell.Latitude);
            }
        }

        public void MiExportSimpleExcel_Click(object sender, EventArgs e)
        {
            exportExcel(this.gridView1, this.gridView2);
        }

        private void miExportGSMSimpleExcel_Click(object sender, EventArgs e)
        {
            exportExcel(this.gridView4, this.gridView3);
        }

        private void MI_ShowAllTestPoints_Click(object sender, EventArgs e)
        {
            showAllLLteTestPoints();
        }

        private void MI_ShowAllGsmTestPoints_Click(object sender, EventArgs e)
        {
            showAllLGsmTestPoints();
        }

        private void exportExcel(GridView MainGV, GridView ChildGV)
        {
            List<NPOIRow> rows = new List<NPOIRow>();
            NPOIRow row = new NPOIRow();
            foreach (GridColumn col in MainGV.Columns)
            {
                row.AddCellValue(col.Caption);
            }
            foreach (GridColumn col in ChildGV.Columns)
            {
                row.AddCellValue(col.Caption);
            }
            rows.Add(row);
            for (int i = 0; i < MainGV.RowCount; i++)
            {
                row = new NPOIRow();
                rows.Add(row);
                foreach (GridColumn col in MainGV.Columns)
                {
                    row.AddCellValue(MainGV.GetRowCellDisplayText(i, col));
                }
                MainGV.ExpandMasterRow(i);
                DevExpress.XtraGrid.Views.Grid.GridView view = MainGV.GetDetailView(i, 0) as DevExpress.XtraGrid.Views.Grid.GridView;
                if (view != null)
                {
                    for (int j = 0; j < view.RowCount; j++)
                    {
                        NPOIRow subRow = new NPOIRow();
                        row.AddSubRow(subRow);
                        foreach (GridColumn subCol in view.Columns)
                        {
                            subRow.AddCellValue(view.GetRowCellDisplayText(j, subCol));
                        }
                    }
                }
            }
            ExcelNPOIManager.ExportToExcel(rows);
        }

    }
}
