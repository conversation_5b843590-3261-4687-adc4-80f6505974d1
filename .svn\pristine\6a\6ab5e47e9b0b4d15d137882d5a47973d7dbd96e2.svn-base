﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.MTGis;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class FileDistributeConfigForm : BaseDialog
    {
        public FileDistributeConfigForm()
        {
            InitializeComponent();
            btnCancel.Click += BtnCancel_Click;
            btnOK.Click += BtnOK_Click;
            btnSelect.Click += BtnSelect_Click;
            FillByConfig();
        }

        private void FillByConfig()
        {
            DistributeFileConfig config = new DistributeFileConfig();
            config.Load();

            txtReplaceTarget.Text = config.ReplaceTarget;
            txtFtpPathPrefix.Text = config.FtpPathPrefix;
            txtSavePathPrefix.Text = config.SavePathPrefix;

            txtShp.Text = config.ShpFileName;
            cbxField.Items.Clear();
            if (!string.IsNullOrEmpty(config.ShpFileName) && System.IO.File.Exists(config.ShpFileName) && !string.IsNullOrEmpty(config.ShpFieldName))
            {
                List<string> fields = ShapeHelper.GetFieldNamesFromFile(config.ShpFileName);
                cbxField.Items.AddRange(fields.ToArray());
                int fieldIndex = fields.IndexOf(config.ShpFieldName);
                if (fieldIndex != -1)
                {
                    cbxField.SelectedIndex = fieldIndex;
                }
            }
        }

        private void BtnOK_Click(object sender, EventArgs e)
        {
            DistributeFileConfig config = new DistributeFileConfig();
            config.ReplaceTarget = txtReplaceTarget.Text;
            config.FtpPathPrefix = txtFtpPathPrefix.Text;
            config.SavePathPrefix = txtSavePathPrefix.Text;
            config.ShpFileName = txtShp.Text;
            config.ShpFieldName = cbxField.SelectedItem == null ? null : cbxField.SelectedItem.ToString();

            if (!config.IsValid)
            {
                MessageBox.Show("设置项不能留空", "设置错误", MessageBoxButtons.OK, MessageBoxIcon.Information);
                DialogResult = DialogResult.None;
                return;
            }

            config.Save();
            DialogResult = DialogResult.OK;
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
        }

        private void BtnSelect_Click(object sender, EventArgs e)
        {
            OpenFileDialog dlg = new OpenFileDialog();
            dlg.Filter = FilterHelper.Shp;
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return;
            }

            txtShp.Text = dlg.FileName;
            List<string> fields = ShapeHelper.GetFieldNamesFromFile(dlg.FileName);
            cbxField.Items.AddRange(fields.ToArray());
            if (cbxField.Items.Count > 0)
            {
                cbxField.SelectedIndex = 0;
            }
        }
    }
}
