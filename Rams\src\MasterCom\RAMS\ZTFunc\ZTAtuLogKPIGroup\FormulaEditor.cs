﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc.ZTAtuLogKPIGroup
{
    public partial class FormulaEditor : BaseForm
    {
        public FormulaEditor()
        {
            InitializeComponent();
            if (this.DesignMode)
            {
                return;
            }
            cmbSimpleMoMt.SelectedIndex = 0;
            cmbMultiMoMt1.SelectedIndex = 0;
            cmbMultiMoMt2.SelectedIndex = 1;
            rbtnChinaMobile.Checked = true;

            editor.SubmitFormula += editor_SubmitFormula;
            tabCtrl.SelectedPageChanged += tabCtrl_SelectedPageChanged;
            rTxtSimpleExp.GotFocus += rTxtSimpleExp_GotFocus;
            rTxtExpMulti1.GotFocus += rTxtExpMulti1_GotFocus;
            rTxtExpMulti2.GotFocus += rTxtExpMulti2_GotFocus;
            pageMulti.PageEnabled = false;
        }

        void rTxtExpMulti2_GotFocus(object sender, EventArgs e)
        {
            lastFocusTxt = sender as RichTextBox;
        }

        void rTxtExpMulti1_GotFocus(object sender, EventArgs e)
        {
            lastFocusTxt = sender as RichTextBox;
        }

        void rTxtSimpleExp_GotFocus(object sender, EventArgs e)
        {
            lastFocusTxt = sender as RichTextBox;
        }

        RichTextBox lastFocusTxt = null;
        void tabCtrl_SelectedPageChanged(object sender, DevExpress.XtraTab.TabPageChangedEventArgs e)
        {
            if (tabCtrl.SelectedTabPage == pageSimple)
            {
                rTxtSimpleExp.Focus();
            }
            else
            {
                rTxtExpMulti1.Focus();
            }
        }

        void editor_SubmitFormula(object sender, EventArgs e)
        {
            if (lastFocusTxt == null)
            {
                return;
            }
            MasterCom.RAMS.Util.KPIFormulaEditor.SubmitFormulaEventArgs fe = e as MasterCom.RAMS.Util.KPIFormulaEditor.SubmitFormulaEventArgs;
            lastFocusTxt.Text = fe.Formula;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            indOption.CarrierID = carrierID;
            if (rBtnSimple.Checked)
            {
                if (rTxtSimpleExp.Text.Trim().Length == 0)
                {
                    MessageBox.Show("公式不能为空！");
                    return;
                }
                indOption.MoMt1 = cmbSimpleMoMt.SelectedIndex;
                indOption.IsMultiFormula = false;
                string formula = rTxtSimpleExp.Text.Substring(rTxtSimpleExp.Text.IndexOf('{')
                               , rTxtSimpleExp.Text.IndexOf('}') + 1);
                indOption.KPIFormula = formula;
            }
            else
            {

                if (rTxtExpMulti1.Text.Trim().Length == 0)
                {
                    MessageBox.Show("指标一公式不能为空！");
                    return;
                }

                if (rTxtExpMulti2.Text.Trim().Length == 0)
                {
                    MessageBox.Show("指标二公式不能为空！");
                    return;
                }

                if (cmbMultiMoMt1.SelectedIndex == cmbMultiMoMt2.SelectedIndex)
                {
                    MessageBox.Show("指标一和指标二主被叫不能相同！");
                    return;
                }

                indOption.IsMultiFormula = true;
                indOption.MoMt1 = cmbMultiMoMt1.SelectedIndex + 1;
                indOption.MoMt2 = cmbMultiMoMt2.SelectedIndex + 1;

                string formula = rTxtExpMulti1.Text.Substring(rTxtExpMulti1.Text.IndexOf('{')
                               , rTxtExpMulti1.Text.IndexOf('}') + 1);
                indOption.Formula1 = formula;
                formula = rTxtExpMulti2.Text.Substring(rTxtExpMulti2.Text.IndexOf('{')
                               , rTxtExpMulti2.Text.IndexOf('}') + 1);
                indOption.Formula2 = formula;
                indOption.KPIFormula = indOption.Formula1 + " - " + indOption.Formula2;

            }
            DialogResult = DialogResult.OK;
        }

        private void rBtnSimple_CheckedChanged(object sender, EventArgs e)
        {
            tabCtrl.SelectedTabPageIndex = rBtnSimple.Checked ? 0 : 1;
            pageSimple.PageEnabled = rBtnSimple.Checked;
            pageMulti.PageEnabled = !rBtnSimple.Checked;
        }

        GroupIndicatorOption indOption = null;
        public GroupIndicatorOption IndicatorOption
        {
            get
            {
                return indOption;
            }
            set
            {
                indOption = value;
                carrierID = value.CarrierID;
                rBtnMulti.Checked = value.IsMultiFormula;
                rBtnSimple.Checked = !rBtnMulti.Checked;
                if (value.IsMultiFormula)
                {
                    if (value.MoMt1 > 0)
                    {
                        cmbMultiMoMt1.SelectedIndex = value.MoMt1 - 1;
                    }
                    if (value.MoMt2 > 0)
                    {
                        cmbMultiMoMt2.SelectedIndex = value.MoMt2 - 1;
                    }
                    rTxtExpMulti1.Text = value.Formula1;
                    rTxtExpMulti2.Text = value.Formula2;
                    lastFocusTxt = rTxtExpMulti1;
                }
                else
                {
                    cmbSimpleMoMt.SelectedIndex = value.MoMt1;
                    rTxtSimpleExp.Text = value.KPIFormula;
                    lastFocusTxt = rTxtSimpleExp;
                }
            }
        }

        private byte carrierID
        {
            get
            {
                if (rbtnChinaMobile.Checked)
                {
                    return (byte)Model.CarrierType.ChinaMobile;
                }
                else if (rbtnChinaUnicom.Checked)
                {
                    return (byte)Model.CarrierType.ChinaUnicom;
                }
                else
                {
                    return (byte)Model.CarrierType.ChinaTelecom;
                }
            }
            set
            {
                CarrierType type = (CarrierType)value;
                switch (type)
                {
                    case CarrierType.ChinaMobile:
                        rbtnChinaMobile.Checked = true;
                        break;
                    case CarrierType.ChinaUnicom:
                        rbtnChinaUnicom.Checked = true;
                        break;
                    case CarrierType.ChinaTelecom:
                        rbtnChinaTelecom.Checked = true;
                        break;
                    default:
                        break;
                }
            }
        }

    }
}
