﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTGSMDataDLRateSettingForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.btnOK = new System.Windows.Forms.Button();
            this.btnCancel = new System.Windows.Forms.Button();
            this.numSampleCountLimit = new System.Windows.Forms.NumericUpDown();
            this.label4 = new System.Windows.Forms.Label();
            this.numRadius = new System.Windows.Forms.NumericUpDown();
            this.label2 = new System.Windows.Forms.Label();
            this.label1 = new System.Windows.Forms.Label();
            this.radioButtonAppRate = new System.Windows.Forms.RadioButton();
            this.radioButtonRLCRate = new System.Windows.Forms.RadioButton();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.rangeSettingDLRate = new MasterCom.RAMS.Chris.Util.RangeSetting();
            ((System.ComponentModel.ISupportInitialize)(this.numSampleCountLimit)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRadius)).BeginInit();
            this.groupBox1.SuspendLayout();
            this.SuspendLayout();
            // 
            // btnOK
            // 
            this.btnOK.Location = new System.Drawing.Point(182, 170);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 33;
            this.btnOK.Text = "确定";
            this.btnOK.UseVisualStyleBackColor = true;
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // btnCancel
            // 
            this.btnCancel.Location = new System.Drawing.Point(279, 170);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 32;
            this.btnCancel.Text = "取消";
            this.btnCancel.UseVisualStyleBackColor = true;
            this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
            // 
            // numSampleCountLimit
            // 
            this.numSampleCountLimit.Location = new System.Drawing.Point(286, 117);
            this.numSampleCountLimit.Maximum = new decimal(new int[] {
            5000,
            0,
            0,
            0});
            this.numSampleCountLimit.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numSampleCountLimit.Name = "numSampleCountLimit";
            this.numSampleCountLimit.Size = new System.Drawing.Size(56, 21);
            this.numSampleCountLimit.TabIndex = 29;
            this.numSampleCountLimit.Value = new decimal(new int[] {
            10,
            0,
            0,
            0});
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(180, 122);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(101, 12);
            this.label4.TabIndex = 31;
            this.label4.Text = "至少包含采样点数";
            // 
            // numRadius
            // 
            this.numRadius.Increment = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.numRadius.Location = new System.Drawing.Point(104, 117);
            this.numRadius.Maximum = new decimal(new int[] {
            500,
            0,
            0,
            0});
            this.numRadius.Minimum = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.numRadius.Name = "numRadius";
            this.numRadius.Size = new System.Drawing.Size(56, 21);
            this.numRadius.TabIndex = 28;
            this.numRadius.Value = new decimal(new int[] {
            10,
            0,
            0,
            0});
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(45, 122);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(53, 12);
            this.label2.TabIndex = 30;
            this.label2.Text = "汇聚半径";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(21, 80);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(77, 12);
            this.label1.TabIndex = 26;
            this.label1.Text = "下载速率门限";
            // 
            // radioButtonAppRate
            // 
            this.radioButtonAppRate.AutoSize = true;
            this.radioButtonAppRate.Checked = true;
            this.radioButtonAppRate.Location = new System.Drawing.Point(34, 20);
            this.radioButtonAppRate.Name = "radioButtonAppRate";
            this.radioButtonAppRate.Size = new System.Drawing.Size(59, 16);
            this.radioButtonAppRate.TabIndex = 34;
            this.radioButtonAppRate.TabStop = true;
            this.radioButtonAppRate.Text = "APP DL";
            this.radioButtonAppRate.UseVisualStyleBackColor = true;
            // 
            // radioButtonRLCRate
            // 
            this.radioButtonRLCRate.AutoSize = true;
            this.radioButtonRLCRate.Location = new System.Drawing.Point(149, 20);
            this.radioButtonRLCRate.Name = "radioButtonRLCRate";
            this.radioButtonRLCRate.Size = new System.Drawing.Size(59, 16);
            this.radioButtonRLCRate.TabIndex = 34;
            this.radioButtonRLCRate.Text = "RLC DL";
            this.radioButtonRLCRate.UseVisualStyleBackColor = true;
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.radioButtonAppRate);
            this.groupBox1.Controls.Add(this.radioButtonRLCRate);
            this.groupBox1.Location = new System.Drawing.Point(12, 12);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(347, 44);
            this.groupBox1.TabIndex = 35;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "下载速率";
            // 
            // rangeSettingDLRate
            // 
            this.rangeSettingDLRate.Location = new System.Drawing.Point(100, 71);
            this.rangeSettingDLRate.Name = "rangeSettingDLRate";
            this.rangeSettingDLRate.Size = new System.Drawing.Size(254, 28);
            this.rangeSettingDLRate.TabIndex = 27;
            // 
            // ZTGSMDataDLRateSettingForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(371, 216);
            this.Controls.Add(this.groupBox1);
            this.Controls.Add(this.btnOK);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.numSampleCountLimit);
            this.Controls.Add(this.label4);
            this.Controls.Add(this.numRadius);
            this.Controls.Add(this.label2);
            this.Controls.Add(this.rangeSettingDLRate);
            this.Controls.Add(this.label1);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedToolWindow;
            this.Name = "ZTGSMDataDLRateSettingForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "汇聚设置";
            ((System.ComponentModel.ISupportInitialize)(this.numSampleCountLimit)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRadius)).EndInit();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.Button btnOK;
        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.NumericUpDown numSampleCountLimit;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.NumericUpDown numRadius;
        private System.Windows.Forms.Label label2;
        private MasterCom.RAMS.Chris.Util.RangeSetting rangeSettingDLRate;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.RadioButton radioButtonAppRate;
        private System.Windows.Forms.RadioButton radioButtonRLCRate;
        private System.Windows.Forms.GroupBox groupBox1;
    }
}