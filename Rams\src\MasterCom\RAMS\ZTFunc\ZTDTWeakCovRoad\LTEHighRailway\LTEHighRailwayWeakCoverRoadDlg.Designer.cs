﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class LTEHighRailwayWeakCoverRoadDlg
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.btnOK = new DevExpress.XtraEditors.SimpleButton();
            this.btnCancel = new DevExpress.XtraEditors.SimpleButton();
            this.numWeakTestPoints = new System.Windows.Forms.NumericUpDown();
            this.label2 = new System.Windows.Forms.Label();
            this.label1 = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.numRSRP = new System.Windows.Forms.NumericUpDown();
            this.numTestPoints = new System.Windows.Forms.NumericUpDown();
            this.label4 = new System.Windows.Forms.Label();
            ((System.ComponentModel.ISupportInitialize)(this.numWeakTestPoints)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRSRP)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numTestPoints)).BeginInit();
            this.SuspendLayout();
            // 
            // btnOK
            // 
            this.btnOK.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnOK.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnOK.Appearance.Options.UseFont = true;
            this.btnOK.Location = new System.Drawing.Point(219, 150);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 5;
            this.btnOK.Text = "确定";
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // btnCancel
            // 
            this.btnCancel.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnCancel.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnCancel.Appearance.Options.UseFont = true;
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnCancel.Location = new System.Drawing.Point(313, 150);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 6;
            this.btnCancel.Text = "取消";
            // 
            // numWeakTestPoints
            // 
            this.numWeakTestPoints.Location = new System.Drawing.Point(146, 67);
            this.numWeakTestPoints.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numWeakTestPoints.Name = "numWeakTestPoints";
            this.numWeakTestPoints.Size = new System.Drawing.Size(80, 21);
            this.numWeakTestPoints.TabIndex = 90;
            this.numWeakTestPoints.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numWeakTestPoints.Value = new decimal(new int[] {
            3,
            0,
            0,
            0});
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(39, 73);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(101, 12);
            this.label2.TabIndex = 89;
            this.label2.Text = "弱覆盖采样点数≥";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(39, 33);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(101, 12);
            this.label1.TabIndex = 88;
            this.label1.Text = "采样点主服RSRP≤";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(232, 31);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(23, 12);
            this.label3.TabIndex = 87;
            this.label3.Text = "dBm";
            // 
            // numRSRP
            // 
            this.numRSRP.Location = new System.Drawing.Point(146, 27);
            this.numRSRP.Maximum = new decimal(new int[] {
            25,
            0,
            0,
            0});
            this.numRSRP.Minimum = new decimal(new int[] {
            140,
            0,
            0,
            -2147483648});
            this.numRSRP.Name = "numRSRP";
            this.numRSRP.Size = new System.Drawing.Size(80, 21);
            this.numRSRP.TabIndex = 86;
            this.numRSRP.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numRSRP.Value = new decimal(new int[] {
            110,
            0,
            0,
            -2147483648});
            // 
            // numTestPoints
            // 
            this.numTestPoints.Location = new System.Drawing.Point(307, 109);
            this.numTestPoints.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numTestPoints.Name = "numTestPoints";
            this.numTestPoints.Size = new System.Drawing.Size(80, 21);
            this.numTestPoints.TabIndex = 92;
            this.numTestPoints.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numTestPoints.Value = new decimal(new int[] {
            3,
            0,
            0,
            0});
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(39, 113);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(257, 12);
            this.label4.TabIndex = 91;
            this.label4.Text = "弱覆盖采样点间存在的最大正常覆盖采样点数≥";
            // 
            // LTEHighRailwayWeakCoverRoadDlg
            // 
            this.AcceptButton = this.btnOK;
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(410, 199);
            this.Controls.Add(this.numTestPoints);
            this.Controls.Add(this.label4);
            this.Controls.Add(this.numWeakTestPoints);
            this.Controls.Add(this.label2);
            this.Controls.Add(this.label1);
            this.Controls.Add(this.label3);
            this.Controls.Add(this.numRSRP);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.Name = "LTEHighRailwayWeakCoverRoadDlg";
            this.Text = "弱覆盖路段(高铁)条件设置";
            ((System.ComponentModel.ISupportInitialize)(this.numWeakTestPoints)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRSRP)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numTestPoints)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraEditors.SimpleButton btnOK;
        private DevExpress.XtraEditors.SimpleButton btnCancel;
        private System.Windows.Forms.NumericUpDown numWeakTestPoints;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.NumericUpDown numRSRP;
        private System.Windows.Forms.NumericUpDown numTestPoints;
        private System.Windows.Forms.Label label4;
    }
}