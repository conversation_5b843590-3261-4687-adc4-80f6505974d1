﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc.AreaArchiveManage
{
    public partial class ReportConditionDlg : BaseDialog
    {
        public ReportConditionDlg()
            : base()
        {
            InitializeComponent();
        }

        TemplateMngr templateMngr;
        public ReportConditionDlg(TemplateMngr templateMngr)
            : this()
        {
            this.templateMngr = templateMngr;
            fillTemplate(null);
        }

        private void fillTemplate(AreaReportTemplate selTemplate)
        {
            cbxReportTemplate.Items.Clear();
            foreach (AreaReportTemplate rpt in templateMngr.ReportTemplates)
            {
                cbxReportTemplate.Items.Add(rpt);
            }
            if (selTemplate != null)
            {
                cbxReportTemplate.SelectedItem = selTemplate;
            }
            else if (cbxReportTemplate.Items.Count > 0)
            {
                cbxReportTemplate.SelectedIndex = 0;
            }
        }

        private void btnTemplateOption_Click(object sender, EventArgs e)
        {
            AreaRptTemplateOptionDlg dlg = new AreaRptTemplateOptionDlg();
            dlg.SetTemplate(cbxReportTemplate.SelectedItem as AreaReportTemplate);
            dlg.ShowDialog();
            fillTemplate(dlg.CurTemplate);
        }

        public AreaReportTemplate SelTemplate
        {
            get
            {
                return cbxReportTemplate.SelectedItem as AreaReportTemplate;
            }
        }

        public bool CheckStatLatestOnly
        {
            get { return chkStatLatestOnly.Checked; }
            set { chkStatLatestOnly.Checked = value; }
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            if (cbxReportTemplate.SelectedItem == null)
            {
                MessageBox.Show("请选择报表！");
                return;
            }
            DialogResult = DialogResult.OK;
        }
    }

}
