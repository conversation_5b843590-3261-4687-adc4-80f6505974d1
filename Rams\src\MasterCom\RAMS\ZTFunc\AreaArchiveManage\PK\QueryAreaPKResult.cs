﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.KPI_Statistics;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using MasterCom.RAMS.ZTFunc.AreaArchiveManage;

namespace MasterCom.RAMS.ZTFunc
{
    class QueryAreaPKResult : AreaKpiQueryBase
    {
        private PKCondition pkCond = null;

        private readonly CPkValue pkValue;

        public QueryAreaPKResult()
            : base(MainModel.GetInstance())
        {
            pkValue = new CPkValue();
        }

        public override string Name
        {
            get { return "竞争对比"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 31000, 31004, this.Name);
        }

        protected override bool setConditionDlg()
        {
            PKBaseSettingDlg dlg = new PKBaseSettingDlg();
            if (pkCond == null)
            {
                pkCond = new PKCondition();
                pkCond.Init();
            }
            dlg.SetCondition(pkCond);

            if (dlg.ShowDialog() == DialogResult.OK)
            {
                pkCond = dlg.GetCondition();
                return true;
            }
            return false;
        }

        protected override void searchData()
        {
            Dictionary<int, Dictionary<int, AreaBase>> typeIdDic = getTypeIds();
            pkValue.CMHubDic = doSearch(pkCond.ConditionCM, pkCond.SelTemplate.CMHub, typeIdDic);
            pkValue.CUHubDic = doSearch(pkCond.ConditionCU, pkCond.SelTemplate.CUHub, typeIdDic);
            pkValue.CTHubDic = doSearch(pkCond.ConditionCT, pkCond.SelTemplate.CTHub, typeIdDic);
        }

        private Dictionary<AreaBase, CHubValue> doSearch(QueryCondition baseCond, PKHub hub, 
            Dictionary<int, Dictionary<int, AreaBase>> typeIdDic)
        {
            QueryCondition searchCond = new QueryCondition();
            searchCond.DistrictIDs = archiveCondition.BaseCondition.DistrictIDs;
            searchCond.Periods = baseCond.Periods;
            searchCond.Projects = baseCond.Projects;
            searchCond.FileName = baseCond.FileName;
            searchCond.FileNameOrNum = baseCond.FileNameOrNum;
            searchCond.NameFilterType = baseCond.NameFilterType;
            searchCond.CarrierTypes.Clear();
            searchCond.CarrierTypes.AddRange(baseCond.CarrierTypes);
            searchCond.ServiceTypes.Clear();
            searchCond.ServiceTypes.AddRange(baseCond.ServiceTypes);

            //AreaKpiSearchPartByPart search = new AreaKpiSearchPartByPart();
            //search.SetCondition(archiveCondition);
            //search.SetFormula(new List<string>() { formula.Replace("{", "").Replace("}", "") });
            //search.SetQueryAllParams(isQueryAllParams);
            //search.SetStatLatestOnly(pkCond.IsStatLatestOnly);
            //search.SetTypes(getTypeIds());
            //search.SetDealer(anaDealer, areaCondition);
            //search.Query();

            //return search.AreaKpiMap;

            AreaPKSearchByCountry query = new AreaPKSearchByCountry();
            query.IsStatLatestOnly = this.isStatLatestOnly;
            query.SetQueryCondition(searchCond);
            query.SetFormula(new List<string>() { hub.PkBase.FormulaExp.Replace("{", "").Replace("}", "") });
            query.SetTypes(typeIdDic);
            query.SetHub(hub);
            query.Query();

            return query.PkValueDic;
        }

        protected new Dictionary<int, Dictionary<int, AreaBase>> getTypeIds()
        {
            Dictionary<int, Dictionary<int, AreaBase>> typeIds = new Dictionary<int, Dictionary<int, AreaBase>>();
            foreach (List<AreaBase> vils in rootLeafDic.Values)
            {
                foreach (AreaBase area in vils)
                {
                    AreaBase pArea = area;
                    while (pArea != null)
                    {
                        Dictionary<int, AreaBase> ids;
                        if (!typeIds.TryGetValue(pArea.AreaTypeID, out ids))
                        {
                            ids = new Dictionary<int, AreaBase>();
                            typeIds[pArea.AreaTypeID] = ids;
                        }
                        ids[pArea.AreaID] = pArea;

                        pArea = pArea.ParentArea;
                    }
                }
            }
            return typeIds;
        }

        protected override void fireShowForm()
        {
            AreaPKResultForm form = MainModel.GetObjectFromBlackboard(typeof(AreaPKResultForm)) as AreaPKResultForm;
            if (form == null || form.IsDisposed)
            {
                form = new AreaPKResultForm();
                form.Owner = MainModel.MainForm;
            }
            form.FillData(pkCond, pkValue);
            form.Visible = true;
            form.BringToFront();
        }
    }

    public class CPkValue
    {
        public Dictionary<AreaBase, CHubValue> CMHubDic { get; set; }

        public Dictionary<AreaBase, CHubValue> CUHubDic { get; set; }

        public Dictionary<AreaBase, CHubValue> CTHubDic { get; set; }

        public CPkValue()
        {
            CMHubDic = new Dictionary<AreaBase, CHubValue>();
            CUHubDic = new Dictionary<AreaBase, CHubValue>();
            CTHubDic = new Dictionary<AreaBase, CHubValue>();
        }

        public double GetCMValue(AreaBase area)
        {
            CHubValue hub;
            if (!CMHubDic.TryGetValue(area, out hub))
            {
                return double.NaN;
            }
            return hub.DValue;
        }

        public double GetCUValue(AreaBase area)
        {
            CHubValue hub;
            if (!CUHubDic.TryGetValue(area, out hub))
            {
                return double.NaN;
            }
            return hub.DValue;
        }

        public double GetCTValue(AreaBase area)
        {
            CHubValue hub;
            if (!CTHubDic.TryGetValue(area, out hub))
            {
                return double.NaN;
            }
            return hub.DValue;
        }
    }

    public class PkAreaSummary
    {
        public Dictionary<AreaBase, CAreaSummary> SummaryMapCM { get; set; }

        public Dictionary<AreaBase, CAreaSummary> SummaryMapCU { get; set; }

        public Dictionary<AreaBase, CAreaSummary> SummaryMapCT { get; set; }

        public PkAreaSummary()
        {
            SummaryMapCM = new Dictionary<AreaBase, CAreaSummary>();
            SummaryMapCU = new Dictionary<AreaBase, CAreaSummary>();
            SummaryMapCT = new Dictionary<AreaBase, CAreaSummary>();
        }

        public AreaKPIDataGroup<AreaBase> GetKpiGroup(AreaBase area, ECarrier car)
        {
            switch (car)
            {
                case ECarrier.移动:
                    return SummaryMapCM[area].AreaKpiGroup;
                case ECarrier.联通:
                    return SummaryMapCM[area].AreaKpiGroup;
                case ECarrier.电信:
                    return SummaryMapCM[area].AreaKpiGroup;
                default:
                    break;
            }
            return null;
        }
    }
}
