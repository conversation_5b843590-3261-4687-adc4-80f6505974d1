﻿namespace MasterCom.RAMS.ZTFunc.AreaArchiveManage.Problem
{
    partial class ProblemAreaListForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.lv = new BrightIdeasSoftware.ObjectListView();
            this.colSn = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colProbNum = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colWeakCvrAndWorst = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colWeakCvrAndPoorQual = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colWeakCvrOnly = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colAbnormalEvt = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colAbnormalEvtDesc = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colAbnormalEvtNum = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colCvrCM = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colCvrCU = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colCvrCT = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colQual = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.ctxMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExport2Xls = new System.Windows.Forms.ToolStripMenuItem();
            ((System.ComponentModel.ISupportInitialize)(this.lv)).BeginInit();
            this.ctxMenu.SuspendLayout();
            this.SuspendLayout();
            // 
            // lv
            // 
            this.lv.AllColumns.Add(this.colSn);
            this.lv.AllColumns.Add(this.colName);
            this.lv.AllColumns.Add(this.colProbNum);
            this.lv.AllColumns.Add(this.colWeakCvrAndWorst);
            this.lv.AllColumns.Add(this.colWeakCvrAndPoorQual);
            this.lv.AllColumns.Add(this.colWeakCvrOnly);
            this.lv.AllColumns.Add(this.colAbnormalEvt);
            this.lv.AllColumns.Add(this.colAbnormalEvtDesc);
            this.lv.AllColumns.Add(this.colAbnormalEvtNum);
            this.lv.AllColumns.Add(this.colCvrCM);
            this.lv.AllColumns.Add(this.colCvrCU);
            this.lv.AllColumns.Add(this.colCvrCT);
            this.lv.AllColumns.Add(this.colQual);
            this.lv.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.colSn,
            this.colName,
            this.colWeakCvrAndWorst,
            this.colWeakCvrAndPoorQual,
            this.colWeakCvrOnly,
            this.colAbnormalEvt,
            this.colAbnormalEvtDesc,
            this.colAbnormalEvtNum,
            this.colCvrCM,
            this.colCvrCU,
            this.colCvrCT,
            this.colQual});
            this.lv.ContextMenuStrip = this.ctxMenu;
            this.lv.Cursor = System.Windows.Forms.Cursors.Default;
            this.lv.Dock = System.Windows.Forms.DockStyle.Fill;
            this.lv.FullRowSelect = true;
            this.lv.GridLines = true;
            this.lv.HeaderStyle = System.Windows.Forms.ColumnHeaderStyle.Nonclickable;
            this.lv.HeaderWordWrap = true;
            this.lv.IsNeedShowOverlay = false;
            this.lv.Location = new System.Drawing.Point(0, 0);
            this.lv.Name = "lv";
            this.lv.OwnerDraw = true;
            this.lv.ShowGroups = false;
            this.lv.Size = new System.Drawing.Size(983, 462);
            this.lv.TabIndex = 5;
            this.lv.UseCompatibleStateImageBehavior = false;
            this.lv.View = System.Windows.Forms.View.Details;
            // 
            // colSn
            // 
            this.colSn.HeaderFont = null;
            this.colSn.Text = "序号";
            this.colSn.Width = 80;
            // 
            // colName
            // 
            this.colName.HeaderFont = null;
            this.colName.Text = "名称";
            this.colName.Width = 100;
            // 
            // colProbNum
            // 
            this.colProbNum.DisplayIndex = 2;
            this.colProbNum.HeaderFont = null;
            this.colProbNum.IsVisible = false;
            this.colProbNum.Text = "问题个数";
            // 
            // colWeakCvrAndWorst
            // 
            this.colWeakCvrAndWorst.HeaderFont = null;
            this.colWeakCvrAndWorst.Text = "弱覆盖且差于竞争对手";
            // 
            // colWeakCvrAndPoorQual
            // 
            this.colWeakCvrAndPoorQual.HeaderFont = null;
            this.colWeakCvrAndPoorQual.Text = "弱覆盖且质差";
            // 
            // colWeakCvrOnly
            // 
            this.colWeakCvrOnly.HeaderFont = null;
            this.colWeakCvrOnly.Text = "仅弱覆盖";
            // 
            // colAbnormalEvt
            // 
            this.colAbnormalEvt.HeaderFont = null;
            this.colAbnormalEvt.Text = "异常事件";
            // 
            // colAbnormalEvtDesc
            // 
            this.colAbnormalEvtDesc.HeaderFont = null;
            this.colAbnormalEvtDesc.Text = "异常事件详情";
            this.colAbnormalEvtDesc.Width = 120;
            // 
            // colAbnormalEvtNum
            // 
            this.colAbnormalEvtNum.HeaderFont = null;
            this.colAbnormalEvtNum.Text = "异常事件总数";
            this.colAbnormalEvtNum.Width = 74;
            // 
            // colCvrCM
            // 
            this.colCvrCM.HeaderFont = null;
            this.colCvrCM.Text = "移动覆盖";
            // 
            // colCvrCU
            // 
            this.colCvrCU.HeaderFont = null;
            this.colCvrCU.Text = "联通覆盖";
            // 
            // colCvrCT
            // 
            this.colCvrCT.HeaderFont = null;
            this.colCvrCT.Text = "电信覆盖";
            // 
            // colQual
            // 
            this.colQual.HeaderFont = null;
            this.colQual.Text = "质量";
            // 
            // ctxMenu
            // 
            this.ctxMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExport2Xls});
            this.ctxMenu.Name = "ctxMenu";
            this.ctxMenu.Size = new System.Drawing.Size(139, 26);
            // 
            // miExport2Xls
            // 
            this.miExport2Xls.Name = "miExport2Xls";
            this.miExport2Xls.Size = new System.Drawing.Size(138, 22);
            this.miExport2Xls.Text = "导出Excel...";
            this.miExport2Xls.Click += new System.EventHandler(this.miExport2Xls_Click);
            // 
            // ProblemAreaListForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(983, 462);
            this.Controls.Add(this.lv);
            this.Name = "ProblemAreaListForm";
            this.Text = "问题村庄列表";
            ((System.ComponentModel.ISupportInitialize)(this.lv)).EndInit();
            this.ctxMenu.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private BrightIdeasSoftware.ObjectListView lv;
        private BrightIdeasSoftware.OLVColumn colSn;
        private BrightIdeasSoftware.OLVColumn colName;
        private BrightIdeasSoftware.OLVColumn colProbNum;
        private BrightIdeasSoftware.OLVColumn colWeakCvrAndWorst;
        private BrightIdeasSoftware.OLVColumn colWeakCvrAndPoorQual;
        private BrightIdeasSoftware.OLVColumn colWeakCvrOnly;
        private BrightIdeasSoftware.OLVColumn colAbnormalEvt;
        private BrightIdeasSoftware.OLVColumn colAbnormalEvtDesc;
        private BrightIdeasSoftware.OLVColumn colAbnormalEvtNum;
        private BrightIdeasSoftware.OLVColumn colCvrCM;
        private BrightIdeasSoftware.OLVColumn colCvrCU;
        private BrightIdeasSoftware.OLVColumn colCvrCT;
        private BrightIdeasSoftware.OLVColumn colQual;
        private System.Windows.Forms.ContextMenuStrip ctxMenu;
        private System.Windows.Forms.ToolStripMenuItem miExport2Xls;
    }
}