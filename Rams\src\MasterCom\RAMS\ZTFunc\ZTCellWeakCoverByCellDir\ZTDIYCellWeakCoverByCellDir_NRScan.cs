﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTDIYCellWeakCoverByCellDir_NRScan : ZTDIYCellWeakCoverByCellDir_GScan
    {
        private static ZTDIYCellWeakCoverByCellDir_NRScan intance = null;
        public new static ZTDIYCellWeakCoverByCellDir_NRScan GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new ZTDIYCellWeakCoverByCellDir_NRScan();
                    }
                }
            }
            return intance;
        }

        protected ZTDIYCellWeakCoverByCellDir_NRScan()
            : base()
        {
            isAddSampleToDTDataManager = false;
            ServiceTypes.Clear();
            ServiceTypes = ServiceTypeManager.GetServiceTypesByServiceName(ServiceName.NRScan);
            carrierID = CarrierType.ChinaMobile;
        }

        public override string Name
        {
            get { return "弱覆盖小区_NR扫频"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 36000, 36002, this.Name);
        }

        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            return NRTpHelper.InitNrScanParamSample(NRTpHelper.NrScanTpManager.RsrpThemeName);
        }

        protected override float? getBestRxLev(TestPoint tp)
        {
            float? rsrp = NRTpHelper.NrScanTpManager.GetCellRsrp(tp, 0, true);
            if (rsrp != null)
            {
                return rsrp;
            }
            return null;
        }

        protected override void getWeakCellDic(List<GridForCellWeakCover> weakGridList, Dictionary<ICell, CellWeakCoverByGridInfoBase> weakCellDic,
            double lonDiff, double latDiff)
        {
            List<NRCell> cellList = getCellsOfRegion();
            int iLoop = 1;
            WaitBox.Text = "开始获取弱覆盖小区...";
            foreach (NRCell cell in cellList)
            {
                dealWeakCellByWeakGrid(weakGridList, weakCellDic, lonDiff, latDiff, cell);
                WaitBox.ProgressPercent = (int)(100.0 * iLoop++ / cellList.Count);
            }
        }

        protected override CellWeakCoverByGridInfoBase getCellWeakCoverByGrid(ICell cell)
        {
            if (cell is NRCell)
            {
                NRCell NRCell = cell as NRCell;
                CellWeakCoverByGridInfoNr weakCell = new CellWeakCoverByGridInfoNr();
                weakCell.FillCellData(NRCell);
                //weakCell.Calculate();
                return weakCell;
            }
            return null;
        }

        protected virtual List<NRCell> getCellsOfRegion()
        {
            List<NRCell> cellList = new List<NRCell>();
            int index = 1;
            List<NRCell> curCells = MainModel.CellManager.GetCurrentNRCells();
            WaitBox.Text = "开始获取选择区域内小区...";
            foreach (NRCell cell in curCells)
            {
                if (cell.Type == NRBTSType.Outdoor 
                    && condition.Geometorys.GeoOp.Contains(cell.Longitude, cell.Latitude))
                {
                    cellList.Add(cell);
                }
                WaitBox.ProgressPercent = (int)(100.0 * index++ / curCells.Count);
            }
            return cellList;
        }

        public override BackgroundFuncType FuncType
        {
            get { return BackgroundFuncType.None; }
        }
    }
}
