﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;


namespace MasterCom.RAMS.ZTFunc.ZTLastWeakMosAna
{
    public partial class LastWeakMosAnaResultForm : MinCloseForm
    {
        private List<LastWeakMosResult> listResult = new List<LastWeakMosResult>();
        public LastWeakMosAnaResultForm()
        {
            InitializeComponent();
            this.gridView1.OptionsView.ColumnAutoWidth = false;
            this.gridView1.OptionsBehavior.Editable = false;

            ToolStripMenuItem item = new ToolStripMenuItem("导出Excel");
            item.Click += item_Click;
            ContextMenuStrip menu = new ContextMenuStrip();
            menu.Items.Add(item);
            this.gridControl1.ContextMenuStrip = menu;

            this.gridView1.DoubleClick += gridView1_DoubleClick;
            
        }

        void item_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(gridView1);
        }

        void gridView1_DoubleClick(object sender, EventArgs e)
        {
            try
            {
                int[] rows = gridView1.GetSelectedRows();
                if (rows == null || rows.Length < 1 || rows[0] < 0)
                {
                    return;
                }
                LastWeakMosResult lr = gridView1.GetRow(rows[0]) as LastWeakMosResult;
                if (lr == null)
                {
                    return;
                }
                MainModel.DTDataManager.Clear();
                MainModel.DTDataManager.Add(lr.Ev);
                MainModel.FireSetDefaultMapSerialThemes("LTE_TDD:RSRP", "LTE_FDD:RSRP");
                MainModel.MainForm.GetMapForm().GoToView(lr.Ev.Longitude, lr.Ev.Latitude);
                MainModel.FireDTDataChanged(this);
            }
            catch
            {
                //continue
            }
        }

        public void FillData(List<LastWeakMosResult> lr)
        {
            this.listResult = lr;
            this.gridControl1.DataSource = this.listResult;
            this.gridControl1.RefreshDataSource();
            this.gridControl1.Refresh();
        }
    }

    public class LastWeakMosResult
    {
        public int SN{get;set;}
        public string EventName { get; set; }
        public string DateTime { get; set; }
        public int TpNum { get; set; }
        public float AvgRSRP { get; set; }
        public float AvgSINR { get; set; }
        public float AvgUpBLER { get; set; }
        public float AvgDnBLER { get; set; }
        public int Earfcn { get; set; }
        public int PCI { get; set; }
        public float AvgLossRate { get; set; }
        public float AvgMOS { get; set; }
        public int HandOverCount { get; set; }
        public Event Ev { get; set; }
        public double Distance { get; set; }
        public string FileName { get; set; }
    }

    public class MiddleValue
    {
        public double RsrpSum { get; set; } = 0;
        public int RsrpCount { get; set; } = 0;

        public double SinrSum { get; set; } = 0;
        public int SinrCount { get; set; } = 0;

        public double UpBlerSum { get; set; } = 0;
        public int UpBlerCount { get; set; } = 0;

        public double DnBlerSum { get; set; } = 0;
        public int DnBlerCount { get; set; } = 0;

        public int Earfcn { get; set; } = 0;

        public int PCI { get; set; } = 0;

        public double LossRateSum { get; set; } = 0;
        public int LossRateCount { get; set; } = 0;

        public double MOSSum { get; set; } = 0;
        public int MOSCount { get; set; } = 0;

        public int HandOverCount { get; set; } = 0;
        public double Distance { get; set; }
    }
}
