<?xml version="1.0"?>
<Configs>
  <!--配置文件。 Author: Gene-->
  <!--！！！配置文件自动生成，请勿随便更改。！！！-->
  <Config name="MapAttributeConfig">
    <Item name="ConfigParames" typeName="IDictionary">
      <Item typeName="IDictionary" key="AllAttributes">
        <Item typeName="IList" key="事件">
          <Item typeName="IDictionary">
            <Item typeName="String" key="Name">事件</Item>
            <Item typeName="String" key="TypeName">MasterCom.RAMS.Model.Event</Item>
            <Item typeName="String" key="ImagePath">\images\event\blockedCall.png</Item>
            <Item key="AspectImage" />
            <Item key="AspectName" />
            <Item key="AspectDes" />
            <Item key="AspectDetailInfo" />
          </Item>
        </Item>
        <Item typeName="IList" key="采样点">
          <Item typeName="IDictionary">
            <Item typeName="String" key="Name">采样点</Item>
            <Item typeName="String" key="TypeName">MasterCom.RAMS.Model.TestPoint</Item>
            <Item key="ImagePath" />
            <Item key="AspectImage" />
            <Item key="AspectName" />
            <Item key="AspectDes" />
            <Item key="AspectDetailInfo" />
          </Item>
        </Item>
        <Item typeName="IList" key="小区">
          <Item typeName="IDictionary">
            <Item typeName="String" key="Name">小区</Item>
            <Item typeName="String" key="TypeName">MasterCom.RAMS.Model.Cell</Item>
            <Item typeName="String" key="ImagePath">\images\CELL.gif</Item>
            <Item key="AspectImage" />
            <Item typeName="String" key="AspectName">Name</Item>
            <Item key="AspectDes" />
            <Item typeName="String" key="AspectDetailInfo">DetailInfo</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Name">TD小区</Item>
            <Item typeName="String" key="TypeName">MasterCom.RAMS.Model.TDCell</Item>
            <Item typeName="String" key="ImagePath">\images\CELL.gif</Item>
            <Item key="AspectImage" />
            <Item typeName="String" key="AspectName">Name</Item>
            <Item key="AspectDes" />
            <Item typeName="String" key="AspectDetailInfo">DetailInfo</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Name">WCDMA小区</Item>
            <Item typeName="String" key="TypeName">MasterCom.RAMS.Model.WCell</Item>
            <Item typeName="String" key="ImagePath">\images\CELL.gif</Item>
            <Item key="AspectImage" />
            <Item typeName="String" key="AspectName">Name</Item>
            <Item key="AspectDes" />
            <Item typeName="String" key="AspectDetailInfo">DetailInfo</Item>
          </Item>
        </Item>
        <Item typeName="IList" key="BTS">
          <Item typeName="IDictionary">
            <Item typeName="String" key="Name">BTS</Item>
            <Item typeName="String" key="TypeName">MasterCom.RAMS.Model.BTS</Item>
            <Item typeName="String" key="ImagePath">\images\BTS.gif</Item>
            <Item key="AspectImage" />
            <Item typeName="String" key="AspectName">Name</Item>
            <Item key="AspectDes" />
            <Item typeName="String" key="AspectDetailInfo">DetailInfo</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Name">TD_BTS</Item>
            <Item typeName="String" key="TypeName">MasterCom.RAMS.Model.TDNodeB</Item>
            <Item typeName="String" key="ImagePath">\images\BTS.gif</Item>
            <Item key="AspectImage" />
            <Item typeName="String" key="AspectName">Name</Item>
            <Item key="AspectDes" />
            <Item typeName="String" key="AspectDetailInfo">DetailInfo</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Name">WCDMA_BTS</Item>
            <Item typeName="String" key="TypeName">MasterCom.RAMS.Model.WNodeB</Item>
            <Item typeName="String" key="ImagePath">\images\BTS.gif</Item>
            <Item key="AspectImage" />
            <Item typeName="String" key="AspectName">Name</Item>
            <Item key="AspectDes" />
            <Item typeName="String" key="AspectDetailInfo">DetailInfo</Item>
          </Item>
        </Item>
        <Item typeName="IList" key="天线">
          <Item typeName="IDictionary">
            <Item typeName="String" key="Name">天线</Item>
            <Item typeName="String" key="TypeName">MasterCom.RAMS.Model.Antenna</Item>
            <Item key="ImagePath" />
            <Item key="AspectImage" />
            <Item typeName="String" key="AspectName">Description</Item>
            <Item typeName="String" key="AspectDes">SimpleInfo</Item>
            <Item typeName="String" key="AspectDetailInfo">DetailInfo</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Name">TD天线</Item>
            <Item typeName="String" key="TypeName">MasterCom.RAMS.Model.TDAntenna</Item>
            <Item key="ImagePath" />
            <Item key="AspectImage" />
            <Item typeName="String" key="AspectName">Description</Item>
            <Item typeName="String" key="AspectDes">SimpleInfo</Item>
            <Item typeName="String" key="AspectDetailInfo">DetailInfo</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Name">WCDMA天线</Item>
            <Item typeName="String" key="TypeName">MasterCom.RAMS.Model.WAntenna</Item>
            <Item key="ImagePath" />
            <Item key="AspectImage" />
            <Item typeName="String" key="AspectName">Description</Item>
            <Item typeName="String" key="AspectDes">SimpleInfo</Item>
            <Item typeName="String" key="AspectDetailInfo">DetailInfo</Item>
          </Item>
        </Item>
        <Item typeName="IList" key="街道">
          <Item typeName="IDictionary">
            <Item typeName="String" key="Name">街道</Item>
            <Item typeName="String" key="TypeName">MapInfo.Data.Feature</Item>
            <Item key="ImagePath" />
            <Item key="AspectImage" />
            <Item key="AspectName" />
            <Item key="AspectDes" />
            <Item key="AspectDetailInfo" />
          </Item>
        </Item>
        <Item typeName="IList" key="中继器">
          <Item typeName="IDictionary">
            <Item typeName="String" key="Name">中继器</Item>
            <Item typeName="String" key="TypeName">MasterCom.RAMS.Model.Repeater</Item>
            <Item typeName="String" key="ImagePath">\images\repeater.gif</Item>
            <Item key="AspectImage" />
            <Item typeName="String" key="AspectName">Name</Item>
            <Item key="AspectDes" />
            <Item typeName="String" key="AspectDetailInfo">DetailInfo</Item>
          </Item>
        </Item>
        <Item typeName="IList" key="直放站">
          <Item typeName="IDictionary">
            <Item typeName="String" key="Name">直放站</Item>
            <Item typeName="String" key="TypeName">MasterCom.RAMS.Model.Repeater</Item>
            <Item typeName="String" key="ImagePath">\images\repeater.gif</Item>
            <Item key="AspectImage" />
            <Item typeName="String" key="AspectName">Name</Item>
            <Item key="AspectDes" />
            <Item typeName="String" key="AspectDetailInfo">DetailInfo</Item>
          </Item>
        </Item>
        <Item typeName="IList" key="CQT地点">
          <Item typeName="IDictionary">
            <Item typeName="String" key="Name">CQT地点</Item>
            <Item typeName="String" key="TypeName">MasterCom.RAMS.Func.CqtAnalysis.CqtPlaceMain</Item>
            <Item typeName="String" key="ImagePath">\images\repeater.gif</Item>
            <Item key="AspectImage" />
            <Item typeName="String" key="AspectName">Name</Item>
            <Item key="AspectDes" />
            <Item typeName="String" key="AspectDetailInfo">DetailInfo</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="IDictionary" key="AllCategoryConfig">
        <Item typeName="IDictionary" key="事件">
          <Item typeName="String" key="Name">事件</Item>
          <Item typeName="String" key="ImagePath">\images\event\blockedCall.png</Item>
          <Item typeName="Boolean" key="Visible">True</Item>
        </Item>
        <Item typeName="IDictionary" key="采样点">
          <Item typeName="String" key="Name">采样点</Item>
          <Item typeName="String" key="ImagePath">\images\event\testpoint.png</Item>
          <Item typeName="Boolean" key="Visible">True</Item>
        </Item>
        <Item typeName="IDictionary" key="小区">
          <Item typeName="String" key="Name">小区</Item>
          <Item typeName="String" key="ImagePath">\images\CELL.gif</Item>
          <Item typeName="Boolean" key="Visible">False</Item>
        </Item>
        <Item typeName="IDictionary" key="BTS">
          <Item typeName="String" key="Name">BTS</Item>
          <Item typeName="String" key="ImagePath">\images\BTS.gif</Item>
          <Item typeName="Boolean" key="Visible">False</Item>
        </Item>
        <Item typeName="IDictionary" key="天线">
          <Item typeName="String" key="Name">天线</Item>
          <Item key="ImagePath" />
          <Item typeName="Boolean" key="Visible">False</Item>
        </Item>
        <Item typeName="IDictionary" key="街道">
          <Item typeName="String" key="Name">街道</Item>
          <Item key="ImagePath" />
          <Item typeName="Boolean" key="Visible">False</Item>
        </Item>
        <Item typeName="IDictionary" key="中继器">
          <Item typeName="String" key="Name">中继器</Item>
          <Item key="ImagePath" />
          <Item typeName="Boolean" key="Visible">False</Item>
        </Item>
        <Item typeName="IDictionary" key="直放站">
          <Item typeName="String" key="Name">直放站</Item>
          <Item key="ImagePath" />
          <Item typeName="Boolean" key="Visible">False</Item>
        </Item>
        <Item typeName="IDictionary" key="CQT地点">
          <Item typeName="String" key="Name">CQT地点</Item>
          <Item key="ImagePath" />
          <Item typeName="Boolean" key="Visible">False</Item>
        </Item>
      </Item>
    </Item>
  </Config>
</Configs>