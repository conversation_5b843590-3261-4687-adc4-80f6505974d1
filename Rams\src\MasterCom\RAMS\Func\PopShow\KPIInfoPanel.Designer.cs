﻿namespace MasterCom.RAMS.Func.PopShow
{
    partial class KPIInfoPanel
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(KPIInfoPanel));
            this.panel1 = new System.Windows.Forms.Panel();
            this.label1 = new System.Windows.Forms.Label();
            this.tChartKPI = new Steema.TeeChart.TChart();
            this.bar = new Steema.TeeChart.Styles.Bar();
            this.dataGridView = new System.Windows.Forms.DataGridView();
            this.cbxReportSel = new System.Windows.Forms.ComboBox();
            this.label2 = new System.Windows.Forms.Label();
            this.cbxShowType = new System.Windows.Forms.ComboBox();
            this.label3 = new System.Windows.Forms.Label();
            this.cbxContentType = new System.Windows.Forms.ComboBox();
            this.panel2 = new System.Windows.Forms.Panel();
            this.btnColor = new System.Windows.Forms.Button();
            this.btnPopShow = new System.Windows.Forms.Button();
            this.splitMain = new System.Windows.Forms.SplitContainer();
            this.panel1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridView)).BeginInit();
            this.panel2.SuspendLayout();
            this.splitMain.Panel1.SuspendLayout();
            this.splitMain.Panel2.SuspendLayout();
            this.splitMain.SuspendLayout();
            this.SuspendLayout();
            // 
            // panel1
            // 
            this.panel1.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left)
                        | System.Windows.Forms.AnchorStyles.Right)));
            this.panel1.BackColor = System.Drawing.Color.Silver;
            this.panel1.Controls.Add(this.label1);
            this.panel1.Location = new System.Drawing.Point(3, 3);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(901, 30);
            this.panel1.TabIndex = 1;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.label1.ForeColor = System.Drawing.Color.White;
            this.label1.Location = new System.Drawing.Point(19, 9);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(61, 14);
            this.label1.TabIndex = 0;
            this.label1.Text = "KPI指标";
            // 
            // tChartKPI
            // 
            // 
            // 
            // 
            this.tChartKPI.Aspect.ElevationFloat = 345;
            this.tChartKPI.Aspect.RotationFloat = 345;
            // 
            // 
            // 
            // 
            // 
            // 
            this.tChartKPI.Axes.Bottom.Automatic = true;
            // 
            // 
            // 
            this.tChartKPI.Axes.Bottom.Grid.Style = System.Drawing.Drawing2D.DashStyle.Dash;
            this.tChartKPI.Axes.Bottom.Grid.ZPosition = 0;
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            this.tChartKPI.Axes.Bottom.Labels.Font.Shadow.Visible = false;
            this.tChartKPI.Axes.Bottom.Labels.Font.Unit = System.Drawing.GraphicsUnit.World;
            // 
            // 
            // 
            this.tChartKPI.Axes.Bottom.Labels.Shadow.Visible = false;
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            this.tChartKPI.Axes.Bottom.Title.Font.Shadow.Visible = false;
            this.tChartKPI.Axes.Bottom.Title.Font.Unit = System.Drawing.GraphicsUnit.World;
            // 
            // 
            // 
            this.tChartKPI.Axes.Bottom.Title.Shadow.Visible = false;
            // 
            // 
            // 
            this.tChartKPI.Axes.Depth.Automatic = true;
            // 
            // 
            // 
            this.tChartKPI.Axes.Depth.Grid.Style = System.Drawing.Drawing2D.DashStyle.Dash;
            this.tChartKPI.Axes.Depth.Grid.ZPosition = 0;
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            this.tChartKPI.Axes.Depth.Labels.Font.Shadow.Visible = false;
            this.tChartKPI.Axes.Depth.Labels.Font.Unit = System.Drawing.GraphicsUnit.World;
            // 
            // 
            // 
            this.tChartKPI.Axes.Depth.Labels.Shadow.Visible = false;
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            this.tChartKPI.Axes.Depth.Title.Font.Shadow.Visible = false;
            this.tChartKPI.Axes.Depth.Title.Font.Unit = System.Drawing.GraphicsUnit.World;
            // 
            // 
            // 
            this.tChartKPI.Axes.Depth.Title.Shadow.Visible = false;
            // 
            // 
            // 
            this.tChartKPI.Axes.DepthTop.Automatic = true;
            // 
            // 
            // 
            this.tChartKPI.Axes.DepthTop.Grid.Style = System.Drawing.Drawing2D.DashStyle.Dash;
            this.tChartKPI.Axes.DepthTop.Grid.ZPosition = 0;
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            this.tChartKPI.Axes.DepthTop.Labels.Font.Shadow.Visible = false;
            this.tChartKPI.Axes.DepthTop.Labels.Font.Unit = System.Drawing.GraphicsUnit.World;
            // 
            // 
            // 
            this.tChartKPI.Axes.DepthTop.Labels.Shadow.Visible = false;
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            this.tChartKPI.Axes.DepthTop.Title.Font.Shadow.Visible = false;
            this.tChartKPI.Axes.DepthTop.Title.Font.Unit = System.Drawing.GraphicsUnit.World;
            // 
            // 
            // 
            this.tChartKPI.Axes.DepthTop.Title.Shadow.Visible = false;
            // 
            // 
            // 
            this.tChartKPI.Axes.Left.Automatic = true;
            // 
            // 
            // 
            this.tChartKPI.Axes.Left.Grid.Style = System.Drawing.Drawing2D.DashStyle.Dash;
            this.tChartKPI.Axes.Left.Grid.ZPosition = 0;
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            this.tChartKPI.Axes.Left.Labels.Font.Shadow.Visible = false;
            this.tChartKPI.Axes.Left.Labels.Font.Unit = System.Drawing.GraphicsUnit.World;
            // 
            // 
            // 
            this.tChartKPI.Axes.Left.Labels.Shadow.Visible = false;
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            this.tChartKPI.Axes.Left.Title.Font.Shadow.Visible = false;
            this.tChartKPI.Axes.Left.Title.Font.Unit = System.Drawing.GraphicsUnit.World;
            // 
            // 
            // 
            this.tChartKPI.Axes.Left.Title.Shadow.Visible = false;
            // 
            // 
            // 
            this.tChartKPI.Axes.Right.Automatic = true;
            // 
            // 
            // 
            this.tChartKPI.Axes.Right.Grid.Style = System.Drawing.Drawing2D.DashStyle.Dash;
            this.tChartKPI.Axes.Right.Grid.ZPosition = 0;
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            this.tChartKPI.Axes.Right.Labels.Font.Shadow.Visible = false;
            this.tChartKPI.Axes.Right.Labels.Font.Unit = System.Drawing.GraphicsUnit.World;
            // 
            // 
            // 
            this.tChartKPI.Axes.Right.Labels.Shadow.Visible = false;
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            this.tChartKPI.Axes.Right.Title.Font.Shadow.Visible = false;
            this.tChartKPI.Axes.Right.Title.Font.Unit = System.Drawing.GraphicsUnit.World;
            // 
            // 
            // 
            this.tChartKPI.Axes.Right.Title.Shadow.Visible = false;
            // 
            // 
            // 
            this.tChartKPI.Axes.Top.Automatic = true;
            // 
            // 
            // 
            this.tChartKPI.Axes.Top.Grid.Style = System.Drawing.Drawing2D.DashStyle.Dash;
            this.tChartKPI.Axes.Top.Grid.ZPosition = 0;
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            this.tChartKPI.Axes.Top.Labels.Font.Shadow.Visible = false;
            this.tChartKPI.Axes.Top.Labels.Font.Unit = System.Drawing.GraphicsUnit.World;
            // 
            // 
            // 
            this.tChartKPI.Axes.Top.Labels.Shadow.Visible = false;
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            this.tChartKPI.Axes.Top.Title.Font.Shadow.Visible = false;
            this.tChartKPI.Axes.Top.Title.Font.Unit = System.Drawing.GraphicsUnit.World;
            // 
            // 
            // 
            this.tChartKPI.Axes.Top.Title.Shadow.Visible = false;
            this.tChartKPI.BackColor = System.Drawing.Color.Transparent;
            this.tChartKPI.Dock = System.Windows.Forms.DockStyle.Fill;
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            this.tChartKPI.Footer.Font.Shadow.Visible = false;
            this.tChartKPI.Footer.Font.Unit = System.Drawing.GraphicsUnit.World;
            // 
            // 
            // 
            this.tChartKPI.Footer.Shadow.Visible = false;
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            this.tChartKPI.Header.Font.Shadow.Visible = false;
            this.tChartKPI.Header.Font.Unit = System.Drawing.GraphicsUnit.World;
            this.tChartKPI.Header.Lines = new string[] {
        "TeeChart"};
            // 
            // 
            // 
            this.tChartKPI.Header.Shadow.Visible = false;
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            this.tChartKPI.Legend.Font.Shadow.Visible = false;
            this.tChartKPI.Legend.Font.Unit = System.Drawing.GraphicsUnit.World;
            // 
            // 
            // 
            // 
            // 
            // 
            this.tChartKPI.Legend.Title.Font.Bold = true;
            // 
            // 
            // 
            this.tChartKPI.Legend.Title.Font.Shadow.Visible = false;
            this.tChartKPI.Legend.Title.Font.Unit = System.Drawing.GraphicsUnit.World;
            // 
            // 
            // 
            this.tChartKPI.Legend.Title.Pen.Visible = false;
            // 
            // 
            // 
            this.tChartKPI.Legend.Title.Shadow.Visible = false;
            this.tChartKPI.Location = new System.Drawing.Point(0, 0);
            this.tChartKPI.Name = "tChartKPI";
            // 
            // 
            // 
            // 
            // 
            // 
            this.tChartKPI.Panel.Brush.Color = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(255)))));
            // 
            // 
            // 
            this.tChartKPI.Panel.Shadow.Visible = false;
            this.tChartKPI.Series.Add(this.bar);
            this.tChartKPI.Size = new System.Drawing.Size(316, 254);
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            this.tChartKPI.SubFooter.Font.Shadow.Visible = false;
            this.tChartKPI.SubFooter.Font.Unit = System.Drawing.GraphicsUnit.World;
            // 
            // 
            // 
            this.tChartKPI.SubFooter.Shadow.Visible = false;
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            this.tChartKPI.SubHeader.Font.Shadow.Visible = false;
            this.tChartKPI.SubHeader.Font.Unit = System.Drawing.GraphicsUnit.World;
            // 
            // 
            // 
            this.tChartKPI.SubHeader.Shadow.Visible = false;
            this.tChartKPI.TabIndex = 2;
            // 
            // 
            // 
            // 
            // 
            // 
            this.tChartKPI.Walls.Back.AutoHide = false;
            // 
            // 
            // 
            this.tChartKPI.Walls.Back.Shadow.Visible = false;
            // 
            // 
            // 
            this.tChartKPI.Walls.Bottom.AutoHide = false;
            // 
            // 
            // 
            this.tChartKPI.Walls.Bottom.Shadow.Visible = false;
            // 
            // 
            // 
            this.tChartKPI.Walls.Left.AutoHide = false;
            // 
            // 
            // 
            this.tChartKPI.Walls.Left.Shadow.Visible = false;
            // 
            // 
            // 
            this.tChartKPI.Walls.Right.AutoHide = false;
            // 
            // 
            // 
            this.tChartKPI.Walls.Right.Shadow.Visible = false;
            // 
            // bar
            // 
            // 
            // 
            // 
            this.bar.Brush.Color = System.Drawing.Color.Red;
            // 
            // 
            // 
            // 
            // 
            // 
            this.bar.Marks.Callout.ArrowHead = Steema.TeeChart.Styles.ArrowHeadStyles.None;
            this.bar.Marks.Callout.ArrowHeadSize = 8;
            // 
            // 
            // 
            this.bar.Marks.Callout.Brush.Color = System.Drawing.Color.Black;
            this.bar.Marks.Callout.Distance = 0;
            this.bar.Marks.Callout.Draw3D = false;
            this.bar.Marks.Callout.Length = 20;
            this.bar.Marks.Callout.Style = Steema.TeeChart.Styles.PointerStyles.Rectangle;
            // 
            // 
            // 
            // 
            // 
            // 
            this.bar.Marks.Font.Shadow.Visible = false;
            this.bar.Marks.Font.Unit = System.Drawing.GraphicsUnit.World;
            // 
            // 
            // 
            this.bar.Pen.Color = System.Drawing.Color.FromArgb(((int)(((byte)(153)))), ((int)(((byte)(0)))), ((int)(((byte)(0)))));
            this.bar.Title = "bar1";
            // 
            // 
            // 
            this.bar.XValues.DataMember = "X";
            this.bar.XValues.Order = Steema.TeeChart.Styles.ValueListOrder.Ascending;
            // 
            // 
            // 
            this.bar.YValues.DataMember = "Bar";
            // 
            // dataGridView
            // 
            this.dataGridView.AllowUserToAddRows = false;
            this.dataGridView.AllowUserToDeleteRows = false;
            this.dataGridView.BackgroundColor = System.Drawing.Color.White;
            this.dataGridView.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D;
            this.dataGridView.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dataGridView.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dataGridView.Location = new System.Drawing.Point(0, 0);
            this.dataGridView.Name = "dataGridView";
            this.dataGridView.ReadOnly = true;
            this.dataGridView.RowHeadersVisible = false;
            this.dataGridView.RowTemplate.Height = 23;
            this.dataGridView.Size = new System.Drawing.Size(562, 254);
            this.dataGridView.TabIndex = 3;
            this.dataGridView.CellClick += new System.Windows.Forms.DataGridViewCellEventHandler(this.dataGridView_CellClick);
            // 
            // cbxReportSel
            // 
            this.cbxReportSel.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cbxReportSel.FormattingEnabled = true;
            this.cbxReportSel.Location = new System.Drawing.Point(3, 4);
            this.cbxReportSel.Name = "cbxReportSel";
            this.cbxReportSel.Size = new System.Drawing.Size(226, 20);
            this.cbxReportSel.TabIndex = 4;
            this.cbxReportSel.SelectedIndexChanged += new System.EventHandler(this.cbxReportSel_SelectedIndexChanged);
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(242, 9);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(41, 12);
            this.label2.TabIndex = 5;
            this.label2.Text = "方式：";
            // 
            // cbxShowType
            // 
            this.cbxShowType.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cbxShowType.FormattingEnabled = true;
            this.cbxShowType.Location = new System.Drawing.Point(279, 4);
            this.cbxShowType.Name = "cbxShowType";
            this.cbxShowType.Size = new System.Drawing.Size(110, 20);
            this.cbxShowType.TabIndex = 4;
            this.cbxShowType.SelectedIndexChanged += new System.EventHandler(this.cbxShowType_SelectedIndexChanged);
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(394, 9);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(41, 12);
            this.label3.TabIndex = 5;
            this.label3.Text = "内容：";
            // 
            // cbxContentType
            // 
            this.cbxContentType.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cbxContentType.FormattingEnabled = true;
            this.cbxContentType.Location = new System.Drawing.Point(431, 5);
            this.cbxContentType.Name = "cbxContentType";
            this.cbxContentType.Size = new System.Drawing.Size(128, 20);
            this.cbxContentType.TabIndex = 4;
            this.cbxContentType.SelectedIndexChanged += new System.EventHandler(this.cbxContentType_SelectedIndexChanged);
            // 
            // panel2
            // 
            this.panel2.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left)
                        | System.Windows.Forms.AnchorStyles.Right)));
            this.panel2.Controls.Add(this.btnColor);
            this.panel2.Controls.Add(this.btnPopShow);
            this.panel2.Controls.Add(this.cbxReportSel);
            this.panel2.Controls.Add(this.cbxShowType);
            this.panel2.Controls.Add(this.label2);
            this.panel2.Controls.Add(this.cbxContentType);
            this.panel2.Controls.Add(this.label3);
            this.panel2.Location = new System.Drawing.Point(11, 36);
            this.panel2.Name = "panel2";
            this.panel2.Size = new System.Drawing.Size(882, 27);
            this.panel2.TabIndex = 6;
            // 
            // btnColor
            // 
            this.btnColor.Location = new System.Drawing.Point(566, 3);
            this.btnColor.Name = "btnColor";
            this.btnColor.Size = new System.Drawing.Size(83, 23);
            this.btnColor.TabIndex = 7;
            this.btnColor.Text = "颜色设置";
            this.btnColor.UseVisualStyleBackColor = true;
            this.btnColor.Visible = false;
            // 
            // btnPopShow
            // 
            this.btnPopShow.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnPopShow.FlatStyle = System.Windows.Forms.FlatStyle.Popup;
            this.btnPopShow.Location = new System.Drawing.Point(838, 4);
            this.btnPopShow.Name = "btnPopShow";
            this.btnPopShow.Size = new System.Drawing.Size(41, 20);
            this.btnPopShow.TabIndex = 6;
            this.btnPopShow.Text = "弹出";
            this.btnPopShow.UseVisualStyleBackColor = true;
            this.btnPopShow.Click += new System.EventHandler(this.btnPopShow_Click);
            // 
            // splitMain
            // 
            this.splitMain.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
                        | System.Windows.Forms.AnchorStyles.Left)
                        | System.Windows.Forms.AnchorStyles.Right)));
            this.splitMain.Location = new System.Drawing.Point(11, 69);
            this.splitMain.Name = "splitMain";
            // 
            // splitMain.Panel1
            // 
            this.splitMain.Panel1.Controls.Add(this.dataGridView);
            // 
            // splitMain.Panel2
            // 
            this.splitMain.Panel2.Controls.Add(this.tChartKPI);
            this.splitMain.Size = new System.Drawing.Size(882, 254);
            this.splitMain.SplitterDistance = 562;
            this.splitMain.TabIndex = 7;
            // 
            // KPIInfoPanel
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.BackColor = System.Drawing.Color.Transparent;
            this.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.Controls.Add(this.splitMain);
            this.Controls.Add(this.panel2);
            this.Controls.Add(this.panel1);
            this.Name = "KPIInfoPanel";
            this.Size = new System.Drawing.Size(907, 337);
            this.panel1.ResumeLayout(false);
            this.panel1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridView)).EndInit();
            this.panel2.ResumeLayout(false);
            this.panel2.PerformLayout();
            this.splitMain.Panel1.ResumeLayout(false);
            this.splitMain.Panel2.ResumeLayout(false);
            this.splitMain.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.Panel panel1;
        private Steema.TeeChart.TChart tChartKPI;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.DataGridView dataGridView;
        private System.Windows.Forms.ComboBox cbxReportSel;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.ComboBox cbxShowType;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.ComboBox cbxContentType;
        private System.Windows.Forms.Panel panel2;
        private System.Windows.Forms.SplitContainer splitMain;
        private Steema.TeeChart.Styles.Bar bar;
        private System.Windows.Forms.Button btnPopShow;
        private System.Windows.Forms.Button btnColor;
    }
}
