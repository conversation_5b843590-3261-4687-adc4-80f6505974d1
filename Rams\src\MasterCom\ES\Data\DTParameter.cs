﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Xml;
using MasterCom.Util;

namespace MasterCom.ES.Data
{
    public class DTParameter
    {
        public string paramKey { get; set; }
        public long minValue { get; set; }
        public long maxValue { get; set; }
        public override string ToString()
        {
            return paramKey;
        }

        public static XmlElement AddItem(XmlConfigFile configFile, XmlElement config, string name, object value)
        {
            if (value is DTParameter)
            {
                XmlElement item = configFile.AddItem(config, name, value.GetType());
                configFile.AddItem(item, "Parameter", (value as DTParameter).Param);
                return item;
            }
            return null;
        }
        public static object GetItemValue(XmlConfigFile configFile, XmlElement item, string typeName)
        {
            if (typeName.Equals(typeof(DTParameter).Name))
            {
                Dictionary<string, object> param = configFile.GetItemValue(item, "Parameter") as Dictionary<string, object>;
                DTParameter p = new DTParameter();
                p.Param = param;
                return p;
            }
            return null;
        }
        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["paramKey"] = paramKey;
                param["minValue"] = minValue;
                param["maxValue"] = maxValue;
                return param;
            }
            set
            {
                if (value == null || value.Count == 0)
                {
                    return;
                }
                this.paramKey = (string)value["paramKey"];
                this.minValue = (long)value["minValue"];
                this.maxValue = (long)value["maxValue"];
            }
        }
    }
}
