﻿using MasterCom.MTGis;
using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class UltraSiteQueryNR : UltraSiteQueryBase
    {
        protected static readonly object lockObj = new object();
        private static UltraSiteQueryNR instance = null;
        public static UltraSiteQueryNR GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new UltraSiteQueryNR();
                    }
                }
            }
            return instance;
        }

        public UltraSiteQueryNR()
           : base()
        {
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.Region;
        }

        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 35000, 35016, this.Name);
        }

        protected override List<ISite> getAllSites()
        {
            List<NRBTS> tempSites = getCurrentNRBTSs();
            List<ISite> allSites = new List<ISite>();
            foreach (NRBTS item in tempSites)
            {
                if (item.Type == NRBTSType.Indoor)
                {
                    continue;
                }
                allSites.Add(item);
            }

            return allSites;
        }

        private List<NRBTS> getCurrentNRBTSs()
        {
            List<NRBTS> validBts = CellManager.GetInstance().GetCurrentNRBTSs();
            if (MainModel.SearchGeometrys != null && MainModel.SearchGeometrys.IsSelectRegion())
            {
                DbRect selectRect = MainModel.SearchGeometrys.RegionBounds;
                DbRect validRect = new DbRect(selectRect.x1 - 0.1, selectRect.y1 - 0.1, selectRect.x2 + 0.1, selectRect.y2 + 0.1);

                for (int i = 0; i < validBts.Count; i++)
                {
                    ISite site = validBts[i];
                    if (!validRect.IsPointInThisRect(site.Longitude, site.Latitude))
                    {
                        validBts.RemoveAt(i);
                        i--;
                    }
                }
            }
            return validBts;
        }

        protected override List<UltraSiteInfo> getCellAvgDistanceByDir(ISite site, List<ISite> otherSites)
        {
            NRBTS bts = site as NRBTS;
            List<UltraSiteInfo> cellDisList = new List<UltraSiteInfo>();
            foreach (NRCell cell in bts.LatestCells)
            {
                dealCellDis(otherSites, bts, cellDisList, cell);
            }
            return cellDisList;
        }

        protected override void addValidBts(Dictionary<ICell, UltraSiteInfo> cellDisDic, ISite bts, ISite judgedBts, double dis)
        {
            NRBTS nrBts = bts as NRBTS;
            NRBTS nrJudgedBts = judgedBts as NRBTS;

            foreach (NRCell cell in nrJudgedBts.LatestCells)
            {
                if (dis <= UltraSiteCondition.DiffBandDistanceMin)
                {//距离很近有可能是共站情况，过滤。
                    continue;
                }

                addUltraNearSite(cellDisDic, nrBts, dis, cell);
            }
        }

        protected override void dealOutRegionSites(List<ISite> outRegionSites, Dictionary<ICell, UltraSiteInfo> cellDisDic, ISite bts)
        {
            foreach (ISite other in outRegionSites)
            {
                double dis = 0;
                if (UltraSiteCondition.IsTooNearSite(bts, other, out dis))
                {
                    NRBTS lteBts = bts as NRBTS;
                    foreach (NRCell cell in lteBts.LatestCells)
                    {
                        addUltraNearSite(cellDisDic, other, dis, cell);
                    }
                }
            }
        }

        protected override void showResultForm(Dictionary<ICell, List<UltraSiteInfo>> cellDic)
        {
            UltraSiteCellFormNR frm = MainModel.CreateResultForm(typeof(UltraSiteCellFormNR)) as UltraSiteCellFormNR;
            frm.FillData(farCells, highCells, nearSites);
            frm.Visible = true;
            frm.BringToFront();
        }
    }
}
