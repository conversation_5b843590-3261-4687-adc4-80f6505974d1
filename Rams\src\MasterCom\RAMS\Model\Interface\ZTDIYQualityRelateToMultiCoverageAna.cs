﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.ZTFunc;
using System.Windows.Forms;
using MasterCom.Util;
using MasterCom.RAMS.Grid;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Stat.Data;
using DBDataViewer;
using System.Drawing;
using MasterCom.MTGis;

namespace MasterCom.RAMS.Net
{
    /// <summary>
    /// Written by Wu<PERSON>un<PERSON>ong 2012.12.6
    /// </summary>
    public class ZTDIYQualityRelateToMultiCoverageAna : DIYSampleByRegion
    {
        GridMatrix<GridContent> gridContentMatrix;
        GridMatrix<ColorUnit> colorMatrix;
        public MapFormItemSelection ItemSelection { get; set; }

        //int setRxlev = 0;   //12
        //int setRxlevDiff = 0;  //90
        //bool coFreq = false;
        //MapForm.DisplayInterferenceType interferenceType = MapForm.DisplayInterferenceType.BCCH_CPI;
        //CoverDegreeType coverType = CoverDegreeType.Relative;
        //int qualityDividingLine = 0;
        //int multiCoverageDividingLine = 0;
        //string channel;

        readonly MultiCoverageCondition curCondition = new MultiCoverageCondition();

        QueryCondition curConditionDT;

        AdjustToolQualRelateMutilCov adjustTool = null;

        public ZTDIYQualityRelateToMultiCoverageAna(MainModel mainModel)
            : base(mainModel)
        {
            isAddSampleToDTDataManager = false;
        }

        public override string Name
        {
            get { return "路测质量关联扫频重叠度分析"; }
        }

        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 12000, 12072, this.Name);
        }

        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            List<object> columnsDef = new List<object>();

            Dictionary<string, object> param = new Dictionary<string, object>();
            param["param_name"] = "GSCAN_RxLev";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "GSCAN_BCCH";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "GSCAN_BSIC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            Dictionary<string, object> tmpDic = new Dictionary<string, object>();
            tmpDic.Add("name", (object)"GSM_SCAN_RxLev");
            tmpDic.Add("themeName", (object)"GSM_SCAN_RxLev");
            tmpDic.Add("columnsDef", (object)columnsDef);

            DIYSampleGroup group = new DIYSampleGroup();
            group.Param = tmpDic;
            return group;
        }

        protected override void query()
        {
            curSelDIYSampleGroup = getCurSelDIYSampleGroupPrepare();
            if (curSelDIYSampleGroup == null)
                return;

            SetQueryFormQualRelateMultiCov setDataForm = new SetQueryFormQualRelateMultiCov(MainModel, ItemSelection, Condition);
            if (setDataForm.ShowDialog() != DialogResult.OK)
                return;

            curConditionDT = setDataForm.CurConditionDT;
               
            MultiCoverageSetForm multiCoverageSetForm = new MultiCoverageSetForm();
            if (multiCoverageSetForm.ShowDialog() != DialogResult.OK)
                return;
            multiCoverageSetForm.GetSettingFilterRet(curCondition);

            QueryQualityGrid();

            QueryCondition curConditionScan = setDataForm.CurConditionScan;
            this.SetQueryCondition(curConditionScan);

            WaitBox.CanCancel = true;
            WaitBox.Text = "正在查询...";
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
                MainModel.ClearDTData();
                WaitBox.Show("正在统计扫频的重叠度数据...", queryInThread, clientProxy);

                CalculateColor();

                MainModel.IsPrepareWithoutGridPartParam = true;
                MainModel.MainForm.GetMapForm().GetGridShowLayer().CurUsingGridColorFixed = null;

                MapGridLayer.NeedFreshFullImg = true;
                MainModel.FireGridCoverQueried(this);

                setLegendType();
                MainModel.RefreshLegend();

                MainModel.IsPrepareWithoutGridPartParam = false; //查询结束，回复原始的布尔值，不影响其他栅格绘图

                if (adjustTool != null)
                {
                    adjustTool.Close();
                }

                adjustTool = new AdjustToolQualRelateMutilCov(MainModel,gridContentMatrix, colorMatrix, curCondition.QualityDividingLine, curCondition.MultiCoverageDividingLine, curCondition.CoverDegreeType);
                adjustTool.Show(MainModel.MainForm.GetMapForm());
            }
            finally
            {
                clientProxy.Close();
            }
        }

        private void setLegendType()
        {
            GridColorFixed gridColorFixed = new GridColorFixed();
            gridColorFixed.items = new List<GridColorFixedItem>();
            gridColorFixed.theme = "质量RxQuality0-5级占比 & 重叠覆盖度对比栅格";
            GridColorFixedItem item = new GridColorFixedItem();
            item.desc = "质量<" + curCondition.QualityDividingLine + "% &" + "重叠覆盖度<" + curCondition.MultiCoverageDividingLine;
            item.color = Color.Maroon;
            gridColorFixed.items.Add(item);
            item = new GridColorFixedItem();
            item.desc = "质量>=" + curCondition.QualityDividingLine + "% &" + "重叠覆盖度<" + curCondition.MultiCoverageDividingLine;
            item.color = Color.Blue;
            gridColorFixed.items.Add(item);
            item = new GridColorFixedItem();
            item.desc = "质量<" + curCondition.QualityDividingLine + "% &" + "重叠覆盖度>=" + curCondition.MultiCoverageDividingLine;
            item.color = Color.Yellow;
            gridColorFixed.items.Add(item);
            item = new GridColorFixedItem();
            item.desc = "质量>=" + curCondition.QualityDividingLine + "% &" + "重叠覆盖度>=" + curCondition.MultiCoverageDividingLine;
            item.color = Color.Green;
            gridColorFixed.items.Add(item);

            MainModel.MainForm.GetMapForm().GetGridShowLayer().CurUsingGridColorFixed = gridColorFixed;
        }

        protected DTDisplayParameterInfo rxLevParam = DTDisplayParameterManager.GetInstance()["GSM_SCAN", "RxLev"];
        protected DTDisplayParameterInfo bsicParam = DTDisplayParameterManager.GetInstance()["GSM_SCAN", "BSIC"];

        protected override void doWithDTData(TestPoint tp)
        {
            MainCellInfo info = new MainCellInfo();
            info.MaxRxlev = rxLevParam.ValueMax;
            info.MinRxlev = rxLevParam.ValueMin;
            info.MainCellName = "";
            info.MainCell = null;
            info.MaxValue = null;//主强rxlev

            int relativeLevel = 0;
            int absoluteLevel = 0;
            int relANDabsLevel = 0;

            for (int index = 0; index < 50; index++)//扫频数据，入库时已按rxlev从大到小排序，无需再排序
            {
                short? bcchChannel = (short?)(int?)tp["GSCAN_BCCH", 0];
                if (curCondition.Channel == "900" && bcchChannel != null)
                {
                    if (bcchChannel >= 512)
                        return;
                }
                else if (curCondition.Channel == "1800" && bcchChannel != null)
                {
                    if (bcchChannel < 512)
                        return;
                }
                else if (bcchChannel == null)
                {
                    return;
                }

                dealOverLapLevel(tp, info, ref relativeLevel, ref absoluteLevel, ref relANDabsLevel, index);
            }

            //重叠覆盖度要包括主服小区，因此需要+1
            absoluteLevel++;
            relativeLevel++;
            relANDabsLevel++;
            
            GridContent curGc=LocateGridContent(tp);
            if (curGc != null)
            {
                curGc.relCoverDegree += relativeLevel;
                curGc.absCoverDegree += absoluteLevel;
                curGc.relAndAbsCoverDegree += relANDabsLevel;
                curGc.countTestpoint++;
            }
        }

        private void dealOverLapLevel(TestPoint tp, MainCellInfo info, ref int relativeLevel, ref int absoluteLevel, ref int relANDabsLevel, int index)
        {
            float? rxlev = (float?)tp["GSCAN_RxLev", index];
            short? bcch = (short?)(int?)tp["GSCAN_BCCH", index];
            byte? bsic = (byte?)(int?)tp["GSCAN_BSIC", index];
            if (rxlev != null && bcch != null && bsic != null && rxlev < info.MaxRxlev && rxlev > info.MinRxlev)
            {
                if (info.MaxValue == null)
                {
                    info.MaxValue = rxlev;
                    info.MainCell = CellManager.GetInstance().GetNearestCell(tp.DateTime, (short)bcch, (byte)bsic, tp.Longitude, tp.Latitude);
                    info.MainCellName = getCellName(tp.DateTime, (short)bcch, (byte)bsic, tp.Longitude, tp.Latitude);
                }
                else
                {
                    Cell curCell = CellManager.GetInstance().GetNearestCell(tp.DateTime, (short)bcch, (byte)bsic, tp.Longitude, tp.Latitude);

                    if (rxlev > curCondition.SetRxlev)//绝对覆盖度
                    {
                        absoluteLevel = addLevel(info.MainCellName, info.MainCell, absoluteLevel, bcch, curCell);
                    }

                    if (rxlev - info.MaxValue > -curCondition.SetRxlevDiff)//相对覆盖度
                    {
                        relativeLevel = addLevel(info.MainCellName, info.MainCell, relativeLevel, bcch, curCell);
                    }

                    if (rxlev > curCondition.SetRxlev && rxlev - info.MaxValue > -curCondition.SetRxlevDiff)
                    {
                        relANDabsLevel = addLevel(info.MainCellName, info.MainCell, relANDabsLevel, bcch, curCell);
                    }
                }
            }
        }

        private class MainCellInfo
        {
            public float MaxRxlev;
            public float MinRxlev;
             
            public string MainCellName = "";
            public Cell MainCell = null;
            public float? MaxValue = null;//主强rxlev
        }

        private int addLevel(string mainCellName, Cell mainCell, int relANDabsLevel, short? bcch, Cell curCell)
        {
            if (curCondition.CoFreq)
            {
                bool isCoFreq = IsCoFreq(mainCell, mainCellName, curCell, (short)bcch);
                if (isCoFreq)
                {
                    relANDabsLevel++;
                }
            }
            else
            {
                relANDabsLevel++;
            }

            return relANDabsLevel;
        }

        int tarRowCount = 0;
        int tarColCount = 0;

        private void CalculateColor()
        {
            for (int r = 0; r < tarRowCount; r++)
            {
                for (int c = 0; c < tarColCount; c++)
                {
                    dealGridColor(r, c);
                }
            }
        }

        private void dealGridColor(int r, int c)
        {
            GridContent gc = gridContentMatrix[r, c];
            ColorUnit cu = colorMatrix[r, c];
            if (gc != null && cu != null)
            {
                #region 显示与GridContent相应的栅格颜色
                if (gc.EnableCompare)
                {
                    if (curCondition.CoverDegreeType == CoverDegreeType.Relative)
                    {
                        setColor(gc, cu, gc.RelAvgCoverDegree);
                    }
                    else if (curCondition.CoverDegreeType == CoverDegreeType.Absolute)
                    {
                        setColor(gc, cu, gc.AbsAvgCoverDegreeAvg);
                    }
                    else if (curCondition.CoverDegreeType == CoverDegreeType.RelAndAbs)
                    {
                        setColor(gc, cu, gc.RelAndAbsAvgCoverDegree);
                    }
                }
                else //数据缺失，栅格无法比较
                {
                    cu.color = Color.Transparent;
                }
                #endregion
            }
        }

        private void setColor(GridContent gc, ColorUnit cu, int coverDegree)
        {
            if (gc.rxquality0_5 < curCondition.QualityDividingLine && coverDegree < curCondition.MultiCoverageDividingLine)
            {
                cu.color = Color.Maroon;
            }
            else if (gc.rxquality0_5 >= curCondition.QualityDividingLine && coverDegree < curCondition.MultiCoverageDividingLine)
            {
                cu.color = Color.Blue;
            }
            else if (gc.rxquality0_5 < curCondition.QualityDividingLine && coverDegree >= curCondition.MultiCoverageDividingLine)
            {
                cu.color = Color.Yellow;
            }
            else if (gc.rxquality0_5 >= curCondition.QualityDividingLine && coverDegree >= curCondition.MultiCoverageDividingLine)
            {
                cu.color = Color.Green;
            }
        }

        /// <summary>
        /// 定位采样点所在的栅格
        /// </summary>
        private GridContent LocateGridContent(TestPoint testPoint)
        {
            int r, c;
            GridHelper.GetIndexOfDefaultSizeGrid(testPoint.Longitude, testPoint.Latitude, out r, out c);
            return gridContentMatrix[r, c];
        }

        protected string getCellName(DateTime time, short bcch, byte bsic, double x, double y)
        {
            Cell cell = CellManager.GetInstance().GetNearestCell(time, bcch, bsic, x, y);
            if (cell == null)
            {
                return bcch.ToString() + "_" + bsic.ToString();
            }
            else
            {
                return cell.Name;
            }
        }

        protected bool IsCoFreq(Cell mainCell, string mainCellName, Cell curCell, short curBCCH)
        {
            if (mainCell != null)
            {
                if (curCell != null)
                {
                    if (mainCell.IsCoFreq(curCell, curCondition.InterferenceType))
                    {
                        return true;
                    }
                }
                else
                {
                    return judgeCoFreq(mainCell, curBCCH);
                }
            }
            else
            {
                short mainBCCH = short.Parse(mainCellName.Substring(0, mainCellName.IndexOf("_")));
                if (curCell != null)
                {
                    return judgeCoFreq(curCell, mainBCCH);
                }
                else
                {
                    if (curBCCH == mainBCCH)
                    {
                        return true;
                    }
                }
            }
            return false;
        }

        private bool judgeCoFreq(Cell curCell, short mainBCCH)
        {
            if (curCondition.InterferenceType == MapForm.DisplayInterferenceType.BCCH_CPI)
            {
                if (curCell.IsCo_BCCH(mainBCCH))
                {
                    return true;
                }
            }
            else if (curCondition.InterferenceType == MapForm.DisplayInterferenceType.TCH_FREQ)
            {
                if (curCell.IsCo_TCH(mainBCCH))
                {
                    return true;
                }
            }
            else
            {
                if (curCell.IsCo_BCCH(mainBCCH) || curCell.IsCo_TCH(mainBCCH))
                {
                    return true;
                }
            }
            return false;
        }

        private void QueryQualityGrid()
        {
            MapGridLayer gridLayer = this.MainModel.MainForm.GetMapForm().GetGridShowLayer();
            GridColorModeItem curCMItem = null;
            bool needRecover = false;
            if (gridLayer.CurUsingColorMode != null)
            {
                needRecover = true;
                curCMItem = gridLayer.CurUsingColorMode;
            }

            DiyQueryQualityInGrid queryQuality = new DiyQueryQualityInGrid(MainModel);
            queryQuality.setCurGridColorModeItemFormula("100*((Mx_5A010501+Mx_5A010502+Mx_5A010503+Mx_5A010504+Mx_5A010505+Mx_5A010506)/Mx_5A01050C)");  //输入RxQuality0-5占比公式
            queryQuality.SetQueryCondition(curConditionDT);
            queryQuality.Query();
            tarRowCount = queryQuality.tarRowCount;
            tarColCount = queryQuality.tarColCount;
            gridContentMatrix = queryQuality.gridContentMatrix;
            colorMatrix = queryQuality.colorMatrix;

            if (needRecover)
                gridLayer.CurUsingColorMode = curCMItem;  //恢复原有的当前栅格样式
        }

        private class DiyQueryQualityInGrid : DIYQueryCoverGridByRegion
        {
            public DiyQueryQualityInGrid(MainModel mainModel)
                : base(mainModel)
            {
            }

            public GridMatrix<GridContent>gridContentMatrix = null;
            public GridMatrix<ColorUnit> colorMatrix = null;
            string curFormula = "";
            /// <summary>
            /// 设定需要查询的栅格公式
            /// </summary>
            /// <param name="formula"></param>
            public void setCurGridColorModeItemFormula(string formula)
            {
                MapGridLayer gridLayer = this.MainModel.MainForm.GetMapForm().GetGridShowLayer();
                GridColorModeItem gridColorModeItem = new GridColorModeItem();
                gridColorModeItem.formula = formula;
                curFormula = formula;
                gridLayer.CurUsingColorMode = gridColorModeItem;
            }

            protected override void query()
            {
                ClientProxy clientProxy = new ClientProxy();
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
                try
                {
                    WaitBox.Show("正在统计栅格数据...", queryInThread, clientProxy);
                }
                finally
                {
                    clientProxy.Close();
                }
            }
            protected override void afterRecieveOnePeriodData(params object[] reservedParams)
            {
                GetGridQuality();
            }

            protected override string getStatImgNeededTriadID(params object[] paramSet)
            {
                List<string> formulaSet = new List<string>();
                formulaSet.Add(curFormula);
                return getTriadIDIgnoreServiceType(formulaSet);
            }
            
            public int tarRowCount = 0;
            public int tarColCount = 0;
            /// <summary>
            /// 获取栅格内的路测质量
            /// </summary>
            private void GetGridQuality()
            {
                colorMatrix = MainModel.CurGridColorUnitMatrix;
                gridContentMatrix = getGridFormulaMatrix(colorMatrix);
                for (int r = 0; r < tarRowCount; r++)
                {
                    for (int c = 0; c < tarColCount; c++)
                    {
                        ColorUnit cu = colorMatrix[r, c];
                        if (cu != null && cu.DataHub != null)
                        {
                            try
                            {
                                float retf = (float)cu.DataHub.CalcValueByFormula(curFormula);
                                GridContent gridContent = gridContentMatrix[r, c];
                                gridContent.rxquality0_5 = retf;
                                gridContent.gridRow = tarRowCount;
                                gridContent.gridCol = tarColCount;
                                gridContentMatrix[r, c] = gridContent;
                            }
                            catch (Exception ex)
                            {
                                log.Error(ex.StackTrace);
                            }
                        }
                    }
                }
            }

            private GridMatrix<GridContent> getGridFormulaMatrix(GridMatrix<ColorUnit> colorMatrix)
            {
                if (colorMatrix==null)
                {
                    return new GridMatrix<GridContent>();
                }
                GridMatrix<GridContent> matrix = new GridMatrix<GridContent>();
                foreach (ColorUnit cu in colorMatrix)
                {
                    int r, c;
                    GridHelper.GetIndexOfDefaultSizeGrid(cu.CenterLng, cu.CenterLat, out r, out c);
                    if (matrix[r, c] == null)
                    {
                        GridContent grid = new GridContent();
                        grid.LTLng = cu.LTLng;
                        grid.LTLat = cu.LTLat;
                        matrix[r, c] = grid;
                    }
                }
                return matrix;
            }
           
        }
    }

    public enum CoverDegreeType
    {
        Relative,
        Absolute,
        RelAndAbs,
    }

    public class GridContent:GridUnitBase
    {
        public int gridRow { get; set; }
        public int gridCol { get; set; }
        /// <summary>
        /// 质量与重叠度比较有效
        /// </summary>
        public bool EnableCompare { get; set; }
        public double rxquality0_5 { get; set; }
        public int relCoverDegree { get; set; }
        public int absCoverDegree { get; set; }
        public int relAndAbsCoverDegree { get; set; }
        public int channel { get; set; } //1:900；   2:1800；   3:全频段.

        public int countTestpoint { get; set; }

        public int RelAvgCoverDegree
        {
            get { return countTestpoint == 0 ? 0 : relCoverDegree / countTestpoint; }
        }

        public int AbsAvgCoverDegreeAvg
        {
            get { return countTestpoint == 0 ? 0 : absCoverDegree / countTestpoint; }
        }

        public int RelAndAbsAvgCoverDegree
        {
            get { return countTestpoint == 0 ? 0 : relAndAbsCoverDegree / countTestpoint; }
        }
    }
}
