﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using DevExpress.XtraCharts;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class LTEScanStructSetForm : BaseDialog
    {
        public LTEScanStructSetForm()
        {
            InitializeComponent();
            initData();
        }

        private void initData()
        {
            cbRsrpLevel.Text = "-95";
            cbRsrpLevel.Items.Add("-90");
            cbRsrpLevel.Items.Add("-95");
            cbRsrpLevel.Items.Add("-100");
            cbRsrpLevel.Items.Add("-105");
            cbRsrpLevel.Items.Add("-110");
        }

        public int iRsrpSplit { get; set; } = -95;
        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            int rsrp;
            if (int.TryParse(cbRsrpLevel.Text, out rsrp))
            {
                iRsrpSplit = rsrp;
                iRsrpSplit = iRsrpSplit / 5 * 5;
                if (iRsrpSplit > -90 || iRsrpSplit < -110)
                {
                    iRsrpSplit = -95;
                    cbRsrpLevel.Text = "-95";
                }
                else
                {
                    this.DialogResult = DialogResult.OK;
                }
            }
            else
            {
                iRsrpSplit = -95;
                cbRsrpLevel.Text = "-95";
            }
        }
    }
}
