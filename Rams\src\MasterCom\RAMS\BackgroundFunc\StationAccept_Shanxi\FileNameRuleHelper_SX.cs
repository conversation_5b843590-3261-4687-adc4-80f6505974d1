﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.BackgroundFunc.StationAccept_Shanxi
{
    public static class FileNameRuleHelper_SX
    {
        public static bool JudgeFileValidByName(string fileName)
        {
            if (fileName.Contains("DT") //基站环测文件名中包含DT关键字，只在出报告截图时用
                || (!fileName.Contains("下载") && !fileName.Contains("Download")
                && !fileName.Contains("上传") && !fileName.Contains("Upload")))//单站测试文件名应包含下载或上传关键字
            {
                return false;
            }
            return true;
        }


        /// <summary>
        /// 根据文件名获取当前文件测试的小区标识
        /// </summary>
        public static string GetCellNameKeyByFile(string fileName)
        {
            string[] strs = fileName.Split('_');
            string cellNameKey = "";
            if (strs.Length == 4 || strs.Length == 5)
            {
                //旧版手动命名格式
                //宏站_榆林定边冯地坑苗大渠-ZLH-YLSO023TL-40_任意点_下载测试.log
                //宏站_咸阳秦都安吉物流-HLH-XYBO044FLD-0_好点_上传测试_MS1.csv
                //宏站_咸阳秦都安吉物流-HLH-电梯1-XYBO044FLD-0_好点_上传测试_MS1.csv
                //宏站_咸阳秦都安吉物流-HLH-地库1-XYBO044FLD-0_好点_上传测试_MS1.csv

                cellNameKey = GetCellNameKeyByFull(strs[1]);
            }
            //else if (strs.Length == 7)
            //{
            //    //最新gen文件自动转换的命名格式
            //    //19980225090435789_20200806_Any Download_移动_陕西_西安_PHU_网格_HLH-HZAO006TND-41.gen
            //    //19980225090435789_20200806_Any Download_移动_陕西_西安_PHU_网格_HLH-Elevator1-HZAO006TND-41.gen
            //    //19980225090435789_20200806_Any Download_移动_陕西_西安_PHU_网格_HLH-Basement1-HZAO006TND-41.gen
            //    cellNameKey = GetCellNameKeyByFull(strs[4]);
            //    //cellNameKey = strs[4];
            //}
            return cellNameKey;
        }

        public static string GetCellNameKeyByFull(string cellNameFull)
        {
            string cellNameKey = "";

            string[] cellStr = cellNameFull.Split(new char[] { '-' }, 2);
            if (cellStr.Length == 2)
            {
                cellNameKey = cellStr[1];
            }
            return cellNameKey;
        }


        public static string GetNameKey(string fileName, bool isOutDoor)
        {
            if (isOutDoor)
            {
                return getOutDoorFileNameKey(fileName);
            }
            else
            {
                return getInDoorFileNameKey(fileName);
            }
        }

        private static string getOutDoorFileNameKey(string fileName)
        {
            string strKeyHead = "";
            if (fileName.Contains("上传") || fileName.Contains("Upload"))
            {
                strKeyHead = "上传";
            }
            else if (fileName.Contains("下载") || fileName.Contains("Download"))
            {
                strKeyHead = "下载";
            }

            if (fileName.Contains("好点") || fileName.Contains("Good"))
            {
                return strKeyHead + "好点";
            }
            else if (fileName.Contains("中点") || fileName.Contains("Middle"))
            {
                return strKeyHead + "中点";
            }
            else if (fileName.Contains("差点") || fileName.Contains("Bad"))
            {
                return strKeyHead + "差点";
            }
            else if (fileName.Contains("任意点") || fileName.Contains("Any"))
            {
                return strKeyHead + "任意点";
            }
            else if (fileName.Contains("切换"))
            {
                return strKeyHead + "切换";
            }

            else if (fileName.Contains("大包"))
            {
                return "大包时延";
            }
            else if (fileName.Contains("小包"))
            {
                return "小包时延";
            }
            return "";
        }

        private static string getInDoorFileNameKey(string fileName)
        {
            if (fileName.Contains("上传") || fileName.Contains("Upload"))
            {
                return "上行";
            }
            else if (fileName.Contains("下载") || fileName.Contains("Download"))
            {
                return "下行";
            }
            return "";
        }


        public static string GetFileFilterStr(string btsName)
        {
            string strFilter = string.Format("{0}%DT%下载 or {0}%DT%上传 or {0}%DT%Download or {0}%DT%Upload", btsName);
            return strFilter;
        }


        public static FileNameKey GetFileNameKey(string fileName)
        {
            if (fileName.Contains("上传") || fileName.Contains("Upload"))
            {
                return FileNameKey.Upload;
            }
            else if (fileName.Contains("下载") || fileName.Contains("Download"))
            {
                return FileNameKey.Download;
            }
            else if (fileName.Contains("大包"))
            {
                return FileNameKey.PingBig;
            }
            else if (fileName.Contains("小包"))
            {
                return FileNameKey.PingSmall;
            }
            return FileNameKey.UNKNOWN;
        }









        //public static FileNameKey JudgeFileType(string fileName,bool isOutDoor)
        //{
        //    if (isOutDoor)
        //    {
        //        if (fileName.Contains("下载") || fileName.Contains("Download"))
        //        {
        //            return judeOutDoorDownloadFile(fileName);
        //        }
        //        else if (fileName.Contains("上传") || fileName.Contains("Upload"))
        //        {
        //            return judeOutDoorUploadFile(fileName);
        //        }
        //        return FileNameKey.UNKNOWN;
        //    }
        //    else
        //    {
        //        if (fileName.Contains("下载") || fileName.Contains("Download"))
        //        {
        //            return FileNameKey.InDoor_Download;
        //        }
        //        else if (fileName.Contains("上传") || fileName.Contains("Upload"))
        //        {
        //            return FileNameKey.InDoor_Upload;
        //        }
        //        return FileNameKey.UNKNOWN;
        //    }
        //}

        //private static FileNameKey judeOutDoorDownloadFile(string fileName)
        //{
        //    if (fileName.Contains("DT"))
        //    {
        //        return FileNameKey.OutDoor_DT_Download;
        //    }
        //    else if (fileName.Contains("好点") || fileName.Contains("Good"))
        //    {
        //        return FileNameKey.OutDoor_Good_Download;
        //    }
        //    else if (fileName.Contains("中点") || fileName.Contains("Middle"))
        //    {
        //        return FileNameKey.OutDoor_Middle_Download;
        //    }
        //    else if (fileName.Contains("差点") || fileName.Contains("Bad"))
        //    {
        //        return FileNameKey.OutDoor_Bad_Download;
        //    }
        //    return FileNameKey.UNKNOWN;
        //}

        //private static FileNameKey judeOutDoorUploadFile(string fileName)
        //{
        //    if (fileName.Contains("DT"))
        //    {
        //        return FileNameKey.OutDoor_DT_Upload;
        //    }
        //    else if (fileName.Contains("好点") || fileName.Contains("Good"))
        //    {
        //        return FileNameKey.OutDoor_Good_Upload;
        //    }
        //    else if (fileName.Contains("中点") || fileName.Contains("Middle"))
        //    {
        //        return FileNameKey.OutDoor_Middle_Upload;
        //    }
        //    else if (fileName.Contains("差点") || fileName.Contains("Bad"))
        //    {
        //        return FileNameKey.OutDoor_Bad_Upload;
        //    }
        //    return FileNameKey.UNKNOWN;
        //}
    }

    public enum FileNameKey
    {
        UNKNOWN,
        Download,
        Upload,
        PingBig,
        PingSmall

        //InDoor_Download,
        //InDoor_Upload,

        //OutDoor_DT_Upload,
        //OutDoor_DT_Download,
        //OutDoor_Good_Upload,
        //OutDoor_Good_Download,
        //OutDoor_Middle_Upload,
        //OutDoor_Middle_Download,
        //OutDoor_Bad_Upload,
        //OutDoor_Bad_Download,
        //OutDoor_Any_Upload,
        //OutDoor_Any_Download
    }
}
