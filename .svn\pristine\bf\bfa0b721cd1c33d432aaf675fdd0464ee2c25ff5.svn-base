﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using BrightIdeasSoftware;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.NewBlackBlock
{
    public partial class ReappearResultForm : MinCloseForm
    {
        public ReappearResultForm() : base()
        {
            InitializeComponent();
            DisposeWhenClose = true;
            InitListView();

            miCollapseAll.Click += MiCollapseAll_Click;
            miExpandAll.Click += MiExpandAll_Click;
            miExportExcel.Click += MiExportExcel_Click;
            treeListView.MouseDoubleClick += TreeListView_MouseDoubleClick;
        }

        public void FillData(List<ReappearBlockItem> resultList)
        {
            treeListView.BeginUpdate();
            treeListView.ClearObjects();
            treeListView.SetObjects(resultList);
            treeListView.EndUpdate();
        }

        private void InitListView()
        {
            treeListView.CanExpandGetter = delegate(object row)
            {
                ReappearBlockItem item = row as ReappearBlockItem;
                return item.SubItems.Count > 0;
            };
            treeListView.ChildrenGetter = delegate(object row)
            {
                ReappearBlockItem item = row as ReappearBlockItem;
                return item.SubItems;
            };
            colID.AspectGetter = delegate(object row)
            {
                ReappearBlockItem item = row as ReappearBlockItem;
                return item.BlackBlockItem.blockId;
            };
            colName.AspectGetter = delegate(object row)
            {
                ReappearBlockItem item = row as ReappearBlockItem;
                return item.BlackBlockItem.AreaName;
            };
            colCreateDate.AspectGetter = delegate(object row)
            {
                ReappearBlockItem item = row as ReappearBlockItem;
                return item.BlackBlockItem.CreateDateString;
            };
            colCloseDate.AspectGetter = delegate(object row)
            {
                ReappearBlockItem item = row as ReappearBlockItem;
                return item.BlackBlockItem.ClosedDateString;
            };
            colStatus.AspectGetter = delegate(object row)
            {
                ReappearBlockItem item = row as ReappearBlockItem;
                return item.BlackBlockItem.StatusDes;
            };
            colCentLng.AspectGetter = delegate(object row)
            {
                ReappearBlockItem item = row as ReappearBlockItem;
                return item.BlackBlockItem.CenterLongitude;
            };
            colCentLat.AspectGetter = delegate(object row)
            {
                ReappearBlockItem item = row as ReappearBlockItem;
                return item.BlackBlockItem.CenterLatitude;
            };
            colDistance.AspectGetter = delegate(object row)
            {
                ReappearBlockItem item = row as ReappearBlockItem;
                return item.Distance == -1 ? "" : Math.Round(item.Distance, 2).ToString();
            };
            colReappearCount.AspectGetter = delegate(object row)
            {
                ReappearBlockItem item = row as ReappearBlockItem;
                if (item.Distance == -1)
                {
                    return item.SubItems.Count.ToString();
                }
                return "";
            };
        }

        private void MiCollapseAll_Click(object sender, EventArgs e)
        {
            treeListView.CollapseAll();
        }

        private void MiExpandAll_Click(object sender, EventArgs e)
        {
            treeListView.ExpandAll();
        }

        private void MiExportExcel_Click(object sender, EventArgs e)
        {
            List<List<object>> exportData = new List<List<object>>();
            miExpandAll.PerformClick();

            // header row
            List<object> headerList = TreeListViewGetRow(null, true);
            List<object> headerList2 = TreeListViewGetRow(null, true);
            headerList2[0] = "重现黑点ID";
            headerList.AddRange(headerList2);
            exportData.Add(headerList);

            // content
            foreach (object first in treeListView.Roots)
            {
                List<object> rowList = new List<object>();
                List<object> firstList = TreeListViewGetRow(first, false);
                rowList.AddRange(firstList);

                bool hasChild = false;
                foreach (object second in treeListView.GetChildren(first))
                {
                    hasChild = true;
                    List<object> secondList = TreeListViewGetRow(second, false);
                    rowList.AddRange(secondList);
                    exportData.Add(rowList);
                }
                if (!hasChild)
                {
                    exportData.Add(rowList);
                }
            }

            ExcelNPOIManager.ExportToExcel(exportData);
        }

        private void TreeListView_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            OLVListItem item = treeListView.GetItemAt(e.X, e.Y) as OLVListItem;
            if (item == null)
            {
                return;
            }
            treeListView.SelectedItem = item;

            double x = Convert.ToDouble(colCentLng.GetStringValue(treeListView.SelectedObject));
            double y = Convert.ToDouble(colCentLat.GetStringValue(treeListView.SelectedObject));
            MainModel.MainForm.GetMapForm().GoToView(x, y);
        }

        /// <summary>
        /// 从TreeListView获取一行内容或者列标题
        /// </summary>
        /// <param name="rowObject">行对象, 如果isHeader为true则忽略该参数</param>
        /// <param name="isHeader">是否获取列标题</param>
        /// <returns></returns>
        private List<object> TreeListViewGetRow(object rowObject, bool isHeader)
        {
            List<object> retList = new List<object>();
            foreach (OLVColumn col in treeListView.Columns)
            {
                if (isHeader)
                {
                    retList.Add(col.Text);
                }
                else
                {
                    retList.Add(col.GetStringValue(rowObject));
                }
            }
            return retList;
        }
    }
}
