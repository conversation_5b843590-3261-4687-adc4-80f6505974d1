﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.ZTFunc;
using System.Windows.Forms;
using MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas;
using MasterCom.Util;
using MasterCom.MTGis;
using MasterCom.RAMS.Func.SystemSetting;
using DevExpress.XtraEditors;
using MasterCom.RAMS.BackgroundFunc;

namespace MasterCom.RAMS.Net
{
    public class ZTLowSpeedClassify : ZTLowSpeedAnaByRegion_LTE
    {
        public ZTLowSpeedClassify(): base()
        {
        }
        public override string Name
        {
            get { return "LTE下载低速率原因归类分析及统计"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22101, this.Name);
        }
        protected override bool getCondition()
        {
            this.IncludeMessage = true;
            this.FilterEventByRegion = false;

            #region condition
            funcCond.bMatchRoad = true;
            funcCond.CheckVideo = false;
            funcCond.CheckEmail = false;
            funcCond.CheckHTTP = false;
            funcCond.CheckLowSpeed_TDOrW = false;
            funcCond.CheckGSMLowSpeed = false;

            funcCond.FTPDLRateMax = 2;
            funcCond.FTPULRateMax = 0.5;
            funcCond.DistanceFTPDLMin = 100;
            funcCond.DistanceFTPULMin = 100;
            funcCond.LowPercent = 100;
            #endregion

            SettingDlgLTE dlg = new SettingDlgLTE();
            dlg.SetCondition(funcCond);
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            funcCond = dlg.GetCondition();
            setRoadCond();
            return true;
        }

        protected override void fireShowForm()
        {
            if (lowSpeedInfoList_LTE.Count == 0)
            {
                MessageBox.Show("没有符合条件的信息！");
                CodeRateHelper.Clear();
                return;
            }

            MainModel.FireSetDefaultMapSerialTheme(funcCond.IsAppSpeed ? "LTE_TDD:APP_Speed_Mb" : "LTE_TDD:PDCP_DL_Mb");
            
            ZTLowSpeedClassifyForm lowSpeedInfoForm = MainModel.GetObjectFromBlackboard(typeof(ZTLowSpeedClassifyForm)) as ZTLowSpeedClassifyForm;
            if (lowSpeedInfoForm == null || lowSpeedInfoForm.IsDisposed)
            {
                lowSpeedInfoForm = new ZTLowSpeedClassifyForm(MainModel);
            }
            lowSpeedInfoForm.FillData(lowSpeedInfoList_LTE, GetCodeTable());
            lowSpeedInfoForm.Owner = MainModel.MainForm;
            lowSpeedInfoForm.Visible = true;
            lowSpeedInfoForm.BringToFront();
            lowSpeedInfoList_LTE = null;
            CodeRateHelper.Clear();
        }

        protected override void getLTEPointDetail(TempData data, ref List<string> lacciList, ref List<int> freqList)
        {
            getLTEPointDetail(data, lacciList, freqList, false, null);
        }

        protected override void getLTEPointDetail(TempData data, string netType, ref List<string> lacciList, ref List<int> freqList)
        {
            getLTEPointDetail(data, lacciList, freqList, true, netType);
        }

        private void getLTEPointDetail(TempData data, List<string> lacciList, List<int> freqList, bool dealNB, string netType)
        {
            try
            {
                #region  LTE 速率,pccpch_rscp,pccpch_c2i,dpch_rscp,dpch_c2i,bler,pdsch_rscp,pdsch_c2i,scch_rscp,scch_c2i等指标  最大值,最小值,均值
                TestPoint tpPrev = null;
                List<string> cellNames = new List<string>();
                foreach (TestPoint tPoint in tps)
                {
                    setDataValue(data, tpPrev, tPoint, dealNB);

                    int? lac = (int?)(ushort?)tPoint["lte_TAC"];
                    //int? ci = (int?)tPoint["lte_ECI"];
                    LTECell lteCell = tPoint.GetMainCell_LTE();
                    int? bcch = (int?)tPoint["lte_EARFCN"];

                    if (lteCell != null)
                    {
                        string lacci = lac.ToString() + "_" + lteCell.SCellID.ToString();
                        string cellName = lteCell.Name;
                        if (!lacciList.Contains(lacci))
                        {
                            lacciList.Add(lacci);
                        }
                        if (!cellNames.Contains(cellName))
                        {
                            cellNames.Add(cellName);
                        }
                    }
                    if (bcch != null && bcch > 0 && bcch < 65535 && !freqList.Contains((int)bcch))
                    {
                        freqList.Add((int)bcch);
                    }
                    newManager.Add(tPoint);
                    tpPrev = tPoint;
                    data.iTestPointCount++;
                }
                #endregion

                SaveInfoFunc(data, lacciList, cellNames, freqList, netType, distance);
            }
            catch (Exception ee)
            {
                MessageBox.Show(ee.Message + Environment.NewLine + ee.Source + Environment.NewLine + ee.StackTrace);
            }
        }

        private void setDataValue(TempData data, TestPoint tpPrev, TestPoint tPoint, bool dealNB)
        {
            float? speed;
            funcCond.IsValidSpeed(tPoint, out speed);
            float? rsrp = (float?)tPoint["lte_RSRP"];
            float? sinr = (float?)tPoint["lte_SINR"];
            int? throughput_DL = (int?)tPoint["lte_APP_ThroughputDL"];
            if (speed != null && speed >= 0)
            {
                data.speed_sampleNum++;
                data.low_speed = getLow(speed, data.low_speed);
                data.high_speed = getHigh(speed, data.high_speed);
                data.mean_speed += (float)speed;
                data.distance_speed_0 += calculateDistance0Speed((float)speed, tPoint, tpPrev);
                data.duration_speed_0 += calculateDuration0Speed((float)speed, tPoint, tpPrev);
            }
            if (rsrp != null && rsrp >= -141 && rsrp <= 25)
            {
                data.rsrp_sampleNum++;
                data.low_rsrp = getLow(rsrp, data.low_rsrp);
                data.high_rsrp = getHigh(rsrp, data.high_rsrp);
                data.mean_rsrp += (float)rsrp;
            }
            if (dealNB)
            {
                addNCellData(data, tpPrev, tPoint, rsrp);
            }
            if (sinr != null && sinr >= -20 && sinr <= 50)
            {
                data.sinr_sampleNum++;
                data.low_sinr = (int)getLow(sinr, data.low_sinr);
                data.high_sinr = (int)getHigh(sinr, data.high_sinr);
                data.mean_sinr += (int)sinr;
            }
            if (rsrp != null && rsrp >= -141 && rsrp <= 25 && sinr != null && sinr >= -20 && sinr <= 50 && rsrp > -105 && sinr > -3)
            {
                data.iBestEvment++;
            }
            if (throughput_DL != null && throughput_DL >= 0)
            {
                data.throughput_DL_sampleNum++;
                data.low_throughput_DL = getLow(throughput_DL, data.low_throughput_DL);
                data.high_throughput_DL = getHigh(throughput_DL, data.high_throughput_DL);
                data.mean_throughput_DL += (float)throughput_DL;
            }
            data.multiCoverageSampleNum += isMultiCoverage(tPoint) ? 1 : 0;
        }

        private void addNCellData(TempData data, TestPoint tpPrev, TestPoint tPoint, float? rsrp)
        {
            bool isFind = false;
            for (int i = 0; i < 25; i++)
            {
                float? nRsrp = (float?)tPoint["lte_NCell_RSRP", i];
                if (nRsrp != null && nRsrp >= -141 && nRsrp <= 25)
                {
                    data.nrsrp_sampleNum++;
                    data.low_nrsrp = getLow(nRsrp, data.low_nrsrp);
                    data.high_nrsrp = getHigh(nRsrp, data.high_nrsrp);
                    data.mean_nrsrp += (float)nRsrp;
                    if (nRsrp > -105 && nRsrp - rsrp >= 5)
                    {
                        if (!isFind)
                        {
                            data.AddNCellDurEndTime(tPoint, tpPrev);
                            data.iNCelltpCount++;
                        }
                        isFind = true;
                    }
                }
            }
        }

        private float getLow(float? data, float lowData)
        {
            if (data > lowData)
            {
                return lowData;
            }
            return (float)data;
        }

        private float getHigh(float? data, float highData)
        {
            if (data > highData)
            {
                return (float)data;
            }
            return highData;
        }

        private void SaveInfoFunc(TempData data, List<string> lacciList, List<string> cellNames, List<int> freqList, string netType, double distance)
        {
            StringBuilder laccis = new StringBuilder();
            foreach (string lacci in lacciList)
            {
                if (laccis.Length != 0)
                {
                    laccis.Append(" | ");
                }
                laccis.Append(lacci);
            }

            StringBuilder cellNameStr = new StringBuilder();
            foreach (string name in cellNames)
            {
                if (cellNameStr.Length != 0)
                {
                    cellNameStr.Append(" | ");
                }
                cellNameStr.Append(name);
            }

            StringBuilder bcchs = new StringBuilder();
            foreach (int bcch in freqList)
            {
                if (bcchs.Length != 0)
                {
                    bcchs.Append(" | ");
                }
                bcchs.Append(bcch.ToString());
            }
            AnalysisMessage(tps, data);
            data.GetResult();

            DIYLowSpeedInfo_LTE_MainRoad roadPart = new DIYLowSpeedInfo_LTE_MainRoad(data, distance, laccis.ToString(), bcchs.ToString(), tps, cellNameStr.ToString());
            searchWeakSinrRoad(tps, out roadPart.weakSinrLst, out roadPart.weakSinrDistance);
            calDisMultiCoverage(tps, out roadPart.distanceMulticoverage);
            calOtherInfo(tps, out roadPart.meanPDSCHBLER, out roadPart.code0Mean, out roadPart.code1Mean, out roadPart.code0Max,
                out roadPart.code1Max, out roadPart.cqi0Mean, out roadPart.cqi1Mean, out roadPart.code0_64QAMRate, out roadPart.code0_16QAMRate,
                out roadPart.code1_64QAMRate, out roadPart.code1_16QAMRate);
            calOtherInfo2(tps, roadPart);
            roadPart.HandOver_count = 0;
            roadPart.GetResult();
            roadPart.LowPercent = curLowPercent;
            roadPart.LowPointCount = curLowTpCount;
            roadPart.NetType = netType;

            roadPart.CityName = DistrictManager.GetInstance().getDistrictName(MainModel.DistrictID);
            short? lteType = (short?)tps[0]["lte_APP_type"];

            if (netType == null)
            {
                if (roadPart.Distance >= funcCond.DistanceMin)
                {
                    roadPart.SN = lowSpeedInfoList_LTE.Count + 1;
                    lowSpeedInfoList_LTE.Add(roadPart);
                }
            }
            else
            {
                SaveInfoByType(lteType, roadPart);
            }
        }
       
        private void AnalysisMessage(List<TestPoint> tps, TempData data)
        {
            data.Init();
            TempData dataTmp = CodeRateHelper.GetNearCode(tps[0].FileID, tps[0].lTimeWithMillsecond, tps[tps.Count - 1].lTimeWithMillsecond);
            if (dataTmp != null)
            {
                data.CodeEPS = dataTmp.CodeEPS;
                data.CodeAPN = dataTmp.CodeAPN;
                data.CodeQuality = dataTmp.CodeQuality;
                data.CodeResult = dataTmp.CodeResult;
                data.isPartLimit = dataTmp.isPartLimit;
                foreach (TempData tmp in CodeRateHelper.dicCode[tps[0].FileID].Values)
                {
                    if (tmp.CodeResult.isLimit)
                        data.iLimitNum++;
                }
            }
        }

        /// <summary>
        /// 获取全部文件限速信令
        /// </summary>
        private System.Data.DataTable GetCodeTable()
        {
            System.Data.DataTable table = new System.Data.DataTable("信令页");
            table.Columns.Add("序号");
            table.Columns.Add("地市");
            table.Columns.Add("文件名");
            table.Columns.Add("时间");
            table.Columns.Add("信令名称");
            table.Columns.Add("信令类型");
            table.Columns.Add("限速大小");
            table.Columns.Add("是否有效");
            table.Columns.Add("有效时段");
            table.Columns.Add("LTE FTP业务限速时长");
            if (CodeRateHelper.dicCode == null)
                return table;
            int iCol = Int32.MinValue;
            foreach (int iFileID in CodeRateHelper.dicCode.Keys)
            {
                foreach (int iTime in CodeRateHelper.dicCode[iFileID].Keys)
                {
                    if (CodeRateHelper.dicCode[iFileID][iTime].CodeResult.TimeSlotDic.Count > iCol)
                        iCol = CodeRateHelper.dicCode[iFileID][iTime].CodeResult.TimeSlotDic.Count;
                }
            }
            iCol = iCol > 10 ? 10 : iCol;
            for (int iColumns = 1; iColumns < iCol + 1; iColumns++)
            {
                table.Columns.Add("FTP限速内采样点数" + iColumns);
                table.Columns.Add("FTP大于限速采样点数" + iColumns);
                table.Columns.Add("有效比例" + iColumns);
            }
            string cityName = DistrictManager.GetInstance().getDistrictName(MainModel.DistrictID);
            int idx = 1;
            foreach (int iFileID in CodeRateHelper.dicCode.Keys)
            {
                idx = addTableRow(table, iCol, cityName, idx, iFileID);
            }
            return table;
        }

        private int addTableRow(System.Data.DataTable table, int iCol, string cityName, int idx, int iFileID)
        {
            foreach (int iTime in CodeRateHelper.dicCode[iFileID].Keys)
            {
                System.Data.DataRow dr = table.NewRow();
                dr["序号"] = idx++;
                dr["地市"] = cityName;
                dr["文件名"] = CodeRateHelper.dicCode[iFileID][iTime].BelongFile;
                dr["时间"] = CodeRateHelper.dicCode[iFileID][iTime].CodeTime.ToString("yyyy-MM-dd HH:mm:ss.fff");
                dr["信令名称"] = "Modify EPS bearer context request";
                dr["信令类型"] = CodeRateHelper.dicCode[iFileID][iTime].CodeResult.strType;
                dr["限速大小"] = CodeRateHelper.dicCode[iFileID][iTime].CodeResult.strLimitInfo.Replace("有限速，", "");
                dr["是否有效"] = CodeRateHelper.dicCode[iFileID][iTime].CodeResult.IsValidLimit() ? "是" : "否";
                if (CodeRateHelper.dicCode[iFileID][iTime].CodeResult.IsValidLimit())
                {
                    dr["有效时段"] = CodeRateHelper.dicCode[iFileID][iTime].CodeResult.strValidLimit();
                }
                else
                {
                    dr["有效时段"] = "-";
                }
                dr["LTE FTP业务限速时长"] = CodeRateHelper.dicCode[iFileID][iTime].CodeResult.iLimitDuration;
                int itmp = 0;
                foreach (StatTmp stat in CodeRateHelper.dicCode[iFileID][iTime].CodeResult.TimeSlotDic.Values)
                {
                    itmp++;
                    if (itmp > iCol)
                        break;
                    dr["FTP限速内采样点数" + itmp] = stat.iLimitTpCount;
                    dr["FTP大于限速采样点数" + itmp] = stat.iGTRCount;
                    dr["有效比例" + itmp] = stat.strValidRate;
                }
                table.Rows.Add(dr);
            }

            return idx;
        }
    }

    public class DIYLowSpeedInfo_LTE_MainRoad : DIYLowSpeedInfo_LTE
    {
        public DIYLowSpeedInfo_LTE_MainRoad(MasterCom.RAMS.Net.ZTLowSpeedAnaByRegion_GSM.TempData data,
            double distance, string laccis, string bcchs, List<TestPoint> testPointList, string cellName)
            : base(data, distance, laccis, bcchs, testPointList, cellName)
        {
            this.CodeRate = data.CodeResult;
            this.iBestEvment = data.iBestEvment;
            this.bMainCellEvment = data.bMainCellEvment;
            this.bNCellEvment = data.bNCellEvment;
            this.iNCellDuration = data.iNCellDur;
            this.iLimitNum = data.iLimitNum;
            this.Duration_speed_0 = data.duration_speed_0;
            this.isPartLimit = data.isPartLimit;
        }
        public MasterCom.RAMS.Net.ZTLowSpeedAnaByRegion_GSM.CodeRate CodeRate { get; set; }
        /// <summary>
        /// 无线环境好采样点数
        /// </summary>
        public int iBestEvment { get; set; }

        //主小区无线环境 true为好，false为差
        public bool bMainCellEvment { get; set; } = false;
        public bool bNCellEvment { get; set; } = false;

        //无线环境差,邻小区无线环境较好的时长
        public int iNCellDuration { get; set; }
        public string WirelessInfo
        {
            get
            {
                string showInfo ="";
                if (bMainCellEvment)
                {
                    showInfo = "无线环境好";
                    if (!CodeRate.isLimit)
                        showInfo += "，无限速";
                    else
                        showInfo += "，有限速"; 
                }
                else
                {
                    showInfo = "无线环境差";
                    if (!CodeRate.isLimit)
                        showInfo += "，无限速";
                    else
                        showInfo += "，有限速"; 
                    if(bNCellEvment)
                        showInfo += "，但邻小区无线环境好"; 
                    else
                        showInfo += "，但邻小区无线环境也差"; 
                }
                return showInfo;
            }
        }

        public int iLimitNum { get; set; }
        /// <summary>
        ///限速是否有效
        /// </summary>
        public bool IsValidLimit
        {
            get
            {
                if (CodeRate.TimeSlotDic.Count == 0)
                    return false;
                bool b = false;
                if (isPartLimit)
                {
                    for (int idx = 0; idx < this.testPointList.Count; idx++)
                    {
                        b = this.CodeRate.IsValidLimit(this.testPointList[idx].lTimeWithMillsecond);
                        if (b)
                            break;
                    }
                }
                else
                {
                    b = this.CodeRate.IsValidLimit(this.testPointList[0].lTimeWithMillsecond);
                }
                return b;
            }
        }

        private bool isPartLimit = false;
        public bool IsPartLimit
        {
            get
            {
                return this.IsValidLimit && isPartLimit;
            }
            set { this.isPartLimit = value; }
        }

        public string strLimitInfo
        {
            get
            {
                string info = this.CodeRate.strLimitInfo;
                if (!string.IsNullOrEmpty(info))
                {
                    if (!this.IsValidLimit)
                        info += "，但无效";
                    if (this.IsPartLimit)
                        info += "，部分限速";
                }
                else
                {
                    info = "-";
                }
                return info;
            }
        }
        public int Duration_speed_0 { get; set; }
    }
}
