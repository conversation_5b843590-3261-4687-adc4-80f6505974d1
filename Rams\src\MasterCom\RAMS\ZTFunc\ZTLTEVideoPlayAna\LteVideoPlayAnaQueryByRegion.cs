﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    class LteVideoPlayAnaQueryByRegion : LteVideoPlayAnaBase
    {
        public LteVideoPlayAnaQueryByRegion(MainModel mm)
            : base(mm)
        {

        }
        private static LteVideoPlayAnaQueryByRegion instance = null;
        protected static readonly object lockObj = new object();
        public static LteVideoPlayAnaQueryByRegion GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new LteVideoPlayAnaQueryByRegion(MainModel.GetInstance());
                    }
                }
            }
            return instance;
        }

        public override string Name
        {
            get { return "流媒体卡顿缓冲分析(按区域)"; }
        }
        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22102, this.Name);
        }
        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.Region;
        }

    }
    class LteFddVideoPlayAnaQueryByRegion : LteVideoPlayAnaQueryByRegion
    {
        public LteFddVideoPlayAnaQueryByRegion(MainModel mm)
            : base(mm)
        {

        }
        protected override void setParmAndServiceType()
        {
            this.RsrpStr = "lte_fdd_RSRP";
            this.SinrStr = "lte_fdd_SINR";
            this.AppSpeedStr = "lte_fdd_APP_Speed_Mb";
            //this.AppSpeedStr = "lte_fdd_APP_ThroughputDL_Mb";
            this.NCellRsrpStr = "lte_fdd_NCell_RSRP";
            this.TacStr = "lte_fdd_TAC";

            this.Columns = new List<string>();
            Columns.Add(RsrpStr);
            Columns.Add(SinrStr);
            Columns.Add(TacStr);
            Columns.Add(NCellRsrpStr);
            Columns.Add(AppSpeedStr);
            Columns.Add("lte_fdd_ECI");
            Columns.Add("lte_fdd_EARFCN");
            Columns.Add("lte_fdd_PCI");
            Columns.Add("lte_fdd_APP_Speed");
            Columns.Add("lte_fdd_APP_type");
            Columns.Add("lte_fdd_Transmission_Mode");
            Columns.Add("lte_fdd_PDSCH_BLER");
            Columns.Add("lte_fdd_Rank_Indicator");
            Columns.Add("lte_fdd_PDCCH_DL_Grant_Count");
            Columns.Add("lte_fdd_PDSCH_PRb_Num_slot");
            Columns.Add("lte_fdd_NCell_SINR");

            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_FDD_DATA);
            ServiceTypes.Add(ServiceType.LTE_FDD_IDLE);
            ServiceTypes.Add(ServiceType.LTE_FDD_MULTI);
            ServiceTypes.Add(ServiceType.LTE_FDD_VOICE);
        }

        private static LteFddVideoPlayAnaQueryByRegion instance = null;
        public new static LteFddVideoPlayAnaQueryByRegion GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new LteFddVideoPlayAnaQueryByRegion(MainModel.GetInstance());
                    }
                }
            }
            return instance;
        }
        public override string Name
        {
            get { return "流媒体卡顿缓冲LTE_FDD分析(按区域)"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 26000, 26076, this.Name);
        }

    }
}
