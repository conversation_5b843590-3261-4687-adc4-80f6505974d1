﻿using System;
using System.Collections.Generic;
using System.Windows.Forms;
using MasterCom.Util;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NbIotMgrsWeakRsrpResult : NbIotMgrsResultControlBase
    {
        protected NbIotMgrsFuncItem funcItem = null;
        protected ZTScanGridLayer layer;
        /// <summary>
        /// 整理成可用于图层显示的数据
        /// </summary>
        protected List<ScanGridInfo> scanGridInfoList = new List<ScanGridInfo>();
        /// <summary>
        /// 传入的数据源
        /// </summary>
        protected List<CarrierData> carrierDataList = new List<CarrierData>();

        public NbIotMgrsWeakRsrpResult()
        {
            InitializeComponent();

            miExportSimpleExcel.Click += MiExportSimpleExcel_Click;
            miExportDetailExcel.Click += MiExportDetailExcel_Click;
            gvSerialGrid.DoubleClick += GridView_DoubleClick;
            gvGrid.DoubleClick += GridView_DoubleClick;

            miExportAllExcel.Click += base.MiExportExcelAll_Click;
            miExportList.Click += MiExportGridList_Click;

            miExportAllShp.Click += base.MiExportShpAll_Click;
            miExportShp.Click += MiExportShp_Click;
        }

        public override string Desc
        {
            get { return "连续弱覆盖"; }
        }

        public virtual void FillData(NbIotMgrsFuncItem curFuncItem)
        {
            this.funcItem = curFuncItem;
            RefreshResult();
        }

        public virtual void RefreshResult()
        {
            GetData();
            scanGridInfoList = new List<ScanGridInfo>();
            foreach (var areaWeakGrid in carrierDataList)
            {
                foreach (var serialWeakGrid in areaWeakGrid.AreaGridViews)
                {
                    foreach (var gridInfo in serialWeakGrid.SerialGridViews)
                    {
                        foreach (var item in gridInfo.GridViews)
                        {
                            if (item.CellGrid.Count > 0)
                            {
                                scanGridInfoList.Add(item.CellGrid[0]);
                            }
                        }
                    }
                }
            }

            gridControl1.DataSource = carrierDataList;
            gridControl1.RefreshDataSource();
        }

        protected virtual void GetData()
        {
            NbIotMgrsWeakRsrpStater stater = this.funcItem.Stater as NbIotMgrsWeakRsrpStater;
            carrierDataList = stater.GetViews();
        }

        #region 导出
        protected override void ExportAllExcel(string savePath)
        {
            string sheetName = Desc;
            string fileName = System.IO.Path.Combine(savePath, sheetName + ".xlsx");
            List<NPOIRow> rowList = getNPOIRow();
            ExcelNPOIManager.ExportToExcel(rowList, fileName, sheetName);
        }

        private void MiExportSimpleExcel_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(gv);
        }

        private void MiExportGridList_Click(object sender, EventArgs e)
        {
            List<CarrierData> viewList = gridControl1.DataSource as List<CarrierData>;
            if (viewList == null)
            {
                return;
            }

            List<List<object>> content = new List<List<object>>();
            List<object> title = new List<object>();
            title.Add("序号");
            title.Add("栅格编号");
            title.Add("小区数");
            title.Add("最大场强");
            title.Add("平均场强");
            title.Add("最大SINR");
            title.Add("平均SINR");
            title.Add("中心经度");
            title.Add("中心纬度");
            content.Add(title);

            int idIndex = 0;
            foreach (CarrierData carrier in viewList)
            {
                foreach (AreaGridData area in carrier.AreaGridViews)
                {
                    foreach (SerialWeakGrid view in area.SerialGridViews)
                    {
                        foreach (WeakGrid grid in view.GridViews)
                        {
                            List<object> row = new List<object>();
                            row.Add(++idIndex);
                            row.Add(grid.MGRTIndex);
                            row.Add(grid.CellCount);
                            row.Add(grid.MaxRsrp);
                            row.Add(grid.AvgRsrp);
                            row.Add(grid.MaxSinr);
                            row.Add(grid.AvgSinr);
                            row.Add(grid.CentLng);
                            row.Add(grid.CentLat);
                            content.Add(row);
                        }
                    }
                }
            }

            ExcelNPOIManager.ExportToExcel(content);
        }

        private void MiExportDetailExcel_Click(object sender, EventArgs e)
        {
            List<NPOIRow> rowList = getNPOIRow();
            ExcelNPOIManager.ExportToExcel(rowList);
        }

        protected virtual List<NPOIRow> getNPOIRow()
        {
            List<NPOIRow> rowList = new List<NPOIRow>(carrierDataList.Count + 1);
            //row为标题
            NPOIRow titleRow = new NPOIRow();
            titleRow.AddCellValue("运营商名称");
            titleRow.AddCellValue("连续问题点数");

            titleRow.AddCellValue("区域名称");
            titleRow.AddCellValue("区域问题点数");

            titleRow.AddCellValue("序号");
            titleRow.AddCellValue("文件名");
            titleRow.AddCellValue("连续栅格号");
            titleRow.AddCellValue("连续栅格数");
            titleRow.AddCellValue("栅格最强电平");
            titleRow.AddCellValue("栅格最弱电平");
            titleRow.AddCellValue("栅格平均电平");
            titleRow.AddCellValue("栅格最强SINR");
            titleRow.AddCellValue("栅格最弱SINR");
            titleRow.AddCellValue("栅格平均SINR");

            titleRow.AddCellValue("栅格编号");
            titleRow.AddCellValue("小区数");
            titleRow.AddCellValue("小区平均电平");
            titleRow.AddCellValue("小区最强电平");
            titleRow.AddCellValue("小区平均SINR");
            titleRow.AddCellValue("小区最强SINR");
            titleRow.AddCellValue("中心经度");
            titleRow.AddCellValue("中心纬度");
            
            titleRow.AddCellValue("EARFCN");
            titleRow.AddCellValue("PCI");
            titleRow.AddCellValue("采样点数");
            titleRow.AddCellValue("采样点平均RSRP");
            titleRow.AddCellValue("采样点平均SINR");

            rowList.Add(titleRow);

            int index = 1;
            foreach (CarrierData item in carrierDataList)
            {
                NPOIRow row = new NPOIRow();
                fillRow(ref row, item, index);
                rowList.Add(row);
            }
            return rowList;
        }

        protected virtual void fillRow(ref NPOIRow row, CarrierData item, int index)
        {
            if (row == null || item == null)
                return;
            //添加一级数据
            row.AddCellValue(item.Name);
            row.AddCellValue(item.IssuesCount);
            foreach (var area in item.AreaGridViews)
            {
                //添加二级数据
                NPOIRow subRow = new NPOIRow();
                subRow.AddCellValue(area.AreaName);
                subRow.AddCellValue(area.IssuesCount);
                foreach (var serialGrid in area.SerialGridViews)
                {
                    //添加三级数据
                    NPOIRow thirdRow = new NPOIRow();
                    thirdRow.AddCellValue(index++);
                    thirdRow.AddCellValue(serialGrid.FileNames);
                    thirdRow.AddCellValue(serialGrid.WeakGridDesc);
                    thirdRow.AddCellValue(serialGrid.WeakGridCount);
                    thirdRow.AddCellValue(serialGrid.MaxRsrp);
                    thirdRow.AddCellValue(serialGrid.MinRsrp);
                    thirdRow.AddCellValue(serialGrid.AvgRsrp);
                    thirdRow.AddCellValue(serialGrid.MaxSinr);
                    thirdRow.AddCellValue(serialGrid.MinSinr);
                    thirdRow.AddCellValue(serialGrid.AvgSinr);
                    //添加四级数据
                    foreach (WeakGrid subItem in serialGrid.GridViews)
                    {
                        NPOIRow fourthRow = new NPOIRow();
                        fourthRow.AddCellValue(subItem.MGRTIndex);
                        fourthRow.AddCellValue(subItem.CellCount);
                        fourthRow.AddCellValue(subItem.AvgRsrp);
                        fourthRow.AddCellValue(subItem.MaxRsrp);
                        fourthRow.AddCellValue(subItem.AvgSinr);
                        fourthRow.AddCellValue(subItem.MaxSinr);
                        fourthRow.AddCellValue(subItem.CentLng);
                        fourthRow.AddCellValue(subItem.CentLat);
                        //添加五级数据
                        foreach (var thirdItem in subItem.CellGrid)
                        {
                            NPOIRow fifthRow = new NPOIRow();
                            fifthRow.AddCellValue(thirdItem.EARFCN);
                            fifthRow.AddCellValue(thirdItem.PCI);
                            fifthRow.AddCellValue(thirdItem.SampleCount);
                            fifthRow.AddCellValue(thirdItem.R0_RP);
                            fifthRow.AddCellValue(thirdItem.R0_CINR);
                            fourthRow.AddSubRow(fifthRow);
                        }
                        thirdRow.AddSubRow(fourthRow);
                    }
                    subRow.AddSubRow(thirdRow);
                }
                row.AddSubRow(subRow);
            }
        }

        private void MiExportShp_Click(object sender, EventArgs e)
        {
            SaveFileDialog saveFileDlg = new SaveFileDialog();
            saveFileDlg.Filter = FilterHelper.Shp;
            saveFileDlg.FilterIndex = 1;
            saveFileDlg.RestoreDirectory = true;
            saveFileDlg.Title = "另存为";
            if (saveFileDlg.ShowDialog() == DialogResult.OK)
            {
                string fileName = saveFileDlg.FileName;
                int expRet = layer.MakeShpFile_inject(fileName);
                if (expRet == 0)
                {
                    MessageBox.Show(this, "所选导出图层没有需要导出的数据！");
                }
                else if (expRet == 1)
                {
                    MessageBox.Show(this, "导出成功！");
                }
                else if (expRet == 2)
                {
                    //取消导出
                }
                else
                {
                    MessageBox.Show(this, "导出图层发生错误！");
                }
            }
        }

        protected override void ExportAllShp(string savePath)
        {
            string fileName = System.IO.Path.Combine(savePath, Desc + ".shp");
            if (System.IO.File.Exists(fileName))
            {
                System.IO.File.Delete(fileName);
            }
            string filedbf = System.IO.Path.Combine(savePath, Desc + ".dbf");
            if (System.IO.File.Exists(filedbf))
            {
                System.IO.File.Delete(filedbf);
            }
            string fileprj = System.IO.Path.Combine(savePath, Desc + ".prj");
            if (System.IO.File.Exists(fileprj))
            {
                System.IO.File.Delete(fileprj);
            }
            string fileshx = System.IO.Path.Combine(savePath, Desc + ".shx");
            if (System.IO.File.Exists(fileshx))
            {
                System.IO.File.Delete(fileshx);
            }

            if (layer == null)
            {
                mf = MainModel.MainForm.GetMapForm();
                MasterCom.MTGis.LayerBase clayer = mf.GetTempLayerBase(typeof(ZTScanGridLayer));
                layer = clayer as ZTScanGridLayer;
            }
            layer.GridInfos = scanGridInfoList;
            layer.MakeShpFile_inject(fileName);
        }
        #endregion
        
        private void GridView_DoubleClick(object sender, EventArgs e)
        {
            DevExpress.XtraGrid.Views.Grid.GridView curGv = sender as DevExpress.XtraGrid.Views.Grid.GridView;
            object row = curGv.GetRow(curGv.GetSelectedRows()[0]);
            if (row is WeakGrid)
            {
                WeakGrid grid = row as WeakGrid;
                MainModel.MainForm.GetMapForm().GoToView(grid.CentLng, grid.CentLat);
                layer.SelectedGrid = grid.CellGrid[0];
            }
            else if (row is SerialWeakGrid)
            {
                SerialWeakGrid view = row as SerialWeakGrid;
                WeakGrid grid = view.GridViews[view.GridViews.Count / 2];
                MainModel.MainForm.GetMapForm().GoToView(grid.CentLng, grid.CentLat);
            }
        }

        /// <summary>
        /// 图层跳转到数据所在位置
        /// </summary>
        protected virtual void GotoSelectedViewGV(SerialWeakGrid selectedData, int zoom)
        {
            double fLong = selectedData.GridViews[0].CentLng;
            double fLat = selectedData.GridViews[0].CentLat;
            if (zoom == 0)
            {
                MainModel.MainForm.GetMapForm().GoToView(fLong, fLat);
            }
            else
            {
                MainModel.MainForm.GetMapForm().GoToView(fLong, fLat, zoom);
            }
        }

        public override void DrawOnLayer()
        {
            mf = MainModel.MainForm.GetMapForm();
            MasterCom.MTGis.LayerBase clayer = mf.GetTempLayerBase(typeof(ZTScanGridLayer));
            layer = clayer as ZTScanGridLayer;
            layer.GridInfos = scanGridInfoList;
            layer.SelectedGrid = null;
            layer.InitColor(ZTScanGridLayer.RenderingIndex.R0_RP);
            layer.CurType = typeof(NbIotMgrsWeakRsrpResult);
            MainModel.FireSetDefaultMapSerialTheme(layer.SerialInfoName);
            mf.updateMap();
            if (carrierDataList[0].AreaGridViews.Count > 0 && carrierDataList[0].AreaGridViews[0].SerialGridViews.Count > 0)
            {
                GotoSelectedViewGV(carrierDataList[0].AreaGridViews[0].SerialGridViews[0], 0);
            }
        }

        private void gvSerialGrid_CustomDrawRowIndicator(object sender, DevExpress.XtraGrid.Views.Grid.RowIndicatorCustomDrawEventArgs e)
        {
            if (e.Info.IsRowIndicator && e.RowHandle > -1)
            {
                e.Info.DisplayText = (e.RowHandle + 1).ToString();
            }
        }

        public override void LayerDataClear()
        {
            MainModel.FireSetDefaultMapSerialTheme("");
            layer.GridInfos = null;
            layer.SelectedGrid = null;
        }
    }
}
