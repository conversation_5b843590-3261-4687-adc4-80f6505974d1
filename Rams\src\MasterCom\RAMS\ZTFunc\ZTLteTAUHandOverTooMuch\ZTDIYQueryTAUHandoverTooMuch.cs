﻿using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.ZTFunc;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.Net
{
    public class ZTDIYQueryTAUHandoverTooMuchLTE : ZTDIYQueryHandoverTooMuchLTE
    {
        private static ZTDIYQueryTAUHandoverTooMuchLTE intance = null;
        public new static ZTDIYQueryTAUHandoverTooMuchLTE GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new ZTDIYQueryTAUHandoverTooMuchLTE();
                    }
                }
            }
            return intance;
        }

        protected ZTDIYQueryTAUHandoverTooMuchLTE()
            : base()
        {
            MainModel.NeedType = true;
            ServiceTypes.Clear();
            ServiceTypes.AddRange(ServiceTypeManager.GetLteAllServiceTypes());
        }

        public override string Name
        {
            get
            {
                return "跟踪区频繁更新-按区域(LTE)";
            }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22064, this.Name);
        }

        protected override void initStatEventIDs()
        {
            statEventIDs.Clear();
            statEventIDs.Add(853);
        }
        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager fileDataManager in MainModel.DTDataManager.FileDataManagers)
            {
                fileDataManager.Events = filterEvents(fileDataManager.Events);
                List<Event> curHandoverEvents = new List<Event>();
                HandoverFileDataManager curHandoverFile = HandoverAndReselectionManager.GetHandoverTooMuchResult(fileDataManager,
                timeLimit, distanceLimit, handoverCount, curHandoverEvents);
                if (curHandoverFile.HandoverTimes > 0)
                {
                    curHandoverFile.fmnger.TestPoints = filterTestPointsByEvents(curHandoverFile.Events, fileDataManager.TestPoints);
                    HandoverAndReselectionManager.GetHandoverToMuchDetails(curHandoverFile, false);
                    curHandoverFile.Index = handoverFileList.Count + 1;
                    handoverFileList.Add(curHandoverFile);
                    handoverEvents.AddRange(curHandoverEvents);
                }

                if (!MainModel.IsBackground)
                {
                    fileDataManager.ClearDTDatas();
                    fileDataManager.ClearTestPoints();
                    dtFileDataBak.Add(fileDataManager);
                }
            }
        }

        protected override void fireShowForm()
        {
            object obj = MainModel.GetInstance().GetObjectFromBlackboard(typeof(ZTTAUHandoverTooMuchForm).FullName);
            ZTTAUHandoverTooMuchForm handoverTooMuchForm = obj == null ? null : obj as ZTTAUHandoverTooMuchForm;
            if (handoverTooMuchForm == null || handoverTooMuchForm.IsDisposed)
            {
                handoverTooMuchForm = new ZTTAUHandoverTooMuchForm(MainModel);
            }
            handoverTooMuchForm.showAnalyseInfo(dtFileDataBak, handoverFileList, handoverEvents,
                timeLimit, distanceLimit, handoverCount);
            if (!handoverTooMuchForm.Visible)
            {
                handoverTooMuchForm.Show(MainModel.MainForm);
            }
        }
    }

    public class ZTDIYQueryTAUHandoverTooMuchLTE_FDD : ZTDIYQueryTAUHandoverTooMuchLTE
    {
        private static ZTDIYQueryTAUHandoverTooMuchLTE_FDD instance = null;
        public static new ZTDIYQueryTAUHandoverTooMuchLTE_FDD GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new ZTDIYQueryTAUHandoverTooMuchLTE_FDD();
                    }
                }
            }
            return instance;
        }

        protected ZTDIYQueryTAUHandoverTooMuchLTE_FDD()
            : base()
        {
            carrierID = CarrierType.ChinaUnicom;
        }

        public override string Name
        {
            get
            {
                return "跟踪区频繁更新-按区域(LTE_FDD)";
            }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 26000, 26040, this.Name);
        }

        protected override void initStatEventIDs()
        {
            statEventIDs.Clear();
            statEventIDs.Add(3172);
        }
    }
}
