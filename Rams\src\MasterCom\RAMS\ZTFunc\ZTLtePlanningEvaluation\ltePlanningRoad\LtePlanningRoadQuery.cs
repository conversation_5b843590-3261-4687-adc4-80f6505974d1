﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Func;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class LtePlanningRoadQueryByFile : DIYReplayFileQuery
    {
        public LtePlanningRoadQueryByFile(MainModel mainModel)
            : base(mainModel)
        {
            IsAddSampleToDTDataManager = false;
            IsAddMessageToDTDataManager = false;
            isAutoLoadCQTPicture = false;
            queryer = LtePlanningRoadQueryer.Instance;
        }

        public override string Name
        {
            get { return queryer.Name; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return queryer.getRecLogItem();
        }

        protected override bool isValidCondition()
        {
            return queryer.IsValidCondition();
        }

        protected override DIYReplayContentOption getDIYReplayContent()
        {
            DIYReplayContentOption option = new DIYReplayContentOption();
            option.DefaultSerialThemeName = null;
            option.EventInclude = false;

            List<ColumnDefItem> cols = null;
            foreach (string item in queryer.Columns)
            {
                cols = InterfaceManager.GetInstance().GetColumnDefByShowName(item);
                option.SampleColumns.AddRange(cols);
            }

            return option;
        }

        protected override void queryReplayInfo(ClientProxy clientProxy, Package package, FileInfo fileInfo)
        {
            queryer.StartStatFile(fileInfo);
            base.queryReplayInfo(clientProxy, package, fileInfo);
            queryer.FinishStatFile();
        }

        protected override void doWithDTData(TestPoint tp)
        {
            queryer.DoWithTestPoint(tp);
        }

        protected override void doPostReplayAction()
        {
            queryer.GetResultAfterQuery();
        }

        protected override void fireShowResult()
        {
            queryer.FireShowForm();
        }

        private readonly LtePlanningRoadQueryer queryer;
    }

    public class LtePlanningRoadQueryByRegion : DIYAnalyseFilesOneByOneByRegion
    {
        public LtePlanningRoadQueryByRegion(MainModel mainModel)
            : base(mainModel)
        {
            IncludeEvent = false;
            FilterEventByRegion = false;
            FilterSampleByRegion = false;
            queryer = LtePlanningRoadQueryer.Instance;
            Columns = queryer.Columns;
        }

        public override string Name
        {
            get { return queryer.Name; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return queryer.getRecLogItem();
        }

        protected override bool getCondition()
        {
            return queryer.IsValidCondition();
        }

        protected override void fireShowForm()
        {
            queryer.FireShowForm();
        }

        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager fileDataManager in MainModel.DTDataManager.FileDataManagers)
            {
                queryer.StartStatFile(fileDataManager.GetFileInfo());
                List<TestPoint> testPointList = fileDataManager.TestPoints;
                foreach (TestPoint tp in testPointList)
                {
                    queryer.DoWithTestPoint(tp);
                }
                queryer.FinishStatFile();
            }
        }

        protected override void getResultsAfterQuery()
        {
            queryer.GetResultAfterQuery();
        }

        private readonly LtePlanningRoadQueryer queryer;
    }

    public class LtePlanningRoadQueryer
    {
        public static LtePlanningRoadQueryer Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new LtePlanningRoadQueryer();
                }
                return instance;
            }
        }

        public string Name
        {
            get { return "LTE规划站小区评估"; }
        }

        public List<string> Columns
        {
            get
            {
                return new List<string>()
                {
                    "isampleid",
                    "itime",
                    "ilongitude",
                    "ilatitude",
                    "lte_TAC",
                    "lte_ECI",
                    "lte_SCell_CI",
                    "lte_EARFCN",
                    "lte_PCI",
                    "lte_RSRQ",
                    "lte_RSRP",
                    "lte_RSSI",
                    "lte_SINR",
                    "lte_NCell_EARFCN",
                    "lte_NCell_PCI",
                    "lte_NCell_RSRP",
                    "lte_NCell_RSRQ",
                    "lte_NCell_RSSI",
                    "lte_NCell_SINR",
                    "lte_PDCP_DL",
                };
            }
        }

        public MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22029, "LTE规划路段评估");
        }

        public bool IsValidCondition()
        {
            if (setForm == null || setForm.IsDisposed)
            {
                setForm = new LtePlanningRoadSettingForm();
            }
            if (setForm.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            cond = setForm.GetCondition();

            // load Excel
            try
            {
                if (!LtePlanningInfoManager.Instance.LoadFromXls(cond.XlsFileName))
                {
                    return false;
                }
            }
            catch (System.Exception ex)
            {
                MessageBox.Show(ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }

            evaluators.Clear();
            evaluators.Add(new WeakCoverageRoadEvaluator(this.cond));
            evaluators.Add(new MultiCoverageRoadEvaluator(this.cond));
            return true;
        }

        public void StartStatFile(FileInfo fInfo)
        {
            foreach (LtePlanningRoadEvaluatorBase eva in evaluators)
            {
                if (eva.Enable)
                {
                    eva.StartStatFile(fInfo);
                }
            }
        }

        public void DoWithTestPoint(TestPoint tp)
        {
            if (tp.Longitude == 0 || tp.Latitude == 0)
            {
                return;
            }
            foreach (LtePlanningRoadEvaluatorBase eva in evaluators)
            {
                if (eva.Enable)
                {
                    eva.DoWithTestPoint(tp);
                }
            }
        }

        public void FinishStatFile()
        {
            foreach (LtePlanningRoadEvaluatorBase eva in evaluators)
            {
                if (eva.Enable)
                {
                    eva.FinishStatFile();
                }
            }
        }

        public void GetResultAfterQuery()
        {
            foreach (LtePlanningRoadEvaluatorBase eva in evaluators)
            {
                if (eva.Enable)
                {
                    eva.CalcResult();
                }
            }
        }

        public void FireShowForm()
        {
            List<object> result = new List<object>();
            foreach (LtePlanningRoadEvaluatorBase eva in evaluators)
            {
                if (eva.Enable)
                {
                    result.Add(eva.GetResult());
                }
            }

            foreach (LtePlanningRoadEvaluatorBase eva in evaluators)
            {
                if (eva.Enable)
                {
                    eva.Clear();
                }
            }

            LtePlanningRoadResultForm resultForm = mainModel.GetObjectFromBlackboard(typeof(LtePlanningRoadResultForm).FullName) as LtePlanningRoadResultForm;
            if (resultForm == null || resultForm.IsDisposed)
            {
                resultForm = new LtePlanningRoadResultForm(mainModel);
            }
            resultForm.FillData(result);
            if (!resultForm.Visible)
            {
                resultForm.Show(mainModel.MainForm);
            }
        }

        private LtePlanningRoadQueryer()
        {
            this.mainModel = MainModel.GetInstance();
            this.evaluators = new List<LtePlanningRoadEvaluatorBase>();
        }

        private readonly MainModel mainModel;
        private LtePlanningRoadSettingForm setForm;
        private LtePlanningRoadCondition cond { get; set; }
        private readonly List<LtePlanningRoadEvaluatorBase> evaluators;

        private static LtePlanningRoadQueryer instance;
    }
}
