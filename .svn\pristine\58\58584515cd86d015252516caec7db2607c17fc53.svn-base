﻿namespace MasterCom.RAMS.ZTFunc.ZTSINR.WeakSINRReason
{
    partial class ReasonPnlOverCover
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.label3 = new System.Windows.Forms.Label();
            this.label1 = new System.Windows.Forms.Label();
            this.numMainRsrp = new DevExpress.XtraEditors.SpinEdit();
            this.label2 = new System.Windows.Forms.Label();
            this.numOverCoverFactor = new DevExpress.XtraEditors.SpinEdit();
            ((System.ComponentModel.ISupportInitialize)(this.grp)).BeginInit();
            this.grp.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numMainRsrp.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numOverCoverFactor.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // grp
            // 
            this.grp.Controls.Add(this.numOverCoverFactor);
            this.grp.Controls.Add(this.numMainRsrp);
            this.grp.Controls.Add(this.label1);
            this.grp.Controls.Add(this.label2);
            this.grp.Controls.Add(this.label3);
            this.grp.Size = new System.Drawing.Size(606, 68);
            this.grp.Text = "过覆盖";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(36, 37);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(71, 12);
            this.label3.TabIndex = 0;
            this.label3.Text = "主服RSRP ≥";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(286, 37);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(65, 12);
            this.label1.TabIndex = 1;
            this.label1.Text = "过覆盖系数";
            // 
            // numMainRsrp
            // 
            this.numMainRsrp.EditValue = new decimal(new int[] {
            100,
            0,
            0,
            -2147483648});
            this.numMainRsrp.Location = new System.Drawing.Point(112, 32);
            this.numMainRsrp.Name = "numMainRsrp";
            this.numMainRsrp.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numMainRsrp.Properties.MaxValue = new decimal(new int[] {
            25,
            0,
            0,
            0});
            this.numMainRsrp.Properties.MinValue = new decimal(new int[] {
            141,
            0,
            0,
            -2147483648});
            this.numMainRsrp.Size = new System.Drawing.Size(75, 21);
            this.numMainRsrp.TabIndex = 2;
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(193, 37);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(17, 12);
            this.label2.TabIndex = 0;
            this.label2.Text = "dB";
            // 
            // numOverCoverFactor
            // 
            this.numOverCoverFactor.EditValue = new decimal(new int[] {
            15,
            0,
            0,
            65536});
            this.numOverCoverFactor.Location = new System.Drawing.Point(357, 32);
            this.numOverCoverFactor.Name = "numOverCoverFactor";
            this.numOverCoverFactor.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numOverCoverFactor.Properties.Increment = new decimal(new int[] {
            1,
            0,
            0,
            65536});
            this.numOverCoverFactor.Properties.MaxValue = new decimal(new int[] {
            20,
            0,
            0,
            0});
            this.numOverCoverFactor.Size = new System.Drawing.Size(75, 21);
            this.numOverCoverFactor.TabIndex = 2;
            // 
            // ReasonPnlOverCover
            // 
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.Name = "ReasonPnlOverCover";
            this.Size = new System.Drawing.Size(606, 68);
            ((System.ComponentModel.ISupportInitialize)(this.grp)).EndInit();
            this.grp.ResumeLayout(false);
            this.grp.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numMainRsrp.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numOverCoverFactor.Properties)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label1;
        private DevExpress.XtraEditors.SpinEdit numOverCoverFactor;
        private DevExpress.XtraEditors.SpinEdit numMainRsrp;
        private System.Windows.Forms.Label label2;
    }
}
