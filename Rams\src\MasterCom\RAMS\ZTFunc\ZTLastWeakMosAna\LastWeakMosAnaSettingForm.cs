﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc.ZTLastWeakMosAna
{
    public partial class LastWeakMosAnaSettingForm : MinCloseForm
    {
        public LastWeakMosAnaSettingForm()
        {
            InitializeComponent();

            this.spinEditTPNum.Value = 2;

            this.comboBoxMOS.Items.Add("2.8");
            this.comboBoxMOS.Items.Add("3.0");

            this.spinEditTPNum.ValueChanged += spinEditTPNum_ValueChanged;
        }

        void spinEditTPNum_ValueChanged(object sender, EventArgs e)
        {
            double d = (double)this.spinEditTPNum.Value;
            if (d > 9999) d = 9999;
            if (d < 0) d = 0;
            d = Math.Round(d, 0);
            this.spinEditTPNum.Value = (decimal)d;
        }

        private void buttonOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = System.Windows.Forms.DialogResult.OK;
        }

        private void buttonCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = System.Windows.Forms.DialogResult.Cancel;
        }
        public void SetValue(EWeakMosEventType eWeakMosEventType, int tpNum)
        {
            this.comboBoxMOS.SelectedIndex = (int)eWeakMosEventType;
            this.spinEditTPNum.Value = tpNum;
        }
        public void GetResult(out EWeakMosEventType eWeakMosEventType, out int tpNum)
        {
            eWeakMosEventType = (EWeakMosEventType)this.comboBoxMOS.SelectedIndex;
            tpNum = (int)this.spinEditTPNum.Value;
        }
    }
}
