﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Xml;
using MasterCom.Util;

namespace MasterCom.ES.Core
{
    public class ProcRoutine
    {
        public static Dictionary<string, bool> PathExistDic { get; set; } = new Dictionary<string, bool>();
        public ProcRoutine()
        {

        }
        public string Name { get; set; }
        public NodeEntry RootNode { get; set; }
        public ResvStore ReservStore
        {
            get
            {
                return resvStore;
            }
        }
        public bool InProcModule { get; set; } = false;
        public bool IsDirty { get; set; } = false;
        private readonly ResvStore resvStore = new ResvStore();
        public List<NodeEntry> _HoverNodes { get; set; } = new List<NodeEntry>();
        //
        public static XmlElement AddItem(XmlConfigFile configFile, XmlElement config, string name, object value)
        {
            if (value is ProcRoutine)
            {
                XmlElement item = configFile.AddItem(config, name, value.GetType());
                NodeEntry node = (value as ProcRoutine).RootNode;
                configFile.AddItem(item, "RootNode", node.Param);
                return item;
            }
            return null;
        }
        public static Dictionary<int, NodeEntry> TempKeyInRoutineDic { get; set; } = new Dictionary<int, NodeEntry>(); 
        public static object GetItemValue(XmlConfigFile configFile, XmlElement item, string typeName)
        {
            if (typeName.Equals(typeof(ProcRoutine).Name))
            {
                TempKeyInRoutineDic.Clear();
                Dictionary<string, object> param = configFile.GetItemValue(item, "RootNode") as Dictionary<string, object>;
                NodeEntry node = new NodeEntry();
                node.Param = param;
                return node;
            }
            return null;
        }

        internal void ResetIdx()
        {
            RootNode.ClearIdx();
            RootNode.ResetIdx(1);
            PathExistDic.Clear();
        }
    }
}
