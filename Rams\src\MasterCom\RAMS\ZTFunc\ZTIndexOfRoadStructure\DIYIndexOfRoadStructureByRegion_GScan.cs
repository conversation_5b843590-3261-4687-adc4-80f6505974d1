﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Grid;

namespace MasterCom.RAMS.ZTFunc
{
    public class DIYIndexOfRoadStructureByRegion_GScan : DIYSampleByRegion
    {
        public MapFormItemSelection ItemSelection { get; set; }
        QueryCondition curConditionDT { get; set; }
        QueryCondition curConditionScan { get; set; }
        int rxLevDValue = 12;
        int totalBandCount900 = 95;
        int totalBandCount1800 = 125;
        GridMatrix<GridFormula> qualityGridMatrix;
        readonly Dictionary<BTSBandType, Dictionary<string, GridForIndexOfRoadStructure>> gridDic = new Dictionary<BTSBandType, Dictionary<string, GridForIndexOfRoadStructure>>();
        public DIYIndexOfRoadStructureByRegion_GScan(MainModel mainModel)
            : base(mainModel)
        {
            isAddSampleToDTDataManager = false;
        }

        public override string Name
        {
            get { return "道路结构指数"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 15000, 15035, this.Name);//////
        }

        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            List<object> columnsDef = new List<object>();

            Dictionary<string, object> param = new Dictionary<string, object>();
            param["param_name"] = "GSCAN_RxLev";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "GSCAN_BCCH";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "GSCAN_BSIC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            Dictionary<string, object> tmpDic = new Dictionary<string, object>();
            tmpDic.Add("name", (object)"GSM_SCAN_RxLev");
            tmpDic.Add("themeName", (object)"GSM_SCAN_RxLev");
            tmpDic.Add("columnsDef", (object)columnsDef);

            DIYSampleGroup group = new DIYSampleGroup();
            group.Param = tmpDic;
            return group;
        }

        protected override void FireShowFormAfterQuery()
        {
            doRelatedTo2GQuality();
            if (MainModel.GridForIndexOfRoadStructureDic.Count == 0)
            {
                System.Windows.Forms.MessageBox.Show("没有符合条件的数据。");
                return;
            }
            MainModel.MainForm.FireIndexOfRoadStructureForm();
            MainModel.FireSetDefaultMapSerialTheme("GSM_SCAN_RxLev");
        }

        IndexOfRoadStructureDlg conditionDlg = null;
        protected override bool getConditionBeforeQuery()
        {
            SetQueryFormQualRelateMultiCov setCondiForm = new SetQueryFormQualRelateMultiCov(MainModel, ItemSelection, Condition);
            if (setCondiForm.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            curConditionDT = setCondiForm.CurConditionDT;
            curConditionScan = setCondiForm.CurConditionScan;
            this.condition = curConditionScan;

            if (conditionDlg == null)
            {
                conditionDlg = new IndexOfRoadStructureDlg();
            }
            if (conditionDlg.ShowDialog() == DialogResult.OK)
            {
                conditionDlg.GetCondition(ref rxLevDValue, ref totalBandCount900, ref totalBandCount1800);
                return true;
            }
            return false;
        }

        protected override bool isValidTestPoint(TestPoint tp)
        {
            if (tp is ScanTestPoint_G)
            {
                return base.isValidTestPoint(tp);
            }
            return false;
        }

        protected override void doWithDTData(MasterCom.RAMS.Model.TestPoint tp)
        {
            List<int> lst900;
            List<int> lst1800;
            GScanTestPointSplitter.Split(tp, out lst900, out lst1800);
            anaSpecificBand(tp, BTSBandType.GSM900, lst900);
            anaSpecificBand(tp, BTSBandType.DSC1800, lst1800);
        }

        private void anaSpecificBand(TestPoint tp, BTSBandType bandType, List<int> idxOfBand)
        {
            if (idxOfBand.Count == 0)
            {
                return;
            }
            int mainIdx = idxOfBand[0];

            float? rxLevMain = (float?)tp["GSCAN_RxLev", mainIdx];
            if (rxLevMain == null || rxLevMain < -120 || rxLevMain > -10)
            {
                return;
            }
            int? bcchMain = (int?)tp["GSCAN_BCCH", mainIdx];
            int? bsicMain = (int?)tp["GSCAN_BSIC", mainIdx];

            Cell cellMain = tp.GetCell_GSMScan(mainIdx);
            List<CellOfSampleForIndexOfRoadStructure> cellList = new List<CellOfSampleForIndexOfRoadStructure>();   //采样点扫到的信号信息
            CellOfSampleForIndexOfRoadStructure cellOfSampleMain = new CellOfSampleForIndexOfRoadStructure();
            if (cellMain != null)
            {
                cellOfSampleMain.CellName = cellMain.Name;
                cellOfSampleMain.TCHCount = cellMain.TCH.Count;
            }
            else
            {
                cellOfSampleMain.CellName = "未知小区";
            }
            cellOfSampleMain.BCCH = (int)bcchMain;
            cellOfSampleMain.BSIC = (int)bsicMain;
            cellOfSampleMain.RxLev = (float)rxLevMain;
            cellList.Add(cellOfSampleMain);

            int sum = getSumTCH(tp, idxOfBand, rxLevMain, cellList);
            if (sum == 0)
            {
                return;
            }

            int totalBandCount = bandType == BTSBandType.GSM900 ? totalBandCount900 : totalBandCount1800;
            double indexStructure = Math.Round(1.0 * sum / totalBandCount, 4);
            SampleForIndexOfRoadStructure sample = new SampleForIndexOfRoadStructure();
            sample.FileName = tp.FileName;
            sample.Longitude = tp.Longitude;
            sample.Latitude = tp.Latitude;
            sample.BandType = bandType;
            sample.IndexOfStructure = indexStructure;
            sample.CellList = cellList;

            double longitude = getGridTLLong(tp.Longitude);
            double latitude = getGridTLLat(tp.Latitude);
            string key = longitude + "|" + latitude;
            if (!gridDic.ContainsKey(bandType))
            {
                Dictionary<string, GridForIndexOfRoadStructure> tmpDic = new Dictionary<string, GridForIndexOfRoadStructure>();
                gridDic[bandType] = tmpDic;
            }
            if (!gridDic[bandType].ContainsKey(key))
            {
                GridForIndexOfRoadStructure grid = new GridForIndexOfRoadStructure(longitude, latitude);
                gridDic[bandType][key] = grid;
            }
            gridDic[bandType][key].AddTestPoint(sample);
        }

        private int getSumTCH(TestPoint tp, List<int> idxOfBand, float? rxLevMain, List<CellOfSampleForIndexOfRoadStructure> cellList)
        {
            int sum = 0;
            for (int i = 1; i < idxOfBand.Count; i++)
            {
                int idx = idxOfBand[i];
                float? rxLev = (float?)tp["GSCAN_RxLev", idx];
                if (rxLev == null || rxLev < -120 || rxLev > -10)
                {
                    break;
                }
                int? bcch = (int?)tp["GSCAN_BCCH", idx];
                int? bsic = (int?)tp["GSCAN_BSIC", idx];
                Cell cell = tp.GetCell_GSMScan(idx);
                if (rxLevMain - rxLev <= rxLevDValue)
                {
                    if (cell != null)
                    {
                        sum += cell.TCH.Count;
                    }
                    addCellList(cellList, rxLev, bcch, bsic, cell);
                }
                else
                {
                    break;
                }
            }

            return sum;
        }

        private static void addCellList(List<CellOfSampleForIndexOfRoadStructure> cellList, float? rxLev, int? bcch, int? bsic, Cell cell)
        {
            CellOfSampleForIndexOfRoadStructure cellOfSample = new CellOfSampleForIndexOfRoadStructure();
            if (cell != null)
            {
                cellOfSample.CellName = cell.Name;
                cellOfSample.TCHCount = cell.TCH.Count;
            }
            else
            {
                cellOfSample.CellName = "未知小区";
            }
            cellOfSample.BCCH = (int)bcch;
            cellOfSample.BSIC = (int)bsic;
            cellOfSample.RxLev = (float)rxLev;
            cellList.Add(cellOfSample);
        }

        protected override void getResultAfterQuery()
        {
            MainModel.GridForIndexOfRoadStructureDic.Clear();
            foreach (BTSBandType bandType in gridDic.Keys)
            {
                if (!MainModel.GridForIndexOfRoadStructureDic.ContainsKey(bandType))
                {
                    List<GridForIndexOfRoadStructure> tmpList = new List<GridForIndexOfRoadStructure>();
                    MainModel.GridForIndexOfRoadStructureDic[bandType] = tmpList;
                }
                foreach (GridForIndexOfRoadStructure grid in gridDic[bandType].Values)
                {
                    MainModel.GridForIndexOfRoadStructureDic[bandType].Add(grid);
                }
            }
            gridDic.Clear();
        }

        protected double getGridTLLong(double longitude)
        {
            int tllongitude = (int)(longitude * 10000000) / 4000 * 4000;
            return tllongitude / 10000000.0D;
        }
        protected double getGridTLLat(double latitude)
        {
            int tllatitude = (int)(latitude * 10000000) / 3600 * 3600 + 3600;
            return tllatitude / 10000000.0D;
        }

        private void doRelatedTo2GQuality()
        {
            if (MainModel.GridForIndexOfRoadStructureDic.Count <= 0)
            {
                return;
            }
            query2GQualityGrid();
            addQualityToRoadStructureDic();
        }

        private void addQualityToRoadStructureDic()
        {
            foreach (BTSBandType band in MainModel.GridForIndexOfRoadStructureDic.Keys)
            {
                foreach (GridForIndexOfRoadStructure gfios in MainModel.GridForIndexOfRoadStructureDic[band])
                {
                    int rAt, cAt;
                    GridHelper.GetIndexOfDefaultSizeGrid(gfios.MidLongitude, gfios.MidLatitude, out rAt, out cAt);
                    GridFormula qg = qualityGridMatrix[rAt, cAt];
                    if (qg == null || qg.formulaValueDic.Count <= 0)
                    {
                        gfios.rxqualityDic["Mx_5A010501"] = 0;
                        gfios.rxqualityDic["Mx_5A010502"] = 0;
                        gfios.rxqualityDic["Mx_5A010503"] = 0;
                        gfios.rxqualityDic["Mx_5A010504"] = 0;
                        gfios.rxqualityDic["Mx_5A010505"] = 0;
                        gfios.rxqualityDic["Mx_5A010506"] = 0;
                        gfios.rxqualityDic["Mx_5A010507"] = 0;
                        gfios.rxqualityDic["Mx_5A010508"] = 0;
                    }
                    else if (band == BTSBandType.GSM900)
                    {
                        gfios.rxqualityDic["Mx_5A010501"] = qg.formulaValueDic["Mx_5A01011E01"];
                        gfios.rxqualityDic["Mx_5A010502"] = qg.formulaValueDic["Mx_5A01011E02"];
                        gfios.rxqualityDic["Mx_5A010503"] = qg.formulaValueDic["Mx_5A01011E03"];
                        gfios.rxqualityDic["Mx_5A010504"] = qg.formulaValueDic["Mx_5A01011E04"];
                        gfios.rxqualityDic["Mx_5A010505"] = qg.formulaValueDic["Mx_5A01011E05"];
                        gfios.rxqualityDic["Mx_5A010506"] = qg.formulaValueDic["Mx_5A01011E06"];
                        gfios.rxqualityDic["Mx_5A010507"] = qg.formulaValueDic["Mx_5A01011E07"];
                        gfios.rxqualityDic["Mx_5A010508"] = qg.formulaValueDic["Mx_5A01011E08"];
                    }
                    else if (band == BTSBandType.DSC1800)
                    {
                        gfios.rxqualityDic["Mx_5A010501"] = qg.formulaValueDic["Mx_5A01021E01"];
                        gfios.rxqualityDic["Mx_5A010502"] = qg.formulaValueDic["Mx_5A01021E02"];
                        gfios.rxqualityDic["Mx_5A010503"] = qg.formulaValueDic["Mx_5A01021E03"];
                        gfios.rxqualityDic["Mx_5A010504"] = qg.formulaValueDic["Mx_5A01021E04"];
                        gfios.rxqualityDic["Mx_5A010505"] = qg.formulaValueDic["Mx_5A01021E05"];
                        gfios.rxqualityDic["Mx_5A010506"] = qg.formulaValueDic["Mx_5A01021E06"];
                        gfios.rxqualityDic["Mx_5A010507"] = qg.formulaValueDic["Mx_5A01021E07"];
                        gfios.rxqualityDic["Mx_5A010508"] = qg.formulaValueDic["Mx_5A01021E08"];
                    }
                }
            }
        }

        private void query2GQualityGrid()
        {
            DIYQueryFormulaInGridByRegion queryQuality = new DIYQueryFormulaInGridByRegion(MainModel);
            List<string> formulaList = addFormulas();//查询指标 rxquality 0 - 7
            StringBuilder sbuilder = new StringBuilder();
            for(int index=0;index<formulaList.Count;index++)
            {
                sbuilder.Append(formulaList[index]);
                if (index < formulaList.Count - 1)
                {
                    sbuilder.Append("+");
                }
            }
            queryQuality.setQueryFormulas(formulaList, sbuilder.ToString());
            queryQuality.SetQueryCondition(curConditionDT);
            queryQuality.Query();
            qualityGridMatrix = queryQuality.formulaGridMatrix;
        }

        private List<string> addFormulas()
        {
            List<string> formulaList = new List<string>();

            //900
            formulaList.Add("Mx_5A01011E01");
            formulaList.Add("Mx_5A01011E02");
            formulaList.Add("Mx_5A01011E03");
            formulaList.Add("Mx_5A01011E04");
            formulaList.Add("Mx_5A01011E05");
            formulaList.Add("Mx_5A01011E06");
            formulaList.Add("Mx_5A01011E07");
            formulaList.Add("Mx_5A01011E08");
            //1800
            formulaList.Add("Mx_5A01021E01");
            formulaList.Add("Mx_5A01021E02");
            formulaList.Add("Mx_5A01021E03");
            formulaList.Add("Mx_5A01021E04");
            formulaList.Add("Mx_5A01021E05");
            formulaList.Add("Mx_5A01021E06");
            formulaList.Add("Mx_5A01021E07");
            formulaList.Add("Mx_5A01021E08");
            return formulaList;
        }

        public static double GRID_SPAN_LONG { get; set; } = CD.ATOM_SPAN_LONG;//当前运算中使用的值
        public static double GRID_SPAN_LAT { get; set; } = CD.ATOM_SPAN_LAT;//当前运算中使用的值
        //
        /// <summary>
        /// 获取所在矩阵中的位置 【rAt，cAt】 
        /// </summary>
        /// <param name="longitude"></param>
        /// <param name="latitude"></param>
        /// <param name="bounds"></param>
        /// <param name="xAt"></param>
        /// <param name="yAt"></param>
        protected void getInPlace(double longitude, double latitude, MasterCom.MTGis.DbRect bounds, out int rAt, out int cAt)
        {
            double xDis = (longitude) - getGridTLLong(bounds.x1);
            cAt = (int)(Math.Round(xDis / GRID_SPAN_LONG));
            double yDis = getGridTLLat(bounds.y2) - latitude;
            rAt = (int)(Math.Round(yDis / GRID_SPAN_LAT));
        }
    }

    public class GridForIndexOfRoadStructure
    {
        public GridForIndexOfRoadStructure(double longitude, double latitude)
        {
            this.ltLongitude = longitude;
            this.ltLatitude = latitude;

            initRxquality();
        }
        private readonly double ltLongitude;
        public double LTLongitude
        {
            get { return ltLongitude; }
        }
        private readonly double ltLatitude;
        public double LTLatitude
        {
            get { return ltLatitude; }
        }

        public double MidLongitude
        {
            get { return ltLongitude + 0.0002; }
        }
        public double MidLatitude
        {
            get { return ltLatitude - 0.00018; }
        }

        public double BRLongitude
        {
            get { return ltLongitude + 0.0004; }
        }
        public double BRLatitude
        {
            get { return ltLatitude - 0.00036; }
        }

        public double IndexOfStructure
        {
            get
            {
                double sum = 0;
                foreach (SampleForIndexOfRoadStructure sample in SampleList)
                {
                    sum += sample.IndexOfStructure;
                }
                return Math.Round(sum / SampleList.Count, 4);
            }
        }

        public Dictionary<string, double> rxqualityDic { get; set; } = new Dictionary<string, double>();

        public List<SampleForIndexOfRoadStructure> SampleList { get; set; } = new List<SampleForIndexOfRoadStructure>();

        public void AddTestPoint(SampleForIndexOfRoadStructure sample)
        {
            SampleList.Add(sample);
        }

        internal bool Within(double x1, double y1, double x2, double y2)
        {
            foreach (SampleForIndexOfRoadStructure tp in SampleList)
            {
                if (tp.Longitude >= x1 && tp.Longitude <= x2 && tp.Latitude >= y1 && tp.Latitude <= y2)
                {
                    return true;
                }
            }
            return false;
        }

        private void initRxquality()
        {
            rxqualityDic["Mx_5A010501"] = 0;
            rxqualityDic["Mx_5A010502"] = 0;
            rxqualityDic["Mx_5A010503"] = 0;
            rxqualityDic["Mx_5A010504"] = 0;
            rxqualityDic["Mx_5A010505"] = 0;
            rxqualityDic["Mx_5A010506"] = 0;
            rxqualityDic["Mx_5A010507"] = 0;
            rxqualityDic["Mx_5A010508"] = 0;
        }

        public override bool Equals(object obj)
        {
            if (obj is GridForIndexOfRoadStructure)
            {
                GridForIndexOfRoadStructure other = obj as GridForIndexOfRoadStructure;
                if (this.ltLongitude == other.ltLongitude && this.ltLatitude == other.ltLatitude)
                {
                    return true;
                }
            }
            return false;
        }

        public override int GetHashCode()
        {
            return (LTLongitude + "_" + LTLatitude).GetHashCode();
        }
    }

    public class SampleForIndexOfRoadStructure
    {
        public string FileName { get; set; }
        public double Longitude { get; set; }
        public double Latitude { get; set; }
        public BTSBandType BandType { get; set; }
        public double IndexOfStructure { get; set; }
        public string SampleInfo { get; set; }
        public List<CellOfSampleForIndexOfRoadStructure> CellList { get; set; } = new List<CellOfSampleForIndexOfRoadStructure>();

        public void AddCell(CellOfSampleForIndexOfRoadStructure cell)
        {
            CellList.Add(cell);
        }
    }

    public class CellOfSampleForIndexOfRoadStructure
    {
        public string CellName { get; set; } = "";
        public int BCCH { get; set; }
        public int BSIC { get; set; }
        public int TCHCount { get; set; }
        private float rxLev;
        public float RxLev
        {
            get { return (float)Math.Round(rxLev, 2); }
            set { rxLev = value; }
        }
    }
}
