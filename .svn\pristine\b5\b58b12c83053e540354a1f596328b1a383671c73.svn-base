﻿using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc.ZTHighReverseFlowCoverage._45GHighReverseFlowCoverage;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTHighReverseFlowCoverage
{
    public class CellParamInfo
    {
        #region 基础参数 从工参表获取
        public string Province { get; protected set; }
        public int ProvinceID { get; protected set; }
        public string City { get; protected set; }
        public int CityID { get; protected set; }
        public string Region { get; protected set; }
        public string Grid { get; protected set; }
        public int Enodebid { get; protected set; }
        public string CellName { get; protected set; }
        public string Type { get; protected set; }
        public int TAC { get; protected set; }
        public long CI { get; protected set; }

        //public int CellLongitude { get; protected set; }
        //public int CellLatitude { get; protected set; }

        //public int BtsLongitude { get; protected set; }
        //public int BtsLatitude { get; protected set; }

        //public double CellLongitudeD { get; protected set; }
        //public double CellLatitudeD { get; protected set; }

        public LongitudeLatitude Cell { get; set; } = new LongitudeLatitude();
        public LongitudeLatitude Bts { get; set; } = new LongitudeLatitude();
        #endregion

        #region 根据参数计算获得
        public string TAC16 { get; protected set; }
        public string CI16 { get; protected set; }
        #endregion

        public bool HasErr { get; set; }
        public string ErrMsg { get; set; } = null;

        /// <summary>
        /// 编号 根据工参计算获得
        /// </summary>
        public string Number { get; set; }
        public bool IsConnectToLte { get; set; }

        public CellParamInfo ParentCell { get; set; } = null;

        public string Key { get; protected set; }

        public virtual void Calculate()
        {
            TAC16 = $"{TAC:X}";
            CI16 = $"{CI:X}";
        }

        public virtual void FillDataBySQL(Package package)
        {
            Province = package.Content.GetParamString();
            ProvinceID = package.Content.GetParamInt();
            City = package.Content.GetParamString();
            CityID = package.Content.GetParamInt();
            Region = package.Content.GetParamString();
            Grid = package.Content.GetParamString();
            Enodebid = package.Content.GetParamInt();
            CellName = package.Content.GetParamString();
            Type = package.Content.GetParamString();
            TAC = package.Content.GetParamInt();
            CI = package.Content.GetParamInt64();

            Cell.FillIntDataWithTransfer(package);
            Bts.FillIntData(package);

            //CellLongitude = package.Content.GetParamInt();
            //CellLongitudeD = CellLongitude / 10000000d;
            //CellLatitude = package.Content.GetParamInt();
            //CellLatitudeD = CellLatitude / 10000000d;

            //BtsLongitude = package.Content.GetParamInt();
            //BtsLatitude = package.Content.GetParamInt();

            Key = SerialNumberHelper.GetKey(this);

            Calculate();
        }

        public virtual void FillDataByExcel(System.Data.DataRow dr)
        {
            Province = getValid(dr, "省份");
            ProvinceID = getValidIntData(getValid(dr, "省份ID"));
            City = getValid(dr, "地市");
            CityID = getValidIntData(getValid(dr, "地市ID"));
            Region = getValid(dr, "区域");
            Grid = getValid(dr, "网格");
            Enodebid = getValidIntData(getValid(dr, "基站号"));
            CellName = getValid(dr, "小区名");
            Type = getValid(dr, "覆盖类型");
            TAC = getValidIntData(getValid(dr, "TAC"));
            CI = getValidLongData(getValid(dr, "CI"));
            Cell.ILongitude = getValidIntData(getValid(dr, "celllongitude"));
            Cell.ILatitude = getValidIntData(getValid(dr, "celllatitude"));
            Bts.ILongitude = getValidIntData(getValid(dr, "btslongitude"));
            Bts.ILatitude = getValidIntData(getValid(dr, "btslatitude"));
        }

        protected string getValid(System.Data.DataRow row, string name)
        {
            return row[name]?.ToString().Trim() ?? "";
        }

        protected int getValidIntData(string str)
        {
            if (int.TryParse(str, out int res))
            {
                return res;
            }
            return 0;
        }

        protected long getValidLongData(string str)
        {
            if (long.TryParse(str, out long res))
            {
                return res;
            }
            return 0;
        }

        public double GetDistance(CellParamInfo info)
        {
            return Cell.GetDistance(info.Cell);
        }
    }

    public class NRCellInfo : CellParamInfo
    {
        //用于区分700M和2.6G
        public FreqBand FreqBandType { get; set; } = FreqBand.Unknown;

        public enum FreqBand
        {
            Unknown,
            _700M,
            _2600M
        }

        public override void FillDataByExcel(System.Data.DataRow dr)
        {
            base.FillDataByExcel(dr);
            string type = getValid(dr, "频段");
            type = type.ToUpper();
            if (type == "2.6GHZ")
            {
                FreqBandType = FreqBand._2600M;
            }
            else if (type == "700MHZ")
            {
                FreqBandType = FreqBand._700M;
            }
        }
    }

    public class LTECellInfo : CellParamInfo
    {
        public double Distance { get; set; }
    }
}
