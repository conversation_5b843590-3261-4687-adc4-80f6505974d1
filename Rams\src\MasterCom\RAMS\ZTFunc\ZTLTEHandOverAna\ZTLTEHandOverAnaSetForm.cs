﻿using System;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTLTEHandOverAnaSetForm : BaseDialog
    {
        public ZTLTEHandOverAnaSetForm()
        {
            InitializeComponent();
        }

        public ZTLTEHandOverAnaCondition GetCondition()
        {
            ZTLTEHandOverAnaCondition condition = new ZTLTEHandOverAnaCondition();
            condition.BeforeSecond = Math.Round((double)numBeforeSecond.Value,3);
            condition.AfterSecond = Math.Round((double)numAfterSecond.Value,3);
            condition.SiteDistance = (int)numSiteDistance.Value;
            condition.IsBusiness = chkBusiness.Checked;
            return condition;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        private void ZTLTEHandOverAnaSetForm_Load(object sender, EventArgs e)
        {
            //
        }
    }
}
