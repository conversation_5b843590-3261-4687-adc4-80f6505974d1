﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Drawing;

namespace MasterCom.RAMS.Func
{
    /// <summary>
    /// 重写了HashCode，为X.GetHashCode() ^ Y.GetHashCode()
    /// 即如果两Vertex经纬度一致，则HashCode亦一致
    /// </summary>
    public struct Vertex : IComparable<Vertex>, IVoronoi
    {
        public double X { get; set; }
        public double Y { get; set; }

        public Vertex(double x, double y)
        {
            X = x;
            Y = y;
        }

        public PointF ToPointF()
        {
            return new PointF((float)X, (float)Y);
        }

        public override bool Equals(object obj)
        {
            return obj is Vertex && this == (Vertex)obj;
        }

        public override int GetHashCode()
        {
            return X.GetHashCode() ^ Y.GetHashCode();
        }

        public static bool operator ==(Vertex a, Vertex b)
        {
            return a.X == b.X && a.Y == b.Y;
        }

        public static bool operator !=(Vertex a, Vertex b)
        {
            return !(a == b);
        }

        public double VertexX { get { return X; } }
        public double VertexY { get { return Y; } }


        #region IComparable<Vertex> 成员
        public int CompareTo(Vertex other)
        {
            if (X == other.X && Y == other.Y)
            {
                return 0;
            }
            else if (X == other.X)
            {
                return Y > other.Y ? 1 : -1;
            }
            else
            {
                return X > other.X ? 1 : -1;
            }
        }
        #endregion
    }
}
