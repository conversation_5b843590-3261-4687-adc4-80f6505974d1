﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class WCellMosListForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(WCellMosListForm));
            this.ctxMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem1 = new System.Windows.Forms.ToolStripSeparator();
            this.miExpandAll = new System.Windows.Forms.ToolStripMenuItem();
            this.miCallapsAll = new System.Windows.Forms.ToolStripMenuItem();
            this.ListViewCell = new BrightIdeasSoftware.TreeListView();
            this.olvColumnSN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLACCI = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnSample = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLogName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMOS = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnTotalRSCP = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnTotalEcIo = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLongitude = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLatitude = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMaxMos = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMinMos = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnAvgMos = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMaxRscp = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMinRscp = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnAvgRscp = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMaxEcIo = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMinEcIo = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnEcIo = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.ctxMenu.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.ListViewCell)).BeginInit();
            this.SuspendLayout();
            // 
            // ctxMenu
            // 
            this.ctxMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExportExcel,
            this.toolStripMenuItem1,
            this.miExpandAll,
            this.miCallapsAll});
            this.ctxMenu.Name = "ctxMenu";
            this.ctxMenu.Size = new System.Drawing.Size(153, 98);
            // 
            // miExportExcel
            // 
            this.miExportExcel.Name = "miExportExcel";
            this.miExportExcel.Size = new System.Drawing.Size(152, 22);
            this.miExportExcel.Text = "导出Excel";
            this.miExportExcel.Click += new System.EventHandler(this.miExportExcel_Click);
            // 
            // toolStripMenuItem1
            // 
            this.toolStripMenuItem1.Name = "toolStripMenuItem1";
            this.toolStripMenuItem1.Size = new System.Drawing.Size(149, 6);
            this.toolStripMenuItem1.Visible = false;
            // 
            // miExpandAll
            // 
            this.miExpandAll.Name = "miExpandAll";
            this.miExpandAll.Size = new System.Drawing.Size(152, 22);
            this.miExpandAll.Text = "全部展开";
            this.miExpandAll.Visible = false;
            this.miExpandAll.Click += new System.EventHandler(this.miExpandAll_Click);
            // 
            // miCallapsAll
            // 
            this.miCallapsAll.Name = "miCallapsAll";
            this.miCallapsAll.Size = new System.Drawing.Size(152, 22);
            this.miCallapsAll.Text = "全部合并";
            this.miCallapsAll.Visible = false;
            this.miCallapsAll.Click += new System.EventHandler(this.miCallapsAll_Click);
            // 
            // ListViewCell
            // 
            this.ListViewCell.AllColumns.Add(this.olvColumnSN);
            this.ListViewCell.AllColumns.Add(this.olvColumnCellName);
            this.ListViewCell.AllColumns.Add(this.olvColumnLACCI);
            this.ListViewCell.AllColumns.Add(this.olvColumnSample);
            this.ListViewCell.AllColumns.Add(this.olvColumnLogName);
            this.ListViewCell.AllColumns.Add(this.olvColumnMOS);
            this.ListViewCell.AllColumns.Add(this.olvColumnTotalRSCP);
            this.ListViewCell.AllColumns.Add(this.olvColumnTotalEcIo);
            this.ListViewCell.AllColumns.Add(this.olvColumnLongitude);
            this.ListViewCell.AllColumns.Add(this.olvColumnLatitude);
            this.ListViewCell.AllColumns.Add(this.olvColumnMaxMos);
            this.ListViewCell.AllColumns.Add(this.olvColumnMinMos);
            this.ListViewCell.AllColumns.Add(this.olvColumnAvgMos);
            this.ListViewCell.AllColumns.Add(this.olvColumnMaxRscp);
            this.ListViewCell.AllColumns.Add(this.olvColumnMinRscp);
            this.ListViewCell.AllColumns.Add(this.olvColumnAvgRscp);
            this.ListViewCell.AllColumns.Add(this.olvColumnMaxEcIo);
            this.ListViewCell.AllColumns.Add(this.olvColumnMinEcIo);
            this.ListViewCell.AllColumns.Add(this.olvColumnEcIo);
            this.ListViewCell.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnSN,
            this.olvColumnCellName,
            this.olvColumnLACCI,
            this.olvColumnSample,
            this.olvColumnLogName,
            this.olvColumnMOS,
            this.olvColumnTotalRSCP,
            this.olvColumnTotalEcIo,
            this.olvColumnLongitude,
            this.olvColumnLatitude,
            this.olvColumnMaxMos,
            this.olvColumnMinMos,
            this.olvColumnAvgMos,
            this.olvColumnMaxRscp,
            this.olvColumnMinRscp,
            this.olvColumnAvgRscp,
            this.olvColumnMaxEcIo,
            this.olvColumnMinEcIo,
            this.olvColumnEcIo});
            this.ListViewCell.ContextMenuStrip = this.ctxMenu;
            this.ListViewCell.Cursor = System.Windows.Forms.Cursors.Default;
            this.ListViewCell.Dock = System.Windows.Forms.DockStyle.Fill;
            this.ListViewCell.FullRowSelect = true;
            this.ListViewCell.GridLines = true;
            this.ListViewCell.HeaderWordWrap = true;
            this.ListViewCell.IsNeedShowOverlay = false;
            this.ListViewCell.Location = new System.Drawing.Point(0, 0);
            this.ListViewCell.Name = "ListViewCell";
            this.ListViewCell.OwnerDraw = true;
            this.ListViewCell.ShowGroups = false;
            this.ListViewCell.Size = new System.Drawing.Size(1211, 502);
            this.ListViewCell.TabIndex = 5;
            this.ListViewCell.UseCompatibleStateImageBehavior = false;
            this.ListViewCell.View = System.Windows.Forms.View.Details;
            this.ListViewCell.VirtualMode = true;
            this.ListViewCell.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.listViewTotal_MouseDoubleClick);
            // 
            // olvColumnSN
            // 
            this.olvColumnSN.AspectName = "";
            this.olvColumnSN.HeaderFont = null;
            this.olvColumnSN.Text = "序号";
            // 
            // olvColumnCellName
            // 
            this.olvColumnCellName.HeaderFont = null;
            this.olvColumnCellName.Text = "小区名称";
            this.olvColumnCellName.Width = 150;
            // 
            // olvColumnLACCI
            // 
            this.olvColumnLACCI.HeaderFont = null;
            this.olvColumnLACCI.Text = "LAC_CI";
            this.olvColumnLACCI.Width = 120;
            // 
            // olvColumnSample
            // 
            this.olvColumnSample.HeaderFont = null;
            this.olvColumnSample.Text = "采样点数";
            // 
            // olvColumnLogName
            // 
            this.olvColumnLogName.HeaderFont = null;
            this.olvColumnLogName.Text = "文件名称";
            this.olvColumnLogName.Width = 200;
            // 
            // olvColumnMOS
            // 
            this.olvColumnMOS.HeaderFont = null;
            this.olvColumnMOS.Text = "MOS";
            // 
            // olvColumnTotalRSCP
            // 
            this.olvColumnTotalRSCP.HeaderFont = null;
            this.olvColumnTotalRSCP.Text = "TotalRSCP";
            // 
            // olvColumnTotalEcIo
            // 
            this.olvColumnTotalEcIo.HeaderFont = null;
            this.olvColumnTotalEcIo.Text = "TotalEcIo";
            // 
            // olvColumnLongitude
            // 
            this.olvColumnLongitude.HeaderFont = null;
            this.olvColumnLongitude.Text = "经度";
            // 
            // olvColumnLatitude
            // 
            this.olvColumnLatitude.HeaderFont = null;
            this.olvColumnLatitude.Text = "纬度";
            // 
            // olvColumnMaxMos
            // 
            this.olvColumnMaxMos.HeaderFont = null;
            this.olvColumnMaxMos.Text = "最大MOS";
            // 
            // olvColumnMinMos
            // 
            this.olvColumnMinMos.HeaderFont = null;
            this.olvColumnMinMos.Text = "最小MOS";
            // 
            // olvColumnAvgMos
            // 
            this.olvColumnAvgMos.HeaderFont = null;
            this.olvColumnAvgMos.Text = "平均MOS";
            // 
            // olvColumnMaxRscp
            // 
            this.olvColumnMaxRscp.HeaderFont = null;
            this.olvColumnMaxRscp.Text = "最大RSCP";
            // 
            // olvColumnMinRscp
            // 
            this.olvColumnMinRscp.HeaderFont = null;
            this.olvColumnMinRscp.Text = "最小RSCP";
            // 
            // olvColumnAvgRscp
            // 
            this.olvColumnAvgRscp.HeaderFont = null;
            this.olvColumnAvgRscp.Text = "平均RSCP";
            // 
            // olvColumnMaxEcIo
            // 
            this.olvColumnMaxEcIo.HeaderFont = null;
            this.olvColumnMaxEcIo.Text = "最大EcIo";
            // 
            // olvColumnMinEcIo
            // 
            this.olvColumnMinEcIo.HeaderFont = null;
            this.olvColumnMinEcIo.Text = "最小EcIo";
            // 
            // olvColumnEcIo
            // 
            this.olvColumnEcIo.HeaderFont = null;
            this.olvColumnEcIo.Text = "平均EcIo";
            // 
            // WCellMosListForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1211, 502);
            this.Controls.Add(this.ListViewCell);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Name = "WCellMosListForm";
            this.Text = "WCDMA_小区MOS关联分析列表";
            this.ctxMenu.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.ListViewCell)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.ContextMenuStrip ctxMenu;
        private System.Windows.Forms.ToolStripMenuItem miExportExcel;
        private System.Windows.Forms.ToolStripSeparator toolStripMenuItem1;
        private System.Windows.Forms.ToolStripMenuItem miExpandAll;
        private System.Windows.Forms.ToolStripMenuItem miCallapsAll;
        private BrightIdeasSoftware.TreeListView ListViewCell;
        private BrightIdeasSoftware.OLVColumn olvColumnSN;
        private BrightIdeasSoftware.OLVColumn olvColumnLACCI;
        private BrightIdeasSoftware.OLVColumn olvColumnCellName;
        private BrightIdeasSoftware.OLVColumn olvColumnAvgRscp;
        private BrightIdeasSoftware.OLVColumn olvColumnSample;
        private BrightIdeasSoftware.OLVColumn olvColumnMaxRscp;
        private BrightIdeasSoftware.OLVColumn olvColumnMinRscp;
        private BrightIdeasSoftware.OLVColumn olvColumnMaxEcIo;
        private BrightIdeasSoftware.OLVColumn olvColumnMinEcIo;
        private BrightIdeasSoftware.OLVColumn olvColumnEcIo;
        private BrightIdeasSoftware.OLVColumn olvColumnMaxMos;
        private BrightIdeasSoftware.OLVColumn olvColumnMinMos;
        private BrightIdeasSoftware.OLVColumn olvColumnAvgMos;
        private BrightIdeasSoftware.OLVColumn olvColumnLogName;
        private BrightIdeasSoftware.OLVColumn olvColumnMOS;
        private BrightIdeasSoftware.OLVColumn olvColumnTotalRSCP;
        private BrightIdeasSoftware.OLVColumn olvColumnTotalEcIo;
        private BrightIdeasSoftware.OLVColumn olvColumnLongitude;
        private BrightIdeasSoftware.OLVColumn olvColumnLatitude;

    }
}