﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Stat.Data;
using MasterCom.RAMS.Model.Interface;
using DBDataViewer;
using System.Windows.Forms;

namespace MasterCom.RAMS.CQT
{
    public class DIYQueryCQTSwitchFailDataNew : DIYStatQuery
    {
        public DIYQueryCQTSwitchFailDataNew(MainModel mainModel, string netWoker)
            : base(mainModel)
        {
            this.mainModel = mainModel;
            this.netType = netWoker;
        }
        private readonly string netType;
        /// <summary>
        /// 标题名称
        /// </summary>
        public override string Name
        {
            get { return ""; }
        }
        /// <summary>
        /// 图标名称
        /// </summary>
        public override string IconName
        {
            get { return "Images/regionevt.gif"; }
        }
        /// <summary>
        /// 查询条件是否有效
        /// </summary>
        protected override bool isValidCondition()
        {
            return true;
        }
        protected override StatTbToken getBranchToken()
        {
            return StatTbToken.log;
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 21000, 21025, this.Name);//临时
        }
        

        #region 全局变量
        private readonly List<FileInfo> fileList = new List<FileInfo>();
        private readonly List<string> fileValueNameList = new List<string>();
        readonly Dictionary<string, List<FileInfo>> fileValueList = new Dictionary<string, List<FileInfo>>();
        Dictionary<int, List<Event>> condEventsDic = new Dictionary<int, List<Event>>();
        readonly List<CQTHandOverItemFail> cqtResultList = new List<CQTHandOverItemFail>();
        #endregion
        /// <summary>
        /// 查询数据条件
        /// </summary>
        protected override void query()
        {
            fileList.Clear();
            fileValueNameList.Clear();
            fileValueList.Clear();
            cqtResultList.Clear();
            // 查找全区域文件
            DIYFileInfoData diyFileInfoData = new DIYFileInfoData(mainModel);
            diyFileInfoData.SetQueryCondition(this.Condition);
            diyFileInfoData.Query();
            fileList.AddRange(diyFileInfoData.FlieInfoData);
            //找出所有文件中包含的地点名称
            foreach (FileInfo fileIn in fileList)
            {
                string[] name = fileIn.Name.Split('_');
                if (name.Length < 3)
                    continue;
                if (!fileValueNameList.Contains(name[2]))
                {
                    fileValueNameList.Add(name[2]);
                }
            }
            //处理事件
            PrepareEvents();
            List<int> eventId = new List<int>();
            eventId.AddRange(condEventsDic.Keys);
            //每个地点所涉及的文件
            addFileValueList(eventId);
            //各测试地点事件
            if (netType.Equals("GSM"))
            {
                WaitBox.Show("开始分析各个测试地点的切换事件", cqtHandOverEventAnaGSM);
            }
            else if (netType.Equals("TD"))
            {
                WaitBox.Show("开始分析各个测试地点的切换事件", cqtHandOverEventAnaTD);
            }

            CQTSwitchFailXtraFormNew showForm = new CQTSwitchFailXtraFormNew(MainModel, condition, netType);
            showForm.setData(cqtResultList);
            showForm.Show();
        }

        private void addFileValueList(List<int> eventId)
        {
            foreach (string nameL in fileValueNameList)
            {
                List<FileInfo> subFileList = new List<FileInfo>();
                foreach (FileInfo fileIn in fileList)
                {
                    string[] name = fileIn.Name.Split('_');
                    if (name.Length < 3)
                        continue;
                    if (nameL.Equals(name[2]) && eventId.Contains(fileIn.ID))
                    {
                        subFileList.Add(fileIn);
                    }
                }
                fileValueList.Add(nameL, subFileList);
            }
        }

        /// <summary>
        /// 查询全区域切换事件
        /// </summary>
        private void PrepareEvents()
        {
            DIYEventByAllRegion queryEvent = new DIYEventByAllRegion(MainModel);
            queryEvent.SetIsAddEventToDTDataManager(false);
            queryEvent.SetSaveAsFileEventsDic(true);
            queryEvent.showEventChooser = false;
            queryEvent.IsQueryAllEvents = false;
            List<int> eventIds = new List<int>();
            if (netType.Equals("GSM"))
            {
                eventIds.Add(18);
            }
            else if (netType.Equals("TD"))
            {
                eventIds.Add(143);
                eventIds.Add(146);
                eventIds.Add(149);
                eventIds.Add(152);
            }
            condition.EventIDs = eventIds;
            queryEvent.SetQueryCondition(condition);
            queryEvent.Query();
            condEventsDic = queryEvent.fileEventsDic;
        }

        /// <summary>
        /// 按文件分析切换事件（TD）
        /// </summary>
        private void cqtHandOverEventAnaTD()
        {
            WaitBox.CanCancel = true;
            int idx = 1;
            foreach (string cpn in fileValueNameList)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                WaitBox.Text = "正在分析 " + idx++ + "/" + fileValueNameList.Count + " " + cpn + " 的切换事件...";
                WaitBox.ProgressPercent = 30;
                Dictionary<int, List<TestPoint>> fileTestPoint;
                fileTestPoint = replayEventFile(fileValueList[cpn]);
                foreach (FileInfo fileinfo in fileValueList[cpn])
                {
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                    foreach (Event eve in condEventsDic[fileinfo.ID])
                    {
                        addCQTHandOverItemTD(cpn, fileTestPoint, eve);
                    }
                }
                WaitBox.ProgressPercent = 90;
            }
            WaitBox.Close();
        }

        private void addCQTHandOverItemTD(string cpn, Dictionary<int, List<TestPoint>> fileTestPoint, Event eve)
        {
            if (eve.ID % 3 == 2)
            {
                CQTHandOverItemFail cqtResultTem = new CQTHandOverItemFail();
                cqtResultTem.HandOverType = "TD切换失败";
                cqtResultTem.Strcqtname = cpn;
                cqtResultTem.Strfilename = eve.FileName;
                cqtResultTem.Dtime = eve.DateTime;
                if (fileTestPoint.ContainsKey(eve.FileID))
                {
                    string[] strSubResult = TestPointAnaTD(fileTestPoint[eve.FileID], eve.DateTime).Split('|');
                    if (strSubResult[0] != "" && strSubResult[1] != "" && strSubResult[2] != "")
                    {
                        cqtResultTem.Strcellname = strSubResult[0];
                        cqtResultTem.Ilac = int.Parse(strSubResult[1]);
                        cqtResultTem.Ici = int.Parse(strSubResult[2]);
                        cqtResultTem.IUARFCN = int.Parse(strSubResult[3]);
                        cqtResultTem.ICPI = int.Parse(strSubResult[4]);
                        cqtResultTem.IRSCP = int.Parse(strSubResult[5]);
                        cqtResultTem.Ifileid = eve.FileID;
                        cqtResultList.Add(cqtResultTem);
                    }
                }
            }
        }

        /// <summary>
        /// 按文件分析切换事件（GSM）
        /// </summary>
        private void cqtHandOverEventAnaGSM()
        {
            WaitBox.CanCancel = true;
            int idx = 1;
            foreach (string cpn in fileValueNameList)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                WaitBox.Text = "正在分析 " + idx++ + "/" + fileValueNameList.Count + " " + cpn + " 的切换事件...";
                WaitBox.ProgressPercent = 30;
                Dictionary<int, List<TestPoint>> fileTestPoint;
                fileTestPoint = replayEventFile(fileValueList[cpn]);
                foreach (FileInfo fileinfo in fileValueList[cpn])
                {
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                    foreach (Event eve in condEventsDic[fileinfo.ID])
                    {
                        addCQTHandOverItemGSM(cpn, fileTestPoint, eve);
                    }
                }
                WaitBox.ProgressPercent = 90;
            }
            WaitBox.Close();
        }

        private void addCQTHandOverItemGSM(string cpn, Dictionary<int, List<TestPoint>> fileTestPoint, Event eve)
        {
            if (eve.ID == 18)
            {
                CQTHandOverItemFail cqtResultTem = new CQTHandOverItemFail();
                cqtResultTem.HandOverType = "GSM切换失败";
                cqtResultTem.Strcqtname = cpn;
                cqtResultTem.Strfilename = eve.FileName;
                cqtResultTem.Dtime = eve.DateTime;
                if (fileTestPoint.ContainsKey(eve.FileID))
                {
                    string[] strSubResult = TestPointAnaGSM(fileTestPoint[eve.FileID], eve.DateTime).Split('|');
                    if (strSubResult[0] != "" && strSubResult[1] != "" && strSubResult[2] != "")
                    {
                        cqtResultTem.Strcellname = strSubResult[0];
                        cqtResultTem.Ilac = int.Parse(strSubResult[1]);
                        cqtResultTem.Ici = int.Parse(strSubResult[2]);
                        cqtResultTem.IBCCH = int.Parse(strSubResult[3]);
                        cqtResultTem.IBSIC = int.Parse(strSubResult[4]);
                        cqtResultTem.IRxLevSub = int.Parse(strSubResult[5]);
                        cqtResultTem.Ifileid = eve.FileID;
                        cqtResultList.Add(cqtResultTem);
                    }
                }
            }
        }

        /// <summary>
        /// 回放事件所在文件获取采样点
        /// </summary>
        private Dictionary<int, List<TestPoint>> replayEventFile(List<FileInfo> filelist)
        {
            Dictionary<int, List<TestPoint>> fileTestPointTem
                = new Dictionary<int, List<TestPoint>>();
            QueryCondition condition = new QueryCondition();
            condition.FileInfos.AddRange(filelist);
            ReplayFileCQT query = new ReplayFileCQT(mainModel);
            query.SetQueryCondition(condition);
            query.Query();
            foreach (TestPoint tp in query.testPointList)
            {
                bool isValid = judgeValidTP(tp);
                if (isValid)
                {
                    if (!fileTestPointTem.ContainsKey(tp.FileID))
                    {
                        List<TestPoint> test = new List<TestPoint>();
                        test.Add(tp);
                        fileTestPointTem.Add(tp.FileID, test);
                    }
                    else
                    {
                        fileTestPointTem[tp.FileID].Add(tp);
                    }
                }
            }
            return fileTestPointTem;
        }

        private bool judgeValidTP(TestPoint tp)
        {
            if (netType.Equals("GSM"))
            {
                int? lac = (int?)tp["LAC"];
                int? ci = (int?)tp["CI"];
                short? bcch = (short?)tp["BCCH"];
                byte? bsic = (byte?)tp["BSIC"];
                if (lac == null || ci == null || bcch == null || bsic == null || tp["RxLevSub"] == null)
                {
                    return false;
                }
                int? iCi = (int)tp["CI"];
                int? iRxlevSub = (int)(short?)tp["RxLevSub"];
                if (iCi == null || iRxlevSub == null || iCi <= 0 || iRxlevSub > -10 || iRxlevSub < -140
                    || tp["RxQualSub"] == null || int.Parse(tp["RxQualSub"].ToString()) > 7 || int.Parse(tp["RxQualSub"].ToString()) < 0)
                {
                    return false;
                }
            }
            else if (netType.Equals("TD"))
            {
                if ((int?)tp["TD_SCell_UARFCN"] > 11000 || (int?)tp["TD_SCell_UARFCN"] < 9000
                    || (int?)tp["TD_SCell_CPI"] > 124 || (int?)tp["TD_SCell_CPI"] < 0 || tp["TD_PCCPCH_RSCP"] == null
                    || (int)(float?)tp["TD_PCCPCH_RSCP"] > -10 || (int)(float?)tp["TD_PCCPCH_RSCP"] < -140)
                {
                    return false;
                }
                int? lac = (int?)tp["TD_SCell_LAC"];
                int? ci = (int?)tp["TD_SCell_CI"];
                if (lac == null || ci == null)
                {
                    return false;
                }
            }
            return true;
        }

        /// <summary>
        /// 分析切换前后6个采样点（TD）
        /// </summary>
        private string TestPointAnaTD(List<TestPoint> testPointL, DateTime dTime)
        {
            int idTem = 0;
            int AvgRSCP = 0;
            int SumRSCP = 0;
            string cellName = "";
            string lac = "";
            string ci = "";
            string UARFCN = "";
            string CPI = "";
            testPointL.Sort(TestPointSortByTime.GetCompareByTime());
            for (int tpId = 0; tpId < testPointL.Count - 1; tpId++)
            {
                if (testPointL[tpId].DateTime <= dTime && testPointL[tpId + 1].DateTime >= dTime)
                {
                    idTem = tpId;
                    TDCell tdCell = null;
                    Cell cell = null;
                    getTDCell(testPointL, idTem, ref UARFCN, ref CPI, ref tdCell);
                    if (tdCell == null)
                    {
                        cell = getGsmCell(testPointL, idTem, cell);
                    }
                    AvgRSCP = SumRSCP / 6;
                    if (tdCell != null)
                    {
                        cellName = tdCell.Name;
                        lac = tdCell.LAC.ToString();
                        ci = tdCell.CI.ToString();
                    }
                    if (tdCell == null && cell != null)
                    {
                        cellName = cell.Name;
                        lac = cell.LAC.ToString();
                        ci = cell.CI.ToString();
                        UARFCN = cell.BCCH.ToString();
                        CPI = cell.BSIC.ToString();
                    }
                    break;
                }
            }
            return cellName + "|" + lac + "|" + ci + "|" + UARFCN + "|" + CPI + "|" + AvgRSCP.ToString();
        }

        private void getTDCell(List<TestPoint> testPointL, int idTem, ref string UARFCN, ref string CPI, ref TDCell tdCell)
        {
            for (int i = idTem; i > idTem - 6; i--)
            {
                if (i < 0 || (2 * idTem + 1 - i) > (testPointL.Count - 1))
                    break;
                if (tdCell == null)
                {
                    tdCell = CellManager.GetInstance().GetTDCell(testPointL[i].DateTime, (ushort)(int?)testPointL[i]["TD_SCell_LAC"],
                        (ushort)(int?)testPointL[i]["TD_SCell_CI"]);
                    UARFCN = testPointL[i]["TD_SCell_UARFCN"].ToString();
                    CPI = testPointL[i]["TD_SCell_CPI"].ToString();

                }
            }
        }

        private static Cell getGsmCell(List<TestPoint> testPointL, int idTem, Cell cell)
        {
            for (int i = idTem; i > idTem - 6; i--)
            {
                if (i < 0 || (2 * idTem + 1 - i) > (testPointL.Count - 1))
                    break;
                if (cell == null)
                {
                    if ((int?)testPointL[i]["LAC"] == null || (int?)testPointL[i]["CI"] == null)
                        continue;
                    cell = CellManager.GetInstance().GetCell(testPointL[i].DateTime, (ushort)(int?)testPointL[i]["LAC"],
                        (ushort)(int?)testPointL[i]["CI"]);
                    //UARFCN = testPointL[i]["BCCH"].ToString();
                    //CPI = testPointL[i]["BSIC"].ToString();
                }
                else
                {
                    break;
                }
            }

            return cell;
        }

        /// <summary>
        /// 分析切换前后6个采样点（GSM）
        /// </summary>
        private string TestPointAnaGSM(List<TestPoint> testPointL, DateTime dTime)
        {
            int idTem = 0;
            int AvgRxLevSub = 0;
            int SumRxLevSub = 0;
            string cellName = "";
            string lac = "";
            string ci = "";
            string BCCH = "";
            string BSIC = "";
            testPointL.Sort(TestPointSortByTime.GetCompareByTime());
            for (int tpId = 0; tpId < testPointL.Count - 1; tpId++)
            {
                if (testPointL[tpId].DateTime <= dTime && testPointL[tpId + 1].DateTime >= dTime)
                {
                    idTem = tpId;
                    Cell cell = null;
                    Cell cellTarget = null;
                    getGsmCell(testPointL, idTem, ref SumRxLevSub, ref cell, ref cellTarget);
                    AvgRxLevSub = SumRxLevSub / 6;
                    if (cell != null)
                    {
                        cellName = cell.Name;
                        lac = cell.LAC.ToString();
                        ci = cell.CI.ToString();
                        BCCH = cell.BCCH.ToString();
                        BSIC = cell.BSIC.ToString();
                    }
                    break;
                }
            }
            return cellName + "|" + lac + "|" + ci + "|" + BCCH + "|" + BSIC + "|" + AvgRxLevSub.ToString();
        }

        private void getGsmCell(List<TestPoint> testPointL, int idTem, ref int SumRxLevSub, ref Cell cell, ref Cell cellTarget)
        {
            for (int i = idTem; i > idTem - 6; i--)
            {
                if (i < 0 || (2 * idTem + 1 - i) > (testPointL.Count - 1))
                    break;

                if (cell == null)
                {
                    cell = CellManager.GetInstance().GetCell(testPointL[i].DateTime, (ushort)(int?)testPointL[i]["LAC"],
                        (ushort)(int?)testPointL[i]["CI"]);
                }
                if (cellTarget == null)
                {
                    cellTarget = CellManager.GetInstance().GetCell(testPointL[2 * idTem - i].DateTime,
                        (ushort)(int?)testPointL[2 * idTem + 1 - i]["LAC"], (ushort)(int?)testPointL[2 * idTem + 1 - i]["CI"]);
                }
                SumRxLevSub += (int)(short?)testPointL[i]["RxLevSub"];
            }
        }
    }
    public class CQTHandOverItemFail
    {
        public int Beforerelev { get; set; }
        public DateTime Dtime { get; set; }
        public string HandOverType { get; set; }
        public int Hotime { get; set; }
        public int Iareaid { get; set; }
        public int Iareatype { get; set; }
        public int Ici { get; set; }
        public int Ieventid { get; set; }
        public int Ifileid { get; set; }
        public int Ilac { get; set; }
        public int IBCCH { get; set; }
        public int IBSIC { get; set; }
        public int IRxLevSub { get; set; }
        public int Itime { get; set; }
        public string Strcellname { get; set; }
        public string Strcqtname { get; set; }
        public string Strfilename { get; set; }
        public int Wtimems { get; set; }
        public int IUARFCN { get; set; }
        public int ICPI { get; set; }
        public int IRSCP { get; set; }
    }
}