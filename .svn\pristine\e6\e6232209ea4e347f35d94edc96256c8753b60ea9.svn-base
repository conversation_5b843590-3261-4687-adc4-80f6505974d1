﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class RangeSetting
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.simpleBtnOK = new DevExpress.XtraEditors.SimpleButton();
            this.spinEditMin = new DevExpress.XtraEditors.SpinEdit();
            this.spinEditMax = new DevExpress.XtraEditors.SpinEdit();
            this.simpleBtnCancel = new DevExpress.XtraEditors.SimpleButton();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditMin.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditMax.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // simpleBtnOK
            // 
            this.simpleBtnOK.Location = new System.Drawing.Point(111, 48);
            this.simpleBtnOK.Name = "simpleBtnOK";
            this.simpleBtnOK.Size = new System.Drawing.Size(52, 23);
            this.simpleBtnOK.TabIndex = 0;
            this.simpleBtnOK.Text = "确定";
            this.simpleBtnOK.Click += new System.EventHandler(this.simpleBtnOK_Click);
            // 
            // spinEditMin
            // 
            this.spinEditMin.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.spinEditMin.Location = new System.Drawing.Point(13, 12);
            this.spinEditMin.Name = "spinEditMin";
            this.spinEditMin.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditMin.Properties.Mask.EditMask = "f0";
            this.spinEditMin.Size = new System.Drawing.Size(76, 21);
            this.spinEditMin.TabIndex = 1;
            this.spinEditMin.ValueChanged += new System.EventHandler(this.spinEditMin_ValueChanged);
            // 
            // spinEditMax
            // 
            this.spinEditMax.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.spinEditMax.Location = new System.Drawing.Point(168, 12);
            this.spinEditMax.Name = "spinEditMax";
            this.spinEditMax.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditMax.Properties.Mask.EditMask = "f0";
            this.spinEditMax.Size = new System.Drawing.Size(76, 21);
            this.spinEditMax.TabIndex = 1;
            this.spinEditMax.ValueChanged += new System.EventHandler(this.spinEditMax_ValueChanged);
            // 
            // simpleBtnCancel
            // 
            this.simpleBtnCancel.Location = new System.Drawing.Point(181, 48);
            this.simpleBtnCancel.Name = "simpleBtnCancel";
            this.simpleBtnCancel.Size = new System.Drawing.Size(52, 23);
            this.simpleBtnCancel.TabIndex = 0;
            this.simpleBtnCancel.Text = "取消";
            this.simpleBtnCancel.Click += new System.EventHandler(this.simpleBtnCancel_Click);
            // 
            // labelControl1
            // 
            this.labelControl1.Location = new System.Drawing.Point(125, 15);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(7, 14);
            this.labelControl1.TabIndex = 3;
            this.labelControl1.Text = "X";
            // 
            // labelControl3
            // 
            this.labelControl3.Appearance.Font = new System.Drawing.Font("宋体", 9F);
            this.labelControl3.Appearance.Options.UseFont = true;
            this.labelControl3.Location = new System.Drawing.Point(101, 16);
            this.labelControl3.Name = "labelControl3";
            this.labelControl3.Size = new System.Drawing.Size(12, 12);
            this.labelControl3.TabIndex = 3;
            this.labelControl3.Text = "≤";
            // 
            // labelControl2
            // 
            this.labelControl2.Appearance.Font = new System.Drawing.Font("宋体", 9F);
            this.labelControl2.Appearance.Options.UseFont = true;
            this.labelControl2.Location = new System.Drawing.Point(144, 16);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(12, 12);
            this.labelControl2.TabIndex = 3;
            this.labelControl2.Text = "≤";
            // 
            // RangeSetting
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(252, 82);
            this.Controls.Add(this.labelControl2);
            this.Controls.Add(this.labelControl3);
            this.Controls.Add(this.labelControl1);
            this.Controls.Add(this.spinEditMax);
            this.Controls.Add(this.spinEditMin);
            this.Controls.Add(this.simpleBtnCancel);
            this.Controls.Add(this.simpleBtnOK);
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "RangeSetting";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "范围设置";
            ((System.ComponentModel.ISupportInitialize)(this.spinEditMin.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditMax.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraEditors.SimpleButton simpleBtnOK;
        private DevExpress.XtraEditors.SpinEdit spinEditMin;
        private DevExpress.XtraEditors.SpinEdit spinEditMax;
        private DevExpress.XtraEditors.SimpleButton simpleBtnCancel;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.LabelControl labelControl2;
    }
}