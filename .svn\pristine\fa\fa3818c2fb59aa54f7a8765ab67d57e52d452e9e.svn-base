﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Net;
using MasterCom.ES.ColorManager;

namespace MasterCom.RAMS.Func.PopShow
{
    public partial class ESTaskItemPanel : UserControl
    {
        private List<PopESEvent> allEvents = null;
        private List<PopESEvent> curShowEvents = null;
        public ESTaskItemPanel()
        {
            InitializeComponent();
            init();
        }

        private void init()
        {
            this.olvColumnBranch.AspectGetter = delegate(object row)
            {
                if (row is BranchInfo)
                {
                    BranchInfo branch = row as BranchInfo;
                    return branch.branchName;
                }
                return "";
            };
            this.olvColumnEvtName.ImageGetter = delegate(object row)
            {
                if (row is PopESEvent)
                {
                    PopESEvent evt = row as PopESEvent;
                    EventInfo eventType = EventInfoManager.GetInstance()[evt.iEventID];
                    if (eventType != null)
                    {
                        return eventType.Image;
                    }
                }
                return null;
            };
            this.olvColumnEvtName.AspectGetter = delegate(object row)
            {
                if (row is BranchInfo)
                {
                    BranchInfo evt = row as BranchInfo;
                    return evt.events.Count + "个";
                }
                else if (row is PopESEvent)
                {
                    PopESEvent evt = row as PopESEvent;
                    EventInfo eventType=EventInfoManager.GetInstance()[evt.iEventID];
                    if(eventType!=null)
                    {
                        return eventType.Name;
                    }
                }
                return "";
            };
            this.olvColumnTime.AspectGetter = delegate(object row)
            {
                if (row is PopESEvent)
                {
                    PopESEvent evt = row as PopESEvent;
                    return JavaDate.GetDateTimeFromMilliseconds(1000L * evt.itime).ToString("yyyy-MM-dd HH:mm:ss");
                }
                return "";
            };
            this.olvColumnPreType.AspectGetter = delegate(object row)
            {
                if (row is PopESEvent)
                {
                    PopESEvent evt = row as PopESEvent;
                    return evt.pretype_desc;
                }
                return "";
            };
            this.olvColumnLogfile.AspectGetter = delegate(object row)
            {
                if (row is PopESEvent)
                {
                    PopESEvent evt = row as PopESEvent;
                    return evt.filaname;
                }
                return "";
            };
            this.listViewTotal.CanExpandGetter = delegate(object x)
            {
                return x is BranchInfo;
            };
            this.listViewTotal.ChildrenGetter = delegate(object x)
            {
                BranchInfo branch = (BranchInfo)x;
                return branch.events;
            };
        }
        internal void FillInfo(string taskname, List<PopESEvent> list)
        {
            if(allEvents!=null)
            {
                allEvents.Clear();
            }
            allEvents = list;
            this.lbAnaTaskTitle.Text = taskname;
            Dictionary<string, bool> areaNamesDic = new Dictionary<string, bool>();
            foreach(PopESEvent evt in list)
            {
                if (!areaNamesDic.ContainsKey(evt.strname))
                {
                    areaNamesDic[evt.strname] = true;
                }
            }
            cbxContentType.Items.Clear();
            cbxContentType.Items.Add("(全部)");
            foreach(string name in areaNamesDic.Keys)
            {
                cbxContentType.Items.Add(name);
            }
            cbxContentType.SelectedIndex = 0;
            freshShowSelectedArea();
        }

        private void freshShowSelectedArea()
        {
            if(curShowEvents!=null)
            {
                curShowEvents.Clear();
            }
            else
            {
                curShowEvents = new List<PopESEvent>();
            }
            string filterAreaName = cbxContentType.SelectedItem.ToString();
            foreach(PopESEvent evt in allEvents)
            {
                if (filterAreaName == "(全部)"||evt.strname == filterAreaName )
                {
                    curShowEvents.Add(evt);
                }
            }
            Dictionary<string, BranchInfo> branchEventsDic = new Dictionary<string, BranchInfo>();
            foreach(PopESEvent evt in curShowEvents)
            {
                BranchInfo branch = null;
                if (!branchEventsDic.TryGetValue(evt.strbranch, out branch))
                {
                    branch = new BranchInfo();
                    branch.branchName = evt.strbranch;
                    branchEventsDic[evt.strbranch] = branch;
                }
                branch.events.Add(evt);
            }
            listViewTotal.ClearObjects();
            listViewTotal.SetObjects(branchEventsDic.Values);
            freshShowChart(branchEventsDic);
        }

        private void freshShowChart(Dictionary<string, BranchInfo> branchEventsDic)
        {
            tChartESBranch.Series.Clear();
            Steema.TeeChart.Styles.Bar newBar = new Steema.TeeChart.Styles.Bar();
            newBar.Marks.Style = Steema.TeeChart.Styles.MarksStyles.Value;

            tChartESBranch.Series.Add(newBar);
            tChartESBranch.Header.Text = lbAnaTaskTitle.Text;
            tChartESBranch.Axes.Bottom.Title.Text = "现象分类";
            tChartESBranch.Axes.Bottom.Labels.Angle = 0;
            tChartESBranch.Axes.Left.Title.Text = "异常事件数量";
            foreach (BranchInfo branch in branchEventsDic.Values)
            {
                tChartESBranch.Series[0].Add(branch.events.Count, branch.branchName);
            }
        }

        private void cbxContentType_SelectedIndexChanged(object sender, EventArgs e)
        {
            freshShowSelectedArea();
        }

        private void miReplayEvent_Click(object sender, EventArgs e)
        {
            if (listViewTotal.SelectedObject is PopESEvent)
            {
                PopESEvent dtData = listViewTotal.SelectedObject as PopESEvent;
                if (dtData != null)
                {
                    PreNextMinutesForm preNextMinutesForm = new PreNextMinutesForm(false);
                    QueryBase qb = new DIYReplayFileWithinPeriodQuery(MainModel.GetInstance());
                    if (preNextMinutesForm.ShowDialog() == DialogResult.OK)
                    {
                        ReplayEvent(preNextMinutesForm, dtData, qb);
                    }
                }
            }
        }
        private void ReplayEvent(PreNextMinutesForm preNextMinutesForm, PopESEvent dtData, QueryBase qb)
        {
            try
            {
                QueryCondition condition = new QueryCondition();
                condition.QueryType = 2;//depth
                FileInfo fileInfo = new FileInfo();
                fileInfo.Name = dtData.filaname;
                fileInfo.ProjectID = dtData.iprojecttype;
                fileInfo.ID = dtData.ifileid;
                DateTime dt = JavaDate.GetDateTimeFromMilliseconds(1000L * dtData.itime);
                fileInfo.SampleTbName = dtData.sampletbname;
                fileInfo.ServiceType = dtData.iservice;
                fileInfo.LogTable = string.Format("tb_log_file_{0}_{1:D2}", dt.Year, dt.Month);

                condition.FileInfos.Add(fileInfo);
                int pre = preNextMinutesForm.Pre;
                int next = preNextMinutesForm.Next;
                condition.Periods.Add(new TimePeriod(dt.AddMinutes(-pre), dt.AddMinutes(next)));
                qb.SetQueryCondition(condition);
                qb.Query();
            }
            catch
            { 
                //continue
            }
        }

        private void miAnalyseEvent_Click(object sender, EventArgs e)
        {
            if (listViewTotal.SelectedObject is PopESEvent)
            {
                PopESEvent dtData = listViewTotal.SelectedObject as PopESEvent;
                MainModel.GetInstance().SelectedEvents.Clear();
                MainModel.GetInstance().SelectedEvents.Add(dtData.ConvertToEvent());
                EventOptionManager.GetInstance().DoAnalyseEvent();
            }
        }

        private void miExpandAll_Click(object sender, EventArgs e)
        {
            listViewTotal.ExpandAll();
        }

        private void miCallapsAll_Click(object sender, EventArgs e)
        {
            listViewTotal.CollapseAll();
        }
    }
    internal class BranchInfo
    {
        internal string branchName;
        internal List<PopESEvent> events = new List<PopESEvent>();
    };
}
