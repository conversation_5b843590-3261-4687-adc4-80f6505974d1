﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraTreeList;
using DevExpress.XtraTreeList.Nodes;

namespace MasterCom.Util.DevControlManager
{
    public static class TreeListHelper
    {
        public static void ThreeStateControl(TreeList treeList)
        {
            treeList.BeforeCheckNode -= treeList_BeforeCheckNode;
            treeList.BeforeCheckNode += treeList_BeforeCheckNode;
            treeList.AfterCheckNode -= treeList_AfterCheckNode;
            treeList.AfterCheckNode += treeList_AfterCheckNode;
        }

        static void treeList_BeforeCheckNode(object sender, CheckNodeEventArgs e)
        {
            e.State = (e.PrevState == CheckState.Checked ? CheckState.Unchecked : CheckState.Checked);
        }

        static void treeList_AfterCheckNode(object sender, NodeEventArgs e)
        {
            DevExpress.XtraTreeList.TreeList tree = sender as DevExpress.XtraTreeList.TreeList;
            tree.BeginUpdate();
            setCheckedChildNodes(e.Node, e.Node.CheckState);
            setCheckedParentNodes(e.Node, e.Node.CheckState);
            tree.EndUpdate();
        }

        private static void setCheckedChildNodes(TreeListNode node, CheckState check)
        {
            foreach (TreeListNode subNode in node.Nodes)
            {
                subNode.CheckState = check;
                if (subNode.Nodes.Count > 0)
                {
                    setCheckedChildNodes(subNode, check);
                }
            }
        }
        private static void setCheckedParentNodes(TreeListNode node, CheckState check)
        {
            if (node.ParentNode != null)
            {
                bool b = false;
                CheckState state;
                for (int i = 0; i < node.ParentNode.Nodes.Count; i++)
                {
                    state = node.ParentNode.Nodes[i].CheckState;
                    if (!check.Equals(state))
                    {
                        b = !b;
                        break;
                    }
                }
                node.ParentNode.CheckState = b ? CheckState.Indeterminate : check;
                setCheckedParentNodes(node.ParentNode, check);
            }
        }

    }


}
