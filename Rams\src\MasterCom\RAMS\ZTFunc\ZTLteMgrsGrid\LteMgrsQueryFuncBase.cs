﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class LteMgrsQueryFuncBase : QueryBase
    {
        public LteMgrsQueryFuncBase(MainModel mainModel) : base(mainModel)
        {
        }

        public override string Name
        {
            get { return "LTE扫频栅格分析"; }
        }

        public override string IconName
        {
            get { return string.Empty; }
        }

        protected override bool isValidCondition()
        {
            RegisterFuncs();
            if (condForm == null)
            {
                condForm = new LteMgrsConditionForm(funcList);
            }
            return condForm.ShowDialog() == DialogResult.OK;
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 23000, 23012, this.Name);
        }

        protected override void query()
        {
            DoSomethingBeforeQuery();

            List<LteMgrsCity> queryCitys = new List<LteMgrsCity>();
            WaitBox.Show("正在处理本次测试图层...", GetQueryCitys, queryCitys);
            LteMgrsQuery queryer = new LteMgrsQuery(MainModel);
            queryer.SetQueryCondition(this.Condition);
            queryer.QueryCitys = queryCitys;
            queryer.Query();
            queryer.QueryCitys = null;

            List<LteMgrsCity> baseCitys = null;
            if (LteMgrsBaseSettingManager.Instance.BaseDataEnable)
            {
                baseCitys = new List<LteMgrsCity>();
                foreach (LteMgrsCity city in queryCitys)
                {
                    baseCitys.Add(new LteMgrsCity(city.CityName, city.RegionShapeDic));
                }
                LteMgrsQuery baseQueryer = new LteMgrsQuery(MainModel);
                baseQueryer.SetQueryCondition(LteMgrsBaseSettingManager.Instance.BaseQueryCondition);
                baseQueryer.QueryCitys = baseCitys;
                baseQueryer.Query();
                baseQueryer.QueryCitys = null;
            }

            foreach (LteMgrsFuncItem funcItem in funcList)
            {
                funcItem.QueryCondition = Condition;
                funcItem.BaseCondition = LteMgrsBaseSettingManager.Instance.BaseQueryCondition;
                funcItem.BaseQueryCitys = baseCitys;
                funcItem.CurQueryCitys = queryCitys;
            }
            FireShowForm();
        }

        protected virtual void DoSomethingBeforeQuery()
        {
            LteMgrsGrid.FileType = "";
        }

        protected virtual void FireShowForm()
        {
            LteMgrsResultForm resultForm = MainModel.CreateResultForm(typeof(LteMgrsResultForm)) as LteMgrsResultForm;
            resultForm.FillData(funcList);
            resultForm.Visible = true;
            resultForm.BringToFront();
        }

        protected virtual void RegisterFuncs()
        {
            if (funcList == null)
            {
                funcList = new List<LteMgrsFuncItem>();
                funcList.Add(new LteMgrsFuncItem(new LteMgrsBaseSettingControl(), new LteMgrsStaterBase()));
                funcList.Add(new LteMgrsFuncItem(new LteMgrsBaseDataControl(), new LteMgrsStaterBase()));
                funcList.Add(new LteMgrsFuncItem(new LteMgrsSampleRateSetting(), new LteMgrsSampleRateStater()));
                funcList.Add(new LteMgrsFuncItem(new LteMgrsDualFreqSetting(), new LteMgrsDualFreqStater()));
                funcList.Add(new LteMgrsFuncItem(null, new LteMgrsTestDepthStater()));
                funcList.Add(new LteMgrsFuncItem(new LteMgrsRsrpRangeSetting(), new LteMgrsRsrpRangeStater()));
                funcList.Add(new LteMgrsFuncItem(new LteMgrsCoverageRangeSetting(), new LteMgrsCoverageRangeStater()));
                funcList.Add(new LteMgrsFuncItem(new LteMgrsWeakRsrpSetting(), new LteMgrsWeakRsrpStater()));
                funcList.Add(new LteMgrsFuncItem(new LteMgrsHighCoverageSetting(), new LteMgrsHighCoverageStater()));
            }
        }

        protected void GetQueryCitys(object result)
        {
            List<LteMgrsCity> queryCitys = result as List<LteMgrsCity>;
            try
            {
                int iLoop = 0;
                List<LteMgrsCitySettingItem> cityItems = LteMgrsBaseSettingManager.Instance.CityItems;
                foreach (LteMgrsCitySettingItem city in cityItems)
                {
                    WaitBox.ProgressPercent = ++iLoop * 100 / cityItems.Count;
                    if (city.Enable)
                    {
                        LteMgrsCity queryItem = city.Convert();
                        queryCitys.Add(queryItem);
                    }
                }
            }
            finally
            {
                System.Threading.Thread.Sleep(500);
                WaitBox.Close();
            }
        }

        protected void MergeCitys(object args)
        {
            try
            {
                object[] o = args as object[];
                List<LteMgrsCity> curCitys = o[0] as List<LteMgrsCity>;
                List<LteMgrsCity> baseCitys = o[1] as List<LteMgrsCity>;
                List<LteMgrsCity> mergedCitys = o[2] as List<LteMgrsCity>;

                int loopSum = 0, iLoop = 0;
                foreach (LteMgrsCity bCity in baseCitys)
                {
                    loopSum += bCity.MgrsRegionMap.Count;
                }

                foreach (LteMgrsCity bCity in baseCitys)
                {
                    LteMgrsCity cCity = null;
                    foreach (LteMgrsCity city in curCitys)
                    {
                        if (bCity.CityName == city.CityName)
                        {
                            cCity = city;
                            break;
                        }
                    }
                    if (cCity == null)
                    {
                        continue;
                    }

                    iLoop = mgrsCityAddGrid(mergedCitys, loopSum, iLoop, bCity, cCity);
                }
            }
            finally
            {
                System.Threading.Thread.Sleep(500);
                WaitBox.Close();
            }
        }

        private int mgrsCityAddGrid(List<LteMgrsCity> mergedCitys, int loopSum, int iLoop, LteMgrsCity bCity, LteMgrsCity cCity)
        {
            LteMgrsCity mCity = new LteMgrsCity(cCity.CityName, cCity.RegionShapeDic);
            mergedCitys.Add(mCity);
            foreach (LteMgrsRegion reg in cCity.RegionDic.Values)
            {
                foreach (LteMgrsGrid grid in reg.GridDic.Values)
                {
                    mCity.AddGrid(grid, reg.RegionName);
                }
            }

            foreach (string bKey in bCity.MgrsRegionMap.Keys)
            {
                WaitBox.ProgressPercent = (++iLoop) * 100 / loopSum;
                if (mCity.MgrsRegionMap.ContainsKey(bKey))
                {
                    continue;
                }
                LteMgrsRegion reg = bCity.MgrsRegionMap[bKey];
                LteMgrsGrid grid = reg.GridDic[bKey];
                mCity.AddGrid(grid, reg.RegionName);
            }

            return iLoop;
        }

        protected LteMgrsConditionForm condForm = null;
        protected List<LteMgrsFuncItem> funcList = null;
    }

    public class LteMgrsFuncItem
    {
        public LteMgrsConditionControlBase ConditionControl { get; set; }
        public LteMgrsStaterBase Stater { get; set; }
        public object FuncCondtion { get; set; }
        public QueryCondition QueryCondition { get; set; }
        public QueryCondition BaseCondition { get; set; } // 基准库条件
        public List<LteMgrsCity> CurQueryCitys { get; set; }
        public List<LteMgrsCity> BaseQueryCitys { get; set; }
        public int SelectedCityIndex { get; set; } = -1;

        public LteMgrsFuncItem(LteMgrsConditionControlBase conditionControl, LteMgrsStaterBase stater)
        {
            ConditionControl = conditionControl;
            this.Stater = stater;
        }

        public string ConditionTitle
        {
            get { return ConditionControl == null ? null : ConditionControl.Title; }
        }

        public void SelectCityByName(string cityName)
        {
            for (int i = 0; i < CurQueryCitys.Count; ++i)
            {
                if (CurQueryCitys[i].CityName == cityName)
                {
                    SelectedCityIndex = i;
                    return;
                }
            }
            SelectedCityIndex = -1;
        }

        public virtual void Clear()
        {
            CurQueryCitys = BaseQueryCitys = null;
            if (Stater != null)
            {
                Stater.Clear();
            }
        }
    }
}
