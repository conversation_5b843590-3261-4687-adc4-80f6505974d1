﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class IndexOfRoadStructureDlg : BaseDialog
    {
        public IndexOfRoadStructureDlg()
        {
            InitializeComponent();
        }

        public void GetCondition(ref int rxLevDValue, ref int totalBCCHCount900, ref int totalBCCHCount1800)
        {
            rxLevDValue = (int)numRxLevDValue.Value;
            totalBCCHCount900 = (int)numTotalBcchCount900.Value;
            totalBCCHCount1800 = (int)numTotalBCCHCount1800.Value;
        }
    }
}
