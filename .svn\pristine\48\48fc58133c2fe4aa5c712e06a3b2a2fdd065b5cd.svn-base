﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Drawing;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Grid;
using MasterCom.RAMS.KPI_Statistics;

namespace MasterCom.RAMS
{
    public class QueryCPGridCell : QueryBase
    {
        public MapFormItemSelection ItemSelection { get; set; }

        private CPGridCellDlgLsatCondition curCPModeCondition;

        public QueryCPGridCell(MainModel mainModel)
            : base(mainModel)
        {
            this.mainModel = mainModel;
        }

        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 20000, 20047, this.Name);
        }

        public override string IconName
        {
            get { return "IconCP"; }
        }

        public override string Name
        {
            get { return "栅格小区对比"; }
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.Region;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            if (searchGeometrys.Region == null)
            {
                return false;
            }
            return searchGeometrys.IsValidRegion;
        }

        protected override bool isValidCondition()
        {
            return true;
        }

        protected override void query()
        {
            CPGridCellsSettingDlg dlg = new CPGridCellsSettingDlg(mainModel, ItemSelection, curCPModeCondition);
            if (dlg.ShowDialog() != System.Windows.Forms.DialogResult.OK)
            {
                return;
            }
            curCPModeCondition = dlg.CurCPModeCondition;

            CPGridCellShowForm form = null;

            form = MainModel.GetObjectFromBlackboard(typeof(CPGridCellShowForm).FullName) as CPGridCellShowForm;
            if (form == null || form.IsDisposed)
            {
                form = new CPGridCellShowForm(MainModel, ItemSelection, this.condition, 
                    curCPModeCondition.CurConditionHost, curCPModeCondition.CurConditionGuest,
                    curCPModeCondition.CurCompareTemplate,curCPModeCondition.CompareConfig);
            }
            if (!form.Visible)
            {
                form.Show(MainModel.MainForm);
            }
        }
    }

    public class QueryGridCellByGrids : QueryKPIStatBase
    {
        public GridMatrix<GridCellFormula> RtGridCells { get; set; }

        public GridMatrix<GridUnitBase> Grids { get; set; }

        public string curFormula { get; set; }

        public Dictionary<ICell, StatDataHubBase> CellDataDic
        {
            get
            {
                Dictionary<ICell, StatDataHubBase> cellDataDic = new Dictionary<ICell, StatDataHubBase>();
                foreach (GridCellFormula unit in RtGridCells)
                {
                    foreach (ICell cell in unit.CellDataHubDic.Keys)
                    {
                        StatDataHubBase hub;
                        if (!cellDataDic.TryGetValue(cell, out hub))
                        {
                            hub = new StatDataHubBase();
                            cellDataDic[cell] = hub;
                        }
                        hub.Merge(unit.CellDataHubDic[cell]);
                    }
                }
                return cellDataDic;
            }
        }

        private double ltLng = 360;
        private double ltLat;
        private double rbLng = 0;
        private double rbLat = 0;

        public QueryGridCellByGrids()
            : base()
        {
            Grids = new GridMatrix<GridUnitBase>();
            RtGridCells = new GridMatrix<GridCellFormula>();

            ltLat = 0;
            rbLat = 360;
        }

        protected override MasterCom.RAMS.Model.Interface.StatTbToken getTableNameToken()
        {
            return MasterCom.RAMS.Model.Interface.StatTbToken.cell_grid;
        }

        public void AddGrid(GridUnitBase grid)
        {
            int rowIdx, colIdx;
            GridHelper.GetIndexOfDefaultSizeGrid(grid.LTLng, grid.LTLat, out rowIdx, out colIdx);
            Grids[rowIdx, colIdx] = grid;

            ltLng = Math.Min(ltLng, grid.LTLng);
            ltLat = Math.Max(ltLat, grid.LTLat);
            rbLng = Math.Max(rbLng, grid.BRLng);
            rbLat = Math.Min(rbLat, grid.BRLat);
        }

        protected override void preparePackageCommand(MasterCom.RAMS.Net.Package package)
        {
            package.Command = Command.DIYSearch;
            package.SubCommand = SubCommand.Request;
            package.Content.Type = RequestType.KPI_CELL_GRID;
            package.Content.PrepareAddParam();
        }

        protected override void AddExtraCondition(Package package, params object[] reservedParams)
        {
            package.Content.AddParam((byte)OpOptionDef.AreaSelectIntersect);
            package.Content.AddParam(ltLng);
            package.Content.AddParam(ltLat);
            package.Content.AddParam(rbLng);
            package.Content.AddParam(rbLat);
            AddDIYEndOpFlag(package);
        }

        private void clear()
        {
            RtGridCells.Grids.Clear();
        }

        protected override bool getConditionBeforeQuery()
        {
            clear();
            return Grids.Length > 0;
        }

        public IEnumerable<string> FormulaSet
        {
            get;
            set;
        }

        protected override string getStatImgNeededTriadID(params object[] paramSet)
        {
            return getTriadIDIgnoreServiceType(FormulaSet);
        }

        protected override void afterRecieveAllData(params object[] reservedParams)
        {
            if(RtGridCells != null && this.curFormula != string.Empty)
            {
                foreach (GridCellFormula gridCellFormula in RtGridCells)
                {
                    foreach(GridCellDataHub gridCellDataHub in gridCellFormula.CellDataHubDic.Values)
                    {
                        try
                        {
                            gridCellDataHub.formulaValueDic[GridCellDataHub.strFormula] = gridCellDataHub.CalcValueByFormula(this.curFormula);
                        }
                        catch
                        {
                            gridCellDataHub.formulaValueDic[GridFormula.strFormula] = -10000000;
                        }
                    }
                }
            }
        }

        protected override void recieveAndHandleSpecificStatData(Package package, List<MasterCom.RAMS.Model.Interface.StatImgDefItem> curImgColumnDef, KPIStatDataBase singleStatData)
        {
            int lac = package.Content.GetParamInt();
            //int ci = package.Content.GetParamInt();
            long ci = NRDTDataHelper.AnalyseNCI(package.Content);
            double dLong = package.Content.GetParamDouble();
            double dLat = package.Content.GetParamDouble();
            GridUnitBase grid = new GridUnitBase(dLong, dLat);
            int rowIdx, colIdx;
            GridHelper.GetIndexOfDefaultSizeGrid(grid.LTLng, grid.LTLat, out rowIdx, out colIdx);

            if (Grids[rowIdx, colIdx] == null)
            {
                return;
            }
            GridCellFormula unit = RtGridCells[rowIdx, colIdx];
            if (unit == null)
            {
                unit = new GridCellFormula();
                unit.LTLng = dLong;
                unit.LTLat = dLat;
                RtGridCells[rowIdx, colIdx] = unit;
            }

            MasterCom.RAMS.Model.ICell cell = MasterCom.RAMS.Model.CellManager.GetInstance().GetICellByLACCI(lac, ci);
            fillStatData(package, curImgColumnDef, singleStatData);
            int fileID = (int)singleStatData[KPIStatDataBase.FileIDKey, KPIStatDataBase.NewGridFileIDKey, -1];
            MasterCom.RAMS.Model.DTDataHeaderManager.GetInstance().GetHeaderByFileID(fileID);

            unit.AddCellData(cell, singleStatData, false);
        }
    }

    public class GridCellFormula : GridUnitBase
    {
        public Color GridColor { get; set; }

        public Dictionary<ICell, GridCellDataHub> CellDataHubDic { get; set; }

        public GridCellFormula()
        {
            CellDataHubDic = new Dictionary<ICell, GridCellDataHub>();
            GridColor = new Color();
        }

        public void AddCellData(ICell cell, KPIStatDataBase kpiData, bool makeGridMatrix)
        {
            GridCellDataHub data;
            if (!CellDataHubDic.TryGetValue(cell, out data))
            {
                data = new GridCellDataHub();
                CellDataHubDic.Add(cell, data);
            }
            data.AddStatData(kpiData, makeGridMatrix);
        }
    }

    public class GridCellDataHub : StatDataHubBase
    {

        public static readonly string strFormula = "strFormula";

        public Dictionary<string, double> formulaValueDic { get; set; } = new Dictionary<string, double>();

        public double GetValue(string key)
        {
            double dValue;
            if (!formulaValueDic.TryGetValue(key, out dValue) || dValue == -10000000)
            {
                dValue = double.NaN;
            }
            return dValue;
        }
    }

}
