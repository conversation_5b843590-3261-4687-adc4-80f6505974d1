﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using DevExpress.XtraEditors;
using System.Windows.Forms;

namespace MasterCom.RAMS.Func.CustomTestpointFilter
{
    public class QueryFilterTestpoints : QueryBase
    {
        public QueryFilterTestpoints(MainModel mainModel)
            : base(mainModel)
        { 
        }

        protected override bool isValidCondition()
        {
            return true;
        }

        //public override bool CanEnabled(SearchGeometrys searchGeometrys)
        //{
        //    if (MainModel.SelectedTestPoints.Count > 0)
        //        return true;
        //    else
        //        return false;
        //}

        public override string Name
        {
            get { return "采样点过滤查询"; }
        }
        public override string IconName
        {
            get { return "Images/regionevt.gif"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 11000, 11014, this.Name);
        }

        private bool isLogicAnd()
        {
            if (MainModel.CExpressList[0].logic=="[与]") //因为所有的公式的与和或关系都是一致的，只需取头个公式即可知所有逻辑关系
                return true;
            else
                return false;
        }

        CExpressRet_and.childCExpress retCExpress;
        /// <summary>
        /// 合并逻辑与公式
        /// </summary>
        private void MergeCExpressList(out bool validRet)
        {  
            validRet = true;
            retCExpress = new CExpressRet_and.childCExpress();
            retCExpress.fillFromCE(MainModel.CExpressList[0]);
            MainModel.CExpressRet_and.childCExpressList.Clear();
            if (MainModel.CExpressList.Count == 1)
            {
                MainModel.CExpressRet_and.childCExpressList.Add(retCExpress);
            }
            else
            {
                for (int i = 1; i < MainModel.CExpressList.Count; i++)
                {
                    bool buildNewChildCE = false;//公式有新的参数名称，新建结果的子公式childCExpress
                    CExpress addCexpress = MainModel.CExpressList[i];

                    if (MainModel.CExpressRet_and.childCExpressList.Count == 0)
                    {
                        MainModel.CExpressRet_and.childCExpressList.Add(retCExpress);
                    }

                    int count = 1;
                    foreach (CExpressRet_and.childCExpress childCe in MainModel.CExpressRet_and.childCExpressList)
                    {
                        if (childCe.information.name != addCexpress.information.name && count == MainModel.CExpressRet_and.childCExpressList.Count)
                        {
                            buildNewChildCE = true;
                        }
                        count++;
                    }

                    if (buildNewChildCE)
                    {
                        CExpressRet_and.childCExpress addce = new CExpressRet_and.childCExpress();
                        addce.fillFromCE(addCexpress);
                        MainModel.CExpressRet_and.childCExpressList.Add(addce);
                    }
                    else
                    {
                        bool needMerge = false;
                        bool hasNewArgument = false;
                        bool hasNewMS = false;
                        if (!retCExpress.enableArgument && !addCexpress.enableArgument)  //新增与现有公式都没有参数序号，需要合并
                            needMerge = true;
                        else if (retCExpress.enableArgument && addCexpress.enableArgument && !retCExpress.enableMS && !addCexpress.enableMS)
                        {
                            if (retCExpress.argument == addCexpress.argument) //新增与现有公式都有参数序号的，没有端口号，序号相同则需要合并，否则提示无法合并
                                needMerge = true;
                        }
                        else if (retCExpress.enableArgument && addCexpress.enableMS && retCExpress.enableMS && addCexpress.enableMS)
                        {
                            if (retCExpress.argument == addCexpress.argument && retCExpress.MS == addCexpress.MS)//新增与现有公式都有参数序号，端口号，同时两者相同则需要合并，否则提示无法合并
                                needMerge = true;
                        }
                        else if (retCExpress.enableArgument && addCexpress.enableMS && !retCExpress.enableMS && addCexpress.enableMS)
                        {
                            if (retCExpress.argument == addCexpress.argument)//新增与现有公式都有参数序号，现有没有端口号，新增有端口号，同时两者序号相同则需要合并，否则提示无法合并
                            {
                                hasNewMS = true;
                                needMerge = true;
                            }
                        }
                        else if (!retCExpress.enableArgument && addCexpress.enableArgument)
                        {
                            if (addCexpress.enableMS)//新增公式有参数序号，端口号，现有的都没有，合并后算选用新的序号和端口号
                            {
                                hasNewArgument = true;
                                hasNewMS = true;
                                needMerge = true;
                            }
                            else //新增公式有参数序号，没有端口号，现有的都没有，合并后算选用新的序号，仍没有端口号
                            {
                                hasNewArgument = true;
                                needMerge = true;
                            }
                        }
                        else
                            needMerge = false;

                        if (needMerge)  //按符号和门限值，进行逻辑[与]的合并
                        {
                            switch (retCExpress.information.sign)
                            {
                                case CExpress.Information.Sign.lt:
                                    switch (addCexpress.information.sign)
                                    {
                                        case CExpress.Information.Sign.lt:
                                            retCExpress.information.threshold = Math.Min(Double.Parse(addCexpress.information.threshold), Double.Parse(retCExpress.information.threshold)).ToString();
                                                if (hasNewArgument)
                                                {
                                                    retCExpress.argument = addCexpress.argument;
                                                    retCExpress.enableArgument = true;
                                                }
                                                if (hasNewMS)
                                                {
                                                    retCExpress.MS = addCexpress.MS;
                                                    retCExpress.enableMS = true;
                                                }
                                            break;
                                        case CExpress.Information.Sign.le:
                                                retCExpress.information.threshold = Math.Min(Double.Parse(addCexpress.information.threshold), Double.Parse(retCExpress.information.threshold)).ToString();
                                                retCExpress.information.sign = CExpress.Information.Sign.le;
                                                if (hasNewArgument)
                                                {
                                                    retCExpress.argument = addCexpress.argument;
                                                    retCExpress.enableArgument = true;
                                                }
                                                if (hasNewMS)
                                                {
                                                    retCExpress.MS = addCexpress.MS;
                                                    retCExpress.enableMS = true;
                                                }
                                            break;
                                        case CExpress.Information.Sign.eq:
                                            if (Double.Parse(addCexpress.information.threshold) < Double.Parse(retCExpress.information.threshold))
                                            {
                                                retCExpress.information.threshold = addCexpress.information.threshold;
                                                retCExpress.information.sign = CExpress.Information.Sign.eq;
                                                if (hasNewArgument)
                                                {
                                                    retCExpress.argument = addCexpress.argument;
                                                    retCExpress.enableArgument = true;
                                                }
                                                if (hasNewMS)
                                                {
                                                    retCExpress.MS = addCexpress.MS;
                                                    retCExpress.enableMS = true;
                                                }
                                            }
                                            else
                                                validRet = false;
                                            break;
                                        case CExpress.Information.Sign.ge:
                                            if (Double.Parse(addCexpress.information.threshold) < Double.Parse(retCExpress.information.threshold))
                                            {
                                                retCExpress.information.headThreshold = Double.Parse(addCexpress.information.threshold) - 0.001;
                                                retCExpress.information.tailThreshold = Double.Parse(retCExpress.information.threshold);
                                                retCExpress.information.sign = CExpress.Information.Sign.range;
                                                retCExpress.information.threshold = null;  //赋给原来的门限值无效值
                                                if (hasNewArgument)
                                                {
                                                    retCExpress.argument = addCexpress.argument;
                                                    retCExpress.enableArgument = true;
                                                }
                                                if (hasNewMS)
                                                {
                                                    retCExpress.MS = addCexpress.MS;
                                                    retCExpress.enableMS = true;
                                                }
                                            }
                                            else
                                                validRet = false;
                                            break;
                                        case CExpress.Information.Sign.gt:
                                            if (Double.Parse(addCexpress.information.threshold) < Double.Parse(retCExpress.information.threshold))
                                            {
                                                retCExpress.information.headThreshold = Double.Parse(addCexpress.information.threshold);
                                                retCExpress.information.tailThreshold = Double.Parse(retCExpress.information.threshold);
                                                retCExpress.information.sign = CExpress.Information.Sign.range;
                                                retCExpress.information.threshold = null;
                                                if (hasNewArgument)
                                                {
                                                    retCExpress.argument = addCexpress.argument;
                                                    retCExpress.enableArgument = true;
                                                }
                                                if (hasNewMS)
                                                {
                                                    retCExpress.MS = addCexpress.MS;
                                                    retCExpress.enableMS = true;
                                                }
                                            }
                                            else
                                                validRet = false;
                                            break;
                                        default:
                                            validRet = false;
                                            break;
                                    }
                                    break;
                                case CExpress.Information.Sign.le:
                                    switch (addCexpress.information.sign)
                                    {
                                        case CExpress.Information.Sign.lt:
                                                retCExpress.information.threshold = Math.Min(Double.Parse(addCexpress.information.threshold), Double.Parse(retCExpress.information.threshold)).ToString();
                                                retCExpress.information.sign = CExpress.Information.Sign.lt;
                                                if (hasNewArgument)
                                                {
                                                    retCExpress.argument = addCexpress.argument;
                                                    retCExpress.enableArgument = true;
                                                }
                                                if (hasNewMS)
                                                {
                                                    retCExpress.MS = addCexpress.MS;
                                                    retCExpress.enableMS = true;
                                                }
                                                break;
                                        case CExpress.Information.Sign.le:
                                            retCExpress.information.threshold = Math.Min(Double.Parse(addCexpress.information.threshold), Double.Parse(retCExpress.information.threshold)).ToString();
                                                if (hasNewArgument)
                                                {
                                                    retCExpress.argument = addCexpress.argument;
                                                    retCExpress.enableArgument = true;
                                                }
                                                if (hasNewMS)
                                                {
                                                    retCExpress.MS = addCexpress.MS;
                                                    retCExpress.enableMS = true;
                                                }
                                            break;
                                        case CExpress.Information.Sign.eq:
                                            if (Double.Parse(addCexpress.information.threshold) <= Double.Parse(retCExpress.information.threshold))
                                            {
                                                retCExpress.information.threshold = addCexpress.information.threshold;
                                                retCExpress.information.sign = CExpress.Information.Sign.eq;
                                                if (hasNewArgument)
                                                {
                                                    retCExpress.argument = addCexpress.argument;
                                                    retCExpress.enableArgument = true;
                                                }
                                                if (hasNewMS)
                                                {
                                                    retCExpress.MS = addCexpress.MS;
                                                    retCExpress.enableMS = true;
                                                }
                                            }
                                            else
                                                validRet = false;
                                            break;
                                        case CExpress.Information.Sign.ge:
                                            if (Double.Parse(addCexpress.information.threshold) < Double.Parse(retCExpress.information.threshold))
                                            {
                                                retCExpress.information.headThreshold = Double.Parse(addCexpress.information.threshold) - 0.001;
                                                retCExpress.information.tailThreshold = Double.Parse(retCExpress.information.threshold) + 0.001;
                                                retCExpress.information.sign = CExpress.Information.Sign.range;
                                                retCExpress.information.threshold = null;
                                                if (hasNewArgument)
                                                {
                                                    retCExpress.argument = addCexpress.argument;
                                                    retCExpress.enableArgument = true;
                                                }
                                                if (hasNewMS)
                                                {
                                                    retCExpress.MS = addCexpress.MS;
                                                    retCExpress.enableMS = true;
                                                }
                                            }
                                            else
                                                validRet = false;
                                            break;
                                        case CExpress.Information.Sign.gt:
                                            if (Double.Parse(addCexpress.information.threshold) < Double.Parse(retCExpress.information.threshold))
                                            {
                                                retCExpress.information.headThreshold = Double.Parse(addCexpress.information.threshold);
                                                retCExpress.information.tailThreshold = Double.Parse(retCExpress.information.threshold) + 0.001;
                                                retCExpress.information.sign = CExpress.Information.Sign.range;
                                                retCExpress.information.threshold = null;
                                                if (hasNewArgument)
                                                {
                                                    retCExpress.argument = addCexpress.argument;
                                                    retCExpress.enableArgument = true;
                                                }
                                                if (hasNewMS)
                                                {
                                                    retCExpress.MS = addCexpress.MS;
                                                    retCExpress.enableMS = true;
                                                }
                                            }
                                            break;
                                        default:
                                            validRet = false;
                                            break;
                                    }
                                    break;
                                case CExpress.Information.Sign.eq:
                                    switch (addCexpress.information.sign)
                                    {
                                        case CExpress.Information.Sign.lt:
                                            validRet = false;
                                            break;
                                        case CExpress.Information.Sign.le:
                                            validRet = false;
                                            break;
                                        case CExpress.Information.Sign.eq:
                                            if (addCexpress.information.threshold != retCExpress.information.threshold)
                                                validRet = false;
                                            else
                                            {
                                                if (hasNewArgument)
                                                {
                                                    retCExpress.argument = addCexpress.argument;
                                                    retCExpress.enableArgument = true;
                                                }
                                                if (hasNewMS)
                                                {
                                                    retCExpress.MS = addCexpress.MS;
                                                    retCExpress.enableMS = true;
                                                }
                                            }
                                            break;
                                        case CExpress.Information.Sign.ge:
                                            validRet = false;
                                            break;
                                        case CExpress.Information.Sign.gt:
                                            validRet = false;
                                            break;
                                        default:
                                            validRet = false;
                                            break;
                                    }
                                    break;
                                case CExpress.Information.Sign.ge:
                                    switch (addCexpress.information.sign)
                                    {
                                        case CExpress.Information.Sign.lt:
                                            if (Double.Parse(addCexpress.information.threshold) > Double.Parse(retCExpress.information.threshold))
                                            {
                                                retCExpress.information.headThreshold = Double.Parse(retCExpress.information.threshold) - 0.001;
                                                retCExpress.information.tailThreshold = Double.Parse(addCexpress.information.threshold);
                                                retCExpress.information.sign = CExpress.Information.Sign.range;
                                                retCExpress.information.threshold = null;
                                                if (hasNewArgument)
                                                {
                                                    retCExpress.argument = addCexpress.argument;
                                                    retCExpress.enableArgument = true;
                                                }
                                                if (hasNewMS)
                                                {
                                                    retCExpress.MS = addCexpress.MS;
                                                    retCExpress.enableMS = true;
                                                }
                                            }
                                            else
                                                validRet = false;
                                            break;
                                        case CExpress.Information.Sign.le:
                                            if (Double.Parse(addCexpress.information.threshold) > Double.Parse(retCExpress.information.threshold))
                                            {
                                                retCExpress.information.headThreshold = Double.Parse(retCExpress.information.threshold) - 0.001;
                                                retCExpress.information.tailThreshold = Double.Parse(addCexpress.information.threshold) + 0.001;
                                                retCExpress.information.sign = CExpress.Information.Sign.range;
                                                retCExpress.information.threshold = null;
                                                if (hasNewArgument)
                                                {
                                                    retCExpress.argument = addCexpress.argument;
                                                    retCExpress.enableArgument = true;
                                                }
                                                if (hasNewMS)
                                                {
                                                    retCExpress.MS = addCexpress.MS;
                                                    retCExpress.enableMS = true;
                                                }
                                            }
                                            else
                                                validRet = false;
                                            break;
                                        case CExpress.Information.Sign.eq:
                                            if (Double.Parse(addCexpress.information.threshold) >= Double.Parse(retCExpress.information.threshold))
                                            {
                                                retCExpress.information.threshold = addCexpress.information.threshold;
                                                retCExpress.information.sign = CExpress.Information.Sign.eq;
                                                if (hasNewArgument)
                                                {
                                                    retCExpress.argument = addCexpress.argument;
                                                    retCExpress.enableArgument = true;
                                                }
                                                if (hasNewMS)
                                                {
                                                    retCExpress.MS = addCexpress.MS;
                                                    retCExpress.enableMS = true;
                                                }
                                            }
                                            else
                                                validRet = false;
                                            break;
                                        case CExpress.Information.Sign.ge:
                                                retCExpress.information.threshold = Math.Max(Double.Parse(addCexpress.information.threshold), Double.Parse(retCExpress.information.threshold)).ToString();
                                                if (hasNewArgument)
                                                {
                                                    retCExpress.argument = addCexpress.argument;
                                                    retCExpress.enableArgument = true;
                                                }
                                                if (hasNewMS)
                                                {
                                                    retCExpress.MS = addCexpress.MS;
                                                    retCExpress.enableMS = true;
                                                }
                                            break;
                                        case CExpress.Information.Sign.gt:
                                                retCExpress.information.threshold = Math.Max(Double.Parse(addCexpress.information.threshold), Double.Parse(retCExpress.information.threshold)).ToString();
                                                retCExpress.information.sign = CExpress.Information.Sign.gt;
                                                if (hasNewArgument)
                                                {
                                                    retCExpress.argument = addCexpress.argument;
                                                    retCExpress.enableArgument = true;
                                                }
                                                if (hasNewMS)
                                                {
                                                    retCExpress.MS = addCexpress.MS;
                                                    retCExpress.enableMS = true;
                                                }
                                            break;
                                        default:
                                            validRet = false;
                                            break;
                                    }
                                    break;
                                case CExpress.Information.Sign.gt:
                                    switch (addCexpress.information.sign)
                                    {
                                        case CExpress.Information.Sign.lt:
                                            if (Double.Parse(addCexpress.information.threshold) > Double.Parse(retCExpress.information.threshold))
                                            {
                                                retCExpress.information.headThreshold = Double.Parse(retCExpress.information.threshold);
                                                retCExpress.information.tailThreshold = Double.Parse(addCexpress.information.threshold);
                                                retCExpress.information.sign = CExpress.Information.Sign.range;
                                                retCExpress.information.threshold = null;
                                                if (hasNewArgument)
                                                {
                                                    retCExpress.argument = addCexpress.argument;
                                                    retCExpress.enableArgument = true;
                                                }
                                                if (hasNewMS)
                                                {
                                                    retCExpress.MS = addCexpress.MS;
                                                    retCExpress.enableMS = true;
                                                }
                                            }
                                            else
                                                validRet = false;
                                            break;
                                        case CExpress.Information.Sign.le:
                                            if (Double.Parse(addCexpress.information.threshold) > Double.Parse(retCExpress.information.threshold))
                                            {
                                                retCExpress.information.headThreshold = Double.Parse(retCExpress.information.threshold);
                                                retCExpress.information.tailThreshold = Double.Parse(addCexpress.information.threshold) + 0.001;
                                                retCExpress.information.sign = CExpress.Information.Sign.range;
                                                retCExpress.information.threshold = null;
                                                if (hasNewArgument)
                                                {
                                                    retCExpress.argument = addCexpress.argument;
                                                    retCExpress.enableArgument = true;
                                                }
                                                if (hasNewMS)
                                                {
                                                    retCExpress.MS = addCexpress.MS;
                                                    retCExpress.enableMS = true;
                                                }
                                            }
                                            else
                                                validRet = false;
                                            break;
                                        case CExpress.Information.Sign.eq:
                                            if (Double.Parse(addCexpress.information.threshold) > Double.Parse(retCExpress.information.threshold))
                                            {
                                                retCExpress.information.threshold = addCexpress.information.threshold;
                                                retCExpress.information.sign = CExpress.Information.Sign.eq;
                                                if (hasNewArgument)
                                                {
                                                    retCExpress.argument = addCexpress.argument;
                                                    retCExpress.enableArgument = true;
                                                }
                                                if (hasNewMS)
                                                {
                                                    retCExpress.MS = addCexpress.MS;
                                                    retCExpress.enableMS = true;
                                                }
                                            }
                                            else
                                                validRet = false;
                                            break;
                                        case CExpress.Information.Sign.ge:
                                                retCExpress.information.threshold = Math.Max(Double.Parse(addCexpress.information.threshold), Double.Parse(retCExpress.information.threshold)).ToString();
                                                retCExpress.information.sign = CExpress.Information.Sign.ge;
                                                if (hasNewArgument)
                                                {
                                                    retCExpress.argument = addCexpress.argument;
                                                    retCExpress.enableArgument = true;
                                                }
                                                if (hasNewMS)
                                                {
                                                    retCExpress.MS = addCexpress.MS;
                                                    retCExpress.enableMS = true;
                                                }
                                            break;
                                        case CExpress.Information.Sign.gt:
                                                retCExpress.information.threshold =Math.Max(Double.Parse(addCexpress.information.threshold), Double.Parse(retCExpress.information.threshold)).ToString();
                                                if (hasNewArgument)
                                                {
                                                    retCExpress.argument = addCexpress.argument;
                                                    retCExpress.enableArgument = true;
                                                }
                                                if (hasNewMS)
                                                {
                                                    retCExpress.MS = addCexpress.MS;
                                                    retCExpress.enableMS = true;
                                                }
                                            break;
                                        default:
                                            validRet = false;
                                            break;
                                    }
                                    break;
                                case CExpress.Information.Sign.range:
                                    switch (addCexpress.information.sign)
                                    {
                                        case CExpress.Information.Sign.lt:
                                            if (Double.Parse(addCexpress.information.threshold) <= retCExpress.information.headThreshold)
                                            {
                                                validRet = false;
                                            }
                                            else if (Double.Parse(addCexpress.information.threshold) > retCExpress.information.headThreshold && Double.Parse(addCexpress.information.threshold) < retCExpress.information.tailThreshold)
                                            {
                                                retCExpress.information.tailThreshold = Double.Parse(addCexpress.information.threshold);
                                                if (hasNewArgument)
                                                {
                                                    retCExpress.argument = addCexpress.argument;
                                                    retCExpress.enableArgument = true;
                                                }
                                                if (hasNewMS)
                                                {
                                                    retCExpress.MS = addCexpress.MS;
                                                    retCExpress.enableMS = true;
                                                }
                                            }
                                            else
                                            {
                                                if (hasNewArgument)
                                                {
                                                    retCExpress.argument = addCexpress.argument;
                                                    retCExpress.enableArgument = true;
                                                }
                                                if (hasNewMS)
                                                {
                                                    retCExpress.MS = addCexpress.MS;
                                                    retCExpress.enableMS = true;
                                                }
                                            }
                                            break;
                                        case CExpress.Information.Sign.le:
                                            if (Double.Parse(addCexpress.information.threshold) <= retCExpress.information.headThreshold)
                                            {
                                                validRet = false;
                                            }
                                            else if (Double.Parse(addCexpress.information.threshold) > retCExpress.information.headThreshold && Double.Parse(addCexpress.information.threshold) < retCExpress.information.tailThreshold)
                                            {
                                                retCExpress.information.tailThreshold = Double.Parse(addCexpress.information.threshold) + 0.001;
                                                if (hasNewArgument)
                                                {
                                                    retCExpress.argument = addCexpress.argument;
                                                    retCExpress.enableArgument = true;
                                                }
                                                if (hasNewMS)
                                                {
                                                    retCExpress.MS = addCexpress.MS;
                                                    retCExpress.enableMS = true;
                                                }
                                            }
                                            else
                                            {
                                                if (hasNewArgument)
                                                {
                                                    retCExpress.argument = addCexpress.argument;
                                                    retCExpress.enableArgument = true;
                                                }
                                                if (hasNewMS)
                                                {
                                                    retCExpress.MS = addCexpress.MS;
                                                    retCExpress.enableMS = true;
                                                }
                                            }
                                            break;
                                        case CExpress.Information.Sign.eq:
                                            if (Double.Parse(addCexpress.information.threshold) > retCExpress.information.headThreshold && Double.Parse(addCexpress.information.threshold) < retCExpress.information.tailThreshold)
                                            {
                                                retCExpress.information.threshold = addCexpress.information.threshold;
                                                retCExpress.information.sign = CExpress.Information.Sign.eq;
                                                if (hasNewArgument)
                                                {
                                                    retCExpress.argument = addCexpress.argument;
                                                    retCExpress.enableArgument = true;
                                                }
                                                if (hasNewMS)
                                                {
                                                    retCExpress.MS = addCexpress.MS;
                                                    retCExpress.enableMS = true;
                                                }
                                            }
                                            else
                                                validRet = false;
                                            break;
                                        case CExpress.Information.Sign.ge:
                                            if (Double.Parse(addCexpress.information.threshold) <= retCExpress.information.headThreshold)
                                            {
                                                if (hasNewArgument)
                                                {
                                                    retCExpress.argument = addCexpress.argument;
                                                    retCExpress.enableArgument = true;
                                                }
                                                if (hasNewMS)
                                                {
                                                    retCExpress.MS = addCexpress.MS;
                                                    retCExpress.enableMS = true;
                                                }
                                            }
                                            else if (Double.Parse(addCexpress.information.threshold) > retCExpress.information.headThreshold && Double.Parse(addCexpress.information.threshold) < retCExpress.information.tailThreshold)
                                            {
                                                retCExpress.information.headThreshold = Double.Parse(addCexpress.information.threshold) - 0.01;
                                                if (hasNewArgument)
                                                {
                                                    retCExpress.argument = addCexpress.argument;
                                                    retCExpress.enableArgument = true;
                                                }
                                                if (hasNewMS)
                                                {
                                                    retCExpress.MS = addCexpress.MS;
                                                    retCExpress.enableMS = true;
                                                }
                                            }
                                            else
                                                validRet = false;
                                            break;
                                        case CExpress.Information.Sign.gt:
                                            if (Double.Parse(addCexpress.information.threshold) < retCExpress.information.headThreshold)
                                            {
                                                if (hasNewArgument)
                                                {
                                                    retCExpress.argument = addCexpress.argument;
                                                    retCExpress.enableArgument = true;
                                                }
                                                if (hasNewMS)
                                                {
                                                    retCExpress.MS = addCexpress.MS;
                                                    retCExpress.enableMS = true;
                                                }
                                            }
                                            else if (Double.Parse(addCexpress.information.threshold) > retCExpress.information.headThreshold && Double.Parse(addCexpress.information.threshold) < retCExpress.information.tailThreshold)
                                            {
                                                retCExpress.information.headThreshold = Double.Parse(addCexpress.information.threshold);
                                                if (hasNewArgument)
                                                {
                                                    retCExpress.argument = addCexpress.argument;
                                                    retCExpress.enableArgument = true;
                                                }
                                                if (hasNewMS)
                                                {
                                                    retCExpress.MS = addCexpress.MS;
                                                    retCExpress.enableMS = true;
                                                }
                                            }
                                            else
                                                validRet = false;
                                            break;
                                        default:
                                            validRet = false;
                                            break;
                                    }
                                    break;
                                case CExpress.Information.Sign.be:
                                    switch (addCexpress.information.sign)
                                    {
                                        case CExpress.Information.Sign.lt:
                                            validRet = false;
                                            break;
                                        case CExpress.Information.Sign.le:
                                            validRet = false;
                                            break;
                                        case CExpress.Information.Sign.eq:
                                            validRet = false;
                                            break;
                                        case CExpress.Information.Sign.ge:
                                            validRet = false;
                                            break;
                                        case CExpress.Information.Sign.gt:
                                            validRet = false;
                                            break;
                                        case CExpress.Information.Sign.range:
                                            validRet = false;
                                            break;
                                        case CExpress.Information.Sign.be:
                                            if (addCexpress.information.threshold != retCExpress.information.threshold)
                                            {
                                                validRet = false;
                                            }
                                            break;
                                        case CExpress.Information.Sign.notbe:
                                            if (addCexpress.information.threshold == retCExpress.information.threshold)
                                            {
                                                validRet = false;
                                            }
                                            break;
                                        default:
                                            break;
                                    }
                                    break;
                                case CExpress.Information.Sign.notbe:
                                    switch (addCexpress.information.sign)
                                    {
                                        case CExpress.Information.Sign.lt:
                                            validRet = false;
                                            break;
                                        case CExpress.Information.Sign.le:
                                            validRet = false;
                                            break;
                                        case CExpress.Information.Sign.eq:
                                            validRet = false;
                                            break;
                                        case CExpress.Information.Sign.ge:
                                            validRet = false;
                                            break;
                                        case CExpress.Information.Sign.gt:
                                            validRet = false;
                                            break;
                                        case CExpress.Information.Sign.range:
                                            validRet = false;
                                            break;
                                        case CExpress.Information.Sign.be:
                                            if (addCexpress.information.threshold == retCExpress.information.threshold)
                                            {
                                                validRet = false;
                                            }
                                            else
                                            {
                                                retCExpress.information.sign = CExpress.Information.Sign.be;
                                                retCExpress.information.threshold = addCexpress.information.threshold;
                                                retCExpress.information.notbeThresholds = null;
                                            }
                                            break;
                                        case CExpress.Information.Sign.notbe:
                                            if (retCExpress.information.notbeThresholds != null
                                                && !retCExpress.information.notbeThresholds.Contains(addCexpress.information.threshold))
                                            {
                                                retCExpress.information.notbeThresholds.Add(addCexpress.information.threshold);
                                            }
                                            break;
                                        default:
                                            break;
                                    }
                                    break;
                                default:
                                    break;
                            }
                        }
                        else
                        {
                            validRet = false;
                        }

                    }
                }
            }

        }

        protected override void query()
        {
            MainModel.CExpressList.Clear();
            MainModel.CExpressRet_and.childCExpressList.Clear();
            MainModel.FormAction = CustomExpressionForm.FormAction.Cancel;
            if (MainModel.DTDataManager.TestPointCount == 0)
            {
                XtraMessageBox.Show("地图上不存在采样点！");
                return;
            }

            CustomExpressionForm frm = MainModel.CreateResultForm(typeof(CustomExpressionForm)) as CustomExpressionForm;
            frm.btnApply.Click -= new EventHandler(ApplyCExpressList);
            frm.btnApply.Click += new EventHandler(ApplyCExpressList);
            frm.btnOK.Click -= new EventHandler(RunCExpressList);
            frm.btnOK.Click += new EventHandler(RunCExpressList);
            frm.Visible = true;
            frm.BringToFront();
        }


        private void RunCExpressList(object sender, EventArgs e)
        {
            ApplyCExpressList(null, null);
            //MainModel.TempPointList.Clear();
            if (MainModel.MainForm.GetMapForm()!=null)
            {
                MapDTLayer dtLayer = MainModel.MainForm.GetMapForm().GetDTLayer();
                dtLayer.Invalidate();
            }

            #region 确定修改地图上的采样点（原有）
            /*
            if (MainModel.FormAction == CustomExpressionForm.FormAction.Ok)
            {
                List<TestPoint> desiredTestpointList = new List<TestPoint>();  //被多次统计的同一个采样点不记录在内
                List<TestPoint> retTestpointList = new List<TestPoint>(); //结果采样点集
                RefillTestpoints();
                if (isFirstTimeApply)
                {
                    foreach (DTFileDataManager dfManager in MainModel.DTDataManager.FileDataManagers)
                    {
                        foreach (TestPoint tp in dfManager.TestPoints)
                        {
                            MainModel.TempPointList.Add(tp);
                            //curTempTestpointList.Add(tp);
                        }
                    }
                }
                MainModel.DTDataManager.Clear();

                if (isLogicAnd())
                {
                    bool isValidRet = true;
                    MergeCExpressList(out isValidRet);
                    if (!isValidRet)
                    {
                        XtraMessageBox.Show("公式进行“与”合并，结果合集为空，请检查公式");
                        return;
                    }

                    bool isFirstCe = true;
                    foreach (CExpressRet_and.childCExpress ce in MainModel.CExpressRet_and.childCExpressList)
                    {
                        if (MainModel.CExpressRet_and.childCExpressList.IndexOf(ce) == 0)
                            isFirstCe = true;
                        else
                            isFirstCe = false;
                        foreach (TestPoint tp in MainModel.TempPointList)
                        {
                            foreach (DTParameter dtParameter in tp.Parameters.Map.Keys)
                            {
                                if (dtParameter.Info.Name.Contains(ce.information.name))
                                //if (ce.information.name == dtParameter.Info.Name)
                                {
                                    float valueF = -255;//初始为无效值
                                    string valueStr = null;
                                    bool isStr = false;
                                    if (getTestPointParamString(tp, ce.systemName, ce.information.name, dtParameter.ArrayIndex) != null)
                                    {
                                        if (getTestPointParamString(tp, ce.systemName, ce.information.name, dtParameter.ArrayIndex) is float)
                                            valueF = (float)getTestPointParamString(tp, ce.systemName, ce.information.name, dtParameter.ArrayIndex);
                                        else
                                        {
                                            valueStr = getTestPointParamString(tp, ce.systemName, ce.information.name, dtParameter.ArrayIndex).ToString();
                                            isStr = true;
                                        }
                                    }

                                    if (!isStr)
                                    {
                                        switch (ce.information.sign)
                                        {
                                            case CExpress.Information.Sign.lt:
                                                if (valueF < float.Parse(ce.information.threshold))
                                                {
                                                    if (isValidArgumentOrMs(ce, tp, dtParameter))
                                                    {
                                                        if (!desiredTestpointList.Contains(tp) && isFirstCe)
                                                        {
                                                            desiredTestpointList.Add(tp);
                                                        }
                                                        if (desiredTestpointList.Contains(tp) && !isFirstCe)
                                                        {
                                                            retTestpointList.Add(tp);
                                                        }
                                                    }
                                                }
                                                break;
                                            case CExpress.Information.Sign.le:
                                                if (valueF <= float.Parse(ce.information.threshold))
                                                    if (isValidArgumentOrMs(ce, tp, dtParameter))
                                                    {
                                                        if (!desiredTestpointList.Contains(tp) && isFirstCe)
                                                        {
                                                            desiredTestpointList.Add(tp);
                                                        }
                                                        if (desiredTestpointList.Contains(tp) && !isFirstCe)
                                                        {
                                                            retTestpointList.Add(tp);
                                                        }
                                                    }
                                                break;
                                            case CExpress.Information.Sign.eq:
                                                if (valueF == float.Parse(ce.information.threshold))
                                                    if (isValidArgumentOrMs(ce, tp, dtParameter))
                                                    {
                                                        if (!desiredTestpointList.Contains(tp) && isFirstCe)
                                                        {
                                                            desiredTestpointList.Add(tp);
                                                        }
                                                        if (desiredTestpointList.Contains(tp) && !isFirstCe)
                                                        {
                                                            retTestpointList.Add(tp);
                                                        }
                                                    }
                                                break;
                                            case CExpress.Information.Sign.ge:
                                                if (valueF >= float.Parse(ce.information.threshold))
                                                    if (isValidArgumentOrMs(ce, tp, dtParameter))
                                                    {
                                                        if (!desiredTestpointList.Contains(tp) && isFirstCe)
                                                        {
                                                            desiredTestpointList.Add(tp);
                                                        }
                                                        if (desiredTestpointList.Contains(tp) && !isFirstCe)
                                                        {
                                                            retTestpointList.Add(tp);
                                                        }
                                                    }
                                                break;
                                            case CExpress.Information.Sign.gt:
                                                if (valueF > float.Parse(ce.information.threshold))
                                                    if (isValidArgumentOrMs(ce, tp, dtParameter))
                                                    {
                                                        if (!desiredTestpointList.Contains(tp) && isFirstCe)
                                                        {
                                                            desiredTestpointList.Add(tp);
                                                        }
                                                        if (desiredTestpointList.Contains(tp) && !isFirstCe)
                                                        {
                                                            retTestpointList.Add(tp);
                                                        }
                                                    }
                                                break;
                                            case CExpress.Information.Sign.range:
                                                if (valueF > ce.information.headThreshold && valueF < ce.information.tailThreshold)
                                                    if (isValidArgumentOrMs(ce, tp, dtParameter))
                                                    {
                                                        if (!desiredTestpointList.Contains(tp) && isFirstCe)
                                                        {
                                                            desiredTestpointList.Add(tp);
                                                        }
                                                        if (desiredTestpointList.Contains(tp) && !isFirstCe)
                                                        {
                                                            retTestpointList.Add(tp);
                                                        }
                                                    }
                                                break;
                                            default:
                                                break;
                                        }
                                    }
                                    else
                                    {
                                        if (ce.information.sign == CExpress.Information.Sign.be)
                                        {
                                            if (valueStr == ce.information.threshold)
                                            {
                                                if (isValidArgumentOrMs(ce, tp, dtParameter))
                                                {
                                                    if (!desiredTestpointList.Contains(tp) && isFirstCe)
                                                    {
                                                        desiredTestpointList.Add(tp);
                                                    }
                                                    if (desiredTestpointList.Contains(tp) && !isFirstCe)
                                                    {
                                                        retTestpointList.Add(tp);
                                                    }
                                                }
                                            }
                                        }
                                        else if (ce.information.sign == CExpress.Information.Sign.notbe)
                                        {
                                            if (valueStr != ce.information.threshold)
                                            {
                                                if (isValidArgumentOrMs(ce, tp, dtParameter))
                                                {
                                                    if (!desiredTestpointList.Contains(tp) && isFirstCe)
                                                    {
                                                        desiredTestpointList.Add(tp);
                                                    }
                                                    if (desiredTestpointList.Contains(tp) && !isFirstCe)
                                                    {
                                                        retTestpointList.Add(tp);
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }

                    if (isFirstCe)
                    {
                        retTestpointList = desiredTestpointList;
                    }

                    foreach (TestPoint tp in retTestpointList)
                    {
                        MainModel.DTDataManager.Add(tp);
                    }

                }
                else
                {
                    foreach (CExpress ce in MainModel.CExpressList)
                    {
                        foreach (TestPoint tp in MainModel.TempPointList)
                        {
                            foreach (DTParameter dtParameter in tp.Parameters.Map.Keys)
                            {
                                if (dtParameter.Info.Name.Contains(ce.information.name))
                                //if (ce.information.name == dtParameter.Info.Name)
                                {
                                    float valueF = -255;//初始为无效值
                                    string valueStr = null;
                                    bool isStr = false;
                                    if (getTestPointParamString(tp, ce.systemName, ce.information.name, dtParameter.ArrayIndex) != null)
                                    {
                                        if (getTestPointParamString(tp, ce.systemName, ce.information.name, dtParameter.ArrayIndex) is float)
                                            valueF = (float)getTestPointParamString(tp, ce.systemName, ce.information.name, dtParameter.ArrayIndex);
                                        else
                                        {
                                            valueStr = getTestPointParamString(tp, ce.systemName, ce.information.name, dtParameter.ArrayIndex).ToString();
                                            isStr = true;
                                        }
                                    }

                                    if (!isStr)
                                    {
                                        switch (ce.information.sign)
                                        {
                                            case CExpress.Information.Sign.lt:
                                                if (valueF < float.Parse(ce.information.threshold))
                                                {
                                                    if (isValidArgumentOrMs(ce, tp, dtParameter))
                                                        if (!desiredTestpointList.Contains(tp))
                                                        {
                                                            desiredTestpointList.Add(tp);
                                                            MainModel.DTDataManager.Add(tp);
                                                        }
                                                }
                                                break;
                                            case CExpress.Information.Sign.le:
                                                if (valueF <= float.Parse(ce.information.threshold))
                                                    if (isValidArgumentOrMs(ce, tp, dtParameter))
                                                        if (!desiredTestpointList.Contains(tp))
                                                        {
                                                            desiredTestpointList.Add(tp);
                                                            MainModel.DTDataManager.Add(tp);
                                                        }
                                                break;
                                            case CExpress.Information.Sign.eq:
                                                if (valueF == float.Parse(ce.information.threshold))
                                                    if (isValidArgumentOrMs(ce, tp, dtParameter))
                                                        if (!desiredTestpointList.Contains(tp))
                                                        {
                                                            desiredTestpointList.Add(tp);
                                                            MainModel.DTDataManager.Add(tp);
                                                        }
                                                break;
                                            case CExpress.Information.Sign.ge:
                                                if (valueF >= float.Parse(ce.information.threshold))
                                                    if (isValidArgumentOrMs(ce, tp, dtParameter))
                                                        if (!desiredTestpointList.Contains(tp))
                                                        {
                                                            desiredTestpointList.Add(tp);
                                                            MainModel.DTDataManager.Add(tp);
                                                        }
                                                break;
                                            case CExpress.Information.Sign.gt:
                                                if (valueF > float.Parse(ce.information.threshold))
                                                    if (isValidArgumentOrMs(ce, tp, dtParameter))
                                                        if (!desiredTestpointList.Contains(tp))
                                                        {
                                                            desiredTestpointList.Add(tp);
                                                            MainModel.DTDataManager.Add(tp);
                                                        }
                                                break;
                                            case CExpress.Information.Sign.range:
                                                if (valueF > ce.information.headThreshold && valueF < ce.information.tailThreshold)
                                                    if (isValidArgumentOrMs(ce, tp, dtParameter))
                                                        if (!desiredTestpointList.Contains(tp))
                                                        {
                                                            desiredTestpointList.Add(tp);
                                                            MainModel.DTDataManager.Add(tp);
                                                        }
                                                break;
                                            default:
                                                break;
                                        }
                                    }
                                    else
                                    {
                                        if (ce.information.sign == CExpress.Information.Sign.be)
                                        {
                                            if (valueStr == ce.information.threshold)
                                            {
                                                if (isValidArgumentOrMs(ce, tp, dtParameter))
                                                    if (!desiredTestpointList.Contains(tp))
                                                    {
                                                        desiredTestpointList.Add(tp);
                                                        MainModel.DTDataManager.Add(tp);
                                                    }
                                            }
                                        }
                                        else if (ce.information.sign == CExpress.Information.Sign.notbe)
                                        {
                                            if (valueStr != ce.information.threshold)
                                            {
                                                if (isValidArgumentOrMs(ce, tp, dtParameter))
                                                    if (!desiredTestpointList.Contains(tp))
                                                    {
                                                        desiredTestpointList.Add(tp);
                                                        MainModel.DTDataManager.Add(tp);
                                                    }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                MainModel.TempPointList.Clear();
            }
             * */
            #endregion
        }

        private void ApplyCExpressList(object sender, EventArgs e)
        {
            List<TestPoint> desiredTestpointList = new List<TestPoint>();  //被多次统计的同一个采样点不记录在内
            List<TestPoint> retTestpointList = new List<TestPoint>(); //结果采样点集
            RefillTestpoints();
            foreach (DTFileDataManager dfManager in MainModel.DTDataManager.FileDataManagers)
            {
                foreach (TestPoint tp in dfManager.TestPoints)
                {
                    if (!MainModel.TempPointList.Contains(tp))
                        MainModel.TempPointList.Add(tp);//保存当前所有采样点
                }
            }
            foreach (DTFileDataManager fileDataManager in MainModel.DTDataManager.FileDataManagers)
            {
                fileDataManager.TestPoints.Clear();
            }


            if (isLogicAnd())
            {
                bool isValidRet = true;
                MergeCExpressList(out isValidRet);
                if (!isValidRet)
                {
                    XtraMessageBox.Show("公式进行“与”合并，结果合集为空，请检查公式");
                    RefillTestpoints();
                    MainModel.TempPointList.Clear();
                    return;
                }

                bool isFirstCe = true;
                foreach (CExpressRet_and.childCExpress ce in MainModel.CExpressRet_and.childCExpressList)
                {
                    if (MainModel.CExpressRet_and.childCExpressList.IndexOf(ce) == 0)
                        isFirstCe = true;
                    else
                        isFirstCe = false;
                    foreach (TestPoint tp in MainModel.TempPointList)
                    {
                        foreach (DTParameter dtParameter in tp.Parameters.Map.Keys)
                        {
                            if (dtParameter.Info.Name.Contains(ce.information.name))
                            //if (ce.information.name == dtParameter.Info.Name)
                            {
                                float valueF = -255;//初始为无效值
                                string valueStr = null;
                                bool isStr = false;
                                if (getTestPointParamString(tp, ce.systemName, ce.information.name, dtParameter.ArrayIndex) != null)
                                {
                                    if (getTestPointParamString(tp, ce.systemName, ce.information.name, dtParameter.ArrayIndex) is float)
                                        valueF = (float)getTestPointParamString(tp, ce.systemName, ce.information.name, dtParameter.ArrayIndex);
                                    else
                                    {
                                        valueStr = getTestPointParamString(tp, ce.systemName, ce.information.name, dtParameter.ArrayIndex).ToString();
                                        isStr = true;
                                    }
                                }

                                if (!isStr)
                                {
                                    switch (ce.information.sign)
                                    {
                                        case CExpress.Information.Sign.lt:
                                            if (valueF < float.Parse(ce.information.threshold))
                                            {
                                                addValidTP(desiredTestpointList, retTestpointList, isFirstCe, ce, tp, dtParameter, valueStr);
                                            }
                                            break;
                                        case CExpress.Information.Sign.le:
                                            if (valueF <= float.Parse(ce.information.threshold))
                                                addValidTP(desiredTestpointList, retTestpointList, isFirstCe, ce, tp, dtParameter, valueStr);
                                            break;
                                        case CExpress.Information.Sign.eq:
                                            if (valueF == float.Parse(ce.information.threshold))
                                                addValidTP(desiredTestpointList, retTestpointList, isFirstCe, ce, tp, dtParameter, valueStr);
                                            break;
                                        case CExpress.Information.Sign.ge:
                                            if (valueF >= float.Parse(ce.information.threshold))
                                                addValidTP(desiredTestpointList, retTestpointList, isFirstCe, ce, tp, dtParameter, valueStr);
                                            break;
                                        case CExpress.Information.Sign.gt:
                                            if (valueF > float.Parse(ce.information.threshold))
                                                addValidTP(desiredTestpointList, retTestpointList, isFirstCe, ce, tp, dtParameter, valueStr);
                                            break;
                                        case CExpress.Information.Sign.range:
                                            if (valueF > ce.information.headThreshold && valueF < ce.information.tailThreshold)
                                                addValidTP(desiredTestpointList, retTestpointList, isFirstCe, ce, tp, dtParameter, valueStr);
                                            break;
                                        default:
                                            break;
                                    }
                                }
                                else
                                {
                                    if (ce.information.sign == CExpress.Information.Sign.be)
                                    {
                                        addValidTP(desiredTestpointList, retTestpointList, isFirstCe, ce, tp, dtParameter, valueStr);
                                    }
                                    else if (ce.information.sign == CExpress.Information.Sign.notbe)
                                    {
                                        addValidTP(desiredTestpointList, retTestpointList, isFirstCe, ce, tp, dtParameter, valueStr);
                                    }
                                }
                            }
                        }
                    }
                }

                if (isFirstCe)
                {
                    retTestpointList = desiredTestpointList;
                }

                foreach (TestPoint tp in retTestpointList)
                {
                    MainModel.DTDataManager.Add(tp);
                }
            }
            else
            {
                foreach (CExpress ce in MainModel.CExpressList)
                {
                    foreach (TestPoint tp in MainModel.TempPointList)
                    {
                        foreach (DTParameter dtParameter in tp.Parameters.Map.Keys)
                        {
                            if (dtParameter.Info.Name.Contains(ce.information.name))
                            //if (ce.information.name == dtParameter.Info.Name)
                            {
                                float valueF = -255;//初始为无效值
                                string valueStr = null;
                                bool isStr = false;
                                if (getTestPointParamString(tp, ce.systemName, ce.information.name, dtParameter.ArrayIndex) != null)
                                {
                                    if (getTestPointParamString(tp, ce.systemName, ce.information.name, dtParameter.ArrayIndex) is float)
                                        valueF = (float)getTestPointParamString(tp, ce.systemName, ce.information.name, dtParameter.ArrayIndex);
                                    else
                                    {
                                        valueStr = getTestPointParamString(tp, ce.systemName, ce.information.name, dtParameter.ArrayIndex).ToString();
                                        isStr = true;
                                    }
                                }

                                if (!isStr)
                                {
                                    setNumericInfo(desiredTestpointList, ce, tp, dtParameter, valueF);
                                }
                                else
                                {
                                    setStrInfo(desiredTestpointList, ce, tp, dtParameter, valueStr);
                                }
                            }
                        }
                    }
                }
            }

            if (MainModel.MainForm.GetMapForm() != null)
            {
                MapDTLayer dtLayer = MainModel.MainForm.GetMapForm().GetDTLayer();
                dtLayer.Invalidate();
            }
        }

        private void addValidTP(List<TestPoint> desiredTestpointList, List<TestPoint> retTestpointList, bool isFirstCe, 
            CExpressRet_and.childCExpress ce, TestPoint tp, DTParameter dtParameter, string valueStr)
        {
            if (valueStr != ce.information.threshold)
            {
                bool isValid = isValidArgumentOrMs(ce, tp, dtParameter);
                if (isValid)
                {
                    if (!desiredTestpointList.Contains(tp) && isFirstCe)
                    {
                        desiredTestpointList.Add(tp);
                    }
                    if (desiredTestpointList.Contains(tp) && !isFirstCe)
                    {
                        retTestpointList.Add(tp);
                    }
                }
            }
        }

        private void setNumericInfo(List<TestPoint> desiredTestpointList, CExpress ce, TestPoint tp, DTParameter dtParameter, float valueF)
        {
            switch (ce.information.sign)
            {
                case CExpress.Information.Sign.lt:
                    if (valueF < float.Parse(ce.information.threshold))
                    {
                        addValidTP(desiredTestpointList, ce, tp, dtParameter);
                    }
                    break;
                case CExpress.Information.Sign.le:
                    if (valueF <= float.Parse(ce.information.threshold))
                        addValidTP(desiredTestpointList, ce, tp, dtParameter);
                    break;
                case CExpress.Information.Sign.eq:
                    if (valueF == float.Parse(ce.information.threshold))
                        addValidTP(desiredTestpointList, ce, tp, dtParameter);
                    break;
                case CExpress.Information.Sign.ge:
                    if (valueF >= float.Parse(ce.information.threshold))
                        addValidTP(desiredTestpointList, ce, tp, dtParameter);
                    break;
                case CExpress.Information.Sign.gt:
                    if (valueF > float.Parse(ce.information.threshold))
                        addValidTP(desiredTestpointList, ce, tp, dtParameter);
                    break;
                case CExpress.Information.Sign.range:
                    if (valueF > ce.information.headThreshold && valueF < ce.information.tailThreshold)
                        addValidTP(desiredTestpointList, ce, tp, dtParameter);
                    break;
                default:
                    break;
            }
        }

        private void setStrInfo(List<TestPoint> desiredTestpointList, CExpress ce, TestPoint tp, DTParameter dtParameter, string valueStr)
        {
            if (ce.information.sign == CExpress.Information.Sign.be)
            {
                if (valueStr == ce.information.threshold)
                {
                    addValidTP(desiredTestpointList, ce, tp, dtParameter);
                }
            }
            else if (ce.information.sign == CExpress.Information.Sign.notbe)
            {
                if (valueStr != ce.information.threshold)
                {
                    addValidTP(desiredTestpointList, ce, tp, dtParameter);
                }
            }
            else
            {
                //
            }
        }

        private void addValidTP(List<TestPoint> desiredTestpointList, CExpress ce, TestPoint tp, DTParameter dtParameter)
        {
            if (isValidArgumentOrMs(ce, tp, dtParameter) && !desiredTestpointList.Contains(tp))
            {
                desiredTestpointList.Add(tp);
                MainModel.DTDataManager.Add(tp);
            }
        }

        /// <summary>
        /// 重新显示所有原先查询出的采样点
        /// </summary>
        private void RefillTestpoints()
        {
            if (MainModel.TempPointList.Count>0)
            {
                foreach (TestPoint tp in MainModel.TempPointList)
                {
                    MainModel.DTDataManager.Add(tp);
                }
            }
        }

        private bool isValidArgumentOrMs(CExpress ce,TestPoint tp,DTParameter dtp)
        {
            if (!ce.enableArgument && !ce.enableMS)
            {
                return true;
            }
            else if (ce.enableArgument && !ce.enableMS)
            {
                if (ce.argument == dtp.ArrayIndex)
                    return true;
                else
                    return false;
            }
            else if (ce.enableArgument && ce.enableMS)
            {
                if (ce.argument == dtp.ArrayIndex && ce.MS == tp.MS)
                    return true;
                else
                    return false;
            }
            return false;
        }

        /**
        private float? getValue(TestPoint testPoint, DTParameter dtParameter)
        {
            if (dtParameter == null)
            {
                return null;
            }
            if (DTParameterManager.GetInstance().CanConvertToFloat(testPoint[dtParameter.Info.Name, dtParameter.ArrayIndex], dtParameter.Info.ValueType))
            {
                return DTParameterManager.GetInstance().ConvertToFloat(testPoint[dtParameter.Info.Name, dtParameter.ArrayIndex], dtParameter.Info.ValueType);
            }
            return null;
        }
        */

        private object getTestPointParamString(TestPoint testPoint, string systemName, string paramName, int arrayIndex)
        {
            if (testPoint != null)
            {
                DTDisplayParameter displayParam = DTDisplayParameterManager.GetInstance()[systemName, paramName, arrayIndex];
                if (displayParam != null)
                {
                    DTParameter param = displayParam.Info.ParamInfo[displayParam.ArrayIndex];
                    object objectValue = testPoint[param];
                    if (objectValue != null)
                    {
                        return getValidObjectValue(displayParam, param, objectValue);
                    }
                }
            }
            return null;
        }

        private static object getValidObjectValue(DTDisplayParameter displayParam, DTParameter param, object objectValue)
        {
            if (DTParameterManager.GetInstance().CanConvertToFloat(objectValue, param.Info.ValueType)
                && displayParam.Info.ValueMin != displayParam.Info.ValueMax)
            {
                float value = DTParameterManager.GetInstance().ConvertToFloat(objectValue, param.Info.ValueType);
                if (value < displayParam.Info.ValueMin || value > displayParam.Info.ValueMax)
                {
                    return null;
                }
            }
            float valueF;
            if (float.TryParse(objectValue.ToString(), out valueF))
            {
                return valueF;
            }
            return objectValue;
        }
    }
}
