﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTDIYCellAbnormalAngleByRegion : DIYSampleByRegion
    {
        private readonly List<BTS> btsList;
        private readonly List<Cell> cellList;
        private readonly List<TDNodeB> tdBtsList;
        private readonly List<TDCell> tdCellList;
        private readonly List<LTECell> lteCellList;
        private readonly List<NRCell> nrCellList;
        private int rxlevMin;
        private int distanceMin;
        private int angleMin;
        private readonly List<AbnormalAngleCellInfo> angleCellInfoList;
        private readonly Dictionary<string, AbnormalAngleCellInfo> cellAngleCellInfoDic;

        public ZTDIYCellAbnormalAngleByRegion(MainModel mainModel)
            : base(mainModel)
        {
            btsList = new List<BTS>();
            cellList = new List<Cell>();
            tdBtsList = new List<TDNodeB>();
            tdCellList = new List<TDCell>();
            lteCellList = new List<LTECell>();
            nrCellList = new List<NRCell>();
            angleCellInfoList = new List<AbnormalAngleCellInfo>();
            cellAngleCellInfoDic = new Dictionary<string, AbnormalAngleCellInfo>();
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 19000, 19036, this.Name);
        }

        //查询入口
        protected override void query()
        {
            if (!getConditionBeforeQuery())
            {
                return;
            }
            curSelDIYSampleGroup = getCurSelDIYSampleGroupPrepare();
            if (curSelDIYSampleGroup == null)
            {
                return;
            }
            WaitBox.CanCancel = true;
            WaitBox.Text = "正在查询...";
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                dataClear();

                foreach (int districtID in condition.DistrictIDs)
                {
                    queryDistrictData(districtID);
                }

                afterRecieveAllData();

                FireShowFormAfterQuery();//在该方法最后设置了默认的指标

                //加多一个对ThemeName内容的判断，避免默认指标被清空
                if (curSelDIYSampleGroup.ThemeName != null && curSelDIYSampleGroup.ThemeName != "")
                {
                    MainModel.FireSetDefaultMapSerialTheme(curSelDIYSampleGroup.ThemeName);
                }
                MainModel.FireDTDataChanged(this);
            }
            catch (Exception e)
            {
                System.Windows.Forms.MessageBox.Show(e.Source + Environment.NewLine + e.Message);
            }
            finally
            {
                clientProxy.Close();
            }
        }

        protected virtual void queryDistrictData(int districtID)
        {
            ClientProxy clientProxy = new ClientProxy();
            if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, districtID) != ConnectResult.Success)
            {
                ErrorInfo = "连接服务器失败!";
                return;
            }
            try
            {
                WaitBox.Show("开始统计数据...", queryInThread, clientProxy);
            }
            finally
            {
                clientProxy.Close();
            }
        }

        protected override MasterCom.RAMS.Model.Interface.DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            List<object> columnsDef = new List<object>();
            Dictionary<string, object> param = new Dictionary<string, object>();
            param["param_name"] = "TD_PCCPCH_RSCP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "TD_SCell_LAC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "TD_SCell_CI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "TD_SCell_UARFCN";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "TD_SCell_CPI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "TD_BLER";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "RxLevSub";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "RxQualSub";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "LAC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "CI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "BCCH";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "BSIC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "W_Reference_RSCP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "W_SysLAI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "W_SysCellID";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "W_frequency";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "W_Reference_PSC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);
            //LTE
            param = new Dictionary<string, object>();
            param["param_name"] = "lte_TAC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_ECI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_RSRP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_SINR";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            //NR
            param = new Dictionary<string, object>();
            param["param_name"] = "NR_TAC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "NR_NCI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "NR_SS_RSRP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "NR_SS_SINR";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            Dictionary<string, object> tmpDic = new Dictionary<string, object>();
            tmpDic.Add("name", (object)"TD_PCCPCH_RSCP");
            tmpDic.Add("themeName", (object)"TD_PCCPCH_RSCP");
            tmpDic.Add("columnsDef", (object)columnsDef);

            DIYSampleGroup group = new DIYSampleGroup();
            group.Param = tmpDic;
            return group;
        }

        protected override bool getConditionBeforeQuery()
        {
            ZTDIYCellAbnormalAngleSettingDlg dlg = new ZTDIYCellAbnormalAngleSettingDlg();
            if (dlg.ShowDialog() != System.Windows.Forms.DialogResult.OK)
            {
                return false;
            }
            rxlevMin = dlg.RxlevMin;
            distanceMin = dlg.DistanceMin;
            angleMin = dlg.AngleMin;
            return true;
        }

        private void dataClear()
        {
            btsList.Clear();
            cellList.Clear();
            tdBtsList.Clear();
            tdCellList.Clear();
            lteCellList.Clear();
            nrCellList.Clear();
            angleCellInfoList.Clear();
            cellAngleCellInfoDic.Clear();
        }

        protected override bool isValidTestPoint(TestPoint tp)
        {
            float? rxlev = null;
            if (tp is TestPointDetail)
            {
                short? rxlevSub = (short?)tp["RxLevSub"];
                rxlev = rxlevSub;
            }
            else if (tp is TDTestPointDetail)
            {
                float? rscp = (float?)tp["TD_PCCPCH_RSCP"];
                rxlev = rscp;
            }
            else if (tp is WCDMATestPointDetail)
            {
                float? rscp = (float?)tp["W_Reference_RSCP"];
                rxlev = rscp;
            }
            else if (tp is LTETestPointDetail)
            {
                rxlev = (float?)tp["lte_RSRP"];
            }
            else if (tp is TestPoint_NR)
            {
                rxlev = (float?)tp["NR_SS_RSRP"];
            }
            return rxlev != null && rxlev >= -140 && rxlev <= -10 && rxlev >= rxlevMin;
        }

        protected virtual void afterRecieveAllData(params object[] reservedParams)
        {
            foreach (AbnormalAngleCellInfo info in angleCellInfoList)
            {
                info.CalcResult();
            }
        }

        protected override void FireShowFormAfterQuery()
        {
            if (angleCellInfoList.Count <= 0 || angleCellInfoList[0].AbnormalAnglePointInfoList.Count <= 0)
            {
                return;
            }
            if (angleCellInfoList[0].AbnormalAnglePointInfoList[0].testPoint is TestPointDetail)
            {
                curSelDIYSampleGroup.ThemeName = "GSM RxLevSub";
            }
            else if (angleCellInfoList[0].AbnormalAnglePointInfoList[0].testPoint is TDTestPointDetail)
            {
                curSelDIYSampleGroup.ThemeName = "TD_PCCPCH_RSCP";
            }
            else if (angleCellInfoList[0].AbnormalAnglePointInfoList[0].testPoint is WCDMATestPointDetail)
            {
                curSelDIYSampleGroup.ThemeName = "W_Reference_RSCP";
            }
            else if (angleCellInfoList[0].AbnormalAnglePointInfoList[0].testPoint is LTETestPointDetail)
            {
                curSelDIYSampleGroup.ThemeName = "lte_RSRP";
            }
            else if (angleCellInfoList[0].AbnormalAnglePointInfoList[0].testPoint is TestPoint_NR)
            {
                curSelDIYSampleGroup.ThemeName = "NR_SS_RSRP";
            }
            else
            {
                curSelDIYSampleGroup.ThemeName = "";
            }
            object obj = MainModel.GetInstance().GetObjectFromBlackboard(typeof(ZTDIYCellAbnormalAngleInfoForm).FullName);
            ZTDIYCellAbnormalAngleInfoForm form = obj == null ? null : obj as ZTDIYCellAbnormalAngleInfoForm;
            if (form == null || form.IsDisposed)
            {
                form = new ZTDIYCellAbnormalAngleInfoForm(MainModel.GetInstance());
            }
            form.FillData(angleCellInfoList);
            form.Visible = false;
            form.Show(MainModel.GetInstance().MainForm);
        }

        protected override void doWithDTData(MasterCom.RAMS.Model.TestPoint tp)
        {
            try
            {
                if (tp is TestPointDetail)
                {
                    deal2GPoint(tp);
                }
                else if (tp is TDTestPointDetail)
                {
                    dealTDPoint(tp);
                }
                else if (tp is WCDMATestPointDetail)
                {
                    dealWPoint(tp);
                }
                else if (tp is LTETestPointDetail)
                {
                    dealLTEPoint(tp);
                }
                else if (tp is TestPoint_NR)
                {
                    dealNRPoint(tp);
                }
            }
            catch
            {
                //continue
            }
        }

        private void deal2GPoint(TestPoint tp)
        {
            short? rxlev = (short?)tp["RxLevSub"];
            int? lac = (int?)tp["LAC"];
            int? ci = (int?)tp["CI"];
            Cell cell = CellManager.GetInstance().GetCell(tp.DateTime, (ushort)lac, (ushort)ci);
            if (cell != null)
            {
                double distance = cell.GetDistance(tp.Longitude, tp.Latitude);
                addAbnormalAnglePointInfo(tp, (ushort)lac, (ushort)ci, rxlev, cell, distance);
            }
        }

        private void dealTDPoint(TestPoint tp)
        {
            float? rscp = (float?)tp["TD_PCCPCH_RSCP"];
            int? lac = (int?)tp["TD_SCell_LAC"];
            int? ci = (int?)tp["TD_SCell_CI"];
            if (lac == null || ci == null) return;
            TDCell tdCell = CellManager.GetInstance().GetTDCell(tp.DateTime, (int)lac, (int)ci);
            if (tdCell != null)
            {
                double distance = tdCell.GetDistance(tp.Longitude, tp.Latitude);
                addAbnormalAnglePointInfo(tp, (int)lac, (int)ci, rscp, tdCell, distance);
            }
        }

        private void dealWPoint(TestPoint tp)
        {
            float? rscp = (float?)tp["W_Reference_RSCP"];
            int? lac = (int?)tp["W_SysLAI"];
            int? ci = (int?)tp["W_SysCellID"];
            WCell wCell = CellManager.GetInstance().GetWCell(tp.DateTime, (int)lac, (int)ci);
            if (wCell != null)
            {
                double distance = wCell.GetDistance(tp.Longitude, tp.Latitude);
                addAbnormalAnglePointInfo(tp, (int)lac, (int)ci, rscp, wCell, distance);
            }
        }

        private void dealLTEPoint(TestPoint tp)
        {
            int? tac = (int?)(ushort?)tp["lte_TAC"];
            int? eci = (int?)tp["lte_ECI"];
            float? rsrp = (float?)tp["lte_RSRP"];
            if (tac == null || tac == -1 || eci == null || eci == -10000000 || rsrp == null || rsrp == -10000000)
            {
                return;
            }
            LTECell lteCell = CellManager.GetInstance().GetLTECell(tp.DateTime, (int)tac, (int)eci);
            if (lteCell != null)
            {
                double distance = lteCell.GetDistance(tp.Longitude, tp.Latitude);
                addAbnormalAnglePointInfo(tp, (int)tac, (int)eci, rsrp, lteCell, distance);
            }
        }

        private void dealNRPoint(TestPoint tp)
        {
            int? tac = (int?)NRTpHelper.NrTpManager.GetTAC(tp);
            long? nci = (long?)NRTpHelper.NrTpManager.GetNCI(tp);
            float? rsrp = NRTpHelper.NrTpManager.GetSCellRsrp(tp, true);
            if (tac == null || nci == null || rsrp == null)
            {
                return;
            }

            NRCell nrCell = CellManager.GetInstance().GetNRCell(tp.DateTime, (int)tac, (long)nci);
            if (nrCell == null) return;
            double distance = nrCell.GetDistance(tp.Longitude, tp.Latitude);
            addAbnormalAnglePointInfo(tp, (int)tac, (long)nci, rsrp, nrCell, distance);
        }

        private void addAbnormalAnglePointInfo(TestPoint tp, int tac, long ci, float? rsrp, ICell cell, double distance)
        {
            int angle_Cell_TestPoint;
            if (distance > distanceMin && checkAbnormalAngle(cell.Longitude, cell.Latitude, tp.Longitude, tp.Latitude, cell.Direction, out angle_Cell_TestPoint))
            {
                AbnormalAnglePointInfo pointInfo = new AbnormalAnglePointInfo((float)rsrp, distance, angle_Cell_TestPoint, tp, cell);
                AbnormalAngleCellInfo cellInfo;
                if (!cellAngleCellInfoDic.TryGetValue(cell.Name, out cellInfo))
                {
                    cellInfo = new AbnormalAngleCellInfo(cell.Name, tac, ci, cell);
                    cellAngleCellInfoDic[cell.Name] = cellInfo;
                    angleCellInfoList.Add(cellInfo);
                }
                cellInfo.AbnormalAnglePointInfoList.Add(pointInfo);
            }
            else
            {
                if (cellAngleCellInfoDic.TryGetValue(cell.Name, out var cellInfo))
                {
                    cellInfo.NormalPointNum++;
                }
            }
        }

        private bool checkAbnormalAngle(double x1, double y1, double x2, double y2, float direction, out int angle_Cell_TestPoint)
        {
            int angleN = MathFuncs.getAngleFromPointToPoint(x1, y1, x2, y2);
            angle_Cell_TestPoint = (int)Math.Min(360 - Math.Abs(angleN - direction), Math.Abs(angleN - direction));
            if (angle_Cell_TestPoint > angleMin)
            {
                return true;
            }
            return false;
        }
    }

    public class AbnormalAngleCellInfo
    {
        public string CellName { get; set; }
        public int LAC { get; set; }
        public long CI { get; set; }
        public Cell cell { get; set; }
        public TDCell tdCell { get; set; }
        public WCell wCell { get; set; }
        public LTECell lteCell { get; set; }
        public NRCell NRCell { get; set; }
        public List<AbnormalAnglePointInfo> AbnormalAnglePointInfoList { get; set; }
        public string FileName { get; set; }
        public string MeanRxlev { get; set; }
        public string MeanAngle { get; set; }
        public string MeanDistance { get; set; }
        public string MaxVar { get; private set; }
        public string MinVar { get; private set; }
        public string MeanVar { get; private set; }

        public double AbnormalRate { get; set; }
        public int NormalPointNum { get; set; }
        public int PointNum
        {
            get
            {
                if (this.AbnormalAnglePointInfoList == null) return 0;
                return this.AbnormalAnglePointInfoList.Count;
            }
        }

        public AbnormalAngleCellInfo(string cellName, int lac, long ci, ICell cell)
        {
            this.CellName = cellName;
            this.LAC = lac;
            this.CI = ci;
            this.FileName = null;
            this.MaxVar = "";
            this.MinVar = "";
            this.MeanVar = "";
            AbnormalAnglePointInfoList = new List<AbnormalAnglePointInfo>();

            switch (cell)
            {
                case NRCell nrCell:
                    NRCell = nrCell;
                    break;
                case LTECell lteCell:
                    this.lteCell = lteCell;
                    break;
                case Cell gsmCell:
                    this.cell = gsmCell;
                    break;
                case TDCell tdCell:
                    this.tdCell = tdCell;
                    break;
                case WCell wCell:
                    this.wCell = wCell;
                    break;
            }
        }

        private double maxVar = double.MinValue;
        private double minVar = double.MaxValue;
        private double sumVar = 0;
        private int cntVar = 0;
        public void CalcResult()
        {
            if (AbnormalAnglePointInfoList.Count == 0)
            {
                return;
            }

            AbnormalRate = Math.Round(PointNum * 100d / (PointNum + NormalPointNum), 2);

            if (FileName == null)
            {
                FileName = AbnormalAnglePointInfoList[0].testPoint.FileName;
            }
            double sumRxlev = 0;
            double sumAngle = 0;
            double sumDistance = 0;
            foreach (AbnormalAnglePointInfo pt in AbnormalAnglePointInfoList)
            {
                if (pt.Var != null)
                {
                    sumVar += (double)pt.Var;
                    maxVar = Math.Max(maxVar, (double)pt.Var);
                    minVar = Math.Min(minVar, (double)pt.Var);
                    ++cntVar;
                }
                sumRxlev += pt.Rxlev;
                sumAngle += pt.Angle;
                sumDistance += pt.Distance;
            }
            if (cntVar != 0)
            {
                MaxVar = Math.Round(maxVar, 3).ToString();
                MinVar = Math.Round(minVar, 3).ToString();
                MeanVar = Math.Round(sumVar / cntVar, 3).ToString();
            }
            if (this.AbnormalAnglePointInfoList != null && this.AbnormalAnglePointInfoList.Count > 0)
            {
                this.MeanRxlev = Math.Round(sumRxlev / AbnormalAnglePointInfoList.Count, 3).ToString();
                this.MeanAngle = Math.Round(sumAngle / AbnormalAnglePointInfoList.Count, 3).ToString();
                this.MeanDistance = Math.Round(sumDistance / AbnormalAnglePointInfoList.Count, 3).ToString();
            }
        }
    }

    public class AbnormalAnglePointInfo
    {
        public double Longitude { get; set; }
        public double Latitude { get; set; }
        public float Rxlev { get; set; }
        public double Distance { get; set; }
        public int Angle { get; set; }
        public TestPoint testPoint { get; set; }
        public Cell cell { get; set; }
        public TDCell tdCell { get; set; }
        public WCell wCell { get; set; }
        public LTECell lteCell { get; set; }
        public NRCell NRCell { get; set; }
        public double? Var { get; set; }

        public AbnormalAnglePointInfo(float rxlev, double distance, int angle, TestPoint tp, ICell cell)
        {
            Rxlev = rxlev;
            Distance = Math.Round(distance, 3);
            Angle = angle;
            testPoint = tp;
            Longitude = tp.Longitude;
            Latitude = tp.Latitude;

            if (testPoint is TestPointDetail)
            {
                this.cell = cell as Cell;
                Var = (double?)(byte?)testPoint["RxQualSub"];
                Var = getValidData(Var, 0, 7);
            }
            else if (testPoint is TDTestPointDetail)
            {
                this.tdCell = cell as TDCell;
                Var = (int?)testPoint["TD_BLER"];
                Var = getValidData(Var, 0, 100);
            }
            else if (testPoint is WCDMATestPointDetail)
            {
                this.wCell = cell as WCell;
                Var = null;
            }
            else if (testPoint is LTETestPointDetail)
            {
                this.lteCell = cell as LTECell;
                Var = (double?)(float?)testPoint["lte_SINR"];
                Var = getValidData(Var, 0, 100);
            }
            else if (testPoint is TestPoint_NR)
            {
                NRCell = cell as NRCell;
                Var = (double?)(float?)testPoint["NR_SS_SINR"];
                Var = getValidData(Var, 0, 100);
            }
        }

        private double? getValidData(double? data, double min, double max)
        {
            if (data != null && (data < min || data > max))
            {
                data = null;
            }
            return data;
        }
    }
}