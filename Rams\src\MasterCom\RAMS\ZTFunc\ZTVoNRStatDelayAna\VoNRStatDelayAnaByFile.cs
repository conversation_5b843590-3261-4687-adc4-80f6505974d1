﻿using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public class VoNRStatDelayAnaByFile : VoNRStatDelayAnaByRegion
    {
        protected VoNRStatDelayAnaByFile()
            : base()
        {
        }

        private static VoNRStatDelayAnaByFile intance = null;
        public static new VoNRStatDelayAnaByFile GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new VoNRStatDelayAnaByFile();
                    }
                }
            }
            return intance;
        }

        public override string Name
        {
            get { return "VoNR时延分析(按文件)"; }
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.File;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                return true;
            }
            return false;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        /// <summary>
        /// 判断是否在所选区域内
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            return true;
        }

    }

    public class VoNRStatDelayAnaByFile_FDD : VoNRStatDelayAnaByFile
    {
        private static VoNRStatDelayAnaByFile_FDD instance = null;
        public static new VoNRStatDelayAnaByFile_FDD GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new VoNRStatDelayAnaByFile_FDD();
                    }
                }
            }
            return instance;
        }
        protected VoNRStatDelayAnaByFile_FDD()
            : base()
        {
            ServiceTypes.Clear();
        }
        public override string Name
        {
            get { return "VoNR时延分析(按文件)"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 30000, 30020, this.Name);
        }
    }
}
