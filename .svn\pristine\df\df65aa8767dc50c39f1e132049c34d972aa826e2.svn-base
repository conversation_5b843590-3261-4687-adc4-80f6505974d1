﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public class LteEventCellInfoAnaByRegion : LteEventCellInfoAnaBase
    {
        private LteEventCellInfoAnaByRegion()
            : base()
        {
            FilterSampleByRegion = true;
        }

        private static LteEventCellInfoAnaByRegion intance = null;
        public static LteEventCellInfoAnaByRegion GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new LteEventCellInfoAnaByRegion();
                    }
                }
            }
            return intance;
        }

        public override string Name
        {
            get { return "事件指标分析(按区域)"; }
        }

    }
}
