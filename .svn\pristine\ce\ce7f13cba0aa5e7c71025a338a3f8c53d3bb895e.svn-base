﻿using System;
using System.Collections.Generic;
using System.Text;
using System.IO;

using System.Data;
using System.Data.OleDb;

using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;

namespace StationRoadDistanceUtil
{
    /// <summary>
    /// 获取工作表指定的列内容
    /// </summary>
    public class ExcelNPOIReaderExt
    {
        public string xlsFilePath { get; set; }
        private IWorkbook book { get; set; }
        private ISheet usingSheet = null;
        public string lastError { get; set; } = "";

        public ExcelNPOIReaderExt(string xlsFile)
        {
            this.xlsFilePath = xlsFile;
            using (FileStream fs = File.OpenRead(xlsFile))
            {
                book = WorkbookFactory.Create(fs);
            }
        }

        public IWorkbook WorkBook
        {
            get { return book; }
        }

        /// <summary>
        /// 获取所有可见的工作表名
        /// </summary>
        /// <returns></returns>
        public List<string> GetSheets()
        {
            List<string> retSheets = new List<string>();
            int sheetCount = book.NumberOfSheets;
            for (int i = 0; i < sheetCount; ++i)
            {
                if (book.IsSheetHidden(i))
                {
                    continue;
                }
                ISheet sheet = book.GetSheetAt(i);
                retSheets.Add(sheet.SheetName);
            }
            return retSheets;
        }

        /// <summary>
        /// 获取Sheet的首行列名
        /// </summary>
        /// <param name="sheetName">工作表名</param>
        /// <returns></returns>
        public List<string> GetColumns(string sheetName)
        {
            List<string> retColumns = new List<string>();
            ISheet sheet = book.GetSheet(sheetName);
            if (sheet == null)
            {
                return retColumns;
            }

            IRow row = sheet.GetRow(sheet.FirstRowNum);
            if (row == null)
            {
                return retColumns;
            }

            for (int i = row.FirstCellNum; i <= row.LastCellNum; ++i)
            {
                ICell cell = row.GetCell(i);
                if (cell == null)
                {
                    continue;
                }

                object cellValue = GetCellValue(cell);
                if (cellValue == null)
                {
                    continue;
                }
                retColumns.Add(cellValue.ToString());
            }
            return retColumns;
        }

        ///<summary>
        ///获取指定行作为列名
        /// </summary>
        /// <param name="sheetName">工作表名称</param>
        /// <param name="rowIndex">作为列名的行下标，0开始</param>
        public List<string> GetColumns(string sheetName, int rowIndex)
        {
            List<string> retColumns = new List<string>();
            ISheet sheet = book.GetSheet(sheetName);
            if (sheet == null)
            {
                return retColumns;
            }

            IRow row = sheet.GetRow(rowIndex);
            if (row == null)
            {
                return retColumns;
            }
            for (int i = row.FirstCellNum; i <= row.LastCellNum; ++i)
            {
                ICell cell = row.GetCell(i);
                if (cell == null)
                {
                    continue;
                }

                object cellValue = GetCellValue(cell);
                if (cellValue == null)
                {
                    continue;
                }
                retColumns.Add(cellValue.ToString());
            }
            return retColumns;
        }

        /// <summary>
        /// 获取第一个非空工作表的首行列名
        /// </summary>
        /// <returns></returns>
        public List<string> GetColumns()
        {
            for (int i = 0; i < book.NumberOfSheets; ++i)
            {
                if (book.IsSheetHidden(i))
                {
                    continue;
                }

                ISheet sheet = book.GetSheetAt(i);
                if (sheet == null)
                {
                    continue;
                }

                List<string> retColumns = GetColumns(sheet.SheetName);
                if (retColumns.Count != 0)
                {
                    return retColumns;
                }
            }
            return new List<string>();
        }

        /// <summary>
        /// 获取工作表的指定列内容
        /// </summary>
        /// <param name="sheetName"></param>
        /// <param name="colNames"></param>
        /// <returns></returns>
        public ExcelNPOITable GetTable(string sheetName, List<string> colNames)
        {
            ISheet sheet = book.GetSheet(sheetName);
            int[] idx = GetColumnsIndex(sheet, colNames);
            List<object[]> cellValues = new List<object[]>();

            // Set Cells Value
            for (int i = sheet.FirstRowNum + 1; i <= sheet.LastRowNum; ++i)
            {
                object[] values = new object[colNames.Count];
                cellValues.Add(values);

                IRow row = sheet.GetRow(i);
                if (row == null)
                {
                    continue;
                }

                for (int j = 0; j < idx.Length; ++j)
                {
                    if (idx[j] == -1)
                    {
                        continue;
                    }
                    ICell cell = row.GetCell(idx[j]);
                    values[j] = cell == null ? "" : GetCellValue(cell);
                }
            }

            // Set Columns Index
            for (int i = 0; i < idx.Length; ++i)
            {
                if (idx[i] != -1)
                {
                    idx[i] += 1;
                }
            }

            // return table
            ExcelNPOITable table = new ExcelNPOITable();
            table.CellValues = cellValues;
            table.FirstRow = sheet.FirstRowNum + 2; // 从0开始加1，第一行是列头再加1
            table.LastRow = sheet.LastRowNum + 1;
            table.ColumnsIndex = idx;

            return table;
        }

        /// <summary>
        /// 获取第一个匹配指定列名的列内容
        /// </summary>
        /// <param name="colNames"></param>
        /// <returns>如果所有工作表无法完全匹配指定列，返回null</returns>
        public ExcelNPOITable GetTable(List<string> colNames)
        {
            List<string> sheetNames = GetSheets();
            foreach (string shtName in sheetNames)
            {
                ISheet sheet = book.GetSheet(shtName);
                int sheetIndex = book.GetSheetIndex(sheet);
                if (book.IsSheetHidden(sheetIndex))
                {
                    continue;
                }

                bool isMatch = true;
                int[] idx = GetColumnsIndex(sheet, colNames);
                for (int i = 0; i < idx.Length; ++i)
                {
                    if (idx[i] == -1)
                    {
                        isMatch = false;
                        break;
                    }
                }

                if (isMatch)
                {
                    return GetTable(shtName, colNames);
                }
            }
            return null;
        }

        /// <summary>
        /// 获取指定工作表的所有列内容
        /// </summary>
        /// <param name="sheetName"></param>
        /// <returns></returns>
        public ExcelNPOITable GetTable(string sheetName)
        {
            List<string> colNames = GetColumns(sheetName);
            return GetTable(sheetName, colNames);
        }

        /// <summary>
        /// 获取第一个非空工作表的所有列内容
        /// </summary>
        /// <returns>如果所有工作表都为空，返回null</returns>
        public ExcelNPOITable GetTable()
        {
            List<string> colNames = GetColumns();
            return GetTable(colNames);
        }

        /// <summary>
        /// 从工作表中获取指定列名所在的列索引，索引从0开始
        /// </summary>
        /// <param name="sheet"></param>
        /// <param name="colNames"></param>
        /// <returns></returns>
        private int[] GetColumnsIndex(ISheet sheet, List<string> colNames)
        {
            int[] idx = new int[colNames.Count];
            for (int i = 0; i < idx.Length; ++i)
            {
                idx[i] = -1;
            }

            IRow firstRow = sheet.GetRow(sheet.FirstRowNum);
            if (firstRow == null)
            {
                return idx;
            }

            for (int i = 0; i < colNames.Count; ++i)
            {
                string colName = colNames[i];
                for (int j = firstRow.FirstCellNum; j <= firstRow.LastCellNum; ++j)
                {
                    ICell cell = firstRow.GetCell(j);
                    if (cell == null)
                    {
                        continue;
                    }

                    object value = GetCellValue(cell);
                    if (value == null || value.ToString().Trim() != colName)
                    {
                        continue;
                    }

                    idx[i] = j;
                    break;
                }
            }
            return idx;
        }

        private object GetCellValue(ICell cell)
        {
            switch (cell.CellType)
            {
                case CellType.Blank:
                    return "";
                case CellType.Boolean:
                    return cell.BooleanCellValue;
                case CellType.Numeric:
                    return cell.ToString();
                case CellType.String:
                    return cell.StringCellValue;
                default:
                    return "";
            }
        }

        public bool UseSheet(string sheetName)
        {
            this.usingSheet = this.book.GetSheet(sheetName);
            if (this.usingSheet == null)
            {
                return false;
            }
            return true;
        }

        public string[] GetRowOfUsingSheet(int rowIndex)
        {
            IRow row = this.usingSheet.GetRow(rowIndex);
            if (row == null) return new string[0];
            ICell cell = null;
            string[] data = new string[row.LastCellNum];
            for (int i = 0; i < row.LastCellNum; i++)
            {
                cell = row.GetCell(i);
                if (cell == null)
                {
                    data[i] = "";
                    continue;
                }
                data[i] = this.GetCellValue(cell).ToString();
            }
            return data;
        }

        /// <summary>
        /// 将excel中的数据导入到DataTable中
        /// </summary>
        /// <param name="sheetName">excel工作薄sheet的名称</param>
        /// <param name="firstRowIndex">有效数据的第一行下标，0开始</param>
        /// <param name="isFirstRowColumn">第一行是否是DataTable的列名</param>
        /// <returns>返回的DataTable</returns>
        public DataTable ExcelToDataTable(string sheetName, int firstRowIndex, bool isFirstRowColumn)
        {
            ISheet sheet = null;
            DataTable data = new DataTable();
            int startRow;
            try
            {
                sheet = this.book.GetSheet(sheetName);
                if (sheet == null) //如果没有找到指定的sheetName对应的sheet，
                {
                    return null;
                }
                IRow firstRow = sheet.GetRow(firstRowIndex);
                int cellCount = firstRow.LastCellNum; //一行最后一个cell的编号 即总的列数

                if (isFirstRowColumn)
                {
                    addColumns(data, firstRow, cellCount);
                    startRow = firstRowIndex + 1;
                }
                else
                {
                    for (int i = firstRow.FirstCellNum; i < cellCount; ++i)
                    {
                        DataColumn co = new DataColumn();
                        data.Columns.Add(co);
                    }
                    startRow = firstRowIndex;
                }

                addLastRow(sheet, data, startRow, cellCount);
                return data;
            }
            catch (Exception ex)
            {
                this.lastError = ex.Message;
            }
            return null;
        }

        private void addColumns(DataTable data, IRow firstRow, int cellCount)
        {
            for (int i = firstRow.FirstCellNum; i < cellCount; ++i)
            {
                ICell cell = firstRow.GetCell(i);
                if (cell != null)
                {
                    string cellValue = cell.StringCellValue;
                    if (cellValue != null)
                    {
                        DataColumn column = new DataColumn(cellValue);
                        data.Columns.Add(column);
                    }
                }
            }
        }

        private void addLastRow(ISheet sheet, DataTable data, int startRow, int cellCount)
        {
            //最后一列的标号
            int lastRowIndex = sheet.LastRowNum;
            for (int i = startRow; i <= lastRowIndex; ++i)
            {
                IRow row = sheet.GetRow(i);
                if (row == null) continue; //没有数据的行默认是null　　　　　　　

                DataRow dataRow = data.NewRow();
                for (int j = row.FirstCellNum; j < cellCount; ++j)
                {
                    if (row.GetCell(j) != null) //同理，没有数据的单元格都默认是null
                        dataRow[j] = row.GetCell(j).ToString();
                }
                data.Rows.Add(dataRow);
            }
        }


        /// <summary>
        /// 将DataTable数据导入到excel中
        /// </summary>
        /// <param name="data">要导入的数据</param>
        /// <param name="isColumnWritten">DataTable的列名是否要导入</param>
        /// <param name="sheetName">要导入的excel的sheet的名称</param>
        /// <returns>导入数据行数(包含列名那一行)</returns>
        public static int DataTableToExcel(DataTable data, string excelFileName, string sheetName, bool columnNameToFirstRow)
        {
            int i = 0;
            int j = 0;
            int count = 0;
            ISheet sheet = null;

            FileStream fs = new FileStream(excelFileName, FileMode.OpenOrCreate, FileAccess.ReadWrite);
            // (fileName.IndexOf(".xls") > 0) // 2003版本
            HSSFWorkbook workbook = new HSSFWorkbook();
            try
            {
                sheet = workbook.CreateSheet(sheetName);

                if (columnNameToFirstRow) //写入DataTable的列名
                {
                    IRow row = sheet.CreateRow(0);
                    for (j = 0; j < data.Columns.Count; ++j)
                    {
                        row.CreateCell(j).SetCellValue(data.Columns[j].ColumnName);
                    }
                    count = 1;
                }
                for (i = 0; i < data.Rows.Count; ++i)
                {
                    IRow row = sheet.CreateRow(count);
                    for (j = 0; j < data.Columns.Count; ++j)
                    {
                        row.CreateCell(j).SetCellValue(data.Rows[i][j].ToString());
                    }
                    ++count;
                }
                workbook.Write(fs); //写入到excel
                fs.Close();
                return count;
            }
            catch
            {       
                //continue         
            }
            return -1;
        }
    }

    /// <summary>
    /// 工作表内容
    /// </summary>
    public class ExcelNPOITable
    {
        /// <summary>
        /// 内容起始行，从1开始
        /// </summary>
        public int FirstRow { get; set; }

        /// <summary>
        /// 内容结束行，从1开始
        /// </summary>
        public int LastRow { get; set; }

        /// <summary>
        /// 列索引，从1开始
        /// </summary>
        public int[] ColumnsIndex { get; set; }

        /// <summary>
        /// 所有表格值，表格值可能为null
        /// 保证object[].Length == ColumnsIndex.Length
        /// 且List.Count == LastRow - FirstRow + 1
        /// </summary>
        public List<object[]> CellValues { get; set; }
    }

    public static class DataRowReader
    {
        public static bool GetInt(DataRow dr, string colName, out int value)
        {
            value = 0;
            return !Convert.IsDBNull(dr[colName]) && int.TryParse(dr[colName].ToString(), out value);
        }

        public static bool GetFloat(DataRow dr, string colName, out float value)
        {
            value = 0;
            return !Convert.IsDBNull(dr[colName]) && float.TryParse(dr[colName].ToString(), out value);
        }

        public static bool GetDouble(DataRow dr, string colName, out double value)
        {
            value = 0;
            return !Convert.IsDBNull(dr[colName]) && double.TryParse(dr[colName].ToString(), out value);
        }

        public static bool GetString(DataRow dr, string colName, out string value)
        {
            value = null;
            if (Convert.IsDBNull(dr[colName]))
            {
                return false;
            }
            value = dr[colName].ToString();
            return true;
        }

        public static bool GetDateTime(DataRow dr, string colName, out DateTime value)
        {
            value = DateTime.MaxValue;
            if (Convert.IsDBNull(dr[colName]))
            {
                return false;
            }
            return DateTime.TryParse(dr[colName].ToString(), out value);
        }
    }
}
