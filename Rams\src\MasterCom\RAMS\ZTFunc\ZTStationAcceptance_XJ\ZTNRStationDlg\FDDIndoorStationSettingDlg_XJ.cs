﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class FDDIndoorStationSettingDlg_XJ : BaseDialog
    {
        public FDDIndoorStationSettingDlg_XJ()
        {
            InitializeComponent();
        }
        public FDDIndoorStationSettingDlg_XJ(FDDIndoorStationSettingDlgConfigModel_XJ condition)
        {
            InitializeComponent();
            setCondition(condition);
        }

        public void setCondition(FDDIndoorStationSettingDlgConfigModel_XJ condition)
        {
            if (condition == null)
            {
                return;
            }

            txtAccessRate.Value = Convert.ToDecimal(condition.AccessSuccessRate);
            txtAccessTestCount.Value = Convert.ToDecimal(condition.AccessTestCount);
            txtCSFBTestCount.Value = Convert.ToDecimal(condition.CSFBTestCount);
            txtCSFBSuccessRate.Value = Convert.ToDecimal(condition.CSFBSuccessRate);
            txtVolteTestCount.Value = Convert.ToDecimal(condition.VOLTETestCount);
            txtVolteSuccessRate.Value = Convert.ToDecimal(condition.VOLTESuccessRate);
            txtFTPDownSpeed.Value = Convert.ToDecimal(condition.FTPDownloadThroughput);
            txtFTPUpSpeed.Value = Convert.ToDecimal(condition.FTPUploadThroughput);
            txtSwitchCount.Value = Convert.ToDecimal(condition.SwitchCount);
            txtSwitchSuccessRate.Value = Convert.ToDecimal(condition.SwtichSuccessRate);
            txtAvgRSRP.Value = Convert.ToDecimal(condition.AvgRSRP);
            txtAvgSINR.Value = Convert.ToDecimal(condition.AvgSINR);
            txtStandard.Text = condition.WeakCoverCheckStandard;
            txtSystemInSwitch.Value = Convert.ToDecimal(condition.SystemInSwitch);
        }

        public FDDIndoorStationSettingDlgConfigModel_XJ GetCondition()
        {
            FDDIndoorStationSettingDlgConfigModel_XJ condition = new FDDIndoorStationSettingDlgConfigModel_XJ();
            condition.AccessSuccessRate = txtAccessRate.Value.ToString();
            condition.AccessTestCount = Convert.ToInt32(txtAccessTestCount.Value);
            condition.CSFBTestCount = Convert.ToInt32(txtCSFBTestCount.Value);
            condition.CSFBSuccessRate = txtCSFBSuccessRate.Value.ToString();
            condition.VOLTETestCount = Convert.ToInt32(txtVolteTestCount.Value);
            condition.VOLTESuccessRate = txtVolteSuccessRate.Value.ToString();
            condition.FTPDownloadThroughput = txtFTPDownSpeed.Value.ToString();
            condition.FTPUploadThroughput = txtFTPUpSpeed.Value.ToString();
            condition.SwitchCount = Convert.ToInt32(txtSwitchCount.Value);
            condition.SwtichSuccessRate = txtSwitchSuccessRate.Value.ToString();
            condition.AvgRSRP = txtAvgRSRP.Value.ToString();
            condition.AvgSINR = txtAvgSINR.Value.ToString();
            condition.WeakCoverCheckStandard = txtStandard.Text;
            condition.SystemInSwitch = txtSystemInSwitch.Value.ToString();
            return condition;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            var cond = GetCondition();
            if (cond != null)
            {
                FDDIndoorStationSettingDlgConfig_XJ.Instance.SaveConfig(cond);
                DialogResult = DialogResult.OK;
            }
            else
            {
                MessageBox.Show("主库连接设置不能为空");
            }
        }
    }
}
