﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class LteCellAngleForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.xtraTabControl1 = new DevExpress.XtraTab.XtraTabControl();
            this.xtraTabPage4 = new DevExpress.XtraTab.XtraTabPage();
            this.btnNextpage = new System.Windows.Forms.Button();
            this.btnPrevpage = new System.Windows.Forms.Button();
            this.label5 = new System.Windows.Forms.Label();
            this.txtPage = new System.Windows.Forms.TextBox();
            this.labPage = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.btnGo = new System.Windows.Forms.Button();
            this.labNum = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.btnSearch = new System.Windows.Forms.Button();
            this.txtCellName = new System.Windows.Forms.TextBox();
            this.label1 = new System.Windows.Forms.Label();
            this.dataGridViewCell = new System.Windows.Forms.DataGridView();
            this.colIndex = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column11 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column1 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column14 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colvender = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column41 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column2 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colcgi = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colcovertype = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column3 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colbeamwidth = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column22 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column4 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column5 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column6 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column7 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column62 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column63 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column64 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colgmax = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.col3db = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.col6db = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column8 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column9 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column10 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column12 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column13 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column15 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column16 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column17 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column18 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column19 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column20 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column56 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column57 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column58 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column59 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column60 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column61 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column21 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column26 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column24 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column27 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column28 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column30 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column31 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column32 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column33 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column35 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column36 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column37 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column38 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column40 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column42 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column43 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column46 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column44 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column47 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column48 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column49 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column50 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column51 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column52 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column53 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column54 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column55 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column23 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column25 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column29 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column34 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column39 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column45 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colrange1 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colrange2 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colrange3 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colrange4 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colrange5 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colrange6 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colrange7 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colrange8 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colphase1 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colphase2 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colphase3 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colphase4 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colphase5 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colphase6 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colphase7 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colphase8 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.contextMenuStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.导出CSVToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportWholeExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.显示小区ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).BeginInit();
            this.xtraTabControl1.SuspendLayout();
            this.xtraTabPage4.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewCell)).BeginInit();
            this.contextMenuStrip.SuspendLayout();
            this.SuspendLayout();
            // 
            // xtraTabControl1
            // 
            this.xtraTabControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.xtraTabControl1.Location = new System.Drawing.Point(0, 0);
            this.xtraTabControl1.Name = "xtraTabControl1";
            this.xtraTabControl1.SelectedTabPage = this.xtraTabPage4;
            this.xtraTabControl1.Size = new System.Drawing.Size(1184, 650);
            this.xtraTabControl1.TabIndex = 1;
            this.xtraTabControl1.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.xtraTabPage4});
            // 
            // xtraTabPage4
            // 
            this.xtraTabPage4.Controls.Add(this.btnNextpage);
            this.xtraTabPage4.Controls.Add(this.btnPrevpage);
            this.xtraTabPage4.Controls.Add(this.label5);
            this.xtraTabPage4.Controls.Add(this.txtPage);
            this.xtraTabPage4.Controls.Add(this.labPage);
            this.xtraTabPage4.Controls.Add(this.label4);
            this.xtraTabPage4.Controls.Add(this.btnGo);
            this.xtraTabPage4.Controls.Add(this.labNum);
            this.xtraTabPage4.Controls.Add(this.label3);
            this.xtraTabPage4.Controls.Add(this.label2);
            this.xtraTabPage4.Controls.Add(this.btnSearch);
            this.xtraTabPage4.Controls.Add(this.txtCellName);
            this.xtraTabPage4.Controls.Add(this.label1);
            this.xtraTabPage4.Controls.Add(this.dataGridViewCell);
            this.xtraTabPage4.Name = "xtraTabPage4";
            this.xtraTabPage4.Size = new System.Drawing.Size(1177, 620);
            this.xtraTabPage4.Text = "LTE天线覆盖方向分析";
            // 
            // btnNextpage
            // 
            this.btnNextpage.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnNextpage.Location = new System.Drawing.Point(860, 595);
            this.btnNextpage.Name = "btnNextpage";
            this.btnNextpage.Size = new System.Drawing.Size(33, 23);
            this.btnNextpage.TabIndex = 16;
            this.btnNextpage.Text = ">>";
            this.btnNextpage.UseVisualStyleBackColor = true;
            this.btnNextpage.Click += new System.EventHandler(this.btnNextpage_Click);
            // 
            // btnPrevpage
            // 
            this.btnPrevpage.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnPrevpage.Location = new System.Drawing.Point(821, 595);
            this.btnPrevpage.Name = "btnPrevpage";
            this.btnPrevpage.Size = new System.Drawing.Size(33, 23);
            this.btnPrevpage.TabIndex = 15;
            this.btnPrevpage.Text = "<<";
            this.btnPrevpage.UseVisualStyleBackColor = true;
            this.btnPrevpage.Click += new System.EventHandler(this.btnPrevpage_Click);
            // 
            // label5
            // 
            this.label5.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(763, 597);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(19, 14);
            this.label5.TabIndex = 14;
            this.label5.Text = "页";
            // 
            // txtPage
            // 
            this.txtPage.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.txtPage.Location = new System.Drawing.Point(698, 594);
            this.txtPage.Name = "txtPage";
            this.txtPage.Size = new System.Drawing.Size(63, 22);
            this.txtPage.TabIndex = 13;
            // 
            // labPage
            // 
            this.labPage.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.labPage.AutoSize = true;
            this.labPage.Location = new System.Drawing.Point(598, 599);
            this.labPage.Name = "labPage";
            this.labPage.Size = new System.Drawing.Size(14, 14);
            this.labPage.TabIndex = 12;
            this.labPage.Text = "0";
            // 
            // label4
            // 
            this.label4.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(555, 598);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(43, 14);
            this.label4.TabIndex = 11;
            this.label4.Text = "个，共";
            // 
            // btnGo
            // 
            this.btnGo.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnGo.Location = new System.Drawing.Point(782, 594);
            this.btnGo.Name = "btnGo";
            this.btnGo.Size = new System.Drawing.Size(33, 23);
            this.btnGo.TabIndex = 9;
            this.btnGo.Text = "GO";
            this.btnGo.UseVisualStyleBackColor = true;
            this.btnGo.Click += new System.EventHandler(this.btnGo_Click);
            // 
            // labNum
            // 
            this.labNum.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.labNum.AutoSize = true;
            this.labNum.Location = new System.Drawing.Point(510, 599);
            this.labNum.Name = "labNum";
            this.labNum.Size = new System.Drawing.Size(14, 14);
            this.labNum.TabIndex = 8;
            this.labNum.Text = "0";
            // 
            // label3
            // 
            this.label3.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(632, 598);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(67, 14);
            this.label3.TabIndex = 7;
            this.label3.Text = "页，跳转至";
            // 
            // label2
            // 
            this.label2.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(434, 598);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(79, 14);
            this.label2.TabIndex = 6;
            this.label2.Text = "总计小区共：";
            // 
            // btnSearch
            // 
            this.btnSearch.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnSearch.Location = new System.Drawing.Point(1128, 594);
            this.btnSearch.Name = "btnSearch";
            this.btnSearch.Size = new System.Drawing.Size(42, 23);
            this.btnSearch.TabIndex = 5;
            this.btnSearch.Text = "查找";
            this.btnSearch.UseVisualStyleBackColor = true;
            this.btnSearch.Click += new System.EventHandler(this.btnSearch_Click);
            // 
            // txtCellName
            // 
            this.txtCellName.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.txtCellName.Location = new System.Drawing.Point(966, 595);
            this.txtCellName.Name = "txtCellName";
            this.txtCellName.Size = new System.Drawing.Size(157, 22);
            this.txtCellName.TabIndex = 4;
            // 
            // label1
            // 
            this.label1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(905, 599);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(67, 14);
            this.label1.TabIndex = 3;
            this.label1.Text = "小区名称：";
            // 
            // dataGridViewCell
            // 
            this.dataGridViewCell.AllowUserToAddRows = false;
            this.dataGridViewCell.AllowUserToDeleteRows = false;
            this.dataGridViewCell.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.dataGridViewCell.BackgroundColor = System.Drawing.SystemColors.Window;
            this.dataGridViewCell.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dataGridViewCell.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.colIndex,
            this.Column11,
            this.Column1,
            this.Column14,
            this.colvender,
            this.Column41,
            this.Column2,
            this.colcgi,
            this.colcovertype,
            this.Column3,
            this.colbeamwidth,
            this.Column22,
            this.Column4,
            this.Column5,
            this.Column6,
            this.Column7,
            this.Column62,
            this.Column63,
            this.Column64,
            this.colgmax,
            this.col3db,
            this.col6db,
            this.Column8,
            this.Column9,
            this.Column10,
            this.Column12,
            this.Column13,
            this.Column15,
            this.Column16,
            this.Column17,
            this.Column18,
            this.Column19,
            this.Column20,
            this.Column56,
            this.Column57,
            this.Column58,
            this.Column59,
            this.Column60,
            this.Column61,
            this.Column21,
            this.Column26,
            this.Column24,
            this.Column27,
            this.Column28,
            this.Column30,
            this.Column31,
            this.Column32,
            this.Column33,
            this.Column35,
            this.Column36,
            this.Column37,
            this.Column38,
            this.Column40,
            this.Column42,
            this.Column43,
            this.Column46,
            this.Column44,
            this.Column47,
            this.Column48,
            this.Column49,
            this.Column50,
            this.Column51,
            this.Column52,
            this.Column53,
            this.Column54,
            this.Column55,
            this.Column23,
            this.Column25,
            this.Column29,
            this.Column34,
            this.Column39,
            this.Column45,
            this.colrange1,
            this.colrange2,
            this.colrange3,
            this.colrange4,
            this.colrange5,
            this.colrange6,
            this.colrange7,
            this.colrange8,
            this.colphase1,
            this.colphase2,
            this.colphase3,
            this.colphase4,
            this.colphase5,
            this.colphase6,
            this.colphase7,
            this.colphase8});
            this.dataGridViewCell.ContextMenuStrip = this.contextMenuStrip;
            this.dataGridViewCell.Location = new System.Drawing.Point(0, 0);
            this.dataGridViewCell.Name = "dataGridViewCell";
            this.dataGridViewCell.RowTemplate.Height = 23;
            this.dataGridViewCell.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dataGridViewCell.Size = new System.Drawing.Size(1177, 590);
            this.dataGridViewCell.TabIndex = 2;
            // 
            // colIndex
            // 
            this.colIndex.HeaderText = "序号";
            this.colIndex.Name = "colIndex";
            // 
            // Column11
            // 
            this.Column11.HeaderText = "地市";
            this.Column11.Name = "Column11";
            // 
            // Column1
            // 
            this.Column1.HeaderText = "小区英文名";
            this.Column1.Name = "Column1";
            // 
            // Column14
            // 
            this.Column14.HeaderText = "小区名";
            this.Column14.Name = "Column14";
            // 
            // colvender
            // 
            this.colvender.HeaderText = "主设备厂家";
            this.colvender.Name = "colvender";
            // 
            // Column41
            // 
            this.Column41.HeaderText = "是否匹配工参";
            this.Column41.Name = "Column41";
            // 
            // Column2
            // 
            this.Column2.HeaderText = "天线权值";
            this.Column2.Name = "Column2";
            // 
            // colcgi
            // 
            this.colcgi.HeaderText = "CGI";
            this.colcgi.Name = "colcgi";
            // 
            // colcovertype
            // 
            this.colcovertype.HeaderText = "覆盖类型";
            this.colcovertype.Name = "colcovertype";
            // 
            // Column3
            // 
            this.Column3.HeaderText = "小区频段";
            this.Column3.Name = "Column3";
            // 
            // colbeamwidth
            // 
            this.colbeamwidth.HeaderText = "波束宽度";
            this.colbeamwidth.Name = "colbeamwidth";
            // 
            // Column22
            // 
            this.Column22.HeaderText = "方位角";
            this.Column22.Name = "Column22";
            // 
            // Column4
            // 
            this.Column4.HeaderText = "预置下倾角";
            this.Column4.Name = "Column4";
            // 
            // Column5
            // 
            this.Column5.HeaderText = "机械下倾角";
            this.Column5.Name = "Column5";
            // 
            // Column6
            // 
            this.Column6.HeaderText = "电调下倾角";
            this.Column6.Name = "Column6";
            // 
            // Column7
            // 
            this.Column7.HeaderText = "挂高";
            this.Column7.Name = "Column7";
            // 
            // Column62
            // 
            this.Column62.HeaderText = "分析结果";
            this.Column62.Name = "Column62";
            // 
            // Column63
            // 
            this.Column63.HeaderText = "告警状态";
            this.Column63.Name = "Column63";
            // 
            // Column64
            // 
            this.Column64.HeaderText = "评估结果";
            this.Column64.Name = "Column64";
            // 
            // colgmax
            // 
            this.colgmax.HeaderText = "Gmax(天线权值计算)";
            this.colgmax.Name = "colgmax";
            // 
            // col3db
            // 
            this.col3db.HeaderText = "3dB功率角";
            this.col3db.Name = "col3db";
            // 
            // col6db
            // 
            this.col6db.HeaderText = "6dB功率角";
            this.col6db.Name = "col6db";
            // 
            // Column8
            // 
            this.Column8.HeaderText = "上行吞吐量";
            this.Column8.Name = "Column8";
            // 
            // Column9
            // 
            this.Column9.HeaderText = "下行吞吐量";
            this.Column9.Name = "Column9";
            // 
            // Column10
            // 
            this.Column10.HeaderText = "无线接通率";
            this.Column10.Name = "Column10";
            // 
            // Column12
            // 
            this.Column12.HeaderText = "无线掉线率";
            this.Column12.Name = "Column12";
            // 
            // Column13
            // 
            this.Column13.HeaderText = "切换成功率";
            this.Column13.Name = "Column13";
            // 
            // Column15
            // 
            this.Column15.HeaderText = "ERAB建立成功率";
            this.Column15.Name = "Column15";
            // 
            // Column16
            // 
            this.Column16.HeaderText = "ERAB掉线率";
            this.Column16.Name = "Column16";
            // 
            // Column17
            // 
            this.Column17.HeaderText = "RSRP均值";
            this.Column17.Name = "Column17";
            // 
            // Column18
            // 
            this.Column18.HeaderText = "SINR均值";
            this.Column18.Name = "Column18";
            // 
            // Column19
            // 
            this.Column19.HeaderText = "95覆盖率";
            this.Column19.Name = "Column19";
            // 
            // Column20
            // 
            this.Column20.HeaderText = "110覆盖率";
            this.Column20.Name = "Column20";
            // 
            // Column56
            // 
            this.Column56.HeaderText = "MRO总采样点数";
            this.Column56.Name = "Column56";
            // 
            // Column57
            // 
            this.Column57.HeaderText = "重叠覆盖条件采样点数";
            this.Column57.Name = "Column57";
            // 
            // Column58
            // 
            this.Column58.HeaderText = "重叠覆盖指数";
            this.Column58.Name = "Column58";
            // 
            // Column59
            // 
            this.Column59.HeaderText = "过覆盖影响小区数";
            this.Column59.Name = "Column59";
            // 
            // Column60
            // 
            this.Column60.HeaderText = "高重叠覆盖小区";
            this.Column60.Name = "Column60";
            // 
            // Column61
            // 
            this.Column61.HeaderText = "过覆盖小区";
            this.Column61.Name = "Column61";
            // 
            // Column21
            // 
            this.Column21.HeaderText = "扫频采样点总数";
            this.Column21.Name = "Column21";
            // 
            // Column26
            // 
            this.Column26.HeaderText = "主瓣采样点比例";
            this.Column26.Name = "Column26";
            // 
            // Column24
            // 
            this.Column24.HeaderText = "主瓣最强信号强度";
            this.Column24.Name = "Column24";
            // 
            // Column27
            // 
            this.Column27.HeaderText = "疑似旁瓣数量";
            this.Column27.Name = "Column27";
            // 
            // Column28
            // 
            this.Column28.HeaderText = "旁瓣1辐射方向";
            this.Column28.Name = "Column28";
            // 
            // Column30
            // 
            this.Column30.HeaderText = "旁瓣1最强信号强度";
            this.Column30.Name = "Column30";
            // 
            // Column31
            // 
            this.Column31.HeaderText = "旁瓣1平均信号强度";
            this.Column31.Name = "Column31";
            // 
            // Column32
            // 
            this.Column32.HeaderText = "旁瓣1采样点比例";
            this.Column32.Name = "Column32";
            // 
            // Column33
            // 
            this.Column33.HeaderText = "旁瓣2辐射方向";
            this.Column33.Name = "Column33";
            // 
            // Column35
            // 
            this.Column35.HeaderText = "旁瓣2最强信号强度";
            this.Column35.Name = "Column35";
            // 
            // Column36
            // 
            this.Column36.HeaderText = "旁瓣2平均信号强度";
            this.Column36.Name = "Column36";
            // 
            // Column37
            // 
            this.Column37.HeaderText = "旁瓣2采样点比例";
            this.Column37.Name = "Column37";
            // 
            // Column38
            // 
            this.Column38.HeaderText = "旁瓣3辐射方向";
            this.Column38.Name = "Column38";
            // 
            // Column40
            // 
            this.Column40.HeaderText = "旁瓣3最强信号强度";
            this.Column40.Name = "Column40";
            // 
            // Column42
            // 
            this.Column42.HeaderText = "旁瓣3平均信号强度";
            this.Column42.Name = "Column42";
            // 
            // Column43
            // 
            this.Column43.HeaderText = "旁瓣3采样点比例";
            this.Column43.Name = "Column43";
            // 
            // Column46
            // 
            this.Column46.HeaderText = "背瓣采样点比例";
            this.Column46.Name = "Column46";
            // 
            // Column44
            // 
            this.Column44.HeaderText = "前后比";
            this.Column44.Name = "Column44";
            // 
            // Column47
            // 
            this.Column47.HeaderText = "路测采样点";
            this.Column47.Name = "Column47";
            // 
            // Column48
            // 
            this.Column48.HeaderText = "覆盖率(RSRP≥-110&SINR>=-3)";
            this.Column48.Name = "Column48";
            // 
            // Column49
            // 
            this.Column49.HeaderText = "LTE覆盖率(RSRP≥-110)";
            this.Column49.Name = "Column49";
            // 
            // Column50
            // 
            this.Column50.HeaderText = "LTE覆盖率(SINR>=-3)";
            this.Column50.Name = "Column50";
            // 
            // Column51
            // 
            this.Column51.HeaderText = "小区平均RSRP";
            this.Column51.Name = "Column51";
            // 
            // Column52
            // 
            this.Column52.HeaderText = "小区平均SINR";
            this.Column52.Name = "Column52";
            // 
            // Column53
            // 
            this.Column53.HeaderText = "小区过覆盖指数";
            this.Column53.Name = "Column53";
            // 
            // Column54
            // 
            this.Column54.HeaderText = "±(0,60°)范围内采样点比例";
            this.Column54.Name = "Column54";
            // 
            // Column55
            // 
            this.Column55.HeaderText = "±(0,60°)范围内小区平均RSRP";
            this.Column55.Name = "Column55";
            // 
            // Column23
            // 
            this.Column23.HeaderText = "±(0,60°)范围内覆盖率(RSRP≥-110&SINR>=-3)";
            this.Column23.Name = "Column23";
            // 
            // Column25
            // 
            this.Column25.HeaderText = "±(0,60°)范围内LTE覆盖率(RSRP≥-110)";
            this.Column25.Name = "Column25";
            // 
            // Column29
            // 
            this.Column29.HeaderText = "±(0,60°)范围内LTE覆盖率(SINR>=-3)";
            this.Column29.Name = "Column29";
            // 
            // Column34
            // 
            this.Column34.HeaderText = "±(60,150°)范围内采样点比例";
            this.Column34.Name = "Column34";
            // 
            // Column39
            // 
            this.Column39.HeaderText = "±(150,180°)范围内采样点比例";
            this.Column39.Name = "Column39";
            // 
            // Column45
            // 
            this.Column45.HeaderText = "路测前后比";
            this.Column45.Name = "Column45";
            // 
            // colrange1
            // 
            this.colrange1.HeaderText = "端口1幅度";
            this.colrange1.Name = "colrange1";
            // 
            // colrange2
            // 
            this.colrange2.HeaderText = "端口2幅度";
            this.colrange2.Name = "colrange2";
            // 
            // colrange3
            // 
            this.colrange3.HeaderText = "端口3幅度";
            this.colrange3.Name = "colrange3";
            // 
            // colrange4
            // 
            this.colrange4.HeaderText = "端口4幅度";
            this.colrange4.Name = "colrange4";
            // 
            // colrange5
            // 
            this.colrange5.HeaderText = "端口5幅度";
            this.colrange5.Name = "colrange5";
            // 
            // colrange6
            // 
            this.colrange6.HeaderText = "端口6幅度";
            this.colrange6.Name = "colrange6";
            // 
            // colrange7
            // 
            this.colrange7.HeaderText = "端口7幅度";
            this.colrange7.Name = "colrange7";
            // 
            // colrange8
            // 
            this.colrange8.HeaderText = "端口8幅度";
            this.colrange8.Name = "colrange8";
            // 
            // colphase1
            // 
            this.colphase1.HeaderText = "端口1相位";
            this.colphase1.Name = "colphase1";
            // 
            // colphase2
            // 
            this.colphase2.HeaderText = "端口2相位";
            this.colphase2.Name = "colphase2";
            // 
            // colphase3
            // 
            this.colphase3.HeaderText = "端口3相位";
            this.colphase3.Name = "colphase3";
            // 
            // colphase4
            // 
            this.colphase4.HeaderText = "端口4相位";
            this.colphase4.Name = "colphase4";
            // 
            // colphase5
            // 
            this.colphase5.HeaderText = "端口5相位";
            this.colphase5.Name = "colphase5";
            // 
            // colphase6
            // 
            this.colphase6.HeaderText = "端口6相位";
            this.colphase6.Name = "colphase6";
            // 
            // colphase7
            // 
            this.colphase7.HeaderText = "端口7相位";
            this.colphase7.Name = "colphase7";
            // 
            // colphase8
            // 
            this.colphase8.HeaderText = "端口8相位";
            this.colphase8.Name = "colphase8";
            // 
            // contextMenuStrip
            // 
            this.contextMenuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.显示小区ToolStripMenuItem,
            this.导出CSVToolStripMenuItem,
            this.miExportWholeExcel});
            this.contextMenuStrip.Name = "contextMenuStrip";
            this.contextMenuStrip.Size = new System.Drawing.Size(153, 92);
            // 
            // 导出CSVToolStripMenuItem
            // 
            this.导出CSVToolStripMenuItem.Name = "导出CSVToolStripMenuItem";
            this.导出CSVToolStripMenuItem.Size = new System.Drawing.Size(152, 22);
            this.导出CSVToolStripMenuItem.Text = "拆分导出CSV";
            this.导出CSVToolStripMenuItem.Click += new System.EventHandler(this.导出CSVToolStripMenuItem_Click);
            // 
            // miExportWholeExcel
            // 
            this.miExportWholeExcel.Name = "miExportWholeExcel";
            this.miExportWholeExcel.Size = new System.Drawing.Size(152, 22);
            this.miExportWholeExcel.Text = "导出Excel";
            this.miExportWholeExcel.Click += new System.EventHandler(this.miExportWholeExcel_Click);
            // 
            // 显示小区ToolStripMenuItem
            // 
            this.显示小区ToolStripMenuItem.Name = "显示小区ToolStripMenuItem";
            this.显示小区ToolStripMenuItem.Size = new System.Drawing.Size(152, 22);
            this.显示小区ToolStripMenuItem.Text = "显示小区";
            this.显示小区ToolStripMenuItem.Click += new System.EventHandler(this.显示小区ToolStripMenuItem_Click);
            // 
            // LteCellAngleForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1184, 650);
            this.Controls.Add(this.xtraTabControl1);
            this.Name = "LteCellAngleForm";
            this.Text = "LTE天线覆盖方向分析";
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).EndInit();
            this.xtraTabControl1.ResumeLayout(false);
            this.xtraTabPage4.ResumeLayout(false);
            this.xtraTabPage4.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewCell)).EndInit();
            this.contextMenuStrip.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraTab.XtraTabControl xtraTabControl1;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip;
        private System.Windows.Forms.ToolStripMenuItem miExportWholeExcel;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage4;
        private System.Windows.Forms.DataGridView dataGridViewCell;
        private System.Windows.Forms.Button btnSearch;
        private System.Windows.Forms.TextBox txtCellName;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Button btnGo;
        private System.Windows.Forms.Label labNum;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label labPage;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.TextBox txtPage;
        private System.Windows.Forms.Button btnNextpage;
        private System.Windows.Forms.Button btnPrevpage;
        private System.Windows.Forms.ToolStripMenuItem 导出CSVToolStripMenuItem;
        private System.Windows.Forms.DataGridViewTextBoxColumn colIndex;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column11;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column1;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column14;
        private System.Windows.Forms.DataGridViewTextBoxColumn colvender;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column41;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column2;
        private System.Windows.Forms.DataGridViewTextBoxColumn colcgi;
        private System.Windows.Forms.DataGridViewTextBoxColumn colcovertype;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column3;
        private System.Windows.Forms.DataGridViewTextBoxColumn colbeamwidth;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column22;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column4;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column5;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column6;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column7;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column62;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column63;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column64;
        private System.Windows.Forms.DataGridViewTextBoxColumn colgmax;
        private System.Windows.Forms.DataGridViewTextBoxColumn col3db;
        private System.Windows.Forms.DataGridViewTextBoxColumn col6db;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column8;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column9;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column10;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column12;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column13;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column15;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column16;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column17;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column18;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column19;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column20;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column56;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column57;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column58;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column59;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column60;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column61;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column21;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column26;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column24;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column27;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column28;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column30;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column31;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column32;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column33;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column35;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column36;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column37;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column38;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column40;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column42;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column43;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column46;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column44;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column47;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column48;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column49;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column50;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column51;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column52;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column53;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column54;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column55;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column23;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column25;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column29;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column34;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column39;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column45;
        private System.Windows.Forms.DataGridViewTextBoxColumn colrange1;
        private System.Windows.Forms.DataGridViewTextBoxColumn colrange2;
        private System.Windows.Forms.DataGridViewTextBoxColumn colrange3;
        private System.Windows.Forms.DataGridViewTextBoxColumn colrange4;
        private System.Windows.Forms.DataGridViewTextBoxColumn colrange5;
        private System.Windows.Forms.DataGridViewTextBoxColumn colrange6;
        private System.Windows.Forms.DataGridViewTextBoxColumn colrange7;
        private System.Windows.Forms.DataGridViewTextBoxColumn colrange8;
        private System.Windows.Forms.DataGridViewTextBoxColumn colphase1;
        private System.Windows.Forms.DataGridViewTextBoxColumn colphase2;
        private System.Windows.Forms.DataGridViewTextBoxColumn colphase3;
        private System.Windows.Forms.DataGridViewTextBoxColumn colphase4;
        private System.Windows.Forms.DataGridViewTextBoxColumn colphase5;
        private System.Windows.Forms.DataGridViewTextBoxColumn colphase6;
        private System.Windows.Forms.DataGridViewTextBoxColumn colphase7;
        private System.Windows.Forms.DataGridViewTextBoxColumn colphase8;
        private System.Windows.Forms.ToolStripMenuItem 显示小区ToolStripMenuItem;
    }
}