﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public class ScanWeakSinrSampleRoadQuery : ZTWeakSINRRoadQuery
    {
        public static ScanWeakSinrSampleRoadQuery Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new ScanWeakSinrSampleRoadQuery();
                }
                return instance;
            }
        }

        private ScanWeakSinrSampleRoadQuery()
            : base()
        {
            Columns = new List<string>();
            Columns.Add("");
            Columns.Add("LTESCAN_TopN_EARFCN");
            Columns.Add("LTESCAN_TopN_PCI");
            Columns.Add("LTESCAN_TopN_CELL_Specific_RSRP");
            Columns.Add("LTESCAN_TopN_CELL_Specific_RSSINR");
        }

        public ScanWeakSinrSampleRoadQuery(ServiceName serviceName)
            : this()
        {
            ServiceTypes.Clear();
            ServiceTypes = ServiceTypeManager.GetServiceTypesByServiceName(serviceName);
        }

        public override string Name
        {
            get { return "质差采样点统计_NBIOT扫频"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 33000, 33019, this.Name);
        }

        protected override float? getSinr(TestPoint testPoint)
        {
            return (float?)testPoint["LTESCAN_TopN_CELL_Specific_RSSINR"];
        }

        protected override float? getRsrp(TestPoint testPoint)
        {
            return (float?)testPoint["LTESCAN_TopN_CELL_Specific_RSRP"];
        }

        protected override void fireShowForm()
        {
            List<ScanWeakSinrSampleRoad> roadList = new List<ScanWeakSinrSampleRoad>();
            foreach (WeakSINRRoad road in weakCoverList)
            {
                ScanWeakSinrSampleRoad sampleRoad = new ScanWeakSinrSampleRoad(road);
                sampleRoad.GetResult();
                roadList.Add(sampleRoad);
            }

            ScanWeakSinrSampleRoadForm frm =MainModel.CreateResultForm(typeof(ScanWeakSinrSampleRoadForm)) as ScanWeakSinrSampleRoadForm;
            frm.FillData(roadList);
            frm.Visible = true;
            frm.BringToFront();
            weakCoverList = null;
        }

        private static ScanWeakSinrSampleRoadQuery instance;
    }

    public class ScanWeakSinrSampleRoad : WeakSINRRoad
    {
        public ScanWeakSinrSampleRoad(WeakSINRRoad baseRoad)
            : base(baseRoad)
        {
        }

        public List<ScanWeakSinrSampleInfo> SampleInfos
        {
            get;
            private set;
        }

        public void GetResult()
        {
            SampleInfos = new List<ScanWeakSinrSampleInfo>();
            int sn = 0;
            foreach (TestPoint tp in TestPoints)
            {
                ScanWeakSinrSampleInfo sample = new ScanWeakSinrSampleInfo(tp);
                sample.SN = ++sn;
                SampleInfos.Add(sample);
            }
        }
    }

    public class ScanWeakSinrSampleInfo
    {
        public TestPoint TestPoint
        {
            get;
            protected set;
        }

        public int SN
        {
            get;
            set;
        }

        public string TimeString
        {
            get;
            protected set;
        }

        public string CellID
        {
            get;
            protected set;
        }

        public string CellName
        {
            get;
            protected set;
        }

        public int? Earfcn
        {
            get;
            protected set;
        }

        public int? Pci
        {
            get;
            protected set;
        }

        public double Longitude
        {
            get;
            protected set;
        }

        public double Latitude
        {
            get;
            protected set;
        }

        public float? Rsrp
        {
            get;
            protected set;
        }

        public float? Sinr
        {
            get;
            protected set;
        }

        public float? Rsrq
        {
            get;
            protected set;
        }

        public ScanWeakSinrSampleInfo(TestPoint tp)
        {
            TestPoint = tp;

            TimeString = tp.DateTimeStringWithMillisecond;
            Longitude = tp.Longitude;
            Latitude = tp.Latitude;

            Earfcn = (int?)tp["LTESCAN_TopN_EARFCN"];
            Pci = (int?)(short?)tp["LTESCAN_TopN_PCI"];

            LTECell lteCell = tp.GetCell_LTEScan(0);
            if (lteCell == null)
            {
                CellID = CellName = "";
            }
            else
            {
                CellID = lteCell.ID.ToString();
                CellName = lteCell.Name;
            }

            Rsrp = (float?)tp["LTESCAN_TopN_CELL_Specific_RSRP"];
            Sinr = (float?)tp["LTESCAN_TopN_CELL_Specific_RSSINR"];
            Rsrq = (float?)tp["LTESCAN_TopN_CELL_Specific_RSRQ"];    
        }

        protected class CodingScheme : IComparable<CodingScheme>
        {
            public int? Value { get; set; }

            public string Desc { get; set; }

            public CodingScheme(int? value, string desc)
            {
                Value = value;
                Desc = desc;
            }

            public int CompareTo(CodingScheme other)
            {
                if (this == other) return 0;
                if (Value == null) return -1;
                if (other.Value == null) return 1;
                return Value > other.Value ? 1 : -1;
            }
        }
    }
}
