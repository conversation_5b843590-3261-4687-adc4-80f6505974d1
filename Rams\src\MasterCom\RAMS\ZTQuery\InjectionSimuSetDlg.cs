﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using System.IO;
using MasterCom.Util;
using DevExpress.XtraEditors;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTQuery
{
    public partial class InjectionSimuSetDlg : BaseDialog
    {
        public InjectionSimuSetDlg()
        {
            InitializeComponent();
        }

        public void GetMapSetting(out string streetInjectMap, out string streetInjectColumn, out string testMap)
        {
            streetInjectMap = edtStreetInjMap.Text.Trim();
            streetInjectColumn = cbxStreetInjColumn.Text;
            testMap = edtTestMap.Text.Trim();
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            if (edtStreetInjMap.Text.Trim() != "" && !File.Exists(edtStreetInjMap.Text.Trim()))
            {
                XtraMessageBox.Show("渗透图层文件不存在，请重新设置！");
                DialogResult = DialogResult.Retry;
                edtStreetInjMap.Focus();
                return;
            }
            if (!File.Exists(edtTestMap.Text.Trim()))
            {
                XtraMessageBox.Show("测试路径文件不存在，请重新设置！");
                DialogResult = DialogResult.Retry;
                edtTestMap.Focus();
                return;
            }
            this.DialogResult = DialogResult.OK;
        }

        private void edtStreetInjMap_ButtonClick(object sender, DevExpress.XtraEditors.Controls.ButtonPressedEventArgs e)
        {
            OpenFileDialog openFileDialog = new OpenFileDialog();
            openFileDialog.Multiselect = false;
            openFileDialog.CheckFileExists = true;
            openFileDialog.DefaultExt = "SHP";
            openFileDialog.Filter = FilterHelper.Shp;
            openFileDialog.InitialDirectory = Application.StartupPath + @"\GEOGRAPHIC";
            if (openFileDialog.ShowDialog(this) == DialogResult.OK)
            {
                MapWinGIS.Shapefile table = new MapWinGIS.Shapefile();
                try
                {
                    if (table.Open(openFileDialog.FileName, null))
                    {
                        cbxStreetInjColumn.Properties.Items.Clear();
                        int numFields = table.NumFields;
                        for (int x = 0; x < numFields; x++)
                        {
                            MapWinGIS.Field field = table.get_Field(x);
                            cbxStreetInjColumn.Properties.Items.Add(field.Name);
                        }
                        cbxStreetInjColumn.SelectedIndex = 0;
                    }
                    edtStreetInjMap.Text = openFileDialog.FileName;
                }
                catch
                {
                    //continue
                }
            }
        }

        private void edtTestMap_ButtonClick(object sender, DevExpress.XtraEditors.Controls.ButtonPressedEventArgs e)
        {
            OpenFileDialog fileDialog = new OpenFileDialog();
            fileDialog.Filter = "Shape Files (*.SHP)|*.SHP";
            if (fileDialog.ShowDialog() == DialogResult.OK)
            {
                edtTestMap.Text = fileDialog.FileName;
            }
        }
    }
}