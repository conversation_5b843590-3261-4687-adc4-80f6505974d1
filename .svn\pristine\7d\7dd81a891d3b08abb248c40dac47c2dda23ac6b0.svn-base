﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public class LastRoadCondition
    {
        public double Max2TesetPointDis { get; set; } = 50;
        public double MinLastDistance { get; set; } = 200;
        public ELogicalType LogicalType { get; set; }
        public List<TestPointValueCondition> DetailItems { get; set; } = new List<TestPointValueCondition>();
        /// <summary>
        /// 符合设定条件？
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        public bool IsTPValueMatch(TestPoint tp)
        {
            bool match = true;//最终逻辑判断结果，先假设为真
            foreach (TestPointValueCondition item in DetailItems)
            {
                bool matchOne = item.IsMatch(tp);
                switch (LogicalType)
                {
                    case ELogicalType.与:
                        match = match && matchOne;
                        if (!match)
                        {//与 关系判断，其一条件不成立，即返回不符合：false
                            return false;
                        }
                        break;
                    case ELogicalType.或:
                        if (matchOne)
                        {//或 关系判断，其一条件成立，即返回符合：true
                            return true;
                        }
                        else
                        {
                            match = false;
                        }
                        break;
                    default:
                        break;
                }
            }
            return match;
        }

        public bool Is2TPDisMatch(double distance)
        {
            return distance <= Max2TesetPointDis;
        }

        public bool IsLastDisMatch(double lastDis)
        {
            return lastDis >= MinLastDistance;
        }

        public Dictionary<string, object> CfgParam
        {
            get
            {
                Dictionary<string, object> paramDic = new Dictionary<string, object>();
                paramDic["TwoTestPointMaxDis"] = Max2TesetPointDis;
                paramDic["MinLastDistance"] = MinLastDistance;
                paramDic["LogicalType"] = LogicalType.ToString();
                List<object> subParams = new List<object>();
                foreach (TestPointValueCondition subCnd in DetailItems)
                {
                    subParams.Add(subCnd.CfgParam);
                }
                paramDic["Details"] = subParams;
                return paramDic;
            }
            set {
                if (value==null)
                {
                    return;
                }
                Max2TesetPointDis = (double)value["TwoTestPointMaxDis"];
                MinLastDistance = (double)value["MinLastDistance"];
                LogicalType = (ELogicalType)Enum.Parse(typeof(ELogicalType), value["LogicalType"].ToString());
                List<object> subParams = value["Details"] as List<object>;
                DetailItems.Clear();
                foreach (object objParam in subParams)
                {
                    TestPointValueCondition cnd = new TestPointValueCondition();
                    cnd.CfgParam = objParam as Dictionary<string, object>;
                    DetailItems.Add(cnd);
                }
            }
        }

        public string Description
        {
            get
            {
                StringBuilder sb = new StringBuilder();
                sb.Append("两采样点间距离≤");
                sb.Append(Max2TesetPointDis.ToString());
                sb.AppendLine("(米)");
                sb.Append("持续距离≥");
                sb.Append(MinLastDistance.ToString());
                sb.AppendLine("(米)");
                sb.AppendLine();
                sb.Append("采样点指标条件：");
                sb.AppendLine(string.Format("（各个条件为【{0}】关系）", LogicalType.ToString()));
                int idx = 1;
                foreach (TestPointValueCondition item in DetailItems)
                {
                    sb.Append(idx++.ToString() + "、");
                    sb.AppendLine(item.ToString());
                }
                return sb.ToString();
            }
        }

        /// <summary>
        /// 添加条件，该条件与现有条件相同，返回flase，不再添加。否则返回true。
        /// </summary>
        /// <param name="condition"></param>
        /// <returns></returns>
        internal bool Add(TestPointValueCondition condition)
        {
            TestPointValueCondition item = DetailItems.Find(delegate(TestPointValueCondition cnd)
            {
                return cnd.Equals(condition);
            });
            if (item != null)
            {
                return false;
            }
            DetailItems.Add(condition);
            return true;
        }
    }

    public enum ELogicalType
    {
        与 = 0,
        或=1,
    }

    public class TestPointValueCondition
    {
        public string SysName
        {
            get;
            set;
        }
        public string ParamName
        {
            get;
            set;
        }
        public int ParamArrayIndex { get; set; } = -1;
        public double MinValue { get; set; } = double.NaN;
        public double MaxValue { get; set; } = double.NaN;

        public override string ToString()
        {
            return string.Format("{0}≤{1}{2}≤{3}", MinValue, ParamName, ParamArrayIndex > -1 ? "[" + ParamArrayIndex.ToString() + "]" : "", MaxValue);
        }

        /// <summary>
        /// 是否符合条件
        /// </summary>
        /// <param name="tp"></param>
        /// <returns>符合true;否则false</returns>
        public bool IsMatch(TestPoint tp)
        {
            bool matched = false;
            if (!string.IsNullOrEmpty(ParamName))
            {
                DTDisplayParameterInfo disParam = DTDisplayParameterManager.GetInstance()[this.SysName][this.ParamName];
                object objValue = ParamArrayIndex >= 0 ? tp[disParam.ParamInfo.Name, ParamArrayIndex] : tp[disParam.ParamInfo.Name];
                double value;
                if (objValue != null && double.TryParse(objValue.ToString(), out value))
                {
                    matched = MinValue <= value && value <= MaxValue;
                }
            }
            return matched;
        }

        public virtual Dictionary<string, object> CfgParam
        {
            get
            {
                Dictionary<string, object> paramDic = new Dictionary<string, object>();
                paramDic["SysName"] = this.SysName;
                paramDic["ParamName"] = this.ParamName;
                paramDic["ParamArrayIndex"] = this.ParamArrayIndex;
                paramDic["MinValue"] = this.MinValue;
                paramDic["MaxValue"] = this.MaxValue;
                return paramDic;
            }
            set
            {
                if (value == null)
                {
                    return;
                }
                this.SysName = value["SysName"].ToString();
                this.ParamName = value["ParamName"].ToString();
                this.ParamArrayIndex = (int)value["ParamArrayIndex"];
                this.MinValue = (double)value["MinValue"];
                this.MaxValue = (double)value["MaxValue"];
            }
        }

        public override bool Equals(object obj)
        {
            if (obj is TestPointValueCondition)
            {
                TestPointValueCondition cnd = obj as TestPointValueCondition;
                return ToString().Equals(cnd.ToString());
            }
            return false;
        }
        public override int GetHashCode()
        {
            return this.ToString().GetHashCode();
        }
    }


}
