﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Runtime.Serialization;
using System.Text;
using MasterCom.MTGis;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc.TestDepth
{
    public class TestDepthGridLayer: CustomDrawLayer
    {
        public TestDepthGridLayer(MapOperation mp, string name)
            : base(mp, name)
        {

        }

        public double GridPercition
        {
            get;
            set;
        }
        
        public bool DrawHistory { get; set; } = true;
        public bool DrawEstimate { get; set; } = true;
        private readonly SolidBrush historyBrush = new SolidBrush(Color.FromArgb(200, Color.Blue));
        public Color HistoryColor
        {
            get { return historyBrush.Color; }
            set { historyBrush.Color = Color.FromArgb(200, value); }
        }
        private readonly SolidBrush estimateBrush = new SolidBrush(Color.FromArgb(200, Color.Orange));
        public Color EstimateColor
        {
            get { return estimateBrush.Color; }
            set { estimateBrush.Color = Color.FromArgb(200, value); }
        }

        public List<TestDepthDetail> RegionGridDetails
        {
            get;
            set;
        }

        public override void Draw(System.Drawing.Rectangle clientRect, System.Drawing.Rectangle updateRect, Graphics graphics)
        {
            if (!IsVisible)
            {
                return;
            }
            if (RegionGridDetails == null || RegionGridDetails.Count == 0)
            {
                return;
            }
            Rectangle inflatedRect = new Rectangle(updateRect.X, updateRect.Y, updateRect.Width, updateRect.Height);
            inflatedRect.Inflate((int)(40 * 10000 / Map.Scale), (int)(40 * 10000 / Map.Scale));
            DbRect dRect;
            Map.FromDisplay(updateRect, out dRect);
            float size = float.NaN;
            foreach (TestDepthDetail detail in RegionGridDetails)
            {
                fillGrid(graphics, historyBrush, dRect, detail.HistoryGrids, ref size);
                fillGrid(graphics, estimateBrush, dRect, detail.EstimateGrids, ref size);
            }
        }

        private void fillGrid(Graphics graphics,SolidBrush brush ,DbRect dRect, List<TestDepthTpGrid> grids, ref float size)
        {
            PointF ltPnt;
            foreach (TestDepthTpGrid grid in grids)
            {
                if (grid.Bounds.Within(dRect))
                {
                    Map.ToDisplay(new DbPoint(grid.LTLng, grid.LTLat), out ltPnt);
                    if (float.IsNaN(size))
                    {
                        PointF brPnt;
                        Map.ToDisplay(new DbPoint(grid.BRLng, grid.BRLat), out brPnt);
                        size = brPnt.X - ltPnt.X;
                    }
                    graphics.FillRectangle(brush, ltPnt.X, ltPnt.Y, size, size);
                }
            }
        }

    }
}
