﻿using System;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public enum DataTypePCI
    {
        Unknown = 0,
        Boolean = 1,
        Char = 2,
        Byte = 3,
        Int16 = 4,
        Int32 = 5,
        Int64 = 6,
        SByte = 7,
        UInt16 = 8,
        UInt32 = 9,
        UInt64 = 10,
        Single = 11,
        Double = 12,
        String = 13,
        DateTime = 14,
        TimePeriod = 15,
        Color = 16,
        Float = 17,
        Object = 18,
        Image = 19,
        Numeric = 20
    }

    public struct Numeric
    {
        private readonly string _selfvalue;

        public Numeric(string str)
        {
            _selfvalue = str;
        }
        public static implicit operator Numeric(string value)
        {
            return new Numeric(value);
        }
        public static implicit operator string(Numeric fix)
        {
            return fix._selfvalue;
        }
        public static implicit operator Numeric(float value)
        {
            return new Numeric(value.ToString());
        }
        public static implicit operator float(Numeric fix)
        {
            return float.Parse(fix._selfvalue);
        }
        public static implicit operator Numeric(double value)
        {
            return new Numeric(value.ToString());
        }
        public static implicit operator double(Numeric fix)
        {
            return double.Parse(fix._selfvalue);
        }
        public static implicit operator Numeric(int value)
        {
            return new Numeric(value.ToString());
        }
        public static implicit operator int(Numeric fix)
        {
            return int.Parse(fix._selfvalue);
        }
        public static implicit operator Numeric(long value)
        {
            return new Numeric(value.ToString());
        }
        public static implicit operator long(Numeric fix)
        {
            return long.Parse(fix._selfvalue);
        }
        public static implicit operator Numeric(short value)
        {
            return new Numeric(value.ToString());
        }
        public static implicit operator short(Numeric fix)
        {
            return short.Parse(fix._selfvalue);
        }
        public static implicit operator Numeric(ushort value)
        {
            return new Numeric(value.ToString());
        }
        public static implicit operator ushort(Numeric fix)
        {
            return ushort.Parse(fix._selfvalue);
        }
        public static implicit operator Numeric(uint value)
        {
            return new Numeric(value.ToString());
        }
        public static implicit operator uint(Numeric fix)
        {
            return uint.Parse(fix._selfvalue);
        }
        public static implicit operator Numeric(ulong value)
        {
            return new Numeric(value.ToString());
        }
        public static implicit operator ulong(Numeric fix)
        {
            return ulong.Parse(fix._selfvalue);
        }
        public override string ToString()
        {
            return _selfvalue;
        }
    }

    public static class DataTypeHelper2
    {
        public static Type GetType(DataTypePCI type)
        {
            switch (type)
            {
                case DataTypePCI.String:
                    return typeof(string);
                case DataTypePCI.Int32:
                    return typeof(int);
                case DataTypePCI.Double:
                    return typeof(double);
                case DataTypePCI.DateTime:
                    return typeof(DateTime);
                case DataTypePCI.Single:
                    return typeof(float);
                case DataTypePCI.Float:
                    return typeof(float);
                case DataTypePCI.Int16:
                    return typeof(short);
                case DataTypePCI.Int64:
                    return typeof(long);
                case DataTypePCI.Char:
                    return typeof(char);
                case DataTypePCI.SByte:
                    return typeof(sbyte);
                case DataTypePCI.UInt16:
                    return typeof(ushort);
                case DataTypePCI.UInt32:
                    return typeof(uint);
                case DataTypePCI.UInt64:
                    return typeof(ulong);
                case DataTypePCI.Numeric:
                    return typeof(Numeric);
                case DataTypePCI.Boolean:
                    return typeof(bool);
                case DataTypePCI.Color:
                    return typeof(System.Drawing.Color);
                case DataTypePCI.Byte:
                    return typeof(byte);
                case DataTypePCI.TimePeriod:
                    return typeof(TimePeriod);
                case DataTypePCI.Image:
                    return typeof(byte[]);
                default:
                    return typeof(object);
            }
        }

        public static DataTypePCI GetDataType(Type type)
        {
            foreach (DataTypePCI dt in Enum.GetValues(typeof(DataTypePCI)))
            {
                if (type.Name.ToLower().Equals(dt.ToString().ToLower()))
                    return dt;
            }
            return DataTypePCI.Unknown;
        }
    }
}
