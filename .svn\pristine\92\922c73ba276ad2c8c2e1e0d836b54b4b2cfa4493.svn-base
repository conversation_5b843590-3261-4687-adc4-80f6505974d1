﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Frame;
using DevExpress.XtraGrid.Views.Base;
using DevExpress.XtraGrid.Columns;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.TableViewer
{
    public partial class TableViewerForm : ChildForm
    {
        private bool isCompare = true;
        public TableViewerForm()
        {
            InitializeComponent();
            gridView1.OptionsSelection.EnableAppearanceFocusedCell = false;
            gridView1.OptionsSelection.EnableAppearanceHideSelection = false;
            this.LogItemSrc = new UserMng.LogInfoItem(2, 20000, 20021, this.Text);
        }

        public override void Init()
        {
            MainModel.DistrictChanged += districtChanged;
            MainModel.DTDataChanged += dtDataChanged;
            MainModel.SelectedTestPointsChanged += selectedTestPointsChanged;
            MainModel.SelectedEventsChanged += selectedEventsChanged;
            MainModel.SelectedMessageChanged += selectedMessageChanged;
            Disposed += disposed;
            initDefaultColumns();
            dtDataChanged(null, null);

        }

        private void initDefaultColumns()
        {
            if (displayColumns == null)
            {
                displayColumns = new List<TVColumnInfo>();
                if (displayColumnsParamName != null && displayColumnsParamIdx != null && displayColumnsType != null && displayColumnsParamSystemName != null)
                {
                    addDefaultColumns(displayColumnsType.Count);
                }
                else
                {
                    displayColumns.Add(new TVColumnInfo("序号"));
                    displayColumns.Add(new TVColumnInfo("时间"));
                    displayColumns.Add(new TVColumnInfo("毫秒"));
                    displayColumns.Add(new TVColumnInfo("经度"));
                    displayColumns.Add(new TVColumnInfo("纬度"));
                }
                quickColumnDic = new Dictionary<string, TVColumnInfo>();
                foreach (TVColumnInfo tvi in displayColumns)
                {
                    quickColumnDic[tvi.columnName] = tvi;
                }
            }
        }

        private void addDefaultColumns(int count)
        {
            for (int i = 0; i < count; i++)
            {
                if ((int)displayColumnsType[i] == (int)TVColumnInfo.ColumnParaType.FixColumn)//基本列
                {
                    displayColumns.Add(new TVColumnInfo(displayColumnsParamName[i].ToString()));
                }
                else
                {
                    string systemName = displayColumnsParamSystemName[i].ToString();
                    string paramName = displayColumnsParamName[i].ToString();
                    DTDisplayParameterInfo paramInfo = DTDisplayParameterManager.GetInstance()[systemName, paramName];
                    int arrIndex = (int)displayColumnsParamIdx[i];
                    string columnName = paramInfo.Name;
                    if (paramInfo.IsArray && paramInfo.ArrayBounds > 1)
                    {
                        columnName = paramInfo.Name + "_" + arrIndex;
                    }
                    columnName = systemName + ":" + columnName;
                    TVColumnInfo tvColumnInfo = new TVColumnInfo(columnName, paramInfo);
                    tvColumnInfo.ArrayIndex = arrIndex;
                    displayColumns.Add(tvColumnInfo);
                }
            }
        }

        private void disposed(object sender, EventArgs e)
        {
            MainModel.DistrictChanged -= districtChanged;
            MainModel.DTDataChanged -= dtDataChanged;
            MainModel.SelectedTestPointsChanged -= selectedTestPointsChanged;
            MainModel.SelectedEventsChanged -= selectedEventsChanged;
            MainModel.SelectedMessageChanged -= selectedMessageChanged;
            rowDataMap.Clear();
            dtDataIndexs.Clear();
        }

        private void districtChanged(object sender, EventArgs e)
        {
            dtDataChanged(null, null);
        }

        private void dtDataChanged(object sender, EventArgs e)
        {
            ColumnView view = gridControl.MainView as ColumnView;
            Dictionary<string, int> dicColUnVisible = new Dictionary<string, int>();//用来记录哪些列是不需要显示的

            //写入表头
            addTableHead(view, dicColUnVisible);

            //重新写入数据
            resetColumnData(view, dicColUnVisible);

            DataTable dataTable = new DataTable();
            addColumnToDataTable(dataTable);

            //==rows
            int rdx = 0;
            rowDataMap.Clear();
            dtDataIndexs.Clear();
            foreach (DTFileDataManager fileDataManager in MainModel.DTDataManager.FileDataManagers)
            {
                if (MainModel.IsFileReplayByCompareMode)
                {
                    if (isCompare && fileDataManager.MoMtFlag != (int)MoMtFile.MoFlag)
                    {
                        continue;
                    }
                    else if (!isCompare && fileDataManager.MoMtFlag != (int)MoMtFile.MtFlag)
                    {
                        continue;
                    }
                }
                int tpIndex = 0;
                foreach (TestPoint testPoint in fileDataManager.TestPoints)
                {
                    rdx++;
                    DataRow dataRow = dataTable.NewRow();
                    addRowData(rdx, testPoint, dataRow);
                    dataTable.Rows.Add(dataRow);
                    DTDataIndex dtIndex = new DTDataIndex(fileDataManager.FileID, DTDataType.TestPoint, tpIndex++);
                    rowDataMap[dataRow] = dtIndex;
                    dtDataIndexs.Add(dtIndex);
                }
            }
            this.gridControl.DataSource = dataTable;
        }

        private void addRowData(int rdx, TestPoint testPoint, DataRow dataRow)
        {
            foreach (TVColumnInfo tvColumn in displayColumns)
            {
                if (tvColumn.eColumnParaType == TVColumnInfo.ColumnParaType.FixColumn)
                {
                    addFixColumnData(rdx, testPoint, dataRow, tvColumn);
                }
                else
                {
                    addOtherColumnData(testPoint, dataRow, tvColumn);
                }
            }
        }

        private void addOtherColumnData(TestPoint testPoint, DataRow dataRow, TVColumnInfo tvColumn)
        {
            object vobj = null;
            DTParameterInfo info = tvColumn.paramInfo.ParamInfo;
            if (tvColumn.ArrayIndex == -1)
            {
                vobj = testPoint[info.Name];
            }
            else
            {
                vobj = testPoint[info.Name, tvColumn.ArrayIndex];
            }

            if (vobj != null && tvColumn.paramInfo.ParamInfo.ValueType != DTParameterValueType.String)
            {
                float fv;
                if (float.TryParse(vobj.ToString(), out fv)
                    && (fv > tvColumn.paramInfo.ValueMax || fv < tvColumn.paramInfo.ValueMin))
                {
                    vobj = null;//若在值域外，不显示
                }
            }
            dataRow[tvColumn.columnName] = vobj;
        }

        private void addFixColumnData(int rdx, TestPoint testPoint, DataRow dataRow, TVColumnInfo tvColumn)
        {
            if (tvColumn.columnName == "序号")
            {
                dataRow["序号"] = rdx;
            }
            else if (tvColumn.columnName == "时间")
            {
                dataRow["时间"] = testPoint.DateTimeStringWithMillisecond;
            }
            else if (tvColumn.columnName == "毫秒")
            {
                dataRow["毫秒"] = testPoint.Millisecond;
            }
            else if (tvColumn.columnName == "经度")
            {
                dataRow["经度"] = testPoint.Longitude;
            }
            else if (tvColumn.columnName == "纬度")
            {
                dataRow["纬度"] = testPoint.Latitude;
            }
        }

        private void addColumnToDataTable(DataTable dataTable)
        {
            foreach (TVColumnInfo tvColumn in displayColumns)
            {
                if (tvColumn.eColumnParaType == TVColumnInfo.ColumnParaType.FixColumn)
                {
                    if (tvColumn.columnName == "序号" || tvColumn.columnName == "毫秒")
                    {
                        dataTable.Columns.Add(tvColumn.columnName, typeof(int));
                    }
                    else if (tvColumn.columnName == "时间")
                    {
                        dataTable.Columns.Add(tvColumn.columnName, typeof(string));
                    }
                    else if (tvColumn.columnName == "经度" || tvColumn.columnName == "纬度")
                    {
                        dataTable.Columns.Add(tvColumn.columnName, typeof(double));
                    }
                }
                else
                {
                    string columnName = tvColumn.columnName;
                    dataTable.Columns.Add(columnName);
                }
            }
        }

        private void resetColumnData(ColumnView view, Dictionary<string, int> dicColUnVisible)
        {
            view.Columns.Clear();
            for (int c = 0; c < displayColumns.Count; c++)
            {
                TVColumnInfo tvColumn = displayColumns[c];
                string columnName = tvColumn.columnName;
                string columnCaption = tvColumn.columnName;
                if (tvColumn.eColumnParaType == TVColumnInfo.ColumnParaType.ParamInfo && tvColumn.ArrayIndex >= 0)
                {
                    columnCaption = tvColumn.paramInfo.Name + "[" + tvColumn.ArrayIndex + "]";
                }
                GridColumn column = view.Columns.AddField(columnName);
                column.Tag = displayColumns[c];
                column.VisibleIndex = c;
                if (dicColUnVisible.ContainsKey(columnCaption))
                {
                    column.Visible = false;
                }
                else
                {
                    column.Visible = true;
                }

                if (columnName.Equals("毫秒"))
                {
                    column.Visible = false;
                }
                column.OptionsColumn.AllowEdit = false;
                column.OptionsFilter.AllowFilter = true;
                column.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.True;
                column.OptionsColumn.AllowIncrementalSearch = false;
                column.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.True;
                column.OptionsColumn.AllowMove = true;
                column.OptionsColumn.AllowSize = true;
                column.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.True;
                column.OptionsColumn.FixedWidth = false;
                column.Caption = columnCaption;
            }
        }

        private void addTableHead(ColumnView view, Dictionary<string, int> dicColUnVisible)
        {
            for (int i = 0; i < view.Columns.Count; i++)
            {
                if (!view.Columns[i].Visible)
                {
                    string colName = ((TVColumnInfo)view.Columns[i].Tag).columnName;

                    dicColUnVisible.Add(colName, 1);
                }
            }
        }

        private void selectedTestPointsChanged(object sender, EventArgs e)
        {
            if (sender != this && MainModel.SelectedTestPoints.Count > 0)
            {
                TestPoint testPoint = MainModel.SelectedTestPoints[0];
                selectedRelativeRow(testPoint.SN, false);
            }
        }

        private void selectedEventsChanged(object sender, EventArgs e)
        {
            if (sender != this && MainModel.SelectedEvents.Count > 0)
            {
                Event evt = MainModel.SelectedEvents[0];
                selectedRelativeRow(evt.SN, false);
            }
        }

        private void selectedMessageChanged(object sender, EventArgs e)
        {
            if (sender != this && MainModel.SelectedMessage != null)
            {
                Model.Message message = MainModel.SelectedMessage;
                selectedRelativeRow(message.SN, true);
            }
        }

        private void gridControl_DragEnter(object sender, DragEventArgs e)
        {
            if (e.Data.GetDataPresent(typeof(TreeNode)))
                e.Effect = DragDropEffects.All;
            else
                e.Effect = DragDropEffects.None;
        }

        private void gridControl_DragDrop(object sender, DragEventArgs e)
        {
            if (e.Data.GetDataPresent(typeof(TreeNode)))
            {
                TreeNode dropNode = (TreeNode)(e.Data.GetData(typeof(TreeNode)));
                DTDisplayParameterInfo paramInfo = dropNode.Tag as DTDisplayParameterInfo;
                if (paramInfo != null)
                {
                    string paramColumnName = paramInfo.Name;
                    int arrIndex = -1;
                    if (paramInfo.IsArray && paramInfo.ArrayBounds > 1)
                    {
                        ParamArrayIndexSelDlg dlg = new ParamArrayIndexSelDlg(paramInfo.ArrayBounds, paramInfo.Name);
                        if (DialogResult.OK == dlg.ShowDialog())
                        {
                            arrIndex = dlg.GetSelArrayIndex();
                            paramColumnName = paramInfo.Name + "_" + arrIndex;
                        }
                        else
                        {
                            return;
                        }
                    }
                    paramColumnName = paramInfo.System.Name + ":" + paramColumnName;
                    addDragColumn(paramInfo, paramColumnName, arrIndex);
                }
            }
        }

        private void addDragColumn(DTDisplayParameterInfo paramInfo, string paramColumnName, int arrIndex)
        {
            if (!quickColumnDic.ContainsKey(paramColumnName))
            {
                TVColumnInfo tvNewColumnInfo = new TVColumnInfo(paramColumnName, paramInfo);
                tvNewColumnInfo.ArrayIndex = arrIndex;
                displayColumns.Add(tvNewColumnInfo);
                quickColumnDic[paramColumnName] = tvNewColumnInfo;
                dtDataChanged(null, null);
            }
            else
            {
                ColumnView view = gridControl.MainView as ColumnView;

                for (int i = 0; i < view.Columns.Count; i++)
                {
                    GridColumn column = view.Columns[i];
                    if (((TVColumnInfo)column.Tag).columnName == paramColumnName)
                    {
                        column.Visible = true;
                    }
                }
                /*shielded by wj
                 * 自动显示已隐藏的column
                MainModel.GetInstance().MainForm.ShowTipOptionAtStatusBar("所选参数在列表中已经存在！是否隐藏了该列？", Color.Red)
                 * shielded by wj end*/
            }
        }

        private void gridView_RowClick(object sender, DevExpress.XtraGrid.Views.Grid.RowClickEventArgs e)
        {
            DataRowView rowView = gridView1.GetRow(e.RowHandle) as DataRowView;
            if (rowView == null || !rowDataMap.ContainsKey(rowView.Row))
            {
                return;
            }

            DTDataIndex dtDataIndex = rowDataMap[rowView.Row];
            DTData dtData = MainModel.DTDataManager[dtDataIndex];
            if (dtDataIndex.DTDataType == DTDataType.TestPoint)
            {
                MainModel.ClearSelectedTestPoints();
                MainModel.ClearSelectedEvents();
                MainModel.SelectedMessage = null;
                dtData.Selected = true;
                MainModel.SelectedTestPoints.Add((TestPoint)dtData);
                MainModel.FireSelectedTestPointsChanged(this);
            }
        }

        private void toolBarBtnRestore_Click(object sender, EventArgs e)
        {
            displayColumns = null;
            displayColumnsParamName = null;
            displayColumnsParamIdx = null;
            displayColumnsType = null;
            displayColumnsParamSystemName = null;
            initDefaultColumns();
            dtDataChanged(null, null);
        }

        private void miExportExcel_Click(object sender, EventArgs e)
        {
            DataTable dataTable = this.gridControl.DataSource as DataTable;
            if (dataTable.Rows.Count > 65535)
            {
                MessageBox.Show(this, "数据超过65535，请保存为txt格式！", "提示");
                return;
            }
            ColumnView view = gridControl.MainView as ColumnView;

            List<int> lstVisibleCols = new List<int>();//用来记录哪些列是需要显示的

            //写入表头
            for (int i = 0; i < view.Columns.Count; i++)
            {
                if (view.Columns[i].Visible)
                {
                    lstVisibleCols.Add(i);
                }
            }

            string strLog = this.Text + "_启用导出Excel功能 尝试导出数据:" + dataTable.Rows.Count + "条";
            MainModel.GetInstance().MainForm.MakeLog(new UserMng.LogInfoItem(2, 0000, 0000, strLog));
            ExcelNPOIManager.ExportToExcel(dataTable, lstVisibleCols, true);

            /**
            System.String resultFile = "";
            SaveFileDialog saveFileDialog = new SaveFileDialog();
            saveFileDialog.Filter = "Excel file (*.xls)|*.xls";
            saveFileDialog.RestoreDirectory = true;
            if (saveFileDialog.ShowDialog() == DialogResult.OK)
            {
                resultFile = saveFileDialog.FileName;
                if (System.IO.File.Exists(resultFile))
                {
                    System.IO.File.Delete(resultFile);
                }
                WaitBox.Show("开始导出数据到excel...", ExportToFile, resultFile);
                //ExportToFile(resultFile);
                if (DialogResult.Yes == MessageBox.Show(this, "Excel文件保存完毕，是否现在打开?", "打开文件", MessageBoxButtons.YesNo))
                {
                    try
                    {
                        System.Diagnostics.Process.Start(resultFile);
                    }
                    catch
                    {
                        MessageBox.Show("打开失败!\r\n文件名:" + resultFile);
                    }
                }
            }*/
        }

        private void ExportToFile(object file)
        {
            string resultFile = (string)file;
            System.IO.FileStream fs = new System.IO.FileStream(resultFile, System.IO.FileMode.CreateNew, System.IO.FileAccess.Write, System.IO.FileShare.Read);
            System.IO.StreamWriter sw = new System.IO.StreamWriter(fs, Encoding.Default);

            try
            {
                StringBuilder sb = new StringBuilder();
                ColumnView view = gridControl.MainView as ColumnView;
                Dictionary<string, int> dicVisibleCols = new Dictionary<string, int>();//用来记录哪些列是需要显示的

                addTableHeadStream(sw, sb, view, dicVisibleCols);
                addTableContentStream(sw, sb, dicVisibleCols);
            }
            catch
            {
                //continue
            }
            finally
            {
                if (sw != null)
                {
                    sw.Close();
                }
                if (fs != null)
                {
                    fs.Close();
                }
                WaitBox.Close();
            }
        }

        private void addTableContentStream(System.IO.StreamWriter sw, StringBuilder sb, Dictionary<string, int> dicVisibleCols)
        {
            //写入内容
            DataTable dataTable = this.gridControl.DataSource as DataTable;
            int progress = 0;
            int index = 0;
            foreach (DataRow dataRow in dataTable.Rows)
            {
                sb.Remove(0, sb.Length);

                foreach (TVColumnInfo tvColumn in displayColumns)
                {
                    if (dicVisibleCols.ContainsKey(tvColumn.columnName))
                    {
                        sb.Append(dataRow[tvColumn.columnName]);
                        sb.Append("\t");
                    }
                }
                sw.WriteLine(sb.ToString());

                changeProgess(ref progress, ref index);
            }
        }

        private static void changeProgess(ref int progress, ref int index)
        {
            if (Math.Log(index++) * (100000 / 10000) > WaitBox.ProgressPercent)
            {
                progress++;
                if (progress > 95)
                {
                    progress = 5;
                    index = 0;
                }
                if (progress % 5 == 0)
                {
                    WaitBox.ProgressPercent = progress;
                }
            }
        }

        private void addTableHeadStream(System.IO.StreamWriter sw, StringBuilder sb, ColumnView view, Dictionary<string, int> dicVisibleCols)
        {
            //写入表头
            for (int i = 0; i < view.Columns.Count; i++)
            {
                if (view.Columns[i].Visible)
                {
                    sb.Append(((TVColumnInfo)view.Columns[i].Tag).columnName);
                    sb.Append("\t");

                    dicVisibleCols.Add(((TVColumnInfo)view.Columns[i].Tag).columnName, 1);
                }
            }
            sw.WriteLine(sb.ToString());
        }

        private void miExportTxt_Click(object sender, EventArgs e)
        {
            string strLog = this.Text + "_启用导出txt功能 尝试导出数据:" + (this.gridControl.DataSource as DataTable).Rows.Count + "条";
            MainModel.GetInstance().MainForm.MakeLog(new UserMng.LogInfoItem(2, 0000, 0000, strLog));
            ExportResultSecurityHelper.ExportToTxt(ExportToFile, ExportResultSecurityHelper.ObjFileName, true);
        }

        private void selectedRelativeRow(int sn, bool isMessage)
        {
            try
            {
                int selectedRowIndex = -1;
                for (int index = 0; index < dtDataIndexs.Count; index++)
                {
                    DTDataIndex dtDataIndex = dtDataIndexs[index];
                    int curSn = MainModel.DTDataManager[dtDataIndex].SN;
                    if (curSn > (isMessage ? sn + 1 : sn))
                    {
                        break;
                    }
                    else if (curSn == sn)
                    {
                        selectedRowIndex = index;
                        break;
                    }
                    selectedRowIndex = index;
                }
                if (selectedRowIndex == -1)
                {
                    return;
                }

                int rowHandle = gridView1.GetRowHandle(selectedRowIndex);
                gridView1.SelectRow(rowHandle);
                gridView1.FocusedRowHandle = rowHandle;
            }
            catch
            {
                //continue
            }
        }

        public override Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                List<object> columnsParamSystem = new List<object>();
                List<object> columnsParamName = new List<object>();
                List<object> columnsParamIndex = new List<object>();
                List<object> columnsType = new List<object>();
                foreach (GridColumn column in gridView1.Columns)
                {
                    if (column.Visible)
                    {
                        TVColumnInfo columnInfo = column.Tag as TVColumnInfo;
                        if (columnInfo.paramInfo != null)
                        {
                            columnsParamSystem.Add(columnInfo.paramInfo.System.Name);
                            columnsParamName.Add(columnInfo.paramInfo.Name);
                        }
                        else
                        {
                            columnsParamSystem.Add(null);
                            columnsParamName.Add(columnInfo.columnName);
                        }
                        columnsParamIndex.Add(columnInfo.ArrayIndex);
                        columnsType.Add((int)columnInfo.eColumnParaType);
                    }
                }
                param["ColumnsParamSystemName"] = columnsParamSystem;
                param["ColumnsParamName"] = columnsParamName;
                param["ColumnsParamIndex"] = columnsParamIndex;
                param["ColumnsType"] = columnsType;
                return param;
            }
            set
            {
                if (value != null && value.Count > 0)
                {
                    if (value.ContainsKey("ColumnsParamSystemName"))
                    {
                        displayColumnsParamSystemName = (List<object>)value["ColumnsParamSystemName"];
                    }
                    if (value.ContainsKey("ColumnsParamName"))
                    {
                        displayColumnsParamName = (List<object>)value["ColumnsParamName"];
                    }
                    if (value.ContainsKey("ColumnsParamIndex"))
                    {
                        displayColumnsParamIdx = (List<object>)value["ColumnsParamIndex"];
                    }
                    if (value.ContainsKey("ColumnsType"))
                    {
                        displayColumnsType = (List<object>)value["ColumnsType"];
                    }
                }
            }
        }

        private List<object> displayColumnsParamName = null;
        private List<object> displayColumnsParamSystemName = null;
        private List<object> displayColumnsParamIdx = null;
        private List<object> displayColumnsType = null;
        private Dictionary<DataRow, DTDataIndex> rowDataMap = new Dictionary<DataRow, DTDataIndex>();
        private List<DTDataIndex> dtDataIndexs = new List<DTDataIndex>();
        private static List<TVColumnInfo> displayColumns { get; set; }
        private static Dictionary<string, TVColumnInfo> quickColumnDic { get; set; }

        private void numHeight_EditValueChanged(object sender, EventArgs e)
        {
            gridView1.ColumnPanelRowHeight = (int)numHeight.Value;
        }

        private void ToolStripMenuItemCompare_Click(object sender, EventArgs e)
        {
            isCompare = false;
            Dictionary<string, object> actionParam = new Dictionary<string, object>();
            actionParam["MainForm"] = MainModel.MainForm;
            actionParam["AssemblyName"] = "RAMS.exe";
            actionParam["TypeName"] = "MasterCom.RAMS.TableViewer.TableViewerForm";
            actionParam["Text"] = "数据列表--对比回放";
            actionParam["ImageFilePath"] = @"images\dataview.gif";
            MainForm.newChildForm(actionParam);
            MainModel.FireDTDataChanged(MainForm);
        }

        private void miExportExcelDefault_Click(object sender, EventArgs e)
        {
            DataTable dataTable = this.gridControl.DataSource as DataTable;
            if (dataTable.Rows.Count > 65535)
            {
                MessageBox.Show(this, "数据超过65535，请保存为txt格式！", "提示");
                return;
            }
            ColumnView view = gridControl.MainView as ColumnView;

            List<int> lstVisibleCols = new List<int>();//用来记录哪些列是需要显示的

            //写入表头
            for (int i = 0; i < view.Columns.Count; i++)
            {
                if (view.Columns[i].Visible)
                {
                    lstVisibleCols.Add(i);
                }
            }

            string strLog = this.Text + "_启用导出Excel功能 尝试导出数据:" + dataTable.Rows.Count + "条";
            MainModel.GetInstance().MainForm.MakeLog(new MasterCom.RAMS.UserMng.LogInfoItem(2, 0000, 0000, strLog));
            string path = Application.StartupPath + "采样点.xlsx";
            ExcelNPOIManager.ExportToExcel(dataTable, lstVisibleCols, true, path);
        }
    }

    public class TVColumnInfo
    {
        public enum ColumnParaType
        {
            FixColumn = 1,//时间，经纬度等基础指标
            ParamInfo = 2
        }

        public ColumnParaType eColumnParaType { get; set; }
        public string columnName { get; set; }
        public DTDisplayParameterInfo paramInfo { get; set; }
        public int ArrayIndex { get; set; } = -1;

        public TVColumnInfo(string colname)
        {
            this.columnName = colname;
            eColumnParaType = ColumnParaType.FixColumn;
        }

        public TVColumnInfo(string colname, DTDisplayParameterInfo pInfo)
        {
            this.eColumnParaType = ColumnParaType.ParamInfo;
            this.columnName = colname;
            this.paramInfo = pInfo;
        }
    };
}