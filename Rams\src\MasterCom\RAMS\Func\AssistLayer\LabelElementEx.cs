using System;
using System.Collections.Generic;
using System.Text;
using GeneGraph;
using System.Drawing;
using MasterCom.MTGis;

namespace MasterCom.RAMS.Func.AssistLayer
{
    [Serializable()]
    public class LabelElementEx:LabelElement
    {
        public DbPoint LocationM { get; set; }
        
        public IGisAdapter GisAdapter { get; set; }

        public override void Draw(System.Drawing.Graphics g)
        {
            if (!Visible) return;
            PointF pf;
            GisAdapter.ToDisplay(LocationM, out pf);
            if (pf.X < -100 || pf.X > 5000 || pf.Y < -100 || pf.Y > 5000)
                return;
            this.Location = pf;
            base.Draw(g);
        }

        public RectangleF GetTestBound(System.Drawing.Graphics g)
        {
            PointF pf;
            GisAdapter.ToDisplay(LocationM, out pf);
            SizeF sizeF = MyFont.MeasureString(g, text, this.scaleX);
            return new RectangleF(pf.X - sizeF.Width / 2, pf.Y - sizeF.Height / 2,
                                sizeF.Width, sizeF.Height);
        }
    }
}
