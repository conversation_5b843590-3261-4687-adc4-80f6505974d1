﻿namespace MasterCom.RAMS.BackgroundFunc
{
    partial class WeakRoadAutoSettingDlg
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.Windows.Forms.ColumnHeader columnHeader1;
            System.Windows.Forms.ColumnHeader columnHeader4;
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.numLastDisMax = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl7 = new DevExpress.XtraEditors.LabelControl();
            this.numLastDisMin = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl6 = new DevExpress.XtraEditors.LabelControl();
            this.groupControl2 = new DevExpress.XtraEditors.GroupControl();
            this.labelControl10 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl9 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.numWeakPerMin = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl13 = new DevExpress.XtraEditors.LabelControl();
            this.numRsrpMax = new DevExpress.XtraEditors.SpinEdit();
            this.splitContainerControl1 = new DevExpress.XtraEditors.SplitContainerControl();
            this.groupControl4 = new DevExpress.XtraEditors.GroupControl();
            this.btnNewReport = new DevExpress.XtraEditors.SimpleButton();
            this.btnRemoveReport = new DevExpress.XtraEditors.SimpleButton();
            this.listAllReport = new System.Windows.Forms.ListBox();
            this.btnSave = new DevExpress.XtraEditors.SimpleButton();
            this.groupControl5 = new DevExpress.XtraEditors.GroupControl();
            this.cbxSaveType = new DevExpress.XtraEditors.ComboBoxEdit();
            this.chkCanUse = new DevExpress.XtraEditors.CheckEdit();
            this.labelControl14 = new DevExpress.XtraEditors.LabelControl();
            this.txtFilePath = new System.Windows.Forms.TextBox();
            this.txtFileName = new DevExpress.XtraEditors.TextEdit();
            this.labelControl12 = new DevExpress.XtraEditors.LabelControl();
            this.txtFuncId = new DevExpress.XtraEditors.TextEdit();
            this.labelControl11 = new DevExpress.XtraEditors.LabelControl();
            this.btnSelectFilePath = new DevExpress.XtraEditors.SimpleButton();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.groupControl6 = new DevExpress.XtraEditors.GroupControl();
            this.checkAllCity = new System.Windows.Forms.CheckBox();
            this.label1 = new System.Windows.Forms.Label();
            this.listViewCity = new System.Windows.Forms.ListView();
            this.labelControl15 = new DevExpress.XtraEditors.LabelControl();
            this.lbSvCount = new System.Windows.Forms.Label();
            this.btnPopupService = new DevExpress.XtraEditors.SimpleButton();
            this.listViewService = new System.Windows.Forms.ListView();
            this.label12 = new System.Windows.Forms.Label();
            this.labelControl5 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.numRecentMonths = new DevExpress.XtraEditors.SpinEdit();
            this.txtProjects = new DevExpress.XtraEditors.TextEdit();
            this.labelControl8 = new DevExpress.XtraEditors.LabelControl();
            this.groupControl3 = new DevExpress.XtraEditors.GroupControl();
            this.numTpCellAngleMax = new DevExpress.XtraEditors.SpinEdit();
            this.label10 = new System.Windows.Forms.Label();
            this.label9 = new System.Windows.Forms.Label();
            this.numTpCellDisMax = new DevExpress.XtraEditors.SpinEdit();
            this.label13 = new System.Windows.Forms.Label();
            this.label14 = new System.Windows.Forms.Label();
            this.folderBrowserDialog1 = new System.Windows.Forms.FolderBrowserDialog();
            this.toolStripDropDownService = new System.Windows.Forms.ToolStripDropDown();
            columnHeader1 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            columnHeader4 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numLastDisMax.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numLastDisMin.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).BeginInit();
            this.groupControl2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numWeakPerMin.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRsrpMax.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).BeginInit();
            this.splitContainerControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl4)).BeginInit();
            this.groupControl4.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl5)).BeginInit();
            this.groupControl5.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cbxSaveType.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkCanUse.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtFileName.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtFuncId.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl6)).BeginInit();
            this.groupControl6.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numRecentMonths.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtProjects.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl3)).BeginInit();
            this.groupControl3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numTpCellAngleMax.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numTpCellDisMax.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // columnHeader1
            // 
            columnHeader1.Width = 150;
            // 
            // columnHeader4
            // 
            columnHeader4.Width = 140;
            // 
            // groupControl1
            // 
            this.groupControl1.Controls.Add(this.labelControl2);
            this.groupControl1.Controls.Add(this.numLastDisMax);
            this.groupControl1.Controls.Add(this.labelControl7);
            this.groupControl1.Controls.Add(this.numLastDisMin);
            this.groupControl1.Controls.Add(this.labelControl6);
            this.groupControl1.Dock = System.Windows.Forms.DockStyle.Top;
            this.groupControl1.Location = new System.Drawing.Point(0, 59);
            this.groupControl1.Name = "groupControl1";
            this.groupControl1.Size = new System.Drawing.Size(478, 57);
            this.groupControl1.TabIndex = 6;
            this.groupControl1.Text = "距离";
            // 
            // labelControl2
            // 
            this.labelControl2.Location = new System.Drawing.Point(297, 29);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(22, 14);
            this.labelControl2.TabIndex = 6;
            this.labelControl2.Text = "(米)";
            // 
            // numLastDisMax
            // 
            this.numLastDisMax.EditValue = new decimal(new int[] {
            500,
            0,
            0,
            0});
            this.numLastDisMax.Location = new System.Drawing.Point(229, 26);
            this.numLastDisMax.Name = "numLastDisMax";
            this.numLastDisMax.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numLastDisMax.Properties.MaxValue = new decimal(new int[] {
            9999999,
            0,
            0,
            0});
            this.numLastDisMax.Properties.MinValue = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numLastDisMax.Size = new System.Drawing.Size(62, 21);
            this.numLastDisMax.TabIndex = 5;
            // 
            // labelControl7
            // 
            this.labelControl7.Location = new System.Drawing.Point(118, 29);
            this.labelControl7.Name = "labelControl7";
            this.labelControl7.Size = new System.Drawing.Size(22, 14);
            this.labelControl7.TabIndex = 4;
            this.labelControl7.Text = "(米)";
            // 
            // numLastDisMin
            // 
            this.numLastDisMin.EditValue = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.numLastDisMin.Location = new System.Drawing.Point(50, 26);
            this.numLastDisMin.Name = "numLastDisMin";
            this.numLastDisMin.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numLastDisMin.Properties.MaxValue = new decimal(new int[] {
            9999999,
            0,
            0,
            0});
            this.numLastDisMin.Properties.MinValue = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numLastDisMin.Size = new System.Drawing.Size(62, 21);
            this.numLastDisMin.TabIndex = 3;
            // 
            // labelControl6
            // 
            this.labelControl6.Location = new System.Drawing.Point(148, 29);
            this.labelControl6.Name = "labelControl6";
            this.labelControl6.Size = new System.Drawing.Size(70, 14);
            this.labelControl6.TabIndex = 2;
            this.labelControl6.Text = "<持续距离 <";
            // 
            // groupControl2
            // 
            this.groupControl2.Controls.Add(this.labelControl10);
            this.groupControl2.Controls.Add(this.labelControl9);
            this.groupControl2.Controls.Add(this.labelControl4);
            this.groupControl2.Controls.Add(this.numWeakPerMin);
            this.groupControl2.Controls.Add(this.labelControl13);
            this.groupControl2.Controls.Add(this.numRsrpMax);
            this.groupControl2.Dock = System.Windows.Forms.DockStyle.Top;
            this.groupControl2.Location = new System.Drawing.Point(0, 0);
            this.groupControl2.Name = "groupControl2";
            this.groupControl2.Size = new System.Drawing.Size(478, 59);
            this.groupControl2.TabIndex = 7;
            this.groupControl2.Text = "指标";
            // 
            // labelControl10
            // 
            this.labelControl10.Location = new System.Drawing.Point(155, 29);
            this.labelControl10.Name = "labelControl10";
            this.labelControl10.Size = new System.Drawing.Size(24, 14);
            this.labelControl10.TabIndex = 8;
            this.labelControl10.Text = "dbm";
            // 
            // labelControl9
            // 
            this.labelControl9.Location = new System.Drawing.Point(358, 29);
            this.labelControl9.Name = "labelControl9";
            this.labelControl9.Size = new System.Drawing.Size(12, 14);
            this.labelControl9.TabIndex = 7;
            this.labelControl9.Text = "%";
            // 
            // labelControl4
            // 
            this.labelControl4.Location = new System.Drawing.Point(209, 29);
            this.labelControl4.Name = "labelControl4";
            this.labelControl4.Size = new System.Drawing.Size(81, 14);
            this.labelControl4.TabIndex = 6;
            this.labelControl4.Text = "弱覆盖点占比>";
            // 
            // numWeakPerMin
            // 
            this.numWeakPerMin.EditValue = new decimal(new int[] {
            70,
            0,
            0,
            0});
            this.numWeakPerMin.Location = new System.Drawing.Point(296, 26);
            this.numWeakPerMin.Name = "numWeakPerMin";
            this.numWeakPerMin.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numWeakPerMin.Properties.MaxValue = new decimal(new int[] {
            9999999,
            0,
            0,
            0});
            this.numWeakPerMin.Properties.MinValue = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numWeakPerMin.Size = new System.Drawing.Size(56, 21);
            this.numWeakPerMin.TabIndex = 5;
            // 
            // labelControl13
            // 
            this.labelControl13.Location = new System.Drawing.Point(50, 29);
            this.labelControl13.Name = "labelControl13";
            this.labelControl13.Size = new System.Drawing.Size(37, 14);
            this.labelControl13.TabIndex = 4;
            this.labelControl13.Text = "RSRP<";
            // 
            // numRsrpMax
            // 
            this.numRsrpMax.EditValue = new decimal(new int[] {
            105,
            0,
            0,
            -2147483648});
            this.numRsrpMax.Location = new System.Drawing.Point(93, 26);
            this.numRsrpMax.Name = "numRsrpMax";
            this.numRsrpMax.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numRsrpMax.Properties.MaxValue = new decimal(new int[] {
            25,
            0,
            0,
            0});
            this.numRsrpMax.Properties.MinValue = new decimal(new int[] {
            141,
            0,
            0,
            -2147483648});
            this.numRsrpMax.Size = new System.Drawing.Size(56, 21);
            this.numRsrpMax.TabIndex = 3;
            // 
            // splitContainerControl1
            // 
            this.splitContainerControl1.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Default;
            this.splitContainerControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl1.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl1.LookAndFeel.SkinName = "Office 2007 Blue";
            this.splitContainerControl1.LookAndFeel.UseDefaultLookAndFeel = false;
            this.splitContainerControl1.Name = "splitContainerControl1";
            this.splitContainerControl1.Panel1.AutoScroll = true;
            this.splitContainerControl1.Panel1.Controls.Add(this.groupControl4);
            this.splitContainerControl1.Panel1.Text = "Panel1";
            this.splitContainerControl1.Panel2.AutoScroll = true;
            this.splitContainerControl1.Panel2.Controls.Add(this.groupControl5);
            this.splitContainerControl1.Panel2.Controls.Add(this.groupControl6);
            this.splitContainerControl1.Panel2.Controls.Add(this.groupControl3);
            this.splitContainerControl1.Panel2.Controls.Add(this.groupControl1);
            this.splitContainerControl1.Panel2.Controls.Add(this.groupControl2);
            this.splitContainerControl1.Panel2.Text = "Panel2";
            this.splitContainerControl1.Size = new System.Drawing.Size(684, 642);
            this.splitContainerControl1.SplitterPosition = 198;
            this.splitContainerControl1.TabIndex = 9;
            this.splitContainerControl1.Text = "splitContainerControl1";
            // 
            // groupControl4
            // 
            this.groupControl4.Controls.Add(this.btnNewReport);
            this.groupControl4.Controls.Add(this.btnRemoveReport);
            this.groupControl4.Controls.Add(this.listAllReport);
            this.groupControl4.Controls.Add(this.btnSave);
            this.groupControl4.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControl4.Location = new System.Drawing.Point(0, 0);
            this.groupControl4.Name = "groupControl4";
            this.groupControl4.Size = new System.Drawing.Size(198, 638);
            this.groupControl4.TabIndex = 7;
            this.groupControl4.Text = "已有设置";
            // 
            // btnNewReport
            // 
            this.btnNewReport.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.btnNewReport.Location = new System.Drawing.Point(68, 606);
            this.btnNewReport.Name = "btnNewReport";
            this.btnNewReport.Size = new System.Drawing.Size(55, 27);
            this.btnNewReport.TabIndex = 4;
            this.btnNewReport.Text = "新建";
            this.btnNewReport.Click += new System.EventHandler(this.btnNewReport_Click);
            // 
            // btnRemoveReport
            // 
            this.btnRemoveReport.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.btnRemoveReport.Enabled = false;
            this.btnRemoveReport.Location = new System.Drawing.Point(2, 606);
            this.btnRemoveReport.Name = "btnRemoveReport";
            this.btnRemoveReport.Size = new System.Drawing.Size(54, 27);
            this.btnRemoveReport.TabIndex = 3;
            this.btnRemoveReport.Text = "删除";
            this.btnRemoveReport.Click += new System.EventHandler(this.btnRemoveReport_Click);
            // 
            // listAllReport
            // 
            this.listAllReport.Dock = System.Windows.Forms.DockStyle.Top;
            this.listAllReport.DrawMode = System.Windows.Forms.DrawMode.OwnerDrawFixed;
            this.listAllReport.FormattingEnabled = true;
            this.listAllReport.ItemHeight = 12;
            this.listAllReport.Location = new System.Drawing.Point(2, 23);
            this.listAllReport.Name = "listAllReport";
            this.listAllReport.Size = new System.Drawing.Size(194, 508);
            this.listAllReport.Sorted = true;
            this.listAllReport.TabIndex = 2;
            this.listAllReport.DrawItem += new System.Windows.Forms.DrawItemEventHandler(this.listAllReport_DrawItem);
            this.listAllReport.DoubleClick += new System.EventHandler(this.listAllReport_DoubleClick);
            // 
            // btnSave
            // 
            this.btnSave.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.btnSave.Location = new System.Drawing.Point(129, 606);
            this.btnSave.Name = "btnSave";
            this.btnSave.Size = new System.Drawing.Size(64, 27);
            this.btnSave.TabIndex = 1;
            this.btnSave.Text = "保存";
            this.btnSave.Click += new System.EventHandler(this.btnSave_Click);
            // 
            // groupControl5
            // 
            this.groupControl5.Controls.Add(this.cbxSaveType);
            this.groupControl5.Controls.Add(this.chkCanUse);
            this.groupControl5.Controls.Add(this.labelControl14);
            this.groupControl5.Controls.Add(this.txtFilePath);
            this.groupControl5.Controls.Add(this.txtFileName);
            this.groupControl5.Controls.Add(this.labelControl12);
            this.groupControl5.Controls.Add(this.txtFuncId);
            this.groupControl5.Controls.Add(this.labelControl11);
            this.groupControl5.Controls.Add(this.btnSelectFilePath);
            this.groupControl5.Controls.Add(this.labelControl3);
            this.groupControl5.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControl5.Location = new System.Drawing.Point(0, 471);
            this.groupControl5.Name = "groupControl5";
            this.groupControl5.Size = new System.Drawing.Size(478, 167);
            this.groupControl5.TabIndex = 11;
            this.groupControl5.Text = "其他";
            // 
            // cbxSaveType
            // 
            this.cbxSaveType.AllowDrop = true;
            this.cbxSaveType.EditValue = "";
            this.cbxSaveType.Location = new System.Drawing.Point(358, 134);
            this.cbxSaveType.Name = "cbxSaveType";
            this.cbxSaveType.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cbxSaveType.Properties.Items.AddRange(new object[] {
            ".txt",
            ".csv"});
            this.cbxSaveType.Size = new System.Drawing.Size(52, 21);
            this.cbxSaveType.TabIndex = 17;
            // 
            // chkCanUse
            // 
            this.chkCanUse.EditValue = true;
            this.chkCanUse.Location = new System.Drawing.Point(150, 34);
            this.chkCanUse.Name = "chkCanUse";
            this.chkCanUse.Properties.Caption = "启用";
            this.chkCanUse.Size = new System.Drawing.Size(46, 19);
            this.chkCanUse.TabIndex = 16;
            // 
            // labelControl14
            // 
            this.labelControl14.Location = new System.Drawing.Point(32, 36);
            this.labelControl14.Name = "labelControl14";
            this.labelControl14.Size = new System.Drawing.Size(100, 14);
            this.labelControl14.TabIndex = 15;
            this.labelControl14.Text = "自动统计是否启用:";
            // 
            // txtFilePath
            // 
            this.txtFilePath.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.txtFilePath.Location = new System.Drawing.Point(102, 135);
            this.txtFilePath.Name = "txtFilePath";
            this.txtFilePath.ReadOnly = true;
            this.txtFilePath.Size = new System.Drawing.Size(248, 21);
            this.txtFilePath.TabIndex = 14;
            // 
            // txtFileName
            // 
            this.txtFileName.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.txtFileName.Location = new System.Drawing.Point(165, 99);
            this.txtFileName.Name = "txtFileName";
            this.txtFileName.Size = new System.Drawing.Size(303, 21);
            this.txtFileName.TabIndex = 13;
            // 
            // labelControl12
            // 
            this.labelControl12.Location = new System.Drawing.Point(33, 102);
            this.labelControl12.Name = "labelControl12";
            this.labelControl12.Size = new System.Drawing.Size(116, 14);
            this.labelControl12.TabIndex = 12;
            this.labelControl12.Text = "保存文件名称 : 年月+";
            // 
            // txtFuncId
            // 
            this.txtFuncId.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.txtFuncId.Location = new System.Drawing.Point(165, 66);
            this.txtFuncId.Name = "txtFuncId";
            this.txtFuncId.Size = new System.Drawing.Size(303, 21);
            this.txtFuncId.TabIndex = 11;
            // 
            // labelControl11
            // 
            this.labelControl11.Location = new System.Drawing.Point(33, 69);
            this.labelControl11.Name = "labelControl11";
            this.labelControl11.Size = new System.Drawing.Size(123, 14);
            this.labelControl11.TabIndex = 10;
            this.labelControl11.Text = "本设置ID(iSubFuncID):";
            // 
            // btnSelectFilePath
            // 
            this.btnSelectFilePath.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnSelectFilePath.Location = new System.Drawing.Point(416, 129);
            this.btnSelectFilePath.Name = "btnSelectFilePath";
            this.btnSelectFilePath.Size = new System.Drawing.Size(52, 27);
            this.btnSelectFilePath.TabIndex = 5;
            this.btnSelectFilePath.Text = "选择";
            this.btnSelectFilePath.Click += new System.EventHandler(this.btnSelectFilePath_Click);
            // 
            // labelControl3
            // 
            this.labelControl3.Location = new System.Drawing.Point(33, 137);
            this.labelControl3.Name = "labelControl3";
            this.labelControl3.Size = new System.Drawing.Size(56, 14);
            this.labelControl3.TabIndex = 2;
            this.labelControl3.Text = "保存路径 :";
            // 
            // groupControl6
            // 
            this.groupControl6.Controls.Add(this.checkAllCity);
            this.groupControl6.Controls.Add(this.label1);
            this.groupControl6.Controls.Add(this.listViewCity);
            this.groupControl6.Controls.Add(this.labelControl15);
            this.groupControl6.Controls.Add(this.lbSvCount);
            this.groupControl6.Controls.Add(this.btnPopupService);
            this.groupControl6.Controls.Add(this.listViewService);
            this.groupControl6.Controls.Add(this.label12);
            this.groupControl6.Controls.Add(this.labelControl5);
            this.groupControl6.Controls.Add(this.labelControl1);
            this.groupControl6.Controls.Add(this.numRecentMonths);
            this.groupControl6.Controls.Add(this.txtProjects);
            this.groupControl6.Controls.Add(this.labelControl8);
            this.groupControl6.Dock = System.Windows.Forms.DockStyle.Top;
            this.groupControl6.Location = new System.Drawing.Point(0, 183);
            this.groupControl6.Name = "groupControl6";
            this.groupControl6.Size = new System.Drawing.Size(478, 288);
            this.groupControl6.TabIndex = 10;
            this.groupControl6.Text = "统计文件检索";
            // 
            // checkAllCity
            // 
            this.checkAllCity.AutoSize = true;
            this.checkAllCity.Location = new System.Drawing.Point(39, 132);
            this.checkAllCity.Name = "checkAllCity";
            this.checkAllCity.Padding = new System.Windows.Forms.Padding(2, 0, 0, 0);
            this.checkAllCity.Size = new System.Drawing.Size(50, 16);
            this.checkAllCity.TabIndex = 1;
            this.checkAllCity.Text = "全选";
            this.checkAllCity.UseVisualStyleBackColor = true;
            this.checkAllCity.CheckedChanged += new System.EventHandler(this.checkAllCity_CheckedChanged);
            // 
            // label1
            // 
            this.label1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(31, 108);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(65, 12);
            this.label1.TabIndex = 37;
            this.label1.Text = "统计地市：";
            // 
            // listViewCity
            // 
            this.listViewCity.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.listViewCity.CheckBoxes = true;
            this.listViewCity.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            columnHeader1});
            this.listViewCity.HeaderStyle = System.Windows.Forms.ColumnHeaderStyle.None;
            this.listViewCity.Location = new System.Drawing.Point(102, 108);
            this.listViewCity.Name = "listViewCity";
            this.listViewCity.Size = new System.Drawing.Size(366, 70);
            this.listViewCity.TabIndex = 0;
            this.listViewCity.UseCompatibleStateImageBehavior = false;
            this.listViewCity.View = System.Windows.Forms.View.Details;
            // 
            // labelControl15
            // 
            this.labelControl15.Location = new System.Drawing.Point(191, 35);
            this.labelControl15.Name = "labelControl15";
            this.labelControl15.Size = new System.Drawing.Size(24, 14);
            this.labelControl15.TabIndex = 24;
            this.labelControl15.Text = "个月";
            // 
            // lbSvCount
            // 
            this.lbSvCount.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.lbSvCount.AutoSize = true;
            this.lbSvCount.Location = new System.Drawing.Point(60, 214);
            this.lbSvCount.Name = "lbSvCount";
            this.lbSvCount.Size = new System.Drawing.Size(23, 12);
            this.lbSvCount.TabIndex = 23;
            this.lbSvCount.Text = "[0]";
            // 
            // btnPopupService
            // 
            this.btnPopupService.Location = new System.Drawing.Point(32, 238);
            this.btnPopupService.Name = "btnPopupService";
            this.btnPopupService.Size = new System.Drawing.Size(57, 27);
            this.btnPopupService.TabIndex = 22;
            this.btnPopupService.Text = "选择";
            this.btnPopupService.Click += new System.EventHandler(this.btnPopupService_Click);
            // 
            // listViewService
            // 
            this.listViewService.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.listViewService.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            columnHeader4});
            this.listViewService.HeaderStyle = System.Windows.Forms.ColumnHeaderStyle.None;
            this.listViewService.Location = new System.Drawing.Point(102, 196);
            this.listViewService.Name = "listViewService";
            this.listViewService.Size = new System.Drawing.Size(366, 76);
            this.listViewService.TabIndex = 21;
            this.listViewService.UseCompatibleStateImageBehavior = false;
            this.listViewService.View = System.Windows.Forms.View.List;
            // 
            // label12
            // 
            this.label12.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.label12.AutoSize = true;
            this.label12.Location = new System.Drawing.Point(31, 196);
            this.label12.Name = "label12";
            this.label12.Size = new System.Drawing.Size(65, 12);
            this.label12.TabIndex = 20;
            this.label12.Text = "业务类型：";
            // 
            // labelControl5
            // 
            this.labelControl5.Location = new System.Drawing.Point(33, 35);
            this.labelControl5.Name = "labelControl5";
            this.labelControl5.Size = new System.Drawing.Size(84, 14);
            this.labelControl5.TabIndex = 12;
            this.labelControl5.Text = "统计月份 : 最近";
            // 
            // labelControl1
            // 
            this.labelControl1.Location = new System.Drawing.Point(165, 35);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(0, 14);
            this.labelControl1.TabIndex = 11;
            // 
            // numRecentMonths
            // 
            this.numRecentMonths.EditValue = new decimal(new int[] {
            2,
            0,
            0,
            0});
            this.numRecentMonths.Location = new System.Drawing.Point(123, 32);
            this.numRecentMonths.Name = "numRecentMonths";
            this.numRecentMonths.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numRecentMonths.Properties.MaxValue = new decimal(new int[] {
            9999999,
            0,
            0,
            0});
            this.numRecentMonths.Properties.MinValue = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numRecentMonths.Size = new System.Drawing.Size(58, 21);
            this.numRecentMonths.TabIndex = 10;
            // 
            // txtProjects
            // 
            this.txtProjects.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.txtProjects.Location = new System.Drawing.Point(104, 70);
            this.txtProjects.Name = "txtProjects";
            this.txtProjects.Size = new System.Drawing.Size(364, 21);
            this.txtProjects.TabIndex = 9;
            // 
            // labelControl8
            // 
            this.labelControl8.Location = new System.Drawing.Point(33, 73);
            this.labelControl8.Name = "labelControl8";
            this.labelControl8.Size = new System.Drawing.Size(56, 14);
            this.labelControl8.TabIndex = 2;
            this.labelControl8.Text = "文件类型 :";
            // 
            // groupControl3
            // 
            this.groupControl3.Controls.Add(this.numTpCellAngleMax);
            this.groupControl3.Controls.Add(this.label10);
            this.groupControl3.Controls.Add(this.label9);
            this.groupControl3.Controls.Add(this.numTpCellDisMax);
            this.groupControl3.Controls.Add(this.label13);
            this.groupControl3.Controls.Add(this.label14);
            this.groupControl3.Dock = System.Windows.Forms.DockStyle.Top;
            this.groupControl3.Location = new System.Drawing.Point(0, 116);
            this.groupControl3.Name = "groupControl3";
            this.groupControl3.Size = new System.Drawing.Size(478, 67);
            this.groupControl3.TabIndex = 8;
            this.groupControl3.Text = "优化问题判断条件";
            // 
            // numTpCellAngleMax
            // 
            this.numTpCellAngleMax.EditValue = new decimal(new int[] {
            60,
            0,
            0,
            0});
            this.numTpCellAngleMax.Location = new System.Drawing.Point(376, 31);
            this.numTpCellAngleMax.Name = "numTpCellAngleMax";
            this.numTpCellAngleMax.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numTpCellAngleMax.Properties.IsFloatValue = false;
            this.numTpCellAngleMax.Properties.Mask.EditMask = "N00";
            this.numTpCellAngleMax.Properties.MaxValue = new decimal(new int[] {
            360,
            0,
            0,
            0});
            this.numTpCellAngleMax.Properties.MinValue = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numTpCellAngleMax.Size = new System.Drawing.Size(56, 21);
            this.numTpCellAngleMax.TabIndex = 30;
            // 
            // label10
            // 
            this.label10.AutoSize = true;
            this.label10.Location = new System.Drawing.Point(443, 36);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(17, 12);
            this.label10.TabIndex = 29;
            this.label10.Text = "度";
            // 
            // label9
            // 
            this.label9.AutoSize = true;
            this.label9.Location = new System.Drawing.Point(251, 34);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(119, 12);
            this.label9.TabIndex = 28;
            this.label9.Text = "采样点与小区夹角 ≤";
            // 
            // numTpCellDisMax
            // 
            this.numTpCellDisMax.EditValue = new decimal(new int[] {
            300,
            0,
            0,
            0});
            this.numTpCellDisMax.Location = new System.Drawing.Point(141, 29);
            this.numTpCellDisMax.Name = "numTpCellDisMax";
            this.numTpCellDisMax.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numTpCellDisMax.Properties.IsFloatValue = false;
            this.numTpCellDisMax.Properties.Mask.EditMask = "N00";
            this.numTpCellDisMax.Properties.MaxValue = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numTpCellDisMax.Properties.MinValue = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numTpCellDisMax.Size = new System.Drawing.Size(62, 21);
            this.numTpCellDisMax.TabIndex = 27;
            // 
            // label13
            // 
            this.label13.AutoSize = true;
            this.label13.Location = new System.Drawing.Point(212, 34);
            this.label13.Name = "label13";
            this.label13.Size = new System.Drawing.Size(17, 12);
            this.label13.TabIndex = 25;
            this.label13.Text = "米";
            // 
            // label14
            // 
            this.label14.AutoSize = true;
            this.label14.Location = new System.Drawing.Point(16, 36);
            this.label14.Name = "label14";
            this.label14.Size = new System.Drawing.Size(119, 12);
            this.label14.TabIndex = 26;
            this.label14.Text = "采样点与小区距离 ≤";
            // 
            // toolStripDropDownService
            // 
            this.toolStripDropDownService.LayoutStyle = System.Windows.Forms.ToolStripLayoutStyle.Flow;
            this.toolStripDropDownService.Name = "toolStripDropDown1";
            this.toolStripDropDownService.Size = new System.Drawing.Size(2, 4);
            // 
            // WeakRoadAutoSettingDlg
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(96F, 96F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Dpi;
            this.AutoScroll = true;
            this.ClientSize = new System.Drawing.Size(684, 642);
            this.Controls.Add(this.splitContainerControl1);
            this.Name = "WeakRoadAutoSettingDlg";
            this.Text = "LTE弱覆盖路段自动统计设置";
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            this.groupControl1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numLastDisMax.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numLastDisMin.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).EndInit();
            this.groupControl2.ResumeLayout(false);
            this.groupControl2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numWeakPerMin.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRsrpMax.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).EndInit();
            this.splitContainerControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl4)).EndInit();
            this.groupControl4.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl5)).EndInit();
            this.groupControl5.ResumeLayout(false);
            this.groupControl5.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cbxSaveType.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkCanUse.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtFileName.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtFuncId.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl6)).EndInit();
            this.groupControl6.ResumeLayout(false);
            this.groupControl6.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numRecentMonths.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtProjects.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl3)).EndInit();
            this.groupControl3.ResumeLayout(false);
            this.groupControl3.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numTpCellAngleMax.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numTpCellDisMax.Properties)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.GroupControl groupControl1;
        private DevExpress.XtraEditors.LabelControl labelControl7;
        private DevExpress.XtraEditors.SpinEdit numLastDisMin;
        private DevExpress.XtraEditors.LabelControl labelControl6;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl1;
        private DevExpress.XtraEditors.LabelControl labelControl13;
        private DevExpress.XtraEditors.SpinEdit numRsrpMax;
        private DevExpress.XtraEditors.GroupControl groupControl4;
        private System.Windows.Forms.ListBox listAllReport;
        private DevExpress.XtraEditors.GroupControl groupControl2;
        private DevExpress.XtraEditors.SimpleButton btnSave;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.SpinEdit numLastDisMax;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private DevExpress.XtraEditors.SpinEdit numWeakPerMin;
        private DevExpress.XtraEditors.LabelControl labelControl9;
        private DevExpress.XtraEditors.LabelControl labelControl10;
        private DevExpress.XtraEditors.SimpleButton btnRemoveReport;
        private DevExpress.XtraEditors.SimpleButton btnNewReport;
        private System.Windows.Forms.FolderBrowserDialog folderBrowserDialog1;
        private System.Windows.Forms.ToolStripDropDown toolStripDropDownService;
        private DevExpress.XtraEditors.GroupControl groupControl5;
        private DevExpress.XtraEditors.ComboBoxEdit cbxSaveType;
        private DevExpress.XtraEditors.CheckEdit chkCanUse;
        private DevExpress.XtraEditors.LabelControl labelControl14;
        private System.Windows.Forms.TextBox txtFilePath;
        private DevExpress.XtraEditors.TextEdit txtFileName;
        private DevExpress.XtraEditors.LabelControl labelControl12;
        private DevExpress.XtraEditors.TextEdit txtFuncId;
        private DevExpress.XtraEditors.LabelControl labelControl11;
        private DevExpress.XtraEditors.SimpleButton btnSelectFilePath;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.GroupControl groupControl6;
        private System.Windows.Forms.CheckBox checkAllCity;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.ListView listViewCity;
        private DevExpress.XtraEditors.LabelControl labelControl15;
        private System.Windows.Forms.Label lbSvCount;
        private DevExpress.XtraEditors.SimpleButton btnPopupService;
        private System.Windows.Forms.ListView listViewService;
        private System.Windows.Forms.Label label12;
        private DevExpress.XtraEditors.LabelControl labelControl5;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.SpinEdit numRecentMonths;
        private DevExpress.XtraEditors.TextEdit txtProjects;
        private DevExpress.XtraEditors.LabelControl labelControl8;
        private DevExpress.XtraEditors.GroupControl groupControl3;
        private DevExpress.XtraEditors.SpinEdit numTpCellDisMax;
        private System.Windows.Forms.Label label13;
        private System.Windows.Forms.Label label14;
        private System.Windows.Forms.Label label10;
        private System.Windows.Forms.Label label9;
        private DevExpress.XtraEditors.SpinEdit numTpCellAngleMax;
    }
}