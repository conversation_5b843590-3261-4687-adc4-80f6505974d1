﻿using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class LteHandOverFreqBandAnaForm : MinCloseForm
    {
        public LteHandOverFreqBandAnaForm()
        {
            InitializeComponent();
        }

        List<LteHandOverFreqBandAnaResult> resList;
        public void FillData(List<LteHandOverFreqBandAnaResult> resList)
        {
            this.resList = resList;
            gridControl.DataSource = resList;
            gridControl.RefreshDataSource();

            //MainModel.ClearDTData();
            //foreach (LteHandOverFreqBandAnaResult freqBand in resList)
            //{
            //    foreach (var item in freqBand.SameBandHandOver)
            //    {
            //        MainModel.DTDataManager.Add(item.Evt);

            //        foreach (TestPoint tp in item.TpList)
            //        {
            //            MainModel.DTDataManager.Add(tp);
            //        }
            //    }
            //}
            //MainModel.FireSetDefaultMapSerialTheme("LTE_TDD:RSRP");
            //MainModel.FireDTDataChanged(this);
        }

        private void miExportXls_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(gridView);
        }

        private void miExportXlsDetail_Click(object sender, EventArgs e)
        {
            List<NPOIRow> rows = new List<NPOIRow>();
            NPOIRow row = new NPOIRow();
            rows.Add(row);
            row.AddCellValue("频段名");
            row.AddCellValue("同频切换次数");
            row.AddCellValue("异频切换次数");
            row.AddCellValue("同频切换前平均电平");

            row.AddCellValue("切换类型");
            row.AddCellValue("切换时间");
            row.AddCellValue("切换前平均电平");
            row.AddCellValue("切换前频点");
            row.AddCellValue("切换前频段");
            row.AddCellValue("切换后频点");
            row.AddCellValue("切换后频段");

            foreach (LteHandOverFreqBandAnaResult freqBand in resList)
            {
                row = new NPOIRow();
                rows.Add(row);
                row.AddCellValue(freqBand.FreqBandName);
                row.AddCellValue(freqBand.SameBandHandOverNum);
                row.AddCellValue(freqBand.DiffBandHandOverNum);
                row.AddCellValue(freqBand.SameBandSrcRsrpInfo.Avg);
                foreach (LteHandOverFreqBandAnaResult.BandHandOverInfo freq in freqBand.FreqBandHandOverList)
                {
                    NPOIRow subRow = new NPOIRow();
                    row.AddSubRow(subRow);
                    subRow.AddCellValue(freq.HandOverType);
                    subRow.AddCellValue(freq.HandOverTime);
                    subRow.AddCellValue(freq.SrcHandOverInfo.RsrpInfo.Avg);
                    subRow.AddCellValue(freq.SrcHandOverInfo.Earfcn);
                    subRow.AddCellValue(freq.SrcHandOverInfo.FreqBand);
                    subRow.AddCellValue(freq.TarHandOverInfo.Earfcn);
                    subRow.AddCellValue(freq.TarHandOverInfo.FreqBand);
                }
            }
            ExcelNPOIManager.ExportToExcel(rows);
        }
    }
}
