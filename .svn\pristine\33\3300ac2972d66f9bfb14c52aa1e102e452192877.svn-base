using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.Frame
{
    public partial class BadRxQualAnaConditionDlg : Form
    {
        public BadRxQualAnaConditionDlg()
        {
            InitializeComponent();
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        public int RxQualThreshold
        {
            get { return int.Parse(numRxQualThreshold.Value.ToString()); }
        }

        public int SecondPerUnit
        {
            get { return int.Parse(numSecondPerUnit.Value.ToString()); }
        }

        public int MinBadQualSencond
        {
            get { return int.Parse(numMinBadQualSencond.Value.ToString()); }
        }
    }
}