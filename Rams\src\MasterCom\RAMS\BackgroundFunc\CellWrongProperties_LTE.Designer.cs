﻿namespace MasterCom.RAMS.Func.SystemSetting
{
    partial class CellWrongProperties_LTE
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.spinEditRSCP = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.spinEditPer = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl6 = new DevExpress.XtraEditors.LabelControl();
            this.spinEditAngle = new DevExpress.XtraEditors.SpinEdit();
            this.spinEditDis = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl7 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl5 = new DevExpress.XtraEditors.LabelControl();
            this.groupControl = new DevExpress.XtraEditors.GroupControl();
            this.chkBackgroundStat = new DevExpress.XtraEditors.CheckEdit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditRSCP.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditPer.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditAngle.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditDis.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl)).BeginInit();
            this.groupControl.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chkBackgroundStat.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // groupControl1
            // 
            this.groupControl1.Controls.Add(this.spinEditRSCP);
            this.groupControl1.Controls.Add(this.labelControl1);
            this.groupControl1.Controls.Add(this.spinEditPer);
            this.groupControl1.Controls.Add(this.labelControl4);
            this.groupControl1.Controls.Add(this.labelControl6);
            this.groupControl1.Controls.Add(this.spinEditAngle);
            this.groupControl1.Controls.Add(this.spinEditDis);
            this.groupControl1.Controls.Add(this.labelControl2);
            this.groupControl1.Controls.Add(this.labelControl3);
            this.groupControl1.Controls.Add(this.labelControl7);
            this.groupControl1.Controls.Add(this.labelControl5);
            this.groupControl1.Location = new System.Drawing.Point(3, 62);
            this.groupControl1.Name = "groupControl1";
            this.groupControl1.Size = new System.Drawing.Size(448, 138);
            this.groupControl1.TabIndex = 5;
            this.groupControl1.Text = "条件设置";
            // 
            // spinEditRSCP
            // 
            this.spinEditRSCP.EditValue = new decimal(new int[] {
            80,
            0,
            0,
            -2147483648});
            this.spinEditRSCP.Location = new System.Drawing.Point(208, 80);
            this.spinEditRSCP.Name = "spinEditRSCP";
            this.spinEditRSCP.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditRSCP.Properties.Increment = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.spinEditRSCP.Properties.IsFloatValue = false;
            this.spinEditRSCP.Properties.Mask.EditMask = "N00";
            this.spinEditRSCP.Properties.MaxValue = new decimal(new int[] {
            10,
            0,
            0,
            -2147483648});
            this.spinEditRSCP.Properties.MinValue = new decimal(new int[] {
            120,
            0,
            0,
            -2147483648});
            this.spinEditRSCP.Size = new System.Drawing.Size(69, 21);
            this.spinEditRSCP.TabIndex = 10;
            // 
            // labelControl1
            // 
            this.labelControl1.Location = new System.Drawing.Point(130, 83);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(69, 14);
            this.labelControl1.TabIndex = 11;
            this.labelControl1.Text = "采样点场强≥";
            // 
            // spinEditPer
            // 
            this.spinEditPer.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.spinEditPer.Location = new System.Drawing.Point(208, 107);
            this.spinEditPer.Name = "spinEditPer";
            this.spinEditPer.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditPer.Properties.Increment = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.spinEditPer.Properties.IsFloatValue = false;
            this.spinEditPer.Properties.Mask.EditMask = "N00";
            this.spinEditPer.Properties.MaxValue = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.spinEditPer.Size = new System.Drawing.Size(69, 21);
            this.spinEditPer.TabIndex = 12;
            // 
            // labelControl4
            // 
            this.labelControl4.Location = new System.Drawing.Point(133, 110);
            this.labelControl4.Name = "labelControl4";
            this.labelControl4.Size = new System.Drawing.Size(69, 14);
            this.labelControl4.TabIndex = 9;
            this.labelControl4.Text = "不符百分比≥";
            // 
            // labelControl6
            // 
            this.labelControl6.Location = new System.Drawing.Point(283, 110);
            this.labelControl6.Name = "labelControl6";
            this.labelControl6.Size = new System.Drawing.Size(12, 14);
            this.labelControl6.TabIndex = 8;
            this.labelControl6.Text = "%";
            // 
            // spinEditAngle
            // 
            this.spinEditAngle.EditValue = new decimal(new int[] {
            50,
            0,
            0,
            0});
            this.spinEditAngle.Location = new System.Drawing.Point(208, 53);
            this.spinEditAngle.Name = "spinEditAngle";
            this.spinEditAngle.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditAngle.Properties.IsFloatValue = false;
            this.spinEditAngle.Properties.Mask.EditMask = "N00";
            this.spinEditAngle.Properties.MaxValue = new decimal(new int[] {
            180,
            0,
            0,
            0});
            this.spinEditAngle.Size = new System.Drawing.Size(69, 21);
            this.spinEditAngle.TabIndex = 7;
            // 
            // spinEditDis
            // 
            this.spinEditDis.EditValue = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.spinEditDis.Location = new System.Drawing.Point(208, 26);
            this.spinEditDis.Name = "spinEditDis";
            this.spinEditDis.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditDis.Properties.Increment = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.spinEditDis.Properties.IsFloatValue = false;
            this.spinEditDis.Properties.Mask.EditMask = "N00";
            this.spinEditDis.Size = new System.Drawing.Size(69, 21);
            this.spinEditDis.TabIndex = 6;
            // 
            // labelControl2
            // 
            this.labelControl2.Location = new System.Drawing.Point(71, 29);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(129, 14);
            this.labelControl2.TabIndex = 5;
            this.labelControl2.Text = "采样点与主服小区距离≥";
            // 
            // labelControl3
            // 
            this.labelControl3.Location = new System.Drawing.Point(23, 56);
            this.labelControl3.Name = "labelControl3";
            this.labelControl3.Size = new System.Drawing.Size(177, 14);
            this.labelControl3.TabIndex = 2;
            this.labelControl3.Text = "采样点与主服小区发射方向夹角≥";
            // 
            // labelControl7
            // 
            this.labelControl7.Location = new System.Drawing.Point(283, 56);
            this.labelControl7.Name = "labelControl7";
            this.labelControl7.Size = new System.Drawing.Size(12, 14);
            this.labelControl7.TabIndex = 3;
            this.labelControl7.Text = "度";
            // 
            // labelControl5
            // 
            this.labelControl5.Location = new System.Drawing.Point(283, 29);
            this.labelControl5.Name = "labelControl5";
            this.labelControl5.Size = new System.Drawing.Size(12, 14);
            this.labelControl5.TabIndex = 4;
            this.labelControl5.Text = "米";
            // 
            // groupControl
            // 
            this.groupControl.Controls.Add(this.chkBackgroundStat);
            this.groupControl.Location = new System.Drawing.Point(3, 3);
            this.groupControl.Name = "groupControl";
            this.groupControl.Size = new System.Drawing.Size(448, 53);
            this.groupControl.TabIndex = 4;
            this.groupControl.Text = "开关设置";
            // 
            // chkBackgroundStat
            // 
            this.chkBackgroundStat.Location = new System.Drawing.Point(15, 26);
            this.chkBackgroundStat.Name = "chkBackgroundStat";
            this.chkBackgroundStat.Properties.Caption = "启用";
            this.chkBackgroundStat.Size = new System.Drawing.Size(75, 19);
            this.chkBackgroundStat.TabIndex = 0;
            // 
            // CellWrongProperties_LTE
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.groupControl1);
            this.Controls.Add(this.groupControl);
            this.Name = "CellWrongProperties_LTE";
            this.Size = new System.Drawing.Size(454, 280);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            this.groupControl1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditRSCP.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditPer.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditAngle.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditDis.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl)).EndInit();
            this.groupControl.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.chkBackgroundStat.Properties)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.GroupControl groupControl1;
        private DevExpress.XtraEditors.SpinEdit spinEditRSCP;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.SpinEdit spinEditPer;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private DevExpress.XtraEditors.LabelControl labelControl6;
        private DevExpress.XtraEditors.SpinEdit spinEditAngle;
        private DevExpress.XtraEditors.SpinEdit spinEditDis;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.LabelControl labelControl7;
        private DevExpress.XtraEditors.LabelControl labelControl5;
        private DevExpress.XtraEditors.GroupControl groupControl;
        private DevExpress.XtraEditors.CheckEdit chkBackgroundStat;

    }
}
