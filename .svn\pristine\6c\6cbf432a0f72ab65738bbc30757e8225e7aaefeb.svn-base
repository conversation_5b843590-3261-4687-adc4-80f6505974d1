﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    class ZTNBMgrsCoveDetail : ZTLteMgrsCoveDetail
    {
        public ZTNBMgrsCoveDetail(ServiceName serviceName, MainModel mainModel)
            : base(serviceName, mainModel)
        {
        }

        public override string Name
        {
            get { return "NB军事栅格扫频分析"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 33000, 33013, this.Name);
        }
    }
}
