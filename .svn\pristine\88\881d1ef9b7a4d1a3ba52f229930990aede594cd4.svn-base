﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTSiteDistanceResultForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(ZTSiteDistanceResultForm));
            this.treeListView = new BrightIdeasSoftware.TreeListView();
            this.olvColumnRegionName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnArea = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCount = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMeanDistance = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLAC = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCI = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBCCH = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBSIC = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnDistance = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColNearestBtsName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColNearestBtsDistance = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColFarthestBtsName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColFarthestBtsDistance = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.contextMenuStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExpandAll = new System.Windows.Forms.ToolStripMenuItem();
            this.miCollapseAll = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportToExcel = new System.Windows.Forms.ToolStripMenuItem();
            ((System.ComponentModel.ISupportInitialize)(this.treeListView)).BeginInit();
            this.contextMenuStrip.SuspendLayout();
            this.SuspendLayout();
            // 
            // treeListView
            // 
            this.treeListView.AllColumns.Add(this.olvColumnRegionName);
            this.treeListView.AllColumns.Add(this.olvColumnArea);
            this.treeListView.AllColumns.Add(this.olvColumnCount);
            this.treeListView.AllColumns.Add(this.olvColumnMeanDistance);
            this.treeListView.AllColumns.Add(this.olvColumnLAC);
            this.treeListView.AllColumns.Add(this.olvColumnCI);
            this.treeListView.AllColumns.Add(this.olvColumnBCCH);
            this.treeListView.AllColumns.Add(this.olvColumnBSIC);
            this.treeListView.AllColumns.Add(this.olvColumnDistance);
            this.treeListView.AllColumns.Add(this.olvColNearestBtsName);
            this.treeListView.AllColumns.Add(this.olvColNearestBtsDistance);
            this.treeListView.AllColumns.Add(this.olvColFarthestBtsName);
            this.treeListView.AllColumns.Add(this.olvColFarthestBtsDistance);
            this.treeListView.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnRegionName,
            this.olvColumnArea,
            this.olvColumnCount,
            this.olvColumnMeanDistance,
            this.olvColumnLAC,
            this.olvColumnCI,
            this.olvColumnBCCH,
            this.olvColumnBSIC,
            this.olvColumnDistance,
            this.olvColNearestBtsName,
            this.olvColNearestBtsDistance,
            this.olvColFarthestBtsName,
            this.olvColFarthestBtsDistance});
            this.treeListView.ContextMenuStrip = this.contextMenuStrip;
            this.treeListView.Cursor = System.Windows.Forms.Cursors.Default;
            this.treeListView.Dock = System.Windows.Forms.DockStyle.Fill;
            this.treeListView.FullRowSelect = true;
            this.treeListView.GridLines = true;
            this.treeListView.Location = new System.Drawing.Point(0, 0);
            this.treeListView.MultiSelect = false;
            this.treeListView.Name = "treeListView";
            this.treeListView.OwnerDraw = true;
            this.treeListView.ShowGroups = false;
            this.treeListView.Size = new System.Drawing.Size(778, 371);
            this.treeListView.TabIndex = 1;
            this.treeListView.UseCompatibleStateImageBehavior = false;
            this.treeListView.View = System.Windows.Forms.View.Details;
            this.treeListView.VirtualMode = true;
            this.treeListView.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.treeListView_MouseDoubleClick);
            // 
            // olvColumnRegionName
            // 
            this.olvColumnRegionName.HeaderFont = null;
            this.olvColumnRegionName.Text = "区域名称";
            this.olvColumnRegionName.Width = 150;
            // 
            // olvColumnArea
            // 
            this.olvColumnArea.HeaderFont = null;
            this.olvColumnArea.Text = "面积(平方公里)";
            this.olvColumnArea.Width = 120;
            // 
            // olvColumnCount
            // 
            this.olvColumnCount.HeaderFont = null;
            this.olvColumnCount.Text = "数量";
            // 
            // olvColumnMeanDistance
            // 
            this.olvColumnMeanDistance.HeaderFont = null;
            this.olvColumnMeanDistance.Text = "平均站间距(米)";
            this.olvColumnMeanDistance.Width = 100;
            // 
            // olvColumnLAC
            // 
            this.olvColumnLAC.HeaderFont = null;
            this.olvColumnLAC.Text = "LAC";
            this.olvColumnLAC.Width = 80;
            // 
            // olvColumnCI
            // 
            this.olvColumnCI.HeaderFont = null;
            this.olvColumnCI.Text = "CI";
            this.olvColumnCI.Width = 80;
            // 
            // olvColumnBCCH
            // 
            this.olvColumnBCCH.HeaderFont = null;
            this.olvColumnBCCH.Text = "频点";
            this.olvColumnBCCH.Width = 80;
            // 
            // olvColumnBSIC
            // 
            this.olvColumnBSIC.HeaderFont = null;
            this.olvColumnBSIC.Text = "BSIC/扰码";
            this.olvColumnBSIC.Width = 80;
            // 
            // olvColumnDistance
            // 
            this.olvColumnDistance.HeaderFont = null;
            this.olvColumnDistance.Text = "站间距(米)";
            this.olvColumnDistance.Width = 80;
            // 
            // olvColNearestBtsName
            // 
            this.olvColNearestBtsName.HeaderFont = null;
            this.olvColNearestBtsName.Text = "最近基站名称";
            this.olvColNearestBtsName.Width = 120;
            // 
            // olvColNearestBtsDistance
            // 
            this.olvColNearestBtsDistance.HeaderFont = null;
            this.olvColNearestBtsDistance.Text = "最近基站距离";
            this.olvColNearestBtsDistance.Width = 90;
            // 
            // olvColFarthestBtsName
            // 
            this.olvColFarthestBtsName.HeaderFont = null;
            this.olvColFarthestBtsName.Text = "最远基站名称";
            this.olvColFarthestBtsName.Width = 120;
            // 
            // olvColFarthestBtsDistance
            // 
            this.olvColFarthestBtsDistance.HeaderFont = null;
            this.olvColFarthestBtsDistance.Text = "最远基站距离";
            this.olvColFarthestBtsDistance.Width = 90;
            // 
            // contextMenuStrip
            // 
            this.contextMenuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExpandAll,
            this.miCollapseAll,
            this.miExportToExcel});
            this.contextMenuStrip.Name = "contextMenuStrip";
            this.contextMenuStrip.Size = new System.Drawing.Size(130, 70);
            this.contextMenuStrip.Opening += new System.ComponentModel.CancelEventHandler(this.contextMenuStrip_Opening);
            // 
            // miExpandAll
            // 
            this.miExpandAll.Name = "miExpandAll";
            this.miExpandAll.Size = new System.Drawing.Size(129, 22);
            this.miExpandAll.Text = "全部展开";
            this.miExpandAll.Click += new System.EventHandler(this.miExpandAll_Click);
            // 
            // miCollapseAll
            // 
            this.miCollapseAll.Name = "miCollapseAll";
            this.miCollapseAll.Size = new System.Drawing.Size(129, 22);
            this.miCollapseAll.Text = "全部折叠";
            this.miCollapseAll.Click += new System.EventHandler(this.miCollapseAll_Click);
            // 
            // miExportToExcel
            // 
            this.miExportToExcel.Name = "miExportToExcel";
            this.miExportToExcel.Size = new System.Drawing.Size(129, 22);
            this.miExportToExcel.Text = "导出Excel";
            this.miExportToExcel.Click += new System.EventHandler(this.miExportToExcel_Click);
            // 
            // ZTSiteDistanceResultForm
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("ZTSiteDistanceResultForm.Appearance.Image")));
            this.Appearance.Options.UseFont = true;
            this.Appearance.Options.UseImage = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(778, 371);
            this.Controls.Add(this.treeListView);
            this.Name = "ZTSiteDistanceResultForm";
            this.Text = "平均站间距";
            ((System.ComponentModel.ISupportInitialize)(this.treeListView)).EndInit();
            this.contextMenuStrip.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private BrightIdeasSoftware.TreeListView treeListView;
        private BrightIdeasSoftware.OLVColumn olvColumnRegionName;
        private BrightIdeasSoftware.OLVColumn olvColumnLAC;
        private BrightIdeasSoftware.OLVColumn olvColumnCI;
        private BrightIdeasSoftware.OLVColumn olvColumnBCCH;
        private BrightIdeasSoftware.OLVColumn olvColumnDistance;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip;
        private System.Windows.Forms.ToolStripMenuItem miExportToExcel;
        private System.Windows.Forms.ToolStripMenuItem miExpandAll;
        private System.Windows.Forms.ToolStripMenuItem miCollapseAll;
        private BrightIdeasSoftware.OLVColumn olvColumnBSIC;
        private BrightIdeasSoftware.OLVColumn olvColumnCount;
        private BrightIdeasSoftware.OLVColumn olvColumnMeanDistance;
        private BrightIdeasSoftware.OLVColumn olvColumnArea;
        private BrightIdeasSoftware.OLVColumn olvColNearestBtsName;
        private BrightIdeasSoftware.OLVColumn olvColNearestBtsDistance;
        private BrightIdeasSoftware.OLVColumn olvColFarthestBtsName;
        private BrightIdeasSoftware.OLVColumn olvColFarthestBtsDistance;
    }
}