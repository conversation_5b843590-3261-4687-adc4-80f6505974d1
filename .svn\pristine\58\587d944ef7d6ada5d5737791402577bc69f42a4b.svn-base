﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc.ZTCellCheck
{
    public partial class ResultForm : MinCloseForm
    {
        public ResultForm()
            : base(MainModel.GetInstance())
        {
            InitializeComponent();
        }

        public void FillData(List<CellCheckInfo> cellInfos)
        {
            gridCtrl.DataSource = cellInfos;
            gridCtrl.RefreshDataSource();
            gridView.BestFitColumns();
        }

        private void miExport_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(gridView);
        }

        private void gridView_DoubleClick(object sender, EventArgs e)
        {
            DevExpress.Utils.DXMouseEventArgs dxEvt = e as DevExpress.Utils.DXMouseEventArgs;
            if (dxEvt == null)
            {
                return;
            }
            DevExpress.XtraGrid.Views.Grid.ViewInfo.GridHitInfo info = gridView.CalcHitInfo(dxEvt.Location);
            if (info == null || !info.InRow)
            {
                return;
            }
            CellCheckInfo cell = gridView.GetRow(info.RowHandle) as CellCheckInfo;
            if (cell!=null)
            {
                MainModel.SelectedLTECell = cell.Cell as LTECell;
                MainModel.MainForm.GetMapForm().GoToView(cell.Cell.Longitude, cell.Cell.Latitude, 6000);
            }

        }


    }
}
