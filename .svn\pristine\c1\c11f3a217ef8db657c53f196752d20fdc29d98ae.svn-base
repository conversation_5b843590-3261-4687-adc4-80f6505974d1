﻿using MasterCom.RAMS.BackgroundFunc.StationAccept_Shanxi;
using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.BackgroundFunc
{
    public class StationAcceptAna_GX : StationAcceptAna_SX
    {
        private static StationAcceptAna_GX intance = null;
        public static new StationAcceptAna_GX GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new StationAcceptAna_GX();
                    }
                }
            }
            return intance;
        }
        protected StationAcceptAna_GX()
            : base()
        { 
        }
        public override string Name
        {
            get
            {
                return "广西单站验收";
            }
        }
        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 22000, 22118, "查询");
        }
        protected override StationAutoAcceptManager_SX_LTE getNewAcceptManager()
        {
            return new StationAutoAcceptManager_GX();
        }
        protected override BtsAcceptInfo_SX<LTECell, string> getNewBtsAcceptInfo(bool isOutDoor, string btsName, int btsId)
        {
            BtsAcceptInfo_GX btsAcceptInfo = new BtsAcceptInfo_GX(isOutDoor);
            btsAcceptInfo.BtsName = btsName;
            btsAcceptInfo.BtsId = btsId;
            return btsAcceptInfo;
        }
        protected override CellAcceptInfoBase_SX getNewCellAcceptInfo(bool isOutDoor)
        {
            CellAcceptInfoBase_SX cellAcceptInfo;
            if (isOutDoor)
            {
                cellAcceptInfo = new OutDoorCellAcceptInfo_GX();
            }
            else
            {
                cellAcceptInfo = new IndoorCellAcceptInfo_GX();
            }
            return cellAcceptInfo;
        }
    }
}
