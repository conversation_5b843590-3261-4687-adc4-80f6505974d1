﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class NbIotMgrsWeakRsrpResult
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            DevExpress.XtraGrid.GridLevelNode gridLevelNode1 = new DevExpress.XtraGrid.GridLevelNode();
            DevExpress.XtraGrid.GridLevelNode gridLevelNode2 = new DevExpress.XtraGrid.GridLevelNode();
            DevExpress.XtraGrid.GridLevelNode gridLevelNode3 = new DevExpress.XtraGrid.GridLevelNode();
            DevExpress.XtraGrid.GridLevelNode gridLevelNode4 = new DevExpress.XtraGrid.GridLevelNode();
            this.gvArea = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn29 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn30 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridControl1 = new DevExpress.XtraGrid.GridControl();
            this.contextMenuStrip1 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExportSimpleExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportDetailExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportList = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportShp = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator1 = new System.Windows.Forms.ToolStripSeparator();
            this.miExportAllExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportAllShp = new System.Windows.Forms.ToolStripMenuItem();
            this.gv = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gvGrid = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn15 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn19 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn18 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn22 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn7 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn20 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn21 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gvCell = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn8 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn16 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn17 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn24 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn25 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn26 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn27 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn28 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gvSerialGrid = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn9 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn10 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn11 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn12 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn13 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn14 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn6 = new DevExpress.XtraGrid.Columns.GridColumn();
            ((System.ComponentModel.ISupportInitialize)(this.gvArea)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl1)).BeginInit();
            this.contextMenuStrip1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gv)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvGrid)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvCell)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvSerialGrid)).BeginInit();
            this.SuspendLayout();
            // 
            // gvArea
            // 
            this.gvArea.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn29,
            this.gridColumn30});
            this.gvArea.GridControl = this.gridControl1;
            this.gvArea.IndicatorWidth = 40;
            this.gvArea.Name = "gvArea";
            this.gvArea.OptionsBehavior.Editable = false;
            this.gvArea.OptionsDetail.ShowDetailTabs = false;
            this.gvArea.OptionsView.ShowGroupPanel = false;
            this.gvArea.CustomDrawRowIndicator += new DevExpress.XtraGrid.Views.Grid.RowIndicatorCustomDrawEventHandler(this.gvSerialGrid_CustomDrawRowIndicator);
            // 
            // gridColumn29
            // 
            this.gridColumn29.Caption = "区域名称";
            this.gridColumn29.FieldName = "AreaName";
            this.gridColumn29.Name = "gridColumn29";
            this.gridColumn29.Visible = true;
            this.gridColumn29.VisibleIndex = 0;
            // 
            // gridColumn30
            // 
            this.gridColumn30.Caption = "区域连续问题点数";
            this.gridColumn30.FieldName = "IssuesCount";
            this.gridColumn30.Name = "gridColumn30";
            this.gridColumn30.Visible = true;
            this.gridColumn30.VisibleIndex = 1;
            // 
            // gridControl1
            // 
            this.gridControl1.ContextMenuStrip = this.contextMenuStrip1;
            this.gridControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControl1.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gridControl1.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gridControl1.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gridControl1.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gridControl1.EmbeddedNavigator.Buttons.Remove.Visible = false;
            gridLevelNode1.LevelTemplate = this.gvArea;
            gridLevelNode2.LevelTemplate = this.gvSerialGrid;
            gridLevelNode3.LevelTemplate = this.gvGrid;
            gridLevelNode4.LevelTemplate = this.gvCell;
            gridLevelNode4.RelationName = "CellGrid";
            gridLevelNode3.Nodes.AddRange(new DevExpress.XtraGrid.GridLevelNode[] {
            gridLevelNode4});
            gridLevelNode3.RelationName = "GridViews";
            gridLevelNode2.Nodes.AddRange(new DevExpress.XtraGrid.GridLevelNode[] {
            gridLevelNode3});
            gridLevelNode2.RelationName = "SerialGridViews";
            gridLevelNode1.Nodes.AddRange(new DevExpress.XtraGrid.GridLevelNode[] {
            gridLevelNode2});
            gridLevelNode1.RelationName = "AreaGridViews";
            this.gridControl1.LevelTree.Nodes.AddRange(new DevExpress.XtraGrid.GridLevelNode[] {
            gridLevelNode1});
            this.gridControl1.Location = new System.Drawing.Point(0, 0);
            this.gridControl1.MainView = this.gv;
            this.gridControl1.Name = "gridControl1";
            this.gridControl1.ShowOnlyPredefinedDetails = true;
            this.gridControl1.Size = new System.Drawing.Size(854, 513);
            this.gridControl1.TabIndex = 7;
            this.gridControl1.UseEmbeddedNavigator = true;
            this.gridControl1.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gv,
            this.gvGrid,
            this.gvCell,
            this.gvSerialGrid,
            this.gvArea});
            // 
            // contextMenuStrip1
            // 
            this.contextMenuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExportSimpleExcel,
            this.miExportDetailExcel,
            this.miExportList,
            this.miExportShp,
            this.toolStripSeparator1,
            this.miExportAllExcel,
            this.miExportAllShp});
            this.contextMenuStrip1.Name = "contextMenuStrip1";
            this.contextMenuStrip1.Size = new System.Drawing.Size(171, 142);
            // 
            // miExportSimpleExcel
            // 
            this.miExportSimpleExcel.Name = "miExportSimpleExcel";
            this.miExportSimpleExcel.Size = new System.Drawing.Size(170, 22);
            this.miExportSimpleExcel.Text = "导出简单Excel...";
            // 
            // miExportDetailExcel
            // 
            this.miExportDetailExcel.Name = "miExportDetailExcel";
            this.miExportDetailExcel.Size = new System.Drawing.Size(170, 22);
            this.miExportDetailExcel.Text = "导出详细Excel...";
            // 
            // miExportList
            // 
            this.miExportList.Name = "miExportList";
            this.miExportList.Size = new System.Drawing.Size(170, 22);
            this.miExportList.Text = "导出栅格列表...";
            // 
            // miExportShp
            // 
            this.miExportShp.Name = "miExportShp";
            this.miExportShp.Size = new System.Drawing.Size(170, 22);
            this.miExportShp.Text = "导出图层Shp";
            // 
            // toolStripSeparator1
            // 
            this.toolStripSeparator1.Name = "toolStripSeparator1";
            this.toolStripSeparator1.Size = new System.Drawing.Size(167, 6);
            // 
            // miExportAllExcel
            // 
            this.miExportAllExcel.Name = "miExportAllExcel";
            this.miExportAllExcel.Size = new System.Drawing.Size(170, 22);
            this.miExportAllExcel.Text = "导出全部Excel...";
            // 
            // miExportAllShp
            // 
            this.miExportAllShp.Name = "miExportAllShp";
            this.miExportAllShp.Size = new System.Drawing.Size(170, 22);
            this.miExportAllShp.Text = "导出全部图层Shp";
            // 
            // gv
            // 
            this.gv.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn1,
            this.gridColumn3});
            this.gv.GridControl = this.gridControl1;
            this.gv.IndicatorWidth = 40;
            this.gv.Name = "gv";
            this.gv.OptionsBehavior.Editable = false;
            this.gv.OptionsDetail.ShowDetailTabs = false;
            this.gv.OptionsView.ShowGroupPanel = false;
            this.gv.OptionsView.ShowIndicator = false;
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "运营商名称";
            this.gridColumn1.FieldName = "Name";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.Visible = true;
            this.gridColumn1.VisibleIndex = 0;
            // 
            // gridColumn3
            // 
            this.gridColumn3.Caption = "连续问题点数";
            this.gridColumn3.FieldName = "IssuesCount";
            this.gridColumn3.Name = "gridColumn3";
            this.gridColumn3.Visible = true;
            this.gridColumn3.VisibleIndex = 1;
            // 
            // gvGrid
            // 
            this.gvGrid.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn2,
            this.gridColumn15,
            this.gridColumn19,
            this.gridColumn18,
            this.gridColumn22,
            this.gridColumn7,
            this.gridColumn20,
            this.gridColumn21});
            this.gvGrid.GridControl = this.gridControl1;
            this.gvGrid.Name = "gvGrid";
            this.gvGrid.OptionsBehavior.Editable = false;
            this.gvGrid.OptionsDetail.ShowDetailTabs = false;
            this.gvGrid.OptionsView.ShowGroupPanel = false;
            this.gvGrid.OptionsView.ShowIndicator = false;
            // 
            // gridColumn2
            // 
            this.gridColumn2.Caption = "栅格编号";
            this.gridColumn2.FieldName = "MGRTIndex";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.Visible = true;
            this.gridColumn2.VisibleIndex = 0;
            // 
            // gridColumn15
            // 
            this.gridColumn15.Caption = "小区数";
            this.gridColumn15.FieldName = "CellCount";
            this.gridColumn15.Name = "gridColumn15";
            this.gridColumn15.Visible = true;
            this.gridColumn15.VisibleIndex = 1;
            // 
            // gridColumn19
            // 
            this.gridColumn19.Caption = "小区最强电平";
            this.gridColumn19.FieldName = "MaxRsrp";
            this.gridColumn19.Name = "gridColumn19";
            this.gridColumn19.Visible = true;
            this.gridColumn19.VisibleIndex = 2;
            // 
            // gridColumn18
            // 
            this.gridColumn18.Caption = "小区平均场强";
            this.gridColumn18.FieldName = "AvgRsrp";
            this.gridColumn18.Name = "gridColumn18";
            this.gridColumn18.Visible = true;
            this.gridColumn18.VisibleIndex = 3;
            // 
            // gridColumn22
            // 
            this.gridColumn22.Caption = "小区最强SINR";
            this.gridColumn22.FieldName = "MaxSinr";
            this.gridColumn22.Name = "gridColumn22";
            this.gridColumn22.Visible = true;
            this.gridColumn22.VisibleIndex = 4;
            // 
            // gridColumn7
            // 
            this.gridColumn7.Caption = "小区平均SINR";
            this.gridColumn7.FieldName = "AvgSinr";
            this.gridColumn7.Name = "gridColumn7";
            this.gridColumn7.Visible = true;
            this.gridColumn7.VisibleIndex = 5;
            // 
            // gridColumn20
            // 
            this.gridColumn20.Caption = "中心经度";
            this.gridColumn20.FieldName = "CentLng";
            this.gridColumn20.Name = "gridColumn20";
            this.gridColumn20.Visible = true;
            this.gridColumn20.VisibleIndex = 6;
            // 
            // gridColumn21
            // 
            this.gridColumn21.Caption = "中心纬度";
            this.gridColumn21.FieldName = "CentLat";
            this.gridColumn21.Name = "gridColumn21";
            this.gridColumn21.Visible = true;
            this.gridColumn21.VisibleIndex = 7;
            // 
            // gvCell
            // 
            this.gvCell.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn8,
            this.gridColumn16,
            this.gridColumn17,
            this.gridColumn24,
            this.gridColumn25,
            this.gridColumn26,
            this.gridColumn27,
            this.gridColumn28});
            this.gvCell.GridControl = this.gridControl1;
            this.gvCell.Name = "gvCell";
            this.gvCell.OptionsBehavior.Editable = false;
            this.gvCell.OptionsDetail.ShowDetailTabs = false;
            this.gvCell.OptionsView.ShowGroupPanel = false;
            this.gvCell.OptionsView.ShowIndicator = false;
            // 
            // gridColumn8
            // 
            this.gridColumn8.Caption = "小区名";
            this.gridColumn8.FieldName = "CellName";
            this.gridColumn8.Name = "gridColumn8";
            // 
            // gridColumn16
            // 
            this.gridColumn16.Caption = "EARFCN";
            this.gridColumn16.FieldName = "EARFCN";
            this.gridColumn16.Name = "gridColumn16";
            this.gridColumn16.Visible = true;
            this.gridColumn16.VisibleIndex = 0;
            // 
            // gridColumn17
            // 
            this.gridColumn17.Caption = "PCI";
            this.gridColumn17.FieldName = "PCI";
            this.gridColumn17.Name = "gridColumn17";
            this.gridColumn17.Visible = true;
            this.gridColumn17.VisibleIndex = 1;
            // 
            // gridColumn24
            // 
            this.gridColumn24.Caption = "采样点数";
            this.gridColumn24.FieldName = "SampleCount";
            this.gridColumn24.Name = "gridColumn24";
            this.gridColumn24.Visible = true;
            this.gridColumn24.VisibleIndex = 2;
            // 
            // gridColumn25
            // 
            this.gridColumn25.Caption = "采样点平均RSRP";
            this.gridColumn25.DisplayFormat.FormatString = "F2";
            this.gridColumn25.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn25.FieldName = "R0_RP";
            this.gridColumn25.Name = "gridColumn25";
            this.gridColumn25.Visible = true;
            this.gridColumn25.VisibleIndex = 3;
            // 
            // gridColumn26
            // 
            this.gridColumn26.Caption = "采样点平均SINR";
            this.gridColumn26.FieldName = "R0_CINR";
            this.gridColumn26.Name = "gridColumn26";
            this.gridColumn26.Visible = true;
            this.gridColumn26.VisibleIndex = 4;
            // 
            // gridColumn27
            // 
            this.gridColumn27.Caption = "经度";
            this.gridColumn27.FieldName = "Longitude";
            this.gridColumn27.Name = "gridColumn27";
            // 
            // gridColumn28
            // 
            this.gridColumn28.Caption = "纬度";
            this.gridColumn28.FieldName = "Latitude";
            this.gridColumn28.Name = "gridColumn28";
            // 
            // gvSerialGrid
            // 
            this.gvSerialGrid.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn9,
            this.gridColumn10,
            this.gridColumn11,
            this.gridColumn12,
            this.gridColumn13,
            this.gridColumn14,
            this.gridColumn4,
            this.gridColumn5,
            this.gridColumn6});
            this.gvSerialGrid.GridControl = this.gridControl1;
            this.gvSerialGrid.IndicatorWidth = 40;
            this.gvSerialGrid.Name = "gvSerialGrid";
            this.gvSerialGrid.OptionsBehavior.Editable = false;
            this.gvSerialGrid.OptionsDetail.ShowDetailTabs = false;
            this.gvSerialGrid.OptionsView.ShowGroupPanel = false;
            this.gvSerialGrid.CustomDrawRowIndicator += new DevExpress.XtraGrid.Views.Grid.RowIndicatorCustomDrawEventHandler(this.gvSerialGrid_CustomDrawRowIndicator);
            // 
            // gridColumn9
            // 
            this.gridColumn9.Caption = "文件名称";
            this.gridColumn9.FieldName = "FileNames";
            this.gridColumn9.Name = "gridColumn9";
            this.gridColumn9.Visible = true;
            this.gridColumn9.VisibleIndex = 0;
            // 
            // gridColumn10
            // 
            this.gridColumn10.Caption = "连续栅格集";
            this.gridColumn10.FieldName = "WeakGridDesc";
            this.gridColumn10.Name = "gridColumn10";
            this.gridColumn10.Visible = true;
            this.gridColumn10.VisibleIndex = 1;
            // 
            // gridColumn11
            // 
            this.gridColumn11.Caption = "连续栅格数";
            this.gridColumn11.FieldName = "WeakGridCount";
            this.gridColumn11.Name = "gridColumn11";
            this.gridColumn11.Visible = true;
            this.gridColumn11.VisibleIndex = 2;
            // 
            // gridColumn12
            // 
            this.gridColumn12.Caption = "栅格最强电平";
            this.gridColumn12.FieldName = "MaxRsrp";
            this.gridColumn12.Name = "gridColumn12";
            this.gridColumn12.Visible = true;
            this.gridColumn12.VisibleIndex = 3;
            // 
            // gridColumn13
            // 
            this.gridColumn13.Caption = "栅格最弱电平";
            this.gridColumn13.DisplayFormat.FormatString = "F2";
            this.gridColumn13.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn13.FieldName = "MinRsrp";
            this.gridColumn13.Name = "gridColumn13";
            this.gridColumn13.Visible = true;
            this.gridColumn13.VisibleIndex = 4;
            // 
            // gridColumn14
            // 
            this.gridColumn14.Caption = "栅格平均电平";
            this.gridColumn14.DisplayFormat.FormatString = "F2";
            this.gridColumn14.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn14.FieldName = "AvgRsrp";
            this.gridColumn14.Name = "gridColumn14";
            this.gridColumn14.SummaryItem.DisplayFormat = "连续弱覆盖比例:\"{0}\"";
            this.gridColumn14.SummaryItem.SummaryType = DevExpress.Data.SummaryItemType.Custom;
            this.gridColumn14.SummaryItem.Tag = "1";
            this.gridColumn14.Visible = true;
            this.gridColumn14.VisibleIndex = 5;
            // 
            // gridColumn4
            // 
            this.gridColumn4.Caption = "栅格最强SINR";
            this.gridColumn4.FieldName = "MaxSinr";
            this.gridColumn4.Name = "gridColumn4";
            this.gridColumn4.Visible = true;
            this.gridColumn4.VisibleIndex = 6;
            // 
            // gridColumn5
            // 
            this.gridColumn5.Caption = "栅格最弱SINR";
            this.gridColumn5.FieldName = "MinSinr";
            this.gridColumn5.Name = "gridColumn5";
            this.gridColumn5.Visible = true;
            this.gridColumn5.VisibleIndex = 7;
            // 
            // gridColumn6
            // 
            this.gridColumn6.Caption = "栅格平均SINR";
            this.gridColumn6.FieldName = "AvgSinr";
            this.gridColumn6.Name = "gridColumn6";
            this.gridColumn6.Visible = true;
            this.gridColumn6.VisibleIndex = 8;
            // 
            // NBIOTMgrsWeakRsrpResult
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.gridControl1);
            this.Name = "NBIOTMgrsWeakRsrpResult";
            ((System.ComponentModel.ISupportInitialize)(this.gvArea)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl1)).EndInit();
            this.contextMenuStrip1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gv)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvGrid)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvCell)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvSerialGrid)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.ToolStripMenuItem miExportSimpleExcel;
        private System.Windows.Forms.ToolStripMenuItem miExportDetailExcel;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator1;
        private System.Windows.Forms.ToolStripMenuItem miExportAllExcel;
        private System.Windows.Forms.ToolStripMenuItem miExportList;
        protected System.Windows.Forms.ContextMenuStrip contextMenuStrip1;
        protected DevExpress.XtraGrid.GridControl gridControl1;
        protected DevExpress.XtraGrid.Views.Grid.GridView gv;
        protected DevExpress.XtraGrid.Views.Grid.GridView gvArea;
        protected DevExpress.XtraGrid.Views.Grid.GridView gvSerialGrid;
        protected DevExpress.XtraGrid.Views.Grid.GridView gvGrid;
        protected DevExpress.XtraGrid.Views.Grid.GridView gvCell;
        protected DevExpress.XtraGrid.Columns.GridColumn gridColumn9;
        protected DevExpress.XtraGrid.Columns.GridColumn gridColumn10;
        protected DevExpress.XtraGrid.Columns.GridColumn gridColumn11;
        protected DevExpress.XtraGrid.Columns.GridColumn gridColumn12;
        protected DevExpress.XtraGrid.Columns.GridColumn gridColumn13;
        protected DevExpress.XtraGrid.Columns.GridColumn gridColumn14;
        protected DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        protected DevExpress.XtraGrid.Columns.GridColumn gridColumn15;
        protected DevExpress.XtraGrid.Columns.GridColumn gridColumn18;
        protected DevExpress.XtraGrid.Columns.GridColumn gridColumn19;
        protected DevExpress.XtraGrid.Columns.GridColumn gridColumn20;
        protected DevExpress.XtraGrid.Columns.GridColumn gridColumn21;
        protected DevExpress.XtraGrid.Columns.GridColumn gridColumn8;
        protected DevExpress.XtraGrid.Columns.GridColumn gridColumn16;
        protected DevExpress.XtraGrid.Columns.GridColumn gridColumn17;
        protected DevExpress.XtraGrid.Columns.GridColumn gridColumn24;
        protected DevExpress.XtraGrid.Columns.GridColumn gridColumn25;
        protected DevExpress.XtraGrid.Columns.GridColumn gridColumn26;
        protected DevExpress.XtraGrid.Columns.GridColumn gridColumn27;
        protected DevExpress.XtraGrid.Columns.GridColumn gridColumn28;
        protected DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        protected DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        protected DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        protected DevExpress.XtraGrid.Columns.GridColumn gridColumn5;
        protected DevExpress.XtraGrid.Columns.GridColumn gridColumn6;
        protected DevExpress.XtraGrid.Columns.GridColumn gridColumn22;
        protected DevExpress.XtraGrid.Columns.GridColumn gridColumn7;
        protected DevExpress.XtraGrid.Columns.GridColumn gridColumn29;
        protected DevExpress.XtraGrid.Columns.GridColumn gridColumn30;
        private System.Windows.Forms.ToolStripMenuItem miExportShp;
        private System.Windows.Forms.ToolStripMenuItem miExportAllShp;
    }
}
