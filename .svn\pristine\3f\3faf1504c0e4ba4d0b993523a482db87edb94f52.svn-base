﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    class LteTestAcceptResult
    {


        public string BtsName
        {
            get;
            private set;
        }

        public int SectorCount
        {
            get { return sectorDic.Count; }
        }

        public List<int> SectorIDs
        {
            get
            {
                List<int> retList = new List<int>(sectorDic.Keys);
                retList.Sort();
                return retList;
            }
        }

        public LteTestAcceptResult(string btsName,
            int rowCnt, int colCnt)
        {
            BtsName = btsName;

            this.rowCnt = rowCnt;
            this.colCnt = colCnt;
            this.sectorDic = new Dictionary<int, object[,]>();
        }

        public object GetValue(int sectorID, int rowIdx, int colIdx)
        {
            if (!sectorDic.ContainsKey(sectorID))
            {
                return null;
            }

            object[,] table = sectorDic[sectorID];
            if (rowIdx >= table.GetLength(0) || colIdx >= table.GetLength(1))
            {
                return null;
            }

            return table[rowIdx, colIdx];
        }

        public void SetValue(int sectorID, int rowIdx, int colIdx, object value, bool isCover)
        {
            try
            {
                if (isCover)
                {
                    if (!sectorDic.ContainsKey(sectorID))
                    {
                        sectorDic.Add(sectorID, new object[rowCnt, colCnt]);
                    }
                    sectorDic[sectorID][rowIdx, colIdx] = value;
                }
                else
                {
                    if (!sectorDic.ContainsKey(sectorID))
                    {
                        sectorDic.Add(sectorID, new object[rowCnt, colCnt]);
                        sectorDic[sectorID][rowIdx, colIdx] = value;
                    }
                    else if (sectorDic[sectorID][rowIdx, colIdx] == null)
                    {
                        sectorDic[sectorID][rowIdx, colIdx] = value;
                    }
                    else
                    {
                        if (value is int)
                        {
                            int iValue = (int)value + (int)sectorDic[sectorID][rowIdx, colIdx];
                            sectorDic[sectorID][rowIdx, colIdx] = iValue as object;
                        }
                        else if (value is double)
                        {
                            double dValue = (double)value + (double)sectorDic[sectorID][rowIdx, colIdx];
                            sectorDic[sectorID][rowIdx, colIdx] = dValue as object;
                        }
                        else if (value is float)
                        {
                            float fValue = (float)value + (float)sectorDic[sectorID][rowIdx, colIdx];
                            sectorDic[sectorID][rowIdx, colIdx] = fValue as object;
                        }
                        else
                        {
                            sectorDic[sectorID][rowIdx, colIdx] = value;

                        }
                    }
                }
            }
            catch
            {
                //continue
            }
        }

        private readonly Dictionary<int, object[,]> sectorDic;

        private readonly int rowCnt;

        private readonly int colCnt;
    }
}
