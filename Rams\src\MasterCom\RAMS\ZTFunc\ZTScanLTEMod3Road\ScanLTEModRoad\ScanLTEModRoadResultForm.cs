﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

using DevExpress.XtraGrid.Columns;
using DevExpress.XtraGrid.Views.Grid;

using MasterCom.Util;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Util;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ScanLTEModRoadResultForm : MinCloseForm
    {
        public ScanLTEModRoadResultForm(MainModel mm)
            : base(mm)
        {
            InitializeComponent();
            this.DisposeWhenClose = true;
            btnFind.Click += BtnFind_Click;
            gridView1.DoubleClick += GridView_DoubleClick;
            gridView2.DoubleClick += GridView_DoubleClick;
            gridView3.DoubleClick += GridView_DoubleClick;
            miExportExcel.Click += MiExportExcel_Click;
        }

        private void BtnFind_Click(object sender, EventArgs e)
        {
            FillData();
        }

        private void GridView_DoubleClick(object sender, EventArgs e)
        {
            GridView gv = sender as GridView;
            object row = gv.GetRow(gv.GetSelectedRows()[0]);
            if (row is ScanLTEModCellInfo)
            {
                ScanLTEModCellInfo cellInfo = row as ScanLTEModCellInfo;
                LTECell cell = cellInfo.LteCell;
                MainModel.SelectedLTECells = null;
                MainModel.SelectedLTECell = cell;
                MainModel.FireSelectedCellChanged(MainModel.MainForm);
            }
            else if (row is ScanLTEModRoadInfo)
            {
                ScanLTEModRoadInfo roadInfo = row as ScanLTEModRoadInfo;
                MainModel.DTDataManager.Clear();
                foreach (TestPoint tp in roadInfo.TestPoints)
                {
                    MainModel.DTDataManager.Add(tp);
                }
                MainModel.FireDTDataChanged(MainModel.MainForm);
                
                if (roadInfo.TestPoints.Count == 0)
                {
                    return;
                }
                TestPoint midTp = roadInfo.TestPoints[roadInfo.TestPoints.Count / 2];
                MainModel.MainForm.GetMapForm().GoToView(midTp.Longitude, midTp.Latitude);

                OutlineOfRoad outRoad = new OutlineOfRoad();
                outRoad.SetPoints(roadInfo.TestPoints);
                TempLayer.Instance.Draw(outRoad.Drawer);
            }
        }

        private void MiExportExcel_Click(object sender, EventArgs e)
        {
            if (isNeedHideCellKeyInfo)
            {
                List<List<object>> exportList = MasterCom.Util.GridViewTransfer.Transfer(gridControl1);
                MasterCom.Util.ExcelNPOIManager.ExportToExcel(exportList);
                return;
            }

            List<NPOIRow> contents = new List<NPOIRow>();
            NPOIRow titleRow = new NPOIRow();
            foreach (GridColumn col in gridView1.VisibleColumns)
            {
                titleRow.AddCellValue(col.Caption);
            }
            foreach (GridColumn col in gridView2.VisibleColumns)
            {
                titleRow.AddCellValue(col.Caption);
            }
            foreach (GridColumn col in gridView3.VisibleColumns)
            {
                titleRow.AddCellValue(col.Caption);
            }
            contents.Add(titleRow);

            List<ScanLTEModRoadInfo> roadList = gridControl1.DataSource as List<ScanLTEModRoadInfo>;
            foreach (ScanLTEModRoadInfo road in roadList)
            {
                NPOIRow roadRow = new NPOIRow();
                roadRow.AddCellValue(road.SN);
                roadRow.AddCellValue(road.RoadDesc);
                roadRow.AddCellValue(road.Length);
                roadRow.AddCellValue(road.RelLevel);
                roadRow.AddCellValue(road.TotalSampleCount);
                roadRow.AddCellValue(road.InvalidSampleCount);
                roadRow.AddCellValue(road.CellsCount);
                roadRow.AddCellValue(road.ModCellsCount);
                roadRow.AddCellValue(road.FileName);

                foreach (ScanLTEModCellInfo tarCell in road.ModCells)
                {
                    NPOIRow tarRow = new NPOIRow();
                    tarRow.cellValues.AddRange(roadRow.cellValues);
                    tarRow.AddCellValue(tarCell.CellName);
                    tarRow.AddCellValue(tarCell.CellID);
                    tarRow.AddCellValue(tarCell.EARFCN);
                    tarRow.AddCellValue(tarCell.PCI);
                    tarRow.AddCellValue(tarCell.SID);
                    tarRow.AddCellValue(tarCell.SrcCellsCount);
                    tarRow.AddCellValue(tarCell.Longitude);
                    tarRow.AddCellValue(tarCell.Latitude);
                    tarRow.AddCellValue(tarCell.Direction);

                    foreach (ScanLTEModCellInfo srcCell in tarCell.SrcCells)
                    {
                        NPOIRow srcRow = new NPOIRow();
                        srcRow.cellValues.AddRange(tarRow.cellValues);
                        srcRow.AddCellValue(srcCell.CellName);
                        srcRow.AddCellValue(srcCell.CellID);
                        srcRow.AddCellValue(srcCell.TarInterfereCount);
                        srcRow.AddCellValue(srcCell.EARFCN);
                        srcRow.AddCellValue(srcCell.PCI);
                        srcRow.AddCellValue(srcCell.SID);
                        srcRow.AddCellValue(srcCell.Longitude);
                        srcRow.AddCellValue(srcCell.Latitude);
                        srcRow.AddCellValue(srcCell.Direction);
                        srcRow.AddCellValue(srcCell.TarDistance);

                        contents.Add(srcRow);
                    }
                }
            }
            ExcelNPOIManager.ExportToExcel(contents);
        }

        public void FillData(ScanLTEModRoadStater stater)
        {
            this.stater = stater;
            FillData();
        }

        private void FillData()
        {
            LTEModInterfereCond filter = GetFilter();
            object result = stater.GetStatResult(filter);
            gridControl1.DataSource = result;
            gridControl1.RefreshDataSource();
        }

        public override void ReleaseResources()
        {
            if (this.stater != null)
            {
                stater.Clear();
            }
            TempLayer.Instance.Clear();
            MainModel.DTDataManager.Clear();
            MainModel.FireDTDataChanged(MainModel.MainForm);
        }

        private LTEModInterfereCond GetFilter()
        {
            LTEModInterfereCond filter = new LTEModInterfereCond();
            filter.Angle = (double)numAngle.Value;
            filter.Distance = (double)numDistance.Value;
            if (chkValue0.Checked) filter.Sids.Add(0);
            if (chkValue1.Checked) filter.Sids.Add(1);
            if (chkValue2.Checked) filter.Sids.Add(2);
            return filter;
        }

        private ScanLTEModRoadStater stater;
    }
}
