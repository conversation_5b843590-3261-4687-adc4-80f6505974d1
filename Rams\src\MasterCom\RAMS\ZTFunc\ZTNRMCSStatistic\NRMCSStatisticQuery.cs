﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc.ZTNRMCSStatistic;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRMCSStatisticQuery : DIYAnalyseByFileBackgroundBase
    {
        protected static readonly object lockObj = new object();
        private static NRMCSStatisticQuery instance = null;
        public static NRMCSStatisticQuery GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new NRMCSStatisticQuery();
                    }
                }
            }
            return instance;
        }

        protected NRMCSStatisticQuery()
           : base(MainModel.GetInstance())
        {
            this.IncludeMessage = true;
            this.Columns = new List<string>();

            Columns.Add("NR_QPSK_Count_UL");
            Columns.Add("NR_QPSK_Count_DL");
            Columns.Add("NR_16QAM_Count_UL");
            Columns.Add("NR_16QAM_Count_DL");
            Columns.Add("NR_64QAM_Count_UL");
            Columns.Add("NR_64QAM_Count_DL");
            Columns.Add("NR_256QAM_Count_UL");
            Columns.Add("NR_256QAM_Count_DL");
        }

        public override string Name
        {
            get
            {
                return "调度编码方式分析(按文件)";
            }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22046, this.Name);
        }

        protected bool checkDelay = true;
        protected int maxDelaySec = 10;

        protected List<SingleMCSStatisticInfo> callStatList = null;

        protected override bool getCondition()
        {
            //var dlg = new CallConditionDlg();
            //dlg.SetCondition(checkDelay, maxDelaySec);
            //if (dlg.ShowDialog() != DialogResult.OK)
            //{
            //    return false;
            //}

            //dlg.GetCondition(out checkDelay, out maxDelaySec);

            callStatList = new List<SingleMCSStatisticInfo>();

            return callStatList != null;
        }

        protected override void fireShowForm()
        {
            if (callStatList == null || callStatList.Count == 0)
            {
                MessageBox.Show("没有符合条件的数据。");
                return;
            }

            var frm = MainModel.GetObjectFromBlackboard(typeof(MCSStatisticForm)) as MCSStatisticForm;
            if (frm == null || frm.IsDisposed)
            {
                frm = new MCSStatisticForm();
            }
            frm.FillData(callStatList);
            frm.Owner = MainModel.MainForm;
            frm.Visible = true;
            frm.BringToFront();
            callStatList = null;
        }

        protected override void analyseFiles()
        {
            try
            {
                clearDataBeforeAnalyseFiles();
                int iloop = 0;

                foreach (FileInfo fileInfo in MainModel.FileInfos)
                {
                    if (ServiceTypes.Count > 0 && fileInfo.ServiceType > 0
                       && !ServiceTypes.Contains((ServiceType)fileInfo.ServiceType))
                    {
                        continue;
                    }

                    WaitBox.Text = "正在分析(" + (++iloop) + "/" + MainModel.FileInfos.Count + ")...";
                    WaitBox.ProgressPercent = (int)(iloop * 100.0 / MainModel.FileInfos.Count);

                    condition.FileInfos.Add(fileInfo);

                    replay();
                    condition.FileInfos.Clear();

                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                }
            }
            finally
            {
                System.Threading.Thread.Sleep(10);
                WaitBox.Close();
            }
        }

        protected override void doStatWithQuery()
        {
            foreach (var file in MainModel.DTDataManager.FileDataManagers)
            {
                if (file != null)
                {
                    dealMCSFile(file);
                }
            }
        }

        private void dealMCSFile(DTFileDataManager mcsFile)
        {
            try
            {
                SingleMCSStatisticInfo singleMCS = new SingleMCSStatisticInfo();

                for (int i = 0; i < mcsFile.DTDatas.Count; i++)
                {
                    var sample = mcsFile.DTDatas[i];

                    TestPoint_NR nrtp = sample as TestPoint_NR;

                    if (nrtp != null)
                    {
                        singleMCS.nrtp = nrtp;
                        dealMCSSample(singleMCS, nrtp);
                    }
                }

                singleMCS.Sn = 1;
                callStatList.Add(singleMCS);
            }
            catch (Exception e)
            {
                MessageBox.Show(e.Message + Environment.NewLine + e.StackTrace);
            }
        }

        private void dealMCSSample(SingleMCSStatisticInfo mcsInfo, TestPoint_NR sample)
        {
            mcsInfo.FileName = sample.FileName;

            mcsInfo._qpskUpCount += Convert.ToInt32(sample["NR_QPSK_Count_UL"]);
            mcsInfo._qpskDownCount += Convert.ToInt32(sample["NR_QPSK_Count_DL"]);

            mcsInfo._16qamUpCount += Convert.ToInt32(sample["NR_16QAM_Count_UL"]);
            mcsInfo._16qamDownCount += Convert.ToInt32(sample["NR_16QAM_Count_DL"]);

            mcsInfo._64qamUpCount += Convert.ToInt32(sample["NR_64QAM_Count_UL"]);
            mcsInfo._64qamDownCount += Convert.ToInt32(sample["NR_64QAM_Count_DL"]);

            mcsInfo._256qamUpCount += Convert.ToInt32(sample["NR_256QAM_Count_UL"]);
            mcsInfo._256qamDownCount += Convert.ToInt32(sample["NR_256QAM_Count_DL"]);
        }

    }


    public class SingleMCSStatisticInfo
    {
        public TestPoint_NR nrtp { get; set; }

        public int Sn { get; set; }
        public string FileName { get; set; }

        public float _qpskUpProportion
        {
            get
            {
                long mcsSum = _qpskUpCount + _16qamUpCount + _64qamUpCount + _256qamUpCount;

                if (mcsSum <= 0)
                {
                    return 0;
                }

                return _qpskUpCount * 1.0F / mcsSum;
            }
        }
        public float _qpskDownProportion
        {
            get
            {
                long mcsSum = _qpskDownCount + _16qamDownCount + _64qamDownCount + _256qamDownCount;

                if (mcsSum <= 0)
                {
                    return 0;
                }

                return _qpskDownCount * 1.0F / mcsSum;
            }
        }

        public float _16qamUpProportion
        {
            get
            {
                long mcsSum = _qpskUpCount + _16qamUpCount + _64qamUpCount + _256qamUpCount;

                if (mcsSum <= 0)
                {
                    return 0;
                }

                return _16qamUpCount * 1.0F / mcsSum;
            }
        }
        public float _16qamDownProportion
        {
            get
            {
                long mcsSum = _qpskDownCount + _16qamDownCount + _64qamDownCount + _256qamDownCount;

                if (mcsSum <= 0)
                {
                    return 0;
                }

                return _16qamDownCount * 1.0F / mcsSum;
            }
        }

        public float _64qamUpProportion
        {
            get
            {
                long mcsSum = _qpskUpCount + _16qamUpCount + _64qamUpCount + _256qamUpCount;

                if (mcsSum <= 0)
                {
                    return 0;
                }

                return _64qamUpCount * 1.0F / mcsSum;
            }
        }
        public float _64qamDownProportion
        {
            get
            {
                long mcsSum = _qpskDownCount + _16qamDownCount + _64qamDownCount + _256qamDownCount;

                if (mcsSum <= 0)
                {
                    return 0;
                }

                return _64qamDownCount * 1.0F / mcsSum;
            }
        }

        public float _256qamUpProportion
        {
            get
            {
                long mcsSum = _qpskUpCount + _16qamUpCount + _64qamUpCount + _256qamUpCount;

                if (mcsSum <= 0)
                {
                    return 0;
                }

                return _256qamUpCount * 1.0F / mcsSum;
            }
        }
        public float _256qamDownProportion
        {
            get
            {
                long mcsSum = _qpskDownCount + _16qamDownCount + _64qamDownCount + _256qamDownCount;

                if (mcsSum <= 0)
                {
                    return 0;
                }

                return _256qamDownCount * 1.0F / mcsSum;
            }
        }


        public int _qpskUpCount { get; set; }
        public int _qpskDownCount { get; set; }

        public int _16qamUpCount { get; set; }
        public int _16qamDownCount { get; set; }

        public int _64qamUpCount { get; set; }
        public int _64qamDownCount { get; set; }

        public int _256qamUpCount { get; set; }
        public int _256qamDownCount { get; set; }

    }




}
