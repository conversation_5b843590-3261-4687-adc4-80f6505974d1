﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Stat;
using MasterCom.RAMS.Stat.Data;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model.Interface;
using DBDataViewer;
using System.Windows.Forms;
using MasterCom.RAMS.ZTFunc;
using MasterCom.RAMS.KPI_Statistics;

namespace MasterCom.RAMS.Net
{
    public class ZTNBCellMissByRegion_LTE2G : ZTNBCellMissByRegion
    {
        public ZTNBCellMissByRegion_LTE2G(MainModel mainModel)
            : base(mainModel)
        {
        }

        public override string Name
        {
            get { return "LTE2G邻区配置核查"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 23000, 23001, this.Name);
        }

        protected override bool getConditionBeforeQuery()
        {
            cellGridDic.Clear();
            MainModel.CellManager.GetLTENBCellInfo();
            return true;
        }

        protected override void fireShowResult()
        {
            ZTNBCellMissForm frm = MainModel.CreateResultForm(typeof(ZTNBCellMissForm)) as ZTNBCellMissForm;
            frm.FillDatas(cellGridDic, InspectType.LTE2GSM);
            frm.Visible = true;
            frm.BringToFront();
        }

        protected override string getStatImgNeededTriadID(params object[] paramSet)
        {
            List<string> formulaSet = new List<string>();
            formulaSet.Add(DataScan_LTE.PSSRPMeanValue);
            formulaSet.Add(DataScan_LTE.PSSRPMaxValue);
            formulaSet.Add(DataScan_LTE.PSSRPSampleNum);
            formulaSet.Add(DataScan_GSM.RxlevMeanValueID);
            formulaSet.Add(DataScan_GSM.RxlevMaxID);
            formulaSet.Add(DataScan_GSM.RxlevSampleCountID);
            return getTriadIDIgnoreServiceType(formulaSet);
        }

        protected override void getGridCells()
        {
            foreach (GridDataUnit grid in CurScanGridUnitMatrix)
            {
                StatDataSCAN_LTE dataScan = grid.GetStatData(typeof(StatDataSCAN_LTE)) as StatDataSCAN_LTE;
                dealDataSCAN(grid, dataScan, "LTE", DataScan_LTE.PSSRPMeanValue, DataScan_LTE.PSSRPMaxValue, DataScan_LTE.PSSRPSampleNum);

                StatDataSCAN_GSM dataScam_GSM = grid.GetStatData(typeof(StatDataSCAN_GSM)) as StatDataSCAN_GSM;
                dealDataSCAN(grid, dataScam_GSM, "GSM", DataScan_GSM.RxlevMeanValueID, DataScan_GSM.RxlevMaxID, DataScan_GSM.RxlevSampleCountID);
            }
        }
    }
}
