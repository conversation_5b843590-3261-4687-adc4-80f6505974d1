﻿using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;
using static MasterCom.RAMS.Func.PopShow.NewBlackBlockInfoPanel_ng;

namespace MasterCom.RAMS.Func.PopShow
{
    internal class BlackBlockInfoHelper
    {
        #region
        public List<BlockNumResult> PrepareShowByPeriod(List<NewBlockEntryItem> blockList, string selShowType)
        {
            Dictionary<string, Dictionary<int, BlockNumResult>> areaDic = dealAreaDic(blockList, selShowType, getTimeStr);
            //按当前采用的时间粒度进行填充，使各个地区均有相同多的时间块数
            //找到最大最小
            int mintimeV = 1934567890;
            int maxtimeV = 0;
            getMinMax(areaDic, ref mintimeV, ref maxtimeV);

            int timeStepGap = getTimeStepGap(selShowType);
            setAreaDic(selShowType, areaDic, mintimeV, maxtimeV, timeStepGap, getTimeStr);

            Dictionary<string, List<BlockNumResult>> placeHistoryDic = dealPlaceHistoryDic(areaDic, addHisList);

            List<BlockNumResult> finalResult = new List<BlockNumResult>();
            foreach (List<BlockNumResult> list in placeHistoryDic.Values)
            {
                finalResult.AddRange(list);
            }
            return finalResult;
        }

        private Dictionary<string, Dictionary<int, BlockNumResult>> dealAreaDic(List<NewBlockEntryItem> blockList, string selShowType, Func func)
        {
            //(dbid+区域名) -> dic<timevalue,计数单元>
            Dictionary<string, Dictionary<int, BlockNumResult>> areaDic = new Dictionary<string, Dictionary<int, BlockNumResult>>();
            foreach (NewBlockEntryItem block in blockList)
            {
                string placekey = block.dbid + ":" + block.area_names;
                Dictionary<int, BlockNumResult> placeNumRetDic = null;
                if (!areaDic.TryGetValue(placekey, out placeNumRetDic))
                {
                    placeNumRetDic = new Dictionary<int, BlockNumResult>();
                    areaDic[placekey] = placeNumRetDic;
                }
                int dayValue = 0;
                string timeStrCreated = "";
                func(selShowType, block.created_date, ref dayValue, ref timeStrCreated);
                BlockNumResult numRet = null;
                if (!placeNumRetDic.TryGetValue(dayValue, out numRet))
                {
                    numRet = new BlockNumResult();
                    numRet._timeValue = dayValue;
                    numRet.timeStr = timeStrCreated;
                    numRet.areaStr = block.area_names;
                    placeNumRetDic[dayValue] = numRet;
                }
                numRet.createdNum++;
                if (block.status == 4)//已经关闭的
                {
                    string timeStrClosed = "";
                    func(selShowType, block.created_date, ref dayValue, ref timeStrClosed);
                    BlockNumResult numRet2 = null;
                    if (!placeNumRetDic.TryGetValue(dayValue, out numRet2))
                    {
                        numRet2 = new BlockNumResult();
                        numRet2._timeValue = dayValue;
                        numRet2.timeStr = timeStrClosed;
                        numRet2.areaStr = block.area_names;
                        placeNumRetDic[dayValue] = numRet2;
                    }
                    numRet2.closedNum++;
                }
            }

            return areaDic;
        }
        #endregion


        #region
        public List<BlockNumResult> PrepareShowByLastPeriod(List<BlockEntryItem> blockList, string selShowType)
        {
            Dictionary<string, Dictionary<int, BlockNumResult>> areaDic = dealAreaDic(blockList, selShowType, getTimeStrLast);
            //按当前采用的时间粒度进行填充，使各个地区均有相同多的时间块数
            //找到最大最小
            int mintimeV = 1934567890;
            int maxtimeV = 0;
            getMinMax(areaDic, ref mintimeV, ref maxtimeV);

            int timeStepGap = getTimeStepGapLast(selShowType);
            setAreaDic(selShowType, areaDic, mintimeV, maxtimeV, timeStepGap, getTimeStrLast);

            Dictionary<string, List<BlockNumResult>> placeHistoryDic = dealPlaceHistoryDic(areaDic, addHisListLast);

            List<BlockNumResult> finalResult = new List<BlockNumResult>();
            foreach (List<BlockNumResult> list in placeHistoryDic.Values)
            {
                if (list.Count > 0)
                {
                    finalResult.Add(list[list.Count - 1]);//最后一个统计时段
                }
            }
            return finalResult;
        }

        private void getTimeStrLast(string type, int time, ref int value, ref string timeStr)
        {
            if (type == "最近一周")
            {
                timeStr = getWeekStr(time, out value);
            }
            else if (type == "最近一月")
            {
                timeStr = getMonthStr(time, out value);
            }
        }

        private static int getTimeStepGapLast(string selShowType)
        {
            //找每周跨度的跨度
            int timeStepGap = 0;
            if (selShowType == "最近一周")
            {
                timeStepGap = 24 * 3600 * 7;
            }
            else if (selShowType == "最近一月")
            {
                timeStepGap = 24 * 3600 * 28;//最少天数
            }
            else
            {
                throw (new Exception("未预期的粒度"));
            }

            return timeStepGap;
        }

        private static string addHisListLast(List<BlockNumResult> hisList, Dictionary<int, BlockNumResult> dayDic)
        {
            string area_names_rpl = "";
            foreach (BlockNumResult nr in dayDic.Values)
            {
                if (nr.areaStr != "" && area_names_rpl == "")
                {
                    area_names_rpl = nr.areaStr;
                }
            }
            foreach (BlockNumResult nr in dayDic.Values)
            {
                hisList.Add(nr);
            }

            return area_names_rpl;
        }
        #endregion

        #region
        public List<BlockNumResult> PrepareShowByPeriod(List<BlockEntryItem> blockList, string selShowType)
        {
            Dictionary<string, Dictionary<int, BlockNumResult>> areaDic = dealAreaDic(blockList, selShowType, getTimeStr);
            //按当前采用的时间粒度进行填充，使各个地区均有相同多的时间块数
            //找到最大最小
            int mintimeV = 1934567890;
            int maxtimeV = 0;
            getMinMax(areaDic, ref mintimeV, ref maxtimeV);

            int timeStepGap = getTimeStepGap(selShowType);
            setAreaDic(selShowType, areaDic, mintimeV, maxtimeV, timeStepGap, getTimeStr);

            Dictionary<string, List<BlockNumResult>> placeHistoryDic = dealPlaceHistoryDic(areaDic, addHisList);

            List<BlockNumResult> finalResult = new List<BlockNumResult>();
            foreach (List<BlockNumResult> list in placeHistoryDic.Values)
            {
                finalResult.AddRange(list);
            }
            return finalResult;
        }

        private void getTimeStr(string type, int time, ref int value, ref string timeStr)
        {
            if (type == "按天")
            {
                timeStr = getDayStr(time, out value);
            }
            else if (type == "按周")
            {
                timeStr = getWeekStr(time, out value);
            }
            else if (type == "按月")
            {
                timeStr = getMonthStr(time, out value);
            }
        }

        private int getTimeStepGap(string selShowType)
        {
            //找每周跨度的跨度
            int timeStepGap = 0;
            if (selShowType == "按天")
            {
                timeStepGap = 24 * 3600;
            }
            else if (selShowType == "按周")
            {
                timeStepGap = 24 * 3600 * 7;
            }
            else if (selShowType == "按月")
            {
                timeStepGap = 24 * 3600 * 28;//最少天数
            }
            else
            {
                throw (new Exception("未预期的粒度"));
            }

            return timeStepGap;
        }

        private static string addHisList(List<BlockNumResult> hisList, Dictionary<int, BlockNumResult> dayDic)
        {
            string area_names_rpl = "";
            foreach (BlockNumResult nr in dayDic.Values)
            {
                hisList.Add(nr);
                if (nr.areaStr != "" && area_names_rpl == "")
                {
                    area_names_rpl = nr.areaStr;
                }
            }

            return area_names_rpl;
        }
        #endregion



        private Dictionary<string, Dictionary<int, BlockNumResult>> dealAreaDic(List<BlockEntryItem> blockList, string selShowType, Func func)
        {
            //(dbid+区域名) -> dic<timevalue,计数单元>
            Dictionary<string, Dictionary<int, BlockNumResult>> areaDic = new Dictionary<string, Dictionary<int, BlockNumResult>>();
            foreach (BlockEntryItem block in blockList)
            {
                string placekey = block.dbid + ":" + block.area_names;
                Dictionary<int, BlockNumResult> placeNumRetDic = null;
                if (!areaDic.TryGetValue(placekey, out placeNumRetDic))
                {
                    placeNumRetDic = new Dictionary<int, BlockNumResult>();
                    areaDic[placekey] = placeNumRetDic;
                }
                int dayValue = 0;
                string timeStrCreated = "";
                func(selShowType, block.created_date, ref dayValue, ref timeStrCreated);
                BlockNumResult numRet = null;
                if (!placeNumRetDic.TryGetValue(dayValue, out numRet))
                {
                    numRet = new BlockNumResult();
                    numRet._timeValue = dayValue;
                    numRet.timeStr = timeStrCreated;
                    numRet.areaStr = block.area_names;
                    placeNumRetDic[dayValue] = numRet;
                }
                numRet.createdNum++;
                if (block.status == 4)//已经关闭的
                {
                    string timeStrClosed = "";
                    func(selShowType, block.created_date, ref dayValue, ref timeStrClosed);
                    BlockNumResult numRet2 = null;
                    if (!placeNumRetDic.TryGetValue(dayValue, out numRet2))
                    {
                        numRet2 = new BlockNumResult();
                        numRet2._timeValue = dayValue;
                        numRet2.timeStr = timeStrClosed;
                        numRet2.areaStr = block.area_names;
                        placeNumRetDic[dayValue] = numRet2;
                    }
                    numRet2.closedNum++;
                }
            }

            return areaDic;
        }

        private static void getMinMax(Dictionary<string, Dictionary<int, BlockNumResult>> areaDic, ref int mintimeV, ref int maxtimeV)
        {
            foreach (Dictionary<int, BlockNumResult> timeNumDic in areaDic.Values)
            {
                foreach (int timek in timeNumDic.Keys)
                {
                    if (timek < mintimeV)
                    {
                        mintimeV = timek;
                    }
                    if (timek > maxtimeV)
                    {
                        maxtimeV = timek;
                    }
                }
            }
        }

        delegate void Func(string type, int time, ref int value, ref string timeStr);

        private void setAreaDic(string selShowType, Dictionary<string, Dictionary<int, BlockNumResult>> areaDic, int mintimeV, int maxtimeV, int timeStepGap, Func func)
        {
           
            int stepCount = 2 + (maxtimeV - mintimeV) / timeStepGap;
            for (int i = 0; i < stepCount; i++)
            {
                int _timevalue = mintimeV + i * timeStepGap;
                int _rplaceTime = 0;
                string _rplaceTimeStr = "";
                func(selShowType, _timevalue, ref _rplaceTime, ref _rplaceTimeStr);
                if (_rplaceTime > maxtimeV)
                {
                    break;
                }
                foreach (Dictionary<int, BlockNumResult> timeNumDic in areaDic.Values)
                {
                    if (!timeNumDic.ContainsKey(_rplaceTime))
                    {
                        BlockNumResult _rplNumRet = new BlockNumResult();
                        _rplNumRet._timeValue = _rplaceTime;
                        _rplNumRet.timeStr = _rplaceTimeStr;
                        _rplNumRet.areaStr = "";//未填入
                        timeNumDic[_rplaceTime] = _rplNumRet;//填补空缺的时间
                    }
                }
            }
        }

        delegate string FuncHis(List<BlockNumResult> hisList, Dictionary<int, BlockNumResult> dayDic);

        private static Dictionary<string, List<BlockNumResult>> dealPlaceHistoryDic(Dictionary<string, Dictionary<int, BlockNumResult>> areaDic, FuncHis func)
        {
            //生成按时间排序的结构
            Dictionary<string, List<BlockNumResult>> placeHistoryDic = new Dictionary<string, List<BlockNumResult>>();
            foreach (string thekey in areaDic.Keys)
            {
                List<BlockNumResult> hisList = new List<BlockNumResult>();
                placeHistoryDic[thekey] = hisList;
                Dictionary<int, BlockNumResult> dayDic = areaDic[thekey];
                string area_names_rpl = func(hisList, dayDic);
                hisList.Sort(BlockNumResult.GetCompareByTime());//按时间先后排序
                //按增减累计填充每个时段的黑点统计个数
                int curRemCount = 0;
                foreach (BlockNumResult nr in hisList)
                {
                    if (nr.areaStr == "")
                    {
                        nr.areaStr = area_names_rpl;
                    }
                    curRemCount += nr.createdNum;
                    curRemCount -= nr.closedNum;
                    nr.remainNum = curRemCount;
                }
            }

            return placeHistoryDic;
        }

        private string getDayStr(int time, out int dayValue)
        {
            DateTime dt = JavaDate.GetDateTimeFromMilliseconds(1000L * time);
            dayValue = (int)(JavaDate.GetMilliseconds(dt) / 1000L);
            return dt.ToString("yyyy.MM.dd");
        }

        private string getMonthStr(int time, out int monthValue)
        {
            DateTime dt = JavaDate.GetDateTimeFromMilliseconds(1000L * time);
            monthValue = (int)(JavaDate.GetMilliseconds(new DateTime(dt.Year, dt.Month, 1)) / 1000L);
            return dt.ToString("yyyy.MM");
        }

        private string getWeekStr(int time, out int weekValue)
        {
            DateTime dt = JavaDate.GetDateTimeFromMilliseconds(time * 1000L).Date;
            DayOfWeek dweek = dt.DayOfWeek;
            int dayToMondy = 0;
            switch (dweek)
            {
                case DayOfWeek.Monday:
                    dayToMondy = 0;
                    break;
                case DayOfWeek.Tuesday:
                    dayToMondy = 1;
                    break;
                case DayOfWeek.Wednesday:
                    dayToMondy = 2;
                    break;
                case DayOfWeek.Thursday:
                    dayToMondy = 3;
                    break;
                case DayOfWeek.Friday:
                    dayToMondy = 4;
                    break;
                case DayOfWeek.Saturday:
                    dayToMondy = 5;
                    break;
                case DayOfWeek.Sunday:
                    dayToMondy = 6;
                    break;

            }
            weekValue = (int)(JavaDate.GetMilliseconds(dt.AddDays(-dayToMondy)) / 1000L);
            return dt.AddDays(-dayToMondy).ToString("yyyy.MM.dd_") + dt.AddDays(6 - dayToMondy).ToString("MM.dd");
        }
    }
}
