﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using MasterCom.Util.UiEx;
using System;
using System.Collections.Generic;
using System.Windows.Forms;
using System.Data;
using System.Text;
using MasterCom.RAMS.NewBlackBlock;

namespace MasterCom.RAMS.Func
{
    public class BlackBlockCalculateBase : QueryBase
    {
        private Dictionary<BBCityTokenItem, List<BBSummaryItem>> resultSummaryDic = null;
        private Dictionary<BBCityTokenItem, BBStatItem> resultStatDic = null;     //黑点情况
        private BlackBlockTimeSetCondition bbStatCondition = null;

        private static readonly object lockObj = new object();
        private static BlackBlockCalculateBase instance = null;
        public static BlackBlockCalculateBase GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new BlackBlockCalculateBase();
                    }
                }
            }
            return instance;
        }
        public override bool IsNeedSetQueryCondition
        {
            get
            {
                return false;
            }
        }

        BlackBlockSetConditionForm setForm = null;
        protected override bool isValidCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }
            if (setForm == null)
            {
                setForm = new BlackBlockSetConditionForm();
            }
            if (setForm.ShowDialog() == DialogResult.OK)
            {
                this.bbStatCondition = setForm.GetCondition();
                return true;
            }
            return false;
        }

        public override string Name
        {
            get { return "黑点统计"; }
        }

        public override string IconName
        {
            get { return null; }
        }

        protected BlackBlockCalculateBase()
            : base(MainModel.GetInstance())
        {
        }

        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 18000, 18039, this.Name);
        }
        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }
        protected override void query()
        {
            WaitBox.Show(doStat);
            DataTable dt;
            List<List<BBDetailItem>> resultList = getSheetLists(out dt);
            if (resultList == null || resultList.Count == 0 || dt.Rows.Count == 0)
            {
                MessageBox.Show("没有符合条件的数据");
                return;
            }

            showResultForm(resultList, dt);
        }

        private void doStat()
        {
            try
            {
                resultSummaryDic = new Dictionary<BBCityTokenItem, List<BBSummaryItem>>();
                resultStatDic = new Dictionary<BBCityTokenItem, BBStatItem>();

                DistrictManager mgr = DistrictManager.GetInstance();
                int iloop = 0;
                foreach (Stat.IDNamePair pair in mgr.GetAvailableDistrict())
                {
                    WaitBox.Text = "正在统计[" + pair.Name + "]的黑点情况,统计进度(" + (++iloop) + "/" + mgr.GetAvailableDistrict().Count + ")...";
                    ClientProxy clientProxy = new ClientProxy();

                    if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, pair.id) != ConnectResult.Success)
                    {
                        ErrorInfo = "连接服务器失败";
                        continue;
                    }

                    statEachDB(pair);
                }
                WaitBox.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.ToString());
                throw;
            }
        }
        private void statEachDB(Stat.IDNamePair pair)
        {
            foreach (BlackBlockToken token in bbStatCondition.TokenList)
            {
                try
                {
                    statEachToken(pair, token);
                }
                catch (System.Exception ex)
                {
                    log.Debug("统计地市：" + pair.Name + "出错,错误信息为：" + ex.ToString());
                }
            }
        }
        private void statEachToken(Stat.IDNamePair pair, BlackBlockToken token)
        {
            List<BBSummaryItem> bbList;
            Dictionary<int, List<BBEventItem>> evtDic;

            bbList = getSummaryList(pair.id, token.Token);
            evtDic = getEvtDic(pair.id, token.Token, pair.Name);

            BBStatItem statItem = new BBStatItem();
            List<BBSummaryItem> bbValidList = new List<BBSummaryItem>();
            foreach (BBSummaryItem bbItem in bbList)
            {
                bool isValid = judgeValid(bbItem);
                if (isValid)
                {
                    //有效数据
                    setEventInfo(bbItem, evtDic[bbItem.id], token.Token, pair.Name);  //获取事件信息
                    bbValidList.Add(bbItem);
                    statItem.bbTotal++;
                    if ((bbItem.status == 2 || bbItem.status == 3)
                        && bbItem.created_date >= bbStatCondition.beginTime && bbItem.created_date <= bbStatCondition.endTime)
                    {
                        //在此期间创建，新增++
                        statItem.bbAdded++;
                    }
                    if (bbItem.status == 4
                        && bbItem.closed_date >= bbStatCondition.beginTime && bbItem.closed_date <= bbStatCondition.endTime)
                    {
                        //在此期间关闭，关闭++
                        statItem.bbClosed++;
                    }
                }
            }

            add2ResultDic(pair.Name, token, statItem, bbValidList);
        }

        private bool judgeValid(BBSummaryItem bbItem)
        {
            if (bbItem.status != 2 && bbItem.status != 3 && bbItem.status != 4)     //状态不合理，不分析
            {
                return false;
            }
            if (bbItem.created_date > bbStatCondition.endTime)  //在统计周期后出现的黑点，不分析
            {
                return false;
            }
            if (bbItem.status == 4 && bbItem.closed_date < bbStatCondition.beginTime)  //在统计周期前关闭的黑点，不分析
            {
                return false;
            }
            return true;
        }

        private List<BBSummaryItem> getSummaryList(int dbid, string token)
        {
            DIYSQLBlackBlockSummary query = new DIYSQLBlackBlockSummary(MainModel, dbid, token, bbStatCondition.beginTime, bbStatCondition.endTime);
            query.Query();
            return query.GetBlackBlockSummaryList();
        }

        private Dictionary<int, List<BBEventItem>> getEvtDic(int dbid, string token, string cityName)
        {
            List<BBEventItem> evtList;
            DIYSQLBlackBlockEvt query = new DIYSQLBlackBlockEvt(MainModel, dbid, token, bbStatCondition.beginTime, bbStatCondition.endTime, cityName);
            query.Query();
            evtList = query.GetBlackBlockEvtList();

            //按照black_block_id分成哈希表
            Dictionary<int, List<BBEventItem>> evtDic = new Dictionary<int, List<BBEventItem>>();
            foreach (BBEventItem evtItem in evtList)
            {
                if (evtDic.ContainsKey(evtItem.black_block_id))
                {
                    evtDic[evtItem.black_block_id].Add(evtItem);
                }
                else
                {
                    List<BBEventItem> eventList = new List<BBEventItem>();
                    eventList.Add(evtItem);
                    evtDic.Add(evtItem.black_block_id, eventList);
                }
            }
            return evtDic;
        }

        public void setEventInfo(BBSummaryItem summary, List<BBEventItem> eventList, string token, string cityName)
        {
            Dictionary<int, int> allEventDic = new Dictionary<int, int>();      //<eventID, eventCount>
            Dictionary<int, int> lastTestEventDic = new Dictionary<int, int>();   //<eventID, eventCount>，最后一个测试天对应的事件，如正常，则无事件

            foreach (BBEventItem item in eventList)
            {
                if (item.cityName == cityName && item.token == token)
                {
                    addAllEventDic(allEventDic, item);

                    addLastTestEventDic(summary, lastTestEventDic, item);
                }
            }

            summary.eventInfo = getEventInfos(allEventDic);
            summary.eventLast = getEventInfos(lastTestEventDic);
        }

        private static void addAllEventDic(Dictionary<int, int> allEventDic, BBEventItem item)
        {
            if (allEventDic.ContainsKey(item.event_id))
            {
                allEventDic[item.event_id]++;
            }
            else
            {
                allEventDic.Add(item.event_id, 1);
            }
        }

        private static void addLastTestEventDic(BBSummaryItem summary, Dictionary<int, int> lastTestEventDic, BBEventItem item)
        {
            if ((item.timevalue - item.timevalue % 86400 - 28800) == summary.last_test_date)
            {
                if (lastTestEventDic.ContainsKey(item.event_id))
                {
                    lastTestEventDic[item.event_id]++;
                }
                else
                {
                    lastTestEventDic.Add(item.event_id, 1);
                }
            }
        }

        /// <summary>
        /// [弱覆盖路段_150米_低于负100dBm] 1个|[FTP下载低速率_150米_低于4Mbps] 1个
        /// </summary>
        /// <param name="evtDic"></param>
        /// <returns></returns>
        private string getEventInfos(Dictionary<int, int> evtDic)
        {
            StringBuilder allInfo = new StringBuilder();
            foreach (int evtID in evtDic.Keys)
            {
                string evtName = getEventName(evtID);
                allInfo.Append("[" + evtName + "]" + " " + evtDic[evtID].ToString() + "个" + "|");
            }

            return allInfo.ToString();
        }

        private string getEventName(int evtID)
        {
            EventInfoManager emgr = EventInfoManager.GetInstance();
            foreach (EventInfo info in emgr.EventInfos)
            {
                if (evtID > 0 && info.ID == evtID)
                {
                    return info.Name;
                }
            }
            return "事件ID_" + evtID.ToString();
        }

        private void add2ResultDic(string cityName, BlackBlockToken token, BBStatItem bbStatItem, List<BBSummaryItem> bbSummaryList)
        {
            BBCityTokenItem cityToken = new BBCityTokenItem();
            cityToken.CityName = cityName;
            cityToken.Token = token.Token;
            cityToken.TokenName = token.Name;

            if (!resultStatDic.ContainsKey(cityToken))
            {
                resultStatDic.Add(cityToken, bbStatItem);
            }

            if (!resultSummaryDic.ContainsKey(cityToken))
            {
                resultSummaryDic.Add(cityToken, bbSummaryList);
            }
        }

        private List<List<BBDetailItem>> getSheetLists(out DataTable dt)
        {
            //最终结果为每个token一个sheet，需要对原有数据进行重新组织
            Dictionary<string, List<BBDetailItem>> tokenDic = new Dictionary<string, List<BBDetailItem>>();

            foreach (BBCityTokenItem cityTokenItem in resultSummaryDic.Keys)
            {
                foreach (BBSummaryItem summary in resultSummaryDic[cityTokenItem])
                {
                    if (!tokenDic.ContainsKey(cityTokenItem.Token))
                    {
                        List<BBDetailItem> tokenList = new List<BBDetailItem>();
                        BBDetailItem row = getContentRow(cityTokenItem.CityName, 1, summary);
                        row.SetToken(cityTokenItem.Token, cityTokenItem.TokenName);
                        tokenList.Add(row);
                        tokenDic.Add(cityTokenItem.Token, tokenList);
                    }
                    else
                    {
                        int listCount = tokenDic[cityTokenItem.Token].Count + 1;
                        BBDetailItem row = getContentRow(cityTokenItem.CityName, listCount, summary);
                        row.SetToken(cityTokenItem.Token, cityTokenItem.TokenName);
                        tokenDic[cityTokenItem.Token].Add(row);
                    }
                }
            }

            //生成统计结果
            dt = getStatList();

            //生成最终结果
            List<List<BBDetailItem>> resultList = new List<List<BBDetailItem>>();
            foreach (string token in tokenDic.Keys)
            {
                resultList.Add(tokenDic[token]);
            }

            return resultList;
        }

        private BBDetailItem getContentRow(string cityName, int listCount, BBSummaryItem summary)
        {
            BBDetailItem row = new BBDetailItem();

            row.SN = listCount.ToString();
            row.CityName = cityName;
            row.BlackID = summary.id.ToString();
            row.Branch = getBranchGroupInfo(summary.area_names);
            row.Name = summary.name;
            row.WrongDays = summary.abnormal_days.ToString();
            row.Days = summary.normal_days.ToString();
            row.EventsNum = summary.abnormal_event_count.ToString();

            if (summary.status == 2 || summary.status == 3)
            {
                row.BlackStatus = "已建立";
            }
            else if (summary.status == 4)
            {
                row.BlackStatus = "已关闭";
            }

            row.Longitude = summary.midLong;
            row.Latitude = summary.midLat;

            if (summary.road_names == "无道路信息")
            {
                row.Location = "";
            }
            else
            {
                row.Location = summary.road_names;
            }
            row.Network = summary.griddesc;
            row.Reason = summary.reason;
            row.CreateDate = JavaDate.GetDateTimeFromMilliseconds(summary.created_date * 1000L).ToString("yyyy/MM/dd");
            row.DiscoverTime = JavaDate.GetDateTimeFromMilliseconds(summary.discoverTime * 1000L).ToString("yyyy/MM/dd");

            if (summary.closed_date > 0 && summary.status == 4)  //必须加上等于4的限制，因为存在已经关闭的黑点被打开的情况，其中的closedata有数据
            {
                row.CloseDate = JavaDate.GetDateTimeFromMilliseconds(summary.closed_date * 1000L).ToString("yyyy/MM/dd");
            }
            else
            {
                row.CloseDate = "";
            }

            row.BeginWrongDate = JavaDate.GetDateTimeFromMilliseconds(summary.first_abnormal_date * 1000L).ToString("yyyy/MM/dd");
            row.EndWrongDate = JavaDate.GetDateTimeFromMilliseconds(summary.last_abnormal_date * 1000L).ToString("yyyy/MM/dd");
            row.EndTestDate = JavaDate.GetDateTimeFromMilliseconds(summary.last_test_date * 1000L).ToString("yyyy/MM/dd");
            row.EventDetail = summary.eventInfo;
            row.IsChecked = "是";             //是否考核，全部填写为是
            row.TestDay = summary.gooddays_count.ToString();

            if (summary.last_validate_date > 0)
            {
                row.TestDate = JavaDate.GetDateTimeFromMilliseconds(summary.last_validate_date * 1000L).ToString("yyyy/MM/dd");
            }
            else
            {
                row.TestDate = "";
            }

            switch (summary.validate_status)
            {
                case 0:
                    row.TestResult = "未验证测试";
                    break;
                case 1:
                    row.TestResult = "预关闭";
                    break;
                case 2:
                    row.TestResult = "验证测试异常";
                    break;
                case 3:
                    row.TestResult = "预关闭后异常";
                    break;
                default:
                    row.TestResult = "";
                    break;
            }

            row.Events = summary.eventLast;
            row.CellName = summary.cell_names;
            row.Building = "";     //周边建筑

            return row;
        }

        private string getBranchGroupInfo(string area_names)
        {
            string branch = "";
            string group = "";
            if (string.IsNullOrEmpty(area_names))
            {
                branch = string.Empty;
                group = string.Empty;
            }
            else
            {
                string[] arr = area_names.Split('|');
                if (arr.Length == 0)
                {
                    branch = string.Empty;
                    group = string.Empty;
                }
                for (int i = 0; i < arr.Length; i++)
                {
                    string temp = arr[i];
                    if (!string.IsNullOrEmpty(temp) && !temp.Contains("中心点坐标"))
                    {
                        string[] branchArr = temp.Split('_');
                        if (branchArr.Length == 2)
                        {
                            branch = branchArr[0];
                            int tempIdx = branch.IndexOf(',');
                            if (tempIdx != -1)
                            {
                                branch = branch.Substring(tempIdx + 1);
                            }
                            group = branchArr[1];
                        }
                        else
                        {
                            branch = temp;
                            group = string.Empty;
                        }
                        break;
                    }
                }
            }

            return branch;
        }

        /// <summary>
        /// 汇总结果统计
        /// </summary>
        /// <param name="countDic"></param>
        /// <returns></returns>
        private DataTable getStatList()
        {
            DataTable dt = new DataTable();
            List<string> tokenList = new List<string>();
            //每个地市
            Dictionary<string, Dictionary<string, BBStatItem>> cityDicAll = new Dictionary<string, Dictionary<string, BBStatItem>>();   //统计每个地市下对应token下面各状态黑点数量
            Dictionary<string, BBStatItem> totalDicAll = new Dictionary<string, BBStatItem>();     //统计全省所有地市对应token下面各状态黑点数量

            dt.Columns.Add("地市");
            foreach (BBCityTokenItem item in resultStatDic.Keys)
            {
                if (!tokenList.Contains(item.Token))
                {
                    tokenList.Add(item.Token);
                    dt.Columns.Add(item.Token + "总数");
                    dt.Columns.Add(item.Token + "新增数");
                    dt.Columns.Add(item.Token + "解决数");
                }
            }
            dt.Columns.Add("汇总总数");
            dt.Columns.Add("汇总新增数");
            dt.Columns.Add("汇总解决数");
            dt.Columns.Add("汇总解决率");

            dealResultStatDic(cityDicAll, totalDicAll);

            addCityData(dt, cityDicAll);

            //汇总
            DataRow drAll = getAllData(dt, totalDicAll);
            dt.Rows.Add(drAll);
            return dt;
        }

        private void dealResultStatDic(Dictionary<string, Dictionary<string, BBStatItem>> cityDicAll, Dictionary<string, BBStatItem> totalDicAll)
        {
            foreach (BBCityTokenItem item in resultStatDic.Keys)
            {
                if (!cityDicAll.ContainsKey(item.CityName))
                {
                    Dictionary<string, BBStatItem> dic = new Dictionary<string, BBStatItem>();
                    BBStatItem statItem = new BBStatItem();
                    statItem.bbAdded = resultStatDic[item].bbAdded;
                    statItem.bbClosed = resultStatDic[item].bbClosed;
                    statItem.bbTotal = resultStatDic[item].bbTotal;
                    dic.Add(item.Token, statItem);
                    cityDicAll.Add(item.CityName, dic);
                }
                else
                {
                    if (!cityDicAll[item.CityName].ContainsKey(item.Token))
                    {
                        BBStatItem statIem = new BBStatItem();
                        statIem.bbAdded = resultStatDic[item].bbAdded;
                        statIem.bbClosed = resultStatDic[item].bbClosed;
                        statIem.bbTotal = resultStatDic[item].bbTotal;
                        cityDicAll[item.CityName].Add(item.Token, statIem);
                    }
                    else
                    {
                        cityDicAll[item.CityName][item.Token].bbAdded = resultStatDic[item].bbAdded;
                        cityDicAll[item.CityName][item.Token].bbClosed = resultStatDic[item].bbClosed;
                        cityDicAll[item.CityName][item.Token].bbTotal = resultStatDic[item].bbTotal;
                    }
                }

                BBStatItem allItem = null;
                if (!totalDicAll.TryGetValue(item.Token, out allItem))
                {
                    allItem = new BBStatItem();
                    totalDicAll[item.Token] = allItem;
                }
                allItem.bbAdded += resultStatDic[item].bbAdded;
                allItem.bbClosed += resultStatDic[item].bbClosed;
                allItem.bbTotal += resultStatDic[item].bbTotal;
            }
        }

        private void addCityData(DataTable dt, Dictionary<string, Dictionary<string, BBStatItem>> cityDicAll)
        {
            foreach (string cityName in cityDicAll.Keys)
            {
                int totalNum = 0;
                int addNum = 0;
                int closeNum = 0;
                DataRow dr = dt.NewRow();
                dr["地市"] = cityName;
                foreach (string token in cityDicAll[cityName].Keys)
                {
                    dr[token + "总数"] = cityDicAll[cityName][token].bbTotal;
                    dr[token + "新增数"] = cityDicAll[cityName][token].bbAdded;
                    dr[token + "解决数"] = cityDicAll[cityName][token].bbClosed;
                    totalNum += cityDicAll[cityName][token].bbTotal;
                    addNum += cityDicAll[cityName][token].bbAdded;
                    closeNum += cityDicAll[cityName][token].bbClosed;
                }
                dr["汇总总数"] = totalNum;
                dr["汇总新增数"] = addNum;
                dr["汇总解决数"] = closeNum;
                dr["汇总解决率"] = (Math.Round((double)closeNum / (double)totalNum, 2) * 100).ToString() + "%";
                if (totalNum == 0 && addNum == 0 && closeNum == 0) continue;
                dt.Rows.Add(dr);
            }
        }

        private DataRow getAllData(DataTable dt, Dictionary<string, BBStatItem> totalDicAll)
        {
            int totalNumAll = 0;
            int addNumAll = 0;
            int closeNumAll = 0;
            DataRow drAll = dt.NewRow();
            drAll["地市"] = "全省";
            foreach (string token in totalDicAll.Keys)
            {
                drAll[token + "总数"] = totalDicAll[token].bbTotal;
                drAll[token + "新增数"] = totalDicAll[token].bbAdded;
                drAll[token + "解决数"] = totalDicAll[token].bbClosed;
                totalNumAll += totalDicAll[token].bbTotal;
                addNumAll += totalDicAll[token].bbAdded;
                closeNumAll += totalDicAll[token].bbClosed;
            }
            drAll["汇总总数"] = totalNumAll;
            drAll["汇总新增数"] = addNumAll;
            drAll["汇总解决数"] = closeNumAll;
            if (totalNumAll == 0)
            {
                drAll["汇总解决率"] = "";
            }
            else
            {
                drAll["汇总解决率"] = (Math.Round((double)closeNumAll / (double)totalNumAll, 2) * 100).ToString() + "%";
            }

            return drAll;
        }

        protected virtual void showResultForm(List<List<BBDetailItem>> resultList, DataTable dt)
        {
            BlackBlockCalculateResultForm frm = MainModel.CreateResultForm(typeof(BlackBlockCalculateResultForm)) as BlackBlockCalculateResultForm;
            frm.FillData(resultList, dt);
            frm.Visible = true;
            frm.BringToFront();

            resultSummaryDic = null;
            resultStatDic = null;
            bbStatCondition = null;
        }
    }
}
