﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;

using MasterCom.RAMS.Func.SystemSetting;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class CellSetByBackgroundProperties : PropertiesControl
    {
        private CellSetByBackgroundQuery queryFunc;

        public CellSetByBackgroundProperties(CellSetByBackgroundQuery queryFunc)
        {
            InitializeComponent();
            cbxBandType.Properties.Items.Clear();
            cbxBandType.Properties.Items.Add(BTSBandType.GSM900.ToString());
            cbxBandType.Properties.Items.Add(BTSBandType.DSC1800.ToString());
            cbxBandType.Properties.Items.Add(BTSBandType.CoSite.ToString());
            cbxBandType.SelectedIndex = 0;
            this.queryFunc = queryFunc;
        }

        public override string ParentName
        {
            get { return queryFunc.FuncType.ToString(); }
        }

        public override string ParentSubName
        {
            get { return queryFunc.SubFuncType.ToString(); }
        }

        public override string SelfName
        {
            get { return queryFunc.Name; }
        }

        public override string TabPageName
        {
            get { return queryFunc.Name; }
        }

        public override void Flush()
        {
            chkBackgroundStat.Checked = queryFunc.BackgroundStat;
            numMinRxLev.Value = (decimal)queryFunc.RxlevMin;
            int idx = cbxBandType.Properties.Items.IndexOf(queryFunc.bandType.ToString());
            if (idx != -1)
            {
                cbxBandType.SelectedIndex = idx;
            }
        }

        public override bool IsValid()
        {
            return true;
        }

        public override void Apply()
        {
            queryFunc.BackgroundStat = chkBackgroundStat.Checked;
            queryFunc.RxlevMin = (double)numMinRxLev.Value;
            queryFunc.bandType = (BTSBandType)Enum.Parse(typeof(BTSBandType), cbxBandType.SelectedItem as string);
        }
    }
}
