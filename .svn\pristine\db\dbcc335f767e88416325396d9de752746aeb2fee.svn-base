﻿using System;
using System.Collections.Generic;
using MasterCom.Util;
using MasterCom.RAMS.Func.CoverageCheck;
using MasterCom.RAMS.Chris.Util;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc;

namespace MasterCom.RAMS
{
    public class CompareMode
    {
        public string Path { get; private set; }

        public Dictionary<string, CompareParam> compareConfigDic { get; set; }

        public CompareMode(string path)
        {
            compareConfigDic = new Dictionary<string, CompareParam>();

            this.Path = path;
        }

        public List<CompareParam> CompareConfigList
        {
            get
            {
                List<CompareParam> compareParamList = new List<CompareParam>(compareConfigDic.Values);
                compareParamList.Sort(delegate(CompareParam a, CompareParam b) { return a.sn - b.sn; });
                return compareParamList;
            }
        }

        public bool loadConfig()
        {
            try
            {
                compareConfigDic.Clear();
                XmlConfigFile configFile = new XmlConfigFile(Path);
                Dictionary<string, object> newDic = configFile.GetItemValue("Configs", "CompareConfig") as Dictionary<string, object>;
                if (newDic != null)
                {
                    this.Param = newDic;
                }
            }
            catch
            {
                return false;
            }
            return true;
        }

        public bool saveConfig()
        {
            try
            {
                XmlConfigFile xmlconfigfile = new XmlConfigFile();
                System.Xml.XmlElement cfg = xmlconfigfile.AddConfig("Configs");
                xmlconfigfile.AddItem(cfg, "CompareConfig", this.Param);
                xmlconfigfile.Save(Path);
            }
            catch
            {
                return false;
            }
            return true;
        }

        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                Dictionary<string, object> odic = new Dictionary<string, object>();
                foreach (string key in compareConfigDic.Keys)
                {
                    odic[key] = compareConfigDic[key].Param;
                }
                param["compareParamConfigDic"] = odic;
                return param;
            }
            set
            {
                Dictionary<string, CompareParam> compareConfigXml = new Dictionary<string, CompareParam>();
                Dictionary<string, object> odic = (Dictionary<string, object>)value["compareParamConfigDic"];
                foreach (string key in odic.Keys)
                {
                    CompareParam cpParam = new CompareParam();
                    cpParam.Param = (Dictionary<string,object>)odic[key];
                    compareConfigXml[key] = cpParam;
                }
                compareConfigDic = compareConfigXml;
            }
        }
    }

    public class CompareParam
    {
        public int sn { get; set; }
        public string name { get; set; }

        public List<int> serviceList_A { get; set; }
        public int carrier_A { get; set; }
        public string formula_A { get; set; }
        public bool isLimit_A { get; set; }
        public Range Range_A { get; set; }
        public bool judgeByBndOr { get; set; }
        public List<CompareDisplayColumn> displayColumnList_A { get; set; }

        public List<int> serviceList_B { get; set; }
        public int carrier_B { get; set; }
        public string formula_B { get; set; }
        public bool isLimit_B { get; set; }
        public Range Range_B { get; set; }
        public List<CompareDisplayColumn> displayColumnList_B { get; set; }

        public CPModeAlgorithmItem AlgorithmCfg { get; set; }

        public CompareParam()
        {
            displayColumnList_A = new List<CompareDisplayColumn>();
            displayColumnList_B = new List<CompareDisplayColumn>();
            serviceList_A = new List<int>();
            serviceList_B = new List<int>();
            AlgorithmCfg = new CPModeAlgorithmItem();
        }

        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["sn"] = sn;
                param["name"] = name;

                List<object> serviceAList = new List<object>();
                foreach (int isa in serviceList_A)
                {
                    serviceAList.Add((object)isa);
                }

                List<object> dispalyList_A = new List<object>();
                foreach (CompareDisplayColumn col in displayColumnList_A)
                {
                    dispalyList_A.Add(col.CfgParam);
                }

                param["serviceList_A"] = serviceAList;
                param["carrier_A"] = carrier_A;
                param["formula_A"] = formula_A;
                param["isLimit_A"] = isLimit_A;
                param["Range_A"] = Range_A.Param;
                param["judgeByBndOr"] = judgeByBndOr;
                param["displayColumnList_A"] = dispalyList_A;

                List<object> serviceBList = new List<object>();
                foreach (int isb in serviceList_B)
                {
                    serviceBList.Add((object)isb);
                }
                List<object> dispalyList_B = new List<object>();
                foreach (CompareDisplayColumn col in displayColumnList_B)
                {
                    dispalyList_B.Add(col.CfgParam);
                }
                param["serviceList_B"] = serviceBList;
                param["carrier_B"] = carrier_B;
                param["formula_B"] = formula_B;
                param["isLimit_B"] = isLimit_B;
                param["Range_B"] = Range_B.Param;
                param["displayColumnList_B"] = dispalyList_B;

                param["algorithmName"] = AlgorithmCfg.name;
                List<object> algorithmList = new List<object>();
                foreach (CPModeColorItem colorItem in AlgorithmCfg.colorItemList)
                {
                    Dictionary<string, object> agthmDic = colorItem.Param;
                    algorithmList.Add(agthmDic);
                }
                param["colorItems"] = algorithmList;



                List<object> bothStandardList = new List<object>();
                foreach (CPModeColorItem standardItem in AlgorithmCfg.bothStandardList)
                {
                    Dictionary<string, object> standardDic = standardItem.Param;
                    bothStandardList.Add(standardDic);
                }
                param["bothStandards"] = bothStandardList;


                List<object> specialList = new List<object>();
                foreach (TextColorRange tcRange in AlgorithmCfg.specialColorList)
                {
                    Dictionary<string, object> specialDic = tcRange.Param;
                    specialDic["Visible"] = tcRange.Visible;
                    specialList.Add(specialDic);
                }
                param["specials"] = specialList;
                return param;
            }

            set
            {
                sn = (int)value["sn"];
                name = (string)value["name"];
                setDisplayA(value);

                setDisPlayB(value);

                AlgorithmCfg.name = (string)value["algorithmName"];
                List<object> colorItems = value["colorItems"] as List<object>;
                AlgorithmCfg.colorItemList.Clear();
                foreach (object item in colorItems)
                {
                    Dictionary<string, object> itemDic = item as Dictionary<string, object>;
                    CPModeColorItem cpColorItem = new CPModeColorItem();
                    AlgorithmCfg.colorItemList.Add(cpColorItem);
                    cpColorItem.Param = itemDic;
                }
                List<object> bothStandardList = !value.ContainsKey("bothStandards") ? new List<object>() : value["bothStandards"] as List<object>;
                AlgorithmCfg.bothStandardList.Clear();
                foreach (object bothStandard in bothStandardList)
                {
                    Dictionary<string, object> itemDic = bothStandard as Dictionary<string, object>;
                    CPModeColorItem cpStandardItem = new CPModeColorItem();
                    AlgorithmCfg.bothStandardList.Add(cpStandardItem);
                    cpStandardItem.Param = itemDic;
                }
                List<object> specialList = value["specials"] as List<object>;
                AlgorithmCfg.specialColorList.Clear();
                foreach (object special in specialList)
                {
                    Dictionary<string, object> speDic = special as Dictionary<string, object>;
                    TextColorRange tcRange = new TextColorRange();
                    AlgorithmCfg.specialColorList.Add(tcRange);
                    tcRange.Param = speDic;
                    tcRange.Visible = (bool)speDic["Visible"];
                }
            }
        }

        private void setDisPlayB(Dictionary<string, object> value)
        {
            try
            {
                List<object> displayList_B = value["displayColumnList_B"] as List<object>;
                displayColumnList_B.Clear();
                foreach (object display in displayList_B)
                {
                    CompareDisplayColumn CPCol = new CompareDisplayColumn();
                    CPCol.CfgParam = display as Dictionary<string, object>;
                    displayColumnList_B.Add(CPCol);
                }
            }
            catch
            {
                //continue
            }

            List<object> servBList = value["serviceList_B"] as List<object>;
            serviceList_B.Clear();
            foreach (object oServB in servBList)
            {
                serviceList_B.Add(Convert.ToInt32(oServB));
            }
            carrier_B = (int)value["carrier_B"];
            formula_B = (string)value["formula_B"];
            isLimit_B = (bool)value["isLimit_B"];

            Range_B = new Range(0, false, 100, true);
            if (value.ContainsKey("Range_B"))
                Range_B.Param = (Dictionary<string, object>)value["Range_B"];
            else
            {
                Range_B = new Range((double)value["minValue_B"], (bool)value["minValueInclude_B"], (double)value["maxValue_B"], (bool)value["maxValueInclude_B"]);
            }
        }

        private void setDisplayA(Dictionary<string, object> value)
        {
            try
            {
                List<object> displayList_A = value["displayColumnList_A"] as List<object>;
                displayColumnList_A.Clear();
                foreach (object display in displayList_A)
                {
                    CompareDisplayColumn CPCol = new CompareDisplayColumn();
                    CPCol.CfgParam = display as Dictionary<string, object>;
                    displayColumnList_A.Add(CPCol);
                }
            }
            catch
            {
                //continue
            }
            List<object> servAList = value["serviceList_A"] as List<object>;
            serviceList_A.Clear();
            foreach (object oservB in servAList)
            {
                serviceList_A.Add(Convert.ToInt32(oservB));
            }
            carrier_A = (int)value["carrier_A"];
            formula_A = (string)value["formula_A"];
            isLimit_A = (bool)value["isLimit_A"];

            Range_A = new Range(0, false, 100, true);
            if (value.ContainsKey("Range_A"))
                Range_A.Param = (Dictionary<string, object>)value["Range_A"];
            else
            {
                Range_A = new Range((double)value["minValue_A"], (bool)value["minValueInclude_A"], (double)value["maxValue_A"], (bool)value["maxValueInclude_A"]);
            }
            judgeByBndOr = true;
            if (value.ContainsKey("judgeByBndOr"))
            {
                judgeByBndOr = (bool)value["judgeByBndOr"];
            }
        }

        public TextColorRange GetTextColorRange(GridFormula gridHost, GridFormula gridGuest, ref double dHost, ref double dGuest)
        {
            if (gridHost != null)
            {
                dHost = gridHost.GetValue(GridFormula.strFormula);
            }
            if (gridGuest != null)
            {
                dGuest = gridGuest.GetValue(GridFormula.strFormula);
            }

            return GetTextColorRange(ref dHost, ref dGuest);
        }

        public TextColorRange GetTextColorRange(ref double dHost, ref double dGuest)
        {
            if (double.IsNaN(dHost) && double.IsNaN(dGuest))
                return AlgorithmCfg.GetSpecialColor(CPModeEditForm.OTHERS);

            return AlgorithmCfg.GetTextColorRange(ref dHost, ref dGuest, isLimit_A, isLimit_B, Range_A, Range_B, judgeByBndOr);
        } 

        public bool IsValid(double dValue, bool bHost)
        {
            if (bHost)
            {
                return Range_A.Contains(dValue);
            }
            else
            {
                return Range_B.Contains(dValue);
            }
        }

        internal bool AddHostDisplayColumn(CompareDisplayColumn col)
        {
            CompareDisplayColumn existCol = displayColumnList_A.Find(
            delegate(CompareDisplayColumn c) { return c.Caption.Equals(col.Caption); });
            if (existCol != null)
            {
                return false;
            }
            else
            {
                displayColumnList_A.Add(col);
                return true;
            }
        }
        internal bool AddGuestDisplayColumn(CompareDisplayColumn col)
        {
            CompareDisplayColumn existCol = displayColumnList_B.Find(
            delegate(CompareDisplayColumn c) { return c.Caption.Equals(col.Caption); });
            if (existCol != null)
            {
                return false;
            }
            else
            {
                displayColumnList_B.Add(col);
                return true;
            }
        }
        internal CompareDisplayColumn GetGuestDisColByHost(CompareDisplayColumn col)
        {
            CompareDisplayColumn colGuest = displayColumnList_B.Find(
            delegate(CompareDisplayColumn c) { return c.Caption.Equals(col.Caption); });

            return colGuest;
        }

        public override string ToString()
        {
            return name;
        }
    }

    public class CPModeAlgorithmItem
    {
        public string name { get; set; }
        public List<CPModeColorItem> colorItemList { get; set; }
        public List<TextColorRange> specialColorList { get; set; }
        public List<CPModeColorItem> bothStandardList { get; set; }

        public CPModeAlgorithmItem()
        {
            colorItemList = new List<CPModeColorItem>();
            specialColorList = new List<TextColorRange>();
            bothStandardList = new List<CPModeColorItem>();
            initColorItemList();
            initSpecialList();
        }

        private void initColorItemList()
        {
            CPModeColorItem colorGreat = new CPModeColorItem();
            colorGreat.colorRange.description = "好";
            colorGreat.colorRange.color = System.Drawing.Color.Green;
            colorGreat.colorRange.Visible = true;
            colorGreat.range = new Range(10, true, 100, true);
            colorGreat.InitType0();
            colorItemList.Add(colorGreat);

            CPModeColorItem colorMid = new CPModeColorItem();
            colorMid.colorRange.description = "中";
            colorMid.colorRange.color = System.Drawing.Color.Yellow;
            colorMid.colorRange.Visible = true;
            colorMid.range = new Range(0, true, 10, false);
            colorMid.InitType0();
            colorItemList.Add(colorMid);

            CPModeColorItem colorWorse = new CPModeColorItem();
            colorWorse.colorRange.description = "差";
            colorWorse.colorRange.color = System.Drawing.Color.Red;
            colorWorse.colorRange.Visible = true;
            colorWorse.range = new Range(-100, true, 0, false);
            colorWorse.InitType0();
            colorItemList.Add(colorWorse);
        }

        private void initSpecialList()
        {
            TextColorRange tcRangeHost = new TextColorRange();
            tcRangeHost.color = System.Drawing.Color.LightGray;
            tcRangeHost.description = CPModeEditForm.GUESTNULL;
            tcRangeHost.Visible = true;
            specialColorList.Add(tcRangeHost);

            TextColorRange tcRangeGuest = new TextColorRange();
            tcRangeGuest.color = System.Drawing.Color.DarkGray;
            tcRangeGuest.description = CPModeEditForm.HOSTNULL;
            tcRangeGuest.Visible = true;
            specialColorList.Add(tcRangeGuest);

            TextColorRange tcRangeOthers = new TextColorRange();
            tcRangeOthers.color = System.Drawing.Color.Black;
            tcRangeOthers.description = CPModeEditForm.OTHERS;
            tcRangeOthers.Visible = true;
            specialColorList.Add(tcRangeOthers);
        }

        public TextColorRange GetSpecialColor(string key)
        {
            TextColorRange rtColor = null;
            foreach (TextColorRange color in specialColorList)
            {
                if (color.description == key)
                {
                    rtColor = color;
                    break;
                }
            }
            return rtColor;
        }

        private TextColorRange getColor(double dHost, double dGuest, List<CPModeColorItem> cpModeLst)
        {
            TextColorRange rtColor = null;
            foreach (CPModeColorItem item in cpModeLst)
            {
                if (item.BInvalid(dHost, dGuest))
                {
                    rtColor = item.colorRange;
                    break;
                }
            }
            return rtColor;
        }

        public TextColorRange GetTextColorRange(ref double dHost, ref double dGuest, bool isLimit_A, bool isLimit_B, Range Range_A, Range Range_B, bool judgeByBndOr)
        {
            if (double.IsNaN(dHost) || (judgeByBndOr && isLimit_A && !Range_A.Contains(dHost)))
            {
                dHost = double.NaN;
                return GetSpecialColor(CPModeEditForm.HOSTNULL);
            }
            else if (double.IsNaN(dGuest) || (judgeByBndOr && isLimit_B && !Range_B.Contains(dGuest)))
            {
                dGuest = double.NaN;
                return GetSpecialColor(CPModeEditForm.GUESTNULL);
            }

            TextColorRange rtColor = getColor(dHost, dGuest, bothStandardList);

            if (rtColor == null)
                rtColor = getColor(dHost, dGuest, colorItemList);

            if (rtColor == null)
                rtColor = GetSpecialColor(CPModeEditForm.OTHERS);

            return rtColor;
        }
    }

    public class CPModeColorItem
    {
        public int CmpType { get; set; }// 0：主队-客队  1：均达标(免比)
        public float fHostMin { get; set; }
        public bool bHostMinInclude { get; set; }
        public float fGuestMin { get; set; }
        public bool bGuestMinInclude { get; set; }
        public Range range { get; set; }
        public TextColorRange colorRange { get; set; }

        public CPModeColorItem()
        {
            range = new Range(0, true, 0, true);
            colorRange = new TextColorRange();
            InitType0();
        }

        public void InitType0()
        {
            CmpType = (int)CPModeEditForm.ECmpType.HOST_SUB_GUEST;
            fGuestMin = 0;
            fHostMin = 0;
            bHostMinInclude = true;
            bGuestMinInclude = true;
        }

        public bool BInvalid(double dHost, double dGuest)
        {
            if (CmpType == (int)CPModeEditForm.ECmpType.HOST_SUB_GUEST)
            {
                return range.Contains(dHost - dGuest);
            }
            return dHost >= fHostMin && (bHostMinInclude || dHost != fHostMin) &&
                dGuest >= fGuestMin && (bGuestMinInclude || dGuest != fGuestMin);
        }

        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = colorRange.Param;
                param["Visible"] = colorRange.Visible;
                param["minValue"] = range.Min;
                param["minValueInclude"] = range.MinIncluded;
                param["maxValue"] = range.Max;
                param["maxValueInclude"] = range.MaxIncluded;
                param["CmpType"] = CmpType;
                param["fHostMin"] = fHostMin;
                param["fGuestMin"] = fGuestMin;
                param["bHostMinInclde"] = bHostMinInclude;
                param["bGuestMinInclude"] = bGuestMinInclude;
                return param;
            }
            set
            {
                Dictionary<string, object> itemDic = value;

                range = new Range((double)itemDic["minValue"], (bool)itemDic["minValueInclude"],
                        (double)itemDic["maxValue"], (bool)itemDic["maxValueInclude"]);
                colorRange.Param = itemDic;
                colorRange.Visible = (bool)itemDic["Visible"];
                CmpType = !itemDic.ContainsKey("CmpType") ? (int)CPModeEditForm.ECmpType.HOST_SUB_GUEST : (int)itemDic["CmpType"];
                fHostMin = !itemDic.ContainsKey("fHostMin") ? 0 : (float)itemDic["fHostMin"];
                fGuestMin = !itemDic.ContainsKey("fGuestMin") ? 0 : (float)itemDic["fGuestMin"];
                bHostMinInclude = !itemDic.ContainsKey("bHostMinInclude") || (bool)itemDic["bHostMinInclude"];
                bGuestMinInclude = !itemDic.ContainsKey("bGuestMinInclude") || (bool)itemDic["bGuestMinInclude"];
            }
        }
    }



    public class CompareMode2
    {
        public string Path { get; private set; }

        public Dictionary<string, CompareParam2> compareConfigDic { get; set; }

        public CompareMode2(string path)
        {
            compareConfigDic = new Dictionary<string, CompareParam2>();

            this.Path = path;
        }

        public List<CompareParam2> CompareConfigList
        {
            get
            {
                List<CompareParam2> compareParamList = new List<CompareParam2>(compareConfigDic.Values);
                compareParamList.Sort(delegate (CompareParam2 a, CompareParam2 b) { return a.sn - b.sn; });
                return compareParamList;
            }
        }

        public bool loadConfig()
        {
            try
            {
                compareConfigDic.Clear();
                XmlConfigFile configFile = new XmlConfigFile(Path);
                Dictionary<string, object> newDic = configFile.GetItemValue("Configs", "CompareConfig") as Dictionary<string, object>;
                if (newDic != null)
                {
                    this.Param = newDic;
                }
            }
            catch
            {
                return false;
            }
            return true;
        }

        public bool saveConfig()
        {
            try
            {
                XmlConfigFile xmlconfigfile = new XmlConfigFile();
                System.Xml.XmlElement cfg = xmlconfigfile.AddConfig("Configs");
                xmlconfigfile.AddItem(cfg, "CompareConfig", this.Param);
                xmlconfigfile.Save(Path);
            }
            catch
            {
                return false;
            }
            return true;
        }

        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                Dictionary<string, object> odic = new Dictionary<string, object>();
                foreach (string key in compareConfigDic.Keys)
                {
                    odic[key] = compareConfigDic[key].Param;
                }
                param["compareParamConfigDic"] = odic;
                return param;
            }
            set
            {
                Dictionary<string, CompareParam2> compareConfigXml = new Dictionary<string, CompareParam2>();
                Dictionary<string, object> odic = (Dictionary<string, object>)value["compareParamConfigDic"];
                foreach (string key in odic.Keys)
                {
                    CompareParam2 cpParam = new CompareParam2();
                    cpParam.Param = (Dictionary<string, object>)odic[key];
                    compareConfigXml[key] = cpParam;
                }
                compareConfigDic = compareConfigXml;
            }
        }
    }


    public class CompareParam2
    {
        public int sn { get; set; }
        public string name { get; set; }

        public List<int> serviceList_A { get; set; }
        public int carrier_A { get; set; }
        public string formula_A { get; set; }
        public bool isLimit_A { get; set; }
        public Range Range_A { get; set; }
        public bool judgeByBndOr { get; set; }
        public List<CompareDisplayColumn> displayColumnList_A { get; set; }

        public List<int> serviceList_B { get; set; }
        public int carrier_B { get; set; }
        public string formula_B { get; set; }
        public bool isLimit_B { get; set; }
        public Range Range_B { get; set; }
        public List<CompareDisplayColumn> displayColumnList_B { get; set; }

        public CPModeAlgorithmItem2 AlgorithmCfg { get; set; }

        public CompareParam2()
        {
            displayColumnList_A = new List<CompareDisplayColumn>();
            displayColumnList_B = new List<CompareDisplayColumn>();
            serviceList_A = new List<int>();
            serviceList_B = new List<int>();
            AlgorithmCfg = new CPModeAlgorithmItem2();
        }

        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["sn"] = sn;
                param["name"] = name;

                List<object> serviceAList = new List<object>();
                foreach (int isa in serviceList_A)
                {
                    serviceAList.Add((object)isa);
                }

                List<object> dispalyList_A = new List<object>();
                foreach (CompareDisplayColumn col in displayColumnList_A)
                {
                    dispalyList_A.Add(col.CfgParam);
                }

                param["serviceList_A"] = serviceAList;
                param["carrier_A"] = carrier_A;
                param["formula_A"] = formula_A;
                param["isLimit_A"] = isLimit_A;
                param["Range_A"] = Range_A.Param;
                param["judgeByBndOr"] = judgeByBndOr;
                param["displayColumnList_A"] = dispalyList_A;

                List<object> serviceBList = new List<object>();
                foreach (int isb in serviceList_B)
                {
                    serviceBList.Add((object)isb);
                }
                List<object> dispalyList_B = new List<object>();
                foreach (CompareDisplayColumn col in displayColumnList_B)
                {
                    dispalyList_B.Add(col.CfgParam);
                }
                param["serviceList_B"] = serviceBList;
                param["carrier_B"] = carrier_B;
                param["formula_B"] = formula_B;
                param["isLimit_B"] = isLimit_B;
                param["Range_B"] = Range_B.Param;
                param["displayColumnList_B"] = dispalyList_B;

                param["algorithmName"] = AlgorithmCfg.name;
                List<object> algorithmList = new List<object>();
                foreach (CPModeColorItem2 colorItem in AlgorithmCfg.colorItemList)
                {
                    Dictionary<string, object> agthmDic = colorItem.Param;
                    algorithmList.Add(agthmDic);
                }
                param["colorItems"] = algorithmList;



                List<object> bothStandardList = new List<object>();
                foreach (CPModeColorItem2 standardItem in AlgorithmCfg.bothStandardList)
                {
                    Dictionary<string, object> standardDic = standardItem.Param;
                    bothStandardList.Add(standardDic);
                }
                param["bothStandards"] = bothStandardList;


                List<object> specialList = new List<object>();
                foreach (TextColorRange tcRange in AlgorithmCfg.specialColorList)
                {
                    Dictionary<string, object> specialDic = tcRange.Param;
                    specialDic["Visible"] = tcRange.Visible;
                    specialList.Add(specialDic);
                }
                param["specials"] = specialList;
                return param;
            }

            set
            {
                sn = (int)value["sn"];
                name = (string)value["name"];

                setDisplayA(value);

                setDisPlayB(value);

                AlgorithmCfg.name = (string)value["algorithmName"];
                List<object> colorItems = value["colorItems"] as List<object>;
                AlgorithmCfg.colorItemList.Clear();
                foreach (object item in colorItems)
                {
                    Dictionary<string, object> itemDic = item as Dictionary<string, object>;
                    CPModeColorItem2 cpColorItem = new CPModeColorItem2();
                    AlgorithmCfg.colorItemList.Add(cpColorItem);
                    cpColorItem.Param = itemDic;
                }
                List<object> bothStandardList = !value.ContainsKey("bothStandards") ? new List<object>() : value["bothStandards"] as List<object>;
                AlgorithmCfg.bothStandardList.Clear();
                foreach (object bothStandard in bothStandardList)
                {
                    Dictionary<string, object> itemDic = bothStandard as Dictionary<string, object>;
                    CPModeColorItem2 cpStandardItem = new CPModeColorItem2();
                    AlgorithmCfg.bothStandardList.Add(cpStandardItem);
                    cpStandardItem.Param = itemDic;
                }
                List<object> specialList = value["specials"] as List<object>;
                AlgorithmCfg.specialColorList.Clear();
                foreach (object special in specialList)
                {
                    Dictionary<string, object> speDic = special as Dictionary<string, object>;
                    TextColorRange tcRange = new TextColorRange();
                    AlgorithmCfg.specialColorList.Add(tcRange);
                    tcRange.Param = speDic;
                    tcRange.Visible = (bool)speDic["Visible"];
                }
            }
        }

        private void setDisPlayB(Dictionary<string, object> value)
        {
            try
            {
                List<object> displayList_B = value["displayColumnList_B"] as List<object>;
                displayColumnList_B.Clear();
                foreach (object display in displayList_B)
                {
                    CompareDisplayColumn CPCol = new CompareDisplayColumn();
                    CPCol.CfgParam = display as Dictionary<string, object>;
                    displayColumnList_B.Add(CPCol);
                }
            }
            catch
            {
                //continue
            }

            List<object> servBList = value["serviceList_B"] as List<object>;
            serviceList_B.Clear();
            foreach (object oServB in servBList)
            {
                serviceList_B.Add(Convert.ToInt32(oServB));
            }
            carrier_B = (int)value["carrier_B"];
            formula_B = (string)value["formula_B"];
            isLimit_B = (bool)value["isLimit_B"];

            Range_B = new Range(0, false, 100, true);
            if (value.ContainsKey("Range_B"))
                Range_B.Param = (Dictionary<string, object>)value["Range_B"];
            else
            {
                Range_B = new Range((double)value["minValue_B"], (bool)value["minValueInclude_B"], (double)value["maxValue_B"], (bool)value["maxValueInclude_B"]);
            }
        }

        private void setDisplayA(Dictionary<string, object> value)
        {
            try
            {
                List<object> displayList_A = value["displayColumnList_A"] as List<object>;
                displayColumnList_A.Clear();
                foreach (object display in displayList_A)
                {
                    CompareDisplayColumn CPCol = new CompareDisplayColumn();
                    CPCol.CfgParam = display as Dictionary<string, object>;
                    displayColumnList_A.Add(CPCol);
                }
            }
            catch
            {
                //continue
            }
            List<object> servAList = value["serviceList_A"] as List<object>;
            serviceList_A.Clear();
            foreach (object oservB in servAList)
            {
                serviceList_A.Add(Convert.ToInt32(oservB));
            }
            carrier_A = (int)value["carrier_A"];
            formula_A = (string)value["formula_A"];
            isLimit_A = (bool)value["isLimit_A"];

            Range_A = new Range(0, false, 100, true);
            if (value.ContainsKey("Range_A"))
                Range_A.Param = (Dictionary<string, object>)value["Range_A"];
            else
            {
                Range_A = new Range((double)value["minValue_A"], (bool)value["minValueInclude_A"], (double)value["maxValue_A"], (bool)value["maxValueInclude_A"]);
            }
            judgeByBndOr = true;
            if (value.ContainsKey("judgeByBndOr"))
            {
                judgeByBndOr = (bool)value["judgeByBndOr"];
            }
        }

        public TextColorRange GetTextColorRange(GridFormula gridHost, GridFormula gridGuest, ref double dHost, ref double dGuest)
        {
            if (gridHost != null)
            {
                dHost = gridHost.GetValue(GridFormula.strFormula);
            }
            if (gridGuest != null)
            {
                dGuest = gridGuest.GetValue(GridFormula.strFormula);
            }

            return GetTextColorRange(ref dHost, ref dGuest);
        }

        public TextColorRange GetTextColorRange(ref double dHost, ref double dGuest)
        {
            if (double.IsNaN(dHost) && double.IsNaN(dGuest))
                return AlgorithmCfg.GetSpecialColor(CPModeEditForm.OTHERS);

            return AlgorithmCfg.GetTextColorRange(ref dHost, ref dGuest, isLimit_A, isLimit_B, Range_A, Range_B, judgeByBndOr);
        }

        public bool IsValid(double dValue, bool bHost)
        {
            if (bHost)
            {
                return Range_A.Contains(dValue);
            }
            else
            {
                return Range_B.Contains(dValue);
            }
        }

        internal bool AddHostDisplayColumn(CompareDisplayColumn col)
        {
            CompareDisplayColumn existCol = displayColumnList_A.Find(
            delegate (CompareDisplayColumn c) { return c.Caption.Equals(col.Caption); });
            if (existCol != null)
            {
                return false;
            }
            else
            {
                displayColumnList_A.Add(col);
                return true;
            }
        }
        internal bool AddGuestDisplayColumn(CompareDisplayColumn col)
        {
            CompareDisplayColumn existCol = displayColumnList_B.Find(
            delegate (CompareDisplayColumn c) { return c.Caption.Equals(col.Caption); });
            if (existCol != null)
            {
                return false;
            }
            else
            {
                displayColumnList_B.Add(col);
                return true;
            }
        }
        internal CompareDisplayColumn GetGuestDisColByHost(CompareDisplayColumn col)
        {
            CompareDisplayColumn colGuest = displayColumnList_B.Find(
            delegate (CompareDisplayColumn c) { return c.Caption.Equals(col.Caption); });

            return colGuest;
        }

        public override string ToString()
        {
            return name;
        }
    }

    public class CPModeAlgorithmItem2
    {
        public string name { get; set; }
        public List<CPModeColorItem2> colorItemList { get; set; }
        public List<TextColorRange> specialColorList { get; set; }
        public List<CPModeColorItem2> bothStandardList { get; set; }

        public CPModeAlgorithmItem2()
        {
            colorItemList = new List<CPModeColorItem2>();
            specialColorList = new List<TextColorRange>();
            bothStandardList = new List<CPModeColorItem2>();
            initColorItemList();
            initSpecialList();
        }

        private void initColorItemList()
        {
            CPModeColorItem2 colorGreat = new CPModeColorItem2();
            colorGreat.colorRange.description = "好";
            colorGreat.colorRange.color = System.Drawing.Color.Green;
            colorGreat.colorRange.Visible = true;
            colorGreat.range = new Range(10, true, 100, true);
            colorGreat.InitType0();
            colorItemList.Add(colorGreat);

            CPModeColorItem2 colorMid = new CPModeColorItem2();
            colorMid.colorRange.description = "中";
            colorMid.colorRange.color = System.Drawing.Color.Yellow;
            colorMid.colorRange.Visible = true;
            colorMid.range = new Range(0, true, 10, false);
            colorMid.InitType0();
            colorItemList.Add(colorMid);

            CPModeColorItem2 colorWorse = new CPModeColorItem2();
            colorWorse.colorRange.description = "差";
            colorWorse.colorRange.color = System.Drawing.Color.Red;
            colorWorse.colorRange.Visible = true;
            colorWorse.range = new Range(-100, true, 0, false);
            colorWorse.InitType0();
            colorItemList.Add(colorWorse);
        }

        private void initSpecialList()
        {
            TextColorRange tcRangeHost = new TextColorRange();
            tcRangeHost.color = System.Drawing.Color.LightGray;
            tcRangeHost.description = CPModeEditForm.GUESTNULL;
            tcRangeHost.Visible = true;
            specialColorList.Add(tcRangeHost);

            TextColorRange tcRangeGuest = new TextColorRange();
            tcRangeGuest.color = System.Drawing.Color.DarkGray;
            tcRangeGuest.description = CPModeEditForm.HOSTNULL;
            tcRangeGuest.Visible = true;
            specialColorList.Add(tcRangeGuest);

            TextColorRange tcRangeOthers = new TextColorRange();
            tcRangeOthers.color = System.Drawing.Color.Black;
            tcRangeOthers.description = CPModeEditForm.OTHERS;
            tcRangeOthers.Visible = true;
            specialColorList.Add(tcRangeOthers);
        }

        public TextColorRange GetSpecialColor(string key)
        {
            TextColorRange rtColor = null;
            foreach (TextColorRange color in specialColorList)
            {
                if (color.description == key)
                {
                    rtColor = color;
                    break;
                }
            }
            return rtColor;
        }

        private TextColorRange getColor(double dHost, double dGuest, List<CPModeColorItem2> cpModeLst)
        {
            TextColorRange rtColor = null;
            foreach (CPModeColorItem2 item in cpModeLst)
            {
                if (item.BInvalid(dHost, dGuest))
                {
                    rtColor = item.colorRange;
                    break;
                }
            }
            return rtColor;
        }

        public TextColorRange GetTextColorRange(ref double dHost, ref double dGuest, bool isLimit_A, bool isLimit_B, Range Range_A, Range Range_B, bool judgeByBndOr)
        {
            if (double.IsNaN(dHost) || (judgeByBndOr && isLimit_A && !Range_A.Contains(dHost)))
            {
                dHost = double.NaN;
                return GetSpecialColor(CPModeEditForm.HOSTNULL);
            }
            else if (double.IsNaN(dGuest) || (judgeByBndOr && isLimit_B && !Range_B.Contains(dGuest)))
            {
                dGuest = double.NaN;
                return GetSpecialColor(CPModeEditForm.GUESTNULL);
            }

            TextColorRange rtColor = getColor(dHost, dGuest, bothStandardList);

            if (rtColor == null)
                rtColor = getColor(dHost, dGuest, colorItemList);

            if (rtColor == null)
                rtColor = GetSpecialColor(CPModeEditForm.OTHERS);

            return rtColor;
        }
    }

    public class CPModeColorItem2
    {
        public int CmpType { get; set; }// 0：主队-客队  1：均达标(免比)
        public float fHostMin { get; set; }
        public bool bHostMinInclude { get; set; }
        public float fGuestMin { get; set; }
        public bool bGuestMinInclude { get; set; }
        public Range range { get; set; }
        public TextColorRange colorRange { get; set; }

        public CPModeColorItem2()
        {
            range = new Range(0, true, 0, true);
            colorRange = new TextColorRange();
            InitType0();
        }

        public void InitType0()
        {
            CmpType = (int)CPModeEditForm.ECmpType.HOST_SUB_GUEST;
            fGuestMin = 0;
            fHostMin = 0;
            bHostMinInclude = true;
            bGuestMinInclude = true;
        }

        public bool BInvalid(double dHost, double dGuest)
        {
            if (CmpType == (int)CPModeEditForm.ECmpType.HOST_SUB_GUEST)
            {
                return range.Contains(dHost - dGuest);
            }
            return dHost >= fHostMin && (bHostMinInclude || dHost != fHostMin) &&
                dGuest >= fGuestMin && (bGuestMinInclude || dGuest != fGuestMin);
        }

        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = colorRange.Param;
                param["Visible"] = colorRange.Visible;
                param["minValue"] = range.Min;
                param["minValueInclude"] = range.MinIncluded;
                param["maxValue"] = range.Max;
                param["maxValueInclude"] = range.MaxIncluded;
                param["CmpType"] = CmpType;
                param["fHostMin"] = fHostMin;
                param["fGuestMin"] = fGuestMin;
                param["bHostMinInclde"] = bHostMinInclude;
                param["bGuestMinInclude"] = bGuestMinInclude;
                return param;
            }
            set
            {
                Dictionary<string, object> itemDic = value;

                range = new Range((double)itemDic["minValue"], (bool)itemDic["minValueInclude"],
                        (double)itemDic["maxValue"], (bool)itemDic["maxValueInclude"]);
                colorRange.Param = itemDic;
                colorRange.Visible = (bool)itemDic["Visible"];
                CmpType = !itemDic.ContainsKey("CmpType") ? (int)CPModeEditForm.ECmpType.HOST_SUB_GUEST : (int)itemDic["CmpType"];
                fHostMin = !itemDic.ContainsKey("fHostMin") ? 0 : (float)itemDic["fHostMin"];
                fGuestMin = !itemDic.ContainsKey("fGuestMin") ? 0 : (float)itemDic["fGuestMin"];
                bHostMinInclude = !itemDic.ContainsKey("bHostMinInclude") || (bool)itemDic["bHostMinInclude"];
                bGuestMinInclude = !itemDic.ContainsKey("bGuestMinInclude") || (bool)itemDic["bGuestMinInclude"];
            }
        }
    }



}
