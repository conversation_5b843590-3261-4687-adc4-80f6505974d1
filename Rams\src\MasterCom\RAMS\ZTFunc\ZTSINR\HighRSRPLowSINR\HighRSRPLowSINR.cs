﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.ZTFunc
{
    public class HighRSRPLowSINR
    {
        private readonly List<TestPoint> testPoints = new List<TestPoint>();
        public List<TestPoint> TestPoints
        {
            get { return testPoints; }
        }
        public int TestPointCount
        {
            get { return testPoints.Count; }
        }
        public double MidLng
        {
            get {
                double lng = double.NaN;
                if (TestPointCount>0)
                {
                    lng = testPoints[TestPointCount / 2].Longitude;
                }
                return lng;
            }
        }
        public double MidLat
        {
            get
            {
                double lat = double.NaN;
                if (TestPointCount > 0)
                {
                    lat = testPoints[TestPointCount / 2].Latitude;
                }
                return lat;
            }
        }
        public int SN
        {
            get;
            set;
        }
        private float minRsrp = float.MaxValue;
        public float MinRsrp
        {
            get { return minRsrp; }
        }
        private float maxRsrp = float.MinValue;
        public float MaxRsrp
        {
            get { return maxRsrp; }
        }
        private float sumRsrp = 0;
        public float AvgRsrp
        {
            get
            {
                float avg = float.NaN;
                if (testPoints.Count > 0)
                {
                    avg = (float)Math.Round(sumRsrp / testPoints.Count, 2);
                }
                return avg;
            }
        }

        private float minSinr = float.MaxValue;
        public float MinSinr
        {
            get { return minSinr; }
        }
        private float maxSinr = float.MinValue;
        public float MaxSinr
        {
            get { return maxSinr; }
        }
        private float sumSinr= 0;
        public float AvgSinr
        {
            get
            {
                float avg = float.NaN;
                if (testPoints.Count > 0)
                {
                    avg = (float)Math.Round(sumSinr / testPoints.Count, 2);
                }
                return avg;
            }
        }

        public double StaySecond
        {
            get {
                double sec = 0;
                if (testPoints.Count>1)
                {
                    sec = (testPoints[testPoints.Count - 1].DateTime - testPoints[0].DateTime).TotalSeconds;
                }
                return sec;
            }
        }

        private double stayDistance = 0;
        public double StayDistance
        {
            get
            {
                return stayDistance;
            }
        }

        public string FileName
        {
            get {
                string name = string.Empty;
                if (testPoints.Count>0)
                {
                    name = testPoints[0].FileName;
                }
                return name;
            }
        }

        private string streetNames = null;
        public string RoadName
        {
            get
            {
                return streetNames;
            }
        }

        public void FindRoadName()
        {
            if (testPoints.Count > 0)
            {
                List<double> lngs = new List<double>();
                List<double> lats = new List<double>();
                TestPoint tp = testPoints[0];
                lngs.Add(tp.Longitude);
                lats.Add(tp.Latitude);
                tp = testPoints[(testPoints.Count / 2)];
                lngs.Add(tp.Longitude);
                lats.Add(tp.Latitude);
                tp = testPoints[testPoints.Count - 1];
                lngs.Add(tp.Longitude);
                lats.Add(tp.Latitude);
                streetNames = GISManager.GetInstance().GetRoadPlaceDesc(lngs, lats);
            }
        }

        public void AddTestPoint(TestPoint tp, float? rsrp, float? sinr, double dis2LastTP)
        {
            testPoints.Add(tp);
            if (rsrp <= -10 && rsrp >= -141)
            {
                minRsrp = Math.Min(minRsrp, (float)rsrp);
                maxRsrp = Math.Max(maxRsrp, (float)rsrp);
                sumRsrp += (float)rsrp;
            }

            if (-50 <= sinr && sinr <= 50)
            {
                minSinr = Math.Min(minSinr, (float)sinr);
                maxSinr = Math.Max(maxSinr, (float)sinr);
                sumSinr += (float)sinr;
            }

            stayDistance += dis2LastTP;
        }

        public double Percent { get; set; }

        public BackgroundResult ConvertToBackgroundResult()
        {
            BackgroundResult bgResult = new BackgroundResult();
            bgResult.LongitudeStart = 0;
            bgResult.LatitudeStart = 0;
            bgResult.LongitudeEnd = 0;
            bgResult.LatitudeEnd = 0;
            bgResult.ISTime = 0;
            bgResult.IETime = 0;
            if (testPoints.Count > 0)
            {
                bgResult.FileID = testPoints[0].FileID;
                bgResult.FileName = testPoints[0].FileName;
                bgResult.LongitudeStart = testPoints[0].Longitude;
                bgResult.LatitudeStart = testPoints[0].Latitude;
                bgResult.LongitudeEnd = testPoints[testPoints.Count - 1].Longitude;
                bgResult.LatitudeEnd = testPoints[testPoints.Count - 1].Latitude;
                bgResult.ISTime = (int)(JavaDate.GetMilliseconds(testPoints[0].DateTime) / 1000);
                bgResult.IETime = (int)(JavaDate.GetMilliseconds(testPoints[testPoints.Count - 1].DateTime) / 1000);
            }
            bgResult.LongitudeMid = MidLng;
            bgResult.LatitudeMid = MidLat;
            bgResult.DistanceLast = StayDistance;
            bgResult.SampleCount = TestPointCount;
            bgResult.RxLevMean = AvgRsrp;
            bgResult.RxLevMin = MinRsrp;
            bgResult.RxLevMax = MaxRsrp;
            bgResult.RxQualMean = AvgSinr;
            bgResult.RxQualMin = MinSinr;
            bgResult.RxQualMax = MaxSinr;
            bgResult.RoadDesc = RoadName;

            bgResult.AddImageValue((float)Percent);

            return bgResult;
        }
    }
}
