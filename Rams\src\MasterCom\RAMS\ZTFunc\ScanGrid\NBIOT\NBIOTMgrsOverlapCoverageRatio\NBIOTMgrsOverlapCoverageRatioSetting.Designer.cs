﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class NbIotMgrsOverlapCoverageRatioSetting
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.numCoverage = new System.Windows.Forms.NumericUpDown();
            this.label3 = new System.Windows.Forms.Label();
            this.numMinRSRP = new System.Windows.Forms.NumericUpDown();
            this.label1 = new System.Windows.Forms.Label();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.numGridCount = new System.Windows.Forms.NumericUpDown();
            this.label4 = new System.Windows.Forms.Label();
            this.label7 = new System.Windows.Forms.Label();
            this.numRSRPDis = new System.Windows.Forms.NumericUpDown();
            ((System.ComponentModel.ISupportInitialize)(this.numCoverage)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMinRSRP)).BeginInit();
            this.groupBox1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numGridCount)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRSRPDis)).BeginInit();
            this.SuspendLayout();
            // 
            // numCoverage
            // 
            this.numCoverage.Location = new System.Drawing.Point(276, 57);
            this.numCoverage.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numCoverage.Name = "numCoverage";
            this.numCoverage.Size = new System.Drawing.Size(120, 21);
            this.numCoverage.TabIndex = 9;
            this.numCoverage.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numCoverage.Value = new decimal(new int[] {
            4,
            0,
            0,
            0});
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(192, 61);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(77, 12);
            this.label3.TabIndex = 8;
            this.label3.Text = "重叠覆盖度≥";
            // 
            // numMinRSRP
            // 
            this.numMinRSRP.Location = new System.Drawing.Point(276, 99);
            this.numMinRSRP.Maximum = new decimal(new int[] {
            1000,
            0,
            0,
            0});
            this.numMinRSRP.Minimum = new decimal(new int[] {
            1000,
            0,
            0,
            -2147483648});
            this.numMinRSRP.Name = "numMinRSRP";
            this.numMinRSRP.Size = new System.Drawing.Size(120, 21);
            this.numMinRSRP.TabIndex = 6;
            this.numMinRSRP.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numMinRSRP.Value = new decimal(new int[] {
            84,
            0,
            0,
            -2147483648});
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(204, 103);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(65, 12);
            this.label1.TabIndex = 5;
            this.label1.Text = "平均RSRP≥";
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.numGridCount);
            this.groupBox1.Controls.Add(this.label4);
            this.groupBox1.Controls.Add(this.label7);
            this.groupBox1.Controls.Add(this.numCoverage);
            this.groupBox1.Controls.Add(this.numRSRPDis);
            this.groupBox1.Controls.Add(this.label3);
            this.groupBox1.Controls.Add(this.numMinRSRP);
            this.groupBox1.Controls.Add(this.label1);
            this.groupBox1.Dock = System.Windows.Forms.DockStyle.Top;
            this.groupBox1.Location = new System.Drawing.Point(0, 0);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(620, 176);
            this.groupBox1.TabIndex = 12;
            this.groupBox1.TabStop = false;
            // 
            // numGridCount
            // 
            this.numGridCount.Location = new System.Drawing.Point(276, 15);
            this.numGridCount.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numGridCount.Name = "numGridCount";
            this.numGridCount.Size = new System.Drawing.Size(120, 21);
            this.numGridCount.TabIndex = 15;
            this.numGridCount.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numGridCount.Value = new decimal(new int[] {
            4,
            0,
            0,
            0});
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(180, 19);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(89, 12);
            this.label4.TabIndex = 14;
            this.label4.Text = "连续栅格个数≥";
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(156, 145);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(113, 12);
            this.label7.TabIndex = 11;
            this.label7.Text = "小区最强RSRP差距≤";
            // 
            // numRSRPDis
            // 
            this.numRSRPDis.Location = new System.Drawing.Point(276, 141);
            this.numRSRPDis.Maximum = new decimal(new int[] {
            1000,
            0,
            0,
            0});
            this.numRSRPDis.Minimum = new decimal(new int[] {
            1000,
            0,
            0,
            -2147483648});
            this.numRSRPDis.Name = "numRSRPDis";
            this.numRSRPDis.Size = new System.Drawing.Size(120, 21);
            this.numRSRPDis.TabIndex = 12;
            this.numRSRPDis.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numRSRPDis.Value = new decimal(new int[] {
            6,
            0,
            0,
            0});
            // 
            // NBIOTMgrsOverlapCoverageRatioSetting
            // 
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.Controls.Add(this.groupBox1);
            this.Name = "NBIOTMgrsOverlapCoverageRatioSetting";
            ((System.ComponentModel.ISupportInitialize)(this.numCoverage)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMinRSRP)).EndInit();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numGridCount)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRSRPDis)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.NumericUpDown numCoverage;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.NumericUpDown numMinRSRP;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.NumericUpDown numRSRPDis;
        private System.Windows.Forms.NumericUpDown numGridCount;
        private System.Windows.Forms.Label label4;
    }
}
