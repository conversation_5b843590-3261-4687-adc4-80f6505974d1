﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc.ZTNearWeakCover;

namespace MasterCom.RAMS.ZTFunc
{
    public class QueryNearWeakCover_LteScan : DIYAnalyseByCellBackgroundBaseByPeriod
    {
        private static QueryNearWeakCover_LteScan intance = null;
        protected static readonly object lockObj = new object();
        public static QueryNearWeakCover_LteScan GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new QueryNearWeakCover_LteScan();
                    }
                }
            }
            return intance;
        }

        protected QueryNearWeakCover_LteScan()
            : base(MainModel.GetInstance())
        {
            ServiceTypes.Clear();
#if LT
            carrierID = CarrierType.ChinaUnicom;
#else
            carrierID = CarrierType.ChinaMobile;
#endif
        }

        public override string Name
        {
            get { return "近场低输出_LTEScan"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 23000, 23014, this.Name);
        }

        protected override void doSomethingBeforeQueryInThread()
        {
            cells = new List<NearWeakCoverLteCell>();
        }

        private List<NearWeakCoverLteCell> cells = null;
        protected override bool isValidTestPoint(TestPoint tp)
        {
            if (!Condition.Geometorys.GeoOp.Contains(tp.Longitude, tp.Latitude))
            {
                return false;
            }
            for (int i = 0; i < 10; i++)
            {
                object value = tp["LTESCAN_TopN_CELL_Specific_RSRP"];
                if (value == null)
                {
                    break;
                }
                float rsrp = float.Parse(value.ToString());
                if (rsrp < -141 || rsrp > 25 || rsrp > funcCond.Rsrp)
                {
                    break;
                }

                LTECell cell = tp.GetCell_LTEScan(i);
                if (cell==null)
                {
                    continue;
                }
                double distance = tp.Distance2(cell.Longitude, cell.Latitude);
                if (distance <= funcCond.Distance)
                {
                    NearWeakCoverLteCell cellItem = cells.Find(delegate(NearWeakCoverLteCell x)
                    { return x.Cell == cell; });
                    if (cellItem == null)
                    {
                        cellItem = new NearWeakCoverLteCell(cell);
                        cells.Add(cellItem);
                    }
                    cellItem.AddPoint(tp, distance, rsrp, i == 0);
                }
            }
            return false;
        }

        FuncCondition funcCond = null;
        protected override bool getConditionBeforeQuery()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }
            NearWeakCoverSettingDlg dlg = new NearWeakCoverSettingDlg();
            dlg.Condition = funcCond;
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            funcCond = dlg.Condition;
            return true;
        }

        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            List<object> columnsDef = new List<object>();
            Dictionary<string, object> param = new Dictionary<string, object>();
            param["param_name"] = "LTESCAN_TopN_CELL_Specific_RSRP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param["param_name"] = "LTESCAN_TopN_CELL_Specific_RSSINR";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "LTESCAN_TopN_EARFCN";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "LTESCAN_TopN_PCI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            Dictionary<string, object> tmpDic = new Dictionary<string, object>();
            tmpDic.Add("name", (object)"LTE_SCAN");
            tmpDic.Add("themeName", (object)"TopN_CELL_Specific_RSRP");
            tmpDic.Add("columnsDef", (object)columnsDef);

            DIYSampleGroup group = new DIYSampleGroup();
            group.Param = tmpDic;
            return group;
        }

        protected override void getResultAfterQuery()
        {
            int sn = 1;
            cells.Sort();
            foreach (NearWeakCoverLteCell cell in cells)
            {
                cell.Sn = sn++;
                cell.MakeSummary();
            }
        }

        protected override void FireShowFormAfterQuery()
        {
            ResultForm form = MainModel.GetObjectFromBlackboard(typeof(ResultForm)) as ResultForm;
            if (form == null || form.IsDisposed)
            {
                form = new ResultForm();
                form.Owner = MainModel.MainForm;
            }
            form.FillData(new List<NearWeakCoverLteCell>(cells));
            form.Visible = true;
            form.BringToFront();
            cells = null;
        }

    }
}
