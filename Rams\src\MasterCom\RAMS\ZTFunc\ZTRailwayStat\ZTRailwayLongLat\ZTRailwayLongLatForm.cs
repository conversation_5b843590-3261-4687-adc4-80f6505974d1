﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.Grid;
using MasterCom.MControls;
using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using System.Reflection;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTRailwayLongLatForm : MinCloseForm
    {
        public ZTRailwayLongLatForm(MainModel mainModel):base(mainModel)
        {
            InitializeComponent();
            mapForm = MainModel.MainForm.GetMapForm();
            this.mainModel = mainModel;
        }
        List<NPOIRow> nrDataList = new List<NPOIRow>();
        Dictionary<int, FileInfo> logfileDic = new Dictionary<int, FileInfo>();
        MapForm mapForm;
        MainModel mainModel;
        ZTRailwayLongLat ztRailwayLongLat;
        QueryCondition QCondition;

        /// <summary>
        /// 初始化数据
        /// </summary>
        public void InitData(List<NPOIRow> nrDataList, Dictionary<int, FileInfo> logfileDic, ZTRailwayLongLat ztRailwayLongLat,QueryCondition QCondition)
        {
            this.nrDataList = nrDataList;
            this.logfileDic = logfileDic;
            this.ztRailwayLongLat = ztRailwayLongLat;
            this.QCondition = QCondition;
        }

        /// <summary>
        /// 填充数据
        /// </summary>
        public void fillData()
        {
            int rowIndex = 0;
            dataGridViewRailway.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dataGridViewRailway.Columns.Clear();
            dataGridViewRailway.Rows.Clear();
            foreach (NPOIRow nr in nrDataList)
            {
                if (rowIndex == 0)
                    intDataViewColumn(dataGridViewRailway, nr.cellValues);
                else
                    intDataViewRow(dataGridViewRailway, nr.cellValues);
                rowIndex++;
            }
            if (nrDataList.Count <= 1)
            {
                dataGridViewRailway.Visible = false;
                string strReportInfo = "    提示：";
                if (QCondition.DistrictIDs[0] == 1 && QCondition.DistrictIDs.Count == 1)
                {
                    strReportInfo += "当前仅选择【广东】地市，查询不到地市文件信息，请在系统界面右侧【检索条件】→【地市】勾选需查询的地市，并关闭当前窗口重新执行查询。";
                }
                else
                {
                    strReportInfo += string.Format("当前查询时间段 {0} - {1} 暂无回填信息，请联系系统管理员。", QCondition.Periods[0].BeginTime.ToString(), QCondition.Periods[0].EndTime.ToString());
                }
                txtReportInfo.Text = strReportInfo;
            }
        }

        /// <summary>
        /// 初始列头
        /// </summary>
        private void intDataViewColumn(DataGridView dataGridView, List<object> objs)
        {
            dataGridView.Columns.Clear();
            int idx = 1;
            foreach (object obj in objs)
            {
                dataGridView.Columns.Add(idx++.ToString(), obj.ToString());
            }
        }

        /// <summary>
        /// 填充行数据
        /// </summary>
        private void intDataViewRow(DataGridView dataGridView, List<object> objs)
        {
            DataGridViewRow row = new DataGridViewRow();
            int idx = 0;
            foreach (object obj in objs)
            {
                if (idx == 1)
                {
                    //文件ID
                    row.Tag = obj;
                    idx++;
                    continue;
                }
                DataGridViewTextBoxCell boxcell = new DataGridViewTextBoxCell();
                boxcell.Value = obj.ToString();
                row.Cells.Add(boxcell);
                idx++;
            }
            dataGridView.Rows.Add(row);
        }

        /// <summary>
        /// 回放采样点
        /// </summary>
        private void btnSampleReplay_Click(object sender, EventArgs e)
        {
            Replay("回放回填采样点");
        }
        private void btnOriginalSampleReplay_Click(object sender, EventArgs e)
        {
            Replay("回放原始采样点");
        }

        private void Replay(string strType)
        {
            if (dataGridViewRailway.SelectedRows[0].Tag != null && dataGridViewRailway.SelectedRows.Count == 1)
            {
                try
                {
                    int ifileid = 0;
                    FileInfo logfile = null;
                    ifileid = Convert.ToInt32(dataGridViewRailway.SelectedRows[0].Tag);
                    logfile = logfileDic[ifileid];
                    switch (strType)
                    {
                        case "回放回填采样点":
                            ztRailwayLongLat.repalyerSample(logfile, ZTRailwayLongLat.ReplayType.Dealwith);
                            break;
                        case "回放原始采样点":
                            ztRailwayLongLat.repalyerSample(logfile, ZTRailwayLongLat.ReplayType.Default);
                            break;
                    }
                 
                }
                catch (Exception ex)
                {
                    MessageBox.Show(ex.Message);
                }
            }
        }
        DataTable dtLead;
        Dictionary<string, LongLat> longLatDic = new Dictionary<string, LongLat>();
        private void btnLeadExcel_Click(object sender, EventArgs e)
        {
            longLatDic.Clear();
            string fileName = "";
            OpenFileDialog openFile = new OpenFileDialog();
            openFile.Filter = "Excel|*.xls;*.xlsx";
            if (openFile.ShowDialog() == DialogResult.OK)
            {
                txtBoxExcelPath.Text = fileName = openFile.FileName;
            }
            else
            {
                return;
            }
            try
            {
                DataSet ds = ExcelNPOIManager.ImportFromExcel(fileName);

                dtLead = null;
                foreach (DataTable table in ds.Tables)
                {
                    if (table.Columns.Contains("经度") && table.Columns.Contains("纬度"))
                    {
                        dtLead = table;
                        break;
                    }
                }
                if (dtLead == null)
                {
                    MessageBox.Show("导入Excel文件无效");
                    return;
                }
                dtLead.Columns.Add("序号", typeof(string));
                int iRow = 1;
                foreach (DataRow dr in dtLead.Rows)
                {
                    dr["序号"] = iRow++;
                }
                dtLead.Columns["序号"].SetOrdinal(0);
                if (dataGridViewResult.Columns.Count > 0)
                    dataGridViewResult.Columns.Clear();
                if (dataGridViewResult.Rows.Count > 0)
                    dataGridViewResult.Rows.Clear();
                FillDataTable(dtLead);
                miShwoChart(dtLead);
                btnSearch.Enabled = true;
            }
            catch
            {
                MessageBox.Show("Excel 读取失败，请按照模板规范填写。");
            }
        }

        /// <summary>
        /// 列表填数
        /// </summary>
        private void FillDataTable(DataTable dataTable)
        {
            if (dataTable == null)
                return;
            dataGridViewResult.DataSource = dataTable;
            labelRowCount.Text = dataTable.Rows.Count.ToString();
        }

        private void miShwoChart(DataTable dataTable)
        {
            for (int index = dataTable.Rows.Count - 1; index >= 0; index--)
            {
                string strCellName = dataTable.Rows[index][1].ToString();
                LongLat ll = new LongLat();
                ll.fLongitude = Convert.ToSingle(dtLead.Rows[index][2].ToString());
                ll.fLatitude = Convert.ToSingle(dtLead.Rows[index][3].ToString());
                if (!longLatDic.ContainsKey(strCellName))
                {
                    longLatDic.Add(strCellName, ll);
                }
            }

            LongLatPointLayer antLayer = mapForm.GetLayerBase(typeof(LongLatPointLayer)) as LongLatPointLayer;
            if (antLayer != null)
            {
                LongLat ll = new LongLat();
                foreach(LongLat l in longLatDic.Values)
                {
                    if (l != null)
                    {
                        ll = l;
                        break;
                    }
                }

                MainModel.MainForm.GetMapForm().GoToView(ll.fLongitude, ll.fLatitude);
                antLayer.gisSampleDic = longLatDic;
                antLayer.Invalidate();
            }
        }

        private void btnTemplateDown_Click(object sender, EventArgs e)
        {
            List<List<object>> datas = new List<List<object>>();
            List<object> head = new List<object>();
            head.Add("站点名称");
            head.Add("经度");
            head.Add("纬度");
            datas.Add(head);
            ExcelNPOIManager.ExportToExcel(datas);
        }

        private void btnSearch_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(txtBoxSite.Text))
            {
                FillDataTable(dtLead);
                return;
            }
            string strSite = txtBoxSite.Text.Trim();
            DataTable tmpDt = dtLead.Copy();
            for (int index = tmpDt.Rows.Count - 1; index >=0; index--)
            {
                string strTmp = dtLead.Rows[index][1].ToString();
                if (!strTmp.Contains(strSite))
                {
                    tmpDt.Rows.RemoveAt(index);
                }
            }
            FillDataTable(tmpDt);
        }

        /// <summary>
        /// 站点定位
        /// </summary>
        private void MenuItemLocation_Click(object sender, EventArgs e)
        {
            if (dataGridViewResult.SelectedRows.Count >= 1)
            {
                string strCellName = dataGridViewResult.SelectedRows[0].Cells[1].Value.ToString();
                if (longLatDic.ContainsKey(strCellName))
                {
                    LongLat ll = longLatDic[strCellName];
                    MainModel.MainForm.GetMapForm().GoToView(ll.fLongitude, ll.fLatitude);
                }
            }
        }
    }
}
