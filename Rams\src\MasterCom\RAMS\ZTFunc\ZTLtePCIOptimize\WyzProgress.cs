﻿
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;
namespace MasterCom.RAMS.ZTFunc
{
    public class WyzProgress : Control
    {
        #region Construct
        
        public WyzProgress()
        {
            this.ProgressMaximum = 100;
            this.BackColor = Color.White;

            this.SetStyle(ControlStyles.OptimizedDoubleBuffer | ControlStyles.ResizeRedraw | ControlStyles.AllPaintingInWmPaint, true);
            this.Paint += MyProgress_Paint;
            RegionColor = Color.Green;

            ProgressAppearance = ProgressMode.Win8;
        }

        ~WyzProgress()
        {
            black_pen.Dispose();
        }

        #endregion

        #region Field and Property

        public int ProgressValue { get { return SpeedList.Count - 1; } }
        public int ProgressMaximum { get; set; }
        public int ProgressMinimum { get; set; }
        public Color RegionColor 
        {
            get
            {
                return region_color;
            }
            set
            {
                region_color = value;
                if (brushAll != null) brushAll.Dispose();
                if (brushRegion != null) brushRegion.Dispose();
                HSLColor hsl = new HSLColor(value);
                hsl.Lightness *= 1.2;
                brushAll = new SolidBrush(Color.FromArgb(128, hsl.Color.R, hsl.Color.G, hsl.Color.B));
                brushRegion = new SolidBrush(RegionColor);
            }
        }
        private Color region_color = Color.Lime;
        private SolidBrush brushAll = null;
        private SolidBrush brushRegion = null;

        private readonly List<int> SpeedList = new List<int>();
        private readonly List<int> SpeedListSecond = new List<int>();
        private readonly Pen black_pen = new Pen(Color.Black, 1);
        private int max_speed = 0;

        public enum ProgressMode { Normal, Win8 }
        public ProgressMode ProgressAppearance { get; set; }

        #endregion

        #region Method
        
        public void Reset()
        {
            SpeedList.Clear();
            SpeedListSecond.Clear();
        }

        private Point GetPointByProgressAndSpeed(int progress, int speed)
        {
            return new Point(progress * (Width - 2) / (ProgressMaximum - ProgressMinimum), max_speed == 0 ? Height - 2 : (Height - speed * (Height - 20) / max_speed));
        }

        public void Step(int speed, int speedSecond)
        {
            SpeedList.Add(speed);
            SpeedListSecond.Add(speedSecond);
            this.Invalidate();
            this.Update();
        }

        public void Step(int speed, int speedSecond, int progress)
        {
            SpeedList.Add(speed);
            SpeedListSecond.Add(speedSecond);
            this.Invalidate();
            this.Update();
        }

        #endregion

        #region Paint event handler
        
        void MyProgress_Paint(object sender, PaintEventArgs e)
        {
            BufferedGraphicsContext currentContext = BufferedGraphicsManager.Current;
            BufferedGraphics myBuffer = currentContext.Allocate(e.Graphics, e.ClipRectangle);
            Graphics g = myBuffer.Graphics;
            g.SmoothingMode = SmoothingMode.HighQuality;
            g.PixelOffsetMode = PixelOffsetMode.HighSpeed;

            g.Clear(this.BackColor);

            if (ProgressAppearance == ProgressMode.Win8)
            {
                int a = 60;
                while (a < Width)
                {
                    g.DrawLine(Pens.LightGray, a, 1, a, Height - 1);
                    a += 60;
                }

                a = 20;
                while (a < Width)
                {
                    g.DrawLine(Pens.LightGray, 1, a, Width - 1, a);
                    a += 20;
                }
            }

            if (SpeedList.Count > 0)
            {
                Point lastPoint = GetPointByProgressAndSpeed(SpeedList.Count - 1, SpeedList[SpeedList.Count - 1]);
                Point lastPointSecond = GetPointByProgressAndSpeed(SpeedListSecond.Count - 1, SpeedListSecond[SpeedList.Count - 1]);
                List<Point> points = new List<Point>();
                List<Point> pointsSecond = new List<Point>();
                lastPoint = getWin8Points(lastPoint, lastPointSecond, points, pointsSecond);

                g.FillRectangle(brushAll, 1, 1, lastPoint.X, Height - 2);

                drawWin8(g, points, pointsSecond);
            }
            g.DrawRectangle(Pens.DarkGray, 0, 0, Width - 1, Height - 1);

            myBuffer.Render(e.Graphics);
            g.Dispose();
            myBuffer.Dispose();//释放资源
        }

        private Point getWin8Points(Point lastPoint, Point lastPointSecond, List<Point> points, List<Point> pointsSecond)
        {
            if (ProgressAppearance == ProgressMode.Win8)
            {
                max_speed = getMax(SpeedList);

                //进度                    
                for (int i = 0; i < SpeedList.Count; i++)
                {
                    points.Add(GetPointByProgressAndSpeed(i, SpeedList[i]));
                }
                points.Add(new Point(lastPoint.X, Height - 1));
                points.Add(new Point(1, Height - 1));
                for (int i = 0; i < SpeedListSecond.Count; i++)
                {
                    pointsSecond.Add(GetPointByProgressAndSpeed(i, SpeedListSecond[i]));
                }
                pointsSecond.Add(new Point(lastPointSecond.X, Height - 1));
                pointsSecond.Add(new Point(1, Height - 1));
            }

            return lastPoint;
        }

        private void drawWin8(Graphics g, List<Point> points, List<Point> pointsSecond)
        {
            if (ProgressAppearance == ProgressMode.Win8)
            {
                g.FillPolygon(brushRegion, points.ToArray());
                g.DrawLines(new Pen(Color.Yellow), pointsSecond.ToArray());

                //当前的标尺线
                int current_speed = SpeedList[SpeedList.Count - 1];
                Point pCurrent = GetPointByProgressAndSpeed(ProgressValue, current_speed);
                g.DrawLine(black_pen, 0, pCurrent.Y, Width - 1, pCurrent.Y);

                //绘制当前值
                SizeF sf = g.MeasureString(this.Text, this.Font);
                g.DrawString(this.Text, this.Font, Brushes.Black, Width - sf.Width - 2, pCurrent.Y - sf.Height);
            }
        }

        private int getMax(List<int> iLst)
        {
            int max = int.MinValue;
            foreach (int item in iLst)
            {
                if (item > max)
                {
                    max = item;
                }
            }
            return max;
        }

        #endregion
    }
}