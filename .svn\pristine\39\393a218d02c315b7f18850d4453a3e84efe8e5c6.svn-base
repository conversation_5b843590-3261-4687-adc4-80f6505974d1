﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc.LteSignalImsi
{
    public partial class ReplayFileByImsiSetForm : BaseDialog
    {
        public ReplayFileByImsiSetForm()
        {
            InitializeComponent();

            txtSearch.TextChanged += TxtSearch_TextChanged;
            lbxUsers.DoubleClick += LbxUsers_DoubleClick;
            btnCancel.Click += BtnCancel_Click;
            btnOK.Click += BtnOK_Click;

            imsiFileDic = new Dictionary<string, List<ImsiFile>>();
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = System.Windows.Forms.DialogResult.Cancel;
        }

        private void BtnOK_Click(object sender, EventArgs e)
        {
            if (lbxUsers.SelectedItem == null)
            {
                DialogResult = System.Windows.Forms.DialogResult.None;
                MessageBox.Show("请选择一个用户", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            else
            {
                DialogResult = System.Windows.Forms.DialogResult.OK;
            }
        }

        private void LbxUsers_DoubleClick(object sender, EventArgs e)
        {
            btnOK.PerformClick();
        }

        private void TxtSearch_TextChanged(object sender, EventArgs e)
        {
            string searchTxt = txtSearch.Text.Trim();

            lbxUsers.Items.Clear();
            foreach (string imsi in imsiFileDic.Keys)
            {
                if (string.IsNullOrEmpty(searchTxt) || imsi.Contains(searchTxt))
                {
                    lbxUsers.Items.Add(imsi);
                }
            }
            if (lbxUsers.Items.Count > 0)
            {
                lbxUsers.SelectedIndex = 0;
            }
        }

        public void FillData(List<ImsiFile> imsiFiles)
        {
            imsiFileDic.Clear();
            foreach (ImsiFile iFile in imsiFiles)
            {
                foreach (string imsi in iFile.ImsiList)
                {
                    List<ImsiFile> tmpList = null;
                    if (!imsiFileDic.TryGetValue(imsi, out tmpList))
                    {
                        tmpList = new List<ImsiFile>();
                        imsiFileDic.Add(imsi, tmpList);
                    }
                    tmpList.Add(iFile);
                }
            }
            TxtSearch_TextChanged(txtSearch, EventArgs.Empty);
        }

        public List<ImsiFile> GetSelectedFiles(ref string imsiSel)
        {
            if (lbxUsers.SelectedItem != null && imsiFileDic.ContainsKey(lbxUsers.SelectedItem as string))
            {
                imsiSel = lbxUsers.SelectedItem as String;
                return imsiFileDic[lbxUsers.SelectedItem as string];
            }
            else if (imsiFileDic.Count > 0)
            {
                List<ImsiFile> retList = null;
                foreach (List<ImsiFile> tmpList in imsiFileDic.Values)
                {
                    if (tmpList != null)
                    {
                        retList = tmpList;
                        break;
                    }
                }
                return retList;
            }
            else
            {
                return new List<ImsiFile>();
            }
        }

        private Dictionary<string, List<ImsiFile>> imsiFileDic;
    }
}
