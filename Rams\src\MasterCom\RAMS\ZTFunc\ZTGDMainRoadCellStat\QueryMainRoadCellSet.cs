﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class QueryMainRoadCellSet : QueryBase
    {
        public QueryMainRoadCellSet(MainModel mainModel)
            : base(mainModel)
        { 
        }

        public override string Name
        {
            get { return "高速小区集精简查询"; }
        }
        public override string IconName
        {
            get { return "Images/regionq.gif"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22099, this.Name);
        }
        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }
        public override MainModel.NeedSearchType needSearchType()
        {
            return MainModel.NeedSearchType.Region;
        }
        protected override bool isValidCondition()
        {
            return true;
        }

        #region 变量
        Dictionary<string, Dictionary<string, Dictionary<string, List<MainRoadCellInfo>>>> cityMainRoadCellInfoDic = null;
        Dictionary<string, Dictionary<string, List<FileInfo>>> cityMainRoadFileDic = null;
        Dictionary<int, DateTime> fileDateDic = null;
        readonly List<string> strMainRoadList = new List<string>();
        ResultData resultData = null;
        #endregion

        protected override void query()
        {
            initData();
            WaitBox.CanCancel = true;
            WaitBox.Text = "正在查询...";
            WaitBox.Show("1.查询设置条件内的所有文件...", queryFiles);
            WaitBox.Show("2.按文件查询预处理的小区信息...", doSomethBeforeQueryCell);
            WaitBox.Show("3.正在处理小区汇总统计...", doSomethAferQuery);
            WaitBox.Show("4.分析高速多次覆盖小区信息...", doCalMainRoadTestCellMuch);
            fireShowForm();
        }

        private void initData()
        {
            cityMainRoadCellInfoDic = new Dictionary<string, Dictionary<string, Dictionary<string, List<MainRoadCellInfo>>>>();
            cityMainRoadFileDic = new Dictionary<string, Dictionary<string, List<FileInfo>>>();
            fileDateDic = new Dictionary<int, DateTime>();
            strMainRoadList.Clear();
            resultData = new ResultData();
        }

        private void queryFiles()
        {
            DIYQueryFileInfo queryFile = new DIYQueryFileInfo(MainModel);
            queryFile.IsShowFileInfoForm = false;
            queryFile.SetQueryCondition(condition);
            queryFile.Query();

            foreach (FileInfo file in MainModel.FileInfos)
            {
                if (!fileDateDic.ContainsKey(file.ID))
                {
                    fileDateDic[file.ID] = JavaDate.GetDateTimeFromMilliseconds(file.BeginTime * 1000L);
                }
                string strCityName = DistrictManager.GetInstance().getDistrictName(file.DistrictID);
                string strMainRoad = AreaManager.GetInstance().GetAreaDesc(file.AreaTypeID, file.AreaID);
                if (!cityMainRoadFileDic.ContainsKey(strCityName))
                {
                    Dictionary<string, List<FileInfo>> maindRoadFileDic = new Dictionary<string, List<FileInfo>>();
                    maindRoadFileDic[strMainRoad] = new List<FileInfo>() { file };
                    cityMainRoadFileDic[strCityName] = maindRoadFileDic;
                }
                else
                {
                    if (!cityMainRoadFileDic[strCityName].ContainsKey(strMainRoad))
                        cityMainRoadFileDic[strCityName][strMainRoad] = new List<FileInfo>() { file };
                    else
                        cityMainRoadFileDic[strCityName][strMainRoad].Add(file);
                }
            }
            MainModel.FileInfos.Clear();

            WaitBox.Close();
        }

        private void doSomethBeforeQueryCell()
        {
            List<string> cellMonthList = getCellMonthList();
            int iCityIDTmp = MainModel.DistrictID;
            foreach (string strCityName in cityMainRoadFileDic.Keys)
            {
                WaitBox.Text = string.Format(" 正在查询 {0} 地市 状态库小区表", strCityName);
                DiyNopCellInfo queryNopCell = new DiyNopCellInfo(MainModel, strCityName);
                queryNopCell.SetQueryCondition(condition);
                queryNopCell.Query();
                WaitBox.ProgressPercent = 55;
                WaitBox.Text = string.Format(" 正在查询 {0} 地市 高速底图序列点", strCityName);
                MainModel.DistrictID = DistrictManager.GetInstance().GetDistrictID(strCityName);
                DiyMainRoadPoint queryRoadPoint = new DiyMainRoadPoint(MainModel);
                queryRoadPoint.SetQueryCondition(condition);
                queryRoadPoint.Query();
                foreach (string strMainRoad in cityMainRoadFileDic[strCityName].Keys)
                {
                    if (!strMainRoadList.Contains(strMainRoad))
                    {
                        strMainRoadList.Add(strMainRoad);
                    }
                    bool isSuccess = queryByMonth(cellMonthList, iCityIDTmp, strCityName, queryNopCell, strMainRoad);
                    if (!isSuccess)
                    {
                        return;
                    }
                    List<MainRoadCellInfo> mainRoadPointList = new List<MainRoadCellInfo>();
                    if (queryRoadPoint.mainRoadPointInfoDic.ContainsKey(strMainRoad))
                        mainRoadPointList.AddRange(queryRoadPoint.mainRoadPointInfoDic[strMainRoad]);
                    else
                        System.Windows.Forms.MessageBox.Show(string.Format("没有 {0} 的底图信息，请检查配置！！！", strMainRoad));
                    doCalTestRoadDistance(strCityName, strMainRoad, mainRoadPointList);
                }
            }
            MainModel.DistrictID = iCityIDTmp;
            cityMainRoadFileDic.Clear();
            fileDateDic.Clear();
            WaitBox.Close();
        }

        private List<string> getCellMonthList()
        {
            List<string> cellMonthList = new List<string>();
            DateTime dStartTime = condition.Periods[0].BeginTime;
            while (dStartTime <= condition.Periods[0].EndTime)
            {
                string strMonth = dStartTime.ToString("yyMM");
                if (!cellMonthList.Contains(strMonth))
                {
                    cellMonthList.Add(strMonth);
                }
                dStartTime = dStartTime.AddDays(1);
            }

            return cellMonthList;
        }

        private bool queryByMonth(List<string> cellMonthList, int iCityIDTmp, string strCityName, DiyNopCellInfo queryNopCell, string strMainRoad)
        {
            foreach (string month in cellMonthList)
            {
                WaitBox.Text = string.Format(" 正在查询 {0} 高速 {1} 地市 {2} 月份 小区详情列表", strMainRoad, strCityName, month);
                ClientProxy clientProxy = new ClientProxy();
                try
                {
                    if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName
                        , MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
                    {
                        ErrorInfo = "连接服务器失败!";
                        return false;
                    }
                    queryMainRoadCells(new object[] { strMainRoad, strCityName, month, cityMainRoadFileDic[strCityName][strMainRoad], queryNopCell.cgiCellDic });
                }
                catch
                {
                    clientProxy.Close();
                    MainModel.DistrictID = iCityIDTmp;
                }
            }
            return true;
        }

        private void doCalTestRoadDistance(string strCityName, string strMainRoad, List<MainRoadCellInfo> mainRoadPointList)
        {
            if (!cityMainRoadCellInfoDic.ContainsKey(strCityName) || !cityMainRoadCellInfoDic[strCityName].ContainsKey(strMainRoad))
            {
                return;
            }
            Dictionary<string, List<MainRoadCellInfo>> monthCellDic = cityMainRoadCellInfoDic[strCityName][strMainRoad];
            foreach (string month in monthCellDic.Keys)
            {
                foreach (MainRoadCellInfo cellInfo in monthCellDic[month])
                {
                    if (cellInfo.StrCellName != "")
                    {
                        setMainRoadCellInfo(mainRoadPointList, cellInfo);
                    }
                }
            }
        }

        private void setMainRoadCellInfo(List<MainRoadCellInfo> mainRoadPointList, MainRoadCellInfo cellInfo)
        {
            foreach (MainRoadCellInfo cellPoint in mainRoadPointList)
            {
                if ((cellPoint.DLongitude < cellInfo.DLongitude - 0.03) || (cellPoint.DLongitude > cellInfo.DLongitude + 0.03)
                    || (cellPoint.DLatitude < cellInfo.DLatitude - 0.03) || (cellPoint.DLatitude > cellInfo.DLatitude + 0.03))
                {
                    continue;
                }
                double dDistance = MathFuncs.GetDistance(cellInfo.DLongitude, cellInfo.DLatitude, cellPoint.DLongitude, cellPoint.DLatitude);
                if (dDistance < cellInfo.DTestRoadDistance)
                {
                    cellInfo.DTestRoadDistance = dDistance;
                    cellInfo.StrRoadPointLng = cellPoint.DLongitude.ToString();
                    cellInfo.StrRoadPointLat = cellPoint.DLatitude.ToString();
                }
            }
        }

        private void queryMainRoadCells(object args)
        {
            object[] objArray = args as object[];
            string strMainRoad = objArray[0] as string;
            string strCityName = objArray[1] as string;
            string strMonth = objArray[2] as string;
            List<FileInfo> fileList = objArray[3] as List<FileInfo>;
            Dictionary<string, NOPCellInfo> cgiCellDic = objArray[4] as Dictionary<string, NOPCellInfo>;
            DiyMainRoaCellInfo queryCell = new DiyMainRoaCellInfo(MainModel);
            queryCell.SetQueryCondition(condition);
            queryCell.SetCondition(strMainRoad, strCityName, strMonth, fileList);
            queryCell.Query();

            foreach (MainRoadCellInfo cell in queryCell.MainRoadCellSetInfoList)
            {
                string strLac_Ci = cell.ILAC + "_" + cell.ICI;
                if (cgiCellDic.ContainsKey(strLac_Ci))
                {
                    cell.StrCGI = cgiCellDic[strLac_Ci].StrCGI;
                    if (cell.StrNet.Equals("LTE"))
                    {
                        cell.StrCGI = "460-00-" + (cell.ICI / 256) + "-" + (cell.ICI - cell.ICI / 256 * 256);
                    }
                    cell.StrCellName = cgiCellDic[strLac_Ci].StrCellName;
                    cell.StrCellEngName = cgiCellDic[strLac_Ci].StrCellEngName;
                    cell.StrENODEBID = cgiCellDic[strLac_Ci].StrENODEBID;
                    cell.StrDirection = cgiCellDic[strLac_Ci].StrDirection;
                    cell.StrDownward = cgiCellDic[strLac_Ci].StrDownward;
                    cell.StrLongitude = cgiCellDic[strLac_Ci].StrLongitude;
                    cell.StrLatitude = cgiCellDic[strLac_Ci].StrLatitude;
                    cell.StrBTSLogicName = cgiCellDic[strLac_Ci].StrBTSLogicName;
                    cell.StrBTSPhysiName = cgiCellDic[strLac_Ci].StrBTSPhysiName;
                }
            }

            if (!cityMainRoadCellInfoDic.ContainsKey(strCityName))
            {
                Dictionary<string, Dictionary<string, List<MainRoadCellInfo>>> mainRoadMonthCellDic 
                    = new Dictionary<string, Dictionary<string, List<MainRoadCellInfo>>>();
                Dictionary<string, List<MainRoadCellInfo>> monthCellDic = new Dictionary<string, List<MainRoadCellInfo>>();
                monthCellDic[strMonth] = queryCell.MainRoadCellSetInfoList;
                mainRoadMonthCellDic[strMainRoad] = monthCellDic;
                cityMainRoadCellInfoDic[strCityName] = mainRoadMonthCellDic;
            }
            else
            {
                if (!cityMainRoadCellInfoDic[strCityName].ContainsKey(strMainRoad))
                {
                    Dictionary<string, List<MainRoadCellInfo>> monthCellDic = new Dictionary<string, List<MainRoadCellInfo>>();
                    monthCellDic[strMonth] = queryCell.MainRoadCellSetInfoList;
                    cityMainRoadCellInfoDic[strCityName][strMainRoad] = monthCellDic;
                } 
                else
                {
                    if (!cityMainRoadCellInfoDic[strCityName][strMainRoad].ContainsKey(strMonth))
                    {
                        cityMainRoadCellInfoDic[strCityName][strMainRoad][strMonth] = queryCell.MainRoadCellSetInfoList; 
                    }
                }
            }
        }

        private void doSomethAferQuery()
        {
            foreach (string mainRoad in strMainRoadList)
            {
                foreach (string strCityName in cityMainRoadCellInfoDic.Keys)
                {
                    if (!cityMainRoadCellInfoDic[strCityName].ContainsKey(mainRoad))
                    {
                        continue;
                    }
                    foreach (string strMonth in cityMainRoadCellInfoDic[strCityName][mainRoad].Keys)
                    {
                        foreach (MainRoadCellInfo mainRoadCell in cityMainRoadCellInfoDic[strCityName][mainRoad][strMonth])
                        {
                            mainRoadCell.ISN = resultData.MainRoadCellInfoDetailList.Count + 1;
                            resultData.MainRoadCellInfoDetailList.Add(mainRoadCell);

                            List<CityMainRoadCellKey> cellAllKeyList  = new List<CityMainRoadCellKey>();
                            cellAllKeyList.Add(new CityMainRoadCellKey(mainRoadCell.StrMainRoadName, mainRoadCell.StrCity, mainRoadCell.StrTestMonth, mainRoadCell.StrTestDir, mainRoadCell.StrNet));
                            cellAllKeyList.Add(new CityMainRoadCellKey(mainRoadCell.StrMainRoadName, mainRoadCell.StrCity, mainRoadCell.StrTestMonth, mainRoadCell.StrTestDir, "汇总"));
                            cellAllKeyList.Add(new CityMainRoadCellKey(mainRoadCell.StrMainRoadName, mainRoadCell.StrCity, mainRoadCell.StrTestMonth, "汇总", "汇总"));
                            cellAllKeyList.Add(new CityMainRoadCellKey(mainRoadCell.StrMainRoadName, mainRoadCell.StrCity, "汇总", "汇总", "汇总"));
                            cellAllKeyList.Add(new CityMainRoadCellKey(mainRoadCell.StrMainRoadName, "汇总", "汇总", "汇总", "汇总"));
                            cellAllKeyList.Add(new CityMainRoadCellKey("汇总", mainRoadCell.StrCity, "汇总", "汇总", "汇总"));

                            string strLac_Ci = mainRoadCell.ILAC + "_" + mainRoadCell.ICI;
                            foreach (CityMainRoadCellKey cellAllKey in cellAllKeyList)
                            {
                                if (!resultData.MainRoadCellInfoSumDic.ContainsKey(cellAllKey))
                                {
                                    MainRoadCellInfo cellSumInfo = new MainRoadCellInfo();
                                    cellSumInfo.StrMainRoadName = cellAllKey.StrMainRoad;
                                    cellSumInfo.StrCity = cellAllKey.StrCity;
                                    cellSumInfo.StrTestMonth = cellAllKey.StrTestMonth;
                                    cellSumInfo.StrTestDir = cellAllKey.StrTestDir;
                                    cellSumInfo.StrNet = cellAllKey.StrNet;
                                    cellSumInfo.CellNameList.Add(strLac_Ci);
                                    cellSumInfo.ICellNum = 1;
                                    if (mainRoadCell.StrBTSLogicName != "")
                                    {
                                        cellSumInfo.BtsLogicList.Add(mainRoadCell.StrBTSLogicName);
                                        cellSumInfo.IBTSLogicNum = 1;
                                    }
                                    if (mainRoadCell.StrBTSPhysiName != "")
                                    {
                                        cellSumInfo.BtsPhysiList.Add(mainRoadCell.StrBTSPhysiName);
                                        cellSumInfo.IBTSPhysiNum = 1;
                                    }
                                    resultData.MainRoadCellInfoSumDic[cellAllKey] = cellSumInfo;
                                }
                                else
                                {
                                    if (!resultData.MainRoadCellInfoSumDic[cellAllKey].CellNameList.Contains(strLac_Ci))
                                    {
                                        resultData.MainRoadCellInfoSumDic[cellAllKey].CellNameList.Add(strLac_Ci);
                                        resultData.MainRoadCellInfoSumDic[cellAllKey].ICellNum += 1;
                                    }
                                    if (!resultData.MainRoadCellInfoSumDic[cellAllKey].BtsLogicList.Contains(mainRoadCell.StrBTSLogicName)
                                        && mainRoadCell.StrBTSLogicName != "")
                                    {
                                        resultData.MainRoadCellInfoSumDic[cellAllKey].BtsLogicList.Add(mainRoadCell.StrBTSLogicName);
                                        resultData.MainRoadCellInfoSumDic[cellAllKey].IBTSLogicNum += 1;
                                    }
                                    if (!resultData.MainRoadCellInfoSumDic[cellAllKey].BtsPhysiList.Contains(mainRoadCell.StrBTSPhysiName)
                                        && mainRoadCell.StrBTSPhysiName != "")
                                    {
                                        resultData.MainRoadCellInfoSumDic[cellAllKey].BtsPhysiList.Add(mainRoadCell.StrBTSPhysiName);
                                        resultData.MainRoadCellInfoSumDic[cellAllKey].IBTSPhysiNum += 1;
                                    }
                                }
                            }
                        }
                    }
                }
            }
            WaitBox.Close();
        }

        private void doCalMainRoadTestCellMuch()
        {
            Dictionary<CityMainRoadCellKey,MainRoadCellInfo> cityMainRoandMuchCoverNumDic
                 = new Dictionary<CityMainRoadCellKey,MainRoadCellInfo>();
            Dictionary<CityMainRoadCellKey, int> cityMainRoandMuchTestNumDic = new Dictionary<CityMainRoadCellKey, int>();
            foreach(MainRoadCellInfo mainRoadCell in resultData.MainRoadCellInfoDetailList)
            {
                CityMainRoadCellKey testInfoKey = new CityMainRoadCellKey();
                testInfoKey.StrCity = mainRoadCell.StrCity;
                testInfoKey.StrMainRoad = mainRoadCell.StrMainRoadName;
                testInfoKey.StrTestMonth = mainRoadCell.StrTestMonth;
                testInfoKey.StrTestDir = mainRoadCell.StrTestDir;
                if (!cityMainRoandMuchTestNumDic.ContainsKey(testInfoKey))
                {
                    cityMainRoandMuchTestNumDic[testInfoKey] = 1;
                }

                CityMainRoadCellKey coverInfoKey = new CityMainRoadCellKey();
                coverInfoKey.StrCity = mainRoadCell.StrCity;
                coverInfoKey.StrMainRoad = mainRoadCell.StrMainRoadName;
                coverInfoKey.StrNet = mainRoadCell.StrNet;
                coverInfoKey.ILAC = mainRoadCell.ILAC;
                coverInfoKey.ICI = mainRoadCell.ICI;
                if (!cityMainRoandMuchCoverNumDic.ContainsKey(coverInfoKey))
                {
                    cityMainRoandMuchCoverNumDic[coverInfoKey] = mainRoadCell;
                    cityMainRoandMuchCoverNumDic[coverInfoKey].ITestNum = 0;
                    cityMainRoandMuchCoverNumDic[coverInfoKey].ICoverNum = 1;
                }
                else
                    cityMainRoandMuchCoverNumDic[coverInfoKey].ICoverNum++;
            }

            foreach (CityMainRoadCellKey coverInfoKey in cityMainRoandMuchCoverNumDic.Keys)
            {
                foreach (CityMainRoadCellKey testInfoKey in cityMainRoandMuchTestNumDic.Keys)
                {
                    if (coverInfoKey.StrCity == testInfoKey.StrCity && coverInfoKey.StrMainRoad == testInfoKey.StrMainRoad)
                    {
                        cityMainRoandMuchCoverNumDic[coverInfoKey].ITestNum++;
                    }
                }
            }
            resultData.MainRoadMuchCoverDetailList.AddRange(cityMainRoandMuchCoverNumDic.Values);
            WaitBox.Close();
        }

        protected void fireShowForm()
        {
            object obj = MainModel.GetInstance().GetObjectFromBlackboard(typeof(QueryMainRoadCellSetForm).FullName);
            QueryMainRoadCellSetForm resultForm = obj == null ? null : obj as QueryMainRoadCellSetForm;
            if (resultForm == null || resultForm.IsDisposed)
            {
                resultForm = new QueryMainRoadCellSetForm(MainModel);
            }
            resultForm.FillData(resultData);
            if (!resultForm.Visible)
            {
                resultForm.Show(MainModel.MainForm);
            }
        }
    }

    public class ResultData
    {
        public List<MainRoadCellInfo> MainRoadCellInfoDetailList { get; set; } = new List<MainRoadCellInfo>();
        public Dictionary<CityMainRoadCellKey, MainRoadCellInfo> MainRoadCellInfoSumDic { get; set; } = new Dictionary<CityMainRoadCellKey, MainRoadCellInfo>();
        public List<MainRoadCellInfo> MainRoadMuchCoverDetailList { get; set; } = new List<MainRoadCellInfo>();
        public Dictionary<CityMainRoadCellKey, MainRoadCellInfo> MainRoadMuchCoverSumDic { get; set; } = new Dictionary<CityMainRoadCellKey, MainRoadCellInfo>();
    }
}
