﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc.AreaArchiveManage.Problem
{
    public partial class ProblemAreaListForm : MinCloseForm
    {
        public ProblemAreaListForm()
            : base()
        {
            InitializeComponent();
            initLv();
        }

        private void initLv()
        {
            this.colAbnormalEvtDesc.AspectGetter += delegate (object row)
            {
                if (row is ProblemArea)
                {
                    return (row as ProblemArea).AbnormalEventsDesc;
                }
                return null;
            };
            this.colAbnormalEvtNum.AspectGetter += delegate (object row)
            {
                if (row is ProblemArea)
                {
                    return (row as ProblemArea).AbnormalEventNum;
                }
                return null;
            };
            this.colName.AspectGetter += delegate (object row)
            {
                if (row is ProblemArea)
                {
                    return (row as <PERSON>Area).Area.FullName;
                }
                return null;
            };
            this.colProbNum.AspectGetter += delegate (object row)
            {
                if (row is ProblemArea)
                {
                    return (row as <PERSON>Area).ProbNum;
                }
                return null;
            };
            this.colSn.AspectGetter += delegate (object row)
            {
                if (row is ProblemArea)
                {
                    return (row as ProblemArea).Area.SN;
                }
                return null;
            };

            setProb();
            setCover();
            this.colQual.AspectGetter += delegate (object row)
            {
                if (row is ProblemArea)
                {
                    double val = (row as ProblemArea).PoorQual;
                    if (double.IsNaN(val))
                    {
                        return "-";
                    }
                    else
                    {
                        return val;
                    }
                }
                return null;
            };
        }

        private void setCover()
        {
            this.colCvrCM.AspectGetter += delegate (object row)
            {
                if (row is ProblemArea)
                {
                    double val = (row as ProblemArea).CoverCM;
                    if (double.IsNaN(val))
                    {
                        return "-";
                    }
                    else
                    {
                        return val;
                    }
                }
                return null;
            };
            this.colCvrCT.AspectGetter += delegate (object row)
            {
                if (row is ProblemArea)
                {
                    double val = (row as ProblemArea).CoverCT;
                    if (double.IsNaN(val))
                    {
                        return "-";
                    }
                    else
                    {
                        return val;
                    }
                }
                return null;
            };
            this.colCvrCU.AspectGetter += delegate (object row)
            {
                if (row is ProblemArea)
                {
                    double val = (row as ProblemArea).CoverCU;
                    if (double.IsNaN(val))
                    {
                        return "-";
                    }
                    else
                    {
                        return val;
                    }
                }
                return null;
            };
        }

        private void setProb()
        {
            this.colAbnormalEvt.AspectGetter += delegate (object row)
            {
                if (row is ProblemArea)
                {
                    return ((row as ProblemArea).ProbType & ProblemType.异常事件) == ProblemType.异常事件 ? "是" : "否";
                }
                return null;
            };
            this.colWeakCvrAndPoorQual.AspectGetter += delegate (object row)
            {
                if (row is ProblemArea)
                {
                    return ((row as ProblemArea).ProbType & ProblemType.弱覆盖且语音质差) == ProblemType.弱覆盖且语音质差 ? "是" : "否";
                }
                return null;
            };
            this.colWeakCvrAndWorst.AspectGetter += delegate (object row)
            {
                if (row is ProblemArea)
                {
                    return ((row as ProblemArea).ProbType & ProblemType.弱覆盖且差于竞争对手) == ProblemType.弱覆盖且差于竞争对手 ? "是" : "否";
                }
                return null;
            };
            this.colWeakCvrOnly.AspectGetter += delegate (object row)
            {
                if (row is ProblemArea)
                {
                    return ((row as ProblemArea).ProbType & ProblemType.仅弱覆盖) == ProblemType.仅弱覆盖 ? "是" : "否";
                }
                return null;
            };
        }

        public void FillData(List<ProblemArea> probAreas,ProblemCondition cond)
        {
            this.colQual.Text = cond.PoorQualName;
            this.colCvrCM.Text = cond.WeakCvrNameCM;
            this.colCvrCT.Text = cond.WeakCvrNameCT;
            this.colCvrCU.Text = cond.WeakCvrNameCU;
            lv.ClearObjects();
            lv.SetObjects(probAreas);
        }

        private void miExport2Xls_Click(object sender, EventArgs e)
        {
            MasterCom.Util.ExcelNPOIManager.ExportToExcel(lv);
        }

    }
}