﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc.ZTTestPointBlock.Competition
{
    public partial class TestPointOptionDlg : BaseForm
    {
        public TestPointOptionDlg()
        {
            InitializeComponent();
            initCbx();
        }
        private TestPointConvergedCondition condition = null;
        private List<TPBlockDisplayColumn> columns = null;
        public void SetOptions(TestPointConvergedCondition condition, List<TPBlockDisplayColumn> columns)
        {
            fillConditionView(condition, 0);
            fillDisplayColumnView(columns, 0);
        }

        private void fillConditionView(TestPointConvergedCondition condition, int selIdx)
        {
            this.condition = condition;
            lvParam.Items.Clear();
            int idx = -1;
            foreach (TPConvergedDetailItem detail in condition.DetailConditionSet)
            {
                idx++;
                ListViewItem item = new ListViewItem(detail.ToString());
                item.Tag = detail;
                lvParam.Items.Add(item);
                item.Selected = idx == selIdx;
            }
            lvParam.Invalidate();
        }

        private void fillDisplayColumnView(List<TPBlockDisplayColumn> columns, int selIdx)
        {
            this.columns = columns;
            lvShow.Items.Clear();
            int idx = -1;
            foreach (TPBlockDisplayColumn col in columns)
            {
                idx++;
                ListViewItem item = new ListViewItem(col.DisplayParam.Name);
                item.Tag = col;
                lvShow.Items.Add(item);
                item.Selected = idx == selIdx;
            }
            lvShow.Invalidate();
        }

        private void initCbx()
        {
            cbxTPSys.Items.Clear();
            cbxShowSys.Items.Clear();
            cbxTPParam.Items.Clear();
            cbxShowParam.Items.Clear();
            cbxTPIdx.Items.Clear();
            cbxShowIdx.Items.Clear();
            foreach (DTDisplayParameterSystem system in DTDisplayParameterManager.GetInstance().Systems)
            {
                if (system.Name == "Common Param")
                {
                    continue;
                }
                cbxTPSys.Items.Add(system);
                cbxShowSys.Items.Add(system);
            }
        }

        private void cbxTPSys_SelectedIndexChanged(object sender, EventArgs e)
        {
            curDetailItem = null;
            fillCbxParams(cbxTPParam, cbxTPSys.SelectedItem);
        }

        private void cbxShowSys_SelectedIndexChanged(object sender, EventArgs e)
        {
            curCol = null;
            fillCbxParams(cbxShowParam, cbxShowSys.SelectedItem);
        }

        private void fillCbxParams(ComboBox cbx, object sys)
        {
            cbx.Items.Clear();
            DTDisplayParameterSystem paramSys = sys as DTDisplayParameterSystem;
            if (sys == null)
            {
                return;
            }
            foreach (DTDisplayParameterInfo param in paramSys.DisplayParamInfos)
            {
                cbx.Items.Add(param);
            }
            if (cbx.Items.Count > 0)
            {
                cbx.SelectedIndex = 0;
            }
        }

        private void cbxTPParam_SelectedIndexChanged(object sender, EventArgs e)
        {
            btnTPAdd.Enabled = true;
            curDetailItem = null;
            DTDisplayParameterInfo disParam = cbxTPParam.SelectedItem as DTDisplayParameterInfo;
            if (disParam == null)
            {
                return;
            }
            numMinValue.ValueChanged -= numMinValue_ValueChanged;
            numMaxValue.ValueChanged -= numMaxValue_ValueChanged;
            numMinValue.Enabled = numMaxValue.Enabled = true;
            numMinValue.Minimum = numMaxValue.Minimum = (decimal)disParam.ValueMin;
            numMinValue.Maximum = numMaxValue.Maximum = (decimal)disParam.ValueMax;
            fillCbxParamIdx(cbxTPIdx, cbxTPParam.SelectedItem);
            numMinValue.ValueChanged += numMinValue_ValueChanged;
            numMaxValue.ValueChanged += numMaxValue_ValueChanged;
        }

        private void cbxShowParam_SelectedIndexChanged(object sender, EventArgs e)
        {
            btnShowAdd.Enabled = true;
            curCol = null;
            DTDisplayParameterInfo disParam = cbxShowParam.SelectedItem as DTDisplayParameterInfo;
            if (disParam == null)
            {
                return;
            }
            radioShowValueType.Enabled = true;
            fillCbxParamIdx(cbxShowIdx, cbxShowParam.SelectedItem);
        }

        private void fillCbxParamIdx(ComboBox cbx, object param)
        {
            cbx.Items.Clear();
            DTDisplayParameterInfo disParam = param as DTDisplayParameterInfo;
            if (param != null)
            {
                if (disParam.IsArray)
                {
                    cbx.Enabled = true;
                    for (int i = 0; i < disParam.ArrayBounds; i++)
                    {
                        cbx.Items.Add(i);
                    }
                    cbx.SelectedIndex = 0;
                }
                else
                {
                    cbx.Enabled = false;
                }
            }
        }

        TPConvergedDetailItem curDetailItem = null;
        private void lvParam_SelectedIndexChanged(object sender, EventArgs e)
        {
            btnTPRemove.Enabled = lvParam.SelectedItems.Count > 0;
            if (lvParam.FocusedItem == null)
            {
                return;
            }
            TPConvergedDetailItem item = lvParam.FocusedItem.Tag as TPConvergedDetailItem;
            cbxTPSys.SelectedItem = DTDisplayParameterManager.GetInstance()[item.SysName];
            cbxTPParam.SelectedItem = DTDisplayParameterManager.GetInstance()[item.SysName][item.ParamName];
            cbxTPIdx.SelectedItem = item.ParamArrayIndex;
            numMinValue.ValueChanged -= numMinValue_ValueChanged;
            numMaxValue.ValueChanged -= numMaxValue_ValueChanged;
            numMinValue.Minimum = (decimal)DTDisplayParameterManager.GetInstance()[item.SysName][item.ParamName].ValueMin;
            numMinValue.Maximum = (decimal)DTDisplayParameterManager.GetInstance()[item.SysName][item.ParamName].ValueMax;
            numMaxValue.Minimum = numMinValue.Minimum;
            numMaxValue.Maximum = numMinValue.Maximum;
            numMinValue.Value = (decimal)item.MinValue;
            numMaxValue.Value = (decimal)item.MaxValue;
            numMinValue.ValueChanged += numMinValue_ValueChanged;
            numMaxValue.ValueChanged += numMaxValue_ValueChanged;
            curDetailItem = item;
        }

        TPBlockDisplayColumn curCol = null;
        private void lvShow_SelectedIndexChanged(object sender, EventArgs e)
        {
            btnShowRemove.Enabled = lvShow.SelectedItems.Count > 0;
            if (lvShow.FocusedItem == null)
            {
                return;
            }
            TPBlockDisplayColumn item = lvShow.FocusedItem.Tag as TPBlockDisplayColumn;
            cbxShowSys.SelectedItem = item.DisplayParam.System;
            cbxShowParam.SelectedItem = item.DisplayParam;
            cbxShowIdx.SelectedItem = item.ParamArrayIndex;
            radioShowValueType.SelectedIndexChanged -= radioShowValueType_SelectedIndexChanged;
            radioShowValueType.SelectedIndex = (int)item.ValueType;
            radioShowValueType.SelectedIndexChanged += radioShowValueType_SelectedIndexChanged;
            curCol = item;
        }

        private void btnTPAdd_Click(object sender, EventArgs e)
        {
            if (numMinValue.Value > numMaxValue.Value)
            {
                MessageBox.Show("指标的最小值不能大于最大值！请重新设置！");
                return;
            }
            TPConvergedDetailItem item = new TPConvergedDetailItem();
            item.SysName = cbxTPSys.SelectedItem.ToString();
            item.ParamName = cbxTPParam.SelectedItem.ToString();
            if (cbxTPIdx.Enabled)
            {
                item.ParamArrayIndex = int.Parse(cbxTPIdx.SelectedItem.ToString());
            }
            if (condition.Contains(item.SysName, item.ParamName, item.ParamArrayIndex))
            {
                return;
            }
            item.MinValue = (double)numMinValue.Value;
            item.MaxValue = (double)numMaxValue.Value;
            condition.DetailConditionSet.Add(item);
            fillConditionView(condition, condition.DetailConditionSet.Count - 1);
        }

        private void btnTPRemove_Click(object sender, EventArgs e)
        {
            if (lvParam.SelectedItems.Count == 0)
            {
                return;
            }
            foreach (ListViewItem item in lvParam.SelectedItems)
            {
                TPConvergedDetailItem detail = item.Tag as TPConvergedDetailItem;
                if (detail != null)
                {
                    condition.DetailConditionSet.Remove(detail);
                }
            }
            fillConditionView(condition, 0);
        }

        private void btnShowAdd_Click(object sender, EventArgs e)
        {
            DTDisplayParameterInfo param = cbxShowParam.SelectedItem as DTDisplayParameterInfo;
            if (param == null)
            {
                MessageBox.Show("参数选择有误！");
                return;
            }
            int idx = -1;
            if (cbxShowIdx.Enabled)
            {
                int.TryParse(cbxShowIdx.SelectedItem.ToString(), out idx);
            }
            foreach (TPBlockDisplayColumn item in columns)
            {
                if (item.DisplayParam == param && item.ParamArrayIndex == idx && (int)item.ValueType == radioShowValueType.SelectedIndex)
                {
                    return;
                }
            }
            TPBlockDisplayColumn col = new TPBlockDisplayColumn(param, idx, (ESummaryValueType)radioShowValueType.SelectedIndex);
            columns.Add(col);
            fillDisplayColumnView(columns, columns.Count - 1);
        }

        private void btnShowRemove_Click(object sender, EventArgs e)
        {
            foreach (ListViewItem item in lvShow.Items)
            {
                TPBlockDisplayColumn col = item.Tag as TPBlockDisplayColumn;
                if (col != null)
                {
                    columns.Remove(col);
                }
            }
            fillDisplayColumnView(columns, 0);
        }

        private void btnHide_Click(object sender, EventArgs e)
        {
            Hide();
        }

        private void TestPointOptionDlg_FormClosing(object sender, FormClosingEventArgs e)
        {
            e.Cancel = true;
            Hide();
        }

        private void numMinValue_ValueChanged(object sender, EventArgs e)
        {
            if (curDetailItem != null)
            {
                curDetailItem.MinValue = (double)numMinValue.Value;
                //lvParam.RedrawItems(0, lvParam.Items.Count - 1, true);
                lvParam.Invalidate();
            }
        }

        private void numMaxValue_ValueChanged(object sender, EventArgs e)
        {
            if (curDetailItem != null)
            {
                curDetailItem.MaxValue = (double)numMaxValue.Value;
                //lvParam.RedrawItems(0, lvParam.Items.Count - 1, true);
                lvParam.Invalidate();
            }
        }

        private void radioShowValueType_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (curCol != null)
            {
                curCol.ValueType = (ESummaryValueType)radioShowValueType.SelectedIndex;
                //lvShow.RedrawItems(0, lvShow.Items.Count - 1, true);
                lvShow.Invalidate();
            }
        }

        private void cbxTPIdx_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (curDetailItem != null)
            {
                curDetailItem.ParamArrayIndex = int.Parse(cbxTPIdx.SelectedItem.ToString());
                lvParam.RedrawItems(0, lvParam.Items.Count - 1, true);
                lvParam.Invalidate();
            }
        }

        private void cbxShowIdx_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (curCol != null)
            {
                curCol.ParamArrayIndex = int.Parse(cbxShowIdx.SelectedItem.ToString());
                //lvShow.RedrawItems(0, lvShow.Items.Count - 1, true);
                lvShow.Invalidate();
            }
        }

        private void lvParam_DrawItem(object sender, DrawListViewItemEventArgs e)
        {
            if ((e.State & ListViewItemStates.Selected) != 0)
            {
                e.Graphics.FillRectangle(Brushes.Orange, e.Bounds);
                e.DrawFocusRectangle();
            }
            else
            {
                using (LinearGradientBrush brush =
                    new LinearGradientBrush(e.Bounds, Color.Pink,
                    Color.Pink, LinearGradientMode.Horizontal))
                {
                    e.Graphics.FillRectangle(brush, e.Bounds);
                }
            }
            if (e.Item.Tag != null)
            {
                e.Graphics.DrawString(e.Item.Tag.ToString(), lvParam.Font, Brushes.Black, e.Bounds);
            }
        }

        private void lvShow_DrawItem(object sender, DrawListViewItemEventArgs e)
        {
            if ((e.State & ListViewItemStates.Selected) != 0)
            {
                e.Graphics.FillRectangle(Brushes.Orange, e.Bounds);
                e.DrawFocusRectangle();
            }
            else
            {
                using (LinearGradientBrush brush =
                    new LinearGradientBrush(e.Bounds, Color.Pink,
                    Color.Pink, LinearGradientMode.Horizontal))
                {
                    e.Graphics.FillRectangle(brush, e.Bounds);
                }
            }
            if (e.Item.Tag != null)
            {
                e.Graphics.DrawString(e.Item.Tag.ToString(), lvShow.Font, Brushes.Black, e.Bounds);
            }
        }



    }

}
