﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc.KPISheet
{
    public class ReportColunm
    {
        public string FullName
        {
            get
            {
                if (tbCol==null)
                {
                    return this.Name;
                }
                return tbCol.FullName;
            }
        }

        public string Name
        {
            get;
            set;
        }

        public bool IsFrozen
        {
            get;
            set;
        }

        TableColumn tbCol = null;
        public TableColumn TableCol
        {
            get { return tbCol; }
        }
        public ReportColunm(TableColumn tbCol)
            : this()
        {
            this.tbCol = tbCol;
            this.Name = tbCol.Name;
        }

        public ReportColunm()
        {
            IsStaticColor = true;
            CellDynamicBkColorRanges = new List<Model.DTParameterRangeColor>();
        }

        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> dic = new Dictionary<string, object>();
                dic["Name"] = this.Name;
                dic["ColHashName"] = this.tbCol.HashName;
                dic["IsFrozen"] = this.IsFrozen;
                dic["ColHeaderBkColor"] = ColHeaderBkColor.ToArgb();
                dic["IsStaticColor"] = IsStaticColor;
                dic["CellStaticBkColor"] = CellStaticBkColor.ToArgb();
                dic["ValueRangeMin"] = ValueRangeMin;
                dic["ValueRangeMax"] = ValueRangeMax;
                dic["CellDynamicBkColorRanges"] = CellDynamicBkColorRanges;
                List<object> colorParam = new List<object>();
                if (CellDynamicBkColorRanges != null)
                {
                    foreach (DTParameterRangeColor item in CellDynamicBkColorRanges)
                    {
                        colorParam.Add(item.Params);
                    }
                }
                dic["ColorParam"] = colorParam;
                return dic;
            }
            set
            {
                this.Name = value["Name"] as string;
                string hashName = value["ColHashName"] as string;
                this.tbCol = TableCfgManager.Instance.GetTableColumn(hashName);
                this.IsFrozen = (bool)value["IsFrozen"];
                ColHeaderBkColor = Color.FromArgb((int)value["ColHeaderBkColor"]);
                IsStaticColor = (bool)value["IsStaticColor"];
                CellStaticBkColor = Color.FromArgb((int)value["CellStaticBkColor"]);
                ValueRangeMin = (double)value["ValueRangeMin"];
                ValueRangeMax = (double)value["ValueRangeMax"];

                List<object> colorParam = value["ColorParam"] as List<object>;
                if (colorParam != null)
                {
                    this.CellDynamicBkColorRanges = new List<DTParameterRangeColor>();
                    foreach (Dictionary<string, object> item in colorParam)
                    {
                        DTParameterRangeColor color = new DTParameterRangeColor();
                        color.Params = item;
                        this.CellDynamicBkColorRanges.Add(color);
                    }
                }
            }
        }


        public System.Drawing.Color ColHeaderBkColor { get; set; }
        public bool IsStaticColor
        { get; set; }

        public System.Drawing.Color CellStaticBkColor { get; set; }

        public double ValueRangeMin { get; set; }

        public double ValueRangeMax { get; set; }

        public List<DTParameterRangeColor> CellDynamicBkColorRanges { get; set; }
    }
}
