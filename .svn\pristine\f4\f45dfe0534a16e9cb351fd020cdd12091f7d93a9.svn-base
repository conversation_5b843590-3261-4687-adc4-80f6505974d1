﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTDIYQuerLteHighirskAnaSetForm : BaseForm
    {
        public ZTDIYQuerLteHighirskAnaSetForm()
        {
            InitializeComponent();
        }

        public void GetSettingFilterRet(ref HighriskCond highHriskCond)
        {
            try
            {
                highHriskCond.curOverCoverRxlev = (int)numOverCoverFilter.Value;
                highHriskCond.curOverCoverSampleCount = int.Parse(numOverCoverSampleCount.Text.ToString());
                highHriskCond.curOverCoverMinDistance = (int)numOverCoverDistanceMin.Value;
                highHriskCond.curOverCoverMaxDistance = (int)numOverCoverDistanceMax.Value;
                highHriskCond.curOverCoverPercent = (float)numOverCoverRate.Value;
                highHriskCond.curOverCoverDisFactor = (float)numOverCoverRadiusRatio.Value;
                highHriskCond.curOverCoverInterfereRSRP = (int)numOverCoverInterfereRSRP.Value;

                highHriskCond.curWeakCoverMeanRSRP = (int)numWeakCoverMeanRSRP.Value;
                highHriskCond.curWeakCoverPercent = (float)numWeakCoverRate.Value;
                highHriskCond.curWeakCoverRxlev = (int)numWeakCoverRsrp.Value;
                highHriskCond.curWeakCoverSampleCount = (int)numWeakCoverSampleCount.Value;
            }
            catch (Exception ex)
            {
                MessageBox.Show("配置项有异常，请检查 " + ex.ToString());
            }
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }
    }
}