﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;


namespace MasterCom.RAMS.ZTFunc.LteHttpPageOrVideoPlay
{
    public class LteCommonParm
    {
        public int SN
        {
            get;
            set;
        }
        public string FileName
        {
            get;
            set;
        }
        public DateTime BeginTime { get; set; }
        public DateTime EndTime { get; set; }
        public double BeginLongitude
        {
            get;
            set;
        }
        public double BeginLatitude
        {
            get;
            set;
        }
        public double EndLongitude
        {
            get;
            set;
        }
        public double EndLatitude
        {
            get;
            set;
        }
        public string BeginTimeStr
        {
            get
            {
                return BeginTime.ToString("yy-MM-dd HH:mm:ss.fff");
            }
        }
        public string EndTimeStr
        {
            get
            {
                return EndTime.ToString("yy-MM-dd HH:mm:ss.fff");
            }
        }
        public List<TestPoint> TpsList { get; set; }        //相关采样点
        public List<KeyValuePair<TestPoint,int>> StatTpsList { get; set; }     //需要统计的采样点及其对应的最强邻区索引 <Tp,maxNBIndex>
        public List<int> MaxNBIndexList { get; set; }        //最强邻区采样点索引表

        public int TestPointsCount
        {
            get
            {
                return TpsList.Count;
            }
        }

        public float? RsrpMax    //Rsrp最大值
        {
            get;
            protected set;
        }
        public float? RsrpMin    //Rsrp最小值
        {
            get;
            protected set;
        }
        public double? RsrpAvg   //Rsrp平均值
        {
            get;
            protected set;
        }
        public float? MaxNbRsrpMax   //最强邻区Rsrp最大值
        {
            get;
            protected set;
        }
        public float? MaxNbRsrpMin   //最强邻区Rsrp最小值
        {
            get;
            protected set;
        }
        public double? MaxNbRsrpAvg   //最强邻区Rsrp平均值
        {
            get;
            protected set;
        }
        public float? SinrMax    //Sinr最大值
        {
            get;
            protected set;
        }
        public float? SinrMin    //Sinr最小值
        {
            get;
            protected set;
        }
        public double? SinrAvg   //Sinr平均值
        {
            get;
            protected set;
        }
        public double? AppSpeedMbAvg   //AppSpeed平均值
        {
            get;
            protected set;
        }
        public string TacCIStr
        {
            get;
            protected set;
        }
        public string CellNameStr
        {
            get;
            protected set;
        }

        /// <summary>
        /// 获取常用LTE网络参数
        /// </summary>
        public virtual void FillItem(string rsrpStr, string sinrStr, string tacStr, string appSpeedStr, string nCellRsrpStr)
        {
            float rsrpSum = 0;
            float maxNbRsrpSum = 0;
            float sinrSum = 0;
            double appSpeedMbSum = 0;
            int rsrpCount = 0;
            int sinrCount = 0;
            int maxNbRsrpCount = 0;
            int appSpeedMbCount = 0;
            MaxNBIndexList = new List<int>();

            foreach (TestPoint tp in TpsList)
            {
                float? rsrp = (float?)tp[rsrpStr];
                float? sinr = (float?)tp[sinrStr];
                double? appSpeedMb = (double?)tp[appSpeedStr];
                float? maxNbRsrp = findMaxNBCell(nCellRsrpStr, tp, MaxNBIndexList);
                setRsrp(ref rsrpSum, ref rsrpCount, rsrp);
                setSinr(ref sinrSum, ref sinrCount, sinr);
                if (appSpeedMb != null)                       //统计appSpeed
                {
                    appSpeedMbCount++;
                    appSpeedMbSum += (double)appSpeedMb;
                }
                setMaxRsrp(ref maxNbRsrpSum, ref maxNbRsrpCount, maxNbRsrp);
            }
            if (rsrpCount != 0)
            {
                RsrpAvg = Math.Round(rsrpSum / (double)rsrpCount, 3);
            }
            if (sinrCount != 0)
            {
                SinrAvg = Math.Round(sinrSum / (double)sinrCount, 3);
            }
            if (appSpeedMbCount != 0)
            {
                AppSpeedMbAvg = Math.Round(appSpeedMbSum / (double)appSpeedMbCount, 3);
            }
            if (maxNbRsrpCount != 0)
            {
                MaxNbRsrpAvg = Math.Round(maxNbRsrpSum / (double)maxNbRsrpCount, 3);
            }
            fillCellInfo(tacStr);
        }

        private void setRsrp(ref float rsrpSum, ref int rsrpCount, float? rsrp)
        {
            if (rsrp != null)                           //统计rsrp
            {
                if (RsrpMax == null || (float)rsrp > RsrpMax)
                {
                    RsrpMax = (float)rsrp;
                }
                if (RsrpMin == null || (float)rsrp < RsrpMin)
                {
                    RsrpMin = (float)rsrp;
                }
                rsrpCount++;
                rsrpSum += (float)rsrp;
            }
        }

        private void setSinr(ref float sinrSum, ref int sinrCount, float? sinr)
        {
            if (sinr != null)                           //统计sinr
            {
                if (SinrMax == null || (float)sinr > SinrMax)
                {
                    SinrMax = (float)sinr;
                }
                if (SinrMin == null || (float)sinr < SinrMin)
                {
                    SinrMin = (float)sinr;
                }
                sinrCount++;
                sinrSum += (float)sinr;
            }
        }

        private void setMaxRsrp(ref float maxNbRsrpSum, ref int maxNbRsrpCount, float? maxNbRsrp)
        {
            if (maxNbRsrp != null)                  //统计最强邻区rsrp
            {
                if (MaxNbRsrpMax == null || (float)maxNbRsrp > MaxNbRsrpMax)
                {
                    MaxNbRsrpMax = (float)maxNbRsrp;
                }
                if (MaxNbRsrpMin == null || (float)maxNbRsrp < MaxNbRsrpMin)
                {
                    MaxNbRsrpMin = (float)maxNbRsrp;
                }
                maxNbRsrpCount++;
                maxNbRsrpSum += (float)maxNbRsrp;
            }
        }

        /// <summary>
        /// 找出最强邻区的rsrp值
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        protected float? findMaxNBCell(string nCellRsrpStr, TestPoint tp, List<int> maxNBIndexList)
        {
            float nCellRsrpMax = float.MinValue;
            int maxNBIndex = -1;
            for (int i = 0; i < 10; i++)
            {
                float? rsrp = (float?)tp[nCellRsrpStr, i];
                if (rsrp == null || rsrp < -141 || rsrp > 25)
                {
                    continue;
                }
                if (rsrp > nCellRsrpMax)
                {
                    nCellRsrpMax = (float)rsrp;
                    maxNBIndex = i;
                }
            }
            if (nCellRsrpMax != float.MinValue)
            {
                maxNBIndexList.Add(maxNBIndex);
                return nCellRsrpMax;
            }
            else
            {
                maxNBIndexList.Add(maxNBIndex);
                return null;
            }
        }


        /// <summary>
        /// 填充小区信息
        /// </summary>
        /// <param name="tacStr"></param>
        protected void fillCellInfo(string tacStr)
        {
            List<string> tacciList = new List<string>();
            List<string> cellNameList = new List<string>();
            foreach (TestPoint tp in TpsList)
            {
                dealTP(tacStr, tacciList, cellNameList, tp);
            }
            if (tacciList.Count != 0)
            {
                TacCIStr = gatherStringListToString(tacciList);
            }
            if (cellNameList.Count != 0)
            {
                CellNameStr = gatherStringListToString(cellNameList);
            }
        }

        private void dealTP(string tacStr, List<string> tacciList, List<string> cellNameList, TestPoint tp)
        {
            int? tac = (int?)(ushort?)tp[tacStr];
            if (tac != null)
            {
                LTECell lteCell = getMainCell(tp, tacStr);
                if (lteCell != null)
                {
                    string tacci = tac.ToString() + "_" + lteCell.SCellID.ToString();
                    if (!tacciList.Contains(tacci))
                    {
                        tacciList.Add(tacci);
                    }
                    if (!cellNameList.Contains(lteCell.Name))
                    {
                        cellNameList.Add(lteCell.Name);
                    }
                }
            }
        }

        /// <summary>
        /// 字符串表合成字符串
        /// </summary>
        /// <param name="stringList"></param>
        /// <returns></returns>
        private string gatherStringListToString(List<string> stringList)
        {
            StringBuilder strbDes = new StringBuilder();
            foreach (string des in stringList)
            {
                strbDes.Append(des + "|");
            }
            if (strbDes.Length > 0)
            {
                strbDes.Remove(strbDes.Length - 1, 1);
            }
            return strbDes.ToString();
        }
        /// <summary>
        /// 获取采样点的主服小区
        /// </summary>
        /// <param name="testPoint"></param>
        /// <returns></returns>
        private LTECell getMainCell(TestPoint testPoint, string tacStr)
        {
            if (tacStr == "lte_fdd_TAC")
            {
                return testPoint.GetMainCell_LTE_FDD();
            }
            else
            {
                return testPoint.GetMainCell_LTE();
            }
        }
    }
}
