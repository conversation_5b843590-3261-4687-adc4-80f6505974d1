﻿using System;
using System.Collections.Generic;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using MasterCom.RAMS.BackgroundFunc;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTWeakCovRoadInfo
    {
        public int SN { get; set; }
        public int FileID { get; set; }

        private string fileName = "";
        public string FileName
        {
            get { return fileName; }
        }
        public string CityName { get; set; }
        public float RxLevMax { get; set; }
        public float RxLevMin { get; set; }
        public float RxLevAvg { get; set; }
        public List<TestPoint> SampleList { get; set; } = new List<TestPoint>();
        public List<double> LongitudeList { get; set; } = new List<double>();
        public List<double> LatitudeList { get; set; } = new List<double>();
        public List<string> LacciList { get; set; } = new List<string>();

        protected double distance = 0;
        public double Distance
        {
            get { return distance; }
        }
        public double DistanceShow
        {
            get { return Math.Round(distance, 2); }
        }
        
        public string RoadName { get; set; } = "";

        private string areaName = "";
        public string AreaName
        {
            get { return areaName; }
        }

        private string gridName = "";
        public string GridName
        {
            get { return gridName; }
        }

        private string areaAgentName = "";
        public string AreaAgentName
        {
            get { return areaAgentName; }
        }

        private int second;
        public int Second
        {
            get { return second; }
        }

        private double longitudeMid;
        public double LongitudeMid
        {
            get { return longitudeMid; }
        }

        private double latitudeMid;
        public double LatitudeMid
        {
            get { return latitudeMid; }
        }

        private double longitudeStart;
        public double LongitudeStart
        {
            get { return longitudeStart; }
        }

        private double latitudeStart;
        public double LatitudeStart
        {
            get { return latitudeStart; }
        }

        private double longitudeEnd;
        public double LongitudeEnd
        {
            get { return longitudeEnd; }
        }

        private double latitudeEnd;
        public double LatitudeEnd
        {
            get { return latitudeEnd; }
        }
        
        public int Istime { get; set; }
        public int Ietime { get; set; }
        public int SampleCount { get; set; }
        public float SampleCountOp { get; set; }
        public float SampleCountPlan { get; set; }
        public string CellInfos { get; set; } = "";
        public string MotorWay { get; set; } = "";

        public void Add(float rxlev, double distance, TestPoint tp)
        {
            if (SampleList.Count == 0)     //first
            {
                RxLevMax = rxlev;
                RxLevMin = rxlev;
                RxLevAvg = rxlev;
            }
            else
            {
                if (RxLevMax < rxlev)
                {
                    RxLevMax = rxlev;
                }
                if (RxLevMin > rxlev)
                {
                    RxLevMin = rxlev;
                }

                RxLevAvg += rxlev;
                this.distance += distance;
            }
            int? lac = null;
            int? ci = null;
            if (tp is TDTestPointDetail)
            {
                lac = (int?)tp[MainModel.TD_SCell_LAC];
                ci = (int?)tp[MainModel.TD_SCell_CI];
            }
            else
            {
                lac = (int?)tp["LAC"];
                ci = (int?)tp["CI"];
            }
            if (lac != null && ci != null && ci != -255)
            {
                string lacci = lac.ToString() + "|" + ci.ToString();
                if (!LacciList.Contains(lacci))
                {
                    LacciList.Add(lacci);
                }
            }
            SampleList.Add(tp);
        }

        public virtual void GetResult(bool saveTestPoints)
        {
            FileID = SampleList[0].FileID;
            fileName = SampleList[0].FileName;
            second = SampleList[SampleList.Count - 1].Time - SampleList[0].Time;
            longitudeMid = SampleList[(SampleList.Count / 2)].Longitude;
            latitudeMid = SampleList[(SampleList.Count / 2)].Latitude;
            longitudeStart = SampleList[0].Longitude;
            latitudeStart = SampleList[0].Latitude;
            longitudeEnd = SampleList[SampleList.Count - 1].Longitude;
            latitudeEnd = SampleList[SampleList.Count - 1].Latitude;
            Istime = SampleList[0].Time;
            Ietime = SampleList[SampleList.Count - 1].Time;
            SampleCount = SampleList.Count;
            RxLevAvg = (float)Math.Round((double)RxLevAvg / (double)SampleList.Count, 2);

            if (LacciList.Count > 0)
            {
                StringBuilder sb = new StringBuilder(CellInfos);
                foreach (string lacci in LacciList)
                {
                    sb.Append(lacci + ";");
                }
                CellInfos = sb.ToString();
            }
            if (!saveTestPoints)
            {
                foreach (TestPoint tp in SampleList)
                {
                    LongitudeList.Add(tp.Longitude);
                    LatitudeList.Add(tp.Latitude);
                }
                SampleList.Clear();
            }
            RoadName = GISManager.GetInstance().GetRoadPlaceDesc(LongitudeMid, LatitudeMid);
            setAreaName();
            setGridName();
            setAreaAgentName();
        }

        private void setAreaName()
        {
            string strAreaName = GISManager.GetInstance().GetAreaPlaceDesc(LongitudeMid, LatitudeMid);
            if (strAreaName != null)
            {
                if (areaName == null || areaName == "")
                {
                    areaName = strAreaName;
                }
                else
                {
                    if (!areaName.Contains(strAreaName) && strAreaName != "")
                    {
                        areaName += "," + strAreaName;
                    }
                }
            }
        }

        private void setGridName()
        {
            string strGridName = GISManager.GetInstance().GetGridDesc(LongitudeMid, LatitudeMid);
            if (strGridName != null)
            {
                if (gridName == null || gridName == "")
                {
                    gridName = strGridName;
                }
                else
                {
                    if (!gridName.Contains(strGridName) && strGridName != "")
                    {
                        gridName += "," + strGridName;
                    }
                }
            }
        }

        private void setAreaAgentName()
        {
            string strAreaAgentName = GISManager.GetInstance().GetAreaAgentDesc(LongitudeMid, LatitudeMid);
            if (strAreaAgentName != null)
            {
                if (areaAgentName == null || areaAgentName == "")
                {
                    areaAgentName = strAreaAgentName;
                }
                else
                {
                    if (!areaAgentName.Contains(strAreaAgentName) && strAreaAgentName != "")
                    {
                        areaAgentName += "," + strAreaAgentName;
                    }
                }
            }
        }

        public BackgroundResult ConvertToBackgroundResult()
        {
            BackgroundResult bgResult = new BackgroundResult();
            bgResult.FileID = FileID;
            bgResult.FileName = fileName;
            bgResult.LongitudeStart = LongitudeStart;
            bgResult.LatitudeStart = LatitudeStart;
            bgResult.LongitudeMid = LongitudeMid;
            bgResult.LatitudeMid = LatitudeMid;
            bgResult.LongitudeEnd = LongitudeEnd;
            bgResult.LatitudeEnd = LatitudeEnd;
            bgResult.ISTime = Istime;
            bgResult.IETime = Ietime;
            bgResult.DistanceLast = Distance;
            bgResult.SampleCount = SampleCount;
            bgResult.RxLevMean = RxLevAvg;
            bgResult.RxLevMin = RxLevMin;
            bgResult.RxLevMax = RxLevMax;
            bgResult.RoadDesc = RoadName;
            bgResult.AreaDesc = areaName;
            bgResult.GridDesc = gridName;
            bgResult.AreaAgentDesc = areaAgentName;
            bgResult.ProjectString = BackgroundFuncBaseSetting.GetInstance().projectType;

            bgResult.AddImageValue((float)Math.Round(100.0 * SampleCountOp / SampleCount, 2));
            bgResult.AddImageValue((float)Math.Round(100.0 * SampleCountPlan / SampleCount, 2));
            bgResult.AddImageValue(CellInfos);
            bgResult.AddImageValue(bgResult.ProjectString);

            getImage(bgResult);
            return bgResult;
        }
        public void SetMotorWay(int areaID, int areaTypeID)
        {
            MotorWay = AreaManager.GetInstance().GetAreaDesc(areaTypeID, areaID);    
        }
        protected virtual void getImage(BackgroundResult bgResult)
        {
        }
    }
}