﻿namespace MasterCom.RAMS.NOP
{
    partial class FlowDiagramPanel
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.toolBar = new DevExpress.XtraBars.Bar();
            this.SuspendLayout();
            // 
            // toolBar
            // 
            this.toolBar.BarName = "toolBar";
            this.toolBar.DockCol = 0;
            this.toolBar.DockRow = 0;
            this.toolBar.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            this.toolBar.OptionsBar.MultiLine = true;
            this.toolBar.OptionsBar.UseWholeRow = true;
            this.toolBar.Text = "工具栏";
            // 
            // FlowDiagramPanel
            // 
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Inherit;
            this.AutoScroll = true;
            this.BackColor = System.Drawing.Color.White;
            this.Name = "FlowDiagramPanel";
            this.Size = new System.Drawing.Size(655, 385);
            this.MouseClick += new System.Windows.Forms.MouseEventHandler(this.thisPanel_MouseClick);
            this.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.thisPanel_MouseDoubleClick);
            this.MouseDown += new System.Windows.Forms.MouseEventHandler(this.thisPanel_MouseDown);
            this.MouseMove += new System.Windows.Forms.MouseEventHandler(this.thisPanel_MouseMove);
            this.MouseUp += new System.Windows.Forms.MouseEventHandler(this.thisPanel_MouseUp);
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraBars.Bar toolBar;


    }
}
