﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.ZTFunc;
using MasterCom.RAMS.ZTFunc.ZTLteTestAcceptance;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using Excel = Microsoft.Office.Interop.Excel;

namespace MasterCom.RAMS.BackgroundFunc
{
    class NbIotExportOutdoorBtsReportHelper : ExportOutdoorBtsReportBase
    {
        public static readonly string WorkDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "userdata/BTS");
        protected static string getTargetFile(string btsName, int cellCount, string saveFolder)
        {
            if (cellCount > 6)
            {
                throw (new Exception(string.Format("基站{0}小区数超过6个，不支持报告导出", btsName)));
            }

            string templateFile = "NBIOT单站验证模板表.xlsx";
            templateFile = Path.Combine(WorkDir, templateFile);

            string targetFile = string.Format("NBIOT新站验收_{0}.xlsx", btsName);
            targetFile = Path.Combine(saveFolder, targetFile);
            if (File.Exists(targetFile))
            {
                File.Delete(targetFile);
            }
            File.Copy(templateFile, targetFile);
            return targetFile;
        }
        public static bool ExportReports(NbIotOutDoorBtsAcceptInfo btsInfo, NbIotBtsWorkParam btsWorkParamInfo)
        {
            if (MainModel.GetInstance().BackgroundStopRequest)
            {
                return false;
            }

            Excel.Application xlApp = null;
            try
            {
                NbIotStationAcceptAutoSet funcSet = NbIotStationAcceptAna.GetInstance().FuncSet;
                string folderPath = Path.Combine(funcSet.ReportSavePath, btsWorkParamInfo.DateDes);
                if (!Directory.Exists(folderPath))
                {
                    Directory.CreateDirectory(folderPath);
                }
                string targetFile = getTargetFile(btsInfo.BtsName, btsInfo.NBIOTBts.Cells.Count, folderPath);
                reportInfo(string.Format("开始导出 {0} 站点的单验报告", btsInfo.BtsName));

                xlApp = new Excel.Application();
                xlApp.Visible = false;
                Excel.Workbook eBook = xlApp.Workbooks.Open(targetFile);

                fillHomePage(eBook, btsWorkParamInfo, btsInfo);
                fillParameterPage(eBook, btsInfo);
                fillKpiPage(eBook, btsInfo);
                fillCoverPicPage(eBook, btsInfo);

                eBook.Save();
                eBook.Close(Type.Missing, Type.Missing, Type.Missing);

                NbIotStationAcceptAna.GetInstance().ReportFilePaths.Add(targetFile);
                reportInfo(string.Format("成功导出 {0} 站点的单验报告。", btsInfo.BtsName));
                return true;
            }
            catch (Exception ex)
            {
                reportError(ex);
                return false;
            }
            finally
            {
                if (xlApp != null)
                {
                    xlApp.Quit();
                }
            }
        }

        //填充报告首页-宏站验收记录单
        protected static void fillHomePage(Excel.Workbook eBook, NbIotBtsWorkParam btsWorkParamInfo
            , NbIotOutDoorBtsAcceptInfo btsInfo)
        {
            LTEBTS srcLteBts = btsInfo.NBIOTBts;
            Excel.Worksheet homePageSheet = (Excel.Worksheet)eBook.Sheets[1];
            string districtName = DistrictManager.GetInstance().getDistrictName(MainModel.GetInstance().DistrictID);

            #region 基站描述及基站参数
            homePageSheet.get_Range("e7").set_Value(Type.Missing, srcLteBts.Name);
            homePageSheet.get_Range("e9").set_Value(Type.Missing, srcLteBts.BTSID);
            homePageSheet.get_Range("z7").set_Value(Type.Missing, DateTime.Now.ToString("yyyy-MM-dd"));
            homePageSheet.get_Range("z9").set_Value(Type.Missing, districtName);
            #endregion

            homePageSheet.get_Range("n31").set_Value(Type.Missing, btsInfo.IsCoverAccordDes);
            homePageSheet.get_Range("n32").set_Value(Type.Missing, btsInfo.IsAttachAccordDes);
            homePageSheet.get_Range("n33").set_Value(Type.Missing, btsInfo.IsRRCAccordDes);
            homePageSheet.get_Range("n34").set_Value(Type.Missing, btsInfo.IsPingAccordDes);
            homePageSheet.get_Range("n35").set_Value(Type.Missing, btsInfo.IsThroughputAccordDes);

            homePageSheet.get_Range("h44").set_Value(Type.Missing, btsInfo.IsAccordAcceptStr);//验收结论
            homePageSheet.get_Range("a47").set_Value(Type.Missing, btsInfo.NotAccordKpiDes);//未通过验收的原因
        }

        //填充报告第二页-参数验证
        protected static void fillParameterPage(Excel.Workbook eBook, NbIotOutDoorBtsAcceptInfo btsInfo)
        {
            LTEBTS srcLteBts = btsInfo.NBIOTBts;
            Excel.Worksheet kpiPageSheet = (Excel.Worksheet)eBook.Sheets[2];
            #region 基站参数(工程)
            kpiPageSheet.get_Range("h5").set_Value(Type.Missing, srcLteBts.Longitude);
            kpiPageSheet.get_Range("h6").set_Value(Type.Missing, srcLteBts.Latitude);
            if (srcLteBts.Cells.Count > 0)
            {
                kpiPageSheet.get_Range("h7").set_Value(Type.Missing, srcLteBts.Cells[0].TAC);
            }
            kpiPageSheet.get_Range("h8").set_Value(Type.Missing, srcLteBts.BTSID);
            #endregion

            #region 小区参数
            int cellIndex = 0;
            foreach (LTECell icell in srcLteBts.Cells)
            {
                int cellParamColIndex = 8 + (cellIndex * 8);//小区参数列：首个小区所在列号为8，其他小区列号以8递增
                kpiPageSheet.Cells[11, cellParamColIndex] = icell.Name;
                kpiPageSheet.Cells[14, cellParamColIndex] = icell.CellID;
                kpiPageSheet.Cells[15, cellParamColIndex] = icell.PCI;
                kpiPageSheet.Cells[16, cellParamColIndex] = icell.EARFCN;
                kpiPageSheet.Cells[20, cellParamColIndex] = icell.Altitude;
                kpiPageSheet.Cells[21, cellParamColIndex] = icell.Direction;
                kpiPageSheet.Cells[22, cellParamColIndex] = icell.Downward;
                cellIndex++;
            }
            #endregion
        }

        //填充报告第三页-性能验收测试表格
        protected static void fillKpiPage(Excel.Workbook eBook, NbIotOutDoorBtsAcceptInfo btsInfo)
        {
            LTEBTS srcLteBts = btsInfo.NBIOTBts;
            Excel.Worksheet kpiPageSheet = (Excel.Worksheet)eBook.Sheets[3];
            kpiPageSheet.get_Range("b3").set_Value(Type.Missing, srcLteBts.Name);
            kpiPageSheet.get_Range("d3").set_Value(Type.Missing, srcLteBts.BTSID);
            kpiPageSheet.get_Range("f3").set_Value(Type.Missing, DateTime.Now.ToString("yyyy-MM-dd"));

            int cellIndex = 0;
            foreach (LTECell icell in srcLteBts.Cells)
            {
                NbIotOutDoorCellAcceptInfo cellInfo;
                if (btsInfo.CellsAcceptDic.TryGetValue(icell.CellID, out cellInfo))
                {
                    int rowIndex = 8 + (cellIndex * 5);//首个小区所在行号为8，其他小区行号以5递增
                    kpiPageSheet.Cells[rowIndex, 3] = cellInfo.AttachRate.AvgRSRPStr;
                    kpiPageSheet.Cells[rowIndex, 4] = cellInfo.AttachRate.AvgSINRStr;
                    kpiPageSheet.Cells[rowIndex, 5] = cellInfo.AttachRate.SuccessRateStr;
                    kpiPageSheet.Cells[rowIndex, 6] = cellInfo.AttachRate.IsAccordDes;
                    rowIndex++;
                    kpiPageSheet.Cells[rowIndex, 3] = cellInfo.RRCRate.AvgRSRPStr;
                    kpiPageSheet.Cells[rowIndex, 4] = cellInfo.RRCRate.AvgSINRStr;
                    kpiPageSheet.Cells[rowIndex, 5] = cellInfo.RRCRate.SuccessRateStr;
                    kpiPageSheet.Cells[rowIndex, 6] = cellInfo.RRCRate.IsAccordDes;
                    rowIndex++;
                    kpiPageSheet.Cells[rowIndex, 3] = cellInfo.PingDelay.AvgRSRPStr;
                    kpiPageSheet.Cells[rowIndex, 4] = cellInfo.PingDelay.AvgSINRStr;
                    kpiPageSheet.Cells[rowIndex, 5] = cellInfo.PingDelay.DataStr;
                    kpiPageSheet.Cells[rowIndex, 6] = cellInfo.PingDelay.IsAccordDes;
                    rowIndex++;
                    kpiPageSheet.Cells[rowIndex, 3] = cellInfo.ULThroughput.AvgRSRPStr;
                    kpiPageSheet.Cells[rowIndex, 4] = cellInfo.ULThroughput.AvgSINRStr;
                    kpiPageSheet.Cells[rowIndex, 5] = cellInfo.ULThroughput.DataStr;
                    kpiPageSheet.Cells[rowIndex, 6] = cellInfo.ULThroughput.IsAccordDes;
                    rowIndex++;
                    kpiPageSheet.Cells[rowIndex, 3] = cellInfo.DLThroughput.AvgRSRPStr;
                    kpiPageSheet.Cells[rowIndex, 4] = cellInfo.DLThroughput.AvgSINRStr;
                    kpiPageSheet.Cells[rowIndex, 5] = cellInfo.DLThroughput.DataStr;
                    kpiPageSheet.Cells[rowIndex, 6] = cellInfo.DLThroughput.IsAccordDes;
                }
                cellIndex++;
            }
        }
        protected static void insertCoverPic(Excel.Workbook eBook, string coverPicPath, string colIndex)
        {
            if (File.Exists(coverPicPath))
            {
                NBIotAcpAutoCoverPicture.InsertExcelPicture(eBook, colIndex, coverPicPath);
            }
        }
        
        //填充报告第四页-站点验收覆盖效果图
        protected static void fillCoverPicPage(Excel.Workbook eBook, NbIotOutDoorBtsAcceptInfo btsInfo)
        {
            Excel.Worksheet coverPicPageSheet = (Excel.Worksheet)eBook.Sheets[4];
            LTEBTS srcLteBts = btsInfo.NBIOTBts;
            coverPicPageSheet.get_Range("b3").set_Value(Type.Missing, srcLteBts.Name);
            coverPicPageSheet.get_Range("d3").set_Value(Type.Missing, srcLteBts.BTSID);
            coverPicPageSheet.get_Range("f3").set_Value(Type.Missing, DateTime.Now.ToString("yyyy-MM-dd"));

            int rowIndex = 8;
            foreach (LTECell cell in srcLteBts.Cells)
            {
                NbIotOutDoorCellAcceptInfo cellInfo;
                if (btsInfo.CellsAcceptDic.TryGetValue(cell.CellID, out cellInfo))
                {
                    insertCoverPic(eBook, cellInfo.CoverProperty.CoverPicPath_Rsrp, "b" + rowIndex.ToString());
                    insertCoverPic(eBook, cellInfo.CoverProperty.CoverPicPath_Sinr, "f" + rowIndex.ToString());
                    insertCoverPic(eBook, cellInfo.CoverProperty.CoverPicPath_UL, "j" + rowIndex.ToString());
                    coverPicPageSheet.Cells[rowIndex, 14] = cellInfo.CoverProperty.AvgRSRPStr;
                    coverPicPageSheet.Cells[rowIndex, 15] = cellInfo.CoverProperty.AvgSINRStr;
                    coverPicPageSheet.Cells[rowIndex, 16] = cellInfo.CoverProperty.CoverAvgSpeedStr;
                    coverPicPageSheet.Cells[rowIndex, 17] = cellInfo.CoverProperty.CoverSpeedUpTenStr;
                    coverPicPageSheet.Cells[rowIndex, 18] = cellInfo.CoverProperty.CoverCoverRateStr;
                }
                rowIndex++;
            }
        }
        //填充报告第五页-站点后台指标监控
        protected static void fillFusionKpiPage(Excel.Workbook eBook, BtsFusionInfo_QH btsFusionInfo)
        {
            if (btsFusionInfo == null)
            {
                return;
            }
            Excel.Worksheet bgKpiPageSheet = (Excel.Worksheet)eBook.Sheets[5];

            int dateIndex = 0;
            int increaseRowCount = btsFusionInfo.BtsWorkParamInfo.CellWorkParams.Count > 3 ? 6 : 3;
            DateTime curDate = btsFusionInfo.BeginTime.Date;
            while (curDate <= btsFusionInfo.EndTime.Date)
            {
                string dateDes = BtsFusionInfoBase.GetDateKeyDes(curDate);
                int cellIndex = 0;
                foreach (CellWorkParamBase cellWorkParam in btsFusionInfo.BtsWorkParamInfo.CellWorkParams)
                {
                    int perfRowIndex = 4 + (increaseRowCount * dateIndex) + cellIndex;
                    bgKpiPageSheet.Cells[perfRowIndex, 1] = dateDes;
                    bgKpiPageSheet.Cells[perfRowIndex, 2] = cellWorkParam.CellName;

                    #region 性能数据
                    addFushionData(btsFusionInfo, bgKpiPageSheet, dateDes, cellWorkParam, perfRowIndex);
                    #endregion

                    #region MR数据
                    addMRData(btsFusionInfo, bgKpiPageSheet, dateDes, cellWorkParam, perfRowIndex);
                    #endregion

                    cellIndex++;
                }
                dateIndex++;
                curDate = curDate.AddDays(1);
            }

            #region 告警数据
            addAlarmData(btsFusionInfo, bgKpiPageSheet);
            #endregion
        }

        private static void addAlarmData(BtsFusionInfo_QH btsFusionInfo, Excel.Worksheet bgKpiPageSheet)
        {
            int alarmRowIndex = 26;
            if (btsFusionInfo.BtsWorkParamInfo.CellWorkParams.Count > 3)
            {
                alarmRowIndex = 47;
            }
            if (btsFusionInfo.BtsAlarmInfoDic != null && btsFusionInfo.BtsAlarmInfoDic.Count > 0)
            {
                bgKpiPageSheet.Cells[alarmRowIndex, 2] = "有";
                alarmRowIndex += 2;
                foreach (BtsAlarmDataBase alarmData in btsFusionInfo.BtsAlarmInfoDic.Values)
                {
                    bgKpiPageSheet.Cells[alarmRowIndex, 1] = alarmData.AlarmTitle;
                    bgKpiPageSheet.Cells[alarmRowIndex, 2] = alarmData.BeginTimeStr;
                    bgKpiPageSheet.Cells[alarmRowIndex, 3] = alarmData.EndTimeStr;
                    bgKpiPageSheet.Cells[alarmRowIndex, 4] = alarmData.Desc;
                    alarmRowIndex++;
                }
            }
            else
            {
                bgKpiPageSheet.Cells[alarmRowIndex, 2] = "无";
            }
        }

        private static void addMRData(BtsFusionInfo_QH btsFusionInfo, Excel.Worksheet bgKpiPageSheet, string dateDes, CellWorkParamBase cellWorkParam, int perfRowIndex)
        {
            Dictionary<string, CellMRDataBase> date_CellMRDataDic;
            if (btsFusionInfo.CellMRInfoDic.TryGetValue(cellWorkParam.CGI, out date_CellMRDataDic))
            {
                CellMRDataBase cellMrInfo;
                if (date_CellMRDataDic.TryGetValue(dateDes, out cellMrInfo))
                {
                    setFloatValue(bgKpiPageSheet, perfRowIndex, 17, cellMrInfo.MrCoverRate);
                }
            }
        }

        private static void addFushionData(BtsFusionInfo_QH btsFusionInfo, Excel.Worksheet bgKpiPageSheet, string dateDes, CellWorkParamBase cellWorkParam, int perfRowIndex)
        {
            Dictionary<string, CellPerfDataBase> date_cellPerfDataDic;
            if (btsFusionInfo.CellPerfInfoDic.TryGetValue(cellWorkParam.CGI, out date_cellPerfDataDic))
            {
                CellPerfDataBase cellPerfBase;
                if (date_cellPerfDataDic.TryGetValue(dateDes, out cellPerfBase))
                {
                    CellPerfData_QH cellPerfInfo = cellPerfBase as CellPerfData_QH;
                    if (cellPerfInfo != null)
                    {
                        int perfColIndex = 3;//列序号
                        setFloatValue(bgKpiPageSheet, perfRowIndex, perfColIndex++, cellPerfInfo.RrcConnectTryCount);
                        setFloatValue(bgKpiPageSheet, perfRowIndex, perfColIndex++, cellPerfInfo.RrcSetupSuccessRate);
                        setFloatValue(bgKpiPageSheet, perfRowIndex, perfColIndex++, cellPerfInfo.ErabConnectTryCount);
                        setFloatValue(bgKpiPageSheet, perfRowIndex, perfColIndex++, cellPerfInfo.ErabSetupSuccessRate);
                        setFloatValue(bgKpiPageSheet, perfRowIndex, perfColIndex++, cellPerfInfo.WirelessConnectRate);
                        setFloatValue(bgKpiPageSheet, perfRowIndex, perfColIndex++, cellPerfInfo.WirelessDropRate);
                        setFloatValue(bgKpiPageSheet, perfRowIndex, perfColIndex++, cellPerfInfo.ErabDropRate);
                        setFloatValue(bgKpiPageSheet, perfRowIndex, perfColIndex++, cellPerfInfo.InnerHandoverSuccessRate);
                        setFloatValue(bgKpiPageSheet, perfRowIndex, perfColIndex++, cellPerfInfo.PdcpThroughput_UL);
                        setFloatValue(bgKpiPageSheet, perfRowIndex, perfColIndex++, cellPerfInfo.PdcpThroughput_DL);
                        setFloatValue(bgKpiPageSheet, perfRowIndex, perfColIndex++, cellPerfInfo.PdcpThroughputSum);
                        setFloatValue(bgKpiPageSheet, perfRowIndex, perfColIndex++, cellPerfInfo.WirelessConnectRate_QCI1);
                        setFloatValue(bgKpiPageSheet, perfRowIndex, perfColIndex++, cellPerfInfo.ErabDropRate_QCI1);
                        setFloatValue(bgKpiPageSheet, perfRowIndex, perfColIndex, cellPerfInfo.EsrvccHandoverSuccessRate);
                    }
                }
            }
        }
    }

    class NbIotExportIndoorBtsReportHelper : ExportIndoorBtsReportBase
    {
        //室分站预留
        public static bool ExportReports(NBIotInDoorBtsAcceptInfo btsInfo, NbIotBtsWorkParam btsWorkParamInfo)
        {
            return false;
        }
    }
}
