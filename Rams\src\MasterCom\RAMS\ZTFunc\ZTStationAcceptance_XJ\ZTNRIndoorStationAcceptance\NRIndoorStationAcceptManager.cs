﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using Excel = Microsoft.Office.Interop.Excel;

namespace MasterCom.RAMS.ZTFunc.ZTStationAcceptance_XJ
{
    public class NRIndoorStationAcceptManager : NRStationAcceptManager
    {
        protected override string templateFileName { get { return "NR室分验收模板.xlsx"; } }
        protected override string reportFileName { get { return "NR室分验收"; } }
        protected IndoorStationAcceptHelper indoorHelper { get; } = new IndoorStationAcceptHelper();

        public override void SetAcceptCond(StationAcceptConditionBase cond)
        {
            Singleton<NRIndoorStationAcceptConfigHelper>.Instance.LoadConfig();
            workDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "userdata/NRStationAcceptance");
            acceptCond = cond;

            acceptorList = new List<StationAcceptBase>()
            {
                new NRIndoorAcpCellAddRate(),
                new NRIndoorAcpAccRate(),
                new NRIndoorAcpGoodPointFtpDownload(),
                new NRIndoorAcpGoodPointFtpUpload(),
                new NRIndoorAcpBadPointFtpDownload(),
                new NRIndoorAcpBadPointFtpUpload(),
                new NRIndoorAcpEPSFBRate(),
                new NRIndoorAcpEPSFBDelay(),
                new NRIndoorAcpVoNRRate(),
                new NRIndoorAcpPingDelay(),
                new NRIndoorAcpLeakOutLock(),
                new NRIndoorAcpLeakOutScan(),
                new NRIndoorAcpHandoverRate(),
                new NRIndoorAcpCoverPic(),
                new NRIndoorAcpBtsAlarm(),
                new NRIndoorAcpBtsBaseConfig()
            };
            NRStationAcceptAlarmHelper.DealAlarmInfo();
        }

        protected override string getPath()
        {
            return Singleton<NRIndoorStationAcceptConfigHelper>.Instance.GetCurSavePath();
        }

        protected virtual NRCell GetTargetCell(DTFileDataManager fileManager, string btsName, string pciStr)
        {
            if (fileManager.FileName.Contains("扫频") || fileManager.FileName.Contains("锁频"))
            {
                return getTargetCellByFileName(btsName, pciStr);
            }

            NRCell targeCell = null;
            foreach (TestPoint tp in fileManager.TestPoints)
            {
                int? arfcn = (int?)NRTpHelper.NrTpManager.GetEARFCN(tp);
                int? pci = (int?)NRTpHelper.NrTpManager.GetPCI(tp);
                if (arfcn == null || pci == null)
                {
                    continue;
                }
                
                List<NRCell> cellList = StationAcceptCellHelper_XJ.Instance.GetNRCellListByEarfcnPci(tp, arfcn, pci);
                targeCell = getValidCell(btsName, pciStr, cellList);
                if (targeCell != null)
                {
                    break;
                }
            }
            return targeCell;
        }

        private NRCell getTargetCellByFileName(string btsName, string pciStr)
        {
            if (int.TryParse(pciStr, out int pci))
            {
                List<NRCell> cellList = CellManager.GetInstance().GetNRCellsByPCI(DateTime.Now, pci);
                NRCell targeCell = getValidCell(btsName, pciStr, cellList);
                return targeCell;
            }
            return null;
        }

        private NRCell getValidCell(string btsName, string pci, List<NRCell> cellList)
        {
            NRCell targeCell = null;
            foreach (var cell in cellList)
            {
                if (cell.Name.Contains(btsName) && cell.PCI.ToString() == pci
                    && cell.BelongBTS.Type == NRBTSType.Indoor)
                {
                    targeCell = cell;
                    break;
                }
            }

            return targeCell;
        }

        public override void AnalyzeFile(Model.FileInfo fileInfo, DTFileDataManager fileManager)
        {
            try
            {
                string errMsg = indoorHelper.JudgeValidFileName(fileInfo.Name);
                if (!string.IsNullOrEmpty(errMsg))
                {
                    log.Info(errMsg);
                    ErrMsg.AppendLine(errMsg);
                    return;
                }

                NRCell targetCell = GetTargetCell(fileManager, indoorHelper.BtsNameByFileName, indoorHelper.PCIByFileName);
                if (targetCell == null)
                {
                    errMsg = string.Format("文件{0}未找到目标小区;", fileInfo.Name);
                    log.Info(errMsg);
                    ErrMsg.AppendLine(errMsg);
                    return;
                }

                analyzeFile(fileInfo, fileManager, targetCell, indoorHelper.BtsNameByFileName);
            }
            catch (Exception e)
            {
                string errMsg = $"[{fileInfo.Name}]文件分析时产生异常;";
                log.Error(string.Format("{0} : {1}", errMsg, e.StackTrace));
                ErrMsg.AppendLine(errMsg);
            }
        }

        protected void analyzeFile(Model.FileInfo fileInfo, DTFileDataManager fileManager, NRCell targetCell, string fileBtsName)
        {
            NRIndoorStationAcceptCondition curCond = acceptCond as NRIndoorStationAcceptCondition;
            curCond.NRServiceType = NRStationAcceptFileNameHelper.GetServiceName(fileInfo.Name);
            if (curCond.NRServiceType == NRServiceName.NULL)
            {
                string errMsg = $"[{fileInfo.Name}]不为SA或NSA文件";
                log.Debug(errMsg);
                ErrMsg.AppendLine(errMsg);
                return;
            }
            string btsName = targetCell.BelongBTS.Name;
            if (!BtsInfoDic.TryGetValue(btsName, out BtsInfoBase bts))
            {
                bts = initBtsInfo(targetCell, fileBtsName);
                BtsInfoDic.Add(btsName, bts);
            }
            //NRIndoorBtsInfo nrBtsInfo = bts as NRIndoorBtsInfo;
            //nrBtsInfo.Init(curCond.NRServiceType);
            if (!bts.CellInfoDic.TryGetValue(targetCell.Name, out CellInfoBase cell))
            {
                cell = initCellInfo(targetCell);
                bts.CellInfoDic.Add(targetCell.Name, cell);
            }
            NRIndoorCellInfo nrCellInfo = cell as NRIndoorCellInfo;
            nrCellInfo.Init(targetCell, curCond.NRServiceType);

            foreach (StationAcceptBase acp in acceptorList)
            {
                acp.AnalyzeFile(fileInfo, fileManager, bts, cell, curCond);
            }
        }

        protected override BtsInfoBase initBtsInfo(NRCell targetCell, string fileBtsName)
        {
            return new NRIndoorBtsInfo(targetCell.BelongBTS, fileBtsName);
        }

        protected override CellInfoBase initCellInfo(NRCell targetCell)
        {
            return new NRIndoorCellInfo(targetCell);
        }

        protected override void verifyResult()
        {
            //foreach (var btsInfo in BtsInfoDic.Values)
            //{
            //    NRIndoorBtsInfo nrBtsInfo = btsInfo as NRIndoorBtsInfo;
            //    foreach (var cellInfo in nrBtsInfo.CellInfoList)
            //    {
            //        NRIndoorCellInfo nrCellInfo = cellInfo as NRIndoorCellInfo;
            //        if (nrCellInfo.SAInfo?.LeakOutLock.Divisor == 0)
            //        {
            //            nrCellInfo.SAInfo.LeakOutLock.Data = 1;
            //            nrCellInfo.SAInfo.LeakOutLock.IsValid = true;
            //        }
            //        if (nrCellInfo.NSAInfo?.LeakOutLock.Divisor == 0)
            //        {
            //            nrCellInfo.NSAInfo.LeakOutLock.Data = 1;
            //            nrCellInfo.NSAInfo.LeakOutLock.IsValid = true;
            //        }
            //    }
            //}
        }

        protected override void getExportedFiles(StringBuilder exportedFiles, BtsInfoBase bts)
        {
            NRIndoorBtsInfo nrBtsInfo = bts as NRIndoorBtsInfo;
            exportedFiles.Append(nrBtsInfo.BtsName);
            exportedFiles.Append(",");
        }

        protected override void fillResult(BtsInfoBase bts, Excel.Workbook eBook)
        {
            fillHomePage(eBook, bts);
            fillNetOptimizationTestPageSA(eBook, bts);
            fillNetOptimizationTestPageNSA(eBook, bts);
            fillCoverPic(eBook, bts);
            fillAlarmInfo(eBook, bts);
        }

        #region fillHomePage
        private void fillHomePage(Excel.Workbook eBook, BtsInfoBase btsInfo)
        {

            Excel.Worksheet sheet = (Excel.Worksheet)eBook.Sheets[2];
            sheet.get_Range("C5").set_Value(Type.Missing, btsInfo.BtsName);

            NRIndoorBtsInfo nrBtsInfo = btsInfo as NRIndoorBtsInfo;
            if (nrBtsInfo.BtsBaseInfo != null)
            {
                NRIndoorBtsParameters nrBaseInfo = nrBtsInfo.BtsBaseInfo as NRIndoorBtsParameters;

                sheet.get_Range("T5").set_Value(Type.Missing, nrBaseInfo.ENodeBID);
                sheet.get_Range("C7").set_Value(Type.Missing, nrBaseInfo.Address);
                sheet.get_Range("T7").set_Value(Type.Missing, nrBaseInfo.Country);

                setCellValue(sheet.Cells, 11, 6, nrBaseInfo.BtsLongutide.Planing, nrBaseInfo.BtsLongutide.IsValid);
                setCellValue(sheet.Cells, 11, 9, nrBaseInfo.BtsLatitude.Planing, nrBaseInfo.BtsLatitude.IsValid);
                setCellValue(sheet.Cells, 11, 12, nrBaseInfo.BtsLongutide.Real, nrBaseInfo.BtsLongutide.IsValid);
                setCellValue(sheet.Cells, 11, 16, nrBaseInfo.BtsLatitude.Real, nrBaseInfo.BtsLatitude.IsValid);
                setCellValue(sheet.Cells, 11, 19, nrBaseInfo.BtsLongutide.IsValid && nrBaseInfo.BtsLatitude.IsValid);
                setCellValue(sheet.Cells, 12, 6, nrBaseInfo.BBU.Planing, nrBaseInfo.BBU.IsValid);
                setCellValue(sheet.Cells, 12, 12, nrBaseInfo.BBU.Real, nrBaseInfo.BBU.IsValid);
                setCellValue(sheet.Cells, 12, 19, nrBaseInfo.BBU.IsValid);
                setCellValue(sheet.Cells, 13, 6, nrBaseInfo.RRU.Planing, nrBaseInfo.RRU.IsValid);
                setCellValue(sheet.Cells, 13, 12, nrBaseInfo.RRU.Real, nrBaseInfo.RRU.IsValid);
                setCellValue(sheet.Cells, 13, 19, nrBaseInfo.RRU.IsValid);
                setCellValue(sheet.Cells, 14, 6, nrBaseInfo.CellCount.Planing, nrBaseInfo.CellCount.IsValid);
                setCellValue(sheet.Cells, 14, 12, nrBaseInfo.CellCount.Real, nrBaseInfo.CellCount.IsValid);
                setCellValue(sheet.Cells, 14, 19, nrBaseInfo.CellCount.IsValid);
                setCellValue(sheet.Cells, 15, 6, nrBaseInfo.TAC.Planing, nrBaseInfo.TAC.IsValid);
                setCellValue(sheet.Cells, 15, 12, nrBaseInfo.TAC.Real, nrBaseInfo.TAC.IsValid);
                setCellValue(sheet.Cells, 15, 19, nrBaseInfo.TAC.IsValid);

                ExcelCell antennaInfo = new ExcelCell(18, 6, 13, 5);
                setCellResInfo(sheet, antennaInfo, new List<NRIndoorCellParameters>(nrBaseInfo.CellDic.Values), setCellAntennaInfo);
            }

            ExcelCell fileAnaInfo = new ExcelCell(59, 6, 9, 4);
            setCellResInfo(sheet, fileAnaInfo, btsInfo.CellInfoList, setFileAnaResult);
        }

        delegate void SetResFunc<in T>(Excel.Worksheet sheet, int rowIdx, int colIdx, T nrCellInfo);
        private void setCellResInfo<T>(Excel.Worksheet sheet, ExcelCell info, List<T> dataList, SetResFunc<T> func)
        {
            int cellIndex = 0;
            int rowIdx = info.FstRowIdx;
            int colIdx;
            foreach (var cellInfo in dataList)
            {
                if (cellIndex % 3 == 0)
                {
                    rowIdx += info.RowInterval * (cellIndex / 3);
                    colIdx = info.FstColIdx;
                }
                else
                {
                    colIdx = info.FstColIdx + ((cellIndex % 3) * info.ColInterval);
                }

                func(sheet, rowIdx, colIdx, cellInfo);
                cellIndex++;
            }
        }

        private void setCellAntennaInfo(Excel.Worksheet sheet, int rowIdx, int colIdx, NRIndoorCellParameters cellInfo)
        {
            setCellValue(sheet.Cells, rowIdx, colIdx, cellInfo.CellName, true);
            setCellBaseInfo(sheet, rowIdx + 2, colIdx, cellInfo.Longitude);
            setCellBaseInfo(sheet, rowIdx + 3, colIdx, cellInfo.Latitude);
            setCellBaseInfo(sheet, rowIdx + 4, colIdx, cellInfo.CellID);
            setCellBaseInfo(sheet, rowIdx + 5, colIdx, cellInfo.PCI);
            setCellBaseInfo(sheet, rowIdx + 6, colIdx, cellInfo.FreqBand);
            setCellBaseInfo(sheet, rowIdx + 7, colIdx, cellInfo.Freq);
            setCellBaseInfo(sheet, rowIdx + 8, colIdx, cellInfo.SSBFreq);
            setCellBaseInfo(sheet, rowIdx + 9, colIdx, cellInfo.Bandwidth);
            setCellBaseInfo(sheet, rowIdx + 10, colIdx, cellInfo.PRACH);
            setCellBaseInfo(sheet, rowIdx + 11, colIdx, cellInfo.SubFrameRatio);
            setCellBaseInfo(sheet, rowIdx + 12, colIdx, cellInfo.Channels);
        }

        private void setCellBaseInfo<T>(Excel.Worksheet sheet, int rowIdx, int colIdx, ParamInfo<T> info)
        {
            setCellValue(sheet.Cells, rowIdx, colIdx, info.Planing, info.IsValid);
            setCellValue(sheet.Cells, rowIdx, colIdx + 2, info.Real, info.IsValid);
            setCellValue(sheet.Cells, rowIdx, colIdx + 4, info.IsValid);
        }

        private void setFileAnaResult(Excel.Worksheet sheet, int rowIdx, int colIdx, CellInfoBase cellInfo)
        {
            NRIndoorCellInfo nrCellInfo = cellInfo as NRIndoorCellInfo;
            if (nrCellInfo.SAInfo != null)
            {
                setCellValue(sheet.Cells, rowIdx, colIdx, nrCellInfo.SAInfo.GNBAddInfo.IsValid);
                setCellValue(sheet.Cells, rowIdx + 1, colIdx, nrCellInfo.SAInfo.HandOverInfo.IsValid);
                setCellValue(sheet.Cells, rowIdx + 2, colIdx, nrCellInfo.SAInfo.AccessInfo.IsValid);
                setCellValue(sheet.Cells, rowIdx + 3, colIdx, nrCellInfo.SAInfo.SmallPackageDelay.IsValid
             && nrCellInfo.SAInfo.BigPackageDelay.IsValid);
                setCellValue(sheet.Cells, rowIdx + 4, colIdx, nrCellInfo.SAInfo.SampleDL.IsValid);
                setCellValue(sheet.Cells, rowIdx + 5, colIdx, nrCellInfo.SAInfo.SampleUL.IsValid);
                setCellValue(sheet.Cells, rowIdx + 6, colIdx, nrCellInfo.SAInfo.EPSFBInfo.IsValid
                    && nrCellInfo.SAInfo.EPSFBDelay.IsValid);
                setCellValue(sheet.Cells, rowIdx + 7, colIdx, nrCellInfo.SAInfo.LeakOutLock.IsValid);
            }

            colIdx += 2;
            if (nrCellInfo.NSAInfo != null)
            {
                setCellValue(sheet.Cells, rowIdx, colIdx, nrCellInfo.NSAInfo.GNBAddInfo.IsValid);
                setCellValue(sheet.Cells, rowIdx + 1, colIdx, nrCellInfo.NSAInfo.HandOverInfo.IsValid);
                setCellValue(sheet.Cells, rowIdx + 2, colIdx, nrCellInfo.NSAInfo.AccessInfo.IsValid);
                setCellValue(sheet.Cells, rowIdx + 3, colIdx, nrCellInfo.NSAInfo.SmallPackageDelay.IsValid
             && nrCellInfo.NSAInfo.BigPackageDelay.IsValid);
                setCellValue(sheet.Cells, rowIdx + 4, colIdx, nrCellInfo.NSAInfo.SampleDL.IsValid);
                setCellValue(sheet.Cells, rowIdx + 5, colIdx, nrCellInfo.NSAInfo.SampleUL.IsValid);
                setCellValue(sheet.Cells, rowIdx + 6, colIdx, nrCellInfo.NSAInfo.EPSFBInfo.IsValid
                    && nrCellInfo.NSAInfo.EPSFBDelay.IsValid);
                setCellValue(sheet.Cells, rowIdx + 7, colIdx, nrCellInfo.NSAInfo.LeakOutLock.IsValid);
            }
        }

        //private void fillHomePage(Excel.Workbook eBook, BtsInfoBase btsInfo)
        //{
        //    Excel.Worksheet sheet = (Excel.Worksheet)eBook.Sheets[2];
        //    sheet.get_Range("C5").set_Value(Type.Missing, btsInfo.BtsName);
        //    sheet.get_Range("T5").set_Value(Type.Missing, btsInfo.BtsID);

        //    int cellIndex = 1;
        //    int rowIdx = 61;
        //    int colIdx;
        //    for (int i = 0; i < btsInfo.CellInfoList.Count; i++)
        //    {
        //        NRIndoorCellInfo nrCellInfo = btsInfo.CellInfoList[i] as NRIndoorCellInfo;
        //        if (cellIndex % 3 == 0)
        //        {
        //            rowIdx += 9 * (cellIndex / 3);
        //            colIdx = 6;
        //        }
        //        else
        //        {
        //            colIdx = 6 + ((cellIndex % 3) * 4);
        //        }

        //        setFileAnaResult(sheet, rowIdx, colIdx, nrCellInfo);
        //        cellIndex++;
        //    }

        //}

        //private void setFileAnaResult(Excel.Worksheet sheet, int rowIdx, int colIdx, NRIndoorCellInfo nrCellInfo)
        //{
        //    if (nrCellInfo.SAInfo != null)
        //    {
        //        setCellValue(sheet.Cells, rowIdx, colIdx, nrCellInfo.SAInfo.GNBAddInfo.IsValid);
        //        setCellValue(sheet.Cells, rowIdx + 1, colIdx, nrCellInfo.SAInfo.HandOverInfo.IsValid);
        //        setCellValue(sheet.Cells, rowIdx + 2, colIdx, nrCellInfo.SAInfo.AccessInfo.IsValid);
        //        setCellValue(sheet.Cells, rowIdx + 3, colIdx, nrCellInfo.SAInfo.SmallPackageDelay.IsValid
        //            && nrCellInfo.SAInfo.BigPackageDelay.IsValid);
        //        setCellValue(sheet.Cells, rowIdx + 4, colIdx, nrCellInfo.SAInfo.SampleDL.IsValid);
        //        setCellValue(sheet.Cells, rowIdx + 5, colIdx, nrCellInfo.SAInfo.SampleUL.IsValid);
        //        setCellValue(sheet.Cells, rowIdx + 6, colIdx, nrCellInfo.SAInfo.EPSFBInfo.IsValid
        //            && nrCellInfo.SAInfo.EPSFBDelay.IsValid);
        //        setCellValue(sheet.Cells, rowIdx + 7, colIdx, nrCellInfo.SAInfo.LeakOutLock.IsValid);
        //    }

        //    colIdx += 2;
        //    if (nrCellInfo.NSAInfo != null)
        //    {
        //        setCellValue(sheet.Cells, rowIdx, colIdx, nrCellInfo.NSAInfo.GNBAddInfo.IsValid);
        //        setCellValue(sheet.Cells, rowIdx + 1, colIdx, nrCellInfo.NSAInfo.HandOverInfo.IsValid);
        //        setCellValue(sheet.Cells, rowIdx + 2, colIdx, nrCellInfo.NSAInfo.AccessInfo.IsValid);
        //        setCellValue(sheet.Cells, rowIdx + 3, colIdx, nrCellInfo.NSAInfo.SmallPackageDelay.IsValid
        //            && nrCellInfo.NSAInfo.BigPackageDelay.IsValid);
        //        setCellValue(sheet.Cells, rowIdx + 4, colIdx, nrCellInfo.NSAInfo.SampleDL.IsValid);
        //        setCellValue(sheet.Cells, rowIdx + 5, colIdx, nrCellInfo.NSAInfo.SampleUL.IsValid);
        //        setCellValue(sheet.Cells, rowIdx + 6, colIdx, nrCellInfo.NSAInfo.EPSFBInfo.IsValid
        //            && nrCellInfo.NSAInfo.EPSFBDelay.IsValid);
        //        setCellValue(sheet.Cells, rowIdx + 7, colIdx, nrCellInfo.NSAInfo.LeakOutLock.IsValid);
        //    }
        //}
        #endregion

        #region fillNetOptimizationTestPage
        private void fillNetOptimizationTestPageSA(Excel.Workbook eBook, BtsInfoBase btsInfo)
        {
            Excel.Worksheet sheet = (Excel.Worksheet)eBook.Sheets[3];
            sheet.get_Range("A1").set_Value(Type.Missing, btsInfo.BtsName + "-NR单站验证测试表格");
            sheet.get_Range("C2").set_Value(Type.Missing, btsInfo.BtsName);
            sheet.get_Range("S2").set_Value(Type.Missing, btsInfo.BtsID);
            sheet.get_Range("AC2").set_Value(Type.Missing, DateTime.Now.ToShortDateString());

            int cellIndex = 0;
            foreach (var cellInfo in btsInfo.CellInfoList)
            {
                NRIndoorCellInfo nrCellInfo = cellInfo as NRIndoorCellInfo;
                if (nrCellInfo.SAInfo == null)
                {
                    continue;
                }

                int rowIdx = 7 + (cellIndex * 21);

                setCellValue(sheet.Cells, rowIdx, 1, nrCellInfo.SAInfo.Cell.Name, true);

                //5G连接建立成功率（gNB添加成功率）
                rowIdx = rowIdx + 1;
                setSuccessRateKpiInfo(sheet, rowIdx, nrCellInfo.SAInfo.GNBAddInfo);

                //Access Success Rate
                rowIdx = rowIdx + 1;
                setSuccessRateKpiInfo(sheet, rowIdx, nrCellInfo.SAInfo.AccessInfo);

                //Ping
                rowIdx = rowIdx + 2;
                setCellValue(sheet.Cells, rowIdx, 16, nrCellInfo.SAInfo.SmallPackageDelay.Data, nrCellInfo.SAInfo.SmallPackageDelay.IsValid);
                setCellValue(sheet.Cells, rowIdx, 29, nrCellInfo.SAInfo.BigPackageDelay.Data, nrCellInfo.SAInfo.BigPackageDelay.IsValid);

                //FTP
                rowIdx = rowIdx + 2;
                setFtpPointInfo(sheet, rowIdx, 16, nrCellInfo.SAInfo.SampleDL);
                setFtpPointInfo(sheet, rowIdx + 3, 16, nrCellInfo.SAInfo.SampleUL);

                //5G切换成功率
                rowIdx = rowIdx + 8;
                setSuccessRateKpiInfo(sheet, rowIdx, nrCellInfo.SAInfo.HandOverInfo);

                //室分外泄
                rowIdx = rowIdx + 2;
                setCellValue(sheet.Cells, rowIdx, 16, nrCellInfo.SAInfo.LeakOutLock.Data, nrCellInfo.SAInfo.LeakOutLock.IsValid);

                //EPSFB_端到端呼叫成功率
                rowIdx = rowIdx + 2;
                setSuccessRateKpiInfo(sheet, rowIdx, nrCellInfo.SAInfo.EPSFBInfo);

                //EPSFB_呼叫时延(5G-5G)
                rowIdx = rowIdx + 1;
                setCellValue(sheet.Cells, rowIdx, 16, nrCellInfo.SAInfo.EPSFBDelay.Data, nrCellInfo.SAInfo.EPSFBDelay.IsValid);

                //接通率(VONR-VONR)
                rowIdx = rowIdx + 1;
                setSuccessRateKpiInfo(sheet, rowIdx, nrCellInfo.SAInfo.VONRInfo);

                cellIndex++;
            }
        }

        private void fillNetOptimizationTestPageNSA(Excel.Workbook eBook, BtsInfoBase btsInfo)
        {
            Excel.Worksheet sheet = (Excel.Worksheet)eBook.Sheets[4];
            sheet.get_Range("A1").set_Value(Type.Missing, btsInfo.BtsName + "-NR单站验证测试表格");
            sheet.get_Range("C2").set_Value(Type.Missing, btsInfo.BtsName);
            sheet.get_Range("S2").set_Value(Type.Missing, btsInfo.BtsID);
            sheet.get_Range("AC2").set_Value(Type.Missing, DateTime.Now.ToShortDateString());

            int cellIndex = 0;
            foreach (var cellInfo in btsInfo.CellInfoList)
            {
                NRIndoorCellInfo nrCellInfo = cellInfo as NRIndoorCellInfo;
                if (nrCellInfo.NSAInfo == null)
                {
                    continue;
                }

                int rowIdx = 7 + (cellIndex * 18);

                setCellValue(sheet.Cells, rowIdx, 1, nrCellInfo.NSAInfo.Cell.Name, true);

                //5G连接建立成功率（gNB添加成功率）
                rowIdx = rowIdx + 1;
                setSuccessRateKpiInfo(sheet, rowIdx, nrCellInfo.NSAInfo.GNBAddInfo);

                //Access Success Rate
                rowIdx = rowIdx + 1;
                setSuccessRateKpiInfo(sheet, rowIdx, nrCellInfo.NSAInfo.AccessInfo);

                //Ping
                rowIdx = rowIdx + 2;
                setCellValue(sheet.Cells, rowIdx, 16, nrCellInfo.NSAInfo.SmallPackageDelay.Data, nrCellInfo.NSAInfo.SmallPackageDelay.IsValid);
                setCellValue(sheet.Cells, rowIdx, 29, nrCellInfo.NSAInfo.BigPackageDelay.Data, nrCellInfo.NSAInfo.BigPackageDelay.IsValid);

                //FTP
                rowIdx = rowIdx + 2;
                setFtpPointInfo(sheet, rowIdx, 16, nrCellInfo.NSAInfo.SampleDL);
                setFtpPointInfo(sheet, rowIdx + 3, 16, nrCellInfo.NSAInfo.SampleUL);

                //5G切换成功率
                rowIdx = rowIdx + 8;
                setSuccessRateKpiInfo(sheet, rowIdx, nrCellInfo.NSAInfo.HandOverInfo);

                //室分外泄
                rowIdx = rowIdx + 2;
                setCellValue(sheet.Cells, rowIdx, 16, nrCellInfo.NSAInfo.LeakOutLock.Data, nrCellInfo.NSAInfo.LeakOutLock.IsValid);

                cellIndex++;
            }
        }

        private void setFtpPointInfo(Excel.Worksheet sheet, int rowIdx, int colIdx, FtpPointInfo info)
        {
            setCellValue(sheet.Cells, rowIdx, colIdx, info.Rsrp.Data, info.Rsrp.IsValid);
            setCellValue(sheet.Cells, rowIdx + 1, colIdx, info.Sinr.Data, info.Sinr.IsValid);
            setCellValue(sheet.Cells, rowIdx + 2, colIdx, info.Throughput.Data, info.Throughput.IsValid);
        }

        private void setSuccessRateKpiInfo(Excel.Worksheet sheet, int rowIdx, SuccessRateKpiInfo rate)
        {
            setCellValue(sheet.Cells, rowIdx, 16, rate.RequestCnt, rate.IsValid);
            setCellValue(sheet.Cells, rowIdx, 23, rate.SucceedCnt, rate.IsValid);
            setCellValue(sheet.Cells, rowIdx, 29, rate.FailedCnt, rate.IsValid);
            setCellValue(sheet.Cells, rowIdx, 35, rate.SuccessRate, rate.IsValid);
        }
        #endregion

        private void fillCoverPic(Excel.Workbook eBook, BtsInfoBase btsInfo)
        {
            NRIndoorBtsInfo nrbtsInfo = btsInfo as NRIndoorBtsInfo;
            if (nrbtsInfo.BtsInfo == null)
            {
                return;
            }
            
            InsertExcelPicture(eBook, "B9", nrbtsInfo.BtsInfo.RsrpPicInfo.PicPath);
            InsertExcelPicture(eBook, "C9", nrbtsInfo.BtsInfo.SinrPicInfo.PicPath);
            InsertExcelPicture(eBook, "D9", nrbtsInfo.BtsInfo.PciPicInfo.PicPath);
            InsertExcelPicture(eBook, "E9", nrbtsInfo.BtsInfo.ULPicInfo.PicPath);
            InsertExcelPicture(eBook, "F9", nrbtsInfo.BtsInfo.DLPicInfo.PicPath);
            InsertExcelPicture(eBook, "G9", nrbtsInfo.BtsInfo.LeakOutPicInfo.PicPath);
        }

        public void InsertExcelPicture(Excel.Workbook eBook, string startCell, string picPath)
        {
            if (string.IsNullOrEmpty(picPath))
            {
                return;
            }

            Excel.Worksheet eSheet = (Excel.Worksheet)eBook.Sheets[5];
            Excel.Range rng = eSheet.get_Range(startCell, Type.Missing);

            double width = eBook.Application.CentimetersToPoints(10.93);
            double height = eBook.Application.CentimetersToPoints(7.14);
            eSheet.Shapes.AddPicture(picPath,
                Microsoft.Office.Core.MsoTriState.msoFalse,
                Microsoft.Office.Core.MsoTriState.msoCTrue,
                (float)(double)rng.Left, (float)(double)rng.Top,
                (float)width, (float)height);
        }

        private void fillAlarmInfo(Excel.Workbook eBook, BtsInfoBase btsInfo)
        {
            Excel.Worksheet sheet = (Excel.Worksheet)eBook.Sheets[7];

            NRIndoorBtsInfo nrBtsInfo = btsInfo as NRIndoorBtsInfo;
            string date = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            if (nrBtsInfo.BtsAlarmList == null || nrBtsInfo.BtsAlarmList.Count == 0)
            {
                setCellValue(sheet.Cells, 3, 1, 1, true);
                setCellValue(sheet.Cells, 3, 2, date, true);
                setCellValue(sheet.Cells, 3, 3, nrBtsInfo.BtsName, true);
                setCellValue(sheet.Cells, 3, 4, "无告警信息", true);
                return;
            }

            for (int i = 0; i < nrBtsInfo.BtsAlarmList.Count; i++)
            {
                int idx = 3 + i;
                setCellValue(sheet.Cells, idx, 1, i, true);
                setCellValue(sheet.Cells, idx, 2, date, true);
                setCellValue(sheet.Cells, idx, 3, nrBtsInfo.BtsName, true);
                setCellValue(sheet.Cells, idx, 4, nrBtsInfo.BtsAlarmList[i], true);
            }
        }

        protected override void clear()
        {
            foreach (var acp in acceptorList)
            {
                acp.Clear();
            }
        }
    }
}
