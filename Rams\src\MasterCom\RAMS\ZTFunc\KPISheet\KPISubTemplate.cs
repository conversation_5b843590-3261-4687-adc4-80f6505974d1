﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.KPISheet
{
    public class KPISubTemplate
    {
        public KPISubTemplate()
        { }
        public KPISubTemplate(string templateName)
        {
            this.templateName = templateName;
        }

        public string templateName
        {
            get;
            set;
        }

        public override string ToString()
        {
            return this.templateName;
        }

        /// <summary>
        /// 子模板
        /// </summary>
        private List<KPIColumnOptions> subTemplateList = new List<KPIColumnOptions>();

        public List<KPIColumnOptions> SubTemplateList
        {
            get { return subTemplateList; }
        }

        //
        public Dictionary<string, object> CfgParam
        {
            get
            {
                Dictionary<string, object> paramDic = new Dictionary<string, object>();
                paramDic["TemplateName"] = this.templateName;
                List<object> displayParams = new List<object>();
                foreach (KPIColumnOptions col in this.subTemplateList)
                {
                    displayParams.Add(col.CfgParam);
                }
                paramDic["SubTemplateNameDic"] = displayParams;
                return paramDic;
            }
            set
            {
                if (value == null)
                {
                    return;
                }
                this.templateName = value["TemplateName"].ToString();
                subTemplateList = new List<KPIColumnOptions>();
                List<object> list = value["SubTemplateNameDic"] as List<object>;
                foreach (object objParam in list)
                {
                    KPIColumnOptions col = new KPIColumnOptions();
                    col.CfgParam = objParam as Dictionary<string, object>;
                    subTemplateList.Add(col);
                }
            }
        }

        public string ParamKey
        {
            get
            {
                return string.Format("{0}", templateName);
            }
        }

    }
}
