﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class LowSpeedCellDlg
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.btnOK = new DevExpress.XtraEditors.SimpleButton();
            this.btnCancel = new DevExpress.XtraEditors.SimpleButton();
            this.chkSaveTestPoint = new DevExpress.XtraEditors.CheckEdit();
            this.numMinSpeed = new DevExpress.XtraEditors.SpinEdit();
            this.numMaxRxLev = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.numMaxSpeed = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.numMinRxLev = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            ((System.ComponentModel.ISupportInitialize)(this.chkSaveTestPoint.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMinSpeed.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxRxLev.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxSpeed.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMinRxLev.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // btnOK
            // 
            this.btnOK.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnOK.Appearance.Options.UseFont = true;
            this.btnOK.DialogResult = System.Windows.Forms.DialogResult.OK;
            this.btnOK.Location = new System.Drawing.Point(103, 126);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(72, 23);
            this.btnOK.TabIndex = 2;
            this.btnOK.Text = "确定";
            // 
            // btnCancel
            // 
            this.btnCancel.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnCancel.Appearance.Options.UseFont = true;
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnCancel.Location = new System.Drawing.Point(198, 126);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(72, 23);
            this.btnCancel.TabIndex = 3;
            this.btnCancel.Text = "取消";
            // 
            // chkSaveTestPoint
            // 
            this.chkSaveTestPoint.Location = new System.Drawing.Point(23, 86);
            this.chkSaveTestPoint.Name = "chkSaveTestPoint";
            this.chkSaveTestPoint.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.chkSaveTestPoint.Properties.Appearance.Options.UseFont = true;
            this.chkSaveTestPoint.Properties.Caption = "保留采样点";
            this.chkSaveTestPoint.Size = new System.Drawing.Size(101, 19);
            this.chkSaveTestPoint.TabIndex = 10;
            // 
            // numMinSpeed
            // 
            this.numMinSpeed.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.numMinSpeed.Location = new System.Drawing.Point(25, 45);
            this.numMinSpeed.Name = "numMinSpeed";
            this.numMinSpeed.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numMinSpeed.Properties.Appearance.Options.UseFont = true;
            this.numMinSpeed.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numMinSpeed.Properties.Increment = new decimal(new int[] {
            50,
            0,
            0,
            0});
            this.numMinSpeed.Properties.IsFloatValue = false;
            this.numMinSpeed.Properties.Mask.EditMask = "N00";
            this.numMinSpeed.Size = new System.Drawing.Size(71, 20);
            this.numMinSpeed.TabIndex = 20;
            // 
            // numMaxRxLev
            // 
            this.numMaxRxLev.EditValue = new decimal(new int[] {
            10,
            0,
            0,
            -2147483648});
            this.numMaxRxLev.Location = new System.Drawing.Point(167, 18);
            this.numMaxRxLev.Name = "numMaxRxLev";
            this.numMaxRxLev.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numMaxRxLev.Properties.Appearance.Options.UseFont = true;
            this.numMaxRxLev.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numMaxRxLev.Properties.IsFloatValue = false;
            this.numMaxRxLev.Properties.Mask.EditMask = "N00";
            this.numMaxRxLev.Properties.MaxValue = new decimal(new int[] {
            10,
            0,
            0,
            -2147483648});
            this.numMaxRxLev.Properties.MinValue = new decimal(new int[] {
            120,
            0,
            0,
            -2147483648});
            this.numMaxRxLev.Size = new System.Drawing.Size(71, 20);
            this.numMaxRxLev.TabIndex = 19;
            // 
            // labelControl4
            // 
            this.labelControl4.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl4.Appearance.Options.UseFont = true;
            this.labelControl4.Location = new System.Drawing.Point(246, 48);
            this.labelControl4.Name = "labelControl4";
            this.labelControl4.Size = new System.Drawing.Size(24, 12);
            this.labelControl4.TabIndex = 18;
            this.labelControl4.Text = "Kbps";
            // 
            // labelControl3
            // 
            this.labelControl3.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl3.Appearance.Options.UseFont = true;
            this.labelControl3.Location = new System.Drawing.Point(245, 21);
            this.labelControl3.Name = "labelControl3";
            this.labelControl3.Size = new System.Drawing.Size(18, 12);
            this.labelControl3.TabIndex = 17;
            this.labelControl3.Text = "dBm";
            // 
            // numMaxSpeed
            // 
            this.numMaxSpeed.EditValue = new decimal(new int[] {
            80,
            0,
            0,
            0});
            this.numMaxSpeed.Location = new System.Drawing.Point(167, 45);
            this.numMaxSpeed.Name = "numMaxSpeed";
            this.numMaxSpeed.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numMaxSpeed.Properties.Appearance.Options.UseFont = true;
            this.numMaxSpeed.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numMaxSpeed.Properties.Increment = new decimal(new int[] {
            50,
            0,
            0,
            0});
            this.numMaxSpeed.Properties.IsFloatValue = false;
            this.numMaxSpeed.Properties.Mask.EditMask = "N00";
            this.numMaxSpeed.Size = new System.Drawing.Size(71, 20);
            this.numMaxSpeed.TabIndex = 15;
            // 
            // labelControl2
            // 
            this.labelControl2.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl2.Appearance.Options.UseFont = true;
            this.labelControl2.Location = new System.Drawing.Point(103, 49);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(60, 12);
            this.labelControl2.TabIndex = 16;
            this.labelControl2.Text = "≤ 速率 ≤";
            // 
            // numMinRxLev
            // 
            this.numMinRxLev.EditValue = new decimal(new int[] {
            85,
            0,
            0,
            -2147483648});
            this.numMinRxLev.Location = new System.Drawing.Point(25, 18);
            this.numMinRxLev.Name = "numMinRxLev";
            this.numMinRxLev.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numMinRxLev.Properties.Appearance.Options.UseFont = true;
            this.numMinRxLev.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numMinRxLev.Properties.IsFloatValue = false;
            this.numMinRxLev.Properties.Mask.EditMask = "N00";
            this.numMinRxLev.Properties.MaxValue = new decimal(new int[] {
            10,
            0,
            0,
            -2147483648});
            this.numMinRxLev.Properties.MinValue = new decimal(new int[] {
            120,
            0,
            0,
            -2147483648});
            this.numMinRxLev.Size = new System.Drawing.Size(71, 20);
            this.numMinRxLev.TabIndex = 13;
            // 
            // labelControl1
            // 
            this.labelControl1.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl1.Appearance.Options.UseFont = true;
            this.labelControl1.Location = new System.Drawing.Point(102, 21);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(60, 12);
            this.labelControl1.TabIndex = 14;
            this.labelControl1.Text = "≤ 场强 ≤";
            // 
            // LowSpeedCellDlg
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(298, 174);
            this.Controls.Add(this.numMinSpeed);
            this.Controls.Add(this.numMaxRxLev);
            this.Controls.Add(this.labelControl4);
            this.Controls.Add(this.labelControl3);
            this.Controls.Add(this.numMaxSpeed);
            this.Controls.Add(this.labelControl2);
            this.Controls.Add(this.numMinRxLev);
            this.Controls.Add(this.labelControl1);
            this.Controls.Add(this.chkSaveTestPoint);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.Name = "LowSpeedCellDlg";
            this.Text = "低速率小区条件设置";
            ((System.ComponentModel.ISupportInitialize)(this.chkSaveTestPoint.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMinSpeed.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxRxLev.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxSpeed.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMinRxLev.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraEditors.SimpleButton btnOK;
        private DevExpress.XtraEditors.SimpleButton btnCancel;
        private DevExpress.XtraEditors.CheckEdit chkSaveTestPoint;
        private DevExpress.XtraEditors.SpinEdit numMinSpeed;
        private DevExpress.XtraEditors.SpinEdit numMaxRxLev;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.SpinEdit numMaxSpeed;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.SpinEdit numMinRxLev;
        private DevExpress.XtraEditors.LabelControl labelControl1;
    }
}