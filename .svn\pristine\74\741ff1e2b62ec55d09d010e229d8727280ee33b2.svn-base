﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;

namespace MasterCom.RAMS.ZTFunc.ZTPerformanceParamNew
{
    public partial class FormNameConfirm : DevExpress.XtraEditors.XtraForm
    {
        public FormNameConfirm()
        {
            InitializeComponent();
        }
        public string name { get; set; }

        //确定
        private void buttonOK_Click(object sender, EventArgs e)
        {
            if (this.textEdit1.Text.Trim() != "")
            {
                name = this.textEdit1.Text;
                DialogResult = DialogResult.OK;
            }
            else
            {
                XtraMessageBox.Show("模板名称不能为空!");
            }
        }

        //取消
        private void buttonCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
        }
    }
}