﻿using System;
using System.Collections.Generic;
using System.Text;

namespace DBDataViewer
{
    public abstract class StatDataConverterBase
    {
        public abstract DataItem ParseByte(byte[] dataBuffer, ref int offset);
    }

    public static class StatDataConverterCreater
    {
        private const byte NewKpiMax = 0xC8;

        //高位为1，且枚举未达到201，即为新指标；否则为旧指标
        public static StatDataConverterBase CreateConverter(byte kpiType)
        {
            if ((kpiType & 0x80) > 0 && kpiType <= NewKpiMax)
            {
                return new StatDataNewConverter();
            }
            return new StatDataOldConverter();
        }
    }

    public class StatDataNewConverter : StatDataConverterBase
    {
        public override DataItem ParseByte(byte[] dataBuffer, ref int offset)
        {
            if (dataBuffer.Length < offset + 2)
                return null;

            byte len = (byte)(dataBuffer[offset + 1] >> 4);
            if (dataBuffer.Length < offset + len)
                return null;

            NewKpiFormat dataItem =  NewKpiFormat.GetNewKpi(dataBuffer, ref offset);

            byte dataType = dataBuffer[offset];
            offset++;
            dataItem.Value = DataValueGetter.GetValue(dataBuffer, ref offset, dataType);

            return dataItem;
        }
    }

    public class StatDataOldConverter : StatDataConverterBase
    {
        public override DataItem ParseByte(byte[] dataBuffer, ref int offset)
        {
            DataItem item = null;
            int pIdx = offset;
            while (pIdx < dataBuffer.Length - 4)
            {
                if (dataBuffer[pIdx].Equals((byte)0))
                {
                    item = new DataItem(ByteOpHelper.ByteToOXStr(dataBuffer, offset, pIdx - offset));
                    byte dataType = dataBuffer[pIdx + 1];
                    offset = pIdx + 2;
                    item.Value = DataValueGetter.GetValue(dataBuffer, ref offset, dataType);
                    break;
                }
                pIdx++;
            }

            return item;
        }
    }

    public class NewKpiFormat : DBDataViewer.DataItem
    {
        public byte KpiServiceType { get; private set; }    //Ver:1bit + ServiceType:7bit

        public byte BySize { get; private set; }   //BySize:4bit

        public byte ByReserve { get; private set; } //Reserve:  4bit

        public short ByKpiSn { get; private set; }  //Kpi Number: 2 * 8bit

        public static NewKpiFormat GetNewKpi(byte[] dataBuffer, ref int offset)
        {
            NewKpiFormat kpi = new NewKpiFormat();

            kpi.parse(dataBuffer, ref offset);

            return kpi;
        }

        /// <summary>
        ///@栅格程序image指标前缀
        ///┏━━┳━━┳━━┳━━┳━━┳━━┳━━┳━━┓
        ///┃bit7┃6   ┃5   ┃4   ┃3   ┃2   ┃1   ┃bit0┃
        ///┣━━╋━━┻━━┻━━┻━━┻━━┻━━┻━━┫
        ///┃ ver┃	             业务类型	               ┃OCTET1
        ///┣━━┻━━━━━━━━━━━━━━━━━━━━┫
        ///┃          size        ┃       保留字段       ┃OCTET2
        ///┣━━━━━━━━━━━━━━━━━━━━━━━┫
        ///┃               指标序号高字节                 ┃OCTET3
        ///┣━━━━━━━━━━━━━━━━━━━━━━━┫
        ///┃	            指标序号低字节                 ┃OCTET4
        ///┗━━━━━━━━━━━━━━━━━━━━━━━┛
        ///
        /// </summary>
        /// <param name="eSerType"></param>
        /// <param name="eDataType"></param>
        /// <param name="wKpiMaxIndex"></param>
        /// <returns></returns>
        //private void parse(byte[] dataBuffer, ref int offset)
        //{
        //    KpiServiceType = (byte)(dataBuffer[offset] & 0x7f);
        //    BySize = (byte)(dataBuffer[offset + 1] >> 4);
        //    ByReserve = (byte)(dataBuffer[offset + 1] & 0x0f);
        //    ByKpiSn = Convert.ToInt16(BitConverter.ToString(dataBuffer, offset + 2, 2).Replace("-", ""), 16);

        //    int nImagePreFix = ((1 << 31) | (KpiServiceType << 24) | (BySize << 20) | (ByReserve << 16) | (ByKpiSn & 0x0000ffff));

        //    Name = nImagePreFix.ToString("X8");

        //    offset += BySize;
        //}

        private void parse(byte[] dataBuffer, ref int offset)
        {
            KpiServiceType = (byte)(dataBuffer[offset] & 0x7f);
            BySize = dataBuffer[offset + 1];
            ByKpiSn = Convert.ToInt16(BitConverter.ToString(dataBuffer, offset + 2, 2).Replace("-", ""), 16);

            int nImagePreFix = (1 << 31) | (KpiServiceType << 24) | (BySize << 16) | (ByKpiSn & 0x0000ffff);

            //8位16进制数
            Name = nImagePreFix.ToString("X8");

            offset += BySize;
        }
    }

    public enum eStat_Data_Type : byte
    {
        SBYTE = 1,
        BYTE,
        SHORT,
        USHORT,
        INT,
        UINT,
        LONG,
        ULONG,
        CHAR,
        FLOAT,
        DOUBLE,
        BOOL,
        DECIMAL,
        STRING
    }

    public static class DataValueGetter
    {
        //offset：指向数值
        public static object GetValue(byte[] dataBuffer, ref int offset, byte dataType)
        {
            int dataLong = -1;
            object dataValueTemp = -10000000;

            switch (dataType)
            {
                case (byte)eStat_Data_Type.SBYTE:
                case (byte)eStat_Data_Type.BYTE:
                    dataLong = 1;
                    dataValueTemp = Convert.ToByte(dataBuffer[offset]);
                    break;
                case (byte)eStat_Data_Type.SHORT:
                case (byte)eStat_Data_Type.USHORT:
                    dataLong = 2;
                    dataValueTemp = StatDataConverter.ByteArray2ToInt(dataBuffer[offset], dataBuffer[offset + 1]);
                    break;
                case (byte)eStat_Data_Type.INT:
                    dataLong = 4;
                    dataValueTemp = StatDataConverter.ByteArray4ToInt(dataBuffer[offset], dataBuffer[offset + 1], dataBuffer[offset + 2], dataBuffer[offset + 3]);
                    break;
                case (byte)eStat_Data_Type.UINT:
                    dataLong = 4;
                    dataValueTemp = StatDataConverter.ByteArray4ToUInt(dataBuffer[offset], dataBuffer[offset + 1], dataBuffer[offset + 2], dataBuffer[offset + 3]);
                    break;
                case (byte)eStat_Data_Type.LONG:
                    dataLong = 8;
                    dataValueTemp = StatDataConverter.ByteArray8ToLong(new byte[] { dataBuffer[offset], dataBuffer[offset + 1], dataBuffer[offset + 2], dataBuffer[offset + 3], dataBuffer[offset + 4], dataBuffer[offset + 5], dataBuffer[offset + 6], dataBuffer[offset + 7] });
                    break;
                case (byte)eStat_Data_Type.ULONG:
                    dataLong = 8;
                    dataValueTemp = StatDataConverter.ByteArray8ToULong(new byte[] { dataBuffer[offset], dataBuffer[offset + 1], dataBuffer[offset + 2], dataBuffer[offset + 3], dataBuffer[offset + 4], dataBuffer[offset + 5], dataBuffer[offset + 6], dataBuffer[offset + 7] });
                    break;
                case (byte)eStat_Data_Type.FLOAT:
                    dataLong = 4;
                    dataValueTemp = (float)StatDataConverter.ByteArray4ToInt(dataBuffer[offset], dataBuffer[offset + 1], dataBuffer[offset + 2], dataBuffer[offset + 3]) / 1000;
                    break;
                case (byte)eStat_Data_Type.DOUBLE:
                    dataLong = 8;
                    dataValueTemp = BitConverter.ToDouble(new byte[] { dataBuffer[offset + 7], dataBuffer[offset + 6], dataBuffer[offset + 5], dataBuffer[offset + 4], dataBuffer[offset + 3], dataBuffer[offset + 2], dataBuffer[offset + 1], dataBuffer[offset] }, 0);
                    break;
                case (byte)eStat_Data_Type.STRING:
                    short strLen = BitConverter.ToInt16(dataBuffer, offset);
                    dataLong = strLen + 2;
                    if (strLen >= 0)
                    {
                        byte[] temp = new byte[strLen];

                        for (int ii = 0; ii < strLen; ii++)
                        {
                            temp[ii] = dataBuffer[offset + 2 + strLen - 1 - ii];
                        }

                        dataValueTemp = Encoding.Default.GetString(temp);
                    }
                    break;
                default://char,bool,decimal
                    break;
            }

            offset += dataLong;

            return dataValueTemp;
        }
    }
}
