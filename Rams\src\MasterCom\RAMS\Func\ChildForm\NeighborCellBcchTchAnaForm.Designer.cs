﻿namespace MasterCom.RAMS.Func
{
    partial class NeighborCellBcchTchAnaForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(NeighborCellBcchTchAnaForm));
            this.listViewBcchTch = new System.Windows.Forms.ListView();
            this.columnHeader1 = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderCellName = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderLac = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderCI = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderBcch = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderBsic = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderRxlev = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderTch = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderDistance = new System.Windows.Forms.ColumnHeader();
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.spinEditNei = new DevExpress.XtraEditors.SpinEdit();
            this.spinEditSame = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.groupControl2 = new DevExpress.XtraEditors.GroupControl();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditNei.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditSame.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).BeginInit();
            this.groupControl2.SuspendLayout();
            this.SuspendLayout();
            // 
            // listViewBcchTch
            // 
            this.listViewBcchTch.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.columnHeader1,
            this.columnHeaderCellName,
            this.columnHeaderLac,
            this.columnHeaderCI,
            this.columnHeaderBcch,
            this.columnHeaderBsic,
            this.columnHeaderRxlev,
            this.columnHeaderTch,
            this.columnHeaderDistance});
            this.listViewBcchTch.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listViewBcchTch.FullRowSelect = true;
            this.listViewBcchTch.GridLines = true;
            this.listViewBcchTch.Location = new System.Drawing.Point(2, 23);
            this.listViewBcchTch.MultiSelect = false;
            this.listViewBcchTch.Name = "listViewBcchTch";
            this.listViewBcchTch.Size = new System.Drawing.Size(620, 163);
            this.listViewBcchTch.TabIndex = 0;
            this.listViewBcchTch.UseCompatibleStateImageBehavior = false;
            this.listViewBcchTch.View = System.Windows.Forms.View.Details;
            // 
            // columnHeader1
            // 
            this.columnHeader1.Text = "";
            this.columnHeader1.Width = 46;
            // 
            // columnHeaderCellName
            // 
            this.columnHeaderCellName.Text = "小区名称";
            this.columnHeaderCellName.Width = 87;
            // 
            // columnHeaderLac
            // 
            this.columnHeaderLac.Text = "LAC";
            this.columnHeaderLac.Width = 44;
            // 
            // columnHeaderCI
            // 
            this.columnHeaderCI.Text = "CI";
            this.columnHeaderCI.Width = 44;
            // 
            // columnHeaderBcch
            // 
            this.columnHeaderBcch.Text = "BCCH";
            this.columnHeaderBcch.Width = 44;
            // 
            // columnHeaderBsic
            // 
            this.columnHeaderBsic.Text = "BSIC";
            this.columnHeaderBsic.Width = 40;
            // 
            // columnHeaderRxlev
            // 
            this.columnHeaderRxlev.Text = "Rxlev";
            this.columnHeaderRxlev.Width = 46;
            // 
            // columnHeaderTch
            // 
            this.columnHeaderTch.Text = "TCH";
            this.columnHeaderTch.Width = 176;
            // 
            // columnHeaderDistance
            // 
            this.columnHeaderDistance.Text = "Distance(米)";
            this.columnHeaderDistance.Width = 87;
            // 
            // groupControl1
            // 
            this.groupControl1.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left)
                        | System.Windows.Forms.AnchorStyles.Right)));
            this.groupControl1.Controls.Add(this.spinEditNei);
            this.groupControl1.Controls.Add(this.spinEditSame);
            this.groupControl1.Controls.Add(this.labelControl2);
            this.groupControl1.Controls.Add(this.labelControl1);
            this.groupControl1.Location = new System.Drawing.Point(0, 0);
            this.groupControl1.Name = "groupControl1";
            this.groupControl1.Size = new System.Drawing.Size(624, 53);
            this.groupControl1.TabIndex = 1;
            this.groupControl1.Text = "条件设置:";
            // 
            // spinEditNei
            // 
            this.spinEditNei.EditValue = new decimal(new int[] {
            6,
            0,
            0,
            -2147483648});
            this.spinEditNei.Location = new System.Drawing.Point(270, 24);
            this.spinEditNei.Name = "spinEditNei";
            this.spinEditNei.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditNei.Properties.Mask.EditMask = "f0";
            this.spinEditNei.Size = new System.Drawing.Size(100, 21);
            this.spinEditNei.TabIndex = 3;
            this.spinEditNei.EditValueChanged += new System.EventHandler(this.spinEditNei_EditValueChanged);
            // 
            // spinEditSame
            // 
            this.spinEditSame.EditValue = new decimal(new int[] {
            12,
            0,
            0,
            0});
            this.spinEditSame.Location = new System.Drawing.Point(88, 24);
            this.spinEditSame.Name = "spinEditSame";
            this.spinEditSame.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditSame.Properties.Mask.EditMask = "f0";
            this.spinEditSame.Size = new System.Drawing.Size(100, 21);
            this.spinEditSame.TabIndex = 2;
            this.spinEditSame.EditValueChanged += new System.EventHandler(this.spinEditSame_EditValueChanged);
            // 
            // labelControl2
            // 
            this.labelControl2.Location = new System.Drawing.Point(194, 27);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(64, 14);
            this.labelControl2.TabIndex = 1;
            this.labelControl2.Text = "邻频保护比:";
            // 
            // labelControl1
            // 
            this.labelControl1.Location = new System.Drawing.Point(12, 27);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(64, 14);
            this.labelControl1.TabIndex = 0;
            this.labelControl1.Text = "同频保护比:";
            // 
            // groupControl2
            // 
            this.groupControl2.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
                        | System.Windows.Forms.AnchorStyles.Left)
                        | System.Windows.Forms.AnchorStyles.Right)));
            this.groupControl2.Controls.Add(this.listViewBcchTch);
            this.groupControl2.Location = new System.Drawing.Point(0, 54);
            this.groupControl2.Name = "groupControl2";
            this.groupControl2.Size = new System.Drawing.Size(624, 188);
            this.groupControl2.TabIndex = 2;
            this.groupControl2.Text = "显示列表:";
            // 
            // NeighborCellBcchTchAnaForm
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("NeighborCellBcchTchAnaForm.Appearance.Image")));
            this.Appearance.Options.UseFont = true;
            this.Appearance.Options.UseImage = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(623, 241);
            this.Controls.Add(this.groupControl2);
            this.Controls.Add(this.groupControl1);
            this.Name = "NeighborCellBcchTchAnaForm";
            this.Text = "同邻频列表";
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            this.groupControl1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditNei.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditSame.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).EndInit();
            this.groupControl2.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.ListView listViewBcchTch;
        private System.Windows.Forms.ColumnHeader columnHeaderCellName;
        private System.Windows.Forms.ColumnHeader columnHeaderLac;
        private System.Windows.Forms.ColumnHeader columnHeaderCI;
        private System.Windows.Forms.ColumnHeader columnHeaderBcch;
        private System.Windows.Forms.ColumnHeader columnHeaderBsic;
        private System.Windows.Forms.ColumnHeader columnHeaderRxlev;
        private System.Windows.Forms.ColumnHeader columnHeaderTch;
        private System.Windows.Forms.ColumnHeader columnHeaderDistance;
        private DevExpress.XtraEditors.GroupControl groupControl1;
        private DevExpress.XtraEditors.SpinEdit spinEditNei;
        private DevExpress.XtraEditors.SpinEdit spinEditSame;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.GroupControl groupControl2;
        private System.Windows.Forms.ColumnHeader columnHeader1;
    }
}