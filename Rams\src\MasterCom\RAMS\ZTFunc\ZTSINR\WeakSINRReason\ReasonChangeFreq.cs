﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.ZTFunc.ZTCellCheck.RoadSegment;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc.ZTSINR.WeakSINRReason
{
    public class ReasonChangeFreq : ReasonBase
    {
        //public double distance = 250;
        //public int hoCount = 4;
        public ReasonChangeFreq()
        {
            this.Name = "切换频繁";
        }
        //protected float timePersist = 20;
        //public float TimePersist
        //{
        //    get { return timePersist; }
        //    set { timePersist = value; }
        //}

        public override bool IsValid(Model.TestPoint tp, params object[] resvParams)
        {
            List<Event> evtList = ZTWeakSINRReason.handOverTooMuchEvents;
            foreach (Event evt in evtList)
            {
                if (tp.DateTime >= evt.DateTime.AddSeconds(-ZTWeakSINRReason.timeLimit) && tp.DateTime <= evt.DateTime)
                {
                    return true;
                }
            }
            return false;
        }
    }
}
