﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using BrightIdeasSoftware;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class LastRoadSegmentForm : MinCloseForm
    {
        private List<LastRoadSegment> segments = new List<LastRoadSegment>();
        public LastRoadSegmentForm(MainModel mm):base(mm)
        {
            InitializeComponent();
            DisposeWhenClose = true;
            initFixCol();
        }
        private void initFixCol()
        {
            lv.CanExpandGetter += delegate (object row)
            {
                return row is LastRoadSegment || row is LastRoadSegmentCellItem;
            };
            lv.ChildrenGetter += delegate (object row)
            {
                if (row is LastRoadSegment)
                {
                    LastRoadSegment s = row as LastRoadSegment;
                    return s.CellItems;
                }
                else if (row is LastRoadSegmentCellItem)
                {
                    LastRoadSegmentCellItem s = row as LastRoadSegmentCellItem;
                    return s.TestPoints;
                }
                return null;
            };
            colStaySecond.AspectGetter += delegate (object row)
            {
                if (row is LastRoadSegment)
                {
                    LastRoadSegment s = row as LastRoadSegment;
                    return s.StaySecond;
                }
                else if (row is LastRoadSegmentCellItem)
                {
                    LastRoadSegmentCellItem s = row as LastRoadSegmentCellItem;
                    return s.StaySecond;
                }
                return null;
            };
            colDistance.AspectGetter += delegate (object row)
            {
                if (row is LastRoadSegment)
                {
                    LastRoadSegment s = row as LastRoadSegment;
                    return s.Distance;
                }
                else if (row is LastRoadSegmentCellItem)
                {
                    LastRoadSegmentCellItem s = row as LastRoadSegmentCellItem;
                    return s.Distance;
                }
                return null;
            };
            addLongitudeLatitude();
            addLastRoadSegment();
            colTime.AspectGetter += delegate (object row)
            {
                if (row is TestPoint)
                {
                    TestPoint tp = row as TestPoint;
                    return tp.DateTime;
                }
                return null;
            };
            colTestPointCnt.AspectGetter += delegate (object row)
            {
                if (row is LastRoadSegment)
                {
                    LastRoadSegment s = row as LastRoadSegment;
                    return s.TestPoints.Count;
                }
                else if (row is LastRoadSegmentCellItem)
                {
                    LastRoadSegmentCellItem s = row as LastRoadSegmentCellItem;
                    return s.TestPoints.Count;
                }
                return null;
            };
            colRoadNames.AspectGetter += delegate (object row)
            {
                if (row is LastRoadSegment)
                {
                    LastRoadSegment s = row as LastRoadSegment;
                    return s.RoadNames;
                }
                else if (row is LastRoadSegmentCellItem)
                {
                    LastRoadSegmentCellItem s = row as LastRoadSegmentCellItem;
                    return s.Cell.Name;
                }
                return "";
            };
        }

        private void addLongitudeLatitude()
        {
            colLat.AspectGetter += delegate (object row)
            {
                if (row is LastRoadSegment)
                {
                    LastRoadSegment s = row as LastRoadSegment;
                    return s.CenterLatitude;
                }
                else if (row is LastRoadSegmentCellItem)
                {
                    LastRoadSegmentCellItem s = row as LastRoadSegmentCellItem;
                    return s.CenterLatitude;
                }
                else if (row is TestPoint)
                {
                    TestPoint tp = row as TestPoint;
                    return tp.Latitude;
                }
                return null;
            };
            colLng.AspectGetter += delegate (object row)
            {
                if (row is LastRoadSegment)
                {
                    LastRoadSegment s = row as LastRoadSegment;
                    return s.CenterLongitude;
                }
                else if (row is LastRoadSegmentCellItem)
                {
                    LastRoadSegmentCellItem s = row as LastRoadSegmentCellItem;
                    return s.CenterLongitude;
                }
                else if (row is TestPoint)
                {
                    TestPoint tp = row as TestPoint;
                    return tp.Longitude;
                }
                return null;
            };
        }

        private void addLastRoadSegment()
        {
            colCellsToken.AspectGetter += delegate (object row)
            {
                if (row is LastRoadSegment)
                {
                    LastRoadSegment s = row as LastRoadSegment;
                    return s.CellsToken;
                }
                return null;
            };
            colCell2TpDisAvg.AspectGetter += delegate (object row)
            {
                if (row is LastRoadSegmentCellItem)
                {
                    LastRoadSegmentCellItem s = row as LastRoadSegmentCellItem;
                    return s.AvgDistance2Points;
                }
                return null;
            };
            colFile.AspectGetter += delegate (object row)
            {
                if (row is LastRoadSegment)
                {
                    LastRoadSegment s = row as LastRoadSegment;
                    return s.FileName;
                }
                return null;
            };
            colSN.AspectGetter += delegate (object row)
            {
                if (row is LastRoadSegment)
                {
                    LastRoadSegment s = row as LastRoadSegment;
                    return segments.IndexOf(s) + 1;
                }
                return null;
            };
        }

        public void FillData(LastRoadReport report,List<LastRoadSegment> segments)
        {
            this.Text = "持续路段-" + report.Name;
            lv.ClearObjects();
            this.segments = segments;
            addCol2ListView(report.DisplayColumns);
            lv.SetObjects(segments);
            showAllTestPoint(report,segments);
        }

        private void showAllTestPoint(LastRoadReport report,List<LastRoadSegment> segments)
        {
            MainModel.ClearDTData();
            foreach (LastRoadSegment seg in segments)
            {
                foreach (TestPoint tp in seg.TestPoints)
                {
                    MainModel.DTDataManager.Add(tp);
                }
            }
            MainModel.FireSetDefaultMapSerialTheme(report.GISDisplaySerialName);
            MainModel.FireDTDataChanged(this);
        }

        List<OLVColumn> addtionalDisCols = new List<OLVColumn>();
        private void addCol2ListView(List<TestPointDisplayColumn> cols)
        {
            foreach (OLVColumn oldCol in addtionalDisCols)
            {
                lv.AllColumns.Remove(oldCol);
                lv.Columns.Remove(oldCol);
                oldCol.Dispose();
            }
            addtionalDisCols.Clear();
            foreach (TestPointDisplayColumn colInfo in cols)
            {
                OLVColumn col = new OLVColumn();
                col.Text = colInfo.Caption;
                col.Tag = colInfo;
                lv.AllColumns.Add(col);
                lv.Columns.AddRange(new ColumnHeader[] { col });
                addtionalDisCols.Add(col);
                setColumnAspect(col);
            }
            lv.RebuildColumns();
        }

        private void setColumnAspect(OLVColumn col)
        {
            col.AspectGetter += delegate (object row)
            {
                TestPointDisplayColumn disColInfo = col.Tag as TestPointDisplayColumn;
                LastRoadSegment roadSegment = row as LastRoadSegment;
                if (roadSegment != null)
                {
                    double d = roadSegment[disColInfo];
                    if (double.IsNaN(d))
                    {
                        return "-";
                    }
                    else
                    {
                        return d;
                    }
                }
                else if (row is TestPoint)
                {
                    TestPoint tp = row as TestPoint;
                    object objVal = tp[disColInfo.DisplayParam.ParamInfo.Name, disColInfo.ParamArrayIndex];
                    if (objVal != null)
                    {
                        double v = double.Parse(objVal.ToString());
                        if (disColInfo.DisplayParam.ValueMin <= v && disColInfo.DisplayParam.ValueMax >= v)
                        {
                            return v;
                        }
                    }
                    return null;
                }
                return null;
            };
        }

        private void miExpandAll_Click(object sender, EventArgs e)
        {
            lv.ExpandAll();
        }

        private void miCollapseAll_Click(object sender, EventArgs e)
        {
            lv.CollapseAll();
        }

        private void miExport_Click(object sender, EventArgs e)
        {
            lv.ExpandAll();
            MasterCom.Util.ExcelNPOIManager.ExportToExcel(lv);
        }

        private void lv_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            MainModel.ClearSelectedTestPoints();
            if (lv.SelectedObject is LastRoadSegment)
            {
                LastRoadSegment s = lv.SelectedObject as LastRoadSegment;
                MainModel.SelectedTestPoints.AddRange(s.TestPoints);
                MainModel.MainForm.GetMapForm().GoToView(s.CenterLongitude, s.CenterLatitude);
            }
            else if (lv.SelectedObject is TestPoint)
            {
                TestPoint tp = lv.SelectedObject as TestPoint;
                MainModel.SelectedTestPoints.Add(tp);
                MainModel.MainForm.GetMapForm().GoToView(tp.Longitude, tp.Latitude);
            }
        }

        private void miExportLevelCell_Click(object sender, EventArgs e)
        {
            lv.Expand(segments);
            foreach (LastRoadSegment road in segments)
            {
                lv.Expand(road);
                foreach (LastRoadSegmentCellItem cellItem in road.CellItems)
                {
                    lv.Collapse(cellItem);
                }
            }
            MasterCom.Util.ExcelNPOIManager.ExportToExcel(lv);
        }

        private void miExportLevelRoad_Click(object sender, EventArgs e)
        {
            lv.CollapseAll();
            MasterCom.Util.ExcelNPOIManager.ExportToExcel(lv);
        }

    }
}
