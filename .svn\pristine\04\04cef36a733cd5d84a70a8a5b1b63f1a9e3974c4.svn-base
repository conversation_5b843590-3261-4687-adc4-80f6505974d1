﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTWeakCoverByEventResultForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.xtraTabData = new DevExpress.XtraTab.XtraTabControl();
            this.xtraTabDataView = new DevExpress.XtraTab.XtraTabPage();
            this.gridDataView = new DevExpress.XtraGrid.GridControl();
            this.cmItems = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.replayEvent = new System.Windows.Forms.ToolStripMenuItem();
            this.outPutData = new System.Windows.Forms.ToolStripMenuItem();
            this.gridView1 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn6 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn7 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn8 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn9 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn10 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.xtraTabPageSummary = new DevExpress.XtraTab.XtraTabPage();
            this.gridDataSummary = new DevExpress.XtraGrid.GridControl();
            this.gridView2 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn11 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn12 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn13 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn14 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn15 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn16 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn17 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn18 = new DevExpress.XtraGrid.Columns.GridColumn();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabData)).BeginInit();
            this.xtraTabData.SuspendLayout();
            this.xtraTabDataView.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridDataView)).BeginInit();
            this.cmItems.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).BeginInit();
            this.xtraTabPageSummary.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridDataSummary)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).BeginInit();
            this.SuspendLayout();
            // 
            // xtraTabData
            // 
            this.xtraTabData.Dock = System.Windows.Forms.DockStyle.Fill;
            this.xtraTabData.Location = new System.Drawing.Point(0, 0);
            this.xtraTabData.Name = "xtraTabData";
            this.xtraTabData.SelectedTabPage = this.xtraTabDataView;
            this.xtraTabData.Size = new System.Drawing.Size(1087, 498);
            this.xtraTabData.TabIndex = 0;
            this.xtraTabData.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.xtraTabDataView,
            this.xtraTabPageSummary});
            this.xtraTabData.SelectedPageChanged += new DevExpress.XtraTab.TabPageChangedEventHandler(this.xtraTabData_SelectedPageChanged);
            // 
            // xtraTabDataView
            // 
            this.xtraTabDataView.Controls.Add(this.gridDataView);
            this.xtraTabDataView.Name = "xtraTabDataView";
            this.xtraTabDataView.Size = new System.Drawing.Size(1080, 468);
            this.xtraTabDataView.Text = "弱覆盖路段详情";
            // 
            // gridDataView
            // 
            this.gridDataView.ContextMenuStrip = this.cmItems;
            this.gridDataView.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridDataView.Location = new System.Drawing.Point(0, 0);
            this.gridDataView.MainView = this.gridView1;
            this.gridDataView.Name = "gridDataView";
            this.gridDataView.Size = new System.Drawing.Size(1080, 468);
            this.gridDataView.TabIndex = 1;
            this.gridDataView.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView1});
            // 
            // cmItems
            // 
            this.cmItems.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.replayEvent,
            this.outPutData});
            this.cmItems.Name = "cmItems";
            this.cmItems.Size = new System.Drawing.Size(123, 48);
            // 
            // replayEvent
            // 
            this.replayEvent.Name = "replayEvent";
            this.replayEvent.Size = new System.Drawing.Size(122, 22);
            this.replayEvent.Text = "事件回放";
            this.replayEvent.Click += new System.EventHandler(this.replayEvent_Click);
            // 
            // outPutData
            // 
            this.outPutData.Name = "outPutData";
            this.outPutData.Size = new System.Drawing.Size(122, 22);
            this.outPutData.Text = "导出数据";
            this.outPutData.Click += new System.EventHandler(this.outPutData_Click);
            // 
            // gridView1
            // 
            this.gridView1.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn1,
            this.gridColumn2,
            this.gridColumn3,
            this.gridColumn4,
            this.gridColumn5,
            this.gridColumn6,
            this.gridColumn7,
            this.gridColumn8,
            this.gridColumn9,
            this.gridColumn10});
            this.gridView1.GridControl = this.gridDataView;
            this.gridView1.Name = "gridView1";
            this.gridView1.OptionsBehavior.Editable = false;
            this.gridView1.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "地市名称";
            this.gridColumn1.FieldName = "StrCityName";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.Visible = true;
            this.gridColumn1.VisibleIndex = 0;
            // 
            // gridColumn2
            // 
            this.gridColumn2.Caption = "道路类型";
            this.gridColumn2.FieldName = "StrGridType";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.Visible = true;
            this.gridColumn2.VisibleIndex = 2;
            // 
            // gridColumn3
            // 
            this.gridColumn3.Caption = "网格名称";
            this.gridColumn3.FieldName = "StrGridName";
            this.gridColumn3.Name = "gridColumn3";
            this.gridColumn3.Visible = true;
            this.gridColumn3.VisibleIndex = 3;
            // 
            // gridColumn4
            // 
            this.gridColumn4.Caption = "事件时间";
            this.gridColumn4.FieldName = "StrTime";
            this.gridColumn4.Name = "gridColumn4";
            // 
            // gridColumn5
            // 
            this.gridColumn5.Caption = "事件经度";
            this.gridColumn5.FieldName = "DLng";
            this.gridColumn5.Name = "gridColumn5";
            this.gridColumn5.Visible = true;
            this.gridColumn5.VisibleIndex = 3;
            // 
            // gridColumn6
            // 
            this.gridColumn6.Caption = "事件纬度";
            this.gridColumn6.FieldName = "DLat";
            this.gridColumn6.Name = "gridColumn6";
            this.gridColumn6.Visible = true;
            this.gridColumn6.VisibleIndex = 4;
            // 
            // gridColumn7
            // 
            this.gridColumn7.Caption = "开始时间";
            this.gridColumn7.FieldName = "StrBeginTime";
            this.gridColumn7.Name = "gridColumn7";
            this.gridColumn7.Visible = true;
            this.gridColumn7.VisibleIndex = 6;
            // 
            // gridColumn8
            // 
            this.gridColumn8.Caption = "结束时间";
            this.gridColumn8.FieldName = "StrEndTime";
            this.gridColumn8.Name = "gridColumn8";
            this.gridColumn8.Visible = true;
            this.gridColumn8.VisibleIndex = 7;
            // 
            // gridColumn9
            // 
            this.gridColumn9.Caption = "持续时长(s)";
            this.gridColumn9.FieldName = "StrLastTime";
            this.gridColumn9.Name = "gridColumn9";
            this.gridColumn9.Visible = true;
            this.gridColumn9.VisibleIndex = 8;
            // 
            // gridColumn10
            // 
            this.gridColumn10.Caption = "持续距离(m)";
            this.gridColumn10.FieldName = "StrLastDistance";
            this.gridColumn10.Name = "gridColumn10";
            this.gridColumn10.Visible = true;
            this.gridColumn10.VisibleIndex = 9;
            // 
            // xtraTabPageSummary
            // 
            this.xtraTabPageSummary.Controls.Add(this.gridDataSummary);
            this.xtraTabPageSummary.Name = "xtraTabPageSummary";
            this.xtraTabPageSummary.Size = new System.Drawing.Size(1080, 468);
            this.xtraTabPageSummary.Text = "弱覆盖路段汇总";
            // 
            // gridDataSummary
            // 
            this.gridDataSummary.ContextMenuStrip = this.cmItems;
            this.gridDataSummary.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridDataSummary.Location = new System.Drawing.Point(0, 0);
            this.gridDataSummary.MainView = this.gridView2;
            this.gridDataSummary.Name = "gridDataSummary";
            this.gridDataSummary.Size = new System.Drawing.Size(1080, 468);
            this.gridDataSummary.TabIndex = 2;
            this.gridDataSummary.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView2});
            // 
            // gridView2
            // 
            this.gridView2.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn11,
            this.gridColumn12,
            this.gridColumn13,
            this.gridColumn14,
            this.gridColumn15,
            this.gridColumn16,
            this.gridColumn17,
            this.gridColumn18});
            this.gridView2.GridControl = this.gridDataSummary;
            this.gridView2.Name = "gridView2";
            this.gridView2.OptionsBehavior.Editable = false;
            this.gridView2.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn11
            // 
            this.gridColumn11.Caption = "地市名称";
            this.gridColumn11.FieldName = "StrCityName";
            this.gridColumn11.Name = "gridColumn11";
            this.gridColumn11.Visible = true;
            this.gridColumn11.VisibleIndex = 0;
            // 
            // gridColumn12
            // 
            this.gridColumn12.Caption = "道路类型";
            this.gridColumn12.FieldName = "StrGridType";
            this.gridColumn12.Name = "gridColumn12";
            this.gridColumn12.Visible = true;
            this.gridColumn12.VisibleIndex = 1;
            // 
            // gridColumn13
            // 
            this.gridColumn13.Caption = "网格名称";
            this.gridColumn13.FieldName = "StrGridName";
            this.gridColumn13.Name = "gridColumn13";
            this.gridColumn13.Visible = true;
            this.gridColumn13.VisibleIndex = 2;
            // 
            // gridColumn14
            // 
            this.gridColumn14.Caption = "弱覆盖路段数";
            this.gridColumn14.FieldName = "IRoadNum";
            this.gridColumn14.Name = "gridColumn14";
            this.gridColumn14.Visible = true;
            this.gridColumn14.VisibleIndex = 3;
            // 
            // gridColumn15
            // 
            this.gridColumn15.Caption = "弱覆盖总时长(s)";
            this.gridColumn15.FieldName = "DTimeSum";
            this.gridColumn15.Name = "gridColumn15";
            this.gridColumn15.Visible = true;
            this.gridColumn15.VisibleIndex = 4;
            // 
            // gridColumn16
            // 
            this.gridColumn16.Caption = "弱覆盖总里程(m)";
            this.gridColumn16.FieldName = "DDistanceSum";
            this.gridColumn16.Name = "gridColumn16";
            this.gridColumn16.Visible = true;
            this.gridColumn16.VisibleIndex = 5;
            // 
            // gridColumn17
            // 
            this.gridColumn17.Caption = "弱覆盖平均时长(s)";
            this.gridColumn17.FieldName = "DTimeAvg";
            this.gridColumn17.Name = "gridColumn17";
            this.gridColumn17.Visible = true;
            this.gridColumn17.VisibleIndex = 6;
            // 
            // gridColumn18
            // 
            this.gridColumn18.Caption = "弱覆盖平均里程(m)";
            this.gridColumn18.FieldName = "DDistanceAvg";
            this.gridColumn18.Name = "gridColumn18";
            this.gridColumn18.Visible = true;
            this.gridColumn18.VisibleIndex = 7;
            // 
            // ZTWeakCoverByEventResultForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1087, 498);
            this.Controls.Add(this.xtraTabData);
            this.Name = "ZTWeakCoverByEventResultForm";
            this.Text = "干线弱覆盖路段统计";
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabData)).EndInit();
            this.xtraTabData.ResumeLayout(false);
            this.xtraTabDataView.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridDataView)).EndInit();
            this.cmItems.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).EndInit();
            this.xtraTabPageSummary.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridDataSummary)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraTab.XtraTabControl xtraTabData;
        private DevExpress.XtraTab.XtraTabPage xtraTabDataView;
        private DevExpress.XtraGrid.GridControl gridDataView;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView1;
        private DevExpress.XtraTab.XtraTabPage xtraTabPageSummary;
        private DevExpress.XtraGrid.GridControl gridDataSummary;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn6;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn7;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn8;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn9;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn10;
        private System.Windows.Forms.ContextMenuStrip cmItems;
        private System.Windows.Forms.ToolStripMenuItem replayEvent;
        private System.Windows.Forms.ToolStripMenuItem outPutData;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn11;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn12;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn13;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn14;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn15;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn16;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn17;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn18;

    }
}