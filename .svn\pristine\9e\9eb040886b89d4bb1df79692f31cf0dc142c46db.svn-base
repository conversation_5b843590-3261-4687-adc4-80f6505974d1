using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.Util;
//using EventDefineEngine;

namespace MasterCom.RAMS.Model
{
    public class TDTestPointSummary : TestPoint
    {
        public override DateTime DateTime
        {
            get { return JavaDate.GetDateTimeFromMilliseconds(Time * 1000L + Millisecond); }
        }
        public override void Fill(MasterCom.RAMS.Net.Content content)
        {
            FileID = content.GetParamInt();
            SN = content.GetParamInt();
            Time = content.GetParamInt();
            Millisecond = content.GetParamShort();
            MS = content.GetParamByte();
            Longitude = content.GetParamDouble();
            Latitude = content.GetParamDouble();
            this[MainModel.TD_SCell_CI] = content.GetParamInt();
            this[MainModel.TD_SCell_CPI] = content.GetParamInt();
            this[MainModel.TD_SCell_UARFCN] = content.GetParamInt();
            this[MainModel.TD_SCell_LAC] = content.GetParamInt();
            this["TD_CarrierRSSI"] = content.GetParamInt();
            this["TD_PCCPCH_ISCP"] = content.GetParamInt();
            this["TD_DPCH_ISCP"] = content.GetParamInt();
            this["TD_PCCPCH_RSCP"] = content.GetParamInt();
            this["TD_DPCH_RSCP"] = content.GetParamInt();
            this[MainModel.TD_TA] = content.GetParamInt();
            this["TD_PCCPCHSIR"] = content.GetParamFloat();
            this["TD_CCTRCHSIR"] = content.GetParamFloat();
            this["TD_DownlinkTargetSIR"] = content.GetParamFloat();
            this["TD_PCCPCH_C2I"] = content.GetParamInt();
            this["TD_DPCH_C2I"] = content.GetParamInt();
            this["TD_TxPower"] = content.GetParamInt();
            this["TD_NCell_CPI", 0] = content.GetParamInt();
            this["TD_NCell_CPI", 1] = content.GetParamInt();
            this["TD_NCell_UARFCN",0] = content.GetParamInt();
            this["TD_NCell_UARFCN",1] = content.GetParamInt();
            this["TD_NCell_PCCPCH_RSCP",0] = content.GetParamInt();
            this["TD_NCell_PCCPCH_RSCP",1] = content.GetParamInt();
            this["TD_NCell_Rn",0] = content.GetParamInt();
            this["TD_NCell_Rn",1] = content.GetParamInt();
            this["TD_NCell_RSSI",0] = content.GetParamInt();
            this["TD_NCell_RSSI",1] = content.GetParamInt();
        }
       
    }
}
