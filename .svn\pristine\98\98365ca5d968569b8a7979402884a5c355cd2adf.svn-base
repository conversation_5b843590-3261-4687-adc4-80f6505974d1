﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.Net
{
    public class DIYCellStatByFile : DIYCellsOfRegionStatQuery
    {
        public DIYCellStatByFile(MainModel mm)
            : base(mm)
        {
            this.needFindCell = false;
        }
        public override bool CanEnabled(Model.SearchGeometrys searchGeometrys)
        {
            return true;
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 11000, 11047, this.Name);
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MainModel.NeedSearchType.File;
        }

        public override string Name
        {
            get { return "文件涉及到的小区统计"; }
        }

        protected override bool isValidCondition()
        {
            if (condition.FileInfos == null || condition.FileInfos.Count == 0)
            {
                return false;
            }
            DateTime bTime = DateTime.MaxValue;
            DateTime eTime = DateTime.MinValue;
            StringBuilder sb = new StringBuilder();
            foreach (FileInfo fi in condition.FileInfos)
            {
                DateTime fBTime = DateTime.Parse(fi.BeginTimeString);
                DateTime fETime = DateTime.Parse(fi.EndTimeString);
                if (fBTime < bTime)
                {
                    bTime = fBTime;
                }
                if (fETime > eTime)
                {
                    eTime = fETime;
                }
                if (!condition.CarrierTypes.Contains(fi.CarrierType))
                {
                    condition.CarrierTypes.Add(fi.CarrierType);
                }
                if (!condition.ServiceTypes.Contains(fi.ServiceType))
                {
                    condition.ServiceTypes.Add(fi.ServiceType);
                }
                if (!condition.Projects.Contains(fi.ProjectID))
                {
                    condition.Projects.Add(fi.ProjectID);
                }
                sb.Append(fi.ID);
                sb.Append(",");
            }
            TimePeriod p = new TimePeriod(bTime, eTime);
            condition.Periods.Add(p);
            condition.FileName = sb.ToString().TrimEnd(',');
            condition.NameFilterType = FileFilterType.ByMark_ID;
            return true;
        }

        protected override bool isValidCell(int lac, long ci)
        {
            return true;
        }


    }
}
