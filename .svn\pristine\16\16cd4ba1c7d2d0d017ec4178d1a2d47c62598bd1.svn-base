<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="ColumnRange.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="ColumnTimes.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="ColumnRatio.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="ColumnAVG.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="ColumnMax.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="ColumnMin.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="contextMenuStrip1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="$this.Icon" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAABAAEAICAAAAEAIACoEAAAFgAAACgAAAAgAAAAQAAAAAEAIAAAAAAAABAAABILAAASCwAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAJNTU0XTk5OKkxMTChMTEwoTExMKExMTChMTEwoTExMKExMTChMTEwoTExMKExM
        TChMTEwoTExMKExMTChMTEwoTExMKE5OTipPT08dTExMCj8/PwgAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAPz8/CIaGhsKYmJjvlJSU7JKSkuySkpLskpKS7JGRkeyRkZHskJCQ7JCQ
        kOyPj4/sj4+P7I6OjuyNjY3sjY2N7I6OjuyOjo7sh4eH6np6et1lZWXGVFRUu05OTptRUVGEVlZWYVhY
        WDEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA/Pz8IoaGh9P/////39/f/9vb2//T09P/y8vL/8fHx/+/v
        7//u7u7/7Ozs/+rq6v/p6en/5+fn/+Xl5f/k5OT/4+Pj/+jo6P/x8fH/9vb2//Hx8f/Z2dn/ra2t/Ht7
        e/dYWFjvVlZWyFJSUiUAAAAAAAAAAAAAAAAAAAAAAAAAACoqKgadnZ3z//////Hx8f/v7+//7u7u/+zs
        7P/r6+v/6enp/+jo6P/m5ub/5eXl/+Pj4//i4uL/4ODg/9/f3//d3d3/3Nzc/+Dg4P/r6+v/9/f3////
        /////////////9LS0v9cXFzoVVVVRQAAAAAAAAAAAAAAAAAAAAAAAAAAKioqBp6envP/////8vLy//Hx
        8f/v7+//7u7u/+zs7P/r6+v/6enp/+jo6P/m5ub/5eXl/+bk5v/n4+b/4ODg/9/f3//d3d3/29vb/+Dg
        4P/q6ur/9PT0//39/f///////////3x8fOxQUFA/AAAAAAAAAAAAAAAAAAAAAAAAAAAqKioGn5+f8///
        ///09PT/8vLy//Hx8f/v7+//7u7u/+zs7P/r6+v/6enp/+no6f/x6e//293a/8bZyv/o5Oj/4eHh/9/f
        3//d3d3/29vb/+Dg4P/q6ur/9PT0//39/f//////pKSk9UtLS0oAAAAAAAAAAAAAAAAAAAAAAAAAACoq
        KgagoKDz//////X19f/09PT/8vLy//Hx8f/v7+//7u7u/+zs7P/s6+z/5+bm/63TtP9L1HP/cd2S/9jY
        1f/j3eH/7OPq/+Th4//d3d3/29vb/+Dg4P/q6ur/9PT0///////Jycn9T09PcwAAAAAAAAAAAAAAAAAA
        AAAAAAAAKioqBqGhofP/////9/f3//X19f/09PT/8vLy//Hx8f/v7+//7u7u//Xt8/+T1qb/HN1b/zHy
        dP8y5m3/L9Fh/0XJbP9/x5L/zdXO/+zi6f/e3t7/29vb/+Dg4P/q6ur/+fn5/+Li4v88PDyZAAAAAAAA
        AAAAAAAAAAAAAAAAAAAqKioGoaGh8//////4+Pj/9/f3//X19f/09PT/8vLy//Hx8f/v7+//8O7v/+ft
        6f9w65r/Hehi/zPpcv8r6G3/CuNV/wDWRf8Xwk3/isWa/+nf5v/g3t//29vb/+Dg4P/s7Oz/6+vr/0ZG
        Rp8AAAAAAAAAAAAAAAAAAAAAAAAAACoqKgaioqLz//////r6+v/4+Pj/9/f3//35/f/29fb/8vLy//Hx
        8f/v7+//8u7x//Lt8f9k55H/gOek/93o4f+d5LX/O91z/wDXQP8AwjP/a7yB/+jf5v/e3t7/29vb/+Hh
        4f/p6en/UVFRqAAAAAAAAAAAAAAAAAAAAAAAAAAAKioqBqOjo/P/////+/v7//r6+v/9+/3/vtvF/+3w
        7v/49vf/8vLy//Ty9P/28fX/7+ns/93l3v/I48//9Ofv//vq9v/q5+n/dtuY/wDKOv8Atij/fr2N/+zi
        6f/d3d3/3Nzc/+Dg4P9RUVGpAAAAAAAAAAAAAAAAAAAAAAAAAAAqKioGpKSk8//////9/f3//////8Tg
        yf8ApSX/VL1u//Tz9P/8+Pv/7evr/63Ytf+M4KP/iuim/43mp/+Q2aL/xdnH//Pp8P/46fT/fNWW/wC6
        KP8JqSz/vc/B/+bi5f/e3t7/29vb/09PT6kAAAAAAAAAAAAAAAAAAAAAAAAAACoqKgakpKTz////////
        ////+/7/QLBU/wCsFv9Hx2f//Pj8/+np5/9YxXX/Htpd/yTnaP8t7G//KOpr/0TnfP9D0W7/mMii//Tq
        8f/u6O3/PsFe/wCmEv9fsmz/7OPr/+Dg4P/d3d3/T09PqQAAAAAAAAAAAAAAAAAAAAAAAAAAKioqBqSk
        pPP//////////8Lfxf8AmxX/Cq4t/9jx3v//+v//V7Rs/1PWfP8w4G7/Et1Z/wfcUP8b4F//j+Kq/w3L
        Sv8HtDf/tMq2//7w+/+k1a//AKMW/xufL//Y2tf/5ePk/9/f3/9PT0+pAAAAAAAAAAAAAAAAAAAAAAAA
        AAAqKioGpKSk8///////////fcGF/wCQAP9TwGf//////9Hf0v8AoiH/Lr5T/3LJif+z6MT/UeOE/yva
        Z/+W6a//AMEz/wCxIv9QqV//+Oz1/9nj2/8RpCr/AJMR/7nPu//r5uv/4ODg/09PT6kAAAAAAAAAAAAA
        AAAAAAAAAAAAACoqKgakpKTz//////////9Vr13/AIoA/4zSl///////pM2n/wCZEf8ArSL/ALEf/03G
        bP//////2O/e/0/Aa/8Asib/AKYb/ymfOP/q5un/6erp/yekOP8AiQD/qsur/+/p7//i4uL/T09PqQAA
        AAAAAAAAAAAAAAAAAAAAAAAAKioqBqSkpPP//////////0yqUf8AgQD/ndKj//////+o1qz/AJMS/wSg
        IP8Aph//AKce/8fnzf/P7db/FbA3/wCiHf8BmRj/MaI8/+zr7P/m6ef/H5sr/wCDAP+tzq7/8Orw/+Pj
        4/9PT0+pAAAAAAAAAAAAAAAAAAAAAAAAAAAqKioGpKSk8///////////Xa5e/wB2AP+BvoX//////9zt
        3P8+okL/PKhG/zKqQv87sU7/2O7c///+//+Xy5z/R65S/zKgOf91uHb//fX9/8vezP8Chwj/BYIH/8jZ
        yP/u6u7/5eXl/09PT6kAAAAAAAAAAAAAAAAAAAAAAAAAACoqKgakpKTz//////////+PxY//AHgA/0ec
        R////////////5jLmP9rtWz/c7t1/3K7df+cy5//7PTt/3/Agv9ut3D/bbVt/83gzP//+v//ebl7/wB7
        AP81ljT/6unq/+vq6//m5ub/T09PqQAAAAAAAAAAAAAAAAAAAAAAAAAAKioqBqSkpPP//////////9bq
        1v8cjRz/GYYZ/8LYwv//////8fjx/6rUqv+hz6H/ptKm/6DPoP/D3cT/rtWv/53NnP/E3sP///j//8La
        wv8ijyL/DoYO/4/Bj//48fj/6urq/+jo6P9RUVGpAAAAAAAAAAAAAAAAAAAAAAAAAAAqKioGpKSk8///
        /////////////222bf8fjx//S5hL/+vv6///////9/v3/9Tp1P/N5s3/0OjQ/77cvv/F4MX/3+vf//r3
        +v/9+P3/tta2/yGOIf9HoUf/6evp/+/u7//s7Oz/6enp/1FRUakAAAAAAAAAAAAAAAAAAAAAAAAAACoq
        KgakpKTz////////////////4fDh/0ijSP83mTf/XJ9c/9zm3P///////////+ry6v/O387//f79//r6
        +v/8+vz/9/f3//X19f/++P7/tdW1/8neyf/38/f/7e7t/+3t7f/r6+v/UVFRqQAAAAAAAAAAAAAAAAAA
        AAAAAAAAKioqBqSkpPP/////////////////////y+XL/1GoUf9LpEv/VZ5V/5O6k//V4dX/yN3I/1CZ
        UP/O3c7///////r6+v/4+Pj/9/f3//X19f/8+Pz/+PX4//Dw8P/v7+//7+/v/+zs7P9RUVGpAAAAAAAA
        AAAAAAAAAAAAAAAAAAAqKioGpKSk8///////////////////////////2+3b/3S5dP9ZrFn/W6pb/2Cn
        YP9lp2X/ZLFk/2GkYf/L28v///7///r6+v/4+Pj/9/f3//X19f/09PT/8vLy//Hx8f/w8PD/7u7u/1FR
        UakAAAAAAAAAAAAAAAAAAAAAAAAAACoqKgakpKTz////////////////////////////////+fz5/7/f
        v/+Jw4n/dLp0/263bv+Av4D/brZu/3CtcP/09vT//v3+//r6+v/4+Pj/9/f3//X19f/09PT/8vLy//Ly
        8v/v7+//UVFRqQAAAAAAAAAAAAAAAAAAAAAAAAAAKioqBqSkpPP/////////////////////////////
        ///////////////////4/Pj/tNm0/2axZv+s1az/9Pr0///////9/f3/+/v7//r6+v/4+Pj/9/f3//X1
        9f/09PT/9PT0//Hx8f9RUVGpAAAAAAAAAAAAAAAAAAAAAAAAAAAqKioGpKSk8///////////////////
        ///////////////////////////////////t9u3/4fDh//////////////////7+/v/9/f3/+/v7//r6
        +v/4+Pj/9/f3//X19f/19fX/8vLy/1JSUqkAAAAAAAAAAAAAAAAAAAAAAAAAACoqKgakpKTz////////
        //////////////////////////////////////////////////////////////////////////////7+
        /v/9/f3/+/v7//r6+v/4+Pj/9/f3//f39//09PT/UlJSqQAAAAAAAAAAAAAAAAAAAAAAAAAAKioqBqSk
        pPP/////////////////////////////////////////////////////////////////////////////
        //////////////7+/v/9/f3/+/v7//r6+v/4+Pj/+Pj4//X19f9SUlKpAAAAAAAAAAAAAAAAAAAAAAAA
        AAA/Pz8IpaWl9P//////////////////////////////////////////////////////////////////
        /////////////////////////////////////////////////////////////1VVVasAAAAAAAAAAAAA
        AAAAAAAAAAAAAD8/PwSHh4fdqKio+qOjo/ijo6P4o6Oj+KOjo/ijo6P4o6Oj+KOjo/ijo6P4o6Oj+KOj
        o/ijo6P4o6Oj+KOjo/ijo6P4o6Oj+KOjo/ijo6P4o6Oj+KOjo/iioqL4oaGh+KGhofigoKD4RkZGkQAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAwAAA/8AAAA/AAAAHwAAAB8AAAAfAAAAHwAAAB8AAAAfAAAAHwAAAB8AA
        AAfAAAAHwAAAB8AAAAfAAAAHwAAAB8AAAAfAAAAHwAAAB8AAAAfAAAAHwAAAB8AAAAfAAAAHwAAAB8AA
        AAfAAAAHwAAAB8AAAAfAAAAHwAAAB/////8=
</value>
  </data>
</root>