﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.ZTFunc;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Func.SystemSetting;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.BackgroundFunc;

namespace MasterCom.RAMS.Net
{
    class ZTQueryBadRxQualByRegion_LTE : ZTQueryBadRxQualByRegion
    {
        private static ZTQueryBadRxQualByRegion_LTE intance = null;
        public static new ZTQueryBadRxQualByRegion_LTE GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new ZTQueryBadRxQualByRegion_LTE();
                    }
                }
            }
            return intance;
        }

        protected ZTQueryBadRxQualByRegion_LTE()
            : base()
        {
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_TDD_VOICE);
        }
        public override string Name
        {
            get { return "质差点汇聚_LTE_CSFB"; }
        }

        protected override BadRxqualBlock getBlockInstance(TestPoint tp)
        {
            return new BadRxqualBlock_LTE(tp);
        }
        protected override void getParam(TestPoint tp, out int? lac, out int? ci, out int? rxQual, out int? rxLev)
        {
            lac = (int?)tp["lte_gsm_SC_LAC"];
            ci = (int?)tp["lte_gsm_SC_CI"];
            rxQual = (int?)(byte?)tp["lte_gsm_DM_RxQualSub"];
            rxLev = (int?)(short?)tp["lte_gsm_DM_RxLevSub"];
        }

        protected override void getParam2(TestPoint tp, out int? n_tch, out int? tch, out int? bcch, out byte? bsic)
        {
            n_tch = (int?)(short?)tp["lte_gsm_DR_List_of_TCH", 0];
            tch = (int?)(short?)tp["lte_gsm_DR_TCH"];
            bcch = (int?)(short?)tp["lte_gsm_SC_BCCH"];
            bsic = (byte?)tp["lte_gsm_SC_BSIC"];
        }


        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            List<object> columnsDef = new List<object>();

            Dictionary<string, object> param = new Dictionary<string, object>();
            param["param_name"] = "lte_gsm_SC_LAC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_gsm_SC_CI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_gsm_SC_BCCH";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_gsm_SC_BSIC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_gsm_DR_TCH";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_gsm_DM_RxQualSub";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_gsm_DM_RxLevSub";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            for (int i = 0; i < 6; i++)
            {
                param = new Dictionary<string, object>();
                param["param_name"] = "lte_gsm_DR_List_of_TCH";
                param["param_arg"] = i;
                columnsDef.Add((object)param);
            }

            Dictionary<string, object> tmpDic = new Dictionary<string, object>();
            tmpDic.Add("name", (object)"LTE RxQual");
            tmpDic.Add("themeName", (object)"LTE RxQual");
            tmpDic.Add("columnsDef", (object)columnsDef);

            DIYSampleGroup group = new DIYSampleGroup();
            group.Param = tmpDic;
            return group;
        }

        protected bool isValidTestPoint_Base(TestPoint tp)
        {
            try
            {
                return Condition.Geometorys.GeoOp.Contains(tp.Longitude, tp.Latitude);
            }
            catch
            {
                try
                {
                    return Condition.Geometorys.GeoOp2.CheckPointInRegion(tp.Longitude, tp.Latitude);//网络体检后台
                }
                catch
                {
                    //continue
                }
                return false;
            }
        }
        protected override bool isValidTestPoint(TestPoint tp)
        {
            try
            {
                if (!(tp is LTETestPointDetail))
                {
                    return false;
                }
                int? lac, ci, rxQual, tem;
                this.getParam(tp, out lac, out ci, out rxQual, out tem);
                if (lac != null && ci != null && lac != 255 && ci != -255 && rxQual != null && rxQual >= 0 && rxQual <= 7 && rxQual >= rxQualThreshold)
                {
                    return isValidTestPoint_Base(tp);
                }
                return false;
            }
            catch
            {
                return false;
            }
        }
    }

    class ZTQueryBadRxQualByRegion_LTE_FDD : ZTQueryBadRxQualByRegion_LTE
    {
        private static ZTQueryBadRxQualByRegion_LTE_FDD instance = null;
        public static new ZTQueryBadRxQualByRegion_LTE_FDD GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new ZTQueryBadRxQualByRegion_LTE_FDD();
                    }
                }
            }
            return instance;
        }

        public override string Name
        {
            get { return "质差点汇聚_LTE_FDD"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 26000, 26068, this.Name);
        }
        protected ZTQueryBadRxQualByRegion_LTE_FDD()
            : base()
        {
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_FDD_VOICE);
        }
        protected override void getParam(TestPoint tp, out int? lac, out int? ci, out int? rxQual, out int? rxLev)
        {
            lac = tp.GetLAC();
            ci = tp.GetCI();
            rxQual = (int?)(float?)tp["lte_fdd_wcdma_TotalEc_Io"];
            rxLev = (int?)tp.GetRxlev();
        }

        protected override void getParam2(TestPoint tp, out int? n_tch, out int? tch, out int? bcch, out byte? bsic)
        {
            n_tch = (int?)(short?)tp["lte_fdd_gsm_DR_List_of_TCH", 0];
            tch = (int?)(short?)tp["lte_fdd_gsm_DR_TCH"];
            bcch = tp.GetBCCH();
            bsic = (byte?)tp.GetBSIC();
        }


        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            List<object> columnsDef = new List<object>();

            Dictionary<string, object> param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_wcdma_SysLAI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_wcdma_SysCellID";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_wcdma_frequency";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_wcdma_Reference_PSC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_gsm_DR_TCH";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_wcdma_TotalEc_Io";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_wcdma_TotalRSCP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            for (int i = 0; i < 6; i++)
            {
                param = new Dictionary<string, object>();
                param["param_name"] = "lte_fdd_gsm_DR_List_of_TCH";
                param["param_arg"] = i;
                columnsDef.Add((object)param);
            }

            Dictionary<string, object> tmpDic = new Dictionary<string, object>();
            tmpDic.Add("name", (object)"LTE RxQual");
            tmpDic.Add("themeName", (object)"LTE RxQual");
            tmpDic.Add("columnsDef", (object)columnsDef);

            DIYSampleGroup group = new DIYSampleGroup();
            group.Param = tmpDic;
            return group;
        }
        protected override bool isValidTestPoint(TestPoint tp)
        {
            try
            {
                if (!(tp is LTEFddTestPoint))
                {
                    return false;
                }
                int? lac, ci, rxQual, tem;
                this.getParam(tp, out lac, out ci, out rxQual, out tem);
                if (lac != null && ci != null && lac != 255 && ci != -255 && rxQual != null && rxQual >= 0 && rxQual <= 7 && rxQual >= rxQualThreshold)
                {
                    return isValidTestPoint_Base(tp);
                }
                return false;
            }
            catch
            {
                return false;
            }
        }
    }

    class BadRxqualBlock_LTE : BadRxqualBlock
    {
        public BadRxqualBlock_LTE()
            : base()
        { 
        }

        public BadRxqualBlock_LTE(TestPoint tp)
            :base(tp)
        {
        }

        protected override float? get_C_I(TestPoint tp)
        {
            return null;
        }
        protected override int? get_N_RxLev(TestPoint tp, int index)
        {
            return (int?)(short?)tp["N_RxLev", index];
        }
        protected override int? get_RxLevSub(TestPoint tp)
        {
            return (int?)(short?)tp["RxLevSub"];
        }
        protected override void getParam(TestPoint tp, out int? lac, out int? ci, out short? bcch, out byte? bsic)
        {
            if (tp is LTEFddTestPoint)
            {
                lac = (int?)tp["lte_fdd_wcdma_SysLAI"];
                ci = (int?)tp["lte_fdd_wcdma_SysCellID"];
                bcch = (short?)tp["lte_fdd_wcdma_frequency"];
                bsic = (byte?)tp["lte_fdd_wcdma_Reference_PSC"];
            }
            else
            {
                lac = (int?)tp["lte_gsm_SC_LAC"];
                ci = (int?)tp["lte_gsm_SC_CI"];
                bcch = (short?)tp["lte_gsm_SC_BCCH"];
                bsic = (byte?)tp["lte_gsm_SC_BSIC"];
            }
        }
    }
}
