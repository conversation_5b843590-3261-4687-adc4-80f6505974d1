﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc.ZTNRScanLowSinrRoad
{
    public partial class NRScanLowSinrRoadDlg : Form
    {
        public NRScanLowSinrRoadDlg()
        {
            InitializeComponent();
        }

        public NRScanLowSinrRoadCond Condition { get; set; }

        private void simpleBtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        private void simpleBtnOK_Click(object sender, EventArgs e)
        {
            Condition = new NRScanLowSinrRoadCond()
            {
                SINRMax = (int)spinEditSINR.Value,
                DistanceMin = (int)spinEditDistance.Value
            };

            this.DialogResult = DialogResult.OK;
        }

        public void ResetDlg(NRScanLowSinrRoadCond cond)
        {
            this.spinEditSINR.Value = cond.SINRMax;
            this.spinEditDistance.Value = cond.DistanceMin;
        }
    }
}
