﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class CSFBCauseSettingDlg
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.checkedListBoxControlCauses = new DevExpress.XtraEditors.CheckedListBoxControl();
            this.simpleButtonDown = new DevExpress.XtraEditors.SimpleButton();
            this.simpleButtonUp = new DevExpress.XtraEditors.SimpleButton();
            this.splitContainer1 = new System.Windows.Forms.SplitContainer();
            this.xtraTabControl1 = new DevExpress.XtraTab.XtraTabControl();
            this.xtraTabPage1 = new DevExpress.XtraTab.XtraTabPage();
            this.groupBox4 = new System.Windows.Forms.GroupBox();
            this.numericUpDownOverCovRadius = new System.Windows.Forms.NumericUpDown();
            this.numericUpDownOverCovDisMax = new System.Windows.Forms.NumericUpDown();
            this.numericUpDownOverCovBtsNum = new System.Windows.Forms.NumericUpDown();
            this.numericUpDownOverCovDisMin = new System.Windows.Forms.NumericUpDown();
            this.numericUpDownOverCovRxlev = new System.Windows.Forms.NumericUpDown();
            this.label7 = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.label8 = new System.Windows.Forms.Label();
            this.label5 = new System.Windows.Forms.Label();
            this.label6 = new System.Windows.Forms.Label();
            this.groupBox5 = new System.Windows.Forms.GroupBox();
            this.numericUpDownCovInConfAngle = new System.Windows.Forms.NumericUpDown();
            this.numericUpDownCovInConfRxlevMax = new System.Windows.Forms.NumericUpDown();
            this.numericUpDownCovInConfRxlevMin = new System.Windows.Forms.NumericUpDown();
            this.numericUpDownCovInConfDistanceMin = new System.Windows.Forms.NumericUpDown();
            this.label9 = new System.Windows.Forms.Label();
            this.label10 = new System.Windows.Forms.Label();
            this.label11 = new System.Windows.Forms.Label();
            this.label35 = new System.Windows.Forms.Label();
            this.label12 = new System.Windows.Forms.Label();
            this.groupBox6 = new System.Windows.Forms.GroupBox();
            this.numericUpDownCoverLapCovNum = new System.Windows.Forms.NumericUpDown();
            this.numericUpDownCoverLapDiff = new System.Windows.Forms.NumericUpDown();
            this.numericUpDownCoverLapMainRxlev = new System.Windows.Forms.NumericUpDown();
            this.label34 = new System.Windows.Forms.Label();
            this.label13 = new System.Windows.Forms.Label();
            this.label14 = new System.Windows.Forms.Label();
            this.label15 = new System.Windows.Forms.Label();
            this.groupBox7 = new System.Windows.Forms.GroupBox();
            this.numericUpDownFastFailureRxlevMax = new System.Windows.Forms.NumericUpDown();
            this.numericUpDownFastFailureTimeSpan = new System.Windows.Forms.NumericUpDown();
            this.numericUpDownFastFailureRxlevMin = new System.Windows.Forms.NumericUpDown();
            this.label16 = new System.Windows.Forms.Label();
            this.label19 = new System.Windows.Forms.Label();
            this.label17 = new System.Windows.Forms.Label();
            this.label18 = new System.Windows.Forms.Label();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.numericUpDownWeakCovNBMax = new System.Windows.Forms.NumericUpDown();
            this.numericUpDownWeakCovMainRxlevMax = new System.Windows.Forms.NumericUpDown();
            this.label2 = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.label1 = new System.Windows.Forms.Label();
            this.xtraTabPage2 = new DevExpress.XtraTab.XtraTabPage();
            this.groupBox9 = new System.Windows.Forms.GroupBox();
            this.label26 = new System.Windows.Forms.Label();
            this.label25 = new System.Windows.Forms.Label();
            this.numericUpDownStrongRxlevBadRxqualQualMin = new System.Windows.Forms.NumericUpDown();
            this.numericUpDownStrongRxlevBadRxqualSinrMax = new System.Windows.Forms.NumericUpDown();
            this.label24 = new System.Windows.Forms.Label();
            this.numericUpDownStrongRxlevBadRxqualRxlevMin = new System.Windows.Forms.NumericUpDown();
            this.label23 = new System.Windows.Forms.Label();
            this.groupBox8 = new System.Windows.Forms.GroupBox();
            this.label21 = new System.Windows.Forms.Label();
            this.label20 = new System.Windows.Forms.Label();
            this.numericUpDownBadRxqualQualMin = new System.Windows.Forms.NumericUpDown();
            this.label22 = new System.Windows.Forms.Label();
            this.numericUpDownBadRxqualSinrMax = new System.Windows.Forms.NumericUpDown();
            this.xtraTabPage3 = new DevExpress.XtraTab.XtraTabPage();
            this.groupBox11 = new System.Windows.Forms.GroupBox();
            this.label31 = new System.Windows.Forms.Label();
            this.groupBox10 = new System.Windows.Forms.GroupBox();
            this.label27 = new System.Windows.Forms.Label();
            this.label28 = new System.Windows.Forms.Label();
            this.numericUpDownMod3Percent = new System.Windows.Forms.NumericUpDown();
            this.numericUpDownMod3Diff = new System.Windows.Forms.NumericUpDown();
            this.label33 = new System.Windows.Forms.Label();
            this.label29 = new System.Windows.Forms.Label();
            this.numericUpDownMod3MainRxlev = new System.Windows.Forms.NumericUpDown();
            this.xtraTabPage4 = new DevExpress.XtraTab.XtraTabPage();
            this.groupBox14 = new System.Windows.Forms.GroupBox();
            this.label40 = new System.Windows.Forms.Label();
            this.label41 = new System.Windows.Forms.Label();
            this.numericUpDownUnReasonableDiff = new System.Windows.Forms.NumericUpDown();
            this.groupBox13 = new System.Windows.Forms.GroupBox();
            this.label36 = new System.Windows.Forms.Label();
            this.label37 = new System.Windows.Forms.Label();
            this.numericUpDownDelayHOPercent = new System.Windows.Forms.NumericUpDown();
            this.numericUpDownDelayHONBMax = new System.Windows.Forms.NumericUpDown();
            this.label38 = new System.Windows.Forms.Label();
            this.label39 = new System.Windows.Forms.Label();
            this.numericUpDownDelayHOMainRxlev = new System.Windows.Forms.NumericUpDown();
            this.groupBox12 = new System.Windows.Forms.GroupBox();
            this.label30 = new System.Windows.Forms.Label();
            this.label32 = new System.Windows.Forms.Label();
            this.numericUpDownFrequentHOCnt = new System.Windows.Forms.NumericUpDown();
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.simpleButtonCancel = new DevExpress.XtraEditors.SimpleButton();
            this.simpleButtonOK = new DevExpress.XtraEditors.SimpleButton();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.textBoxSuguest = new System.Windows.Forms.TextBox();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.textBoxDesc = new System.Windows.Forms.TextBox();
            ((System.ComponentModel.ISupportInitialize)(this.checkedListBoxControlCauses)).BeginInit();
            this.splitContainer1.Panel1.SuspendLayout();
            this.splitContainer1.Panel2.SuspendLayout();
            this.splitContainer1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).BeginInit();
            this.xtraTabControl1.SuspendLayout();
            this.xtraTabPage1.SuspendLayout();
            this.groupBox4.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownOverCovRadius)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownOverCovDisMax)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownOverCovBtsNum)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownOverCovDisMin)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownOverCovRxlev)).BeginInit();
            this.groupBox5.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownCovInConfAngle)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownCovInConfRxlevMax)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownCovInConfRxlevMin)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownCovInConfDistanceMin)).BeginInit();
            this.groupBox6.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownCoverLapCovNum)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownCoverLapDiff)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownCoverLapMainRxlev)).BeginInit();
            this.groupBox7.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownFastFailureRxlevMax)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownFastFailureTimeSpan)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownFastFailureRxlevMin)).BeginInit();
            this.groupBox3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownWeakCovNBMax)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownWeakCovMainRxlevMax)).BeginInit();
            this.xtraTabPage2.SuspendLayout();
            this.groupBox9.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownStrongRxlevBadRxqualQualMin)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownStrongRxlevBadRxqualSinrMax)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownStrongRxlevBadRxqualRxlevMin)).BeginInit();
            this.groupBox8.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownBadRxqualQualMin)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownBadRxqualSinrMax)).BeginInit();
            this.xtraTabPage3.SuspendLayout();
            this.groupBox11.SuspendLayout();
            this.groupBox10.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownMod3Percent)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownMod3Diff)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownMod3MainRxlev)).BeginInit();
            this.xtraTabPage4.SuspendLayout();
            this.groupBox14.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownUnReasonableDiff)).BeginInit();
            this.groupBox13.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownDelayHOPercent)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownDelayHONBMax)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownDelayHOMainRxlev)).BeginInit();
            this.groupBox12.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownFrequentHOCnt)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            this.groupBox2.SuspendLayout();
            this.groupBox1.SuspendLayout();
            this.SuspendLayout();
            // 
            // checkedListBoxControlCauses
            // 
            this.checkedListBoxControlCauses.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left)));
            this.checkedListBoxControlCauses.Location = new System.Drawing.Point(5, 26);
            this.checkedListBoxControlCauses.Name = "checkedListBoxControlCauses";
            this.checkedListBoxControlCauses.Size = new System.Drawing.Size(224, 267);
            this.checkedListBoxControlCauses.TabIndex = 0;
            this.checkedListBoxControlCauses.SelectedIndexChanged += new System.EventHandler(this.checkedListBoxControlCauses_SelectedIndexChanged);
            // 
            // simpleButtonDown
            // 
            this.simpleButtonDown.Appearance.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.simpleButtonDown.Appearance.Options.UseFont = true;
            this.simpleButtonDown.Location = new System.Drawing.Point(247, 91);
            this.simpleButtonDown.Name = "simpleButtonDown";
            this.simpleButtonDown.Size = new System.Drawing.Size(55, 23);
            this.simpleButtonDown.TabIndex = 6;
            this.simpleButtonDown.Text = "↓";
            this.simpleButtonDown.Click += new System.EventHandler(this.simpleButtonDown_Click);
            // 
            // simpleButtonUp
            // 
            this.simpleButtonUp.Appearance.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.simpleButtonUp.Appearance.Options.UseFont = true;
            this.simpleButtonUp.Location = new System.Drawing.Point(247, 58);
            this.simpleButtonUp.Name = "simpleButtonUp";
            this.simpleButtonUp.Size = new System.Drawing.Size(55, 23);
            this.simpleButtonUp.TabIndex = 5;
            this.simpleButtonUp.Text = "↑";
            this.simpleButtonUp.Click += new System.EventHandler(this.simpleButtonUp_Click);
            // 
            // splitContainer1
            // 
            this.splitContainer1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainer1.FixedPanel = System.Windows.Forms.FixedPanel.Panel1;
            this.splitContainer1.Location = new System.Drawing.Point(0, 0);
            this.splitContainer1.Name = "splitContainer1";
            this.splitContainer1.Orientation = System.Windows.Forms.Orientation.Horizontal;
            // 
            // splitContainer1.Panel1
            // 
            this.splitContainer1.Panel1.Controls.Add(this.xtraTabControl1);
            // 
            // splitContainer1.Panel2
            // 
            this.splitContainer1.Panel2.Controls.Add(this.groupControl1);
            this.splitContainer1.Size = new System.Drawing.Size(922, 506);
            this.splitContainer1.SplitterDistance = 204;
            this.splitContainer1.TabIndex = 7;
            // 
            // xtraTabControl1
            // 
            this.xtraTabControl1.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.xtraTabControl1.Location = new System.Drawing.Point(0, 0);
            this.xtraTabControl1.Name = "xtraTabControl1";
            this.xtraTabControl1.SelectedTabPage = this.xtraTabPage1;
            this.xtraTabControl1.Size = new System.Drawing.Size(922, 204);
            this.xtraTabControl1.TabIndex = 0;
            this.xtraTabControl1.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.xtraTabPage1,
            this.xtraTabPage2,
            this.xtraTabPage3,
            this.xtraTabPage4});
            // 
            // xtraTabPage1
            // 
            this.xtraTabPage1.Controls.Add(this.groupBox4);
            this.xtraTabPage1.Controls.Add(this.groupBox5);
            this.xtraTabPage1.Controls.Add(this.groupBox6);
            this.xtraTabPage1.Controls.Add(this.groupBox7);
            this.xtraTabPage1.Controls.Add(this.groupBox3);
            this.xtraTabPage1.Name = "xtraTabPage1";
            this.xtraTabPage1.Size = new System.Drawing.Size(915, 174);
            this.xtraTabPage1.Text = "覆盖类";
            // 
            // groupBox4
            // 
            this.groupBox4.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBox4.Controls.Add(this.numericUpDownOverCovRadius);
            this.groupBox4.Controls.Add(this.numericUpDownOverCovDisMax);
            this.groupBox4.Controls.Add(this.numericUpDownOverCovBtsNum);
            this.groupBox4.Controls.Add(this.numericUpDownOverCovDisMin);
            this.groupBox4.Controls.Add(this.numericUpDownOverCovRxlev);
            this.groupBox4.Controls.Add(this.label7);
            this.groupBox4.Controls.Add(this.label4);
            this.groupBox4.Controls.Add(this.label8);
            this.groupBox4.Controls.Add(this.label5);
            this.groupBox4.Controls.Add(this.label6);
            this.groupBox4.Location = new System.Drawing.Point(411, 0);
            this.groupBox4.Name = "groupBox4";
            this.groupBox4.Size = new System.Drawing.Size(501, 73);
            this.groupBox4.TabIndex = 0;
            this.groupBox4.TabStop = false;
            this.groupBox4.Text = "过覆盖";
            // 
            // numericUpDownOverCovRadius
            // 
            this.numericUpDownOverCovRadius.DecimalPlaces = 1;
            this.numericUpDownOverCovRadius.Increment = new decimal(new int[] {
            1,
            0,
            0,
            65536});
            this.numericUpDownOverCovRadius.Location = new System.Drawing.Point(443, 18);
            this.numericUpDownOverCovRadius.Name = "numericUpDownOverCovRadius";
            this.numericUpDownOverCovRadius.Size = new System.Drawing.Size(50, 22);
            this.numericUpDownOverCovRadius.TabIndex = 1;
            this.numericUpDownOverCovRadius.Value = new decimal(new int[] {
            16,
            0,
            0,
            65536});
            // 
            // numericUpDownOverCovDisMax
            // 
            this.numericUpDownOverCovDisMax.Location = new System.Drawing.Point(206, 43);
            this.numericUpDownOverCovDisMax.Maximum = new decimal(new int[] {
            100000,
            0,
            0,
            0});
            this.numericUpDownOverCovDisMax.Name = "numericUpDownOverCovDisMax";
            this.numericUpDownOverCovDisMax.Size = new System.Drawing.Size(50, 22);
            this.numericUpDownOverCovDisMax.TabIndex = 1;
            this.numericUpDownOverCovDisMax.Value = new decimal(new int[] {
            300,
            0,
            0,
            0});
            // 
            // numericUpDownOverCovBtsNum
            // 
            this.numericUpDownOverCovBtsNum.Location = new System.Drawing.Point(264, 18);
            this.numericUpDownOverCovBtsNum.Name = "numericUpDownOverCovBtsNum";
            this.numericUpDownOverCovBtsNum.Size = new System.Drawing.Size(50, 22);
            this.numericUpDownOverCovBtsNum.TabIndex = 1;
            this.numericUpDownOverCovBtsNum.Value = new decimal(new int[] {
            3,
            0,
            0,
            0});
            // 
            // numericUpDownOverCovDisMin
            // 
            this.numericUpDownOverCovDisMin.Location = new System.Drawing.Point(57, 43);
            this.numericUpDownOverCovDisMin.Maximum = new decimal(new int[] {
            100000,
            0,
            0,
            0});
            this.numericUpDownOverCovDisMin.Name = "numericUpDownOverCovDisMin";
            this.numericUpDownOverCovDisMin.Size = new System.Drawing.Size(50, 22);
            this.numericUpDownOverCovDisMin.TabIndex = 1;
            this.numericUpDownOverCovDisMin.Value = new decimal(new int[] {
            100,
            0,
            0,
            0});
            // 
            // numericUpDownOverCovRxlev
            // 
            this.numericUpDownOverCovRxlev.Location = new System.Drawing.Point(57, 18);
            this.numericUpDownOverCovRxlev.Minimum = new decimal(new int[] {
            141,
            0,
            0,
            -2147483648});
            this.numericUpDownOverCovRxlev.Name = "numericUpDownOverCovRxlev";
            this.numericUpDownOverCovRxlev.Size = new System.Drawing.Size(50, 22);
            this.numericUpDownOverCovRxlev.TabIndex = 1;
            this.numericUpDownOverCovRxlev.Value = new decimal(new int[] {
            90,
            0,
            0,
            -2147483648});
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(110, 47);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(95, 14);
            this.label7.TabIndex = 0;
            this.label7.Text = "m<过覆盖距离<";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(110, 22);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(153, 14);
            this.label4.TabIndex = 0;
            this.label4.Text = "Bm，理想覆盖参考基站数=";
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(262, 47);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(17, 14);
            this.label8.TabIndex = 0;
            this.label8.Text = "m";
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(320, 22);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(124, 14);
            this.label5.TabIndex = 0;
            this.label5.Text = "，理想覆盖半径系数=";
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(11, 22);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(40, 14);
            this.label6.TabIndex = 0;
            this.label6.Text = "场强>";
            // 
            // groupBox5
            // 
            this.groupBox5.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBox5.Controls.Add(this.numericUpDownCovInConfAngle);
            this.groupBox5.Controls.Add(this.numericUpDownCovInConfRxlevMax);
            this.groupBox5.Controls.Add(this.numericUpDownCovInConfRxlevMin);
            this.groupBox5.Controls.Add(this.numericUpDownCovInConfDistanceMin);
            this.groupBox5.Controls.Add(this.label9);
            this.groupBox5.Controls.Add(this.label10);
            this.groupBox5.Controls.Add(this.label11);
            this.groupBox5.Controls.Add(this.label35);
            this.groupBox5.Controls.Add(this.label12);
            this.groupBox5.Location = new System.Drawing.Point(411, 79);
            this.groupBox5.Name = "groupBox5";
            this.groupBox5.Size = new System.Drawing.Size(501, 48);
            this.groupBox5.TabIndex = 0;
            this.groupBox5.TabStop = false;
            this.groupBox5.Text = "覆盖不符";
            // 
            // numericUpDownCovInConfAngle
            // 
            this.numericUpDownCovInConfAngle.Location = new System.Drawing.Point(222, 18);
            this.numericUpDownCovInConfAngle.Maximum = new decimal(new int[] {
            360,
            0,
            0,
            0});
            this.numericUpDownCovInConfAngle.Name = "numericUpDownCovInConfAngle";
            this.numericUpDownCovInConfAngle.Size = new System.Drawing.Size(50, 22);
            this.numericUpDownCovInConfAngle.TabIndex = 1;
            this.numericUpDownCovInConfAngle.Value = new decimal(new int[] {
            60,
            0,
            0,
            0});
            // 
            // numericUpDownCovInConfRxlevMax
            // 
            this.numericUpDownCovInConfRxlevMax.Location = new System.Drawing.Point(405, 18);
            this.numericUpDownCovInConfRxlevMax.Minimum = new decimal(new int[] {
            141,
            0,
            0,
            -2147483648});
            this.numericUpDownCovInConfRxlevMax.Name = "numericUpDownCovInConfRxlevMax";
            this.numericUpDownCovInConfRxlevMax.Size = new System.Drawing.Size(50, 22);
            this.numericUpDownCovInConfRxlevMax.TabIndex = 1;
            this.numericUpDownCovInConfRxlevMax.Value = new decimal(new int[] {
            90,
            0,
            0,
            -2147483648});
            // 
            // numericUpDownCovInConfRxlevMin
            // 
            this.numericUpDownCovInConfRxlevMin.Location = new System.Drawing.Point(321, 18);
            this.numericUpDownCovInConfRxlevMin.Minimum = new decimal(new int[] {
            141,
            0,
            0,
            -2147483648});
            this.numericUpDownCovInConfRxlevMin.Name = "numericUpDownCovInConfRxlevMin";
            this.numericUpDownCovInConfRxlevMin.Size = new System.Drawing.Size(50, 22);
            this.numericUpDownCovInConfRxlevMin.TabIndex = 1;
            this.numericUpDownCovInConfRxlevMin.Value = new decimal(new int[] {
            110,
            0,
            0,
            -2147483648});
            // 
            // numericUpDownCovInConfDistanceMin
            // 
            this.numericUpDownCovInConfDistanceMin.Location = new System.Drawing.Point(79, 18);
            this.numericUpDownCovInConfDistanceMin.Maximum = new decimal(new int[] {
            100000,
            0,
            0,
            0});
            this.numericUpDownCovInConfDistanceMin.Name = "numericUpDownCovInConfDistanceMin";
            this.numericUpDownCovInConfDistanceMin.Size = new System.Drawing.Size(50, 22);
            this.numericUpDownCovInConfDistanceMin.TabIndex = 1;
            this.numericUpDownCovInConfDistanceMin.Value = new decimal(new int[] {
            200,
            0,
            0,
            0});
            // 
            // label9
            // 
            this.label9.AutoSize = true;
            this.label9.Location = new System.Drawing.Point(132, 22);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(90, 14);
            this.label9.TabIndex = 0;
            this.label9.Text = "m,与主服夹角>";
            // 
            // label10
            // 
            this.label10.AutoSize = true;
            this.label10.Location = new System.Drawing.Point(271, 22);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(52, 14);
            this.label10.TabIndex = 0;
            this.label10.Text = "度,场强(";
            // 
            // label11
            // 
            this.label11.AutoSize = true;
            this.label11.Location = new System.Drawing.Point(4, 22);
            this.label11.Name = "label11";
            this.label11.Size = new System.Drawing.Size(76, 14);
            this.label11.TabIndex = 0;
            this.label11.Text = "与主服距离>";
            // 
            // label35
            // 
            this.label35.AutoSize = true;
            this.label35.Location = new System.Drawing.Point(455, 22);
            this.label35.Name = "label35";
            this.label35.Size = new System.Drawing.Size(36, 14);
            this.label35.TabIndex = 0;
            this.label35.Text = "dBm)";
            // 
            // label12
            // 
            this.label12.AutoSize = true;
            this.label12.Location = new System.Drawing.Point(369, 22);
            this.label12.Name = "label12";
            this.label12.Size = new System.Drawing.Size(35, 14);
            this.label12.TabIndex = 0;
            this.label12.Text = "dBm,";
            // 
            // groupBox6
            // 
            this.groupBox6.Controls.Add(this.numericUpDownCoverLapCovNum);
            this.groupBox6.Controls.Add(this.numericUpDownCoverLapDiff);
            this.groupBox6.Controls.Add(this.numericUpDownCoverLapMainRxlev);
            this.groupBox6.Controls.Add(this.label34);
            this.groupBox6.Controls.Add(this.label13);
            this.groupBox6.Controls.Add(this.label14);
            this.groupBox6.Controls.Add(this.label15);
            this.groupBox6.Location = new System.Drawing.Point(0, 47);
            this.groupBox6.Name = "groupBox6";
            this.groupBox6.Size = new System.Drawing.Size(405, 80);
            this.groupBox6.TabIndex = 0;
            this.groupBox6.TabStop = false;
            this.groupBox6.Text = "重叠覆盖";
            // 
            // numericUpDownCoverLapCovNum
            // 
            this.numericUpDownCoverLapCovNum.Location = new System.Drawing.Point(229, 48);
            this.numericUpDownCoverLapCovNum.Name = "numericUpDownCoverLapCovNum";
            this.numericUpDownCoverLapCovNum.Size = new System.Drawing.Size(50, 22);
            this.numericUpDownCoverLapCovNum.TabIndex = 1;
            this.numericUpDownCoverLapCovNum.Value = new decimal(new int[] {
            3,
            0,
            0,
            0});
            // 
            // numericUpDownCoverLapDiff
            // 
            this.numericUpDownCoverLapDiff.Location = new System.Drawing.Point(349, 18);
            this.numericUpDownCoverLapDiff.Name = "numericUpDownCoverLapDiff";
            this.numericUpDownCoverLapDiff.Size = new System.Drawing.Size(50, 22);
            this.numericUpDownCoverLapDiff.TabIndex = 1;
            this.numericUpDownCoverLapDiff.Value = new decimal(new int[] {
            6,
            0,
            0,
            0});
            // 
            // numericUpDownCoverLapMainRxlev
            // 
            this.numericUpDownCoverLapMainRxlev.Location = new System.Drawing.Point(77, 18);
            this.numericUpDownCoverLapMainRxlev.Minimum = new decimal(new int[] {
            141,
            0,
            0,
            -2147483648});
            this.numericUpDownCoverLapMainRxlev.Name = "numericUpDownCoverLapMainRxlev";
            this.numericUpDownCoverLapMainRxlev.Size = new System.Drawing.Size(50, 22);
            this.numericUpDownCoverLapMainRxlev.TabIndex = 1;
            this.numericUpDownCoverLapMainRxlev.Value = new decimal(new int[] {
            100,
            0,
            0,
            -2147483648});
            // 
            // label34
            // 
            this.label34.AutoSize = true;
            this.label34.Location = new System.Drawing.Point(285, 52);
            this.label34.Name = "label34";
            this.label34.Size = new System.Drawing.Size(12, 14);
            this.label34.TabIndex = 0;
            this.label34.Text = ")";
            // 
            // label13
            // 
            this.label13.AutoSize = true;
            this.label13.Location = new System.Drawing.Point(128, 22);
            this.label13.Name = "label13";
            this.label13.Size = new System.Drawing.Size(220, 14);
            this.label13.TabIndex = 0;
            this.label13.Text = "dBm，邻区中信号场强与主服信号差异<";
            // 
            // label14
            // 
            this.label14.AutoSize = true;
            this.label14.Location = new System.Drawing.Point(11, 52);
            this.label14.Name = "label14";
            this.label14.Size = new System.Drawing.Size(217, 14);
            this.label14.TabIndex = 0;
            this.label14.Text = "dB，（回看5秒采样点重叠覆盖度均值≥";
            // 
            // label15
            // 
            this.label15.AutoSize = true;
            this.label15.Location = new System.Drawing.Point(11, 22);
            this.label15.Name = "label15";
            this.label15.Size = new System.Drawing.Size(67, 14);
            this.label15.TabIndex = 0;
            this.label15.Text = "主服场强＞";
            // 
            // groupBox7
            // 
            this.groupBox7.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBox7.Controls.Add(this.numericUpDownFastFailureRxlevMax);
            this.groupBox7.Controls.Add(this.numericUpDownFastFailureTimeSpan);
            this.groupBox7.Controls.Add(this.numericUpDownFastFailureRxlevMin);
            this.groupBox7.Controls.Add(this.label16);
            this.groupBox7.Controls.Add(this.label19);
            this.groupBox7.Controls.Add(this.label17);
            this.groupBox7.Controls.Add(this.label18);
            this.groupBox7.Location = new System.Drawing.Point(0, 126);
            this.groupBox7.Name = "groupBox7";
            this.groupBox7.Size = new System.Drawing.Size(912, 46);
            this.groupBox7.TabIndex = 0;
            this.groupBox7.TabStop = false;
            this.groupBox7.Text = "场强快衰";
            // 
            // numericUpDownFastFailureRxlevMax
            // 
            this.numericUpDownFastFailureRxlevMax.Location = new System.Drawing.Point(566, 17);
            this.numericUpDownFastFailureRxlevMax.Minimum = new decimal(new int[] {
            141,
            0,
            0,
            -2147483648});
            this.numericUpDownFastFailureRxlevMax.Name = "numericUpDownFastFailureRxlevMax";
            this.numericUpDownFastFailureRxlevMax.Size = new System.Drawing.Size(50, 22);
            this.numericUpDownFastFailureRxlevMax.TabIndex = 1;
            this.numericUpDownFastFailureRxlevMax.Value = new decimal(new int[] {
            95,
            0,
            0,
            -2147483648});
            // 
            // numericUpDownFastFailureTimeSpan
            // 
            this.numericUpDownFastFailureTimeSpan.Location = new System.Drawing.Point(412, 17);
            this.numericUpDownFastFailureTimeSpan.Name = "numericUpDownFastFailureTimeSpan";
            this.numericUpDownFastFailureTimeSpan.Size = new System.Drawing.Size(50, 22);
            this.numericUpDownFastFailureTimeSpan.TabIndex = 1;
            this.numericUpDownFastFailureTimeSpan.Value = new decimal(new int[] {
            3,
            0,
            0,
            0});
            // 
            // numericUpDownFastFailureRxlevMin
            // 
            this.numericUpDownFastFailureRxlevMin.Location = new System.Drawing.Point(326, 17);
            this.numericUpDownFastFailureRxlevMin.Minimum = new decimal(new int[] {
            141,
            0,
            0,
            -2147483648});
            this.numericUpDownFastFailureRxlevMin.Name = "numericUpDownFastFailureRxlevMin";
            this.numericUpDownFastFailureRxlevMin.Size = new System.Drawing.Size(50, 22);
            this.numericUpDownFastFailureRxlevMin.TabIndex = 1;
            this.numericUpDownFastFailureRxlevMin.Value = new decimal(new int[] {
            80,
            0,
            0,
            -2147483648});
            // 
            // label16
            // 
            this.label16.AutoSize = true;
            this.label16.Location = new System.Drawing.Point(376, 21);
            this.label16.Name = "label16";
            this.label16.Size = new System.Drawing.Size(43, 14);
            this.label16.TabIndex = 0;
            this.label16.Text = "dBm，";
            // 
            // label19
            // 
            this.label19.AutoSize = true;
            this.label19.Location = new System.Drawing.Point(619, 21);
            this.label19.Name = "label19";
            this.label19.Size = new System.Drawing.Size(31, 14);
            this.label19.TabIndex = 0;
            this.label19.Text = "dBm";
            // 
            // label17
            // 
            this.label17.AutoSize = true;
            this.label17.Location = new System.Drawing.Point(464, 21);
            this.label17.Name = "label17";
            this.label17.Size = new System.Drawing.Size(103, 14);
            this.label17.TabIndex = 0;
            this.label17.Text = "秒钟以内衰减到＜";
            // 
            // label18
            // 
            this.label18.AutoSize = true;
            this.label18.Location = new System.Drawing.Point(11, 21);
            this.label18.Name = "label18";
            this.label18.Size = new System.Drawing.Size(314, 14);
            this.label18.TabIndex = 0;
            this.label18.Text = "回看5秒，看发生问题的小区是否存在场强快衰，场强从＞";
            // 
            // groupBox3
            // 
            this.groupBox3.Controls.Add(this.numericUpDownWeakCovNBMax);
            this.groupBox3.Controls.Add(this.numericUpDownWeakCovMainRxlevMax);
            this.groupBox3.Controls.Add(this.label2);
            this.groupBox3.Controls.Add(this.label3);
            this.groupBox3.Controls.Add(this.label1);
            this.groupBox3.Location = new System.Drawing.Point(0, 0);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.Size = new System.Drawing.Size(405, 46);
            this.groupBox3.TabIndex = 0;
            this.groupBox3.TabStop = false;
            this.groupBox3.Text = "弱覆盖";
            // 
            // numericUpDownWeakCovNBMax
            // 
            this.numericUpDownWeakCovNBMax.Location = new System.Drawing.Point(231, 18);
            this.numericUpDownWeakCovNBMax.Minimum = new decimal(new int[] {
            141,
            0,
            0,
            -2147483648});
            this.numericUpDownWeakCovNBMax.Name = "numericUpDownWeakCovNBMax";
            this.numericUpDownWeakCovNBMax.Size = new System.Drawing.Size(50, 22);
            this.numericUpDownWeakCovNBMax.TabIndex = 1;
            this.numericUpDownWeakCovNBMax.Value = new decimal(new int[] {
            105,
            0,
            0,
            -2147483648});
            // 
            // numericUpDownWeakCovMainRxlevMax
            // 
            this.numericUpDownWeakCovMainRxlevMax.Location = new System.Drawing.Point(57, 18);
            this.numericUpDownWeakCovMainRxlevMax.Minimum = new decimal(new int[] {
            141,
            0,
            0,
            -2147483648});
            this.numericUpDownWeakCovMainRxlevMax.Name = "numericUpDownWeakCovMainRxlevMax";
            this.numericUpDownWeakCovMainRxlevMax.Size = new System.Drawing.Size(50, 22);
            this.numericUpDownWeakCovMainRxlevMax.TabIndex = 1;
            this.numericUpDownWeakCovMainRxlevMax.Value = new decimal(new int[] {
            100,
            0,
            0,
            -2147483648});
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(110, 22);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(117, 14);
            this.label2.TabIndex = 0;
            this.label2.Text = "Bm，最强邻区场强<";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(287, 22);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(122, 14);
            this.label3.TabIndex = 0;
            this.label3.Text = "dBm（回看5秒均值）";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(11, 22);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(40, 14);
            this.label1.TabIndex = 0;
            this.label1.Text = "场强<";
            // 
            // xtraTabPage2
            // 
            this.xtraTabPage2.Controls.Add(this.groupBox9);
            this.xtraTabPage2.Controls.Add(this.groupBox8);
            this.xtraTabPage2.Name = "xtraTabPage2";
            this.xtraTabPage2.Size = new System.Drawing.Size(915, 174);
            this.xtraTabPage2.Text = "质差类";
            // 
            // groupBox9
            // 
            this.groupBox9.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBox9.Controls.Add(this.label26);
            this.groupBox9.Controls.Add(this.label25);
            this.groupBox9.Controls.Add(this.numericUpDownStrongRxlevBadRxqualQualMin);
            this.groupBox9.Controls.Add(this.numericUpDownStrongRxlevBadRxqualSinrMax);
            this.groupBox9.Controls.Add(this.label24);
            this.groupBox9.Controls.Add(this.numericUpDownStrongRxlevBadRxqualRxlevMin);
            this.groupBox9.Controls.Add(this.label23);
            this.groupBox9.Location = new System.Drawing.Point(3, 87);
            this.groupBox9.Name = "groupBox9";
            this.groupBox9.Size = new System.Drawing.Size(905, 77);
            this.groupBox9.TabIndex = 3;
            this.groupBox9.TabStop = false;
            this.groupBox9.Text = "强信号弱质量（干扰&硬件故障）";
            // 
            // label26
            // 
            this.label26.AutoSize = true;
            this.label26.Location = new System.Drawing.Point(386, 37);
            this.label26.Name = "label26";
            this.label26.Size = new System.Drawing.Size(98, 14);
            this.label26.TabIndex = 0;
            this.label26.Text = "（回看5秒均值）";
            // 
            // label25
            // 
            this.label25.AutoSize = true;
            this.label25.Location = new System.Drawing.Point(112, 37);
            this.label25.Name = "label25";
            this.label25.Size = new System.Drawing.Size(70, 14);
            this.label25.TabIndex = 0;
            this.label25.Text = "dBm,SINR<";
            // 
            // numericUpDownStrongRxlevBadRxqualQualMin
            // 
            this.numericUpDownStrongRxlevBadRxqualQualMin.Location = new System.Drawing.Point(332, 33);
            this.numericUpDownStrongRxlevBadRxqualQualMin.Name = "numericUpDownStrongRxlevBadRxqualQualMin";
            this.numericUpDownStrongRxlevBadRxqualQualMin.Size = new System.Drawing.Size(50, 22);
            this.numericUpDownStrongRxlevBadRxqualQualMin.TabIndex = 2;
            this.numericUpDownStrongRxlevBadRxqualQualMin.Value = new decimal(new int[] {
            4,
            0,
            0,
            0});
            // 
            // numericUpDownStrongRxlevBadRxqualSinrMax
            // 
            this.numericUpDownStrongRxlevBadRxqualSinrMax.Location = new System.Drawing.Point(183, 33);
            this.numericUpDownStrongRxlevBadRxqualSinrMax.Minimum = new decimal(new int[] {
            141,
            0,
            0,
            -2147483648});
            this.numericUpDownStrongRxlevBadRxqualSinrMax.Name = "numericUpDownStrongRxlevBadRxqualSinrMax";
            this.numericUpDownStrongRxlevBadRxqualSinrMax.Size = new System.Drawing.Size(50, 22);
            this.numericUpDownStrongRxlevBadRxqualSinrMax.TabIndex = 2;
            this.numericUpDownStrongRxlevBadRxqualSinrMax.Value = new decimal(new int[] {
            5,
            0,
            0,
            0});
            // 
            // label24
            // 
            this.label24.AutoSize = true;
            this.label24.Location = new System.Drawing.Point(11, 37);
            this.label24.Name = "label24";
            this.label24.Size = new System.Drawing.Size(40, 14);
            this.label24.TabIndex = 0;
            this.label24.Text = "场强>";
            // 
            // numericUpDownStrongRxlevBadRxqualRxlevMin
            // 
            this.numericUpDownStrongRxlevBadRxqualRxlevMin.Location = new System.Drawing.Point(57, 33);
            this.numericUpDownStrongRxlevBadRxqualRxlevMin.Minimum = new decimal(new int[] {
            141,
            0,
            0,
            -2147483648});
            this.numericUpDownStrongRxlevBadRxqualRxlevMin.Name = "numericUpDownStrongRxlevBadRxqualRxlevMin";
            this.numericUpDownStrongRxlevBadRxqualRxlevMin.Size = new System.Drawing.Size(50, 22);
            this.numericUpDownStrongRxlevBadRxqualRxlevMin.TabIndex = 2;
            this.numericUpDownStrongRxlevBadRxqualRxlevMin.Value = new decimal(new int[] {
            85,
            0,
            0,
            -2147483648});
            // 
            // label23
            // 
            this.label23.AutoSize = true;
            this.label23.Location = new System.Drawing.Point(239, 37);
            this.label23.Name = "label23";
            this.label23.Size = new System.Drawing.Size(92, 14);
            this.label23.TabIndex = 0;
            this.label23.Text = "dB，RxQuality>";
            // 
            // groupBox8
            // 
            this.groupBox8.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBox8.Controls.Add(this.label21);
            this.groupBox8.Controls.Add(this.label20);
            this.groupBox8.Controls.Add(this.numericUpDownBadRxqualQualMin);
            this.groupBox8.Controls.Add(this.label22);
            this.groupBox8.Controls.Add(this.numericUpDownBadRxqualSinrMax);
            this.groupBox8.Location = new System.Drawing.Point(3, 3);
            this.groupBox8.Name = "groupBox8";
            this.groupBox8.Size = new System.Drawing.Size(905, 77);
            this.groupBox8.TabIndex = 3;
            this.groupBox8.TabStop = false;
            this.groupBox8.Text = "质差";
            // 
            // label21
            // 
            this.label21.AutoSize = true;
            this.label21.Location = new System.Drawing.Point(114, 34);
            this.label21.Name = "label21";
            this.label21.Size = new System.Drawing.Size(92, 14);
            this.label21.TabIndex = 0;
            this.label21.Text = "dB，RxQuality>";
            // 
            // label20
            // 
            this.label20.AutoSize = true;
            this.label20.Location = new System.Drawing.Point(11, 34);
            this.label20.Name = "label20";
            this.label20.Size = new System.Drawing.Size(42, 14);
            this.label20.TabIndex = 0;
            this.label20.Text = "SINR<";
            // 
            // numericUpDownBadRxqualQualMin
            // 
            this.numericUpDownBadRxqualQualMin.Location = new System.Drawing.Point(214, 30);
            this.numericUpDownBadRxqualQualMin.Name = "numericUpDownBadRxqualQualMin";
            this.numericUpDownBadRxqualQualMin.Size = new System.Drawing.Size(50, 22);
            this.numericUpDownBadRxqualQualMin.TabIndex = 2;
            this.numericUpDownBadRxqualQualMin.Value = new decimal(new int[] {
            4,
            0,
            0,
            0});
            // 
            // label22
            // 
            this.label22.AutoSize = true;
            this.label22.Location = new System.Drawing.Point(270, 34);
            this.label22.Name = "label22";
            this.label22.Size = new System.Drawing.Size(98, 14);
            this.label22.TabIndex = 0;
            this.label22.Text = "（回看5秒均值）";
            // 
            // numericUpDownBadRxqualSinrMax
            // 
            this.numericUpDownBadRxqualSinrMax.Location = new System.Drawing.Point(58, 30);
            this.numericUpDownBadRxqualSinrMax.Minimum = new decimal(new int[] {
            141,
            0,
            0,
            -2147483648});
            this.numericUpDownBadRxqualSinrMax.Name = "numericUpDownBadRxqualSinrMax";
            this.numericUpDownBadRxqualSinrMax.Size = new System.Drawing.Size(50, 22);
            this.numericUpDownBadRxqualSinrMax.TabIndex = 2;
            this.numericUpDownBadRxqualSinrMax.Value = new decimal(new int[] {
            3,
            0,
            0,
            0});
            // 
            // xtraTabPage3
            // 
            this.xtraTabPage3.Controls.Add(this.groupBox11);
            this.xtraTabPage3.Controls.Add(this.groupBox10);
            this.xtraTabPage3.Name = "xtraTabPage3";
            this.xtraTabPage3.Size = new System.Drawing.Size(915, 174);
            this.xtraTabPage3.Text = "干扰类";
            // 
            // groupBox11
            // 
            this.groupBox11.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBox11.Controls.Add(this.label31);
            this.groupBox11.Location = new System.Drawing.Point(3, 86);
            this.groupBox11.Name = "groupBox11";
            this.groupBox11.Size = new System.Drawing.Size(905, 77);
            this.groupBox11.TabIndex = 4;
            this.groupBox11.TabStop = false;
            this.groupBox11.Text = "室分外泄";
            // 
            // label31
            // 
            this.label31.AutoSize = true;
            this.label31.Location = new System.Drawing.Point(11, 34);
            this.label31.Name = "label31";
            this.label31.Size = new System.Drawing.Size(187, 14);
            this.label31.TabIndex = 0;
            this.label31.Text = "看问题发生时小区是否为室分小区";
            // 
            // groupBox10
            // 
            this.groupBox10.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBox10.Controls.Add(this.label27);
            this.groupBox10.Controls.Add(this.label28);
            this.groupBox10.Controls.Add(this.numericUpDownMod3Percent);
            this.groupBox10.Controls.Add(this.numericUpDownMod3Diff);
            this.groupBox10.Controls.Add(this.label33);
            this.groupBox10.Controls.Add(this.label29);
            this.groupBox10.Controls.Add(this.numericUpDownMod3MainRxlev);
            this.groupBox10.Location = new System.Drawing.Point(3, 3);
            this.groupBox10.Name = "groupBox10";
            this.groupBox10.Size = new System.Drawing.Size(905, 77);
            this.groupBox10.TabIndex = 4;
            this.groupBox10.TabStop = false;
            this.groupBox10.Text = "模三干扰";
            // 
            // label27
            // 
            this.label27.AutoSize = true;
            this.label27.Location = new System.Drawing.Point(215, 34);
            this.label27.Name = "label27";
            this.label27.Size = new System.Drawing.Size(175, 14);
            this.label27.TabIndex = 0;
            this.label27.Text = "dBm，邻区和主服小区电平相差";
            // 
            // label28
            // 
            this.label28.AutoSize = true;
            this.label28.Location = new System.Drawing.Point(11, 34);
            this.label28.Name = "label28";
            this.label28.Size = new System.Drawing.Size(146, 14);
            this.label28.TabIndex = 0;
            this.label28.Text = "回看5秒，主服小区场强＞";
            // 
            // numericUpDownMod3Percent
            // 
            this.numericUpDownMod3Percent.Location = new System.Drawing.Point(594, 30);
            this.numericUpDownMod3Percent.Name = "numericUpDownMod3Percent";
            this.numericUpDownMod3Percent.Size = new System.Drawing.Size(50, 22);
            this.numericUpDownMod3Percent.TabIndex = 2;
            this.numericUpDownMod3Percent.Value = new decimal(new int[] {
            60,
            0,
            0,
            0});
            // 
            // numericUpDownMod3Diff
            // 
            this.numericUpDownMod3Diff.Location = new System.Drawing.Point(395, 30);
            this.numericUpDownMod3Diff.Name = "numericUpDownMod3Diff";
            this.numericUpDownMod3Diff.Size = new System.Drawing.Size(50, 22);
            this.numericUpDownMod3Diff.TabIndex = 2;
            this.numericUpDownMod3Diff.Value = new decimal(new int[] {
            6,
            0,
            0,
            0});
            // 
            // label33
            // 
            this.label33.AutoSize = true;
            this.label33.Location = new System.Drawing.Point(649, 34);
            this.label33.Name = "label33";
            this.label33.Size = new System.Drawing.Size(103, 14);
            this.label33.TabIndex = 0;
            this.label33.Text = "%点满足这个条件";
            // 
            // label29
            // 
            this.label29.AutoSize = true;
            this.label29.Location = new System.Drawing.Point(453, 34);
            this.label29.Name = "label29";
            this.label29.Size = new System.Drawing.Size(141, 14);
            this.label29.TabIndex = 0;
            this.label29.Text = "db以内且存在模三干扰，";
            // 
            // numericUpDownMod3MainRxlev
            // 
            this.numericUpDownMod3MainRxlev.Location = new System.Drawing.Point(159, 30);
            this.numericUpDownMod3MainRxlev.Minimum = new decimal(new int[] {
            141,
            0,
            0,
            -2147483648});
            this.numericUpDownMod3MainRxlev.Name = "numericUpDownMod3MainRxlev";
            this.numericUpDownMod3MainRxlev.Size = new System.Drawing.Size(50, 22);
            this.numericUpDownMod3MainRxlev.TabIndex = 2;
            this.numericUpDownMod3MainRxlev.Value = new decimal(new int[] {
            95,
            0,
            0,
            -2147483648});
            // 
            // xtraTabPage4
            // 
            this.xtraTabPage4.Controls.Add(this.groupBox14);
            this.xtraTabPage4.Controls.Add(this.groupBox13);
            this.xtraTabPage4.Controls.Add(this.groupBox12);
            this.xtraTabPage4.Name = "xtraTabPage4";
            this.xtraTabPage4.Size = new System.Drawing.Size(915, 174);
            this.xtraTabPage4.Text = "切换类";
            // 
            // groupBox14
            // 
            this.groupBox14.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBox14.Controls.Add(this.label40);
            this.groupBox14.Controls.Add(this.label41);
            this.groupBox14.Controls.Add(this.numericUpDownUnReasonableDiff);
            this.groupBox14.Location = new System.Drawing.Point(1, 110);
            this.groupBox14.Name = "groupBox14";
            this.groupBox14.Size = new System.Drawing.Size(907, 50);
            this.groupBox14.TabIndex = 5;
            this.groupBox14.TabStop = false;
            this.groupBox14.Text = "切换不合理";
            // 
            // label40
            // 
            this.label40.AutoSize = true;
            this.label40.Location = new System.Drawing.Point(494, 22);
            this.label40.Name = "label40";
            this.label40.Size = new System.Drawing.Size(45, 14);
            this.label40.TabIndex = 0;
            this.label40.Text = "db以上";
            // 
            // label41
            // 
            this.label41.AutoSize = true;
            this.label41.Location = new System.Drawing.Point(11, 22);
            this.label41.Name = "label41";
            this.label41.Size = new System.Drawing.Size(427, 14);
            this.label41.TabIndex = 0;
            this.label41.Text = "看未接通占用小区是否是切换不合理小区，切换后小区场强比切换前小区场强低";
            // 
            // numericUpDownUnReasonableDiff
            // 
            this.numericUpDownUnReasonableDiff.Location = new System.Drawing.Point(440, 18);
            this.numericUpDownUnReasonableDiff.Name = "numericUpDownUnReasonableDiff";
            this.numericUpDownUnReasonableDiff.Size = new System.Drawing.Size(50, 22);
            this.numericUpDownUnReasonableDiff.TabIndex = 2;
            this.numericUpDownUnReasonableDiff.Value = new decimal(new int[] {
            5,
            0,
            0,
            0});
            // 
            // groupBox13
            // 
            this.groupBox13.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBox13.Controls.Add(this.label36);
            this.groupBox13.Controls.Add(this.label37);
            this.groupBox13.Controls.Add(this.numericUpDownDelayHOPercent);
            this.groupBox13.Controls.Add(this.numericUpDownDelayHONBMax);
            this.groupBox13.Controls.Add(this.label38);
            this.groupBox13.Controls.Add(this.label39);
            this.groupBox13.Controls.Add(this.numericUpDownDelayHOMainRxlev);
            this.groupBox13.Location = new System.Drawing.Point(1, 54);
            this.groupBox13.Name = "groupBox13";
            this.groupBox13.Size = new System.Drawing.Size(907, 50);
            this.groupBox13.TabIndex = 5;
            this.groupBox13.TabStop = false;
            this.groupBox13.Text = "切换不及时";
            // 
            // label36
            // 
            this.label36.AutoSize = true;
            this.label36.Location = new System.Drawing.Point(133, 22);
            this.label36.Name = "label36";
            this.label36.Size = new System.Drawing.Size(124, 14);
            this.label36.TabIndex = 0;
            this.label36.Text = "dBm，最强邻区电平>";
            // 
            // label37
            // 
            this.label37.AutoSize = true;
            this.label37.Location = new System.Drawing.Point(11, 22);
            this.label37.Name = "label37";
            this.label37.Size = new System.Drawing.Size(64, 14);
            this.label37.TabIndex = 0;
            this.label37.Text = "主服电平<";
            // 
            // numericUpDownDelayHOPercent
            // 
            this.numericUpDownDelayHOPercent.Location = new System.Drawing.Point(410, 18);
            this.numericUpDownDelayHOPercent.Name = "numericUpDownDelayHOPercent";
            this.numericUpDownDelayHOPercent.Size = new System.Drawing.Size(50, 22);
            this.numericUpDownDelayHOPercent.TabIndex = 2;
            this.numericUpDownDelayHOPercent.Value = new decimal(new int[] {
            60,
            0,
            0,
            0});
            // 
            // numericUpDownDelayHONBMax
            // 
            this.numericUpDownDelayHONBMax.Location = new System.Drawing.Point(258, 18);
            this.numericUpDownDelayHONBMax.Minimum = new decimal(new int[] {
            141,
            0,
            0,
            -2147483648});
            this.numericUpDownDelayHONBMax.Name = "numericUpDownDelayHONBMax";
            this.numericUpDownDelayHONBMax.Size = new System.Drawing.Size(50, 22);
            this.numericUpDownDelayHONBMax.TabIndex = 2;
            this.numericUpDownDelayHONBMax.Value = new decimal(new int[] {
            85,
            0,
            0,
            -2147483648});
            // 
            // label38
            // 
            this.label38.AutoSize = true;
            this.label38.Location = new System.Drawing.Point(465, 22);
            this.label38.Name = "label38";
            this.label38.Size = new System.Drawing.Size(127, 14);
            this.label38.TabIndex = 0;
            this.label38.Text = "%采样点满足这个条件";
            // 
            // label39
            // 
            this.label39.AutoSize = true;
            this.label39.Location = new System.Drawing.Point(312, 22);
            this.label39.Name = "label39";
            this.label39.Size = new System.Drawing.Size(98, 14);
            this.label39.TabIndex = 0;
            this.label39.Text = "dBm，回看5秒，";
            // 
            // numericUpDownDelayHOMainRxlev
            // 
            this.numericUpDownDelayHOMainRxlev.Location = new System.Drawing.Point(78, 18);
            this.numericUpDownDelayHOMainRxlev.Minimum = new decimal(new int[] {
            141,
            0,
            0,
            -2147483648});
            this.numericUpDownDelayHOMainRxlev.Name = "numericUpDownDelayHOMainRxlev";
            this.numericUpDownDelayHOMainRxlev.Size = new System.Drawing.Size(50, 22);
            this.numericUpDownDelayHOMainRxlev.TabIndex = 2;
            this.numericUpDownDelayHOMainRxlev.Value = new decimal(new int[] {
            90,
            0,
            0,
            -2147483648});
            // 
            // groupBox12
            // 
            this.groupBox12.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBox12.Controls.Add(this.label30);
            this.groupBox12.Controls.Add(this.label32);
            this.groupBox12.Controls.Add(this.numericUpDownFrequentHOCnt);
            this.groupBox12.Location = new System.Drawing.Point(1, 3);
            this.groupBox12.Name = "groupBox12";
            this.groupBox12.Size = new System.Drawing.Size(907, 47);
            this.groupBox12.TabIndex = 5;
            this.groupBox12.TabStop = false;
            this.groupBox12.Text = "频繁切换";
            // 
            // label30
            // 
            this.label30.AutoSize = true;
            this.label30.Location = new System.Drawing.Point(196, 20);
            this.label30.Name = "label30";
            this.label30.Size = new System.Drawing.Size(19, 14);
            this.label30.TabIndex = 0;
            this.label30.Text = "次";
            // 
            // label32
            // 
            this.label32.AutoSize = true;
            this.label32.Location = new System.Drawing.Point(11, 20);
            this.label32.Name = "label32";
            this.label32.Size = new System.Drawing.Size(126, 14);
            this.label32.TabIndex = 0;
            this.label32.Text = "回看10秒，切换次数≥";
            // 
            // numericUpDownFrequentHOCnt
            // 
            this.numericUpDownFrequentHOCnt.Location = new System.Drawing.Point(141, 16);
            this.numericUpDownFrequentHOCnt.Name = "numericUpDownFrequentHOCnt";
            this.numericUpDownFrequentHOCnt.Size = new System.Drawing.Size(50, 22);
            this.numericUpDownFrequentHOCnt.TabIndex = 2;
            this.numericUpDownFrequentHOCnt.Value = new decimal(new int[] {
            3,
            0,
            0,
            0});
            // 
            // groupControl1
            // 
            this.groupControl1.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.groupControl1.Controls.Add(this.simpleButtonCancel);
            this.groupControl1.Controls.Add(this.simpleButtonOK);
            this.groupControl1.Controls.Add(this.groupBox2);
            this.groupControl1.Controls.Add(this.groupBox1);
            this.groupControl1.Controls.Add(this.checkedListBoxControlCauses);
            this.groupControl1.Controls.Add(this.simpleButtonDown);
            this.groupControl1.Controls.Add(this.simpleButtonUp);
            this.groupControl1.Location = new System.Drawing.Point(0, 0);
            this.groupControl1.Name = "groupControl1";
            this.groupControl1.Size = new System.Drawing.Size(922, 298);
            this.groupControl1.TabIndex = 0;
            this.groupControl1.Text = "分析顺序";
            // 
            // simpleButtonCancel
            // 
            this.simpleButtonCancel.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.simpleButtonCancel.Location = new System.Drawing.Point(817, 263);
            this.simpleButtonCancel.Name = "simpleButtonCancel";
            this.simpleButtonCancel.Size = new System.Drawing.Size(75, 23);
            this.simpleButtonCancel.TabIndex = 8;
            this.simpleButtonCancel.Text = "取消";
            this.simpleButtonCancel.Click += new System.EventHandler(this.simpleButtonCancel_Click);
            // 
            // simpleButtonOK
            // 
            this.simpleButtonOK.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.simpleButtonOK.Location = new System.Drawing.Point(718, 263);
            this.simpleButtonOK.Name = "simpleButtonOK";
            this.simpleButtonOK.Size = new System.Drawing.Size(75, 23);
            this.simpleButtonOK.TabIndex = 8;
            this.simpleButtonOK.Text = "确定";
            this.simpleButtonOK.Click += new System.EventHandler(this.simpleButtonOK_Click);
            // 
            // groupBox2
            // 
            this.groupBox2.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBox2.Controls.Add(this.textBoxSuguest);
            this.groupBox2.Location = new System.Drawing.Point(340, 153);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(574, 104);
            this.groupBox2.TabIndex = 7;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "优化建议：";
            // 
            // textBoxSuguest
            // 
            this.textBoxSuguest.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.textBoxSuguest.Location = new System.Drawing.Point(19, 21);
            this.textBoxSuguest.Multiline = true;
            this.textBoxSuguest.Name = "textBoxSuguest";
            this.textBoxSuguest.ReadOnly = true;
            this.textBoxSuguest.Size = new System.Drawing.Size(545, 70);
            this.textBoxSuguest.TabIndex = 0;
            // 
            // groupBox1
            // 
            this.groupBox1.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBox1.Controls.Add(this.textBoxDesc);
            this.groupBox1.Location = new System.Drawing.Point(340, 38);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(574, 104);
            this.groupBox1.TabIndex = 7;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "场景分析说明：";
            // 
            // textBoxDesc
            // 
            this.textBoxDesc.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.textBoxDesc.Location = new System.Drawing.Point(19, 21);
            this.textBoxDesc.Multiline = true;
            this.textBoxDesc.Name = "textBoxDesc";
            this.textBoxDesc.ReadOnly = true;
            this.textBoxDesc.Size = new System.Drawing.Size(545, 70);
            this.textBoxDesc.TabIndex = 0;
            // 
            // CSFBCauseSettingDlg
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(922, 506);
            this.Controls.Add(this.splitContainer1);
            this.Name = "CSFBCauseSettingDlg";
            this.Text = "CSFB未接通原因分析设置";
            ((System.ComponentModel.ISupportInitialize)(this.checkedListBoxControlCauses)).EndInit();
            this.splitContainer1.Panel1.ResumeLayout(false);
            this.splitContainer1.Panel2.ResumeLayout(false);
            this.splitContainer1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).EndInit();
            this.xtraTabControl1.ResumeLayout(false);
            this.xtraTabPage1.ResumeLayout(false);
            this.groupBox4.ResumeLayout(false);
            this.groupBox4.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownOverCovRadius)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownOverCovDisMax)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownOverCovBtsNum)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownOverCovDisMin)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownOverCovRxlev)).EndInit();
            this.groupBox5.ResumeLayout(false);
            this.groupBox5.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownCovInConfAngle)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownCovInConfRxlevMax)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownCovInConfRxlevMin)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownCovInConfDistanceMin)).EndInit();
            this.groupBox6.ResumeLayout(false);
            this.groupBox6.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownCoverLapCovNum)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownCoverLapDiff)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownCoverLapMainRxlev)).EndInit();
            this.groupBox7.ResumeLayout(false);
            this.groupBox7.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownFastFailureRxlevMax)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownFastFailureTimeSpan)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownFastFailureRxlevMin)).EndInit();
            this.groupBox3.ResumeLayout(false);
            this.groupBox3.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownWeakCovNBMax)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownWeakCovMainRxlevMax)).EndInit();
            this.xtraTabPage2.ResumeLayout(false);
            this.groupBox9.ResumeLayout(false);
            this.groupBox9.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownStrongRxlevBadRxqualQualMin)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownStrongRxlevBadRxqualSinrMax)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownStrongRxlevBadRxqualRxlevMin)).EndInit();
            this.groupBox8.ResumeLayout(false);
            this.groupBox8.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownBadRxqualQualMin)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownBadRxqualSinrMax)).EndInit();
            this.xtraTabPage3.ResumeLayout(false);
            this.groupBox11.ResumeLayout(false);
            this.groupBox11.PerformLayout();
            this.groupBox10.ResumeLayout(false);
            this.groupBox10.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownMod3Percent)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownMod3Diff)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownMod3MainRxlev)).EndInit();
            this.xtraTabPage4.ResumeLayout(false);
            this.groupBox14.ResumeLayout(false);
            this.groupBox14.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownUnReasonableDiff)).EndInit();
            this.groupBox13.ResumeLayout(false);
            this.groupBox13.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownDelayHOPercent)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownDelayHONBMax)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownDelayHOMainRxlev)).EndInit();
            this.groupBox12.ResumeLayout(false);
            this.groupBox12.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownFrequentHOCnt)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.CheckedListBoxControl checkedListBoxControlCauses;
        private DevExpress.XtraEditors.SimpleButton simpleButtonDown;
        private DevExpress.XtraEditors.SimpleButton simpleButtonUp;
        private System.Windows.Forms.SplitContainer splitContainer1;
        private DevExpress.XtraEditors.GroupControl groupControl1;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.GroupBox groupBox1;
        private DevExpress.XtraEditors.SimpleButton simpleButtonCancel;
        private DevExpress.XtraEditors.SimpleButton simpleButtonOK;
        private System.Windows.Forms.TextBox textBoxSuguest;
        private System.Windows.Forms.TextBox textBoxDesc;
        private DevExpress.XtraTab.XtraTabControl xtraTabControl1;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage1;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage2;
        private System.Windows.Forms.GroupBox groupBox3;
        private System.Windows.Forms.NumericUpDown numericUpDownWeakCovNBMax;
        private System.Windows.Forms.NumericUpDown numericUpDownWeakCovMainRxlevMax;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.GroupBox groupBox4;
        private System.Windows.Forms.NumericUpDown numericUpDownOverCovRadius;
        private System.Windows.Forms.NumericUpDown numericUpDownOverCovDisMax;
        private System.Windows.Forms.NumericUpDown numericUpDownOverCovBtsNum;
        private System.Windows.Forms.NumericUpDown numericUpDownOverCovDisMin;
        private System.Windows.Forms.NumericUpDown numericUpDownOverCovRxlev;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.GroupBox groupBox5;
        private System.Windows.Forms.NumericUpDown numericUpDownCovInConfAngle;
        private System.Windows.Forms.NumericUpDown numericUpDownCovInConfDistanceMin;
        private System.Windows.Forms.Label label9;
        private System.Windows.Forms.Label label10;
        private System.Windows.Forms.Label label11;
        private System.Windows.Forms.NumericUpDown numericUpDownCovInConfRxlevMin;
        private System.Windows.Forms.Label label12;
        private System.Windows.Forms.GroupBox groupBox6;
        private System.Windows.Forms.NumericUpDown numericUpDownCoverLapDiff;
        private System.Windows.Forms.NumericUpDown numericUpDownCoverLapMainRxlev;
        private System.Windows.Forms.Label label13;
        private System.Windows.Forms.Label label14;
        private System.Windows.Forms.Label label15;
        private System.Windows.Forms.GroupBox groupBox7;
        private System.Windows.Forms.NumericUpDown numericUpDownFastFailureRxlevMax;
        private System.Windows.Forms.NumericUpDown numericUpDownFastFailureTimeSpan;
        private System.Windows.Forms.NumericUpDown numericUpDownFastFailureRxlevMin;
        private System.Windows.Forms.Label label16;
        private System.Windows.Forms.Label label19;
        private System.Windows.Forms.Label label17;
        private System.Windows.Forms.Label label18;
        private System.Windows.Forms.GroupBox groupBox9;
        private System.Windows.Forms.GroupBox groupBox8;
        private System.Windows.Forms.Label label21;
        private System.Windows.Forms.Label label20;
        private System.Windows.Forms.NumericUpDown numericUpDownBadRxqualQualMin;
        private System.Windows.Forms.Label label22;
        private System.Windows.Forms.NumericUpDown numericUpDownBadRxqualSinrMax;
        private System.Windows.Forms.Label label26;
        private System.Windows.Forms.Label label25;
        private System.Windows.Forms.NumericUpDown numericUpDownStrongRxlevBadRxqualQualMin;
        private System.Windows.Forms.NumericUpDown numericUpDownStrongRxlevBadRxqualSinrMax;
        private System.Windows.Forms.Label label24;
        private System.Windows.Forms.NumericUpDown numericUpDownStrongRxlevBadRxqualRxlevMin;
        private System.Windows.Forms.Label label23;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage3;
        private System.Windows.Forms.GroupBox groupBox11;
        private System.Windows.Forms.Label label31;
        private System.Windows.Forms.GroupBox groupBox10;
        private System.Windows.Forms.Label label27;
        private System.Windows.Forms.Label label28;
        private System.Windows.Forms.NumericUpDown numericUpDownMod3Diff;
        private System.Windows.Forms.Label label29;
        private System.Windows.Forms.NumericUpDown numericUpDownMod3MainRxlev;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage4;
        private System.Windows.Forms.NumericUpDown numericUpDownMod3Percent;
        private System.Windows.Forms.Label label33;
        private System.Windows.Forms.GroupBox groupBox14;
        private System.Windows.Forms.Label label40;
        private System.Windows.Forms.Label label41;
        private System.Windows.Forms.NumericUpDown numericUpDownUnReasonableDiff;
        private System.Windows.Forms.GroupBox groupBox13;
        private System.Windows.Forms.Label label36;
        private System.Windows.Forms.Label label37;
        private System.Windows.Forms.NumericUpDown numericUpDownDelayHOPercent;
        private System.Windows.Forms.NumericUpDown numericUpDownDelayHONBMax;
        private System.Windows.Forms.Label label38;
        private System.Windows.Forms.Label label39;
        private System.Windows.Forms.NumericUpDown numericUpDownDelayHOMainRxlev;
        private System.Windows.Forms.GroupBox groupBox12;
        private System.Windows.Forms.Label label30;
        private System.Windows.Forms.Label label32;
        private System.Windows.Forms.NumericUpDown numericUpDownFrequentHOCnt;
        private System.Windows.Forms.NumericUpDown numericUpDownCoverLapCovNum;
        private System.Windows.Forms.Label label34;
        private System.Windows.Forms.NumericUpDown numericUpDownCovInConfRxlevMax;
        private System.Windows.Forms.Label label35;

    }
}