﻿namespace MasterCom.RAMS.Func
{
    partial class InterferenceSelectionPanel
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.btnCancel = new System.Windows.Forms.Button();
            this.btnOK = new System.Windows.Forms.Button();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.cbxAdjFreq = new System.Windows.Forms.CheckBox();
            this.cbxCoFreq = new System.Windows.Forms.CheckBox();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.label2 = new System.Windows.Forms.Label();
            this.numericUpDownDistance = new System.Windows.Forms.NumericUpDown();
            this.label1 = new System.Windows.Forms.Label();
            this.rbtnDistance = new System.Windows.Forms.RadioButton();
            this.rbtnAllMap = new System.Windows.Forms.RadioButton();
            this.groupBox2.SuspendLayout();
            this.groupBox1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownDistance)).BeginInit();
            this.SuspendLayout();
            // 
            // btnCancel
            // 
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnCancel.Location = new System.Drawing.Point(123, 158);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 7;
            this.btnCancel.Text = "取消";
            this.btnCancel.UseVisualStyleBackColor = true;
            this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
            // 
            // btnOK
            // 
            this.btnOK.Location = new System.Drawing.Point(31, 158);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 6;
            this.btnOK.Text = "确定";
            this.btnOK.UseVisualStyleBackColor = true;
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.cbxAdjFreq);
            this.groupBox2.Controls.Add(this.cbxCoFreq);
            this.groupBox2.Location = new System.Drawing.Point(3, 103);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(194, 46);
            this.groupBox2.TabIndex = 5;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "频率类型设置";
            // 
            // cbxAdjFreq
            // 
            this.cbxAdjFreq.AutoSize = true;
            this.cbxAdjFreq.Checked = true;
            this.cbxAdjFreq.CheckState = System.Windows.Forms.CheckState.Checked;
            this.cbxAdjFreq.Location = new System.Drawing.Point(110, 20);
            this.cbxAdjFreq.Name = "cbxAdjFreq";
            this.cbxAdjFreq.Size = new System.Drawing.Size(48, 16);
            this.cbxAdjFreq.TabIndex = 1;
            this.cbxAdjFreq.Text = "邻频";
            this.cbxAdjFreq.UseVisualStyleBackColor = true;
            // 
            // cbxCoFreq
            // 
            this.cbxCoFreq.AutoSize = true;
            this.cbxCoFreq.Checked = true;
            this.cbxCoFreq.CheckState = System.Windows.Forms.CheckState.Checked;
            this.cbxCoFreq.Location = new System.Drawing.Point(30, 20);
            this.cbxCoFreq.Name = "cbxCoFreq";
            this.cbxCoFreq.Size = new System.Drawing.Size(48, 16);
            this.cbxCoFreq.TabIndex = 0;
            this.cbxCoFreq.Text = "同频";
            this.cbxCoFreq.UseVisualStyleBackColor = true;
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.label2);
            this.groupBox1.Controls.Add(this.numericUpDownDistance);
            this.groupBox1.Controls.Add(this.label1);
            this.groupBox1.Controls.Add(this.rbtnDistance);
            this.groupBox1.Controls.Add(this.rbtnAllMap);
            this.groupBox1.Location = new System.Drawing.Point(3, 3);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(194, 94);
            this.groupBox1.TabIndex = 4;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "范围设置";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(148, 65);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(29, 12);
            this.label2.TabIndex = 4;
            this.label2.Text = "公里";
            // 
            // numericUpDownDistance
            // 
            this.numericUpDownDistance.DecimalPlaces = 1;
            this.numericUpDownDistance.Enabled = false;
            this.numericUpDownDistance.Increment = new decimal(new int[] {
            5,
            0,
            0,
            65536});
            this.numericUpDownDistance.Location = new System.Drawing.Point(73, 61);
            this.numericUpDownDistance.Name = "numericUpDownDistance";
            this.numericUpDownDistance.Size = new System.Drawing.Size(69, 21);
            this.numericUpDownDistance.TabIndex = 3;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(24, 66);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(41, 12);
            this.label1.TabIndex = 2;
            this.label1.Text = "距离≤";
            // 
            // rbtnDistance
            // 
            this.rbtnDistance.AutoSize = true;
            this.rbtnDistance.Location = new System.Drawing.Point(6, 40);
            this.rbtnDistance.Name = "rbtnDistance";
            this.rbtnDistance.Size = new System.Drawing.Size(59, 16);
            this.rbtnDistance.TabIndex = 1;
            this.rbtnDistance.Text = "按距离";
            this.rbtnDistance.UseVisualStyleBackColor = true;
            this.rbtnDistance.CheckedChanged += new System.EventHandler(this.rbtnDistance_CheckedChanged);
            // 
            // rbtnAllMap
            // 
            this.rbtnAllMap.AutoSize = true;
            this.rbtnAllMap.Checked = true;
            this.rbtnAllMap.Location = new System.Drawing.Point(6, 18);
            this.rbtnAllMap.Name = "rbtnAllMap";
            this.rbtnAllMap.Size = new System.Drawing.Size(59, 16);
            this.rbtnAllMap.TabIndex = 0;
            this.rbtnAllMap.TabStop = true;
            this.rbtnAllMap.Text = "按全图";
            this.rbtnAllMap.UseVisualStyleBackColor = true;
            // 
            // InterferenceSelectionPanel
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.BackColor = System.Drawing.SystemColors.Window;
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.Controls.Add(this.groupBox2);
            this.Controls.Add(this.groupBox1);
            this.Name = "InterferenceSelectionPanel";
            this.Size = new System.Drawing.Size(200, 186);
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownDistance)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.Button btnOK;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.CheckBox cbxAdjFreq;
        private System.Windows.Forms.CheckBox cbxCoFreq;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.NumericUpDown numericUpDownDistance;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.RadioButton rbtnDistance;
        private System.Windows.Forms.RadioButton rbtnAllMap;
    }
}
