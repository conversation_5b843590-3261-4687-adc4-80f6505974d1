﻿using BrightIdeasSoftware;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class DropCallReasonResultDlg : MinCloseForm
    {
        public DropCallReasonResultDlg()
            : base()
        {
            InitializeComponent();
            mapForm = MainModel.MainForm.GetMapForm();
            init();
            DisposeWhenClose = true;
        }
        private MapForm mapForm = null;
        List<DropCallReasonInfo>resultList=null;
        List<DropCallReasonInfo> resultListMt = null;
        public void FillData(List<DropCallReasonInfo> resultList, List<DropCallReasonInfo> resultListMt)
        {
            this.resultList = resultList;
            this.resultListMt = resultListMt;

            this.listViewMo.RebuildColumns();
            this.listViewMo.ClearObjects();
            this.listViewMo.SetObjects(resultList);

            this.ListViewMt.RebuildColumns();
            this.ListViewMt.ClearObjects();
            this.ListViewMt.SetObjects(resultListMt);

            refreshSummary(chartControlReason.Series[0], resultList);
            refreshSummary(chartControlMtReason.Series[0], resultListMt);

            bindData(resultList, true);
            bindData(resultListMt, false);

            MainModel.RefreshLegend();
            MapForm mf = MainModel.MainForm.GetMapForm();
            if (mf != null)
            {
                mf.GetCellLayer().Invalidate();
            }
        }
        private void init()
        {
            moDataBinding();
            mtDataBinding();
        }

        private void moDataBinding()
        {
            #region Mo界面绑定
            this.olvColumnSN.AspectGetter = delegate (object row)
            {
                if (row is DropCallReasonInfo)
                {
                    DropCallReasonInfo info = row as DropCallReasonInfo;
                    return info.SN;
                }
                return "";
            };
            this.olvColumnMoFileName.AspectGetter = delegate (object row)
            {
                if (row is DropCallReasonInfo)
                {
                    DropCallReasonInfo info = row as DropCallReasonInfo;
                    return info.MoFileName;
                }
                return "";
            };
            this.olvColumnMtFileName.AspectGetter = delegate (object row)
            {
                if (row is DropCallReasonInfo)
                {
                    DropCallReasonInfo info = row as DropCallReasonInfo;
                    if (!string.IsNullOrEmpty(info.MtFileName))
                    {
                        return info.MtFileName;
                    }
                }
                return "";
            };
            this.olvColumnBeginTime.AspectGetter = delegate (object row)
            {
                if (row is DropCallReasonInfo)
                {
                    DropCallReasonInfo info = row as DropCallReasonInfo;
                    return info.Imssipack.DateTime.ToString("HH:mm:ss.fff");
                }
                return "";
            };
            this.olvColumnAttemptTime.AspectGetter = delegate (object row)
            {
                if (row is DropCallReasonInfo)
                {
                    DropCallReasonInfo info = row as DropCallReasonInfo;
                    return info.Callattempt.DateTime.ToString("HH:mm:ss.fff");
                }
                return "";
            };
            this.olvColumnDropTime.AspectGetter = delegate (object row)
            {
                if (row is DropCallReasonInfo)
                {
                    DropCallReasonInfo info = row as DropCallReasonInfo;
                    return info.Dropcall.DateTime.ToString("HH:mm:ss.fff");
                }
                return "";
            };
            this.olvColumnSpanTime.AspectGetter = delegate (object row)
            {
                if (row is DropCallReasonInfo)
                {
                    DropCallReasonInfo info = row as DropCallReasonInfo;
                    return (info.Dropcall.DateTime - info.Callattempt.DateTime).TotalSeconds;
                }
                return "";
            };
            this.olvColumnReason.AspectGetter = delegate (object row)
            {
                if (row is DropCallReasonInfo)
                {
                    DropCallReasonInfo info = row as DropCallReasonInfo;
                    return info.Reason;
                }
                return "";
            };
            this.olvColumnDesc.AspectGetter = delegate (object row)
            {
                if (row is DropCallReasonInfo)
                {
                    DropCallReasonInfo info = row as DropCallReasonInfo;
                    return info.ReasonDesc;
                }
                return "";
            };
            #endregion
        }

        private void mtDataBinding()
        {
            #region Mt界面绑定
            this.olvColumnMtSN.AspectGetter = delegate (object row)
            {
                if (row is DropCallReasonInfo)
                {
                    DropCallReasonInfo info = row as DropCallReasonInfo;
                    return info.SN;
                }
                return "";
            };
            this.olvColumnMtMoFile.AspectGetter = delegate (object row)
            {
                if (row is DropCallReasonInfo)
                {
                    DropCallReasonInfo info = row as DropCallReasonInfo;
                    if (!string.IsNullOrEmpty(info.MoFileName))
                    {
                        return info.MoFileName;
                    }
                }
                return "";
            };
            this.olvColumnMtMtFile.AspectGetter = delegate (object row)
            {
                if (row is DropCallReasonInfo)
                {
                    DropCallReasonInfo info = row as DropCallReasonInfo;
                    return info.MtFileName;
                }
                return "";
            };
            this.olvColumnMtBeginTime.AspectGetter = delegate (object row)
            {
                if (row is DropCallReasonInfo)
                {
                    DropCallReasonInfo info = row as DropCallReasonInfo;
                    return info.Imssipack.DateTime.ToString("HH:mm:ss.fff");
                }
                return "";
            };
            this.olvColumnMtAttemptTime.AspectGetter = delegate (object row)
            {
                if (row is DropCallReasonInfo)
                {
                    DropCallReasonInfo info = row as DropCallReasonInfo;
                    return info.Callattempt.DateTime.ToString("HH:mm:ss.fff");
                }
                return "";
            };
            this.olvColumnMtDropTime.AspectGetter = delegate (object row)
            {
                if (row is DropCallReasonInfo)
                {
                    DropCallReasonInfo info = row as DropCallReasonInfo;
                    return info.Dropcall.DateTime.ToString("HH:mm:ss.fff");
                }
                return "";
            };
            this.olvColumnMtSpanTime.AspectGetter = delegate (object row)
            {
                if (row is DropCallReasonInfo)
                {
                    DropCallReasonInfo info = row as DropCallReasonInfo;
                    return (info.Dropcall.DateTime - info.Callattempt.DateTime).TotalSeconds;
                }
                return "";
            };
            this.olvColumnMtReason.AspectGetter = delegate (object row)
            {
                if (row is DropCallReasonInfo)
                {
                    DropCallReasonInfo info = row as DropCallReasonInfo;
                    return info.Reason;
                }
                return "";
            };
            this.olvColumnMtDesc.AspectGetter = delegate (object row)
            {
                if (row is DropCallReasonInfo)
                {
                    DropCallReasonInfo info = row as DropCallReasonInfo;
                    return info.ReasonDesc;
                }
                return "";
            };
            #endregion
        }

        /// <summary>
        /// 绑定饼图数据
        /// </summary>
        /// <param name="reason"></param>
        /// <param name="result"></param>
        /// <param name="isMO"></param>
        private void refreshSummary(DevExpress.XtraCharts.Series reason, List<DropCallReasonInfo> result)
        {
            Dictionary<string, int> modic = new Dictionary<string, int>();

            reason.SeriesPointsSorting = DevExpress.XtraCharts.SortingMode.Descending;
            reason.SeriesPointsSortingKey = DevExpress.XtraCharts.SeriesPointKey.Value_1;
            reason.Points.Clear();

            foreach (DropCallReasonInfo info in result)
            {
                if (!modic.ContainsKey(info.ReasonDesc))
                {
                    modic.Add(info.ReasonDesc, 1);
                }
                else
                {
                    modic[info.ReasonDesc]++;
                }
            }
            foreach (KeyValuePair<string, int> pair in modic)
            {
                DevExpress.XtraCharts.SeriesPoint sp =
                        new DevExpress.XtraCharts.SeriesPoint(pair.Key, Math.Round((double)pair.Value / result.Count, 2));
                reason.Points.Add(sp);
            }
        }
        private void bindData(List<DropCallReasonInfo> result, bool isMo)
        {
            Dictionary<string, double> dic = new Dictionary<string, double>();
            foreach (DropCallReasonInfo info in result)
            {
                if (!dic.ContainsKey(info.ReasonDesc))
                {
                    dic.Add(info.ReasonDesc, 1);
                }
                else
                {
                    dic[info.ReasonDesc]++;
                }
            }
            DataTable dt = new DataTable();
            dt.Columns.Add("SN", typeof(string));
            dt.Columns.Add("Reason", typeof(string));
            dt.Columns.Add("Num", typeof(string));
            dt.Columns.Add("Per", typeof(string));
            int i = 1;
            foreach (KeyValuePair<string, double> pair in dic)
            {
                DataRow dr = dt.NewRow();
                dr["SN"] = i++;
                dr["Reason"] = pair.Key;
                dr["Num"] = pair.Value.ToString();
                dr["Per"] = Math.Round(pair.Value*100 / result.Count, 2).ToString() + "%";
                dt.Rows.Add(dr);
            }
            DataRow dar = dt.NewRow();
            dar["SN"] = i;
            dar["Reason"] = "汇总";
            dar["Num"] = result.Count;
            dar["Per"] = "100" + "%";
            dt.Rows.Add(dar);
            if (isMo)
            {
                gCMo.DataSource = dt;
            }
            else
            {
                gCMt.DataSource = dt;
            }
        }

        private void ExportItem_Click(object sender, EventArgs e)
        {
            if (listViewMo.Focused)
            {
                MasterCom.Util.ExcelNPOIManager.ExportToExcel(listViewMo);
            }
            else if (ListViewMt.Focused)
            {
                MasterCom.Util.ExcelNPOIManager.ExportToExcel(ListViewMt);
            }
        }

        private void ReplayItem_Click(object sender, EventArgs e)
        {
            if (listViewMo.Focused)
            {
                replay(listViewMo);
            }
            else if (ListViewMt.Focused)
            {
                replay(ListViewMt);
            }
        }
        private void replay(TreeListView listView)
        {
            object rows = listView.GetSelectedObject();
            if (rows == null)
            {
                MessageBox.Show("请选择要回放的数据");
                return;
            }
            DropCallReasonInfo info = listView.GetSelectedObject() as DropCallReasonInfo;
            if (info == null)
            {
                return;
            }
            MainModel.MainForm.NeedChangeWorkSpace(false);
            try
            {
                if (!string.IsNullOrEmpty(info.MoFileName) && !string.IsNullOrEmpty(info.MtFileName))
                {
                    FileReplayer.ReplayOnePartBothSides(info.Imssipack);
                }
                else
                {
                    FileReplayer.ReplayOnePart(info.Imssipack);
                }
            }
            catch
            {
                MainModel.MainForm.CancelChange = true;
            }
            MainModel.MainForm.ChangeWorkSpace();
        }
    }
}
