﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.BackgroundFunc.StationAccept_Shanxi
{
    public class WorkParamsImport : QueryBase
    {
        public WorkParamsImport(MainModel mainModel)
             : base(mainModel)
        {
        }

        public override string Name
        {
            get { return "陕西单验工参管理"; }
        }

        WorkParamsImportCondtion curCondtion;
        List<CellAcceptWorkParam_SX> lteParamList;
        List<CellAcceptWorkParam_SX_NR> nrParamList;

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22001, "陕西单验工参管理");
        }

        protected override bool isValidCondition()
        {
            WorkParamsImportDlg dlg = new WorkParamsImportDlg(curCondtion);
            if (dlg.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                curCondtion = dlg.Condtion;
                return true;
            }
            return false;
        }

        protected override void query()
        {
            lteParamList = new List<CellAcceptWorkParam_SX>();
            nrParamList = new List<CellAcceptWorkParam_SX_NR>();
            WaitBox.CanCancel = true;
            WaitBox.Text = "正在查询单验工参信息...";
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }

                if(curCondtion.Type == NetType.LTE)
                {
                    var query = new DiyQueryStationAcceptParam_SX(curCondtion);
                    query.Query();
                    lteParamList = query.ResList;
                }
                else if (curCondtion.Type == NetType.NR)
                {
                    var query = new DiyQueryStationAcceptParam_SX_NR(curCondtion);
                    query.Query();
                    nrParamList = query.ResList;
                }

                fireShowForm();
            }
            finally
            {
                clientProxy.Close();
            }
        }

        private void fireShowForm()
        {
            if (curCondtion.Type == NetType.LTE)
            {
                var frm = MainModel.GetInstance().CreateResultForm(typeof(WorkParamsImportForm<CellAcceptWorkParam_SX>)) as WorkParamsImportForm<CellAcceptWorkParam_SX>;
                frm.FillData(lteParamList, NetType.LTE);
                frm.Visible = true;
                frm.BringToFront();
            }
            else if (curCondtion.Type == NetType.NR)
            {
                var frm = MainModel.GetInstance().CreateResultForm(typeof(WorkParamsImportForm<CellAcceptWorkParam_SX_NR>)) as WorkParamsImportForm<CellAcceptWorkParam_SX_NR>;
                frm.FillData(nrParamList, NetType.NR);
                frm.Visible = true;
                frm.BringToFront();
            }
        }
    }
}
