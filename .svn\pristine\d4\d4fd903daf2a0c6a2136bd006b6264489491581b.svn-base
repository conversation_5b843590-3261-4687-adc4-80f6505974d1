﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.ZTFunc;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using System.Collections.ObjectModel;
using System.Drawing.Drawing2D;
using System.Drawing;
using MasterCom.MTGis;
using MapWinGIS;
using MasterCom.RAMS.Grid;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.KPI_Statistics;
using MasterCom.RAMS.Stat.Data;
using MasterCom.RAMS.Stat;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTGsmAntParaCommon : ZTAntennaBase
    {
        public ZTGsmAntParaCommon(MainModel mainModel)
            : base(mainModel)
        {
            isAddSampleToDTDataManager = false;
            this.mainModel = mainModel;
        }

        #region 功能相关信息
        public override string Name
        {
            get { return "GSM天线综合分析"; }
        }

        public override BackgroundStatType StatType
        {
            get { return BackgroundStatType.None; }
        }

        public override bool IsNeedSetQueryCondition
        {
            get
            {
                return false;
            }
        }

        protected override bool isValidCondition()
        {
            return true;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 28000, 28003, this.Name);
        }
        #endregion

        #region 公共变量
        ZTDataCfgForm dataCfg { get; set; }
        int iFunc = 1;
        AntTimeCfg timeCfg = new AntTimeCfg();
        string strCityName = "";
        Dictionary<LaiKey, ZTAntGsmTdCellInfo> antCfgRamsParaDic = null;
        Dictionary<LaiKey, ZTGsmAntenna.CellAngleData> antScanInfoDic = null;
        Dictionary<LaiKey, ZTGsmAntenna.CellAngleData> antInfoDic = null;
        Dictionary<LaiKey, GsmAntParaItem> cellAntParaInfoDic = null;
        Dictionary<LaiKey, CellGsmPara> cellGsmParaEciDic = null;
        Dictionary<LaiKey, ZTAntGsmMrCover> antGsmMrCoverDic = null;
        List<List<NPOIRow>> nrDatasList = null;
        List<string> sheetNames = null;
        #endregion

        protected override void query()
        {
            dataCfg = new ZTDataCfgForm(false, "GSM");
            if (dataCfg.ShowDialog() != DialogResult.OK)
                return;
            iFunc = dataCfg.iFunc;
            timeCfg = dataCfg.timeCfgList[0];
            GetDt_ScanProject(timeCfg, "GSM");
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName
                    , MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
                intData();
                WaitBox.CanCancel = true;
                WaitBox.Text = "正在查询...";
                strCityName = DistrictManager.GetInstance().getDistrictName(MainModel.DistrictID);
                WaitBox.Show("1.读取现网天线信息...", doWithAntCfgData);
                WaitBox.Show("2.天线分析数据处理...", AnaCellTestData);
                WaitBox.Show("3.读取小区性能数据...", doWithCellParaData);
                WaitBox.Show("4.读取MR数据...", doWithMrData);
                WaitBox.Show("5.联合数据处理...", AnaCellAngleData);
                if (iFunc != 2)
                    WaitBox.Show("6.获取天线角度赋形数据...", AnaAngleLevelData);
                WaitBox.Show("整理数据结果集...", dealMainUtranCellSample);
                FireShowResultForm();
            }
            catch
            {
                clientProxy.Close();
            }
            finally
            {
                intData();
            }
        }

        private void intData()
        {
            antCfgRamsParaDic = new Dictionary<LaiKey, ZTAntGsmTdCellInfo>();
            antScanInfoDic = new Dictionary<LaiKey, ZTGsmAntenna.CellAngleData>();
            antInfoDic = new Dictionary<LaiKey, ZTGsmAntenna.CellAngleData>();
            cellAntParaInfoDic = new Dictionary<LaiKey, GsmAntParaItem>();
            cellGsmParaEciDic = new Dictionary<LaiKey, CellGsmPara>();
            antGsmMrCoverDic = new Dictionary<LaiKey, ZTAntGsmMrCover>();
            nrDatasList = new List<List<NPOIRow>>();
            sheetNames = new List<string>();
            MainModel.ClearDTData();
        }

        #region 查询综合小区相关信息

        private void doWithAntCfgData()
        {
            WaitBox.ProgressPercent = 20;
            DiyCfgRamsPara cellPara = new DiyCfgRamsPara(MainModel,"GSM");
            cellPara.Query();
            antCfgRamsParaDic = cellPara.antCfgRamsParaDic;
            WaitBox.Close();
        }

        private void AnaCellTestData()
        {
            WaitBox.ProgressPercent = 20;
            ZTGsmAntenna.isScanStat = true;
            if (strProject[0].Length > 0)
            {
                antScanInfoDic = ZTGsmScanAntenna.GetInstance().getBackgroundData(timeCfg.ISitme, timeCfg.IEitme, strProject[0]);
            }

            ZTGsmAntenna.isScanStat = false;
            WaitBox.ProgressPercent = 60;
            if (strProject[1].Length > 0)
            {
                antInfoDic = ZTGsmAntenna.GetInstance().getBackgroundData(timeCfg.ISitme, timeCfg.IEitme, strProject[1]);
            }
            WaitBox.Close();
        }

        private void doWithCellParaData()
        {
            WaitBox.CanCancel = true;
            WaitBox.ProgressPercent = 20;
            DiyGSMCellPara cellPara = new DiyGSMCellPara(MainModel, timeCfg);
            cellPara.Query();
            cellGsmParaEciDic = cellPara.cellGsmParaEciDic;

            WaitBox.Close();
        }
        private void doWithMrData()
        {
            WaitBox.CanCancel = true;
            WaitBox.ProgressPercent = 40;
            DiyGsmMrCover gsmMr = new DiyGsmMrCover(MainModel);
            gsmMr.setCondition(timeCfg.DStime);
            gsmMr.Query();
            antGsmMrCoverDic = gsmMr.antGsmMrCoverDic;
            WaitBox.Close();
        }

        private void AnaCellAngleData()
        {
            WaitBox.CanCancel = true;
            WaitBox.ProgressPercent = 0;
            int iCount = antCfgRamsParaDic.Count;
            int iNum = 0;
            cellAntParaInfoDic.Clear();
            foreach (LaiKey cellKey in antCfgRamsParaDic.Keys)
            {
                if (WaitBox.CancelRequest)
                    break;
                if (iNum % 200 == 0)
                {
                    WaitBox.ProgressPercent = (int)(100 * ((iNum * 1.0) / iCount));
                    WaitBox.Text = "4.联合数据处理(" + iNum++ + "/" + iCount + ")";
                }
                try
                {
                    GsmAntParaItem gsmAntParaItem = getGsmAntParaItem(cellKey);

                    if (!cellAntParaInfoDic.ContainsKey(cellKey))
                    {
                        cellAntParaInfoDic[cellKey] = gsmAntParaItem;
                    }
                }
                catch
                {
                    //continue
                }
            }
            antCfgRamsParaDic.Clear();
            antInfoDic.Clear();
            antScanInfoDic.Clear();
            WaitBox.Close();
        }

        private GsmAntParaItem getGsmAntParaItem(LaiKey cellKey)
        {
            GsmAntParaItem gsmAntParaItem = new GsmAntParaItem();
            gsmAntParaItem.序号 = cellAntParaInfoDic.Keys.Count + 1;

            antCellInfo(ref gsmAntParaItem, antCfgRamsParaDic[cellKey]);
            if (antInfoDic.ContainsKey(cellKey))
            {
                antDtInfo(ref gsmAntParaItem, antInfoDic[cellKey]);
            }
            else
            {
                antDtInfo(ref gsmAntParaItem, new ZTGsmAntenna.CellAngleData());
            }
            if (antScanInfoDic.ContainsKey(cellKey))
            {
                antScanInfo(ref gsmAntParaItem, antScanInfoDic[cellKey]);
            }
            else
            {
                antScanInfo(ref gsmAntParaItem, new ZTGsmAntenna.CellAngleData());
            }

            if (cellGsmParaEciDic.ContainsKey(cellKey))
            {
                gsmAntParaItem.paraCellInfoIem = cellGsmParaEciDic[cellKey];
            }
            else
            {
                gsmAntParaItem.paraCellInfoIem = new CellGsmPara();
            }
            if (antGsmMrCoverDic.ContainsKey(cellKey))
            {
                gsmAntParaItem.antGsmMrData = antGsmMrCoverDic[cellKey];
            }
            else
            {
                gsmAntParaItem.antGsmMrData = new ZTAntGsmMrCover();
            }
            gsmAntParaItem.分析结果 = "";
            gsmAntParaItem.告警状态 = "";
            分析问题类型(ref gsmAntParaItem);
            return gsmAntParaItem;
        }

        #endregion

        #region 综合小区指标信息赋值

        private void antCellInfo(ref GsmAntParaItem gsmAntParaItem,ZTAntGsmTdCellInfo antGsmTdCellInfo)
        {
            gsmAntParaItem.iFunc = iFunc;
            gsmAntParaItem.timeCfg = timeCfg;
            gsmAntParaItem.地市 = strCityName;
            gsmAntParaItem.基站名称 = antGsmTdCellInfo.基站BTS名称;
            gsmAntParaItem.小区名称 = antGsmTdCellInfo.小区名称;
            gsmAntParaItem.小区LAC = antGsmTdCellInfo.小区LAC;
            gsmAntParaItem.小区CI = antGsmTdCellInfo.小区CI;
            gsmAntParaItem.覆盖类型 = antGsmTdCellInfo.基站室分类型;
            gsmAntParaItem.场景类型 = "";
            gsmAntParaItem.小区主频 = antGsmTdCellInfo.小区频点;
            gsmAntParaItem.天线经度 = antGsmTdCellInfo.天线经度;
            gsmAntParaItem.天线纬度 = antGsmTdCellInfo.天线纬度;
            gsmAntParaItem.方位角 = antGsmTdCellInfo.天线方向角;
            gsmAntParaItem.下倾角 = antGsmTdCellInfo.天线下倾角;
            gsmAntParaItem.挂高 = antGsmTdCellInfo.天线挂高;
        }

        private void antDtInfo(ref GsmAntParaItem gsmAntParaItem, ZTGsmAntenna.CellAngleData dtCellAngleData)
        {
            gsmAntParaItem.采样点总数_路测 = dtCellAngleData.bgExt.strSampleCountNum;
            gsmAntParaItem.覆盖率Rxlev90_路测 = dtCellAngleData.bgExt.strRxlev90Rate;
            gsmAntParaItem.覆盖率Rxlev94_路测 = dtCellAngleData.bgExt.strRxlev94Rate;
            gsmAntParaItem.Rxlev均值_路测 = dtCellAngleData.bgExt.strRxlevAvg;
            gsmAntParaItem.RxQual5_7级占比_路测 = dtCellAngleData.bgExt.strRxqual5_7;
            gsmAntParaItem.RxQual平均_路测 = dtCellAngleData.bgExt.strRxqualAvg;
            gsmAntParaItem.C_I均值_路测 = dtCellAngleData.bgExt.strC2IAvg;
            gsmAntParaItem.小区平均通信距离_路测 = dtCellAngleData.bgExt.strSampleDist;
            gsmAntParaItem.范围内采样点比例_60_路测 = dtCellAngleData.bgExt.strMainSampleCountNumRate;
            gsmAntParaItem.范围内平均覆盖距离_60_路测 = dtCellAngleData.bgExt.strMainDistance;
            gsmAntParaItem.范围内覆盖率Rxlev90_60_路测 = dtCellAngleData.bgExt.strMainRxlev90Rate;
            gsmAntParaItem.范围内平均Rxlev_60_路测 = dtCellAngleData.bgExt.strMainRxlevAvg;
            gsmAntParaItem.范围内采样点比例_150_路测 = dtCellAngleData.bgExt.strSideSampleCountNumRate;
            gsmAntParaItem.范围内平均覆盖距离_150_路测 = dtCellAngleData.bgExt.strSideDistance;
            gsmAntParaItem.范围内覆盖率Rxlev90_150_路测 = dtCellAngleData.bgExt.strSideRxlev90Rate;
            gsmAntParaItem.范围内平均Rxlev_150_路测 = dtCellAngleData.bgExt.strSideRxlevAvg;
            gsmAntParaItem.范围内采样点比例_180_路测 = dtCellAngleData.bgExt.strBackSampleCountNumRate;
            gsmAntParaItem.范围内平均覆盖距离_180_路测 = dtCellAngleData.bgExt.strBackDistance;
            gsmAntParaItem.范围内覆盖率Rxlev90_180_路测 = dtCellAngleData.bgExt.strBackRxlev90Rate;
            gsmAntParaItem.范围内平均Rxlev_180_路测 = dtCellAngleData.bgExt.strBackRxlevAvg;
            gsmAntParaItem.前后比_路测 = dtCellAngleData.bgExt.strMainBackDiff;
            gsmAntParaItem.dtCellAngleData = dtCellAngleData;
        }

        private void antScanInfo(ref GsmAntParaItem gsmAntParaItem, ZTGsmAntenna.CellAngleData scanCellAngleData)
        {
            gsmAntParaItem.采样点总数_扫频 = scanCellAngleData.bgExt.strSampleCountNum;
            gsmAntParaItem.覆盖率Rxlev90_扫频 = scanCellAngleData.bgExt.strRxlev90Rate;
            gsmAntParaItem.覆盖率Rxlev94_扫频 = scanCellAngleData.bgExt.strRxlev94Rate;
            gsmAntParaItem.Rxlev均值_扫频 = scanCellAngleData.bgExt.strRxlevAvg;
            gsmAntParaItem.C_I均值_扫频 = scanCellAngleData.bgExt.strC2IAvg;
            gsmAntParaItem.小区平均通信距离_扫频 = scanCellAngleData.bgExt.strSampleDist;
            gsmAntParaItem.范围内采样点比例_60_扫频 = scanCellAngleData.bgExt.strMainSampleCountNumRate;
            gsmAntParaItem.范围内平均覆盖距离_60_扫频 = scanCellAngleData.bgExt.strMainDistance;
            gsmAntParaItem.范围内覆盖率Rxlev90_60_扫频 = scanCellAngleData.bgExt.strMainRxlev90Rate;
            gsmAntParaItem.范围内平均Rxlev_60_扫频 = scanCellAngleData.bgExt.strMainRxlevAvg;
            gsmAntParaItem.范围内采样点比例_150_扫频 = scanCellAngleData.bgExt.strSideSampleCountNumRate;
            gsmAntParaItem.范围内平均覆盖距离_150_扫频 = scanCellAngleData.bgExt.strSideDistance;
            gsmAntParaItem.范围内覆盖率Rxlev90_150_扫频 = scanCellAngleData.bgExt.strSideRxlev90Rate;
            gsmAntParaItem.范围内平均Rxlev_150_扫频 = scanCellAngleData.bgExt.strSideRxlevAvg;
            gsmAntParaItem.范围内采样点比例_180_扫频 = scanCellAngleData.bgExt.strBackSampleCountNumRate;
            gsmAntParaItem.范围内平均覆盖距离_180_扫频 = scanCellAngleData.bgExt.strBackDistance;
            gsmAntParaItem.范围内覆盖率Rxlev90_180_扫频 = scanCellAngleData.bgExt.strBackRxlev90Rate;
            gsmAntParaItem.范围内平均Rxlev_180_扫频 = scanCellAngleData.bgExt.strBackRxlevAvg;
            gsmAntParaItem.前后比_扫频 = scanCellAngleData.bgExt.strMainBackDiff;
            gsmAntParaItem.scanCellAngleData = scanCellAngleData;
        }
        
        private void AnaAngleLevelData()
        {
            //路测 and 扫频 角度信息
            WaitBox.CanCancel = true;
            WaitBox.ProgressPercent = 40;
            WaitBox.Text = "4.获取天线角度赋形数据...";
            Dictionary<LaiKey, ZTGsmAntenna.CellInfoItem> dtCellInfoDic;
            Dictionary<LaiKey, ZTGsmAntenna.CellInfoItem> scanCellInfoDic;
            DIYQueryGSMAntAngle gsmAntQuery = new DIYQueryGSMAntAngle(MainModel);
            gsmAntQuery.SetCondition(timeCfg.ISitme, timeCfg.IEitme);
            gsmAntQuery.Query();
            dtCellInfoDic = gsmAntQuery.dtCellInfoDic;
            scanCellInfoDic = gsmAntQuery.scanCellInfoDic;

            WaitBox.ProgressPercent = 60;
            WaitBox.Text = "4.获取天线角度赋形数据完毕，正在匹配小区进行赋值...";
            foreach (LaiKey cellKey in dtCellInfoDic.Keys)
            {
                if (cellAntParaInfoDic.ContainsKey(cellKey))
                {
                    cellAntParaInfoDic[cellKey].dtCellInfoItem = dtCellInfoDic[cellKey];
                }
            }
            foreach (LaiKey cellKey in scanCellInfoDic.Keys)
            {
                if (cellAntParaInfoDic.ContainsKey(cellKey))
                {
                    cellAntParaInfoDic[cellKey].scanCellInfoItem = scanCellInfoDic[cellKey];
                }
            }
            WaitBox.Close();
        }
        #endregion

        #region 问题判断分析

        private void 分析问题类型(ref GsmAntParaItem gsmAntParaItem)
        {
            gsmAntParaItem.告警状态 = "";
            string strCityLevel = ZTAntFuncHelper.getCityLevel(strCityName);
            if (gsmAntParaItem.覆盖类型.Equals("室分") || gsmAntParaItem.覆盖类型.Equals("室内"))
            {
                室分泄露问题分析(ref gsmAntParaItem, strCityLevel);
            } 
            else
            {
                主瓣弱覆盖问题分析(ref gsmAntParaItem, strCityLevel);
                旁瓣泄露问题分析(ref gsmAntParaItem, strCityLevel);
                背瓣覆盖问题分析(ref gsmAntParaItem, strCityLevel);
                天线工参核查(ref gsmAntParaItem);
            }          
        }

        private void 主瓣弱覆盖问题分析(ref GsmAntParaItem gsmAntParaItem, string strCityLevel)
        {
            int iSampleNumDef = 0;
            if (strCityLevel == "一类")
                iSampleNumDef = 120;
            else if (strCityLevel == "二类")
                iSampleNumDef = 105;
            else
                iSampleNumDef = 95;

            double 采样点比例_60_路测;
            if (gsmAntParaItem.范围内采样点比例_60_路测 != "" && double.TryParse(gsmAntParaItem.范围内采样点比例_60_路测, out 采样点比例_60_路测))
            {
                set主瓣弱覆盖(gsmAntParaItem, iSampleNumDef, 采样点比例_60_路测);
            }
        }

        private static void set主瓣弱覆盖(GsmAntParaItem gsmAntParaItem, int iSampleNumDef, double 采样点比例_60_路测)
        {
            if (double.Parse(gsmAntParaItem.采样点总数_路测) * 采样点比例_60_路测 >= iSampleNumDef
                && double.Parse(gsmAntParaItem.范围内平均Rxlev_60_路测) < -94 && double.Parse(gsmAntParaItem.范围内平均覆盖距离_60_路测) <= 800)
            {
                gsmAntParaItem.分析结果 += "主瓣弱覆盖,";
                if (gsmAntParaItem.告警状态 == "")
                    gsmAntParaItem.告警状态 = "二级告警";
                if (double.Parse(gsmAntParaItem.覆盖率Rxlev90_路测) <= 85 || double.Parse(gsmAntParaItem.范围内覆盖率Rxlev90_60_路测) <= 85)
                {
                    if (!gsmAntParaItem.分析结果.Contains("主瓣弱覆盖"))
                    {
                        gsmAntParaItem.分析结果 += "主瓣弱覆盖,";
                    }
                    gsmAntParaItem.告警状态 = "一级告警";
                }
            }
        }

        private void 旁瓣泄露问题分析(ref GsmAntParaItem gsmAntParaItem, string strCityLevel)
        {
            int iSampleNumDef = 0;
            if (strCityLevel == "一类")
                iSampleNumDef = 300;
            else if (strCityLevel == "二类")
                iSampleNumDef = 270;
            else
                iSampleNumDef = 240;

            double 采样点比例_150_路测;
            if (gsmAntParaItem.范围内采样点比例_150_路测 != "" && double.TryParse(gsmAntParaItem.范围内采样点比例_150_路测, out 采样点比例_150_路测))
            {
                set旁瓣泄露路测(gsmAntParaItem, iSampleNumDef, 采样点比例_150_路测);
            }

            double 采样点比例_150_扫频;
            if (gsmAntParaItem.范围内采样点比例_150_扫频 != "" && double.TryParse(gsmAntParaItem.范围内采样点比例_150_扫频, out 采样点比例_150_扫频))
            {
                set旁瓣泄露扫频(gsmAntParaItem, iSampleNumDef, 采样点比例_150_扫频);
            }
        }

        private static void set旁瓣泄露路测(GsmAntParaItem gsmAntParaItem, int iSampleNumDef, double 采样点比例_150_路测)
        {
            if (double.Parse(gsmAntParaItem.采样点总数_路测) >= iSampleNumDef && 采样点比例_150_路测 < 60 && 采样点比例_150_路测 > 40
                && double.Parse(gsmAntParaItem.范围内平均覆盖距离_150_路测) >= 150)
            {
                gsmAntParaItem.分析结果 += "旁瓣泄露,";
                if (gsmAntParaItem.告警状态 == "")
                    gsmAntParaItem.告警状态 = "二级告警";
                if (double.Parse(gsmAntParaItem.RxQual5_7级占比_路测) >= 5 && double.Parse(gsmAntParaItem.范围内覆盖率Rxlev90_150_路测) >= 85)
                {
                    if (!gsmAntParaItem.分析结果.Contains("旁瓣泄露"))
                    {
                        gsmAntParaItem.分析结果 += "旁瓣泄露,";
                    }
                    gsmAntParaItem.告警状态 = "一级告警";
                }
            }
        }

        private void set旁瓣泄露扫频(GsmAntParaItem gsmAntParaItem, int iSampleNumDef, double 采样点比例_150_扫频)
        {
            if (double.Parse(gsmAntParaItem.采样点总数_扫频) >= iSampleNumDef && 采样点比例_150_扫频 < 60 && 采样点比例_150_扫频 > 40
               && double.Parse(gsmAntParaItem.范围内平均覆盖距离_150_扫频) >= 150)
            {
                if (!gsmAntParaItem.分析结果.Contains("旁瓣泄露"))
                {
                    gsmAntParaItem.分析结果 += "旁瓣泄露,";
                }
                if (gsmAntParaItem.告警状态 == "")
                    gsmAntParaItem.告警状态 = "二级告警";
                if (double.Parse(gsmAntParaItem.范围内覆盖率Rxlev90_150_扫频) >= 85)
                {
                    if (!gsmAntParaItem.分析结果.Contains("旁瓣泄露"))
                    {
                        gsmAntParaItem.分析结果 += "旁瓣泄露,";
                    }
                    gsmAntParaItem.告警状态 = "一级告警";
                }
            }
        }

        private void 背瓣覆盖问题分析(ref GsmAntParaItem gsmAntParaItem, string strCityLevel)
        {
            int iSampleNumDef = 0;
            if (strCityLevel == "一类")
                iSampleNumDef = 300;
            else if (strCityLevel == "二类")
                iSampleNumDef = 270;
            else
                iSampleNumDef = 240;

             double 采样点比例_180_路测;
             if (gsmAntParaItem.范围内采样点比例_180_路测 != "" && double.TryParse(gsmAntParaItem.范围内采样点比例_180_路测, out 采样点比例_180_路测))
            {
                setset背瓣覆盖问题路测(gsmAntParaItem, iSampleNumDef, 采样点比例_180_路测);
            }

            double 采样点比例_180_扫频;
             if (gsmAntParaItem.范围内采样点比例_180_扫频 != "" && double.TryParse(gsmAntParaItem.范围内采样点比例_180_扫频, out 采样点比例_180_扫频))
            {
                set背瓣覆盖问题扫频(gsmAntParaItem, iSampleNumDef, 采样点比例_180_扫频);
            }
        }

        private void setset背瓣覆盖问题路测(GsmAntParaItem gsmAntParaItem, int iSampleNumDef, double 采样点比例_180_路测)
        {
            if (double.Parse(gsmAntParaItem.采样点总数_路测) >= iSampleNumDef && 采样点比例_180_路测 >= 20
              && double.Parse(gsmAntParaItem.范围内平均覆盖距离_180_路测) >= 50 && gsmAntParaItem.前后比_路测 != ""
              && gsmAntParaItem.前后比_路测 != "-" && double.Parse(gsmAntParaItem.前后比_路测) <= 5)
            {
                gsmAntParaItem.分析结果 += "背瓣覆盖异常,";
                if (gsmAntParaItem.告警状态 == "")
                    gsmAntParaItem.告警状态 = "二级告警";
                if (double.Parse(gsmAntParaItem.采样点总数_路测) * 采样点比例_180_路测 >= 12 && (double.Parse(gsmAntParaItem.RxQual5_7级占比_路测) >= 5
                  || double.Parse(gsmAntParaItem.范围内覆盖率Rxlev90_180_路测) >= 85))
                {
                    if (!gsmAntParaItem.分析结果.Contains("背瓣覆盖异常"))
                    {
                        gsmAntParaItem.分析结果 += "背瓣覆盖异常,";
                    }
                    gsmAntParaItem.告警状态 = "一级告警";
                }
            }
        }

        private void set背瓣覆盖问题扫频(GsmAntParaItem gsmAntParaItem, int iSampleNumDef, double 采样点比例_180_扫频)
        {
            if (double.Parse(gsmAntParaItem.采样点总数_扫频) >= iSampleNumDef && 采样点比例_180_扫频 >= 20
               && double.Parse(gsmAntParaItem.范围内平均覆盖距离_180_扫频) >= 100 && gsmAntParaItem.前后比_扫频 != ""
               && gsmAntParaItem.前后比_扫频 != "-" && double.Parse(gsmAntParaItem.前后比_扫频) <= 5)
            {
                if (!gsmAntParaItem.分析结果.Contains("背瓣覆盖异常"))
                {
                    gsmAntParaItem.分析结果 += "背瓣覆盖异常,";
                }
                if (gsmAntParaItem.告警状态 == "")
                    gsmAntParaItem.告警状态 = "二级告警";
                if (double.Parse(gsmAntParaItem.采样点总数_扫频) * 采样点比例_180_扫频 >= 12
                    && double.Parse(gsmAntParaItem.范围内覆盖率Rxlev90_180_扫频) >= 85)
                {
                    if (!gsmAntParaItem.分析结果.Contains("背瓣覆盖异常"))
                    {
                        gsmAntParaItem.分析结果 += "背瓣覆盖异常,";
                    }
                    gsmAntParaItem.告警状态 = "一级告警";
                }
            }
        }

        private void 室分泄露问题分析(ref GsmAntParaItem gsmAntParaItem, string strCityLevel)
        {
            int iSampleNumDef = 0;
            if (strCityLevel == "一类")
                iSampleNumDef = 50;
            else
                iSampleNumDef = 30;

            double dSampleCount;
            if (double.TryParse(gsmAntParaItem.采样点总数_路测, out dSampleCount))
            {
                set室分泄露(gsmAntParaItem, iSampleNumDef, dSampleCount);
            }
        }

        private void set室分泄露(GsmAntParaItem gsmAntParaItem, int iSampleNumDef, double dSampleCount)
        {
            if (dSampleCount >= iSampleNumDef && dSampleCount <= 1000)
            {
                gsmAntParaItem.分析结果 += "室分信号泄露,";
                if (gsmAntParaItem.告警状态 == "")
                    gsmAntParaItem.告警状态 = "二级告警";
                if (double.Parse(gsmAntParaItem.覆盖率Rxlev90_路测) <= 85)
                {
                    if (!gsmAntParaItem.分析结果.Contains("室分信号泄露"))
                    {
                        gsmAntParaItem.分析结果 += "室分信号泄露,";
                    }
                    gsmAntParaItem.告警状态 = "一级告警";
                }
            }
        }

        private void 天线工参核查(ref GsmAntParaItem gsmAntParaItem)
        {
            double dSampleCount;
            if (double.TryParse(gsmAntParaItem.采样点总数_路测, out dSampleCount))
            {
                double 采样点比例_60_路测 = 0;
                if (dSampleCount > 300 && 采样点比例_60_路测 < 40)
                {
                    gsmAntParaItem.分析结果 += "天线工参核查,";
                    gsmAntParaItem.告警状态 = "一级告警";
                }
            }
        }
        #endregion

        #region 整理导出数据

        private List<NPOIRow> fillAntAgleData(GsmAntParaItem gsmAntParaItem,string strDt_Scan)
        {
            List<NPOIRow> dataAntAngle = new List<NPOIRow>();
            NPOIRow nr360;

            List<object> objsL360 = new List<object>();
            objsL360.Add(gsmAntParaItem.地市);
            objsL360.Add(gsmAntParaItem.小区名称);
            objsL360.Add("Rxlev" + strDt_Scan);
            ZTGsmAntenna.CellInfoItem cellInfoItem = null;
            cellInfoItem = strDt_Scan == "_Scan" ? gsmAntParaItem.scanCellInfoItem : gsmAntParaItem.dtCellInfoItem;
            if (cellInfoItem == null)
            {
                cellInfoItem = new ZTGsmAntenna.CellInfoItem();
            }
            for (int i = 0; i < 360; i++)  
            {
                objsL360.Add(getValidData(cellInfoItem.samplNumArray[i], cellInfoItem.relArray[i] / cellInfoItem.samplNumArray[i]));
            }
            nr360 = new NPOIRow();
            nr360.cellValues = objsL360;
            dataAntAngle.Add(nr360);

            if (!ZTGsmAntenna.isScanStat)
            {
                objsL360 = new List<object>();
                objsL360.Add(gsmAntParaItem.地市);
                objsL360.Add(gsmAntParaItem.小区名称);
                objsL360.Add("Rxqual(DL)_Dt");
                for (int i = 0; i < 360; i++)
                {
                    objsL360.Add(getValidData(cellInfoItem.samplNumArray[i], cellInfoItem.rxqDlArray[i] / cellInfoItem.samplNumArray[i]));
                }
                nr360 = new NPOIRow();
                nr360.cellValues = objsL360;
                dataAntAngle.Add(nr360);

                objsL360 = new List<object>();
                objsL360.Add(gsmAntParaItem.地市);
                objsL360.Add(gsmAntParaItem.小区名称);
                objsL360.Add("过覆盖指数_Dt");
                for (int i = 0; i < 360; i++)
                {
                    objsL360.Add(getValidData(cellInfoItem.samplNumArray[i], Math.Round(cellInfoItem.coverArray[i] * 1.0 / cellInfoItem.samplNumArray[i], 2)));
                }
                nr360 = new NPOIRow();
                nr360.cellValues = objsL360;
                dataAntAngle.Add(nr360);
            }

            objsL360 = new List<object>();
            objsL360.Add(gsmAntParaItem.地市);
            objsL360.Add(gsmAntParaItem.小区名称);
            objsL360.Add("通信距离" + strDt_Scan);
            for (int i = 0; i < 360; i++)
            {
                objsL360.Add(getValidData(cellInfoItem.samplNumArray[i], Math.Round((float)(cellInfoItem.samplArray[i] / cellInfoItem.samplNumArray[i]), 2)));
            }
            nr360 = new NPOIRow();
            nr360.cellValues = objsL360;
            dataAntAngle.Add(nr360);

            objsL360 = new List<object>();
            objsL360.Add(gsmAntParaItem.地市);
            objsL360.Add(gsmAntParaItem.小区名称);
            objsL360.Add("C/I" + strDt_Scan);
            for (int i = 0; i < 360; i++)
            {
                objsL360.Add(getValidData(cellInfoItem.samplNumArray[i], cellInfoItem.c2iArray[i] / cellInfoItem.samplNumArray[i]));
            }
            nr360 = new NPOIRow();
            nr360.cellValues = objsL360;
            dataAntAngle.Add(nr360);

            return dataAntAngle;
        }

        private object getValidData(int num, object res)
        {
            if (num == 0)
            {
                return 0;
            }
            else
            {
                return res;
            }
        }

        private List<object> fillCellData(GsmAntParaItem gsmAntParaItem)
        {
            List<object> objsCell = new List<object>();
            objsCell.Add(gsmAntParaItem.序号);
            objsCell.Add(gsmAntParaItem.地市);
            objsCell.Add(gsmAntParaItem.基站名称);
            objsCell.Add(gsmAntParaItem.小区名称);
            objsCell.Add(gsmAntParaItem.小区LAC);
            objsCell.Add(gsmAntParaItem.小区CI);
            objsCell.Add(gsmAntParaItem.覆盖类型);
            objsCell.Add(gsmAntParaItem.小区主频);
            objsCell.Add(gsmAntParaItem.天线经度);
            objsCell.Add(gsmAntParaItem.天线纬度);
            objsCell.Add(gsmAntParaItem.方位角);
            objsCell.Add(gsmAntParaItem.下倾角);
            objsCell.Add(gsmAntParaItem.挂高);
            objsCell.Add(gsmAntParaItem.分析结果);
            objsCell.Add(gsmAntParaItem.告警状态);
            objsCell.Add(gsmAntParaItem.采样点总数_路测);
            objsCell.Add(gsmAntParaItem.覆盖率Rxlev90_路测);
            objsCell.Add(gsmAntParaItem.覆盖率Rxlev94_路测);
            objsCell.Add(gsmAntParaItem.Rxlev均值_路测);
            objsCell.Add(gsmAntParaItem.RxQual5_7级占比_路测);
            objsCell.Add(gsmAntParaItem.RxQual平均_路测);
            objsCell.Add(gsmAntParaItem.C_I均值_路测);
            objsCell.Add(gsmAntParaItem.小区平均通信距离_路测);
            objsCell.Add(gsmAntParaItem.范围内采样点比例_60_路测);
            objsCell.Add(gsmAntParaItem.范围内平均覆盖距离_60_路测);
            objsCell.Add(gsmAntParaItem.范围内覆盖率Rxlev90_60_路测);
            objsCell.Add(gsmAntParaItem.范围内平均Rxlev_60_路测);
            objsCell.Add(gsmAntParaItem.范围内采样点比例_150_路测);
            objsCell.Add(gsmAntParaItem.范围内平均覆盖距离_150_路测);
            objsCell.Add(gsmAntParaItem.范围内覆盖率Rxlev90_150_路测);
            objsCell.Add(gsmAntParaItem.范围内平均Rxlev_150_路测);
            objsCell.Add(gsmAntParaItem.范围内采样点比例_180_路测);
            objsCell.Add(gsmAntParaItem.范围内平均覆盖距离_180_路测);
            objsCell.Add(gsmAntParaItem.范围内覆盖率Rxlev90_180_路测);
            objsCell.Add(gsmAntParaItem.范围内平均Rxlev_180_路测);
            objsCell.Add(gsmAntParaItem.前后比_路测);
            objsCell.Add(gsmAntParaItem.采样点总数_扫频);
            objsCell.Add(gsmAntParaItem.覆盖率Rxlev90_扫频);
            objsCell.Add(gsmAntParaItem.覆盖率Rxlev94_扫频);
            objsCell.Add(gsmAntParaItem.Rxlev均值_扫频);
            objsCell.Add(gsmAntParaItem.C_I均值_扫频);
            objsCell.Add(gsmAntParaItem.小区平均通信距离_扫频);
            objsCell.Add(gsmAntParaItem.范围内采样点比例_60_扫频);
            objsCell.Add(gsmAntParaItem.范围内平均覆盖距离_60_扫频);
            objsCell.Add(gsmAntParaItem.范围内覆盖率Rxlev90_60_扫频);
            objsCell.Add(gsmAntParaItem.范围内平均Rxlev_60_扫频);
            objsCell.Add(gsmAntParaItem.范围内采样点比例_150_扫频);
            objsCell.Add(gsmAntParaItem.范围内平均覆盖距离_150_扫频);
            objsCell.Add(gsmAntParaItem.范围内覆盖率Rxlev90_150_扫频);
            objsCell.Add(gsmAntParaItem.范围内平均Rxlev_150_扫频);
            objsCell.Add(gsmAntParaItem.范围内采样点比例_180_扫频);
            objsCell.Add(gsmAntParaItem.范围内平均覆盖距离_180_扫频);
            objsCell.Add(gsmAntParaItem.范围内覆盖率Rxlev90_180_扫频);
            objsCell.Add(gsmAntParaItem.范围内平均Rxlev_180_扫频);
            objsCell.Add(gsmAntParaItem.前后比_扫频);
            objsCell.Add(gsmAntParaItem.paraCellInfoIem.STR_无线接通率);
            objsCell.Add(gsmAntParaItem.paraCellInfoIem.STR_寻呼成功率);
            objsCell.Add(gsmAntParaItem.paraCellInfoIem.STR_TCH拥塞率不含切);
            objsCell.Add(gsmAntParaItem.paraCellInfoIem.STR_SDCCH拥塞率);
            objsCell.Add(gsmAntParaItem.paraCellInfoIem.STR_上行TBF建立成功率);
            objsCell.Add(gsmAntParaItem.paraCellInfoIem.STR_下行TBF建立成功率);
            objsCell.Add(gsmAntParaItem.paraCellInfoIem.STR_TCH掉话率含切换);
            objsCell.Add(gsmAntParaItem.paraCellInfoIem.STR_切换成功率);
            objsCell.Add(gsmAntParaItem.paraCellInfoIem.STR_下行TBF掉线率);
            objsCell.Add(gsmAntParaItem.paraCellInfoIem.STR_上行高干扰信道比例);

            objsCell.Add(gsmAntParaItem.antGsmMrData.rate上行100覆盖率);
            objsCell.Add(gsmAntParaItem.antGsmMrData.rate上行95覆盖率);
            objsCell.Add(gsmAntParaItem.antGsmMrData.rate上行90覆盖率);
            objsCell.Add(gsmAntParaItem.antGsmMrData.rate上行85覆盖率);
            objsCell.Add(gsmAntParaItem.antGsmMrData.rate下行100覆盖率);
            objsCell.Add(gsmAntParaItem.antGsmMrData.rate下行95覆盖率);
            objsCell.Add(gsmAntParaItem.antGsmMrData.rate下行90覆盖率);
            objsCell.Add(gsmAntParaItem.antGsmMrData.rate下行85覆盖率);
            objsCell.Add(gsmAntParaItem.antGsmMrData.rate上行567质量占比);
            objsCell.Add(gsmAntParaItem.antGsmMrData.rate下行567质量占比);
            return objsCell;
        }

        private List<object> 天线综合分析总表字段配置()
        {
            List<object> objsCell = new List<object>();
            objsCell.Add("序号");
            objsCell.Add("地市");
            objsCell.Add("基站名称");
            objsCell.Add("小区名称");
            objsCell.Add("LAC");
            objsCell.Add("CI");
            objsCell.Add("覆盖类型");
            objsCell.Add("小区主频");
            objsCell.Add("天线经度");
            objsCell.Add("天线纬度");
            objsCell.Add("方位角");
            objsCell.Add("下倾角");
            objsCell.Add("挂高");
            objsCell.Add("分析结果");
            objsCell.Add("告警状态");
            objsCell.Add("采样点总数(路测)");
            objsCell.Add("覆盖率(Rxlev>=-90dBm)(%)(路测)");
            objsCell.Add("覆盖率(Rxlev>=-94dBm)(%)(路测)");
            objsCell.Add("Rxlev均值(路测)");
            objsCell.Add("RxQual5-7级占比(%)(路测)");
            objsCell.Add("RxQual平均(路测)");
            objsCell.Add("C/I均值(路测)");
            objsCell.Add("小区平均通信距离(路测)");
            objsCell.Add("±(0，60°)范围内采样点比例(路测)");
            objsCell.Add("±(0，60°)范围内平均覆盖距离(路测)");
            objsCell.Add("±(0，60°)范围内覆盖率(Rxlev>=-90dBm)(%)(路测)");
            objsCell.Add("±(0，60°)范围内平均Rxlev(路测)");
            objsCell.Add("±(60，150°)范围内采样点比例(路测)");
            objsCell.Add("±(60，150°)范围内平均覆盖距离(路测)");
            objsCell.Add("±(60，150°)范围内覆盖率(Rxlev>=-90dBm)(%)(路测)");
            objsCell.Add("±(60，150°)范围内平均Rxlev(路测)");
            objsCell.Add("±(150，180°)范围内采样点比例(路测)");
            objsCell.Add("±(150，180°)范围内平均覆盖距离(路测)");
            objsCell.Add("±(150，180°)范围内覆盖率(Rxlev>=-90dBm)(%)(路测)");
            objsCell.Add("±(150，180°)范围内平均Rxlev(路测)");
            objsCell.Add("前后比(路测)");
            objsCell.Add("采样点总数(扫频)");
            objsCell.Add("覆盖率(Rxlev>=-90dBm)(%)(扫频)");
            objsCell.Add("覆盖率(Rxlev>=-94dBm)(%)(扫频)");
            objsCell.Add("Rxlev均值(扫频)");
            objsCell.Add("C/I均值(扫频)");
            objsCell.Add("小区平均通信距离(扫频)");
            objsCell.Add("±(0，60°)范围内采样点比例(扫频)");
            objsCell.Add("±(0，60°)范围内平均覆盖距离(扫频)");
            objsCell.Add("±(0，60°)范围内覆盖率(Rxlev>=-90dBm)(%)(扫频)");
            objsCell.Add("±(0，60°)范围内平均Rxlev(扫频)");
            objsCell.Add("±(60，150°)范围内采样点比例(扫频)");
            objsCell.Add("±(60，150°)范围内平均覆盖距离(扫频)");
            objsCell.Add("±(60，150°)范围内覆盖率(Rxlev>=-90dBm)(%)(扫频)");
            objsCell.Add("±(60，150°)范围内平均Rxlev(扫频)");
            objsCell.Add("±(150，180°)范围内采样点比例(扫频)");
            objsCell.Add("±(150，180°)范围内平均覆盖距离(扫频)");
            objsCell.Add("±(150，180°)范围内覆盖率(Rxlev>=-90dBm)(%)(扫频)");
            objsCell.Add("±(150，180°)范围内平均Rxlev(扫频)");
            objsCell.Add("前后比(扫频)");
            objsCell.Add("无线接通率");
            objsCell.Add("寻呼成功率");
            objsCell.Add("TCH拥塞率不含切");
            objsCell.Add("SDCCH拥塞率");
            objsCell.Add("上行TBF建立成功率");
            objsCell.Add("下行TBF建立成功率");
            objsCell.Add("TCH掉话率含切换");
            objsCell.Add("切换成功率");
            objsCell.Add("下行TBF掉线率");
            objsCell.Add("上行高干扰信道比例");

            objsCell.Add("上行>=-100dBm覆盖率");
            objsCell.Add("上行>=-95dBm覆盖率");
            objsCell.Add("上行>=-90dBm覆盖率");
            objsCell.Add("上行>=-85dBm覆盖率");
            objsCell.Add("下行>=-100dBm覆盖率");
            objsCell.Add("下行>=-95dBm覆盖率");
            objsCell.Add("下行>=-90dBm覆盖率");
            objsCell.Add("下行>=-85dBm覆盖率");
            objsCell.Add("上行>=5质量占比");
            objsCell.Add("下行>=5质量占比");
            return objsCell;
        }

        private void dealMainUtranCellSample()
        {
            WaitBox.CanCancel = true;
            WaitBox.ProgressPercent = 10;
            List<NPOIRow> dataCell = new List<NPOIRow>();
            NPOIRow nrCell = new NPOIRow();
            List<object> colCell = 天线综合分析总表字段配置();
            nrCell.cellValues = colCell;
            dataCell.Add(nrCell);

            List<NPOIRow> dataAngle = new List<NPOIRow>();
            NPOIRow nrAngle = new NPOIRow();
            List<object> colAngle = new List<object>();
            colAngle.Add("小区名称");
            colAngle.Add("指标项");
            for (int i = 0; i < 360; i++)
            {
                colAngle.Add(i.ToString() + "°");
            }
            nrAngle.cellValues = colAngle;
            dataAngle.Add(nrAngle);

            int index = 0;
            int iCount = cellAntParaInfoDic.Keys.Count / 100 + 1;
            foreach (LaiKey cellKey in cellAntParaInfoDic.Keys)
            {
                try
                {
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                    WaitBox.ProgressPercent = index++ / iCount;
                    NPOIRow nrCellData = new NPOIRow();
                    nrCellData.cellValues = fillCellData(cellAntParaInfoDic[cellKey]);
                    dataCell.Add(nrCellData);
                    //天线360°角度信息
                    dataAngle.AddRange(fillAntAgleData(cellAntParaInfoDic[cellKey],"_Scan"));
                    dataAngle.AddRange(fillAntAgleData(cellAntParaInfoDic[cellKey], "_Dt"));
                }
                catch
                {
                    //continue
                }
            }
            nrDatasList.Add(dataCell);
            nrDatasList.Add(dataAngle);
            sheetNames.Add("天线综合分析总表");
            sheetNames.Add("小区角度数据");
            WaitBox.Close();
        }

        #endregion

        private void FireShowResultForm()
        {
            object obj = MainModel.GetInstance().GetObjectFromBlackboard(typeof(GsmAntParaCommonForm).FullName);
            GsmAntParaCommonForm form = obj == null ? null : obj as GsmAntParaCommonForm;
            if (form == null || form.IsDisposed)
            {
                form = new GsmAntParaCommonForm(MainModel);
            }
            form.nrDatasList = nrDatasList;
            form.sheetNames = sheetNames;

            form.FillData(cellAntParaInfoDic);
            if (!form.Visible)
            {
                form.Show(MainModel.MainForm);
            }
        }
    }
}
