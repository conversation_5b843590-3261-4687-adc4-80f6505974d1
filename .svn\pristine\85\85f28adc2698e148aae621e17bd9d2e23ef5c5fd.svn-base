﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc.JiLinFocusSet
{
    public partial class PeriodPicker : BaseDialog
    {
        public PeriodPicker()
        {
            InitializeComponent();
        }

        public DateTime DateFrom
        {
            get { return dtFrom.Value.Date; }
            set { dtFrom.Value = value; }
        }

        public DateTime DateTo
        {
            get { return dtTo.Value.Date.AddDays(1).AddMilliseconds(-1); }
            set { dtTo.Value = value; }
        }

        public string OrderIDLower
        {
            get { return txtOrderID.Text.Trim().ToLower(); }
            set { txtOrderID.Text = value; }
        }

    }
}
