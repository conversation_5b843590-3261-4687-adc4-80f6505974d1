﻿using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.Model
{
    public class QueryDBTableInfo
    {
        private readonly string dataBase;
        private readonly string dbConnStr;
        public QueryDBTableInfo(string dbConnStr, string dataBase)
        {
            this.dbConnStr = dbConnStr;
            this.dataBase = dataBase;
        }

        public List<string> Query()
        {
            string queryString = " SELECT TABLE_NAME FROM " + dataBase + ".INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA='dbo' and TABLE_TYPE = 'BASE TABLE'";
            List<string> tableNames = new List<string>();
            using (SqlConnection connection = new SqlConnection(dbConnStr))
            {
                try
                {
                    SqlCommand command = new SqlCommand(queryString, connection);
                    connection.Open();
                    SqlDataReader reader = command.ExecuteReader();
                    while (reader.Read())
                    {
                        tableNames.Add(reader["TABLE_NAME"].ToString());
                    }
                    reader.Close();
                }
                catch (Exception ex)
                {
                    MessageBox.Show(ex.ToString());
                }
            }
            return tableNames;
        }

    }
}
