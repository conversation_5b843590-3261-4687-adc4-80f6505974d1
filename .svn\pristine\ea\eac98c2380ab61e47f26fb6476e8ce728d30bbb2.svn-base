﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTScanLTEBestRxlevListForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(ZTScanLTEBestRxlevListForm));
            this.ctxMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem1 = new System.Windows.Forms.ToolStripSeparator();
            this.miExpandAll = new System.Windows.Forms.ToolStripMenuItem();
            this.miCallapsAll = new System.Windows.Forms.ToolStripMenuItem();
            this.ListViewFreqRxlev = new BrightIdeasSoftware.TreeListView();
            this.olvColumnFreq = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnSample = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnRate = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnMaxRxlev = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnMinRxlev = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnAvgRxlev = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnAboveBenchRxlev = new BrightIdeasSoftware.OLVColumn();
            this.ctxMenu.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.ListViewFreqRxlev)).BeginInit();
            this.SuspendLayout();
            // 
            // ctxMenu
            // 
            this.ctxMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExportExcel,
            this.toolStripMenuItem1,
            this.miExpandAll,
            this.miCallapsAll});
            this.ctxMenu.Name = "ctxMenu";
            this.ctxMenu.Size = new System.Drawing.Size(130, 76);
            // 
            // miExportExcel
            // 
            this.miExportExcel.Name = "miExportExcel";
            this.miExportExcel.Size = new System.Drawing.Size(129, 22);
            this.miExportExcel.Text = "导出Excel";
            this.miExportExcel.Click += new System.EventHandler(this.miExportExcel_Click);
            // 
            // toolStripMenuItem1
            // 
            this.toolStripMenuItem1.Name = "toolStripMenuItem1";
            this.toolStripMenuItem1.Size = new System.Drawing.Size(126, 6);
            this.toolStripMenuItem1.Visible = false;
            // 
            // miExpandAll
            // 
            this.miExpandAll.Name = "miExpandAll";
            this.miExpandAll.Size = new System.Drawing.Size(129, 22);
            this.miExpandAll.Text = "全部展开";
            this.miExpandAll.Click += new System.EventHandler(this.miExpandAll_Click);
            // 
            // miCallapsAll
            // 
            this.miCallapsAll.Name = "miCallapsAll";
            this.miCallapsAll.Size = new System.Drawing.Size(129, 22);
            this.miCallapsAll.Text = "全部合并";
            this.miCallapsAll.Click += new System.EventHandler(this.miCallapsAll_Click);
            // 
            // ListViewFreqRxlev
            // 
            this.ListViewFreqRxlev.AllColumns.Add(this.olvColumnFreq);
            this.ListViewFreqRxlev.AllColumns.Add(this.olvColumnSample);
            this.ListViewFreqRxlev.AllColumns.Add(this.olvColumnRate);
            this.ListViewFreqRxlev.AllColumns.Add(this.olvColumnMaxRxlev);
            this.ListViewFreqRxlev.AllColumns.Add(this.olvColumnMinRxlev);
            this.ListViewFreqRxlev.AllColumns.Add(this.olvColumnAvgRxlev);
            this.ListViewFreqRxlev.AllColumns.Add(this.olvColumnAboveBenchRxlev);
            this.ListViewFreqRxlev.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnFreq,
            this.olvColumnSample,
            this.olvColumnRate,
            this.olvColumnMaxRxlev,
            this.olvColumnMinRxlev,
            this.olvColumnAvgRxlev,
            this.olvColumnAboveBenchRxlev});
            this.ListViewFreqRxlev.ContextMenuStrip = this.ctxMenu;
            this.ListViewFreqRxlev.Cursor = System.Windows.Forms.Cursors.Default;
            this.ListViewFreqRxlev.Dock = System.Windows.Forms.DockStyle.Fill;
            this.ListViewFreqRxlev.FullRowSelect = true;
            this.ListViewFreqRxlev.GridLines = true;
            this.ListViewFreqRxlev.HeaderWordWrap = true;
            this.ListViewFreqRxlev.IsNeedShowOverlay = false;
            this.ListViewFreqRxlev.Location = new System.Drawing.Point(0, 0);
            this.ListViewFreqRxlev.Name = "ListViewFreqRxlev";
            this.ListViewFreqRxlev.OwnerDraw = true;
            this.ListViewFreqRxlev.ShowGroups = false;
            this.ListViewFreqRxlev.Size = new System.Drawing.Size(737, 502);
            this.ListViewFreqRxlev.TabIndex = 5;
            this.ListViewFreqRxlev.UseCompatibleStateImageBehavior = false;
            this.ListViewFreqRxlev.View = System.Windows.Forms.View.Details;
            this.ListViewFreqRxlev.VirtualMode = true;
            this.ListViewFreqRxlev.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.listViewTotal_MouseDoubleClick);
            // 
            // olvColumnFreq
            // 
            this.olvColumnFreq.AspectName = "";
            this.olvColumnFreq.HeaderFont = null;
            this.olvColumnFreq.Text = "频点";
            this.olvColumnFreq.Width = 90;
            // 
            // olvColumnSample
            // 
            this.olvColumnSample.HeaderFont = null;
            this.olvColumnSample.Text = "采样点数";
            this.olvColumnSample.Width = 80;
            // 
            // olvColumnRate
            // 
            this.olvColumnRate.HeaderFont = null;
            this.olvColumnRate.Text = "比例";
            this.olvColumnRate.Width = 80;
            // 
            // olvColumnMaxRxlev
            // 
            this.olvColumnMaxRxlev.HeaderFont = null;
            this.olvColumnMaxRxlev.Text = "信号强度最大值";
            this.olvColumnMaxRxlev.Width = 100;
            // 
            // olvColumnMinRxlev
            // 
            this.olvColumnMinRxlev.HeaderFont = null;
            this.olvColumnMinRxlev.Text = "信号强度最小值";
            this.olvColumnMinRxlev.Width = 100;
            // 
            // olvColumnAvgRxlev
            // 
            this.olvColumnAvgRxlev.HeaderFont = null;
            this.olvColumnAvgRxlev.Text = "信号强度平均值";
            this.olvColumnAvgRxlev.Width = 100;
            // 
            // olvColumnAboveBenchRxlev
            // 
            this.olvColumnAboveBenchRxlev.HeaderFont = null;
            this.olvColumnAboveBenchRxlev.Text = "≥指定信号强度的采样点比例";
            this.olvColumnAboveBenchRxlev.Width = 160;
            // 
            // ZTScanLTEBestRxlevListForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(737, 502);
            this.Controls.Add(this.ListViewFreqRxlev);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Name = "ZTScanLTEBestRxlevListForm";
            this.Text = "LTE干频点信号强度分析";
            this.ctxMenu.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.ListViewFreqRxlev)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.ContextMenuStrip ctxMenu;
        private System.Windows.Forms.ToolStripMenuItem miExportExcel;
        private System.Windows.Forms.ToolStripSeparator toolStripMenuItem1;
        private System.Windows.Forms.ToolStripMenuItem miExpandAll;
        private System.Windows.Forms.ToolStripMenuItem miCallapsAll;
        private BrightIdeasSoftware.TreeListView ListViewFreqRxlev;
        private BrightIdeasSoftware.OLVColumn olvColumnFreq;
        private BrightIdeasSoftware.OLVColumn olvColumnAvgRxlev;
        private BrightIdeasSoftware.OLVColumn olvColumnRate;
        private BrightIdeasSoftware.OLVColumn olvColumnSample;
        private BrightIdeasSoftware.OLVColumn olvColumnMinRxlev;
        private BrightIdeasSoftware.OLVColumn olvColumnAboveBenchRxlev;
        private BrightIdeasSoftware.OLVColumn olvColumnMaxRxlev;

    }
}