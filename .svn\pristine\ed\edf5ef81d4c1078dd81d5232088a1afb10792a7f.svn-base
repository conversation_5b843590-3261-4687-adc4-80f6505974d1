﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using MasterCom.RAMS.Func;
using MasterCom.Util;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.ZTFunc;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTLteMgrsCoveSetDlg : BaseDialog
    {
        public LTEMgrsCondition Condition { get; set; }

        public ZTLteMgrsCoveSetDlg(ServiceName serviceName)
        {
            InitializeComponent();

            if (serviceName == ServiceName.NBIOTSCAN)
            {
                groupBoxRemove.Visible = false;
                ClientSize = new System.Drawing.Size(301, 222);
            }
        }

        public void SetCondition(LTEMgrsCondition cond)
        {
            if (cond == null)
            {
                return;
            }

            numRsrpMin.Value = (decimal)cond.MinRsrp;
            numRsrpDiff.Value = (decimal)cond.DiffRsrp;
            chkOptionalRsrp.Checked = cond.EnableOptional;
            numOptionalRsrp.Value = (decimal)cond.OptionalRsrp;
            chkTwoEarfcn.Checked = cond.CheckTwoEarfcn;
            chkStrongBand.Checked = cond.IsStrongRange;
            chkNotBand.Checked = cond.IsNotBand;

            freqBandControl1.chkFreqBand.Checked = cond.IsFreqBand;
            //设置频点条件
            if (cond.ListFreqPoint != null && cond.ListFreqPoint.Count > 0)
            {
                freqBandControl1.lvFreqBand.Items.Clear();
                foreach (FreqPoint fp in cond.ListFreqPoint)
                {
                    ListViewItem lvi = new ListViewItem();
                    lvi.Text = fp.Carrier + "_" + fp.FreqPointName;
                    lvi.Tag = fp;
                    freqBandControl1.lvFreqBand.Items.Add(lvi);
                }
            }
        }

        public LTEMgrsCondition GetCondition()
        {
            LTEMgrsCondition cond = new LTEMgrsCondition();
            cond.MinRsrp = (double)numRsrpMin.Value;
            cond.DiffRsrp = (double)numRsrpDiff.Value;
            cond.EnableOptional = chkOptionalRsrp.Checked;
            cond.OptionalRsrp = (double)numOptionalRsrp.Value;
            cond.CheckTwoEarfcn = chkTwoEarfcn.Checked;
            cond.IsFreqBand = freqBandControl1.chkFreqBand.Checked;
            cond.IsNotBand = chkNotBand.Checked;
            cond.IsStrongRange = chkStrongBand.Checked;
            //得到频点集合
            cond.ListFreqPoint = freqBandControl1.GetListViewItems();

            return cond;
        }

        private void chkTwoEarfcn_CheckedChanged(object sender, EventArgs e)
        {
            freqBandControl1.chkFreqBand.Enabled = !chkTwoEarfcn.Checked;
            chkStrongBand.Enabled = !chkTwoEarfcn.Checked;
            chkNotBand.Enabled = !chkTwoEarfcn.Checked;
            if (chkTwoEarfcn.Checked)
            {
                freqBandControl1.lvFreqBand.Enabled = false;
                freqBandControl1.btnFreqBand.Enabled =false;
            }
            else
            {
                freqBandControl1.lvFreqBand.Enabled = freqBandControl1.chkFreqBand.Checked;
                freqBandControl1.btnFreqBand.Enabled = freqBandControl1.chkFreqBand.Checked;
            }
        }
        private void chkOptionalRsrp_CheckedChanged(object sender, EventArgs e)
        {
            numOptionalRsrp.Enabled = chkOptionalRsrp.Checked;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            if ((freqBandControl1.chkFreqBand.Checked && freqBandControl1.lvFreqBand.Items.Count <= 0) || (!freqBandControl1.chkFreqBand.Checked && !chkNotBand.Checked && !chkStrongBand.Checked))
            {
                XtraMessageBox.Show("请选择剔除条件", "提示");
                DialogResult = DialogResult.None;
                return;
            }
            Condition = GetCondition();
            DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
        }

        /// <summary>
        /// 分频点
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void chkFreqBand_CheckedChanged(object sender, EventArgs e)
        {
            chkNotBand.Enabled = !freqBandControl1.chkFreqBand.Checked;
            chkStrongBand.Enabled = !freqBandControl1.chkFreqBand.Checked;

            if (freqBandControl1.chkFreqBand.Checked)
            {
                chkNotBand.Checked = false;
                chkStrongBand.Checked = false;
            }
        }

        /// <summary>
        /// 最强归属段
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void chkStrongBand_CheckedChanged(object sender, EventArgs e)
        {
            freqBandControl1.chkFreqBand.Enabled = !chkStrongBand.Checked;
            chkNotBand.Enabled = !chkStrongBand.Checked;
            if (chkStrongBand.Checked)
            {
                freqBandControl1.chkFreqBand.Checked = false;
                chkNotBand.Checked = false;
            }
        }

        /// <summary>
        /// 不分段
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void chkNotBand_CheckedChanged(object sender, EventArgs e)
        {
            freqBandControl1.chkFreqBand.Enabled = !chkNotBand.Checked;
            chkStrongBand.Enabled = !chkNotBand.Checked;

            if (chkNotBand.Checked)
            {
                freqBandControl1.chkFreqBand.Checked = false;
                chkStrongBand.Checked = false;
            }
        }

        private void ZTLteMgrsCoveSetDlg_Load(object sender, EventArgs e)
        {
            freqBandControl1.ChkFreqBandChange_click += new FreqBandControl.ChkChangeDelegate(chkFreqBand_CheckedChanged);//把事件绑定到自定义的委托上
        }
    }
}
