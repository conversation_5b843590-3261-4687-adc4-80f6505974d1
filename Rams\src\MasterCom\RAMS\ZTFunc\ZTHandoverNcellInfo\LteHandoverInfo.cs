﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class LteHandoverInfo
    {
        public LteHandoverInfo(string fileName)
        {
            FileName = fileName;
            NcellList = new List<LteNcellInfo>();
            TestPonitList = new List<TestPoint>();
        }
        public int SN { get; set; }
        public string FileName { get; set; }
        public double Longitude { get; set; }
        public double Latitude { get; set; }
        public string Time { get; set; }
        public string CellName { get; set; }
        public string Earcn { get; set; }
        public string Pci { get; set; }
        public float? Rsrp { get; set; }

        public List<LteNcellInfo> NcellList { get; set; }
        public Event Evt{ get; set; }

        public List<TestPoint> TestPonitList { get; set; }
    }

    public class LteNcellInfo
    {
        public int SN { get; set; }
        public string CellName { get; set; }
        public int? Earcn { get; set; }
        public int? Pci { get; set; }
        public float? Rsrp { get; set; }
        public double Longitude { get; set; }
        public double Latitude { get; set; }
    }
}
