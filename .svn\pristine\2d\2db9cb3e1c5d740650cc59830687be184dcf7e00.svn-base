﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Grid;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.KPI_Statistics
{
    public class PkGridDataHub : GridUnitBase
    {
        public PkGridDataHub(double ltLng, double ltLat)
            : base(ltLng, ltLat)
        {
            FileList = new List<FileInfo>();
            FileGridDic = new Dictionary<int, PkGridDataHub>();
            RoadName = MasterCom.Util.GISManager.GetInstance().GetRoadPlaceDesc(CenterLng, CenterLat);
        }
        public PkGridDataHub()
            : base()
        { }
        public string RangName { get; set; }
        public CarrierStatDataHub cmDataHub { get; set; }
        public CarrierStatDataHub cuDataHub { get; set; }
        public CarrierStatDataHub ctDataHub { get; set; }

        public string RoadName { get; private set; }
        public double CMFormulaValue { get; set; }

        public List<FileInfo> FileList { get; set; }

        //文件栅格
        public Dictionary<int,PkGridDataHub> FileGridDic { get; private set; }
        public void AddFileGridData(FileInfo fi, KPIStatDataBase data)
        {
            PkGridDataHub fileGrid;
            if (!FileGridDic.TryGetValue(fi.ID, out fileGrid))
            {
                fileGrid = new PkGridDataHub(data.LTLng, data.LTLat);
                fileGrid.FileList.Add(fi);
                FileGridDic.Add(fi.ID, fileGrid);
            }
            fileGrid.AddStatData(fi, data);
        }

        public string LowestGridLogNames { get; set; }
        List<PkGridDataHub> fileGridList;
        public void SortFileGrid()
        {
            fileGridList = new List<PkGridDataHub>(FileGridDic.Values);
            fileGridList.Sort((a, b) =>
            {
                return a.CMFormulaValue.CompareTo(b.CMFormulaValue);
            });
        }

        public void SetLowestGridLogName(int num)
        {
            Dictionary<string, PkGridDataHub> fileGridDic = new Dictionary<string, PkGridDataHub>();
            foreach (PkGridDataHub fileGrid in fileGridList)
            {
                FileInfo file = fileGrid.FileList[0];
                if (!fileGridDic.ContainsKey(file.Name) && file.CarrierType == 1)
                {
                    fileGridDic.Add(file.Name, fileGrid);
                }
                if (fileGridDic.Count >= num)
                {
                    break;
                }
            }

            StringBuilder sb = new StringBuilder();
            foreach (string logName in fileGridDic.Keys)
            {
                sb.Append(logName);
                sb.Append(";");
            }
            LowestGridLogNames = sb.ToString().TrimEnd(';');
        }

        public void AddStatData(FileInfo fi, KPIStatDataBase data)
        {
            if ((int)(CarrierType.ChinaMobile) == fi.CarrierType)
            {
                if (cmDataHub == null)
                {
                    cmDataHub = new CarrierStatDataHub(CarrierType.ChinaMobile);
                }
                cmDataHub.AddStatData(data, false);
            }
            else if ((int)(CarrierType.ChinaUnicom) == fi.CarrierType)
            {
                if (cuDataHub == null)
                {
                    cuDataHub = new CarrierStatDataHub(CarrierType.ChinaUnicom);
                }
                cuDataHub.AddStatData(data, false);
            }
            else
            {
                if (ctDataHub == null)
                {
                    ctDataHub = new CarrierStatDataHub(CarrierType.ChinaTelecom);
                }
                ctDataHub.AddStatData(data, false);
            }
        }

        public double CalcFormula(CarrierType carrierType, int momtFlag, string formula, params object[] extraParams)
        {
            double v = double.NaN;
            CarrierStatDataHub hub = getDataHub(carrierType);
            if (hub == null)
            {
                return v;
            }
            v = hub.CalcFormula(momtFlag, formula, extraParams);
            return v;
        }

        private CarrierStatDataHub getDataHub(CarrierType carrierType)
        {
            CarrierStatDataHub hub = null;
            switch (carrierType)
            {
                case CarrierType.ChinaMobile:
                    hub = cmDataHub;
                    break;
                case CarrierType.ChinaUnicom:
                    hub = cuDataHub;
                    break;
                case CarrierType.ChinaTelecom:
                    hub = ctDataHub;
                    break;
                default:
                    break;
            }
            return hub;
        }

    }

    public class PkGridDataGroup
    {
        /// <summary>
        /// 默认为非汇总组
        /// </summary>
        /// <param name="groupInfo"></param>
        public PkGridDataGroup(object groupInfo)
        {
            GroupInfo = groupInfo;
            Matrix = new GridMatrix<PkGridDataHub>();
        }
        public override string ToString()
        {
            string info = string.Empty;
            if (GroupInfo != null)
            {
                info = GroupInfo.ToString();
            }
            return info;
        }
        /// <summary>
        /// 统计组信息，区分按区域，栅格，文件，小区，道路等。
        /// </summary>
        public object GroupInfo { get; set; }
        protected CarrierStatDataHub cmDataHub = null;
        protected CarrierStatDataHub cuDataHub = null;
        protected CarrierStatDataHub ctDataHub = null;

        public void Merge(PkGridDataGroup other)
        {
            MergeDataHub(ref cmDataHub, other.cmDataHub);
            MergeDataHub(ref ctDataHub, other.ctDataHub);
            MergeDataHub(ref cuDataHub, other.cuDataHub);
        }

        private void MergeDataHub(ref CarrierStatDataHub curHub, CarrierStatDataHub otherHub)
        {
            if (otherHub != null)
            {
                if (curHub == null)
                {
                    curHub = otherHub.Clone();
                }
                else
                {
                    curHub.Merge(otherHub);
                }
            }
        }

        public PkGridDataGroup Clone()
        {
            PkGridDataGroup grp = new PkGridDataGroup(GroupInfo);
            if (this.cmDataHub != null)
            {
                grp.cmDataHub = this.cmDataHub.Clone();
            }
            if (this.ctDataHub != null)
            {
                grp.ctDataHub = this.ctDataHub.Clone();
            }
            if (this.cuDataHub != null)
            {
                grp.cuDataHub = this.cuDataHub.Clone();
            }
            return grp;
        }

        public GridMatrix<PkGridDataHub> Matrix { get; set; }
        public virtual void AddStatData(FileInfo fileInfo, KPIStatDataBase data)
        {
            if (fileInfo == null || data == null)
            {
                return;
            }
            PkGridDataHub temp = new PkGridDataHub(data.LTLng, data.LTLat);
            PkGridDataHub grid = Matrix[temp.RowIdx, temp.ColIdx];
            if (grid == null)
            {
                Matrix[temp.RowIdx, temp.ColIdx] = temp;
                grid = temp;
            }
            grid.AddFileGridData(fileInfo, data);
            grid.AddStatData(fileInfo, data);
            addCarrierDataHub(fileInfo, data);
        }

        private void addCarrierDataHub(FileInfo fileInfo, KPIStatDataBase data)
        {
            if (fileInfo.CarrierType == (int)CarrierType.ChinaMobile)
            {
                if (cmDataHub == null)
                {
                    cmDataHub = new CarrierStatDataHub(CarrierType.ChinaMobile);
                }
                cmDataHub.AddStatData(fileInfo, data, false);
            }
            else if (fileInfo.CarrierType == (int)CarrierType.ChinaUnicom)
            {
                if (cuDataHub == null)
                {
                    cuDataHub = new CarrierStatDataHub(CarrierType.ChinaUnicom);
                }
                cuDataHub.AddStatData(fileInfo, data, false);
            }
            else
            {
                if (ctDataHub == null)
                {
                    ctDataHub = new CarrierStatDataHub(CarrierType.ChinaTelecom);
                }
                ctDataHub.AddStatData(fileInfo, data, false);
            }
        }


        public void FinalMtMoGroup()
        {
            if (cmDataHub != null)
                cmDataHub.FinalMtMoGroup();
            if (cuDataHub != null)
                cuDataHub.FinalMtMoGroup();
            if (ctDataHub != null)
                ctDataHub.FinalMtMoGroup();
        }

        public double CalcFormula(CarrierType carrierType, int momtFlag, string formula, params object[] extraParams)
        {
            double v = double.NaN;
            CarrierStatDataHub hub = getDataHub(carrierType);
            if (hub == null)
            {
                return v;
            }
            v = hub.CalcFormula(momtFlag, formula, extraParams);
            return v;
        }

        private CarrierStatDataHub getDataHub(CarrierType carrierType)
        {
            CarrierStatDataHub hub = null;
            switch (carrierType)
            {
                case CarrierType.ChinaMobile:
                    hub = cmDataHub;
                    break;
                case CarrierType.ChinaUnicom:
                    hub = cuDataHub;
                    break;
                case CarrierType.ChinaTelecom:
                    hub = ctDataHub;
                    break;
                default:
                    break;
            }
            return hub;
        }


        #region IComparable<PkGridDataGroup> 成员

        public int CompareTo(PkGridDataGroup other)
        {
            if (other == null)
            {
                return 1;
            }
            return this.ToString().CompareTo(other.ToString());
        }

        #endregion
    }

    public class PkGridDataCombine
    {
        public PkGridDataCombine(PkGridDataHub cu)
        {
            GroupInfo = cu;
            Matrix = new GridMatrix<PkGridDataHub>();
            AllFileGridList = new List<PkGridDataHub>();
            Matrix[cu.RowIdx, cu.ColIdx] = cu;
        }
        public string LowestGridLogNames { get; set; }
        public List<PkGridDataHub> AllFileGridList { get; set; }
        public void SortFileGrid()
        {
            AllFileGridList.Sort((a, b) =>
            {
                return a.CMFormulaValue.CompareTo(b.CMFormulaValue);
            });
        }

        public void SetLowestGridLogName(int num)
        {
            Dictionary<string, PkGridDataHub> fileGridDic = new Dictionary<string, PkGridDataHub>();
            foreach (PkGridDataHub fileGrid in AllFileGridList)
            {
                FileInfo file = fileGrid.FileList[0];
                if (!fileGridDic.ContainsKey(file.Name) && file.CarrierType == 1)
                {
                    fileGridDic.Add(file.Name, fileGrid);
                }
                if (fileGridDic.Count >= num)
                {
                    break;
                }
            }

            StringBuilder sb = new StringBuilder();
            foreach (string logName in fileGridDic.Keys)
            {
                sb.Append(logName);
                sb.Append(";");
            }
            LowestGridLogNames = sb.ToString().TrimEnd(';');
        }

        public string RoadNames { get; private set; }
        public void CalculateRoadName()
        {
            Dictionary<string, int> roadDic = new Dictionary<string, int>();
            StringBuilder sb = new StringBuilder();
            foreach (PkGridDataHub unit in Matrix)
            {
                if (!string.IsNullOrEmpty(unit.RoadName))
                {
                    getGridRoadName(roadDic, sb, unit);
                }
            }
            RoadNames = sb.ToString().TrimEnd(';');
        }

        private void getGridRoadName(Dictionary<string, int> roadDic, StringBuilder sb, PkGridDataHub unit)
        {
            //存在一个栅格有多条道路
            string[] gridRoad = unit.RoadName.Split(';');
            foreach (string road in gridRoad)
            {
                int times;
                if (!roadDic.TryGetValue(road, out times))
                {
                    roadDic[road] = ++times;
                    sb.Append(road);
                    sb.Append(";");
                }
            }
        }

        public string CombineRangName
        {
            get
            {
                if (Matrix.Grids != null && Matrix.Grids.Count > 0)
                {
                    return Matrix.Grids[0].RangName;
                }
                return null;
            }
        }
        public override string ToString()
        {
            StringBuilder strb = new StringBuilder();
            foreach (PkGridDataHub dataHub in Matrix)
            {
                strb.Append(dataHub.ToString() + ", ");
            }
            if (strb.Length > 1)
            {
                strb = strb.Remove(strb.Length - 2, 2);
            }
            return strb.ToString();
        }
        /// <summary>
        /// 统计组信息，区分按区域，栅格，文件，小区，道路等。
        /// </summary>
        public object GroupInfo { get; set; }
        protected CarrierStatDataHub cmDataHub = null;
        protected CarrierStatDataHub cuDataHub = null;
        protected CarrierStatDataHub ctDataHub = null;

        public GridMatrix<PkGridDataHub> Matrix { get; set; }
        public virtual void AddDataHub(PkGridDataHub dataHub)
        {
            if (dataHub.cmDataHub != null)
            {
                if (this.cmDataHub == null)
                {
                    this.cmDataHub = dataHub.cmDataHub.Clone();
                }
                else
                {
                    this.cmDataHub.Merge(dataHub.cmDataHub);
                }
            }
            if (dataHub.ctDataHub != null)
            {
                if (this.ctDataHub == null)
                {
                    this.ctDataHub = dataHub.ctDataHub.Clone();
                }
                else
                {
                    this.ctDataHub.Merge(dataHub.ctDataHub);
                }
            }
            if (dataHub.cuDataHub != null)
            {
                if (this.cuDataHub == null)
                {
                    this.cuDataHub = dataHub.cuDataHub.Clone();
                }
                else
                {
                    this.cuDataHub.Merge(dataHub.cuDataHub);
                }
            }
        }

        public void FinalMtMoGroup()
        {
            foreach (PkGridDataHub dataHub in Matrix)
            {
                this.AddDataHub(dataHub);
            }
            if (cmDataHub != null)
                cmDataHub.FinalMtMoGroup();
            if (cuDataHub != null)
                cuDataHub.FinalMtMoGroup();
            if (ctDataHub != null)
                ctDataHub.FinalMtMoGroup();
        }

        public double CalcFormula(CarrierType carrierType, int momtFlag, string formula, params object[] extraParams)
        {
            double v = double.NaN;
            CarrierStatDataHub hub = getDataHub(carrierType);
            if (hub == null)
            {
                return v;
            }
            v = hub.CalcFormula(momtFlag, formula, extraParams);
            return v;
        }

        private CarrierStatDataHub getDataHub(CarrierType carrierType)
        {
            CarrierStatDataHub hub = null;
            switch (carrierType)
            {
                case CarrierType.ChinaMobile:
                    hub = cmDataHub;
                    break;
                case CarrierType.ChinaUnicom:
                    hub = cuDataHub;
                    break;
                case CarrierType.ChinaTelecom:
                    hub = ctDataHub;
                    break;
                default:
                    break;
            }
            return hub;
        }
        public bool Intersect(PkGridDataHub cu)
        {
            if (cu.RangName == CombineRangName)
            {
                for (int r = cu.RowIdx - 1; r <= cu.RowIdx + 1; r++)
                {
                    for (int c = cu.ColIdx - 1; c <= cu.ColIdx + 1; c++)
                    {
                        if (Matrix[r, c] != null)
                        {
                            return true;
                        }
                    }
                }
            }
            return false;
        }
        public void Join(PkGridDataCombine combinGb)
        {
            Matrix.Merge(combinGb.Matrix);
        }
    }
}
