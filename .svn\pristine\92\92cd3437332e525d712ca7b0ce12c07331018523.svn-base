﻿using System;
using System.Collections.Generic;
using System.Text;
using System.IO;
using System.Reflection;
using System.Drawing;
using System.Xml;
using System.Windows.Forms;

using NPOI.SS.UserModel;
using NPOI.SS.Util;

using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Func;


namespace MasterCom.RAMS.ZTFunc
{
    public delegate void GetNBCellDelegate();

    public enum CellParamExportType
    {
        DingLi_W,
        RiXun_W,
        Actix_W,
        Google_W,
        Google_G,
        TEMS_G,
        Nastar_G,
        DingLi_G,
        DingLi_TD,
    }

    public class CellParamExportCondition
    {
        public string SavePath { get; set; }
        public List<CellParamExportType> ExportTypes { get; set; }

        public CellParamExportCondition()
        {
            ExportTypes = new List<CellParamExportType>();
        }

        public bool IsValid()
        {
            return SavePath != null && Directory.Exists(SavePath) && ExportTypes.Count > 0;
        }
    }

    public class CellParamExporter
    {
        public static CellParamExporter Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new CellParamExporter();
                }
                return instance;
            }
        }

        public List<CellParamExportBase> GetExporters(CellParamExportCondition cond)
        {
            Dictionary<CellParamExportType, CellParamExportBase> exportDic = new Dictionary<CellParamExportType, CellParamExportBase>();
            foreach (CellParamExportType t in cond.ExportTypes)
            {
                if (exportDic.ContainsKey(t))
                {
                    continue;
                }
                if (!typeExporterMap.ContainsKey(t))
                {
                    continue;
                }

                CellParamExportBase exporter = Activator.CreateInstance(typeExporterMap[t]) as CellParamExportBase;
                // if exporter == null , let it throw
                exporter.SavePath = cond.SavePath;
                exportDic.Add(t, exporter);
            }
            return new List<CellParamExportBase>(exportDic.Values);
        }
        
        private CellParamExporter()
        {
            typeExporterMap = new Dictionary<CellParamExportType, Type>();

            typeExporterMap.Add(CellParamExportType.DingLi_W, typeof(CellParamExportDingLiW));
            typeExporterMap.Add(CellParamExportType.Actix_W, typeof(CellParamExportActixW));
            typeExporterMap.Add(CellParamExportType.Google_W, typeof(CellParamExportGoogleW));
            typeExporterMap.Add(CellParamExportType.RiXun_W, typeof(CellParamExportRiXunW));

            typeExporterMap.Add(CellParamExportType.Nastar_G, typeof(CellParamExportNastarGsm));
            typeExporterMap.Add(CellParamExportType.TEMS_G, typeof(CellParamExportTemsGsm));
            typeExporterMap.Add(CellParamExportType.Google_G, typeof(CellParamExportGoogleGsm));
            typeExporterMap.Add(CellParamExportType.DingLi_G, typeof(CellParamExportDingLiGsm));

            typeExporterMap.Add(CellParamExportType.DingLi_TD, typeof(CellParamExportDingLiTD));
        }

        private readonly Dictionary<CellParamExportType, Type> typeExporterMap;
        private static CellParamExporter instance;
    }

    public abstract class CellParamExportBase
    {
        protected CellParamExportBase()
        {
            MainModel = MainModel.GetInstance();
        }

        public string SavePath
        {
            get;
            set;
        }

        public abstract GetNBCellDelegate GetNBCellInfo();

        public abstract void Export();

        protected MainModel MainModel;
    }

    public abstract class CellParamExportGoogleBase : CellParamExportBase
    {
        protected string getCDataSection(Object cell)
        {
            string cellName = "";
            string longitude = "";
            string latitude = "";
            string direction = "";
            string downword = "";
            string altitude = "";
            string arfcnTag = "";
            string pscTag = "";
            string lac = "";
            string ci = "";
            string arfcn = "";
            string psc = "";

            if (cell is WCell)
            {
                WCell wCell = cell as WCell;
                cellName = wCell.Name;
                longitude = wCell.Longitude.ToString();
                latitude = wCell.Latitude.ToString();
                direction = wCell.Direction.ToString();
                downword = wCell.Downword < 0 ? "0" : wCell.Downword.ToString();
                altitude = wCell.Altitude < 0 ? "0" : wCell.Altitude.ToString();
                arfcnTag = "ARFCN";
                pscTag = "PSC";
                lac = wCell.LAC.ToString();
                ci = wCell.CI.ToString();
                arfcn = wCell.UARFCN.ToString();
                psc = wCell.PSC.ToString();
            }
            else if (cell is Cell)
            {
                Cell gsmCell = cell as Cell;
                cellName = gsmCell.Name;
                longitude = gsmCell.Longitude.ToString();
                latitude = gsmCell.Latitude.ToString();
                direction = gsmCell.Direction.ToString();
                downword = gsmCell.Downword < 0 ? "0" : gsmCell.ToString();
                altitude = gsmCell.Altitude < 0 ? "0" : gsmCell.Altitude.ToString();
                arfcnTag = "BCCH";
                pscTag = "BSIC";
                lac = gsmCell.LAC.ToString();
                ci = gsmCell.CI.ToString();
                arfcn = gsmCell.BCCH.ToString();
                psc = gsmCell.BSIC.ToString();
            }

            string content = "<table border=\"0\" width=\"620\" style=\"border-collapse: collapse\">";
            content += "<tr>";
            content += "<td width=\"30%\"><p align=\"left\"><b><span lang=\"zh-cn\"><font size=\"6\" face=\"黑体\" color=\"#0000FF\">基站扇区信息</font></span></b></p></td>";
            content += "<td width=\"30%\"><p align=\"right\"><b><span lang=\"zh-cn\"><font size=\"6\" face=\"黑体\" color=\"#66FF33\">" + DateTime.Now.ToLongDateString() + "</font></span></b></p></td>";
            content += "</tr>";
            content += "</table>";

            content += "<table width=\"620\" border=\"1\" style=\"border-collapse: collapse\">";
            content += "<tr>";
            content += "<td colspan=\"1\"><p align=\"left\"><b><font color=color=\"#FF0000\">小区名称</font></b></td>";
            content += "<td colspan=\"3\"><p align=\"center\">" + cellName + "</td>";
            content += "</tr>";

            content += "<tr>";
            content += "<td width=\"20%\"><p align=\"left\"><font color=\"#0000FF\">小区名称</font></td>";
            content += "<td width=\"30%\"><font color=\"#0000FF\">地区</font></td>";
            content += "<td width=\"20%\"><font color=\"#0000FF\">经度</font></td>";
            content += "<td width=\"30%\"><font color=\"#0000FF\">纬度</font></td>";
            content += "</tr>";

            content += "<tr>";
            content += "<td width=\"20%\">" + cellName + "</td>";
            content += "<td width=\"30%\">" + DistrictManager.GetInstance().getDistrictName(this.MainModel.DistrictID) + "</td>";
            content += "<td width=\"20%\">" + longitude + "</td>";
            content += "<td width=\"30%\">" + latitude + "</td>";
            content += "</tr>";

            content += "<tr>";
            content += "<td width=\"20%\"><p align=\"left\"><font color=\"#0000FF\">方向角</font></td>";
            content += "<td width=\"30%\"><font color=\"#0000FF\">下倾角</font></td>";
            content += "<td width=\"20%\"><font color=\"#0000FF\">挂高</font></td>";
            content += "<td width=\"30%\"><font color=\"#0000FF\"></font></td>";
            content += "</tr>";

            content += "<tr>";
            content += "<td width=\"20%\">" + direction + "</td>";
            content += "<td width=\"30%\">" + downword + "</td>";
            content += "<td width=\"20%\">" + altitude + "</td>";
            content += "<td width=\"30%\">" + "" + "</td>";
            content += "</tr>";

            content += "<tr>";
            content += "<td width=\"20%\"><p align=\"left\"><font color=\"#0000FF\">LAC</font></td>";
            content += "<td width=\"30%\"><font color=\"#0000FF\">CI</font></td>";
            content += "<td width=\"20%\"><font color=\"#0000FF\">" + arfcnTag + "</font></td>";
            content += "<td width=\"30%\"><font color=\"#0000FF\">" + pscTag + "</font></td>";
            content += "</tr>";

            content += "<tr>";
            content += "<td width=\"20%\">" + lac + "</td>";
            content += "<td width=\"30%\">" + ci + "</td>";
            content += "<td width=\"20%\">" + arfcn + "</td>";
            content += "<td width=\"30%\">" + psc + "</td>";
            content += "</tr>";

            return content;
        }

        protected string getCellPoints(Object cell)
        {
            float direction = 0;
            double longitude = 0;
            double latitude = 0;

            if (cell is WCell)
            {
                WCell wCell = cell as WCell;
                direction = wCell.Direction;
                longitude = wCell.Longitude;
                latitude = wCell.Latitude;
            }
            else if (cell is Cell)
            {
                Cell gsmCell = cell as Cell;
                direction = gsmCell.Direction;
                longitude = gsmCell.Longitude;
                latitude = gsmCell.Latitude;
            }

            List<LayerPoint> antennaGEPoints = new List<LayerPoint>();

            double radiusGE = 0.00036;
            antennaGEPoints.Add(new LayerPoint(0, -0.00001, 1));
            antennaGEPoints.Add(new LayerPoint(radiusGE - 0.00003, -0.00001, 1));
            antennaGEPoints.Add(new LayerPoint(radiusGE - 0.00005, -0.00005, 1));
            antennaGEPoints.Add(new LayerPoint(radiusGE, 0, 1));
            antennaGEPoints.Add(new LayerPoint(radiusGE - 0.00005, 0.00005, 1));
            antennaGEPoints.Add(new LayerPoint(radiusGE - 0.00003, 0.00001, 1));
            antennaGEPoints.Add(new LayerPoint(0, 0.00001, 1));
            antennaGEPoints.Add(new LayerPoint(0, -0.00001, 1));

            StringBuilder location = new StringBuilder();
            foreach (LayerPoint p in antennaGEPoints)
            {
                double angle1 = (90 - direction) * Math.PI / 180;
                double angle2 = Math.Atan(p.Y / p.X);
                double angle3 = angle1 + angle2;
                double r = Math.Sqrt(p.X * p.X + p.Y * p.Y);
                double tarX = r * Math.Cos(angle3) + longitude;
                double tarY = r * Math.Sin(angle3) + latitude;
                location.AppendFormat("{0},{1},10 ", tarX, tarY);
            }

            return location.ToString();
        }

        protected void initGoogleStyle(ref KMLExporter exporter, ref XmlElement root, ref XmlDocument doc)
        {
            XmlElement Style1 = getElementStyle1(doc);
            root.AppendChild(Style1);
            XmlElement Style2 = getElementStyle2(doc);
            root.AppendChild(Style2);
            XmlElement Style3 = getElementStyle3(doc);
            root.AppendChild(Style3);
            XmlElement Style4 = getElementStyle4(doc);
            root.AppendChild(Style4);
            XmlElement Style5 = getElementStyle5(doc);
            root.AppendChild(Style5);
        }

        protected void addFolderInfoPlaceMark(XmlDocument doc, XmlElement FolderInfo, Object cell)
        {
            XmlElement PlaceMark = doc.CreateElement("Placemark");
            FolderInfo.AppendChild(PlaceMark);

            XmlElement name = doc.CreateElement("name");
            PlaceMark.AppendChild(name);
            if (cell is WCell)
            {
                WCell wCell = cell as WCell;
                name.AppendChild(doc.CreateTextNode(wCell.Name));
            }
            else if (cell is Cell)
            {
                Cell gsmCell = cell as Cell;
                name.AppendChild(doc.CreateTextNode(gsmCell.Name));
            }

            XmlElement styleUrl = doc.CreateElement("styleUrl");
            PlaceMark.AppendChild(styleUrl);
            styleUrl.AppendChild(doc.CreateTextNode("#onlytextname"));

            XmlElement Point = doc.CreateElement("Point");
            PlaceMark.AppendChild(Point);
            XmlElement extrude = doc.CreateElement("extrude");
            Point.AppendChild(extrude);
            extrude.AppendChild(doc.CreateTextNode("1"));
            XmlElement altitudeMode = doc.CreateElement("altitudeMode");
            Point.AppendChild(altitudeMode);
            altitudeMode.AppendChild(doc.CreateTextNode("relativeToGround"));
            XmlElement coordinates = doc.CreateElement("coordinates");
            Point.AppendChild(coordinates);
            if (cell is WCell)
            {
                WCell wCell = cell as WCell;
                coordinates.AppendChild(doc.CreateTextNode(wCell.Longitude.ToString() + "," + wCell.Latitude.ToString() + ",0"));
            }
            else if (cell is Cell)
            {
                Cell gsmCell = cell as Cell;
                coordinates.AppendChild(doc.CreateTextNode(gsmCell.Longitude.ToString() + "," + gsmCell.Latitude.ToString() + ",0"));
            }
        }

        protected void addFolderPicPlaceMark(XmlDocument doc, XmlElement FolderPic, Object cell)
        {
            XmlElement PlaceMark = doc.CreateElement("Placemark");
            FolderPic.AppendChild(PlaceMark);

            XmlElement name = doc.CreateElement("name");
            PlaceMark.AppendChild(name);
            if (cell is WCell)
            {
                WCell wCell = cell as WCell;
                name.AppendChild(doc.CreateTextNode(wCell.Name));
            }
            else if (cell is Cell)
            {
                Cell gsmCell = cell as Cell;
                name.AppendChild(doc.CreateTextNode(gsmCell.Name));
            }

            XmlElement styleUrl = doc.CreateElement("styleUrl");
            PlaceMark.AppendChild(styleUrl);
            styleUrl.AppendChild(doc.CreateTextNode("#msn-yzl-pushpin-sector0"));

            XmlElement Polygon = doc.CreateElement("Polygon");
            PlaceMark.AppendChild(Polygon);
            XmlElement extrude = doc.CreateElement("extrude");
            Polygon.AppendChild(extrude);
            extrude.AppendChild(doc.CreateTextNode("1"));
            XmlElement altitudeMode = doc.CreateElement("altitudeMode");
            Polygon.AppendChild(altitudeMode);
            altitudeMode.AppendChild(doc.CreateTextNode("relativeToGround"));
            XmlElement outerBoundaryIs = doc.CreateElement("outerBoundaryIs");
            Polygon.AppendChild(outerBoundaryIs);
            XmlElement LinearRing = doc.CreateElement("LinearRing");
            outerBoundaryIs.AppendChild(LinearRing);
            XmlElement coordinates = doc.CreateElement("coordinates");
            LinearRing.AppendChild(coordinates);
            coordinates.AppendChild(doc.CreateTextNode(getCellPoints(cell)));

            XmlElement description = doc.CreateElement("description");
            PlaceMark.AppendChild(description);
            description.AppendChild(doc.CreateCDataSection(getCDataSection(cell)));
        }

        protected XmlElement getElementStyle1(XmlDocument doc)
        {
            XmlElement elemStyle = doc.CreateElement("Style");
            elemStyle.SetAttribute("id", "onlytextname");
            XmlElement elemStyleIcon = doc.CreateElement("IconStyle");
            elemStyle.AppendChild(elemStyleIcon);
            XmlElement elemStyleIconIcon = doc.CreateElement("Icon");
            elemStyleIcon.AppendChild(elemStyleIconIcon);

            XmlElement elemStyleLable = doc.CreateElement("LabelStyle");
            elemStyle.AppendChild(elemStyleLable);
            XmlElement elemStyleLableColor = doc.CreateElement("color");
            elemStyleLable.AppendChild(elemStyleLableColor);
            XmlElement elemStyleLableScale = doc.CreateElement("scale");
            elemStyleLable.AppendChild(elemStyleLableScale);

            return elemStyle;
        }

        protected XmlElement getElementStyle2(XmlDocument doc)
        {
            XmlElement elemStyle = doc.CreateElement("Style");
            elemStyle.SetAttribute("id", "msn-yzl-pushpin0");

            //Style2-IconStyle
            XmlElement elemStyleIcon = doc.CreateElement("IconStyle");
            elemStyle.AppendChild(elemStyleIcon);

            XmlElement elemStyleIconScale = doc.CreateElement("scale");
            elemStyleIcon.AppendChild(elemStyleIconScale);
            elemStyleIconScale.AppendChild(doc.CreateTextNode("1"));

            //Style2-IconStyle-Icon
            XmlElement elemStyleIconIcon = doc.CreateElement("Icon");
            elemStyleIcon.AppendChild(elemStyleIconIcon);

            XmlElement elemStyleIconIconHref = doc.CreateElement("href");
            elemStyleIconIcon.AppendChild(elemStyleIconIconHref);
            elemStyleIconIconHref.AppendChild(doc.CreateTextNode("http://maps.google.com/mapfiles/kml/pushpin/blue-pushpin.png"));

            //Style2-IconStyle-HotSpot
            XmlElement elemStyleIconHotSpot = doc.CreateElement("hotSpot");
            elemStyleIcon.AppendChild(elemStyleIconHotSpot);
            elemStyleIconHotSpot.SetAttribute("x", "20");
            elemStyleIconHotSpot.SetAttribute("y", "2");
            elemStyleIconHotSpot.SetAttribute("xunits", "pixels");
            elemStyleIconHotSpot.SetAttribute("yunits", "pixels");
            elemStyleIcon.AppendChild(elemStyleIconHotSpot);

            //Style2-PolyStyle
            XmlElement elemStylePoly = doc.CreateElement("PolyStyle");
            elemStyle.AppendChild(elemStylePoly);
            XmlElement elemStylePolyColor = doc.CreateElement("color");
            elemStylePoly.AppendChild(elemStylePolyColor);
            elemStylePolyColor.AppendChild(doc.CreateTextNode("7000ffff"));

            //Style2-LabelStyle
            XmlElement elemStyleLabel = doc.CreateElement("LabelStyle");
            elemStyle.AppendChild(elemStyleLabel);
            XmlElement elemStyleLableScale = doc.CreateElement("scale");
            elemStyleLabel.AppendChild(elemStyleLableScale);

            return elemStyle;
        }

        protected XmlElement getElementStyle3(XmlDocument doc)
        {
            XmlElement elemStyle = doc.CreateElement("Style");
            elemStyle.SetAttribute("id", "msn-yzl-pushpin-sector0");

            //Style3-LineStyle
            XmlElement elemStyleLine = doc.CreateElement("LineStyle");
            elemStyle.AppendChild(elemStyleLine);
            XmlElement elemStyleLineWidth = doc.CreateElement("width");
            elemStyleLine.AppendChild(elemStyleLineWidth);
            elemStyleLineWidth.AppendChild(doc.CreateTextNode("1"));
            XmlElement elemStyleLineColor = doc.CreateElement("color");
            elemStyleLine.AppendChild(elemStyleLineColor);
            elemStyleLineColor.AppendChild(doc.CreateTextNode("FFFF8080"));

            //Style3-PolyStyle
            XmlElement elemStylePoly = doc.CreateElement("PolyStyle");
            elemStyle.AppendChild(elemStylePoly);
            XmlElement elemStylePolyColor = doc.CreateElement("color");
            elemStylePoly.AppendChild(elemStylePolyColor);
            elemStylePolyColor.AppendChild(doc.CreateTextNode("8000FF00"));

            return elemStyle;
        }

        protected XmlElement getElementStyle4(XmlDocument doc)
        {
            XmlElement elemStyle = doc.CreateElement("Style");
            elemStyle.SetAttribute("id", "msn-yzl-line0");

            XmlElement elemStyleLine = doc.CreateElement("LineStyle");
            elemStyle.AppendChild(elemStyleLine);
            XmlElement elemStyleLineWidth = doc.CreateElement("width");
            elemStyleLine.AppendChild(elemStyleLineWidth);
            elemStyleLineWidth.AppendChild(doc.CreateTextNode("1"));
            XmlElement elemStyleLineColor = doc.CreateElement("color");
            elemStyleLine.AppendChild(elemStyleLineColor);
            elemStyleLineColor.AppendChild(doc.CreateTextNode("FF0000FF"));

            return elemStyle;
        }

        protected XmlElement getElementStyle5(XmlDocument doc)
        {
            XmlElement elemStyle = doc.CreateElement("Style");
            elemStyle.SetAttribute("id", "msn-yzl-label0");

            //Style5-IconStyle
            XmlElement elemStyleIcon = doc.CreateElement("IconStyle");
            elemStyle.AppendChild(elemStyleIcon);
            XmlElement elemStyleIconIcon = doc.CreateElement("Icon");
            elemStyleIcon.AppendChild(elemStyleIconIcon);

            XmlElement elemStyleLabel = doc.CreateElement("LabelStyle");
            elemStyle.AppendChild(elemStyleLabel);
            XmlElement elemStyleLableScale = doc.CreateElement("scale");
            elemStyleLabel.AppendChild(elemStyleLableScale);
            elemStyleLableScale.AppendChild(doc.CreateTextNode("1"));
            XmlElement elemStyleLableColor = doc.CreateElement("color");
            elemStyleLabel.AppendChild(elemStyleLableColor);
            elemStyleLableColor.AppendChild(doc.CreateTextNode("FFFFFFFF"));

            return elemStyle;
        }

        protected XmlElement getElementFolder(XmlDocument doc, string folderName)
        {
            XmlElement elemFolder = doc.CreateElement("Folder");
            XmlElement elemFolderName = doc.CreateElement("name");
            elemFolder.AppendChild(elemFolderName);
            elemFolderName.AppendChild(doc.CreateTextNode(folderName));

            return elemFolder;
        } 
    }

    public class CellParamExportGoogleW : CellParamExportGoogleBase
    {
        public override GetNBCellDelegate GetNBCellInfo()
        {
            return CellManager.GetInstance().GetWNBCellInfo;
        }

        public override void Export()
        {
            WaitBox.Text = "正在导出Google-WCDMA...";
            WaitBox.ProgressPercent = 0;

            List<WCell> cellList = this.MainModel.CellManager.GetCurrentWCells();

            KMLExporter exporter = new KMLExporter();
            XmlElement root = exporter.InitAndGetParentElementNodeParamExport();
            XmlDocument doc = exporter.getRootNode();

            initGoogleStyle(ref exporter, ref root, ref doc);

            XmlElement FolderInfo = getElementFolder(doc, "信息");
            root.AppendChild(FolderInfo);
            XmlElement FolderPic = getElementFolder(doc, "图形");
            root.AppendChild(FolderPic);

            int iLoop = 1;

            foreach (WCell cell in cellList)
            {
                addFolderInfoPlaceMark(doc, FolderInfo, cell);
                addFolderPicPlaceMark(doc, FolderPic, cell);

                WaitBox.ProgressPercent = (int)(100.0 * iLoop++ / cellList.Count);
            }

            exporter.Save(this.SavePath + "//Google(WCDMA).kml");
        }
    }

    public class CellParamExportRiXunW : CellParamExportBase
    {
        public override GetNBCellDelegate GetNBCellInfo()
        {
            return CellManager.GetInstance().GetWNBCellInfo;
        }

        public override void Export()
        {
            WaitBox.Text = "正在导出日讯-WCDMA...";
            WaitBox.ProgressPercent = 0;

            List<WCell> cellList = this.MainModel.CellManager.GetCurrentWCells();
            string fileName = this.SavePath + "//日讯(WCDMA).txt";
            System.IO.FileStream fileStream = new System.IO.FileStream(fileName, System.IO.FileMode.Create, System.IO.FileAccess.Write, System.IO.FileShare.Read);
            System.IO.StreamWriter streamWriter = new System.IO.StreamWriter(fileStream, Encoding.Default);
            try
            {
                writeRiXunContent(streamWriter, cellList);
            }
            finally
            {
                streamWriter.Close();
                fileStream.Close();
            }
        }

        private void writeRiXunContent(System.IO.StreamWriter streamWriter, List<WCell> cellList)
        {
            StringBuilder sb = new StringBuilder();
            sb.Append("LAC_CI\t");
            sb.Append("RNCID\t");
            sb.Append("CI\t");
            sb.Append("Azimuth\t");
            sb.Append("ARFCN\t");
            sb.Append("SC\t");
            sb.Append("Cell_Name\t");
            sb.Append("Longitude\t");
            sb.Append("Latitude\t");
            sb.Append("SiteType\t");

            sb.Append("SiteName\t");
            sb.Append("NODEBID\t");
            sb.Append("工期\t");
            sb.Append("MGWID\t");
            sb.Append("AntHeight\t");
            sb.Append("DownTilt\t");
            sb.Append("mechTilt\t");
            sb.Append("elecTilt\t");
            sb.Append("SiteConfig\t");
            sb.Append("NodeBVersion\t");

            sb.Append("SiteState\t");
            sb.Append("CellState\t");
            sb.Append("CellHSDPAState\t");
            sb.Append("CellHSUPAState\t");
            sb.Append("操作状态\t");
            sb.Append("管理状态\t");
            sb.Append("状态说明\t");
            sb.Append("E1_NUM\t");
            sb.Append("IP_TXBW\t");
            sb.Append("IP_RXBW\t");

            sb.Append("LAC\t");
            sb.Append("RAC\t");
            sb.Append("SAC\t");
            sb.Append("Region\t");
            sb.Append("date\t");
            sb.Append("邻区\t");

            streamWriter.WriteLine(sb.ToString());
            sb.Remove(0, sb.Length);
            int iLoop = 1;

            foreach (WCell wcell in cellList)
            {
                sb.Append(wcell.LAC + "_" + wcell.CI + "\t");
                
                string siteName = "";
                int siteID = 0;
                if (wcell.BelongNodeBs.Count > 0)
                {
                    int index = wcell.BelongNodeBs.Count;
                    siteName = wcell.BelongNodeBs[index - 1].Name;
                    siteID = wcell.BelongNodeBs[index - 1].ID;
                }
                sb.Append(wcell.CI + "\t");
                sb.Append(wcell.Direction + "\t");
                sb.Append(wcell.UARFCN + "\t");
                sb.Append("\t");    //SC
                sb.Append(wcell.Name + "\t");
                sb.Append(wcell.Longitude + "\t");
                sb.Append(wcell.Latitude + "\t");
                sb.Append("\t");    //SiteType
                sb.Append(siteName + "\t");
                sb.Append(siteID + "\t");
                sb.Append("\t");    //工期
                sb.Append("\t");    //MGWID
                sb.Append(wcell.Altitude < 0 ? "0" : wcell.Altitude.ToString() + "\t");
                sb.Append(wcell.Downword < 0 ? "0" : wcell.Downword.ToString() + "\t");
                sb.Append("\t");    //mechTilt
                sb.Append("\t");    //elecTilt
                sb.Append("\t");    //SiteConfig
                sb.Append("\t");    //NodeBVersion
                sb.Append("\t");    //SiteState
                sb.Append("\t");    //CellState
                sb.Append("\t");    //CellHSDPAState
                sb.Append("\t");    //CellHSUPAState
                sb.Append("\t");    //操作状态
                sb.Append("\t");    //管理状态
                sb.Append("\t");    //状态说明
                sb.Append("\t");    //E1_NUM
                sb.Append("\t");    //IP_TXBW
                sb.Append("\t");    //IP_RXBW
                sb.Append(wcell.LAC + "\t");
                sb.Append("\t");    //RAC
                sb.Append("\t");    //SAC
                sb.Append("\t");    //Region
                sb.Append("\t");    //date
                sb.Append("");
                StringBuilder sbNCell = new StringBuilder();
                foreach (Cell nbcell in wcell.NeighbourGCells)
                {
                    sbNCell.Append(nbcell.LAC + "_" + nbcell.CI + ",");
                }
                foreach (WCell nbcell in wcell.NeighbourWCells)
                {
                    sbNCell.Append(nbcell.LAC + "_" + nbcell.CI + ",");
                }
                sbNCell = sbNCell.Length > 0 ? sbNCell.Remove(sbNCell.Length - 1, 1) : sbNCell;
                sb.Append(sbNCell.ToString());
                streamWriter.WriteLine(sb.ToString());
                sb.Remove(0, sb.Length);
                WaitBox.ProgressPercent = (int)(100.0 * iLoop++ / cellList.Count);
            }
        }
    }

    public class CellParamExportActixW : CellParamExportBase
    {
        public override GetNBCellDelegate GetNBCellInfo()
        {
            return CellManager.GetInstance().GetWNBCellInfo;
        }

        public override void Export()
        {
            WaitBox.Text = "正在导出Actix-WCDMA...";
            WaitBox.ProgressPercent = 0;
            List<WCell> cellList = this.MainModel.CellManager.GetCurrentWCells();
            string fileName = this.SavePath + "//Actix(WCDMA).txt";
            System.IO.FileStream fileStream = new System.IO.FileStream(fileName, System.IO.FileMode.Create, System.IO.FileAccess.Write, System.IO.FileShare.Read);
            System.IO.StreamWriter streamWriter = new System.IO.StreamWriter(fileStream, Encoding.Default);
            try
            {
                writeActixContent(streamWriter, cellList);
            }
            finally
            {
                streamWriter.Close();
                fileStream.Close();
            }
        }

        private void writeActixContent(System.IO.StreamWriter streamWriter, List<WCell> cellList)
        {
            StringBuilder sb = new StringBuilder();
            sb.Append("; #NetworkData - datafile");
            streamWriter.WriteLine(sb.ToString());
            sb.Remove(0, sb.Length);

            sb.Append("类型\t");
            sb.Append("小区名称\t");
            sb.Append("LAC\t");
            sb.Append("CI\t");
            sb.Append("UARFCN\t");
            sb.Append("PSC\t");
            sb.Append("Longitude\t");
            sb.Append("Latitude\t");
            sb.Append("方向角\t");
            sb.Append("下倾角\t");
            sb.Append("挂高\t");
            sb.Append("邻区\t");

            streamWriter.WriteLine(sb.ToString());
            sb.Remove(0, sb.Length);
            int iLoop = 1;

            foreach (WCell wcell in cellList)
            {
                sb.Append("WCDMA_Cell" + "\t");
                sb.Append(wcell.Name + "\t");
                sb.Append(wcell.LAC + "\t");
                sb.Append(wcell.CI + "\t");
                sb.Append(wcell.UARFCN + "\t");
                sb.Append(wcell.PSC + "\t");
                sb.Append(wcell.Longitude + "\t");
                sb.Append(wcell.Latitude + "\t");
                sb.Append(wcell.Direction + "\t");
                sb.Append(wcell.Downword < 0 ? "0" : wcell.Downword.ToString() + "\t");
                sb.Append(wcell.Altitude < 0 ? "0" : wcell.Altitude.ToString() + "\t");
                sb.Append("");
                StringBuilder sbNCell = new StringBuilder();
                foreach (Cell nbcell in wcell.NeighbourGCells)
                {
                    sbNCell.Append(nbcell.LAC + "_" + nbcell.CI + ",");
                }
                foreach (WCell nbcell in wcell.NeighbourWCells)
                {
                    sbNCell.Append(nbcell.LAC + "_" + nbcell.CI + ",");
                }
                sbNCell = sbNCell.Length > 0 ? sbNCell.Remove(sbNCell.Length - 1, 1) : sbNCell;
                sb.Append(sbNCell.ToString());
                streamWriter.WriteLine(sb.ToString());
                sb.Remove(0, sb.Length);
                WaitBox.ProgressPercent = (int)(100.0 * iLoop++ / cellList.Count);
            }
        }
    }

    public class CellParamExportDingLiW : CellParamExportBase
    {
        public override GetNBCellDelegate GetNBCellInfo()
        {
            return CellManager.GetInstance().GetWNBCellInfo;
        }

        public override void Export()
        {
            WaitBox.Text = "正在导出DingLi-WCDMA...";
            WaitBox.ProgressPercent = 0;

            List<NPOIRow> rows = new List<NPOIRow>();
            List<WCell> cellList = this.MainModel.CellManager.GetCurrentWCells();
            NPOIRow row = new NPOIRow();
            row.AddCellValue("SITE Name");
            row.AddCellValue("Longitude");
            row.AddCellValue("Latitude");
            row.AddCellValue("Cell Name");
            row.AddCellValue("PSC");
            row.AddCellValue("CELL ID");
            row.AddCellValue("LAC");
            row.AddCellValue("RA");
            row.AddCellValue("URA");
            row.AddCellValue("ANTENNA HEIGHT");
            row.AddCellValue("AZIMUTH");
            row.AddCellValue("UARFCN");
            row.AddCellValue("邻区");
            rows.Add(row);

            int iLoop = 1;
            foreach (WCell wcell in cellList)
            {
                row = new NPOIRow();
                if (wcell.BelongNodeBs.Count == 0)
                {
                    continue;
                }

                int index = wcell.BelongNodeBs.Count;
                string siteName = wcell.BelongNodeBs[index - 1].Name;  //最近的一个
                row.AddCellValue(siteName);
                row.AddCellValue(wcell.Longitude);
                row.AddCellValue(wcell.Latitude);
                row.AddCellValue(wcell.Name);
                row.AddCellValue(wcell.PSC);
                row.AddCellValue(wcell.CI);
                row.AddCellValue(wcell.LAC);
                row.AddCellValue("");//RA 
                row.AddCellValue("");//URA
                row.AddCellValue(wcell.Altitude < 0 ? "0" : wcell.Altitude.ToString());
                row.AddCellValue(wcell.Direction);
                row.AddCellValue(wcell.UARFCN);
                StringBuilder sbNCell = new StringBuilder();
                foreach (Cell nbcell in wcell.NeighbourGCells)
                {
                    sbNCell.Append(nbcell.LAC + "_" + nbcell.CI + ",");
                }
                foreach (WCell nbcell in wcell.NeighbourWCells)
                {
                    sbNCell.Append(nbcell.LAC + "_" + nbcell.CI + ",");
                }
                sbNCell = sbNCell.Length > 0 ? sbNCell.Remove(sbNCell.Length - 1, 1) : sbNCell;
                row.AddCellValue(sbNCell.ToString());
                rows.Add(row);

                WaitBox.ProgressPercent = (int)(100.0 * iLoop++ / cellList.Count);
            }
            ExcelNPOIManager.AutoExportExcel(rows, this.SavePath + "//鼎利(WCDMA).xlsx");
        }
    }

    public class CellParamExportGoogleGsm : CellParamExportGoogleBase
    {
        public override GetNBCellDelegate GetNBCellInfo()
        {
            return CellManager.GetInstance().GetGSMNBCellInfo;
        }

        public override void Export()
        {
            WaitBox.Text = "正在导出Google-GSM...";
            WaitBox.ProgressPercent = 0;

            List<Cell> cellList = this.MainModel.CellManager.GetCurrentCells();

            KMLExporter exporter = new KMLExporter();
            XmlElement root = exporter.InitAndGetParentElementNodeParamExport();
            XmlDocument doc = exporter.getRootNode();

            initGoogleStyle(ref exporter, ref root, ref doc);

            XmlElement FolderInfo = getElementFolder(doc, "信息");
            root.AppendChild(FolderInfo);
            XmlElement FolderPic = getElementFolder(doc, "图形");
            root.AppendChild(FolderPic);

            int iLoop = 1;

            foreach (Cell cell in cellList)
            {
                addFolderInfoPlaceMark(doc, FolderInfo, cell);
                addFolderPicPlaceMark(doc, FolderPic, cell);

                WaitBox.ProgressPercent = (int)(100.0 * iLoop++ / cellList.Count);
            }

            exporter.Save(this.SavePath + "//Google(GSM).kml");
        }
    }

    public class CellParamExportTemsGsm : CellParamExportBase
    {
        public override GetNBCellDelegate GetNBCellInfo()
        {
            return CellManager.GetInstance().GetGSMNBCellInfo;
        }

        public override void Export()
        {
            WaitBox.Text = "正在导出TEMS-GSM...";
            WaitBox.ProgressPercent = 0;

            List<Cell> cellList = this.MainModel.CellManager.GetCurrentCells();

            string fileName = this.SavePath + "//TEMS(GSM).txt";
            System.IO.FileStream fileStream = new System.IO.FileStream(fileName, System.IO.FileMode.Create, System.IO.FileAccess.Write, System.IO.FileShare.Read);
            System.IO.StreamWriter streamWriter = new System.IO.StreamWriter(fileStream, Encoding.Default);
            try
            {
                writeTEMSContent(streamWriter, cellList);
            }
            finally
            {
                streamWriter.Close();
                fileStream.Close();
            }
        }

        private void writeTEMSContent(System.IO.StreamWriter streamWriter, List<Cell> cellList)
        {
            StringBuilder sb = new StringBuilder();
            sb.Append("55 TEMS_-_Cell_names");
            streamWriter.WriteLine(sb.ToString());
            sb.Remove(0, sb.Length);

            sb.Append("Cell\t");
            sb.Append("ARFCN\t");
            sb.Append("BSIC\t");
            sb.Append("UARFCN\t");
            sb.Append("SC\t");
            sb.Append("Lat\t");
            sb.Append("Lon\t");
            sb.Append("MCC\t");
            sb.Append("MNC\t");
            sb.Append("LAC\t");
            sb.Append("CI\t");
            sb.Append("LAC_N_1\t");
            sb.Append("CI_N_1\t");
            sb.Append("RNC-ID\t");
            sb.Append("C-ID\t");
            sb.Append("RNC-ID_N_1\t");
            sb.Append("C-ID_N_1\t");
            sb.Append("ANT_DIRECTION\t");
            sb.Append("ANT_BEAM_WIDTH\t");
            sb.Append("TCH_ARFCN_1\t");
            sb.Append("TCH_ARFCN_2\t");
            sb.Append("TCH_ARFCN_3\t");
            sb.Append("ANT_TYPE\t");
            sb.Append("ANT_HEIGHT\t");
            sb.Append("ANT_TILT\t");
            sb.Append("CELL_TYPE\t");
            sb.Append("NODE_B\t");
            sb.Append("NODE_B_STATUS\t");
            sb.Append("RA\t");
            sb.Append("URA\t");
            sb.Append("TIME_OFFSET\t");
            sb.Append("CPICH_POWER\t");
            sb.Append("MAX_TX_POWER\t");
            sb.Append("邻区\t");
            streamWriter.WriteLine(sb.ToString());
            sb.Remove(0, sb.Length);
            int iLoop = 1;
            foreach (Cell cell in cellList)
            {
                sb.Append(cell.Name + "\t");
                sb.Append(cell.BCCH + "\t");
                sb.Append(cell.BSIC + "\t");
                sb.Append("" + "\t");
                sb.Append("" + "\t");
                sb.Append(cell.Latitude + "\t");
                sb.Append(cell.Longitude + "\t");
                sb.Append("460" + "\t");
                sb.Append("1" + "\t");
                sb.Append(cell.LAC + "\t");
                sb.Append(cell.CI + "\t");
                sb.Append("" + "\t");
                sb.Append("" + "\t");
                sb.Append("" + "\t");
                sb.Append("" + "\t");
                sb.Append("" + "\t");
                sb.Append("" + "\t");
                sb.Append(cell.Direction + "\t");
                sb.Append("60" + "\t");
                sb.Append("" + "\t");
                sb.Append("" + "\t");
                sb.Append("" + "\t");
                sb.Append("" + "\t");
                sb.Append("" + "\t");
                sb.Append("" + "\t");
                sb.Append("" + "\t");
                sb.Append("" + "\t");
                sb.Append("" + "\t");
                sb.Append("" + "\t");
                sb.Append("" + "\t");
                sb.Append("" + "\t");
                sb.Append("" + "\t");
                sb.Append("" + "\t");
                StringBuilder sbNCell = new StringBuilder();
                foreach (Cell nbCell in cell.GetNeighbourCells())
                {
                    sbNCell.Append(nbCell.LAC + "_" + nbCell.CI + ",");
                }
                sbNCell = sbNCell.Length > 0 ? sbNCell.Remove(sbNCell.Length - 1, 1) : sbNCell;
                sb.Append(sbNCell.ToString());
                streamWriter.WriteLine(sb.ToString());
                sb.Remove(0, sb.Length);
                WaitBox.ProgressPercent = (int)(100.0 * iLoop++ / cellList.Count);
            }
        }
    }

    public class CellParamExportNastarGsm : CellParamExportBase
    {
        public override GetNBCellDelegate GetNBCellInfo()
        {
            return CellManager.GetInstance().GetGSMNBCellInfo;
        }

        public override void Export()
        {
            WaitBox.Text = "正在导出Nastar-GSM...";
            WaitBox.ProgressPercent = 0;

            List<NPOIRow> rows = new List<NPOIRow>();
            List<Cell> cellList = this.MainModel.CellManager.GetCurrentCells();

            NPOIRow row = new NPOIRow();
            row.AddCellValue("BSC名称");
            row.AddCellValue("基站索引");
            row.AddCellValue("基站名称");
            row.AddCellValue("小区索引");
            row.AddCellValue("小区名称");
            row.AddCellValue("基站类型");
            row.AddCellValue("小区LAC");
            row.AddCellValue("小区CI");
            row.AddCellValue("BCCH频点");
            row.AddCellValue("BSIC八进制");
            row.AddCellValue("小区网络色码");
            row.AddCellValue("小区基站色码");
            row.AddCellValue("跳频模式");
            row.AddCellValue("激活状态");
            row.AddCellValue("基站2M电路数");
            row.AddCellValue("传输复用比");
            row.AddCellValue("物理单板数");
            row.AddCellValue("逻辑载频数");
            row.AddCellValue("TCH");
            row.AddCellValue("经度");
            row.AddCellValue("纬度");
            row.AddCellValue("方位角");
            row.AddCellValue("机械下倾角");
            row.AddCellValue("站高");
            row.AddCellValue("邻区");
            rows.Add(row);

            int iLoop = 1;

            foreach (Cell cell in cellList)
            {
                row = new NPOIRow();
                row.AddCellValue(cell.BelongBSCName);

                string siteName = cell.BelongBTS.Name;  //最近的一个
                int siteID = cell.BelongBTS.ID;
                row.AddCellValue(siteName);
                row.AddCellValue(siteID);
                row.AddCellValue(cell.ID);
                row.AddCellValue(cell.Name);
                row.AddCellValue(getCellBandType(cell.BelongBTS.BandType, cell.Type));
                row.AddCellValue(cell.LAC);
                row.AddCellValue(cell.CI);
                row.AddCellValue(cell.BCCH);
                row.AddCellValue(cell.BSIC);
                string strBsic = cell.BSIC.ToString();
                row.AddCellValue(strBsic.Length > 0 ? strBsic.Substring(0, 1) : "");
                row.AddCellValue(strBsic.Length > 1 ? strBsic.Substring(1, 1) : "");
                row.AddCellValue(cell.HOP ? "跳频" : "不跳频");
                row.AddCellValue("");//激活
                row.AddCellValue("");//基站2M电路数
                row.AddCellValue("");//传输复用比
                row.AddCellValue("");//物理单板数
                row.AddCellValue(cell.DCH.Count);
                row.AddCellValue(cell.TCHDescription);
                row.AddCellValue(cell.Longitude);
                row.AddCellValue(cell.Latitude);
                row.AddCellValue(cell.Direction);
                row.AddCellValue(cell.Downword < 0 ? "0" : cell.Downword.ToString());
                row.AddCellValue(cell.Altitude < 0 ? "0" : cell.Altitude.ToString());
                StringBuilder sbNCell = new StringBuilder();
                foreach (Cell nbCell in cell.GetNeighbourCells())
                {
                    sbNCell.Append(nbCell.LAC + "_" + nbCell.CI + ",");
                }
                sbNCell = sbNCell.Length > 0 ? sbNCell.Remove(sbNCell.Length - 1, 1) : sbNCell;
                row.AddCellValue(sbNCell.ToString());
                rows.Add(row);

                WaitBox.ProgressPercent = (int)(100.0 * iLoop++ / cellList.Count);
            }
            ExcelNPOIManager.AutoExportExcel(rows, this.SavePath + "//Nastar(GSM).xlsx");
        }

        protected string getCellBandType(BTSBandType bandType, BTSType cellType)
        {
            string result = "";

            switch (cellType)
            {
                case BTSType.Outdoor:
                case BTSType.Upper:
                case BTSType.Other:
                    result = "宏蜂窝";
                    break;
                case BTSType.Indoor:
                    result = "微蜂窝";
                    break;
                default:
                    break;
            }

            switch (bandType)
            {
                case BTSBandType.GSM900:
                    result += "900";
                    break;
                case BTSBandType.DSC1800:
                    result += "1800";
                    break;
                case BTSBandType.CoSite:
                    result += "900/1800共址";
                    break;
                default:
                    result += "";
                    break;
            }

            return result;
        }
    }

    public class CellParamExportDingLiGsm : CellParamExportBase
    {
        public override GetNBCellDelegate GetNBCellInfo()
        {
            return CellManager.GetInstance().GetGSMNBCellInfo;
        }

        public override void Export()
        {
            WaitBox.Text = "正在导出DingLi-GSM...";
            WaitBox.ProgressPercent = 0;

            List<NPOIRow> rows = new List<NPOIRow>();
            List<Cell> cellList = this.MainModel.CellManager.GetCurrentCells();

            int iLoop = 0;
            rows.Add(GetTitleRow());
            foreach (Cell cell in cellList)
            {
                WaitBox.ProgressPercent = ++iLoop * 100 / cellList.Count;
                rows.Add(GetContentRow(cell));
            }
            ExcelNPOIManager.AutoExportExcel(rows, this.SavePath + "//DingLi(GSM).xlsx");
        }

        private NPOIRow GetTitleRow()
        {
            NPOIRow row = new NPOIRow();
            row.AddCellValue("SITENAME");
            row.AddCellValue("LONGITUDE");
            row.AddCellValue("LATITUDE");
            row.AddCellValue("CELLNAME");
            row.AddCellValue("BCCH");
            row.AddCellValue("BSIC");
            row.AddCellValue("CELL ID");
            row.AddCellValue("LAC");
            row.AddCellValue("RA");
            row.AddCellValue("NEIGHBOR LIST");
            row.AddCellValue("MCC");
            row.AddCellValue("MNC");
            row.AddCellValue("OMCR");
            row.AddCellValue("MSC");
            row.AddCellValue("BSC");
            row.AddCellValue("ANTENNA HEIGHT");
            row.AddCellValue("AZIMUTH");
            row.AddCellValue("TILT");
            row.AddCellValue("POWER");
            row.AddCellValue("GROUND HEIGHT");
            return row;
        }

        private NPOIRow GetContentRow(Cell cell)
        {
            NPOIRow row = new NPOIRow();
            row.AddCellValue(cell.BelongBSCName);
            row.AddCellValue(cell.Longitude);
            row.AddCellValue(cell.Latitude);
            row.AddCellValue(cell.Name);
            row.AddCellValue(cell.BCCH);
            row.AddCellValue(cell.BSIC);
            row.AddCellValue(cell.CI); // modify to CI, not ID
            row.AddCellValue(cell.LAC);
            row.AddCellValue(""); // RA ?
            row.AddCellValue(GetNBList(cell));
            row.AddCellValue(""); // MCC
            row.AddCellValue(""); // MNC
            row.AddCellValue(""); // OMCR
            row.AddCellValue(""); // MSC
            row.AddCellValue(""); // BSC
            row.AddCellValue(cell.Altitude < 0 ? "0" : cell.Altitude.ToString());
            row.AddCellValue(cell.Direction);
            row.AddCellValue(cell.Downword < 0 ? "0" : cell.Downword.ToString());
            row.AddCellValue(""); // POWER
            row.AddCellValue("");
            return row;
        }

        private string GetNBList(Cell cell)
        {
            StringBuilder sb = new StringBuilder();
            foreach (Cell nc in cell.GetNeighbourCells())
            {
                sb.Append(nc.LAC + "_" + nc.CI + ",");
            }
            return sb.Length > 0 ? sb.Remove(sb.Length - 1, 1).ToString() : sb.ToString();
        }
    }

    public class CellParamExportDingLiTD : CellParamExportBase
    {
        public override GetNBCellDelegate GetNBCellInfo()
        {
            return CellManager.GetInstance().GetTDNBCellInfo;
        }

        public override void Export()
        {
            WaitBox.Text = "正在导出DingLi-TD...";
            WaitBox.ProgressPercent = 0;

            List<NPOIRow> rows = new List<NPOIRow>();
            List<TDCell> cellList = this.MainModel.CellManager.GetCurrentTDCells();

            int iLoop = 0;
            rows.Add(GetTitleRow());
            foreach (TDCell cell in cellList)
            {
                WaitBox.ProgressPercent = ++iLoop * 100 / cellList.Count;
                rows.Add(GetContentRow(cell));
            }
            ExcelNPOIManager.AutoExportExcel(rows, this.SavePath + "//DingLi(TD).xlsx");
        }

        private NPOIRow GetTitleRow()
        {
            NPOIRow row = new NPOIRow();
            row.AddCellValue("SITENAME");
            row.AddCellValue("LONGITUDE");
            row.AddCellValue("LATITUDE");
            row.AddCellValue("CELLNAME");
            row.AddCellValue("UARFCN");
            row.AddCellValue("CPI");
            row.AddCellValue("CELL ID");
            row.AddCellValue("LAC");
            row.AddCellValue("RA");
            row.AddCellValue("NEIGHBOR LIST");
            row.AddCellValue("MCC");
            row.AddCellValue("MNC");
            row.AddCellValue("OMCR");
            row.AddCellValue("MSC");
            row.AddCellValue("BSC");
            row.AddCellValue("ANTENNA HEIGHT");
            row.AddCellValue("AZIMUTH");
            row.AddCellValue("TILT");
            row.AddCellValue("POWER");
            row.AddCellValue("GROUND HEIGHT");
            return row;
        }

        private NPOIRow GetContentRow(TDCell cell)
        {
            NPOIRow row = new NPOIRow();
            row.AddCellValue(cell.NodeBName);
            row.AddCellValue(cell.Longitude);
            row.AddCellValue(cell.Latitude);
            row.AddCellValue(cell.Name);
            row.AddCellValue(cell.FREQ);
            row.AddCellValue(cell.CPI);
            row.AddCellValue(cell.CI); // modify to CI, not ID
            row.AddCellValue(cell.LAC);
            row.AddCellValue(""); // RA ?
            row.AddCellValue(GetNBList(cell));
            row.AddCellValue(""); // MCC
            row.AddCellValue(""); // MNC
            row.AddCellValue(""); // OMCR
            row.AddCellValue(""); // MSC
            row.AddCellValue(""); // BSC
            row.AddCellValue(cell.Altitude < 0 ? "0" : cell.Altitude.ToString());
            row.AddCellValue(cell.Direction);
            row.AddCellValue(cell.Downword < 0 ? "0" : cell.Downword.ToString());
            row.AddCellValue(""); // POWER
            row.AddCellValue("");
            return row;
        }

        private string GetNBList(TDCell cell)
        {
            StringBuilder sb = new StringBuilder();
            foreach (Cell nc in cell.NeighbourCells)
            {
                sb.Append(nc.LAC + "_" + nc.CI + ",");
            }
            foreach (TDCell nc in cell.NeighbourTDCells)
            {
                sb.Append(nc.LAC + "_" + nc.CI + ",");
            }
            return sb.Length > 0 ? sb.Remove(sb.Length - 1, 1).ToString() : sb.ToString();
        }
    }
}
