﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTLTEHighCoverageRoadSetForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.labelControl9 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl5 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.spinEditRxlevMin = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl7 = new DevExpress.XtraEditors.LabelControl();
            this.spinEditCoverage = new DevExpress.XtraEditors.SpinEdit();
            this.spinEditSampleDistance = new DevExpress.XtraEditors.SpinEdit();
            this.spinEditRoadDistance = new DevExpress.XtraEditors.SpinEdit();
            this.spinEditMaxDiff = new DevExpress.XtraEditors.SpinEdit();
            this.btnCancel = new DevExpress.XtraEditors.SimpleButton();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.btnOK = new DevExpress.XtraEditors.SimpleButton();
            this.labelControl8 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl6 = new DevExpress.XtraEditors.LabelControl();
            this.groupBoxIndoor = new System.Windows.Forms.GroupBox();
            this.chkShieldProblem = new System.Windows.Forms.CheckBox();
            this.label5 = new System.Windows.Forms.Label();
            this.numRadiusFactor = new DevExpress.XtraEditors.SpinEdit();
            this.label2 = new System.Windows.Forms.Label();
            this.numNearestBtsCount = new DevExpress.XtraEditors.SpinEdit();
            this.label1 = new System.Windows.Forms.Label();
            this.chkCellCheck = new System.Windows.Forms.CheckBox();
            this.groupBoxClose = new System.Windows.Forms.GroupBox();
            this.txtFileName = new System.Windows.Forms.TextBox();
            this.btnSelect = new System.Windows.Forms.Button();
            this.radioMulti = new System.Windows.Forms.RadioButton();
            this.txtEci = new System.Windows.Forms.TextBox();
            this.label4 = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.txtTac = new System.Windows.Forms.TextBox();
            this.radioSingle = new System.Windows.Forms.RadioButton();
            this.chkCloseSimu = new System.Windows.Forms.CheckBox();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.absValue = new DevExpress.XtraEditors.SpinEdit();
            this.chbRelative = new System.Windows.Forms.CheckBox();
            this.chbAbslute = new System.Windows.Forms.CheckBox();
            this.labelControl10 = new DevExpress.XtraEditors.LabelControl();
            this.absCoverate = new DevExpress.XtraEditors.SpinEdit();
            this.label16 = new System.Windows.Forms.Label();
            this.label15 = new System.Windows.Forms.Label();
            this.spinEditRoadPercent = new DevExpress.XtraEditors.SpinEdit();
            this.groupBoxConstancy = new System.Windows.Forms.GroupBox();
            this.groupBoxIndex = new System.Windows.Forms.GroupBox();
            this.chkTwoEarfcn = new DevExpress.XtraEditors.CheckEdit();
            this.groupBoxRemove = new System.Windows.Forms.GroupBox();
            this.freqBandControl1 = new MasterCom.RAMS.ZTFunc.FreqBandControl();
            this.toolStripDropDownFreq = new System.Windows.Forms.ToolStripDropDown();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditRxlevMin.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditCoverage.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditSampleDistance.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditRoadDistance.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditMaxDiff.Properties)).BeginInit();
            this.groupBoxIndoor.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numRadiusFactor.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numNearestBtsCount.Properties)).BeginInit();
            this.groupBoxClose.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.absValue.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.absCoverate.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditRoadPercent.Properties)).BeginInit();
            this.groupBoxConstancy.SuspendLayout();
            this.groupBoxIndex.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chkTwoEarfcn.Properties)).BeginInit();
            this.groupBoxRemove.SuspendLayout();
            this.SuspendLayout();
            // 
            // labelControl9
            // 
            this.labelControl9.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl9.Appearance.Options.UseFont = true;
            this.labelControl9.Location = new System.Drawing.Point(528, 21);
            this.labelControl9.Name = "labelControl9";
            this.labelControl9.Size = new System.Drawing.Size(12, 12);
            this.labelControl9.TabIndex = 26;
            this.labelControl9.Text = "米";
            // 
            // labelControl5
            // 
            this.labelControl5.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl5.Appearance.Options.UseFont = true;
            this.labelControl5.Location = new System.Drawing.Point(141, 100);
            this.labelControl5.Name = "labelControl5";
            this.labelControl5.Size = new System.Drawing.Size(18, 12);
            this.labelControl5.TabIndex = 27;
            this.labelControl5.Text = "dBm";
            // 
            // labelControl4
            // 
            this.labelControl4.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl4.Appearance.Options.UseFont = true;
            this.labelControl4.Location = new System.Drawing.Point(13, 100);
            this.labelControl4.Name = "labelControl4";
            this.labelControl4.Size = new System.Drawing.Size(60, 12);
            this.labelControl4.TabIndex = 21;
            this.labelControl4.Text = "最强信号 >";
            // 
            // spinEditRxlevMin
            // 
            this.spinEditRxlevMin.EditValue = new decimal(new int[] {
            105,
            0,
            0,
            -2147483648});
            this.spinEditRxlevMin.Location = new System.Drawing.Point(76, 96);
            this.spinEditRxlevMin.Name = "spinEditRxlevMin";
            this.spinEditRxlevMin.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditRxlevMin.Properties.Appearance.Options.UseFont = true;
            this.spinEditRxlevMin.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditRxlevMin.Properties.IsFloatValue = false;
            this.spinEditRxlevMin.Properties.Mask.EditMask = "N00";
            this.spinEditRxlevMin.Properties.MaxValue = new decimal(new int[] {
            10,
            0,
            0,
            -2147483648});
            this.spinEditRxlevMin.Properties.MinValue = new decimal(new int[] {
            120,
            0,
            0,
            -2147483648});
            this.spinEditRxlevMin.Size = new System.Drawing.Size(62, 20);
            this.spinEditRxlevMin.TabIndex = 14;
            // 
            // labelControl7
            // 
            this.labelControl7.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl7.Appearance.Options.UseFont = true;
            this.labelControl7.Location = new System.Drawing.Point(285, 21);
            this.labelControl7.Name = "labelControl7";
            this.labelControl7.Size = new System.Drawing.Size(12, 12);
            this.labelControl7.TabIndex = 28;
            this.labelControl7.Text = "米";
            // 
            // spinEditCoverage
            // 
            this.spinEditCoverage.EditValue = new decimal(new int[] {
            4,
            0,
            0,
            0});
            this.spinEditCoverage.Location = new System.Drawing.Point(439, 21);
            this.spinEditCoverage.Name = "spinEditCoverage";
            this.spinEditCoverage.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditCoverage.Properties.Appearance.Options.UseFont = true;
            this.spinEditCoverage.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditCoverage.Properties.IsFloatValue = false;
            this.spinEditCoverage.Properties.Mask.AutoComplete = DevExpress.XtraEditors.Mask.AutoCompleteType.None;
            this.spinEditCoverage.Properties.Mask.EditMask = "N00";
            this.spinEditCoverage.Properties.MaxValue = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.spinEditCoverage.Properties.MinValue = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.spinEditCoverage.Size = new System.Drawing.Size(82, 20);
            this.spinEditCoverage.TabIndex = 15;
            // 
            // spinEditSampleDistance
            // 
            this.spinEditSampleDistance.EditValue = new decimal(new int[] {
            50,
            0,
            0,
            0});
            this.spinEditSampleDistance.Location = new System.Drawing.Point(439, 17);
            this.spinEditSampleDistance.Name = "spinEditSampleDistance";
            this.spinEditSampleDistance.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditSampleDistance.Properties.Appearance.Options.UseFont = true;
            this.spinEditSampleDistance.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditSampleDistance.Properties.IsFloatValue = false;
            this.spinEditSampleDistance.Properties.Mask.AutoComplete = DevExpress.XtraEditors.Mask.AutoCompleteType.None;
            this.spinEditSampleDistance.Properties.Mask.EditMask = "N00";
            this.spinEditSampleDistance.Properties.MaxValue = new decimal(new int[] {
            50,
            0,
            0,
            0});
            this.spinEditSampleDistance.Properties.MinValue = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.spinEditSampleDistance.Size = new System.Drawing.Size(82, 20);
            this.spinEditSampleDistance.TabIndex = 22;
            // 
            // spinEditRoadDistance
            // 
            this.spinEditRoadDistance.EditValue = new decimal(new int[] {
            50,
            0,
            0,
            0});
            this.spinEditRoadDistance.Location = new System.Drawing.Point(197, 17);
            this.spinEditRoadDistance.Name = "spinEditRoadDistance";
            this.spinEditRoadDistance.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditRoadDistance.Properties.Appearance.Options.UseFont = true;
            this.spinEditRoadDistance.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditRoadDistance.Properties.Increment = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.spinEditRoadDistance.Properties.IsFloatValue = false;
            this.spinEditRoadDistance.Properties.Mask.AutoComplete = DevExpress.XtraEditors.Mask.AutoCompleteType.None;
            this.spinEditRoadDistance.Properties.Mask.EditMask = "N00";
            this.spinEditRoadDistance.Properties.MaxValue = new decimal(new int[] {
            100000,
            0,
            0,
            0});
            this.spinEditRoadDistance.Size = new System.Drawing.Size(82, 20);
            this.spinEditRoadDistance.TabIndex = 17;
            // 
            // spinEditMaxDiff
            // 
            this.spinEditMaxDiff.EditValue = new decimal(new int[] {
            6,
            0,
            0,
            0});
            this.spinEditMaxDiff.Location = new System.Drawing.Point(217, 21);
            this.spinEditMaxDiff.Name = "spinEditMaxDiff";
            this.spinEditMaxDiff.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditMaxDiff.Properties.Appearance.Options.UseFont = true;
            this.spinEditMaxDiff.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditMaxDiff.Properties.IsFloatValue = false;
            this.spinEditMaxDiff.Properties.Mask.AutoComplete = DevExpress.XtraEditors.Mask.AutoCompleteType.None;
            this.spinEditMaxDiff.Properties.Mask.EditMask = "N00";
            this.spinEditMaxDiff.Properties.MaxValue = new decimal(new int[] {
            30,
            0,
            0,
            0});
            this.spinEditMaxDiff.Size = new System.Drawing.Size(82, 20);
            this.spinEditMaxDiff.TabIndex = 13;
            // 
            // btnCancel
            // 
            this.btnCancel.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnCancel.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnCancel.Appearance.Options.UseFont = true;
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnCancel.Location = new System.Drawing.Point(506, 453);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 25;
            this.btnCancel.Text = "取消";
            // 
            // labelControl1
            // 
            this.labelControl1.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl1.Appearance.Options.UseFont = true;
            this.labelControl1.Location = new System.Drawing.Point(305, 25);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(12, 12);
            this.labelControl1.TabIndex = 24;
            this.labelControl1.Text = "dB";
            // 
            // btnOK
            // 
            this.btnOK.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnOK.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnOK.Appearance.Options.UseFont = true;
            this.btnOK.Location = new System.Drawing.Point(405, 453);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 23;
            this.btnOK.Text = "确定";
            // 
            // labelControl8
            // 
            this.labelControl8.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl8.Appearance.Options.UseFont = true;
            this.labelControl8.Location = new System.Drawing.Point(322, 21);
            this.labelControl8.Name = "labelControl8";
            this.labelControl8.Size = new System.Drawing.Size(102, 12);
            this.labelControl8.TabIndex = 16;
            this.labelControl8.Text = "相邻采样点距离 ≤";
            // 
            // labelControl3
            // 
            this.labelControl3.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl3.Appearance.Options.UseFont = true;
            this.labelControl3.Location = new System.Drawing.Point(345, 25);
            this.labelControl3.Name = "labelControl3";
            this.labelControl3.Size = new System.Drawing.Size(78, 12);
            this.labelControl3.TabIndex = 18;
            this.labelControl3.Text = "重叠覆盖度 ≥";
            // 
            // labelControl6
            // 
            this.labelControl6.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl6.Appearance.Options.UseFont = true;
            this.labelControl6.Location = new System.Drawing.Point(115, 20);
            this.labelControl6.Name = "labelControl6";
            this.labelControl6.Size = new System.Drawing.Size(66, 12);
            this.labelControl6.TabIndex = 19;
            this.labelControl6.Text = "持续距离 ≥";
            // 
            // groupBoxIndoor
            // 
            this.groupBoxIndoor.Controls.Add(this.chkShieldProblem);
            this.groupBoxIndoor.Controls.Add(this.label5);
            this.groupBoxIndoor.Controls.Add(this.numRadiusFactor);
            this.groupBoxIndoor.Controls.Add(this.label2);
            this.groupBoxIndoor.Controls.Add(this.numNearestBtsCount);
            this.groupBoxIndoor.Controls.Add(this.label1);
            this.groupBoxIndoor.Controls.Add(this.chkCellCheck);
            this.groupBoxIndoor.Location = new System.Drawing.Point(12, 195);
            this.groupBoxIndoor.Name = "groupBoxIndoor";
            this.groupBoxIndoor.Size = new System.Drawing.Size(211, 147);
            this.groupBoxIndoor.TabIndex = 29;
            this.groupBoxIndoor.TabStop = false;
            // 
            // chkShieldProblem
            // 
            this.chkShieldProblem.AutoSize = true;
            this.chkShieldProblem.Location = new System.Drawing.Point(43, 33);
            this.chkShieldProblem.Name = "chkShieldProblem";
            this.chkShieldProblem.Size = new System.Drawing.Size(96, 16);
            this.chkShieldProblem.TabIndex = 32;
            this.chkShieldProblem.Text = "屏蔽小区信号";
            this.chkShieldProblem.UseVisualStyleBackColor = true;
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.ForeColor = System.Drawing.Color.Red;
            this.label5.Location = new System.Drawing.Point(141, 1);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(65, 12);
            this.label5.TabIndex = 31;
            this.label5.Text = "(速度较慢)";
            // 
            // numRadiusFactor
            // 
            this.numRadiusFactor.EditValue = new decimal(new int[] {
            16,
            0,
            0,
            65536});
            this.numRadiusFactor.Location = new System.Drawing.Point(112, 104);
            this.numRadiusFactor.Name = "numRadiusFactor";
            this.numRadiusFactor.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numRadiusFactor.Properties.Appearance.Options.UseFont = true;
            this.numRadiusFactor.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numRadiusFactor.Properties.Mask.AutoComplete = DevExpress.XtraEditors.Mask.AutoCompleteType.None;
            this.numRadiusFactor.Properties.MaxValue = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.numRadiusFactor.Properties.MinValue = new decimal(new int[] {
            100,
            0,
            0,
            -2147483648});
            this.numRadiusFactor.Size = new System.Drawing.Size(82, 20);
            this.numRadiusFactor.TabIndex = 25;
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(4, 107);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(107, 12);
            this.label2.TabIndex = 24;
            this.label2.Text = "理想覆盖半径系数:";
            // 
            // numNearestBtsCount
            // 
            this.numNearestBtsCount.EditValue = new decimal(new int[] {
            3,
            0,
            0,
            0});
            this.numNearestBtsCount.Location = new System.Drawing.Point(112, 69);
            this.numNearestBtsCount.Name = "numNearestBtsCount";
            this.numNearestBtsCount.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numNearestBtsCount.Properties.Appearance.Options.UseFont = true;
            this.numNearestBtsCount.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numNearestBtsCount.Properties.IsFloatValue = false;
            this.numNearestBtsCount.Properties.Mask.AutoComplete = DevExpress.XtraEditors.Mask.AutoCompleteType.None;
            this.numNearestBtsCount.Properties.Mask.EditMask = "N00";
            this.numNearestBtsCount.Properties.MaxValue = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.numNearestBtsCount.Size = new System.Drawing.Size(82, 20);
            this.numNearestBtsCount.TabIndex = 23;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(28, 72);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(83, 12);
            this.label1.TabIndex = 1;
            this.label1.Text = "最近基站个数:";
            // 
            // chkCellCheck
            // 
            this.chkCellCheck.AutoSize = true;
            this.chkCellCheck.Location = new System.Drawing.Point(16, 0);
            this.chkCellCheck.Name = "chkCellCheck";
            this.chkCellCheck.Size = new System.Drawing.Size(108, 16);
            this.chkCellCheck.TabIndex = 0;
            this.chkCellCheck.Text = "越区和室内信号";
            this.chkCellCheck.UseVisualStyleBackColor = true;
            // 
            // groupBoxClose
            // 
            this.groupBoxClose.Controls.Add(this.txtFileName);
            this.groupBoxClose.Controls.Add(this.btnSelect);
            this.groupBoxClose.Controls.Add(this.radioMulti);
            this.groupBoxClose.Controls.Add(this.txtEci);
            this.groupBoxClose.Controls.Add(this.label4);
            this.groupBoxClose.Controls.Add(this.label3);
            this.groupBoxClose.Controls.Add(this.txtTac);
            this.groupBoxClose.Controls.Add(this.radioSingle);
            this.groupBoxClose.Controls.Add(this.chkCloseSimu);
            this.groupBoxClose.Location = new System.Drawing.Point(12, 346);
            this.groupBoxClose.Name = "groupBoxClose";
            this.groupBoxClose.Size = new System.Drawing.Size(589, 98);
            this.groupBoxClose.TabIndex = 30;
            this.groupBoxClose.TabStop = false;
            // 
            // txtFileName
            // 
            this.txtFileName.Location = new System.Drawing.Point(133, 67);
            this.txtFileName.Name = "txtFileName";
            this.txtFileName.ReadOnly = true;
            this.txtFileName.Size = new System.Drawing.Size(291, 21);
            this.txtFileName.TabIndex = 8;
            // 
            // btnSelect
            // 
            this.btnSelect.Location = new System.Drawing.Point(446, 65);
            this.btnSelect.Name = "btnSelect";
            this.btnSelect.Size = new System.Drawing.Size(75, 23);
            this.btnSelect.TabIndex = 7;
            this.btnSelect.Text = "导入文件";
            this.btnSelect.UseVisualStyleBackColor = true;
            // 
            // radioMulti
            // 
            this.radioMulti.AutoSize = true;
            this.radioMulti.Location = new System.Drawing.Point(18, 70);
            this.radioMulti.Name = "radioMulti";
            this.radioMulti.Size = new System.Drawing.Size(71, 16);
            this.radioMulti.TabIndex = 6;
            this.radioMulti.Text = "批量小区";
            this.radioMulti.UseVisualStyleBackColor = true;
            // 
            // txtEci
            // 
            this.txtEci.Location = new System.Drawing.Point(324, 35);
            this.txtEci.Name = "txtEci";
            this.txtEci.Size = new System.Drawing.Size(100, 21);
            this.txtEci.TabIndex = 5;
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(289, 38);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(29, 12);
            this.label4.TabIndex = 4;
            this.label4.Text = "ECI:";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(99, 38);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(29, 12);
            this.label3.TabIndex = 3;
            this.label3.Text = "TAC:";
            // 
            // txtTac
            // 
            this.txtTac.Location = new System.Drawing.Point(134, 35);
            this.txtTac.Name = "txtTac";
            this.txtTac.Size = new System.Drawing.Size(100, 21);
            this.txtTac.TabIndex = 2;
            // 
            // radioSingle
            // 
            this.radioSingle.AutoSize = true;
            this.radioSingle.Checked = true;
            this.radioSingle.Location = new System.Drawing.Point(18, 36);
            this.radioSingle.Name = "radioSingle";
            this.radioSingle.Size = new System.Drawing.Size(59, 16);
            this.radioSingle.TabIndex = 1;
            this.radioSingle.TabStop = true;
            this.radioSingle.Text = "单小区";
            this.radioSingle.UseVisualStyleBackColor = true;
            // 
            // chkCloseSimu
            // 
            this.chkCloseSimu.AutoSize = true;
            this.chkCloseSimu.Location = new System.Drawing.Point(16, 0);
            this.chkCloseSimu.Name = "chkCloseSimu";
            this.chkCloseSimu.Size = new System.Drawing.Size(72, 16);
            this.chkCloseSimu.TabIndex = 0;
            this.chkCloseSimu.Text = "模拟闭站";
            this.chkCloseSimu.UseVisualStyleBackColor = true;
            // 
            // labelControl2
            // 
            this.labelControl2.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl2.Appearance.Options.UseFont = true;
            this.labelControl2.Location = new System.Drawing.Point(305, 61);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(12, 12);
            this.labelControl2.TabIndex = 24;
            this.labelControl2.Text = "dB";
            // 
            // absValue
            // 
            this.absValue.EditValue = new decimal(new int[] {
            110,
            0,
            0,
            -2147483648});
            this.absValue.Enabled = false;
            this.absValue.Location = new System.Drawing.Point(217, 56);
            this.absValue.Name = "absValue";
            this.absValue.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.absValue.Properties.Appearance.Options.UseFont = true;
            this.absValue.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.absValue.Properties.IsFloatValue = false;
            this.absValue.Properties.Mask.AutoComplete = DevExpress.XtraEditors.Mask.AutoCompleteType.None;
            this.absValue.Properties.Mask.EditMask = "N00";
            this.absValue.Properties.MaxValue = new decimal(new int[] {
            30,
            0,
            0,
            0});
            this.absValue.Size = new System.Drawing.Size(82, 20);
            this.absValue.TabIndex = 13;
            // 
            // chbRelative
            // 
            this.chbRelative.AutoSize = true;
            this.chbRelative.Checked = true;
            this.chbRelative.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chbRelative.Location = new System.Drawing.Point(16, 24);
            this.chbRelative.Name = "chbRelative";
            this.chbRelative.Size = new System.Drawing.Size(186, 16);
            this.chbRelative.TabIndex = 33;
            this.chbRelative.Text = "相对覆盖带：与最强信号差异<";
            this.chbRelative.UseVisualStyleBackColor = true;
            this.chbRelative.CheckedChanged += new System.EventHandler(this.chbRelative_CheckedChanged);
            // 
            // chbAbslute
            // 
            this.chbAbslute.AutoSize = true;
            this.chbAbslute.Location = new System.Drawing.Point(52, 58);
            this.chbAbslute.Name = "chbAbslute";
            this.chbAbslute.Size = new System.Drawing.Size(150, 16);
            this.chbAbslute.TabIndex = 33;
            this.chbAbslute.Text = "绝对覆盖带：信号强度>";
            this.chbAbslute.UseVisualStyleBackColor = true;
            this.chbAbslute.CheckedChanged += new System.EventHandler(this.chbAbslute_CheckedChanged);
            // 
            // labelControl10
            // 
            this.labelControl10.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl10.Appearance.Options.UseFont = true;
            this.labelControl10.Location = new System.Drawing.Point(345, 61);
            this.labelControl10.Name = "labelControl10";
            this.labelControl10.Size = new System.Drawing.Size(78, 12);
            this.labelControl10.TabIndex = 18;
            this.labelControl10.Text = "重叠覆盖度 ≥";
            // 
            // absCoverate
            // 
            this.absCoverate.EditValue = new decimal(new int[] {
            11,
            0,
            0,
            0});
            this.absCoverate.Enabled = false;
            this.absCoverate.Location = new System.Drawing.Point(439, 56);
            this.absCoverate.Name = "absCoverate";
            this.absCoverate.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.absCoverate.Properties.Appearance.Options.UseFont = true;
            this.absCoverate.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.absCoverate.Properties.IsFloatValue = false;
            this.absCoverate.Properties.Mask.AutoComplete = DevExpress.XtraEditors.Mask.AutoCompleteType.None;
            this.absCoverate.Properties.Mask.EditMask = "N00";
            this.absCoverate.Properties.MaxValue = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.absCoverate.Properties.MinValue = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.absCoverate.Size = new System.Drawing.Size(82, 20);
            this.absCoverate.TabIndex = 15;
            // 
            // label16
            // 
            this.label16.AutoSize = true;
            this.label16.Location = new System.Drawing.Point(262, 57);
            this.label16.Name = "label16";
            this.label16.Size = new System.Drawing.Size(11, 12);
            this.label16.TabIndex = 34;
            this.label16.Text = "%";
            // 
            // label15
            // 
            this.label15.AutoSize = true;
            this.label15.Location = new System.Drawing.Point(56, 57);
            this.label15.Name = "label15";
            this.label15.Size = new System.Drawing.Size(125, 12);
            this.label15.TabIndex = 35;
            this.label15.Text = "高重叠覆盖度点占比≥";
            // 
            // spinEditRoadPercent
            // 
            this.spinEditRoadPercent.EditValue = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.spinEditRoadPercent.Location = new System.Drawing.Point(196, 52);
            this.spinEditRoadPercent.Name = "spinEditRoadPercent";
            this.spinEditRoadPercent.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditRoadPercent.Properties.Increment = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.spinEditRoadPercent.Properties.MaxValue = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.spinEditRoadPercent.Size = new System.Drawing.Size(60, 21);
            this.spinEditRoadPercent.TabIndex = 36;
            // 
            // groupBoxConstancy
            // 
            this.groupBoxConstancy.Controls.Add(this.label15);
            this.groupBoxConstancy.Controls.Add(this.label16);
            this.groupBoxConstancy.Controls.Add(this.labelControl6);
            this.groupBoxConstancy.Controls.Add(this.labelControl8);
            this.groupBoxConstancy.Controls.Add(this.labelControl7);
            this.groupBoxConstancy.Controls.Add(this.spinEditRoadPercent);
            this.groupBoxConstancy.Controls.Add(this.spinEditRoadDistance);
            this.groupBoxConstancy.Controls.Add(this.spinEditSampleDistance);
            this.groupBoxConstancy.Controls.Add(this.labelControl9);
            this.groupBoxConstancy.Location = new System.Drawing.Point(12, 103);
            this.groupBoxConstancy.Name = "groupBoxConstancy";
            this.groupBoxConstancy.Size = new System.Drawing.Size(589, 86);
            this.groupBoxConstancy.TabIndex = 65;
            this.groupBoxConstancy.TabStop = false;
            this.groupBoxConstancy.Text = "持续性";
            // 
            // groupBoxIndex
            // 
            this.groupBoxIndex.Controls.Add(this.spinEditMaxDiff);
            this.groupBoxIndex.Controls.Add(this.labelControl3);
            this.groupBoxIndex.Controls.Add(this.chbAbslute);
            this.groupBoxIndex.Controls.Add(this.labelControl10);
            this.groupBoxIndex.Controls.Add(this.chbRelative);
            this.groupBoxIndex.Controls.Add(this.labelControl1);
            this.groupBoxIndex.Controls.Add(this.labelControl2);
            this.groupBoxIndex.Controls.Add(this.absValue);
            this.groupBoxIndex.Controls.Add(this.spinEditCoverage);
            this.groupBoxIndex.Controls.Add(this.absCoverate);
            this.groupBoxIndex.Location = new System.Drawing.Point(12, 6);
            this.groupBoxIndex.Name = "groupBoxIndex";
            this.groupBoxIndex.Size = new System.Drawing.Size(589, 86);
            this.groupBoxIndex.TabIndex = 66;
            this.groupBoxIndex.TabStop = false;
            this.groupBoxIndex.Text = "指标限定";
            // 
            // chkTwoEarfcn
            // 
            this.chkTwoEarfcn.Location = new System.Drawing.Point(18, 31);
            this.chkTwoEarfcn.Name = "chkTwoEarfcn";
            this.chkTwoEarfcn.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.chkTwoEarfcn.Properties.Appearance.Options.UseFont = true;
            this.chkTwoEarfcn.Properties.Caption = "多层网剔除异频";
            this.chkTwoEarfcn.Size = new System.Drawing.Size(115, 19);
            this.chkTwoEarfcn.TabIndex = 34;
            this.chkTwoEarfcn.CheckedChanged += new System.EventHandler(this.chkTwoEarfcn_CheckedChanged);
            // 
            // groupBoxRemove
            // 
            this.groupBoxRemove.Controls.Add(this.chkTwoEarfcn);
            this.groupBoxRemove.Controls.Add(this.labelControl4);
            this.groupBoxRemove.Controls.Add(this.spinEditRxlevMin);
            this.groupBoxRemove.Controls.Add(this.labelControl5);
            this.groupBoxRemove.Controls.Add(this.freqBandControl1);
            this.groupBoxRemove.Location = new System.Drawing.Point(229, 196);
            this.groupBoxRemove.Name = "groupBoxRemove";
            this.groupBoxRemove.Size = new System.Drawing.Size(372, 146);
            this.groupBoxRemove.TabIndex = 67;
            this.groupBoxRemove.TabStop = false;
            this.groupBoxRemove.Text = "剔除规则";
            // 
            // freqBandControl1
            // 
            this.freqBandControl1.ChkFreqBandChange_click = null;
            this.freqBandControl1.Location = new System.Drawing.Point(158, 10);
            this.freqBandControl1.Name = "freqBandControl1";
            this.freqBandControl1.Size = new System.Drawing.Size(208, 130);
            this.freqBandControl1.TabIndex = 35;
            // 
            // toolStripDropDownFreq
            // 
            this.toolStripDropDownFreq.LayoutStyle = System.Windows.Forms.ToolStripLayoutStyle.Flow;
            this.toolStripDropDownFreq.Name = "toolStripDropDown1";
            this.toolStripDropDownFreq.Size = new System.Drawing.Size(2, 4);
            // 
            // ZTLTEHighCoverageRoadSetForm
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(612, 488);
            this.Controls.Add(this.groupBoxRemove);
            this.Controls.Add(this.groupBoxIndex);
            this.Controls.Add(this.groupBoxConstancy);
            this.Controls.Add(this.groupBoxClose);
            this.Controls.Add(this.groupBoxIndoor);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.Name = "ZTLTEHighCoverageRoadSetForm";
            this.Text = "高重叠覆盖度路段条件设置";
            this.Load += new System.EventHandler(this.ZTLTEHighCoverageRoadSetForm_Load);
            ((System.ComponentModel.ISupportInitialize)(this.spinEditRxlevMin.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditCoverage.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditSampleDistance.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditRoadDistance.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditMaxDiff.Properties)).EndInit();
            this.groupBoxIndoor.ResumeLayout(false);
            this.groupBoxIndoor.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numRadiusFactor.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numNearestBtsCount.Properties)).EndInit();
            this.groupBoxClose.ResumeLayout(false);
            this.groupBoxClose.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.absValue.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.absCoverate.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditRoadPercent.Properties)).EndInit();
            this.groupBoxConstancy.ResumeLayout(false);
            this.groupBoxConstancy.PerformLayout();
            this.groupBoxIndex.ResumeLayout(false);
            this.groupBoxIndex.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chkTwoEarfcn.Properties)).EndInit();
            this.groupBoxRemove.ResumeLayout(false);
            this.groupBoxRemove.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.LabelControl labelControl9;
        private DevExpress.XtraEditors.LabelControl labelControl5;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private DevExpress.XtraEditors.SpinEdit spinEditRxlevMin;
        private DevExpress.XtraEditors.LabelControl labelControl7;
        private DevExpress.XtraEditors.SpinEdit spinEditCoverage;
        private DevExpress.XtraEditors.SpinEdit spinEditSampleDistance;
        private DevExpress.XtraEditors.SpinEdit spinEditRoadDistance;
        private DevExpress.XtraEditors.SpinEdit spinEditMaxDiff;
        private DevExpress.XtraEditors.SimpleButton btnCancel;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.SimpleButton btnOK;
        private DevExpress.XtraEditors.LabelControl labelControl8;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.LabelControl labelControl6;
        private System.Windows.Forms.GroupBox groupBoxIndoor;
        private System.Windows.Forms.CheckBox chkCellCheck;
        private DevExpress.XtraEditors.SpinEdit numRadiusFactor;
        private System.Windows.Forms.Label label2;
        private DevExpress.XtraEditors.SpinEdit numNearestBtsCount;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.GroupBox groupBoxClose;
        private System.Windows.Forms.TextBox txtEci;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.TextBox txtTac;
        private System.Windows.Forms.RadioButton radioSingle;
        private System.Windows.Forms.CheckBox chkCloseSimu;
        private System.Windows.Forms.TextBox txtFileName;
        private System.Windows.Forms.Button btnSelect;
        private System.Windows.Forms.RadioButton radioMulti;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.CheckBox chkShieldProblem;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.SpinEdit absValue;
        private System.Windows.Forms.CheckBox chbRelative;
        private System.Windows.Forms.CheckBox chbAbslute;
        private DevExpress.XtraEditors.LabelControl labelControl10;
        private DevExpress.XtraEditors.SpinEdit absCoverate;
        private System.Windows.Forms.Label label16;
        private System.Windows.Forms.Label label15;
        private DevExpress.XtraEditors.SpinEdit spinEditRoadPercent;
        private System.Windows.Forms.GroupBox groupBoxConstancy;
        private System.Windows.Forms.GroupBox groupBoxIndex;
        private DevExpress.XtraEditors.CheckEdit chkTwoEarfcn;
        private System.Windows.Forms.GroupBox groupBoxRemove;
        private System.Windows.Forms.ToolStripDropDown toolStripDropDownFreq;
        private FreqBandControl freqBandControl1;
    }
}