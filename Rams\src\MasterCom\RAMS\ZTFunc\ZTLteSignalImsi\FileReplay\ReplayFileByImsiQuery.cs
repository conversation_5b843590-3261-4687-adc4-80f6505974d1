﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.Util.UiEx;

using MasterCom.RAMS.ZTFunc.LteSignalImsi;
using MasterCom.RAMS.Model.Interface;

namespace MasterCom.RAMS.ZTFunc
{
    public class LteSignalSingleReplayByImsi : DIYReplayFileQuery
    {
        public LteSignalSingleReplayByImsi(MainModel mainModel) : base(mainModel)
        {
        }

        protected override void query()
        {
            List<ImsiFile> imsiFiles = QueryImsiFiles();
            if (imsiFiles.Count == 0)
            {
                System.Windows.Forms.MessageBox.Show("未能查询到文件的用户列表", "提示");
                return;
            }

            ReplayFileByImsiSetForm selectForm = new ReplayFileByImsiSetForm();
            selectForm.FillData(imsiFiles);
            if (selectForm.ShowDialog() != System.Windows.Forms.DialogResult.OK)
            {
                return;
            }
            String imsiSel = "";
            imsiFiles = selectForm.GetSelectedFiles(ref imsiSel);
            curImsiSel = imsiSel;
            DoSomethingBeforeQuery(imsiFiles);
            base.query();
        }

        protected override bool isValidTestPoint(TestPoint tp)
        {
            object imsi = tp["signal_IMSI"];
            return imsi != null && IsContainImsi(imsi.ToString());
        }

        protected override bool isValidEvent(Event e)
        {
            object imsi = e["Value3"];
            return imsi != null && IsContainImsi(imsi.ToString());
        }

        protected virtual List<ImsiFile> QueryImsiFiles()
        {
            ImsiFileQuery query = new ImsiFileQuery();
            return query.QueryFilesForEach(Condition.FileInfos);
        }

        protected virtual void DoSomethingBeforeQuery(List<ImsiFile> selectedFiles)
        {
        }

        private bool IsContainImsi(string imsi)
        {
            return curImsiSel == imsi;
        }

       
        private string curImsiSel = "";
        //private Dictionary<string, int> imsiFileDic;
    }

    public class LteSignalMultiReplayByImsi : LteSignalSingleReplayByImsi
    {
        public LteSignalMultiReplayByImsi(MainModel mainModel)
            : base(mainModel)
        {
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return Model.MainModel.NeedSearchType.None;
        }

        protected override List<ImsiFile> QueryImsiFiles()
        {
            List<ImsiFile> retList = new List<ImsiFile>();
            WaitTextBox.Show("正在查询用户列表...", QueryImsiFilesInThread, retList);
            return retList;
        }

        protected override void DoSomethingBeforeQuery(List<ImsiFile> selectedFiles)
        {
            Dictionary<int, bool> selectedFileDic = new Dictionary<int, bool>();
            foreach (ImsiFile iFile in selectedFiles)
            {
                selectedFileDic[iFile.FileID] = true;
            }

            List<FileInfo> fileInfos = new List<FileInfo>();
            foreach (FileInfo fInfo in this.Condition.FileInfos)
            {
                if (selectedFileDic.ContainsKey(fInfo.ID))
                {
                    fileInfos.Add(fInfo);
                }
            }
            this.Condition.FileInfos.Clear();
            this.Condition.FileInfos.AddRange(fileInfos);
        }

        private void QueryImsiFilesInThread(object args)
        {
            List<ImsiFile> imsiFiles = args as List<ImsiFile>;
            try
            {
                DIYQueryFileInfo fileQuery = new DIYQueryFileInfo(MainModel);
                fileQuery.IsShowFileInfoForm = false;
                fileQuery.SetQueryCondition(this.Condition);
                fileQuery.Query();
                this.Condition.FileInfos.Clear();
                this.Condition.FileInfos.AddRange(MainModel.FileInfos);

                ImsiFileQuery query = new ImsiFileQuery();
                imsiFiles.AddRange(query.QueryFilesBatch(MainModel.FileInfos));
            }
            finally
            {
                System.Threading.Thread.Sleep(300);
                WaitTextBox.Close();
            }
        }
    }
}
