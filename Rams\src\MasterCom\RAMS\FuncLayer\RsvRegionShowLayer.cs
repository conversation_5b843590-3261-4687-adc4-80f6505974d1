﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.MTGis;
using MasterCom.RAMS.Func;
using MapWinGIS;
using AxMapWinGIS;
using System.Windows.Forms;
using System.Drawing;
using MasterCom.Util;

namespace MasterCom.RAMS.FuncLayer
{
    /// <summary>
    /// 预存区域图层
    /// </summary>
    public class RsvRegionShowLayer
    {
        readonly private MapForm mapForm;
        private MapOperation mop { get; set; }
        readonly Shapefile shapeFile = null;
        readonly int handle = -1;
        public RsvRegionShowLayer(MapForm form, MapOperation op)
        {
            this.mapForm = form;
            this.mop = op;
            AxMap mapMain = mapForm.GetMapFormControl();
            shapeFile = new Shapefile();
            bool result = shapeFile.CreateNewWithShapeID("", ShpfileType.SHP_POLYGON);
            if (!result)
            {
                MessageBox.Show(shapeFile.get_ErrorMsg(shapeFile.LastErrorCode));
                return;
            }
            shapeFile.DefaultDrawingOptions.LineWidth = 3;
            shapeFile.DefaultDrawingOptions.LineColor = (uint)ColorTranslator.ToOle(Color.Green);
            shapeFile.DefaultDrawingOptions.FillTransparency = 35;
            shapeFile.DefaultDrawingOptions.FillColor = (uint)ColorTranslator.ToOle(Color.Green);
            handle = mapMain.AddLayer(shapeFile, true);
        }
        internal void ApplyRegion(MapWinGIS.Shape shape)
        {
            shapeFile.EditClear();
            int shpIdx = 0;
            shapeFile.EditInsertShape(shape, ref shpIdx);
        }
        internal void ApplyRegions(List<ResvRegion> list)
        {
            shapeFile.EditClear();
            int shpIdx = 0;
            foreach (ResvRegion reg in list)
            {
                shapeFile.EditInsertShape(reg.Shape, ref shpIdx);
                shpIdx++;
            }
        }

        internal string ApplyResvRegion(List<ResvRegion> list, bool unionReg)
        {
            string resultStr = "";
            AxMap mapMain = mapForm.GetMapFormControl();
            shapeFile.EditClear();
            int shpIdx = 0;
            if (!unionReg && list.Count < 50)//合并区域
            {
                List<string> invalidList = new List<string>();
                MapWinGIS.Shape unionShp = null;
                foreach (ResvRegion reg in list)
                {
                    dealShape(ref shpIdx, invalidList, ref unionShp, reg);
                }
                if (unionShp != null)
                {
                    shapeFile.EditInsertShape(unionShp, ref shpIdx);
                }
                if (invalidList.Count == 0)
                {
                    resultStr = "成功合并区域！";
                }
                else
                {
                    resultStr = "区域：";
                    StringBuilder sb = new StringBuilder();
                    foreach (string name in invalidList)
                    {
                        sb.Append(name + "、");
                    }
                    resultStr += sb.ToString();
                    resultStr = resultStr.Substring(0, resultStr.Length - 1);
                    resultStr += "无法与其他区域合并！";
                }
            }
            else
            {
                ApplyRegions(list);
            }
            mapMain.MoveLayerTop(mapMain.get_LayerPosition(handle));
            return resultStr;
        }

        private void dealShape(ref int shpIdx, List<string> invalidList, ref MapWinGIS.Shape unionShp, ResvRegion reg)
        {
            if (reg.Shape.IsValid)
            {
                if (unionShp == null)
                {
                    unionShp = reg.Shape;
                }
                else
                {
                    MapWinGIS.Shape tempShp = unionShp.Clip(reg.Shape, tkClipOperation.clUnion);
                    if (tempShp == null)//合并失败，不合并该区域
                    {
                        shapeFile.EditInsertShape(reg.Shape, ref shpIdx);
                        shpIdx++;
                    }
                    else
                    {
                        unionShp = tempShp;
                    }
                }
            }
            else//无效区域、自相交区域无法与其他区域合并
            {
                invalidList.Add(reg.RegionName);
                shapeFile.EditInsertShape(reg.Shape, ref shpIdx);
                shpIdx++;
            }
        }

        internal void ClearData()
        {
            shapeFile.EditClear();
        }
    }
}
