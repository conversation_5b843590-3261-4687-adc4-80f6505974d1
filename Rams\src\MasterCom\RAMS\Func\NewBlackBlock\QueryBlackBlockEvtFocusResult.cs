﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.NOP;

namespace MasterCom.RAMS.NewBlackBlock
{
    class QueryBlackBlockEvtFocusResult : DIYSQLBase
    {
        private string evtIDs = string.Empty;
        protected override void query()
        {
            Dictionary<int, List<BlackBlockItem>> cityBlockDic = new Dictionary<int, List<BlackBlockItem>>();
            Dictionary<string, bool> focusTbNameDic = new Dictionary<string, bool>();
            Dictionary<int, bool> evtIDDic = new Dictionary<int, bool>();
            foreach (BlackBlockItem item in mainModel.CurBlackBlockList)
            {
                List<BlackBlockItem> blocks = null;
                if (!cityBlockDic.TryGetValue(item.DistrictID,out blocks))
                {
                    blocks = new List<BlackBlockItem>();
                    cityBlockDic[item.DistrictID] = blocks;
                }
                blocks.Add(item);
                foreach (BlockEventItem blockEvt in item.AbEvents)
                {
                    string tbName = string.Format("tb_focus_event_result_{0}",
                        blockEvt.DateTime.ToString("yyMM"));
                    focusTbNameDic[tbName] = true;
                    evtIDDic[blockEvt.ieventId] = true;
                }
            }
            List<string> focusTbNames = new List<string>(focusTbNameDic.Keys);
            StringBuilder sb = new StringBuilder();
            foreach (int evtID in evtIDDic.Keys)
            {
                sb.Append(evtID + ",");
            }
            evtIDs = sb.ToString().TrimEnd(',');
            foreach (int districtID in cityBlockDic.Keys)
            {
                ClientProxy clientProxy = new ClientProxy();
                try
                {
                    if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port
                        , MainModel.User.LoginName, MainModel.User.Password, districtID)
                        != ConnectResult.Success)
                    {
                        ErrorInfo = "连接服务器失败!";
                        return;
                    }
                    esResults = new List<ESResultInfo>();
                    foreach (string tbName in focusTbNames)
                    {
                        curTbName = tbName;
                        queryInThread(clientProxy);
                    }
                    foreach (ESResultInfo result in esResults)
                    {
                        setEvtResult(cityBlockDic, districtID, result);
                    }
                }
                finally
                {
                    clientProxy.Close();
                }
            }
            esResults = null;
        }

        private void setEvtResult(Dictionary<int, List<BlackBlockItem>> cityBlockDic, int districtID, ESResultInfo result)
        {
            //bool filled = false;
            foreach (BlackBlockItem block in cityBlockDic[districtID])
            {
                foreach (BlockEventItem evtItem in block.AbEvents)
                {
                    if (evtItem.ESResult == null
                        && evtItem.file_id == result.FileID
                        && evtItem.ieventId == result.EventID
                        && evtItem.sn == result.SeqID)
                    {
                        evtItem.ESResult = result;
                        //filled = true;
                        break;
                    }
                }
                //if (filled)
                //{
                //    break;
                //}
            }
        }

        string curTbName = string.Empty;
        protected override string getSqlTextString()
        {
            string sql = "select fileid,evtid,seqid,rid,suggest,detail,msg";
            sql += ",stime,etime,midlongitude,midlatitude,primarytype,specifictype";
            sql += " from " + curTbName;
            sql += string.Format(" where evtid in({0})", evtIDs);
            return sql;
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] arr = new E_VType[13];
            int i = 0;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_VARYBIN;//msg
            arr[i++] = E_VType.E_String;//stime
            arr[i++] = E_VType.E_String;//etime
            arr[i++] = E_VType.E_Float;//midlongitude
            arr[i++] = E_VType.E_Float;//midlatitude
            arr[i++] = E_VType.E_String;//primarytype
            arr[i] = E_VType.E_String;//specifictype
            //arr[i++] = E_VType.E_VARYBIN;//extend
            return arr;
        }
        List<ESResultInfo> esResults = null;
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    ESResultInfo esInfo = new ESResultInfo();
                    esInfo.FileID = package.Content.GetParamInt();
                    esInfo.EventID = package.Content.GetParamInt();
                    esInfo.SeqID = package.Content.GetParamInt();
                    esInfo.RelationID = package.Content.GetParamInt();
                    esInfo.Suggest = package.Content.GetParamString();
                    esInfo.Detail = package.Content.GetParamString();
                    try
                    {
                        esInfo.FillMsg(package.Content.GetParamBytes());
                    }
                    catch (Exception ee)
                    {
                        log.Error(ee.Message, ee);
                    }

                    esInfo.STime = package.Content.GetParamString();
                    esInfo.ETime = package.Content.GetParamString();
                    esInfo.MidLongitude = package.Content.GetParamFloat();
                    esInfo.MidLatitude = package.Content.GetParamFloat();
                    esInfo.PrimaryType = package.Content.GetParamString();
                    esInfo.SpecificType = package.Content.GetParamString();
                    //resultInfo.Extend = package.Content.GetParamBytes();
                    esResults.Add(esInfo);
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    break;
                }
            }
        }

    }
}
