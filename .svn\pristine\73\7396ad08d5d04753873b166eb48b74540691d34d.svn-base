﻿using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.BackgroundFunc
{
    #region 室外站
    public class NbIotOutDoorBtsAcceptInfo
    {
        public NbIotOutDoorBtsAcceptInfo(LTEBTS bts)
        {
            NBIOTBts = bts;
            AccpetTimePeriod = new TimePeriod();
            CellsAcceptDic = new Dictionary<int, NbIotOutDoorCellAcceptInfo>();
        }
        public int SN { get; set; }
        public string Time
        {
            get
            {
                AccpetTimePeriod.showDayFormat = true;
                return AccpetTimePeriod.ToString();
            }
        }

        public TimePeriod AccpetTimePeriod { get; set; }

        public BtsFusionInfoBase FusionInfo { get; set; }
        public LTEBTS NBIOTBts { get; set; }
        public string BtsName { get { return NBIOTBts.Name.Trim(); } }
        public int BtsId { get { return NBIOTBts.BTSID; } }
        public double Longitude { get { return NBIOTBts.Longitude; } }
        public double Latitude { get { return NBIOTBts.Latitude; } }
        public string NotAccordKpiDes { get; set; }
        public string IsAccordAcceptStr { get { return IsAccordAccept ? "是" : "否"; } }
        public bool IsAccordAccept { get; set; }

        //各小区验收信息key:cellid
        public Dictionary<int, NbIotOutDoorCellAcceptInfo> CellsAcceptDic { get; set; }

        public string IsCoverAccordDes { get; set; }
        public string IsAttachAccordDes { get; set; }
        public string IsRRCAccordDes { get; set; }
        public string IsPingAccordDes { get; set; }
        public string IsThroughputAccordDes { get; set; }

        /// <summary>
        /// 检验基站和各小区指标是否合格,获取完基站和各小区的指标信息后调用
        /// </summary>
        public virtual void CheckBtsIsAccordAccept()
        {
            StringBuilder strbNotAccordDes = new StringBuilder();
            bool allCellAccord = true;
            foreach (LTECell cell in NBIOTBts.Cells)
            {
                NbIotOutDoorCellAcceptInfo cellInfo;
                if (this.CellsAcceptDic.TryGetValue(cell.CellID, out cellInfo))
                {
                    cellInfo.CheckCellIsAccordAccept();
                    setBtsKpiInfo(cellInfo);
                    if (!cellInfo.IsAccord)
                    {
                        allCellAccord = false;
                        strbNotAccordDes.AppendLine(cell.Name + "小区不达标的指标：" + cellInfo.NotAccordKpiDes);
                    }
                }
                else
                {
                    cellInfo = new NbIotOutDoorCellAcceptInfo(cell);
                    setBtsKpiInfo(cellInfo);
                    allCellAccord = false;
                    strbNotAccordDes.AppendLine(cell.Name + "小区未查询到指标信息;");
                }
            }
            NotAccordKpiDes = strbNotAccordDes.ToString();
            IsAccordAccept = allCellAccord;
        }

        protected void setBtsKpiInfo(NbIotOutDoorCellAcceptInfo cellInfo)
        {
            IsCoverAccordDes = isKpiAccord(IsCoverAccordDes, cellInfo.CoverProperty.IsAccord);
            IsAttachAccordDes = isKpiAccord(IsAttachAccordDes, cellInfo.AttachRate.IsAccord);
            IsRRCAccordDes = isKpiAccord(IsRRCAccordDes, cellInfo.RRCRate.IsAccord);
            IsPingAccordDes = isKpiAccord(IsPingAccordDes, cellInfo.PingDelay.IsAccord);
            IsThroughputAccordDes = isKpiAccord(IsThroughputAccordDes, (cellInfo.DLThroughput.IsAccord && cellInfo.ULThroughput.IsAccord));
        }

        private string isKpiAccord(string btsKpiDes, bool cellKpi)
        {
            if (btsKpiDes == null)
            {
                return cellKpi ? "是" : "否";
            }

            if ((btsKpiDes == "是") && cellKpi)
            {
                return "是";
            }
            return "否";
        }
    }

    public class NbIotOutDoorCellAcceptInfo
    {
        public NbIotOutDoorCellAcceptInfo(LTECell cell)
        {
            this.NBIOTCell = cell;
        }

        protected List<string> hasGotKpiKeyList = new List<string>();//已经获取到的指标Key集合

        protected bool isAccord = false;
        public bool IsAccord { get { return isAccord; } }
        public string IsAccordDes { get { return isAccord ? "是" : "否"; } }
        public string NotAccordKpiDes { get; set; }
        public LTECell NBIOTCell { get; set; }

        public int CellId
        {
            get
            {
                if (NBIOTCell != null)
                {
                    return NBIOTCell.CellID;
                }
                return 0;
            }
        }

        public string CellName
        {
            get
            {
                if (NBIOTCell != null)
                {
                    return NBIOTCell.Name;
                }
                return "";
            }
        }

        private readonly NBIotKpiInfo attachRate = new NBIotKpiInfo("Attach成功率");
        public NBIotKpiInfo AttachRate { get { return attachRate; } }

        private readonly NBIotKpiInfo rrcRate = new NBIotKpiInfo("RRC成功率");
        public NBIotKpiInfo RRCRate { get { return rrcRate; } }

        private readonly NBIotKpiInfo pingDelay = new NBIotKpiInfo("Ping时延");
        public NBIotKpiInfo PingDelay { get { return pingDelay; } }

        private readonly NBIotKpiInfo ulThroughput = new NBIotKpiInfo("上行吞吐率");
        public NBIotKpiInfo ULThroughput { get { return ulThroughput; } }

        private readonly NBIotKpiInfo dlThroughput = new NBIotKpiInfo("下行吞吐率");
        public NBIotKpiInfo DLThroughput { get { return dlThroughput; } }

        private readonly NBIotPicInfo coverProperty = new NBIotPicInfo("覆盖");
        public NBIotPicInfo CoverProperty { get { return coverProperty; } }

        public void AddAcceptKpiInfo(Dictionary<uint, object> kpiDic)
        {
            statsKpiNewestValues(kpiDic);

            dealWithData();
        }

        protected void dealWithData()
        {
            attachRate.DealWithData();
            rrcRate.DealWithData();
            pingDelay.DealWithData();
            ulThroughput.DealWithData();
            dlThroughput.DealWithData();
            coverProperty.DealWithData();
        }

        /// <summary>
        /// 统计各指标的最新一个指标值
        /// </summary>
        /// <param name="kpiDic"></param>
        protected void statsKpiNewestValues(Dictionary<uint, object> kpiDic)
        {
            foreach (uint key in kpiDic.Keys)
            {
                NBIotKpiKey kpiKey = (NBIotKpiKey)key;

                if (!hasGotKpiKeyList.Contains(kpiKey.ToString()))
                {
                    object objValue = kpiDic[key];
                    hasGotKpiKeyList.Add(kpiKey.ToString());

                    statsKpiNewestValue(kpiKey, objValue);
                }
            }
        }

        protected virtual void statsKpiNewestValue(NBIotKpiKey kpiKey, object objValue)
        {
            getAttachData(kpiKey, objValue);
            getRrcData(kpiKey, objValue);
            getPingData(kpiKey, objValue);
            getULData(kpiKey, objValue);
            getDLData(kpiKey, objValue);
            getCoverData(kpiKey, objValue);
        }

        protected virtual void getAttachData(NBIotKpiKey kpiKey, object objValue)
        {
            switch (kpiKey)
            {
                case NBIotKpiKey.AttachAvgRSRP:
                    this.attachRate.AvgRSRP = (double)objValue;
                    break;
                case NBIotKpiKey.AttachAvgSINR:
                    this.attachRate.AvgSINR = (double)objValue;
                    break;
                case NBIotKpiKey.AttachRate:
                    this.attachRate.SuccessRate = (double)objValue;
                    break;
            }
        }

        protected virtual void getRrcData(NBIotKpiKey kpiKey, object objValue)
        {
            switch (kpiKey)
            {
                case NBIotKpiKey.RrcAvgRSRP:
                    this.rrcRate.AvgRSRP = (double)objValue;
                    break;
                case NBIotKpiKey.RrcAvgSINR:
                    this.rrcRate.AvgSINR = (double)objValue;
                    break;
                case NBIotKpiKey.RrcRate:
                    this.rrcRate.SuccessRate = (double)objValue;
                    break;
            }
        }

        protected virtual void getPingData(NBIotKpiKey kpiKey, object objValue)
        {
            switch (kpiKey)
            {
                case NBIotKpiKey.PingAvgRSRP:
                    this.pingDelay.AvgRSRP = (double)objValue;
                    break;
                case NBIotKpiKey.PingAvgSINR:
                    this.pingDelay.AvgSINR = (double)objValue;
                    break;
                case NBIotKpiKey.PingDelay:
                    this.pingDelay.Data = (double)objValue;
                    break;
            }
        }

        protected virtual void getULData(NBIotKpiKey kpiKey, object objValue)
        {
            switch (kpiKey)
            {
                case NBIotKpiKey.ULAvgRSRP:
                    this.ulThroughput.AvgRSRP = (double)objValue;
                    break;
                case NBIotKpiKey.ULAvgSINR:
                    this.ulThroughput.AvgSINR = (double)objValue;
                    break;
                case NBIotKpiKey.ULThroughputRate:
                    this.ulThroughput.Data = (double)objValue;
                    break;
            }
        }

        protected virtual void getDLData(NBIotKpiKey kpiKey, object objValue)
        {
            switch (kpiKey)
            {
                case NBIotKpiKey.DLAvgRSRP:
                    this.dlThroughput.AvgRSRP = (double)objValue;
                    break;
                case NBIotKpiKey.DLAvgSINR:
                    this.dlThroughput.AvgSINR = (double)objValue;
                    break;
                case NBIotKpiKey.DLThroughputRate:
                    this.dlThroughput.Data = (double)objValue;
                    break;
            }
        }

        protected virtual void getCoverData(NBIotKpiKey kpiKey, object objValue)
        {
            switch (kpiKey)
            {
                case NBIotKpiKey.CoverRsrpPath:
                    this.coverProperty.CoverPicPath_Rsrp = objValue.ToString();
                    break;
                case NBIotKpiKey.CoverSinrPath:
                    this.coverProperty.CoverPicPath_Sinr = objValue.ToString();
                    break;
                case NBIotKpiKey.CoverULPath:
                    this.coverProperty.CoverPicPath_UL = objValue.ToString();
                    break;
                case NBIotKpiKey.CoverAvgRSRP:
                    this.coverProperty.AvgRSRP = (double)objValue;
                    break;
                case NBIotKpiKey.CoverAvgSINR:
                    this.coverProperty.AvgSINR = (double)objValue;
                    break;
                case NBIotKpiKey.CoverAvgSpeed:
                    this.coverProperty.CoverAvgSpeed = (double)objValue;
                    break;
                case NBIotKpiKey.CoverSpeedUpTen:
                    this.coverProperty.CoverSpeedUpTen = (double)objValue;
                    break;
                case NBIotKpiKey.CoverCoverRate:
                    this.coverProperty.CoverCoverRate = (double)objValue;
                    break;
                case NBIotKpiKey.AntennaOppositeRate:
                    this.coverProperty.CoverAntennaOppositeRate = (double)objValue;
                    break;
            }
        }

        /// <summary>
        /// 检验小区指标是否合格,获取完小区各指标信息后调用
        /// </summary>
        /// <returns></returns>
        public void CheckCellIsAccordAccept()
        {
            coverProperty.CheckIsAccordCover();
            attachRate.CheckIsAccordRate(100);
            rrcRate.CheckIsAccordRate(100);
            pingDelay.CheckIsAccordData(double.MinValue, 1.5);
            ulThroughput.CheckIsAccordData(10, double.MaxValue);
            dlThroughput.CheckIsAccordData(13, double.MaxValue);

            bool isAllKpiAccord = true;
            StringBuilder strbNotAccordKpiName = new StringBuilder();
            strbNotAccordKpiName.Append(getNotAccordKpiInfo(this.coverProperty, ref isAllKpiAccord));
            strbNotAccordKpiName.Append(getNotAccordKpiInfo(this.attachRate, ref isAllKpiAccord));
            strbNotAccordKpiName.Append(getNotAccordKpiInfo(this.rrcRate, ref isAllKpiAccord));
            strbNotAccordKpiName.Append(getNotAccordKpiInfo(this.pingDelay, ref isAllKpiAccord));
            strbNotAccordKpiName.Append(getNotAccordKpiInfo(this.ulThroughput, ref isAllKpiAccord));
            strbNotAccordKpiName.Append(getNotAccordKpiInfo(this.dlThroughput, ref isAllKpiAccord));

            this.isAccord = isAllKpiAccord;
            this.NotAccordKpiDes = strbNotAccordKpiName.ToString();
        }

        protected string getNotAccordKpiInfo(NBIotKpiInfo kpi, ref bool isAllKpiAccord)
        {
            return getNotAccordKpiInfo(kpi.IsAccord, kpi.KpiName, ref isAllKpiAccord);
        }

        protected string getNotAccordKpiInfo(bool isKpiAccord, string strKpiName, ref bool isAllKpiAccord)
        {
            if (!isKpiAccord)
            {
                isAllKpiAccord = false;
                return strKpiName + ";";
            }
            return string.Empty;
        }
    }
    #endregion

    #region 室内站
    public class NBIotInDoorBtsAcceptInfo : BtsAcceptInfoBase
    {
        public NBIotInDoorBtsAcceptInfo(LTEBTS lteBts)
            : base(lteBts)
        {
            CellsAcceptDic = new Dictionary<int, NBIotInDoorCellAcceptInfo>();
        }

        public List<NBIotInDoorCellAcceptInfo> CellsAcceptList
        {
            get
            {
                List<NBIotInDoorCellAcceptInfo> cellsAcceptList = new List<NBIotInDoorCellAcceptInfo>(CellsAcceptDic.Values);
                cellsAcceptList.Sort();
                return cellsAcceptList;
            }
        }

        //key:CellID
        public Dictionary<int, NBIotInDoorCellAcceptInfo> CellsAcceptDic { get; set; }

        /// <summary>
        /// 检验基站和各小区指标是否合格,获取完基站和各小区的指标信息后调用
        /// </summary>
        public override void CheckBtsIsAccordAccept()
        {
            bool allCellAccord = true;
            StringBuilder strbNotAccordDes = new StringBuilder();
            foreach (LTECell cell in LteBts.Cells)
            {
                NBIotInDoorCellAcceptInfo cellInfo;
                if (this.CellsAcceptDic.TryGetValue(cell.CellID, out cellInfo))
                {
                    cellInfo.CheckCellIsAccordAccept();
                    if (!cellInfo.IsAccord)
                    {
                        allCellAccord = false;
                        strbNotAccordDes.AppendLine(cell.Name + "小区不达标的指标：" + cellInfo.NotAccordKpiDes);
                    }
                }
                else
                {
                    allCellAccord = false;
                    strbNotAccordDes.AppendLine(cell.Name + "小区未查询到指标信息;");
                }
            }
            this.NotAccordKpiDes = strbNotAccordDes.ToString();
            this.IsAccordAccept = allCellAccord;
        }
    }

    public class NBIotInDoorCellAcceptInfo : CellAcceptInfoBase
    {
        public NBIotInDoorCellAcceptInfo(LTECell cell)
            : base(cell)
        {
        }

        public virtual void AddAcceptKpiInfo(string fileName, Dictionary<uint, object> kpiDic)
        {
            statsKpiNewestValues(kpiDic);
        }

        /// <summary>
        /// 统计各指标的最新一个指标值
        /// </summary>
        /// <param name="kpiDic"></param>
        protected new void statsKpiNewestValues(Dictionary<uint, object> kpiDic)
        {
            throw new NotSupportedException();
        }

        /// <summary>
        /// 检验小区指标是否合格,获取完小区各指标信息后调用
        /// </summary>
        /// <returns></returns>
        public override void CheckCellIsAccordAccept()
        {

        }
    }
    #endregion

    public class NBIotKpiInfo
    {
        public NBIotKpiInfo(string strKpiName)
        {
            KpiName = strKpiName;
            AvgRSRP = double.NaN;
            AvgSINR = double.NaN;
            SuccessRate = double.NaN;
            Data = double.NaN;
        }

        public string KpiName { get; set; }

        public double AvgRSRP { get; set; }
        public double AvgSINR { get; set; }
        public double SuccessRate { get; set; }
        public double Data { get; set; }

        public double Distance { get; set; }

        public string AvgRSRPStr { get; private set; }
        public string AvgSINRStr { get; private set; }
        public string SuccessRateStr { get; private set; }
        public string DataStr { get; private set; }

        public virtual void DealWithData()
        {
            AvgRSRPStr = getVaildData(AvgRSRP);
            AvgSINRStr = getVaildData(AvgSINR);
            SuccessRateStr = getValidRate(SuccessRate);
            DataStr = getVaildData(Data);
        }

        protected string getVaildData(double data)
        {
            if (double.IsNaN(data))
            {
                return "-";
            }
            return data.ToString();
        }

        protected string getValidRate(double data)
        {
            string dataStr = getVaildData(data);
            if (dataStr == "-")
            {
                return dataStr;
            }
            return dataStr + "%";
        }

        protected bool isAccord = false;
        public bool IsAccord
        {
            get { return isAccord; }
        }
        public string IsAccordDes { get; private set; }

        protected void setAccord(bool accord)
        {
            isAccord = accord;
            if (accord)
            {
                IsAccordDes = "是";
            }
            else
            {
                IsAccordDes = "否";
            }
        }

        /// <summary>
        /// 检验是否通过验收（拨测次数合格门限，成功率合格门限）
        /// </summary>
        /// <param name="minValidCount"></param>
        /// <param name="minValidRate"></param>
        public void CheckIsAccordRate(double minValidRate)
        {
            if (!double.IsNaN(SuccessRate) && SuccessRate >= minValidRate)
            {
                setAccord(true);
            }
            else
            {
                setAccord(false);
            }
        }

        public void CheckIsAccordData(double minValidData, double maxValidData)
        {
            if (!double.IsNaN(Data) && Data >= minValidData && Data <= maxValidData)
            {
                setAccord(true);
            }
            else
            {
                setAccord(false);
            }
        }

        public override string ToString()
        {
            return IsAccordDes;
        }
    }

    public class NBIotPicInfo : NBIotKpiInfo
    {
        public NBIotPicInfo(string strPicName)
            : base(strPicName)
        {
            CoverAvgSpeed = double.NaN;
            CoverSpeedUpTen = double.NaN;
            CoverCoverRate = double.NaN;
            CoverAntennaOppositeRate = double.NaN;
        }

        public string CoverPicPath_Rsrp { get; set; }
        public string CoverPicPath_Sinr { get; set; }
        public string CoverPicPath_DL { get; set; }
        public string CoverPicPath_UL { get; set; }

        public double CoverAvgSpeed { get; set; }
        public double CoverSpeedUpTen { get; set; }
        public double CoverCoverRate { get; set; }
        public double CoverAntennaOppositeRate { get; set; }

        public string CoverAvgSpeedStr { get; private set; }
        public string CoverSpeedUpTenStr { get; private set; }
        public string CoverCoverRateStr { get; private set; }
        public string CoverAntennaOppositeRateStr { get; private set; }

        public override void DealWithData()
        {
            base.DealWithData();
            CoverAvgSpeedStr = getVaildData(CoverAvgSpeed);
            CoverSpeedUpTenStr = getVaildData(CoverSpeedUpTen);
            CoverCoverRateStr = getVaildData(CoverCoverRate);
            CoverAntennaOppositeRateStr = getVaildData(CoverAntennaOppositeRate);
        }

        public void CheckIsAccordCover()
        {
            if (CoverCoverRate > 95 && CoverAntennaOppositeRate > 70)
            {
                isAccord = true;
            }
            else
            {
                isAccord = false;
            }
        }
    }

}
