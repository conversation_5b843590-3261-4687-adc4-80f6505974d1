﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc.ZTPoorRSRQRoad
{
    public partial class SettingFormNR : BaseDialog
    {
        public SettingFormNR()
            : base()
        {
            InitializeComponent();
        }

        public SettingFormNR(PoorRsrqRoadCondition condition)
        {
            InitializeComponent();
            setCondition(condition);
        }

        private void setCondition(PoorRsrqRoadCondition condition)
        {
            if (condition == null)
            {
                return;
            }
            numMaxValue.Value = (decimal)condition.MaxRsrq;
            numMinDistance.Value = (decimal)condition.MinCoverRoadDistance;
            numMaxTPDistance.Value = (decimal)condition.Max2TPDistance;
        }

        public PoorRsrqRoadCondition GetCondition()
        {
            PoorRsrqRoadCondition condition = new PoorRsrqRoadCondition();
            condition.MaxRsrq = (float)numMaxValue.Value;
            condition.MinCoverRoadDistance = (double)numMinDistance.Value;
            condition.Max2TPDistance = (double)numMaxTPDistance.Value;
            return condition;
        }

    }
}
