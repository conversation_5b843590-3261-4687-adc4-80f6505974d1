﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class LastRoadReportCustomForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.cbxSys = new DevExpress.XtraEditors.ComboBoxEdit();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.cbxParam = new DevExpress.XtraEditors.ComboBoxEdit();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.cbxParamIdx = new DevExpress.XtraEditors.ComboBoxEdit();
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.labelControl7 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl5 = new DevExpress.XtraEditors.LabelControl();
            this.numMinLastDis = new DevExpress.XtraEditors.SpinEdit();
            this.num2TPMaxDis = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl6 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.groupControl2 = new DevExpress.XtraEditors.GroupControl();
            this.groupControl6 = new DevExpress.XtraEditors.GroupControl();
            this.txtDisSerial = new DevExpress.XtraEditors.TextEdit();
            this.btnDisSerial = new DevExpress.XtraEditors.SimpleButton();
            this.labelControl18 = new DevExpress.XtraEditors.LabelControl();
            this.groupControl5 = new DevExpress.XtraEditors.GroupControl();
            this.listCondition = new System.Windows.Forms.ListBox();
            this.btnRemoveCondition = new DevExpress.XtraEditors.SimpleButton();
            this.labelControl13 = new DevExpress.XtraEditors.LabelControl();
            this.btnAddCondition = new DevExpress.XtraEditors.SimpleButton();
            this.numMaxValue = new DevExpress.XtraEditors.SpinEdit();
            this.numMinValue = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl11 = new DevExpress.XtraEditors.LabelControl();
            this.cbxLogicalType = new DevExpress.XtraEditors.ComboBoxEdit();
            this.labelControl15 = new DevExpress.XtraEditors.LabelControl();
            this.groupControl3 = new DevExpress.XtraEditors.GroupControl();
            this.listDisplay = new System.Windows.Forms.ListBox();
            this.btnAddDisCol = new DevExpress.XtraEditors.SimpleButton();
            this.btnRemoveDisCol = new DevExpress.XtraEditors.SimpleButton();
            this.radioShowValueType = new DevExpress.XtraEditors.RadioGroup();
            this.txtCaption = new DevExpress.XtraEditors.TextEdit();
            this.cbxSysDisplay = new DevExpress.XtraEditors.ComboBoxEdit();
            this.cbxParamDisplay = new DevExpress.XtraEditors.ComboBoxEdit();
            this.labelControl8 = new DevExpress.XtraEditors.LabelControl();
            this.cbxParamIdxDisplay = new DevExpress.XtraEditors.ComboBoxEdit();
            this.labelControl9 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl10 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl14 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl12 = new DevExpress.XtraEditors.LabelControl();
            this.splitContainerControl1 = new DevExpress.XtraEditors.SplitContainerControl();
            this.groupControl4 = new DevExpress.XtraEditors.GroupControl();
            this.listAllReport = new System.Windows.Forms.ListBox();
            this.btnSave = new DevExpress.XtraEditors.SimpleButton();
            this.btnRemoveReport = new DevExpress.XtraEditors.SimpleButton();
            this.btnNewReport = new DevExpress.XtraEditors.SimpleButton();
            ((System.ComponentModel.ISupportInitialize)(this.cbxSys.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbxParam.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbxParamIdx.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numMinLastDis.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.num2TPMaxDis.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).BeginInit();
            this.groupControl2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl6)).BeginInit();
            this.groupControl6.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtDisSerial.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl5)).BeginInit();
            this.groupControl5.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxValue.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMinValue.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbxLogicalType.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl3)).BeginInit();
            this.groupControl3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.radioShowValueType.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtCaption.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbxSysDisplay.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbxParamDisplay.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbxParamIdxDisplay.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).BeginInit();
            this.splitContainerControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl4)).BeginInit();
            this.groupControl4.SuspendLayout();
            this.SuspendLayout();
            // 
            // labelControl2
            // 
            this.labelControl2.Location = new System.Drawing.Point(57, 40);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(52, 14);
            this.labelControl2.TabIndex = 1;
            this.labelControl2.Text = "指标类别:";
            // 
            // cbxSys
            // 
            this.cbxSys.Location = new System.Drawing.Point(124, 37);
            this.cbxSys.Name = "cbxSys";
            this.cbxSys.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cbxSys.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            this.cbxSys.Size = new System.Drawing.Size(208, 21);
            this.cbxSys.TabIndex = 0;
            // 
            // labelControl3
            // 
            this.labelControl3.Location = new System.Drawing.Point(57, 82);
            this.labelControl3.Name = "labelControl3";
            this.labelControl3.Size = new System.Drawing.Size(52, 14);
            this.labelControl3.TabIndex = 1;
            this.labelControl3.Text = "指标名称:";
            // 
            // cbxParam
            // 
            this.cbxParam.Location = new System.Drawing.Point(124, 79);
            this.cbxParam.Name = "cbxParam";
            this.cbxParam.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cbxParam.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            this.cbxParam.Size = new System.Drawing.Size(208, 21);
            this.cbxParam.TabIndex = 0;
            // 
            // labelControl4
            // 
            this.labelControl4.Location = new System.Drawing.Point(29, 124);
            this.labelControl4.Name = "labelControl4";
            this.labelControl4.Size = new System.Drawing.Size(76, 14);
            this.labelControl4.TabIndex = 1;
            this.labelControl4.Text = "指标参数索引:";
            // 
            // cbxParamIdx
            // 
            this.cbxParamIdx.Location = new System.Drawing.Point(124, 121);
            this.cbxParamIdx.Name = "cbxParamIdx";
            this.cbxParamIdx.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cbxParamIdx.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            this.cbxParamIdx.Size = new System.Drawing.Size(208, 21);
            this.cbxParamIdx.TabIndex = 0;
            // 
            // groupControl1
            // 
            this.groupControl1.Controls.Add(this.labelControl7);
            this.groupControl1.Controls.Add(this.labelControl5);
            this.groupControl1.Controls.Add(this.numMinLastDis);
            this.groupControl1.Controls.Add(this.num2TPMaxDis);
            this.groupControl1.Controls.Add(this.labelControl6);
            this.groupControl1.Controls.Add(this.labelControl1);
            this.groupControl1.Dock = System.Windows.Forms.DockStyle.Top;
            this.groupControl1.Location = new System.Drawing.Point(2, 23);
            this.groupControl1.Name = "groupControl1";
            this.groupControl1.Size = new System.Drawing.Size(825, 73);
            this.groupControl1.TabIndex = 6;
            this.groupControl1.Text = "距离";
            // 
            // labelControl7
            // 
            this.labelControl7.Location = new System.Drawing.Point(583, 38);
            this.labelControl7.Name = "labelControl7";
            this.labelControl7.Size = new System.Drawing.Size(22, 14);
            this.labelControl7.TabIndex = 4;
            this.labelControl7.Text = "(米)";
            // 
            // labelControl5
            // 
            this.labelControl5.Location = new System.Drawing.Point(282, 38);
            this.labelControl5.Name = "labelControl5";
            this.labelControl5.Size = new System.Drawing.Size(22, 14);
            this.labelControl5.TabIndex = 4;
            this.labelControl5.Text = "(米)";
            // 
            // numMinLastDis
            // 
            this.numMinLastDis.EditValue = new decimal(new int[] {
            50,
            0,
            0,
            0});
            this.numMinLastDis.Location = new System.Drawing.Point(460, 35);
            this.numMinLastDis.Name = "numMinLastDis";
            this.numMinLastDis.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numMinLastDis.Properties.MaxValue = new decimal(new int[] {
            9999999,
            0,
            0,
            0});
            this.numMinLastDis.Properties.MinValue = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numMinLastDis.Size = new System.Drawing.Size(117, 21);
            this.numMinLastDis.TabIndex = 3;
            this.numMinLastDis.EditValueChanged += new System.EventHandler(this.numMinLastDis_EditValueChanged);
            // 
            // num2TPMaxDis
            // 
            this.num2TPMaxDis.EditValue = new decimal(new int[] {
            20,
            0,
            0,
            0});
            this.num2TPMaxDis.Location = new System.Drawing.Point(159, 35);
            this.num2TPMaxDis.Name = "num2TPMaxDis";
            this.num2TPMaxDis.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.num2TPMaxDis.Properties.MaxValue = new decimal(new int[] {
            9999999,
            0,
            0,
            0});
            this.num2TPMaxDis.Properties.MinValue = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.num2TPMaxDis.Size = new System.Drawing.Size(117, 21);
            this.num2TPMaxDis.TabIndex = 3;
            this.num2TPMaxDis.EditValueChanged += new System.EventHandler(this.num2TPMaxDis_EditValueChanged);
            // 
            // labelControl6
            // 
            this.labelControl6.Location = new System.Drawing.Point(386, 38);
            this.labelControl6.Name = "labelControl6";
            this.labelControl6.Size = new System.Drawing.Size(57, 14);
            this.labelControl6.TabIndex = 2;
            this.labelControl6.Text = "持续距离≥";
            // 
            // labelControl1
            // 
            this.labelControl1.Location = new System.Drawing.Point(15, 38);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(117, 14);
            this.labelControl1.TabIndex = 2;
            this.labelControl1.Text = "相邻的两采样点距离≤";
            // 
            // groupControl2
            // 
            this.groupControl2.Controls.Add(this.groupControl6);
            this.groupControl2.Controls.Add(this.groupControl5);
            this.groupControl2.Controls.Add(this.groupControl1);
            this.groupControl2.Controls.Add(this.cbxLogicalType);
            this.groupControl2.Controls.Add(this.labelControl15);
            this.groupControl2.Dock = System.Windows.Forms.DockStyle.Top;
            this.groupControl2.Location = new System.Drawing.Point(0, 0);
            this.groupControl2.Name = "groupControl2";
            this.groupControl2.Size = new System.Drawing.Size(829, 377);
            this.groupControl2.TabIndex = 7;
            this.groupControl2.Text = "持续条件";
            // 
            // groupControl6
            // 
            this.groupControl6.Controls.Add(this.txtDisSerial);
            this.groupControl6.Controls.Add(this.btnDisSerial);
            this.groupControl6.Controls.Add(this.labelControl18);
            this.groupControl6.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControl6.Location = new System.Drawing.Point(2, 295);
            this.groupControl6.Name = "groupControl6";
            this.groupControl6.Size = new System.Drawing.Size(825, 80);
            this.groupControl6.TabIndex = 13;
            this.groupControl6.Text = "GIS显示";
            // 
            // txtDisSerial
            // 
            this.txtDisSerial.Location = new System.Drawing.Point(124, 37);
            this.txtDisSerial.Name = "txtDisSerial";
            this.txtDisSerial.Properties.ReadOnly = true;
            this.txtDisSerial.Size = new System.Drawing.Size(208, 21);
            this.txtDisSerial.TabIndex = 14;
            // 
            // btnDisSerial
            // 
            this.btnDisSerial.Location = new System.Drawing.Point(355, 34);
            this.btnDisSerial.Name = "btnDisSerial";
            this.btnDisSerial.Size = new System.Drawing.Size(87, 27);
            this.btnDisSerial.TabIndex = 13;
            this.btnDisSerial.Text = "选择";
            this.btnDisSerial.Click += new System.EventHandler(this.BtnDisSerial_Click);
            // 
            // labelControl18
            // 
            this.labelControl18.Location = new System.Drawing.Point(55, 40);
            this.labelControl18.Name = "labelControl18";
            this.labelControl18.Size = new System.Drawing.Size(52, 14);
            this.labelControl18.TabIndex = 7;
            this.labelControl18.Text = "显示指标:";
            // 
            // groupControl5
            // 
            this.groupControl5.Controls.Add(this.listCondition);
            this.groupControl5.Controls.Add(this.cbxSys);
            this.groupControl5.Controls.Add(this.btnRemoveCondition);
            this.groupControl5.Controls.Add(this.labelControl2);
            this.groupControl5.Controls.Add(this.labelControl13);
            this.groupControl5.Controls.Add(this.btnAddCondition);
            this.groupControl5.Controls.Add(this.labelControl3);
            this.groupControl5.Controls.Add(this.cbxParamIdx);
            this.groupControl5.Controls.Add(this.numMaxValue);
            this.groupControl5.Controls.Add(this.labelControl4);
            this.groupControl5.Controls.Add(this.numMinValue);
            this.groupControl5.Controls.Add(this.labelControl11);
            this.groupControl5.Controls.Add(this.cbxParam);
            this.groupControl5.Dock = System.Windows.Forms.DockStyle.Top;
            this.groupControl5.Location = new System.Drawing.Point(2, 96);
            this.groupControl5.Name = "groupControl5";
            this.groupControl5.Size = new System.Drawing.Size(825, 199);
            this.groupControl5.TabIndex = 7;
            this.groupControl5.Text = "指标";
            // 
            // listCondition
            // 
            this.listCondition.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.listCondition.DrawMode = System.Windows.Forms.DrawMode.OwnerDrawFixed;
            this.listCondition.FormattingEnabled = true;
            this.listCondition.ItemHeight = 18;
            this.listCondition.Location = new System.Drawing.Point(460, 26);
            this.listCondition.Name = "listCondition";
            this.listCondition.Size = new System.Drawing.Size(360, 166);
            this.listCondition.TabIndex = 2;
            this.listCondition.DrawItem += new System.Windows.Forms.DrawItemEventHandler(this.listCondition_DrawItem);
            // 
            // btnRemoveCondition
            // 
            this.btnRemoveCondition.Enabled = false;
            this.btnRemoveCondition.Location = new System.Drawing.Point(355, 54);
            this.btnRemoveCondition.Name = "btnRemoveCondition";
            this.btnRemoveCondition.Size = new System.Drawing.Size(87, 27);
            this.btnRemoveCondition.TabIndex = 4;
            this.btnRemoveCondition.Text = "移除条件";
            this.btnRemoveCondition.Click += new System.EventHandler(this.btnRemoveCondition_Click);
            // 
            // labelControl13
            // 
            this.labelControl13.Location = new System.Drawing.Point(196, 166);
            this.labelControl13.Name = "labelControl13";
            this.labelControl13.Size = new System.Drawing.Size(54, 14);
            this.labelControl13.TabIndex = 4;
            this.labelControl13.Text = "≤指标值≤";
            // 
            // btnAddCondition
            // 
            this.btnAddCondition.Enabled = false;
            this.btnAddCondition.Location = new System.Drawing.Point(355, 96);
            this.btnAddCondition.Name = "btnAddCondition";
            this.btnAddCondition.Size = new System.Drawing.Size(87, 27);
            this.btnAddCondition.TabIndex = 4;
            this.btnAddCondition.Text = "添加条件";
            this.btnAddCondition.Click += new System.EventHandler(this.btnAddCondition_Click);
            // 
            // numMaxValue
            // 
            this.numMaxValue.EditValue = new decimal(new int[] {
            20,
            0,
            0,
            0});
            this.numMaxValue.Location = new System.Drawing.Point(266, 163);
            this.numMaxValue.Name = "numMaxValue";
            this.numMaxValue.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numMaxValue.Properties.MaxValue = new decimal(new int[] {
            9999999,
            0,
            0,
            0});
            this.numMaxValue.Properties.MinValue = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numMaxValue.Size = new System.Drawing.Size(65, 21);
            this.numMaxValue.TabIndex = 3;
            // 
            // numMinValue
            // 
            this.numMinValue.EditValue = new decimal(new int[] {
            20,
            0,
            0,
            0});
            this.numMinValue.Location = new System.Drawing.Point(124, 163);
            this.numMinValue.Name = "numMinValue";
            this.numMinValue.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numMinValue.Properties.MaxValue = new decimal(new int[] {
            9999999,
            0,
            0,
            0});
            this.numMinValue.Properties.MinValue = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numMinValue.Size = new System.Drawing.Size(65, 21);
            this.numMinValue.TabIndex = 3;
            // 
            // labelControl11
            // 
            this.labelControl11.Location = new System.Drawing.Point(57, 166);
            this.labelControl11.Name = "labelControl11";
            this.labelControl11.Size = new System.Drawing.Size(52, 14);
            this.labelControl11.TabIndex = 1;
            this.labelControl11.Text = "门限条件:";
            // 
            // cbxLogicalType
            // 
            this.cbxLogicalType.Location = new System.Drawing.Point(161, 2);
            this.cbxLogicalType.Name = "cbxLogicalType";
            this.cbxLogicalType.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cbxLogicalType.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            this.cbxLogicalType.Size = new System.Drawing.Size(117, 21);
            this.cbxLogicalType.TabIndex = 0;
            this.cbxLogicalType.SelectedIndexChanged += new System.EventHandler(this.cbxLogicalType_SelectedIndexChanged);
            // 
            // labelControl15
            // 
            this.labelControl15.Location = new System.Drawing.Point(93, 6);
            this.labelControl15.Name = "labelControl15";
            this.labelControl15.Size = new System.Drawing.Size(52, 14);
            this.labelControl15.TabIndex = 1;
            this.labelControl15.Text = "判断逻辑:";
            // 
            // groupControl3
            // 
            this.groupControl3.Controls.Add(this.listDisplay);
            this.groupControl3.Controls.Add(this.btnAddDisCol);
            this.groupControl3.Controls.Add(this.btnRemoveDisCol);
            this.groupControl3.Controls.Add(this.radioShowValueType);
            this.groupControl3.Controls.Add(this.txtCaption);
            this.groupControl3.Controls.Add(this.cbxSysDisplay);
            this.groupControl3.Controls.Add(this.cbxParamDisplay);
            this.groupControl3.Controls.Add(this.labelControl8);
            this.groupControl3.Controls.Add(this.cbxParamIdxDisplay);
            this.groupControl3.Controls.Add(this.labelControl9);
            this.groupControl3.Controls.Add(this.labelControl10);
            this.groupControl3.Controls.Add(this.labelControl14);
            this.groupControl3.Controls.Add(this.labelControl12);
            this.groupControl3.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControl3.Location = new System.Drawing.Point(0, 377);
            this.groupControl3.Name = "groupControl3";
            this.groupControl3.Size = new System.Drawing.Size(829, 237);
            this.groupControl3.TabIndex = 8;
            this.groupControl3.Text = "报表显示（已预设文件名、经度、纬度列，无需自行定制）";
            // 
            // listDisplay
            // 
            this.listDisplay.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.listDisplay.DrawMode = System.Windows.Forms.DrawMode.OwnerDrawFixed;
            this.listDisplay.FormattingEnabled = true;
            this.listDisplay.ItemHeight = 18;
            this.listDisplay.Location = new System.Drawing.Point(475, 26);
            this.listDisplay.Name = "listDisplay";
            this.listDisplay.Size = new System.Drawing.Size(349, 202);
            this.listDisplay.TabIndex = 2;
            this.listDisplay.DrawItem += new System.Windows.Forms.DrawItemEventHandler(this.listDisplay_DrawItem);
            // 
            // btnAddDisCol
            // 
            this.btnAddDisCol.Enabled = false;
            this.btnAddDisCol.Location = new System.Drawing.Point(357, 103);
            this.btnAddDisCol.Name = "btnAddDisCol";
            this.btnAddDisCol.Size = new System.Drawing.Size(87, 27);
            this.btnAddDisCol.TabIndex = 11;
            this.btnAddDisCol.Text = "添加列";
            this.btnAddDisCol.Click += new System.EventHandler(this.btnAddDisCol_Click);
            // 
            // btnRemoveDisCol
            // 
            this.btnRemoveDisCol.Enabled = false;
            this.btnRemoveDisCol.Location = new System.Drawing.Point(357, 61);
            this.btnRemoveDisCol.Name = "btnRemoveDisCol";
            this.btnRemoveDisCol.Size = new System.Drawing.Size(87, 27);
            this.btnRemoveDisCol.TabIndex = 12;
            this.btnRemoveDisCol.Text = "移除列";
            this.btnRemoveDisCol.Click += new System.EventHandler(this.btnRemoveDisCol_Click);
            // 
            // radioShowValueType
            // 
            this.radioShowValueType.Enabled = false;
            this.radioShowValueType.Location = new System.Drawing.Point(126, 195);
            this.radioShowValueType.Name = "radioShowValueType";
            this.radioShowValueType.Properties.Items.AddRange(new DevExpress.XtraEditors.Controls.RadioGroupItem[] {
            new DevExpress.XtraEditors.Controls.RadioGroupItem(null, "平均值"),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(null, "最小值"),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(null, "最大值")});
            this.radioShowValueType.Size = new System.Drawing.Size(208, 29);
            this.radioShowValueType.TabIndex = 9;
            this.radioShowValueType.SelectedIndexChanged += new System.EventHandler(this.radioShowValueType_SelectedIndexChanged);
            // 
            // txtCaption
            // 
            this.txtCaption.Location = new System.Drawing.Point(126, 155);
            this.txtCaption.Name = "txtCaption";
            this.txtCaption.Size = new System.Drawing.Size(208, 21);
            this.txtCaption.TabIndex = 8;
            this.txtCaption.EditValueChanged += new System.EventHandler(this.txtCaption_EditValueChanged);
            // 
            // cbxSysDisplay
            // 
            this.cbxSysDisplay.Location = new System.Drawing.Point(126, 47);
            this.cbxSysDisplay.Name = "cbxSysDisplay";
            this.cbxSysDisplay.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cbxSysDisplay.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            this.cbxSysDisplay.Size = new System.Drawing.Size(208, 21);
            this.cbxSysDisplay.TabIndex = 2;
            // 
            // cbxParamDisplay
            // 
            this.cbxParamDisplay.Location = new System.Drawing.Point(126, 83);
            this.cbxParamDisplay.Name = "cbxParamDisplay";
            this.cbxParamDisplay.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cbxParamDisplay.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            this.cbxParamDisplay.Size = new System.Drawing.Size(208, 21);
            this.cbxParamDisplay.TabIndex = 3;
            // 
            // labelControl8
            // 
            this.labelControl8.Location = new System.Drawing.Point(31, 122);
            this.labelControl8.Name = "labelControl8";
            this.labelControl8.Size = new System.Drawing.Size(76, 14);
            this.labelControl8.TabIndex = 5;
            this.labelControl8.Text = "指标参数索引:";
            // 
            // cbxParamIdxDisplay
            // 
            this.cbxParamIdxDisplay.Location = new System.Drawing.Point(126, 119);
            this.cbxParamIdxDisplay.Name = "cbxParamIdxDisplay";
            this.cbxParamIdxDisplay.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cbxParamIdxDisplay.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            this.cbxParamIdxDisplay.Size = new System.Drawing.Size(208, 21);
            this.cbxParamIdxDisplay.TabIndex = 4;
            // 
            // labelControl9
            // 
            this.labelControl9.Location = new System.Drawing.Point(59, 86);
            this.labelControl9.Name = "labelControl9";
            this.labelControl9.Size = new System.Drawing.Size(52, 14);
            this.labelControl9.TabIndex = 6;
            this.labelControl9.Text = "指标名称:";
            // 
            // labelControl10
            // 
            this.labelControl10.Location = new System.Drawing.Point(58, 50);
            this.labelControl10.Name = "labelControl10";
            this.labelControl10.Size = new System.Drawing.Size(52, 14);
            this.labelControl10.TabIndex = 7;
            this.labelControl10.Text = "指标类别:";
            // 
            // labelControl14
            // 
            this.labelControl14.Location = new System.Drawing.Point(72, 201);
            this.labelControl14.Name = "labelControl14";
            this.labelControl14.Size = new System.Drawing.Size(40, 14);
            this.labelControl14.TabIndex = 1;
            this.labelControl14.Text = "值类型:";
            // 
            // labelControl12
            // 
            this.labelControl12.Location = new System.Drawing.Point(72, 159);
            this.labelControl12.Name = "labelControl12";
            this.labelControl12.Size = new System.Drawing.Size(40, 14);
            this.labelControl12.TabIndex = 1;
            this.labelControl12.Text = "列标题:";
            // 
            // splitContainerControl1
            // 
            this.splitContainerControl1.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Default;
            this.splitContainerControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl1.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl1.LookAndFeel.SkinName = "Office 2007 Blue";
            this.splitContainerControl1.LookAndFeel.UseDefaultLookAndFeel = false;
            this.splitContainerControl1.Name = "splitContainerControl1";
            this.splitContainerControl1.Panel1.Controls.Add(this.groupControl4);
            this.splitContainerControl1.Panel1.Text = "Panel1";
            this.splitContainerControl1.Panel2.AutoScroll = true;
            this.splitContainerControl1.Panel2.Controls.Add(this.groupControl3);
            this.splitContainerControl1.Panel2.Controls.Add(this.groupControl2);
            this.splitContainerControl1.Panel2.Text = "Panel2";
            this.splitContainerControl1.Size = new System.Drawing.Size(1132, 618);
            this.splitContainerControl1.SplitterPosition = 295;
            this.splitContainerControl1.TabIndex = 9;
            this.splitContainerControl1.Text = "splitContainerControl1";
            // 
            // groupControl4
            // 
            this.groupControl4.Controls.Add(this.listAllReport);
            this.groupControl4.Controls.Add(this.btnSave);
            this.groupControl4.Controls.Add(this.btnRemoveReport);
            this.groupControl4.Controls.Add(this.btnNewReport);
            this.groupControl4.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControl4.Location = new System.Drawing.Point(0, 0);
            this.groupControl4.Name = "groupControl4";
            this.groupControl4.Size = new System.Drawing.Size(295, 614);
            this.groupControl4.TabIndex = 7;
            this.groupControl4.Text = "已有报告";
            // 
            // listAllReport
            // 
            this.listAllReport.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.listAllReport.DrawMode = System.Windows.Forms.DrawMode.OwnerDrawFixed;
            this.listAllReport.FormattingEnabled = true;
            this.listAllReport.ItemHeight = 12;
            this.listAllReport.Location = new System.Drawing.Point(5, 26);
            this.listAllReport.Name = "listAllReport";
            this.listAllReport.Size = new System.Drawing.Size(287, 520);
            this.listAllReport.Sorted = true;
            this.listAllReport.TabIndex = 2;
            this.listAllReport.DrawItem += new System.Windows.Forms.DrawItemEventHandler(this.listAllReport_DrawItem);
            this.listAllReport.DoubleClick += new System.EventHandler(this.listAllReport_DoubleClick);
            // 
            // btnSave
            // 
            this.btnSave.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnSave.Location = new System.Drawing.Point(203, 572);
            this.btnSave.Name = "btnSave";
            this.btnSave.Size = new System.Drawing.Size(87, 27);
            this.btnSave.TabIndex = 1;
            this.btnSave.Text = "保存";
            this.btnSave.Click += new System.EventHandler(this.btnSave_Click);
            // 
            // btnRemoveReport
            // 
            this.btnRemoveReport.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnRemoveReport.Enabled = false;
            this.btnRemoveReport.Location = new System.Drawing.Point(104, 572);
            this.btnRemoveReport.Name = "btnRemoveReport";
            this.btnRemoveReport.Size = new System.Drawing.Size(87, 27);
            this.btnRemoveReport.TabIndex = 1;
            this.btnRemoveReport.Text = "删除";
            this.btnRemoveReport.Click += new System.EventHandler(this.btnRemoveReport_Click);
            // 
            // btnNewReport
            // 
            this.btnNewReport.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnNewReport.Location = new System.Drawing.Point(5, 572);
            this.btnNewReport.Name = "btnNewReport";
            this.btnNewReport.Size = new System.Drawing.Size(87, 27);
            this.btnNewReport.TabIndex = 1;
            this.btnNewReport.Text = "新建";
            this.btnNewReport.Click += new System.EventHandler(this.btnNewReport_Click);
            // 
            // LastRoadReportCustomForm
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(96F, 96F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Dpi;
            this.ClientSize = new System.Drawing.Size(1132, 618);
            this.Controls.Add(this.splitContainerControl1);
            this.MinimumSize = new System.Drawing.Size(986, 656);
            this.Name = "LastRoadReportCustomForm";
            this.Text = "持续道路指标报告定制";
            ((System.ComponentModel.ISupportInitialize)(this.cbxSys.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbxParam.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbxParamIdx.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            this.groupControl1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numMinLastDis.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.num2TPMaxDis.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).EndInit();
            this.groupControl2.ResumeLayout(false);
            this.groupControl2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl6)).EndInit();
            this.groupControl6.ResumeLayout(false);
            this.groupControl6.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtDisSerial.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl5)).EndInit();
            this.groupControl5.ResumeLayout(false);
            this.groupControl5.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxValue.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMinValue.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbxLogicalType.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl3)).EndInit();
            this.groupControl3.ResumeLayout(false);
            this.groupControl3.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.radioShowValueType.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtCaption.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbxSysDisplay.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbxParamDisplay.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbxParamIdxDisplay.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).EndInit();
            this.splitContainerControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl4)).EndInit();
            this.groupControl4.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.ComboBoxEdit cbxSys;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.ComboBoxEdit cbxParam;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private DevExpress.XtraEditors.ComboBoxEdit cbxParamIdx;
        private DevExpress.XtraEditors.GroupControl groupControl3;
        private DevExpress.XtraEditors.GroupControl groupControl2;
        private DevExpress.XtraEditors.GroupControl groupControl1;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.LabelControl labelControl7;
        private DevExpress.XtraEditors.LabelControl labelControl5;
        private DevExpress.XtraEditors.SpinEdit numMinLastDis;
        private DevExpress.XtraEditors.SpinEdit num2TPMaxDis;
        private DevExpress.XtraEditors.LabelControl labelControl6;
        private DevExpress.XtraEditors.SimpleButton btnRemoveCondition;
        private DevExpress.XtraEditors.SimpleButton btnAddCondition;
        private DevExpress.XtraEditors.TextEdit txtCaption;
        private DevExpress.XtraEditors.ComboBoxEdit cbxSysDisplay;
        private DevExpress.XtraEditors.ComboBoxEdit cbxParamDisplay;
        private DevExpress.XtraEditors.LabelControl labelControl8;
        private DevExpress.XtraEditors.ComboBoxEdit cbxParamIdxDisplay;
        private DevExpress.XtraEditors.LabelControl labelControl9;
        private DevExpress.XtraEditors.LabelControl labelControl10;
        private DevExpress.XtraEditors.LabelControl labelControl12;
        private DevExpress.XtraEditors.LabelControl labelControl11;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl1;
        private DevExpress.XtraEditors.SimpleButton btnNewReport;
        private DevExpress.XtraEditors.LabelControl labelControl13;
        private DevExpress.XtraEditors.SpinEdit numMaxValue;
        private DevExpress.XtraEditors.SpinEdit numMinValue;
        private DevExpress.XtraEditors.GroupControl groupControl4;
        private System.Windows.Forms.ListBox listAllReport;
        private DevExpress.XtraEditors.SimpleButton btnSave;
        private DevExpress.XtraEditors.RadioGroup radioShowValueType;
        private DevExpress.XtraEditors.LabelControl labelControl14;
        private DevExpress.XtraEditors.SimpleButton btnAddDisCol;
        private DevExpress.XtraEditors.SimpleButton btnRemoveDisCol;
        private DevExpress.XtraEditors.LabelControl labelControl15;
        private DevExpress.XtraEditors.GroupControl groupControl5;
        private DevExpress.XtraEditors.ComboBoxEdit cbxLogicalType;
        private System.Windows.Forms.ListBox listCondition;
        private System.Windows.Forms.ListBox listDisplay;
        private DevExpress.XtraEditors.SimpleButton btnRemoveReport;
        private DevExpress.XtraEditors.GroupControl groupControl6;
        private DevExpress.XtraEditors.LabelControl labelControl18;
        private DevExpress.XtraEditors.TextEdit txtDisSerial;
        private DevExpress.XtraEditors.SimpleButton btnDisSerial;
    }
}