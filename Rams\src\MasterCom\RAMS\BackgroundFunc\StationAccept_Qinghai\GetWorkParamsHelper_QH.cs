﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Text;

namespace MasterCom.RAMS.BackgroundFunc
{
    class GetWorkParamsHelper_QH
    {
        protected GetWorkParamsHelper_QH()
        {

        }

        public static Dictionary<string, Dictionary<int, BtsWorkParam_QH>> GetWorkParamsInfo(
            StationAcceptAutoSet_QH funcSet)
        {
            // Dictionary<地市, Dictionary<基站编号, BtsWorkParam_QH>> 
            Dictionary<string, Dictionary<int, BtsWorkParam_QH>> workParamSumDic = new Dictionary<string, Dictionary<int, BtsWorkParam_QH>>();
            try
            {
                readAndUploadWorkParams(funcSet.CellParamFolderPath);//读取并上传资管推送来的待验收工参信息

                WorkParamsQuery_QH queryParams = new WorkParamsQuery_QH(funcSet.WorkParamTime_Begin, funcSet.WorkParamTime_End);
                queryParams.Query();//从服务端查询需要自动导出报告的站点工参
                workParamSumDic = queryParams.WorkParamSumDic;
            }
            catch (Exception ex)
            {
                reportError(ex);
            }
            reportInfo(string.Format("查询到{0}个地市的待评估工参数据", workParamSumDic.Count));
            return workParamSumDic;
        }

        /// <summary>
        /// 将文件夹下的文件备份
        /// </summary>
        /// <param name="srcFolder">需要备份的文件夹</param>
        /// <param name="fileNameList">该文件夹下需要备份的文件</param>
        public static void BcpFiles(string srcFolder, List<string> fileNameList)
        {
            string bcpFolderPath = srcFolder + "\\工参备份\\" + DateTime.Now.ToString("yyMMdd");
            if (!Directory.Exists(bcpFolderPath))
            {
                Directory.CreateDirectory(bcpFolderPath);
            }

            foreach (string fileName in fileNameList)//备份工参文件后删除
            {
                string filePathNew = fileName.Replace(srcFolder, bcpFolderPath);
                string fileExtension = System.IO.Path.GetExtension(fileName); 

                while (File.Exists(filePathNew))
                {
                    System.Threading.Thread.Sleep(1000);
                    string fileTitle = System.IO.Path.GetFileNameWithoutExtension(filePathNew);
                    filePathNew = filePathNew.Replace(string.Format("{0}{1}", fileTitle, fileExtension)
                        , string.Format("{0}_{1}{2}", fileTitle, DateTime.Now.ToString("HHmmss"), fileExtension));
                }
                File.Copy(fileName, filePathNew, true);
                File.Delete(fileName);
            }
        }

        #region 读取并上传资管推送来的待验收工参信息
        protected static void readAndUploadWorkParams(string strCellParamFolderPath)
        {
            try
            {
                reportInfo("开始读取待评估对象数据...");

                List<string> fileNameList = new List<string>();
                if (System.IO.Directory.Exists(strCellParamFolderPath))
                {
                    System.IO.DirectoryInfo dinfo = new System.IO.DirectoryInfo(strCellParamFolderPath);
                    foreach (System.IO.FileInfo file in dinfo.GetFiles("*.csv", System.IO.SearchOption.TopDirectoryOnly))
                    {
                        fileNameList.Add(file.FullName);
                    }
                }
                if (fileNameList.Count > 0)
                {
                    Dictionary<string, CellWorkParam_QH> cellParamInfoDic = new Dictionary<string, CellWorkParam_QH>();

                    foreach (string fileName in fileNameList)//逐个读取待上传工参文件
                    {
                        getWorkParamFormExcle(fileName, ref cellParamInfoDic);
                    }

                    reportInfo("正在上传待评估对象数据...");
                    UpLoadWorkParams_QH upLoadParams = new UpLoadWorkParams_QH(cellParamInfoDic);
                    upLoadParams.Query();//上传工参

                    BcpFiles(strCellParamFolderPath, fileNameList);
                }
                else
                {
                    reportInfo("未找到指定目录下的待上传工参文件");
                }
            }
            catch (Exception ex)
            {
                reportError(ex);
            }
        }
        protected static bool getWorkParamFormExcle(string fileName
            , ref Dictionary<string, CellWorkParam_QH> cellParamInfoDic)
        {
            try
            {
                reportInfo(string.Format("正在读取文件 {0} 信息...", fileName));
                int reReadCount = 0;
                while (reReadCount < 6 && FileStatus.FileIsOpen(fileName) == 1)
                {
                    System.Threading.Thread.Sleep(10000);
                    reReadCount++;
                }

                CSVReader reader = new CSVReader(fileName);
                List<string[]> listData = reader.GetAllData();
                if (listData != null && listData.Count > 0)
                {
                    int index = 0;
                    foreach (String[] rowDataVec in listData)
                    {
                        try
                        {
                            index++;
                            string firstValue = rowDataVec[0];
                            if (string.IsNullOrEmpty(firstValue) || firstValue == "小区名")
                            {
                                continue;
                            }
                            CellWorkParam_QH cellParam = new CellWorkParam_QH();
                            cellParam.CellName = firstValue;
                            cellParam.CellID = getIntValue(rowDataVec[1]);
                            cellParam.BtsName = rowDataVec[2];
                            cellParam.CGI = rowDataVec[3];
                            cellParam.Tac = getIntValue(rowDataVec[4]);
                            cellParam.Earfcn = getIntValue(rowDataVec[5]);
                            cellParam.Pci = getIntValue(rowDataVec[6]);
                            cellParam.DistrictName = rowDataVec[7];
                            cellParam.CoverTypeDes = rowDataVec[8];
                            cellParam.CoverScene = rowDataVec[9];
                            cellParam.ENodeBID = getIntValue(rowDataVec[10]);
                            cellParam.Longitude = Convert.ToDouble(rowDataVec[11].Trim());
                            cellParam.Latitude = Convert.ToDouble(rowDataVec[12].Trim());
                            cellParam.Direction = getIntValue(rowDataVec[13]);
                            cellParam.Downward = getIntValue(rowDataVec[14]);
                            cellParam.Altitude = getIntValue(rowDataVec[15]);

                            cellParam.Eci = cellParam.ENodeBID * 256 + cellParam.CellID;
                            cellParamInfoDic[cellParam.Token] = cellParam;
                        }
                        catch(Exception ex)
                        {
                            reportInfo(string.Format("第{0}行工参格式有误！：{1}", index, ex.Message));
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                reportError(ex);
                return false;
            }
            return true;
        }

        protected static int getIntValue(string strValue)
        {
            if (string.IsNullOrEmpty(strValue))
            {
                return 0;
            }
            if (strValue.Contains("."))
            {
                double dValue;
                if (double.TryParse(strValue.Trim(), out dValue))
                {
                    return (int)dValue;
                }
            }
            else
            {
                int intValue;
                if (int.TryParse(strValue.Trim(), out intValue))
                {
                    return intValue;
                }
            }
            return 0;
        }
        #endregion
        protected static void reportInfo(string str)
        {
            BackgroundFuncManager.GetInstance().ReportBackgroundInfo(str);
        }
        protected static void reportError(Exception ex)
        {
            BackgroundFuncManager.GetInstance().ReportBackgroundError(ex);
        }
    }
    public class BtsWorkParam_QH : BtsWorkParamBase
    {
        public BtsWorkParam_QH(CellWorkParam_QH cellParam)
            : base(cellParam)
        {
            this.Longitude = cellParam.Longitude;
            this.Latitude = cellParam.Latitude;
        }
        public void AddCellParamsInfo(CellWorkParam_QH info)
        {
            CellWorkParamDic[info.CellID] = info;
        }
    }
    public class CellWorkParam_QH : CellWorkParamBase
    {
        public int Eci { get; set; }
        public int Earfcn { get; set; }
        public int Pci { get; set; }
        public int Direction { get; set; }
        public int Downward { get; set; }
        public int Altitude { get; set; }
        public double Longitude { get; set; }
        public double Latitude { get; set; }
        public string Token { get { return string.Format("{0}-{1}", Tac, Eci); } }
        public string StrDes { get; set; }
    }

    public class UpLoadWorkParams_QH : DiySqlMultiNonQuery
    {
        readonly Dictionary<string, CellWorkParam_QH> cellParamInfoDic;
        public UpLoadWorkParams_QH(Dictionary<string, CellWorkParam_QH> cellParamInfoDic)
            : base()
        {
            MainDB = true;
            this.cellParamInfoDic = cellParamInfoDic;
        }
        protected override string getSqlTextString()
        {
            StringBuilder strb = new StringBuilder();
            foreach (CellWorkParam_QH cellInfo in cellParamInfoDic.Values)
            {
                strb.Append(string.Format("delete from tb_btscheck_qinghai_cfg_cell where cellName = '{0}';"
                   , cellInfo.CellName));
                strb.Append(string.Format(@"insert into [tb_btscheck_qinghai_cfg_cell]([cellName],[cellid],[btsName]
,[cgi],[tac],[earfcn],[pci],[districtName],[coverType],[coverScene],[enodebid],[ilongitude],[ilatitude]
,[direction],[downward],[altitude]) values 
      ('{0}',{1}, '{2}', '{3}', {4}, {5}, {6}, '{7}', '{8}', '{9}', {10}, {11}, {12}, {13}, {14}, {15})"
                    , cellInfo.CellName, cellInfo.CellID, cellInfo.BtsName, cellInfo.CGI, cellInfo.Tac
                    , cellInfo.Earfcn, cellInfo.Pci, cellInfo.DistrictName, cellInfo.CoverTypeDes, cellInfo.CoverScene
                    , cellInfo.ENodeBID, cellInfo.Longitude * 10000000, cellInfo.Latitude * 10000000
                    , cellInfo.Direction, cellInfo.Downward, cellInfo.Altitude));
            }
            return strb.ToString();
        }
    }
    public class WorkParamsQuery_QH : DIYSQLBase
    {
        readonly DateTime beginTime;
        readonly DateTime endTime;

        // Dictionary<地市, Dictionary<基站编号, BtsWorkParam_QH>> 
        public Dictionary<string, Dictionary<int, BtsWorkParam_QH>> WorkParamSumDic { get; set; } = new Dictionary<string, Dictionary<int, BtsWorkParam_QH>>();
        public WorkParamsQuery_QH(DateTime beginTime, DateTime endTime)
        {
            MainDB = true;
            this.beginTime = beginTime;
            this.endTime = endTime;
        }
        protected override string getSqlTextString()
        {
            return string.Format(@"select [cellName],[cellid],[btsName],[cgi],[tac],[earfcn],[pci]
,[districtName],[coverType],[coverScene],[enodebid],[ilongitude],[ilatitude],[updateTime],[strdes] 
,[direction],[downward],[altitude]
from tb_btscheck_qinghai_cfg_cell 
where updateTime between '{0}' and '{1}'", beginTime.ToString(), endTime.ToString());
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            int i = 0;
            E_VType[] arr = new E_VType[18];
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_Int;
            arr[i] = E_VType.E_Int;
            return arr;
        }
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            WorkParamSumDic.Clear();
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    CellWorkParam_QH cellInfo = new CellWorkParam_QH();
                    cellInfo.CellName = package.Content.GetParamString();
                    cellInfo.CellID = package.Content.GetParamInt();
                    cellInfo.BtsName = package.Content.GetParamString();
                    cellInfo.CGI = package.Content.GetParamString();
                    cellInfo.Tac = package.Content.GetParamInt();
                    cellInfo.Earfcn = package.Content.GetParamInt();
                    cellInfo.Pci = package.Content.GetParamInt();
                    cellInfo.DistrictName = package.Content.GetParamString();
                    cellInfo.CoverTypeDes = package.Content.GetParamString();
                    cellInfo.CoverScene = package.Content.GetParamString();
                    cellInfo.ENodeBID = package.Content.GetParamInt();
                    int iLongitude = package.Content.GetParamInt();
                    int iLatitude = package.Content.GetParamInt();
                    cellInfo.Longitude = (double)iLongitude / 10000000;
                    cellInfo.Latitude = (double)iLatitude / 10000000;
                    string upDateTime = package.Content.GetParamString();
                    cellInfo.StrDes = package.Content.GetParamString();
                    cellInfo.Direction = package.Content.GetParamInt();
                    cellInfo.Downward = package.Content.GetParamInt();
                    cellInfo.Altitude = package.Content.GetParamInt();

                    cellInfo.Eci = cellInfo.ENodeBID * 256 + cellInfo.CellID;
                    LTECell lteCell = CellManager.GetInstance().GetLTECellLatest(cellInfo.CellName);
                    if (lteCell != null)//待验收工参中没有的信息，关联客户端本地工参补全
                    {
                        cellInfo.SectorID = lteCell.SectorID;
                    }

                    Dictionary<int, BtsWorkParam_QH> btsInfoIDic;
                    if (!WorkParamSumDic.TryGetValue(cellInfo.DistrictName, out btsInfoIDic))
                    {
                        btsInfoIDic = new Dictionary<int, BtsWorkParam_QH>();
                        WorkParamSumDic.Add(cellInfo.DistrictName, btsInfoIDic);
                    }
                    BtsWorkParam_QH btsInfo;
                    if (!btsInfoIDic.TryGetValue(cellInfo.ENodeBID, out btsInfo))
                    {
                        btsInfo = new BtsWorkParam_QH(cellInfo);
                        btsInfoIDic.Add(cellInfo.ENodeBID, btsInfo);
                    }
                    btsInfo.DateTimeDes = upDateTime;
                    btsInfo.AddCellParamsInfo(cellInfo);
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    break;
                }
            }
        }
    }

    public class UpdateWorkParamDes : DiySqlMultiNonQuery
    {
        readonly BtsWorkParam_QH btsParam;
        public UpdateWorkParamDes(BtsWorkParam_QH btsParam)
        {
            MainDB = true;
            this.btsParam = btsParam;
        }
        protected override string getSqlTextString()
        {
            StringBuilder strb = new StringBuilder();
            foreach (CellWorkParamBase info in btsParam.CellWorkParamDic.Values)
            {
                CellWorkParam_QH cellParam = info as CellWorkParam_QH;
                strb.Append(string.Format("update tb_btscheck_qinghai_cfg_cell set strdes = '{0}' where cgi = '{1}';"
                    , cellParam.StrDes, cellParam.CGI));
            }
            return strb.ToString();
        }
    }
}
