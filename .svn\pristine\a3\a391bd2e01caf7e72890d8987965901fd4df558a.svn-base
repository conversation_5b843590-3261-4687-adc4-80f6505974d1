﻿using MasterCom.RAMS.Func;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTRailWayResultForm : MinCloseForm
    {
        public ZTRailWayResultForm(MainModel mainModel)
            : base(mainModel)
        {
            InitializeComponent();
            DisposeWhenClose = true;
        }

        public void FillData(List<ZTRailWayZWInfo> lsZw)
        {
            InitColums();
            this.dataGv.Rows.Clear();

            int idx = 1;

            DataGridViewRow dataGirdViewRow = null;
            foreach (ZTRailWayZWInfo zwInfo in lsZw)
            {
                dataGirdViewRow = dataGv.Rows[dataGv.Rows.Add(1)];
                dataGirdViewRow.Cells["Idx"].Value = idx++;
                dataGirdViewRow.Cells["City"].Value = zwInfo.City;
                dataGirdViewRow.Cells["FileName"].Value = zwInfo.FileName;
                dataGirdViewRow.Cells["Net"].Value = zwInfo.strNetType;
                dataGirdViewRow.Cells["AreaType"].Value = zwInfo.strAreaType;

                dataGirdViewRow.Cells["SampleNum"].Value = zwInfo.iTotalSample;
                dataGirdViewRow.Cells["iTestTime"].Value = zwInfo.TotalTime;
                dataGirdViewRow.Cells["sTime"].Value = zwInfo.STime;
                dataGirdViewRow.Cells["eTime"].Value = zwInfo.ETime;
                dataGirdViewRow.Cells["ZwSampleNum"].Value = zwInfo.iZWTotalSample;
                dataGirdViewRow.Cells["ZwSampleTimeRate"].Value = zwInfo.ZWTimeRate;
                dataGirdViewRow.Cells["ZwSampleTime"].Value = zwInfo.ZWTime;
                dataGirdViewRow.Cells["iShiftNum"].Value = zwInfo.iShiftNum;
            }
            lsZw.Clear();
        }

        private void InitColums()
        {
            this.dataGv.Columns.Clear();
            this.dataGv.Columns.Add("Idx", "序号");
            this.dataGv.Columns.Add("City", "地市");
            this.dataGv.Columns.Add("FileName", "文件名");
            this.dataGv.Columns.Add("Net", "网络类型");
            this.dataGv.Columns.Add("AreaType", "区域信息");
            this.dataGv.Columns.Add("SampleNum", "总采样点数");
            this.dataGv.Columns.Add("iTestTime", "测试时长(s)");
            this.dataGv.Columns.Add("sTime", "起始时间");
            this.dataGv.Columns.Add("eTime", "结束时间");
            this.dataGv.Columns.Add("ZwSampleNum", "专网采样点数");
            this.dataGv.Columns.Add("ZwSampleTimeRate", "专网占用时长占比");
            this.dataGv.Columns.Add("ZwSampleTime", "专网占用时长");
            this.dataGv.Columns.Add("iShiftNum", "专网小区切出次数");
        }

        private void exportExcel_Click(object sender, EventArgs e)
        {
            MasterCom.Util.ExcelNPOIManager.ExportToExcel(this.dataGv);
        }
    }
}
