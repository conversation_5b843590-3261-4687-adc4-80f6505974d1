﻿namespace MasterCom.RAMS.Func
{
    partial class MapForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(MapForm));
            this.repositoryItemComboBox1 = new DevExpress.XtraEditors.Repository.RepositoryItemComboBox();
            this.statusStrip = new System.Windows.Forms.StatusStrip();
            this.toolStripStatusLabelX = new System.Windows.Forms.ToolStripStatusLabel();
            this.toolStripStatusLabelY = new System.Windows.Forms.ToolStripStatusLabel();
            this.toolStripStatusLabelScale = new System.Windows.Forms.ToolStripStatusLabel();
            this.tsBtnOffset = new System.Windows.Forms.ToolStripDropDownButton();
            this.tsOffset = new System.Windows.Forms.ToolStripDropDown();
            this.toolStripStatusLabel = new System.Windows.Forms.ToolStripStatusLabel();
            this.toolStripStatusLabelZoom = new System.Windows.Forms.ToolStripStatusLabel();
            this.toolStripStatusLabelSample = new System.Windows.Forms.ToolStripStatusLabel();
            this.toolStripStatusLabelRegionInfo = new System.Windows.Forms.ToolStripStatusLabel();
            this.contextMenuMap = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miMapReplayFile = new System.Windows.Forms.ToolStripMenuItem();
            this.miReplayEvent = new System.Windows.Forms.ToolStripMenuItem();
            this.miReplayEventBothSides = new System.Windows.Forms.ToolStripMenuItem();
            this.miAnaES = new System.Windows.Forms.ToolStripMenuItem();
            this.miReplayTestPoint = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator1 = new System.Windows.Forms.ToolStripSeparator();
            this.miShowFlyLines = new System.Windows.Forms.ToolStripMenuItem();
            this.miViewCellInfo = new System.Windows.Forms.ToolStripMenuItem();
            this.miBTSLineSetting = new System.Windows.Forms.ToolStripMenuItem();
            this.miShowCoCellsByDistance = new System.Windows.Forms.ToolStripMenuItem();
            this.miShowAdjCellsByDistance = new System.Windows.Forms.ToolStripMenuItem();
            this.miDrawEventDateLbl = new System.Windows.Forms.ToolStripMenuItem();
            this.miAdjustCellSize = new System.Windows.Forms.ToolStripMenuItem();
            this.miViewSaveRegion = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem6 = new System.Windows.Forms.ToolStripSeparator();
            this.miViewBlackBlock = new System.Windows.Forms.ToolStripMenuItem();
            this.miViewEventBlockInfo = new System.Windows.Forms.ToolStripMenuItem();
            this.panelMap = new System.Windows.Forms.Panel();
            this.btnCmpRelatedRegionOK = new System.Windows.Forms.Button();
            this.btnCQTImgPosSettingOK = new System.Windows.Forms.Button();
            this.mapControl = new AxMapWinGIS.AxMap();
            this.barManager = new DevExpress.XtraBars.BarManager(this.components);
            this.mapFormToolBar = new DevExpress.XtraBars.Bar();
            this.tool_Select = new DevExpress.XtraBars.BarCheckItem();
            this.tool_ZoomOut = new DevExpress.XtraBars.BarCheckItem();
            this.tool_ZoomIn = new DevExpress.XtraBars.BarCheckItem();
            this.tool_Pan = new DevExpress.XtraBars.BarCheckItem();
            this.tool_All = new DevExpress.XtraBars.BarButtonItem();
            this.tool_AddRect = new DevExpress.XtraBars.BarCheckItem();
            this.tool_AddPolygon = new DevExpress.XtraBars.BarCheckItem();
            this.barBtnMeasure = new DevExpress.XtraBars.BarButtonItem();
            this.popupMenuMeasure = new DevExpress.XtraBars.PopupMenu(this.components);
            this.tool_Clear = new DevExpress.XtraBars.BarButtonItem();
            this.tool_Info = new DevExpress.XtraBars.BarCheckItem();
            this.tool_AddPointMark = new DevExpress.XtraBars.BarCheckItem();
            this.tool_AddLineMark = new DevExpress.XtraBars.BarCheckItem();
            this.tool_RectangleFlyLine = new DevExpress.XtraBars.BarCheckItem();
            this.tool_PolygonFlyLine = new DevExpress.XtraBars.BarCheckItem();
            this.mapFormOtherBar = new DevExpress.XtraBars.Bar();
            this.barBtnLayerCtrl = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnTestPointArrow = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnLoadPlanImg = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnGE = new DevExpress.XtraBars.BarButtonItem();
            this.popupMenuGE = new DevExpress.XtraBars.PopupMenu(this.components);
            this.barTrackLayerTransparent = new DevExpress.XtraBars.BarEditItem();
            this.trackBarBlindTransparentRibbon = new DevExpress.XtraEditors.Repository.RepositoryItemTrackBar();
            this.barEditItemLocation = new DevExpress.XtraBars.BarEditItem();
            this.comboBoxLocationRibbon = new DevExpress.XtraEditors.Repository.RepositoryItemComboBox();
            this.barBtnLocation = new DevExpress.XtraBars.BarButtonItem();
            this.popupMenuMap = new DevExpress.XtraBars.PopupMenu(this.components);
            this.barBtnCellInterfere = new DevExpress.XtraBars.BarButtonItem();
            this.barSubItemCellInterfere = new DevExpress.XtraBars.BarSubItem();
            this.barCellBcch = new DevExpress.XtraBars.BarButtonItem();
            this.barCellTch = new DevExpress.XtraBars.BarButtonItem();
            this.barCellBcchTch = new DevExpress.XtraBars.BarButtonItem();
            this.barCellInterfereSetting = new DevExpress.XtraBars.BarButtonItem();
            this.barEditItem2 = new DevExpress.XtraBars.BarEditItem();
            this.repositoryItemComboBox3 = new DevExpress.XtraEditors.Repository.RepositoryItemComboBox();
            this.barBtnShowCoBSIC = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnNCell = new DevExpress.XtraBars.BarButtonItem();
            this.barSubItemNCType = new DevExpress.XtraBars.BarSubItem();
            this.barButtonItemNCell = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItemBothSideNC = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnClearCellPlanning = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnCombineColorUnit = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnOtherFunc = new DevExpress.XtraBars.BarButtonItem();
            this.barChkItemCQT = new DevExpress.XtraBars.BarCheckItem();
            this.barBtnFindCQTPnt = new DevExpress.XtraBars.BarButtonItem();
            this.barCheckItemGSMCell = new DevExpress.XtraBars.BarCheckItem();
            this.barCheckItemTDCell = new DevExpress.XtraBars.BarCheckItem();
            this.barCheckItemWCDMACell = new DevExpress.XtraBars.BarCheckItem();
            this.barCheckItemLTECell = new DevExpress.XtraBars.BarCheckItem();
            this.barCheckItemNBCell = new DevExpress.XtraBars.BarCheckItem();
            this.barCheckItemNRCell = new DevExpress.XtraBars.BarCheckItem();
            this.barButtonItemSearchCell = new DevExpress.XtraBars.BarButtonItem();
            this.barAndDockingController1 = new DevExpress.XtraBars.BarAndDockingController(this.components);
            this.barDockControl1 = new DevExpress.XtraBars.BarDockControl();
            this.barDockControl2 = new DevExpress.XtraBars.BarDockControl();
            this.barDockControl3 = new DevExpress.XtraBars.BarDockControl();
            this.barDockControl4 = new DevExpress.XtraBars.BarDockControl();
            this.imageListMapToolBar = new System.Windows.Forms.ImageList(this.components);
            this.barComboBoxLocation = new DevExpress.XtraBars.BarEditItem();
            this.imageListPropertySerial = new System.Windows.Forms.ImageList(this.components);
            this.imageListOffset = new System.Windows.Forms.ImageList(this.components);
            this.contextMenuStripFile = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.toolStripMenuItemDIYReplay = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItemStat = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItemDownload = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripFileHoSeq = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripDropDownCellInterfere = new System.Windows.Forms.ToolStripDropDown();
            this.contextMenuStripNeighberCells = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miDisplayOneOrBothWayNBCell = new System.Windows.Forms.ToolStripMenuItem();
            this.toolTipCQTImgPos = new System.Windows.Forms.ToolTip(this.components);
            this.barDockControlTop = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlBottom = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlLeft = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlRight = new DevExpress.XtraBars.BarDockControl();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemComboBox1)).BeginInit();
            this.statusStrip.SuspendLayout();
            this.contextMenuMap.SuspendLayout();
            this.panelMap.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.mapControl)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.barManager)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.popupMenuMeasure)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.popupMenuGE)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.trackBarBlindTransparentRibbon)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.comboBoxLocationRibbon)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.popupMenuMap)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemComboBox3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).BeginInit();
            this.contextMenuStripFile.SuspendLayout();
            this.contextMenuStripNeighberCells.SuspendLayout();
            this.SuspendLayout();
            // 
            // repositoryItemComboBox1
            // 
            this.repositoryItemComboBox1.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repositoryItemComboBox1.CaseSensitiveSearch = true;
            this.repositoryItemComboBox1.Items.AddRange(new object[] {
            "1",
            "2",
            "3",
            "4",
            "5",
            "6",
            "7"});
            this.repositoryItemComboBox1.Name = "repositoryItemComboBox1";
            // 
            // statusStrip
            // 
            this.statusStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.toolStripStatusLabelX,
            this.toolStripStatusLabelY,
            this.toolStripStatusLabelScale,
            this.tsBtnOffset,
            this.toolStripStatusLabel,
            this.toolStripStatusLabelZoom,
            this.toolStripStatusLabelSample,
            this.toolStripStatusLabelRegionInfo});
            this.statusStrip.Location = new System.Drawing.Point(0, 499);
            this.statusStrip.Name = "statusStrip";
            this.statusStrip.Size = new System.Drawing.Size(1091, 23);
            this.statusStrip.SizingGrip = false;
            this.statusStrip.TabIndex = 4;
            this.statusStrip.Text = "statusStrip";
            this.statusStrip.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.statusStrip_MouseDoubleClick);
            // 
            // toolStripStatusLabelX
            // 
            this.toolStripStatusLabelX.AutoSize = false;
            this.toolStripStatusLabelX.BorderSides = ((System.Windows.Forms.ToolStripStatusLabelBorderSides)((((System.Windows.Forms.ToolStripStatusLabelBorderSides.Left | System.Windows.Forms.ToolStripStatusLabelBorderSides.Top) 
            | System.Windows.Forms.ToolStripStatusLabelBorderSides.Right) 
            | System.Windows.Forms.ToolStripStatusLabelBorderSides.Bottom)));
            this.toolStripStatusLabelX.BorderStyle = System.Windows.Forms.Border3DStyle.SunkenOuter;
            this.toolStripStatusLabelX.Name = "toolStripStatusLabelX";
            this.toolStripStatusLabelX.Size = new System.Drawing.Size(80, 18);
            // 
            // toolStripStatusLabelY
            // 
            this.toolStripStatusLabelY.AutoSize = false;
            this.toolStripStatusLabelY.BorderSides = ((System.Windows.Forms.ToolStripStatusLabelBorderSides)((((System.Windows.Forms.ToolStripStatusLabelBorderSides.Left | System.Windows.Forms.ToolStripStatusLabelBorderSides.Top) 
            | System.Windows.Forms.ToolStripStatusLabelBorderSides.Right) 
            | System.Windows.Forms.ToolStripStatusLabelBorderSides.Bottom)));
            this.toolStripStatusLabelY.BorderStyle = System.Windows.Forms.Border3DStyle.SunkenOuter;
            this.toolStripStatusLabelY.Name = "toolStripStatusLabelY";
            this.toolStripStatusLabelY.Size = new System.Drawing.Size(80, 18);
            // 
            // toolStripStatusLabelScale
            // 
            this.toolStripStatusLabelScale.AutoSize = false;
            this.toolStripStatusLabelScale.BorderSides = ((System.Windows.Forms.ToolStripStatusLabelBorderSides)((((System.Windows.Forms.ToolStripStatusLabelBorderSides.Left | System.Windows.Forms.ToolStripStatusLabelBorderSides.Top) 
            | System.Windows.Forms.ToolStripStatusLabelBorderSides.Right) 
            | System.Windows.Forms.ToolStripStatusLabelBorderSides.Bottom)));
            this.toolStripStatusLabelScale.BorderStyle = System.Windows.Forms.Border3DStyle.SunkenOuter;
            this.toolStripStatusLabelScale.Name = "toolStripStatusLabelScale";
            this.toolStripStatusLabelScale.Size = new System.Drawing.Size(100, 18);
            // 
            // tsBtnOffset
            // 
            this.tsBtnOffset.BackColor = System.Drawing.SystemColors.Control;
            this.tsBtnOffset.DropDown = this.tsOffset;
            this.tsBtnOffset.Image = ((System.Drawing.Image)(resources.GetObject("tsBtnOffset.Image")));
            this.tsBtnOffset.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.tsBtnOffset.Name = "tsBtnOffset";
            this.tsBtnOffset.Overflow = System.Windows.Forms.ToolStripItemOverflow.Never;
            this.tsBtnOffset.ShowDropDownArrow = false;
            this.tsBtnOffset.Size = new System.Drawing.Size(88, 21);
            this.tsBtnOffset.Text = "显示与偏移";
            // 
            // tsOffset
            // 
            this.tsOffset.LayoutStyle = System.Windows.Forms.ToolStripLayoutStyle.Flow;
            this.tsOffset.Name = "toolStripDropDown1";
            this.tsOffset.OwnerItem = this.tsBtnOffset;
            this.tsOffset.Size = new System.Drawing.Size(2, 4);
            // 
            // toolStripStatusLabel
            // 
            this.toolStripStatusLabel.AutoSize = false;
            this.toolStripStatusLabel.Name = "toolStripStatusLabel";
            this.toolStripStatusLabel.Size = new System.Drawing.Size(490, 18);
            this.toolStripStatusLabel.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // toolStripStatusLabelZoom
            // 
            this.toolStripStatusLabelZoom.AutoSize = false;
            this.toolStripStatusLabelZoom.BorderSides = ((System.Windows.Forms.ToolStripStatusLabelBorderSides)((((System.Windows.Forms.ToolStripStatusLabelBorderSides.Left | System.Windows.Forms.ToolStripStatusLabelBorderSides.Top) 
            | System.Windows.Forms.ToolStripStatusLabelBorderSides.Right) 
            | System.Windows.Forms.ToolStripStatusLabelBorderSides.Bottom)));
            this.toolStripStatusLabelZoom.BorderStyle = System.Windows.Forms.Border3DStyle.SunkenOuter;
            this.toolStripStatusLabelZoom.Name = "toolStripStatusLabelZoom";
            this.toolStripStatusLabelZoom.Size = new System.Drawing.Size(100, 18);
            // 
            // toolStripStatusLabelSample
            // 
            this.toolStripStatusLabelSample.AutoSize = false;
            this.toolStripStatusLabelSample.BorderSides = ((System.Windows.Forms.ToolStripStatusLabelBorderSides)((((System.Windows.Forms.ToolStripStatusLabelBorderSides.Left | System.Windows.Forms.ToolStripStatusLabelBorderSides.Top) 
            | System.Windows.Forms.ToolStripStatusLabelBorderSides.Right) 
            | System.Windows.Forms.ToolStripStatusLabelBorderSides.Bottom)));
            this.toolStripStatusLabelSample.BorderStyle = System.Windows.Forms.Border3DStyle.SunkenOuter;
            this.toolStripStatusLabelSample.Name = "toolStripStatusLabelSample";
            this.toolStripStatusLabelSample.Size = new System.Drawing.Size(107, 18);
            this.toolStripStatusLabelSample.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // toolStripStatusLabelRegionInfo
            // 
            this.toolStripStatusLabelRegionInfo.Name = "toolStripStatusLabelRegionInfo";
            this.toolStripStatusLabelRegionInfo.Size = new System.Drawing.Size(0, 18);
            // 
            // contextMenuMap
            // 
            this.contextMenuMap.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miMapReplayFile,
            this.miReplayEvent,
            this.miReplayEventBothSides,
            this.miAnaES,
            this.miReplayTestPoint,
            this.toolStripSeparator1,
            this.miShowFlyLines,
            this.miViewCellInfo,
            this.miBTSLineSetting,
            this.miShowCoCellsByDistance,
            this.miShowAdjCellsByDistance,
            this.miDrawEventDateLbl,
            this.miAdjustCellSize,
            this.miViewSaveRegion,
            this.toolStripMenuItem6,
            this.miViewBlackBlock,
            this.miViewEventBlockInfo});
            this.contextMenuMap.Name = "ctxMapMenu";
            this.contextMenuMap.Size = new System.Drawing.Size(185, 346);
            this.contextMenuMap.Opening += new System.ComponentModel.CancelEventHandler(this.contextMenuMap_Opening);
            // 
            // miMapReplayFile
            // 
            this.miMapReplayFile.Image = ((System.Drawing.Image)(resources.GetObject("miMapReplayFile.Image")));
            this.miMapReplayFile.Name = "miMapReplayFile";
            this.miMapReplayFile.Size = new System.Drawing.Size(184, 22);
            this.miMapReplayFile.Text = "回放所属文件";
            this.miMapReplayFile.Visible = false;
            this.miMapReplayFile.Click += new System.EventHandler(this.miMapReplayFile_Click);
            // 
            // miReplayEvent
            // 
            this.miReplayEvent.Image = ((System.Drawing.Image)(resources.GetObject("miReplayEvent.Image")));
            this.miReplayEvent.Name = "miReplayEvent";
            this.miReplayEvent.Size = new System.Drawing.Size(184, 22);
            this.miReplayEvent.Text = "回放事件";
            this.miReplayEvent.Visible = false;
            this.miReplayEvent.Click += new System.EventHandler(this.miReplayEvent_Click);
            // 
            // miReplayEventBothSides
            // 
            this.miReplayEventBothSides.Name = "miReplayEventBothSides";
            this.miReplayEventBothSides.Size = new System.Drawing.Size(184, 22);
            this.miReplayEventBothSides.Text = "对比回放事件";
            this.miReplayEventBothSides.Click += new System.EventHandler(this.miReplayEventBothSides_Click);
            // 
            // miAnaES
            // 
            this.miAnaES.Image = global::MasterCom.RAMS.Properties.Resources.help;
            this.miAnaES.Name = "miAnaES";
            this.miAnaES.Size = new System.Drawing.Size(184, 22);
            this.miAnaES.Text = "智能预判事件";
            this.miAnaES.Click += new System.EventHandler(this.miAnaES_Click);
            // 
            // miReplayTestPoint
            // 
            this.miReplayTestPoint.Image = ((System.Drawing.Image)(resources.GetObject("miReplayTestPoint.Image")));
            this.miReplayTestPoint.Name = "miReplayTestPoint";
            this.miReplayTestPoint.Size = new System.Drawing.Size(184, 22);
            this.miReplayTestPoint.Text = "回放采样点";
            this.miReplayTestPoint.Visible = false;
            this.miReplayTestPoint.Click += new System.EventHandler(this.miReplayTestPoint_Click);
            // 
            // toolStripSeparator1
            // 
            this.toolStripSeparator1.Name = "toolStripSeparator1";
            this.toolStripSeparator1.Size = new System.Drawing.Size(181, 6);
            // 
            // miShowFlyLines
            // 
            this.miShowFlyLines.Name = "miShowFlyLines";
            this.miShowFlyLines.Size = new System.Drawing.Size(184, 22);
            this.miShowFlyLines.Text = "设置飞线图";
            this.miShowFlyLines.Click += new System.EventHandler(this.miShowFlyLines_Click);
            // 
            // miViewCellInfo
            // 
            this.miViewCellInfo.Name = "miViewCellInfo";
            this.miViewCellInfo.Size = new System.Drawing.Size(184, 22);
            this.miViewCellInfo.Text = "查看小区配置信息";
            this.miViewCellInfo.Click += new System.EventHandler(this.miViewCellInfo_Click);
            // 
            // miBTSLineSetting
            // 
            this.miBTSLineSetting.Name = "miBTSLineSetting";
            this.miBTSLineSetting.Size = new System.Drawing.Size(184, 22);
            this.miBTSLineSetting.Text = "设置站间线";
            this.miBTSLineSetting.Click += new System.EventHandler(this.miBTSLineSetting_Click);
            // 
            // miShowCoCellsByDistance
            // 
            this.miShowCoCellsByDistance.Name = "miShowCoCellsByDistance";
            this.miShowCoCellsByDistance.Size = new System.Drawing.Size(184, 22);
            this.miShowCoCellsByDistance.Text = "按距离查看同频小区";
            this.miShowCoCellsByDistance.Click += new System.EventHandler(this.miShowCoCellsByDistance_Click);
            // 
            // miShowAdjCellsByDistance
            // 
            this.miShowAdjCellsByDistance.Name = "miShowAdjCellsByDistance";
            this.miShowAdjCellsByDistance.Size = new System.Drawing.Size(184, 22);
            this.miShowAdjCellsByDistance.Text = "按距离查看邻频小区";
            this.miShowAdjCellsByDistance.Click += new System.EventHandler(this.miShowAdjCellsByDistance_Click);
            // 
            // miDrawEventDateLbl
            // 
            this.miDrawEventDateLbl.Name = "miDrawEventDateLbl";
            this.miDrawEventDateLbl.Size = new System.Drawing.Size(184, 22);
            this.miDrawEventDateLbl.Text = "显示事件时间标签";
            this.miDrawEventDateLbl.Click += new System.EventHandler(this.miDrawEventDateLbl_Click);
            // 
            // miAdjustCellSize
            // 
            this.miAdjustCellSize.Name = "miAdjustCellSize";
            this.miAdjustCellSize.Size = new System.Drawing.Size(184, 22);
            this.miAdjustCellSize.Text = "突出显示小区";
            this.miAdjustCellSize.Click += new System.EventHandler(this.miAdjustCellSize_Click);
            // 
            // miViewSaveRegion
            // 
            this.miViewSaveRegion.Name = "miViewSaveRegion";
            this.miViewSaveRegion.Size = new System.Drawing.Size(184, 22);
            this.miViewSaveRegion.Text = "保存为预存区域";
            this.miViewSaveRegion.Click += new System.EventHandler(this.miSaveRegion_Click);
            // 
            // toolStripMenuItem6
            // 
            this.toolStripMenuItem6.Name = "toolStripMenuItem6";
            this.toolStripMenuItem6.Size = new System.Drawing.Size(181, 6);
            // 
            // miViewBlackBlock
            // 
            this.miViewBlackBlock.Name = "miViewBlackBlock";
            this.miViewBlackBlock.Size = new System.Drawing.Size(184, 22);
            this.miViewBlackBlock.Text = "黑点情况";
            this.miViewBlackBlock.Click += new System.EventHandler(this.miViewBlackBlock_Click);
            // 
            // miViewEventBlockInfo
            // 
            this.miViewEventBlockInfo.Name = "miViewEventBlockInfo";
            this.miViewEventBlockInfo.Size = new System.Drawing.Size(184, 22);
            this.miViewEventBlockInfo.Text = "事件汇聚详情";
            this.miViewEventBlockInfo.Click += new System.EventHandler(this.miViewEventBlockInfo_Click);
            // 
            // panelMap
            // 
            this.panelMap.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D;
            this.panelMap.Controls.Add(this.btnCmpRelatedRegionOK);
            this.panelMap.Controls.Add(this.btnCQTImgPosSettingOK);
            this.panelMap.Controls.Add(this.mapControl);
            this.panelMap.Dock = System.Windows.Forms.DockStyle.Fill;
            this.panelMap.Location = new System.Drawing.Point(0, 26);
            this.panelMap.Name = "panelMap";
            this.panelMap.Size = new System.Drawing.Size(1091, 473);
            this.panelMap.TabIndex = 0;
            // 
            // btnCmpRelatedRegionOK
            // 
            this.btnCmpRelatedRegionOK.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnCmpRelatedRegionOK.FlatStyle = System.Windows.Forms.FlatStyle.Popup;
            this.btnCmpRelatedRegionOK.Location = new System.Drawing.Point(1034, 520);
            this.btnCmpRelatedRegionOK.Name = "btnCmpRelatedRegionOK";
            this.btnCmpRelatedRegionOK.Size = new System.Drawing.Size(37, 21);
            this.btnCmpRelatedRegionOK.TabIndex = 2;
            this.btnCmpRelatedRegionOK.Text = "确定";
            this.btnCmpRelatedRegionOK.UseVisualStyleBackColor = true;
            this.btnCmpRelatedRegionOK.Visible = false;
            this.btnCmpRelatedRegionOK.Click += new System.EventHandler(this.btnCmpRelatedRegionOK_Click);
            // 
            // btnCQTImgPosSettingOK
            // 
            this.btnCQTImgPosSettingOK.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnCQTImgPosSettingOK.FlatStyle = System.Windows.Forms.FlatStyle.Popup;
            this.btnCQTImgPosSettingOK.Location = new System.Drawing.Point(1037, 448);
            this.btnCQTImgPosSettingOK.Name = "btnCQTImgPosSettingOK";
            this.btnCQTImgPosSettingOK.Size = new System.Drawing.Size(37, 21);
            this.btnCQTImgPosSettingOK.TabIndex = 2;
            this.btnCQTImgPosSettingOK.Text = "确定";
            this.btnCQTImgPosSettingOK.UseVisualStyleBackColor = true;
            this.btnCQTImgPosSettingOK.Visible = false;
            this.btnCQTImgPosSettingOK.Click += new System.EventHandler(this.btnCQTImgPosSettingOK_Click);
            // 
            // mapControl
            // 
            this.mapControl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.mapControl.Enabled = true;
            this.mapControl.Location = new System.Drawing.Point(0, 0);
            this.mapControl.Name = "mapControl";
            this.mapControl.OcxState = ((System.Windows.Forms.AxHost.State)(resources.GetObject("mapControl.OcxState")));
            this.mapControl.Size = new System.Drawing.Size(1087, 469);
            this.mapControl.TabIndex = 3;
            this.mapControl.MouseDownEvent += new AxMapWinGIS._DMapEvents_MouseDownEventHandler(this.mapControl_MouseDownEvent);
            this.mapControl.MouseUpEvent += new AxMapWinGIS._DMapEvents_MouseUpEventHandler(this.mapControl_MouseUpEvent);
            this.mapControl.MouseMoveEvent += new AxMapWinGIS._DMapEvents_MouseMoveEventHandler(this.mapControl_MouseMoveEvent);
            this.mapControl.ExtentsChanged += new System.EventHandler(this.mapControl_ExtentsChanged);
            this.mapControl.OnDrawBackBuffer += new AxMapWinGIS._DMapEvents_OnDrawBackBufferEventHandler(this.mapControl_OnDrawBackBuffer);
            this.mapControl.DblClick += new System.EventHandler(this.mapControl_DblClick);
            this.mapControl.PreviewKeyDown += new System.Windows.Forms.PreviewKeyDownEventHandler(this.mapControl_PreviewKeyDown);
            // 
            // barManager
            // 
            this.barManager.Bars.AddRange(new DevExpress.XtraBars.Bar[] {
            this.mapFormToolBar,
            this.mapFormOtherBar});
            this.barManager.Controller = this.barAndDockingController1;
            this.barManager.DockControls.Add(this.barDockControl1);
            this.barManager.DockControls.Add(this.barDockControl2);
            this.barManager.DockControls.Add(this.barDockControl3);
            this.barManager.DockControls.Add(this.barDockControl4);
            this.barManager.Form = this;
            this.barManager.Images = this.imageListMapToolBar;
            this.barManager.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.tool_Select,
            this.tool_ZoomIn,
            this.tool_ZoomOut,
            this.tool_Pan,
            this.tool_All,
            this.tool_Info,
            this.tool_AddPolygon,
            this.tool_Clear,
            this.barBtnLayerCtrl,
            this.barTrackLayerTransparent,
            this.barComboBoxLocation,
            this.barBtnLocation,
            this.barBtnCellInterfere,
            this.barBtnTestPointArrow,
            this.barBtnLoadPlanImg,
            this.barBtnGE,
            this.barEditItemLocation,
            this.barEditItem2,
            this.barBtnShowCoBSIC,
            this.barBtnNCell,
            this.barBtnClearCellPlanning,
            this.barBtnCombineColorUnit,
            this.barBtnOtherFunc,
            this.barSubItemCellInterfere,
            this.barCellBcch,
            this.barCellTch,
            this.barCellBcchTch,
            this.barCellInterfereSetting,
            this.barSubItemNCType,
            this.barButtonItemNCell,
            this.barButtonItemBothSideNC,
            this.tool_AddRect,
            this.barCheckItemTDCell,
            this.barCheckItemGSMCell,
            this.barCheckItemWCDMACell,
            this.barButtonItemSearchCell,
            this.tool_AddPointMark,
            this.tool_RectangleFlyLine,
            this.tool_PolygonFlyLine,
            this.tool_AddLineMark,
            this.barCheckItemLTECell,
            this.barChkItemCQT,
            this.barBtnFindCQTPnt,
            this.barBtnMeasure,
            this.barCheckItemNBCell,
            this.barCheckItemNRCell});
            this.barManager.MaxItemId = 86;
            this.barManager.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.trackBarBlindTransparentRibbon,
            this.comboBoxLocationRibbon,
            this.repositoryItemComboBox3});
            // 
            // mapFormToolBar
            // 
            this.mapFormToolBar.BarName = "mapFormToolBar";
            this.mapFormToolBar.DockCol = 0;
            this.mapFormToolBar.DockRow = 0;
            this.mapFormToolBar.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            this.mapFormToolBar.FloatLocation = new System.Drawing.Point(20, 181);
            this.mapFormToolBar.FloatSize = new System.Drawing.Size(126, 118);
            this.mapFormToolBar.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.tool_Select),
            new DevExpress.XtraBars.LinkPersistInfo(this.tool_ZoomOut),
            new DevExpress.XtraBars.LinkPersistInfo(this.tool_ZoomIn),
            new DevExpress.XtraBars.LinkPersistInfo(this.tool_Pan),
            new DevExpress.XtraBars.LinkPersistInfo(this.tool_All),
            new DevExpress.XtraBars.LinkPersistInfo(this.tool_AddRect),
            new DevExpress.XtraBars.LinkPersistInfo(this.tool_AddPolygon),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnMeasure),
            new DevExpress.XtraBars.LinkPersistInfo(this.tool_Clear),
            new DevExpress.XtraBars.LinkPersistInfo(this.tool_Info),
            new DevExpress.XtraBars.LinkPersistInfo(this.tool_AddPointMark),
            new DevExpress.XtraBars.LinkPersistInfo(this.tool_AddLineMark),
            new DevExpress.XtraBars.LinkPersistInfo(this.tool_RectangleFlyLine),
            new DevExpress.XtraBars.LinkPersistInfo(this.tool_PolygonFlyLine)});
            this.mapFormToolBar.OptionsBar.AllowCollapse = true;
            this.mapFormToolBar.OptionsBar.DisableClose = true;
            this.mapFormToolBar.OptionsBar.DrawSizeGrip = true;
            this.mapFormToolBar.Text = "工具";
            // 
            // tool_Select
            // 
            this.tool_Select.Caption = "选择";
            this.tool_Select.Hint = "选择";
            this.tool_Select.Id = 0;
            this.tool_Select.ImageIndex = 22;
            this.tool_Select.Name = "tool_Select";
            this.tool_Select.Tag = "Select";
            this.tool_Select.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.mapFormToolBarCheckItemClick);
            // 
            // tool_ZoomOut
            // 
            this.tool_ZoomOut.Caption = "缩小";
            this.tool_ZoomOut.Hint = "缩小";
            this.tool_ZoomOut.Id = 2;
            this.tool_ZoomOut.ImageIndex = 18;
            this.tool_ZoomOut.Name = "tool_ZoomOut";
            this.tool_ZoomOut.Tag = "ZoomOut";
            this.tool_ZoomOut.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.mapFormToolBarCheckItemClick);
            // 
            // tool_ZoomIn
            // 
            this.tool_ZoomIn.Caption = "放大";
            this.tool_ZoomIn.Hint = "放大";
            this.tool_ZoomIn.Id = 1;
            this.tool_ZoomIn.ImageIndex = 12;
            this.tool_ZoomIn.Name = "tool_ZoomIn";
            this.tool_ZoomIn.Tag = "ZoomIn";
            this.tool_ZoomIn.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.mapFormToolBarCheckItemClick);
            // 
            // tool_Pan
            // 
            this.tool_Pan.Caption = "移动";
            this.tool_Pan.Hint = "移动";
            this.tool_Pan.Id = 3;
            this.tool_Pan.ImageIndex = 23;
            this.tool_Pan.Name = "tool_Pan";
            this.tool_Pan.Tag = "Pan";
            this.tool_Pan.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.mapFormToolBarCheckItemClick);
            // 
            // tool_All
            // 
            this.tool_All.Caption = "全图";
            this.tool_All.Hint = "全图";
            this.tool_All.Id = 4;
            this.tool_All.ImageIndex = 17;
            this.tool_All.Name = "tool_All";
            this.tool_All.Tag = "All";
            this.tool_All.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.tool_All_ItemClick);
            // 
            // tool_AddRect
            // 
            this.tool_AddRect.Caption = "矩形区域";
            this.tool_AddRect.Id = 47;
            this.tool_AddRect.ImageIndex = 14;
            this.tool_AddRect.Name = "tool_AddRect";
            this.tool_AddRect.Tag = "AddRect";
            this.tool_AddRect.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.mapFormToolBarCheckItemClick);
            // 
            // tool_AddPolygon
            // 
            this.tool_AddPolygon.Caption = "多边形区域";
            this.tool_AddPolygon.Hint = "多边形区域";
            this.tool_AddPolygon.Id = 12;
            this.tool_AddPolygon.ImageIndex = 10;
            this.tool_AddPolygon.Name = "tool_AddPolygon";
            this.tool_AddPolygon.Tag = "AddPolygon";
            this.tool_AddPolygon.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.mapFormToolBarCheckItemClick);
            // 
            // barBtnMeasure
            // 
            this.barBtnMeasure.ButtonStyle = DevExpress.XtraBars.BarButtonStyle.DropDown;
            this.barBtnMeasure.Caption = "测距";
            this.barBtnMeasure.DropDownControl = this.popupMenuMeasure;
            this.barBtnMeasure.Hint = "测距";
            this.barBtnMeasure.Id = 81;
            this.barBtnMeasure.ImageIndex = 7;
            this.barBtnMeasure.Name = "barBtnMeasure";
            this.barBtnMeasure.Tag = "barItemMeasure";
            this.barBtnMeasure.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.mapFormToolBarButtonItemClick);
            // 
            // popupMenuMeasure
            // 
            this.popupMenuMeasure.Manager = this.barManager;
            this.popupMenuMeasure.Name = "popupMenuMeasure";
            this.popupMenuMeasure.BeforePopup += new System.ComponentModel.CancelEventHandler(this.popupMenuMeasure_BeforePopup);
            // 
            // tool_Clear
            // 
            this.tool_Clear.Caption = "清除";
            this.tool_Clear.Hint = "清除";
            this.tool_Clear.Id = 16;
            this.tool_Clear.ImageIndex = 16;
            this.tool_Clear.Name = "tool_Clear";
            this.tool_Clear.Tag = "Clear";
            this.tool_Clear.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.tool_Clear_ItemClick);
            // 
            // tool_Info
            // 
            this.tool_Info.Caption = "信息";
            this.tool_Info.Hint = "信息";
            this.tool_Info.Id = 10;
            this.tool_Info.ImageIndex = 21;
            this.tool_Info.Name = "tool_Info";
            this.tool_Info.Tag = "Info";
            this.tool_Info.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.mapFormToolBarCheckItemClick);
            // 
            // tool_AddPointMark
            // 
            this.tool_AddPointMark.Caption = "点标签";
            this.tool_AddPointMark.Hint = "点标签";
            this.tool_AddPointMark.Id = 64;
            this.tool_AddPointMark.ImageIndex = 26;
            this.tool_AddPointMark.Name = "tool_AddPointMark";
            this.tool_AddPointMark.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.mapFormToolBarCheckItemClick);
            // 
            // tool_AddLineMark
            // 
            this.tool_AddLineMark.Caption = "画线";
            this.tool_AddLineMark.Hint = "画线";
            this.tool_AddLineMark.Id = 75;
            this.tool_AddLineMark.ImageIndex = 27;
            this.tool_AddLineMark.Name = "tool_AddLineMark";
            this.tool_AddLineMark.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.mapFormToolBarCheckItemClick);
            // 
            // tool_RectangleFlyLine
            // 
            this.tool_RectangleFlyLine.Caption = "矩形选择";
            this.tool_RectangleFlyLine.Hint = "矩形范围拉线图";
            this.tool_RectangleFlyLine.Id = 68;
            this.tool_RectangleFlyLine.ImageIndex = 15;
            this.tool_RectangleFlyLine.Name = "tool_RectangleFlyLine";
            this.tool_RectangleFlyLine.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.mapFormToolBarCheckItemClick);
            // 
            // tool_PolygonFlyLine
            // 
            this.tool_PolygonFlyLine.Caption = "多边形选择";
            this.tool_PolygonFlyLine.Hint = "多边形范围拉线图";
            this.tool_PolygonFlyLine.Id = 69;
            this.tool_PolygonFlyLine.ImageIndex = 11;
            this.tool_PolygonFlyLine.Name = "tool_PolygonFlyLine";
            this.tool_PolygonFlyLine.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.mapFormToolBarCheckItemClick);
            // 
            // mapFormOtherBar
            // 
            this.mapFormOtherBar.BarName = "其它";
            this.mapFormOtherBar.DockCol = 1;
            this.mapFormOtherBar.DockRow = 0;
            this.mapFormOtherBar.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            this.mapFormOtherBar.FloatLocation = new System.Drawing.Point(427, 200);
            this.mapFormOtherBar.FloatSize = new System.Drawing.Size(621, 30);
            this.mapFormOtherBar.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnLayerCtrl),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnTestPointArrow),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnLoadPlanImg),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnGE),
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.Width, this.barTrackLayerTransparent, "", true, true, true, 91),
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.Width, this.barEditItemLocation, "", true, true, true, 67),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnLocation),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnCellInterfere, true),
            new DevExpress.XtraBars.LinkPersistInfo(this.barSubItemCellInterfere),
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.Width, this.barEditItem2, "", false, true, true, 67),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnShowCoBSIC),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnNCell),
            new DevExpress.XtraBars.LinkPersistInfo(this.barSubItemNCType),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnClearCellPlanning),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnCombineColorUnit, true),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnOtherFunc),
            new DevExpress.XtraBars.LinkPersistInfo(this.barChkItemCQT),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnFindCQTPnt),
            new DevExpress.XtraBars.LinkPersistInfo(this.barCheckItemGSMCell),
            new DevExpress.XtraBars.LinkPersistInfo(this.barCheckItemTDCell),
            new DevExpress.XtraBars.LinkPersistInfo(this.barCheckItemWCDMACell),
            new DevExpress.XtraBars.LinkPersistInfo(this.barCheckItemLTECell),
            new DevExpress.XtraBars.LinkPersistInfo(this.barCheckItemNBCell),
            new DevExpress.XtraBars.LinkPersistInfo(this.barCheckItemNRCell),
            new DevExpress.XtraBars.LinkPersistInfo(this.barButtonItemSearchCell)});
            this.mapFormOtherBar.Offset = 399;
            this.mapFormOtherBar.OptionsBar.AllowCollapse = true;
            this.mapFormOtherBar.OptionsBar.DisableClose = true;
            this.mapFormOtherBar.OptionsBar.DisableCustomization = true;
            this.mapFormOtherBar.OptionsBar.DrawSizeGrip = true;
            this.mapFormOtherBar.Text = "其它";
            // 
            // barBtnLayerCtrl
            // 
            this.barBtnLayerCtrl.Hint = "图层控制";
            this.barBtnLayerCtrl.Id = 17;
            this.barBtnLayerCtrl.ImageIndex = 20;
            this.barBtnLayerCtrl.Name = "barBtnLayerCtrl";
            this.barBtnLayerCtrl.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnLayerCtrl_ItemClick);
            // 
            // barBtnTestPointArrow
            // 
            this.barBtnTestPointArrow.ButtonStyle = DevExpress.XtraBars.BarButtonStyle.Check;
            this.barBtnTestPointArrow.Caption = "显示测试方向";
            this.barBtnTestPointArrow.Hint = "显示测试方向";
            this.barBtnTestPointArrow.Id = 22;
            this.barBtnTestPointArrow.ImageIndex = 8;
            this.barBtnTestPointArrow.Name = "barBtnTestPointArrow";
            this.barBtnTestPointArrow.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnTestPointArrow_ItemClick);
            // 
            // barBtnLoadPlanImg
            // 
            this.barBtnLoadPlanImg.Caption = "加载CQT平面图";
            this.barBtnLoadPlanImg.Hint = "加载CQT平面图";
            this.barBtnLoadPlanImg.Id = 23;
            this.barBtnLoadPlanImg.ImageIndex = 5;
            this.barBtnLoadPlanImg.Name = "barBtnLoadPlanImg";
            this.barBtnLoadPlanImg.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnLoadPlanImg_ItemClick);
            // 
            // barBtnGE
            // 
            this.barBtnGE.ButtonStyle = DevExpress.XtraBars.BarButtonStyle.DropDown;
            this.barBtnGE.Caption = "转到谷歌地图";
            this.barBtnGE.DropDownControl = this.popupMenuGE;
            this.barBtnGE.Hint = "转到谷歌地图";
            this.barBtnGE.Id = 24;
            this.barBtnGE.ImageIndex = 6;
            this.barBtnGE.Name = "barBtnGE";
            this.barBtnGE.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnGE_ItemClick);
            // 
            // popupMenuGE
            // 
            this.popupMenuGE.Manager = this.barManager;
            this.popupMenuGE.Name = "popupMenuGE";
            this.popupMenuGE.Popup += new System.EventHandler(this.popupMenuGE_Popup);
            // 
            // barTrackLayerTransparent
            // 
            this.barTrackLayerTransparent.Caption = "地图透明度";
            this.barTrackLayerTransparent.Description = "地图透明度";
            this.barTrackLayerTransparent.Edit = this.trackBarBlindTransparentRibbon;
            this.barTrackLayerTransparent.Hint = "地图透明度";
            this.barTrackLayerTransparent.Id = 18;
            this.barTrackLayerTransparent.Name = "barTrackLayerTransparent";
            // 
            // trackBarBlindTransparentRibbon
            // 
            this.trackBarBlindTransparentRibbon.Maximum = 255;
            this.trackBarBlindTransparentRibbon.Name = "trackBarBlindTransparentRibbon";
            // 
            // barEditItemLocation
            // 
            this.barEditItemLocation.Caption = "定位地点，支持模糊查找地点";
            this.barEditItemLocation.Edit = this.comboBoxLocationRibbon;
            this.barEditItemLocation.Hint = "定位地点，支持模糊查找地点";
            this.barEditItemLocation.Id = 27;
            this.barEditItemLocation.Name = "barEditItemLocation";
            // 
            // comboBoxLocationRibbon
            // 
            this.comboBoxLocationRibbon.AutoHeight = false;
            this.comboBoxLocationRibbon.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.comboBoxLocationRibbon.CaseSensitiveSearch = true;
            this.comboBoxLocationRibbon.ImmediatePopup = true;
            this.comboBoxLocationRibbon.Name = "comboBoxLocationRibbon";
            this.comboBoxLocationRibbon.Sorted = true;
            // 
            // barBtnLocation
            // 
            this.barBtnLocation.ButtonStyle = DevExpress.XtraBars.BarButtonStyle.DropDown;
            this.barBtnLocation.Caption = "定位";
            this.barBtnLocation.DropDownControl = this.popupMenuMap;
            this.barBtnLocation.Hint = "定位";
            this.barBtnLocation.Id = 20;
            this.barBtnLocation.ImageIndex = 9;
            this.barBtnLocation.Name = "barBtnLocation";
            this.barBtnLocation.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnLocation_ItemClick);
            // 
            // popupMenuMap
            // 
            this.popupMenuMap.Manager = this.barManager;
            this.popupMenuMap.Name = "popupMenuMap";
            this.popupMenuMap.BeforePopup += new System.ComponentModel.CancelEventHandler(this.popupMenuMap_BeforePopup);
            // 
            // barBtnCellInterfere
            // 
            this.barBtnCellInterfere.ButtonStyle = DevExpress.XtraBars.BarButtonStyle.Check;
            this.barBtnCellInterfere.Caption = "显示同邻频干扰";
            this.barBtnCellInterfere.Hint = "显示同邻频干扰";
            this.barBtnCellInterfere.Id = 21;
            this.barBtnCellInterfere.ImageIndex = 1;
            this.barBtnCellInterfere.Name = "barBtnCellInterfere";
            this.barBtnCellInterfere.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnCellInterfere_ItemClick);
            // 
            // barSubItemCellInterfere
            // 
            this.barSubItemCellInterfere.Caption = "BCCH/CPI";
            this.barSubItemCellInterfere.Enabled = false;
            this.barSubItemCellInterfere.Id = 36;
            this.barSubItemCellInterfere.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.barCellBcch),
            new DevExpress.XtraBars.LinkPersistInfo(this.barCellTch),
            new DevExpress.XtraBars.LinkPersistInfo(this.barCellBcchTch),
            new DevExpress.XtraBars.LinkPersistInfo(this.barCellInterfereSetting, true)});
            this.barSubItemCellInterfere.Name = "barSubItemCellInterfere";
            // 
            // barCellBcch
            // 
            this.barCellBcch.ButtonStyle = DevExpress.XtraBars.BarButtonStyle.Check;
            this.barCellBcch.Caption = "BCCH/CPI";
            this.barCellBcch.Id = 37;
            this.barCellBcch.Name = "barCellBcch";
            this.barCellBcch.Tag = "1";
            this.barCellBcch.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barCellInterfereType_ItemClick);
            // 
            // barCellTch
            // 
            this.barCellTch.ButtonStyle = DevExpress.XtraBars.BarButtonStyle.Check;
            this.barCellTch.Caption = "TCH/FREQ";
            this.barCellTch.Id = 38;
            this.barCellTch.Name = "barCellTch";
            this.barCellTch.Tag = "2";
            this.barCellTch.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barCellInterfereType_ItemClick);
            // 
            // barCellBcchTch
            // 
            this.barCellBcchTch.ButtonStyle = DevExpress.XtraBars.BarButtonStyle.Check;
            this.barCellBcchTch.Caption = "BCCH/TCH CPI&&FREQ";
            this.barCellBcchTch.Id = 39;
            this.barCellBcchTch.Name = "barCellBcchTch";
            this.barCellBcchTch.Tag = "0";
            this.barCellBcchTch.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barCellInterfereType_ItemClick);
            // 
            // barCellInterfereSetting
            // 
            this.barCellInterfereSetting.Caption = "设置";
            this.barCellInterfereSetting.Id = 40;
            this.barCellInterfereSetting.Name = "barCellInterfereSetting";
            this.barCellInterfereSetting.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barCellInterfereSetting_ItemClick);
            // 
            // barEditItem2
            // 
            this.barEditItem2.Caption = "显示同邻频干扰类型";
            this.barEditItem2.Edit = this.repositoryItemComboBox3;
            this.barEditItem2.Hint = "显示同邻频干扰类型";
            this.barEditItem2.Id = 28;
            this.barEditItem2.Name = "barEditItem2";
            this.barEditItem2.Visibility = DevExpress.XtraBars.BarItemVisibility.Never;
            // 
            // repositoryItemComboBox3
            // 
            this.repositoryItemComboBox3.AutoHeight = false;
            this.repositoryItemComboBox3.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repositoryItemComboBox3.Name = "repositoryItemComboBox3";
            // 
            // barBtnShowCoBSIC
            // 
            this.barBtnShowCoBSIC.ButtonStyle = DevExpress.XtraBars.BarButtonStyle.Check;
            this.barBtnShowCoBSIC.Caption = "显示同色码";
            this.barBtnShowCoBSIC.Hint = "显示同色码";
            this.barBtnShowCoBSIC.Id = 29;
            this.barBtnShowCoBSIC.ImageIndex = 19;
            this.barBtnShowCoBSIC.Name = "barBtnShowCoBSIC";
            this.barBtnShowCoBSIC.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnShowCoBSIC_ItemClick);
            // 
            // barBtnNCell
            // 
            this.barBtnNCell.ButtonStyle = DevExpress.XtraBars.BarButtonStyle.Check;
            this.barBtnNCell.Caption = "显示邻区";
            this.barBtnNCell.Hint = "显示邻区";
            this.barBtnNCell.Id = 30;
            this.barBtnNCell.ImageIndex = 0;
            this.barBtnNCell.Name = "barBtnNCell";
            this.barBtnNCell.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnNCell_ItemClick);
            // 
            // barSubItemNCType
            // 
            this.barSubItemNCType.Caption = "邻区";
            this.barSubItemNCType.Enabled = false;
            this.barSubItemNCType.Id = 41;
            this.barSubItemNCType.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.barButtonItemNCell),
            new DevExpress.XtraBars.LinkPersistInfo(this.barButtonItemBothSideNC)});
            this.barSubItemNCType.Name = "barSubItemNCType";
            this.barSubItemNCType.Popup += new System.EventHandler(this.barSubItemNCType_Popup);
            // 
            // barButtonItemNCell
            // 
            this.barButtonItemNCell.ButtonStyle = DevExpress.XtraBars.BarButtonStyle.Check;
            this.barButtonItemNCell.Caption = "邻区";
            this.barButtonItemNCell.Id = 42;
            this.barButtonItemNCell.Name = "barButtonItemNCell";
            this.barButtonItemNCell.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barShowNCType_Click);
            // 
            // barButtonItemBothSideNC
            // 
            this.barButtonItemBothSideNC.Caption = "单双向邻区";
            this.barButtonItemBothSideNC.Id = 44;
            this.barButtonItemBothSideNC.Name = "barButtonItemBothSideNC";
            this.barButtonItemBothSideNC.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barShowNCType_Click);
            // 
            // barBtnClearCellPlanning
            // 
            this.barBtnClearCellPlanning.Caption = "清除小区规划信息";
            this.barBtnClearCellPlanning.Hint = "清除小区规划信息";
            this.barBtnClearCellPlanning.Id = 31;
            this.barBtnClearCellPlanning.ImageIndex = 2;
            this.barBtnClearCellPlanning.Name = "barBtnClearCellPlanning";
            this.barBtnClearCellPlanning.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnClearCellPlanning_ItemClick);
            // 
            // barBtnCombineColorUnit
            // 
            this.barBtnCombineColorUnit.Caption = "栅格汇聚";
            this.barBtnCombineColorUnit.Hint = "栅格汇聚";
            this.barBtnCombineColorUnit.Id = 33;
            this.barBtnCombineColorUnit.ImageIndex = 3;
            this.barBtnCombineColorUnit.Name = "barBtnCombineColorUnit";
            this.barBtnCombineColorUnit.Visibility = DevExpress.XtraBars.BarItemVisibility.Never;
            this.barBtnCombineColorUnit.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnCombineColorUnit_ItemClick);
            // 
            // barBtnOtherFunc
            // 
            this.barBtnOtherFunc.Caption = "其他设置";
            this.barBtnOtherFunc.Hint = "其他设置";
            this.barBtnOtherFunc.Id = 34;
            this.barBtnOtherFunc.ImageIndex = 4;
            this.barBtnOtherFunc.Name = "barBtnOtherFunc";
            this.barBtnOtherFunc.Visibility = DevExpress.XtraBars.BarItemVisibility.Never;
            this.barBtnOtherFunc.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnOtherFunc_ItemClick);
            // 
            // barChkItemCQT
            // 
            this.barChkItemCQT.Caption = "CQT";
            this.barChkItemCQT.Id = 78;
            this.barChkItemCQT.Name = "barChkItemCQT";
            this.barChkItemCQT.Visibility = DevExpress.XtraBars.BarItemVisibility.Never;
            this.barChkItemCQT.CheckedChanged += new DevExpress.XtraBars.ItemClickEventHandler(this.barChkItemCQT_CheckedChanged);
            // 
            // barBtnFindCQTPnt
            // 
            this.barBtnFindCQTPnt.Caption = "查找地点";
            this.barBtnFindCQTPnt.Id = 79;
            this.barBtnFindCQTPnt.Name = "barBtnFindCQTPnt";
            this.barBtnFindCQTPnt.Visibility = DevExpress.XtraBars.BarItemVisibility.Never;
            // 
            // barCheckItemGSMCell
            // 
            this.barCheckItemGSMCell.Caption = "GSM";
            this.barCheckItemGSMCell.Hint = "GSM小区";
            this.barCheckItemGSMCell.Id = 54;
            this.barCheckItemGSMCell.Name = "barCheckItemGSMCell";
            this.barCheckItemGSMCell.CheckedChanged += new DevExpress.XtraBars.ItemClickEventHandler(this.barCheckItemCellLayer_CheckedChanged);
            // 
            // barCheckItemTDCell
            // 
            this.barCheckItemTDCell.Caption = "TD";
            this.barCheckItemTDCell.Hint = "TD小区";
            this.barCheckItemTDCell.Id = 53;
            this.barCheckItemTDCell.Name = "barCheckItemTDCell";
            this.barCheckItemTDCell.Visibility = DevExpress.XtraBars.BarItemVisibility.Never;
            this.barCheckItemTDCell.CheckedChanged += new DevExpress.XtraBars.ItemClickEventHandler(this.barCheckItemCellLayer_CheckedChanged);
            // 
            // barCheckItemWCDMACell
            // 
            this.barCheckItemWCDMACell.Caption = "WCDMA";
            this.barCheckItemWCDMACell.Hint = "WCDMA";
            this.barCheckItemWCDMACell.Id = 55;
            this.barCheckItemWCDMACell.Name = "barCheckItemWCDMACell";
            this.barCheckItemWCDMACell.Visibility = DevExpress.XtraBars.BarItemVisibility.Never;
            this.barCheckItemWCDMACell.CheckedChanged += new DevExpress.XtraBars.ItemClickEventHandler(this.barCheckItemCellLayer_CheckedChanged);
            // 
            // barCheckItemLTECell
            // 
            this.barCheckItemLTECell.Caption = "LTE";
            this.barCheckItemLTECell.Hint = "LTE小区";
            this.barCheckItemLTECell.Id = 77;
            this.barCheckItemLTECell.Name = "barCheckItemLTECell";
            this.barCheckItemLTECell.CheckedChanged += new DevExpress.XtraBars.ItemClickEventHandler(this.barCheckItemCellLayer_CheckedChanged);
            // 
            // barCheckItemNBCell
            // 
            this.barCheckItemNBCell.Caption = "NB";
            this.barCheckItemNBCell.Hint = "NB小区";
            this.barCheckItemNBCell.Id = 83;
            this.barCheckItemNBCell.Name = "barCheckItemNBCell";
            this.barCheckItemNBCell.Visibility = DevExpress.XtraBars.BarItemVisibility.Never;
            this.barCheckItemNBCell.CheckedChanged += new DevExpress.XtraBars.ItemClickEventHandler(this.barCheckItemCellLayer_CheckedChanged);
            // 
            // barCheckItemNRCell
            // 
            this.barCheckItemNRCell.Caption = "NR";
            this.barCheckItemNRCell.Hint = "NR小区";
            this.barCheckItemNRCell.Id = 85;
            this.barCheckItemNRCell.Name = "barCheckItemNRCell";
            this.barCheckItemNRCell.Visibility = DevExpress.XtraBars.BarItemVisibility.Never;
            this.barCheckItemNRCell.CheckedChanged += new DevExpress.XtraBars.ItemClickEventHandler(this.barCheckItemCellLayer_CheckedChanged);
            // 
            // barButtonItemSearchCell
            // 
            this.barButtonItemSearchCell.Caption = "查找小区";
            this.barButtonItemSearchCell.Id = 56;
            this.barButtonItemSearchCell.Name = "barButtonItemSearchCell";
            this.barButtonItemSearchCell.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItemSearchCell_ItemClick);
            // 
            // barAndDockingController1
            // 
            this.barAndDockingController1.PaintStyleName = "Skin";
            this.barAndDockingController1.PropertiesBar.AllowLinkLighting = false;
            // 
            // barDockControl1
            // 
            this.barDockControl1.Dock = System.Windows.Forms.DockStyle.Top;
            this.barDockControl1.Location = new System.Drawing.Point(0, 0);
            this.barDockControl1.Size = new System.Drawing.Size(1091, 26);
            // 
            // barDockControl2
            // 
            this.barDockControl2.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.barDockControl2.Location = new System.Drawing.Point(0, 522);
            this.barDockControl2.Size = new System.Drawing.Size(1091, 0);
            // 
            // barDockControl3
            // 
            this.barDockControl3.Dock = System.Windows.Forms.DockStyle.Left;
            this.barDockControl3.Location = new System.Drawing.Point(0, 26);
            this.barDockControl3.Size = new System.Drawing.Size(0, 496);
            // 
            // barDockControl4
            // 
            this.barDockControl4.Dock = System.Windows.Forms.DockStyle.Right;
            this.barDockControl4.Location = new System.Drawing.Point(1091, 26);
            this.barDockControl4.Size = new System.Drawing.Size(0, 496);
            // 
            // imageListMapToolBar
            // 
            this.imageListMapToolBar.ImageStream = ((System.Windows.Forms.ImageListStreamer)(resources.GetObject("imageListMapToolBar.ImageStream")));
            this.imageListMapToolBar.TransparentColor = System.Drawing.Color.Transparent;
            this.imageListMapToolBar.Images.SetKeyName(0, "62.png");
            this.imageListMapToolBar.Images.SetKeyName(1, "同邻频");
            this.imageListMapToolBar.Images.SetKeyName(2, "gnome_edit_clear.ico");
            this.imageListMapToolBar.Images.SetKeyName(3, "go.png");
            this.imageListMapToolBar.Images.SetKeyName(4, "gnome_fs_executable.png");
            this.imageListMapToolBar.Images.SetKeyName(5, "CQT平面图.png");
            this.imageListMapToolBar.Images.SetKeyName(6, "Google地图.png");
            this.imageListMapToolBar.Images.SetKeyName(7, "测距.png");
            this.imageListMapToolBar.Images.SetKeyName(8, "测试方向.png");
            this.imageListMapToolBar.Images.SetKeyName(9, "定位.png");
            this.imageListMapToolBar.Images.SetKeyName(10, "多边形区域.png");
            this.imageListMapToolBar.Images.SetKeyName(11, "多边形选择.png");
            this.imageListMapToolBar.Images.SetKeyName(12, "放大.png");
            this.imageListMapToolBar.Images.SetKeyName(13, "居中.png");
            this.imageListMapToolBar.Images.SetKeyName(14, "矩形区域.png");
            this.imageListMapToolBar.Images.SetKeyName(15, "矩形选择.png");
            this.imageListMapToolBar.Images.SetKeyName(16, "清除.png");
            this.imageListMapToolBar.Images.SetKeyName(17, "全图.png");
            this.imageListMapToolBar.Images.SetKeyName(18, "缩小.png");
            this.imageListMapToolBar.Images.SetKeyName(19, "同色码.png");
            this.imageListMapToolBar.Images.SetKeyName(20, "图层控制.png");
            this.imageListMapToolBar.Images.SetKeyName(21, "信息.png");
            this.imageListMapToolBar.Images.SetKeyName(22, "选择.png");
            this.imageListMapToolBar.Images.SetKeyName(23, "移动.png");
            this.imageListMapToolBar.Images.SetKeyName(24, "60.png");
            this.imageListMapToolBar.Images.SetKeyName(25, "圆点1.png");
            this.imageListMapToolBar.Images.SetKeyName(26, "点标签.png");
            this.imageListMapToolBar.Images.SetKeyName(27, "画线.png");
            this.imageListMapToolBar.Images.SetKeyName(28, "街景.png");
            // 
            // barComboBoxLocation
            // 
            this.barComboBoxLocation.Caption = "地点";
            this.barComboBoxLocation.Edit = this.repositoryItemComboBox1;
            this.barComboBoxLocation.Id = 19;
            this.barComboBoxLocation.Name = "barComboBoxLocation";
            // 
            // imageListPropertySerial
            // 
            this.imageListPropertySerial.ImageStream = ((System.Windows.Forms.ImageListStreamer)(resources.GetObject("imageListPropertySerial.ImageStream")));
            this.imageListPropertySerial.TransparentColor = System.Drawing.Color.Transparent;
            this.imageListPropertySerial.Images.SetKeyName(0, "folder_closed.gif");
            this.imageListPropertySerial.Images.SetKeyName(1, "folder_opened.gif");
            this.imageListPropertySerial.Images.SetKeyName(2, "down.png");
            this.imageListPropertySerial.Images.SetKeyName(3, "up.png");
            this.imageListPropertySerial.Images.SetKeyName(4, "angle3d.gif");
            this.imageListPropertySerial.Images.SetKeyName(5, "value.png");
            this.imageListPropertySerial.Images.SetKeyName(6, "");
            this.imageListPropertySerial.Images.SetKeyName(7, "");
            this.imageListPropertySerial.Images.SetKeyName(8, "");
            this.imageListPropertySerial.Images.SetKeyName(9, "");
            this.imageListPropertySerial.Images.SetKeyName(10, "");
            this.imageListPropertySerial.Images.SetKeyName(11, "");
            this.imageListPropertySerial.Images.SetKeyName(12, "");
            this.imageListPropertySerial.Images.SetKeyName(13, "");
            // 
            // imageListOffset
            // 
            this.imageListOffset.ImageStream = ((System.Windows.Forms.ImageListStreamer)(resources.GetObject("imageListOffset.ImageStream")));
            this.imageListOffset.TransparentColor = System.Drawing.Color.Transparent;
            this.imageListOffset.Images.SetKeyName(0, "folder_closed.gif");
            this.imageListOffset.Images.SetKeyName(1, "folder_opened.gif");
            this.imageListOffset.Images.SetKeyName(2, "filelist.gif");
            this.imageListOffset.Images.SetKeyName(3, "BSC.gif");
            this.imageListOffset.Images.SetKeyName(4, "snapscreen.gif");
            this.imageListOffset.Images.SetKeyName(5, "各地市KPI.png");
            // 
            // contextMenuStripFile
            // 
            this.contextMenuStripFile.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.toolStripMenuItemDIYReplay,
            this.toolStripMenuItemStat,
            this.toolStripMenuItemDownload,
            this.toolStripFileHoSeq});
            this.contextMenuStripFile.Name = "contextMenuStrip1";
            this.contextMenuStripFile.Size = new System.Drawing.Size(137, 92);
            // 
            // toolStripMenuItemDIYReplay
            // 
            this.toolStripMenuItemDIYReplay.Image = ((System.Drawing.Image)(resources.GetObject("toolStripMenuItemDIYReplay.Image")));
            this.toolStripMenuItemDIYReplay.Name = "toolStripMenuItemDIYReplay";
            this.toolStripMenuItemDIYReplay.Size = new System.Drawing.Size(136, 22);
            this.toolStripMenuItemDIYReplay.Text = "回放文件";
            this.toolStripMenuItemDIYReplay.Click += new System.EventHandler(this.toolStripMenuItemDIYReplay_Click);
            // 
            // toolStripMenuItemStat
            // 
            this.toolStripMenuItemStat.Image = global::MasterCom.RAMS.Properties.Resources.stat;
            this.toolStripMenuItemStat.Name = "toolStripMenuItemStat";
            this.toolStripMenuItemStat.Size = new System.Drawing.Size(136, 22);
            this.toolStripMenuItemStat.Text = "统计";
            this.toolStripMenuItemStat.Click += new System.EventHandler(this.toolStripMenuItemStat_Click);
            // 
            // toolStripMenuItemDownload
            // 
            this.toolStripMenuItemDownload.Image = global::MasterCom.RAMS.Properties.Resources.download;
            this.toolStripMenuItemDownload.Name = "toolStripMenuItemDownload";
            this.toolStripMenuItemDownload.Size = new System.Drawing.Size(136, 22);
            this.toolStripMenuItemDownload.Text = "下载到本地";
            this.toolStripMenuItemDownload.Click += new System.EventHandler(this.toolStripMenuItemDownload_Click);
            // 
            // toolStripFileHoSeq
            // 
            this.toolStripFileHoSeq.Image = global::MasterCom.RAMS.Properties.Resources.handover;
            this.toolStripFileHoSeq.Name = "toolStripFileHoSeq";
            this.toolStripFileHoSeq.Size = new System.Drawing.Size(136, 22);
            this.toolStripFileHoSeq.Text = "切换序列";
            this.toolStripFileHoSeq.Click += new System.EventHandler(this.toolStripFileHoSeq_Click);
            // 
            // toolStripDropDownCellInterfere
            // 
            this.toolStripDropDownCellInterfere.LayoutStyle = System.Windows.Forms.ToolStripLayoutStyle.Flow;
            this.toolStripDropDownCellInterfere.Name = "toolStripDropDownCellInterfere";
            this.toolStripDropDownCellInterfere.Size = new System.Drawing.Size(2, 4);
            // 
            // contextMenuStripNeighberCells
            // 
            this.contextMenuStripNeighberCells.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miDisplayOneOrBothWayNBCell});
            this.contextMenuStripNeighberCells.Name = "contextMenuStripNeighberCells";
            this.contextMenuStripNeighberCells.Size = new System.Drawing.Size(161, 26);
            // 
            // miDisplayOneOrBothWayNBCell
            // 
            this.miDisplayOneOrBothWayNBCell.CheckOnClick = true;
            this.miDisplayOneOrBothWayNBCell.Name = "miDisplayOneOrBothWayNBCell";
            this.miDisplayOneOrBothWayNBCell.Size = new System.Drawing.Size(160, 22);
            this.miDisplayOneOrBothWayNBCell.Text = "显示单双向邻区";
            this.miDisplayOneOrBothWayNBCell.Click += new System.EventHandler(this.miDisplayOneOrBothWayNBCell_Click);
            // 
            // toolTipCQTImgPos
            // 
            this.toolTipCQTImgPos.AutomaticDelay = 100;
            this.toolTipCQTImgPos.IsBalloon = true;
            this.toolTipCQTImgPos.ShowAlways = true;
            this.toolTipCQTImgPos.ToolTipIcon = System.Windows.Forms.ToolTipIcon.Info;
            this.toolTipCQTImgPos.ToolTipTitle = "提示";
            this.toolTipCQTImgPos.UseAnimation = false;
            this.toolTipCQTImgPos.UseFading = false;
            // 
            // barDockControlTop
            // 
            this.barDockControlTop.Dock = System.Windows.Forms.DockStyle.Top;
            this.barDockControlTop.Location = new System.Drawing.Point(0, 26);
            this.barDockControlTop.Size = new System.Drawing.Size(1091, 0);
            // 
            // barDockControlBottom
            // 
            this.barDockControlBottom.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.barDockControlBottom.Location = new System.Drawing.Point(0, 522);
            this.barDockControlBottom.Size = new System.Drawing.Size(1091, 0);
            // 
            // barDockControlLeft
            // 
            this.barDockControlLeft.Dock = System.Windows.Forms.DockStyle.Left;
            this.barDockControlLeft.Location = new System.Drawing.Point(0, 26);
            this.barDockControlLeft.Size = new System.Drawing.Size(0, 496);
            // 
            // barDockControlRight
            // 
            this.barDockControlRight.Dock = System.Windows.Forms.DockStyle.Right;
            this.barDockControlRight.Location = new System.Drawing.Point(1091, 26);
            this.barDockControlRight.Size = new System.Drawing.Size(0, 496);
            // 
            // MapForm
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("MapForm.Appearance.Image")));
            this.Appearance.Options.UseFont = true;
            this.Appearance.Options.UseImage = true;
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.AutoSize = true;
            this.ClientSize = new System.Drawing.Size(1091, 522);
            this.Controls.Add(this.panelMap);
            this.Controls.Add(this.statusStrip);
            this.Controls.Add(this.barDockControlTop);
            this.Controls.Add(this.barDockControlBottom);
            this.Controls.Add(this.barDockControlLeft);
            this.Controls.Add(this.barDockControlRight);
            this.Controls.Add(this.barDockControl3);
            this.Controls.Add(this.barDockControl4);
            this.Controls.Add(this.barDockControl2);
            this.Controls.Add(this.barDockControl1);
            this.Name = "MapForm";
            this.SizeGripStyle = System.Windows.Forms.SizeGripStyle.Hide;
            this.Text = " ";
            this.Load += new System.EventHandler(this.MapForm_Load);
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemComboBox1)).EndInit();
            this.statusStrip.ResumeLayout(false);
            this.statusStrip.PerformLayout();
            this.contextMenuMap.ResumeLayout(false);
            this.panelMap.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.mapControl)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.barManager)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.popupMenuMeasure)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.popupMenuGE)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.trackBarBlindTransparentRibbon)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.comboBoxLocationRibbon)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.popupMenuMap)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemComboBox3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).EndInit();
            this.contextMenuStripFile.ResumeLayout(false);
            this.contextMenuStripNeighberCells.ResumeLayout(false);
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.StatusStrip statusStrip;
        private System.Windows.Forms.ToolStripStatusLabel toolStripStatusLabelY;
        private System.Windows.Forms.ToolStripStatusLabel toolStripStatusLabelX;
        private System.Windows.Forms.ToolStripStatusLabel toolStripStatusLabelZoom;
        private System.Windows.Forms.ToolStripStatusLabel toolStripStatusLabelScale;
        private System.Windows.Forms.Panel panelMap;
        private System.Windows.Forms.ContextMenuStrip contextMenuMap;
        private System.Windows.Forms.ImageList imageListPropertySerial;
        private System.Windows.Forms.ToolStripDropDownButton tsBtnOffset;
        private System.Windows.Forms.ToolStripDropDown tsOffset;
        private System.Windows.Forms.ImageList imageListOffset;
        private System.Windows.Forms.ToolStripStatusLabel toolStripStatusLabelSample;
        private System.Windows.Forms.ToolStripMenuItem miReplayTestPoint;
        private System.Windows.Forms.ContextMenuStrip contextMenuStripFile;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItemStat;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItemDownload;
        private System.Windows.Forms.ToolStripMenuItem miReplayEvent;
        private System.Windows.Forms.ToolStripMenuItem toolStripFileHoSeq;
        private System.Windows.Forms.ToolStripMenuItem miViewBlackBlock;
        private System.Windows.Forms.ToolStripMenuItem miViewCellInfo;
        private System.Windows.Forms.ToolStripStatusLabel toolStripStatusLabel;
        private System.Windows.Forms.ToolStripMenuItem miAnaES;
        private System.Windows.Forms.ToolStripMenuItem miShowCoCellsByDistance;
        private System.Windows.Forms.ToolStripMenuItem miShowAdjCellsByDistance;
        private System.Windows.Forms.ToolStripDropDown toolStripDropDownCellInterfere;
        private System.Windows.Forms.ToolStripMenuItem miShowFlyLines;
        private System.Windows.Forms.ToolStripMenuItem miReplayEventBothSides;
        private System.Windows.Forms.ToolStripMenuItem miViewEventBlockInfo;
        private System.Windows.Forms.ToolStripMenuItem miMapReplayFile;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItemDIYReplay;
        private System.Windows.Forms.ToolStripMenuItem miViewSaveRegion;
        private System.Windows.Forms.ToolStripMenuItem miAdjustCellSize;
        private System.Windows.Forms.ContextMenuStrip contextMenuStripNeighberCells;
        private System.Windows.Forms.ToolStripMenuItem miDisplayOneOrBothWayNBCell;
        private System.Windows.Forms.ToolStripMenuItem miBTSLineSetting;
        private System.Windows.Forms.ToolStripMenuItem miDrawEventDateLbl;
        private System.Windows.Forms.ToolStripSeparator toolStripMenuItem6;
        private System.Windows.Forms.ToolTip toolTipCQTImgPos;
        private System.Windows.Forms.Button btnCQTImgPosSettingOK;
        private DevExpress.XtraBars.BarDockControl barDockControlTop;
        private DevExpress.XtraBars.BarDockControl barDockControlBottom;
        private DevExpress.XtraBars.BarDockControl barDockControlLeft;
        private DevExpress.XtraBars.BarDockControl barDockControlRight;
        private DevExpress.XtraBars.BarManager barManager;
        private DevExpress.XtraBars.BarDockControl barDockControl1;
        private DevExpress.XtraBars.BarDockControl barDockControl2;
        private DevExpress.XtraBars.BarDockControl barDockControl3;
        private DevExpress.XtraBars.BarDockControl barDockControl4;
        private DevExpress.XtraBars.Bar mapFormToolBar;
        private DevExpress.XtraBars.BarCheckItem tool_Select;
        private DevExpress.XtraBars.BarCheckItem tool_ZoomIn;
        private DevExpress.XtraBars.BarCheckItem tool_ZoomOut;
        private DevExpress.XtraBars.BarCheckItem tool_Pan;
        private DevExpress.XtraBars.BarButtonItem tool_All;
        private DevExpress.XtraBars.BarCheckItem tool_Info;
        private System.Windows.Forms.ImageList imageListMapToolBar;
        private DevExpress.XtraBars.BarButtonItem tool_Clear;
        private DevExpress.XtraBars.Bar mapFormOtherBar;
        private DevExpress.XtraBars.BarButtonItem barBtnLayerCtrl;
        private DevExpress.XtraBars.BarEditItem barTrackLayerTransparent;
        private DevExpress.XtraEditors.Repository.RepositoryItemTrackBar trackBarBlindTransparentRibbon;
        private DevExpress.XtraBars.BarEditItem barComboBoxLocation;
        private DevExpress.XtraBars.BarButtonItem barBtnLocation;
        private DevExpress.XtraBars.BarButtonItem barBtnCellInterfere;
        private DevExpress.XtraBars.BarButtonItem barBtnTestPointArrow;
        private DevExpress.XtraBars.BarButtonItem barBtnLoadPlanImg;
        private DevExpress.XtraBars.BarButtonItem barBtnGE;
        private DevExpress.XtraBars.BarEditItem barEditItemLocation;
        private DevExpress.XtraEditors.Repository.RepositoryItemComboBox comboBoxLocationRibbon;
        private DevExpress.XtraBars.BarEditItem barEditItem2;
        private DevExpress.XtraEditors.Repository.RepositoryItemComboBox repositoryItemComboBox3;
        private DevExpress.XtraBars.BarButtonItem barBtnShowCoBSIC;
        private DevExpress.XtraBars.BarButtonItem barBtnNCell;
        private DevExpress.XtraBars.BarButtonItem barBtnClearCellPlanning;
        private DevExpress.XtraBars.BarButtonItem barBtnCombineColorUnit;
        private DevExpress.XtraBars.BarButtonItem barBtnOtherFunc;
        private DevExpress.XtraEditors.Repository.RepositoryItemComboBox repositoryItemComboBox1;
        private DevExpress.XtraBars.BarSubItem barSubItemCellInterfere;
        private DevExpress.XtraBars.BarButtonItem barCellBcch;
        private DevExpress.XtraBars.BarButtonItem barCellTch;
        private DevExpress.XtraBars.BarButtonItem barCellBcchTch;
        private DevExpress.XtraBars.BarButtonItem barCellInterfereSetting;
        private DevExpress.XtraBars.BarSubItem barSubItemNCType;
        private DevExpress.XtraBars.BarButtonItem barButtonItemNCell;
        private DevExpress.XtraBars.BarButtonItem barButtonItemBothSideNC;
        private System.Windows.Forms.Button btnCmpRelatedRegionOK;
        private System.Windows.Forms.ToolStripStatusLabel toolStripStatusLabelRegionInfo;
        private DevExpress.XtraBars.PopupMenu popupMenuMap;
        private AxMapWinGIS.AxMap mapControl;
        private DevExpress.XtraBars.BarCheckItem barCheckItemTDCell;
        private DevExpress.XtraBars.BarCheckItem barCheckItemGSMCell;
        private DevExpress.XtraBars.BarCheckItem barCheckItemWCDMACell;
        private DevExpress.XtraBars.BarButtonItem barButtonItemSearchCell;
        private DevExpress.XtraBars.BarAndDockingController barAndDockingController1;
        private DevExpress.XtraBars.BarCheckItem tool_AddPointMark;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator1;
        private DevExpress.XtraBars.BarCheckItem tool_RectangleFlyLine;
        private DevExpress.XtraBars.BarCheckItem tool_PolygonFlyLine;
        private DevExpress.XtraBars.BarCheckItem tool_AddLineMark;
        private DevExpress.XtraBars.BarCheckItem barCheckItemLTECell;
        private DevExpress.XtraBars.BarCheckItem barChkItemCQT;
        private DevExpress.XtraBars.BarButtonItem barBtnFindCQTPnt;
        private DevExpress.XtraBars.PopupMenu popupMenuGE;
        public DevExpress.XtraBars.BarCheckItem tool_AddPolygon;
        private DevExpress.XtraBars.BarCheckItem tool_AddRect;
        private DevExpress.XtraBars.BarButtonItem barBtnMeasure;
        private DevExpress.XtraBars.PopupMenu popupMenuMeasure;
        private DevExpress.XtraBars.BarCheckItem barCheckItemNBCell;
        private DevExpress.XtraBars.BarCheckItem barCheckItemNRCell;
    }
}