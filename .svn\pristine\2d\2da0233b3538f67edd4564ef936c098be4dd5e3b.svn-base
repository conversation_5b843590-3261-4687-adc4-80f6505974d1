﻿using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTWeakSINRRoadNRQuery : ZTWeakSINRRoadBaseQuery<ZTWeakSINRRoadNRQuery>
    {
        protected override string themeName { get { return "NR:SS_SINR"; } }
        protected override string rsrpName { get { return "NR_SS_RSRP"; } }
        protected override string sinrName { get { return "NR_SS_SINR"; } }

        public ZTWeakSINRRoadNRQuery()
            : base()
        {
            Columns = NRTpHelper.InitBaseReplayParamBackground(false, false);
            Columns.Add("NR_SS_RSRQ");
            Columns.Add("NR_lte_RSRP");
            Columns.Add("NR_lte_SINR");
            ServiceTypes = ServiceTypeManager.GetServiceTypesByServiceName(ServiceName.NR);
        }

        public override string Name
        {
            get { return "SINR质差路段_NR"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 35000, 35007, this.Name);//////
        }

        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                setRoadCond();
                return true;
            }
            WeakSINRRoadNRSettingDlg dlg = new WeakSINRRoadNRSettingDlg(WeakCondition);
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                WeakCondition = dlg.GetCondition();
                setRoadCond();
                return true;
            }
            return false;
        }

        protected override void addToReportInfo(List<TestPoint> testPointList, double curWeakPercent, double curDis)
        {
            if (testPointList.Count == 0)
            {
                return;
            }
            WeakSINRRoadNR weakCover = new WeakSINRRoadNR();
            for (int i = 0; i < testPointList.Count; i++)
            {
                TestPoint testPoint = testPointList[i];
                float? sinr = NRTpHelper.NrTpManager.GetSCellSinr(testPoint);
                float? rsrp = NRTpHelper.NrTpManager.GetSCellRsrp(testPoint);
                weakCover.Add(sinr, rsrp, testPoint);
                weakCover.AddOtherTPInfo(testPoint);
                if (weakCover.MotorWay == "" || weakCover.MotorWay == null)
                {
                    weakCover.SetMotorWay(testPoint.AreaID, testPoint.AreaTypeID);
                }
            }
            weakCover.WeakPercent = curWeakPercent;
            weakCover.Distance = curDis;
            weakCover.Duration = duration;
            weakCover.CityName = DistrictManager.GetInstance().getDistrictName(MainModel.DistrictID);
            saveWeakCoverInfo(weakCover);
        }

        protected override void fireShowForm()
        {
            WeakSINRRoadNRForm frm = MainModel.CreateResultForm(typeof(WeakSINRRoadNRForm)) as WeakSINRRoadNRForm;
            List<WeakSINRRoadNR> resList = new List<WeakSINRRoadNR>();
            foreach (var item in weakCoverList)
            {
                resList.Add(item as WeakSINRRoadNR);
            }

            frm.FillData(resList);
            frm.Visible = true;
            frm.BringToFront();
            weakCoverList = null;
        }
    }

    public class ZTWeakSINRRoadNRQueryByFile : ZTWeakSINRRoadNRQuery
    {
        private static ZTWeakSINRRoadNRQueryByFile instance = null;
        public new static ZTWeakSINRRoadNRQueryByFile GetInstance()
        {
            if (instance == null)
            {
                instance = new ZTWeakSINRRoadNRQueryByFile();
            }
            return instance;
        }

        public override string Name
        {
            get { return "SINR质差路段_NR(按文件)"; }
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MainModel.NeedSearchType.File;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        protected override bool isTPInRegion(TestPoint tp)
        {
            return true;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                return true;
            }
            return false;
        }
    }
}
