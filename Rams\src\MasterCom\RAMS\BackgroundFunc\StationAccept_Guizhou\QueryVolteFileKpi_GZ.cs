﻿using MasterCom.RAMS.Func.SystemSetting;
using MasterCom.RAMS.KPI_Statistics;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Stat;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.BackgroundFunc
{
    public class QueryVolteFileKpi_GZ : FileKPIQuery
    {
        public QueryVolteFileKpi_GZ(ReporterTemplate template, Dictionary<string, StatInfoBase> fileKeyDataDic)
            :base(template, fileKeyDataDic)
        {
        }
        protected override void recieveAndHandleSpecificStatData(Package package
            , List<StatImgDefItem> curImgColumnDef, KPIStatDataBase singleStatData)
        {
            fillStatData(package, curImgColumnDef, singleStatData);

            DTDataHeader fi = DTDataHeaderManager.GetInstance().GetHeaderByFileID(singleStatData.FileID);
            StatInfoBase tmp = new StatInfoBase();
            tmp.DistrictID = condition.DistrictID;
            tmp.FileHeader = fi;
            tmp.KPIData.AddStatData(fi, singleStatData, false);
            string flieKey = fi == null ? "" : fi.ID.ToString();
            StatInfoBase fsi = null;
            if (this.FileKeyDataDic.TryGetValue(flieKey, out fsi))
            {
                fsi.KPIData.AddStatData(fi, singleStatData, false);
            }
            else
            {
                fsi = tmp;
                this.FileKeyDataDic[flieKey] = fsi;
            }
        }

        protected override void handleStatEvent(Event evt)
        {
            StatDataEvent singleStatData = new StatDataEvent(evt, needSeparateByServiceID(evt), needSeparateByFileName(evt));
            DTDataHeader fi = DTDataHeaderManager.GetInstance().GetHeaderByFileID(evt.FileID);
            StatInfoBase tmp = new StatInfoBase();
            tmp.DistrictID = condition.DistrictID;
            tmp.FileHeader = fi;
            tmp.KPIData.AddStatData(fi, singleStatData, false);
            string flieKey = fi == null ? "" : fi.ID.ToString();
            StatInfoBase fsi = null;
            if (this.FileKeyDataDic.TryGetValue(flieKey, out fsi))
            {
                fsi.KPIData.AddStatData(fi, singleStatData, false);
            }
            else
            {
                fsi = tmp;
                this.FileKeyDataDic[flieKey] = fsi;
            }
        }
        public List<NPOIRow> CreateReport(ReporterTemplate tpl, List<CellKpiInfo_VOLTE> retdatas)
        {
            List<NPOIRow> outputRows = new List<NPOIRow>();
            if (retdatas == null)
            {
                return outputRows;
            }

            try
            {
                NPOIRow row;
                int staticTitleCount = 2;

                for (int r = 0; r < retdatas.Count; r++)
                {
                    CellKpiInfo_VOLTE cellInfo = retdatas[r];
                    row = new NPOIRow();
                    outputRows.Add(row);
                    row.cellValues = new List<object>(tpl.Columns.Count + staticTitleCount);
                    row.cellValues.Add(cellInfo.BtsName);
                    row.cellValues.Add(cellInfo.CellName);
                    for (int i = staticTitleCount; i < tpl.Columns.Count + staticTitleCount; i++)
                    {
                        row.cellValues.Add(null);
                        object obj = getColumnSetValue(cellInfo.KpiInfo, tpl, i - staticTitleCount);
                        if (obj is string && (obj.Equals("-") || obj.Equals("")))
                        {
                            row.cellValues[i] = 0;//应网优之家要求
                        }
                        else
                        {
                            row.cellValues[i] = obj;
                        }
                    }
                }
            }
            catch
            {
                //continue
            }
            return outputRows;
        }
    }
}
