﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class NRReasonPnlWeakCover
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.label1 = new System.Windows.Forms.Label();
            this.numMaxRSRP = new DevExpress.XtraEditors.SpinEdit();
            this.label2 = new System.Windows.Forms.Label();
            ((System.ComponentModel.ISupportInitialize)(this.grp)).BeginInit();
            this.grp.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxRSRP.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // grp
            // 
            this.grp.Controls.Add(this.label2);
            this.grp.Controls.Add(this.numMaxRSRP);
            this.grp.Controls.Add(this.label1);
            this.grp.Size = new System.Drawing.Size(421, 70);
            this.grp.Text = "弱覆盖原因";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(36, 39);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(41, 12);
            this.label1.TabIndex = 0;
            this.label1.Text = "RSRP≤";
            // 
            // numMaxRSRP
            // 
            this.numMaxRSRP.EditValue = new decimal(new int[] {
            105,
            0,
            0,
            -2147483648});
            this.numMaxRSRP.Location = new System.Drawing.Point(83, 34);
            this.numMaxRSRP.Name = "numMaxRSRP";
            this.numMaxRSRP.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numMaxRSRP.Properties.MaxValue = new decimal(new int[] {
            25,
            0,
            0,
            0});
            this.numMaxRSRP.Properties.MinValue = new decimal(new int[] {
            141,
            0,
            0,
            -2147483648});
            this.numMaxRSRP.Size = new System.Drawing.Size(75, 21);
            this.numMaxRSRP.TabIndex = 1;
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(164, 40);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(17, 12);
            this.label2.TabIndex = 2;
            this.label2.Text = "dB";
            // 
            // ReasonPnlWeakCover
            // 
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.Name = "ReasonPnlWeakCover";
            this.Size = new System.Drawing.Size(421, 70);
            ((System.ComponentModel.ISupportInitialize)(this.grp)).EndInit();
            this.grp.ResumeLayout(false);
            this.grp.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxRSRP.Properties)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.SpinEdit numMaxRSRP;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Label label2;
    }
}
