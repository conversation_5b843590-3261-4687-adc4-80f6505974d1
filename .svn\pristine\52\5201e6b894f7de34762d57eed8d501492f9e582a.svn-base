﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRBlockCallAnaInfo
    {
        public NRBlockCallAnaInfo()
        { 
        
        }

        private NRBlockCallAnaInfo otherSideCall;
        public NRBlockCallAnaInfo OtherSideCall
        {
            get { return otherSideCall; }
            set
            {
                otherSideCall = value;
                if (otherSideCall != null)
                {
                    otherSideCall.otherSideCall = this;
                }
            }
        }

        public string FileName { get; set; }
        public DateTime BeginTime { get; set; }
        public DateTime EndTime { get; set; }

        public Event BlockEvt { get; set; }
        public DateTime BlockTime { get; set; }
        public string BlockTimeDesc { get; set; }
        public ENRBlockCallCause BlockCause { get; set; } = ENRBlockCallCause.其它;
        public string Suggest { get; set; }

        public bool IsFilter { get; set; }
        public string IsFilterDesc { get; set; }

        public List<TestPoint> TestPoints { get; protected set; } = new List<TestPoint>();
        public List<Event> Events { get; protected set; } = new List<Event>();
        public List<Message> Messages { get; protected set; } = new List<Message>();

        public bool IsBlockCall { get; set; }
        public string IsBlockCallDesc { get; set; }

        public string MoMtDesc { get; set; }
        public bool IsMultiCvr { get; set; }

        public DataInfo RsrpInfo { get; set; } = new DataInfo();
        public DataInfo SinrInfo { get; set; } = new DataInfo();
        public DataInfo LteRsrpInfo { get; set; } = new DataInfo();
        public DataInfo LteSinrInfo { get; set; } = new DataInfo();
        public DataInfo MultiCvrInfo { get; set; } = new DataInfo();

        public int HoNum { get; set; }

        readonly NRBlockCallAnaHelper helper = new NRBlockCallAnaHelper();

        public string ErrorMsgName { get; private set; }

        public void SetErrorMsgName(Message ErrorMsg)
        {
            if (ErrorMsg != null)
            {
                MessageInfo messageInfo = MessageInfoManager.GetInstance()[ErrorMsg.ID];
                if (messageInfo != null)
                {
                    ErrorMsgName = messageInfo.Name;
                }
            }
        }

        public void Evaluate(NRBlockCallAnaCondtion blockCallCond, bool both)
        {
            foreach (TestPoint tp in TestPoints)
            {
                double totalSeconds = (BlockTime - DateTime.Parse(tp.DateTimeStringWithMillisecond)).TotalSeconds;
                if (totalSeconds <= blockCallCond.WeakRsrpSec)
                {
                    float? rsrp = NRTpHelper.NrTpManager.GetSCellRsrp(tp);
                    RsrpInfo.Add(rsrp);

                    float? lteRsrp = NRTpHelper.NrLteTpManager.GetSCellRsrp(tp);
                    LteRsrpInfo.Add(lteRsrp);
                }
                if (totalSeconds <= blockCallCond.PoorSinrSec)
                {
                    float? sinr = NRTpHelper.NrTpManager.GetSCellSinr(tp);
                    SinrInfo.Add(sinr);

                    float? lteSinr = NRTpHelper.NrLteTpManager.GetSCellSinr(tp);
                    LteSinrInfo.Add(lteSinr);
                }
                if (totalSeconds <= blockCallCond.MultiSec)
                {
                    dealMultiCvr(blockCallCond, MultiCvrInfo, tp);
                }
            }

            IsFilterDesc = setDesc(IsFilter);
            IsBlockCallDesc = setDesc(IsBlockCall);

            RsrpInfo.Calculate();
            SinrInfo.Calculate();
            LteRsrpInfo.Calculate();
            LteSinrInfo.Calculate();
            MultiCvrInfo.Calculate();
            MultiCvrInfo.Avg *= 100d;

            dealHandoverEvt(blockCallCond);

            dealBlockCause(blockCallCond);

            if (both && this.otherSideCall != null)
            {
                this.otherSideCall.Evaluate(blockCallCond, false);
            }
        }

        private string setDesc(bool isTrue)
        {
            if (isTrue)
            {
                return "是";
            }
            else
            {
                return "否";
            }
        }

        private void dealMultiCvr(NRBlockCallAnaCondtion blockCallCond, DataInfo multiCvrInfo, TestPoint tp)
        {
            float? rsrp = NRTpHelper.NrTpManager.GetSCellRsrp(tp);
            if (rsrp != null)
            {
                multiCvrInfo.Count++;
                int num = 1;
                for (int i = 0; i < 16; i++)
                {
                    bool isNCell = NRTpHelper.NrTpManager.JudgeIsNCell(tp, i);
                    if (!isNCell)
                    {
                        continue;
                    }
                    float? rsrpN = NRTpHelper.NrTpManager.GetNCellRsrp(tp, i);
                    if (rsrpN == null)
                    {
                        break;
                    }
                    if (Math.Abs((float)(rsrpN - rsrp)) <= blockCallCond.MultiBand)
                    {
                        num++;
                    }
                }
                if (num >= blockCallCond.MultiValue)
                {
                    multiCvrInfo.Sum++;
                }
            }
        }

        private void dealHandoverEvt(NRBlockCallAnaCondtion blockCallCond)
        {
            foreach (Event evt in Events)
            {
                if (evt.DateTime > this.BlockTime)
                {
                    break;
                }
                if (helper.HandOverEvtIdList.Contains(evt.ID))
                {
                    double totalSeconds = (BlockTime - evt.DateTime).TotalSeconds;
                    if (totalSeconds <= blockCallCond.HoSec)
                    {
                        HoNum++;
                    }
                }
            }
        }

        private void dealBlockCause(NRBlockCallAnaCondtion blockCallCond)
        {
            bool netProb = false;
            foreach (ZTNRBlockCallAna.NRBlockCallAnaCauseBase cause in blockCallCond.CauseSet)
            {
                if (cause.IsSatisfy(this))
                {
                    netProb = true;
                }
            }

            if (!netProb)
            {
                if (this.RsrpInfo.Avg <= blockCallCond.WeakRsrp)
                {
                    BlockCause = ENRBlockCallCause.弱覆盖;
                }
                else if (this.SinrInfo.Avg <= blockCallCond.PoorSinr)
                {
                    BlockCause = ENRBlockCallCause.质差;
                }
                else if (this.MultiCvrInfo.Avg >= blockCallCond.MultiPer)
                {
                    BlockCause = ENRBlockCallCause.高重叠覆盖;
                    IsMultiCvr = true;
                }
                else if (this.HoNum >= blockCallCond.HoNum)
                {
                    BlockCause = ENRBlockCallCause.频繁切换;
                }
            }
        }

        public class DataInfo
        {
            public int Count { get; set; }
            public double Sum { get; set; }
            public double Avg { get; set; }

            public void Add(float? data)
            {
                if (data != null)
                {
                    Sum += (float)data;
                    Count++;
                }
            }

            public void Calculate()
            {
                if (Count != 0)
                {
                    Avg = Math.Round(Sum / Count, 2);
                }
                else
                {
                    Avg = 0;
                }
            }
        }
    }

    public enum ENRBlockCallCause
    {
        异常信令,
        VoiceHangup,
        UE_Cancel,
        弱覆盖,
        质差,
        高重叠覆盖,
        频繁切换,
        其它
    }
}
