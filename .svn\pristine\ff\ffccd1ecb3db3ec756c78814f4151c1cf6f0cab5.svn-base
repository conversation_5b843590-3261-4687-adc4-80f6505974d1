﻿using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTNRDominantAreaAna
{
    public class DiyQuerySceneInfo : DIYSQLBase
    {
        public List<NRDominantAreaSceneInfo> SceneInfoList { get; private set; }

        public DiyQuerySceneInfo()
            : base()
        {
            MainDB = true;
        }

        public override string Name { get { return "查询场景信息表"; } }

        protected override string getSqlTextString()
        {
            StringBuilder selectSQL = new StringBuilder();
            selectSQL.Append($"SELECT [地市],[区县],[场景区域],[场景类型] FROM {DiyInsertSceneInfo.TableName}");
            return selectSQL.ToString();
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            int idx = 0;
            E_VType[] rType = new E_VType[4];
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx] = E_VType.E_String;
            return rType;
        }

        protected override bool isValidCondition()
        {
            SceneInfoList = new List<NRDominantAreaSceneInfo>();
            return true;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    dealReceiveData(package);
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }

        protected void dealReceiveData(Package package)
        {
            NRDominantAreaSceneInfo sceneInfo = new NRDominantAreaSceneInfo();
            sceneInfo.FillDataBySQL(package);
            SceneInfoList.Add(sceneInfo);
        }
    }
}
