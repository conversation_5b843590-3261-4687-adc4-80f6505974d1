﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.NewBlackBlock;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.BackgroundFunc
{
    public class CellAcceptFileKpiInfo
    {
        public int KpiKey { get; set; }
        public object KpiValue { get; set; }
    }
    public class CellAcceptFileInfo
    {
        public CellAcceptFileInfo(FileInfo fileInfo, LTECell cell)
        {
            this.File = fileInfo;
            this.LteCell = cell;
        }
        public int SN { get; set; }
        public int FileId
        {
            get
            {
                if (File != null)
                {
                    return File.ID;
                }
                return 0;
            }
        }

        public string FileName
        {
            get
            {
                if (File != null)
                {
                    return File.Name;
                }
                return "";
            }
        }
        public int CellId
        {
            get
            {
                if (LteCell != null)
                {
                    return LteCell.CellID;
                }
                return 0;
            }
        }
        public string CellName
        {
            get
            {
                if (LteCell != null)
                {
                    return LteCell.Name;
                }
                return "";
            }
        }
        public string BtsName
        {
            get
            {
                if (LteCell != null)
                {
                    return LteCell.BTSName;
                }
                return "";
            }
        }
        public int BtsId
        {
            get
            {
                if (LteCell != null)
                {
                    return LteCell.BelongBTS.BTSID;
                }
                return 0;
            }
        }

        public int TAC
        {
            get
            {
                if (LteCell != null)
                {
                    return LteCell.TAC;
                }
                return 0;
            }
        }
        public int ECI 
        {
            get 
            {
                if (LteCell != null)
                {
                    return LteCell.ECI;
                }
                return 0;
            } 
        }

        public LTECell LteCell { get; set; }

        public FileInfo File { get; set; }

        public int PointCount { get; set; }

        public Dictionary<uint, object> AcceptKpiDic { get; set; } = new Dictionary<uint, object>();

        public BackgroundResult ConvertToBackgroundResult(int funcId,string strProject)
        {
            BackgroundResult bgResult = new BackgroundResult();
            bgResult.SubFuncID = funcId;
            bgResult.ProjectString = strProject;
            bgResult.FileID = FileId;
            bgResult.SampleCount = PointCount;
            bgResult.FileName = FileName;
            if (File != null)
            {
                bgResult.ISTime = File.BeginTime;
                bgResult.IETime = File.EndTime;
            }
            if (LteCell != null)
            {
                bgResult.StrDesc = LteCell.Type == LTEBTSType.Outdoor ? "室外" : "室内";
                bgResult.BCCH = LteCell.EARFCN;
                bgResult.BSIC = LteCell.PCI;
                bgResult.LongitudeMid = LteCell.Longitude;
                bgResult.LatitudeMid = LteCell.Latitude;
            }
            bgResult.CellType = BackgroundCellType.LTE;
            bgResult.LAC = TAC;
            bgResult.CI = ECI;

            byte[] kpiBytes = KeyValueImageParser.ToImage(AcceptKpiDic);
            bgResult.AddImageValue(kpiBytes);

            return bgResult;
        }
    }
}
