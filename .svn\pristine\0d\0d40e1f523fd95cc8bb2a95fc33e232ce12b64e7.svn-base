﻿using System;
using System.Collections.Generic;
using System.Text;
using CsGL.Util;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class CsfbFailureEntity_Fdd : CsfbFailureEntity
    {
        public CsfbFailureEntity_Fdd()
            : base()
        {
        }

        public CsfbFailureEntity_Fdd(DTFileDataManager fi, int fromEvtIdx, int toEvtIdx, int forwardSec)
            : this()
        {
            MoMtFlag = fi.MoMtFlag;
            Event requestEvent = fi.Events[fromEvtIdx];
            RequestTime = requestEvent.DateTime;
            Event failureEvent = fi.Events[toEvtIdx];
            FailureTime = failureEvent.DateTime;
            int endIdx = fi.DTDatas.IndexOf(failureEvent);
            //从failure事件往前取数据，直到超出request事件forwardSec秒
            for (int i = endIdx; i >= 0; i--)
            {
                DTData data = fi.DTDatas[i];
                if (requestEvent.Time - data.Time > forwardSec)
                { //超出前x秒
                    break;
                }
                if (data is Event)
                {
                    Event evt = data as Event;
                    Events.Add(evt);
                    if (evt.ID == 3073 || evt.ID == 3072)
                    {
                        hasReleaseEvt = true;
                    }
                    else if (evt.ID == 3001 || evt.ID == 3002)
                    {
                        hasCallAttemptEvt = true;
                    }
                }
                else if (data is TestPoint)
                {
                    TestPoints.Add(data as TestPoint);
                }
                else if (data is Message)
                {
                    dealMsg(requestEvent, data);
                }
            }
        }

        private void dealMsg(Event requestEvent, DTData data)
        {
            Message msg = data as Message;
            Messages.Add(msg);
            if (msg is MessageWithSource && msg.SN > requestEvent.SN)
            { //取request后面的Msg
                if (msg.ID == (int)CSFBMsg_FDD.RRCConnectionRelease)
                {
                    releaseMsg = msg as MessageWithSource;
                }
                else if (msg.ID == (int)CSFBMsg_FDD.Alterting)
                {
                    alertingMsg = msg as MessageWithSource;
                }
                else if (msg.ID == (int)CSFBMsg_FDD.Disconnect)
                {
                    disconnnectMsg = msg as MessageWithSource;
                }
                else if (msg.ID == (int)CSFBMsg_FDD.CmServiceAbort)
                {
                    cmServiceAbort = true;
                }
            }
        }

        public override void Summary(CsfbFailureCondition cond)
        {
            float rsrpTotal = 0;
            int rsrpNum = 0;
            float sinrTotal = 0;
            int sinrNum = 0;
            int rxLevTotal = 0;
            int rxLevNum = 0;
            int qualTotal = 0;
            int qualNum = 0;
            float rscpTotal = 0;
            int rscpNum = 0;
            float blerTotal = 0;
            int blerNum = 0;
            foreach (TestPoint tp in TestPoints)
            {
                object value = tp["lte_fdd_RSRP"];
                if (value != null)
                {
                    float rsrp = float.Parse(value.ToString());
                    if (rsrp >= -141)
                    {
                        RsrpMax = RsrpMax == null ? rsrp : Math.Max(rsrp, (float)RsrpMax);
                        RsrpMin = RsrpMin == null ? rsrp : Math.Min(rsrp, (float)RsrpMin);
                        rsrpTotal += rsrp;
                        rsrpNum++;
                    }
                }

                value = tp["lte_fdd_SINR"];
                if (value != null)
                {
                    float sinr = float.Parse(value.ToString());
                    if (sinr >= -50 && sinr <= 50)
                    {
                        SinrMax = SinrMax == null ? sinr : Math.Max(sinr, (float)SinrMax);
                        SinrMin = SinrMin == null ? sinr : Math.Min(sinr, (float)SinrMin);
                        sinrTotal += sinr;
                        sinrNum++;
                    }
                }

                value = tp["lte_fdd_gsm_DM_RxLevSub"];
                if (value != null)
                {
                    int rxLev = int.Parse(value.ToString());
                    if (-120 <= rxLev && rxLev <= -10)
                    {
                        RxLevSubMax = RxLevSubMax == null ? rxLev : Math.Max(rxLev, (int)RxLevSubMax);
                        RxLevSubMin = RxLevSubMin == null ? rxLev : Math.Min(rxLev, (int)RxLevSubMin);
                        rxLevTotal += rxLev;
                        rxLevNum++;
                    }
                }

                value = tp["lte_fdd_gsm_DM_RxQualSub"];
                if (value != null)
                {
                    int qual = int.Parse(value.ToString());
                    if (0 <= qual && qual <= 8)
                    {
                        RxQualMax = RxQualMax == null ? qual : Math.Max(qual, (int)RxQualMax);
                        RxQualMin = RxQualMin == null ? qual : Math.Min(qual, (int)RxQualMin);
                        qualTotal += qual;
                        qualNum++;
                    }
                }

                value = tp["lte_fdd_wcdma_TotalRSCP"];
                if (value != null)
                {
                    float rscp = float.Parse(value.ToString());
                    if (-140 <= rscp && rscp <= 10)
                    {
                        RscpMax = RscpMax == null ? rscp : Math.Max(rscp, (int)RscpMax);
                        RscpMin = RscpMin == null ? rscp : Math.Min(rscp, (int)RscpMin);
                        rscpTotal += rscp;
                        rscpNum++;
                    }
                }

                value = tp["lte_fdd_wcdma_TotalEc_Io"];
                if (value != null)
                {
                    float bler = float.Parse(value.ToString());
                    if (-40 <= bler && bler <= 0)
                    {
                        BlerMax = BlerMax == null ? bler : Math.Max(bler, (int)BlerMax);
                        BlerMin = BlerMin == null ? bler : Math.Min(bler, (int)BlerMin);
                        blerTotal += bler;
                        blerNum++;
                    }
                }
            }
            if (rsrpNum != 0)
            {
                RsrpAvg = (float)Math.Round(rsrpTotal / rsrpNum, 2);
            }
            if (sinrNum != 0)
            {
                SinrAvg = (float)Math.Round(sinrTotal / sinrNum, 2);
            }
            if (rxLevNum != 0)
            {
                RxLevSubAvg = rxLevTotal / rxLevNum;
            }
            if (qualNum != 0)
            {
                RxQualAvg = qualTotal / qualNum;
            }
            if (rscpNum != 0)
            {
                RscpAvg = (float)Math.Round(rscpTotal / rscpNum, 2);
            }
            if (blerNum != 0)
            {
                BlerAvg = (float)Math.Round(blerTotal / blerNum, 2);
            }

            evaluate(cond);
            getRoadDesc();
        }

        protected override bool dealNeighbour(uint carrier, ref uint group)
        {
            MessageDecodeHelper.GetSingleUInt("lte-rrc.utra_FDD_r9", ref group);
            if (carrier != (int)ECarrierInfo.utra_FDD || group <= 0)
            {
                Cause = CsfbFailureCause.无G_W邻区配置;
                return true;
            }
            return false;
        }

        protected override bool dealNetWork(CsfbFailureCondition cond)
        {
            if (RscpAvg != null && RscpAvg <= cond.Pccpch_Rscp)
            {
                Cause = CsfbFailureCause.W网络弱覆盖;
                return true;
            }
            //if (BlerAvg != null && BlerAvg >= cond.Bler)
            if (BlerAvg != null && BlerAvg <= cond.Bler)
            {
                Cause = CsfbFailureCause.W网络高质差;
                return true;
            }
            return false;
        }
    }
}
