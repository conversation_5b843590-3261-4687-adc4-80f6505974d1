﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Stat;
using MasterCom.RAMS.UserMng;
using MasterCom.MTGis;
using MasterCom.MControls;

namespace MasterCom.RAMS.Net
{
    public class DIYQueryCoverGridByRegion : DIYGridQuery
    {
        public DIYQueryCoverGridByRegion(MainModel mainModel)
            : base(mainModel)
        {
        }
        public override string Name
        {
            get { return "栅格查询(按区域)"; }
        }
        public override string IconName
        {
            get { return null; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 11000, 11015, this.Name);
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.Region;
        }

        protected override void AddGeographicFilter(Package package)
        {
            this.AddDIYRegion_Intersect(package);
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            if (searchGeometrys.Region == null)
            {
                return false;
            }
            return searchGeometrys.IsValidRegion;
        }

        protected override bool isValidStatImg(double lng, double lat)
        {
            MasterCom.RAMS.Grid.GridUnitBase grid = new Grid.GridUnitBase(lng, lat);
            return condition.Geometorys.GeoOp.Contains(grid.CenterLng,grid.CenterLat);
        }

        protected override void afterRecieveOnePeriodData(params object[] reservedParams)
        {
            //按图例设置栅格个数
            Dictionary<ColorRange, int> gridColorRangeCountStaticsDic = new Dictionary<ColorRange, int>();
            foreach (var unit in mainModel.CurGridColorUnitMatrix)
            {
                Grid.GridColorModeItem colorMode  = mainModel.MainForm.GetMapForm().GetGridShowLayer().CurUsingColorMode;

                float retf = (float)unit.DataHub.CalcValueByFormula(colorMode.formula);
                ColorRange cr = colorMode.GetColorRange(retf);
                if (cr != null)
                {
                    int ct;
                    if (gridColorRangeCountStaticsDic.TryGetValue(cr, out ct))
                    {
                        gridColorRangeCountStaticsDic[cr] = ct + 1;
                    }
                    else
                    {
                        gridColorRangeCountStaticsDic[cr] = 1;
                    }
                }

            }
            mainModel.MainForm.LegendPanel.UpdateSetGridColorStaticsCount(gridColorRangeCountStaticsDic);
        }
    }
}
