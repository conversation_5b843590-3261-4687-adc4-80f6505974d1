using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class WeakC_IRoadSettingDlg : BaseDialog
    {
        public WeakC_IRoadSettingDlg(int ciThreshold,int rxLevThreshold,float distanceLastThreshold,float distanceTPThreshold)
        {
            InitializeComponent();
            spinEditC_I.Value = ciThreshold;
            spinEditRxLev.Value = rxLevThreshold;
            spinEditDistanceLast.Value = (decimal)distanceLastThreshold;
            spinEditDistanceTP.Value = (decimal)distanceTPThreshold;
        }

        public void GetSettingValue(out int ciThreshold, out int rxLevThreshold, out float distanceLastThreshold, out float distanceTPThreshold)
        {
            ciThreshold = (int)spinEditC_I.Value;
            rxLevThreshold = (int)spinEditRxLev.Value;
            distanceLastThreshold = (float)spinEditDistanceLast.Value;
            distanceTPThreshold = (float)spinEditDistanceTP.Value;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
        }
    }
}