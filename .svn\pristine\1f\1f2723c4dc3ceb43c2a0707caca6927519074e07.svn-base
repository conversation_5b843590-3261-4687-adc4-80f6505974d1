﻿using MasterCom.RAMS.Model.Interface;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.Model
{
    public static class NRTpHelper
    {
        public static NRTpManager NrTpManager { get; set; } = new NRTpManager();
        public static NRLTETpManager NrLteTpManager { get; set; } = new NRLTETpManager();

        public static NRScanManager NrScanTpManager { get; set; } = new NRScanManager();

        public enum NRNCellType
        {
            PCELL,
            NCELL,
            UNKNOWN
        }

        public static float? GetValidData(float? data, float min, float max)
        {
            bool isValid = JudgeValidData(data, min, max);
            if (isValid)
            {
                return data;
            }
            return null;
        }

        public static bool JudgeValidData(float? data, double minValue, double maxValue)
        {
            return data != null && data > minValue && data <= maxValue;
        }

        #region 继承BackgroundQueryBase基础回放参数设置
        public static List<string> InitBaseReplayParamBackground(bool includeLte, bool includeNeighbor)
        {
            List<string> columns = new List<string>();
            InitNrParamBackground(columns);

            if (includeNeighbor)
            {
                InitNrNCellParamBackground(columns);
            }
            if (includeLte)
            {
                InitNrLteParamBackground(columns);
                if (includeNeighbor)
                {
                    InitNrLteNCellParamBackground(columns);
                }
            }

            return columns;
        }

        public static void InitNrParamBackground(List<string> columns)
        {
            columns.Add("NR_SSB_ARFCN");
            columns.Add("NR_PCI");
            columns.Add("NR_TAC");
            columns.Add("NR_NCI");
            columns.Add("NR_SS_RSRP");
            columns.Add("NR_SS_SINR");
        }

        public static void InitNrNCellParamBackground(List<string> columns)
        {
            columns.Add("NR_NCell_CellType");
            columns.Add("NR_NCell_ARFCN");
            columns.Add("NR_NCell_PCI");
            //columns.Add("NR_NCell_TAC");
            //columns.Add("NR_NCell_NCI");
            columns.Add("NR_NCell_RSRP");
            columns.Add("NR_NCell_SINR");
        }

        public static void InitNrLteParamBackground(List<string> columns)
        {
            columns.Add("NR_lte_EARFCN");
            columns.Add("NR_lte_PCI");
            columns.Add("NR_lte_TAC");
            columns.Add("NR_lte_ECI");
            columns.Add("NR_lte_RSRP");
            columns.Add("NR_lte_SINR");
        }

        public static void InitNrLteNCellParamBackground(List<string> columns)
        {
            columns.Add("NR_lte_NCell_EARFCN");
            columns.Add("NR_lte_NCell_PCI");
            //columns.Add("NR_lte_NCell_TAC");
            //columns.Add("NR_lte_NCell_ECI");
            columns.Add("NR_lte_NCell_RSRP");
            columns.Add("NR_lte_NCell_SINR");
        }

        public static List<string> InitNrScanParamBackground()
        {
            List<string> columns = new List<string>();
            columns.Add("NRSCAN_GroupNum");
            columns.Add("NRSCAN_SSB_RSRP");
            columns.Add("NRSCAN_SSB_SINR");
            columns.Add("NRSCAN_EARFCN");
            columns.Add("NRSCAN_PCI");
            return columns;
        }
        #endregion

        #region 继承DIYSampleQuery基础回放参数设置
        public static List<object> InitBaseReplayParamSample(bool includeLte, bool includeNeighbor)
        {
            List<object> columnsDef = new List<object>();
            InitNrParamSample(columnsDef);

            if (includeNeighbor)
            {
                InitNrNCellParamSample(columnsDef);
            }
            if (includeLte)
            {
                InitNrLteNParamSample(columnsDef);
                if (includeNeighbor)
                {
                    InitNrLteNCellParamSample(columnsDef);
                }
            }
            return columnsDef;
        }

        public static void InitNrParamSample(List<object> columnsDef)
        {
            AddParam(columnsDef, "NR_SSB_ARFCN", 0);
            AddParam(columnsDef, "NR_PCI", 0);
            AddParam(columnsDef, "NR_TAC", 0);
            AddParam(columnsDef, "NR_NCI", 0);
            AddParam(columnsDef, "NR_SS_RSRP", 0);
            AddParam(columnsDef, "NR_SS_SINR", 0);
        }

        public static void InitNrNCellParamSample(List<object> columnsDef)
        {
            for (int i = 0; i < 16; i++)
            {
                AddParam(columnsDef, "NR_NCell_CellType", i);
                AddParam(columnsDef, "NR_NCell_ARFCN", i);
                AddParam(columnsDef, "NR_NCell_PCI", i);
                //AddParam(columnsDef, "NR_NCell_TAC", i);
                //AddParam(columnsDef, "NR_NCell_NCI", i);
                AddParam(columnsDef, "NR_NCell_RSRP", i);
                AddParam(columnsDef, "NR_NCell_SINR", i);
            }
        }

        public static void InitNrLteNParamSample(List<object> columnsDef)
        {
            AddParam(columnsDef, "NR_lte_EARFCN", 0);
            AddParam(columnsDef, "NR_lte_PCI", 0);
            AddParam(columnsDef, "NR_lte_TAC", 0);
            AddParam(columnsDef, "NR_lte_ECI", 0);
            AddParam(columnsDef, "NR_lte_RSRP", 0);
            AddParam(columnsDef, "NR_lte_SINR", 0);
        }

        public static void InitNrLteNCellParamSample(List<object> columnsDef)
        {
            AddParam(columnsDef, "NR_lte_NCell_EARFCN", 0);
            AddParam(columnsDef, "NR_lte_NCell_PCI", 0);
            //AddParam(columnsDef, "NR_lte_NCell_TAC", 0);
            //AddParam(columnsDef, "NR_lte_NCell_ECI", 0);
            AddParam(columnsDef, "NR_lte_NCell_RSRP", 0);
            AddParam(columnsDef, "NR_lte_NCell_SINR", 0);
        }

        public static void InitNrScanParamSample(List<object> columnsDef)
        {
            AddParam(columnsDef, "NRSCAN_GroupNum", 0);
            AddParam(columnsDef, "NRSCAN_SSB_RSRP", 0);
            AddParam(columnsDef, "NRSCAN_SSB_SINR", 0);
            AddParam(columnsDef, "NRSCAN_EARFCN", 0);
            AddParam(columnsDef, "NRSCAN_PCI", 0);
            AddParam(columnsDef, "NR_SS_SINR", 0);
        }

        public static DIYSampleGroup InitNrScanParamSample(string themeName)
        {
            List<object> columnsDef = new List<object>();

            InitNrScanParamSample(columnsDef);

            Dictionary<string, object> tmpDic = new Dictionary<string, object>();
            tmpDic.Add("name", (object)">NR扫频");
            tmpDic.Add("themeName", (object)themeName);
            tmpDic.Add("columnsDef", (object)columnsDef);

            DIYSampleGroup group = new DIYSampleGroup();
            group.Param = tmpDic;
            return group;
        }

        public static void AddParam(List<object> columnsDef, string name, int index)
        {
            Dictionary<string, object> param = new Dictionary<string, object>();
            param["param_name"] = name;
            param["param_arg"] = index;
            columnsDef.Add((object)param);
        }
        #endregion
    }

    public class NRTpManager : NRTpManagerBase
    {
        #region 主服
        public override float? GetSCellRsrp(TestPoint tp, bool judgeRange = false)
        {
            float? data = (float?)tp["NR_SS_RSRP"];
            return judgeRangeFunc(judgeRange, data, -150, 30);
        }
        public override float? GetSCellSinr(TestPoint tp, bool judgeRange = false)
        {
            float? data = (float?)tp["NR_SS_SINR"];
            return judgeRangeFunc(judgeRange, data, -50, 50);
        }
        public override object GetEARFCN(TestPoint tp)
        {
            return (int?)tp["NR_SSB_ARFCN"];
        }
        public override object GetPCI(TestPoint tp)
        {
            return (int?)tp["NR_PCI"];
        }
        public override object GetTAC(TestPoint tp)
        {
            return (int?)tp["NR_TAC"];
        }
        public override object GetNCI(TestPoint tp)
        {
            long? nci = (long?)tp["NR_NCI"];
            int? tac = (int?)GetTAC(tp);
            if (tac == null && nci == 4284967296)
            {
                return null;
            }

            return nci;
        }

        public float? GetSCellRsrq(TestPoint tp, bool judgeRange = false)
        {
            float? data = (float?)tp["NR_SS_RSRQ"];
            return judgeRangeFunc(judgeRange, data, -40, 40);
        }

        public float? GetSCellRssi(TestPoint tp, bool judgeRange = false)
        {
            float? data = (float?)tp["NR_SS_RSSI"];
            return judgeRangeFunc(judgeRange, data, -150, 30);
        }
        #endregion

        #region 邻区
        public override float? GetNCellRsrp(TestPoint tp, int index, bool judgeRange = false)
        {
            float? data = (float?)tp["NR_NCell_RSRP", index];
            return judgeRangeFunc(judgeRange, data, -150, 30);
        }
        public override float? GetNCellSinr(TestPoint tp, int index, bool judgeRange = false)
        {
            float? data = (float?)tp["NR_NCell_SINR", index];
            return judgeRangeFunc(judgeRange, data, -50, 50);
        }
        public override object GetNEARFCN(TestPoint tp, int index)
        {
            return (int?)tp["NR_NCell_ARFCN", index];
        }
        public override object GetNPCI(TestPoint tp, int index)
        {
            return (int?)tp["NR_NCell_PCI", index];
        }

        public NRTpHelper.NRNCellType GetNCellType(TestPoint tp, int index)
        {
            string type = tp["NR_NCell_CellType", index]?.ToString();
            switch (type)
            {
                case "PCell":
                    return NRTpHelper.NRNCellType.PCELL;
                case "NCell":
                    return NRTpHelper.NRNCellType.NCELL;
                default:
                    return NRTpHelper.NRNCellType.UNKNOWN;
            }
        }

        public bool JudgeIsNCell(TestPoint tp, int index)
        {
            NRTpHelper.NRNCellType type = GetNCellType(tp, index);
            if (type == NRTpHelper.NRNCellType.NCELL)
            {
                return true;
            }
            return false;
        }
        #endregion

        #region 速率
        public short? GetAppType(TestPoint tp)
        {
            return (short?)tp["NR_APP_type"];
        }
        public short? GetAppStatus(TestPoint tp)
        {
            return (short?)tp["NR_APP_Status"];
        }
        public double? GetAppSpeedMb(TestPoint tp)
        {
            return (double?)tp["NR_APP_Speed_Mb"];
        }
        public double? GetAppThroughputDLMb(TestPoint tp)
        {
            return (double?)tp["NR_APP_ThroughputDL_Mb"];
        }

        #region 下载
        public double? GetMacDLMb(TestPoint tp)
        {
            return (double?)tp["NR_Throughput_MAC_DL_Mb"];
        }

        public double? GetPdcpDLMb(TestPoint tp)
        {
            return (double?)tp["NR_Throughput_PDCP_DL_Mb"];
        }
        #endregion

        #region 上传
        public double? GetMacULMb(TestPoint tp)
        {
            return (double?)tp["NR_Throughput_MAC_UL_Mb"];
        }

        public double? GetPdcpULMb(TestPoint tp)
        {
            return (double?)tp["NR_Throughput_PDCP_UL_Mb"];
        }
        #endregion
        #endregion

        #region MOS
        public float? GetMosInfo(TestPoint tp, ref string curMosParamName)
        {
            if (string.IsNullOrEmpty(curMosParamName))
            {
                float? pesqLQ = (float?)tp["NR_PESQLQ"];
                //float? pesqMos = (float?)tp["NR_PESQMos"];
                //float? pesqScore = (float?)tp["NR_PESQScore"];
                float? polqa = (float?)tp["NR_POLQA_Score_SWB"];

                if (NRTpHelper.JudgeValidData(polqa, 0, 5))
                {
                    curMosParamName = "NR_POLQA_Score_SWB";
                    return polqa;
                }
                else if (NRTpHelper.JudgeValidData(pesqLQ, 0, 5))
                {
                    curMosParamName = "NR_PESQLQ";
                    return pesqLQ;
                }
                //else if (NRTpHelper.JudgeValidData(pesqMos, 0, 5))
                //{
                //    curMosParamName = "NR_PESQMos";
                //    return pesqMos;
                //}
                //else if (NRTpHelper.JudgeValidData(pesqScore, 0, 5))
                //{
                //    curMosParamName = "NR_PESQScore";
                //    return pesqScore;
                //}
            }
            else
            {
                float? mos = (float?)tp[curMosParamName];
                if (NRTpHelper.JudgeValidData(mos, 0, 5))
                {
                    return mos;
                }
            }
            return null;
        }

        public float? GetMosInfo(TestPoint tp)
        {
            float? pesqLQ = (float?)tp["NR_PESQLQ"];
            //float? pesqMos = (float?)tp["NR_PESQMos"];
            //float? pesqScore = (float?)tp["NR_PESQScore"];
            float? polqa = (float?)tp["NR_POLQA_Score_SWB"];
            if (NRTpHelper.JudgeValidData(polqa, 0, 5))
            {
                return polqa;
            }
            else if (NRTpHelper.JudgeValidData(pesqLQ, 0, 5))
            {
                return pesqLQ;
            }
            //else if (NRTpHelper.JudgeValidData(pesqMos, 0, 5))
            //{
            //    return pesqMos;
            //}
            //else if (NRTpHelper.JudgeValidData(pesqScore, 0, 5))
            //{
            //    return pesqScore;
            //}
            return null;
        }
        #endregion
    }

    public class NRLTETpManager : NRTpManagerBase
    {
        #region 主服
        public override float? GetSCellRsrp(TestPoint tp, bool judgeRange = false)
        {
            float? data = (float?)tp["NR_lte_RSRP"];
            return judgeRangeFunc(judgeRange, data, -150, 30);
        }
        public override float? GetSCellSinr(TestPoint tp, bool judgeRange = false)
        {
            float? data = (float?)tp["NR_lte_SINR"];
            return judgeRangeFunc(judgeRange, data, -50, 50);
        }
        public override object GetEARFCN(TestPoint tp)
        {
            return (int?)tp["NR_lte_EARFCN"];
        }
        public override object GetPCI(TestPoint tp)
        {
            return (int?)(short?)tp["NR_lte_PCI"];
        }
        public override object GetTAC(TestPoint tp)
        {
            return (int?)tp["NR_lte_TAC"];
        }
        public override object GetNCI(TestPoint tp)
        {
            return (int?)tp["NR_lte_ECI"];
        }
        #endregion

        #region 邻区
        public override float? GetNCellRsrp(TestPoint tp, int index, bool judgeRange = false)
        {
            float? data = (float?)tp["NR_lte_NCell_RSRP", index];
            return judgeRangeFunc(judgeRange, data, -150, 30);
        }
        public override float? GetNCellSinr(TestPoint tp, int index, bool judgeRange = false)
        {
            float? data = (float?)tp["NR_lte_NCell_SINR", index];
            return judgeRangeFunc(judgeRange, data, -50, 50);
        }
        public override object GetNEARFCN(TestPoint tp, int index)
        {
            return (int?)tp["NR_lte_NCell_EARFCN", index];
        }
        public override object GetNPCI(TestPoint tp, int index)
        {
            return (int?)tp["NR_lte_NCell_PCI", index];
        }
        #endregion
    }

    public class NRScanManager : NRScanTpManagerBase
    {
        public string RsrpThemeName { get; } = "NR_SCAN_SSB_RSRP";
        public string SinrThemeName { get; } = "NR_SCAN_SSB_SINR";

        public string RsrpFullThemeName { get; } = "NR_SCAN:NR_SCAN_SSB_RSRP";
        public string SinrFullThemeName { get; } = "NR_SCAN:NR_SCAN_SSB_SINR";

        public override float? GetCellRsrp(TestPoint tp, int index, bool judgeRange = false)
        {
            float? data = (float?)tp["NRSCAN_SSB_RSRP", index];
            return judgeRangeFunc(judgeRange, data, -150, 30);
        }

        public override float? GetCellSinr(TestPoint tp, int index, bool judgeRange = false)
        {
            float? data = (float?)tp["NRSCAN_SSB_SINR", index];
            return judgeRangeFunc(judgeRange, data, -40, 40);
        }

        public override object GetEARFCN(TestPoint tp, int index)
        {
            int? earfcn = (int?)tp["NRSCAN_EARFCN", index];
            return earfcn;
        }

        public override object GetPCI(TestPoint tp, int index)
        {
            int? pci = (int?)tp["NRSCAN_PCI", index];
            return pci;
        }

        public int? GetCellGroup(TestPoint tp, int index)
        {
            int? data = (int?)tp["NRSCAN_GroupNum", index];
            return data;
        }

        public Dictionary<int, int> GetCellMaxBeam(TestPoint tp)
        {
            Dictionary<int, int> groupDic = new Dictionary<int, int>();
            //NR扫频数据每个Group有8个Beam
            //采样点数据仅按Rsrp大小排序
            //因此每个Group取到的第一条数据就是这个小区Beam的最大值
            for (int index = 0; index < 50; index++)
            {
                int? group = GetCellGroup(tp, index);
                if (group == null)
                {
                    break;
                }
                int data = (int)group;
                if (groupDic.ContainsKey(data))
                {
                    continue;
                }

                groupDic.Add(data, index);
            }
            return groupDic;
        }
    }

    public abstract class NRTpManagerBase : TpManagerBase
    {
        public abstract float? GetSCellRsrp(TestPoint tp, bool judgeRange = false);
        public abstract float? GetSCellSinr(TestPoint tp, bool judgeRange = false);
        public abstract object GetEARFCN(TestPoint tp);
        public abstract object GetPCI(TestPoint tp);
        public abstract object GetTAC(TestPoint tp);
        public abstract object GetNCI(TestPoint tp);

        public abstract float? GetNCellRsrp(TestPoint tp, int index, bool judgeRange = false);
        public abstract float? GetNCellSinr(TestPoint tp, int index, bool judgeRange = false);
        public abstract object GetNEARFCN(TestPoint tp, int index);
        public abstract object GetNPCI(TestPoint tp, int index);
    }

    public abstract class NRScanTpManagerBase : TpManagerBase
    {
        public abstract float? GetCellRsrp(TestPoint tp, int index, bool judgeRange = false);
        public abstract float? GetCellSinr(TestPoint tp, int index, bool judgeRange = false);
        public abstract object GetEARFCN(TestPoint tp, int index);
        public abstract object GetPCI(TestPoint tp, int index);
    }

    public abstract class TpManagerBase
    {
        protected float? judgeRangeFunc(bool judgeRange, float? data, double minValue, double maxValue)
        {
            if (!judgeRange || NRTpHelper.JudgeValidData(data, minValue, maxValue))
            {
                return data;
            }
            return null;
        }
    }
}
