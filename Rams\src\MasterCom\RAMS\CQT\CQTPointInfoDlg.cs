﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.CQT
{
    public partial class CQTPointInfoDlg : BaseDialog
    {
        private CQTPoint cqtPoint;
        public CQTPointInfoDlg()
        {
            InitializeComponent();
        }

        public void FillData(CQTPoint cqtPoint)
        {
            this.SuspendLayout();
            gcAddrInfo.BeginInit();
            groupControl1.BeginInit();
            this.cqtPoint = cqtPoint;
            fillBasicInfo();
            filleKpiInfo();
            groupControl1.EndInit();
            gcAddrInfo.EndInit();
            PerformLayout();
            this.ResumeLayout();
        }

        private void fillBasicInfo()
        {
            Text = cqtPoint.Name;
            txtLongitude.Text = cqtPoint.Longitude.ToString();
            txtLatitude.Text = cqtPoint.Latitude.ToString();
            txtCoverType.Text = cqtPoint.CoverTypeDesc;
            txtNetType.Text = cqtPoint.NetworkTypeDesc;
            txtSpaceType.Text = cqtPoint.SpaceTypeDesc;
            txtDensityType.Text = cqtPoint.DensityTypeDesc;
            txtPointType.Text = cqtPoint.PointTypeDesc;
        }

        private void filleKpiInfo()
        {
            CQTMainPointKPI mainPntData;
            List<CQTFileKPIData> filesData;
            CQTKPIDataManager kpiDataMng = CQTKPIDataManager.GetInstance();
            kpiDataMng.GetCurShowPointKPI(cqtPoint, out mainPntData, out filesData);
            if (mainPntData==null)
            {
                showKPIInfo(false);
                return;
            }
            showKPIInfo(true);
            string kpiCaption = "";
            if (kpiDataMng.CurColorColumn is CQTKPIReportColumn)
            {
                CQTKPIReportColumn rCol = kpiDataMng.CurColorColumn as CQTKPIReportColumn;
                kpiCaption = rCol.Name;
            }
            else if(kpiDataMng.CurColorColumn is CQTKPISummaryColumn)
            {
                CQTKPISummaryColumn sCol = kpiDataMng.CurColorColumn as CQTKPISummaryColumn;
                kpiCaption = sCol.Name;
            }
            object curColorCol = kpiDataMng.CurColorColumn;
            dataGridViewMain.Columns[0].HeaderText = kpiCaption;
            dataGridViewAbove.Columns[1].HeaderText = kpiCaption;
            dataGridViewBelow.Columns[1].HeaderText = kpiCaption;
            if (curColorCol is CQTKPIReportColumn)
            {
                CQTKPIReportColumn color = curColorCol as CQTKPIReportColumn;
                dataGridViewMain.Rows.Clear();
                dataGridViewBelow.Rows.Clear();
                dataGridViewAbove.Rows.Clear();

                double kpiValue;
                double score;
                kpiValue = mainPntData.ColumnKPIResultDic[color].KPIValue;
                score = mainPntData.ColumnKPIResultDic[color].Score;
                int idx = dataGridViewMain.Rows.Add();
                DataGridViewRow mRow = dataGridViewMain.Rows[idx];
      
                if (!double.IsNaN(kpiValue))
                {
                    mRow.Cells[0].Value = kpiValue;
                    mRow.Cells[1].Value = Math.Round(score, 2);
                    foreach (CQTFileKPIData fileData in filesData)
                    {
                        double fileKpiValue = fileData.ColumnKPIResultDic[color].KPIValue;
                        double fileScore = fileData.ColumnKPIResultDic[color].Score;
                        if (double.IsNaN(fileKpiValue)||fileKpiValue>=kpiValue)
                        {
                            int rowIdx = dataGridViewAbove.Rows.Add();
                            DataGridViewRow row = dataGridViewAbove.Rows[rowIdx];
                            row.Cells[0].Value = fileData.DataHeader.Name;
                            row.Cells[1].Value = fileKpiValue;
                            row.Cells[2].Value = Math.Round(fileScore, 2);
                            row.Tag = fileData.DataHeader;
                        }
                        else if(fileKpiValue<kpiValue)
                        {
                            int rowIdx = dataGridViewBelow.Rows.Add();
                            DataGridViewRow row = dataGridViewBelow.Rows[rowIdx];
                            row.Cells[0].Value = fileData.DataHeader.Name;
                            row.Cells[1].Value = fileKpiValue;
                            row.Cells[2].Value =Math.Round( fileScore,2);
                            row.Tag = fileData.DataHeader;
                        }
                    }
                }
            }
        }

        private void showKPIInfo(bool show)
        {
            if (show)
            {
                this.Height = 446;
            }
            else
            {
                this.Height = 152;
            }
        }

        private void CQTPointInfoDlg_Deactivate(object sender, EventArgs e)
        {
           // Visible = false;
        }

        private void CQTPointInfoDlg_FormClosing(object sender, FormClosingEventArgs e)
        {
            e.Cancel = true;
            Visible = false;
        }

        private void miReplayBeloveFiles_Click(object sender, EventArgs e)
        {
            replayViewFiles(dataGridViewBelow);
        }

        private void miReplayAboveFiles_Click(object sender, EventArgs e)
        {
            replayViewFiles(dataGridViewAbove);
        }

        private void replayViewFiles(DataGridView view)
        {
            List<FileInfo> files = new List<FileInfo>();
            foreach (DataGridViewRow row in view.SelectedRows)
            {
                FileInfo fi = row.Tag as FileInfo;
                if (fi != null)
                {
                    files.Add(fi);
                }
            }
            if (files.Count > 0)
            {
                DIYReplayFileQuery query = new DIYReplayFileQuery(MainModel.GetInstance());
                QueryCondition qc = new QueryCondition();
                qc.FileInfos = files;
                query.SetQueryCondition(qc);
                query.Query();
            }
            else
            {
                MessageBox.Show("请选择文件！");
            }
        }

    }
}
