﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class TdAntParaCommonForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle1 = new System.Windows.Forms.DataGridViewCellStyle();
            DevExpress.XtraCharts.XYDiagram xyDiagram1 = new DevExpress.XtraCharts.XYDiagram();
            DevExpress.XtraCharts.SecondaryAxisY secondaryAxisY1 = new DevExpress.XtraCharts.SecondaryAxisY();
            DevExpress.XtraCharts.SecondaryAxisY secondaryAxisY2 = new DevExpress.XtraCharts.SecondaryAxisY();
            DevExpress.XtraCharts.Series series1 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel1 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.SideBySideBarSeriesView sideBySideBarSeriesView1 = new DevExpress.XtraCharts.SideBySideBarSeriesView();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel2 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.RadarDiagram radarDiagram1 = new DevExpress.XtraCharts.RadarDiagram();
            DevExpress.XtraCharts.Series series2 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.RadarPointSeriesLabel radarPointSeriesLabel1 = new DevExpress.XtraCharts.RadarPointSeriesLabel();
            DevExpress.XtraCharts.SeriesPoint seriesPoint1 = new DevExpress.XtraCharts.SeriesPoint("360");
            DevExpress.XtraCharts.SeriesPoint seriesPoint2 = new DevExpress.XtraCharts.SeriesPoint("350");
            DevExpress.XtraCharts.SeriesPoint seriesPoint3 = new DevExpress.XtraCharts.SeriesPoint("340");
            DevExpress.XtraCharts.SeriesPoint seriesPoint4 = new DevExpress.XtraCharts.SeriesPoint("330");
            DevExpress.XtraCharts.SeriesPoint seriesPoint5 = new DevExpress.XtraCharts.SeriesPoint("320");
            DevExpress.XtraCharts.RadarLineSeriesView radarLineSeriesView1 = new DevExpress.XtraCharts.RadarLineSeriesView();
            DevExpress.XtraCharts.Series series3 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.RadarPointSeriesLabel radarPointSeriesLabel2 = new DevExpress.XtraCharts.RadarPointSeriesLabel();
            DevExpress.XtraCharts.RadarLineSeriesView radarLineSeriesView2 = new DevExpress.XtraCharts.RadarLineSeriesView();
            DevExpress.XtraCharts.RadarPointSeriesLabel radarPointSeriesLabel3 = new DevExpress.XtraCharts.RadarPointSeriesLabel();
            DevExpress.XtraCharts.RadarLineSeriesView radarLineSeriesView3 = new DevExpress.XtraCharts.RadarLineSeriesView();
            DevExpress.XtraCharts.RadarDiagram radarDiagram2 = new DevExpress.XtraCharts.RadarDiagram();
            DevExpress.XtraCharts.Series series4 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.RadarPointSeriesLabel radarPointSeriesLabel4 = new DevExpress.XtraCharts.RadarPointSeriesLabel();
            DevExpress.XtraCharts.SeriesPoint seriesPoint6 = new DevExpress.XtraCharts.SeriesPoint("360");
            DevExpress.XtraCharts.SeriesPoint seriesPoint7 = new DevExpress.XtraCharts.SeriesPoint("350");
            DevExpress.XtraCharts.SeriesPoint seriesPoint8 = new DevExpress.XtraCharts.SeriesPoint("340");
            DevExpress.XtraCharts.SeriesPoint seriesPoint9 = new DevExpress.XtraCharts.SeriesPoint("330");
            DevExpress.XtraCharts.SeriesPoint seriesPoint10 = new DevExpress.XtraCharts.SeriesPoint("320");
            DevExpress.XtraCharts.RadarLineSeriesView radarLineSeriesView4 = new DevExpress.XtraCharts.RadarLineSeriesView();
            DevExpress.XtraCharts.Series series5 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.RadarPointSeriesLabel radarPointSeriesLabel5 = new DevExpress.XtraCharts.RadarPointSeriesLabel();
            DevExpress.XtraCharts.RadarLineSeriesView radarLineSeriesView5 = new DevExpress.XtraCharts.RadarLineSeriesView();
            DevExpress.XtraCharts.RadarPointSeriesLabel radarPointSeriesLabel6 = new DevExpress.XtraCharts.RadarPointSeriesLabel();
            DevExpress.XtraCharts.RadarLineSeriesView radarLineSeriesView6 = new DevExpress.XtraCharts.RadarLineSeriesView();
            DevExpress.XtraCharts.RadarDiagram radarDiagram3 = new DevExpress.XtraCharts.RadarDiagram();
            DevExpress.XtraCharts.Series series6 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.RadarPointSeriesLabel radarPointSeriesLabel7 = new DevExpress.XtraCharts.RadarPointSeriesLabel();
            DevExpress.XtraCharts.SeriesPoint seriesPoint11 = new DevExpress.XtraCharts.SeriesPoint("360");
            DevExpress.XtraCharts.SeriesPoint seriesPoint12 = new DevExpress.XtraCharts.SeriesPoint("350");
            DevExpress.XtraCharts.SeriesPoint seriesPoint13 = new DevExpress.XtraCharts.SeriesPoint("340");
            DevExpress.XtraCharts.SeriesPoint seriesPoint14 = new DevExpress.XtraCharts.SeriesPoint("330");
            DevExpress.XtraCharts.SeriesPoint seriesPoint15 = new DevExpress.XtraCharts.SeriesPoint("320");
            DevExpress.XtraCharts.RadarPointSeriesView radarPointSeriesView1 = new DevExpress.XtraCharts.RadarPointSeriesView();
            DevExpress.XtraCharts.Series series7 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.RadarPointSeriesLabel radarPointSeriesLabel8 = new DevExpress.XtraCharts.RadarPointSeriesLabel();
            DevExpress.XtraCharts.RadarPointSeriesView radarPointSeriesView2 = new DevExpress.XtraCharts.RadarPointSeriesView();
            DevExpress.XtraCharts.RadarPointSeriesLabel radarPointSeriesLabel9 = new DevExpress.XtraCharts.RadarPointSeriesLabel();
            DevExpress.XtraCharts.RadarPointSeriesView radarPointSeriesView3 = new DevExpress.XtraCharts.RadarPointSeriesView();
            DevExpress.XtraCharts.RadarDiagram radarDiagram4 = new DevExpress.XtraCharts.RadarDiagram();
            DevExpress.XtraCharts.Series series8 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.RadarPointSeriesLabel radarPointSeriesLabel10 = new DevExpress.XtraCharts.RadarPointSeriesLabel();
            DevExpress.XtraCharts.SeriesPoint seriesPoint16 = new DevExpress.XtraCharts.SeriesPoint("360");
            DevExpress.XtraCharts.SeriesPoint seriesPoint17 = new DevExpress.XtraCharts.SeriesPoint("350");
            DevExpress.XtraCharts.SeriesPoint seriesPoint18 = new DevExpress.XtraCharts.SeriesPoint("340");
            DevExpress.XtraCharts.SeriesPoint seriesPoint19 = new DevExpress.XtraCharts.SeriesPoint("330");
            DevExpress.XtraCharts.SeriesPoint seriesPoint20 = new DevExpress.XtraCharts.SeriesPoint("320");
            DevExpress.XtraCharts.RadarPointSeriesView radarPointSeriesView4 = new DevExpress.XtraCharts.RadarPointSeriesView();
            DevExpress.XtraCharts.Series series9 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.RadarPointSeriesLabel radarPointSeriesLabel11 = new DevExpress.XtraCharts.RadarPointSeriesLabel();
            DevExpress.XtraCharts.RadarPointSeriesView radarPointSeriesView5 = new DevExpress.XtraCharts.RadarPointSeriesView();
            DevExpress.XtraCharts.RadarPointSeriesLabel radarPointSeriesLabel12 = new DevExpress.XtraCharts.RadarPointSeriesLabel();
            DevExpress.XtraCharts.RadarPointSeriesView radarPointSeriesView6 = new DevExpress.XtraCharts.RadarPointSeriesView();
            DevExpress.XtraCharts.XYDiagram xyDiagram2 = new DevExpress.XtraCharts.XYDiagram();
            DevExpress.XtraCharts.Series series10 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel3 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel4 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.XYDiagram xyDiagram3 = new DevExpress.XtraCharts.XYDiagram();
            DevExpress.XtraCharts.Series series11 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel5 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel6 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.XYDiagram xyDiagram4 = new DevExpress.XtraCharts.XYDiagram();
            DevExpress.XtraCharts.Series series12 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel7 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel8 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.XYDiagram xyDiagram5 = new DevExpress.XtraCharts.XYDiagram();
            DevExpress.XtraCharts.Series series13 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel9 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel10 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.XYDiagram xyDiagram6 = new DevExpress.XtraCharts.XYDiagram();
            DevExpress.XtraCharts.Series series14 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel11 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel12 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            this.xTabGSMAnt = new DevExpress.XtraTab.XtraTabControl();
            this.pageCell = new DevExpress.XtraTab.XtraTabPage();
            this.btnNextpage = new System.Windows.Forms.Button();
            this.btnPrevpage = new System.Windows.Forms.Button();
            this.label5 = new System.Windows.Forms.Label();
            this.txtPage = new System.Windows.Forms.TextBox();
            this.labPage = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.btnGo = new System.Windows.Forms.Button();
            this.labNum = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.btnSearch = new System.Windows.Forms.Button();
            this.txtCellName = new System.Windows.Forms.TextBox();
            this.label1 = new System.Windows.Forms.Label();
            this.dataGridViewCell = new System.Windows.Forms.DataGridView();
            this.menuItem = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miShwoChart = new System.Windows.Forms.ToolStripMenuItem();
            this.miShowSimulation = new System.Windows.Forms.ToolStripMenuItem();
            this.miShowChart = new System.Windows.Forms.ToolStripMenuItem();
            this.outPutCsv = new System.Windows.Forms.ToolStripMenuItem();
            this.outPutExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.pageAngle = new DevExpress.XtraTab.XtraTabPage();
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.groupControl2 = new DevExpress.XtraEditors.GroupControl();
            this.dataGridViewAngle = new System.Windows.Forms.DataGridView();
            this.cluCellName = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.cluTarget = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.panel1 = new System.Windows.Forms.Panel();
            this.chartControl1 = new DevExpress.XtraCharts.ChartControl();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.cbbxSeries2 = new System.Windows.Forms.ComboBox();
            this.cbbxSeries1 = new System.Windows.Forms.ComboBox();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.pageBar = new DevExpress.XtraTab.XtraTabPage();
            this.groupControl3 = new DevExpress.XtraEditors.GroupControl();
            this.groupBox5 = new System.Windows.Forms.GroupBox();
            this.chartControlScan = new DevExpress.XtraCharts.ChartControl();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.chartControlDt = new DevExpress.XtraCharts.ChartControl();
            this.PageBarMr = new DevExpress.XtraTab.XtraTabPage();
            this.groupControl5 = new DevExpress.XtraEditors.GroupControl();
            this.cbPart = new System.Windows.Forms.ComboBox();
            this.label7 = new System.Windows.Forms.Label();
            this.groupBox4 = new System.Windows.Forms.GroupBox();
            this.chartCellLine = new DevExpress.XtraCharts.ChartControl();
            this.groupBox15 = new System.Windows.Forms.GroupBox();
            this.chartCellPoint = new DevExpress.XtraCharts.ChartControl();
            this.pageMR = new DevExpress.XtraTab.XtraTabPage();
            this.groupControl4 = new DevExpress.XtraEditors.GroupControl();
            this.groupBox12 = new System.Windows.Forms.GroupBox();
            this.rtbDesc = new System.Windows.Forms.RichTextBox();
            this.groupBox8 = new System.Windows.Forms.GroupBox();
            this.chartControlAoa = new DevExpress.XtraCharts.ChartControl();
            this.groupBox11 = new System.Windows.Forms.GroupBox();
            this.chartControlSinr = new DevExpress.XtraCharts.ChartControl();
            this.groupBox10 = new System.Windows.Forms.GroupBox();
            this.chartControlRsrp = new DevExpress.XtraCharts.ChartControl();
            this.groupBox7 = new System.Windows.Forms.GroupBox();
            this.chartControlPower = new DevExpress.XtraCharts.ChartControl();
            this.groupBox9 = new System.Windows.Forms.GroupBox();
            this.chartControlTA = new DevExpress.XtraCharts.ChartControl();
            ((System.ComponentModel.ISupportInitialize)(this.xTabGSMAnt)).BeginInit();
            this.xTabGSMAnt.SuspendLayout();
            this.pageCell.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewCell)).BeginInit();
            this.menuItem.SuspendLayout();
            this.pageAngle.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).BeginInit();
            this.groupControl2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewAngle)).BeginInit();
            this.groupBox2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chartControl1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(secondaryAxisY1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(secondaryAxisY2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel2)).BeginInit();
            this.groupBox1.SuspendLayout();
            this.pageBar.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl3)).BeginInit();
            this.groupControl3.SuspendLayout();
            this.groupBox5.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlScan)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarDiagram1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarLineSeriesView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarLineSeriesView2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarLineSeriesView3)).BeginInit();
            this.groupBox3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlDt)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarDiagram2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarLineSeriesView4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarLineSeriesView5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel6)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarLineSeriesView6)).BeginInit();
            this.PageBarMr.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl5)).BeginInit();
            this.groupControl5.SuspendLayout();
            this.groupBox4.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chartCellLine)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarDiagram3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series6)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel7)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series7)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel8)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesView2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel9)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesView3)).BeginInit();
            this.groupBox15.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chartCellPoint)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarDiagram4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series8)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel10)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesView4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series9)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel11)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesView5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel12)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesView6)).BeginInit();
            this.pageMR.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl4)).BeginInit();
            this.groupControl4.SuspendLayout();
            this.groupBox12.SuspendLayout();
            this.groupBox8.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlAoa)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series10)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel4)).BeginInit();
            this.groupBox11.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlSinr)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series11)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel6)).BeginInit();
            this.groupBox10.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlRsrp)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series12)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel7)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel8)).BeginInit();
            this.groupBox7.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlPower)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series13)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel9)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel10)).BeginInit();
            this.groupBox9.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlTA)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram6)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series14)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel11)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel12)).BeginInit();
            this.SuspendLayout();
            // 
            // xTabGSMAnt
            // 
            this.xTabGSMAnt.Dock = System.Windows.Forms.DockStyle.Fill;
            this.xTabGSMAnt.Location = new System.Drawing.Point(0, 0);
            this.xTabGSMAnt.Name = "xTabGSMAnt";
            this.xTabGSMAnt.SelectedTabPage = this.pageCell;
            this.xTabGSMAnt.Size = new System.Drawing.Size(1217, 622);
            this.xTabGSMAnt.TabIndex = 3;
            this.xTabGSMAnt.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.pageCell,
            this.pageAngle,
            this.pageBar,
            this.PageBarMr,
            this.pageMR});
            // 
            // pageCell
            // 
            this.pageCell.Controls.Add(this.btnNextpage);
            this.pageCell.Controls.Add(this.btnPrevpage);
            this.pageCell.Controls.Add(this.label5);
            this.pageCell.Controls.Add(this.txtPage);
            this.pageCell.Controls.Add(this.labPage);
            this.pageCell.Controls.Add(this.label4);
            this.pageCell.Controls.Add(this.btnGo);
            this.pageCell.Controls.Add(this.labNum);
            this.pageCell.Controls.Add(this.label3);
            this.pageCell.Controls.Add(this.label2);
            this.pageCell.Controls.Add(this.btnSearch);
            this.pageCell.Controls.Add(this.txtCellName);
            this.pageCell.Controls.Add(this.label1);
            this.pageCell.Controls.Add(this.dataGridViewCell);
            this.pageCell.Name = "pageCell";
            this.pageCell.Size = new System.Drawing.Size(1210, 592);
            this.pageCell.Text = "天线数据总表";
            // 
            // btnNextpage
            // 
            this.btnNextpage.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnNextpage.Location = new System.Drawing.Point(768, 571);
            this.btnNextpage.Name = "btnNextpage";
            this.btnNextpage.Size = new System.Drawing.Size(33, 23);
            this.btnNextpage.TabIndex = 16;
            this.btnNextpage.Text = ">>";
            this.btnNextpage.UseVisualStyleBackColor = true;
            this.btnNextpage.Click += new System.EventHandler(this.btnNextpage_Click);
            // 
            // btnPrevpage
            // 
            this.btnPrevpage.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnPrevpage.Location = new System.Drawing.Point(729, 571);
            this.btnPrevpage.Name = "btnPrevpage";
            this.btnPrevpage.Size = new System.Drawing.Size(33, 23);
            this.btnPrevpage.TabIndex = 15;
            this.btnPrevpage.Text = "<<";
            this.btnPrevpage.UseVisualStyleBackColor = true;
            this.btnPrevpage.Click += new System.EventHandler(this.btnPrevpage_Click);
            // 
            // label5
            // 
            this.label5.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(671, 573);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(19, 14);
            this.label5.TabIndex = 14;
            this.label5.Text = "页";
            // 
            // txtPage
            // 
            this.txtPage.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.txtPage.Location = new System.Drawing.Point(606, 570);
            this.txtPage.Name = "txtPage";
            this.txtPage.Size = new System.Drawing.Size(63, 22);
            this.txtPage.TabIndex = 13;
            // 
            // labPage
            // 
            this.labPage.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.labPage.AutoSize = true;
            this.labPage.Location = new System.Drawing.Point(506, 575);
            this.labPage.Name = "labPage";
            this.labPage.Size = new System.Drawing.Size(14, 14);
            this.labPage.TabIndex = 12;
            this.labPage.Text = "0";
            // 
            // label4
            // 
            this.label4.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(463, 574);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(43, 14);
            this.label4.TabIndex = 11;
            this.label4.Text = "个，共";
            // 
            // btnGo
            // 
            this.btnGo.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnGo.Location = new System.Drawing.Point(690, 570);
            this.btnGo.Name = "btnGo";
            this.btnGo.Size = new System.Drawing.Size(33, 23);
            this.btnGo.TabIndex = 9;
            this.btnGo.Text = "GO";
            this.btnGo.UseVisualStyleBackColor = true;
            this.btnGo.Click += new System.EventHandler(this.btnGo_Click);
            // 
            // labNum
            // 
            this.labNum.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.labNum.AutoSize = true;
            this.labNum.Location = new System.Drawing.Point(418, 575);
            this.labNum.Name = "labNum";
            this.labNum.Size = new System.Drawing.Size(14, 14);
            this.labNum.TabIndex = 8;
            this.labNum.Text = "0";
            // 
            // label3
            // 
            this.label3.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(540, 574);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(67, 14);
            this.label3.TabIndex = 7;
            this.label3.Text = "页，跳转至";
            // 
            // label2
            // 
            this.label2.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(342, 574);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(79, 14);
            this.label2.TabIndex = 6;
            this.label2.Text = "总计小区共：";
            // 
            // btnSearch
            // 
            this.btnSearch.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnSearch.Location = new System.Drawing.Point(1036, 570);
            this.btnSearch.Name = "btnSearch";
            this.btnSearch.Size = new System.Drawing.Size(42, 23);
            this.btnSearch.TabIndex = 5;
            this.btnSearch.Text = "查找";
            this.btnSearch.UseVisualStyleBackColor = true;
            this.btnSearch.Click += new System.EventHandler(this.btnSearch_Click);
            // 
            // txtCellName
            // 
            this.txtCellName.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.txtCellName.Location = new System.Drawing.Point(874, 571);
            this.txtCellName.Name = "txtCellName";
            this.txtCellName.Size = new System.Drawing.Size(157, 22);
            this.txtCellName.TabIndex = 4;
            // 
            // label1
            // 
            this.label1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(813, 575);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(67, 14);
            this.label1.TabIndex = 3;
            this.label1.Text = "小区名称：";
            // 
            // dataGridViewCell
            // 
            this.dataGridViewCell.AllowUserToAddRows = false;
            this.dataGridViewCell.AllowUserToDeleteRows = false;
            this.dataGridViewCell.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.dataGridViewCell.BackgroundColor = System.Drawing.SystemColors.Window;
            this.dataGridViewCell.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dataGridViewCell.ContextMenuStrip = this.menuItem;
            dataGridViewCellStyle1.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle1.BackColor = System.Drawing.SystemColors.Window;
            dataGridViewCellStyle1.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            dataGridViewCellStyle1.ForeColor = System.Drawing.Color.Black;
            dataGridViewCellStyle1.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle1.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle1.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dataGridViewCell.DefaultCellStyle = dataGridViewCellStyle1;
            this.dataGridViewCell.Location = new System.Drawing.Point(0, 0);
            this.dataGridViewCell.Name = "dataGridViewCell";
            this.dataGridViewCell.RowTemplate.Height = 23;
            this.dataGridViewCell.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dataGridViewCell.Size = new System.Drawing.Size(1210, 570);
            this.dataGridViewCell.TabIndex = 2;
            // 
            // menuItem
            // 
            this.menuItem.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miShwoChart,
            this.miShowSimulation,
            this.miShowChart,
            this.outPutCsv,
            this.outPutExcel});
            this.menuItem.Name = "menuItem";
            this.menuItem.Size = new System.Drawing.Size(197, 136);
            // 
            // miShwoChart
            // 
            this.miShwoChart.Name = "miShwoChart";
            this.miShwoChart.Size = new System.Drawing.Size(196, 22);
            this.miShwoChart.Text = "显示天线辐射波形重建";
            this.miShwoChart.Click += new System.EventHandler(this.miShwoChart_Click);
            // 
            // miShowSimulation
            // 
            this.miShowSimulation.Name = "miShowSimulation";
            this.miShowSimulation.Size = new System.Drawing.Size(196, 22);
            this.miShowSimulation.Text = "显示天线GIS覆盖仿真";
            this.miShowSimulation.Click += new System.EventHandler(this.miShowSimulation_Click);
            // 
            // miShowChart
            // 
            this.miShowChart.Name = "miShowChart";
            this.miShowChart.Size = new System.Drawing.Size(196, 22);
            this.miShowChart.Text = "显示统计图表";
            this.miShowChart.Click += new System.EventHandler(this.miShowChart_Click);
            // 
            // outPutCsv
            // 
            this.outPutCsv.Name = "outPutCsv";
            this.outPutCsv.Size = new System.Drawing.Size(196, 22);
            this.outPutCsv.Text = "拆分导出CSV";
            this.outPutCsv.Click += new System.EventHandler(this.outPutCsv_Click);
            // 
            // outPutExcel
            // 
            this.outPutExcel.Name = "outPutExcel";
            this.outPutExcel.Size = new System.Drawing.Size(196, 22);
            this.outPutExcel.Text = "导出Excel";
            this.outPutExcel.Click += new System.EventHandler(this.outPutExcel_Click);
            // 
            // pageAngle
            // 
            this.pageAngle.Controls.Add(this.groupControl1);
            this.pageAngle.Name = "pageAngle";
            this.pageAngle.Size = new System.Drawing.Size(1210, 592);
            this.pageAngle.Text = "天线角度级采样数据(路测与扫频)";
            // 
            // groupControl1
            // 
            this.groupControl1.Controls.Add(this.groupControl2);
            this.groupControl1.Controls.Add(this.groupBox2);
            this.groupControl1.Controls.Add(this.groupBox1);
            this.groupControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControl1.Location = new System.Drawing.Point(0, 0);
            this.groupControl1.Name = "groupControl1";
            this.groupControl1.Size = new System.Drawing.Size(1210, 592);
            this.groupControl1.TabIndex = 1;
            this.groupControl1.Text = "图表";
            // 
            // groupControl2
            // 
            this.groupControl2.Controls.Add(this.dataGridViewAngle);
            this.groupControl2.Location = new System.Drawing.Point(10, 386);
            this.groupControl2.Name = "groupControl2";
            this.groupControl2.Size = new System.Drawing.Size(1142, 218);
            this.groupControl2.TabIndex = 2;
            this.groupControl2.Text = "天线角度";
            // 
            // dataGridViewAngle
            // 
            this.dataGridViewAngle.AllowUserToAddRows = false;
            this.dataGridViewAngle.AllowUserToDeleteRows = false;
            this.dataGridViewAngle.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dataGridViewAngle.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.cluCellName,
            this.cluTarget});
            this.dataGridViewAngle.Location = new System.Drawing.Point(12, 32);
            this.dataGridViewAngle.Name = "dataGridViewAngle";
            this.dataGridViewAngle.RowTemplate.Height = 23;
            this.dataGridViewAngle.Size = new System.Drawing.Size(1121, 181);
            this.dataGridViewAngle.TabIndex = 0;
            // 
            // cluCellName
            // 
            this.cluCellName.HeaderText = "小区名称";
            this.cluCellName.Name = "cluCellName";
            this.cluCellName.Width = 150;
            // 
            // cluTarget
            // 
            this.cluTarget.HeaderText = "指标项";
            this.cluTarget.Name = "cluTarget";
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.panel1);
            this.groupBox2.Controls.Add(this.chartControl1);
            this.groupBox2.Location = new System.Drawing.Point(10, 75);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(1142, 295);
            this.groupBox2.TabIndex = 1;
            this.groupBox2.TabStop = false;
            // 
            // panel1
            // 
            this.panel1.Location = new System.Drawing.Point(1092, 21);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(29, 268);
            this.panel1.TabIndex = 3;
            // 
            // chartControl1
            // 
            xyDiagram1.AxisX.MinorCount = 1;
            xyDiagram1.AxisX.Range.Auto = false;
            xyDiagram1.AxisX.Range.MaxValueInternal = 3.4999999999999991D;
            xyDiagram1.AxisX.Range.MinValueInternal = -0.5D;
            xyDiagram1.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram1.AxisX.Range.SideMarginsEnabled = true;
            xyDiagram1.AxisX.Tickmarks.MinorLength = 1;
            xyDiagram1.AxisX.Title.Alignment = System.Drawing.StringAlignment.Near;
            xyDiagram1.AxisX.VisibleInPanesSerializable = "-1";
            xyDiagram1.AxisY.Color = System.Drawing.Color.DarkOrange;
            xyDiagram1.AxisY.Interlaced = true;
            xyDiagram1.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram1.AxisY.Range.SideMarginsEnabled = true;
            xyDiagram1.AxisY.Thickness = 2;
            xyDiagram1.AxisY.VisibleInPanesSerializable = "-1";
            xyDiagram1.DefaultPane.BackColor = System.Drawing.Color.Transparent;
            xyDiagram1.EnableAxisXScrolling = true;
            xyDiagram1.EnableAxisXZooming = true;
            xyDiagram1.EnableAxisYScrolling = true;
            xyDiagram1.Margins.Bottom = 1;
            xyDiagram1.Margins.Left = 1;
            xyDiagram1.Margins.Right = 1;
            xyDiagram1.Margins.Top = 1;
            xyDiagram1.PaneDistance = 5;
            secondaryAxisY1.AxisID = 0;
            secondaryAxisY1.Color = System.Drawing.Color.Blue;
            secondaryAxisY1.GridLines.Color = System.Drawing.Color.White;
            secondaryAxisY1.Interlaced = true;
            secondaryAxisY1.Name = "Secondary AxisY 1";
            secondaryAxisY1.Range.Auto = false;
            secondaryAxisY1.Range.MaxValueSerializable = "1";
            secondaryAxisY1.Range.MinValueSerializable = "0";
            secondaryAxisY1.Range.ScrollingRange.SideMarginsEnabled = true;
            secondaryAxisY1.Range.SideMarginsEnabled = true;
            secondaryAxisY1.Thickness = 2;
            secondaryAxisY1.VisibleInPanesSerializable = "-1";
            secondaryAxisY2.AxisID = 1;
            secondaryAxisY2.Name = "Secondary AxisY 2";
            secondaryAxisY2.Range.ScrollingRange.SideMarginsEnabled = true;
            secondaryAxisY2.Range.SideMarginsEnabled = true;
            secondaryAxisY2.VisibleInPanesSerializable = "-1";
            xyDiagram1.SecondaryAxesY.AddRange(new DevExpress.XtraCharts.SecondaryAxisY[] {
            secondaryAxisY1,
            secondaryAxisY2});
            this.chartControl1.Diagram = xyDiagram1;
            this.chartControl1.Location = new System.Drawing.Point(9, 21);
            this.chartControl1.Name = "chartControl1";
            this.chartControl1.RefreshDataOnRepaint = false;
            this.chartControl1.RuntimeHitTesting = false;
            sideBySideBarSeriesLabel1.LineVisible = false;
            sideBySideBarSeriesLabel1.Visible = false;
            series1.Label = sideBySideBarSeriesLabel1;
            series1.Name = "Series 3";
            series1.ShowInLegend = false;
            sideBySideBarSeriesView1.AxisYName = "Secondary AxisY 2";
            sideBySideBarSeriesView1.BarWidth = 1D;
            sideBySideBarSeriesView1.Color = System.Drawing.Color.Red;
            series1.View = sideBySideBarSeriesView1;
            this.chartControl1.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series1};
            sideBySideBarSeriesLabel2.LineVisible = true;
            this.chartControl1.SeriesTemplate.Label = sideBySideBarSeriesLabel2;
            this.chartControl1.SideBySideBarDistanceVariable = 0.01D;
            this.chartControl1.Size = new System.Drawing.Size(1112, 268);
            this.chartControl1.TabIndex = 2;
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.cbbxSeries2);
            this.groupBox1.Controls.Add(this.cbbxSeries1);
            this.groupBox1.Controls.Add(this.labelControl2);
            this.groupBox1.Controls.Add(this.labelControl1);
            this.groupBox1.Location = new System.Drawing.Point(10, 29);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(1142, 50);
            this.groupBox1.TabIndex = 0;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "图例";
            // 
            // cbbxSeries2
            // 
            this.cbbxSeries2.FormattingEnabled = true;
            this.cbbxSeries2.Location = new System.Drawing.Point(404, 18);
            this.cbbxSeries2.Name = "cbbxSeries2";
            this.cbbxSeries2.Size = new System.Drawing.Size(155, 22);
            this.cbbxSeries2.TabIndex = 3;
            this.cbbxSeries2.SelectedIndexChanged += new System.EventHandler(this.cbbxSeries2_SelectedIndexChanged);
            // 
            // cbbxSeries1
            // 
            this.cbbxSeries1.FormattingEnabled = true;
            this.cbbxSeries1.Location = new System.Drawing.Point(133, 18);
            this.cbbxSeries1.Name = "cbbxSeries1";
            this.cbbxSeries1.Size = new System.Drawing.Size(175, 22);
            this.cbbxSeries1.TabIndex = 2;
            this.cbbxSeries1.SelectedIndexChanged += new System.EventHandler(this.cbbxSeries1_SelectedIndexChanged);
            // 
            // labelControl2
            // 
            this.labelControl2.Location = new System.Drawing.Point(314, 21);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(84, 14);
            this.labelControl2.TabIndex = 1;
            this.labelControl2.Text = "路测折线图指标";
            // 
            // labelControl1
            // 
            this.labelControl1.Location = new System.Drawing.Point(43, 21);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(84, 14);
            this.labelControl1.TabIndex = 0;
            this.labelControl1.Text = "扫频柱形图指标";
            // 
            // pageBar
            // 
            this.pageBar.Controls.Add(this.groupControl3);
            this.pageBar.Name = "pageBar";
            this.pageBar.Size = new System.Drawing.Size(1210, 592);
            this.pageBar.Text = "天线辐射波形重构";
            // 
            // groupControl3
            // 
            this.groupControl3.Controls.Add(this.groupBox5);
            this.groupControl3.Controls.Add(this.groupBox3);
            this.groupControl3.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControl3.Location = new System.Drawing.Point(0, 0);
            this.groupControl3.Name = "groupControl3";
            this.groupControl3.Size = new System.Drawing.Size(1210, 592);
            this.groupControl3.TabIndex = 1;
            this.groupControl3.Text = "全向分析";
            // 
            // groupBox5
            // 
            this.groupBox5.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left)));
            this.groupBox5.Controls.Add(this.chartControlScan);
            this.groupBox5.Location = new System.Drawing.Point(599, 26);
            this.groupBox5.Name = "groupBox5";
            this.groupBox5.Size = new System.Drawing.Size(583, 559);
            this.groupBox5.TabIndex = 2;
            this.groupBox5.TabStop = false;
            this.groupBox5.Text = "路测俯视面辐射图";
            // 
            // chartControlScan
            // 
            this.chartControlScan.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left)));
            this.chartControlScan.BorderOptions.Color = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(255)))));
            radarDiagram1.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            radarDiagram1.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            radarDiagram1.AxisY.Range.SideMarginsEnabled = true;
            radarDiagram1.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(192)))), ((int)(((byte)(255)))), ((int)(((byte)(192)))));
            this.chartControlScan.Diagram = radarDiagram1;
            this.chartControlScan.Location = new System.Drawing.Point(6, 21);
            this.chartControlScan.Name = "chartControlScan";
            radarPointSeriesLabel1.LineVisible = true;
            series2.Label = radarPointSeriesLabel1;
            series2.Name = "AntSeries";
            series2.Points.AddRange(new DevExpress.XtraCharts.SeriesPoint[] {
            seriesPoint1,
            seriesPoint2,
            seriesPoint3,
            seriesPoint4,
            seriesPoint5});
            series2.ShowInLegend = false;
            series2.View = radarLineSeriesView1;
            radarPointSeriesLabel2.LineVisible = true;
            series3.Label = radarPointSeriesLabel2;
            series3.Name = "StandardSeries";
            series3.ShowInLegend = false;
            series3.View = radarLineSeriesView2;
            this.chartControlScan.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series2,
        series3};
            radarPointSeriesLabel3.LineVisible = true;
            this.chartControlScan.SeriesTemplate.Label = radarPointSeriesLabel3;
            this.chartControlScan.SeriesTemplate.View = radarLineSeriesView3;
            this.chartControlScan.Size = new System.Drawing.Size(571, 524);
            this.chartControlScan.TabIndex = 1;
            // 
            // groupBox3
            // 
            this.groupBox3.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left)));
            this.groupBox3.Controls.Add(this.chartControlDt);
            this.groupBox3.Location = new System.Drawing.Point(10, 26);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.Size = new System.Drawing.Size(583, 559);
            this.groupBox3.TabIndex = 0;
            this.groupBox3.TabStop = false;
            this.groupBox3.Text = "路测俯视面辐射图";
            // 
            // chartControlDt
            // 
            this.chartControlDt.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left)));
            this.chartControlDt.BorderOptions.Color = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(255)))));
            radarDiagram2.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            radarDiagram2.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            radarDiagram2.AxisY.Range.SideMarginsEnabled = true;
            radarDiagram2.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(192)))), ((int)(((byte)(255)))), ((int)(((byte)(192)))));
            this.chartControlDt.Diagram = radarDiagram2;
            this.chartControlDt.Location = new System.Drawing.Point(6, 21);
            this.chartControlDt.Name = "chartControlDt";
            radarPointSeriesLabel4.LineVisible = true;
            series4.Label = radarPointSeriesLabel4;
            series4.Name = "AntSeries";
            series4.Points.AddRange(new DevExpress.XtraCharts.SeriesPoint[] {
            seriesPoint6,
            seriesPoint7,
            seriesPoint8,
            seriesPoint9,
            seriesPoint10});
            series4.ShowInLegend = false;
            series4.View = radarLineSeriesView4;
            radarPointSeriesLabel5.LineVisible = true;
            series5.Label = radarPointSeriesLabel5;
            series5.Name = "StandardSeries";
            series5.ShowInLegend = false;
            series5.View = radarLineSeriesView5;
            this.chartControlDt.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series4,
        series5};
            radarPointSeriesLabel6.LineVisible = true;
            this.chartControlDt.SeriesTemplate.Label = radarPointSeriesLabel6;
            this.chartControlDt.SeriesTemplate.View = radarLineSeriesView6;
            this.chartControlDt.Size = new System.Drawing.Size(571, 532);
            this.chartControlDt.TabIndex = 0;
            // 
            // PageBarMr
            // 
            this.PageBarMr.Controls.Add(this.groupControl5);
            this.PageBarMr.Name = "PageBarMr";
            this.PageBarMr.Size = new System.Drawing.Size(1210, 592);
            this.PageBarMr.Text = "MR数据二维图";
            // 
            // groupControl5
            // 
            this.groupControl5.Controls.Add(this.cbPart);
            this.groupControl5.Controls.Add(this.label7);
            this.groupControl5.Controls.Add(this.groupBox4);
            this.groupControl5.Controls.Add(this.groupBox15);
            this.groupControl5.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControl5.Location = new System.Drawing.Point(0, 0);
            this.groupControl5.Name = "groupControl5";
            this.groupControl5.Size = new System.Drawing.Size(1210, 592);
            this.groupControl5.TabIndex = 3;
            this.groupControl5.Text = "MR全向分析";
            // 
            // cbPart
            // 
            this.cbPart.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cbPart.FormattingEnabled = true;
            this.cbPart.Location = new System.Drawing.Point(76, 24);
            this.cbPart.Name = "cbPart";
            this.cbPart.Size = new System.Drawing.Size(281, 22);
            this.cbPart.TabIndex = 5;
            this.cbPart.SelectedIndexChanged += new System.EventHandler(this.cbPart_SelectedIndexChanged);
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(11, 29);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(67, 14);
            this.label7.TabIndex = 4;
            this.label7.Text = "选择区间：";
            // 
            // groupBox4
            // 
            this.groupBox4.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left)));
            this.groupBox4.Controls.Add(this.chartCellLine);
            this.groupBox4.Location = new System.Drawing.Point(599, 47);
            this.groupBox4.Name = "groupBox4";
            this.groupBox4.Size = new System.Drawing.Size(583, 538);
            this.groupBox4.TabIndex = 1;
            this.groupBox4.TabStop = false;
            this.groupBox4.Text = "MR覆盖模拟图";
            // 
            // chartCellLine
            // 
            this.chartCellLine.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left)));
            this.chartCellLine.BorderOptions.Color = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(255)))));
            radarDiagram3.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            radarDiagram3.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            radarDiagram3.AxisY.Range.SideMarginsEnabled = true;
            radarDiagram3.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(192)))), ((int)(((byte)(255)))), ((int)(((byte)(192)))));
            this.chartCellLine.Diagram = radarDiagram3;
            this.chartCellLine.Location = new System.Drawing.Point(6, 21);
            this.chartCellLine.Name = "chartCellLine";
            radarPointSeriesLabel7.LineVisible = true;
            series6.Label = radarPointSeriesLabel7;
            series6.Name = "AntSeries";
            series6.Points.AddRange(new DevExpress.XtraCharts.SeriesPoint[] {
            seriesPoint11,
            seriesPoint12,
            seriesPoint13,
            seriesPoint14,
            seriesPoint15});
            series6.ShowInLegend = false;
            radarPointSeriesView1.PointMarkerOptions.Size = 10;
            series6.View = radarPointSeriesView1;
            radarPointSeriesLabel8.LineVisible = true;
            series7.Label = radarPointSeriesLabel8;
            series7.Name = "StandardSeries";
            series7.ShowInLegend = false;
            radarPointSeriesView2.PointMarkerOptions.Size = 10;
            series7.View = radarPointSeriesView2;
            this.chartCellLine.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series6,
        series7};
            radarPointSeriesLabel9.LineVisible = true;
            this.chartCellLine.SeriesTemplate.Label = radarPointSeriesLabel9;
            radarPointSeriesView3.PointMarkerOptions.Size = 10;
            this.chartCellLine.SeriesTemplate.View = radarPointSeriesView3;
            this.chartCellLine.Size = new System.Drawing.Size(565, 511);
            this.chartCellLine.TabIndex = 0;
            // 
            // groupBox15
            // 
            this.groupBox15.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left)));
            this.groupBox15.Controls.Add(this.chartCellPoint);
            this.groupBox15.Location = new System.Drawing.Point(10, 47);
            this.groupBox15.Name = "groupBox15";
            this.groupBox15.Size = new System.Drawing.Size(583, 538);
            this.groupBox15.TabIndex = 0;
            this.groupBox15.TabStop = false;
            this.groupBox15.Text = "MR采样点分布图";
            // 
            // chartCellPoint
            // 
            this.chartCellPoint.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left)));
            this.chartCellPoint.BorderOptions.Color = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(255)))));
            radarDiagram4.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            radarDiagram4.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            radarDiagram4.AxisY.Range.SideMarginsEnabled = true;
            radarDiagram4.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(192)))), ((int)(((byte)(255)))), ((int)(((byte)(192)))));
            this.chartCellPoint.Diagram = radarDiagram4;
            this.chartCellPoint.Location = new System.Drawing.Point(6, 21);
            this.chartCellPoint.Name = "chartCellPoint";
            radarPointSeriesLabel10.LineVisible = true;
            series8.Label = radarPointSeriesLabel10;
            series8.Name = "AntSeries";
            series8.Points.AddRange(new DevExpress.XtraCharts.SeriesPoint[] {
            seriesPoint16,
            seriesPoint17,
            seriesPoint18,
            seriesPoint19,
            seriesPoint20});
            series8.ShowInLegend = false;
            radarPointSeriesView4.PointMarkerOptions.Size = 10;
            series8.View = radarPointSeriesView4;
            radarPointSeriesLabel11.LineVisible = true;
            series9.Label = radarPointSeriesLabel11;
            series9.Name = "StandardSeries";
            series9.ShowInLegend = false;
            radarPointSeriesView5.PointMarkerOptions.Size = 10;
            series9.View = radarPointSeriesView5;
            this.chartCellPoint.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series8,
        series9};
            radarPointSeriesLabel12.LineVisible = true;
            this.chartCellPoint.SeriesTemplate.Label = radarPointSeriesLabel12;
            radarPointSeriesView6.PointMarkerOptions.Size = 10;
            this.chartCellPoint.SeriesTemplate.View = radarPointSeriesView6;
            this.chartCellPoint.Size = new System.Drawing.Size(571, 511);
            this.chartCellPoint.TabIndex = 0;
            // 
            // pageMR
            // 
            this.pageMR.Controls.Add(this.groupControl4);
            this.pageMR.Name = "pageMR";
            this.pageMR.Size = new System.Drawing.Size(1210, 592);
            this.pageMR.Text = "MR数据统计表";
            // 
            // groupControl4
            // 
            this.groupControl4.Controls.Add(this.groupBox12);
            this.groupControl4.Controls.Add(this.groupBox8);
            this.groupControl4.Controls.Add(this.groupBox11);
            this.groupControl4.Controls.Add(this.groupBox10);
            this.groupControl4.Controls.Add(this.groupBox7);
            this.groupControl4.Controls.Add(this.groupBox9);
            this.groupControl4.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControl4.Location = new System.Drawing.Point(0, 0);
            this.groupControl4.Name = "groupControl4";
            this.groupControl4.Size = new System.Drawing.Size(1210, 592);
            this.groupControl4.TabIndex = 3;
            this.groupControl4.Text = "MR测量项数据图表";
            // 
            // groupBox12
            // 
            this.groupBox12.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left)));
            this.groupBox12.Controls.Add(this.rtbDesc);
            this.groupBox12.Location = new System.Drawing.Point(592, 414);
            this.groupBox12.Name = "groupBox12";
            this.groupBox12.Size = new System.Drawing.Size(576, 160);
            this.groupBox12.TabIndex = 4;
            this.groupBox12.TabStop = false;
            this.groupBox12.Text = "MR测量项说明";
            // 
            // rtbDesc
            // 
            this.rtbDesc.Location = new System.Drawing.Point(6, 18);
            this.rtbDesc.Name = "rtbDesc";
            this.rtbDesc.ReadOnly = true;
            this.rtbDesc.Size = new System.Drawing.Size(564, 161);
            this.rtbDesc.TabIndex = 1;
            this.rtbDesc.Text = "";
            // 
            // groupBox8
            // 
            this.groupBox8.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left)));
            this.groupBox8.Controls.Add(this.chartControlAoa);
            this.groupBox8.Location = new System.Drawing.Point(592, 220);
            this.groupBox8.Name = "groupBox8";
            this.groupBox8.Size = new System.Drawing.Size(576, 160);
            this.groupBox8.TabIndex = 4;
            this.groupBox8.TabStop = false;
            this.groupBox8.Text = "天线到达角（度）";
            // 
            // chartControlAoa
            // 
            xyDiagram2.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram2.AxisX.Range.SideMarginsEnabled = true;
            xyDiagram2.AxisX.VisibleInPanesSerializable = "-1";
            xyDiagram2.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram2.AxisY.Range.SideMarginsEnabled = true;
            xyDiagram2.AxisY.VisibleInPanesSerializable = "-1";
            this.chartControlAoa.Diagram = xyDiagram2;
            this.chartControlAoa.Legend.Visible = false;
            this.chartControlAoa.Location = new System.Drawing.Point(6, 21);
            this.chartControlAoa.Name = "chartControlAoa";
            sideBySideBarSeriesLabel3.LineVisible = true;
            series10.Label = sideBySideBarSeriesLabel3;
            series10.Name = "Series 1";
            this.chartControlAoa.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series10};
            sideBySideBarSeriesLabel4.LineVisible = true;
            this.chartControlAoa.SeriesTemplate.Label = sideBySideBarSeriesLabel4;
            this.chartControlAoa.Size = new System.Drawing.Size(564, 161);
            this.chartControlAoa.TabIndex = 1;
            // 
            // groupBox11
            // 
            this.groupBox11.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left)));
            this.groupBox11.Controls.Add(this.chartControlSinr);
            this.groupBox11.Location = new System.Drawing.Point(10, 414);
            this.groupBox11.Name = "groupBox11";
            this.groupBox11.Size = new System.Drawing.Size(576, 160);
            this.groupBox11.TabIndex = 1;
            this.groupBox11.TabStop = false;
            this.groupBox11.Text = "上行信噪比（dB）";
            // 
            // chartControlSinr
            // 
            xyDiagram3.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram3.AxisX.Range.SideMarginsEnabled = true;
            xyDiagram3.AxisX.VisibleInPanesSerializable = "-1";
            xyDiagram3.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram3.AxisY.Range.SideMarginsEnabled = true;
            xyDiagram3.AxisY.VisibleInPanesSerializable = "-1";
            this.chartControlSinr.Diagram = xyDiagram3;
            this.chartControlSinr.Legend.Visible = false;
            this.chartControlSinr.Location = new System.Drawing.Point(6, 21);
            this.chartControlSinr.Name = "chartControlSinr";
            sideBySideBarSeriesLabel5.LineVisible = true;
            series11.Label = sideBySideBarSeriesLabel5;
            series11.Name = "Series 1";
            this.chartControlSinr.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series11};
            sideBySideBarSeriesLabel6.LineVisible = true;
            this.chartControlSinr.SeriesTemplate.Label = sideBySideBarSeriesLabel6;
            this.chartControlSinr.Size = new System.Drawing.Size(564, 161);
            this.chartControlSinr.TabIndex = 2;
            // 
            // groupBox10
            // 
            this.groupBox10.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left)));
            this.groupBox10.Controls.Add(this.chartControlRsrp);
            this.groupBox10.Location = new System.Drawing.Point(10, 220);
            this.groupBox10.Name = "groupBox10";
            this.groupBox10.Size = new System.Drawing.Size(576, 160);
            this.groupBox10.TabIndex = 3;
            this.groupBox10.TabStop = false;
            this.groupBox10.Text = "接收信号码功率（dBm）";
            // 
            // chartControlRsrp
            // 
            xyDiagram4.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram4.AxisX.Range.SideMarginsEnabled = true;
            xyDiagram4.AxisX.VisibleInPanesSerializable = "-1";
            xyDiagram4.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram4.AxisY.Range.SideMarginsEnabled = true;
            xyDiagram4.AxisY.VisibleInPanesSerializable = "-1";
            this.chartControlRsrp.Diagram = xyDiagram4;
            this.chartControlRsrp.Legend.Visible = false;
            this.chartControlRsrp.Location = new System.Drawing.Point(6, 21);
            this.chartControlRsrp.Name = "chartControlRsrp";
            sideBySideBarSeriesLabel7.LineVisible = true;
            series12.Label = sideBySideBarSeriesLabel7;
            series12.Name = "Series 1";
            this.chartControlRsrp.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series12};
            sideBySideBarSeriesLabel8.LineVisible = true;
            this.chartControlRsrp.SeriesTemplate.Label = sideBySideBarSeriesLabel8;
            this.chartControlRsrp.Size = new System.Drawing.Size(564, 161);
            this.chartControlRsrp.TabIndex = 1;
            // 
            // groupBox7
            // 
            this.groupBox7.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBox7.Controls.Add(this.chartControlPower);
            this.groupBox7.Location = new System.Drawing.Point(592, 26);
            this.groupBox7.Name = "groupBox7";
            this.groupBox7.Size = new System.Drawing.Size(609, 188);
            this.groupBox7.TabIndex = 2;
            this.groupBox7.TabStop = false;
            this.groupBox7.Text = "UE发射功率（dB）";
            // 
            // chartControlPower
            // 
            xyDiagram5.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram5.AxisX.Range.SideMarginsEnabled = true;
            xyDiagram5.AxisX.VisibleInPanesSerializable = "-1";
            xyDiagram5.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram5.AxisY.Range.SideMarginsEnabled = true;
            xyDiagram5.AxisY.VisibleInPanesSerializable = "-1";
            this.chartControlPower.Diagram = xyDiagram5;
            this.chartControlPower.Legend.Visible = false;
            this.chartControlPower.Location = new System.Drawing.Point(6, 21);
            this.chartControlPower.Name = "chartControlPower";
            sideBySideBarSeriesLabel9.LineVisible = true;
            series13.Label = sideBySideBarSeriesLabel9;
            series13.Name = "Series 1";
            this.chartControlPower.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series13};
            sideBySideBarSeriesLabel10.LineVisible = true;
            this.chartControlPower.SeriesTemplate.Label = sideBySideBarSeriesLabel10;
            this.chartControlPower.Size = new System.Drawing.Size(564, 161);
            this.chartControlPower.TabIndex = 1;
            // 
            // groupBox9
            // 
            this.groupBox9.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left)));
            this.groupBox9.Controls.Add(this.chartControlTA);
            this.groupBox9.Location = new System.Drawing.Point(10, 26);
            this.groupBox9.Name = "groupBox9";
            this.groupBox9.Size = new System.Drawing.Size(576, 160);
            this.groupBox9.TabIndex = 0;
            this.groupBox9.TabStop = false;
            this.groupBox9.Text = "时间提前量（米）";
            // 
            // chartControlTA
            // 
            xyDiagram6.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram6.AxisX.Range.SideMarginsEnabled = true;
            xyDiagram6.AxisX.VisibleInPanesSerializable = "-1";
            xyDiagram6.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram6.AxisY.Range.SideMarginsEnabled = true;
            xyDiagram6.AxisY.VisibleInPanesSerializable = "-1";
            this.chartControlTA.Diagram = xyDiagram6;
            this.chartControlTA.Legend.Visible = false;
            this.chartControlTA.Location = new System.Drawing.Point(6, 21);
            this.chartControlTA.Name = "chartControlTA";
            sideBySideBarSeriesLabel11.LineVisible = true;
            series14.Label = sideBySideBarSeriesLabel11;
            series14.Name = "Series 1";
            this.chartControlTA.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series14};
            sideBySideBarSeriesLabel12.LineVisible = true;
            this.chartControlTA.SeriesTemplate.Label = sideBySideBarSeriesLabel12;
            this.chartControlTA.Size = new System.Drawing.Size(564, 161);
            this.chartControlTA.TabIndex = 2;
            // 
            // TdAntParaCommonForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1217, 622);
            this.Controls.Add(this.xTabGSMAnt);
            this.MaximizeBox = false;
            this.Name = "TdAntParaCommonForm";
            this.Text = "天线综合分析总表";
            ((System.ComponentModel.ISupportInitialize)(this.xTabGSMAnt)).EndInit();
            this.xTabGSMAnt.ResumeLayout(false);
            this.pageCell.ResumeLayout(false);
            this.pageCell.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewCell)).EndInit();
            this.menuItem.ResumeLayout(false);
            this.pageAngle.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).EndInit();
            this.groupControl2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewAngle)).EndInit();
            this.groupBox2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(secondaryAxisY1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(secondaryAxisY2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControl1)).EndInit();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.pageBar.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl3)).EndInit();
            this.groupControl3.ResumeLayout(false);
            this.groupBox5.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(radarDiagram1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarLineSeriesView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarLineSeriesView2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarLineSeriesView3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlScan)).EndInit();
            this.groupBox3.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(radarDiagram2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarLineSeriesView4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarLineSeriesView5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel6)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarLineSeriesView6)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlDt)).EndInit();
            this.PageBarMr.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl5)).EndInit();
            this.groupControl5.ResumeLayout(false);
            this.groupControl5.PerformLayout();
            this.groupBox4.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(radarDiagram3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel7)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series6)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel8)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesView2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series7)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel9)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesView3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartCellLine)).EndInit();
            this.groupBox15.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(radarDiagram4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel10)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesView4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series8)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel11)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesView5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series9)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel12)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesView6)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartCellPoint)).EndInit();
            this.pageMR.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl4)).EndInit();
            this.groupControl4.ResumeLayout(false);
            this.groupBox12.ResumeLayout(false);
            this.groupBox8.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(xyDiagram2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series10)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlAoa)).EndInit();
            this.groupBox11.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(xyDiagram3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series11)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel6)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlSinr)).EndInit();
            this.groupBox10.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(xyDiagram4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel7)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series12)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel8)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlRsrp)).EndInit();
            this.groupBox7.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(xyDiagram5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel9)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series13)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel10)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlPower)).EndInit();
            this.groupBox9.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(xyDiagram6)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel11)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series14)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel12)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlTA)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraTab.XtraTabControl xTabGSMAnt;
        private DevExpress.XtraTab.XtraTabPage pageCell;
        private System.Windows.Forms.Button btnNextpage;
        private System.Windows.Forms.Button btnPrevpage;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.TextBox txtPage;
        private System.Windows.Forms.Label labPage;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.Button btnGo;
        private System.Windows.Forms.Label labNum;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Button btnSearch;
        private System.Windows.Forms.TextBox txtCellName;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.DataGridView dataGridViewCell;
        private DevExpress.XtraTab.XtraTabPage pageAngle;
        private DevExpress.XtraEditors.GroupControl groupControl1;
        private DevExpress.XtraEditors.GroupControl groupControl2;
        private System.Windows.Forms.DataGridView dataGridViewAngle;
        private System.Windows.Forms.DataGridViewTextBoxColumn cluCellName;
        private System.Windows.Forms.DataGridViewTextBoxColumn cluTarget;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.Panel panel1;
        private DevExpress.XtraCharts.ChartControl chartControl1;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.ComboBox cbbxSeries2;
        private System.Windows.Forms.ComboBox cbbxSeries1;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraTab.XtraTabPage pageBar;
        private DevExpress.XtraEditors.GroupControl groupControl3;
        private System.Windows.Forms.GroupBox groupBox5;
        private System.Windows.Forms.GroupBox groupBox3;
        private DevExpress.XtraCharts.ChartControl chartControlDt;
        private System.Windows.Forms.ContextMenuStrip menuItem;
        private System.Windows.Forms.ToolStripMenuItem miShwoChart;
        private System.Windows.Forms.ToolStripMenuItem outPutCsv;
        private System.Windows.Forms.ToolStripMenuItem outPutExcel;
        private DevExpress.XtraTab.XtraTabPage pageMR;
        private DevExpress.XtraEditors.GroupControl groupControl4;
        private System.Windows.Forms.GroupBox groupBox12;
        private System.Windows.Forms.RichTextBox rtbDesc;
        private System.Windows.Forms.GroupBox groupBox8;
        private DevExpress.XtraCharts.ChartControl chartControlAoa;
        private System.Windows.Forms.GroupBox groupBox11;
        private DevExpress.XtraCharts.ChartControl chartControlSinr;
        private System.Windows.Forms.GroupBox groupBox10;
        private DevExpress.XtraCharts.ChartControl chartControlRsrp;
        private System.Windows.Forms.GroupBox groupBox7;
        private DevExpress.XtraCharts.ChartControl chartControlPower;
        private System.Windows.Forms.GroupBox groupBox9;
        private DevExpress.XtraCharts.ChartControl chartControlTA;
        private System.Windows.Forms.ToolStripMenuItem miShowChart;
        private DevExpress.XtraTab.XtraTabPage PageBarMr;
        private DevExpress.XtraEditors.GroupControl groupControl5;
        private System.Windows.Forms.ComboBox cbPart;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.GroupBox groupBox4;
        private DevExpress.XtraCharts.ChartControl chartCellLine;
        private System.Windows.Forms.GroupBox groupBox15;
        private DevExpress.XtraCharts.ChartControl chartCellPoint;
        private DevExpress.XtraCharts.ChartControl chartControlScan;
        private System.Windows.Forms.ToolStripMenuItem miShowSimulation;
    }
}