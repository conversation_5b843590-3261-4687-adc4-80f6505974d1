﻿using System;
using System.Collections.Generic;
using System.Text;
using AxMapWinGIS;
using MasterCom.MTGis;
using MapWinGIS;
using GMap.NET;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using System.Drawing;

namespace MasterCom.RAMS.MapControlTool
{
    class MapControlToolPolygonFlyLine
    {
        private MapForm mapForm { get; set; }
        private AxMap map { get; set; }
        private readonly MapControlToolAddPolygon polygonTool;

        public event EventHandler PolygonCreated;

        public List<DbPoint> Points
        {
            get { return polygonTool.Points; }
        }

        public MapControlToolPolygonFlyLine(MapForm mapForm, AxMap map)
        {
            this.mapForm = mapForm;
            this.map = map;
            this.polygonTool = new MapControlToolAddPolygon(mapForm, map);
            this.polygonTool.PolygonCreated += FireDrawFinished;
            this.polygonTool.Style.PolygonPen = new Pen(Color.Blue, 2);
            this.polygonTool.Style.PolygonBackBrush = null;
        }

        public void Clear()
        {
            polygonTool.ClearPolygon();
        }

        public void Activate()
        {
            polygonTool.Activate();
        }

        public void Deactivate()
        {
            polygonTool.Deactivate();
        }

        public void Draw(Graphics g)
        {
            polygonTool.Draw(g);
        }

        private void FireDrawFinished(object sender, EventArgs e)
        {
            PolygonCreated(this, EventArgs.Empty);
        }
    }
}
