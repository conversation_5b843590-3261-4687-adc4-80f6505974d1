﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc.VoLTEAbnormalEvt;

namespace MasterCom.RAMS.ZTFunc
{
    public class VoLTEAbnormalEvtQuery : DIYEventByRegion
    {
        public VoLTEAbnormalEvtQuery()
            : base(MainModel.GetInstance())
        {
            showForm = false;
        }

        public override string Name
        {
            get { return "VoLTE异常事件查找"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 27000, 27009, this.Name);
        }

        protected override void fireShowFormAfterQuery()
        {
            AbnormalEvtListForm frm = MainModel.CreateResultForm(typeof(AbnormalEvtListForm)) as AbnormalEvtListForm;
            frm.FillData();
            frm.Visible = true;
            frm.BringToFront();
        }

        protected override bool prepareAskWhatEvent()
        {
            List<int> selectedEventIDs = new List<int>();
            selectedEventIDs.Add(1080);
            selectedEventIDs.Add(1078);
            selectedEventIDs.Add(1079);
            condition.EventIDs = selectedEventIDs;
            condition.FilterOffValue9 = true;
            return true;
        }
    }

    public class VoLTEAbnormalEvtQuery_FDD : VoLTEAbnormalEvtQuery
    {
        public VoLTEAbnormalEvtQuery_FDD()
            : base()
        {
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_FDD_VOLTE);
            ServiceTypes.Add(ServiceType.SER_LTE_FDD_VIDEO_VOLTE);
            carrierID = CarrierType.ChinaUnicom;
        }
        public override string Name
        {
            get { return "VoLTE_FDD异常事件查找"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 30000, 30032, this.Name);
        }
        protected override bool prepareAskWhatEvent()
        {
            List<int> selectedEventIDs = new List<int>();
            selectedEventIDs.Add(3608);
            selectedEventIDs.Add(3613);
            selectedEventIDs.Add(3620);
            condition.EventIDs = selectedEventIDs;
            condition.FilterOffValue9 = true;
            return true;
        }
    }
}
