﻿using MasterCom.RAMS.Func;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NRPilotFrequencyPolluteDlg : BaseDialog
    {
        public NRPilotFrequencyPolluteDlg()
        {
            InitializeComponent();
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        public int RxlevMin
        {
            get { return (int)numRxlevMin.Value; }
        }

        public int RxlevMax
        {
            get { return (int)numRxLevMax.Value; }
        }

        public int Radius
        {
            get { return (int)numRadius.Value; }
        }

        public int SampleCountLimit
        {
            get { return (int)numSampleCountLimit.Value; }
        }

        public int CellCountThreshold
        {
            get { return (int)numCellCountLimit.Value; }
        }

        public int RxLevDValueThreshold
        {
            get { return (int)numRxLevDValue.Value; }
        }

        public bool CoFreqOnly
        {
            get { return chkCoOnly.Checked; }
        }
    }
}
