﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTCellCheck
{
    public class FuncConditionNR
    {
        public UltraSiteCondition UltraSiteCondition { get; set; } = new UltraSiteCondition();
        public float CoverlapRSRPMin { get; set; } = -90;
        public double CvrDisFactorMax { get; set; } = 1.6f;
        public int CoverSiteNum { get; set; } = 3;
        public float MultiCoverRSRP { get; set; }
        public float MultiCoverDiff { get; set; }

        public float Mod3Diff { get; set; }
        public float Mod3RSRP { get; set; }
        public float Mod4Diff { get; set; }
        public float Mod4RSRP { get; set; }
        public float Mod6Diff { get; set; }
        public float Mod6RSRP { get; set; }

        public float WeakCoverRSRP { get; set; }

        internal bool IsWeakCover(float rsrp)
        {
            return rsrp <= WeakCoverRSRP;
        }

        internal bool IsCoverLap(float rsrp, double distance, double cellMaxDis)
        {
            return rsrp >= CoverlapRSRPMin && distance > cellMaxDis;
        }
    }
}
