﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.KPI_Statistics;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using Mobius.Utility;
using MasterCom.Util;
using System.Windows.Forms;

namespace MasterCom.RAMS.Stat
{
    public class QueryAreaStat : ReportStatQueryBase
    {
        private NoGisStatCond noGisCond = null;
        public Dictionary<string, AreaStatInfo> AreaKeyDataDic { get; set; }
        StringBuilder curStrb = new StringBuilder();
        readonly bool isMergeData;
        protected override Model.Interface.StatTbToken getTableNameToken()
        {
            return Model.Interface.StatTbToken.area;
        }

        public Dictionary<int, List<OrderCheckFileItem>> CheckFileIDDic { get; set; }

        public QueryAreaStat(ReporterTemplate template, bool isMergeData, Dictionary<string, AreaStatInfo> areaKeyDataDic)
            : base()
        {
            this.rptTemplate = template;
            this.isMergeData = isMergeData;
            this.AreaKeyDataDic = areaKeyDataDic;
            this.IsShowResultForm = false;
        }
        public void SetQueryCondition(NoGisStatCond cond)
        {
            noGisCond = cond;
            this.condition = cond.queryCondition;
        }
        protected override void AddExtraCondition(Package package, params object[] reservedParams)
        {
            AddDIYEndOpFlag(package);
            package.Content.AddParam((byte)OpOptionDef.InSelect);
            package.Content.AddParam("0,25,1");
            package.Content.AddParam("" + noGisCond.areaType);
            package.Content.AddParam((byte)OpOptionDef.InSelect);
            package.Content.AddParam("0,26,1");
            package.Content.AddParam(curStrb.ToString());
        }

        protected override void AddGeographicFilter(Package package)
        {
            // Method intentionally left empty.
        }

        protected override void preparePackageCommand(Package package)
        {
            if (isQueringEvent)
            {
                package.Command = Command.DIYSearch;
                package.SubCommand = SubCommand.Request;
                package.Content.Type = RequestType.REQTYPE_DIY_AREASTAT_EVNET;
                package.Content.PrepareAddParam();
            }
            else
            {
                package.Command = Command.DIYSearch;
                package.SubCommand = SubCommand.Request;
                package.Content.Type = RequestType.REQTYPE_DIY_AREASTAT_KPI;
                package.Content.PrepareAddParam();
            }
        }

        protected override void preparePackageNeededInfo_Event(Package package)
        {
            StringBuilder sb = new StringBuilder();
            sb.Append("0,1,47,");
            sb.Append("0,2,47,");
            sb.Append("0,3,47,");
            sb.Append("0,4,47,");
            sb.Append("0,5,47,");
            sb.Append("0,6,47,");
            sb.Append("0,7,47,");
            sb.Append("0,8,47,");
            sb.Append("0,9,47,");
            sb.Append("0,10,47,");
            sb.Append("0,11,47,");
            sb.Append("0,12,47,");
            sb.Append("0,13,47,");
            sb.Append("0,14,47,");
            sb.Append("0,15,47,");
            sb.Append("0,16,47");
            package.Content.AddParam(sb.ToString());
        }

        protected override bool getConditionBeforeQuery()
        {
            return true;
        }

        public virtual void ClearDataAfterQuery()
        {
            curStrb = new StringBuilder();
        }
        protected override void afterRecieveAllData(params object[] reservedParams)
        {
            // Method intentionally left empty.
        }
        protected override void queryPeriodInfo(TimePeriod period, ClientProxy clientProxy, params object[] reservedParams)
        {
            List<int> tmpList = new List<int>(noGisCond.areaList);
            while (tmpList.Count > 0)
            {
                noGisCond.areaList.Clear();
                curStrb = new StringBuilder();
                for (int i = 0; i < tmpList.Count; i++)
                {
                    int id = tmpList[i];
                    noGisCond.areaList.Add(id);
                    curStrb.Append(id);
                    curStrb.Append(",");
                    int strLength = noGisCond.queryCondition.FileName == null ? curStrb.Length
                        : curStrb.Length + noGisCond.queryCondition.FileName.Length;

                    if (strLength > 5000)
                    {
                        tmpList.RemoveRange(0, i + 1);
                        break;
                    }
                    else if (i == tmpList.Count - 1)
                    {
                        tmpList.Clear();
                    }
                }
                if (curStrb.Length > 0)
                {
                    curStrb.Remove(curStrb.Length - 1, 1);
                }
                //stat
                base.queryPeriodInfo(period, clientProxy, reservedParams);
            }
        }

        protected override bool isImgColDefContent(Package package, List<StatImgDefItem> imgColDefSet)
        {
            if (package.Content.Type == ResponseType.COLUMN_DEFINE)
            {
                imgColDefSet.Clear();
                parseToCurImgColumnDef(package.Content.GetParamString(), imgColDefSet);
                return true;
            }
            else
            {
                return false;
            }
        }

        protected override bool isKPIDataContent(Package package, out KPIStatDataBase statData)
        {
            statData = null;
            switch (package.Content.Type)
            {
                case ResponseType.RESTYPE_DIY_AREASTAT_KPI_GSM:
                case ResponseType.RESTYPE_DIY_AREASTAT_KPI_GPRS:
                    statData = new StatDataGSM();
                    break;
                case ResponseType.AREASTAT_KPI_LTE:
                    statData = new StatDataLTE();
                    break;
                case ResponseType.RESTYPE_DIY_AREASTAT_KPI_LTE_FDD_AMR:
                    statData = new StatDataLTE_FDD();
                    break;
                case ResponseType.RESTYPE_DIY_AREASTAT_KPI_TDSCDMA_AMR:
                case ResponseType.RESTYPE_DIY_AREASTAT_KPI_TDSCDMA_PS:
                case ResponseType.RESTYPE_DIY_AREASTAT_KPI_TDSCDMA_VP:
                    statData = new StatDataTD();
                    break;
                case ResponseType.RESTYPE_DIY_AREASTAT_KPI_WCDMA_AMR:
                case ResponseType.RESTYPE_DIY_AREASTAT_KPI_WCDMA_PS:
                case ResponseType.RESTYPE_DIY_AREASTAT_KPI_WCDMA_VP:
                case ResponseType.RESTYPE_DIY_AREASTAT_KPI_WCDMA_PSHS:
                    statData = new StatDataWCDMA();
                    break;
                case ResponseType.RESTYPE_DIY_AREASTAT_KPI_CDMA_V:
                case ResponseType.RESTYPE_DIY_AREASTAT_KPI_CDMA_D:
                    statData = new StatDataCDMA_Voice();
                    break;
                case ResponseType.RESTYPE_DIY_AREASTAT_KPI_CDMA2000_D:
                    statData = new StatDataCDMA_EVDO();
                    break;
                case ResponseType.RESTYPE_DIY_AREASTAT_KPI_SCAN_GSM:
                    statData = new StatDataSCAN_GSM();
                    break;
                case ResponseType.RESTYPE_DIY_AREASTAT_KPI_SCAN_LTETOPN:
                case ResponseType.RESTYPE_DIY_AREASTAT_KPI_SCAN_LTEFREQSPECTRUM:
                    statData = new StatDataSCAN_LTE();
                    break;
                case ResponseType.RESTYPE_DIY_AREASTAT_KPI_SCAN_TDSCDMA:
                    statData = new StatDataSCAN_TD();
                    break;
                case ResponseType.RESTYPE_DIY_AREA_COVER_GRID_SCAN_WCDMA:
                case ResponseType.RESTYPE_DIY_AREA_COVER_GRID_SCAN_DTWCDMA:
                    statData = new StatDataSCAN_WCDMA();
                    break;

                case ResponseType.RESTYPE_DIY_AREA_COVER_GRID_SCAN_CDMA:
                    statData = new StatDataSCAN_CDMA();
                    break;
                case ResponseType.RESTYPE_DIY_AREA_COVER_GRID_GSM_MTR:
                case ResponseType.RESTYPE_DIY_LOG_KPI_GSM_MTR:
                    statData = new StatDataGSM_MTR();
                    break;
                //case ResponseType.RESTYPE_DIY_LOG_KPI_WLAN: //与RESTYPE_DIY_AREASTAT_KPI_LTE_FDD_AMR冲突
                //    statData = new StatDataWLAN();
                //    break;
                case ResponseType.RESTYPE_DIY_AREA_COVER_GRID_LTE_SIGNAL:
                    statData = new StatDataLTE_Signal();
                    break;
                case ResponseType.RESTYPE_DIY_AREA_COVER_GRID_SCAN_NBIOT_TOPN:
                case ResponseType.RESTYPE_DIY_CELL_COVER_GRID_SCAN_NBIOT_TOPN:
                case ResponseType.RESTYPE_DIY_AREASTAT_KPI_SCAN_NBIOTTOPN:
                    statData = new StatDataSCAN_NBIOT();
                    break;
                case ResponseType.RESTYPE_DIY_AREASTAT_KPI_NR:
                    statData = new StatDataNR();
                    break;
            }

            return statData != null;
        }

#if GDCompareStat
        private bool isGDCompareStatType(byte ContentType)
        {
            return ContentType == ResponseType.RESTYPE_DIY_AREASTAT_KPI_LTE_FDD_AMR
                || ContentType == ResponseType.RESTYPE_DIY_AREASTAT_KPI_SCAN_GSM
                || ContentType == ResponseType.RESTYPE_DIY_AREASTAT_KPI_SCAN_TDSCDMA
                || ContentType == ResponseType.RESTYPE_DIY_AREASTAT_KPI_SCAN_LTETOPN
                || ContentType == ResponseType.AREASTAT_KPI_LTE;
        }
#endif

        protected override void recieveAndHandleSpecificStatData(Package package
            , List<StatImgDefItem> curImgColumnDef
            , KPIStatDataBase singleStatData)
        {
            int areaType = package.Content.GetParamInt();
            int areaID = package.Content.GetParamInt();
#if GDCompareStat
            if (isGDCompareStatType(package.Content.Type) && noGisCond.isReject && areaType == 40 && areaID == 1)
            {
                return;
            }
#endif
            fillStatData(package, curImgColumnDef, singleStatData);
            DTDataHeader fi = DTDataHeaderManager.GetInstance().GetHeaderByFileID(singleStatData.FileID);

            List<AreaStatInfo> areaDataSet = new List<AreaStatInfo>();
            bool isAdded = addAreaDataSet(singleStatData, areaType, areaID, fi, areaDataSet);
            if (!isAdded)
            {
                return;
            }

            foreach (AreaStatInfo tmp in areaDataSet)
            {
                bool valid;
                string rowKeyStr = GetKeyUnionString(this.rptTemplate, tmp, out valid);
                if (!valid)
                {
                    return;
                }
                AreaStatInfo areaData = null;
                if (this.AreaKeyDataDic.TryGetValue(rowKeyStr, out areaData))
                {
                    areaData.KPIData.AddStatData(fi, singleStatData, false);
                    if (areaData.FileIDDic != null)
                    {
                        areaData.AddFileInfo(fi);
                    }
                }
                else
                {
                    this.AreaKeyDataDic[rowKeyStr] = tmp;
                }
                getMergeData(tmp, fi, singleStatData);
            }
        }

        private bool addAreaDataSet(KPIStatDataBase singleStatData, int areaType, int areaID, DTDataHeader fi, List<AreaStatInfo> areaDataSet)
        {
            if (CheckFileIDDic != null)
            {
                List<OrderCheckFileItem> files;
                if (!CheckFileIDDic.TryGetValue(fi.ID, out files))
                {
                    return false;
                }
                else
                {
                    foreach (OrderCheckFileItem file in files)
                    {
                        AreaStatInfo orderCheck = new AreaStatInfo();
                        orderCheck.ReservedDic = new Dictionary<string, object>();
                        orderCheck.ReservedDic[KeyName.OrderFlag] = file.OrderFlag;
                        orderCheck.ReservedDic[KeyName.OrderCityName] = file.CityName;
                        orderCheck.ReservedDic[KeyName.OrderCheckRound] = file.Round;
                        orderCheck.ReservedDic[KeyName.OrderAreaTypeName] = file.AreaTypeName;
                        orderCheck.ReservedDic[KeyName.OrderAreaName] = file.AreaName;
                        orderCheck.AreaType = areaType;
                        orderCheck.AreaID = areaID;
                        orderCheck.DistrictID = condition.DistrictID;
                        orderCheck.FileHeader = fi;
                        orderCheck.AddFileInfo(fi);
                        orderCheck.KPIData.AddStatData(fi, singleStatData, false);
                        areaDataSet.Add(orderCheck);
                    }
                }
            }
            else
            {
                AreaStatInfo tmp = new AreaStatInfo();
                tmp.AreaType = areaType;
                tmp.AreaID = areaID;
                tmp.DistrictID = condition.DistrictID;
                tmp.FileHeader = fi;
                tmp.AddFileInfo(fi);
                tmp.KPIData.AddStatData(fi, singleStatData, false);
                areaDataSet.Add(tmp);
            }
            return true;
        }

        protected override void handleStatEvent(Event evt)
        {
#if GDCompareStat
            if (noGisCond.isReject && evt.AreaTypeID == 40 && evt.AreaID == 1)
            {
                return;
            }
#endif
            int iareatype = evt.AreaTypeID;
            int iareaid = evt.AreaID;
            int ifileid = evt.FileID;
            DTDataHeader fi = DTDataHeaderManager.GetInstance().GetHeaderByFileID(ifileid);
            StatDataEvent singleStatData = new StatDataEvent(evt, needSeparateByServiceID(evt), needSeparateByFileName(evt));

            List<AreaStatInfo> areaDataSet = new List<AreaStatInfo>();
            if (CheckFileIDDic != null)
            {
                List<OrderCheckFileItem> files;
                if (!CheckFileIDDic.TryGetValue(fi.ID, out files))
                {
                    return;
                }
                else
                {
                    foreach (OrderCheckFileItem file in files)
                    {
                        AreaStatInfo orderCheck = new AreaStatInfo();
                        orderCheck.ReservedDic = new Dictionary<string, object>();
                        orderCheck.ReservedDic[KeyName.OrderFlag] = file.OrderFlag;
                        orderCheck.ReservedDic[KeyName.OrderCityName] = file.CityName;
                        orderCheck.ReservedDic[KeyName.OrderCheckRound] = file.Round;
                        orderCheck.ReservedDic[KeyName.OrderAreaTypeName] = file.AreaTypeName;
                        orderCheck.ReservedDic[KeyName.OrderAreaName] = file.AreaName;
                        orderCheck.AreaType = iareatype;
                        orderCheck.AreaID = iareaid;
                        orderCheck.DistrictID = condition.DistrictID;
                        orderCheck.FileHeader = fi;
                        orderCheck.AddFileInfo(fi);
                        orderCheck.KPIData.AddStatData(fi, singleStatData, false);
                        areaDataSet.Add(orderCheck);
                    }
                }
            }
            else
            {
                AreaStatInfo tmp = new AreaStatInfo();
                tmp.AreaType = iareatype;
                tmp.AreaID = iareaid;
                tmp.DistrictID = condition.DistrictID;
                tmp.FileHeader = fi;
                tmp.AddFileInfo(fi);
                tmp.KPIData.AddStatData(fi, singleStatData, false);
                areaDataSet.Add(tmp);
            }
          
            foreach (AreaStatInfo tmp in areaDataSet)
            {
                bool valid;
                string rowKeyStr = GetKeyUnionString(this.rptTemplate, tmp, out valid);
                if (!valid)
                {
                    return;
                }

                AreaStatInfo areaData = null;
                if (this.AreaKeyDataDic.TryGetValue(rowKeyStr, out areaData))
                {
                    areaData.KPIData.AddStatData(fi, singleStatData, false);
                    areaData.AddFileInfo(fi);
                }
                else
                {
                    this.AreaKeyDataDic[rowKeyStr] = tmp;
                }
                getMergeData(tmp, fi, singleStatData);
            }
        }

        private void getMergeData(AreaStatInfo data, DTDataHeader fi, KPIStatDataBase singleStatData)
        {
            if (isMergeData)
            {
                bool isCanMerge = false;
                string keyMergeUnionStr = getMergeKeyUnionString(rptTemplate, data, ref isCanMerge);
                if (isCanMerge)
                {
                    AreaStatInfo retDataMerge;
                    if (AreaKeyDataDic.TryGetValue(keyMergeUnionStr, out retDataMerge))
                    {
                        retDataMerge.KPIData.AddStatData(fi, singleStatData, false);
                    }
                    else
                    {
                        AreaStatInfo auData = new AreaStatInfo();
                        auData.IsSum = true;
                        auData.AreaType = data.AreaType;
                        auData.AreaID = data.AreaID;
                        auData.DistrictID = data.DistrictID;
                        auData.KPIData.AddStatData(fi, singleStatData, false);
                        AreaKeyDataDic[keyMergeUnionStr] = auData;
                    }
                }
            }
        }

        private string getMergeKeyUnionString(ReporterTemplate tpl, AreaStatInfo statInfo, ref bool isCanMerge)
        {
            isCanMerge = false;
            if (tpl.KeyCount <= 1)
            {
                return "";
            }
            string val = "";
            for (int i = 0; i < tpl.KeyCount; i++)
            {
                ColumnSet cs = tpl.Columns[i];
                if (cs.Exp == null || !cs.Exp.Contains("kAreaId"))
                {
                    continue;
                }
                isCanMerge = true;
                string keyFieldRet = "";
                if (IsKeyIdColumn(cs.Exp, out keyFieldRet))
                {
                    val = GetKeyValue(keyFieldRet, tpl, statInfo);
                }
                else
                {
                    val = statInfo.KPIData.CalcFormula((CarrierType)cs.carrierId, cs.momt, cs.Exp, cs.decPlace
                        , cs.Title, cs.ServiceIDSet, cs.FileNameKeyValueList).ToString();
                }
                break;
            }
            return val;
        }

        protected string GetKeyUnionString(ReporterTemplate tpl, AreaStatInfo statInfo, out bool valid)
        {
            valid = true;
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < tpl.KeyCount; i++)
            {
                ColumnSet cs = tpl.Columns[i];
                string keyFieldRet = "";
                string val = "";
                if (IsKeyIdColumn(cs.Exp, out keyFieldRet))
                {
                    val = GetValidKeyValue(keyFieldRet, tpl, statInfo, out valid);
                }
                else
                {
                    val = statInfo.KPIData.CalcFormula((CarrierType)cs.carrierId
                        , cs.momt, cs.Exp, cs.decPlace, cs.Title, cs.ServiceIDSet, cs.FileNameKeyValueList).ToString();
                }
                sb.Append(val);
                sb.Append("&");
            }
            return sb.ToString();
        }

        protected string GetValidKeyValue(string keyFieldRet, ReporterTemplate tpl, AreaStatInfo statInfo, out bool valid)
        {
            valid = true;
            string val = "";

            DTDataHeader fileHeader = statInfo.FileHeader;
            if (keyFieldRet == "kCollegeSceneType" && fileHeader != null)
            {
                if (fileHeader.StatStatus >= 3 && fileHeader.SubType1 != -1)
                {
                    val = CommonNoGisStatForm.collegeSceneTypeDic[fileHeader.SubType1];
                }
                else
                {
                    valid = false;
                }
            }
            else
            {
                val = GetKeyValue(keyFieldRet, tpl, statInfo);
            }
            return val;
        }

        protected override string GetKeyValue(string keyFieldRet, ReporterTemplate tpl, StatInfoBase statInfo)
        {
            AreaStatInfo areaInfo = (AreaStatInfo)statInfo;
            return GetKeyValueBase(keyFieldRet, tpl, areaInfo.AreaType, areaInfo.AreaID, areaInfo.DistrictID, statInfo);
        }

        public List<NPOIRow> CreateReport(ReporterTemplate tpl, List<AreaStatInfo> retdatas)
        {
            List<NPOIRow> outputRows = new List<NPOIRow>();
            NPOIRow row = new NPOIRow();
            outputRows.Add(row);
            //标题
            for (int i = 0; i < tpl.Columns.Count; i++)
            {
                ColumnSet column = tpl.Columns[i];
                row.AddCellValue(column.Title);
            }
            if (retdatas == null)
                return outputRows;

            for (int r = 0; r < retdatas.Count; r++)
            {
                AreaStatInfo statInfo = retdatas[r];
                row = new NPOIRow();
                outputRows.Add(row);
                row.cellValues = new List<object>(tpl.Columns.Count);
                for (int i = 0; i < tpl.Columns.Count; i++)
                {
                    row.cellValues.Add(null);
                    row.cellValues[i] = getColumnSetValue(statInfo, tpl, i, false);
                }
            }
            return outputRows;
        }
    }

    public class AreaStatInfo : StatInfoBase, IComparable<AreaStatInfo>
    {
        public int AreaType { get; set; }
        public int AreaID { get; set; }
        public string AreaName { get; set; }

#region IComparable<AreaStatInfo> 成员

        public int CompareTo(AreaStatInfo other)
        {
            if (this.IsSum != other.IsSum)
            {
                return this.IsSum.CompareTo(other.IsSum);
            }
            else if (this.DistrictID != other.DistrictID)
            {
                return this.DistrictID.CompareTo(other.DistrictID);
            }
            else
            {
                return this.AreaID.CompareTo(other.AreaID);
            }
        }

#endregion
    }
}
