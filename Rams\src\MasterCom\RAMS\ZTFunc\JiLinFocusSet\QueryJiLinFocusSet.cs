﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc.JiLinFocusSet;
using MasterCom.Util;
using MasterCom.Util.UiEx;

namespace MasterCom.RAMS.ZTFunc
{
    public class QueryJiLinFocusSet : DIYSQLBase
    {
        public QueryJiLinFocusSet()
            : base(MainModel.GetInstance())
        {
            MainDB = true;
            IsShowConditonDlg = true;
        }

        public override bool IsNeedSetQueryCondition
        {
            get
            {
                return false;
            }
        }

        private enum TbName
        {
            FocusSet,
            DetailItem,
            CheckLog
        }

        public bool IsShowConditonDlg
        {
            get;
            set;
        }
        
        public DateTime DateFrom { get; set; } = DateTime.Now.AddMonths(-1);
        public DateTime DateTo { get; set; } = DateTime.Now.Date.AddDays(1).AddMilliseconds(-1);

        public string OrderIDLower
        {
            get;
            set;
        }

        protected override void query()
        {
            if (IsShowConditonDlg)
            {
                PeriodPicker pk = new PeriodPicker();
                pk.DateFrom = DateFrom;
                pk.DateTo = DateTo;
                if (pk.ShowDialog() != DialogResult.OK)
                {
                    return;
                }
                DateFrom = pk.DateFrom;
                DateTo = pk.DateTo;
                OrderIDLower = pk.OrderIDLower;
            }

            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, userName, password, dbid > 0 ? dbid : MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
                MainModel.DTDataManager.Clear();
                orderDic = new Dictionary<string, FocusSetMainItem>();
                WaitTextBox.Show("正在查询工单...", queryInThread, clientProxy);
                fireShowResultForm();
                MainModel.FireDTDataChanged(this);
            }
            finally
            {
                clientProxy.Close();
            }
        }

        private void fireShowResultForm()
        {
            FocusSetListForm form = MainModel.CreateResultForm(typeof(FocusSetListForm)) as FocusSetListForm;
            form.FillData(new List<FocusSetMainItem>(orderDic.Values), DateFrom, DateTo, OrderIDLower);
            form.Visible = true;
            form.BringToFront();
        }

        TbName curTbName;
        protected override void queryInThread(object o)
        {
            try
            {
                queryFromDB((ClientProxy)o, TbName.FocusSet);
                if (orderDic.Count > 0)
                {
                    queryFromDB((ClientProxy)o, TbName.DetailItem);
                    queryFromDB((ClientProxy)o, TbName.CheckLog);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.ToString());
            }
            finally
            {
                WaitTextBox.Close();
            }
        }

        private void queryFromDB(ClientProxy proxy, TbName table)
        {
            Package package = proxy.Package;
            curTbName = table;
            string strsql = getSqlTextString();
            E_VType[] retArrDef = getSqlRetTypeArr();
            package.Command = Command.DIYSearch;
            package.SubCommand = SubCommand.Request;
            if (MainDB)
            {
                package.Content.Type = RequestType.REQTYPE_DIY_MODEL_SQL_FORMAINDB;
            }
            else
            {
                package.Content.Type = RequestType.REQTYPE_DIY_MODEL_SQL;
            }
            package.Content.PrepareAddParam();
            package.Content.AddParam(strsql);
            StringBuilder sb = new StringBuilder();
            if (retArrDef != null)
            {
                for (int i = 0; i < retArrDef.Length; i++)
                {
                    sb.Append((int)retArrDef[i]);
                    sb.Append(",");
                }
            }

            package.Content.AddParam(sb.ToString().TrimEnd(','));
            proxy.Send();
            receiveRetData(proxy);
        }

        protected override string getSqlTextString()
        {
            //吉林现场，工单放在Province库
            string orderCond = " where createdDate >=" + (int)(JavaDate.GetMilliseconds(DateFrom) / 1000)
                + " and createdDate<=" + (int)(JavaDate.GetMilliseconds(DateTo) / 1000)
                + (MainModel.User.DBID == -1 && MainModel.DistrictID == 10 ? "" : " and cityID = " + MainModel.DistrictID);
            switch (curTbName)
            {
#if DEBUG
                case TbName.FocusSet:
                    return @"select cityID,setOrderType,setID,createdDate,longitude,latitude,itemCount,areaNames,roadNames,orderID,statusEx from RAMSSERVER.[dbo].tb_focus_order"
                        + orderCond + " order by cityID,setID";
                case TbName.DetailItem:
                    return @"select cityID,setOrderType,setID,itemID,status,createdDate,closedDate
,fileID,projectID,serviceID,logTbName,seqID,time,timeMS,ms,longitude,latitude,eventID,LAC,CI,anaDate,preTypeDesc
,reasonDesc,solutionDesc,fileName,问题点开始时间,问题点结束时间,平均速率,平均RSRP,平均SINR from RAMSSERVER.[dbo].tb_focus_order_event"
                     + (MainModel.User.DBID == -1 && MainModel.DistrictID == 10 ? "" : " where cityID = " + MainModel.DistrictID)
                     + " order by setID,itemID";
                case TbName.CheckLog://Province
                    return @"select setid, cityid, ordertype, checktime, status, filename,fileid, stime, etime,ProjectID,ServiceType,CarrierType, checkcount 
from RAMSSERVER.[dbo].tb_focus_check_log  "
                         + (MainModel.User.DBID == -1 && MainModel.DistrictID == 10 ? "" : " where cityid = " + MainModel.DistrictID)
                     + " order by setid";
#else
                case TbName.FocusSet:
                    return @"select cityID,setOrderType,setID,createdDate,longitude,latitude,itemCount,areaNames,roadNames,orderID,statusEx from Province.[dbo].tb_focus_order"
                        + orderCond + " order by cityID,setID";
                case TbName.DetailItem:
                    return @"select cityID,setOrderType,setID,itemID,status,createdDate,closedDate
,fileID,projectID,serviceID,logTbName,seqID,time,timeMS,ms,longitude,latitude,eventID,LAC,CI,anaDate,preTypeDesc
,reasonDesc,solutionDesc,fileName,问题点开始时间,问题点结束时间,平均速率,平均RSRP,平均SINR from Province.[dbo].tb_focus_order_event"
                     + (MainModel.User.DBID == -1 && MainModel.DistrictID == 10 ? "" : " where cityID = " + MainModel.DistrictID)
                     + " order by setID,itemID";
                case TbName.CheckLog://Province
                    return @"select setid, cityid, ordertype, checktime, status, filename,fileid, stime, etime,ProjectID,ServiceType,CarrierType, checkcount 
from Province.[dbo].tb_focus_check_log  "
                         + (MainModel.User.DBID == -1 && MainModel.DistrictID == 10 ? "" : " where cityid = " + MainModel.DistrictID)
                     + " order by setid";
#endif
                default:
                    break;
            }
            return null;
        }

        protected override Model.Interface.E_VType[] getSqlRetTypeArr()
        {
            int i = 0;
            if (TbName.FocusSet == curTbName)
            {
                E_VType[] arr = new E_VType[11];
                arr[i++] = E_VType.E_Int;
                arr[i++] = E_VType.E_String;
                arr[i++] = E_VType.E_Int;
                arr[i++] = E_VType.E_Int;
                arr[i++] = E_VType.E_Int;
                arr[i++] = E_VType.E_Int;
                arr[i++] = E_VType.E_Int;
                arr[i++] = E_VType.E_String;
                arr[i++] = E_VType.E_String;
                arr[i++] = E_VType.E_String;
                arr[i] = E_VType.E_Int;
                return arr;
            }
            else if (TbName.DetailItem == curTbName)
            {
                E_VType[] arr = new E_VType[30];
                arr[i++] = E_VType.E_Int;
                arr[i++] = E_VType.E_String;
                arr[i++] = E_VType.E_Int;
                arr[i++] = E_VType.E_Int;
                arr[i++] = E_VType.E_Int;
                arr[i++] = E_VType.E_Int;
                arr[i++] = E_VType.E_Int;
                arr[i++] = E_VType.E_Int;
                arr[i++] = E_VType.E_Int;
                arr[i++] = E_VType.E_Int;
                arr[i++] = E_VType.E_String;
                arr[i++] = E_VType.E_Int;
                arr[i++] = E_VType.E_Int;
                arr[i++] = E_VType.E_Short;
                arr[i++] = E_VType.E_Byte;
                arr[i++] = E_VType.E_Int;
                arr[i++] = E_VType.E_Int;
                arr[i++] = E_VType.E_Int;
                arr[i++] = E_VType.E_Int;
                arr[i++] = E_VType.E_Int;
                arr[i++] = E_VType.E_Int;
                arr[i++] = E_VType.E_String;
                arr[i++] = E_VType.E_String;
                arr[i++] = E_VType.E_String;
                arr[i++] = E_VType.E_String;
                arr[i++] = E_VType.E_String;
                arr[i++] = E_VType.E_String;
                arr[i++] = E_VType.E_Float;
                arr[i++] = E_VType.E_Float;
                arr[i] = E_VType.E_Float;
                return arr;
            }
            else if (TbName.CheckLog == curTbName)
            {
                E_VType[] arr = new E_VType[13];
                arr[i++] = E_VType.E_Int;
                arr[i++] = E_VType.E_Int;
                arr[i++] = E_VType.E_String;
                arr[i++] = E_VType.E_String;
                arr[i++] = E_VType.E_String;
                arr[i++] = E_VType.E_String;
                arr[i++] = E_VType.E_Int;
                arr[i++] = E_VType.E_Int;
                arr[i++] = E_VType.E_Int;
                arr[i++] = E_VType.E_Int;
                arr[i++] = E_VType.E_Int;
                arr[i++] = E_VType.E_Int;
                arr[i] = E_VType.E_Int;
                return arr;
            }
            return new E_VType[0];
        }

        private Dictionary<string, FocusSetMainItem> orderDic = null;
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    fillData(package);
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_SUCCESS)
                {
                    //
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_FAIL)
                {
                    //
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }

        private void fillData(Package package)
        {
            if (curTbName == TbName.FocusSet)
            {
                FocusSetMainItem item = FocusSetMainItem.Create(package.Content);
                if (string.IsNullOrEmpty(OrderIDLower) || item.OrderID.ToLower().Contains(OrderIDLower))
                {
                    orderDic[item.Key] = item;
                }
            }
            else if (curTbName == TbName.DetailItem)
            {
                EventItem evtItem = EventItem.Create(package.Content);
                FocusSetMainItem mainItem = null;
                if (orderDic.TryGetValue(evtItem.OrderKey, out mainItem))
                {
                    mainItem.AddItem(evtItem);
                    MainModel.DTDataManager.Add(evtItem);
                }
            }
            else if (curTbName == TbName.CheckLog)
            {
                CheckLogItem checkItem = CheckLogItem.Create(package.Content);
                FocusSetMainItem mainItem = null;
                if (orderDic.TryGetValue(checkItem.Key, out mainItem))
                {
                    mainItem.LogItems.Add(checkItem);
                }
            }
        }
    }

}
