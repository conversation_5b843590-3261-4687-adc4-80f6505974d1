﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.NewBlackBlock;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.BackgroundFunc
{
    public class CellFileAnaInfo_CqtUl_GZ : CellFileAnaInfoBase_GZ
    {
        public CellFileAnaInfo_CqtUl_GZ(FileInfo file, LTECell cell)
            : base(file, cell)
        {
            this.RsrpInfo = new AvgKpiInfo();
            this.SinrInfo = new AvgKpiInfo();
            this.UlInfo = new AvgKpiInfo();
        }
        public AvgKpiInfo RsrpInfo { get; set; }
        public AvgKpiInfo SinrInfo { get; set; }
        public AvgKpiInfo UlInfo { get; set; }

        private double maxUlSpeed = double.MinValue;
        public double MaxUlSpeed
        {
            get { return Math.Round(maxUlSpeed, 2); }
            set { maxUlSpeed = value; }
        }
        public override BackgroundResult ConvertToBackgroundResult()
        {
            BackgroundResult bgResult = getFileAndCellToBgResult();
            bgResult.RxLevMean = RsrpInfo.KpiAvgValue == null ? 255 : (float)RsrpInfo.KpiAvgValue;
            bgResult.RxQualMean = SinrInfo.KpiAvgValue == null ? 255 : (float)SinrInfo.KpiAvgValue;

            Dictionary<uint, object> kpiDic = new Dictionary<uint, object>();
            kpiDic.Add((uint)KpiKey.FtpUlPntCount_Rsrp, RsrpInfo.PointCount);
            kpiDic.Add((uint)KpiKey.FtpUlPntCount_Sinr, SinrInfo.PointCount);

            if (UlInfo.KpiAvgValue != null)
            {
                kpiDic.Add((uint)KpiKey.FtpUlSpeedAvg, UlInfo.KpiAvgValue);
                kpiDic.Add((uint)KpiKey.FtpUlPntCount_Speed, UlInfo.PointCount);
                kpiDic.Add((uint)KpiKey.FtpUlSpeedMax, MaxUlSpeed);
            }
            byte[] kpiBytes = KeyValueImageParser.ToImage(kpiDic);
            bgResult.AddImageValue(kpiBytes);

            return bgResult;
        }
    }
    public class CellFileAnaInfo_CqtDl_GZ : CellFileAnaInfoBase_GZ
    {
        public CellFileAnaInfo_CqtDl_GZ(FileInfo file, LTECell cell)
            : base(file, cell)
        {
            this.RsrpInfo = new AvgKpiInfo();
            this.SinrInfo = new AvgKpiInfo();
            this.DlInfo = new AvgKpiInfo();
        }
        public AvgKpiInfo RsrpInfo { get; set; }
        public AvgKpiInfo SinrInfo { get; set; }
        public AvgKpiInfo DlInfo { get; set; }

        private double maxDlSpeed = double.MinValue;
        public double MaxDlSpeed
        {
            get { return Math.Round(maxDlSpeed, 2); }
            set { maxDlSpeed = value; }
        }
        public override BackgroundResult ConvertToBackgroundResult()
        {
            BackgroundResult bgResult = getFileAndCellToBgResult();
            bgResult.RxLevMean = RsrpInfo.KpiAvgValue == null ? 255 : (float)RsrpInfo.KpiAvgValue;
            bgResult.RxQualMean = SinrInfo.KpiAvgValue == null ? 255 : (float)SinrInfo.KpiAvgValue;

            Dictionary<uint, object> kpiDic = new Dictionary<uint, object>();
            kpiDic.Add((uint)KpiKey.FtpDlPntCount_Rsrp, RsrpInfo.PointCount);
            kpiDic.Add((uint)KpiKey.FtpDlPntCount_Sinr, SinrInfo.PointCount);

            if (DlInfo.KpiAvgValue != null)
            {
                kpiDic.Add((uint)KpiKey.FtpDlSpeedAvg, DlInfo.KpiAvgValue);
                kpiDic.Add((uint)KpiKey.FtpDlPntCount_Speed, DlInfo.PointCount);
                kpiDic.Add((uint)KpiKey.FtpDlSpeedMax, MaxDlSpeed);

            }
            byte[] kpiBytes = KeyValueImageParser.ToImage(kpiDic);
            bgResult.AddImageValue(kpiBytes);

            return bgResult;
        }
    }
    public class CellFileAnaInfo_CqtCsfb_GZ : CellFileAnaInfoBase_GZ
    {
        public CellFileAnaInfo_CqtCsfb_GZ(FileInfo file, LTECell cell)
            : base(file, cell)
        {
            this.CsfbCallInfo = new CallSucessInfo();
        }
        public CallSucessInfo CsfbCallInfo { get; set; }
        public override BackgroundResult ConvertToBackgroundResult()
        {
            BackgroundResult bgResult = getFileAndCellToBgResult();
            Dictionary<uint, object> kpiDic = new Dictionary<uint, object>();
            if (CsfbCallInfo.SucessRatioAllCall != double.MinValue)
            {
                kpiDic.Add((uint)KpiKey.CsfbSucessRatioAllCall, CsfbCallInfo.SucessRatioAllCall);
                kpiDic.Add((uint)KpiKey.CsfbSucessRatioMoCall, CsfbCallInfo.SucessRatioMoCall);
            }
            byte[] kpiBytes = KeyValueImageParser.ToImage(kpiDic);
            bgResult.AddImageValue(kpiBytes);

            return bgResult;
        }
    }
    public class CellFileAnaInfo_DT_GZ : CellFileAnaInfoBase_GZ
    {
        public CellFileAnaInfo_DT_GZ(FileInfo file, LTECell cell)
            : base(file, cell)
        {
            this.RsrpInfo = new AvgKpiInfo();
            this.SinrInfo = new AvgKpiInfo();
        }
        public AvgKpiInfo RsrpInfo { get; set; }
        public AvgKpiInfo SinrInfo { get; set; }
        public override BackgroundResult ConvertToBackgroundResult()
        {
            BackgroundResult bgResult = getFileAndCellToBgResult();
            bgResult.RxLevMean = RsrpInfo.KpiAvgValue == null ? 255 : (float)RsrpInfo.KpiAvgValue;
            bgResult.RxQualMean = SinrInfo.KpiAvgValue == null ? 255 : (float)SinrInfo.KpiAvgValue;

            Dictionary<uint, object> kpiDic = new Dictionary<uint, object>();
            kpiDic.Add((uint)KpiKey.FtpDlPntCount_Rsrp, RsrpInfo.PointCount);
            kpiDic.Add((uint)KpiKey.FtpDlPntCount_Sinr, SinrInfo.PointCount);

            byte[] kpiBytes = KeyValueImageParser.ToImage(kpiDic);
            bgResult.AddImageValue(kpiBytes);

            return bgResult;
        }
    }

    public abstract class CellFileAnaInfoBase_GZ
    {
        protected CellFileAnaInfoBase_GZ(FileInfo file, LTECell cell)
        {
            this.File = file;
            this.LteCell = cell;
        }
        public FileInfo File { get; set; }
        public LTECell LteCell { get; set; }

        public abstract BackgroundResult ConvertToBackgroundResult();
        protected BackgroundResult getFileAndCellToBgResult()
        {
            BackgroundResult bgResult = new BackgroundResult();
            bgResult.FileID = File.ID;
            bgResult.FileName = File.Name;
            bgResult.ISTime = File.BeginTime;
            bgResult.IETime = File.EndTime;

            bgResult.CellType = BackgroundCellType.LTE;
            bgResult.LAC = LteCell.TAC;
            bgResult.CI = LteCell.ECI;
            bgResult.BCCH = LteCell.EARFCN;
            bgResult.BSIC = LteCell.PCI;
            bgResult.LongitudeMid = LteCell.Longitude;
            bgResult.LatitudeMid = LteCell.Latitude;
            bgResult.CellIDDesc = LteCell.Name;
            bgResult.StrDesc = LteCell.BTSName;

            return bgResult;
        }
    }
}
