﻿using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public abstract class CellSetRegionDataBase
    {
        protected CellSetRegionDataBase()
        {
            RegionCellDic = new Dictionary<string, Dictionary<ICell, CellStater>>();
            RegionUnknowCellDic = new Dictionary<string, Dictionary<string, CellStater>>();
            RegionTestPointCnt = new Dictionary<string, long>();
            RegionBtsCntDic = new Dictionary<string, long>();
            RegionBTSDic = new Dictionary<string, Dictionary<ISite, BtsStater>>();
            RegionUnknowBTSDic = new Dictionary<string, Dictionary<string, BtsStater>>();
            RegionNbCellDic = new Dictionary<string, Dictionary<ICell, List<float>>>();
            SNCellList = new List<CellSetResultBase.SNCellGatherInfo>();
        }

        //cell
        public Dictionary<string, Dictionary<ICell, CellStater>> RegionCellDic { get; set; }
        public Dictionary<string, Dictionary<string, CellStater>> RegionUnknowCellDic { get; set; }

        //tp & bts cnt
        public Dictionary<string, long> RegionTestPointCnt { get; set; }
        public Dictionary<string, long> RegionBtsCntDic { get; set; }

        //bts
        public Dictionary<string, Dictionary<ISite, BtsStater>> RegionBTSDic { get; set; }
        public Dictionary<string, Dictionary<string, BtsStater>> RegionUnknowBTSDic { get; set; }

        //nbcell
        public Dictionary<string, Dictionary<ICell, List<float>>> RegionNbCellDic { get; set; }
        public List<CellSetResultBase.SNCellGatherInfo> SNCellList { get; set; }

        public CellSetResultBase Result { get; set; }
        public abstract void DoWithData(TestPoint tpPoint);
        public abstract void Stat(List<string> selectedRegionNames, QueryCondition cond);

        protected virtual string getRegionName(TestPoint tpPoint)
        {
            string RoadName = GISManager.GetInstance().GetRoadPlaceDesc(tpPoint.Longitude, tpPoint.Latitude);
            if (string.IsNullOrEmpty(RoadName))
            {
                RoadName = "当前所选道路";
            }
            else
            {
                string[] strAry = RoadName.Split(';');
                RoadName = strAry[0];
            }

            return RoadName;
        }

        protected virtual bool isValidData(TestPoint tpPoint)
        {
            if (tpPoint.Longitude == 0 && tpPoint.Latitude == 0)
            {
                return false;
            }
            float? rsrp = NRTpHelper.NrTpManager.GetSCellRsrp(tpPoint);
            if (rsrp == null)
            {
                return false;
            }
            return true;
        }

        #region 添加数据
        protected abstract string getCellToken(TestPoint tp);
        protected abstract CellStater getCellStater();
        protected abstract BtsStater getBtsStater();

        protected virtual void addNBCell(string regionName, ICell nbCell, float? nRsrp)
        {
            Dictionary<ICell, List<float>> nbCellStaterMap;
            if (!RegionNbCellDic.TryGetValue(regionName, out nbCellStaterMap))
            {
                nbCellStaterMap = new Dictionary<ICell, List<float>>();
                RegionNbCellDic.Add(regionName, nbCellStaterMap);
            }
            if (nbCellStaterMap.ContainsKey(nbCell))
            {
                nbCellStaterMap[nbCell].Add((float)nRsrp);
            }
            else
            {
                List<float> rsrpList = new List<float>
                {
                    (float)nRsrp
                };
                nbCellStaterMap[nbCell] = rsrpList;
            }
        }

        protected virtual void addCell(string regionName, ICell cell, TestPoint tpPoint)
        {
            Dictionary<ICell, CellStater> cellStaterMap;
            if (!RegionCellDic.TryGetValue(regionName, out cellStaterMap))
            {
                cellStaterMap = new Dictionary<ICell, CellStater>();
                RegionCellDic.Add(regionName, cellStaterMap);
            }

            CellStater stater;
            if (cellStaterMap.ContainsKey(cell))
            {
                stater = cellStaterMap[cell];
            }
            else
            {
                stater = getCellStater();
                cellStaterMap[cell] = stater;
            }

            float? rsrp = NRTpHelper.NrTpManager.GetSCellRsrp(tpPoint);
            stater.RsrpDataSub.AddDataSub(rsrp, stater.TPCount);
            float? sinr = NRTpHelper.NrTpManager.GetSCellSinr(tpPoint);
            stater.SinrDataSub.AddDataSub(sinr, stater.TPCount);
            float? rsrq = NRTpHelper.NrTpManager.GetSCellRsrq(tpPoint);
            stater.RsrqDataSub.AddDataSub(rsrq, stater.TPCount);
            double distance = MathFuncs.GetDistance(cell.Longitude, cell.Latitude, tpPoint.Longitude, tpPoint.Latitude);
            stater.TPCount = stater.DistanceDataSub.AddDataSub(distance, stater.TPCount);

            if (RegionTestPointCnt.ContainsKey(regionName))
            {
                RegionTestPointCnt[regionName]++;
            }
            else
            {
                RegionTestPointCnt[regionName] = 1;
            }
        }

        protected virtual void addNodeB(string regionName, ISite nodeb, TestPoint tpPoint)
        {
            Dictionary<ISite, BtsStater> tdbtsStaterMap;
            if (!RegionBTSDic.TryGetValue(regionName, out tdbtsStaterMap))
            {
                tdbtsStaterMap = new Dictionary<ISite, BtsStater>();
                RegionBTSDic.Add(regionName, tdbtsStaterMap);
            }

            BtsStater stater;
            if (tdbtsStaterMap.ContainsKey(nodeb))
            {
                stater = tdbtsStaterMap[nodeb];
            }
            else
            {
                stater = getBtsStater();
                tdbtsStaterMap[nodeb] = stater;
            }

            float? rsrp = NRTpHelper.NrTpManager.GetSCellRsrp(tpPoint);
            stater.RsrpDataSub.AddDataSub(rsrp, stater.TPCount);
            float? sinr = NRTpHelper.NrTpManager.GetSCellSinr(tpPoint);
            stater.SinrDataSub.AddDataSub(sinr, stater.TPCount);
            float? rsrq = NRTpHelper.NrTpManager.GetSCellRsrq(tpPoint);
            stater.RsrqDataSub.AddDataSub(rsrq, stater.TPCount);
            double distance = MathFuncs.GetDistance(nodeb.Longitude, nodeb.Latitude, tpPoint.Longitude, tpPoint.Latitude);
            stater.TPCount = stater.DistanceDataSub.AddDataSub(distance, stater.TPCount);

            if (RegionBtsCntDic.ContainsKey(regionName))
            {
                RegionBtsCntDic[regionName]++;
            }
            else
            {
                RegionBtsCntDic[regionName] = 1;
            }
        }

        protected virtual void addUnknownCell(string regionName, TestPoint tpPoint)
        {
            Dictionary<string, CellStater> unknownCellStaterMap;
            if (!RegionUnknowCellDic.TryGetValue(regionName, out unknownCellStaterMap))
            {
                unknownCellStaterMap = new Dictionary<string, CellStater>();
                RegionUnknowCellDic.Add(regionName, unknownCellStaterMap);
            }

            CellStater stater;
            string key = getCellToken(tpPoint);
            if (unknownCellStaterMap.ContainsKey(key))
            {
                stater = unknownCellStaterMap[key];
            }
            else
            {
                stater = getCellStater();
                unknownCellStaterMap[key] = stater;
            }

            float? rsrp = NRTpHelper.NrTpManager.GetSCellRsrp(tpPoint);
            stater.RsrpDataSub.AddDataSub(rsrp, stater.TPCount);
            float? sinr = NRTpHelper.NrTpManager.GetSCellSinr(tpPoint);
            stater.SinrDataSub.AddDataSub(sinr, stater.TPCount);
            float? rsrq = NRTpHelper.NrTpManager.GetSCellRsrq(tpPoint);
            stater.RsrqDataSub.AddDataSub(rsrq, stater.TPCount);
            stater.TPCount = stater.DistanceDataSub.AddDataSub(0d, stater.TPCount);

            if (RegionTestPointCnt.ContainsKey(regionName))
            {
                RegionTestPointCnt[regionName]++;
            }
            else
            {
                RegionTestPointCnt[regionName] = 1;
            }
        }
        #endregion

        //protected virtual void addUnknownNodeB(string regionName, TestPoint tpPoint)
        //{
        //    Dictionary<string, NRBtsStater> unknownBtsStaterMap;
        //    if (!RegionUnknowBTSDic.TryGetValue(regionName, out unknownBtsStaterMap))
        //    {
        //        unknownBtsStaterMap = new Dictionary<string, NRBtsStater>();
        //        RegionUnknowBTSDic.Add(regionName, unknownBtsStaterMap);
        //    }
        //    NRBtsStater stater;

        //    //对于无法匹配的小区，默认CI去掉最后一位数字为基站标识，如23456-->2345
        //    string CI = tpPoint[MainModel.LTE_ECI].ToString();
        //    string btsMark = CI.Substring(0, CI.Length - 1);
        //    if (btsMark.Length == 0)
        //    {
        //        btsMark = CI;
        //    }

        //    string key = tpPoint[MainModel.LTE_TAC].ToString() + "_" + btsMark;
        //    if (unknownBtsStaterMap.ContainsKey(key))
        //    {
        //        stater = unknownBtsStaterMap[key];
        //    }
        //    else
        //    {
        //        stater = new NRBtsStater();
        //        unknownBtsStaterMap[key] = stater;
        //    }

        //    float? rsrp = NRTpHelper.NrTpManager.GetSCellRsrp(tpPoint);
        //    stater.RsrpDataSub.AddDataSub(rsrp, stater.TestPointCount);
        //    float? sinr = NRTpHelper.NrTpManager.GetSCellSinr(tpPoint);
        //    stater.SinrDataSub.AddDataSub(sinr, stater.TestPointCount);
        //    float? rsrq = (float?)tpPoint["NR_SS_RSRQ"];
        //    stater.RsrqDataSub.AddDataSub(rsrq, stater.TestPointCount);
        //    stater.TestPointCount = stater.DistanceDataSub.AddDataSub(0d, stater.TestPointCount);
        //    if (RegionBtsCntDic.ContainsKey(regionName))
        //    {
        //        RegionBtsCntDic[regionName]++;
        //    }
        //    else
        //    {
        //        RegionBtsCntDic[regionName] = 1;
        //    }
        //}
    }

    public class NRCellSetRegionData : CellSetRegionDataBase
    {
        protected override string getCellToken(TestPoint tp)
        {
            int? nrEarfcn = (int?)NRTpHelper.NrTpManager.GetEARFCN(tp);
            int? nrPci = (int?)NRTpHelper.NrTpManager.GetPCI(tp);
            string key = nrEarfcn.ToString() + "_" + nrPci.ToString();
            return key;
        }

        protected override CellStater getCellStater()
        {
            return new NRCellStater();
        }

        protected override BtsStater getBtsStater()
        {
            return new NRBtsStater();
        }

        public override void DoWithData(TestPoint tpPoint)
        {
            string regionName = getRegionName(tpPoint);
            CellSetResultBase.SNCellGatherInfo snCellInfo = new CellSetResultBase.SNCellGatherInfo();
            SNCellList.Add(snCellInfo);

            dealNCellData(tpPoint, regionName, snCellInfo);

            dealMainCellData(tpPoint, regionName, snCellInfo);
        }

        private void dealNCellData(TestPoint tpPoint, string regionName, CellSetResultBase.SNCellGatherInfo snCellInfo)
        {
            for (int i = 0; i < 12; i++)
            {
                NRTpHelper.NRNCellType type = NRTpHelper.NrTpManager.GetNCellType(tpPoint, i);
                if (type == NRTpHelper.NRNCellType.NCELL)
                {
                    int? nArfcn = (int?)NRTpHelper.NrTpManager.GetNEARFCN(tpPoint, i);
                    int? nCpi = (int?)NRTpHelper.NrTpManager.GetNPCI(tpPoint, i);
                    float? nRSRP = NRTpHelper.NrTpManager.GetNCellRsrp(tpPoint, i);
                    if (nArfcn == null || nCpi == null || nRSRP == null)
                    {
                        continue;
                    }

                    NRCell nCell = tpPoint.GetNBCell_NR(i);
                    if (nCell != null)
                    {
                        if (MainModel.GetInstance().SystemConfigInfo.distLimit
                            && nCell.GetDistance(tpPoint.Longitude, tpPoint.Latitude) >= CD.MAX_COV_DISTANCE_NR)
                        {
                            continue;
                        }
                        addNBCell(regionName, nCell, nRSRP);
                        snCellInfo.NCells.Add(nCell);
                    }
                }
            }
        }

        private void dealMainCellData(TestPoint tpPoint, string regionName, CellSetResultBase.SNCellGatherInfo snCellInfo)
        {
            int? nrEarfcn = (int?)NRTpHelper.NrTpManager.GetEARFCN(tpPoint);
            int? nrPci = (int?)NRTpHelper.NrTpManager.GetPCI(tpPoint);
            if (!isValidData(tpPoint))
            {
                return;
            }
            NRCell cell = tpPoint.GetMainCell_NR();
            if (cell != null)
            {
                if (MainModel.GetInstance().SystemConfigInfo.distLimit
                    && cell.GetDistance(tpPoint.Longitude, tpPoint.Latitude) >= CD.MAX_COV_DISTANCE_NR)
                {
                    return;
                }
                addCell(regionName, cell, tpPoint);
                addNodeB(regionName, cell.BelongBTS, tpPoint);
                snCellInfo.SCell = cell;
            }
            else if (nrEarfcn != null && nrPci != null)
            {
                addUnknownCell(regionName, tpPoint);
            }
        }

        public override void Stat(List<string> selectedRegionNames, QueryCondition cond)
        {
            NRCellSetResultNR result = new NRCellSetResultNR();
            result.DealEarfcnCell(RegionCellDic, selectedRegionNames);
            result.DealCell(RegionCellDic, RegionUnknowCellDic, RegionTestPointCnt, selectedRegionNames, cond);
            result.DealBts(RegionBTSDic, RegionBtsCntDic, selectedRegionNames);
            result.DealNbCell(RegionNbCellDic, SNCellList, selectedRegionNames);

            Result = result;
        }
    }
}
