﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.Util;

namespace MasterCom.RAMS.Model
{
    public class CellSign : Snapshot<CellSign>
    {
        protected string code = string.Empty;
        /// <summary>
        /// 小区号
        /// </summary>
        public string Code
        {
            get { return code; }
        }
        protected int signID = -1;
        /// <summary>
        /// 自维护ID
        /// </summary>
        public int SignID
        {
            get { return signID; }
        }

      

    }
}
