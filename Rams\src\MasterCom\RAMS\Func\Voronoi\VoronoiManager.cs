﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Drawing;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Func.Voronoi;
using MasterCom.MTGis;

namespace MasterCom.RAMS.Func
{
    /// <summary>
    /// 泰森多边形的泛型构建器
    /// </summary>
    /// <typeparam name="T"></typeparam>
    public class VoronoiManager<T> where T: IVoronoi
    {
        public delegate bool VoronoiFilter(T vertex, MapOperation2 mop2);

        public static VoronoiManager<T> GetInstance()
        {
            if (instance == null)
            {
                instance = new VoronoiManager<T>();
            }
            return instance;
        }

        public string LastErrorText
        {
            get;
            private set;
        }

        public Dictionary<T, List<Vertex[]>> Construct(
            IEnumerable<T> points, 
            VoronoiFilter voiFilter,
            bool isShowProgress)
        {
            EntryPoint(points, voiFilter, isShowProgress);
            if (this.isShowProgress)
            {
                WaitBox.Show("", Construct);
            }
            else
            {
                Construct();
            }
            return ExitPoint();
        }

        /// <summary>
        /// 构建泰森多边形的过程
        /// </summary>
        private void Construct()
        {
            doConstruct();
            if (isShowProgress)
            {
                System.Threading.Thread.Sleep(500);
                WaitBox.Close();
            }
        }

        private void doConstruct()
        {
            try
            {
                // 获取系统配置,边界设置,数据转换
                List<Vertex> vertexList = null;
                if (!GetCurVoronoiConfig() || !SetVoiBorder() || !BuildDataMap(out vertexList))
                {
                    return;
                }

                if (isShowProgress)
                {
                    WaitBox.Text = "正在构建泰森多边形...";
                    WaitBox.ProgressPercent = 90;
                }

                // 构建过程
                voiBuilder.MaxAngle = voiConfig.MaxAngle;
                voiBuilder.ClipAllLessCount = voiConfig.ClipAllLessCount;
                Dictionary<Vertex, List<Vertex[]>> resultDict = voiBuilder.Construct(vertexList, voiBorder);
                if (voiBuilder.IsErrorOccur)
                {
                    LastErrorText = voiBuilder.ErrorText;
                    return;
                }

                // 解释返回结果
                ParseResult(resultDict);
            }
            catch (Exception ex)
            {
                LastErrorText = ex.Message;
            }
        }

        /// <summary>
        /// 切割边界
        /// </summary>
        /// <returns></returns>
        private bool SetVoiBorder()
        {
            if (isShowProgress)
            {
                WaitBox.Text = "正在处理切割边界...";
                WaitBox.ProgressPercent = 90;
            }

            voiBorder.ReserveCount = voiConfig.BorderReserveCount;
            if (MainModel.GetInstance().SearchGeometrys.IsSelectRegion())
            {
                voiBorder.SetBorder(MainModel.GetInstance().SearchGeometrys.Region);
            }
            else if (voiConfig.DefaultBorderPath == null || voiConfig.DefaultBorderPath == "")
            {
                LastErrorText = "默认切割边界未有系统配置";
                return false;
            }
            else
            {
                voiBorder.SetBorder(voiConfig.DefaultBorderPath);
            }

            if (voiBorder.IsErrorOccur)
            {
                LastErrorText = voiBorder.ErrorText;
                return false;
            }

            return true;
        }

        /// <summary>
        /// 数据过滤和映射
        /// </summary>
        /// <param name="vertexList">经过去重复的顶点列表，用于VoronoiBuilder.Construct参数</param>
        /// <returns></returns>
        private bool BuildDataMap(out List<Vertex> vertexList)
        {
            if (isShowProgress)
            {
                WaitBox.Text = "正在查找区域中的基站...";
            }

            // 按条件过滤
            vertexList = new List<Vertex>();
            foreach (T t in origPointList)
            {
                if (!voiFilter(t, voiBorder.BorderMop2))
                {
                    continue;
                }
                Vertex v = new Vertex(t.VertexX, t.VertexY);
                voiToVertexDict.Add(t, v);
                vertexList.Add(v);
            }

            // 去重复
            vertexList.Sort();
            for (int i = 1; i < vertexList.Count; ++i)
            {
                if (vertexList[i] == vertexList[i - 1])
                {
                    vertexList.RemoveAt(i);
                    --i;
                }
            }

            if (vertexList.Count < 3)
            {
                LastErrorText = "输入点少于三个";
                return false;
            }

            return true;
        }

        /// <summary>
        /// 解析构建返回结果
        /// </summary>
        /// <param name="btsToVertex"></param>
        /// <param name="retDict"></param>
        private void ParseResult(Dictionary<Vertex, List<Vertex[]>> resultDict)
        {
            if (isShowProgress)
            {
                WaitBox.Text = "正在解释泰森多边形结果...";
            }
            int loop = 0;
            foreach (T t in voiToVertexDict.Keys)
            {
                if (isShowProgress)
                {
                    WaitBox.ProgressPercent = (++loop) * 100 / voiToVertexDict.Count;
                }

                Vertex vkey = voiToVertexDict[t];
                retDict.Add(t, resultDict[vkey]);
            }
        }

        /// <summary>
        /// 读取系统默认配置
        /// </summary>
        /// <returns></returns>
        private bool GetCurVoronoiConfig()
        {
            MainModel mainModel = MainModel.GetInstance();
            int districtID = mainModel.DistrictID;
            string districtName = DistrictManager.GetInstance().getDistrictName(districtID);
            foreach (VoronoiConfigInfo vinfo in mainModel.SystemConfigInfo.VoiInfoList)
            {
                if (vinfo.DistrictName == districtName)
                {
                    voiConfig = vinfo;
                    return true;
                }
            }
            LastErrorText = "未找到当前地市的系统配置: " + districtName;
            return false;
        }

        /// <summary>
        /// 单实例的初始化
        /// </summary>
        /// <param name="points"></param>
        /// <param name="voiFilter"></param>
        /// <param name="isShowProgress"></param>
        private void EntryPoint(IEnumerable<T> points, VoronoiFilter voiFilter, bool isShowProgress)
        {
            this.origPointList = points;
            this.isShowProgress = isShowProgress;
            this.voiFilter = voiFilter;
            voiConfig = null;
            voiBorder = new VoronoiBorder();
            voiBuilder = new VoronoiBuilder();
            voiToVertexDict = new Dictionary<T, Vertex>();
            retDict = new Dictionary<T, List<Vertex[]>>();
            LastErrorText = "";
        }

        /// <summary>
        /// 数据清理
        /// </summary>
        /// <returns></returns>
        private Dictionary<T, List<Vertex[]>> ExitPoint()
        {
            isShowProgress = false;
            origPointList = null;
            voiToVertexDict = null;
            voiFilter = null;
            voiBorder = null;
            voiBuilder = null;
            voiConfig = null;

            Dictionary<T, List<Vertex[]>> tmpRetDict = retDict;
            retDict = null;

            return LastErrorText == "" ? tmpRetDict : null;
        }

        private VoronoiManager()
        {
        }

        private static VoronoiManager<T> instance;
        private bool isShowProgress;
        private IEnumerable<T> origPointList;
        private VoronoiBuilder voiBuilder;
        private VoronoiBorder voiBorder;
        private VoronoiFilter voiFilter;
        private VoronoiConfigInfo voiConfig;
        private Dictionary<T, Vertex> voiToVertexDict;
        private Dictionary<T, List<Vertex[]>> retDict;
    }
}
