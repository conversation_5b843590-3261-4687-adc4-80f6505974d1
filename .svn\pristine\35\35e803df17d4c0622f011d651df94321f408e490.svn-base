﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.Util;
using System.IO;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTDeviceManageDlg : BaseDialog
    {
        public ZTDeviceManageDlg()
        {
            InitializeComponent();
        }

        private Dictionary<string, DeviceManageInfo> deviceInfoDic = null;
        public Dictionary<string, DeviceManageInfo> DeviceInfoDic
        {
            get { return deviceInfoDic; }
        }

        protected override void setCurFuncLogItem(object sender, EventArgs e)
        {
            LogItemSrc = new UserMng.LogInfoItem(2, 18000, 18050, "设备信息管理");
            UserMng.ExportFuncResultManager.GetInstance().SetCurLogItem(LogItemSrc);
        }

        protected override void clearCurFuncLogItem(object sender, EventArgs e)
        {
            UserMng.ExportFuncResultManager.GetInstance().SetCurLogItem(null);
        }

        private void btnSearch_Click(object sender, EventArgs e)
        {
            OpenFileDialog openDlg = new OpenFileDialog();
            openDlg.Filter = "Excel|*.xlsx;*.xls";
            openDlg.Title = "请选择设备信息表";
            if (openDlg.ShowDialog() == DialogResult.OK)
            {
                txtBox.Text = openDlg.FileName;
            }
        }

        private void ImportDeviceInfo(object obj)
        { 
            //读取Excel
            List<DeviceManageInfo> importDeviceInfolist = readZWExcel(obj);
            //插入到数据库
            DiyInsertDeviceManageInfo query = new DiyInsertDeviceManageInfo(importDeviceInfolist);
            query.Query();

            WaitBox.Close();
        }

        private List<DeviceManageInfo> readZWExcel(object obj)
        {
            List<DeviceManageInfo> importDeviceInfolist = new List<DeviceManageInfo>();
            string fileName = obj as string;
            WaitBox.ProgressPercent = 10;
            DataSet ds = ExcelNPOIManager.ImportFromExcel(fileName);
            if (ds != null && ds.Tables != null && ds.Tables.Count > 0)
            {
                try
                {
                    foreach (DataTable dt in ds.Tables)
                    {
                        WaitBox.Text = "正在读取设备信息..." + dt.TableName;
                        WaitBox.ProgressPercent += 40;
                        foreach (DataRow dr in dt.Rows)
                        {
                            DeviceManageInfo deviceInfo = new DeviceManageInfo();
                            deviceInfo.FillDataByExcel(dr);
                            importDeviceInfolist.Add(deviceInfo);
                        }
                    }

                    if (importDeviceInfolist.Count <= 0)
                    {
                        WaitBox.Text = "没有设备信息或读取设备息表失败！";
                        System.Threading.Thread.Sleep(1000);
                        return new List<DeviceManageInfo>();
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show(ex.Message + Environment.NewLine + ex.Source + Environment.NewLine + ex.StackTrace);
                    return new List<DeviceManageInfo>();
                }
            }
            WaitBox.Text = "读取完毕...";
            return importDeviceInfolist;
        }

        private void btnImport_Click(object sender, EventArgs e)
        {
            //每次导入的可能只是部分数据,更新重复的部分,插入新的部分
            string file = txtBox.Text;
            if (!File.Exists(file))
            {
                MessageBox.Show(file + "\r\n不存在！", "提示");
                return;
            }
            
            WaitBox.Show("正在读取并导入设备信息...", ImportDeviceInfo, file);
        }

        private void btnDownLoad_Click(object sender, EventArgs e)
        {
            List<ExportToExcelModel> lsData = new List<ExportToExcelModel>();
            ExportToExcelModel sheetDevice = new ExportToExcelModel();
            sheetDevice.SheetName = "设备信息表";
            List<NPOIRow> rows = new List<NPOIRow>();
            NPOIRow nr = new NPOIRow();
            nr.AddCellValue("BOX_ID");
            nr.AddCellValue("厂商");
            nr.AddCellValue("软件版本");
            nr.AddCellValue("下发版本");
            nr.AddCellValue("所属域");
            nr.AddCellValue("操作");
            nr.AddCellValue("状态");
            rows.Add(nr);

            nr = new NPOIRow();
            nr.AddCellValue("1871181");
            nr.AddCellValue("鼎利");
            nr.AddCellValue("35");
            nr.AddCellValue("35");
            nr.AddCellValue("文山");
            nr.AddCellValue("可用");
            nr.AddCellValue("在线");
            rows.Add(nr);

            sheetDevice.Data = rows;
            lsData.Add(sheetDevice);
            MasterCom.Util.ExcelNPOIManager.ExportToExcelMore(lsData);
        }

        private void btnManage_Click(object sender, EventArgs e)
        {
            //先查询所有数据
            DiyQueryDeviceManageInfo query = new DiyQueryDeviceManageInfo();
            query.Query();
            //进入数据管理界面对数据进行修改
            ZTDeviceManageForm manageForm = new ZTDeviceManageForm();
            manageForm.FillData(query.DeviceInfoList);
            manageForm.ShowDialog();
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            //重新查询修改后的数据
            DiyQueryDeviceManageInfo query = new DiyQueryDeviceManageInfo();
            query.Query();
            deviceInfoDic = query.DeviceInfoDic;

            this.DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }
    }
}
