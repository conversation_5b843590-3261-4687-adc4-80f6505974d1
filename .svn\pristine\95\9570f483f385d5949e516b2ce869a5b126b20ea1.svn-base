﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Net;
using DevExpress.XtraGrid.Columns;
using MasterCom.Util;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class RoadProblemForm : MinCloseForm
    {
        MapForm mapForm;
        int dataType = 0;
        public RoadProblemForm(MainModel mainModel)
            : base(mainModel)
        {
            InitializeComponent();
            this.mapForm = mainModel.MainForm.GetMapForm();
            DisposeWhenClose = true;

            cbxStatus.Properties.Items.Clear();
            cbxStatus.Properties.Items.Add("全部");
            cbxStatus.Properties.Items.Add("已创建");
            cbxStatus.Properties.Items.Add("已关闭");
            cbxStatus.SelectedIndex = 1;

            cbxLevel.Properties.Items.Clear();
            cbxLevel.Properties.Items.Add("1");
            cbxLevel.Properties.Items.Add("2");
            cbxLevel.Properties.Items.Add("3");
            cbxLevel.Properties.Items.Add("4");
            cbxLevel.Properties.Items.Add("5");
            cbxLevel.Properties.Items[0].CheckState = CheckState.Checked;
            cbxLevel.Properties.Items[1].CheckState = CheckState.Checked;

            cbxAreaName.Properties.Items.Clear();
            cbxAreaName.Properties.Items.Add("全部");
            cbxAreaName.Properties.Items.Add("宝安东");
            cbxAreaName.Properties.Items.Add("宝安南");
            cbxAreaName.Properties.Items.Add("宝安西");
            cbxAreaName.Properties.Items.Add("宝安中");
            cbxAreaName.Properties.Items.Add("福田");
            cbxAreaName.Properties.Items.Add("龙岗北");
            cbxAreaName.Properties.Items.Add("龙岗南");
            cbxAreaName.Properties.Items.Add("龙岗西");
            cbxAreaName.SelectedIndex = 0;
        }

        public void FillData(int dataType, int status)
        {
            this.dataType = dataType;
            cbxStatus.SelectedIndex = status;
            initGridControlGridKPI();
            refreshData();
        }

        private void initGridControlGridKPI()
        {
            gridViewRoadKPI.Columns.Clear();
            if (dataType == 0)  //GSM
            {
                GridColumn gridColumnMonth = new GridColumn();
                gridColumnMonth.Caption = "月份";
                gridColumnMonth.FieldName = "Month";
                gridColumnMonth.Name = "gridColumnMonth";
                gridColumnMonth.Visible = true;
                gridColumnMonth.VisibleIndex = 0;
                gridColumnMonth.Width = 120;

                GridColumn gridColumnIsPoor = new GridColumn();
                gridColumnIsPoor.Caption = "是否质差";
                gridColumnIsPoor.FieldName = "IsPoorString";
                gridColumnIsPoor.Name = "gridColumnIsPoor";
                gridColumnIsPoor.Visible = true;
                gridColumnIsPoor.VisibleIndex = 1;

                GridColumn gridColumnRxQualTotal = new GridColumn();
                gridColumnRxQualTotal.Caption = "RxQual采样点数";
                gridColumnRxQualTotal.FieldName = "RxQualTotal";
                gridColumnRxQualTotal.Name = "gridColumnRxQualTotal";
                gridColumnRxQualTotal.Visible = true;
                gridColumnRxQualTotal.VisibleIndex = 2;
                gridColumnRxQualTotal.Width = 120;

                GridColumn gridColumnRxQual0_4Pct = new GridColumn();
                gridColumnRxQual0_4Pct.Caption = "RxQual(0-4)占比";
                gridColumnRxQual0_4Pct.FieldName = "RxQual0_4Pct";
                gridColumnRxQual0_4Pct.Name = "gridColumnRxQual0_4Pct";
                gridColumnRxQual0_4Pct.Visible = true;
                gridColumnRxQual0_4Pct.VisibleIndex = 3;
                gridColumnRxQual0_4Pct.Width = 120;

                GridColumn gridColumnRxQual5Pct = new GridColumn();
                gridColumnRxQual5Pct.Caption = "RxQual(5)占比";
                gridColumnRxQual5Pct.FieldName = "RxQual5Pct";
                gridColumnRxQual5Pct.Name = "gridColumnRxQual5Pct";
                gridColumnRxQual5Pct.Visible = true;
                gridColumnRxQual5Pct.VisibleIndex = 4;
                gridColumnRxQual5Pct.Width = 100;

                GridColumn gridColumnRxQual6_7Pct = new GridColumn();
                gridColumnRxQual6_7Pct.Caption = "RxQual(6-7)占比";
                gridColumnRxQual6_7Pct.FieldName = "RxQual6_7Pct";
                gridColumnRxQual6_7Pct.Name = "gridColumnRxQual6_7Pct";
                gridColumnRxQual6_7Pct.Visible = true;
                gridColumnRxQual6_7Pct.VisibleIndex = 5;
                gridColumnRxQual6_7Pct.Width = 120;

                GridColumn gridColumnMosTotal = new GridColumn();
                gridColumnMosTotal.Caption = "Mos采样点数";
                gridColumnMosTotal.FieldName = "MosTotal";
                gridColumnMosTotal.Name = "gridColumnMosTotal";
                gridColumnMosTotal.Visible = true;
                gridColumnMosTotal.VisibleIndex = 6;
                gridColumnMosTotal.Width = 100;

                GridColumn gridColumnMos2d8Pct = new GridColumn();
                gridColumnMos2d8Pct.Caption = "Mos2.8占比";
                gridColumnMos2d8Pct.FieldName = "Mos2d8Pct";
                gridColumnMos2d8Pct.Name = "gridColumnMos2d8Pct";
                gridColumnMos2d8Pct.Visible = true;
                gridColumnMos2d8Pct.VisibleIndex = 7;
                gridColumnMos2d8Pct.Width = 100;

                gridViewRoadKPI.Columns.AddRange(new GridColumn[] { gridColumnMonth, gridColumnIsPoor, 
                    gridColumnRxQualTotal, gridColumnRxQual0_4Pct, gridColumnRxQual5Pct, gridColumnRxQual6_7Pct, 
                    gridColumnMosTotal, gridColumnMos2d8Pct});
            }
            else if (dataType == 1)  //TD
            {
                GridColumn gridColumnMonth = new GridColumn();
                gridColumnMonth.Caption = "月份";
                gridColumnMonth.FieldName = "Month";
                gridColumnMonth.Name = "gridColumnMonth";
                gridColumnMonth.Visible = true;
                gridColumnMonth.VisibleIndex = 0;
                gridColumnMonth.Width = 120;

                GridColumn gridColumnIsPoor = new GridColumn();
                gridColumnIsPoor.Caption = "是否弱覆盖";
                gridColumnIsPoor.FieldName = "IsPoorString";
                gridColumnIsPoor.Name = "gridColumnIsPoor";
                gridColumnIsPoor.Visible = true;
                gridColumnIsPoor.VisibleIndex = 1;
                gridColumnIsPoor.Width = 120;

                GridColumn gridColumnRSCPTotal = new GridColumn();
                gridColumnRSCPTotal.Caption = "RSCP采样点数";
                gridColumnRSCPTotal.FieldName = "RSCPTotal";
                gridColumnRSCPTotal.Name = "gridColumnRSCPTotal";
                gridColumnRSCPTotal.Visible = true;
                gridColumnRSCPTotal.VisibleIndex = 2;
                gridColumnRSCPTotal.Width = 120;

                GridColumn gridColumnRSCP85Pct = new GridColumn();
                gridColumnRSCP85Pct.Caption = "RSCP85占比";
                gridColumnRSCP85Pct.FieldName = "RSCP85Pct";
                gridColumnRSCP85Pct.Name = "gridColumnRSCP85Pct";
                gridColumnRSCP85Pct.Visible = true;
                gridColumnRSCP85Pct.VisibleIndex = 3;
                gridColumnRSCP85Pct.Width = 120;

                gridViewRoadKPI.Columns.AddRange(new GridColumn[] { gridColumnMonth, gridColumnIsPoor, 
                    gridColumnRSCPTotal, gridColumnRSCP85Pct});
            }
        }

        private void refreshData()
        {
            MainModel.CurRoadProblemDic.Clear();
            BindingSource source = new BindingSource();
            foreach (RoadProblem road in MainModel.AllRoadProblemsDic.Values)
            {
                if (filterCondtion(road))
                {
                    MainModel.CurRoadProblemDic[road.ID] = road;
                }
            }
            source.DataSource = MainModel.CurRoadProblemDic.Values;
            gridControlRoad.DataSource = source;
            gridControlRoad.RefreshDataSource();
        }

        private bool filterCondtion(RoadProblem road)
        {
            if (cbxStatus.SelectedIndex != 0)
            {
                if (cbxStatus.SelectedIndex == 1 && road.Status != 0)
                {
                    return false;
                }
                if (cbxStatus.SelectedIndex == 2 && road.Status != 1)
                {
                    return false;
                }
            }
            string selectLevelsString = cbxLevel.Properties.GetCheckedItems().ToString();
            if (selectLevelsString.Length > 0)
            {
                string[] sLevelArr = selectLevelsString.Split(',');
                List<int> levels = getLevels(sLevelArr);
                if (!levels.Contains(road.Level))
                {
                    return false;
                }
            }
            if (cbxAreaName.Text.Trim() != "" && cbxAreaName.Text.Trim() != "全部" && road.AreaName != cbxAreaName.Text.Trim())
            {
                return false;
            }
            if (edtRoadName.Text.Trim() != "" && !road.RoadName.Contains(edtRoadName.Text.Trim()))
            {
                return false;
            }
            return true;
        }

        private static List<int> getLevels(string[] sLevelArr)
        {
            List<int> levels = new List<int>();
            foreach (string sLevel in sLevelArr)
            {
                int level;
                if (int.TryParse(sLevel.Trim(), out level))
                {
                    levels.Add(level);
                }
            }

            return levels;
        }

        private void btnQuery_Click(object sender, EventArgs e)
        {
            refreshData();
        }

        private void gridViewGrid_FocusedRowChanged(object sender, DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventArgs e)
        {
            if (gridViewRoad.SelectedRowsCount > 0)
            {
                RoadProblem road = gridViewRoad.GetRow(gridViewRoad.GetSelectedRows()[0]) as RoadProblem;
                BindingSource source = new BindingSource();
                if (!road.bQueryedKPI)
                {
                    DIYQueryRoadProblemInfo.GetInstance().dataType = dataType;
                    DIYQueryRoadProblemInfo.GetInstance().year = road.BeginYear;
                    DIYQueryRoadProblemInfo.GetInstance().batch = road.BeginBatch;
                    DIYQueryRoadProblemInfo.GetInstance().areaName = road.AreaName;
                    DIYQueryRoadProblemInfo.GetInstance().level = road.Level;
                    DIYQueryRoadProblemInfo.GetInstance().roadName = road.RoadName;
                    DIYQueryRoadProblemInfo.GetInstance().Query();
                    road.RoadKPIMonthList = DIYQueryRoadProblemInfo.GetInstance().RoadKPIList;
                    road.bQueryedKPI = true;
                }
                if (road.RoadKPIMonthList[0] is RoadKPIMonthGSM)
                {
                    List<RoadKPIMonthGSM> roadList = new List<RoadKPIMonthGSM>();
                    foreach (RoadKPIMonth roadKPI in road.RoadKPIMonthList)
                    {
                        roadList.Add(roadKPI as RoadKPIMonthGSM);
                    }
                    source.DataSource = roadList;
                }
                else if (road.RoadKPIMonthList[0] is RoadKPIMonthTD)
                {
                    List<RoadKPIMonthTD> roadList = new List<RoadKPIMonthTD>();
                    foreach (RoadKPIMonth roadKPI in road.RoadKPIMonthList)
                    {
                        roadList.Add(roadKPI as RoadKPIMonthTD);
                    }
                    source.DataSource = roadList;
                }
                gridControlRoadKPI.DataSource = source;
                gridControlRoadKPI.RefreshDataSource();
            }
        }

        private void miExportToExcel_Click(object sender, EventArgs e)
        {
            if (gridControlRoad.Focused)
            {
                ExcelNPOIManager.ExportToExcel(gridViewRoad);
            }
            else if (gridControlRoadKPI.Focused)
            {
                ExcelNPOIManager.ExportToExcel(gridViewRoadKPI);
            }
            //return;
            //List<NPOIRow> rows = new List<NPOIRow>();
            //NPOIRow row = new NPOIRow();
            //row.AddCellValue("问题点编号");
            //row.AddCellValue("栅格编号");
            //row.AddCellValue("状态");
            //row.AddCellValue("权重");
            //row.AddCellValue("创建年份");
            //row.AddCellValue("创建轮次");
            //row.AddCellValue("关闭年份");
            //row.AddCellValue("关闭轮次");
            //row.AddCellValue("最后异常年份");
            //row.AddCellValue("最后异常轮次");
            //row.AddCellValue("验证正常次数");
            //row.AddCellValue("验证测试状态");
            //row.AddCellValue("栅格问题次数");
            //row.AddCellValue("中心经度");
            //row.AddCellValue("中心纬度");
            //row.AddCellValue("网格");
            //row.AddCellValue("片区");
            //row.AddCellValue("代维分区");
            ////if (dataType == 0)
            ////{
            ////    row.AddCellValue("年份");
            ////    row.AddCellValue("轮次");
            ////    row.AddCellValue("是否质差");
            ////    row.AddCellValue("RxQual采样点数");
            ////    row.AddCellValue("RxQual(0-4)占比");
            ////    row.AddCellValue("RxQual(5)占比");
            ////    row.AddCellValue("RxQual(6-7)占比");
            ////    row.AddCellValue("Mos采样点数");
            ////    row.AddCellValue("Mos2.8占比");
            ////}
            ////else
            ////{

            ////}
            //rows.Add(row);
            //foreach (GridProblem grid in MainModel.CurGridProblemDic.Values)
            //{
            //    row = new NPOIRow();
            //    row.AddCellValue(grid.ID);
            //    row.AddCellValue(grid.NetGridID);
            //    row.AddCellValue(grid.StatusString);
            //    row.AddCellValue(grid.Weight);
            //    row.AddCellValue(grid.CreatedYear);
            //    row.AddCellValue(grid.CreatedBatch);
            //    row.AddCellValue(grid.ClosedYear);
            //    row.AddCellValue(grid.ClosedBatch);
            //    row.AddCellValue(grid.LastAbnormalYear);
            //    row.AddCellValue(grid.LastAbnormalBatch);
            //    row.AddCellValue(grid.GoodDaysCount);
            //    row.AddCellValue(grid.ValidateStatusString);
            //    row.AddCellValue(grid.GridRepeatCount);
            //    row.AddCellValue(grid.MidLong);
            //    row.AddCellValue(grid.MidLat); ;
            //    row.AddCellValue(grid.AreaATUGridID);
            //    row.AddCellValue(grid.AreaOpt);
            //    row.AddCellValue(grid.AreaAgent);
            //    //foreach (GridKPIMonth gridKPI in grid.GridKPIMonthList)
            //    //{
            //    //    NPOIRow subRow = new NPOIRow();
            //    //    subRow.AddCellValue(gridKPI.Year);
            //    //    subRow.AddCellValue(gridKPI.Batch);
            //    //    subRow.AddCellValue(gridKPI.IsPoorString);
            //    //    if (dataType == 0)
            //    //    {
            //    //        GridKPIMonthGSM gridKPIGSM = gridKPI as GridKPIMonthGSM;
            //    //        subRow.AddCellValue(gridKPIGSM.RxQualTotal);
            //    //        subRow.AddCellValue(gridKPIGSM.RxQual0_4Pct);
            //    //        subRow.AddCellValue(gridKPIGSM.RxQual5Pct);
            //    //        subRow.AddCellValue(gridKPIGSM.RxQual6_7Pct);
            //    //        subRow.AddCellValue(gridKPIGSM.MosTotal);
            //    //        subRow.AddCellValue(gridKPIGSM.Mos2d8Pct);
            //    //    }
            //    //    else
            //    //    {

            //    //    }
            //    //    row.AddSubRow(subRow);
            //    //}
            //    rows.Add(row);
            //}
            //ExcelNPOIManager.ExportToExcel(rows);
        }

        private void gridViewGrid_DoubleClick(object sender, EventArgs e)
        {
            //if (gridViewRoad.SelectedRowsCount > 0)
            //{
            //    RoadProblem road = gridViewRoad.GetRow(gridViewRoad.GetSelectedRows()[0]) as RoadProblem;
            //    MainModel.CurGridProblem = road;
            //    MapForm mf = MainModel.MainForm.GetMapForm();
            //    if (mf != null)
            //    {
            //        mf.GoToView(road.MidLong, road.MidLat);
            //    }
            //}
        }

        private void miQuerySample_Click(object sender, EventArgs e)
        {
            if (gridViewRoadKPI.SelectedRowsCount > 0)
            {
                RoadKPIMonth road = gridViewRoadKPI.GetRow(gridViewRoadKPI.GetSelectedRows()[0]) as RoadKPIMonth;
                MapForm mf = MainModel.MainForm.GetMapForm();
                if (mf != null)
                {
                    mf.QuerySampleByStreetPreDefined(road.Year, road.Batch, 17, "网格新片区", road.AreaName, road.RoadName);
                }
            }
        }

        private void contextMenuStrip_Opening(object sender, CancelEventArgs e)
        {
            if (gridControlRoad.Focused)
            {
                miQuerySample.Visible = false;
            }
            else if (gridControlRoadKPI.Focused)
            {
                miQuerySample.Visible = true;
            }
        }
    }
}
