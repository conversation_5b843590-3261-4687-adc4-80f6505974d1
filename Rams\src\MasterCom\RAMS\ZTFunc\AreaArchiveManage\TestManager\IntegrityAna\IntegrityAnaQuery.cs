﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.ZTFunc.AreaArchiveManage;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class IntegrityAnaQuery : AreaKpiQueryBase
    {
        public IntegrityAnaQuery(MainModel mainModel)
            : base(mainModel)
        {
            isQueryEvents = false;
            areaCondition = new IntegrityTestCondition();
            anaDealer = new IntegrityAnaDealer(areaCondition as IntegrityTestCondition);
        }

        public override string Name
        {
            get { return "完整性分析"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 31000, 31009, this.Name);
        }

        protected override bool setConditionDlg()
        {
            IntegritySettingDlg dlg = new IntegritySettingDlg();
            dlg.SetCondition((IntegrityTestCondition)areaCondition);

            if (dlg.ShowDialog() == DialogResult.OK)
            {
                areaCondition = dlg.GetCondition();
                return true;
            }
            return false;
        }

        protected override List<string> getFormulas()
        {
            return new List<string> { "Mx_0801", "Tx_0801", "Wx_0801", "Cx_0801", "Ex_0801" };
        }

        protected override void fireShowForm()
        {
            IntegrityForm form = MainModel.CreateResultForm(typeof(IntegrityForm)) as IntegrityForm;
            form.FillData(anaDealer, areaSummaryMap);
            form.Show(MainModel.MainForm);
        }
    }
}
