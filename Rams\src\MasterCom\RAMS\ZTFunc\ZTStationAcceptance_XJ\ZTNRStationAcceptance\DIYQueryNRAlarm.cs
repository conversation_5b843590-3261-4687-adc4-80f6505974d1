﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTStationAcceptance_XJ
{
    public class DIYQueryNRAlarm : DIYSQLBase
    {
        public List<NRAlarmInfo> DataList { get; private set; } = new List<NRAlarmInfo>();
        readonly string btsName;
        readonly string testDayStr;
        readonly FddDatabaseSetting setting;
        public DIYQueryNRAlarm(string btsName, FddDatabaseSetting setting)
            : base()
        {
            MainDB = true;
            testDayStr = DateTime.Now.ToString("yyMMdd");
            this.btsName = btsName;
            this.setting = setting;
        }

        public override string Name
        {
            get
            {
                return "查询NR单验告警信息";
            }
        }

        protected override string getSqlTextString()
        {
            string tableName = string.Format(@"{0}.{1}.[dbo].[{2}{3}]", setting.ServerIp, setting.DbName, setting.TableNameHead, testDayStr);
            StringBuilder selectSQL = new StringBuilder();
            selectSQL.AppendFormat(@"select distinct a.告警标准名 from {0} inner join tb_xinjiang_cfg_alarm b on a.告警标准名 = b.告警名称 where 告警对象名称 = '{1}'", tableName, btsName);
            return selectSQL.ToString();
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            int idx = 0;
            E_VType[] rType = new E_VType[1];
            rType[idx] = E_VType.E_String;
            return rType;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            DataList = new List<NRAlarmInfo>();
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    NRAlarmInfo data = new NRAlarmInfo();
                    data.FillData(package);
                    DataList.Add(data);
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }
    }

    public class NRAlarmInfo
    {
        public string AlarmName { get; set; }

        public void FillData(Package package)
        {
            AlarmName = package.Content.GetParamString();
        }

        public void FillData(System.Data.DataRow dr)
        {
            AlarmName = dr["告警名称"].ToString().Trim();
        }
    }
}
