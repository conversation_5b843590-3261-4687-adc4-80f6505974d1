﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc.BTSDistanceCondition
{
    public partial class BTSDistanceCondSetDlg : BaseDialog
    {
        public BTSDistanceCondSetDlg()
        {
            InitializeComponent();

            this.btnOK.Click += btnOK_Click;
        }

        BTSCond cond = new BTSCond();
        public void SetCond(BTSCond cond)
        {
            this.cond = cond;
            this.checkEditCell.Checked = this.cond.IsQueryGSM;
            this.checkLTE.Checked = this.cond.IsQueryLTE;
            this.checkEditTDCell.Checked = this.cond.ISQueryTD;
            this.checkEditWCell.Checked = this.cond.IsQueryW;
            this.spinEditMaxDistance.Value = (decimal)this.cond.maxDistanceToCell;
        }
        public BTSCond GetCond()
        {
            return this.cond;
        }
        private void btnOK_Click(object sender, EventArgs e)
        {
            this.cond.IsQueryGSM = this.checkEditCell.Checked;
            this.cond.IsQueryLTE = this.checkLTE.Checked;
            this.cond.ISQueryTD = this.checkEditTDCell.Checked;
            this.cond.IsQueryW = this.checkEditWCell.Checked;
            this.cond.maxDistanceToCell = (double)this.spinEditMaxDistance.Value;
            this.DialogResult = System.Windows.Forms.DialogResult.OK;
        }
        
        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

    }

    public class BTSCond
    {
        public bool IsQueryGSM { get; set; } = true;
        public bool ISQueryTD { get; set; } = true;
        public bool IsQueryW { get; set; } = true;
        public bool IsQueryLTE { get; set; } = true;
        public double maxDistanceToCell { get; set; } = 1000;
    }
}
