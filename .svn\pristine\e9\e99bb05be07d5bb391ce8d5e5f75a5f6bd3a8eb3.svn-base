﻿namespace MasterCom.RAMS.ZTFunc.ZTLteTestAcceptance
{
    partial class LteTestSettingForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.label1 = new System.Windows.Forms.Label();
            this.btnFolder = new System.Windows.Forms.Button();
            this.txtFolder = new System.Windows.Forms.TextBox();
            this.btnCancel = new System.Windows.Forms.Button();
            this.btnOK = new System.Windows.Forms.Button();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.txtStandard = new System.Windows.Forms.TextBox();
            this.txtSystemInSwitch = new System.Windows.Forms.NumericUpDown();
            this.label27 = new System.Windows.Forms.Label();
            this.label29 = new System.Windows.Forms.Label();
            this.label22 = new System.Windows.Forms.Label();
            this.txtAvgSINR = new System.Windows.Forms.NumericUpDown();
            this.label23 = new System.Windows.Forms.Label();
            this.label24 = new System.Windows.Forms.Label();
            this.txtAvgRSRP = new System.Windows.Forms.NumericUpDown();
            this.label25 = new System.Windows.Forms.Label();
            this.label16 = new System.Windows.Forms.Label();
            this.txtSwitchSuccessRate = new System.Windows.Forms.NumericUpDown();
            this.label17 = new System.Windows.Forms.Label();
            this.label18 = new System.Windows.Forms.Label();
            this.txtSwitchCount = new System.Windows.Forms.NumericUpDown();
            this.label19 = new System.Windows.Forms.Label();
            this.label14 = new System.Windows.Forms.Label();
            this.txtFTPUpSpeed = new System.Windows.Forms.NumericUpDown();
            this.label15 = new System.Windows.Forms.Label();
            this.label12 = new System.Windows.Forms.Label();
            this.txtFTPDownSpeed = new System.Windows.Forms.NumericUpDown();
            this.label13 = new System.Windows.Forms.Label();
            this.label9 = new System.Windows.Forms.Label();
            this.txtVolteSuccessRate = new System.Windows.Forms.NumericUpDown();
            this.label11 = new System.Windows.Forms.Label();
            this.label7 = new System.Windows.Forms.Label();
            this.txtVolteTestCount = new System.Windows.Forms.NumericUpDown();
            this.label8 = new System.Windows.Forms.Label();
            this.label20 = new System.Windows.Forms.Label();
            this.txtCSFBSuccessRate = new System.Windows.Forms.NumericUpDown();
            this.label21 = new System.Windows.Forms.Label();
            this.label5 = new System.Windows.Forms.Label();
            this.txtCSFBTestCount = new System.Windows.Forms.NumericUpDown();
            this.label6 = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.txtAccessTestCount = new System.Windows.Forms.NumericUpDown();
            this.label2 = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.txtAccessRate = new System.Windows.Forms.NumericUpDown();
            this.label10 = new System.Windows.Forms.Label();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.txtStandard1 = new System.Windows.Forms.TextBox();
            this.label52 = new System.Windows.Forms.Label();
            this.txtSystemInSwitch1 = new System.Windows.Forms.NumericUpDown();
            this.label55 = new System.Windows.Forms.Label();
            this.label48 = new System.Windows.Forms.Label();
            this.txtAntAvgSINR = new System.Windows.Forms.NumericUpDown();
            this.label49 = new System.Windows.Forms.Label();
            this.label50 = new System.Windows.Forms.Label();
            this.txtAntAvgRSRP = new System.Windows.Forms.NumericUpDown();
            this.label51 = new System.Windows.Forms.Label();
            this.label44 = new System.Windows.Forms.Label();
            this.txtDoorUpThroughput = new System.Windows.Forms.NumericUpDown();
            this.label45 = new System.Windows.Forms.Label();
            this.label46 = new System.Windows.Forms.Label();
            this.txtDoorDownThroughput = new System.Windows.Forms.NumericUpDown();
            this.label47 = new System.Windows.Forms.Label();
            this.label40 = new System.Windows.Forms.Label();
            this.txtDoorAvgSINR = new System.Windows.Forms.NumericUpDown();
            this.label41 = new System.Windows.Forms.Label();
            this.label42 = new System.Windows.Forms.Label();
            this.txtDoorAvgRSRP = new System.Windows.Forms.NumericUpDown();
            this.label43 = new System.Windows.Forms.Label();
            this.label36 = new System.Windows.Forms.Label();
            this.txtFTPUpSpeed1 = new System.Windows.Forms.NumericUpDown();
            this.label37 = new System.Windows.Forms.Label();
            this.label38 = new System.Windows.Forms.Label();
            this.txtFTPDownSpeed1 = new System.Windows.Forms.NumericUpDown();
            this.label39 = new System.Windows.Forms.Label();
            this.label32 = new System.Windows.Forms.Label();
            this.txtVolteSuccessRate1 = new System.Windows.Forms.NumericUpDown();
            this.label33 = new System.Windows.Forms.Label();
            this.label34 = new System.Windows.Forms.Label();
            this.txtVolteTestCount1 = new System.Windows.Forms.NumericUpDown();
            this.label35 = new System.Windows.Forms.Label();
            this.label26 = new System.Windows.Forms.Label();
            this.txtAccessTestCount1 = new System.Windows.Forms.NumericUpDown();
            this.label28 = new System.Windows.Forms.Label();
            this.label30 = new System.Windows.Forms.Label();
            this.txtAccessSuccessRate1 = new System.Windows.Forms.NumericUpDown();
            this.label31 = new System.Windows.Forms.Label();
            this.groupBox2.SuspendLayout();
            this.groupBox1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtSystemInSwitch)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtAvgSINR)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtAvgRSRP)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtSwitchSuccessRate)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtSwitchCount)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtFTPUpSpeed)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtFTPDownSpeed)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtVolteSuccessRate)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtVolteTestCount)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtCSFBSuccessRate)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtCSFBTestCount)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtAccessTestCount)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtAccessRate)).BeginInit();
            this.groupBox3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtSystemInSwitch1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtAntAvgSINR)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtAntAvgRSRP)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDoorUpThroughput)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDoorDownThroughput)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDoorAvgSINR)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDoorAvgRSRP)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtFTPUpSpeed1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtFTPDownSpeed1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtVolteSuccessRate1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtVolteTestCount1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtAccessTestCount1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtAccessSuccessRate1)).BeginInit();
            this.SuspendLayout();
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.label1);
            this.groupBox2.Controls.Add(this.btnFolder);
            this.groupBox2.Controls.Add(this.txtFolder);
            this.groupBox2.Location = new System.Drawing.Point(17, 47);
            this.groupBox2.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Padding = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.groupBox2.Size = new System.Drawing.Size(828, 112);
            this.groupBox2.TabIndex = 1;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "导出目录";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.ForeColor = System.Drawing.Color.Red;
            this.label1.Location = new System.Drawing.Point(91, 0);
            this.label1.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(161, 18);
            this.label1.TabIndex = 2;
            this.label1.Text = "同名文件将被覆盖!";
            // 
            // btnFolder
            // 
            this.btnFolder.Location = new System.Drawing.Point(523, 38);
            this.btnFolder.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.btnFolder.Name = "btnFolder";
            this.btnFolder.Size = new System.Drawing.Size(107, 36);
            this.btnFolder.TabIndex = 1;
            this.btnFolder.Text = "选择";
            this.btnFolder.UseVisualStyleBackColor = true;
            // 
            // txtFolder
            // 
            this.txtFolder.Location = new System.Drawing.Point(16, 41);
            this.txtFolder.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtFolder.Name = "txtFolder";
            this.txtFolder.ReadOnly = true;
            this.txtFolder.Size = new System.Drawing.Size(497, 28);
            this.txtFolder.TabIndex = 0;
            // 
            // btnCancel
            // 
            this.btnCancel.Location = new System.Drawing.Point(738, 545);
            this.btnCancel.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(107, 36);
            this.btnCancel.TabIndex = 2;
            this.btnCancel.Text = "取消";
            this.btnCancel.UseVisualStyleBackColor = true;
            // 
            // btnOK
            // 
            this.btnOK.Location = new System.Drawing.Point(618, 545);
            this.btnOK.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(107, 36);
            this.btnOK.TabIndex = 3;
            this.btnOK.Text = "确定";
            this.btnOK.UseVisualStyleBackColor = true;
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click_1);
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.txtStandard);
            this.groupBox1.Controls.Add(this.txtSystemInSwitch);
            this.groupBox1.Controls.Add(this.label27);
            this.groupBox1.Controls.Add(this.label29);
            this.groupBox1.Controls.Add(this.label22);
            this.groupBox1.Controls.Add(this.txtAvgSINR);
            this.groupBox1.Controls.Add(this.label23);
            this.groupBox1.Controls.Add(this.label24);
            this.groupBox1.Controls.Add(this.txtAvgRSRP);
            this.groupBox1.Controls.Add(this.label25);
            this.groupBox1.Controls.Add(this.label16);
            this.groupBox1.Controls.Add(this.txtSwitchSuccessRate);
            this.groupBox1.Controls.Add(this.label17);
            this.groupBox1.Controls.Add(this.label18);
            this.groupBox1.Controls.Add(this.txtSwitchCount);
            this.groupBox1.Controls.Add(this.label19);
            this.groupBox1.Controls.Add(this.label14);
            this.groupBox1.Controls.Add(this.txtFTPUpSpeed);
            this.groupBox1.Controls.Add(this.label15);
            this.groupBox1.Controls.Add(this.label12);
            this.groupBox1.Controls.Add(this.txtFTPDownSpeed);
            this.groupBox1.Controls.Add(this.label13);
            this.groupBox1.Controls.Add(this.label9);
            this.groupBox1.Controls.Add(this.txtVolteSuccessRate);
            this.groupBox1.Controls.Add(this.label11);
            this.groupBox1.Controls.Add(this.label7);
            this.groupBox1.Controls.Add(this.txtVolteTestCount);
            this.groupBox1.Controls.Add(this.label8);
            this.groupBox1.Controls.Add(this.label20);
            this.groupBox1.Controls.Add(this.txtCSFBSuccessRate);
            this.groupBox1.Controls.Add(this.label21);
            this.groupBox1.Controls.Add(this.label5);
            this.groupBox1.Controls.Add(this.txtCSFBTestCount);
            this.groupBox1.Controls.Add(this.label6);
            this.groupBox1.Controls.Add(this.label4);
            this.groupBox1.Controls.Add(this.txtAccessTestCount);
            this.groupBox1.Controls.Add(this.label2);
            this.groupBox1.Controls.Add(this.label3);
            this.groupBox1.Controls.Add(this.txtAccessRate);
            this.groupBox1.Controls.Add(this.label10);
            this.groupBox1.Location = new System.Drawing.Point(17, 169);
            this.groupBox1.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Padding = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.groupBox1.Size = new System.Drawing.Size(828, 328);
            this.groupBox1.TabIndex = 4;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "LTE室分";
            // 
            // txtStandard
            // 
            this.txtStandard.Location = new System.Drawing.Point(213, 282);
            this.txtStandard.Name = "txtStandard";
            this.txtStandard.Size = new System.Drawing.Size(113, 28);
            this.txtStandard.TabIndex = 191;
            // 
            // txtSystemInSwitch
            // 
            this.txtSystemInSwitch.Location = new System.Drawing.Point(671, 286);
            this.txtSystemInSwitch.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtSystemInSwitch.Maximum = new decimal(new int[] {
            1000000,
            0,
            0,
            0});
            this.txtSystemInSwitch.Minimum = new decimal(new int[] {
            1000000,
            0,
            0,
            -2147483648});
            this.txtSystemInSwitch.Name = "txtSystemInSwitch";
            this.txtSystemInSwitch.Size = new System.Drawing.Size(114, 28);
            this.txtSystemInSwitch.TabIndex = 190;
            this.txtSystemInSwitch.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.txtSystemInSwitch.Value = new decimal(new int[] {
            75,
            0,
            0,
            65536});
            // 
            // label27
            // 
            this.label27.AutoSize = true;
            this.label27.Location = new System.Drawing.Point(541, 291);
            this.label27.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label27.Name = "label27";
            this.label27.Size = new System.Drawing.Size(116, 18);
            this.label27.TabIndex = 189;
            this.label27.Text = "系统内切换≥";
            // 
            // label29
            // 
            this.label29.AutoSize = true;
            this.label29.Location = new System.Drawing.Point(72, 290);
            this.label29.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label29.Name = "label29";
            this.label29.Size = new System.Drawing.Size(134, 18);
            this.label29.TabIndex = 186;
            this.label29.Text = "室分泄露标准：";
            // 
            // label22
            // 
            this.label22.AutoSize = true;
            this.label22.Location = new System.Drawing.Point(791, 252);
            this.label22.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label22.Name = "label22";
            this.label22.Size = new System.Drawing.Size(26, 18);
            this.label22.TabIndex = 185;
            this.label22.Text = "dB";
            // 
            // txtAvgSINR
            // 
            this.txtAvgSINR.Location = new System.Drawing.Point(671, 247);
            this.txtAvgSINR.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtAvgSINR.Maximum = new decimal(new int[] {
            1000000,
            0,
            0,
            0});
            this.txtAvgSINR.Minimum = new decimal(new int[] {
            1000000,
            0,
            0,
            -2147483648});
            this.txtAvgSINR.Name = "txtAvgSINR";
            this.txtAvgSINR.Size = new System.Drawing.Size(114, 28);
            this.txtAvgSINR.TabIndex = 184;
            this.txtAvgSINR.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.txtAvgSINR.Value = new decimal(new int[] {
            75,
            0,
            0,
            65536});
            // 
            // label23
            // 
            this.label23.AutoSize = true;
            this.label23.Location = new System.Drawing.Point(561, 254);
            this.label23.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label23.Name = "label23";
            this.label23.Size = new System.Drawing.Size(98, 18);
            this.label23.TabIndex = 183;
            this.label23.Text = "平均SINR≥";
            // 
            // label24
            // 
            this.label24.AutoSize = true;
            this.label24.Location = new System.Drawing.Point(331, 251);
            this.label24.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label24.Name = "label24";
            this.label24.Size = new System.Drawing.Size(35, 18);
            this.label24.TabIndex = 182;
            this.label24.Text = "dBM";
            // 
            // txtAvgRSRP
            // 
            this.txtAvgRSRP.Location = new System.Drawing.Point(212, 246);
            this.txtAvgRSRP.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtAvgRSRP.Maximum = new decimal(new int[] {
            1000000,
            0,
            0,
            0});
            this.txtAvgRSRP.Minimum = new decimal(new int[] {
            1000000,
            0,
            0,
            -2147483648});
            this.txtAvgRSRP.Name = "txtAvgRSRP";
            this.txtAvgRSRP.Size = new System.Drawing.Size(114, 28);
            this.txtAvgRSRP.TabIndex = 181;
            this.txtAvgRSRP.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.txtAvgRSRP.Value = new decimal(new int[] {
            50,
            0,
            0,
            0});
            // 
            // label25
            // 
            this.label25.AutoSize = true;
            this.label25.Location = new System.Drawing.Point(106, 250);
            this.label25.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label25.Name = "label25";
            this.label25.Size = new System.Drawing.Size(98, 18);
            this.label25.TabIndex = 180;
            this.label25.Text = "平均RSRP≥";
            // 
            // label16
            // 
            this.label16.AutoSize = true;
            this.label16.Location = new System.Drawing.Point(793, 212);
            this.label16.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label16.Name = "label16";
            this.label16.Size = new System.Drawing.Size(17, 18);
            this.label16.TabIndex = 179;
            this.label16.Text = "%";
            // 
            // txtSwitchSuccessRate
            // 
            this.txtSwitchSuccessRate.Location = new System.Drawing.Point(671, 207);
            this.txtSwitchSuccessRate.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtSwitchSuccessRate.Maximum = new decimal(new int[] {
            1000000,
            0,
            0,
            0});
            this.txtSwitchSuccessRate.Minimum = new decimal(new int[] {
            1000000,
            0,
            0,
            -2147483648});
            this.txtSwitchSuccessRate.Name = "txtSwitchSuccessRate";
            this.txtSwitchSuccessRate.Size = new System.Drawing.Size(114, 28);
            this.txtSwitchSuccessRate.TabIndex = 178;
            this.txtSwitchSuccessRate.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.txtSwitchSuccessRate.Value = new decimal(new int[] {
            75,
            0,
            0,
            65536});
            // 
            // label17
            // 
            this.label17.AutoSize = true;
            this.label17.Location = new System.Drawing.Point(542, 214);
            this.label17.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label17.Name = "label17";
            this.label17.Size = new System.Drawing.Size(116, 18);
            this.label17.TabIndex = 177;
            this.label17.Text = "切换成功率≥";
            // 
            // label18
            // 
            this.label18.AutoSize = true;
            this.label18.Location = new System.Drawing.Point(332, 211);
            this.label18.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label18.Name = "label18";
            this.label18.Size = new System.Drawing.Size(26, 18);
            this.label18.TabIndex = 176;
            this.label18.Text = "次";
            // 
            // txtSwitchCount
            // 
            this.txtSwitchCount.Location = new System.Drawing.Point(212, 206);
            this.txtSwitchCount.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtSwitchCount.Maximum = new decimal(new int[] {
            1000000,
            0,
            0,
            0});
            this.txtSwitchCount.Minimum = new decimal(new int[] {
            1000000,
            0,
            0,
            -2147483648});
            this.txtSwitchCount.Name = "txtSwitchCount";
            this.txtSwitchCount.Size = new System.Drawing.Size(114, 28);
            this.txtSwitchCount.TabIndex = 175;
            this.txtSwitchCount.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.txtSwitchCount.Value = new decimal(new int[] {
            50,
            0,
            0,
            0});
            // 
            // label19
            // 
            this.label19.AutoSize = true;
            this.label19.Location = new System.Drawing.Point(107, 211);
            this.label19.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label19.Name = "label19";
            this.label19.Size = new System.Drawing.Size(98, 18);
            this.label19.TabIndex = 174;
            this.label19.Text = "切换次数≥";
            // 
            // label14
            // 
            this.label14.AutoSize = true;
            this.label14.Location = new System.Drawing.Point(793, 172);
            this.label14.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label14.Name = "label14";
            this.label14.Size = new System.Drawing.Size(17, 18);
            this.label14.TabIndex = 173;
            this.label14.Text = "M";
            // 
            // txtFTPUpSpeed
            // 
            this.txtFTPUpSpeed.Location = new System.Drawing.Point(671, 167);
            this.txtFTPUpSpeed.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtFTPUpSpeed.Maximum = new decimal(new int[] {
            1000000,
            0,
            0,
            0});
            this.txtFTPUpSpeed.Minimum = new decimal(new int[] {
            1000000,
            0,
            0,
            -2147483648});
            this.txtFTPUpSpeed.Name = "txtFTPUpSpeed";
            this.txtFTPUpSpeed.Size = new System.Drawing.Size(114, 28);
            this.txtFTPUpSpeed.TabIndex = 172;
            this.txtFTPUpSpeed.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.txtFTPUpSpeed.Value = new decimal(new int[] {
            75,
            0,
            0,
            65536});
            // 
            // label15
            // 
            this.label15.AutoSize = true;
            this.label15.Location = new System.Drawing.Point(536, 174);
            this.label15.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label15.Name = "label15";
            this.label15.Size = new System.Drawing.Size(125, 18);
            this.label15.TabIndex = 171;
            this.label15.Text = "FTP上传速率≥";
            // 
            // label12
            // 
            this.label12.AutoSize = true;
            this.label12.Location = new System.Drawing.Point(336, 170);
            this.label12.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label12.Name = "label12";
            this.label12.Size = new System.Drawing.Size(17, 18);
            this.label12.TabIndex = 170;
            this.label12.Text = "M";
            // 
            // txtFTPDownSpeed
            // 
            this.txtFTPDownSpeed.Location = new System.Drawing.Point(212, 166);
            this.txtFTPDownSpeed.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtFTPDownSpeed.Maximum = new decimal(new int[] {
            1000000,
            0,
            0,
            0});
            this.txtFTPDownSpeed.Minimum = new decimal(new int[] {
            1000000,
            0,
            0,
            -2147483648});
            this.txtFTPDownSpeed.Name = "txtFTPDownSpeed";
            this.txtFTPDownSpeed.Size = new System.Drawing.Size(114, 28);
            this.txtFTPDownSpeed.TabIndex = 169;
            this.txtFTPDownSpeed.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.txtFTPDownSpeed.Value = new decimal(new int[] {
            50,
            0,
            0,
            0});
            // 
            // label13
            // 
            this.label13.AutoSize = true;
            this.label13.Location = new System.Drawing.Point(80, 171);
            this.label13.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label13.Name = "label13";
            this.label13.Size = new System.Drawing.Size(125, 18);
            this.label13.TabIndex = 168;
            this.label13.Text = "FTP下载速率≥";
            // 
            // label9
            // 
            this.label9.AutoSize = true;
            this.label9.Location = new System.Drawing.Point(791, 129);
            this.label9.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(17, 18);
            this.label9.TabIndex = 167;
            this.label9.Text = "%";
            // 
            // txtVolteSuccessRate
            // 
            this.txtVolteSuccessRate.Location = new System.Drawing.Point(670, 124);
            this.txtVolteSuccessRate.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtVolteSuccessRate.Maximum = new decimal(new int[] {
            200,
            0,
            0,
            0});
            this.txtVolteSuccessRate.Minimum = new decimal(new int[] {
            200,
            0,
            0,
            -2147483648});
            this.txtVolteSuccessRate.Name = "txtVolteSuccessRate";
            this.txtVolteSuccessRate.Size = new System.Drawing.Size(114, 28);
            this.txtVolteSuccessRate.TabIndex = 166;
            this.txtVolteSuccessRate.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.txtVolteSuccessRate.Value = new decimal(new int[] {
            100,
            0,
            0,
            0});
            // 
            // label11
            // 
            this.label11.AutoSize = true;
            this.label11.Location = new System.Drawing.Point(537, 128);
            this.label11.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label11.Name = "label11";
            this.label11.Size = new System.Drawing.Size(125, 18);
            this.label11.TabIndex = 165;
            this.label11.Text = "VOLTE成功率≥";
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(334, 128);
            this.label7.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(26, 18);
            this.label7.TabIndex = 164;
            this.label7.Text = "次";
            // 
            // txtVolteTestCount
            // 
            this.txtVolteTestCount.Location = new System.Drawing.Point(212, 123);
            this.txtVolteTestCount.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtVolteTestCount.Maximum = new decimal(new int[] {
            10000000,
            0,
            0,
            0});
            this.txtVolteTestCount.Name = "txtVolteTestCount";
            this.txtVolteTestCount.Size = new System.Drawing.Size(114, 28);
            this.txtVolteTestCount.TabIndex = 163;
            this.txtVolteTestCount.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.txtVolteTestCount.Value = new decimal(new int[] {
            10,
            0,
            0,
            0});
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(62, 128);
            this.label8.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(143, 18);
            this.label8.TabIndex = 162;
            this.label8.Text = "VOLTE测试次数≥";
            // 
            // label20
            // 
            this.label20.AutoSize = true;
            this.label20.Location = new System.Drawing.Point(791, 82);
            this.label20.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label20.Name = "label20";
            this.label20.Size = new System.Drawing.Size(17, 18);
            this.label20.TabIndex = 161;
            this.label20.Text = "%";
            // 
            // txtCSFBSuccessRate
            // 
            this.txtCSFBSuccessRate.Location = new System.Drawing.Point(670, 79);
            this.txtCSFBSuccessRate.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtCSFBSuccessRate.Maximum = new decimal(new int[] {
            200,
            0,
            0,
            0});
            this.txtCSFBSuccessRate.Minimum = new decimal(new int[] {
            200,
            0,
            0,
            -2147483648});
            this.txtCSFBSuccessRate.Name = "txtCSFBSuccessRate";
            this.txtCSFBSuccessRate.Size = new System.Drawing.Size(114, 28);
            this.txtCSFBSuccessRate.TabIndex = 160;
            this.txtCSFBSuccessRate.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.txtCSFBSuccessRate.Value = new decimal(new int[] {
            100,
            0,
            0,
            0});
            // 
            // label21
            // 
            this.label21.AutoSize = true;
            this.label21.Location = new System.Drawing.Point(546, 85);
            this.label21.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label21.Name = "label21";
            this.label21.Size = new System.Drawing.Size(116, 18);
            this.label21.TabIndex = 159;
            this.label21.Text = "CSFB成功率≥";
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(334, 83);
            this.label5.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(26, 18);
            this.label5.TabIndex = 158;
            this.label5.Text = "次";
            // 
            // txtCSFBTestCount
            // 
            this.txtCSFBTestCount.Location = new System.Drawing.Point(212, 78);
            this.txtCSFBTestCount.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtCSFBTestCount.Maximum = new decimal(new int[] {
            10000000,
            0,
            0,
            0});
            this.txtCSFBTestCount.Name = "txtCSFBTestCount";
            this.txtCSFBTestCount.Size = new System.Drawing.Size(114, 28);
            this.txtCSFBTestCount.TabIndex = 157;
            this.txtCSFBTestCount.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.txtCSFBTestCount.Value = new decimal(new int[] {
            10,
            0,
            0,
            0});
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(71, 83);
            this.label6.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(134, 18);
            this.label6.TabIndex = 156;
            this.label6.Text = "CSFB测试次数≥";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(791, 42);
            this.label4.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(26, 18);
            this.label4.TabIndex = 136;
            this.label4.Text = "次";
            // 
            // txtAccessTestCount
            // 
            this.txtAccessTestCount.Location = new System.Drawing.Point(670, 37);
            this.txtAccessTestCount.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtAccessTestCount.Maximum = new decimal(new int[] {
            1000000,
            0,
            0,
            0});
            this.txtAccessTestCount.Name = "txtAccessTestCount";
            this.txtAccessTestCount.Size = new System.Drawing.Size(114, 28);
            this.txtAccessTestCount.TabIndex = 135;
            this.txtAccessTestCount.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.txtAccessTestCount.Value = new decimal(new int[] {
            10,
            0,
            0,
            0});
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(529, 43);
            this.label2.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(134, 18);
            this.label2.TabIndex = 134;
            this.label2.Text = "接入测试次数≥";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(335, 39);
            this.label3.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(17, 18);
            this.label3.TabIndex = 133;
            this.label3.Text = "%";
            // 
            // txtAccessRate
            // 
            this.txtAccessRate.Location = new System.Drawing.Point(213, 33);
            this.txtAccessRate.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtAccessRate.Maximum = new decimal(new int[] {
            200,
            0,
            0,
            0});
            this.txtAccessRate.Minimum = new decimal(new int[] {
            200,
            0,
            0,
            -2147483648});
            this.txtAccessRate.Name = "txtAccessRate";
            this.txtAccessRate.Size = new System.Drawing.Size(114, 28);
            this.txtAccessRate.TabIndex = 132;
            this.txtAccessRate.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.txtAccessRate.Value = new decimal(new int[] {
            100,
            0,
            0,
            0});
            // 
            // label10
            // 
            this.label10.AutoSize = true;
            this.label10.Location = new System.Drawing.Point(8, 40);
            this.label10.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(197, 18);
            this.label10.TabIndex = 131;
            this.label10.Text = "Access Success Rate≥";
            // 
            // groupBox3
            // 
            this.groupBox3.Controls.Add(this.txtStandard1);
            this.groupBox3.Controls.Add(this.label52);
            this.groupBox3.Controls.Add(this.txtSystemInSwitch1);
            this.groupBox3.Controls.Add(this.label55);
            this.groupBox3.Controls.Add(this.label48);
            this.groupBox3.Controls.Add(this.txtAntAvgSINR);
            this.groupBox3.Controls.Add(this.label49);
            this.groupBox3.Controls.Add(this.label50);
            this.groupBox3.Controls.Add(this.txtAntAvgRSRP);
            this.groupBox3.Controls.Add(this.label51);
            this.groupBox3.Controls.Add(this.label44);
            this.groupBox3.Controls.Add(this.txtDoorUpThroughput);
            this.groupBox3.Controls.Add(this.label45);
            this.groupBox3.Controls.Add(this.label46);
            this.groupBox3.Controls.Add(this.txtDoorDownThroughput);
            this.groupBox3.Controls.Add(this.label47);
            this.groupBox3.Controls.Add(this.label40);
            this.groupBox3.Controls.Add(this.txtDoorAvgSINR);
            this.groupBox3.Controls.Add(this.label41);
            this.groupBox3.Controls.Add(this.label42);
            this.groupBox3.Controls.Add(this.txtDoorAvgRSRP);
            this.groupBox3.Controls.Add(this.label43);
            this.groupBox3.Controls.Add(this.label36);
            this.groupBox3.Controls.Add(this.txtFTPUpSpeed1);
            this.groupBox3.Controls.Add(this.label37);
            this.groupBox3.Controls.Add(this.label38);
            this.groupBox3.Controls.Add(this.txtFTPDownSpeed1);
            this.groupBox3.Controls.Add(this.label39);
            this.groupBox3.Controls.Add(this.label32);
            this.groupBox3.Controls.Add(this.txtVolteSuccessRate1);
            this.groupBox3.Controls.Add(this.label33);
            this.groupBox3.Controls.Add(this.label34);
            this.groupBox3.Controls.Add(this.txtVolteTestCount1);
            this.groupBox3.Controls.Add(this.label35);
            this.groupBox3.Controls.Add(this.label26);
            this.groupBox3.Controls.Add(this.txtAccessTestCount1);
            this.groupBox3.Controls.Add(this.label28);
            this.groupBox3.Controls.Add(this.label30);
            this.groupBox3.Controls.Add(this.txtAccessSuccessRate1);
            this.groupBox3.Controls.Add(this.label31);
            this.groupBox3.Location = new System.Drawing.Point(13, 202);
            this.groupBox3.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.Padding = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.groupBox3.Size = new System.Drawing.Size(828, 324);
            this.groupBox3.TabIndex = 5;
            this.groupBox3.TabStop = false;
            this.groupBox3.Text = "LTE小站";
            // 
            // txtStandard1
            // 
            this.txtStandard1.Location = new System.Drawing.Point(673, 273);
            this.txtStandard1.Name = "txtStandard1";
            this.txtStandard1.Size = new System.Drawing.Size(113, 28);
            this.txtStandard1.TabIndex = 202;
            // 
            // label52
            // 
            this.label52.AutoSize = true;
            this.label52.Location = new System.Drawing.Point(532, 278);
            this.label52.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label52.Name = "label52";
            this.label52.Size = new System.Drawing.Size(134, 18);
            this.label52.TabIndex = 201;
            this.label52.Text = "室分泄露标准：";
            // 
            // txtSystemInSwitch1
            // 
            this.txtSystemInSwitch1.Location = new System.Drawing.Point(215, 271);
            this.txtSystemInSwitch1.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtSystemInSwitch1.Maximum = new decimal(new int[] {
            1000000,
            0,
            0,
            0});
            this.txtSystemInSwitch1.Minimum = new decimal(new int[] {
            1000000,
            0,
            0,
            -2147483648});
            this.txtSystemInSwitch1.Name = "txtSystemInSwitch1";
            this.txtSystemInSwitch1.Size = new System.Drawing.Size(114, 28);
            this.txtSystemInSwitch1.TabIndex = 199;
            this.txtSystemInSwitch1.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.txtSystemInSwitch1.Value = new decimal(new int[] {
            50,
            0,
            0,
            0});
            // 
            // label55
            // 
            this.label55.AutoSize = true;
            this.label55.Location = new System.Drawing.Point(91, 276);
            this.label55.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label55.Name = "label55";
            this.label55.Size = new System.Drawing.Size(116, 18);
            this.label55.TabIndex = 198;
            this.label55.Text = "系统内切换≥";
            // 
            // label48
            // 
            this.label48.AutoSize = true;
            this.label48.Location = new System.Drawing.Point(795, 238);
            this.label48.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label48.Name = "label48";
            this.label48.Size = new System.Drawing.Size(26, 18);
            this.label48.TabIndex = 197;
            this.label48.Text = "dB";
            // 
            // txtAntAvgSINR
            // 
            this.txtAntAvgSINR.Location = new System.Drawing.Point(674, 233);
            this.txtAntAvgSINR.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtAntAvgSINR.Maximum = new decimal(new int[] {
            1000000,
            0,
            0,
            0});
            this.txtAntAvgSINR.Minimum = new decimal(new int[] {
            1000000,
            0,
            0,
            -2147483648});
            this.txtAntAvgSINR.Name = "txtAntAvgSINR";
            this.txtAntAvgSINR.Size = new System.Drawing.Size(114, 28);
            this.txtAntAvgSINR.TabIndex = 196;
            this.txtAntAvgSINR.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.txtAntAvgSINR.Value = new decimal(new int[] {
            75,
            0,
            0,
            65536});
            // 
            // label49
            // 
            this.label49.AutoSize = true;
            this.label49.Location = new System.Drawing.Point(532, 240);
            this.label49.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label49.Name = "label49";
            this.label49.Size = new System.Drawing.Size(134, 18);
            this.label49.TabIndex = 195;
            this.label49.Text = "天线Avg SINR≥";
            // 
            // label50
            // 
            this.label50.AutoSize = true;
            this.label50.Location = new System.Drawing.Point(336, 237);
            this.label50.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label50.Name = "label50";
            this.label50.Size = new System.Drawing.Size(35, 18);
            this.label50.TabIndex = 194;
            this.label50.Text = "dBm";
            // 
            // txtAntAvgRSRP
            // 
            this.txtAntAvgRSRP.Location = new System.Drawing.Point(215, 232);
            this.txtAntAvgRSRP.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtAntAvgRSRP.Maximum = new decimal(new int[] {
            1000000,
            0,
            0,
            0});
            this.txtAntAvgRSRP.Minimum = new decimal(new int[] {
            1000000,
            0,
            0,
            -2147483648});
            this.txtAntAvgRSRP.Name = "txtAntAvgRSRP";
            this.txtAntAvgRSRP.Size = new System.Drawing.Size(114, 28);
            this.txtAntAvgRSRP.TabIndex = 193;
            this.txtAntAvgRSRP.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.txtAntAvgRSRP.Value = new decimal(new int[] {
            50,
            0,
            0,
            0});
            // 
            // label51
            // 
            this.label51.AutoSize = true;
            this.label51.Location = new System.Drawing.Point(74, 237);
            this.label51.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label51.Name = "label51";
            this.label51.Size = new System.Drawing.Size(134, 18);
            this.label51.TabIndex = 192;
            this.label51.Text = "天线Avg RSRP≥";
            // 
            // label44
            // 
            this.label44.AutoSize = true;
            this.label44.Location = new System.Drawing.Point(794, 196);
            this.label44.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label44.Name = "label44";
            this.label44.Size = new System.Drawing.Size(17, 18);
            this.label44.TabIndex = 191;
            this.label44.Text = "M";
            // 
            // txtDoorUpThroughput
            // 
            this.txtDoorUpThroughput.Location = new System.Drawing.Point(672, 191);
            this.txtDoorUpThroughput.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtDoorUpThroughput.Maximum = new decimal(new int[] {
            1000000,
            0,
            0,
            0});
            this.txtDoorUpThroughput.Minimum = new decimal(new int[] {
            1000000,
            0,
            0,
            -2147483648});
            this.txtDoorUpThroughput.Name = "txtDoorUpThroughput";
            this.txtDoorUpThroughput.Size = new System.Drawing.Size(114, 28);
            this.txtDoorUpThroughput.TabIndex = 190;
            this.txtDoorUpThroughput.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.txtDoorUpThroughput.Value = new decimal(new int[] {
            75,
            0,
            0,
            65536});
            // 
            // label45
            // 
            this.label45.AutoSize = true;
            this.label45.Location = new System.Drawing.Point(513, 198);
            this.label45.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label45.Name = "label45";
            this.label45.Size = new System.Drawing.Size(152, 18);
            this.label45.TabIndex = 189;
            this.label45.Text = "室内上行吞吐率≥";
            // 
            // label46
            // 
            this.label46.AutoSize = true;
            this.label46.Location = new System.Drawing.Point(337, 194);
            this.label46.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label46.Name = "label46";
            this.label46.Size = new System.Drawing.Size(17, 18);
            this.label46.TabIndex = 188;
            this.label46.Text = "M";
            // 
            // txtDoorDownThroughput
            // 
            this.txtDoorDownThroughput.Location = new System.Drawing.Point(213, 190);
            this.txtDoorDownThroughput.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtDoorDownThroughput.Maximum = new decimal(new int[] {
            1000000,
            0,
            0,
            0});
            this.txtDoorDownThroughput.Minimum = new decimal(new int[] {
            1000000,
            0,
            0,
            -2147483648});
            this.txtDoorDownThroughput.Name = "txtDoorDownThroughput";
            this.txtDoorDownThroughput.Size = new System.Drawing.Size(114, 28);
            this.txtDoorDownThroughput.TabIndex = 187;
            this.txtDoorDownThroughput.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.txtDoorDownThroughput.Value = new decimal(new int[] {
            50,
            0,
            0,
            0});
            // 
            // label47
            // 
            this.label47.AutoSize = true;
            this.label47.Location = new System.Drawing.Point(54, 195);
            this.label47.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label47.Name = "label47";
            this.label47.Size = new System.Drawing.Size(152, 18);
            this.label47.TabIndex = 186;
            this.label47.Text = "室内下行吞吐率≥";
            // 
            // label40
            // 
            this.label40.AutoSize = true;
            this.label40.Location = new System.Drawing.Point(794, 158);
            this.label40.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label40.Name = "label40";
            this.label40.Size = new System.Drawing.Size(17, 18);
            this.label40.TabIndex = 185;
            this.label40.Text = "%";
            // 
            // txtDoorAvgSINR
            // 
            this.txtDoorAvgSINR.Location = new System.Drawing.Point(672, 153);
            this.txtDoorAvgSINR.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtDoorAvgSINR.Maximum = new decimal(new int[] {
            1000000,
            0,
            0,
            0});
            this.txtDoorAvgSINR.Minimum = new decimal(new int[] {
            1000000,
            0,
            0,
            -2147483648});
            this.txtDoorAvgSINR.Name = "txtDoorAvgSINR";
            this.txtDoorAvgSINR.Size = new System.Drawing.Size(114, 28);
            this.txtDoorAvgSINR.TabIndex = 184;
            this.txtDoorAvgSINR.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.txtDoorAvgSINR.Value = new decimal(new int[] {
            75,
            0,
            0,
            65536});
            // 
            // label41
            // 
            this.label41.AutoSize = true;
            this.label41.Location = new System.Drawing.Point(530, 158);
            this.label41.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label41.Name = "label41";
            this.label41.Size = new System.Drawing.Size(134, 18);
            this.label41.TabIndex = 183;
            this.label41.Text = "室内Avg SINR≥";
            // 
            // label42
            // 
            this.label42.AutoSize = true;
            this.label42.Location = new System.Drawing.Point(333, 157);
            this.label42.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label42.Name = "label42";
            this.label42.Size = new System.Drawing.Size(26, 18);
            this.label42.TabIndex = 182;
            this.label42.Text = "次";
            // 
            // txtDoorAvgRSRP
            // 
            this.txtDoorAvgRSRP.Location = new System.Drawing.Point(213, 152);
            this.txtDoorAvgRSRP.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtDoorAvgRSRP.Maximum = new decimal(new int[] {
            1000000,
            0,
            0,
            0});
            this.txtDoorAvgRSRP.Minimum = new decimal(new int[] {
            1000000,
            0,
            0,
            -2147483648});
            this.txtDoorAvgRSRP.Name = "txtDoorAvgRSRP";
            this.txtDoorAvgRSRP.Size = new System.Drawing.Size(114, 28);
            this.txtDoorAvgRSRP.TabIndex = 181;
            this.txtDoorAvgRSRP.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.txtDoorAvgRSRP.Value = new decimal(new int[] {
            50,
            0,
            0,
            0});
            // 
            // label43
            // 
            this.label43.AutoSize = true;
            this.label43.Location = new System.Drawing.Point(72, 157);
            this.label43.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label43.Name = "label43";
            this.label43.Size = new System.Drawing.Size(134, 18);
            this.label43.TabIndex = 180;
            this.label43.Text = "室内Avg RSRP≥";
            // 
            // label36
            // 
            this.label36.AutoSize = true;
            this.label36.Location = new System.Drawing.Point(794, 120);
            this.label36.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label36.Name = "label36";
            this.label36.Size = new System.Drawing.Size(17, 18);
            this.label36.TabIndex = 179;
            this.label36.Text = "M";
            // 
            // txtFTPUpSpeed1
            // 
            this.txtFTPUpSpeed1.Location = new System.Drawing.Point(672, 115);
            this.txtFTPUpSpeed1.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtFTPUpSpeed1.Maximum = new decimal(new int[] {
            1000000,
            0,
            0,
            0});
            this.txtFTPUpSpeed1.Minimum = new decimal(new int[] {
            1000000,
            0,
            0,
            -2147483648});
            this.txtFTPUpSpeed1.Name = "txtFTPUpSpeed1";
            this.txtFTPUpSpeed1.Size = new System.Drawing.Size(114, 28);
            this.txtFTPUpSpeed1.TabIndex = 178;
            this.txtFTPUpSpeed1.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.txtFTPUpSpeed1.Value = new decimal(new int[] {
            75,
            0,
            0,
            65536});
            // 
            // label37
            // 
            this.label37.AutoSize = true;
            this.label37.Location = new System.Drawing.Point(537, 122);
            this.label37.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label37.Name = "label37";
            this.label37.Size = new System.Drawing.Size(125, 18);
            this.label37.TabIndex = 177;
            this.label37.Text = "FTP上传速率≥";
            // 
            // label38
            // 
            this.label38.AutoSize = true;
            this.label38.Location = new System.Drawing.Point(337, 118);
            this.label38.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label38.Name = "label38";
            this.label38.Size = new System.Drawing.Size(17, 18);
            this.label38.TabIndex = 176;
            this.label38.Text = "M";
            // 
            // txtFTPDownSpeed1
            // 
            this.txtFTPDownSpeed1.Location = new System.Drawing.Point(213, 114);
            this.txtFTPDownSpeed1.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtFTPDownSpeed1.Maximum = new decimal(new int[] {
            1000000,
            0,
            0,
            0});
            this.txtFTPDownSpeed1.Minimum = new decimal(new int[] {
            1000000,
            0,
            0,
            -2147483648});
            this.txtFTPDownSpeed1.Name = "txtFTPDownSpeed1";
            this.txtFTPDownSpeed1.Size = new System.Drawing.Size(114, 28);
            this.txtFTPDownSpeed1.TabIndex = 175;
            this.txtFTPDownSpeed1.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.txtFTPDownSpeed1.Value = new decimal(new int[] {
            50,
            0,
            0,
            0});
            // 
            // label39
            // 
            this.label39.AutoSize = true;
            this.label39.Location = new System.Drawing.Point(81, 119);
            this.label39.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label39.Name = "label39";
            this.label39.Size = new System.Drawing.Size(125, 18);
            this.label39.TabIndex = 174;
            this.label39.Text = "FTP下载速率≥";
            // 
            // label32
            // 
            this.label32.AutoSize = true;
            this.label32.Location = new System.Drawing.Point(792, 81);
            this.label32.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label32.Name = "label32";
            this.label32.Size = new System.Drawing.Size(17, 18);
            this.label32.TabIndex = 173;
            this.label32.Text = "%";
            // 
            // txtVolteSuccessRate1
            // 
            this.txtVolteSuccessRate1.Location = new System.Drawing.Point(671, 76);
            this.txtVolteSuccessRate1.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtVolteSuccessRate1.Maximum = new decimal(new int[] {
            200,
            0,
            0,
            0});
            this.txtVolteSuccessRate1.Minimum = new decimal(new int[] {
            200,
            0,
            0,
            -2147483648});
            this.txtVolteSuccessRate1.Name = "txtVolteSuccessRate1";
            this.txtVolteSuccessRate1.Size = new System.Drawing.Size(114, 28);
            this.txtVolteSuccessRate1.TabIndex = 172;
            this.txtVolteSuccessRate1.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.txtVolteSuccessRate1.Value = new decimal(new int[] {
            100,
            0,
            0,
            0});
            // 
            // label33
            // 
            this.label33.AutoSize = true;
            this.label33.Location = new System.Drawing.Point(538, 80);
            this.label33.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label33.Name = "label33";
            this.label33.Size = new System.Drawing.Size(125, 18);
            this.label33.TabIndex = 171;
            this.label33.Text = "VOLTE成功率≥";
            // 
            // label34
            // 
            this.label34.AutoSize = true;
            this.label34.Location = new System.Drawing.Point(335, 80);
            this.label34.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label34.Name = "label34";
            this.label34.Size = new System.Drawing.Size(26, 18);
            this.label34.TabIndex = 170;
            this.label34.Text = "次";
            // 
            // txtVolteTestCount1
            // 
            this.txtVolteTestCount1.Location = new System.Drawing.Point(213, 75);
            this.txtVolteTestCount1.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtVolteTestCount1.Maximum = new decimal(new int[] {
            10000000,
            0,
            0,
            0});
            this.txtVolteTestCount1.Name = "txtVolteTestCount1";
            this.txtVolteTestCount1.Size = new System.Drawing.Size(114, 28);
            this.txtVolteTestCount1.TabIndex = 169;
            this.txtVolteTestCount1.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.txtVolteTestCount1.Value = new decimal(new int[] {
            10,
            0,
            0,
            0});
            // 
            // label35
            // 
            this.label35.AutoSize = true;
            this.label35.Location = new System.Drawing.Point(63, 80);
            this.label35.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label35.Name = "label35";
            this.label35.Size = new System.Drawing.Size(143, 18);
            this.label35.TabIndex = 168;
            this.label35.Text = "VOLTE测试次数≥";
            // 
            // label26
            // 
            this.label26.AutoSize = true;
            this.label26.Location = new System.Drawing.Point(794, 45);
            this.label26.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label26.Name = "label26";
            this.label26.Size = new System.Drawing.Size(26, 18);
            this.label26.TabIndex = 142;
            this.label26.Text = "次";
            // 
            // txtAccessTestCount1
            // 
            this.txtAccessTestCount1.Location = new System.Drawing.Point(673, 40);
            this.txtAccessTestCount1.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtAccessTestCount1.Maximum = new decimal(new int[] {
            1000000,
            0,
            0,
            0});
            this.txtAccessTestCount1.Name = "txtAccessTestCount1";
            this.txtAccessTestCount1.Size = new System.Drawing.Size(114, 28);
            this.txtAccessTestCount1.TabIndex = 141;
            this.txtAccessTestCount1.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.txtAccessTestCount1.Value = new decimal(new int[] {
            10,
            0,
            0,
            0});
            // 
            // label28
            // 
            this.label28.AutoSize = true;
            this.label28.Location = new System.Drawing.Point(532, 46);
            this.label28.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label28.Name = "label28";
            this.label28.Size = new System.Drawing.Size(134, 18);
            this.label28.TabIndex = 140;
            this.label28.Text = "接入测试次数≥";
            // 
            // label30
            // 
            this.label30.AutoSize = true;
            this.label30.Location = new System.Drawing.Point(338, 42);
            this.label30.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label30.Name = "label30";
            this.label30.Size = new System.Drawing.Size(17, 18);
            this.label30.TabIndex = 139;
            this.label30.Text = "%";
            // 
            // txtAccessSuccessRate1
            // 
            this.txtAccessSuccessRate1.Location = new System.Drawing.Point(216, 36);
            this.txtAccessSuccessRate1.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtAccessSuccessRate1.Maximum = new decimal(new int[] {
            200,
            0,
            0,
            0});
            this.txtAccessSuccessRate1.Minimum = new decimal(new int[] {
            200,
            0,
            0,
            -2147483648});
            this.txtAccessSuccessRate1.Name = "txtAccessSuccessRate1";
            this.txtAccessSuccessRate1.Size = new System.Drawing.Size(114, 28);
            this.txtAccessSuccessRate1.TabIndex = 138;
            this.txtAccessSuccessRate1.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.txtAccessSuccessRate1.Value = new decimal(new int[] {
            100,
            0,
            0,
            0});
            // 
            // label31
            // 
            this.label31.AutoSize = true;
            this.label31.Location = new System.Drawing.Point(11, 43);
            this.label31.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label31.Name = "label31";
            this.label31.Size = new System.Drawing.Size(197, 18);
            this.label31.TabIndex = 137;
            this.label31.Text = "Access Success Rate≥";
            // 
            // LteTestSettingForm
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(10F, 22F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(867, 599);
            this.Controls.Add(this.groupBox3);
            this.Controls.Add(this.groupBox1);
            this.Controls.Add(this.btnOK);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.groupBox2);
            this.Margin = new System.Windows.Forms.Padding(10, 14, 10, 14);
            this.Name = "LteTestSettingForm";
            this.Text = "LTE室分验收";
            this.Load += new System.EventHandler(this.LteTestSettingForm_Load);
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtSystemInSwitch)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtAvgSINR)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtAvgRSRP)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtSwitchSuccessRate)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtSwitchCount)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtFTPUpSpeed)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtFTPDownSpeed)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtVolteSuccessRate)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtVolteTestCount)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtCSFBSuccessRate)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtCSFBTestCount)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtAccessTestCount)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtAccessRate)).EndInit();
            this.groupBox3.ResumeLayout(false);
            this.groupBox3.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtSystemInSwitch1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtAntAvgSINR)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtAntAvgRSRP)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDoorUpThroughput)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDoorDownThroughput)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDoorAvgSINR)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDoorAvgRSRP)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtFTPUpSpeed1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtFTPDownSpeed1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtVolteSuccessRate1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtVolteTestCount1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtAccessTestCount1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtAccessSuccessRate1)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.Button btnFolder;
        private System.Windows.Forms.TextBox txtFolder;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.Button btnOK;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.GroupBox groupBox3;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.NumericUpDown txtAccessTestCount;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.NumericUpDown txtAccessRate;
        private System.Windows.Forms.Label label10;
        private System.Windows.Forms.Label label20;
        private System.Windows.Forms.NumericUpDown txtCSFBSuccessRate;
        private System.Windows.Forms.Label label21;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.NumericUpDown txtCSFBTestCount;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.Label label9;
        private System.Windows.Forms.NumericUpDown txtVolteSuccessRate;
        private System.Windows.Forms.Label label11;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.NumericUpDown txtVolteTestCount;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.Label label14;
        private System.Windows.Forms.NumericUpDown txtFTPUpSpeed;
        private System.Windows.Forms.Label label15;
        private System.Windows.Forms.Label label12;
        private System.Windows.Forms.NumericUpDown txtFTPDownSpeed;
        private System.Windows.Forms.Label label13;
        private System.Windows.Forms.NumericUpDown txtSystemInSwitch;
        private System.Windows.Forms.Label label27;
        private System.Windows.Forms.Label label29;
        private System.Windows.Forms.Label label22;
        private System.Windows.Forms.NumericUpDown txtAvgSINR;
        private System.Windows.Forms.Label label23;
        private System.Windows.Forms.Label label24;
        private System.Windows.Forms.NumericUpDown txtAvgRSRP;
        private System.Windows.Forms.Label label25;
        private System.Windows.Forms.Label label16;
        private System.Windows.Forms.NumericUpDown txtSwitchSuccessRate;
        private System.Windows.Forms.Label label17;
        private System.Windows.Forms.Label label18;
        private System.Windows.Forms.NumericUpDown txtSwitchCount;
        private System.Windows.Forms.Label label19;
        private System.Windows.Forms.TextBox txtStandard;
        private System.Windows.Forms.Label label40;
        private System.Windows.Forms.NumericUpDown txtDoorAvgSINR;
        private System.Windows.Forms.Label label41;
        private System.Windows.Forms.Label label42;
        private System.Windows.Forms.NumericUpDown txtDoorAvgRSRP;
        private System.Windows.Forms.Label label43;
        private System.Windows.Forms.Label label36;
        private System.Windows.Forms.NumericUpDown txtFTPUpSpeed1;
        private System.Windows.Forms.Label label37;
        private System.Windows.Forms.Label label38;
        private System.Windows.Forms.NumericUpDown txtFTPDownSpeed1;
        private System.Windows.Forms.Label label39;
        private System.Windows.Forms.Label label32;
        private System.Windows.Forms.NumericUpDown txtVolteSuccessRate1;
        private System.Windows.Forms.Label label33;
        private System.Windows.Forms.Label label34;
        private System.Windows.Forms.NumericUpDown txtVolteTestCount1;
        private System.Windows.Forms.Label label35;
        private System.Windows.Forms.Label label26;
        private System.Windows.Forms.NumericUpDown txtAccessTestCount1;
        private System.Windows.Forms.Label label28;
        private System.Windows.Forms.Label label30;
        private System.Windows.Forms.NumericUpDown txtAccessSuccessRate1;
        private System.Windows.Forms.Label label31;
        private System.Windows.Forms.NumericUpDown txtSystemInSwitch1;
        private System.Windows.Forms.Label label55;
        private System.Windows.Forms.Label label48;
        private System.Windows.Forms.NumericUpDown txtAntAvgSINR;
        private System.Windows.Forms.Label label49;
        private System.Windows.Forms.Label label50;
        private System.Windows.Forms.NumericUpDown txtAntAvgRSRP;
        private System.Windows.Forms.Label label51;
        private System.Windows.Forms.Label label44;
        private System.Windows.Forms.NumericUpDown txtDoorUpThroughput;
        private System.Windows.Forms.Label label45;
        private System.Windows.Forms.Label label46;
        private System.Windows.Forms.NumericUpDown txtDoorDownThroughput;
        private System.Windows.Forms.Label label47;
        private System.Windows.Forms.TextBox txtStandard1;
        private System.Windows.Forms.Label label52;
    }
}