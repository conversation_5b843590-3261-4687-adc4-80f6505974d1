﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.ZTFunc.GridOrderCommon
{
    public class QueryGridOrderToken : DIYSQLBase
    {
        public QueryGridOrderToken()
        {
            MainDB = true;
        }

        protected override string getSqlTextString()
        {
            return string.Format(@"SELECT [setTypeID],[setTypeName],[suffixName] FROM {0}.[dbo].[tb_cfg_grid_set_type];", QueryGridOrder.DBCatName);
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] arr = new E_VType[3];
            arr[0] = E_VType.E_Int;
            arr[1] = E_VType.E_String;
            arr[2] = E_VType.E_String;
            return arr;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            TokenSet = new List<GridOrderToken>();
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    GridOrderToken token = new GridOrderToken();
                    token.ID = package.Content.GetParamInt();
                    token.Name = package.Content.GetParamString();
                    token.Token = package.Content.GetParamString();
                    TokenSet.Add(token);
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_SUCCESS)
                {
                    //
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_FAIL)
                {
                    //
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }

        public List<GridOrderToken> TokenSet { get; set; }
    }
}
